remotecfg {
	url            = "https://fleet-management-prod-014.grafana.net"
	id             = constants.hostname
	poll_frequency = "60s"

	basic_auth {
		username = "1227797"
		password = sys.env("GCLOUD_RW_API_KEY")
	}
}

prometheus.remote_write "metrics_service" {
	endpoint {
		url = "https://prometheus-prod-36-prod-us-west-0.grafana.net/api/prom/push"

		basic_auth {
			username = "2379833"
			password = sys.env("GCLOUD_RW_API_KEY")
		}
	}

	external_labels = {
		environment   = "production",
		tenant_id     = "clararx",
		service_group = "k8s-pg",
		instance      = constants.hostname,
	}
}

loki.write "grafana_cloud_loki" {
	endpoint {
		url = "https://logs-prod-021.grafana.net/loki/api/v1/push"

		basic_auth {
			username = "1185594"
			password = sys.env("GCLOUD_RW_API_KEY")
		}
	}

	external_labels = {
		environment   = "production",
		tenant_id     = "clararx",
		service_group = "k8s-pg",
		instance      = constants.hostname,
	}
}