remotecfg {
	url            = "https://fleet-management-prod-014.grafana.net"
	id             = constants.hostname
	poll_frequency = "60s"

	basic_auth {
		username = "1227797"
		password = sys.env("GCLOUD_RW_API_KEY")
	}

    attributes     = {
		environment   = "production",
		tenant_id     = "clararx",
		service_group = "admin",
		instance      = constants.hostname,
	}
}



prometheus.remote_write "metrics_service" {
	endpoint {
		url = "https://prometheus-prod-36-prod-us-west-0.grafana.net/api/prom/push"

		basic_auth {
			username = "2379833"
			password = sys.env("GCLOUD_RW_API_KEY")
		}
	}

	external_labels = {
		environment   = "production",
		tenant_id     = "clararx",
		service_group = "admin",
		instance      = constants.hostname,
	}
}

loki.write "grafana_cloud_loki" {
	endpoint {
		url = "https://logs-prod-021.grafana.net/loki/api/v1/push"

		basic_auth {
			username = "1185594"
			password = sys.env("GCLOUD_RW_API_KEY")
		}
	}

	external_labels = {
		environment   = "production",
		tenant_id     = "clararx",
		service_group = "admin",
		instance      = constants.hostname,
	}
}

local.file_match "logs_docker_compose" {
	path_targets = [{
		__address__ = "localhost",
		__path__    = "/var/lib/docker/containers/*/*.log",
		job         = "docker",
	}]
}

loki.source.file "logs_docker_compose" {
	targets    = local.file_match.logs_docker_compose.targets
	forward_to = [loki.write.grafana_cloud_loki.receiver]
}