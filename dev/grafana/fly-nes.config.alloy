remotecfg {
	url            = "https://fleet-management-prod-014.grafana.net"
	id             = sys.env("FLY_ID")
	poll_frequency = "60s"

	basic_auth {
		username = "1227797"
		password = sys.env("GCLOUD_RW_API_KEY")
	}
}

prometheus.remote_write "metrics_service" {
	endpoint {
		url = "https://prometheus-prod-36-prod-us-west-0.grafana.net/api/prom/push"

		basic_auth {
			username = "2379833"
			password = sys.env("GCLOUD_RW_API_KEY")
		}
	}

	external_labels = {
		environment   = coalesce(sys.env("NODE_ENV"), "development"),
		tenant_id     = string.trim_prefix(string.trim_prefix(string.trim_prefix(string.trim_prefix(string.trim_suffix(sys.env("FLY_ID"), "-nes"), "d-"), "t-"), "s-"), "p-"),
		service_group = "fly-nes",
		instance      = constants.hostname,
	}
}

loki.write "grafana_cloud_loki" {
	endpoint {
		url = "https://logs-prod-021.grafana.net/loki/api/v1/push"

		basic_auth {
			username = "1185594"
			service_group = "fly-nes",
			password = sys.env("GCLOUD_RW_API_KEY")
		}
	}

	external_labels = {
		environment   = coalesce(sys.env("NODE_ENV"), "development"),
		tenant_id     = string.trim_prefix(string.trim_prefix(string.trim_prefix(string.trim_prefix(string.trim_suffix(sys.env("FLY_ID"), "-nes"), "d-"), "t-"), "s-"), "p-"),
		service_group = "fly-nes",
		instance      = constants.hostname,
	}
}