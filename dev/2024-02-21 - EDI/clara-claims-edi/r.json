{"meta": {"senderId": "APIM_Marketplace_SBX_Native_Uhq5tOmCZhfDf3Du", "applicationMode": "sandbox", "traceId": "e690c288-8ce2-0773-214b-8ff043b23728"}, "controlNumber": "376672775", "reassociationKey": "376672775", "tradingPartnerServiceId": "CMSMED", "provider": {"providerName": "HAPPY DOCTORS GROUP PRACTICE", "entityIdentifier": "Provider", "entityType": "Non-Person Entity", "npi": "**********"}, "subscriber": {"memberId": "**********", "firstName": "JOHNONE", "lastName": "DOEONE", "middleName": "M", "gender": "F", "entityIdentifier": "Insured or Subscriber", "entityType": "Person", "dateOfBirth": "18800102", "relationToSubscriber": "Self", "insuredIndicator": "Y", "maintenanceTypeCode": "001", "maintenanceReasonCode": "25", "address": {"address1": "123 address1", "city": "SEATTLE", "state": "WA", "postalCode": "*********"}}, "subscriberTraceNumbers": [{"traceTypeCode": "2", "traceType": "Referenced Transaction Trace Numbers", "referenceIdentification": "*********", "originatingCompanyIdentifier": "**********"}, {"traceTypeCode": "2", "traceType": "Referenced Transaction Trace Numbers", "referenceIdentification": "*********", "originatingCompanyIdentifier": "**********"}], "payer": {"entityIdentifier": "Payer", "entityType": "Non-Person Entity", "name": "CMS", "payorIdentification": "CMSMED"}, "planDateInformation": {"eligibility": "20211020-20211020"}, "planStatus": [{"statusCode": "1", "status": "Active Coverage", "serviceTypeCodes": ["88", "30", "42", "45", "48", "49", "69", "76", "83", "A5", "A7", "AG", "BT", "BU", "BV", "30", "2", "23", "24", "25", "26", "27", "28", "3", "33", "36", "37", "38", "39", "4", "40", "42", "50", "51", "52", "53", "62", "67", "69", "73", "76", "83", "86", "98", "A4", "A6", "A8", "AI", "AJ", "AK", "AL", "BT", "BU", "BV", "DM", "UC"]}], "benefitsInformation": [{"code": "I", "name": "Non-Covered", "serviceTypeCodes": ["41", "54"], "serviceTypes": ["<PERSON><PERSON><PERSON> (Preventive) Dental", "Long Term Care"]}, {"code": "1", "name": "Active Coverage", "serviceTypeCodes": ["88"], "serviceTypes": ["Pharmacy"]}, {"code": "1", "name": "Active Coverage", "serviceTypeCodes": ["30", "42", "45", "48", "49", "69", "76", "83", "A5", "A7", "AG", "BT", "BU", "BV"], "serviceTypes": ["Health Benefit Plan Coverage", "Home Health Care", "Hospice", "Hospital - Inpatient", "Hospital - Room and Board", "Maternity", "Dialysis", "Infertility", "Psychiatric - Room and Board", "Psychiatric - Inpatient", "Skilled Nursing Care", "Gynecological", "Obstetrical", "Obstetrical/Gynecological"], "insuranceTypeCode": "MA", "insuranceType": "Medicare Part A", "benefitsDateInformation": {"plan": "20041101"}, "additionalInformation": [{"description": "0-Beneficiary insured due to age OASI"}]}, {"code": "C", "name": "Deductible", "serviceTypeCodes": ["30"], "serviceTypes": ["Health Benefit Plan Coverage"], "insuranceTypeCode": "MA", "insuranceType": "Medicare Part A", "timeQualifierCode": "26", "timeQualifier": "Episode", "benefitAmount": "1484", "benefitsDateInformation": {"plan": "20210101-20211231"}}, {"code": "C", "name": "Deductible", "serviceTypeCodes": ["30"], "serviceTypes": ["Health Benefit Plan Coverage"], "insuranceTypeCode": "MA", "insuranceType": "Medicare Part A", "timeQualifierCode": "29", "timeQualifier": "Remaining", "benefitAmount": "1484", "benefitsDateInformation": {"plan": "20210101-20211231"}}, {"code": "C", "name": "Deductible", "serviceTypeCodes": ["42", "45"], "serviceTypes": ["Home Health Care", "Hospice"], "insuranceTypeCode": "MA", "insuranceType": "Medicare Part A", "timeQualifierCode": "26", "timeQualifier": "Episode", "benefitAmount": "0", "benefitsDateInformation": {"benefit": "20210101-20211231"}}, {"code": "1", "name": "Active Coverage", "serviceTypeCodes": ["30", "2", "23", "24", "25", "26", "27", "28", "3", "33", "36", "37", "38", "39", "4", "40", "42", "50", "51", "52", "53", "62", "67", "69", "73", "76", "83", "86", "98", "A4", "A6", "A8", "AI", "AJ", "AK", "AL", "BT", "BU", "BV", "DM", "UC"], "serviceTypes": ["Health Benefit Plan Coverage", "Surgical", "Diagnostic Dental", "Periodontics", "Restorative", "Endodontics", "Maxillofacial Prosthetics", "Adjunctive Dental Services", "Consultation", "Chiropractic", "Dental Crowns", "Dental Accident", "Orthodontics", "Prosthodontics", "Diagnostic X-Ray", "Oral Surgery", "Home Health Care", "Hospital - Outpatient", "Hospital - Emergency Accident", "Hospital - Emergency Medical", "Hospital - Ambulatory Surgical", "MRI/CAT Scan", "Smoking Cessation", "Maternity", "Diagnostic Medical", "Dialysis", "Infertility", "Emergency Services", "Professional (Physician) Visit - Office", "Psychiatric", "Psychotherapy", "Psychiatric - Outpatient", "Substance Abuse", "Alcoholism", "Drug Addiction", "Vision (Optometry)", "Gynecological", "Obstetrical", "Obstetrical/Gynecological", "Durable Medical Equipment", "Urgent Care"], "insuranceTypeCode": "MB", "insuranceType": "Medicare Part B", "benefitsDateInformation": {"plan": "20041101"}, "additionalInformation": [{"description": "0-Beneficiary insured due to age OASI"}]}, {"code": "C", "name": "Deductible", "serviceTypeCodes": ["30"], "serviceTypes": ["Health Benefit Plan Coverage"], "insuranceTypeCode": "MB", "insuranceType": "Medicare Part B", "timeQualifierCode": "23", "timeQualifier": "Calendar Year", "benefitAmount": "203", "benefitsDateInformation": {"plan": "20210101-20211231"}}, {"code": "C", "name": "Deductible", "serviceTypeCodes": ["30"], "serviceTypes": ["Health Benefit Plan Coverage"], "insuranceTypeCode": "MB", "insuranceType": "Medicare Part B", "timeQualifierCode": "29", "timeQualifier": "Remaining", "benefitAmount": "0", "benefitsDateInformation": {"plan": "20210101-20211231"}}, {"code": "A", "name": "Co-Insurance", "serviceTypeCodes": ["30"], "serviceTypes": ["Health Benefit Plan Coverage"], "insuranceTypeCode": "MB", "insuranceType": "Medicare Part B", "timeQualifierCode": "27", "timeQualifier": "Visit", "benefitPercent": "0.2", "benefitsDateInformation": {"plan": "20210101-20211231"}}, {"code": "C", "name": "Deductible", "serviceTypeCodes": ["42", "67", "AJ"], "serviceTypes": ["Home Health Care", "Smoking Cessation", "Alcoholism"], "insuranceTypeCode": "MB", "insuranceType": "Medicare Part B", "timeQualifierCode": "23", "timeQualifier": "Calendar Year", "benefitAmount": "0", "benefitsDateInformation": {"benefit": "20210101-20211231"}}, {"code": "A", "name": "Co-Insurance", "serviceTypeCodes": ["42", "67", "AJ"], "serviceTypes": ["Home Health Care", "Smoking Cessation", "Alcoholism"], "insuranceTypeCode": "MB", "insuranceType": "Medicare Part B", "timeQualifierCode": "27", "timeQualifier": "Visit", "benefitPercent": "0", "benefitsDateInformation": {"benefit": "20210101-20211231"}}, {"code": "R", "name": "Other or Additional Payor", "serviceTypeCodes": ["88"], "serviceTypes": ["Pharmacy"], "insuranceTypeCode": "OT", "insuranceType": "Other", "headerLoopIdentifierCode": "2120", "trailerLoopIdentifierCode": "2120", "benefitsAdditionalInformation": {"planNumber": "S5601", "planNetworkIdNumber": "203"}, "benefitsDateInformation": {"benefit": "20210101"}, "benefitsRelatedEntity": {"entityIdentifier": "Payer", "entityType": "Non-Person Entity", "entityName": "extra healthy insurance", "address": {"address1": "123 address1", "city": "Nashville", "state": "TN", "postalCode": "37203"}, "contactInformation": {"contacts": [{"communicationMode": "Telephone", "communicationNumber": "**********"}, {"communicationMode": "Uniform Resource Locator (URL)", "communicationNumber": "www.testwebsite.com"}]}}, "benefitsRelatedEntities": [{"entityIdentifier": "Payer", "entityType": "Non-Person Entity", "entityName": "extra healthy insurance", "address": {"address1": "123 address1"}, "contactInformation": {"contacts": [{"communicationMode": "Telephone", "communicationNumber": "**********"}, {"communicationMode": "Uniform Resource Locator (URL)", "communicationNumber": "www.testwebsite.com"}]}}]}], "x12": "ISA*00*          *01*SomePwd   *ZZ*EMDEON         *ZZ*TPG00000       *240219*1042*^*00501*376672775*0*T*:~GS*HB*MTEXE*LLX1210001*20131015*2219*376672775*X*005010X279A1~ST*271*0001*005010X279A1~BHT*0022*11*e690c288-8ce2-0773-214b-8ff043b23728*20240219*1042~HL*1**20*1~NM1*PR*2*CMS*****PI*CMSMED~HL*2*1*21*1~NM1*1P*2*HAPPY DOCTORS GROUP PRACTICE*****XX***********~HL*3*2*22*0~TRN*2*********************~TRN*2************9MCK-TSHRT~NM1*IL*1*DOEONE*JOHNONE*M***MI***********~N3*123 address1~N4*SEATTLE*WA**********~DMG*D8*18800102*F~INS*Y*18*001*25~DTP*307*RD8*20211020-20211020~EB*I**41^54~EB*1**88~EB*1**30^42^45^48^49^69^76^83^A5^A7^AG^BT^BU^BV*MA~DTP*291*D8*20041101~MSG*0-Beneficiary insured due to age OASI~EB*C**30*MA**26*1484~DTP*291*RD8*20210101-20211231~EB*C**30*MA**29*1484~DTP*291*RD8*20210101-20211231~EB*C**42^45*MA**26*0~DTP*292*RD8*20210101-20211231~EB*1**30^2^23^24^25^26^27^28^3^33^36^37^38^39^4^40^42^50^51^52^53^62^67^69^73^76^83^86^98^A4^A6^A8^AI^AJ^AK^AL^BT^BU^BV^DM^UC*MB~DTP*291*D8*20041101~MSG*0-Beneficiary insured due to age OASI~EB*C**30*MB**23*203~DTP*291*RD8*20210101-20211231~EB*C**30*MB**29*0~DTP*291*RD8*20210101-20211231~EB*A**30*MB**27**0.2~DTP*291*RD8*20210101-20211231~EB*C**42^67^AJ*MB**23*0~DTP*292*RD8*20210101-20211231~EB*A**42^67^AJ*MB**27**0~DTP*292*RD8*20210101-20211231~EB*R**88*OT~REF*18*S5601~REF*N6*203*SilverScript SmartRx~DTP*292*D8*20210101~LS*2120~NM1*PR*2*extra healthy insurance~N3*123 address1~N4*Nashville*TN*37203~PER*IC**TE************UR*www.testwebsite.com~LE*2120~SE*50*0001~GE*1*376672775~IEA*1*376672775~"}