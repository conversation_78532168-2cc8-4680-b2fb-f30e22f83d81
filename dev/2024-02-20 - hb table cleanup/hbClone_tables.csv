hb_clone	public	nescache	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	dsl	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_user	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_user	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_wf_queue_node	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_wf_queue_node	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_workflow	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_wf_queue_team	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_wf_queue_team	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_wf_queue_team_members_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_closing	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_wf_queue_team_members_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_workflow	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_closing	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_line_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_audit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_audit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_line_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_calendar	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_wf_wizard_step	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_wf_wizard_step	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_calendar	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_careplan_problem	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_careplan_intervention	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_careplan_intervention	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_careplan_goal	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_careplan_goal	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_careplan_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_coffeedsl	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_careplan_problem	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_careplan_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_coffeedsl	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_issue_ticket	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_fax_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_fax_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_dashboard_comment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_dashboard_comment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_issue_ticket_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_issue_ticket_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_issue_ticket_to_issue_ticket_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sec_rule	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_issue_ticket	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_issue_ticket_to_issue_ticket_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_labresult_components_to_labcomponent_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_labresult	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_letter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_labcomponent	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_labcomponent	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_labresult_components_to_labcomponent_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_labresult	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_letter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_270_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sec_rule	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_270_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_adjustment_reason	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_adjustment_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_adjustment_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_activity	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_activity	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_adjustment_reason	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_adjustment_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_adjustment_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_allergen_group	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_allergen_group	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_list_allergen_allergen_group_to_list_allergen_group_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_bill_note_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_allergen	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_allergen	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_bill_note_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_list_allergen_allergen_group_to_list_allergen_group_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_careplan_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_careplan_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_category	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_category	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_cms_npi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_cms_npi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_code_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_claim_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_claim_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_code_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_coverage_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_coverage_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_diet	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_diet	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_dosage_form	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_dispense_type	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_dispense_type	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_discharge_reason	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_discharge_reason	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_disease	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_disease	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_dosage_form	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_drug_brand	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_drug_brand	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_entity_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_facility_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_fax_category	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_entity_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_entity_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_facility_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_entity_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_fax_category	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_flag	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_flag	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_fdb_ndc_price	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_fdb_ndc_price	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_fdb_alrgn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_fdb_alrgn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_fdb_ndc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_fdb_ndc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_frequency	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_frequency	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_device_subq_assmt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_insurance_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_hcpc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_hcpc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_intake_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_intake_substatus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_insurance_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_intake_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_intake_substatus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_inv_category	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_inv_category	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_label_header	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_label_header	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_invoice_type	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_invoice_type	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_invoice_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_invoice_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_lab	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_lab	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_los_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sideeffect_catheter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_los_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_ncpdp_terms	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_msp_type	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_msp_type	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_manufacturer	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_manufacturer	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_ndc_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_ndc_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_ncpdp_terms	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_note_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_note_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_order_substatus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_order_substatus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_order_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_order_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_nursing_issue	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_nursing_issue	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_outcome	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_outcome	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sideeffect_catheter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_payer_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_pa_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_pa_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_payer_type	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_payer_type	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_patient_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_patient_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_payer_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_pharmacy_issue	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_pharmacy_issue	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_device_subq_assmt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_place_residence	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_price_basis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_pharmacy_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_pharmacy_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_price_basis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_price_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_place_residence	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_price_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_priorauth_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_priorauth_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_ps_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_provider_ref_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_provider_ref_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_provider_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_provider_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_provider_type	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_provider_type	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_ps_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_pwk_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_pwk_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_responsibility_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_relationship	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_relationship	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_reaction	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_reaction	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_required_doc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_required_doc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_responsibility_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_revenue_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_revenue_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_service_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_rx_origin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_route	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_route	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_rx_origin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_service_type	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_service_type	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_service_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_software_cert	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_shipping_issue	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_shipping_issue	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_step	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_specialty	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_shipping_method	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_shipping_method	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_software_cert	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_specialty	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_step	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_tag	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_tax_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_system	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_system	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_tax_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_team	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_tag	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_team	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_therapy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_therapy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_us_state	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_unit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_unit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_unit_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_unit_qualifier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_unlock_reason	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_unlock_reason	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_us_state	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_void_reason_billing	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_void_reason_billing	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_note_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_module	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_wf_queue_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_void_reason	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_void_reason	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_wf_queue_status	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_module	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_note_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_medical_hx	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_password_policy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_password_policy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_pdf_template_fields	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_sch_enc_rpt_appt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_sch_enc_rpt_appt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_medical_hx	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_pdf_template_fields	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_pdf_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_pdf_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_pdf_template_form_fields_to_pdf_template_fields_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_pdf_template_form_fields_to_pdf_template_fields_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_permission_group	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_permission_group_member_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_physician_group	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_permission_group_member_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_physician_group_approved_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_physician_group	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_permission_group	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_physician_group_approved_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_query	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_release_notes	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_referral_source	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_referral_source	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_physician	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_physician	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_query	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_release_notes	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_report_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_report_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_report	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_activity_log	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_sales_attachment_flag_id_to_list_flag_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_activity_log	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_report	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_sales_attachment_flag_id_to_list_flag_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_call_import	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_call_import	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_notification	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_notification	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sideeffect	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sideeffect	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_manager	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_sideeffect_route_id_to_list_route_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_manager	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_sales_manager_rep_id_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_sideeffect_route_id_to_list_route_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_site_price_code_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_site_price_code_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_sales_manager_rep_id_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_prospect	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_prospect	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_rep_goals	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_rep_goals	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_territory	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_sideeffect_therapy_id_to_list_therapy_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_territory	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_device_subq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_sales_territory_assigned_sales_rep_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_sales_territory_assigned_sales_rep_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_sideeffect_therapy_id_to_list_therapy_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_sideeffect_drug_id_to_list_drug_brand_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_sideeffect_drug_id_to_list_drug_brand_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_list_symptoms_disease_id_to_list_disease_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_organization	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_sales_account_territories_to_sales_territory_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_call_log	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_sales_account_territories_to_sales_territory_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_call_log	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_organization	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_organization_physician_id_to_physician_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_sales_contact_to_sales_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_sales_contact_to_sales_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_sideeffect_disease_id_to_list_disease_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_sideeffect_disease_id_to_list_disease_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_account	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_sales_opportunity_to_sales_activity_log	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_sales_call_log_phy_id_to_sales_account_physician_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_sales_call_log_phy_id_to_sales_account_physician_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_view_communication_user_id_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_view_communication	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_view_schedule_user_id_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_view_communication	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_wf_wizard_to_wf_wizard_step	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_device_subq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sec_assign	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_task	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_site	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_site	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_task	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_site_price_code_matrix	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_inventory	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sec_assign	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_site_price_code_matrix	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_site_price_code_matrix_to_site_price_code_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_user_preference	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_user_preference	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ancillary_provider	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_supervisor_group	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_supervisor_group	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_supervisor_group_team_members_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_therapy_map_cotherapies_id_to_list_therapy_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_closing_unlock	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ancillary_provider	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_closing_unlock	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_supervisor_group_team_members_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_site_price_code_matrix_to_site_price_code_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_sales_call_log_to_sales_task	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_sales_call_log_to_sales_task	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_view_communication_user_id_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_therapy_map	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_therapy_map_cotherapies_id_to_list_therapy_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_therapy_map	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_view_dash_reminder	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_view_dash_reminder	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_company	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_company	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_contact	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_facility	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_facility	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_feesched_medicare	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_feesched_medicare	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_contact	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_daptomycin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_general_attachment_flag_id_to_list_flag_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_view_schedule	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_order_template_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_general_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_general_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_inventory	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_sales_call_log_contacts_id_to_sales_account_contact_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_sales_call_log_contacts_id_to_sales_account_contact_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_wf_wizard	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_view_schedule_user_id_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_wf_wizard	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_view_schedule	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_wf_wizard_to_wf_wizard_step	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_htcrequest	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_ptadmin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_ptadmin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_general_attachment_flag_id_to_list_flag_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_order_template_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_order_template_to_order_template_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_order_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_docusign_audit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_order_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_list_alternate_payer_to_general_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_list_alternate_payer_brand_id_to_list_drug_brand_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_alternate_payer	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_alternate_payer	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_letter_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_letter_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_inventory_lot	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_inventory_lot	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_docusign_audit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_billing_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_list_alternate_payer_to_general_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_list_billing_code_therapy_id_to_list_therapy_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_list_billing_code_therapy_id_to_list_therapy_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_order_template_to_order_template_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_patient_tag_id_to_list_tag_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_patient_tag_id_to_list_tag_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_sales_contact_log_dr_id_to_sales_account_physician_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_contact	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_sales_contact_to_sales_contact_log	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_contact	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_ccpayment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_list_alternate_payer_brand_id_to_list_drug_brand_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_activity_id_to_list_activity_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_activity_id_to_list_activity_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_sales_courtesy_call_dr_id_to_sales_account_physician_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_ccpayment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_medicarebdme	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_orderchange	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_medicarebdme	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_courtesy_call	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_paymentplan	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_sales_courtesy_call_to_sales_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_orderchange	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_paymentplan	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_daptomycin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_billing_code	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_payer_price_matrix_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_payer_price_matrix_item_rdoc_id_to_list_required_doc_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_payer_price_matrix_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_claimstatus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_cms_npi_group	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_cms_npi_group	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_courtesy_call	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_claimstatus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_copay_program	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_list_copay_program_brand_name_id_to_list_drug_brand_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_cms_npi_state	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_cms_npi_state	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_cms_npi_other	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_cms_npi_other	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_copay_program	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_courier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_courier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_list_courier_site_id_to_site_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_electronic_visit_verification	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_list_copay_program_brand_name_id_to_list_drug_brand_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_list_courier_site_id_to_site_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_diet_id_to_list_diet_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_diet_id_to_list_diet_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_payer_price_matrix_item_rdoc_id_to_list_required_doc_i	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_sales_courtesy_call_to_sales_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_costest	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_costest	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_htcrequest	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_electronic_visit_verification	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_payer_price_matrix_to_payer_price_matrix_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_payer_price_matrix	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_dose_type	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_list_dose_type_associated_therapy_id_to_list_therapy_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_diagnosis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_diagnosis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_payer_price_matrix	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_account_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_sales_account_attachment_flag_to_list_flag_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_account_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_dose_type	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_intake_to_therapy_map	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_intake_to_therapy_map	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_intake_therapy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_intake_therapy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_pap_program	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_list_pap_program_brand_name_id_to_list_drug_brand_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_encounter_aat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_list_dose_type_associated_therapy_id_to_list_therapy_i	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_list_pap_program_brand_name_id_to_list_drug_brand_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_payer_price_matrix_to_payer_price_matrix_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_sales_account_attachment_flag_to_list_flag_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_sales_courtesy_call_dr_id_to_sales_account_physician_i	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_patient_notification	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_patient_notification	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_selfreport_refill	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_pap_program	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_account_link	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_list_symptoms_drug_id_to_list_drug_brand_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_symptoms	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_list_supplier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_account_contact	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_list_supplier_site_id_to_site_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_supplier	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_list_symptoms_therapy_id_to_list_therapy_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_list_symptoms_disease_id_to_list_disease_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_account_contact	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_account_link	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_list_pap_program_to_general_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_list_pap_program_to_general_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_list_supplier_site_id_to_site_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sms_audit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_account_physician	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_account_physician	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_selfreport_refill	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sms_audit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_encounter_aat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_surescripts_medication	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_list_symptoms	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_sales_account_to_sales_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_sales_account_to_sales_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_opportunity	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_sales_opportunity_to_sales_activity_log	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_opportunity	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_encounter_antibiotic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_sales_call_log_dr_id_to_sales_account_physician_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_list_symptoms_drug_id_to_list_drug_brand_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_sales_call_log_dr_id_to_sales_account_physician_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_swim_lane_card	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_contact_log	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_sales_contact_log_dr_id_to_sales_account_physician_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sales_contact_log	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_sales_contact_to_sales_contact_log	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_surescripts_medication	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_swim_lane_card	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_billing_assessment_htcrequest_htc_id_to_list_step_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_billing_assessment_htcrequest_htc_id_to_list_step_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_sales_territory_assigned_sales_manager_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_sales_territory_assigned_sales_manager_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sec_role	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_list_symptoms_therapy_id_to_list_therapy_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_organization_physician_id_to_physician_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sec_group	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_sec_group	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sec_role	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_encounter_antibiotic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_hfqol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_sales_account	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_pttransfer	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_ptintro	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_ptintro	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_ptcall	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_ptcall	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_pttransfer	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_refcontact	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_pttriage	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_pttriage	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_refcontact	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_hbi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_invoice	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_sigoptout	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_sigoptout	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_careplan_wt_lb	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_claim	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_claim	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_invoice	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_careplan_wt_lb	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_careplan	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_careplan	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_careplan_template_id_to_careplan_template_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_careplan_template_id_to_careplan_template_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_checklist_fall	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_checklist_pain	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_checklist_pain	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_checklist_nutrition	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_checklist_nutrition	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_checklist_fall	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_checklist_safety	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_checklist_safety	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_checklist_wound_details	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_checklist_wound_details	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_checklist_wound_note	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_checklist_teaching	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_checklist_teaching	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_checklist_wound_note	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_encounter_environment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_checklist_wound_to_checklist_wound_note	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_checklist_wound_to_checklist_wound_note	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_encounter_environment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_encounter_factor	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_encounter_factor	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_encounter_iv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_encounter_iv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_encounter_lemtrada	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_encounter_lemtrada	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_checklist_wound	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_checklist_wound	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_checklist_wound_to_checklist_wound_details	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_checklist_wound_to_checklist_wound_details	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_claim	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_claim_to_billing_line_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_claim_to_billing_line_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_aghda	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_claim	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_aghda	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_alsfrs	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_alsfrs	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_cidp	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_cidp	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_braden	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_braden	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_basdai	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_basdai	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_dlqi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_dlqi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_epworth	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_epworth	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_hat_qol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_grip_strength	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_edss	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_edss	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_grip_strength	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_hat_qol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_hpq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_hmq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_hbi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_hbi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_hmq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_hfqol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_hfqol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_hpq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_incat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_incat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_mgadl	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_mgadl	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_mhaq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_mfis5	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_mfis5	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_mhaq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_mmas8	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_mmas8	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_moqlq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_moqlq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_ms	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_ms	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_mmn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_mmn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_mygrav	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_mygrav	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_myositis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_myositis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_pas2	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_padqol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_padqol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_pes	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_pes	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_pas2	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_phq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_phq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_radai	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_radai	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_qlsh	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_qlsh	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_poem	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_poem	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_rods	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_rods	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_encounter_pemphigus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_wat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_stiffps	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_sf12v2	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_sf12v2	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_sibdq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_stiffps	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_sibdq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_wat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_wellness_iv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_wellness_iv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_wpai	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_wpai	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_comorbid_anticoag	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_clinical_wellness_si	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_clinical_wellness_si	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_comorbid_anticoag	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_comorbid_headache	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_comorbid_headache	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_comorbid_hepatic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_comorbid_hepatic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_comorbid_renal	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_comorbid_thromboembolic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_encounter_pemphigus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_comorbid_thromboembolic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_comorbid_pancreatic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_comorbid_pancreatic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_comorbid_renal	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_dashboard	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_dashboard	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_dashboard_to_dashboard_comment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_dashboard_to_dashboard_comment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_device_catheter_assmt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_device_catheter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_device_catheter_assmt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_device_catheter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_hmq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_device_general_assmt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_device_general_assmt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_encounter_radicava	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_encounter_subqig	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_device_general	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_device_general	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_encounter_ros	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_encounter_ros	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_encounter_radicava	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_encounter_subqig	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_encounter_tnf	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_encounter_tnf	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_encounter_tpn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_encounter_tpn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_fax_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_fax_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_encounter_vital	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_encounter_vital	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_factor_bleed	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_factor_bleed	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_mgadl	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_incoming_fax_to_sales_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_incoming_fax	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_incoming_fax_to_sales_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_incoming_fax	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_inventory_log_log	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_inventory_log_log	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_inventory_lots	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_inventory_lots_to_inventory_lot	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_inventory_lots	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_inventory_lots_to_inventory_lot	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_inventory_po_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_inventory_po_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_inventory_po_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_inventory_po_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_inventory_po_template_to_inventory_po_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_inventory_po	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_inventory_po_template_to_inventory_po_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_inventory_po_to_inventory_po_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_inventory_po_to_inventory_po_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_inventory_po	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_mhaq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_mhaq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_medicare_hit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_medicare_hit_labs_list_id_to_list_lab_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_medicare_hit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_medicare_hit_labs_list_id_to_list_lab_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_nursing_assessment_to_device_catheter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_tpn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_tpn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_careplan_order_to_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_careplan_order_to_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_copay_program	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_medicare_hit_patient_allergy_id_to_list_allergen_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_medicare_hit_patient_allergy_id_to_list_allergen_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_nursing_assessment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_nursing_assessment_to_device_catheter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_nursing_assessment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_aat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_aat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_aminoglycoside_4	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_aminoglycoside_4	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_antibiotic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_antibiotic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_hepb	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_hepb	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_hepc_w12	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_daptomycin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_daptomycin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_hepc_w12	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_hpq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_hepc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_hepc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_inotrope	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_ms	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_hiv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_hiv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_inotrope	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_ocrevus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_ms	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_ocrevus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_mfis5	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_ra_1	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_psoriasis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_psoriasis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_ongoing_radicava_other_reactions_to_list_reaction_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_radicava	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_soliris	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_ongoing_radicava_other_reactions_to_list_reaction_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_ra_1	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_radicava	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_soliris	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_vancomycin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_vancomycin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_tysabri	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_subqig	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_subqig	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_tysabri	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_vyvgart	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_vyvgart	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_hbi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_inotrope	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_allergy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_allergy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_allergy_reaction_id_to_list_reaction_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_inotrope	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_copay_program	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_event	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_event_order_id_to_careplan_order_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_allergy_reaction_id_to_list_reaction_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_attachment_flag_id_to_list_flag_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_bill_note	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_ancillary	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_ancillary	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_bill_note	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_attachment_flag_id_to_list_flag_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_call_attempt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_call_attempt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_hospitalization	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_diagnosis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_courier_delivery	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_courier_delivery	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_diagnosis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_hospitalization	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_intake_medicare	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_intake_medicare	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_intake_soliris	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_intake_soliris	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_intake_ocrevus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_intake_ocrevus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_patient_labresult_to_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_lab	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_epworth	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_lab_labs_list_id_to_list_lab_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_lab	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_labresult	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_labresult	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_epworth	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_basdai	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_lab_labs_list_id_to_list_lab_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_patient_labresult_to_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_medication	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_medication	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_package_delivery	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_note	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_note	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_package_delivery	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_moqlq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_portal	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_portal	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_mfis5	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_enbrel	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_enbrel	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_prescriber	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_prescriber	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_event	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_providers	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_patient_todo	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_providers	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_patient_providers_to_patient_ancillary	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_patient_providers_to_patient_ancillary	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_todo_assign_to_id_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_todo_cc_response_id_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_payer_contract	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_payer_contract	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_moqlq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_payer	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_payer_subform_contact_to_contact_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_payer_site_id_to_site_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_dlqi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_payer	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_actemra	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_chemotherapy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_chemotherapy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_enbrel	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_humira	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_humira	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_ra	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_hfqol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_comorbid_thromboembolic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_comorbid_thromboembolic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_factor	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_payer_site_id_to_site_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_aat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_schedule_event_series	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_schedule_event_series	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_schedule_event	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_aat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_schedule_event	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_aat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_hmq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_payer_subform_contact_to_contact_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_aat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_dupixent	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_hemob	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_hemob	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_vyvgart	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_mgadl	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_wellness_iv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_wellness_iv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_vyvgart	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_kystexxa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_kystexxa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_vyvgart	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_cimzia	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_chemotherapy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_actemra	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_actemra	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_bisphosphonates	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_cimzia	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_bisphosphonates	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_chemotherapy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_dupixent	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_dupixent	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_factor	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_factor	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_hpq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_hemoa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_enbrel	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_enbrel	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_hemob	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_hemoa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_hemob	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_hepb	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_hepb	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_biosphosphonates	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_factor	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_hepc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_hepc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_stelara	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_orencia	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_comorbid_anticoag	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_comorbid_anticoag	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_lemtrada	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_ra	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_padqol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_hepc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_hepc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_inotrope	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_humira	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_humira	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_hiv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_hiv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_inotrope	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_krystexxa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_krystexxa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_ocrevus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_lemtrada	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_lemtrada	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_ms	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_ms	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_ocrevus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_orencia	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_orencia	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_hemoa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_hemoa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_soliris	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_psoriasis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_comorbid_headache	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_soliris	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_psoriasis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_psoriasis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_biosphosphonates	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_biosphosphonates	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_stelara	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_mmas8	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_comorbid_pancreatic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_tysabri	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_rituxan	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_mmas8	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_ra	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_radicava	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_rituxan	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_remicade	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_remicade	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_radicava	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_ra	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_rituxan	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_simponi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_simponi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_stelara	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_soliris	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_soliris	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_simponiaria	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_simponiaria	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_steroid	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_stelara	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_steroid	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_comorbid_hepatic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_comorbid_hepatic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_subqig	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_subqig	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_subqig	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_subqig	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_hepb	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_hepb	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_simponiaria	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_device_subq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_wellness_si	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_inotrope	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_pas2	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_pas2	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_tnf	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_vancomycin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_tysabri	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_tpn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_tpn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_tysabri	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_tnf	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_vancomycin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_vwd	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_vwd	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_enbrel	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_phq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_dupixent	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_biosphosphonates	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment_vyvgart	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment_vyvgart	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_actemra	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_factor	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_incat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_incat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_lemtrada	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_aghda	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_aghda	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_ms	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_pes	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_pes	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_hepc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_hepc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_phq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_radicava	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_radicava	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_event_outcome_nursing_to_list_outcome_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_event_outcome_nursing_to_list_outcome_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_kystexxa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_device_subq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_billing_assessment_claim_id_to_billing_claim_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_padqol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_antibiotic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_antibiotic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_event_reaction_id_to_list_reaction_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_event_reaction_id_to_list_reaction_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_sf12v2	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_sf12v2	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_kystexxa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_cimzia	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_cimzia	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_hiv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_hiv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_vwd	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_assessment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_assessment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_vwd	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_steroid	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_steroid	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_simponi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_wat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_patient_providers_to_patient_prescriber	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_patient_providers_to_patient_prescriber	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_todo_assign_to_id_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_todo	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_refill	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_refill	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_todo	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_todo_cc_response_id_to_user_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_poem	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_poem	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_ocrevus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_ocrevus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_prior_auth	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_remicade	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_remicade	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_wpai	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_wpai	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_vancomycin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_vancomycin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_billing_assessment_dx_id_to_patient_diagnosis_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_surescripts_message	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_medicarebdme	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_surescripts_message_to_surescripts_medication	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_ptcall	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_billing_assessment_dx_id_to_patient_diagnosis_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_billing_assessment_order_id_to_careplan_order_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_ptcall	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_patient_intake_bv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_ocrevus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_ocrevus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_qlsh	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_qlsh	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_soliris	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_soliris	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_surescripts_message	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_billing_assessment_order_id_to_careplan_order_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_billing_assessment_insurance_id_to_patient_insurance_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_sigoptout	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_radicava	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_radicava	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_wat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_alsfrs	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_alsfrs	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_surescripts_message_to_surescripts_medication	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_tpn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_tpn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_sibdq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_sibdq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_subqig	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_subqig	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_simponi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_device_catheter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_device_catheter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_wat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_wat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_rods	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_hat_qol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_hat_qol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_ms	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_sf12v2	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_sf12v2	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_wellness_iv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_edss	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_edss	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_vancomycin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_vancomycin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_wellness_si	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_psoriasis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_psoriasis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_pttransfer	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_ptadmin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_htcapprove	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_pttransfer	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_htcapprove	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_audit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_ptadmin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_billing_assessment_claim_id_to_billing_claim_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_paymentplan	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_assessment_tnf	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_alsfrs	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_wellness_si	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_alsfrs	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_tnf	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_htcapprove	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_assessment_audit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_adjustment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_adjustment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_audit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_billing_posting_invoice_id_to_billing_invoice_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment_htcapprove	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_kystexxa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_braden	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_hat_qol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_braden	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_hat_qol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_billing_assessment_htcapprove_htc_id_to_list_step_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_billing_assessment_htcapprove_htc_id_to_list_step_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_line_transaction	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_dupixent	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_posting	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_line_transaction	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_dupixent	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_comorbid_headache	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_hbi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_tysabri	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_hbi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_rods	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_rods	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_posting	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_ms	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_ms	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_enbrel	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_enbrel	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_ccpayment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_orderchange	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_medicarebdme	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_psoriasis	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_hfqol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_hfqol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_qlsh	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_qlsh	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_billing_posting_claim_id_to_billing_claim_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_billing_posting_claim_id_to_billing_claim_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_careplan_dl_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_careplan_dl_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_careplan_schedule_encounter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_orencia	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_hmq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_hmq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_billing_posting_invoice_id_to_billing_invoice_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_careplan_schedule_encounter_to_patient_sch_enc_rpt_appt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_careplan_schedule_encounter_to_patient_sch_enc_rpt_app	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_ra_1	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_ra_1	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_dlqi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_asssessment_pttriage	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_pttriage	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_audit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_asssessment_pttriage	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_inotrope	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_epworth	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_epworth	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_sibdq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_sibdq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_careplan_schedule_encounter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_careplan_wt_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_dashboard_audit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_dashboard_audit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_careplan_wt_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_tysabri	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_tysabri	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_hpq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_comorbid_pancreatic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_hpq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_factor	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_dupixent	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_chemotherapy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_chemotherapy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_factor	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_dupixent	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_krystexxa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_rituxan	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_moqlq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_moqlq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_krystexxa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_tpn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_tpn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_aghda	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_mfis5	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_aghda	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_mfis5	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_assessment_simponiaria	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_mgadl	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_mgadl	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_mhaq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_mhaq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_mmas8	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_mmas8	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_basdai	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_incat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_basdai	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_incat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_asssessment_pttransfer	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_rods	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_wellness_iv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_wellness_si	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_aat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_aat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_vyvgart	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_htcrequest	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_htcrequest	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_dlqi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_dlqi	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_padqol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_padqol	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_basdai	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_infection_pat_med_id_to_patient_medication_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_billing_assessment_auth_id_to_patient_prior_auth_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_patient_intake_bv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_paymentplan	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_billing_assessment_auth_id_to_patient_prior_auth_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_asssessment_pttransfer	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_pas2	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_pas2	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_aminoglycoside_4	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_aminoglycoside_4	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_factor	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_costest	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_claimstatus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_assessment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_claimstatus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_costest	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_ccpayment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_orderchange	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_careplan_dt_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_pes	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_pes	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_clinical_braden	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_clinical_braden	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_kystexxa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_refcontact	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_refcontact	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_assessment_to_billing_assessment_ptintro	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_ptintro	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_sigoptout	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_asssessment_pttriage	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_asssessment_pttriage	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_careplan_dt_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_encounter_agency	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_agency_to_patient_event	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_encounter_agency	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_agency_to_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_phq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_phq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_hepc_w12	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_hepc_w12	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_infection	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_infection_pat_med_id_to_patient_medication_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_infection	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_insurance	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_insurance	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_billing_batch_posting_to_billing_posting	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_poem	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_poem	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_hepb	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_hepb	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_asssessment_pttransfer	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_asssessment_pttransfer	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_billing_assessment_insurance_id_to_patient_insurance_i	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_billing_assessment_pttriage	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_assessment_to_patient_todo	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_batch_posting	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_billing_assessment_invoice_id_to_billing_invoice_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_billing_assessment_invoice_id_to_billing_invoice_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_agency_to_patient_event	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_edss	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_edss	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_clinical_wpai	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_clinical_wpai	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_ongoing_kystexxa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_ongoing_kystexxa	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_batch_posting	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_event_outcome_shipping_to_list_outcome_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_event_outcome_shipping_to_list_outcome_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_intake_bv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_intake_bv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_prior_auth	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_chemotherapy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_chemotherapy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_billing_batch_posting_to_billing_posting	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_careplan_order_to_careplan_order_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_account	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_account	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_billing_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_billing_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_billing_item_dx_id_to_patient_diagnosis_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_event_outcome_pharmacy_to_list_outcome_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_event_outcome_pharmacy_to_list_outcome_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_assessment_to_comorbid_renal	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_assessment_to_comorbid_renal	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_ongoing_to_ongoing_hiv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_ongoing_to_ongoing_hiv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_billing_item_dx_id_to_patient_diagnosis_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_careplan_order	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_careplan_order_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_careplan_order_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_careplan_order_to_careplan_order_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_careplan_order	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_event_order_id_to_careplan_order_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_agency_to_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_patient_intake_to_patient_intake_ocrevus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_patient_intake_to_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_patient_intake_to_patient_intake_medicare	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_patient_intake_to_patient_intake_medicare	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_patient_intake_to_patient_intake_bv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_patient_intake_to_patient_prior_auth	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_patient_intake_to_patient_intake_ocrevus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_patient_intake_to_patient_prior_auth	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_patient_intake_to_patient_intake_soliris	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_patient_intake_to_patient_intake_soliris	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_intake_insurance_id_to_patient_insurance_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_intake_insurance_id_to_patient_insurance_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_intake	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_intake	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_patient_intake_to_patient_intake_bv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_patient_intake_to_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_status_audit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_status_audit	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_wf_queue	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_wf_queue	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_careplan_delivery_tick_to_billing_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_careplan_delivery_tick_order_id_to_careplan_order_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_careplan_delivery_tick_to_billing_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_careplan_delivery_tick	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_careplan_delivery_tick_order_id_to_careplan_order_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_careplan_delivery_tick_to_careplan_dt_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_careplan_delivery_tick	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_careplan_delivery_tick_to_careplan_dt_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_careplan_work_ticket	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_careplan_work_ticket	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_careplan_work_ticket_subform_label_to_careplan_wt_lb_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_careplan_work_ticket_subform_label_to_careplan_wt_lb_i	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_careplan_work_ticket_to_careplan_dt_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_careplan_work_ticket_to_careplan_dt_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_careplan_work_ticket_subform_wti_to_careplan_wt_item_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_careplan_work_ticket_subform_wti_to_careplan_wt_item_i	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_assistance_pap_id_to_list_pap_program_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_assistance_pap_cl_id_to_list_pap_program_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_assistance_ap_id_to_list_alternate_payer_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_assistance_pap_id_to_list_pap_program_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_assistance_ap_id_to_list_alternate_payer_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_patient_assistance	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_assistance_ap_cl_id_to_list_alternate_payer_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_patient_assistance	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_assistance_ap_cl_id_to_list_alternate_payer_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_assistance_copay_id_to_list_copay_program_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	gr_form_patient_assistance_copay_cl_id_to_list_copay_program_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_assistance_copay_cl_id_to_list_copay_program_i	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_assistance_copay_id_to_list_copay_program_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_careplan_delivery_log_to_careplan_dl_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_careplan_delivery_log_to_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lgr_form_patient_assistance_pap_cl_id_to_list_pap_program_id	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_admin_general	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_admin_general	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_admin_catheter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_wf_comment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_wf_comment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_admin_catheter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_admin_subq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_admin_subq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_careplan_delivery_log	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_careplan_delivery_log	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_careplan_delivery_log_to_careplan_dl_item	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_careplan_delivery_log_to_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_device_catheter_assmt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_device_catheter_assmt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_encounter_environment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	form_encounter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_device_catheter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	log_encounter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_device_catheter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_encounter_environment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_encounter_subqig	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_encounter_subqig	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_encounter_pemphigus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_device_general	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_encounter_antibiotic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_encounter_pemphigus	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_device_general	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_encounter_antibiotic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_device_subq_assmt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_device_subq_assmt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_encounter_lemtrada	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_encounter_lemtrada	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_encounter_vital	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_encounter_aat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_encounter_aat	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_encounter_vital	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_encounter_tnf	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_encounter_tnf	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_patient_attachment	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_encounter_radicava	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_encounter_radicava	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_patient_event	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_patient_event	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_encounter_iv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_admin_catheter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_encounter_iv	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_admin_catheter	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_encounter_tpn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_encounter_tpn	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_encounter_factor	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_encounter_factor	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_admin_general	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_device_general_assmt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_admin_subq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_encounter_ros	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_device_general_assmt	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_admin_general	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_admin_subq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_encounter_ros	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	sf_form_encounter_to_device_subq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
hb_clone	public	lsf_form_encounter_to_device_subq	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
