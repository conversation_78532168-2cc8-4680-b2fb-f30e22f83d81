ALTER TABLE form_list_billing_dose_indicator DROP CONSTRAINT IF EXISTS form_list_billing_dose_indicator_change_by_fk;
ALTER TABLE form_list_billing_dose_indicator DROP CONSTRAINT IF EXISTS form_list_billing_dose_indicator_created_by_fk;
ALTER TABLE form_list_billing_dose_indicator DROP CONSTRAINT IF EXISTS form_list_billing_dose_indicator_reviewed_by_fk;
ALTER TABLE form_list_billing_dose_indicator DROP CONSTRAINT IF EXISTS form_list_billing_dose_indicator_updated_by_fk;
ALTER TABLE form_list_billing_uom_code DROP CONSTRAINT IF EXISTS form_list_billing_uom_code_change_by_fk;
ALTER TABLE form_list_billing_uom_code DROP CONSTRAINT IF EXISTS form_list_billing_uom_code_created_by_fk;
ALTER TABLE form_list_billing_uom_code DROP CONSTRAINT IF EXISTS form_list_billing_uom_code_reviewed_by_fk;
ALTER TABLE form_list_billing_uom_code DROP CONSTRAINT IF EXISTS form_list_billing_uom_code_updated_by_fk;
ALTER TABLE form_list_compound_type DROP CONSTRAINT IF EXISTS form_list_compound_type_change_by_fk;
ALTER TABLE form_list_compound_type DROP CONSTRAINT IF EXISTS form_list_compound_type_created_by_fk;
ALTER TABLE form_list_compound_type DROP CONSTRAINT IF EXISTS form_list_compound_type_reviewed_by_fk;
ALTER TABLE form_list_compound_type DROP CONSTRAINT IF EXISTS form_list_compound_type_updated_by_fk;
ALTER TABLE form_list_compound_form DROP CONSTRAINT IF EXISTS form_list_compound_form_change_by_fk;
ALTER TABLE form_list_compound_form DROP CONSTRAINT IF EXISTS form_list_compound_form_created_by_fk;
ALTER TABLE form_list_compound_form DROP CONSTRAINT IF EXISTS form_list_compound_form_reviewed_by_fk;
ALTER TABLE form_list_compound_form DROP CONSTRAINT IF EXISTS form_list_compound_form_updated_by_fk;
ALTER TABLE form_list_compound_route DROP CONSTRAINT IF EXISTS form_list_compound_route_change_by_fk;
ALTER TABLE form_list_compound_route DROP CONSTRAINT IF EXISTS form_list_compound_route_created_by_fk;
ALTER TABLE form_list_compound_route DROP CONSTRAINT IF EXISTS form_list_compound_route_reviewed_by_fk;
ALTER TABLE form_list_compound_route DROP CONSTRAINT IF EXISTS form_list_compound_route_updated_by_fk;
ALTER TABLE form_list_compound_unit DROP CONSTRAINT IF EXISTS form_list_compound_unit_change_by_fk;
ALTER TABLE form_list_compound_unit DROP CONSTRAINT IF EXISTS form_list_compound_unit_created_by_fk;
ALTER TABLE form_list_compound_unit DROP CONSTRAINT IF EXISTS form_list_compound_unit_reviewed_by_fk;
ALTER TABLE form_list_compound_unit DROP CONSTRAINT IF EXISTS form_list_compound_unit_updated_by_fk;
ALTER TABLE form_list_daw_qualifier DROP CONSTRAINT IF EXISTS form_list_daw_qualifier_change_by_fk;
ALTER TABLE form_list_daw_qualifier DROP CONSTRAINT IF EXISTS form_list_daw_qualifier_created_by_fk;
ALTER TABLE form_list_daw_qualifier DROP CONSTRAINT IF EXISTS form_list_daw_qualifier_reviewed_by_fk;
ALTER TABLE form_list_daw_qualifier DROP CONSTRAINT IF EXISTS form_list_daw_qualifier_updated_by_fk;
ALTER TABLE form_list_coverage_relationship DROP CONSTRAINT IF EXISTS form_list_coverage_relationship_change_by_fk;
ALTER TABLE form_list_coverage_relationship DROP CONSTRAINT IF EXISTS form_list_coverage_relationship_created_by_fk;
ALTER TABLE form_list_coverage_relationship DROP CONSTRAINT IF EXISTS form_list_coverage_relationship_reviewed_by_fk;
ALTER TABLE form_list_coverage_relationship DROP CONSTRAINT IF EXISTS form_list_coverage_relationship_updated_by_fk;
ALTER TABLE form_list_eligibility_class_code DROP CONSTRAINT IF EXISTS form_list_eligibility_class_code_change_by_fk;
ALTER TABLE form_list_eligibility_class_code DROP CONSTRAINT IF EXISTS form_list_eligibility_class_code_created_by_fk;
ALTER TABLE form_list_eligibility_class_code DROP CONSTRAINT IF EXISTS form_list_eligibility_class_code_reviewed_by_fk;
ALTER TABLE form_list_eligibility_class_code DROP CONSTRAINT IF EXISTS form_list_eligibility_class_code_updated_by_fk;
ALTER TABLE form_list_inter_auth_type DROP CONSTRAINT IF EXISTS form_list_inter_auth_type_change_by_fk;
ALTER TABLE form_list_inter_auth_type DROP CONSTRAINT IF EXISTS form_list_inter_auth_type_created_by_fk;
ALTER TABLE form_list_inter_auth_type DROP CONSTRAINT IF EXISTS form_list_inter_auth_type_reviewed_by_fk;
ALTER TABLE form_list_inter_auth_type DROP CONSTRAINT IF EXISTS form_list_inter_auth_type_updated_by_fk;
ALTER TABLE form_list_ncpdp_delay_reason DROP CONSTRAINT IF EXISTS form_list_ncpdp_delay_reason_change_by_fk;
ALTER TABLE form_list_ncpdp_delay_reason DROP CONSTRAINT IF EXISTS form_list_ncpdp_delay_reason_created_by_fk;
ALTER TABLE form_list_ncpdp_delay_reason DROP CONSTRAINT IF EXISTS form_list_ncpdp_delay_reason_reviewed_by_fk;
ALTER TABLE form_list_ncpdp_delay_reason DROP CONSTRAINT IF EXISTS form_list_ncpdp_delay_reason_updated_by_fk;
ALTER TABLE form_list_pregnancy_indicator DROP CONSTRAINT IF EXISTS form_list_pregnancy_indicator_change_by_fk;
ALTER TABLE form_list_pregnancy_indicator DROP CONSTRAINT IF EXISTS form_list_pregnancy_indicator_created_by_fk;
ALTER TABLE form_list_pregnancy_indicator DROP CONSTRAINT IF EXISTS form_list_pregnancy_indicator_reviewed_by_fk;
ALTER TABLE form_list_pregnancy_indicator DROP CONSTRAINT IF EXISTS form_list_pregnancy_indicator_updated_by_fk;
ALTER TABLE form_list_submission_class_code DROP CONSTRAINT IF EXISTS form_list_submission_class_code_change_by_fk;
ALTER TABLE form_list_submission_class_code DROP CONSTRAINT IF EXISTS form_list_submission_class_code_created_by_fk;
ALTER TABLE form_list_submission_class_code DROP CONSTRAINT IF EXISTS form_list_submission_class_code_reviewed_by_fk;
ALTER TABLE form_list_submission_class_code DROP CONSTRAINT IF EXISTS form_list_submission_class_code_updated_by_fk;
ALTER TABLE form_list_tax_basis DROP CONSTRAINT IF EXISTS form_list_tax_basis_change_by_fk;
ALTER TABLE form_list_tax_basis DROP CONSTRAINT IF EXISTS form_list_tax_basis_created_by_fk;
ALTER TABLE form_list_tax_basis DROP CONSTRAINT IF EXISTS form_list_tax_basis_reviewed_by_fk;
ALTER TABLE form_list_tax_basis DROP CONSTRAINT IF EXISTS form_list_tax_basis_updated_by_fk;
ALTER TABLE form_careplan DROP CONSTRAINT IF EXISTS form_careplan_dx_1_fk;
ALTER TABLE form_careplan DROP CONSTRAINT IF EXISTS form_careplan_dx_1_fkey;
ALTER TABLE form_careplan DROP CONSTRAINT IF EXISTS form_careplan_intake_id_fk;
ALTER TABLE form_careplan DROP CONSTRAINT IF EXISTS form_careplan_intake_id_fkey;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_1_code_fk;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_1_code_fkey;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_1_fk;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_1_fkey;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_2_code_fk;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_2_code_fkey;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_2_fk;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_2_fkey;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_3_code_fk;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_3_code_fkey;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_3_fk;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_3_fkey;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_4_code_fk;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_4_code_fkey;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_4_fk;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_4_fkey;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_5_code_fk;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_5_code_fkey;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_5_fk;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_drug_brand_5_fkey;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_intake_id_fk;
ALTER TABLE form_ongoing DROP CONSTRAINT IF EXISTS form_ongoing_intake_id_fkey;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_drug_brand_1_fk;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_drug_brand_2_fk;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_drug_brand_3_fk;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_drug_brand_4_fk;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_drug_brand_5_fk;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_dx_1_fk;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_dx_2_fk;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_dx_3_fk;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_dx_4_fk;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_dx_5_fk;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_state_fk;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_therapy_1_fk;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_therapy_2_fk;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_therapy_3_fk;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_therapy_4_fk;
ALTER TABLE form_sales_account DROP CONSTRAINT IF EXISTS form_sales_account_therapy_5_fk;
ALTER TABLE form_patient DROP CONSTRAINT IF EXISTS form_patient_dx_1_fk;
ALTER TABLE form_patient DROP CONSTRAINT IF EXISTS form_patient_dx_1_fkey;
ALTER TABLE form_patient DROP CONSTRAINT IF EXISTS form_patient_home_state_fk;
ALTER TABLE form_patient DROP CONSTRAINT IF EXISTS form_patient_home_state_fkey;
ALTER TABLE form_patient DROP CONSTRAINT IF EXISTS form_patient_ship_state_fk;
ALTER TABLE form_patient DROP CONSTRAINT IF EXISTS form_patient_ship_state_fkey;
ALTER TABLE form_patient DROP CONSTRAINT IF EXISTS form_patient_therapy_1_fk;
ALTER TABLE form_patient DROP CONSTRAINT IF EXISTS form_patient_therapy_1_fkey;
ALTER TABLE form_physician DROP CONSTRAINT IF EXISTS form_physician_state_fk;
ALTER TABLE form_physician DROP CONSTRAINT IF EXISTS form_physician_state_fkey;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_drug_brand_1_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_drug_brand_2_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_drug_brand_3_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_drug_brand_4_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_drug_brand_5_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_dx_1_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_dx_2_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_dx_3_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_dx_4_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_dx_5_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_intake_1_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_intake_2_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_intake_3_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_intake_4_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_intake_5_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_therapy_1_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_therapy_2_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_therapy_3_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_therapy_4_fk;
ALTER TABLE form_wf_queue DROP CONSTRAINT IF EXISTS form_wf_queue_therapy_5_fk;
ALTER TABLE form_patient_insurance DROP CONSTRAINT IF EXISTS form_patient_insurance_insurance_name_fk;
ALTER TABLE form_patient_insurance DROP CONSTRAINT IF EXISTS form_patient_insurance_insurance_name_fkey;
ALTER TABLE form_encounter DROP CONSTRAINT IF EXISTS form_encounter_chief_complaint_fk;
ALTER TABLE form_encounter DROP CONSTRAINT IF EXISTS form_encounter_chief_complaint_fkey;
ALTER TABLE form_encounter DROP CONSTRAINT IF EXISTS form_encounter_intake_id_fk;
ALTER TABLE form_encounter DROP CONSTRAINT IF EXISTS form_encounter_intake_id_fkey;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_1_code_fk;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_1_code_fkey;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_1_fk;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_1_fkey;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_2_code_fk;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_2_code_fkey;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_2_fk;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_2_fkey;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_3_code_fk;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_3_code_fkey;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_3_fk;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_3_fkey;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_4_code_fk;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_4_code_fkey;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_4_fk;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_4_fkey;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_5_code_fk;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_5_code_fkey;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_5_fk;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_drug_brand_5_fkey;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_intake_id_fk;
ALTER TABLE form_assessment DROP CONSTRAINT IF EXISTS form_assessment_intake_id_fkey;
ALTER TABLE form_insurance DROP CONSTRAINT IF EXISTS form_insurance_change_by_fk;
ALTER TABLE form_insurance DROP CONSTRAINT IF EXISTS form_insurance_change_by_fkey;
ALTER TABLE form_insurance DROP CONSTRAINT IF EXISTS form_insurance_created_by_fk;
ALTER TABLE form_insurance DROP CONSTRAINT IF EXISTS form_insurance_created_by_fkey;
ALTER TABLE form_insurance DROP CONSTRAINT IF EXISTS form_insurance_reviewed_by_fk;
ALTER TABLE form_insurance DROP CONSTRAINT IF EXISTS form_insurance_reviewed_by_fkey;
ALTER TABLE form_insurance DROP CONSTRAINT IF EXISTS form_insurance_state_fkey;
ALTER TABLE form_insurance DROP CONSTRAINT IF EXISTS form_insurance_updated_by_fk;
ALTER TABLE form_insurance DROP CONSTRAINT IF EXISTS form_insurance_updated_by_fkey;
ALTER TABLE form_insurance DROP CONSTRAINT IF EXISTS form_insurance_user_id_fk;
ALTER TABLE form_insurance DROP CONSTRAINT IF EXISTS form_insurance_user_id_fkey;
ALTER TABLE form_allergen DROP CONSTRAINT IF EXISTS form_allergen_change_by_fk;
ALTER TABLE form_allergen DROP CONSTRAINT IF EXISTS form_allergen_change_by_fkey;
ALTER TABLE form_allergen DROP CONSTRAINT IF EXISTS form_allergen_created_by_fk;
ALTER TABLE form_allergen DROP CONSTRAINT IF EXISTS form_allergen_created_by_fkey;
ALTER TABLE form_allergen DROP CONSTRAINT IF EXISTS form_allergen_reviewed_by_fk;
ALTER TABLE form_allergen DROP CONSTRAINT IF EXISTS form_allergen_reviewed_by_fkey;
ALTER TABLE form_allergen DROP CONSTRAINT IF EXISTS form_allergen_updated_by_fk;
ALTER TABLE form_allergen DROP CONSTRAINT IF EXISTS form_allergen_updated_by_fkey;
ALTER TABLE form_unit DROP CONSTRAINT IF EXISTS form_unit_change_by_fk;
ALTER TABLE form_unit DROP CONSTRAINT IF EXISTS form_unit_change_by_fkey;
ALTER TABLE form_unit DROP CONSTRAINT IF EXISTS form_unit_created_by_fk;
ALTER TABLE form_unit DROP CONSTRAINT IF EXISTS form_unit_created_by_fkey;
ALTER TABLE form_unit DROP CONSTRAINT IF EXISTS form_unit_reviewed_by_fk;
ALTER TABLE form_unit DROP CONSTRAINT IF EXISTS form_unit_reviewed_by_fkey;
ALTER TABLE form_unit DROP CONSTRAINT IF EXISTS form_unit_updated_by_fk;
ALTER TABLE form_unit DROP CONSTRAINT IF EXISTS form_unit_updated_by_fkey;
ALTER TABLE form_medication DROP CONSTRAINT IF EXISTS form_medication_change_by_fk;
ALTER TABLE form_medication DROP CONSTRAINT IF EXISTS form_medication_change_by_fkey;
ALTER TABLE form_medication DROP CONSTRAINT IF EXISTS form_medication_created_by_fk;
ALTER TABLE form_medication DROP CONSTRAINT IF EXISTS form_medication_created_by_fkey;
ALTER TABLE form_medication DROP CONSTRAINT IF EXISTS form_medication_reviewed_by_fk;
ALTER TABLE form_medication DROP CONSTRAINT IF EXISTS form_medication_reviewed_by_fkey;
ALTER TABLE form_medication DROP CONSTRAINT IF EXISTS form_medication_updated_by_fk;
ALTER TABLE form_medication DROP CONSTRAINT IF EXISTS form_medication_updated_by_fkey;
ALTER TABLE form_facility DROP CONSTRAINT IF EXISTS form_facility_state_fk;
ALTER TABLE form_facility DROP CONSTRAINT IF EXISTS form_facility_state_fkey;
ALTER TABLE form_us_state DROP CONSTRAINT IF EXISTS form_us_state_change_by_fk;
ALTER TABLE form_us_state DROP CONSTRAINT IF EXISTS form_us_state_change_by_fkey;
ALTER TABLE form_us_state DROP CONSTRAINT IF EXISTS form_us_state_created_by_fk;
ALTER TABLE form_us_state DROP CONSTRAINT IF EXISTS form_us_state_created_by_fkey;
ALTER TABLE form_us_state DROP CONSTRAINT IF EXISTS form_us_state_reviewed_by_fk;
ALTER TABLE form_us_state DROP CONSTRAINT IF EXISTS form_us_state_reviewed_by_fkey;
ALTER TABLE form_us_state DROP CONSTRAINT IF EXISTS form_us_state_updated_by_fk;
ALTER TABLE form_us_state DROP CONSTRAINT IF EXISTS form_us_state_updated_by_fkey;
ALTER TABLE form_diagnosis DROP CONSTRAINT IF EXISTS form_diagnosis_change_by_fk;
ALTER TABLE form_diagnosis DROP CONSTRAINT IF EXISTS form_diagnosis_change_by_fkey;
ALTER TABLE form_diagnosis DROP CONSTRAINT IF EXISTS form_diagnosis_created_by_fk;
ALTER TABLE form_diagnosis DROP CONSTRAINT IF EXISTS form_diagnosis_created_by_fkey;
ALTER TABLE form_diagnosis DROP CONSTRAINT IF EXISTS form_diagnosis_disease_fk;
ALTER TABLE form_diagnosis DROP CONSTRAINT IF EXISTS form_diagnosis_disease_fkey;
ALTER TABLE form_diagnosis DROP CONSTRAINT IF EXISTS form_diagnosis_reviewed_by_fk;
ALTER TABLE form_diagnosis DROP CONSTRAINT IF EXISTS form_diagnosis_reviewed_by_fkey;
ALTER TABLE form_diagnosis DROP CONSTRAINT IF EXISTS form_diagnosis_updated_by_fk;
ALTER TABLE form_diagnosis DROP CONSTRAINT IF EXISTS form_diagnosis_updated_by_fkey;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_assessment_form_fk;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_assessment_form_fkey;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_change_by_fk;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_change_by_fkey;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_created_by_fk;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_created_by_fkey;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_encounter_form_fk;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_encounter_form_fkey;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_ongoing_form_fk;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_ongoing_form_fkey;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_reviewed_by_fk;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_reviewed_by_fkey;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_selfreport_form_fk;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_selfreport_form_fkey;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_updated_by_fk;
ALTER TABLE form_disease DROP CONSTRAINT IF EXISTS form_disease_updated_by_fkey;
ALTER TABLE form_therapy DROP CONSTRAINT IF EXISTS form_therapy_change_by_fk;
ALTER TABLE form_therapy DROP CONSTRAINT IF EXISTS form_therapy_change_by_fkey;
ALTER TABLE form_therapy DROP CONSTRAINT IF EXISTS form_therapy_created_by_fk;
ALTER TABLE form_therapy DROP CONSTRAINT IF EXISTS form_therapy_created_by_fkey;
ALTER TABLE form_therapy DROP CONSTRAINT IF EXISTS form_therapy_reviewed_by_fk;
ALTER TABLE form_therapy DROP CONSTRAINT IF EXISTS form_therapy_reviewed_by_fkey;
ALTER TABLE form_therapy DROP CONSTRAINT IF EXISTS form_therapy_updated_by_fk;
ALTER TABLE form_therapy DROP CONSTRAINT IF EXISTS form_therapy_updated_by_fkey;
ALTER TABLE form_problem DROP CONSTRAINT IF EXISTS form_problem_change_by_fk;
ALTER TABLE form_problem DROP CONSTRAINT IF EXISTS form_problem_change_by_fkey;
ALTER TABLE form_problem DROP CONSTRAINT IF EXISTS form_problem_created_by_fk;
ALTER TABLE form_problem DROP CONSTRAINT IF EXISTS form_problem_created_by_fkey;
ALTER TABLE form_problem DROP CONSTRAINT IF EXISTS form_problem_reviewed_by_fk;
ALTER TABLE form_problem DROP CONSTRAINT IF EXISTS form_problem_reviewed_by_fkey;
ALTER TABLE form_problem DROP CONSTRAINT IF EXISTS form_problem_updated_by_fk;
ALTER TABLE form_problem DROP CONSTRAINT IF EXISTS form_problem_updated_by_fkey;
ALTER TABLE form_company DROP CONSTRAINT IF EXISTS form_company_state_fk;
ALTER TABLE form_schedule_event DROP CONSTRAINT IF EXISTS form_schedule_event_therapy_1_fk;
ALTER TABLE form_assessment_bisphosphonates DROP CONSTRAINT IF EXISTS form_assessment_bisphosphonates_intake_id_fk;
ALTER TABLE form_assessment_bisphosphonates DROP CONSTRAINT IF EXISTS form_assessment_bisphosphonates_intake_id_fkey;
ALTER TABLE form_assessment_chemotherapy DROP CONSTRAINT IF EXISTS form_assessment_chemotherapy_intake_id_fk;
ALTER TABLE form_assessment_chemotherapy DROP CONSTRAINT IF EXISTS form_assessment_chemotherapy_intake_id_fkey;
ALTER TABLE form_patient_allergy DROP CONSTRAINT IF EXISTS form_patient_allergy_allergen_fk;
ALTER TABLE form_patient_allergy DROP CONSTRAINT IF EXISTS form_patient_allergy_allergen_fkey;
ALTER TABLE form_assessment_factor DROP CONSTRAINT IF EXISTS form_assessment_factor_intake_id_fk;
ALTER TABLE form_assessment_factor DROP CONSTRAINT IF EXISTS form_assessment_factor_intake_id_fkey;
ALTER TABLE form_assessment_factor DROP CONSTRAINT IF EXISTS form_assessment_factor_last_therapy_brand_fk;
ALTER TABLE form_assessment_factor DROP CONSTRAINT IF EXISTS form_assessment_factor_last_therapy_brand_fkey;
ALTER TABLE form_assessment_aat DROP CONSTRAINT IF EXISTS form_assessment_aat_intake_id_fk;
ALTER TABLE form_assessment_aat DROP CONSTRAINT IF EXISTS form_assessment_aat_intake_id_fkey;
ALTER TABLE form_assessment_aat DROP CONSTRAINT IF EXISTS form_assessment_aat_last_therapy_brand_fk;
ALTER TABLE form_assessment_aat DROP CONSTRAINT IF EXISTS form_assessment_aat_last_therapy_brand_fkey;
ALTER TABLE form_assessment_hepb DROP CONSTRAINT IF EXISTS form_assessment_hepb_intake_id_fk;
ALTER TABLE form_assessment_hepb DROP CONSTRAINT IF EXISTS form_assessment_hepb_intake_id_fkey;
ALTER TABLE form_assessment_dupixent DROP CONSTRAINT IF EXISTS form_assessment_dupixent_intake_id_fk;
ALTER TABLE form_assessment_dupixent DROP CONSTRAINT IF EXISTS form_assessment_dupixent_intake_id_fkey;
ALTER TABLE form_assessment_hepc DROP CONSTRAINT IF EXISTS form_assessment_hepc_intake_id_fk;
ALTER TABLE form_assessment_hepc DROP CONSTRAINT IF EXISTS form_assessment_hepc_intake_id_fkey;
ALTER TABLE form_assessment_ms DROP CONSTRAINT IF EXISTS form_assessment_ms_intake_id_fk;
ALTER TABLE form_assessment_ms DROP CONSTRAINT IF EXISTS form_assessment_ms_intake_id_fkey;
ALTER TABLE form_assessment_hiv DROP CONSTRAINT IF EXISTS form_assessment_hiv_intake_id_fk;
ALTER TABLE form_assessment_hiv DROP CONSTRAINT IF EXISTS form_assessment_hiv_intake_id_fkey;
ALTER TABLE form_assessment_krystexxa DROP CONSTRAINT IF EXISTS form_assessment_krystexxa_intake_id_fk;
ALTER TABLE form_assessment_krystexxa DROP CONSTRAINT IF EXISTS form_assessment_krystexxa_intake_id_fkey;
ALTER TABLE form_assessment_humira DROP CONSTRAINT IF EXISTS form_assessment_humira_intake_id_fk;
ALTER TABLE form_assessment_humira DROP CONSTRAINT IF EXISTS form_assessment_humira_intake_id_fkey;
ALTER TABLE form_assessment_steroid DROP CONSTRAINT IF EXISTS form_assessment_steroid_intake_id_fk;
ALTER TABLE form_assessment_steroid DROP CONSTRAINT IF EXISTS form_assessment_steroid_intake_id_fkey;
ALTER TABLE form_assessment_vyvgart DROP CONSTRAINT IF EXISTS form_assessment_vyvgart_intake_id_fkey;
ALTER TABLE form_assessment_inotrope DROP CONSTRAINT IF EXISTS form_assessment_inotrope_intake_id_fk;
ALTER TABLE form_assessment_inotrope DROP CONSTRAINT IF EXISTS form_assessment_inotrope_intake_id_fkey;
ALTER TABLE form_assessment_psoriasis DROP CONSTRAINT IF EXISTS form_assessment_psoriasis_intake_id_fk;
ALTER TABLE form_assessment_psoriasis DROP CONSTRAINT IF EXISTS form_assessment_psoriasis_intake_id_fkey;
ALTER TABLE form_checklist_pain DROP CONSTRAINT IF EXISTS form_checklist_pain_intake_id_fk;
ALTER TABLE form_checklist_pain DROP CONSTRAINT IF EXISTS form_checklist_pain_intake_id_fkey;
ALTER TABLE form_assessment_tpn DROP CONSTRAINT IF EXISTS form_assessment_tpn_intake_id_fk;
ALTER TABLE form_assessment_tpn DROP CONSTRAINT IF EXISTS form_assessment_tpn_intake_id_fkey;
ALTER TABLE form_assessment_tysabri DROP CONSTRAINT IF EXISTS form_assessment_tysabri_intake_id_fk;
ALTER TABLE form_assessment_tysabri DROP CONSTRAINT IF EXISTS form_assessment_tysabri_intake_id_fkey;
ALTER TABLE form_assessment_lemtrada DROP CONSTRAINT IF EXISTS form_assessment_lemtrada_intake_id_fk;
ALTER TABLE form_assessment_lemtrada DROP CONSTRAINT IF EXISTS form_assessment_lemtrada_intake_id_fkey;
ALTER TABLE form_assessment_ra DROP CONSTRAINT IF EXISTS form_assessment_ra_intake_id_fk;
ALTER TABLE form_assessment_ra DROP CONSTRAINT IF EXISTS form_assessment_ra_intake_id_fkey;
ALTER TABLE form_assessment_soliris DROP CONSTRAINT IF EXISTS form_assessment_soliris_intake_id_fk;
ALTER TABLE form_assessment_soliris DROP CONSTRAINT IF EXISTS form_assessment_soliris_intake_id_fkey;
ALTER TABLE form_assessment_ocrevus DROP CONSTRAINT IF EXISTS form_assessment_ocrevus_intake_id_fk;
ALTER TABLE form_assessment_ocrevus DROP CONSTRAINT IF EXISTS form_assessment_ocrevus_intake_id_fkey;
ALTER TABLE form_assessment_radicava DROP CONSTRAINT IF EXISTS form_assessment_radicava_intake_id_fk;
ALTER TABLE form_assessment_radicava DROP CONSTRAINT IF EXISTS form_assessment_radicava_intake_id_fkey;
ALTER TABLE form_assessment_rituxan DROP CONSTRAINT IF EXISTS form_assessment_rituxan_intake_id_fk;
ALTER TABLE form_assessment_rituxan DROP CONSTRAINT IF EXISTS form_assessment_rituxan_intake_id_fkey;
ALTER TABLE form_assessment_tnf DROP CONSTRAINT IF EXISTS form_assessment_tnf_intake_id_fk;
ALTER TABLE form_assessment_tnf DROP CONSTRAINT IF EXISTS form_assessment_tnf_intake_id_fkey;
ALTER TABLE form_careplan_problem DROP CONSTRAINT IF EXISTS form_careplan_problem_intake_id_fk;
ALTER TABLE form_careplan_problem DROP CONSTRAINT IF EXISTS form_careplan_problem_intake_id_fkey;
ALTER TABLE form_checklist_nutrition DROP CONSTRAINT IF EXISTS form_checklist_nutrition_intake_id_fk;
ALTER TABLE form_checklist_nutrition DROP CONSTRAINT IF EXISTS form_checklist_nutrition_intake_id_fkey;
ALTER TABLE form_checklist_wound DROP CONSTRAINT IF EXISTS form_checklist_wound_intake_id_fk;
ALTER TABLE form_checklist_wound DROP CONSTRAINT IF EXISTS form_checklist_wound_intake_id_fkey;
ALTER TABLE form_checklist_fall DROP CONSTRAINT IF EXISTS form_checklist_fall_intake_id_fk;
ALTER TABLE form_checklist_fall DROP CONSTRAINT IF EXISTS form_checklist_fall_intake_id_fkey;
ALTER TABLE form_careplan_goal DROP CONSTRAINT IF EXISTS form_careplan_goal_intake_id_fk;
ALTER TABLE form_careplan_goal DROP CONSTRAINT IF EXISTS form_careplan_goal_intake_id_fkey;
ALTER TABLE form_checklist_teaching DROP CONSTRAINT IF EXISTS form_checklist_teaching_intake_id_fk;
ALTER TABLE form_checklist_teaching DROP CONSTRAINT IF EXISTS form_checklist_teaching_intake_id_fkey;
ALTER TABLE form_checklist_safety DROP CONSTRAINT IF EXISTS form_checklist_safety_intake_id_fk;
ALTER TABLE form_checklist_safety DROP CONSTRAINT IF EXISTS form_checklist_safety_intake_id_fkey;
ALTER TABLE form_checklist_wound_details DROP CONSTRAINT IF EXISTS form_checklist_wound_details_intake_id_fk;
ALTER TABLE form_checklist_wound_details DROP CONSTRAINT IF EXISTS form_checklist_wound_details_intake_id_fkey;
ALTER TABLE form_checklist_wound_note DROP CONSTRAINT IF EXISTS form_checklist_wound_note_intake_id_fk;
ALTER TABLE form_checklist_wound_note DROP CONSTRAINT IF EXISTS form_checklist_wound_note_intake_id_fkey;
ALTER TABLE form_encounter_antibiotic DROP CONSTRAINT IF EXISTS form_encounter_antibiotic_intake_id_fk;
ALTER TABLE form_encounter_antibiotic DROP CONSTRAINT IF EXISTS form_encounter_antibiotic_intake_id_fkey;
ALTER TABLE form_encounter_aat DROP CONSTRAINT IF EXISTS form_encounter_aat_intake_id_fk;
ALTER TABLE form_encounter_aat DROP CONSTRAINT IF EXISTS form_encounter_aat_intake_id_fkey;
ALTER TABLE form_drug_brand DROP CONSTRAINT IF EXISTS form_drug_brand_change_by_fk;
ALTER TABLE form_drug_brand DROP CONSTRAINT IF EXISTS form_drug_brand_change_by_fkey;
ALTER TABLE form_drug_brand DROP CONSTRAINT IF EXISTS form_drug_brand_created_by_fk;
ALTER TABLE form_drug_brand DROP CONSTRAINT IF EXISTS form_drug_brand_created_by_fkey;
ALTER TABLE form_drug_brand DROP CONSTRAINT IF EXISTS form_drug_brand_reviewed_by_fk;
ALTER TABLE form_drug_brand DROP CONSTRAINT IF EXISTS form_drug_brand_reviewed_by_fkey;
ALTER TABLE form_drug_brand DROP CONSTRAINT IF EXISTS form_drug_brand_therapy_filter_fk;
ALTER TABLE form_drug_brand DROP CONSTRAINT IF EXISTS form_drug_brand_therapy_filter_fkey;
ALTER TABLE form_drug_brand DROP CONSTRAINT IF EXISTS form_drug_brand_updated_by_fk;
ALTER TABLE form_drug_brand DROP CONSTRAINT IF EXISTS form_drug_brand_updated_by_fkey;
ALTER TABLE form_encounter_factor DROP CONSTRAINT IF EXISTS form_encounter_factor_intake_id_fk;
ALTER TABLE form_encounter_factor DROP CONSTRAINT IF EXISTS form_encounter_factor_intake_id_fkey;
ALTER TABLE form_encounter_lemtrada DROP CONSTRAINT IF EXISTS form_encounter_lemtrada_intake_id_fk;
ALTER TABLE form_encounter_lemtrada DROP CONSTRAINT IF EXISTS form_encounter_lemtrada_intake_id_fkey;
ALTER TABLE form_encounter_radicava DROP CONSTRAINT IF EXISTS form_encounter_radicava_intake_id_fk;
ALTER TABLE form_encounter_radicava DROP CONSTRAINT IF EXISTS form_encounter_radicava_intake_id_fkey;
ALTER TABLE form_ongoing_ocrevus DROP CONSTRAINT IF EXISTS form_ongoing_ocrevus_intake_id_fk;
ALTER TABLE form_ongoing_ocrevus DROP CONSTRAINT IF EXISTS form_ongoing_ocrevus_intake_id_fkey;
ALTER TABLE form_encounter_ros DROP CONSTRAINT IF EXISTS form_encounter_ros_intake_id_fk;
ALTER TABLE form_encounter_ros DROP CONSTRAINT IF EXISTS form_encounter_ros_intake_id_fkey;
ALTER TABLE form_encounter_tnf DROP CONSTRAINT IF EXISTS form_encounter_tnf_intake_id_fk;
ALTER TABLE form_encounter_tnf DROP CONSTRAINT IF EXISTS form_encounter_tnf_intake_id_fkey;
ALTER TABLE form_encounter_vital DROP CONSTRAINT IF EXISTS form_encounter_vital_intake_id_fk;
ALTER TABLE form_encounter_vital DROP CONSTRAINT IF EXISTS form_encounter_vital_intake_id_fkey;
ALTER TABLE form_encounter_tpn DROP CONSTRAINT IF EXISTS form_encounter_tpn_intake_id_fk;
ALTER TABLE form_encounter_tpn DROP CONSTRAINT IF EXISTS form_encounter_tpn_intake_id_fkey;
ALTER TABLE form_ongoing_aat DROP CONSTRAINT IF EXISTS form_ongoing_aat_intake_id_fk;
ALTER TABLE form_ongoing_aat DROP CONSTRAINT IF EXISTS form_ongoing_aat_intake_id_fkey;
ALTER TABLE form_ongoing_radicava DROP CONSTRAINT IF EXISTS form_ongoing_radicava_intake_id_fk;
ALTER TABLE form_ongoing_radicava DROP CONSTRAINT IF EXISTS form_ongoing_radicava_intake_id_fkey;
ALTER TABLE form_factor_bleed DROP CONSTRAINT IF EXISTS form_factor_bleed_intake_id_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_change_by_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_change_by_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_created_by_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_created_by_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_drug_brand_1_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_drug_brand_1_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_drug_brand_2_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_drug_brand_2_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_drug_brand_3_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_drug_brand_3_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_drug_brand_4_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_drug_brand_4_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_drug_brand_5_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_drug_brand_5_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_1_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_1_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_2_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_2_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_3_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_3_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_4_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_4_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_5_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_5_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_by_1_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_by_1_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_by_2_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_by_2_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_by_3_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_by_3_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_by_4_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_by_4_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_by_5_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_dx_by_5_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_hospital_id_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_hospital_id_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_patient_id_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_patient_id_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_pharmacist_id_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_pharmacist_id_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_physician_1_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_physician_1_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_physician_2_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_physician_2_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_physician_3_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_physician_3_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_physician_4_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_physician_4_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_physician_5_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_physician_5_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_primary_nurse_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_primary_nurse_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_pt_advocate_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_pt_advocate_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_referrer_name_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_referrer_name_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_reviewed_by_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_reviewed_by_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_tdi_by_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_tdi_by_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_therapy_1_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_therapy_1_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_therapy_2_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_therapy_2_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_therapy_3_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_therapy_3_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_therapy_4_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_therapy_4_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_therapy_5_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_therapy_5_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_treatment_state_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_treatment_state_fkey;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_updated_by_fk;
ALTER TABLE form_intake DROP CONSTRAINT IF EXISTS form_intake_updated_by_fkey;
ALTER TABLE form_medicare_hit DROP CONSTRAINT IF EXISTS form_medicare_hit_intake_id_fk;
ALTER TABLE form_medicare_hit DROP CONSTRAINT IF EXISTS form_medicare_hit_intake_id_fkey;
ALTER TABLE form_medicare_hit DROP CONSTRAINT IF EXISTS form_medicare_hit_patient_state_fk;
ALTER TABLE form_medicare_hit DROP CONSTRAINT IF EXISTS form_medicare_hit_patient_state_fkey;
ALTER TABLE form_medicare_hit DROP CONSTRAINT IF EXISTS form_medicare_hit_primary_dx_fk;
ALTER TABLE form_medicare_hit DROP CONSTRAINT IF EXISTS form_medicare_hit_primary_dx_fkey;
ALTER TABLE form_medicare_hit DROP CONSTRAINT IF EXISTS form_medicare_hit_secondary_dx_fk;
ALTER TABLE form_medicare_hit DROP CONSTRAINT IF EXISTS form_medicare_hit_secondary_dx_fkey;
ALTER TABLE form_patient_external_order DROP CONSTRAINT IF EXISTS form_patient_external_order_change_by_fk;
ALTER TABLE form_patient_external_order DROP CONSTRAINT IF EXISTS form_patient_external_order_change_by_fkey;
ALTER TABLE form_patient_external_order DROP CONSTRAINT IF EXISTS form_patient_external_order_created_by_fk;
ALTER TABLE form_patient_external_order DROP CONSTRAINT IF EXISTS form_patient_external_order_created_by_fkey;
ALTER TABLE form_patient_external_order DROP CONSTRAINT IF EXISTS form_patient_external_order_patient_id_fk;
ALTER TABLE form_patient_external_order DROP CONSTRAINT IF EXISTS form_patient_external_order_patient_id_fkey;
ALTER TABLE form_patient_external_order DROP CONSTRAINT IF EXISTS form_patient_external_order_reviewed_by_fk;
ALTER TABLE form_patient_external_order DROP CONSTRAINT IF EXISTS form_patient_external_order_reviewed_by_fkey;
ALTER TABLE form_patient_external_order DROP CONSTRAINT IF EXISTS form_patient_external_order_updated_by_fk;
ALTER TABLE form_patient_external_order DROP CONSTRAINT IF EXISTS form_patient_external_order_updated_by_fkey;
ALTER TABLE form_letter_template DROP CONSTRAINT IF EXISTS form_letter_template_therapy_fk;
ALTER TABLE form_letter_template DROP CONSTRAINT IF EXISTS form_letter_template_therapy_fkey;
ALTER TABLE form_manufacturer DROP CONSTRAINT IF EXISTS form_manufacturer_change_by_fk;
ALTER TABLE form_manufacturer DROP CONSTRAINT IF EXISTS form_manufacturer_change_by_fkey;
ALTER TABLE form_manufacturer DROP CONSTRAINT IF EXISTS form_manufacturer_created_by_fk;
ALTER TABLE form_manufacturer DROP CONSTRAINT IF EXISTS form_manufacturer_created_by_fkey;
ALTER TABLE form_manufacturer DROP CONSTRAINT IF EXISTS form_manufacturer_reviewed_by_fk;
ALTER TABLE form_manufacturer DROP CONSTRAINT IF EXISTS form_manufacturer_reviewed_by_fkey;
ALTER TABLE form_manufacturer DROP CONSTRAINT IF EXISTS form_manufacturer_updated_by_fk;
ALTER TABLE form_manufacturer DROP CONSTRAINT IF EXISTS form_manufacturer_updated_by_fkey;
ALTER TABLE form_ongoing_antibiotic DROP CONSTRAINT IF EXISTS form_ongoing_antibiotic_aminoglycoside_drug_brand_fk;
ALTER TABLE form_ongoing_antibiotic DROP CONSTRAINT IF EXISTS form_ongoing_antibiotic_aminoglycoside_drug_brand_fkey;
ALTER TABLE form_ongoing_antibiotic DROP CONSTRAINT IF EXISTS form_ongoing_antibiotic_intake_id_fk;
ALTER TABLE form_ongoing_antibiotic DROP CONSTRAINT IF EXISTS form_ongoing_antibiotic_intake_id_fkey;
ALTER TABLE form_ongoing_chemotherapy DROP CONSTRAINT IF EXISTS form_ongoing_chemotherapy_intake_id_fk;
ALTER TABLE form_ongoing_chemotherapy DROP CONSTRAINT IF EXISTS form_ongoing_chemotherapy_intake_id_fkey;
ALTER TABLE form_ongoing_hiv DROP CONSTRAINT IF EXISTS form_ongoing_hiv_intake_id_fk;
ALTER TABLE form_ongoing_hiv DROP CONSTRAINT IF EXISTS form_ongoing_hiv_intake_id_fkey;
ALTER TABLE form_ongoing_hepb DROP CONSTRAINT IF EXISTS form_ongoing_hepb_intake_id_fk;
ALTER TABLE form_ongoing_hepb DROP CONSTRAINT IF EXISTS form_ongoing_hepb_intake_id_fkey;
ALTER TABLE form_ongoing_dupixent DROP CONSTRAINT IF EXISTS form_ongoing_dupixent_intake_id_fk;
ALTER TABLE form_ongoing_dupixent DROP CONSTRAINT IF EXISTS form_ongoing_dupixent_intake_id_fkey;
ALTER TABLE form_ongoing_hepc DROP CONSTRAINT IF EXISTS form_ongoing_hepc_intake_id_fk;
ALTER TABLE form_ongoing_hepc DROP CONSTRAINT IF EXISTS form_ongoing_hepc_intake_id_fkey;
ALTER TABLE form_ongoing_factor DROP CONSTRAINT IF EXISTS form_ongoing_factor_intake_id_fk;
ALTER TABLE form_ongoing_factor DROP CONSTRAINT IF EXISTS form_ongoing_factor_intake_id_fkey;
ALTER TABLE form_ongoing_inotrope DROP CONSTRAINT IF EXISTS form_ongoing_inotrope_intake_id_fk;
ALTER TABLE form_ongoing_inotrope DROP CONSTRAINT IF EXISTS form_ongoing_inotrope_intake_id_fkey;
ALTER TABLE form_ongoing_krystexxa DROP CONSTRAINT IF EXISTS form_ongoing_krystexxa_intake_id_fk;
ALTER TABLE form_ongoing_krystexxa DROP CONSTRAINT IF EXISTS form_ongoing_krystexxa_intake_id_fkey;
ALTER TABLE form_ongoing_psoriasis DROP CONSTRAINT IF EXISTS form_ongoing_psoriasis_intake_id_fk;
ALTER TABLE form_ongoing_psoriasis DROP CONSTRAINT IF EXISTS form_ongoing_psoriasis_intake_id_fkey;
ALTER TABLE form_ongoing_ms DROP CONSTRAINT IF EXISTS form_ongoing_ms_intake_id_fk;
ALTER TABLE form_ongoing_ms DROP CONSTRAINT IF EXISTS form_ongoing_ms_intake_id_fkey;
ALTER TABLE form_ongoing_vyvgart DROP CONSTRAINT IF EXISTS form_ongoing_vyvgart_intake_id_fkey;
ALTER TABLE form_ongoing_soliris DROP CONSTRAINT IF EXISTS form_ongoing_soliris_intake_id_fk;
ALTER TABLE form_ongoing_soliris DROP CONSTRAINT IF EXISTS form_ongoing_soliris_intake_id_fkey;
ALTER TABLE form_ongoing_tpn DROP CONSTRAINT IF EXISTS form_ongoing_tpn_intake_id_fk;
ALTER TABLE form_ongoing_tpn DROP CONSTRAINT IF EXISTS form_ongoing_tpn_intake_id_fkey;
ALTER TABLE form_patient_diagnosis DROP CONSTRAINT IF EXISTS form_patient_diagnosis_dx_fkey;
ALTER TABLE form_patient_copay_program DROP CONSTRAINT IF EXISTS form_patient_copay_program_external_order_fk;
ALTER TABLE form_patient_copay_program DROP CONSTRAINT IF EXISTS form_patient_copay_program_external_order_fkey;
ALTER TABLE form_patient_copay_program DROP CONSTRAINT IF EXISTS form_patient_copay_program_manufacturer_fk;
ALTER TABLE form_patient_copay_program DROP CONSTRAINT IF EXISTS form_patient_copay_program_manufacturer_fkey;
ALTER TABLE form_ongoing_tysabri DROP CONSTRAINT IF EXISTS form_ongoing_tysabri_intake_id_fk;
ALTER TABLE form_ongoing_tysabri DROP CONSTRAINT IF EXISTS form_ongoing_tysabri_intake_id_fkey;
ALTER TABLE form_inventory DROP CONSTRAINT IF EXISTS form_inventory_dispense_unit_fk;
ALTER TABLE form_inventory DROP CONSTRAINT IF EXISTS form_inventory_dispense_unit_fkey;
ALTER TABLE form_inventory DROP CONSTRAINT IF EXISTS form_inventory_strength_unit_fk;
ALTER TABLE form_inventory DROP CONSTRAINT IF EXISTS form_inventory_strength_unit_fkey;
ALTER TABLE form_inventory DROP CONSTRAINT IF EXISTS form_inventory_uom_fk;
ALTER TABLE form_inventory DROP CONSTRAINT IF EXISTS form_inventory_uom_fkey;
ALTER TABLE form_patient_medication DROP CONSTRAINT IF EXISTS form_patient_medication_assoc_cond_fk;
ALTER TABLE form_patient_medication DROP CONSTRAINT IF EXISTS form_patient_medication_assoc_cond_fkey;
ALTER TABLE form_patient_medication DROP CONSTRAINT IF EXISTS form_patient_medication_medication_name_fk;
ALTER TABLE form_patient_medication DROP CONSTRAINT IF EXISTS form_patient_medication_medication_name_fkey;
ALTER TABLE form_therapy_map DROP CONSTRAINT IF EXISTS form_therapy_map_therapy_fk;
ALTER TABLE form_therapy_map DROP CONSTRAINT IF EXISTS form_therapy_map_therapy_fkey;
ALTER TABLE form_agency DROP CONSTRAINT IF EXISTS form_agency_calendar_id_fk;
ALTER TABLE form_agency DROP CONSTRAINT IF EXISTS form_agency_change_by_fk;
ALTER TABLE form_agency DROP CONSTRAINT IF EXISTS form_agency_created_by_fk;
ALTER TABLE form_agency DROP CONSTRAINT IF EXISTS form_agency_reviewed_by_fk;
ALTER TABLE form_agency DROP CONSTRAINT IF EXISTS form_agency_state_fk;
ALTER TABLE form_agency DROP CONSTRAINT IF EXISTS form_agency_updated_by_fk;
ALTER TABLE token_auth DROP CONSTRAINT IF EXISTS token_auth_user_id_fkey;
ALTER TABLE form_sec_assign DROP CONSTRAINT IF EXISTS form_sec_assign_agency_fk;
ALTER TABLE form_view_schedule DROP CONSTRAINT IF EXISTS form_view_schedule_therapy_1_fk;
ALTER TABLE form_schedule_event_series DROP CONSTRAINT IF EXISTS form_schedule_event_series_therapy_1_fk;
ALTER TABLE form_sales_prospect DROP CONSTRAINT IF EXISTS form_sales_prospect_state_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_compound_disp_unit_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_compound_dose_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_compound_route_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_compound_type_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_daw_qualifier_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_delay_reason_code_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_dose_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_eligibility_class_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_inter_auth_type_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_pregnancy_indicator_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_relationship_code_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_submission_class_code_1_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_submission_class_code_2_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_submission_class_code_3_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_submission_class_code_4_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_tax_basis_id_fk;
ALTER TABLE form_billing_claim DROP CONSTRAINT IF EXISTS form_billing_claim_uom_id_fk;

DROP TABLE IF EXISTS cookie_session;
DROP TABLE IF EXISTS form_agency;
DROP TABLE IF EXISTS form_allergen;
DROP TABLE IF EXISTS form_assessment_ad;
DROP TABLE IF EXISTS form_assessment_anemia;
DROP TABLE IF EXISTS form_assessment_antibiotic;
DROP TABLE IF EXISTS form_assessment_as;
DROP TABLE IF EXISTS form_assessment_biologics;
DROP TABLE IF EXISTS form_assessment_bleeding_disorder;
DROP TABLE IF EXISTS form_assessment_blood_thinners;
DROP TABLE IF EXISTS form_assessment_breast_cancer;
DROP TABLE IF EXISTS form_assessment_cancer;
DROP TABLE IF EXISTS form_assessment_cardiology;
DROP TABLE IF EXISTS form_assessment_catheter_care;
DROP TABLE IF EXISTS form_assessment_cd;
DROP TABLE IF EXISTS form_assessment_chelation;
DROP TABLE IF EXISTS form_assessment_cinqair;
DROP TABLE IF EXISTS form_assessment_coag;
DROP TABLE IF EXISTS form_assessment_cvid;
DROP TABLE IF EXISTS form_assessment_dietitian;
DROP TABLE IF EXISTS form_assessment_dietitian_tpn;
DROP TABLE IF EXISTS form_assessment_enteral;
DROP TABLE IF EXISTS form_assessment_enzyme;
DROP TABLE IF EXISTS form_assessment_fh;
DROP TABLE IF EXISTS form_assessment_general;
DROP TABLE IF EXISTS form_assessment_gh;
DROP TABLE IF EXISTS form_assessment_hbig;
DROP TABLE IF EXISTS form_assessment_he;
DROP TABLE IF EXISTS form_assessment_hf;
DROP TABLE IF EXISTS form_assessment_hhome;
DROP TABLE IF EXISTS form_assessment_hs;
DROP TABLE IF EXISTS form_assessment_hydration;
DROP TABLE IF EXISTS form_assessment_hyperlipidemia;
DROP TABLE IF EXISTS form_assessment_ig;
DROP TABLE IF EXISTS form_assessment_igneuro;
DROP TABLE IF EXISTS form_assessment_immunotherapy;
DROP TABLE IF EXISTS form_assessment_intrathecal;
DROP TABLE IF EXISTS form_assessment_iron_overload;
DROP TABLE IF EXISTS form_assessment_iron_therapy;
DROP TABLE IF EXISTS form_assessment_ivig;
DROP TABLE IF EXISTS form_assessment_jia;
DROP TABLE IF EXISTS form_assessment_jra;
DROP TABLE IF EXISTS form_assessment_methyl;
DROP TABLE IF EXISTS form_assessment_nephro;
DROP TABLE IF EXISTS form_assessment_neutropenia;
DROP TABLE IF EXISTS form_assessment_nurse;
DROP TABLE IF EXISTS form_assessment_nursing;
DROP TABLE IF EXISTS form_assessment_oncology;
DROP TABLE IF EXISTS form_assessment_osteoarthritis;
DROP TABLE IF EXISTS form_assessment_osteoporosis;
DROP TABLE IF EXISTS form_assessment_other;
DROP TABLE IF EXISTS form_assessment_pah;
DROP TABLE IF EXISTS form_assessment_pain_management;
DROP TABLE IF EXISTS form_assessment_prostate_cancer;
DROP TABLE IF EXISTS form_assessment_psa;
DROP TABLE IF EXISTS form_assessment_pso;
DROP TABLE IF EXISTS form_assessment_scig;
DROP TABLE IF EXISTS form_assessment_skin_cancer;
DROP TABLE IF EXISTS form_assessment_tepezza;
DROP TABLE IF EXISTS form_assessment_thrombocytopenia;
DROP TABLE IF EXISTS form_assessment_transplant;
DROP TABLE IF EXISTS form_assessment_uc;
DROP TABLE IF EXISTS form_assessment_ultomiris;
DROP TABLE IF EXISTS form_assessment_uveitis;
DROP TABLE IF EXISTS form_assessment_vyepti;
DROP TABLE IF EXISTS form_assigned_assessment;
DROP TABLE IF EXISTS form_billing_claim_med_item;
DROP TABLE IF EXISTS form_billing_claim_ncpdp_add_chrg;
DROP TABLE IF EXISTS form_billing_claim_ncpdp_cob;
DROP TABLE IF EXISTS form_billing_claim_ncpdp_dx;
DROP TABLE IF EXISTS form_billing_claim_ncpdp_item;
DROP TABLE IF EXISTS form_billing_claim_ncpdp_measure;
DROP TABLE IF EXISTS form_billing_claim_ncpdp_partial;
DROP TABLE IF EXISTS form_billing_healthcare_provider_taxonomy;
DROP TABLE IF EXISTS form_billing_healthcare_taxonomy_group;
DROP TABLE IF EXISTS form_billing_other_provider_identifier;
DROP TABLE IF EXISTS form_careplan_pharmacist;
DROP TABLE IF EXISTS form_careplan_wt_label;
DROP TABLE IF EXISTS form_careplan_wt_lbl;
DROP TABLE IF EXISTS form_changeset;
DROP TABLE IF EXISTS form_channel;
DROP TABLE IF EXISTS form_channel_user;
DROP TABLE IF EXISTS form_communication;
DROP TABLE IF EXISTS form_communication_group;
DROP TABLE IF EXISTS form_communication_to;
DROP TABLE IF EXISTS form_company_configuration;
DROP TABLE IF EXISTS form_company_test;
DROP TABLE IF EXISTS form_complaint;
DROP TABLE IF EXISTS form_cpr_icd_master_list;
DROP TABLE IF EXISTS form_cpr_icd_patient;
DROP TABLE IF EXISTS form_cprexport;
DROP TABLE IF EXISTS form_diagnosis;
DROP TABLE IF EXISTS form_disease;
DROP TABLE IF EXISTS form_drug_brand;
DROP TABLE IF EXISTS form_ebridged_error;
DROP TABLE IF EXISTS form_education;
DROP TABLE IF EXISTS form_educationbundle;
DROP TABLE IF EXISTS form_encounter_aat_admin;
DROP TABLE IF EXISTS form_encounter_ad;
DROP TABLE IF EXISTS form_encounter_ad_admin;
DROP TABLE IF EXISTS form_encounter_aghda;
DROP TABLE IF EXISTS form_encounter_alsfrs;
DROP TABLE IF EXISTS form_encounter_anemia;
DROP TABLE IF EXISTS form_encounter_anemia_admin;
DROP TABLE IF EXISTS form_encounter_antibiotic_admin;
DROP TABLE IF EXISTS form_encounter_as;
DROP TABLE IF EXISTS form_encounter_as_admin;
DROP TABLE IF EXISTS form_encounter_basdai;
DROP TABLE IF EXISTS form_encounter_biologics;
DROP TABLE IF EXISTS form_encounter_biologics_admin;
DROP TABLE IF EXISTS form_encounter_bisphosphonates;
DROP TABLE IF EXISTS form_encounter_bisphosphonates_admin;
DROP TABLE IF EXISTS form_encounter_bleeding_disorder;
DROP TABLE IF EXISTS form_encounter_bleeding_disorder_admin;
DROP TABLE IF EXISTS form_encounter_blood_thinners;
DROP TABLE IF EXISTS form_encounter_blood_thinners_admin;
DROP TABLE IF EXISTS form_encounter_braden;
DROP TABLE IF EXISTS form_encounter_breast_cancer;
DROP TABLE IF EXISTS form_encounter_breast_cancer_admin;
DROP TABLE IF EXISTS form_encounter_cancer;
DROP TABLE IF EXISTS form_encounter_cancer_admin;
DROP TABLE IF EXISTS form_encounter_catheter_care;
DROP TABLE IF EXISTS form_encounter_catheter_care_admin;
DROP TABLE IF EXISTS form_encounter_cd;
DROP TABLE IF EXISTS form_encounter_cd_admin;
DROP TABLE IF EXISTS form_encounter_chelation;
DROP TABLE IF EXISTS form_encounter_chelation_admin;
DROP TABLE IF EXISTS form_encounter_chemotherapy;
DROP TABLE IF EXISTS form_encounter_chemotherapy_admin;
DROP TABLE IF EXISTS form_encounter_cinqair;
DROP TABLE IF EXISTS form_encounter_cinqair_admin;
DROP TABLE IF EXISTS form_encounter_coag;
DROP TABLE IF EXISTS form_encounter_coag_admin;
DROP TABLE IF EXISTS form_encounter_cvid;
DROP TABLE IF EXISTS form_encounter_cvid_admin;
DROP TABLE IF EXISTS form_encounter_dlqi;
DROP TABLE IF EXISTS form_encounter_dupixent;
DROP TABLE IF EXISTS form_encounter_dupixent_admin;
DROP TABLE IF EXISTS form_encounter_edss;
DROP TABLE IF EXISTS form_encounter_enteral;
DROP TABLE IF EXISTS form_encounter_enteral_admin;
DROP TABLE IF EXISTS form_encounter_enzyme;
DROP TABLE IF EXISTS form_encounter_enzyme_admin;
DROP TABLE IF EXISTS form_encounter_epworth;
DROP TABLE IF EXISTS form_encounter_factor_admin;
DROP TABLE IF EXISTS form_encounter_fh;
DROP TABLE IF EXISTS form_encounter_fh_admin;
DROP TABLE IF EXISTS form_encounter_general;
DROP TABLE IF EXISTS form_encounter_general_admin;
DROP TABLE IF EXISTS form_encounter_gh;
DROP TABLE IF EXISTS form_encounter_gh_admin;
DROP TABLE IF EXISTS form_encounter_hat_qol;
DROP TABLE IF EXISTS form_encounter_hbi;
DROP TABLE IF EXISTS form_encounter_hbig;
DROP TABLE IF EXISTS form_encounter_hbig_admin;
DROP TABLE IF EXISTS form_encounter_hcv_symptoms;
DROP TABLE IF EXISTS form_encounter_he;
DROP TABLE IF EXISTS form_encounter_he_admin;
DROP TABLE IF EXISTS form_encounter_hepb;
DROP TABLE IF EXISTS form_encounter_hepb_admin;
DROP TABLE IF EXISTS form_encounter_hepc;
DROP TABLE IF EXISTS form_encounter_hepc_admin;
DROP TABLE IF EXISTS form_encounter_hf;
DROP TABLE IF EXISTS form_encounter_hf_admin;
DROP TABLE IF EXISTS form_encounter_hfqol;
DROP TABLE IF EXISTS form_encounter_hhome;
DROP TABLE IF EXISTS form_encounter_hhome_admin;
DROP TABLE IF EXISTS form_encounter_hiv;
DROP TABLE IF EXISTS form_encounter_hiv_admin;
DROP TABLE IF EXISTS form_encounter_hmq;
DROP TABLE IF EXISTS form_encounter_hpq;
DROP TABLE IF EXISTS form_encounter_hs;
DROP TABLE IF EXISTS form_encounter_hs_admin;
DROP TABLE IF EXISTS form_encounter_humira;
DROP TABLE IF EXISTS form_encounter_humira_admin;
DROP TABLE IF EXISTS form_encounter_hydration;
DROP TABLE IF EXISTS form_encounter_hydration_admin;
DROP TABLE IF EXISTS form_encounter_hyperlipidemia;
DROP TABLE IF EXISTS form_encounter_hyperlipidemia_admin;
DROP TABLE IF EXISTS form_encounter_igneuro;
DROP TABLE IF EXISTS form_encounter_igneuro_admin;
DROP TABLE IF EXISTS form_encounter_immunotherapy;
DROP TABLE IF EXISTS form_encounter_immunotherapy_admin;
DROP TABLE IF EXISTS form_encounter_incat;
DROP TABLE IF EXISTS form_encounter_inotrope;
DROP TABLE IF EXISTS form_encounter_inotrope_admin;
DROP TABLE IF EXISTS form_encounter_intrathecal;
DROP TABLE IF EXISTS form_encounter_intrathecal_admin;
DROP TABLE IF EXISTS form_encounter_iron_overload;
DROP TABLE IF EXISTS form_encounter_iron_overload_admin;
DROP TABLE IF EXISTS form_encounter_iron_therapy;
DROP TABLE IF EXISTS form_encounter_iron_therapy_admin;
DROP TABLE IF EXISTS form_encounter_ivig;
DROP TABLE IF EXISTS form_encounter_ivig_admin;
DROP TABLE IF EXISTS form_encounter_ivig_cidp;
DROP TABLE IF EXISTS form_encounter_ivig_immdef;
DROP TABLE IF EXISTS form_encounter_ivig_itp;
DROP TABLE IF EXISTS form_encounter_ivig_mmn;
DROP TABLE IF EXISTS form_encounter_ivig_ms;
DROP TABLE IF EXISTS form_encounter_ivig_mygrav;
DROP TABLE IF EXISTS form_encounter_ivig_myositis;
DROP TABLE IF EXISTS form_encounter_ivig_pemphigus;
DROP TABLE IF EXISTS form_encounter_ivig_stiffps;
DROP TABLE IF EXISTS form_encounter_jia;
DROP TABLE IF EXISTS form_encounter_jia_admin;
DROP TABLE IF EXISTS form_encounter_jra;
DROP TABLE IF EXISTS form_encounter_jra_admin;
DROP TABLE IF EXISTS form_encounter_krystexxa;
DROP TABLE IF EXISTS form_encounter_krystexxa_admin;
DROP TABLE IF EXISTS form_encounter_lemtrada_admin;
DROP TABLE IF EXISTS form_encounter_methyl;
DROP TABLE IF EXISTS form_encounter_methyl_admin;
DROP TABLE IF EXISTS form_encounter_mfis5;
DROP TABLE IF EXISTS form_encounter_mgadl;
DROP TABLE IF EXISTS form_encounter_mhaq;
DROP TABLE IF EXISTS form_encounter_mmas8;
DROP TABLE IF EXISTS form_encounter_monoclon;
DROP TABLE IF EXISTS form_encounter_monoclon_admin;
DROP TABLE IF EXISTS form_encounter_moqlq;
DROP TABLE IF EXISTS form_encounter_ms;
DROP TABLE IF EXISTS form_encounter_ms_admin;
DROP TABLE IF EXISTS form_encounter_nephro;
DROP TABLE IF EXISTS form_encounter_nephro_admin;
DROP TABLE IF EXISTS form_encounter_neutropenia;
DROP TABLE IF EXISTS form_encounter_neutropenia_admin;
DROP TABLE IF EXISTS form_encounter_nursing;
DROP TABLE IF EXISTS form_encounter_nursing_admin;
DROP TABLE IF EXISTS form_encounter_ocrevus;
DROP TABLE IF EXISTS form_encounter_ocrevus_admin;
DROP TABLE IF EXISTS form_encounter_osteoporosis;
DROP TABLE IF EXISTS form_encounter_osteoporosis_admin;
DROP TABLE IF EXISTS form_encounter_other;
DROP TABLE IF EXISTS form_encounter_other_admin;
DROP TABLE IF EXISTS form_encounter_padqol;
DROP TABLE IF EXISTS form_encounter_pah;
DROP TABLE IF EXISTS form_encounter_pah_admin;
DROP TABLE IF EXISTS form_encounter_pain_management;
DROP TABLE IF EXISTS form_encounter_pain_management_admin;
DROP TABLE IF EXISTS form_encounter_pas2;
DROP TABLE IF EXISTS form_encounter_pes;
DROP TABLE IF EXISTS form_encounter_phq;
DROP TABLE IF EXISTS form_encounter_poem;
DROP TABLE IF EXISTS form_encounter_prostate_cancer;
DROP TABLE IF EXISTS form_encounter_prostate_cancer_admin;
DROP TABLE IF EXISTS form_encounter_psa;
DROP TABLE IF EXISTS form_encounter_psa_admin;
DROP TABLE IF EXISTS form_encounter_pso;
DROP TABLE IF EXISTS form_encounter_pso_admin;
DROP TABLE IF EXISTS form_encounter_psoriasis;
DROP TABLE IF EXISTS form_encounter_psoriasis_admin;
DROP TABLE IF EXISTS form_encounter_qlsh;
DROP TABLE IF EXISTS form_encounter_qol;
DROP TABLE IF EXISTS form_encounter_ra;
DROP TABLE IF EXISTS form_encounter_ra_admin;
DROP TABLE IF EXISTS form_encounter_radai;
DROP TABLE IF EXISTS form_encounter_radicava_admin;
DROP TABLE IF EXISTS form_encounter_rituxan;
DROP TABLE IF EXISTS form_encounter_rituxan_admin;
DROP TABLE IF EXISTS form_encounter_rods;
DROP TABLE IF EXISTS form_encounter_scig;
DROP TABLE IF EXISTS form_encounter_scig_admin;
DROP TABLE IF EXISTS form_encounter_sf12v2;
DROP TABLE IF EXISTS form_encounter_sibdq;
DROP TABLE IF EXISTS form_encounter_skin_cancer;
DROP TABLE IF EXISTS form_encounter_skin_cancer_admin;
DROP TABLE IF EXISTS form_encounter_soliris;
DROP TABLE IF EXISTS form_encounter_soliris_admin;
DROP TABLE IF EXISTS form_encounter_steroid;
DROP TABLE IF EXISTS form_encounter_steroid_admin;
DROP TABLE IF EXISTS form_encounter_tepezza;
DROP TABLE IF EXISTS form_encounter_tepezza_admin;
DROP TABLE IF EXISTS form_encounter_thrombocytopenia;
DROP TABLE IF EXISTS form_encounter_thrombocytopenia_admin;
DROP TABLE IF EXISTS form_encounter_tnf_admin;
DROP TABLE IF EXISTS form_encounter_tnf_crohns;
DROP TABLE IF EXISTS form_encounter_tnf_parthritis;
DROP TABLE IF EXISTS form_encounter_tnf_psoriasis;
DROP TABLE IF EXISTS form_encounter_tnf_rheumatology;
DROP TABLE IF EXISTS form_encounter_tnf_ulccolitis;
DROP TABLE IF EXISTS form_encounter_tpn_admin;
DROP TABLE IF EXISTS form_encounter_transplant;
DROP TABLE IF EXISTS form_encounter_transplant_admin;
DROP TABLE IF EXISTS form_encounter_tysabri;
DROP TABLE IF EXISTS form_encounter_tysabri_admin;
DROP TABLE IF EXISTS form_encounter_uc;
DROP TABLE IF EXISTS form_encounter_uc_admin;
DROP TABLE IF EXISTS form_encounter_ultomiris;
DROP TABLE IF EXISTS form_encounter_ultomiris_admin;
DROP TABLE IF EXISTS form_encounter_uveitis;
DROP TABLE IF EXISTS form_encounter_uveitis_admin;
DROP TABLE IF EXISTS form_encounter_vyepti;
DROP TABLE IF EXISTS form_encounter_vyepti_admin;
DROP TABLE IF EXISTS form_encounter_vyvgart;
DROP TABLE IF EXISTS form_encounter_vyvgart_admin;
DROP TABLE IF EXISTS form_encounter_wat;
DROP TABLE IF EXISTS form_encounter_wpai;
DROP TABLE IF EXISTS form_ext_cpr_order;
DROP TABLE IF EXISTS form_flag;
DROP TABLE IF EXISTS form_followupprogram;
DROP TABLE IF EXISTS form_immunization;
DROP TABLE IF EXISTS form_incident;
DROP TABLE IF EXISTS form_insurance;
DROP TABLE IF EXISTS form_intake;
DROP TABLE IF EXISTS form_intervention;
DROP TABLE IF EXISTS form_intervention_type;
DROP TABLE IF EXISTS form_lab;
DROP TABLE IF EXISTS form_list_billing_dispensing_status;
DROP TABLE IF EXISTS form_list_billing_dose_indicator;
DROP TABLE IF EXISTS form_list_billing_uom_code;
DROP TABLE IF EXISTS form_list_compound_form;
DROP TABLE IF EXISTS form_list_compound_route;
DROP TABLE IF EXISTS form_list_compound_type;
DROP TABLE IF EXISTS form_list_compound_unit;
DROP TABLE IF EXISTS form_list_coverage_relationship;
DROP TABLE IF EXISTS form_list_daw_qualifier;
DROP TABLE IF EXISTS form_list_eligibility_class_code;
DROP TABLE IF EXISTS form_list_healthcare_provider_taxonomy;
DROP TABLE IF EXISTS form_list_healthcare_taxonomy_group;
DROP TABLE IF EXISTS form_list_inter_auth_type;
DROP TABLE IF EXISTS form_list_ncpdp_add_qualifier;
DROP TABLE IF EXISTS form_list_ncpdp_benefit_qual;
DROP TABLE IF EXISTS form_list_ncpdp_conflict_type;
DROP TABLE IF EXISTS form_list_ncpdp_cost_basis;
DROP TABLE IF EXISTS form_list_ncpdp_coupon_type;
DROP TABLE IF EXISTS form_list_ncpdp_delay_reason;
DROP TABLE IF EXISTS form_list_ncpdp_dx_qualifier;
DROP TABLE IF EXISTS form_list_ncpdp_effort;
DROP TABLE IF EXISTS form_list_ncpdp_inerv_outcome;
DROP TABLE IF EXISTS form_list_ncpdp_interv_type;
DROP TABLE IF EXISTS form_list_ncpdp_los;
DROP TABLE IF EXISTS form_list_ncpdp_measure;
DROP TABLE IF EXISTS form_list_ncpdp_measure_unit;
DROP TABLE IF EXISTS form_list_ncpdp_payer_qualifier;
DROP TABLE IF EXISTS form_list_ncpdp_payer_type;
DROP TABLE IF EXISTS form_list_other_provider_identifier;
DROP TABLE IF EXISTS form_list_pregnancy_indicator;
DROP TABLE IF EXISTS form_list_submission_class_code;
DROP TABLE IF EXISTS form_list_tax_basis;
DROP TABLE IF EXISTS form_manufacturer;
DROP TABLE IF EXISTS form_medication;
DROP TABLE IF EXISTS form_medication_diagnosis;
DROP TABLE IF EXISTS form_ndc_code;
DROP TABLE IF EXISTS form_npi_delegate;
DROP TABLE IF EXISTS form_number_series;
DROP TABLE IF EXISTS form_ongoing_ad;
DROP TABLE IF EXISTS form_ongoing_anemia;
DROP TABLE IF EXISTS form_ongoing_as;
DROP TABLE IF EXISTS form_ongoing_biologics;
DROP TABLE IF EXISTS form_ongoing_bisphosphonates;
DROP TABLE IF EXISTS form_ongoing_bleeding_disorder;
DROP TABLE IF EXISTS form_ongoing_blood_thinners;
DROP TABLE IF EXISTS form_ongoing_breast_cancer;
DROP TABLE IF EXISTS form_ongoing_cancer;
DROP TABLE IF EXISTS form_ongoing_cardiology;
DROP TABLE IF EXISTS form_ongoing_catheter_care;
DROP TABLE IF EXISTS form_ongoing_cd;
DROP TABLE IF EXISTS form_ongoing_chelation;
DROP TABLE IF EXISTS form_ongoing_cinqair;
DROP TABLE IF EXISTS form_ongoing_coag;
DROP TABLE IF EXISTS form_ongoing_cvid;
DROP TABLE IF EXISTS form_ongoing_enteral;
DROP TABLE IF EXISTS form_ongoing_enzyme;
DROP TABLE IF EXISTS form_ongoing_fh;
DROP TABLE IF EXISTS form_ongoing_general;
DROP TABLE IF EXISTS form_ongoing_gh;
DROP TABLE IF EXISTS form_ongoing_hbig;
DROP TABLE IF EXISTS form_ongoing_he;
DROP TABLE IF EXISTS form_ongoing_hf;
DROP TABLE IF EXISTS form_ongoing_hhome;
DROP TABLE IF EXISTS form_ongoing_hs;
DROP TABLE IF EXISTS form_ongoing_humira;
DROP TABLE IF EXISTS form_ongoing_hydration;
DROP TABLE IF EXISTS form_ongoing_hyperlipidemia;
DROP TABLE IF EXISTS form_ongoing_igneuro;
DROP TABLE IF EXISTS form_ongoing_immunotherapy;
DROP TABLE IF EXISTS form_ongoing_intrathecal;
DROP TABLE IF EXISTS form_ongoing_iron_overload;
DROP TABLE IF EXISTS form_ongoing_iron_therapy;
DROP TABLE IF EXISTS form_ongoing_ivig;
DROP TABLE IF EXISTS form_ongoing_jia;
DROP TABLE IF EXISTS form_ongoing_jra;
DROP TABLE IF EXISTS form_ongoing_lemtrada;
DROP TABLE IF EXISTS form_ongoing_methyl;
DROP TABLE IF EXISTS form_ongoing_nephro;
DROP TABLE IF EXISTS form_ongoing_neutropenia;
DROP TABLE IF EXISTS form_ongoing_nursing;
DROP TABLE IF EXISTS form_ongoing_oncology;
DROP TABLE IF EXISTS form_ongoing_osteoarthritis;
DROP TABLE IF EXISTS form_ongoing_osteoporosis;
DROP TABLE IF EXISTS form_ongoing_other;
DROP TABLE IF EXISTS form_ongoing_pah;
DROP TABLE IF EXISTS form_ongoing_pain_management;
DROP TABLE IF EXISTS form_ongoing_prostate_cancer;
DROP TABLE IF EXISTS form_ongoing_psa;
DROP TABLE IF EXISTS form_ongoing_pso;
DROP TABLE IF EXISTS form_ongoing_ra;
DROP TABLE IF EXISTS form_ongoing_rituxan;
DROP TABLE IF EXISTS form_ongoing_scig;
DROP TABLE IF EXISTS form_ongoing_skin_cancer;
DROP TABLE IF EXISTS form_ongoing_steroid;
DROP TABLE IF EXISTS form_ongoing_tepezza;
DROP TABLE IF EXISTS form_ongoing_thrombocytopenia;
DROP TABLE IF EXISTS form_ongoing_tnf;
DROP TABLE IF EXISTS form_ongoing_transplant;
DROP TABLE IF EXISTS form_ongoing_uc;
DROP TABLE IF EXISTS form_ongoing_ultomiris;
DROP TABLE IF EXISTS form_ongoing_uveitis;
DROP TABLE IF EXISTS form_ongoing_vyepti;
DROP TABLE IF EXISTS form_outreach_log;
DROP TABLE IF EXISTS form_pat_sch_selfrp_rpt_appt;
DROP TABLE IF EXISTS form_patient_adherence;
DROP TABLE IF EXISTS form_patient_benefits_investigation;
DROP TABLE IF EXISTS form_patient_call;
DROP TABLE IF EXISTS form_patient_catheter;
DROP TABLE IF EXISTS form_patient_dispense;
DROP TABLE IF EXISTS form_patient_dispense_item;
DROP TABLE IF EXISTS form_patient_education;
DROP TABLE IF EXISTS form_patient_external_dispense;
DROP TABLE IF EXISTS form_patient_external_documents;
DROP TABLE IF EXISTS form_patient_external_invoice;
DROP TABLE IF EXISTS form_patient_external_order;
DROP TABLE IF EXISTS form_patient_history;
DROP TABLE IF EXISTS form_patient_immunization;
DROP TABLE IF EXISTS form_patient_lab_draw;
DROP TABLE IF EXISTS form_patient_note_template;
DROP TABLE IF EXISTS form_patient_pending_payment;
DROP TABLE IF EXISTS form_patient_physician;
DROP TABLE IF EXISTS form_patient_referral_call;
DROP TABLE IF EXISTS form_patient_sch_lab_rpt_appt;
DROP TABLE IF EXISTS form_patient_schedule_encounter;
DROP TABLE IF EXISTS form_patient_schedule_lab;
DROP TABLE IF EXISTS form_patient_schedule_selfreport;
DROP TABLE IF EXISTS form_patient_schedule_task;
DROP TABLE IF EXISTS form_patient_status_history;
DROP TABLE IF EXISTS form_patient_substatus;
DROP TABLE IF EXISTS form_patient_supply;
DROP TABLE IF EXISTS form_patient_supply_factor;
DROP TABLE IF EXISTS form_physician_order;
DROP TABLE IF EXISTS form_portal_attachment;
DROP TABLE IF EXISTS form_premedication;
DROP TABLE IF EXISTS form_previous_treatment;
DROP TABLE IF EXISTS form_private_group;
DROP TABLE IF EXISTS form_problem;
DROP TABLE IF EXISTS form_problem_diagnosis;
DROP TABLE IF EXISTS form_query_module;
DROP TABLE IF EXISTS form_questionnaire;
DROP TABLE IF EXISTS form_reaction;
DROP TABLE IF EXISTS form_referral;
DROP TABLE IF EXISTS form_rxcode_price;
DROP TABLE IF EXISTS form_sales_entity_link;
DROP TABLE IF EXISTS form_salesaccount;
DROP TABLE IF EXISTS form_salesaccount_attachment;
DROP TABLE IF EXISTS form_salesaccount_contact;
DROP TABLE IF EXISTS form_salesaccount_physician;
DROP TABLE IF EXISTS form_selfreport;
DROP TABLE IF EXISTS form_selfreport_aat;
DROP TABLE IF EXISTS form_selfreport_ad;
DROP TABLE IF EXISTS form_selfreport_anemia;
DROP TABLE IF EXISTS form_selfreport_antibiotic;
DROP TABLE IF EXISTS form_selfreport_as;
DROP TABLE IF EXISTS form_selfreport_biologics;
DROP TABLE IF EXISTS form_selfreport_bisphosphonates;
DROP TABLE IF EXISTS form_selfreport_bleeding_disorder;
DROP TABLE IF EXISTS form_selfreport_blood_thinners;
DROP TABLE IF EXISTS form_selfreport_breast_cancer;
DROP TABLE IF EXISTS form_selfreport_cancer;
DROP TABLE IF EXISTS form_selfreport_catheter_care;
DROP TABLE IF EXISTS form_selfreport_cd;
DROP TABLE IF EXISTS form_selfreport_chelation;
DROP TABLE IF EXISTS form_selfreport_chemotherapy;
DROP TABLE IF EXISTS form_selfreport_cinqair;
DROP TABLE IF EXISTS form_selfreport_cvid;
DROP TABLE IF EXISTS form_selfreport_dupixent;
DROP TABLE IF EXISTS form_selfreport_enteral;
DROP TABLE IF EXISTS form_selfreport_enzyme;
DROP TABLE IF EXISTS form_selfreport_factor;
DROP TABLE IF EXISTS form_selfreport_fh;
DROP TABLE IF EXISTS form_selfreport_general;
DROP TABLE IF EXISTS form_selfreport_gh;
DROP TABLE IF EXISTS form_selfreport_hbig;
DROP TABLE IF EXISTS form_selfreport_he;
DROP TABLE IF EXISTS form_selfreport_hepb;
DROP TABLE IF EXISTS form_selfreport_hepc;
DROP TABLE IF EXISTS form_selfreport_hf;
DROP TABLE IF EXISTS form_selfreport_hiv;
DROP TABLE IF EXISTS form_selfreport_hs;
DROP TABLE IF EXISTS form_selfreport_humira;
DROP TABLE IF EXISTS form_selfreport_hydration;
DROP TABLE IF EXISTS form_selfreport_hyperlipidemia;
DROP TABLE IF EXISTS form_selfreport_igneuro;
DROP TABLE IF EXISTS form_selfreport_immunotherapy;
DROP TABLE IF EXISTS form_selfreport_inotrope;
DROP TABLE IF EXISTS form_selfreport_intrathecal;
DROP TABLE IF EXISTS form_selfreport_iron_overload;
DROP TABLE IF EXISTS form_selfreport_iron_therapy;
DROP TABLE IF EXISTS form_selfreport_ivig;
DROP TABLE IF EXISTS form_selfreport_jia;
DROP TABLE IF EXISTS form_selfreport_jra;
DROP TABLE IF EXISTS form_selfreport_krystexxa;
DROP TABLE IF EXISTS form_selfreport_methyl;
DROP TABLE IF EXISTS form_selfreport_ms;
DROP TABLE IF EXISTS form_selfreport_nephro;
DROP TABLE IF EXISTS form_selfreport_neutropenia;
DROP TABLE IF EXISTS form_selfreport_nursing;
DROP TABLE IF EXISTS form_selfreport_osteoporosis;
DROP TABLE IF EXISTS form_selfreport_other;
DROP TABLE IF EXISTS form_selfreport_pah;
DROP TABLE IF EXISTS form_selfreport_pain_management;
DROP TABLE IF EXISTS form_selfreport_prostate_cancer;
DROP TABLE IF EXISTS form_selfreport_psa;
DROP TABLE IF EXISTS form_selfreport_pso;
DROP TABLE IF EXISTS form_selfreport_psoriasis;
DROP TABLE IF EXISTS form_selfreport_ra;
DROP TABLE IF EXISTS form_selfreport_radicava;
DROP TABLE IF EXISTS form_selfreport_rituxan;
DROP TABLE IF EXISTS form_selfreport_skin_cancer;
DROP TABLE IF EXISTS form_selfreport_soliris;
DROP TABLE IF EXISTS form_selfreport_steroid;
DROP TABLE IF EXISTS form_selfreport_tepezza;
DROP TABLE IF EXISTS form_selfreport_thrombocytopenia;
DROP TABLE IF EXISTS form_selfreport_tnf;
DROP TABLE IF EXISTS form_selfreport_tnf_crohns;
DROP TABLE IF EXISTS form_selfreport_tnf_parthritis;
DROP TABLE IF EXISTS form_selfreport_tnf_psoriasis;
DROP TABLE IF EXISTS form_selfreport_tnf_rheumatology;
DROP TABLE IF EXISTS form_selfreport_tnf_ulccolitis;
DROP TABLE IF EXISTS form_selfreport_tpn;
DROP TABLE IF EXISTS form_selfreport_transplant;
DROP TABLE IF EXISTS form_selfreport_tysabri;
DROP TABLE IF EXISTS form_selfreport_uc;
DROP TABLE IF EXISTS form_selfreport_uveitis;
DROP TABLE IF EXISTS form_ship_log;
DROP TABLE IF EXISTS form_supplybundle;
DROP TABLE IF EXISTS form_supplyitem;
DROP TABLE IF EXISTS form_tag;
DROP TABLE IF EXISTS form_test;
DROP TABLE IF EXISTS form_test_subform;
DROP TABLE IF EXISTS form_therapy;
DROP TABLE IF EXISTS form_unit;
DROP TABLE IF EXISTS form_us_state;
DROP TABLE IF EXISTS form_user_role;
DROP TABLE IF EXISTS form_view_patient_prepurchase;
DROP TABLE IF EXISTS gr_form_assessment_aat_adr_type_to_problem_id;
DROP TABLE IF EXISTS gr_form_assessment_antibiotic_adr_type_to_problem_id;
DROP TABLE IF EXISTS gr_form_assessment_antibiotic_have_side_effects_to_problem_id;
DROP TABLE IF EXISTS gr_form_assessment_factor_adr_type_to_problem_id;
DROP TABLE IF EXISTS gr_form_assessment_factor_has_hiv_list_to_drug_brand_id;
DROP TABLE IF EXISTS gr_form_assessment_factor_have_antibiotic_list_to_drug_brand_id;
DROP TABLE IF EXISTS gr_form_assessment_hydration_adr_type_to_problem_id;
DROP TABLE IF EXISTS gr_form_assessment_ivig_adr_type_to_problem_id;
DROP TABLE IF EXISTS gr_form_assessment_methyl_adr_type_to_problem_id;
DROP TABLE IF EXISTS gr_form_assessment_steroid_adr_type_to_problem_id;
DROP TABLE IF EXISTS gr_form_assessment_tnf_adr_type_to_reaction_id;
DROP TABLE IF EXISTS gr_form_assessment_tnf_have_antibiotic_list_to_drug_brand_id;
DROP TABLE IF EXISTS gr_form_assessment_tpn_adr_type_to_problem_id;
DROP TABLE IF EXISTS gr_form_assigned_assessment_questionnaire_to_questionnaire_id;
DROP TABLE IF EXISTS gr_form_billing_claim_auth_id_to_patient_prior_auth_id;
DROP TABLE IF EXISTS gr_form_billing_item_payor_id_to_patient_insurance_id;
DROP TABLE IF EXISTS gr_form_careplan_delivery_tick_subform_item_to_billing_item_id;
DROP TABLE IF EXISTS gr_form_careplan_template_ids_to_careplan_template_id;
DROP TABLE IF EXISTS gr_form_careplan_work_ticket_subform_itm_to_careplan_wt_item_id;
DROP TABLE IF EXISTS gr_form_channel_filter_dx_to_diagnosis_id;
DROP TABLE IF EXISTS gr_form_channel_filter_group_to_communication_group_id;
DROP TABLE IF EXISTS gr_form_channel_filter_site_access_to_site_id;
DROP TABLE IF EXISTS gr_form_channel_filter_therapy_to_therapy_id;
DROP TABLE IF EXISTS gr_form_channel_filter_user_to_user_id;
DROP TABLE IF EXISTS gr_form_communication_group_members_to_user_id;
DROP TABLE IF EXISTS gr_form_drug_brand_diagnosis_to_diagnosis_id;
DROP TABLE IF EXISTS gr_form_educationbundle_items_to_education_id;
DROP TABLE IF EXISTS gr_form_encounter_had_adr_reactions_to_reaction_id;
DROP TABLE IF EXISTS gr_form_encounter_other_complaint_to_problem_id;
DROP TABLE IF EXISTS gr_form_intake_other_meds_to_medication_id;
DROP TABLE IF EXISTS gr_form_intervention_adr_reported_to_reaction_id;
DROP TABLE IF EXISTS gr_form_list_billing_code_therapy_to_list_intake_therapy_id;
DROP TABLE IF EXISTS gr_form_medicare_hit_labs_list_to_lab_id;
DROP TABLE IF EXISTS gr_form_medicare_hit_labs_list_to_list_lab_id;
DROP TABLE IF EXISTS gr_form_medicare_hit_patient_allergy_to_allergen_id;
DROP TABLE IF EXISTS gr_form_medicare_hit_patient_allergy_to_list_allergen_id;
DROP TABLE IF EXISTS gr_form_npi_delegate_approved_to_user_id;
DROP TABLE IF EXISTS gr_form_ongoing_factor_have_antibiotic_list_to_drug_brand_id;
DROP TABLE IF EXISTS gr_form_ongoing_had_adr_reactions_to_reaction_id;
DROP TABLE IF EXISTS gr_form_ongoing_radicava_other_reactions_to_reaction_id;
DROP TABLE IF EXISTS gr_form_patient_allergy_reaction_to_list_reaction_id;
DROP TABLE IF EXISTS gr_form_patient_allergy_reaction_to_problem_id;
DROP TABLE IF EXISTS gr_form_patient_attachment_flag_to_flag_id;
DROP TABLE IF EXISTS gr_form_patient_attachment_flag_to_list_flag_id;
DROP TABLE IF EXISTS gr_form_patient_infection_meds_to_inventory_id;
DROP TABLE IF EXISTS gr_form_patient_insurance_next_id_to_patient_insurance_id;
DROP TABLE IF EXISTS gr_form_patient_lab_labs_list_to_lab_id;
DROP TABLE IF EXISTS gr_form_patient_lab_labs_list_to_list_lab_id;
DROP TABLE IF EXISTS gr_form_patient_patient_tags_to_tag_id;
DROP TABLE IF EXISTS gr_form_patient_schedule_lab_labs_list_to_lab_id;
DROP TABLE IF EXISTS gr_form_patient_todo_assign_to_to_user_id;
DROP TABLE IF EXISTS gr_form_patient_todo_cc_response_to_user_id;
DROP TABLE IF EXISTS gr_form_previous_treatment_had_adr_reactions_to_reaction_id;
DROP TABLE IF EXISTS gr_form_private_group_user_id_to_user_id;
DROP TABLE IF EXISTS gr_form_sales_account_attachment_flag_to_flag_id;
DROP TABLE IF EXISTS gr_form_sales_attachment_flag_to_flag_id;
DROP TABLE IF EXISTS gr_form_sales_attachment_flag_to_list_flag_id;
DROP TABLE IF EXISTS gr_form_sales_call_log_contacts_ids_to_sales_account_contact_id;
DROP TABLE IF EXISTS gr_form_sales_call_log_contacts_ids_to_salesaccount_contact_id;
DROP TABLE IF EXISTS gr_form_sales_call_log_dr_id_to_salesaccount_physician_id;
DROP TABLE IF EXISTS gr_form_sales_call_log_phy_ids_to_sales_account_physician_id;
DROP TABLE IF EXISTS gr_form_sales_call_log_phy_ids_to_salesaccount_physician_id;
DROP TABLE IF EXISTS gr_form_sales_contact_log_dr_id_to_salesaccount_physician_id;
DROP TABLE IF EXISTS gr_form_sales_courtesy_call_dr_id_to_salesaccount_physician_id;
DROP TABLE IF EXISTS gr_form_sales_manager_rep_ids_to_user_id;
DROP TABLE IF EXISTS gr_form_salesaccount_attachment_flag_to_flag_id;
DROP TABLE IF EXISTS gr_form_salesaccount_territories_to_sales_territory_id;
DROP TABLE IF EXISTS gr_form_selfreport_methyl_reactions_to_problem_id;
DROP TABLE IF EXISTS gr_form_selfreport_steroid_reactions_to_problem_id;
DROP TABLE IF EXISTS gr_form_sideeffect_disease_to_list_disease_id;
DROP TABLE IF EXISTS gr_form_sideeffect_drug_to_list_drug_brand_id;
DROP TABLE IF EXISTS gr_form_sideeffect_route_to_list_route_id;
DROP TABLE IF EXISTS gr_form_sideeffect_therapy_to_list_therapy_id;
DROP TABLE IF EXISTS gr_form_supplybundle_items_to_supplyitem_id;
DROP TABLE IF EXISTS gr_form_test_cotherapies_to_list_therapy_id;
DROP TABLE IF EXISTS gr_form_test_subform_fselect_multi_to_patient_id;
DROP TABLE IF EXISTS gr_form_therapy_map_cotherapies_to_list_therapy_id;
DROP TABLE IF EXISTS gr_form_therapy_map_cotherapies_to_therapy_id;
DROP TABLE IF EXISTS lgr_form_assessment_aat_adr_type_to_problem_id;
DROP TABLE IF EXISTS lgr_form_assessment_antibiotic_adr_type_to_problem_id;
DROP TABLE IF EXISTS lgr_form_assessment_antibiotic_have_side_effects_to_problem_id;
DROP TABLE IF EXISTS lgr_form_assessment_factor_adr_type_to_problem_id;
DROP TABLE IF EXISTS lgr_form_assessment_factor_has_hiv_list_to_drug_brand_id;
DROP TABLE IF EXISTS lgr_form_assessment_factor_have_antibiotic_list_to_drug_brand_i;
DROP TABLE IF EXISTS lgr_form_assessment_hydration_adr_type_to_problem_id;
DROP TABLE IF EXISTS lgr_form_assessment_ivig_adr_type_to_problem_id;
DROP TABLE IF EXISTS lgr_form_assessment_methyl_adr_type_to_problem_id;
DROP TABLE IF EXISTS lgr_form_assessment_steroid_adr_type_to_problem_id;
DROP TABLE IF EXISTS lgr_form_assessment_tnf_adr_type_to_reaction_id;
DROP TABLE IF EXISTS lgr_form_assessment_tnf_have_antibiotic_list_to_drug_brand_id;
DROP TABLE IF EXISTS lgr_form_assessment_tpn_adr_type_to_problem_id;
DROP TABLE IF EXISTS lgr_form_assigned_assessment_questionnaire_to_questionnaire_id;
DROP TABLE IF EXISTS lgr_form_billing_claim_auth_id_to_patient_prior_auth_id;
DROP TABLE IF EXISTS lgr_form_billing_item_payor_id_to_patient_insurance_id;
DROP TABLE IF EXISTS lgr_form_careplan_delivery_tick_subform_item_to_billing_item_id;
DROP TABLE IF EXISTS lgr_form_careplan_template_ids_to_careplan_template_id;
DROP TABLE IF EXISTS lgr_form_careplan_work_ticket_subform_itm_to_careplan_wt_item_i;
DROP TABLE IF EXISTS lgr_form_channel_filter_dx_to_diagnosis_id;
DROP TABLE IF EXISTS lgr_form_channel_filter_group_to_communication_group_id;
DROP TABLE IF EXISTS lgr_form_channel_filter_site_access_to_site_id;
DROP TABLE IF EXISTS lgr_form_channel_filter_therapy_to_therapy_id;
DROP TABLE IF EXISTS lgr_form_channel_filter_user_to_user_id;
DROP TABLE IF EXISTS lgr_form_communication_group_members_to_user_id;
DROP TABLE IF EXISTS lgr_form_drug_brand_diagnosis_to_diagnosis_id;
DROP TABLE IF EXISTS lgr_form_educationbundle_items_to_education_id;
DROP TABLE IF EXISTS lgr_form_encounter_had_adr_reactions_to_reaction_id;
DROP TABLE IF EXISTS lgr_form_encounter_other_complaint_to_problem_id;
DROP TABLE IF EXISTS lgr_form_intake_other_meds_to_medication_id;
DROP TABLE IF EXISTS lgr_form_intervention_adr_reported_to_reaction_id;
DROP TABLE IF EXISTS lgr_form_list_billing_code_therapy_to_list_intake_therapy_id;
DROP TABLE IF EXISTS lgr_form_medicare_hit_labs_list_to_lab_id;
DROP TABLE IF EXISTS lgr_form_medicare_hit_labs_list_to_list_lab_id;
DROP TABLE IF EXISTS lgr_form_medicare_hit_patient_allergy_to_allergen_id;
DROP TABLE IF EXISTS lgr_form_medicare_hit_patient_allergy_to_list_allergen_id;
DROP TABLE IF EXISTS lgr_form_npi_delegate_approved_to_user_id;
DROP TABLE IF EXISTS lgr_form_ongoing_factor_have_antibiotic_list_to_drug_brand_id;
DROP TABLE IF EXISTS lgr_form_ongoing_had_adr_reactions_to_reaction_id;
DROP TABLE IF EXISTS lgr_form_ongoing_radicava_other_reactions_to_reaction_id;
DROP TABLE IF EXISTS lgr_form_patient_allergy_reaction_to_list_reaction_id;
DROP TABLE IF EXISTS lgr_form_patient_allergy_reaction_to_problem_id;
DROP TABLE IF EXISTS lgr_form_patient_attachment_flag_to_flag_id;
DROP TABLE IF EXISTS lgr_form_patient_attachment_flag_to_list_flag_id;
DROP TABLE IF EXISTS lgr_form_patient_infection_meds_to_inventory_id;
DROP TABLE IF EXISTS lgr_form_patient_insurance_next_id_to_patient_insurance_id;
DROP TABLE IF EXISTS lgr_form_patient_lab_labs_list_to_lab_id;
DROP TABLE IF EXISTS lgr_form_patient_lab_labs_list_to_list_lab_id;
DROP TABLE IF EXISTS lgr_form_patient_patient_tags_to_tag_id;
DROP TABLE IF EXISTS lgr_form_patient_schedule_lab_labs_list_to_lab_id;
DROP TABLE IF EXISTS lgr_form_patient_todo_assign_to_to_user_id;
DROP TABLE IF EXISTS lgr_form_patient_todo_cc_response_to_user_id;
DROP TABLE IF EXISTS lgr_form_previous_treatment_had_adr_reactions_to_reaction_id;
DROP TABLE IF EXISTS lgr_form_private_group_user_id_to_user_id;
DROP TABLE IF EXISTS lgr_form_sales_attachment_flag_to_flag_id;
DROP TABLE IF EXISTS lgr_form_sales_attachment_flag_to_list_flag_id;
DROP TABLE IF EXISTS lgr_form_sales_call_log_contacts_ids_to_sales_account_contact_i;
DROP TABLE IF EXISTS lgr_form_sales_call_log_contacts_ids_to_salesaccount_contact_id;
DROP TABLE IF EXISTS lgr_form_sales_call_log_dr_id_to_salesaccount_physician_id;
DROP TABLE IF EXISTS lgr_form_sales_call_log_phy_ids_to_sales_account_physician_id;
DROP TABLE IF EXISTS lgr_form_sales_call_log_phy_ids_to_salesaccount_physician_id;
DROP TABLE IF EXISTS lgr_form_sales_contact_log_dr_id_to_salesaccount_physician_id;
DROP TABLE IF EXISTS lgr_form_sales_courtesy_call_dr_id_to_salesaccount_physician_id;
DROP TABLE IF EXISTS lgr_form_sales_manager_rep_ids_to_user_id;
DROP TABLE IF EXISTS lgr_form_salesaccount_attachment_flag_to_flag_id;
DROP TABLE IF EXISTS lgr_form_salesaccount_territories_to_sales_territory_id;
DROP TABLE IF EXISTS lgr_form_selfreport_methyl_reactions_to_problem_id;
DROP TABLE IF EXISTS lgr_form_selfreport_steroid_reactions_to_problem_id;
DROP TABLE IF EXISTS lgr_form_sideeffect_disease_to_list_disease_id;
DROP TABLE IF EXISTS lgr_form_sideeffect_drug_to_list_drug_brand_id;
DROP TABLE IF EXISTS lgr_form_sideeffect_route_to_list_route_id;
DROP TABLE IF EXISTS lgr_form_sideeffect_therapy_to_list_therapy_id;
DROP TABLE IF EXISTS lgr_form_supplybundle_items_to_supplyitem_id;
DROP TABLE IF EXISTS lgr_form_therapy_map_cotherapies_to_list_therapy_id;
DROP TABLE IF EXISTS lgr_form_therapy_map_cotherapies_to_therapy_id;
DROP TABLE IF EXISTS log_agency;
DROP TABLE IF EXISTS log_allergen;
DROP TABLE IF EXISTS log_assessment_ad;
DROP TABLE IF EXISTS log_assessment_anemia;
DROP TABLE IF EXISTS log_assessment_antibiotic;
DROP TABLE IF EXISTS log_assessment_as;
DROP TABLE IF EXISTS log_assessment_biologics;
DROP TABLE IF EXISTS log_assessment_bleeding_disorder;
DROP TABLE IF EXISTS log_assessment_blood_thinners;
DROP TABLE IF EXISTS log_assessment_breast_cancer;
DROP TABLE IF EXISTS log_assessment_cancer;
DROP TABLE IF EXISTS log_assessment_catheter_care;
DROP TABLE IF EXISTS log_assessment_cd;
DROP TABLE IF EXISTS log_assessment_chelation;
DROP TABLE IF EXISTS log_assessment_cinqair;
DROP TABLE IF EXISTS log_assessment_cvid;
DROP TABLE IF EXISTS log_assessment_dietitian;
DROP TABLE IF EXISTS log_assessment_dietitian_tpn;
DROP TABLE IF EXISTS log_assessment_enteral;
DROP TABLE IF EXISTS log_assessment_enzyme;
DROP TABLE IF EXISTS log_assessment_fh;
DROP TABLE IF EXISTS log_assessment_general;
DROP TABLE IF EXISTS log_assessment_gh;
DROP TABLE IF EXISTS log_assessment_hbig;
DROP TABLE IF EXISTS log_assessment_he;
DROP TABLE IF EXISTS log_assessment_hf;
DROP TABLE IF EXISTS log_assessment_hhome;
DROP TABLE IF EXISTS log_assessment_hs;
DROP TABLE IF EXISTS log_assessment_hydration;
DROP TABLE IF EXISTS log_assessment_hyperlipidemia;
DROP TABLE IF EXISTS log_assessment_ig;
DROP TABLE IF EXISTS log_assessment_igneuro;
DROP TABLE IF EXISTS log_assessment_immunotherapy;
DROP TABLE IF EXISTS log_assessment_intrathecal;
DROP TABLE IF EXISTS log_assessment_iron_overload;
DROP TABLE IF EXISTS log_assessment_iron_therapy;
DROP TABLE IF EXISTS log_assessment_ivig;
DROP TABLE IF EXISTS log_assessment_jia;
DROP TABLE IF EXISTS log_assessment_jra;
DROP TABLE IF EXISTS log_assessment_methyl;
DROP TABLE IF EXISTS log_assessment_nephro;
DROP TABLE IF EXISTS log_assessment_neutropenia;
DROP TABLE IF EXISTS log_assessment_nurse;
DROP TABLE IF EXISTS log_assessment_nursing;
DROP TABLE IF EXISTS log_assessment_oncology;
DROP TABLE IF EXISTS log_assessment_osteoporosis;
DROP TABLE IF EXISTS log_assessment_other;
DROP TABLE IF EXISTS log_assessment_pah;
DROP TABLE IF EXISTS log_assessment_pain_management;
DROP TABLE IF EXISTS log_assessment_prostate_cancer;
DROP TABLE IF EXISTS log_assessment_psa;
DROP TABLE IF EXISTS log_assessment_pso;
DROP TABLE IF EXISTS log_assessment_skin_cancer;
DROP TABLE IF EXISTS log_assessment_tepezza;
DROP TABLE IF EXISTS log_assessment_thrombocytopenia;
DROP TABLE IF EXISTS log_assessment_transplant;
DROP TABLE IF EXISTS log_assessment_uc;
DROP TABLE IF EXISTS log_assessment_uveitis;
DROP TABLE IF EXISTS log_assessment_vyepti;
DROP TABLE IF EXISTS log_assigned_assessment;
DROP TABLE IF EXISTS log_billing_claim_med_item;
DROP TABLE IF EXISTS log_billing_claim_ncpdp_add_chrg;
DROP TABLE IF EXISTS log_billing_claim_ncpdp_cob;
DROP TABLE IF EXISTS log_billing_claim_ncpdp_dx;
DROP TABLE IF EXISTS log_billing_claim_ncpdp_item;
DROP TABLE IF EXISTS log_billing_claim_ncpdp_measure;
DROP TABLE IF EXISTS log_billing_claim_ncpdp_partial;
DROP TABLE IF EXISTS log_careplan_pharmacist;
DROP TABLE IF EXISTS log_careplan_wt_label;
DROP TABLE IF EXISTS log_careplan_wt_lbl;
DROP TABLE IF EXISTS log_channel;
DROP TABLE IF EXISTS log_channel_user;
DROP TABLE IF EXISTS log_communication;
DROP TABLE IF EXISTS log_communication_group;
DROP TABLE IF EXISTS log_communication_to;
DROP TABLE IF EXISTS log_company_configuration;
DROP TABLE IF EXISTS log_company_test;
DROP TABLE IF EXISTS log_complaint;
DROP TABLE IF EXISTS log_cpr_icd_master_list;
DROP TABLE IF EXISTS log_cpr_icd_patient;
DROP TABLE IF EXISTS log_cprexport;
DROP TABLE IF EXISTS log_diagnosis;
DROP TABLE IF EXISTS log_disease;
DROP TABLE IF EXISTS log_drug_brand;
DROP TABLE IF EXISTS log_ebridged_error;
DROP TABLE IF EXISTS log_education;
DROP TABLE IF EXISTS log_educationbundle;
DROP TABLE IF EXISTS log_encounter_aat_admin;
DROP TABLE IF EXISTS log_encounter_ad;
DROP TABLE IF EXISTS log_encounter_ad_admin;
DROP TABLE IF EXISTS log_encounter_aghda;
DROP TABLE IF EXISTS log_encounter_alsfrs;
DROP TABLE IF EXISTS log_encounter_anemia;
DROP TABLE IF EXISTS log_encounter_anemia_admin;
DROP TABLE IF EXISTS log_encounter_antibiotic_admin;
DROP TABLE IF EXISTS log_encounter_as;
DROP TABLE IF EXISTS log_encounter_as_admin;
DROP TABLE IF EXISTS log_encounter_basdai;
DROP TABLE IF EXISTS log_encounter_biologics;
DROP TABLE IF EXISTS log_encounter_biologics_admin;
DROP TABLE IF EXISTS log_encounter_bisphosphonates;
DROP TABLE IF EXISTS log_encounter_bisphosphonates_admin;
DROP TABLE IF EXISTS log_encounter_bleeding_disorder;
DROP TABLE IF EXISTS log_encounter_bleeding_disorder_admin;
DROP TABLE IF EXISTS log_encounter_blood_thinners;
DROP TABLE IF EXISTS log_encounter_blood_thinners_admin;
DROP TABLE IF EXISTS log_encounter_braden;
DROP TABLE IF EXISTS log_encounter_breast_cancer;
DROP TABLE IF EXISTS log_encounter_breast_cancer_admin;
DROP TABLE IF EXISTS log_encounter_cancer;
DROP TABLE IF EXISTS log_encounter_cancer_admin;
DROP TABLE IF EXISTS log_encounter_catheter_care;
DROP TABLE IF EXISTS log_encounter_catheter_care_admin;
DROP TABLE IF EXISTS log_encounter_cd;
DROP TABLE IF EXISTS log_encounter_cd_admin;
DROP TABLE IF EXISTS log_encounter_chelation;
DROP TABLE IF EXISTS log_encounter_chelation_admin;
DROP TABLE IF EXISTS log_encounter_chemotherapy;
DROP TABLE IF EXISTS log_encounter_chemotherapy_admin;
DROP TABLE IF EXISTS log_encounter_cinqair;
DROP TABLE IF EXISTS log_encounter_cinqair_admin;
DROP TABLE IF EXISTS log_encounter_cvid;
DROP TABLE IF EXISTS log_encounter_cvid_admin;
DROP TABLE IF EXISTS log_encounter_dlqi;
DROP TABLE IF EXISTS log_encounter_dupixent;
DROP TABLE IF EXISTS log_encounter_dupixent_admin;
DROP TABLE IF EXISTS log_encounter_edss;
DROP TABLE IF EXISTS log_encounter_enteral;
DROP TABLE IF EXISTS log_encounter_enteral_admin;
DROP TABLE IF EXISTS log_encounter_enzyme;
DROP TABLE IF EXISTS log_encounter_enzyme_admin;
DROP TABLE IF EXISTS log_encounter_epworth;
DROP TABLE IF EXISTS log_encounter_factor_admin;
DROP TABLE IF EXISTS log_encounter_fh;
DROP TABLE IF EXISTS log_encounter_fh_admin;
DROP TABLE IF EXISTS log_encounter_general;
DROP TABLE IF EXISTS log_encounter_general_admin;
DROP TABLE IF EXISTS log_encounter_gh;
DROP TABLE IF EXISTS log_encounter_gh_admin;
DROP TABLE IF EXISTS log_encounter_hat_qol;
DROP TABLE IF EXISTS log_encounter_hbi;
DROP TABLE IF EXISTS log_encounter_hbig;
DROP TABLE IF EXISTS log_encounter_hbig_admin;
DROP TABLE IF EXISTS log_encounter_hcv_symptoms;
DROP TABLE IF EXISTS log_encounter_he;
DROP TABLE IF EXISTS log_encounter_he_admin;
DROP TABLE IF EXISTS log_encounter_hepb;
DROP TABLE IF EXISTS log_encounter_hepb_admin;
DROP TABLE IF EXISTS log_encounter_hepc;
DROP TABLE IF EXISTS log_encounter_hepc_admin;
DROP TABLE IF EXISTS log_encounter_hf;
DROP TABLE IF EXISTS log_encounter_hf_admin;
DROP TABLE IF EXISTS log_encounter_hfqol;
DROP TABLE IF EXISTS log_encounter_hhome;
DROP TABLE IF EXISTS log_encounter_hhome_admin;
DROP TABLE IF EXISTS log_encounter_hiv;
DROP TABLE IF EXISTS log_encounter_hiv_admin;
DROP TABLE IF EXISTS log_encounter_hmq;
DROP TABLE IF EXISTS log_encounter_hpq;
DROP TABLE IF EXISTS log_encounter_hs;
DROP TABLE IF EXISTS log_encounter_hs_admin;
DROP TABLE IF EXISTS log_encounter_humira;
DROP TABLE IF EXISTS log_encounter_humira_admin;
DROP TABLE IF EXISTS log_encounter_hydration;
DROP TABLE IF EXISTS log_encounter_hydration_admin;
DROP TABLE IF EXISTS log_encounter_hyperlipidemia;
DROP TABLE IF EXISTS log_encounter_hyperlipidemia_admin;
DROP TABLE IF EXISTS log_encounter_igneuro;
DROP TABLE IF EXISTS log_encounter_igneuro_admin;
DROP TABLE IF EXISTS log_encounter_immunotherapy;
DROP TABLE IF EXISTS log_encounter_immunotherapy_admin;
DROP TABLE IF EXISTS log_encounter_incat;
DROP TABLE IF EXISTS log_encounter_inotrope;
DROP TABLE IF EXISTS log_encounter_inotrope_admin;
DROP TABLE IF EXISTS log_encounter_intrathecal;
DROP TABLE IF EXISTS log_encounter_intrathecal_admin;
DROP TABLE IF EXISTS log_encounter_iron_overload;
DROP TABLE IF EXISTS log_encounter_iron_overload_admin;
DROP TABLE IF EXISTS log_encounter_iron_therapy;
DROP TABLE IF EXISTS log_encounter_iron_therapy_admin;
DROP TABLE IF EXISTS log_encounter_ivig;
DROP TABLE IF EXISTS log_encounter_ivig_admin;
DROP TABLE IF EXISTS log_encounter_ivig_cidp;
DROP TABLE IF EXISTS log_encounter_ivig_immdef;
DROP TABLE IF EXISTS log_encounter_ivig_itp;
DROP TABLE IF EXISTS log_encounter_ivig_mmn;
DROP TABLE IF EXISTS log_encounter_ivig_ms;
DROP TABLE IF EXISTS log_encounter_ivig_mygrav;
DROP TABLE IF EXISTS log_encounter_ivig_myositis;
DROP TABLE IF EXISTS log_encounter_ivig_pemphigus;
DROP TABLE IF EXISTS log_encounter_ivig_stiffps;
DROP TABLE IF EXISTS log_encounter_jia;
DROP TABLE IF EXISTS log_encounter_jia_admin;
DROP TABLE IF EXISTS log_encounter_jra;
DROP TABLE IF EXISTS log_encounter_jra_admin;
DROP TABLE IF EXISTS log_encounter_krystexxa;
DROP TABLE IF EXISTS log_encounter_krystexxa_admin;
DROP TABLE IF EXISTS log_encounter_lemtrada_admin;
DROP TABLE IF EXISTS log_encounter_methyl;
DROP TABLE IF EXISTS log_encounter_methyl_admin;
DROP TABLE IF EXISTS log_encounter_mfis5;
DROP TABLE IF EXISTS log_encounter_mgadl;
DROP TABLE IF EXISTS log_encounter_mhaq;
DROP TABLE IF EXISTS log_encounter_mmas8;
DROP TABLE IF EXISTS log_encounter_monoclon;
DROP TABLE IF EXISTS log_encounter_monoclon_admin;
DROP TABLE IF EXISTS log_encounter_moqlq;
DROP TABLE IF EXISTS log_encounter_ms;
DROP TABLE IF EXISTS log_encounter_ms_admin;
DROP TABLE IF EXISTS log_encounter_nephro;
DROP TABLE IF EXISTS log_encounter_nephro_admin;
DROP TABLE IF EXISTS log_encounter_neutropenia;
DROP TABLE IF EXISTS log_encounter_neutropenia_admin;
DROP TABLE IF EXISTS log_encounter_nursing;
DROP TABLE IF EXISTS log_encounter_nursing_admin;
DROP TABLE IF EXISTS log_encounter_ocrevus;
DROP TABLE IF EXISTS log_encounter_ocrevus_admin;
DROP TABLE IF EXISTS log_encounter_osteoporosis;
DROP TABLE IF EXISTS log_encounter_osteoporosis_admin;
DROP TABLE IF EXISTS log_encounter_other;
DROP TABLE IF EXISTS log_encounter_other_admin;
DROP TABLE IF EXISTS log_encounter_padqol;
DROP TABLE IF EXISTS log_encounter_pah;
DROP TABLE IF EXISTS log_encounter_pah_admin;
DROP TABLE IF EXISTS log_encounter_pain_management;
DROP TABLE IF EXISTS log_encounter_pain_management_admin;
DROP TABLE IF EXISTS log_encounter_pas2;
DROP TABLE IF EXISTS log_encounter_pes;
DROP TABLE IF EXISTS log_encounter_phq;
DROP TABLE IF EXISTS log_encounter_poem;
DROP TABLE IF EXISTS log_encounter_prostate_cancer;
DROP TABLE IF EXISTS log_encounter_prostate_cancer_admin;
DROP TABLE IF EXISTS log_encounter_psa;
DROP TABLE IF EXISTS log_encounter_psa_admin;
DROP TABLE IF EXISTS log_encounter_pso;
DROP TABLE IF EXISTS log_encounter_pso_admin;
DROP TABLE IF EXISTS log_encounter_psoriasis;
DROP TABLE IF EXISTS log_encounter_psoriasis_admin;
DROP TABLE IF EXISTS log_encounter_qlsh;
DROP TABLE IF EXISTS log_encounter_qol;
DROP TABLE IF EXISTS log_encounter_ra;
DROP TABLE IF EXISTS log_encounter_ra_admin;
DROP TABLE IF EXISTS log_encounter_radai;
DROP TABLE IF EXISTS log_encounter_radicava_admin;
DROP TABLE IF EXISTS log_encounter_rituxan;
DROP TABLE IF EXISTS log_encounter_rituxan_admin;
DROP TABLE IF EXISTS log_encounter_rods;
DROP TABLE IF EXISTS log_encounter_sf12v2;
DROP TABLE IF EXISTS log_encounter_sibdq;
DROP TABLE IF EXISTS log_encounter_skin_cancer;
DROP TABLE IF EXISTS log_encounter_skin_cancer_admin;
DROP TABLE IF EXISTS log_encounter_soliris;
DROP TABLE IF EXISTS log_encounter_soliris_admin;
DROP TABLE IF EXISTS log_encounter_steroid;
DROP TABLE IF EXISTS log_encounter_steroid_admin;
DROP TABLE IF EXISTS log_encounter_tepezza;
DROP TABLE IF EXISTS log_encounter_tepezza_admin;
DROP TABLE IF EXISTS log_encounter_thrombocytopenia;
DROP TABLE IF EXISTS log_encounter_thrombocytopenia_admin;
DROP TABLE IF EXISTS log_encounter_tnf_admin;
DROP TABLE IF EXISTS log_encounter_tnf_crohns;
DROP TABLE IF EXISTS log_encounter_tnf_parthritis;
DROP TABLE IF EXISTS log_encounter_tnf_psoriasis;
DROP TABLE IF EXISTS log_encounter_tnf_rheumatology;
DROP TABLE IF EXISTS log_encounter_tnf_ulccolitis;
DROP TABLE IF EXISTS log_encounter_tpn_admin;
DROP TABLE IF EXISTS log_encounter_transplant;
DROP TABLE IF EXISTS log_encounter_transplant_admin;
DROP TABLE IF EXISTS log_encounter_tysabri;
DROP TABLE IF EXISTS log_encounter_tysabri_admin;
DROP TABLE IF EXISTS log_encounter_uc;
DROP TABLE IF EXISTS log_encounter_uc_admin;
DROP TABLE IF EXISTS log_encounter_uveitis;
DROP TABLE IF EXISTS log_encounter_uveitis_admin;
DROP TABLE IF EXISTS log_encounter_vyepti;
DROP TABLE IF EXISTS log_encounter_vyepti_admin;
DROP TABLE IF EXISTS log_encounter_wat;
DROP TABLE IF EXISTS log_encounter_wpai;
DROP TABLE IF EXISTS log_ext_cpr_order;
DROP TABLE IF EXISTS log_flag;
DROP TABLE IF EXISTS log_followupprogram;
DROP TABLE IF EXISTS log_immunization;
DROP TABLE IF EXISTS log_incident;
DROP TABLE IF EXISTS log_insurance;
DROP TABLE IF EXISTS log_intake;
DROP TABLE IF EXISTS log_intervention;
DROP TABLE IF EXISTS log_intervention_type;
DROP TABLE IF EXISTS log_lab;
DROP TABLE IF EXISTS log_list_billing_dispensing_status;
DROP TABLE IF EXISTS log_list_billing_dose_indicator;
DROP TABLE IF EXISTS log_list_billing_uom_code;
DROP TABLE IF EXISTS log_list_compound_form;
DROP TABLE IF EXISTS log_list_compound_route;
DROP TABLE IF EXISTS log_list_compound_type;
DROP TABLE IF EXISTS log_list_compound_unit;
DROP TABLE IF EXISTS log_list_coverage_relationship;
DROP TABLE IF EXISTS log_list_daw_qualifier;
DROP TABLE IF EXISTS log_list_eligibility_class_code;
DROP TABLE IF EXISTS log_list_healthcare_provider_taxonomy;
DROP TABLE IF EXISTS log_list_healthcare_taxonomy_group;
DROP TABLE IF EXISTS log_list_inter_auth_type;
DROP TABLE IF EXISTS log_list_ncpdp_add_qualifier;
DROP TABLE IF EXISTS log_list_ncpdp_benefit_qual;
DROP TABLE IF EXISTS log_list_ncpdp_conflict_type;
DROP TABLE IF EXISTS log_list_ncpdp_cost_basis;
DROP TABLE IF EXISTS log_list_ncpdp_coupon_type;
DROP TABLE IF EXISTS log_list_ncpdp_delay_reason;
DROP TABLE IF EXISTS log_list_ncpdp_dx_qualifier;
DROP TABLE IF EXISTS log_list_ncpdp_effort;
DROP TABLE IF EXISTS log_list_ncpdp_inerv_outcome;
DROP TABLE IF EXISTS log_list_ncpdp_interv_type;
DROP TABLE IF EXISTS log_list_ncpdp_los;
DROP TABLE IF EXISTS log_list_ncpdp_measure;
DROP TABLE IF EXISTS log_list_ncpdp_measure_unit;
DROP TABLE IF EXISTS log_list_ncpdp_payer_qualifier;
DROP TABLE IF EXISTS log_list_ncpdp_payer_type;
DROP TABLE IF EXISTS log_list_other_provider_identifier;
DROP TABLE IF EXISTS log_list_pregnancy_indicator;
DROP TABLE IF EXISTS log_list_submission_class_code;
DROP TABLE IF EXISTS log_list_tax_basis;
DROP TABLE IF EXISTS log_manufacturer;
DROP TABLE IF EXISTS log_medication;
DROP TABLE IF EXISTS log_medication_diagnosis;
DROP TABLE IF EXISTS log_ndc_code;
DROP TABLE IF EXISTS log_npi_delegate;
DROP TABLE IF EXISTS log_ongoing_ad;
DROP TABLE IF EXISTS log_ongoing_anemia;
DROP TABLE IF EXISTS log_ongoing_as;
DROP TABLE IF EXISTS log_ongoing_biologics;
DROP TABLE IF EXISTS log_ongoing_bisphosphonates;
DROP TABLE IF EXISTS log_ongoing_bleeding_disorder;
DROP TABLE IF EXISTS log_ongoing_blood_thinners;
DROP TABLE IF EXISTS log_ongoing_breast_cancer;
DROP TABLE IF EXISTS log_ongoing_cancer;
DROP TABLE IF EXISTS log_ongoing_catheter_care;
DROP TABLE IF EXISTS log_ongoing_cd;
DROP TABLE IF EXISTS log_ongoing_chelation;
DROP TABLE IF EXISTS log_ongoing_cinqair;
DROP TABLE IF EXISTS log_ongoing_cvid;
DROP TABLE IF EXISTS log_ongoing_enteral;
DROP TABLE IF EXISTS log_ongoing_enzyme;
DROP TABLE IF EXISTS log_ongoing_fh;
DROP TABLE IF EXISTS log_ongoing_general;
DROP TABLE IF EXISTS log_ongoing_gh;
DROP TABLE IF EXISTS log_ongoing_hbig;
DROP TABLE IF EXISTS log_ongoing_he;
DROP TABLE IF EXISTS log_ongoing_hf;
DROP TABLE IF EXISTS log_ongoing_hhome;
DROP TABLE IF EXISTS log_ongoing_hs;
DROP TABLE IF EXISTS log_ongoing_humira;
DROP TABLE IF EXISTS log_ongoing_hydration;
DROP TABLE IF EXISTS log_ongoing_hyperlipidemia;
DROP TABLE IF EXISTS log_ongoing_igneuro;
DROP TABLE IF EXISTS log_ongoing_immunotherapy;
DROP TABLE IF EXISTS log_ongoing_intrathecal;
DROP TABLE IF EXISTS log_ongoing_iron_overload;
DROP TABLE IF EXISTS log_ongoing_iron_therapy;
DROP TABLE IF EXISTS log_ongoing_ivig;
DROP TABLE IF EXISTS log_ongoing_jia;
DROP TABLE IF EXISTS log_ongoing_jra;
DROP TABLE IF EXISTS log_ongoing_lemtrada;
DROP TABLE IF EXISTS log_ongoing_methyl;
DROP TABLE IF EXISTS log_ongoing_nephro;
DROP TABLE IF EXISTS log_ongoing_neutropenia;
DROP TABLE IF EXISTS log_ongoing_nursing;
DROP TABLE IF EXISTS log_ongoing_oncology;
DROP TABLE IF EXISTS log_ongoing_osteoporosis;
DROP TABLE IF EXISTS log_ongoing_other;
DROP TABLE IF EXISTS log_ongoing_pah;
DROP TABLE IF EXISTS log_ongoing_pain_management;
DROP TABLE IF EXISTS log_ongoing_prostate_cancer;
DROP TABLE IF EXISTS log_ongoing_psa;
DROP TABLE IF EXISTS log_ongoing_pso;
DROP TABLE IF EXISTS log_ongoing_ra;
DROP TABLE IF EXISTS log_ongoing_rituxan;
DROP TABLE IF EXISTS log_ongoing_skin_cancer;
DROP TABLE IF EXISTS log_ongoing_steroid;
DROP TABLE IF EXISTS log_ongoing_tepezza;
DROP TABLE IF EXISTS log_ongoing_thrombocytopenia;
DROP TABLE IF EXISTS log_ongoing_tnf;
DROP TABLE IF EXISTS log_ongoing_transplant;
DROP TABLE IF EXISTS log_ongoing_uc;
DROP TABLE IF EXISTS log_ongoing_uveitis;
DROP TABLE IF EXISTS log_ongoing_vyepti;
DROP TABLE IF EXISTS log_outreach_log;
DROP TABLE IF EXISTS log_pat_sch_selfrp_rpt_appt;
DROP TABLE IF EXISTS log_patient_adherence;
DROP TABLE IF EXISTS log_patient_benefits_investigation;
DROP TABLE IF EXISTS log_patient_call;
DROP TABLE IF EXISTS log_patient_catheter;
DROP TABLE IF EXISTS log_patient_dispense;
DROP TABLE IF EXISTS log_patient_dispense_item;
DROP TABLE IF EXISTS log_patient_education;
DROP TABLE IF EXISTS log_patient_external_dispense;
DROP TABLE IF EXISTS log_patient_external_documents;
DROP TABLE IF EXISTS log_patient_external_invoice;
DROP TABLE IF EXISTS log_patient_external_order;
DROP TABLE IF EXISTS log_patient_history;
DROP TABLE IF EXISTS log_patient_immunization;
DROP TABLE IF EXISTS log_patient_lab_draw;
DROP TABLE IF EXISTS log_patient_note_template;
DROP TABLE IF EXISTS log_patient_pending_payment;
DROP TABLE IF EXISTS log_patient_referral_call;
DROP TABLE IF EXISTS log_patient_sch_lab_rpt_appt;
DROP TABLE IF EXISTS log_patient_schedule_encounter;
DROP TABLE IF EXISTS log_patient_schedule_lab;
DROP TABLE IF EXISTS log_patient_schedule_selfreport;
DROP TABLE IF EXISTS log_patient_schedule_task;
DROP TABLE IF EXISTS log_patient_status_history;
DROP TABLE IF EXISTS log_patient_substatus;
DROP TABLE IF EXISTS log_patient_supply;
DROP TABLE IF EXISTS log_patient_supply_factor;
DROP TABLE IF EXISTS log_portal_attachment;
DROP TABLE IF EXISTS log_premedication;
DROP TABLE IF EXISTS log_previous_treatment;
DROP TABLE IF EXISTS log_private_group;
DROP TABLE IF EXISTS log_problem;
DROP TABLE IF EXISTS log_problem_diagnosis;
DROP TABLE IF EXISTS log_query_module;
DROP TABLE IF EXISTS log_questionnaire;
DROP TABLE IF EXISTS log_reaction;
DROP TABLE IF EXISTS log_referral;
DROP TABLE IF EXISTS log_rxcode_price;
DROP TABLE IF EXISTS log_sales_entity_link;
DROP TABLE IF EXISTS log_salesaccount;
DROP TABLE IF EXISTS log_salesaccount_attachment;
DROP TABLE IF EXISTS log_salesaccount_contact;
DROP TABLE IF EXISTS log_salesaccount_physician;
DROP TABLE IF EXISTS log_selfreport;
DROP TABLE IF EXISTS log_selfreport_aat;
DROP TABLE IF EXISTS log_selfreport_ad;
DROP TABLE IF EXISTS log_selfreport_anemia;
DROP TABLE IF EXISTS log_selfreport_antibiotic;
DROP TABLE IF EXISTS log_selfreport_as;
DROP TABLE IF EXISTS log_selfreport_biologics;
DROP TABLE IF EXISTS log_selfreport_bisphosphonates;
DROP TABLE IF EXISTS log_selfreport_bleeding_disorder;
DROP TABLE IF EXISTS log_selfreport_blood_thinners;
DROP TABLE IF EXISTS log_selfreport_breast_cancer;
DROP TABLE IF EXISTS log_selfreport_cancer;
DROP TABLE IF EXISTS log_selfreport_catheter_care;
DROP TABLE IF EXISTS log_selfreport_cd;
DROP TABLE IF EXISTS log_selfreport_chelation;
DROP TABLE IF EXISTS log_selfreport_chemotherapy;
DROP TABLE IF EXISTS log_selfreport_cinqair;
DROP TABLE IF EXISTS log_selfreport_cvid;
DROP TABLE IF EXISTS log_selfreport_dupixent;
DROP TABLE IF EXISTS log_selfreport_enteral;
DROP TABLE IF EXISTS log_selfreport_enzyme;
DROP TABLE IF EXISTS log_selfreport_factor;
DROP TABLE IF EXISTS log_selfreport_fh;
DROP TABLE IF EXISTS log_selfreport_general;
DROP TABLE IF EXISTS log_selfreport_gh;
DROP TABLE IF EXISTS log_selfreport_hbig;
DROP TABLE IF EXISTS log_selfreport_he;
DROP TABLE IF EXISTS log_selfreport_hepb;
DROP TABLE IF EXISTS log_selfreport_hepc;
DROP TABLE IF EXISTS log_selfreport_hf;
DROP TABLE IF EXISTS log_selfreport_hiv;
DROP TABLE IF EXISTS log_selfreport_hs;
DROP TABLE IF EXISTS log_selfreport_humira;
DROP TABLE IF EXISTS log_selfreport_hydration;
DROP TABLE IF EXISTS log_selfreport_hyperlipidemia;
DROP TABLE IF EXISTS log_selfreport_igneuro;
DROP TABLE IF EXISTS log_selfreport_immunotherapy;
DROP TABLE IF EXISTS log_selfreport_inotrope;
DROP TABLE IF EXISTS log_selfreport_intrathecal;
DROP TABLE IF EXISTS log_selfreport_iron_overload;
DROP TABLE IF EXISTS log_selfreport_iron_therapy;
DROP TABLE IF EXISTS log_selfreport_ivig;
DROP TABLE IF EXISTS log_selfreport_jia;
DROP TABLE IF EXISTS log_selfreport_jra;
DROP TABLE IF EXISTS log_selfreport_krystexxa;
DROP TABLE IF EXISTS log_selfreport_methyl;
DROP TABLE IF EXISTS log_selfreport_ms;
DROP TABLE IF EXISTS log_selfreport_nephro;
DROP TABLE IF EXISTS log_selfreport_neutropenia;
DROP TABLE IF EXISTS log_selfreport_nursing;
DROP TABLE IF EXISTS log_selfreport_osteoporosis;
DROP TABLE IF EXISTS log_selfreport_other;
DROP TABLE IF EXISTS log_selfreport_pah;
DROP TABLE IF EXISTS log_selfreport_pain_management;
DROP TABLE IF EXISTS log_selfreport_prostate_cancer;
DROP TABLE IF EXISTS log_selfreport_psa;
DROP TABLE IF EXISTS log_selfreport_pso;
DROP TABLE IF EXISTS log_selfreport_psoriasis;
DROP TABLE IF EXISTS log_selfreport_ra;
DROP TABLE IF EXISTS log_selfreport_radicava;
DROP TABLE IF EXISTS log_selfreport_rituxan;
DROP TABLE IF EXISTS log_selfreport_skin_cancer;
DROP TABLE IF EXISTS log_selfreport_soliris;
DROP TABLE IF EXISTS log_selfreport_steroid;
DROP TABLE IF EXISTS log_selfreport_tepezza;
DROP TABLE IF EXISTS log_selfreport_thrombocytopenia;
DROP TABLE IF EXISTS log_selfreport_tnf;
DROP TABLE IF EXISTS log_selfreport_tnf_crohns;
DROP TABLE IF EXISTS log_selfreport_tnf_parthritis;
DROP TABLE IF EXISTS log_selfreport_tnf_psoriasis;
DROP TABLE IF EXISTS log_selfreport_tnf_rheumatology;
DROP TABLE IF EXISTS log_selfreport_tnf_ulccolitis;
DROP TABLE IF EXISTS log_selfreport_tpn;
DROP TABLE IF EXISTS log_selfreport_transplant;
DROP TABLE IF EXISTS log_selfreport_tysabri;
DROP TABLE IF EXISTS log_selfreport_uc;
DROP TABLE IF EXISTS log_selfreport_uveitis;
DROP TABLE IF EXISTS log_ship_log;
DROP TABLE IF EXISTS log_supplybundle;
DROP TABLE IF EXISTS log_supplyitem;
DROP TABLE IF EXISTS log_tag;
DROP TABLE IF EXISTS log_test_subform;
DROP TABLE IF EXISTS log_therapy;
DROP TABLE IF EXISTS log_unit;
DROP TABLE IF EXISTS log_us_state;
DROP TABLE IF EXISTS log_view_patient_prepurchase;
DROP TABLE IF EXISTS lsf_form_assessment_dietitian_to_assessment_dietitian_tpn;
DROP TABLE IF EXISTS lsf_form_assessment_dietitian_to_ongoing_tpn;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_aat;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_antibiotic;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_biologics;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_bisphosphonates;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_blood_thinners;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_catheter_care;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_chelation;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_chemotherapy;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_cinqair;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_enteral;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_enzyme;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_factor;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_hbig;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_hepb;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_hepc;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_hydration;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_immunotherapy;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_inotrope;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_ivig;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_lemtrada;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_methyl;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_ms;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_nursing;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_ocrevus;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_pain_management;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_radicava;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_steroid;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_tnf;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_tpn;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_assessment_tysabri;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_factor_bleed;
DROP TABLE IF EXISTS lsf_form_assessment_nurse_to_patient_catheter;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_ad;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_anemia;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_antibiotic;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_as;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_biologics;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_bisphosphonates;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_bleeding_disorder;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_blood_thinners;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_breast_cancer;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_cancer;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_catheter_care;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_cd;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_chelation;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_cinqair;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_cvid;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_enteral;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_enzyme;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_fh;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_general;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_gh;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_hbig;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_he;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_hf;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_hhome;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_hs;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_hydration;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_hyperlipidemia;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_igneuro;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_immunotherapy;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_intrathecal;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_iron_overload;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_iron_therapy;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_ivig;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_jia;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_jra;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_krystexxa;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_methyl;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_nephro;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_neutropenia;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_nursing;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_oncology;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_osteoporosis;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_other;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_pah;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_pain_management;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_prostate_cancer;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_psa;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_pso;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_skin_cancer;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_tepezza;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_thrombocytopenia;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_transplant;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_uc;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_uveitis;
DROP TABLE IF EXISTS lsf_form_assessment_to_assessment_vyepti;
DROP TABLE IF EXISTS lsf_form_assessment_to_encounter_alsfrs;
DROP TABLE IF EXISTS lsf_form_assessment_to_encounter_edss;
DROP TABLE IF EXISTS lsf_form_assessment_to_encounter_mhaq;
DROP TABLE IF EXISTS lsf_form_assessment_to_encounter_qol;
DROP TABLE IF EXISTS lsf_form_assessment_to_encounter_sf12v2;
DROP TABLE IF EXISTS lsf_form_assessment_to_encounter_wpai;
DROP TABLE IF EXISTS lsf_form_assessment_to_factor_bleed;
DROP TABLE IF EXISTS lsf_form_assessment_to_intervention;
DROP TABLE IF EXISTS lsf_form_assessment_to_patient_attachment;
DROP TABLE IF EXISTS lsf_form_assessment_to_previous_treatment;
DROP TABLE IF EXISTS lsf_form_billing_claim_to_billing_claim_med_item;
DROP TABLE IF EXISTS lsf_form_billing_claim_to_billing_claim_ncpdp_add_chrg;
DROP TABLE IF EXISTS lsf_form_billing_claim_to_billing_claim_ncpdp_cob;
DROP TABLE IF EXISTS lsf_form_billing_claim_to_billing_claim_ncpdp_dx;
DROP TABLE IF EXISTS lsf_form_billing_claim_to_billing_claim_ncpdp_item;
DROP TABLE IF EXISTS lsf_form_billing_claim_to_billing_claim_ncpdp_measure;
DROP TABLE IF EXISTS lsf_form_billing_claim_to_billing_claim_ncpdp_partial;
DROP TABLE IF EXISTS lsf_form_careplan_pharmacist_to_careplan_problem;
DROP TABLE IF EXISTS lsf_form_careplan_to_careplan_goal;
DROP TABLE IF EXISTS lsf_form_careplan_to_careplan_problem;
DROP TABLE IF EXISTS lsf_form_careplan_to_patient_attachment;
DROP TABLE IF EXISTS lsf_form_claim_to_billing_claim_med_item;
DROP TABLE IF EXISTS lsf_form_communication_to_communication_to;
DROP TABLE IF EXISTS lsf_form_complaint_to_incident;
DROP TABLE IF EXISTS lsf_form_complaint_to_intervention;
DROP TABLE IF EXISTS lsf_form_complaint_to_patient_attachment;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_aat_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ad;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ad_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_alsfrs;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_anemia;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_anemia_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_antibiotic_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_as;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_as_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_biologics;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_biologics_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_bisphosphonates;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_bisphosphonates_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_bleeding_disorder;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_bleeding_disorder_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_blood_thinners;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_blood_thinners_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_breast_cancer;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_breast_cancer_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_cancer;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_cancer_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_catheter_care;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_catheter_care_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_cd;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_cd_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_chelation;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_chelation_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_chemotherapy;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_chemotherapy_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_cinqair;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_cinqair_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_cvid;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_cvid_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_dupixent;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_dupixent_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_enteral;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_enteral_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_enzyme;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_enzyme_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_factor_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_fh;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_fh_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_general;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_general_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_gh;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_gh_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hbig;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hbig_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_he;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_he_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hepb;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hepb_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hepc;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hepc_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hf;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hf_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hhome;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hhome_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hiv;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hiv_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hpq;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hs;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hs_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_humira;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_humira_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hydration;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hydration_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hyperlipidemia;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_hyperlipidemia_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_igneuro;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_igneuro_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_immunotherapy;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_immunotherapy_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_inotrope;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_inotrope_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_intrathecal;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_intrathecal_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_iron_overload;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_iron_overload_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_iron_therapy;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_iron_therapy_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ivig;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ivig_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ivig_cidp;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ivig_immdef;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ivig_itp;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ivig_mmn;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ivig_ms;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ivig_mygrav;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ivig_myositis;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ivig_pemphigus;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ivig_stiffps;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_jia;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_jia_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_jra;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_jra_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_krystexxa;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_krystexxa_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_lemtrada_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_methyl;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_methyl_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_mhaq;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_monoclon;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_monoclon_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ms;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ms_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_nephro;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_nephro_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_neutropenia;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_neutropenia_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_nursing;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_nursing_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ocrevus;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ocrevus_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_osteoporosis;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_osteoporosis_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_other;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_other_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_pah;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_pah_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_pain_management;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_pain_management_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_prostate_cancer;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_prostate_cancer_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_psa;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_psa_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_pso;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_pso_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_psoriasis;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_psoriasis_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_qol;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ra;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_ra_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_radicava_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_rituxan;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_rituxan_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_sf12v2;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_skin_cancer;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_skin_cancer_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_soliris;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_soliris_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_steroid;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_steroid_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_tepezza;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_tepezza_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_thrombocytopenia;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_thrombocytopenia_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_tnf_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_tnf_crohns;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_tnf_parthritis;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_tnf_psoriasis;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_tnf_rheumatology;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_tnf_ulccolitis;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_tpn_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_transplant;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_transplant_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_tysabri;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_tysabri_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_uc;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_uc_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_uveitis;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_uveitis_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_vyepti;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_vyepti_admin;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_wat;
DROP TABLE IF EXISTS lsf_form_encounter_to_encounter_wpai;
DROP TABLE IF EXISTS lsf_form_encounter_to_factor_bleed;
DROP TABLE IF EXISTS lsf_form_encounter_to_intervention;
DROP TABLE IF EXISTS lsf_form_encounter_to_patient_catheter;
DROP TABLE IF EXISTS lsf_form_incoming_fax_to_patient_attachment;
DROP TABLE IF EXISTS lsf_form_intake_to_patient_attachment;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_aat;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_antibiotic;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_bisphosphonates;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_blood_thinners;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_chemotherapy;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_cinqair;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_enteral;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_factor;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_hepb;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_hepc;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_ig;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_inotrope;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_lemtrada;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_ms;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_ocrevus;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_pain_management;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_radicava;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_steroid;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_tnf;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_tpn;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_assessment_tysabri;
DROP TABLE IF EXISTS lsf_form_nursing_assessment_to_factor_bleed;
DROP TABLE IF EXISTS lsf_form_ongoing_to_encounter_alsfrs;
DROP TABLE IF EXISTS lsf_form_ongoing_to_encounter_mhaq;
DROP TABLE IF EXISTS lsf_form_ongoing_to_encounter_phq;
DROP TABLE IF EXISTS lsf_form_ongoing_to_encounter_qol;
DROP TABLE IF EXISTS lsf_form_ongoing_to_encounter_sf12v2;
DROP TABLE IF EXISTS lsf_form_ongoing_to_encounter_wpai;
DROP TABLE IF EXISTS lsf_form_ongoing_to_factor_bleed;
DROP TABLE IF EXISTS lsf_form_ongoing_to_intervention;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_ad;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_anemia;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_as;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_biologics;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_bisphosphonates;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_bleeding_disorder;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_blood_thinners;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_breast_cancer;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_cancer;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_catheter_care;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_cd;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_chelation;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_cinqair;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_cvid;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_enteral;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_enzyme;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_fh;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_general;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_gh;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_hbig;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_he;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_hf;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_hhome;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_hs;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_hydration;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_hyperlipidemia;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_igneuro;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_immunotherapy;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_intrathecal;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_iron_overload;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_iron_therapy;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_ivig;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_jia;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_jra;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_krystexxa;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_lemtrada;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_methyl;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_nephro;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_neutropenia;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_nursing;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_oncology;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_osteoporosis;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_other;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_pah;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_pain_management;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_prostate_cancer;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_psa;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_pso;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_ra;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_rituxan;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_skin_cancer;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_steroid;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_tepezza;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_thrombocytopenia;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_tnf;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_transplant;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_uc;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_uveitis;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ongoing_vyepti;
DROP TABLE IF EXISTS lsf_form_ongoing_to_patient_attachment;
DROP TABLE IF EXISTS lsf_form_ongoing_to_ship_log;
DROP TABLE IF EXISTS lsf_form_patient_dispense_to_patient_dispense_item;
DROP TABLE IF EXISTS lsf_form_patient_external_order_to_patient_adherence;
DROP TABLE IF EXISTS lsf_form_patient_external_order_to_patient_external_dispense;
DROP TABLE IF EXISTS lsf_form_patient_lab_to_patient_lab_draw;
DROP TABLE IF EXISTS lsf_form_patient_schedule_encounter_to_patient_sch_enc_rpt_appt;
DROP TABLE IF EXISTS lsf_form_patient_schedule_lab_to_patient_sch_lab_rpt_appt;
DROP TABLE IF EXISTS lsf_form_patient_schedule_selfreport_to_pat_sch_selfrp_rpt_appt;
DROP TABLE IF EXISTS lsf_form_patient_supply_to_patient_supply_factor;
DROP TABLE IF EXISTS lsf_form_portal_attachment_to_patient_attachment;
DROP TABLE IF EXISTS lsf_form_sales_contact_to_patient_attachment;
DROP TABLE IF EXISTS lsf_form_salesaccount_to_patient_attachment;
DROP TABLE IF EXISTS lsf_form_selfreport_to_encounter_qol;
DROP TABLE IF EXISTS lsf_form_selfreport_to_encounter_sf12v2;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_aat;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_ad;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_anemia;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_antibiotic;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_as;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_biologics;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_bisphosphonates;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_bleeding_disorder;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_blood_thinners;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_breast_cancer;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_cancer;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_catheter_care;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_cd;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_chelation;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_chemotherapy;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_cvid;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_dupixent;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_enteral;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_enzyme;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_factor;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_fh;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_general;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_gh;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_hbig;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_he;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_hepb;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_hepc;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_hf;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_hiv;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_hs;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_hydration;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_hyperlipidemia;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_igneuro;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_immunotherapy;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_inotrope;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_intrathecal;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_iron_overload;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_iron_therapy;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_ivig;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_jia;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_jra;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_krystexxa;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_methyl;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_ms;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_nephro;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_neutropenia;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_nursing;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_osteoporosis;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_other;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_pah;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_pain_management;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_prostate_cancer;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_psa;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_pso;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_psoriasis;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_ra;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_radicava;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_skin_cancer;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_soliris;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_steroid;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_tepezza;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_thrombocytopenia;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_tnf;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_tnf_crohns;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_tnf_parthritis;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_tnf_psoriasis;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_tnf_rheumatology;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_tnf_ulccolitis;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_tpn;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_transplant;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_tysabri;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_uc;
DROP TABLE IF EXISTS lsf_form_selfreport_to_selfreport_uveitis;
DROP TABLE IF EXISTS migration_log;
DROP TABLE IF EXISTS permission_context;
DROP TABLE IF EXISTS session;
DROP TABLE IF EXISTS session_data;
DROP TABLE IF EXISTS sf_form_assessment_dietitian_to_assessment_dietitian_tpn;
DROP TABLE IF EXISTS sf_form_assessment_dietitian_to_ongoing_tpn;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_aat;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_antibiotic;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_biologics;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_bisphosphonates;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_blood_thinners;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_catheter_care;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_chelation;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_chemotherapy;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_cinqair;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_enteral;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_enzyme;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_factor;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_hbig;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_hepb;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_hepc;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_hydration;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_immunotherapy;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_inotrope;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_ivig;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_lemtrada;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_methyl;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_ms;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_nursing;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_ocrevus;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_pain_management;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_radicava;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_steroid;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_tepezza;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_tnf;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_tpn;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_assessment_tysabri;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_factor_bleed;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_patient_attachment;
DROP TABLE IF EXISTS sf_form_assessment_nurse_to_patient_catheter;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_ad;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_anemia;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_antibiotic;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_as;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_biologics;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_bisphosphonates;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_bleeding_disorder;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_blood_thinners;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_breast_cancer;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_cancer;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_cardiology;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_catheter_care;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_cd;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_chelation;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_cinqair;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_coag;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_cvid;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_enteral;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_enzyme;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_fh;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_general;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_gh;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_hbig;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_he;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_hf;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_hhome;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_hs;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_hydration;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_hyperlipidemia;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_igneuro;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_immunotherapy;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_intrathecal;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_iron_overload;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_iron_therapy;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_ivig;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_jia;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_jra;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_krystexxa;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_methyl;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_nephro;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_neutropenia;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_nursing;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_oncology;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_osteoarthritis;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_osteoporosis;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_other;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_pah;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_pain_management;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_prostate_cancer;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_psa;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_pso;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_scig;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_skin_cancer;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_tepezza;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_thrombocytopenia;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_transplant;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_uc;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_ultomiris;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_uveitis;
DROP TABLE IF EXISTS sf_form_assessment_to_assessment_vyepti;
DROP TABLE IF EXISTS sf_form_assessment_to_encounter_alsfrs;
DROP TABLE IF EXISTS sf_form_assessment_to_encounter_edss;
DROP TABLE IF EXISTS sf_form_assessment_to_encounter_mhaq;
DROP TABLE IF EXISTS sf_form_assessment_to_encounter_qol;
DROP TABLE IF EXISTS sf_form_assessment_to_encounter_sf12v2;
DROP TABLE IF EXISTS sf_form_assessment_to_encounter_wpai;
DROP TABLE IF EXISTS sf_form_assessment_to_factor_bleed;
DROP TABLE IF EXISTS sf_form_assessment_to_intervention;
DROP TABLE IF EXISTS sf_form_assessment_to_patient_attachment;
DROP TABLE IF EXISTS sf_form_assessment_to_previous_treatment;
DROP TABLE IF EXISTS sf_form_billing_claim_to_billing_claim_med_item;
DROP TABLE IF EXISTS sf_form_billing_claim_to_billing_claim_ncpdp_add_chrg;
DROP TABLE IF EXISTS sf_form_billing_claim_to_billing_claim_ncpdp_cob;
DROP TABLE IF EXISTS sf_form_billing_claim_to_billing_claim_ncpdp_dx;
DROP TABLE IF EXISTS sf_form_billing_claim_to_billing_claim_ncpdp_item;
DROP TABLE IF EXISTS sf_form_billing_claim_to_billing_claim_ncpdp_measure;
DROP TABLE IF EXISTS sf_form_billing_claim_to_billing_claim_ncpdp_partial;
DROP TABLE IF EXISTS sf_form_careplan_pharmacist_to_careplan_problem;
DROP TABLE IF EXISTS sf_form_careplan_to_careplan_goal;
DROP TABLE IF EXISTS sf_form_careplan_to_careplan_problem;
DROP TABLE IF EXISTS sf_form_careplan_to_patient_attachment;
DROP TABLE IF EXISTS sf_form_claim_to_billing_claim_med_item;
DROP TABLE IF EXISTS sf_form_communication_to_communication_to;
DROP TABLE IF EXISTS sf_form_company_test_to_test_subform;
DROP TABLE IF EXISTS sf_form_complaint_to_incident;
DROP TABLE IF EXISTS sf_form_complaint_to_intervention;
DROP TABLE IF EXISTS sf_form_complaint_to_patient_attachment;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_aat_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ad;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ad_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_alsfrs;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_anemia;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_anemia_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_antibiotic_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_as;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_as_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_biologics;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_biologics_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_bisphosphonates;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_bisphosphonates_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_bleeding_disorder;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_bleeding_disorder_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_blood_thinners;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_blood_thinners_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_breast_cancer;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_breast_cancer_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_cancer;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_cancer_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_catheter_care;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_catheter_care_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_cd;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_cd_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_chelation;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_chelation_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_chemotherapy;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_chemotherapy_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_cinqair;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_cinqair_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_coag;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_coag_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_cvid;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_cvid_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_dupixent;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_dupixent_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_enteral;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_enteral_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_enzyme;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_enzyme_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_factor_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_fh;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_fh_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_general;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_general_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_gh;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_gh_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hbig;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hbig_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_he;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_he_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hepb;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hepb_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hepc;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hepc_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hf;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hf_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hhome;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hhome_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hiv;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hiv_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hpq;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hs;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hs_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_humira;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_humira_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hydration;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hydration_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hyperlipidemia;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_hyperlipidemia_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_igneuro;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_igneuro_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_immunotherapy;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_immunotherapy_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_inotrope;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_inotrope_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_intrathecal;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_intrathecal_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_iron_overload;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_iron_overload_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_iron_therapy;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_iron_therapy_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ivig;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ivig_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ivig_cidp;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ivig_immdef;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ivig_itp;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ivig_mmn;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ivig_ms;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ivig_mygrav;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ivig_myositis;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ivig_pemphigus;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ivig_stiffps;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_jia;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_jia_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_jra;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_jra_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_krystexxa;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_krystexxa_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_lemtrada_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_methyl;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_methyl_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_mhaq;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_monoclon;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_monoclon_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ms;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ms_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_nephro;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_nephro_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_neutropenia;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_neutropenia_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_nursing;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_nursing_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ocrevus;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ocrevus_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_osteoporosis;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_osteoporosis_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_other;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_other_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_pah;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_pah_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_pain_management;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_pain_management_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_prostate_cancer;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_prostate_cancer_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_psa;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_psa_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_pso;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_pso_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_psoriasis;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_psoriasis_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_qol;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ra;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ra_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_radicava_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_rituxan;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_rituxan_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_scig;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_scig_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_sf12v2;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_skin_cancer;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_skin_cancer_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_soliris;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_soliris_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_steroid;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_steroid_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_tepezza;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_tepezza_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_thrombocytopenia;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_thrombocytopenia_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_tnf_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_tnf_crohns;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_tnf_parthritis;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_tnf_psoriasis;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_tnf_rheumatology;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_tnf_ulccolitis;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_tpn_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_transplant;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_transplant_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_tysabri;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_tysabri_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_uc;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_uc_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ultomiris;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_ultomiris_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_uveitis;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_uveitis_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_vyepti;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_vyepti_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_vyvgart;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_vyvgart_admin;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_wat;
DROP TABLE IF EXISTS sf_form_encounter_to_encounter_wpai;
DROP TABLE IF EXISTS sf_form_encounter_to_factor_bleed;
DROP TABLE IF EXISTS sf_form_encounter_to_intervention;
DROP TABLE IF EXISTS sf_form_encounter_to_patient_catheter;
DROP TABLE IF EXISTS sf_form_incoming_fax_to_patient_attachment;
DROP TABLE IF EXISTS sf_form_intake_to_patient_attachment;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_aat;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_antibiotic;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_bisphosphonates;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_blood_thinners;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_chemotherapy;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_cinqair;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_enteral;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_factor;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_hepb;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_hepc;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_ig;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_inotrope;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_lemtrada;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_ms;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_ocrevus;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_pain_management;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_radicava;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_steroid;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_tnf;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_tpn;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_assessment_tysabri;
DROP TABLE IF EXISTS sf_form_nursing_assessment_to_factor_bleed;
DROP TABLE IF EXISTS sf_form_ongoing_to_encounter_alsfrs;
DROP TABLE IF EXISTS sf_form_ongoing_to_encounter_mhaq;
DROP TABLE IF EXISTS sf_form_ongoing_to_encounter_phq;
DROP TABLE IF EXISTS sf_form_ongoing_to_encounter_qol;
DROP TABLE IF EXISTS sf_form_ongoing_to_encounter_sf12v2;
DROP TABLE IF EXISTS sf_form_ongoing_to_encounter_wpai;
DROP TABLE IF EXISTS sf_form_ongoing_to_factor_bleed;
DROP TABLE IF EXISTS sf_form_ongoing_to_intervention;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_ad;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_anemia;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_as;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_biologics;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_bisphosphonates;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_bleeding_disorder;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_blood_thinners;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_breast_cancer;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_cancer;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_cardiology;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_catheter_care;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_cd;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_chelation;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_cinqair;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_coag;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_cvid;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_enteral;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_enzyme;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_fh;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_general;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_gh;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_hbig;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_he;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_hf;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_hhome;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_hs;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_hydration;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_hyperlipidemia;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_igneuro;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_immunotherapy;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_intrathecal;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_iron_overload;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_iron_therapy;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_ivig;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_jia;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_jra;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_krystexxa;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_lemtrada;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_methyl;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_nephro;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_neutropenia;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_nursing;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_oncology;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_osteoarthritis;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_osteoporosis;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_other;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_pah;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_pain_management;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_prostate_cancer;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_psa;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_pso;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_ra;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_rituxan;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_scig;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_skin_cancer;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_steroid;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_tepezza;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_thrombocytopenia;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_tnf;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_transplant;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_uc;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_ultomiris;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_uveitis;
DROP TABLE IF EXISTS sf_form_ongoing_to_ongoing_vyepti;
DROP TABLE IF EXISTS sf_form_ongoing_to_patient_attachment;
DROP TABLE IF EXISTS sf_form_ongoing_to_ship_log;
DROP TABLE IF EXISTS sf_form_patient_dispense_to_patient_dispense_item;
DROP TABLE IF EXISTS sf_form_patient_external_order_to_patient_adherence;
DROP TABLE IF EXISTS sf_form_patient_external_order_to_patient_external_dispense;
DROP TABLE IF EXISTS sf_form_patient_lab_to_patient_lab_draw;
DROP TABLE IF EXISTS sf_form_patient_schedule_encounter_to_patient_attachment;
DROP TABLE IF EXISTS sf_form_patient_schedule_encounter_to_patient_sch_enc_rpt_appt;
DROP TABLE IF EXISTS sf_form_patient_schedule_lab_to_patient_sch_lab_rpt_appt;
DROP TABLE IF EXISTS sf_form_patient_schedule_selfreport_to_pat_sch_selfrp_rpt_appt;
DROP TABLE IF EXISTS sf_form_patient_supply_to_patient_supply_factor;
DROP TABLE IF EXISTS sf_form_portal_attachment_to_patient_attachment;
DROP TABLE IF EXISTS sf_form_sales_account_to_patient_attachment;
DROP TABLE IF EXISTS sf_form_sales_contact_to_patient_attachment;
DROP TABLE IF EXISTS sf_form_salesaccount_to_patient_attachment;
DROP TABLE IF EXISTS sf_form_selfreport_to_encounter_qol;
DROP TABLE IF EXISTS sf_form_selfreport_to_encounter_sf12v2;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_aat;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_ad;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_anemia;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_antibiotic;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_as;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_biologics;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_bisphosphonates;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_bleeding_disorder;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_blood_thinners;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_breast_cancer;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_cancer;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_catheter_care;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_cd;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_chelation;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_chemotherapy;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_cvid;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_dupixent;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_enteral;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_enzyme;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_factor;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_fh;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_general;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_gh;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_hbig;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_he;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_hepb;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_hepc;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_hf;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_hiv;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_hs;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_hydration;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_hyperlipidemia;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_igneuro;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_immunotherapy;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_inotrope;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_intrathecal;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_iron_overload;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_iron_therapy;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_ivig;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_jia;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_jra;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_krystexxa;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_methyl;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_ms;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_nephro;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_neutropenia;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_nursing;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_osteoporosis;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_other;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_pah;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_pain_management;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_prostate_cancer;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_psa;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_pso;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_psoriasis;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_ra;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_radicava;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_skin_cancer;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_soliris;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_steroid;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_tepezza;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_thrombocytopenia;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_tnf;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_tnf_crohns;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_tnf_parthritis;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_tnf_psoriasis;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_tnf_rheumatology;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_tnf_ulccolitis;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_tpn;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_transplant;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_tysabri;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_uc;
DROP TABLE IF EXISTS sf_form_selfreport_to_selfreport_uveitis;
DROP TABLE IF EXISTS token_auth;
DROP TABLE IF EXISTS wf_rule;
DROP TABLE IF EXISTS wf_rule_audit;
DROP TABLE IF EXISTS wf_schedule_log;
DROP TABLE IF EXISTS wf_subscription;