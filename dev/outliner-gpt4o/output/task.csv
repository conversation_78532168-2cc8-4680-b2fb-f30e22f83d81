Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Integrations;Storage;S3 API;Feature;Centralize S3 Integration;Implement centralized S3 integration similar to existing setup.;Brandon;
Integrations;Storage;S3 API;Feature;Minio Integration;Integrate Minio as an open-source S3 alternative in the k8s cluster.;Brandon;
Integrations;Communication;SMS;Feature;Twilio Integration;Integrate Twilio for SMS services.;Brandon;
Integrations;Communication;SMS;Feature;Telnyx Integration;Integrate Telnyx for SMS services and streamline the 10DLC auth process.;Brandon;
Integrations;Communication;Email;Feature;Mandrill Integration;Integrate Mandrill for email templating.;Brandon;
Integrations;Communication;Email;Feature;Sendgrid Integration;Integrate Sendgrid for email templating and use a newer templating engine/service.;Brandon;
Integrations;Notifications;Push;Feature;Slack Integration;Integrate Slack for notifications.;Brandon;
Integrations;Notifications;Push;Feature;Teams Integration;Integrate Microsoft Teams for notifications.;Brandon;
Integrations;Notifications;Push;Feature;Client Push Notifications;Implement push notifications to the client.;Brandon;
Integrations;Notifications;Push;Feature;Firebase Integration;Integrate Firebase for push notifications.;Brandon;
Integrations;Notifications;Push;Feature;Electron-based Notifications;Implement native electron-ish notifications since a native app won't be available for a while.;Brandon;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Integrations;Communication;Fax;Feature;Integrate Phaxio for faxing;Integrate Phaxio for faxing with per customer number at $1.00 per reserved number, dedicated callback URI, and outbound support.;Brandon;
Integrations;Communication;Fax;Feature;Integrate Ringcentral for faxing;Support Ringcentral faxing by sending an email to their email-to-fax address with correct templating.;Brandon;
Integrations;Configuration;Address Completion;Feature;Integrate Radar for geo address;Use Radar via Admin server proxy for bulk pricing for geo address completion.;Brandon;
Integrations;Compliance;SSO;Feature;Integrate Keycloak for SSO;Integrate Keycloak for single sign-on (SSO) functionality.;Brandon;
Integrations;Compliance;SSO;Feature;Integrate Gluu for SSO;Integrate Gluu or similar solutions like goauthentik.io for single sign-on (SSO) functionality.;Brandon;
Infrastructure;Monitoring;Uptime;Feature;Implement basic keepalive/uptime monitoring;Implement basic keepalive and uptime monitoring for system health.;Brandon;
Infrastructure;Monitoring;K8s;Feature;Implement full K8s monitoring;Implement full Kubernetes (K8s) monitoring for system health and performance.;Brandon;
Infrastructure;Monitoring;Failover;Feature;Conduct failover testing and alerting;Conduct failover testing and set up alerting mechanisms.;Brandon;
Infrastructure;Monitoring;Sentry;Feature;Integrate Sentry for exception monitoring;Integrate Sentry to monitor and report exceptions.;Brandon;
Infrastructure;Monitoring;Performance;Feature;Implement performance monitoring of request lifecycle;Monitor the performance of the request lifecycle to identify bottlenecks.;Brandon;
Infrastructure;Monitoring;Logging;Feature;Set up centralized logging;Implement centralized logging for better monitoring and troubleshooting.;Brandon;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Integrations;Surescripts;Certification;Task;Test all required message types;Test all required message types and make sure we satisfy the minimal basic requirements for Surescripts certification as well as Drummond certification (DEA vendor);Brandon;
Integrations;Payments;Authorize.net;Task;Implement Payment Gateway;Implement a middleware API that can handle many different payment rails, possibly using Authorize.net;Brandon;
Integrations;Claims;Adjudication;Task;Integrate Change Healthcare;Integrate Change Healthcare as the primary NCPDP claim processor;Brandon;
Integrations;Claims;Adjudication;Task;Implement Medical Claims Processing;Implement medical claims processing;Brandon;
Integrations;Claims;Adjudication;Task;Integrate Redsail;Integrate Redsail as a secondary or additional primary NCPDP claim processor;Brandon;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Integrations;Insurance;Payer;Feature;Implement back office payer workflow for non-EDI payers;Implement back office payer workflow for non-EDI payers, which can take up to 48 hours for eligibility verification.;Brandon;
Integrations;Insurance;Payer;Feature;Parse JSON for EligibilityInquiry API;Use the EligibilityInquiry API to get information about non-EDI payers and parse the JSON into a usable format for NES to create a PDF.;Brandon;
Integrations;Insurance;Payer;Feature;Mechanism to check eligibility request completion;Implement a mechanism to check after 24 hours if the eligibility request has been completed using pgboss cron, fetch the eligibility data, and attempt to fetch a PDF for the requestID.;Brandon;
Integrations;Insurance;Payer;Feature;Upload completed PDF to S3 and update databases;If there is a PDF and the response is completed, upload to S3, update the customer_id.db and default.db to set the s3_filehash for the requestID, and send a PUT to the Clara NES server with the data.;Brandon;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Infrastructure;Deployment;Lint;Feature;Use an AST parser against the compiled DSL;Use an AST parser against the compiled DSL under the bd-dsl directory (should be in ../bd-dsl for /bd or allow a setting in the plugin to set a custom directory). If coffee is not compiled, compile the DSL first before loading into the AST.;Brandon;
Infrastructure;Deployment;Lint;Task;Check that all validators from the DSL are implemented;For Handlers (look for files where with class *BaseView extends CRView), check that all validators from the DSL are implemented (could also just implement as a warning when deploying the DSL);Brandon;
Infrastructure;Deployment;Lint;Task;Check for dead validators;For Handlers (look for files where with class *BaseView extends CRView), check for dead validators that aren't in the DSL;Brandon;
Infrastructure;Deployment;Lint;Task;Check that fields used in the handler aren't in the DSL references;For Handlers (look for files where with class *BaseView extends CRView), check that fields used in the handler aren't in the DSL references;Brandon;
Infrastructure;Deployment;Lint;Task;Check for sync calls;For Handlers (look for files where with class *BaseView extends CRView), check for sync calls, should all be async or show an error;Brandon;
Infrastructure;Deployment;Lint;Task;Check API calls;For Handlers (look for files where with class *BaseView extends CRView), check API calls to make sure path exists in nes;Brandon;
Infrastructure;Deployment;Lint;Task;Check that handlers have the extends portion;For Handlers (look for files where with class *BaseView extends CRView), check that handlers also have the extends portion at the bottom i.e. class Billing_claim_genericHandlerView extends Billing_claim_genericHandlerBaseView;Brandon;
Infrastructure;Deployment;Lint;Task;Check validator/transform functions in readonly mode;For Handlers (look for files where with class *BaseView extends CRView), check that any validator/transform functions are not setting any values if in readonly mode;Brandon;
Infrastructure;Deployment;Lint;Task;Check field types;For Handlers (look for files where with class *BaseView extends CRView), check that field types are getting set to the appropriate value types (i.e. date fields are MM/DD/YYYY etc);Brandon;
Infrastructure;Deployment;Lint;Task;Check for dead functions;For Handlers (look for files where with class *BaseView extends CRView), check for dead functions that aren't being used;Brandon;
Infrastructure;Deployment;Lint;Task;Check for validator loops;For Handlers (look for files where with class *BaseView extends CRView), check for validator loops (i.e. a validator calls a function which sets the field that contains the validator and has true, true on setting value_field);Brandon;
Infrastructure;Deployment;Lint;Task;Check usage of non-required fields;For Handlers (look for files where with class *BaseView extends CRView), check if we are using non-required fields without first seeing if they have a value. (using ? i.e. this.data.fieldname?);Brandon;
Infrastructure;Deployment;Lint;Task;Check validators/transforms using dd.value_field;For Handlers (look for files where with class *BaseView extends CRView), check that validators/transforms that use different fields through dd.value_field are attached to every field that it passes through (warning only);Brandon;
Infrastructure;Deployment;Lint;Task;Check async calls for .fail function;For Handlers (look for files where with class *BaseView extends CRView), check that all async calls have a corresponding .fail function defined;Brandon;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Infrastructure;Deployment;Lint;Task;Check Validators / Transforms;Check Validators / Transforms that are not implemented in the client.;Brandon;
Infrastructure;Deployment;Lint;Task;Check offscreen fields;Check that offscreen fields that have model.if defined are also in the model.sections (otherwise if logic doesn't work);Brandon;
Infrastructure;Deployment;Lint;Task;Check foreign key references;All foreign key referenced fields are also listed in the indexes;Brandon;
Infrastructure;Deployment;Lint;Task;Check fields in if logic;Check that fields listed in the if logic are only listed for one field (it doesn't work if the same field is show/hidden in multiple ifs, in the same field.model.if is fine though);Brandon;
Infrastructure;Deployment;Lint;Task;Check offscreen fields in model.sections;All fields not listed as offscreen are in the model.sections;Brandon;
Infrastructure;Deployment;Lint;Task;Gridedit fields warning;Warning if gridedit fields have more than 5 fields listed in the gridfields;Brandon;
Infrastructure;Deployment;Lint;Task;Gridedit required fields warning;Warning if gridedit fields have required fields not listed in the gridfields if gridadd != ‘flyout';Brandon;
Infrastructure;Deployment;Lint;Task;Check model.source forms;Check that listed model.source forms exist;Brandon;
Infrastructure;Deployment;Lint;Task;Check subform fields;Check that subform fields are in their own section_group by themselves.;Brandon;
Infrastructure;Deployment;Lint;Task;Model.source array warning;Warning If model.source is an array, and has <= 5 entries, field.view.control should be ‘radio' or ‘checkbox' instead of ‘select';Brandon;
Infrastructure;Deployment;Lint;Task;Model.view.control error;Error If model.view.control = ‘radio' and model.multi = true, control should be select or checkbox.;Brandon;
Infrastructure;Deployment;Lint;Task;Check model.auto_name references;Check that model.auto_name is not referencing fields contained in the if: logic. Provide a warning but not an error if this is the case.;Brandon;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Foundations;DSL;Prefill;Feature;Transforms that run once only on form create;Ideally make this an option in the PrefillSelect transform.;Brandon;
Foundations;DSL;Tooltip;Feature;Help tooltips;Fields that have a tooltip that will show on hover.;Brandon;
Foundations;DSL;Validator;Feature;Validator for matching values;Validator that checks if the value of 2 fields (or a tuple of fields) have the same values/ids and presents an error if so.;Brandon;
Foundations;DSL;If Logic;Feature;Clear fields option;An option under if: to clear_fields with a list of fields to clear the values from (if hidden too).;Brandon;
Foundations;DSL;Prefill;Feature;Prefill from parent form;An option in prefill to pull from the parent form inside of subforms.;Brandon;
Foundations;DSL;Prefill;Feature;Prefill using API endpoint;An option in the Prefill transform to use an API endpoint and pass in the form's current data and prefill in with the results.;Brandon;
Foundations;DSL;Component;Feature;XML data field;A new type of field for “xml” data that will show an xml icon that when clicked, opens up a modal with the xml data.;Brandon;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Modules;Queue;Performance;Bug;Performance issue;Address performance issues in the queue module.;Osama;
Modules;Queue;User Comments;Feature;User comments support functionality;Add functionality to support user comments in the queue.;Osama;
Modules;Queue;Background Refresh;Feature;Background auto refresh;Implement background auto-refresh for the queue.;Osama;
Modules;Queue;Workflow;Feature;Workflow swimlanes drag and drop;Enable drag and drop for workflow swimlanes and force refresh on drop.;Osama;
Modules;Queue;Workflow;Feature;Integrating workflow and action events;Integrate workflow and action events in the workflow queue.;Osama;
Modules;Queue;Security;Feature;Security integration;Integrate security features into the queue module.;Osama;
Foundations;NES;Handler;Bug;GET calls not working with filters;Fix GET calls not working with filters on the client side for field sourceid=code.;Osama;
Foundations;DSL;Prefill;Bug;Redundant API calls in DSL record prefilling;Optimize DSL record prefilling to avoid redundant API calls.;Osama;
Foundations;DSL;If Logic;Feature;Make fields readonly based on if condition;Implement functionality to make fields readonly based on if condition.;Osama;
Foundations;DSL;Validator;Feature;Client Validation for can_sync fields;Add client validation for can_sync fields on DSL level to keep the IDs consistent.;Osama;
Foundations;DSL;Filtering;Feature;Handle advanced filters in find view;Handle advanced filters in find view separately from basic filters.;Osama;
Foundations;DSL;Tabs;Feature;Section tabs for DSL sections;Add support for section tabs in DSL sections.;Osama;
Foundations;DSL;Grid View;Feature;Highlight row in grid on save/update;Highlight just the row in the grid on DSL form save/update instead of refreshing the entire grid.;Osama;
Foundations;DSL;Grid View;Bug;Grid count issue on list view;Fix the grid count issue on list view and add pagination support.;Osama;
Foundations;DSL;Component;Bug;Double section header in subforms;Fix the issue of double section headers in subforms when all fields in a section are hidden.;Osama;
Foundations;DSL;Component;Feature;Make archived field checkbox;Add a checkbox to mark fields as archived.;Osama;
Foundations;DSL;Filtering;Feature;Add deleted field to form filters;Add support for filtering by deleted fields in form filters.;Osama;
Foundations;Query;Filtering;Feature;Remove archived field from query module;Remove the archived field from the query module's find view.;Osama;
Modules;Frontend;Tabs;Feature;Add edit support to DSLTabbedView;Add edit support to DSLTabbedView in the frontend.;Osama;
Modules;Frontend;Search;Feature;List view search field revert to auto_name;Revert to auto_name if the search field is not found in DSL list view.;Osama;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Modules;Workflow;Wizard;Feature;Need if condition in wizard to show/hide field;Implement if condition in wizard to dynamically show or hide fields based on conditions.;Osama;
Modules;Workflow;Wizard;Feature;Handle dependent form saves within the same wizard instance;Ensure that form 1 is saved before form 2 within the same wizard instance, as form 2 references the ID of form 1.;Osama;
Modules;Workflow;Wizard;Bug;Wizard has an issue with same form showing in different steps;Fix the issue where the same form appears in multiple steps of the wizard.;Osama;
Modules;Workflow;Wizard;Bug;On save validation issue with hidden forms;Resolve the validation issue where hidden forms are not validated correctly because they are offscreen.;Osama;
Modules;Workflow;Wizard;Feature;No preset support for list view in wizard;Add support for preset values in list view within the wizard, allowing users to add multiple forms.;Osama;
Modules;Workflow;Wizard;Feature;Automatic preset value generation;Implement automatic preset value generation for wizards opened from the queue, filling in preset values from queue data objects.;Osama;
Modules;Workflow;Wizard;Feature;Wizard review process;Develop a review process for wizards.;Osama;
Modules;Workflow;Wizard;Feature;Notification for concurrent wizard entry;Add notifications to alert users if someone else is working on the same wizard entry.;Osama;
Modules;Configuration;Company;Task;Company config usage on client side;Ensure that company configuration is used consistently across the client side.;Osama;
Modules;Navigation;Component;Task;Extra open tabs in dropdown;Display extra open tabs in a dropdown instead of using scroll.;Osama;
Modules;Navigation;Component;Bug;Fix navigation in multiple modules;Address navigation issues in various modules.;Osama;
Modules;Navigation;Component;Bug;Double click to open tab not working;Fix the issue where double-clicking to open a tab is not functioning.;Osama;
Modules;Security;Implementation;Task;Security implementation on client side;Implement security measures on the client side.;Osama;
Modules;Patient;Snapshot;Task;Update code for standard components;Update the code in multiple modules to use standard components instead of custom functionality, e.g., Snapshots in Patient and jQuery.;Osama;
Modules;Patient;Snapshot;Bug;Fix patient snapshot logic;Update patient snapshot logic to support multiple intake and care plan linking, which broke when intake_id was added to every form.;Osama;
Modules;Patient;Snapshot;Feature;Support wizard in patient snapshot;Add proper support for wizards in the patient snapshot.;Osama;
Modules;Patient;Snapshot;Feature;Integrate audit trail with log form API;Integrate the audit trail with the log form API (right-hand side widget).;Osama;
Modules;Patient;Snapshot;Feature;Show in-progress wizards for patient;Provide a place to display all in-progress wizards for a patient on the snapshot.;Osama;
Deployment;Pre Production;Bundle;Task;Bundle react.js and coffee into one;Bundle react.js and coffee into a single package and minimize the bundle size to serve just three files: app.js, react.js, and lib.js.;Osama;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Integrations;Surescripts;Refill;Task;Test refill request;Need to test refill request once we have the full dispensing workflow completed;Patrick;
Integrations;Surescripts;Payer;Feature;Select existing payer in pop-up;Need an option to select an existing payer in the pop-up instead of just popping up the payer creation (or the workflow generator) and work on the co-dependencies for existing forms so you can add them in the workflow if not there (i.e. missing physician record for patient_prescriber record);Patrick;
Integrations;Surescripts;Snapshot;Feature;Snapshot review;Need some kind of snapshot review with just the basics when viewing the details tab;Patrick;
Modules;Inventory;Transfers;Feature;Handle transfers until receipt;Add process for handling transfers until receipt and put inventory “on-hold”;Patrick;
Modules;Inventory;Receipt;Feature;Handle incoming inventory;Need to handle incoming inventory from purchase receipt to import serial and lot numbers (should be CSV or excel file?);Patrick;
Modules;Inventory;Scanning;Feature;Scan in inventory;Need to be able to “scan in” inventory;Patrick;
Modules;Inventory;Nursing Agency;Feature;Tie nursing agency record to billable item;Have a way to tie in nursing agency record to a billable item for nursing hours;Patrick;
Modules;Ordering;Diagnoses;Feature;Select multiple diagnoses;Need some kind of embedded grid view to be able to select multiple diagnoses against an order and order item. (right now it is only 1);Patrick;
Modules;Ordering;Payers;Feature;Assign payers against order;Need some kind of grid view to assign the payers against the order / order item;Patrick;
Modules;Ordering;Compounding;Feature;Support compounding;Need to support compounding;Patrick;
Modules;Ordering;TPN;Feature;TPN machine integration;TPN machine integration;Patrick;
Modules;Ordering;Factor Dosing;Feature;Factor dosing module;Factor dosing module (with bleed/prophy/procedure dosing);Patrick;
Modules;Ordering;Payers;Feature;Better logic layout for multiple payers;Need better logic layout for multiple payers against items (i.e. Medicare -> Medicare Advantage/Supplemental -> Copay);Patrick;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Modules;Claims;Builder;Feature;Build out NCPDP builder;Build out NCPDP “builder” on the backend using pg-boss;Patrick;
Modules;Claims;Schema;Feature;Build out NCPDP schema and validation;Build out NCPDP schema and validation;Patrick;
Modules;Claims;Medical;Feature;Medical Claims;Implement support for medical claims;Patrick;
Modules;Claims;Transform;Feature;Transform NCPDP to Medical Claim;Create a way to transform an NCPDP claim to a medical claim and back and forth;Patrick;
Modules;Claims;Medi-Cal;Feature;Medi-Cal support;Implement support for Medi-Cal claims;Patrick;
Modules;Claims;Invoicing;Feature;Invoicing for payer/patient;Implement invoicing for payer and patient;Patrick;
Modules;Benefits Investigation;E1;Feature;Support E1 for pharmacy benefits investigation;Implement support for the E1 for pharmacy benefits investigation;Patrick;
Modules;Prior Authorization;Form;Feature;Auto-fill PA form for physician;Create a way to auto-fill the PA form for the physician (CA is standardized);Patrick;
Modules;Prior Authorization;ePA;Feature;Look into ePA for required fields;Investigate ePA to get the list of required fields to populate for the physician;Patrick;
Modules;Prior Authorization;Tracking;Feature;Support unit tracking and renewal alerts;Implement support for unit tracking and renewal alerts;Patrick;
Modules;Intake;Workflow;Feature;Intake workflow for benefits investigation and prior auth;Create an intake workflow for benefits investigation and prior authorization;Patrick;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Modules;Frontend;Styling;Task;Standardize styling framework;Standardize styling and create a framework for consistent styling, including variables for colors, margins, paddings, and border-radius.;Mubin;
Modules;Frontend;Testing;Task;UI testing framework;Implement a UI testing framework using Playwright.;Mubin;
Modules;Global Search;Component;Task;Improve global search component;Enhance the functionality and performance of the global search component.;Mubin;
Modules;Frontend;Component;Task;Display relevant popups;Ensure relevant popups, especially error popups, are displayed correctly across the application.;Mubin;
Modules;Frontend;Styling;Task;Remove CoffeeScript styling;Transition all styling from CoffeeScript to React.;Mubin;
Modules;Frontend;Component;Task;Increase type-checking;Implement more type-checking on the React side.;Mubin;
Modules;Frontend;Cleanup;Task;Prune jQuery from React;Remove jQuery dependencies from the React side of the application.;Mubin;
Modules;Documentation;Component;Task;Create onboarding documentation;Develop official/unofficial documentation to help new developers understand the application structure and essence.;Mubin;
Infrastructure;NES;Cleanup;Task;Replace pg-escape in NES;Replace the outdated pg-escape library in NES.;Mubin;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Infrastructure;Deployment;CI/CD;Task;Fix dev ops pipeline single click create fly instance and deploy latest code;Auto deploy latest code to all live machines, take db backup before deploy.;Shoaib;DV-2
Infrastructure;Monitoring;Telemetry;Feature;Adding system alerts for memory usage and CPU usage in NES;Implement system alerts for monitoring memory and CPU usage.;Shoaib;
Infrastructure;Database;Data;Task;Update SQL files transaction block for full files;Ensure SQL files use transaction blocks for full file updates.;Shoaib;
Infrastructure;Logging;Logger;Task;Update logger to be not verbose all over;Modify logger settings to reduce verbosity.;Shoaib;
Infrastructure;Database;Data;Task;Generating test data;Create and populate test data for development and testing purposes.;Shoaib;
Infrastructure;NES;Cleanup;Task;NES clean up remove unused code;Identify and remove unused code in NES.;Shoaib;
Infrastructure;NES;Lint;Task;Standardize NES code with standard.js or equivalent;Implement code standardization using standard.js or an equivalent tool.;Shoaib;
Infrastructure;Database;Validator;Task;Standard way of doing schema validation;Establish a standardized method for schema validation.;Shoaib;
Infrastructure;Migration;Data;Task;Data import from customers;Develop a process for importing customer data.;Shoaib;
Infrastructure;Monitoring;Crash Alerts;Feature;Crash alerts;Implement crash alert notifications.;Shoaib;
Modules;Patient;App;Feature;Patient app;Develop a patient-facing application.;Shoaib;
Infrastructure;Logging;Tool;Feature;Tool to intelligently see logs;Create a tool for intelligent log analysis.;Shoaib;
Analytics;Analytics;Pipeline;Feature;Data analytics pipeline snowflake semblance;Develop a data analytics pipeline to gain insights on customer usage.;Shoaib;
Integrations;Communication;Chat;Feature;Chat-teams integration;Integrate chat functionality with Microsoft Teams.;Shoaib;
Modules;Ticketing;Issue Tracker;Feature;Ticketing system;Develop a ticketing system for issue tracking.;Shoaib;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Modules;Frontend;AppSmith;Task;App optimization handling;App optimization handling by configs of Vite, CRACO, TS config, Webpack as well as babel plugins.;ALTAF;
Modules;Frontend;Component;Task;Common methods to handle animations;Common methods to handle animations by React including less and if needs coffee than just to use only less.;ALTAF;
Modules;Frontend;Breadcrumb;Feature;Breadcrumb;Implement Breadcrumb functionality.;ALTAF;
Modules;Notifications;Push;Task;Notifications handling;Notifications handling by commonly.;ALTAF;
Modules;Frontend;Component;Feature;Custom Skeleton;Custom Skeleton that can be used in table, cards, tabs boxes etc.;ALTAF;
Modules;Frontend;Component;Task;Common component for each block;There should be a common component for each block like buttons, cards, texts, flyouts, popups Modal and drawers.;ALTAF;
Modules;Frontend;Component;Task;Handle repeated functionalities;Repeated functionalities should be handled by using custom hooks as well as functions.;ALTAF;
Modules;Frontend;Component;Task;Create constants;We should create constants as much as we can like strings, messages, numbers i.e., error messages, prop types etc.;ALTAF;
Modules;Frontend;Component;Task;Disable actions during pending requests;Disable while sending a request in its pending time actions should be disabled until request gets response or rejection.;ALTAF;
Modules;Frontend;Component;Task;Handle compact or mobile view;To handle the compact or mobile view need to rechange values of gaps, font sizes by generic method like mixins.;ALTAF;
Modules;Frontend;Component;Feature;Common select box;A common select box that should cover up all the functionalities like with static data multi select, API select, frontend select, search select and its dropdown etc.;ALTAF;
Modules;Frontend;Component;Task;Axios interceptors;Implement Axios interceptors.;ALTAF;
Modules;Frontend;Component;Feature;Data not found component;Data not found common component handle its empty covered area to point hints or a clue to its container.;ALTAF;
Modules;Frontend;Component;Feature;Spinner component;Spinner component should be in multiple variants like full page we have already but container spinner should be and its variants.;ALTAF;
Modules;Frontend;Typography;Feature;Typography;Implement Typography.;ALTAF;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Workflow;Sales;Sales Reps;Feature;Go after patients;Sales reps should go after patients, mostly factor.;Chirag;
Workflow;Sales;Sales Reps;Feature;Go after physicians;Sales reps should go after physicians/physicians group/hospital to get referrals during discharge.;Chirag;
Workflow;Sales;Sales Reps;Feature;Assign territories;Sales reps should be assigned territories that can change.;Chirag;
Workflow;Sales;Sales Reps;Feature;Alert on patient status change;Sales reps should get alerts when a patient changes status and be able to contact the physician based on this.;Chirag;
Workflow;Sales;Sales Reps;Feature;Read-only patient chart;Sales reps should be able to see read-only basics of the patient chart, excluding billing info but including dispensing.;Chirag;
Workflow;Sales;Sales Reps;Feature;Add new contacts;Sales reps should be able to add new contacts, mark them as type = physicians/physicians group, and import all data for physicians from FirstDataBank into form_physician.;Chirag;
Workflow;Sales;Sales Reps;Feature;Link contact records;Link imported physician data to contact records and include group name, address per physician in form_physician_address.;Chirag;
Workflow;Sales;Sales Reps;Feature;Track prospect to referral;Track the process from prospect to lead to referral and measure each step.;Chirag;
Workflow;Sales;Sales Reps;Feature;Set goals per rep;Set goals per rep for grams of IG/units of Factor and dollar amounts.;Chirag;
Workflow;Sales;Sales Reps;Feature;Qualify leads;Qualify leads with a budget per rep per quarter, with limits on spending on physicians/physician groups.;Chirag;
Workflow;Sales;Sales Reps;Feature;Conversion cost per lead;Figure out the conversion cost per lead brought in by sales reps, including all patients of a physician group.;Chirag;
Workflow;Sales;Sales Reps;Feature;House accounts;Manage house accounts, which might be used for non-competes and get referrals directly from physicians, possibly with commission.;Chirag;
Workflow;Intake;Fax;Feature;Handle intake via fax;Handle intake via fax, with CPR shared drive location to link to patients and referral forms by therapy using ARJS.;Chirag;
Workflow;Intake;Intake team;Feature;Verify intake info;Intake team should accept fax, verify all info, assign records to patients, create patients if needed, assign to intake teams, and mark as ignore.;Chirag;
Workflow;Intake;Intake team;Feature;Support multiple fax numbers;Support multiple fax numbers for intake.;Chirag;
Workflow;Intake;Intake team;Feature;Request info;Request info from sales reps, physicians, or patients as needed.;Chirag;
Workflow;Intake;Intake team;Feature;Run BVV;Run BVV after all info is available, including eligibility check, benefits verification, medical co-pays, pharmacy, and patient chart insurance sorting.;Chirag;
Workflow;Patient;Patient Chart;Feature;Manage patient status;Manage patient status with different levels of data entry and access based on status (Pending, Active, On Hold, Inactive/Cancelled/Referred/Deceased).;Chirag;
Workflow;Patient;Intake Status;Feature;Manage intake status;Manage intake status per insurance with steps for new prescription/order, patient insurance entry, eligibility, BVV, patient services, prior auth, claim status, and initial assessment.;Chirag;
Workflow;Patient;Billing Assessment;Feature;Link billing assessment to intake;Link successful billing assessments to intake status flags and manage billing type with multi-select therapy.;Chirag;
Workflow;Patient;Billing Assessment;Feature;Show on-service required;Show on-service required for therapy in patient intake when billing assessment exists.;Chirag;
Workflow;Dashboard;KanBan;Feature;Support KanBan;Support numeric status/lane in the dashboard for KanBan.;Chirag;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Modules;Patient;Intake;Task;Setup intake process per therapy;Determine what all should be setup as part of the intake process for each therapy.;Chirag;
Modules;Patient;Prior Auth;Task;Prior Auth / BI process;Define and implement the Prior Authorization and Benefits Investigation process.;Chirag;
Modules;Dispense;Template;Task;Creation of dispense tickets;Identify what drives the creation of dispense tickets.;Chirag;
Modules;Dispense;Template;Task;Dispense ticket template;Create a template for dispense tickets based on therapy/order.;Chirag;
Modules;Dispense;Template;Task;Delivery ticket;Create a delivery ticket with/without pricing.;Chirag;
Modules;Inventory;Movement;Task;Inventory movement;Handle inventory movement as part of the dispense ticket process.;Chirag;
Modules;Insurance;Setup;Task;Insurance plan mapping;Pre-build a database of mapping insurance plans to hours/meds.;Chirag;
Modules;Insurance;Setup;Task;Primary/secondary insurance;Figure out primary/secondary insurance beforehand, before dispense tickets.;Chirag;
Modules;Insurance;Setup;Task;Insurance impact on dispense tickets;Factor in patient's insurance companies to decide which dispense ticket items to bill/print.;Chirag;
Modules;Insurance;Setup;Task;Insurance setup per order;Setup insurance per order per product.;Chirag;
Modules;Insurance;Setup;Task;Insurance copay programs;Manage insurance copay PAP programs with $ limits and annual expiration.;Chirag;
Modules;Insurance;Medical;Task;Medical BI at dispense;Implement Medical Benefits Investigation at dispense or service.;Chirag;
Modules;Insurance;Prior Auth;Task;Prior auth process;Manually run prior auth before eligibility and rerun eligibility on Medical patients monthly.;Chirag;
Modules;Insurance;Prior Auth;Task;Automate eligibility;Make eligibility checks easier or automated.;Chirag;
Modules;Insurance;Prior Auth;Task;Store prior auth at prescription level;Store prior authorization at the prescription level with start/stop/units for the drug.;Chirag;
Modules;Insurance;Payer;Task;Payer/insurance matrix;Create a payer/insurance x supply matrix for dispense ticket billable/print-only.;Chirag;
Modules;Dispense;Template;Task;Customize dispense ticket template;Allow customization of dispense ticket template based on therapy per patient.;Chirag;
Modules;Global Changes;Requirements;Task;Care plan requirement;Define care plan requirements by pharmacist/nurse per therapy.;Chirag;
Modules;Global Changes;Requirements;Task;Intake form design;Design intake forms by therapy.;Chirag;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Modules;Configuration;User;Feature;User pref table;On smaller resolutions, switch to compact style, Shortcut for search: F1 etc., Printer selection for documents vs. label;Chirag;
Modules;Configuration;Company;Feature;Company info table;Address, billing, logo upload, phones/fax etc. - for printing vs showing on screen;Chirag;
Modules;Configuration;Territory;Feature;Territory management;ID/Name per territory, Usually zip-code, Categorized based on size of physician group, Linked to Physician / Physician Group, Physicians must be linked to Physician Group;Chirag;
Modules;Configuration;User;Feature;Status tracking;Status of: e-sign, document upload;Chirag;
Modules;Configuration;Data;Feature;Save draft for all tables;Save draft for all tables without validation. Query all tables for non draft only;Chirag;
Modules;Frontend;Responsive;Feature;Mobile/Tablet support for Homebase;Mobile = Reports, Tablet = Everything (auto-switch to compact mode);Chirag;
Modules;Frontend;Component;Feature;Summary counts;Summary counts at top of every table - easy filter;Chirag;
Modules;Frontend;Component;Feature;Dummy answers in form;Dummy answers in form upon shortcut;Chirag;
Modules;Database;Data;Feature;Shared tables;Drug monographs, Zip-code, NDC pricing/fees, NPI Physicians;Chirag;
Modules;Database;Data;Feature;NPI Physicians;form_physician can profile on NPI, subform for multiple addresses;Chirag;
Modules;Compliance;Audit;Feature;Drug monographs and work tickets;Drug monographs and work tickets to that as well. Work tickets are how they compound;Chirag;
Modules;Compliance;EVV;Feature;EVV;EVV https://www.medicaid.gov/medicaid/home-community-based-services/guidance/electronic-visit-verification-evv/index.html - Yes needed;Chirag;
Modules;DSL;Editor;Feature;DSL editor with table version;DSL editor with table version sooner? 2023 Q2;Chirag;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Modules;Claims;Submit/Resubmit;Task;Allow editing specific fields of claims before submit/resubmit;Determine how much should be editable and what should be locked. E.g., can biller insert rows for inventory unrelated to invoice/dispense? Can they modify dollars/hours?;Chirag;
Modules;Claims;Submit/Resubmit;Task;Show claim back-and-forth process on invoice;Display original invoice and as-of-date end total of what insurance(s) paid vs balance;Chirag;
Modules;Claims;Submit/Resubmit;Task;Combine nurse hours and dispense tickets in a single claim or invoice;Determine the many-to-many relationship between nurse hours and dispense tickets;Chirag;
Modules;Claims;Submit/Resubmit;Task;Identify other billable items;Identify what else is billable to patients/insurance/programs besides nurse visit hours and dispense ticket meds;Chirag;
Modules;Claims;Submit/Resubmit;Task;Define claim and invoice generation;Determine when a claim and invoice are generated;Chirag;
Modules;Claims;Split Billing;Task;Implement split billing;Develop functionality for split billing;Chirag;
Infrastructure;Deployment;Multi-tenancy;Task;Implement multi-tenancy;Develop multi-tenancy support;Chirag;
Infrastructure;Deployment;Data;Task;Develop data-out integrations;Create integrations for exporting data;Chirag;
Modules;Schedule;Calendar;Task;Handle recurring schedules in nurse schedule;Address the issue of linking nurse schedule to encounters and handle recurring schedules;Chirag;
Modules;EDI;Transform;Task;Determine EDI/HL7 requirements;Identify when 835/837 EDI/HL7 is needed;Chirag;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Modules;Workflow;Wizard;Feature;Open readonly last note for review;Wizard should open the last note in readonly mode for review.;Chirag;
Modules;Workflow;Wizard;Feature;Open past wizard and jump to step by table name;Wizard should allow opening past instances and jumping to specific steps by table name.;Chirag;
Modules;DSL;Documentation;Feature;Help text for forms;Add help text to forms in the DSL.;Chirag;
Modules;Communication;SMS;Feature;Send SMS while filling form remotely;Send SMS while filling a form remotely, e.g., list of symptoms.;Chirag;
Modules;Claims;Submit/Resubmit;Feature;Automate claim creation;Automate claim creation based on rules, history, and preset templates.;Chirag;
Modules;Claims;Transactions;Feature;Billing type options;Support various billing types: invoice, payment, refund, discount, waived (100% discount).;Chirag;
Modules;Claims;Transactions;Feature;Payment type options;Support different payment types: patient, insurance, program (e.g., pharma copay).;Chirag;
Modules;Claims;Submit/Resubmit;Task;Documentation for claims;Determine if any documentation is needed to send for each claim.;Chirag;
Modules;Claims;Certification;Task;Out of state billing;Support out of state billing, including license checks per pharmacist by period and pharmacist sign-off.;Chirag;
Modules;Claims;Submit/Resubmit;Feature;NES getBillable();NES getBillable() for any table should return line items to add to a new bill or adjustments.;Chirag;
Modules;Claims;Submit/Resubmit;Feature;NES createClaim();NES createClaim() should look at the invoice and create one or more claim JSONs based on all available info, including primary/secondary, HCPCS/Dx codes, etc.;Chirag;
Modules;Claims;Submit/Resubmit;Feature;NES submitClaim();NES submitClaim() should send claims to Change Health and later bring adjudicated data for review and acceptance. It should be okay to void unpaid claims.;Chirag;
Modules;Claims;Submit/Resubmit;Feature;NES updateClaim();NES updateClaim() should update based on adjudicated claim or new billable data for resubmission.;Chirag;
Modules;Accounting;Ledger;Feature;Accounting Calendar;Implement an accounting calendar to start the month and year, locking billing/invoice changes except for special override roles.;Chirag;
Modules;Accounting;Ledger;Feature;Close year;Implement a feature to close the year, ensuring no unlocking for anyone.;Chirag;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Foundations;NES;Application Entry;Feature;Reversible/voidable Application Entry table;Each encounter/dispense billed should have an Application Entry table that can be reversed/voided and regenerated;Chirag;
Integrations;Compliance;OpenAI;Feature;OpenAI Azure function calling;Implement OpenAI Azure using function calling;Chirag;
Modules;CRM;Tracking;Feature;CRM modules with dollar spent tracking;Implement CRM modules with dollar spent - with reps dinner to lead tracking;Chirag;
Integrations;Compliance;USP;Feature;USP monograph / Drug prices import;Implement USP monograph / Drug prices Deno import and NES;Chirag;
Modules;Configuration;Training;Task;User demos and training;Conduct user demos and training sessions;Chirag;
Integrations;Configuration;API;Task;API OpenAPI documentation;Create API OpenAPI documentation for internal vs external use;Chirag;
Integrations;Compliance;Cover My Meds;Feature;Cover My Meds API integration;Integrate Cover My Meds API;Chirag;
Integrations;Compliance;EPIC;Feature;EPIC app integration;Integrate EPIC app for hospitals using HL7;Chirag;
Infrastructure;Database;Logging;Task;PG server with snapshot and log db;Set up PG server with snapshot, log db, separate VMs, and db cloning;Chirag;
Modules;Patient;Readonly Portal;Feature;Partial DSL for patient readonly portal;Implement partial DSL for patient readonly portal;Chirag;
Infrastructure;Security;Sessions;Task;Session flow management;Figure out proper flow of sessions with JWT expiry, screen lock, re-login, and oAuth;Chirag;
Foundations;NES;Versioning;Task;Handle record conflicts and versioning;Handle 2+ editors same record conflict, DSL versioning, conflict-save, and draft auto-save to backend;Chirag;
Modules;Printing;Label Editor;Feature;Label editor;Create a label editor for dispense ticket, inventory label, payment receipt, invoice, etc., including logo and company address/contact/billing info;Chirag;
Modules;Printing;Label Generator;Feature;Label generator/printer;Implement a label generator/printer for any table view row to print with preview or automatic without preview;Chirag;
Analytics;Query;Reports;Feature;Basic reports in UI;Create basic reports in UI, written with SQL and UI editor;Chirag;
Modules;Global Search;GPT;Feature;Search using GPT-4;Implement search functionality using GPT-4 / Alfred style;Chirag;
Modules;Compliance;EVV;Feature;EVV/e-signature;Implement EVV and e-signature functionality;Chirag;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Foundations;DSL;Data;Task;Select ID and limit from every table;Select id, limit=1 from every table and only show if error;Chirag;
Foundations;DSL;Data;Task;Show extra tables on server;Show extra tables on server, not with matching cson;Chirag;
Foundations;Query;Views;Feature;Create View field;Create View: Yes/No;Chirag;
Foundations;Query;Views;Feature;Refresh View Every field;Refresh View Every: [minutes];Chirag;
Foundations;Query;Views;Task;Add to pgboss queue;On NES init, add to pgboss queue if not already in queue;Chirag;
Foundations;Query;Views;Task;Save to api/query/save.js;Save to: api/query/save.js;Chirag;
Foundations;Query;Views;Task;Validator for SQL;Validator to check if SQL includes %s, %L etc.;Chirag;
Foundations;Query;Views;Task;Transaction handling;Standard api/form save.js;Chirag;
Foundations;Query;Views;Task;Create view_{code};Create view_{code}, delete/recreate on every save;Chirag;
Foundations;Query;Views;Task;Update NES cron/timer;Update NES cron/timer for pgboss;Chirag;
Foundations;Query;Views;Task;Refresh view on call;When calling api/query/code (api/query/index.js), refresh if ?refresh={truthy e.g. 1, true};Chirag;
Foundations;Query;Views;Task;Return data from view;Return data from view_, with filters specified;Chirag;
Foundations;Security;Setup;Task;Update all DSL;All DSL update;Chirag;
Foundations;Security;Setup;Task;Setup rules;Setup rules;Chirag;
Foundations;Security;Setup;Task;Use rules in client;Use rules in client;Chirag;
Foundations;Security;Setup;Task;Use rules in server;Use rules in server;Chirag;
Modules;Schedule;Calendar;Bug;Primary therapy from first intake;Primary therapy should pull from patient's first intake;Chirag;
Modules;Schedule;Calendar;Bug;Readonly/hide visit schedule date/start/end;For single event in series, if override is entered, can we readonly/hide visit schedule date/start/end?;Chirag;
Modules;Schedule;Calendar;Bug;Bug in save: override_end_time;Bug in save: override_end_time;Chirag;
Modules;Schedule;Calendar;Bug;Drag-drop goes back 12hrs;Drag-drop somehow goes back 12hrs;Chirag;
Modules;Schedule;Calendar;Bug;Drag-drop should update popup date/time;Drag-drop should update popup date/time;Chirag;
Modules;Schedule;Calendar;Bug;Don't show 12am in popup;Don't show 12am in popup;Chirag;
Modules;Schedule;Calendar;Task;Pretty print "Update following events" list;Pretty print "Update following events" list;Chirag;
Modules;Schedule;Calendar;Bug;Fix position of +2 more popup;Fix position of +2 more popup;Chirag;
Modules;Schedule;Calendar;Task;Default to repeat every 1 period;Default to repeat every 1 period;Chirag;
Modules;Schedule;Calendar;Bug;Weekly Repeat On single select;Why is repeat-every Weekly Repeat On a single select?;Chirag;
Modules;Schedule;Calendar;Task;Save on drag-drop;Save on drag-drop: Drag-drop should set Override start;Chirag;
Modules;Schedule;Calendar;Task;Clear override;If override start date/time = event start date/time, then clear override;Chirag;
Modules;Schedule;Calendar;Task;Flyout Save/Cancel;Flyout should show Save / Cancel, not Save / Close;Chirag;
Modules;Schedule;Calendar;Feature;Clicking on Day# in Month view;Feature request: Clicking on Day# in Month view should switch to Day view;Chirag;
Modules;Manage;Therapy;Task;Therapy list;Therapy list;Chirag;
Modules;Global Search;Filtering;Task;Archived/deleted patients;How is it finding archived/deleted patients?;Chirag;
Modules;Referral;Report;Task;Embed report: referral intake;Embed report: referral intake;Chirag;
Modules;Review;QA;Task;Sync on/off & active;Allow sync on/off & active for Module/Query/Report/Security/Workflow;Chirag;
Modules;Security;Setup;Task;Copy all security rules;Copy all security rules to d-clararx;Chirag;
Foundations;DSL;Data;Task;Sync mode full;Get data from different sources (do not rely on form.id to be same);Chirag;
Foundations;DSL;Data;Task;Import CMS DME schedule;Import CMS DME schedule into form_cms_dme;Chirag;
Foundations;DSL;Data;Task;Import CMS NPI list;Import CMS NPI list into form_cms_npi;Chirag;
Foundations;DSL;Data;Task;Import FDB NDC list;Import FDB NDC list into form_fdb_ndc, ICD codes into form_fdb_icd;Chirag;
Foundations;DSL;Data;Task;Create .sql files;Use grunt/node to create .sql files;Chirag;
Foundations;DSL;Data;Task;Check for max(created_on/updated_on);Add check for max(created_on/updated_on);Chirag;
Foundations;DSL;Data;Task;Add to post/0000-tablename.sql;Add them to post/0000-tablename.sql;Chirag;
Modules;Sales;Territory;Task;Test sales rep security;Test sales rep security as sales rep with territory;Chirag;
Modules;Sales;Territory;Task;Test sales rep security as manager;Test sales rep security as manager with multiple territories;Chirag;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Infrastructure;Deployment;IPv6;Task;Wiki document IPv6;Fly IPs allocate-v6 -a d-emubin-nes;Chirag;
Modules;CarePlan;Template;Task;Import careplan template defaults;Import careplan template defaults;Chirag;
Modules;Analytics;Report;Feature;Sales rep patient log report;Create a report for sales rep patient logs;Chirag;
Modules;Printing;Label;Feature;Dispense label;Create a label for dispense;Chirag;
Modules;Printing;Ticket;Feature;Dispense ticket;Create a ticket for dispense;Chirag;
Modules;DSL;Tabs;Feature;Form tabs;Allow sections, then tabs with sections, then sections etc.;Chirag;
Modules;DSL;Subform;Feature;Subform editable;Multi-line tabs with filters for loading/maint-dose;Chirag;
Modules;DSL;If Logic;Feature;Field.model.if.Yes.editable;Make fields editable based on if condition;Chirag;
Modules;CarePlan;Intake;Feature;Save intake to auto-create careplan;Save intake -> auto-create careplan active;Chirag;
Modules;Workflow;Insurance;Feature;Launch benefits workflow;Launch benefits workflow per patient insurance;Chirag;
Modules;Workflow;Insurance;Feature;Launch prior auth workflow;Launch prior auth workflow per patient insurance;Chirag;
Modules;Insurance;Eligibility;Feature;Eligibility check from patient insurance;Perform eligibility check from patient insurance;Chirag;
Configuration;Printer;ID;Feature;Printer ID preference;Set printer ID preference;Chirag;
Modules;Patient;Snapshot;Feature;Patient snapshot + dispense wizard;Integrate dispense wizard with patient snapshot;Chirag;
Modules;Patient;Snapshot;Feature;Patient snapshot + claim + claim wizard;Integrate claim wizard with patient snapshot;Chirag;
Modules;Workflow;Queue;Feature;Workflow queue + wizard;Integrate wizard with workflow queue;Chirag;
Modules;Workflow;Queue;Feature;Reports in both sites;Generate reports in both sites;Chirag;
Modules;Workflow;Queue;Feature;Workflow queues;Manage workflow queues;Chirag;
Modules;Workflow;Wizard;Feature;Wizard mode;Implement wizard mode;Chirag;
Modules;Workflow;Archive;Feature;Archive reports/workflows;Archive reports and workflows;Chirag;
Infrastructure;Deployment;Fly.io;Task;Optimize fly.dev deployment;Optimize deployment on fly.dev;Chirag;
Modules;Sales;CRM;Feature;Manage goals;Manage goals for IG Grams/Period and revenue;Chirag;
Modules;Sales;CRM;Feature;Fix snapshot physician goal link;Link snapshot box for goals to edit sales_account for physician;Chirag;
Modules;Sales;CRM;Feature;Address validation;Implement address validation;Chirag;
Modules;Sales;CRM;Report;Feature;Embed report for Sales Rep/Sales Mgr.;Embed report for Sales Rep and Sales Manager;Chirag;
Modules;Workflow;Swim-lane;Feature;Swim-lane view;Implement swim-lane view;Chirag;
Modules;Workflow;Swim-lane;Feature;Popup;Implement popup in swim-lane view;Chirag;
Modules;Workflow;Swim-lane;Feature;Drag-drop to move;Implement drag-drop to move in swim-lane view;Chirag;
Modules;Workflow;Swim-lane;Feature;Change status;Implement change status in swim-lane view;Chirag;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Modules;Frontend;Component;Feature;Define sizes of text boxes;We need to be able to define sizes of text boxes, the default area is usually too small;Altaf;
Modules;Compliance;EVV;Feature;Remember & reload last e-sign;Support for storing e-signature (or last one against the user);Mubin;
Modules;Sales;Territory;Feature;Primary and access territories for sales reps;Sales reps should have a primary territory and then access territories (also need a smart territory field to show reps assigned, can use in the intake form);Mubin;
Modules;DSL;Smart Field;Feature;DOB field should show age;DOB field should show the age next to it (smart field like the weight);Osama;
Modules;DSL;Smart Field;Feature;Smart field for time;Need a smart field for time as well ‘5 H for hours converts to minutes';Osama;
Modules;DSL;Smart Field;Feature;Auto-calculate BSA, LBW, and IBW fields;Auto-calculate BSA, LBW, and IBW fields;Osama;
Modules;DSL;Smart Field;Feature;Add note template text;Add note template text based on subjects (see list_bill_note_template and list_note_template);Osama;
Modules;DSL;Select Field;Feature;Limit multi-select items;Use field.model.max to limit how many multi:true count items can be selected;Osama;
Modules;DSL;Select Field;Feature;Select 1 or many options in a checkbox;Need option to select 1 or many of others in a checkbox (i.e. Select None or an Select All) Add support for a single checkbox option (can we store the value as Yes/No?);Osama;
Modules;DSL;Select Field;Feature;Blow out foreign source checkbox;Need an option to “blow out” foreign source checkbox to show all option (i.e. using this for symptoms based on disease / therapy and supply / nursing codes). We can cap it at 20 options?;Osama;
Modules;DSL;Select Field;Feature;Support list_step;Support the list_step (similar to above) which will show managed filtered bullet points;Osama;
Modules;DSL;If Logic;Feature;Support {{now}} in if logic;Support {{now}} when checking if logic against the current day;Osama;
Modules;DSL;If Logic;Feature;Support age field in if logic;Add support for age field that pulls in and auto populates based on patient dob (need to use in if logic for pregnancy checks);Osama;
Modules;DSL;Grid View;Feature;Support subfields required in grid views;For grid views, need support for subfields.{name}.required. could use multi subform editable for this;Osama;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Modules;Patient;Snapshot;Task;Mirror style/theme from patient snapshot to sales account snapshot;Ensure the style and theme from the patient snapshot are mirrored to the sales account snapshot.;CHIRAG;
Modules;Patient;Snapshot;Feature;Primary Patient/Order workflow - Patient Snapshot;Develop the primary patient/order workflow focusing on the patient snapshot, including insurance, care plan, intake, and order dispense.;CHIRAG;
Modules;Workflow;Wizard;Feature;Workflow Wizard;Develop a workflow wizard to select section_groups/sections to show for existing and new forms.;CHIRAG;
Modules;Workflow;Queue;Feature;Workflow Queue Inspect Views;Create inspect views for the workflow queue.;CHIRAG;
Modules;Workflow;Queue;Feature;Workflow Queue Swim-lane View;Develop a swim-lane view for the workflow queue with Edge->Wizard link.;CHIRAG;
Modules;Workflow;Review;Feature;Workflow Review;Implement a workflow review system to audit track which fields can be edited/approved and show before/after comparison.;CHIRAG;
Modules;Configuration;Address Completion;Feature;Address Verification;Integrate address verification using the Radar API.;CHIRAG;
Modules;Global Search;Component;Feature;Search Bar;Support sales_account in the search bar based on access.;CHIRAG;
Modules;DSL;Editor;Feature;DSL Editor;Develop a DSL editor for mass updates and setting bundles.;CHIRAG;
Modules;DSL;Data;Task;DSL mass update;Perform a mass update on the DSL to set bundles.;CHIRAG;
Modules;Frontend;Navigation;Task;Handle tab list overflow via dropdown;Implement a dropdown to handle tab list overflow.;CHIRAG;
Modules;Frontend;Component;Feature;Support datepickers in table filters;Add support for datepickers in table filters.;CHIRAG;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Infrastructure;Database;Data;Task;Admin CMS import;Import data from CMS NPI files.;Chirag;
Infrastructure;Database;Data;Feature;Sync_Mode Full;Import full table data from Admin console and set period per table.;Chirag;
Infrastructure;Database;Data;Feature;Sync_Mode Mixed;Support POST api/form _meta sync_mode=mixed, show recent changes for IDs < 10k in all mixed tables, create post .sql file with recent changes.;Chirag;
Infrastructure;Database;Data;Feature;Flags/Secrets;NES should send all front-end flags via /api/version.;Chirag;
Infrastructure;Database;Data;Feature;DSL.field.model.two-way-write;Save to multiple destinations e.g. H&P, PMP Opt-Out.;Chirag;
Infrastructure;Database;Data;Task;Replace .rule with /api calls;Handle scheduling via pg-boss, especially for multiple NES instances.;Chirag;
Infrastructure;Database;Data;Task;Version change handling;Handle version changes for workflows, reports, and DSL changes including showing records, printing records, and using data in reports.;Chirag;
Modules;Printing;Wizard;Feature;Wizard forms print;Enable printing of forms within wizards.;Chirag;
Modules;Printing;Label;Feature;Label assignment;Assign labels based on drug, ship-to state, site, and agency.;Chirag;
Analytics;Query;Report;Feature;License ActiveReportJS;License ActiveReportJS for BI/reporting.;Chirag;
Analytics;Query;Report;Feature;Use read-replicas for BI report queries;Use read-replicas for all BI report queries (/api/query, /api/view), instead of primary DB.;Chirag;
Analytics;Query;Report;Feature;Export to pretty Excel;Enable export to pretty Excel.;Chirag;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Infrastructure;Cron Scheduler;;Feature;Run .rule if possible;Implement the ability to run .rule files as part of the cron scheduler.;Chirag;
Infrastructure;Security;Homebase Auth;Feature;Store api/version/ Flags in window.NESFlags;Store API version flags in the window.NESFlags for Homebase authentication.;Chirag;
Infrastructure;Security;Homebase Auth;Feature;First get api/access;Ensure the first step in Homebase authentication is to get API access.;Chirag;
Infrastructure;Security;Homebase Auth;Feature;Then check us_state;After obtaining API access, check the us_state for Homebase authentication.;Chirag;
Migration;Migration Tool;Basic;Feature;Import Patient CSVs;Implement a basic migration tool to import patient data from CSV files.;Chirag;
Migration;Migration Tool;Advanced;Feature;Live sync;Implement live sync for migration tables on the client instance, including configuration, import period per cpr+ table, source server and replication parameters, and status view.;Chirag;
Migration;Migration Tool;Advanced;Feature;Replication;Set up replication for migration tables as per the guide on SQL Server Central.;Chirag;
Migration;Migration Tool;Advanced;Feature;Verify;Ensure all imported data is consistent and will not cause database crashes.;Chirag;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Modules;Queue;Inspect;Feature;Inspect views;Implement inspect views for eFax and eRx in the queue module.;Chirag;
Modules;Queue;Inspect;Feature;Inspect views for eFax, eRx;Develop inspect views for eFax and eRx in the queue module.;Chirag;
Modules;Queue;Inspect;Feature;Right-click on grid table queue-view;Add inspect-view buttons/features to the right-click menu on grid table queue-view.;Chirag;
Modules;Queue;Inspect;Feature;Show Swimlane mode;Implement swimlane mode in the queue module.;Chirag;
Modules;Queue;Inspect;Feature;Drag&drop in Swimlane mode;Enable drag&drop functionality in swimlane mode to launch wizard based on Edge.;Chirag;
Modules;Sales;Manager;Feature;Sales Rep / Manager view;Develop Sales Rep / Manager view using ARJS.;Chirag;
Modules;Patient;Submenu;Feature;Sort Submenus;Allow sorting of submenus by dragging, except for snapshot.;Chirag;
Modules;Patient;Submenu;Feature;Make bar taller;Make the submenu bar taller and easier to click.;Chirag;
Modules;Patient;Chart;Feature;Multi-table grids;Implement multi-table grids in patient chart to show all ad-hoc forms.;Chirag;
Modules;Patient;Alerts;Feature;Generic patient alerts;Develop generic patient alerts with src form/id/field, dst group, and auto-set.;Chirag;
Modules;Patient;Security;Feature;VIP / VVIP patients;Implement security features for VIP / VVIP patients.;Chirag;
Modules;Patient;Portal;Feature;SMS Audit portal setup;Set up SMS Audit portal without .rule.;Chirag;
Modules;Communication;Chat;Feature;Old Chat functionality;Figure out if/how to make old Chat work.;Chirag;
Modules;Schedule;;Feature;Schedule module;Develop the schedule module.;Chirag;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Infrastructure;Inventory;Drug;Task;Sync from FDB;Sync Drug/NDC/HDCP data from FDB with pricing;Chirag;
Infrastructure;Inventory;Supplier;Task;Sync from FDB;Sync Supplier data from FDB;Chirag;
Infrastructure;Inventory;Item;Task;Security/Access;Implement security/access for inventory items;Chirag;
Infrastructure;Inventory;Item;Task;NDC Link;Link NDC to FDB if NDC exists, else no link;Chirag;
Infrastructure;Inventory;Item;Task;Item UOM Matrix;Create Item UOM Matrix;Chirag;
Infrastructure;Inventory;Category;Task;Category/Subcategory Lists;Create category and subcategory lists;Chirag;
Infrastructure;Inventory;Bin;Task;Bin List;Create bin list;Chirag;
Infrastructure;Inventory;Supplier;Task;Supplier List;Create supplier list;Chirag;
Infrastructure;Inventory;Payer;Task;Fee Schedule x Item;Create fee schedule per item for payers;Chirag;
Infrastructure;Inventory;Kits;Task;Kit Line Items;Create kit line items with item#, bill/print inventory qty, qty in kit, comments, and applies only to fill#;Chirag;
Infrastructure;Inventory;Orders;Task;Purchase Order;Create purchase order with purchase line;Chirag;
Infrastructure;Inventory;Orders;Task;Receipt Order;Create receipt order with receipt line;Chirag;
Infrastructure;Inventory;Orders;Task;Drug Order;Create drug order with drug line, including importing kit as individual lines and fields for bill qty, print qty, inventory qty, applies only to fill#, and payer line;Chirag;
Infrastructure;Inventory;Orders;Task;Dispense Ticket;Create dispense ticket with fields for fill# and dispense line linking to original kit# per line and lot#;Chirag;
Infrastructure;Inventory;Ledger;Task;Inventory Ledger;Create inventory ledger for inventory movement between bins and sites, lot tracking, and label print log;Chirag;
Infrastructure;Inventory;API;Task;Inventory API;Create /api/inventory/ endpoint;Chirag;
Infrastructure;Inventory;Inventory Item List;Task;Fee Schedule Matrix;Show matrix of fee schedule by payer;Chirag;
Infrastructure;Inventory;Inventory Item List;Task;Label Design;Design labels for inventory items;Chirag;
Infrastructure;Inventory;Purchase Order;Task;EDI 850;Implement EDI 850 via Change API for purchase orders;Chirag;
Infrastructure;Inventory;Asset Management;Task;Calibration;Implement calibration for asset management;Chirag;
Infrastructure;Inventory;Asset Management;Task;Preventative Management;Implement preventative management for assets;Chirag;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Modules;Inventory;Barcode integration;Feature;Implement zbar-wasm for barcode integration;Implement https://github.com/undecaf/zbar-wasm for barcode scanning.;Chirag;
Modules;Inventory;Barcode integration;Feature;Implement samsam2310/zbar.wasm for barcode integration;Implement https://github.com/samsam2310/zbar.wasm for barcode scanning.;Chirag;
Modules;Inventory;Barcode integration;Feature;Parse GS1 barcodes;Use https://github.com/PeterBrockfeld/BarcodeParser to parse GS1 barcodes.;Chirag;
Modules;Inventory;Operations;Feature;Receive inventory;Implement functionality to receive inventory items.;Chirag;
Modules;Inventory;Operations;Feature;Use inventory;Implement functionality to use inventory items.;Chirag;
Modules;Inventory;Operations;Feature;Move inventory;Implement functionality to move inventory items.;Chirag;
Modules;Inventory;Operations;Feature;Adjust inventory;Implement functionality to adjust inventory items.;Chirag;
Modules;Inventory;Operations;Feature;Print inventory labels;Implement functionality to print labels for inventory items.;Chirag;
Modules;Inventory;Reports;Feature;Inventory on Hand report;Create a report for inventory on hand as of a specific date.;Chirag;
Modules;Inventory;Reports;Feature;Inventory Movement/Ledger report;Create a report for inventory movement and ledger details.;Chirag;
Modules;Dispense;N/A;Feature;Move inventory as part of dispense ticket posting;Implement functionality to move inventory as part of the dispense ticket posting process.;Chirag;
Modules;Billing;N/A;Feature;Change API integration;Integrate Change API to store credentials per customer in AppSmith and read in NES from environment variables.;Chirag;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Analytics;Analytics;;Feature;Implement query builder;Implement query builder using https://www.datasparc.com/sql-query-builder/;Chirag;
Compliance;Compliance;URAC;Feature;URAC compliance;Ensure URAC compliance within the system;Chirag;
Compliance;Compliance;Audit;Feature;Audit compliance;Implement audit compliance features;Chirag;
Issue Tracker;Issue Tracker;;Feature;Create new YouTrack ticket;Create new YouTrack ticket with customer/env-tag;Chirag;
Configuration;Settings;Configuration;Feature;Enable Sites;Enable Sites configuration in company.cson;Chirag;
Configuration;Settings;Configuration;Feature;Default site;Set default site configuration in company.cson;Chirag;
Configuration;Settings;Configuration;Feature;Enable 3rd-party Agency;Enable 3rd-party Agency configuration in company.cson;Chirag;
Configuration;Settings;Configuration;Feature;Default agency;Set default agency configuration in company.cson;Chirag;
Configuration;Settings;Manage;Feature;2nd level menu: All, Bundle-based;Go through all the DSL.model.bundle options and show them at 2nd level;Chirag;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Infrastructure;Deployment;React;Task;React compiled size 20mb+ troubleshoot;Troubleshoot the React compiled size which is currently over 20mb.;Chirag;
Infrastructure;Database;K8s;Feature;Setup K8s APIs for Customer Creation;Create K8s APIs for customer creation including customer_name, DB password, read-replica count, and CPU/VM resources.;Chirag;
Infrastructure;Database;K8s;Feature;Setup K8s APIs for Customer Update;Update K8s APIs to allow overwriting DB password without requiring the old password, and updating read-replica count and CPU/VM resources.;Chirag;
Infrastructure;Database;K8s;Feature;Setup K8s APIs for Backup/Snapshot;Create K8s APIs to backup/snapshot environment/customer to URL (S3 or local).;Chirag;
Infrastructure;Database;K8s;Feature;Setup K8s APIs for Restore;Create K8s APIs to restore environment/customer from URL .tar.gz.;Chirag;
Infrastructure;Database;K8s;Feature;Setup K8s APIs for Cloning;Create K8s APIs to clone environment/customer to another environment/customer, internally calling Backup/Restore if needed.;Chirag;
Infrastructure;Database;K8s;Feature;Setup K8s APIs for Backup Schedule;Create K8s APIs to set backup schedule for each environment/customer, using default for environment if not set.;Chirag;
Infrastructure;Admin Server;Deno;Bug;Fix /service/fly/deploy;Fix the /service/fly/deploy endpoint to use cookie to authenticate with Appsmith user/me API.;Chirag;
Infrastructure;Admin Server;Sync_Mode;Feature;Implement Sync_Mode - Full;Download data from external APIs like FDB for Drug, Provider, and Payer, and auto-update periodically (Daily/Weekly etc.).;Chirag;
Infrastructure;Admin Server;SureScripts;Feature;Import SureScripts data;Import data per customer and store it to .sqlite.;Chirag;
Infrastructure;Admin Server;Log Search;Feature;Create Log Search API;Create an API for all NES/Fly.io servers to dump stderr and a UI/API for global log search.;Chirag;
Infrastructure;Admin Server;AppSmith;Feature;AppSmith Customer Management;Implement features to rename app-name, manage user count (licensed user count), and manage list of pg servers in K8s cluster.;Chirag;
Infrastructure;Admin Server;AppSmith;Feature;AppSmith Database Management;Manage backups/snapshots per customer/environment and restore via Customer > Environment screen.;Chirag;
Infrastructure;Admin Server;AppSmith;Feature;AppSmith Secret Management;Add button for random secret (32char hash) e.g. FLY_NES, and create DATABASE_URL secret & push to K8s cluster.;Chirag;
Infrastructure;Admin Server;AppSmith;Feature;AppSmith Login Page Editor;Create a content-editor for the login page.;Chirag;
Infrastructure;Fly.io;WebSockets;Task;WebSockets support/troubleshoot;Troubleshoot and support WebSockets on Fly.io.;Chirag;
Infrastructure;Fly.io;Customer;Feature;Customer cname url mapping;Setup customer cname URL mapping and subdomains.;Chirag;
Infrastructure;CI/CD;Continuous Integration;Feature;Continuous integration setup;Build once on merge into #develop, send errors to AWS logs, and alert in Slack.;Chirag;
Infrastructure;CI/CD;Code Deployment;Feature;Code deployment process;Implement code deployment with testing on ephemeral Fly instance and production deployment via Fly image update.;Chirag;
Infrastructure;CI/CD;VCS Integration;Feature;VCS integration with GitLab;Integrate VCS with GitLab and come up with branch/tag naming conventions.;Chirag;
Team;Module;Submodule;Task Type;Task Summary;Task Description;Suggested By;Ticket
Modules;Frontend;Component;Feature;Base Theme (React-based);Implement the base theme using React, including form tabs and action buttons inside nested tab view.;PROJECT;UX-26
Modules;Frontend;Component;Feature;Navbar UI (collapsed view);Design and implement a collapsible navbar UI.;PROJECT;UX-13
Modules;Frontend;Component;Feature;Base Icon-set, fonts, colors;Create and integrate a base icon set, fonts, and color scheme.;PROJECT;UX-27
Modules;Frontend;Component;Feature;Form multi-column views (web/mobile);Develop multi-column views for forms, with specific layouts for web and mobile.;PROJECT;UX-14
Modules;Frontend;Component;Feature;Form - Web 2 column;Implement a two-column layout for web forms.;PROJECT;UX-28
Modules;Frontend;Component;Feature;Form - Mobile 1 column;Implement a single-column layout for mobile forms.;PROJECT;UX-29
Modules;Frontend;Component;Feature;Compact vs. Standard view;Create compact and standard view options for forms.;PROJECT;UX-31
Modules;Workflow;Wizard;Feature;Initial Assessment;Develop the initial assessment wizard.;PROJECT;UX-16
Modules;Workflow;Wizard;Feature;Dispense (create ticket);Create a wizard for dispensing tickets.;PROJECT;UX-17
Modules;Workflow;Wizard;Feature;Claim (create new);Develop a wizard for creating new claims.;PROJECT;UX-18
Modules;Workflow;Wizard;Feature;Company Onboarding;Create a company onboarding wizard.;PROJECT;UX-19
Modules;Dashboard;Component;Feature;Dashboard UX;Design and implement the dashboard user experience.;PROJECT;UX-20
Modules;Ledger;Views;Feature;Ledger View;Develop views for ledger entries.;PROJECT;UX-23
Modules;Inventory;Views;Feature;Inventory Module View;Create views for the inventory module.;PROJECT;UX-22
Modules;Patient;Views;Feature;Patient List view;Develop a list view for patients.;PROJECT;UX-32
Modules;Patient;Views;Feature;Patient Snapshot view;Create a snapshot view for patients.;PROJECT;UX-33
Modules;Dispense;Views;Feature;Dispense Module View;Develop views for the dispense module.;PROJECT;UX-24
Modules;Billing;Views;Feature;Billing Module View;Create views for the billing module.;PROJECT;UX-25
Modules;Security;Editor;Feature;Security Editor;Develop an editor for managing security settings.;PROJECT;UX-6
Modules;Workflow;Editor;Feature;Workflow Editor;Create an editor for managing workflows.;PROJECT;UX-7
Modules;Printing;Editor;Feature;Label/Print-out Editor;Develop an editor for labels and print-outs.;PROJECT;UX-8
Modules;Patient;Editor;Feature;Snapshot View Editor;Create an editor for snapshot views.;PROJECT;UX-9
Modules;Query;Editor;Feature;SQL View Editor;Develop an editor for SQL views.;PROJECT;UX-34
Modules;DSL;Editor;Feature;DSL Editor;Create an editor for DSL configurations.;PROJECT;UX-10
Modules;Migration;Editor;Feature;Migration Editor;Develop an editor for managing migrations.;PROJECT;UX-11
Modules;Frontend;React;Feature;React-driven Interface;Develop a React-driven interface for all forms.;PROJECT;DV-40
Modules;Frontend;React;Feature;React Tabbed view for all forms;Implement a tabbed view for all forms using React.;PROJECT;DV-50
Modules;Frontend;React;Feature;React Table Grid with filters, export, action;Create a table grid with filters, export, and action capabilities using React.;PROJECT;DV-49
Modules;Frontend;React;Feature;React Module Parent with nested tabbed views;Develop a parent module with nested tabbed views using React.;PROJECT;DV-60
Modules;Frontend;React;Feature;React Navigation-pane (left-bar, collapsible);Create a collapsible left-bar navigation pane using React.;PROJECT;DV-62
Modules;Frontend;React;Feature;React Modal - Fly-out view;Implement a fly-out view modal using React.;PROJECT;DV-48
Modules;Frontend;React;Feature;React Search box, always visible;Develop an always-visible search box using React.;PROJECT;DV-61
Modules;Frontend;Styling;Feature;Bootstrap override;Override Bootstrap styles to fit the project's needs.;PROJECT;DV-41
Modules;Frontend;Styling;Feature;Flex-based layout;Implement a flex-based layout for the frontend.;PROJECT;DV-42
Modules;Frontend;Styling;Feature;Brand-specific style/color/fonts;Create and apply brand-specific styles, colors, and fonts.;PROJECT;DV-43
Modules;Frontend;Views;Feature;Updated Form views;Update form views to match new design specifications.;PROJECT;DV-44
Modules;Frontend;Views;Feature;Web 1 column;Implement a single-column layout for web forms.;PROJECT;DV-45
Modules;Frontend;Views;Feature;Web 2 column;Implement a two-column layout for web forms.;PROJECT;DV-46
Modules;Frontend;Views;Feature;Module 1 column;Create a single-column layout for module views.;PROJECT;DV-47
Modules;Cleanup;Component;Task;New Manage table view;Develop a new manage table view.;PROJECT;DV-85
Modules;Communication;Component;Task;Remove Communications module;Remove the obsolete communications module.;PROJECT;DV-51
Modules;Cleanup;Component;Task;BD repo cleanup;Clean up the BD repository.;PROJECT;DV-53
Modules;DSL;Component;Task;Delete obsolete DSL forms, underlying code, rules;Remove obsolete DSL forms, underlying code, and rules.;PROJECT;DV-52
Modules;Communication;Component;Task;SMS Audit / Portal setup cleanup, update;Clean up and update the SMS audit and portal setup.;PROJECT;DV-54
Modules;Dashboard;Component;Feature;Dashboard Blocks;Develop various types of dashboard blocks.;PROJECT;DV-70
Modules;Dashboard;Component;Feature;Block type: Event;Create event-type blocks for the dashboard.;PROJECT;DV-71
Modules;Dashboard;Component;Feature;Block type: Connector;Create connector-type blocks for the dashboard.;PROJECT;DV-73
Modules;Dashboard;Component;Feature;Block type: Field;Create field-type blocks for the dashboard.;PROJECT;DV-72
Modules;Dashboard;Component;Feature;Table View fields;Develop table view fields for the dashboard.;PROJECT;DV-74
Modules;Dashboard;Component;Feature;Action Item components;Create action item components for the dashboard.;PROJECT;DV-75
Modules;Dashboard;API;Feature;NES Dashboard Editor APIs;Develop NES dashboard editor APIs.;PROJECT;DV-76
Modules;Dashboard;Editor;Feature;React Editor (ReactFlow);Create a ReactFlow-based editor for the dashboard.;PROJECT;DV-69
Modules;Dashboard;Component;Feature;Workflow Table;Develop a workflow table for the dashboard.;PROJECT;DV-79
Modules;NES;Component;Task;Stability improvements;Implement stability improvements for NES.;PROJECT;DV-83
Modules;NES;Component;Task;Review NES Write code - DSL-update, form save;Review and improve NES write code for DSL updates and form saves.;PROJECT;DV-63
Modules;NES;Component;Task;Handle DB failures;Implement mechanisms to handle database failures.;PROJECT;DV-84
Modules;NES;Component;Feature;DB Transaction Process;Develop a process for handling database transactions.;PROJECT;DV-87
Modules;Security;Component;Feature;Initial DSL sync;Implement initial DSL synchronization.;PROJECT;DV-77
Modules;Security;Component;Feature;Activity-based Access;Develop activity-based access controls.;PROJECT;DV-55
Modules;Security;Component;Feature;User access;Implement user access controls.;PROJECT;DV-57
Modules;Security;Component;Feature;Group access;Implement group access controls.;PROJECT;DV-58
Modules;Security;Component;Feature;Role access;Implement role access controls.;PROJECT;DV-59
Modules;Security;Component;Feature;Module-level Access;Develop module-level access controls.;PROJECT;DV-56
Modules;Security;API;Feature;NES Security APIs;Develop NES security APIs.;PROJECT;DV-78
Modules;Ledger;Component;Feature;Number series (more than auto-increment int);Implement a number series feature that goes beyond auto-increment integers.;PROJECT;DV-38
Modules;Ledger;Component;Feature;DSL-style CSON/JSON setup per ledger module;Develop a DSL-style CSON/JSON setup for each ledger module.;PROJECT;DV-33
Modules;Ledger;Component;Feature;Journal entry - pre-posting;Implement pre-posting for journal entries.;PROJECT;DV-35
Modules;Ledger;Component;Feature;Journal entry - post, void;Implement post and void functionalities for journal entries.;PROJECT;DV-34
Modules;Ledger;Component;Feature;Stored procedures for posting, using DB transaction;Develop stored procedures for posting using database transactions.;PROJECT;DV-64
Modules;Ledger;Component;Feature;As-of-now Balance views;Create as-of-now balance views.;PROJECT;DV-65
Modules;Ledger;Component;Feature;As-of date Balances (transaction summary) view by key field;Develop as-of-date balance views by key field.;PROJECT;DV-36
Modules;Ledger;Component;Feature;Audit trail, transaction history;Implement an audit trail and transaction history.;PROJECT;DV-37
Modules;Inventory;Management;Feature;Inventory Management;Develop inventory management features.;PROJECT;DV-23
Modules;Inventory;Management;Feature;Item list;Create an item list for inventory management.;PROJECT;DV-17
Modules;Inventory;Management;Feature;Category, Subcategory Lists;Develop category and subcategory lists for inventory management.;PROJECT;DV-18
Modules;Inventory;Management;Feature;Bin List;Create a bin list for inventory management.;PROJECT;DV-19
Modules;Inventory;Management;Feature;Supplier / Vendor list;Develop a supplier/vendor list for inventory management.;PROJECT;DV-31
Modules;Inventory;Transactions;Feature;Inventory Transactions;Develop features for handling inventory transactions.;PROJECT;DV-24
Modules;Inventory;Transactions;Feature;Inventory Ledger;Create an inventory ledger.;PROJECT;DV-21
Modules;Inventory;Transactions;Feature;Item receipt by lot, vendor;Implement item receipt functionalities by lot and vendor.;PROJECT;DV-20
Modules;Inventory;Transactions;Feature;Transfer / Movements;Develop features for inventory transfers and movements.;PROJECT;DV-22
Modules;Inventory;Transactions;Feature;Inventory Adjustments;Implement inventory adjustment functionalities.;PROJECT;DV-25
Modules;Inventory;Transactions;Feature;Barcode scanner for receipt, movement;Develop barcode scanner functionalities for inventory receipt and movement.;PROJECT;DV-30
Modules;Inventory;Reports;Feature;Inventory Reports / Printing;Develop inventory reports and printing functionalities.;PROJECT;DV-26
Modules;Inventory;Reports;Feature;Inventory-on-hand by Lot, Category, Bin;Create reports for inventory-on-hand by lot, category, and bin.;PROJECT;DV-27
Modules;Inventory;Reports;Feature;Inventory Valuation;Develop inventory valuation reports.;PROJECT;DV-28
Modules;Inventory;Reports;Feature;Ledger details;Create ledger detail reports.;PROJECT;DV-29
Modules;Dispense;Billing;Feature;Credit/Debit Card Billing;Implement credit and debit card billing functionalities.;PROJECT;DV-81
Modules;Global Search;Component;Feature;Search (Shortcut Bar);Develop a search shortcut bar.;PROJECT;DV-8
Modules;Global Search;Component;Feature;List all opened modules/tabs;Create a feature to list all opened modules and tabs.;PROJECT;DV-80
Modules;Schedule;Component;Feature;Schedule;Develop scheduling functionalities.;PROJECT;DV-9
Analytics;Query;Feature;BI (Analytics / Reporting);Develop business intelligence and analytics reporting features.;PROJECT;DV-12
Modules;Onboarding;Component;Feature;Onboarding Tool;Create an onboarding tool.;PROJECT;DV-10
Modules;Migration;Component;Feature;Migration Tool;Develop a migration tool.;PROJECT;DV-11
Modules;Issue Tracker;Component;Feature;Issue Tracker;Create an issue tracker.;PROJECT;DV-13
Infrastructure;Database;Component;Feature;RDS/AWS;Implement RDS/AWS for database management.;PROJECT;IN-6
Infrastructure;Database;Component;Feature;Caching;Develop caching functionalities for the database.;PROJECT;IN-7
Infrastructure;Database;Component;Feature;Logging;Implement logging functionalities for the database.;PROJECT;IN-8
Infrastructure;Admin Console;AppSmith;Feature;AppSmith;Develop functionalities for AppSmith integration.;PROJECT;IN-3
Infrastructure;Deployment;Fly.io;Feature;Fly.io;Implement Fly.io for deployment.;PROJECT;IN-4
Infrastructure;Deployment;Pipeline;Feature;Pipeline;Develop a CI/CD pipeline.;PROJECT;IN-5
