Team;Module;Submodule
Integrations;File Attachments;S3
Integrations;File Attachments;Minio
Integrations;SMS;Twilio
Integrations;SMS;Telnyx
Integrations;Email templating;Mandrill
Integrations;Email templating;Sendgrid
Integrations;Notifications;Slack
Integrations;Notifications;Teams
Integrations;Notifications;Push to client
Integrations;Notifications;Firebase
Integrations;Faxing;Phaxio
Integrations;Faxing;Ringcentral
Integrations;Geo Address;Radar
Integrations;SSO;Keycloak
Integrations;SSO;Gluu
Infrastructure;Monitoring;Basic keepalive / uptime monitoring
Infrastructure;Monitoring;K8s full monitoring
Infrastructure;Monitoring;Failover testing and alerting
Infrastructure;Monitoring;Sentry for exceptions
Infrastructure;Monitoring;Performance monitoring of request lifecycle
Infrastructure;Monitoring;Centralized Logging
Integrations;Surescripts;Certification
Integrations;Payment Gateway;Middleware API
Integrations;Claims;Change Healthcare
Integrations;Claims;Primary NCPDP
Integrations;Claims;Medical Claims
Integrations;Claims;Redsail
Foundations;PVerify;Back Office Payer Workflow
Integrations;PVerify;Eligibility Inquiry API
Integrations;PVerify;PDF Creation
Integrations;PVerify;Eligibility Data Fetching
Integrations;PVerify;PDF Upload to S3
Integrations;PVerify;Database Update
Integrations;PVerify;PUT to Clara NES Server
Infrastructure;PVerify;pgboss Cron Job
Foundations;B&D Dev Ops Tools;ESLint or CoffeeLint Plugins
Foundations;B&D Dev Ops Tools;Handlers
Foundations;B&D Dev Ops Tools;DSL Lint Features or during deployment (in grunt compiler)
Modules;B&D Dev Ops Tools;DSL Lint Features or during deployment (in grunt compiler)
Foundations;DSL;Transforms
Foundations;DSL;Help tooltips
Foundations;DSL;Validator
Foundations;DSL;if: clear_fields
Foundations;DSL;Prefill
Foundations;DSL;Prefill transform
Foundations;DSL;xml data field
Foundations;NES;Performance issue
Foundations;NES;User comments support functionality
Foundations;NES;Background auto refresh
Foundations;NES;Workflow swimlanes drag and drop
Foundations;NES;Integrating workflow and action events
Foundations;NES;Security integration
Foundations;NES;GET calls not filters
Foundations;DSL;DSL record prefilling
Foundations;DSL;Make fields readonly based on if condition
Foundations;DSL;Client Validation for can_sync fields
Foundations;DSL;Handle advanced filters
Foundations;DSL;Section tabs for DSL sections
Foundations;DSL;DSL form save/update
Foundations;DSL;Grid count issue
Foundations;DSL;Double section header
Foundations;DSL;Archived field checkbox
Foundations;DSL;Add deleted field to form filters
Foundations;DSL;Removed archived field from query module
Foundations;DSL;Edit support to DSLTabbedView
Foundations;DSL;List view search field revert to auto_name
Foundations;Workflow Wizard;Show/Hide Field
Foundations;Workflow Wizard;Dependent Form Saves
Foundations;Workflow Wizard;Form Steps Issue
Foundations;Workflow Wizard;Save Validation
Foundations;Workflow Wizard;List View Preset Support
Foundations;Workflow Wizard;Automatic Preset Value Generation
Foundations;Workflow Wizard;Review Process
Foundations;Workflow Wizard;Entry Notification
Modules;Miscellaneous;Company Config Usage
Modules;Miscellaneous;Extra Open Tabs
Modules;Miscellaneous;Navigation Fixes
Modules;Miscellaneous;Double Click to Open Tab
Infrastructure;Miscellaneous;Security Implementation
Modules;Miscellaneous;Standard Component Code Update
Modules;Patient SnapShot;Snapshot Logic Update
Modules;Patient SnapShot;Wizard Support
Modules;Patient SnapShot;Audit Trail Integration
Modules;Patient SnapShot;In Progress Wizards
Infrastructure;Pre Production;Bundle React.js and CoffeeScript
Foundations;SureScripts;Refill Request Testing
Foundations;SureScripts;Payer Selection Popup
Foundations;SureScripts;Snapshot Review
Modules;Inventory;Transfers Handling
Modules;Inventory;Purchase Receipt Handling
Modules;Inventory;Inventory Scanning
Modules;Inventory;Nursing Agency Record
Modules;Ordering;Embedded Grid View for Diagnoses
Modules;Ordering;Grid View for Payers Assignment
Modules;Ordering;Compounding Support
Modules;Ordering;TPN Machine Integration
Modules;Ordering;Factor Dosing Module
Modules;Ordering;Logic Layout for Multiple Payers
Integrations;Claims;NCPDP builder
Integrations;Claims;NCPDP schema and validation
Integrations;Claims;Medical Claims
Integrations;Claims;NCPDP to Medical Claims transformation
Integrations;Claims;Medi-Cal support
Integrations;Claims;Invoicing
Integrations;Benefits Investigation;E1 support
Integrations;Prior Authorization;PA form auto-fill
Integrations;Prior Authorization;ePA required fields
Integrations;Prior Authorization;Unit tracking/renewal alerts
Integrations;Intake;Benefits investigation workflow
Integrations;Intake;Prior auth workflow
Foundations;Styling;Framework
Modules;UI Testing;Playwright
Modules;Global Search;Component
Modules;Popups;Error Popups
Modules;Styling;React
Modules;Type-checking;React
Modules;jQuery;Prune
Modules;Documentation;Dev Onboarding
Foundations;pg-escape;Replacement
Foundations;DSL;Deploy Fixes
Infrastructure;Dev Ops Pipeline;Single Click Create Fly Instance
Infrastructure;Dev Ops Pipeline;Auto Deploy Latest Code
Infrastructure;Dev Ops Pipeline;DB Backup Before Deploy
Infrastructure;System Alerts;Memory Usage
Infrastructure;System Alerts;CPU Usage
Foundations;SQL Files;Transaction Block
Foundations;Logger;Verbose Update
Foundations;Test Data;Generation
Foundations;NES;Code Cleanup
Foundations;NES;Standardize Code (standard.js)
Foundations;Schema;Validation
Foundations;Data Import;Customers
Infrastructure;Crash Alerts;All Over
Modules;Patient App;Development
Infrastructure;Logs;Intelligent Tool
Analytics;Data Pipeline;Snowflake Semblance
Integrations;Chat;Teams Integration
Infrastructure;Ticketing System;Setup
Modules;App Optimization;Vite Config
Modules;App Optimization;CRACO Config
Modules;App Optimization;TS Config
Modules;App Optimization;Webpack Config
Modules;App Optimization;Babel Plugins
Modules;Animations;Less
Modules;Animations;Coffee
Modules;Breadcrumb
Modules;Notifications
Modules;Custom Skeleton;Table
Modules;Custom Skeleton;Cards
Modules;Custom Skeleton;Tabs
Modules;Common Components;Buttons
Modules;Common Components;Cards
Modules;Common Components;Texts
Modules;Common Components;Flyouts
Modules;Common Components;Popups
Modules;Common Components;Modal
Modules;Common Components;Drawers
Modules;Custom Hooks
Modules;Constants;Strings
Modules;Constants;Messages
Modules;Constants;Numbers
Modules;Constants;Error Messages
Modules;Constants;Prop Types
Modules;Request Handling;Disable Actions
Modules;Mobile View;Gaps
Modules;Mobile View;Font Sizes
Modules;Mobile View;Mixins
Modules;Common Select Box;Static Data
Modules;Common Select Box;Multi Select
Modules;Common Select Box;API Select
Modules;Common Select Box;Frontend Select
Modules;Common Select Box;Search Select
Modules;Common Select Box;Dropdown
Modules;Axios Interceptors
Modules;Data Not Found Component
Modules;Spinner Component;Full Page
Modules;Spinner Component;Container Spinner
Modules;Typography
Foundations;Workflow;Sales for CRM
Integrations;Workflow;Sales for CRM
Modules;Workflow;Sales for CRM
Analytics;Workflow;Sales for CRM
Infrastructure;Workflow;Sales for CRM
Foundations;Workflow;Intake
Integrations;Workflow;Intake
Modules;Workflow;Intake
Analytics;Workflow;Intake
Infrastructure;Workflow;Intake
Foundations;Workflow;Patient Chart
Integrations;Workflow;Patient Chart
Modules;Workflow;Patient Chart
Analytics;Workflow;Patient Chart
Infrastructure;Workflow;Patient Chart
Foundations;Intake;Patient Chart
Foundations;Intake;Prior Auth
Foundations;Intake;BI Process
Modules;Dispense;Ticket Creation
Modules;Dispense;Template
Modules;Dispense;Delivery Ticket
Modules;Inventory;Movement
Modules;Insurance;Plan Mapping
Modules;Insurance;Primary/Secondary
Modules;Insurance;Dispense Ticket Items
Modules;Insurance;Order Setup
Modules;Insurance;Copay Programs
Modules;Insurance;Medical BI
Modules;Insurance;Eligibility Check
Modules;Insurance;Prior Auth Storage
Modules;Insurance;Payer/Supply Matrix
Modules;Insurance;Dispense Ticket Template
Modules;Global Changes;Requirements Per Therapy
Modules;Global Changes;Care Plan
Modules;Global Changes;Intake Form Design
Foundations;Other;User pref table
Foundations;Other;Company info table
Foundations;Other;Territory
Foundations;Other;Status of
Foundations;Other;Save draft
Foundations;Other;Mobile/Tablet support
Foundations;Other;Summary counts
Foundations;Other;Dummy answers
Foundations;Other;Shared tables
Foundations;Other;Drug monographs and work tickets
Foundations;Other;EVV
Foundations;Other;DSL editor
Foundations;Claims;Editing Fields
Foundations;Claims;Submit/Resubmit
Foundations;Claims;Invoice Voiding
Foundations;Claims;Invoice Generation
Foundations;Claims;Split Billing
Foundations;Claims;Multi-tenancy
Foundations;Claims;Data-out Integrations
Foundations;Nurse Schedule;Recurring Schedule
Foundations;EDI/HL7;835/837
Analytics;Feature Ideas;Wizard
Analytics;Feature Ideas;DSL
Analytics;Feature Ideas;Send SMS
Modules;Billing;Claim Creation
Modules;Billing;Billing Type
Modules;Billing;Payment Type
Modules;Billing;Documentation
Modules;Billing;Out of State Billing
Foundations;Billing;getBillable
Foundations;Billing;createClaim
Foundations;Billing;submitClaim
Foundations;Billing;updateClaim
Modules;Accounting;Accounting Calendar
Modules;Accounting;Close Year
Foundations;CRM;Modules
Foundations;CRM;Dollar spent tracking
Foundations;CRM;Reps dinner to lead tracking
Foundations;API;OpenAPI documentation
Foundations;API;Internal documentation
Foundations;API;External documentation
Foundations;Integration;Cover My Meds API
Foundations;Integration;EPIC app integration
Foundations;Integration;HL7
Foundations;Infrastructure;PG server
Foundations;Infrastructure;Snapshot
Foundations;Infrastructure;Log db
Foundations;Infrastructure;Separate VMs
Foundations;Infrastructure;DB cloning
Foundations;Workflow;Sessions flow
Foundations;Workflow;JWT expiry
Foundations;Workflow;Screen lock
Foundations;Workflow;Re-login
Foundations;Workflow;oAuth
Foundations;DSL;Partial DSL
Foundations;DSL;Patient readonly portal
Foundations;DSL;Versioning
Foundations;DSL;Conflict-save
Foundations;DSL;Draft auto-save
Modules;Label;Editor
Modules;Label;Generator
Modules;Label;Printer
Analytics;Reports;Basic reports
Analytics;Reports;SQL reports
Analytics;Reports;UI editor reports
Modules;Search;GPT4
Modules;Search;Alfred style
Modules;EVV;e-signature
Foundations;bd-dsl;grunt
Foundations;materialized views;Create View
Foundations;materialized views;Refresh View Every
Foundations;materialized views;api/query/save.js
Foundations;materialized views;validator
Foundations;materialized views;transaction
Foundations;materialized views;standard api/form save.js
Foundations;materialized views;view_{code}
Foundations;materialized views;nes cron/timer
Foundations;materialized views;api/query/index.js
Foundations;SECURITY;dsl update
Foundations;SECURITY;setup rules
Foundations;SECURITY;use rules in client
Foundations;SECURITY;use rules in server
Modules;FIXES;Schedule module
Modules;FIXES;Manage
Modules;FIXES;Global Search
Analytics;Base Reports;
Analytics;Referral;embed report
Foundations;REVIEW;sync on/off & active
Foundations;REVIEW;Module
Foundations;REVIEW;Query
Foundations;REVIEW;Report
Foundations;REVIEW;Security
Foundations;REVIEW;Workflow
Foundations;sync_mode full;CMS DME schedule
Foundations;sync_mode full;CMS NPI list
Foundations;sync_mode full;FDB NDC list
Foundations;sync_mode full;grunt/node
Foundations;sync_mode full;post/0000-tablename.sql
Foundations;Test sales rep security;sales rep with territory
Foundations;Test sales rep security;mgr with multiple territories
Foundations;General;IPv6
Analytics;Analytics;Sales rep patient log report
Analytics;Analytics;Dispense label
Analytics;Analytics;Dispense ticket
Modules;DSL;Form tabs
Modules;DSL;Subform editable
Modules;DSL;Field model editable
Modules;Intake;Careplan
Modules;Workflow;Benefits workflow
Modules;Workflow;Prior auth workflow
Modules;Workflow;Eligibility check
Modules;Preferences;Printers ID
Modules;Patient Snapshot;Dispense wizard
Modules;Patient Snapshot;Claim wizard
Modules;Workflow;Queue wizard
Modules;Workflow;Reports
Modules;Workflow;Wizard mode
Modules;Workflow;Archive reports
Infrastructure;Deployment;Fly.dev
Modules;Sales CRM;Manage goals
Modules;Sales CRM;Snapshot physician goal link
Modules;Sales CRM;Address validation
Modules;Sales CRM;Embed report
Modules;Sales CRM;Swim-lane view
Foundations;Smart Field;DOB Field
Foundations;Smart Field;Time Field
Foundations;Smart Field;BSA, LBW, IBW Calculation
Foundations;Smart Field;Note Template
Foundations;Select Field;Field Model Max
Foundations;Select Field;Single Checkbox Option
Foundations;Select Field;Foreign Source Checkbox
Foundations;Select Field;List Step
Foundations;Logic;Current Day Logic
Foundations;Logic;Age Field Logic
Foundations;Grid View;Subfields Required
Modules;Text Box;Size Definition
Modules;EVV;E-signature Reload
Modules;EVV;E-signature Storage
Modules;Sales Rep;Primary Territory
Modules;Sales Rep;Access Territories
Foundations;General;Figma
Modules;General;Figma
Modules;General;Primary Patient/Order workflow
Modules;General;Patient Snapshot
Modules;General;Insurance
Modules;General;CarePlan
Modules;General;Intake
Modules;General;Order
Modules;General;Dispense
Modules;General;Workflow Wizard
Modules;General;Workflow Queue Inspect Views
Modules;General;Mirror style/theme from patient snapshot to sales account snapshot
Modules;General;Workflow Queue Swim-lane View
Modules;General;Workflow Review
Modules;UX;Handle tab list overflow via dropdown
Modules;UX;Support datepickers in table filters
Integrations;UX;Address Verification
Modules;UX;Search Bar
Modules;UX;Workflow
Modules;UX;Wizard
Modules;UX;Queue
Modules;UX;Review
Foundations;DSL;DSL mass update
Foundations;DSL;Set bundles
Foundations;DSL;DSL Editor
Foundations;General;Data
Foundations;General;Flags/Secrets
Foundations;General;Replace .rule with /api calls
Foundations;General;Version change handling
Foundations;General;Printing
Foundations;General;BI / Reporting
Foundations;Cron Scheduler;Run .rule if possible
Foundations;Security;Homebase Auth
Foundations;Migration Tool;Basic
Foundations;Migration Tool;Advanced
Foundations;Migration Tool;Verify
Foundations;Queue;Inspect views
Foundations;Queue;Inspect views for eFax, eRx
Foundations;Queue;Right-click on grid table queue-view for inspect-view buttons/features
Foundations;Queue;Show Swimlane mode
Foundations;Queue;Launch Wizard based on Edge
Modules;Sales;Sales Rep / Manager view
Modules;Patient;Submenu
Modules;Patient;Multi-table grids
Modules;Patient;Generic patient alerts
Modules;Patient;Security - VIP / VVIP patients
Modules;Patient;Portal
Modules;Patient;Communication
Modules;Schedule;
Foundations;Inventory;DB
Foundations;Inventory;Form/Tables
Foundations;Inventory;Managed
Foundations;Inventory;Sync from FDB
Foundations;Inventory;Drug / NDC / HDCP
Foundations;Inventory;Item
Foundations;Inventory;Category / Subcategory
Foundations;Inventory;Bin
Foundations;Inventory;Supplier
Foundations;Inventory;Payer
Foundations;Inventory;Kits
Foundations;Inventory;Orders
Foundations;Inventory;Purchase Order
Foundations;Inventory;Receipt Order
Foundations;Inventory;Drug Order
Foundations;Inventory;Dispense Ticket
Foundations;Inventory;Ledger/Transaction
Foundations;Inventory;Inventory Ledger
Foundations;Inventory;Inventory Movement
Foundations;Inventory;Lot Tracking
Foundations;Inventory;Label Print Log
Foundations;Inventory;API
Foundations;Inventory;Inventory Item List
Foundations;Inventory;Purchase Order
Foundations;Inventory;Asset Management
Foundations;Inventory;Barcode integration
Foundations;Inventory;Operations
Analytics;Inventory;Reports
Modules;Dispense;Inventory movement
Integrations;Billing;Change API integration
Analytics;Module-specific;Analytics
Compliance;Module-specific;URAC
Compliance;Module-specific;Audit
Modules;Module-specific;Issue Tracker
Infrastructure;Module-specific;Settings
Infrastructure;Settings;Configuration
Infrastructure;Configuration;Sites
Infrastructure;Sites;Enable Sites
Infrastructure;Sites;Default site
Infrastructure;Configuration;Agency
Infrastructure;Agency;Enable 3rd-party Agency
Infrastructure;Agency;Default agency
Modules;Settings;Manage
Modules;Manage;2nd level menu: All, Bundle-based
Modules;Manage;Query
Modules;Manage;Report
Modules;Manage;Security
Modules;Manage;Workflow
Modules;Manage;Query
Modules;Query;Implement query builder
Infrastructure;Deployment;React compiled size 20mb+ troubleshoot
Infrastructure;PostgreSQL clusters;Environments
Infrastructure;PostgreSQL clusters;Dev/Test/Stage
Infrastructure;PostgreSQL clusters;Production
Infrastructure;PostgreSQL clusters;Setup K8s APIs
Infrastructure;PostgreSQL clusters;Customer
Infrastructure;PostgreSQL clusters;Create
Infrastructure;PostgreSQL clusters;DB password
Infrastructure;PostgreSQL clusters;Read-replica count
Infrastructure;PostgreSQL clusters;CPU/VM resources
Infrastructure;PostgreSQL clusters;Update
Infrastructure;PostgreSQL clusters;Backup/Snapshot env/customer to URL
Infrastructure;PostgreSQL clusters;Restore env/customer from URL .tar.gz
Infrastructure;PostgreSQL clusters;Clone env/customer to env/customer
Infrastructure;PostgreSQL clusters;Set Backup schedule for each env/customer
Infrastructure;Admin Server;Deno /api/service
Infrastructure;Admin Server;Fix /service/fly/deploy
Infrastructure;Admin Server;Sync_Mode - Full
Infrastructure;Admin Server;Download data from external APIs
Infrastructure;Admin Server;Drug (form_fdb_drug)
Infrastructure;Admin Server;Provider (form_fdb_provider)
Infrastructure;Admin Server;Payer
Infrastructure;Admin Server;Auto-update periodically
Infrastructure;Admin Server;SureScripts
Infrastructure;Admin Server;Import data per customer
Infrastructure;Admin Server;Log Search
Infrastructure;Admin Server;AppSmith
Infrastructure;Admin Server;Customer
Infrastructure;Admin Server;Database (new tab)
Infrastructure;Admin Server;Secret
Infrastructure;Admin Server;Login page content-editor
Infrastructure;Fly.io;WebSockets support / troubleshoot
Infrastructure;Fly.io;Customer cname url mapping / subdomains setup
Infrastructure;CI/CD;Continuous integration
Infrastructure;CI/CD;Code deployment
Infrastructure;CI/CD;VCS integration with gitlab
Foundations;UX Design;Base UI
Foundations;UX Design;Form multi-column views
Foundations;UX Design;Wizards
Foundations;UX Design;Module Design
Foundations;UX Design;Patient Card View
Foundations;UX Design;Editors
Modules;Development;Coffee->React
Modules;Development;Cleanup Old Code/Features
Modules;Development;Dashboard (Workflow)
Foundations;Development;NES Updates
Foundations;Development;Security
Foundations;Development;Ledger Entry Management
Modules;Development;Inventory
Modules;Development;Dispensing
Modules;Development;Search (Shortcut Bar)
Modules;Development;Schedule
Analytics;Development;BI (Analytics / Reporting)
Modules;Development;Onboarding Tool
Modules;Development;Migration Tool
Modules;Development;Issue Tracker
Infrastructure;Infrastructure;Database
Infrastructure;Infrastructure;AppSmith
Infrastructure;Infrastructure;Fly.io
Infrastructure;Infrastructure;Pipeline
