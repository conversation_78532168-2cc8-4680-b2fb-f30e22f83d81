<?php

function get_gpt4($sysprom, $query) {

    $API_KEY = getenv('OPENAI_API_KEY');
    $data = [
        "model" => "gpt-4o", // 128kb context
        "temperature" => 0.1,
        "messages" => [
            ["role" => "system", "content" => $sysprom], // 1000+ lines
            ["role" => "user", "content" => $query] // 50 lines -> give me answer for THESE ONLY
        ],
    ];

    $opts = [
        'http' => [
            'method'  => 'POST',
            'header'  => 'Content-type: application/json' . "\r\n" .
                         'Authorization: Bearer ' . $API_KEY,
            'content' => json_encode($data),
            'timeout' => 300 // Timeout in seconds
        ],
    ];

    $context  = stream_context_create($opts);
    $result = file_get_contents('https://api.openai.com/v1/chat/completions', false, $context);

    return ($result ? json_decode($result, true) : null);
}

function parse_csv($csv, $delim = ',') {
    $csv = (is_array($csv) ? $csv : explode("\n", trim($csv)));
    try {
        return array_map(function($row) use ($delim) {
            return str_getcsv($row, $delim);
        }, $csv);
    } catch (Exception $e) {
        print_r($e);
    }
    return null;
}

$outline_blocks = file_get_contents('./prompts/outline-blocks.txt');
$outline_full = file_get_contents('./prompts/outline-full.txt');
$sp_modsub = file_get_contents('./prompts/sp-modsub.txt');
$sp_task = file_get_contents('./prompts/sp-task.txt');
$teams = explode(';', 'Foundations;Integrations;Modules;Analytics;Infrastructure');

?>