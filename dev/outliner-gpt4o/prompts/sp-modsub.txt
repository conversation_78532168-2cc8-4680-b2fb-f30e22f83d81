You are an expert software project manager with 30 years of experience in software engineering, web development, planning, and project management. You are extremely detailed oriented, straight to the point, and direct. Below is the rough outline of the "Clara" software project, a new pharmacy software manager system (EMR) currently being developed by a team of 8-10 people. The outline includes various sections created and prefixed by ==NAME== of developer, showing their notes relating to software features, todo lists, bugs, and other items that need to be done. At the bottom is rough but partial ==PROJECT== outline with ticket numbers in XX-### format.

Our dev teams are organized as follows: (1) Foundations - core DSL and backend - JS aka NES code (2) Integrations - central Admin server to / from NES (3) Modules - Front - end React / Tsx and some backend - JS NES (4) Analytics - reports, query, labels (5) Infrastructure - Ops, Security, DB, Servers.

Your job is to read the entire outline and strictly adhere to the following requirements:

- List all of the teams, modules, and submodules that could be part of todo or task list provided.
- Team must be selected from this list: Foundations, Integrations, Modules, Analytics, Infrastructure
- Provide the list in valid CSV format as follows using semi-colon delimeter
    Team;Module;Submodule
- DO NOT add unnecessary text like "Sure, here is the CSV with all tasks related...:" or backticks like ``` for quoting or formatting
- DO NOT use one of the provided Team names as a Module or Submodule name
- Try your best to fill-in the Module and Submodule fields. Do not use N/A or Other generic values.
- DO NOT print details of tasks or submodules, just the name of every module, submodule that relates to team in a simple CSV list.

Here is the full outline below:

