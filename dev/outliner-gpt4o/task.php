<?php

include "shared.php";

$pattern = '/(?=^==\w+==)/m';
$ob = preg_split($pattern, $outline_blocks, -1, PREG_SPLIT_NO_EMPTY);
$sp = $sp_task . "\n" . $outline_full;

$ms = parse_csv(file('./prompts/modsub.csv'));
$mod = []; $sub = [];
foreach($ms as $r) {
    $mod[$r[1]] = $r[1];
    $sub[$r[2]] = $r[2];
}

ksort($mod);
ksort($sub);

$sp = str_replace(['[[MOD]]', '[[SUB]]'], [implode("\n    ",$mod), implode("\n    ", $sub)], $sp);
$up = 'Please filter your CSV output to the following excerpt of the above outline and ONLY show tasks that relate to the text below:';

foreach($ob as $b => $block) {
    $ust = $up . "\n\n" . $block;
    $retry = 0;
    while($retry++ < 4) {
        echo "Creating task list for block #" . ($b+1) . ($retry > 1 ? ', attempt #' . $retry : '') . "...\n";
        $ms = get_gpt4($sp, $ust);
        if(is_array($ms) && isset($ms['choices'][0]['message']['content'])) {
            $ct = trim($ms['choices'][0]['message']['content']);
            $csv = parse_csv($ct, ';');
            if(is_array($csv) && isset($csv[0][0]) && $csv[0][0] == 'Team') {
                if($retry < 4) {
                    foreach(array_slice($csv, 1) as $r) {
                        if(!in_array($r[0], $teams)) {
                            echo 'Invalid team: ' . $r[0] . "\n";
                            continue 2;
                        }
                        if(!isset($mod[$r[1]])) {
                            echo 'Invalid module: ' . $r[1] . "\n";
                            continue 2;
                        }
                        if(!isset($sub[$r[2]])) {
                            echo 'Invalid submodule: ' . $r[2] . "\n";
                            continue 2;
                        }
                    }
                } else {
                    echo "TODO: VERIFY BLOCK #" . ($b+1) . "\n";
                }

                file_put_contents('./output/task.csv', $ct . "\n", FILE_APPEND);
                continue 2;
            }
        }
        print_r([$b, $ms]);
    }
}

?>