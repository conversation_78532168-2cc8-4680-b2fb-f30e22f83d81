==BRANDON==

Integrations Needed

File Attachments:
•	S3 - like existing integration except possible centralize ?
•	Minio - open source s3 in k8s clusteR?
•	https://min.io/


SMS
•	Twilio
•	Telnyx
•	Had a call with them, they want our business so would be open to streamlining the 10DLC auth proc

Email templating
•	Mandrill
•	Sendgrid
•	Ideally use sendgrid or a newer templating engine/service, something that allows us to template everything within ARJS and send a json blob off async for delivery


Notifications:
•	Slack
•	Teams
•	Push to client
•	Firebase
•	Something native electron-ish since we wont have a native app for a while

==BRANDON==

Faxing:
•	Phaxio
•	Per customer number is 1.00 per reserved number, dedicated callback uri and outbound
•	Ringcentral
•	This can be supported by sending an email to their email-> fax email address, as long as we template it correctly.

Geo Address:
•	Radar (proxy via Admin server for bulk pricing)

SSO:
•	Keycloak
•	Gluu or others like https://goauthentik.io/

Monitoring:
•	Basic keepalive / uptime monitoring
•	K8s full monitoring
•	Falover testing and alerting
•	Sentry for exceptions
•	Performance monitoring of request lifecycle
•	Centralized Logging

==BRANDON==

Integration Requirements  / Implementation ToDo's

Surescripts:
•	Test all required message types and make sure we satisfy the minimal basic requirements for Surescripts certification as well as Drummond certification (DEA vendor)

Payment Gateway:
•	Authorize.net ?
Some kind of middleware api that can handle many different payment rails.

Claims:
•	Change Healthcare
•	Primary NCPDP claim processor
•	Medical Claims processing
•	Redsail
•	Secondary or additional primary NCPDP claim processing

==BRANDON==

PVerify:
•	We still need to implement back office payer workflow, when a payer is a non-edi payer they do not give instant responses for eligibility verification, these can take up to 48 hours to be completed. https://postman.pverify.com/#backoffice-payers
•	Pverify will return to a normal EligibilitySummary or EligibilityInquiry POST , return a status of 3, Pending and the response field of IsPayerBackOffice will be True.
•	These requests need to use the EligibilityInquiry  API to get information about these payers so we will need to also parse the json into a useable format for nes to create a PDF
•	We will need a mechanism to check after 24 hours if the eligibility request has been completed, we can use pgboss cron and have a blanket job scheduled that checks for requests from the past x days that still have not been fulfilled, attempt to getch the eligibility data , if there is a response of processed now on the EligiblityInquiryResponse then we can attempt to fetch a PDF for that requestID
•	If there is a PDF and the response is completed, upload to s3, update the customer_id.db and default.db to set the s3_filehash for this requestid and send a PUT to the Clara NES server with the data so it can have the filehash to present to the user

==BRANDON==

B&D Dev Ops Tools:

ESLint or CoffeeLint Plugins:
- Use an AST parser against the compiled DSL under the bd-dsl directory (should be in ../bd-dsl for /bd or allow a setting in the plugin to set a custom directory). If coffee is not compiled, compile the DSL first before loading into the AST.

For Handlers (look for files where with class *BaseView extends CRView)
* Check that all validators from the DSL are implemented (could also just implement as a warning when deploying the DSL)
* Check for dead validators that aren't in the DSL
* Check that fields used in the handler aren't in the DSL references
* Check for sync calls, should all be async or show an error
* Check API calls to make sure path exists in nes
* Check that handlers also have the extends portion at the bottom i.e.
    * class Billing_claim_genericHandlerView extends Billing_claim_genericHandlerBaseView
    *
* Check that any validator / transform functions are not setting any values if in readonly mode: i.e.
* add_billing_assessment: (subject_id) =>
* return if (@mode is 'read') or not subject_id
* Check that field types are getting set to the appropriate value types (i.e. date fields are MM/DD/YYYY etc)
* @parent.value_field 'expiration_date', exp_date.format("MM/DD/YYYY"), true
* Check for dead functions that aren't being used
* Check for validator loops (i.e. a validator calls a function which sets the field that contains the validator and has true, true on setting value_field:
* @parent.value_field 'pri_method_id', insur.billing_method_id, true, true
* Check if we are using non-required fields without first seeing if they have a value. (using ? i.e. this.data.fieldname?)
* Check that validators / transforms that use different fields through dd.value_field are attached to every fields that it passes through (warning only)
* Check that all async calls have a corresponding .fail function defined.

==BRANDON==

B&D Dev Ops Tools:

DSL Lint Features or during deployment (in grunt compiler)
* Check Validators / Transforms that are not implemented in the client.
* Check that offscreen fields that have model.if defined are also in the model.sections (otherwise if logic doesn't work)
* All foreign key referenced fields are also listed in the indexes
* Check that fields listed in the if logic are only listed for one field (it doesn't work if the same field is show/hidden in multiple ifs, in the same field.model.if is fine though)
* All fields not listed as offscreen are in the model.sections
* Warning if gridedit fields have more than 5 fields listed in the gridfields
* Warning if gridedit fields have required fields not listed in the gridfields if gridadd != ‘flyout'
* Check that listed model.source forms exist
* Check that subform fields are in their own section_group by themselves.
* Warning If model.source is an array, and has <= 5 entries, field.view.control should be ‘radio' or ‘checkbox' instead of ‘select'
* Error If model.view.control = ‘radio' and model.multi = true, control should be select or checkbox.
* Check that model.auto_name is not referencing fields contained in the if: logic. Remember that they can be an array of fields or a template string for the auto_name. Provide a warning but not an error if this is the case.

==BRANDON==
B&D Dev Ops Tools:

DSL Features Request
* Transforms that run once only on form create only (Ideally make this an option in the PrefillSelect transform)
* Help tooltips, fields that have a tooltip that will show on hover.
* Validator that checks if the value of 2 fields (or a tuple of fields) have the same values / ids and presents an error if so. I.e. if I have an inventory_id and inventory_other_id: field and I want to make sure they are not the same value, I could use the validator. Also for the tuple, I could check if 3 fields all have the same value as another 3 fields and if so, present an error message. (like 2 drugs of the same inventory item and the same dose and the same quantity on an order)
* An option under if: to clear_fields with a list of fields to clear the values from (if hidden too).
* An option in prefill to pull from the parent form inside of subforms (i.e. prefill:[‘parent.record_number'] would pull in the record_number into the subform. This should work for hidden / read only fields as well.
* An option in the Prefill transform to use an api endpoint and pass in the form's current data and prefill in with the results (results should be an object with key value pairs stored under body.results, body.error would contain an error message)
* A new type of field for “xml” data that will show an xml icon that when clicked, opens up a modal with the xml data (you can use this: <EMAIL> for the icon, 50x50 for the size). This is an option for the viewer (https://www.npmjs.com/package/react-xml-viewer#live-demo)

==OSAMA==

Queues
•	Performance issue
•	User comments support functionality
•	Background auto refresh
•	Workflow swimlanes drag and drop and force refresh on drop
•	Integrating workflow and action events in workflow queue
•	Security integration
NES
•	On the client side in some situations (mostly for field sourceid=code) GET calls `not filters` i.e &filter=!Yes are  not working
DSL
•	DSL record  prefilling is sending redundant API calls (since it prefill  is called for all the subforms as well if  two forms have the same prefill it sends to separate call to fetch the same data (we could use pooling but that needs to be tested if it will work))
•	Make fields readonly based on if condition
•	Client Validation for can_sync fields on dsl level to keep the ids consistent. > 100000 etc.
•	Handle advanced filters in find view currently advances and basic filters are treated the same way
•	Section tabs for DSL sections
•	On dsl form save/update instead of refreshing the grid add/highlight just the row in grid (on  top of list)
•	Grid count issue on list view `1 out of 10` and pagination support
•	Double section header in case of subforms (if all the fields in a section are  hidden the section header is  still visible.)
•	Make archived field checkbox
•	Add deleted field to form filters
•	Removed archived field from query  module find view
•	Need to add edit support to  DSLTabbedView  (DSL view being shown in ss_message screen inspect component is DSL TabbedView)
•	List view search field revert to auto_name if search field is  not found in DSL

==OSAMA==

Workflow Wizard
•	Need if condition in wizard to show/hide field
•	Need to handle dependent form saves within the same wizard instance (i.e form 1 needs to be saved before you could save form 2 because form references id of form 1)
•	Wizard has an issue with same form showing in to different steps
•	Issue with on save validation where hidden form are not validated correctly because they are offscreen
•	No preset support for list view in wizard  (option where we allow users  to add multiple forms)
•	Missing support for automatic preset value generation (for example if wizard is being opened from queue we want to fill in some preset value from queue data object into wizard)
•	Wizard review process
•	Notification if someone else is also working on the same wizard entry
Miscellaneous
•	Company config usage on client side is not being used everywhere
•	Extra open tabs should be shown as in dropdown instead of scroll
•	Navigation need to be fixed in a number of modules
•	Double click to open tab is not working
•	Security implementation on client side
•	Need to updated the code in multiple module where instead of using standard component separate functionality was introduced i.e  Snapshots in Patient and Jquert
Patient SnapShot
•	Patient snapshot logic needs to be updated to support fixing issue with multiple intake and care plan linking (it broke when we added back intake_id to every form)
•	Need to add proper support for wizard in patient snapshot (jerry rigged it before previous demo)
•	Audit trail need to be integrated with log_ form api (right hand side widget)
•	Need a place to show all in progress wizards for  that patient on snapshot
Pre Production
•	Bundle react.js and coffee into one and keep the bundle size to minimum so just 3 files are being served to client app.js react.js lib.js

==PATRICK==

SureScripts
•	Need to test refill request once we have the full dispensing workflow completed
•	Need an option to select an existing payer in the pop-up instead of just popping up the payer creation (or the workflow generator) and work on the co-dependencies for existing forms so you can add them in the workflow if not there (i.e. missing physician record for patient_prescriber record)
•	Need some kind of snapshot review with just the basics when viewing the details tab

Inventory
•	Add process for handling transfers until receipt and put inventory “on-hold”
•	Need to handle incoming inventory from purchase receipt to import serial and lot numbers (should be CSV or excel file?)
•	Need to be able to “scan in” inventory
•	Have a way to tie in nursing agency record to a billable item for nursing hours

Ordering
•	Need some kind of embedded grid view to be able to select multiple diagnoses against an order and order item. (right now it is only 1)
•	Need some kind of grid view to to assign the payers against the order / order item
•	Need to support compounding
•	TPN machine integration
•	Factor dosing module (with bleed/prophy/procedure dosing)
•	Need better logic layout for multiple payers against items (i.e. Medicare -> Medicare Advantage/Supplemental -> Copay)

==PATRICK==

Claims
•	Build out NCPDP “builder” on the backend using pg-boss
•	Build out NCPDP schema and validation
•	Medical Claims
•	Some kind of way to transform an NCPDP claim to a medical claim and back and forth (this is a common scenario according to Kinga)
•	Medi-Cal support
•	Invoicing (payer/patient)

Benefits Investigation
•	Need to support the E1 for pharmacy benefits investigation

Prior Authorization
•	Need a way to auto-fill the PA form for the physician (CA is standardized)
•	Look into ePA to see if we can get the list of required fields to populate for the physician
•	Support unit tracking / renewal alerts

Intake
•	Need an intake workflow for benefits investigation and prior auth


==MUBIN==

•	Standardize styling, and create some sort of framework for that, right now everything is very ambiguous like @color-one, @color-two etc on the React side with a bunch of other variables declared. Additionally, there is tons of css through which we are trying to set margin and padding into different .less files that should also need to come from one and only one place. For example, border-radius for the entire application needs to come from the one variable/mixin only
    .border-radius(@radius: 5px) {
         -webkit-border-radius: @radius
        -moz-border-radius: @radius
        border-radius: @radius
    }
•	A UI testing framework that is already in progress using Playwright, but it is still a TODO item.
•	Global search component, that is functional, but can be improved overall.
•	Display relevant popup on the screen, mostly it is error popup all over the places.
•	CoffeeScript will be removed, eventually, so going forward, we need to serve all of the styling from React side.
•	There should be more type-checking on the React side(right now only here and there).
•	Prune jQuery from the React side.
•	[dev onboard]Some sort of official/unofficial docs to bring new dev(or other team members) up to the speed without explicitly explaining a lot.
      example.cson
        fields:
          field: model: .., view: ..
        model: ..
        view: ..

    Something similar for the React/Coffee side and for NES that explains the essence of application

•	pg-escape needs to be replaced in NES. it has been dead for 8 years now.

==SHOAIB==

1.	dsl deploy fixes, column alters, fk code update, log tables update column when column changes.
2.	fix dev ops pipeline single click create fly instance and deploy latest code, auto deploy latest code to all live machines, take db backup before deploy. (I'd like to do dev ops stuff)
3.	adding system alerts mem usage and cpu usage in nes.
4.	update sql files transaction block for full files
5.	update logger to be not verbose all over
6.	generating test data
7.	nes clean up remove unused code
8.	standardise nes code standard.js or equivalent
9.	standard way of doing Schema validation
10.	data import from customers
11.	crash alerts
12.	patient app.
13.	tool to intelligently see logs.
14.	data analytics pipeline snowflake semblance, to acquire data that gives us insight on customer usage
15.	Chat-teams integration.
16.	Ticketing system.


==ALTAF==

Points of Project
All comming implementations dynamically/commonly/generic functionalities by React.

* App optimization handling by configs of Vite, CRACO, TS config, Webpack as well as babel plugins.

* Common methods to handle animations by React invcluding less and if needs coffee than just to use only less.
* Breadcrumb

* Notifications handling by commonly.

* Custom Skeleton that can be used in table, cards, tabs boxes et.

* There should be a common component for each block like buttons, cards, texts, flyouts, popus Modal and drawers.

* Repeated functionalitis should be handled by using custom hooks as well as functions'

* We should create constants as much as we can like strings, messages, numbers ie error messages, prop types etc

* Disable while sending a request in Its pending time actions should be disabled until request gets response or rejection. Like buttons icons, icons, clicks on DIVs like table rows etc to be avoid from duplicating requests.

* To handle the compact or mobile view need to rechange values of gaps, font sizes by generic method like mixins.

* A common select box that should cover up all the functionalities like with static data multi select, API select, frotnend select, search select and its dropdown etc.

* Axios interceptors'

* Data no found common component handle its empty covered area to point hints or a clue to its container.

* Spinner component should be in multiple variants like full page we have already but contiainer spinner should be and its variants.

* Typography


==CHIRAG==

- Workflow
    - Sales for CRM
        - Sales Reps
            - Go after patients (mostly factor)
            - Go after physicians / physicians group / hospital
                - Get referrals during discharge
            - Should be assigned territories that can change
        - Get alert when patient change status
                - Can contact physician based on this
            - Should be able to see read-only basics of patient chart
                - NO bIlling info, YES dispensing
            - Should be able to add new contacts
                - Mark them as type = physicians / physicians group
                - Import all data for physicians from FirstDataBank into form_physician
                    - And link to contact record
                    - form_physician_address can include group name, address per physician
                - Take prospect > lead > referral
                    - Measure each step of this
        - Goals per Rep
            - Grams of IG / Units of Factor
            - Dollar amounts
        - Qualifying leads
            - Have budget per rep, per quarter, with limits on how much can be spent
                - On physicians / on physician group
                - Example Limit: Factor reps can get higher budget
            - Figure out conversion cost per lead being in by sales rep
                - For entire physician group, include all their patients
        - House accounts (could be multiple)
            - Might be used for non-competes
            - Got referrals directly from physicians
                - Might get commission
    - Intake
        - Comes in via fax
            - CPR: Shared drive location, tries to figure out what patient to link to
            - Need referral form by therapy using ARJS, blank PDF
                - Q3 2023: Maybe AI parse to create patient etc.
        - Intake team (any CSR/office)
            - Accept fax etc., to verify all info is available
                - Assign records to patients
                    - Might create a patient from this
                - Assign to Intake teams
                - Mark as ignore
                - Support multiple fax#
            - Request info:
                - Factor: Work with sales rep directly. Might be good to integrate with Teams
                - Directly from Physician
                - Work with patient
            - After all info is available, run BVV
                - Eligibility check (very basic confirmation - valid insurance/date)
                - Not assigning care-team to patient
                - Benefits verification
                - Medical
                    - Figure out co-pays
                - Pharmacy
                - Patient chart sort insurance
    - Patient Chart
        - Status
            - Pending = all new data entry (no clinical forms)
            - Active = everything
            - On Hold = all data entry (no clinical forms)
            - Inactive/Cancelled/Referred/Deceased = Nothing except change status
        - Intake Status (per insurance)
            - New
                - Prescription / Order (subform)
                    - PDF attached
                    - Allow inactivating old rx and adding new with dates
                - Enter patient insurance
                - For each info run (Eligibility/BVV/Patient Services/Prior Authorization)
            - Eligibility (optional)
            - BVV (form_patient_bvv per insurance)
                - Missing Clinical
            - Patient Services
                - Copay Assistance
                - Write offs
                - PAPs
                - Foundations
            - Prior Auth (does not need dosage info)
                - Pharmacy runs dosage info
            - Claim Status
                - Pharmacy runs
            - Initial Assessment
        - Billing Assessment => Onboarding Process
            - Link successful ones to Intake Status flags
            - Billing Type manage table
                - On-service required: multi-select therapy
                - Patient > Intake wil show on-service when Billing Assessment exists for each on-service required for therapy

    - Episodes of care? No
    - Do we need to make workflow trigger events other than dashboard views, i.e. billing events, dispense events etc
    - Are rules driven based on Therapy vs. Dx. vs. Drug name vs. Drug specific dosage
    - Dashboard - numeric status / lane - support for KanBan

==CHIRAG==

- Intake / Patient Chart
    - What all should be setup as part of the intake process - per therapy
    - Prior Auth / BI process
- Dispense
    - What drives the creation of dispense tickets?
    - Template for dispense tickets based on therapy/order
    - Delivery ticket - with/without pricing
- Inventory
    - Inventory movement as part of dispense ticket
- Insurance
    - Do we pre-build a db of mapping insurance plans to hours/meds?
    - Figure out primary/secondary insurance beforehand? Before dispense tickets?
    - Do we need to factor in patient's insurance companies to decide which dispense ticket items to bill/print? YES
    - Setup insurance per order per product
    - Insurance copay pap programs have $ limits and annual expiration
    - Medical BI at dispense or service
    - CSP Currently - Manually runs prior auth before eligibility. Then Rerun eligibility on Medical patients monthly
    - Can we run eligibility auto or make it easier? YES
    - Prior auth must be stored at prescription level with start/stop/units - for drug
    - Payer/insurance x supply matrix for dispense ticket billable/print-only
    - Dispense ticket template based on therapy - allow to customize per patient per therapy
- Global changes
    - Requirements per therapy
        - Care plan requirement by pharmacist / nurse
        - Intake form design by therapy

==CHIRAG==

- Other
    - User pref table
        - On smaller resolutions, switch to compact style
        - Shortcut for search: F1 etc.
        - Printer selection for documents vs. label
    - Company info table - address, billing, logo upload, phones/fax etc. - for printing vs showing on screen
    - Territory
        - ID/Name per territory
            - Usually zip-code
        - Could be categorized based on size of physician group
        - Linked to Physician / Physician Group
        - Physicians must be linked to Physician Group
    - Status of: e-sign, document upload
    - Save draft for all tables without validation. Query all tables for non draft only
    - Mobile/Tablet support for Homebase
        - Mobile = Reports
        - Tablet = Everything (auto-switch to compact mode)
    - Summary counts at top of every table - easy filter
    - Dummy answers in form upon shortcut
        - Yes, lets make this
    - Shared tables
        - Drug monographs
        - Zip-code
        - NDC pricing/fees
        - NPI Physicians
            - form_physician can profile on NPI
                - subform for multiple addresses
    - Drug monographs and work tickets to that as well. Work tickets are how they compound.
    - EVV https://www.medicaid.gov/medicaid/home-community-based-services/guidance/electronic-visit-verification-evv/index.html - Yes needed
    - DSL editor with table version sooner? 2023 Q2

==CHIRAG==

To discuss:
- Is editing a billed table row is allowed? What if the invoice is voided?
- Do need to allow editing specific fields of claims before submit/resubmit? How much should we allow editing and what should be locked? E.g. can biller insert rows for inventory unrelated to invoice/dispense? Can they modify dollars/hours?
- Details of the claim back-and-forth process to show on the invoice - do we just show original invoice and as-of-date end total of what insurance(s) paid vs balance? YES, SUMMARY
- Can we always combine nurse hours and dispense tickets in a single claim or invoice? What is the many-many relationship between these?
- Other than nurse visit hours and dispense ticket meds, what else is billable to patients / insurance / programs
- When is a claim and invoice generated?
- Split billing
- Multi-tenancy
- Data-out integrations
- Nurse schedule - linking to encounter is issue. How do we handle recurring schedule?
- EDI / HL7 - when do we need this? 835/837?

==CHIRAG==


Feature ideas
- Wizard - open readonly last note for review
- Wizard - open past wizard and jump to step by table name
- DSL: help text for forms
- Send sms while filling form remotely, e.g. list of symptoms


Billing/Claim:
- Automate claim creation based on rules / history / preset templates
- Billing type: invoice/payment/refund/discount/waived (100% discount)
- Payment type: patient, insurance, program e.g. pharma copay
- Is there any documentation we need to send for each claim?
- Out of state billing? Yes, ask for license per pharmacist by period. Pharmacist sign off?
- NES > getBillable() for any table should return line items to add to a new bill/adjustments
- NES > createClaim() looks at invoice and creates one or more claim jsons based on all available info. This includes figuring out primary/secondary, HCPCS/Dx codes etc. - Shoaib
- NES > submitClaim() sends claims to Change Health and later brings adjudicated data for review and acceptance. Ok to void on unpaid claim
- NES > updateClaim() based on adjudicated claim or new billable data, for resubmission

Accounting:
- Accounting Calendar: start month, year - lock billing/invoice changes except for special override roles
- Close year - no unlocking for anyone

==CHIRAG==

Requirements:
- Each encounter/dispense billed should have an Application Entry table that can be reversed/voided and regenerated
- OpenAI azure using function calling
- CRM modules with dollar spent - with reps dinner to lead tracking
- USP monograph / Drug prices Deno import and NES
- User demos / training
- API OpenAPI documentation for internal vs external
- Cover My Meds API integration
- EPIC app integration for hospitals HL7
- PG server with snapshot, log db, separate vms, db cloning etc
- NES: partial dsl for patient readonly portal
- Figure out proper flow of sessions with JWT expiry, screen lock, re-login, oAuth etc.
- Shoaib: 2+ editors same record conflict handle/ DSL versioning / conflict-save / draft auto-save to backend

Modules:
- Label editor - dispense ticket, inventory label, payment receipt, invoice, etc. Include logo / company address/contact/billing info.
- Label generator / printer - any table view row to print with preview or automatic without preview (https://qz.io/docs/getting-started)
- Analytics - basic reports in ui, written with sql and written with ui editor
- Search: using gpt4 / Alfred style
- EVV / e-signature

==CHIRAG==

      bd-dsl grunt
        select id,limit=1 from every table and only show if error
        show extra tables on server, not with matching cson
      materialized views
        new field:
          Create View: Yes/No
          Refresh View Every: [minutes]
            on nes init, add to pgboss queue if not already in queue
        save to: api/query/save.js
          validator to check if sql includes %s, %L etc.
          transaction
            standard api/form save.js
            create view_{code}
              delete/recreate on every save
          update nes cron/timer for pgboss
        when calling api/query/code (api/query/index.js)
          refresh if ?refresh={truthy e.g. 1, true}
          return data from view_, with filters specified
      SECURITY
        all dsl update
        setup rules
        use rules in client
        use rules in server
      FIXES
        Schedule module
          Primary therapy should pull from patient's first intake
          For single event in series, if override is entered, can we readonly/hide visit schedule date/start/end?
          Bug in save: ovverride_end_time
          Drag-drop somehow goes back 12hrs
          Drag-drop should update popup date/time
          Don't show 12am in popup
          Pretty print "Update following events" list
          Fix position of +2 more popup
          Default to repeat every 1 period
          Why is repeat-every Weekly Repeat On a single select?
          Save on drag-drop: Drag-drop should set Override start
            If override start date/time = event start date/time, then clear override
          Flyout should show Save / Cancel, not Save / Close
          Feature request: Clicking on Day# in Month view should switch to Day view
        Manage
          Therapy list
        Global Search
          How is it finding archived/deleted patients?
      Base Reports
      Referral
        embed report: referral intake
      REVIEW
        Allow sync on/off & active for Module/Query/Report/Security/Workflow
      Copy all security rules to d-clararx
      sync_mode full
        get data from dif sources (do not rely on form.id to be same)
          Import CMS DME schedule into form_cms_dme
            https://www.cms.gov/medicare/payment/fee-schedules/dmepos/dmepos-fee-schedule

          Import CMS NPI list into form_cms_npi
            filter significantly
          Import FDB NDC list into form_fdb_ndc, ICD codes into form_fdb_icd
        use grunt/node to create .sql files
          add check for max(created_on/updated_on)
        add them to post/0000-tablename.sql
      Test sales rep security
        as sales rep with territory
        as mgr with multiple territories

==CHIRAG==

    shoaib
      wiki document ipv6
        fly ips allocate-v6 -a d-emubin-nes

    patrick
      import careplan template defaults
      discuss pmp
      analytics
        Sales rep patient log report
        Dispense label
        Dispense ticket

    osama
      coffee/dsl
        form tabs
          model.sections.tab = X / Y
            allow sections, then tabs with sections, then sections etc.
          subform editable
            multi-line tabs with filters for loading/maint-dose
        field.model.if.Yes.editable = bool
      intake/careplan
        save intake -> auto-create careplan active
      workflow wizard
        per patient insurance
          launch benefits workflow
          launch prior auth workflow

      elig check from patient insurance
      pref > printers id
      patient snapshot + dispense wizard
      patient snapshot + claim + claim wizard
      workflow queue + wizard
      reports in both sites
      Workflow Queues
      Wizard mode
      Archive reports/workflows etc.
      Optimize fly.dev deployment
      Sales CRM
        manage > goals
          IG Grams / Period
          IG Period length: Month / Quarter / Year
          same for revenue
        fix snapshot physician goal link
          goals/revenue for physicians are in sales_account
          so snapshot box for goals should link to edit sales_account for physician
        Address validation
        embed report for Sales Rep / Sales Mgr.
          sales_crm_dash_manager
          sales_crm_dash_rep
      Swim-lane view
        Popup
        Drag-drop to move
        Change status

==CHIRAG==

    Altaf
        We need to be able to define sizes of text boxes, the default area is usually too small
    Mubin
        evv - remember & reload last e-sign
            Support for storing e-signature (or last one against the user)
        Sales reps should have a primary territory and then access territories (also need a smart territory field to show reps assigned, can use in the intake form)
    Osama
        coffee smart field
            DOB field should show the age next to it (smart field like the weight) Need a smart field for time as well ‘5 H for hours converts to minutes'
            Auto-calculate BSA,LBW, and IBW fields
            Add note template text based on subjects (see list_bill_note_template and list_note_template)
        coffee select
            use field.model.max to limit how many multi:true count items can be selected
            Need option to select 1 or many of others in a checkbox (i.e. Select None or an Select All) Add support for a single checkbox option (can we store the value as Yes/No?)
            Need an option to “blow out” foreign source checkbox to show all option (i.e. using this for symptoms based on disease / therapy and supply / nursing codes). We can cap it at 20 options?
            Support the list_step (similar to above) which will show managed filtered bullet points
        if-logic
            Support {{now}} when checking if logic against the current day
            Add support for age field that pulls in and auto populates based on patient dob (need to use in if logic for pregnancy checks)
        For grid views, need support for subfields.{name}.required. could use multi subform editable for this

==CHIRAG==

    General
      Figma
        Primary Patient/Order workflow
          Patient Snapshot
            Insurance
          CarePlan
          Intake
          Order
            Dispense
        Workflow Wizard
        Workflow Queue Inspect Views
        Mirror style/theme from patient snapshot to sales account snapshot
        Workflow Queue Swim-lane View
        Workflow Review
      UX
        Handle tab list overflow via dropdown
        Support datepickers in table filters
        Address Verification
          https://radar.com/product/address-verification-api

        Search Bar
          Support sales_account, based on access
        Workflow
          Wizard
            Select section_groups/sections to show
              For existing & new forms
          Queue
            Swimlane mode with Edge->Wizard link
          Review
            Audit track which field can be edited/approved
            Show before/after comparison
      DSL
        DSL mass update
          Set bundles
        DSL Editor

==CHIRAG==

    General
      Data
        Admin CMS import
          https://download.cms.gov/nppes/NPI_Files.html

        Sync_Mode (DSL.model.sync_mode)
          Full
            Import full table data from Admin console
            Set period per table
          Mixed
            Support POST api/form _meta sync_mode=mixed
              So nes/save.js creates new IDs under 10k
            In bd-dsl, as part of grunt dsl deploy
              Show all recent changes for IDs < 10k in all mixed tables
              Create post .sql file with recent changes
        Flags/Secrets
          NES should send all front-end flags via /api/version
        DSL.field.model.two-way-write
          Save to multiple destination e.g. H&P, PMP Opt-Out
        Replace .rule with /api calls
          Handle scheduling via pg-boss, especially for multiple NES instances
        Version change handling for
          Workflows
          Reports
          DSL changes
            Show records
            Print records
            Use data in reports
      Printing
        Wizard forms print
        Label assignment
          Based on
            Drug (Inventory item)
            Ship-To State
            Site
            Agency
      BI / Reporting
        License ActiveReportJS
        Use read-replicas for all BI report queries (/api/query, /api/view), instead of primary DB
        Export to pretty Excel
          https://stackoverflow.com/questions/21335057/how-to-style-xlsx-files-in-node-js?rq=4

==CHIRAG==

    General
      Cron Scheduler
        Run .rule if possible
      Security
        Homebase Auth
          Store api/version/ Flags in window.NESFlags
          First get api/access
          Then check us_state
      Migration Tool
        Basic
          Import Patient CSVs
        Advanced
          Live sync
            Migration tables on client instance
              config
                import period per cpr+ table
                source server + replication params / credentials
                status view
            Replication
              https://www.sqlservercentral.com/articles/configure-transactional-sql-replication-between-on-premises-sql-server-and-aws-rds-for-sql-server

          Verify
            ensure all data imported is consistent and will not crash en db

==CHIRAG==

    Module-specific
      Queue
        Inspect views
        Inspect views for eFax, eRx
        Right-click on grid table queue-view for inspect-view buttons/features
        Show Swimlane mode
          On drag&drop, Launch Wizard based on Edge
      Sales
        Sales Rep / Manager view
          Use ARJS

      Patient
        Submenu (Showing Snapshot...)
          Sort Submenus by dragging except for snapshot
          Make bar taller and easier to click
        Multi-table grids (e.g. patient chart showing all ad-hoc forms)
        Generic patient alerts
          src form/id/field
          dst group
          auto-set
        Security - VIP / VVIP patients
        Portal
          SMS Audit portal setup without .rule
        Communication
          Figure out if/how to make old Chat work
      Schedule

==CHIRAG==

    Module-specific
      Inventory
        DB
          Form/Tables
            Managed
              Sync from FDB
                Drug / NDC / HDCP
                  w/ Pricing
                ? Supplier
              Item
                Security/Access
                NDC (link to FDB if NDC, else no link)
                Item UOM Matrix
              Category / Subcategory
              Bin
              Supplier
              Payer
                Fee Schedule x Item (not full FDB list)
              Kits
                Kit Line with
                  Item#
                  Bill / Print Inv Qty
                  Qty in Kit
                  Comments
                  Applies Only to Fill#
                    1
                    2
                    3
                    4..
            Orders
              Purchase Order
                Purchase Line
              Receipt Order
                Receipt Line
              Drug Order
                Drug Line
                  Import Kit as individual lines
                  Fields
                    Bill Qty.
                    Print Qty.
                    Inventory Qty.
                    Applies Only to Fill#
                      1..2
                      2
                      3
                      4..
                  Payer Line
                    Bill Qty.
                    Bill Unit
                    Bill %
              Dispense Ticket
                Fields
                  Fill# (1+)
                Dispense Line
                  Link to original kit# per line
                  Lot#
            Ledger/Transaction
              Inventory Ledger
                Inventory Movement
                  Between Bins
                  Between Sites
              Lot Tracking
              Label Print Log
          API
            /api/inventory/
        Inventory Item List
          Show Matrix of Fee Schedule by Payer
          Label Design
        Purchase Order
          EDI 850 - via Change API
        Asset Management
          Calibration
          Preventative Management

==CHIRAG==

    Module-specific
      Inventory
        Barcode integration
          JS/SDK
            Implement https://github.com/undecaf/zbar-wasm

              Demo: https://undecaf.github.io/zbar-wasm/example/

            Or: https://github.com/samsam2310/zbar.wasm

              Demo: https://zbar-wasm.github.io/demo/

            Parse GS1: https://github.com/PeterBrockfeld/BarcodeParser

            GS1 Application Identifiers used: - (01) Global Trade Item Number (GTIN) - (10) Batch or Lot Number - (17) Expiry Date (YYMMDD) - (21) Serial Number - (30) Quantity Each Example: GS1 Data Matrix = (01)50344206455109(21)25184376974640(17)260219(10)P100598577 GS1-128 = (17)260219(10)P100598577(30)10 GS1-128 = (01)50344206455109(21)25184376974640
          Operations
            Receive
            Use
            Move
            Adjust
            Print Labels
        Reports
          Inventory on Hand
            As Of
          Inventory Movement / Ledger
      Dispense
        Move inventory as part of dispense ticket posting
      Billing
        Change API integration
          Store credentials per customer in AppSmith, read in NES from env[]

==CHIRAG==

    Module-specific
      Analytics
      Compliance
        URAC
        Audit
      Issue Tracker
        Create new YouTrack ticket with customer/env-tag
      Settings
        Configuration (company.cson)
          Sites
            Enable Sites
            Default site
          Agency
            Enable 3rd-party Agency
            Default agency
        Manage
          2nd level menu: All, Bundle-based
            Go through all the DSL.model.bundle options and show them at 2nd level
            Do not show 'Editor' bundles here
              Query
              Report
              Security
              Workflow
        Query
          Implement query builder using
            https://www.datasparc.com/sql-query-builder/

==CHIRAG==

    Infrastructure
      Deployment
        React compiled size 20mb+ troubleshoot
      PostgreSQL clusters
        Environments
          Dev/Test/Stage
          Production
        Setup K8s APIs for
          Customer
            Create
              customer_name
              DB password
              Read-replica count
              CPU/VM resources
            Update
              DB password (overwrite without requiring old password)
              Read-replica count
              CPU/VM resources
          Backup/Snapshot env/customer to URL (S3 or local)
          Restore env/customer from URL .tar.gz
          Clone env/customer to env/customer
            Internally call Backup/Restore if needed
          Set Backup schedule for each env/customer
            Use default for env if not set

      Admin Server
        Deno /api/service
          Fix /service/fly/deploy
            Uses cookie to auth with Appsmith user/me api
        Sync_Mode - Full
          Download data from external APIs like FDB
            Drug (form_fdb_drug => linked to form_inventory)
            Provider (form_fdb_provider => linked to form_physician, form_sales_account.type=physician)
            Payer
          Auto-update periodically (Daily/Weekly etc.)
          Accessible over https://admin.clararx.com/services/sync/[tablename]

        SureScripts
          Import data per customer, store to .sqlite
        Log Search
          API for all NES/Fly.io servers to dump stderr
          UI/API for global log search
        AppSmith
          Customer
            how to rename app-name
            user count => licensed user count
          Database (new tab)
            Manage list of pg servers in K8s cluster
            Manage backups/snapshots per customer/env
              Restore via Customer > Env screen
          Secret
            Add button for random secret (32char hash) e.g. FLY_NES
            Create DATABASE_URL secret & push to K8s cluster
          Login page content-editor
      Fly.io
        WebSockets support / troubleshoot
        Customer cname url mapping / subdomains setup
      CI/CD
        Continuous integration
          Build once on merge into #develop
          Errors go to aws logs, alert in slack
        Code deployment
          With testing on ephemeral fly instance
          Prod deployment via fly image update
        VCS integration with gitlab
          Come up with branch/tag name

==PROJECT==

- **UX-1 UX Design**
  - **UX-2 Base UI**
    - UX-26 Base Theme (React-based)
      - UX-12 Form tabs, action buttons (inside nested tab view)
      - UX-13 Navbar UI (collapsed view)
      - UX-27 Base Icon-set, fonts, colors
    - **UX-14 Form multi-column views (web/mobile)**
      - UX-28 Form - Web 2 column
      - UX-29 Form - Mobile 1 column
      - UX-31 Compact vs. Standard view
  - **UX-3 Wizards**
    - UX-16 Initial Assessment
    - UX-17 Dispense (create ticket)
    - UX-18 Claim (create new)
    - UX-19 Company Onboarding
  - **UX-4 Module Design**
    - UX-20 Dashboard UX
    - UX-23 Ledger View
    - UX-22 Inventory Module View
  - **UX-21 Patient Card View**
      - UX-32 Patient List view
      - UX-33 Patient Snapshot view
    - UX-24 Dispense Module View
    - UX-25 Billing Module View
  - **UX-5 Editors**
    - UX-6 Security Editor
    - UX-7 Workflow Editor
    - UX-8 Label/Print-out Editor
    - UX-9 Snapshot View Editor
    - UX-34 SQL View Editor
    - UX-10 DSL Editor
    - UX-11 Migration Editor

- **DV-1 Development**
  - **DV-2 Coffee->React**
    - **DV-40 React-driven Interface**
        - DV-50 React Tabbed view for all forms
        - DV-49 React Table Grid with filters, export, action
        - DV-60 React Module Parent with nested tabbed views
        - DV-62 React Navigation-pane (left-bar, collapsible)
        - DV-48 React Modal - Fly-out view
        - DV-61 React Search box, always visible
    - **DV-41 Bootstrap override**
      - DV-42 Flex-based layout
      - DV-43 Brand-specific style/color/fonts
    - **DV-44 Updated Form views**
        - DV-45 Web 1 column (current)
        - DV-46 Web 2 column
        - DV-47 Module 1 column
  - **DV-39 Cleanup Old Code/Features**
    - DV-85 New Manage table view
    - DV-51 Remove Communications module
    - DV-53 BD repo cleanup
    - DV-52 Delete obsolete DSL forms, underlying code, rules
    - DV-54 SMS Audit / Portal setup cleanup, update
  - **DV-4 Dashboard (Workflow)**
    - **DV-70 Dashboard Blocks**
      - DV-71 Block type: Event
      - DV-73 Block type: Connector
      - DV-72 Block type: Field
      - DV-74 Table View fields
      - DV-75 Action Item components
    - DV-76 NES Dashboard Editor APIs
    - DV-69 React Editor (ReactFlow)
      - DV-79 Workflow Table
  - **DV-82 NES Updates**
    - DV-83 Stability improvements
      - DV-63 Review NES Write code - DSL-update, form save
      - DV-84 Handle DB failures
    - **DV-86 Services**
      - DV-87 DB Transaction Process
  - **DV-3 Security**
    - DV-77 Initial DSL sync
    - **DV-55 Activity-based Access**
      - DV-57 User access
      - DV-58 Group access
      - DV-59 Role access
    - DV-56 Module-level Access
    - DV-78 NES Security APIs
  - **DV-32 Ledger Entry Management**
    - DV-38 Number series (more than auto-increment int)
    - DV-33 DSL-style CSON/JSON setup per ledger module
    - DV-35 Journal entry - pre-posting
    - DV-34 Journal entry - post, void
    - DV-64 Stored procedures for posting, using DB transaction
    - DV-65 As-of-now Balance views
    - DV-36 As-of date Balances (transaction summary) view by key field
    - DV-37 Audit trail, transaction history
  - **DV-5 Inventory**
    - **DV-23 Inventory Management**
      - DV-17 Item list
      - DV-18 Category, Subcategory Lists
      - DV-19 Bin List
      - DV-31 Supplier / Vendor list
    - **DV-24 Inventory Transactions**
      - DV-21 Inventory Ledger
      - DV-20 Item receipt by lot, vendor
      - DV-22 Transfer / Movements
      - DV-25 Inventory Adjustments
      - DV-30 Barcode scanner for receipt, movement
    - **DV-26 Inventory Reports / Printing**
      - DV-27 Inventory-on-hand by Lot, Category, Bin
      - DV-28 Inventory Valuation
      - DV-29 Ledger details
  - **DV-6 Dispensing**
    - **DV-7 Billing (Insurance/Claim)**
      - DV-81 Credit/Debit Card Billing
    - **DV-8 Search (Shortcut Bar)**
      - DV-80 List all opened modules/tabs
    - DV-9 Schedule
    - DV-12 BI (Analytics / Reporting)
    - DV-10 Onboarding Tool
    - DV-11 Migration Tool
    - DV-13 Issue Tracker

- **IN-1 Infrastructure**
  - IN-2 Database
    - IN-6 RDS/AWS
    - IN-7 Caching
    - IN-8 Logging
  - IN-3 AppSmith
  - IN-4 Fly.io
  - IN-5 Pipeline

