I am going to group our dev teams as follows: (1) Foundations - core DSL and backend - JS aka NES code(2) Integrations - central Admin server to / from NES(3) Modules - Front - end React / Tsx and some backend - JS NES(4) Analytics - reports, query, labels(5) Infrastructure - Ops, Security, DB, Servers. Can you please review the full outline provided, create detailed tasks for each team, and output the list in the following CSV format: Team, Module, Submodule, Task - Type(Feature, Bug, Task), Task Description, Expected Man - Hours(Integer), Expected Duration(Integer Days), Priority(High, Medium, Low)

for this prompt, please ignore the request re: csv format output. i would like you to review the full outline provided and just list all of the modules that could be part of (1) foundations team tasklist. i don’t want details of tasks or submodules. just the name of every module that relates to foundation team in a simple text list.

Dev Modules:

Workflow Wizard
Workflow Queues
Patient Snapshot
Inventory Management
Security
Label/Print-out Editor
Schedule
Dispensing
Billing (Insurance/Claim)
Search (Shortcut Bar)
CRM (Sales/Patient Management)
Compliance (URAC/Audit)
Administration (User/Role Management)
Communication (Messaging/Notification)
Patient Portal

Patient List View
Patient Snapshot View
Patient Card View
Care Plan
Intake Forms
Order Management
Dispense Module View
Billing Module View
Inventory Module View
Dashboard UX
Workflow Wizard
Workflow Queues
Workflow Review
Reports Module View
Navigation Pane

Block Storage Integration
SMS Integration
Email Integration
Messaging Integration
Fax Integration
Radar Integration
SSO Integration
SureScripts Integration
PVerify Integration