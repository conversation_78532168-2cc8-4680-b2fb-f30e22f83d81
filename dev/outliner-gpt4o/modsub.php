<?php

include "shared.php";

$pattern = '/(?=^==\w+==)/m';
$ob = preg_split($pattern, $outline_blocks, -1, PREG_SPLIT_NO_EMPTY);
$sp = $sp_modsub . "\n" . $outline_full;
$up = 'Please filter your CSV output (Team;Module;Submodule) to the following excerpt of the above outline and ONLY show lines that relate to the text below:';

foreach($ob as $b => $block) {
    $ust = $up . "\n\n" . $block;
    echo "Creating list for block #" . ($b+1) . "...\n";
    $ms = get_gpt4($sp, $ust);
    if(is_array($ms) && isset($ms['choices'][0]['message']['content'])) {
        file_put_contents('./output/modsub.csv', trim($ms['choices'][0]['message']['content']) . "\n", FILE_APPEND);
    } else {
        print_r([$b, $ms]);
    }
}

?>