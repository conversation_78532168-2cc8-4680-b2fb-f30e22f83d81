# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.30
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha2AllocationResult(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'available_on_nodes': 'V1NodeSelector',
        'resource_handles': 'list[V1alpha2ResourceHandle]',
        'shareable': 'bool'
    }

    attribute_map = {
        'available_on_nodes': 'availableOnNodes',
        'resource_handles': 'resourceHandles',
        'shareable': 'shareable'
    }

    def __init__(self, available_on_nodes=None, resource_handles=None, shareable=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha2AllocationResult - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._available_on_nodes = None
        self._resource_handles = None
        self._shareable = None
        self.discriminator = None

        if available_on_nodes is not None:
            self.available_on_nodes = available_on_nodes
        if resource_handles is not None:
            self.resource_handles = resource_handles
        if shareable is not None:
            self.shareable = shareable

    @property
    def available_on_nodes(self):
        """Gets the available_on_nodes of this V1alpha2AllocationResult.  # noqa: E501


        :return: The available_on_nodes of this V1alpha2AllocationResult.  # noqa: E501
        :rtype: V1NodeSelector
        """
        return self._available_on_nodes

    @available_on_nodes.setter
    def available_on_nodes(self, available_on_nodes):
        """Sets the available_on_nodes of this V1alpha2AllocationResult.


        :param available_on_nodes: The available_on_nodes of this V1alpha2AllocationResult.  # noqa: E501
        :type: V1NodeSelector
        """

        self._available_on_nodes = available_on_nodes

    @property
    def resource_handles(self):
        """Gets the resource_handles of this V1alpha2AllocationResult.  # noqa: E501

        ResourceHandles contain the state associated with an allocation that should be maintained throughout the lifetime of a claim. Each ResourceHandle contains data that should be passed to a specific kubelet plugin once it lands on a node. This data is returned by the driver after a successful allocation and is opaque to Kubernetes. Driver documentation may explain to users how to interpret this data if needed.  Setting this field is optional. It has a maximum size of 32 entries. If null (or empty), it is assumed this allocation will be processed by a single kubelet plugin with no ResourceHandle data attached. The name of the kubelet plugin invoked will match the DriverName set in the ResourceClaimStatus this AllocationResult is embedded in.  # noqa: E501

        :return: The resource_handles of this V1alpha2AllocationResult.  # noqa: E501
        :rtype: list[V1alpha2ResourceHandle]
        """
        return self._resource_handles

    @resource_handles.setter
    def resource_handles(self, resource_handles):
        """Sets the resource_handles of this V1alpha2AllocationResult.

        ResourceHandles contain the state associated with an allocation that should be maintained throughout the lifetime of a claim. Each ResourceHandle contains data that should be passed to a specific kubelet plugin once it lands on a node. This data is returned by the driver after a successful allocation and is opaque to Kubernetes. Driver documentation may explain to users how to interpret this data if needed.  Setting this field is optional. It has a maximum size of 32 entries. If null (or empty), it is assumed this allocation will be processed by a single kubelet plugin with no ResourceHandle data attached. The name of the kubelet plugin invoked will match the DriverName set in the ResourceClaimStatus this AllocationResult is embedded in.  # noqa: E501

        :param resource_handles: The resource_handles of this V1alpha2AllocationResult.  # noqa: E501
        :type: list[V1alpha2ResourceHandle]
        """

        self._resource_handles = resource_handles

    @property
    def shareable(self):
        """Gets the shareable of this V1alpha2AllocationResult.  # noqa: E501

        Shareable determines whether the resource supports more than one consumer at a time.  # noqa: E501

        :return: The shareable of this V1alpha2AllocationResult.  # noqa: E501
        :rtype: bool
        """
        return self._shareable

    @shareable.setter
    def shareable(self, shareable):
        """Sets the shareable of this V1alpha2AllocationResult.

        Shareable determines whether the resource supports more than one consumer at a time.  # noqa: E501

        :param shareable: The shareable of this V1alpha2AllocationResult.  # noqa: E501
        :type: bool
        """

        self._shareable = shareable

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha2AllocationResult):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha2AllocationResult):
            return True

        return self.to_dict() != other.to_dict()
