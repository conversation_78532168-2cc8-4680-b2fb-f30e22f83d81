# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.30
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1NamespaceStatus(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'conditions': 'list[V1NamespaceCondition]',
        'phase': 'str'
    }

    attribute_map = {
        'conditions': 'conditions',
        'phase': 'phase'
    }

    def __init__(self, conditions=None, phase=None, local_vars_configuration=None):  # noqa: E501
        """V1NamespaceStatus - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._conditions = None
        self._phase = None
        self.discriminator = None

        if conditions is not None:
            self.conditions = conditions
        if phase is not None:
            self.phase = phase

    @property
    def conditions(self):
        """Gets the conditions of this V1NamespaceStatus.  # noqa: E501

        Represents the latest available observations of a namespace's current state.  # noqa: E501

        :return: The conditions of this V1NamespaceStatus.  # noqa: E501
        :rtype: list[V1NamespaceCondition]
        """
        return self._conditions

    @conditions.setter
    def conditions(self, conditions):
        """Sets the conditions of this V1NamespaceStatus.

        Represents the latest available observations of a namespace's current state.  # noqa: E501

        :param conditions: The conditions of this V1NamespaceStatus.  # noqa: E501
        :type: list[V1NamespaceCondition]
        """

        self._conditions = conditions

    @property
    def phase(self):
        """Gets the phase of this V1NamespaceStatus.  # noqa: E501

        Phase is the current lifecycle phase of the namespace. More info: https://kubernetes.io/docs/tasks/administer-cluster/namespaces/  # noqa: E501

        :return: The phase of this V1NamespaceStatus.  # noqa: E501
        :rtype: str
        """
        return self._phase

    @phase.setter
    def phase(self, phase):
        """Sets the phase of this V1NamespaceStatus.

        Phase is the current lifecycle phase of the namespace. More info: https://kubernetes.io/docs/tasks/administer-cluster/namespaces/  # noqa: E501

        :param phase: The phase of this V1NamespaceStatus.  # noqa: E501
        :type: str
        """

        self._phase = phase

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1NamespaceStatus):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1NamespaceStatus):
            return True

        return self.to_dict() != other.to_dict()
