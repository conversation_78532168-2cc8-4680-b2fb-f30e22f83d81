# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.30
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha2NamedResourcesIntSlice(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'ints': 'list[int]'
    }

    attribute_map = {
        'ints': 'ints'
    }

    def __init__(self, ints=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha2NamedResourcesIntSlice - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._ints = None
        self.discriminator = None

        self.ints = ints

    @property
    def ints(self):
        """Gets the ints of this V1alpha2NamedResourcesIntSlice.  # noqa: E501

        Ints is the slice of 64-bit integers.  # noqa: E501

        :return: The ints of this V1alpha2NamedResourcesIntSlice.  # noqa: E501
        :rtype: list[int]
        """
        return self._ints

    @ints.setter
    def ints(self, ints):
        """Sets the ints of this V1alpha2NamedResourcesIntSlice.

        Ints is the slice of 64-bit integers.  # noqa: E501

        :param ints: The ints of this V1alpha2NamedResourcesIntSlice.  # noqa: E501
        :type: list[int]
        """
        if self.local_vars_configuration.client_side_validation and ints is None:  # noqa: E501
            raise ValueError("Invalid value for `ints`, must not be `None`")  # noqa: E501

        self._ints = ints

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha2NamedResourcesIntSlice):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha2NamedResourcesIntSlice):
            return True

        return self.to_dict() != other.to_dict()
