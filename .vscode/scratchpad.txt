postgresql://clara:<EMAIL>:15466/clararx


postgresql://hb:<EMAIL>:15439/crxhb

mkcert -cert-file ./configs/.local.crt.pem -key-file ./configs/.local.key.pem "*.local.clararx.com"

git checkout reactor; git reset --soft origin/develop; git commit -m "CLI DSL, React Homebase setup";

git push --force-with-lease

https://admin.envoylabs.net/service/config/nes/?slug=echirag&env=dev&fly_nes=089100ce5323a11f1bc5829576372b82

fly deploy --build-arg NODE_ENV=development --build-target=development