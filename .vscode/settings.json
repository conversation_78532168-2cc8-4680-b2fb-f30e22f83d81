{
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit",
        "source.fixAll.stylelint": "explicit"
    },
    "editor.detectIndentation": false,
    "editor.formatOnSave": false,
    "editor.tabSize": 4,
    "eslint.format.enable": true,
    "eslint.validate": [
        "javascript",
        "javascriptreact",
        "typescript",
        "typescriptreact",
        "json",
        "jsonc",
        "yaml",
    ],
    "stylelint.validate": ["css", "less", "scss"],
    "css.validate": false,
    "less.validate": false,
    "scss.validate": false,
    "[coffeescript]": {
        "editor.tabSize": 4,
        "editor.insertSpaces": false,
    },
    "augment.chat.userGuidelines": "You must follow all of the rules in @bd/.cursor/rules/*.mdc with every single chat response.",
}