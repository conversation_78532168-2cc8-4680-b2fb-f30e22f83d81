{"permissions": {"allow": ["mcp__sse-server__execute_sql", "Bash(grep:*)", "Bash(find:*)", "Bash(ls:*)", "mcp__sse-server__list_schemas", "mcp__sse-server__list_objects", "mcp__sse-server__get_object_details", "<PERSON><PERSON>(sed:*)", "Bash(rg:*)", "Bash(/Users/<USER>/npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -l \"MedicalElectronicClaimsBuilderClass\" /Users/<USER>/Documents/EnvoyCore --type js 2 > /dev/null)", "Bash(/Users/<USER>/npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -l \"segments\\.js|require.*segments|import.*segments\" /Users/<USER>/Documents/EnvoyCore/bd/nes/src/api/billing/medical/electronic --type js)", "Bash(/Users/<USER>/npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"require.*[\"\"'']\\./segments[\"\"'']|MedicalElectronicClaimSegmentsBuilderClass\" /Users/<USER>/Documents/EnvoyCore --type js -n 2 > /dev/null)", "Bash(/Users/<USER>/npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"require.*[\"\"''].*electronic/builder[\"\"'']|require.*[\"\"'']\\./builder[\"\"'']\" /Users/<USER>/Documents/EnvoyCore --type js -B2 -A2 2 > /dev/null)", "Bash(psql:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./run_mm_test.sh:*)", "Bash(rm:*)", "Bash(Check for rental log CSON files)"]}, "enableAllProjectMcpServers": false}