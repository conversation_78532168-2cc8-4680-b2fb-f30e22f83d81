{ executeRemote } = require('./generators/utils/fx');

module.exports = (grunt, options) ->
	# export post data to db via nes pg-import-post
	# grunt pg-import-post  --file=0000-mixed-query.sql  --database-url=postgresql://yourdatabase_url (import given one file)
	# grunt pg-import-post  --file=0000-mixed-query.sql,0000-mixed-workflow.sql  --database-url=postgresql://yourdatabase_url (import given multiple file)
	# grunt pg-import-post  --sync=full --database-url=postgresql://yourdatabase_url (import files from /post folder --sync option can have: full,mixed,all,other)
	# grunt pg-import-post --database-url=postgresql://yourdatabase_url (import all those files other than full or mixed)
	# grunt pg-import-post --sync=cms --database-url=postgresql://yourdatabase_url (import files from /cms folder)

	grunt.registerTask 'pg-import-post', 'Run Post Script', ->
		done = @async()
		DSL = {}
		glob = require('glob')
		fs = require('fs')
		readline = require('readline')
		{ spawn } = require('child_process')
		# Define base paths dynamically
		basePath = options.path + '/base/'
		customerPath = options.path + '/' + (grunt.option('customer') || 'default_customer') + '/' # Ensure customer path exists

		kv = options.store
		url = '/dsl/deploy/post'
		script = ''
		database_url = grunt.option('database-url');
		skip = grunt.option('skip');
		customer = grunt.option('customer'); # Keep customer variable

		toSkip = []
		skipped = []
		syncMode = grunt.option('sync') or 'other'
		# Add 'cms' to valid sync modes
		if not ['all', 'full', 'mixed', 'other', 'fdb', 'cms', 'operations'].includes syncMode
			grunt.log.error 'Invalid --sync=' + grunt.option('sync')
			done false
			return

		file = grunt.option('file')
		started = if grunt.option('resume') then false else true

		if skip == 'post' # Should this also check for 'cms'? Maybe rename skip flag later.
			grunt.log.writeln 'force skipping post/cms import'
			done true
			return

		if not database_url
			grunt.log.writeln 'missing --database-url flag skipping.'
			done true
			return

		# Determine target directory and pattern based on syncMode
		targetDir = 'post' # Default
		filePattern = '*.sql'

		if syncMode is 'fdb'
			targetDir = 'post/fdb' 
			filePattern = '*.sql'
		else if syncMode is 'cms'
			targetDir = 'post/cms'
			filePattern = '*.sql'
		else if syncMode is 'operations'
			targetDir = 'post/operations'
			filePattern = '*.sql'
		else if ['full', 'mixed'].includes(syncMode)
			targetDir = "post/#{syncMode}"
			filePattern = '*.sql'
		else if file # Handle explicit file list
			targetDir = 'post' # Assume explicit files are in 'post' unless path is absolute
			filePattern = null # Will use the file list directly
		else # 'other/default always run operations'
			targetDir = 'post/operations'
			filePattern = '*.sql'

		base = basePath + targetDir + '/'
		cpath = customerPath + 'post/' # Customer overrides are in post directory, not subdirectories

		try
			# Handle file expansion based on mode
			if file
				# Handle specific file(s)
				if file.includes(",")
					files = file.split(',').map (f) -> 
						if f.startsWith('/') then f else basePath + targetDir + '/' + f
				else
					files = [if file.startsWith('/') then file else basePath + targetDir + '/' + file]

				# For specific files, check if they exist
				files = files.filter (f) -> 
					exists = fs.existsSync(f)
					if not exists
						grunt.log.warn "File not found: #{f}"
					exists
			else
				# Use glob pattern for non-specific file cases
				files = grunt.file.expand({filter: "isFile"}, [base + filePattern])

			# Always check for customer overrides
			cfiles = grunt.file.expand({filter: "isFile"}, [cpath + (filePattern || '*.sql')])

			# Apply customer overrides
			files = files.map((file) ->
				fileName = file.split('/').pop()
				matchingFile = cfiles.find((cfile) -> cfile.endsWith(fileName))
				return matchingFile || file
			)

			if files.length == 0
				grunt.log.warn "No SQL files found to process for syncMode '#{syncMode}'"
				done true
				return

			# Function to safely count INSERT statements in a file using streams
			countInserts = (filePath) ->
				new Promise (resolve, reject) ->
					try
						if !fs.existsSync(filePath)
							grunt.log.error "File not found: #{filePath}"
							resolve(0)
							return

						count = 0
						fileStream = fs.createReadStream(filePath, {
							encoding: 'utf8',
							highWaterMark: 1024 * 1024 # 1MB chunks
						})

						rl = readline.createInterface({
							input: fileStream,
							crlfDelay: Infinity
						})

						rl.on 'line', (line) ->
							if line.includes('INSERT INTO')
								count++

						rl.on 'close', ->
							resolve(count)

						rl.on 'error', (err) ->
							grunt.log.error "Error reading file #{filePath}: #{err.message}"
							resolve(0)

						fileStream.on 'error', (err) ->
							grunt.log.error "Error reading file #{filePath}: #{err.message}"
							resolve(0)

					catch err
						grunt.log.error "Error setting up file read #{filePath}: #{err.message}"
						resolve(0)

			# Add timing variables AND the interval constant here
			totalStartTime = Date.now()
			totalRecordsProcessed = 0
			lastProgressUpdate = Date.now()
			UPDATE_INTERVAL = 2000  # Define interval ONCE here

			# Function to update progress (Moved before executePsql)
			updateProgress = (file, recordsProcessed, startTime) ->
				now = Date.now() # Get current time again
				# Removed redundant time check
				duration = (now - startTime) / 1000
				# Prevent division by zero or NaN if duration is very small
				speed = if duration > 0 then Math.round(recordsProcessed / duration) else 0 
				fileName = file.split('/').pop()
				process.stdout.write("\r\x1b[K") # Clear current line
				process.stdout.write("Progress: #{recordsProcessed.toLocaleString()} records @ #{speed.toLocaleString()}/sec [#{duration.toFixed(1)}s]")
				lastUpdate = now # Still update the last update time here

			# Function to execute psql with real-time progress updates
			executePsql = (file, database_url, customArgs = null) ->
				new Promise (resolve, reject) ->
					try
						cleanUrl = database_url.replace(/^postgresql:\/\//, '')
						# Handle authentication info
						authPart = cleanUrl.split('@')[0]
						hostPart = cleanUrl.split('@')[1]

						args = []
						
						if hostPart
							# Has authentication
							[username, password] = authPart.split(':')
							[host, database] = hostPart.split('/')
							[hostname, port] = host.split(':')
							
							if hostname
								args.push('-h', hostname)
							if port
								args.push('-p', port)
							if username
								args.push('-U', username)
							if database
								args.push('-d', database)
						else
							# No authentication
							[host, database] = cleanUrl.split('/')
							[hostname, port] = host.split(':')
							
							if hostname
								args.push('-h', hostname)
							if port
								args.push('-p', port)
							if database
								args.push('-d', database)

						if customArgs
							args.push(...customArgs)
						else
							args.push('-f', file)
							args.push('-v', 'ON_ERROR_STOP=1')  # Stop on first error
							args.push('-v', 'VERBOSITY=verbose')  # More detailed output
							args.push('-v', "maintenance_work_mem='2GB'")  # Increase maintenance work memory
							args.push('-v', "work_mem='4GB'")  # Increase work memory per operation
							args.push('-v', "temp_buffers='1GB'")  # Increase temp buffers
							args.push('-v', "synchronous_commit=off")  # Disable synchronous commits for speed
							args.push('-a')  # Print all output
						
						env = { ...process.env }
						if password
							env.PGPASSWORD = password

						psql = spawn('psql', args, { env })
						currentRecords = 0
						startTime = Date.now()
						lastUpdate = Date.now() # Initialize lastUpdate time

						# Simplified line processor just for counting
						processLineForCount = (line) ->
							if line
								# Count INSERT operations
								insertMatch = line.match(/INSERT\s+0\s+(\d+)/) ||
									line.match(/INSERT\s+(\d+)/) ||
									line.match(/^(\d+)$/)
								if insertMatch
									count = parseInt(insertMatch[1])
									if !isNaN(count)
										currentRecords += count
								# Count UPDATE operations
								updateMatch = line.match(/UPDATE\s+(\d+)/)
								if updateMatch
									count = parseInt(updateMatch[1])
									if !isNaN(count)
										currentRecords += count
								# Count DELETE operations
								deleteMatch = line.match(/DELETE\s+(\d+)/)
								if deleteMatch
									count = parseInt(deleteMatch[1])
									if !isNaN(count)
										currentRecords += count
								# Count CREATE/ALTER operations as 1 record
								if line.match(/CREATE\s+(TABLE|VIEW|FUNCTION|PROCEDURE)/i) or line.match(/ALTER\s+(TABLE|VIEW|FUNCTION|PROCEDURE)/i)
									currentRecords += 1

						psql.stdout.on 'data', (data) ->
							lines = data.toString().split('\n')
							for line in lines
								processLineForCount(line.trim())
								# Display NOTICE messages
								if line.includes('NOTICE:') and not line.includes('already exists, skipping')
									grunt.log.writeln line.trim()
							
							now = Date.now()
							diff = now - lastUpdate
							if diff >= UPDATE_INTERVAL
								updateProgress(file, currentRecords, startTime)

						psql.stderr.on 'data', (data) ->
							lines = data.toString().split('\n')
							for line in lines
								if line.toLowerCase().includes('error')
									grunt.log.error line.trim()
								# Also display NOTICE messages from stderr
								if line.includes('NOTICE:') and not line.includes('already exists, skipping')
									grunt.log.writeln line.trim()

						psql.on 'close', (code) ->
							if code == 0
								updateProgress(file, currentRecords, startTime)
								resolve(currentRecords)
							else if code == 3
								reject(new Error("Fatal error - stopping processing"))
							else
								grunt.log.warn "Non-fatal error (code #{code}) - continuing processing"
								resolve(currentRecords)
					catch err
						reject(new Error("Failed to parse database URL: #{err.message}"))

			# --- FDB Processing Block ---
			if syncMode is 'fdb'
				# Filter specifically for FDB again if filePattern wasn't specific enough
				files = files.filter (file) -> file.split('/').pop().toLowerCase().includes('fdb')

				files.sort (a, b) ->
					aName = a.split('/').pop()
					bName = b.split('/').pop()
					return aName.localeCompare(bName)

				# Process FDB files
				grunt.log.writeln "\n--- Processing FDB Files ---"
				for file, index in files
					if(!file.endsWith('.sql'))
						continue
					if(skipped.includes file)
						continue

					if not started
						if file.split('/').pop() is grunt.option('resume')
							started = true
						else
							grunt.log.writeln "Skipping: " + file
							continue

					fileName = file.split('/').pop()
					fileStartTime = Date.now()
					
					grunt.log.writeln "\n[#{index + 1}/#{files.length}] Processing FDB file: #{fileName}"
					grunt.log.writeln "Started at: #{new Date(fileStartTime).toISOString()}"

					try
						# Enhanced psql settings for FDB imports
						args = [
							'-v', 'ON_ERROR_STOP=1'
							'-v', 'VERBOSITY=verbose'
							'-v', "maintenance_work_mem='2GB'"  # Increased for FDB
							'-v', "work_mem='4GB'"            # Increased for FDB
							'-v', "temp_buffers='1GB'"
							'-v', "synchronous_commit=off"    # Allow more worker processes
							'-f', file
						]

						res = await executePsql(file, database_url, args)

						totalRecordsProcessed += res
						duration = (Date.now() - fileStartTime) / 1000
						speed = Math.round(res / duration)
						process.stdout.write("\r\x1b[K") # Clear progress line
						grunt.log.writeln "\nCompleted #{fileName}:"
						grunt.log.writeln "  Records processed: #{res.toLocaleString()}"
						grunt.log.writeln "  Average speed: #{speed.toLocaleString()} records/second"
						grunt.log.writeln "  Duration: #{duration.toFixed(2)} seconds"
	
						toSkip.push file
					catch err
						grunt.log.error "Error processing #{fileName}: #{err.message}"
						if err.stack
							grunt.log.debug err.stack
						continue


			# --- CMS Processing Block ---
			else if syncMode is 'cms'
				grunt.log.writeln "\n--- Processing CMS Files ---"
				totalFiles = files.length
				for file, index in files
					if(!file.endsWith('.sql'))
						continue
					if(skipped.includes file)
						continue

					if not started
						if file.split('/').pop() is grunt.option('resume')
							started = true
						else
							grunt.log.writeln "Skipping: " + file
							continue

					fileName = file.split('/').pop()
					fileStartTime = Date.now()
					
					# Verify file exists before processing
					if !fs.existsSync(file)
						grunt.log.error "File not found: #{file}"
						continue

					# Count inserts for CMS files if needed (optional)
					# expectedInserts = await countInserts(file)
					
					grunt.log.writeln "\n[#{index + 1}/#{totalFiles}] Processing CMS file: #{fileName}"
					grunt.log.writeln "Started at: #{new Date(fileStartTime).toISOString()}"

					try
						# Use default psql arguments for CMS for now
						res = await executePsql(file, database_url)
						
						totalRecordsProcessed += res
						duration = (Date.now() - fileStartTime) / 1000
						speed = Math.round(res / duration)

						process.stdout.write("\r\x1b[K") # Clear progress line
						grunt.log.writeln "\nCompleted #{fileName}:"
						grunt.log.writeln "  Records processed: #{res.toLocaleString()}"
						grunt.log.writeln "  Average speed: #{speed.toLocaleString()} records/second"
						grunt.log.writeln "  Duration: #{duration.toFixed(2)} seconds"

						toSkip.push file
					catch err
						grunt.log.error "Error processing #{fileName}: #{err.message}"
						if err.stack
							grunt.log.debug err.stack
						continue


			# --- Other/Generic Processing Block ---
			# This block should only run if syncMode is NOT 'fdb' and NOT 'cms'
			else if files?.length > 0
				grunt.log.writeln "\n--- Processing Generic/Other Post Files --- in path #{base}"
				if syncMode is 'other' # Filter 'other' specifically if needed
					files = files.filter (file) ->
						/^(?!0000)\d{4}.*\.sql$/.test(file.split('/').pop())

				# Process files (Existing loop remains largely the same)
				totalFiles = files.length
				for file, index in files
					if(!file.endsWith('.sql'))
						continue
					if(skipped.includes file)
						continue

					if not started
						if file.split('/').pop() is grunt.option('resume')
							started = true
						else
							grunt.log.writeln "Skipping: " + file
							continue

					fileName = file.split('/').pop()
					fileStartTime = Date.now()
					
					# Verify file exists before processing
					if !fs.existsSync(file)
						grunt.log.error "File not found: #{file}"
						continue

					expectedInserts = await countInserts(file)
					
					grunt.log.writeln "\n[#{index + 1}/#{totalFiles}] Processing: #{fileName}"
					grunt.log.writeln "Started at: #{new Date(fileStartTime).toISOString()}"

					try
						res = await executePsql(file, database_url)
						
						totalRecordsProcessed += res
						duration = (Date.now() - fileStartTime) / 1000
						speed = Math.round(res / duration)

						process.stdout.write("\r\x1b[K") # Clear progress line
						grunt.log.writeln "\nCompleted #{fileName}:"
						grunt.log.writeln "  Records processed: #{res.toLocaleString()}"
						grunt.log.writeln "  Average speed: #{speed.toLocaleString()} records/second"
						grunt.log.writeln "  Duration: #{duration.toFixed(2)} seconds"

						# Only add to toSkip if processing was successful
						toSkip.push file
					catch err
						grunt.log.error "Error processing #{fileName}: #{err.message}"
						if err.stack
							grunt.log.debug err.stack
						if err.message.includes("Fatal error") and !grunt.option('force')
							grunt.log.error "Fatal error encountered - stopping processing"
							done false
							return
						continue

			else
				grunt.log.warn "No SQL files found to process for syncMode '#{syncMode}'"
				done true
				return

			# Log total statistics (Moved outside specific blocks to run once)
			totalDuration = (Date.now() - totalStartTime) / 1000
			# Avoid division by zero if duration is too short or no records processed
			totalSpeed = if totalDuration > 0 then Math.round(totalRecordsProcessed / totalDuration) else 0 
			grunt.log.writeln "\n--- Total Import Statistics (syncMode: #{syncMode}) ---"
			grunt.log.writeln "  Total files attempted: #{files.length}"
			grunt.log.writeln "  Total records processed: #{totalRecordsProcessed.toLocaleString()}"
			grunt.log.writeln "  Overall average speed: #{totalSpeed.toLocaleString()} records/second"
			grunt.log.writeln "  Total duration: #{totalDuration.toFixed(2)} seconds"

			done true
		catch err
			grunt.log.error "Fatal error: #{err.message}"
			if err.stack
				grunt.log.debug err.stack
			# Only log skipped files if there are any
			if toSkip.length > 0
				grunt.log.writeln "Successfully processed files:"
				for file in toSkip
					grunt.log.writeln "  #{file}"
			done false
