module.exports = (grunt, options) ->
	grunt.registerTask 'dsl-get', 'DSL Download', ->

		# initialize
		dp = require('./classes/dsl')(grunt, options)
		et = dp.etcd
		ep = et.params          # no need to reload since DSLParse already loads ETCD
		cc = ep.customer.trim() # customer code
		hs = grunt.option('host')

		arc = dp.PATH_STORE + cc + '/' + hs
		require('delete').sync(arc)

		remote = "/form/coffeedsl/?sort=code"
		for d in et.get_api(remote)
			grunt.log.writeln 'Saving: ' + d.code + ' (' + d.data.length + ' bytes)'
			grunt.file.write arc + '/' + d.code + '.cson', d.data
		grunt.log.writeln '\nCoffee DSL files downloaded to: ' + arc
		return
