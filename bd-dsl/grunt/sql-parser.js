"use strict";
const moment = require("moment");
const { Tasks } = require('./generators/task');
const fs = require('fs');
const path = require('path');

module.exports = function (grunt, options) {
    grunt.registerTask('sql-parser', 'CSV To SQL', async function (category) {
        let done = this.async(); // grunt will immediately return without this

        const taskName = grunt.option('runner');
        const importType = grunt.option('import_type')
        const database_url = grunt.option('database-url')
        const params = grunt.option('params');
        const date_today = moment().format('YYYY-MM-DD HH:mm:ss');

        // Get the category for the task if specified
        const taskCategory = category || (taskName ? Tasks.get_task_category(taskName) : null);
        const outputDir = taskCategory ? `${__dirname}/../base/post/${taskCategory}/` : `${__dirname}/../base/post/`;

        // Ensure the output directory exists
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        const tableName = grunt.option('tables') || taskName;
        const outputFile = `${outputDir}0000-full-${tableName}.sql`;

        let initOptions = {
            date_today,
            outputFile,
            params,
            store: options.store,
            importType: importType,
            database_url: database_url,
            gruntFlags: grunt.option.flags(),
            tables: tableName
        };

        let tasks;
        if (taskName) {
            tasks = [taskName];
        } else if (category) {
            tasks = Tasks.list_tasks(category.toLowerCase());
            console.log(`Running ${category} tasks:`, tasks);
        } else {
            tasks = Tasks.list_tasks();
        }

        if (tasks && tasks.length > 0) {
            for (let task of tasks) {
                try {
                    const taskRunner = Tasks.get_task(task);
                    if (taskRunner) {
                        console.log(`Running task: ${task}`);
                        await taskRunner.run(initOptions);
                    } else {
                        console.warn(`Task not found: ${task}`);
                    }
                } catch (error) {
                    console.error(`Error running task ${task}:`, error);
                }
            }
        } else {
            console.warn('No tasks found to run');
        }
        done();
    });
};