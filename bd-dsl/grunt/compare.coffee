module.exports = (grunt, options) ->
	grunt.registerTask 'compare', 'CSON DSL Compare', ->

		# initialize
		DSL = {}           # load left and right DSLs
		DIFF = {}          # final diff to be outputted
		CSON = require('cson')
		glob = require('glob')
		dp = require('./classes/dsl')(grunt, options)

		# ignore right-side values for following keys
		leftoverride = [
			'model.access.',
			'model.bundle',
			'model.collections',
			'view.comment',
		]

		# left/right comparison parameters check, diff.log setup
		cl = grunt.option('left')
		cr = grunt.option('right')
		cmp = dp.PATH_STORE + '.compare/'
		grunt.fail.fatal 'Invalid --left parameter: ' + cl if not cl
		grunt.fail.fatal 'Invalid --right parameter: ' + cr if not cr
		grunt.log.writeln 'Compare: ' + cl + ' vs. ' + cr
		dif = cmp + cl.replace('/', '.') + '-' + cr.replace('/', '.') + '/'
		require('delete').sync(dif)
		flg = dif + 'diff.log'
		dlg = []

		# DIFF FUNCTIONS
		# recursive diff
		diffdeep = (path, keys, left, right) ->
			for k,v of keys
				tv = dp.getType(v)
				continue if tv isnt 'object'
				if k is dp.D # DEFAULTS
					for f in dp.keys(left, right)
						if not (f of right)
							# No need to log missing right keys as those are inherited
							continue
						else if not (f of left)
							difflog 'Diff Key: ' + cl + '/' + path + f
							dfs = dp.mergeDSL path, f, {}, v, {} # get defaults for this node from spec
							diffdeep path + f + '.', v, dfs, right[f]
							continue
						else if left[f].model?.autoinsert? and left[f].model.autoinsert
							continue
						else if right[f].model?.autoinsert? and right[f].model.autoinsert
							continue
						else
							diffdeep path + f + '.', v, left[f], right[f]
					continue

				# all non-DEFAULTS keys will always be present in both left & right due to merging
				if v.def?
					tl = dp.getType(left[k])
					tr = dp.getType(right[k])
					jl = JSON.stringify(left[k])
					jr = JSON.stringify(right[k])
					if ((tl isnt tr) or (jl isnt jr)) and not (diffignore(path) or diffignore(path + k))
						difflog 'Diff Val: ' + path + k +
							'\n  ' + jl + '\n  ' + jr
						diffset path + k, right[k]
				else
					diffdeep path + k + '.', v, left[k], right[k]

			return

		# apply left override
		diffignore = (path) ->
			for l in leftoverride
				if path.substr(-l.length) is l
					return true
			false

		# log diff message to screen and file
		difflog = (msg) ->
			grunt.log.writeln msg
			dlg.push msg

		# save right-side diff
		diffset = (path, value, node = null) ->
			node = DIFF if not node
			p = path.split('.')
			car = p[0]
			node[car] = {} if not (car of node)
			if p.length > 1
				cdr = p.slice(1).join('.')
				diffset cdr, value, node[car]
			else
				node[car] = value
			return

		# load left/right DSLs
		[pl, dl] = dp.loadDSLFromPath(cl)
		[pr, dr] = dp.loadDSLFromPath(cr)
		for k,v of dl
			DSL[k] =
				left: v
				right: false
		for k,v of dr
			DSL[k] = left: false, right: false if not DSL[k]?
			DSL[k].right = v

		# compare DSLs
		for k in dp.keys(DSL)
			v = DSL[k]
			if not v.left
				difflog 'Missing CSON: ' + cl + '/' + k
				grunt.file.copy pr + k + '.cson', dif + k + '.cson'
				continue
			if not v.right
				difflog 'Missing CSON: ' + cr + '/' + k
				continue
			diffdeep k + '.', dp.DSL_Specification, v.left, v.right

		for k,v of DIFF
			grunt.file.write dif + k + '.cson', CSON.stringify(v)

		grunt.log.writeln '\nPlease see the diff log in:'
		grunt.log.writeln '  ' + flg
		grunt.file.write flg, dlg.join('\n')
		return
