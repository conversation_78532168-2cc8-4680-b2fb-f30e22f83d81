fs = require 'fs'

# import sync=mixed data from db nes-export-mixed
module.exports = (grunt, options) ->
	grunt.registerTask 'nes-export-mixed', 'Generate sql files for sync', ->

		grunt.log.writeln "Start process for generating Sql files for sync_mode: mixed"

		# initialize
		dp = require('./classes/dsl')(grunt, options)
		et = dp.etcd
		formFromOption = grunt.option('form')
		customer = grunt.option('customer')

		# Determine base path based on customer
		base = if customer
			options.path + '/' + customer + '/post/mixed/'
		else
			options.path + '/base/post/mixed/'

		formList = et.get_api '/sync/mixed'
		mixedForms = []

		if formFromOption
			formFromOption = formFromOption.split(',')
			for form in formFromOption
				if formList.includes(form)
					mixedForms.push(form)
				else
					grunt.log.writeln 'Invalid form: Not sync mode mixed skipping form: ', form
		else 
			mixedForms = formList

		for formName in mixedForms
			try
				apiEndpoint = '/sync/mixed/' + formName
				fileName = getFileNameToWrite(base, formName)
				# get sql queries
				response = et.get_api apiEndpoint, true

				if not response?.length
					grunt.log.warn "No data returned for form: #{formName}. Skipping..."
					continue

				# Check for empty data scenarios before attempting to write
				normalizedResponse = response.trim().toUpperCase()
				isEmpty = normalizedResponse == 'COMMIT;' or normalizedResponse == 'BEGIN; COMMIT;' or normalizedResponse == 'BEGIN;\nCOMMIT;' or normalizedResponse == 'BEGIN;\r\nCOMMIT;' or (not response.includes('INSERT INTO') and not response.includes('DELETE FROM') and not response.includes('UPDATE'))

				if isEmpty
					grunt.log.warn "Empty data returned for form: #{formName}. Skipping..."
					continue

				if not fileName.endsWith('.sql')
					fileName += '.sql'
				name = base + fileName

				# Check if this is a customer export and file exists
				if customer and fs.existsSync(name)
					existingContent = fs.readFileSync(name, 'utf8')
					# If we got here, we have data, so it's safe to overwrite
					grunt.log.writeln "Overwriting existing file for form: #{formName} with new data"

				# write file
				grunt.file.write base + fileName, response
				grunt.log.writeln "Generated file: #{base}#{fileName} \tAPI endpoint: #{apiEndpoint}"
			catch error
				grunt.log.warn "Error processing form #{formName}:"
				grunt.log.warn error.message if error.message
				grunt.log.warn "Skipping form #{formName} and continuing with others..."
				continue

		grunt.log.writeln "End process for generating Sql files for sync_mode: mixed"
		return

# (without formname/With formname) download all/formname mixed allow-sync Yes
getFileNameToWrite = (directoryPath, formName, syncMode = 'mixed') ->
	nameTemplate = '0000-{syncmode}-{formname}.sql'
	files = fs.readdirSync(directoryPath)

	if files.length == 0
		return "0000-#{syncMode}-#{formName}"

	fileToFind = syncMode + '-' + formName
	filteredName = files.filter (str) -> str.includes(fileToFind)

	if filteredName.length > 0
		return filteredName[0]

	files.sort()
	files.reverse()

	name = nameTemplate.replace('{syncmode}', syncMode)
	.replace('{formname}', formName)

	return name
