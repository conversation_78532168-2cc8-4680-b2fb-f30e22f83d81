module.exports = (grunt, options) ->
	grunt.registerTask 'compare-db', 'CSON DSL Compare', ->
		# Initialize
		done = @async()
		CSON = require('cson')
		glob = require('glob')
		fs = require('fs')
		pg = require('pg')
		database_url = grunt.option('database-url');
		base = options.path + '/base/cson/'
		all = glob.sync(base + '*.cson', options)
		results = []
		resultsFromDB = []

		if not database_url
			grunt.log.writeln 'missing --database-url flag skipping.'
			done true
			return

		# Iterate through each CSON file
		for file in all
			data = fs.readFileSync(file, 'utf8');
			csonData = CSON.parse(data);
			tableName = ""
			csonName = file.split('/').pop().replace('.cson', '')
			console.log("Form:", csonName);
			tableForms = {}
			tableSubforms = {}
			tableFormsLog = {}
			tableSubformsLog = {}

			# Iterate through each field in the CSON
			for fieldName, fieldData of csonData.fields
				if (Array.isArray(fieldData?.model?.source))
					continue;

				if (fieldData?.model?.source && typeof fieldData?.model?.source is 'object')
					continue;
				if fieldData?.model?.type && fieldData?.model?.type is 'subform' and fieldData?.model?.source?
					tableName = fieldData.model.source
					if (fieldData.model.sourcefilter)
						for sourcefilterKey, sourcefilterValue of fieldData.model.sourcefilter
							tableName = "sf_" + "form_" + csonName + "_to_" + sourcefilterKey if tableName?
							tableSubforms[tableName] = fieldName
							tableSubformsLog["l" + tableName] = fieldName

					tableName = "sf_" + "form_" + csonName + "_to_" + tableName if tableName?
					tableSubforms[tableName] = fieldName
					tableSubformsLog["l" + tableName] = fieldName
				else if fieldData?.model?.multi and fieldData?.model?.type != 'subform' and fieldData?.model?.source
					tableName = fieldData.model.source
					if (fieldData.model.sourcefilter)
						for sourcefilterKey, sourcefilterValue of fieldData.model.sourcefilter
							tableForms["gr_" + "form_" + csonName + "_" + fieldName + "_to_" + sourcefilterKey + "_id"] = sourcefilterKey
							tableFormsLog["lgr_" + "form_" + csonName + "_" + fieldName + "_to_" + sourcefilterKey + "_id"] = sourcefilterKey

					tableForms["gr_" + "form_" + csonName + "_" + fieldName + "_to_" + tableName + "_id"] = fieldName
					tableFormsLog["lgr_" + "form_" + csonName + "_" + fieldName + "_to_" + tableName + "_id"] = fieldName

			results.push({
				cson: "form_" + csonName,
				grTables: tableForms,
				sfTables: tableSubforms
				lgrTables: tableFormsLog,
				lsfTables: tableSubformsLog

			})

		pool = new pg.Pool
			connectionString: database_url

		query = """
			SELECT table_name
			FROM information_schema.tables
			WHERE table_type = 'BASE TABLE'
            AND (table_name LIKE 'form_%'
            OR table_name LIKE 'lgr_%'
            OR table_name LIKE 'gr_%'
            OR table_name LIKE 'lsf_%'
            OR table_name LIKE 'sf_%');
		"""
		pool.connect()
			.then((client) ->
				return client.query(query)
						.then((result) ->
							client.release()
							resultsFromDB = result.rows
							notMatched =
								Forms: {},
								GerundTables: {},
								SubformTables: {},
								LogGerundTables: {},
								LogSubformTables: {}

							for result_obj in results

								{cson, grTables, sfTables, lgrTables, lsfTables} = result_obj

								obj = resultsFromDB.find (o) -> o.table_name is cson

								if(!obj)
									notMatched.Forms[cson] = {}

								for grTableName, grTableValue of grTables
									obj = resultsFromDB.find (o) -> o.table_name is grTableName
									if(!obj)
										notMatched.GerundTables[grTableName] = {}

								for sfTableName, sfTableValue of sfTables
									obj = resultsFromDB.find (o) -> o.table_name is sfTableName
									if(!obj)
										notMatched.SubformTables[sfTableName] = {}

								for lgrTableName, lgrTableValue of lgrTables
									obj = resultsFromDB.find (o) -> o.table_name is lgrTableName
									if(!obj)
										notMatched.LogGerundTables[lgrTableName] = {}

								for lsfTableName, lsfTableValue of lsfTables
									obj = resultsFromDB.find (o) -> o.table_name is lsfTableName
									if(!obj)
										notMatched.LogSubformTables[lsfTableName] = {}

							console.log("--------------> not matched <---------------")
							console.log(notMatched)
							done true
						)
						.catch((err) ->
							client.release()
							console.error "Error executing query:", err
						)
			)
			.catch((err) ->
				console.error "Error connecting to database:", err
			)

		pool.on 'error', (err) ->
			console.error "Unexpected error on idle client:", err