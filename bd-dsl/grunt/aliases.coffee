module.exports =
	compile:  ['clean', 'cleanstore', 'dsl-make']
	deploy:   ['clean', 'cleanstore', 'dsl-make', 'parse-autoname', 'pg-import-pre', 'dsl-push', 'pg-import-post']
	default:  ['dsl-make']
	download: ['clean', 'dsl-get']
	man:      ['help']
	prune:    ['clean', 'cleanstore', 'dsl-make', 'dsl-prune']
	pgimportpre:    ['pg-import-pre']
	pgimportpost:    ['pg-import-post']
	generate: ['sql-parser']
	generate_cms: ['sql-parser:cms']
	generate_fdb: ['sql-parser:fdb']
	generate_managed: ['sql-parser:managed']
	nesfull: ['nes-export-full']
	nesmixed:    ['nes-export-mixed']
	makeautoname:    ['clean', 'cleanstore','dsl-make','make-autoname']
	update:  ['dsl-make', 'dsl-push']
