module.exports = (grunt, options) ->
	grunt.registerTask 'dsl-prune', 'Delete obsolete DSLs', ->

		# initialize
		dp = require('./classes/dsl')(grunt, options)
		et = dp.etcd
		ep = et.params          # no need to reload since DSLParse already loads ETCD
		cc = ep.customer.trim() # customer code
		build = dp.PATH_STORE + cc + dp.PATH_BUILD
		lDSL = grunt.file.readJSON(build + 'dsl.json')

		if grunt.option('onlyifeow')
			plist = grunt.option('onlyifeow').split(',').sort()
		else
			grunt.log.writeln 'Reading remote DSL...'
			plist = Object.keys(et.get_api '/dsl/')

		obs = {}
		for k in plist
			continue if k in ['autoinsert', 'changeset', 'user']
			obs[k] = k if not lDSL[k]?
		obs = Object.keys(obs).sort()

		cnt = 0
		totalcnt = obs.length

		for k in obs
			grunt.log.write '(' + ++cnt + '/' + totalcnt + ') '
			grunt.log.writeln 'Deleting: ' + k
			grunt.log.write '  DSL.......'
			if et.delete_api '/dsl/deploy/' + k
				grunt.log.writeln ' OK'
			else
				grunt.fail.fatal 'Cannot delete form!'

		return
