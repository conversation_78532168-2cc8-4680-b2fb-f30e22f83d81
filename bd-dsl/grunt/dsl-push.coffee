module.exports = (grunt, options) ->
	grunt.registerTask 'dsl-push', 'DSL Deploy', ->

		# initialize
		DSL = {}
		glob = require('glob')
		dp = require('./classes/dsl')(grunt, options)
		et = dp.etcd
		ep = et.params          # no need to reload since DSLParse already loads ETCD
		cc = ep.customer.trim() # customer code
		oi = false              # only-if-end-of-world
		updated = false;

		base = options.path + '/base/cson/'
		cdir = options.path + '/' + cc + '/cson/'
		kv = options.store
		missing_cson = {}

		# latest build
		build = dp.PATH_STORE + cc + dp.PATH_BUILD

		if grunt.option('full') and grunt.option('onlyifeow')
			grunt.fail.fatal 'Cannot use --full and --onlyifeow together!'

		# special tables
		special_tables = ['changeset', 'user']

		# base: list all cson files
		for f in glob.sync(base + '*.cson', options)
			k = f.replace(base, '').replace('.cson', '')
			DSL[k] =
				source: 'base'
				checksum:
					local: ''
					remote: ''

		# customer: list all cson files
		for f in glob.sync(cdir + '*.cson', options)
			k = f.replace(cdir, '').replace('.cson', '')
			DSL[k] =
				source: cc
				checksum:
					local: ''
					remote: ''

		# local: load checksum
		for k, v of DSL
			m = kv.getItemSync('meta-' + v.source + '-' + k + '.log')
			grunt.fail.fatal 'Cannot find compiled metadata for: ' + k if not (m? and m)
			DSL[k].checksum.local = m.checksum

			json = kv.getItemSync('dsl-' + v.source + '-' + k + '.json')
			if not json
				grunt.fail.fatal 'Cannot find compiled JSON for: ' + k
			DSL[k].json = json

			cson = kv.getItemSync('merged-' + k + '.cson')
			if not cson
				grunt.fail.fatal 'Cannot find merged CSON for: ' + k
			DSL[k].cson = cson

		# load server coffee table list
		for c in et.get_api '/form/coffeedsl/'
			k = c.code
			if not DSL[k]
				missing_cson[k] = 1
				continue
			if grunt.option('full')
				DSL[k].checksum.remote = ''
			else
				DSL[k].checksum.remote = c.checksum or ''
			DSL[k].remoteid = c.id

		# only push specified forms --onlyifeow=form1,form2
		if grunt.option('onlyifeow')
			oi = grunt.option('onlyifeow').split(',')
			for k in oi
				if not DSL[k]
					missing_cson[k] = 1
					continue
				DSL[k].checksum.remote = ''

		# build dependency list
		DEP = {}
		for k,v of DSL
			DEP[k] = {}
		for k,v of DSL
			for pf,_ of v.json.model.prefill
				DEP[k][pf] = 1 if DEP[pf]? and (pf isnt k)
			for _,f of v.json.fields
				if typeof(f.model.source) is 'string'
					DEP[k][f.model.source] = 1 if DEP[f.model.source]? and (f.model.source isnt k)
				for sf,_ of f.model.sourcefilter
					DEP[k][sf] = 1 if DEP[sf]? and (sf isnt k)

		# list dependency in order of most independent to most dependent
		GRF = {}
		found = true
		while found
			found = false
			for k,v of DEP
				continue if Object.keys(v).length isnt 0
				for kk, vv of DEP
					continue if k is kk
					delete DEP[kk][k] if DEP[kk][k]?
				GRF[k] = DSL[k]
				delete DEP[k]
				found = true

		# ensure there is no unresolved dependency
		if Object.keys(DEP).length isnt 0
			find_dep = (breadcrumb, next) =>
				if next
					if breadcrumb.includes(next)
						grunt.fail.fatal 'Circular dependency found in:\n\n  ' +
							breadcrumb.join(' -> ') +
							' -> ' + next +
							'\n\nPlease fix the dependency issues before deploying.'
					breadcrumb.push(next)
				for k,v of (if next then DEP[next] else DEP)
					find_dep breadcrumb, k

			find_dep([], false)
		else
			DSL = GRF

		# save dependency list
		grunt.file.write build + 'order.json', JSON.stringify(Object.keys(DSL))

		# allow overwriting remote DSL for --overwrite=form1,form2
		if grunt.option('overwrite')
			for d in grunt.option('overwrite').split(',')
				continue if not DSL[d]?
				DSL[d].checksum.remote = ''

		# count total number of DSLs to push
		totalcnt = cnt = 0
		st_changed = []
		for k, v of DSL
			continue if v.checksum.local is v.checksum.remote
			continue if oi and (k not in oi)
			totalcnt++
			if k in special_tables
				st_changed.push k

		# check server version
		ver = et.get_api '/version/'
		skip = false
		if ver and ver.version?
			[vmaj, vmin] = ver.version.split('.')
			vmaj = parseInt(vmaj)
			vmin = parseInt(vmin)
			if (vmaj > 4) or (vmaj is 4 and vmin >= 40)
				skip = true

		if st_changed.length > 0 and (not grunt.option('ipromise')) and (not skip)
			grunt.log.writeln '\nYou are trying to update the following special table(s):'
			grunt.log.writeln '  ' + st_changed.join('\n  ')
			grunt.log.writeln '\nAfter deploying the above, you must manually update nes repo dsl/fixtures/*.json\n'
			grunt.fail.fatal 'Please re-run the same command with the parameter --ipromise to confirm you will do so'


		# push dsl to remote if checksums different
		started = if grunt.option('resume') then false else true
		for k, v of DSL
			continue if v.checksum.local is v.checksum.remote
			continue if oi and (k not in oi)
			grunt.log.write '(' + ++cnt + '/' + totalcnt + ') '

			if not started
				if k is grunt.option('resume')
					started = true
				else
					grunt.log.writeln 'Skipping: ' + k + ' - ' + v.json.view.comment
					continue

			if v.remoteid?
				grunt.log.writeln 'Updating: ' + k + ' - ' + v.json.view.comment
				grunt.log.write '  JSON.......'
				table_autoname = v.json.table_autoname
				delete v.json.table_autoname
				js = {}
				js[k] = v.json
				url = '/dsl/deploy/' + k
				if grunt.option('full')
					url = url + '/full' #forces the creation of log tables for sf and gr
				if et.put_api url, js
					grunt.log.writeln ' OK'
					grunt.log.write '  Coffee.....'
					pjs =
						code: k
						comment: v.json.view.comment
						checksum: v.checksum.local
						form_label: v.json.view.label
						bundle: if v.json.model.bundle?.length then v.json.model.bundle else []
						data: v.cson
						table_autoname: table_autoname
					if et.put_api '/form/coffeedsl/' + v.remoteid, pjs
						grunt.log.writeln ' OK'
						updated = true;
					else
						grunt.fail.fatal 'Cannot update CSON on NES DB!'
				else
					grunt.fail.fatal 'Cannot push DSL!'
			else
				grunt.log.writeln 'Adding Coffee: ' + k + ' - ' + v.json.view.comment
				grunt.log.write '  JSON.......'
				table_autoname = v.json.table_autoname
				delete v.json.table_autoname
				js = {}
				js[k] = v.json
				if et.post_api '/dsl/deploy/', js
					grunt.log.writeln ' OK'
					grunt.log.write '  Coffee.....'
					pjs =
						code: k
						comment: v.json.view.comment
						checksum: v.checksum.local
						data: v.cson.trim()
						form_label: v.json.view.label
						bundle: if v.json.model.bundle?.length then v.json.model.bundle else null
						table_autoname: table_autoname
					if et.post_api '/form/coffeedsl/', pjs
						grunt.log.writeln ' OK'
						updated = true;
					else
						grunt.log.writeln ''
						grunt.fail.warn 'If this error is due to duplicate value, you should try to unset the form:\n\n  ' + ('grunt archive --unset=' + k).blue + "\n\n"
				else
					grunt.fail.fatal 'Cannot push DSL!'
		if updated? and updated == true
			grunt.log.writeln "updating dsl in nes"
			et.put_api '/dsl/reload'
			updated = true
		missing_cson = Object.keys(missing_cson)
		if missing_cson.length > 0
			missing_cson = missing_cson.sort()
			grunt.log.writeln '\nWARNING: Cannot find local DSL for: '
			for k in missing_cson
				grunt.log.writeln '  ' + k
			grunt.log.writeln '\nYou should mark these CoffeeDSL rows as ARCHIVED by running:\n'
			grunt.log.writeln "  grunt archive --set=" + missing_cson.join(",")

		return
