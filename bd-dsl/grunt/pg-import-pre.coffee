{ executeRemote } = require('./generators/utils/fx');
module.exports = (grunt, options) ->






	grunt.registerTask 'pg-import-pre', 'Run Pre Script', ->
		done = @async()
		DSL = {}
		glob = require('glob')
		base = options.path + '/base/pre/*'
		kv = options.store
		url = '/dsl/deploy/pre'
		script = ''
		database_url = grunt.option('database-url');
		skip = grunt.option('skip');
		customer = grunt.option('customer');
		cpath = options.path + '/' + customer + '/pre/*'

		preDeployment = ['form_user','form_coffeedsl','dsl']

		if skip == 'pre'
			grunt.log.writeln 'force skipping pre'
			done(true)
			return

		if not database_url
			grunt.log.writeln 'missing --database-url flag skipping.'
			done true
			return

		# Get all SQL files from base/pre
		files = grunt.file.expand({filter: "isFile"}, [base]);
		# Get all SQL files from customer/pre
		cfiles = grunt.file.expand({filter: "isFile"}, [cpath]);
		
		# Map customer overrides to base files
		files = files.map((file) -> 
			fileName = file.split('/').pop();
			matchingFile = cfiles.find((cfile) -> cfile.endsWith(fileName));
			return matchingFile || file;
		);
		try
			if files?.length > 0
				for file in files
					imprt = false
					if(!file.endsWith('.sql'))
						continue
					for precheck in preDeployment
						if file.includes '-' + precheck + '.sql'
							try
								grunt.log.writeln precheck + ' is First deployment requirement checking if it already exists'
								cmnd = 'psql ' + database_url + " -c 'select id from " + precheck + " LIMIT 1;'"
								imprt = await executeRemote(cmnd, 500000000)
							catch err
								if !err.message.includes 'ERROR:  relation "' + precheck + '" does not exist'
									throw err
								else
									grunt.log.writeln precheck + ' does not exist on provided DB'
					if(imprt)
						grunt.log.writeln 'Exists skipping to next'
						continue
					grunt.log.writeln 'Importing: ' + file + '...'
					console.time 'transactionTime'
					cmnd = 'psql ' + database_url + ' -f ' + file
					resp = await executeRemote(cmnd, 500000000)
					grunt.log.writeln 'response: ' + resp
					console.timeEnd 'transactionTime'
			else
				grunt.log.warn "No File Read"
				done true
		catch error
			grunt.log.writeln "GRUNT ERROR"
			grunt.log.write error
			done false

		files = files.filter (str) -> str.includes(preDeployment)
		grunt.log.writeln files
		done true
