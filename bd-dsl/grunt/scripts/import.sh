#/bin/bash

# REQUIREMENTS:
#   sshpass
#     via brew install sshpass

# Set the working directory to the script's directory
cd "$(dirname "$0")"

# Establish SSH tunnel in the background
ssh -L 2222:ftp.fdbhealth.com:22 $(whoami)@fdb-sftp.envoymobile.net -N &
SSH_PID=$!
echo "SSH tunnel established with PID $SSH_PID"

# Give SSH tunnel some time to establish
sleep 3

# Define FTP details
FTP_HOST="ftp://localhost:2222"
FTP_USER="srm_fdb"
FTP_PASS="pH5HOQIM"
FTP_STREAM="TEL253613D"

# Download files
sshpass -p $FTP_PASS scp -o HostKeyAlgorithms=+ssh-rsa -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -P 2222 $FTP_USER@localhost:"/$FTP_STREAM/Current/NDDF Plus DB/NDDF PLUS DB.zip" "../generators/source/fdb-db.zip"
sshpass -p $FTP_PASS scp -o HostKeyAlgorithms=+ssh-rsa -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -P 2222 $FTP_USER@localhost:"/$FTP_STREAM/Current/NDDF Plus DDL/NDDF Plus DDL.zip" "../generators/source/fdb-ddl.zip"

# Close the SSH tunnel
kill $SSH_PID
echo "SSH tunnel closed"

# Unzip files without preserving directory structure
mkdir -p "../generators/source/fdb"

unzip -j "../generators/source/fdb-db.zip" -d "../generators/source/fdb/"
unzip -j "../generators/source/fdb-ddl.zip" -d "../generators/source/fdb/"

echo "Files unzipped successfully"

cd ..

# NEXT STEPS
#   grunt generate
#   grunt pgimportpre
#   grunt pgimportpost --sync=other
#   grunt pgimportpost --sync=full
#
# CRITICAL: MAKE SURE THIS IS DONE
#   grunt nesmixed --fqdn=X --hap=Y --customer=Z
# BEFORE YOU RUN THIS
#   grunt pgimportpost --sync=mixed
