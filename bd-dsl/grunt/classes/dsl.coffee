fs = require('fs')
path = require('path')
coffee = require('coffeescript')

module.exports = (grunt, options) ->
	new DSLParse(grunt, options)


class DSLParse

	#CM:2024-09-24 - use the shared DSL Spec from bd repo
	DSL_Defaults_Path: '../../../bd/jsshared/src/coffee/' +
						'models/dsl/dsl-defaults.coffee'
	DSL_Specification: null

	D: 'DEFAULTS'
	E: false
	L: false
	X: false

	MAX_COLLECTIONS: 5
	PATH_BUILD: '/build/'
	PATH_STORE: 'store/'

	constructor: (@grunt, @options) ->
		@etcd = require('./etcd')(@grunt, @options)
		@DSL_AutoInsert = @auto_insert(@etcd.get_api_cached('autoins.json', '/dsl/autoinsert'))

		dsl_path = path.join(__dirname, @DSL_Defaults_Path)
		dsl_coffee = fs.readFileSync(dsl_path, 'utf8') +
						"\n\treturn DSL_Specification"
		spec = coffee.compile(dsl_coffee)
		@DSL_Specification = eval(spec)
		return

	auto_insert: (au) ->
		allowed =
			autoinsert: true
			label: ''
			readonly: true
			source: null
			type: ''
		r = {}
		for k,v of au.autoinsert.fields
			r[k] = {}
			for t in ['model', 'view']
				r[k][t] = {}
				for kk,vv of v[t]
					if kk is 'type' and vv in ['boolean', 'string']
						vv = 'text'
					if kk of allowed
						r[k][t][kk] = vv
		fields: r

	insertTabIfField: (section, secf, v) ->
		name = "tabif_" + section.replace(/[^\w]|\s/gi, '').toLowerCase()
		fv =
			model:
				source: ['Yes']
				default: 'Yes'
				if:
					'Yes':
						sections: [section]
						fields: secf or []
			view:
				label: 'Tab If For ' + section
				offscreen: true
				readonly: true
				validate: [{
					name: 'RemoveSectionTabs'
				}]
		v.fields[name] = fv
		return name

	insertTabToggleFields: (k, v) ->
		fields = []
		if v.model.sections_group? and v.model.sections_group.length > 0
			for gr in v.model.sections_group
				for kk,vv of gr
					if vv.tab_toggle
						fields.push(@insertTabIfField(kk, vv.fields, v))
					else if vv.sections?
						for sc in vv.sections
							for sk, sv of sc
								if sv.tab_toggle
									fields.push(@insertTabIfField(sk, sv.fields, v))
			if fields.length > 0
				v.model.sections_group.push({
					"Tab Toggles": {
						fields: fields
						hide_header: true
					}
				})
		else if v.model.sections?
			for sc in v.model.sections
				for sk, sv of sc
					if sv.tab_toggle
						fields.push(@insertTabIfField(sk, sv.fields, v))
			if fields.length > 0
				v.model.sections['Tab Toggles'] = {
						fields: fields
						hide_header: true
					}
		return

	err: (msg) ->
		@E.push msg
		return

	info: (msg) ->
		@W.push msg
		return

	getType: (val) ->
		return 'null' if val is null
		return 'boolean' if typeof val is 'boolean'
		return 'string' if typeof val is 'string'
		return 'number' if typeof val is 'number'
		return 'array' if (typeof val is 'object') and (val instanceof Array)
		return 'object' if (typeof val is 'object') and not (val instanceof Array)
		return 'undefined'

	# check if type is valid
	isValidType: (types, val) ->
		types = [types] if @getType(types) is 'string'
		@getType(val) in types

	keys: (args...) ->
		ks = {}
		for a in args
			for k,_ of a
				ks[k] = 1
		Object.keys(ks) # not sorted so as to preserve handwritten sort order

	loadCSON: (file) ->
		CSON = require('cson')
		v = CSON.load(file)
		if v.location?
			@grunt.log.warn v.toString()
			@grunt.fail.fatal 'Invalid CSON - ' + file
			false
		else
			rkws = ['tabif_'] #no field can startwith
			rkw = ['_meta', '_force', 'limit', 'group', 'select'] #no field can have this
			for kw in rkw
				if v.fields and Object.keys(v.fields).includes(kw)
					@grunt.fail.fatal file.split("cson/")[1] + " cannot have field with reserved keyword " + kw
			for kw in rkws
				if v.fields
					for field in Object.keys(v.fields)
						if field.startsWith(kw)
							@grunt.fail.fatal 'cannot have field with name starting with ' + kw + ' having ' + field + ' in ' + file.split("cson/")[1]
			v

	# load compiled cson dsl files from PATH_STORE subfolders
	loadDSLFromPath: (path) ->
		pp = false
		if path.length is 3
			if @grunt.file.exists(@PATH_STORE + path + @PATH_BUILD)
				pp = @PATH_STORE + path + @PATH_BUILD
			else
				@grunt.fail.fatal 'Cannot load DSL from: ' + path +
					'\n\nCompile DSL for any host for this customer and try again.\n' +
					'  grunt compile --host=[any_host_for_customer_' + path + ']'
		else if @grunt.file.exists(@PATH_STORE + path)
			pp = @PATH_STORE + path + '/'
		else
			@grunt.fail.fatal 'Cannot load DSL from: ' + path

		# load all cson files
		glob = require('glob')
		dsl = {}
		@grunt.log.write 'Loading DSL from: ' + pp + '...'
		for f in glob.sync(pp + '/*.cson', @options)
			@grunt.log.write '.'
			k = f.replace(pp, '').replace('.cson', '')
			p = @parse(k, @loadCSON(f))
			dsl[k] = p.json[k]
		@grunt.log.writeln ' OK'
		[pp, dsl]

	# merge & validate custom hand-written DSL with specifications & auto-insert fields
	mergeDSL: (parents, key, edsl, spec, auto) ->
		ret = {}

		# add all the keys from spec if not a default value
		if not ('def' of spec)
			for k, v of spec
				continue if k is @D
				if @getType(v) is 'object'
					if 'def' of v
						ret[k] = v.def
					else
						ret[k] = {}
				else
					ret[k] = v

		# add all the keys from auto and hand-written dsl
		for src in [auto, edsl]
			for k, v of src
				keyparent = (if parents then parents + '.' else '') + key
				keypath = keyparent + '.' + k
				# check for reserved keywords
				if k in ['def', @D]
					@err keyparent + ' cannot use the reserved keyword: ' + k
				# check for valid key names
				if not (k of ret) and # only type:objects can have dynamic key names
						not (@D of spec) and
						not ('def' of spec and 'type' of spec and ((spec.type is 'object') or ('object' in spec.type)))
					@err keyparent + ' has unknown key: ' + k
				if @getType(v) is 'object'
					ret[k] = {}
				else if @D of spec
					if k of spec[@D]
						if @isValidType(spec[@D][k].type, v)
							ret[k] = v
						else
							@err keypath + ' must be type:' + spec[@D][k].type + ' instead of type:' + @getType(v)
					else
						@err keypath + ' must be type:object instead of type:' + @getType(v)
				else
					if (k of spec) and (@getType(spec[k]) is 'object')
						if ('def' of spec[k])
							# check if type is valid
							if ('type' of spec[k]) and not @isValidType(spec[k].type, v)
								@err keypath + ' must be type:' + spec[k].type + ' instead of type:' + @getType(v)
							# check if value is within approved source list
							if ('source' of spec[k]) and not (v in spec[k].source)
								@err keypath + ' must have value:' + spec[k].source + ' instead of value:' + v
						else
							# check if type is object
							if @getType(v) isnt 'object'
								@err keypath + ' must be type:object instead of type:' + @getType(v)
					ret[k] = v

		# merge dsl sub-tree
		for k, v of ret
			if @getType(v) is 'object'
				p1 = if k of edsl then edsl[k] else {}
				if k of spec
					p2 = spec[k]
				else if @D of spec
					p2 = spec[@D]
				else
					p2 = {}
				p3 = if k of auto then auto[k] else {}
				ret[k] = @mergeDSL (if parents then parents + '.' else '') + key, k, p1, p2, p3
		ret

	merge_customer: (k, b, c, s = null) ->
		stopat = ['if', 'sourcefilter', 'subfields', 'prefill', 'sections']
		kmerge = (bb, cc, ss) =>
			ss = ss[@D] if ss and @D of ss
			if @getType(bb) isnt 'object' or @getType(cc) isnt 'object' or (ss and 'def' of ss)
				cc
			else
				m = {}
				for f,_ of bb
					sf = ss[f] or ss
					if f of cc
						sf = def:true if f in stopat # force selection of bb or cc in next step
						m[f] = kmerge(bb[f], cc[f], sf)
					else
						m[f] = bb[f]
				for f,_ of cc
					if not (f of bb)
						sf = ss[f] or ss
						m[f] = cc[f]
				m

		n = kmerge(b, c, if s is null then @DSL_Specification else s)
		[n, @parse(k, n)]

	parse: (code, json) ->
		@E = []
		@W = []
		@L = {transform: [], validate: []}
		@X = {}
		@parseDSL(code, json)
		return json: @X, err: @E, warn: @W

	parseDSL: (k, v) ->
		if (@getType(v) isnt 'object') or not ('fields' of v)
			@err k + ': ' + v.toString()
			@err k + ' does not have any fields!'
		@insertTabToggleFields(k,v)
		@X[k] = @mergeDSL '', k, v, @DSL_Specification, @DSL_AutoInsert

		# Support negative field permissions as per byoung
		# This allows a role to have read/write perms for all fields EXCEPT ones specified by
		#   -rolename in a field's model.access.read/write
		#   DO NOT add these roles to the read_all/write_all groups, otherwise it is useless
		negperm = {read: {}, write: {}}
		for fn, fv of @X[k].fields
			for acc in ['read', 'write']
				facc = []
				for nk, nr of fv.model.access[acc]
					if nr.substr(0, 1) isnt '-'
						facc.push nr
						continue
					nr = nr.substr(1)
					negperm[acc][nr] = {} if not negperm[acc][nr]?
					negperm[acc][nr][fn] = 1
				@X[k].fields[fn].model.access[acc] = facc

		for fn, fv of @X[k].fields
			# auto-add field access if not in negative permission
			for acc, nr of negperm
				for nk, nv of nr
					if not nv[fn]
						@X[k]['fields'][fn].model.access[acc].push nk

			# set appropriate view.control
			if (fv.view.control is '')
				if fv.model.source isnt null
					@X[k]['fields'][fn].view.control = 'select'
				else if fv.model.type in ['date', 'datetime', 'time']
					@X[k]['fields'][fn].view.control = 'picker'
				else
					@X[k]['fields'][fn].view.control = 'input'
			if (fv.view.control is 'radio' and @getType(fv.model.source) is 'string' and fv.model.type isnt 'subform')
				if fv.model.source.indexOf('{') < 0
					@X[k]['fields'][fn].view.control = 'select'
					@info k + '.' + fn + ' - invalid control radio for source table using select instead.'

			if (fv.model.type is 'subform')
				@X[k]['fields'][fn].view.control = if fv.model.multi then 'subform' else 'inline'

			# mark multi true if findfilter is array
			if @getType(fv.view.findfilter) is 'array'
				@X[k]['fields'][fn].view.findmulti = true

			# find existence of all sources
			if fv.model.sourceid not in ['id', 'code', 'mcr_ref']
				@err k + '.fields.' + fn + '.model.sourceid is invalid: ' + fv.model.sourceid

			# verify <field>.model.if.<selection>: fields/sections exist and are active
			if fv.model.active
				for ifk, ifv of fv.model.if
					@verifyIfSelections k + '.fields.' + fn + '.model.if.' + ifk, @X[k], fn, ifk, ifv

			if (fv.view.embed.form or fv.view.embed.query)
				@X[k]['fields'][fn].view.control = 'embedded_table'
				@X[k]['fields'][fn].model.source = null
				@X[k]['fields'][fn].model.type = 'json'
			if fv.view.control is 'file' and fv.view.class and fv.view.class.includes('media-preview')
				@X[k]['fields'][fn].view.columns = 1
			# verify that offscreen fields using if + prefill are readonly
			if (fv.model.active) and (fv.view.offscreen) and (not fv.view.readonly) and (fv.model.prefill.length > 0) and (@getType(fv.model.if) is 'object')
				@err k + '.fields.' + fn + '.view.readonly must be true since it is offscreen and uses if/prefill.'

			# setup sorting of subfields
			if fv.model.subfields? and fv.model.subfields_sort.length is 0
				fv.model.subfields_sort = for sfk, _ of fv.model.subfields
					sfk

			# list all field transformers and validators
			@queueList k + '.' + fn + '.model', 'field.model', 'transform', fv.model.transform
			@queueList k + '.' + fn + '.model', 'field.model', 'validate',  fv.model.validate
			@queueList k + '.' + fn + '.view',  'field.view',  'transform', fv.view.transform
			@queueList k + '.' + fn + '.view',  'field.view',  'validate',  fv.view.validate

		# ensure model.name fields exists IF model.name is array
		if @getType(@X[k].model.name) is 'array'
			for f in @X[k].model.name
				if not (f of @X[k].fields)
					@err k + '.model.name uses non-existent form field: ' + f

		# add many-index on auto_name if not exists
		anf = false
		for fa in @X[k].model.indexes.many
			if fa.length is 1 and fa[0] is 'auto_name'
				anf = true
				break;
		if not anf
			@X[k].model.indexes.many.push(['auto_name'])

		# autofill view.find.advanced if needed
		if (@X[k].view.find.advanced.length is 0) and (@X[k].view.find.basic.length > 0)
			@X[k].view.find.advanced = @X[k].view.find.basic

		# verify all fields in view.find exist and are active
		for ff in ['basic', 'advanced']
			node = fields: @X[k].view.find[ff], sections: []
			@verifyIfSelections k + '.view.find.' + ff, @X[k], false, false, node

		# verify all fields/sort in view.grid exist and are active
		for f in @X[k].view.grid.fields
			continue if (@getType(f) isnt 'string') or (f.indexOf('.') > -1)
			if not (f of @X[k].fields)
				@err k + '.view.grid.fields' + ' uses non-existent form field: ' + f
			else if not @X[k].fields[f].model.active
				@err k + '.view.grid.fields' + ' uses inactive form field: ' + f
		if @X[k].view.grid.label.length > @X[k].view.grid.fields.length
			@err k + '.view.grid.labels' + ' has more enteries than grid.fields'
		for f in @X[k].view.grid.sort
			continue if (@getType(f) isnt 'string') or (f.indexOf('.') > -1)
			f = f.replace(/^([-|+])*/g, "")
			if not (f of @X[k].fields)
				@err k + '.view.grid.sort' + ' uses non-existent form field: ' + f
			else if not @X[k].fields[f].model.active
				@err k + '.view.grid.sort' + ' uses inactive form field: ' + f

		# TODO Verify Schema of each object in @X[key].view.grid.menu
		# label: def: '',     type: 'string'
		# open:  def:'latest',      source: ['latest', 'new', 'list']
		# mode:  def:'edit',        source: ['read', 'edit', 'add', 'addfill']
		# source: type: 'string'
		# filter: def: {},    type: 'object'
		# auto_fk: def: true, type: 'boolean' if auto_fk is true it will use <form_name>_id as fk in source form and match it to id of current form
		#verify all hide_columns fields in view.grid exist
		gfields = []
		for fd in @X[k].view.grid.fields
			if @getType(fd) is 'object'
				gfields.push(Object.keys(fd)[0])
			else
				gfields.push(fd)
		for h in @X[k].view.grid.hide_columns
			if not (gfields.includes(h))
				@err k + '.view.grid.hide_columns uses field which is not in ' + k + '.view.grid.fields of the form '

		# list all form transformers and validators
		@queueList k + '.model', 'model', 'transform', @X[k].model.transform
		@queueList k + '.model', 'model', 'validate',  @X[k].model.validate
		@queueList k + '.view' , 'view',  'transform', @X[k].view.transform
		@queueList k + '.view' , 'view',  'validate',  @X[k].view.validate

		@setSectionsOrder k
		@verifySections k, @X[k]

		return

	queueList: (parent, modelview, type, list) ->
		keypath = parent + '.' + type
		if @getType(list) isnt 'array'
			@err keypath + ' must be type:array instead of type:' + @getType(list)
		for i in list
			if (@getType(i) is 'object')
				if 'name' of i
					@L[type].push(modelview + '.' + type + '.' + i.name)
				else
					@err keypath + ' is missing key:name'
			else
				@err keypath + ' must contain only type:object instead of type:' + @getType(i)

	setSectionsOrder: (k) ->
		pref_order = if (@getType(@X[k].model.sections_order) is 'array') then @X[k].model.sections_order else null
		@X[k].model.sections_order = []
		if @X[k].model.sections_group? and @X[k].model.sections_group.length > 0
			@X[k].model.sections = {}
			for gr in @X[k].model.sections_group
				for kk,vv of gr
					if vv.fields?
						vv.group =
							label: kk
							note: if vv.note? then vv.note else ''
							hide_header: if vv.hide_header? then vv.hide_header else false
						@X[k].model.sections[kk] = vv
						@X[k].model.sections_order.push(kk)
					else if vv.sections?
						for sc in vv.sections
							for sk,sv of sc
								sv.group =
									label: kk
									note: if vv.note? then vv.note else ''
									hide_header: if vv.hide_header? then vv.hide_header else false
								if @X[k].model.sections[sk]?
									@grunt.fail.fatal 'Cannot redefine section in ' + k + ': ' + kk + ' > ' + sk +
									'\n             Suggested solution: Rename the key, especially if using a different area.'
								@X[k].model.sections[sk] = sv
								@X[k].model.sections_order.push(sk)
		else if @X[k].model.sections?
			@X[k].model.sections_order = for kk,vv of @X[k].model.sections
				kk
		if (@getType(pref_order) is 'array' and pref_order.length > 0)
			missed_sections = @X[k].model.sections_order.filter((s) -> (not pref_order.includes(s)))
			if missed_sections.length > 0
				@err(k + ': Some Sections are missing from form: ' + k + ' -> \n\t' + missed_sections.join('\n\t'))
			else
				@X[k].model.sections_order = pref_order
		return

	showLists: ->
		# show results of parsing
		@grunt.log.write 'Transform[]:'
		for k in @uniqueSort(@L.transform)
			@grunt.log.write ' ' + k
		@grunt.log.write '\nValidate[]:'
		for k in @uniqueSort(@L.validate)
			@grunt.log.write ' ' + k
		return

	uniqueSort: (arr) ->
		obj = {}
		for v in arr
			obj[v] = 1
		ret = []
		for k,v of obj
			ret.push(k)
		ret.sort()
		ret

	validateGridOptionFields: (DSL, pform, pf, cform, fields, type, err) ->
		ssf = Object.keys(DSL[cform].fields)
		vsf = Object.keys(DSL[pform].fields[pf].model.subfields)
		invalid_fields = []
		for gf in fields
			if ssf.indexOf(gf) is -1 and vsf.indexOf(gf) is -1
				invalid_fields.push(gf)
		if invalid_fields.length isnt 0
			err.push(pform + '.fields.' + pf + '.view.grid.' + type + ' contains field names not present in embed/source: ' + cform + ' : ' + invalid_fields.join(',') + ' ')

	verifyGridOptions: (DSL, grid, pform, pf, cform, err) ->
		if grid.fields.length isnt 0
			@validateGridOptionFields(DSL, pform, pf, cform, grid.fields, 'fields', err)
		if grid.fields.length and grid.width.length
			if grid.fields.length isnt grid.width.length
				err.push(pform + '.fields.' + pf + '.view.grid.fields and view.grid.width must have same number of elements')
		if !grid.fields.length and grid.width.length
			if DSL[cform].view.grid.fields.length isnt grid.width.length
				err.push(pform + '.fields.' + pf + '.view.grid.width and form.' + cform + '.view.grid.fields must have same number of elements or define view.grid.fields in ' + pform + ' to use view.grid.width')
		if grid.tooltip.length isnt 0
			@validateGridOptionFields(DSL, pform, pf, cform, grid.tooltip, 'tooltip', err)
		if grid.copy.length isnt 0
			@validateGridOptionFields(DSL, pform, pf, cform, grid.copy, 'copy', err)

	verifySyncMode: (DSL, fname) ->
		err = []
		msngf = []
		reqf = ['allow_sync','active']
		sm = DSL[fname].model?.sync_mode
		for f in reqf
			if not Object.keys(DSL[fname].fields).includes(f)
				msngf.push(f)
		if sm is 'mixed' and msngf.length > 0
			err.push(fname + ".model.sync_mode is mixed but does not have field(s) #{msngf.join(',')}")
		err

	verifyAutoName: (DSL, fname) ->
		err = []
		if @getType(DSL[fname].model.name) is 'array' and  DSL[fname].model.name.length is 0
			err.push fname + " does not have model.name"
		err

	verifyFieldSources: (DSL, k) ->
		# run this at the end when all DSLs are loaded
		err = []

		for fn, fv of DSL[k].fields
			if @getType(fv.view.embed.form) is 'string'
				if not (fv.view.embed.form of DSL)
					err.push(k + '.fields.' + fn + '.view.embed links to invalid form: ' + fv.view.embed.form)
				else
					@verifyGridOptions(DSL, fv.view.grid, k, fn, fv.view.embed.form, err)
			else if @getType(fv.model.source) is 'string' and fv.model.source.indexOf('{') is -1
				if not (fv.model.source of DSL)
					err.push(k + '.fields.' + fn + '.model.source links to invalid form: ' + fv.model.source)
					continue
				else
					if DSL[fv.model.source].model.sync_mode is 'full' and (fv.model.sourceid isnt 'code' or !fv.model.sourceid)
						err.push(k + '.fields.' + fn + '.model.source links to full form: ' + fv.model.source + ' but has sourceid set to ' + fv.model.sourceid)
					if fv.model.sourceid isnt 'id'
						if not (fv.model.sourceid of DSL[fv.model.source].fields)
							err.push(k + '.fields.' + fn + '.model.sourceid links to invalid form field: ' + fv.model.source + '.' + fv.model.sourceid)
						else
							idxfnd = false
							for ik, iv of DSL[fv.model.source].model.indexes.many.concat(DSL[fv.model.source].model.indexes.unique)
								if iv[0] is fv.model.sourceid
									idxfnd = iv
									break
							#if not idxfnd
							#	err.push(k + '.fields.' + fn + '.model.sourceid links to unindexed form field: ' + fv.model.source + '.' + fv.model.sourceid)

				@verifyGridOptions(DSL, fv.view.grid, k, fn, fv.model.source, err)
			else if @getType(fv.model.source) is 'object' and @getType(fv.model.source_order) is 'array'
				if JSON.stringify(Object.keys(fv.model.source).sort()) != JSON.stringify(fv.model.source_order.slice(0).sort())
					err.push(k + '.fields.' + fn + '.model.source_order is invalid : ' + fv.model.source + ' source_order must contains order for all the keys of object.')
		err


	verifyIfConditions: (DSL, form) ->
		# run this at the end when all DSLs are loaded
		err = []
		warn = []
		map_f = {}
		map_s = {}
		ifed_sections = []
		renderable_fields = []
		for k, field of DSL[form].model.sections
			renderable_fields = renderable_fields.concat(field.fields)
		for k, field of DSL[form].fields
			for ifcond, ifData of field.model.if
				if ifData.fields.length
					map_f["#{k}.if.#{ifcond}"] = ifData.fields
				if ifData.sections.length
					map_s["#{k}.if.#{ifcond}"] = ifData.sections
					ifed_sections = ifed_sections.concat(ifData.sections)
		for sec, sections of map_s
			for s in sections
				if not DSL[form].model.sections_order.includes(s)
					err.push "#{form}.#{sec} Invalid if section \"#{s}\""
		for s, section of DSL[form].model.sections
			for ifk, fields of map_f
				for f in fields
					if not renderable_fields.includes(f)
						continue
					cf = ifk.split('.')[0]
					if DSL[form].model.sections[s].fields.includes(f) and not DSL[form].model.sections[s].fields.includes(cf)
						if map_s[ifk] and map_s[ifk].includes(s)
							continue
						if !ifed_sections.includes(s)
							continue
						warn.push "#{form}.#{f} is shown by if condition [#{ifk}] but section is hidden some other condition"

		for ifk, fields of map_f
			for f in fields
				if not DSL[form].fields[f]
					err.push "#{form}.#{ifk} Invalid if section \"#{f}\""
		error: err, warn: warn


	verifyIfSelections: (parents, form, fn, ifkey, node) ->
		# avoid bugs like HB-1902 caused by invalid if: conditions
		if fn and ifkey and form?.fields?[fn]?
			checkKey = (l, r) ->
				r.trim().toLowerCase() is l
			fv = form.fields[fn]
			ms = fv.model.source
			mt = @getType(ms)
			mk = ifkey.trim().toLowerCase()
			fnd = ['!', '*'].includes(mk)
			if (fv.model.type in ['decimal', 'int']) and (~mk.indexOf('>') or ~mk.indexOf('<') or ~mk.indexOf('..'))
				fnd = true
			else if mt is 'array'
				for k in ms
					if checkKey(mk, k)
						fnd = true
						break
			else if mt is 'object'
				for k,v of ms
					if checkKey(mk, k) or checkKey(mk, v)
						fnd = true
						break
			else if mt is 'null'
				# check if source:null has any condition other than '*'
			else
				fnd = true
			if (not fnd) and (not fv.view.offscreen) and (not fv.view.readonly)
				@err parents + ' uses non-existent source condition: ' + ifkey

		for f in node.fields
			if not (f of form.fields)
				@err parents + ' uses non-existent form field: ' + f
			else if not form.fields[f].model.active
				@err parents + ' uses inactive form field: ' + f
		for f in node?.readonly?.fields or []
			if not (f of form.fields)
				@err parents + ' uses non-existent form field: ' + f
			else if not form.fields[f].model.active
				@err parents + ' uses inactive form field: ' + f
		if not (form.model.sections_group? and form.model.sections_group.length > 0)
			for f in node.sections
				if not (f of form.model.sections)
					@err parents + ' uses non-existent form section: ' + f
			for f in node?.readonly?.sections or []
				if not (f of form.model.sections)
					@err parents + ' uses non-existent form section: ' + f
		return

	verifySections: (parents, form) ->
		return if @getType(form.model.sections) is 'array'
		for k, v of form.model.sections
			if @getType(v.prefill) not in ['null', 'string', 'undefined']
				@err parents + '.model.sections.\'' + k + '\'.prefill must be string'
			for f in v.fields
				if not (f of form.fields)
					@err parents + '.model.sections.\'' + k + '\'.fields uses non-existent form field: ' + f
				else if (not form.fields[f].model.active) and (form.fields[f].model.type isnt 'subform')
					@err parents + '.model.sections.\'' + k + '\'.fields uses inactive form field: ' + f
				else if v.fields.length > 1 and form.fields[f].model.type is 'subform'
					@err parents + '.model.sections.\'' + k + '\'.fields must have exactly one subform field: ' + f
		return
