"use strict";
const Etcd = require('node-etcd');

module.exports = class ETCDStore {

	constructor(grunt, options, config) {
		this.grunt = grunt;
		this.options = options;
		this.config = config;

		this.store = new Etcd(config.url, {
			auth: {
				user: config.username,
				pass: config.password,
			}
		});
	}

	async del(key, options = {}) {
		await new Promise((resolve, reject) => {
			this.store.del(key, options, (err, res) => {
				if (err)
					reject(err);
				resolve();
			});
		});
	}

	async get(key, options = {}) {
		let val;
		await new Promise((resolve, reject) => {
			this.store.get(key, options, (err, res) => {
				if (err)
					reject(err);
				val = res;
				resolve();
			});
		});

		if ('err' in val && val.err) {
			throw val.err;
		} else if (('node' in val) && ('dir' in val.node) && (val.node.dir)) {
			let kv = {};
			if ('nodes' in val.node) {
				for (const n of val.node.nodes) {
					let kn = n.key.split('/');
					if ('value' in n) {
						if (kn.length > 0)
							kv[kn[kn.length - 1]] = n.value;
						else
							kv[n.key] = n.value;
					}
				}
			}
			return kv;
		} else if (('node' in val) && ('value' in val.node)) {
			return val.node.value;
		}
	}

	async mkdir(dir, options = { recursive: true }) {
		await new Promise((resolve, reject) => {
			this.store.mkdir(dir, options, (err, res) => {
				if (err)
					reject(err);
				resolve();
			});
		});
	}

	async rmdir(dir, options = { recursive: true }) {
		await new Promise((resolve, reject) => {
			this.store.rmdir(dir, options, (err, res) => {
				if (err)
					reject(err);
				resolve();
			});
		});
	}

	async set(key, val = null, options = {}) {
		await new Promise((resolve, reject) => {
			this.store.set(key, val, options, (err, res) => {
				if (err)
					reject(err);
				resolve();
			});
		});
	};
}
