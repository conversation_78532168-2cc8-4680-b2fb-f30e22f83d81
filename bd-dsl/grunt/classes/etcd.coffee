module.exports = (grunt, options) ->
	new ETCD(grunt, options)


class ETCD
	cred: {}
	host: false
	loaded: false
	params: {}
	req: false

	sample_format: """
{
	"code": {
		"p": "stable",
		"m": "demo",
		"s": "sandbox",
		"b": "beach",
		"t": "testing",
		"d": "unstable"
	},
	"alias": {
		"stable": "prod",
		"demo": "prod",
		"sandbox": "prod",
		"beach": "prod",
		"testing": "dev",
		"unstable": "dev"
	},
	"etcd": {
		"prod": {
			"username": "...",
			"password": "...",
			"url": "https://admin.envoylabs.net/service/config/nes"
		},
		"dev": {
			"username": "...",
			"password": "...",
			"url": "https://admin.envoylabs.net/service/config/nes"
		},
		"kvp": {
			"username": "...",
			"password": "...",
			"url": "https://admin.envoylabs.net/service/config/nes"
		},
		"kvd": {
			"username": "...",
			"password": "...",
			"url": "https://admin.envoylabs.net/service/config/nes"
		}
	}
}
"""

	constructor: (@grunt, @options) ->
		# if not @grunt.file.exists(gjs = process.env['HOME'] + '/.clara/etcd.json')
		# 	@grunt.fail.fatal 'Please create a valid ' + gjs + ' file with:\n\n' + @sample_format
		# 	return false
		# @cred = @grunt.file.readJSON(gjs)
		# if not @cred?.etcd?.prod?.username?
		# 	@grunt.fail.fatal 'Invalid file format: ' + gjs + '. Must follow template:\n\n' + @sample_format
		# 	return false

		@host = @grunt.option('host')
		@params = {}
		@req = require('sync-request')
		return

	delete_api: (path, json) ->
		@load_host() if not @loaded
		url = @make_url(path)
		if not url?
			return false

		if @list_only()
			@grunt.log.write 'DUMMY DELETE ' + url + ' '
			{}
		else
			try
				res = @req('DELETE', url,
					headers:
						'Authorization': 'Basic ' + Buffer.from('admin:' + @params.nes_admin_password.trim()).toString('base64')
						'x-timezone-info': 'UTC'
					json: json
				)
				return @parse_api_body res
			catch err
				@grunt.log.writeln " ERROR!".red + "\n\n" + err.toString().red
				return false

	get_api: (path, raw = false) ->
		@load_host() if not @loaded
		url = @make_url(path)
		if not url?
			return false
		res = @req('GET', url,
			headers:
				'Authorization': 'Basic ' + Buffer.from('admin:' + @params.nes_admin_password.trim()).toString('base64')
				'x-timezone-info': 'UTC'
		)
		if raw then res.getBody().toString('utf-8') else JSON.parse(res.getBody().toString('utf-8'))

	get_api_cached: (local, remote, force = false) ->
		@load_host() if not @loaded
		local = './store/.tmp/' + local
		if @grunt.file.exists(local) and not force
			data = @grunt.file.readJSON(local)
		else
			data = @get_api(remote)
			@grunt.file.write local, JSON.stringify(data)
		data

	get_store: (hlogin, path) ->
		res = @req('GET', hlogin.url + path,
			headers:
				'Authorization': 'Basic ' + Buffer.from(hlogin.username + ':' + hlogin.password).toString('base64')
				'x-timezone-info': 'UTC'
		)
		ret = {}
		for _,v of JSON.parse(res.getBody().toString('utf-8'))
			ret[v.key] = if typeof(v.value) is 'string' then v.value.trim() else v.value
		ret

	make_url: (path) ->
		if @params.full_url?
			return @params.full_url + path
		# else if (@params.hostname_alias? and @params.nes_admin_password?)
		# 	return 'https://' + (if @params.hostname_alias.includes('.') then @params.hostname_alias else @params.hostname_alias + '.clararx.com')  + '/api' + path
		else if (@params.fqdn? and @params.nes_admin_password?)
			if (@params.fqdn.includes('localhost'))
				if @params.fqdn.includes('https://')
					return @params.fqdn + '/api' + path
			else
				return 'https://' + @params.fqdn + '/api' + path
		else
			@grunt.fail.fatal 'Cannot construct full url from given data; either etcd OR the command line. '
			return undefined

	get_env_for_host: (host) ->
		hcode = host.substr(3, 1)
		if not @cred.code?[hcode]?
			@grunt.fail.fatal 'Cannot extract code from host: ' + host
		halias = @cred.code[hcode]
		if not @cred.alias?[halias]?
			@grunt.fail.fatal 'Cannot extract alias from host: ' + host + ' with code: ' + hcode
		henv = @cred.alias[halias]
		if not @cred.etcd?[henv]?
			@grunt.fail.fatal 'Cannot extract environment from host: ' + host +
				' with code: ' + hcode + ' and alias: ' + halias
		@cred.etcd[henv]

	list_only: ->
		typeof(@grunt.option('list')) isnt 'undefined'

	load_host: ->
		hjs = './store/.tmp/host.json'

		if @grunt.option('full-url')
			@params['full_url'] = @grunt.option('full-url').trim()
			@params['nes_admin_password'] = ''
			@params['customer'] = @grunt.option('customer').trim()

		else if @grunt.option('customer') or @grunt.option('fqdn') or @grunt.option('hap')
			if not (@grunt.option('customer') and @grunt.option('fqdn') and @grunt.option('hap'))
				@grunt.fail.fatal 'Please specify the full credentials using --customer, --fqdn, and --hap parameters.\n\n' +
													'Example: grunt --customer=env --fqdn=envdap001.clararx.com --hap=[nes_admin_password]'
				return false
			@grunt.log.writeln 'Using command-line credentials.'
			@params['customer'] = @grunt.option('customer').trim()
			@params['fqdn'] = @grunt.option('fqdn').trim()
			@params['nes_admin_password'] = @grunt.option('hap')
			@grunt.file.write hjs, JSON.stringify(@params)

		else if @host
			@grunt.log.writeln 'Reading data from etcd.'
			hlogin = @get_env_for_host(@host)
			hvals = @get_store(hlogin, '/host/' + @host)
			for k,v of hvals
				@params[k] = v
			@grunt.file.write hjs, JSON.stringify(@params)

		else if @grunt.file.exists(hjs)
			@grunt.log.writeln 'Using cached credentials.'
			@params = @grunt.file.readJSON(hjs)

		else
			@grunt.fail.fatal 'Please specify the hostname using the --host=[hostname] parameter.\n\n' +
												'Example: grunt --host=envdap001'
			return false

		@grunt.log.writeln 'Using host: ' + @make_url('')
		@loaded = true
		return

	parse_api_body: (res, js = false) ->
		if (res.statusCode in [302]) or @grunt.option('force')
			b = res.body.toString('utf-8')
		else
			b = res.getBody().toString('utf-8')

		if js
			b
		else
			try
				JSON.parse(b)
			catch err
				@grunt.log.writeln 'STATUS: ' + res.statusCode
				@grunt.log.writeln b
				false

	post_api: (path, json, js = false) ->
		@load_host() if not @loaded
		url = @make_url(path)
		if not url?
			return false

		if @list_only()
			@grunt.log.write 'DUMMY POST ' + url + '  '
			{}
		else
			@post_put_req('POST', url, json, js)

	put_api: (path, json, js = false) ->
		@load_host() if not @loaded
		url = @make_url(path)
		if not url?
			return false

		if @list_only()
			@grunt.log.write 'DUMMY PUT ' + url + '	 '
			{}
		else
			@post_put_req('PUT', url, json, js)

	post_put_req: (method, url, json, js = false) ->
		rb =
			headers:
				'Authorization': 'Basic ' + Buffer.from('admin:' + @params.nes_admin_password.trim()).toString('base64')
				'x-timezone-info': 'UTC'
		if js or (method is 'POST')
			rb.followRedirects = false
		if js
			rb.headers['Content-type'] = 'application/javascript'
			rb.body = json
		else
			rb.json = json
		try
			res = @req(method, url, rb)
			return @parse_api_body res, js
		catch err
			@grunt.log.writeln " ERROR!".red + "\n\n" + err.toString().red
			return false

	store: (config) ->
		etcd_store = require('./etcd-store')
		new etcd_store(@grunt, @options, config);
