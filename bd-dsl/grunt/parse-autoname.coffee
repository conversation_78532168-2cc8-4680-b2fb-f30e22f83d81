fs = require 'fs'
cson = require('cson')

# import sync=mixed data from db nes-export-mixed
module.exports = (grunt, options) ->
	grunt.registerTask 'parse-autoname', 'Generate sql files for sync', ->

		# initialize
		DSL = {}
		glob = require('glob')
		dp = require('./classes/dsl')(grunt, options)
		et = dp.etcd
		ep = et.params          # no need to reload since DSLParse already loads ETCD
		cc = ep.customer.trim() # customer code
		oi = false              # only-if-end-of-world
		updated = false;

		base = options.path + '/base/cson/'
		cdir = options.path + '/' + cc + '/cson/'
		kv = options.store
		missing_cson = {}

		build = dp.PATH_STORE + cc + dp.PATH_BUILD

		# base: list all cson files
		for f in glob.sync(base + '*.cson', options)
			k = f.replace(base, '').replace('.cson', '')
			DSL[k] =
				source: 'base'
				checksum:
					local: ''
					remote: ''

		# customer: list all cson files
		for f in glob.sync(cdir + '*.cson', options)
			k = f.replace(cdir, '').replace('.cson', '')
			DSL[k] =
				source: cc
				checksum:
					local: ''
					remote: ''

		getSource = (f, cson, formName) ->
			f = if f.includes '_auto_name' then f.split('_auto_name')[0] else f
			field = cson.fields[f]
			if (not field?.model?.multi) and (typeof field?.model?.source is 'string') and (not (field?.model?.type? is 'subform'))
				code = if field?.model?.sourceid? then field?.model?.sourceid else 'id'
				sf = ""
				if field.model.sourcefilter?
					for key of field.model.sourcefilter
						if field.model.sourcefilter[key].static?
							sf = sf + " AND form_#{field.model.source}.#{key} = '#{field.model.sourcefilter[key].static}'"
				return [field.model.source, code, f, sf]
			else if field?.model?.multi
				return 'multi'
			else
				return []

		# local: load checksum
		for k, v of DSL
			json = kv.getItemSync('dsl-' + v.source + '-' + k + '.json')
			if not json
				grunt.fail.fatal 'Cannot find compiled JSON for: ' + k
			DSL[k].json = json

			aname = ''
			cname = DSL[k].json.model.name
			if cname
				if typeof cname is 'string' and not (cname.includes('{') or cname.includes('}'))
					aname = cname
				else if typeof cname is 'string' and (cname.includes('{') or cname.includes('}'))
					aname = cname.split('}').filter(Boolean).map((o) ->
						if not o.includes '{'
							return "#{o}"
						fld = o.split('{').filter(Boolean)
						n = if fld.length is 1 then fld[0] else fld[1]
						source = getSource n, DSL[k].json, k
						if Array.isArray(source) and source.length > 0
							n = if fld.length is 1 then "" else fld[0]
							saname = n + "{form_#{source[0]}.#{source[1]} = #{source[2]} #{source[3]}}"
							return saname
						else if source == 'multi'
							return
						else
							o = if o.includes '_auto_name' then o.split('_auto_name')[0] else o
							return "#{o}}"
					).filter(Boolean).join('')
				else if typeof cname is 'object' and Array.isArray(cname) and cname isnt null and cname.length > 0
					aname = cname.map((n)->
						source = getSource n, DSL[k].json, k
						if Array.isArray(source) and source.length > 0
							saname = "form_#{source[0]}.#{source[1]} = #{source[2]} #{source[3]}"
							return saname
						else if source == 'multi'
							return
						else
							n = if n.includes '_auto_name' then n.split('_auto_name')[0] else n
							return n
					).filter(Boolean)
					aname = aname.map((item) =>
						"{#{item}}"
					);
					aname = aname.join(' ')
				json.table_autoname = aname
			kv.setItemSync('dsl-' + v.source + '-' + k + '.json', json)
		return