fs = require 'fs'
cson = require('cson')

# import sync=mixed data from db nes-export-mixed
module.exports = (grunt, options) ->
	grunt.registerTask 'make-autoname', 'Generate sql files for sync', ->

		# initialize
		DSL = {}
		glob = require('glob')
		dp = require('./classes/dsl')(grunt, options)
		et = dp.etcd
		ep = et.params          # no need to reload since DSLParse already loads ETCD
		cc = ep.customer.trim() # customer code
		oi = false              # only-if-end-of-world
		updated = false;

		base = options.path + '/base/cson/'
		cdir = options.path + '/' + cc + '/cson/'
		kv = options.store
		missing_cson = {}

		build = dp.PATH_STORE + cc + dp.PATH_BUILD

		# base: list all cson files
		for f in glob.sync(base + '*.cson', options)
			k = f.replace(base, '').replace('.cson', '')
			DSL[k] =
				source: 'base'
				checksum:
					local: ''
					remote: ''

		# customer: list all cson files
		for f in glob.sync(cdir + '*.cson', options)
			k = f.replace(cdir, '').replace('.cson', '')
			DSL[k] =
				source: cc
				checksum:
					local: ''
					remote: ''

		# local: load checksum
		for k, v of DSL
			json = kv.getItemSync('dsl-' + v.source + '-' + k + '.json')
			if not json
				grunt.fail.fatal 'Cannot find compiled JSON for: ' + k
			DSL[k].json = json

			cson = kv.getItemSync('merged-' + k + '.cson')
			if not cson
				grunt.fail.fatal 'Cannot find merged CSON for: ' + k
			DSL[k].cson = cson
		sql = []
		noname = []
		isGr = (f, cson, formName) ->
			f = if f.includes '_auto_name' then f.split('_auto_name')[0] else f
			field = cson.fields[f]
			if not field?.model?.multi and typeof field?.model?.source is 'string' and not (field?.model?.type? is 'subform')
				code = if field?.model?.sourceid? then field?.model?.sourceid else 'id'
				return [field.model.source, code, f]
			else
				return []
		for k,v of DSL
			formName = k
			formCson = v.json
			formsql = ''
			if Array.isArray(formCson.model?.name)
				if formCson.model?.name.length > 0
					src = {}
					f = ''
					autonameW = "CONCAT_WS(' '," + formCson.model?.name.map((n)->
						source = isGr n, formCson, formName
						if source.length > 0
							src[source[0]] = {pfld: source[2], cfld: source[1]}
							return "form_#{source[0]}.auto_name"
						else
							return "pf.#{n}::text"
					).join(",") + ")"
					formsql = "UPDATE form_#{formName} AS pf SET auto_name = #{autonameW}"
					srcs = Object.keys src
					if srcs.length > 0
						formsql = "#{formsql} FROM form_#{formName} fc"
					for form in srcs
						formsql = "#{formsql} LEFT JOIN form_#{form} ON form_#{form}.#{src[form].cfld} = fc.#{src[form].pfld} "
					formsql = formsql + " WHERE "
					if srcs.length > 0
						formsql = formsql + " fc.id = pf.id AND "
					formsql = "#{formsql} pf.auto_name IS DISTINCT FROM #{autonameW};"
					sql.push(formsql)
				else
					noname.push("form_#{formName} does not have autoname")
			else if typeof formCson.model?.name == 'string'
				if !formCson.model?.name.includes("%")
					src = {}
					fform = ''
					autonameW = "CONCAT(" + formCson.model.name.split('}').filter(Boolean).map((o) ->
						if not o.includes '{'
							return "'#{o}'"
						fld = o.split('{').filter(Boolean)
						n = if fld.length is 1 then fld[0] else fld[1]
						source = isGr n, formCson, formName
						if not source.length > 0
							if fld.length is 1
								return ("pf." + o.replace('{',''))
							else
								return "'#{o.replace('{',"' || pf.")}"
						else
							src[source[0]] = {pfld: source[2], cfld: source[1]}
							s = "form_#{source[0]}.auto_name"
							if fld.length > 1
								s = "'#{fld[0]}' || #{s}"
							return s
					).join(',') + ")"
					formsql = "UPDATE form_#{formName} AS pf SET auto_name = #{autonameW}"
					srcs = Object.keys src
					if srcs.length > 0
						formsql = "#{formsql} FROM form_#{formName} fc"
					for form in srcs
						formsql = "#{formsql} LEFT JOIN form_#{form} ON form_#{form}.#{src[form].cfld} = fc.#{src[form].pfld} "
					formsql = formsql + " WHERE "
					if srcs.length > 0
						formsql = formsql + " fc.id = pf.id AND "
					formsql = "#{formsql} pf.auto_name IS DISTINCT FROM #{autonameW};"
					sql.push(formsql)
				else
					noname.push("form_#{formName} autoname has % operator")
			else
				noname.push("form_#{formName} does not have autoname")
		grunt.file.write('./autoname-fix.sql', sql.join('\n'));
		grunt.file.write('./noautoname.text', noname.join('\n'));
		return
