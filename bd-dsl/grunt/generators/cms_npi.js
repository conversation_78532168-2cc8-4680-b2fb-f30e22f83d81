"use strict";
const fs = require("fs");
const csv = require("csv-parser");
const cson = require("cson");
const { form_auto_name, safeWrite } = require("./utils/fx");
const BaseRunner = require("./base");
const puppeteer = require("puppeteer");
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const os = require('os');
const path = require('path');
const { downloadFile } = require('./utils/download');
/**
 * Represents a runner class for downloading and processing CMS NPI (National Provider Identifier) data files.
 *
 * The `ListCMSNPI` class is responsible for:
 * - Downloading monthly CMS NPI data files from the CMS website
 * - Processing the downloaded files and inserting the data into a sql file
 * - Handling the creation and management of output files for the different database tables
 *
 * This class is designed to be used as part of a larger system for managing and updating CMS NPI data.
 */
class CMSNPI extends BaseRunner {
    constructor() {
        super();
        this.url = "https://download.cms.gov/nppes/NPI_Files.html";
        this.sourceFolder = this.sourceFolder + `/cms_npi/`;
        this.BATCH_SIZE = 50000;
        this.CHUNK_SIZE = 10000;
        this.WRITE_BUFFER_SIZE = 1024 * 1024 * 16;
        this.numWorkers = Math.min(os.cpus().length, 8);
        this.workers = [];
        this.currentChunk = [];
        this.processedCount = 0;
        this.startTime = Date.now();
        this.lastProgressUpdate = Date.now();
        this.UPDATE_INTERVAL = 1000;
        this.workerStatus = new Map();
        this.pendingChunks = [];
        this.writeQueue = [];
        this.writeInProgress = false;
        this.activeWorkers = 0;
        this.maxConcurrentWorkers = this.numWorkers;
        this.sequenceCounters = {};
        this.lastMemCheck = Date.now();
        this.MEM_CHECK_INTERVAL = 30000; // Check memory every 30 seconds
        this.MAX_HEAP_SIZE = 3072; // 3GB max heap size
        this.buffers = {
            main: [],
            group: [],
            state: [],
            other: []
        };
        // Add new class properties
        this.jsonData = {};
        this.columnData = {};
        this.csonFields = [];
    }

    static taskList = {};

    static register_tasks(task) {
        let t = new task();
        this.taskList[t.constructor.name] = t;
    }

    static get_task(task) {
        return this.taskList[task] || null;
    }

    async run(init) {
        super.run(init);
        await this.processNPI("list_cms_npi", init.date_today);
    }

    async processNPI(tableName, date_today) {
        const csonFiles = {
            main: __dirname + "/../../base/cson/list_cms_npi.cson",
            other: __dirname + "/../../base/cson/list_cms_npi_other.cson",
            group: __dirname + "/../../base/cson/list_cms_npi_group.cson",
            state: __dirname + "/../../base/cson/list_cms_npi_state.cson",
        };

        // Initialize jsonData as class property
        for (const [key, path] of Object.entries(csonFiles)) {
            this.jsonData[key] = cson.parse(fs.readFileSync(path, "utf8"));
        }

        // Store csonFields as class property
        this.csonFields = Object.keys(this.jsonData.main.fields);

        // Store columnData as class property
        this.columnData = {
                    taxonomyGroup: [
                        "npi",
                        "healthcare_provider_taxonomy_group",
                        "auto_name",
                        "created_on",
                        "created_by",
                    ],
                    providerTaxonomy: [
                        "npi",
                        "healthcare_provider_taxonomy_code",
                        "provider_license_number",
                        "provider_license_number_state_code",
                        "healthcare_provider_primary_taxonomy_switch",
                        "auto_name",
                        "created_on",
                        "created_by",
                    ],
                    otherProvider: [
                        "npi",
                        "other_provider_identifier",
                        "other_provider_identifier_type_code",
                        "other_provider_identifier_state",
                        "other_provider_identifier_issuer",
                        "auto_name",
                        "created_on",
                        "created_by",
                    ],
                };

        // Define SQL output files with correct paths
        const files = {
            main: path.join(__dirname, '../../base/post', '0000-full-list_cms_npi.sql'),
            other: path.join(__dirname, '../../base/post', '0000-full-list_cms_npi_other.sql'),
            group: path.join(__dirname, '../../base/post', '0000-full-list_cms_npi_group.sql'),
            state: path.join(__dirname, '../../base/post', '0000-full-list_cms_npi_state.sql')
        };

        // Ensure the post directory exists
        const postDir = path.join(__dirname, '../../base/post');
        if (!fs.existsSync(postDir)) {
            fs.mkdirSync(postDir, { recursive: true });
        }

        const objectMap = {
            code: "NPI",
            replacement_npi: "Replacement NPI",
            employer_identification_number: "Employer Identification Number (EIN)",
            provider_organization_name: "Provider Organization Name (Legal Business Name)",
            provider_last_name: "Provider Last Name (Legal Name)",
            parent_organization_tin: "parent_organization_tin",
            parent_organization_lbn: "Parent Organization LBN",
            npi_reactivation_date: "NPI Reactivation Date",
            npi_deactivation_date: "NPI Deactivation Date",
            npi_deactivation_reason_code: "NPI Deactivation Reason Code",
            provider_business_mailing_address_postal_code_outside_us: "provider_business_mailing_address_postal_code_outside_us",
            provider_business_practice_loc_addr_postal_code_outside_us: "Provider Business Practice Location Address Country Code (If outside U.S.)",
            authorized_official_title_or_position: "Authorized Official Title Or Position",
        };

        const processFile = async (file) => {
            return new Promise(async (resolve) => {
                const streamOptions = {
                    flags: 'w',
                    highWaterMark: this.WRITE_BUFFER_SIZE
                };

                const streams = {};
                for (const [key, path] of Object.entries(files)) {
                    streams[key] = fs.createWriteStream(path, streamOptions);
                    streams[key].on('error', (err) => {
                        console.error(`Error writing to ${path}:`, err);
                    });
                }

                // Write initial cleanup and optimization transactions
                await Promise.all(Object.entries(files).map(([key, _]) => 
                    safeWrite(streams[key], 
                        `BEGIN;
SET session_replication_role = replica;
-- Temporarily disable some PostgreSQL features for faster bulk load
SET maintenance_work_mem = '2GB';
SET synchronous_commit = off;
SET work_mem = '2GB';
SET temp_buffers = '1GB';

TRUNCATE TABLE form_list_cms_npi${key !== "main" ? `_${key}` : ""};
COMMIT;\n`
                    )
                ));

                const zip = this.getZipStream(file);
                const entry = await this.getFileEntry(zip);
                const csvStream = await zip.stream(entry.name);

                // Process in chunks using worker threads
                this.workers = Array(this.numWorkers).fill(null).map(() => 
                    new Worker(`${__dirname}/worker.js`, {
                        workerData: {
                            date_today,
                            jsonData: this.jsonData,
                            columnData: this.columnData,
                            csonFields: this.csonFields,
                            objectMap
                        }
                    })
                );

                // Set up worker message handling
                this.workers.forEach(worker => {
                    worker.on('message', result => this.handleWorkerResult(result, streams));
                    worker.on('error', error => console.error('Worker error:', error));
                });

                csvStream.pipe(csv())
                    .on('data', (row) => {
                        this.currentChunk.push(row);
                        if (this.currentChunk.length >= this.CHUNK_SIZE) { 
                            this.processChunk();
                        }
                    })
                    .on('end', async () => {
                        // Process remaining records
                        if (this.currentChunk.length > 0) {
                            this.processChunk();
                        }

                        // Wait for all workers to finish
                        await Promise.all(this.workers.map(worker => worker.terminate()));

                        // Wait for all writes to complete
                        while (this.writeQueue.length > 0 || this.writeInProgress) {
                            await new Promise(resolve => setTimeout(resolve, 100));
                        }

                        console.log('\nWriting final sequence updates...');
                        
                        // Write final sequence updates and role resets
                        for (const [key, stream] of Object.entries(streams)) {
                            const tableName = key === 'state' ? 'form_list_cms_npi_state' :
                                            key === 'group' ? 'form_list_cms_npi_group' :
                                            key === 'other' ? 'form_list_cms_npi_other' : 'form_list_cms_npi';

                            const nextSeq = (this.sequenceCounters[key] || 0) + 1;
                            console.log(`Writing sequence update for ${tableName}: ${nextSeq}`);

                            const finalSQL = `\nBEGIN;
ALTER SEQUENCE "${tableName}_id_seq" RESTART WITH ${nextSeq};
SET session_replication_role = DEFAULT;
COMMIT;\n`;

                            await safeWrite(stream, finalSQL);
                            // Force flush
                            await new Promise(resolve => stream.write('', 'utf8', resolve));
                        }

                        console.log('Closing streams...');
                        // Close streams with explicit flush
                        await Promise.all(Object.entries(streams).map(([key, stream]) => 
                            new Promise((resolve, reject) => {
                                stream.end('', () => {
                                    console.log(`Stream ${key} closed`);
                                    resolve();
                                });
                            })
                        ));

                        const endTime = Date.now();
                        const duration = (endTime - this.startTime) / 1000;
                        console.log(`Processed ${this.processedCount} records in ${duration} seconds`);
                        console.log(`Average speed: ${Math.round(this.processedCount / duration)} records/second`);

                        resolve();
                });
            });
        };

        let promises = [];
        if (!fs.existsSync(this.sourceFolder)) {
            fs.mkdirSync(this.sourceFolder, { recursive: true });
            console.log(`Directory created: ${this.sourceFolder}`);
        } else {
            console.log(`Directory already exists: ${this.sourceFolder}`);
        }

        // Check for existing downloads first
        const existingFiles = fs.readdirSync(this.sourceFolder)
            .filter(file => file.endsWith('.zip') && file.includes('NPPES_Data_Dissemination'))
            .map(file => ({
                path: path.join(this.sourceFolder, file),
                name: file,
                // Get file stats for sorting by modification time
                stats: fs.statSync(path.join(this.sourceFolder, file))
            }))
            // Sort by modification time, newest first
            .sort((a, b) => b.stats.mtime.getTime() - a.stats.mtime.getTime());

        let filesToProcess = [];
        if (existingFiles.length > 0) {
            // Only process the most recent file
            const mostRecentFile = existingFiles[0];
            console.log(`Using most recent file: ${mostRecentFile.name} (modified ${mostRecentFile.stats.mtime})`);
            filesToProcess = [mostRecentFile.path];
        } else {
            console.log('No existing downloads found, downloading new files...');
            const downloadedFiles = await this.downloadCMSNPIFiles(this.url, false);
            // Only use the most recent downloaded file
            filesToProcess = [downloadedFiles[downloadedFiles.length - 1]];
        }

        // Add tracking sets for duplicate detection
        this.processedNPIs = {
            main: new Set(),
            group: new Set(),
            state: new Set(),
            other: new Set()
        };

        console.log('Processing files...');
        for (let file of filesToProcess) {
            promises.push(processFile(file));
        }
        await Promise.all(promises);
    }

    async downloadCMSNPIFiles(url = this.url, type) {
        console.log('Downloading CMS NPI Files...');
        let browser = await puppeteer.launch({
            headless: true,
            defaultViewport: null,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        let page = await browser.newPage();
        await page.goto(url, {
            waitUntil: "domcontentloaded",
        });
        let { links } = await page.evaluate(() => {
            let links = [];
            let elem = document.querySelector(".mainbox");
            let files = elem.querySelectorAll('a');
            for (let file of files) {
                if (file.href.includes('NPPES_Data_Dissemination_') && file.href.includes('.zip'))
                    links.push(file.href);
            }
            return { links };
        });
        let downloadedFiles = [];
        links = links.filter((link) => !link.toLowerCase().includes('weekly'));
        for (let link of links) {
            let fileName = link.split('/').pop();
            console.log('Downloading file...');
            let downloadedFile = await this.downloadFile(link, fileName, this.sourceFolder);
            downloadedFiles.push(downloadedFile);
        }
        await browser.close();
        return downloadedFiles
    };

    async getFileEntry(zip) {
        const entries = Object.values(await zip.entries());
        return entries.find(e => !e.isDirectory && e.name.includes('npidata') && e.name.includes("pfile") && e.name.endsWith(".csv")) || null;
    }

    async processChunk() {
        if (this.currentChunk.length > 0) {
            this.pendingChunks.push([...this.currentChunk]);
            this.currentChunk = [];
        }

        // Check memory usage and apply backpressure if needed
        const now = Date.now();
        const memoryUsage = process.memoryUsage();
        const heapUsedGB = Math.round(memoryUsage.heapUsed / 1024 / 1024 / 1024 * 100) / 100;

        if (now - this.lastMemCheck > this.MEM_CHECK_INTERVAL) {
            if (heapUsedGB > 2.5) { // 2.5GB threshold
                console.log('\nHigh memory usage detected, pausing for cleanup...');
                await new Promise(resolve => setTimeout(resolve, 5000));
                this.currentChunk = [];
            }
            this.lastMemCheck = now;
        }

        // Apply backpressure based on pending chunks and memory usage
        const maxPendingChunks = Math.min(
            this.maxConcurrentWorkers * 2,
            Math.max(2, Math.floor((3 - heapUsedGB) * 4))
        );
        
        if (this.pendingChunks.length > maxPendingChunks) {
            await new Promise(resolve => setTimeout(resolve, 100));
            return;
        }

        // Start multiple workers if we have capacity
        const availableWorkers = this.maxConcurrentWorkers - this.activeWorkers;
        const workersToStart = Math.min(availableWorkers, this.pendingChunks.length);
        
        if (workersToStart > 0) {
            const availableWorkersList = this.workers.filter(w => !this.workerStatus.get(w.threadId));
            for (let i = 0; i < workersToStart && i < availableWorkersList.length; i++) {
                const worker = availableWorkersList[i];
                const chunk = this.pendingChunks.shift();
                if (!chunk) break;

                this.workerStatus.set(worker.threadId, true);
                this.activeWorkers++;
                
                worker.postMessage({
                    chunk,
                    date_today: new Date().toISOString().split('T')[0]
                });

                this.processedCount += chunk.length;
            }
            this.updateProgress();
        }
    }

    handleWorkerResult(result, streams) {
        const workerId = result.workerId;
        this.workerStatus.set(workerId, false);
        this.activeWorkers--;

        const { buffers } = result;
        Object.entries(buffers).forEach(([key, values]) => {
            if (values && values.length > 0) {
                const streamKey = this.getStreamKey(key);
                if (!streamKey || !streams[streamKey]) {
                    console.error(`Invalid stream key: ${key} -> ${streamKey}`);
                    return;
                }

                // Process all values at once to maintain sequence integrity
                const rewrittenValues = this.processValues(values, streamKey);
                if (rewrittenValues.length > 0) {
                    // Accumulate values in buffer before writing
                    if (!this.buffers[streamKey]) {
                        this.buffers[streamKey] = [];
                    }
                    this.buffers[streamKey].push(...rewrittenValues);

                    // When we have enough records, queue them for writing
                    while (this.buffers[streamKey].length >= this.BATCH_SIZE) {
                        const batch = this.buffers[streamKey].splice(0, this.BATCH_SIZE);
                        this.writeQueue.push({
                            streamKey,
                            values: batch,
                            stream: streams[streamKey]
                        });
                    }
                }
                
                // Clear arrays to help with memory
                values.length = 0;
                rewrittenValues.length = 0;
            }
        });

        if (!this.writeInProgress) {
            this.processWriteQueue();
        }

        setImmediate(() => this.processChunk());
    }

    getStreamKey(key) {
        const streamKeyMap = {
            main: 'main',
            taxonomyGroup: 'group',
            providerTaxonomy: 'state',
            otherProvider: 'other'
        };
        return streamKeyMap[key];
    }

    processValues(values, streamKey) {
        // Initialize sequence counter if needed
        if (!this.sequenceCounters[streamKey]) {
            this.sequenceCounters[streamKey] = 0;
        }

        const result = values.map(value => {
            const npiMatch = value.match(/'(\d+)'/);
            if (!npiMatch) return null;
            
            this.sequenceCounters[streamKey]++;
            return value.replace(/^\(\d+,/, `(${this.sequenceCounters[streamKey] - 1},`);
        }).filter(Boolean);

        // Help free memory
        values.length = 0;
        return result;
    }

    queueWrite(streamKey, values, stream) {
        if (!this.buffers[streamKey]) {
            this.buffers[streamKey] = [];
        }
        this.buffers[streamKey].push(...values);

        // Process complete batches
        while (this.buffers[streamKey].length >= this.BATCH_SIZE) {
            const batch = this.buffers[streamKey].splice(0, this.BATCH_SIZE);
            this.writeQueue.push({
                streamKey,
                values: batch,
                stream
            });
        }

        if (!this.writeInProgress) {
            this.processWriteQueue();
        }

        // Clear input array
        values.length = 0;
    }

    async processWriteQueue() {
        if (this.writeInProgress || this.writeQueue.length === 0) return;
        
        this.writeInProgress = true;
        const { streamKey, values, stream } = this.writeQueue.shift();
        
        try {
            const tableName = streamKey === 'state' ? 'form_list_cms_npi_state' :
                            streamKey === 'group' ? 'form_list_cms_npi_group' :
                            streamKey === 'other' ? 'form_list_cms_npi_other' : 'form_list_cms_npi';

            let columns;
            if (streamKey === 'state') {
                columns = ['id', ...this.columnData.providerTaxonomy].join(', ');
            } else if (streamKey === 'group') {
                columns = ['id', ...this.columnData.taxonomyGroup].join(', ');
            } else if (streamKey === 'other') {
                columns = ['id', ...this.columnData.otherProvider].join(', ');
            } else {
                columns = 'id, ' + this.csonFields.join(', ') + ', auto_name, created_on, created_by';
            }

            // Only write if we have a full batch (or it's the end of processing)
            if (values.length === this.BATCH_SIZE || (values.length > 0 && this.writeQueue.length === 0)) {
                const startTime = Date.now();
                
                // Add timing comments to help track import progress
                const sql = [
                    '-- Batch start: ' + new Date().toISOString(),
                    `-- Table: ${tableName}, Records: ${values.length}`,
                    'BEGIN;',
                    `INSERT INTO ${tableName} (${columns}) VALUES`,
                    values.join(',\n'),
                    ';',
                    'COMMIT;\n',
                    '-- Batch end: ' + new Date().toISOString(),
                    `-- Duration: ${((Date.now() - startTime) / 1000).toFixed(2)}s\n\n`
                ].join('\n');

                await safeWrite(stream, sql);
                
                // Log progress for large tables
                if (tableName === 'form_list_cms_npi') {
                    const progress = ((this.sequenceCounters[streamKey] || 0) / 10000000 * 100).toFixed(1);
                    console.log(`\nMain table progress: ~${progress}% (${this.sequenceCounters[streamKey].toLocaleString()} records)`);
                }
            } else {
                // Put back in queue if not a full batch (unless we're at the end)
                this.writeQueue.push({ streamKey, values, stream });
            }

            // Clear memory
            values.length = 0;
        } catch (error) {
            console.error(`\nError writing to stream ${streamKey}:`, error);
            try {
                await safeWrite(stream, 'ROLLBACK;\n\n');
            } catch (rollbackError) {
                console.error(`\nError rolling back transaction:`, rollbackError);
            }
        }

        this.writeInProgress = false;
        setImmediate(() => this.processWriteQueue());
    }

    updateProgress() {
        const now = Date.now();
        if (now - this.lastProgressUpdate >= this.UPDATE_INTERVAL) {
            const duration = (now - this.startTime) / 1000;
            const speed = Math.round(this.processedCount / duration);
            const memoryUsage = process.memoryUsage();
            
            // Format memory usage in MB
            const formatMB = (bytes) => Math.round(bytes / 1024 / 1024);
            const memoryStr = `[Heap: ${formatMB(memoryUsage.heapUsed)}/${formatMB(memoryUsage.heapTotal)}MB]`;
            
            // Enhanced status line
            const workerStates = Array.from(this.workerStatus.entries())
                .map(([id, active]) => active ? '●' : '○')
                .join('');
            
            const queueInfo = `[Chunks: ${this.pendingChunks.length}|Writes: ${this.writeQueue.length}]`;
            
            const statusLine = `[${this.activeWorkers}/${this.maxConcurrentWorkers} ${workerStates}]${memoryStr}${queueInfo} Processed ${this.processedCount.toLocaleString()} records at ${speed.toLocaleString()} records/second`;
            
            process.stdout.write(`\r\x1b[K${statusLine}`);
            this.lastProgressUpdate = now;
        }
    }
}

module.exports = { CMSNPI };