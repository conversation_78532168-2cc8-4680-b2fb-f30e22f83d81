"use strict";
const BaseRunner = require('./base');
const moment = require('moment')
const { form_auto_name } = require('./utils/fx');

class PECOSNPI extends BaseRunner {
    constructor() {
        super();
    }

    static taskList = {};
    static register_tasks(task) {
        let t = new task();
        this.taskList[t.letructor.name] = t;
    }
    static get_task(task) {
        if (Object.keys(this.taskList).includes(task))
            return this.taskList[task];
    }

    async run(init) {
        super.run(init);
        try {
            await this.getNPIDataFromAPI();
        } catch (error) {
            throw new Error(error)
        }
    };

    async getNPIDataFromAPI(){
        console.log('Process Started...');
        const tableName = 'list_pecos_npi'
        const data_uuid = '2457ea29-fc82-48b0-86ec-3b0755de7515'
        let offset = 0;
        const size = 5000;
        const url = `https://data.cms.gov/data-api/v1/dataset/${data_uuid}/data`;
        let flag = true;
        let id = 0;
        let writeStream = this.initFullFileWriteStream(tableName);
        console.log("Getting data and inserting rows...")
        while (flag) {
            flag = false;
            try {
                console.log(`Row #: ${offset}...`)
                let response = await this.getDataFromAPI(url + `?offset=${offset}&size=${size}`);
                let transformed_data = this.transformData(response);
                id = await this.generateSql(transformed_data, 'list_pecos_npi', offset, writeStream);
                if (response.length > size) {
                    throw new Error('Response size is greater than the given limit..');
                }
                if (response.length === size) {
                    flag = true;
                    offset += size;
                }
            } catch (error) {
                throw new Error('Error during process:', error)
            }
        }
        console.log("Finalizing...");
        await this.finalizeFullFileWriteStream(writeStream, tableName, (id + 1));
    };

    async generateSql(objArray, tableName, id, writeStream) {
        const date_today = moment().format('YYYY-MM-DD HH:mm:ss');
        const formCson = this.readCson(tableName);
        let columns = ['id'].concat(Object.keys(objArray[0]));
        let dataArray = [];
        let sql = `INSERT INTO form_${tableName} (${columns.join(',')},created_on,created_by,auto_name) VALUES\n`;
        for (let index in objArray) {
            id++;
            let val = objArray[index];
            const values = columns.map(col => {
                if (col === 'id') {
                    return id;
                } else {
                    const value = val[col];
                    if (typeof value === 'string') {
                        return `'${value.replace(/'/g, "''")}'`;
                    } else if (value === null || value === undefined) {
                        return 'NULL';
                    } else {
                        return value;
                    }
                }
            });
            let auto_name = form_auto_name(formCson, val).replace(/'/g, "''");
            dataArray.push(`(${values.join(',')},'${date_today}',1, '${auto_name}')\n`);

            if ((id % 10000) === 0) {
                sql = `${sql} ${dataArray.join(',')};\n`;
                dataArray = [];
                writeStream.write(`\n${sql}\n`);
                sql = `INSERT INTO form_${tableName} (${columns.join(',')},created_on,created_by,auto_name) VALUES`;
            }
        }
        if (dataArray.length > 0) {
            sql = `${sql} ${dataArray.join(',')};\n`;
            writeStream.write(`${sql}\n`);
        }
        return id;
    }

    transformData(data) {
        return data.map(item => {
            return Object.keys(item).reduce((acc, key) => {
                acc[key.toLowerCase()] = item[key];
                return acc;
            }, {});
        });
    }
}
module.exports = { PECOSNPI };