"use strict";
const BaseRunner = require('./base');
const puppeteer = require('puppeteer');
const moment = require("moment");
const csv = require('csv-parser');
const cson = require('cson');
const fs = require('fs');

class CMSPrices extends BaseRunner {
    constructor() {
        super();
        this.sourceFolder = this.sourceFolder + "/cms_prices/";
        this.url = 'https://www.cms.gov/medicare/payment/part-b-drugs/asp-pricing-files';
    }

    async run(init) {
        super.run(init);
        let tasks = init.params?.split(",");
        let promises = [];
        if (tasks && tasks.length > 0) {
            for (let name of tasks) {
                let task = global.Tasks.get_task(name);
                if (task) {
                    promises.push(task.init(name, false));
                }
            }
        } else {
            await this.runAll();
        }
        await Promise.all(promises);
        console.log("All tasks completed");
    }

    async runAll() {
        const cmsTasks = global.Tasks.list_tasks('cms');
        for (let taskName of cmsTasks) {
            let task = global.Tasks.get_task(taskName);
            if (task && task instanceof CMSPrices) {
                await task.init(taskName, false);
            }
        }
    }

    async downloadASPPriceFiles(url = this.url) {
        console.log('Downloading ASP price files from CMS...');
        const browser = await puppeteer.launch({
            headless: true,
            defaultViewport: null,
            args: ['--disable-setuid-sandbox', '--no-sandbox']
        });
        const page = await browser.newPage();
        await page.goto(url, {
            waitUntil: "networkidle0",
        });

        const { links } = await page.evaluate(() => {
            let links = [];
            // Find all links on the page
            const allLinks = Array.from(document.querySelectorAll('a'));
            // Filter for 2024 ASP and crosswalk files
            const relevantLinks = allLinks.filter(link => {
                const href = link.href.toLowerCase();
                console.log(`${href} ${href.includes('2024') && (href.includes('asp-pricing-file') || href.includes('ndc-hcpcs-crosswalk')) && href.endsWith('.zip')}`)
                return (href.includes('2024') &&
                    (href.includes('asp-pricing-file') ||
                        href.includes('ndc-hcpcs-crosswalk'))) &&
                    href.endsWith('.zip');
            });

            relevantLinks.forEach(link => {
                links.push(link.href);
            });

            return { links };
        });

        if (links.length === 0) {
            throw new Error("No 2024 ASP pricing files found on the page");
        }

        let { latestASPFiles, latestCrosswalkFiles } = this.extractLatestFiles(links);
        let fileName = latestASPFiles.split('/').pop();
        latestASPFiles = await this.downloadFile(latestASPFiles, fileName);
        fileName = latestCrosswalkFiles.split('/').pop();
        latestCrosswalkFiles = await this.downloadFile(latestCrosswalkFiles, fileName);
        await browser.close();
        return [latestASPFiles, latestCrosswalkFiles];
    };

    extractLatestFiles(fileLinks) {
        let latestASPFiles = [];
        let latestCrosswalkFiles = [];

        fileLinks.forEach(link => {
            const fileName = link.split('/').pop();
            const parts = fileName.split('-');
            const month = parts[0];
            const year = parseInt(parts[1]);

            if (fileName.includes('asp-pricing-file')) {
                if (!latestASPFiles[year] || moment(`${month} ${year} `, 'MMMM YYYY').isAfter(moment(`${latestASPFiles[year].month} ${year} `, 'MMMM YYYY'))) {
                    latestASPFiles[year] = link;
                }
            } else if (fileName.includes('ndc-hcpcs-crosswalk')) {
                if (!latestCrosswalkFiles[year] || moment(`${month} ${year} `, 'MMMM YYYY').isAfter(moment(`${latestCrosswalkFiles[year].month} ${year} `, 'MMMM YYYY'))) {
                    latestCrosswalkFiles[year] = link;
                }
            }
        });
        latestASPFiles = latestASPFiles.filter(Boolean);
        latestCrosswalkFiles = latestCrosswalkFiles.filter(Boolean);
        if (!(latestASPFiles.length > 0 && latestCrosswalkFiles.length > 0)) {
            throw new Error("latestASPFiles OR latestCrosswalkFiles is empty");
        }
        return { latestASPFiles: latestASPFiles[0], latestCrosswalkFiles: latestCrosswalkFiles[0] };
    }
}
class list_cms_ndc_hcpc_cw extends CMSPrices {
    constructor() {
        super();
        this.fieldsMapCSV = {
            "ASP": {
                "_2024_CODE": "hcpc",
                "BILLUNITS": "bill_units",
                "BILLUNITSPKG": "package_bill_units",
                "NDC2": 'ndc',
                "Short Description": 'short_desc',
                "LABELER NAME": 'labeler_name',
                "Drug Name": 'drug_name',
                "HCPCS dosage": 'hcpcs_dosage',
                "PKG SIZE": 'pkg_size',
                "PKG QTY": 'pkg_qty',
            },
            "AWP": {
                "HCPCS Code": "hcpc",
                "BILLUNITS": "bill_units",
                "BILLUNITSPKG": "package_bill_units",
                "NDC": 'ndc',
                "Short Descriptor": 'short_desc',
                "LABELER NAME": 'labeler_name',
                "DRUG NAME": 'drug_name',
                "HCPCS DOSAGE": 'hcpcs_dosage',
                "PKG SIZE": 'pkg_size',
                "PKG QTY": 'pkg_qty',
            },
            "OPPS": {
                "BILLUNITS": "bill_units",
                "BILLUNITSPKG": "package_bill_units",
                "_2024_CODE": "hcpc",
                "NDC or ALTERNATE ID": 'ndc',
                "Short Descriptor": 'short_desc',
                "LABELER NAME": 'labeler_name',
                "Drug Name": 'drug_name',
                "HCPCS Dosage": 'hcpcs_dosage',
                "PKG SIZE": 'pkg_size',
                "PKG QTY": 'pkg_qty',
            },
        };
    }
    async init() {
        console.log("Downloading CMS ASP files...");
        let files = await this.downloadASPPriceFiles(this.url);
        if (files.length < 0) {
            throw Error("No files downloaded from CMS");
        }
        let insertObjects = await this.getInsertObjects(files);
        if (insertObjects.length < 1) {
            throw Error("No data extracted from downloaded files");
        }
        let cols = [...new Set(Object.values(this.fieldsMapCSV.ASP))];
        cols.push('price_type');
        for (let entry of insertObjects) {
            let missingCols = cols.filter(c => !entry.hasOwnProperty(c));
            if (missingCols.length > 0)
                throw Error("Downloaded data missing required fields: " + cols.join(", "));
        }
        await this.generateSql(insertObjects, 'list_cms_ndc_hcpc_cw');
    };
    async getInsertObjects(files) {
        let objects = [];
        for (let file of files) {
            if (!file.includes("crosswalk")) {
                console.log("Skipping non-crosswalk file: " + file);
                continue;
            }
            let zip = this.getZipStream(file);
            let entries = Object.values(await zip.entries());
            for (let [type, fieldsMap] of Object.entries(this.fieldsMapCSV)) {
                let entry = entries.filter(e => !e.isDirectory && e.name.includes(type) && e.name.includes("Crosswalk") && e.name.endsWith(".csv"))[0];
                console.log("Processing " + type + " file: " + entry.name);
                let fileName = entry.name;
                await new Promise(async (resolve) => {
                    let stm = await zip.stream(entry.name);
                    stm.pipe(csv({ skipLines: 8 })).on('data', async (row) => {
                        const obj = {};
                        for (let [key, value] of Object.entries(fieldsMap)) {
                            let val = row[key];
                            if (!(val))
                                continue;
                            //Skipping J0211 as it is a special case where pkg_qty is wrong and this hcpcs is not used
                            if (row['_2024_CODE'] && row['_2024_CODE'] == 'J0211') {
                                continue;
                            }
                            if (key.includes('NDC')) {
                                val = val.split('-').join('');
                            }
                            if (value.includes('bill')) {
                                val = parseFloat(val);
                            }
                            obj[value] = val;
                            obj['price_type'] = type;
                        }
                        if (Object.keys(obj).length > 0) {
                            objects.push(obj);
                        }
                    }).on('end', () => {
                        console.log(fileName + ' processed');
                        resolve();
                    });
                });
            }
            zip.close();
        }
        return objects;
    }
}
class list_cms_ndc_hcpc extends CMSPrices {
    constructor() {
        super();
        this.fieldsMapCSVASP = {
            "HCPCS Code": "hcpc",
            "Short Description": "short_desc",
            "HCPCS Code Dosage": "hcpcs_dosage",
            "Payment Limit": 'payment_limit',
            "Co-insurance Percentage": 'co_insurance_per',
            "Vaccine AWP%": 'vaccine_awp_per',
            "Vaccine Limit": 'vaccine_limit',
            "Blood AWP%": 'blood_awp_per',
            "Blood limit": 'blood_limit',
            "Clotting Factor": 'clotting_factor',
            "Notes": 'notes',
        }
    }

    async init() {
        console.log("Downloading CMS ASP files...");
        let files = await this.downloadASPPriceFiles(this.url);
        if (files.length < 0) {
            throw Error("No files downloaded from CMS");
        }
        let insertObjects = await this.getInsertObjects(files);
        if (insertObjects.length < 1) {
            throw Error("No data extracted from downloaded files");
        }
        let cols = [...new Set(Object.values(this.fieldsMapCSVASP))];
        for (let entry of insertObjects) {
            let missingCols = cols.filter(c => !entry.hasOwnProperty(c));
            if (missingCols.length > 0)
                throw Error("Downloaded data missing required fields: " + cols.join(", "));
        }
        await this.generateSql(insertObjects, 'list_cms_ndc_hcpc');
    };

    async getInsertObjects(files) {
        let objects = [];
        let jsonData = cson.parse(fs.readFileSync(__dirname + '/../../base/cson/list_cms_ndc_hcpc.cson', 'utf8'));
        for (let file of files) {
            if (file.includes("crosswalk")) {
                continue;
            }
            let zip = this.getZipStream(file);
            let entries = Object.values(await zip.entries());

            let entry = entries.filter(e => !e.isDirectory && e.name.includes('ASP') && !e.name.includes("Crosswalk") && e.name.endsWith(".csv"))[0];
            console.log("Processing file: " + entry.name);
            let fileName = entry.name;
            await new Promise(async (resolve) => {
                let stm = await zip.stream(entry.name);
                stm.pipe(csv({ skipLines: 8 })).on('data', async (row) => {
                    const obj = {};
                    for (let [key, value] of Object.entries(this.fieldsMapCSVASP)) {
                        let val = row[key];
                        let updeted_val = this.validateFields(jsonData.fields[value], val);
                        obj[value] = updeted_val;
                    }
                    if (Object.keys(obj).length > 0) {
                        objects.push(obj);
                    }
                }).on('end', () => {
                    console.log(fileName + ' processed');
                    resolve();
                });
            });

            zip.close();
        }
        return objects;
    }

    validateFields(fieldDef, value) {
        if (value == 'N/A')
            return null;
        if (fieldDef.model && fieldDef.model?.type === 'int') {
            if (value && value.length > 0) {
                return parseInt(value, 10);
            } else {
                return null;
            }
        } else if (fieldDef.model && fieldDef.model?.type === 'decimal') {
            if (value && value.length > 0) {
                return parseFloat(value);
            } else {
                return null;
            }
        } else if (fieldDef.model && fieldDef.model?.type === 'date') {
            let val = moment(value).format('YYYY-MM-DD');
            if (val === 'Invalid date') {
                val = null;
            }
            return `'${val}'`;
        } else {
            return value;
        }
    }
}

module.exports = { CMSPrices };
