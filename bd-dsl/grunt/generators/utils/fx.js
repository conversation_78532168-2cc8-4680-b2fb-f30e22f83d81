const pgesc = require("pg-escape");
const _ = require('lodash');
const { exec } = require("child_process");

exports.serialize = (value) => {
    if (value === null) {
        return 'NULL';
    } else if (typeof value === 'string') {
        return pgesc.literal(value);
    } else if (value instanceof Date) {
        return pgesc.literal(value.toISOString());
    } else if (typeof value === 'object') {
        // For JSON/JSONB, stringify and then escape the complete JSON string.
        return pgesc.literal(JSON.stringify(value));
    } else if (typeof value === 'boolean') {
        return pgesc.literal(value.toString());
    } else if (typeof value === 'number' || typeof value === 'bigint') {
        // Directly use numbers as they don't require escaping.
        return value.toString();
    }
    return value.toString();
};

exports.executeRemote = (cmd, timeout) => {
    if (!timeout) {
        timeout = 50000;
    }
    return new Promise((resolve, reject) => {
        try {
            exec(cmd, { timeout: timeout }, (error, stdout, stderr) => {
                if (error || stderr) {
                    if (!stderr.includes('skipping')) {
                        reject(error || stderr);
                        return;
                    }
                }
                resolve(true);
            });
        } catch (err) {
            console.error(err);
            reject(err);
        }
    });
};

exports.form_auto_name = (cson, data) => {
    let auto_name_field = cson.model.name;
    try {
        if (auto_name_field.length > 0) {
            if (Array.isArray(auto_name_field)) {
                //add all elements of array to create auto_name
                var auto_name = "";
                auto_name_field.forEach((col) => {
                    if (!!data[col]) {
                        let ss = data[col];
                        if (typeof ss === 'string') {
                            if (ss.replaceAll('(', '').replaceAll(')', '').replaceAll('{', '').replaceAll('}', '').trim() !== '')
                                auto_name += data[col] + " ";
                        }
                        else
                            auto_name += data[col] + " ";
                    }
                });
                auto_name = auto_name.trim();
                data['auto_name'] = auto_name;
            } else {
                // parse string to see if it follows a pattern or is it a name of column
                if (auto_name_field.includes("{")) {
                    let auto_name = auto_name_field;
                    let cols = _get_column_names(auto_name_field);
                    cols.forEach((col) => {
                        if (col.includes(":")) {
                            for (const [k, v] of Object.entries(this._is_float(col))) {
                                let val;
                                if (data[k] != "") {
                                    val = parseFloat(data[k]).toFixed(v);
                                } else {
                                    val = "0.00";
                                }
                                auto_name = auto_name.replace("{" + col + "}", _.isEmpty(val) ? "" : val);
                            }
                        } else {
                            if (get_type(data[col]) === 'string')
                                data[col] = data[col].trim();
                            if (get_type(data[col]) === 'number') {
                                auto_name = auto_name.replace("{" + col + "}", data[col]);
                            } else {
                                auto_name = auto_name.replace("{" + col + "}", _.isEmpty(data[col]) ? "" : data[col]);
                            }
                        }
                    });
                    data['auto_name'] = auto_name;
                } else {
                    data['auto_name'] = data[auto_name_field];
                }
            }
        } else if (cson.view.name) {
            data['auto_name'] = data.name;
        } else if (cson.view.code) {
            data['auto_name'] = data.code;
        } else {
            data['auto_name'] = "";
        }
        data['auto_name'] = _remove_empty_brackets(data['auto_name']);
        // console.log("Generated auto_name:" + data['auto_name']);
    } catch (e) {
        console.log(e);
    }
    return data['auto_name'];
};
_remove_empty_brackets = (string) => {
    if (typeof string === 'string') {
        string = string.replace("()", "");
        string = string.replace("{}", "");
        string = string.replace("[]", "");
        string = string.trim().replace(/^:|:$/g, '').trim();
        return string;
    }
};

get_type = (val) => {
    if (val === null)
        return "null";
    else if (typeof val === "boolean")
        return "boolean";
    else if (typeof val === "string")
        return "string";
    else if (typeof val === "number")
        return "number";
    else if (typeof val === "object")
        return (val instanceof Array ? "array" : "object");
    else
        return "undefined";
};

let _get_column_names = (auto_name_field) => {
    var col_names = [];
    var columns = [];
    let autoname_strings = _space_seperated_strings(auto_name_field);
    autoname_strings.forEach((str) => {
        col_names.push(str_btw_enclosed(str));
    });
    col_names.forEach((cols) => {
        cols.forEach((col) => {
            columns.push(col);
        });
    });
    return columns;
};
let _space_seperated_strings = (str) => {
    return str.split(" ");
};

let str_btw_enclosed = (str, startsWith = "{", endsWith = "}") => {
    var cols = [];
    (str.split(endsWith)).forEach((str) => {
        if (str.startsWith(startsWith)) {
            (str.split(startsWith)).forEach((str) => {
                if (str != "") cols.push(str);
            });
        } else if (str.indexOf(startsWith) != -1) {
            str.substring(str.indexOf(startsWith)).split(startsWith).forEach((str) => {
                if (str != "") cols.push(str);
            });
        }
    });
    return cols;
};

/**
 * Safely writes data to a stream, ensuring the write completes before proceeding
 * @param {WriteStream} stream - The stream to write to
 * @param {string} data - The data to write
 * @param {boolean} [flush=false] - Whether to flush the stream after writing
 * @returns {Promise<void>}
 */
exports.safeWrite = async (stream, data, flush = false) => {
    return new Promise((resolve, reject) => {
        const canContinue = stream.write(data, 'utf8', (err) => {
            if (err) {
                reject(err);
                return;
            }
            
            if (!canContinue || flush) {
                stream.once('drain', () => {
                    resolve();
                });
            } else {
                resolve();
            }
        });
    });
};