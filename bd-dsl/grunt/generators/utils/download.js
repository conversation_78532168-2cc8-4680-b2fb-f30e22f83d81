const fs = require('fs');
const path = require('path');
const https = require('https');

/**
 * Downloads a file from a URL and saves it to the specified location
 * @param {string} url - The URL to download from
 * @param {string} fileName - The name to save the file as
 * @param {string} targetFolder - The folder to save the file in
 * @returns {Promise<string>} - The full path to the downloaded file
 */
async function downloadFile(url, fileName, targetFolder) {
    return new Promise((resolve, reject) => {
        const filePath = path.join(targetFolder, fileName);
        
        // Create write stream
        const fileStream = fs.createWriteStream(filePath);
        
        // Make HTTPS request
        https.get(url, (response) => {
            // <PERSON>le redirects
            if (response.statusCode === 301 || response.statusCode === 302) {
                downloadFile(response.headers.location, fileName, targetFolder)
                    .then(resolve)
                    .catch(reject);
                return;
            }

            // Verify successful response
            if (response.statusCode !== 200) {
                reject(new Error(`Failed to download file: ${response.statusCode} ${response.statusMessage}`));
                return;
            }

            // Pipe the response to the file
            response.pipe(fileStream);

            // Handle errors
            response.on('error', (err) => {
                fs.unlink(filePath, () => reject(err));
            });

            fileStream.on('error', (err) => {
                fs.unlink(filePath, () => reject(err));
            });

            // Resolve when download completes
            fileStream.on('finish', () => {
                fileStream.close();
                console.log(`Downloaded ${fileName} successfully`);
                resolve(filePath);
            });
        }).on('error', (err) => {
            fs.unlink(filePath, () => reject(err));
        });
    });
}

module.exports = {
    downloadFile
}; 