const { parentPort, workerData, threadId } = require('worker_threads');
const { form_auto_name } = require('./utils/fx');
const moment = require('moment');
const grunt = require('grunt');

// Add a timeout mechanism to detect if the worker gets stuck
let processingTimeout;
const PROCESSING_TIMEOUT = 120000; // 120 seconds max for processing a chunk
let lastProgressReport = Date.now();
const PROGRESS_INTERVAL = 10000; // Report progress every 10 seconds for large chunks

// Track memory usage
function getMemoryUsage() {
    const memoryUsage = process.memoryUsage();
    return {
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        rss: Math.round(memoryUsage.rss / 1024 / 1024)
    };
}

function resetProcessingTimeout() {
    if (processingTimeout) {
        clearTimeout(processingTimeout);
    }
    
    processingTimeout = setTimeout(() => {
        const memory = getMemoryUsage();
        grunt.log.error(`Worker ${threadId}: Processing timeout after ${PROCESSING_TIMEOUT/1000}s | Mem: ${memory.heapUsed}/${memory.heapTotal}MB`);
        // Send an error message back to the main thread
        parentPort.postMessage({
            error: `Processing timeout after ${PROCESSING_TIMEOUT/1000}s. Possible memory issue or deadlock.`,
            workerId: threadId
        });
    }, PROCESSING_TIMEOUT);
}

function formatValue(value, fieldDef) {
    if (value === undefined || value === null || value === '') {
        return 'NULL';
    }

    try {
        if (fieldDef.model && fieldDef.model.type === 'int') {
            const parsed = parseInt(value, 10);
            return isNaN(parsed) ? 'NULL' : parsed;
        } else if (fieldDef.model && fieldDef.model.type === 'decimal') {
            const parsed = parseFloat(value);
            return isNaN(parsed) ? 'NULL' : parsed;
        } else if (fieldDef.model && fieldDef.model.type === 'date') {
            if (!value) return 'NULL';
            const formatted = moment(value).format('YYYY-MM-DD');
            return formatted === 'Invalid date' ? 'NULL' : `'${formatted}'`;
        } else {
            if (typeof value !== 'string') {
                value = String(value);
            }
            return `'${value.replace(/'/g, "''")}'`;
        }
    } catch (err) {
        grunt.log.error(`Worker ${threadId}: Error formatting value "${value}": ${err.message}`);
        return 'NULL';
    }
}

function processChunk(chunk, tableName) {
    // Reset the processing timeout
    resetProcessingTimeout();
    lastProgressReport = Date.now();
    
    const values = [];
    let processedCount = 0;
    const { jsonData, columnData, fieldDefinitions, csonList } = workerData;
    const chunkLength = chunk.length;
    const startTime = Date.now();

    try {
        for (const row of chunk) {
            try {
                const autonameData = {};
                const rowValues = [];
                
                // Get the code value directly from the row object
                // This was set in getJsonObj in the main process
                const codeValue = row.code !== undefined && row.code !== null 
                    ? `'${row.code.toString().replace(/'/g, "''")}'` 
                    : 'NULL';
                
                // Add formatted_ndc if specified in csonList
                let formattedNdcValue = 'NULL';
                if (csonList && csonList[tableName] && csonList[tableName].formatted_ndc === true) {
                    // Format the NDC value
                    const formattedNdc = formatNDC(row.ndc, row.ndcfi);
                    if (formattedNdc) {
                        formattedNdcValue = `'${formattedNdc.replace(/'/g, "''")}'`;
                        // Add formatted_ndc to autonameData for auto_name generation
                        autonameData['formatted_ndc'] = formattedNdc;
                    }
                }
                
                // Process each field according to field definitions
                for (const fieldDef of fieldDefinitions) {
                    const field = Object.keys(fieldDef)[0];
                    const value = formatValue(row[field], fieldDef[field]);
                    rowValues.push(value);
                    
                    if (row[field] !== undefined && row[field] !== null) {
                        autonameData[field] = row[field];
                    }
                }

                // Add formatted_ndc to rowValues if needed
                if (csonList && csonList[tableName] && csonList[tableName].formatted_ndc === true) {
                    rowValues.push(formattedNdcValue);
                }

                // Generate auto_name using the proper function
                const auto_name = form_auto_name(jsonData, autonameData);
                
                // Add auto_name, created_on and created_by
                const now = moment().format('YYYY-MM-DD HH:mm:ss');
                rowValues.push(`'${auto_name.replace(/'/g, "''")}'`);
                rowValues.push(`'${now}'`);
                rowValues.push(1); // created_by = 1

                // Create the complete value string with placeholder for ID and code
                values.push(`(ID_PLACEHOLDER, ${codeValue}, ${rowValues.join(", ")})`);
                
                processedCount++;
                
                // Reset timeout periodically during processing
                if (processedCount % 5000 === 0) {
                    const elapsed = (Date.now() - startTime) / 1000;
                    const speed = Math.round(processedCount / elapsed);
                    grunt.log.debug(`Worker ${threadId}: ${processedCount.toLocaleString()} records at ${speed.toLocaleString()}/sec`);
                }
            } catch (err) {
                grunt.log.error(`Worker ${threadId}: Error processing row: ${err.message}`);
            }
        }
        
        // Free up memory by clearing references to processed objects
        chunk.length = 0;
        
    } catch (err) {
        grunt.log.error(`Worker ${threadId}: Error processing chunk: ${err.message}`);
        throw err; // Re-throw to be caught by the message handler
    } finally {
        // Clear the timeout when done processing
        if (processingTimeout) {
            clearTimeout(processingTimeout);
        }
    }

    const duration = (Date.now() - startTime) / 1000;
    const speed = Math.round(processedCount / duration);
    grunt.log.debug(`Worker ${threadId}: Completed ${processedCount.toLocaleString()} records in ${duration.toFixed(2)}s (${speed.toLocaleString()}/sec)`);

    return {
        values,
        processedCount,
        workerId: threadId
    };
}

// Helper function to format NDC
function formatNDC(ndc, ndcfi) {
    if (!ndc) return null;
    ndc = ndc.replace(/[-\s]/g, '');

    try {
        // All NDCs from FDB are 11 digits, format as 5-4-2
        if (ndc.length === 11) {
            return `${ndc.slice(0, 5)}-${ndc.slice(5, 9)}-${ndc.slice(9)}`;
        }

        return ndc;
    } catch (error) {
        return ndc;
    }
}

// Handle messages from the main thread
parentPort.on('message', (message) => {
    grunt.log.debug(`Worker ${threadId}: Received message. Message type: ${message.chunk ? 'chunk processing' : 'other'}`);
    
    try {
        if (message.finalize) {
            grunt.log.debug(`Worker ${threadId}: Received finalize signal, completing pending work...`);
            // Send acknowledgment back to main thread
            parentPort.postMessage({
                finalizeAck: true,
                workerId: threadId
            });
            return;
        }

        const { chunk, tableName } = message;
        grunt.log.debug(`Worker ${threadId}: Processing chunk for table ${tableName} with ${chunk ? chunk.length : 0} records`);
        
        resetProcessingTimeout();
        
        const memory = getMemoryUsage();
        grunt.log.debug(`Worker ${threadId}: Starting chunk of ${chunk.length.toLocaleString()} records | Mem: ${memory.heapUsed}/${memory.heapTotal}MB`);
        
        const startTime = Date.now();
        const result = processChunk(chunk, tableName);
        const duration = (Date.now() - startTime) / 1000;
        grunt.log.debug(`Worker ${threadId}: Processed chunk in ${duration.toFixed(2)}s with ${result.processedCount} records and ${result.values ? result.values.length : 0} values`);
        
        const finalMemory = getMemoryUsage();
        grunt.log.debug(`Worker ${threadId}: Finished chunk of ${result.processedCount.toLocaleString()} records | Mem: ${finalMemory.heapUsed}/${finalMemory.heapTotal}MB`);
        
        // Clear the timeout
        if (processingTimeout) {
            clearTimeout(processingTimeout);
        }
        
        // Run garbage collection if available
        if (global.gc) {
            try {
                global.gc();
            } catch (e) {
                // Ignore if gc is not available
            }
        }
        
        parentPort.postMessage(result);
    } catch (err) {
        grunt.log.debug(`Worker ${threadId}: Encountered error: ${err.message}`);
        grunt.log.error(`Worker ${threadId}: Unhandled error: ${err.message}`);
        
        // Clear the timeout
        if (processingTimeout) {
            clearTimeout(processingTimeout);
        }
        
        // Send error result back to main thread
        grunt.log.debug(`Worker ${threadId}: Sending error back to main thread`);
        parentPort.postMessage({
            error: err.message,
            workerId: threadId,
            processedCount: 0
        });
    }
});

// Handle worker errors
process.on('uncaughtException', (err) => {
    grunt.log.error(`Worker ${threadId}: Uncaught exception: ${err.message}`);
    
    // Clear timeout
    if (processingTimeout) {
        clearTimeout(processingTimeout);
    }
    
    // Attempt to notify main thread
    try {
        parentPort.postMessage({
            error: `Uncaught exception: ${err.message}`,
            workerId: threadId,
            processedCount: 0
        });
    } catch (e) {
        // If we can't send a message, just exit
        process.exit(1);
    }
});

// Handle out of memory errors specifically
process.on('memoryWarning', () => {
    const memory = getMemoryUsage();
    grunt.log.warn(`Worker ${threadId}: Memory pressure detected | Mem: ${memory.heapUsed}/${memory.heapTotal}MB`);
    
    // Run garbage collection if available
    if (global.gc) {
        try {
            global.gc();
            grunt.log.debug(`Worker ${threadId}: Garbage collection performed`);
        } catch (e) {
            // Ignore if gc is not available
        }
    }
});

// Log when worker starts
const startMemory = getMemoryUsage();
grunt.log.debug(`Worker ${threadId}: Initialized and ready | Mem: ${startMemory.heapUsed}/${startMemory.heapTotal}MB`);