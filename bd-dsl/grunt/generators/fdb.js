"use strict";
const BaseRunner = require('./base');
const csv = require('csv-parser');
const fs = require("fs");
const moment = require("moment");
const splitFile = require('split-file');
const {
    form_auto_name,
    safeWrite
} = require('./utils/fx');
const os = require('os');
const { Worker } = require('worker_threads');
const path = require('path');

class FDBImport extends BaseRunner {
    constructor() {
        super();
        this.sourceReadOptions = {
            separator: '|'
        };
        this.sourceFolder = this.sourceFolder + '/fdb/';
        
        // Add concurrency management properties
        this.BATCH_SIZE = 50000;
        this.CHUNK_SIZE = 10000;
        this.WRITE_BUFFER_SIZE = 1024 * 1024 * 16;
        this.numWorkers = Math.min(os.cpus().length, 8);
        this.workers = [];
        this.currentChunk = [];
        this.processedCount = 0;
        this.startTime = Date.now();
        this.lastProgressUpdate = Date.now();
        this.lastDetailedLog = Date.now();
        this.UPDATE_INTERVAL = 1000;
        this.workerStatus = new Map();
        this.pendingChunks = [];
        this.writeQueue = [];
        this.writeInProgress = false;
        this.activeWorkers = 0;
        this.maxConcurrentWorkers = this.numWorkers;
        this.sequenceCounters = {};
        this.lastMemCheck = Date.now();
        this.MEM_CHECK_INTERVAL = 30000; // Check memory every 30 seconds
        this.MAX_HEAP_SIZE = 3072; // 3GB max heap size
        this.buffers = {};
        this.csonList = {
            'list_fdb_ndc': {
                sourceDataFilePath: this.sourceFolder + '/RNDC14_NDC_MSTR',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|',
                    quote: "{"
                },
                dupKey: ['ndc'],
                formatted_ndc: true
            },
            'list_fdb_tm_ndc': {
                sourceDataFilePath: this.sourceFolder + '/RTMNID0_TM_NDC',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_TALL_MAN_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['ndc', 'tm_name_type_id'] // From RTMNID0_PK in TALL_MAN_ANSI.SQL
            },
            'list_fdb_alrgn_grp_desc': {
                sourceDataFilePath: this.sourceFolder + '/RDAMAGD1_ALRGN_GRP_DESC',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_ALLERGY_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: 'dam_alrgn_grp' // From RDAMAGD1_PK in DRUG_ALLERGY_ANSI.SQL
            },
            'list_fdb_med_table': {
                sourceDataFilePath: this.sourceFolder + '/RMIID1_MED',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_MEDICATION_CONCEPTS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|',
                    quote: ";"
                },
                dupKey: 'medid' // From RMIID1_PK in MEDICATION_CONCEPTS_ANSI.SQL
            },
            'list_fdb_medicare_mstr': {
                sourceDataFilePath: this.sourceFolder + '/RMCRMA1_MEDICARE_MSTR',
                sourceHeaderFilePath: this.sourceFolder + '/MEDICARE_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['ndc', 'mcr_region', 'mcr_datec'] // From RMCRMA1_PK in MEDICARE_ANSI.SQL
            },
            'list_fdb_medicare_price': {
                sourceDataFilePath: this.sourceFolder + '/RMCRPB0_MEDICARE_HCPC_PB_PRICE',
                sourceHeaderFilePath: this.sourceFolder + '/MEDICARE_RDB.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['hcpc', 'ndc']
            },
            'list_fdb_ndc_clin_qual_map': {
                sourceDataFilePath: this.sourceFolder + '/RCQDNDC0_DISP_CLNQTY_NDC',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                }
            },
            'list_fdb_clinqty_ndc': {
                sourceDataFilePath: this.sourceFolder + '/RCQNDC0_CLNQTY_NDC',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_CROSS_REFERENCE_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['ndc', 'medid'] // From RCQNDC0_PK in CROSS_REFERENCE_ANSI.SQL
            },
            'list_fdb_clinqty_medid': {
                sourceDataFilePath: this.sourceFolder + '/RCQMED0_CLNQTY_MEDID',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_CROSS_REFERENCE_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['medid', 'medid_sn'] // From RCQMED0_PK in CROSS_REFERENCE_ANSI.SQL
            },
            'list_fdb_gen_df_mstr_link': {
                sourceDataFilePath: this.sourceFolder + '/RPEIGL0_GEN_DF_MSTR_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['dosage_form_id', 'gcdf']
            },
            'list_fdb_price_qty_uom': {
                sourceDataFilePath: this.sourceFolder + '/RPRDUOM0_PRICE_QTY_UOM',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: 'price_uom_id' // From RPRDUOM0_PK in BASICS_ANSI.SQL
            },
            'list_fdb_ndc_to_route': {
                sourceDataFilePath: this.sourceFolder + '/RPEINR0_NDC_RT_RELATION',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['ndc', 'clinical_rt_id']
            },
            'list_fdb_route_abbrev': {
                sourceDataFilePath: this.sourceFolder + '/RROUTED3_ROUTE_DESC',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: 'gcrt'
            },
            'list_fdb_ndc_to_therapy': {
                sourceDataFilePath: this.sourceFolder + '/RETCNDC0_ETC_NDC',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_ENHANCED_THERAPEUTIC_CLASSIFICATION_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['ndc', 'etc_id'] // From RETCNDC0_PK in THERAPEUTIC_CLASSIFICATION_ANSI.SQL
            },
            'list_fdb_route_mstr': {
                sourceDataFilePath: this.sourceFolder + '/RPEIRM0_RT_MSTR',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                }
            },
            'list_fdb_therapy': {
                sourceDataFilePath: this.sourceFolder + '/RETCTBL0_ETC_ID',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_ENHANCED_THERAPEUTIC_CLASSIFICATION_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                }
            },
            'list_manufacturer': {
                sourceDataFilePath: this.sourceFolder + '/RLBLRID3_LBLR_DESC',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: 'lblrid'
            },
            'list_fdb_ndc_attribute': {
                sourceDataFilePath: this.sourceFolder + '/RNDCAT0_NDC_ATTRIBUTE',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['ndc', 'ndc_attribute_type_cd', 'ndc_attribute_sn']
            },
            'list_fdb_alt_strength': {
                sourceDataFilePath: this.sourceFolder + '/RPEINS0_NDC_STR_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['ndc', 'hic_seqn', 'str_conc_type_id']
            },
            'list_fdb_medicare_desc': {
                sourceDataFilePath: this.sourceFolder + '/RMCRRD1_MEDICARE_REG_REF_DESC',
                sourceHeaderFilePath: this.sourceFolder + '/MEDICARE_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['mcr_ref', 'mcr_refsn']
            },
            'list_fdb_ndc_to_medid': {
                sourceDataFilePath: this.sourceFolder + '/RMINDC1_NDC_MEDID',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_MEDICATION_CONCEPTS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: 'ndc'
            },
            'list_fdb_gcnseq_label_link': {
                sourceDataFilePath: this.sourceFolder + '/RLBLWGC0_GCNSEQNO_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/PRIORITIZED_LABEL_WARNINGS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['gcn_seqno', 'lbl_warn']
            },
            'list_fdb_warning_label_info': {
                sourceDataFilePath: this.sourceFolder + '/RLBLWD0_DESC',
                sourceHeaderFilePath: this.sourceFolder + '/PRIORITIZED_LABEL_WARNINGS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|',
                    quote: "+"
                },
                dupKey: ['lbl_warn', 'lbl_textsn'] // From RLBLWD0_PK in LABEL_WARNINGS_ANSI.SQL
            },
            'list_fdb_alrgn_mstr': {
                sourceDataFilePath: this.sourceFolder + '/RDAMAPM0_ALRGN_PICKLIST_MSTR',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_ALLERGY_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['dam_concept_id', 'dam_concept_id_typ'] // From RDAMAPM0_PK in DRUG_ALLERGY_ANSI.SQL
            },
            'list_fdb_ndc_attribute_val': {
                sourceDataFilePath: this.sourceFolder + '/RNDCVD0_NDC_ATTRIBUTE_VALU_DSC',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: 'ndc_attribute_value_cd' // From RNDCVD0_PK in NDDF_DESCRIPTIVE_BASICS_ANSI.SQL
            },
            'list_fdb_dosage_master': {
                sourceDataFilePath: this.sourceFolder + '/RPEIDM0_DOSAGE_FORM_MSTR',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: 'dosage_form_id' // From RPEIDM0_PK in NDDF_DESCRIPTIVE_BASICS_ANSI.SQL
            },
            'list_fdb_unit_master': {
                sourceDataFilePath: this.sourceFolder + '/RPEIUM0_UOM_MSTR',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: 'uom_id' // From RPEIUM0_PK in NDDF_DESCRIPTIVE_BASICS_ANSI.SQL
            },
            'list_fdb_unit_conversion': {
                sourceDataFilePath: this.sourceFolder + '/RPEIUC0_UOM_CONVERSION',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['from_uom_id', 'to_uom_id'] // From RPEIUC0_PK in NDDF_DESCRIPTIVE_BASICS_ANSI.SQL
            },
            'list_fdb_ingred_strength': {
                sourceDataFilePath: this.sourceFolder + '/RSTRUOM0_STRENGTH_UOM',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|',
                    quote: '',
                    escape: '',

                },
                dupKey: ['uom_id'] // From RSTRUOM0_PK in NDDF_DESCRIPTIVE_BASICS_ANSI.SQL
            },
            'list_fdb_clinc_ingred_strength': {
                sourceDataFilePath: this.sourceFolder + '/RGCNSTR0_INGREDIENT_STRENGTH',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['gcn_seqno', 'hic_seqn']
            },
            'list_fdb_uom_desc': {
                sourceDataFilePath: this.sourceFolder + '/RPEIUT0_UOM_TYPE_DESC',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: 'uom_type_id' // From RPEIUT0_PK in NDDF_DESCRIPTIVE_BASICS_ANSI.SQL
            },
            'list_fdb_ob_ndc_rtl': {
                sourceDataFilePath: this.sourceFolder + '/ROBCNDC0_OBC_NDC',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['ndc'] // From ROBCNDC0_PK in NDDF_DESCRIPTIVE_BASICS_ANSI.SQL
            },
            'list_fdb_ndc_fda_app': {
                sourceDataFilePath: this.sourceFolder + '/RAPPLNA0_FDA_NDC_APPL',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['ndc'] // From RAPPLNA0_FDA_NDC_APPL in NDDF_DESCRIPTIVE_BASICS_ANSI.SQL
            },
            'list_fdb_ndc_dose_form_link': {
                sourceDataFilePath: this.sourceFolder + '/RPEIND0_NDC_DF_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['ndc']
            },
            'list_fdb_hic_alrgn_grp_link': {
                sourceDataFilePath: this.sourceFolder + '/RDAMGHC0_HIC_ALRGN_GRP_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_ALLERGY_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['hic_seqn', 'dam_alrgn_grp'] // From RDAMGHC0_PK in DRUG_ALLERGY_ANSI.SQL
            },
            'list_fdb_alrgn_grp_xsense_link': {
                sourceDataFilePath: this.sourceFolder + '/RDAMGX0_ALRGN_GRP_XSENSE_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_ALLERGY_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['dam_alrgn_grp', 'dam_alrgn_xsense'] // From RDAMGX0_PK in DRUG_ALLERGY_ANSI.SQL
            },
            'list_fdb_hic_hicl_alrg_link': {
                sourceDataFilePath: this.sourceFolder + '/RDAMHHA0_HIC_HICL_ALG_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_ALLERGY_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['hicl_seqno', 'dam_alrgn_hic_seqn'] // From RDAMHHA0_PK in DRUG_ALLERGY_ANSI.SQL
            },
            'list_fdb_hic_alrgn_xsense_link': {
                sourceDataFilePath: this.sourceFolder + '/RDAMXHC0_HIC_ALRGN_XSENSE_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_ALLERGY_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['hic_seqn', 'dam_alrgn_xsense'] // From RDAMXHC0_PK in DRUG_ALLERGY_ANSI.SQL
            },
            'list_fdb_alrgn_xsense_hist': {
                sourceDataFilePath: this.sourceFolder + '/RDAMXSH0_ALRGN_XSENSE_HIST',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_ALLERGY_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['repl_dam_alrgn_xsense', 'prev_dam_alrgn_xsense'] // From RDAMXSH0_PK in DRUG_ALLERGY_ANSI.SQL
            },
            'list_fdb_hic_desc': {
                sourceDataFilePath: this.sourceFolder + '/RHICD5_HIC_DESC',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: 'hic_seqn' // From RHICD5_PK in NDDF_DESCRIPTIVE_BASICS_ANSI.SQL
            },
            'list_fdb_alrgn_grp_hist': {
                sourceDataFilePath: this.sourceFolder + '/RDAMGRH0_ALRGN_GRP_HIST',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_ALLERGY_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['repl_dam_alrgn_grp', 'prev_dam_alrgn_grp'] // From RDAMGRH0_PK in DRUG_ALLERGY_ANSI.SQL
            },
            'list_fdb_gcnseqno_mstr': {
                sourceDataFilePath: this.sourceFolder + '/RGCNSEQ4_GCNSEQNO_MSTR',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|',
                    quote: "}"
                },
                dupKey: 'gcn_seqno' // From RGCNSEQ4_PK in NDDF_DESCRIPTIVE_BASICS_ANSI.SQL
            },
            'list_fdb_concept': {
                sourceDataFilePath: this.sourceFolder + '/RDAMCA0_CONCEPT',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_ALLERGY_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|',
                    quote: "}"
                },
                dupKey: ['dam_concept_id', 'dam_concept_id_typ'] // From RDAMCA0_PK in DRUG_ALLERGY_ANSI.SQL
            },
            'list_fdb_ndc_inactv_link': {
                sourceDataFilePath: this.sourceFolder + '/RNDCINH0_NDC_INACTV_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['ndc', 'inactv_date'] // From RNDCINH0_PK in NDDF_DESCRIPTIVE_BASICS_ANSI.SQL
            },
            'list_fdb_hic_hic_link': {
                sourceDataFilePath: this.sourceFolder + '/RHICHCR0_HIC_HIC_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['hic_seqn', 'rel_hic_seqn'] // From RHICHCR0_PK in NDDF_DESCRIPTIVE_BASICS_ANSI.SQL
            },
            'list_fdb_inactv_reviewed': {
                sourceDataFilePath: this.sourceFolder + '/RNDCINR0_INACTV_REVIEWED',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['ndc', 'inactv_date'] // From RNDCINR0_PK in NDDF_DESCRIPTIVE_BASICS_ANSI.SQL
            },
            'list_fdb_hic_hiclseqno_link': {
                sourceDataFilePath: this.sourceFolder + '/RHICL1_HIC_HICLSEQNO_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['hic_seqn', 'hicl_seqno'] // From RHICL1_PK in NDDF_DESCRIPTIVE_BASICS_ANSI.SQL
            },
            'list_fdb_med_hiclseqno_link': {
                sourceDataFilePath: this.sourceFolder + '/RMEDMHL0_MED_HICLSEQNO_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_MEDICATION_CONCEPTS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['med_concept_id', 'med_concept_id_typ', 'hicl_seqno'] // From RMEDMHL0_PK in MEDICATION_CONCEPTS_ANSI.SQL
            },
            'list_fdb_xsensit_allergy_desc': {
                sourceDataFilePath: this.sourceFolder + '/RDAMCSD1_XSENSIT_ALLERGY_DESC',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_ALLERGY_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|',
                    quote: "}"
                },
                dupKey: 'dam_alrgn_xsense' // From RDAMCSD1_PK in DRUG_ALLERGY_ANSI.SQL
            },
            'list_fdb_med_concept_typ_desc': {
                sourceDataFilePath: this.sourceFolder + '/RMEDCD0_MED_CONCEPT_TYP_DESC',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_MEDICATION_CONCEPTS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: 'med_concept_id_typ' // From RMEDCD0_PK in MEDICATION_CONCEPTS_ANSI.SQL
            },
            'list_fdb_gcnseqno_link': {
                sourceDataFilePath: this.sourceFolder + '/RADIMGC4_GCNSEQNO_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_DRUG_INTERACTION_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['gcn_seqno', 'ddi_codex'] // From RADIMGC4_PK in DRUG_DRUG_INTERACTION_ANSI.SQL
            },
            'list_fdb_gcnseqno_str_link': {
                sourceDataFilePath: this.sourceFolder + '/RPEIGS0_GCNSEQNO_STR_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['gcn_seqno', 'str_strength_type_id'] // From RPEIGS0_PK in NDDF_DESCRIPTIVE_BASICS_ANSI.SQL
            },
            'list_fdb_mstr': {
                sourceDataFilePath: this.sourceFolder + '/RADIMMA5_MSTR',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_DRUG_INTERACTION_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|',
                    quote: "}"
                },
                dupKey: 'ddi_codex' // From RADIMMA5_PK in DRUG_DRUG_INTERACTION_ANSI.SQL
            },
            'list_fdb_mono': {
                sourceDataFilePath: this.sourceFolder + '/RADIMMO5_MONO',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_DRUG_INTERACTION_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|',
                    quote: "}"
                },
                dupKey: ['ddi_monox', 'adi_monosn'] // From RADIMMO5_PK in DRUG_DRUG_INTERACTION_ANSI.SQL
            },
            'list_fdb_sever_level': {
                sourceDataFilePath: this.sourceFolder + '/RADIMSL1_SEVER_LEVEL',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_DRUG_INTERACTION_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['ddi_sl', 'ddi_slsn'] // From RADIMSL1_PK in DRUG_DRUG_INTERACTION_ANSI.SQL
            },
            'list_fdb_clin_effects_link': {
                sourceDataFilePath: this.sourceFolder + '/RADIMIE4_CLIN_EFFECTS_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_DRUG_INTERACTION_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['ddi_codex', 'adi_efftc'] // From RADIMIE4_PK in DRUG_DRUG_INTERACTION_ANSI.SQL
            },
            'list_fdb_clin_effect': {
                sourceDataFilePath: this.sourceFolder + '/RADIMEF0_CLIN_EFFECT',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_DRUG_INTERACTION_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: 'adi_efftc' // From RADIMEF0_PK in DRUG_DRUG_INTERACTION_ANSI.SQL
            },
            'list_fdb_ndc_inactv_ddim_link': {
                sourceDataFilePath: this.sourceFolder + '/RDDIMIN0_NDC_INACTV_DDIM_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/DRUG_DRUG_INTERACTION_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['ddi_ndc', 'ddi_codex', 'ddi_ndc_hicseqn'] // From RDDIMIN0_PK in DRUG_DRUG_INTERACTION_ANSI.SQL
            },
            'list_fdb_mono_gcnseqno_link': {
                sourceDataFilePath: this.sourceFolder + '/RPEMOGC0_MONO_GCNSEQNO_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/PATIENT_EDUCATION_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['pemono', 'gcn_seqno'] // From RPEMOGC0_PK in PATIENT_EDUCATION_ANSI.SQL
            },
            "list_fdb_rpemmoe2_mono": {
                sourceDataFilePath: this.sourceFolder + '/RPEMMOE2_MONO',
                sourceHeaderFilePath: this.sourceFolder + '/PATIENT_EDUCATION_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|'
                },
                dupKey: ['pemono'] // From RPEMMOE2_PK in PATIENT_EDUCATION_ANSI.SQL
            },
            'list_fdb_rmidfid1_routed_dose_form_med': {
                sourceDataFilePath: this.sourceFolder + '/RMIDFID1_ROUTED_DOSE_FORM_MED',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_MEDICATION_CONCEPTS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|',
                    quote: "}"
                },
                dupKey: ['routed_med_id', 'med_dosage_form_id']
            },
            'list_fdb_rmirmid1_routed_med': {
                sourceDataFilePath: this.sourceFolder + '/RMIRMID1_ROUTED_MED',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_MEDICATION_CONCEPTS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|',
                    quote: "}"
                },
                dupKey: ['routed_med_id', 'med_name_id']
            },
            'list_fdb_rminmid1_med_name': {
                sourceDataFilePath: this.sourceFolder + '/RMINMID1_MED_NAME',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_MEDICATION_CONCEPTS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|',
                    quote: "}"
                },
                dupKey: ['med_name_id']
            },
            'list_fdb_rmigc1_medid_gcnseqno_link': {
                sourceDataFilePath: this.sourceFolder + '/RMIGC1_MEDID_GCNSEQNO_LINK',
                sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_MEDICATION_CONCEPTS_ANSI.SQL',
                sourceReadOptions: {
                    separator: '|',
                },
                dupKey: ['medid', 'gcn_seqno']
            }
        };
        // Get tables from command line if specified
        const grunt = require('grunt');
        const tablesArg = grunt.option('tables');
        this.tablesToProcess = tablesArg ? tablesArg.split(',') : null;
    }
    static taskList = {};
    static register_tasks(task) {
        let t = new task();
        this.taskList[t.constructor.name] = t;
    }
    static get_task(task) {
        if (Object.keys(this.taskList).includes(task))
            return this.taskList[task];
    }

    static getAllTasks() {
        return this.taskList;
    }

    async run(init) {
        super.run(init);
        let promises = [];

        // Get the table name from initOptions
        const tableName = init.tables;

        // If no table specified, run all tables
        if (!tableName) {
            console.log("No specific table specified, running all tables");
            await this.runAll();
            return;
        }

        // Check if it's a registered task first
        let task = FDBImport.get_task(tableName);
        if (task) {
            console.log("Processing registered task:", tableName);
            promises.push(task.init(tableName, false));
        } 
        // Then check if it's in csonList
        else if (this.csonList.hasOwnProperty(tableName)) {
            console.log("Processing table from csonList:", tableName);
            const tableConfig = this.csonList[tableName];
            const { table, columns, primaryKey, foreignKeys } = tableConfig;
            promises.push(this.init(tableName, tableConfig, this.outputFile));
        } else {
            throw new Error(`No configuration found for table: ${tableName}`);
        }

        await Promise.all(promises);
        console.log("All tasks completed");
    }

    async runAll() {
        for (let name of Object.keys(this.csonList)) {
            await this.init(name, this.csonList[name]);
        }
        for (let [name, task] of Object.entries(FDBImport.getAllTasks())) {
            await task.init(name, false);
        }
    }

    async init(name, taskObj) {
        if (!taskObj && this.overrideTaskObj && Object.keys(this.overrideTaskObj).length > 0)
            taskObj = this.overrideTaskObj;
        let fileName = name;
        console.log(`Initializing ${fileName} import...`);
        console.log(`Source: ${taskObj.sourceDataFilePath}`);
        const actualOutputFileForTable = path.join(__dirname, '../../base/post/fdb', `0000-full-${name}.sql`);
        console.log(`Output for ${name}: ${actualOutputFileForTable}`);
        console.log("Getting Headers...");
        let headerArray = this.getHeaders(name, taskObj);
        console.log("Creating Field Def...");
        this.fieldDefinitions = this.createFieldsObjArray(headerArray, name);
        if (this.fieldDefinitions.length == 0) {
            console.log("No fields defined for " + fileName);
            throw new Error("No fields defined for " + fileName);
        }
        let headerString = headerArray.join('|') + '\n';
        let firstLine = await this.readFirstLine(taskObj.sourceDataFilePath);
        if (firstLine === headerString.trim()) {
            console.log("Header already exists in " + fileName);
        } else {
            console.log("Appending Headers...");
            await this.appendHeader(headerString, taskObj);
            console.log("headerString Appended to start for: ", fileName);
        }
        let objArray = await this.getJsonObj(fileName, taskObj.sourceDataFilePath, taskObj.sourceReadOptions, taskObj.dupKey);
        if (objArray.length == 0) {
            throw new Error("No data found for " + fileName);
        }
        console.log("Json object array created for:", fileName);
        await this.generateSql(objArray, fileName);
        console.log(fileName + " DONE");
    }

    async readFirstLine(filePath) {
        return new Promise((resolve, reject) => {
            let readStream = fs.createReadStream(filePath, {
                encoding: 'utf8'
            });
            let firstLine = '';

            readStream.on('data', (chunk) => {
                firstLine += chunk;
                let lines = firstLine.split('\n');
                if (lines.length > 0) {
                    readStream.destroy();
                    resolve(lines[0]);
                }
            });

            readStream.on('error', (err) => {
                reject(err);
            });

            readStream.on('end', () => {
                if (firstLine === '') {
                    reject(new Error("File is empty or unreadable"));
                }
            });
        });
    }

    async appendHeader(headerString, taskObj) {
        let dataArray = [headerString];
        dataArray = await this.smallerFile(taskObj.sourceDataFilePath, dataArray);
        console.log("write starting");
        let writeStream = fs.createWriteStream(taskObj.sourceDataFilePath, {
            flags: 'w'
        });
        console.log("write stream Created and writing");
        for (let line of dataArray) {
            writeStream.write(line);
        }
        console.log("write stream ending");
        writeStream.end();
        await new Promise((resolve) => writeStream.on('finish', resolve));
    }

    getHeaders(name, taskObj) {
        try {
            let contents = this.readFileSync(taskObj.sourceHeaderFilePath);
            let sourceName = taskObj.sourceDataFilePath.split("/").pop();
            let startIndex = contents.indexOf("CREATE TABLE " + sourceName);
            let endIndex = contents.indexOf(";", startIndex);
            if (startIndex == -1 || endIndex == -1) {
                throw new Error(`For ${name}, startIndex or endIndex was not found in header file \n${taskObj.sourceHeaderFilePath}
                searching for: ${"CREATE TABLE " + sourceName}`);
            }
            let headerString = contents.substring(startIndex, endIndex + 1);
            let headerArray = this.extractColumnNames(headerString);
            if (headerArray.length == 0) {
                throw new Error(`No columns found for ${name} in header: \n${headerString}`);
            }
            return headerArray.map(e => e.toLowerCase());
        } catch (error) {
            throw new Error(error);
        }
    }
    extractColumnNames(sqlStatement) {
        // Regular expression to match column definitions in the SQL statement
        const columnRegex = /(\w+)\s+(?:NUMERIC|VARCHAR|DATE)/gi;
        let match;
        const columns = [];
        // Extract all matches from the SQL string
        while ((match = columnRegex.exec(sqlStatement)) !== null) {
            columns.push(match[1]);
        }
        return columns;
    }
    createFieldsObjArray(headerArray, name) {
        let jsonData = this.readCson(name);
        return this.convertToViewFormat(jsonData.fields, headerArray);
    }
    convertToViewFormat(obj, headerArray) {
        const result = [];
        for (let header of headerArray) {
            const viewItem = {};
            let col = header.toLowerCase();
            const item = obj[col];
            if (!item) throw new Error(`Field not found: ${col}`);
            viewItem[col] = {
                view: {
                    label: item.view.label
                },
                model: {
                    type: item.model?.type || 'text'
                }
            };
            result.push(viewItem);
        }
        if (result.length !== headerArray.length) {
            throw new Error(`Result array length (${result.length}) does not match header array length (${headerArray.length})`);
        }
        return result;
    }
    async generateSql(objArray, tableName) {
        console.log(`DEBUG: generateSql started for ${tableName} with ${objArray.length} records`);
        
        // Initialize worker-related state
        this.workers = [];
        this.workerStatus = new Map();
        this.pendingChunks = [];
        this.writeQueue = [];
        this.writeInProgress = false;
        this.processedCount = 0;
        this.startTime = Date.now();
        this.lastProgressUpdate = Date.now();
        this.UPDATE_INTERVAL = 1000;
        this.BATCH_SIZE = 50000;
        this.CHUNK_SIZE = 10000;
        this.numWorkers = Math.min(os.cpus().length, 4);
        this.activeWorkers = 0;
        this.sequenceCounters = {};
        this.buffers = {};
        this.currentTableName = tableName;
        this.totalRecords = objArray.length;
        this.maxConcurrentWorkers = this.numWorkers;
        this.lastMemCheck = Date.now();

        // Define SQL output file with correct path
        const outputFile = path.join(__dirname, '../../base/post/fdb', `0000-full-${tableName}.sql`);
        
        // Ensure the post directory exists
        const postDir = path.join(__dirname, '../../base/post/fdb');
        if (!fs.existsSync(postDir)) {
            fs.mkdirSync(postDir, { recursive: true });
        }

        // Create write stream
        const streamOptions = {
            flags: 'w',
            highWaterMark: this.WRITE_BUFFER_SIZE
        };
        this.writeStream = fs.createWriteStream(outputFile, streamOptions);
        this.writeStream.on('error', (err) => {
            process.stdout.write(`\rMain: Error writing to ${outputFile}: ${err}\n`);
        });

        console.log(`DEBUG: Write stream created for ${outputFile}`);

        // Get JSON data and field definitions from CSON
        const jsonData = this.readCson(tableName);
        const columnData = Object.keys(jsonData.fields);
        
        // Ensure fieldDefinitions is an array
        const fieldDefinitionsArray = Array.isArray(this.fieldDefinitions) ? 
            this.fieldDefinitions : 
            Object.entries(this.fieldDefinitions).map(([key, value]) => ({ [key]: value }));
        
        console.log(`DEBUG: JSON data read with ${columnData.length} columns`);
        
        // Write initial setup
        await safeWrite(this.writeStream, 
            `-- SQL Generation started: ${new Date().toISOString()}
-- Table: ${tableName}
-- Total records: ${this.totalRecords}

-- Initial configuration
BEGIN;
SET session_replication_role = replica;
SET maintenance_work_mem = '1GB';
SET synchronous_commit = off;
SET work_mem = '2GB';
SET temp_buffers = '1GB';
SET statement_timeout = 0;

-- Clean table
TRUNCATE TABLE form_${tableName};
COMMIT;

-- Beginning data import in batches of ${this.BATCH_SIZE} records
-- Each batch will have its own transaction
`);

        // Initialize workers
        process.stdout.write(`\rMain: Starting processing of ${this.totalRecords.toLocaleString()} records with ${Math.ceil(this.totalRecords / this.CHUNK_SIZE)} chunks\n`);
        process.stdout.write(`\rMain: Using ${this.numWorkers} workers (out of ${os.cpus().length} CPUs)\n`);

        // Create workers
        for (let i = 0; i < this.numWorkers; i++) {
            const worker = new Worker(`${__dirname}/fdb_worker.js`, {
                workerData: {
                    jsonData,
                    columnData,
                    fieldDefinitions: fieldDefinitionsArray,
                    csonList: this.csonList
                }
            });

            worker.on('message', result => {
                this.handleWorkerResult(result);
            });
            
            worker.on('error', error => {
                process.stdout.write(`\rMain: Worker ${worker.threadId} error: ${error.message}\n`);
                this.workerStatus.set(worker.threadId, false);
                this.activeWorkers--;
            });

            this.workers.push(worker);
            this.workerStatus.set(worker.threadId, false);
        }

        console.log(`DEBUG: All workers created. Creating chunks from data array`);

        // Split data into chunks and add to pending chunks
        let chunkCount = 0;
        for (let i = 0; i < objArray.length; i += this.CHUNK_SIZE) {
            const chunk = objArray.slice(i, i + this.CHUNK_SIZE);
            this.pendingChunks.push(chunk);
            chunkCount++;
            if (chunkCount <= 2 || chunkCount % 10 === 0) {
                console.log(`DEBUG: Created chunk ${chunkCount} with ${chunk.length} records`);
            }
        }

        console.log(`DEBUG: Created ${this.pendingChunks.length} chunks. Starting processChunks`);

        // Start processing chunks
        this.processChunks();

        // Wait for all processing to complete
        let statusInterval = setInterval(() => {
//            console.log(`DEBUG: Status - Pending chunks: ${this.pendingChunks.length}, Active workers: ${this.activeWorkers}, Write queue: ${this.writeQueue.length}, Processed: ${this.processedCount}, Buffer size: ${this.buffers[this.currentTableName] ? this.buffers[this.currentTableName].length : 0}`);
            
            // Make sure the write queue gets processed even when no new chunks are being processed
            if (this.writeQueue.length > 0 && !this.writeInProgress) {
//                console.log(`DEBUG: Manually triggering processWriteQueue from status interval - queue length: ${this.writeQueue.length}`);
                this.processWriteQueue();
            }
        }, 5000);

        while (this.pendingChunks.length > 0 || this.activeWorkers > 0 || this.writeQueue.length > 0 || (this.buffers[this.currentTableName] && this.buffers[this.currentTableName].length > 0)) {
            // If all chunks processed but we still have data in buffer, move it to write queue
            if (this.pendingChunks.length === 0 && this.activeWorkers === 0 && 
                this.buffers[this.currentTableName] && this.buffers[this.currentTableName].length > 0) {
                
                // Add any remaining items in buffer to the write queue
                const batch = this.buffers[this.currentTableName];
                this.buffers[this.currentTableName] = [];
                this.writeQueue.push({
                    tableName: this.currentTableName,
                    values: batch
                });
            }
            
            // ADDED: Ensure write queue is processed even when all workers are done
            if ((this.pendingChunks.length === 0 && this.activeWorkers === 0) && 
                this.writeQueue.length > 0 && !this.writeInProgress) {
                await this.processWriteQueue();
            }
            
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        clearInterval(statusInterval);
        console.log(`DEBUG: All processing complete, checking for any remaining data`);
        
        // Make sure any remaining buffer is processed
        if (this.buffers[this.currentTableName] && this.buffers[this.currentTableName].length > 0) {
            console.log(`DEBUG: Found ${this.buffers[this.currentTableName].length} remaining records in buffer`);
            const batch = this.buffers[this.currentTableName];
            this.buffers[this.currentTableName] = [];
            this.writeQueue.push({
                tableName: this.currentTableName,
                values: batch
            });
            await this.processWriteQueue();
        }
        
        console.log(`DEBUG: All processing complete, writing final SQL`);
        
        this.writeInProgress = false;
        // Process any items still in the write queue and WAIT for it to complete
        if (this.writeQueue.length > 0) {
            console.log(`DEBUG: FORCE PROCESS - Processing ${this.writeQueue.length} remaining items in write queue`);
            await this.processWriteQueue();
        }
        
        // Double check that all items are processed
        while (this.writeQueue.length > 0 || this.writeInProgress) {
            console.log(`DEBUG: Still waiting for writes to complete. Queue: ${this.writeQueue.length}, Write in progress: ${this.writeInProgress}`);
            await new Promise(resolve => setTimeout(resolve, 500));
        }
        
        console.log(`DEBUG: All writes confirmed complete. Finalizing file.`);
        
        // Write final sequence update and close stream
        await safeWrite(this.writeStream, `
-- Import complete: ${new Date().toISOString()}
-- Total records processed: ${this.processedCount}
-- Duration: ${((Date.now() - this.startTime) / 1000).toFixed(2)} seconds

-- Reset and update sequence to next available value
BEGIN;
ALTER SEQUENCE form_${tableName}_id_seq RESTART WITH ${(this.sequenceCounters[tableName] || 0) + 1};
SET session_replication_role = DEFAULT;
COMMIT;
`);

        console.log(`DEBUG: Closing write stream...`);
        await new Promise(resolve => this.writeStream.end(resolve));
        console.log(`DEBUG: Write stream closed`);

        const duration = (Date.now() - this.startTime) / 1000;
        process.stdout.write(`\r\nMain: Completed processing ${this.processedCount.toLocaleString()} records in ${duration.toFixed(2)} seconds\n`);
        process.stdout.write(`\rMain: Average speed: ${Math.round(this.processedCount / duration).toLocaleString()} records/second\n`);
    }

    processChunks() {
        
        // Check memory usage and apply backpressure if needed
        const now = Date.now();
        const memoryUsage = process.memoryUsage();
        const heapUsedGB = Math.round(memoryUsage.heapUsed / 1024 / 1024 / 1024 * 100) / 100;

        if (now - this.lastMemCheck > this.MEM_CHECK_INTERVAL) {
            if (heapUsedGB > 2.5) { // 2.5GB threshold
                process.stdout.write(`\rMain: High memory usage detected (${heapUsedGB}GB), pausing for cleanup...\n`);
                setTimeout(() => this.processChunks(), 5000); // This is the pause
                // return; // TEMP: Comment out to prevent pause for testing
            }
            this.lastMemCheck = now;
        }

        // FIXED: Changed backpressure logic to allow chunks to be processed
        // We want to allow at least some chunks to be processed regardless of pending count
        const maxPendingInProgress = this.numWorkers * 2;
        if (this.activeWorkers >= this.numWorkers) {
            setTimeout(() => this.processChunks(), 100);
            return;
        }

        // Start multiple workers if we have capacity
        const availableWorkers = this.numWorkers - this.activeWorkers;
        const workersToStart = Math.min(availableWorkers, this.pendingChunks.length);
        
        
        if (workersToStart > 0) {
            const availableWorkersList = this.workers.filter(w => !this.workerStatus.get(w.threadId));
            
            for (let i = 0; i < workersToStart && i < availableWorkersList.length; i++) {
                const worker = availableWorkersList[i];
                const chunk = this.pendingChunks.shift();
                if (!chunk) {
                    break;
                }

                this.workerStatus.set(worker.threadId, true);
                this.activeWorkers++;
                
                try {
                    worker.postMessage({
                        chunk,
                        tableName: this.currentTableName
                    });
                } catch (e) {
                    process.stdout.write(`\rMain: Error sending message to worker: ${e.message}\n`);
                    this.workerStatus.set(worker.threadId, false);
                    this.activeWorkers--;
                    this.pendingChunks.unshift(chunk);
                    break;
                }
            }
            this.updateProgress();
        } 
        if (this.pendingChunks.length > 0 || this.activeWorkers > 0) {
            setTimeout(() => this.processChunks(), 100);
        }
    }

    handleWorkerResult(result) {
        
        const workerId = result.workerId;
        this.workerStatus.set(workerId, false);
        this.activeWorkers--;

        if (result.error) {
            process.stdout.write(`\rMain: Worker ${workerId} error: ${result.error}\n`);
            return;
        }

        const { values, processedCount } = result;
        
        if (values && values.length > 0) {
            // Process all values at once to maintain sequence integrity
            const rewrittenValues = this.processValues(values);
            
            if (rewrittenValues.length > 0) {
                // Accumulate values in buffer before writing
                if (!this.buffers[this.currentTableName]) {
                    this.buffers[this.currentTableName] = [];
                }
                this.buffers[this.currentTableName].push(...rewrittenValues);

                // When we have enough records, queue them for writing
                while (this.buffers[this.currentTableName].length >= this.BATCH_SIZE) {
                    const batch = this.buffers[this.currentTableName].splice(0, this.BATCH_SIZE);
                    this.writeQueue.push({
                        tableName: this.currentTableName,
                        values: batch
                    });
                }
                
                // If we're getting close to the end, also process smaller batches
                const remainingRecords = this.totalRecords - this.processedCount;
                if (remainingRecords <= this.BATCH_SIZE * 2 && this.buffers[this.currentTableName].length > 1000) {
                    const smallBatch = this.buffers[this.currentTableName];
                    this.buffers[this.currentTableName] = [];
                    this.writeQueue.push({
                        tableName: this.currentTableName,
                        values: smallBatch
                    });
                }
            }
            
            // Clear arrays to help with memory
            values.length = 0;
            rewrittenValues.length = 0;
        }

        this.processedCount += processedCount;
        this.updateProgress();

        if (this.writeInProgress && this.writeQueue.length > 0) {
            // The write has been "in progress" but if there are no active workers, it might be stuck
            if (this.activeWorkers === 0) {
                console.log(`DEBUG: Write appears to be stuck. Resetting writeInProgress flag.`);
                this.writeInProgress = false;
            }
        }

        // Schedule processing of the write queue if appropriate
        if (!this.writeInProgress && this.writeQueue.length > 0) {
            // Use setImmediate to ensure we don't block processing
            setImmediate(() => this.processWriteQueue());
        } else {
        }

        // Schedule more chunk processing
        setImmediate(() => this.processChunks());
    }
    
    // Process values method - now only called during the write process
    processValues(values) {
        
        if (!this.sequenceCounters[this.currentTableName]) {
            this.sequenceCounters[this.currentTableName] = 0;
        }
        
        const processed = values.map(value => {
            this.sequenceCounters[this.currentTableName]++;
            return value.replace('ID_PLACEHOLDER', this.sequenceCounters[this.currentTableName]);
        });
        
        return processed;
    }

    updateProgress() {
        const now = Date.now();
        if (now - this.lastProgressUpdate < this.UPDATE_INTERVAL) {
            return;
        }
        
        this.lastProgressUpdate = now;
        
        const elapsed = (now - this.startTime) / 1000;
        const speed = Math.round(this.processedCount / elapsed);
        
        // Calculate ETA
        let etaStr = 'calculating...';
        if (this.processedCount > 0 && this.totalRecords > 0) {
            const remainingRecords = this.totalRecords - this.processedCount;
            const etaSeconds = remainingRecords / speed;
            
            if (etaSeconds < 60) {
                etaStr = `${Math.round(etaSeconds)}s`;
            } else if (etaSeconds < 3600) {
                etaStr = `${Math.floor(etaSeconds / 60)}m ${Math.round(etaSeconds % 60)}s`;
            } else {
                etaStr = `${Math.floor(etaSeconds / 3600)}h ${Math.floor((etaSeconds % 3600) / 60)}m`;
            }
        }
        
        // Get worker states
        const workerStates = Array.from(this.workerStatus.entries())
            .map(([id, active]) => active ? `${id}:✓` : `${id}:✗`)
            .join(' ');
        
        // Clear the line and write the progress
        process.stdout.write('\x1B[2K\r'); // Clear the entire line
        process.stdout.write(`Processed: ${this.processedCount.toLocaleString()} at ${speed.toLocaleString()}/sec | ` +
            `ETA: ${etaStr} | Workers: ${this.activeWorkers}/${this.numWorkers} ${workerStates}`);
    }

    async processWriteQueue() {
        
        // If the write is already marked as in progress, check if we might be stuck
        if (this.writeInProgress) {
            if (this.activeWorkers === 0 && this.pendingChunks.length === 0) {
                this.writeInProgress = false;
            } else {
                return;
            }
        }
        
        if (this.writeQueue.length === 0) {
            console.log(`DEBUG: Write queue is empty, nothing to process`);
            // Check if there's anything in the buffer to move to queue
            if (this.buffers[this.currentTableName] && this.buffers[this.currentTableName].length > 0) {
                const batch = this.buffers[this.currentTableName];
                this.buffers[this.currentTableName] = [];
                this.writeQueue.push({
                    tableName: this.currentTableName,
                    values: batch
                });
                // Recursively call to process this batch
                return this.processWriteQueue();
            }
            return;
        }
        
        // Process multiple batches from the queue, one after another
        let processedCount = 0;
        const startTime = Date.now();
        
        this.writeInProgress = true;
        console.log(`DEBUG: Setting writeInProgress = true`);
        
        try {
            // Process up to 5 batches at a time to allow for interleaving with other processing
            let batchesToProcess = Math.min(5, this.writeQueue.length);
            
            for (let i = 0; i < batchesToProcess; i++) {
                if (this.writeQueue.length === 0) break;
                
                const { tableName, values } = this.writeQueue.shift();
                
                console.log(`DEBUG: Processing write queue item with ${values ? values.length : 0} values for table ${tableName}. Remaining queue: ${this.writeQueue.length}`);
                
                try {
                    // Get the column names from field definitions
                    let columns = this.fieldDefinitions.map(fieldDef => Object.keys(fieldDef)[0]).join(', ');
                    
                    if (this.csonList[tableName]?.formatted_ndc === true) {
                        columns += ', formatted_ndc';
                    }
                    
                    const columnsWithIdAndTimestamp = `id, code, ${columns}, auto_name, created_on, created_by`;
//                    console.log(`DEBUG: SQL columns: ${columnsWithIdAndTimestamp}`);
                    
                    const sql = [
                        'BEGIN;',
                        `INSERT INTO form_${tableName} (${columnsWithIdAndTimestamp}) VALUES`,
                        values.join(',\n'),
                        ';',
                        'COMMIT;\n'
                    ].join('\n');
    
                    await safeWrite(this.writeStream, sql);
                    processedCount += values.length;
                    

                    const progress = ((this.sequenceCounters[tableName] || 0) / this.totalRecords * 100).toFixed(1);
                    process.stdout.write(`\rMain: Table progress: ~${progress}% (${this.sequenceCounters[tableName].toLocaleString()} records)`);

                } catch (error) {
                    console.log(`DEBUG: Error writing to stream: ${error.message}`);
                    process.stdout.write(`\rMain: Error writing to stream: ${error.message}\n`);
                    try {
                        await safeWrite(this.writeStream, 'ROLLBACK;\n\n');
                    } catch (rollbackError) {
                        process.stdout.write(`\rMain: Error rolling back transaction: ${rollbackError.message}\n`);
                    }
                    // If there was an error, break out of the loop
                    break;
                }
                
                // Short pause between batches to allow other operations
                await new Promise(resolve => setTimeout(resolve, 10));
            }
        } finally {
            this.writeInProgress = false;
        }
        
        const duration = (Date.now() - startTime) / 1000;
        console.log(`DEBUG: Processed ${processedCount} records in ${duration.toFixed(2)}s (${Math.min(5, this.writeQueue.length + processedCount / (this.BATCH_SIZE || 10000))} batches)`);
        
        // Check if there's more to process
        if (this.writeQueue.length > 0) {
            return await this.processWriteQueue();
        } else if (this.buffers[this.currentTableName] && this.buffers[this.currentTableName].length > 0) {
            const batch = this.buffers[this.currentTableName];
            this.buffers[this.currentTableName] = [];
            this.writeQueue.push({
                tableName: this.currentTableName,
                values: batch
            });
            return await this.processWriteQueue();
        } else {
            console.log(`DEBUG: Write queue and buffer are now empty`);
        }
    }

    async getJsonObj(tableName, filePath, options = {
        separator: '|',
        quote: "{"
    }, dupKey) {
        const objects = [];
        // Use the dupKey provided in the constructor or passed dynamically
        const keyFields = dupKey || (this.overrideTaskObj && this.overrideTaskObj.dupKey);

        await new Promise((resolve, reject) => { // Added reject
            fs.createReadStream(filePath)
                .pipe(csv(options))
                .on('data', async (row) => {
                    const obj = {};
                    try {
                        // Extract and format data for each field defined in the schema
                        for (let index in this.fieldDefinitions) {
                            let fieldDef = this.fieldDefinitions[index];
                            const field = Object.keys(fieldDef)[0];
                            fieldDef = Object.values(fieldDef)[0];
                            const value = row[field] ? row[field].trim() : '';

                            if (fieldDef.model && fieldDef.model.type === 'int') {
                                obj[field] = value ? parseInt(value, 10) : null;
                                if (isNaN(obj[field])) obj[field] = null; // Handle NaN
                            } else if (fieldDef.model && fieldDef.model.type === 'decimal') {
                                obj[field] = value ? parseFloat(value) : null;
                                if (isNaN(obj[field])) obj[field] = null; // Handle NaN
                            } else if (fieldDef.model && fieldDef.model.type === 'date') {
                                let val = moment(value).format('YYYY-MM-DD');
                                obj[field] = (val === 'Invalid date') ? null : val;
                            } else {
                                obj[field] = value;
                            }
                        }

                        // Generate the composite 'code' based on dupKey fields
                        if (keyFields && Array.isArray(keyFields) && keyFields.length > 0) {
                            // Ensure date field is formatted correctly for the key
                            const keyValues = keyFields.map(field => {
                                if (field === 'price_effective_dt') {
                                    // Use the already formatted date from obj[field]
                                    return obj[field] || ''; // Use empty string if null
                                }
                                return obj[field] !== null && obj[field] !== undefined ? obj[field].toString() : '';
                            });
                            obj['code'] = keyValues.join('-');
                        } else {
                             // Fallback or error if dupKey isn't defined correctly
                             console.warn(`Warning: dupKey not properly defined for ${tableName}. Code will be missing.`);
                             obj['code'] = null;
                        }

                        // Add auto_name based on the code (or other logic if needed)
                        // obj['auto_name'] = form_auto_name({ fields: this.fieldDefinitions }, obj); // REMOVE THIS LINE

                        objects.push(obj);

                    } catch (error) {
                        console.error(`Error processing row for list_fdb_ndc_price:`, row);
                        console.error(error);
                        // Skip problematic row or decide on error handling
                        // reject(new Error(`Error processing row: ${error.message}`)); // Option: stop processing
                    }
                })
                .on('error', (err) => { // Handle stream errors
                    console.error(`Error reading file ${filePath}:`, err);
                    reject(err);
                })
                .on('end', resolve);
        });
        return objects;
    }

    formatNDC(ndc) {
        if (!ndc) return null;
        ndc = ndc.replace(/[-\s]/g, '');

        try {
            // All NDCs from FDB are 11 digits, format as 5-4-2
            if (ndc.length === 11) {
                return `${ndc.slice(0, 5)}-${ndc.slice(5, 9)}-${ndc.slice(9)}`;
            }

            return ndc;
        } catch (error) {
            return ndc;
        }
    }

    async smallerFile(file, dataArray) {
        console.log("Splitting file");
        await splitFile.splitFileBySize(file, 1024 * 1024 * 1) // 1MB
            .then(async (chunks) => {
                for (let idx = 0; idx < chunks.length; idx++) {
                    await new Promise((resolve, reject) => {
                        let readStream = fs.createReadStream(chunks[idx], {
                            encoding: 'utf8'
                        });

                        readStream.on('data', (chunkData) => {
                            dataArray.push(chunkData);
                        });

                        readStream.on('end', async () => {
                            try {
                                await fs.promises.unlink(chunks[idx]);
                                resolve();
                            } catch (err) {
                                reject(err);
                            }
                        });

                        readStream.on('error', (err) => {
                            console.error("Error reading chunk", idx, err);
                            reject(err);
                        });
                    });
                }
            });
        console.log("Split file completed");
        return dataArray;
    }

    async initFullFileWriteStream(tableName) {
        const fullFilePath = `${this.baseFolder}/post/0000-full-${tableName}.sql`;
        
        // Create the directory if it doesn't exist
        await fs.promises.mkdir(path.dirname(fullFilePath), { recursive: true });
        
        // Create a write stream for the full file
        const writeStream = fs.createWriteStream(fullFilePath);
        
        // Write the header
        const timestamp = new Date().toISOString();
        writeStream.write(`-- SQL Generation started: ${timestamp}\n`);
        writeStream.write(`-- Table: ${tableName}\n`);
        writeStream.write(`-- Total records: ${this.totalRecords}\n\n`);
        
        // Write initial configuration
        writeStream.write(`-- Initial configuration\n`);
        writeStream.write(`BEGIN;\n`);
        writeStream.write(`SET session_replication_role = replica;\n`);
        writeStream.write(`SET maintenance_work_mem = '1GB';\n`); // Reduced from 4GB
        writeStream.write(`SET synchronous_commit = off;\n`);
        writeStream.write(`SET work_mem = '2GB';\n`); // Reduced from 8GB
        writeStream.write(`SET temp_buffers = '1GB';\n`); // Reduced from 2GB
        writeStream.write(`SET statement_timeout = 0;\n\n`);
        
        // Clean table - use TRUNCATE instead of DELETE for better performance
        writeStream.write(`-- Clean table\n`);
        writeStream.write(`TRUNCATE TABLE form_${tableName};\n`);
        writeStream.write(`COMMIT;\n\n`);
        
        // Write batch header
        writeStream.write(`-- Beginning data import in batches of ${this.BATCH_SIZE} records\n`);
        writeStream.write(`-- Each batch will have its own transaction\n`);
        writeStream.write(`BEGIN;\n`);
        
        return writeStream;
    }

    initializeWorkers(jsonData, columnData) {

        // Create workers
        for (let i = 0; i < this.numWorkers; i++) {
            const worker = new Worker(`${__dirname}/fdb_worker.js`, {
                workerData: {
                    workerId: i,
                    jsonData,
                    columnData,
                    fieldDefinitions: this.fieldDefinitions,
                    csonList: this.csonList // Pass csonList to worker
                }
            });

            console.log(`DEBUG: Setting up event handlers for worker ${worker.threadId}`);
            worker.on('message', result => {
                this.handleWorkerResult(result);
            });
            
            worker.on('error', error => {
                process.stdout.write(`\rMain: Worker ${worker.threadId} error: ${error.message}\n`);
                this.workerStatus.set(worker.threadId, false);
                this.activeWorkers--;
            });

            this.workers.push(worker);
            this.workerStatus.set(worker.threadId, false);  // Initialize as not busy
        }
    }

    handleWorkerError(workerId) {
        if (!this.restartedWorkers) this.restartedWorkers = 0;
        if (!this.MAX_WORKER_RESTARTS) this.MAX_WORKER_RESTARTS = 3;
        
        if (this.restartedWorkers < this.MAX_WORKER_RESTARTS) {
            process.stdout.write(`\rMain: Restarting worker ${workerId}\n`);
            const jsonData = this.readCson(this.currentTableName);
            const columnData = Object.keys(jsonData.fields);
            
            this.workers[workerId] = new Worker(`${__dirname}/fdb_worker.js`, {
                workerData: {
                    workerId,
                    jsonData,
                    columnData,
                    fieldDefinitions: this.fieldDefinitions,
                    csonList: this.csonList // Add csonList to restarted worker
                }
            });
            
            this.workerStatus.set(workerId, false);
            this.activeWorkers--;
            this.restartedWorkers++;
        } else {
            process.stdout.write(`\rMain: Too many worker restarts (${this.MAX_WORKER_RESTARTS}). Giving up on worker ${workerId}\n`);
        }
    }

    async terminateWorkers() {
        process.stdout.write(`\rMain: Preparing to terminate workers...\n`);
        
        // First try to gracefully signal workers to finish their current work
        for (const worker of this.workers) {
            try {
                // Signal worker to finish up and not accept new chunks
                worker.postMessage({ finalize: true });
            } catch (err) {
                process.stdout.write(`\rMain: Unable to send finalize message to worker ${worker.threadId}: ${err}\n`);
            }
        }
        
        // Give workers some time to finish current tasks
        process.stdout.write(`\rMain: Waiting for workers to finish current tasks...\n`);
        await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds
        
        // Now proceed with termination
        process.stdout.write(`\rMain: Now terminating workers...\n`);
        
        // Use longer timeout for termination
        const terminationPromises = this.workers.map(worker => {
            return new Promise(resolve => {
                const timeout = setTimeout(() => {
                    process.stdout.write(`\rMain: Worker ${worker.threadId} termination timed out, forcing exit\n`);
                    worker.terminate();
                    resolve();
                }, 10000); // 10 seconds timeout
                
                worker.terminate().then(() => {
                    clearTimeout(timeout);
                    process.stdout.write(`\rMain: Worker ${worker.threadId} terminated gracefully\n`);
                    resolve();
                }).catch(err => {
                    process.stdout.write(`\rMain: Error terminating worker ${worker.threadId}: ${err}\n`);
                    clearTimeout(timeout);
                    resolve();
                });
            });
        });
        
        await Promise.all(terminationPromises);
        process.stdout.write(`\rMain: All workers terminated\n`);
    }

    checkMemoryUsage() {
        const memoryUsage = process.memoryUsage();
        const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
        const heapTotalMB = Math.round(memoryUsage.heapTotal / 1024 / 1024);
        const rss = Math.round(memoryUsage.rss / 1024 / 1024);
        
        process.stdout.write(`\rMemory Usage: ${heapUsedMB}MB (${Math.round(heapUsedMB/heapTotalMB*100)}%) of ${heapTotalMB}MB | RSS: ${rss}MB`);
        
        // If memory usage is high, force garbage collection and reduce concurrency
        if (heapUsedMB > this.MAX_HEAP_SIZE * 0.8) {
            process.stdout.write(`\r\nHigh memory usage detected (${heapUsedMB}MB). Adjusting parameters...\n`);
            
            // Temporarily reduce max concurrent workers
            const oldMax = this.maxConcurrentWorkers;
            this.maxConcurrentWorkers = Math.max(1, Math.floor(this.maxConcurrentWorkers * 0.7));
            
            if (oldMax !== this.maxConcurrentWorkers) {
                process.stdout.write(`\rReduced max concurrent workers from ${oldMax} to ${this.maxConcurrentWorkers}\n`);
            }
            
            // Force garbage collection if available
            if (global.gc) {
                try {
                    global.gc();
                    process.stdout.write(`\rForced garbage collection completed\n`);
                } catch (e) {
                    process.stdout.write(`\rFailed to perform garbage collection: ${e}\n`);
                }
            }
        } else if (this.maxConcurrentWorkers < this.numWorkers && heapUsedMB < this.MAX_HEAP_SIZE * 0.5) {
            // If memory usage is low, gradually increase concurrency back
            const oldMax = this.maxConcurrentWorkers;
            this.maxConcurrentWorkers = Math.min(this.numWorkers, this.maxConcurrentWorkers + 1);
            
            if (oldMax !== this.maxConcurrentWorkers) {
                process.stdout.write(`\r\nIncreased max concurrent workers from ${oldMax} to ${this.maxConcurrentWorkers}\n`);
            }
        }
    }

    checkWorkerTimeouts() {
        const now = Date.now();
        this.workerStatus.forEach((isActive, workerId) => {
            if (isActive) {
                const startTime = this.workerTimeouts.get(workerId) || now;
                const duration = now - startTime;
                
                if (duration > this.WORKER_TIMEOUT) {
                    process.stdout.write(`\rWorker ${workerId} has been active for ${Math.round(duration/1000)}s, which exceeds timeout of ${this.WORKER_TIMEOUT/1000}s\n`);
                    
                    if (this.restartedWorkers < this.MAX_WORKER_RESTARTS) {
                        process.stdout.write(`\rRestarting worker ${workerId}\n`);
                        
                        // Force terminate and recreate the worker
                        const jsonData = this.readCson(this.currentTableName);
                        const columnData = Object.keys(jsonData.fields);
                        
                        this.workers[workerId].terminate().then(() => {
                            this.workers[workerId] = this.createWorker(workerId, jsonData, columnData);
                            this.workerStatus.set(workerId, false);
                            this.activeWorkers--;
                            this.restartedWorkers++;
                        }).catch(err => {
                            process.stdout.write(`\rMain: Error during worker termination: ${err}\n`);
                        });
                        
                        // Remove the timeout entry
                        this.workerTimeouts.delete(workerId);
                    } else {
                        process.stdout.write(`\rMain: Too many worker restarts (${this.MAX_WORKER_RESTARTS}). Leaving worker ${workerId} running.\n`);
                    }
                }
            }
        });
    }

    isProcessingComplete() {
        // Add a check to ensure we've processed all records
        const allRecordsProcessed = this.processedCount >= this.totalRecords;
        
        // Check if there are active workers still processing
        const activeWorkersCount = Array.from(this.workerStatus.values()).filter(status => status).length;
        
        // Log detailed information if we're close to completion
        if (this.pendingChunks.length === 0 && activeWorkersCount === 0 && !allRecordsProcessed) {
            process.stdout.write(`\rMain: WARNING - Processing appears complete but only ${this.processedCount.toLocaleString()}/${this.totalRecords.toLocaleString()} records processed\n`);
        }
        
        // Add a safety delay before declaring completion
        if (!this.completionCheckStarted && 
            this.pendingChunks.length === 0 && 
            activeWorkersCount === 0 && 
            this.processedCount >= this.totalRecords) {
            
            process.stdout.write(`\rMain: All records processed. Starting completion check to ensure workers are done...\n`);
            this.completionCheckStarted = Date.now();
            return false; // Don't immediately declare completion
        }
        
        // If completion check has started, wait for a short period to ensure workers are truly done
        if (this.completionCheckStarted) {
            const timeElapsed = Date.now() - this.completionCheckStarted;
            if (timeElapsed < 2000) { // Wait 2 seconds
                return false; // Still waiting
            } else {
                process.stdout.write(`\rMain: Completion check passed after ${timeElapsed}ms. Finalizing process...\n`);
                return true; // Now we can declare completion
            }
        }
        
        return (
            this.pendingChunks.length === 0 &&
            activeWorkersCount === 0 &&
            this.writeQueue.length === 0 &&
            !this.writeInProgress &&
            allRecordsProcessed
        );
    }

    shouldStartCompletion() {
        return (
            this.pendingChunks.length === 0 &&
            this.activeWorkers <= Math.ceil(this.numWorkers / 2) &&
            this.processedCount >= this.totalRecords * 0.95
        );
    }

    // Add this function to detect processing stalls
    checkProgressStalled() {
        const now = Date.now();
        if (now - this.lastProgressCheck >= this.PROGRESS_CHECK_INTERVAL) {
            this.lastProgressCheck = now;
            
            // If processedCount hasn't changed since last check, processing might be stalled
            if (this.processedCount === this.lastProcessedCount && !this.isProcessingComplete()) {
                process.stdout.write(`\r\n⚠️ WARNING: Processing appears stalled! No progress in the last ${this.PROGRESS_CHECK_INTERVAL/1000} seconds\n`);
                process.stdout.write(`\rAttempting recovery...\n`);
                
                // Force GC if available
                if (global.gc) {
                    try {
                        global.gc();
                        process.stdout.write(`\rMain: Forced garbage collection during recovery\n`);
                    } catch (e) {
                        process.stdout.write(`\rMain: Failed to perform garbage collection: ${e}\n`);
                    }
                }
                
                // Reset any workers that have been busy too long
                this.workerStatus.forEach((isActive, workerId) => {
                    if (isActive) {
                        const startTime = this.workerTimeouts.get(workerId) || now;
                        const duration = now - startTime;
                        
                        if (duration > 5000) { // 5 seconds is probably too long for a chunk
                            process.stdout.write(`\rMain: Resetting worker ${workerId} that's been active for ${Math.round(duration/1000)}s\n`);
                            
                            // Recreate the worker
                            const jsonData = this.readCson(this.currentTableName);
                            const columnData = Object.keys(jsonData.fields);
                            
                            this.workers[workerId].terminate().then(() => {
                                this.workers[workerId] = this.createWorker(workerId, jsonData, columnData);
                                this.workerStatus.set(workerId, false);
                                this.activeWorkers--;
                                this.restartedWorkers++;
                            }).catch(err => {
                                process.stdout.write(`\rMain: Error during worker reset: ${err}\n`);
                            });
                        }
                    }
                });
                
                // Process any pending chunks
                if (this.pendingChunks.length > 0) {
                    this.processChunks();
                }
                
                // Process write queue if needed
                if (this.writeQueue.length > 0 && !this.writeInProgress) {
                    this.processWriteQueue();
                }
            }
            
            this.lastProcessedCount = this.processedCount;
        }
    }
}
class list_fdb_drug_brand extends FDBImport {

    constructor() {
        super();
        this.overrideTaskObj = {
            sourceDataFilePath: this.sourceFolder + '/RNDC14_NDC_MSTR',
            sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
            sourceReadOptions: {
                separator: '|',
                quote: "{"
            },
        };
    }

    createFieldsObjArray(headerArray, name) {
        // For drug_brand, we only need the 'bn' field
        return [{
            name: {
                view: {
                    label: 'Brand Name'
                },
                model: {
                    type: 'text'
                }
            },
            auto_name: {
                view: {
                    label: 'Auto Name'
                },
                model: {
                    type: 'text'
                }
            }
        }];
    }

    async init(name, taskObj) {
        if (!taskObj && this.overrideTaskObj && Object.keys(this.overrideTaskObj).length > 0)
            taskObj = this.overrideTaskObj;
        let fileName = name;
        console.log("Initializing " + fileName + " import... \n Getting Headers");
        let headerArray = this.getHeaders(name, taskObj);
        console.log("Creating Field Def...");
        this.fieldDefinitions = this.createFieldsObjArray(headerArray, name);
        if (this.fieldDefinitions.length == 0) {
            console.log("No fields defined for " + fileName);
            throw new Error("No fields defined for " + fileName);
        }
        let headerString = headerArray.join('|') + '\n';
        let firstLine = await this.readFirstLine(taskObj.sourceDataFilePath);
        if (firstLine === headerString.trim()) {
        } else {
            await this.appendHeader(headerString, taskObj);
        }
        let objArray = await this.getJsonObj(taskObj.sourceDataFilePath, taskObj.sourceReadOptions);
        if (objArray.length == 0) {
            throw new Error("No data found for " + fileName);
        }
        await this.generateSql(objArray, fileName);
        console.log(fileName + " DONE");
    }

    async getJsonObj(filePath, options = {
        separator: '|',
        quote: "{"
    }) {
        const objects = [];
        const uniqueBnSet = new Set();
        await new Promise((resolve) => {
            fs.createReadStream(filePath)
                .pipe(csv(options))
                .on('data', async (row) => {
                    try {
                        const bn = row['bn'].trim();
                        if (!uniqueBnSet.has(bn)) {
                            uniqueBnSet.add(bn);
                            const obj = {
                                code: bn,
                                name: bn,
                                auto_name: bn
                            };
                            objects.push(obj);
                        }
                    } catch (error) {
                        console.log(row);
                        throw new Error('Invalid field: ' + row['bn']);
                    }
                })
                .on('end', resolve);
        });
        return objects;
    }
}
class list_fdb_ndc_price extends FDBImport {
    constructor() {
        super();
        this.overrideTaskObj = {
            sourceDataFilePath: this.sourceFolder + '/RNP3_NDC_PRICE',
            sourceHeaderFilePath: this.sourceFolder + '/NDDF_DESCRIPTIVE_BASICS_ANSI.SQL',
            sourceReadOptions: {
                separator: '|'
            },
            // Define the composite key fields used for the 'code'
            dupKey: ['ndc', 'price_type', 'price_effective_dt']
        };
    }

    async smallerFile(file, dataArray) {
        console.log("Splitting file");
        await splitFile.splitFileBySize(file, 1024 * 1024 * 1) // 1MB
            .then(async (chunks) => {
                for (let idx = 0; idx < chunks.length; idx++) {
                    await new Promise((resolve, reject) => {
                        let readStream = fs.createReadStream(chunks[idx], {
                            encoding: 'utf8'
                        });

                        readStream.on('data', (chunkData) => {
                            dataArray.push(chunkData);
                        });

                        readStream.on('end', async () => {
                            try {
                                await fs.promises.unlink(chunks[idx]);
                                resolve();
                            } catch (err) {
                                reject(err);
                            }
                        });

                        readStream.on('error', (err) => {
                            console.error("Error reading chunk", idx, err);
                            reject(err);
                        });
                    });
                }
            });
        console.log("Split file completed");
        return dataArray;
    }

    async appendHeader(headerString, taskObj) {
        let dataArray = [headerString];
        dataArray = await this.smallerFile(taskObj.sourceDataFilePath, dataArray);
        console.log("write starting");
        let writeStream = fs.createWriteStream(taskObj.sourceDataFilePath, {
            flags: 'w'
        });
        console.log("write stream Created and writing");
        for (let line of dataArray) {
            writeStream.write(line);
        }
        console.log("write stream ending");
        writeStream.end();
        await new Promise((resolve) => writeStream.on('finish', resolve));
    }
};

FDBImport.register_tasks(list_fdb_ndc_price);
FDBImport.register_tasks(list_fdb_drug_brand);
module.exports = {
    FDBImport
};