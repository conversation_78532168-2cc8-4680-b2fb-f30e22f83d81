
"use strict";
const fs = require('fs');
const csv = require('csv-parser');
const cson = require('cson');
const moment = require("moment");
const { form_auto_name } = require('./utils/fx');
const BaseRunner = require('./base');
const puppeteer = require('puppeteer');

class CMSNPIWeekly extends BaseRunner {
    constructor() {
        super();
        this.url = 'https://download.cms.gov/nppes/NPI_Files.html';
        this.sourceFolder = this.sourceFolder + '/cms_npi_weekly/';
        this.query;
        this.database_url = null;
    }

    async run(init) {
        super.run(init);
        this.database_url = init?.database_url;

        if (!(this.database_url)) {
            console.log("Database URL not found skipping import.");
            return false;
        }
        this.query = this.connectDB()
        await this.processNPI('list_cms_npi', init.date_today);
    };
    async processNPI(tableName, date_today) {
        const csonFiles = {
            main: __dirname + '/../../base/cson/list_cms_npi.cson',
            other: __dirname + '/../../base/cson/list_cms_npi_other.cson',
            group: __dirname + '/../../base/cson/list_cms_npi_group.cson',
            state: __dirname + '/../../base/cson/list_cms_npi_state.cson'
        };

        const jsonData = {};
        for (const [key, path] of Object.entries(csonFiles)) {
            jsonData[key] = cson.parse(fs.readFileSync(path, 'utf8'));
        }
        let processedRows = 0;  // Keep track of the number of processed rows
        let startTime = Date.now();  // Record the start time

        // Function to log the progress
        const logProgress = () => {
            const now = Date.now();
            const elapsedMinutes = Math.floor((now - startTime) / 60000);

            if (elapsedMinutes >= 1) {  // Log every minute
                console.log(`${processedRows} rows processed in ${elapsedMinutes} minutes.`);
                startTime = now;  // Reset the start time
            }
        }

        const processFile = async (file) => {
            const { main: jsonDataMain, other: jsonDataOther, group: jsonDataGroup, state: jsonDataState } = jsonData;
            const csonFields = Object.keys(jsonDataMain.fields);
            const objectMap = {
                code: 'NPI',
                replacement_npi: 'Replacement NPI',
                employer_identification_number: 'Employer Identification Number (EIN)',
                provider_organization_name: 'Provider Organization Name (Legal Business Name)',
                provider_last_name: 'Provider Last Name (Legal Name)',
                parent_organization_tin: 'parent_organization_tin',
                parent_organization_lbn: 'Parent Organization LBN',
                npi_reactivation_date: 'NPI Reactivation Date',
                npi_deactivation_date: 'NPI Deactivation Date',
                npi_deactivation_reason_code: 'NPI Deactivation Reason Code',
                provider_business_mailing_address_postal_code_outside_us: 'provider_business_mailing_address_postal_code_outside_us',
                provider_business_practice_loc_addr_postal_code_outside_us: 'Provider Business Practice Location Address Country Code (If outside U.S.)',
                authorized_official_title_or_position: 'Authorized Official Title Or Position',
            };
            const objMapKeys = Object.keys(objectMap);
            return new Promise(async (resolve) => {
                let valueArrays = {
                    main: [],
                    taxonomyGroup: [],
                    providerTaxonomy: [],
                    otherProvider: []
                };

                const columnData = {
                    taxonomyGroup: ['npi', 'healthcare_provider_taxonomy_group', 'auto_name', 'created_on', 'created_by'],
                    providerTaxonomy: ['npi', 'healthcare_provider_taxonomy_code', 'provider_license_number', 'provider_license_number_state_code', 'healthcare_provider_primary_taxonomy_switch', 'auto_name', 'created_on', 'created_by'],
                    otherProvider: ['npi', 'other_provider_identifier', 'other_provider_identifier_type_code', 'other_provider_identifier_state', 'other_provider_identifier_issuer', 'auto_name', 'created_on', 'created_by']
                };

                let queryCounters = await this.getMaxIds()

                const files = {
                    main: __dirname + '/../../base/post/0000-full-list_cms_npi_weekly.sql',
                    group: __dirname + '/../../base/post/0000-full-list_cms_npi_group_weekly.sql',
                    state: __dirname + '/../../base/post/0000-full-list_cms_npi_state_weekly.sql',
                    other: __dirname + '/../../base/post/0000-full-list_cms_npi_other_weekly.sql'
                };

                const streams = {};
                for (const [key, path] of Object.entries(files)) {
                    streams[key] = fs.createWriteStream(path);
                    streams[key].write(`BEGIN;\n SET session_replication_role = replica;\n`);
                }

                const zip = this.getZipStream(file);
                const entry = await this.getFileEntry(zip);
                const stream = await zip.stream(entry.name);

                let isProcessing = false;
                let rowsPending = 0;
                stream.pipe(csv()).on('data', async (row) => {
                    isProcessing = true;
                    rowsPending++;
                    try {
                        let npi = row['NPI'];
                        stream.pause();  // Pause the stream while processing the row
                        let existingRecord = await this.getExistingRecord('form_list_cms_npi', 'code', npi);
                        if (existingRecord.length > 0) {
                            valueArrays = await this.updateExistingRecord(streams, row, date_today, npi, queryCounters, valueArrays, jsonData, objectMap, columnData);
                            stream.resume();  // Resume the stream after processing the row
                        } else {
                            const autonameData = {};
                            const rowValues = [];

                            for (const csonField of csonFields) {
                                let value = 'NULL';
                                if (objMapKeys.includes(csonField)) {
                                    const fieldName = objectMap[csonField];
                                    if (row[fieldName]) {
                                        value = this.validateFields(jsonDataMain.fields[csonField], row[fieldName]);
                                        autonameData[csonField] = row[fieldName];
                                    }
                                } else {
                                    const fieldName = csonField.split('_').map(o => o.charAt(0).toUpperCase() + o.slice(1)).join(' ');
                                    const val = row[fieldName];
                                    value = this.validateFields(jsonDataMain.fields[csonField], val);
                                    autonameData[csonField] = val;
                                }
                                rowValues.push(value);
                            }

                            const auto_name = form_auto_name(jsonDataMain, autonameData);
                            valueArrays.main.push(`(${queryCounters.main}, ${rowValues.map(val => `${val}`).join(', ')}, '${auto_name}', '${date_today}', 1) \n`);
                            if (valueArrays.main.length >= 1000) {
                                console.log(`Inserting records into main table`);
                                const insertQuery = `INSERT INTO form_${tableName} (id, ${csonFields.join(', ')}, auto_name, created_on, created_by)\nVALUES\n${valueArrays.main};\n`;
                                streams.main.write(`\n${insertQuery}\n`);
                                valueArrays.main = [];
                            }

                            // Handle other provider values
                            for (let i = 1; i <= 50; i++) {
                                if (row[`Other Provider Identifier_${i}`] || row[`Other Provider Identifier Type Code_${i}`] || row[`Other Provider Identifier State_${i}`] || row[`Other Provider Identifier Issuer_${i}`]) {
                                    const auto_name = form_auto_name(jsonDataOther, { 'code': row['NPI'] }).replace(/'/g, "''");
                                    valueArrays.otherProvider.push(`(${queryCounters.otherProvider}, '${row['NPI']}', '${row[`Other Provider Identifier_${i}`].replace(/'/g, "''")}', '${row[`Other Provider Identifier Type Code_${i}`].replace(/'/g, "''")}', '${row[`Other Provider Identifier State_${i}`].replace(/'/g, "''")}', '${row[`Other Provider Identifier Issuer_${i}`].replace(/'/g, "''")}', '${auto_name}', '${date_today}', 1)\n`);
                                    if (valueArrays.otherProvider.length >= 1000) {
                                        const insertQuery = `INSERT INTO form_list_cms_npi_other (id, ${columnData.otherProvider.join(', ')}) VALUES ${valueArrays.otherProvider};\n`;
                                        streams.other.write(`\n${insertQuery}\n`);
                                        valueArrays.otherProvider = [];
                                    }
                                    queryCounters.otherProvider++;
                                }
                            }

                            // Handle taxonomy group values
                            for (let i = 1; i <= 15; i++) {
                                if (row[`Healthcare Provider Taxonomy Group_${i}`]) {
                                    const auto_name = form_auto_name(jsonDataGroup, { 'npi': row['NPI'] }).replace(/'/g, "''");
                                    valueArrays.taxonomyGroup.push(`(${queryCounters.taxonomyGroup}, '${row['NPI']}', '${row[`Healthcare Provider Taxonomy Group_${i}`].replace(/'/g, "''")}', '${auto_name}', '${date_today}', 1)\n`);
                                    if (valueArrays.taxonomyGroup.length > 1000) {
                                        const insertQuery = `INSERT INTO form_list_cms_npi_group (id, ${columnData.taxonomyGroup.join(', ')})\nVALUES\n${valueArrays.taxonomyGroup};\n`;
                                        streams.group.write(`\n${insertQuery}\n`);
                                        valueArrays.taxonomyGroup = [];
                                    }
                                    queryCounters.taxonomyGroup++;
                                }
                            }

                            // Handle provider taxonomy values
                            for (let i = 1; i <= 15; i++) {
                                if (row[`Healthcare Provider Taxonomy Code_${i}`] || row[`Provider License Number_${i}`] || row[`Provider License Number State Code_${i}`] || row[`Healthcare Provider Primary Taxonomy Switch_${i}`]) {
                                    const auto_name = form_auto_name(jsonDataState, { 'npi': row['NPI'] }).replace(/'/g, "''");
                                    valueArrays.providerTaxonomy.push(`(${queryCounters.providerTaxonomy}, '${row['NPI']}', '${row[`Healthcare Provider Taxonomy Code_${i}`].replace(/'/g, "''")}', '${row[`Provider License Number_${i}`].replace(/'/g, "''")}', '${row[`Provider License Number State Code_${i}`].replace(/'/g, "''")}', '${row[`Healthcare Provider Primary Taxonomy Switch_${i}`].replace(/'/g, "''")}', '${auto_name}', '${date_today}', 1)\n`);
                                    if (valueArrays.providerTaxonomy.length >= 1000) {
                                        const insertQuery = `INSERT INTO form_list_cms_npi_state (id, ${columnData.providerTaxonomy.join(', ')}) VALUES ${valueArrays.providerTaxonomy};\n`;
                                        streams.state.write(`\n${insertQuery}\n`);
                                        valueArrays.providerTaxonomy = [];
                                    }
                                    queryCounters.providerTaxonomy++;
                                }
                            }
                            queryCounters.main++;
                        }
                        processedRows++;  // Increment the processed row count
                        logProgress();
                    } catch (error) {
                        console.error(`Error processing row: ${error}`);
                        throw new Error(`Error processing row: ${error}`);
                    } finally {
                        rowsPending--;
                        if (rowsPending === 0) {
                            isProcessing = false;
                        }
                        stream.resume();
                    }
                });
                stream.on('end', () => {
                    const checkCompletion = () => {
                        if (isProcessing || rowsPending > 0) {
                            setTimeout(checkCompletion, 1000); // Recheck after 1 second
                        } else {
                            // Finalize queries for all tables
                            const alterQueryMain = `ALTER SEQUENCE "form_list_cms_npi_id_seq" RESTART WITH ${queryCounters.main};`;
                            if (valueArrays.main.length > 0) {
                                const insertQuery = `INSERT INTO form_${tableName} (id, ${csonFields.join(', ')}, auto_name, created_on, created_by)\nVALUES\n${valueArrays.main};\n`;
                                streams.main.write(insertQuery + `${alterQueryMain}\nSET session_replication_role = DEFAULT;\nCOMMIT;\n`);
                            } else {
                                streams.main.write(`${alterQueryMain}\nSET session_replication_role = DEFAULT;\nCOMMIT;\n`);
                            }

                            const alterQueryGroup = `ALTER SEQUENCE "form_list_cms_npi_group_id_seq" RESTART WITH ${queryCounters.taxonomyGroup};`;
                            if (valueArrays.taxonomyGroup.length > 0) {
                                const insertQuery = `INSERT INTO form_list_cms_npi_group (id, ${columnData.taxonomyGroup.join(', ')}) VALUES ${valueArrays.taxonomyGroup};\n`;
                                streams.group.write(insertQuery + `${alterQueryGroup}\nSET session_replication_role = DEFAULT;\nCOMMIT;\n`);
                            } else {
                                streams.group.write(`${alterQueryGroup}\nSET session_replication_role = DEFAULT;\nCOMMIT;\n`);
                            }

                            const alterQueryState = `ALTER SEQUENCE "form_list_cms_npi_state_id_seq" RESTART WITH ${queryCounters.providerTaxonomy};`;
                            if (valueArrays.providerTaxonomy.length > 0) {
                                const insertQuery = `INSERT INTO form_list_cms_npi_state (id, ${columnData.providerTaxonomy.join(', ')}) VALUES ${valueArrays.providerTaxonomy};\n`;
                                streams.state.write(insertQuery + `${alterQueryState}\nSET session_replication_role = DEFAULT;\nCOMMIT;\n`);
                            } else {
                                streams.state.write(`${alterQueryState}\nSET session_replication_role = DEFAULT;\nCOMMIT;\n`);
                            }

                            const alterQueryOther = `ALTER SEQUENCE "form_list_cms_npi_other_id_seq" RESTART WITH ${queryCounters.otherProvider};`;
                            if (valueArrays.otherProvider.length > 0) {
                                const insertQuery = `INSERT INTO form_list_cms_npi_other (id, ${columnData.otherProvider.join(', ')}) VALUES ${valueArrays.otherProvider};\n`;
                                streams.other.write(insertQuery + `${alterQueryOther}\nSET session_replication_role = DEFAULT;\nCOMMIT;\n`);
                            } else {
                                streams.other.write(`${alterQueryOther}\nSET session_replication_role = DEFAULT;\nCOMMIT;\n`);
                            }
                            let resolved = [];
                            resolved.push(new Promise((resolve) => streams.main.on('finish', () => resolve())));
                            resolved.push(new Promise((resolve) => streams.group.on('finish', () => resolve())));
                            resolved.push(new Promise((resolve) => streams.state.on('finish', () => resolve())));
                            resolved.push(new Promise((resolve) => streams.other.on('finish', () => resolve())));
                            Promise.all(resolved).then(() => { resolve(); });
                        }
                    };
                    checkCompletion();
                });
            });
        }
        let promises = [];
        if (!fs.existsSync(this.sourceFolder)) {
            // Create the directory
            fs.mkdirSync(this.sourceFolder, { recursive: true });
            console.log(`Directory created: ${this.sourceFolder}`);
        } else {
            console.log(`Directory already exists: ${this.sourceFolder}`);
        }

        let downloadedFiles = await this.downloadCMSNPIFiles(this.url)
        console.log('Processing weekly files...');
        let weeklyFile = this.findClosestDateFile(downloadedFiles);
        promises.push(processFile(weeklyFile));
        await Promise.all(promises);
    }

    async getExistingRecord(tableName, columnName, value) {
        let result = await this.query(`SELECT id FROM ${tableName} WHERE ${columnName} = '${value}' LIMIT 1;`);
        return result;
    }

    async downloadCMSNPIFiles(url = this.url) {
        console.log('Downloading CMS NPI Files...');
        let browser = await puppeteer.launch({
            headless: true,
            defaultViewport: null,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        let page = await browser.newPage();
        await page.goto(url, { waitUntil: "domcontentloaded" });
        let { links } = await page.evaluate(() => {
            let links = [];
            let elem = document.querySelector(".mainbox");
            let files = elem.querySelectorAll('a');
            for (let file of files) {
                if (file.href.includes('NPPES_Data_Dissemination_') && file.href.includes('.zip'))
                    links.push(file.href);
            }
            return { links };
        });
        let downloadedFiles = [];
        links = links.filter((link) => link.toLowerCase().includes('weekly'));
        for (let link of links) {
            let fileName = link.split('/').pop();
            console.log('Downloading file...');
            let downloadedFile = await this.downloadFile(link, fileName, this.sourceFolder);
            downloadedFiles.push(downloadedFile);
        }
        await browser.close();
        return downloadedFiles;
    };


    async getMaxIds() {
        let mainTableResult = await this.query('SELECT MAX(id) AS max FROM form_list_cms_npi;');
        let groupTableResult = await this.query('SELECT MAX(id) AS max FROM form_list_cms_npi_group;');
        let stateTableResult = await this.query('SELECT MAX(id) AS max FROM form_list_cms_npi_state;');
        let otherTableResult = await this.query('SELECT MAX(id) AS max FROM form_list_cms_npi_other;');
        return {
            main: mainTableResult?.length > 0 ? mainTableResult[0].max + 1 : 1,
            taxonomyGroup: groupTableResult?.length > 0 ? groupTableResult[0].max + 1 : 1,
            providerTaxonomy: stateTableResult?.length > 0 ? stateTableResult[0].max + 1 : 1,
            otherProvider: otherTableResult?.length > 0 ? otherTableResult[0].max + 1 : 1,
        };
    }


    parseDateFromFilename(filename) {
        let regex = /_(\d{6})_(\d{6})_/;
        let match = filename.match(regex);
        if (match) {
            let startDateStr = match[1];
            return moment(startDateStr, "MMDDYY");
        }
        return null;
    }

    findClosestDateFile(filenames) {
        let currentDate = moment();
        let closestFile = null;
        let smallestDiff = Infinity;

        filenames.forEach(filename => {
            let fileDate = this.parseDateFromFilename(filename);
            if (fileDate) {
                let diff = Math.abs(currentDate.diff(fileDate));
                if (diff < smallestDiff) {
                    smallestDiff = diff;
                    closestFile = filename;
                }
            }
        });
        return closestFile;
    }

    async getFileEntry(zip) {
        let entries = Object.values(await zip.entries());
        return entries.filter(e => !e.isDirectory && e.name.includes('npidata') && e.name.includes("pfile") && e.name.endsWith(".csv"))[0];
    }

    async updateExistingRecord(outputFiles, row, date_today, npi, queryCounters, valueArrays, jsonData, objectMap, columnData) {
        let { main, group, state, other } = outputFiles;
        const { main: jsonDataMain, other: jsonDataOther, group: jsonDataGroup, state: jsonDataState } = jsonData;
        let mainData = jsonData.main
        let csonFields = Object.keys(mainData.fields);
        let objMapKeys = Object.keys(objectMap);
        let obj = {};
        for (let csonField of csonFields) {
            let value = 'NULL';
            if (objMapKeys.includes(csonField)) {
                if (row[objectMap[csonField]]) {
                    let updatedVal = this.validateFields(mainData.fields[csonField], row[objectMap[csonField]]);
                    obj[csonField] = updatedVal;
                    value = updatedVal;
                }
            } else {
                let val = row[csonField.split('_').map(o => o.charAt(0).toUpperCase() + o.slice(1)).join(' ')];
                let updatedVal = this.validateFields(mainData.fields[csonField], val);
                value = updatedVal;
                obj[csonField] = updatedVal;
            }
        }
        let sqlString = `UPDATE form_list_cms_npi SET ${Object.keys(obj).map(o => o + '=' + obj[o]).join(', ')} WHERE code = '${npi}';`;
        main.write(`${sqlString} \n`);
        let respOther = await this.query(`SELECT id FROM form_list_cms_npi_other WHERE npi = '${npi}' LIMIT 1;`);
        if (respOther.length > 0) {
            let other_provider_col = ['npi', 'other_provider_identifier', 'other_provider_identifier_type_code', 'other_provider_identifier_state', 'other_provider_identifier_issuer'];
            let sql = '';
            for (let i = 1; i <= 50; i++) {
                if (row[`Other Provider Identifier_${i}`] || row[`Other Provider Identifier Type Code_${i}`] || row[`Other Provider Identifier State_${i}`] || row[`Other Provider Identifier Issuer${i}`]) {
                    let values = `('${row['NPI']}', '${row[`Other Provider Identifier_${i}`].replace(/'/g, "''")}', '${row[`Other Provider Identifier Type Code_${i}`].replace(/'/g, "''")}', '${row[`Other Provider Identifier State_${i}`].replace(/'/g, "''")}', '${row[`Other Provider Identifier Issuer_${i}`].replace(/'/g, "''")}')`
                    sql = `UPDATE form_list_cms_npi_other SET (${other_provider_col.join(', ')}) = ${values} WHERE npi = '${npi}';`;
                    other.write(`${sql} \n`);
                }
            }
        } else {
            for (let i = 1; i <= 50; i++) {
                if (row[`Other Provider Identifier_${i}`] || row[`Other Provider Identifier Type Code_${i}`] || row[`Other Provider Identifier State_${i}`] || row[`Other Provider Identifier Issuer_${i}`]) {
                    const auto_name = form_auto_name(jsonDataOther, { 'code': row['NPI'] }).replace(/'/g, "''");
                    valueArrays.otherProvider.push(`(${queryCounters.otherProvider}, '${row['NPI']}', '${row[`Other Provider Identifier_${i}`].replace(/'/g, "''")}', '${row[`Other Provider Identifier Type Code_${i}`].replace(/'/g, "''")}', '${row[`Other Provider Identifier State_${i}`].replace(/'/g, "''")}', '${row[`Other Provider Identifier Issuer_${i}`].replace(/'/g, "''")}', '${auto_name}', '${date_today}', 1)\n`);
                    if (valueArrays.otherProvider.length >= 1000) {
                        const insertQuery = `INSERT INTO form_list_cms_npi_other (id, ${columnData.otherProvider.join(', ')}) VALUES ${valueArrays.otherProvider};\n`;
                        other.write(`\n${insertQuery}\n`);
                        valueArrays.otherProvider = [];
                    }
                    queryCounters.otherProvider++;
                }
            }
        }

        let respGroup = await this.query(`SELECT id FROM form_list_cms_npi_group WHERE npi = '${npi}' LIMIT 1;`);
        if (respGroup.length > 0) {
            let taxonomy_group_col = ['npi', 'healthcare_provider_taxonomy_group'];
            let sql = '';
            for (let i = 1; i <= 15; i++) {
                if (row[`Healthcare Provider Taxonomy Group_${i}`]) {
                    let values = `('${row['NPI']}', '${row[`Healthcare Provider Taxonomy Group_${i}`].replace(/'/g, "''")}')`
                    sql = `UPDATE form_list_cms_npi_group SET (${taxonomy_group_col.join(', ')}) = ${values} WHERE npi = '${npi}';`;
                    group.write(`${sql} \n`);
                }
            }
        } else {
            for (let i = 1; i <= 15; i++) {
                if (row[`Healthcare Provider Taxonomy Group_${i}`]) {
                    const auto_name = form_auto_name(jsonDataGroup, { 'npi': row['NPI'] }).replace(/'/g, "''");
                    valueArrays.taxonomyGroup.push(`(${queryCounters.taxonomyGroup}, '${row['NPI']}', '${row[`Healthcare Provider Taxonomy Group_${i}`].replace(/'/g, "''")}', '${auto_name}', '${date_today}', 1)\n`);
                    if (valueArrays.taxonomyGroup.length > 1000) {
                        const insertQuery = `INSERT INTO form_list_cms_npi_group (id, ${columnData.taxonomyGroup.join(', ')})\nVALUES\n${valueArrays.taxonomyGroup};\n`;
                        group.write(`\n${insertQuery}\n`);
                        valueArrays.taxonomyGroup = [];
                    }
                    queryCounters.taxonomyGroup++;
                }
            }
        }

        let respState = await this.query(`SELECT id FROM form_list_cms_npi_state WHERE npi = '${npi}' LIMIT 1;`);
        if (respState.length > 0) {
            let provider_taxonomy_col = ['npi', 'healthcare_provider_taxonomy_code', 'provider_license_number', 'provider_license_number_state_code', 'healthcare_provider_primary_taxonomy_switch'];
            let sql = '';
            for (let i = 1; i <= 15; i++) {
                if (row[`Healthcare Provider Taxonomy Code_${i}`] || row[`Provider License Number_${i}`] || row[`Provider License Number State Code_${i}`] || row[`Healthcare Provider Primary Taxonomy Switch_${i}`]) {
                    let values = `('${row['NPI']}', '${row[`Healthcare Provider Taxonomy Code_${i}`].replace(/'/g, "''")}', '${row[`Provider License Number_${i}`].replace(/'/g, "''")}', '${row[`Provider License Number State Code_${i}`].replace(/'/g, "''")}', '${row[`Healthcare Provider Primary Taxonomy Switch_${i}`].replace(/'/g, "''")}')`
                    sql = `UPDATE form_list_cms_npi_state SET (${provider_taxonomy_col.join(', ')}) = ${values} WHERE npi = '${npi}';`;
                    state.write(`${sql} \n`);
                }
            }
        } else {
            for (let i = 1; i <= 15; i++) {
                if (row[`Healthcare Provider Taxonomy Code_${i}`] || row[`Provider License Number_${i}`] || row[`Provider License Number State Code_${i}`] || row[`Healthcare Provider Primary Taxonomy Switch_${i}`]) {
                    const auto_name = form_auto_name(jsonDataState, { 'npi': row['NPI'] }).replace(/'/g, "''");
                    valueArrays.providerTaxonomy.push(`(${queryCounters.providerTaxonomy}, '${row['NPI']}', '${row[`Healthcare Provider Taxonomy Code_${i}`].replace(/'/g, "''")}', '${row[`Provider License Number_${i}`].replace(/'/g, "''")}', '${row[`Provider License Number State Code_${i}`].replace(/'/g, "''")}', '${row[`Healthcare Provider Primary Taxonomy Switch_${i}`].replace(/'/g, "''")}', '${auto_name}', '${date_today}', 1)\n`);
                    if (valueArrays.providerTaxonomy.length >= 1000) {
                        const insertQuery = `INSERT INTO form_list_cms_npi_state (id, ${columnData.providerTaxonomy.join(', ')}) VALUES ${valueArrays.providerTaxonomy};\n`;
                        state.write(`\n${insertQuery}\n`);
                        valueArrays.providerTaxonomy = [];
                    }
                    queryCounters.providerTaxonomy++;
                }
            }
        }
        return valueArrays;
    }
}

module.exports = { CMSNPIWeekly };
