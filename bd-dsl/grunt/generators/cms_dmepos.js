"use strict";
const BaseRunner = require('./base');
const moment = require('moment')
const {
    form_auto_name
} = require('./utils/fx');
const fs = require('fs');
const puppeteer = require('puppeteer');
const csv = require('csv-parser');

class CMSDMEPOS extends BaseRunner {
    constructor() {
        super();
        this.sourceFolder = this.sourceFolder + "/cms_dmepos/";
        this.url = 'https://www.cms.gov/medicare/payment/fee-schedules/dmepos/dmepos-fee-schedule'
    }

    async run(init) {
        let task = global.Tasks.get_task('CMSDMEPOS');
        if (task) {
            return await task.run(init);
        }
        await super.run(init);
        try {
            let downloadedZip = await this.downloadFeeFiles(this.url);
            if (!downloadedZip) {
                throw Error("No file downloaded");
            }
            await this.processFile(downloadedZip);
        } catch (error) {
            throw new Error(error)
        }
    }

    async runAll(init) {
        let task = global.Tasks.get_task('CMSDMEPOS');
        if (task) {
            return await task.runAll(init);
        }
        return await this.run(init);
    }

    async processFile(file) {
        const zip = this.getZipStream(file);
        const entries = Object.values(await zip.entries());

        const entryMap = {
            'DMEPOS': 'list_cms_dmepos',
            'DMEPEN': 'list_cms_dmepen',
            'DME Rural': 'list_cms_dme_rural'
        };

        for (const [key, tableName] of Object.entries(entryMap)) {
            const entry = entries.find(e => !e.isDirectory && e.name.includes(key) && e.name.endsWith(".csv"));
            if (entry) {
                const data = await this.getInsertObjects(zip, entry, tableName);
                await this.generateSql(data, tableName);
            }
        }

        zip.close();
    }

    async downloadFeeFiles(url) {
        const downloadPath = this.sourceFolder;

        if (!fs.existsSync(downloadPath)) {
            fs.mkdirSync(downloadPath);
        }

        const browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox'
            ],
            defaultViewport: null
        });

        const page = await browser.newPage();

        await page._client().send('Page.setDownloadBehavior', {
            behavior: 'allow',
            downloadPath: downloadPath
        });

        await page.goto(url);

        await page.waitForSelector('table');
        const latestFileLink = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('table tbody tr'));
            let latestDate = null;
            let latestLink = null;

            rows.forEach(row => {
                const dateText = row.querySelector('td:nth-child(2)').innerText.trim();
                const link = row.querySelector('a');

                const dateParts = dateText.split(' ');
                const [month, year] = [dateParts[0], dateParts[1]];
                const date = new Date(`${month} 1, ${year}`);

                if (!latestDate || date > latestDate) {
                    latestDate = date;
                    latestLink = link.href;
                }
            });

            return latestLink;
        });
        if (latestFileLink) {

            await page.goto(latestFileLink, {
                waitUntil: 'networkidle2'
            });

            const zipFileLink = await page.evaluate(() => {
                const zipLinkElement = document.querySelector('a[href$=".zip"]');
                return zipLinkElement ? zipLinkElement.href : null;
            });

            if (zipFileLink) {
                let fileName = zipFileLink.split('/').pop();
                return await this.downloadFile(zipFileLink, fileName);
            } else {
                return null;
            }
        }
        await browser.close();
    }

    async getInsertObjects(zip, entry, tableName) {
        let jsonData = this.readCson(tableName);
        let objects = [];
        await new Promise(async (resolve) => {
            let fileName = entry.name
            let stm = await zip.stream(entry.name);
            let skipLines = tableName == 'list_cms_dme_rural' ? 0 :
                tableName == 'list_cms_dmepos' ? 6 :
                tableName == 'list_cms_dmepen' ? 4 : 0;

            // Get expected headers from CSON and create normalized versions for matching
            const expectedHeaders = Object.entries(jsonData.fields).reduce((acc, [fieldName, field]) => {
                // Create a normalized version of the header for matching
                const normalizedHeader = field.view.label
                    .replace(/\s+/g, '') // Remove spaces
                    .replace(/[\(\)]/g, ''); // Remove parentheses completely
                acc[normalizedHeader] = {
                    fieldName,
                    type: field.model ? field.model.type || 'text' : 'text'
                };
                return acc;
            }, {});

            console.log('Expected headers:', expectedHeaders);
            let isFirstRow = true;
            let headerMapping = {};

            stm.pipe(csv({
                skipLines: skipLines,
                headers: false,
                skipEmptyLines: true,
                ignoreEmpty: true
            })).on('data', async (row) => {
                // Handle the header row
                if (isFirstRow) {
                    // Create mapping from CSV headers to field names
                    Object.keys(row).forEach(idx => {
                        const csvHeader = row[idx];
                        if (!csvHeader) return;

                        // Normalize the CSV header the same way we did the expected headers
                        const normalizedHeader = csvHeader
                            .replace(/\s+/g, '')
                            .replace(/[\(\)]/g, '');

                        if (expectedHeaders[normalizedHeader]) {
                            headerMapping[idx] = expectedHeaders[normalizedHeader];
                        } else if (csvHeader === 'Description' || csvHeader === 'Desc') {
                            // Special handling for Description/Desc field
                            headerMapping[idx] = {
                                fieldName: 'description',
                                type: 'text'
                            };
                        }
                    });
                    console.log('Final header mapping:', headerMapping);
                    isFirstRow = false;
                    return;
                }

                const obj = {};
                Object.entries(row).forEach(([idx, val]) => {
                    if (!headerMapping[idx]) return;

                    const {
                        fieldName,
                        type
                    } = headerMapping[idx];
                    try {
                        // Only process value if it's not empty
                        if (val && val.trim() !== '') {
                            // Handle numeric types
                            if (type === 'decimal' || type === 'number') {
                                val = val.replace(/,/g, ''); // Remove any commas
                                val = parseFloat(val);
                                if (!isNaN(val)) {
                                    obj[fieldName] = val;
                                }
                            } else {
                                // For text fields, remove any surrounding quotes and trim
                                obj[fieldName] = val.replace(/^"|"$/g, '').trim();
                            }
                        }
                    } catch (error) {
                        console.log(`Error processing field ${fieldName} with value ${val}:`, error);
                    }
                });

                if (Object.keys(obj).length > 0) {
                    objects.push(obj);
                }
            }).on('end', () => {
                console.log(`${fileName} processed - ${objects.length} records found`);
                resolve();
            });
        });

        return objects;
    }

    async generateSql(objArray, tableName = this.constructor.name) {
        const date_today = moment().format('YYYY-MM-DD HH:mm:ss');
        console.log("generateSql for Table Name: ", tableName);
        let writeStream = this.initFullFileWriteStream(tableName);
        const formCson = this.readCson(tableName);
        let columns = ['id'].concat(Object.keys(formCson.fields));
        let dataArray = [];
        
        // Write transaction setup at the start of the file
        writeStream.write(`BEGIN;
SET session_replication_role = replica;
-- Temporarily disable some PostgreSQL features for faster bulk load
SET maintenance_work_mem = '2GB';
SET synchronous_commit = off;
SET work_mem = '2GB';
SET temp_buffers = '1GB';

TRUNCATE TABLE form_${tableName};
COMMIT;

BEGIN;
SET session_replication_role = replica;
SET maintenance_work_mem = '2GB';
SET synchronous_commit = off;
SET work_mem = '2GB';
SET temp_buffers = '1GB';

`);

        let sql = `INSERT INTO form_${tableName} (${columns.join(',')},created_on,created_by,auto_name) VALUES\n`;
        let id = 0;
        for (let index in objArray) {
            id = parseInt(index) + 1;
            let val = objArray[index];
            const values = columns.map(col => {
                if (col === 'id') {
                    return id;
                } else {
                    const field = formCson.fields[col];
                    const value = val[col];

                    if (value === null || value === undefined) {
                        return 'NULL';
                    }

                    if (field.model && (field.model.type === 'decimal' || field.model.type === 'number')) {
                        return value;
                    }

                    return `'${value.toString().replace(/'/g, "''")}'`;
                }
            });
            let auto_name = form_auto_name(formCson, val);
            dataArray.push(`(${values.join(',')},'${date_today}',1, '${auto_name}')\n`);

            if ((id % 10000) === 0) {
                sql = `${sql} ${dataArray.join(',')};\n`;
                dataArray = [];
                writeStream.write(`\n${sql}\n`);
                sql = `INSERT INTO form_${tableName} (${columns.join(',')},created_on,created_by,auto_name) VALUES`;
            }
        }
        if (dataArray.length > 0) {
            sql = `${sql} ${dataArray.join(',')};\n`;
            writeStream.write(`${sql}\n`);
        }

        // Write transaction cleanup at the end of the file
        writeStream.write(`\nCOMMIT;

BEGIN;
ALTER SEQUENCE "form_${tableName}_id_seq" RESTART WITH ${id + 1};
SET session_replication_role = DEFAULT;
COMMIT;\n`);

        await this.finalizeFullFileWriteStream(writeStream, tableName, (id + 1));
        console.log("DONE");
    }
}

module.exports = {
    CMSDMEPOS
};
