"use strict";
const BaseRunner = require('./base');
const csv = require('csv-parser');
const fs = require('fs');

class CHPayer extends BaseRunner {
    constructor() {
        super();
        this.sourceFolder = this.sourceFolder + "/ch_payer/";
    }

    static taskList = {};
    static register_tasks(task) {
        let t = new task();
        this.taskList[t.constructor.name] = t;
    }
    static get_task(task) {
        if (Object.keys(this.taskList).includes(task))
            return this.taskList[task];
    }

    static getAllTasks() {
        return this.taskList;
    }

    async runAll() {
        for (let [name, task] of Object.entries(CHPayer.getAllTasks())) {
            await task.init(name, false);
        }
    }

    async run(init) {
        super.run(init);
        let promises = [];
        promises.push(this.init())
        await Promise.all(promises);
        console.log("All tasks completed");
    };

    async init() {
        let csvFile = this.sourceFolder + 'ch_imprt.csv'
        let insertObjects = await this.processCSV(csvFile)
        if (insertObjects.length < 1) {
            throw Error("No data extracted from downloaded files");
        }
        await this.generateSql(insertObjects, 'list_chc_payer');
    };

    updateStatuses (value) {
        let mapObject = {
            'A' : 'Active',
            'I' : 'In-Active',
            'N' : 'No',
            'Y' : 'Yes',
        }
        if (mapObject[value]){
            return mapObject[value]
        }
    } 

    async processCSV(inputFile) {
        return new Promise((resolve, reject) => {
            const results = [];
            fs.createReadStream(inputFile)
                .pipe(csv({
                    columns: true,
                    skip_empty_lines: true,
                    trim: true
                  }))
                .on('data', (row) => {
                    results.push({
                        payer_name: row[Object.keys(row)[0]],
                        imn_payer_id: row['IMN Payer ID'],
                        legacy_payer_id: row['Legacy CHC Payer ID'],
                        rpa_eligibility_id: row['RPA Payer ID - Eligibility'],
                        rpa_claim_status_id: row['RPA Payer ID - Claim Status'],
                        eligibillity_status: this.updateStatuses(row['Status Eligibility']),
                        stand_in_indicator_eligibillity: this.updateStatuses(row['Stand-in Indicator Eligibility']),
                        enrollment_required_eligibillity: this.updateStatuses(row[`Enrollment Req'd Eligibility`]),
                        status_claim: this.updateStatuses(row['Status Claim']),
                        stand_in_indicator_claim: this.updateStatuses(row['Stand-in Indicator Claim']),
                        enrollment_required_claim: this.updateStatuses(row[`Enrollment Req'd Claim`]),
                        status_auth: this.updateStatuses(row['Status Auth']),
                        stand_in_indicator_auth: this.updateStatuses(row['Stand-in Indicator Auth']),
                        enrollment_required_auth: this.updateStatuses(row[`Enrollment Req'd Auth`]),
                        status_referral: this.updateStatuses(row['Status Referral']),
                        stand_in_indicator_referral: this.updateStatuses(row['Stand-in Indicator Referral']),
                        enrollment_required_referral: this.updateStatuses(row[`Enrollment Req'd Referral`]),
                        status_notification: this.updateStatuses(row['Status Notification']),
                        stand_in_indicator_notification: this.updateStatuses(row['Stand-in Indicator Notification']),
                        enrollment_required_notification: this.updateStatuses(row[`Enrollment Req'd Notification`]),
                        service_restored: this.updateStatuses(row['Service Restored']),
                        active_date: row['Active Date']? row['Active Date']: null,
                    });
                })
                .on('end', () => {
                    resolve(results);
                })
                .on('error', (error) => {
                    reject(error);
                });
        });
    }

}
module.exports = { CHPayer };