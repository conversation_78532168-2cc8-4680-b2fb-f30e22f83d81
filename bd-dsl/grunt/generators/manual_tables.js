"use strict";
const fs = require('fs');
const { form_auto_name } = require('./utils/fx');
const cson = require('cson');
const BaseRunner = require('./base');
class ManagedTables extends BaseRunner {
    constructor() {
        super();
        this.directoryPath = this.sourceFolder + '/manual_tables/';
    }

    static taskList = {};
    static register_tasks(task) {
        let t = new task();
        this.taskList[t.letructor.name] = t;
    }

    static get_task(task) {
        if (Object.keys(this.taskList).includes(task))
            return this.taskList[task];
    }

    async run(init) {
        if (!fs.existsSync(this.directoryPath))
            return;

        let files = fs.readdirSync(this.directoryPath);
        files.sort();
        let promises = [];
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            promises.push(this.processFile(file, init.date_today));
        }
        await Promise.all(promises);
    }

    processFile = async (file, date_today) => {
        let doesNotExist = [];
        let outputFile = "";
        return new Promise((resolve) => {
            try {
                const tables = fs.readFileSync(this.directoryPath + file, 'utf8');
                let tablesStringArray = tables.split('form:').filter((e) => e.length > 0);
                let pushToLast = ['list_billing_code', 'list_billing_csstatus', 'list_billing_cstatus', 'list_dose_type', 'list_symptom'], skippedTables = {};
                for (let tableString of tablesStringArray) {
                    let tableData = tableString.split('\n').filter((e) => e.length > 0);

                    // get cson and check if it exists
                    let tableName = tableData.shift();
                    if (tableString.indexOf(':::') > 0) {
                        skippedTables[tableName] = tableData;
                        continue;
                    }
                    if (pushToLast.includes(tableName)) {
                        // console.log(`${tableName} self skipped Need to add gr queries`);
                        skippedTables[tableName] = tableData;
                        continue;
                    }
                    const csonFilePath = __dirname + `/../../base/cson/${tableName}.cson`;
                    if (!fs.existsSync(csonFilePath)) {
                        doesNotExist.push(tableName);
                        continue;
                    }
                    let numString = '/../../base/post/0000-full-';
                    if (pushToLast.includes(tableName))
                        numString = '/../../base/post/0001-full-';
                    outputFile = __dirname + numString + tableName + '.sql';
                    const data = fs.readFileSync(csonFilePath, 'utf8');
                    const jsonData = cson.parse(data);
                    let valuesArray = [];
                    let id = 1;
                    let cols = [];
                    for (let row of tableData) { // name:Name::code:A
                        let autoNameData = {};
                        let vals = [];
                        let columns = row.split('::'); // [0 : name:Name,1:code:A]
                        for (let colVal of columns) { // name:Name
                            if (colVal.indexOf(':') < 0) {
                                console.warn(colVal);
                                console.warn(tableName);
                                console.warn("faulty String: Inspect");
                                return;
                            }
                            let [column, data] = [colVal.split(':')[0], colVal.split(':')[1]];
                            autoNameData[column] = data;
                            if (!cols.includes(column))
                                cols.push(column);
                            vals.push(this.validateFields(jsonData.fields[column], data));
                        }
                        // create insert query
                        if (id === 1)
                            cols = cols.concat(['id', 'created_on', 'created_by', 'auto_name']);
                        let auto_name = form_auto_name(jsonData, autoNameData);
                        valuesArray.push(`(${vals.join(",")},${id},'${date_today}',1,'${auto_name.replace(/'/g, "''")}')\n`);
                        id++;
                    }
                    let query = `INSERT INTO form_${tableName} ("${cols.join('","')}") VALUES ${valuesArray.join(',')};`;
                    // console.log(query);
                    // write to file
                    const sequenceTableName = `form_${tableName}_id_seq`;
                    const alterQuery = `ALTER SEQUENCE "${sequenceTableName}" RESTART WITH ${id};`;
                    fs.writeFileSync(outputFile, `BEGIN;\n SET session_replication_role = replica;\n DELETE FROM form_${tableName};\n${query}\n${alterQuery}\nSET session_replication_role = DEFAULT;\n COMMIT;\n`);
                    // return;
                }
                // console.log(tablesName.length);
                let grts = {};
                for (let [table, tdata] of Object.entries(skippedTables)) {
                    let tableName = table;
                    const csonFilePath = __dirname + `/../../base/cson/${tableName}.cson`;
                    if (!fs.existsSync(csonFilePath)) {
                        console.log(`CSON ${tableName} does not exists skipping`);
                        continue;
                    }
                    let numString = '/../../base/post/0001-full-';
                    let numGRString = '/../../base/post/0002-full-';
                    outputFile = __dirname + numString + tableName + '.sql';
                    let grOutFile = '';
                    const data = fs.readFileSync(csonFilePath, 'utf8');
                    const jsonData = cson.parse(data);
                    let valuesArray = [];
                    let id = 1;
                    let gid = 1;
                    let cols = [];
                    let gQueries = {};
                    for (let row of tdata) { // name:Name::code:A
                        let autoNameData = {};
                        let vals = [];
                        let grs = row.split(':::');
                        for (let split of grs) {
                            let columns = split.split('::'); // [0 : name:Name,1:code:A]
                            for (let colval of columns) {
                                let [column, data] = [colval.split(':')[0], colval.split(':')[1]];
                                if (jsonData.fields[column].model.source
                                    && jsonData.fields[column].model.multi
                                    && typeof jsonData.fields[column].model.source === 'string'
                                    && jsonData.fields[column].model.source !== '') {
                                    let sourceTable = jsonData.fields[column].model.source;
                                    let grTableName = `gr_form_${tableName}_${column}_to_${sourceTable}_id`;
                                    if (!(Object.keys(gQueries).includes(grTableName)))
                                        gQueries[grTableName] = [];
                                    grOutFile = __dirname + numGRString + `gr${tableName}_${sourceTable}` + '.sql';
                                    if (!(Object.keys(grts).includes(tableName)))
                                        grts[tableName] = {};
                                    grts[tableName][column] = 1;
                                    // "gr_" + table_name + "_" + column + "_to_" + jsonData.fields[column].model.source + "_id"
                                    // "gr_form_list_dose_type_associated_therapy_id_to_list_therapy_id"
                                    for (let sid of data.split(',')) {
                                        gQueries[grTableName].push(`INSERT INTO ${grTableName} (id,form_${tableName}_fk,form_${sourceTable}_fk) VALUES (${gid},${id},'${sid.trim()}')`);
                                        gid++;
                                    }
                                } else {
                                    autoNameData[column] = data;
                                    if (!cols.includes(column))
                                        cols.push(column);
                                    vals.push(this.validateFields(jsonData.fields[column], data));
                                }
                            }
                        }
                        // create insert query
                        if (id === 1)
                            cols = cols.concat(['id', 'created_on', 'created_by', 'auto_name']);
                        let auto_name = form_auto_name(jsonData, autoNameData);
                        valuesArray.push(`(${vals.join(",")},${id},'${date_today}',1,'${auto_name.replace(/'/g, "''")}')\n`);
                        id++;
                    }
                    const sequenceTableName = `form_${tableName}_id_seq`;
                    const alterQuery = `ALTER SEQUENCE "${sequenceTableName}" RESTART WITH ${id};`;
                    let query = `INSERT INTO form_${table} ("${cols.join('","')}") VALUES ${valuesArray.join(',')};`;
                    // console.log(query);
                    // write to file
                    for (let [grtable, querArray] of Object.entries(gQueries)) {
                        fs.writeFileSync(grOutFile, `BEGIN;\n SET session_replication_role = replica;\n DELETE FROM ${grtable};\n` + querArray.join(';\n') + ';\nSET session_replication_role = DEFAULT;\n COMMIT;\n');
                    }
                    // fs.writeFileSync(grOutFile, `BEGIN;\n SET session_replication_role = replica;\n DELETE FROM form_${table};\n ` + gQueries.join(';') + '; SET session_replication_role = DEFAULT;\n COMMIT;\n');
                    fs.writeFileSync(outputFile, `BEGIN;\n SET session_replication_role = replica;\n DELETE FROM form_${table};\n ` + query + `\n${alterQuery}\nSET session_replication_role = DEFAULT;\n COMMIT;\n`);
                }
                resolve();
                (doesNotExist.length > 0) ?? console.log(`CSON Does not exist please Verify ${doesNotExist.join('\n')}`);
            } catch (error) {
                console.log(error);
                console.log("<<<<<<<<<<error>>>>>>>>>>");
            }
        });
    };
};
module.exports = { ManagedTables };