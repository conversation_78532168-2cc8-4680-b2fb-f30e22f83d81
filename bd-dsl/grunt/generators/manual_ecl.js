const fs = require('fs');
const path = require('path');
const BaseRunner = require('./base');

class ECLImport extends BaseRunner {
    constructor() {
        super();
        this.jsonPath = this.sourceFolder + '/manual_ecl/';
    }

    async run(init) {
        super.run(init);
        try {
            if (!fs.existsSync(this.jsonPath))
                return;
            const jsonFiles = await this.readJsonFiles(this.jsonPath);
            const dataObjects = this.createDataObjects(jsonFiles);
            await this.generateSql(dataObjects, 'list_med_claim_ecl')
            console.log('SQL file created successfully!');
        } catch (error) {
            console.error('Error:', error);
        }
    }

    // Reads all JSON files from the directory
    async readJsonFiles(dir) {
        return new Promise((resolve, reject) => {
            fs.readdir(dir, (err, files) => {
                if (err) return reject(err);
                let jsonArray = [];
                files.forEach(file => {
                    if (path.extname(file) === '.json') {
                        const data = JSON.parse(fs.readFileSync(path.join(dir, file), 'utf8'));
                        jsonArray.push(data);
                    }
                });
                resolve(jsonArray);
            });
        });
    }

    // Converts JSON to array of objects with {column: value} structure
    createDataObjects(jsonArray) {
        let dataObjects = [];
        jsonArray.forEach(extJson => {
            for (const fieldColumn in extJson) {
                const data = extJson[fieldColumn];
                for (const code in data) {
                    if (code.trim().toLowerCase() !== 'blank') {
                        dataObjects.push({
                            field: fieldColumn,
                            code: code,
                            name: data[code],
                        });
                    }
                }
            }
        });
        return dataObjects;
    }
}

module.exports = { ECLImport };
