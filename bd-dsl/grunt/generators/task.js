"use strict";
const { FDBImport } = require('./fdb');
const { CMSPrices } = require('./cms_prices');
const { MedGuide } = require('./medguide');
const { CMSNPI } = require('./cms_npi');
const { PECOSNPI } = require('./pecos_npi');
const { ManagedTables } = require('./manual_tables');
const { CMSNPIWeekly } = require('./cms_npi_weekly');
const { ECLImport } = require('./manual_ecl');
const { CMSDMEPOS } = require('./cms_dmepos')
const { CHPayer } = require('./ch_payer')

class Tasks {
    static taskList = {};
    static taskCategories = {
        cms: [],
        fdb: [],
        managed: [],
        ch_payer: []
    };

    static register_tasks(task, category) {
        let t = new task();
        const taskName = t.constructor.name;
        this.taskList[taskName] = t;
        
        if (category && this.taskCategories[category]) {
            if (!this.taskCategories[category].includes(taskName)) {
                this.taskCategories[category].push(taskName);
            }
        }
    }

    static get_task(task) {
        if (Object.keys(this.taskList).includes(task))
            return this.taskList[task];
    }

    static list_tasks(category) {
        if (category && this.taskCategories[category]) {
            console.log(`Filtering tasks for category: ${category}`);
            return this.taskCategories[category];
        }
        return Object.keys(this.taskList);
    }

    static get_task_category(taskName) {
        for (const [category, tasks] of Object.entries(this.taskCategories)) {
            if (tasks.includes(taskName)) {
                return category;
            }
        }
        return null;
    }
};

// Register tasks with proper categories
// FDB Tasks
Tasks.register_tasks(FDBImport, 'fdb');

// CMS Tasks
Tasks.register_tasks(CMSDMEPOS, 'cms');
Tasks.register_tasks(CMSPrices, 'cms');
Tasks.register_tasks(CMSNPI, 'cms');
Tasks.register_tasks(CMSNPIWeekly, 'cms');
Tasks.register_tasks(PECOSNPI, 'cms');

// Managed Tasks
Tasks.register_tasks(MedGuide, 'managed');
Tasks.register_tasks(ManagedTables, 'managed');
Tasks.register_tasks(ECLImport, 'managed');

// CH Payer Tasks
Tasks.register_tasks(CHPayer, 'ch_payer');

module.exports = { Tasks };
