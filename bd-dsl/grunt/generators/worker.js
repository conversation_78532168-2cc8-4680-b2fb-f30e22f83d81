const { parentPort, workerData, threadId } = require('worker_threads');
const { form_auto_name } = require('./utils/fx');
const grunt = require('grunt');

function processMainRecord(row, counter, date_today, jsonData, csonFields, objectMap) {
    try {
        const autonameData = {};
        const rowValues = [];
        const objMapKeys = Object.keys(objectMap);

        for (const csonField of csonFields) {
            let value = "NULL";
            if (objMapKeys.includes(csonField)) {
                const fieldName = objectMap[csonField];
                if (row[fieldName]) {
                    value = `'${row[fieldName].replace(/'/g, "''")}'`;
                    autonameData[csonField] = row[fieldName];
                }
            } else {
                const fieldName = csonField
                    .split("_")
                    .map((o) => o.charAt(0).toUpperCase() + o.slice(1))
                    .join(" ");
                const val = row[fieldName];
                if (val) {
                    value = `'${val.replace(/'/g, "''")}'`;
                    autonameData[csonField] = val;
                }
            }
            rowValues.push(value);
        }

        const auto_name = form_auto_name(jsonData, autonameData).replace(/'/g, "''");
        return `(${counter}, ${rowValues.join(", ")}, '${auto_name}', '${date_today}', 1)`;
    } catch (err) {
        grunt.log.error(`Error processing main record: ${err.message}`);
        return null;
    }
}

function processTaxonomyGroup(row, index, counter, date_today, jsonData) {
    try {
        const auto_name = form_auto_name(jsonData, {
            npi: row["NPI"]
        }).replace(/'/g, "''");

        return `(${counter}, '${row["NPI"]}', '${row[`Healthcare Provider Taxonomy Group_${index}`].replace(/'/g, "''")}', '${auto_name}', '${date_today}', 1)`;
    } catch (err) {
        grunt.log.error('Error processing taxonomy group: ' + err.message);
        return null;
    }
}

function processProviderTaxonomy(row, index, counter, date_today, jsonData) {
    try {
        const auto_name = form_auto_name(jsonData, {
            npi: row["NPI"]
        }).replace(/'/g, "''");

        return `(${counter}, '${row["NPI"]}', '${(row[`Healthcare Provider Taxonomy Code_${index}`] || '').replace(/'/g, "''")}', '${(row[`Provider License Number_${index}`] || '').replace(/'/g, "''")}', '${(row[`Provider License Number State Code_${index}`] || '').replace(/'/g, "''")}', '${(row[`Healthcare Provider Primary Taxonomy Switch_${index}`] || '').replace(/'/g, "''")}', '${auto_name}', '${date_today}', 1)`;
    } catch (err) {
        grunt.log.error('Error processing provider taxonomy: ' + err.message);
        return null;
    }
}

function processOtherProvider(row, index, counter, date_today, jsonData) {
    try {
        const auto_name = form_auto_name(jsonData, {
            npi: row["NPI"]
        }).replace(/'/g, "''");

        return `(${counter}, '${row["NPI"]}', '${(row[`Other Provider Identifier_${index}`] || '').replace(/'/g, "''")}', '${(row[`Other Provider Identifier Type Code_${index}`] || '').replace(/'/g, "''")}', '${(row[`Other Provider Identifier State_${index}`] || '').replace(/'/g, "''")}', '${(row[`Other Provider Identifier Issuer_${index}`] || '').replace(/'/g, "''")}', '${auto_name}', '${date_today}', 1)`;
    } catch (err) {
        grunt.log.error('Error processing other provider: ' + err.message);
        return null;
    }
}

// Process a chunk of records
function processChunk(chunk, date_today, jsonData, columnData, csonFields, objectMap) {
    const buffers = {
        main: [],
        taxonomyGroup: [],
        providerTaxonomy: [],
        otherProvider: []
    };

    let counters = {
        main: 0,
        taxonomyGroup: 0,
        providerTaxonomy: 0,
        otherProvider: 0
    };

    for (const row of chunk) {
        try {
            // Process main record
            const mainValues = processMainRecord(row, counters.main, date_today, jsonData.main, csonFields, objectMap);
            if (mainValues) {
                buffers.main.push(mainValues);
                counters.main++;
            }

            // Process taxonomy groups
            for (let i = 1; i <= 15; i++) {
                if (row[`Healthcare Provider Taxonomy Group_${i}`]) {
                    const groupValues = processTaxonomyGroup(row, i, counters.taxonomyGroup, date_today, jsonData.group);
                    if (groupValues) {
                        buffers.taxonomyGroup.push(groupValues);
                        counters.taxonomyGroup++;
                    }
                }
            }

            // Process provider taxonomy
            for (let i = 1; i <= 15; i++) {
                if (row[`Healthcare Provider Taxonomy Code_${i}`] ||
                    row[`Provider License Number_${i}`] ||
                    row[`Provider License Number State Code_${i}`] ||
                    row[`Healthcare Provider Primary Taxonomy Switch_${i}`]) {
                    const taxValues = processProviderTaxonomy(row, i, counters.providerTaxonomy, date_today, jsonData.state);
                    if (taxValues) {
                        buffers.providerTaxonomy.push(taxValues);
                        counters.providerTaxonomy++;
                    }
                }
            }

            // Process other providers
            for (let i = 1; i <= 50; i++) {
                if (row[`Other Provider Identifier_${i}`] ||
                    row[`Other Provider Identifier Type Code_${i}`] ||
                    row[`Other Provider Identifier State_${i}`] ||
                    row[`Other Provider Identifier Issuer_${i}`]) {
                    const otherValues = processOtherProvider(row, i, counters.otherProvider, date_today, jsonData.other);
                    if (otherValues) {
                        buffers.otherProvider.push(otherValues);
                        counters.otherProvider++;
                    }
                }
            }
        } catch (err) {
            grunt.log.error('Error processing row: ' + err.message);
            grunt.log.error('Problematic row: ' + JSON.stringify(row));
        }
    }

    return { buffers, counters };
}

// Handle messages from the main thread
parentPort.on('message', (message) => {
    // Get static data from workerData
    const { jsonData, columnData, csonFields, objectMap } = workerData;
    // Get chunk data from the current message
    const { chunk, date_today } = message;
    
    grunt.log.debug(`Worker ${threadId}: Received message. Message type: ${message.chunk ? 'chunk processing' : 'other'}`);
    
    const result = processChunk(chunk, date_today, jsonData, columnData, csonFields, objectMap);
    
    // Add worker ID to result
    result.workerId = threadId;
    
    parentPort.postMessage(result);
}); 