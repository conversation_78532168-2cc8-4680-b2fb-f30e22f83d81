"use strict";
const BaseRunner = require('./base');
const moment = require("moment");

class MedGuide extends BaseRunner {
    constructor() {
        super();
        this.url = 'https://dps.fda.gov/medguide';
        this.tableName = 'list_fda_med_guides';
        this.sqlFilePath = __dirname + '/../../base/post/0000-full-' + this.tableName + '.sql';
    }
    async run(init) {
        super.run(init);
        await this.getGuidesList();
    };
    async getGuidesList(url = this.url) {

        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Response status: ${response.status}`);
            }
            const html = await response.text();
            const scriptRegex = /<script\b[^>]*>([\s\S]*?)<\/script>/gi;

            let match;
            let guide = false;
            while ((match = scriptRegex.exec(html)) !== null) {
                if (match[1].includes('ABILIFY')) {
                    const raw_list = match[1].replace("self.__next_f.push", "");
                    const pre_json = eval("({" + eval(raw_list)[1].trim() + "})");
                    guide = pre_json["15"][3].data;
                    break;
                }
            }

            if (guide)
                this.generateSQL(guide);

        } catch (error) {
            console.error(error);
            throw new Error('Error fetching data from API', error);
        }
    };

    /**
      ["drug_name", "active_ingredient", "form", "application_number", "sponsor", "max_date_sub", "app_doc_url"] */
    async generateSQL(data) {
        console.log("Generating SQL...");
        if (!data || data.length < 1)
            console.error("Invalid Medguide data!");
        const dateToday = moment().format('YYYY-MM-DD HH:mm:ss');
        let fieldsMap = {
            "drug_name": "drug_name",
            "active_ingredient": "active_ingredient",
            "form_route": "form",
            "application_no": "application_number",
            "company": "sponsor",
            "date": "max_date_sub",
            "link": "app_doc_url",
        };
        let name = ['drug_name', 'application_no'];
        let stream = this.initFullFileWriteStream(this.tableName);
        let csonFields = Object.keys(fieldsMap);
        let sql = `INSERT INTO form_${this.tableName} (id, ${csonFields.join(', ')},auto_name, created_on, created_by)\n VALUES `;
        let id = 1;
        for (let row of data) {
            let rowData = [];
            let autoName = '';
            for (let field of csonFields) {
                let value = row[fieldsMap[field]];
                if (name.includes(field))
                    autoName = (autoName + ' ' + value).trim();
                if (field == 'date') {
                    value = moment(new Date(value)).format('YYYY-MM-DD HH:mm:ss');
                }
                else if (!value)
                    value = 'NULL';
                rowData.push(value);
            }
            id++;
            sql = `${sql}\n(${id},'${rowData.join("','")}','${autoName}','${dateToday}',1),`;
        }
        sql = sql.slice(0, -1) + ';';
        const sequenceTableName = `form_${this.tableName}_id_seq`;
        const alterQuery = `ALTER SEQUENCE "${sequenceTableName}" RESTART WITH ${++id};`;
        stream.write(sql);
        await this.finalizeFullFileWriteStream(stream, this.tableName, id);
        console.log("SQL Generated...");
    }
};
module.exports = { MedGuide };