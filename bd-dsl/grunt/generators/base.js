"use strict";
const mime = require('mime-types');
const fs = require("fs");
const cson = require('cson');
const { mkdir } = require("fs/promises");
const StreamZip = require('node-stream-zip');
const path = require("path");
const axios = require('axios');
const moment = require("moment");
const { form_auto_name } = require('./utils/fx');
const pgp = require("pg-promise")({
    noWarnings: true
});
module.exports = class BaseRunner {
    constructor() {
        this.storeDir = __dirname + '/../../store/.tmp';
        this.downloadLocation = __dirname + '/source';
        this.sourceFolder = __dirname + '/source';
        this.fullFilesLocation = __dirname + '/../../base/cms/';
        this.url = 'http://i3.ytimg.com/vi/J---aiyznGQ/mqdefault.jpg'; // better change it to rick roll
        this.database_url;
    }

    run(init) {
        for (let key of Object.keys(init)) {
            this[key] = init[key];
        }
        if (!fs.existsSync(__dirname + '/source')) {
            fs.mkdirSync(__dirname + '/source');
        }
        if (!fs.existsSync(this.sourceFolder)) {
            fs.mkdirSync(this.sourceFolder);
        }
    }

    connectDB() {
        try {
            const db = pgp(this.database_url);
            return (q, params) => {
                q = this.sanitize_query(q);
                if (!params) {
                    return db.any(q);
                } else {
                    return db.any(q, params);
                }
            };
        } catch (error) {
            console.log("Error at connecting database:", error);
        }
    }

    sanitize_query(query) {
        const forbiddenOperations = /\b(UPDATE|DELETE|INSERT)\b/gi;
        return query.replace(forbiddenOperations, '"$1"');
    }

    createDir(path) {
        if (!path) {
            throw new Error("Path not defined");
        }
        fs.mkdirSync(path);
        return true;
    }
    async downloadFile(url = this.url, filename = null, downloadLocation = this.sourceFolder) {
        console.log("Downloading " + filename + " from: ", url);
        try {
            const response = await axios({
                method: 'GET',
                url: url,
                responseType: 'stream'
            });
            const contentType = response.headers['content-type'];
            const extension = contentType.split('/').pop(); // Extract extension from content type
            const fileName = filename ? filename : `downloadedFile_${Date.now()}.${extension}`;
            console.log('Downloading file to: ', downloadLocation);

            if (!fs.existsSync(downloadLocation)) await mkdir(downloadLocation);
            // Write the stream to file
            const filePath = path.resolve(downloadLocation, fileName);
            const writer = fs.createWriteStream(filePath);
            response.data.pipe(writer);
            return new Promise((resolve, reject) => {
                writer.on('finish', () => resolve(filePath));
                writer.on('error', reject);
            });
        } catch (error) {
            console.error('Error downloading file:', error);
            throw error;
        }
    };

    async getAllDownloadedFilesFromDir(dir = this.downloadLocation) {
        let files = fs.readdirSync(dir);
        const absoluteFilePaths = files.map(file => path.join(dir, file))
            .filter(filePath => fs.statSync(filePath).isFile())
            .filter(file => !file.includes('.DS_Store'));
        return absoluteFilePaths;
    }

    segregateFilesByType(files) {
        const segregatedFiles = {};
        for (let file of files) {
            const extension = file.split('.').pop().toLowerCase();
            if (segregatedFiles[extension]) {
                segregatedFiles[extension].push(file);
            } else {
                segregatedFiles[extension] = [file];
            }
        }
        return segregatedFiles;
    }

    getWriteStream(dest) {
        return fs.createWriteStream(dest, { flags: 'w' });
    }
    async generateSql(objArray, tableName = this.constructor.name) {
        const date_today = moment().format('YYYY-MM-DD HH:mm:ss');
        console.log("generateSql for Table Name: ", tableName);
        let writeStream = this.initFullFileWriteStream(tableName);

        const formCson = this.readCson(tableName);
        let columns = ['id'].concat(Object.keys(objArray[0]));
        let dataArray = [];
        let sql = `INSERT INTO form_${tableName} (${columns.join(',')},created_on,created_by,auto_name) VALUES\n`;
        let id = 0;
        for (let index in objArray) {
            id = parseInt(index) + 1;
            let val = objArray[index];
            const values = columns.map(col => {
                if (col === 'id') {
                    return id;
                } else {
                    const value = val[col];
                    if (typeof value === 'string') {
                        return `'${value.replace(/'/g, "''")}'`;
                    } else if (value === null || value === undefined) {
                        return 'NULL';
                    } else {
                        return value;
                    }
                }
            });
            let auto_name = form_auto_name(formCson, val).replace(/'/g, "''");
            dataArray.push(`(${values.join(',')},'${date_today}',1, '${auto_name}')\n`);

            if ((id % 10000) === 0) {
                sql = `${sql} ${dataArray.join(',')};\n`;
                dataArray = [];
                writeStream.write(`\n${sql}\n`);
                sql = `INSERT INTO form_${tableName} (${columns.join(',')},created_on,created_by,auto_name) VALUES`;
            }
        }
        //dataArray.length condition
        if (dataArray.length > 0) {
            sql = `${sql} ${dataArray.join(',')};\n`;
            writeStream.write(`${sql}\n`);
        }
        console.log("finalizing write stream for table: ", tableName);
        await this.finalizeFullFileWriteStream(writeStream, tableName, (id + 1));
        console.log("DONE");
    }
    readCson(fileName) {
        const csonFilePath = __dirname + `/../../base/cson/${fileName}.cson`;
        const data = fs.readFileSync(csonFilePath, 'utf8');
        return cson.parse(data);
    }
    readFileSync(path) {
        return fs.readFileSync(path, 'utf8');
    }
    initFullFileWriteStream(tableName) {
        // Get the category from the table name
        const category = tableName.startsWith('list_fdb_') ? 'fdb' :
            tableName.startsWith('list_cms_') ? 'cms' :
                'managed';

        // Create the category-specific directory if it doesn't exist
        const categoryDir = path.join(this.fullFilesLocation, category);
        if (!fs.existsSync(categoryDir)) {
            fs.mkdirSync(categoryDir, { recursive: true });
        }

        let dest = path.join(categoryDir, `0000-full-${tableName}.sql`);
        let stream = this.getWriteStream(dest);
        return stream;
    }

    async finalizeFullFileWriteStream(writeStream, tableName, id) {
        const sequenceTableName = `form_${tableName}_id_seq`;
        const alterQuery = `ALTER SEQUENCE "${sequenceTableName}" RESTART WITH ${id};`;
        writeStream.write(`${alterQuery}\n`);
        writeStream.end();
        await new Promise((resolve) => writeStream.on('finish', resolve));
    }

    async getFileMime(file) {
        try {
            const fileData = fs.readFileSync(file);
            const mimeType = mime.contentType(fileData) || mime.contentType(mime.lookup(file));

            if (mimeType) {
                return mimeType;
            } else {
                return false;
            }
        } catch (err) {
            console.error('Error reading file:', err);
        }
    }

    getZipStream(file) {
        const zipStream = new StreamZip.async({ file: file });
        return zipStream;
    }

    validateFields(fieldDef, value) {
        if (fieldDef.model && fieldDef.model?.type === 'int') {
            if (value && value.length > 0) {
                return parseInt(value, 10);
            } else {
                return null;
            }
        } else if (fieldDef.model && fieldDef.model?.type === 'decimal') {
            if (value && value.length > 0) {
                return parseFloat(value);
            } else {
                return null;
            }
        } else if (fieldDef.model && fieldDef.model?.type === 'date') {
            let val = moment(value).format('YYYY-MM-DD');
            if (val === 'Invalid date') {
                val = null;
            }
            return `'${val}'`;
        } else if (typeof value === 'string') {
            return `'${value.trim().replace(/'/g, "''")}'`;
        } else {
            return value;
        }
    }

    async getDataFromAPI(url) {
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Response status: ${response.status}`);
            }
            const json = await response.json();
            return json;
        } catch (error) {
            throw new Error('Error fetching data from API', error);
        }
    }
};
