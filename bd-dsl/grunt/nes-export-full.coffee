fs = require 'fs'

# import sync=full  data from db nes-export-full
module.exports = (grunt, options) ->
	grunt.registerTask 'nes-export-full', 'Generate sql files for sync', ->

		grunt.log.writeln "Start process for generating Sql files for sync_mode: full"

		# initialize
		dp = require('./classes/dsl')(grunt, options)
		et = dp.etcd
		formFromOption = grunt.option('form')
		customer = grunt.option('customer')

		if not formFromOption
			grunt.log.warn 'option --form cannot be empty'
			return
			
		# Determine base path based on customer
		base = if customer
			options.path + '/' + customer + '/post/full/'
		else
			options.path + '/base/post/full/'
			
		formList = [formFromOption]

		for formName in formList
			try
				apiEndpoint = '/sync/full/' + formName
				fileName = getFileNameToWrite(base, formName)
				# get sql queries
				response = et.get_api apiEndpoint, true
				continue if not response?.length

				# Check for empty data scenarios before attempting to write
				normalizedResponse = response.trim().toUpperCase()
				isEmpty = normalizedResponse == 'COMMIT;' or normalizedResponse == 'BEGIN; COMMIT;' or normalizedResponse == 'BEGIN;\nCOMMIT;' or normalizedResponse == 'BEGIN;\r\nCOMMIT;' or (not response.includes('INSERT INTO') and not response.includes('DELETE FROM') and not response.includes('UPDATE'))

				if isEmpty
					grunt.log.warn "Empty data returned for form: #{formName}. Skipping..."
					continue

				# write file
				grunt.file.write base + fileName, response
				grunt.log.writeln "Generated file: ", fileName, '\tAPI endpoint: ', apiEndpoint
			catch error
				grunt.log.writeln error
				grunt.log.warn 'Invalid response in api call ' + apiEndpoint + ' form name: ' + formName
				return

		grunt.log.writeln "End process for generating Sql files for sync_mode: full"
		return

# (without formname/With formname) download all/formname full allow-sync Yes
getFileNameToWrite = (directoryPath, formName, syncMode = 'full') ->
	nameTemplate = '0000-{syncmode}-{formname}.sql'
	files = fs.readdirSync(directoryPath)

	if files.length == 0
		return "0000-#{syncMode}-#{formName}"

	fileToFind = syncMode + '-' + formName
	filteredName = files.filter (str) -> str.includes(fileToFind)

	if filteredName.length > 0
		return filteredName[0]

	files.sort()
	files.reverse()

	name = nameTemplate.replace('{syncmode}', syncMode)
	.replace('{formname}', formName)

	return name
