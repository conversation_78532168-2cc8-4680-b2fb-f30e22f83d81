	module.exports = (grunt, options) ->
		grunt.registerTask 'dsl-make', 'DSL Compile', ->

			# fx
			findFields = (form, obj, path = []) ->
				results = []
				for key, value of obj
					if key is 'fields' and Array.isArray(value) and value.every((v) -> typeof v is 'string')
						for item in value
							if DSL[form]?.fields[item]?.model?.type is 'subform' and obj.area
								results.push [item  + " #" + obj.area, path]
							else
								results.push [item, path]
					else if typeof value is 'object'
						results = results.concat findFields(form, value, path.concat(key))
				results

			warnDuplicates = (form, fields_tuples) ->
				result = {}
				for [key, path] in fields_tuples
					unless result[key]
						result[key] = []
					result[key].push(path)
				for key, paths of result
					continue if paths.length < 2
					fpaths = []
					for path in paths
						filteredPath = path.filter((segment) -> isNaN(segment))
						fpaths.push filteredPath.join(' > ')
					if fpaths.length > 1
						WARN[form] = [] unless WARN[form]?
						WARN[form].push form + "." + key + " x" + fpaths.length + " in " + [...new Set(fpaths)].join(" | ")

			hasIssues = (o) ->
				for k, v of o
					return true if v.length > 0
				false

			logFormIssues = (sym, form, arr) ->
				return unless DSL[form]?
				result = {}
				for item in arr
					[key, ...value] = item.split('.')
					unless key is form and value
						key = form
						value = [item]
					result[key] ?= []
					result[key].push value.join(".")

				for key, values of result
					console.log "#{sym} #{key}:"
					for value in values
						console.log "  ∙ #{value}"

			# initialize
			DSL = {}
			ERR = {}
			WARN = {}
			ndsl = {}
			CSON = require('cson')
			checksum = require('checksum')
			glob = require('glob')
			fs = require('fs')
			dp = require('./classes/dsl')(grunt, options)
			ep = dp.etcd.params     # no need to reload since DSLParse already loads ETCD
			cc = ep.customer.trim() # customer code

			base = options.path + '/base/cson/'
			cdir = options.path + '/' + cc + '/cson/'
			kv = options.store

			# latest build
			build = dp.PATH_STORE + cc + dp.PATH_BUILD
			build_save = (file, data) ->
				file += '.cson'
				if cson
					grunt.file.write file, data
				else if grunt.file.exists(file)
					grunt.file.delete(file)
				return
			# clear build folder if doing a full rebuild (commands: compile/deploy)
			if not kv.getItemSync('build')
				require('delete').sync(build)
				kv.setItemSync('build', (new Date()).getTime())

			# base: load all unmodified json, identify modified cson to load later
			grunt.log.writeln 'Parsing Base DSL'
			for f in glob.sync(base + '*.cson', options)
				k = f.replace(base, '').replace('.cson', '')
				ots = kv.getItemSync('meta-base-' + k + '.log')
				nts = fs.statSync(f).mtime.getTime()
				if ots?.ts? and (nts is ots.ts)
					json = kv.getItemSync('dsl-base-' + k + '.json')
					if typeof json isnt 'undefined'
						DSL[k] = json
						continue
				ndsl[k] =
					base:
						file: f
						ts: nts
				# if base changes, force clear cached cc
				kv.removeItemSync('dsl-' + cc + '-' + k + '.json')
				kv.removeItemSync('meta-' + cc + '-' + k + '.log')

			# customer: load all unmodified json, identify modified cson to load later
			grunt.log.writeln 'Parsing ' + cc.toUpperCase() + ' DSL'
			for f in glob.sync(cdir + '*.cson', options)
				k = f.replace(cdir, '').replace('.cson', '')
				ots = kv.getItemSync('meta-' + cc + '-' + k + '.log')
				nts = fs.statSync(f).mtime.getTime()
				if ots?.ts? and (nts is ots.ts)
					json = kv.getItemSync('dsl-' + cc + '-' + k + '.json')
					if typeof json isnt 'undefined'
						DSL[k] = json
						continue
				ndsl[k] =
					base:
						file: base + k + '.cson'
						ts: if ndsl[k]?.base.ts? then ndsl[k].base.ts else -1
					cc:
						file: f
						ts: nts

			# base: load & parse all modified (new/edited) cson dsl
			for k,f of ndsl
				continue if not (f.base?.file? and grunt.file.exists(f.base.file))
				grunt.log.writeln '  ' + k
				p = dp.parse(k, dp.loadCSON(f.base.file))

				DSL[k] = p.json[k]
				if p.warn.length > 0
					WARN[k] = p.warn
				warnDuplicates(k, findFields(k, DSL[k].model.sections))
				if p.err.length > 0
					ERR[k] = p.err
					kv.removeItemSync('merged-' + k + '.cson')
					kv.removeItemSync('dsl-base-' + k + '.json')
					kv.removeItemSync('meta-base-' + k + '.log')
					build_save(build + k, false)
				else if f.base.ts isnt -1
					cson = grunt.file.read(f.base.file)
					kv.setItemSync('merged-' + k + '.cson', cson)
					kv.setItemSync('dsl-base-' + k + '.json', DSL[k])
					kv.setItemSync('meta-base-' + k + '.log',
						checksum: checksum(cson)
						ts: f.base.ts
					)
					build_save(build + k, cson)


			# customer: load & parse all modified (new/edited) cson dsl
			for k,f of ndsl
				continue if not (f.cc?.file? and grunt.file.exists(f.cc.file))
				grunt.log.writeln '  ' + cc + '/' + k
				bv = if grunt.file.exists(f.base.file) then dp.loadCSON(f.base.file) else {}
				cv = dp.loadCSON(f.cc.file)
				[b, p] = dp.merge_customer(k, bv, cv)
				DSL[k] = p.json[k]
				if p.err.length > 0
					ERR[k] = [] if not ERR[k]?
					ERR[k] = ERR[k].concat(p.err)
					kv.removeItemSync('merged-' + k + '.cson')
					kv.removeItemSync('dsl-' + cc + '-' + k + '.json')
					kv.removeItemSync('meta-' + cc + '-' + k + '.log')
					build_save(build + k, false)
				else
					cson = CSON.stringify(b)
					kv.setItemSync('merged-' + k + '.cson', cson)
					kv.setItemSync('dsl-' + cc + '-' + k + '.json', DSL[k])
					kv.setItemSync('meta-' + cc + '-' + k + '.log',
						checksum: checksum(cson)
						ts: f.cc.ts
					)
					build_save(build + k, cson)
			# verify used forms are valid
			for k,_ of DSL
				ferr = dp.verifyFieldSources(DSL, k)
				ifr = dp.verifyIfConditions(DSL, k)
				smr = dp.verifySyncMode DSL, k
				anr = dp.verifyAutoName DSL, k
				continue if ferr.length is 0 and ifr.error.length is 0 and ifr.warn.length is 0 and smr.length is 0 and anr.length is 0

				WARN[k] = [] if not WARN[k]?
				WARN[k] = WARN[k].concat(ifr.warn)

				ERR[k] = [] if not ERR[k]?
				ERR[k] = ERR[k].concat(ferr).concat(ifr.error).concat(smr).concat(anr)
				if ferr?.length > 0
					grunt.log.writeln k + ' ' + ferr
				if ifs?.error?.length > 0 
					grunt.log.writeln k + ' ' + ifs.error
				if smr?.length > 0
					grunt.log.writeln k + ' ' + smr
				if anr?.length > 0
					grunt.log.writeln k + ' ' + anr
			if hasIssues(WARN)
				grunt.log.writeln '\nDSL WARNING:'
				for k in Object.keys(WARN).sort()
					logFormIssues '*', k, WARN[k]
			if hasIssues(ERR)
				grunt.log.writeln '\nDSL ERROR:'
				for k in Object.keys(ERR).sort()
					logFormIssues '×', k, ERR[k]
				grunt.log.writeln ''
				grunt.fail.fatal 'DSL error(s) found!'
			grunt.file.write build + 'dsl.json', JSON.stringify(DSL)
			grunt.log.writeln '\nDSL OK!'

			return
