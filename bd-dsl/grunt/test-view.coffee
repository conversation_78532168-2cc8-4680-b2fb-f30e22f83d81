module.exports = (grunt, options) ->
	grunt.registerTask 'test-view', 'Testing View Calls', ->

		# initialize
		done = @async()
		dp = require('./classes/dsl')(grunt, options)
		et = dp.etcd
		remote = '/form/coffeedsl/'
		cson = require('cson');
		fs = require('fs')
		form_errored = []
		form_with_no_cson = []
		form_with_500 = []

		for c in et.get_api remote
			grunt.log.writeln 'Checking View: ' + c.code + ' ...'
			try
				csonFilePath = __dirname + "/../base/cson/#{c.code}.cson";
				data = fs.readFileSync(csonFilePath, 'utf8');
				jsonData = cson.parse(data);
				if jsonData.model.save == false
					continue;
				else
					vr = et.get_api "/form/#{c.code}"
					grunt.log.writeln ' OK'
			catch e
				form_errored.push("Form:#{c.code}")
				if e.code == 'ENOENT'
					form_with_no_cson.push("Form:#{c.code}")
				if e.statusCode == 500
					form_with_500.push("Form:#{c.code}")
		console.log("--------------> These forms view calls are not resolved correctly. <---------------")
		console.log(form_with_500)
		console.log("--------------> These forms csons not found but they are in coffeedsl. <---------------")
		console.log(form_with_no_cson)
		return