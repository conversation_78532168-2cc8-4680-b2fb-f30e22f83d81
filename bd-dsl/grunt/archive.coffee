module.exports = (grunt, options) ->
	grunt.registerTask 'archive', 'Archive CoffeeDSL forms', ->

		# initialize
		dp = require('./classes/dsl')(grunt, options)
		et = dp.etcd

		cfid = {}
		remote = '/form/coffeedsl/?archived=all'
		# remote: load ids
		for c in et.get_api remote
			cfid[c.code] = c.id if not cfid[c.code]

		farch = {}
		# forms to archive
		if grunt.option('set')
			for d in grunt.option('set').split(',')
				continue if not cfid[d]?
				farch[d] =
					remoteid: cfid[d]
					archived: true

		# forms to unarchive
		if grunt.option('unset')
			for d in grunt.option('unset').split(',')
				continue if not cfid[d]?
				farch[d] =
					remoteid: cfid[d]
					archived: false

		for k,v of farch
			grunt.log.write if v.archived then 'Set' else 'Unset'
			grunt.log.write ' Archive: ' + k + '...'
			pjs =
				archived: v.archived
			if et.put_api '/form/coffeedsl/' + v.remoteid, pjs
				grunt.log.writeln ' OK'
			else
				grunt.log.writeln '\nCannot set archive status on form!'.red

		return
