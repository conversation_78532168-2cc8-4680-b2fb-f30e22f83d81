# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

BD-DSL is a Domain-Specific Language (DSL) management system for healthcare data forms and database schemas. It uses CSON files to define database structures and business logic, which are then compiled and deployed to PostgreSQL databases.

## Build Commands

```bash
# Basic compilation (fastest - only modified files)
grunt

# Full clean and compile
grunt compile --host=<hostname>

# Deploy to remote host
grunt deploy --host=<hostname>
grunt deploy --host=<hostname> --list   # Preview changes
grunt deploy --host=<hostname> --force  # Continue on errors

# Database operations
grunt pgimportpre --database-url=<url>   # Import pre-deployment SQL
grunt pgimportpost --database-url=<url>  # Import post-deployment SQL

# Development utilities
grunt download --host=<hostname>         # Download DSL from remote
grunt compare --left=<src> --right=<src> # Compare DSLs
grunt clean                              # Clear cached data
```

## Architecture

### DSL Structure
- Forms are defined in CSON files under `base/cson/` (core) or `csp/cson/` and `heritage/cson/` (customer-specific)
- Each form maps to a database table with `form_` prefix (e.g., `ncpdp.cson` → `form_ncpdp`)
- Field types: date, datetime, decimal, int, time, text, subform
- Subforms create relationship tables: `sf_form_{parent}_to_{child}`
- Multi-value fields use gerund tables: `gr_form_{form}_{field}_to_{source}_id`

### SQL Conventions
- Function parameters: `p_` prefix
- Local variables: `v_` prefix
- Standard table aliases (e.g., `lcl` for `form_ledger_charge_line`)
- Always use explicit type casting: `::text`, `::numeric`, `::integer`
- Check `deleted IS NOT TRUE AND archived IS NOT TRUE` for form tables
- Views (`vw_` or `mvw_` prefix) don't need deleted/archived checks

### Medical Claims System
The codebase is currently implementing a major refactor to move medical claims processing from Node.js to PostgreSQL. Key tables:
- `med_claim_*` tables for claim data structure
- `med_claim_resp_*` tables for response handling (999, 277, 835)
- `med_claim_error_log` for error tracking

## Database Connection

For development testing:
```bash
psql "postgresql://clara:<EMAIL>:15466/patrickdev"
```

## Key Directories

- `base/cson/`: Core form definitions
- `base/post/operations/`: SQL functions and stored procedures
- `base/post/tests/`: SQL test files
- `grunt/`: Build system tasks and utilities
- `.cursor/rules/`: Development documentation and conventions

## Current Development

The project is on the `med_claims` branch, implementing medical claims processing functionality. When working on medical claims:
1. Follow the patterns in existing `med_claim_*` CSON files
2. Ensure SQL functions follow the naming conventions
3. Add appropriate error handling to `med_claim_error_log`
4. Test with both 837p electronic and CMS-1500 paper claim formats