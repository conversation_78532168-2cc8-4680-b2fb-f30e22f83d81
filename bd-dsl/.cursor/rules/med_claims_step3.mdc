---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 3: Build out med_claim and med_claim_1500 to json conversion

*   **Action:** Now we are going to work on a function to build out the CMS 1500 and major medical claim into our json blobs that we will be returning as json type that will be used by our invoice builder. If you look in 0074-ncpdp-json-builder that is where we are providing the migration of our compsite types for a ncpdp claim form over to json. We need to follow a similar pattern for the med_claim and med_claim_1500 records.

*   **Action:** Build test files to test out the generation of each segment of the builder and the final claim. You can insert mock records into the database as it is a test database I'm hooked up to. Be sure to check for extra escaped json.
