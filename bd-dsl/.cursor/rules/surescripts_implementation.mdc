---
description: 
globs: 
alwaysApply: false
---
```markdown
# MDC Rule: Surescripts Implementation Plan

**Rule ID:** SURESCRIPTS-IMPLEMENTATION-V1-DETAILED-FINAL
**Version:** 1.0
**Date:** 2025-05-02
**Status:** Finalized

## 0. Project Description & Context

**Goal:** Refactor our current logic for handling inbound/outbound Surescripts e-prescription messages that is in node.js under our nes/src/api/surescripts to move the bulk of the logic from node.js into SQL functions that can parse, check, and map into our Surescripts message records. This includes validating new messages, parsing them and storing in the proper tables, validating outbound messages, and parsing and mapping to our outbound json object. We also need to add support functions to generate records in the patient chart for a new patient, patient_diagnosis, form_patient_address, form_patient_allergy, form_patient_medication, patient_prescriber, careplan_order.

**Workflow:** We have an admin server which will send us new messages in our nes/src/api/surescripts/inbound/ endpoint. Those messages can be new messages or responses for messages we had previously sent out. We take those messages, validate them, and either send back an error response to our admin server or store them into the proper tables. Out outbound endpoint is in nes/src/api/surescripts/outbound and is an instance of ss_message that is passed into the endpoint. We validate the outbound message, including checking the receiving site to through our directory endpoint on the admin server to check if the receiving physician has the capabiltiies to receive the type of message we want to receive, we can't migrate any of that directory logic over to SQL, but we can migrate everything else around it. The other functionality we will have is to generate records from the ss_message. This include the patient record (form_patient), the insurance (if we have the payer in the system) in form_patient_insurance, form_patient_diagnosis, the physician link (if we have the physician in the system) to form_patient_prescriber, or the order (form_patient_order + the form_patient_orderp_item) and when creating the form_patient_orderp_item, we have to set ss_description to the description on the message, is_erx = 'Yes', and physician_order_id = ss_message.physician_order_id. The other fields that we map in should be straight forward but stop and ask if you have any questions. Obviously if we don't have a patient, we have to create that first before creating any other records (since they require a patient_id). I would like to create a view that contains the form name, and a json blob of the preset values called preset and an id of the ss_message it is related to. We only want to do this for the forms that we don't already have a matching record in the system. I will show a grid of these view results that they can click on it to generate the form and preset the values into it from the message.

**Key Components:**
*   Please generate all .sql files under bd-dsl/base/post/operations
*   **Schema Records** `ecl.xsd` contain a list of our possible values for fields that are mapped into our form_list_* tables mentioned below. `script.xsd` contains the schema structure of our ss_message that will be mapped into our ss_message and related ss_* tables. It is represented as a json object inside of ss_log.message_json field.
*   **Log Record Table** `ss_log` will contain a log of our inbound or outbound message where message_json will contain the json representation of our message that we will be using our mapping for to map into our Surescripts forms.
*   **Surescripts Message Tables:** `ss_message`, `ss_observation`,`ss_due`,`ss_diagnosis`, `ss_compound`, `ss_codified_note`, `ss_chg_resp`, `ss_chg_med`, `ss_followup`,`ss_benefit`, `ss_attachment`, `ss_allergy` will contain the contents of our inbound and outbound message compounds that we will be mapping into. Each of these should be a custom type that we can use to build an object and finally convert it into a json record before sending back the response to the client where they can save it (or send it if an outbound message)
*   **Helper Functions:** Reusable SQL functions for parsing new inbound messages from ss_log, outbound message parser to build the json representation for an outbound message, 
*   **Error Logging:** Dedicated table `ss_error_log` for capturing issues.
*  Examine the following table structures before continuing, they will be used throughout the implementation: 
form_company, form_site, form_ss_message, form_patient, form_order, form_orderp_item, form_patient_prescriber, form_physician, form_inventory, form_diagnosis, form_patient_diagnosis, form_patient_allergy, form_patient_medication, form_ss_log, form_ss_message, form_careplan_order_rx, form_site,
form_ss_observation, form_ss_followup, form_ss_due, form_ss_diagnosis, form_ss_compound, form_ss_codified_note, form_ss_chg_resp, form_ss_chg_med, form_ss_benefit, form_ss_attachment, form_ss_allergy, form_list_ss_chg_code, form_list_ss_chg_subcode, form_list_chg_valid_reason, form_ss_coagent_qualifier, form_ss_cvg_status, form_list_ss_denial_reason, form_list_ss_drug_coded_qualifier, form_list_ss_dx_qualifier, form_list_ss_error_code, form_list_ss_error_desc, form_list_ss_error_resp_code, form_list_ss_gen_opts, form_list_ss_group_reason, form_list_ss_note_qualifier, form_list_ss_pa_status, form_list_ss_payer_type,
form_list_ss_pro_service_rsn_code, form_list_ss_product_qualifier, form_list_ss_quantity_qualifier, form_list_ss_quantity_qualifier, form_list_ss_service_level, form_list_ss_service_reason_code, form_list_ss_service_result_code

Also see the values in all form_list_* tables as needed as they will be the available values for fields that reference them under field.source.

**Key Linking:**
We now store a few items differently than when the originally functionality was written. For example, orders now are single items under form_careplan_order and have a subform form_careplan_orderp_item linked through the gerund table sf_form_careplan_order_to_careplan_orderp_item. They can be turned into a prescription (before they are dispensed) which is a form_careplan_order_rx record linked through rx_no. Their refills remaining count are store in form_careplan_order_rx.refills_remaining. You can check how many dispenses a prescription had by check form_careplan_order_rx_disp where rx_no = rx_no and status = 'Confirmed'. 

**Core Business Events:**
*   Parse inbound message from ss_log into the ss_message and its' subforms creating the entries in the database and returning the record id, or returning any errors during the parsing. This may including updating older related messages if we have a cancellation request or a respose to an outbound message.
*   Parse an oubound ss_message record by passing in the id of the record and return any errors or return the json representation of the message that can be sent.
*   Parse an ss_message to find the available actions available for that message by passing in the message id. Should return an array of available accounts which can be the following: resend, refill, followup, create_order, create_records, request_pa, request_clarification, request_change, request_provider_info, approve_cancel, deny_cancel, mark_reviewed
*   Parse an ss_message for missing form_patient, form_patient_diagnosis, form_patient_allergy, form_patient_medication, form_patient_prescriber, form_careplan_order, 
form_patient_address,
form_careplan_orderp_item and return a json representation of those forms that will be an object with the key being the form name and the value being either an array if multiple objects or an object with the key value pairs for the form.

**Replacement vs Updating a function:**
The functions in our .js files that we will be replacing the logic inside of SQL (not necessary with a new function but just having the same logic somewhere in our new SQL code) will be tagged at the top of our function header as REPLACE IN SQL, ones where we will need to update the function to handle our new SQL code will be tagged as UPDATE WITH SQL. 

**Coding Standards & Requirements:**
*   Strict adherence to naming conventions (`p_` for parameters, `v_` for variables, specified table aliases).
*   Mandatory data type casting (`::text`, `::numeric`, `::integer`, etc.) in all operations.
*   Implement robust error handling using `EXCEPTION` blocks and logging to the dedicated error tables.
*   Use `RAISE LOG` for tracing execution flow and decision points during development/debugging.
*   Include `deleted IS NOT TRUE AND archived IS NOT TRUE` checks for `form_` tables and `archive IS NOT TRUE AND delete IS NOT TRUE` for `sf_` tables in joins.
*   When using a `gr_` table link, there is no archived or deleted, the rows are deleted from the database if that value is not linked to that field.
*   Please make sure the column exist on the table (and that the table exists in the database) before using and validate any queries when you complete them.
*   When perform INSERT,UPDATES, or queries, make it one column per row to spread it out for easy readability and for INSERT statements, put the column name as a comment next to the value. 
*   Put comments throughout the implemenetation documenting any assumptions made or reference this spec. 
*   Document function headers for each function generates defining parameters and a description of the function.
*   If you are undecided on where to pull a value, please ask before proceeding.
*   To call our SQL functions or perform a query in our node.js code, we will be using this.db.env.rw.parseSQLUsingPGP which we pass in our SQL and our parameters. We will use %s to integer values and %L for string values. Make sure you cast their type in the SQL. Below is an example of how we would use it:

            const sql = `
            SELECT
                create_rx_fill_invoice(
                %s::integer,
                %s::integer,
                %s::integer,
                %s::integer,
                %s::integer
            ) as result
            `;

            const sqlParams = [
                insuranceId,
                payerId,
                rxRec.patient_id,
                siteId,
                rxRec.id,
            ];

            const rxChargesAndInvoicesRow =
                await this.db.env.rw.parseSQLUsingPGP(sql, sqlParams);

            if (rxChargesAndInvoicesRow.length === 0) {
                return ActionResponseWrappers.error(
                    `Error generating invoice record for the prescription. Rx ID ${rxRec.id}`
                );
            }
            const results = rxChargesAndInvoicesRow[0].result;

**Testing:**
We want to perform compresentive testing by inserting mock entries into the ss_log, you can use any existing id from form_patient to test with. You should be able to use some of the example files provided to generate test scenarios and then call the function to parse them and verify the ss_message was created. For the oubound, we can also generate mock scenarios and create ss_message records and test parsing the outbound message. Testing the generation presets of the records, we can test our view and make sure the results are as expected and have the proper columns for the form_* table we would be prefilling into with the proper types. 
---

