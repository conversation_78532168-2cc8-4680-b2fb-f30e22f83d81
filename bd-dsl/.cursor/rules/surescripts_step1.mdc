---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 1: Helper Files / Functions

*   **Action:** Create a function that given an ss_log id, will parse an inbound message and use our mapping 

*   **Action:** Execute SQL `CREATE TABLE ...` statement for `ss_error_log`.
Create only if they don't exist.

       CREATE TABLE ss_error_log (
           id BIGSERIAL PRIMARY KEY,
           error_message TEXT NOT NULL,
           error_context TEXT,
           occurred_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
           error_type TEXT,
           schema_name TEXT,
           table_name TEXT,
           table_id INTEGER,
           application_name TEXT,
           additional_details JSONB
       );

*   **Action:** Review our ss_mapping field and create the appropriate GIN indexes (if they don't exist) that we will use to map into our records. The current map is a "flat" json structure using flatten but we will not use that since we will be parsing the json directly using jsonb and query it directly to get the values that we need.
