---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 5: Testing

*   **Action:** We will be testing our new functions to parse inbound message, outbound messages, and calculate the available actions. For our inbound messages, you can check our old unit tests in nes/__tests__/surescripts and our mock entries in nes/.__mocks__/surescripts/inbound. The inbound mocks will be the raw xml messages so we will need to convert them first into their representation json objects and save those into a ss_log message first so we can use those records for the inbound conversion. We don't want to use the actual unit tests themselves, since they is co-dependent on the admin server which we don't have setup currently and want to isolate our testing to our SQL functions. We also have unit tests for our outbound as well and mocks. For the mocks, we can directly insert those into our database and then use the ids of those to test our outbound parsing. For the available actions, there are also unit tests for those. We can use our mocks and/or create new ones in ss_message to test the available actions.
