---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 2: Parsing an inbound message (File: `0134-138*.sql`)

*   **Action:** We will be replacing the inbound parsing logic starting at converter.js in function __convert_inbound on line 253. We should create a function (and its supporting functions, if necessary) that will take in an ss_log id and return either an error message, if we run into one during the processing, or it should insert the record into the ss_message table and its supporting subforms / gerund tables to link it and return the id of the new ss_message row. 
*  This is also where we should be trying to look up our links when creating the ss_message. site_id, patient_id, physician_id, pharmacy_order_id
