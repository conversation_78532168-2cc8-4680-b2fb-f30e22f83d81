---
description: 
globs: *.cson,*.sql
alwaysApply: false
---
The forms from our system are cson based and have fields, a model section (which determine access levels and can be ignored) and view section (how the form is displayed, which can also be ignored). dsl-defaults.coffee is our schema for the forms. All forms live under a base/cson directory or under the customer specific directory like csp/cson. The forms represent tables in our database where form_ is appended to the form name (i.e. ncpdp = form_ncpdp) and the fields represent the columns (for the ones where model.type != 'subform', i.e. ncpdp.pt_rel_code is the pt_rel_code in the form_ncpdp table). Fields are made up of model and view properies (you can ignore the view component expect for one thing mentioned below for this project). If no model is defined, it is just a normal text field, model.type defines the type of field: date is in MM/DD/YYYY format, datetime is in MM/DD/YYYY HH:mm a format, decimal is a floating point, int is an integer, time is in HH:mm a format, text is default if not defined and is just a normal text field and subform is a unique type that is a link to another form and the field is actually an array of other form entries (model.source represents the other form name). Pasted is an example billing_invoice for reference so you understand the structure both from the response after it is stored in the database and one that we are posting. Single links to other form values are defined using model.source for the other form and model.sourceid will filter down to the available values for that field. model.multi is either true/false and define if you can select multiple values (if type != 'subform'), and multiple value fields are stored as arrays of the values from the other form. I have included the values of form_list_ncpdp_ecl and form_list_ncpdp_ext_ecl as reference as we will be using them in testing, and I will tell you the test values we are putting in for the others. If sourceid: 'int' then the value will be stored as an integer which is the foreign key of the other form and if sourceid: 'code', then the value will stored as a string which is the 'code' value of the other form. If the type='subform', the value will always be an array of object with values from the other form. If multi, it means you can have multiple objects in the array whereas multi: false just means there can be only one value in the array. If multi:true and type = 'subform' then view.max_count (if set) represents the max count of objects in the array. model.prefill can be ignored for this project and model.required means the field is required or the form will be rejected. model.sourcefilter defines how we filter down to the available values based on either a static value (i.e. for the ones we are using here, ncpdp.transaction_code.model.sourcefilter only allows values from form_list_ncpdp_ecl where field = '103-A3'). model.max defines either the maximum string length or the max number for decimals and integers and model.min defines the minimum string length or minimum decimal or integer values. model.rounding defines how decimal numbers will be rounded when input (on the client, not during the POST). model.if defines our logic to show / hide form sections or fields depending on the set value which means if a field is required, it is only required if not hidden using that logic. When the if logic is defined using '*', it means any value in that field will trigger the condition. model.if with a require_fields array fields those fields will be required when that value is set. model.default defines a default value if not set for that field. Where ever you see a field.view.validate with a RegExValidator, it means the value most adhere to the regex defined in the pattern otherwise it is invalid. 

In the view section of the form (not the field), always add a dimensions property like this:
view:
	dimensions:
		width: '85%'
		height: '75%'

We want to set the width and height based on the number of fields in the form. If there are less than 10 fields in the form that will be visible (the field.view.offscreen != true), set the width to be 85% and height to be 55%. If > 20 set the width to be 85% and height to be 85%. If <= 4, set the width to be 55% and height to be 45%.

Every visible field should have a view.columns set. The max columns is 4 and we shouldn't mix even and odd sizes. If not defined, that means the default column size is 1. So you can have fields in the form with column size 1,2,4 or 1,3 but not 1,3,4 for example. Most comment boxes should have a column width of 1. 
We want to aim for 1,2,4 most of the time. The only caveat to this rule is when we have special fields like addresses. Where we have columns addr_1, addr_2, addr_city, addr_state, addr_zip. And small fields like modifiers which are only 2 characters where we have column set to 'modifier'. Things with _id at the end should default to a column size of 2 unless a site_id which can default to columns size of 4. If manually set to 4, don't override. Forms that have subform fields must have their view defined as sections_group and the subform field has to live in its own section by itself. We don't allow more than one section per group. For subform fields, we should always set the field.view.grid.width which is an array of integers that add up to 100. This are percentages that the fields will take up in the table view. Default anything with an _id to at least 20 or more if it will fit. Anything else can default to 15. No more than 7 fields should be defined in the view.grid.fields. 