---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 6: Handle parsing the payer responses into the proper tables.

*   **Action:** Now we are going to switch to handling the parsing of our electronic medical claims response (999, 277, and 835) on an after update trigger to the form_med_claim_resp entry. We want to make sure this doesn't prevent us from saving the form_med_claim_resp_log as if the parsing fails, we'll want to log an error so we can fix the problem. The 277 / 835 mapping has completely changed, the starting point will be in form_med_claim_resp_277 and form_med_claim_resp_835. One thing to note is that when we get in these 277/835 reports, they can be for batched claims. In the _meta for each field in the cson, you will find the path for that field if it is from the claim response and not derived. We will be splitting up the 277 status responses by claim number (unique patient_account_number) so one payer response can have mulitple 277 responses if we batched. That will be equal to the patient_control_number in form_med_claim_info.patient_control_number so we can match that up to find the claim. In the form_med_claim_resp_835, we will have a similar situation but will store the batch payment information as a seperate entry in form_med_claim_resp_835_batch which includes the top level entries for the batch of claim responses and then form_med_claim_resp_835 should be the unique entries in that response per claim, which is again, linked through the patient_control_number. 

In receiver.js, we should really only be using the parseClearinghouseResponse and parsePayerClaimResponse to store the med_claim_resp_log and we should have a trigger off of that to parse the response into its' proper tables

You'll need to update the form_med_claim status as well based on the status_code of the response. You can see how we are doing that in the [receiver.js](mdc:bd-dsl/nes/src/api/billing/medical/electronic/receiver.js) now.
