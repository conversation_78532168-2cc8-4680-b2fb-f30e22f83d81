---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 7: Integrating cleanup

*   **Action:** We have now integrated our postgres functions into the existing node.js application. The full functionality can be found in nes/src/api/surescripts/. We have action buttons that that control the responses and prefill in the data to the response records and we have a view that will generate the prefill data for new forms we can create from the message (see vw_ss_available_prefills). The piece that remains is when we send a message out, we get a status response back immediately which could be a Status, Verify, or Error response. Currently we are still handling that in the sender.js but we should be able to use our parse_ss_inbound function to handle this. We need to verify that it handles it the same way as we currently are. We can also get in a status / error response back through the normal process (and typically do, it will get sent and then later on, the receiving end verifies it and we get a verify response). We are also still using build_error_response in our helper.js to build the error response back to the admin server during our inbound message processing. This should really be part of the parse_ss_inbound response as an additional error_json property that we can send back to the admin server (we only do this during the inbound processing, outbound errors are sent back to the client for the user to correct). Some other areas we didn't handle yet, or at least verify they are handled in the sql code, are the validation checks in SSValidatorClass and SSCheckerClass. Note that in the check() function, we have to map the error response back to the raw path in the original xml json. You can see where we add the path (with the placeholders) in __check_managed_field but for example, if we found an invalid value for the qualifier field of the medication in the NewRx, you'd need the error to say 'Invalid value:${value} for path:Message.Body.NewRx.MedicationPrescribed.DrugCoded.ProductCode.Qualifier'. We also check our form_list_* managed table values during the check to verify they have a valid value. The only piece we really can't move up (in our validation checks) to SQL is the directory calls to fetch_service_levels to get the service levels of the sender/receiver and check if we can perform that action / accept that message before sending / during receiving. We also need to make sure our triggers are handling the updating of the status_icons correctly. For example, setting reviewed_by/reviewed_on should remove the 'new' icon. For the follow-up message, that is something we can either leave as-is or move up into the SQL processing (ideally). The way that works is we are sending the same message again but with the follow-up flags (see #Body.<MessageType>.FollowUpRequest) and keeping a tally of the follow-up count (as we can allow send a follow-up to a sent message every 48 hours if we don't have a response). Then we are making an entry in the subform_followup (through sf_form_ss_message_to_ss_followup) with the details of the new message_id, message_status (from the response) and any errors. 

I also was having issues with the prefill views, while they are generating some data, I wasn't seeing any with the patient data from any parsed messages. All the values were null, we should verify the data in the message is getting set correcting by testing a few inbound NewRx messages and checking the prefill responses from vw_ss_available_prefills. Ideally, I want to test an order too, you can set the patient_id to any patient record in the db to test with, that should allow you the option to in vw_ss_available_prefills for a 'careplan_order' record. We need to make sure none of the subform_single_order values are getting double escaped in the results because of how we nested the json_build_array and json_build_object. If so, we'll have to pass that as a seperate property for the form_careplan_orderp_item record in our view and then insert it under subform_single_order before saving. 

Finally, all of our functions in node.js should be camel-cased now. We did the previous code prior to implementing this, if you can please update all function names to be camel-cased.
