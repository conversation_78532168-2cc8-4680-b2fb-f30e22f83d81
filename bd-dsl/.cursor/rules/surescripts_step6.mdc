---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 5: Integrating into node.js

*   **Action:** We now will need to integrate our SQL functions into our existing node.js application. This means we will need to remove any functions / enums / files in the nes/src/surescripts/* that are no longer used and the logic is now being handled inside of hte SQL function. The functions I tagged in the comment with REPLACE IN SQL are the ones I was expected to have the logic moved to SQL. Please verify the logic is indeed in our SQL functions before removing. The ones where I tagged in the comment UPDATE WITH SQL are functions I expect to be updated based on our new SQL logic. Please review other functions not tagged as well and the enums to see if they are applicable any longer. We will not be used the available actions any longer as that field is now sunset, we will be using what is know as our action buttons for the ss_message form which is a now standard approach of handling showing buttons for forms. The way it works is that you have a file with the formname under nes/src/api/form/actions (i.e. there is already a skeleton setup in ss_message.js under there). That has a called getActions that we use to get the available actions/buttons for the forms. We have a bunch of wrapper functions in nes/src/api/form/actions/index.js that we use to create an array of those buttons and bass them back in the actions property with and options warning string. We can use that warning string for example if a message has a related CancelRx message and that is why there is no actions available to it and/or we can use the ss_message.warning which we should be setting (double-check this is getting set properly in the previous SQL files) but since this is a key-value pair in the ss_message.cson we'll have to map on our end. We will primarily be using the iconActionButtonWrapper which shows in readonly mode for a message at the bottom of the form. When a user clicks on one of those actions, it calls the runAction function which is where we would confirm the action is valid for that message or return an error and then we perform the steps to run the action. In our scenario, this is where we want to build the prefill using our function for that new message type and have them populate it and post back to our actions to handle the sending part. That will be the ActionResponseWrappers.create wrapper that we use and then the json blob for the form will come back in the postAction function. We can then attempt to save that record and then use our new logic to attempt to send the message, handling any errors along the way and returning the results back to the user if the message was successfully sent or an error message with any errors during the sending process. Note that some of these records have links to others in there so please review the *.cson files to determine the linking/dependencies and ask questions if you are unsure. One particular one that is different is the form_careplan_orderp_item.payer_ids which is going to be a json array of the ids from form_patient_insurance, please query the example entries so you can see how existing form_careplan_orderp_items are formatted, same for the other files we are going to create, there are plenty of examples in the database already. Also note in our *.cson files we have the SelectPrefill transform on alot of the fields. This is a transform we use to map the fields from the related for to the existing form, we will need to handle that logic as well since it won't be run and we will be creating the records in our SQL function(s). There are plenty of examples of how we are using the action buttons workflow in the nes/src/api/form/actions directory and I created helper files for alot of them in their respective workflow sections like in nes/src/dispense/action.js and nes/src/api/dispense/actions/ and same for nes/src/api/billing/action.js and nes/src/api/billing/actions/. That helped split out the logic for us. If the nes/api/form/actions/ss_message.js will be over 600 lines, we should do the same and process the individual actions (the buttons, actions, and posts) under nes/src/surescripts with a actions.js file and then split under an a actions folder with buttons.js, actions.js, and posts.js
