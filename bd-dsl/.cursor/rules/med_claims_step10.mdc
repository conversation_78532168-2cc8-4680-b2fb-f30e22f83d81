---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 10: Build unit tests

*   **Action:** Now that we have our full medical claims logic build we are going to want to the following unit test files under 
/Users/<USER>/Documents/EnvoyCore/bd-dsl/base/post/tests

0015-test-mm-claim-generation.sql
- We are going to want to insert form_ledger_charge_line entries for at least 3 different types of form_inventory records, a drug (form_inventory.type = 'Drug'), a equipment rental (form_inventory.type = 'Equipment Rental') and a billable (form_inventory.type = 'Billable'). Make sure to populate all the required fields based on the dsl cson and you can query the database to get mock values / ids to put in. You can even use the create_ledger_charge_line function to get the values to insert with the test ids that you provide but just know you'll still have to set the rental_type = 'Rental', frequency_code, date_of_service, date_of_service_end. Now we want to build a with a medical claim so you'll have to either use an existing form_payer record where form_payer.billing_method_id = 'mm' or you'll need to create one, if you create one, make sure you turn on all the mm_* settings to maximize our "kitchen sink" test. Also make sure all the required fields are populated including mm_payer_name which is our clearinghouse payer name. You'll also want to either assign that payer to a patient using form_patient_insurance or you'll want to use an existing patient already tied to that payer. You'll other records for the test like form_careplan_order, form_careplan_order_rx, etc. You'll need to review the code to see all the entries you'll need to make for testing. We want to maxmimize the amount of fields that will get populated for this particular test. This will NOT be our COB test, we'll do that next so you don't need to mimick a 277 or 835 response. 

The first function we will test is build_mm_claim(). This should build the expected output mm_claim_record composite record. We need to assert each value that have in our test records with the output in that record to make sure they got entered correctly. 

The second function we will test is mm_record_to_json which will take that composite record and turn it into a json object. We will want to assert that each value was properly inserted into the json object and is of the correct type. 

After you create this, I want you to deploy it and run it and check for any errors and attempt to fix any you find. If you are questioning the results, please ask before fixing anything but if it is clearing wrong, fix it (i.e. referencing an invalid field, errors in the function where we missed a delcare for variable, etc)

0016-test-mm-cob-claim-generation.sql
We will now be handling the complex situation of generating a COB claim, we want to handle testing 3 scenarios here, where the parent claim is a form_ncpdp claim, where the parent claim is a form_med_claim_1500 claim, and finally one where it is a form_med_claim with claim level and service line adjustments. You'll have to insert claim responses (at least a form_med_claim_resp_835 and form_med_claim_resp_835_batch) to test this scenario. Similar to the previous test, we want to verify all the fields were populated in the compsite type and then we want to convert that to json using mm_record_to_json and verify all the fields made it over.

I'd like to also test that all the fields returned to us in mm_record_to_json are actually on the form_med_claim*.cson records that we will be inserting. One way of doing this is to use the dsl table where key = form name and value = json representation of the *.cson form. You can look at the fields property from that to get the list of fields. 

Now I want you to test the mm_build_claim_json function in 0163-mm-preprocessor. For this you'll want to match the output 
from the ClaimsV3.json / ClaimSubmissionRequest and make sure the final json output has the fields of the correct type (object vs array etc) and assert all of our values from the claim record made it over. 

After you create this, I want you to deploy it and run it and check for any errors and attempt to fix any you find. If you are questioning the results, please ask before fixing anything but if it is clearing wrong, fix it (i.e. referencing an invalid field, errors in the function where we missed a delcare for variable, etc)

0017-test-1500-claim-generation.sql
Now we will want to switch gears to test our form_med_claim_1500 generation. To do this, we will need to once again create charge lines and a payer with a billing_method_id = 'cms1500' or use an existing one. We will be using build_mm_1500_claim and once again, make sure your records are maximizing the amount of information that will go on the claim. This should return us a mm_1500_claim_record composite type record that we can assert all the values on there. After that, we will be testing mm_1500_record_to_json to turn it into a json object matching the form_med_claim_1500* fields and assert that all of our values made it over. 

I'd like to also test that all the fields returned to us in mm_record_to_json are actually on the form_med_claim*.cson records that we will be inserting. One way of doing this is to use the dsl table where key = form name and value = json representation of the *.cson form. You can look at the fields property from that to get the list of fields. 

After you create this, I want you to deploy it and run it and check for any errors and attempt to fix any you find. If you are questioning the results, please ask before fixing anything but if it is clearing wrong, fix it (i.e. referencing an invalid field, errors in the function where we missed a delcare for variable, etc)

0018-test-nursing-charge-line-generation.sql
Next up, we will be testing the create_nursing_visit_charge_line_json generation. You'll need to create a form_encounter with the proper information (including the record linking to either mock or existing records) to trigger the charge line generation. Make sure that the finally values were put into the form_ledger_charge_line using asserts after the trigger fired. 

After you create this, I want you to deploy it and run it and check for any errors and attempt to fix any you find. If you are questioning the results, please ask before fixing anything but if it is clearing wrong, fix it (i.e. referencing an invalid field, errors in the function where we missed a delcare for variable, etc)

0019-test-dme-line-generation.sql
Finally, we will be testing our DME charge line generation. You can review 0014-test_dme_rental_billing for how this is working but we won't be able to test the PG boss event but we can simulate it if you look at what we do in /Users/<USER>/Documents/EnvoyCore/bd/nes/src/core/modules/pgboss/dme_manager.js. So we'll create the proper test records to ensure that we can test all the edge cases (i.e. patient is not active, we hit the maximum number of claims, etc) and try to insert the generate charge line data into form_ledger_charge_line record.
As a bonus, we'll want to make sure that new charge line shows up in our "ready to bill" queue which is defined as the vw_ready_to_bill view results found in /Users/<USER>/Documents/EnvoyCore/bd-dsl/base/post/operations/0015-workflow-views.sql. 

After you create this, I want you to deploy it and run it and check for any errors and attempt to fix any you find. If you are questioning the results, please ask before fixing anything but if it is clearing wrong, fix it (i.e. referencing an invalid field, errors in the function where we missed a delcare for variable, etc)
