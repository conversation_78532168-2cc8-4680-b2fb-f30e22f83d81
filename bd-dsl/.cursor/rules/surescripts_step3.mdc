---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 3: Parsing an outbound message

*   **Action:** We will be replacing the outbound parsing logic starting at converter.js in function __convert_outbound on line 353. We should create a function (and its supporting functions, if necessary) that will take in an ss_message id and return either a json object of the converted outbound message or an error if we are unable to convert / run into a validation error. Note that we will still have to use our node.js function to validate any directory issues. We should break up the sql files by type so we should have one for RxRenewalRequest, CancelRxResponse, and RxChangeRequest and another one for the outbound parsing call. If you need another for helper functions, you can add that before all the others.


