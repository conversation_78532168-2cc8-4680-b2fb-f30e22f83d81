---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 5: Handle billing for DME equipment
*   **Action:** Now we are going to handle the workflow for billing dme rentals (not purchases, those happen normally through the delivery ticket process). The way this is going to work is if a patient has a rental checked out, there will be an entry in the form_inventory_rental_log where patient_id = patient_id. The ticket_no and ticket_item_no will be set on the form_inventory_rental_log if checked out to point to the delivery ticket item related to it (form_careplan_dt_item) which has an rx_id for the related prescription record (form_careplan_order_rx). If they do have one checked out, then we are going to want to attempt to pull the rental_insurance_id from vw_order_raw where vw_order_raw.rx_id = rx_id but before that, check the original form_careplan_dt_item and look for an insurance_id and use that if set. 
If you still don't find it, you'll have to attempt to look it up through form_patient_insurance where active = 'Yes' and billing_method_id IN ('mm', 'cms1500') and then form_payer.cover_dme = 'Yes'. If you still don't find one, then you can ignore it and we won't generate the delivery ticket item. 

To get the inventory_id of the rental billable item, we can first look at form_careplan_order.rental_billable_id which we can get to through the form_careplan_order_rx where order_no = form_careplan_order.order_no. If that is not set, then we need to get the billable_code_id from the form_inventory record from the rental log, and then get the associated billable, form_inventory.billable_code_id = billable_code_id and type = 'Billable'. If billable_code_id is set on the equipment rental item but has no associated 'Billable' inventory item, then just use the form_inventory record for the device. 

Basically what we want to do here is use pgboss to setup a "billing schedule" for our rentals. You we'll need some work in both the node.js side (see /nes/src/api/query files where we create the queue to refresh our views on a schedule). We should trigger this based on a trigger when our form_inventory_rental_log is updated to be checked out by a patient and the rental_type is 'Rental' in our form_careplan_dt_item. The rental day starts on the form_careplan_delivery_tick.delivery_date (or checked_out_date from the form_inventory_rental_log if null). How we determine the schedule will depending on the payer settings:
If form_payer.no_recurring_billing = 'No' then we don't generate any charges for the rental
If form_payer.bill_recur_arrears = 'Yes' means we bill for the PREVIOUS date of service days and we'll want to generate a billable the first of every month for the previous month's covered days. 

Just note that on the first dispense, even if we have a frequency code indicating monthly, we will have a fractional charge amount and if we dispense on the first day of the month and we bill in arrears, you will wait until the 1st of next month to bill the previous month's fractional amount. If we don't bill in arrears, then we we will be billing for the service days up front so you will bill for the entire month (or remainder of the month based on the delivery_date). 

There are a few conditions regarding when we stop/not bill: 
- If the item is checked backed in (form_ledger_rental_log_activity will have an entry where previous_patient_id = patient_id and patient_id IS NULL, localized_datetime is when the activity took place) (note: sometimes rentals are checked back in for maintenance, the patient may get a "loaner" for we will have to check for that scenario when going to bill so we still bill for the entire month, you'll have to use the form_ledger_rental_log_activity to see the dates that the user checked out a device a device).
- If we reach the max number of claims (get_inventory_pricing will return a max_rental_claims but we should default to 18 if not set or 13 if form_payer.type_id IN ('MCRB','MCRD')). You'll also want to check the prior auth (form_patient_prior_auth, look for it in a similar fashion that we did for the nursing visit, similar to the nursing visits, is we will want to look up any form_patient_prior_auth entries where insurance_id = rental_insurance_id, it is active (status_id = '5') form_patient_prior_auth where pa_type includes 'DME' (pa_type will be a string representation of a json array so just search for like '%DME%') and rental_coverage_type = 'Rental'.) We can check the form_ledger_charge_line for the same inventory_id of the calculated 'Billable' record that we are using for the rental. 

- The patient is discharged (form_patient.status_id NOT IN ('1','3')) (unless billing in arrears, then you could be billing for the previous month before discharge). You can tell when the patient went off service by checking the form_ledger_patient_activity log where patient_id = patient_id and field will be 'status_id' and old_value IN ('1','3') and new_value NOT IN ('1','3') and the localized_datetime will be a timestamp of the date of the status change. 

Our frequency_code should help us determine the charge quantity / charge quantity each. So if we are billing 'Monthly' then we should bill either 1 for the entire month, or our fractional amount (rounded to 2 decimal places). Likewise for 'Daily' or 'Weekly' (the charge code we are using will be reflective of the frequency_code). The bill quantity in this case will be the same as whatever we calculate for our charge quantity.

Regarding the pricing amounts, if we have a prior auth, we might have rental_price_approved set. If so, that will be the amount per charge quantity which should override our default billed and expected amount on the charge line. Otherwise, our call to get_inventory_pricing returns rental_expected_daily_ea, rental_expected_monthly_ea, rental_bill_daily_ea, and rental_bill_monthly_ea. Depending on our frequency depends on how we use this, rental_expected_daily_ea * number of days if we are billing for daily (for weekly, we still use the daily rate * # of days but the charge quantity / bill quantity is # days / 7), for monthly, rental_expected_monthly_ea * # of days / 28. This will go into the form_ledger_charge_line.expected. For the billed amount, we will use the rental_bill_daily_ea, or rental_bill_monthly_ea.  
We'll want to update our create_ledger_charge_line logic accordingly. 

*   **Action:** Build a test .sql file that will setup a patient to be due for a reoccuring rental and verify the view that we created will properly show it is due.
