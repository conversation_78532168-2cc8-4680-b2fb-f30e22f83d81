---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 8: Integrating cleanup

*   **Action:** We want to perform the following cleanup on our node.js code
* Move logic in validator.js into a seperate file/function in our postgres .sql files to validate the outbound message before moving forward that checks all the same validation as validator.js and returns an error or null if successful. We should have a function still in node.js to get the service levels that we can pass into the postgres function. Note that we will have to escape the array values like we do here: 

            const rxwIdsArray = this.fx.pgArrayFormat(deliveryTicketRec.rx_id);
            const sql = `
                SELECT 
                    working_dispense_id
                FROM vw_working_prescriptions wrx
                WHERE wrx.rx_id = ANY('${rxwIdsArray}'::integer[])`;

            const rows = await this.db.env.rw.parseSQLUsingPGP(sql);

The checker.js shows how we get the service levels and checks them using this.directory.fetchServiceLevels(spi). The expectation is the service levels would be a ::TEXT[] object we can pass up to our function.

* Move the logic from checker.js into a postgres function that we can use to run the same checks currently that we run when calling check(). This would include the __checkManagedField call:
NOTE: We have to turn the JSON path in dot notation if we find an invalid value. So all the fields in the form that have model:source as a foreign key, we have to check that form_list_* where code = {value} to see if it is valid. Otherwise, we have to return an error 'Invalid value:${vlaue} for path:${path}'. (i.e. path:Message.Body.NewRx.MedicationPrescribed.DrugCoded.ProductCode.Qualifier)

__buildFieldChecks and __checkAndConvertField should be handled during the parsing of the inbound message based on the type defined in model.source. The easiest way to handle this is to build a map of the types in an enum type for each form_* that we are inserting into in the 0133-ss-custom-types file and then use that to determine how to convert the field (if not null) or throw an error if can't be converted to the correct type with the path. 

* Reduce dependency on helper.js. Since we are moving the checks up into the SQL functions, we should be able to remove isCanceled since that should be handled in there. I would like to get rid of fetchOg, fetchRelatedOg, and fetchAllOg. We should be able to move the logic where it is used or at least make a SQL function to get the record id to update in node.js. Ideally, it would be removed though. 

For convertFields, as long as the outbound is running the covertion for the fields in SSConversionFields then we should be fine to remove this and its' usage. For converting datetime/date/time fields, we don't need to do that any longer, the client is going to handle that for us and leave the datetime stored as UTC. For boolean values, we just need to store the true/false as "true"/"false" and convert back on the oubound. The only fields we need to worry about converting to a string/number and back is severity_code_id and coagent_qualifier_id. We have a few fields which need to be an array of strings on the outbound (which use the gr_ tables: renewal_denial_reason_code, chg_type_sc_id, vr_code_id, service_res_id, dr_code_id, and drug_cvg_status_id, we may already be doing that but need to verify)

* In sender.js, we should move the logic for parsing a sent message response to just be handled by receiver.js (so we take the response and pass it up through an instance of receiver.js to be handled like a normal inbound). The validation we moved over should also be happening in SQL (we can pass up the directory information for the service provided). This means we may need to beef up our inbound message handling for follow-up responses so it updates the existing record in that case and inserts into the subform for follow-ups on the original message like we are currently doing in the node.js file(s).

* In receiver.js, we should be handling the status responses by default in our new postgres parse_ss_inbound function so the check at top using __handleResponseMessage we should be able to remove if we have feature parity down that path. We don't need to run the standard checks like we are doing in __covertInbound though on a status response so will need to update that. In __handleInboundErrorResponse this really should be handled already with the rest of the status responses in parse_ss_inbound.

* In directory.js, we should be able to remove the logic where we are setting ss_organization_specialty to the view vw_surescripts_directory_site_info. 
