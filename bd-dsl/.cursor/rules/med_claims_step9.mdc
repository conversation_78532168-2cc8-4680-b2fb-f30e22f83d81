---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 9: Major Medical claim form triggers

*   **Action:** We have some triggers/1 pg-boss event that need to happen to keep things in sync on the claim. You can add them to 0163-mm-triggers.sql

The first trigger is that if form_med_claim has any adjustment entries (form_med_claim_adj) linked through from_med_claim_osub through the sf_ tables which is linked through form_med_claim_info through the sf_ tables then we need to set form_med_claim_opayer.other_payer_claim_adjustment_indicator = 'Y', otherwise set to NULL which is linked through the form_med_claim_info through sf_ tables which is linked to form_med_claim through the sf_ tables
Both form_med_claim_osub.cob_payer_id should = form_med_claim_opayer.cob_payer_id to match to the correct entry.

We need a pg boss event that will register to mark the form_med_claim or form_med_claim_1500 substatus_id, if in '114' (on-hold for date of service) as '101' (Ready to billing) after the date_of_service_end date on the related form_billing_invoice. I'm including our current logic for the dme_manager which is how we schedule a job to run daily at 6am to check for rentals that are due, we can follow a similar pattern and create a view to find claims where the form_med_claim.service_date >= CURRENT_TIMEDATE::date or form_billing_invoice.billing_method_id = 'cms1500' and then you can get the claim from there using form_med_claim_1500.invoice_no = form_billing_invoice.invoice_no AND form_billing_invoice.date_of_service >= CURRENT_TIMESTAMP::date. 

We also need to keep the modifiers on the service line (form_med_claim_1500_sl and form_med_claim_sv) in sync with form_ledger_charge_line.modifier_1, modifier_2, modifier_3, and modifier_4. The user will only be editing them on the form_ledger_charge_line so needs to only go one way. Good news here is form_med_claim_sl.charge_no = form_ledger_charge_line.charge_no so it is simple to change. Then you just need to use the sf_ join for form_med_claim_sv from the form_med_claim_sl.

We need to keep diagnoses in sync between form_med_claim_sv entries and form_med_claim_dx entries. Can you put a trigger on med_claim_sv to make sure on save that dx_id_1, dx_id_2, dx_id_3, and dx_id_4 have a corresponding entry in the form_med_claim_sv table (remember you add an entry in the sf_ table). If one of the above values have changed, you'll need to see if any form_med_claim_sv entries have that dx_id_* listed and if not, you can set the corresponding form_med_claim_dx entry to archived and the corresponding sf_form_med_claim_info_to_med_claim_dx entry to archive = TRUE. Remember you join through all the sf_ tables when checking so you can get back to the main form_med_claim_info where the form_med_claim_dx is linked and how you can get to the other service lines. 

The other thing we need to do is keep the service dates in sync if the form_ledger_charge_line has rental_type = 'Rental' defined (other charge lines won't have the date_of_service/date_of_service_end date defined). You can get to their claim related claim charge line where form_med_claim_sl.charge_no = form_ledger_charge_line.charge_no. 
form_med_claim_sl.service_date = form_ledger_charge_line.date_of_service and form_med_claim_sl.service_date_end = form_ledger_charge_line.date_of_service_end. For the 1500, we will be looking at form_med_claim_1500_sl.service_date and form_med_claim_1500_sl.service_date_end A change in either direction should sync but we should ignore the trigger if the dates match and not update (to avoid looping). 

We need to also be able to sync adding new form_ledger_charge_line items to the claim (through claim_no) which should add the service line directly into the proper tables and recalculate the aggregate values. So if you add a charge line, you are going to add the entry under form_med_claim_sl or form_med_claim_1500_sl (or if COALESCE(form_ledger_charge_line.void,'No') = 'Yes' or (form_ledger_charge_line.zeroed,'No') = 'Yes' or form_form_ledger_charge_line.archived = TRUE then we set the archived flag on the related claim entry on the claim form (form_med_claim_sl or form_med_claim_1500_sl where charge_no = charge_no) and in the sf_ table and we are "removing" that charge line.). Note that once either one happens, you are going to have to update the aggreigate fields that relate to all claims, that includes the form_med_claim/form_med_claim_1500.expected, form_med_claim/form_med_claim_1500.billed, form_med_claim/form_med_claim_1500.copay, form_med_claim_info.claim_charge_amount (sum or existing charge lines billed where form_ledger_charge_line.invoice_no = form_med_claim.invoice_no). If form_med_claim_contract exist as a subform entry for form_med_claim and contract_type_code is set, then we also want to update form_med_claim_contract.contract_amount = billed amount (for all charge_lines). One note here, if you are actually removing a charge line, then we need to reset all form_med_claim_sl.assigned_number and form_med_claim_sv.assigned_number to be sequential starting at 1 starting at the number after the existing form_med_claim_sl.assigned_number and form_med_claim_sv.assigned_number that we are archiving.

We also need to keep any rental charge line info in sync. This means if the form_ledger_charge_line.frequency_code is updated. So if form_med_claim_sl (linked again through charge_no) exists then we want to look if it has a form_med_claim_dme subform defined where frequency_code is not null and <> form_ledger_charge_line.frequency_code then we want to update it.

If form_ledger_charge_line.hcpc_code changes, we'll want to update the associated form_med_claim_sv.procedure_code or form_med_claim_1500_sl.procedure_code = form_ledger_charge_line.hcpc_code or where form_med_claim_sv/form_med_claim_1500_sl.charge_no = charge_no (unless form_ledger_charge_line.compound_no is set!)

If form_ledger_charge_line.ndc changes, we'll want to see if there is a corrolating entry in form_med_claim_sl_di.national_drug_code if form_med_claim_sl_di.national_drug_unit_count is not null (where charge_no = charge_no).

If form_ledger_charge_line.metric_quantity changes, and if there is an associated entry where form_med_claim_sl_di.national_drug_unit_count is not null (where charge_no = charge_no), set form_med_claim_sl_di.national_drug_unit_count = metric_quantity.

f form_ledger_charge_line.charge_quantity changes, we'll want to sync up to form_med_claim_sv.service_unit_count or form_med_claim_1500_sl.service_unit_count

If form_ledger_charge_line.description changes, we'll want to update form_med_claim_sv.description where form_med_claim_sv.charge_no = charge_no

If form_ledger_charge_line.billed amount changes, we'll want to update form_med_claim_sv.line_item_charge_amount / form_med_claim_1500_sl.line_item_charge_amount and possibly form_med_claim_dme.rental_price if it is a rental and frequeny_code is set on form_med_claim_dme (linked through charge_no)

If form_ledger_charge_line.inventory_id changes, we'll update form_med_claim_sv.inventory_id (if compound_no is null) or form_med_claim_1500_sl.inventory_id and possibly form_med_claim_sl_di.inventory_id. Also this might have change the billing_unit_id and if there is an entry in form_med_claim_sl_di, this means you'll need to map in the new value using this map: 
    CASE
      WHEN p_inventory_rec.billing_unit_id = 'gram' THEN 'GR'::text
      WHEN p_inventory_rec.billing_unit_id = 'mL' THEN 'ML'::text
      ELSE 'UN'::text
    END::text as measurement_unit_code

If form_ledger_charge_line.date_of_service or date_of_service_end is updated, we want to calculate the number of days (inclusive) between the start and end date and if different, update form_med_claim_dme.days. 

