---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 9: Handling inbound errors

*   **Action:** We want to perform the following checks in parse_ss_inbound and return the appropriate error_json_response when we run into them. Note the format of the error_json_response should be as followings: {
            Message: {
                Header: {
                    RelatesToMessageID: xml_json.Message?.Header?.MessageID,
                    To: xml_json.Message?.Header?.From,
                    From: xml_json.Message?.Header?.To,
                    SentTime: moment().utc().format("YYYYMMDDHHmmss"),
                    PrescriberOrderNumber:
                        xml_json.Message?.Header?.PrescriberOrderNumber,
                },
                Body: {
                    Error: {
                        Code: sserror.TransactionErrorCode,
                        DescriptionCode: sserror.DescriptionCode,
                        Description: sserror.Description,
                    },
                },
            },
        }
* **Requirement:** All Description values must contain the path to the incorrect value in the XML/JSON. 

* If the site is invalid, (it doesn't match form_site.ss_organization_id or form_site.ncpdp_id) then Code = '601' and DescriptionCode = '210' and Description = 'Invalid receiving site identifier, does not match any given site at location. Value: {Message.Header.To} Path: Message.Header.To'

* If the message is a duplicate (we already have a form_ss_message record with the same message_id) then Code = '601' and DescriptionCode = '220' and Description = 'Message is a duplicate, did not process. Value: {Message.Header.MessageID} Path: Message.Header.MessageID'

* If the message type is unsupported by the receiving site (look in form_site.ss_service_level for supported service levels, it is a multi field so must be joined through the gr_ table and has the following map: New = NewRx, Refill = RxRenewalResponse, Change = RxChangeResponse) if an CancelRx, always let it through. If unsupported then Code = '700' and DescriptionCode = '4040' and Description = 'Receiver does not support this message type. Value: {Message.Body.{MessageType}} Path: Message.Body.{MessageType}'

* If the prescriber is missing a DEA number of the medication is a controlled substance for NewRx messages. Look at Message.Body.{MessageType}.Prescriber.NonVeterinarian.Identification.DEANumber for the prescriber DEA number and Message.Body.{MessageType}.MedicationPrescribed.DEASchedule to get the DEA schedule for the medication. If set, must contain prescriber DEA. If missing then then Code = '601' and DescriptionCode = '210' and Description = 'Prescriber DEA number is missing but medication is a controlled substance. Value: Message.Body.NewRx.Prescriber.NonVeterinarian.Identification.DEANumber Path: Message.Body.NewRx.Prescriber.NonVeterinarian.Identification.DEANumber'

* For a NewRx message, if the written date is > 365 days ago, or expiration date has expired, (Message.NewRx.MedicationPrescribed.OtherMedicationDate.OtherMedicationDateQualifier = 'ExpirationDate' AND Message.NewRx.MedicationPrescribed.OtherMedicationDate.OtherMedicationDate.Date <= Today) then Code = '601' and DescriptionCode = '210' and Description = 'This prescription has already expired'. Value: Either expiration or written date Path: Path to invalid date

* If handling a RxRenewalResponse message for a controlled substance, the message must be digitially signed. You can check the digitial signature in the Message.Header.DigitalSignature.DigitalSignatureIndicator (should be set to 'true'). If not true, then Code = '601' and DescriptionCode = '210' and Description = 'This prescription contains a controlled substance but is missing a digital signature'. Path: Message.Header.DigitalSignature.DigitalSignatureIndicator

Other items that should be handled in the inbound message.
* For a RxChangeResponse, if ProhibitRenewalRequest was set on the original message but not on the response, then that value should copy forward to the RxChangeResponse

