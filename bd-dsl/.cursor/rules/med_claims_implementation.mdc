---
description: 
globs: 
alwaysApply: false
---
```markdown
# MDC Rule: Surescripts Implementation Plan

**Rule ID:** MEDCLAIMS-IMPLEMENTATION-V1-DETAILED-FINAL
**Version:** 1.0
**Date:** 2025-05-02
**Status:** Finalized

## 0. Project Description & Context

**Goal:** Refactor our current logic for handling electronic and paper (CMS 1500) medical claims, which is currently done in node.js, and move the majority of the logic up to postgres SQL functions which can be called from our node.js app. We previous did the same for our pharmacy (NCPDP) claims and now need to handle the 837p medical claims. 

**Workflow:** Currently, when we have a patient that is going to be billing a medical claim for their medications, DME equipment, per-diem supplies, or nursing visits, we do that after the shipment has been completed (or nursing visit has been signed off on by the nursing manager). The shipment itself is logged on a delivery ticket (form_careplan_delivery_tick) which has items (form_careplan_dt_item) linked through ticket_no. This items will have work ticket entries to determine what stock was pulled from the system (form_careplan_dt_wt_pulled), linked through ticket_item_no and ticket_no. After the ticket has been confirmed as shipped (form_careplan_delivery_tick.status = 'confirmed') then we generate our charge lines for our medical claim (but not the invoice yet!) (form_ledger_charge_line) which links to our insurance record (form_patient_insurance through insurance_id) and payer record (form_payer through payer_id). Those will set in a queue called 'Ready to Bill' until a biller generates an invoice (form_billing_invoice) for them, which they do through a creation view. This is the stage where we come in, this generation point is in our node.js application in /Users/<USER>/Documents/EnvoyCore/bd/nes/src/api/billing/generator.js generateSplitChargeCreateInvoiceView(). Currently our invoice generation only supports ncpdp payers where form_payer.billing_method_id = 'ncpdp' but we will need to handle the electronic medical (837p, form_payer.billing_method_id = 'mm') form and paper medical claim (CMS-1500, form_payer.billing_method_id = 'cms1500'). After the generation of the invoice, depending on how the payer is setup, the claim may automatically go into a hold status (if the form_payer.mm_hold_claims_dos_end = 'Yes' then we have to wait until the end of the date of service, form_billing_invoice.date_of_service_end before sending the claim). Once ready to submit (if electronic medical) we will submit the claim through a call to our 'admin server' after we have run our modifications to the claim, and verified it through our JSON schema using AJV. The code that runs and validates the claim can be found in /Users/<USER>/Documents/EnvoyCore/bd/nes/src/api/billing/medical/electronic/runner.js. From there, depending on the response, we may get an error from the clearinghouse (Change Healthcare), or the admin server, or we may have found one during the validation process. We way, we attempt to let them client know which field was causing the error through a mapping class and bring that error back up to the top to display to the user. The code that processes all response for the electronic medical claim can be found in /Users/<USER>/Documents/EnvoyCore/bd/nes/src/api/billing/medical/electronic/receiver.js. That is also an endpoint that the admin server calls when we have a new response for a medical claim from a payer, either a status response (277) or the full 835 response. We should have a view which will show all the 835 or 277 reports that have no been reviewed, and provide a function that will help us process our cash posting from and 835 by breaking down the payments per invoice / charge line. The cash posting forms are the form_billing_cash and form_billing_cash_cl (to link the charge line and amount to the form_billing_cash entry). We want 2 views for this, one which shows the total paid amount (From the 835) with invoice details (the same as found on the vw_ar_manager_invoice_summary view but with the paid amount coming from the claim response) and the charge line details (same as from vw_ar_manager_chargeline_summary but with the paid amount coming from the claim response). We will show this in a "split view" that the user can confirm the amounts before making the cash entries (which we will make an endpoint for under our /Users/<USER>/Documents/EnvoyCore/bd/nes/src/api/billing/index.js endpoint), I would also add entries in there in case they want to make adjustments which would go on our form_billing_adjustment and form_billing_adjustment_cl tables. One caveat to mention here is we might have a bill for denial case (flagged in form_patient_insurance.bill_for_denial = 'Yes' which gets copied over to the form_billing_invoice) which means we pull the pricing for the next payer in line and submit it with that (so that is done at the charge line generation level). 

**Key Components:**
*   Please generate all .sql files under bd-dsl/base/post/operations and any tests under the /Users/<USER>/Documents/EnvoyCore/bd-dsl/base/post/tests folder.
*   **Schema Records** For our electronic medical claims, we have 3 json schemas available from the clearinghouse, the Professional_Claims_3-2.json, the 277.schema.json for the status responses, and the 835.schema.json for the 835 payer responses. 
The mapping for the 837p claim correspones to the med_claim.cson (form_med_claim) form in our system and its respective subforms (all starting with med_claim_*.cson). All med claim responses (999 from the clearinghouse, 277 status report from the payer, or full 835 admittance report of final payment or denial) are all first stored in form_med_claim_resp_log where we have the response_raw_json which is stored as a string in the database of the full json response found in the schemas, and the response_meta and the original request in request_raw_data. You can see the mapping logic we have there to may the response to the invoice (or invoices) and we already have some logic in node to map the payments but definitely not everything that should be there. We attempt to map it to their response in our form (form_med_claim_resp, form_med_claim_resp_277, or form_med_claim_resp_835). The local json schema we use for AJV can be found in the following location: /Users/<USER>/Documents/EnvoyCore/bd/nes/src/core/schemas/837p/* which may help you better understand the structure. Note that we do handle some manipulation of the form before running through it though. 

*   **Relevant Tables:** 
The following cson file are representations of our form_med_claim* tables in our database and are what the user fills our for the 837p form (and what we auto generation from the form_ledger_charge_lines to put on the form_billing_invoice):
med_claim
med_claim_address_bprov
med_claim_address_dep
med_claim_address_fac
med_claim_address_opay
med_claim_address_oprov
med_claim_address_osub
med_claim_address_pay
med_claim_address_rend
med_claim_address_rprov
med_claim_address_sprov
med_claim_address_sub
med_claim_address_sup
med_claim_adj
med_claim_adj_dl
med_claim_adj_sl
med_claim_adj_sl_dl
med_claim_cond
med_claim_contact_bprov
med_claim_contact_oprov
med_claim_contact_rend
med_claim_contact_rprov
med_claim_contact_sprov
med_claim_contract
med_claim_dates
med_claim_dep
med_claim_dep_cont
med_claim_dme
med_claim_dme_cert
med_claim_dme_cmn
med_claim_dme_cond
med_claim_dx
med_claim_facility
med_claim_file
med_claim_info
med_claim_info_other
med_claim_note
med_claim_obill_id
med_claim_ofac_id
med_claim_ofacility
med_claim_opayer
med_claim_opayer_id
med_claim_oprov_bill
med_claim_oprov_ref
med_claim_oprov_rend
med_claim_oprovs
med_claim_oprov_sup
med_claim_orend_id
med_claim_osub
med_claim_osub_nm
med_claim_prov_bill
med_claim_prov_ord
med_claim_prov_ref
med_claim_prov_rend
med_claim_provs
med_claim_prov_sup
med_claim_receiver
med_claim_report
med_claim_reprice
med_claim_reprice_sl
med_claim_rprov_id
med_claim_sl
med_claim_sl_adj
med_claim_sl_di
med_claim_sl_dt
med_claim_sl_fi
med_claim_sl_file
med_claim_sl_ref
med_claim_sl_ref_pa
med_claim_sl_ref_rn
med_claim_sl_sup
med_claim_smt_cont
med_claim_sprov_id
med_claim_sub_cont
med_claim_submitter
med_claim_subscriber
med_claim_supplemental
med_claim_sv

The following tables are representative of our CMS-1500 paper med claim form:
med_claim_1500
med_claim_1500_bp
med_claim_1500_dx
med_claim_1500_sf
med_claim_1500_sl

And the following tables are related to the med claim response (electronic)
med_claim_resp
med_claim_resp_277
med_claim_resp_835
med_claim_resp_bl_cont
med_claim_resp_bl_cont_m
med_claim_resp_ch_meta
med_claim_resp_ch_pyr
med_claim_resp_cont
med_claim_resp_cont_m
med_claim_resp_cpt_nm
med_claim_resp_dep
med_claim_resp_dlv_mthd
med_claim_resp_dt_info
med_claim_resp_edit
med_claim_resp_err
med_claim_resp_fail
med_claim_resp_fn_in
med_claim_resp_inpt
med_claim_resp_log
med_claim_resp_meta
med_claim_resp_oid
med_claim_resp_osub
med_claim_resp_outpt
med_claim_resp_pe_adr
med_claim_resp_pmt_info
med_claim_resp_pri_pyr
med_claim_resp_prov
med_claim_resp_prov_adj
med_claim_resp_prov_adjs
med_claim_resp_prov_cstat
med_claim_resp_prov_stat
med_claim_resp_prov_sum
med_claim_resp_prov_sup
med_claim_resp_pt_nm
med_claim_resp_pt_stat
med_claim_resp_py_adr
med_claim_resp_py_cont
med_claim_resp_py_cont_m
med_claim_resp_py_dtl
med_claim_resp_pye
med_claim_resp_py_info
med_claim_resp_pyr
med_claim_resp_pyr_adj
med_claim_resp_py_sub
med_claim_resp_rc_act
med_claim_resp_ref
med_claim_resp_rend
med_claim_resp_rend_id
med_claim_resp_sd_act
med_claim_resp_sl
med_claim_resp_sl_adj
med_claim_resp_sl_hc
med_claim_resp_sl_pcy
med_claim_resp_sl_pi
med_claim_resp_sl_rprov
med_claim_resp_sl_sqty
med_claim_resp_sl_sup
med_claim_resp_sprov_stat
med_claim_resp_stat
med_claim_resp_stat_dtl
med_claim_resp_stat_pyr
med_claim_resp_stat_tran
med_claim_resp_sub
med_claim_resp_sup
med_claim_resp_sup_qty
med_claim_resp_svc_id
med_claim_resp_sv_prov
med_claim_resp_sv_st
med_claim_resp_sv_stt
med_claim_resp_tcnt
med_claim_resp_tcont_m
med_claim_resp_tran
med_claim_resp_ts
med_claim_resp_x_ovr

Also see the values in some of the form_list_* tables as needed as they will be the available values for fields that reference them under field.source. One table used heavily through the claim is the form_list_med_claim_ecl. That is a list of all external code values we were provided by the clearinghouse and filtered by a field reference for the relevant field. 

*   **Error Logging:** Dedicated table `med_claim_error_log` should be created for capturing issues. 

**Key Linking:**
We now store a few items differently than when the originally functionality was written. For example, orders now are single items under form_careplan_order and have a subform form_careplan_orderp_item linked through the gerund table sf_form_careplan_order_to_careplan_orderp_item. They can be turned into a prescription (before they are dispensed) which is a form_careplan_order_rx record linked through rx_no. Their refills remaining count are store in form_careplan_order_rx.refills_remaining. You can check how many dispenses a prescription had by check form_careplan_order_rx_disp where rx_no = rx_no and status = 'Confirmed'. 

**Core Business Events:**
*   Create the json for the med_claim and and med_claim_1500 forms during the invoice generation like we are doing for the ncpdp claim. 
*   Parse the inbound response from the clearinghouse after sending, boiling any errors back up to the client after logging. Also update the status of the form_med_claim approprietly and save the response so the user can view it inline. 
*   Parse the inbound status (277) and remittance (835) reports and update the form_med_claim
*   Create a pretty view of the responses (999, 277, 835) as we did for the form_ncpdp_response in the build_ncpdp_response_summary() function and inserted into a new form. We don't want the response to be anymore than 2 layers deep like we did for the ncpdp_response.
*   Generate the views for the unreviewed 277 and 835 forms that we will display in a swimlane to the client.
*   Generate the 2 new views before we handle the cash posting for the 835. 

**Replacement vs Updating a function:**
The functions in our .js files really need to be hammered down to the minimum business logic wise. We want to move all the parsing and business logic up into our postgres functions and views. Unless there is a reason to keep the logic in node.js, we want it moved up to postgres.

**Coding Standards & Requirements:**
*   Strict adherence to naming conventions (`p_` for parameters, `v_` for variables, specified table aliases).
*   Mandatory data type casting (`::text`, `::numeric`, `::integer`, etc.) in all operations.
*   Use custom types whenever possible to pass around objects in postgres function, never pass a RECORD type around, it doesn't work.
*   Never generate json until the final step of the process, otherwise it will be "double-escaped" in our final results. 
*   Never use ROW() when trying to generate an array of custom types, it doesn't work, and it escapes certain values.
*   Implement robust error handling using `EXCEPTION` blocks and logging to the dedicated error tables.
*   Use `RAISE LOG` for tracing execution flow and decision points during development/debugging. When debugging, use RAISE NOTICE so we can see it when running our query in TablePlus.
*   Include `deleted IS NOT TRUE AND archived IS NOT TRUE` checks for `form_` tables and `archive IS NOT TRUE AND delete IS NOT TRUE` for `sf_` tables in joins. There is no deleted/archived or delete/archive for the 'gr_*' tables, we just delete the row from there.
*   Please make sure the column exist on the table (and that the table exists in the database) before using and validate any queries when you complete them.
*   When perform INSERT,UPDATES, or queries, make it one column per row to spread it out for easy readability and for INSERT or UPDATE statements, put the column name as a comment next to the value. 
*   Put comments throughout the implemenetation documenting any assumptions made or reference this spec. 
*   Document function headers for each function generates defining parameters and a description of the function.
*   If you are undecided on where to pull a value, please ask before proceeding.
*   To call our SQL functions or perform a query in our node.js code, we will be using this.db.env.rw.parseSQLUsingPGP which we pass in our SQL and our parameters. We will use %s to integer values and %L for string values. Make sure you cast their type in the SQL. Below is an example of how we would use it:

            const sql = `
            SELECT
                create_rx_fill_invoice(
                %s::integer,
                %s::integer,
                %s::integer,
                %s::integer,
                %s::integer
            ) as result
            `;

            const sqlParams = [
                insuranceId,
                payerId,
                rxRec.patient_id,
                siteId,
                rxRec.id,
            ];

            const rxChargesAndInvoicesRow =
                await this.db.env.rw.parseSQLUsingPGP(sql, sqlParams);

            if (rxChargesAndInvoicesRow.length === 0) {
                return ActionResponseWrappers.error(
                    `Error generating invoice record for the prescription. Rx ID ${rxRec.id}`
                );
            }
            const results = rxChargesAndInvoicesRow[0].result;

Note that you can only call postgres functions with array values with primitives and integers or strings, other types don't work. So pass up record id(s) when possible. Arrays need special handling when passing up, ask if you need to do that and I'll explain.

**Testing:**
We want to perform compresentive testing by inserting mock entries into the form_med_claim_resp_log, you can use any existing id from form_patient to test with. After each step of code generation, we should create subsequent tests to verify the logic. 
---

