---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 4: Handle billing for nursing visits

*   **Action:** Now we are going to handle the workflow for billing nursing visits. These are going to based on the encounter form (form_encounter) which we will need to generate the charge lines for. The way this works is when a nurse has completed a visit, she will document on the form and then it will go to review by a nursing supervisor. Once they sign off on the note, approved will be set to 'Yes' and cc_signature will be populated. We should create a trigger to monitor for that. Once that is done, we need to generate a form_ledger_charge_line which will go into a queue to create an invoice off of. We want to link that charge number to the delivery ticket of the drug that was used (form_encounter.delivery_ticket_id) by getting the ticket_no from form_careplan_delivery_tick.ticket_no to store against the charge line. The way we can find the payer for that visit is going to be one of 2 ways. The first way we want to try is there will be prescriptions linked to that delivery ticket on the form_careplan_dt_item where ticket_no = ticket_no through rx_id = form_careplan_order_rx.id. From there, we can use the vw_order_raw (note there might be mulitple items on the ticket, we want the first one that has this next field set) vw_order_raw.nursing_insurance_id where vw_order_raw.rx_no = form_careplan_order_rx.rx_no. Which will be the form_patient_insurance.id which has the payer_id on it. To find the inventory_id we want to use, the first thing we will want to do is see if the patient has an active (status_id = '5') form_patient_prior_auth where pa_type include 'Nursing' (pa_type will be a string representation of a json array so just search for like '%Nursing%'). If that is found, there *might* be entries in nc_id which is a multi-source foreign key (so entries will be in gr_form_patient_prior_auth_nc_id_to_list_billing_code_id) to form_list_billing_code which we can use to find the form_inventory record where type = 'Billable', active = 'Yes', and billable_code_id = gr_form_patient_prior_auth_nc_id_to_list_billing_code_id.form_list_billing_code_fk. There might be multiple codes linked so we will want to pick the best one, there is usually one for the intial visit and then one for subsequent visits. The initial_visit should be set to 'Yes' if for the initial visit and null otherwise. The nursing_hours will be a decimal/numeric type that represents the number of hours represented for the code. This should be how we calculate our bill_quantity and charge_quantity in the form_ledger_charge_line. The way this works is the bill_quantity and charge_quantity will be time_total / nursing_hours. You can tell if it is the intial visit by looking at the form_encounter.visit_number which should be 1 or greater.

 If that fails, and we don't find the insurance record, we want to attempt to look up the insurance record based on the patient_id where active = 'Yes' sorted by rank desc where form_payer.billing_method_id IN ('mm','cms1500'). If that fails, we will not generate the charge line but should log it in the billing_errors table for record keeping. 

To get the pricing for the item, you'll want to use the get_inventory_pricing function which returns you some other columns we'll want to add to the form_ledger_charge_line. You can see how we use it for drugs in the existing charge line functions. We should add a new helper function to generate the charge line for the visit. 

*   **Action:** Build a test .sql file that will insert a form_encounter entry with the fields set to verify we can generate a charge line from it. You can query the database to find test records to input for the patient, I'd start at the form_patient_insurance level first to find one that can bill for nursing and then insert a form_patient_prior_auth for that one and query to find the form_inventory records to get the billing codes for the visits.
