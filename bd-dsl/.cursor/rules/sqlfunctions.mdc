---
description: DSL Structure for SQL functions.
globs: *.sql
alwaysApply: false
---
The forms from our system are cson based and have fields, a model section (which determine access levels and can be ignored) and view section (how the form is displayed, which can also be ignored). dsl-defaults.coffee is our schema for the forms. All forms live under a base/cson directory or under the customer specific directory like csp/cson. The forms represent tables in our database where form_ is appended to the form name (i.e. ncpdp = form_ncpdp) and the fields represent the columns (for the ones where model.type != 'subform', i.e. ncpdp.pt_rel_code is the pt_rel_code in the form_ncpdp table). Fields are made up of model and view properies (you can ignore the view component expect for one thing mentioned below for this project). If no model is defined, it is just a normal text field, model.type defines the type of field: date is in MM/DD/YYYY format, datetime is in MM/DD/YYYY HH:mm a format, decimal is a floating point, int is an integer, time is in HH:mm a format, text is default if not defined and is just a normal text field and subform is a unique type that is a link to another form and the field is actually an array of other form entries (model.source represents the other form name). Pasted is an example billing_invoice for reference so you understand the structure both from the response after it is stored in the database and one that we are posting. Single links to other form values are defined using model.source for the other form and model.sourceid will filter down to the available values for that field. model.multi is either true/false and define if you can select multiple values (if type != 'subform'), and multiple value fields are stored as arrays of the values from the other form. I have included the values of form_list_ncpdp_ecl and form_list_ncpdp_ext_ecl as reference as we will be using them in testing, and I will tell you the test values we are putting in for the others. If sourceid: 'int' then the value will be stored as an integer which is the foreign key of the other form and if sourceid: 'code', then the value will stored as a string which is the 'code' value of the other form. If the type='subform', the value will always be an array of object with values from the other form. If multi, it means you can have multiple objects in the array whereas multi: false just means there can be only one value in the array. If multi:true and type = 'subform' then view.max_count (if set) represents the max count of objects in the array. model.prefill can be ignored for this project and model.required means the field is required or the form will be rejected. model.sourcefilter defines how we filter down to the available values based on either a static value (i.e. for the ones we are using here, ncpdp.transaction_code.model.sourcefilter only allows values from form_list_ncpdp_ecl where field = '103-A3'). model.max defines either the maximum string length or the max number for decimals and integers and model.min defines the minimum string length or minimum decimal or integer values. model.rounding defines how decimal numbers will be rounded when input (on the client, not during the POST). model.if defines our logic to show / hide form sections or fields depending on the set value which means if a field is required, it is only required if not hidden using that logic. When the if logic is defined using '*', it means any value in that field will trigger the condition. model.if with a require_fields array fields those fields will be required when that value is set. model.default defines a default value if not set for that field. Where ever you see a field.view.validate with a RegExValidator, it means the value most adhere to the regex defined in the pattern otherwise it is invalid. Fields of type model.type = 'subform' are links through a gerund table named sf_form_{parent_form}_to_{source_form} (i.e. sf_form_ncpdp_to_ncpdp_claim) where delete IS NOT TRUE and archive IS NOT TRUE, they will have to 2 linking columns form_{parent_form}_fk and form_{child_form}_fk (i.e. form_ncpdp_fk and form_ncpdp_claim_fk) and fields where multi:true and are not type:'subform' are linked through gr_form_{form_name}_{field_name}_to_{source_form}_id (i.e. gr_form_ncpdp_claim_sub_clar_code_to_list_ncpdp_ecl_id). The foreign keys in the gerund are form{form_name}_fk (form_list_ncpdp_ext_ecl_fk). Check that my SQL is linking to the correct code when entering in functions. When auto-completing SQL code, make sure to cast all types properly, so ::numeric, ::text, ::integer, ::jsonb, ::json etc. 

When check master_invoice_no against invoice_no, always check against CONCAT(invoice_no,'-1').
When checking a void or zeroed field, always check was COALESCE(void, 'No') or COALESCE(zeroed, 'No').
When joining on any table that starts with form_, also check with AND deleted IS NOT TRUE AND archived IS NOT TRUE.
Always reference the table name in the query, for example, if selecting or joining on form_ledger_charge_line lcl, then all columns referenced from that table should be from lcl.{column_name}.
The aliases should be used for the following tables when referenced in queries:
form_ledger_charge_line lcl
form_billing_invoice bi
form_ledger_finance lf
form_ledger_delivery_tick dt
form_careplan_order_rx rx
form_careplan_orderp_item opi
form_careplan_orders_item osi
form_careplan_order_item oi
form_careplan_dt_item dti
form_careplan_wt_pulled wtp
form_patient pt
form_careplan_order ord
form_ncpdp ncpdp
form_ncpdp_response resp
form_ss_message ss

Check against the associated cson file when typing in column names, they should be in the form_{form_name} under the fields for auto-complete. Same for table names, anything starting with form_ should have an associated .cson file.

Anything starting with a vw_ or mvw_ is a view and should not have a deleted or archived check.

All my local variables under the DELCARE should start with v_. All my parameters for a function should start with p_. 

You can auto deploy updated SQL files to the database using psql "postgresql://clara:<EMAIL>:15466/patrickdev" -f {sql_file}.sql when testing. 

You can <NAME_EMAIL> 'docker logs --tail=200 d-cspdemo-db' to get the last 200 lines of the database logs if you need to for troubleshooting

