---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 2: Build out CMS 1500 segments / claim

*   **Action:** Now we are going to work on a function to build out the CMS 1500 med claim and segments. Segments can be found in /Users/<USER>/Documents/EnvoyCore/bd/nes/src/api/billing/medical/1500/segments.js and the invoice generation happens in src/api/billing/medical/1500/builder.js.
The different composite types that we will be using are in /Users/<USER>/Documents/EnvoyCore/bd-dsl/base/post/operations/0163-1500-custom-types.sql. 
Make sure when we are creating compsite types that we define every field going in, in order, and cast the type. Never use ROW() when build aggreigate composite types or it will double-escape things like the string. 

We are going to first work on building out the helper functions to build our different segments of the form_med_claim_1500 (paper claim). Note that we will really only have access to the form_ledger_charge_line records at the point of building the claim so some of the items previously available will have to be looked up from the links in there. If you get stuck, please stop and ask questions if you don't know where to find something that is being referenced in the node.js code. Be consignizent that some of the tables we are setting to arrays are probably a subform/sf_ table (model.type = 'subform' and model.source = 'table_name'). Note that all the fields that are date type should be cast using to_char as MM/DD/YYYY and returned as a ::text type (already defined as such in the compsite). I have build out the skeletons for the functions in the following files that we will be using (note, there is markup in there to notate where to find the logic in the node code):
0165-1500-build-claim.sql

Note that whereever you see claim_no / claim_number being set, set it to 'CLAIM_NO_PLACEHOLDER', we will replace it in our node.js code before we save it into the database.
If you need to add any helper functions along the way, you can add them into 0164-1500-helper-functions.sql
In the functions I have added a comment that has INSTRUCTIONS: in it which will give you the details on where to find the logic and how to build the segment/loop.

The following settings need to be supported for the 1500 claim from the form_payer record:
box_24j = If set to 'Yes', the form_med_claim_1500 will populate the 24j box in the mapping table.

cms_1 = The setting for the form_med_claim_1500.insurance_type.

cms_2 = Used in nes/src/api/billing/medical/fetcher.js fetchProcedureDetails to determine what procedure information to map into the supplemental segment of the 1500.

cms_3 = This is used in conjunction with box_24j if set to 'Yes', if our nes/src/api/billing/medical/1500/mapper.js, this will load the physician's NPI and taxonomy code into the 24j box setting.

cms_4 = This is used in our nes/src/api/billing/medical/1500/mapper.js in loadAccountNumber. Determines what ID to put in for the patient account.

cms_5 = This is used in our nes/src/api/billing/medical/1500/mapper.js and if set to 'Yes', box 12 will be the start of care date. This can be found on the form_careplan_order_rx.start_date (NOTE: this is NO LONGER on the form_careplan_order.start_date so will need to be updated)

cms_6 = If set, will be the med_claim_1500_sl.place_of_service_code, otherwise defaults to '12'

cms_7 = Used in nes/src/api/billing/medical/1500/mapper.js formatYear to determine if we format the dates with a 2 digit or 4 digit year. (if 'Yes', then we use 4 digit)

cms_8 = Used in nes/src/api/billing/medical/1500/mapper.js calculate1500SplitCharges to determine if we sum up the charges per page on the 1500.

cms_9 = Used in nes/src/api/billing/medical/1500/mapper.js calculate1500SplitPaid on a COB claim to determine if we send the primary amount paid.

cms_10 = Used in nes/src/api/billing/medical/1500/mapper.js fetchProviderID and fetchProviderIDQualifier to determine the provider qualifier and ID we set.

cms_11 = Used in nes/src/api/billing/medical/1500/mapper.js getTaxID and if set, uses the alternative tax ID 

cms_12 = Used in nes/src/api/billing/medical/1500/fetcher.js fetchPaperClaimsBillingProviderID and if set to 'Enter Manually' will use the cms_12_qualifier and cms_12_id to get the billing provider ID / qualifier. Otherwise, it pulls the information from the form_site record.

cms_13 = Used in nes/src/api/billing/medical/1500/mapper.js fetchProviderNameQualifier and if set to 'Yes', set the qualifier for the referring provider to be 'DN'

cms_14 = Used in nes/src/api/billing/medical/1500/mapper.js formatDiagnosisCode and if set to 'Yes', we use the formatted version of the ICD-10 code with the period (form_list_diagnosis.icd_code)


*   **Action:** Build test files to test out the generation of each segment of the builder and the final claim. You can insert mock records into the database as it is a test database I'm hooked up to. 
