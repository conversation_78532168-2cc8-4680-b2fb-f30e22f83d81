---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 8: Handle integrating current changes into the node.js app

*   **Action:** Now we are going to clean up some of our node.js functions arround the medical claims and make sure our current code is handling some of the additional complexities. The place where we will be generating the medical claims / invoice can be found in generator.js at generateCOBInvoice or generateInvoiceFromInvoiceCreationView. Everything should work until we reach __trySaveInvoiceRecords, we were previously just storing the generated claim_no to the main claim form but we have it also in form_med_claim_supplemental.claim_number and form_med_claim_sl.claim_no now so will need to update to look for that in the generated json blob before saving.

In builder.js for the electronic medical claims (form_med_claim) we should be able to delete that file and segments.js, so review that one more time and make sure we didn't miss and business logic in there. 

In receiver.js, we should really only be using the parseClearinghouseResponse and parsePayerClaimResponse to store the med_claim_resp_log and we should have a trigger off of that to parse the response into its' proper tables