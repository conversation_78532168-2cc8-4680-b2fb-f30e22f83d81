---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 4: Parsing an inbound message (File: `0137-ss-actions.sql`)

*   **Action:** We will be creating a function that given an ss_message id, will return a list of actions that should be available to that message. The current action calculations happen in mapper.js in the __calc_actions functions. We will be replacing that and no longer calculating the available_actions on the ss_message. We will be calculating the values for the available actions we had previous with the exception of create_records where we will be instead be creating a view which calculate the prefill values for each form we can create out of the ss_message. Remember as mentioned in the implementation breakdown, you have to first have a linked patient in ss_message.patient_id if we want to create any further records outside of form_patient since we will be the patient_id as part of it. 

Also we will need to have functions that build the "prefill" in the values for a response. In these, subforms will be arrays with their objects and values similar to how we are handling the form_ncpdp in the 0065-create-ncpdp-claim.sql file. Each type of the 4 responses that we support will have different prefill values. The related_message_id will need to be set but the server on the outbound will assign our message_id. 
You can see how we structure the dsl for the form_ss_message in nes/__mocks__/surescripts/outbound/**/*.json. The ss_message.cson should be able to tell you if you look at the message_type and the model.if for that field which sections / subforms are supported for that type. Instead of sending an empty array for some of them though, just set to null. We never want to send a prefill with an empty array to the client. Also if you look at the __handle_roll_forward in src/api/surescripts/builder.js, that determines the fields we roll forward based on message type. 
