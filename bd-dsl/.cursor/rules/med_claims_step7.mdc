---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 7: <PERSON>le building the final json blob that we send to the clearinghouse

*   **Action:** 
Now we want to replace the preprocessor.js functionality for the form_med_claim. 
We should add a function in the sql file 0163-mm-preprocessor.sql to handle this by taking in a form_med_claim.id and building the json blob that we will be sending to the server to process. The specs for what we have to send are in Claims_V3.json under ClaimSubmissionRequest. These will map to fields/values in the form_med_claim* forms. You'll have to use the sf_* to join through the subforms and gr_ to get the multi:true foreign sourced values (only for form_med_claim_cond.condition_codes). To avoid building a huge json blob all at once, I would divide up the segments into functions that return the subqueries or json blobs for the various segments. Just know you can't nest json objects when building the json in postgres or it will double escape it so you'll want to divide it up in such a way that you can join them together to get to the final results or as a last resort, use composite types and pass those back and forth and use those to build the final json blob. Just remember that you can't pass a RECORD type into a function so you'll have to pass the id of the form_med_claim or its subforms and work with those. Make sure to add plenty of logging and catch exceptions and log in billing_error_log. Function names should start with mm_ to reduce the possibility of creating duplicate function names. Use your access to the database to query for table names / linking if unsure and same for data field types. Do not include any values that are null, just don't add that key to the final results (or remove them in the final json blob, either way).

The way this currently works is we have the following conversions of the data happening in AJV before we run our preprocessor (I'm going to remove these so will just need to implement as part of building our final json):
- Date fields are converted to a YYYYMMDD
- Modifier fields (modifier_1, modifier_2, modifier_3, modifier_4) in the form_med_claim_sv into an array of values under procedureModifiers under the professional service segment and for form_med_claim_sl_adj under the procedureModifier property.
- All phone_number, phone_extension, fax_number fields are reduced to just numbers using value.replace(/\D/g, "")

Now in the preprocessor.postValidationTransformations we do the following:
- For the medication info segment
-- If attachment_transmission_code in form_med_claim_report is not set, then don't build that segment under the service line.
- The information under form_med_claim_info_other is brought onto the make claim info segment.
- For each other subscriber information, we change the providers into an object with the entry under that array (there will only be a single entry in that subform if set)
- We use the list of diagnoses index pointers under the healthcare code information and assign the linked diagnoses on the service line back to an array of index values (starting with 1) into diagnosisCodePointers
- If the form_med_claim_dme_cond.certification_condition_indicator != 'Y' then we are removing that segment (so just don't process it in that condition)
- We are aggigating up the modifier_1, 
- Flatten any objects that aren't suppose to be sent as arrays (this should be apparent when review the json schema spec and will be handled anyway while you are build it)
