---
description: 
globs: 
alwaysApply: false
---
```markdown

## Phase 1: Build out segments / claim

*   **Action:** Now we are going to work on a function to build out the electornic med claim and segments. Segments can be found in /Users/<USER>/Documents/EnvoyCore/bd/nes/src/api/billing/medical/electronic/segments.js and the invoice generation happens in src/api/billing/medical/electronic/builder.js.
The different composite types that we will be using are in /Users/<USER>/Documents/EnvoyCore/bd-dsl/base/post/operations/0147-mm-custom-types.sql. 
Make sure when we are creating compsite types that we define every field going in, in order, and cast the type. Never use ROW() when build aggreigate composite types or it will double-escape things like the string. 

We are going to first work on building out the helper functions to build our different segments of the form_med_claim (electronic). Note that we will really only have access to the form_ledger_charge_line records at the point of building the claim so some of the items previously available will have to be looked up from the links in there. If you get stuck, please stop and ask questions if you don't know where to find something that is being referenced in the node.js code. Be consignizent that some of the tables we are setting to arrays are probably a subform/sf_ table (model.type = 'subform' and model.source = 'table_name'). Note that all the fields that are date type should be cast using to_char as MM/DD/YYYY and returned as a ::text type (already defined as such in the compsite). I have build out the skeletons for the functions in the following files that we will be using (note, there is markup in there to notate where to find the logic in the node code):
0149-mm-build-provider-loop.sql
0150-mm-build-receiver-segment.sql
0151-mm-build-submitter-segment.sql
0152-mm-build-subsciber-segment.sql
0153-mm-build-dependent-segment.sql
0154-mm-build-claim-info-segment.qsl
0155-mm-build-pay-to-segment.sql
0156-mm-build-claim.sql

Note that whereever you see claim_no / claim_number being set, set it to 'CLAIM_NO_PLACEHOLDER', we will replace it in our node.js code before we save it into the database.
If you need to add any helper functions along the way, you can add them into 0148-mm-helper-functions.sql
In the functions I have added a comment that has INSTRUCTIONS: in it which will give you the details on where to find the logic and how to build the segment/loop.

The following settings need to be supported from form_payer record for the payer from the claim:
mm_hold_claims_dos_end = This will set the form_med_claim.substatus_id = '114' if the date_of_service_end is in the future if set to 'Yes'

mm_send_billing_prov_commercial_number = If set to 'Yes', for the billing provider (form_med_claim_prov_bill) sets the commercial_number to form_payer.mm_billing_commercial_number


mm_send_rendering_prov_commercial_number = If set to 'Yes', for the billing provider (form_med_claim_prov_bill) sets the commercial_number to form_payer.mm_rendering_commercial_number

mm_submit_ndc_rx_info = If set to 'Yes', we send the drug identification segment (form_claim_sl_di)

mm_sos_only = If set to 'Yes', only sends the start date of service and not the end date of service.

mm_default_service_place_id = If set, in form_med_claim_info.place_of_service_code set to mm_default_service_place_id otherwise default to '12'. Also in the form_med_claim_sv set to mm_default_service_place_id or default to '12'

mm_payer_name = If set, use in the receiver segment (form_med_claim_receiver.organization_name), otherwise, use the form_payer.organization. Also when doing a COB, if the previous payer was a major medical payer (form_payer.billing_method_id = 'mm') then first see if mm_sec_payer_id is set for the previous/parent payer and use that in form_med_claim_opayer.mm_sec_payer_id, otherwise if mm_payer_name is set, use that, otherwise use form_payer.organization

mm_claim_filing_indicator_code = If set, use in form_med_claim_info.claim_filing_code, otherwise default to 'CI'

mm_send_contract_pricing = If set to 'Yes', then we send the contract segment (form_med_claim_contract)

mm_submit_repricing = If set to 'Yes', then we send the repricing segment in the claim info segment (form_med_claim_reprice) and the service line (form_med_claim_reprice_sl)

mm_copy_ordering_md_to_ref = If set to 'Yes', then the ordering prescriber info (form_careplan_order_rx.physician_id) information is copied over to the referring provider segment (form_med_claim_prov_ref). Otherwise, the referring provider can be found in form_patient.referrer_id (which is the form_physician.id). If not set on the patient, then use the ordering prescriber details as the referring provider anyways.

mm_send_upin_supplies = If set to 'Yes', and form_med_claim_sv.type = 'Supply', and form_inventory.upin is set, then set form_med_claim_sv.procedure_identifier to 'ER' and procedure_code to form_inventory.upin.

The following settings need to be supported for a COB claim:
mm_sec_claim_filing_indicator_code = If set for the COB payer record, set in the form_med_claim_osub.claim_filing_indicator_code (note this setting can be overwritten in the insurance rules, see below). If not set, and not set in the form_patient_insurance_med_cond rules, then you can default to 'CI'.

mm_sec_payer_id = If set on the COB payer record, used in form_med_claim_opayer.other_payer_organization_name, otherwise, use the form_payer.mm_payer_name, otherwise, use the form_payer.organization (note this can be overwritten in the patient_insurance_med_cond rules below)

mm_sec_send_identifier = If set to 'Yes' on the COB payer record, then we set the other_payer_secondary_identifier segment with the qualifier = mm_sec_id_qualifier and identifier = mm_sec_id

mm_sec_submit_repricing = If set to 'Yes', and we have a COB claim, this determines if we send the primary claim information the repricing segment in the claim info and service lines. If not, we don't send the repricing information.

mm_sec_claims_pr_code = If set on the COB payer record, then we set the form_med_claim_osub.payment_responsibility_level_code = mm_sec_claims_pr_code. (note, this can be overwritten in the form_patient_insurance_med_cond record in the payment_responsibility_level_code setting). If it isn't set, then we need to figure out the setting based on the payer level of the COB claims based on the number of COB claims.

From the form_patient_insurance_med_cond when the previous payer_id (parent claim) = previous_payer_id
and the current payer_id = active_payer_id (these override any default settings or settings on the form_payer record if set)
sec_payer_id = If set, this override the form_payer.mm_sec_payer_id setting
insurance_type_code = If set, this overrides the form_med_claim_osub.insurance_type_code.

payment_responsibility_level_code = If set, this overrides the form_payer.mm_sec_claims_pr_code setting for the COB payer.

claim_filing_code = If set, this overrides the form_med_claim_info.claim_filing_code

accident_date = If set, this goes into the form_med_claim_dates.accident_date
symptom_date = If set, this goes into the form_med_claim_dates.symptom_date

The following setting need to be supported when generating a DME claim service line from the form_payer record:
span_rental_dates = Based on the form_med_claim_sl.service_date, you want to span to the same day in the following month - 1 day. For example, if service_date is on 5/5/2025 then the service_date_end will be 6/4/2025. If the date in the next month doesn't exist (i.e. service_date = 1/31/2025) then we pull the next EOM for the next month (2/29/2025). We need to take leap years into account here as well if not handled automatically. 

*   **Handling COB claims:** 
If handling a COB claim, and we have a parent claim number, we will need to perform the following actions:
NOTE: The form fields have changed for both the med_claim_resp_277 and med_claim_resp_835, I am including the *.cson forms for review but this means some of the COB logic in the node.js code no longer points to fetching the information from the correct form / location from the response.

- The service lines we submit cannot change across claims, what we submitted to the primary payer, need to be what we submit to the secondary. To lock these down, we set form_med_claim_sl.lock_sv = 'Yes'. This does not mean we do not support the payer settings from above though regarding what identifiers to set on the rest of the claim or what segments to show/hide. 
- The main segments that are affected by a COB claim would be the repricing loop (claim and service line level), and the service line adjustments (claim level adjustments fall under the other subscriber information), and the other subscriber information (which is mainly where the COB information is contained).If the claim was rejected and all we have is a 277 response (form_med_claim_resp_277) and not a 835 (form_med_claim_resp_835) then we will not be adding in any adjustments or payer paid amounts, just the rejection code / reason. 
- To determine which response type to use, if a COB claim, for the parent claim get the latest form_med_claim_resp_log where claim_no = form_med_claim.claim_no. Then that should match the response_id in either a form_med_claim_resp_277.response_id or form_med_claim_resp_835.response_id. If you can't find either for the parent claim, throw an exception. The only reason we would allow a COB based on just the form_med_claim_resp_277 is if the response was rejected, if we had a paid response in the parent claim, and you don't have an 835 response, throw an exception "COB Claims for Paid Major Medical claims require the full 835 response. Please wait for the full response before generating". Generating a COB off of the 277 will be easier because we don't need the adjustment codes for that parent claim, just node that, that parent claim maybe part of a chain so you will have to work your way down (we want to start at the root level claim) until you get to the one that we are currently creating.

- To match service lines across claims to their responses, we have the form_med_claim_resp_277_sl.line_item_control_number = form_med_claim_sl.assigned_number and form_med_claim_resp_835_sl.line_item_control_number = form_med_claim_sl.assigned_number just make sure you are getting the correct form_med_claim_resp_277/form_med_claim_resp_835 parent form.

- To find the claim level adjustments, they will be under the form_med_claim_resp_835_adj off the main form_med_claim_resp_835 record. You can get to them through the sf_ table. Note that these are flattened but have the claim_adjustment_group_code on each that you can use to group them by.

- To find the service line adjustments, you'll need toget the service lines first (form_med_claim_resp_835_sl, linked to form_med_claim_resp_835 through the sf_ table) and then 
the adjustments can be found in the form_med_claim_resp_835_sl_adj table linked through theform_med_claim_resp_835_sl entries through the sf_ table. Note that these are flattened but have the claim_adjustment_group_code on each that you can use to group them by.

- The remaining patient liability can be found on the form_med_claim_resp_835.total_pt_pay. If the claim was rejected, and all we have is the 277 response, then that will be the full billed amount from form_med_claim_resp_277.claim_total_claim_charge_amount. 

- The total claim paid amount can be found on form_med_claim_resp_835.claim_payment_amount. If rejected, that is going to be $0.00. 

- The total claim billed amount can be found on the form_med_claim_resp_835.total_charge_amount or the form_med_claim_resp_277.claim_total_claim_charge_amount

- Pull as much information from the claim responses as possible like the adjudicated date and remittance date (i.e. the paid date if not rejected).

- If the payer record is set to send the repricing loop (see logic above), check the existing logic in the node.js on how to populate those fields.

*   **Action:** Build test files to test out the generation of each segment of the builder and the final claim. You can insert mock records into the database as it is a test database I'm hooked up to. 
