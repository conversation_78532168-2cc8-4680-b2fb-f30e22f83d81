BEGIN;
SET session_replication_role = replica;

-- Clear the table first:
DELETE FROM form_list_rph_labels;

-- Insert your new therapies:
INSERT INTO form_list_rph_labels ("firstname","lastname", "professional_designation", "password", "npi", "license_number", "code", "active", "id","created_on","created_by","auto_name") VALUES
('<PERSON>','<PERSON><PERSON><PERSON>','<PERSON>arm<PERSON>','Winter99!', NULL, NULL, 'GK4', 'Yes',1,'2025-02-14 00:00:00', 1, '<PERSON>'),
('<PERSON>','<PERSON><PERSON><PERSON>','PharmD','123', NULL, NULL, 'AR', 'Yes',2,'2025-02-14 00:00:00', 1, '<PERSON>'),
('<PERSON>da','Jawad','PharmD','Hudz1989', NULL, NULL, 'HJ', 'Yes',3,'2025-02-14 00:00:00', 1, '<PERSON><PERSON>'),
('<PERSON>','<PERSON><PERSON>','PharmD','B<PERSON>@csp22', NULL, NULL, 'BN', 'Yes',4,'2025-02-14 00:00:00', 1, '<PERSON><PERSON>'),
('<PERSON><PERSON>e','Kama<PERSON>ea<PERSON>-Mendoza','PharmD','KKM', NULL, NULL, 'KKM', 'Yes',5,'2025-02-14 00:00:00', 1, '<PERSON><PERSON>e Ka<PERSON><PERSON><PERSON>-<PERSON>'),
('Neel','Mehta','<PERSON>armD','Nm@2023!', NULL, NULL, 'NM', 'Yes',6,'2025-02-14 00:00:00', 1, 'Neel Mehta'),
('Andrew','Yoo','PharmD','AY@2023!', NULL, NULL, 'AY', 'Yes',7,'2025-02-14 00:00:00', 1, 'Andrew Yoo'),
('Dara','Abacan','PharmD','DA', NULL, NULL, 'DA', 'Yes',8,'2025-02-14 00:00:00', 1, 'Dara Abacan'),
('Chris','Munoz','PharmD','CM@2024!', NULL, NULL, 'CM', 'Yes',9,'2025-02-14 00:00:00', 1, 'Chris Munoz'),
('Jimmy','Nguyen','PharmD','JN@2024!', NULL, NULL, 'JN', 'Yes',10,'2025-02-14 00:00:00', 1, 'Jimmy Nguyen'),
('Lindsay','Park','PharmD','LP', NULL, NULL, 'LP', 'Yes',11,'2025-02-14 00:00:00', 1, 'Lindsay Park'),
('Dana','Shimabukuro','PharmD','DS', NULL, NULL, 'DS', 'Yes',12,'2025-02-14 00:00:00', 1, 'Dana Shimabukuro'),
('Palak','Shah','PharmD','PS', NULL, NULL, 'PS', 'Yes',13,'2025-02-14 00:00:00', 1, 'Palak Shah'),
('Christy','Nguyen','PharmD','CN', NULL, NULL, 'CN', 'Yes',14,'2025-02-14 00:00:00', 1, 'Christy Nguyen');

-- Bump the sequence so subsequent inserts pick up after the highest ID:
ALTER SEQUENCE "form_list_rph_labels_id_seq" RESTART WITH 15;

SET session_replication_role = DEFAULT;
COMMIT;
