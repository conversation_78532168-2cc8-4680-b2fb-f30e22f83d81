BEGIN;
INSERT INTO form_list_reaction ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Rash',NULL,NULL,'2024-03-11T16:04:58.000Z',NULL,'Rash','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_reaction.allow_sync = 'Yes';
COMMIT;
