BEGIN;
INSERT INTO form_module ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "nav_placement", "sort_order", "path", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2024-02-14T19:05:00.000Z','Queue',NULL,NULL,NULL,NULL,'Queue','queue','top',1,'/queue','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "nav_placement" = EXCLUDED."nav_placement", "sort_order" = EXCLUDED."sort_order", "path" = EXCLUDED."path" WHERE form_module.allow_sync = 'Yes';
INSERT INTO form_module ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "nav_placement", "sort_order", "path", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2024-11-07T13:03:44.000Z','Sales',NULL,NULL,NULL,NULL,'Sales','sales','top',2,'/sales','Yes','No') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "nav_placement" = EXCLUDED."nav_placement", "sort_order" = EXCLUDED."sort_order", "path" = EXCLUDED."path" WHERE form_module.allow_sync = 'Yes';
INSERT INTO form_module ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "nav_placement", "sort_order", "path", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,'2024-02-12T15:05:29.000Z','Patient',NULL,NULL,NULL,NULL,'Patient','patient','top',3,'/patient','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "nav_placement" = EXCLUDED."nav_placement", "sort_order" = EXCLUDED."sort_order", "path" = EXCLUDED."path" WHERE form_module.allow_sync = 'Yes';
INSERT INTO form_module ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "nav_placement", "sort_order", "path", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,'2024-02-12T15:06:13.000Z','Schedule',NULL,NULL,NULL,NULL,'Schedule','schedule','top',4,'/schedule','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "nav_placement" = EXCLUDED."nav_placement", "sort_order" = EXCLUDED."sort_order", "path" = EXCLUDED."path" WHERE form_module.allow_sync = 'Yes';
INSERT INTO form_module ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "nav_placement", "sort_order", "path", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,'2025-04-01T17:57:18.000Z','Inventory',NULL,NULL,NULL,NULL,'Inventory','inventory','top',5,'/inventory','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "nav_placement" = EXCLUDED."nav_placement", "sort_order" = EXCLUDED."sort_order", "path" = EXCLUDED."path" WHERE form_module.allow_sync = 'Yes';
INSERT INTO form_module ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "nav_placement", "sort_order", "path", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,'2024-08-23T04:34:22.000Z','Dispense',NULL,NULL,NULL,NULL,'Dispense','dispense','top',6,'/dispense','Yes','No') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "nav_placement" = EXCLUDED."nav_placement", "sort_order" = EXCLUDED."sort_order", "path" = EXCLUDED."path" WHERE form_module.allow_sync = 'Yes';
INSERT INTO form_module ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "nav_placement", "sort_order", "path", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,'2025-04-01T17:55:47.000Z','Billing',NULL,NULL,NULL,NULL,'Billing','billing','top',7,'/billing','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "nav_placement" = EXCLUDED."nav_placement", "sort_order" = EXCLUDED."sort_order", "path" = EXCLUDED."path" WHERE form_module.allow_sync = 'Yes';
INSERT INTO form_module ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "nav_placement", "sort_order", "path", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,'2024-11-14T22:58:39.000Z','Compliance',NULL,NULL,NULL,NULL,'Compliance','compliance','top',8,'/compliance','Yes','No') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "nav_placement" = EXCLUDED."nav_placement", "sort_order" = EXCLUDED."sort_order", "path" = EXCLUDED."path" WHERE form_module.allow_sync = 'Yes';
INSERT INTO form_module ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "nav_placement", "sort_order", "path", "allow_sync", "active") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,'2025-01-29T15:26:42.000Z','Analytics',NULL,NULL,NULL,NULL,'Analytics','analytics','top',9,'/analytics','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "nav_placement" = EXCLUDED."nav_placement", "sort_order" = EXCLUDED."sort_order", "path" = EXCLUDED."path" WHERE form_module.allow_sync = 'Yes';
INSERT INTO form_module ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "nav_placement", "sort_order", "path", "allow_sync", "active") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,'2024-02-12T15:06:25.000Z','Settings',NULL,NULL,NULL,NULL,'Settings','settings','bottom',10,'/settings','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "nav_placement" = EXCLUDED."nav_placement", "sort_order" = EXCLUDED."sort_order", "path" = EXCLUDED."path" WHERE form_module.allow_sync = 'Yes';
INSERT INTO form_module ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "nav_placement", "sort_order", "path", "allow_sync", "active") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,'2025-04-01T10:23:50.000Z','Referral',NULL,NULL,NULL,NULL,'Referral','referral','top',11,'/referral','Yes','No') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "nav_placement" = EXCLUDED."nav_placement", "sort_order" = EXCLUDED."sort_order", "path" = EXCLUDED."path" WHERE form_module.allow_sync = 'Yes';
INSERT INTO form_module ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "nav_placement", "sort_order", "path", "allow_sync", "active") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,'2025-04-01T10:23:31.000Z','ePrescribing',NULL,NULL,'2024-04-15T12:24:57.000Z',NULL,'ePrescribing','erx','top',3,'/erx','Yes','No') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "nav_placement" = EXCLUDED."nav_placement", "sort_order" = EXCLUDED."sort_order", "path" = EXCLUDED."path" WHERE form_module.allow_sync = 'Yes';
COMMIT;
