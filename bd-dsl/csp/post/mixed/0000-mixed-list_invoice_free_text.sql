BEGIN;
SET session_replication_role = replica;

-- Clear the table first:
DELETE FROM form_list_invoice_free_text;

-- Insert your new therapies:
INSERT INTO form_list_invoice_free_text ("name","id","created_on","created_by","auto_name") VALUES
('For more information, contact <PERSON> @ 365-3453',1,'2025-02-14 00:00:00',1,'For more information, contact <PERSON> @ 365-3453'),
('Pay promptly to avoid interest fee',2,'2025-02-14 00:00:00',1,'Pay promptly to avoid interest fee'),
('Resubmitted - 2nd',3,'2025-02-14 00:00:00',1,'Resubmitted - 2nd'),
('Thank you for allowing us to serve you',4,'2025-02-14 00:00:00',1,'Thank you for allowing us to serve you'),
('Your prompt payment is appreciated.',5,'2025-02-14 00:00:00',1,'Your prompt payment is appreciated.');

-- Bump the sequence so subsequent inserts pick up after the highest ID:
ALTER SEQUENCE "form_list_invoice_free_text_id_seq" RESTART WITH 6;

SET session_replication_role = DEFAULT;
COMMIT;
