BEGIN;
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Clinical',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Clinical','Clinical','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:31.000Z','Patient Services',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'PtServices','Patient Services','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:41.000Z','Patient Experience',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'PtExperience','Patient Experience','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:55.000Z','Patient Education',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'PtEducation','Patient Education','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:52:04.000Z','Patient Praises',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'PtPraise','Patient Praises','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Patient Complaints',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'PtComplaint','Patient Complaints','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','HBNow',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'HBNow','HBNow','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Clinical Rounding',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'CnclRounding','Clinical Rounding','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Dietitian',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Dietitian','Dietitian','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Medical Policy Review',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'MedPolicyReview','Medical Policy Review','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Reporting',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Reporting','Reporting','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Service Recovery',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'SvcRecovery','Service Recovery','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Shipping Complaint',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ShpComplaint','Shipping Complaint','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','ADR reported to manufacturer',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ADRReportedMfgr','ADR reported to manufacturer','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','ADR reported to FDA MedWatch',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ADRReportedMW','ADR reported to FDA MedWatch','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (16,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','ADR reported to physician',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ADRReportedDr','ADR reported to physician','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (17,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','ADR Physician should be notified',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ADRNotifyDr','ADR Physician should be notified','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
INSERT INTO form_list_intervention_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "active", "allow_sync", "legacy_data", "envoy_external_id") VALUES (18,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','ADR Manufacturer should be notified',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ADRNotifyMfgr','ADR Manufacturer should be notified','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_list_intervention_type.allow_sync = 'Yes';
COMMIT;
