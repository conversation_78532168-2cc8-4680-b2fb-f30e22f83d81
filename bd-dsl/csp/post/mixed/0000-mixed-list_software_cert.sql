BEGIN;
INSERT INTO form_list_software_cert ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "clearinghouse", "payer", "code", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T00:23:14.000Z','Clearin House',NULL,NULL,'2024-09-24T19:23:15.000Z',NULL,'Clearin House',NULL,NULL,'Yes',NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "clearinghouse" = EXCLUDED."clearinghouse", "payer" = EXCLUDED."payer", "code" = EXCLUDED."code" WHERE form_list_software_cert.allow_sync = 'Yes';
COMMIT;
