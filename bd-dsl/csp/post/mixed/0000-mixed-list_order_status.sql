BEGIN;
INSERT INTO form_list_order_status ("id", "created_by", "change_by", "updated_by", "reviewed_by", "patient_status_id", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "notification_type", "all_orders", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,'1 - Pending',NULL,NULL,'2024-06-03T23:13:39.000Z',NULL,'1','Pending',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "patient_status_id" = EXCLUDED."patient_status_id", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "notification_type" = EXCLUDED."notification_type", "all_orders" = EXCLUDED."all_orders" WHERE form_list_order_status.allow_sync = 'Yes';
INSERT INTO form_list_order_status ("id", "created_by", "change_by", "updated_by", "reviewed_by", "patient_status_id", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "notification_type", "all_orders", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2024-07-08T05:08:53.000Z','2 - Discontinued',NULL,NULL,'2024-06-03T23:13:39.000Z',NULL,'2','Discontinued','Inactive',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "patient_status_id" = EXCLUDED."patient_status_id", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "notification_type" = EXCLUDED."notification_type", "all_orders" = EXCLUDED."all_orders" WHERE form_list_order_status.allow_sync = 'Yes';
INSERT INTO form_list_order_status ("id", "created_by", "change_by", "updated_by", "reviewed_by", "patient_status_id", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "notification_type", "all_orders", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2024-07-08T05:08:35.000Z','3 - No-Go',NULL,NULL,'2024-06-03T23:13:39.000Z',NULL,'3','No-Go','Warning',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "patient_status_id" = EXCLUDED."patient_status_id", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "notification_type" = EXCLUDED."notification_type", "all_orders" = EXCLUDED."all_orders" WHERE form_list_order_status.allow_sync = 'Yes';
INSERT INTO form_list_order_status ("id", "created_by", "change_by", "updated_by", "reviewed_by", "patient_status_id", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "notification_type", "all_orders", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,'4 - On-Hold',NULL,NULL,'2024-06-03T23:13:39.000Z',NULL,'4','On-Hold',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "patient_status_id" = EXCLUDED."patient_status_id", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "notification_type" = EXCLUDED."notification_type", "all_orders" = EXCLUDED."all_orders" WHERE form_list_order_status.allow_sync = 'Yes';
INSERT INTO form_list_order_status ("id", "created_by", "change_by", "updated_by", "reviewed_by", "patient_status_id", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "notification_type", "all_orders", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2024-07-08T05:08:07.000Z','5 - Active',NULL,NULL,'2024-06-03T23:13:39.000Z',NULL,'5','Active','Ok',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "patient_status_id" = EXCLUDED."patient_status_id", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "notification_type" = EXCLUDED."notification_type", "all_orders" = EXCLUDED."all_orders" WHERE form_list_order_status.allow_sync = 'Yes';
COMMIT;
