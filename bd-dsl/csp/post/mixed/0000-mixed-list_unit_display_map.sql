BEGIN;
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,'CK',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','CK',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'CKs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,'pad',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pad',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pads','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,'mole',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mole',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'moles','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,'tablet/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,'tab',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tabs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,'cap',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','capsule',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'caps','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,'x10e6 EIN/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','x10e6 equivalent islet number/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'x10e6 EINs/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,'oz/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ounce/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ozs/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (9,1,NULL,1,NULL,'tspn/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','teaspoon/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tspns/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (10,1,NULL,1,NULL,'x 10e9 viable cells',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','x 10e9 viable cells',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'x 10e9 viable cells','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (11,1,NULL,1,NULL,'day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'days','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (12,1,NULL,1,NULL,'wk',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','week',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'wks','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (13,1,NULL,1,NULL,'tsp',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','teaspoonful',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tsps','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (14,1,NULL,1,NULL,'puff',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','puff',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'puffs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (15,1,NULL,1,NULL,'mo',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','month',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mos','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (16,1,NULL,1,NULL,'yr',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','year',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'yrs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (17,1,NULL,1,NULL,'tbsp',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablespoonful',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tbsps','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (18,1,NULL,1,NULL,'caplet',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','caplet',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'caplets','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (19,1,NULL,1,NULL,'piece of gum',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','piece of gum',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pieces of gum','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (20,1,NULL,1,NULL,'sachet',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','sachet',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sachets','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (21,1,NULL,1,NULL,'pastille',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pastille',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pastilles','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (22,1,NULL,1,NULL,'pellet',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pellet',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pellets','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (23,1,NULL,1,NULL,'vaginal insert',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vaginal insert',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vaginal inserts','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (24,1,NULL,1,NULL,'ophthalmic insert',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ophthalmic insert',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ophthalmic inserts','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (25,1,NULL,1,NULL,'buccal tab',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','buccal tablet',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'buccal tabs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (26,1,NULL,1,NULL,'dose',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','dose',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'doses','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (27,1,NULL,1,NULL,'lb',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pound',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'lbs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (28,1,NULL,1,NULL,'m2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','meter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'m2s','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (29,1,NULL,1,NULL,'in2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inch squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'in2s','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (30,1,NULL,1,NULL,'lesion',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','lesion',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'lesions','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (31,1,NULL,1,NULL,'gram/m2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/meter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/m2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (32,1,NULL,1,NULL,'kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'kgs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (33,1,NULL,1,NULL,'milliunit/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliunit/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'milliunits/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (34,1,NULL,1,NULL,'L',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','liter',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Ls','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (35,1,NULL,1,NULL,'gram/m2/hr',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/meter squared/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/m2/hr','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (36,1,NULL,1,NULL,'unit/m2/hr',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/meter squared/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/m2/hr','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (37,1,NULL,1,NULL,'irrig',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','irrigation',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'irrigs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (38,1,NULL,1,NULL,'lozenge',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','lozenge',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'lozenges','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (39,1,NULL,1,NULL,'cm2 of lesion',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','centimeter squared of lesion',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cm2s of lesion','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (40,1,NULL,1,NULL,'each/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (41,1,NULL,1,NULL,'mg/mL',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/milliliter',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/mL','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (42,1,NULL,1,NULL,'implant',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','implant',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'implants','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (43,1,NULL,1,NULL,'mmu cells/cm2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million cells/centimeter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmu cells/cm2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (44,1,NULL,1,NULL,'mL/min',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/min','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (45,1,NULL,1,NULL,'mcg/m2/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microgram/meter squared/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/m2/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (46,1,NULL,1,NULL,'pump',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pump',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pumps','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (47,1,NULL,1,NULL,'mCi',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','millicurie',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mCis','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (48,1,NULL,1,NULL,'nanogram/minute',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','nanogram/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'nanograms/minute','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (49,1,NULL,1,NULL,'bar/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','bar/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'bars/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (50,1,NULL,1,NULL,'bar',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','bar',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'bars','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (51,1,NULL,1,NULL,'drop/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','drop/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'drops/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (52,1,NULL,1,NULL,'mcg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microgram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (53,1,NULL,1,NULL,'mcg/minute',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microgram/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/minute','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (54,1,NULL,1,NULL,'mmol/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','millimole/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmols/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (55,1,NULL,1,NULL,'cm/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','centimeter/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cms/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (56,1,NULL,1,NULL,'mg/L',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/liter',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/L','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (57,1,NULL,1,NULL,'application/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','application/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'applications/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (58,1,NULL,1,NULL,'inch/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inch/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inches/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (59,1,NULL,1,NULL,'tablet or capsule/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet-capsule/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets or capsules/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (60,1,NULL,1,NULL,'applicatorful/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','applicatorful/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'applicatorfuls/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (61,1,NULL,1,NULL,'mg/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (62,1,NULL,1,NULL,'mL/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (63,1,NULL,1,NULL,'milliunit',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliunit',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'milliunits','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (64,1,NULL,1,NULL,'mg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (65,1,NULL,1,NULL,'inhalation/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inhalation/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inhalations/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (66,1,NULL,1,NULL,'spray/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','spray/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sprays/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (67,1,NULL,1,NULL,'mL/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (68,1,NULL,1,NULL,'softgel',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','softgel',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'softgels','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (69,1,NULL,1,NULL,'million units/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (70,1,NULL,1,NULL,'mg of phenytoin equiv',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram phenytoin equivalent',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs of phenytoin equiv','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (71,1,NULL,1,NULL,'unit/minute',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/minute','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (72,1,NULL,1,NULL,'insert/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','insert/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inserts/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (73,1,NULL,1,NULL,'insert',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','insert',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inserts','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (74,1,NULL,1,NULL,'mg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (75,1,NULL,1,NULL,'gram',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (76,1,NULL,1,NULL,'mmol',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','millimole',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmols','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (77,1,NULL,1,NULL,'drop',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','drop',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'drops','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (78,1,NULL,1,NULL,'unit',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (79,1,NULL,1,NULL,'mcg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microgram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (80,1,NULL,1,NULL,'cm',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','centimeter',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cms','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (81,1,NULL,1,NULL,'application',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','application',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'applications','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (82,1,NULL,1,NULL,'inch',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inch',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inches','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (83,1,NULL,1,NULL,'tablet or capsule',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet-capsule',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets or capsules','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (84,1,NULL,1,NULL,'applicatorful',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','applicatorful',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'applicatorfuls','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (85,1,NULL,1,NULL,'inhalation',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inhalation',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inhalations','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (86,1,NULL,1,NULL,'mL',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (87,1,NULL,1,NULL,'packet/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','packet/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'packets/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (88,1,NULL,1,NULL,'package/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','package/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'packages/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (89,1,NULL,1,NULL,'mg/square inch',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/inch squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/square inch','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (90,1,NULL,1,NULL,'million units/lesion',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/lesion',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/lesion','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (91,1,NULL,1,NULL,'mmol/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','millimole/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmols/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (92,1,NULL,1,NULL,'sprays',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','sprays (DO NOT USE)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sprays','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (93,1,NULL,1,NULL,'package',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','package',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'packages','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (94,1,NULL,1,NULL,'mEq/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliequivalent/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (95,1,NULL,1,NULL,'thousand units',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (96,1,NULL,1,NULL,'packet',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','packet',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'packets','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (97,1,NULL,1,NULL,'gram/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (98,1,NULL,1,NULL,'suppository',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','suppository',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'suppositories','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (99,1,NULL,1,NULL,'suppository/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','suppository/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'suppositories/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (100,1,NULL,1,NULL,'thousand units/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (101,1,NULL,1,NULL,'vaginal ring/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vaginal ring/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vaginal rings/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (102,1,NULL,1,NULL,'gummy',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gummy',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gummies','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (103,1,NULL,1,NULL,'patch/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','patch/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'patches/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (104,1,NULL,1,NULL,'thousand units/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (105,1,NULL,1,NULL,'million units/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (106,1,NULL,1,NULL,'thousand units/minute',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/minute','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (107,1,NULL,1,NULL,'vaginal ring',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vaginal ring',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vaginal rings','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (108,1,NULL,1,NULL,'million units',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (109,1,NULL,1,NULL,'mEq/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliequivalent/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (110,1,NULL,1,NULL,'patch',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','patch',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'patches','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (111,1,NULL,1,NULL,'million units/minute',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/minute','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (112,1,NULL,1,NULL,'mg/minute',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/minute','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (113,1,NULL,1,NULL,'minute',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'minutes','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (114,1,NULL,1,NULL,'mEq',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliequivalent',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (115,1,NULL,1,NULL,'spray',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','spray',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sprays','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (116,1,NULL,1,NULL,'milliunit/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliunit/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'milliunits/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (117,1,NULL,1,NULL,'unit/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (118,1,NULL,1,NULL,'hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'hours','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (119,1,NULL,1,NULL,'melt/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','melt/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'melts/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (120,1,NULL,1,NULL,'second',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','second',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'seconds','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (121,1,NULL,1,NULL,'mcg/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microgram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (122,1,NULL,1,NULL,'melt',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','melt',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'melts','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (123,1,NULL,1,NULL,'milliunit/minute',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliunit/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'milliunits/minute','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (124,1,NULL,1,NULL,'unit/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (125,1,NULL,1,NULL,'pad/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pad/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pads/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (126,1,NULL,1,NULL,'microunit',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microunit',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'microunits','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (127,1,NULL,1,NULL,'vial/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vial/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vials/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (128,1,NULL,1,NULL,'vial',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vial',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vials','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (129,1,NULL,1,NULL,'strip/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','strip/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'strips/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (130,1,NULL,1,NULL,'nanogram',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','nanogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'nanograms','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (131,1,NULL,1,NULL,'strip',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','strip',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'strips','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (132,1,NULL,1,NULL,'mg of phenytoin equiv/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram phenytoin equivalent/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs of phenytoin equiv/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (133,1,NULL,1,NULL,'mcg/kg/minute',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microgram/kilogram/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/kg/minute','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (134,1,NULL,1,NULL,'unit/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (135,1,NULL,1,NULL,'mg/kg/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/kilogram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/kg/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (136,1,NULL,1,NULL,'mcg/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microgram/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (137,1,NULL,1,NULL,'milliunit/kg/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliunit/kilogram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'milliunits/kg/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (138,1,NULL,1,NULL,'unit/kg/minute',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/kilogram/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/kg/minute','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (139,1,NULL,1,NULL,'unit/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (140,1,NULL,1,NULL,'unit/kg/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/kilogram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/kg/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (141,1,NULL,1,NULL,'gram/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (142,1,NULL,1,NULL,'mL/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (143,1,NULL,1,NULL,'gram/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (144,1,NULL,1,NULL,'mcg/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microgram/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (145,1,NULL,1,NULL,'mEq/kg/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliequivalent/kilogram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs/kg/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (146,1,NULL,1,NULL,'tablet or capsule/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet-capsule/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets or capsules/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (147,1,NULL,1,NULL,'tablet or capsule/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet-capsule/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets or capsules/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (148,1,NULL,1,NULL,'mmol/kg/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','millimole/kilogram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmols/kg/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (149,1,NULL,1,NULL,'nanogram/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','nanogram/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'nanograms/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (150,1,NULL,1,NULL,'mEq/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliequivalent/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (151,1,NULL,1,NULL,'million units/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (152,1,NULL,1,NULL,'million units/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (153,1,NULL,1,NULL,'mEq/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliequivalent/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (154,1,NULL,1,NULL,'nanogram/kg/minute',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','nanogram/kilogram/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'nanograms/kg/minute','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (155,1,NULL,1,NULL,'mg/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (156,1,NULL,1,NULL,'thousand units/kg/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/kilogram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/kg/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (157,1,NULL,1,NULL,'mg/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (158,1,NULL,1,NULL,'million units/kg/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/kilogram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/kg/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (159,1,NULL,1,NULL,'mg/pound',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/pound',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/pound','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (160,1,NULL,1,NULL,'mg/kg/minute',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/kilogram/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/kg/minute','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (161,1,NULL,1,NULL,'packet/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','packet/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'packets/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (162,1,NULL,1,NULL,'million units/kg/minute',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/kilogram/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/kg/minute','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (163,1,NULL,1,NULL,'mL/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (164,1,NULL,1,NULL,'thousand units/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (165,1,NULL,1,NULL,'thousand units/kg/minute',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/kilogram/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/kg/minute','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (166,1,NULL,1,NULL,'thousand units/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (167,1,NULL,1,NULL,'mmol/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','millimole/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmols/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (168,1,NULL,1,NULL,'mmol/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','millimole/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmols/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (169,1,NULL,1,NULL,'mcg/kg/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microgram/kilogram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/kg/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (170,1,NULL,1,NULL,'mg of phenytoin equiv/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram phenytoin equivalent/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs of phenytoin equiv/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (171,1,NULL,1,NULL,'mcg/m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microgram/meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (172,1,NULL,1,NULL,'unit/m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (173,1,NULL,1,NULL,'mg/m2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/meter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/m2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (174,1,NULL,1,NULL,'gram/m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (175,1,NULL,1,NULL,'million units/m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (176,1,NULL,1,NULL,'unit/m2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/meter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/m2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (177,1,NULL,1,NULL,'mg/m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (178,1,NULL,1,NULL,'mg/m2/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/meter squared/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/m2/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (179,1,NULL,1,NULL,'thousand units/m2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand unit/meter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/m2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (180,1,NULL,1,NULL,'mL/m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter/meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (181,1,NULL,1,NULL,'microunit/m2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microunit/meter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'microunits/m2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (182,1,NULL,1,NULL,'mL/m2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter/meter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/m2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (183,1,NULL,1,NULL,'million units/m2/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/meter squared/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/m2/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (184,1,NULL,1,NULL,'million units/m2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/meter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/m2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (185,1,NULL,1,NULL,'thousand units/m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (186,1,NULL,1,NULL,'mcg/m2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microgram/meter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/m2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (187,1,NULL,1,NULL,'each',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (188,1,NULL,1,NULL,'each/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (189,1,NULL,1,NULL,'each/dose',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/dose',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/dose','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (190,1,NULL,1,NULL,'each/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (191,1,NULL,1,NULL,'each/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (192,1,NULL,1,NULL,'each/kg/dose',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/kilogram/dose',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/kg/dose','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (193,1,NULL,1,NULL,'each/kg/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/kilogram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/kg/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (194,1,NULL,1,NULL,'each/1.73 m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/1.73 meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/1.73 m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (195,1,NULL,1,NULL,'gram/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (196,1,NULL,1,NULL,'gram/kg/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/kilogram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/kg/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (197,1,NULL,1,NULL,'mg/dose',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/dose',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/dose','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (198,1,NULL,1,NULL,'mg/kg/dose',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/kilogram/dose',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/kg/dose','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (199,1,NULL,1,NULL,'mg/1.73 m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/1.73 meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/1.73 m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (200,1,NULL,1,NULL,'mL/dose',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter/dose',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/dose','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (201,1,NULL,1,NULL,'mL/kg/dose',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter/kilogram/dose',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/kg/dose','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (202,1,NULL,1,NULL,'mL/kg/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter/kilogram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/kg/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (203,1,NULL,1,NULL,'mL/1.73 m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter/1.73 meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/1.73 m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (204,1,NULL,1,NULL,'scoop',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','scoop',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'scoops','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (205,1,NULL,1,NULL,'scoop/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','scoop/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'scoops/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (206,1,NULL,1,NULL,'scoop/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','scoop/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'scoops/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (207,1,NULL,1,NULL,'mL/cm2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter/centimeter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/cm2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (208,1,NULL,1,NULL,'mL/cm2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter/centimeter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/cm2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (209,1,NULL,1,NULL,'mg/cm2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/centimeter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/cm2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (210,1,NULL,1,NULL,'mg/cm2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/centimeter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/cm2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (211,1,NULL,1,NULL,'neb',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','nebule',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'nebs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (212,1,NULL,1,NULL,'towelette',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','towelette',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'towelettes','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (213,1,NULL,1,NULL,'towelette/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','towelette/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'towelettes/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (214,1,NULL,1,NULL,'wafer',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','wafer',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'wafers','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (215,1,NULL,1,NULL,'wafer/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','wafer/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'wafers/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (216,1,NULL,1,NULL,'mg of phenytoin equiv/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram phenytoin equivalent/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs of phenytoin equiv/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (217,1,NULL,1,NULL,'scoop/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','scoop/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'scoops/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (218,1,NULL,1,NULL,'vag insert/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vaginal insert/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vag inserts/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (219,1,NULL,1,NULL,'lozenge/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','lozenge/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'lozenges/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (220,1,NULL,1,NULL,'device',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','device',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'devices','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (221,1,NULL,1,NULL,'mmu cells',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million cells',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmu cells','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (222,1,NULL,1,NULL,'m',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','meter',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ms','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (223,1,NULL,1,NULL,'mEq/m2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliequivalent/meter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs/m2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (224,1,NULL,1,NULL,'mEq/m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliequivalent/meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs/m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (225,1,NULL,1,NULL,'mCi/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','millicurie/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mCis/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (226,1,NULL,1,NULL,'microL',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microliter',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'microLs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (227,1,NULL,1,NULL,'microL/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microliter/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'microLs/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (228,1,NULL,1,NULL,'mL/m2/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter/meter squared/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/m2/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (229,1,NULL,1,NULL,'micromole',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','micromole',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'micromoles','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (230,1,NULL,1,NULL,'piece of gum/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','piece of gum/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pieces of gum/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (231,1,NULL,1,NULL,'amp',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ampule',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'amps','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (232,1,NULL,1,NULL,'g/100 mL',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/100 mL',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gs/100 mL','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (233,1,NULL,1,NULL,'mg/100 mL',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/100 mL',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/100 mL','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (234,1,NULL,1,NULL,'g/100 g',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/100 g',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gs/100 g','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (235,1,NULL,1,NULL,'mg/100 g',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram/100 grams',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/100 g','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (236,1,NULL,1,NULL,'microCi',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microcurie',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'microCis','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (237,1,NULL,1,NULL,'ea/kg/min',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/kilogram/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eas/kg/min','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (238,1,NULL,1,NULL,'ea/min',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eas/min','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (239,1,NULL,1,NULL,'ea/m2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/meter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eas/m2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (240,1,NULL,1,NULL,'ea/m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eas/m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (241,1,NULL,1,NULL,'ea/m2/hr',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/meter squared/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eas/m2/hr','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (242,1,NULL,1,NULL,'inh/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inhalation/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inhs/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (243,1,NULL,1,NULL,'inh/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inhalation/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inhs/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (244,1,NULL,1,NULL,'L/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','liter/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Ls/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (245,1,NULL,1,NULL,'L/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','liter/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Ls/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (246,1,NULL,1,NULL,'L/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','liter/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Ls/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (247,1,NULL,1,NULL,'L/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','liter/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Ls/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (248,1,NULL,1,NULL,'L/kg/hour',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','liter/kilogram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Ls/kg/hour','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (249,1,NULL,1,NULL,'mL/kg/min',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliliter/kilogram/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/kg/min','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (250,1,NULL,1,NULL,'mmu cells/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million cells/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmu cells/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (251,1,NULL,1,NULL,'film',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','film',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'films','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (252,1,NULL,1,NULL,'film/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','film/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'films/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (253,1,NULL,1,NULL,'sachet/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','sachet/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sachets/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (254,1,NULL,1,NULL,'patch/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','patch/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'patches/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (255,1,NULL,1,NULL,'patch/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','patch/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'patches/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (256,1,NULL,1,NULL,'vial/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vial/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vials/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (257,1,NULL,1,NULL,'vial/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vial/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vials/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (258,1,NULL,1,NULL,'packet/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','packet/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'packets/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (259,1,NULL,1,NULL,'inch/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inch/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inches/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (260,1,NULL,1,NULL,'inch/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inch/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inches/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (261,1,NULL,1,NULL,'cell',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cell',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cells','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (262,1,NULL,1,NULL,'minim',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','minim',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'minims','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (263,1,NULL,1,NULL,'ft',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','foot',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'fts','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (264,1,NULL,1,NULL,'bead',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','bead',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'beads','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (265,1,NULL,1,NULL,'marker',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','marker',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'markers','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (266,1,NULL,1,NULL,'mm',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','millimeter',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mms','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (267,1,NULL,1,NULL,'gauge',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gauge',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gauges','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (268,1,NULL,1,NULL,'oz',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ounce',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ozs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (269,1,NULL,1,NULL,'yd',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','yard',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'yds','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (270,1,NULL,1,NULL,'cp',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cup',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cps','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (271,1,NULL,1,NULL,'ng per day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','nanogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ngs per day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (272,1,NULL,1,NULL,'nanogram/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','nanogram/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'nanograms/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (273,1,NULL,1,NULL,'ng per hr',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','nanogram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ngs per hr','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (274,1,NULL,1,NULL,'vial/m2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vial/meter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vials/m2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (275,1,NULL,1,NULL,'vial/m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vial/meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vials/m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (276,1,NULL,1,NULL,'tablet/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (277,1,NULL,1,NULL,'capsule/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','capsule/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'capsules/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (278,1,NULL,1,NULL,'cell/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cell/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cells/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (279,1,NULL,1,NULL,'pop',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Lollipop',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pops','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (280,1,NULL,1,NULL,'amp/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ampule/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'amps/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (281,1,NULL,1,NULL,'vial/hr',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vial/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vials/hr','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (282,1,NULL,1,NULL,'L/min',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','liter/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Ls/min','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (283,1,NULL,1,NULL,'mm2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','millimeter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mm2s','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (284,1,NULL,1,NULL,'drp/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','drop/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'drps/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (285,1,NULL,1,NULL,'drp/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','drop/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'drps/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (286,1,NULL,1,NULL,'cm2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','centimeter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cm2s','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (287,1,NULL,1,NULL,'tablet or capsule/m2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet-capsule/meter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets or capsules/m2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (288,1,NULL,1,NULL,'tablet or capsule/m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet-capsule/meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets or capsules/m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (289,1,NULL,1,NULL,'tablet/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (290,1,NULL,1,NULL,'capsule/kg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','capsule/kilogram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'capsules/kg/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (291,1,NULL,1,NULL,'tablet/m2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet/meter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets/m2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (292,1,NULL,1,NULL,'capsule/m2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','capsule/meter squared',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'capsules/m2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (293,1,NULL,1,NULL,'tablet/m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet/meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets/m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (294,1,NULL,1,NULL,'capsule/m2/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','capsule/meter squared/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'capsules/m2/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (295,1,NULL,1,NULL,'capsule/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','capsule/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'capsules/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (296,1,NULL,1,NULL,'cells/kg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cells/kilogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cells/kg','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (297,1,NULL,1,NULL,'vg',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vector genome',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vgs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (298,1,NULL,1,NULL,'vg/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vector genome/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vgs/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (299,1,NULL,1,NULL,'pump/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pump/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pumps/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (300,1,NULL,1,NULL,'tube',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tube',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tubes','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (301,1,NULL,1,NULL,'tube/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tube/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tubes/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (302,1,NULL,1,NULL,'implant/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','implant/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'implants/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (303,1,NULL,1,NULL,'billion cells/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','billion cells/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'billion cells/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (304,1,NULL,1,NULL,'billion cells',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','billion cells',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'billion cells','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (305,1,NULL,1,NULL,'troche',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','troche',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'troches','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (306,1,NULL,1,NULL,'blister',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','blister',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'blisters','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (307,1,NULL,1,NULL,'kit',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','kit',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'kits','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (308,1,NULL,1,NULL,'lancet',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','lancet',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'lancets','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (309,1,NULL,1,NULL,'pen needle',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pen needle',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pen needles','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (310,1,NULL,1,NULL,'sponge',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','sponge',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sponges','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (311,1,NULL,1,NULL,'stick',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','stick',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sticks','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (312,1,NULL,1,NULL,'swab',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','swab',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'swabs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (313,1,NULL,1,NULL,'tspn',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','teaspoon',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tspns','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (314,1,NULL,1,NULL,'tbspn',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablespoon',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tbspns','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (315,1,NULL,1,NULL,'act',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','actuation',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'acts','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (316,1,NULL,1,NULL,'squirt',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','squirt',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'squirts','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (317,1,NULL,1,NULL,'gel',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gel',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gels','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (318,1,NULL,1,NULL,'gum',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gum',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gums','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (319,1,NULL,1,NULL,'piece',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','piece',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pieces','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (320,1,NULL,1,NULL,'container',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','container',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'containers','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (321,1,NULL,1,NULL,'respule',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','respule',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'respules','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (322,1,NULL,1,NULL,'applicator',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','applicator',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'applicators','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (323,1,NULL,1,NULL,'pledget',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pledget',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pledgets','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (324,1,NULL,1,NULL,'pill',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pill',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pills','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (325,1,NULL,1,NULL,'pen',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pen',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pens','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (326,1,NULL,1,NULL,'cartridge',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cartridge',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cartridges','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (327,1,NULL,1,NULL,'syringe',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','syringe',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'syringes','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (328,1,NULL,1,NULL,'mg fish oil',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram fish oil',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs fish oil','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (329,1,NULL,1,NULL,'mg fish oil/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milligram fish oil/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs fish oil/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (330,1,NULL,1,NULL,'puff/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','puff/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'puffs/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (331,1,NULL,1,NULL,'actuation/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','actuation/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'actuations/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (332,1,NULL,1,NULL,'applicator/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','applicator/ day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'applicators/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (333,1,NULL,1,NULL,'cartridge/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cartridge/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cartridges/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (334,1,NULL,1,NULL,'container/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','container/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'containers/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (335,1,NULL,1,NULL,'device/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','device/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'devices/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (336,1,NULL,1,NULL,'gel/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gel/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gels/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (337,1,NULL,1,NULL,'gum/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gum/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gums/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (338,1,NULL,1,NULL,'gummy/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gummy/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gummies/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (339,1,NULL,1,NULL,'lancet/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','lancet/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'lancets/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (340,1,NULL,1,NULL,'lollipop/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','lollipop/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'lollipops/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (341,1,NULL,1,NULL,'nebule/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','nebule/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'nebules/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (342,1,NULL,1,NULL,'pen/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pen/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pens/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (343,1,NULL,1,NULL,'piece/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','piece/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pieces/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (344,1,NULL,1,NULL,'pill/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pill/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pills/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (345,1,NULL,1,NULL,'pledget/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pledget/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pledgets/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (346,1,NULL,1,NULL,'respule/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','respule/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'respules/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (347,1,NULL,1,NULL,'softgel/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','softgel/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'softgels/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (348,1,NULL,1,NULL,'squirt/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','squirt/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'squirts/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (349,1,NULL,1,NULL,'syringe/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','syringe/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'syringes/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (350,1,NULL,1,NULL,'tbsp/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablespoon/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tbsps/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (351,1,NULL,1,NULL,'kit/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','kit/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'kits/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (352,1,NULL,1,NULL,'units/day',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','units/day (DO NOT USE)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/day','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (353,1,NULL,1,NULL,'million cells/cm2',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million cells/square centimeter-Do Not Use: Duplicate of 193',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million cells/cm2','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "code", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active") VALUES (354,1,NULL,1,NULL,'units',NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','units (DO NOT USE)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "code" = EXCLUDED."code", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name" WHERE form_list_unit_display_map.allow_sync = 'Yes';
COMMIT;
