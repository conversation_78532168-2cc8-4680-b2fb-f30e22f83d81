BEGIN;
SET session_replication_role = replica;

-- Clear the table first:
DELETE FROM form_list_therapy;

-- Insert your new therapies:
INSERT INTO form_list_therapy ("name","code","id","created_on","created_by","auto_name") VALUES
('ABX','abx',30,'2025-02-14 00:00:00',1,'ABX'),
('ALLERGY','allergy',31,'2025-02-14 00:00:00',1,'ALLERGY'),
('ANAPHYLA','anaphyla',32,'2025-02-14 00:00:00',1,'ANAPHYLA'),
('ANEMIA','anemia',33,'2025-02-14 00:00:00',1,'ANEMIA'),
('ANTI-INF','anti_inf',34,'2025-02-14 00:00:00',1,'ANTI-INF'),
('ANTICOAG','anticoag',35,'2025-02-14 00:00:00',1,'AN<PERSON>CO<PERSON>'),
('BIOLOGIC','biologic',36,'2025-02-14 00:00:00',1,'BIOLOGIC'),
('CARDIO','cardio',37,'2025-02-14 00:00:00',1,'CARDIO'),
('COMPOUND','compound',38,'2025-02-14 00:00:00',1,'COMPOUND'),
('CROHN/UC','crohn_uc',39,'2025-02-14 00:00:00',1,'CROHN/UC'),
('DERM','derm',40,'2025-02-14 00:00:00',1,'DERM'),
('DIABETES','diabetes',41,'2025-02-14 00:00:00',1,'DIABETES'),
('DILUENT','diluent',42,'2025-02-14 00:00:00',1,'DILUENT'),
('ENZYME','enzyme',43,'2025-02-14 00:00:00',1,'ENZYME'),
('FACTOR','factor',44,'2025-02-14 00:00:00',1,'FACTOR'),
('FERTILIT','fertilit',45,'2025-02-14 00:00:00',1,'FERTILIT'),
('FLUSH','flush',46,'2025-02-14 00:00:00',1,'FLUSH'),
('GASTRO','gastro',47,'2025-02-14 00:00:00',1,'GASTRO'),
('HEMOPHIL','hemophil',48,'2025-02-14 00:00:00',1,'HEMOPHIL'),
('HEP B','hep_b',49,'2025-02-14 00:00:00',1,'HEP B'),
('HEP C','hep_c',50,'2025-02-14 00:00:00',1,'HEP C'),
('HGH','hgh',51,'2025-02-14 00:00:00',1,'HGH'),
('HIV','hiv',52,'2025-02-14 00:00:00',1,'HIV'),
('HTN','htn',53,'2025-02-14 00:00:00',1,'HTN'),
('INOTROPE','inotrope',54,'2025-02-14 00:00:00',1,'INOTROPE'),
('IRON','iron',55,'2025-02-14 00:00:00',1,'IRON'),
('IV-ABX','iv_abx',56,'2025-02-14 00:00:00',1,'IV-ABX'),
('IV-TPN','iv_tpn',57,'2025-02-14 00:00:00',1,'IV-TPN'),
('IVIG','ivig',58,'2025-02-14 00:00:00',1,'IVIG'),
('MAB','mab',59,'2025-02-14 00:00:00',1,'MAB'),
('MS','ms',60,'2025-02-14 00:00:00',1,'MS'),
('NARCOTIC','narcotic',61,'2025-02-14 00:00:00',1,'NARCOTIC'),
('NEPHROLO','nephrolo',62,'2025-02-14 00:00:00',1,'NEPHROLO'),
('NEURO','neuro',63,'2025-02-14 00:00:00',1,'NEURO'),
('ONCOLOGY','oncology',64,'2025-02-14 00:00:00',1,'ONCOLOGY'),
('OPHTHAL','ophthal',65,'2025-02-14 00:00:00',1,'OPHTHAL'),
('ORGAN TX','organ_tx',66,'2025-02-14 00:00:00',1,'ORGAN TX'),
('OSTEOART','osteoart',67,'2025-02-14 00:00:00',1,'OSTEOART'),
('OSTEOPOR','osteopor',68,'2025-02-14 00:00:00',1,'OSTEOPOR'),
('OTHER','other',69,'2025-02-14 00:00:00',1,'OTHER'),
('PRE-MED','pre_med',70,'2025-02-14 00:00:00',1,'PRE-MED'),
('PULMONAR','pulmonar',71,'2025-02-14 00:00:00',1,'PULMONAR'),
('RHEUM','rheum',72,'2025-02-14 00:00:00',1,'RHEUM'),
('SCIG','scig',73,'2025-02-14 00:00:00',1,'SCIG'),
('SUPPLY','supply',74,'2025-02-14 00:00:00',1,'SUPPLY'),
('UROLOGY','urology',75,'2025-02-14 00:00:00',1,'UROLOGY'),
('VACCINE','vaccine',76,'2025-02-14 00:00:00',1,'VACCINE'),
('WT LOSS','wt_loss',77,'2025-02-14 00:00:00',1,'WT LOSS');

-- Bump the sequence so subsequent inserts pick up after the highest ID:
ALTER SEQUENCE "form_list_therapy_id_seq" RESTART WITH 78;

SET session_replication_role = DEFAULT;
COMMIT;
