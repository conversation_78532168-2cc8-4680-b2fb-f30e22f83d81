BEGIN;
INSERT INTO form_template_report ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "type", "is_page_less", "page_width", "page_height", "json_data", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,10,NULL,NULL,NULL,'2024-09-02T02:21:23.000Z','Template for Page (Continuous Sections)',NULL,NULL,'2024-05-08T16:12:37.000Z',NULL,'Template for Page (Continuous Sections)','page01a','Page','No',8.5,11,E'{"Name":"Page Template","Width":"0in","Layers":[{"Name":"default"}],"CustomProperties":[{"Name":"DisplayType","Value":"Page"},{"Name":"SizeType","Value":"Default"},{"Name":"PaperOrientation","Value":"Portrait"},{"Name":"CollapseWhiteSpace","Value":"True"}],"Version":"7.6.0","Page":{"PageWidth":"8.5in","PageHeight":"11in","RightMargin":"0in","LeftMargin":"0in","TopMargin":"0in","BottomMargin":"0in","Columns":1,"ColumnSpacing":"0.5in","PaperOrientation":"Portrait"},"DataSources":[{"Name":"apiform","ConnectionProperties":{"DataProvider":"JSON","ConnectString":"endpoint=/api/form"}}],"ReportSections":[{"Type":"Continuous","Name":"ContinuousSection1","Page":{"PageWidth":"8.5in","PageHeight":"11in","RightMargin":"0.5in","LeftMargin":"0.5in","TopMargin":"0.5in","BottomMargin":"0.5in","Columns":1,"ColumnSpacing":"0.5in","PaperOrientation":"Portrait"},"Width":"7.5in","PageHeader":{"Height":"0.5208in","ReportItems":[{"Type":"image","Name":"Image1","Value":"=\\"/api/file/secure/\\" & First(Fields.Item(\\"pdf_logo.filehash\\").Value, \\"company\\")","Sizing":"FitProportional","Width":"3in","Height":"0.5in"},{"Type":"textbox","Name":"TextBox2","ZIndex":1,"CanGrow":true,"KeepTogether":true,"Value":"DOCUMENT","Style":{"FontSize":"12pt","FontWeight":"Bold","PaddingLeft":"2pt","PaddingRight":"2pt","PaddingTop":"2pt","PaddingBottom":"2pt","Color":"#888888","TextAlign":"Right","VerticalAlign":"Middle"},"Left":"4.7084in","Top":"0.25in","Width":"1.75in","Height":"0.25in"},{"Type":"textbox","Name":"TextBox3","ZIndex":2,"CanGrow":true,"KeepTogether":true,"Value":"1234567","Style":{"FontFamily":"Lucida Console","FontSize":"12pt","FontWeight":"Bold","PaddingLeft":"2pt","PaddingRight":"2pt","PaddingTop":"2pt","PaddingBottom":"2pt","VerticalAlign":"Middle"},"Left":"6.4879in","Top":"0.25in","Width":"1in","Height":"0.25in"}],"Style":{"Border":{"Color":"#aaaaaa"},"BottomBorder":{"Style":"Solid"}}},"Body":{"Height":"4.4551in","ReportItems":[{"Type":"textbox","Name":"TextBox1","ZIndex":1,"CanGrow":true,"KeepTogether":true,"Value":"Template For Page (Continuous Sections)","Style":{"PaddingLeft":"2pt","PaddingRight":"2pt","PaddingTop":"2pt","PaddingBottom":"2pt","TextAlign":"Center","VerticalAlign":"Middle","BackgroundColor":"#90cbf9"},"Left":"0.75in","Top":"4.0801in","Width":"6in","Height":"0.375in"}]},"PageFooter":{"ReportItems":[{"Type":"textbox","Name":"TextBox5","ZIndex":2,"CanGrow":true,"KeepTogether":true,"Value":"=\\"Page \\" & Globals!PageNumber & \\" of \\" & Globals!TotalPages","Style":{"FontSize":"7pt","PaddingRight":"4pt","PaddingTop":"2pt","TextAlign":"Right"},"Left":"5.25in","Width":"2.25in","Height":"0.25in"},{"Type":"textbox","Name":"TextBox4","CanGrow":true,"KeepTogether":true,"Value":"=First(Fields!name.Value) & \\" • \\" & First(Fields!street.Value) & IIF(First(Fields!street2.Value) <> \\"\\", \\", \\" + First(Fields!street2.Value), \\"\\") & \\", \\" & First(Fields!city.Value) & \\", \\" & First(Fields!state_id.Value) & \\" \\" & Substring(Trim(First(Fields!zip.Value)), 0, 5) & \\" • P: \\" & First(Fields!phone.Value) & \\" • F: \\" & First(Fields!fax.Value)","Style":{"FontSize":"6pt","PaddingTop":"3pt","Color":"#888888","TextAlign":"Center"},"Left":"1in","Width":"5.75in","Height":"0.25in"},{"Type":"textbox","Name":"TextBox6","ZIndex":5,"CanGrow":true,"KeepTogether":true,"Value":"=Globals!ExecutionTime","Style":{"Border":{"Color":"#aaaaaa"},"TopBorder":{"Style":"Solid"},"FontSize":"7pt","PaddingLeft":"4pt","PaddingTop":"2pt","TextAlign":"Left"},"Width":"2.5in","Height":"0.25in"}],"Style":{"Border":{"Color":"#aaaaaa"},"TopBorder":{"Style":"Solid"}}}}],"DataSets":[{"Name":"company","Fields":[{"Name":"_meta.source","DataField":"_meta.source"},{"Name":"id","DataField":"id"},{"Name":"created_by","DataField":"created_by"},{"Name":"change_by","DataField":"change_by"},{"Name":"updated_by","DataField":"updated_by"},{"Name":"reviewed_by","DataField":"reviewed_by"},{"Name":"state","DataField":"state"},{"Name":"reviewed_on","DataField":"reviewed_on"},{"Name":"deleted","DataField":"deleted"},{"Name":"archived","DataField":"archived"},{"Name":"updated_on","DataField":"updated_on"},{"Name":"auto_name","DataField":"auto_name"},{"Name":"change_type","DataField":"change_type"},{"Name":"change_data","DataField":"change_data"},{"Name":"created_on","DataField":"created_on"},{"Name":"change_on","DataField":"change_on"},{"Name":"name","DataField":"name"},{"Name":"logo.filehash","DataField":"logo.filehash"},{"Name":"logo.filename","DataField":"logo.filename"},{"Name":"logo.filesize","DataField":"logo.filesize"},{"Name":"logo.mimetype","DataField":"logo.mimetype"},{"Name":"street","DataField":"street"},{"Name":"street2","DataField":"street2"},{"Name":"city","DataField":"city"},{"Name":"zip","DataField":"zip"},{"Name":"phone","DataField":"phone"},{"Name":"fax","DataField":"fax"},{"Name":"support","DataField":"support"},{"Name":"supmail","DataField":"supmail"},{"Name":"bi_refresh","DataField":"bi_refresh"},{"Name":"jwt_timeout","DataField":"jwt_timeout"},{"Name":"kb_refresh","DataField":"kb_refresh"},{"Name":"snap_refresh","DataField":"snap_refresh"},{"Name":"ui_lock","DataField":"ui_lock"},{"Name":"filter_wc","DataField":"filter_wc"},{"Name":"prevent_datetime_focus_popup","DataField":"prevent_datetime_focus_popup"},{"Name":"no_download","DataField":"no_download"},{"Name":"toast_form_level_errors","DataField":"toast_form_level_errors"},{"Name":"separate_numbers","DataField":"separate_numbers"},{"Name":"number_separator","DataField":"number_separator"},{"Name":"decimal_separator","DataField":"decimal_separator"},{"Name":"format_currency","DataField":"format_currency"},{"Name":"currency_prefix","DataField":"currency_prefix"},{"Name":"esign_title","DataField":"esign_title"},{"Name":"pdf_logo.filehash","DataField":"pdf_logo.filehash"},{"Name":"pdf_logo.filename","DataField":"pdf_logo.filename"},{"Name":"pdf_logo.filesize","DataField":"pdf_logo.filesize"},{"Name":"pdf_logo.mimetype","DataField":"pdf_logo.mimetype"},{"Name":"max_collections","DataField":"max_collections"},{"Name":"max_grid_columns","DataField":"max_grid_columns"},{"Name":"max_grid_rows","DataField":"max_grid_rows"},{"Name":"max_filter_rows","DataField":"max_filter_rows"},{"Name":"max_data_rows","DataField":"max_data_rows"},{"Name":"error_msg","DataField":"error_msg"},{"Name":"cal_default_view","DataField":"cal_default_view"},{"Name":"weekends","DataField":"weekends"},{"Name":"hidden_days","DataField":"hidden_days"},{"Name":"first_day","DataField":"first_day"},{"Name":"default_event_duration","DataField":"default_event_duration"},{"Name":"default_event_span","DataField":"default_event_span"},{"Name":"state_id","DataField":"state_id"},{"Name":"surescripts_id","DataField":"surescripts_id"},{"Name":"default_lot_expire_days","DataField":"default_lot_expire_days"},{"Name":"default_order_expire_days","DataField":"default_order_expire_days"},{"Name":"next_delivery_day","DataField":"next_delivery_day"},{"Name":"next_compound_days","DataField":"next_compound_days"},{"Name":"dose_start_days","DataField":"dose_start_days"},{"Name":"default_order_days","DataField":"default_order_days"},{"Name":"refill_reminder_days_out","DataField":"refill_reminder_days_out"},{"Name":"ncpdp_new_pref","DataField":"ncpdp_new_pref"},{"Name":"ncpdp_refill_pref","DataField":"ncpdp_refill_pref"},{"Name":"overwrite_manual_claim_items","DataField":"overwrite_manual_claim_items"},{"Name":"auto_test_claim","DataField":"auto_test_claim"},{"Name":"mm_ready_pref","DataField":"mm_ready_pref"},{"Name":"password_strength","DataField":"password_strength"},{"Name":"bg_color","DataField":"bg_color"},{"Name":"fg_color","DataField":"fg_color"},{"Name":"dt_inventory_check","DataField":"dt_inventory_check"},{"Name":"est_qty_inv_check","DataField":"est_qty_inv_check"},{"Name":"refill_days_out","DataField":"refill_days_out"},{"Name":"adjustment_threshold","DataField":"adjustment_threshold"},{"Name":"created_by_auto_name","DataField":"created_by_auto_name"},{"Name":"change_by_auto_name","DataField":"change_by_auto_name"},{"Name":"updated_by_auto_name","DataField":"updated_by_auto_name"},{"Name":"reviewed_by_auto_name","DataField":"reviewed_by_auto_name"},{"Name":"state_id_auto_name","DataField":"state_id_auto_name"}],"Query":{"DataSourceName":"apiform","CommandText":"uri=/company/1;jpath=$[0]"},"CaseSensitivity":"Auto","KanatypeSensitivity":"Auto","AccentSensitivity":"Auto","WidthSensitivity":"Auto"}]}','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "type" = EXCLUDED."type", "is_page_less" = EXCLUDED."is_page_less", "page_width" = EXCLUDED."page_width", "page_height" = EXCLUDED."page_height", "json_data" = EXCLUDED."json_data" WHERE form_template_report.allow_sync = 'Yes';
INSERT INTO form_template_report ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "type", "is_page_less", "page_width", "page_height", "json_data", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,10,NULL,NULL,NULL,'2024-09-05T00:53:08.000Z','Template For Label (Fixed Layout)',NULL,NULL,'2024-05-08T16:12:58.000Z',NULL,'Template For Label (Fixed Layout)','label01a','Label','No',4,6,E'{"Name":"Label Template11","Width":"6in","Layers":[{"Name":"default"}],"CustomProperties":[{"Name":"DisplayType","Value":"Page"},{"Name":"SizeType","Value":"Default"},{"Name":"PaperOrientation","Value":"Portrait"},{"Name":"LayoutOrder","Value":"ZOrder"},{"Name":"CollapseWhiteSpace","Value":"True"}],"Version":"7.6.0","Page":{"PageWidth":"6in","PageHeight":"4in","RightMargin":"0in","LeftMargin":"0in","TopMargin":"0in","BottomMargin":"0in","Columns":1,"ColumnSpacing":"0in","PaperOrientation":"Portrait"},"DataSources":[{"Name":"apiform","ConnectionProperties":{"DataProvider":"JSON","ConnectString":"endpoint=/api/form"}}],"FixedPage":{"Pages":[{"ReportItems":[{"Type":"textbox","Name":"TextBox1","ZIndex":1,"CanGrow":true,"KeepTogether":true,"Value":"Template For Label (Fixed Layout)","Style":{"PaddingLeft":"2pt","PaddingRight":"2pt","PaddingTop":"2pt","PaddingBottom":"2pt","TextAlign":"Center","VerticalAlign":"Middle","BackgroundColor":"#90cbf9"},"Width":"6in","Height":"0.375in"},{"Type":"image","Name":"Image1","ZIndex":2,"Value":"=\\"/api/file/secure/\\" & First(Fields.Item(\\"pdf_logo.filehash\\").Value, \\"company\\")","Sizing":"FitProportional","Style":{"Border":{"Color":"#aaaaaa"},"TopBorder":{"Style":"Solid"},"PaddingLeft":"4pt","PaddingBottom":"4pt"},"Top":"3.75in","Width":"1.5in","Height":"0.25in"},{"Type":"textbox","Name":"TextBox6","CanGrow":true,"KeepTogether":true,"Value":"=First(Fields!name.Value) & \\" • \\" & First(Fields!street.Value) & IIF(First(Fields!street2.Value) <> \\"\\", \\", \\" + First(Fields!street2.Value), \\"\\") & \\", \\" & First(Fields!city.Value) & \\", \\" & First(Fields!state_id.Value) & \\" \\" & Substring(Trim(First(Fields!zip.Value)), 0, 5) & \\" • P: \\" & First(Fields!phone.Value) & \\" • F: \\" & First(Fields!fax.Value)","Style":{"Border":{"Color":"#aaaaaa"},"TopBorder":{"Style":"Solid"},"FontSize":"5pt","PaddingRight":"4pt","PaddingTop":"2pt","Color":"#888888","TextAlign":"Center"},"Top":"3.75in","Width":"6in","Height":"0.25in"},{"Type":"textbox","Name":"TextBox3","ZIndex":3,"CanGrow":true,"KeepTogether":true,"Value":"=Globals!ExecutionTime","Style":{"Border":{"Color":"#aaaaaa"},"TopBorder":{"Style":"Solid"},"FontSize":"5pt","PaddingRight":"4pt","PaddingTop":"2pt","TextAlign":"Right"},"Left":"3.5in","Top":"3.75in","Width":"2.5in","Height":"0.25in"}],"LeftMargin":"0in","RightMargin":"0in","TopMargin":"0in","BottomMargin":"0in","PaperOrientation":"Portrait"},{"ReportItems":[{"Type":"textbox","Name":"TextBox2","CanGrow":true,"KeepTogether":true,"Value":"[Optional] Overflow section can go on this page","Style":{"PaddingLeft":"2pt","PaddingRight":"2pt","PaddingTop":"2pt","PaddingBottom":"2pt","TextAlign":"Center","VerticalAlign":"Middle","BackgroundColor":"#90cbf9"},"Width":"6in","Height":"0.375in"}],"LeftMargin":"0in","RightMargin":"0in","TopMargin":"0in","BottomMargin":"0in","PaperOrientation":"Portrait"}]},"DataSets":[{"Name":"company","Fields":[{"Name":"_meta.source","DataField":"_meta.source"},{"Name":"id","DataField":"id"},{"Name":"created_by","DataField":"created_by"},{"Name":"change_by","DataField":"change_by"},{"Name":"updated_by","DataField":"updated_by"},{"Name":"reviewed_by","DataField":"reviewed_by"},{"Name":"state","DataField":"state"},{"Name":"reviewed_on","DataField":"reviewed_on"},{"Name":"deleted","DataField":"deleted"},{"Name":"archived","DataField":"archived"},{"Name":"updated_on","DataField":"updated_on"},{"Name":"auto_name","DataField":"auto_name"},{"Name":"change_type","DataField":"change_type"},{"Name":"change_data","DataField":"change_data"},{"Name":"created_on","DataField":"created_on"},{"Name":"change_on","DataField":"change_on"},{"Name":"name","DataField":"name"},{"Name":"logo.filehash","DataField":"logo.filehash"},{"Name":"logo.filename","DataField":"logo.filename"},{"Name":"logo.filesize","DataField":"logo.filesize"},{"Name":"logo.mimetype","DataField":"logo.mimetype"},{"Name":"street","DataField":"street"},{"Name":"street2","DataField":"street2"},{"Name":"city","DataField":"city"},{"Name":"zip","DataField":"zip"},{"Name":"phone","DataField":"phone"},{"Name":"fax","DataField":"fax"},{"Name":"support","DataField":"support"},{"Name":"supmail","DataField":"supmail"},{"Name":"bi_refresh","DataField":"bi_refresh"},{"Name":"jwt_timeout","DataField":"jwt_timeout"},{"Name":"kb_refresh","DataField":"kb_refresh"},{"Name":"snap_refresh","DataField":"snap_refresh"},{"Name":"ui_lock","DataField":"ui_lock"},{"Name":"filter_wc","DataField":"filter_wc"},{"Name":"prevent_datetime_focus_popup","DataField":"prevent_datetime_focus_popup"},{"Name":"no_download","DataField":"no_download"},{"Name":"toast_form_level_errors","DataField":"toast_form_level_errors"},{"Name":"separate_numbers","DataField":"separate_numbers"},{"Name":"number_separator","DataField":"number_separator"},{"Name":"decimal_separator","DataField":"decimal_separator"},{"Name":"format_currency","DataField":"format_currency"},{"Name":"currency_prefix","DataField":"currency_prefix"},{"Name":"esign_title","DataField":"esign_title"},{"Name":"pdf_logo.filehash","DataField":"pdf_logo.filehash"},{"Name":"pdf_logo.filename","DataField":"pdf_logo.filename"},{"Name":"pdf_logo.filesize","DataField":"pdf_logo.filesize"},{"Name":"pdf_logo.mimetype","DataField":"pdf_logo.mimetype"},{"Name":"max_collections","DataField":"max_collections"},{"Name":"max_grid_columns","DataField":"max_grid_columns"},{"Name":"max_grid_rows","DataField":"max_grid_rows"},{"Name":"max_filter_rows","DataField":"max_filter_rows"},{"Name":"max_data_rows","DataField":"max_data_rows"},{"Name":"error_msg","DataField":"error_msg"},{"Name":"cal_default_view","DataField":"cal_default_view"},{"Name":"weekends","DataField":"weekends"},{"Name":"hidden_days","DataField":"hidden_days"},{"Name":"first_day","DataField":"first_day"},{"Name":"default_event_duration","DataField":"default_event_duration"},{"Name":"default_event_span","DataField":"default_event_span"},{"Name":"state_id","DataField":"state_id"},{"Name":"surescripts_id","DataField":"surescripts_id"},{"Name":"default_lot_expire_days","DataField":"default_lot_expire_days"},{"Name":"default_order_expire_days","DataField":"default_order_expire_days"},{"Name":"next_delivery_day","DataField":"next_delivery_day"},{"Name":"next_compound_days","DataField":"next_compound_days"},{"Name":"dose_start_days","DataField":"dose_start_days"},{"Name":"default_order_days","DataField":"default_order_days"},{"Name":"refill_reminder_days_out","DataField":"refill_reminder_days_out"},{"Name":"ncpdp_new_pref","DataField":"ncpdp_new_pref"},{"Name":"ncpdp_refill_pref","DataField":"ncpdp_refill_pref"},{"Name":"overwrite_manual_claim_items","DataField":"overwrite_manual_claim_items"},{"Name":"auto_test_claim","DataField":"auto_test_claim"},{"Name":"mm_ready_pref","DataField":"mm_ready_pref"},{"Name":"password_strength","DataField":"password_strength"},{"Name":"bg_color","DataField":"bg_color"},{"Name":"fg_color","DataField":"fg_color"},{"Name":"dt_inventory_check","DataField":"dt_inventory_check"},{"Name":"est_qty_inv_check","DataField":"est_qty_inv_check"},{"Name":"refill_days_out","DataField":"refill_days_out"},{"Name":"adjustment_threshold","DataField":"adjustment_threshold"},{"Name":"created_by_auto_name","DataField":"created_by_auto_name"},{"Name":"change_by_auto_name","DataField":"change_by_auto_name"},{"Name":"updated_by_auto_name","DataField":"updated_by_auto_name"},{"Name":"reviewed_by_auto_name","DataField":"reviewed_by_auto_name"},{"Name":"state_id_auto_name","DataField":"state_id_auto_name"}],"Query":{"DataSourceName":"apiform","CommandText":"uri=/company/1;jpath=$[0]"},"CaseSensitivity":"Auto","KanatypeSensitivity":"Auto","AccentSensitivity":"Auto","WidthSensitivity":"Auto"}]}','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "type" = EXCLUDED."type", "is_page_less" = EXCLUDED."is_page_less", "page_width" = EXCLUDED."page_width", "page_height" = EXCLUDED."page_height", "json_data" = EXCLUDED."json_data" WHERE form_template_report.allow_sync = 'Yes';
INSERT INTO form_template_report ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "type", "is_page_less", "page_width", "page_height", "json_data", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,9,NULL,NULL,NULL,'2024-09-01T15:22:20.000Z','Template For Chart (Embedded Dashboard)',NULL,NULL,'2024-05-08T16:13:23.000Z',NULL,'Template For Chart (Embedded Dashboard)','chart01a','Chart','Yes',NULL,NULL,'{"Name":"dashboard1","Width":"0in","DataElementName":"Dashboard","Layers":[{"Name":"default"}],"CustomProperties":[{"Name":"ViewerType","Value":"Dashboard"},{"Name":"DisplayType","Value":"Page"},{"Name":"SizeType","Value":"Default"},{"Name":"PaperOrientation","Value":"Portrait"},{"Name":"CollapseWhiteSpace","Value":"True"}],"Version":"7.6.0","Page":{"PageWidth":"8.5in","PageHeight":"11in","RightMargin":"0in","LeftMargin":"0in","TopMargin":"0in","BottomMargin":"0in","Columns":1,"ColumnSpacing":"0.5in","PaperOrientation":"Portrait"},"ReportSections":[{"Type":"Continuous","Name":"Section1","Page":{"PageWidth":"8.5in","PageHeight":"11in","RightMargin":"0in","LeftMargin":"0in","TopMargin":"0in","BottomMargin":"0in","Columns":1,"ColumnSpacing":"0in","PaperOrientation":"Portrait"},"Width":"8in","Body":{"Height":"6in","ReportItems":[{"Type":"textbox","Name":"TextBox1","CanGrow":true,"KeepTogether":true,"Value":"Template For Chart (Embedded Dashboard)","Style":{"PaddingLeft":"2pt","PaddingRight":"2pt","PaddingTop":"2pt","PaddingBottom":"2pt","TextAlign":"Center","VerticalAlign":"Middle","BackgroundColor":"#90cbf9"},"Width":"8in","Height":"0.375in"}]}}]}','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "type" = EXCLUDED."type", "is_page_less" = EXCLUDED."is_page_less", "page_width" = EXCLUDED."page_width", "page_height" = EXCLUDED."page_height", "json_data" = EXCLUDED."json_data" WHERE form_template_report.allow_sync = 'Yes';
INSERT INTO form_template_report ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "type", "is_page_less", "page_width", "page_height", "json_data", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,3,NULL,NULL,NULL,'2024-11-09T17:29:25.000Z','Template for Page (Repeating)',NULL,NULL,'2024-11-09T16:26:31.000Z',NULL,'Template for Page (Repeating)','page02a','Page Repeating','No',8.5,11,'{"Name":"multisectionreport1","Width":"0in","Layers":[{"Name":"default"}],"CustomProperties":[{"Name":"DisplayType","Value":"Page"},{"Name":"SizeType","Value":"Default"},{"Name":"CollapseWhiteSpace","Value":"True"}],"Version":"7.6.0","Page":{"PageWidth":"8.5in","PageHeight":"11in","RightMargin":"0in","LeftMargin":"0in","TopMargin":"0in","BottomMargin":"0in","Columns":1,"ColumnSpacing":"0.5in","PaperOrientation":"Portrait"},"ReportSections":[{"Type":"Continuous","Name":"ContinuousSection1","Page":{"PageWidth":"8.5in","PageHeight":"11in","RightMargin":"1in","LeftMargin":"1in","TopMargin":"1in","BottomMargin":"1in","Columns":1,"ColumnSpacing":"0in","PaperOrientation":"Portrait"},"Width":"2.3477in","Body":{"Height":"0.504in","ReportItems":[{"Type":"textbox","Name":"TextBox1","CanGrow":true,"KeepTogether":true,"Style":{"PaddingLeft":"2pt","PaddingRight":"2pt","PaddingTop":"2pt","PaddingBottom":"2pt"},"Left":"1.3477in","Top":"0.254in","Width":"1in","Height":"0.25in"}]}}]}','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "type" = EXCLUDED."type", "is_page_less" = EXCLUDED."is_page_less", "page_width" = EXCLUDED."page_width", "page_height" = EXCLUDED."page_height", "json_data" = EXCLUDED."json_data" WHERE form_template_report.allow_sync = 'Yes';
COMMIT;
