BEGIN;
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T18:37:19.000Z','WBC',NULL,NULL,'2015-11-20T18:37:19.000Z','2015-11-20T18:37:19.000Z','White Blood Cell Count','WBC','billion cells/L',3.5,10.5,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T18:39:38.000Z','RBC',NULL,NULL,'2015-11-20T18:39:38.000Z','2015-11-20T18:39:38.000Z','Red Blood Cell Count','RBC','trillion cells/L',3.9,5.72,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T22:09:42.000Z','Hgb',NULL,NULL,'2015-11-20T18:40:26.000Z','2015-11-20T18:40:26.000Z','Hemoglobin','Hgb','grams/dL',12,17.5,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T18:40:59.000Z','Hct',NULL,NULL,'2015-11-20T18:40:59.000Z','2015-11-20T18:40:59.000Z','Hematocrit','Hct','%',34.9,50,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T18:43:58.000Z','Platelet count',NULL,NULL,'2015-11-20T18:43:58.000Z','2015-11-20T18:43:58.000Z','Platelet count','Platelet count','billion/L',150,450,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T18:47:08.000Z','MCV',NULL,NULL,'2015-11-20T18:47:08.000Z','2015-11-20T18:47:08.000Z','Mean Corpuscular Volume','MCV','fL',81.2,98.3,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T18:48:27.000Z','Leukocytes',NULL,NULL,'2015-11-20T18:48:27.000Z','2015-11-20T18:48:27.000Z','Total Leukocytes','Leukocytes','10^9/L',4,11,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T18:48:49.000Z','Neutrophils',NULL,NULL,'2015-11-20T18:48:49.000Z','2015-11-20T18:48:49.000Z','Neutrophils','Neutrophils','10^9/L',2.5,7.5,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T18:49:03.000Z','Lymphocytes',NULL,NULL,'2015-11-20T18:49:03.000Z','2015-11-20T18:49:03.000Z','Lymphocytes','Lymphocytes','10^9/L',1.5,3.5,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T18:49:26.000Z','Monocytes',NULL,NULL,'2015-11-20T18:49:26.000Z','2015-11-20T18:49:26.000Z','Monocytes','Monocytes','10^9/L',0.2,0.8,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T18:50:00.000Z','Eosinophils',NULL,NULL,'2015-11-20T18:50:00.000Z','2015-11-20T18:50:00.000Z','Eosinophils','Eosinophils','10^9/L',0.04,0.4,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T18:50:16.000Z','Basophils',NULL,NULL,'2015-11-20T18:50:16.000Z','2015-11-20T18:50:16.000Z','Basophils','Basophils','10^9/L',0.01,0.1,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T18:52:05.000Z','MCH',NULL,NULL,'2015-11-20T18:52:05.000Z','2015-11-20T18:52:05.000Z','Mean Corpuscular Hemoglobin','MCH','pg/cell',27,33,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T18:52:22.000Z','MCHC',NULL,NULL,'2015-11-20T18:52:22.000Z','2015-11-20T18:52:22.000Z','Mean Corpuscular Hemoglobin Concentration','MCHC','g/dL',33,36,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T18:54:00.000Z','RDW',NULL,NULL,'2015-11-20T18:54:00.000Z','2015-11-20T18:54:00.000Z','Red Cell Distribution Width','RDW','%',10.2,14.5,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (16,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T18:54:47.000Z','MPV',NULL,NULL,'2015-11-20T18:54:47.000Z','2015-11-20T18:54:47.000Z','Mean Platelet Volume','MPV','fL',7.5,11.5,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (17,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:25:17.000Z','BUN',NULL,NULL,'2015-11-20T21:25:17.000Z','2015-11-20T21:25:17.000Z','Blood urea nitrogen','BUN','mg/dL',7,20,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (18,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:29:04.000Z','Cr',NULL,NULL,'2015-11-20T21:29:04.000Z','2015-11-20T21:29:04.000Z','Creatinine','Cr','mg/dL',0.6,1.2,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (19,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:30:49.000Z','Na',NULL,NULL,'2015-11-20T21:30:49.000Z','2015-11-20T21:30:49.000Z','Sodium','Na','mEq/L',135,145,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (20,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:33:23.000Z','Potassium',NULL,NULL,'2015-11-20T21:33:23.000Z','2015-11-20T21:33:23.000Z','Potassium','Potassium','mEq/L',3.5,5,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (21,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:35:12.000Z','Magnesium',NULL,NULL,'2015-11-20T21:35:12.000Z','2015-11-20T21:35:12.000Z','Magnesium','Magnesium','mg/dL',1.7,2.2,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (22,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:36:54.000Z','Phosphorus',NULL,NULL,'2015-11-20T21:36:54.000Z','2015-11-20T21:36:54.000Z','Phosphorus','Phosphorus','mg/dL',2.4,4.1,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (23,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:39:26.000Z','Calcium',NULL,NULL,'2015-11-20T21:39:26.000Z','2015-11-20T21:39:26.000Z','Calcium','Calcium','mg/dL',8.5,10.2,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (24,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:40:58.000Z','Chloride',NULL,NULL,'2015-11-20T21:40:58.000Z','2015-11-20T21:40:58.000Z','Chloride','Chloride','mEq/L',96,106,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (25,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:42:26.000Z','CO2',NULL,NULL,'2015-11-20T21:42:26.000Z','2015-11-20T21:42:26.000Z','Carbon Dioxide','CO2','mEq/L',23,29,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (26,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:44:36.000Z','ALB',NULL,NULL,'2015-11-20T21:44:36.000Z','2015-11-20T21:44:36.000Z','Albumin','ALB','g/dL',3.5,5.5,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (27,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:45:38.000Z','PreAlbumin',NULL,NULL,'2015-11-20T21:45:38.000Z','2015-11-20T21:45:38.000Z','PreAlbumin','PreAlbumin','mg/dL',19,38,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (28,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:48:21.000Z','Glucose',NULL,NULL,'2015-11-20T21:48:21.000Z','2015-11-20T21:48:21.000Z','Glucose','Glucose','mg/dL',70,100,NULL,126,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (29,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:50:48.000Z','Triglycerides',NULL,NULL,'2015-11-20T21:50:48.000Z','2015-11-20T21:50:48.000Z','Triglycerides','Triglycerides','mg/dL',0,200,NULL,500,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (30,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:52:20.000Z','ALP',NULL,NULL,'2015-11-20T21:52:20.000Z','2015-11-20T21:52:20.000Z','Alkaline phosphatase','ALP','IU/L',44,147,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (31,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:54:38.000Z','AST',NULL,NULL,'2015-11-20T21:54:38.000Z','2015-11-20T21:54:38.000Z','Aspartate Aminotransferase','AST','units/L',10,40,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (32,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:55:35.000Z','ALT',NULL,NULL,'2015-11-20T21:55:35.000Z','2015-11-20T21:55:35.000Z','Alanine Aminotransferase','ALT','units/L',7,56,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (33,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:57:46.000Z','TBIL',NULL,NULL,'2015-11-20T21:57:46.000Z','2015-11-20T21:57:46.000Z','Bilirubin','TBIL','mg/dL',0.1,0.4,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (34,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T21:59:56.000Z','DBIL',NULL,NULL,'2015-11-20T21:59:56.000Z','2015-11-20T21:59:56.000Z','Direct Bilirubin','DBIL','mg/dL',0.2,1.2,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "short_name", "unit", "norm_low", "norm_high", "severe_low", "severe_high", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (35,1,NULL,1,NULL,NULL,NULL,NULL,'2015-11-20T22:02:02.000Z','Total Protein',NULL,NULL,'2015-11-20T22:02:02.000Z','2015-11-20T22:02:02.000Z','Total Protein','Total Protein','g/dL',6.4,8.3,NULL,NULL,'Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "unit" = EXCLUDED."unit", "norm_low" = EXCLUDED."norm_low", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "severe_high" = EXCLUDED."severe_high", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
COMMIT;
