BEGIN;
SET session_replication_role = replica;

-- Clear the table first:
DELETE FROM form_list_wf_event;

INSERT INTO form_list_wf_event ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active", "sys_period", "external_id") VALUES
(1, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Req. Auth.', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Req. Auth.', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '246'),
(2, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Auth Rcvd', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Auth Rcvd', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '247'),
(3, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INACTIVE INV', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INACTIVE INV', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '257'),
(4, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'RX EXP', NULL, NULL, '2025-02-14 00:00:00', NULL, 'RX EXP', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '258'),
(5, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'ZERO REFILLS', NULL, NULL, '2025-02-14 00:00:00', NULL, 'ZERO REFILLS', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '259'),
(6, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'RX DENIED', NULL, NULL, '2025-02-14 00:00:00', NULL, 'RX DENIED', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '260'),
(7, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'VER INCOMPLETE', NULL, NULL, '2025-02-14 00:00:00', NULL, 'VER INCOMPLETE', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '389'),
(8, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Claim Change', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Claim Change', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '390'),
(9, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - Info Needed', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - Info Needed', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '669'),
(10, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'IPA - MD Re-Authorization Required', NULL, NULL, '2025-02-14 00:00:00', NULL, 'IPA - MD Re-Authorization Required', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '670'),
(11, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'IPA - Re-Authorization Submitted', NULL, NULL, '2025-02-14 00:00:00', NULL, 'IPA - Re-Authorization Submitted', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '671'),
(12, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - PA 1st F/U', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - PA 1st F/U', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '672'),
(13, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - PA 2nd F/U', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - PA 2nd F/U', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '673'),
(14, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - Left Message 1', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - Left Message 1', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '680'),
(15, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - Left Message 2', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - Left Message 2', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '681'),
(16, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - Pt will call back', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - Pt will call back', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '682'),
(17, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'COPAY - Funding Submitted', NULL, NULL, '2025-02-14 00:00:00', NULL, 'COPAY - Funding Submitted', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '684'),
(18, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'COPAY - Pending completed Forms', NULL, NULL, '2025-02-14 00:00:00', NULL, 'COPAY - Pending completed Forms', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '685'),
(19, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'COPAY - Status Check 1', NULL, NULL, '2025-02-14 00:00:00', NULL, 'COPAY - Status Check 1', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '686'),
(20, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'COPAY - Status Check 2', NULL, NULL, '2025-02-14 00:00:00', NULL, 'COPAY - Status Check 2', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '687'),
(21, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - PA Required', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - PA Required', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '776'),
(22, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - PA Appeal Submitted', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - PA Appeal Submitted', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '780'),
(23, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - PA Appeal Required', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - PA Appeal Required', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '781'),
(24, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - 1st Refill Request Submitted', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - 1st Refill Request Submitted', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '801'),
(25, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'COPAY - Funding Required', NULL, NULL, '2025-02-14 00:00:00', NULL, 'COPAY - Funding Required', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '820'),
(26, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - Left Message 3 and Call MDO', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - Left Message 3 and Call MDO', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '821'),
(27, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - 2nd Refill Request Submitted', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - 2nd Refill Request Submitted', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '822'),
(28, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - 3rd Refill Request Submitted and Contact MDO', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - 3rd Refill Request Submitted and Contact MDO', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '823'),
(29, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'COPAY - Patient Refused Medication', NULL, NULL, '2025-02-14 00:00:00', NULL, 'COPAY - Patient Refused Medication', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '827'),
(30, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - Refill Too Soon', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - Refill Too Soon', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '829'),
(31, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Nursing - Coordination Required', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Nursing - Coordination Required', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '830'),
(32, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Nursing - Nursing Agency Pending', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Nursing - Nursing Agency Pending', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '833'),
(33, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - PA Approved', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - PA Approved', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '835'),
(34, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - PA 3rd F/U and TM Notification', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - PA 3rd F/U and TM Notification', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '838'),
(35, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '842'),
(36, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - 1st Request Fax-Missing Rx', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - 1st Request Fax-Missing Rx', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '844'),
(37, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Auth Correction Needed', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Auth Correction Needed', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '846'),
(38, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - 2nd Call- Missing Rx', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - 2nd Call- Missing Rx', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '847'),
(39, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - PA Submitted', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - PA Submitted', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '850'),
(40, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'IPA - Med D PA Denied / Submitted to IPA', NULL, NULL, '2025-02-14 00:00:00', NULL, 'IPA - Med D PA Denied / Submitted to IPA', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '851'),
(41, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Schedule Call Pending', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Schedule Call Pending', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '856'),
(42, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - 1st Fax Request-Missing Rx & Clinicals', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - 1st Fax Request-Missing Rx & Clinicals', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '858'),
(43, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Missing Clinicals', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Missing Clinicals', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '859'),
(44, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Ready to Process', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Ready to Process', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '861'),
(45, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'RPh - Pending Rx Clarification', NULL, NULL, '2025-02-14 00:00:00', NULL, 'RPh - Pending Rx Clarification', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '863'),
(46, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'RPh - Pending Clinical Clarification', NULL, NULL, '2025-02-14 00:00:00', NULL, 'RPh - Pending Clinical Clarification', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '864'),
(47, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - Forced to Competitor', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - Forced to Competitor', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '865'),
(48, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - 3rd Call- Missing Rx', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - 3rd Call- Missing Rx', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '866'),
(49, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Med Rec - Docs Requested Call 1', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Med Rec - Docs Requested Call 1', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '867'),
(50, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Med Rec - Docs Requested Call 2', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Med Rec - Docs Requested Call 2', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '868'),
(51, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Med Rec - Docs Requested Call 3', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Med Rec - Docs Requested Call 3', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '869'),
(52, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Med Rec - Documents Received', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Med Rec - Documents Received', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '870'),
(53, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Med Rec - Pending RPH Descision', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Med Rec - Pending RPH Descision', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '871'),
(54, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Auth Correction Submitted', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Auth Correction Submitted', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '872'),
(55, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Auth Correction Received', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Auth Correction Received', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '873'),
(56, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Onc Inst Schedule Call Pending', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Onc Inst Schedule Call Pending', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '874'),
(57, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - 2nd Request Call-Missing Rx & Clinicals', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - 2nd Request Call-Missing Rx & Clinicals', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '875'),
(58, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - 3rd Request Call-Missing Rx & Clinicals', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - 3rd Request Call-Missing Rx & Clinicals', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '876'),
(59, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Missing Rx & Clinicals', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Missing Rx & Clinicals', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '877'),
(60, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Missing Rx', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Missing Rx', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '878'),
(61, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Directions', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Directions', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '879'),
(62, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Quantity', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Quantity', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '880'),
(63, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Refill Amount', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Refill Amount', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '881'),
(64, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Drug', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Drug', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '882'),
(65, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Prescriber', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Prescriber', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '883'),
(66, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Demographic Info', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Demographic Info', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '884'),
(67, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Days Supply', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Days Supply', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '885'),
(68, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Date Written', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Date Written', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '886'),
(69, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Nursing - Patient Contact Pending', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Nursing - Patient Contact Pending', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '887'),
(70, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Biosimilar Change Requested', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Biosimilar Change Requested', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '888'),
(71, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC- Order on Hold', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC- Order on Hold', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '889'),
(72, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Rx - Pending RPh Verification', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Rx - Pending RPh Verification', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '890'),
(73, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - Call MDO', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - Call MDO', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '892'),
(74, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Refill RX', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Refill RX', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '893'),
(75, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Missing Maintenance Rx', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Missing Maintenance Rx', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '895'),
(76, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'HSP - PA Required', NULL, NULL, '2025-02-14 00:00:00', NULL, 'HSP - PA Required', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '898'),
(77, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'New Hope Schedule Call Pending', NULL, NULL, '2025-02-14 00:00:00', NULL, 'New Hope Schedule Call Pending', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '926'),
(78, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Missing Insurance', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Missing Insurance', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '936'),
(79, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - MDO Will Call Back', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - MDO Will Call Back', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '1003'),
(80, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'MD- HOLD For Toxicity', NULL, NULL, '2025-02-14 00:00:00', NULL, 'MD- HOLD For Toxicity', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '1059'),
(20, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - Left Message 1', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - Left Message 1', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '680'),
(21, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - Left Message 2', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - Left Message 2', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '681'),
(22, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - Pt will call back', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - Pt will call back', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '682'),
(24, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'COPAY - Funding Submitted', NULL, NULL, '2025-02-14 00:00:00', NULL, 'COPAY - Funding Submitted', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '684'),
(25, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'COPAY - Pending completed Forms', NULL, NULL, '2025-02-14 00:00:00', NULL, 'COPAY - Pending completed Forms', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '685'),
(26, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'COPAY - Status Check 1', NULL, NULL, '2025-02-14 00:00:00', NULL, 'COPAY - Status Check 1', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '686'),
(27, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'COPAY - Status Check 2', NULL, NULL, '2025-02-14 00:00:00', NULL, 'COPAY - Status Check 2', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '687'),
(28, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - PA Required', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - PA Required', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '776'),
(30, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - PA Appeal Submitted', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - PA Appeal Submitted', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '780'),
(31, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - PA Appeal Required', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - PA Appeal Required', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '781'),
(34, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - 1st Refill Request Submitted', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - 1st Refill Request Submitted', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '801'),
(36, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'COPAY - Funding Required', NULL, NULL, '2025-02-14 00:00:00', NULL, 'COPAY - Funding Required', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '820'),
(37, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - Left Message 3 and Call MDO', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - Left Message 3 and Call MDO', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '821'),
(38, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - 2nd Refill Request Submitted', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - 2nd Refill Request Submitted', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '822'),
(39, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - 3rd Refill Request Submitted and Contact MDO', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - 3rd Refill Request Submitted and Contact MDO', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '823'),
(40, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'COPAY - Patient Refused Medication', NULL, NULL, '2025-02-14 00:00:00', NULL, 'COPAY - Patient Refused Medication', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '827'),
(41, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - Refill Too Soon', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - Refill Too Soon', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '829'),
(42, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Nursing - Coordination Required', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Nursing - Coordination Required', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '830'),
(45, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Nursing - Nursing Agency Pending', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Nursing - Nursing Agency Pending', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '833'),
(47, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - PA Approved', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - PA Approved', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '835'),
(49, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - PA 3rd F/U and TM Notification', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - PA 3rd F/U and TM Notification', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '838'),
(50, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '842'),
(52, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - 1st Request Fax-Missing Rx', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - 1st Request Fax-Missing Rx', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '844'),
(54, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Auth Correction Needed', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Auth Correction Needed', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '846'),
(55, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - 2nd Call- Missing Rx', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - 2nd Call- Missing Rx', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '847'),
(58, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - PA Submitted', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - PA Submitted', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '850'),
(59, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'IPA - Med D PA Denied / Submitted to IPA', NULL, NULL, '2025-02-14 00:00:00', NULL, 'IPA - Med D PA Denied / Submitted to IPA', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '851'),
(64, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Schedule Call Pending', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Schedule Call Pending', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '856'),
(66, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - 1st Fax Request-Missing Rx & Clinicals', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - 1st Fax Request-Missing Rx & Clinicals', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '858'),
(67, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Missing Clinicals', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Missing Clinicals', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '859'),
(69, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Ready to Process', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Ready to Process', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '861'),
(71, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'RPh - Pending Rx Clarification', NULL, NULL, '2025-02-14 00:00:00', NULL, 'RPh - Pending Rx Clarification', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '863'),
(72, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'RPh - Pending Clinical Clarification', NULL, NULL, '2025-02-14 00:00:00', NULL, 'RPh - Pending Clinical Clarification', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '864'),
(73, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'INS - Forced to Competitor', NULL, NULL, '2025-02-14 00:00:00', NULL, 'INS - Forced to Competitor', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '865'),
(74, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - 3rd Call- Missing Rx', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - 3rd Call- Missing Rx', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '866'),
(75, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Med Rec - Docs Requested Call 1', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Med Rec - Docs Requested Call 1', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '867'),
(76, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Med Rec - Docs Requested Call 2', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Med Rec - Docs Requested Call 2', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '868'),
(77, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Med Rec - Docs Requested Call 3', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Med Rec - Docs Requested Call 3', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '869'),
(78, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Med Rec - Documents Received', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Med Rec - Documents Received', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '870'),
(79, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Med Rec - Pending RPH Descision', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Med Rec - Pending RPH Descision', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '871'),
(80, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Auth Correction Submitted', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Auth Correction Submitted', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '872'),
(81, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Auth Correction Received', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Auth Correction Received', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '873'),
(82, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Onc Inst Schedule Call Pending', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Onc Inst Schedule Call Pending', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '874'),
(83, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - 2nd Request Call-Missing Rx & Clinicals', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - 2nd Request Call-Missing Rx & Clinicals', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '875'),
(84, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - 3rd Request Call-Missing Rx & Clinicals', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - 3rd Request Call-Missing Rx & Clinicals', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '876'),
(85, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Missing Rx & Clinicals', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Missing Rx & Clinicals', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '877'),
(86, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Missing Rx', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Missing Rx', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '878'),
(87, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Directions', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Directions', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '879'),
(88, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Quantity', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Quantity', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '880'),
(89, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Refill Amount', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Refill Amount', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '881'),
(90, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Drug', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Drug', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '882'),
(91, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Prescriber', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Prescriber', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '883'),
(92, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Demographic Info', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Demographic Info', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '884'),
(93, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Days Supply', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Days Supply', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '885'),
(94, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Incorrect - Date Written', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Incorrect - Date Written', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '886'),
(95, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Nursing - Patient Contact Pending', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Nursing - Patient Contact Pending', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '887'),
(96, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Biosimilar Change Requested', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Biosimilar Change Requested', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '888'),
(97, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC- Order on Hold', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC- Order on Hold', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '889'),
(98, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Rx - Pending RPh Verification', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Rx - Pending RPh Verification', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '890'),
(100, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - Call MDO', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - Call MDO', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '892'),
(101, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Refill RX', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Refill RX', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '893'),
(103, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Missing Maintenance Rx', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Missing Maintenance Rx', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '895'),
(106, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'HSP - PA Required', NULL, NULL, '2025-02-14 00:00:00', NULL, 'HSP - PA Required', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '898'),
(108, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'New Hope Schedule Call Pending', NULL, NULL, '2025-02-14 00:00:00', NULL, 'New Hope Schedule Call Pending', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '926'),
(109, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Referral Received - Missing Insurance', NULL, NULL, '2025-02-14 00:00:00', NULL, 'Referral Received - Missing Insurance', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '936'),
(110, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PCC - MDO Will Call Back', NULL, NULL, '2025-02-14 00:00:00', NULL, 'PCC - MDO Will Call Back', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '1003'),
(111, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'MD- HOLD For Toxicity', NULL, NULL, '2025-02-14 00:00:00', NULL, 'MD- HOLD For Toxicity', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '1059'),
(112, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'CMPD - Final Doses Delivered', NULL, NULL, '2025-02-14 00:00:00', NULL, 'CMPD - Final Doses Delivered', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '1060'),
(113, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'CMPD - Pending AIC Schedule', NULL, NULL, '2025-02-14 00:00:00', NULL, 'CMPD - Pending AIC Schedule', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '1061'),
(114, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'CMPD- RPH Clinical Review Needed', NULL, NULL, '2025-02-14 00:00:00', NULL, 'CMPD- RPH Clinical Review Needed', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '1073'),
(115, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'CMPD - Completed; Ready to fill', NULL, NULL, '2025-02-14 00:00:00', NULL, 'CMPD - Completed; Ready to fill', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '1074'),
(116, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'CMPD - HOLD - PT Hospitalized', NULL, NULL, '2025-02-14 00:00:00', NULL, 'CMPD - HOLD - PT Hospitalized', NULL, 'Yes', 'Yes', '["2025-02-14 18:46:18.194444+00",)', '1075');

-- Bump the sequence so subsequent inserts pick up after the highest ID:
ALTER SEQUENCE "form_list_wf_event_seq" RESTART WITH 117;

SET session_replication_role = DEFAULT;
COMMIT;
