BEGIN;
INSERT INTO form_list_writeoff_reason ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "bad_debt", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T20:29:49.000Z','Hardship Waiver',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Hardship','Hardship Waiver','Yes','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "bad_debt" = EXCLUDED."bad_debt" WHERE form_list_writeoff_reason.allow_sync = 'Yes';
INSERT INTO form_list_writeoff_reason ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "bad_debt", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T20:30:10.000Z','Bankruptcy',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Bankruptcy','Bankruptcy','Yes','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "bad_debt" = EXCLUDED."bad_debt" WHERE form_list_writeoff_reason.allow_sync = 'Yes';
INSERT INTO form_list_writeoff_reason ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "bad_debt", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T20:31:32.000Z','Damaged Product',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'DamagedProduct','Damaged Product',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "bad_debt" = EXCLUDED."bad_debt" WHERE form_list_writeoff_reason.allow_sync = 'Yes';
INSERT INTO form_list_writeoff_reason ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "bad_debt", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T20:31:17.000Z','Payer Non-responsive',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Nonresponsive','Payer Non-responsive','Yes','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "bad_debt" = EXCLUDED."bad_debt" WHERE form_list_writeoff_reason.allow_sync = 'Yes';
INSERT INTO form_list_writeoff_reason ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "bad_debt", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T20:31:02.000Z','Uncollectible',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Uncollectible','Uncollectible','Yes','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "bad_debt" = EXCLUDED."bad_debt" WHERE form_list_writeoff_reason.allow_sync = 'Yes';
INSERT INTO form_list_writeoff_reason ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "bad_debt", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T20:30:26.000Z','Disputed Claim',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'DisputedClaim','Disputed Claim','Yes','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "bad_debt" = EXCLUDED."bad_debt" WHERE form_list_writeoff_reason.allow_sync = 'Yes';
INSERT INTO form_list_writeoff_reason ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "bad_debt", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T20:30:26.000Z','Fraud',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Fraud','Fraud','Yes','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "bad_debt" = EXCLUDED."bad_debt" WHERE form_list_writeoff_reason.allow_sync = 'Yes';
INSERT INTO form_list_writeoff_reason ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "bad_debt", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T20:30:26.000Z','Billing Error',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Error','Billing Error',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "bad_debt" = EXCLUDED."bad_debt" WHERE form_list_writeoff_reason.allow_sync = 'Yes';
COMMIT;
