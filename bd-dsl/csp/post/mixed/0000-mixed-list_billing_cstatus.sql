BEGIN;
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'1 - Pending Delivery Ticket Confirmation',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'1','Pending Delivery Ticket Confirmation',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2 - Pending Delivery Work Ticket Verification',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'2','Pending Delivery Work Ticket Verification',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'3 - Ready to Bill',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'3','Ready to Bill',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'4 - Ready to Transmit',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'4','Ready to Transmit',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'5 - Sent',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'5','Sent',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'6 - Rejected',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'6','Rejected',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'7 - Payable',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'7','Payable',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'8 - Captured',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'8','Captured',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'9 - Accepted',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'9','Accepted',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'10 - Billed',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'10','Billed',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'11 - Denied',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'11','Denied',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'12 - On-Hold',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'12','On-Hold',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'13 - On-Hold',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'13','On-Hold',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'14 - Time Out',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'14','Time Out',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'15 - Pending Delivery Confirmation',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'15','Pending Delivery Confirmation',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (16,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'16 - Adjudicated',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'16','Adjudicated',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_cstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (17,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'17 - Voided',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'17','Voided',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_cstatus.allow_sync = 'Yes';
COMMIT;
