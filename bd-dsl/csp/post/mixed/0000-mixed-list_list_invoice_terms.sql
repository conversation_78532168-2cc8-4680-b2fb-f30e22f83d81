BEGIN;
SET session_replication_role = replica;

-- Clear the table first:
DELETE FROM form_list_invoice_terms;

-- Insert your new therapies:
INSERT INTO form_list_invoice_terms ("name","id","created_on","created_by","auto_name") VALUES
('Due net 7 days.',1,'2025-02-14 00:00:00',1,'Due net 7 days.'),
('Due net 10 days.',2,'2025-02-14 00:00:00',1,'Due net 10 days.'),
('Due net 15 days.',3,'2025-02-14 00:00:00',1,'Due net 15 days.'),
('Due net 30 days.',4,'2025-02-14 00:00:00',1,'Due net 30 days.'),
('Due net 45 days.',5,'2025-02-14 00:00:00',1,'Due net 45 days.'),
('Due net 60 days.',6,'2025-02-14 00:00:00',1,'Due net 60 days.'),
('Due net 90 days.',7,'2025-02-14 00:00:00',1,'Due net 90 days.'),
('Payable on receipt.',8,'2025-02-14 00:00:00',1,'Payable on receipt.');

-- Bump the sequence so subsequent inserts pick up after the highest ID:
ALTER SEQUENCE "form_list_invoice_terms_id_seq" RESTART WITH 9;

SET session_replication_role = DEFAULT;
COMMIT;
