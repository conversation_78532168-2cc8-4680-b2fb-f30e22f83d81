BEGIN;
INSERT INTO form_list_dx_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "description", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Asthma asthma',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'asthma','Asthma',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "description" = EXCLUDED."description" WHERE form_list_dx_asmt.allow_sync = 'Yes';
INSERT INTO form_list_dx_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "description", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','HIV hiv',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'hiv','HIV',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "description" = EXCLUDED."description" WHERE form_list_dx_asmt.allow_sync = 'Yes';
INSERT INTO form_list_dx_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "description", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Hepatitis C hepc',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'hepc','Hepatitis C',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "description" = EXCLUDED."description" WHERE form_list_dx_asmt.allow_sync = 'Yes';
INSERT INTO form_list_dx_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "description", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Hepatitis B hepb',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'hepb','Hepatitis B',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "description" = EXCLUDED."description" WHERE form_list_dx_asmt.allow_sync = 'Yes';
INSERT INTO form_list_dx_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "description", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Hemophilia A hemoa',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'hemoa','Hemophilia A',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "description" = EXCLUDED."description" WHERE form_list_dx_asmt.allow_sync = 'Yes';
INSERT INTO form_list_dx_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "description", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Rheumatoid Arthritis ra',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ra','Rheumatoid Arthritis',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "description" = EXCLUDED."description" WHERE form_list_dx_asmt.allow_sync = 'Yes';
INSERT INTO form_list_dx_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "description", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Psoriasis psoriasis',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'psoriasis','Psoriasis',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "description" = EXCLUDED."description" WHERE form_list_dx_asmt.allow_sync = 'Yes';
INSERT INTO form_list_dx_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "description", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Multiple Sclerosis ms',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ms','Multiple Sclerosis',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "description" = EXCLUDED."description" WHERE form_list_dx_asmt.allow_sync = 'Yes';
INSERT INTO form_list_dx_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "description", "allow_sync", "active") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Von Willebrand Disease vwd',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vwd','Von Willebrand Disease',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "description" = EXCLUDED."description" WHERE form_list_dx_asmt.allow_sync = 'Yes';
COMMIT;
