BEGIN;
INSERT INTO form_list_cmpd_template ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "compounding_instructions", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'CefTRIAXone 1 gm/50 mL Elastomeric',NULL,NULL,'2024-02-15T18:21:05.000Z',NULL,'CefTRIAXone 1 gm/50 mL Elastomeric','CefTRIAXone 1 gm/50 mL Elastomeric

1. Select appropriate ingredients, vial size or volume, strength, lot, and quantity of drug, diluent, and bags to compound this Work Order. See Drugs/Supplies Used section of Work Order. 
2. Pharmacist to review for correct ingredients, strength, volume, quantity along with quality, color, and integrity of vials and expiration date as part of pre-check and sign Work Order before transferring to clean suite.
3. All medications, supplies, and totes are wiped with sterile IPA prior to entering clean suite. 
4. All medications, supplies, and totes are wiped with sterile IPA prior to entering ISO 7 Buffer area of clean suite. 
5. All medications and supplies are wiped with sterile IPA before entering ISO 5 PEC.
6. Aseptically perform all manipulations within the PEC, including reconstitution and/or transfer of drug as instructed on this Work Order. For reconstituted products, check that all powder is in solution before any transfers occur.
7. Licensed, Registered, or otherwise authorized personnel labels CSP(s). 
8. Final product is checked by Pharmacist during post-check, including leak test for closure integrity including any coring of stoppers or leaks, verifying correct medications and volumes are used, product checked against light/dark background for presence of particulate matter or precipitates, correct color, consistency, and volume and Work Order signed. 
9. Post-checked final product is bagged and final label affixed.
10. Final product is stored in the refrigerator awaiting delivery to the patient.

Reconstitute CefTRIAXone 1 gm Vial with 9.6 mL SWFI. Reconstitute CefTRIAXone 2 gm Vial with 19.2 mL SWFI. Reconstitute CefTRIAXone 10 gm Vial with 95 mL SWFI. Yields 100 mg/mL (1 gm/10 mL). 
Add 80 mL NS to each 100 mL Elastomeric. Add 20 mL (2 gm) reconstituted CefTRIAXone solution to each 100 mL NS Elastomeric for total volume of 100 mL.

Wasted/Discarded Amount: ________________    

Homepump Eclipse at final concentration 10 mg/mL, 20 mg/mL, 40 mg/mL: Beyond-Use Dating of 9 days REFRIGERATED is based on USP <797>. 

','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "compounding_instructions" = EXCLUDED."compounding_instructions" WHERE form_list_cmpd_template.allow_sync = 'Yes';
INSERT INTO form_list_cmpd_template ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "compounding_instructions", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-13T13:30:34.000Z','Vancomycin 1gm/50ml',NULL,NULL,'2024-02-15T18:23:39.000Z',NULL,'Vancomycin 1gm/50ml','Vancomycin 1gm/50ml

1. Select appropriate ingredients, vial size or volume, strength, lot, and quantity of drug, diluent, and bags to compound this Work Order. See Drugs/Supplies Used section of Work Order. 
2. Pharmacist to review for correct ingredients, strength, volume, quantity along with quality, color, and integrity of vials and expiration date as part of pre-check and sign Work Order before transferring to clean suite.
3. All medications, supplies, and totes are wiped with sterile IPA prior to entering clean suite. 
4. All medications, supplies, and totes are wiped with sterile IPA prior to entering ISO 7 Buffer area of clean suite. 
5. All medications and supplies are wiped with sterile IPA before entering ISO 5 PEC.
6. Aseptically perform all manipulations within the PEC, including reconstitution and/or transfer of drug as instructed on this Work Order. For reconstituted products, check that all powder is in solution before any transfers occur.
7. Licensed, Registered, or otherwise authorized personnel labels CSP(s). 
8. Final product is checked by Pharmacist during post-check, including leak test for closure integrity including any coring of stoppers or leaks, verifying correct medications and volumes are used, product checked against light/dark background for presence of particulate matter or precipitates, correct color, consistency, and volume and Work Order signed. 
9. post-checked final product is bagged and final label affixed.

10. Final product is stored in the refrigerator awaiting delivery to the patient.

Reconstitute Vancomycin 1gm vial with 10ml SWFI. Reconstitute Vancomycin 5gm vial with 50ml SWFI. Reconstitute Vancomycin 10gm vial with 95ml SWFI. Yields 100mg/ml. 
Add ___ ml (CHOOSE 1) NS or D5W to each __ mL __ mL/hr elastomeric. QS with __ ml ( __ gm) Vancomycin solution for final volume of __ml. 
NOTE: Maximum infusion rate = 1 gm/hr. 

Wasted/Discarded Amount: ________________    

Homepump Eclipse at final concentration 5 mg/mL in D5W: Beyond-Use Dating of 10 days REFRIGERATED is based on USP <797>.
Homepump Eclipse at final concentration 5 mg/mL, 10mg/ml, 15 mg/mL in NS: Beyond-Use Dating of 10 days REFRIGERATED is based on USP <797>.
SmartEZ at final concentration of 5 mg/mL, 15 mg/mL in NS: Beyond-Use Dating of 10 days REFRIGERATED is based on USP <797>.
EasyPump II at final concentration of 5 mg/mL, 15 mg/mL in NS: Beyond-Use Dating of 10 days REFRIGERATED is based on USP <797>. 
','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "compounding_instructions" = EXCLUDED."compounding_instructions" WHERE form_list_cmpd_template.allow_sync = 'Yes';
INSERT INTO form_list_cmpd_template ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "compounding_instructions", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-13T13:30:46.000Z','Rifampin 600mg/500mL NS',NULL,NULL,'2024-02-15T18:23:53.000Z',NULL,'Rifampin 600mg/500mL NS','Rifampin 600mg/500mL NS

1. Select appropriate ingredients, vial size or volume, strength, lot, and quantity of drug, diluent, and bags to compound this Work Order. See Drugs/Supplies Used section of Work Order. 
2. Pharmacist to review for correct ingredients, strength, volume, quantity along with quality, color, and integrity of vials and expiration date as part of pre-check and sign Work Order before transferring to clean suite.
3. All medications, supplies, and totes are wiped with sterile IPA prior to entering clean suite. 
4. All medications, supplies, and totes are wiped with sterile IPA prior to entering ISO 7 Buffer area of clean suite. 
5. All medications and supplies are wiped with sterile IPA before entering ISO 5 PEC.
6. Aseptically perform all manipulations within the PEC, including reconstitution and/or transfer of drug as instructed on this Work Order. For reconstituted products, check that all powder is in solution before any transfers occur.
7. Licensed, Registered, or otherwise authorized personnel labels CSP(s). 
8. Final product is checked by Pharmacist during post-check, including leak test for closure integrity including any coring of stoppers or leaks, verifying correct medications and volumes are used, product checked against light/dark background for presence of particulate matter or precipitates, correct color, consistency, and volume and Work Order signed. 
9. post-checked final product is bagged and final label affixed.
10. Final product is stored in the refrigerator awaiting delivery to the patient.

Reconstitute each 600mg vial with 10mL SWFI. Yields 60 mg/mL. Fill 500 mL elastomeric with 490mL NS. QS with 10 mL ( 600 mg) rifampin for final volume of 500 mL.

Wasted/Discarded Amount: ________________    

SmartEZ at final concentration 0.5 mg/mL and 3 mg/mL: Beyond-Use Dating of 6 days REFRIGERATED is based on stability information from from SmartEZ manufacturer data dated 05/2021, which is shorter than USP <797> permitted sterility dating.  

','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "compounding_instructions" = EXCLUDED."compounding_instructions" WHERE form_list_cmpd_template.allow_sync = 'Yes';
INSERT INTO form_list_cmpd_template ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "compounding_instructions", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-13T13:30:46.000Z','Milrinone',NULL,NULL,'2024-02-15T18:23:53.000Z',NULL,'Milrinone','Milrinone 

1. Select appropriate ingredients, vial size or volume, strength, lot, and quantity of drug, diluent, and bags to compound this Work Order. See Drugs/Supplies Used section of Work Order. 
2. Pharmacist to review for correct ingredients, strength, volume, quantity along with quality, color, and integrity of vials and expiration date as part of pre-check and sign Work Order before transferring to clean suite.
3. All medications, supplies, and totes are wiped with sterile IPA prior to entering clean suite. 
4. All medications, supplies, and totes are wiped with sterile IPA prior to entering ISO 7 Buffer area of clean suite. 
5. All medications and supplies are wiped with sterile IPA before entering ISO 5 PEC.
6. Aseptically perform all manipulations within the PEC, including reconstitution and/or transfer of drug as instructed on this Work Order. For reconstituted products, check that all powder is in solution before any transfers occur.
7. Licensed, Registered, or otherwise authorized personnel labels CSP(s). 
8. Final product is checked by Pharmacist during post-check, including leak test for closure integrity including any coring of stoppers or leaks, verifying correct medications and volumes are used, product checked against light/dark background for presence of particulate matter or precipitates, correct color, consistency, and volume and Work Order signed. 
9. Post-checked final product is bagged and final label affixed.
10. Final product is stored in the refrigerator awaiting delivery to the patient.    
 
Add __ D5W to empty bag. Withdraw  __ mL (__ mg) milrinone and add to bag. Remove air. Spike and prime bag with required tubing. Clamp tubing. Final concentration 200, 400 mcg/mL. 

Wasted/Discarded Amount: ________________    
 
Beyond-Use Dating of 9 days REFRIGERATED is based on USP <797>.','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "compounding_instructions" = EXCLUDED."compounding_instructions" WHERE form_list_cmpd_template.allow_sync = 'Yes';
INSERT INTO form_list_cmpd_template ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "compounding_instructions", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-13T13:30:46.000Z','Ertapenem 1gm/100mL Eclipse',NULL,NULL,'2024-02-15T18:23:53.000Z',NULL,'Ertapenem 1gm/100mL Eclipse','Ertapenem 1gm/100mL Eclipse

1. Select appropriate ingredients, vial size or volume, strength, lot, and quantity of drug, diluent, and bags to compound this Work Order. See Drugs/Supplies Used section of Work Order. 
2. Pharmacist to review for correct ingredients, strength, volume, quantity along with quality, color, and integrity of vials and expiration date as part of pre-check and sign Work Order before transferring to clean suite.
3. All medications, supplies, and totes are wiped with sterile IPA prior to entering clean suite. 
4. All medications, supplies, and totes are wiped with sterile IPA prior to entering ISO 7 Buffer area of clean suite. 
5. All medications and supplies are wiped with sterile IPA before entering ISO 5 PEC.
6. Aseptically perform all manipulations within the PEC, including reconstitution and/or transfer of drug as instructed on this Work Order. For reconstituted products, check that all powder is in solution before any transfers occur.
7. Licensed, Registered, or otherwise authorized personnel labels CSP(s). 
8. Final product is checked by Pharmacist during post-check, including leak test for closure integrity including any coring of stoppers or leaks, verifying correct medications and volumes are used, product checked against light/dark background for presence of particulate matter or precipitates, correct color, consistency, and volume and Work Order signed. 
9. Post-checked final product is bagged and final label affixed.
10. Final product is stored in the refrigerator awaiting delivery to the patient.

Reconstitute each ertapenem 1gm vial with 10mL SWFI. Add 90mL NS to each 100mL 200mL/hr elastomeric. QS with 10mL reconstituted ertapenem solution to total volume of 100mL. Final concentration 10mg/mL. 

Wasted/Discarded Amount: ________________    

Homepump Eclipse, SmartEZ, EasyPump II at final concentration 10 mg/mL: Beyond-Use Dating of 7 days REFRIGERATED is based on stability information from Extended Stability for Parenteral Drugs, 6th Edition which is shorter than USP <797> permitted sterility dating.','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "compounding_instructions" = EXCLUDED."compounding_instructions" WHERE form_list_cmpd_template.allow_sync = 'Yes';
INSERT INTO form_list_cmpd_template ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "compounding_instructions", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-13T13:30:46.000Z','Piperacillin Tazobactam 24-Hour SmartEZ',NULL,NULL,'2024-02-15T18:23:53.000Z',NULL,'Piperacillin Tazobactam 24-Hour SmartEZ','Piperacillin Tazobactam 24-Hour SmartEZ:

1. Select appropriate ingredients, vial size or volume, strength, lot, and quantity of drug, diluent, and bags to compound this Work Order. See Drugs/Supplies Used section of Work Order. 
2. Pharmacist to review for correct ingredients, strength, volume, quantity along with quality, color, and integrity of vials and expiration date as part of pre-check and sign Work Order before transferring to clean suite.
3. All medications, supplies, and totes are wiped with sterile IPA prior to entering clean suite. 
4. All medications, supplies, and totes are wiped with sterile IPA prior to entering ISO 7 Buffer area of clean suite. 
5. All medications and supplies are wiped with sterile IPA before entering ISO 5 PEC.
6. Aseptically perform all manipulations within the PEC, including reconstitution and/or transfer of drug as instructed on this Work Order. For reconstituted products, check that all powder is in solution before any transfers occur.
7. Licensed, Registered, or otherwise authorized personnel labels CSP(s). 
8. Final product is checked by Pharmacist during post-check, including leak test for closure integrity including any coring of stoppers or leaks, verifying correct medications and volumes are used, product checked against light/dark background for presence of particulate matter or precipitates, correct color, consistency, and volume and Work Order signed. 
9. Post-checked final product is bagged and final label affixed.

10. Final product is stored in the refrigerator awaiting delivery to the patient.

Reconstitute each 40.5gm bulk vial with 152mL SWFI. Reconstitute each 13.5gm bulk vial with 51mL SWFI. Reconstitute each 4.5gm vial with 17mL SWFI. Reconstitute each 3.375gm vial with 12.67mL SWFI. Reconstitute each 2.25gm vial with 8.5mL SWFI. Yields 225mg/mL.
Add __ mL NS to each 270ml 10ml/hr elastomeric. QS with __mL (__ gm) of piperacillin tazobactam for a total volume of __mL. 

For 18gm/240mL: Add 160mL NS to each 270ml 10ml/hr SmartEZ elastomeric. QS with 80mL (18gm) of piperacillin tazobactam for a total volume of 240mL. 
For 13.5gm/240mL: Add 180mL NS to each 270ml 10ml/hr SmartEZ elastomeric. QS with 60mL (13.5gm) of piperacillin tazobactam for a total volume of 240mL. 
For 10.125gm/240mL: Add 195mL NS to each 270ml 10ml/hr SmartEZ elastomeric. QS with 45mL (10.125gm) of piperacillin tazobactam for a total volume of 240mL.

Wasted/Discarded Amount: ________________    

SmartEZ: at final concentration 10-80 mg/mL: Beyond-Use Dating of 10 days REFRIGERATED is based on USP <797>.','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "compounding_instructions" = EXCLUDED."compounding_instructions" WHERE form_list_cmpd_template.allow_sync = 'Yes';
INSERT INTO form_list_cmpd_template ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "compounding_instructions", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-13T13:30:46.000Z','Cefepime 1 gm/50 mL Elastomeric',NULL,NULL,'2024-02-15T18:23:53.000Z',NULL,'Cefepime 1 gm/50 mL Elastomeric','Cefepime 1 gm/50 mL Elastomeric:

1. Select appropriate ingredients, vial size or volume, strength, lot, and quantity of drug, diluent, and bags to compound this Work Order. See Drugs/Supplies Used section of Work Order. 
2. Pharmacist to review for correct ingredients, strength, volume, quantity along with quality, color, and integrity of vials and expiration date as part of pre-check and sign Work Order before transferring to clean suite.
3. All medications, supplies, and totes are wiped with sterile IPA prior to entering clean suite. 
4. All medications, supplies, and totes are wiped with sterile IPA prior to entering ISO 7 Buffer area of clean suite. 
5. All medications and supplies are wiped with sterile IPA before entering ISO 5 PEC.
6. Aseptically perform all manipulations within the PEC, including reconstitution and/or transfer of drug as instructed on this Work Order. For reconstituted products, check that all powder is in solution before any transfers occur.
7. Licensed, Registered, or otherwise authorized personnel labels CSP(s). 
8. Final product is checked by Pharmacist during post-check, including leak test for closure integrity including any coring of stoppers or leaks, verifying correct medications and volumes are used, product checked against light/dark background for presence of particulate matter or precipitates, correct color, consistency, and volume and Work Order signed. 
9. Post-checked final product is bagged and final label affixed.
10.  Final product is stored in the refrigerator awaiting delivery to the patient.

Reconstitute each cefePIME 1gm vial with 8.8mL SWFI. Reconstitute each Cefepime 2gm vial with 18.5mL SWFI. Yields 1gm/10mL. Add 40mL NS to 50mL Elastomeric. QS with 10mL reconstituted cefePIME solution for final volume of 50mL.  

Wasted/Discarded Amount: ________________    

Eclipse OR Easypump OR SmartEZ: Beyond-Use Dating of 9 days REFRIGERATED is based on USP <797>.','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "compounding_instructions" = EXCLUDED."compounding_instructions" WHERE form_list_cmpd_template.allow_sync = 'Yes';
COMMIT;
