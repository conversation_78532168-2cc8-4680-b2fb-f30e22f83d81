
BEGIN;
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Adm. Jevity @ 95 ml/hr via PEG Tube from 8pm-8am QD with Flexiflo Pump',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Adm. Jevity @ 95 ml/hr via PEG Tube from 8pm-8am QD with Flexiflo Pump','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Adm. Vancomycin, 1 Gm IVPB Q 12 hrs via Groshong thru 07/01/93',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Adm. Vancomycin, 1 Gm IVPB Q 12 hrs via Groshong thru 07/01/93','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Care/Mgmt. of Hickman catheter according to established protocol',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Care/Mgmt. of Hickman catheter according to established protocol','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Adm. Nafcillin 2 Gm IVPB Q 4 hr w/ Cadd-Plus Pump thru 08/15/93',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Adm. Nafcillin 2 Gm IVPB Q 4 hr w/ Cadd-Plus Pump thru 08/15/93','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Adm. 3:1 TPN, 2100 mls QD, 1 hr Taper Up/1 hr Taper down, 12 hr. Cycle',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Adm. 3:1 TPN, 2100 mls QD, 1 hr Taper Up/1 hr Taper down, 12 hr. Cycle','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Insert/Maintain Landmark catheter according to established protocol',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Insert/Maintain Landmark catheter according to established protocol','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Draw SMAC-12, Mg, Phos, Ca Q Wk and report results to M.D.',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Draw SMAC-12, Mg, Phos, Ca Q Wk and report results to M.D.','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Observe for Response to Therapy, S/S of Adverse Reactions/Report to MD',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Observe for Response to Therapy, S/S of Adverse Reactions/Report to MD','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Instruct PCG on Adm. of home IV ATB''s, Care/Mgmt of Landmark catheter',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Instruct PCG on Adm. of home IV ATB''s, Care/Mgmt of Landmark catheter','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','R.Ph to perform Clinical Drug Monitoring & Consultation PRN',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'R.Ph to perform Clinical Drug Monitoring & Consultation PRN','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Instruct Clt/PCG on Pump Operation, Management and Troubleshooting',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Instruct Clt/PCG on Pump Operation, Management and Troubleshooting','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Provide information in short/brief seagments.',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Provide information in short/brief seagments.','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Provide information about need for high protein/carbohydrate diet.',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Provide information about need for high protein/carbohydrate diet.','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Weekly Vancomycin P&T Report Result To Md',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Weekly Vancomycin P&T Report Result To Md','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Provide information about disease process/prognosis and treatment.',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Provide information about disease process/prognosis and treatment.','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (16,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Prevent infection during acute phases of disease/treatment.',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Prevent infection during acute phases of disease/treatment.','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (17,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Alleviate pain.',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Alleviate pain.','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (18,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Instruct clt on couging, deepbreathing and muscular exercises.',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Instruct clt on couging, deepbreathing and muscular exercises.','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (19,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Administered IV hydration as ordered.',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Administered IV hydration as ordered.','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (20,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Coordinate with MD''s office for catheter care & site change.',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Coordinate with MD''s office for catheter care & site change.','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (21,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Homepump and supplies required to complete Rocephin therapy.',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Homepump and supplies required to complete Rocephin therapy.','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (22,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Care/Mgm of OmegaPort per established protocol',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Care/Mgm of OmegaPort per established protocol','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (23,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Needles, syringes, and infusion supplies necessary to complete therapy',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Needles, syringes, and infusion supplies necessary to complete therapy','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (24,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Solu-Medrol 1g/100ml D5W IV over 60 min Qd x 4 days starting',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Solu-Medrol 1g/100ml D5W IV over 60 min Qd x 4 days starting','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (25,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Bact Normal Saline 3 ml IV PRN to maintain Heparin Lock',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Bact Normal Saline 3 ml IV PRN to maintain Heparin Lock','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (26,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Heparin Flush 100 u/ml-3ml IV PRN to maintain Heparin Lock',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Heparin Flush 100 u/ml-3ml IV PRN to maintain Heparin Lock','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (27,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Provide all supplies & homepump necessary to adm drug & maintain line',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Provide all supplies & homepump necessary to adm drug & maintain line','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
INSERT INTO form_list_pot_order ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "active", "allow_sync") VALUES (28,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Provide skilled IV nursing care to complete IV therapy',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Provide skilled IV nursing care to complete IV therapy','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name" WHERE form_list_pot_order.allow_sync = 'Yes';
COMMIT;