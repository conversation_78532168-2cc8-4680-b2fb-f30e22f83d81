
fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Demographics
	gender:
		model:
			if:
				'Female':
					fields: ['are_preg', 'will_be_pregnant']
			prefill: ['patient']
		view:
			label: 'Sex'
			offscreen: true
			readonly: true

	will_be_pregnant:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Do you plan to get pregnant while on this medication?"

	mutations:
		model:
			source: ['EGFR', 'ALK', 'BRAF V600E', 'BRAF V600K', 'CLL with 17p deletion', 'Other']
			if:
				'Other':
					fields: ['mutations_other']
			multi: true
		view:
			control: 'checkbox'
			label: 'Confirmed Mutations'

	mutations_other:
		view:
			control: 'area'
			label: 'Other Mutations'

	scr_value:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Recent SCr Value'

	scr_date:
		model:
			type: 'date'
		view:
			label: 'Recent SCr Date'

	gfr_value:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Recent GFR Value'

	gfr_date:
		model:
			type: 'date'
		view:
			label: 'Recent GFR Date'

	# Renal Assessment
	has_diabetes:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['has_insulin']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with diabetes?'

	has_insulin:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you depend on insulin to regulate blood sugar?'

	had_kidney_disease:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with kidney disease?'

	meds_taking:
		model:
			max: 32
			min: 1
			multi: true
			source:['Acyclovir','Aminoglycosides','Amphotericin','Atripla','Cisplatin','Diuretics (loops, thiazides)','NSAIDS',
				'Prograf','Proton-Pump Inhibitors','Tenofovir','Viread','Truvada','Other','None']
			if:
				'Other':
					fields: ['meds_taking_other']
		view:
			control: 'checkbox'
			label: 'Are you currently taking any of the following (concomitant nephrotoxic) drugs?'

	meds_taking_other:
		view:
			label: 'Other Drugs'

	urine_drop:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you noticed a drop in urine output?'

	has_htn:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pressure_controlled']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with hypertension (HTN)?'

	pressure_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your blood pressure currently controlled?'

	high_chol:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['chol_controlled']
		view:
			control: 'radio'
			label: 'Is your cholesterol level high?'

	chol_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your cholesterol currently controlled?'

	heart_cond:
		model:
			max: 64
			min: 1
			multi: true
			source: ['Congestive heart failure (CHF)', 'Cardiomyopathy', 'Valve Disease', 'Congenital Defects', 'Atrial Fibrillation', 'Angina', 'Pulmonary disease', 'Other', 'None']
			if:
				'Other':
					fields: ['heart_cond_other']
		view:
			control: 'checkbox'
			label: 'Do you suffer from any of the following heart diseases?'

	heart_cond_other:
		view:
			control: 'area'
			label: 'Other Heart Diseases'

	fam_cond:
		model:
			max: 64
			min: 1
			multi: true
			source: ['Coronary Artery Disease (CAD/Atherosclerotic)', 'Angina', 'Deep Vein Thrombosis (DVT)',
						'Cerebral Infarction', 'Myocardial Infarction', 'Other', 'None']
			if:
				'Other':
					fields: ['fam_cond_other']
		view:
			control: 'checkbox'
			label: 'Do you or any of your Parents or siblings have a history of:'

	fam_cond_other:
		view:
			control: 'area'
			label: 'Other Family History'

	per_immob:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you experience prolonged periods of immobilization?'

	had_throm:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with thrombophilia?'

	had_sic:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with sickle cell anemia?'

	has_lowbp:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_sep']
		view:
			control: 'radio'
			label: 'Do your hands/extremities feel cold and clammy (do you experience low BP)?'

	had_sep:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any recent cases of sepsis or infection in blood?'

	are_preg:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: ' Are you currently pregnant or have given birth in the last 6 weeks?'


	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections_group: [
		'Chemotherapy Questionnaire':
			sections: [
				'Chemotherapy General Assessment':
					fields: ['gender', 'are_preg', 'will_be_pregnant', 'mutations', 'mutations_other']
				'Chemotherapy Renal Disease Risk Assessment':
					fields: ['scr_value', 'scr_date', 'gfr_value', 'gfr_date', 'has_diabetes', 'has_insulin', 'had_kidney_disease', 'meds_taking', 'meds_taking_other', 'urine_drop']
				'Chemotherapy Thromboembolic Risk Assessment':
					fields: ['has_htn', 'pressure_controlled', 'high_chol', 'chol_controlled', 'heart_cond', 'heart_cond_other', 'fam_cond', 'fam_cond_other', 'per_immob',
								'had_throm', 'had_sic', 'has_lowbp', 'had_sep']
			]
		]

view:
	comment: 'Patient > Careplan > Assessment > Chemotherapy'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Chemotherapy'
	open: 'read'
