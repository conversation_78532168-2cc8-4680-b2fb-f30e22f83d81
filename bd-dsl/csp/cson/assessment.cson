fields:

	# Base overrides

	admin_location:
		model:
			required: false
			if:
				'*':
					fields: ['reviewed_by']
		view:
			offscreen: true
			readonly: true

	subform_pmp:
		model:
			required: false

	counseling_performed:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'
			columns: 4

	route_id:
		model:
			required: false
			source: 'list_route'
			sourceid: 'code'
			if:
				'IV':
					fields: ['self_admin_medication_na']
				'IVP':
					fields: ['self_admin_medication_na']
				'IA':
					fields: ['self_admin_medication_na']
				'SQ':
					prefill:
						cath_type: 'Subcutaneous'
				'*':
					fields: ['self_admin_medication']
		view:
			readonly: true
			offscreen: false
			label: 'Drug Route'
			columns: 4

	dx_1:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			columns: 2
			readonly: true
			label: 'Primary Diagnosis'

	dx_2:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			readonly: true
			offscreen: true
			label: 'Secondary Diagnosis'

	dx_3:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			readonly: true
			offscreen: true
			label: 'Secondary Diagnosis'

	dx_4:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			readonly: true
			offscreen: true
			label: 'Tertiary Diagnosis'

	dx_5:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			readonly: true
			offscreen: true
			label: 'Quaternary Diagnosis'

	# Demographics
	gender:
		model:
			if:
				'Female':
					fields: ['pregnant_lac']
			prefill: ['patient']
		view:
			label: 'Sex'
			offscreen: true
			readonly: true

	pregnant_lac:
		model:
			required: true
			source: ['No', 'Yes', 'N/A']
		view:
			label: 'Are you currently pregnant or lactating?'
			control: 'radio'
			columns: 4

	delivery_ticket_status:
		model:
			source:
				delivery_ticket: 'Delivery Ticket Creation' # This is a new delivery ticket, not associated with a claim
				ready_to_fill: 'Ready to Fill' # This is a delivery ticket that is ready to be filled and prescription has been verified or started from a refill request
				order_ver: 'Pending Order Verification' # This is a delivery ticket that has been filled and is await pharmacist verification
				pending_conf: 'Pending Confirmation' # This is a delivery ticket that has been filled and is await pharmacist confirmation
				ready_to_bill: 'Ready to Bill' # This is a delivery ticket that has been confirmed by the pharmacist and is ready to be billed
				billed: 'Billed' # This is a delivery ticket that has been billed for all charges on the ticket
				voided: 'Voided' # This is a delivery ticket that has been voided
			if:
				'order_ver':
					sections: ['Post Assessment']
					fields: ['cust_medication_counseling_performed', 'contact_notes']
				'pending_conf':
					sections: ['Post Assessment']
					fields: ['cust_medication_counseling_performed', 'contact_notes']

				'ready_to_bill':
					sections: ['Post Assessment']
					fields: ['cust_medication_counseling_performed', 'contact_notes']

				'billed':
					sections: ['Post Assessment']
					fields: ['cust_medication_counseling_performed', 'contact_notes']

		view:
			columns: 2
			label: 'Status'
			readonly: true
			offscreen: true

	portal_preference:
		model:
			multi: true
			source: ['Web Portal', 'SMS']
			default: ['SMS']
		view:
			control: 'checkbox'
			label: 'Portal Access'
			offscreen: true
			readonly: true

	sms_program_opt:
		model:
			max: 3
			source: ['No', 'Yes']
			default: 'Yes' 
			required: true
			if:
				'Yes':
					fields: ['phone_cell','email']
		view:
			control: 'radio'
			label: 'Patient wishes to opt into the SMS program?'
			offscreen: true
			readonly: true

	phone_cell:
		model:
			prefill: ['patient.phone_cell', 'patient.phone_home']
			required: true
		view:
			format: 'us_phone'
			label: 'Patient SMS Phone'
			offscreen: true
			readonly: true

	email:
		model:
			prefill: ['patient.email']
			required: false
 			# validate: [
			# 		name: 'EmailValidator'
			# ] 
		view:
			label: 'Email Address'
			note: 'Must be a valid email address for Portal Access'
			offscreen: true
			readonly: true

	subform_portal:
		model:
			source: 'patient_portal'
			type: 'subform'
		view:
			label: 'Patient Portal SignUp'
			offscreen: true
			readonly: true

	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['reviewed_by']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Therapy'
			validate: [
					name: 'QolHideValidate'
			]

	therapy_2:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['reviewed_by']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Therapy'

	therapy_3:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['reviewed_by']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Therapy'

	therapy_4:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['reviewed_by']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Therapy'

	therapy_5:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['reviewed_by']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Therapy'

	subform_therapy_1:
		model:
			source: 'assessment_{therapy_1}'
			sourcefilter:
				'assessment_factor': {}
				'assessment_aat': {}
				'assessment_ivig': {}
				'assessment_scig': {}
				'assessment_tnf': {}
				'assessment_iv_tpn': {}
				'assessment_steroid': {}
				'assessment_inotrope': {}
				'assessment_oncology': {}
				'assessment_bisphosphonates': {}
				'assessment_biologic': {}
			type: 'subform'
		view:
			label: 'Primary Therapy Assessment'

	subform_therapy_2:
		model:
			source: 'assessment_{therapy_2}'
			sourcefilter:
				'assessment_factor': {}
				'assessment_aat': {}
				'assessment_ivig': {}
				'assessment_scig': {}
				'assessment_tnf': {}
				'assessment_iv_tpn': {}
				'assessment_steroid': {}
				'assessment_inotrope': {}
				'assessment_oncology': {}
				'assessment_bisphosphonates': {}
				'assessment_biologic': {}
			type: 'subform'
		view:
			label: 'Secondary Therapy Assessment'

	subform_therapy_3:
		model:
			source: 'assessment_{therapy_3}'
			sourcefilter:
				'assessment_factor': {}
				'assessment_aat': {}
				'assessment_ivig': {}
				'assessment_scig': {}
				'assessment_tnf': {}
				'assessment_iv_tpn': {}
				'assessment_steroid': {}
				'assessment_inotrope': {}
				'assessment_oncology': {}
				'assessment_bisphosphonates': {}
				'assessment_biologic': {}
			type: 'subform'
		view:
			label: 'Tertiary Therapy Assessment'

	subform_therapy_4:
		model:
			source: 'assessment_{therapy_4}'
			sourcefilter:
				'assessment_factor': {}
				'assessment_aat': {}
				'assessment_ivig': {}
				'assessment_scig': {}
				'assessment_tnf': {}
				'assessment_iv_tpn': {}
				'assessment_steroid': {}
				'assessment_inotrope': {}
				'assessment_oncology': {}
				'assessment_bisphosphonates': {}
				'assessment_biologic': {}
			type: 'subform'
		view:
			label: 'Quaternary Therapy Assessment'

	subform_therapy_5:
		model:
			source: 'assessment_{therapy_5}'
			sourcefilter:
				'assessment_factor': {}
				'assessment_aat': {}
				'assessment_ig': {}
				'assessment_scig': {}
				'assessment_tnf': {}
				'assessment_iv_tpn': {}
				'assessment_steroid': {}
				'assessment_inotrope': {}
				'assessment_oncology': {}
				'assessment_bisphosphonates': {}
				'assessment_biologic': {}
			type: 'subform'
		view:
			label: 'Quinary Therapy Assessment'

	experiencing_pain:
		model:
			required: true 
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pain_scale']
		view:
			label: 'Are you experiencing any pain?'
			control: 'radio'
			columns: 2

	pain_scale:
		model:
			required: true
			source: ['1' ,'2' ,'3' ,'4' ,'5' ,'6' ,'7' ,'8' ,'9' ,'10']
			if:
				'*':
					fields: ['cust_pain_management']
		view:
			control: 'radio'
			label: 'Pain Scale'
			columns: 2

	cust_pain_management:
		model:
			required: true
		view:
			label: 'What are you doing to control the pain?'
			control: 'area'
			columns: 2

	brand_1:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Primary Brand'

	brand_2:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Brand'

	brand_3:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Brand'

	brand_4:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Brand'

	brand_5:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Brand'

	subform_brand_1:
		model:
			source: 'assessment_{brand_1}'
			sourcefilter:
				'assessment_cimzia': {}
				'assessment_dupixent': {}
				'assessment_enbrel': {}
				'assessment_humira': {}
				'assessment_krystexxa': {}
				'assessment_lemtrada': {}
				'assessment_ocrevus': {}
				'assessment_orencia': {}
				'assessment_radicava': {}
				'assessment_remicade': {}
				'assessment_rituxan': {}
				'assessment_simponi': {}
				'assessment_simponiaria': {}
				'assessment_soliris': {}
				'assessment_stelara': {}
				'assessment_tysabri': {}
				'assessment_vyvgart': {}
				'assessment_vancomycin': {}
				'assessment_methyl': {}
			type: 'subform'
		view:
			label: 'Primary Drug Brand Assessment'

	subform_brand_2:
		model:
			source: 'assessment_{brand_2}'
			sourcefilter:
				'assessment_cimzia': {}
				'assessment_dupixent': {}
				'assessment_enbrel': {}
				'assessment_humira': {}
				'assessment_krystexxa': {}
				'assessment_lemtrada': {}
				'assessment_ocrevus': {}
				'assessment_orencia': {}
				'assessment_radicava': {}
				'assessment_remicade': {}
				'assessment_rituxan': {}
				'assessment_simponi': {}
				'assessment_simponiaria': {}
				'assessment_soliris': {}
				'assessment_stelara': {}
				'assessment_tysabri': {}
				'assessment_vyvgart': {}
				'assessment_vancomycin': {}
				'assessment_methyl': {}
			type: 'subform'
		view:
			label: 'Secondary Drug Brand Assessment'

	subform_brand_3:
		model:
			source: 'assessment_{brand_3}'
			sourcefilter:
				'assessment_cimzia': {}
				'assessment_dupixent': {}
				'assessment_enbrel': {}
				'assessment_humira': {}
				'assessment_krystexxa': {}
				'assessment_lemtrada': {}
				'assessment_ocrevus': {}
				'assessment_orencia': {}
				'assessment_radicava': {}
				'assessment_remicade': {}
				'assessment_rituxan': {}
				'assessment_simponi': {}
				'assessment_simponiaria': {}
				'assessment_soliris': {}
				'assessment_stelara': {}
				'assessment_tysabri': {}
				'assessment_vyvgart': {}
				'assessment_vancomycin': {}
				'assessment_methyl': {}
			type: 'subform'
		view:
			label: 'Tertiary Drug Brand Assessment'

	subform_brand_4:
		model:
			source: 'assessment_{brand_4}'
			sourcefilter:
				'assessment_cimzia': {}
				'assessment_dupixent': {}
				'assessment_enbrel': {}
				'assessment_humira': {}
				'assessment_krystexxa': {}
				'assessment_lemtrada': {}
				'assessment_ocrevus': {}
				'assessment_orencia': {}
				'assessment_radicava': {}
				'assessment_remicade': {}
				'assessment_rituxan': {}
				'assessment_simponi': {}
				'assessment_simponiaria': {}
				'assessment_soliris': {}
				'assessment_stelara': {}
				'assessment_tysabri': {}
				'assessment_vyvgart': {}
				'assessment_vancomycin': {}
				'assessment_methyl': {}
			type: 'subform'
		view:
			label: 'Quaternary Drug Brand Assessment'

	subform_brand_5:
		model:
			source: 'assessment_{brand_5}'
			sourcefilter:
				'assessment_cimzia': {}
				'assessment_dupixent': {}
				'assessment_enbrel': {}
				'assessment_humira': {}
				'assessment_krystexxa': {}
				'assessment_lemtrada': {}
				'assessment_ocrevus': {}
				'assessment_orencia': {}
				'assessment_radicava': {}
				'assessment_remicade': {}
				'assessment_rituxan': {}
				'assessment_simponi': {}
				'assessment_simponiaria': {}
				'assessment_soliris': {}
				'assessment_stelara': {}
				'assessment_tysabri': {}
				'assessment_vyvgart': {}
				'assessment_vancomycin': {}
				'assessment_methyl': {}
			type: 'subform'
		view:
			label: 'Quinary Drug Brand Assessment'

	shipping_location:
		model:
			source: ['Physician office', 'Home', 'Infusion Suite', 'Other']
			required: true
			if:
				'Physician office':
					fields: ['reviewed_by']
				'*':
					fields: ['cust_pmp_benefits', 'cust_benefits_understand_optout', 'first_time', 'medical_history',
					'therapy_1', 'therapy_2', 'therapy_3', 'therapy_4', 'therapy_5', 'brand_1', 'brand_2', 'brand_3', 'brand_4', 'brand_5']
		view:
			control: 'radio'
			label: 'Med Administration Location'
			columns: 2

	cust_pmp_benefits:
		model:
			required: true
			multi: true
			source: ["Goal: Keep patient compliant to appropriate adherence level and ensure patient’s understanding of therapy -PDC score >90% for Specialty Disease States including, but not limited to Oncology, RA, Hep C, IVIG, and Multiple Sclerosis -PDC score >80% for all other non-specialty disease states",
			'Goal: Minimize Patient Adverse Reactions, Site Infections, Hospitalizations and ER visits',
			'Goal: Minimize Patient issues in administration and side effects',
			'Goal: Maintain monthly follow-ups if patient has multiple comorbidities, multiple specialty medications, or classifies as a “high-risk” patient per CSP P&P']
		view:
			label: 'The benefits of getting enrolled in CSP’s Patient Management Program'
			control: 'checkbox'
			columns: 1

	cust_benefits_understand_optout:
		model:
			max: 12
			source: ['Opt-Out', 'Opt-In']
			required: false
			default: 'Opt-In'
			if:
				'Opt-In':
					fields: ['disease_1', 'disease_2', 'disease_3', 'disease_4', 'disease_5', 'clinical_1', 'clinical_2', 'clinical_3', 'clinical_4', 'clinical_5']
		view:
			control: 'radio'
			label: 'Patient understands the benefits and wish to Opt-In/Opt-Out of Patient Management Program?'
			columns: 1

	cust_medication_counseling_performed:
		model:
			required: true
			multi: true
			default: ['Directions for use', 'Plan of care', 'Potential ADRs', 'Adherence', 'Meds, Supply storage, Disposal', 'How to contact the pharmacy', 'Drug Interaction']
			source: ['Therapy expectations and possible outcomes', 'Directions for use, administration and duration', 'Side effects (prevention, minimization, and management)', 'Adherence and missed dose management', 'Safety precautions, interactions, contraindications', 'Safe handling, storage, disposal', 'How to contact the pharmacy','Report new or changed medications to the pharmacy', 'Vaccinations, if applicable', 'Educational, community, financial and advocacy resources, if applicable', 'Other']
		view:
			class: 'checkbox checkbox-2'
			control: 'checkbox'
			label: "Assessed the patient's understanding and provided counseling as necessary/appropriate:"
			columns: 1

	cust_pharm_checkbox:
		model:
			required: true 
			multi: true 
			source: ['Med Rec reviewed', 'DUR reviewed', 'Allergies reviewed']
		view:
			class: 'checkbox-3'
			label:"Pharmacist Checklist"
			control: 'checkbox'
			columns: 2

	contact_notes:
		view:
			label: 'Notes'
			columns: 1

	# Treatment History
	first_time:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['previous_details_available']
		view:
			control: 'radio'
			label: 'Have you received prescribed medication before?'
			columns: 2

	previous_details_available:
		model:
			max: 3
			min: 1
			required: true
			default: 'Yes'
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Previous drug therapy details are available?'
			columns: 2

	medical_history:
		view:
			control: 'area'
			label: 'Past Medical History / Comorbidities'
			columns: 2

	fields_based_on_route:
		model:
			source: ['Yes', 'No']
			default: 'No'
			if:
				'Yes':
					fields: ['self_admin_medication']
				'No':
					fields: ['self_admin_medication_na']
		view:
			control: 'radio'
			label: 'Show fields based on routes'
			readonly: true
			offscreen: true

	self_admin_medication:
		model:
			required: true
			source: ['Yes', 'No']
			default: 'Yes'
			if:
				'No':
					fields: ['household_assist']
		view:
			control: 'radio'
			label: 'Are you able to self-administer the medication?'
			columns: 2

	self_admin_medication_na:
		model:
			required: true
			source: ['N/A']
			default: 'N/A'
		view:
			control: 'radio'
			label: 'Are you able to self-administer the medication?'
			columns: 2

	household_assist:
		model:
			required: true
			source: ['Yes', 'No']
			if:
				'No':
					fields: ['extra_details']
		view:
			control: 'radio'
			label: 'Is there a caregiver or member in the household who can assist in administration?'
			columns: 2

	extra_details:
		model:
			required: false
			type: 'text'
		view:
			label: 'Please provide further details'
			control: 'area'
			columns: 2

	cath_type:
		model:
			max: 32
			min: 1
			source: ['NA', 'Central Line', 'Peripheral Line', 'Port', 'Subcutaneous', 'Self-infuses with butterfly needles']
			if:
				'Central Line':
					fields: ['cath_type_cent', 'catheter_num']
				'Peripheral Line':
					fields: ['cath_type_per']
				'Self-infuses with butterfly needles':
					fields: ['cath_self_infuse_comment']
		view:
			control: 'radio'
			label: 'Catheter type'
			columns: 2

	cath_self_infuse_comment:
		view:
			control: 'area'
			label: 'Self-infuses comment'
			columns: 2

	catheter_num:
		model:
			max: 4
			min: 1
			type: 'int'
		view:
			label: 'Number of Intravenous Catheters (1-4)'
			columns: 2

	cath_type_cent:
		model:
			max: 32
			min: 1
			source: {groshong_sl:'Groshong SL', groshong_dl:'Groshong DL', groshong_tl:'Groshong TL', hickman_sl:'Hickman SL', hickman_dl:'Hickman DL', hickman_tl:'Hickman TL', picc_sl:'PICC SL', picc_dl:'PICC DL',
			picc_tl:'PICC TL', powerpicc_sl:'PowerPICC SL', powerpicc_dl:'PowerPICC DL', powerpicc_tl:'PowerPICC TL', port_a_cah_sl:'Port-a-cath SL', port_a_cah_dl:'Port-a-cath DL', subclavian_sl:'Subclavian SL',
			subclavian_dl:'Subclavian DL', subclavian_tl:'Subclavian TL', other:'Other'}
			if:
				other:
					fields: ['cath_type_cent_other']
		view:
			control: 'select'
			label: 'Central Line Type'
			columns: 2

	cath_type_cent_other:
		model:
			max: 128
		view:
			label: 'Catheter Type Other'
			columns: 2

	cath_type_per:
		model:
			max: 32
			min: 1
			source: ['Peripheral', 'Midline']
		view:
			control: 'radio'
			label: 'Peripheral Line'
			columns: 2

	disease_1:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Primary Disease'

	disease_2:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Disease'

	disease_3:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Disease'

	disease_4:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Disease'

	disease_5:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Disease'

	subform_disease_1:
		model:
			source: 'assessment_{disease_1}'
			sourcefilter:
				'assessment_asthma': {}
				'assessment_hiv': {}
				'assessment_hepc': {}
				'assessment_hepb': {}
				'assessment_hemob': {}
				'assessment_hemoa': {}
				'assessment_ra': {}
				'assessment_psoriasis': {}
				'assessment_ms': {}
				'assessment_vwd': {}
			type: 'subform'
		view:
			label: 'Primary Disease Assessment'

	subform_disease_2:
		model:
			source: 'assessment_{disease_2}'
			sourcefilter:
				'assessment_asthma': {}
				'assessment_hiv': {}
				'assessment_hepc': {}
				'assessment_hepb': {}
				'assessment_hemob': {}
				'assessment_hemoa': {}
				'assessment_ra': {}
				'assessment_psoriasis': {}
				'assessment_ms': {}
				'assessment_vwd': {}
			type: 'subform'
		view:
			label: 'Secondary Disease Assessment'

	subform_disease_3:
		model:
			source: 'assessment_{disease_3}'
			sourcefilter:
				'assessment_asthma': {}
				'assessment_hiv': {}
				'assessment_hepc': {}
				'assessment_hepb': {}
				'assessment_hemob': {}
				'assessment_hemoa': {}
				'assessment_ra': {}
				'assessment_psoriasis': {}
				'assessment_ms': {}
				'assessment_vwd': {}
			type: 'subform'
		view:
			label: 'Tertiary Disease Assessment'

	subform_disease_4:
		model:
			source: 'assessment_{disease_4}'
			sourcefilter:
				'assessment_asthma': {}
				'assessment_hiv': {}
				'assessment_hepc': {}
				'assessment_hepb': {}
				'assessment_hemob': {}
				'assessment_hemoa': {}
				'assessment_ra': {}
				'assessment_psoriasis': {}
				'assessment_ms': {}
				'assessment_vwd': {}
			type: 'subform'
		view:
			label: 'Quaternary Disease Assessment'

	subform_disease_5:
		model:
			source: 'assessment_{disease_5}'
			sourcefilter:
				'assessment_asthma': {}
				'assessment_hiv': {}
				'assessment_hepc': {}
				'assessment_hepb': {}
				'assessment_hemob': {}
				'assessment_hemoa': {}
				'assessment_ra': {}
				'assessment_psoriasis': {}
				'assessment_ms': {}
				'assessment_vwd': {}
			type: 'subform'
		view:
			label: 'Quinary Disease Assessment'

	clinical_1:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['subform_clinical_1']
					sections: ['Primary Clinical Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Clinical Assessment'

	clinical_2:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['subform_clinical_2']
					sections: ['Secondary Clinical Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Clinical Assessment'

	clinical_3:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['subform_clinical_3']
					sections: ['Tertiary Clinical Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Clinical Assessment'

	clinical_4:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['subform_clinical_4']
					sections: ['Quaternary Clinical Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Clinical Assessment'

	clinical_5:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['subform_clinical_5']
					sections: ['Quinary Clinical Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Clinical Assessment'

	subform_clinical_1:
		model:
			source: 'clinical_{clinical_1}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
			type: 'subform'
		view:
			label: 'Primary Clinical Assessment'

	subform_clinical_2:
		model:
			source: 'clinical_{clinical_2}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
			type: 'subform'
		view:
			label: 'Secondary Clinical Assessment'

	subform_clinical_3:
		model:
			source: 'clinical_{clinical_3}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
			type: 'subform'
		view:
			label: 'Tertiary Clinical Assessment'

	subform_clinical_4:
		model:
			source: 'clinical_{clinical_4}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
			type: 'subform'
		view:
			label: 'Quaternary Clinical Assessment'

	subform_clinical_5:
		model:
			source: 'clinical_{clinical_5}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
			type: 'subform'
		view:
			label: 'Quinary Clinical Assessment'

	measurement_log:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				form: 'patient_measurement_log'
				selectable: false
			grid:
				add: 'inline'
				edit: true
				rank: 'none'
				fields: ['created_by', 'date', 'height', 'weight']
				width: [40, 20, 20, 20]
			label: 'Measurement Log'
			readonly: true

	patient_medications:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': 'Active'
		view:
			embed:
				form: 'patient_medication'
				selectable: false
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['fdb_id', 'medication_dose', 'medication_frequency', 'start_date']
				label: ['Medication', 'Dose', 'Frequency', 'Start Dt']
				width: [35, 25, 25, 15]
			label: 'Med List'

	patient_allergies:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				end_date:
					'dynamic': ''
		view:
			embed:
				form: 'patient_allergy'
				selectable: false
				add_preset:
					status: 'Active'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['allergen_id', 'reaction_id', 'severity_id', 'start_date']
				label: ['Allergen', 'Reaction', 'Severity', 'Start Dt']
				width: [35, 25, 25, 15]
			label: 'Allergies'

	patient_interactions:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			readonly: true
			embed:
				form: 'interactions'
				selectable: false
			grid:
				add: 'none'
				hide_cardmenu: true
				edit:true
				rank: 'none'
				fields: ['created_by', 'has_da', 'has_dd']
				label: ['Created By','Drug Allergy', 'Drug Drug']
				width: [30, 30, 30]
			label: 'Interactions'

	patient_medical_hx:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				end_date:
					'dynamic': ''
		view:
			embed:
				form: 'patient_medical_hx'
				selectable: false
			grid:
				add: 'none'
				edit: true
				rank: 'none'
				fields: ['created_by', 'created_on', 'medical_hist', 'medical_hist_desc']
				label: ['Created By', 'Created On', 'Medical History', 'Details']
				width: [20, 15, 35, 30]
			label: 'Medical History'

	patient_interaction_btn:
		model:
			source: ['Check Interactions']
		view:
			columns: 2
			class: 'dsl-button'
			control: 'checkbox'
			label: 'Patient Interaction'
			validate: [
				name: 'DrugAllergyInteraction'
			]

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['patient-form']
	name: ['patient_id', 'created_on']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
			['therapy_1']
			['therapy_2']
			['therapy_3']
			['therapy_4']
			['therapy_5']
			['brand_1']
			['brand_2']
			['brand_3']
			['brand_4']
			['brand_5']
			['disease_1']
			['disease_2']
			['disease_3']
			['disease_4']
			['disease_5']
			['clinical_1']
			['clinical_2']
			['clinical_3']
			['clinical_4']
			['clinical_5']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections_group: [
			'H&P':
				tab: 'Assessment'
				hide_header: true
				indent: false
				fields: ['delivery_ticket_status', 'assessment_date', 'route_id', 'therapy_1', 'therapy_2', 'therapy_3',
						'therapy_4', 'therapy_5', 'brand_1', 'brand_2', 'brand_3', 'brand_4',
						'brand_5', 'disease_1', 'disease_2', 'disease_3',
						'disease_4', 'disease_5', 'clinical_1', 'clinical_2', 'clinical_3',
						'clinical_4', 'clinical_5', 'shipping_location',
						'cust_pmp_benefits', 'cust_benefits_understand_optout',
						'self_admin_medication', 'self_admin_medication_na', 'household_assist','extra_details'
						]
			'Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['first_time', 'previous_details_available', 'medical_history',
				'gender', 'pregnant_lac','experiencing_pain', 'pain_scale', 'cust_pain_management']

			'Primary Therapy Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_therapy_1'] # subform
			'Secondary Therapy Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_therapy_2'] # subform
			'Tertiary Therapy Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_therapy_3'] # subform
			'Quaternary Therapy Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_therapy_4'] # subform
			'Quinary Therapy Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_therapy_5'] # subform

			'Primary Drug Brand Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_brand_1'] # subform
			'Secondary Drug Brand Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_brand_2'] # subform
			'Tertiary Drug Brand Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_brand_3'] # subform
			'Quaternary Drug Brand Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_brand_4'] # subform
			'Quinary Drug Brand Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_brand_5']

			'Primary Disease Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_disease_1'] # subform
			'Secondary Disease Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_disease_2'] # subform
			'Tertiary Disease Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_disease_3'] # subform
			'Quaternary Disease Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_disease_4'] # subform
			'Quinary Disease Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_disease_5']

			'Primary Clinical Assessment':
				hide_header: true
				indent: false
				tab: 'Clinical'
				fields: ['subform_clinical_1'] # subform
			'Secondary Clinical Assessment':
				hide_header: true
				indent: false
				tab: 'Clinical'
				fields: ['subform_clinical_2'] # subform
			'Tertiary Clinical Assessment':
				hide_header: true
				indent: false
				tab: 'Clinical'
				fields: ['subform_clinical_3'] # subform
			'Quaternary Clinical Assessment':
				hide_header: true
				indent: false
				tab: 'Clinical'
				fields: ['subform_clinical_4'] # subform
			'Quinary Clinical Assessment':
				hide_header: true
				indent: false
				tab: 'Clinical'
				fields: ['subform_clinical_5']

			'DUR - Medications':
				hide_header: true
				indent: false
				tab: 'DUR'
				fields: ['patient_medications']

			'DUR - Allergies':
				hide_header: true
				indent: false
				tab: 'DUR'
				fields: ['patient_allergies']
			
			'DUR - DD DA Interaction':
				hide_header: true
				indent: false
				tab: 'DUR'
				fields: ['patient_interaction_btn']

			'DUR - Interaction':
				hide_header: true
				indent: false
				tab: 'DUR'
				fields: ['patient_interactions']

			'Pharmacist Review':
				hide_header: true
				indent: false
				tab: 'DUR'
				fields: ['cust_pharm_checkbox']

			'Post Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				fields: ['cust_medication_counseling_performed', 'contact_notes']
	]

view:
	comment: 'Patient > Careplan > Assessment'
	grid:
		fields: ['assessment_date', 'created_by', 'updated_on']
		sort: ['-assessment_date']
	label: 'Assessment Questionnaire'
	open: 'read'
