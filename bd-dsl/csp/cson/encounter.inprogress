fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true

	bleed_log:
		view:
			offscreen: true
			readonly: true

	is_legacy:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['subform_legacy']
					sections: ['Envoy Nursing Note']
				'*':
					sections: ['']
		view:
			label: 'Is Legacy'
			offscreen: true
			readonly: true

	subform_legacy:
		model:
			type: 'subform'
			multi: false
			source: 'encounter_legacy'
		view:
			label: 'Legacy Data'

	code_status:
		model:
			required: true
			prefill: ['patient']
			source: ['Full', 'DNR']
		view:
			control: 'radio'
			label: 'Code Status'
			columns: 4

	advanced_directive:
		model:
			required: true
			prefill: ['patient']
			max: 3
			source: ['No', 'Yes']
		view:
			label: 'Advanced Directives'
			control: 'radio'
			columns: 4

	user_name_str:
		model:
			if:
				'Kahu Agency Nurse':
					fields: ['generic_nurse_name']
				'Kahu Agency Nurse (Nurse)':
					fields: ['generic_nurse_name']
				'43392':
					fields: ['generic_nurse_name']
				'Adept Infusion Nurse':
					fields: ['generic_nurse_name']
				'Aplus Nursing Care':
					fields: ['generic_nurse_name']
				'Infusion of Care Infusion Nurse':
					fields: ['generic_nurse_name']
				'Infusion Partners 360 Infusion Nurse':
					fields: ['generic_nurse_name']
		view:
			label: 'Note By'
			readonly: true
			template: '{{user.name}}'

	generic_nurse_name:
		model:
			required: true
		view:
			label: 'Full Nurse Name'

	# TODO: Need to handle non-CSP orders
	order_out_csp:
		model:
			source: ['No', 'Yes']
			prefill: ['encounter', 'legacy_encounter']
			if:
				'Yes':
					fields: ['primary_order_id' , 'primary_order_dosing_count']
				'No':
					fields: ['med_name']
		view:
			control: 'radio'
			label: 'Orders from CSP?'
			columns: 4

	med_name:
		model:
			prefill: ['encounter', 'legacy_encounter']
			required: true
		view:
			label: 'Primary Medication'
			columns: 4

	contact_reason:
		model:
			if:
				'*':
					fields: ['reviewed_by']

	contact_location:
		model:
			max: 12
			min: 1
			required: true
			default: ''
			source: ['Home', 'AIS', 'Other']
			if:
				'Home':
					fields: ['travel_time_st', 'travel_time_ed', 'travel_time_from_st', 'travel_time_from_ed', 'travel_time_total', 'anaphylaxis_kit']
					sections: ['Previous Environment Assessments', 'Environment Assessment', 'Analphylaxis Kit', 'Fall Risk Assessment', 'Fall Risk Score']
				'Other':
					fields: ['travel_time_st', 'travel_time_ed', 'travel_time_from_st', 'travel_time_from_ed', 'travel_time_total', 'anaphylaxis_kit']
					sections: ['Analphylaxis Kit']
			prefill: ['encounter', 'legacy_encounter']
		view:
			control: 'radio'
			validate: [
					name: 'HandleEnvAndDischargeIns'
			]
			label: 'Contact Visit'

	completed:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['esign_warning', 'rn_signature', 'cc_signature']
			default: 'No'
		view:
			control: 'radio'
			label: 'Note Completed?'

	esign_warning:
		model:
			multi: true
			source: ["<span color='red'>YOU WILL NOT BE ABLE TO EDIT THE NOTE AGAIN AFTER E-SIGNING</span>"]
		view:
			control: 'checkbox'
			label: 'Warning'
			class: 'list'
			readonly: true

	cust_contact_reason:
		model:
			required: true
			multi: false
			default: 'Initial Comprehensive visit'
			source: ['Initial Comprehensive visit', 'Reassessment', 'Follow up visit',
			'One-Time Skilled Nurse', 'Discharge Summary Non-Visit', 'Transfer Summary Non-Visit' ,'On Hold']
			if:
				'Initial Comprehensive visit':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments', 'Lots Used',
					'Infusion/Vitals Log', 'Measurement Log', 'Drug Admin', 'Review Of Systems',
					'Response to Therapy/Teaching-Education Reviewed', 'Signatures Requested']
					fields: ['subform_ros', 'measurement_log',
					'subform_route_admin', 'subform_vital', 'subform_lots', 'route_id']
				'Reassessment':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments', 'Lots Used',
					'Infusion/Vitals Log', 'Measurement Log', 'Drug Admin', 'Review Of Systems',
					'Response to Therapy/Teaching-Education Reviewed', 'Signatures Requested']
					fields: ['subform_ros', 'measurement_log',
					'subform_route_admin', 'subform_vital', 'subform_lots', 'route_id']
				'Follow up visit':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments', 'Lots Used',
					'Infusion/Vitals Log', 'Measurement Log', 'Drug Admin', 'Review Of Systems',
					'Response to Therapy/Teaching-Education Reviewed', 'Signatures Requested']
					fields: ['subform_ros', 'measurement_log',
					'subform_route_admin', 'subform_vital', 'subform_lots', 'route_id']
				'One-Time Skilled Nurse':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments', 'Measurement Log', 'Review Of Systems', 'Response to Therapy/Teaching-Education Reviewed', 'Signatures Requested']
					fields: ['subform_ros','measurement_log']
				'Discharge Summary Non-Visit':
					fields: ['in_facility',  'height', 'weight', 'lab_draw']
					sections: ['Discharge/Treatment Summary Non-Visit', 'Medication and Allergens List', 'Discharge Instructions']
				'Transfer Summary Non-Visit':
					fields: ['in_facility',  'height', 'weight', 'lab_draw']
					sections: ['Discharge/Treatment Summary Non-Visit', 'Medication and Allergens List', 'Discharge Instructions']
				'On Hold':
					fields: ['on_hold_details']
		view:
			control: 'checkbox'
			note: 'Select all that apply'
			label: 'Reason for Visit'
			columns: -2

	on_hold_details:
		view:
			label: 'On-Hold Reason'
			control: 'area'
			columns: 2

	in_facility:
		model:
			prefill: ['encounter', 'legacy_encounter']
			required: true
			source: ['No', 'Yes', 'Answer not provided']
			if:
				'Yes':
					fields: ['in_facility_type', 'in_facility_name', 'in_facility_address', 'in_facility_phone', 'in_facility_length', 'in_facility_notes']
		view:
			control: 'radio'
			label: 'Are you currently in a skilled nursing facility, hospital, assisted living, or hospice?'
			columns: 2

	in_facility_type:
		model:
			prefill: ['encounter', 'legacy_encounter']
			required: false
			source: ['Skilled Nursing Facility', 'Hospital', 'Assisted Living', 'Hospice']
		view:
			label: 'Facility Type'
			columns: 2

	in_facility_name:
		model:
			prefill: ['encounter', 'legacy_encounter']
			required: false
		view:
			label: 'Facility Name'
			columns: 2

	in_facility_address:
		model:
			prefill: ['encounter', 'legacy_encounter']
			required: false
		view:
			label: 'Facility Address'
			columns: 2

	in_facility_phone:
		model:
			prefill: ['encounter', 'legacy_encounter']
			required: false
		view:
			label: 'Facility Phone'
			format: 'us_phone'
			columns: 2

	in_facility_length:
		model:
			required: false
		view:
			label: 'How long have you been at the current facility?'
			columns: 2

	in_facility_notes:
		view:
			control: 'area'
			label: 'Facility Notes'
			columns: 2

	lab_draw:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['lab_draw_type', 'subform_lab_draw']
					sections: ['Lab Draw']
		view:
			control: 'radio'
			label: 'Lab Draw?'
			columns: 4

	lab_draw_type:
		model:
			source: ['Quest Diagnostics', 'Other']
			required: true
		view:
			control: 'radio'
			label: 'Lab Draw Type'
			columns: 4

	subform_discharge_instr:
		model:
			source: 'enc_discharge_instructions'
			multi: false
			type: 'subform'
		view:
			label: 'Discharges Instructions'

	subform_nursing_inventions:
		model:
			source: 'enc_nursing_intervention_order'
			multi: false
			type: 'subform'
		view:
			label: 'Nursing Orders/Inventions'

	subform_plan_physical_assessment:
		model:
			source: 'enc_plan_treatment_physical_assessment'
			multi: false
			type: 'subform'
		view:
			label: 'Plan Physical Assessment'

	subform_dis_summary:
		model:
			source: 'enc_discharge_summary'
			multi: false
			type: 'subform'
		view:
			label: 'Discharge Summary'

	subjective:
		model:
			required: true
		view:
			label: 'Subjective'
			control: 'area'
			columns: 2

	objective:
		model:
			required: true
		view:
			label: 'Objective'
			control: 'area'
			columns: 2

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
		careplan_delivery_tick:
			link:
				patient_id: 'patient_id'
			filter:
				status: ['ready_to_bill', 'billed']
				void: '!Yes'
			max: 'created_on'
		encounter_environment:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
		company:
			filter:
				id: 1
			max: 'created_on'
		patient_insurance:
			link:
				patient_id: 'patient_id'
			filter:
				active: 'Yes'
				type_id: ['MCRB', 'MCRD']
			max: 'created_on'
		checklist_fall:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
		legacy_encounter:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	access:
		create:     ['admin', 'pharm', 'csr', 'cm', 'nurse']
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm', 'csr', 'nurse']
		read:       ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm', 'liaison', 'crn', 'patient','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm', 'liaison','physician']
		request:    []
		update:     ['admin', 'pharm', 'csr', 'cm', 'nurse','liaison', 'crn','physician']
		update_all: ['admin', 'pharm','physician']
		write:      ['admin', 'pharm', 'csr', 'cm', 'nurse','liaison', 'crn','physician']
	bundle: ['patient-form']
	name: ['contact_date', 'cust_contact_reason']
	sections_group: [
		'Visit Start':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['travel_time_st', 'travel_time_ed', 'time_in', 'contact_location', 'cust_contact_reason',
			'contact_details', 'external_note_contact_check', 'uses_external_nursing_note']

		'Lab Draw':
			indent: false
			tab: 'Pre-Visit'
			fields: ['subform_lab_draw']

		'Discharge/Treatment Summary Non-Visit':
			tab: 'Pre-Visit'
			indent: false
			fields: ['subform_dis_summary']

		'Patient Demographics':
			tab: 'Pre-Visit'
			indent: false
			fields: ['subform_pt_demographics']
		'Nursing Intervention/Orders':
			tab: 'Pre-Visit'
			indent: false
			fields: ['subform_nursing_inventions']

		#'POC Review and Care Coordination':
		'Nursing Care Plan (485)':
			hide_header: true
			indent: false
			tab: 'Care Plan'
			fields: ['cms485_careplan']
		'Treatment Plan':
			hide_header: true
			indent: false
			tab: 'Treatment Plan'
			fields: ['treatment_plan']

		'Plan of Treatment Physical Assessment':
			tab: 'Pre-Visit'
			indent: false
			fields: ['subform_plan_physical_assessment']

		'Discharge Instructions':
			tab: 'Pre-Visit'
			indent: false
			fields: ['subform_discharge_instr']

		'Measurement Log':
			note: 'Include any updated measurements'
			indent: false
			tab: 'Pre-Visit'
			fields: ['measurement_log']

		'Medical History':
			indent: false
			tab: 'Pre-Visit'
			fields: ['patient_medical_hx']

		'Medication Profile':
			indent: false
			tab: 'Pre-Visit'
			fields: ['patient_medications']

		'Allergies':
			indent: false
			tab: 'Pre-Visit'
			fields: ['patient_allergies']

		'DUR - DD DA Interaction':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['patient_interaction_btn']

		'DUR - Interaction':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['patient_interactions']

		'Previous Environment Assessments':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['embed_environment']

		'Environment Assessment':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['subform_environment']

		'Analphylaxis Kit':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['anaphylaxis_kit', 'anaphylaxis_kit_expire', 'anaphylaxis_kit_why', 'anaphylaxis_kit_notified', 'pharmacy_not_notified_why']

		'Catheter Log':
			hide_header: true
			indent: false
			tab: 'Access'
			fields: ['catheter_log']
		'Catheter Access Assessment':
			hide_header: true
			indent: false
			tab: 'Access'
			fields: ['subform_access']

		'Vital Signs (Baseline)':
			hide_header: true
			indent: false
			tab: 'Vitals/Admin'
			fields: ['bp', 'pulse', 'temp']
		'Vital Signs Comments':
			hide_header: true
			indent: false
			tab: 'Vitals/Admin'
			fields: ['vital_comments']

		'Pre-Medications (CSP)':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['premeds']
		'Pre-Hydration (CSP)':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['prehydration']

		'Drug Admin (CSP)':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['subform_route_admin'] # subform

		'Lots Used':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['subform_lots']

		'Flushes (CSP)':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['subform_flush']

		'Infusion/Vitals Log':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['subform_vital']

		'Post-Hydration (CSP)':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['posthydration']
		'Post-Medications (CSP)':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['postmeds']

		'Visit Details':
			fields: ['subjective', 'objective']
			prefill: 'encounter'

		'Review Of Systems':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_ros'] # subform

		'Fall Risk Assessment':
			indent: false
			tab: 'Assessment'
			fields: ['subform_checklist_fall'] # subform

		'Fall Risk Score':
			indent: false
			tab: 'Assessment'
			note: '0-24 = No Risk - Good basic nursing care\n25-50 = Low Risk - Implement standard fall interventions\n≥50 = High Risk - Implement high-risk interventions'
			fields: ['fall_risk_score', 'fall_risk_score_by', 'fall_risk_score_datetime']

		'Response to Therapy/Teaching-Education Reviewed':
			indent: false
			tab: 'Assessment'
			fields: ['post_observations', 'post_observations_note', 'new_problems', 'new_problems_list',
			'new_problems_notified', 'new_problems_comment', 'teaching']

		'Post Visit':
			hide_header: true
			indent: false
			tab: 'Review'
			fields: ['time_out', 'time_total', 'travel_time_from_st', 'travel_time_from_ed',
			'travel_time_total', 'total_mileage', 'next_visit_scheduled', 'next_infusion', 'additional_notes']

		'Signatures Requested':
			indent: false
			tab: 'Review'
			fields: ['esign_warning', 'client_signature', 'rn_signature']

		'Post Visit Review':
			indent: false
			tab: 'Review'
			fields: ['pharm_warning', 'pharm_notes', 'ready_for_review', 'review_status']

		'Approve':
			modal: true
			indent: false
			fields: ['approved_by', 'approved_datetime', 'cc_signature']
		'Denied':
			modal: true
			indent: false
			fields: ['denied_by', 'denied_datetime', 'denied_reason']
		'Locked/Unlock History':
			indent: false
			tab: 'Review'
			fields: ['subform_lock_history']

		'Interventions/ADE':
			note: 'Document any ADEs or Catheter related event as an intervention'
			fields: ['subform_intervention']

		'File Attachments':
			note: 'Attach scans of labels if available'
			fields: ['subform_attachment'] # subform
	]

view:
	comment: 'Patient > Careplan > Encounter'
	grid:
		fields: ['created_by', 'contact_date', 'time_in', 'time_out', 'cust_contact_reason', 'time_total', 'travel_time_total']
		sort: ['-id']
	label: 'Patient Encounter'
	open: 'read'
	block:
		validate: [
			name: 'EncounterBlock'
		]