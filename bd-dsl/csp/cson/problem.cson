fields:
	name:
		model:
			max: 128
			required: true
		view:
			label: 'Problem Name'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ['manage', 'reference']
	indexes:
		unique: [
			['name']
		]
	name: ['name']
	sections:
		'Main':
			fields: ['name']

view:
	comment: 'Manage > Problems'
	find:
		basic: ['name']
	grid:
		fields: ['name']
		sort: ['name']
	label: 'Problems'
