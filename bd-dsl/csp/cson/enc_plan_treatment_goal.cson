fields:
	one_time_vist_goal_list:
		model:
			multi:true
			source: ['Patient/Caregiver will demonstrate safe administration of prescribed therapy',
			'Patient/Caregiver will verbalize potential side effects and/or complications of therapy to report and appropriate action as required',
			'Patient/Caregiver will demonstrate correct care and maintenance of access device',
			'Infusion access device will remain free from infection or other complications',
			'See attached Medication Profile for additional medications' , 
			'No Additional OTC Medications or Herbal/Home Remedies'
			]
			if:
				'Patient/Caregiver will demonstrate correct care and maintenance of access device':
					fields: ['correct_care_device_list']
				'Infusion access device will remain free from infection or other complications':
					fields: ['one_time_vist_goal_list_other']
				# 'Other':
				# 	fields: ['one_time_vist_goal_list_other']
			required: false
		view:
			label: 'List of Goals'
			control:'checkbox'
			columns:3

	correct_care_device_list:
		model:
			source: ['IV', 'Subcutaneous', 'Other']
			if:
				'Other':
					fields: ['correct_care_device_other']
		view:
			label: 'Correct Care Device List'
			control:'radio'
			columns:3

	correct_care_device_other:
		model:
			required: true
		view:
			label: 'Correct Care Device List Other'
			columns:3

	one_time_vist_goal_list_other:
		model:
			required: true
		view:
			label: 'Other'
			columns:3

# prefill this form might be prefilled from encounter
	premed:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': ['Active', 'Pending']
		view:
			embed:
				form: 'patient_medication'
				selectable: false
				add_preset:
					reported_by: 'Nurse'
					status: 'Active'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				rank: 'none'
				fields: ['fdb_id', 'medication_dose', 'medication_frequency', 'start_date']
				label: ['Medication', 'Dose', 'Frequency', 'Start Dt']
				width: [35, 25, 25, 15]
			label: 'Pre-Medications'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true


model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin','liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin','csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']
	sections:
		'Goals':
			fields: ['one_time_vist_goal_list', 'correct_care_device_list', 'correct_care_device_other',
			'one_time_vist_goal_list_other'
			]
		'Active Medications':
			hide_header: true
			indent: true
			fields: ['premed']
	name: ['created_on']
view:
	comment: ''
	grid:
		fields: ['one_time_vist_goal_list']
	label: 'Plan Of Treatment One Time Visit Goals'

