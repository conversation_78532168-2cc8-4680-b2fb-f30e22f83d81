fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	access:
		model:
			required: true
			source: ['Peripheral', 'Port', 'PICC', 'Other']
			if:
				'Other':
					fields: ['access_other']
		view:
			control: 'radio'
			label: "Access"

	access_other:
		model:
			required: true
		view:
			label: "Access Other"

	ordering_factor:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['drug_brand', 'doses', 'units']
		view:
			control: 'radio'
			label: "Ordering Factor?"

	drug_brand:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Factor Drug Brand'

	doses:
		model:
			type: 'int'
			rounding: 1
		view:
			label: 'Doses'

	units:
		model:
			type: 'int'
			rounding: 1
		view:
			label: 'Units'
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'csr', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'csr', 'nurse', 'pharm']
	bundle: ['patient']
	name: '{access}'
	indexes:
		many: [
			['patient_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
	sections:
		'Factor Order Details':
			fields: ['access', 'access_other', 'ordering_factor', 'drug_brand', 'doses', 'units']
view:
	comment: 'Patient > Orders > Factor'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Patient Orders: Factor'
	open: 'read'
