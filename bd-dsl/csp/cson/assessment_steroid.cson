fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'


	gender:
		model:
			prefill: ['patient']
			if:
				'Female':
					fields: ['is_pregnant', 'will_be_pregnant']
		view:
			offscreen: true
			readonly: true

	first_time:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is this the first time you are receiving Steroid therapy?'
			offscreen: true
			readonly: true

	last_therapy_date:
		model:
			type: 'date'
		view:
			label: 'When did you receive this therapy last?'
			offscreen: true
			readonly: true

	last_therapy_duration:
		model:
			rounding: 1
			type: 'decimal'
		view:
			label: 'Last treatment duration'
			offscreen: true
			readonly: true

	last_therapy_duration_type:
		model:
			source: ["Days", "Weeks", "Months"]
			default: "Months"
		view:
			label: 'Last treatment duration type'
			control: 'radio'
			offscreen: true
			readonly: true

	last_therapy_response:
		model:
			max: 12
			min: 1
			source: ['Excellent','Good','Fair','Poor']
		view:
			control: 'radio'
			label: 'What was the response to the treatment?'
			offscreen: true
			readonly: true

	had_adr:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['adr_type', 'reaction_treatment']
		view:
			control: 'radio'
			label: 'Did you experience any adverse drug reactions (ADRs)?'
			offscreen: true
			readonly: true

	adr_type:
		model:
			max: 128
			multi: true
			source: 'problem'
			if:
				'Other':
					fields: ['adr_other']
				'Pain scale (1 to 10)':
					fields: ['pain_scale']
		view:
			control: 'select'
			label: 'Type of reactions'
			offscreen: true
			readonly: true

	pain_scale:
		model:
			max: 1
			min: 1
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Pain Scale'
			offscreen: true
			readonly: true

	adr_other:
		model:
			max: 128
		view:
			label: 'Describe other reactions'
			offscreen: true
			readonly: true

	reaction_treatment:
		model:
			max: 64
			min: 1
			source: ['Medication Change', 'Medication dose or frequency change', 'Therapy discountined', 'Other']
			if:
				'Other':
					fields: ['reaction_treatment_other']
		view:
			control: 'radio'
			label: 'What was done about the reactions?'
			offscreen: true
			readonly: true

	reaction_treatment_other:
		model:
			max: 128
		view:
			label: 'Describe what was done about the reaction'
			offscreen: true
			readonly: true

	# Assessment Questions

	immuno_therapy:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Is the patient currently taking or plan to start immunosuppressant therapy?"
			columns: 2

	have_fever:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Does patient report recent fevers?"
			columns: 2

	have_infections:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Has the patient had any chronic or recent infections?"
			columns: 2

	have_tb_exposure:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Has the patient had any recent exposure to tuberculosis (TB), HBV, or mycoses?"
			columns: 2

	have_measles_exposure:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Has the patient had any recent reports of Measles, Shingles, or Varicella exposure?"
			columns: 2

	had_seizure:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Does patient report any history of seizures?"
			columns: 2

	is_pregnant:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Are you currently pregnant, or breast feeding?"
			columns: 2

	will_be_pregnant:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Do you plan to get pregnant while on this medication?"
			columns: 2

	# Hepatic Disease Risk
	hepatic_disease:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with Hepatic disease?'
			columns: 2

	# Renal Disease Risk
	renal_disease:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with Renal (kidney) disease?'
			columns: 2

	has_diabetes:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with diabetes?'
			columns: 2

	meds_taking:
		model:
			max: 32
			min: 1
			multi: true
			source:['Acyclovir','Aminoglycosides','Amphotericin','Atripla','Cisplatin','Diuretics (loops, thiazides)','NSAIDS',
				'Prograf','Proton-Pump Inhibitors','Tenofovir','Viread','Truvada','Other','None']
			if:
				'Other':
					fields: ['meds_taking_other']
		view:
			control: 'checkbox'
			label: 'Are you currently taking any of the following (concomitant nephrotoxic) drugs?'
			columns: 2

	meds_taking_other:
		view:
			label: 'Other Drugs'
			columns: 2

	# Thromboembolic Risk
	has_htn:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pressure_controlled']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with hypertension (HTN)?'
			columns: 2

	pressure_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your blood pressure currently controlled?'
			columns: 2.1

	high_chol:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['chol_controlled']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with high cholesterol?'
			columns: 2

	chol_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your cholesterol currently controlled?'
			columns: 2.1

	heart_cond:
		model:
			max: 64
			min: 1
			multi: true
			source: ['Congestive heart failure (CHF)', 'Cardiomyopathy', 'Valve Disease', 'Congenital Defects', 'Atrial Fibrillation', 'Angina', 'Pulmonary disease', 'Other', 'None']
			if:
				'Other':
					fields: ['heart_cond_other']
		view:
			control: 'checkbox'
			label: 'Do you suffer from any of the following heart diseases?'
			columns: 2

	heart_cond_other:
		view:
			control: 'area'
			label: 'Other Heart Diseases'
			columns: 2.1

	immobil_periods:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you experience prolonged periods of immobilization?'
			columns: 2

	diag_thomb:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with thrombophilia?'
			columns: 2

	clam_hands:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do your hands/extremities feel cold and clammy (experience low BP)?'
			columns: 2

	had_sepsis:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any recent cases of sepsis or infection in blood?'
			columns: 2

	# Support Questions
	has_ems:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Does patient have access to EMS (w/in 15 minutes)?"
			columns: 2

	nurse_present:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will nurse be present for the entire infusion?"
			columns: 2

	adult_present:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will an additional adult be present for the entire infusion?"
			columns: 2

	adult_present_after:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will an additional adult be present for 2 to 3 hours after infusion completed?"
			columns: 2

	physician_available:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will the ordering physician be available by phone during the infusion?"
			columns: 2

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Steroid General Assessment':
			indent: false
			tab: 'Steroid General'
			fields: ['gender', 'immuno_therapy', 'have_fever', 'have_infections', 'have_tb_exposure', 'have_measles_exposure', 'had_seizure', 'is_pregnant', 'will_be_pregnant', 'has_ems', 'nurse_present', 'adult_present', 'adult_present_after', 'physician_available']
		'Steroid Hepatic Risk Assessment':
			indent: false
			tab: 'Steroid Hepatic Risk'
			fields: ['hepatic_disease']
		'Steroid Renal Risk Assessment':
			indent: false
			tab: 'Steroid Renal Risk'
			fields: ['renal_disease', 'has_diabetes', 'meds_taking', 'meds_taking_other']
		'Steroid Thromboembolic Risk Assessment':
			indent: false
			tab: 'Steroid Thromboembolic Risk'
			fields: ['has_htn', 'pressure_controlled', 'high_chol', 'chol_controlled', 'heart_cond', 'heart_cond_other', 'immobil_periods', 'diag_thomb', 'clam_hands', 'had_sepsis']
view:
	comment: 'Patient > Careplan > Assessment > Steroid'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Steroid'
	open: 'read'
