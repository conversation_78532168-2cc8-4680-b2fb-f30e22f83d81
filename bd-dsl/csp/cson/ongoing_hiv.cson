
fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'


	patient_had_liver_issues:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Direct patient to call doctor immediately'
		view:
			control: 'radio'
			label: 'Have you had any yellowing of eyes/skin, abdominal pain/swelling, or abnormal itching?'

	patient_lactic_acidosis:
		model:
			multi:true
			source: ['Muscle pain or weakness', 'Numb or cold feeling in your arms and legs',
			'Trouble breathing', 'Stomach pain', 'Nausea with vomiting', 'Fast or uneven heart rate',
			'Dizziness', 'Feeling very weak or tired', 'None']
			if:
				'Muscle pain or weakness':
					note: 'Direct patient to call doctor immediately'
				'Numb or cold feeling in your arms and legs':
					note: 'Direct patient to call doctor immediately'
				'Trouble breathing':
					note: 'Direct patient to call doctor immediately'
				'Stomach pain':
					note: 'Direct patient to call doctor immediately'
				'Nausea with vomiting':
					note: 'Direct patient to call doctor immediately'
				'Fast or uneven heart rate':
					note: 'Direct patient to call doctor immediately'
				'Dizziness':
					note: 'Direct patient to call doctor immediately'
				'Feeling very weak or tired':
					note: 'Direct patient to call doctor immediately'
		view:
			control: 'checkbox'
			label: 'Have you had any of the following symptoms?'

	patient_pancreas_issues:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Direct patient to call doctor immediately'
		view:
			control: 'radio'
			label: 'Have you had severe pain in your upper stomach spreading to your back, nausea and vomiting, fast heart rate?'

	patient_has_infection:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had fever, night sweats, swollen glands, mouth sores, diarrhea, stomach pain, weight loss?'

	patient_has_chest_pain:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had chest pain (especially when you breathe), dry cough, wheezing, feeling short of breath?'

	patient_has_cold_sores:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had cold sores, sores on your genital or anal area?'

	patient_has_heart_issues:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had rapid heart rate, feeling anxious or irritable, weakness or prickly feeling, problems with balance or eye movement?'

	patient_has_speaking_issues:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had trouble speaking or swallowing, severe lower back pain, loss of bladder or bowel control?'

	patient_has_swelling:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had swelling in your neck or throat (enlarged thyroid), menstrual changes, impotence, loss of interest in sex?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	sections:
		'Ongoing HIV - Lab':
			fields: ['patient_had_liver_issues', 'patient_lactic_acidosis', 'patient_pancreas_issues',
			'patient_has_infection', 'patient_has_chest_pain', 'patient_has_cold_sores',
			'patient_has_heart_issues', 'patient_has_speaking_issues', 'patient_has_swelling']


view:
	comment: 'Patient > Careplan > Ongoing > HIV'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Ongoing Assessment: HIV'
	open: 'read'
