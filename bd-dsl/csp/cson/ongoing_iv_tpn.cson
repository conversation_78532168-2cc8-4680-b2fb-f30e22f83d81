fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Demographics
	gender:
		model:
			prefill: ['patient']
			if:
				'Female':
					fields: ['is_pregnant']
		view:
			offscreen: true
			readonly: true
			label: 'Sex'
			columns:3

	pmh:
		model:
			prefill: ['ongoing_iv_tpn']
		view:
			control: 'area'
			label: 'Past Medical History'
			columns:3.1


	height:
		model:
			max: 250
			min: 15
			required: true
			rounding: 0.01
			type: 'decimal'
			prefill: ['patient_measurement_log']
		view:
			class: 'unit'
			label: 'Height (cm)'
			note: 'E.g.: 143, 143cm, 1.43m, 56in, 56", 4\' 8", 4 8'
			transform: [
					name: 'HeightTransform'
			]
			columns:3

	weight:
		model:
			max: 1000
			min: 1
			required: true
			rounding: 0.01
			type: 'decimal'
			prefill: ['patient_measurement_log']
		view:
			class: 'unit'
			label: 'Weight (Kg)'
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]
			columns:3

	weight_goal:
		model:
			max: 1000
			min: 1
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'unit'
			label: "What is the patient's goal or usual healthy weight (Kg)?"
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]
			columns:3

	pediatric:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			prefill: ['ongoing_iv_tpn']
			if:
				'Yes':
					fields: ['wt_50', 'wt_pt', 'ht_pt', 'complete_adl']
				'No':
					fields: ['ibw', 'ibw_percentage', 'abw']
		view:
			control: 'radio'
			label: "Pediatric patient?"
			columns:3

	wt_50:
		model:
			max: 1000
			min: 1
			rounding: 0.01
			type: 'decimal'
			prefill: ['ongoing_iv_tpn']
		view:
			class: 'unit'
			label: 'Weight at 50%ile'
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]
			columns:3

	wt_pt:
		model:
			max: 100
			min: 1
			rounding: 0.01
			type: 'decimal'
			prefill: ['ongoing_iv_tpn']
		view:
			label: 'Weight %ile'
			columns:3

	ht_pt:
		model:
			max: 100
			min: 1
			rounding: 0.01
			type: 'decimal'
			prefill: ['ongoing_iv_tpn']
		view:
			label: 'Height %ile'
			columns:3

	ibw:
		model:
			max: 1000
			min: 1
			rounding: 0.01
			type: 'decimal'
			prefill: ['ongoing_iv_tpn']
		view:
			class: 'unit'
			label: 'IBW'
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]
			columns:3

	ibw_percentage:
		model:
			max: 100
			min: 1
			rounding: 0.01
			type: 'decimal'
			prefill: ['ongoing_iv_tpn']
		view:
			label: '%IBW'
			columns:3

	abw:
		model:
			max: 1000
			min: 1
			rounding: 0.01
			type: 'decimal'
			prefill: ['ongoing_iv_tpn']
		view:
			class: 'unit'
			label: 'Adjusted BW'
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]
			columns:3

	is_pregnant:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			prefill: ['ongoing_iv_tpn']
			if:
				'Yes':
					fields: ['prepreg_weight']
		view:
			control: 'radio'
			label: "Currently pregnant?"
			columns:3

	prepreg_weight:
		model:
			max: 1000
			min: 1
			required: true
			rounding: 0.01
			type: 'decimal'
			prefill: ['ongoing_iv_tpn']
		view:
			class: 'unit'
			label: 'Pre-Pregnancy Weight'
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]
			columns:3

	type:
		model:
			source: ['Initial Follow Up', 'Ongoing Follow Up', 'Home Start Evaluation', 'Nutrition Screen']
		view:
			control : 'radio'
			label: 'Assessment Type'
			columns:3

	goal_calorie:
		model:
			max: 20000
			min: 1
			type: 'decimal'
			prefill: ['ongoing_iv_tpn']
			required: true
		view:
			label: 'Calorie Goal (kcal)'
			columns:3

	goal_protein:
		model:
			max: 20000
			min: 1
			type: 'decimal'
			required: true
		view:
			label: 'Protein Goal (gm/kg)'
			columns:3

	goal_fluid:
		model:
			max: 20000
			min: 1
			type: 'decimal'
			required: true
		view:
			label: 'Fluid Goal (mL)'
			columns:3

	needs_met:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Meeting estimated needs?'
			columns:3

	tolerating:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Tolerating current TPN formulation?'
			columns:3

	nutr_meds:
		view:
			control : 'area'
			label: 'Nutrition Related Meds'
			columns:3

	recommend:
		view:
			control: 'area'
			label: 'Progress Notes/Recommendations'
			columns:3

	any_drains:
		model:
			max: 3
			min: 1
			source: ['PleureX', 'Jackson-Pratt(JP)', 'Chest/abdominal tube', 'Other']
			if:
				'Other':
					fields: ['any_drains_other']
		view:
			control: 'radio'
			label: 'Any type of drains?'
			columns:3

	any_drains_other:
		model:
			max: 128
		view:
			label: 'Drain Type Other'
			columns:3

	needs_change_lab:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['needs_change_lab_details']
		view:
			control: 'radio'
			label: 'Are changes to electrolytes, trace elements, vitamin, amino acid, fat, and glucose required based on recent labs and patient assessment?'
			columns:3

	needs_change_lab_details:
		view:
			control: 'area'
			label: 'Change details'
			columns:3

	pt_met_goals:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient meeting nutritional goals, oral intake, fluid balance (input/output) and weight goal set by the ordering physician?'
			columns:3

	pt_compliant:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient tolerating TPN therapy and compliant with prescribed regimen?'
			columns:3

	pt_rpt_adr:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient reporting any side effects to prescribed therapy?'
			columns:3.1

	pt_rpt_sym:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pt_rpt_sym_details', 'pt_rpt_sym_episodes']
		view:
			control: 'radio'
			label: 'Is patient reporting gastrointestinal symptoms, such as nausea, vomiting, bloating, pain, diarrhea, or symptoms of bowel obstruction?'
			columns:3

	pt_rpt_sym_details:
		model:
			required: true
		view:
			label: 'Symptoms reported'
			columns:3

	pt_rpt_sym_episodes:
		model:
			max: 100
			min: 1
			type: 'decimal'
			required: true
		view:
			label: 'Number of episodes per day'
			columns:3

	pt_rpt_dia:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pt_rpt_dia_episodes']
		view:
			control: 'radio'
			label: 'Is patient reporting having diarrhea?'
			columns:3

	pt_rpt_dia_episodes:
		model:
			max: 100
			min: 1
			type: 'decimal'
			required: true
		view:
			label: 'Number of episodes per day'
			columns:3

	pt_rpt_bwl_move:
		model:
			max: 10
			min: 1
			type: 'decimal'
		view:
			note: '(x per day/week)'
			label: 'Bowel movement Frequency'
			columns:3

	pt_rpt_bwl_move_freq:
		model:
			max: 4
			min: 1
			source: ['Day','Week']
		view:
			control: 'radio'
			label: 'Bowel movement Interval'
			columns:3

	pt_rpt_urine:
		model:
			max: 32
			min: 1
			source: ['Clear','Cloudy','Strong Odor']
		view:
			control: 'radio'
			label: 'Urine Color'
			columns:3

	pt_rpt_urine_freq_day:
		model:
			max: 100
			min: 1
			type: 'decimal'
		view:
			note: '(x times per day)'
			label: 'Urination Frequency (daytime)'
			columns:3

	pt_rpt_urine_freq_night:
		model:
			max: 100
			min: 1
			type: 'decimal'
		view:
			note: '(x times per night)'
			label: 'Urination Frequency (nighttime)'
			columns:3

	pt_rpt_cath_prob:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pt_rpt_cath_prob_list']
		view:
			control: 'radio'
			note: 'e.g. blood clots, line patency, pain, fever, or signs/symptoms of infection'
			label: 'Is patient reporting catheter related complications?'
			columns:3

	pt_rpt_cath_prob_list:
		model:
			multi: true
			source: ['Blood Clots', 'Line Patency', 'Pain', 'Fever', 'Infection']
		view:
			control: 'checkbox'
			note: 'Select all that apply'
			label: 'Reported catheter related complications'
			columns:3

	complete_adl:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Was patient able to complete age appropriate ADL on their own?'
			columns:3

	require_phys_note:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['require_phys_note_details']
		view:
			control: 'radio'
			label: 'After review of recent Nursing Visit Documentation, has pharmacist identified any issues that require physician notification?'
			columns:3

	require_phys_note_details:
		view:
			control: 'area'
			label: 'Issues that require physician notification'
			columns:3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		ongoing_iv_tpn:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'

		patient_measurement_log:
			link:
				id: 'patient_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'TPN H&P':
			prefill: 'ongoing_iv_tpn'
			fields: ['gender', 'pmh', 'height', 'weight', 'weight_goal', 'pediatric', 'wt_50', 'wt_pt', 'ht_pt', 'ibw', 'ibw_percentage', 'abw', 'is_pregnant', 'prepreg_weight']
		'TPN Nutritional Report':
			prefill: 'ongoing_iv_tpn'
			fields: ['type', 'goal_calorie', 'goal_protein', 'goal_fluid', 'needs_met', 'tolerating', 'nutr_meds', 'recommend']
		'TPN Treatment Condition':
			fields: ['any_drains', 'any_drains_other']
		'TPN Assessment':
			fields: ['needs_change_lab', 'needs_change_lab_details', 'pt_met_goals', 'pt_compliant', 'pt_rpt_adr', 'pt_rpt_sym', 'pt_rpt_sym_details', 'pt_rpt_sym_episodes',
						'pt_rpt_dia', 'pt_rpt_dia_episodes', 'pt_rpt_bwl_move', 'pt_rpt_bwl_move_freq', 'pt_rpt_urine',
						'pt_rpt_urine_freq_day', 'pt_rpt_urine_freq_night', 'pt_rpt_cath_prob', 'pt_rpt_cath_prob_list', 'complete_adl', 'require_phys_note', 'require_phys_note_details']

view:
	comment: 'Patient > Careplan > Ongoing > TPN'
	grid:
		fields: ['goal_calorie', 'goal_protein', 'goal_fluid', 'needs_met']
	label: 'Ongoing Assessment: TPN'
	open: 'read'
