fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	# Problem Details
	date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Problem Date'

	problem:
		model:
			required: true
			source: ['Knowledge deficit of disease process',
					'Knowledge deficit of medication therapy',
					'Health Maintenance',
					'Pt unable to infuse own therapy',
					'Pt/CG unable to initiate IV access for therapy',
					'Pt unable to perform collection of blood specimen',
					'Pain management',
					'Other']
			if:
				'Other':
					fields:['problem_other']
		view:
			label: 'Problem'
			control: 'select'

	problem_other:
		model:
			required: true
		view:
			label: 'Problem (Other)'

	goal:
		model:
			required: true
			source: ['Pt/cg will understand disease process',
					'Pt/cg will understand therapy as it relates to disease process',
					'Pt will show improvement and stabilization of health with therapy',
					'Pt will receive medication as prescribed without adverse effects',
					'Pt will be comfortable',
					"Pt's pain scale will be monitored during course of therapy",
					'Other']
			if:
				'Pt will show improvement and stabilization of health with therapy':
					fields:['improve_evidence']
				"Pt's pain scale will be monitored during course of therapy":
					fields:['pain_scale_target']
				'Other':
					fields:['goal_other']
		view:
			label: 'Goal'
			control: 'select'

	goal_other:
		model:
			required: true
		view:
			label: 'Goal (Other)'

	improve_evidence:
		model:
			required: true
		view:
			label: "Evidence of patient's improvement"

	pain_scale_target:
		model:
			required: true
		view:
			label: "Patient's target pain scale"

	task:
		model:
			required: true
			source: ['Pre-medicate patient prior to therapy as ordered',
					'SN to perform lab draw',
					'Skilled nurse to administer therapy as ordered',
					'Skilled nurse to initiate/maintain IV access',
					'Instruct use of pain meds PRN as directed by physician',
					'Instruct pt on compliance with keeping daily log of pain assessment',
					'Instruct pt on disease process and potential adverse reactions',
					'Other']
			if:
				'SN to perform lab draw':
					fields:['lab_draw']
				'Other':
					fields:['task_other']
		view:
			label: 'Task'
			control: 'select'

	task_other:
		model:
			required: true
		view:
			label: 'Task (Other)'

	lab_draw:
		model:
			required: true
		view:
			label: 'Lab Draw Details'

	# Updates
	date_updated:
		model:
			type: 'date'
		view:
			label: 'Date Updated'

	date_resolved:
		model:
			type: 'date'
		view:
			label: 'Date Resolved'

	comment:
		model:
			max: 4096
		view:
			control: 'area'
			label: "Problem Comment"


	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient',]
	name: ['patient_id', 'created_on']
	indexes:
		many: [
			['patient_id']
		]
	sections:
		'Problem Details':
			fields: ['date', 'problem', 'problem_other', 'goal', 'goal_other', 'improve_evidence', 'pain_scale_target', 'task', 'task_other', 'lab_draw']
		'Status Updates':
			fields: ['date_updated', 'date_resolved']
		'Problem Comment':
			fields: ['comment']

view:
	comment: 'Patient >  Care Plan > Problem'
	grid:
		fields: ['date', 'problem',  'date_updated', 'date_resolved']
	label: 'Patient Care Plan Problem'
