fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	pml_symptoms:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Withhold Tysabri dosing and perform appropriate diagnostic evaluation at the first sign or symptom suggestive of PML'
		view:
			control: 'radio'
			label: 'Have you had any progressive weakness on one side of the body or clumsiness of limbs, disturbance of vision, and changes in thinking, memory, and orientation leading to confusion and personality changes?'

	meningitis_symptoms:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Withhold Tysabri if patient has herpes encephalitis or meningitis'
		view:
			control: 'radio'
			label: 'Have you had any of the following symptoms: sudden high fever, stiff neck, abnormal severe headache, confusion, seizures, skin rash, sensitivity to light, or no appetite or thirst?'

	arn_symptoms:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Patient should be referred for retinal screening for Acute retinal necrosis ARN'
		view:
			control: 'radio'
			label: 'Have you had any eye problems, including decreased visual acuity, redness, or eye pain?'

	liver_symptoms:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Withhold Tysabri dosing and patient should be referred for lab work to monitor for signs of liver injury'
		view:
			control: 'radio'
			label: 'Have you had any of the following symptoms: severe abdominal pain or tenderness, blood in stool or tarry black stool, yellowing of the skin or whites of the eyes, or itchiness?'

	exacerbations_last_month:
		model:
			type: 'int'
			required: true
		view:
			label: 'Number of exacerbations last month'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		ongoing_tysabri:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Tysabri Followup':
			fields: ['pml_symptoms', 'meningitis_symptoms', 'arn_symptoms', 'liver_symptoms', 'exacerbations_last_month']

view:
	comment: 'Patient > Careplan > Ongoing > Tysabri'
	grid:
		fields: ['created_by','created_on']
	label: 'Ongoing Assessment: Tysabri'
	open: 'read'
