fields:
	patient_id:
		model:
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'

	user_id:
		model:
			source: 'user'
			type: 'int'
		view:
			label: 'User'

	status:
		model:
			if:
				'undelivered':
					fields: ['resend_sms']
		view:
			label: 'SMS Status'
			readonly: true

	resend_sms:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Resend SMS'

	message_id:
		view:
			label: 'Message ID'
			readonly: true

	phone_number:
		view:
			label: 'Phone #'
			readonly: true

	content:
		view:
			label: 'Content'
			readonly: true

	tag:
		view:
			label: 'Tag'
			readonly: true

	event_time:
		model:
			type: 'datetime'
		view:
			label: 'Event Time'
			readonly: true

	event_type:
		view:
			label: 'Event Type'
			offscreen: true
			readonly: true

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['audit']
	name: ['patient_id', 'created_on']
	sections:
		'SMS History':
					fields: ['patient_id', 'user_id', 'phone_number', 'message_id', 'content', 'event_type', 'event_time', 'status', 'resend_sms']

view:
	comment: 'SMS History'
	find:
		basic: ['patient_id', 'phone_number', 'message_id', 'event_type']
		advanced: ['created_by', 'updated_by', 'event_time', 'tag']
	grid:
		fields: ['created_on', 'patient_id', 'message_id', 'status', 'event_type']
		sort: ['-created_on']
	label: 'SMS History'