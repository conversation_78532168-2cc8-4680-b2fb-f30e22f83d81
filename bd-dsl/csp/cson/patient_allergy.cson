fields:

	life_threatening:
		model:
			max: 3
			min: 1
			required: true
			default: 'Unknown'
			source: ['No', 'Yes', 'Unknown']
		view:
			columns: 4
			control: 'radio'
			label: 'Is the allergy life threatening?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	active:
		model:
			required: true
			default: 'Yes'
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['end_date']
		view:
			columns: 4
			control: 'radio'
			label: 'Active?'
			findfilter: 'Yes'

	start_date:
		model:
			type: 'date'
		view:
			columns: 4
			note: 'Approximate date that allergy was first noted by the patient'
			label: 'Start Date'

	end_date:
		model:
			type: 'date'
		view:
			columns: 4
			note: 'Approximate date that allergy subsided for the patient'
			label: 'End Date'

	reaction_id:
		model:
			source: 'list_reaction'
			multi: true
		view:
			label: 'Reaction(s)'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician', 'payor']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'payor']
		request:    []
		review:     ['admin','liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin','csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
			['allergen_id', 'start_date', 'end_date']
		]
		unique: [
			['patient_id', 'allergen_id', 'start_date']
		]
	sections:
		'Allergen':
			fields: ['allergen_id', 'severity_id','reaction_id','active','life_threatening','start_date', 'end_date', 'note']
	name: '{allergen_id}'

view:
	hide_cardmenu: true
	validate: [
		{
			name: "DateOrderValidator"
			fields: [
				"start_date",
				"end_date"
			]
			error: "End Date cannot be lesser than Start Date"
		}
	]
	comment: 'Patient > Allergies'
	find:
		basic: ['allergen_id', 'active']
	grid:
		fields: ['created_on', 'allergen_id', 'reaction_id', 'severity_id', 'start_date', 'end_date', 'life_threatening', 'reviewed_by', 'reviewed_on']
		sort: ['-start_date', '-end_date', 'allergen_id']
	label: 'Patient Allergies'
	open: 'read'
