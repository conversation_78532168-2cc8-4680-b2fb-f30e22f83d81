fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	pt_mdraw_particiates:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'No'
			if:
				'Yes':
					sections: ['M-DRAW Baseline', 'Adherence Risk', 'Data Collection Sheet']
		view:
			control : 'radio'
			label: 'Will a M-DRAW be completed during this assessment?'

	unsure_when_take_meds:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['verify_understanding']
			required: true
		view:
			label: 'Do you feel unsure about how/when to take your medications?'
			control: 'radio'

	verify_understanding:
		model:
			source: ['Completed', 'Incomplete']
			if:
				'Incomplete':
					fields: ['verify_understanding_incomp']
			required: true
		view:
			label: 'Verify adherence and correct understanding of regimen; Identify discrepancies; add to their knowledge.'
			control: 'radio'

	verify_understanding_incomp:
		model:
			required: true
		view:
			control: 'area'
			label: 'Barriers to completion'

	difficulty_getting_meds:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['difficulty_getting_to_pharmacy', 'difficulty_paying_for_med', 'forget_refills']
			required: true
		view:
			label: 'Do you have any difficulty getting your medications on time from the pharmacy?'
			control: 'radio'

	difficulty_getting_to_pharmacy:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['suggest_mailorder']
			required: true
		view:
			label: 'Is it difficult for you to get to the pharmacy to pick up your medications?'
			control: 'radio'

	suggest_mailorder:
		model:
			source: ['Completed', 'Incomplete']
			if:
				'Incomplete':
					fields: ['suggest_mailorder_incomp']
			required: true
		view:
			label: 'Suggest mail order or delivery option; Assist with set-up'
			control: 'radio'

	suggest_mailorder_incomp:
		model:
			required: true
		view:
			control: 'area'
			label: 'Barriers to completion'

	difficulty_paying_for_med:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'See cost reduction strategies'
					sections: ['Suggested Actions and Guides']
					fields: ['cost_reduction_strategies']
			required: true
		view:
			label: 'Is paying for your medications a burden on your finances?'
			control: 'radio'

	forget_refills:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['suggest_auto_refill']
			required: true
		view:
			label: 'Do you forget to place refill requests on time?'
			control: 'radio'

	suggest_auto_refill:
		model:
			source: ['Completed', 'Incomplete']
			if:
				'Incomplete':
					fields: ['suggest_auto_refill_incomp']
			required: true
		view:
			label: 'Suggest automatic refill or refill synchronization; Assist with set-up'
			control: 'radio'

	suggest_auto_refill_incomp:
		model:
			required: true
		view:
			control: 'area'
			label: 'Barriers to completion'

	difficult_tracking_schedule:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'See Medication burden reduction strategies and Adherence aid methods '
					sections: ['Suggested Actions and Guides']
					fields: ['adherence_aid_methods', 'med_burden_strategies']
			required: true
		view:
			note: 'e.g., when to take each medication'
			label: 'Do you have difficulty keeping track of all your medication schedules throughout the day?'
			control: 'radio'

	adr_interferes_adherence:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'See Symptom management strategies'
					sections: ['Suggested Actions and Guides']
					fields: ['explore_adr_management', 'sym_management_strategies']
			required: true
		view:
			label: 'Do your medications give you side effects that make you NOT want to take it?'
			control: 'radio'

	explore_adr_management:
		model:
			source: ['Completed', 'Incomplete']
			if:
				'Incomplete':
					fields: ['explore_adr_management_incomp']
			required: true
		view:
			label: 'Explore ways to manage side effects or switch to more tolerable alternative'
			control: 'radio'

	explore_adr_management_incomp:
		model:
			required: true
		view:
			control: 'area'
			label: 'Barriers to completion'

	worries_about_interactions:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['create_interactions_list']
			required: true
		view:
			label: 'Do you worry about what foods or other medications might interact with your medication?'
			control: 'radio'

	create_interactions_list:
		model:
			source: ['Completed', 'Incomplete']
			if:
				'Incomplete':
					fields: ['create_interactions_list_incomp']
			required: true
		view:
			label: 'Assist with creating a list of foods/medications that can cause major interaction with the current regimen'
			control: 'radio'

	create_interactions_list_incomp:
		model:
			required: true
		view:
			control: 'area'
			label: 'Barriers to completion'

	self_adjust_rx:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'See Patient-centered medication review and Motivational interviewing'
					sections: ['Suggested Actions and Guides']
					fields: ['medication_review', 'motivation_interview']
			required: true
		view:
			label: 'Do you feel that you can take more or less of your medication than the prescribed dose to fit your lifestyle?'
			control: 'radio'

	feels_lack_benefits:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'See Patient-centered medication review and Motivational interviewing'
					sections: ['Suggested Actions and Guides']
					fields: ['medication_review', 'motivation_interview']
			required: true
		view:
			label: 'Do you feel like you don’t get any benefits from taking your medication?'
			control: 'radio'

	uncomfortable_taking_meds_while_out:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['explore_alternative_route']
			required: true
		view:
			label: 'Do you feel uncomfortable about taking your medication while you are out with family and friends?'
			control: 'radio'

	explore_alternative_route:
		model:
			source: ['Completed', 'Incomplete']
			if:
				'Incomplete':
					fields: ['explore_alternative_route_incomp']
			required: true
		view:
			label: 'Explore alternative formulations or route of administration that would be less interfering with patient’s lifestyle'
			control: 'radio'

	explore_alternative_route_incomp:
		model:
			required: true
		view:
			control: 'area'
			label: 'Barriers to completion'

	feels_med_burden_life:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'See Motivational interviewing and Additional referrals'
					sections: ['Suggested Actions and Guides']
					fields: ['add_referals', 'motivation_interview']
			required: true
		view:
			label: 'Do you consider it a burden that you have to take your medications for the rest of your life?'
			control: 'radio'

	doubts_need_treatment:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'See Motivational interviewing and Additional referrals'
					sections: ['Suggested Actions and Guides']
					fields: ['add_referals', 'motivation_interview']
			required: true
		view:
			label: 'Do you have doubts about whether your health condition needs to be treated?'
			control: 'radio'

	doubts_rx_improves_health:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'See Motivational interviewing and Additional referrals'
					sections: ['Suggested Actions and Guides']
					fields: ['add_referals', 'motivation_interview']
			required: true
		view:
			label: 'Do you have doubts if taking your medication will improve your health condition in the long term?'
			control: 'radio'

	feels_treatment_inadequate:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'See Patient-centered medication review and Motivational interviewing'
					sections: ['Suggested Actions and Guides']
					fields: ['medication_review', 'motivation_interview']
			required: true
		view:
			label: 'Do you feel that you are NOT receiving the best possible treatment available from your health care provider?'
			control: 'radio'

	anxiety_self_injection:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['review_managing_self_injection']
			required: true
		view:
			label: 'I experience anxiety with injecting myself with the medication'
			control: 'radio'

	review_managing_self_injection:
		model:
			source: ['Completed', 'Incomplete']
			if:
				'Incomplete':
					fields: ['review_managing_self_injection_incomp']
			required: true
		view:
			label: 'Review “Managing self-injection difficulties in patients with relapsing remitting MS”'
			control: 'radio'

	review_managing_self_injection_incomp:
		model:
			required: true
		view:
			control: 'area'
			label: 'Barriers to completion'

	other_doubts_concerns:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['additional_barriers', 'additional_barriers_intervention']
			required: true
		view:
			note: 'language barrier, cultural issues, social support, faith-related, etc.'
			label: 'Do you have any other doubts or concerns about taking your medication?'
			control: 'radio'

	additional_barriers:
		model:
			required: true
		view:
			control: 'area'
			label: 'Document additional barriers'

	additional_barriers_intervention:
		model:
			required: true
		view:
			control: 'area'
			label: 'Intervention given'

	cost_reduction_strategies:
		model:
			default: '-Substitute with generic options if available\n-Reduce number of medications\n-Use combination drugs when possible\n-Consider tablet splitting\n-Consider therapeutic interchange\n-NeedyMeds.com\n'
		view:
			control: 'area'
			label: 'Cost Reduction Strategies'
			readonly: true

	adherence_aid_methods:
		model:
			default: '-Reminder tools: schedule phone alarms, text message reminders, goal setting to tie medication taking to other habituated behaviors in the patient’s routine\n-Organization tools: day/time pill box, medication calendar, wallet cards\n'
		view:
			control: 'area'
			label: 'Adherence Aid Methods'
			readonly: true

	med_burden_strategies:
		model:
			default: '-Verify appropriateness of each medication the patient is taking\n-Use long-acting drugs where possible\n-Use combination drugs when possible\n'
		view:
			control: 'area'
			label: 'Medication Burden Reduction Strategies'
			readonly: true

	sym_management_strategies:
		model:
			default: '-Determine if the symptoms are consistent with side effects of medications the patient is taking\n-Evaluate if symptoms need to be treated or make a change in treatment\n'
		view:
			control: 'area'
			label: 'Symptom Management Strategies'
			readonly: true

	motivation_interview:
		model:
			default: '-Use open-ended questions to divulge patient’s concerns and motivations\n-Use reflective listening: “It sounds like you are concerned about your condition...”\n-Listen for indicators of the patient’s DESIRE, their ABILITY, their REASONS, and their NEED to make changes.\n-Listen for their COMMITMENT and TAKING STEPS to make changes.\n-When you hear these, they are motivators or actions to encourage.\n-Ask for permission to talk: “Can we talk a bit about your medications?”\n-Eliciting change: “What would you like to see different about your current situation?”\n-Ensure that having difficulties while changing is not uncommon: “Many people report feeling like you do. They want to change their ___, but find it difficult.”\n-Decisional balancing: “What are some of the good things about taking your medications? Okay, on the flipside, what are some of the less good things about taking your medications?\n-Before educating, assess patient’s baseline knowledge: “What might happen down the road if you don’t take your medications?\n-Columbo approach: “So, help me to understand, on the one hand you say you want to live to see your 12-year old daughter grow up and go to college, and yet you won’t take the medication your doctor prescribed for your diabetes. How will that help you live to see your daughter grow up?”\n-Supportive statements: “It’s clear that you’re really trying to change the way you take your medications.”\n'
		view:
			control: 'area'
			label: 'Motivational Interviewing'
			readonly: true

	medication_review:
		model:
			default: '-Evaluate the appropriateness of each medication\n-Review the medications for therapeutic efficacy\n-Walk through the purpose of each medication with the patient\n'
		view:
			control: 'area'
			label: 'Patient-centered Medication Review'
			readonly: true

	add_referals:
		model:
			default: '-Refer to support groups for patients lacking social support\n-Refer to nutritional classes to aid meal planning and nutrition education\n-Suggest mental health therapy to help with issues rooted in mental exhaustion\n'
		view:
			control: 'area'
			label: 'Additional Referrals'
			readonly: true

	assigned_group:
		model:
			source: ['UNA', 'PNA', 'ONA']
			required: false
		view:
			label: 'Assigned group based on Priming Question'
			control: 'radio'
			offscreen: true
			readonly: true

	assigned_study_group:
		model:
			source: ['Control', 'Intervention']
			required: false
		view:
			label: 'Assigned study group'
			control: 'radio'
			offscreen: true
			readonly: true

	sense_decrease_cog_func:
		model:
			source: ['No', 'Yes']
			required: false
		view:
			label: 'At any time during this interview, did you sense an issue about decreased cognitive function?'
			control: 'radio'

	adherence_risk:
		model:
			source: ['Low', 'Moderate', 'High']
			required: true
		view:
			label: 'Adherence Risk'
			control: 'radio'

	intervention_performed:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Was an intervention performed?'
			control: 'radio'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'csr', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'

	sections:
		'Participation':
			fields: ['pt_mdraw_particiates']
		'M-DRAW Baseline':
			note: 'For each question answered “sometimes” or “often”, check the box for “YES”. For each YES, consider the suggested actions for intervention'
			fields: ['unsure_when_take_meds', 'verify_understanding', 'verify_understanding_incomp', 'difficulty_getting_meds', 'difficulty_getting_to_pharmacy', 'suggest_mailorder', 'suggest_mailorder_incomp',
			'difficulty_paying_for_med', 'forget_refills', 'suggest_auto_refill', 'suggest_auto_refill_incomp', 'difficult_tracking_schedule', 'adr_interferes_adherence', 'explore_adr_management', 'explore_adr_management_incomp', 'worries_about_interactions', 'create_interactions_list', 'create_interactions_list_incomp', 'self_adjust_rx', 'feels_lack_benefits', 'uncomfortable_taking_meds_while_out', 'explore_alternative_route', 'explore_alternative_route_incomp', 'feels_med_burden_life', 'doubts_need_treatment', 'doubts_rx_improves_health', 'feels_treatment_inadequate', 'anxiety_self_injection', 'review_managing_self_injection', 'review_managing_self_injection_incomp', 'other_doubts_concerns', 'additional_barriers', 'additional_barriers_intervention']
		'Suggested Actions and Guides':
			fields: ['adherence_aid_methods', 'med_burden_strategies', 'sym_management_strategies','cost_reduction_strategies', 'motivation_interview', 'medication_review', 'add_referals']
		'Adherence Risk':
			fields: ['adherence_risk', 'intervention_performed']
		'Data Collection Sheet':
			fields: ['assigned_group', 'assigned_study_group', 'sense_decrease_cog_func']
view:
	comment: 'Patient > Careplan > Assessment > M-Draw Baseline'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Modified Drug Adherence Work-up Tool (M-DRAW) Baseline'
	open: 'read'
