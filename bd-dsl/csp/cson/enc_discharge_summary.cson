fields:

	soc_date:
		model:
			type: 'date'
		view:
			label: 'SOC Date'
			columns:3.1

	last_visit:
		model:
			type: 'date'
		view:
			label: 'Date of Last Visit'
			columns:3

	dc_transfer:
		model:
			type: 'date'
			required: false
		view:
			label: 'Date of - (DC/transfer)'
			columns:3


	dx_1:
		model:
			prefill: ['parent.dx_1']
			source: 'list_diagnosis'
			sourceid: 'code'
		view: 
			readonly: true
			label: 'Primary Diagnosis'

	brief_desc:
		model:
			type:'text'
		view:
			control:'area'
			label:'Brief description of care provided (include current status of patients condition, response and understanding of discharge activities. Infusion therapy provided):'
			columns:3

	icd_code_1:
		model:
			prefill: ['parent.dx_1.icd_code']
			# source: 'diagnosis'
		view: 
			readonly: true
			label: 'Primary Diagnosis ICD Code'

	dx_date_1:
		model:
			prefill: ['parent.dx_date_1']
		view: 
			readonly: true
			label: 'Primary Diagnosis Onset Date'

	dx_2:
		model:
			prefill: ['parent.dx_2']
			source: 'list_diagnosis'
			sourceid: 'code'

		view: 
			readonly: true
			label: 'Secondary Diagnosis'

	icd_code_2:
		model:
			prefill: ['parent.dx_2.icd_code']
			# source: 'diagnosis'
		view: 
			readonly: true
			label: 'Secondary Diagnosis ICD Code'

	dx_date_2:
		model:
			prefill: ['parent.dx_date_2']
		view: 
			readonly: true
			label: 'Secondary Diagnosis Onset Date'

	pt_diagnoses:
		model:
			required: false
		view:
			label: 'Patient Diagnoses'
			columns:3

	pt_directive:
		model:
			source:['Yes' , 'No']
		view:
			label:'Patient has Advance Directives'
			columns:3

	brief_care_discruption:
		model:
			required: false
		view:
			control: 'area'
			label: 'Brief description of care provided (include current status of patient condition, response and understanding of discharge
			activities. Infusion therapy provided)'

	pat_independent_status:
		model:
			source : ['Patient independent in meeting future health care needs, understands discharge instructions' , 'Patient dependent on others for support regarding health care needs']
			if:
				'Patient dependent on others for support regarding health care needs':
					fields: ['pat_dependent_status']
			required: false
		view:
			control:'checkbox'
			label: 'Patient Independent Meeting'
			columns:3

	pat_dependent_status:
		model:
			source: ['Caregiver', 'Agency/Institution']
			required: false
		view:
			control: 'checkbox'
			label: 'Patient dependent status'
			columns:3

	dis_instruction:
		model:
			source: ['Patient', 'Caregiver', 'Set Appointment', 'Follow - up with MD for any problems/concerns', 'Other']
			required: false
			if:
				'Caregiver':
					fields: ['dis_instruction_note_caregiver']
				'Set Appointment':
					fields: ['dis_appointment_with', 'dis_appointment_on']
				'Follow - up with MD for any problems/concerns':
					fields: ['dis_instruction_note_follow_up']
				'Other':
					fields: ['dis_instruction_note']
		view:
			control: 'radio'
			label: 'Discharge instructions provided to:'
			columns:3

	dis_appointment_with:
		model:
			required: false
		view:
			label: 'Appointment with'
			columns:3

	dis_appointment_on:
		model:
			type: 'date'
			required: false
		view:
			label: 'Appointment On'
			columns:3

	dis_instruction_note:
		model:
			required: true
		view:
			label: 'Discharge Instruction Note'
			columns:3

	reason_discharge_note:
		model:
			required: true
		view:
			label: 'Reason DisInvoice #te'

	dis_instruction_note_caregiver:
		model:
			required: false
		view:
			label: 'Caregiver Instruction Note'
			columns:3

	dis_instruction_note_follow_up:
		model:
			required: false
		view:
			label: 'Follwo up note'
			columns:3

	reason_discharge:
		model:
			source: ['No further Home Health Care needed', 'Admitted to hospital', 'Admitted to SN / IC facility', 'Family/friends assumed responsibility',
			'Patient moved out of area', 'Patient refused service', 'Transferred to another Nursing Provider', 'Physician request', 'Patient Expired', 'Transferred to Hospice',
			'Other (Specify)'
			]
			required: false
			if:
				'Other (Specify)':
					fields: ['reason_discharge_note']
		view:
			control: 'radio'
			label: 'Reason For Discharge'
			columns:3

	reason_discharge_note:
		model:
			required: false
		view:
			label: 'Reason for DisInvoice #te'
			columns:3

	discharge_date:
		model:
			type: 'date'
			required: false
		view:
			label: 'Date of Discharge'
			columns:3

	continues_pharmacy:
		model:
			source: ['Yes' , 'No']
			required: false
		view:
			label: 'Continues on Pharmacy services'
			control:'radio'
			columns:3

	disc_care:
		model:
			source: ['Self', 'Physician']
			required: false
		view:
			control: 'checkbox'
			label: 'Discharge to Care of'
			columns:3
		
	disc_caregiver:
		model:
			source:['Caregiver']
		view:
			label:'Caregiver'
			control:'checkbox'
			columns:3

	disc_other:
		model:
			source:['Other']
		view:
			label:'Other (Specify)'
			control:'checkbox'
			columns:3

	disc_physician:
		model:
			source: ['Physician Notified of Discharge']
			required: false
		
		view:
			control: 'checkbox'
			label: 'Physician Notified of Discharge'
			columns:3

	disc_care_note:
		model:
			required: true
		view:
			label: 'Discharge to Care of Note'
			columns:3

	goals:
		model:
			source: ['Met', 'Not Met']
			required: false
			if:
				'Not Met':
					fields: ['goals_note']
		view:
			control: 'radio'
			label: 'Goals'
			columns:3

	goals_note:
		model:
			required: false
		view:
			label: 'Goals Explanation'
			columns:3

	rn_name:
		model:
			required: false
		view:
			label: 'RN Name'
			columns:3

	rn_signature:
		model:
			type: 'json'
		view:
			control: 'esign'
			label: 'RN Signature'
			columns:3

	physician_name:
		model:
			required: false
		view:
			label: 'Physicians Name'
			columns:3

	physician_phone:
		model:
			required: false
		view:
			label: 'Physicians Phone Number'
			columns:3

	physician_fax:
		model:
			required: false
		view:
			label: 'Physician FAX Number'
			columns:3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true


model:
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	name: [ 'soc_date', 'created_on']
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin','liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin','csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']
	sections:
		'Discharge/Treatment Summary Non-Visit':
			fields: ['soc_date','last_visit', 'dc_transfer', 'pt_diagnoses','pt_directive', 'pat_independent_status' ,'pat_dependent_status','brief_desc' ,'dis_instruction' , 'dis_instruction_note','dis_instruction_note_caregiver','dis_appointment_on' ,'dis_appointment_with','dis_instruction_note_follow_up'
			,'reason_discharge', 'reason_discharge_note', 'discharge_date','continues_pharmacy','disc_physician',
			'disc_care' ,'disc_caregiver','disc_other', 'goals', 'goals_note', 'rn_name', 'rn_signature', 'physician_name', 'physician_phone',
			'physician_fax'
			]

view:
	comment: ''
	grid:
		fields: ['pt_diagnoses']
	label: 'Discharge/Transfer Summary Non-Visit'
