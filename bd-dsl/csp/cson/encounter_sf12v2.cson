fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'
			offscreen: true
			readonly: true

	last_particiates:
		model:
			max: 32
			source: ['No', 'Yes']
			prefill: ['encounter_sf12v2.pt_qol_particiates']
			if:
				'Yes':
					fields: ['last_assessment_date']
		view:
			control : 'radio'
			label: 'Will a Quality of Life assessment be completed today?'
			offscreen: true 
			readonly: true

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['encounter_sf12v2.assessment_date']
		view:
			note: 'Recommended 2x per year'
			label: 'Last Assessment Date'
			readonly: true
			columns:3

	pt_qol_particiates:
		model:
			max: 32
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Will a Quality of Life assessment be completed today?'
			findfilter: 'Yes'
			offscreen: true
			readonly: true

	# Health Improvement
	health_general:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Excellent', 'Very Good', 'Good', 'Fair', 'Poor']
		view:
			control : 'radio'
			label: 'In general, would you say your health is?'
			columns:3

	# Limitations

	limitation_moderate:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Yes, limited a lot', 'Yes, limited a little', 'No, not limited at all']
		view:
			control : 'radio'
			label: 'Moderate activities, such as moving a table, pushing a vacuum cleaner, bowling, or playing golf'
			columns:2

	limitation_climbing_many:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Yes, limited a lot', 'Yes, limited a little', 'No, not limited at all']
		view:
			control : 'radio'
			label: 'Climbing several flights of stairs'
			columns:2

	# Daily Activities (Physical)
	phys_accomplish:
		model:
			access:
				read: ['patient']
			max: 64
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control : 'radio'
			label: 'Accomplished less than you would like'
			columns:3

	phys_limited:
		model:
			access:
				read: ['patient']
			max: 64
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control : 'radio'
			label: 'Were limited in the kind of work or other activities'
			columns:3

	# Daily Activities (Emotional)
	emo_accomplish:
		model:
			access:
				read: ['patient']
			max: 3
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control : 'radio'
			label: 'Accomplished less than you would like'
			columns:3

	emo_careless:
		model:
			access:
				read: ['patient']
			max: 3
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control : 'radio'
			label: 'Did work or other activities less carefully than usual'
			columns:3

	# General
	gen_pain_interfere:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Not at all', 'A little bit', 'Moderately', 'Quite a bit', 'Extremely']
		view:
			control : 'radio'
			label: 'During the past 4 weeks, how much did pain interfere with your normal work (including both work outside the home and housework)?'
			columns:3

	# 4-Week Review
	wkchk_calm:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control : 'radio'
			label: 'Have you felt calm and peaceful?'
			columns:3

	wkchk_energy:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control : 'radio'
			label: 'Did you have a lot of energy?'
			columns:3

	wkchk_downhearted:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control : 'radio'
			label: 'Have you felt downhearted and depressed?'
			columns:3

	gen_social_interfere:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control : 'radio'
			label: 'During the past 4 weeks, how much of the time has your physical health or emotional problems interfered with your social activities (like visiting friends, relatives, etc.)?'
			columns:3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'patient', 'pharm'] 
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm', 'patient']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm', 'patient']
	bundle: ['patient', 'careplan', 'encounter']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_sf12v2:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Quality Of Life Survey Participation':
			hide_header:true
			fields: ['last_particiates', 'last_assessment_date', 'pt_qol_particiates', 'assessment_date']
		'Health Improvement':
			fields: ['health_general']
			prefill: 'encounter_sf12v2'
		'Limitations':
			note: 'The following questions are about activities you might do during a typical day. Does your health now limit you in these activities? If so, how much?'
			fields: ['limitation_moderate', 'limitation_climbing_many']
			prefill: 'encounter_sf12v2'
		'Daily Activities (Physical)':
			note: 'During the past 4 weeks, how much of the time have you had any of the following problems with your work or other regular daily activities as a result of your physical health?'
			fields: ['phys_accomplish', 'phys_limited']
			prefill: 'encounter_sf12v2'
		'Daily Activities (Emotional)':
			note: 'During the past 4 weeks, have you had any of the following problems with your work or other regular daily activities as a result of any emotional problems (such as feeling depressed or anxious)?'
			fields: ['emo_accomplish', 'emo_careless']
			prefill: 'encounter_sf12v2'
		'Pain':
			fields: ['gen_pain_interfere']
			prefill: 'encounter_sf12v2'
		'4-Week Review':
			note: 'These questions are about how you feel and how things have been with you during the past 4 weeks. For each question, please give the one answer that comes closest to the way you have been feeling. How much of the time during the past 4 weeks...'
			fields: ['wkchk_calm', 'wkchk_energy', 'wkchk_downhearted']
			prefill: 'encounter_sf12v2'
		'Social':
			fields: ['gen_social_interfere']
			prefill: 'encounter_sf12v2'

view:
	comment: 'Patient > Careplan > Encounter > Quality of Life'
	find:
		basic: ['pt_qol_particiates']
	grid:
		fields: ['created_on', 'assessment_date', 'created_by']
		sort: ['-id']
	label: 'Patient Encounter: Quality of Life'
