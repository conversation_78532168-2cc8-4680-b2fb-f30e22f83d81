fields:
	name:
		model:
			max: 128
			required: true
		view:
			label: 'Flag Name'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['manage', 'reference']
	indexes:
		unique: [
			['name']
		]
	name: ['name']
	sections:
		'File Flag':
			fields: ['name']

view:
	comment: 'Manage > File Flag'
	find:
		basic: ['name']
	grid:
		fields: ['name']
		sort: ['name']
	label: 'File Flag'
	open: 'read'