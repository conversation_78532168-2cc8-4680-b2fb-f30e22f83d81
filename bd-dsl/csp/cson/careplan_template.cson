fields:
	external_id:
		model:
			prefill: ['careplan_template']
			type: 'text'

	name:
		model:
			prefill: ['careplan_template']
			max: 64
			required: true
		view:
			label: 'Name'



	subform_problems:
		model:
			multi: true
			type: 'subform'
			source: 'careplan_template_problem'
		view:
			grid:
				hide_cardmenu: true
				edit: true
				fields: ['name', 'is_complete', 'completed_date']
				label: ['Name', 'Completed', 'Completed Date']
			label: 'Problems'

	subform_goal:
		model:
			multi: true
			type: 'subform'
			source: 'careplan_template_goal'
		view:
			grid:
				hide_cardmenu: true
				edit: true
				fields: ['name', 'is_complete', 'completed_date']
				label: ['Name', 'Completed', 'Completed Date']
			label: 'Goals'

	subform_intervention:
		model:
			multi: true
			type: 'subform'
			source: 'careplan_template_intervention'
		view:
			grid:
				hide_cardmenu: true
				edit: true
				fields: ['name', 'is_complete', 'completed_date']
				label: ['Name', 'Completed', 'Completed Date']
			label: 'Interventions'


	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['manage']
	indexes:
		many: [
			['name']
		]
		unique: [
			['name']
		]
	prefill:
		careplan_template:
			max: 'created_on'
	name: ['name']
	sections:
		'Template Details':
			fields: ['name']
		'Problems':
			fields: ['subform_problems']
		'Goals':
			fields: ['subform_goal']
		'Interventions':
			fields: ['subform_intervention']

view:
	comment: 'Manage > Care Plan Template'
	find:
		basic: ['name']
	grid:
		fields: ['name', 'created_by', 'created_on', 'updated_on', 'updated_by']
		sort: ['name']
	label: 'Care Plan Template'
