fields:
	status:
		model:
			type: 'text'
		view:
			label: 'Status'

	data_diff:
		model:
			type: 'json'
		view:
			label: 'Form Changes'

	fields_changed:
		model:
			type: 'json'
		view:
			label: 'Fields Changed'

	patient_id:
		model:
			source: 'patient'
		view:
			label: 'Patient Name'

	created_on:
		view:
			label: 'Change Date/Time'

	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			prefill: ['careplan_order.therapy_id']

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	bundle: ['manage']
	name: ['created_on']
	sections:
		'Audit':
			fields: ['created_on', 'status', 'patient_id', 'therapy_1']
	access:
		create:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		review:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		update:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
view:
	find:
		basic: [ 'patient_id','therapy_1']
	grid:
		fields: ['created_on', 'status','patient_id','therapy_1']
		sort: ['-created_on']
	label: 'Admission/Discharge Audit'
	open: 'read'
