fields:

	current_iv:
		model:
			source: ['Needs to be restarted', 'Tender Painful', 'Drain<PERSON>','<PERSON><PERSON>' , '<PERSON>cchymosis' , '<PERSON>rythema' , 'Sutures', 'WNL']
		view:
			control : 'checkbox'
			label : 'Current IV Site'
			columns:3

	type_iv:
		model:
			source: ['Peripheral', 'Midline', 'PICC','Port' , 'Others']
			if:
				'Others':
					fields:['other_type']
		view:
			control : 'checkbox'
			label: 'Type'
			columns:3

	current_dressing:
		model:
			source: ['Dry and Intact' , 'Type of Dressing']
			if:
				'Type of Dressing':
					fields: ['type_of_dressing']
		view:
			label:'Current Dressing'
			control:'radio'
			columns:3

	type_of_dressing:
		model:
			type: 'text'
		view:
			label: 'Other Dressing Type'
			columns:3

	other_type:
		model:
			type: 'text'
		view:
			label: 'Other Type'
			columns:3

	iv_purpose:
		model:
			source: ['Immunoglobulin', 'Biologic Therapy', 'Hydration','Chemotherapy' , 'Blood or Components' ,'Pain Management' ,'TP<PERSON>,<PERSON>pids' , 'Others']
			if:
				'Others':
					fields:['other_iv_purpose']
		view:
			control: 'checkbox'
			label: 'IV Purpose'
			columns:3

	other_iv_purpose:
		model:
			type: 'text'
		view:
			label: 'Other IV purpose'
			columns:3

	pump:
		model:
			source: ['None', 'Curlin', 'CADD 4000/6000', 'Others']
			if:
				'Others':
					fields: ['other_pump']
		view:
			control : 'checkbox'
			label: 'Pump'
			columns:3

	other_pump:
		model:
			type: 'text'
		view:
			label: 'Other pump'
			columns:3

	settings:
		model:
			source: ['Variable' , 'Continous' , 'Others']
			if:
				'Others':
					fields:['other_setting']
		view:
			control: 'checkbox'
			label: 'Settings'
			columns:3

	other_setting:
		model:
			type: 'text'
		view:
			label: 'Other Settings'
			columns:3

	access_start:
		model:
			source: ['Yes' , 'No']
		view:
			control: 'radio'
			label: 'Access Start'
			columns:3

	re_start:
		model:
			source: ['Yes' , 'No']
		view:
			control: 'radio'
			label: 'Restart'
			columns:3

	subform_attempt:
		model:
			multi: true
			source: 'intravenous_attempt'
			type: 'subform'
		view:
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['site', 'gauge', 'length', 'brand', 'type_in', 'lumens']
				label: ['Site', 'Gauge', 'Length', 'Brand', 'Type', 'Lumens']
				width: [25, 15, 15, 15, 15, 15]
			label: 'Attempts'

model:
	sections_group: [
		'Current Intravenous Status':
			fields: ['current_iv', 'type_iv', 'other_type', 'current_dressing' , 'type_of_dressing', 'iv_purpose',
			'other_iv_purpose', 'pump', 'other_pump', 'settings', 'other_setting', 'access_start' ,'re_start']
		'Intravenous Attempt':
			hide_header: false
			indent: false
			fields: ['subform_attempt']
	]

	bundle: ['patient']
	name: ['created_on', 'created_by']
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin','liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']
view:
	label: 'Intravenous Details'
	open: 'read'
