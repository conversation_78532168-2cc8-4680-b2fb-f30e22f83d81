fields:

	time_taken:
		model:
			required: false
		view:
			requireall_bypass: true

	temp_method:
		model:
			required: false
		view:
			requireall_bypass: true

	temp:
		model:
			required: false
		view:
			requireall_bypass: true

	pulse_method:
		model:
			required: false
		view:
			requireall_bypass: true

	pulse:
		model:
			required: false
		view:
			requireall_bypass: true

	bp_method:
		model:
			required: false
		view:
			requireall_bypass: true

	bp_loc:
		model:
			required: false
		view:
			requireall_bypass: true

	bp:
		model:
			required: false
		view:
			requireall_bypass: true

	resp:
		model:
			required: false
		view:
			requireall_bypass: true

	resp_details:
		model:
			required: false
		view:
			requireall_bypass: true

	o2_sat:
		model:
			required: false
		view:
			requireall_bypass: true

	titration:
		model:
			required: false
		view:
			requireall_bypass: true

	titration_type:
		model:
			required: false
		view:
			requireall_bypass: true

	flushes_grid:
		model:
			subfields:
				when:
					label: 'When?'
					source: ['Pre', 'Post', 'During']
					style:
						width: '20%'
				med_name:
					label: 'Flush Name'
					source :['NS' ,'Heparin']
					style:
						width: '20%'
				med_amount:
					label: 'Amount(ml)'
					type:'text'
					style:
						width: '30%'
				flush_type:
					label: 'Comments'
					type:'text'
					style:
						width: '30%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Flushes'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

view:
	comment: 'Patient > Careplan > Encounter > Vitals'
	grid:
		fields: ['time_taken', 'titration', 'temp', 'pulse', 'bp', 'comment']
	label: 'Infusion Logs/Vitals'