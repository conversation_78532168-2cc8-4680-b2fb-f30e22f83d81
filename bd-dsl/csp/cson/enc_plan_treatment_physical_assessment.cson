fields:
	endocrine:
		model:
			max: 128
			multi: true
			source: ['WNL', 'Diabetes', 'Other']
			if:
				'Other':
					fields: ['endocrine_other']
		view:
			control: 'checkbox'
			note: 'Select any that apply'
			label: 'Endocrine'
			columns:3

	endocrine_other:
		model:
			required: false
		view:
			label: 'Endocrine Other Details'
			columns:3

	neurological:
		model:
			multi: true
			source:['WNL', 'Headache', 'Vertigo, Lightheaded', 'Alert and Oriented', 'Tremors, Seizures', 'Impaired Speech', 'Impaired Vision', 'Other']
			if:
				'Other':
					fields: ['neurological_other']
				'Alert and Oriented':
					fields: ['alert_oriented_note']
		view:
			control: 'checkbox'
			label: 'Neurological'
			columns:3

	alert_oriented_note:
		model:
			required: false
		view:
			label: 'Alert and Oriented Note'
			columns:3

	neurological_other:
		model:
			required: false
		view:
			label: 'Neurological Details'
			columns:3

	cardiovascular:
		model:
			max: 128
			multi: true
			source:['WNL', 'Chest Pain, <PERSON><PERSON>', 'Anticoagulants', 'Heart Tones, Pulses',
				'Diaphoresis', 'Poor Endurance', 'Edema', 'Other']
			if:
				'Other':
					fields: ['cardiovascular_other']
				'Edema':
					fields: ['cardiovascular_edema']
		view:
			control: 'checkbox'
			note: 'Select any that apply'
			label: 'Cardiovascular'
			columns:3

	cardiovascular_edema:
		model:
			multi: true
			source: ['LUE', 'RUE', 'LLE', 'RLE', 'Facial', 'Lymphedema', 'Anasarca']
			required: false
		view:
			control: 'checkbox'
			label: 'Edema'
			columns:3

	cardiovascular_other:
		model:
			required: false
		view:
			label: 'Cardiovascular Other'
			columns:3

	psychological:
		model:
			max: 128
			multi: true
			source:['WNL', 'Chest Pain, Angina', 'Anticoagulants', 'Heart Tones, Pulses', 'Diaphoresis', 'Poor Endurance', 'Edema', 'Other']
			if:
				'Other':
					fields: ['psychological_other']
				'Edema':
					fields: ['psychological_edema']
		view:
			control: 'checkbox'
			note: 'Select any that apply'
			label: 'Psychological'
			columns:3

	psychological_edema:
		model:
			multi: true
			source: ['1+', '2+', '3+', 'Pitting', 'LUE', 'RUE', 'LLE', 'RLE', 'Facial', 'Lymphedema', 'Anasarca']
			required: false
		view:
			control: 'checkbox'
			label: 'Edema'
			columns:3

	psychological_other:
		model:
			required: false
		view:
			label: 'Psychological Other'
			columns:3

	respiratory:
		model:
			multi: true
			source:['WNL', 'SOB', 'Lung Sounds', 'Cough', 'Sputum', 'Other']
			if:
				'Other':
					fields: ['respiratory_other']
				'Lung Sounds':
					fields: ['respiratory_lung_sounds']
				'Sputum':
					fields: ['respiratory_sputum']
				'Cough':
					fields: ['respiratory_cough']
		view:
			control: 'checkbox'
			note: 'Select any that apply'
			label: 'Respiratory'
			columns:3

	respiratory_lung_sounds:
		model:
			multi: true
			source: ['Clear', 'Wheeze', 'Rales', 'Rohnchi']
			required: false
		view:
			control: 'checkbox'
			label: 'Respiratory Lung Sounds'
			columns:3

	respiratory_cough:
		model:
			multi: true
			source: ['Dry', 'Moist']
			required: false
		view:
			control: 'checkbox'
			label: 'Respiratory Cough'
			columns:3

	respiratory_sputum:
		model:
			required: false
		view:
			label: 'Respiratory Color, Amount'
			columns:3

	respiratory_other:
		model:
			required: false
		view:
			label: 'Respiratory Other Details'
			columns:3

	gastrointestinal:
		model:
			multi: true
			source:['WNL', 'SOB', 'Lung Sounds', 'Cough', 'Sputum', 'Other']
			if:
				'Other':
					fields: ['gastrointestinal_other']
				'Lung Sounds':
					fields: ['gastrointestinal_lung_sounds']
				'Sputum':
					fields: ['gastrointestinal_sputum']
				'Cough':
					fields: ['gastrointestinal_cough']
		view:
			control: 'checkbox'
			note: 'Select any that apply'
			label: 'Gastrointestinal'
			columns:3

	gastrointestinal_lung_sounds:
		model:
			multi: true
			source: ['Clear', 'Wheeze', 'Rales', 'Rohnchi']
			required: false
		view:
			control: 'checkbox'
			label: 'Gastrointestinal Lung Sounds'
			columns:3

	gastrointestinal_cough:
		model:
			multi: true
			source: ['Dry', 'Moist']
			required: false
		view:
			control: 'checkbox'
			label: 'Gastrointestinal Cough'
			columns:3

	gastrointestinal_sputum:
		model:
			required: false
		view:
			label: 'Gastrointestinal Color, Amount'
			columns:3

	gastrointestinal_other:
		model:
			required: false
		view:
			label: 'Gastrointestinal Other Details'
			columns:3

	musculoskeletal:
		model:
			multi: true
			source:['WNL', 'Weak', 'Slow Gait', 'Shuffling Gait', 
				'Poor Balance', 'High Fall Risk', 'Limited Mobility', 'Assistive Devices', 'Paralysis', 'Muscle Rigidity', 'Other']
			if:
				'Other':
					fields: ['musculoskeletal_other']
				'Assistive Devices':
					fields: ['assistive_devices_list']
			
		view:
			control: 'checkbox'
			note: 'Select any that apply'
			label: 'Musculoskeletal'
			columns:3

	assistive_devices_list:
		model:
			required: true
			multi:true
		view:
			label: 'Musculoskeletal Other Problem Details'
			columns:3

	musculoskeletal_other:
		model:
			required: false
		view:
			label: 'Musculoskeletal Other Details'
			columns:3

	skin:
		model:
			multi: true
			source:['WNL', 'Warm', 'Cool', 'Dry', 'Pink', 'Pale', 'Intact', 'Wound', 'Incision, Tear', 'Bruise', 'Rash, Itching', 'Turgor', 'Other']
			if:
				'Other':
					fields: ['skin_other']
				'Turgor':
					fields: ['skin_turgor']
		view:
			control: 'checkbox'
			note: 'Select any that apply'
			label: 'Skin'
			columns:3

	skin_turgor:
		model:
			multi: true
			source:['Good', 'Fair', 'Poor']
		view:
			control: 'checkbox'
			note: 'Select any that apply'
			label: 'Turgor'
			columns:3

	skin_other:
		model:
			required: false
		view:
			note: 'Select any that apply'
			label: 'Skin Other Details'
			columns:3

	genitourinary:
		model:
			max: 24
			multi: true
			source:['WNL', 'Incontinence', 'Urgency', 'Frequency', 'Itching', 'Burning', 'Pain', 'Odor', 'Foley Cath', 'Other']
			if:
				'Foley Cath':
					fields: ['genitourinary_foley_size', 'genitourinary_change_due']
				'Other':
					fields: ['genitourinary_other']
		view:
			control: 'checkbox'
			label: 'Genitourinary'
			columns:3

	genitourinary_foley_size:
		model:
			required: false
		view:
			label: 'Genitourinary Foley Cath Size'
			columns:3

	genitourinary_change_due:
		model:
			type: 'date'
		view:
			label: 'Genitourinary Change Due'
			columns:3

	genitourinary_other:
		model:
			required: false
		view:
			control: 'area'
			label: 'Genitourinary Other Details'
			columns:3

	pain:
		model:
			multi: true
			source:['No Pain', 'Dull', 'Constant', 'Ache', 'Sharp', 'Intermittent']
		view:
			control: 'checkbox'
			note: 'Select any that apply'
			label: 'Pain'
			columns:3

	pain_level:
		model:
			type: 'int'
			required: false
		view:
			label: 'Current Pain level (0-10)'
			columns:3

	pain_location:
		model:
			required: false
		view:
			label: 'Location'
			columns:3

	acceptable_pain_level:
		model:
			type: 'int'
			required: false
		view:
			label: 'Acceptable Pain Level'
			columns:3

	pain_relieved:
		model:
			source:['Yes', 'No']
		view:
			control: 'checkbox'
			label: 'Pain Relieved'
			columns:3

	commt_phy_assessment:
		model:
			required: false
		view:
			control: 'area'
			label: 'Comments: Physical Assessment'
			columns:3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true



model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin', 'liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'csr', 'liaison', 'nurse', 'pharm']
	name: ['created_on']
	sections:
		'Physical Assessment':
			fields: ['endocrine', 'endocrine_other', 'neurological', 'alert_oriented_note', 'neurological_other', 'cardiovascular', 'cardiovascular_other', 'cardiovascular_edema', 'psychological', 'psychological_edema', 'psychological_other', 'respiratory', 'respiratory_lung_sounds', 'respiratory_cough', 'respiratory_sputum', 'respiratory_other', 'gastrointestinal', 'gastrointestinal_lung_sounds', 'gastrointestinal_cough', 'gastrointestinal_sputum', 'gastrointestinal_other', 'musculoskeletal', 'assistive_devices_list', 'musculoskeletal_other', 'skin', 'skin_other', 'skin_turgor', 'genitourinary', 'genitourinary_foley_size', 'genitourinary_change_due', 'genitourinary_other']
		'Pain':
			hide_header: true
			fields: ['pain', 'pain_level', 'pain_location', 'acceptable_pain_level', 'pain_relieved', 'commt_phy_assessment']
view:
	comment: ''
	label: 'Plan Of Treatment One Time - Physical Assessment'