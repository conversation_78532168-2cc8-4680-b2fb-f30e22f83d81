fields:
	type:
		model:
			source: {
				sn_to_administer:'SN to administer',
				teach: 'Teach', 
				patient: 'Patient', 
				# caregiver_administration: 'Caregiver administration', 
				prescribed_therapy: 'Caregiver'
			}
			required: false
		view:
			control: 'checkbox'
			label: 'Administration by'
			columns:3

	via:
		model:
			source: ['PIV', 'MID', 'PICC', 'Central Line', 'Implanted Port', 'Subcutaneous', 'Other']
			if:
				'Other':
					fields: ['via_other']
			required: false
		view:
			control: 'checkbox'
			label: 'VIA'
			columns:3

	via_other:
		model:
			required: true
		view:
			label: 'VIA Other Details'
			columns:3

	admin_method:
		model:
			source: ['IVP', 'INJ', 'Gravity-Flow Controlled', 'Pump', 'Disp.Pump', 'Other']
			required: false
			if:
				'Other':
					fields:['admin_method_other']
		view:
			control: 'radio'
			label: 'Method of administration'
			columns:3

	admin_method_other:
		model:
			required: false
		view:
			label: 'Method of administration Details'
			columns:3

	adult_opt:
		model:
			source: [
				'NS', 'D5W flush pre/post infusion and p.r.n.','Heparin IV units/ml post infusion and p.r.n',
			]
			required: false
			if:
				'Heparin IV units/ml post infusion and p.r.n':
					fields: ['adult_hep_iv_unit' ,'adult_hep_iv']
				'D5W flush pre/post infusion and p.r.n.':
					fields: ['prn_option']
		view:
			control: 'checkbox'
			label: 'Adult-Access Device-Maintenance-Flushes'
			columns:3
	access_device_maintenace:
		model:
			source: [
				'Adult' ,'SN to remove',
				'SN to perform' ,'SN to administer' ,'SN to administer Ana-Kit p.r.n. per physician’s order','Additional Orders', 'SN to establish Peripheral IV access' ,
				'SN to perform central line care: Cleanse site with alcohol x3, betadine x3, or chloraprep x1. and apply TSM dressing. May apply antimicrobial dressing to IV site p r.n. inflammation/prophylaxis (or) site care per protocols.',
				'SN to obtain lab specimen(s) as ordered'
			]
			required: false
			if:
				'Adult':
					fields:['adult_opt']
				'SN to remove':
					fields: ['sn_to_remove_opt']
				'SN to perform':
					fields: ['sn_to_perform_opt']
				'SN to administer':
					fields: ['administer_option']
				'SN to obtain lab specimen(s) as ordered':
					fields: ['specimen_order']
				'Additional Orders':
					fields: ['additional_order']
		view:
			control: 'checkbox'
			label: 'Access Device: Maintenance'
			columns:3
	
	adult_hep_iv_unit:
		model:
			source:['1 - 3 ml PIV/Midline' ,'3 - 5 ml CVCIPICC/Port']
		view:
			label:'Heparin IV Units'
			control:'radio'
			columns:3

	adult_hep_iv:
		view:
			label:'Heparin IV'
			columns:3


	additional_order:
		view:
			label:'Additional Orders'
			control:'area'
			columns:3

	specimen_order:
		view:
			label:'Speciman Orders'
			columns:3

	sn_to_remove_opt:
		model:
			source :['PICC ','Non-tunneled CVC: when therapy is complete']
		view:
			label:'SN To Remove'
			control:'radio'
			columns:3

	sn_to_perform_opt:
		model:
			source: ['Teach patient/caregiver to access/deaccess: Implanted port' , 'Non-coring needle change']
			if:
				'Non-coring needle change':
					fields :['non_coring_type']
		view:
			label:'SN To Perform'
			control:'checkbox'
			columns:3
	
	non_coring_type:
		model:
			source: ['Weekly' ,'Other']
			if:
				'Other':
					fields: ['other_coring']
		view:
			label:'Needle Change'
			columns:3

	other_coring:
		view:
			label:'Needle Change Other'
			columns:3
	
	prn_option:
		model:
			source: ['2 – 3 ml PIV', '3 – 5 ml Midline/CVCIPICC', '5 – 10 ml Port I valved catheter' , '5 - 1O ml pre/post lab Midline/CVC/PICC', '10 - 20 ml pre/post lab Port I valved catheter']
		view:
			label:'P.R.N options'
			control:'checkbox'
			columns:3


	administer_option:
		model:
			source: ['Teach patient/caregiver to administer: subcutaneous injections, site rotation and signs/symptoms to report']
		view:
			label: 'SN To Admin'
			control:'checkbox'
			columns:3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true



model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin','liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin','csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']
	name: ['created_on']
	sections:
		'Nursing Intervention/Orders':
			fields: ['type', 'via', 'via_other', 'admin_method', 'admin_method_other', 'access_device_maintenace', 'adult_opt', 'adult_hep_iv_unit', 'adult_hep_iv', 'prn_option' ,'sn_to_remove_opt', 'specimen_order','additional_order' , 'sn_to_perform_opt' , 'non_coring_type','other_coring' , 'administer_option']
view:
	comment: ''
	grid:
		fields: ['type']
	label: 'Nursing Intervention/Orders'
