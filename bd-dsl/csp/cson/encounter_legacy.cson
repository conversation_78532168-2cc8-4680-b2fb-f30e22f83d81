fields:

	contact_date:
		model:
			type: 'date'
		view:
			label: 'Contact Date'
			columns: 4
			readonly: true

	travel_time_st:
		model:
			type: 'time'
		view:
			label: 'Travel Time To Start'
			columns: 4
			readonly: true

	travel_time_ed:
		model:
			access:
				read: ['patient']
			type: 'time'
		view:
			label: 'Travel Time To End'
			columns: 4
			readonly: true

	time_in:
		model:
			access:
				read: ['patient']
			required: true
			type: 'time'
		view:
			note: "Start time of the visit"
			label: 'Visit Time In'
			columns: 4
			readonly: true

	contact_location:
		model:
			source: ['Home', 'AIS', 'Other']
		view:
			control: 'radio'
			label: 'Contact Visit'
			readonly: true
			columns: 2

	order_out_csp:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Orders from CSP?'
			readonly: true
			columns: 4

	med_name:
		view:
			label: 'Primary Medication'
			readonly: true
			columns: 4

	cust_contact_reason:
		model:
			required: true
			multi: false
			source: ['Initial Comprehensive visit', 'Reassessment', 'Follow up visit', 'One-Time Skilled Nurse', 'Discharge Summary Non-Visit', 'Transfer Summary Non-Visit' ,'On Hold']
			if:
				'On Hold':
					fields: ['on_hold_details']
		view:
			control: 'checkbox'
			label: 'Visit Type'
			columns: 2
			readonly: true

	on_hold_details:
		view:
			control: 'area'
			columns: 2
			readonly: true

	in_facility:
		model:
			source: ['No', 'Yes', 'Answer not provided']
			if:
				'Yes':
					fields: ['in_facility_type', 'in_facility_name', 'in_facility_address', 'in_facility_phone', 'in_facility_length', 'in_facility_notes']
		view:
			control: 'radio'
			label: 'Are you currently in a skilled nursing facility, hospital, assisted living, or hospice?'
			readonly: true
			columns: 2

	in_facility_type:
		model:
			required: false
			source: ['Skilled Nursing Facility', 'Hospital', 'Assisted Living', 'Hospice']
		view:
			label: 'Facility Type'
			readonly: true
			columns: 2

	in_facility_name:
		model:
			required: false
		view:
			label: 'Facility Name'
			readonly: true
			columns: 2

	in_facility_address:
		model:
			required: false
		view:
			label: 'Facility Address'
			readonly: true
			columns: 2

	in_facility_phone:
		model:
			required: false
		view:
			label: 'Facility Phone'
			format: 'us_phone'
			readonly: true
			columns: 2

	in_facility_length:
		model:
			required: false
		view:
			label: 'How long have you been at the current facility?'
			readonly: true
			columns: 2

	in_facility_notes:
		view:
			control: 'area'
			label: 'Facility Notes'
			readonly: true
			columns: 2

	progress_note:
		view:
			control: 'area'
			label: 'Progress Note'
			readonly: true

	travel_time_from_st:
		model:
			type: 'time'
		view:
			label: 'Travel Time From Start'
			readonly: true
			columns: 4

	travel_time_from_ed:
		model:
			type: 'time'
		view:
			label: 'Travel Time From End'
			readonly: true
			columns: 4

	time_out:
		model:
			type: 'time'
		view:
			note: "End time of the visit"
			label: 'Visit Time Out'
			readonly: true
			columns: 4

	time_total:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Total Visit Time (hrs)'
			readonly: true
			columns: 4

	user_name_str:
		model:
			if:
				'Kahu Agency Nurse':
					fields: ['generic_nurse_name']
				'Kahu Agency Nurse (Nurse)':
					fields: ['generic_nurse_name']
				'43392':
					fields: ['generic_nurse_name']
				'Adept Infusion Nurse':
					fields: ['generic_nurse_name']
				'Aplus Nursing Care':
					fields: ['generic_nurse_name']
				'Infusion of Care Infusion Nurse':
					fields: ['generic_nurse_name']
				'Infusion Partners 360 Infusion Nurse':
					fields: ['generic_nurse_name']
		view:
			label: 'Note By'
			readonly: true
			columns: 2

	generic_nurse_name:
		view:
			label: 'Full Nurse Name'
			readonly: true
			columns: 2

	rn_signature:
		model:
			type: 'json'
		view:
			control: 'esign'
			label: 'RN E-Signature'
			readonly: true
			columns: -2

	client_signature:
		model:
			type: 'json'
		view:
			control: 'esign'
			label: 'Client/other E-Signature'
			readonly: true
			columns: 2

model:
	name: ['contact_date', 'cust_contact_reason']
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin','liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin','csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']

	sections:
		'Visit Start':
			fields: ['contact_date', 'travel_time_st', 'travel_time_ed', 'time_in', 'contact_location', 'order_out_csp', 'med_name', 'cust_contact_reason', 'on_hold_details',
			'in_facility', 'in_facility_type', 'in_facility_name', 'in_facility_address', 'in_facility_phone', 'in_facility_length', 'in_facility_notes']
		'Note':
			fields: ['progress_note']
		'Visit End':
			fields: ['time_out', 'time_total', 'travel_time_from_st', 'travel_time_from_ed',
			'user_name_str', 'generic_nurse_name', 'rn_signature', 'client_signature']

view:
	grid:
		fields: ['created_by', 'contact_date', 'cust_contact_reason', 'time_in', 'time_out', 'reviewed_by', 'reviewed_on']
		sort: ['-id']
