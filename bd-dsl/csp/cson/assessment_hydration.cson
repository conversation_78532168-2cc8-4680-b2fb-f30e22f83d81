fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	# Assessment Questions

	blood_draw:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Has patient had a recent laboratory blood draw?"

	have_infections:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Has the patient had any chronic or recent infections?"

	taking_antibiotic:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Is patient currently receiving antibiotic therapy or have active infection?"

	have_fever:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Does patient report recent fevers?"

	have_thirst:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Does patient report excess thirst?"

	# Hepatic Disease Risk
	hepatic_disease:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with Hepatic disease?'

	# Renal Disease Risk
	renal_disease:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with Renal (kidney) disease?'

	has_diabetes:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with diabetes?'

	meds_taking:
		model:
			max: 32
			min: 1
			multi: true
			source:['Acyclovir','Aminoglycosides','Amphotericin','Atripla','Cisplatin','Diuretics (loops, thiazides)','NSAIDS',
				'Prograf','Proton-Pump Inhibitors','Tenofovir','Viread','Truvada','Other','None']
			if:
				'Other':
					fields: ['meds_taking_other']
		view:
			control: 'checkbox'
			label: 'Are you currently taking any of the following (concomitant nephrotoxic) drugs?'

	meds_taking_other:
		view:
			label: 'Other Drugs'

	# Thromboembolic Risk
	has_htn:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pressure_controlled']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with hypertension (HTN)?'

	pressure_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your blood pressure currently controlled?'

	high_chol:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['chol_controlled']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with high cholesterol?'

	chol_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your cholesterol currently controlled?'

	heart_cond:
		model:
			max: 64
			min: 1
			multi: true
			source: ['Congestive heart failure (CHF)', 'Cardiomyopathy', 'Valve Disease', 'Congenital Defects', 'Atrial Fibrillation', 'Angina', 'Pulmonary disease', 'Other', 'None']
			if:
				'Other':
					fields: ['heart_cond_other']
		view:
			control: 'checkbox'
			label: 'Do you suffer from any of the following heart diseases?'

	heart_cond_other:
		view:
			control: 'area'
			label: 'Other Heart Diseases'

	immobil_periods:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you experience prolonged periods of immobilization?'

	diag_thomb:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with thrombophilia?'

	clam_hands:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do your hands/extremities feel cold and clammy (experience low BP)?'

	had_sepsis:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any recent cases of sepsis or infection in blood?'

	# Support Questions
	has_ems:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Does patient have access to EMS (w/in 15 minutes)?"

	nurse_present:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will nurse be present for the entire infusion?"

	adult_present:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will an additional adult be present for the entire infusion?"

	adult_present_after:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will an additional adult be present for 2 to 3 hours after infusion completed?"

	physician_available:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will the ordering physician be available by phone during the infusion?"

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['patient', 'careplan']
	indexes:
		many: [
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	sections:
		'Hydration General Assessment':
			fields: ['blood_draw', 'have_infections', 'taking_antibiotic', 'have_fever', 'have_thirst', 'has_ems', 'nurse_present', 'adult_present', 'adult_present_after', 'physician_available']
		'Hydration Hepatic Risk Assessment':
			fields: ['hepatic_disease']
		'Hydration Renal Risk Assessment':
			fields: ['renal_disease', 'has_diabetes', 'meds_taking', 'meds_taking_other']
		'Hydration Thromboembolic Risk Assessment':
			fields: ['has_htn', 'pressure_controlled', 'high_chol', 'chol_controlled', 'heart_cond', 'heart_cond_other', 'immobil_periods', 'diag_thomb', 'clam_hands', 'had_sepsis']
view:
	comment: 'Patient > Careplan > Assessment > Hydration'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Hydration'
	open: 'read'
