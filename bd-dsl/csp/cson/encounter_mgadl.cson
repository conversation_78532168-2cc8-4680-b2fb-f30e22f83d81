fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	# Questionnaire Participation
	# Questionnaire Participation
	participates:
		model:
			if:
				'*':
					fields: ['reviewed_by']
		view:
			findfilter: 'Yes'
			control : 'radio'
			label: 'Will a Myasthenia Gravis Activities of Daily Living (MG-ADL) be completed on this visit?'
			readonly: true
			offscreen: true

	document_mg_adl: #marked it required false by using coffee
		model:
			source: ['Yes']
			if:
				'Yes':
					sections: ['Myasthenia Gravis Activities of Daily Living (MG-ADL)']
					fields: ['talking', 'chewing', 'swallowing', 'breathing', 'grooming', 'arise', 'vision', 'eyelid', 'score']
		view:
			control: 'checkbox'
			label: 'MG-ADL'

	# Questionnaire
	# Questionnaire
	talking:
		model:
			required: true
			source: ['Normal', 'Intermittent slurring or nasal speech', 'Constant slurring or nasal speech, but can be understood', 'Difficult-to-understand speech']
		view:
			control : 'radio'
			label: "Talking"
			validate: [
					name: 'MGADLScoreValidate'
			]

	chewing:
		model:
			required: true
			source: ['Normal', 'Fatigue with solid food', 'Fatigue with soft food', 'Gastric tube']
		view:
			control : 'radio'
			label: "Chewing"
			validate: [
					name: 'MGADLScoreValidate'
			]


	swallowing:
		model:
			required: true
			source: ['Normal', 'Rare episode of choking', 'Frequent choking necssitating changes in diet', 'Gastric tube']
		view:
			control : 'radio'
			label: "Swallowing"
			validate: [
					name: 'MGADLScoreValidate'
			]

	breathing:
		model:
			required: true
			source: ['Normal', 'Shortness of breath with exertion', 'Shortness of breath at rest', 'Ventilator dependence']
		view:
			control : 'radio'
			label: "Breathing"
			validate: [
					name: 'MGADLScoreValidate'
			]

	grooming:
		model:
			required: true
			source: ['None', 'Extra effort, but no rest periods needed', 'Rest periods needed', 'Cannot do one of these functions']
		view:
			control : 'radio'
			label: "Impairment of ability to brush teeth or comb hair"
			validate: [
					name: 'MGADLScoreValidate'
			]

	arise:
		model:
			required: true
			source: ['None', 'Mild, sometimes uses arms', 'Moderate, always uses arms', 'Severe, requires assistance']
		view:
			control : 'radio'
			label: "Impairment of ability to arise from a chair"
			validate: [
					name: 'MGADLScoreValidate'
			]

	vision:
		model:
			required: true
			source: ['None', 'Occurs, but not daily', 'Daily, but not constant', 'Constant']
		view:
			control : 'radio'
			label: "Double vision"
			validate: [
					name: 'MGADLScoreValidate'
			]

	eyelid:
		model:
			required: true
			source: ['None', 'Occurs, but not daily', 'Daily, but not constant', 'Constant']
		view:
			control : 'radio'
			label: "Eyelid droop"
			validate: [
					name: 'MGADLScoreValidate'
			]

	score:
		model:
			min: 0
			max: 24
			type: 'int'
		view:
			label: 'Assessment Score'
			readonly: true


	mg_adl_symptoms:
		model:
			multi: true
			source: ['Difficulty breathing (SOB)', 'Morning headaches/tiredness whole day', 
			'Cough (increased secretion/inability to clear secretion)', 
			'Ptosis', 'Diplopia', 'Dysarthria/Slurred speech/weak Hoarse voice',
			'Difficulty Chewing and/ Swallowing/weight loss',
			'Dysphagia',
			'Weakness of the neck or limbs',
			'Muscle weakness/Fatigue',
			'Respiratory paralysis/failure'
			]
		view:
			control: 'checkbox'
			label: "Symptoms"


	failed_treatments:
		view:
			control: 'area'
			label: 'Tried and Failed Treatments'

	acute_exacerbation_history:
		view:
			control: 'area'
			label: 'HX of acute exacerbation/symptoms/hospitalization?'

	evening_symptom_worsening:
		view:
			control: 'area'
			label: 'Symptoms worsening toward the end of the day/which one?'

	other_detail:
		view:
			control: 'area'
			label: 'Additional Details'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']  #Added patient based on this ticekt HB-6553
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'patient'] #Added patient based on this ticekt HB-6553
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'patient'] #Added patient based on this ticekt HB-6553
	bundle: ['patient', 'careplan', 'encounter']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_mgadl:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Document':
			fields: ['document_mg_adl']
		'Myasthenia Gravis Activities of Daily Living (MG-ADL)':
			fields: ['talking', 'chewing', 'swallowing', 'breathing', 'grooming', 'arise', 'vision', 'eyelid', 'score']
		'Symptoms and Other Details':
			fields: ['mg_adl_symptoms', 'failed_treatments', 'acute_exacerbation_history', 'evening_symptom_worsening', 'other_detail']

view:
	comment: 'Patient > Careplan > Encounter > Myasthenia Gravis Activities of Daily Living (MG-ADL)'
	label: 'Patient Encounter: Myasthenia Gravis Activities of Daily Living (MG-ADL)'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by', 'participates', 'score']
		sort: ['-id']