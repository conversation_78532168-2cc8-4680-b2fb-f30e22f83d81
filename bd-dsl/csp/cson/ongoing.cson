fields:

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'
			columns: 2

	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['subform_bleed_events']

		view:
			label: 'Primary Therapy'
			offscreen: true
			readonly: true

	therapy_2:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['subform_bleed_events']
					sections:  ['Factor Bleed Events Log']

		view:
			label: 'Secondary Therapy'
			offscreen: true
			readonly: true

	therapy_3:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['subform_bleed_events']
					sections:  ['Factor Bleed Events Log']
		view:
			label: 'Tertiary Therapy'
			offscreen: true
			readonly: true

	therapy_4:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['subform_bleed_events']
					sections:  ['Factor Bleed Events Log']
		view:
			label: 'Quaternary Therapy'
			offscreen: true
			readonly: true

	therapy_5:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['subform_bleed_events']
					sections:  ['Factor Bleed Events Log']
		view:
			label: 'Quinary Therapy'
			offscreen: true
			readonly: true


	drug_brand_1:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
			prefill: ['parent.drug_brand_1']
			if:
				'DUPIXENT SYRINGE':
					sections: ['Health Plan Questions']
					fields: ['hp_asthma_dupixent', 'hp_atopic_derm']
				'DUPIXENT PEN':
					sections: ['Health Plan Questions']
					fields: ['hp_asthma_dupixent', 'hp_atopic_derm']
				'XOLAIR':
					sections: ['Health Plan Questions']
					fields: ['hp_asthma_other', 'hp_idio_urticaria']
				'NUCALA':
					sections: ['Health Plan Questions']
					fields: ['hp_asthma_other', 'hp_idio_urticaria']
				'Faserna':
					sections: ['Health Plan Questions']
					fields: ['hp_asthma_other', 'hp_idio_urticaria']
				'GENOTROPIN':
					sections: ['Health Plan Questions']
					fields: ['hp_growth_hormone']
				'HUMATROPE':
					sections: ['Health Plan Questions']
					fields: ['hp_growth_hormone']
				'NORDITROPIN':
					sections: ['Health Plan Questions']
					fields: ['hp_growth_hormone']
				'Repatha':
					sections: ['Health Plan Questions']
					fields: ['hp_hyperlipidema']
				'Praluent':
					sections: ['Health Plan Questions']
					fields: ['hp_hyperlipidema']
				'Victoza':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'TRULICITY':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'OZEMPIC':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'BYETTA':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'EXENATIDE':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'XULTOPHY 100-3.6':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'SOLIQUA 100-33':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'BYDUREON':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'ENBREL':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'HUMIRA':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'SIMPONI':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'ACTEMRA':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'REMICADE':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'CIMZIA':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'ORENCIA':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'INFLECTRA':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'RENFLEXIS':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'ENTYVIO':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'COSENTYX':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
		view:
			label: 'Primary Drug Brand'
			columns: 3

	drug_brand_1_name:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
			prefill: ['parent.drug_brand_1']
			if:
				'DUPIXENT SYRINGE':
					sections: ['Health Plan Questions']
					fields: ['hp_asthma_dupixent', 'hp_atopic_derm']
				'DUPIXENT PEN':
					sections: ['Health Plan Questions']
					fields: ['hp_asthma_dupixent', 'hp_atopic_derm']
				'XOLAIR':
					sections: ['Health Plan Questions']
					fields: ['hp_asthma_other', 'hp_idio_urticaria']
				'NUCALA':
					sections: ['Health Plan Questions']
					fields: ['hp_asthma_other', 'hp_idio_urticaria']
				'Faserna':
					sections: ['Health Plan Questions']
					fields: ['hp_asthma_other', 'hp_idio_urticaria']
				'GENOTROPIN':
					sections: ['Health Plan Questions']
					fields: ['hp_growth_hormone']
				'HUMATROPE':
					sections: ['Health Plan Questions']
					fields: ['hp_growth_hormone']
				'NORDITROPIN':
					sections: ['Health Plan Questions']
					fields: ['hp_growth_hormone']
				'Repatha':
					sections: ['Health Plan Questions']
					fields: ['hp_hyperlipidema']
				'Praluent':
					sections: ['Health Plan Questions']
					fields: ['hp_hyperlipidema']
				'Victoza':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'TRULICITY':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'OZEMPIC':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'BYETTA':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'EXENATIDE':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'XULTOPHY 100-3.6':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'SOLIQUA 100-33':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'BYDUREON':
					sections: ['Health Plan Questions']
					fields: ['hp_diabetes']
				'ENBREL':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'HUMIRA':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'SIMPONI':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'ACTEMRA':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'REMICADE':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'CIMZIA':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'ORENCIA':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'INFLECTRA':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'RENFLEXIS':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'ENTYVIO':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
				'COSENTYX':
					sections: ['Health Plan Questions']
					fields: ['hp_anti_inflam']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Drug Brand'

	hp_asthma_dupixent:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hp_asthma_demo']
		view:
			control: 'radio'
			label: 'Continued criteria for asthma'
			columns: 2

	hp_asthma_demo:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Demonstrated adherence to asthma controller therapy that includes an ICS plus either a LABA or LTRA'
			columns: 3
	
	hp_asthma_other:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hp_asthma_demo', 'hp_asthma_resp']
		view:
			control: 'radio'
			label: 'Continued criteria for asthma'
			columns: 3

	hp_asthma_resp:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			note: '(examples may include but are not limited to a reduction in exacerbations or corticosteroid dose, improvement in forced expiratory volume over one second)'
			label: 'Member is responding positively to therapy since baseline'
			columns: 3

	hp_idio_urticaria:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hp_idio_urticaria_resp']
		view:
			control: 'radio'
			label: 'Continued criteria for chronic idiopathic urticaria'
			columns: -3

	hp_idio_urticaria_resp:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Responding positively to therapy'
			columns: 3

	hp_atopic_derm:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hp_atopic_derm_resp']
		view:
			control: 'radio'
			label: 'Continued criteria for atopic dermatitis'
			columns: -3

	hp_atopic_derm_resp:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Member is responding positively to therapy as evidenced by, including but not limited to, reduction in itching and scratching'
			columns: 3

	hp_growth_hormone:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hp_growth_hormone_resp', 'hp_growth_hormone_bone']
		view:
			control: 'radio'
			label: 'Continued therapy criteria for growth hormone use in children'
			columns: -3

	hp_growth_hormone_resp:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Member is responding positively to therapy as evidenced by increasing growth rate by 2 cm over baseline in first year'
			columns: 3

	hp_growth_hormone_bone:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Member’s bone age is <15 years if girl or <17 year if boy'
			columns: 3

	hp_hyperlipidema:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hp_hyperlipidema_resp', 'hp_hyperlipidema_adher']
		view:
			control: 'radio'
			label: 'Continued therapy for hyperlipidemia'
			columns: -3

	hp_hyperlipidema_resp:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Member is responding positively to therapy as evidenced by lab results within the last 3 months showing an LDL-C reduction since initiation of medication'
			columns: 3

	hp_hyperlipidema_adher:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'If statin, tolerant, documentation of adherence to a statin at the maximally tolerated dose'
			columns: 3

	hp_diabetes:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hp_diabetes_resp']
		view:
			control: 'radio'
			label: 'Continued criteria for type 2 diabetes'
			columns: -3

	hp_diabetes_resp:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Responding positively to therapy- HbA1c within the past 12 months'
			columns: 3

	hp_anti_inflam:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Responding positive to therapy- reduction in symptoms or continued reduction in symptoms'
			columns: 1


	portal_preference:
		model:
			multi: true
			source: ['Web Portal', 'SMS']
			default: ['SMS']
		view:
			control: 'checkbox'
			label: 'Portal Access'
			offscreen: true

	sms_opt:
		model:
			prefill: ['patient.sms_opt']
			source: ['Prompted', 'Opt-in', 'Opt-Out']
			
		view:
			control: 'radio'
			label: 'Patient is opted into SMS Messaging'
			offscreen: true
			readonly: true

	hide_sms_opt:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['sms_program_opt']
		view:
			control : 'radio'
			label: 'Hide SMS opt-in?'
			offscreen: true
			readonly: true

	sms_program_opt:
		model:
			max: 3
			source: ['No', 'Yes']
			default: 'Yes' 
			required: true
			if:
				'Yes':
					fields: ['phone_cell','email']
		view:
			control : 'radio'
			label: 'Patient wishes to opt into the SMS program?'

	phone_cell:
		model:
			prefill: ['patient.phone_cell', 'patient.phone_home']
			required: true
		view:
			format: 'us_phone'
			label: 'Patient SMS Phone'
	email:
		model:
			prefill: ['patient.email']
			required: false
			# validate: [
			# 		name: 'EmailValidator'
			# ]
		view:
			label: 'Email Address'
			note: 'Must be a valid email address for Portal Access'

	experiencing_pain:
		model:
			required: true 
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pain_scale']
		view:
			label: 'Are you experiencing any pain?'
			control: 'radio'

	pain_scale:
		model:
			required: true
			source: ['1' ,'2' ,'3' ,'4' ,'5' ,'6' ,'7' ,'8' ,'9' ,'10']
			if:
				'*':
					fields: ['cust_pain_management']
		view:
			control: 'radio'
			label: 'Pain Scale'

	cust_pain_management:
		model:
			required: true
		view:
			label: 'What are you doing to control the pain?'
			control: 'area'

	cust_pain_location:
		model:
			required: false
		view:
			label: 'Location'
			offscreen: true
			readonly: true

	cust_pain_effect:
		model:
			required: false
		view:
			label: 'Effect of pain on ADL'
			offscreen: true
			readonly: true

	cust_pain_meds:
		model:
			required: false
		view:
			label: 'Meds to help with pain'
			offscreen: true
			readonly: true

	cust_pain_scale_meds:
		model:
			required: false 
			max: 1
			min: 1
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Pain Scale (with meds)'
			offscreen: true
			readonly: true

	new_meds:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['new_meds_details']
		view:
			control : 'radio'
			label: 'Any change in medication profile?'

	new_meds_details:
		model:
			required: false
		view:
			control : 'area'
			label: 'New medications or herbal supplements details'
			offscreen: true
			readonly: true

	contact_notes:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Notes'

	subform_careplan_new:
		model:
			prefill: ['ongoing']
			multi: false
			source: 'careplan'
			type: 'subform'
		view:
			label: 'Pharmacist Care Plan'

	subform_bleed_events:
		model:
			access:
				read: ['patient']
			source: 'factor_bleed'
			multi: true
			type: 'subform'
		view:
			label: 'Factor Bleed Events Log'

	subform_therapy_1:
		model:
			source: 'ongoing_{therapy_1}'
			sourcefilter:
				'ongoing_factor': {}
				'ongoing_aat': {}
				'ongoing_ivig': {}
				'ongoing_scig': {}
				'ongoing_tnf': {}
				'ongoing_iv_tpn': {}
				'ongoing_steroid': {}
				'ongoing_inotrope': {}
				'ongoing_oncology': {}
				'ongoing_bisphosphonates': {}
				'ongoing_hepb': {}
				'ongoing_hepc': {}
				'ongoing_abx':{}
				'ongoing_iv_abx': {}
				'ongoing_ms':{}
				'ongoing_nursing':{}
				'ongoing_hiv':{}
				'ongoing_tysabri':{}

			type: 'subform'
		view:
			label: 'Primary Therapy Assessment'

	subform_therapy_2:
		model:
			source: 'ongoing_{therapy_2}'
			sourcefilter:
				'ongoing_factor': {}
				'ongoing_aat': {}
				'ongoing_ivig': {}
				'ongoing_scig': {}
				'ongoing_tnf': {}
				'ongoing_iv_tpn': {}
				'ongoing_steroid': {}
				'ongoing_inotrope': {}
				'ongoing_oncology': {}
				'ongoing_bisphosphonates': {}
				'ongoing_hepb': {}
				'ongoing_hepc': {}
				'ongoing_abx':{}
				'ongoing_iv_abx': {}
				'ongoing_ms':{}
				'ongoing_nursing':{}
				'ongoing_hiv':{}
				'ongoing_tysabri':{}
			type: 'subform'
		view:
			label: 'Secondary Therapy Assessment'

	subform_therapy_3:
		model:
			source: 'ongoing_{therapy_3}'
			sourcefilter:
				'ongoing_factor': {}
				'ongoing_aat': {}
				'ongoing_ivig': {}
				'ongoing_scig': {}
				'ongoing_tnf': {}
				'ongoing_iv_tpn': {}
				'ongoing_steroid': {}
				'ongoing_inotrope': {}
				'ongoing_oncology': {}
				'ongoing_bisphosphonates': {}
				'ongoing_hepb': {}
				'ongoing_hepc': {}
				'ongoing_abx':{}
				'ongoing_iv_abx': {}
				'ongoing_ms':{}
				'ongoing_nursing':{}
				'ongoing_hiv':{}
				'ongoing_tysabri':{}
			type: 'subform'
		view:
			label: 'Tertiary Therapy Assessment'

	subform_therapy_4:
		model:
			source: 'ongoing_{therapy_4}'
			sourcefilter:
				'ongoing_factor': {}
				'ongoing_aat': {}
				'ongoing_ivig': {}
				'ongoing_scig': {}
				'ongoing_tnf': {}
				'ongoing_iv_tpn': {}
				'ongoing_steroid': {}
				'ongoing_inotrope': {}
				'ongoing_oncology': {}
				'ongoing_bisphosphonates': {}
				'ongoing_hepb': {}
				'ongoing_hepc': {}
				'ongoing_abx':{}
				'ongoing_iv_abx': {}
				'ongoing_ms':{}
				'ongoing_nursing':{}
				'ongoing_hiv':{}
				'ongoing_tysabri':{}
			type: 'subform'
		view:
			label: 'Quaternary Therapy Assessment'

	subform_therapy_5:
		model:
			source: 'ongoing_{therapy_5}'
			sourcefilter:
				'ongoing_factor': {}
				'ongoing_aat': {}
				'ongoing_ivig': {}
				'ongoing_scig': {}
				'ongoing_tnf': {}
				'ongoing_iv_tpn': {}
				'ongoing_steroid': {}
				'ongoing_inotrope': {}
				'ongoing_oncology': {}
				'ongoing_bisphosphonates': {}
				'ongoing_hepb': {}
				'ongoing_hepc': {}
				'ongoing_abx':{}
				'ongoing_iv_abx': {}
				'ongoing_ms':{}
				'ongoing_nursing':{}
				'ongoing_hiv':{}
				'ongoing_tysabri':{},

			type: 'subform'
		view:
			label: 'Quinary Therapy Assessment'

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'ongoing'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Other Form'
					direct_attachment: 'Yes'
					form_code: '{code}'
					form_name: 'ongoing'
					source: 'Scanned Document'
					patient_id: '{patient_id}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'List of Assigned Documents'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin', 'pharm']
		update:     []
		update_all: ['admin','csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']
	collections: ['ongoing_form']
	bundle: ['patient-form']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
			['order_id']
			['order_no']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		assessment:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
		ongoing:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'created_on']
	sections_group: [
		'Assessment':
			indent: false
			hide_header: true
			tab: 'Assessment'
			fields: ['order_id', 'assessment_date', 'order_no', 'order_item_id', 'requires_nursing', 'drug_brand_1',
			'route_id', 'therapy_1', 'therapy_2', 'therapy_3',
			'therapy_4', 'therapy_5', 'brand_1',
			'brand_2', 'brand_3', 'brand_4',
			'brand_5', 'disease_1', 'disease_2', 'disease_3',
			'disease_4', 'disease_5', 'clinical_1', 'clinical_2', 'clinical_3',
			'clinical_4', 'clinical_5']
		'H&P':
			indent: false
			hide_header: true
			tab: 'H&P'
			fields: ['measurement_log']
		'DUR - Medications':
			hide_header: true
			indent: false
			tab: 'DUR'
			fields: ['patient_medications']		
		'DUR - Allergies':
			hide_header: true
			indent: false
			tab: 'DUR'
			fields: ['patient_allergies']
		'Care Plan':
			indent: false
			hide_header: true
			tab: 'Care Plan'
			fields: ['subform_careplan_new']
		'Factor Bleed Events Log':
			indent: false
			tab: 'Factor Bleed Events Log'
			fields: ['subform_bleed_events']
		'Health Plan Questions':
			indent: false
			tab: 'Health Plan Questions'
			fields: ['hp_asthma_other', 'hp_asthma_demo', 'hp_asthma_resp',
			'hp_idio_urticaria', 'hp_idio_urticaria_resp',
			'hp_atopic_derm', 'hp_atopic_derm_resp', 'hp_growth_hormone',
			'hp_growth_hormone_resp', 'hp_growth_hormone_bone', 'hp_hyperlipidema',
			'hp_hyperlipidema_resp', 'hp_hyperlipidema_adher',
			'hp_diabetes', 'hp_diabetes_resp', 'hp_anti_inflam']
		'Active Symptoms Review':
			indent: false
			tab: 'Active Symptoms Review'
			fields: ['symptoms']
		'Patient Questionnaire':
			hide_header: true
			indent: false
			tab: 'Patient Questionnaire'
			fields: ['response_to_therapy', 'response_to_therapy_rn', 'new_meds',
			'new_allergies_check', 'experiencing_pain', 'pain_scale', 'pain_management',
			'new_events', 'new_events_details', 'symptoms_filter']
		'Missed Medications':
			indent: false
			hide_header: true
			tab: 'Missed Medications'
			fields: ['missed_medications']
		'Catheter Log':
			indent: false
			hide_header: true
			tab: 'Assessment'
			fields: ['catheter_log']

		'Primary Therapy Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_1'] # subform
		'Secondary Therapy Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_2'] # subform
		'Tertiary Therapy Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_3'] # subform
		'Quaternary Therapy Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_4'] # subform
		'Quinary Therapy Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_5'] # subform

		'Primary Drug Brand Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_1'] # subform
		'Secondary Drug Brand Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_2'] # subform
		'Tertiary Drug Brand Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_3'] # subform
		'Quaternary Drug Brand Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_4'] # subform
		'Quinary Drug Brand Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_5']

		'Primary Disease Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_1'] # subform
		'Secondary Disease Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_2'] # subform
		'Tertiary Disease Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_3'] # subform
		'Quaternary Disease Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_4'] # subform
		'Quinary Disease Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_5']

		'Primary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_clinical_1'] # subform
		'Secondary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_clinical_2'] # subform
		'Tertiary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_clinical_3'] # subform
		'Quaternary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_clinical_4'] # subform
		'Quinary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_clinical_5']

		'Assigned Documents':
			indent: false
			hide_header: true
			tab: 'Assigned Documents'
			fields: ['embed_document']

		'Pharmacist Questions':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			fields: ['pharmacist_questions', 'pharm_quest_dtl']

		'Interventions':
			indent: false
			tab: 'Post-Assessment'
			fields: ['intervention']
		'Intervention Details':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			note: 'Document any ADR or Catheter related event as an intervention'
			fields: ['subform_intervention']

		'Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			fields: ['contact_notes', 'requires_pharmacist_review', 'counseling_performed','pharmacist_signature']

		'Nursing Notes':
			hide_header: true
			indent: false
			tab: 'Nursing'
			fields: ['nursing_notes']
			fields: ['contact_notes', 'requires_pharmacist_review', 'counseling_performed','pharmacist_signature']
	]

view:
	comment: 'Patient > Careplan > Ongoing Assessment'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by', 'reviewed_on', 'reviewed_by', 'contact_notes']
		sort: ['-created_on']
	label: 'Ongoing Assessment'
	open: 'read'
