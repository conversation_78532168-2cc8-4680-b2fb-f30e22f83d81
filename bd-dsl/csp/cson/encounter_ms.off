
fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

view:
	comment: 'Patient > Careplan > Encounter > Multiple Sclerosis'
