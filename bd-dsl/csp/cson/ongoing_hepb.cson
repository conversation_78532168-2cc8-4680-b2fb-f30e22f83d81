
fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	lab_results_available:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields:['lab_viral_eq', 'lab_results_week']
				'No':
					fields:['next_lab']
		view:
			control: 'radio'
			label: 'Lab results available?'
			columns: 3

	lab_viral_eq:
		model:
			type: 'decimal'
			rounding: 0.001
		view:
			label: '# Viral Equivalents / ml'
			columns: 3

	lab_results_week:
		model:
			min: 1
			max: 99
			type: 'int'
		view:
			label: 'For Therapy Week'
			columns: 3

	next_lab:
		model:
			type: 'date'
		view:
			label: 'When are the next lab results expected?'
			columns: 3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	sections:
		'Ongoing HBV - Lab':
			fields: ['lab_results_available', 'lab_viral_eq', 'lab_results_week', 'next_lab']

view:
	comment: 'Patient > Careplan > Ongoing > HBV'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Ongoing Assessment: HBV'
	open: 'read'
