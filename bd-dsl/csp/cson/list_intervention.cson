fields:

	code:
		model:
			required: true
		view:
			label: 'Name'

	ae_only:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'No'
		view:
			control : 'radio'
			label: 'AE Related Only?'

	complaint_only:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'No'
		view:
			control : 'radio'
			label: 'Complaint Related Only?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['manage']
	indexes:
		unique: [
			['code']
		]
	name: ['code']
	sections:
		'Adverse Event/Complaint Intervention Type':
			fields: ['code']

view:
	comment: 'Manage > Adverse Event/Complaint Intervention Type'
	grid:
		fields: ['code']
		sort: ['code']
	label: 'AE/Complaint Intervention Type'
	open: 'read'
