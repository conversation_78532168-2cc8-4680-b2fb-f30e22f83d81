fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	route:
		model:
			source: ['IV', 'IM', 'SubQ']
			required: true
		view:
			control : 'radio'
			label: "Route"

	date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Date of Service'

	location:
		model:
			required: true
			max: 12
			source: ['Right Arm', 'Left Arm', 'Right Leg', 'Left Leg', 'Other']
			if:
				'Other':
					fields: ['location_details']
		view:
			control : 'radio'
			label: 'Location'

	location_details:
		model:
			required: true
		view:
			label: 'Location Details:'

	drug_details:
		model:
			subfields:
				drug:
					label: 'Drug Name'
				lot:
					label: 'Drug Lot#'
				expiration:
					label: 'Expiration Date'
					type: 'date'
			type: 'json'
		view:
			control: 'grid'
			label: 'Medication Details'

	notes:
		view:
			control: 'area'
			label: 'Nursing Notes'

	had_adr:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_adr_reactions', 'subform_intervention']
					sections: ['Pharmacist Review', 'Nursing Manager Review']
		view:
			control : 'radio'
			label: 'Did the patient experience any adverse drug reactions?'

	had_catheter:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['subform_intervention']
					sections: ['Nursing Manager Review']
		view:
			control : 'radio'
			label: 'Did the patient experience any catheter related event?'

	visit_summary:
		view:
			control: 'area'
			label: 'Visit Summary'

	had_adr_reactions:
		model:
			multi: true
			access:
				read: ['patient']
			source: 'list_reaction'
		view:
			label: 'Reactions'

	subform_intervention:
		model:
			access:
				read: ['patient']
			source: 'intervention'
			multi: true
			type: 'subform'
		view:
			label: 'Interventions'

	user_role:
		model:
			if:
				'pharm':
					fields: ['reviewed_id']
		view:
			label: 'User Role'
			template: '{{user.role}}'
			offscreen: true
			readonly: true

	reviewed_id:
		model:
			source: 'user'
			sourcefilter:
				role:
					'static': 'pharm'
				group_role:
					'static': '!tech'
		view:
			label: 'Reviewed by Pharmacist'

	user_group_role:
		model:
			if:
				'supervisor':
					fields: ['reviewed_nurse_id']
		view:
			label: 'User Role'
			template: '{{user.group_role}}'
			offscreen: true
			readonly: true

	reviewed_nurse_id:
		model:
			required: false
			source: 'user'
			sourcefilter:
				group_role:
					'static': 'supervisor'
		view:
			label: 'Reviewed by Nursing Manager'

	user_name_str:
		model:
			if:
				'Kahu Agency Nurse':
					fields: ['generic_nurse_name']
				'Kahu Agency Nurse (Nurse)':
					fields: ['generic_nurse_name']
				'43392':
					fields: ['generic_nurse_name']
				'Adept Infusion Nurse':
					fields: ['generic_nurse_name']
				'Aplus Nursing Care':
					fields: ['generic_nurse_name']
				'Infusion of Care Infusion Nurse':
					fields: ['generic_nurse_name']
				'Infusion Partners 360 Infusion Nurse':
					fields: ['generic_nurse_name']
		view:
			label: 'Note By'
			readonly: true
			template: '{{user.name}}'

	generic_nurse_name:
		model:
			required: true
		view:
			label: 'Full Nurse Name'

	pharm_sign:
		model:
			required: false
			type: 'json'
		view:
			control: 'esign'
			label: 'Pharmacist Signature'
			offscreen: true
			readonly: true


	is_reviewed:  # before removing or changing the field please consider this rule first: assessment_needs_review
		model:
			source: ['No', 'Yes']
			default: 'No'
			if:
				'Yes':
					fields: ['reviewed_by_name']
		view:
			control: 'radio'
			label: 'Reviewed by Pharmacist?'
			validate: [
				name: 'ReviewedBy'
			]

	reviewed_by_name:
		model:
			required: false
			type: 'text'
		view:
			label: 'Reviewed by'
			readonly: true



	card_ack:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Is Assessment Pending Review Card Acknowldge ?'
			offscreen: true
			readonly: true

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true


model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin','liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin','csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
	sections_group: [
		'Short Nursing Visit Assessment':
			sections: [
				'Assessment Details':
						fields: ['date', 'route', 'location', 'location_details', 'drug_details', 'had_adr', 'had_adr_reactions', 'had_catheter', 'visit_summary', 'user_name_str', 'pharm_sign', 'is_reviewed', 'reviewed_by_name' ,'generic_nurse_name', 'card_ack']
				'Interventions':
					note: 'Document any ADR or Catheter related event as an intervention'
					fields: ['subform_intervention']
				'Pharmacist Review':
					fields: ['user_role', 'reviewed_id']
				'Nursing Manager Review':
					fields: ['user_group_role', 'reviewed_nurse_id']
			]
	]

	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Short Nursing Visit Assessment"
	]

view:
	comment: 'Patient > Careplan > Short Nursing Visit Assessment'
	grid:
		fields: ['created_on', 'created_by', 'date', 'updated_by', 'updated_on', 'route']
		sort: ['created_on']
	label: 'Short Nursing Visit Assessment'
