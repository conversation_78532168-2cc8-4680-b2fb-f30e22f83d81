
fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	patient_has_fever:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any fevers of 100.5 F (38 C) or higher?'
			columns:3

	patient_had_musc_pain:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any muscle or joint pain, shortness of breath or chest pain, or swelling of the arms or legs?'
			columns:3

	patient_had_dizzy:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any episodes of dizziness, light headed, or headaches?'
			columns:3

	patient_had_angina:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any episodes of tachycardia, palpitations, or angina?'
			columns:3

	patient_had_liver_issues:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any yellowing of eyes/skin, abdominal pain/swelling, or abnormal itching?'
			columns:3

	patient_had_blistering:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any skin blistering?'
			columns:3

	fatigue_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['fatigue_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any episodes of fatigue?'
			columns:3

	fatigue_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of fatigue episodes per week'
			columns:3

	nausea_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['nausea_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any episodes of nausea or vomiting?'
			columns:3

	nausea_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of nausea or vomiting episodes per week'
			columns:3

	diarrhea_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['diarrhea_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any episodes of diarrhea?'
			columns:3

	diarrhea_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of diarrhea episodes per week'
			columns:3

	black_stool_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['black_stool_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any episodes of bloody, black, or tarry stools?'
			columns:3

	black_stool_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of bloody, black, or tarry stools episodes per week'
			columns:3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		ongoing_oncology:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
		assessment_chemotherapy:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'

	sections:
		'Chemotherapy Followup':
			note: 'Ask the patient the following questions'
			fields: ['patient_has_fever', 'patient_had_musc_pain', 'patient_had_dizzy', 'patient_had_angina', 'patient_had_blistering',
			 'patient_had_liver_issues', 'fatigue_episodes', 'fatigue_episodes_cnt', 'nausea_episodes', 'nausea_episodes_cnt', 'diarrhea_episodes',
			 'diarrhea_episodes_cnt', 'black_stool_episodes', 'black_stool_episodes_cnt']

view:
	comment: 'Patient > Careplan > Ongoing > Chemotherapy'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Ongoing Assessment: Chemotherapy'
	open: 'read'
