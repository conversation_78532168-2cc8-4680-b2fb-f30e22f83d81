fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	diagnosis:
		model:
			max: 3
			min: 1
			source: ['MS', "Chron's Disease"]
			if:
				'MS':
					fields: ['ms_taken_meds']
				"Chron's Disease":
					fields: ['chron_taken_meds']
		view:
			control: 'radio'
			label: 'Diagnosis'

	touch_enrolled:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					note: 'STOP—DO NOT INFUSE. If authorization cannot be verified on-line at www.touchprogram.com OR by calling 1-************, the patient must be referred back to the healthcare provider who prescribed TYSABRI'
				'Yes':
					fields: ['enrolled_number']
		view:
			control: 'radio'
			label: 'Verified that the patient is currently authorized to receive Tysabri?'

	enrolled_number:
		model:
			required: true
		view:
			label: 'Patient Enrollment Number:'

	educated_risk:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					note: 'Provide the Patient Medication Guide to the patient'
		view:
			control: 'radio'
			label: "Has the patient received and read the Patient Medication Guide, including the section 'What should I tell my doctor and nurse before each infusion of TYSABRI?'"

	last_month_problems:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['Tysabri Contact Prescriber']
					note: "DO NOT INFUSE. Contact the healthcare provider who prescribed Tysabri and review the patient's answers"
		view:
			control: 'radio'
			label: 'Over the past month, have you had any new or worsening medical problems (such as a new or sudden change in your thinking, eyesight, balance, strength, or other problems) that have persisted over several days?'

	weaken_immune:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['Tysabri Contact Prescriber']
					note: "DO NOT INFUSE. Contact the healthcare provider who prescribed Tysabri and review the patient's answers"
		view:
			control: 'radio'
			label: 'Do you have a medical condition that can weaken your immune system, such as HIV infection or AIDS, leukemia or lymphoma, or an organ transplant, that may suggest that your body is not able to fight infections well?'

	ms_taken_meds:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['Tysabri Contact Prescriber']
					note: "DO NOT INFUSE. Contact the healthcare provider who prescribed Tysabri and review the patient's answers"
		view:
			control: 'radio'
			note: 'Please review medication list with patient on the pre-infusion checklist'
			label: 'In the past month, have you taken medicines to treat cancer or MS or any other medicines that weaken your immune system?'

	chron_taken_meds:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['Tysabri Contact Prescriber']
					note: "DO NOT INFUSE. Contact the healthcare provider who prescribed Tysabri and review the patient's answers"
		view:
			control: 'radio'
			note: 'Please review medication list with patient on the pre-infusion checklist'
			label: "In the past month have you taken, or are you currently on, any medicines other than steroid medicines, to treat cancer or Crohn's disease or any other medicines that weaken your immune system?"


	had_pml:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['have_jcv']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with Progressive multifocal leukoencephalopathy (PML)?'

	have_jcv:
		model:
			source: ['No', 'Yes', 'Unknown']
			if:
				'Yes':
					fields: ['had_ig']
		view:
			control: 'radio'
			label: 'Does the patient have presence of anti-JCV antibodies?'

	had_ig:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Wait at least 6 months after last IG treatment to avoid false-positives'
		view:
			control: 'radio'
			label: 'Has the patient been treated with IG in the last 6 months?'

	had_tnf:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Because of increased risk of PML and other infections, patients on Tysabri should not be on inhibitors of TNF-α'
					sections: ['Tysabri Contact Prescriber']
		view:
			control: 'radio'
			label: 'Are you on TNF therapy?'

	has_diabetes:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['has_insulin']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with diabetes?'

	has_insulin:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you depend on insulin to regulate blood sugar?'

	had_kidney_disease:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with kidney disease?'

	meds_taking:
		model:
			max: 32
			min: 1
			multi: true
			source:['Acyclovir','Aminoglycosides','Amphotericin','Atripla','Cisplatin','Diuretics (loops, thiazides)','NSAIDS',
				'Prograf','Proton-Pump Inhibitors','Tenofovir','Viread','Truvada','Other','None']
			if:
				'Other':
					fields: ['meds_taking_other']
		view:
			control: 'checkbox'
			label: 'Are you currently taking any of the following (concomitant nephrotoxic) drugs?'

	meds_taking_other:
		view:
			label: 'Other Drugs'

	urine_drop:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you noticed a drop in urine output?'

	physician_contact:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			required: true
			if:
				'Yes':
					fields: ['physician_approval']
				'No':
					note: "Instruct the patient to contact his/her prescriber and to reschedule an infusion as soon as possible. Continue efforts to reach the prescriber to inform him/her of the reason(s) for not infusing this patient. You will need to confirm authorization from the prescriber on the subsequent infusion"
		view:
			control: 'radio'
			label: "Were you able to contact the prescriber?"

	physician_approval:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "After discussing the patient's answers, did the prescriber authorize the patient to be infused?"

	infusion_date:
		model:
			type: 'date'
		view:
			label: 'Last Infusion Date'

	next_infusion:
		model:
			type: 'date'
		view:
			label: 'Next Infusion Date'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections_group: [
		'Tysabri Questionnaire':
			sections: [
				'Tysabri Pre-Assessment':
					fields: ['diagnosis', 'touch_enrolled', 'enrolled_number', 'educated_risk']
				'Tysabri Patient Assessment':
					fields: ['last_month_problems', 'weaken_immune', 'ms_taken_meds', 'chron_taken_meds',  'had_pml', 'have_jcv', 'had_ig']
				'Tysabri Renal Disease Risk Assessment':
					fields: ['has_diabetes', 'has_insulin', 'had_kidney_disease', 'meds_taking', 'meds_taking_other', 'urine_drop']
				'Tysabri Contact Prescriber':
					fields: ['physician_contact', 'physician_approval']
				'Tysabri Infusion Details':
					fields: ['infusion_date', 'next_infusion']
			]
		]
view:
	comment: 'Patient > Careplan > Assessment > Tysabri'
	grid:
		fields: ['diagnosis', 'touch_enrolled', 'enrolled_number', 'infusion_date', 'next_infusion']
	label: 'Assessment Questionnaire: Tysabri'
	open: 'read'
