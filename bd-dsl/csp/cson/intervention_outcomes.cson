fields:

	name:
		model:
			required: true
		view:
			label: 'Name'

	type:
		model:
			required: true
			source: ['Pharmacy', 'Shipping', 'Nursing']
		view:
			control: 'radio'
			label: 'Related Type'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['manage']
	name: ['patient_id']
	indexes:
		unique: [
			['name', 'type']
		]
	name: ['name']
	sections:
		'Intervention Outcome':
			fields: ['name', 'type']

view:
	comment: 'Manage > Intervention Outcome'
	find:
		basic: ['name']
	grid:
		fields: ['name', 'type', 'created_by', 'created_on']
		sort: ['name']
	label: 'Intervention Outcome'
	open: 'read'
