fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	flag:
		model:
			required: false
			multi: true
			source: 'file_flag'
		view:
			label: 'Flags'

	patient_file:
		model:
			required: true
			type: 'json'
		view:
			control: 'file'
			label: 'Document / File'
			note: 'Max 100MB. Only documents, images, and archives supported.'

	comments:
		model:
			max: 1024
		view:
			control: 'area'
			label: 'Comments'

	sent_to_ecpr:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			readonly: true
			label: 'Welcome Packet sent to ECPR?'

	cpr_localpath:
		view:
			columns: 2
			readonly: true
			label: 'Local Path in CPR fileshare.'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		delete:     ['admin', 'pharm', 'patient']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
	bundle: ['patient']
	name: ['created_on']
	sections:
		'Main':
			fields: ['flag', 'patient_file', 'comments', 'sent_to_ecpr', 'cpr_localpath']

view:
	comment: 'Careplan > File Attachments'
	find:
		basic: ['flag']
	grid:
		fields: ['created_on', 'created_by', 'patient_file', 'flag', 'comments']
		sort: ['-id']
	label: 'Careplan File Attachment'
