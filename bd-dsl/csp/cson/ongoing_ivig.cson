fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	route:
		model:
			prefill: ['ongoing_ivig']
			source: ['Intravenous', 'Subcutaneous']
			if:
				'Subcutaneous':
					sections: ['SubQ IG Follow Up']
		view:
			control: 'radio'
			label: 'Route'
			columns: 3
	# SubQ IG Followup
	patient_had_infection:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any infections or received antibiotic treatment since your last SubQ Ig administration?'
			columns: 3

	patient_has_fever:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient reporting fever of 100.5 F (38 C) or higher?'
			columns: 3

	seen_since_last_subq:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you seen your physician since your last SubQ Ig treatment?'
			columns: 3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'ongoing']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		ongoing_ivig:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'

	sections:
		'IG Therapy':
			fields: ['route']
		'SubQ IG Follow Up':
			fields: ['patient_had_infection', 'patient_has_fever', 'seen_since_last_subq']
view:
	comment: 'Patient > Careplan > Ongoing > IG'
	grid:
		fields: ['patient_had_infection', 'patient_has_fever']
	label: 'Ongoing Assessment: IG'
	open: 'read'
