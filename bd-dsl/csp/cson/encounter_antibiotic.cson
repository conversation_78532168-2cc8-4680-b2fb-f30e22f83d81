fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	drug_class:
		model:
			prefill: ['encounter_antibiotic']
			if:
				'Aminoglycoside':
					fields:['hearing_episodes', 'vertigo_episodes']
				'Daptomycin':
					fields:['joint_episodes']
				'*':
					fields:['vertigo_episodes']
		view:
			label: 'Drug Class'
			readonly: true

	# Disease Specific Participation - Aminoglycosides
	complications_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['complications_episodes_cnt', 'complications_comment']
		view:
			control : 'radio'
			label: 'Have you had any complications or side effects from your medications?'

	complications_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of complications per week'

	complications_comment:
		model:
			required: true
		view:
			control: 'area'
			label: 'Complications Details'

	itching_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['itching_episodes_cnt', 'itching_episodes_comment']
		view:
			control : 'radio'
			label: 'Have you had any episodes of itching, rash, or hives since starting antibiotic therapy?'

	itching_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes per week'

	itching_episodes_comment:
		model:
			required: true
		view:
			control: 'area'
			label: 'Locations of itching, rash, or hives'

	fever_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['fever_episodes_cnt', 'fever_temp']
		view:
			control : 'radio'
			label: 'Have you had any episodes of fever?'

	fever_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of fever episodes per week'

	fever_temp:
		model:
			min: 1
			type: 'int'
			rounding: 0.1
			required: true
		view:
			label: 'Fever Temperature'

	has_infection:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['has_infection_details']
		view:
			control : 'radio'
			label: 'Have you been treated for any infections since your last appointment?'

	has_infection_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'

	fatigue_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['fatigue_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any episodes of fatigue or weakness?'

	fatigue_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes per week'

	nausea_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['nausea_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any nausea/vomiting, lightheadedness, or headache during or after antibiotic administration?'

	nausea_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes per week'

	diarrhea_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['diarrhea_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any loose stools or diarrhea since starting antibiotic?'

	diarrhea_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes per week'

	taking_probiotic:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Are you currently taking a probiotic or eating yogurt on a regular basis?'

	urine_change:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Have you had any changes in the frequency or volume of urination per day?'

	urine_frequency:
		model:
			min: 1
			max: 99
		view:
			label: 'Urination Frequency (per day)'

	urine_change:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['urine_change_color', 'urine_change_cnt']
		view:
			control : 'radio'
			label: 'Have you had any changes in the color of your urine?'

	urine_change_color:
		model:
			required: true
			source: ['Darker', 'Lighter']
		view:
			control : 'radio'
			label: 'Urine Color Change'

	urine_change_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes of urine color change per week'

	oz_per_day:
		model:
			min: 1
			max: 99
			type: 'int'
		view:
			label: 'How many ounces of fluid are you drinking per day?'

	edema_lower_extremities:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Have you had any new or worsening edema of lower extremities?'

	edema_pit_score:
		model:
			max: 3
			source: ['+1', '+2', '+3', 'NA']
		view:
			control: 'radio'
			label: 'Edema Pitting Score'

	hearing_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hearing_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you noticed any changes in your hearing (i.e. fullness, noise, or ringing in the ears)?'

	hearing_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes per week'

	vertigo_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['vertigo_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you noticed any vertigo or walking instabilities?'

	vertigo_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes per week'

	joint_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['joint_episodes_cnt', 'joint_episodes_score', 'joint_episodes_comment']
		view:
			control : 'radio'
			label: 'Have you had any joint or muscle pain or tenderness since starting your therapy?'

	joint_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes per week'

	joint_episodes_score:
		model:
			required: true
			source: ['1','2','3','4','5','6','7','8','9','10']
		view:
			control: 'radio'
			label: 'Based on a scale of 1 to 10 (10 being the highest), my joint or muscle pain averages around?'

	joint_episodes_comment:
		model:
			required: true
		view:
			control: 'area'
			label: 'Location of joint, muscle pain, or tenderness'

	# Administration Details
	premed_time:
		model:
			type: 'time'
		view:
			label: 'Time premeds taken by the patient'

	# Drug Administration
	pump_type:
		model:
			prefill: ['encounter_antibiotic']
			source: {na:'NA', ambulatory:'Ambulatory', stationary:'Stationary or pole-mounted', gravity:'Gravity or rate-flow tubing', other:'Other'}
			if:
				other:
					fields: ['pump_type_other']
		view:
			control : 'radio'
			label: 'Pump'

	pump_type_other:
		model:
			prefill: ['encounter_antibiotic']
			max: 1024
			required: true
		view:
			label: 'Pump Other Details'

	antibiotic_caregiver_demo:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
		view:
			control : 'radio'
			label: 'Patient/caregiver able to return demonstrate proper antibiotic preparation, administration (if applicable)?'

	add_training:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['add_training_details']
		view:
			control : 'radio'
			label: 'Will patient/caregiver need additional training?'

	add_training_details:
		model:
			required: true
		view:
			label: 'Describe additional education requirements'

	admin_adr:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['admin_adr_detail']
		view:
			control : 'radio'
			label: 'Does patient report any ADRs associated with administration of antibiotic or that develop shortly after administration?'

	admin_adr_detail:
		model:
			required: true
			multi: true
			source: ['Drop in BP', 'Increase in BP', 'Edema', 'Drop in urine output', 'Headaches or Migraine', 'Fever and chills',
			'Allergy', 'Catheter related event', 'Other']
			if:
				'Other':
					fields: ['admin_adr_detail_other']
		view:
			control : 'checkbox'
			label: 'ADRs associated with administration'

	admin_adr_detail_other:
		model:
			required: true
		view:
			label: 'ADR Other'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true


model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'intake', 'encounter']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_antibiotic:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Patient Questionnaire - Antibiotic':
			area:'questions'
			fields: ['complications_episodes', 'complications_episodes_cnt', 'complications_comment', 'itching_episodes', 'itching_episodes_cnt', 'itching_episodes_comment', 'fever_episodes', 'fever_episodes_cnt', 'fever_temp', 'has_infection', 'has_infection_details', 'fatigue_episodes', 'fatigue_episodes_cnt', 'nausea_episodes', 'nausea_episodes_cnt', 'diarrhea_episodes', 'diarrhea_episodes_cnt', 'taking_probiotic', 'urine_change', 'urine_frequency', 'urine_change', 'urine_change_color', 'urine_change_cnt', 'oz_per_day', 'edema_lower_extremities', 'edema_pit_score', 'hearing_episodes', 'hearing_episodes_cnt', 'vertigo_episodes', 'vertigo_episodes_cnt', 'joint_episodes', 'joint_episodes_cnt', 'joint_episodes_score', 'joint_episodes_comment']
		'Administration Details':
			fields: ['drug_class', 'premed_time']
		'Drug Administration':
			fields: ['pump_type', 'pump_type_other', 'antibiotic_caregiver_demo', 'add_training', 'add_training_details', 'admin_adr', 'admin_adr_detail', 'admin_adr_detail_other']
			prefill: 'encounter_antibiotic'
view:
	comment: 'Patient > Careplan > Encounter > Antibiotic'
	label: "Patient Encounter: Antibiotic"
