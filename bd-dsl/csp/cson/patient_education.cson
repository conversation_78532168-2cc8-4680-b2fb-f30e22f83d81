fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	bundle:
		model:
			source: 'educationbundle'
		view:
			control: 'select'
			label: 'Education Bundle'
			validate: [
					name: 'BundleValidate'
			]

	materials:
		model:
			subfields:
				education:
					label: 'Item'
					source: 'education'
				required:
					source: ['No', 'Yes']
					label: 'Required'
				reviewed:
					source: ['No', 'Yes']
					label: 'Reviewed By Patient'
				comment:
					label: 'Comment'
			type: 'json'
		view:
			control: 'grid'
			note: 'Select education material to appear in patient portal'
			label: 'Education Material'
			validate: [
					name: 'MaterialValidate'
			]

	note:
		view:
			control: 'area'
			label: 'Note for patient'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	bundle: ['patient']
	name: '{created_on} - {created_by}'
	sections_group: [
		'Bundle':
			note: 'Select from a bundle or add items individually below'
			fields: ['bundle']
		'Education Material':
			fields: ['materials', 'note']
	]

view:
	comment: 'Patient > Education Material'
	find:
		basic: ['bundle']
	grid:
		fields: ['created_on', 'created_by', 'bundle', 'note']
		sort: ['-created_on']
	label: 'Patient Education'
	open: 'read'
