fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['encounter_alsfrs.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	dominant_hand:
		model:
			source: ['Right', 'Left']
			prefill: ['encounter_alsfrs']
		view:
			label: 'Dominant Hand'

	# Questionnaire
	pt_particiates:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'Yes'
			if:
				'Yes':
					sections: ['ALS C.A.R.E. Questionnaire']
					fields: ['assessment_date', 'dominant_hand']
			prefill: ['encounter_alsfrs']
		view:
			findfilter: 'Yes'
			control : 'radio'
			label: 'Will a ALSFRS-R assessment be completed today?'
	speech:
		model:
			multi:false
			source:
				4: 'Normal speech processes'
				3: 'Detectable speech disturbance'
				2: 'Intelligible with repeating'
				1: 'Speech combined with nonvocal communication'
				0: 'Loss of useful speech'
		view:
			control : 'checkbox'
			label: 'Speech'
			note: 'Listen to how the patient is speaking (if the patient is able to talk) OR ask “How do you think your speech is? Have you had any challenges with your speech?”'
			validate: [
					name: 'ALSFRSScoreValidate'
			]

	salivation:
		model:
			multi:false
			source:
				4: 'Normal'
				3: 'Slight but definite excess of saliva in mouth; may have nighttime drooling'
				2: 'Moderately excessive saliva; may have minimal drooling'
				1: 'Marked excess of saliva with some drooling'
				0: 'Marked drooling; requires constant tissue or handkerchief'
		view:
			control : 'checkbox'
			label: 'Salivation'
			note: 'Ask the patient or caregiver “Have you noticed any excess saliva (drool) when either awake or at nighttime”'
			validate: [
					name: 'ALSFRSScoreValidate'
			]

	swallowing:
		model:
			multi:false
			source:
				4: 'Normal eating habits'
				3: 'Early eating problems-occasional choking'
				2: 'Dietary consistency changes'
				1: 'Needs supplemental tube feeding'
				0: 'NPO (exclusively parenteral or enteral feeding)'
		view:
			control : 'checkbox'
			label: 'Swallowing'
			note: 'Ask the patient or caregiver “How have your meals been going? Do you notice any problems with choking or are you able to eat certain foods?”'
			validate: [
					name: 'ALSFRSScoreValidate'
			]

	handwriting:
		model:
			multi:false
			source:
				4: 'Normal'
				3: 'Slow or sloppy; all words are legible'
				2: 'Not all words are legible'
				1: 'Able to grip pen but unable to write'
				0: 'Unable to grip pen'
		view:
			control : 'checkbox'
			label: 'Handwriting'
			note: 'Ask the patient or caregiver “Have you had any issues with your writing? Are you able to make out all the words you write? Is it harder to write”'
			validate: [
					name: 'ALSFRSScoreValidate'
			]

	gastrostomy:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['cutting_food']
				'Yes':
					fields: ['cutting_food_gastrostomy']
		view:
			control : 'radio'
			label: 'Gastrostomy?'

	cutting_food:
		model:
			multi:false
			source:
				4: 'Normal'
				3: 'Somewhat slow and clumsy, but no help needed'
				2: 'Can cut most foods, although clumsy and slow; some help needed'
				1: 'Food must be cut by someone, but can still feed slowly'
				0: 'Needs to be fed'
		view:
			control : 'checkbox'
			note: 'Ask the patient or caregiver “Are you able to cut your own good? Do you need help? If you have a gastronomy are you able to perform all tasks without assistance?”'
			label: 'Cutting food'
			validate: [
					name: 'ALSFRSScoreValidate'
			]

	cutting_food_gastrostomy:
		model:
			multi:false
			source:
				4: 'Normal'
				3: 'Clumsy but able to perform all manipulations independently'
				2: 'Some help needed with closures and fasteners'
				1: 'Provides minimal assistance to caregiver'
				0: 'Unable to perform any aspect of task'
		view:
			control : 'checkbox'
			label: 'Cutting food with gastrostomy'
			note: 'Ask the patient or caregiver “Are you able to cut your own good? Do you need help? If you have a gastronomy are you able to perform all tasks without assistance?”'
			validate: [
					name: 'ALSFRSScoreValidate'
			]

	dressing_and_hygiene:
		model:
			multi:false
			source:
				4: 'Normal function'
				3: 'Independent and complete self-care with effort or decreased efficiency'
				2: 'Intermittent assistance or substitute methods'
				1: 'Needs attendant for self-care'
				0: 'Total dependence'
		view:
			control : 'checkbox'
			note: 'Ask the patient or caregiver “Are you able to get yourself dressed? Do you need assistance? Is it more difficult to get dressed daily? Do you have problems with buttons?”'
			label: 'Dressing and hygiene'
			validate: [
					name: 'ALSFRSScoreValidate'
			]

	turning_in_bed:
		model:
			multi:false
			source:
				4: 'Normal'
				3: 'Somewhat slow and clumsy, but no help needed'
				2: 'Can turn alone or adjust sheets, but with great difficulty'
				1: 'Can initiate, but not turn or adjust sheets alone'
				0: 'Helpless'
		view:
			control : 'checkbox'
			label: 'Turning in bed'
			note: 'Ask the patient or caregiver “Are you able to turn in bed? Can you adjust the sheets by yourself?”'
			validate: [
					name: 'ALSFRSScoreValidate'
			]

	walking:
		model:
			multi:false
			source:
				4: 'Normal'
				3: 'Early ambulation difficulties'
				2: 'Walks with assistance'
				1: 'Non-ambulatory functional movement only'
				0: 'No purposeful leg movement'
		view:
			control : 'checkbox'
			label: 'Walking'
			note: 'Ask the patient or caregiver “Are you able to walk? Do you need assistance when walking?”'
			validate: [
					name: 'ALSFRSScoreValidate'
			]

	climbing_stairs:
		model:
			multi:false
			source:
				4: 'Normal'
				3: 'Slow'
				2: 'Mild unsteadiness or fatigue'
				1: 'Needs assistance'
				0: 'Cannot do'
		view:
			control : 'checkbox'
			label: 'Climbing stairs'
			note: 'Ask the patient or caregiver “Are you able to climb stairs? Do you have any limitations?'
			validate: [
					name: 'ALSFRSScoreValidate'
			]

	dyspnea:
		model:
			multi:false
			source:
				4: 'None'
				3: 'Occurs when walking'
				2: 'Occurs with one or more of the following: eating, bathing, dressing (ADL)'
				1: 'Occurs at rest, difficulty breathing when either sitting or lying'
				0: 'Significant difficulty, considering using mechanical respiratory support'
		view:
			control : 'checkbox'
			label: 'Dyspnea'
			note: 'Ask the patient or caregiver “Do you have any problems breathing? Any activities that make it better or worse?”'
			validate: [
					name: 'ALSFRSScoreValidate'
			]

	orthopnea:
		model:
			multi:false
			source:
				4: 'None'
				3: 'Some difficulty sleeping at night due to shortness of breath. Does not routinely use more than two pillows'
				2: 'Needs extra pillow in order to sleep (more than two)'
				1: 'Can only sleep sitting up'
				0: 'Unable to sleep'
		view:
			control : 'checkbox'
			label: 'Orthopnea'
			note: 'Ask the patient or caregiver “Do you have any problems with shortness of breath when resting? Do you find yourself needing to be propped up?”'
			validate: [
					name: 'ALSFRSScoreValidate'
			]

	respiratory_insufficiency:
		model:
			multi:false
			source:
				4: 'None'
				3: 'Intermittent use of BiPAP'
				2: 'Continuous use of BiPAP'
				1: 'Continuous use of BiPAP during the night and day'
				0: 'Invasive mechanical ventilation by intubation or tracheostomy'
		view:
			control : 'checkbox'
			label: 'Respiratory insufficiency'
			note: 'Ask the patient or caregiver “Do you use a BiPAP machine? How often?”'
			validate: [
					name: 'ALSFRSScoreValidate'
			]

	years_since_onset:
		model:
			rounding: 0.1
			type: 'decimal'
		view:
			label: 'How many years since onset of symptoms?'

	alsfrs_score:
		model:
			rounding: 0.1
			type: 'decimal'
		view:
			label: 'ALSFRS-R Score'
			readonly: true

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_alsfrs:
			link:
				careplan_id: 'careplan_id'
			filter:
				pt_particiates: 'Yes'
			max: 'created_on'
	sections:
		'ALS C.A.R.E. Questionnaire Participates':
			fields: ['last_assessment_date', 'pt_particiates', 'assessment_date', 'dominant_hand']
		'ALS C.A.R.E. Questionnaire':
			note: 'Ask the patient the answer that best describes their usual abilities OVER THE COURSE OF THE LAST WEEK'
			fields: ['speech', 'salivation', 'swallowing', 'handwriting', 'gastrostomy', 'cutting_food',
			'cutting_food_gastrostomy', 'dressing_and_hygiene', 'turning_in_bed', 'walking', 'climbing_stairs',
			'dyspnea', 'orthopnea', 'respiratory_insufficiency', 'years_since_onset', 'alsfrs_score']
			prefill: 'encounter_alsfrs'
view:
	find:
		basic: ['pt_particiates']
	grid:
		fields: ['created_on', 'created_by', 'assessment_date', 'alsfrs_score']
		sort: ['-id']
	comment: 'Patient > Careplan > Encounter > ALS C.A.R.E.'
	label: 'Patient Encounter: ALS C.A.R.E. (ALSFRS)'
