fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	# Disease Specific Participation
	pt_disease_hidden:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'Yes'
			if:
				'Yes':
					sections: ['Difficulties', 'Weakness', 'Disability Score', 'Grip dynamometry', 'Wellness']
		view:
			control : 'radio'
			label: 'Will a disease specific assessment be completed on this visit?'

	# Difficulties
	cidp_diff_write:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable or illegible, 1 = Difficulty, 2 = Normal)'
			label: 'Writing'

	cidp_diff_utensils:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Difficult or very clumsy, 2 = Normal)'
			label: 'Using utensils'

	cidp_diff_dress:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Getting dressed'

	cidp_diff_lift:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Partial, 2 = Normal)'
			label: 'Lifting arms (straight) over head'

	cidp_diff_climbing:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Difficult or requires assistance, 2 = Normal)'
			label: 'Climbing 3 stairs'

	cidp_diff_bed:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Difficult or requires assistance, 2 = Normal)'
			label: 'Getting up from bed or chairs'

	cidp_diff_balance:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Stand on one foot for 5 seconds (using either foot)'

	cidp_diff_walk:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Difficult or requires assistance, 2 = Normal)'
			label: 'Walking 10 feet'

	# Weakness
	cidp_weakness_rhand:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Right hand'

	cidp_weakness_lhand:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Left hand'

	cidp_weakness_rleg:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Right leg/foot'

	cidp_weakness_lleg:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Left leg/foot'

	# Disability Score
	arm_disability:
		model:
			max: 256
			min: 1
			multi:false
			source: ['Inability to use either arm for any purposeful movement', 'Difficulty in using one or both arms in daily activity', 'No upper limb problems']
		view:
			control: 'radio'
			note: 'Check one'
			label: 'Arm Disability'

	leg_disability:
		model:
			max: 256
			min: 1
			multi:false
			source: ['Unable to stand and walk', 'Difficulty in walking', 'Walking not affected']
		view:
			control: 'radio'
			note: 'Check one'
			label: 'Leg Disability'

	# Grip dynamometry
	grip_right_1_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Right Hand Grip Strength 1'

	grip_right_2_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Right Hand Grip Strength 2'

	grip_right_3_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Right Hand Grip Strength 3'

	grip_left_1_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Left Hand Grip Strength 1'

	grip_left_2_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Left Hand Grip Strength 2'

	grip_left_3_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Left Hand Grip Strength 3'

	grip_right_1_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Right Hand Grip Strength 1'

	grip_right_2_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Right Hand Grip Strength 2'

	grip_right_3_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Right Hand Grip Strength 3'

	grip_left_1_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Left Hand Grip Strength 1'

	grip_left_2_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Left Hand Grip Strength 2'

	grip_left_3_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Left Hand Grip Strength 3'

	# Wellness
	since_last:
		model:
			max: 64
			min: 1
			source: ['Improved', 'Improved, then relapsed', 'No change (stable)', 'Relapsed since last infusion (worsening)', 'N/A (First Lifetime Infusion)']
			if:
				'Improved, then relapsed':
					fields: ['days_relapse', 'last_relapse']
				'Relapsed since last infusion (worsening)':
					fields: ['days_relapse', 'last_relapse']
		view:
			control : 'select'
			label: 'Since Last Infusion'

	days_relapse:
		model:
			type: 'int'
			min: 1
			required: true
		view:
			label: 'How many days after your last infusion until your symptoms reappeared?'

	last_relapse:
		model:
			type: 'date'
			required: true
		view:
			label: 'Date of last relapse'

	compared_wellness:
		model:
			max: 12
			source: ['Better', 'Same', 'Worse']
		view:
			control : 'radio'
			label: 'Compared to onset of my disease, as of today I am:'

	numbness:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['numbness_legs', 'numbness_arms', 'numbness_symmetrical']
		view:
			control : 'radio'
			label: 'Do you have any numbness?'

	numbness_legs:
		model:
			max: 32
			multi:true
			source: ['Toes', 'Foot/Ankle', 'Ankle - Mid Shin', 'Mid Shin - Knee', 'Above Knee']
		view:
			control : 'checkbox'
			note: 'Select all areas of reported numbness'
			label: 'Legs Numbness'

	numbness_arms:
		model:
			max: 32
			multi: true
			source: ['Fingers', 'Hand/Wrist', 'Elbow', 'Full Arm']
		view:
			control: 'checkbox'
			note: 'Select all areas of reported numbness'
			label: 'Arms Numbness'

	numbness_symmetrical:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['numbness_leg_level', 'numbness_arms_level']
		view:
			control : 'radio'
			label: 'Is the numbness symmetrical in both legs or both arms?'

	numbness_leg_level:
		model:
			max: 3
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control : 'radio'
			note: 'Assess the numbness level from 1 (mild) - 10 (severe)'
			label: 'Legs Numbness Level'

	numbness_arms_level:
		model:
			max: 3
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control : 'radio'
			note: 'Assess the numbness level from 1 (mild) - 10 (severe)'
			label: 'Arms Numbness Level'

	distance_walk:
		model:
			max: 32
			source: ['Less than 10 feet', '11-50 feet', '51-100 feet', '101-150 feet', '> 150 feet']
		view:
			control : 'radio'
			label: 'How far can you walk comfortably'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_ivig_cidp:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Disease Specific Participation':
			fields: ['pt_disease_hidden']
		'Difficulties':
			note: 'On a scale of zero to two (0 - 2), with 0 meaning Extreme Difficulty and 2 meaning Normal, please rate your difficulty today with:'
			fields: ['cidp_diff_write', 'cidp_diff_utensils', 'cidp_diff_dress', 'cidp_diff_lift', 'cidp_diff_climbing', 'cidp_diff_bed', 'cidp_diff_balance', 'cidp_diff_walk']
		'Weakness':
			note: 'On a scale from zero to two (0 - 2), with zero meaning Severe weakness and 2 meaning Normal, please rate the weakness in your limbs'
			fields: ['cidp_weakness_rhand','cidp_weakness_lhand','cidp_weakness_rleg','cidp_weakness_lleg']
		'Disability Score':
			fields: ['arm_disability', 'leg_disability']
		'Grip dynamometry':
			note: 'Measure strength of both hand grips 3 times'
			fields: ['grip_right_1_mc', 'grip_right_2_mc', 'grip_right_3_mc', 'grip_left_1_mc', 'grip_left_2_mc', 'grip_left_3_mc',
			'grip_right_1_pounds', 'grip_right_2_pounds', 'grip_right_3_pounds', 'grip_left_1_pounds', 'grip_left_2_pounds', 'grip_left_3_pounds']
		'Wellness':
			fields: ['since_last', 'days_relapse', 'last_relapse', 'compared_wellness', 'numbness', 'numbness_legs', 'numbness_arms','numbness_symmetrical',
				'numbness_leg_level', 'numbness_arms_level', 'distance_walk']

view:
	comment: 'Patient > Careplan > Encounter > IG > CIDP'
	label: 'Patient Encounter: IG - CIDP'
