fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	# Survey Participation
	edss_last_date:
		model:
			prefill: ['encounter_edss.edss_date']
			type: 'date'
		view:
			label: 'EDSS Last Date'
			readonly: true

	edss_last_score:
		model:
			prefill: ['encounter_edss.edss_score']
			access:
				read: ['patient']
			rounding: 0.1
			type: 'decimal'
		view:
			label: 'EDSS Last Score'
			readonly: true

	pt_edss_particiates:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'Yes'
			if:
				'Yes':
					sections: ['Expanded Disability Status Scale (EDSS)']
		view:
			control : 'radio'
			label: 'Will a Expanded Disability Status Scale (EDSS) assessment be completed today?'

	edss_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'EDSS Date'
			readonly: false
			template: '{{now}}'

	edss_scores:
		model:
			source: ['1.0 - No disability, minimal signs in one FS', '1.5 - No disability, minimal signs in more than one FS', '2.0 - Minimal disability in one FS', '2.5 - Mild disability in one FS or minimal disability in two FS', '3.0 - Moderate disability in one FS, or mild disability in three or four FS. No impairment to walking', '3.5 - Moderate disability in one FS and more than minimal disability in several others. No impairment to walking', '4.0 - Significant disability but self-sufficient and up and about some 12 hours a day. Able to walk without aid or rest for 500m', '4.5 - Significant disability but up and about much of the day, able to work a full day, may otherwise have some limitation of full activity or require minimal assistance. Able to walk without aid or rest for 300m', '5.0 - Disability severe enough to impair full daily activities and ability to work a full day without special provisions. Able to walk without aid or rest for 200m', '5.5 - Disability severe enough to preclude full daily activities. Able to walk without aid or rest for 100m', '6.0 - Requires a walking aid - cane, crutch, etc - to walk about 100m with or without resting', '6.5 - Requires two walking aids - pair of canes, crutches, etc - to walk about 20m without resting', '7.0 - Unable to walk beyond approximately 5m even with aid. Essentially restricted to wheelchair; though wheels self in standard wheelchair and transfers alone. Up and about in wheelchair some 12 hours a day', '7.5 - Unable to take more than a few steps. Restricted to wheelchair and may need aid in transferring. Can wheel self but cannot carry on in standard wheelchair for a full day and may require a motorized wheelchair', '8.0 - Essentially restricted to bed or chair or pushed in wheelchair. May be out of bed itself much of the day. Retains many self-care functions. Generally has effective use of arms', '8.5 - Essentially restricted to bed much of day. Has some effective use of arms retains some self-care functions', '9.0 - Confined to bed. Can still communicate and eat', '9.5 - Confined to bed and totally dependent. Unable to communicate effectively or eat/swallow', '10.0 - Death due to MS']
			required: true
		view:
			control: 'checkbox'
			label: 'EDSS Score'
			validate: [
					name: 'EDSSScoreValidate'
			]

	reviewed_id:
		model:
			access:
				read: ['-patient'] # HB-6623 Remove Access of Patient for Reviewed by Field
				write: ['-patient']
			source: 'user'
			sourcefilter:
				role:
					'static': 'pharm'
				group_role:
					'static': '!tech'
		view:
			label: 'Reviewed by Pharmacist'

	edss_score:
		model:
			rounding: 0.1
			type: 'decimal'
		view:
			label: 'EDSS Score'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     ['patient']
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	bundle: ['patient', 'careplan']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_edss:
			link:
				careplan_id: 'careplan_id'
			filter:
				pt_edss_particiates: 'Yes'
			max: 'created_on'
	sections:
		'Expanded Disability Status Scale (EDSS) Participation':
			fields: ['edss_last_date', 'edss_last_score', 'pt_edss_particiates']
			prefill: 'encounter_edss'
		'Expanded Disability Status Scale (EDSS)':
			note: 'EDSS steps 1.0 to 4.5 refer to people with MS who are able to walk without any aid and is based on measures of impairment in eight functional systems (FS):\n\n
•	pyramidal - weakness or difficulty moving limbs\n
•	cerebellar - ataxia, loss of coordination or tremor\n
•	brainstem - problems with speech, swallowing and nystagmus\n
•	sensory - numbness or loss of sensations\n
•	bowel and bladder function\n
•	visual function\n
•	cerebral (or mental) functions\n
•	other\n\n
Each functional system is scored on a scale of 0 (no disability) to 5 or 6 (more severe disability).\n\n
EDSS steps 5.0 to 9.5 are defined by the impairment to walking.\n'
			fields: ['edss_date', 'edss_scores', 'edss_score', 'reviewed_id']

view:
	comment: 'Patient > Careplan > Encounter > Expanded Disability Status Scale'
	grid:
		fields: ['created_on', 'edss_date', 'created_by']
		sort: ['-id']
	label: 'Patient Encounter: Expanded Disability Status Scale'
