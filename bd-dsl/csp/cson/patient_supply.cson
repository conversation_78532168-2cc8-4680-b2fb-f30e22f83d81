fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'


	deliver_by_date:
		model:
			type: 'date'
		view:
			label: 'Delivery By Date'

	delivery_street:
		model:
			prefill: ['patient_supply', 'patient.home_street']
			max: 128
			min: 4
			required: true
		view:
			label: 'Street'

	delivery_street2:
		model:
			prefill: ['patient_supply', 'patient.home_street2']
			max: 128
		view:
			label: 'Street 2'

	delivery_city:
		model:
			prefill: ['patient_supply', 'patient.home_city']
			max: 128
			min: 1
			required: true
		view:
			label: 'City'

	delivery_state:
		model:
			prefill: ['patient_supply', 'patient.home_state']
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
			required: true
		view:
			label: 'State'

	delivery_zip:
		model:
			prefill: ['patient_supply', 'patient.home_zip']
			max: 10
			min: 5
			required: true
		view:
			format: 'us_zip'
			label: 'Zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'home_zip'
						city:'home_city'
						state:'home_state'
			]

	delivery_confirmation:
		model:
			source: ['No', 'Yes']
		view:
			note: 'will default to “Signature Required”'
			control: 'radio'
			label: "Delivery Signature Required?"

	delivery_instructions:
		view:
			control: 'area'
			label: "Specific Delivery Instructions?"

	delivery_phone:
		model:
			prefill: ['patient_supply', 'patient.phone_home', 'patient.phone_cell']
			required: true
			max: 21
		view:
			format: 'us_phone'
			label: 'Delivery Phone'

	bundle:
		model:
			source: 'supplybundle'
		view:
			control: 'select'
			label: 'Supply Bundle'
			validate: [
					name: 'BundleValidate'
			]

	therapy:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'Factor':
					fields: ['subform_therapy_factor']
		view:
			label: 'Therapy'
			readonly: true

	subform_therapy_factor:
		model:
			source: 'patient_supply_factor'
			type: 'subform'
		view:
			label: 'Therapy Order Details'

	supplies:
		model:
			subfields:
				supply:
					label: 'Item'
					source: 'supplyitem'
				count:
					label: 'Count'
				comment:
					label: 'Comment'
			type: 'json'
		view:
			control: 'grid'
			label: 'Supply Items'

	comments:
		view:
			control: 'area'
			label: 'Comments'

	# Pharmacy
	ship_complete:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['ship_complete_details']
		view:
			control: 'radio'
			label: "Order will ship complete?"

	ship_complete_details:
		view:
			control: 'area'
			label: "Why not?"

	ship_date:
		model:
			type: 'date'
		view:
			label: 'Ship Date'


	delivery_date:
		model:
			type: 'date'
		view:
			label: 'Delivery Date'

	address_confirmation:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Shipping Address Verified?"

	processed_by:
		model:
			source: 'user'
		view:
			label: 'Processed By'
			template: '{{user.id}}'

	pharmacy_notes:
		view:
			control: 'area'
			label: 'Pharmacy Notes'
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     ['csr']
		update_all: ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient']
	name: '{created_by} - {created_on}'
	prefill:
		patient:
			link:
				id: 'patient_id'
		patient_supply:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
	sections_group: [
		'Delivery Details':
			fields: ['deliver_by_date', 'delivery_street', 'delivery_street2', 'delivery_city', 'delivery_state', 'delivery_zip', 'delivery_confirmation', 'delivery_instructions', 'delivery_phone']
		'Order Details':
			fields: ['bundle', 'therapy']
		'Therapy Documentation':
			sections: [
				'Therapy Order Form - Factor':
					fields: ['subform_therapy_factor']
				]
		'Order Items':
			fields: ['supplies', 'comments']
		'Pharmacy Acknowledgment':
			fields: ['ship_complete', 'ship_complete_details', 'ship_date', 'delivery_date','processed_by', 'pharmacy_notes']
	]

view:
	comment: 'Patient > Orders'
	find:
		basic: ['delivery_date', 'bundle']
	grid:
		fields: ['delivery_date', 'bundle', 'comments']
		sort: ['delivery_date', 'bundle']
	label: 'Patient Orders'
	open: 'read'