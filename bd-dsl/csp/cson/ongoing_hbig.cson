fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	patient_had_infection:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had an infection or received antibiotic treatment since your last Hepatitis B Immune Globulin (HBIG) administration?'

	patient_has_fever:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient reporting fever of 100.5 F (38 C) or higher?'

	patient_has_rash:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient reporting rash on face or other parts of the body?'

	patient_has_pain:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient reporting muscle or joint pain, shortness of breath or chest pain, or swelling of the arms or legs?'

	patient_has_dizzy:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient reporting any changes in eyesight, seizures, dizziness or passing out?'

	patient_has_weight_gain:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient reporting large weight gain?'

	seen_since_last:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you seen your physician since your last Hepatitis B Immune Globulin (HBIG) treatment?'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'ongoing']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	sections:
		'HBIG Followup':
			note: 'Ask the patient the following questions'
			fields: ['patient_had_infection', 'patient_has_fever', 'patient_has_rash', 'patient_has_pain', 'patient_has_dizzy', 'patient_has_weight_gain', 'seen_since_last']

view:
	comment: 'Patient > Careplan > Ongoing > HBIG'
	grid:
		fields: ['patient_has_fever', 'patient_has_rash', 'patient_has_pain']
	label: 'Ongoing Assessment: HBIG'
	open: 'read'
