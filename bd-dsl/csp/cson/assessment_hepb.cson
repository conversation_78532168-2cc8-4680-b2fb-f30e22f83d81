fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	gender:
		model:
			if:
				'Female':
					fields: ['are_preg', 'birth_control', 'are_postmen']
			prefill: ['patient']
		view:
			label: 'Sex'
			offscreen: true
			readonly: true

	delivery_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Drug delivery date'

	# HCV Teaching:
	understanding_therapy:
		model:
			source: ['Very Knowledgeable', 'Moderately Knowledgeable', 'Not Knowledgeable', 'No Understanding']
			if:
				'Moderately Knowledgeable':
					note: 'Review Hep B disease overview with patient, as needed'
				'Not Knowledgeable':
					note: 'Review Hep B disease overview with patient'
				'No Understanding':
					note: 'Review Hep B disease overview with patient'
		view:
			label: 'How would you rate your understanding of Hepatitis B?'
			control: 'select'

	understanding_drug:
		model:
			source: ['Very Knowledgeable', 'Moderately Knowledgeable', 'Not Knowledgeable', 'No Understanding']
			if:
				'Not Knowledgeable':
					note: 'Review drug information with patient'
				'No Understanding':
					note: 'Review drug information with patient'
		view:
			label: 'How would you rate your understanding of the drug therapy you have been prescribed?'
			note: 'Ask the patient to explain what they know about the drug therapy'
			control: 'select'

	understanding_frequency:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					note: 'Review medication instructions with patient'
		view:
			label: 'Do you know how and when to take the drug that has been prescribed?'
			note: 'Ask the patient to explain what instructions they have received'
			control: 'radio'

	understanding_importance:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					note: 'Review the importance of following the prescription as written'
		view:
			label: 'Do you understand the importance of taking the medication and continuing to take your medication as your doctor prescribed?'
			control: 'radio'

	help_remembering:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Setup a live call by a pharmacist'
		view:
			label: 'Do you need help remembering to take your medication?'
			control: 'radio'

	# HCV Details:
	genotype:
		model:
			max: 2
			source: ['1a', '1b', '2a', '2b', '2c', '2d', '3', '4', '5a', '6a']
		view:
			note: 'e.x. 1a'
			label: 'Genotype'

	il_genotype:
		model:
			source: ['CC', 'CT', 'TT']
		view:
			label: 'IL28B Genotype'

	rna_basline:
		model:
			rounding: 1
			type: 'decimal'
		view:
			label: 'Baseline HBV RNA (IU/mL)'
			note: 'numbers only. no commas'
			
	elevated_alt:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Elevated serum ALT level?'

	cirr_biop:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_cirrhosis']
		view:
			control: 'radio'
			label: 'Cirrhosis and or Liver Biopsy performed?'

	have_cirrhosis:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cirrhosis_type', 'fibrosis_stage', 'child_pugh_score']
		view:
			control: 'radio'
			label: 'Cirrhosis confirmed?'

	cirrhosis_type:
		model:
			max: 3
			min: 1
			source: ['Compensated', 'Decompensated']
		view:
			control: 'radio'
			label: 'Cirrhosis Type'

	fibrosis_stage:
		model:
			source: ['0', '1', '2', '3', '4']
		view:
			control: 'radio'
			label: 'Fibrosis Stage'

	child_pugh_score:
		model:
			type:'int'
		view:
			label: 'Child-Pugh Score'

	# General Assessment
	cancer:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you currently or have you recently been on cancer treatment?'

	tattoos:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you have any tattoos?'

	hcv_mom:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Did your mother have Hepatitis B?'

	has_hiv:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Did you have a history HIV?"

	have_exposure:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Has the patient had any recent exposure to tuberculosis (TB), HCV, or mycoses?"
	travel:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['travel_where']
		view:
			control: 'radio'
			label: 'Any previous international travel?'

	travel_where:
		view:
			label: 'Where?'

	are_preg:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you currently pregnant or have given birth in the last 6 weeks?'

	birth_control:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you currently on any birth control or hormone replacement pills?'

	are_postmen:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you postmenopausal?'

	herb_supplements:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Some herbal supplements can harm the liver, please review with patient'
		view:
			control: 'radio'
			label: 'Are you taking any herbal supplements?'

	# Renal Assessment
	has_diabetes:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['has_insulin']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with diabetes?'

	has_insulin:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you depend on insulin to regulate blood sugar?'

	had_kidney_disease:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with kidney disease?'

	meds_taking:
		model:
			max: 32
			min: 1
			multi: true
			source:['Acyclovir','Aminoglycosides','Amphotericin','Atripla','Cisplatin','Diuretics (loops, thiazides)','NSAIDS',
				'Prograf','Proton-Pump Inhibitors','Tenofovir','Viread','Truvada','Other','None']
			if:
				'Other':
					fields: ['meds_taking_other']
		view:
			control: 'checkbox'
			label: 'Are you currently taking any of the following (concomitant nephrotoxic) drugs?'

	meds_taking_other:
		view:
			label: 'Other Drugs'

	has_htn:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pressure_controlled']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with hypertension (HTN)?'

	pressure_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your blood pressure currently controlled?'

	high_chol:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['chol_controlled']
		view:
			control: 'radio'
			label: 'Is your cholesterol level high?'

	chol_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your cholesterol currently controlled?'

	heart_cond:
		model:
			max: 64
			min: 1
			multi: true
			source: ['Congestive heart failure (CHF)', 'Cardiomyopathy', 'Valve Disease', 'Congenital Defects', 'Atrial Fibrillation', 'Angina', 'Pulmonary disease', 'Other', 'None']
			if:
				'Other':
					fields: ['heart_cond_other']
		view:
			control: 'checkbox'
			label: 'Do you suffer from any of the following heart diseases?'

	heart_cond_other:
		view:
			control: 'area'
			label: 'Other Heart Diseases'

	fam_hist:
		model:
			multi: true
			source: ['Coronary Artery Disease (CAD/Atherosclerotic)', 'Angina', 'Deep Vein Thrombosis (DVT)', 'Cerebral Infarction'
				, 'Myocardial Infarction', 'Other', 'None']
			if:
				'Other':
					fields: ['fam_hist_other']
		view:
			control: 'checkbox'
			label: 'Do you or any of your Parents or siblings have a history of the following:'

	fam_hist_other:
		view:
			control: 'area'
			label: 'Other Family History'

	per_immob:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you experience prolonged periods of immobilization?'

	has_lowbp:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_sep']
		view:
			control: 'radio'
			label: 'Do your hands/extremities feel cold and clammy (do you experience low BP)?'

	had_sep:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any recent cases of sepsis or infection in blood?'

	had_throm:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with thrombophilia?'

	have_headaches:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields : ['type_headache', 'headache_assoc', 'loc_headache', 'headache_scale',
								'freq_headache', 'headache_dur']
		view:
			control: 'radio'
			label: 'Does the patient have headaches?'

	type_headache:
		model:
			max: 9
			min: 1
			source: ['Migraines', 'Sinus', 'Cluster', 'Tension']
		view:
			control: 'radio'
			label: 'Type of Headaches'

	headache_assoc:
		model:
			max: 9
			min: 1
			multi: true
			source: ['Nausea', 'Light Sensitivity', 'Sound Sensitivity', 'Smell Sensitivity', 'Aura', 'Menses']
		view:
			control: 'checkbox'
			label: 'Associated With:'

	loc_headache:
		model:
			max: 10
			min: 1
			source: ['Unilateral', 'Bilateral']
		view:
			control: 'radio'
			label: 'Location of Headaches'

	headache_scale:
		model:
			max: 1
			min: 1
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Pain Scale'

	freq_headache:
		model:
			max: 32
			min: 1
			source: ['Daily', 'Weekly', 'Monthly']
		view:
			label: 'Frequency of Headaches'

	headache_dur:
		model:
			max: 32
			min: 1
			source: ['Minutes', 'Hours', 'Days']
		view:
			label: 'Duration'

	therapy_duration:
		model:
			max: 365
			min: 1
			rounding: 1
			type: 'decimal'
		view:
			label: 'What is the prescribed length of therapy?'

	therapy_duration_unit:
		model:
			source:['days','weeks','months']
			default: 'weeks'
		view:
			label: 'Therapy Duration Type'
			control: 'radio'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections_group: [
		'Hepatitis B Questionnaire':
			sections: [
				'Hepatitis B Teaching':
					fields: ['understanding_therapy', 'understanding_drug', 'understanding_frequency', 'understanding_importance', 'help_remembering']
				'Hepatitis B Condition Details':
					fields: ['genotype', 'il_genotype', 'rna_basline', 'elevated_alt', 'cirr_biop', 'have_cirrhosis', 'cirrhosis_type', 'fibrosis_stage', 'child_pugh_score']
				'Hepatitis B General Assessment':
					fields: ['gender', 'are_preg', 'birth_control', 'are_postmen', 'cancer', 'tattoos', 'hcv_mom', 'has_hiv', 'have_exposure', 'travel', 'travel_where', 'herb_supplements']
				'Hepatitis B Renal Disease Risk Assessment':
					fields: ['has_diabetes', 'has_insulin', 'had_kidney_disease', 'meds_taking', 'meds_taking_other']
				'Hepatitis B Thromboembolic Risk Assessment':
					fields: ['has_htn', 'pressure_controlled', 'high_chol', 'chol_controlled', 'heart_cond', 'heart_cond_other', 'fam_hist', 'fam_hist_other', 'per_immob', 'has_lowbp', 'had_sep', 'had_throm']
				'Hepatitis B Headaches':
					fields: ['have_headaches', 'type_headache', 'headache_assoc', 'loc_headache',
								'headache_scale', 'freq_headache', 'headache_dur']
				'Hepatitis B Drug Details':
					fields: ['delivery_date', 'therapy_duration', 'therapy_duration_unit']
			]
		]
view:
	comment: 'Patient > Careplan > Assessment > Hepatitis B'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Hepatitis B'
	open: 'read'
