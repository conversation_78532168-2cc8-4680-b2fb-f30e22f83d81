fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	# Disease Specific Participation
	pt_disease_hidden:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'Yes'
			if:
				'Yes':
					sections: ['Difficulties', 'Timed arm suspensions', 'Timed upgaze', 'Grip dynamometry', 'Pulse oximetry', 'Wellness']
		view:
			control : 'radio'
			label: 'Will a disease specific assessment be completed on this visit?'

	# Difficulties
	myasgrav_diff_exercise:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = Normal)'
			label: 'Intolerance to exercise'

	myasgrav_diff_speech:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unintelligible, 1 = Slurred, 2 = Normal)'
			label: 'Talking / Slurred Speech'

	myasgrav_diff_chew:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Chewing'

	myasgrav_diff_swallow:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Difficulty (i.e. choking), 2 = Normal)'
			label: 'Swallowing'

	myasgrav_diff_hold_arms:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Partial, 2 = Normal)'
			label: 'Lifting arms (straight) over head'

	myasgrav_diff_breath_flat:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severely impaired, 1 = Moderately impaired, 2 = Normal)'
			label: 'Breathing while lying flat'

	myasgrav_diff_breath_act:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severely impaired, 1 = Moderately impaired, 2 = Normal)'
			label: 'Breathing with activity'

	myasgrav_diff_droop:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe (impairs vision), 1 = Moderate, 2 = Normal)'
			label: 'Drooping eyelids'

	myasgrav_diff_blur:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Moderate or intermittent, 2 = Normal)'
			label: 'Double or blurred vision'

	myasgrav_diff_fatigue:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Fatigue'

	myasgrav_diff_muscle:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Muscle ache'

	myasgrav_diff_walk:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Difficult or requires assistance, 2 = Normal)'
			label: 'Walking 10 feet'

	# Timed arm suspensions
	arms_sus:
		model:
			max: 256
			min: 1
			multi:false
			source: ['Unable to raise both arms', 'Either arm less than 60 seconds', 'Both arms more than 60 seconds']
		view:
			control: 'radio'
			label: 'Raise Arms'

	# Timed upgaze
	gaze:
		model:
			max: 256
			min: 1
			multi:false
			source: ['No upgaze', 'Upgaze less than 60 seconds', 'Upgaze more than 60 seconds']
		view:
			control: 'radio'
			label: 'Upgaze'

	# Grip dynamometry
	grip_right_1_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Right Hand Grip Strength 1'

	grip_right_2_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Right Hand Grip Strength 2'

	grip_right_3_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Right Hand Grip Strength 3'

	grip_left_1_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Left Hand Grip Strength 1'

	grip_left_2_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Left Hand Grip Strength 2'

	grip_left_3_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Left Hand Grip Strength 3'

	grip_right_1_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Right Hand Grip Strength 1'

	grip_right_2_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Right Hand Grip Strength 2'

	grip_right_3_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Right Hand Grip Strength 3'

	grip_left_1_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Left Hand Grip Strength 1'

	grip_left_2_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Left Hand Grip Strength 2'

	grip_left_3_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Left Hand Grip Strength 3'


	# Pulse oximetry
	oxy_sat:
		model:
			min: 1
			max: 100
			type: 'int'
		view:
			label: 'Oxygen saturation (%)'

	oxy_sat_time:
		model:
			type: 'time'
		view:
			label: 'Time of day'

	# Wellness
	treat_reason:
		model:
			max: 32
			source: ['Prevent recurrence?', 'Improve current symptoms?']
		view:
			control : 'radio'
			label: 'Are you being treated to:'

	since_last:
		model:
			max: 64
			min: 1
			source: ['Improved', 'Improved, then relapsed', 'No change (stable)', 'Relapsed since last infusion (worsening)', 'N/A (First Lifetime Infusion)']
			if:
				'Improved, then relapsed':
					fields: ['days_relapse', 'last_relapse']
				'Relapsed since last infusion (worsening)':
					fields: ['days_relapse', 'last_relapse']
		view:
			control : 'select'
			label: 'Since Last Infusion'

	days_relapse:
		model:
			type: 'int'
			min: 1
			required: true
		view:
			label: 'How many days after your last infusion until your symptoms reappeared?'

	last_relapse:
		model:
			type: 'date'
			required: true
		view:
			label: 'Date of last relapse'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External'
			readonly: true
			offscreen: true


model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'intake', 'encounter']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_ivig_mygrav:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Disease Specific Participation':
			fields: ['pt_disease_hidden']
		'Difficulties':
			note: 'On a scale of zero to two (0 - 2), with 0 meaning Extreme Difficulty and 2 meaning Normal, please rate your difficulty today with:'
			fields: ['myasgrav_diff_exercise', 'myasgrav_diff_speech', 'myasgrav_diff_chew', 'myasgrav_diff_swallow', 'myasgrav_diff_hold_arms',
				'myasgrav_diff_breath_flat', 'myasgrav_diff_breath_act', 'myasgrav_diff_droop', 'myasgrav_diff_blur', 'myasgrav_diff_fatigue', 'myasgrav_diff_muscle',
				'myasgrav_diff_walk']
		'Timed arm suspensions':
			note: 'Time how long the patient can hold his/her arms out without falling (0-60 seconds).'
			fields: ['arms_sus']
		'Timed upgaze':
			note: 'Time how long the patient can gaze upward without his/her eyelids drooping (fatigable weakness)'
			fields: ['gaze']
		'Grip dynamometry':
			note: 'Measure strength of both hand grips 3 times'
			fields: ['grip_right_1_mc', 'grip_right_2_mc', 'grip_right_3_mc', 'grip_left_1_mc', 'grip_left_2_mc', 'grip_left_3_mc',
			'grip_right_1_pounds', 'grip_right_2_pounds', 'grip_right_3_pounds', 'grip_left_1_pounds', 'grip_left_2_pounds', 'grip_left_3_pounds']
		'Pulse oximetry':
			fields: ['oxy_sat', 'oxy_sat_time']
		'Wellness':
			fields: ['treat_reason', 'since_last', 'days_relapse', 'last_relapse']

view:
	comment: 'Patient > Careplan > Encounter > IG > Myasthenia Gravis'
	label: 'Patient Encounter: IG - Myasthenia Gravis'
