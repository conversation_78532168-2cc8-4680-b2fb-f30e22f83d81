fields:
	name:
		model:
			required: true
		view:
			label: 'Name'

	description:
		view:
			label: 'Description'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['manage']
	indexes:
		unique: [
			['name']
		]
	name: ['name']
	sections:
		'Main':
			fields: ['name', 'description']

view:
	comment: 'Manage > Supply Item'
	find:
		basic: ['name']
	grid:
		fields: ['name', 'description']
		sort: ['name']
	label: 'Supply Item'
	open: 'read'
