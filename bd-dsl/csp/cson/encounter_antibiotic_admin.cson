fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	# Admin Step
	time_taken:
		model:
			required: true
			type: 'time'
		view:
			label: 'Time Started'
			template: '{{now}}'

	comment:
		model:
			max: 4096
		view:
			control: 'area'
			label: "Administration Step Comment"

	# Pre-infusion Flush
	pre_flush_given:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pre_flush_type', 'pre_flush_vol', 'pre_flush_comment']
		view:
			control : 'radio'
			label: 'Was a pre-infusion flush given?'

	pre_flush_type:
		model:
			max: 32
			multi: true
			source:
				['Heparin', 'Sterile Water (SW)','Normal Saline (NS)', 'Sterile Saline', 'D5W','D5NS', 'Other']
			if:
				'Other':
					fields: ['pre_flush_type_other']
		view:
			control : 'checkbox'
			note: 'Select all that apply'
			label: 'Flush Type'

	pre_flush_type_other:
		view:
			label: 'Pre-infusion flush other'

	pre_flush_vol:
		model:
			type: 'decimal'
			min: 0
		view:
			label: 'Pre-infusion (mls)'

	pre_flush_comment:
		view:
			label: 'Pre-infusion comment'

	# Post infusion flush
	post_flush_given:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['post_flush_type', 'post_flush_vol', 'post_flush_comment']
		view:
			control : 'radio'
			label: 'Was a post-infusion flush given?'

	post_flush_type:
		model:
			max: 32
			multi: true
			source:['Heparin','Sterile Water (SW)','Normal Saline (NS)','D5W','D5NS','Other']
			if:
				'Other':
					fields: ['post_flush_type_other']
		view:
			control : 'checkbox'
			note: 'Select all that apply'
			label: 'Post-infusion Type'

	post_flush_type_other:
		view:
			label: 'Post-infusion flush other'

	post_flush_vol:
		model:
			type: 'decimal'
			min: 0
		view:
			label: 'Post-infusion (mls)'

	post_flush_comment:
		view:
			label: 'Post-infusion comment'

	# Antibiotic Admin
	require_induction:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['induction']
		view:
			control: 'radio'
			label: "Will patient require induction dose and maintenance dose?"
			offscreen: true
			readonly: true

	induction:
		model:
			subfields:
				drug:
					label: 'Drug'
					sourceid: 'code'
					source: 'list_fdb_drug_brand'
				dose:
					label: 'Dose'
					type: 'decimal'
				unit:
					label: 'Dose Unit'
					source: ['mg/kg', 'mg', 'mL']
					type: 'text'
				route:
					label: 'Route'
					source: ['IV', 'IV-Push', 'IM']
					type: 'text'
				infuse:
					label: 'Infuse Over (mins)'
					type: 'int'
				frequency:
					label: 'Frequency (# of times)'
					type: 'int'
			type: 'json'
		view:
			control: 'grid'
			label: 'Induction Dose'
			offscreen: true
			readonly: true

	maintenance:
		model:
			subfields:
				drug:
					label: 'Drug'
					source: 'drug_brand'
				dose:
					label: 'Dose'
					type: 'decimal'
				unit:
					label: 'Dose Unit'
					source: ['mg/kg', 'mg', 'mL']
					type: 'text'
				route:
					label: 'Route'
					source: ['IV', 'IV-Push', 'IM']
					type: 'text'
				infuse:
					label: 'Infuse Over (mins)'
					type: 'int'
				frequency:
					label: 'Frequency (every # hrs)'
					type: 'int'
			type: 'json'
		view:
			control: 'grid'
			label: 'Maintenance Dose'
			offscreen: true
			readonly: true

	drug_brand:
		model:
			prefill: ['encounter_antibiotic_admin']
			required: true
			max: 32
			sourceid: 'code'
			source: 'list_fdb_drug_brand'
		view:
			label: 'Drug Brand'

	antibiotic_dose:
		model:
			required: true
			type: 'decimal'
			min: 0
			max: 9999
		view:
			label: 'Dose Given'

	antibiotic_unit:
		model:
			required: true
			source: ['mg/m^2', 'g', 'mg', 'units']
		view:
			control: 'radio'
			label: 'Dose Unit'

	antibiotic_time:
		model:
			type: 'int'
		view:
			label: 'Over Time Period (minutes)'

	antibiotic_drug_details:
		model:
			subfields:
				lot:
					label: 'Drug Lot#'
				expiration:
					label: 'Expiration Date'
					type: 'date'
			type: 'json'
		view:
			control: 'grid'
			label: 'Drug/Vial Details'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_antibiotic_admin:
			link:
				careplan_id: 'careplan_id'
			max: 'id'
	name: '{time_taken} : {drug_brand} {antibiotic_dose} {antibiotic_unit}'
	sections:
		'Antibiotic Admin Step':
			fields: ['time_taken']
		'Antibiotic Pre-Infusion Flush':
			fields: ['pre_flush_given', 'pre_flush_type', 'pre_flush_type_other', 'pre_flush_vol', 'pre_flush_comment']
			prefill: 'encounter_antibiotic_admin'
		'Antibiotic Administration':
			note: 'Check the drug label with the order before administering'
			fields: ['drug_brand', 'antibiotic_dose', 'antibiotic_unit',
			'antibiotic_time', 'antibiotic_drug_details']
		'Antibiotic Post-Infusion Flush':
			fields: ['post_flush_given', 'post_flush_type', 'post_flush_type_other', 'post_flush_vol', 'post_flush_comment']
			prefill: 'encounter_antibiotic_admin'
		'Antibiotic Admin Step Comment':
			fields: ['comment']
view:
	comment: 'Patient > Careplan > Encounter > Antibiotic > Admin'
	grid:
		fields: ['time_taken', 'drug_brand',  'antibiotic_dose', 'antibiotic_unit']
	label: 'Antibiotic Admin Step'
