fields:

	discharge_instructions:
		model:
			source: ['Care and monitoring of access device and site', 'Monitor for any side effects to to medication', 'Take newly prescribed medication as directed',
			'Follow-up with prescriber for any problems/concerns', 'Appointment', 'Other'
			]
			if:
				'Monitor for any side effects to to medication':
					fields: ['side_effect_med']
				'Take newly prescribed medication as directed':
					fields: ['prescribed_medication']
				'Appointment':
					fields: ['appoint_with', 'appoint_on']
				'Other':
					fields: ['discharge_instruction_other']
			# required: false
		view:
			label: 'Discharge Instructions'
			columns:3

	side_effect_med:
		model:
			required: false
		view:
			label: 'Medication details'
			columns:3

	prescribed_medication:
		model:
			required: false
		view:
			label: 'Newly prescribed Medication As Directed'
			columns:3

	appoint_with:
		model:
			required: false
		view:
			label: 'Appointment With'
			columns:3

	appoint_on:
		model:
			required: false
			type: 'datetime'
		view:
			label: 'Appointment On'
			columns:3

	discharge_instruction_other:
		model:
			required: true
		view:
			control : 'area'
			label: 'Discharge Instruction Details'
			columns:3

	plan_trat_reviewed_with:
		model:
			source: ['Patient', 'Caregiver', 'RPh']
			if:
				'RPh':
					fields: ['rph_details']
			required: false
		view:
			label: 'Plan of Treatment reviewed with'
			columns:3

	rph_details:
		model:
			required: false
		view:
			label: 'RPh Details'
			columns:3

	pt_signature:
		model:
			required: false
			type: 'json'
		view:
			control: 'esign'
			label: 'Patient/Caregiver Signature'
			columns:3

	pt_signature_on:
		model:
			required: false
			type: 'datetime'
		view:
			label: 'Patient/Caregiver Signature Date'
			columns:3

	nurse_signature:
		model:
			required: false
			type: 'json'
		view:
			control: 'esign'
			label: 'Nurse Signature'
			columns:3

	nurse_signature_on:
		model:
			required: false
			type: 'datetime'
		view:
			label: 'Nurse Signature Date'
			columns:3

	authorized_certify:
		model:
			source: {
			certify	: 'I CERTIFY that the above home health services are required and authorized by me. This client is under my care and is in need of skilled nursing
			and/or therapeutic services. I will review the written plan of treatment in accordance with California Specialty Pharmacy company policy.'}
		view:
			control: 'checkbox'
			label: 'Authorized Certify'
			columns:3

	physician_name:
		model:
			required: false
		view:
			label: 'Physician’s Name'
			columns:3

	physician_fax:
		model:
			required: false
		view:
			label: 'Physician’s Fax'
			columns:3

	physician_signature:
		model:
			required: false
			type: 'json'
		view:
			control: 'esign'
			label: 'Physicians Signature'
			columns:3

	physician_signature_on:
		model:
			required: false
			type: 'datetime'
		view:
			label: 'Physicians Signature Date'
			columns:3.1

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true



model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin','liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin','csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']
	name: ['created_on']

	sections:
		'Discharge Instructions':
			hide_header: true
			fields: [ 'discharge_instructions', 'side_effect_med', 'prescribed_medication', 'appoint_with', 'appoint_on',
			 'discharge_instruction_other', 'plan_trat_reviewed_with', 'rph_details', 'pt_signature', 'pt_signature_on'
			 'nurse_signature', 'nurse_signature_on', 'physician_name', 'physician_fax', 'physician_signature', 'physician_signature_on', 'authorized_certify'
			 ]
view:
	comment: 'Discharge Instructions'
	label: 'Discharge Instructions'
