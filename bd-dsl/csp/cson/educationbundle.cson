fields:
	name:
		model:
			required: true
		view:
			label: 'Bundle Name'

	items:
		model:
			source:'education'
			multi: true
			required: true
		view:
			control: 'select'
			label: 'Education Items'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		unique: [
			['name']
		]
	name: '{name}'
	sections:
		'Main':
			fields: ['name', 'items']

view:
	comment: 'Manage > Education Bundle'
	find:
		basic: ['name']
	grid:
		fields: ['name', 'items']
		sort: ['name']
	label: 'Education Bundle'
	open: 'read'
