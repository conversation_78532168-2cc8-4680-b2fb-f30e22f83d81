
fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	shortness_breath:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you been experiencing shortness of breath?'
			columns:3

	appetite_changes:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['appetite_changes_details']
		view:
			control: 'radio'
			label: 'Have you been experience any loss or change of appetite?'
			columns:3

	appetite_changes_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Appetite Changes'
			columns:3

	lor_symptoms:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields:['lor_symptoms_list']
		view:
			control: 'radio'
			label: 'Are you experiencing any signs and symptoms of lack of response to Hepatitis C medication?'
			columns:3

	lor_symptoms_list:
		model:
			multi: true
			source: ['Increase Fatigue', 'Anorexia', 'Weakness', 'Jaundice', 'Abdominal Pain', 'Dark Urine', 'Other']
			if:
				'Other':
					fields: ['lor_symptoms_details']
		view:
			note: 'Select All That Apply'
			control: 'checkbox'
			label: 'Which ones?'
			columns:3.1

	lor_symptoms_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Other Hepatitis C signs and symptoms'
			columns:3

	lab_results_available:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields:['lab_viral_eq', 'lab_results_week']
				'No':
					fields:['next_lab']
		view:
			control: 'radio'
			label: 'Lab results available?'
			columns:3

	lab_viral_eq:
		model:
			type: 'decimal'
			rounding: 0.001
		view:
			label: '# Viral Equivalents / ml'
			columns:3

	lab_results_week:
		model:
			min: 1
			max: 99
			type: 'int'
		view:
			label: 'For Therapy Week'
			columns:3

	next_lab:
		model:
			type: 'date'
		view:
			label: 'When are the next lab results expected?'
			columns:3

	follow_up:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields:['scr_achieved']
		view:
			control: 'radio'
			label: 'Is this a 12-week completion follow-up?'
			columns:3

	scr_achieved:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields:['scr_achieved_details']
		view:
			control: 'radio'
			label: 'Has SVR been achieved?'
			columns:3

	scr_achieved_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Why not?'
			columns:3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true



	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	sections:
		'Ongoing HepC - Patient Questionnaire':
			fields: ['shortness_breath', 'appetite_changes', 'appetite_changes_details']
		'Ongoing HepC - Response To Therapy':
			fields: ['lor_symptoms', 'lor_symptoms_list', 'lor_symptoms_details']
		'Ongoing HepC - Lab':
			fields: ['lab_results_available', 'lab_viral_eq', 'lab_results_week', 'next_lab', 'follow_up', 'scr_achieved', 'scr_achieved_details']

view:
	comment: 'Patient > Careplan > Ongoing > Hepatitis C'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Ongoing Assessment: Hepatitis C'
	open: 'read'
