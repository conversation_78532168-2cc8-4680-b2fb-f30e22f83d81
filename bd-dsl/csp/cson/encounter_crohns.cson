fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	# Disease Specific Participation
	diarrhea_episodes:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['diarrhea_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any episodes of diarrhea?'

	diarrhea_episodes_cnt:
		model:
			type: 'int'
			min: 1
			required: true
		view:
			label: 'Number of episodes per week'

	abdominal_episodes:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['abdominal_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any episodes of abdominal pain or tenderness?'

	abdominal_episodes_cnt:
		model:
			type: 'int'
			min: 1
			required: true
		view:
			label: 'Number of episodes per week'

	recal_bleeding_episodes:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['recal_bleeding_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any episodes of rectal bleeding?'

	recal_bleeding_episodes_cnt:
		model:
			type: 'int'
			min: 1
			required: true
		view:
			label: 'Number of episodes per week'

	lesions_episodes:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['lesions_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any episodes of perianal lesions?'

	lesions_episodes_cnt:
		model:
			type: 'int'
			min: 1
			required: true
		view:
			label: 'Number of episodes per week'

	dr_report_abcscess:
		model:
			max: 32
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Has your physician reported any new fistula, perforations, abscess?'

	pt_report_wt_gain:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_weight_issue_details']
		view:
			control : 'radio'
			label: 'Have you had any loss of appetite or severe weight loss?'

	had_weight_issue_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'intake', 'encounter']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_tnf_crohns:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		"Patient Questionnaire: TNF - Crohn's":
			area:'questions'
			fields: ['diarrhea_episodes', 'diarrhea_episodes_cnt', 'abdominal_episodes', 'abdominal_episodes_cnt',
			'recal_bleeding_episodes', 'recal_bleeding_episodes_cnt', 'lesions_episodes', 'lesions_episodes_cnt', 'dr_report_abcscess', 'pt_report_wt_gain', 'had_weight_issue_details']

view:
	comment: 'Patient > Careplan > Encounter > TNF-Inhibitor > Crohn\'s Disease'
	label: "Patient Encounter: TNF-Inhibitor - Crohn's Disease"
