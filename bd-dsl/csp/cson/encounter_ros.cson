fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	gender:
		model:
			prefill: ['patient']
			if:
				'Male':
					fields: ['repo_problems_male']
				'Female':
					fields: ['repo_problems_female', 'repo_preg']
		view:
			label: 'Sex'
			offscreen: true
			readonly: true

	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			prefill: ['careplan_order.therapy_id']
			required: false
			if:
				'tpn':
					sections: ['ROS - Headaches', 'ROS - Nutrition - TPN']
					fields: ['skin_mucous_mem', 'skin_oral']
				'ig':
					sections: ['ROS - Reproductive', 'ROS - Nutrition']
				'tnf':
					sections: ['ROS - Headaches', 'ROS - Nutrition']
				'factor':
					sections: ['ROS - Headaches', 'ROS - Nutrition']
				'antibiotic':
					sections: ['ROS - Reproductive', 'ROS - Headaches', 'ROS - Nutrition']
		view:
			label: 'Primary Therapy'
			offscreen: false
			readonly: false
			columns: 3.1

	therapy_2:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			prefill: ['parent.therapy_2']
			if:
				'tpn':
					sections: ['ROS - Headaches', 'ROS - Nutrition - TPN']
					fields: ['skin_mucous_mem', 'skin_oral']
				'ig':
					sections: ['ROS - Reproductive', 'ROS - Nutrition']
				'tnf':
					sections: ['ROS - Headaches', 'ROS - Nutrition']
				'factor':
					sections: ['ROS - Headaches', 'ROS - Nutrition']
				'antibiotic':
					sections: ['ROS - Reproductive', 'ROS - Headaches', 'ROS - Nutrition']
		view:
			label: 'Secondary Therapy'
			offscreen: true
			readonly: true

	therapy_3:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			prefill: ['parent.therapy_3']
			if:
				'tpn':
					sections: ['ROS - Headaches', 'ROS - Nutrition - TPN']
					fields: ['skin_mucous_mem', 'skin_oral']
				'ig':
					sections: ['ROS - Reproductive', 'ROS - Nutrition']
				'tnf':
					sections: ['ROS - Headaches', 'ROS - Nutrition']
				'factor':
					sections: ['ROS - Headaches', 'ROS - Nutrition']
				'antibiotic':
					sections: ['ROS - Reproductive', 'ROS - Headaches', 'ROS - Nutrition']
		view:
			label: 'Tertiary Therapy'
			offscreen: true
			readonly: true

	therapy_4:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			prefill: ['parent.therapy_4']
			if:
				'tpn':
					sections: ['ROS - Headaches', 'ROS - Nutrition - TPN']
					fields: ['skin_mucous_mem', 'skin_oral']
				'ig':
					sections: ['ROS - Reproductive', 'ROS - Nutrition']
				'tnf':
					sections: ['ROS - Headaches', 'ROS - Nutrition']
				'factor':
					sections: ['ROS - Headaches', 'ROS - Nutrition']
				'antibiotic':
					sections: ['ROS - Reproductive', 'ROS - Headaches', 'ROS - Nutrition']
		view:
			label: 'Quaternary Therapy'
			offscreen: true
			readonly: true

	therapy_5:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			prefill: ['parent.therapy_5']
			if:
				'tpn':
					sections: ['ROS - Headaches', 'ROS - Nutrition - TPN']
					fields: ['skin_mucous_mem', 'skin_oral']
				'ig':
					sections: ['ROS - Reproductive', 'ROS - Nutrition']
				'tnf':
					sections: ['ROS - Headaches', 'ROS - Nutrition']
				'factor':
					sections: ['ROS - Headaches', 'ROS - Nutrition']
				'antibiotic':
					sections: ['ROS - Reproductive', 'ROS - Headaches', 'ROS - Nutrition']
		view:
			label: 'Quinary Therapy'
			offscreen: true
			readonly: true

	disease_1:
		model:
			prefill: ['parent.disease_1']
			if:
				'psoriasis':
					fields: ['hent_psoriasis_loc', 'hent_psoriasis_severe']
				'parthritis':
					fields: ['hent_psoriasis_loc', 'hent_psoriasis_severe']
		view:
			label: 'Primary Disease'
			offscreen: true
			readonly: true

	disease_2:
		model:
			prefill: ['parent.disease_2']
			if:
				'psoriasis':
					fields: ['hent_psoriasis_loc', 'hent_psoriasis_severe']
				'parthritis':
					fields: ['hent_psoriasis_loc', 'hent_psoriasis_severe']
		view:
			label: 'Secondary Disease'
			offscreen: true
			readonly: true

	disease_3:
		model:
			prefill: ['parent.disease_3']
			if:
				'psoriasis':
					fields: ['hent_psoriasis_loc', 'hent_psoriasis_severe']
				'parthritis':
					fields: ['hent_psoriasis_loc', 'hent_psoriasis_severe']
		view:
			label: 'Tertiary Disease'
			offscreen: true
			readonly: true

	disease_4:
		model:
			prefill: ['parent.disease_4']
			if:
				'psoriasis':
					fields: ['hent_psoriasis_loc', 'hent_psoriasis_severe']
				'parthritis':
					fields: ['hent_psoriasis_loc', 'hent_psoriasis_severe']
		view:
			label: 'Quaternary Disease'
			offscreen: true
			readonly: true

	disease_5:
		model:
			prefill: ['parent.disease_5']
			if:
				'psoriasis':
					fields: ['hent_psoriasis_loc', 'hent_psoriasis_severe']
				'parthritis':
					fields: ['hent_psoriasis_loc', 'hent_psoriasis_severe']
		view:
			label: 'Quinary Disease'
			offscreen: true
			readonly: true

	# ROS - HENT
	hent_denies:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['hent_problems_head_neck', 'hent_problems_eye', 'hent_problems_nose', 'hent_problems_mouth_throat', 'hent_problems_skin']
		view:
			control: 'radio'
			label: 'Patient reports having HENT problems?'
			columns: 4

	hent_problems_head_neck:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['hent_head_neck', 'hent_head_neck_comments']
		view:
			control: 'radio'
			label: 'Patient reports having Head and Neck problems?'
			columns: 4

	hent_head_neck:
		model:
			max: 128
			multi: true
			source: ['Masses','Hearing Loss', 'Tubes in ears', 'Hearing Aids', 'Ear Drainage', 'Deformity','Alopecia','Other']
			if:
				'Other':
					fields: ['hent_head_neck_other']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Head and Neck Problems'
			columns: 4

	hent_head_neck_other:
		model:
			required: true
		view:
			label: 'Other Problem Details'
			columns: 4

	hent_head_neck_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Head and Neck Comments/Pertinent History'
			columns:4

	hent_problems_eye:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['hent_eyes', 'hent_eyes_comments']
		view:
			control: 'radio'
			label: 'Patient reports having Eye problems?'
			columns: -4

	hent_eyes:
		model:
			max: 128
			multi: true
			source: ['Blind', 'Blurred Vision', 'Drop in vision', 'Nystagmus', 'Itching', 'Redness', 'Eyeglasses', 'Reading glasses', 'Other']
			if:
				'Other':
					fields: ['hent_eyes_other']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Eye Problems'
			columns: 4

	hent_eyes_other:
		model:
			required: true
		view:
			label: 'Other Problem Details'
			columns: 4

	hent_eyes_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Eye Comments/Pertinent History'
			columns:4

	hent_problems_nose:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['hent_nose', 'hent_nose_comments']
		view:
			control: 'radio'
			label: 'Patient reports having any Nose problems?'
			columns: 4

	hent_nose:
		model:
			max: 128
			multi: true
			source: ['Congestion','Discharges','Epistaxis','Nasal Flaring', 'Other']
			if:
				'Other':
					fields: ['hent_nose_other']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Nose Problems'
			columns: 4

	hent_nose_other:
		model:
			required: true
		view:
			label: 'Other Problem Details'
			columns: 4

	hent_nose_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Nose Comments/Pertinent History'
			columns:4

	hent_problems_mouth_throat:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['hent_mouth_throat', 'hent_mouth_throat_comments', 'skin_oral']
		view:
			control: 'radio'
			label: 'Patient reports having any Mouth and Throat problems?'
			columns: 4

	hent_mouth_throat:
		model:
			max: 128
			multi: true
			source: ['Full Dentures','Partials','Lesions','Dysphagia','Inflammed', 'Bleeding Gums', 'Drooling', 'Hoarseness', 'Difficulty Swallowing', 'Other']
			if:
				'Other':
					fields: ['hent_mouth_throat_other']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Mouth and Throat Problems'
			columns: 4

	hent_mouth_throat_other:
		model:
			required: true
		view:
			label: 'Other Problem Details'
			columns: 4

	hent_mouth_throat_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Mouth and Throat Comments/Pertinent History'

	hent_problems_skin:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['hent_skin', 'hent_skin_comments']
		view:
			control: 'radio'
			label: 'Patient reports having any Skin problems?'
			columns: 4

	hent_skin:
		model:
			max: 128
			multi: true
			source: ['Pale','Cyanotic','Flushed', 'Warm','Cool',
				'Bruises','Rash','Dry','Tenting','Other']
			if:
				'Other':
					fields: ['hent_skin_other']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Skin Problems'
			columns: 4

	hent_skin_other:
		model:
			required: true
		view:
			label: 'Other Problem Details'
			columns: 4

	hent_skin_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Skin Comments/Pertinent History'
			columns: 4


	hent_psoriasis_loc:
		model:
			max: 128
			multi: true
			source: ['Hands','Feet','Face','Scalp','Groin','Nails','Other']
			if:
				'Other':
					fields: ['hent_psoriasis_loc_other']
		view:
			control: 'checkbox'
			note: 'Select any that apply'
			label: 'Location of psoriasis'
			columns: 4

	hent_psoriasis_loc_other:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Location of psoriasis other'
			columns: 4

	hent_psoriasis_severe:
		model:
			max: 128
			source: ['Mild (up to 3% BSA)','Moderate (3-10% BSA)','Severe (greater than 10% BSA)']
			if:
				'Severe (greater than 10% BSA)':
					fields: ['hent_psoriasis_bsa']
		view:
			control: 'radio'
			note: 'Select any that apply'
			label: 'Psoriasis Severity'
			columns: 4

	hent_psoriasis_bsa:
		model:
			min: 1
			max: 100
			type: 'int'
		view:
			label: 'BSA (%)'
			columns: 4

	# ROS - Neurology
	neuro_denies:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['neuro_problems', 'neuro_comments']
		view:
			control: 'radio'
			label: 'Patient reports having Neurology problems?'
			columns: 4

	neuro_problems:
		model:
			max: 128
			multi: true
			source: ['Headaches','Disoriented','Forgetful','Memory Loss','Pupils Unequal','Paresthesia','Numbness', 'Tingling', 'Paralysis','Hemiplegic','Paraplegic','Spasms','Syncope', 'Tremors','Seizures','Dizziness', 'Weakness', 'Vertigo','Gait Problems','Impaired Speech','Unresponsive','Other']
			if:
				'Other':
					fields: ['neuro_problems_other']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Neurology Problems'
			columns: 4

	neuro_perrla:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'PERRLA'
			columns: 4

	neuro_problems_other:
		model:
			required: true
		view:
			label: 'Other Problem Details'
			columns: 4

	neuro_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Neurology Comments/Pertinent History'
			columns:3

	# ROS - Headaches
	has_headaches:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['headache_type', 'headache_assoc', 'headache_pain', 'headache_num', 'headache_freq', 'headache_dur', 'headache_dur_type', 'headache_med']
		view:
			control: 'radio'
			label: 'Patient reports having headaches?'
			columns: 4

	headache_type:
		model:
			required: true
			max: 16
			source: ['Migraine', 'Sinus', 'Cluster', 'Tension']
		view:
			control: 'radio'
			label: 'Type of headaches'
			columns: 4

	headache_assoc:
		model:
			max: 16
			source:
				nausea: 'Nausea'
				light_sensitive: 'Light Sensitivity'
				sound_sensitive: 'Sound Sensitivity'
				smell_sensitive: 'Smell Sensitivity'
				aura: 'Aura'
				menses: 'Menses'
		view:
			control: 'radio'
			label: 'Headaches associated with'
			columns: 4

	headache_pain:
		model:
			required: true
			max: 16
			source: ['1','2','3','4','5','6','7','8','9','10']
		view:
			control: 'radio'
			label: 'Headaches Pain Scale'
			columns: 4

	headache_num:
		model:
			max: 1000
			min: 1
			required: true
			rounding: 1
			type: 'decimal'
		view:
			note: 'Number of headaches per (day/week/month)'
			label: 'Headache Frequency'
			columns: 4

	headache_freq:
		model:
			max: 6
			min: 1
			required: true
			source: ['Days','Weeks','Months']
		view:
			control: 'radio'
			label: 'Headache Frequency Range'
			columns: 4

	headache_dur:
		model:
			max: 24
			min: 1
			required: true
			rounding: 1
			type: 'decimal'
		view:
			label: 'Headache Duration'
			columns: 4

	headache_dur_type:
		model:
			default: 'Hours'
			source: ['Hours', 'Days', 'Weeks']
		view:
			control: 'radio'
			label: 'Headache Duration'
			columns: 4

	headache_med:
		model:
			type: 'date'
		view:
			label: 'Last time medication was taken for headache relief'
			columns: 4

	headache_comments:
		view:
			control: 'area'
			label: 'Headache Comments/Pertinent History'
			columns:3

	# ROS - Endocrine
	endo_denies:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['endo_problems', 'endo_comments']
		view:
			control: 'radio'
			label: 'Patient reports having Endocrine problems?'
			columns: 4

	endo_problems:
		model:
			max: 128
			multi: true
			source: ['Diabetes','Other']
			if:
				'Other':
					fields: ['endo_problems_other']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Endocrine Problems'
			columns: 4

	endo_problems_other:
		model:
			required: true
		view:
			label: 'Other Problem Details'
			columns: 4

	endo_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Endocrine Comments/Pertinent History'
			columns:3

	# ROS - Musculoskeletal
	musc_denies:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['musc_problems', 'musc_comments']
		view:
			control: 'radio'
			label: 'Patient reports having Musculoskeletal problems?'
			columns: 4

	musc_problems:
		model:
			max: 128
			multi: true
			source: ['Amputation','Atrophy','Muscle Cramping','Muscle Fatigue',
				'Muscle Weakness','Contractures','Joint Tenderness','Spinal Problems',
				'Decreased ROM','Assisted Devices', 'Rigidity', 'Tremors', 'Uncontrolled Movements',
				'Numbness', 'Tingling', 'Spasms', 'Other']
			if:
				'Other':
					fields: ['musc_problems_other']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Musculoskeletal Problems'
			columns: 4

	musc_problems_other:
		model:
			required: true
		view:
			label: 'Other Problem Details'
			columns: 4

	musc_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Musculoskeletal Comments/Pertinent History'
			columns:3

	# ROS - Respiratory
	resp_denies:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['resp_problems', 'resp_breathing', 'resp_cough', 'resp_oxygen', 'resp_comments']
		view:
			control: 'radio'
			label: 'Patient reports having Respiratory problems?'
			columns: 4

	resp_problems:
		model:
			max: 128
			multi: true
			source: ['CLR','SOB','Dyspnea','Orthopena', 'Intercostal Retractions']
			if:
				'Orthopena':
					fields: ['resp_ortho_o2', 'resp_ortho_rate']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Respiratory Problems'
			columns: 2

	resp_ortho_o2:
		model:
			min: 1
			max: 100
			type: 'int'
		view:
			label: 'Orthopena O2 (%)'
			columns: 3

	resp_ortho_rate:
		view:
			label: 'Orthopnea Rate (L/min)'
			columns: 3

	resp_breathing:
		model:
			max: 24
			source:
				absent: 'Absent'
				diminished: 'Diminished'
				rales: 'Rales'
				wheezes: 'Wheezes'
				tubular: 'Tubular'
		view:
			control: 'radio'
			label: 'Breathing Sounds'
			columns: 3

	resp_cough:
		model:
			max: 24
			source: ['No', 'Dry', 'Productive']
			if:
				'Productive':
					fields: ['resp_cough_color']
		view:
			control: 'radio'
			label: 'Cough?'
			columns: 3

	resp_cough_color:
		model:
			max: 24
			source: ['White', 'Yellow', 'Green', 'Pink', 'Red']
		view:
			control: 'radio'
			label: 'Sputum Color'
			columns: 3

	resp_oxygen:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['resp_oxygen_type', 'resp_oxygen_amt', 'resp_pulseox']
		view:
			control: 'radio'
			label: 'Patient on Oxygen?'
			columns: 3

	resp_oxygen_type:
		model:
			source: ['Nasal Cannula', 'Mask']
		view:
			label: 'Oxygen Apparatus'
			columns: 3

	resp_oxygen_amt:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Oxygen (lpm)'
			columns: 3

	resp_pulseox:
		model:
			max: 100
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Pulse Oximeter Reading (%)'
			columns: 3

	resp_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Respiratory Comments/Pertinent History'
			columns:3

	# ROS - Cardiovascular
	card_denies:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['card_problems', 'card_comments']
		view:
			control: 'radio'
			label: 'Patient reports having Cardiovascular problems?'
			columns: 4

	card_problems:
		model:
			max: 128
			multi: true
			source: ['Anemia', 'Irregular Pulse','Tachycardia','Bradycardia',
				'Murmurs','Palpitation','Chest Pain','Extra Sounds',
				'Neck Vain Distention','Hypotention','Hypertension','Edema', 'Pallor', 'Poor Capillary Refill', 'Cardiac/Apnea Monitoring']
			if:
				'Edema':
					fields: ['card_edema_loc', 'card_edema_pit']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Cardiovascular Problems'
			columns: 2

	card_edema_loc:
		model:
			required: true
		view:
			label: 'Edema Location'
			columns: 2

	card_edema_pit:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['card_edema_pit_score']
		view:
			control: 'radio'
			label: 'Edema Pitting?'
			columns: 2

	card_edema_pit_score:
		model:
			max: 3
			required: true
			source: ['+1', '+2', '+3']
		view:
			control: 'radio'
			label: 'Edema Pitting Score'
			columns: 2

	card_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Cardiovascular Comments/Pertinent History'
			columns:3

	# ROS - Nutrition
	nut_diet:
		model:
			max: 8
			min: 1
			source: ['Poor', 'Fair', 'Good']
		view:
			control: 'radio'
			label: "How has the patient's diet been?"
			columns: 2

	nut_diet_type:
		model:
			multi:true
			source: ['Regular', 'Low Sodium', 'Diabetic', 'Fluid Restriction']
		view:
			control: 'radio'
			label: "Diet Restrictions"
			columns: 2

	nut_app:
		model:
			max: 8
			min: 1
			source: ['Poor', 'Fair', 'Good']
		view:
			control: 'radio'
			label: "How has the patient's appetite been?"
			columns: 2

	nut_parenteral:
		model:
			multi: true
			source: ['Parenteral', 'Enteral Feeding', 'None']
			if:
				'Parenteral':
					fields: ['nut_parenteral_total', 'nut_feeding_tol']
				'Enteral Feeding':
					fields: ['nut_enteral_type', 'nut_enteral_route', 'nut_feeding_tol', 'nut_enteral_time', 'tot_nut_enteral_time', 'tot_nut_enteral_onhand']
		view:
			control: 'checkbox'
			label: "Has the patient been undergoing nutrition support?"
			columns: 2

	nut_parenteral_total:
		model:
			min: 1
			max: 24
			required: true
		view:
			label: "Parenteral Nutrition Duration (hrs/day)"
			columns: 2

	nut_enteral_type:
		model:
			max: 1024
		view:
			label: "Enteral Formula Type"
			columns: 2

	nut_enteral_route:
		model:
			source: ['Nasogastric Tube', 'Gastro-Tube', 'Jejunostomy Tube', 'Gastrostomy-Jejunostomy Tube', 'Transpyloric/Nasojejunal', 'Other']
		view:
			control: 'radio'
			label: "Enteral Feeding Route"
			columns: 2

	nut_enteral_time:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Enteral Formula (mls/hour)'
			columns: 2

	tot_nut_enteral_time:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Total Enteral Formula (mls/24 hours)'
			columns: 2

	tot_nut_enteral_onhand:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Total Enteral Formula On Hand (cans)'
			columns: 2

	nut_feeding_tol:
		model:
			max: 8
			source: ['Poor', 'Fair', 'Good']
		view:
			control: 'radio'
			label: "How well is feeding tolerated?"
			columns: 2

	nut_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Nutrition Comments/Pertinent History'

	# ROS - TPN Specific
	nut_feeding_tol_tpn:
		model:
			max: 8
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Is patient tolerating current TPN infusion?"
			columns: 2

	nut_side_effects_tpn:
		model:
			max: 8
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['nut_side_effects_list_tpn']
		view:
			control: 'radio'
			label: "Is patient reporting complications or side effects from TPN administration?"
			columns: 2

	nut_side_effects_list_tpn:
		model:
			max: 4086
			required: true
		view:
			control: 'area'
			label: "TPN Administration Side Effects"
			columns: 2

	nut_feeding_missed_tpn:
		model:
			max: 8
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['nut_feeding_missed_days_tpn', 'nut_feeding_missed_reason_tpn']
		view:
			control: 'radio'
			label: "Has patient missed any TPN infusions since last nursing visit?"
			columns: 2

	nut_feeding_missed_days_tpn:
		model:
			min: 1
			max: 30
			required: true
		view:
			label: "Days TPN Infusion Missed"
			columns: 2

	nut_feeding_missed_reason_tpn:
		model:
			max: 1024
			required: true
		view:
			control: 'area'
			label: "Reason TPN Infusion Missed"
			columns: 2

	nut_app_change:
		model:
			max: 8
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['nut_app_changes']
		view:
			control: 'radio'
			label: "Any changes in the patient's appetite?"
			columns: 2

	nut_app_changes:
		model:
			max: 1024
			required: true
		view:
			control: 'area'
			label: "Appetite Changes"
			columns: 2

	nut_app_consumed:
		model:
			max: 2048
			required: true
		view:
			control: 'area'
			label: "Types and amounts of food consumed daily"
			columns: 2

	nut_fluids:
		model:
			max: 8
			source: ['Poor', 'Fair', 'Good']
		view:
			control: 'radio'
			label: "How has the patient's oral fluid Careplan been?"
			columns: 2

	nut_fluids_intake:
		model:
			min: 1
		view:
			label: "Current fluid Careplan per day (oz)"
			columns: 2

	# ROS - Gastrointestinal
	gas_denies:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['gas_problems', 'gas_sounds', 'gas_ab', 'gas_comments']
		view:
			control: 'radio'
			label: 'Patient reports having Gastrointestinal problems?'
			columns: 4

	gas_problems:
		model:
			max: 128
			multi: true
			source: ['Nausea','Vomiting','Diarrhea','Constipation','Cramping','Heartburn', 'NG Tube', 'G Tube', 'Reflux Emesis', 'Bloody Stools', 'Ostomy', 'Incontinent']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Gastrointestinal Problems'
			columns: 2

	gas_sounds:
		model:
			max: 128
			source: ['Active', 'Hypo', 'Hyper', 'Absent']
		view:
			control: 'radio'
			label: 'Bowel Sounds'
			columns: 2

	gas_ab:
		model:
			max: 128
			source: ['Soft', 'Distended', 'Tight', 'Tender']
		view:
			control: 'radio'
			label: 'Abdomen'
			columns: 2

	gas_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Gastrointestinal Comments/Pertinent History'
			columns: 2

	# ROS - Genitourinary
	gen_denies:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['gen_problems', 'gen_incont', 'gen_urine', 'gen_urine_color', 'gen_comments']
		view:
			control: 'radio'
			label: 'Patient reports having Genitourinary problems?'
			columns: 4

	gen_problems:
		model:
			max: 24
			multi: true
			source: ['Frequency','Urgency','Burning','Pain']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Genitourinary Problems'
			columns: 4

	gen_incont:
		model:
			max: 24
			source: ['Catheter', 'Incontinence Product']
		view:
			control: 'radio'
			label: 'Incontinence'
			columns: 4

	gen_urine:
		model:
			max: 24
			source: ['Cloudy', 'Bloody', 'Sediment']
		view:
			control: 'radio'
			label: 'Urine'
			columns: 4

	gen_urine_color:
		view:
			label: 'Urine Color'
			columns: 4

	gen_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Genitourinary Comments/Pertinent History'
			columns:3


	# ROS - Reproductive - Male & Female
	repo_denies:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					sections: ['ROS - Reproductive - Problems']
		view:
			control: 'radio'
			label: 'Patient reports having Reproductive problems?'
			columns: 4

	repo_problems_male:
		model:
			max: 128
			multi: true
			source: ['Discharge','Impotence','Other']
			if:
				'Other':
					fields: ['repo_problems_male_other']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply (male patients)'
			label: 'Male Reproductive Problems'
			columns: 4

	repo_problems_male_other:
		model:
			required: true
		view:
			label: 'Male Reproductive Other Problem Details'
			columns: 4

	repo_problems_female:
		model:
			max: 128
			multi: true
			source: ['Dysmenorrhea','Amenorrhea','Perimenopause','Postmenopause']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply (female patients)'
			label: 'Female Reproductive Problems'
			columns: 3

	repo_preg:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['repo_preg_post']
				'Yes':
					fields: ['repo_preg_gest']
		view:
			control: 'radio'
			label: 'Pregnant?'
			columns: 4

	repo_preg_gest:
		model:
			min: 1
			max: 40
			type: 'int'
		view:
			note: '1-40'
			label: 'Gestational Week'
			columns: 4

	repo_preg_post:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Postpartum?'
			columns: 4

	repo_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Reproductive Comments/Pertinent History'
			columns:3

	# ROS - Pain
	pain_denies:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['patient_participates_numeric',
					'patient_participates_wongbaker',
					'patient_participates_flacc']
					sections: ['ROS - Pain - Comments']
		view:
			control: 'radio'
			label: 'Patient reports having pain?'
			columns: 4

	patient_participates_numeric:
		model:
			max: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pain_loc', 'pain_freq', 'pain_int', 'pain_cause', 'pain_relieve', 'pain_move', 'pain_sleep']
		view:
			control: 'radio'
			label: 'Will patient perform numeric pain score?'
			columns: 4

	pain_loc:
		model:
			max: 128
		view:
			label: 'Pain Location'
			columns: 4

	pain_freq:
		model:
			max: 24
			source: ['Continuous', 'Intermittent']
		view:
			control: 'radio'
			label: 'Pain Frequency'
			columns: 4

	pain_int:
		model:
			max: 2
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Pain Intensity'
			columns: 4

	pain_cause:
		model:
			max: 1024
		view:
			label: 'What causes the pain?'
			columns: 4

	pain_relieve:
		model:
			max: 1024
		view:
			label: 'What relieves the pain?'
			columns: 4

	pain_move:
		model:
			max: 64
			source:
				never: 'Never'
				sometimes: 'Some of the time (less than daily)'
				daily: 'Daily but not constantly'
				always: 'All the time'
		view:
			control: 'radio'
			label: 'Does the pain interfere with activities/movement?'
			columns: 4

	pain_sleep:
		model:
			max: 64
			source:
				never: 'Never'
				daily: 'Daily'
				sometimes: 'Occasionally'
		view:
			control: 'radio'
			label: 'Does the pain interfere with sleep?'
			columns: 4

	patient_participates_wongbaker:
		model:
			max: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['ROS - Pain Wong-Baker Score']
		view:
			control: 'radio'
			label: 'Will patient perform wong baker score?'
			columns: 4

	wong_baker_score:
		model:
			source: ['No Hurt (0)', 'Hurts Little Bit (2)',
			'Hurts Little More (4)', 'Hurts Even More (6)',
			'Hurts Whole Lot (8)', 'Hurts Worst (10)', 'N/A']
		view:
			control: 'radio'
			label: 'Wong-Baker Score'
			columns: 4

	observed_by:
		model:
			source: ['Nurse', 'Parent', 'Caretaker']
		view:
			control: 'radio'
			label: 'Pain Scale Observed By:'
			columns: 4

	patient_participates_flacc:
		model:
			max: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['ROS - Pain FLACC Score']
		view:
			control: 'radio'
			label: 'Will patient perform FLACC assessment?'
			columns: 4

	flacc_face:
		model:
			source: ['No particular expression or smile', 'Occasional grimace or frown, withdrawn, uninterested',
			'Frequent to constant quivering chin, clenched jaw', 'N/A']
		view:
			control: 'radio'
			label: 'Face'
			columns: 4
			validate: [
				name: 'FLACCScoreValidate'
			]

	flacc_legs:
		model:
			source: ['Normal position or relaxed', 'Uneasy, restless, tense',
			'Kicking, or legs drawn up', 'N/A']
		view:
			control: 'radio'
			label: 'Legs'
			columns: 4
			validate: [
				name: 'FLACCScoreValidate'
			]

	flacc_activity:
		model:
			source: ['Lying quietly, normal position, moves easily', 'Squirming, shifting, back and forth, tense',
			'Arched, rigid or jerking', 'N/A']
		view:
			control: 'radio'
			label: 'Activity'
			columns: 4
			validate: [
				name: 'FLACCScoreValidate'
			]

	flacc_cry:
		model:
			source: ['No cry (awake or asleep)', 'Moans or whimpers; occasional complaint',
			'Crying steadily, screams or sobs, frequent complaints', 'N/A']
		view:
			control: 'radio'
			label: 'Cry'
			columns: 4
			validate: [
				name: 'FLACCScoreValidate'
			]

	flacc_consolability:
		model:
			source: ['Content, relaxed', 'Reassured by occasional touching, hugging or being talked to, distractible',
			'Difficult to console or comfort', 'N/A']
		view:
			control: 'radio'
			label: 'Consolability'
			columns: 4
			validate: [
				name: 'FLACCScoreValidate'
			]

	flacc_observed_by:
		model:
			source: ['Nurse', 'Parent', 'Caretaker']
		view:
			control: 'radio'
			label: 'FLACC Scale Observed By:'
			columns: 4

	flacc_score:
		view:
			readonly: true
			label: 'FLACC Score'
			columns: 4

	pain_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Pain Comments/Pertinent History'
			columns:3

	# ROS - Psychological
	pysch_denies:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['pysch_problems', 'pysch_comments']
		view:
			control: 'radio'
			label: 'Patient reports having Psychological problems?'
			columns: 4

	pysch_problems:
		model:
			max: 128
			multi: true
			source: ['Depressed','Anxious','Demanding','Mood Swings','Sleep Problems','Disruptive Family Interactions','Memory','Reasoning or Judgement Issues', 'Developmentally Delayed', 'Other']
			if:
				'Other':
					fields: ['pysch_problems_details']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Psychological Problems'
			columns: 4

	pysch_problems_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Details'

	pysch_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Psychological Comments / Pertinent History / Social and Community Involvement / Coping Mechanisms'
			columns:3

	# ROS - Skin
	skin_denies:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['skin_problems', 'skin_mucous_mem', 'skin_comments']
		view:
			control: 'radio'
			label: 'Patient reports having Skin problems?'
			columns: 4

	skin_problems:
		model:
			max: 64
			multi: true
			source: ['Rash','Pale','Jaundice','Cyanotic','Lesions','Petechiae','Poor Turgor','Diaphoretic','Skin breakdown (decubitus, pressure ulcer, sores)','Wound', 'Hair Loss']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Skin Problems'
			columns: 4

	skin_mucous_mem:
		model:
			max: 64
			source: ['Moist','Dry']
		view:
			control: 'radio'
			label: 'Mucous Membranes'
			columns: 4

	skin_oral:
		model:
			max: 64
			source: ['Poor', 'Fair', 'Good']
		view:
			control: 'radio'
			label: 'Oral Hygiene'
			columns: 4

	skin_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Skin Comments/Pertinent History'
			columns:3

	# ROS - Gastrointestinal
	gas_denies:
		model:
			max: 3
			source: ['Yes', 'WNL']
			if:
				'Yes':
					fields: ['gas_problems', 'gas_sounds', 'gas_ab', 'gas_comments']
		view:
			control: 'radio'
			label: 'Patient reports having Gastrointestinal problems?'
			columns:4

	gas_problems:
		model:
			max: 128
			multi: true
			source:['Nausea','Vomiting','Diarrhea','Constipation','Cramping','Heartburn', 'NG Tube', 'G Tube', 'Reflux Emesis', 'Bloody Stools', 'Ostomy', 'Incontinent']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Gastrointestinal Problems'
			columns:4

	gas_diapers:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['gas_diapers_changed']
		view:
			control: 'radio'
			label: 'Patient in diapers?'
			columns:4

	gas_diapers_changed:
		model:
			rounding: 1
			type: 'decimal'
		view:
			label: 'Diapers Changed (last 24 hrs)'
			columns:4

	gas_sounds:
		model:
			max: 128
			source: ['Active', 'Hypo', 'Hyper', 'Absent']
		view:
			control: 'radio'
			label: 'Bowel Sounds'
			columns:4


	gas_ab:
		model:
			max: 128
			source: ['Soft', 'Distended', 'Tight', 'Tender']
		view:
			control: 'radio'
			label: 'Abdomen'
			columns:4

	gas_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Gastrointestinal Comments/Pertinent History'
			columns:3

	# Substance Abuse
	substance_abuse:
		model:
			max: 3
			source: ['Yes', 'No']
			if:
				'Yes':
					fields: ['substance_abuse_comments']
		view:
			control: 'radio'
			label: 'Substance abuse?'
			columns:4

	substance_abuse_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Comments'
			columns:3

	self_harm:
		model:
			max: 3
			source: ['Yes', 'No']
			if:
				'Yes':
					fields: ['self_harm_comments']
		view:
			control: 'radio'
			label: 'Thoughts of self-harm?'
			columns:4

	self_harm_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Comments'
			columns:3

	harm_others:
		model:
			max: 3
			source: ['Yes', 'No']
			if:
				'Yes':
					fields: ['harm_others_comments']
		view:
			control: 'radio'
			label: 'Thoughts of harm to others?'
			columns:4

	harm_others_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Comments'
			columns:3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	name: ['patient_id', 'therapy_1']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		careplan_order:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
		encounter_ros:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections_group: [
		"Review of Systems":
			hide_header: true
			indent: false
			note: 'Does patient report any of the following problems?'
		'ROS - HENT':
			fields: [
					'gender', 'therapy_1', 'therapy_2', 'therapy_3', 'therapy_4', 'therapy_5', 'disease_1', 'disease_2', 'disease_3', 'disease_4', 'disease_5',
					'hent_problems_head_neck', 'hent_head_neck', 'hent_head_neck_other', 'hent_head_neck_comments',
					'hent_problems_eye', 'hent_eyes', 'hent_eyes_other', 'hent_eyes_comments',
					'hent_problems_nose', 'hent_nose', 'hent_nose_other', 'hent_nose_comments',
					'hent_problems_mouth_throat', 'hent_mouth_throat', 'skin_oral', 'hent_mouth_throat_other', 'hent_mouth_throat_comments',
					'hent_problems_skin', 'hent_skin', 'hent_skin_other', 'hent_skin_comments']
			prefill: 'encounter_ros'
		'ROS - Neurology':
			fields: ['neuro_denies', 'neuro_perrla', 'neuro_problems', 'neuro_problems_other', 'neuro_comments']
			prefill: 'encounter_ros'
		'ROS - Headaches':
			fields: ['has_headaches', 'headache_type', 'headache_assoc', 'headache_pain', 'headache_num', 'headache_freq', 'headache_dur', 'headache_dur_type', 'headache_med', 'headache_comments']
			prefill: 'encounter_ros'
		'ROS - Endocrine':
			fields: ['endo_denies', 'endo_problems', 'endo_problems_other', 'endo_comments']
			prefill: 'encounter_ros'
		'ROS - Musculoskeletal':
			fields: ['musc_denies', 'musc_problems', 'musc_problems_other', 'musc_comments']
			prefill: 'encounter_ros'
		'ROS - Respiratory':
			fields: ['resp_denies', 'resp_problems', 'resp_ortho_o2', 'resp_ortho_rate', 'resp_breathing', 'resp_cough', 'resp_cough_color', 'resp_oxygen', 'resp_oxygen_type', 'resp_oxygen_amt', 'resp_pulseox', 'resp_comments']
			prefill: 'encounter_ros'
		'ROS - Cardiovascular':
			fields: ['card_denies', 'card_problems', 'card_edema_loc', 'card_edema_pit', 'card_edema_pit_score', 'card_comments']
			prefill: 'encounter_ros'
		'ROS - Nutrition - TPN':
			fields: ['nut_diet', 'nut_diet_type', 'nut_app', 'nut_app_change', 'nut_app_changes', 'nut_parenteral', 'nut_parenteral_total', 'nut_enteral_type', 'nut_enteral_route', 'nut_enteral_time', 'tot_nut_enteral_time', 'tot_nut_enteral_onhand', 'nut_feeding_tol_tpn', 'nut_side_effects_tpn',
					'nut_side_effects_list_tpn', 'nut_feeding_missed_tpn', 'nut_feeding_missed_days_tpn', 'nut_feeding_missed_reason_tpn', 'nut_app_consumed', 'nut_fluids', 'nut_fluids_intake', 'nut_comments']
			prefill: 'encounter_ros'
		'ROS - Nutrition':
			fields: ['nut_diet', 'nut_diet_type', 'nut_app', 'nut_parenteral', 'nut_parenteral_total', 'nut_enteral_type', 'nut_enteral_route', 'nut_enteral_time', 'tot_nut_enteral_time', 'tot_nut_enteral_onhand', 'nut_feeding_tol', 'nut_comments']
			prefill: 'encounter_ros'
		'ROS - Gastrointestinal':
			fields: ['gas_denies', 'gas_diapers', 'gas_diapers_changed', 'gas_problems', 'gas_sounds', 'gas_ab', 'gas_comments']
			prefill: 'encounter_ros'
		'ROS - Genitourinary':
			fields: ['gen_denies', 'gen_problems', 'gen_incont', 'gen_urine', 'gen_urine_color', 'gen_comments']
			prefill: 'encounter_ros'
		'ROS - Reproductive':
			fields: ['repo_denies']
			prefill: 'encounter_ros'
		'ROS - Reproductive - Problems':
			fields: ['repo_problems_male', 'repo_problems_male_other', 'repo_problems_female', 'repo_preg', 'repo_preg_gest', 'repo_preg_post', 'repo_comments']
			prefill: 'encounter_ros'
		'ROS - Pain':
			fields: ['pain_denies', 'patient_participates_numeric', 'patient_participates_wongbaker', 'patient_participates_flacc',
			 'pain_loc', 'pain_freq', 'pain_int', 'pain_cause', 'pain_relieve', 'pain_move', 'pain_sleep']
			prefill: 'encounter_ros'
		'ROS - Pain Wong-Baker Score':
			fields: ['wong_baker_score', 'observed_by']
		'ROS - Pain FLACC Score':
			fields: ['flacc_face', 'flacc_legs', 'flacc_activity', 'flacc_cry', 'flacc_consolability', 'flacc_observed_by', 'flacc_score']
			prefill: 'encounter_ros'
		'ROS - Pain - Comments':
			fields: ['pain_comments']
			prefill: 'encounter_ros'
		'ROS - Behavioral Health':
			fields: ['pysch_denies', 'pysch_problems', 'pysch_problems_details', 'pysch_comments', 'self_harm', 'self_harm_comments', 'harm_others', 'harm_others_comments','substance_abuse','substance_abuse_comments']
			prefill: 'encounter_ros'
		'ROS - Skin':
			fields: ['skin_denies', 'skin_problems', 'skin_mucous_mem', 'hent_psoriasis_loc', 'hent_psoriasis_loc_other', 'hent_psoriasis_severe', 'hent_psoriasis_bsa', 'skin_comments']
			prefill: 'encounter_ros'
	]
view:
	comment: 'Patient > Careplan > Encounter > Review of Systems'
	label: 'Patient Encounter: Review of System'
