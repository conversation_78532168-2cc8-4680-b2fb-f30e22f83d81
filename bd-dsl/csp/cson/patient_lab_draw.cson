fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	lab:
		model:
			required: true
			source: ['<PERSON>', 'LabCorp', 'Other']
			if:
				'Other':
					fields: ['lab_other']
		view:
			control: 'radio'
			label: 'Laboratory'

	lab_other:
		model:
			required: true
		view:
			label: 'Laboratory Other'

	labs_drawn:
		model:
			required: true
			source: ['Peripheral draw', 'Drawn from catheter', 'Discarded blood']
		view:
			label: 'Labs Source'

	amount_drawn:
		model:
			required: true
			type: 'decimal'
		view:
			label: 'Amount Drawn (ml)'

	time:
		model:
			required: true
			type: 'time'
		view:
			label: 'Time'

	# comment:
	# 	view:
	# 		control: 'area'
	# 		label: 'Comments'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	bundle: ['patient']
	name: ['lab']
	sections: 
		'Lab Draw':
			fields: ['lab_other',  'amount_drawn']

view:
	comment: 'Patient > Lab Drawn'
	grid:
		fields: ['time', 'lab_other', 'amount_drawn' ] # dynamic status is displayed in prefilled, rtag is hidden
		sort: ['time']
	label: 'Lab Draw'
