fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['encounter_mhaq.assessment_date']
		view:
			note: 'Recommended 2x per year'
			label: 'Last Assessment Date'
			readonly: true

	# Questionnaire Participation
	mhaq_particiates:
		model:
			prefill: ['encounter_mhaq']
			max: 32
			source: ['No', 'Yes']
			default: 'Yes'
			if:
				'Yes':
					sections: ['MHAQ Questionnaire']
					fields: ['assessment_date']
		view:
			control : 'radio'
			label: 'Will a Modified Health Assessment Questionnaire (mHAQ) be completed on this visit?'

	# Questionnaire
	mhaq_dress:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control : 'radio'
			label: 'Dress yourself, including tying shoelaces and doing buttons?'
			validate: [
					name: 'MHAQScoreValidate'
			]

	mhaq_bed:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control : 'radio'
			label: 'Get in and out of bed?'
			validate: [
					name: 'MHAQScoreValidate'
			]

	mhaq_glass:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control : 'radio'
			label: 'Lift a full cup or glass to your mouth?'
			validate: [
					name: 'MHAQScoreValidate'
			]

	mhaq_walk:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control : 'radio'
			label: 'Walk outdoors on flat ground?'
			validate: [
					name: 'MHAQScoreValidate'
			]

	mhaq_wash:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control : 'radio'
			label: 'Wash and dry your entire body?'
			validate: [
					name: 'MHAQScoreValidate'
			]

	mhaq_bend:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control : 'radio'
			label: 'Bend down to pick up clothing from the floor?'
			validate: [
					name: 'MHAQScoreValidate'
			]

	mhaq_faucet:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control : 'radio'
			label: 'Turn regular faucets on and off?'
			validate: [
					name: 'MHAQScoreValidate'
			]

	mhaq_vehicle:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control : 'radio'
			label: 'Get in and out of a bus, car, train, or airplane?'
			validate: [
					name: 'MHAQScoreValidate'
			]

	score:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'Values <0.3 are considered normal'
			label: 'Assessment Score'
			readonly: true

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'encounter']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_mhaq:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'MHAQ Questionnaire Participation':
			fields: ['last_assessment_date', 'mhaq_particiates', 'assessment_date']
			prefill: 'encounter_mhaq'
		'MHAQ Questionnaire':
			note: 'Ask the patient the answer that best describes their usual abilities OVER THE COURSE OF THE LAST WEEK'
			fields: ['mhaq_dress', 'mhaq_bed', 'mhaq_glass', 'mhaq_walk', 'mhaq_wash', 'mhaq_bend', 'mhaq_faucet', 'mhaq_vehicle', 'score']
			prefill: 'encounter_mhaq'
view:
	comment: 'Patient > Careplan > Encounter > Modified Health Assessment Questionnaire'
	grid:
		fields: ['created_on', 'created_by']
		sort: ['-id']
	label: 'Patient Encounter: Modified Health Assessment Questionnaire (mHAQ)'
