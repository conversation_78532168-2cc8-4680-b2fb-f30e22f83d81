fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	status_id:
		model:
			required: true
			source: 'list_careplan_status'
			sourceid: 'code'
			default: '3'
			if:
				'4':
					sections: ['Careplan Discharge']
		view:
			label: 'Status'
			columns: 3

	careplan_optout:
		model:
			max: 12
			source: ['Opt-Out', 'Opt-In']
			if:
				'Opt-In':
					sections: ['Careplan Duration of therapy', 'Careplan Notes', 'Careplan Discharge Criteria']
			required: true
			default: 'Opt-In'
		view:
			control: 'radio'
			label: 'Opt-In/Opt-Out'
			note: 'Pat<PERSON> opted out of care plan or drug not administered by pharmacy?'
			columns: 3

	template_id:
		model:
			source: 'careplan_template'
			multi: false
			if:
				'*':
					sections: ['Problems', 'Goals', 'Interventions']
					fields: ['subform_problems', 'subform_goal', 'subform_intervention']
		view:
			label: 'Care Plan Template'
			class: 'select_prefill'
			columns: 3
			transform: [
				name: 'SelectPrefill'
				url: '/form/template_careplan/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'subform_problems':
						'type': 'subform'
						'field': 'subform_problems'
						'fields':
							'name': ['sf.name']
							'is_complete': ['sf.is_complete']
							'completed_date': ['sf.completed_date']
					'subform_goal':
						'type': 'subform'
						'field': 'subform_goal'
						'fields':
							'name': ['sf.name']
							'is_complete': ['sf.is_complete']
							'completed_date': ['sf.completed_date']
					'subform_intervention':
						'type': 'subform'
						'field': 'subform_intervention'
						'fields':
							'name': ['sf.name']
							'is_complete': ['sf.is_complete']
							'completed_date': ['sf.completed_date']
			]



	subform_problems:
		model:
			multi: true
			type: 'subform'
			source: 'careplan_template_problem'
		view:
			grid:
				add: 'none'
				hide_cardmenu: true
				edit: true
				fields: ['name', 'is_complete', 'completed_date']
				label: ['Name', 'Completed', 'Completed Date']
			label: 'Problems'

	subform_goal:
		model:
			multi: true
			type: 'subform'
			source: 'careplan_template_goal'
		view:
			grid:
				add: 'none'
				hide_cardmenu: true
				edit: true
				fields: ['name', 'is_complete', 'completed_date']
				label: ['Name', 'Completed', 'Completed Date']
			label: 'Goals'

	subform_intervention:
		model:
			multi: true
			type: 'subform'
			source: 'careplan_template_intervention'
		view:
			grid:
				add: 'none'
				hide_cardmenu: true
				edit: true
				fields: ['name', 'is_complete', 'completed_date']
				label: ['Name', 'Completed', 'Completed Date']
			label: 'Interventions'


	note:
		view:
			control: 'area'
			label: 'Care Plan Notes'

	therapy_start:
		model:
			required: true 
			type: 'date'
		view:
			label: "Therapy Start Date"
			columns: 4

	therapy_end:
		model:
			type: 'date'
		view:
			label: "Therapy End Date"
			columns: 4

	discharge_reason_id:
		model:
			max: 12
			required: true
			source: 'list_discharge_reason'
			sourceid: 'code'
		view:
			label: 'Discharge Reason'
			columns: 2

	home_state_id:
		model:
			source: 'list_us_state'
			sourceid: 'code'
			prefill: ['patient.home_state_id']
			if:
				'TX':
					fields: ['discharge_tx_reason']
		view:
			label: 'Home State'
			offscreen: true
			readonly: true

	discharge_tx_reason:
		model:
			multi: false
			required: true
			source: ['Upon patient’s request',
			'According to physician’s orders',
			'Patient’s medical needs, i.e. medical emergency, prompted the transfer or discharge',
			'The patient’s health or safety were at risk due to disaster',
			'Patient failed to pay for services, except as prohibited by federal law',
			'None of the above']
			if:
				'None of the above':
					fields: ['discharge_tx_notify', 'discharge_tx_notify_written']
		view:
			control: 'checkbox'
			label: 'Discharge Reason'
			note: 'Discharge TX Requirements'
			columns: 2

	discharge_tx_notify:
		model:
			required: true
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Was patient verbally notified of discharge 5 days prior?'
			columns: 2

	discharge_tx_notify_written:
		model:
			required: true
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Was written notification of planned discharge date mailed to patient AND physician at least 8 days prior to discharge?'
			columns: 2

	ongoing_needs:
		view:
			control: 'area'
			label: "Any ongoing needs that couldn't be met?"

	discharge_summary:
		model:
			required: true
		view:
			control: 'area'
			label: "Summary of the services provided"

	discharge_instructions:
		view:
			control: 'area'
			label: "Discharge Instructions"

	discharge_instructions_pt:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Discharge instructions provided to the patient?'
			columns: 2

	discharge_instructions_dr:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Copy of discharge instructions provided to prescriber?'
			columns: 2

	discharge_criteria:
		model:
			source: ['Completion of prescribed therapy', 'Discontinuation of therapy by physician order']
		view:
			label: 'Discharge Criteria'
			columns: 2

	discharge_criteria_status:
		model:
			default: 'Criteria Not met - Patient active'
			source: ['Criteria Not met - Patient active', 'Criteria Not met - Patient Discharged d/t alternate reason','Criteria Met - Patient to be discharged']
		view:
			label: 'Discharge Criteria Status'
			columns: 2

	discharge_criteria_txt:
		view:
			control: 'area'
			label: 'Comments'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	soc_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Start of Care Date'
			template: '{{now}}'


model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin', 'pharm', 'cm', 'cma', 'nurse']
		write:      ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections_group: [
		'Care Plan':
			hide_header: true
			indent: false
			sections: [
				'Status':
					hide_header: true
					fields: ['status_id', 'template_id', 'careplan_optout']
				'Careplan Duration of therapy':
					hide_header: true
					fields: ['therapy_start', 'therapy_end']
				'Problems':
					fields: ['subform_problems']
				'Goals':
					fields: ['subform_goal']
				'Interventions':
					fields: ['subform_intervention']
				'Careplan Notes':
					hide_header: true
					prefill: 'careplan'
					fields: ['note']
				'Careplan Discharge Criteria':
					fields: ['discharge_criteria', 'discharge_criteria_status', 'discharge_criteria_txt']
				'Careplan Discharge':
					fields: ['discharge_reason_id', 'discharge_summary',
					'home_state_id','discharge_tx_reason',
					'discharge_tx_notify', 'discharge_tx_notify_written',
					'ongoing_needs', 'discharge_instructions',
					'discharge_instructions_pt','discharge_instructions_dr']
			]
	]
	transform: [
		{
			name: "EnsureUnique"
			arguments:
				form: "careplan"
				unique_field: "status_id"
				nonunique_value: "No"
				qualifier_field: "patient_id"
		}
	]
	name: '{status_id_auto_name}'
view:
	hide_cardmenu: true
	find:
		basic: ['status_id']
	comment: 'Patient > Careplan > Multidisciplinary Care Plan'
	grid:
		fields: ['created_on', 'created_by', 'status_id']
		sort: ['-created_on']
	label: 'Multidisciplinary Care Plan'
	open: 'read'
