fields:
	# HCV Teaching:
	understanding_therapy:
		model:
			source: ['Very Knowledgeable', 'Moderately Knowledgeable', 'Not Knowledgeable', 'No Understanding']
			if:
				'Moderately Knowledgeable':
					note: 'Review HIV disease overview with patient, as needed'
				'Not Knowledgeable':
					note: 'Review HIV disease overview with patient'
				'No Understanding':
					note: 'Review HIV disease overview with patient'
		view:
			label: 'How would you rate your understanding of HIV?'
			control: 'select'
			columns: 2

	understanding_drug:
		model:
			source: ['Very Knowledgeable', 'Moderately Knowledgeable', 'Not Knowledgeable', 'No Understanding']
			if:
				'Not Knowledgeable':
					note: 'Review drug information with patient'
				'No Understanding':
					note: 'Review drug information with patient'
		view:
			label: 'How would you rate your understanding of the drug therapy you have been prescribed?'
			note: 'Ask the patient to explain what they know about the drug therapy'
			control: 'select'
			columns: 2

	understanding_frequency:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					note: 'Review medication instructions with patient'
		view:
			label: 'Do you know how and when to take the drug that has been prescribed?'
			note: 'Ask the patient to explain what instructions they have received'
			control: 'radio'
			columns: 2

	understanding_importance:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					note: 'Review the importance of following the prescription as written'
		view:
			label: 'Do you understand the importance of taking the medication and continuing to take your medication as your doctor prescribed?'
			control: 'radio'

	help_remembering:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Setup a live call by a pharmacist'
		view:
			label: 'Do you need help remembering to take your medication?'
			control: 'radio'
			columns: 2

	herb_supplements:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Some herbal supplements can harm the liver, please review with patient'
		view:
			control: 'radio'
			label: 'Are you taking any herbal supplements?'
			columns: 3



model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'csr', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'

	sections_group: [
		'HIV Teaching':
			hide_header: true
			indent: false
			tab: 'HIV Teaching'
			fields: ['understanding_therapy', 'understanding_drug', 'understanding_frequency', 'help_remembering', 'understanding_importance', ]
		'HIV Condition Details':
			area: 'preassessment'
			hide_header: true
			indent: false
			tab: 'HIV Condition Details'
			fields: ['creatinine_levels', 'cd4_count', 'viral_load', 'elevated_alt', 'cirr_biop', 'have_cirrhosis', 'cirrhosis_type', 'fibrosis_stage', 'child_pugh_score']
		'HIV General Assessment':
			hide_header: true
			indent: false
			tab: 'HIV General Assessment'
			fields: ['gender', 'are_preg', 'birth_control', 'are_postmen', 'tattoos', 'has_hepb', 'herb_supplements', 'cancer', 'have_exposure', 'travel', 'travel_where']
		'HIV Renal Disease Risk Assessment':
			hide_header: true
			indent: false
			tab: 'HIV Renal Disease Risk Assessment'
			fields: ['had_kidney_disease', 'has_diabetes', 'has_insulin', 'meds_taking', 'meds_taking_other']
	]
view:
	comment: 'Patient > Careplan > Assessment > HIV'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: HIV'
	open: 'read'
