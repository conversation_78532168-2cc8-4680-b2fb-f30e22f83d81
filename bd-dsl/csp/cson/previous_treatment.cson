fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	therapy_1:
		model:
			max: 64
			source: 'list_therapy'
			sourceid: 'code'
			prefill: ['parent.therapy_1']
			if:
				'ig':
					fields: ['frequency', 'ig_dose', 'infusion_time']
				'tnf':
					fields: ['ra_drug_type']
		view:
			label: 'Therapy'

	brand_name_id:
		model:
			required: true
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Drug Brand'

	access_type:
		model:
			multi: true
			source: ['Port', 'PICC', 'Peripheral', 'Winged Needle', 'Subcutaneous', 'Other']
			if:
				'Other':
					fields: ['access_type_details']
		view:
			label: 'Primary Access Type'

	access_type_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Details:'

	infusion_time:
		view:
			label: 'Administration infusion time:'

	ra_drug_type:
		model:
			multi: true
			source: ['Analgesics', 'NSAIDs', 'Corticosteroids', 'DMARDs (Disease Modifying Anti-Rheumatic Drugs)', 'Biologics']
		view:
			label: 'What medications did the patient use before for Rheumatoid Arthritis?'

	ig_dose:
		model:
			type: 'decimal'
			min: 5
			max: 1000
			rounding: 5
		view:
			note: 'If known'
			label: 'Last Administered Dose (g)'

	date:
		model:
			type: 'date'
		view:
			label: 'When did the patient receive therapy last?'

	resolve:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Did the prior therapy used fail to resolve or reduce symptoms?'

	frequency:
		model:
			type: 'decimal'
			rounding: 1
		view:
			label: 'Frequency (every x weeks)'

	duration:
		model:
			type: 'decimal'
			rounding: 0.1
		view:
			label: 'Last Therapy Duration'

	duration_type:
		model:
			source: ['Days', 'Weeks', 'Months', 'Years']
		view:
			label: 'Last Therapy Duration Type'
			control: 'radio'

	response:
		model:
			max: 12
			min: 1
			source: ['Excellent','Good','Fair','Poor', 'Relapsed', 'Stopped due to side effects']
		view:
			control: 'radio'
			label: 'What was the response to the treatment?'

	had_adr:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['reactions', 'reaction_treatment']
		view:
			control: 'radio'
			label: 'Did the patient experience any adverse drug reactions (ADRs)?'

	reactions:
		model:
			multi: true
			source: 'list_reaction'
		view:
			control: 'select'
			label: 'Type of reactions'

	reaction_treatment:
		model:
			max: 64
			min: 1
			source: ['Medication Change', 'Medication dose or frequency change', 'Discontinued', 'Pre-Medication Given','Other']
			if:
				'Other':
					fields: ['reaction_treatment_other']
		view:
			control: 'radio'
			label: 'What was done about the reactions?'

	reaction_treatment_other:
		model:
			max: 128
		view:
			label: 'Describe what was done about the reaction'

	discontinued_reason:
		model:
			source: ['Therapy Completed As Prescribed', 'Discontinued - No Response', 'Discontinued - Adverse Reaction', 'Discontinued - Access Device Related', 'Discontinued - Patient Cannot Afford Expense', 'Discontinued - Medication Not Available', 'Discontinued - Change in eligibility', 'Discontinued - Patient Out Of Country', 'Not preferred on formulary', 'Hospitalized', 'Other']
			if:
				'Other':
					fields: ['discontinued_reason_details']
		view:
			control: 'select'
			label: 'Reason Discontinued'

	discontinued_reason_details:
		model:
			required: true
		view:
			label: 'Details'

	notes:
		view:
			control: 'area'
			label: 'Notes'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	bundle: ['patient']
	name: ['patient_id']
	indexes:
		many: [
			['patient_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
	sections:
		'Previous Treatment Information':
			fields: ['therapy_1', 'brand_name_id', 'access_type', 'access_type_details',
			'ra_drug_type', 'ig_dose', 'date', 'resolve', 'frequency', 'duration',
			'duration_type', 'response', 'had_adr', 'reactions', 'reaction_treatment',
			'reaction_treatment_other', 'discontinued_reason', 'discontinued_reason_details', 'notes']
view:
	grid:
		fields: ['therapy_1', 'brand_name_id', 'date', 'duration', 'duration_type', 'response']
		sort: ['created_on']
	comment: 'Patient > Previous Treatment'
	label: 'Patient: Previous Treatment'
	open: 'read'
