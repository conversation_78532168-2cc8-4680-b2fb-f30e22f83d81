fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	route:
		model:
			prefill: ['encounter_ivig']
			source: ['Intravenous', 'Subcutaneous']
			if:
				'Subcutaneous':
					sections:['SubQ IG Training']
		view:
			control : 'radio'
			label: 'Route'

	# Infusion Device
	infus_devices:
		model:
			prefill: ['encounter_ivig']
			max: 8
			source: ['Pump', 'Gravity']
		view:
			control : 'radio'
			label: 'Infusion Devices'

	infus_devices_com:
		model:
			prefill: ['encounter_ivig']
		view:
			label: 'Infusion Devices Comment'

	infus_devices_maint:
		model:
			prefill: ['encounter_ivig']
			type: 'date'
		view:
			label: 'Periodic Maintenance Date'

	# Drug Administration
	pump_type:
		model:
			prefill: ['encounter_ivig']
			max: 32
			source: ['N/A', 'Ambulatory', 'Syringe', 'Other']
			if:
				'Other':
					fields: ['pump_type_other']
		view:
			control : 'radio'
			label: 'Pump'
			offscreen: true
			readonly: true

	pump_type_other:
		model:
			prefill: ['encounter_ivig']
			max: 1024
			required: true
		view:
			label: 'Pump Other Details'
			offscreen: true
			readonly: true

	pump_maintenance_date:
		model:
			prefill: ['encounter_ivig']
			max: 1024
			type: 'date'
		view:
			label: 'Pump Maintenance Date'
			offscreen: true
			readonly: true

	pump_needs_replaced:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Was the pharmacy notified to replace pump?'
			offscreen: true
			readonly: true

	# SubQ IG
	subq_comments:
		view:
			label: 'SubQ Ig comments'
			control: 'area'

	subq_caregiver_demo:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
		view:
			control : 'radio'
			label: 'Patient/caregiver able to return demonstrate proper SubQ Ig preparation, administration (if applicable)?'

	add_training:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['add_training_details']
		view:
			control : 'radio'
			label: 'Will patient/caregiver need additional training?'

	add_training_details:
		model:
			required: true
		view:
			label: 'Describe additional education requirements'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'encounter']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_ivig:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'IG Infusion Device':
			fields: ['route', 'infus_devices', 'infus_devices_com', 'infus_devices_maint']
			prefill: 'encounter_ivig'
		'SubQ IG Training':
			fields: ['subq_caregiver_demo', 'add_training', 'add_training_details', 'subq_comments']

view:
	comment: 'Patient > Intake > Encounter > IG'
	label: 'Patient Encounter: IG'
