fields:
	allergies:
		model:
			required: false
		view:
			label: 'Allergies (allergen and reaction)'

	code_status:
		model:
			source: ['Full Code', 'Do Not Resuscitate', 'Advance Directive', 'Copy on file', 'Copy Requested']
			required: false
		view:
			control: 'radio'
			label: 'Code Status'

	rehab_pot:
		model:
			multi: true
			source: ['Full Recovery', 'Partial Recovery', 'No Recovery', 'Terminal']
		view:
			control : 'radio'
			label: "Rehab Potential"

	#found in base careplan
	functional_lim:
		model:
			multi: true
			source: ['No Deficiency','Contractures', 'Mental/Cog','Vision'
				'Amputation', ' Bowel/Bladder Incontinence',
					 'Hearing', 'Paralysis', 'Endurance',
					 'Ambulation', 'Speech','Dyspnea With Minimal Exertion', 'Other']
			if:
				'Other':
					fields: ['functional_lim_other']
		view:
			control : 'checkbox'
			label: 'Functional limitations'

	functional_lim_other:
		model:
			required:true
		view:
			label: 'Functional limitations Other'

	mental_status:
		model:
			multi: true
			source: ['Alert/Oriented', 'Anxious', 'Comatose', 'Contused', 
					 'Depressed','Lethargic','Spiritual Needs Unmet','Other']
			if:
				'Other':
					fields: ['mental_status_other']
				'Spiritual Needs Unmet':
					fields: ['sub_spritual']
		view:
			control : 'checkbox'
			label: 'Mental Status'

	sub_spritual:
		model:
			source: ['Depression', 'Suicidal']
		view:
			label:'Spiritual Needs Ummet type'
			control:'radio'

	mental_status_other:
		model:
			required: true
		view:
			label: 'Mental Status Other'

	activities:
		model:
			multi: true
			source: ['No Restrictions', 'Home Bound', 'Noll-Ambulatory', 'Complete Bed Rest','Transfer Bed/Chair',
			'Partial Weight Bearing', 'Cane', 'Walker', 'Independent at Home', 'Bed Bound', 'Ambulatory', 'BRP',
			'Up as Tolerated', 'Exercise Prescribed', 'L', 'R', 'Crutches', 'Wheelchair'
			'Other']
			if:
				'Other':
					fields: ['activities_other']
		view:
			control : 'checkbox'
			label: 'Activities'

	activities_other:
		model:
			required: true
		view:
			control: 'area'
			label: 'Activities Other'

	# Safety Meaures founded careplanbase
	safety_measures:
		model:
			multi: true
			source: ['Clear/Safe Pathways', 'Fall Precautions', 'Medication Knowledge',
					 "Sharps Disposal", 'Infection Control','Bleeding Precautions', 'Home Safety',
					 'Weapons in Home', 'Fire Safety', 'Oxygen Safety']
		view:
			control : 'checkbox'
			label: 'Safety Measures'

	diet:
		model:
			source:['Regular', 'ADA', 'Cardiac', 'Other']
			if:
				'ADA':
					fields: ['ada_note']
				'Cardiac':
					fields: ['card_note']
				'Other':
					fields: ['other_note']
		view:
			control: 'radio'
			label: 'Diet'

	ada_note:
		model:
			required: false
		view:
			control: 'area'
			label: 'ADA Details'

	card_note:
		model:
			required: false
		view:
			control: 'area'
			label: 'Cardiac Details'

	other_note:
		model:
			required: false
		view:
			control: 'area'
			label: 'Other Details'

	dme_supplies:
		model:
			multi: false
			source: ['Infusion Pump', 'Sharps Container',
					 "Infusion Pole", 'Infusion Supplies','Others']
			if:
				'Others':
					fields: ['dme_supplies_other']
		view:
			control : 'checkbox'
			label: 'DME/Supplies'

	dme_supplies_other:
		model:
			required: true
		view:
			control: 'area'
			label: 'DME/Supplies Other'

	dx_1:
		model:
			prefill: ['parent.dx_1']
			source: 'list_diagnosis'
			sourceid: 'code'
		view: 
			readonly: true
			label: 'Primary Diagnosis'

	icd_code_1:
		model:
			prefill: ['parent.dx_1.icd_code']
			# source: 'diagnosis'
		view: 
			readonly: true
			label: 'Primary Diagnosis ICD Code'

	dx_date_1:
		model:
			prefill: ['parent.dx_date_1']
		view: 
			readonly: true
			label: 'Primary Diagnosis Onset Date'

	dx_2:
		model:
			prefill: ['parent.dx_2']
			source: 'list_diagnosis'
			sourceid: 'code'

		view: 
			readonly: true
			label: 'Secondary Diagnosis'

	icd_code_2:
		model:
			prefill: ['parent.dx_2.icd_code']
			# source: 'diagnosis'
		view: 
			readonly: true
			label: 'Secondary Diagnosis ICD Code'

	dx_date_2:
		model:
			prefill: ['parent.dx_date_2']
		view: 
			readonly: true
			label: 'Secondary Diagnosis Onset Date'

	address:
		view:
			label:'Address'
	city:
		view:
			label:'City'
	zip:
		view:
			label:'Zip'
	state:
		view:
			label:'State'

	phone:
		view:
			label:'Phone'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin','liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin','csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']
	name: ['created_on']
	sections:
		'Diagnoses':
			fields: ['dx_1', 'icd_code_1', 'dx_date_1', 'dx_2', 'icd_code_2', 'dx_date_2']
		'Patient Demographics':
			fields: ['address','city'  ,'state', 'zip' ,'phone', 'allergies', 'code_status', 'rehab_pot', 'functional_lim', 'functional_lim_other', 'mental_status',
			'mental_status_other' ,'sub_spritual', 'activities', 'activities_other', 'safety_measures','diet', 'ada_note', 'card_note', 'other_note','dme_supplies',
			'dme_supplies_other']
view:
	comment: ''
	grid:
		fields: ['allergies']
	label: 'Patient Demographics'

