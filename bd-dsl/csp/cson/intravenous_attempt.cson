fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	site:
		model:
			required: true
			source: ['Down' , 'Left Arm', 'Right Arm' , 'Antecubital' , 'Forearm' , 'Hand', 'PORT']
		view:
			label: 'Site'

	gauge:
		model:
			required: false
		view:
			label: 'Gauge'

	length:
		model:
			required: false
		view:
			label: 'Length'

	brand:
		model:
			required: false
		view:
			label: 'Brand'

	type_in:
		model:
			required: false
		view:
			label: 'Type'

	lumens:
		model:
			required: false
		view:
			label: 'Lumens'

	dressing:
		model:
			required: false
		view:
			label: 'Dressing'

model:
	sections:
		'Attempt':
			fields: ['site', 'gauge', 'length', 'brand', 'type_in', 'lumens', 'dressing']
	bundle: ['patient']
	name: ['created_on', 'created_by']
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin','liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']
view:
	label: 'Intravenous Attempt'
	open: 'read'
	grid:
		fields: ['created_by', 'created_on']
		sort: ['-id']