fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['encounter_incat.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	# Arm Disability
	dressing:
		model:
			multi:false
			required: true
			access:
				read: ['patient']
			source: ['Not affected', 'Affected but not prevented', 'Prevented']
		view:
			control : 'checkbox'
			note: 'excluding buttons or zippers'
			label: 'Dressing upper part of body'

	washing_hair:
		model:
			multi:false
			required: true
			access:
				read: ['patient']
			source: ['Not affected', 'Affected but not prevented', 'Prevented']
		view:
			control : 'checkbox'
			label: 'Washing and brushing hair'

	turning_key:
		model:
			multi:false
			required: true
			access:
				read: ['patient']
			source: ['Not affected', 'Affected but not prevented', 'Prevented']
		view:
			control : 'checkbox'
			label: 'Turning a key in a lock'

	using_utensils:
		model:
			multi:false
			required: true
			access:
				read: ['patient']
			source: ['Not affected', 'Affected but not prevented', 'Prevented']
		view:
			control : 'checkbox'
			note: 'spoon applicable if the patient never uses a knife or fork'
			label: 'Using a knife and fork'

	buttons:
		model:
			multi:false
			required: true
			access:
				read: ['patient']
			source: ['Not affected', 'Affected but not prevented', 'Prevented']
		view:
			control : 'checkbox'
			label: 'Doing/undoing buttons and zippers'

	arm_grade:
		model:
			multi:false
			required: true
			access:
				read: ['patient']
			source: ['0 = Normal',
			'1 = Minor symptoms or signs in one or both arms but not affecting any of the funtions listed',
			'2 = Moderate symptoms or signs in one or both arms affecting but not preventing any of the functions listed',
			'3 = Severe symptoms or signs in one or both arms preventing at least one but not all functions listed',
			'4 = Severe symptoms or signs in both arms preventing all functions listed but some purposeful movements still possible',
			'5 = Severe symptoms and signs in both arms preventing all purposeful movements']
		view:
			control : 'checkbox'
			label: 'Arm grade'
			validate: [
					name: 'INCATScoreValidate'
			]

	# Leg Disability
	walking:
		model:
			multi:false
			required: true
			access:
				read: ['patient']
			source: ['No', 'Yes', 'Not applicable']
		view:
			control : 'checkbox'
			label: 'Do you have any problems with your walking?'

	walking_aid:
		model:
			multi:false
			required: true
			access:
				read: ['patient']
			source: ['No', 'Yes', 'Not applicable']
		view:
			control : 'checkbox'
			label: 'Do you use a walking aid?'

	walk_wheelchair:
		model:
			multi:false
			required: true
			access:
				read: ['patient']
			source: ['No', 'Yes', 'Not applicable']
		view:
			control : 'checkbox'
			label: 'If you use a wheelchair, can you stand and walk a few steps with help?'

	purposeful_movements:
		model:
			multi:false
			required: true
			access:
				read: ['patient']
			source: ['No', 'Yes', 'Not applicable']
		view:
			control : 'checkbox'
			label: 'If you are restricted to bed most of the time, are you able to make some purposeful movements?'

	move_without_aid:
		model:
			multi:false
			required: true
			access:
				read: ['patient']
			source: ['No', 'Yes', 'Not applicable']
		view:
			control : 'checkbox'
			label: 'Without aid'

	move_with_crutch:
		model:
			multi:false
			required: true
			access:
				read: ['patient']
			source: ['No', 'Yes', 'Not applicable']
		view:
			control : 'checkbox'
			label: "With one stick or crutch or holding to someone's arm"

	move_with_crutches:
		model:
			multi:false
			required: true
			access:
				read: ['patient']
			source: ['No', 'Yes', 'Not applicable']
		view:
			control : 'checkbox'
			label: "With two sticks or crutches or one stick or crutch and holding onto someone's arm"

	move_wheelchair:
		model:
			multi:false
			required: true
			access:
				read: ['patient']
			source: ['No', 'Yes', 'Not applicable']
		view:
			control : 'checkbox'
			label: 'With a wheelchair'

	leg_grade:
		model:
			multi:false
			required: true
			access:
				read: ['patient']
			source: ['0 = Walking is not affected',
			'1 = Walking is affected but does not look abnormal',
			'2 = Walks independently but gait looks abnormal',
			'3 = Usually uses unilateral support to walk 10 meters (25 feet) (stick, single crutch, one arm)',
			'4 = Usually uses bilateral support to walk 10 meters (25 feet) (sticks, crutches, two arms)',
			'5 = Usually uses wheelchair to travel 10 meters (25 feet)',
			'6 = Restricted to wheelchair, unable to stand and walk few steps with help but able to make some purposeful leg movement',
			'7 = Restricted to wheelchair or bed most of the day, preventing all purposeful movements of the legs (eg. unable to reposition legs in bed)']
		view:
			control : 'checkbox'
			label: 'Leg grade'
			validate: [
					name: 'INCATScoreValidate'
			]

	score:
		model:
			min: 0
			max: 12
			rounding: 1
			type: 'decimal'
		view:
			label: 'Overall Disability Sum Score (ODSS)'
			readonly: true

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	bundle: ['patient', 'encounter']
	name: ['patient_id']
	indexes:
		many: [
			['patient_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		encounter_incat:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
	sections:
		'INCAT Assessment':
			fields: ['last_assessment_date', 'assessment_date']
		'INCAT Arm Disability Functional Checklist':
			note: 'Indicate whether each function is affected or prevented'
			fields: ['dressing', 'washing_hair', 'turning_key', 'using_utensils', 'buttons']
			prefill: 'encounter_incat'
		'INCAT Arm Disability Arm Grade':
			note: 'Allocate one arm grade only by completing the functional questions'
			fields: ['arm_grade']
			prefill: 'encounter_incat'
		'INCAT Leg Disability Functional Checklist':
			fields: ['walking', 'walking_aid', 'walk_wheelchair', 'purposeful_movements']
			prefill: 'encounter_incat'
		'INCAT Leg Disability Mobility':
			note: 'How do you usually get around for about 10 meters (25 feet)'
			fields: ['move_without_aid', 'move_with_crutch', 'move_with_crutches', 'move_wheelchair']
			prefill: 'encounter_incat'
		'INCAT Leg Disability Leg Grade':
			note: 'Allocate one leg grade only by completing the functional questions'
			fields: ['leg_grade']
			prefill: 'encounter_incat'
		'INCAT Score':
			note: 'ODSS = arm grade + leg grade'
			fields: ['score']
view:
	comment: 'Patient > Careplan > Encounter > INCAT'
	grid:
		fields: ['created_on', 'assessment_date', 'created_by', 'score']
		sort: ['-id']
	label: 'Patient Encounter: INCAT'
