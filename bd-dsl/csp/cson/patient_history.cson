fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	contact_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	contact_time:
		model:
			type: 'time'
			required: true
		view:
			label: 'Time'
			template: '{{now}}'

	# Past Medical History
	ever_hospitalized:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['ever_hospitalized_details']
		view:
			control : 'radio'
			label: 'Have you ever been hospitalized?'

	ever_hospitalized_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Hospitalization details'

	ever_serious_injury:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields:['ever_serious_injury_details']
		view:
			control : 'radio'
			label: 'Have you had any serious injuries and/or broken bones?'

	ever_serious_injury_details:
		view:
			label: 'Details'

	ever_blood_transfusion:
		model:
			max: 3
			source: ['No', 'Yes', 'Unknown']
			if:
				'Yes':
					fields:['ever_blood_transfusion_when']
		view:
			control : 'radio'
			label: 'Have you ever received a blood transfusion?'

	ever_blood_transfusion_when:
		view:
			label: 'When?'

	ever_traveled:
		model:
			max: 3
			source: ['No', 'Yes', 'Unknown']
			if:
				'Yes':
					fields:['ever_traveled_details']
		view:
			control : 'radio'
			label: 'Have you ever traveled or lived outside the United States or Canada?'

	ever_traveled_details:
		view:
			label: 'When and Where?'

	#Past Procedures
	had_xray:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_xray_details']
		view:
			control : 'radio'
			label: 'Abnormal chest x-ray'

	had_xray_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'

	had_surgeries:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields:['had_surgeries_details']
		view:
			control : 'radio'
			label: 'Surgeries'

	had_surgeries_details:
		view:
			label: 'Surgery Details'

	had_anxiety:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_anxiety_details']
		view:
			control : 'radio'
			label: 'Anxiety, depression or mental illness'

	had_anxiety_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'

	had_blood_problem:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_blood_problem_details']
		view:
			control : 'radio'
			label: 'Blood problems (abnormal bleeding, anemia,
high or low white count)'

	had_blood_problem_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'

	had_diabetes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_diabetes_details']
		view:
			control : 'radio'
			label: 'Diabetes'

	had_diabetes_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'

	had_high_bp:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_high_bp_details']
		view:
			control : 'radio'
			label: 'High blood pressure'

	had_high_bp_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'

	had_high_chol:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_high_chol_details']
		view:
			control : 'radio'
			label: 'High cholesterol or triglycerides'

	had_high_chol_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'

	had_std:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_std_details']
		view:
			control : 'radio'
			label: 'Sexually transmitted disease'

	had_std_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'

	had_stroke:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_stroke_details']
		view:
			control : 'radio'
			label: 'Stroke or TIA'

	had_stroke_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'

	had_drug_abuse:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_drug_abuse_details']
		view:
			control : 'radio'
			label: 'Treatment for alcohol and/or drug abuse'

	had_drug_abuse_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'

	had_tb:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_tb_details']
		view:
			control : 'radio'
			label: 'Tuberculosis or positive tuberculin skin test'

	had_tb_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'

	# Social Information
	have_advanced_dir:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Do you have an advance directive?'

	have_power_attr:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_power_attr_whom']
		view:
			control : 'radio'
			label: 'Have you designated Medical Durable Power of Attorney to anyone?'

	have_power_attr_whom:
		view:
			label: 'Describe designation'

	# Social History
	education_level:
		model:
			source: ['Primary School', 'High School', "Some College", "Associate Degree", "Bachelor's Degree", 'Post-Graduate Degree']
		view:
			control : 'radio'
			label: 'Education Level'

	employement_status:
		model:
			source: ['Retired', 'Unemployed', 'Homemaker', 'Employed']
			if:
				'Employed':
					fields: ['employement_status_occupation']
		view:
			control : 'radio'
			label: 'Employment Status'

	employement_status_occupation:
		view:
			label: 'Occupation'

	martial_status:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Are you currently married?'

	# Substance Abuse
	use_caffeine:
		model:
			source: ['Currently Use', 'Previously Used', 'No']
			if:
				'Currently Use':
					fields: ['use_caffeine_amt', 'use_caffeine_amt_freq']
				'Previously Used':
					fields: ['use_caffeine_amt', 'use_caffeine_amt_freq', 'use_caffeine_past_long', 'use_caffeine_past_stopped']
		view:
			control : 'radio'
			label: 'Caffeine: coffee, tea, soda'

	use_caffeine_amt:
		model:
			required: true
			type: 'int'
		view:
			label: 'How many servings/cups per day/week/month?'
			note: 'numbers only'

	use_caffeine_amt_freq:
		model:
			required: true
			source: ['Day', 'Week', 'Month']
		view:
			label: 'Servings Frequency Type'

	use_caffeine_past_long:
		model:
			required: true
			type: 'int'
		view:
			label: 'Over how many years?'

	use_caffeine_past_stopped:
		model:
			required: true
			type: 'date'
		view:
			label: 'Approximate date stopped'

	use_tobacoo:
		model:
			source: ['Currently Use', 'Previously Used', 'No']
			if:
				'Currently Use':
					fields: ['use_tobacoo_amt']
				'Previously Used':
					fields: ['use_tobacoo_amt', 'use_tobacoo_past_long', 'use_tobacoo_past_stopped']
		view:
			control : 'radio'
			label: 'Tobacco'

	use_tobacoo_amt:
		model:
			required: true
			rounding: 0.25
			type: 'decimal'
		view:
			note: '(20 cigarettes = 1 pack)'
			label: 'How many cigarettes per day?'

	use_tobacoo_past_long:
		model:
			required: true
			type: 'int'
		view:
			label: 'Over how many years?'

	use_tobacoo_past_stopped:
		model:
			required: true
			type: 'date'
		view:
			label: 'Approximate date stopped'

	use_alchohol:
		model:
			source: ['Currently Use', 'Previously Used', 'No']
			if:
				'Currently Use':
					fields: ['use_alchohol_amt', 'use_alchohol_amt_freq', 'use_alchohol_amt_size']
				'Previously Used':
					fields: ['use_alchohol_amt', 'use_alchohol_amt_freq', 'use_alchohol_past_long', 'use_alchohol_past_stopped', 'use_alchohol_amt_size']
		view:
			control : 'radio'
			label: 'Alcohol – beer, wine, liquor'

	use_alchohol_amt:
		model:
			required: true
			rounding: 0.25
			type: 'decimal'
		view:
			label: 'How many servings per week/month/year?'
			note: 'numbers only'

	use_alchohol_amt_freq:
		model:
			required: true
			source: ['Week', 'Month', 'Year']
		view:
			label: 'Servings Frequency Type'

	use_alchohol_amt_size:
		model:
			required: true
			source: ['ounces', 'drinks (serving size: 12 oz beer, 5 oz wine)']
		view:
			label: 'Servings Type'

	use_alchohol_past_long:
		model:
			required: true
			type: 'int'
		view:
			label: 'Over how many years?'

	use_alchohol_past_stopped:
		model:
			required: true
			type: 'date'
		view:
			label: 'Approximate date stopped'

	use_drugs:
		model:
			source: ['Currently Use', 'Previously Used', 'No']
			if:
				'Currently Use':
					fields: ['use_drugs_amt']
				'Previously Used':
					fields: ['use_drugs_amt', 'use_drugs_past_long', 'use_drugs_past_stopped']
		view:
			control : 'radio'
			label: 'Recreational/Street drugs'

	use_drugs_amt:
		model:
			required: true
		view:
			label: 'Type/Amount/Frequency'

	use_drugs_past_long:
		model:
			required: true
			type: 'int'
		view:
			label: 'Over how many years?'

	use_drugs_past_stopped:
		model:
			required: true
			type: 'date'
		view:
			label: 'Approximate date stopped'

	#Family History

	fam_cancer:
		model:
			multi: true
			source: ['Grandparents', 'Father', 'Mother', 'Brothers', 'Sisters', 'Sons', 'Daughters', 'None']
		view:
			control : 'checkbox'
			label: 'Cancer'
			validate: [
					name: 'ExclusiveValidator'
					match: ['None']
			]

	fam_heart_disease:
		model:
			multi: true
			source: ['Grandparents', 'Father', 'Mother', 'Brothers', 'Sisters', 'Sons', 'Daughters', 'None']
		view:
			control : 'checkbox'
			label: 'Heart Disease'
			validate: [
					name: 'ExclusiveValidator'
					match: ['None']
			]

	fam_diabetes:
		model:
			multi: true
			source: ['Grandparents', 'Father', 'Mother', 'Brothers', 'Sisters', 'Sons', 'Daughters', 'None']
		view:
			control : 'checkbox'
			label: 'Diabetes'
			validate: [
					name: 'ExclusiveValidator'
					match: ['None']
			]

	fam_stroke:
		model:
			multi: true
			source: ['Grandparents', 'Father', 'Mother', 'Brothers', 'Sisters', 'Sons', 'Daughters', 'None']
		view:
			control : 'checkbox'
			label: 'Stroke/TIA'
			validate: [
					name: 'ExclusiveValidator'
					match: ['None']
			]

	fam_high_bp:
		model:
			multi: true
			source: ['Grandparents', 'Father', 'Mother', 'Brothers', 'Sisters', 'Sons', 'Daughters', 'None']
		view:
			control : 'checkbox'
			label: 'High Blood Pressure'
			validate: [
					name: 'ExclusiveValidator'
					match: ['None']
			]

	fam_chol:
		model:
			multi: true
			source: ['Grandparents', 'Father', 'Mother', 'Brothers', 'Sisters', 'Sons', 'Daughters', 'None']
		view:
			control : 'checkbox'
			label: 'High Cholesterol or Triglycerides'
			validate: [
					name: 'ExclusiveValidator'
					match: ['None']
			]

	fam_liver:
		model:
			multi: true
			source: ['Grandparents', 'Father', 'Mother', 'Brothers', 'Sisters', 'Sons', 'Daughters', 'None']
		view:
			control : 'checkbox'
			label: 'Liver Disease'
			validate: [
					name: 'ExclusiveValidator'
					match: ['None']
			]

	fam_drug_abuse:
		model:
			multi: true
			source: ['Grandparents', 'Father', 'Mother', 'Brothers', 'Sisters', 'Sons', 'Daughters', 'None']
		view:
			control : 'checkbox'
			label: 'Alcohol or Drug Abuse'
			validate: [
					name: 'ExclusiveValidator'
					match: ['None']
			]

	fam_anxiety:
		model:
			multi: true
			source: ['Grandparents', 'Father', 'Mother', 'Brothers', 'Sisters', 'Sons', 'Daughters', 'None']
		view:
			control : 'checkbox'
			label: 'Anxiety, Depression or Psychiatric Illness'
			validate: [
					name: 'ExclusiveValidator'
					match: ['None']
			]

	fam_tuberculosis:
		model:
			multi: true
			source: ['Grandparents', 'Father', 'Mother', 'Brothers', 'Sisters', 'Sons', 'Daughters', 'None']
		view:
			control : 'checkbox'
			label: 'Tuberculosis'
			validate: [
					name: 'ExclusiveValidator'
					match: ['None']
			]

	comment:
		view:
			control: 'area'
			label: 'Comments'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		review:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	name: '{created_on} : {patient_id_auto_name}'
	sections:
		'Contact Date/Time':
			fields: ['contact_date', 'contact_time']
		'Past Medical History':
			fields: [
						'ever_hospitalized', 'ever_hospitalized_details', 'ever_serious_injury', 'ever_serious_injury_details', 'ever_blood_transfusion', 'ever_blood_transfusion_when', 'ever_traveled', 'ever_traveled_details',
					]
		'Past Procedures':
			note: 'Have you ever had any of the following?'
			fields: [ 'had_xray', 'had_xray_details', 'had_surgeries', 'had_surgeries_details',
			'had_anxiety', 'had_anxiety_details', 'had_blood_problem', 'had_blood_problem_details',
			'had_diabetes', 'had_diabetes_details', 'had_high_bp', 'had_high_bp_details',
			'had_high_chol', 'had_high_chol_details', 'had_std', 'had_std_details',
			'had_stroke', 'had_stroke_details', 'had_drug_abuse', 'had_drug_abuse_details',
			'had_tb', 'had_tb_details'
			]
		'Social Information':
			fields: ['have_advanced_dir', 'have_power_attr', 'have_power_attr_whom']
		'Social History':
			fields: ['education_level', 'employement_status', 'employement_status_occupation', 'martial_status']
		'Substance Abuse':
			note: 'Have you in the past or are you currently using any of the following'
			fields: ['use_caffeine', 'use_caffeine_amt', 'use_caffeine_amt_freq', 'use_caffeine_past_stopped', 'use_caffeine_past_long', 'use_tobacoo', 'use_tobacoo_amt', 'use_tobacoo_past_long', 'use_tobacoo_past_stopped', 'use_alchohol', 'use_alchohol_amt', 'use_alchohol_amt_freq', 'use_alchohol_amt_size', 'use_alchohol_past_long', 'use_alchohol_past_stopped', 'use_drugs', 'use_drugs_amt', 'use_drugs_past_long', 'use_drugs_past_stopped' ]
		'Family History':
			note: 'Do you have a family history of any of the following (Select all that apply)'
			fields: ['fam_cancer', 'fam_heart_disease', 'fam_diabetes', 'fam_stroke', 'fam_high_bp', 'fam_chol', 'fam_liver', 'fam_drug_abuse', 'fam_anxiety', 'fam_tuberculosis']
		'Comments':
			fields: ['comment']

	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Pharmacist History"
	]

view:
	comment: 'Patient > History'
	grid:
		fields: ['created_on', 'updated_on', 'reviewed_by', 'reviewed_on']
		sort: ['-id']
	label: 'Patient Medical/Social History'
	max_rows: 1
	open: 'read'
