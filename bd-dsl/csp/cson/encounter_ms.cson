fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	# Disease Specific Participation
	pt_disease_hidden:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'Yes'
			if:
				'Yes':
					sections: ['Difficulties', 'Grip dynamometry', 'Wellness']
		view:
			control : 'radio'
			label: 'Will a disease specific assessment be completed on this visit?'

	# Difficulties
	multiscler_diff_write:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable or illegible, 1 = Difficulty, 2 = Normal)'
			label: 'Writing'

	multiscler_diff_utensils:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Difficult or very clumsy, 2 = Normal)'
			label: 'Using utensils'

	multiscler_diff_dress:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Getting dressed'

	multiscler_diff_blurred_vision:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Blind, 1 = Impaired vision, 2 = Normal)'
			label: 'Blurred or double vision / loss of sight'

	multiscler_diff_stiff:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = No stiffness)'
			label: 'Stiff legs'

	multiscler_diff_speech:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unintelligible, 1 = Slurred, 2 = Normal)'
			label: 'Speech'

	multiscler_diff_swallow:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Swallowing'

	multiscler_diff_cramps:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = None)'
			label: 'Cramps / spasms / spasticity'

	multiscler_diff_numb:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = None)'
			label: 'Numbness / tingling / buzzing / vibration'

	multiscler_diff_ftdp:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = None)'
			label: 'Foot drop'

	multiscler_diff_cord:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = None)'
			label: 'Loss of coordination'

	multiscler_diff_motor:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = None)'
			label: 'Tremor with fine motor skills'

	multiscler_diff_vertigo:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = None)'
			label: 'Vertigo'

	multiscler_diff_balance:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Stand on one foot for 5 seconds (using either foot)'

	multiscler_diff_weak:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = None)'
			label: 'Muscle Weakness'

	multiscler_diff_cog:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = None)'
			label: 'Cognition'

	multiscler_diff_bladder:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Total incontinence, 1 = Partial incontinence, 2 = No incontinence)'
			label: 'Bladder / Bowel function'

	multiscler_diff_fatigue:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = None)'
			label: 'Fatigue'

	multiscler_diff_walk:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control : 'radio'
			note: '(0 = Unable, 1 = Difficult or requires assistance, 2 = Normal)'
			label: 'Walking 10 feet'

	multiscler_diff_pain:
		model:
			max: 2
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control : 'radio'
			note: '(0 = No pain  -  10 = Severe pain)'
			label: 'Pain'

	# Grip dynamometry
	grip_right_1_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Right Hand Grip Strength 1'

	grip_right_2_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Right Hand Grip Strength 2'

	grip_right_3_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Right Hand Grip Strength 3'

	grip_left_1_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Left Hand Grip Strength 1'

	grip_left_2_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Left Hand Grip Strength 2'

	grip_left_3_mc:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			offscreen: true
			label: 'Left Hand Grip Strength 3'

	grip_right_1_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Right Hand Grip Strength 1'

	grip_right_2_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Right Hand Grip Strength 2'

	grip_right_3_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Right Hand Grip Strength 3'

	grip_left_1_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Left Hand Grip Strength 1'

	grip_left_2_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Left Hand Grip Strength 2'

	grip_left_3_pounds:
		model:
			max: 150
			min: 0
			access:
				read: ['patient']
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Left Hand Grip Strength 3'

	# Wellness
	since_last:
		model:
			max: 64
			min: 1
			source: ['Improved', 'Improved, then relapsed', 'No change (stable)', 'Relapsed since last infusion (worsening)', 'N/A (First Lifetime Infusion)']
			if:
				'Improved, then relapsed':
					fields: ['days_relapse', 'relapse_duration', 'relapse_ongoing', 'last_relapse']
				'Relapsed since last infusion (worsening)':
					fields: ['days_relapse', 'relapse_duration', 'relapse_ongoing', 'last_relapse']
		view:
			control : 'select'
			label: 'Since Last Infusion'

	days_relapse:
		model:
			type: 'int'
			min: 1
			required: true
		view:
			label: 'How many days after your last infusion until your symptoms reappeared?'

	relapse_duration:
		model:
			max: 128
			source: ['Less than 1 week', 'Less than one month', 'Greater than 1 month']
		view:
			label: 'How long did they last?'

	relapse_ongoing:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Is it still going on?'

	last_relapse:
		model:
			type: 'date'
			required: true
		view:
			label: 'Date of last relapse'

	compared_wellness:
		model:
			max: 12
			source: ['Better', 'Same', 'Worse']
		view:
			control : 'radio'
			label: 'Compared to onset of my disease, as of today I am:'

	distance_walk:
		model:
			max: 32
			source: ['Less than 10 feet', '11-50 feet', '51-100 feet', '101-150 feet', '> 150 feet']
		view:
			control : 'radio'
			label: 'How far can you walk comfortably'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'Careplan', 'encounter']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_ivig_ms:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Disease Specific Participation':
			fields: ['pt_disease_hidden']
		'Difficulties':
			note: 'On a scale of zero to two (0 - 2) (unless specified), with 0 meaning Extreme Difficulty and 2 meaning Normal, please rate your difficulty today with:'
			fields: ['multiscler_diff_write', 'multiscler_diff_utensils', 'multiscler_diff_dress', 'multiscler_diff_blurred_vision', 'multiscler_diff_stiff', 'multiscler_diff_speech', 'multiscler_diff_swallow', 'multiscler_diff_cramps',
				'multiscler_diff_numb', 'multiscler_diff_ftdp', 'multiscler_diff_cord', 'multiscler_diff_motor', 'multiscler_diff_vertigo', 'multiscler_diff_balance',
				'multiscler_diff_weak', 'multiscler_diff_cog', 'multiscler_diff_bladder', 'multiscler_diff_fatigue', 'multiscler_diff_walk', 'multiscler_diff_pain']
		'Grip dynamometry':
			note: 'Measure strength of both hand grips 3 times'
			fields: ['grip_right_1_mc', 'grip_right_2_mc', 'grip_right_3_mc', 'grip_left_1_mc', 'grip_left_2_mc', 'grip_left_3_mc',
			'grip_right_1_pounds', 'grip_right_2_pounds', 'grip_right_3_pounds', 'grip_left_1_pounds', 'grip_left_2_pounds', 'grip_left_3_pounds']
		'Wellness':
			fields: ['since_last', 'days_relapse', 'relapse_duration', 'relapse_ongoing', 'last_relapse', 'compared_wellness', 'distance_walk']

view:
	comment: 'Patient > Careplan > Encounter > IG > Multiple Sclerosis'
	label: 'Patient Encounter: IG - Multiple Sclerosis'
