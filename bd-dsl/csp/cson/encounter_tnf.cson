fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	# Disease Specific Participation
	fever_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['fever_episodes_cnt', 'fever_temp']
		view:
			control : 'radio'
			label: 'Have you had any episodes of fever?'

	fever_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of fever episodes per week'

	fever_temp:
		model:
			min: 1
			type: 'int'
			rounding: 0.1
			required: true
		view:
			label: 'Fever Temperature'

	has_infection:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['has_infection_details']
		view:
			control : 'radio'
			label: 'Have you been treated for any infections since your last appointment?'

	has_infection_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'

	fatigue_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['fatigue_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any episodes of fatigue?'

	fatigue_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes per week'

	# Drug Administration
	pump_type:
		model:
			prefill: ['encounter_tnf']
			source: {na:'NA', ambulatory:'Ambulatory', stationary:'Stationary or pole-mounted', gravity:'Gravity or rate-flow tubing', other:'Other'}
			if:
				other:
					fields: ['pump_type_other']
		view:
			control : 'radio'
			label: 'Pump'

	pump_type_other:
		model:
			prefill: ['encounter_tnf']
			max: 1024
			required: true
		view:
			label: 'Pump Other Details'

	tnf_caregiver_demo:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
		view:
			control : 'radio'
			label: 'Patient/caregiver able to return demonstrate proper TNF-Inhibitor preparation, administration (if applicable)?'

	add_training:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['add_training_details']
		view:
			control : 'radio'
			label: 'Will patient/caregiver need additional training?'

	add_training_details:
		model:
			required: true
		view:
			label: 'Describe additional education requirements'

	admin_adr:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['admin_adr_detail']
		view:
			control : 'radio'
			label: 'Does patient report any ADRs associated with administration of TNF-Inhibitor or that develop shortly after administration?'

	admin_adr_detail:
		model:
			required: true
			multi: true
			source: ['Drop in BP', 'Increase in BP', 'Edema', 'Drop in urine output', 'Headaches or Migraine', 'Fever and chills',
			'Allergy', 'Injection site related event', 'Other']
			if:
				'Other':
					fields: ['admin_adr_detail_other']
		view:
			control : 'checkbox'
			label: 'ADRs associated with administration'

	admin_adr_detail_other:
		model:
			required: true
		view:
			label: 'ADR Other'

	# Post Visit
	tnf_administered:
		model:
			max: 32
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Was TNF-inhibitor administered?'

	tnf_comment:
		view:
			control : 'area'
			label: 'TNF-inhibitor Comment'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_tnf:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Patient Questionnaire - TNF':
			area:'questions'
			fields: ['fever_episodes', 'fever_episodes_cnt', 'fever_temp', 'has_infection', 'has_infection_details', 'fatigue_episodes', 'fatigue_episodes_cnt']
		'Drug Administration':
			fields: ['pump_type', 'pump_type_other', 'tnf_caregiver_demo', 'add_training', 'add_training_details', 'admin_adr', 'admin_adr_detail', 'admin_adr_detail_other']
			prefill: 'encounter_tnf'
		'Post Visit':
			area: 'footer'
			fields: ['tnf_administered', 'tnf_comment']

view:
	comment: 'Patient > Careplan > Encounter > TNF-Inhibitor'
	label: "Patient Encounter: TNF-Inhibitor"
