fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	linked_intake:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	date:
		model:
			type: 'date'
		view:
			label: 'Start of Care'

	status:
		model:
			required: true
			source:['Pending','Active','Discharged','Onhold','PRN','Transfer']
			if:
				'Discharged':
					fields:['discharged','free_text']
				'Onhold':
					fields:['follow_up_date','on_hold_reason']
				'Transfer':
					fields:['date_of_transfer','location']
		view:
			label: 'Status'

	discharged:
		model:
			type: 'date'
		view:
			label: 'Discharge Date'

	free_text:
		model:
			type: 'text'
		view:
			label: 'Discharge Instructions'

	follow_up_date:
		model:
			type: 'date'
		view:
			label: 'Follow Up Date'

	on_hold_reason:
		model:
			type: 'text'
		view:
			label: 'On Hold Reason'

	date_of_transfer:
		model:
			type: 'date'
		view:
			label: 'Date of Transfer'

	location:
		model:
			type: 'text'
		view:
			label: 'Location'

	informed_consent:
		model:
			source:
				info_consent: 'Informed Consent'
				rel_file: 'No Release on File'
				sign_rel_file: 'Signed Release on File'
		view:
			label: 'Release Information'

	ass_of_benifits:
		model:
			source:
				on_file: 'Assignment on File'
				not_file: 'Not on File'
		view:
			label: 'Assignments of Benefits'

	cert_physician:
		model:
			source: 'physician'
		view:
			label: 'Certifying Physician'

	primary_drug:
		model:
			prefill: ['parent.primary_drug']
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Primary Drug'

	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			prefill: ['careplan_order.therapy_id']
		view:
			label: 'Primary Therapy'

	primary_diagnosis:
		model:
			prefill: ['parent.primary_diagnosis']
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			label: 'Primary Diagnosis'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin','liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin','csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['created_on']
	indexes:
		many: [
			['patient_id']
			['linked_intake']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				patient_id: 'patient_id' 

		careplan_order:
			link:
				patient_id: 'patient_id'
			max: 'created_on'

	sections_group: [
		'Nursing - Admission Form':
			sections: [
				'Admission Details':
						fields: ['date','status','discharged','free_text','follow_up_date','on_hold_reason','date_of_transfer','location']
				'Release Information':
						fields: ['informed_consent','ass_of_benifits','cert_physician','primary_drug','primary_diagnosis','therapy_1']
			]
	]

view:
	comment: 'Patient > Careplan > Admission/Discharge'
	grid:
		fields: ['created_on', 'created_by', 'date', 'status']
		sort: ['-id']
	label: 'Admission/Discharge'
