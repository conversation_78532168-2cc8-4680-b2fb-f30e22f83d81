fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	brand_name_id:
		model:
			prefill: ['careplan_order.brand_name_id']
			if:
				'CIMZIA':
					fields:['injection_training']
				'SIMPONI':
					fields:['simponi_injection_training']
				'Enbrel':
					fields:['enbrel_injection_training']
				'STELARA':
					fields:['stelara_injection_training','self_injectable']
				'ACTEMRA':
					fields:['actemra_injection_training']
				'REMICADE':
					fields:['mouse_allergy', 'nurse_present', 'adult_present', 'adult_present_after', 'physician_available']
					sections:['TNF-Inhibitor Hepatic']
				'ORENCIA':
					fields:['orencia_injection_training', 'nurse_present', 'adult_present', 'adult_present_after', 'physician_available']
				'SIMPONI ARIA':
					fields:['nurse_present', 'adult_present', 'adult_present_after', 'physician_available']
				'Humira':
					fields:['humira_injection_training']
					sections:['TNF-Inhibitor Hepatic']
		view:
			offscreen: true
			readonly: true

	dx_ids:
		model:
			prefill: ['careplan_order']
			if:
				'psoriasis':
					sections:['TNF-Inhibitor Psoriasis General Assessment']
				'parthritis':
					sections:['TNF-Inhibitor Psoriasis General Assessment']
				'rheumatology':
					sections: ['TNF-RA Questionnaire Participation']
		view:
			label: 'Primary Disease'
			offscreen: true
			readonly: true

	gender:
		model:
			prefill: ['patient']
			if:
				'Female':
					fields: ['will_be_pregnant']
		view:
			label: 'Gender'
			offscreen: true
			readonly: true

	first_time:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is this the first time you are receiving TNF-Inhibitor therapy?'
			offscreen: true
			readonly: true


	last_therapy_drug_type:
		model:
			multi: true
			source: ['Analgesics', 'NSAIDs', 'Corticosteroids', 'DMARDs (Disease Modifying Anti-Rheumatic Drugs)', 'Biologics']
		view:
			label: 'What medications you used before for Rheumatoid Arthritis?'
			offscreen: true
			readonly: true

	last_therapy_date:
		model:
			type: 'date'
		view:
			label: 'When did you receive this therapy last?'
			offscreen: true
			readonly: true

	last_therapy_interval:
		model:
			max: 6
			min: 1
			source: ['Days','Weeks','Months','Years']
			if:
				'Days':
					fields: ['last_therapy_interval_days']
				'Weeks':
					fields: ['last_therapy_interval_weeks']
				'Months':
					fields: ['last_therapy_interval_months']
				'Years':
					fields: ['last_therapy_interval_years']
		view:
			control: 'radio'
			label: 'How long was your last therapy?'
			offscreen: true
			readonly: true

	last_therapy_interval_days:
		model:
			max: 1000
			min: 1
			rounding: 1
			type: 'decimal'
		view:
			label: 'Number of days'
			offscreen: true
			readonly: true

	last_therapy_interval_weeks:
		model:
			max: 1000
			min: 1
			rounding: 1
			type: 'decimal'
		view:
			label: 'Number of weeks'
			offscreen: true
			readonly: true

	last_therapy_interval_months:
		model:
			max: 1000
			min: 1
			rounding: 1
			type: 'decimal'
		view:
			label: 'Number of months'
			offscreen: true
			readonly: true

	last_therapy_interval_years:
		model:
			max: 1000
			min: 1
			rounding: 1
			type: 'decimal'
		view:
			label: 'Number of years'
			offscreen: true
			readonly: true

	last_therapy_response:
		model:
			max: 12
			min: 1
			source: ['Excellent','Good','Fair','Poor', 'Relapsed', 'Stopped due to side effects']
		view:
			control: 'radio'
			label: 'What was the response to the treatment?'
			offscreen: true
			readonly: true

	had_adr:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Did you experience any adverse drug reactions (ADRs)?'
			offscreen: true
			readonly: true

	# Assessment Questions
	psoriasis_loc:
		model:
			max: 128
			multi: true
			source:['Hands','Feet','Face','Scalp','Groin','Nails','Other']
			if:
				'Other':
					fields: ['psoriasis_loc_other']
		view:
			control: 'checkbox'
			note: 'Select any that apply'
			label: 'Location of psoriasis'

	psoriasis_loc_other:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Location of psoriasis other'

	psoriasis_severe:
		model:
			max: 128
			source:['Mild (up to 3% BSA)','Moderate (3-10% BSA)','Severe (greater than 10% BSA)']
			if:
				'Severe (greater than 10% BSA)':
					fields: ['psoriasis_bsa']
		view:
			control: 'radio'
			note: 'Select any that apply'
			label: 'Psoriasis Severity'

	psoriasis_bsa:
		model:
			min: 1
			max: 100
			type: 'int'
		view:
			label: 'BSA (%)'

	palm_cover_cnt:
		model:
			type: 'int'
			min: 1
			max: 100
		view:
			label: 'If you had to take the palm of your hand and cover up all of the patches of psoriasis on your body today, how many palms of your hand do you think that it would take?'
			note: 'One palm of your hand is equal to about 1% of your body surface area (BSA). If your psoriasis is only scattered small dots, try to imagine combining them together into one patch. Please remember to include your scalp and back if affected. Do not include areas in which psoriasis has faded, leaving only changes in the color of the skin.'

	mouse_allergy:
		model:
			source: ['No', 'Yes', 'Do not know']
		view:
			control: 'radio'
			label: 'Are you allergic or have a sensitivity to murine or mouse proteins?'

	immuno_drugs:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: [ 'immuno_drugs_list','immuno_drugs_details']
		view:
			control: 'radio'
			label: 'Are you currently taking or plan to start immunosuppressant therapy?'

	immuno_drugs_list:
		model:
			subfields:
				stamp:
					label: 'Drug'
					type: 'text'
				temp:
					label: 'Dose'
					type: 'text'
				pulse:
					label: 'Frequency'
					type: 'text'
			type: 'json'
		view:
			control: 'grid'
			label: 'Immunosuppressant Drugs Taken'

	immuno_drugs_details:
		model:
			required: false
		view:
			control: 'area'
			label: 'Immunosuppressant Drugs Taken'

	have_vaccine:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: [ 'have_vaccine_date',   'have_vaccine_details']
		view:
			control: 'radio'
			label: "Have you received any recent vaccines or plan to receive any vaccines while receiving TNF-inhibitor therapy?"

	have_vaccine_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Vaccines Date'

	have_vaccine_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Vaccines Detail'

	have_antibiotic:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['ant_list',   'have_antibiotic_details']
		view:
			control: 'radio'
			label: "Are you currently receiving antibiotic therapy or have active infection?"



	ant_list:
		model:
			multi: true
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
			# sourcefilter:
			# 	therapy_filter:
			# 		'static': 'Antibiotic'
		view:
			label: 'Antibiotic(s)'


	have_antibiotic_details:
		model:
			required: false
		view:
			control: 'area'
			label: "Antibiotic details"

	have_fever:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_fever_details']
		view:
			control: 'radio'
			label: "Does patient report recent fevers?"

	have_fever_details:
		view:
			label: "Fever details"

	have_infection:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_infection_details']
		view:
			control: 'radio'
			label: "Has the patient had any chronic or recent infections?"

	have_infection_details:
		view:
			label: "Infection details"

	have_exposure:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_exposure_details']
		view:
			control: 'radio'
			label: "Have you had any recent exposure to tuberculosis (TB), HBV, or mycoses?"

	have_exposure_details:
		model:
			required: true
		view:
			control: 'area'
			label: "Exposure details"

	have_seizure:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_seizure_details']
		view:
			control: 'radio'
			label: "Do you have seizure disorder or CNS demyelinating disorder?"

	have_seizure_details:
		model:
			required: true
		view:
			control: 'area'
			label: "Seizure/CNS details"

	have_malignancy:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_malignancy_details']
		view:
			control: 'radio'
			label: "Do you have a history of malignancy or myelosuppression?"

	have_malignancy_details:
		model:
			required: true
		view:
			control: 'area'
			label: "Malignancy or myelosuppression details"

	will_be_pregnant:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Do you plan to get pregnant while on this medication?"


	has_ems:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Does patient have access to EMS (w/in 15 minutes)?"

	nurse_present:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will nurse be present for the entire infusion?"

	adult_present:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will an additional adult be present for the entire infusion?"

	adult_present_after:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will an additional adult be present for 2 to 3 hours after infusion completed?"

	physician_available:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will the ordering physician be available by phone during the infusion?"

	# Hepatic Disease Risk
	hepatic_disease:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with Hepatic disease?'

	has_htn:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pressure_controlled']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with hypertension (HTN)?'

	pressure_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your blood pressure currently controlled?'

	high_chol:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['chol_controlled']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with high cholesterol?'

	chol_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your cholesterol currently controlled?'

	heart_cond:
		model:
			max: 64
			min: 1
			multi: true
			source: ['Congestive heart failure (CHF)', 'Cardiomyopathy', 'Valve Disease', 'Congenital Defects', 'Atrial Fibrillation', 'Angina', 'Pulmonary disease', 'Other', 'None']
			if:
				'Other':
					fields: ['heart_cond_other']
		view:
			control: 'checkbox'
			label: 'Do you suffer from any of the following heart diseases?'

	heart_cond_other:
		view:
			control: 'area'
			label: 'Other Heart Diseases'

	immobil_periods:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you experience prolonged periods of immobilization?'

	diag_thomb:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with thrombophilia?'

	clam_hands:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_sepsis']
		view:
			control: 'radio'
			label: 'Do your hands/extremities feel cold and clammy (experience low BP)?'

	had_sepsis:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any recent cases of sepsis or infection in blood?'

	has_cancer:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes', 'N/A']
		view:
			control: 'radio'
			label: 'Are you currently or have you recently received cancer treatment?'

	given_birth:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes', 'N/A']
		view:
			control: 'radio'
			label: 'Are you currently pregnant or have given birth in the last 6 weeks?'

	# Support Questions
	injection_training:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields:['injection_training_type']
		view:
			control : 'radio'
			label: 'Will patient need injection training/nurse support?'

	injection_training_type:
		model:
			max: 3
			source: {physician_train:'Physician office to train patient to use prefilled syringes', nurse_train:'Home Health Nurse to train patient to use prefilled syringes', physician_lyo_powder:'Physician office to administer Lyophilized powder(LYO)', nurse_lyo_powder:'Home Health Nurse to administer Lyophilized powder(LYO)'}
		view:
			control : 'radio'
			label: 'Training/nurse Support Type'

	nursing_visit:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Will patient need nursing visit?'

	# Humira specific questions
	humira_injection_training:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields:['humira_training_site']
		view:
			control : 'radio'
			label: 'Will patient need Humira injection training/nurse support?'

	humira_training_site:
		model:
			max: 3
			source: {home:"Patient's home", clinical_site:'Clinical site', physician_office:'Physician office'}
		view:
			control : 'radio'
			label: 'MyHumira Nurse to provide education & training for Sub-Q injection at'

	# Simponi specific questions
	simponi_injection_training:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Will patient need Simponi injection training/nurse support?'

	# Enbrel specific questions
	enbrel_injection_training:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Will patient need Enbrel Support® Nurse to provide education & training for Sub-Q injection?'

	# Stelara specific questions
	stelara_injection_training:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Will patient need Stelara injection training/nurse support?'

	self_injectable:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Is patient eligible for self-injection?'

	# Actemra specific questions
	actemra_injection_training:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Will patient need Actemra Nurse (RN) to provide education & training for Sub-Q injection?'

	# Orencia specific questions
	orencia_injection_training:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Will patient need Orencia Sub-Q injection training/nurse support?'

	# RA Questionnaire Participation
	ra_first:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['TNF-RA Questionnaire']
		view:
			control : 'radio'
			label: 'Is this your first time on drug therapy for the treatment of Rheumatoid Arthritis?'

	ra_therapy_status:
		model:
			source: ['Currently in the middle of treatment', 'Failed treatment', 'Other']
			if:
				'Currently in the middle of treatment':
					fields: ['ra_drug_taking']
				'Failed treatment':
					fields: ['ra_drug_failed']
				'Other':
					fields: ['ra_status_other']
		view:
			control : 'radio'
			label: 'What best describes your RA therapy status?'

	ra_drug_taking:
		model:
			required: true
		view:
			label: 'Current RA therapy:'

	ra_drug_failed:
		model:
			required: true
		view:
			label: 'Past RA therapy:'

	ra_status_other:
		model:
			required: true
		view:
			label: 'RA therapy status:'

	ra_knowledge:
		model:
			source:
				very: 'Very knowledgeable'
				moderate: 'Moderately knowledgeable'
				little: 'Not knowledgeable'
				none: 'No understanding'
			if:
				moderate:
					note: 'Review disease awareness with patient'
				little:
					note: 'Review disease awareness with patient'
				none:
					note: 'Review disease awareness with patient'
		view:
			control : 'radio'
			note: 'ask patient to tell you what they understand about their condition'
			label: 'How would you rate your understanding of Rheumatoid Arthritis?'

	ra_med_knowledge:
		model:
			source:
				very: 'Very knowledgeable'
				moderate: 'Moderately knowledgeable'
				little: 'Not knowledgeable'
				none: 'No understanding'
			if:
				moderate:
					note: 'Review disease treatment options with patient'
				little:
					note: 'Review disease treatment options with patient'
				none:
					note: 'Review disease treatment options with patient'
		view:
			control : 'radio'
			label: 'How would you rate your understanding of the drug therapy you have been prescribed for rheumatoid arthritis? Tell me what you know about the medication that has been prescribed.'

	ra_med_expect:
		model:
			source: ['No', 'Maybe']
		view:
			control : 'radio'
			label: 'Do you understand what the medication you have been prescribed is supposed to do? Can you share with me what you expect this medication to do?'

	ra_med_take:
		model:
			source: ['No', 'Maybe']
		view:
			control : 'radio'
			label: 'Do you know how and when to take the medication that has been prescribed? Can you tell me what instruction you have received for how and when to take your medication?'

	ra_med_se:
		model:
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Do you know what side effects you can expect from this drug therapy?'

	ra_med_adhere:
		model:
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Do you understand the importance of taking your medication and continuing to take your medication as your doctor prescribed?'

	ra_med_remember:
		model:
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Do you need help remembering to take your medication?'

	ra_med_call:
		model:
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Would you like a call from a pharmacist to discuss your medication therapy?'

	ra_med_comfort:
		model:
			source:
				very: 'Very comfortable'
				moderate: 'Somewhat comfortable'
				little: 'Not comfortable'
				none: 'Afraid of needles'
		view:
			control : 'radio'
			label: 'What is your comfort level with having to inject yourself to administer the medication?'

	ra_med_train:
		model:
			source: ['Schedule training', 'Completed training', 'No']
			if:
				'Schedule training':
					fields: ['ra_med_train_date']
				'Completed training':
					fields: ['ra_med_train_date']
				'No':
					fields: ['ra_med_train_setup']
		view:
			control : 'radio'
			label: 'Have you scheduled or completed injection training?'

	ra_med_train_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Injection Training Date (Scheduled/Completed)'

	ra_med_train_setup:
		model:
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Would you like to speak with someone from the pharmacy who can help you arrange injection training?'

	ra_depress:
		model:
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'In the past 30 days, have you felt down, depressed, or hopeless or felt little interest in doing things that you normally enjoy?'

	ra_employee:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['ra_missed_days']
		view:
			control : 'radio'
			label: 'Are you currently employed?'

	ra_missed_days:
		model:
			source: ['0', '1', '2', '3', '4 or more']
		view:
			control : 'radio'
			label: 'In the last 30 days, how many days from work have you missed because of your condition?'


	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'

		careplan_order:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
	sections:
		'TNF-Inhibitor General Assessment':
			fields: ['brand_name_id', 'dx_ids', 'gender', 'mouse_allergy', 'immuno_drugs', 'immuno_drugs_list' ,'immuno_drugs_details', 'have_vaccine', 'have_vaccine_date', 'have_vaccine_details', 'have_antibiotic', 'ant_list', 'have_antibiotic_details', 'have_fever', 'have_fever_details', 'have_infection', 'have_infection_details', 'have_exposure',
			'have_exposure_details', 'have_seizure', 'have_seizure_details', 'have_malignancy', 'have_malignancy_details', 'will_be_pregnant', 'has_ems', 'nurse_present', 'adult_present', 'adult_present_after', 'physician_available']
		'TNF-Inhibitor Psoriasis General Assessment':
			fields: ['psoriasis_loc', 'psoriasis_loc_other', 'psoriasis_severe', 'psoriasis_bsa', 'palm_cover_cnt']
		'TNF-Inhibitor Hepatic':
			fields: ['hepatic_disease']
		'TNF-Inhibitor Thromboembolic Risk Assessment ':
			fields: ['has_htn', 'pressure_controlled', 'high_chol', 'chol_controlled', 'heart_cond', 'heart_cond_other', 'immobil_periods', 'diag_thomb', 'clam_hands', 'had_sepsis', 'given_birth']
		'TNF-RA Questionnaire Participation':
			fields: ['ra_first']
		'TNF-RA Questionnaire':
			fields: ['ra_therapy_status', 'ra_drug_taking', 'ra_drug_failed', 'ra_status_other', 'ra_knowledge', 'ra_med_knowledge', 'ra_med_expect', 'ra_med_take', 'ra_med_se', 'ra_med_adhere', 'ra_med_remember', 'ra_med_call', 'ra_med_comfort', 'ra_med_train', 'ra_med_train_date', 'ra_med_train_setup', 'ra_depress', 'ra_employee', 'ra_missed_days']
		'TNF-Inhibitor Nurse Support':
				fields: ['injection_training', 'injection_training_type', 'humira_injection_training', 'humira_training_site', 'simponi_injection_training',
				'enbrel_injection_training', 'stelara_injection_training', 'actemra_injection_training', 'orencia_injection_training', 'self_injectable', 'nursing_visit']
view:
	comment: 'Patient > Careplan > Assessment > TNF-Inhibitor'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Assessment Questionnaire: TNF-Inhibitor'
	open: 'read'
