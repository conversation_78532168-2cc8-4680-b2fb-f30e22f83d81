fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	completed:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['intervention']
			required: true
		view:
			label: 'DUR completed?'
			control: 'radio'

	oncology_handout:
		model:
			source: ['No', 'Yes']
			required: false
		view:
			control: 'radio'
			label: 'CSP Oncology Handout'
			offscreen: true
			readonly: true

	intervention:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['dr_contacted', 'dr_intervention_details', 'patient_contacted', 'patient_intervention_details']
			required: true
		view:
			label: 'Intervention needed?'
			control: 'radio'

	dr_contacted:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Prescriber Contacted?'
			control: 'radio'

	dr_intervention_details:
		model:
			required: true
		view:
			label: 'Describe Action'
			control: 'area'

	patient_contacted:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Patient Contacted?'
			control: 'radio'
	
	patient_intervention_details:
		model:
			required: true
		view:
			label: 'Describe Action'
			control: 'area'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin','liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin','csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'

	sections: 
		'DUR Assessment':
			fields: [
						'completed','intervention', 'dr_contacted', 'dr_intervention_details', 'patient_contacted', 'patient_intervention_details'
					]
		
	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "DUR Assessment"
	]

view:
	comment: 'Patient > Careplan > Assessment DUR'
	grid:
		fields: ['created_on', 'created_by', 'intervention', 'dr_contacted', 'patient_contacted']
		sort: ['-id']
	label: 'DUR Assessment'
	open: 'read'
