fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	med_list:
		model:
			subfields:
				_meta:
					label: 'Prefilled/Additional'
					type: 'text'
				checkbox:
					label: 'Check'
					type: 'checkbox'
					style:
						width: '20%'
				medication_name:
					label: 'Drug'
					type: 'text'
					style:
						width: '80%'
					readonly: true
			type: 'json'
		view:
			control: 'grid'
			label: 'Medication List'
			offscreen: true
			readonly: true

	feeling:
		model:
			prefill: ['med_refill', 'ongoing']
			max: 3
			min: 1
			required: false
			source: ['Poor', 'Fair', 'Good', 'Very Good', 'Excellent']
		view:
			control: 'radio'
			label: 'How are you feeling today?'
			offscreen: true
			readonly: true

	days_missed:
		model:
			prefill: ['med_refill', 'ongoing']
			max: 3
			source: ['No', 'Yes', 'NA']
			required: false
			if:
				'Yes':
					fields: ['days_missed_reason', 'days_missed_count']
		view:
			control : 'radio'
			label: 'Have you missed any School/Work?'
			offscreen: true
			readonly: true

	days_missed_reason:
		model:
			prefill: ['med_refill', 'ongoing']
			required: false
			source: ['Due to ADR', 'Due to treating condition', 'Due to secondary condition', 'Avoidable due to lack of medication/supplies/nursing']
		view:
			control: 'radio'
			label: 'Days missed reason'
			offscreen: true
			readonly: true

	days_missed_count:
		model:
			prefill: ['med_refill', 'ongoing']
			type: 'int'
			min: 1
			max: 365
			required: false
		view:
			label: 'How many days?'
			offscreen: true
			readonly: true

	hosp_or_unplanned_visits:
		model:
			prefill: ['med_refill', 'ongoing']
			required: false
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Have you been hospitalized or had any unplanned physician visits?'
			offscreen: true
			readonly: true

	#outcome
	cust_med_aler_changes:
		model:
			prefill: ['med_refill', 'ongoing']
			max: 128
			required: false
			source: ['No', 'Yes']
		view:
			control : 'select'
			label: 'Have there been any changes to your medications and/or allergies?'
			offscreen: true
			readonly: true

	provider_visit:
		model:
			prefill: ['med_refill', 'ongoing']
			max: 3
			source: ['No', 'Yes']
			if:
				'No':
					note: 'Ask patient if they need help scheduling a visit'
				'Yes':
					fields: ['provider_visit_date']
		view:
			label: 'Have you been seen by your healthcare provider recently?'
			offscreen: true
			readonly: true

	provider_visit_date:
		model:
			prefill: ['med_refill', 'ongoing']
			type: 'date'
		view:
			label: 'Last healthcare provider visit date:'
			offscreen: true
			readonly: true
	
	supply_issues:
		model:
			prefill: ['med_refill', 'ongoing']
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['supply_issues_comment']
		view:
			control : 'radio'
			label: 'Any problems with pump, supplies or waste disposal?'
			offscreen: true
			readonly: true

	supply_issues_comment:
		model:
			prefill: ['med_refill', 'ongoing']
		view:
			label: 'Pump, Supplies or Waste disposal Comment'
			offscreen: true
			readonly: true

	had_adr:
		model:
			prefill: ['med_refill', 'ongoing']
			access:
				read: ['patient']
			max: 3
			required: false
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_adr_reactions', 'had_adr_rating', 'had_adr_interventions']
		view:
			control : 'radio'
			label: 'Did you experience any adverse drug reactions?'
			readonly: true
			offscreen: true

	had_adr_reactions:
		model:
			prefill: ['med_refill', 'ongoing']
			multi: true
			access:
				read: ['patient']
			source: 'list_reaction'
		view:
			label: 'Reactions'
			readonly: true
			offscreen: true

	had_adr_rating:
		model:
			prefill: ['med_refill', 'ongoing']
			access:
				read: ['patient']
			max: 8
			required: false
			source: ['Mild', 'Moderate', 'Severe', 'Serious']
		view:
			control : 'radio'
			label: 'How severe was the adverse drug reaction?'
			readonly: true
			offscreen: true

	had_adr_interventions:
		model:
			prefill: ['med_refill', 'ongoing']
			access:
				read: ['patient']
			required: false
			multi: true
			source: ['Provided additional teaching/education', 'Dose held', 'Infusion therapy order changed', 'Infusion therapy discontinued', 'Adjunctive treatment administered', 'Unscheduled nursing visit performed', 'Unplanned hospitalization', 'Emergency department use', 'Unscheduled labs drawn', 'Equipment repaired or replaced', 'FDA MedWatch Report submitted', 'Other']
			if:
				'Other':
					fields: ['had_adr_interventions_other']
		view:
			control : 'checkbox'
			note: 'Check all that apply'
			label: 'Select all the interventions performed in response to the ADR'
			readonly: true
			offscreen: true

	had_adr_interventions_other:
		model:
			prefill: ['med_refill', 'ongoing']
			access:
				read: ['patient']
			required: false
		view:
			label: 'ADR Interventions Other'
			readonly: true
			offscreen: true

	new_meds:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['new_meds_details']
		view:
			control : 'radio'
			label: 'Any change in medication profile?'

	new_meds_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'New medications or herbal supplements details'

	allergens_list:
		model:
			subfields:
				_meta:
					label: 'Prefilled/Additional'
					type: 'text'
				allergen_id: # patient_allergen_id
					label: 'Allergen ID'
					type: 'int'
					offscreen: true
				name:
					label: 'Name'
					type: 'text'
					style:
						width: '30%'
				reaction:
					label: 'Reaction'
					type: 'text'
					style:
						width: '30%'
				comment:
					label: "Comment"
					type: 'text'
					style:
						width: '40%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Allergens List'
			offscreen: true
			readonly: true

	new_allergies_check:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields:['new_allergies']
		view:
			control:'radio'
			label: 'Do you have any new allergies?'

	new_allergies:
		view:
			label: 'New allergies'

	experiencing_pain:
		model:
			required: true 
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pain_scale']
		view:
			label: 'Are you experiencing any pain?'
			control: 'radio'

	pain_scale:
		model:
			required: true
			source: ['1' ,'2' ,'3' ,'4' ,'5' ,'6' ,'7' ,'8' ,'9' ,'10']
			if:
				'*':
					fields: ['cust_pain_management']
		view:
			control: 'radio'
			label: 'Pain Scale'

	cust_pain_management:
		model:
			required: true
		view:
			label: 'What are you doing to control the pain?'
			control: 'area'

	new_visits:
		model:
			max: 3
			source: ['No','Yes']
			required: true 
			if:
				'Yes':
					fields:['new_visits_details']
		view:
			control:'radio'
			label: 'Any ER Visit/Hospital/Unplanned Physician visits?'

	new_visits_details:
		model:
			required: true 
		view:
			control:'area'
			label: 'AE Details'

	missed_medications:
		model:
			max: 3
			source: ['No', 'Yes']
			required: true
			if:
				'Yes':
					fields: ['missed_med_list', 'missed_medications_reason_list']
		view:
			control : 'radio'
			label: 'Did you missed any doses of your medication?'


	missed_med_list:
		model:
			subfields:
				missed:
					label: 'Missed?'
					type: 'checkbox'
					style:
						width: '10%'
				description:
					label: 'Drug'
					type: 'text'
					style:
						width: '50%'
				comment:
					label: "Comment"
					type: 'text'
					style:
						width: '40%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Which Medications Missed?'

	missed_medications_list:
		model:
			required: false
		view:
			label: 'Which medication(s)?'
			offscreen: true
			readonly: true

	missed_medications_doses:
		model:
			required: false
		view:
			label: 'How many doses did you miss?'
			offscreen: true
			readonly: true

	missed_medications_reason_list:
		model:
			multi: true
			source: ['Could not afford them', 'Forgot to take them', 'Forgot to pick up a refill', "Missed a doctor’s appointment", 'Other']
			if:
				'Other':
					fields: ['missed_medications_reason']
		view:
			control: 'checkbox'
			label: 'Reason missed'
			offscreen: true
			readonly: true

	missed_medications_reason:
		model:
			required: false
		view:
			label: 'Reason Missed Other'
			offscreen: true
			readonly: true

	response_to_therapy:
		model:
			prefill: ['med_refill']
			required: true 
			source: ['Improving','No Change/Stable','Worsening']
			if:
				'Worsening':
					fields: ['response_to_therapy_rn']
				'Improving':
					fields: ['response_to_therapy_rn']
		view:
			control:'radio'
			label: 'Response to therapy'

	response_to_therapy_rn:
		model:
			required: true
		view:
			control: 'area'
			label: 'Reason'

	new_events:
		model:
			required: true
			multi: true
			source: ['ER Visit', 'Hospitalization', 'Unscheduled Physician Visit', 'Adverse Drug Event', 'IV infusion related issues', 'Missed Dose', 'New Medical Condition', 'No New Events']
			if:
				'Missed Dose':
					fields: ['missed_med_list', 'new_events_details']
				'*':
					fields: ['new_events_details']
		view:
			control: 'checkbox'
			label: 'Any new events?'

	new_events_details:
		model:
			required: true 
		view:
			control: 'area'
			label: 'Details'

	cust_pharmacist_questions:
		model:
			max: 128
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_pharm_quest_dtl']
		view:
			control : 'radio'
			label: 'Do you have any questions for the Pharmacist?'

	cust_happy:
		model:
			max: 128
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['cust_happy_no']
		view:
			control : 'radio'
			label: 'Are you happy with your nursing and pharmacy services?'

	cust_happy_no:
		model:
			required: true
		view:
			control: 'area'
			label: 'What can we do better?'

	cust_pharm_quest_dtl:
		model:
			required: true
		view:
			control: 'area'
			label: 'Details'
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'nurse', 'patient', 'pharm']
		delete:     ['admin']
		read:       ['admin', 'csr', 'cm', 'nurse', 'patient', 'pharm']
		read_all:   ['admin', 'csr', 'cm', 'nurse', 'patient', 'pharm']
		request:    []
		review:     ['admin', 'nurse', 'pharm','patient']
		update:     []
		update_all: ['admin', 'patient']
		write:      ['admin', 'patient']
	bundle: ['patient']
	name: ['patient_id']
	indexes:
		many: [
			['patient_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		encounter:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
	sections:
		'Therapy Outcomes Questionnaire':
					fields: ['response_to_therapy', 'response_to_therapy_rn', 'new_meds', 'new_meds_details',
					'new_allergies_check', 'new_allergies', 'experiencing_pain', 'pain_scale', 'cust_pain_management',
					'new_events', 'new_events_details', 'missed_med_list','missed_medications_reason_list',
					'missed_medications_reason','cust_pharmacist_questions',
					'cust_pharm_quest_dtl', 'cust_happy', 'cust_happy_no']
view:
	comment: 'Patient >  Satisfaction Survey SMS'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Medication Refill'
	open: 'edit'
