fields:
	name:
		model:
			required: false
		view:
			columns: 2
			label: 'Name'

	is_complete:
		model:
			required: false
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Completed'
			columns: 4

	completed_date:
		model:
			type: 'date'
		view:
			label: 'Completed Date'
			columns: 4

	external_id:
		view:
			label: 'External ID'
			readonly: true
			offscreen: true


model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: '{name}'
	sections:
		'Goals':
			hide_header: true
			fields: ['name', 'is_complete',  'completed_date']

view:
	hide_cardmenu: true
	comment: 'Goals'
	grid:
		fields: ['name', 'is_complete',  'completed_date']
		sort: ['-id']
	label: 'Goals'
	open: 'edit'