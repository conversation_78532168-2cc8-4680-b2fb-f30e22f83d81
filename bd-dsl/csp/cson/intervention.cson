fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: false
			source: 'careplan'
			type: 'int'

	mrn:
		model:
			required: true
		view:
			label: 'MRN'
			validate: [
					name: 'ValidateMRN'
			]

	intervention_type:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	reported_date:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	category:
		model:
			required: true
			source: ['Adverse Event', 'Intervention']
			if:
				'Adverse Event':
					fields: ['ae_type', 'ae_date', 'affect_health']
				'Intervention':
					fields: ['mrn','occurrence_date','associated_order','intervention_type_id','intervention_accepted','intervention_by']
		view:
			control: 'radio'
			label: 'Category'

	intervention_type_id:
		model:
			required: true
			source: 'list_intervention'
			sourceid: 'code'
			if:
				'Other':
					sections: ['Other Details']
					fields: ['other_outcomes_details']
				'Shipping Related':
					sections: ['Shipping Related']
					fields: ['shipping_issue_ae_code','resolved_shipping', 'er_visit_note']
		view:
			control: 'checkbox'
			label: 'Intervention Type'

	other_outcomes_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Comments'

	nursing_other_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Comments'


	ae_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Adverse Event date'
			template: '{{now}}'

	# complaint_type:
	# 	model:
	# 		required: true
	# 		source: ['Shipping Related', 'Pharmacy Related', 'Nursing Related']
	# 		if:
	# 			'Shipping Related':
	# 				sections: ['Shipping Related']
	# 				fields: ['shipping_issue_complaint','resolved_shipping']
	# 			'Pharmacy Related':
	# 				sections: ['Pharmacy Related']
	# 				fields: ['pharmacy_issue_complaint','resolved_pharmacy']
	# 			'Nursing Related':
	# 				sections: ['Nursing Related']
	# 				fields: ['nursing_issue_complaint', 'resolved_nursing']

	# 	view:
	# 		control: 'radio'
	# 		label: 'Complaint Related To:'

	ae_type:
		model:
			required: true
			source: ['Pharmacy Related', 'Nursing Related']
			if:
				'Pharmacy Related':
					sections: ['Pharmacy Related']
					fields: ['pharmacy_issue_ae_code','resolved_pharmacy', 'er_visit_note']
				'Nursing Related':
					sections: ['Nursing Related']
					fields: ['nursing_issue_ae_code', 'resolved_nursing', 'er_visit_note']
		view:
			control: 'radio'
			label: 'AE Related To:'

	associated_order:
		model:
			required: true
			source: 'careplan_order'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			label: 'Order'

	nursing_issue_complaint:
		model:
			required: true
			source: 'list_nursing_issue'
			sourcefilter:
				ae_only:
					'static': '!Yes'
		view:
			control: 'checkbox'
			label: 'Nursing Complaint Type'

	pharmacy_issue_complaint:
		model:
			required: true
			source: 'list_pharmacy_issue'
			sourcefilter:
				ae_only:
					'static': '!Yes'
		view:
			control: 'checkbox'
			label: 'Pharmacy Complaint Type'

	shipping_issue_complaint:
		model:
			required: true
			source: 'list_shipping_issue'
			sourcefilter:
				ae_only:
					'static': '!Yes'
		view:
			control: 'checkbox'
			label: 'Shipping Complaint Type'

	nursing_issue_ae_code:
		model:
			required: true
			source: 'list_nursing_issue'
			sourceid: 'code'
			sourcefilter:
				complaint_only:
					'static': '!Yes'
			if:
				'Extravasation':
					sections: ['Extravasation', 'Vascular Access Device', 'Administration', 'Sign and Symptoms', 'Locations', 'Actions']
				'Other':
					sections:['Other Details']
					fields: ['nursing_other_details']
		view:
			control: 'checkbox'
			label: 'Nursing AE Type'
			validate:[
				{
					name:'setExtraFields' #using this to fix related the rendering of fields on "ADVERSE EVENT/COMPLAINT" form for "Extravasation" on "Nursing AE Type"
				}
				
			]

	extra_fields_show: #Using this to fix related the rendering of fields on "ADVERSE EVENT/COMPLAINT" form for "Extravasation" on "Nursing AE Type"
		model:
			default: 'No'
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['Extravasation', 'Vascular Access Device', 'Administration', 'Sign and Symptoms', 'Locations', 'Actions']
		view:
			control: 'radio'
			label: 'Show Extravasation Fields'
			offscreen: true
			readonly: true

	er_visit_note:
		model:
			multi: true
			source: [
				"<span style='color:black'>Healthcare professionals should report all serious adverse events suspected to be associated with the use of any medicine and device to FDA’s <a href='https://www.accessdata.fda.gov/scripts/medwatch/index.cfm' target='_blank'>MedWatch Reporting System</a> or by calling 800-FDA-1088..</span>"
			]
		view:
			control: "checkbox"
			label: "Alert"
			class: "list"
			readonly: true

	pharmacy_issue_ae_code:
		model:
			required: true
			source: 'list_pharmacy_issue'
			sourceid: 'code'
			if:
				'Experienced Side-Effect (Drug Related)':
					fields: ['reaction_id', 'medwatch_completed']
				'Other':
					sections:['Other Details']
					fields: ['pharmacy_other_details']
			sourcefilter:
				complaint_only:
					'static': '!Yes'
		view:
			control: 'checkbox'
			label: 'Pharmacy AE Type'

	pharmacy_other_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Comments'

	reaction_id:
		model:
			required: true
			multi: true
			source: 'list_reaction'
		view:
			label: 'Reactions Experienced'

	shipping_issue_ae_code:
		model:
			required: true
			source: 'list_shipping_issue'
			sourceid: 'code'
			sourcefilter:
				complaint_only:
					'static': '!Yes'
		view:
			control: 'checkbox'
			label: 'Intervention Type'

	medwatch_completed:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Was a MedWatch completed?'

	description:
		model:
			required: true
		view:
			control: 'area'
			label: 'Description of issue'

	affect_health:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Did the issue affect the patient’s health/disrupt or delay prescribed therapy?'

	cold_chain_management:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is this entry related to Cold Chain Management?'

	resolved_pharmacy:
		model:
			source: ["No", "Yes"]
			default: "No"
			if:
				'Yes':
					fields: ['resolution_date', 'outcome_pharmacy', 'pharmacist_signature']
		view:
			control: 'radio'
			label: 'Issue Resolved?'

	resolution_date:
		model:
			required:true
			type: 'date'
		view:
			label: 'Resolution date'

	resolved_shipping:
		model:
			source: ["No", "Yes"]
			default: "No"
			if:
				'Yes':
					fields: ['resolution_date', 'outcome_shipping']
		view:
			control: 'radio'
			label: 'Issue Resolved?'

	resolved_nursing:
		model:
			source: ["No", "Yes"]
			default: "No"
			if:
				'Yes':
					fields: ['resolution_date', 'outcome_nursing']
		view:
			control: 'radio'
			label: 'Issue Resolved?'

	resolved_other:
		model:
			source: ["No", "Yes"]
			default: "No"
			if:
				'Yes':
					fields: ['resolution_date']
		view:
			control: 'radio'
			label: 'Issue Resolved?'

	pharmacist_signature:
		model:
			required: false
			type: 'json'
			access:
				write: ['-csr', '-liaison', '-nurse']
		view:

			control: 'esign'
			label: 'E-Signed by Pharmacist Charge'

	outcome_pharmacy:
		model:
			required: true
			multi: true
			source: 'list_outcome'
			sourcefilter:
				type:
					'static': 'Pharmacy'
		view:
			label: 'Outcome'
			control: 'checkbox'

	outcome_shipping:
		model:
			required: true
			multi: true
			source: 'list_outcome'
			sourcefilter:
				type:
					'static': 'Shipping'
		view:
			label: 'Outcome'
			control: 'checkbox'

	outcome_nursing:
		model:
			required: true
			multi: true
			source: 'list_outcome'
			sourcefilter:
				type:
					'static': 'Nursing'
		view:
			label: 'Outcome'
			control: 'checkbox'

	outcome_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Outcome Details'

	reason:
		model:
			required: false 
			if:
				'*':
					fields: ['reviewed_by']
		view:
			offscreen: true
			readonly: true

	reason_other:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	hospitalized_date:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	hospitalized_reason:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	hospitalized_reason_event:
		model:
			required:false
		view:
			offscreen: true
			readonly: true

	hospitalized_reason_event_other:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	hospitalized_outcome:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	infectious_disease:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	infectious_disease_date:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	infectious_disease_other:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	adr_reported:
		model:
			required: false
			multi: true
		view:
			offscreen: true
			readonly: true

	adr_reported_other:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	adr_severity:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	adr_hospitalization:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	adr_admin_date:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	adr_drug_stopped:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	adr_abated_after_stopping:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	adr_reported_manufacture:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	cath_complication:
		model:
			required: false
			multi: true
		view:
			offscreen: true
			readonly: true

	cath_complication_other:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	associated_dispense:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	other_details:
		model:
			required: true
		view:
			control:'area'
			label: 'Other Notes'


#  Extravasation Section Fields
#Extravasation
	extra_date:
		model:
			required: false
			type: 'date'
		view:
			label: 'Extravasation Date'

	extra_time:
		model:
			type: 'time'
		view:
			label: 'Extravasation Time'

	extra_drug_solution:
		model:
			required: false
		view:
			label: 'Drug/Solution'

	extra_volume:
		model:
			required: false
			type: 'decimal'
			min: 0
		view:
			label: 'Approximate Volume (ml)'

	extra_type:
		model:
			multi: false
			source: ['During Administration', 'Immediately after Administration', 'After Administration']
			if:
				'After Administration':
					fields: [ 'day_time_freq']
		view:
			control: 'checkbox'
			label: 'Extravasation Type'

	extra_day_time:
		model:
			multi: false
			source: ['Days', 'Hours']
		view:
			control: 'checkbox'
			label: 'Days/Hours'

	day_time_freq:
		model:
			required: false
			type: 'int'
			min: 0
		view:
			label: 'No of.'

# vascular access device
	vac_pivc:
		model:
			multi: false
			source: ['PIVC']
		view:
			control: 'checkbox'
			label: 'Vascular access device PIVC'


	vac_insertion_date:
		model:
			required: false
			type: 'date'
		view:
			label: 'Insertion Date'

	vac_insertion_time:
		model:
			required: false
			type: 'time'
		view:
			label: 'Insertion Time'

	vac_device_type:
		model:
			multi: false
			source: ['PICC', 'PORT', 'TIVAD', 'CVAD', 'Other']
			if:
				'TIVAD':
					fields: ['tivad_mm', 'tivad_gauge']
		view:
			control: 'checkbox'
			label: 'Vascular access device type'

	tivad_mm:
		model:
			required: false
			type: 'decimal'
			min: 0
		view:
			label: 'TIVAD non-coring needle(mm)'

	tivad_gauge:
		model:
			required: false
			type: 'decimal'
			min: 0
		view:
			label: 'TIVAD non-coring needle(gauge)'


# Administration
	extra_administration:
		model:
			multi: false
			source: ['Free flow/gravity', 'Infusion pump', 'Syringe']
		view:
			control: 'checkbox'
			label: 'Administration'

# Sign & Symptoms
	sympthoms:
		model:
			multi: true
			source: ['Pain', 'Erythema', 'Swelling', 'No blood return', 'Burning',
			'Blanching', 'Exudate', 'Change in free flow rate', 'Tingling',
			'Blister/s', 'Induration', 'Infusion pump alarm', 'Stinging',
			'Bleb formation', 'Other discolouration']
			if:
				'Other discolouration':
					fields: ['other_discolouration']
		view:
			control: 'checkbox'
			label: 'Sign and Symptoms'

	other_discolouration:
		model:
			required: false
		view:
			label: 'Other discolouration'

# Locations
	extra_location_width:
		model:
			type: 'decimal'
			min: 0
		view:
			label: 'Size of Area Width'

	extra_location_height:
		model:
			type: 'decimal'
			min: 0
		view:
			label: 'Size of Area Height'

	extra_location_front:
		model:
			multi: false
			source: ['Right', 'Left']
		view:
			control: 'checkbox'
			label: 'Location from Front of the body'

	extra_location_back:
		model:
			multi: false
			source: ['Left', 'Right']
		view:
			control: 'checkbox'
			label: 'Location from Back of the body'

# Actions
	immediate_actions:
		model:
			multi: true
			source: ['Stop injection/infusion immediately', 'LEAVE VAD in situ', 'ASPIRATE residual drug from VAD with syringe',
			'PLAN next actions']
		view:
			control: 'checkbox'
			label: 'Immediate Actions'


	secondary_actions:
		model:
			multi: true
			source: ['Treating team notified', 'Photo taken', 'Affected area outlined with marker',
			'Warm compress', 'Cold compress', 'Antidote', 'Analgesia', 
			]
			if:
				'Warm compress':
					fields: ['application_time']
				'Cold compress':
					fields: ['frequency']
				'Antidote':
					fields: ['antidote_note','antidote_time']
				'Analgesia':
					fields: ['analgesia_note','analgesia_time']
		view:
			control: 'checkbox'
			label: 'Secondary actions'

	antidote_note:
		model:
			type: 'text'
		view:
			label: 'Antidote Note'

	analgesia_note:
		model:
			type: 'text'
		view:
			label: 'Analgesia Note'

	application_time:
		model:
			type: 'time'
		view:
			label: 'Application Time'

	frequency:
		model:
			type: 'int'
			min: 0
		view:
			label: 'Frequency'

	antidote_time:
		model:
			type: 'time'
		view:
			label: 'Antidote Time'

	analgesia_time:
		model:
			type: 'time'
		view:
			label: 'Analgesia Time'


	occurrence_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Occurrence Date'
			template: '{{now}}'

	intervention_accepted:
		model:
			required: true
			source: ['Yes','No']
			if:
				'Yes':
					fields:['check_outcomes']
				'No':
					fields: ['outcomes_detail']
		view:
			control: 'radio'
			label: 'Intervention accepted'

	check_outcomes:
		model:
			required: true
			source: 'intervention_outcomes'
		view:
			control: 'checkbox'
			label: 'Outcome'
			validate: [
					name: 'ValidateOutcomes'
			]

	show_other_outcome_section:
		model:
			required: true
			source: ['Yes','No']
			if:
				'Yes':
					sections: ['Other Details']
					fields: ['outcomes_other']
		view:
			control: 'radio'
			label: 'show other outcome section'
			offscreen: true
			readonly: true

	outcomes_other:
		model:
			required: true
		view:
			label: 'Other Outcome'

	outcomes_detail:
		model:
			required: true
		view:
			label: 'Details'

	intervention_by:
		model:
			required: true
			source: 'user'
			sourcefilter:
				role:
					'static': 'pharm'
		view:
			label: 'Intervention by'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true


model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin','liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin','csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	sections:
		'Adverse Event/Intervention Details':
			fields: ['category','occurrence_date','associated_order','intervention_type_id','intervention_accepted','check_outcomes','show_other_outcome_section','outcomes_detail','intervention_by', 'ae_date', 'ae_type']
		'Shipping Related':
			fields: ['shipping_issue_ae_code', 'er_visit_note', 'description', 'cold_chain_management']
		'Pharmacy Related':
			fields: ['pharmacy_issue_ae_code', 'er_visit_note', 'reaction_id', 'medwatch_completed', 'description']
		'Nursing Related':
			fields: ['nursing_issue_ae_code', 'er_visit_note', 'description']
		'Other Details':
			fields:['nursing_other_details','pharmacy_other_details','other_outcomes_details','outcomes_other']
		'Extravasation': #Fields of this sections marked as non-required in InterventionHandlerBaseView
			fields: ['extra_date', 'extra_time', 'extra_drug_solution', 'extra_volume', 'extra_type', 'extra_day_time', 'day_time_freq', 'extra_fields_show']
		'Vascular Access Device':  #Fields of this sections marked as non-required in InterventionHandlerBaseView
			fields: ['vac_pivc', 'vac_insertion_date', 'vac_insertion_time', 'vac_device_type', 'tivad_mm', 'tivad_gauge']
		'Administration':  #Fields of this sections marked as non-required in InterventionHandlerBaseView
			fields: ['extra_administration']
		'Sign and Symptoms':  #Fields of this sections marked as non-required in InterventionHandlerBaseView
			fields: ['sympthoms', 'other_discolouration']
		'Locations':  #Fields of this sections marked as non-required in InterventionHandlerBaseView
			fields: ['extra_location_width', 'extra_location_height', 'extra_location_front', 'extra_location_back']
		'Actions':  #Fields of this sections marked as non-required in InterventionHandlerBaseView
			fields: ['immediate_actions', 'secondary_actions', 'application_time', 'frequency', 'antidote_note' ,'antidote_time', 'analgesia_note', 'analgesia_time']

	transform_post: [
			name: "AutoNote"
			arguments:
				subject: "Adverse Event/Complaint"
	]

view:
	comment: 'Patient > Careplan > Adverse Event/Intervention'
	find:
		basic: ['category', 'ae_type']
	grid:
		fields: ['created_on', 'created_by', 'category', 'ae_type','affect_health']
		sort: ['-id']
	label: 'Adverse Event/Intervention'
	open: 'read'
