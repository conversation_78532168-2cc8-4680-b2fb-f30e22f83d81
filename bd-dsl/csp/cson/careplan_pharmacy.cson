fields:

	updated_on:
		model:
			type: 'datetime'
		view:
			label: 'Last Updated On'
			readonly: true
			columns: 3

	# Links
	reviewed_by:
		view:
			label: 'Last Reviewed By'
			readonly: true
			columns: 3

	reviewed_on:
		view:
			label: 'Last Reviewed On'
			readonly: true
			columns: 3

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
	

	therapy_1:
		model:
			required: false
		view:
			offscreen: true
			readonly: true
			label: 'Primary Therapy'
			validate: [
				name: 'CheckTherapy'
			]


	template_ids:
		model:
			source: 'careplan_template'
			multi: true
		view:
			label: 'Care Plan Template'
			validate: [
				name: 'LoadCareplanTemplates'
			]
			columns: 3

	active:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['soc_date']
			default: 'Yes'
		view:
			control: 'radio'
			label: 'Active'
			columns: 3

	soc_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Start of Care Date'
			template: '{{now}}'
			columns: 3.1

	# Care Plan Changes
	problems:
		model:
			subfields:
				date:
					label: 'Date'
					type: 'timestamp'
					readonly: true
					style:
						width: '9%'
				problem:
					label: 'Problem'
					type: 'text'
					dynamic: 'cust_problem'
					style:
						width: '75%'
				compeleted:
					type: 'checkbox'
					label: 'Complete'
					style:
						width: '6%'
				compeleted_date:
					label: 'Comp Date'
					type: 'text'
					readonly: true
					style:
						width: '10%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Problems'
			transform: [
					name: 'MarkCompDate'
					field: 'problems'
			]

	goals:
		model:
			subfields:
				date:
					label: 'Date'
					type: 'timestamp'
					readonly: true
					style:
						width: '9%'
				goal:
					label: 'Goal'
					type: 'text'
					dynamic: 'cust_goal'
					style:
						width: '75%'
				compeleted:
					source: ['Yes','No']
					label: 'Progressing Toward Goal'
					style:
						width: '16%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Goals'
			transform: [
					name: 'MarkCompDate'
					field: 'goals'
			]


	interventions:
		model:
			subfields:
				date:
					label: 'Date'
					type: 'timestamp'
					readonly: true
					style:
						width: '9%'
				intervention:
					label: 'Intervention'
					type: 'text'
					dynamic: 'cust_intervention'
					style:
						width: '75%'
				compeleted:
					type: 'checkbox'
					label: 'Complete'
					style:
						width: '6%'
				compeleted_date:
					label: 'Comp Date'
					type: 'text'
					readonly: true
					style:
						width: '10%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Interventions'
			transform: [
					name: 'MarkCompDate'
					field: 'interventions'
			]

	note:
		view:
			control: 'area'
			label: 'Care Plan Notes'


	infusion_meds:
		model:
			required: false 
			subfields:
				name:
					label: 'Name'
					type: 'text'
				dose:
					label: 'Dose'
					type: 'text'
				route:
					label: 'Route'
					type: 'text'
				freq:
					label: 'Frequency'
					type: 'text'
			type: 'json'
		view:
			control: 'grid'
			label: 'Home Infusion Medication'
			offscreen: true
			readonly: true 

	therapy_duration:
		model:
			type: 'int'
			required: true 
		view:
			label:"Therapy Duration #"
			columns: 3

	therapy_duration_type:
		model:
			required: true
			source: ['Days','Months','Years']
			default: 'Months'
		view:
			label:"Therapy Duration Type"
			columns: 3

	discharge_criteria:
		model:
			source:['Completion of prescribed therapy', 'Discontinuation of therapy by physician order']
		view:
			label: 'Discharge Criteria'
			columns: 3

	discharge_criteria_status:
		model:
			default: 'Criteria Not met - Patient active'
			source:['Criteria Not met - Patient active', 'Criteria Not met - Patient Discharged d/t alternate reason','Criteria Met - Patient to be discharged']
		view:
			label: 'Discharge Criteria Status'
			columns: 3

	discharge_criteria_txt:
		view:
			control: 'area'
			label: 'Comments'
			columns: 3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin', 'pharm', 'cm', 'cma', 'nurse']
		write:      ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
	bundle: ['patient', 'intake']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
		careplan_pharmacy:
			link:
				'careplan_id': 'careplan_id'
			max: 'created_on'
	sections:
		'Status':
			fields: ['updated_on', 'active', 'soc_date']
		'Therapy Details':
			prefill: 'careplan_pharmacy'
			fields: [ 'template_ids']
		'Careplan Details':
			prefill: 'careplan_pharmacy'
			fields: ['problems', 'goals', 'interventions']
		'Notes':
			prefill: 'careplan_pharmacy'
			fields: ['note']

	transform: [
		{
			name: "EnsureUnique"
			arguments:
				form: "careplan_pharmacy"
				unique_field: "active"
				nonunique_value: "No"
				qualifier_field: "patient_id"
		}
		]
view:
	find:
		basic: ['active']
	comment: 'Patient > Careplan > Multidisciplinary Care Plan'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by', 'active','note']
		sort: ['created_on']
	label: 'Multidisciplinary Care Plan'
	open: 'read'
