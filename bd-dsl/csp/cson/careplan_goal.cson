fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	# Goal Details
	type:
		model:
			multi: false
			required: true
			source: ['Nursing', 'Pharmacy']
			if:
				'Nursing':
					fields: ['nurse_goal']
				'Pharmacy':
					fields: ['pharmacy_goal']
		view:
			control: 'radio'
			label: 'Care Plan Type'

	date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Date'
			template: '{{now}}'

	nurse_goal:
		model:
			source: ['Administration of ordered meds without complications',
			'VAD to remain patent during course of infusion therapy',
			'Improvement in disease process symptoms r/t drug administered',
			'Patient/CG to verbalize understanding of Effects/ side effects of medications administered',
			'Central venous catheter will remain free of infection',
			'Discharge after completing intravenous therapy',
			'Parent/caregiver will correctly administer drug',
			'Parent/caregiver will correctly dispose of hazardous materials',
			'Parent/caregiver will demonstrate knowledge of home infusion therapy',
			'Parent/caregiver will understand diagnosis and treatment expectations',
			'Patient desires to experience decreased incidences of infection',
			'Patient desires to experience no adverse medication reactions throughout therapy',
			'Patient desires to have improved ambulation as evidenced by less or no episodes of falling or loss of balance',
			'Patient desires to have improved quality of life as demonstrated by improvement in symptoms and feeling of well-being',
			'Patient desires to have improvement in overall ambulation',
			'Patient desires to have less days missed from school or work due to incidences of infection/illness', 
			'Patient desires to have less frequent episodes of numbness and tingling',
			'Patient desires to learn how to complete self-infusion techniques',
			'Patient desires to learn how to log infusions correctly',
			'Patient desires to learn how to mix and prepare medications independently',
			'Patient desires to learn self infusion techniques and become independent with infusions',
			'Patient desires to minimize pain experienced',
			'Patient desires to minimize side effects experienced throughout therapy regimen',
			'Patient desires to remain infection free throughout therapy',
			'Patient desires to remain pain free throughout therapy',
			'Patient will communicate effectively with pharmacy',
			'Patient will correctly dispose of hazardous materials',
			'Patient will demonstrate correct self infusion procedure',
			'Patient will demonstrate increased strength and function',
			'Patient will have decreased adverse reactions related to effective multidisciplinary monitoring',
			'Patient will have improved behavior related to diversional activities provided',
			'Patient will have increased adherence to therapy through effective communication with RN and pharmacist',
			'Patient will have increased adherence to therapy through ongoing multidisciplinary communications',
			'Patient will have no complications related to prescribed home infusion therapy',
			'Patient will maintain adequate fluid volume and electrolyte levels',
			'Patient will receive medications safely and complications will be avoided',
			'Patient will remain free from injury',
			'Patient will successfully self administer subcutaneous infusions',
			'Patient will verbalize and demonstrate ways to prevent infection',
			'Patient will verbalize complications to report to RN and physician',
			'Patient will verbalize improved mobility',
			'Patient will verbalize understanding of diagnosis and treatment expectations',
			"Patient's pain will be relieved/controlled",
			'Patient/caregiver will maintain aseptic technique for subcutaneous ordered infusion therapy',
			'Patients blood glucose level will remain stable',
			'Patients caregiver desires to learn infusion techniques',
			'Patients vascular access site will remain free from infection',
			'Peripheral venous access site will remain free of infection',
			'RN will obtain peripheral venous access',
			'Safe medication administration with no adverse affects',
			'Subcutaneous access sites will remain free of infection']

	pharmacy_goal:
		model:
			prefill: ['careplan']
			multi: false
			source: ['Minimize hepatatoxicity by monitoring patient labs closely',
			'Minimize nephrotoxicity by monitoring patient labs closely',
			'Patient desires to experience decreased incidences of infection',
			'Patient desires to experience no adverse medication reactions throughout therapy',
			'Patient desires to have improved quality of life as demonstrated by improvement in symptoms and feeling of well-being',
			'Patient desires to have improvement in overall ambulation',
			'Patient desires to have less days missed from school or work due to incidences of infection/illness',
			'Patient desires to have less frequent episodes of numbness and tingling',
			'Patient desires to learn how to complete self-infusion techniques',
			'Patient desires to learn how to log infusions correctly',
			'Patient desires to learn how to mix and prepare medications independently',
			'Patient desires to learn self infusion techniques and become independent with infusions',
			'Patient desires to minimize pain experienced',
			'Patient desires to minimize side effects experienced throughout therapy regimen', 
			'Patient desires to remain infection free throughout therapy',
			'Patient desires to remain pain free throughout therapy',
			'Patient will demonstrate adequate pain control',
			'Patient will maintain glucose levels within normal limits',
			'Patient will maintain lab levels appropriate to prescribed therapy',
			'Patient will maintain serum electrolyte levels within normal limits',
			'Patient will remain free from infection', 
			'Patient will report any adverse drug reaction',
			'Patient/caregiver will understand proper storage and handling of biohazard material',
			'Patient/caregiver will verbalize understanding of prescribed therapy',
			'Patients caregiver desires to learn infusion techniques', 
			'Prevention of adverse drug reactions through end of prescription',
			'Prevention of drug interaction through the end of prescription',
			'Resolution of primary infection by end of prescribed therapy']
		view:
			control: 'select'
			label: 'Pharmacy Care Plan Goals'

	# Updates
	date_updated:
		model:
			type: 'date'
		view:
			label: 'Date Updated'

	date_completed:
		model:
			type: 'date'
		view:
			label: 'Date Completed'

	comment:
		view:
			control: 'area'
			label: "Goal Comment"

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm', 'nurse']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient']
	name: ['patient_id', 'created_on']
	indexes:
		many: [
			['patient_id']
		]
	sections:
		'Goal Details':
			fields: ['date', 'type', 'nurse_goal', 'pharmacy_goal']
		'Status Updates':
			fields: ['date_updated', 'date_completed']
		'Goal Comment':
			fields: ['comment']

view:
	comment: 'Patient > Care Plan > Goal'
	grid:
		fields: ['date', 'nurse_goal', 'pharmacy_goal', 'date_updated', 'date_completed']
	label: 'Patient Care Plan Goal'
