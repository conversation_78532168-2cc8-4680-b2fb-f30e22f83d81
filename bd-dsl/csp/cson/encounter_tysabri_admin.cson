fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	# Admin Step
	time_taken:
		model:
			required: true
			type: 'time'
		view:
			label: 'Time Taken'
			template: '{{now}}'

	comment:
		model:
			max: 4096
		view:
			control: 'area'
			label: "Administration Step Comment"

	# Pre-infusion Flush
	pre_flush_given:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pre_flush_type', 'pre_flush_diluation', 'pre_flush_comment']
		view:
			control : 'radio'
			label: 'Was a pre-infusion flush given?'

	pre_flush_type:
		model:
			max: 32
			multi: true
			default: ['Normal Saline (NS)']
			source:
				['Heparin', 'Sterile Water (SW)','Normal Saline (NS)', 'Sterile Saline', 'D5W','D5NS', 'Other']
			if:
				'Other':
					fields: ['pre_flush_type_other']
		view:
			control : 'checkbox'
			note: 'Select all that apply'
			label: 'Flush Type'

	pre_flush_type_other:
		view:
			label: 'Pre-infusion flush other'

	pre_flush_diluation:
		model:
			type: 'decimal'
			min: 0
		view:
			label: 'Pre-infusion dilution (mls)'

	pre_flush_comment:
		view:
			label: 'Pre-infusion comment'

	# Post infusion flush
	post_flush_given:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['post_flush_type', 'post_flush_diluation', 'post_flush_comment']
		view:
			control : 'radio'
			label: 'Was a post-infusion flush given?'

	post_flush_type:
		model:
			max: 32
			multi: true
			source:['Heparin','Dextrose','Sterile Water (SW)','Normal Saline (NS)','D5W','D5NS','Other']
			if:
				'Other':
					fields: ['post_flush_type_other']
		view:
			control : 'checkbox'
			note: 'Select all that apply'
			label: 'Post-infusion Type'

	post_flush_type_other:
		view:
			label: 'Post-infusion flush other'

	post_flush_diluation:
		model:
			type: 'decimal'
			min: 0
		view:
			label: 'Post-infusion dilution (mls)'

	post_flush_comment:
		view:
			label: 'Post-infusion comment'

	# Hydration
	hydration:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hydration_vol', 'hydration_start', 'hydration_end', 'hydration_comment']
		view:
			control : 'radio'
			label: 'Was hydration given?'

	hydration_vol:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Hydration Volume Infused (mls)'

	hydration_comment:
		view:
			label: 'Hydration Comment'

	hydration_start:
		model:
			required: true
			type: 'time'
		view:
			label: 'Hydration Time Start'

	hydration_end:
		model:
			required: true
			type: 'time'
		view:
			label: 'Hydration Time End'

	dose:
		model:
			type: 'decimal'
			min: 0
			max: 9999
		view:
			label: 'Dose Given (mg)'

	dose_time:
		model:
			type: 'int'
		view:
			label: 'Over Time Period (minutes)'

	drug_details:
		model:
			subfields:
				lot:
					label: 'Drug Lot#'
				expiration:
					label: 'Expiration Date'
					type: 'date'
			type: 'json'
		view:
			control: 'grid'
			label: 'Drug/Vial Details'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_tysabri_admin:
			link:
				careplan_id: 'careplan_id'
			max: 'id'
		encounter_tysabri:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: '{time_taken} : {dose} over {dose_time}mins'
	sections:
		'Tysabri Admin Step':
			fields: ['time_taken']
		'Tysabri Pre-Infusion Flush':
			fields: ['pre_flush_given', 'pre_flush_type', 'pre_flush_type_other', 'pre_flush_diluation', 'pre_flush_comment']
			prefill: 'encounter_tysabri_admin'
		'Tysabri Hydration':
			fields: ['hydration', 'hydration_vol', 'hydration_start', 'hydration_end', 'hydration_comment']
			prefill: 'encounter_tysabri_admin'
		'Tysabri Administration':
			note: 'Check the drug label with the order before administering'
			fields: ['dose', 'dose_time','drug_details']
		'Tysabri Post-Infusion Flush':
			fields: ['post_flush_given', 'post_flush_type', 'post_flush_type_other', 'post_flush_diluation', 'post_flush_comment']
			prefill: 'encounter_tysabri_admin'
		'Tysabri Admin Step Comment':
			fields: ['comment']

view:
	comment: 'Patient > Careplan > Encounter > Tysabri > Admin'
	grid:
		fields: ['time_taken', 'dose',  'dose_time']
	label: 'Tysabri Admin Step'
