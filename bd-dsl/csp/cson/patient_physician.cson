fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	external_id:
		model:
			type: 'text'

	phys:
		model:
			source: 'physician'
			type: 'int'
		view:
			readonly: true
			label: 'Physician'

	rank:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			readonly: true
			label: 'Rank'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
	
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    ['csr']
		update:     []
		update_all: ['admin']
		write:      ['admin', 'csr','liaison', 'pharm']

	indexes:
		unique: [
			['external_id']
		]
	name: '{phys_auto_name} - {rank}'
	sections_group: [
		'Physician':
			fields: ['phys', 'rank']
	]
view:
	comment: 'Patient -> Patient Physician'
	find:
		basic: ['phys', 'rank']
	grid:
		fields: ['phys', 'rank']
		sort: ['-rank']
	label: 'Physician'
