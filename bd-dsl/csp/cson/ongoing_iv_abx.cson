fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	drug_class:
		model:
			prefill: ['assessment_antibiotic', 'ongoing_antibiotic']
			multi: true
			required: true
			source: ['Aminoglycoside', 'Daptomycin', 'Vancomycin', 'Other']
			if:
				'Aminoglycoside':
					fields:['hearing_episodes', 'vertigo_episodes', 'aminoglycoside_drug_brand', 'aminoglycoside_trough_value', 'aminoglycoside_trough_date', 'aminoglycoside_peak_value', 'aminoglycoside_peak_date']
				'Daptomycin':
					fields:['joint_episodes', 'has_cpk_lab']
				'Vancomycin':
					fields: ['vancomycin_trough_value', 'vancomycin_trough_date', 'vancomycin_trough_true']
				'*':
					fields:['vertigo_episodes']
		view:
			control : 'checkbox'
			note: 'Select any that apply'
			label: 'Drug Class'

	# follow-up
	injection_site_issues:
		model:
			required: false
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['injection_site_issues_details', 'catheter_issues', 'catheter_flushing']
		view:
			control : 'radio'
			label: 'Have you experienced any medication injection site reaction, pain, or inflammation?'
			columns:3

	injection_site_issues_details:
		view:
			control : 'area'
			label: 'Injection Site Issues'
			columns:3.1

	catheter_issues:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields:['catheter_issues_comments']
		view:
			control : 'radio'
			note: '(e.g. catheter site itching, redness, irritation,  swelling, or pain)'
			label: 'Have you had any catheter related complications?'
			columns:3

	catheter_issues_comments:
		model:
			required: true
		view:
			label: 'Catheter Complications'
			columns:3.1


	catheter_flushing:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['catheter_flushing_details']
		view:
			control : 'radio'
			label: 'Is your catheter flushing normally and are you able to infuse your medication normally?'
			columns:3

	catheter_flushing_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Catheter Flushing Issues'
			columns:3.1

	complications_episodes:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['complications_episodes_cnt', 'complications_comment']
		view:
			control : 'radio'
			label: 'Have you had any complications or side effects from your medications?'
			columns:3

	complications_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of complications per week'
			columns:3

	complications_comment:
		model:
			required: true
		view:
			control: 'area'
			label: 'Complications Details'
			columns:3

	itching_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes per week'
			columns:3

	itching_episodes_comment:
		model:
			required: true
		view:
			control: 'area'
			label: 'Locations of itching, rash, or hives'
			columns:3

	breathing_issues:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['breathing_issues_comment']
		view:
			control : 'radio'
			label: 'Have you had any tightness in the chest or throat, trouble breathing?'
			columns:3

	breathing_issues_comment:
		model:
			required: true
		view:
			control: 'area'
			label: 'Describe Symptoms'
			columns:3.1

	fever_episodes:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['fever_episodes_cnt', 'fever_temp']
		view:
			control : 'radio'
			label: 'Have you had any episodes of fever?'
			columns:3

	fever_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of fever episodes per week'
			columns:3

	fever_temp:
		model:
			min: 1
			type: 'int'
			rounding: 0.1
			required: true
		view:
			label: 'Fever Temperature'
			columns:3

	has_infection:
		model:
			required: false
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['has_infection_details']
		view:
			control : 'radio'
			label: 'Have you been treated for any infections since your last appointment?'
			columns:3

	has_infection_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'
			columns:3.1

	fatigue_episodes:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['fatigue_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any episodes of fatigue or weakness?'
			columns:3

	fatigue_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes per week'
			columns:3.1

	nausea_episodes:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['nausea_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any nausea/vomiting, lightheadedness, or headache during or after antibiotic administration?'
			columns:3

	nausea_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes per week'
			columns:3.1

	diarrhea_episodes:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['diarrhea_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any loose stools or diarrhea since starting antibiotic?'
			columns:3

	diarrhea_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes per week'
			columns:3.1


	taking_probiotic:
		model:
			prefill: ['ongoing_antibiotic']
			required: false
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you currently taking a probiotic or eating yogurt on a regular basis?'
			columns:3


	urine_change_frequency:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Have you had any changes in the frequency or volume of urination per day?'
			columns:3

	urine_frequency:
		model:
			required: false
			min: 1
			max: 99
		view:
			label: 'Urination Frequency (per day)'
			columns:3

	urine_change:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['urine_change_color', 'urine_change_cnt']
		view:
			control : 'radio'
			label: 'Have you had any changes in the color of your urine?'
			columns:3

	urine_change_color:
		model:
			required: false
			source: ['Darker', 'Lighter']
		view:
			control : 'radio'
			label: 'Urine Color Change'
			columns:3

	urine_change_cnt:
		model:
			min: 1
			type: 'int'
			required: false
		view:
			label: 'Number of episodes of urine color change per week'
			columns:3

	oz_per_day:
		model:
			required: false
			min: 1
			max: 99
			type: 'int'
		view:
			label: 'How many ounces of fluid are you drinking per day?'
			columns:3

	edema_lower_extremities:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Have you had any new or worsening edema of lower extremities?'
			columns:3

	edema_pit_score:
		model:
			required: false
			max: 3
			source: ['+1', '+2', '+3', 'NA']
		view:
			control: 'radio'
			label: 'Edema Pitting Score'
			columns:3

	hearing_episodes:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hearing_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you noticed any changes in your hearing (i.e. fullness, noise, or ringing in the ears)?'
			columns:3

	hearing_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes per week'
			columns:3.1

	vertigo_episodes:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['vertigo_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you noticed any vertigo or walking instabilities?'
			columns:3

	vertigo_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes per week'
			columns:3.1

	joint_episodes:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['joint_episodes_cnt', 'joint_episodes_score', 'joint_episodes_comment']
		view:
			control : 'radio'
			label: 'Have you had any joint or muscle pain or tenderness since starting your therapy?'
			columns:3

	joint_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of episodes per week'
			columns:3

	joint_episodes_score:
		model:
			required: true
			source: ['1','2','3','4','5','6','7','8','9','10']
		view:
			control: 'radio'
			label: 'Based on a scale of 1 to 10 (10 being the highest), my joint or muscle pain averages around?'
			columns:3

	joint_episodes_comment:
		model:
			required: true
		view:
			control: 'area'
			label: 'Location of joint, muscle pain, or tenderness'
			columns:3

	has_cpk_lab:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['cpk_lab_level']
		view:
			control : 'radio'
			label: 'Is Creatine phosphokinase (CPK) lab level available?'
			columns:3

	cpk_lab_level:
		model:
			min: 1
			max: 5000
			type: 'int'
			required: false
		view:
			note: 'U/L (units per liter)'
			label: 'Creatine phosphokinase (CPK) Level'
			columns:3.1

	vancomycin_trough_value:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'If available'
			label: 'Vanco trough (mcg/ml)'
			columns:3

	vancomycin_trough_date:
		model:
			type: 'datetime'
		view:
			note: 'If available'
			label: 'Vanco trough date/time'
			columns:3

	vancomycin_trough_true:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['vancomycin_trough_true_details']
		view:
			control: 'radio'
			label: 'True trough?'
			columns:3

	vancomycin_trough_true_details:
		view:
			label: 'True trough details'
			columns:3.1


	aminoglycoside_drug_brand:
		model:
			required: false
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Which Aminoglycoside is patient on?'
			columns:3

	aminoglycoside_trough_value:
		model:
			type: 'int'
		view:
			note: 'If available'
			label: 'Aminoglycoside trough (mcg/ml)'
			columns:3

	aminoglycoside_trough_date:
		model:
			type: 'date'
		view:
			note: 'If available'
			label: 'Aminoglycoside trough date'
			columns:3

	aminoglycoside_peak_value:
		model:
			type: 'int'
		view:
			note: 'If available'
			label: 'Aminoglycoside peak (mcg/ml)'
			columns:3

	aminoglycoside_peak_date:
		model:
			type: 'date'
		view:
			note: 'If available'
			label: 'Aminoglycoside peak date'
			columns:3

	itching_episodes:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['itching_episodes_cnt', 'itching_episodes_comment']
		view:
			control : 'radio'
			label: 'Have you had any episodes of itching, rash, or hives since starting antibiotic therapy?'
			columns:3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		ongoing_antibiotic:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Antibiotic Details':
			fields: ['drug_class']
		'Antibiotic Followup':
			fields: ['injection_site_issues', 'injection_site_issues_details', 'catheter_issues', 'catheter_issues_comments', 'catheter_flushing', 'catheter_flushing_details', 'complications_episodes',  'complications_episodes_cnt', 'complications_comment', 'itching_episodes', 'itching_episodes_cnt', 'itching_episodes_comment', 'breathing_issues', 'breathing_issues_comment', 'fever_episodes', 'fever_episodes_cnt', 'fever_temp', 'has_infection', 'has_infection_details', 'fatigue_episodes', 'fatigue_episodes_cnt', 'nausea_episodes', 'nausea_episodes_cnt', 'diarrhea_episodes', 'diarrhea_episodes_cnt', 'taking_probiotic', 'urine_change_frequency', 'urine_frequency', 'urine_change', 'urine_change_color', 'urine_change_cnt', 'oz_per_day', 'edema_lower_extremities', 'edema_pit_score','hearing_episodes', 'hearing_episodes_cnt', 'vertigo_episodes', 'vertigo_episodes_cnt', 'joint_episodes', 'joint_episodes_cnt', 'joint_episodes_score', 'joint_episodes_comment', 'has_cpk_lab', 'cpk_lab_level', 'vancomycin_trough_value', 'vancomycin_trough_date', 'vancomycin_trough_true', 'vancomycin_trough_true_details', 'aminoglycoside_drug_brand', 'aminoglycoside_trough_value', 'aminoglycoside_trough_date', 'aminoglycoside_peak_value', 'aminoglycoside_peak_date']

view:
	comment: 'Patient > Careplan > Ongoing > Antibiotic'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Ongoing Assessment: Antibiotic'
	open: 'read'
