fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'


	# General Assessment
	infusion_sites:
		model:
			type: 'decimal'
			rounding: 1
		view:
			label: 'How many infusion sites?'
			columns: 2

	have_vaccine:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields:['have_vaccine_date', 'have_vaccine_details']
		view:
			control: 'radio'
			label: "Have you received any recent vaccines or plan to receive any vaccines while receiving IG therapy?"
			columns: 2

	have_vaccine_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Vaccines Date'
			columns: 2

	have_vaccine_details:
		model:
			required: true
		view:
			label: 'Vaccines Detail'
			columns: 2

	have_antibiotic:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Is patient currently receiving antibiotic therapy or have active infection?"
			columns: 2

	have_fever:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Does patient report recent fevers?"
			columns: 2

	have_infection:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Has the patient had any chronic or recent infections?"
			columns: 2

	have_seizure:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Does the patient have seizure disorder?"
			columns: 2

	will_be_pregnant:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Do you plan to get pregnant while on this medication?"
			columns: 2

	has_ems:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Does patient have access to EMS (w/in 15 minutes)?"
			columns: 2

	nurse_present:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will nurse be present for the entire treatment?"
			columns: 2

	adult_present:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will an additional adult be present for the entire treatment?"
			columns: 2

	adult_present_after:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will an additional adult be present for 2 to 3 hours after treatment is completed?"
			columns: 2

	physician_available:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will the ordering physician be available by phone during the treatment?"
			columns: 2

	# Renal Assessment
	has_diabetes:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['has_insulin']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with diabetes?'
			columns: 2

	has_insulin:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you depend on insulin to regulate blood sugar?'
			columns: 2

	had_kidney_disease:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with kidney disease?'
			columns: 2

	meds_taking:
		model:
			max: 32
			min: 1
			multi: true
			source: ['Acyclovir','Aminoglycosides','Amphotericin','Atripla','Cisplatin','Diuretics (loops, thiazides)','NSAIDS',
				'Prograf','Proton-Pump Inhibitors','Tenofovir','Viread','Truvada','Other','None']
			if:
				'Other':
					fields: ['meds_taking_other']
		view:
			control: 'checkbox'
			class: 'checkbox checkbox-3'
			label: 'Are you currently taking any of the following (concomitant nephrotoxic) drugs?'
			columns: -2

	meds_taking_other:
		view:
			label: 'Other Drugs'
			columns: 2

	urine_drop:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you noticed a drop in urine output?'
			columns: 2

	has_htn:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pressure_controlled']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with hypertension (HTN)?'
			columns: 2

	pressure_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your blood pressure currently controlled?'
			columns: 2

	high_chol:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['chol_controlled']
		view:
			control: 'radio'
			label: 'Is your cholesterol level high?'
			columns: -2

	chol_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your cholesterol currently controlled?'
			columns: 2

	heart_cond:
		model:
			max: 64
			min: 1
			multi: true
			source: ['Congestive heart failure (CHF)', 'Cardiomyopathy', 'Valve Disease', 'Congenital Defects', 'Atrial Fibrillation', 'Angina', 'Pulmonary disease', 'Other', 'None']
			if:
				'Other':
					fields: ['heart_cond_other']
		view:
			control: 'checkbox'
			class: 'checkbox checkbox-2'
			label: 'Do you suffer from any of the following heart diseases?'
			columns: -2

	heart_cond_other:
		view:
			control: 'area'
			label: 'Other Heart Diseases'
			columns: 2

	fam_cond:
		model:
			max: 64
			min: 1
			multi: true
			source: ['Coronary Artery Disease (CAD/Atherosclerotic)', 'Angina', 'Deep Vein Thrombosis (DVT)',
						'Cerebral Infarction', 'Myocardial Infarction', 'Other', 'None']
			if:
				'Other':
					fields: ['fam_cond_other']
		view:
			control: 'checkbox'
			label: 'Do you or any of your Parents or siblings have a history of:'
			columns: -2

	fam_cond_other:
		view:
			control: 'area'
			label: 'Other Family History'
			columns: 2

	per_immob:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you experience prolonged periods of immobilization?'
			columns: 2

	had_throm:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with thrombophilia?'
			columns: 2

	had_sic:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with sickle cell anemia?'
			columns: 2

	has_lowbp:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_sep']
		view:
			control: 'radio'
			label: 'Do your hands/extremities feel cold and clammy (do you experience low BP)?'
			columns: -2

	had_sep:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any recent cases of sepsis or infection in blood?'
			columns: 2

	had_can:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_can_details']
		view:
			control: 'radio'
			label: 'Are you currently or have you recently been on cancer treatment?'
			columns: -2

	had_can_details:
		view:
			label: 'What type of cancer and when?'
			columns: 2

	# Training
	requires_training:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				"No":
					fields:["no_training_reason"]
				"Yes":
					fields:["training_by"]
		view:
			label: 'Will patient need training for SubQ IG administration?'
			control: 'radio'
			columns: 2

	no_training_reason:
		model:
			source: {had_subq:"Patient has used SubQ IV before", physician_trained:"Patient has been trained by Physician Office", nurse_trained:"Patient has been trained by other nurse to use SubQ IV", other:"Other"}
			required: true
		view:
			label: 'Why does the patient not require training?'
			columns: 2

	training_by:
		model:
			source: {physician:"Physician-office", nurse:"Home Health Nurse", ats:"ATS"}
			required: true
			if:
				nurse:
					fields:['payor_pay_visit']
		view:
			label: 'Who will train the patient?'
			columns: 2

	payor_pay_visit:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes', 'NA']
			required: true
		view:
			label: 'Will payor cover additional RN visit hours?'
			control: 'radio'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'pharm', 'csr']
		delete:     ['admin', 'pharm', 'csr']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm', 'csr']
	bundle: ['patient', 'careplan']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
	sections:
		'SubQ IG Assessment':
			indent: false
			fields: ['infusion_sites', 'have_vaccine', 'have_vaccine_details', 'have_vaccine_date', 'have_antibiotic', 'have_fever', 'have_infection', 'have_seizure', 'will_be_pregnant', 'has_ems',
						'nurse_present', 'adult_present', 'adult_present_after', 'physician_available']
		'Renal Disease Risk Assessment':
			hide_header: false
			indent: false
			fields: ['has_diabetes', 'has_insulin', 'had_kidney_disease', 'meds_taking', 'meds_taking_other', 'urine_drop']
		'Thromboembolic Risk Assessment':
			hide_header: false
			indent: false
			fields: ['has_htn', 'pressure_controlled', 'high_chol', 'chol_controlled', 'heart_cond', 'heart_cond_other', 'fam_cond', 'fam_cond_other', 'per_immob',
						'had_throm', 'had_sic', 'has_lowbp', 'had_sep', 'had_can', 'had_can_details']
		'SubQ IG Training':
			hide_header: true
			indent: false
			fields: ['requires_training', 'no_training_reason', 'training_by', 'payor_pay_visit']

view:
	comment: 'Patient > Careplan > Assessment > IG'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
	label: 'Assessment Questionnaire: IG'
	open: 'read'
