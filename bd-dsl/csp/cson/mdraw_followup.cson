fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	pt_mdraw_particiates:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'No'
			if:
				'Yes':
					sections: ['M-DRAW Followup', 'Adherence Risk', 'Data Collection Sheet']
		view:
			control : 'radio'
			label: 'Will a M-DRAW be completed during this assessment?'

	unsure_when_take_meds:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			required: true
		view:
			label: 'Do you feel unsure about how/when to take your medications?'
			control: 'radio'

	difficulty_getting_meds:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			if:
				'Sometimes':
					fields: ['difficulty_getting_to_pharmacy', 'difficulty_paying_for_med', 'forget_refills']
				'Often':
					fields: ['difficulty_getting_to_pharmacy', 'difficulty_paying_for_med', 'forget_refills']
			required: true
		view:
			label: 'Do you have any difficulty getting your medications on time from the pharmacy?'
			control: 'radio'

	difficulty_getting_to_pharmacy:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			required: true
		view:
			label: 'Is it difficult for you to get to the pharmacy to pick up your medications?'
			control: 'radio'

	difficulty_paying_for_med:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			required: true
		view:
			label: 'Is paying for your medications a burden on your finances?'
			control: 'radio'

	forget_refills:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			required: true
		view:
			label: 'Do you forget to place refill requests on time?'
			control: 'radio'

	difficult_tracking_schedule:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			required: true
		view:
			note: 'e.g., when to take each medication'
			label: 'Do you have difficulty keeping track of all your medication schedules throughout the day?'
			control: 'radio'

	adr_interferes_adherence:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			required: true
		view:
			label: 'Do your medications give you side effects that make you NOT want to take it?'
			control: 'radio'

	worries_about_interactions:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			required: true
		view:
			label: 'Do you worry about what foods or other medications might interact with your medication?'
			control: 'radio'

	self_adjust_rx:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			required: true
		view:
			label: 'Do you feel that you can take more or less of your medication than the prescribed dose to fit your lifestyle?'
			control: 'radio'

	feels_lack_benefits:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			required: true
		view:
			label: 'Do you feel like you don’t get any benefits from taking your medication?'
			control: 'radio'

	uncomfortable_taking_meds_while_out:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			required: true
		view:
			label: 'Do you feel uncomfortable about taking your medication while you are out with family and friends?'
			control: 'radio'

	feels_med_burden_life:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			required: true
		view:
			label: 'Do you consider it a burden that you have to take your medications for the rest of your life?'
			control: 'radio'

	doubts_need_treatment:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			required: true
		view:
			label: 'Do you have doubts about whether your health condition needs to be treated?'
			control: 'radio'

	doubts_rx_improves_health:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			required: true
		view:
			label: 'Do you have doubts if taking your medication will improve your health condition in the long term?'
			control: 'radio'

	feels_treatment_inadequate:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			required: true
		view:
			label: 'Do you feel that you are NOT receiving the best possible treatment available from your health care provider?'
			control: 'radio'

	anxiety_self_injection:
		model:
			source: ['Never', 'Rarely', 'Sometimes', 'Often']
			required: true
		view:
			label: 'I experience anxiety with injecting myself with the medication'
			control: 'radio'

	other_doubts_concerns:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['additional_barriers', 'additional_barriers_intervention']
			required: true
		view:
			note: 'language barrier, cultural issues, social support, faith-related, etc.'
			label: 'Do you have any other doubts or concerns about taking your medication?'
			control: 'radio'

	additional_barriers:
		model:
			required: true
		view:
			control: 'area'
			label: 'Document additional barriers'

	additional_barriers_intervention:
		model:
			required: true
		view:
			control: 'area'
			label: 'Intervention given'

	assigned_group:
		model:
			prefill: ['mdraw_baseline']
			source: ['UNA', 'PNA', 'ONA']
			required: false
		view:
			label: 'Assigned group based on Priming Question'
			control: 'radio'
			offscreen: true
			readonly: true

	assigned_study_group:
		model:
			prefill: ['mdraw_baseline']
			source: ['Control', 'Intervention']
			if:
				'Intervention':
					sections: ['3-Month Follow-up']
			required: false
		view:
			label: 'Assigned study group'
			control: 'radio'
			offscreen: true
			readonly: true

	interview_helpful:
		model:
			source: ['Not at all helpful', 'Somewhat helpful', 'Very helpful']
		view:
			label: 'Was the interview you received 3 months ago helpful?'
			control: 'radio'

	interview_produced_change:
		model:
			source: ['No change', 'Little change', 'Significant change']
		view:
			label: 'Do you feel like there has been a change in the way you take your medication because of the interview you received?'
			control: 'radio'

	adherence_risk_baseline:
		model:
			prefill: ['mdraw_baseline.adherence_risk']
			source: ['Low', 'Moderate', 'High']
		view:
			label: 'Baseline Adherence Risk'
			control: 'radio'
			readonly: true

	adherence_risk:
		model:
			source: ['Low', 'Moderate', 'High']
			required: true
		view:
			label: 'Adherence Risk'
			control: 'radio'

	intervention_performed:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Was an intervention performed?'
			control: 'radio'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true


model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'csr', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		mdraw_baseline:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'

	sections:
		'Participation':
			fields: ['pt_mdraw_particiates']
		'M-DRAW Followup':
			note: 'For each question answered “sometimes” or “often”, check the box for “YES”. For each YES, consider the suggested actions for intervention'
			fields: ['unsure_when_take_meds', 'difficulty_getting_meds', 'difficulty_getting_to_pharmacy',
			'difficulty_paying_for_med', 'forget_refills', 'difficult_tracking_schedule', 'adr_interferes_adherence', 'worries_about_interactions', 'self_adjust_rx', 'feels_lack_benefits', 'uncomfortable_taking_meds_while_out', 'feels_med_burden_life', 'doubts_need_treatment', 'doubts_rx_improves_health', 'feels_treatment_inadequate', 'anxiety_self_injection', 'other_doubts_concerns', 'additional_barriers', 'additional_barriers_intervention']
		'3-Month Follow-up':
			fields: ['interview_helpful', 'interview_produced_change']
		'Adherence Risk':
			fields: ['adherence_risk_baseline', 'adherence_risk', 'intervention_performed']
		'Data Collection Sheet':
			fields: ['assigned_group', 'assigned_study_group']
view:
	comment: 'Patient > Careplan > Assessment > M-Draw Baseline'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Modified Drug Adherence Work-up Tool (M-DRAW) Baseline'
	open: 'read'
