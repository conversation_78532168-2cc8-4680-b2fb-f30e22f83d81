fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	route:
		model:
			prefill: ['encounter_ivig']
			source: ['Intravenous', 'Subcutaneous']
			if:
				'Intravenous':
					sections:['IG Pre-Infusion Flush', 'IG Hydration', 'IG Post-Infusion Flush']
				'Subcutaneous':
					sections: ['SubQ IG Administration']
		view:
			label: 'Admin Route'
			control: 'radio'

	# Admin Step
	time_taken:
		model:
			required: true
			type: 'time'
		view:
			label: 'Time Started'
			template: '{{now}}'

	comment:
		model:
			max: 4096
		view:
			control: 'area'
			label: "Administration Step Comment"

	# Pre-infusion Flush
	pre_flush_given:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pre_flush_type', 'pre_flush_vol', 'pre_flush_comment']
		view:
			control : 'radio'
			label: 'Was a pre-infusion flush given?'

	pre_flush_type:
		model:
			max: 32
			multi: true
			default: ['Normal Saline (NS)']
			source:
				['Heparin', 'Sterile Water (SW)','Normal Saline (NS)', 'Sterile Saline', 'D5W','D5NS', 'Other']
			if:
				'Other':
					fields: ['pre_flush_type_other']
		view:
			control : 'checkbox'
			note: 'Select all that apply'
			label: 'Flush Type'

	pre_flush_type_other:
		view:
			label: 'Pre-infusion flush other'

	pre_flush_vol:
		model:
			type: 'decimal'
			min: 0
		view:
			label: 'Pre-infusion (mls)'

	pre_flush_comment:
		view:
			label: 'Pre-infusion comment'

	# Post infusion flush
	post_flush_given:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['post_flush_type', 'post_flush_vol', 'post_flush_comment']
		view:
			control : 'radio'
			label: 'Was a post-infusion flush given?'

	post_flush_type:
		model:
			max: 32
			multi: true
			source:['Heparin','Sterile Water (SW)','Normal Saline (NS)','D5W','D5NS','Other']
			if:
				'Other':
					fields: ['post_flush_type_other']
		view:
			control : 'checkbox'
			note: 'Select all that apply'
			label: 'Post-infusion Type'

	post_flush_type_other:
		view:
			label: 'Post-infusion flush other'

	post_flush_vol:
		model:
			type: 'decimal'
			min: 0
		view:
			label: 'Post-infusion (mls)'

	post_flush_comment:
		view:
			label: 'Post-infusion comment'

	# Hydration
	hydration:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hydration_type', 'hydration_vol', 'hydration_start', 'hydration_end', 'hydration_comment']
		view:
			control : 'radio'
			label: 'Was hydration given?'

	hydration_type:
		model:
			max: 32
			multi: true
			source:['D5W', 'Normal Saline (NS)','D5NS', 'D5 1/2 NS', 'Other']
		view:
			control : 'checkbox'
			note: 'Select all that apply'
			label: 'Hydration Type'

	hydration_vol:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Hydration Volume Infused (mls)'

	hydration_comment:
		view:
			label: 'Hydration Comment'

	hydration_start:
		model:
			type: 'time'
		view:
			label: 'Hydration Time Start'

	hydration_end:
		model:
			type: 'time'
		view:
			label: 'Hydration Time End'

	# IVIG Admin
	drug_brand:
		model:
			prefill: ['encounter_ivig_admin']
			max: 32
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Drug Brand'

	pot_drug_dilution:
		model:
			prefill: ['encounter_ivig_admin']
			rounding: 1
			type: 'decimal'
		view:
			label: 'Dilution (percentage %)'

	ivig_dose:
		model:
			type: 'decimal'
			min: 0
			max: 9999
			rounding: 1
		view:
			label: 'Dose Given (mL)'

	ivig_dose_g:
		model:
			type: 'decimal'
			min: 0
			max: 9999
			rounding: 1
		view:
			label: 'Dose Given (g)'

	ivig_dose_mg:
		model:
			type: 'decimal'
			min: 0
			max: 9999
			rounding: 1
		view:
			label: 'Dose Given (mg)'

	ivig_dose_time:
		model:
			rounding: 0.05
			type: 'decimal'
		view:
			label: 'Over Time Period (hours)'

	ivig_premixed:
		model:
			prefill: ['encounter_ivig_admin']
			source: ['No', 'Yes', 'NA']
		view:
			control: 'radio'
			label: 'Was drug pre-mixed?'

	ivig_drug_details:
		model:
			subfields:
				lot:
					label: 'Drug Lot#'
				expiration:
					label: 'Expiration Date'
					type: 'date'
			type: 'json'
		view:
			control: 'grid'
			label: 'Drug/Vial Details'

	# SubQ IG Admin
	subq_admin:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['subq_premed_taken', 'subq_admin_time']
		view:
			control : 'radio'
			label: 'Was SubQ Ig administered to the patient?'

	subq_premed_taken:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['subq_premed_time']
		view:
			control : 'radio'
			label: 'Were premeds taken by the patient?'

	subq_premed_time:
		model:
			type: 'time'
		view:
			label: 'Time premeds taken by patient'

	subq_admin_time:
		model:
			type: 'time'
		view:
			label: 'Time SubQ Ig administered'

	subq_comments:
		view:
			label: 'SubQ Ig comments'
			control: 'area'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_ivig_admin:
			link:
				careplan_id: 'careplan_id'
			max: 'id'
	name: '{time_taken} : {ivig_dose} over {ivig_dose_time}hours'
	sections:
		'IG Admin Step':
			fields: ['route', 'time_taken']
		'IG Pre-Infusion Flush':
			fields: ['pre_flush_given', 'pre_flush_type', 'pre_flush_type_other', 'pre_flush_vol', 'pre_flush_comment']
			prefill: 'encounter_ivig_admin'
		'IG Hydration':
			fields: ['hydration', 'hydration_type', 'hydration_vol', 'hydration_start', 'hydration_end', 'hydration_comment']
			prefill: 'encounter_ivig_admin'
		'IG Administration':
			note: 'Check the drug label with the order before administering'
			fields: ['drug_brand', 'pot_drug_dilution', 'ivig_dose', 'ivig_dose_g', 'ivig_dose_mg', 'ivig_dose_time', 'ivig_premixed', 'ivig_drug_details']
		'SubQ IG Administration':
			fields: ['subq_admin', 'subq_premed_taken', 'subq_premed_time', 'subq_admin_time', 'subq_comments']
		'IG Post-Infusion Flush':
			fields: ['post_flush_given', 'post_flush_type', 'post_flush_type_other', 'post_flush_vol', 'post_flush_comment']
			prefill: 'encounter_ivig_admin'
		'IG Admin Step Comment':
			fields: ['comment']

view:
	comment: 'Patient > Careplan > Encounter > IG > Admin'
	grid:
		fields: ['time_taken', 'drug_brand', 'ivig_dose', 'ivig_dose_time', 'comment']
	label: 'IG Admin Step'
