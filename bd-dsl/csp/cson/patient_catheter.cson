fields:

	cust_exposed:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'in cm from insertion site'
			label: 'Catheter exposure length (cm)'

	location:
		model:
			source: ['Upper arm', 'Other']

	dressing:
		view:
			offscreen: true
			readonly: true

	dressing_details:
		view:
			offscreen: true
			readonly: true

	gauge:
		view:
			offscreen: true
			readonly: true

	length:
		view:
			offscreen: true
			readonly: true

	length_type:
		view:
			offscreen: true
			readonly: true

	facility:
		view:
			label: 'Facility where placed:'

	flush:
		view:
			control: 'area'
			note: 'if provided'
			label: 'Flush recommendation'

	note:
		view:
			control: 'area'
			label: 'Comments/Complications/Follow-up'

	cust_cath_access_new:
		model:
			access:
				read: ['patient']
			source: ['No', 'Yes', 'N/A']
			if:
				'Yes':
					fields: ['cust_cath_gauge', 'cust_cath_length', 'cust_technique', 'cust_dressing']
		view:
			control : 'radio'
			label: 'Do you want to document a new accessing of a portcath, mediport, or passport?'
			requireall_bypass: true

	cust_cath_gauge:
		model:
			access:
				read: ['patient']
			type: 'int'
			prefill: ['patient_catheter.cuse_cath_gauge']
		view:
			label: 'Non-coring Needle Gauge'
			readonly: true
			requireall_bypass: true

	cust_cath_length:
		model:
			access:
				read: ['patient']
			rounding: 0.01
			type: 'decimal'
			prefill: ['patient_catheter']
		view:
			label: 'Non-coring Needle length'
			requireall_bypass: true

	cust_technique:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Aseptic Technique?'
			requireall_bypass: true

	cust_dressing:
		model:
			access:
				read: ['patient']
			prefill: ['patient_catheter']
			source: ['Occlusive', 'Sterile Gauze', 'Other']
			if:
				'Other':
					fields: ['cust_dressing_details']
		view:
			control : 'radio'
			label: 'Dressing Type'
			requireall_bypass: true

	cust_dressing_details:
		model:
			access:
				read: ['patient']
			prefill: ['patient_catheter']
		view:
			control: 'area'
			label: 'Dressing Type Details'
			requireall_bypass: true

	cust_return:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Blood Return?'
			requireall_bypass: true

	cath_access:
		model:
			access:
				read: ['patient']
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Does vascular access draw easily?'
			requireall_bypass: true

	cath_site_cond:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Clean and Dry', 'Drainage']
			if:
				'Drainage':
					fields: ['cath_site_cond_details']
		view:
			control : 'radio'
			label: 'Site Condition'
			requireall_bypass: true

	cath_site_cond_details:
		model:
			access:
				read: ['patient']
			max: 32
			multi: true
			source: ['Red', 'Tender', 'Bruised', 'Infiltrated', 'Sutures']
			if:
				'Red':
					fields: ['cath_ointment', 'cath_ointment_com']
				'Tender':
					fields: ['cath_ointment', 'cath_ointment_com']
				'Infiltrated':
					fields: ['cath_ointment', 'cath_ointment_com']
		view:
			control : 'checkbox'
			label: 'Site Condition Details'
			requireall_bypass: true

	cath_ointment:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'AB Ointment?'

	cath_ointment_com:
		model:
			access:
				read: ['patient']
			max: 1024
		view:
			control: 'area'
			label: 'AB Ointment Comment'

	cath_flushes:
		model:
			access:
				read: ['patient']
			prefill: ['patient_catheter']
			max: 32
			source: ['Easily', 'Sluggish', 'Positional', 'Unable to flush']
		view:
			control : 'radio'
			label: 'Flushes'
			requireall_bypass: true

	cath_lumens:
		model:
			access:
				read: ['patient']
			prefill: ['patient_catheter.lumens']
			max: 32
			source: ['1', '2', '3', '4', '5']
		view:
			control : 'radio'
			label: 'Number of lumens'
			readonly: true

	dress_changed:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['dress_change_used', 'dress_change_site_asymp', 'dress_change_site_skin', 'dress_change_exp', 'dress_change_site_asymp_issues', 'dress_change_next', 'pline_installed', 'cap_changed','discontinued', 'tube_changed']
				'No':
					fields: ['dress_change_used', 'dress_change_site_asymp', 'dress_change_site_skin', 'dress_change_exp', 'dress_change_site_asymp_issues', 'dress_change_next', 'pline_installed', 'cap_changed','discontinued', 'tube_changed']
				'NA':
					fields: ['dress_change_used', 'dress_change_site_skin', 'dress_change_exp', 'dress_change_site_asymp_issues', 'tube_changed']
		view:
			control : 'radio'
			label: 'Was the dressing initiated/changed using sterile technique?'

	dress_change_used:
		model:
			access:
				read: ['patient']
			multi: true
			source: ['Silvasorb Disc', 'Bio Patch', 'Opsite', 'Tegaderm', 'IV 3000', 'Duoderm', '3M PICC Securement Device']
		view:
			control : 'checkbox'
			label: 'Dressing Changed Used'

	dress_change_site_asymp:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['dress_change_site_asymp_issues']
		view:
			control : 'radio'
			label: 'Was the insertion site asymptomatic?'

	dress_change_site_asymp_issues:
		model:
			access:
				read: ['patient']
			multi: true
			source: ['Drainage', 'Blood', 'Serous', 'Redness', 'Swelling', 'Other']
			if:
				'Other':
					fields: ['dress_change_site_asymp_issues_o']
		view:
			control: 'checkbox'
			label: 'Insertion Site Problems'

	dress_change_site_asymp_issues_o:
		model:
			required: true
		view:
			label: 'Details'

	dress_change_site_skin:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Any skin irritation around insertion site?'
			offscreen: true
			readonly: true

	dress_change_exp:
		model:
			access:
				read: ['patient']
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'in cm from insertion site'
			label: 'PICC/CL exposure length (cm)'

	dress_change_next:
		model:
			access:
				read: ['patient']
			type: 'date'
		view:
			label: 'Next dressing change due'

	pline_installed:
		model:
			access:
				read: ['patient']
			prefill: ['patient_catheter.pline_installed']
			max: 32
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['pline_change']
		view:
			control : 'radio'
			label: 'Does patient have a peripheral line installed?'

	pline_change:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pline_change_reason', 'pline_app']
		view:
			control : 'radio'
			label: 'Was the peripheral line site initiated/changed using aseptic technique?'

	pline_change_reason:
		model:
			access:
				read: ['patient']
			multi: true
			source: ['Rotation', 'Infiltration', 'Irritation', 'Redness', 'Swelling', 'Drainage', 'Other']
			if:
				'Other':
					fields: ['pline_change_reason_other']
		view:
			control: 'checkbox'
			label: 'Peripheral line site initiated/changed reason'

	pline_change_reason_other:
		model:
			access:
				read: ['patient']
		view:
			label: 'Peripheral line site initiated/changed reason other'

	pline_app:
		model:
			access:
				read: ['patient']
			multi: true
			source: ['Clean & Dry', 'Redness', 'Swelling', 'Drainage', 'Irritation']
		view:
			control : 'select'
			label: 'Peripheral Line Appearance'

	cap_changed:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['cap_changed_lumens']
		view:
			control : 'radio'
			label: 'Were any cap(s) initiated/changed?'

	cap_changed_lumens:
		model:
			access:
				read: ['patient']
			type: 'int'
			required: true
			default: 1
			min: 1
			max: 5
		view:
			label: 'Caps Changed Lumens'
			note: 'Please add a value from 1 to 5'

	tube_changed:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['tube_changed_lumens']
		view:
			control : 'radio'
			label: 'Was any extension tubing initiated/changed?'

	tube_changed_lumens:
		model:
			access:
				read: ['patient']
			type: 'int'
			required: true
			min: 1
			max: 5
		view:
			label: 'Extension Tubing Changed Lumens'
			note: 'Please add a value from 1 to 5'
	discontinued:
		model:
			access:
				read: ['patient']
			max: 64
			multi: true
			source: ['PICC', 'Midline', 'Non-tunneled CVC', 'PIV', 'Port-a-cath decannulation (deaccessed)']
			if:
				'Non-tunneled CVC':
					fields: ['discontinued_protocol', 'discontinued_reason']
				'PICC':
					fields: ['discontinued_reason', 'discontinued_picc_length', 'discontinued_picc_bleed', 'discontinued_picc_teach', 'discontinued_picc_insert_length']
				'Midline':
					fields: ['discontinued_reason']
				'PIV':
					fields: ['discontinued_reason']
				'Port-a-cath decannulation (deaccessed)':
					fields: ['discontinued_reason']
		view:
			control : 'checkbox'
			note: 'Select any items that were discontinued'
			label: 'Discontinued'
	discontinued_protocol:
		model:
			access:
				read: ['patient']
			max: 1024
			required: true
		view:
			label: 'Discontinued Non-tunneled CVC Per Protocol'

	discontinued_reason:
		model:
			access:
				read: ['patient']
			max: 64
			required: true
			source:
				rotated: 'Site rotated'
				thrombosis: 'Occluded'
				therapy_completed: 'Therapy completed'
				infusion_completed: 'Infusion completed'
				presc_notified: 'Prescriber notified'
				infiltration: 'Infiltration'
				cath_infection: 'Catheter infection'
				port_access: 'Port Reaccess'
				needle_change: 'Needle Change'
		view:
			control : 'radio'
			label: 'Reason For Discontinuation'

	discontinued_picc_length:
		model:
			access:
				read: ['patient']
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'PICC/CL length (cm)'

	discontinued_picc_insert_length:
		model:
			access:
				read: ['patient']
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'in cm from insertion site'
			label: 'PICC/CL exposure length prior to removal (cm)'

	discontinued_picc_bleed:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Bleeding at insertion upon removal?'

	discontinued_picc_teach:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['No', 'Yes']
		view:
			control : 'radio'
			note: 'report to ER if excessive bleeding at site/swelling of arm, keep dressing clean and dry for 24 hours'
			label: 'Teaching completed regarding care of removal site?'
	cath_comments:
		model:
			access:
				read: ['patient']
			prefill: ['patient_catheter.cath_comments']
			max: 4086
		view:
			control: 'area'
			label: 'IV Comments/Pertinent History'
			requireall_bypass: true

	cust_piv_new:
		model:
			access:
				read: ['patient']
			source: ['No', 'Yes', 'N/A']
			if:
				'Yes':
					fields: ['cust_piv_date', 'cust_piv_location', 'cust_piv_side',
					'cust_piv_sticks', 'cust_piv_technique', 'cust_piv_cleanser', 'cust_piv_gauge',
					'cust_return', 'cath_flushes']
		view:
			control : 'radio'
			label: 'Do you want to document a new PIV insertion?'

	cust_piv_date:
		model:
			type: 'date'
		view:
			label: 'Insertion date'

	cust_piv_location:
		model:
			source: ['Antecubital', 'Upper arm', 'Forearm', 'Hand']
		view:
			control : 'radio'
			label: 'Location'

	cust_piv_side:
		model:
			source: ['Down' , 'Left Arm', 'Right Arm' , 'Antecubital' , 'Forearm' , 'Hand']
		view:
			control : 'radio'
			label: 'Side'

	cust_piv_sticks:
		model:
			type: 'int'
		view:
			label: 'Number of Sticks'

	cust_piv_technique:
		model:
			max: 32
			source: ['Clean', 'Aseptic']
		view:
			control : 'radio'
			label: 'Technique'

	cust_piv_cleanser:
		model:
			max: 32
			source: ['Chloraprep', 'Alcohol', 'Provodine']
		view:
			control : 'radio'
			label: 'Cleanser'

	cust_piv_gauge:
		model:
			type: 'int'
		view:
			label: 'Needle Size (Gauge)'

	type_id:
		model:
			required: true
			source: 'list_catheter_type'
			sourceid: 'code'
			if:
				'picc_midline':
					fields: ['cust_exposed' ,'mid_circ', 'lumens']
				'tunneled':
					fields: ['cust_exposed']
				'non_tunneled':
					fields: ['cust_exposed']
				'pip_iv':
					fields: ['cust_exposed','cust_piv_sticks', 'cust_piv_technique', 'cust_piv_cleanser', 'cust_piv_gauge']
				'port':
					fields: ['lumens']
				'other':
					fields: ['type_other']
		view:
			columns: -2
			control: 'select'
			label: 'Type'
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin','liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin','csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	sections:
		'Patient Catheter Information':
			fields: ['date_placed', 'date_discontinued', 'device', 'device_other', 'type_id', 'type_other', 'lumens',
			'mid_circ', 'side', 'cust_exposed', 'facility',
			'flush']
		'Catheter Assessment':
			fields: ['cust_cath_access_new', 'cust_cath_gauge', 'cust_cath_length', 'cust_technique',
					'cust_dressing', 'cust_dressing_details', 'cust_return', 'cath_flushes', 'cath_access', 'cath_site_cond', 'cath_site_cond_details', 'cath_comments']
		'Catheter Maintenance':
			fields: ['cath_ointment', 'cath_ointment_com', 'dress_changed', 'dress_change_used', 'dress_change_site_asymp', 'dress_change_site_asymp_issues', 'dress_change_site_asymp_issues_o', 'dress_change_exp', 'dress_change_next', 'pline_installed', 'pline_change', 'pline_change_reason', 'pline_change_reason_other', 'pline_app', 'cap_changed', 'cap_changed_lumens', 'tube_changed', 'tube_changed_lumens', 
					'discontinued', 'discontinued_protocol', 'discontinued_reason', 'discontinued_picc_length', 'discontinued_picc_insert_length', 'discontinued_picc_bleed', 'discontinued_picc_teach']

	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Patient Catheter Information"
	]

view:
	comment: 'Patient > Patient Catheter Information'
	grid:
		fields: ['date_placed', 'device', 'type_id', 'location', 'date_discontinued']
		sort: ['-date_placed']
	label: 'Patient Catheter Information'
	open: 'read'