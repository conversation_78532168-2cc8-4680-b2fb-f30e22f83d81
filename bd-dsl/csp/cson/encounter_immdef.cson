fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	route:
		model:
			prefill: ['encounter_ivig_immdef']
			source: ['Intravenous', 'Subcutaneous']
			if:
				'Intravenous':
					fields:['pt_disease_hidden']
				'Subcutaneous':
					sections: ['Patient Questionnaire']
		view:
			control : 'radio'
			label: 'Admin Route'

	# Disease Specific Participation
	pt_disease_hidden:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'Yes'
			if:
				'Yes':
					sections: ['Follow-up']
		view:
			control : 'radio'
			label: 'Will a disease specific assessment be completed on this visit?'

	# Follow-up
	immdef_is_first:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['immdef_infections', 'immdef_antibiotics']
		view:
			control : 'radio'
			label: "Is this the patient's first infusion?"

	immdef_infections:
		model:
			max: 128
			source: ['More than once', 'Once', 'None']
			if:
				'More than once':
					fields: ['immdef_infections_type']
				'Once':
					fields: ['immdef_infections_type']
		view:
			control: 'radio'
			label: 'Have you had any infections since the last infusion?'

	immdef_infections_type:
		model:
			max: 32
			multi: true
			required: true
			source: ['Ear', 'Sinus', 'Skin', 'Thrush', 'Abscess', 'Pneumonia', 'Upper Respiratory Tract Infection', 'Bronchitis', 'Laryngitis', 'Other']
			if:
				'Other':
					fields: ['immdef_infections_type_other']
		view:
			control : 'checkbox'
			note: 'Select all that apply'
			label: 'Location / type of infections'

	immdef_infections_type_other:
		model:
			max: 1024
			required: true
		view:
			note: 'Details about other infection'
			label: 'Location / type of infections - Other'

	immdef_antibiotics:
		model:
			max: 128
			source: ['More than once', 'Once', 'None']
		view:
			control : 'radio'
			label: 'How many times has antibiotics been prescribed since the last infusion?'

	immdef_missed_days:
		model:
			max: 128
			source: ['More than once', 'Once', 'None']
		view:
			control : 'radio'
			label: 'In the last month how many days of work/school did you miss due to your immune deficiency?'

	immdef_immunoglobulin_test_date:
		model:
			type: 'date'
		view:
			note: '(Insurance requests every 6 months)'
			label: 'When is your next scheduled Immunoglobulin levels lab test?'

	# Patient Questions
	had_fever:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_fever_count', 'had_fever_type']
		view:
			control : 'radio'
			label: "Have you had any episodes of fever?"

	had_fever_count:
		model:
			min:1
			max:1000
			required: true
			rounding: 1
			type: 'decimal'
		view:
			label: 'Number of episodes per day/week/month'

	had_fever_type:
		model:
			required:true
			source:
				day: 'Day'
				week: 'Week'
				month: 'Month'
		view:
			control : 'radio'
			label: "Episodes per day/week/month"

	treated_for_infections:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'Yes':
					fields: ['treated_for_infections_details']
		view:
			control: 'radio'
			label: 'Have you been treated for any infections since your last appointment?'

	treated_for_infections_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'

	had_fatigue:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_fatigue_count', 'had_fatigue_type']
		view:
			control : 'radio'
			label: "Have you had any episodes of fatigue?"

	had_fatigue_count:
		model:
			min:1
			max:1000
			required: true
			rounding: 1
			type: 'decimal'
		view:
			label: 'Number of episodes per day/week/month/year'

	had_fatigue_type:
		model:
			required:true
			source:
				day: 'Day'
				week: 'Week'
				month: 'Month'
				year: 'Year'
		view:
			control : 'radio'
			label: "Episodes per day/week/month/year"

	had_diarrhea:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_diarrhea_count', 'had_diarrhea_type']
		view:
			control : 'radio'
			label: "Have you had any episodes of diarrhea?"

	had_diarrhea_count:
		model:
			min:1
			max:1000
			required: true
			rounding: 1
			type: 'decimal'
		view:
			label: 'Number of episodes per day/week/month/year'

	had_diarrhea_type:
		model:
			required:true
			source:
				day: 'Day'
				week: 'Week'
				month: 'Month'
				year: 'Year'
		view:
			control : 'radio'
			label: "Episodes per day/week/month/year"


	had_abdominal_pain:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_abdominal_pain_count', 'had_abdominal_pain_type']
		view:
			control : 'radio'
			label: "Have you had any episodes of abdominal pain or tenderness?"

	had_abdominal_pain_count:
		model:
			min:1
			max:1000
			required: true
			rounding: 1
			type: 'decimal'
		view:
			label: 'Number of episodes per day/week/month/year'

	had_abdominal_pain_type:
		model:
			required:true
			source:
				day: 'Day'
				week: 'Week'
				month: 'Month'
				year: 'Year'
		view:
			control : 'radio'
			label: "Episodes per day/week/month/year"

	had_weight_issue:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'Yes':
					fields: ['had_weight_issue_details']
		view:
			control: 'radio'
			label: 'Have you had any loss of appetite or severe weight loss?'

	had_weight_issue_details:
		model:
			required: true
		view:
			control : 'area'
			label: 'Details'

	had_itching:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_itching_count', 'had_itching_type', 'had_itching_location']
		view:
			control : 'radio'
			label: "Have you had any episodes of itching?"

	had_itching_count:
		model:
			min:1
			max:1000
			required: true
			rounding: 1
			type: 'decimal'
		view:
			label: 'Number of episodes per day/week/month/year'

	had_itching_type:
		model:
			required:true
			source:
				day: 'Day'
				week: 'Week'
				month: 'Month'
				year: 'Year'
		view:
			control : 'radio'
			label: "Episodes per day/week/month/year"

	had_itching_location:
		model:
			required:true
		view:
			label: "Location of itching"

	had_pain_site:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_pain_site_count', 'had_pain_site_type', 'had_pain_scale', 'had_pain_location']
		view:
			control : 'radio'
			label: "Have you had any episodes of pain or tenderness at the injection sites?"

	had_pain_site_count:
		model:
			min:1
			max:1000
			required: true
			rounding: 1
			type: 'decimal'
		view:
			label: 'Number of episodes per day/week/month'

	had_pain_site_type:
		model:
			required:true
			source:
				day: 'Day'
				week: 'Week'
				month: 'Month'
		view:
			control : 'radio'
			label: "Episodes per day/week/month"

	had_pain_scale:
		model:
			min:1
			max:10
			rounding: 1
			type: 'decimal'
		view:
			label: 'Based on a scale of 0 to 10 (10 being the highest), my pain usually averages around:'

	had_pain_location:
		view:
			label: 'Location of Pain/tenderness:'

	had_loss_sleep:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_loss_sleep_count', 'had_loss_sleep_type']
		view:
			control : 'radio'
			label: "Have you had any loss of sleep?"

	had_loss_sleep_count:
		model:
			min:1
			max:1000
			required: true
			rounding: 1
			type: 'decimal'
		view:
			label: 'Number of hours per night/week/month'

	had_loss_sleep_type:
		model:
			required:true
			source:
				night: 'Night'
				week: 'Week'
				month: 'Month'
		view:
			control : 'radio'
			label: "Hours per night/week/month"

	change_condition:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['change_condition_details']
		view:
			control : 'radio'
			label: "Have you recently had to change your daily activities due to your condition?"

	change_condition_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Details'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External'
			readonly: true
			offscreen: true


model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_ivig_immdef:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Disease Specific Participation':
			fields: ['pt_disease_hidden']
		'Follow-up':
			fields: ['immdef_is_first', 'immdef_infections', 'immdef_infections_type', 'immdef_infections_type_other', 'immdef_antibiotics', 'immdef_missed_days', 'immdef_immunoglobulin_test_date']
		'Patient Questionnaire':
			area:'questions'
			fields: ['had_fever', 'had_fever_count', 'had_fever_type', 'treated_for_infections', 'treated_for_infections_details', 'had_fatigue', 'had_fatigue_count', 'had_fatigue_type',
					 'had_diarrhea', 'had_diarrhea_count', 'had_diarrhea_type', 'had_abdominal_pain', 'had_abdominal_pain_count', 'had_abdominal_pain_type',
					 'had_weight_issue', 'had_weight_issue_details', 'had_itching', 'had_itching_count', 'had_itching_type', 'had_itching_location', 'had_pain_site', 'had_pain_site_count',
					 'had_pain_site_type', 'had_pain_scale', 'had_pain_location', 'had_loss_sleep', 'had_loss_sleep_count', 'had_loss_sleep_type', 'change_condition', 'change_condition_details']

view:
	comment: 'Patient > Careplan > Encounter > IG > Immune Deficiency'
	label: 'Patient Encounter: IG - Immune Deficiency'
