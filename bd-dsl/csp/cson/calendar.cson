fields:
	name:
		model:
			max: 128
			required: true
		view:
			label: 'Calendar'
	color:
		model:
			max: 16
			required: true
		view:
			label: 'Color (HEX)'

	default_event_duration:
		model:
			type: 'int'
		view:
			label: 'Default Event Duration (minutes)'

	default_event_span:
		model:
			type: 'int'
		view:
			label: 'Default Span for Recurring Events (months)'

	user_id:
		model:
			multi: true
			source: 'user'
			required: false
		view:
			label: 'Assign To'

	agency_user_ids:
		model:
			multi: true
			source: 'user'
			required: false
		view:
			label: 'Agency Nurses To'
			readonly: true
	
	map_link_address:
		view:
			label: 'Map Link Address'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['nursing']
	indexes:
		unique: [
			['name']
		]
	name: ['name']
	sections:
		'Main':
			fields: ['name', 'color','default_event_duration','default_event_span', 'user_id', 'agency_user_ids', 'map_link_address']

view:
	comment: 'Manage > Calendar'
	find:
		basic: ['name']
	grid:
		fields: ['name', 'color']
		sort: ['name']
	label: 'Calendar'
