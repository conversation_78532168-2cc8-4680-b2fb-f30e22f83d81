fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	# Disease Specific Participation
	had_side_effects:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['side_effects']
		view:
			control : 'radio'
			label: 'Have you had any side effects or reactions related to your treatment medications or prescribed therapies?'

	side_effects:
		model:
			required: true
		view:
			control: 'area'
			label: 'What type of side effect or reactions did you have?'

	range_changes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['range_changes_time']
		view:
			control : 'radio'
			label: 'Have you had any changes in your range of motion?'

	range_changes_time:
		model:
			required: true
			source: {morning:'Mostly in the morning', all_day:'Last all day', night:'Mostly at night', decreased:'Has decreased significantly since may last follow-up appointment', other:'Other'}
			if:
				other:
					fields: ['range_changes_time_other']
		view:
			control : 'radio'
			label: 'Is it mostly in the morning, evening, or does it last all day?'

	range_changes_time_other:
		model:
			required: true
		view:
			label: 'Describe the changes'

	joint_changes:
		model:
			multi: true
			source: ['Joint Redness', 'Joint Warmth', 'Joint Swelling']
			if:
				'Joint Redness':
					fields: ['joint_changes_joints']
				'Joint Warmth':
					fields: ['joint_changes_joints']
				'Joint Swelling':
					fields: ['joint_changes_joints']
		view:
			control : 'checkbox'
			label: 'Have you had an increase in any of the following:'

	joint_changes_joints:
		model:
			required: true
			multi: true
			source: ['Upper Body', 'Hands', 'Wrist', 'Elbow', 'Neck', 'Shoulders', 'Back', 'Lower Body', 'Hip', 'Knees', 'Ankles', 'Feet', 'Other']
			if:
				'Other':
					fields: ['joint_changes_joints_other']
		view:
			control: 'checkbox'
			label: 'What joints are involved?'

	joint_changes_joints_other:
		model:
			required: true
		view:
			label: 'Joints Other'

	had_pain:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pain_location']
		view:
			control : 'radio'
			label: 'Have you had an increase in joint pain or tenderness since your last follow-up appointment?'

	pain_location:
		model:
			required: true
			multi: true
			source: ['Upper Body', 'Hands', 'Wrist', 'Elbow', 'Neck', 'Shoulders', 'Back', 'Lower Body', 'Hip', 'Knees', 'Ankles', 'Feet', 'Other']
			if:
				'Other':
					fields: ['pain_location_other']
		view:
			control : 'radio'
			label: 'Pain/Tenderness Location'

	pain_location_other:
		model:
			required: true
		view:
			label: 'Pain/Tenderness Location Other'

	pain_scale:
		model:
			required: true
			max: 2
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Based on a scale of 1 to 10 (10 being the highest), my pain usually averages around'

	sleep_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['sleep_episodes_cnt']
		view:
			control : 'radio'
			label: 'Have you had any loss of sleep?'

	sleep_episodes_cnt:
		model:
			type: 'int'
			min: 1
			required: true
		view:
			label: 'Number of episodes per week'

	activity_change:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control : 'radio'
			label: 'Have you recently had to change your daily activities due to your rheumatoid arthritis?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'

	sections:
		'Patient Questionnaire: TNF - Rheumatology':
			area:'questions'
			fields: ['had_side_effects', 'side_effects', 'range_changes', 'range_changes_time', 'range_changes_time_other', 'had_pain', 'pain_location', 'pain_location_other', 'pain_scale',
					 'joint_changes', 'joint_changes_joints', 'joint_changes_joints_other', 'sleep_episodes', 'sleep_episodes_cnt', 'activity_change']

view:
	comment: 'Patient > Careplan > Encounter > TNF-Inhibitor > Rheumatology'
	label: "Patient Encounter: TNF-Inhibitor - Rheumatology"
