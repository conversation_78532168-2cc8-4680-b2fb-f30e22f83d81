fields:
	therapy:
		model:
			default: 'IVIG'
			max: 64
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Therapy'
	disease:
		model:
			max: 64
			required: false
			source: 'list_disease'
			sourceid: 'code'
		view:
			label: 'Disease'
	title:
		model:
			max: 128
			required: true
		view:
			label: 'Title'
	url:
		model:
			max: 1024
			required: true
		view:
			label: 'URL'
	description:
		model:
			max: 512
			required: true
		view:
			control: 'area'
			label: 'Description'
model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	indexes:
		unique: [
			['therapy', 'title']
		]
	name: '{title}'
	sections:
		'Main':
				fields: ['therapy', 'disease', 'title', 'url', 'description']
view:
	comment: 'Manage > Education'
	find:
		basic: ['therapy', 'title']
		advanced: ['description']
	grid:
		fields: ['therapy', 'disease', 'title', 'url']
	label: 'Education'
