fields:
	patient_id:
		model:
			source: 'patient'
			type: 'int'
			prefill: ['parent.patient_id']
		view:
			label: 'Patient'
			offscreen: true
			readonly: true
			validate: [
				{
					"name": "CopyFromParent",
					"copy": {
						"patient_id": "patient_id"
					}
				},
				{
					name: 'ParentPrefill'
					fields: [
						'ncpdp_cob_opayer.patient_id',
					]
				}
			]

	transaction_code:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '103-A3'
			if:
				'B1': # Drug Billing
					fields: ['subform_opayer', 'other_reject_code', 'subform_paid', 'subform_resp', 'subform_benefit', 'paid_total', 'pt_responsability_total']
					sections: ['Other Payers', 'Other Payer Paid', 'Patient Responsibility', 'Benefits Stage']
				'B3': # Drug Reverse and Bill
					fields: ['subform_opayer', 'other_reject_code', 'subform_paid', 'subform_resp', 'subform_benefit', 'paid_total', 'pt_responsability_total']
					sections: ['Other Payers', 'Other Payer Paid', 'Patient Responsibility', 'Benefits Stage']
				'S1': # Service Billing
					fields: ['subform_opayer', 'other_reject_code', 'subform_paid', 'subform_resp', 'subform_benefit', 'paid_total', 'pt_responsability_total']
					sections: ['Other Payers', 'Other Payer Paid', 'Patient Responsibility', 'Benefits Stage']
				'S3': # Service Reverse and Bill
					fields: ['subform_opayer', 'other_reject_code', 'subform_paid', 'subform_resp', 'subform_benefit', 'paid_total', 'pt_responsability_total']
					sections: ['Other Payers', 'Other Payer Paid', 'Patient Responsibility', 'Benefits Stage']
		view:
			label: 'Transmission Code'
			readonly: true
			offscreen: true
			validate: [
				{
					"name": "CopyFromParent",
					"copy": {
						"transaction_code": "transaction_code"
					}
				}
			]

	pinsurance_id:
		model:
			prefill: ['parent.insurance_id']
			source: 'patient_insurance'
			type: 'int'
		view:
			label: 'Insurance'
			readonly: true
			offscreen: true
			validate: [
				{
					"name": "CopyFromParent",
					"copy": {
						"pinsurance_id": "insurance_id"
					}
				},
				{
					name: 'ParentPrefill'
					fields: [
						'ncpdp_cob_opayer.pinsurance_id',
					]
				}
			]

	subform_opayer:
		model:
			type: 'subform'
			multi: true
			source: 'ncpdp_cob_opayer'
		view:
			grid:
				add: 'flyout'
				edit: true
				fields: ['insurance_id', 'other_coverage_type', 'other_date']
				label: ['Payer', 'Cvg Type', 'Paid Date']
				width: [50, 35, 15]
			reference: '337-4C'
			note: '337-4C'
			label: 'Other Payers'
			max_count: 9

	other_reject_code:
		model:
			multi: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '511-FB'
		view:
			reference: '472-6E'
			note: '472-6E'
			label: 'Other Payer Reject Code(s)'
			max_count: 5

	subform_paid:
		model:
			type: 'subform'
			multi: true
			source: 'ncpdp_cob_opayer_paid'
		view:
			grid:
				add: 'inline'
				edit: true
				fields: ['paid_qualifier', 'other_payer_amount_paid']
				label: ['Qualifier', 'Paid Amt $']
				width: [50, 50]
			label: 'Other Payer Paid'
			max_count: 9
			transform: [
					name: 'CalcOtherPayerPaidTotal'
			]

	subform_resp:
		model:
			type: 'subform'
			multi: true
			source: 'ncpdp_cob_pt_resp'
		view:
			grid:
				add: 'inline'
				edit: true
				fields: ['pt_resp_amt_qualifier', 'pt_resp_amt']
				label: ['Qualifier', 'Pt Resp Amt $']
				width: [50, 50]
			label: 'Patient Responsibility'
			max_count: 25
			transform: [
					name: 'CalcPatientRespTotal'
			]

	subform_benefit:
		model:
			type: 'subform'
			multi: true
			source: 'ncpdp_cob_benefit'
		view:
			grid:
				add: 'inline'
				edit: true
				fields: ['benefit_stage_qualifier', 'benefit_stage_amount']
				label: ['Stage Qualifier', 'Stage Amt $']
				width: [50, 50]
			label: 'Benefit Stage Amount'
			max_count: 4

	paid_total:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			label: 'Total Paid'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	pt_responsability_total:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			label: 'Total Patient Resp'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
			['pinsurance_id']
		]

	name: '{transaction_code} total paid: {paid_total}'
	sections_group: [

		'Header':
			hide_header: true
			indent: false
			fields: ['transaction_code', 'patient_id', 'pinsurance_id']

		'Other Payers':
			hide_header: false
			indent: false
			note: 'Max 9 Entries.'
			fields: ['subform_opayer']

		'Other Payer Paid':
			hide_header: false
			indent: false
			note: 'Max 9 Entries. Not used for non-governmental agency programs if Patient Responsibility Amount (352-NQ) is submitted.'
			fields: ['subform_paid']

		'Patient Responsibility':
			hide_header: false
			indent: false
			note: 'Max 25 Entries'
			fields: ['subform_resp']

		'Benefits Stage':
			hide_header: false
			indent: false
			note: 'Max 4 Entries. Required if the previous payer has financial amounts that apply to Medicare Part D beneficiary benefit stages. This field is required when the plan is a participant in a Medicare Part D program that requires reporting of benefit stage specific financial amounts.'
			fields: ['subform_benefit']

		'Reject Codes':
			hide_header: false
			indent: false
			note: 'Max 5 Entries. Required when the other payer has denied the payment for the billing, designated with Other Coverage Code (308-C8) = 3 (Other Coverage Billed – claim not covered). This field must only contain the NCPDP Reject Code (511-FB) values.'
			fields: ['other_reject_code']

		'COB Totals':
			hide_header: false
			indent: false
			fields: ['paid_total', 'pt_responsability_total']
		]

view:
	dimensions:
		width: '90%'
		height: '85%'
	comment: 'COB'
	grid:
		fields: ['paid_total', 'pt_responsability_total']
		width: [50, 50]
		sort: ['-created_on']
	label: 'COB'
	open: 'read'