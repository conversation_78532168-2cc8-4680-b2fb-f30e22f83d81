fields:

	fdb_id:
		model:
			source: 'list_fdb_ndc'
			sourceid: 'code'
		view:
			label: 'Matching FDB Item'
			readonly: true

	compound_no:
		model:
			type: 'int'
		view:
			label: 'Compound Number'
			readonly: true
			offscreen: true

	#CompoundIngredient.ItemNumber.Qualifier
	qual_id:
		model:
			source: 'list_ss_product_qualifier'
			sourceid: 'code'
		view:
			label: 'Qualifier'
			readonly: true
			offscreen: true

	#CompoundIngredient.ItemNumber.Code
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true

	#CompoundIngredient.CompoundIngredientItemDescription
	description:
		view:
			label: 'Description'
			readonly: true

	#CompoundIngredient.Strength.StrengthValue
	strength:
		view:
			columns: 3
			label: 'Strength'
			readonly: true

	#CompoundIngredient.Strength.StrengthForm.Code
	strength_form_id:
		model:
			source: 'list_ncpdp_strength_form'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Strength Form'
			readonly: true

	#CompoundIngredient.Strength.StrengthUnitOfMeasure.Code
	strength_uom_id:
		model:
			source: 'list_ncpdp_strength_unit_msr'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Strength UOM'
			readonly: true

	#CompoundIngredient.Quantity.Value
	quantity:
		model:
			type: 'decimal'
		view:
			columns: 3
			label: 'Quantity'
			readonly: true

	#CompoundIngredient.Quantity.CodeListQualifier
	quantity_qualifier_id:
		model:
			source: 'list_ss_quantity_qualifier'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Quantity Qualifier'
			readonly: true

	#CompoundIngredient.Quantity.QuantityUnitOfMeasure.Code
	quantity_uom_id:
		model:
			source: 'list_ncpdp_quantity_unit_msr'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Quantity UOM'
			readonly: true

	#CompoundIngredient.DeaSchedule.Code
	dea_schedule_id:
		model:
			source: 'list_dea_schedule'
			sourceid: 'code'
		view:
			label: 'DEA Schedule'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: ['description']
	indexes:
		many: [
			['strength_form_id']
			['strength_uom_id']
			['quantity_qualifier_id']
			['quantity_uom_id']
			['dea_schedule_id']
		]
	sections:
		'Compound Ingredient':
			fields: ['compound_no', 'fdb_id', 'description', 'strength', 'strength_form_id',
			'strength_uom_id', 'qual_id', 'code', 'quantity', 'quantity_qualifier_id', 'quantity_uom_id', 'dea_schedule_id']
view:
	hide_cardmenu: true
	comment: 'Surescripts Compound Ingredient'
	grid:
		fields: ['description', 'strength', 'strength_uom_id', 'quantity', 'quantity_qualifier_id', 'quantity_uom_id', 'dea_schedule_id']
		sort: ['-created_on']
	label: 'Surescripts Compound Ingredient'
	open: 'read'
