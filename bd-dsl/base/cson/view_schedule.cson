fields:
	user_id:
		model:
			multi: true;
			source: 'user'
			#CM:2014-08-21 - this can be removed later on if we want to schedule other user types
			sourcefilter:
				role:
					'static': 'nurse'
			type: 'int'
		view:
			label: 'Nurse'
	patient_id:
		model:
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
	calendar_id:
		model:
			source: 'calendar'
			type: 'int'
		view:
			label: 'Calendar'
	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Primary Therapy'
	show_schedule_encounter:
		model:
			default: 'Yes'
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Show Nurse Visits'
	show_schedule_lab:
		model:
			default: 'No'
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Show Lab Orders'
	show_schedule_selfreport:
		model:
			default: 'No'
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Show Self-Report'

model:
	access:
		read:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
	name:['user_id', 'patient_id', 'calendar_id']
	save: false

view:
	comment: 'Setup > View > Schedule'
	find:
		basic: ['user_id', 'patient_id', 'calendar_id']
	label: 'Schedule'
