fields:

	inventory_id:
		model:
			required: true
			search: 'A'
			source: 'inventory'
		view:
			columns: 3
			label: 'Transferred Item'
			readonly: true

	lot_id:
		model:
			required: false
			search: 'B'
			source: 'inventory_lot'
		view:
			label: 'Lot'
			columns: 3
			readonly: true

	serial_id:
		model:
			required: false
			search: 'B'
			source: 'inventory_serial'
		view:
			label: 'Serial'
			columns: 3
			readonly: true

	quantity:
		model:
			required: true
			search: 'C'
			rounding: 1
			type: 'decimal'
		view:
			columns: 3
			label: 'Quantity'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin','pharm','csr','cm','nurse']
		read_all:   ['admin','pharm','csr','cm','nurse']
		request:    []
		review:     []
		update:     []
		update_all: ['admin','pharm','csr','cm']
		write:      ['admin','pharm','csr','cm']
	bundle: ['inventory']
	indexes:
		many: [
			['inventory_id']
			['lot_id']
			['serial_id']
		]
	name: '{inventory_id} {quantity}'
	sections:
		'Inventory Transfer Receipt Item':
			fields: ['inventory_id', 'lot_id', 'serial_id', 'quantity',]
view:
	comment: 'Inventory Transfer Receipt Item'
	find:
		basic: ['inventory_id']
		advanced: ['lot_id', 'serial_id']
	grid:
		fields: ['inventory_id', 'lot_id', 'serial_id', 'quantity']
		sort: ['-id']
	label: 'Inventory Transfer Receipt Item'
	open: 'edit'