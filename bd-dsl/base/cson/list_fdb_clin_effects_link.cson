#TABLE RADIMIE4_CLIN_EFFECTS_LINK

fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#DDI_CODEX
	ddi_codex:
		model:
			type: 'int'
		view:
			label: 'Drug-Drug Expanded Interaction Code'
			readonly: true
			findunique: true
			columns: 2

	#ADI_EFFTC
	adi_efftc:
		view:
			label: 'Drug-Drug Interaction Clinical Effect Code'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['adi_efftc', 'ddi_codex']
	indexes:
		many: [
			['adi_efftc']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['adi_efftc', 'ddi_codex']

view:
	comment: 'Manage > List FDB Drug-Drug Interaction/Clinical Effects Relation Table'
	find:
		basic: ['adi_efftc']
	grid:
		fields: ['adi_efftc', 'ddi_codex']
	label: 'List FDB Drug-Drug Interaction/Clinical Effects Relation Table'
