fields:
	sent_dt:
		model:
			type: 'datetime'

		view:
			columns: 4
			label: 'Sent At'
			template: '{{now}}'	
	fax_id:
		model:
			type: 'text'
		view:
			label: 'Fax ID'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]
	site_id:
		model:
			required: true
			source: 'site'
			sourceid: 'id'
		view:
			label: 'Site'
			columns: 4
	received_dt:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Received At'
	delivered_dt:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Delivered At'
	direction:
		model:
			source: ['inbound', 'outbound']
			if: 
				'outbound':
					fields:  ['user_id', 'retries'] 
		view:
			columns: 2
			label: 'Direction'
	status:
		model:
			source: ['new', 'scheduled', 'sending', 'delivered', 'failed', 'cancelled', 'received']
			if: 
				'failed':
					fields: ['error_message', 'error_code']
				'new':
					fields: ['sent_dt']
				'sending':
					fields: ['sent_dt', 'retries']
				'delivered':
					fields: ['delivered_dt']
				'received':
					fields: ['received_dt']
				'scheduled':
					fields: ['scheduled_dt']
		view:
			columns: 1
			label: 'Fax Status'
	scheduled_dt:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Scheduled At'
	status_code:
		view:
			columns: 2
			label: 'Status Code'
			readonly: true
	error_message:
		view:
			columns: 2
			label: 'Error Message'
	error_code:
		view:
			columns: 2
			label: 'Error Code'
	total_pages:
		model:
			type: 'int'
		view:
			label: 'Total Pages'
			columns: 4
	total_duration:
		model:
			type: 'int'
		view:
			label: 'Total Duration'
			columns: 4
	external_id:
		view:
			label: 'Job ID'
			readonly: true
			columns: 2
	transaction_id:
		view:
			label: 'Transaction ID'
			readonly: true
			columns: 2
	to_number:
		model:
			type: 'text'
		view:
			columns: 2
			label: 'To Number'
			format: 'us_phone'
	retries:
		model:
			type: 'int'
			default: 0
		view:
			columns: 2
			label: 'Retries'
	to_name:
		model:
			type: 'text'
		view:
			columns: 2
			label: 'Recipient Name'
			note: 'This will be used as the recipient name on the cover page'
	from_number:
		model:
			type: 'text'
		view:
			columns: 2
			label: 'From Number'
			format: 'us_phone'
	from_name:
		model:
			type: 'text'
		view:
			columns: 2
			label: 'Sender Name'
			note: 'This will be used as the sender name on the cover page'
	message:
		model:
			type: 'text'
		view:
			label: 'Message'
			note: 'This will be used as comment on the cover page'
	fax_quality:
		model:
			source: ['Low', 'Standard', 'HD']
			default: 'Standard'
		view:
			label: 'Fax Quality'
			columns: 3
	user_id:
		model:
			source: 'user'
			template: '{{user.id}}'
		view:
			label: 'User'
			readonly: true
					
	embed_document:
		model:
			multi: true
			sourcefilter:
				source:
					'static': 'Fax'
				fax_id:
					'dynamic': '{fax_id}'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					'source': 'Fax'
					'fax_id': '{fax_id}'					
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'
			note: 'Documents assigned to this fax'
	status_message:
		model:
			type: 'text'
		view:
			columns: 2
			label: 'Status Message'
			readonly: true
						
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin', 'cm', 'cma', 'nurse', 'pharm']
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
	bundle: ['manage']
	name: '{created_on} - {direction} {status} {to_number} {from_number}'
	indexes:
		many: [
			['user_id']
			['to_number']
			['from_number']
			['direction']
			['status']
		]
		unique: [
			['external_id']
		]
	prefill:
		user:
			link:
				id: 'id'
						
	sections:
		'Fax Info':
			hide_header: true
			fields: ['direction']
		'Status':
			fields: ['status', 'scheduled_dt', 'status_message','delivered_dt','sent_dt', 'received_dt','retries', 'error_code', 'error_message']
		'Details':
			fields: ['site_id', 'to_number', 'to_name', 'from_number', 'from_name','fax_quality', 'user_id', 'total_pages', 'total_duration','external_id', 'transaction_id']
		'Documents':
			fields: ['embed_document']
						
view:
	dimensions:
        width: '80%'
        height: '80%'
	comment: 'Manage > Faxes'
	find:
		basic: ['site_id', 'direction', 'status', 'to_number', 'from_number', 'sent_dt', 'received_dt']
	grid:
		fields: ['created_on', 'site_id', 'direction', 'status', 'to_number', 'from_number', 'sent_dt', 'received_dt']
		sort: ['-created_on', '-sent_dt', '-received_dt']
	label: 'Faxes'