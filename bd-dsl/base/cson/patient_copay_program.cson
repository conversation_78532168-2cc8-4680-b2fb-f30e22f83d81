fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	program:
		model:
			required: true
		view:
			columns: 3
			label: 'Program/Non-Profit Name'

	contact_name:
		view:
			columns: 3
			label: 'Contact Name'

	contact_phone:
		model:
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Contact Phone'

	last_contact:
		model:
			type: 'datetime'
			required: true
		view:
			columns: 3
			label: 'Last Contact Date/Time'
			template: '{{now}}'

	reference:
		view:
			columns: 3
			label: 'Reference #'

	copay_amount:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			columns: 3
			class: 'numeral'
			format:'$0,0.00'
			label: 'Copay Amount'

	status:
		model:
			required: true
			source: ['Pending', 'Approved', 'Denied']
			default: 'Pending'
		view:
			columns: 3
			class: 'status'
			control: 'radio'
			label: 'Status'

	date:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Enrollment Date'

	renewal_date:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Renewal Date'

	approved_amount:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			columns: 3
			class: 'numeral'
			format:'$0,0.00'
			label: 'Amount Approved'

	assistance_type:
		model:
			required: true
			source: ['Rent', 'Transportation', 'Copay', 'Nursing', 'Other']
		view:
			columns: 2
			control: 'radio'
			label: 'Assistance Type'

	manufacturer:
		model:
			source: 'list_manufacturer'
			sourceid: 'code'
		view:
			columns: 3
			control: 'select'
			label: 'Manufacturer'

	bin:
		view:
			columns: 3
			label: 'BIN'

	pcn:
		view:
			columns: 3
			label: 'PCN'

	group_no:
		view:
			columns: 3
			label: 'Group #'

	id_no:
		view:
			columns: 3
			label: 'ID #'

	notes:
		view:
			control: 'area'
			label: 'Notes'

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['billing']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['program', 'reference', 'status']
	sections:
		'Patient Co-pay Assistance Program':
			fields: ['program', 'id_no', 'group_no', 'approved_amount', 'copay_amount', 'reference', 'contact_name', 'contact_phone', 'last_contact',
			'manufacturer', 'bin', 'pcn', 'status', 'date', 'renewal_date', 'assistance_type', 'notes']
	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Patient Co-pay Assistance Program"
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Patient Co-pay Assistance Program'
	grid:
		fields: ['program', 'status', 'date', 'renewal_date', 'approved_amount']
		sort: ['-created_on']
	label: 'Patient Co-pay Assistance Program'
	open: 'read'
