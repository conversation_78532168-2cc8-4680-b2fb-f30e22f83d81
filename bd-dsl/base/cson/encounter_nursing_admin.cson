
fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'

	# Admin Step
	time_taken:
		model:
			required: true
			type: 'time'
		view:
			label: 'Time Started'
			template: '{{now}}'

	comment:
		model:
			max: 4096
		view:
			control: 'area'
			label: "Administration Step Comment"

	# Pre-infusion Flush
	pre_flush_given:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pre_flush_type', 'pre_flush_vol', 'pre_flush_comment']
		view:
			control : 'radio'
			label: 'Was a pre-infusion flush given?'

	pre_flush_type:
		model:
			max: 32
			multi: true
			source:
				['Heparin', 'Sterile Water (SW)','Normal Saline (NS)', 'Sterile Saline', 'D5W','D5NS', 'Other']
			if:
				'Other':
					fields: ['pre_flush_type_other']
		view:
			control : 'checkbox'
			note: 'Select all that apply'
			label: 'Flush Type'

	pre_flush_type_other:
		view:
			label: 'Pre-infusion flush other'

	pre_flush_vol:
		model:
			type: 'decimal'
			min: 0
		view:
			label: 'Pre-infusion (mls)'

	pre_flush_comment:
		view:
			label: 'Pre-infusion comment'

	# Post infusion flush
	post_flush_given:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['post_flush_type', 'post_flush_vol', 'post_flush_comment']
		view:
			control : 'radio'
			label: 'Was a post-infusion flush given?'

	post_flush_type:
		model:
			max: 32
			multi: true
			source:['Heparin','Sterile Water (SW)','Normal Saline (NS)','D5W','D5NS','Other']
			if:
				'Other':
					fields: ['post_flush_type_other']
		view:
			control : 'checkbox'
			note: 'Select all that apply'
			label: 'Post-infusion Type'

	post_flush_type_other:
		view:
			label: 'Post-infusion flush other'

	post_flush_vol:
		model:
			type: 'decimal'
			min: 0
		view:
			label: 'Post-infusion (mls)'

	post_flush_comment:
		view:
			label: 'Post-infusion comment'

	# Nursing Admin
	drug_brand:
		model:
			prefill: ['encounter_nursing_admin']
			max: 32
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Drug Brand'

	nursing_dose:
		model:
			type: 'decimal'
			min: 0
			max: 9999
		view:
			label: 'Amount Given (mg)'

	nursing_drug_details:
		model:
			subfields:
				lot:
					label: 'Drug Lot#'
				expiration:
					label: 'Expiration Date'
					type: 'date'
			type: 'json'
		view:
			control: 'grid'
			label: 'Drug/Vial Details'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true


model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_nursing_admin:
			link:
				careplan_id: 'careplan_id'
			max: 'id'
	name: '{time_taken} : {drug_brand} {nursing_dose}mg'
	sections:
		'Nursing Admin Step':
			fields: ['time_taken']
		'Nursing Pre-Infusion Flush':
			fields: ['pre_flush_given', 'pre_flush_type', 'pre_flush_type_other', 'pre_flush_vol', 'pre_flush_comment']
			prefill: 'encounter_nursing_admin'
		'Nursing Administration':
			note: 'Check the drug label with the order before administering'
			fields: ['drug_brand', 'nursing_dose', 'nursing_drug_details']
		'Nursing Post-Infusion Flush':
			fields: ['post_flush_given', 'post_flush_type', 'post_flush_type_other', 'post_flush_vol', 'post_flush_comment']
			prefill: 'encounter_nursing_admin'
		'Nursing Admin Step Comment':
			fields: ['comment']
view:
	comment: 'Patient > Careplan > Encounter > Nursing > Admin'
	grid:
		fields: ['time_taken', 'drug_brand',  'nursing_dose']
	label: 'Nursing Admin Step'
