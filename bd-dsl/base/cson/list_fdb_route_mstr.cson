#TABLE: RPEIRM0_RT_MSTR
fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#RT_ID
	rt_id:
		model:
			type: 'int'
		view:
			label: 'Route Identifier'
			findunique: true
			columns: 3

	rt_desc_short:
		model:
			type: 'text'
		view:
			label: 'Route Short Description'
			columns: 3

	rt_desc_long:
		model:
			type: 'text'
		view:
			label: 'Route Long Description'
			columns: 3

	rt_adverb:
		model:
			type: 'text'
		view:
			label: 'Route Adverb'
			columns: 3

	dr2_rt:
		view:
			label: 'DRCM Route of Administration Indicator'
			columns: 3

	drcm_screen_rt_id:
		model:
			type: 'text'
		view:
			label: 'DRCM Screen Route Identifier'
			columns: 3

	poem_rt_id:
		model:
			type: 'int'
		view:
			label: 'POEM Route Identifier'
			columns: 3

	med_route_id:
		model:
			type: 'int'
		view:
			label: 'MED Route Identifier'
			columns: 3

	ovw_clinical_rt_id:
		model:
			type: 'int'
		view:
			label: 'OrderKnowledge Clinical Route Identifier'
			columns: 3

	gcrt:
		model:
			type: 'text'
		view:
			label: 'Generic Clinical Route Identifier'
			columns: 3

	disc_rt_ind:
		model:
			type: 'int'
		view:
			label: 'Discretionary Route Indicator'
			columns: 3

	disc_rt_admin_text:
		model:
			type: 'text'
		view:
			label: 'Discretionary Route Administration Text'
			columns: 3


	valid_cns_rt_ind:
		model:
			type: 'int'
		view:
			label: 'Valid Central Nervous System Route Indicator'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['rt_desc_short']
	indexes:
		many: [
			['rt_id']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['rt_id', 'rt_desc_short', 'rt_desc_long', 'rt_adverb', 'dr2_rt', 'drcm_screen_rt_id',
			'poem_rt_id', 'med_route_id', 'ovw_clinical_rt_id', 'gcrt', 'disc_rt_ind',
			'disc_rt_admin_text', 'valid_cns_rt_ind']

view:
	comment: 'Manage > List FDB Route of Administration Master'
	find:
		basic: ['rt_id', 'rt_desc_short']
	grid:
		fields: ['rt_id', 'rt_desc_short', 'rt_desc_long']
	label: 'List FDB Route of Administration Master'
