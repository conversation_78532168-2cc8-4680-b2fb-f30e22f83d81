fields:

	group_id:
		model:
			max: 15
		view:
			columns: 3
			note: '301-C1'
			label: 'Group Number'
			readonly: true

	plan_id:
		model:
			max: 8
		view:
			columns: 3
			note: '524-FO'
			label: 'Insurance ID'
			readonly: true

	network_reimbursement_id:
		view:
			columns: 3
			note: '545-2F'
			label: 'Net Reimbursement ID'
			readonly: true

	payer_id_qualifier:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '568-J7'
		view:
			columns: 3
			note: '568-J7'
			label: 'Payer ID Qual'
			readonly: true

	payer_id:
		view:
			columns: 3
			note: '569-J8'
			label: 'Payer ID'
			readonly: true

	mcd_id_no:
		view:
			columns: 3
			note: '115-N5'
			label: 'MCD ID #'
			readonly: true

	mcd_agcy_no:
		view:
			columns: 3
			note: '116-N6'
			label: 'MCD Agency #'
			readonly: true

	card_holder_id:
		model:
			max: 20
		view:
			columns: 3
			note: '302-C2'
			label: 'Cardholder #'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['group_id', 'plan_id', 'network_reimbursement_id']
	sections:
		'Insurance':
			hide_header: true
			indent: false
			fields: ['group_id', 'plan_id', 'network_reimbursement_id', 'payer_id_qualifier',
			'payer_id', 'mcd_id_no', 'mcd_agcy_no', 'card_holder_id']

view:
	dimensions:
		width: '65%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Response Insurance'
	grid:
		fields: ['group_id', 'plan_id', 'network_reimbursement_id', 'payer_id_qualifier', 'payer_id', 'mcd_id_no', 'card_holder_id']
		sort: ['-created_on']
	label: 'Response Insurance'
	open: 'read'