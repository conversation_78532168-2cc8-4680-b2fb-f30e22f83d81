#TABLE RGCNSTR0_INGREDIENT_STRENGTH

fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#GCN_SEQNO
	gcn_seqno:
		model:
			type: 'int'
		view:
			label: 'GCN Sequence Number'
			readonly: true
			findunique: true
			columns: 3

	#HIC_SEQN 
	hic_seqn:
		model:
			type: 'int'
		view:
			label: 'HIC Sequence Number'
			readonly: true
			columns: 3

	#STRENGTH_STATUS_CODE 
	strength_status_code:
		model:
			type: 'int'
		view:
			label: 'Strength Status Code'
			readonly: true
			columns: 3

	#STRENGTH 
	strength:
		model:
			type: 'decimal'
		view:
			label: 'Strength'
			readonly: true
			columns: 3

	#STRENGTH_UOM_ID  
	strength_uom_id:
		model:
			type: 'int'
		view:
			label: 'Strength Unit of Measure ID'
			readonly: true
			columns: 3

	#STRENGTH_TYP_CODE  
	strength_typ_code:
		model:
			type: 'int'
		view:
			label: 'Strength Type Code'
			readonly: true
			columns: 3

	#VOLUME  
	volume:
		model:
			type: 'decimal'
		view:
			label: 'Volume'
			readonly: true
			columns: 3

	#VOLUME_UOM_ID
	volume_uom_id:
		model:
			type: 'int'
		view:
			label: 'Volume Unit of Measure ID'
			readonly: true
			columns: 3

	#ALT_STRENGTH 
	alt_strength:
		model:
			type: 'decimal'
		view:
			label: 'Alternative Strength'
			readonly: true
			columns: 3

	#ALT_STRENGTH_UOM_ID  
	alt_strength_uom_id:
		model:
			type: 'int'
		view:
			label: 'Alternative Strength Unit of Measure ID'
			readonly: true
			columns: 3

	#ALT_STRENGTH_TYP_CODE  
	alt_strength_typ_code:
		model:
			type: 'int'
		view:
			label: 'Alternative Strength Type Code'
			readonly: true
			columns: 3

	#TIME_VALUE  
	time_value:
		model:
			type: 'decimal'
		view:
			label: 'Time Value'
			readonly: true
			columns: 3

	#TIME_UOM_ID
	time_uom_id:
		model:
			type: 'int'
		view:
			label: 'Time Unit of Measure ID'
			readonly: true
			columns: 3

	#RANGE_MAX
	range_max:
		model:
			type: 'decimal'
		view:
			label: 'Range Maximum'
			readonly: true
			columns: 3

	#RANGE_MIN
	range_min:
		model:
			type: 'decimal'
		view:
			label: 'Range Minimum'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['gcn_seqno', 'hic_seqn', 'strength', 'strength_uom_id']
	indexes:
		many: [
			['gcn_seqno']
			['hic_seqn']
			['strength_uom_id']
			['volume_uom_id']
			['alt_strength_uom_id']
			['time_uom_id']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['gcn_seqno', 'hic_seqn', 'strength', 'strength_uom_id', 'strength_typ_code',
			'volume', 'volume_uom_id', 'alt_strength', 'alt_strength_uom_id', 'alt_strength_typ_code',
			'time_value', 'time_uom_id', 'range_max', 'range_min']

view:
	comment: 'Manage > List FDB Clinical Ingredient Strength Table'
	find:
		basic: ['gcn_seqno', 'hic_seqn', 'strength', 'strength_uom_id']
	grid:
		fields: ['gcn_seqno', 'hic_seqn', 'strength', 'strength_uom_id']
	label: 'List FDB Clinical Ingredient Strength Table'
