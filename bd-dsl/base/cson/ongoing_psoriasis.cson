fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	palm_cover_cnt:
		model:
			type: 'int'
			min: 1
			max: 100
		view:
			label: 'If you had to take the palm of your hand and cover up all of the patches of psoriasis on your body today, how many palms of your hand do you think that it would take?'
			note: 'One palm of your hand is equal to about 1% of your body surface area (BSA). If your psoriasis is only scattered small dots, try to imagine combining them together into one patch. Please remember to include your scalp and back if affected. Do not include areas in which psoriasis has faded, leaving only changes in the color of the skin.'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		ongoing_psoriasis:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Followup - Psoriasis':
			fields: ['palm_cover_cnt']
view:
	comment: 'Patient > Careplan > Ongoing > Followup - Psoriasis'
	grid:
		fields: ['palm_cover_cnt']
	label: 'Ongoing Assessment: Followup - Psoriasis'
	open: 'read'
