fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	on_anticoag:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['anticoag_brand_id', 'anticoag_physician']
		view:
			control: 'radio'
			label: 'Is patient on any anti-coagulants (blood thinners)?'

	anticoag_brand_id:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
			if:
				'Heparin':
					fields: ['anticoag_heparin_warning']
				'Coumadin':
					fields: ['anticoag_coumadin_warning']
		view:
			label: 'Name of anti-coagulants'

	anticoag_heparin_warning:
		model:
			default: 'WARNING, use of intravenous unfractionated heparin sodium is contraindicated for 120 hours (5 days) after ORBACTIV administration'
		view:
			control: 'area'
			label: 'Heparin Warning'
			readonly: true

	anticoag_coumadin_warning:
		model:
			default: 'Concomitant warfarin use: ORBACTIV has been shown to artificially prolong PT/INR for up to 12 hours.  Patients should be monitored for bleeding if concomitantly receiving ORBACTIV and warfarin. In addition, ORBACTIV has been shown to artificially prolong aPTT for up to 120 hours, and may prolong PT and INR for up to 12 and ACT for up to 24 hours. For patients who require aPTT monitoring within 120 hours of dosing, consider a non-phospholipid dependent coagulation test such as a Factor Xa chromogenic) assay or an alternative anticoagulant not requiring aPTT.'
		view:
			control: 'area'
			label: 'Coumadin Warning'
			readonly: true

	anticoag_physician:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Has physician been notified?'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	name: ['careplan_id', 'order_id']
	sections:
		'Anti-Coagulants Risk Assessment':
			fields: ['on_anticoag', 'anticoag_brand_id', 'anticoag_heparin_warning', 'anticoag_coumadin_warning', 'anticoag_physician']

view:
	comment: 'Comorbid Condition > Anti-Coagulants Risk'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Anti-Coagulants Risk'
	open: 'read'
