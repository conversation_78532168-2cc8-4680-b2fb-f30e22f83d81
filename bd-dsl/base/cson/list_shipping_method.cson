fields:
	code:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 3

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 3

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['lists']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['code']
		]
	name: ['code']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'allow_sync', 'active']

view:
	comment: 'Manage > Shipping Method'
	find:
		basic: ['code', 'allow_sync', 'active']
	grid:
		fields: ['code', 'allow_sync', 'active']
		sort: ['code']
	label: 'Shipping Method'
	open: 'read'
