
fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	rems_enrolled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'No':
					note: 'Close assessment and notify physician'
				'Yes':
					sections: ['Lemtrada Labs', 'Lemtrada Risks', 'Lemtrada Questionnaire', 'Lemtrada Pharmacist Checklist']
		view:
			control: 'radio'
			label: 'Patient enrolled in REMS verified by calling ************?'

	cbc_reviewed:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'CBC/Diff Reviewed?'

	thyroid_reviewed:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Thyroid panel with TSH Reviewed?'

	scr_reviewed:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'sCr level Reviewed?'

	reviewed_reactions:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Pharmacist has explained to patient the risk of infusion reactions and how to report/react?'

	reviewed_thyroid_risk:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Has Pharmacist Counseled patient on increased risk of malignancies, especially skin and Thyroid?'

	ordered_prophylaxis:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['prophylaxis_dispensed_rx']
		view:
			control: 'radio'
			label: 'Has herpetic prophylaxis been ordered and patient counseled on importance of adherence to viral prophylaxis?'

	prophylaxis_dispensed_rx:
		model:
			required: true
		view:
			label: 'Where was herpetic prophylaxis dispensed from?'

	had_vaccinations:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Is MD aware and has patient been informed of the risks?'
		view:
			control: 'radio'
			label: 'Have you had any vaccinations within the past 2 weeks?'

	checklist_rems_form_received:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Lemtrada REMS Prescription ordering form received?'

	checklist_confirmed_failed_therapies:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Pharmacist has confirmed diagnosis of relapsing MS with 2 prior failed therapies?'

	checklist_rems_certified:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Prescriber has been Lemtrada REMS certified?'

	checklist_solumedrol_dispensed:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Solu-medrol ordered and dispensed?'

	checklist_benadryl_dispensed:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Benadryl and Tylenol ordered and dispensed?'

	checklist_patient_guide:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Patient provided with copy of Lemtrada Patient Guide?'

	checklist_infusion_center:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Infusion will only be dispensed to Lemtrada REMS certified infusion center. Not to patient.'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections_group: [
		'Lemtrada Questionnaire':
			sections: [
				'REMS Enrollment':
					fields: ['rems_enrolled']
				'Lemtrada Labs':
					note: 'Pharmacist must review the following labs completed <30 days prior to Lemtrada scheduled infusion'
					fields: ['cbc_reviewed', 'thyroid_reviewed', 'scr_reviewed']
				'Lemtrada Risks':
					fields: ['reviewed_reactions', 'reviewed_thyroid_risk', 'ordered_prophylaxis', 'prophylaxis_dispensed_rx']
				'Lemtrada Questionnaire':
					fields: ['had_vaccinations']
				'Lemtrada Pharmacist Checklist':
					fields: ['checklist_rems_form_received', 'checklist_confirmed_failed_therapies',
					'checklist_rems_certified', 'checklist_solumedrol_dispensed',
					'checklist_benadryl_dispensed', 'checklist_patient_guide', 'checklist_infusion_center']
			]
		]
view:
	comment: 'Patient > Careplan > Assessment > Lemtrada'
	grid:
		fields: ['created_on', 'updated_on', 'created_by']
	label: 'Assessment Questionnaire: Lemtrada'
	open: 'read'
