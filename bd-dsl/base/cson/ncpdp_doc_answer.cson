fields:
	question_number_letter:
		model:
			max: 3
			required: true
		view:
			reference: '378-4B'
			note: '378-4B'
			label: 'Question Number/Letter'

	#TODO: One of the below is required 
	question_percent_response:
		model:
			max: 100.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 3
			reference: '379-4D'
			note: '379-4D'
			label: 'Percent'
			class: 'numeral'
			format: 'percent'

	question_date_response:
		model:
			type: 'date'
		view:
			columns: 3
			reference: '380-4G'
			note: '380-4G'
			label: 'Date'
			validate: [
				name: 'Date<PERSON>ali<PERSON>tor'
				require: 'past'
			]

	question_dollar_amount_response:
		model:
			type: 'decimal'
			max: 999999999.99
			rounding: 0.01
		view:
			columns: 3
			reference: '381-4H'
			note: '381-4H'
			label: 'Dollar'
			class: 'numeral money'
			format: '$0,0.00'

	question_numeric_response:
		model:
			type: 'decimal'
			max: 99999999999
			rounding: 1
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 3
			reference: '382-4J'
			note: '382-4J'
			label: 'Numeric'

	question_alphanumeric_response:
		model:
			max: 30
		view:
			columns: 3
			reference: '383-4K'
			note: '383-4K'
			label: 'Text'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['question_number_letter', 'question_date_response', 'question_dollar_amount_response']
	sections:
		'Answer':
			hide_header: true
			indent: false
			fields: ['question_number_letter', 'question_percent_response', 'question_date_response', 'question_dollar_amount_response',
			'question_numeric_response', 'question_alphanumeric_response']
view:
	dimensions:
		width: '65%'
		height: '65%'
	hide_cardmenu: true
	validate: [
		{
			name: "RequiredAny"
			source_field: 'question_number_letter'
			if:
				'*': ['question_percent_response', 'question_date_response', 'question_dollar_amount_response', 'question_numeric_response', 'question_alphanumeric_response']
		}
	]
	comment: 'Answer'
	grid:
		fields: ['question_number_letter', 'question_percent_response', 'question_date_response', 'question_dollar_amount_response', 'question_numeric_response', 'question_alphanumeric_response']
		sort: ['-created_on']
	label: 'Answer'
	open: 'read'