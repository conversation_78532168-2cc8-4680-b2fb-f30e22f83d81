fields:

	unlock_reason_id:
		model:
			required: true
			source: 'list_unlock_reason'
		view:
			label: 'Reason Unlocked'

	signature:
		model:
			required: true
			type: 'json'
		view:
			note: 'I confirm to unlock the accounting period in question'
			control: 'esign'
			label: 'E-Signature'

	note:
		view:
			control: 'area'
			label: 'Notes'
model:
	access:
		create:     []
		create_all: ['admin', 'cm']
		delete:     []
		read:       ['admin', 'cm']
		read_all:   ['admin', 'cm']
		request:    []
		update:     ['cm']
		update_all: ['admin']
		write:      ['admin', 'cm']

	reportable: true
	name: ['unlock_reason_id', 'created_on']
	sections:
		'Period Closing Unlock':
			fields: ['unlock_reason_id', 'signature','note']
view:
	comment: 'Period Closing Unlock Log'
	find:
		basic: ['unlock_reason_id']
	grid:
		fields: ['created_on','created_by','unlock_reason_id','note']
		sort: ['-created_on']
	label: 'Period Closing Unlock Log'
