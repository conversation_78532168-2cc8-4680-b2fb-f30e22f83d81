fields:
	event_name:
		model:
			type: 'text'
		view:
			label: 'Event Name'
	event_tracer:
		model:
			type: 'json'
		view:
			label: 'Tracer'
	user_id:
		model:
			type: 'int'
		view:
			label: 'User ID'
	user_name:
		model:
			type: 'text'
		view:
			label: 'User Name'
	form_name:
		model:
			type: 'text'
		view:
			label: 'Form Name'
	form_id:
		model:
			type: 'int'
		view:
			label: 'Form ID'
	data_diff:
		model:
			type: 'json'
		view:
			label: 'Form Changes'
	fields:
		model:
			type: 'json'
		view:
			label: 'Fields Changed'
	patient_id:
		model:
			type: 'int'
		view:
			label:'Patient ID'
	patient_name:
		model:
			type: 'text'
		view:
			label: 'Patient Name'
	fields:
		model:
			type: 'json'
		view:
			control: 'area'
			label: 'Changes'
	created_on:
		view:
			label: 'Change Date/Time'
model:
	save: false
	bundle: ['audit']
	sections:
		'Audit Event':
			fields: ['created_on', 'user_name', 'patient_name', 'form_name', 'fields']
	access:
		create: []
		create_all: []
		delete: []
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request: []
		review: []
		update: []
		update_all: []
		write: []
	name:['event_name', 'user_name', 'form_name', 'patient_name']
view:
	find:
		basic: ['event_name', 'user_name', 'form_name', 'patient_name']
		advanced: ['user_id', 'form_id', 'patient_id']
	grid:
		fields: ['created_on', 'event_name', 'user_name', 'form_name', 'patient_name']
		sort: ['-created_on']
	label: 'Audit'
	open: 'read'
