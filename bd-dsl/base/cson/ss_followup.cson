fields:

	#Header.MessageID
	message_id:
		view:
			label: 'MessageID'
			readonly: true
			offscreen: true

	message_status:
		model:
			default: 'Pending'
			source: ['Pending', 'Sent', 'Verified', 'Error']
			if:
				'Error':
					fields: ['error_code_id', 'error_desc_code_id', 'error_description']
		view:
			class: 'status'
			label: 'Message Status'
			readonly: true

	error_code_id:
		view:
			columns: 3
			label: 'Error Code'
			readonly: true

	error_desc_code_id:
		view:
			columns: 3
			label: 'Error Description Code'
			readonly: true

	error_description:
		view:
			columns: 3
			label: 'Error Description'
			readonly: true

	#Body.<MessageType>.FollowUpRequest
	value:
		model:
			type: 'int'
		view:
			columns: 2
			label: 'Follow-up #'
			readonly: true

	sent_dt:
		model:
			type: 'datetime'
		view:
			columns: 2
			label: 'Sent Date/Time'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: ['message_id', 'message_status', 'sent_dt']
	sections:
		'Follow-up Message':
			fields: ['message_id', 'message_status', 'error_code_id', 'error_desc_code_id', 'error_description', 'value', 'sent_dt']
view:
	hide_cardmenu: true
	comment: 'SureScripts Follow-up Message'
	grid:
		fields: ['message_id', 'value', 'sent_dt']
		sort: ['-created_on']
	label: 'Follow-up Message'
	open: 'read'
