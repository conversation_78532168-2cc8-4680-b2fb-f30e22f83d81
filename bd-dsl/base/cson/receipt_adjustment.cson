fields:

	receipt_no:
		view:
			label: 'Receipt No'
			readonly: true
			offscreen: true
			findunique: true

	receipt_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Receipt Date'
			readonly: true

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			readonly: true
			findmulti: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
			search: 'B'
		view:
			label: 'Transferred Item'
			readonly: true

	lot_id:
		model:
			required: false
			source: 'inventory_lot'
			search: 'C'
		view:
			label: 'Lot'
			readonly: true

	serial_id:
		model:
			required: false
			search: 'C'
			source: 'inventory_serial'
		view:
			label: 'Serial'
			readonly: true

	quantity:
		model:
			required: true
			search: 'C'
			rounding: 1
			type: 'decimal'
		view:
			label: 'Quantity'
			readonly: true

	reason_id:
		model:
			required: true
			search: 'C'
			source: 'list_inv_adjustment_reason'
		view:
			label: 'Reason'
			readonly: true

	void:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['voided_datetime', 'void_reason_id']
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Void Adjustment?'
			validate: [
				{
					name: 'VoidAdjustment'
				},
				{
					name: 'PrefillCurrentDateTime'
					condition:
						void: 'Yes'
					dest: 'voided_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						void: 'Yes'
					dest: 'voided_by'
				}
			]

	void_reason_id:
		model:
			required: true
			source: 'list_void_reason'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			label: 'Void Reason'

	voided_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Voided Date'
			readonly: true

	voided_by:
		model:
			source: 'user'
		view:
			label: 'Voided By'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin','pharm','csr','cm','nurse']
		read_all:   ['admin','pharm','csr','cm','nurse']
		request:    []
		review:     []
		update:     []
		update_all: ['admin','pharm','csr','cm']
		write:      ['admin','pharm','csr','cm']
	bundle: ['inventory']
	indexes:
		many: [
			['site_id']
			['inventory_id']
			['lot_id']
			['serial_id']
			['receipt_no']
		]
	name: ['receipt_no', 'receipt_date', 'inventory_id']
	sections:
		'Inventory Adjustment Receipt':
			fields: ['site_id', 'inventory_id', 'lot_id', 'serial_id', 'quantity', 'reason_id']
		'Void':
			modal: true
			fields: ['void', 'voided_datetime', 'voided_by', 'void_reason_id']
view:
	hide_cardmenu: true
	block:
		update:
			if: 'void'
			except: ['empty']
	comment: 'Inventory Adjustment Receipt'
	find:
		basic: ['receipt_no', 'site_id', 'inventory_id', 'reason_id']
		advanced: ['lot_id', 'serial_id']
	grid:
		fields: ['receipt_date', 'site_id', 'inventory_id', 'lot_id', 'serial_id', 'quantity', 'reason_id']
		sort: ['-id']
	label: 'Inventory Adjustment Receipt'
	open: 'edit'