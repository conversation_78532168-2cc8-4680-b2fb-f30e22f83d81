fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: '<PERSON>rateUUID'
			]

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	nursing_agency_id:
		model:
			required: true
			source: 'patient_ancillary'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			label: 'Nursing Provider'

	contact_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Contact Date'

	time_total:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Total Visit Time (hrs)'

	travel_time_total:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Total Travel Time (hrs)'

	billing_notes:
		model:
			type: 'text'
		view:
			control: 'area'
			label: 'Billing Notes'

	pharm_notes:
		view:
			control: 'area'
			label: 'Notes to Pharmacy'

	cc_signature:
		model:
			required: false
			type: 'json'
			access:
				read: ['-crn']
				write: ['-nurse', '-cma', '-csr', '-crn']
		view:
			control: 'esign'
			label: 'Review by RN Clinical Coordinator'

	subform_intervention:
		model:
			source: 'patient_event'
			multi: true
			type: 'subform'
		view:
			label: 'Interventions/ADE'

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'encounter_agency'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Other Form'
					direct_attachment: 'Yes'
					source: 'Scanned Document'
					form_name: 'encounter_agency'
					form_code: '{code}'
					form_filter: 'encounter_agency'
					patient_id: '{patient_id}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_agency:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	access:
		create:     ['admin', 'pharm', 'csr', 'cm', 'nurse']
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm', 'csr', 'nurse']
		read:       ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm', 'liaison', 'crn', 'patient','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm', 'liaison','physician']
		request:    []
		update:     ['admin', 'pharm', 'csr', 'cm', 'nurse','liaison', 'crn','physician']
		update_all: ['admin', 'pharm','physician']
		write:      ['admin', 'pharm', 'csr', 'cm', 'nurse','liaison', 'crn','physician']
	name: ['order_id', 'nursing_agency_id']
	indexes:
		many: [
			['nursing_agency_id']
		]
		unique: [
			['code']
		]
	sections_group: [
		'Main':
			sections: [
				'Visit Info':
					hide_header: true
					indent: false
					fields: ['code', 'nursing_agency_id', 'contact_date']
					tab: 'Visit'

				'Billing Details':
					indent: false
					fields: ['time_total', 'travel_time_total', 'billing_notes']
					tab: 'Visit'

				'Review Signatures':
					indent: false
					fields: ['pharm_notes', 'cc_signature']
					tab: 'Visit'

				'Interventions/ADE':
					indent: false
					note: 'Document any ADEs or Catheter related event as an intervention'
					fields: ['subform_intervention']
					tab: 'Visit'

				'Documents':
					hide_header: true
					indent: false
					fields: ['embed_document']
					tab: 'Assigned Documents'
			]
	]

view:
	comment: 'Patient > Careplan > Agency Encounter'
	grid:
		fields: ['created_by', 'contact_date', 'nursing_agency_id', 'time_total', 'travel_time_total', 'time_total', 'pharm_notes']
		sort: ['-id']
	label: 'Patient Agency Encounter'
	open: 'read'
	block:
		update:
			if: 'cc_signature'
			except: ['admin', 'empty']
