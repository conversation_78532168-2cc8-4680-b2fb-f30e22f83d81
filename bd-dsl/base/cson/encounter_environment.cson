fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Environment
	condition:
		model:
			max: 64
			required: true
			source: ['Clean', 'Unclean', 'Cluttered']
		view:
			control: 'radio'
			label: 'Environment Condition'
			columns: 3

	pets:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Pets?'
			columns: 3

	storage:
		model:
			max: 4
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Room temperature storage area appropriate and clean?'
			columns: 3

	wkspc:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Workspace?'
			columns: 3

	util:
		model:
			max: 24
			multi: true
			source: ['Clean Water', 'Electricity', 'Heat', 'Telephone', 'Refrigerator']
		view:
			control: 'checkbox'
			note: 'Select any that apply'
			label: 'Utilities'
			columns: 3

	safe:
		model:
			max: 3
			source: ['No', 'Yes']
			required: true
		view:
			control: 'radio'
			label: 'Safe Home?'
			columns: 3

	smoke:
		model:
			max: 3
			source: ['No', 'Yes', 'N/A']
			required: true
		view:
			control: 'radio'
			label: 'Working Smoke Alarms?'
			columns: 3

	care:
		model:
			max: 3
			source: ['No', 'Yes', 'N/A']
			required: true
		view:
			control: 'radio'
			label: 'Caregiver capable and willing?'
			columns: 3

	cand:
		model:
			max: 4
			required: true
			source: ['Poor', 'Fair', 'Good']
		view:
			control: 'radio'
			label: 'Overall candidacy for home care'
			columns: 3

	oxy:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['oxy_education', 'oxy_smokers']
		view:
			control: 'radio'
			label: 'Oxygen in the home?'
			columns: -3

	oxy_education:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Education given regarding open flames?'
			columns: 3

	oxy_smokers:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are there any smokers living in the home?'
			columns: 3

	comments:
		view:
			control: 'area'
			label: 'Environment Comments'
	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id']
	sections:
		'Environment':
			hide_header: true
			indent: false
			fields: ['condition', 'pets', 'storage', 'wkspc',
			'util', 'safe', 'oxy', 'oxy_education',
			'oxy_smokers', 'smoke', 'care', 'cand', 'comments']

view:
	comment: 'Patient > Careplan > Encounter > Environment'
	label: 'Patient Encounter: Environment'
