fields:

	submitter_id:
		view:
			columns: 4
			label: 'Submitter ID'
			readonly: true

	sender_id:
		view:
			columns: 4
			label: 'Sender ID'
			readonly: true

	biller_id:
		view:
			columns: 4
			label: 'Biller ID'
			readonly: true

	trace_id:
		view:
			columns: 4
			label: 'Trace ID'
			note: 'Unique Id Assigned by Optum'
			readonly: true

	application_mode:
		view:
			columns: 4
			label: 'Application Mode'
			note: 'Used by Optum to identify where request can be found for support'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['submitter_id', 'sender_id', 'biller_id', 'trace_id']
	sections:
		'Claim Response Meta Data':
			hide_header: true
			fields: ['submitter_id', 'sender_id', 'biller_id',
			'trace_id', 'application_mode']

view:
	dimensions:
		width: '55%'
		height: '55%'
	hide_cardmenu: true
	comment: 'Claim Response Meta Data'
	grid:
		fields: ['submitter_id', 'sender_id', 'biller_id',
			'trace_id', 'application_mode']
		sort: ['-created_on']
	label: 'Claim Response Meta Data'
	open: 'read'