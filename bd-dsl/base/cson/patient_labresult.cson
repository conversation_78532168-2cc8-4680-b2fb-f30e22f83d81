fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	# Draw Received
	draw_date:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Draw Date'

	type:
		model:
			source: ['Components', 'Boolean']
			if:
				'Components':
					fields:['components']
				'Boolean':
					fields:['result']
		view:
			label: 'Type'
			offscreen: true

	# Draw Receive
	receive_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 3
			label: 'Receive Date'

	# Lab
	lab_id:
		model:
			source: 'labresult'
			required: true
		view:
			columns: 3
			control: 'select'
			label: 'Lab'
			validate: [
					name: 'LabValidate'
			]

	result:
		model:
			source: ['Positive', 'Negative', 'Other']
			if:
				'Other':
					fields: ['result_other']
		view:
			columns: 3
			label: 'Result'

	result_other:
		model:
			required: true
		view:
			label: 'Result Other'

	components:
		model:
			subfields:
				component:
					label: 'Component'
				unit:
					label: 'Unit'
				value:
					label: 'Value'
					type: 'decimal'
				range:
					label: 'Range'
				comment:
					label: 'Comment'
			type: 'json'
		view:
			control: 'grid'
			label: 'Result Components'

	result_comments:
		view:
			control: 'area'
			label: 'Result Comments'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy_external_id'
			readonly: true
			offscreen: true

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'patient_labresult'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Other Form'
					direct_attachment: 'Yes'
					form_code: '{code}'
					form_name: 'patient_labresult'
					source: 'Scanned Document'
					type_id: 'Lab Result'
					patient_id: '{patient_id}'
					form_filter: 'patient_labresult'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['lab_id']
			['order_id']
			['patient_id']
		]
		unique: [
			['code']
		]
	bundle: ['patient']
	name: ['patient_id', 'order_id', 'lab_id', 'result']
	sections_group: [
		'Lab':
			hide_header: true
			indent: false
			fields: ['code','draw_date', 'receive_date', 'lab_id']
			tab: 'Results'
		'Results':
			indent: false
			fields: ['components', 'result', 'result_other', 'result_comments']
			tab: 'Results'
		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'
	]

view:
	hide_cardmenu: true
	validate: [
		{
			name: "DateOrderValidator"
			fields: [
				"draw_date",
				"receive_date"
			]
			error: "Receive Date cannot be lesser than Draw Date"
		}
	]
	comment: 'Patient > Lab Result'
	find:
		basic: ['receive_date', 'lab_id']
	grid:
		fields: ['receive_date', 'lab_id', 'result_comments']
		sort: ['receive_date', 'lab_id']
	label: 'Patient Lab Result'
	open: 'read'
