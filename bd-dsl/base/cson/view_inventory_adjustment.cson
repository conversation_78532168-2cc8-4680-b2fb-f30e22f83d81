fields:

	inventory_id:
		model:
			required: true
			source: 'inventory'
			query: 'select_inv_in_stock'
			querytemplate: 'inventoryTemplate'
			sourcefilter:
				type:
					'static': ['Drug', 'Supply', 'Equipment Rental']
				active:
					'static': 'Yes'
		view:
			columns: 3
			label: 'Adjusted Item'
			class: 'select_prefill'
			validate: [
				name: 'ValidateAdjustmentAmount'
			]
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'lot_tracking': ['lot_tracking'],
					'serial_tracking': ['serial_tracking'],
					'type': ['type']
			]

	# Prefill with current site info
	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			columns: 3
			label: 'Site'
			findmulti: true

	type:
		model:
			source: ['Drug', 'Supply', 'Equipment Rental', 'Billable']
			if:
				'Equipment Rental':
					readonly:
						fields: ['quantity']
					prefill:
						'quantity': 1
				'*':
					prefill:
						'quantity': ''
		view:
			label: 'Type'
			readonly: true
			offscreen: true

	lot_tracking:
		model:
			source: ['Yes']
			if:
				'Yes':
					require_fields: ['lot_id']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Track Lots?'
			readonly: true
			offscreen: true

	serial_tracking:
		model:
			source: ['Yes']
			if:
				'Yes':
					require_fields: ['serial_id']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Track Serial Numbers?'
			readonly: true
			offscreen: true

	lot_no:
		view:
			label: 'Lot No'
			readonly: true
			offscreen: true

	lot:
		model:
			save: false
			multi: true
			type: 'int'
			source: 'inventory_lot'
		view:
			label: 'Lots'
			readonly: true
			offscreen: true

	lot_id:
		model:
			required: false
			source: 'inventory_lot'
			sourcefilter:
				inventory_id:
					'dynamic': '{inventory_id}'
				site_id:
					'dynamic': '{site_id}'
				id:
					'dynamic': '{lot}'
			query: 'select_lot_in_stock'
			querytemplate: 'inventoryTemplate'
		view:
			columns: 3
			label: 'Lot'
			class: 'select_prefill'
			validate: [
				{
					name: 'ValidateAdjustmentAmount'
				},
				{
					name: 'CheckInventoryOnHand'
				}
			]
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory_lot/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'lot_no': ['lot_no']
			]

	serial:
		model:
			save: false
			multi: true
			type: 'int'
			source: 'inventory_serial'
		view:
			label: 'Serials'
			readonly: true
			offscreen: true

	serial_id:
		model:
			required: false
			source: 'inventory_serial'
			sourcefilter:
				inventory_id:
					'dynamic': '{inventory_id}'
				site_id:
					'dynamic': '{site_id}'
				lot_no:
					'dynamic': '{lot_id}'
				id:
					'dynamic': '{serial}'
			query: 'select_serial_in_stock'
			querytemplate: 'inventoryTemplate'
		view:
			columns: 3
			label: 'Serial'
			validate: [
				{
					name: 'ValidateAdjustmentAmount'
				},
				{
					name: 'CheckInventoryOnHand'
				}
			]

	quantity:
		model:
			required: true
			rounding: 1
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 3
			note: 'Negative reduce inventory, positive increase inventory'
			label: 'Quantity'
			validate: [
				name: 'ValidateAdjustmentAmount'
			]

	reason_id:
		model:
			required: true
			source: 'list_inv_adjustment_reason'
		view:
			columns: 3
			label: 'Reason'


	comments:
		view:
			control: 'area'
			label: 'Comments'

	hide_serial_lots:
		model:
			source: ['Yes']
			default: 'Yes'
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Hide out-of-stock lots/serial numbers?'
			columns: 3
			validate: [
				name: 'SetSerialAndLots'
			]

model:
	save: false
	access:
		create:     []
		create_all: ['admin','pharm','csr','cm']
		delete:     []
		read:       ['admin','pharm','csr','cm']
		read_all:   ['admin','pharm','csr','cm']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      ['admin','pharm','csr','cm']

	name: ['inventory_id', 'quantity', 'type', 'reason_id']
	sections:
		'Inventory Adjustment':
			fields: ['inventory_id', 'site_id', 'lot_tracking', 'serial_tracking',
			'lot', 'lot_id', 'serial', 'serial_id', 'quantity', 'reason_id', 'hide_serial_lots',  'comments']

view:
	comment: 'Inventory Adjustment'
	grid:
		fields: ['site_id', 'inventory_id', 'lot_id', 'serial_id', 'quantity', 'reason_id']
		sort: ['-id']
	label: 'Inventory Adjustment'
	open: 'edit'