fields:

	#Fields handling the numbering series
	last_number_used:
		model:
			type: 'int'
		view:
			label: 'Last Number Used'

	digit_length:
		model:
			type: 'int'
			required: true
		view:
			label: 'Digit Length'

	prefix:
		model:
			type: 'text'
		view:
			label: 'Prefix'
	suffix:
		model:
			type: 'text'
		view:
			label: 'Suffix'

	last_sequence_number_used:
		model:
			type: 'text'
			required: false
		view:
			label: 'Last Sequence Number Used'

	#Filters for pg fun
	series:
		model:
			type: 'text'
			required: true
		view:
			label: 'Document Type'

	start_date:
		model:
			type: 'date'
		view:
			label: 'Start Date'

	end_date:
		model:
			type: 'date'
			required: false
		view:
			label: 'End Date'

	active:
		model:
			type: 'text'
			default: 'Yes'
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['setup']
	indexes:
		unique: [
			['series']
		]
	name: ['active','series','last_sequence_number_used']
	sections:
		'Number Series':
			fields: ['series', 'last_number_used', 'digit_length','start_date','end_date','active', 'prefix', 'suffix', 'last_sequence_number_used']
view:
	comment: 'Mange > Number Series'
	find:
		basic: ['series']
	grid:
		fields: ['series', 'last_number_used']
		sort: ['series']
	label: 'Number Series'