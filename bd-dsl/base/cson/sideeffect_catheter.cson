fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	# follow-up
	injection_site_issues:
		model:
			required: false
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['injection_site_issues_details', 'catheter_issues', 'catheter_flushing']
		view:
			control: 'radio'
			label: 'Have you experienced any medication injection site reaction, pain, or inflammation?'

	injection_site_issues_details:
		view:
			control: 'area'
			label: 'Injection Site Issues'

	catheter_issues:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields:['catheter_issues_comments']
		view:
			control: 'radio'
			note: '(e.g. catheter site itching, redness, irritation,  swelling, or pain)'
			label: 'Have you had any catheter related complications?'

	catheter_issues_comments:
		model:
			required: true
		view:
			label: 'Catheter Complications'

	catheter_flushing:
		model:
			required: false
			max: 3
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['catheter_flushing_details']
		view:
			control: 'radio'
			label: 'Is your catheter flushing normally and are you able to infuse your medication normally?'

	catheter_flushing_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Catheter Flushing Issues'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['patient_id', 'order_id', 'created_on']
	sections:
		'Catheter Issues':
			fields: ['injection_site_issues', 'injection_site_issues_details', 'catheter_issues',
			'catheter_issues_comments', 'catheter_flushing', 'catheter_flushing_details']

view:
	hide_cardmenu: true
	comment: 'Side Effect > Catheter Issues'
	grid:
		fields: ['injection_site_issues', 'catheter_issues']
	label: 'Side Effect: Catheter Issues'
	open: 'read'
