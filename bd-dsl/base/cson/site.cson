fields:

	external_id:
		view:
			label: 'External ID / Site No'
			readonly: true
			offscreen: true

	name:
		model:
			max: 128
			required: true
		view:
			columns: 3
			label: 'Site Name'
			findunique:true

	logo:
		model:
			type: 'json'
		view:
			columns: 3
			control: 'file'
			label: 'Logo'
			note: 'Max 100MB. Only images are supported.'

	code:
		model:
			required: true
			max: 64
		view:
			columns: 3
			label: 'Company Code'
			findunique:true

	address1:
		model:
			required: true
			max: 35
		view:
			columns: 'addr_1'
			label: 'Address 1'
			validate: [{
				name: 'SetBillingAddress'
			}]

	address2:
		model:
			required: false
			max: 35
		view:
			columns: 'addr_2'
			label: 'Address 2'
			validate: [{
				name: 'SetBillingAddress'
			}]

	city:
		model:
			required: true
			type: 'text'
		view:
			columns: 'addr_city'
			label: 'City'
			validate: [{
				name: 'SetBillingAddress'
			}]

	state_id:
		model:
			required: true
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'State'
			validate: [{
				name: 'SetBillingAddress'
			}]

	zip:
		model:
			required: true
			max: 10
			min: 5
		view:
			columns: 'addr_zip'
			format: 'us_zip'
			label: 'Zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'zip'
						city:'city'
						state:'state'
			]
			validate: [{
				name: 'SetBillingAddress'
			}]

	phone:
		model:
			required: true
			max: 21
		view:
			columns: 2
			format: 'us_phone'
			label: 'Phone Number'

	fax:
		model:
			required: true
			max: 21
		view:
			columns: 2
			format: 'us_phone'
			label: 'Fax Number'

	ncpdp_id:
		model:
			required: true
			max: 64
		view:
			columns: 2
			label: 'NCPDP ID'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{7}$'
				error: 'Invalid NCPDP ID, must be 7 digits long'
			}]

	timezone_id:
		model:
			required: true
			source: 'list_timezone'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Timezone'

	npi:
		model:
			required: true
			max: 10
		view:
			columns: 3
			label: 'NPI'
			findunique: true
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid NPI, must be 10 digits'
			}]

	asap_id:
		view:
			columns: 3
			label: 'ASAP'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{6,20}$'
				error: 'Invalid ASAP ID'
			}]

	state_license:
		view:
			columns: 3
			label: 'State License #'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{5,20}$'
				error: 'Invalid State License Number'
			}]

	# Billing info
	bill_name:
		model:
			max: 128
		view:
			columns: 3
			label: 'Billing Site Name'

	bill_phone:
		model:
			required: false
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Billing Department Phone Number'

	bill_address1:
		model:
			required: true
			max: 35
		view:
			columns: 'addr_1'
			label: 'Address 1'

	bill_address2:
		model:
			max: 35
		view:
			columns: 'addr_2'
			label: 'Address 2'

	bill_city:
		model:
			required: true
			type: 'text'
		view:
			columns: 'addr_city'
			label: 'City'

	bill_state_id:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
			required: true
		view:
			columns: 'addr_state'
			label: 'State'

	bill_zip:
		model:
			max: 10
			min: 5
			required: true
		view:
			columns: 'addr_zip'
			format: 'us_zip'
			label: 'Zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						bill_zip:'bill_zip'
						bill_city:'bill_city'
						bill_state:'bill_state'
			]

	taxonomy_id:
		model:
			source: 'list_nucc_taxonomy'
			sourceid: 'code'
		view:
			label: 'Taxonomy'

	# Medicare 

	submit_medicare_claim:
		model:
			default: 'Yes'
			source: ['Yes']
			if:
				'Yes':
					fields: ['ccn_id', 'nsc_id', 'mcr_ptan_id', 'dme_mac_region', 'dme_mac_submitter_id']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Does site transmit Medicare claims?'

	ccn_id:
		model:
			max: 64
		view:
			columns: 2
			label: 'CCN Number (Part A)'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[0-5][0-9][0-9]{4}$'
				error: 'Invalid CCN Number, should be in the format where XX is a state code and YYYY is a facility identifier'
			}]

	nsc_id:
		model:
			max: 64
		view:
			columns: 2
			label: 'NSC'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid NSC ID, NSC IDs are typically 10-digits long'
			}]

	mcr_ptan_id:
		view:
			columns: 2
			label: 'MCR Provider # (PTAN)'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{5,12}$'
				error: 'Medicare Provider Number (PTAN), must be 5-12 alphanumeric characters"'
			}]

	dme_mac_region:
		model:
			source: ['A','B','C','D']
		view:
			columns: 2
			control: 'radio'
			label: 'DME MAC Region'

	dme_mac_submitter_id:
		model:
			required: false
			max: 64
		view:
			columns: 2
			label: 'DME MAC Submitter'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{6,15}$'
				error: 'Invalid DME MAC ID'
			}]

	# Provider info
	tax_id:
		model:
			required: true
			max: 64
		view:
			columns: 2
			label: 'Tax ID (TIN)'
			findunique: true
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{2}-\\d{7}$'
				error: 'Invalid TIN, must be nine digits, formatted as XX-XXXXXXX'
			}]

	tax_codes_id:
		model:
			multi: true
			required: true
			source: 'list_tax_code'
			sourceid: 'code'
		view:
			columns: 2
			note: 'If state is origin-based, only a single entry is required. Otherwise, all states to which pharmacy ships should be entered.'
			label: 'Tax Code ID'

	bcbs_id:
		view:
			columns: 2
			label: 'BCBS ID'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{1,10}$'
				error: 'Invalid BCBS ID, must be 1-10 alphanumeric characters'
			}]

	dea_id:
		view:
			columns: 2
			label: 'DEA'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z]{2}\\d{7}$'
				error: 'Invalid DEA number, DEA numbers must be 2 letters followed by 7 numbers'
			}]

	nabp_id:
		view:
			columns: 2
			label: 'NABP'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{6,8}$'
				error: 'Invalid NABP number, NABP numbers must be 6 or 8 digits long'
			}]

	medi_cal_pin:
		view:
			columns: 2
			label: 'Medi-Cal Pin'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{6,9}$'
				error: 'Invalid Medi-Cal Pin, must be 6-9 digits'
			}]

	pic_id:
		model:
			required: true
			source: 'user'
		view:
			columns: 2
			label: 'Site PIC'

	pic_npi:
		view:
			columns: 2
			label: 'Site PIC NPI'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid NPI, must be 10 digits'
			}]

	mm_clearinghouse:
		model:
			default: 'Change Healthcare'
			source: ['Change Healthcare']
			if:
				'Change Healthcare':
					fields: ['mm_ch_organization_name', 'mm_ch_trading_partner_id','mm_ch_trading_partner_name', 'mm_contact_name', 'mm_contact_email']
		view:
			note: 'Change Healthcare is the only medical clearinghouse supported at this time.'
			control: 'radio'
			label: 'Major Medical Clearinghouse'

	mm_ch_organization_name:
		model:
			required: true
			min: 1
			max: 60
		view:
			columns: 3
			label: 'Change Healthcare Organization Name'
			class: 'claim-field'

	mm_ch_trading_partner_id:
		model:
			min: 2
			max: 80
			required: false
		view:
			columns: 3
			label: 'Change Healthcare Trading Partner ID'
			class: 'claim-field'

	mm_ch_trading_partner_name:
		model:
			min: 1
			max: 60
			required: true
		view:
			columns: 3
			label: 'Change Healthcare Trading Partner Name'
			class: 'claim-field'

	mm_contact_name:
		model:
			min: 1
			max: 60
		view:
			columns: 2
			label: 'Claim Contact Name'
			class: 'claim-field'

	mm_contact_email:
		model:
			max: 256
			min: 1
			required: false
		view:
			columns: 2
			label: 'Claim Contact Email Address'
			reference: 'PER04 PER03 = EM'
			class: 'claim-field'
			validate: [
					name: 'EmailValidator'
			]

	pbm_clearinghouse:
		model:
			default: 'Change'
			source: ['Change', 'RedSail']
			if:
				'Change':
					fields: ['pbm_clearinghouse_username','pbm_clearinghouse_password']
		view:
			control: 'radio'
			label: 'PBM Clearinghouse'

	pbm_clearinghouse_username:
		model:
			required: true
		view:
			columns: 2
			label: 'PBM Clearinghouse Username'

	pbm_clearinghouse_password:
		model:
			max: 32
			min: 8
			type: 'password'
		view:
			columns: 2
			label: 'PBM Clearinghouse Password'

	pbm_tso:
		model:
			required: false
			max: 64
		view:
			columns: 2
			label: 'TSO #'
			class: 'claim-field'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid TSO #, TSO # must be 6-digits long'
			}]

	pbm_site_id:
		model:
			required: true
		view:
			columns: 2
			label: 'PBM Site ID'
			class: 'claim-field'

	use_ss:
		model:
			source: ['Yes']
			default: 'Yes'
			if:
				'Yes':
					sections: ['Surescripts Business Hours']
					fields: ['ss_organization_id', 'ss_organization_type',
					'ss_organization_specialty', 'ss_service_level', 'subform_hours']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Utilize Surescripts for E-Prescribing?'

	ss_organization_id:
		view:
			columns: 2
			label: 'Surescripts Organization ID'
			note: 'Automatically assigned during Surescripts setup'
			readonly: true

	ss_organization_type:
		model:
			required: true
			default: ['Retail']
			source:
				'MailOrder': 'Mail Order'
				'Retail': 'Retail'
		view:
			columns: 2
			control: 'radio'
			label: 'Surescripts Organization Type'

	ss_organization_specialty:
		model:
			required: false
			default: ['Specialty']
			multi: true
			source:
				'Specialty': 'Specialty'
				'Compounding': 'Compounding'
				'DME': 'DME'
		view:
			columns: 2
			control: 'checkbox'
			note: 'Denotes which service the pharmacy provides to the physician'
			label: 'Surescripts Organization Specialty'

	ss_service_level:
		model:
			default: ['New', 'Refill', 'Change', 'Cancel', 'ControlledSubstance']
			source: 'list_ss_service_level'
			sourceid: 'code'
			required: true
			multi: true
			sourcefilter:
				code:
					'static': ['New', 'Refill', 'Change', 'Cancel', 'ControlledSubstance']
		view:
			control: 'checkbox'
			label: 'Supported Surescripts Service Level'

	subform_hours:
		model:
			type: 'subform'
			multi: true
			required: true
			source: 'site_hours'
		view:
			grid:
				edit: true
				add: 'inline'
				fields: ['day', 'start_time', 'close_time']
				label: ['Day', 'Open', 'Close']
				width: [20, 40, 40]
			max_count: 7
			label: 'Surescripts Directory Business Hours'

	# Settings
	default_is_specialty:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			label: 'Default All Orders to Specialty?'
			columns: 4

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['npi']
			['pic_id']
			['pic_npi']
			['asap_id']
			['state_license']
			['ncpdp_id']
			['tax_id']
			['pbm_site_id']
			['mm_ch_trading_partner_id']
			['ss_organization_id']
			['bcbs_id']
			['dea_id']
			['nabp_id']
			['medi_cal_pin']
			['ccn_id']
			['nsc_id']
			['mcr_ptan_id']
			['dme_mac_region']
			['dme_mac_submitter_id']
		]
		unique: [
			['name','code']
		]
	name: ['name']
	sections_group: [
		'Site Information':
			hide_header: true
			indent: false
			fields: ['name', 'logo', 'code', 'address1', 'address2', 'city', 'state_id', 'zip',
			'phone', 'fax', 'npi', 'asap_id', 'state_license', 'timezone_id']
			tab: 'Site Information'
		'Billing Provider Information':
			hide_header: true
			indent: false
			fields: ['bill_name', 'bill_phone', 'bill_address1', 'bill_address2', 'bill_city', 'bill_state_id', 'bill_zip',
			'taxonomy_id']
			tab: 'Site Information'
		'PIC Settings':
			hide_header: true
			indent: false
			fields: ['pic_id', 'pic_npi']
			tab: 'Site Information'
		'Medicare Information':
			hide_header: true
			indent: false
			fields: ['submit_medicare_claim', 'ccn_id', 'nsc_id', 'mcr_ptan_id', 'dme_mac_region', 'dme_mac_submitter_id']
			tab: 'Billing'
		'Provider Information':
			indent: false
			fields: ['ncpdp_id', 'tax_id', 'tax_codes_id', 'bcbs_id', 'dea_id',
			'nabp_id', 'medi_cal_pin']
			tab: 'Billing'
		'Medical Clearinghouse Setup':
			hide_header: true
			indent: false
			fields: ['mm_clearinghouse', 'mm_ch_organization_name', 'mm_ch_trading_partner_id',
			'mm_ch_trading_partner_name', 'mm_contact_name', 'mm_contact_email']
			tab: 'Billing'
		'PBM Clearinghouse Setup':
			hide_header: true
			indent: false
			fields: ['pbm_clearinghouse', 'pbm_clearinghouse_username', 'pbm_clearinghouse_password', 'pbm_tso', 'pbm_site_id']
			tab: 'Billing'
		'Surescripts Setup':
			hide_header: true
			indent: false
			fields: ['use_ss', 'ss_organization_type', 'ss_organization_specialty', 'ss_organization_id', 'ss_service_level']
			tab: 'Billing'
		'Surescripts Business Hours':
			hide_header: true
			indent: false
			fields: ['subform_hours']
			tab: 'Billing'
		'Settings':
			hide_header: true
			indent: false
			fields: ['default_is_specialty']
			tab: 'Settings'
		]
view:
	hide_cardmenu: true
	comment: 'Manage > Sites'
	find:
		basic: ['name','code', 'state_id','npi','tax_id']
	grid:
		fields: ['name','code', 'city', 'state_id','npi','tax_id']
		sort: ['code']
	label: 'Sites'
