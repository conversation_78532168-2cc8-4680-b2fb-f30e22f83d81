#TABLE RADIMGC4_GCNSEQNO_LINK

fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	# GCN_SEQNO
	gcn_seqno:
		model:
			type: 'int'
		view:
			label: 'Clinical Formulation ID (Stable ID)'
			readonly: true
			findunique: true
			columns: 2

	#DDI_CODEX
	ddi_codex:
		model:
			type: 'int'
		view:
			label: 'Drug-Drug Expanded Interaction Code'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['gcn_seqno', 'ddi_codex']
	indexes:
		many: [
			['gcn_seqno']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['gcn_seqno', 'ddi_codex']

view:
	comment: 'Manage > List FDB GCN_SEQNO/Drug-Drug Interaction Code Relation Table'
	find:
		basic: ['gcn_seqno']
	grid:
		fields: ['gcn_seqno', 'ddi_codex']
	label: 'List FDB GCN_SEQNO/Drug-Drug Interaction Code Relation Table'
