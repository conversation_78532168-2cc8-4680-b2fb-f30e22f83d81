fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: false
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	physician_id:
		model:
			required: true
			source: 'physician'
		view:
			columns: 3
			label: 'Primary Physician'

	is_primary:
		model:
			max: 3
			source: ['Yes']
			default: 'Yes'
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			note: 'Only one primary prescriber per patient'
			label: 'Primary Prescriber?'
			columns: 3

	supervising:
		model:
			max: 3
			source: ['Yes']
			default: 'Yes'
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Supervising Provider?'
			columns: 3

	ordering:
		model:
			max: 3
			source: ['Yes']
			default: 'Yes'
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Ordering Provider?'
			columns: 3

	notes:
		view:
			control: 'area'
			label: 'Notes'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
	bundle: ['patient']
	sections:
		'Patient Prescriber':
			fields: ['physician_id', 'is_primary', 'supervising', 'ordering', 'notes']
	transform: [
		{
			name: "EnsureUnique"
			arguments:
				form: "patient_prescriber"
				unique_field: "is_primary"
				nonunique_value: "No"
				qualifier_field: "patient_id"
		}
	]
	name: ['physician_id']
view:
	dimensions:
        width: '75%'
        height: '75%'
	comment: 'Patient > Patient Prescriber'
	grid:
		fields: ['is_primary', 'physician_id', 'notes']
	icon: 'prescriber'
	label: 'Patient Prescriber'
