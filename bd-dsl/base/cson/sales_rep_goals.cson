fields:
	# sales_goals:
	# 	model:
	# 		source: ['Sales Goals']
	# 		multi: true
	# 	view:
	# 		label: 'Sales Goals'

	assigned_sales_rep:
		model:
			source: 'user'
			sourcefilter:
				role:
					'static': 'liaison'
			# multi: true
		view:
			label: 'Assigned Sales Rep'
			columns: 2
	
	ig_grams:
		model:
			type: 'decimal'
		view:
			label: 'IG Grams'
			columns: 2
	
	ig_grams_period:
		model:
			source: ['Month', 'Quarter', 'Year']
		view:
			control: 'radio'
			label: 'IG Grams/Period'
			columns: 2
	
	revenue:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			label: 'Revenue'
			columns: 2
	
	revenue_period:
		model:
			source: ['Month', 'Quarter', 'Year']
		view:
			control: 'radio'
			label: 'Revenue/Period'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'liaison']
		delete:     ['admin', 'liaison']
		read:       ['admin', 'liaison']
		read_all:   ['admin', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'liaison']
		write:      ['admin', 'liaison']
	name: ['assigned_sales_rep', 'revenue_period']
	sections:
		'Assignee':
			fields: ['assigned_sales_rep']
		'IG Grams / Period':
			fields: ['ig_grams', 'ig_grams_period']
		'Revenue / Period':
			fields: ['revenue', 'revenue_period']

view:
	comment: 'Manage > Sales Contact'
	grid:
		fields: ['assigned_sales_rep']
	label: 'Goals'