#TABLE RSTRUOM0_STRENGTH_UOM
fields:
    code:
        model:
            max: 64
            required: true
        view:
            label: 'Code'

    #UOM_ID
    uom_id:
        model:
            type: 'int'
        view:
            label: 'Strength Unit of Measure Identifier'
            readonly: true
            columns: 3

    #UOM_DESC
    uom_desc:
        view:
            label: 'Strength Unit of Measure Description'
            readonly: true
            columns: 3

    #UOM_ABBR
    uom_abbr:
        view:
            label: 'Strength Unit of Measure Abbreviation'
            readonly: true
            columns: 3

    #UOM_PREFERRED_DESC
    uom_preferred_desc:
        view:
            label: 'Strength Unit of Measure Preferred Description'
            readonly: true
            columns: 3

model:
    access:
        create:     []
        create_all: ['admin']
        delete:     ['admin']
        read:       ['admin']
        read_all:   ['admin']
        request:    []
        update:     []
        update_all: ['admin']
        write:      ['admin']
    bundle: ['reference']
    sync_mode: 'full'
    name: "{uom_desc} - {uom_abbr}"
    indexes:
        many: [
            ['uom_id']
            ['uom_abbr']
        ]
    sections:
        'Details':
            hide_header: true
            indent: false
            fields: [
                'uom_id', 'uom_desc', 'uom_abbr', 'uom_preferred_desc'
            ]

view:
    comment: 'Manage > List FDB Ingredient Strength Unit of Measure'
    find:
        basic: ['uom_id', 'uom_desc', 'uom_abbr']
    grid:
        fields: ['uom_id', 'uom_desc', 'uom_abbr', 'uom_preferred_desc']
    label: 'List FDB Ingredient Strength Unit of Measure'