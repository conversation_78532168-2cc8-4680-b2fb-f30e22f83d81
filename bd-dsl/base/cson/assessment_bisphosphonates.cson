fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Demographics
	gender:
		model:
			if:
				'Female':
					sections: ['Bisphosphonates - Pregnancy Risk']
			prefill: ['patient']
		view:
			label: 'Sex'
			offscreen: true
			readonly: true

	pregnancy_risk:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Reviewed pregnancy risks if applicable?'
			columns: 2

	recent_crcl_value:
		model:
			rounding: 0.01
			type: 'decimal'
			if:
				'<60':
					note: 'Review renal impairment dosage adjustment recommendations for patient’s indication'
		view:
			label: 'Recent CrCl Value'
			columns: 2

	recent_crcl_date:
		model:
			type: 'date'
		view:
			label: 'Recent CrCl Date'
			columns: 2

	recent_scr_value:
		model:
			rounding: 0.01
			type: 'decimal'
			if:
				'>3':
					note: "Review the patient's history and physician notes and consider contacting physician as use in patients with severe renal impairment is generally to be avoided"
		view:
			label: 'Recent SCr Value'
			columns: 2

	recent_scr_date:
		model:
			type: 'date'
		view:
			label: 'Recent SCr Date'
			columns: 2

	hydration_status:
		model:
			source: ['Well Hydrated', 'Dehydrated', 'Unsure']
		view:
			control: 'radio'
			label: 'What is your hydration status?'
			columns: 2

	taking_calcium:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you taking calcium?'
			columns: 2

	seen_dentist:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			note: 'Counsel patient about increased risk of osteonecrosis especially of the jaw and how to avoid'
			label: 'Have you seen the dentist recently?'
			columns: 2

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections: 
		'Bisphosphonates - Pregnancy Risk':
			indent: false
			tab: 'Pregnancy Risk'
			note: 'If patient is a female of reproductive age ensure she is not pregnant and understands the risk of fetal injury from bisphosphonates'
			fields: ['gender', 'pregnancy_risk']
		'Bisphosphonates - Recent Labs':
			indent: false
			tab: 'Recent Labs'
			fields: ['recent_crcl_value', 'recent_crcl_date', 'recent_scr_value', 'recent_scr_date']
		'Bisphosphonates - Patient Questionnaire':
			indent: false
			tab: 'Patient Questionnaire'
			fields: ['hydration_status', 'taking_calcium', 'seen_dentist']
view:
	comment: 'Patient > Careplan > Assessment > Bisphosphonates'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
	label: 'Assessment Questionnaire: Bisphosphonates'
	open: 'read'
