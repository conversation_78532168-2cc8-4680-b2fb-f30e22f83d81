fields:

	pricing_update_no:
		view:
			label: 'Pricing Update No'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			source: 'inventory'
		view:
			columns: 2
			label: 'Drug'
			readonly: true

	pndc:
		view:
			label: 'Previous NDC'
			columns: 4
			readonly: true

	repndc:
		view:
			label: 'Updated NDC'
			columns: 4
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin','pharm','csr','cm', 'nurse']
		read_all:   ['admin','pharm','csr','cm', 'nurse']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['pricing_update_no'],
			['inventory_id']
		]

	name: ['inventory_id']
	sections_group: [
		'Inventory Drug NDC Update':
			hide_header: true
			indent: false
			fields: ['pricing_update_no', 'inventory_id', 'pndc', 'repndc']
	]

view:
	dimensions:
		width: '45%'
		height: '45%'
	hide_cardmenu: true
	comment: 'Inventory Drug NDC Update'
	find:
		basic: ['inventory_id']
		advanced: []
	grid:
		fields: ['inventory_id', 'pndc', 'repndc']
		sort: ['-id']
	label: 'Inventory Drug NDC Update'
	open: 'read'