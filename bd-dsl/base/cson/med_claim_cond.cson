fields:

	condition_codes:
		model:
			required: true
			multi: true
			min: 2
			max: 3
			source: 'list_nubc_code'
			sourceid: 'code'
		view:
			label: 'Condition Code(s)'
			reference: ['HI01-01','HI02-01','HI03-01','HI04-01','HI05-01','HI06-01','HI07-01','HI08-01','HI09-01','HI10-01','HI11-01','HI12-01']
			note: 'Max 12'
			max_count: 12
			_meta:
				location: '2300 HI'
				field: ['01-01','02-01','03-01','04-01','05-01','06-01','07-01','08-01','09-01','10-01','11-01','12-01']
				type: 'index'
				path: 'claimInformation.conditionInformation[{idx1-2}].conditionCodes[{idx1-12}]'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		delete:     ['admin', 'pharm', 'patient']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
	name:['condition_codes']
	sections:
		'Condition Code(s)':
			hide_header: true
			fields: ['condition_codes']

view:
	dimensions:
		width: '85%'
		height: '45%'
	hide_cardmenu: true
	reference: '2300'
	comment: 'Condition Code(s)'
	hide_header: true
	grid:
		fields: ['condition_codes']
		sort: ['-id']
	label: 'Condition Code(s)'
