fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	educated_risk:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					note: 'Provide the Patient Medication Guide to the patient'
		view:
			control: 'radio'
			label: "Has the patient received and read the Patient Medication Guide, including the section 'What should I tell my doctor and nurse before each infusion of TYSABRI?'"

	last_month_problems:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['Tysabri Contact Prescriber']
					note: "DO NOT INFUSE. Contact the healthcare provider who prescribed Tysabri and review the patient's answers"
		view:
			control: 'radio'
			label: 'Over the past month, have you had any new or worsening medical problems (such as a new or sudden change in your thinking, eyesight, balance, strength, or other problems) that have persisted over several days?'

	weaken_immune:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['Tysabri Contact Prescriber']
					note: "DO NOT INFUSE. Contact the healthcare provider who prescribed Tysabri and review the patient's answers"
		view:
			control: 'radio'
			label: 'Do you have a medical condition that can weaken your immune system, such as HIV infection or AIDS, leukemia or lymphoma, or an organ transplant, that may suggest that your body is not able to fight infections well?'

	had_pml:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['have_jcv']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with Progressive multifocal leukoencephalopathy (PML)?'

	have_jcv:
		model:
			source: ['No', 'Yes', 'Unknown']
			if:
				'Yes':
					fields: ['had_ig']
		view:
			control: 'radio'
			label: 'Does the patient have presence of anti-JCV antibodies?'

	had_ig:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Wait at least 6 months after last IG treatment to avoid false-positives'
		view:
			control: 'radio'
			label: 'Has the patient been treated with IG in the last 6 months?'

	physician_contact:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			required: true
			if:
				'Yes':
					fields: ['physician_approval']
				'No':
					note: "Instruct the patient to contact his/her prescriber and to reschedule an infusion as soon as possible. Continue efforts to reach the prescriber to inform him/her of the reason(s) for not infusing this patient. You will need to confirm authorization from the prescriber on the subsequent infusion"
		view:
			control: 'radio'
			label: "Were you able to contact the prescriber?"

	physician_approval:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "After discussing the patient's answers, did the prescriber authorize the patient to be infused?"

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Tysabri Patient Assessment':
			fields: ['educated_risk', 'last_month_problems', 'weaken_immune',
			'had_pml', 'have_jcv', 'had_ig']
		'Tysabri Contact Prescriber':
			fields: ['physician_contact', 'physician_approval']

view:
	comment: 'Patient > Careplan > Assessment > Tysabri'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Assessment Questionnaire: Tysabri'
	open: 'read'
