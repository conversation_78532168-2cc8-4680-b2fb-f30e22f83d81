fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	receipt_no:
		model:
			search: 'A'
		view:
			label: 'Receipt No'
			readonly: true
			offscreen: true
			findunique: true

	auto_generated:
		model:
			source: ['Yes']
		view:
			label: 'Auto-generated'
			readonly: true
			offscreen: true
			findunique: true

	#TODO Need to support 850 electronic PO
	order_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 2
			label: 'Ordered Date'
			template: '{{now}}'

	expected_date:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Expected Date'

	reorder_po_id:
		model:
			source: 'po'
			type: 'int'
		view:
			readonly: true
			label: 'Reorder PO'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/po/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'comments': ['comments'],
					'supplier_id': ['supplier_id'],
					'site_id': ['site_id'],
					'reorder_no': ['reorder_no'],
					'total_cost': ['total_cost'],
					'subform_items':
						'type': 'subform'
						'field': 'subform_items'
						'fields':
							'inventory_id': ['sf.inventory_id']
							'qty_pkg': ['sf.qty_pkg']
							'ordered': ['sf.ordered']
							'total_ordered': ['sf.total_ordered']
							'package_cost': ['sf.package_cost']
							'item_cost': ['sf.item_cost']
							'cost_each': ['sf.cost_each']
							'comments': ['sf.comments']
							'lot_tracking': ['sf.lot_tracking']
							'serial_tracking': ['sf.serial_tracking']
			]

	template_id:
		model:
			source: 'template_po'
			sourcefilter:
				site_id:
					'dynamic': '{site_id}'
		view:
			columns: 2
			label: 'PO Template'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/template_po/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				lock_fields: ['supplier_id', 'comments']
				fields:
					'comments': ['comments'],
					'supplier_id': ['supplier'],
					'subform_items':
						'type': 'subform'
						'field': 'subform_items'
						'fields':
							'template_id': ['pr.id']
							'inventory_id': ['sf.inventory_id']
							'qty_pkg': ['sf.qty_pkg']
							'ordered': ['sf.ordered']
							'package_cost': ['sf.package_cost']
							'item_cost': ['sf.item_cost']
							'comments': ['sf.comments']
			]

	status_id:
		model:
			source: 'list_po_status'
			sourceid: 'code'
			default: 'ORD'
		view:
			class: 'status'
			columns: 2
			label: 'Status'
			readonly: true

	site_id:
		model:
			required: true
			source: 'site'
			search: 'B'
		view:
			columns: 2
			findmulti: true
			label: 'Site'

	supplier_id:
		model:
			required: false
			search: 'B'
			source: 'list_supplier'
		view:
			columns: 2
			label: 'Supplier'

	reorder_no:
		view:
			columns: 2
			label: 'Reorder #'

	confirmation_no:
		model:
			search: 'B'
		view:
			columns: 2
			label: 'Confirmation #'

	comments:
		view:
			columns: 2
			label: 'Comments'
			control: 'area'

	subform_items:
		model:
			multi: true
			source: 'po_item'
			type: 'subform'
		view:
			label: 'Items'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				fields: ['inventory_id', 'qty_pkg', 'package_cost', 'item_cost']
				label: ['Item', 'Quantity', 'Pack $', 'Per $']
				width: [55, 15, 15, 15]
			transform: [
				name: 'MapSubform'
				fields:
					'total_cost':
						'transform': 'Sum'
						'fields': ['item_cost']
			]

	total_cost:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 2
			label: 'Total Cost'
			class: 'numeral'
			format:'$0,0.00'
			readonly: true

	order_file:
		model:
			type: 'json'
		view:
			columns: 2
			control: 'file'
			label: 'File Attachment'
			note: 'Max 100MB. Only documents, images, and archives supported.'

	open_sm:
		model:
			default: '{"url":"#/inventory/sm/manager/stock-manager/{site_id}/received/po/{id}/supplier/{supplier_id}","title":"", "icon": "fa fa-link"}'
			type: 'json'
			save: false
		view:
			label: ''
			control: 'link'
			offscreen: false
			readonly: true

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'po'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Other Form'
					direct_attachment: 'Yes'
					source: 'Scanned Document'
					form_name: 'po'
					form_code: '{code}'
					form_filter: 'po'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	access:
		create:     []
		create_all: ['admin','pharm','cm']
		delete:     ['admin','pharm','cm']
		read:       ['admin','pharm','cm']
		read_all:   ['admin','pharm','cm']
		request:    []
		review:     ['admin','pharm','cm']
		update:     []
		update_all: ['admin','pharm','cm']
		write:      ['admin','pharm','cm']
	bundle: ['inventory']
	name: 'S:{supplier_id} RN:{reorder_no} CN:{confirmation_no} ORD:{order_date}'
	indexes:
		many: [
			['site_id']
			['confirmation_no']
			['supplier_id']
			['status_id']
			['reorder_no']
		]
	sections_group: [
		'Purchase Order':
			hide_header: true
			indent: false
			sections: [
				'Details':
					hide_header: true
					indent: false
					fields: ['reorder_po_id', 'order_date', 'expected_date', 'template_id', 'status_id', 'site_id',
					'supplier_id', 'confirmation_no', 'reorder_no', 'comments']
				'Items':
					fields: ['subform_items']
				'Total':
					fields: ['total_cost', 'order_file']
			]
			'Documents':
				hide_header: true
				indent: false
				fields: ['embed_document']
				tab: 'Assigned Documents'
	]

view:
	hide_cardmenu: true
	block:
		update:
			if: 'receipt_no'
			except: ['empty']
	comment: 'Purchase Order'
	find:
		basic: ['site_id', 'status_id', 'supplier_id', 'confirmation_no', 'reorder_no', 'receipt_no']
	grid:
		fields: ['order_date', 'status_id', 'supplier_id', 'site_id', 'confirmation_no', 'receipt_no']
		sort: ['-id']
	label: 'Purchase Order'
	open: 'edit'