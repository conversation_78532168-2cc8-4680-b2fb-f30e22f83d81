fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	label_accuracy:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Label Accuracy'

	solution_integrity:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Solution Integrity'

	cracking:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Cracking'

	storage:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Storage'

	med_comments:
		view:
			control: 'area'
			label: 'Medication Comments'

	hand_washing:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Hand Washing'

	universal_precautions:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Universal Precautions'

	universal_precautions:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Universal Precautions'

	aspetic_technique:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Work Area / Aseptic Technique'

	caps_connections:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Caps / Connections'

	admixing:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Admixing'

	admixing_comments:
		view:
			control: 'area'
			label: 'Comments on Aseptic Technique'

	premix_meds:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Premixed Medications'

	minibag_advantage_sys:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Mini-bag Plus / Advantage Systems'

	pt_additives:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Patient Additives'

	piggback_bags:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Piggyback bags'

	med_prep_comments:
		view:
			control: 'area'
			label: 'Comments on Medication Preparations'

	gravity:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Gravity'

	pump_type:
		view:
			label: 'Pump Type:'

	pump_setup:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Pump Setup'

	alarm_troubleshooting:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Alarms / Troubleshooting'

	battery_changes:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Battery Changes'

	admin_meth_comments:
		view:
			control: 'area'
			label: 'Comments on Admin Methods'

	prime_tubing:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Priming Tubing'

	pump_loading:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Pump Loading'

	setup_hookup:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Hook Up / Set Up'

	disconnect:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Disconnect'

	admin_tech_comments:
		view:
			control: 'area'
			label: 'Comments on Admin Techniques'

	cath_type:
		model:
			multi: true
			source: ['Peripheral', 'Implanted port', 'Subcutaneous', 'PICC', 'Winged Needle', 'Other']
			if:
				'Other':
					fields: ['cath_type_other']
		view:
			control: 'checkbox'
			label: 'Catheter Type:'
	
	cath_type_other:
		view:
			label: 'Catheter Type Other:'

	site_inspect:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Site Inspection'

	dress_change:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Dressing Changes'

	site_care:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Site Care'

	cath_clamp:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Catheter Clamping'

	flush_protocol:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Flushing Protocol'

	access_maint_comments:
		view:
			control: 'area'
			label: 'Comments on Access Maintenance'

	med_sheet:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Medication Sheet'

	adverse_effects:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Possible adverse effects'

	phlebitis:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Phlebitis / Infiltration:'

	clotting:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Clotting / dislodgement'

	site_infection:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Site Infection'

	air_embolus:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Air Embolus'

	fluid_imbalance:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Fluid Imbalance'

	electrolyte_imbalance:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Electrolyte Imbalance'

	oral_med_info:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Oral Medication Info'

	instructed_returned:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Instructed / Returned'

	self_mont_comments:
		view:
			control: 'area'
			label: 'Other Comments on Self Monitoring'

	other_comments:
		view:
			control: 'area'
			label: 'Other Comments / Potential Complications'

	deliveries:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Deliveries'

	inventory_checks:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Inventory Checks'

	other_placements:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Order Placements'

	supplies_comments:
		view:
			control: 'area'
			label: 'Other Comments on Supplies'

	sharp_disposal:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Sharps Disposal / Pick up'

	narc_disposal:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Narcotics Disposal'

	chem_disposal:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Chemo Disposal'

	chem_spill_kit:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Chemo Spill Kit Use'

	pump_pickup:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Pump Pick Up'

	disposal_comments:
		view:
			control: 'area'
			label: 'Comments on Supplies / Disposal'

	elec_safety:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Electrical Safety'

	fire_safety:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Fire Safety'

	home_safety:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Home / Fall Safety'

	disaster_plan:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Disaster Planning'

	emergency_room:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Emergency Room Use'

	access_numbers:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: '24 hour access numbers'

	safety_comments:
		view:
			control: 'area'
			label: 'Comments on Safety / Disaster Planning'

	therapy_comments:
		view:
			control: 'area'
			label: 'Comments on Therapy Specific Education'

	service_complaints:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Service Complaints'

	advanced_directive:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Advance Directive Info'

	pt_rights:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Rights and Responsibility'

	pt_info_comments:
		view:
			control: 'area'
			label: 'Comments on Patient Information'

	overall_comments:
		view:
			control: 'area'
			label: 'Overall Comments on Checklist'

	therapy_reason:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Reason for Therapy:'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['careplan_id', 'order_id']
	sections_group: [
		'Patient Teaching Checklist':
			note: 'Mark Yes if the material has been reviewed with the patient'
			sections: [
				'Assessment Date':
					fields: ['assessment_date']
				'Medication':
					fields: ['label_accuracy', 'solution_integrity', 'cracking', 'storage', 'med_comments']
				'Aseptic Technique':
					fields: ['hand_washing', 'universal_precautions', 'aspetic_technique', 'caps_connections', 'admixing', 'admixing_comments']
				'Medication Preparations':
					fields: ['premix_meds', 'minibag_advantage_sys', 'pt_additives', 'piggback_bags', 'med_prep_comments']
				'Admin Methods':
					fields: ['gravity', 'pump_type', 'pump_setup', 'alarm_troubleshooting', 'battery_changes', 'admin_meth_comments']
				'Admin Techniques':
					fields: ['prime_tubing', 'pump_loading', 'setup_hookup', 'disconnect', 'admin_tech_comments']
				'Access Maintenance':
					fields: ['cath_type', 'cath_type_other', 'dress_change', 'site_care', 'cath_clamp', 'flush_protocol', 'access_maint_comments']
				'Self Monitoring':
					fields: ['med_sheet', 'adverse_effects', 'phlebitis', 'clotting', 'site_infection', 'air_embolus', 'fluid_imbalance', 'electrolyte_imbalance', 'instructed_returned', 'self_mont_comments', 'other_comments']
				'Supplies':
					fields: ['deliveries', 'inventory_checks', 'other_placements', 'supplies_comments']
				'Supplies / Disposal':
					fields: ['sharp_disposal', 'narc_disposal', 'chem_disposal', 'chem_spill_kit', 'pump_pickup', 'disposal_comments']
				'Safety / Disaster Planning':
					fields: ['elec_safety', 'fire_safety', 'home_safety', 'disaster_plan', 'emergency_room', 'access_numbers', 'safety_comments']
				'Therapy Specific Education':
					fields: ['therapy_comments']
				'Patient Information':
					fields: ['service_complaints', 'advanced_directive', 'pt_rights', 'therapy_reason', 'pt_info_comments', 'overall_comments']
			]
	]

	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Patient Teaching Checklist"
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Checklist > Teaching'
	grid:
		fields: ['created_on', 'assessment_date', 'overall_comments', 'therapy_reason']
		sort: ['created_on']
	label: 'Patient Teaching Checklist'
	open: 'read'
