fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	loose_stool:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Any loose / watery stools?'
			validate: [
					name: 'WATScoreValidate'
			]

	vomiting:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Any vomiting / wretching / gagging?'
			validate: [
					name: 'WATScoreValidate'
			]

	high_temp:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Temperature > 37.8C'
			validate: [
					name: 'WATScoreValidate'
			]

	sbs:
		model:
			source: ['SBS < 0 or asleep/awake/calm', 'SBS > +1 or awake/distressed']
		view:
			control: 'radio'
			label: 'State'
			validate: [
					name: 'WATScoreValidate'
			]

	tremor:
		model:
			source: ['None/mild', 'Moderate/severe']
		view:
			control: 'radio'
			label: 'Tremor'
			validate: [
					name: 'WATScoreValidate'
			]

	sweating:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Any sweating'
			validate: [
					name: 'WATScoreValidate'
			]

	movement:
		model:
			source: ['None/mild', 'Moderate/severe']
		view:
			control: 'radio'
			label: 'Uncoordinated / repetitive movement'
			validate: [
					name: 'WATScoreValidate'
			]

	yawning:
		model:
			source: ['None or 1', '>1']
		view:
			control: 'radio'
			label: 'Yawning or sneezing'
			validate: [
					name: 'WATScoreValidate'
			]

	touch:
		model:
			source: ['None/mild', 'Moderate/severe']
		view:
			control: 'radio'
			label: 'Startle to touch'
			validate: [
					name: 'WATScoreValidate'
			]

	muscle:
		model:
			source: ['Normal', 'Increased']
		view:
			control: 'radio'
			label: 'Muscle tone'
			validate: [
					name: 'WATScoreValidate'
			]

	calm:
		model:
			source: ['< 2min', '2 - 5min', '> 5 min']
		view:
			control: 'radio'
			label: 'Time to gain calm state (SBS < 1)'
			validate: [
					name: 'WATScoreValidate'
			]

	score:
		model:
			min: 0
			max: 130
			type: 'int'
		view:
			label: 'Assessment Score'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_wat:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Previous 12 Hours':
			note: 'Information from patient record, previous 12 hours'
			fields: ['loose_stool', 'vomiting', 'high_temp']
		'2 Minute Pre-stimulus Observation':
			fields: ['sbs', 'tremor', 'sweating', 'movement', 'yawning']
		'1 Minute Stimulus Observation':
			fields: ['touch', 'muscle']
		'Post-stimulus Recovery':
			fields: ['calm']
		'Score':
			fields: ['score']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Withdrawal Assessment Tool'
	grid:
		fields: ['created_on', 'created_by', 'score']
		sort: ['created_on']
	label: 'Withdrawal Assessment Tool'
