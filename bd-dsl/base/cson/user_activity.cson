fields:
	user_id:
		model:
			source: 'user'
		view:
			readonly: true
			note: 'Link to user record associated'
			label: 'User Record'

	event:
		model:
			required: true
		view:
			label: 'Event'

	form:
		model:
			required: true
		view:
			label: 'Form'

	log_data:
		model:
			type: 'json'
		view:
			label: 'Log Data'

model:
	access:
		create: []
		create_all: ['admin']
		delete: ['admin']
		read: ['admin']
		read_all: ['admin']
		request: []
		review: ['admin']
		update: []
		update_all: ['admin']
		write: ['admin']
	bundle: ['audit']
	name: ['user_id', 'event', 'form']

view:
	comment: 'User Activity'
	find:
		basic: ['user_id']
	label: 'User Activity'
	open: 'read'