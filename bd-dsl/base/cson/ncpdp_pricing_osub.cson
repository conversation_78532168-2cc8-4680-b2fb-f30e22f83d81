fields:
	o_amt_sub_qualifier:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '479-H8'
			required: true
		view:
			columns: 3
			reference: '479-H8'
			note: '479-H8'
			label: 'Qualifier'

	o_amt_sub:
		model:
			required: true
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			reference: '480-H9'
			note: '480-H9'
			label: 'Amount'
			class: 'numeral money'
			format: '$0,0.00'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']


	name: ['o_amt_sub_qualifier', 'o_amt_sub']
	sections:
		'Other Amounts Submitted':
			fields: ['o_amt_sub_qualifier', 'o_amt_sub']

view:
	dimensions:
		width: '50%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Other Amounts Submitted'
	grid:
		fields: ['o_amt_sub_qualifier', 'o_amt_sub']
		width: [50, 50]
		sort: ['-created_on']
	label: 'Pricing COB'
	open: 'read'