fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Demographics
	gender:
		model:
			if:
				'Female':
					fields: ['are_preg', 'birth_control', 'are_postmen']
			prefill: ['patient']
		view:
			label: 'Sex'
			offscreen: true
			readonly: true

	# HCV Details:
	genotype:
		model:
			max: 2
			source: ['1a', '1b', '2a', '2b', '2c', '2d', '3', '4', '5a', '6a']
		view:
			note: 'e.x. 1a'
			label: 'Genotype'

	il_genotype:
		model:
			source: ['CC', 'CT', 'TT']
		view:
			label: 'IL28B Genotype'

	elevated_alt:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Elevated serum ALT level?'

	cirr_biop:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_cirrhosis']
		view:
			control: 'radio'
			label: 'Cirrhosis and or Liver Biopsy performed?'

	have_cirrhosis:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cirrhosis_type', 'fibrosis_stage', 'child_pugh_score']
		view:
			control: 'radio'
			label: 'Cirrhosis confirmed?'

	cirrhosis_type:
		model:
			max: 3
			min: 1
			source: ['Compensated', 'Decompensated']
		view:
			control: 'radio'
			label: 'Cirrhosis Type'

	fibrosis_stage:
		model:
			source: ['0', '1', '2', '3', '4']
		view:
			control: 'radio'
			label: 'Fibrosis Stage'

	child_pugh_score:
		model:
			type:'int'
		view:
			label: 'Child-Pugh Score'

	# General Assessment
	cancer:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you currently or have you recently been on cancer treatment?'

	tattoos:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you have any tattoos?'

	hcv_mom:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Did your mother have Hepatitis C?'

	has_hiv:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Did you have a history HIV?"

	have_exposure:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Have you had any recent exposure to tuberculosis (TB), HBV, or mycoses?"

	travel:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['travel_where']
		view:
			control: 'radio'
			label: 'Any recent international travel?'

	travel_where:
		view:
			label: 'Where?'

	are_preg:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you currently pregnant or have given birth in the last 6 weeks?'

	birth_control:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you currently on any birth control or hormone replacement pills?'

	are_postmen:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you postmenopausal?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections_group: [
		'Hepatitis C Questionnaire':
			sections: [
				'Hepatitis C Condition Details':
					area: 'preassessment'
					fields: ['genotype', 'il_genotype', 'elevated_alt', 'cirr_biop',
					'have_cirrhosis', 'cirrhosis_type', 'fibrosis_stage', 'child_pugh_score']
				'Hepatitis C General Assessment':
					fields: ['gender', 'are_preg', 'birth_control', 'are_postmen',
					'cancer', 'tattoos', 'hcv_mom', 'has_hiv', 'have_exposure',
					'travel', 'travel_where']
			]
		]
view:
	comment: 'Patient > Careplan > Assessment > Hepatitis C'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Hepatitis C'
	open: 'read'
