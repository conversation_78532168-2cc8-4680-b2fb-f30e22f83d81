fields:

	claim_no:
		view:
			label: 'Claim No'
			readonly: true
			offscreen: true

	charge_no:
		model:
			required: false
		view:
			label: 'Charge No'
			readonly: true
			offscreen: true

	patient_id:
		model:
			type: 'int'
			required: false
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	site_id:
		model:
			type: 'int'
			required: false
			source: 'site'
			prefill: ['parent.site_id']
		view:
			label: 'Site'
			readonly: true
			offscreen: true

	payer_id:
		model:
			type: 'int'
			source: 'payer'
			prefill: ['parent.payer_id']
		view:
			columns: 3
			label: 'Payer'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/payer/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'mm_calc_perc_sales_tax': ['mm_calc_perc_sales_tax']
			]

	mm_calc_perc_sales_tax:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['sales_tax_amount']
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Calculate and Transmit % Sales Tax"
			note: 'AMT02 AMT01=T'
			offscreen: true
			readonly: true

	inventory_id:
		model:
			type: 'int'
			source: 'inventory'
		view:
			form_link_enabled: true
			columns: 2
			label: 'Service'
			readonly: true

	measurement_unit:
		model:
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SV103'
		view:
			columns: 4
			label: 'Unit'
			readonly: true

	service_unit_count:
		model:
			required: true
			max: 99999.999
			min: 0.001
			rounding: 0.001
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Charge Units'
			class: 'numeral'
			format: '0,0.[000000]'
			readonly: true

	dx_id_1:
		model:
			source: 'patient_diagnosis'
		view:
			columns: 2
			label: 'Diagnosis'
			reference: 'SV107-01'
			readonly: true
			offscreen: true

	modifier_1:
		model:
			min: 2
			max: 2
		view:
			label: 'Modifier'
			readonly: true
			offscreen: true

	line_item_charge_amount:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Charge Amount'
			readonly: true

	assigned_number:
		model:
			max: 999999
			min: 1
			type: 'int'
			default: 1
		view:
			columns: 4
			label: 'Service Line #'
			reference: 'LX01'
			_meta:
				location: '2400 LX'
				field: '01'
				path: 'claimInformation.serviceLines[{idx1-50}].serviceLineNumber'
			readonly: true

	provider_control_number:
		model:
			required: false
			min: 1
			max: 50
		view:
			columns: 4
			label: 'Provider Control Number (PCCN)'
			note: 'Generated at the time of saving'
			reference: 'REF04-02 REF01=6R'
			_meta:
				location: '2400 REF'
				field: '04-02'
				code: '6R'
				path: 'claimInformation.serviceLines[{idx1-50}].providerControlNumber'
			readonly: true

	service_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 4
			label: 'Service Date Start'
			note: 'Must be in MM/DD/YYYY format'
			reference: 'DTP03'
			readonly: true
			_meta:
				location: '2400 DTP'
				field: '03'
				path: 'claimInformation.serviceLines[{idx1-50}].serviceDate'

	service_date_end:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Service Date End'
			note: 'Must be in MM/DD/YYYY format'
			reference: 'DTP03 DTP02=D8'
			readonly: true
			_meta:
				location: '2400 DTP'
				field: '03'
				code: 'D8'
				path: 'claimInformation.serviceLines[{idx1-50}].serviceDateEnd'

	sales_tax_amount:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			offscreen: true
			readonly: true
			columns: 3
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Sales Tax Amt'
			reference: 'AMT02 AMT01=T'
			_meta:
				location: '2400 AMT'
				field: '02'
				code: 'T'
				path: 'claimInformation.serviceLines[{idx1-50}].salesTaxAmount'

	service_line_reference_information:
		model:
			multi: false
			source: 'med_claim_sl_ref'
			type: 'subform'
		view:
			label: 'Reference Information'

	lock_sv:
		model:
			if:
				'Yes':
					readonly:
						sections: ['Service']
		view:
			label: 'Lock Service'
			offscreen: true
			readonly: true

	professional_service:
		model:
			required: true
			multi: false
			source: 'med_claim_sv'
			type: 'subform'
		view:
			label: 'Service'

	drug_identification:
		model:
			required: false
			multi: false
			source: 'med_claim_sl_di'
			type: 'subform'
		view:
			label: 'Drug Identification'

	durable_medical_equipment_service:
		model:
			multi: false
			source: 'med_claim_dme'
			type: 'subform'
		view:
			label: 'DME'

	durable_medical_equipment_certificate_of_medical_necessity:
		model:
			multi: false
			source: 'med_claim_dme_cmn'
			type: 'subform'
		view:
			label: 'DME CMN'

	durable_medical_equipment_certification:
		model:
			multi: false
			source: 'med_claim_dme_cert'
			type: 'subform'
		view:
			label: 'DME Certification'

	condition_indicator_durable_medical_equipment:
		model:
			multi: false
			source: 'med_claim_dme_cond'
			type: 'subform'
		view:
			label: 'DME Condition'

	service_line_date_information:
		model:
			multi: false
			source: 'med_claim_sl_dt'
			type: 'subform'
		view:
			label: 'Service Dates'

	additional_notes:
		model:
			max: 80
		view:
			control: 'area'
			label: 'Notes'
			reference: 'NTE02 NTE01=ADD'
			_meta:
				location: '2400 NTE'
				field: '02'
				code: 'ADD'
				path: 'claimInformation.serviceLines[{idx1-50}].additionalNotes'

	goal_rehab_or_discharge_plans:
		model:
			max: 80
		view:
			control: 'area'
			label: 'Goal/Rehab/Discharge Plans'
			reference: 'NTE02 NTE01=DCP'
			_meta:
				location: '2400 NTE'
				field: '02'
				code: 'DCP'
				path: 'claimInformation.serviceLines[{idx1-50}].goalRehabOrDischargePlans'

	third_party_organization_notes:
		model:
			max: 80
		view:
			control: 'area'
			label: '3rd-Party Organzation Notes'
			reference: 'NTE02 NTE01=TPO'
			_meta:
				location: '2400 NTE'
				field: '02'
				code: 'TPO'
				path: 'claimInformation.serviceLines[{idx1-50}].thirdPartyOrganizationNotes'

	line_pricing_repricing_information:
		model:
			multi: false
			source: 'med_claim_reprice_sl'
			type: 'subform'
		view:
			label: 'Repricing'
			offscreen: true 
			readonly: true 

	line_adjudication_information:
		model:
			multi: true
			source: 'med_claim_sl_adj'
			type: 'subform'
		view:
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['other_payer_primary_identifier', 'procedure_code', 'adjudication_or_payment_date', 'service_line_paid_amount', 'remaining_patient_liability']
				label: ['Payer', 'Proc', 'Date', 'Paid', 'Rem Bal']
				width: [20, 20, 20, 20, 20]
			label: 'Adjudication Information'
			max_count: 15

	form_identification:
		model:
			multi: true
			source: 'med_claim_sl_fi'
			type: 'subform'
		view:
			grid:
				add: 'inline'
				edit: true
				fields: ['form_type_code', 'form_identifier']
				label: ['Type', 'ID']
				width: [50, 50]
			label: 'Form Identification'
			max_count: 99

	service_line_supplemental_information:
		model:
			multi: true
			source: 'med_claim_sl_sup'
			type: 'subform'
		view:
			note: 'Max 10'
			label: 'Supplemental Information'
			grid:
				add: 'flyout'
				edit: true
				fields: ['attachment_report_type_code', 'attachment_control_number']
				label: ['Type', '#']
				width: [70, 30]
			max_count: 10

	file_information:
		model:
			multi: true
			source: 'med_claim_sl_file'
			type: 'subform'
		view:
			note: 'Max 10'
			label: 'Files'
			grid:
				add: 'inline'
				edit: true
				fields: ['file', 'comments']
				label: ['File', 'Comments']
				width: [40, 60]
			max_count: 10

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['assigned_number']
	sections_group: [
		'Details':
			hide_header: true
			tab: 'Service'
			fields: ['patient_id', 'site_id', 'payer_id', 'mm_calc_perc_sales_tax',
			'service_date', 'service_date_end', 'assigned_number', 'provider_control_number',
			'sales_tax_amount', 'lock_sv']
		'Service':
			hide_header: true
			indent: false
			tab: 'Service'
			fields: ['professional_service']
		'Drug Identification':
			hide_header: true
			indent: false
			tab: 'Drug Identification'
			tab_toggle: true
			fields: ['drug_identification']
		'Service Line Reference Information':
			hide_header: true
			indent: false
			tab: 'Service'
			fields: ['service_line_reference_information']
		'Dates':
			hide_header: true
			indent: false
			tab: 'Dates'
			tab_toggle: true
			fields: ['service_line_date_information']
		'Notes':
			hide_header: true
			indent: false
			tab: 'Notes'
			tab_toggle: true
			fields: ['additional_notes', 'goal_rehab_or_discharge_plans',
			'third_party_organization_notes']
		'DME':
			hide_header: true
			indent: false
			tab: 'DME'
			tab_toggle: true
			fields: ['durable_medical_equipment_service']
		'DME CMN':
			hide_header: true
			indent: false
			tab: 'DME'
			tab_toggle: true
			fields: ['durable_medical_equipment_certificate_of_medical_necessity']
		'DME Certification':
			hide_header: true
			indent: false
			tab: 'DME'
			tab_toggle: true
			fields: ['durable_medical_equipment_certification']
		'DME Condition':
			hide_header: true
			indent: false
			tab: 'DME'
			tab_toggle: true
			fields: ['condition_indicator_durable_medical_equipment']
		'Repricing':
			hide_header: true
			indent: false
			tab: 'Repricing'
			tab_toggle: true
			fields: ['line_pricing_repricing_information']
		'Adjudication Information':
			hide_header: true
			indent: false
			tab: 'COB'
			tab_toggle: true
			fields: ['line_adjudication_information']
		'Form Identification':
			hide_header: true
			indent: false
			tab: 'Attachments'
			fields: ['form_identification']
		'Supplemental Information':
			hide_header: true
			indent: false
			tab: 'Attachments'
			fields: ['service_line_supplemental_information']
		'File Information':
			hide_header: true
			indent: false
			tab: 'Attachments'
			fields: ['file_information']
		'Service Line Summary':
			hide_header: true
			indent: false
			tab: 'Attachments'
			fields: ['inventory_id', 'service_unit_count', 'measurement_unit']
	]

view:
	dimensions:
		width: '85%'
		height: '85%'
	hide_cardmenu: true
	validate: [
		{
			name: "DateOrderValidator"
			fields: [
				"service_date",
				"service_date_end"
			]
			error: "Service Date End cannot be before Service Date"
		}
	]
	reference: '2400'
	comment: 'Service Line Claims'
	grid:
		fields: ['assigned_number', 'inventory_id', 'measurement_unit', 'service_unit_count', 'dx_id_1', 'modifier_1']
		width: [10, 30, 15, 15, 20, 10]
		sort: ['-created_on']
	label: 'Service Line Claims'
	open: 'read'