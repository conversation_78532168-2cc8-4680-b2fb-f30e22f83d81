fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Questionnaire Participation

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_padqol.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	# Questionnaire
	padqol_infections:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "I get infections between infusion"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_tired:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "I am more tired than normal"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_cough:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "My cough has worsened"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_flareups:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "I have flare ups and symptoms of sinusitis"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_unscheduled_visit:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "I have to seek unscheduled medical visits for my PIDD"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_nausea:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "I have nausea and bloating"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_infections_trouble:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "I have trouble with infections"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_effects_wear_off:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "The effects of my treatment wears off between infusion"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_effects_wear_off:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "The effects of my treatment wears off between infusion"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_shortness_breath:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "I have trouble with shortness of breath"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_keepup:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "I struggle to keep up with others"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_trouble_sleeping:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "I have trouble sleeping"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_depressed:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "I feel downhearted and depressed about my PIDD"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_missed_days:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "I have missed school or work due to my PIDD"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_burden:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "I feel that I am a burden to others"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_require_help:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "I require help from others frequently"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	padqol_avoid:
		model:
			source: ['Rarely/never', 'Sometimes', 'Often/always']
		view:
			control: 'radio'
			label: "I avoid certain places and situations because of my PIDD"
			validate: [
					name: 'PADQOLScoreValidate'
			]

	score:
		model:
			min: 0
			max: 130
			type: 'int'
		view:
			note: 'Depression Severity: 0-4 none, 5-9 mild, 10-14 moderate, 15-19 moderately severe, 20-27 severe'
			label: 'Assessment Score'
			readonly: true

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_padqol:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'PID Quality Of Life (PADQOL-16)':
			note: 'Instructions: This survey asks for your views about your health. This information will help your physicians and nurses know how you feel, how well you are able to do your usual activities, and how you feel about your treatment and treatment schedule. For each of the following questions please select the answer that best describes your answer on how you felt and how things have been going over the previous four weeks.'
			fields: ['last_assessment_date', 'assessment_date']
		'Questionnaire':
			note: 'How much of the time during the past 4 weeks:'
			fields: ['padqol_infections', 'padqol_tired', 'padqol_cough', 'padqol_flareups', 'padqol_unscheduled_visit',
			'padqol_nausea', 'padqol_infections_trouble', 'padqol_effects_wear_off', 'padqol_shortness_breath',
			'padqol_keepup', 'padqol_trouble_sleeping', 'padqol_depressed', 'padqol_missed_days', 'padqol_burden',
			'padqol_require_help', 'padqol_avoid', 'score']
			prefill: 'clinical_padqol'
view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > PID Quality Of Life'
	label: 'PID Quality Of Life (PADQOL-16)'
