fields:

	severity:
		model:
			source: ['Low', 'Medium', 'High']
			required: true
		view:
			control: 'radio'
			label: 'Severity'

	type:
		model:
			source: ['Forms (DSL)', 'Workflow', 'Analytics', 'Feature Request', 'Other']
			if:
				'Forms (DSL)':
					fields: ['form_location', 'form_section']
				'Workflow':
					fields: ['workflow_dashboard', 'workflow_swimlane']
				'Analytics':
					fields: ['analytics_group', 'analytics_report']
			required: true
		view:
			control: 'radio'
			label: 'Issue Type'

	form_location:
		model:
			required: true
		view:
			note: 'e.g. Patient > medication, or Clinical Assessment > Initial or ongoing'
			label: 'Form Location'

	form_section:
		model:
			required: true
		view:
			note: 'e.g. H&P, Fatigue assessment etc.'
			label: 'Form Section'

	workflow_dashboard:
		model:
			required: true
		view:
			note: 'e.g. Careplan, Pharmacy'
			label: 'Dashboard'

	workflow_swimlane:
		model:
			required: true
		view:
			note: 'e.g. New Patient, Missing Insurance'
			label: 'Swimlane'

	analytics_group:
		model:
			required: true
		view:
			note: 'e.g. Accreditation, Operations'
			label: 'Report Group'

	analytics_report:
		model:
			required: true
		view:
			note: 'e.g. Profit Report, PDC'
			label: 'Report Tab'

	subject:
		model:
			required: true
		view:
			label: 'Subject'

	description:
		model:
			required: true
		view:
			control: 'area'
			label: 'Description'

	responses:
		model:
			subfields:
				date:
					label: 'Date'
					type: 'timestamp'
					readonly: true
				user:
					label: 'User'
					source: '{{user.displayname}}'
					type: 'text'
					readonly: true
				note:
					label: 'Note'
					type: 'text'
			type: 'json'
		view:
			control: 'grid'
			label: 'Responses'

	needs_info:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			note: 'More information is needed from the client'
			label: 'Additional Information Needed?'

	onhold:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			note: 'A fix is in process or waiting to be developed'
			label: 'On Hold?'

	needs_review:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Ready for Review?'

	completed:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			findfilter: 'No'
			control: 'radio'
			label: 'Complete?'

	subform_attachment:
		model:
			multi: true
			source: 'issue_ticket_attachment'
			type: 'subform'
		view:
			label: 'File Attachments'
			note: 'Max 100MB. Only documents, images, and archives supported.'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm', 'cm']
		delete:     ['admin', 'pharm', 'cm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'pharm', 'cm']
		write:      ['admin', 'pharm', 'cm']
	name: ['severity', 'type', 'form_location', 'form_section']
	sections:
		'Issue Ticket':
			fields: ['severity', 'type', 'form_location', 'form_section',
			'workflow_dashboard', 'workflow_swimlane', 'analytics_group', 'analytics_report',
			'subject', 'description', 'responses', 'needs_info', 'onhold', 'needs_review', 'completed']
		'File Attachments':
			fields: ['subform_attachment'] # subform

view:
	comment: 'Manage > Issue Ticket'
	find:
		basic: ['id', 'severity', 'needs_review', 'needs_info', 'onhold', 'completed', 'type']
		advanced: ['created_by', 'updated_by', 'subject', 'description']
	grid:
		fields: ['id', 'created_on', 'created_by', 'severity', 'subject', 'needs_review', 'needs_info', 'onhold',  'completed']
		sort: ['-id']
	label: 'Issue Ticket'
