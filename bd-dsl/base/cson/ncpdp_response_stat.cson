fields:

	transaction_response_status:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '112-AN'
			if:
				'R':
					fields: ['subform_reject']
					sections: ['Reject Codes']
		view:
			class: 'status'
			columns: 3
			note: '112-AN'
			label: 'Transaction Status'
			readonly: true

	approved_msg:
		model:
			multi: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '548-6F'
		view:
			columns: 3
			note: '548-6F'
			label: 'Approved Message Codes'
			readonly: true

	subform_reject:
		model:
			type: 'subform'
			multi: true
			source: 'ncpdp_response_stat_rj'
		view:
			label: 'Reject Codes'
			readonly: true

	subform_msg:
		model:
			type: 'subform'
			multi: true
			source: 'ncpdp_response_stat_msg'
		view:
			label: 'Addtl Messages'
			readonly: true

	help_qual:
		model:
			multi: false
			source: 'list_ncpdp_ext_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '549-7F'
		view:
			columns: 3
			note: '549-7F'
			label: 'Help Desk Qualifier'
			readonly: true

	help_phone:
		view:
			columns: 3
			note: '550-8F'
			format: 'us_phone'
			label: 'Help Desk Phone Number'
			readonly: true

	authorization_number:
		view:
			columns: 3
			note: '503-F3'
			label: 'Authorization Number'
			readonly: true

	transaction_ref_no:
		view:
			columns: 3
			note: '880-K5'
			label: 'Trans Reference Number'
			readonly: true

	internal_control_no:
		view:
			columns: 3
			note: '993-A7'
			label: 'Internal Control Number'
			readonly: true

	url:
		view:
			columns: 3
			note: '987-MA'
			label: 'URL'
			format: 'url'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['transaction_ref_no', 'transaction_response_status']
	sections_group: [
		'Status':
			sections: [
				'Details':
					hide_header: true
					indent: false
					fields: ['transaction_response_status', 'approved_msg']

				'Reject Codes':
					fields: ['subform_reject']

				'Additional Messages':
					hide_header: true
					indent: false
					fields: ['subform_msg']

				'Help Desk':
					fields: ['help_qual', 'help_phone']

				'Reference':
					fields: ['authorization_number', 'transaction_ref_no', 'internal_control_no', 'url']

			]
		]
view:
	dimensions:
		width: '50%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Response Status'
	grid:
		fields: ['transaction_response_status', 'authorization_number', 'help_qual', 'help_phone']
		sort: ['-created_on']
	label: 'Response Status'
	open: 'read'