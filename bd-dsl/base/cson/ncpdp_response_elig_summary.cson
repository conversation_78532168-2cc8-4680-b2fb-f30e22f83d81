fields:

	response_id:
		model:
			type: 'int'
			source: 'ncpdp_response'
		view:
			label: 'NCPDP Response'
			readonly: true
			offscreen: true

	response_uuid:
		model:
			type: 'text'
		view:
			label: 'Response UUID'
			readonly: true
			offscreen: true

	patient_id:
		model:
			type: 'int'
			source: 'patient'
		view:
			label: 'Patient'
			columns: 4
			readonly: true

	svc_prov_id:
		view:
			columns: 4
			label: 'Pharmacy Identifier'
			readonly: true

	claim_no:
		model:
			required: false
			type: 'text'
		view:
			label: 'Claim #'
			columns: 4
			readonly: true
			offscreen: true

	response_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Response Date/Time'
			columns: -4
			readonly: true

	claim_status:
		model:
			source: ["Approved", "Rejected"]
		view:
			columns: 4
			class: 'status'
			control: 'radio'
			label: 'Eligibility Status'
			readonly: true

	group_id:
		model:
			max: 15
		view:
			columns: -4
			note: '301-C1'
			label: 'Group Number'
			readonly: true

	plan_id:
		view:
			label: 'Plan ID'
			columns: 4
			readonly: true

	mcd_id_no:
		view:
			columns: 4
			note: '115-N5'
			label: 'MCD ID #'
			readonly: true

	mcd_agcy_no:
		view:
			columns: 4
			note: '116-N6'
			label: 'MCD Agency #'
			readonly: true

	card_holder_id:
		model:
			max: 20
		view:
			columns: 4
			note: '302-C2'
			label: 'Cardholder #'
			readonly: true

	pt_rel_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '306-C6'
		view:
			columns: 4
			reference: '306-C6'
			note: '306-C6'
			label: 'Relationship Code'

	network_reimbursement_id:
		view:
			columns: 4
			note: '545-2F'
			label: 'Network Rem ID'
			readonly: true

	medi_part_d_cov_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '139-UR'
		view:
			columns: 3
			note: '139-UR'
			label: 'MCD Coverage Code'
			readonly: true

	cms_low_income_sharing:
		view:
			columns: 3
			note: '138-UQ'
			label: 'CMS LICS Level'
			readonly: true

	contract_number:
		view:
			columns: 3
			note: '138-UQ'
			label: 'Contract #'
			readonly: true

	formulary_id:
		view:
			columns: 3
			note: '926-FF'
			label: 'Formulary ID'
			readonly: true
	
	benefit_id:
		view:
			columns: 3
			note: '757-U6'
			label: 'Benefit ID'
			readonly: true

	medi_part_d_eff_date:
		model:
			type: 'date'
		view:
			columns: 2
			note: '140-US'
			label: 'Next MCD Eff Date'
			readonly: true

	medi_part_d_trm_date:
		model:
			type: 'date'
		view:
			columns: 2
			note: '141-UT'
			label: 'Next MCD Term Date'
			readonly: true

	patient_first_name:
		model:
			max: 12
		view:
			columns: -4
			note: '310-CA'
			label: 'First Name'
			readonly: true

	patient_last_name:
		view:
			columns: 4
			note: '311-CB'
			label: 'Last Name'
			readonly: true

	patient_date_of_birth:
		model:
			type: 'date'
		view:
			columns: 4
			note: '304-C4'
			label: 'DOB'
			readonly: true

	transaction_response_status:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '112-AN'
			if:
				'R':
					fields: ['embed_rejmsg']
					sections: ['Reject Message']
		view:
			columns: 4
			note: '112-AN'
			class: 'status'
			label: 'Transaction Status'
			readonly: true

	authorization_number:
		view:
			columns: 4
			note: '503-F3'
			label: 'Auth #'
			readonly: true

	embed_rejmsg:
		model:
			multi: true
			sourcefilter:
				response_uuid:
					'dynamic': '{response_uuid}'
		view:
			embed:
				form: 'ncpdp_response_summary_rejmsg'
				selectable: false
			grid:
				edit: false
				rank: 'none'
				add: 'none'
				fields: ['reject_code']
				label: ['Reject Reason']
				width: [100]
			label: 'Reject Message'
			readonly: true

	embed_addtl_msg:
		model:
			multi: true
			sourcefilter:
				response_uuid:
					'dynamic': '{response_uuid}'
		view:
			embed:
				form: 'ncpdp_response_summary_msg'
				selectable: false
			grid:
				edit: false
				rank: 'none'
				add: 'none'
				fields: ['add_msg', 'add_msg_qualifier']
				label: ['Message', 'Qualifier']
				width: [75, 25]
			label: 'Additional Message'
			readonly: true

	message:
		view:
			control: 'area'
			note: '504-F4'
			label: 'Message'
			readonly: true

	request_json_data:
		model:
			type: 'json'
		view:
			class: 'json-viewer'
			label: ' '
			readonly: true
			columns: 1

	response_json_data:
		model:
			type: 'json'
		view:
			class: 'json-viewer'
			label: ' '
			readonly: true
			columns: 1

	request_d0_raw:
		view:
			control: 'raw'
			label: 'Raw D0 request'
			readonly: true

	response_d0_raw:
		view:
			control: 'raw'
			label: 'Raw D0 response'
			readonly: true
model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['claim_no']
			['patient_id']
			['svc_prov_id']
			['response_uuid']
		]

	name: ['response_uuid']
	sections_group: [
		'Response Summary':
			hide_header: true
			indent: false
			tab: 'Response'
			fields: ['response_uuid', 'patient_id',  'svc_prov_id',
			'claim_no']
		'Details':
			hide_header: true
			indent: false
			tab: 'Response'
			fields: ['response_datetime', 'claim_status', 'group_id', 'plan_id',
			'mcd_id_no', 'mcd_agcy_no', 'card_holder_id', 'network_reimbursement_id',
			'medi_part_d_cov_code', 'cms_low_income_sharing',
			'contract_number', 'formulary_id', 'benefit_id', 'medi_part_d_eff_date',
			'medi_part_d_trm_date', 'patient_first_name', 'patient_last_name',
			'patient_date_of_birth', 'transaction_response_status', 'authorization_number',
			'message']
		'Reject Message':
			hide_header: true
			indent: false
			tab: 'Response'
			fields: ['embed_rejmsg']
		'Additional Message':
			hide_header: true
			indent: false
			tab: 'Response'
			fields: ['embed_addtl_msg']
		'Request':
			indent: false
			tab: 'Raw'
			fields: ['request_json_data']
		'Request D0':
			indent: false
			tab: 'Raw'
			fields: ['request_d0_raw']
		'Response':
			indent: false
			tab: 'Raw'
			fields: ['response_json_data']
		'Response D0':
			indent: false
			tab: 'Raw'
			fields: ['response_d0_raw']
		]

view:
	dimensions:
		width: '90%'
		height: '85%'
	hide_cardmenu: true
	comment: 'NCPDP Eligibility Response Summary'
	find:
		basic: ['patient_id']
		advanced: ['claim_no']
	grid:
		fields: ['response_datetime', 'claim_status', 'transaction_response_status']
		sort: ['-created_on']
	label: 'NCPDP Eligibility Summary'
	open: 'read'