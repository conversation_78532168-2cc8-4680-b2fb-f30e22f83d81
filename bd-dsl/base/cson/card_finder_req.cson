fields:

	patient_id:
		model:
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	type:
		model:
			default: 'CARD<PERSON>NDER'
			source:
				'COMMCF' : 'Commercial'
				'CARDFINDER': 'Commercial and Medicare'
			if:
				'CARDFINDER':
					fields: ['subscriber_ssn']
		view:
			columns: 4
			control: 'radio'
			label: 'PBM Type'

	subscriber_zip:
		model:
			max: 10
			min: 5
		view:
			format: 'us_zip'
			label: 'Zip'
			columns: 'addr_zip'

	clara_mrn:
		model:
			type: 'text'
		view:
			label: 'Clara MRN'
			readonly: true
			offscreen: true

	internal_id:
		model:
			required: false
			type: 'text'
		view:
			label: 'Internal ID'
			readonly: true
			offscreen: true

	subscriber_first_name:
		model:
			type: 'text'
			required: true
		view:
			columns: 4
			label: 'Subscriber First Name'

	subscriber_gender:
		model:
			max: 8
			min: 1
			required: true
			source: ['Female', 'Male']
		view:
			control: 'radio'
			label: 'Sex at birth'
			columns: 4

	subscriber_last_name:
		model:
			type: 'text'
			required: true
		view:
			columns: 4
			label: 'Subscriber Last Name'

	subscriber_dob:
		model:
			type: 'date'
			required: true
		view:
			control: 'input'
			columns: 4
			label: 'Subscriber DOB'

	subscriber_ssn:
		model:
			default: '4444'
			type: 'text'
		view:
			columns: 4
			label: 'Subscriber SSN'
			note: 'Use 4444 if no SSN is available'

	response_json_data:
		model:
			type: 'json'
		view:
			label: 'Response (Json)'
			readonly: true
			offscreen: true

	sent_date:
		model:
			type: 'date'
		view:
			label: 'Last Verification Sent Date'
			readonly:true
			offscreen:true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'pharm']
	indexes:
		unique: ['patient_id']
	sections_group: [
		'Subscriber Information':
			hide_header: true
			indent: false
			fields: ['type', 'subscriber_first_name', 'subscriber_last_name','subscriber_gender','subscriber_dob', 
			'subscriber_ssn', 'subscriber_zip', 'clara_mrn', 'internal_id', 'patient_id', 'response_json_data']
	]
	name: '{clara_mrn} : {subscriber_last_name}, {subscriber_first_name}'

view:
	dimensions:
		width: '80%'
		height: '55%'
	comment: 'Patient > Card Finder'
	find:
		basic: ['clara_mrn']
	grid:
		fields: ['clara_mrn']
	label: 'Patient Card Finder'
