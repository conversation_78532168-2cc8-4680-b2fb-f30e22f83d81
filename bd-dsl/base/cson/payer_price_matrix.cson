fields:

	name:
		model:
			required: true
		view:
			label: 'Shared Contract Name'

	notes:
		view:
			control: 'area'
			label: 'Notes'

	external_id:
		view:
			label: 'External ID'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['billing']
	indexes:
		many: [
			['name']
		]
	reportable: true
	name: '{name} {notes}'

	sections_group: [
		'Shared Contract':
			sections: [
				'Shared Contract Details':
					fields: ['name', 'notes']
			]
		]

view:
	hide_cardmenu: true
	comment: 'Payer > Shared Contract'
	grid:
		fields: ['name', 'notes']
		sort: ['-name']
	label: 'Shared Contracts'
	open: 'read'