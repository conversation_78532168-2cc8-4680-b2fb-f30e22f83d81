fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
			multi: false
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	pat_firstname:
		model:
			prefill: ['patient.firstname']
		view:
			label: "Patient's First Name"

	pat_lastname:
		model:
			prefill: ['patient.lastname']
		view:
			label: "Patient's Last Name"

	date_of_birth:
		model:
			prefill: ['patient.dob']
			type: 'date'
		view:
			label: "Patient's Date of Birth"
			validate: [{
					name: 'DOBValidator'
			}
			]

	patient_mrn:
		model:
			prefill: ['patient.external_id']
		view:
			label: "Patient's MRN"

	patient_street:
		model:
			prefill: ['patient.home_street']
			max: 128
			min: 4
		view:
			label: "Patient's Address"
			columns: 'addr_1'
			class: "api_prefill"
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'patient_street': ['addressLabel']
					'patient_city': ['city']
					'patient_state_id': ['stateCode']
			]

	patient_street2:
		model:
			prefill: ['patient.home_street2']
			max: 128
		view:
			columns: 'addr_2'
			label: "Patient's Address 2"

	patient_city:
		model:
			prefill: ['patient.home_city']
			max: 128
			min: 1
		view:
			columns: 'addr_city'
			label: "Patient's City"

	patient_phone:
		model:
			prefill: ['patient.phone_cell']
			max: 21
		view:
			format: 'us_phone'
			label: "Patient's Cell Phone"

	patient_state_id:
		model:
			source: 'list_us_state'
			sourceid: 'code'
			prefill: ['patient.home_state']
		view:
			columns: 'addr_state'
			label: "Patient's State"

#From Careplan form
# Physician Name
# Phone (Physician Phone)
# Primary Diagnosis
# Secondary Diagnosis

	patient_phy:
		model:
			source: 'physician'
			type: 'int'
			prefill: ['Careplan.referrer_name']
		view:
			label: "Patient's Physician"

	pat_phy_phone:
		model:
			max: 21
			prefill: ['Careplan.referrer_name.phone']
		view:
			format: 'us_phone'
			label: "Patient's Physician Phone"

	primary_dx:
		model:
			prefill: ['Careplan.dx_1']
			if:
				'*':
					fields: ['secondary_dx']
			required: false
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			label: 'Primary Diagnosis'

	secondary_dx:
		model:
			prefill: ['Careplan.dx_2']
			required: false
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			label: 'Secondary Diagnosis'

	pt_allergy_id:
		model:
			source: 'list_fdb_alrgn_mstr'
			multi: true
			sourceid: 'code'
		view:
			label: 'Patient Allergies'

	med_list:
		model:
			subfields:
				name:
					label: 'Name'
					type: 'text'
				dose:
					label: 'Dose'
					type: 'text'
				freq:
					label: 'Frequency'
					type: 'text'
				start:
					label: 'Start Date'
					type: 'date'
				stop:
					label: 'Stop Date'
					type: 'date'
			type: 'json'
		view:
			control: 'grid'
			label: 'Medication List'
			template: 'empty'

	infusion_meds:
		model:
			subfields:
				name:
					label: 'Name'
					type: 'text'
				dose:
					label: 'Dose'
					type: 'text'
				route:
					label: 'Route'
					type: 'text'
				freq:
					label: 'Frequency'
					type: 'text'
			type: 'json'
		view:
			control: 'grid'
			label: 'Home Infusion Medication'

	equipment:
		view:
			label: "MOA:Equipment/Supplies"

	therapy_start:
		model:
			type: 'date'
		view:
			label:"Therapy Start Date"

	therapy_end:
		model:
			type: 'date'
		view:
			label:"Therapy End Date"

	device_order:
		view:
			label: "Vascular Access Device Orders"

	flush_protocol:
		model:
			multi: true
			source: ['Flush access device with NS',
			'Instill Heparin 10units or 100units to maintain patency',
			'Other']
			if:
				'Flush access device with NS':
					fields: ['ns_ml','ns_instruction']
				'Instill Heparin 10units or 100units to maintain patency':
					fields: ['heparin_size', 'heparin_ml','heparin_instruction']
				'Other':
					fields: ['flush_protocol_other']
		view:
			control: 'checkbox'
			label: 'Flush Protocol'

	ns_ml:
		model:
			required: true
			type: 'decimal'
		view:
			label: 'NS mls'

	ns_instruction:
		view:
			label: 'NS Instructions'

	heparin_size:
		model:
			required: true
			source: ['10units', '100units']
		view:
			control: 'radio'
			label: 'Heparin'

	heparin_ml:
		model:
			required: true
			type: 'decimal'
		view:
			label: 'Heparin mls'

	heparin_instruction:
		view:
			label: 'Herapin Instructions'

	flush_protocol_other:
		model:
			required: true
		view:
			label: 'Details'

	labs_list_id:
		model:
			source: 'list_lab'
			required: true
			multi: true
			if:
				'Other':
					fields: ['labs_list_other']
		view:
			control: 'select'
			label: 'Lab Orders'

	labs_list_other:
		model:
			required: true
		view:
			label: 'Lab Orders Other'

	labs_freq:
		view:
			label: 'Lab Frequency'

	home_visits:
		model:
			type: 'decimal'
		view:
			label: 'Home Visits'
			note: 'Per Week'

	total_visits:
		model:
			type: 'decimal'
		view:
			label: 'Total Visits'

	functional_limit:
		view:
			label: 'Functional Limitations'

	diet_need:
		view:
			label: 'Special Dietry or Nutritional Needs'

	safety_concern:
		view:
			label: 'Safety Concern'

	clinician_sign:
		model:
			type: 'json'
		view:
			control: 'esign'
			label: 'Clinician E-Signature'

	clinician_sign_date:
		model:
			type: 'date'
		view:
			label: 'Clinician signature date'
			template: '{{now}}'

	physician_sign:
		model:
			type: 'json'
		view:
			control: 'esign'
			label: 'Physician E-Signature'

	physician_sign_date:
		model:
			type: 'date'
		view:
			label: 'Physician signature date'
			template: '{{now}}'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'pharm']
		delete:     ['admin', 'csr', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'pharm']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	indexes:
		many: [
		]

	name: '{patient_mrn} {primary_dx} {therapy_start} - {therapy_end}'
	sections_group: [
		"Patient's Demographics":
			fields: ['pat_firstname','pat_lastname','date_of_birth','patient_mrn','patient_street','patient_street2', 
					'patient_city','patient_state_id','patient_phone']
		"Patient's Physician Information":
			fields: ['patient_phy','pat_phy_phone']
		'Diagnoses':
			fields: ['primary_dx','secondary_dx']
		"Patient Medical Information":
			fields: ['pt_allergy_id','med_list']
		"Home Infusion Orders":
			fields: ['infusion_meds','equipment','therapy_start','therapy_end']
		"Vascular Access Device":
			fields: ['device_order','flush_protocol','ns_ml','ns_instruction','heparin_size','heparin_ml','heparin_instruction','flush_protocol_other','labs_list_id',
			'labs_list_other','labs_freq']
		"Nursing Services":
			fields: ['home_visits','total_visits','functional_limit','diet_need','safety_concern']
		"Signatures":
			fields: ['clinician_sign','clinician_sign_date','physician_sign','physician_sign_date']
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Care Plan > Medicare HIT'
	find:
		basic: ['date_of_birth']
	grid:
		fields: ['created_on','created_by', 'updated_by']
		sort: ['-date_of_birth']
	label: 'Medicare HIT'
	open: 'read'
