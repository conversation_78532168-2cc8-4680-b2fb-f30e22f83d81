fields:

	type:
		model:
			required: true
			source: ['Patient', 'Payer', 'Site']
			if:
				'Patient':
					fields: ['patient_id']
				'Payer':
					fields: ['payer_id']
				'Site':
					fields: ['site_id']
		view:
			columns: 3
			label: 'Account Type'
			readonly: true

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			columns: 3
			label: 'Patient'
			readonly: true

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
		view:
			columns: 3
			label: 'Site'
			readonly: true

	payer_id:
		model:
			required: true
			source: 'payer'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
			sourcefilter:
				active:
					'static': 'Yes'
		view:
			columns: 3
			label: 'Payer'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm']
		delete:     []
		read:       ['admin', 'cm']
		read_all:   ['admin', 'cm']
		request:    []
		update:     ['cm']
		update_all: ['admin']
		write:      ['admin', 'cm']
	reportable: true
	name: '{type} - {patient_id_auto_name}{site_id_auto_name}{payer_id_auto_name}'
	sections:
		'Account':
			fields: ['type', 'patient_id','site_id','payer_id']
view:
	hide_cardmenu: true
	comment: 'Account'
	find:
		basic: ['type', 'patient_id', 'site_id', 'payer_id']
	grid:
		fields: ['type', 'patient_id', 'site_id', 'payer_id']
		sort: ['-type']
	label: 'Account'
