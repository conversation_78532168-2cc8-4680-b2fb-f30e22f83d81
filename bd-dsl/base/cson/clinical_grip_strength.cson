fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Grip dynamometry
	grip_right_1_pounds:
		model:
			required : true
			max: 150
			min: 0

			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Right Hand Grip Strength 2'

	grip_right_2_pounds:
		model:
			required : true
			max: 150
			min: 0

			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Right Hand Grip Strength 2'

	grip_right_3_pounds:
		model:
			required : true
			max: 150
			min: 0

			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Right Hand Grip Strength 3'

	grip_left_1_pounds:
		model:
			required : true
			max: 150
			min: 0

			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Left Hand Grip Strength 1'

	grip_left_2_pounds:
		model:
			required : true
			max: 150
			min: 0

			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Left Hand Grip Strength 2'

	grip_left_3_pounds:
		model:
			required : true
			max: 150
			min: 0

			rounding: 0.05
			type: 'decimal'
		view:
			note: 'pounds'
			label: 'Left Hand Grip Strength 3'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		encounter_cidp:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Grip Dynamometry':
			note: 'Measure strength of both hand grips 3 times'
			fields: ['grip_right_1_pounds', 'grip_right_2_pounds', 'grip_right_3_pounds',
			'grip_left_1_pounds', 'grip_left_2_pounds', 'grip_left_3_pounds']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Grip Strength Measurements'
	label: 'Patient Clinicals: Grip Strength Measurements'
