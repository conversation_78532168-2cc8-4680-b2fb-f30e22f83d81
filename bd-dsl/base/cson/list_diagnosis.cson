fields:
	name:
		model:
			search: 'A'
			max: 256
			required: true
		view:
			label: 'Name'
			findunique: true

	icd_code:
		model:
			search: 'B'
			max: 12
			required: true
		view:
			label: 'ICD Code'
			findunique: true
			columns: 2

	# Unformatted code (no dot)
	code:
		model:
			max: 12
			required: true
		view:
			label: 'Flat Code'
			findunique: true
			columns: 2

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		many: [
			['name']
			['icd_code']
		]
		unique: [
			['name', 'icd_code', 'code']
			['code']
		] 
	name: '{icd_code} - {name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name', 'icd_code', 'code']

view:
	comment: 'Manage > Diagnosis'
	find:
		basic: ['name', 'icd_code']
	grid:
		fields: ['name', 'icd_code']
		sort: ['name']
	label: 'Diagnosis'
