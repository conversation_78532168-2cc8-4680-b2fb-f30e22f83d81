fields:

	other_payer_referring_provider_identifier:
		model:
			required: true
			multi: true
			source: 'med_claim_rprov_id'
			type: 'subform'
		view:
			note: 'Max 3'
			label: 'Identifier(s)'
			grid:
				add: 'inline'
				edit: true
				fields: ['qualifier', 'identifier']
				label: ['Qualifier', 'ID']
				width: [50, 50]
			max_count: 3

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	sections_group: [
		'Referring Provider':
			hide_header: true
			sections: [
				'Identifier(s)':
					hide_header: true
					indent: false
					fields: ['other_payer_referring_provider_identifier']
			]
	]
	name: ['created_by','created_on']
view:
	dimensions:
		width: '45%'
		height: '45%'
	hide_cardmenu: true
	reference: '2330C'
	comment: 'Referring Provider'
	grid:
		fields: ['created_on', 'created_by']
		sort: ['-created_on']
	label: 'Referring Provider'
	open: 'read'