fields:
	name:
		model:
			required: true
		view:
			label: 'name'

	prospect:
		model:
			required: true
			source: 'sales_prospect'
			type: 'int'
		view:
			label: 'Prospect'

	likelihood:
		model:
			source: ['Low', 'Medium', 'High']
		view:
			control: 'radio'
			label: 'Likelihood?'

	amount:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Amount'

	description:
		view:
			control: 'area'
			label: 'Description'

	close_date:
		model:
			type: 'date'
		view:
			label: 'Close Date'

	target_date:
		model:
			type: 'date'
		view:
			label: 'Target Close Date'

	active:
		model:
			source: ['No', 'Yes']
			default: 'Yes'
		view:
			control: 'radio'
			label: "Active?"

	subform_activity:
		model:
			multi: true
			source: 'sales_activity_log'
			type: 'subform'
		view:
			label: 'Activity Log'
	
model:
	access:
		create:     []
		create_all: ['admin', 'liaison']
		delete:     ['admin', 'liaison']
		read:       ['admin', 'liaison']
		read_all:   ['admin', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'liaison']
		write:      ['admin', 'liaison']
	indexes:
		unique: [
			['name']
		]
	name: ['name']
	sections:
		'Opportunity Info':
			fields: ['name', 'prospect', 'likelihood', 'amount']
		'Status':
			fields: ['active', 'target_date', 'close_date']
		'Activity':
			fields: ['subform_activity']
		'Notes':
			fields: ['description']

view:
	comment: 'Manage > Sales Opportunity'
	find:
		basic: ['name', 'prospect', 'likelihood', 'active', 'target_date', 'close_date']
	grid:
		fields: ['name', 'prospect', 'amount', 'likelihood', 'active', 'target_date', 'close_date']
		sort: ['name']
	label: 'Sales Opportunity'
	open: 'read'