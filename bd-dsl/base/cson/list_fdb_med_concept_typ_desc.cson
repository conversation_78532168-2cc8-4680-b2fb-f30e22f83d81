#TABLE: RMEDCD0_MED_CONCEPT_TYP_DESC
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#MED_CONCEPT_ID_TYP
	med_concept_id_typ:
		model:
			type: 'int'
		view:
			label: 'MED Concept ID Type'
			readonly: true
			columns: 2

	#MED_CONCEPT_ID_TYP_DESC
	med_concept_id_typ_desc:
		view:
			label: 'MED Concept ID Type Description'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{med_concept_id_typ} {med_concept_id_typ_desc}"
	indexes:
		many: [
			['med_concept_id_typ']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['med_concept_id_typ', 'med_concept_id_typ_desc']

view:
	comment: 'Manage > List FDB MED MED Concept ID Type Description Table'
	find:
		basic: ['med_concept_id_typ', 'med_concept_id_typ_desc']
	grid:
		fields: ['med_concept_id_typ', 'med_concept_id_typ_desc']
	label: 'List FDB MED MED Concept ID Type Description Table'
