fields: #RDDIMIN0_NDC_INACTV_DDIM_LINK

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#DDI_NDC
	ddi_ndc:
		view:
			label: 'Drug-Drug Interaction Product National Drug Code'
			readonly: true
			columns: 2

	#DDI_CODEX
	ddi_codex:
		model:
			type: 'int'
		view:
			label: 'Drug-Drug Expanded Interaction Code'
			readonly: true
			columns: 2

	#DDI_NDC_HICSEQN
	ddi_ndc_hicseqn:
		model:
			type: 'int'
		view:
			label: 'Inactive Ingredient Code'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['ddi_ndc', 'ddi_codex', 'ddi_ndc_hicseqn']
	indexes:
		many: [
			['ddi_ndc']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['ddi_ndc', 'ddi_codex', 'ddi_ndc_hicseqn']

view:
	comment: 'Manage > List FDB DDIM NDC/Inactive Ingredient Interaction Code Link Table'
	find:
		basic: ['ddi_ndc', 'ddi_codex', 'ddi_ndc_hicseqn']
	grid:
		fields: ['ddi_ndc', 'ddi_codex', 'ddi_ndc_hicseqn']
	label: 'List FDB DDIM NDC/Inactive Ingredient Interaction Code Link Table'
