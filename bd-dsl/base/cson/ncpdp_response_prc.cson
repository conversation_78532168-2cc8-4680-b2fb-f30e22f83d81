fields:

	ing_cst_paid:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '506-F6'
			label: 'Ingredient Cost Paid'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	amt_ing_cost_contracted:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '148-U8'
			label: 'Ingredient Cost Contracted'
			class: 'numeral money'
			readonly: true
			format: '$0,0.00'

	prof_svc_paid:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '562-J1'
			label: 'Professional Service Amount Paid'
			class: 'numeral money'
			readonly: true
			format: '$0,0.00'

	disp_fee_paid:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '507-F7'
			label: 'Dispensing Fee Paid'
			class: 'numeral money'
			readonly: true
			format: '$0,0.00'

	amt_disp_fee_contracted:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '149-U9'
			label: 'Disp Fee Contracted'
			class: 'numeral money'
			readonly: true
			format: '$0,0.00'

	disp_fee_basis:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '346-HH'
		view:
			columns: 3
			note: '346-HH'
			label: 'Dispense Fee Basis'
			readonly: true

	incv_amt_paid:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '521-FL'
			label: 'Incentive Amount Paid'
			class: 'numeral money'
			readonly: true
			format: '$0,0.00'

	coinsur_amt:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '572-4U'
			label: 'Co-Insurance Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	coinsur_basis:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '573-4V'
		view:
			columns: 3
			note: '573-4V'
			label: 'Co-Insurance Basis'
			readonly: true

	acc_ded_amt:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '512-FC'
			label: 'Accumulated Deductible Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	rem_ded_amt:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '513-FD'
			label: 'Remaining Deductible Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	amt_apld_ded:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '517-FH'
			label: 'Amount Applied to Periodic Deductible'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	amt_copay:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '518-FI'
			label: 'Amount of Copay'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	copay_basis:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '347-HJ'
		view:
			columns: 3
			note: '347-HJ'
			label: 'Copay Basis'
			readonly: true

	hlth_pln_asst:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '129-UD'
			label: 'Health Plan-Funded Assistance Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	rem_ben_amt:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '514-FE'
			label: 'Remaining Benefit Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	amt_exd_ben_max:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '520-FK'
			label: 'Amount Exceeding Periodic Benefit Max'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	spd_acct_remaining:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '128-UC'
			label: 'Spending Account Amount Remaining'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	amt_attr_nonformulary:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '135-UM'
			label: 'Amt Prod Sel/Non-Pref Formulary Sel'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	amt_attr_brd_nonformulary:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '136-UN'
			label: 'Amt Prod Sel/Brand Non-Pref Formulary Sel'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	amt_attr_prov_sel:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '133-UJ'
			label: 'Amt Prov/Network Sel'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	amt_attr_prod_sel:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '134-UK'
			label: 'Amt Prod Sel/Brand Drug'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	tax_exp_ind:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '557-AV'
		view:
			columns: 3
			note: '557-AV'
			label: 'Tax Except Indicator'
			readonly: true

	flat_tax_paid:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '558-AW'
			label: 'Flat Sales Tax Paid'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	flat_tax_basis:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '348-HK'
		view:
			columns: 3
			note: '348-HK'
			label: 'Flat Tax Basis'
			readonly: true

	flat_tax_amt:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '481-HA'
			label: 'Flat Tax Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	sales_tax_paid:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '559-AX'
			label: 'Sales Tax Paid'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	per_sales_tax_paid:
		model:
			type: 'decimal'
			rounding: 0.00001
			max: 100
		view:
			columns: 3
			note: '560-AY'
			label: 'Perc Sales Tax Rate Paid'
			class: 'numeral'
			format: 'percent'
			readonly: true

	sales_tax_basis_paid:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '561-AZ'
		view:
			columns: 3
			note: '561-AZ'
			label: 'Perc Sales Tax Basis'
			class: 'numeral'
			readonly: true

	sales_tax_basis:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '349-HM'
		view:
			columns: 3
			note: '349-HM'
			label: 'Sales Tax Basis'
			readonly: true

	sales_tax_attr:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '523-FN'
			label: 'Amount Attributed to Sales Tax'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	sales_tax_pt:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '575-EQ'
			label: 'Patient Sales Tax Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	sales_tax_plan:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '574-2Y'
			label: 'Plan Sales Tax Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	est_gen_savings:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '577-G3'
			label: 'Estimated Generic Savings'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	subform_opaid:
		model:
			type: 'subform'
			multi: true
			source: 'ncpdp_response_prc_oth'
		view:
			label: 'Other Amount Paid'
			readonly: true

	opayer_amt_rec:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '566-J5'
			label: 'Other Payer Amount Recognized'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	subform_benefit:
		model:
			type: 'subform'
			source: 'ncpdp_response_prc_bft'
			multi: true
		view:
			label: 'Benefit'
			readonly: true

	pro_fee_amt:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '571-NZ'
			label: 'Amount Attributed to Processor Fee'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	amt_coverage_gap:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '137-UP'
			label: 'Amount Attributed To Coverage Gap'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	pt_pay_amt:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '505-F5'
			label: 'Patient Pay Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_paid:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '509-F9'
			label: 'Total Amount Paid'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	rem_basis:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '522-FM'
		view:
			columns: 3
			note: '522-FM'
			label: 'Basis of Reimbursement Determination'
			readonly: true
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['ing_cst_paid', 'amt_ing_cost_contracted']
	sections_group: [
		'Pricing':
			sections: [
				'Totals':
					fields: ['total_paid', 'rem_basis', 'pt_pay_amt']

				'Payment Details':
					indent: false
					fields: ['ing_cst_paid', 'amt_ing_cost_contracted', 'prof_svc_paid',
					'disp_fee_paid', 'amt_disp_fee_contracted', 'disp_fee_basis', 'incv_amt_paid',
					'pro_fee_amt', 'amt_coverage_gap']

				'Patient Co-Insurance Details':
					indent: false
					fields: ['coinsur_amt', 'coinsur_basis', 'acc_ded_amt', 'rem_ded_amt',
					'amt_apld_ded', 'amt_copay', 'copay_basis', 'hlth_pln_asst', 'est_gen_savings',
					'rem_ben_amt', 'amt_exd_ben_max', 'spd_acct_remaining']

				'Formulary':
					indent: false
					fields: ['amt_attr_nonformulary', 'amt_attr_brd_nonformulary', 'amt_attr_prov_sel', 'amt_attr_prod_sel']

				'Tax':
					indent: false
					fields: ['tax_exp_ind', 'flat_tax_paid', 'flat_tax_basis', 'flat_tax_amt',
					'per_sales_tax_paid', 'sales_tax_paid', 'sales_tax_basis_paid', 'sales_tax_basis',
					'sales_tax_attr', 'sales_tax_pt', 'sales_tax_plan']

				'Other Amount Paid':
					indent: false
					fields: ['subform_opaid']

				'COB':
					indent: false
					fields: ['opayer_amt_rec']

				'Benefits Stage':
					indent: false
					fields: ['subform_benefit']

			]
		]

view:
	dimensions:
		width: '75%'
		height: '75%'
	hide_cardmenu: true
	comment: 'Response Pricing'
	grid:
		fields: ['total_paid', 'rem_basis', 'pt_pay_amt', 'amt_copay', 'coinsur_amt', 'amt_coverage_gap']
		sort: ['-created_on']
	label: 'Response Pricing'
	open: 'read'
