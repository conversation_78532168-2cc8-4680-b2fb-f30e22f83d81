fields:
	close_no:
		model:
			type: 'text'
		view:
			label: 'Closing #'
			readonly: true
			offscreen: true

	applied_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Applied Date/Time'
			readonly: true
			offscreen: true

	transaction_no:
		model:
			type: 'text'
		view:
			label: 'Transaction No'
			readonly: true
			offscreen: true

	zeroed:
		model:
			source: ['Yes']
		view:
			columns: 4
			label: 'Zero Transaction?'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						zeroed: 'Yes'
					dest: 'zeroed_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						zeroed: 'Yes'
					dest: 'zeroed_by'
				}
			]

	zeroed_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 4
			label: 'Zeroed Date/Time'
			readonly: true
			template: '{{now}}'

	zeroed_by:
		model:
			source: 'user'
		view:
			label: 'Zeroed By'
			readonly: true
			columns: 4

	zeroed_reason_id:
		model:
			required: true
			source: 'list_void_reason_billing'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			label: 'Zeroed Reason'
			columns: 2

	charge_line_id:
		model:
			required: true
			source: 'ledger_charge_line'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
		view:
			label: 'Charge Line ID'
			readonly: true
			offscreen: true
			validate: [
				name: 'LoadARChargeLineInfo'
			]

	insurance_id:
		model:
			source: 'patient_insurance'
		view:
			control: 'select'
			label: 'Insurance'
			readonly: true
			offscreen: true

	payer_id:
		model:
			source: 'payer'
		view:
			label: 'Payer'
			readonly: true
			columns: 2

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			readonly: true
			offscreen: true
			label: 'Patient'

	firstname:
		view:
			label: 'First Name'
			readonly: true
			columns: 4

	lastname:
		view:
			label: 'Last Name'
			readonly: true
			columns: 4

	dob:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'DOB'
			readonly: true

	mrn:
		view:
			label: 'MRN'
			readonly: true
			columns: 4

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			readonly: true
			columns: 4

	invoice_no:
		model:
			required: true
			type: 'text'
		view:
			label: 'Invoice #'
			columns: 4
			readonly: true

	charge_no:
		model:
			required: true
			type: 'text'
		view:
			label: 'Charge #'
			columns: 4
			readonly: true

	rx_no:
		model:
			type: 'text'
		view:
			label: ''
			columns: 4
			readonly: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
		view:
			form_link_enabled: true
			label: 'Inventory ID'
			columns: 2
			readonly: true

	hcpc_code:
		view:
			columns: 4
			label: 'HCPC'
			readonly: true

	formatted_ndc:
		view:
			label: 'NDC'
			columns: 4
			readonly: true

	account_id:
		model:
			source: 'billing_account'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
		view:
			label: 'Account'
			readonly: true
			offscreen: true

	billed:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
			min: 0
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Billed Amount'
			readonly: true

	expected:
		model:
			rounding: 0.01
			type: 'decimal'
			min: 0
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Expected'
			readonly: true

	paid:
		model:
			default: 0.00
			rounding: 0.01
			type: 'decimal'
			min: 0
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Paid'
			readonly: true

	total_adjusted:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Adjusted Amount' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_balance_due:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Balance Due'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	post_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 4
			label: 'Post Date/Time'
			template: '{{now}}'
			validate: [
				{
					name: 'ValidatePostDate'
				}
			]

	transaction_type:
		model:
			required: true
			source: ['Adjustment', 'Writeoff', 'Payment', 'Cash Allocation']
			if:
				'Cash Allocation':
					fields: ['unapplied_cash_balance', 'amount']
				'Adjustment':
					fields: ['adjustment_reason_id', 'amount', 'adjustment_for']
				'Writeoff':
					fields: ['writeoff_reason_id', 'amount']
					prefill:
						amount: '{total_balance_due}'
				'Payment':
					fields: ['payment_method', 'amount']
		view:
			control: 'radio'
			label: 'Transaction Type'
			columns: 4

	adjustment_for:
		model:
			required: true
			source: ['AR', 'COGS']
		view:
			columns: 4
			control: 'radio'
			label: 'Adjustment For'

	adjustment_type:
		model:
			required: true
			source: ['Credit', 'Debit']
		view:
			columns: 4
			control: 'radio'
			label: 'Adjustment Type'

	adjustment_reason_id:
		model:
			required: true
			source: 'list_adjustment_reason'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Adjustment Reason'

	writeoff_reason_id:
		model:
			required: true
			source: 'list_writeoff_reason'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Writeoff Reason'

	payment_method:
		model:
			required: true
			source: ['Check', 'Cash', 'Credit Card', 'Debit Card', 'ERN', 'Other']
			if:
				'Check':
					fields: ['check_no']
				'Other':
					fields: ['other_payment_method']
		view:
			control: 'radio'
			columns: 4
			label: 'Payment Method'

	other_payment_method:
		model:
			required: true
		view:
			columns: 4
			label: 'Other Payment Method'

	check_no:
		model:
			required: true
		view:
			columns: 4
			label: 'Check Number(s)'

	unapplied_cash_balance:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Cash Available'
			readonly: true
			validate: [
				name: 'ARApplyUnappliedCash'
			]

	amount:
		model:
			required: true
			rounding: 0.01
			min: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Amount'
			validate: [
				name: 'ValidateARActionAmount'
			]

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr']
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr']
		read_all:   ['admin', 'cm', 'cma', 'csr']
		request:    []
		update:     []
		update_all: []
		write:      ['admin', 'cm', 'cma', 'csr']
	bundle: ['audit']
	indexes:
		many: [
			['charge_line_id'],
			['insurance_id'],
			['payer_id'],
			['patient_id'],
			['site_id'],
			['invoice_no'],
			['charge_no']
			['rx_no'],
			['inventory_id'],
			['hcpc_code'],
			['formatted_ndc'],
			['account_id'],
			['transaction_type'],
			['adjustment_type'],
			['adjustment_reason_id'],
			['writeoff_reason_id'],
			['payment_method'],
			['post_datetime'],
			['zeroed_reason_id'],
			['zeroed'],
			['adjustment_for']
		]
	name: 'AR Transaction'
	sections_group: [
			'AR Transaction':
				hide_header: true
				indent: false
				fields: ['close_no','post_datetime', 'charge_line_id', 'insurance_id', 'payer_id', 'patient_id',
				'firstname', 'lastname', 'dob', 'mrn', 'site_id', 'invoice_no', 'charge_no',
				'rx_no', 'inventory_id', 'hcpc_code', 'formatted_ndc', 'account_id',
				'billed', 'expected', 'paid', 'total_adjusted', 'total_balance_due',
				'transaction_type', 'adjustment_for', 'adjustment_type', 'adjustment_reason_id',
				'writeoff_reason_id', 'payment_method', 'check_no', 'amount']
		'Zero':
			modal: true
			note: 'Zeroing a transaction will add a reversal adjustment in the finance ledger.'
			fields: ['zeroed', 'zeroed_datetime', 'zeroed_by', 'zeroed_reason_id']

	]

view:
	block:
		validate: [
			name: 'ClosedOrZeroedBlock'
		]
	dimensions:
		width: '90%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Line Item Transaction'
	find:
		basic: ['patient_id', 'payer_id', 'inventory_id', 'site_id', 'invoice_no', 'charge_no', 'rx_no', 'transaction_type']
	grid:
		fields: ['firstname', 'lastname', 'invoice_no', 'charge_no', 'transaction_type', 'amount']
		width: [20, 20, 15, 15, 20, 10]
		sort: ['-id']
	label: 'Line Item Transaction'
	open: 'read'
	block:
		update:
			if: 'created_on'
			except: ['empty']