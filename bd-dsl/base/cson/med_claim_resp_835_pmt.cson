fields:

	patient_control_number:
		view:
			columns: 4
			label: 'Patient Control Number'
			note: 'CLP09'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimPaymentInfo.patientControlNumber'

	claim_status_code:
		model:
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CLP02'
		view:
			columns: 4
			label: 'Claim Status Code'
			note: 'CLP01'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimPaymentInfo.claimStatusCode'

	claim_filing_indicator_code:
		view:
			columns: 4
			label: 'Claim Filing Indicator'
			note: 'CLP06'
			readonly: true
			_meta:	
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimPaymentInfo.claimFilingIndicatorCode'

	payer_claim_control_number:
		view:
			columns: 4
			label: 'Payer Claim Control Number'
			note: 'CLP07'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimPaymentInfo.payerClaimControlNumber'

	claim_frequency_code:
		view:
			columns: 4
			label: 'Claim Frequency Code'
			note: 'CLP08'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimPaymentInfo.claimFrequencyCode'

	total_claim_charge_amount:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Charge Amount'
			note: 'CLP03'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimPaymentInfo.totalClaimChargeAmount'

	claim_payment_amount:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Claim Payment Amount'
			note: 'CLP04'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimPaymentInfo.claimPaymentAmount'

	patient_responsibility_amount:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Patient Responsibility'
			note: 'CLP05'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimPaymentInfo.patientResponsibilityAmount'

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: 'Claim Payment Details'
	sections:
		'Claim Payment Details':
			fields: ['patient_control_number', 'claim_status_code', 'claim_filing_indicator_code',
			'payer_claim_control_number', 'claim_frequency_code', 'total_claim_charge_amount',
			'claim_payment_amount', 'patient_responsibility_amount']
view:
	dimensions:
		width: '85%'
		height: '45%'
	hide_cardmenu: true
	comment: 'Claim Payment Details'
	grid:
		fields: ['claim_status_code', 'total_claim_charge_amount', 'claim_payment_amount', 'patient_responsibility_amount']
		label: ['Status', 'Total Charge $', 'Total Paid $', 'Total Pt Pay $']
		width: [40, 20, 20, 20]
		sort: ['-created_on']
	label: 'Claim Payment Details'
	open: 'read'