#TABLE: RDAMGX0_ALRGN_GRP_XSENSE_LINK
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#DAM_ALRGN_XSENSE
	dam_alrgn_xsense:
		model:
			type: 'int'
		view:
			label: 'DAM Cross-Sensitive Allergen Group Code (Stable ID)'
			readonly: true
			columns: 2

	#DAM_ALRGN_GRP
	dam_alrgn_grp:
		model:
			type: 'int'
		view:
			label: 'DAM Specific Allergen Group Code (Stable ID)'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{dam_alrgn_xsense} {dam_alrgn_grp}"
	indexes:
		many: [
			['dam_alrgn_xsense']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['dam_alrgn_xsense', 'dam_alrgn_grp']

view:
	comment: 'Manage > List FDB DAM Allergen Group Cross/Sensitivity Link Table'
	find:
		basic: ['dam_alrgn_xsense', 'dam_alrgn_grp']
	grid:
		fields: ['dam_alrgn_xsense', 'dam_alrgn_grp']
	label: 'List FDB DAM Allergen Group Cross/Sensitivity Link Table'
