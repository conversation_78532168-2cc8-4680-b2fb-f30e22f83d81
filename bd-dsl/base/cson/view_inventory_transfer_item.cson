fields:

	site_id:
		model:
			prefill: ['parent.site_id']
		view:
			columns: 3
			label: 'From Site'
			findmulti: true
			offscreen: true
			readonly: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
			query: 'select_inv_in_stock'
			querytemplate: 'inventoryTemplate'
			sourcefilter:
				type:
					'static': ['Drug', 'Supply', 'Equipment Rental']
				active:
					'static': 'Yes'
		view:
			label: 'Transferred Item'
			columns: 3
			class: 'select_prefill'
			validate: [
				name: 'TransferCheckQuantity'
			]
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'lot_tracking': ['lot_tracking'],
					'serial_tracking': ['serial_tracking'],
					'type': ['type']
			]

	type:
		model:
			source: ['Drug', 'Supply', 'Equipment Rental', 'Billable']
			if:
				'Equipment Rental':
					readonly:
						fields: ['quantity']
					prefill:
						'quantity': '1'
		view:
			label: 'Type'
			readonly: true
			offscreen: true

	lot_tracking:
		model:
			source: ['Yes']
			if:
				'Yes':
					require_fields: ['lot_id']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Track Lots?'
			readonly: true
			offscreen: true

	serial_tracking:
		model:
			source: ['Yes']
			if:
				'Yes':
					require_fields: ['serial_id']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Track Serial Numbers?'
			readonly: true
			offscreen: true

	lot_no:
		view:
			label: 'Lot No'
			readonly: true
			offscreen: true

	lot_id:
		model:
			required: false
			source: 'inventory_lot'
			sourcefilter:
				inventory_id:
					'dynamic': '{inventory_id}'
				site_id:
					'dynamic': '{site_id}'
			query: 'select_lot_in_stock'
			querytemplate: 'inventoryTemplate'
		view:
			columns: 3
			label: 'Lot'
			class: 'select_prefill'
			validate: [
				{
					name: 'TransferCheckQuantity'
				},
				{
					name: 'LockQuantity'
				}
			]
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory_lot/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'lot_no': ['lot_no']
			]

	serial_id:
		model:
			required: false
			source: 'inventory_serial'
			sourcefilter:
				inventory_id:
					'dynamic': '{inventory_id}'
				site_id:
					'dynamic': '{site_id}'
				lot_no:
					'dynamic': '{lot_id}'
			query: 'select_serial_in_stock'
			querytemplate: 'inventoryTemplate'
		view:
			columns: 3
			label: 'Serial'
			validate: [
				{
					name: 'TransferCheckQuantity'
				},
				{
					name: 'LockQuantity'
				}
			]

	quantity:
		model:
			min: 1
			required: true
			rounding: 1
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 3
			label: 'Quantity'
			validate: [
				name: 'TransferCheckQuantity'
			]

	expected_date:
		model:
			type: 'date'
		view:
			columns: 3
			control: 'input'
			label: 'Expected Date'

model:
	access:
		create:     []
		create_all: ['admin','pharm','csr','cm']
		delete:     []
		read:       ['admin','pharm','csr','cm']
		read_all:   ['admin','pharm','csr','cm']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      ['admin','pharm','csr','cm']

	name: '{inventory_id} {quantity}'
	sections:
		'Inventory Transfer Item':
			fields: ['site_id', 'inventory_id', 'lot_id', 'serial_id', 'quantity', 'expected_date', 'serial_tracking', 'lot_tracking', 'type']

view:
	comment: 'Inventory Transfer Item'
	grid:
		fields: ['inventory_id', 'quantity', 'lot_id', 'serial_id']
		sort: ['-id']
	label: 'Inventory Transfer Item'
	open: 'edit'