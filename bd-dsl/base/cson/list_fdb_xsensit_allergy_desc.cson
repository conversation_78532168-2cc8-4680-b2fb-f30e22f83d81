#TABLE: RDAMCSD1_XSENSIT_ALLERGY_DESC
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#DAM_ALRGN_XSENSE
	dam_alrgn_xsense:
		model:
			type: 'int'
		view:
			label: 'DAM Cross-Sensitive Allergen Group Code (Stable ID)'
			readonly: true
			columns: 2

	#DAM_ALRGN_XSENSE_DESC
	dam_alrgn_xsense_desc:
		view:
			label: 'DAM Cross-Sensitive Allergen Group Code Description'
			readonly: true
			columns: 2

	#DAM_XSENSE_POTENTIAL_INCTV_IND	
	dam_xsense_potential_inctv_ind:
		model:
			type: 'int'
		view:
			label: 'DAM Cross-Sensitive Allergen Group Potentially Inactive Indicator'
			readonly: true
			columns: 2

	#DAM_ALRGN_XSENSE_STATUS_CD
	dam_alrgn_xsense_status_cd:
		model:
			type: 'int'
		view:
			label: 'DAM Cross-Sensitive Allergen Group Status Code'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{dam_alrgn_xsense_desc}"
	indexes:
		many: [
			['dam_alrgn_xsense']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['dam_alrgn_xsense', 'dam_alrgn_xsense_desc', 'dam_xsense_potential_inctv_ind', 'dam_alrgn_xsense_status_cd']

view:
	comment: 'Manage > List FDB DAM Cross-Sensitive Allergen Group Code Description Table'
	find:
		basic: ['dam_alrgn_xsense_desc', 'dam_xsense_potential_inctv_ind', 'dam_alrgn_xsense']
	grid:
		fields: ['dam_alrgn_xsense_desc', 'dam_xsense_potential_inctv_ind', 'dam_alrgn_xsense']
	label: 'List FDB DAM Cross-Sensitive Allergen Group Code Description Table'
