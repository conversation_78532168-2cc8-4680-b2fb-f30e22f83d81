#TABLE: RPEINR0_NDC_RT_RELATION
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#FDB Route ID
	rt_id:
		model:
			required: true
		view:
			label: 'FDB Route ID'
			findunique: true
			columns: 2

	#CLARA Route
	route_id:
		model:
			required: true
			source: 'list_route'
			sourceid: 'code'
		view:
			label: 'Route'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['rt_id', 'route_id']
	indexes:
		many: [
			['rt_id']
			['route_id']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['rt_id', 'route_id']

view:
	comment: 'Manage > List FDB Route ID -> Clara Route ID Crosswalk'
	find:
		basic: ['rt_id', 'route_id']
	grid:
		fields: ['rt_id', 'route_id']
	label: 'List FDB Route ID -> Clara Route ID  Crosswalk'
