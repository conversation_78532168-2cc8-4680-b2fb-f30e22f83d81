fields:

	response_id:
		model:
			type: 'int'
			source: 'ncpdp_response'
		view:
			label: 'NCPDP Response'
			readonly: true
			offscreen: true

	response_uuid:
		model:
			type: 'text'
		view:
			label: 'Response UUID'
			readonly: true
			offscreen: true

	patient_id:
		model:
			type: 'int'
			source: 'patient'
		view:
			label: 'Patient'
			columns: 4
			readonly: true

	site_id:
		model:
			type: 'int'
			source: 'site'
		view:
			label: 'Site'
			columns: 4
			readonly: true

	svc_prov_id:
		view:
			columns: 4
			label: 'Pharmacy Identifier'
			readonly: true

	invoice_no:
		model:
			required: false
			type: 'text'
		view:
			label: 'Invoice Number'
			columns: 4
			readonly: true

	claim_no:
		model:
			required: false
			type: 'text'
		view:
			label: 'Claim #'
			columns: 4
			readonly: true

	rx_no:
		view:
			label: 'Prescription Number'
			readonly: true
			columns: 4

	date_of_service:
		model:
			type: 'date'
		view:
			label: 'Date of Service'
			columns: 4
			readonly: true

	day_supply:
		model:
			type: 'int'
		view:
			columns: 4
			label: 'Days Supplied'
			readonly: true

	total_cost:
		model:
			rounding: 0.01
			type: 'decimal'
			min: 0
		view:
			columns: 4
			label: 'Cost'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	reimbursement:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
			min: 0.00
		view:
			columns: 4
			label: 'Reimbursement' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_profit:
		model:
			required: false
			type: 'decimal'
			default: 0.00
			min: 0.00
		view:
			columns: 4
			label: 'Profit' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	margin:
		model:
			type: 'decimal'
			rounding: 0.00001
			max: 100
		view:
			columns: 4
			label: 'Margin'
			class: 'numeral discount'
			format: 'percent'
			readonly: true

	embed_pricing:
		model:
			multi: true
			sourcefilter:
				response_uuid:
					'dynamic': '{response_uuid}'
		view:
			embed:
				form: 'ncpdp_response_summary_li'
				selectable: false
			grid:
				edit: false
				rank: 'none'
				add: 'none'
				fields: ['type', 'billed_amount', 'paid_amount', 'discount_amount', 'paid_percentage']
				label: ['Type', 'Billed', 'Paid', 'Discount', 'Paid %']
				width: [40, 15, 15, 15, 15]
			label: 'Pricing'
			readonly: true

	total_billed:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 9999999.99
		view:
			columns: 4
			label: 'Tot Billed'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_paid:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 9999999.99
		view:
			columns: 4
			label: 'Tot Paid'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_discount:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 9999999.99
		view:
			columns: 4
			label: 'Tot Discount'
			note: '%'
			class: 'numeral discount'
			format: '$0,0.00'
			readonly: true

	total_paid_percentage:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 100
		view:
			columns: 4
			label: 'Tot Paid'
			note: '%'
			class: 'numeral discount'
			format: 'percent'
			readonly: true

	inventory_id:
		model:
			type: 'int'
			source: 'inventory'
		view:
			label: 'Drug'
			readonly: true
			columns: 2

	formatted_ndc:
		view:
			label: 'NDC'
			readonly: true
			columns: 4

	quantity_dispensed:
		model:
			min: 0.001
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Quantity (metric)'
			readonly: true

	response_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Response Date/Time'
			columns: -4
			readonly: true

	claim_status:
		model:
			source: ["Approved", "Benefit", "Payable", "Margin", "Captured", "Rejected", "Reversed", "Reversal Rejected", "Rebill Rejected", "PA Deferred", "Duplicate"]
		view:
			columns: 4
			class: 'status'
			control: 'radio'
			label: 'Claim Status'
			readonly: true

	group_id:
		model:
			max: 15
		view:
			columns: -4
			note: '301-C1'
			label: 'Group Number'
			readonly: true

	network_reimbursement_id:
		view:
			columns: 4
			note: '545-2F'
			label: 'Network Rem ID'
			readonly: true

	patient_first_name:
		model:
			max: 12
		view:
			columns: -4
			note: '310-CA'
			label: 'First Name'
			readonly: true

	patient_last_name:
		view:
			columns: 4
			note: '311-CB'
			label: 'Last Name'
			readonly: true

	patient_date_of_birth:
		model:
			type: 'date'
		view:
			columns: 4
			note: '304-C4'
			label: 'DOB'
			readonly: true

	transaction_response_status:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '112-AN'
			if:
				'R':
					fields: ['embed_rejmsg']
					sections: ['Reject Message']
		view:
			columns: 4
			note: '112-AN'
			class: 'status'
			label: 'Transaction Status'
			readonly: true

	authorization_number:
		view:
			columns: 4
			note: '503-F3'
			label: 'Auth #'
			readonly: true

	embed_rejmsg:
		model:
			multi: true
			sourcefilter:
				response_uuid:
					'dynamic': '{response_uuid}'
		view:
			embed:
				form: 'ncpdp_response_summary_rejmsg'
				selectable: false
			grid:
				edit: false
				rank: 'none'
				add: 'none'
				fields: ['reject_code']
				label: ['Reject Reason']
				width: [100]
			label: 'Reject Message'
			readonly: true

	embed_addtl_msg:
		model:
			multi: true
			sourcefilter:
				response_uuid:
					'dynamic': '{response_uuid}'
		view:
			embed:
				form: 'ncpdp_response_summary_msg'
				selectable: false
			grid:
				edit: false
				rank: 'none'
				add: 'none'
				fields: ['add_msg', 'add_msg_qualifier']
				label: ['Message', 'Qualifier']
				width: [75, 25]
			label: 'Additional Message'
			readonly: true

	rx_svc_no_ref_qualifier:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '455-EM'
		view:
			columns: 4
			note: '455-EM'
			label: 'Svc Ref Qual'
			readonly: true

	rx_svc_no:
		model:
			type: 'decimal'
		view:
			columns: 4
			note: '402-D2'
			label: 'Svc Ref #'
			readonly: true

	transaction_ref_no:
		view:
			columns: 4
			note: '880-K5'
			label: 'Trans Ref #'
			readonly: true

	message:
		view:
			control: 'area'
			note: '504-F4'
			label: 'Message'
			readonly: true

	pt_pay_amt:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: -4
			note: '505-F5'
			label: 'Patient Pay Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	ing_cst_paid:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 4
			note: '506-F6'
			label: 'Ing Cost Paid'
			class: 'numeral money'
			format: '$0,0.00'

	disp_fee_paid:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 4
			note: '507-F7'
			label: 'Disp Fee Paid'
			class: 'numeral money'
			readonly: true
			format: '$0,0.00'

	total_paid_amount:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 4
			note: '509-F9'
			label: 'Tot Amt Paid'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_awp:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: -4
			label: 'Total AWP Billed'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_awp_percentage:
		model:
			type: 'decimal'
			rounding: 0.00001
			max: 100
		view:
			columns: 4
			label: 'Total AWP -%'
			class: 'numeral discount'
			format: 'percent'
			readonly: true

	request_json_data:
		model:
			type: 'json'
		view:
			class: 'json-viewer'
			label: ' '
			readonly: true
			columns: 1

	response_json_data:
		model:
			type: 'json'
		view:
			class: 'json-viewer'
			label: ' '
			readonly: true
			columns: 1

	request_d0_raw:
		view:
			control: 'raw'
			label: 'Raw D0 request'
			readonly: true

	response_d0_raw:
		view:
			control: 'raw'
			label: 'Raw D0 response'
			readonly: true
model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['invoice_no']
			['claim_no']
			['patient_id']
			['site_id']
			['svc_prov_id']
			['response_uuid']
		]

	name: ['response_uuid']
	sections_group: [
		'Response Summary':
			hide_header: true
			indent: false
			tab: 'Response'
			fields: ['response_uuid', 'patient_id', 'site_id', 'svc_prov_id', 'invoice_no',
			'claim_no', 'rx_no', 'date_of_service', 'day_supply',
			'total_cost', 'reimbursement', 'total_profit', 'margin']
		'Pricing Breakdown':
			hide_header: true
			indent: false
			tab: 'Response'
			fields: ['embed_pricing']
		'Totals':
			hide_header: true
			indent: false
			tab: 'Response'
			fields: ['total_billed', 'total_paid', 'total_discount', 'total_paid_percentage']
		'Details':
			hide_header: true
			indent: false
			tab: 'Response'
			fields: ['inventory_id', 'formatted_ndc', 'quantity_dispensed',
			'response_datetime', 'claim_status', 'group_id', 'network_reimbursement_id',
			'patient_first_name', 'patient_last_name', 'patient_date_of_birth',
			'transaction_response_status', 'authorization_number',
			'rx_svc_no_ref_qualifier', 'rx_svc_no', 'transaction_ref_no',
			'message', 'pt_pay_amt', 'ing_cst_paid', 'disp_fee_paid',
			'total_paid_amount', 'total_awp', 'total_awp_percentage']
		'Reject Message':
			hide_header: true
			indent: false
			tab: 'Response'
			fields: ['embed_rejmsg']
		'Additional Message':
			hide_header: true
			indent: false
			tab: 'Response'
			fields: ['embed_addtl_msg']
		'Request':
			indent: false
			tab: 'Raw'
			fields: ['request_json_data']
		'Request D0':
			indent: false
			tab: 'Raw'
			fields: ['request_d0_raw']
		'Response':
			indent: false
			tab: 'Raw'
			fields: ['response_json_data']
		'Response D0':
			indent: false
			tab: 'Raw'
			fields: ['response_d0_raw']
		]

view:
	dimensions:
		width: '90%'
		height: '85%'
	hide_cardmenu: true
	comment: 'NCPDP Response Summary'
	find:
		basic: ['patient_id', 'site_id', 'inventory_id']
		advanced: ['invoice_no', 'claim_no']
	grid:
		fields: ['response_datetime', 'claim_status', 'transaction_response_status', 'total_paid_amount', 'pt_pay_amt', 'margin']
		sort: ['-created_on']
	label: 'NCPDP Response Summary'
	open: 'read'