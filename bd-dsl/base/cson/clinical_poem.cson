fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_poem.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	
	itchy:
		model:
			multi:false
			required: true
			source:
				0: 'No days'
				1: '1-2 days'
				2: '3-4 days'
				3: '5-6 days'
				4: 'Every day'
		view:
			control: 'checkbox'
			label: 'Over the last week, on how many days has your skin been itchy because of your eczema?'
			validate: [
					name: 'POEMScoreValidate'
			]

	sleep:
		model:
			multi:false
			required: true
			source:
				0: 'No days'
				1: '1-2 days'
				2: '3-4 days'
				3: '5-6 days'
				4: 'Every day'
		view:
			control: 'checkbox'
			label: 'Over the last week, on how many nights has your sleep been disturbed because of your eczema?'
			validate: [
					name: 'POEMScoreValidate'
			]

	bleed:
		model:
			multi:false
			required: true
			source:
				0: 'No days'
				1: '1-2 days'
				2: '3-4 days'
				3: '5-6 days'
				4: 'Every day'
		view:
			control: 'checkbox'
			label: 'Over the last week, on how many days has your skin been bleeding because of your eczema?'
			validate: [
					name: 'POEMScoreValidate'
			]

	weeping:
		model:
			multi:false
			required: true
			source:
				0: 'No days'
				1: '1-2 days'
				2: '3-4 days'
				3: '5-6 days'
				4: 'Every day'
		view:
			control: 'checkbox'
			label: 'Over the last week, on how many days has your skin been weeping or oozing clear fluid because of your eczema?'
			validate: [
					name: 'POEMScoreValidate'
			]

	cracked:
		model:
			multi:false
			required: true
			source:
				0: 'No days'
				1: '1-2 days'
				2: '3-4 days'
				3: '5-6 days'
				4: 'Every day'
		view:
			control: 'checkbox'
			label: 'Over the last week, on how many days has your skin been cracked because of your eczema?'
			validate: [
					name: 'POEMScoreValidate'
			]

	flaking:
		model:
			multi:false
			required: true
			source:
				0: 'No days'
				1: '1-2 days'
				2: '3-4 days'
				3: '5-6 days'
				4: 'Every day'
		view:
			control: 'checkbox'
			label: 'Over the last week, on how many days has your skin been flaking off because of your eczema?'
			validate: [
					name: 'POEMScoreValidate'
			]

	dry:
		model:
			multi:false
			required: true
			source:
				0: 'No days'
				1: '1-2 days'
				2: '3-4 days'
				3: '5-6 days'
				4: 'Every day'
		view:
			control: 'checkbox'
			label: 'Over the last week, on how many days has your skin felt dry or rough because of your eczema??'
			validate: [
					name: 'POEMScoreValidate'
			]

	poem_score:
		model:
			rounding: 0.1
			type: 'decimal'
		view:
			note: '0 to 2 = Clear or almost clear\n3 to 7 = Mild eczema\n8 to 16 = Moderate eczema\n17 to 24 = Severe eczema\n25 to 28 = Very severe eczema'
			label: 'POEM Score'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_poem:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Patient-Oriented Eczema Measure (POEM)':
			fields: ['last_assessment_date', 'assessment_date']
		'Questionnaire':
			note: 'Ask the patient the answer that best describes their usual abilities OVER THE COURSE OF THE LAST WEEK'
			fields: ['itchy', 'sleep', 'bleed', 'weeping', 'cracked', 'flaking', 'dry', 'poem_score']
			prefill: 'clinical_poem'

view:
	hide_cardmenu: true
	find:
		basic: ['assessment_date']
	grid:
		fields: ['created_on', 'created_by', 'assessment_date', 'poem_score']
		sort: ['-id']
	comment: 'Patient > Careplan > Clinical > POEM'
	label: 'Patient-Oriented Eczema Measure (POEM)'
