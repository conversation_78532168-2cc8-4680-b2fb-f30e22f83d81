fields:
	organization_name:
		view:
			columns: 4
			label: 'Organization Name'
			note: 'NM103'
			readonly: true

	payer_identification:
		view:
			columns: 4
			label: 'Payer Identification'
			note: 'NM109 NM108=PI'
			readonly: true

	centers_for_medicare_and_medicaid_service_plan_id:
		view:
			columns: 4
			label: 'CMS Plan ID'
			note: 'NM109 NM108=XV'
			readonly: true

	status_information_effective_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Status Information Effective Date'
			reference: 'Loop 2200C STC02'
			readonly: true

	health_care_claim_status_category_code:
		view:
			columns: -4
			label: 'Health Care Claim Status Category Code'
			reference: 'STC01-1, STC010-1, STC11-1'
			readonly: true

	health_care_claim_status_category_code_value:
		view:
			columns: 2
			label: 'Health Care Claim Status Category Code Value'
			reference: 'STC01-1, STC010-1, STC11-1'
			readonly: true

	status_code:
		view:
			columns: -4
			label: 'Status Code'
			reference: 'STC01-2, STC010-2, STC11-2'
			readonly: true

	status_code_value:
		view:
			columns: 2
			label: 'Status Code Value'
			reference: 'STC01-2, STC010-2, STC11-2'
			readonly: true

	entity_identifier_code:
		view:
			columns: -4
			label: 'Entity Identifier Code'
			reference: 'STC01-3, STC010-3, STC11-3'
			readonly: true

	entity_identifier_code_value:
		view:
			columns: 4
			label: 'Entity Identifier Code Value'
			reference: 'STC01-3, STC010-3, STC11-3'
			readonly: true

	payer_contact_information:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_py_cont'
		view:
			label: 'Payer Contact Information'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: 'Claim Status Response Payer'
	indexes:
		many: [
			['status_code']
			['organization_name']
			['payer_identification']
			['centers_for_medicare_and_medicaid_service_plan_id']
			['status_information_effective_date']
			['health_care_claim_status_category_code']
			['entity_identifier_code']
		]
	sections_group: [
		'Claim Status Response Payer Status':
			hide_header: true
			sections: [
				'Details':
					hide_header: true
					tab: 'Payer Claim Status'
					fields: ['organization_name', 'payer_identification', 'centers_for_medicare_and_medicaid_service_plan_id',
					'status_information_effective_date', 'health_care_claim_status_category_code', 'health_care_claim_status_category_code_value',
					'status_code', 'status_code_value', 'entity_identifier_code', 'entity_identifier_code_value']
				'Contact Information':
					hide_header: true
					fields: ['payer_contact_information']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Claim Status Response Payer Status'
	grid:
		fields: ['status_information_effective_date', 'organization_name', 'status_code_value', 'health_care_claim_status_category_code_value']
		label: ['Date', 'Payer', 'Status', 'Status Category Code']
		width: [15, 25, 30, 30]
		sort: ['-created_on']
	label: 'Claim Status Response Payer Status'
	open: 'read'