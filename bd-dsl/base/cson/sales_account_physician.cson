fields:
	sales_account_id:
		model:
			required: true
			source: 'sales_account'
			type: 'int'
		view:
			label: 'Sales Account'
			columns: 2

	first:
		model:
			max: 64
			required: true
		view:
			label: 'First Name'
			note: 'Letters, numbers, comma, \', -, . only'
			columns: 2
			validate: [
					name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
			]

	middle:
		model:
			max: 64
			required: false
		view:
			label: 'Middle Name'
			note: 'Letters, numbers, comma, \', -, . only'
			columns: 2
			validate: [
					name: 'NameValidator'
			]

	last:
		model:
			max: 64
			required: true
		view:
			label: 'Last Name'
			note: 'Letters, numbers, comma, \', -, . only'
			columns: 2
			validate: [
					name: 'NameValidator'
			]

	title:
		model:
			max: 128
			required: true
		view:
			label: 'Title'
			columns: 2

	reminders:
		view:
			label: 'Reminders'
			note: 'Appears in the snapshot area'
			control: 'area'

	npi:
		model:
			max: 10
		view:
			label: 'NPI'
			columns: 2
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid NPI, must be 10 digits'
			}]

	phone:
		model:
			prefill: ['sales_account_physician']
			max: 21
		view:
			format: 'us_phone'
			label: 'Phone #'
			columns: 2

	fax:
		model:
			prefill: ['sales_account_physician']
			max: 21
		view:
			format: 'us_phone'
			label: 'Fax #'
			columns: 2

	preferred_communication:
		model:
			source: ['Phone', 'Fax', 'Email']
		view:
			control: 'radio'
			label: 'Preferred Communication Route'
			columns: 2

	email:
		model:
			max: 64
			min: 6
			required: false
		view:
			label: 'Email Address'
			note: 'Must be a valid email address for Portal Access'
			columns: 2
			validate: [
					name: 'EmailValidator'
			]

	specialty:
		model:
			type: 'text'
		view:
			label: 'Physician Specialty'
			note: 'Specialty for the physician'
			columns: 2

	ig_grams:
		model:
			type: 'decimal'
		view:
			label: 'Monthly Estimated IG Grams'
			columns: 2

	physician:
		model:
			required: false
			type: 'int'
			source: 'physician'
		view:
			label: 'Physician Record'
			columns: 2

	notes:
		model:
			subfields:
				date:
					label: 'Date'
					type: 'timestamp'
					readonly: true
				user:
					label: 'User'
					source: '{{user.displayname}}'
					type: 'text'
					readonly: true
				note:
					label: 'Note'
					type: 'text'
			type: 'json'
		view:
			control: 'grid'
			label: 'Notes'

	import_id:
		model:
			required: false
			source: 'sales_call_import'
		view:
			readonly: true
			offscreen: true
			columns: 3
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison']
		delete:     ['admin']
		read:       ['admin', 'csr', 'liaison']
		read_all:   ['admin', 'csr', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'liaison']
		write:      ['admin', 'csr', 'liaison']
	indexes:
		many: [
			['first', 'last', 'title']
			['phone']
			['npi']
		]
	name: '{npi} {last}, {first} {title} {phone}'
	prefill:
		sales_account_physician:
			link:
				sales_account_id: 'sales_account_id'

	sections:
		'Physician Info.':
			fields: ['sales_account_id', 'first', 'last', 'title']
		'Credentialing':
			fields: ['specialty', 'npi']
		'Link':
			fields: ['physician']
		'Contact':
			fields: ['phone', 'fax', 'email', 'preferred_communication']
		'Notes':
			fields: ['reminders', 'notes']
		'Metrics':
			fields: ['ig_grams']

view:
	comment: 'Sales Account > Physician'
	find:
		basic: ['first', 'last']
	grid:
		fields: ['last', 'first', 'title', 'phone', 'fax']
		sort: ['last']
	label: 'Sales Account Physician'
