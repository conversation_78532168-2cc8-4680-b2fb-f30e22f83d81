fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# RA Questionnaire Participation
	first:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['Questionnaire']
		view:
			control: 'radio'
			label: 'Is this your first time on drug therapy for the treatment of Rheumatoid Arthritis?'

	therapy_status:
		model:
			source: ['Currently in the middle of treatment', 'Failed treatment', 'Other']
			if:
				'Currently in the middle of treatment':
					fields: ['drug_taking']
				'Failed treatment':
					fields: ['drug_failed']
				'Other':
					fields: ['status_other']
		view:
			control: 'radio'
			label: 'What best describes your RA therapy status?'

	drug_taking:
		model:
			required: true
		view:
			label: 'Current RA therapy:'

	drug_failed:
		model:
			required: true
		view:
			label: 'Past RA therapy:'

	status_other:
		model:
			required: true
		view:
			label: 'RA therapy status:'

	knowledge:
		model:
			source:
				very: 'Very knowledgeable'
				moderate: 'Moderately knowledgeable'
				little: 'Not knowledgeable'
				none: 'No understanding'
			if:
				moderate:
					note: 'Review disease awareness with patient'
				little:
					note: 'Review disease awareness with patient'
				none:
					note: 'Review disease awareness with patient'
		view:
			control: 'radio'
			note: 'ask patient to tell you what they understand about their condition'
			label: 'How would you rate your understanding of Rheumatoid Arthritis?'

	med_knowledge:
		model:
			source:
				very: 'Very knowledgeable'
				moderate: 'Moderately knowledgeable'
				little: 'Not knowledgeable'
				none: 'No understanding'
			if:
				moderate:
					note: 'Review disease treatment options with patient'
				little:
					note: 'Review disease treatment options with patient'
				none:
					note: 'Review disease treatment options with patient'
		view:
			control: 'radio'
			label: 'How would you rate your understanding of the drug therapy you have been prescribed for rheumatoid arthritis? Tell me what you know about the medication that has been prescribed.'

	med_expect:
		model:
			source: ['No', 'Maybe']
		view:
			control: 'radio'
			label: 'Do you understand what the medication you have been prescribed is supposed to do? Can you share with me what you expect this medication to do?'

	med_take:
		model:
			source: ['No', 'Maybe']
		view:
			control: 'radio'
			label: 'Do you know how and when to take the medication that has been prescribed? Can you tell me what instruction you have received for how and when to take your medication?'

	med_se:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you know what side effects you can expect from this drug therapy?'

	med_adhere:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you understand the importance of taking your medication and continuing to take your medication as your doctor prescribed?'

	med_remember:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you need help remembering to take your medication?'

	med_comfort:
		model:
			source:
				very: 'Very comfortable'
				moderate: 'Somewhat comfortable'
				little: 'Not comfortable'
				none: 'Afraid of needles'
		view:
			control: 'radio'
			label: 'What is your comfort level with having to inject yourself to administer the medication?'

	med_train:
		model:
			source: ['Schedule training', 'Completed training', 'No']
			if:
				'Schedule training':
					fields: ['med_train_date']
				'Completed training':
					fields: ['med_train_date']
				'No':
					fields: ['med_train_setup']
		view:
			control: 'radio'
			label: 'Have you scheduled or completed injection training?'

	med_train_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Injection Training Date (Scheduled/Completed)'

	med_train_setup:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Would you like to speak with someone from the pharmacy who can help you arrange injection training?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Rheumatoid Arthritis':
			fields: ['first']
		'Questionnaire':
			fields: ['therapy_status', 'drug_taking', 'drug_failed',
			'status_other', 'knowledge', 'med_knowledge', 'med_expect',
			'med_take', 'med_se', 'med_adhere', 'med_remember',
			'med_comfort', 'med_train', 'med_train_date', 'med_train_setup']
view:
	comment: 'Patient > Careplan > Assessment > Rheumatoid Arthritis'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Assessment Questionnaire: Rheumatoid Arthritis'
	open: 'read'
