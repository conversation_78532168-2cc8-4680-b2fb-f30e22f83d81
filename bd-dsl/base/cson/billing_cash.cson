fields:
	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site'

	close_no:
		model:
			type: 'text'
		view:
			label: 'Closing #'
			readonly: true
			offscreen: true

	applied_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Applied Date/Time'
			readonly: true
			offscreen: true

	cash_no:
		model:
			type: 'text'
		view:
			label: 'Cash No'
			readonly: true
			offscreen: true

	zeroed:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Zero Posting?'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						zeroed: 'Yes'
					dest: 'zeroed_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						zeroed: 'Yes'
					dest: 'zeroed_by'
				}
			]

	zeroed_reason_id:
		model:
			required: true
			source: 'list_void_reason_billing'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			label: 'Zeroed Reason'

	zeroed_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Zeroed Date/Time'
			readonly: true
			template: '{{now}}'

	zeroed_by:
		model:
			source: 'user'
		view:
			label: 'Zeroed By'
			readonly: true

	patient_id:
		model:
			source: 'patient'
			if:
				'*':
					prefill:
						payer_id: ''
					readonly:
						fields: ['payer_id']
		view:
			label: 'Patient'
			columns: 4

	payer_id:
		model:
			source: 'payer'
			if:
				'*':
					prefill:
						patient_id: ''
					readonly:
						fields: ['patient_id']
		view:
			label: 'Payer'
			columns: 4

	post_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 4
			label: 'Post Date/Time'
			template: '{{now}}'
			validate: [
				{
					name: 'ValidatePostDate'
				}
			]

	payment_method:
		model:
			required: true
			source: ['Check', 'Cash', 'Credit Card', 'Debit Card', 'ERN', 'Other']
			if:
				'Check':
					fields: ['check_no']
				'Other':
					fields: ['other_payment_method']
		view:
			control: 'radio'
			columns: -2
			label: 'Payment Method'

	other_payment_method:
		model:
			required: true
		view:
			columns: 4
			label: 'Other Payment Method'

	check_no:
		model:
			required: true
		view:
			columns: 4
			label: 'Check Number(s)'

	amount:
		model:
			required: true
			rounding: 0.01
			min: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Amount'

model:
	access:
		create:     []
		create_all: ['admin', 'cm']
		delete:     []
		read:       ['admin', 'cm']
		read_all:   ['admin', 'cm']
		request:    []
		update:     []
		update_all: []
		write:      ['admin', 'cm']
	reportable: true
	name: 'Unallocated Cash'
	indexes:
		many: [
			['site_id'],
			['payer_id'],
			['patient_id'],
			['payment_method'],
			['zeroed'],
			['zeroed_reason_id']
		]
	sections:
		'Unallocated Cash':
			hide_header: true
			fields: ['close_no','payer_id','patient_id', 'post_datetime', 'payment_method', 'other_payment_method',
			'check_no','amount']
		'Zero':
			modal: true
			note: 'Zeroing a transaction will add a reversal adjustment in the finance ledger.'
			fields: ['zeroed', 'zeroed_datetime', 'zeroed_by', 'zeroed_reason_id']
view:
	block:
		validate: [
			name: 'ClosedOrZeroedBlock'
		]
	dimensions:
		width: '65%'
		height: '25%'
	hide_cardmenu: true
	comment: 'Unallocated Cash'
	grid:
		fields: ['post_datetime','created_by', 'payer_id', 'patient_id','amount']
		sort: ['-post_datetime']
	label: 'Unallocated Cash'
	open: 'read'