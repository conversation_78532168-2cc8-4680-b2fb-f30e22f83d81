fields:

	name:
		model:
			required: true
		view:
			label: 'Name'

	code:
		model:
			search: 'A'
			required: true
		view:
			# offscreen: false
			# readonly: false
			label: 'Code'

	description:
		model:
			required: false
		view:
			control: 'area'
			label: 'Description'

	report_sql:
		model:
			type: 'text'
		view:
			label: 'Report SQL'
			control: 'area'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'Yes'
		view:
			control: 'radio'
			label: 'Can Sync Record'

	dsl_struct:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			label: 'Defination'

	column_struct:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			label: 'Defination'

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'

	create_view:
		model:
			source: ['Yes', 'No']
			default: 'No'
			if:
				'Yes':
					fields: ['refresh']
		view:
			control: 'radio'
			label: 'Create View'

	refresh:
		model:
			type: 'int'
			default: 60
		view:
			label: 'Refresh Every X Minutes'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	name: '{name}'
	sync_mode: 'mixed'
	sections:
		'Main':
			fields: ['name', 'code','description', 'report_sql', 'allow_sync', 'active', 'dsl_struct', 'column_struct','create_view','refresh']


view:
	comment: 'Query'
	grid:
		fields: ['name', 'code','description', 'create_view', 'allow_sync', 'active']
		sort: ['name', 'code']
	label: 'Query'
	open: 'read'
