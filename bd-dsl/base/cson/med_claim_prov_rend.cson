fields:
	site_id:
		model:
			type: 'int'
			required: false
			source: 'site'
			prefill: ['parent.site_id']
		view:
			columns: 2
			label: 'Site'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/site/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'npi': ['npi'],
					'state_license_number': ['state_license'],
					'organization_name': ['name'],
					'provider_type': 'RenderingProvider',
					'contact_information':
						'type': 'subform'
						'field': 'contact_information'
						'fields':
							'name': ['pr.name']
							'phone_number': ['pr.phone']
							'fax_number': ['pr.fax']
					'address':
						'type': 'subform'
						'field': 'address'
						'fields':
							'address1': ['pr.address1']
							'address2': ['pr.address2']
							'city': ['pr.city']
							'state': ['pr.state_id']
							'postal_code': ['pr.zip']
			]

	provider_type:
		model:
			default: 'RenderingProvider'
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'NM101'
		view:
			columns: 2
			label: 'Provider Type'
			reference: 'NM101'
			readonly: true
			offscreen: true
			_meta:
				path: 'rendering.providerType'

	organization_name:
		model:
			required: true
			min: 1
			max: 60
		view:
			columns: 4
			label: 'Organization Name'
			reference: 'NM103'
			_meta:
				location: '2310B NM1'
				field: '03'
				path: 'rendering.organizationName'

	npi:
		model:
			required: true
			type: 'text'
			max: 10
		view:
			label: 'NPI'
			reference: 'NM109'
			_meta:
				location: '2310B NM1'
				field: '09'
				path: 'rendering.npi'
			columns: 4
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid NPI, must be 10 digits'
			}]

	commercial_number:
		model:
			min: 1
			max: 50
		view:
			columns: 4
			label: 'Commercial Number'
			note: 'Sometimes assigned by Payer'
			reference: 'REF02 REF01=G2'
			_meta:
				location: '2310B REF'
				field: '02'
				code: 'G2'
				path: 'rendering.commercialNumber'

	state_license_number:
		model:
			min: 1
			max: 50
		view:
			columns: 4
			label: 'State License Number'
			reference: 'REF02 REF01=0B'
			_meta:
				location: '2310B REF'
				field: '02'
				code: '0B'
				path: 'rendering.stateLicenseNumber'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{5,20}$'
				error: 'Invalid State License Number'
			}]

	contact_information:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_contact_rend'
		view:
			label: 'Contact Information'

	address:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_address_rend'
		view:
			label: 'Address'

model:
	prefill:
		patient:
			link:
				id: 'patient_id'
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['site_id']
		]

	name:['site_id', 'organization_name','provider_type']
	sections_group: [
		'Site':
			hide_header: true
			fields: ['site_id', 'provider_type', 'organization_name']
		'Contact Information':
			hide_header: true
			indent: false
			fields: ['contact_information']
		'Address':
			hide_header: true
			indent: false
			fields: ['address']
	]

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	reference: '2310B'
	comment: 'Rendering Provider'
	grid:
		fields: ['site_id', 'npi', 'commercial_number', 'state_license_number']
		sort: ['-created_on']
	label: 'Rendering Provider'
	open: 'read'