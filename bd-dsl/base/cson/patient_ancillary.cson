fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	type:
		model:
			required: true
			source: ['Nursing Agency', 'Other']
			if:
				'Nursing Agency':
					fields: ['agency_id']
				'Other':
					fields: ['ancillary_id']
		view:
			control: 'radio'
			label: 'Type'
			columns: 2

	agency_id:
		model:
			required: true
			source: 'nurse_agency'
			sourcefilter:
				active:
					'static': 'Yes'
		view:
			label: 'Nursing Agency'
			columns: 2

	ancillary_id:
		model:
			required: true
			source: 'ancillary_provider'
		view:
			label: 'Ancillary Provider'
			columns: 2

	notes:
		view:
			control: 'area'
			label: 'Notes'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
	bundle: ['patient']
	name: ['ancillary_id', 'agency_id']
	sections:
		'Patient Ancillary Provider':
			fields: ['type', 'agency_id', 'ancillary_id', 'notes']

view:
	hide_cardmenu: true
	comment: 'Patient > Ancillary Provider'
	grid:
		fields: ['agency_id', 'ancillary_id', 'notes']
	label: 'Patient Ancillary Provider'
