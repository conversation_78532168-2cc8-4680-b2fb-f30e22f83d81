fields:

	inv_ids:
		model:
			multi: true
			source: 'billing_invoice'
		view:
			label: 'Invoices'
			readonly: true
			offscreen: true
			transform: [
				name: 'LoadARDetailsView'
			]

	view_mode:
		model:
			source: ['Batch', 'Single']
			if:
				'Single':
					sections: ['Invoice Details', 'Charge Lines', 'Transactions']
				'Batch':
					sections: ['Batch Functions']
		view:
			label: 'View Mode'
			readonly: true
			offscreen: true

	invoice_no:
		model:
			required: true
			type: 'text'
		view:
			label: 'Invoice #'
			readonly: true
			offscreen: true

	billing_method_id:
		model:
			required: true
			source: 'list_billing_method'
			sourceid: 'code'
			if:
				'generic':
					fields: ['generic_charge_line']
				'mm':
					fields: ['mm_charge_line']
				'cms1500':
					fields: ['mm_charge_line']
				'ncpdp':
					fields: ['charge_line']
		view:
			label: 'Billing Method'
			readonly: true
			offscreen: true

	generic_charge_line:
		model:
			multi: false
			sourcefilter:
				invoice_no:
					'dynamic': '{invoice_no}'
				void:
					'static': '!Yes'
		view:
			embed:
				form: 'ledger_charge_line'
				selectable: true
			grid:
				edit: false
				rank: 'none'
				add: 'none'
				delete: false
				label: ['Quantity', 'Description', 'Total Price $', 'Expected $']
				fields: ['bill_quantity', 'description', 'gross_amount_due', 'expected']
				width: [10, 55, 15, 15]
			label: 'Charge Lines'
			validate: [
				name: 'CollectEmbedFieldIds'
				target: 'selected_charge_line'
			]

	mm_charge_line:
		model:
			multi: false
			sourcefilter:
				invoice_no:
					'dynamic': '{invoice_no}'
				void:
					'static': '!Yes'
		view:
			embed:
				form: 'ledger_charge_line'
				selectable: true
			grid:
				edit: false
				rank: 'none'
				add: 'none'
				delete: false
				label: ['Item', 'Quantity', 'Exp $', 'Gross Amt $', 'Cost $', 'HCPC']
				fields: ['inventory_id', 'bill_quantity', 'expected', 'gross_amount_due', 'total_cost', 'hcpc_code']
				width: [35, 10, 15, 15, 10, 15]
			label: 'Charge Lines'
			max_count: 50
			note: 'Max 50'
			validate: [
				name: 'CollectEmbedFieldIds'
				target: 'selected_charge_line'
			]

	charge_line:
		model:
			multi: false
			sourcefilter:
				invoice_no:
					'dynamic': '{invoice_no}'
				void:
					'static': '!Yes'
		view:
			embed:
				form: 'ledger_charge_line'
				selectable: true
			grid:
				edit: false
				rank: 'none'
				add: 'none'
				delete: false
				label: ['Item', 'Quantity', 'Exp $', 'Gross Amt $', 'Cost $', 'NDC']
				fields: ['inventory_id', 'bill_quantity', 'expected', 'gross_amount_due', 'total_cost', 'formatted_ndc']
				width: [35, 10, 15, 15, 10, 15]
			label: 'Charge Lines'
			max_count: 25
			note: 'Max 25'
			validate: [
				name: 'CollectEmbedFieldIds'
				target: 'selected_charge_line'
			]

	selected_charge_line:
		model:
			multi: false
			source: 'ledger_charge_line'
			if:
				'!':
					readonly: 
						fields: ['transactions']
		view:
			label: 'Selected Charge Line'
			readonly: true
			offscreen: true
			transform: [{
				name: 'EmbedRefresh',
				fields: ['transactions']
			}]

	transactions:
		model:
			multi: true
			sourcefilter:
				selected_charge_line:
					'dynamic': '{selected_charge_line}'
				invoice_no:
					'dynamic': '{invoice_no}'
		view:
			embed:
				add_form: 'billing_ar_transaction'
				query: 'ar_charge_line_transactions'
				selectable: false
				add_preset:
					charge_line_id: '{selected_charge_line}'
			grid:
				edit: false
				rank: 'none'
				add: 'flyout'
				delete: false
				label: ['Date/Time', 'By', 'Type', 'Amount', 'Notes']
				fields: ['transaction_datetime', 'transaction_by', 'transaction_type', 'amount', 'notes']
				width: [20, 25, 10, 15, 30]
			label: 'Transactions'
			transform: [{
				name: 'RefreshARInvoiceMetrics'
			}]

	# Batch Functions
	writeoff_invoice:
		model:
			source: ['Writeoff Selected Invoices']
		view:
			columns: 2
			class: 'dsl-button'
			control: 'checkbox'
			label: 'Writeoff Selected Invoices'
			validate: [
				name: 'ARWriteoffSelectedInvoices'
			]

	subs_id:
		model:
			multi: false
			source: 'list_billing_csstatus'
			sourceid: 'code'
			track: true
			if:
				'!':
					readonly:
						fields: ['assign_status']
		view:
			columns: -2
			control: 'select'
			label: 'Claim Substatus'
			class: 'status'

	assign_status:
		model:
			source: ['Assign Claim Status']
		view:
			columns: 2
			class: 'dsl-button'
			control: 'checkbox'
			label: 'Assign Claim Status'
			validate: [
				name: 'ARAssignClaimStatus'
			]

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: []
	save: false
	name: 'Invoice Details'
	sections:
		'View Mode':
			hide_header: true
			indent: false
			fields: ['inv_ids', 'view_mode']
		'Batch Functions':
			hide_header: true
			indent: false
			fields: ['writeoff_invoice', 'subs_id', 'assign_status']
		'Invoice Details':
			hide_header: true
			indent: false
			fields: ['invoice_no', 'billing_method_id','selected_charge_line']
		'Charge Lines':
			hide_header: true
			indent: false
			fields: ['charge_line', 'generic_charge_line', 'mm_charge_line']
		'Transactions':
			hide_header: true
			indent: false
			fields: ['transactions']

view:
	hide_header: true
	hide_cardmenu: true
	comment: 'AR Manager > Invoice Details'
	label: 'Invoice Details'
	open: 'edit'
