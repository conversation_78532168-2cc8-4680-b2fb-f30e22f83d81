#TABLE RADIMEF0_CLIN_EFFECT

fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#ADI_EFFTC
	adi_efftc:
		view:
			label: 'Drug-Drug Interaction Clinical Effect Code'
			readonly: true
			findunique: true
			columns: 2

	#ADI_EFFTXT
	adi_efftxt:
		view:
			label: 'Drug-Drug Interaction Clinical Effect Text'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['adi_efftc', 'adi_efftxt']
	indexes:
		many: [
			['adi_efftc']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['adi_efftc', 'adi_efftxt']

view:
	comment: 'Manage > List FDB Drug-Drug Interaction Clinical Effects Description Table'
	find:
		basic: ['adi_efftc']
	grid:
		fields: ['adi_efftc', 'adi_efftxt']
	label: 'List FDB Drug-Drug Interaction Clinical Effects Description Table'
