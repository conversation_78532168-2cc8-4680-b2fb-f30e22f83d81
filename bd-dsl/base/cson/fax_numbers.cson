fields:
	site_id:
		model:
			source: 'site'
			type: 'int'
			required: true
		view:
			label: 'Site'
	fax_number:
		model:
			max: 32
			required: true
		view:
			label: 'Fax #'
			format: 'us_phone'
			columns: 4
	is_default:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			label: 'Default Fax Number?'
			columns: 4
	number_label:
		model:
			type: 'text'
		view:
			label: 'Label'
			columns: 4
	from_caller_name:
		model:
			type: 'text'
			required: true
			prefill: ['site.auto_name']
		view:
			label: 'From Caller Name'
			columns: 4
			note: 'This will be used as the sender name on the cover page'
	from_caller_id:
		model:
			max: 32
		view:
			label: 'From Caller ID'
			format: 'us_phone'
			columns: 4
			note: 'This will be used as the sender phone number on the cover page'
model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['manage']
	prefill:
		site:
			link:
				id: 'site_id'
	name: '{fax_number} {site_id_auto_name}'
	sections:
		'Fax Numbers':
					fields: ['site_id', 'fax_number', 'is_default', 'number_label', 'from_caller_name', 'from_caller_id']

view:
	hide_cardmenu: true
	comment: 'Fax Numbers'
	find:
		basic: ['site_id', 'fax_number']
	grid:
		fields: ['created_on', 'site_id', 'fax_number', 'is_default', 'number_label', 'from_caller_name', 'from_caller_id']
		sort: ['-created_on']
	label: 'Fax Numbers'
