fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	mrn:
		model:
			prefill: ['patient.mrn']
			required: true
		view:
			label: 'MRN'
			readonly: true
			offscreen: true

	ssn:
		model:
			prefill: ['patient.ssn']
			required: false
		view:
			label: 'SSN'
			readonly: true
			offscreen: true

	external_id:
		model:
			required: false
		view:
			label: 'CPR+ ID'
			columns: 3
			readonly: true
			offscreen: true
	careplan_id:
		model:
			required: false
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	last_verification_status:
		view:
			label: 'Last Verification Status'
			readonly: true
			offscreen: true

	last_verification_req_id:
		view:
			label: 'Last Verification Request ID'
			readonly: true
			offscreen: true

	last_verification_sent_date:
		model:
			type: 'date'
		view:
			label: 'Last Verification Sent Date'
			readonly: true
			offscreen: true

	last_verification_file:
		view:
			label: 'Last Verification File'
			readonly: true
			offscreen: true

	payer_id:
		model:
			required: true
			source: 'payer'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
			sourcefilter:
				active:
					'static': 'Yes'
		view:
			columns: 3
			label: 'Payer'
			class: 'select_prefill'
			form_link_enabled: true
			transform: [
				name: 'SelectPrefill'
				url: '/form/payer/?limit=1&filter=id:'
				fields:
					'billing_method_id': ['billing_method_id'],
					'type_id': ['type_id'],
					'send_blank_person_code': ['send_blank_person_code'],
					'bin': ['bin'],
					'pcn': ['pcn'],
					'is_self_pay': ['is_self_pay'],
					'bill_for_denial': ['always_bill_for_denial'],
					'pap_program_id': ['pap_program_id'],
					'cap_program_id': ['cap_program_id'],
					'other_program_id': ['other_program_id'],
					'bill_mm_and_ncpdp': ['bill_mm_and_ncpdp']
			]

	bill_mm_and_ncpdp:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['bill_for_denial', 'subform_med_cond', 'medical_relationship_id', 'insurance_type_code']
					sections: ['Medical Conditional']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			note: 'If checked, sends NCPDP claim and Major Medical claim'
			label: "Bill Major Medical and NCPDP Claims"
			offscreen: true
			readonly: true

	is_self_pay:
		model:
			source: ['Yes']
			if:
				'!':
					fields: ['payer_level']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Self Pay'
			readonly: true
			offscreen: true

	previous_payer_rank:
		model:
			prefill: ['patient_insurance.rank']
			type: 'int'
			if:
				'!':
					prefill:
						payer_level: 'Primary'
						payer_level_other: null
				'1':
					prefill:
						payer_level: 'Secondary'
						payer_level_other: null
				'2':
					prefill:
						payer_level: 'Tertiary'
						payer_level_other: null
				'3':
					prefill:
						payer_level: 'Other'
						payer_level_other: '4'
				'4':
					prefill:
						payer_level: 'Other'
						payer_level_other: '5'
				'5':
					prefill:
						payer_level: 'Other'
						payer_level_other: '6'
				'6':
					prefill:
						payer_level: 'Other'
						payer_level_other: '7'
				'7':
					prefill:
						payer_level: 'Other'
						payer_level_other: '8'
		view:
			label: 'Previous Payer Rank'
			offscreen: true
			readonly: true

	payer_level:
		model:
			source: ['Primary', 'Secondary', 'Tertiary', 'Other']
			if:
				'Primary':
					prefill:
						rank: '1'
				'Secondary':
					prefill:
						rank: '2'
				'Tertiary':
					prefill:
						rank: '3'
				'Other':
					fields: ['payer_level_other']
		view:
			control: 'radio'
			label: 'Payer Level'
			columns: 3

	payer_level_other:
		model:
			source: ['4', '5', '6', '7', '8']
			required: true
			if:
				'4':
					prefill:
						rank: '4'
				'5':
					prefill:
						rank: '5'
				'6':
					prefill:
						rank: '6'
				'7':
					prefill:
						rank: '7'
				'8':
					prefill:
						rank: '8'
		view:
			control: 'radio'
			label: 'Payer Level Other'
			columns: 3

	rank:
		model:
			type: 'int'
		view:
			label: 'Rank'
			offscreen: true
			readonly: true
			findunique: true

	active:
		model:
			default: 'Yes'
			max: 32
			min: 1
			source: ['Yes']
			if:
				'Yes':
					require_fields: ['effective_date', 'payer_level']
				'!':
					require_fields: ['termination_date']
					prefill:
						rank: null
						payer_level: null
						payer_level_other: null
		view:
			columns: 3
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Active?'

	billing_method_id:
		model:
			required: true
			source: 'list_billing_method'
			sourceid: 'code'
			if:
				'cms1500':
					fields: ['medical_relationship_id', 'bill_for_denial']
				'mm':
					fields: ['bill_for_denial', 'subform_med_cond', 'medical_relationship_id', 'insurance_type_code']
					sections: ['Medical Conditional']
				'ncpdp':
					fields: ['subform_ncpdp_cond', 'pharmacy_relationship_id', 'bin', 'pcn']
					sections: ['NCPDP Conditional', 'Pharmacy Claims Settings']
		view:
			label: 'Billing Method'
			readonly: true
			columns: 3

	send_blank_person_code:
		model:
			source: ['Yes']
			if:
				'Yes':
					field_warning:
						person_code: "Payer is configured to send a blank person code"
		view:
			columns: 3
			control: 'checkbox'
			class: 'checkbox-only'
			label: "22. Send Blank Person Code (C3)"
			note: "(303-C3)"
			offscreen: true
			readonly: true

	type_id:
		model:
			required: true
			source: 'list_payer_type'
			sourceid: 'code'
			if:
				'MCRB':
					sections: ['Cardholder Information', 'Insurance Status']
					fields: ['medicare_number', 'medigap_id', 'certification']
				'MCRD':
					sections: ['Cardholder Information', 'Insurance Status']
					fields: ['medicare_number', 'medigap_id', 'person_code', 'partd_facility', 'certification']
				'MEDI':
					sections: ['Cardholder Information', 'Insurance Status']
					fields: ['medicaid_number', 'medicaid_agency_number', 'medicaid_relationship_id']
				'CMMED':
					sections: ['Cardholder Information', 'Beneficiary Information', 'Insurance Status']
					fields: ['group_name']
				'CMPBM':
					sections: ['Cardholder Information', 'Beneficiary Information', 'Insurance Status']
					fields: ['plan_name', 'send_blank_person_code', 'person_code','partd_facility']
				'COPAY':
					sections: ['Cardholder Information', 'Assistance Program']
					fields: ['cap_program_id', 'reload_date', 'approved_amount_no_limit',
					'card_type', 'card_no', 'card_cvc']
				'PAP':
					sections: ['Cardholder Information', 'Assistance Program']
					fields: ['pap_program_id']
				'FOUND':
					sections: ['Cardholder Information', 'Assistance Program']
					fields: ['other_program_id', 'reload_date', 'approved_amount_no_limit']
				'HARD':
					sections: ['Assistance Program']
					fields: ['hardship_waiver_notes', 'approved_amount_no_limit']
				'SELF':
					prefill:
						active: 'Yes'
					readonly:
						fields: ['active']
		view:
			label: 'Type'
			readonly: true
			columns: 3
			findfilter: '!SELF'

	insurance_type_code:
		model:
			required: false
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR05'
		view:
			columns: 3
			label: 'Insurance Type Code'
			note: 'Used in Electronic Medical Claim'
			reference: 'SBR05'
			class: 'claim-field'

	patient_id_qualifier:
		model:
			default: "EA",
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			required: true
			sourcefilter:
				field:
					'static': '331-CX'
			if:
				'EA': #MRN
					fields: ['patient_claim_id']
					prefill:
						patient_claim_id: ['self.mrn']
				'01': #SSN
					fields: ['patient_claim_id']
					prefill:
						patient_claim_id: ['self.ssn']
				'10': # Employer #
					require_fields: ['employer_id']
				'11': # Payer/PBM Assigned
					fields: ['patient_claim_id']
				'99': #Other
					fields: ['patient_claim_id']
		view:
			columns: 3
			note: '331-CX'
			label: 'Claim Patient ID Qual'
			class: 'claim-field'

	patient_claim_id:
		model:
			required: true
			max: 20
			prefill: ['patient.mrn']
		view:
			columns: 3
			note: '332-CY'
			label: 'Claim Patient ID'

	home_plan:
		model:
			max: 3
		view:
			columns: 3
			reference: '314-CE'
			note: '314-CE'
			label: 'Home Plan'

	medical_relationship_id:
		model:
			required: true
			min: 2
			max: 2
			default: '18'
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR02'
			if:
				'!18':
					fields: ['beneficiary_fname', 'beneficiary_lname', 'beneficiary_mname', 'beneficiary_gender', 'beneficiary_dob',
					'beneficiary_address1', 'beneficiary_address2', 'beneficiary_city', 'beneficiary_state_id',
					'beneficiary_postal_code']
		view:
			columns: 3
			label: 'Relationship to Subscriber'
			reference: 'SBR02'
			class: 'claim-field'

	pharmacy_relationship_id:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			default: '1'
			sourcefilter:
				field:
					'static': '306-C6'
			if:
				'!1':
					fields: ['beneficiary_fname', 'beneficiary_lname', 'beneficiary_mname', 'beneficiary_gender', 'beneficiary_dob']
		view:
			columns: 3
			control: 'select'
			label: 'Relationship to Subscriber'
			class: 'claim-field'

	employer:
		view:
			label: 'Employer'
			offscreen: true
			readonly: true

	employer_id:
		view:
			label: 'Employer ID'
			note: 'If Insurance is provided through employer instead of insurance company'
			offscreen: true
			readonly: true

	beneficiary_fname:
		model:
			required: true
			max: 35
			min: 1
		view:
			columns: -3
			label: 'First Name'
			note: 'Letters, numbers, \', -, . only'

	beneficiary_mname:
		model:
			required: false
			max: 25
			min: 1
		view:
			columns: 3
			label: 'Middle Name'
			note: 'Letters, numbers, \', -, . only'

	beneficiary_lname:
		model:
			required: true
			max: 60
			min: 1
		view:
			columns: 3
			label: 'Last Name'
			note: 'Letters, numbers, \', -, . only'

	beneficiary_dob:
		model:
			required: false
			type: 'date'
		view:
			columns: -3
			control: 'input'
			label: 'Beneficiary Date of Birth'
			note: 'Must be in MM/DD/YYYY format'
			validate: [
					name: 'DOBValidator'
			]

	beneficiary_gender:
		model:
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'DMG03'
		view:
			columns: 3
			note: 'Gender at Birth'
			label: 'Beneficiary Gender'
			class: 'claim-field'

	beneficiary_address1:
		model:
			required: false
			max: 35
			prefill: ['patient.home_street']
			if:
				'*':
					require_fields: ['beneficiary_city', 'beneficiary_state_id', 'beneficiary_postal_code']
		view:
			columns: 'addr_1'
			class: "api_prefill"
			label: 'Address 1'
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'beneficiary_address1': ['addressLabel']
					'beneficiary_city': ['city']
					'beneficiary_state_id': ['stateCode']
					'beneficiary_postal_code': ['postalCode']
			]

	beneficiary_address2:
		model:
			prefill: ['patient.home_street2']
			required: false
			max: 35
		view:
			columns: 'addr_2'
			label: 'Address 2'

	beneficiary_city:
		model:
			prefill: ['patient.home_city']
			max: 30
			if:
				'*':
					require_fields: ['beneficiary_address1', 'beneficiary_state_id', 'beneficiary_postal_code']
		view:
			columns: 'addr_city'
			label: 'City'

	beneficiary_state_id:
		model:
			source: 'list_us_state'
			sourceid: 'code'
			prefill: ['patient.home_state_id']
			if:
				'*':
					require_fields: ['beneficiary_address1', 'beneficiary_city', 'beneficiary_postal_code']
		view:
			label: 'State'
			columns: 'addr_state'
			class: 'claim-field'

	beneficiary_postal_code:
		model:
			max: 10
			prefill: ['patient.home_zip']
			if:
				'*':
					require_fields: ['beneficiary_address1', 'beneficiary_city', 'beneficiary_state_id']
		view:
			columns: 'addr_zip'
			label: 'Zip'
			class: 'claim-field'

	medicare_number:
		model:
			required: false
			max: 14
		view:
			label: 'Medicare Number'
			note: 'e.g. 1EG4TE5MK72 (no hyphens)'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{9,13}$'
				error: 'Invalid Medicare Number, must be 9-13 characters long'
			}]

	medigap_id:
		model:
			required: false
			max: 14
		view:
			label: 'MediGap ID'
			note: '5-10 alphanumeric characters'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{5,10}$'
				error: 'Invalid MediGap ID, must be 5-10 characters long'
			}]
			offscreen: true
			readonly: true

	medicaid_number:
		model:
			required: true
			max: 13
		view:
			label: 'Medicaid Number'
			note: '13-digit number only'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{9,13}$'
				error: 'Invalid Medicaid Number, must be 9-13 characters long'
			}]

	medicaid_agency_number:
		view:
			label: 'Medicaid Agency Number'

	medicaid_relationship_id:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '306-C6'
		view:
			label: 'Medicaid Relationship Indicator'
			class: 'claim-field'

	certification:
		model:
			subfields:
				start_date:
					label: 'Start Date'
					type: 'date'
				end_date:
					label: 'End Date'
					type: 'date'
				status:
					label: 'Status'
					source: ['Pending', 'Active']
			type: 'json'
		view:
			control: 'grid'
			label: 'CMS-485 Certification Status'

	cardholder_id:
		model:
			required: true
			min: 2
			max: 80
		view:
			columns: 3
			note: 'Unique for the individual within the same insurance plan'
			label: 'Cardholder ID'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{2,80}$'
				error: 'Cardholder ID, must be 2-80 alphanumeric characters'
			}]

	policy_number:
		model:
			required: false
			min: 1
			max: 50
		view:
			columns: 3
			note: 'Usually the same across individuals in a family, similar to a policy number'
			label: 'Policy Number'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{1,50}$'
				error: 'Insurance ID, must be 1-50 alphanumeric characters'
			}]

	plan_name:
		model:
			min: 1
			max: 60
		view:
			columns: 3
			label: 'Plan Name'

	group_name:
		model:
			min: 1
			max: 60
		view:
			columns: 3
			label: 'Group Name'

	group_number:
		model:
			max: 50
		view:
			columns: 3
			label: 'Group Number'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{1,20}$'
				error: 'Invalid Group Number, must be 1-20 alphanumeric characters'
			}]

	bin:
		model:
			max: 6
			required: true
		view:
			columns: 3
			label: 'BIN'
			class: 'claim-field'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{6}$'
				error: 'Invalid BIN, must be 6 digits'
			}]

	pcn:
		model:
			required: false
			max: 10
		view:
			columns: 3
			label: 'PCN'
			class: 'claim-field'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{1,10}$'
				error: 'Invalid PCN, must be 1-10 alphanumeric characters'
			}]

	person_code:
		view:
			columns: 3
			label: 'Person Code'
			class: 'claim-field'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{1,4}$'
				error: 'Invalid Person Code, must be 1-4 alphanumeric characters'
			}]

	partd_facility:
		model:
			source:
				'Y': 'Yes'
		view:
			control: 'checkbox'
			label: 'Resides in CMS Part D Facility?'
			class: 'claim-field'

	bill_for_denial:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['next_insurance_id']
		view:
			columns: 3
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Bill For Denial?'

	next_insurance_id:
		model:
			required: true
			source: 'patient_insurance'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
				id:
					'dynamic': '!{id}'
				billing_method_id:
					'static': ['mm', 'ncpdp', 'cms1500']
		view:
			columns: 3
			label: 'Next Insurance'
			note: 'The next insurance used for pricing'

	effective_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 3
			label: 'Effective Date'

	termination_date:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Termination Date'

	month_to_month:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Month-To-Month'
			columns: 3

	# if beneficiary_relationship_id is not 18 then beneficiary_name, else contact_name
	contact_name:
		view:
			columns: 3
			label: 'Contact'
	
	contact_email:
		view:
			columns: 3
			label: 'Email'

	contact_phone:
		model:
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Phone'
	insurance_contact_phone:
		model:
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Insurance Contact Phone'

	#Copay/PAP/Foundational Assistance Program
	pap_program_id:
		model:
			required: true
			source: 'list_pap_program'
		view:
			columns: 3
			label: 'PAP Program'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_pap_program/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'reenrollment_process': ['reenrollment_process']
			]

	cap_program_id:
		model:
			required: false
			source: 'list_copay_program'
		view:
			columns: 3
			label: 'Copay Assistance Program'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_copay_program/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'reenrollment_process': ['reenrollment_process']
			]

	other_program_id:
		model:
			required: true
			source: 'list_alt_payer'
		view:
			columns: 3
			label: 'Other Assistance Program'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_alt_payer/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'reenrollment_process': ['reenrollment_process']
			]

	reenrollment_process:
		view:
			columns: 3
			control: 'area'
			label: 'Re-enrollment process'
			readonly: true

	reenrollment_notes:
		view:
			columns: 3
			control: 'area'
			label: 'Re-enrollment notes'

	assistance_status:
		model:
			source: ['Approved', 'Exhausted']
			default: 'Approved'
			required: true
		view:
			columns: 3
			control: 'radio'
			label: 'Assistance Program Status'

	enrollment_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 3
			label: 'Enrollment Date'
			template: '{{now}}'

	assistance_effective_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 3
			label: 'Effective Date'
			template: '{{now}}'

	assistance_has_expiration_date:
		model:
			required: true
			source: ['Yes', 'No']
			if:
				'No':
					fields: ['assistance_no_expiration_date']
				'Yes':
					fields: ['assistance_expiration_date']
		view:
			columns: 3
			control: 'radio'
			label: 'Assistance Expiration Date?'

	assistance_no_expiration_date:
		model:
			required: false
		view:
			columns: 3
			control: 'area'
			label: 'Why no expiration date?'

	assistance_expiration_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 3
			label: 'Expiration Date'

	hardship_waiver_notes:
		model:
			required: false
		view:
			columns: 3
			control: 'area'
			label: 'Hardship Waiver Notes'

	card_type:
		model:
			required: true
			default: 'Copay Card'
			source: ['Credit Card', 'Debit Card', 'Copay Card']
			if:
				'Credit Card':
					fields: ['card_no', 'card_cvc']
				'Debit Card':
					fields: ['card_no', 'card_cvc']
				'Copay Card':
					fields: ['bin', 'pcn']
		view:
			control: 'radio'
			label: 'Card Type'

	card_no:
		model:
			required: false
		view:
			columns: 3
			label: 'Card Number'
			transform: [{
				name: 'CopyFieldToField'
				to: 'cardholder_id'
				overwrite: false
			}]

	card_cvc:
		model:
			type: 'int'
			max: 999
		view:
			columns: 3
			label: 'CVC number'

	renewal:
		model:
			required: true
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Renewal?'

	renewal_type:
		model:
			source: ['Automatic','Renewed by Patient/Pharmacy']
			default: 'Renewed by Patient/Pharmacy'
			if:
				'Renewed by Patient/Pharmacy':
					fields: ['renewal_reminder_date']
				'Automatic':
					fields: ['reload_date']
		view:
			columns: 3
			label: 'Renewal Type'

	reload_date:
		model:
			type: 'date'
		view:
			columns: 3
			note: 'Will automatically reload the amount left on this date and update to the next year'
			label: 'Reload Date'
			validate: [
				{
					name: 'PrefillFirstDayOfNextCalendarYear'
				},
				{
					name: 'PrefillReminderDate'
					field: 'renewal_reminder_date'
					subtract_days: 60
				}
			]

	#TODO Should show a reminder somewhere on a swimlane. This should default to 2 months prior to the expiration date
	renewal_reminder_date:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Renewal Reminder Date'

	approved_amount_no_limit:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['approved_amount', 'amount_left']
			required: true
		view:
			columns: 3
			control: 'checkbox'
			label: 'Amount Approved has Limit?'

	approved_amount:
		model:
			required: true
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral money'
			format: '$0,0.00'
			note: 'This can be the same as the unused amount if unknown'
			label: 'Amount Approved'
			columns: 3
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'amount_left'
					overwrite: true
				}
				{
					name: "CompareValidator"
					fields: [
						"approved_amount"
						"amount_left"
					]
					require: "gte"
					error: "Amount Approved must be greater than Unused Amount"
				}
		]

	amount_left:
		model:
			required: true
			type: 'decimal'
			rounding: 0.01
		view:
			columns: 3
			class: 'numeral money'
			format: '$0,0.00'
			note: 'Call the copay card to get the amount left on the card'
			label: 'Unused Amount'

	subform_ncpdp_cond:
		model:
			type: 'subform'
			multi: true
			source: 'patient_insurance_ncpdp_cond'
		view:
			grid:
				edit: true
				add: 'inline'
				fields: ['previous_payer_id', 'plan_name', 'person_code']
				label: ['Prev Payer', 'Plan', 'Code']
				width: [60, 20, 20]
			label: 'NCPDP Conditional'

	subform_med_cond:
		model:
			type: 'subform'
			multi: true
			source: 'patient_insurance_med_cond'
		view:
			grid:
				edit: true
				add: 'flyout'
				hide_cardmenu: true
				fields: ['previous_payer_id', 'accident_date', 'symptom_date', 'insurance_type_code']
				label: ['Prev Payer', 'Acc Dt', 'Sym Dt', 'Type']
				width: [40, 20, 20, 20]
			label: 'Medical Conditional'

# not making source because it will go to circular dependency,need this to check whether insurance is created against assistance or not
	patient_assistance_id:
		model:
			required: false
			type: 'int'
		view:
			label: 'Patient Assistance Id'
			offscreen: true
			readonly: true

model:
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
		patient_insurance:
			link:
				patient_id: 'patient_id'
			filter:
				active: 'Yes'
			max: 'rank'
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'pharm']
	bundle: ['patient']
	indexes:
		fulltext: ['medicare_number', 'medicaid_number', 'cardholder_id', 'policy_number', 'group_number']
		many: [
			['payer_id']
			['patient_id']
			['billing_method_id']
			['type_id']
			['medicare_number']
			['medigap_id']
			['medicaid_number']
			['cardholder_id']
			['policy_number']
			['group_number']
			['group_name']
			['plan_name']
		]
		unique: ['patient_id', 'careplan_id', 'active', 'archived']
	sections_group: [
		'Insurance':
			hide_header: true
			indent: false
			tab: 'Insurance'
			fields: ['mrn', 'ssn', 'payer_id', 'bill_mm_and_ncpdp', 'is_self_pay', 'previous_payer_rank', 'payer_level', 'payer_level_other',
			'rank', 'active', 'billing_method_id', 'type_id', 'insurance_type_code', 'month_to_month', 'insurance_contact_phone']
		'Assistance Program':
			indent: false
			hide_header: true
			tab: 'Insurance'
			fields: ['pap_program_id', 'cap_program_id', 'other_program_id', 'assistance_status', 'assistance_effective_date','enrollment_date',
			'assistance_has_expiration_date', 'assistance_expiration_date',
			'card_type', 'card_no' , 'card_cvc', 'renewal', 'renewal_type', 'renewal_reminder_date', 'reload_date',
			'approved_amount_no_limit', 'approved_amount', 'amount_left', 'hardship_waiver_notes', 'reenrollment_process', 'reenrollment_notes']
		'Cardholder Information':
			hide_header: true
			indent: false
			tab: 'Insurance'
			fields: ['medicare_number', 'medigap_id', 'medicaid_number', 'medicaid_agency_number', 'medicaid_relationship_id', 
			'cardholder_id', 'policy_number', 'plan_name', 'group_name', 'group_number', 'bin', 'pcn', 'send_blank_person_code', 'person_code','partd_facility', 'certification']
		'Contact':
			indent: false
			tab: 'Insurance'
			fields: ['contact_name', 'contact_phone', 'contact_email']
		'Insurance Status':
			hide_header: true
			indent: false
			tab: 'Insurance'
			fields: ['bill_for_denial', 'next_insurance_id', 'effective_date', 'termination_date']
		'Beneficiary Information':
			hide_header: true
			indent: false
			tab: 'Beneficiary'
			fields: ['pharmacy_relationship_id', 'medical_relationship_id', 'employer', 'employer_id',
			'beneficiary_fname', 'beneficiary_mname', 'beneficiary_lname', 'beneficiary_dob', 'beneficiary_gender',
			'beneficiary_address1', 'beneficiary_address2', 'beneficiary_city', 'beneficiary_state_id', 'beneficiary_postal_code']
		'Pharmacy Claims Settings':
			hide_header: true
			indent: false
			tab: 'Claims'
			fields: ['patient_id_qualifier', 'patient_claim_id', 'home_plan']
		'NCPDP Conditional':
			hide_header: true
			indent: false
			tab: 'Claims'
			fields: ['subform_ncpdp_cond']
		'Medical Conditional':
			hide_header: true
			indent: false
			tab: 'Claims'
			fields: ['subform_med_cond']
	]

	name: '{rank} : {payer_id_auto_name} : {type_id_auto_name}'

view:
	dimensions:
		width: '90%'
		height: '65%'
	hide_cardmenu: true
	validate: [
		{
			name: "DateOrderValidator"
			fields: [
				"effective_date",
				"termination_date"
			]
			error: "Termination Date cannot be lesser than Effective Date"
		}
	]
	block:
		validate: [
			name: 'InsuranceBlock'
			disabled_fields: ['payer_id']
		]
	comment: 'Patient > Insurance'
	find:
		basic: ['rank', 'type_id', 'payer_id', 'active', 'assistance_status']
		advanced: ['medicare_number', 'medicaid_number', 'cardholder_id', 'policy_number', 'group_number']
	grid:
		fields: ['type_id', 'payer_id', 'active', 'assistance_status', 'bill_for_denial','rank']
		sort: ['rank']
	icon: 'insurance'
	label: 'Patient Insurance'
	open: 'read'