fields:
	response_uuid:
		model:
			type: 'text'
		view:
			label: 'Response UUID'
			readonly: true
			offscreen: true

	reject_code:
		model:
			multi: false
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '511-FB'
		view:
			note: '511-FB'
			label: 'Payer Reject Code'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['response_uuid']
		]
	name: ['reject_code']
	sections:
		'Reject Message':
			hide_header: true
			indent: false
			fields: ['response_uuid', 'reject_code']

view:
	dimensions:
		width: '80%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Reject Message'
	grid:
		fields: ['reject_code']
		sort: ['-created_on']
	label: 'Reject Message'
	open: 'read'