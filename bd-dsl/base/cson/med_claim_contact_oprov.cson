fields:

	name:
		model:
			min: 1
			max: 60
		view:
			columns: 4
			label: 'Name'
			reference: 'PER02'
			_meta:
				location: '2310C PER'
				field: '02'
				path: 'ordering.contactInformation.name'

	email:
		model:
			max: 256
			min: 1
			required: false
		view:
			columns: 4
			label: 'Email Address'
			reference: 'PER04 PER03 = EM'
			validate: [
					name: 'EmailValidator'
			]
			_meta:
				location: '2420E PER'
				field: ['04', '06', '08']
				type: 'many'
				code: 'EM'
				path: 'ordering.contactInformation.email'

	phone_number:
		model:
			max: 21
		view:
			columns: 4
			format: 'us_phone'
			label: 'Phone #'
			reference: 'PER04 PER03 = TE'
			_meta:
				location: '2420E PER'
				field: ['04', '06', '08']
				type: 'many'
				code: 'TE'
				path: 'ordering.contactInformation.phoneNumber'

	fax_number:
		model:
			max: 21
		view:
			columns: 4
			format: 'us_phone'
			label: 'Fax #'
			reference: 'PER04 PER03 = FX'
			_meta:
				location: '2420E PER'
				field: ['04', '06', '08']
				type: 'many'
				code: 'FX'
				path: 'ordering.contactInformation.faxNumber'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name:['name','phone_number']
	sections:
		'Ordering Provider Contact Information':
			hide_header: true
			fields: ['name', 'email', 'phone_number', 'fax_number']

view:
	dimensions:
		width: '85%'
		height: '55%'
	hide_cardmenu: true
	comment: 'Ordering Provider Contact Information'
	grid:
		fields: ['name', 'email', 'phone_number', 'fax_number']
		width: [30, 30, 20, 20]
		sort: ['-created_on']
	label: 'Ordering Provider Contact Information'
	open: 'read'