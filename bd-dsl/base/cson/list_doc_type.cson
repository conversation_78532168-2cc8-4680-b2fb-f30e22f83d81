fields:

	code:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 2

	rdc_id:
		model:
			sourceid: 'code'
			source: 'list_required_doc'
		view:
			label: 'Required Doc Type'
			columns: 2

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 2

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['lists']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['code']
		]
	name: ['code']
	sections:
		'Document Type':
			fields: ['code', 'rdc_id', 'allow_sync', 'active']

view:
	comment: 'Manage > Document Type'
	find:
		basic: ['code']
	grid:
		fields: ['code']
		sort: ['code']
	label: 'Document Type'
	open: 'read'
