fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	route:
		model:
			prefill: ['encounter_steroid']
			source: ['Intravenous', 'Intramuscular']
			if:
				'Intravenous':
					sections: ['Steroid Pump']
		view:
			control: 'radio'
			label: 'Infusion Route'
			columns: 2

	# Infusion Device
	infus_devices:
		model:
			prefill: ['encounter_steroid']
			max: 8
			source: ['Pump', 'Gravity']
		view:
			control: 'radio'
			label: 'Infusion Devices'
			columns: 2

	infus_devices_com:
		model:
			prefill: ['encounter_steroid']
		view:
			label: 'Infusion Devices Comment'
			columns: 2

	# Drug Administration
	pump_type:
		model:
			prefill: ['encounter_steroid']
			max: 32
			source: ['N/A', 'Ambulatory', 'Syringe', 'Other']
			if:
				'Other':
					fields: ['pump_type_other']
		view:
			control: 'radio'
			label: 'Pump'
			columns: 2

	pump_type_other:
		model:
			prefill: ['encounter_steroid']
			max: 1024
			required: true
		view:
			label: 'Pump Other Details'
			columns: 2

	pump_maintenance_date:
		model:
			prefill: ['encounter_steroid']
			max: 1024
			type: 'date'
		view:
			label: 'Pump Maintenance Date'
			columns: 2

	pump_needs_replaced:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Was the pharmacy notified to replace pump?'
			columns: 2

	# Post-Visit
	add_training:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['add_training_details']
		view:
			control: 'radio'
			label: 'Will patient/caregiver need additional training?'
			columns: 2

	add_training_details:
		model:
			required: true
		view:
			label: 'Describe additional education requirements'
			columns: 2

	# Patient Questions
	fatigue_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['fatigue_episodes_cnt']
		view:
			control: 'radio'
			label: 'Have you had any episodes of fatigue?'
			columns: 2

	fatigue_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of fatigue episodes per week'
			columns: 2

	nausea_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['nausea_episodes_cnt']
		view:
			control: 'radio'
			label: 'Have you had any episodes of nausea or vomiting?'
			columns: 2

	nausea_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of nausea or vomiting episodes per week'
			columns: 2

	diarrhea_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['diarrhea_episodes_cnt']
		view:
			control: 'radio'
			label: 'Have you had any episodes of diarrhea?'
			columns: 2

	diarrhea_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of diarrhea episodes per week'
			columns: 2

	black_stool_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['black_stool_episodes_cnt']
		view:
			control: 'radio'
			label: 'Have you had any episodes of bloody, black, or tarry stools?'
			columns: 2

	black_stool_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of bloody, black, or tarry stools episodes per week'
			columns: 2

	constipation_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['constipation_episodes_cnt']
		view:
			control: 'radio'
			label: 'Have you had any episodes of constipation?'
			columns: 2

	constipation_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of constipation episodes per week'
			columns: 2

	urine_change:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any changes in amount, color, or frequency of urination?'
			columns: 2

	edema_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['edema_episodes_cnt']
		view:
			control: 'radio'
			label: 'Have you had any episodes of edema?'
			columns: 2

	edema_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of edema episodes per week'
			columns: 2

	appetite_change:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any changes in your appetite?'
			columns: 2

	trouble_eating:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any trouble eating or drinking throughout the day?'
			columns: 2

	weight_change:
		model:
			source: ['No', 'Yes', "Don't know"]
		view:
			control: 'radio'
			label: 'Have you had any significant changes in weight?'
			columns: 2

	pain_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pain_episodes_cnt', 'pain_episodes_scale', 'pain_episodes_location']
		view:
			control: 'radio'
			label: 'Have you had any episodes of pain?'
			columns: 2

	pain_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of pain episodes per week'
			columns: 2

	pain_episodes_scale:
		model:
			max: 1
			min: 1
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Based on a scale of 1 to 10 (10 being the highest), my pain usually averages around'
			columns: 2

	pain_episodes_location:
		view:
			label: 'Location of pain'
			columns: 2

	sleep_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['sleep_episodes_cnt']
		view:
			control: 'radio'
			label: 'Have you had any episodes of loss of sleep?'
			columns: 2

	sleep_episodes_cnt:
		model:
			min: 1
			type: 'int'
			required: true
		view:
			label: 'Number of loss of sleep episodes per week'
			columns: 2

	activity_changes:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you recently had to change your daily activities due to your Steroid treatments'
			columns: 2

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_steroid:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Patient Questionnaire - Steroid':
			area:'questions'
			fields: ['fatigue_episodes', 'fatigue_episodes_cnt',
				'nausea_episodes', 'nausea_episodes_cnt',
				'diarrhea_episodes', 'diarrhea_episodes_cnt',
				'black_stool_episodes', 'black_stool_episodes_cnt',
				'constipation_episodes', 'constipation_episodes_cnt',
				'urine_change', 'edema_episodes', 'edema_episodes_cnt',
				'appetite_change', 'trouble_eating', 'weight_change',
				'pain_episodes', 'pain_episodes_cnt', 'pain_episodes_scale',
				'pain_episodes_location', 'sleep_episodes',
				'sleep_episodes_cnt', 'activity_changes']
		'Steroid Infusion Device':
			fields: ['route', 'infus_devices', 'infus_devices_com']
			prefill: 'encounter_steroid'
		'Steroid Pump':
			fields: ['pump_type', 'pump_type_other', 'pump_maintenance_date', 'pump_needs_replaced']

view:
	comment: 'Patient > Careplan > Encounter > Steroid'
	label: 'Patient Encounter: Steroid'
