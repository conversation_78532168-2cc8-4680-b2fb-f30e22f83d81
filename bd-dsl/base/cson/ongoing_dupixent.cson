fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	indication:
		model:
			max: 3
			required: true
			prefill: ['ongoing_dupixent', 'assessment_dupixent']
			source: ['Atopic Dermatitis', 'Asthma']
			if:
				'Atopic Dermatitis':
					fields: ['dermatitis_symptoms_improved', 'dermatitis_symptoms_comment']
				'Asthma':
					fields: ['exacerbation_last_month', 'last_lung_function_date', 'last_lung_function_comment']
		view:
			control: 'radio'
			label: 'Indication'

	current_infection:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Do you have any current infections?'
			control: 'radio'

	exacerbation_last_month:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['exacerbation_cnt', 'exacerbation_steroids', 'exacerbation_er', 'exacerbation_hospitalization']
		view:
			control: 'radio'
			label: 'Has the patient had an asthma exacerbation in the last month?'

	exacerbation_cnt:
		model:
			type: 'int'
		view:
			label: 'Number of exacerbations'

	exacerbation_steroids:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Required treatment with steroids?'

	exacerbation_er:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Required ER visit?'

	exacerbation_hospitalization:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Required hospitalization?'

	last_lung_function_date:
		model:
			type: 'date'
		view:
			label: 'When was your last lung function test completed?'
	
	last_lung_function_comment:
		view:
			label: 'Lung function test comment:'

	dermatitis_symptoms_improved:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have dermatitis symptoms improved?'

	dermatitis_symptoms_comment:
		view:
			label: 'Dermatitis symptoms comment:'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		ongoing_dupixent:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
		assessment_dupixent:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id' ]
	sections:
		'Dupixent Assessment':
			fields: ['indication', 'current_infection',
			'exacerbation_last_month', 'exacerbation_cnt', 'exacerbation_steroids',
			'exacerbation_er', 'exacerbation_hospitalization',
			'last_lung_function_date', 'last_lung_function_comment', 'dermatitis_symptoms_improved',
			'dermatitis_symptoms_comment']
view:
	comment: 'Patient > Careplan > Ongoing > Dupixent'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Ongoing Assessment: Dupixent'
	open: 'read'
