fields:

	name:
		model:
			required: true
			max: 128
		view:
			label: 'Name'

	subform_order:
		model:
			type: 'subform'
			multi: true
			required: true
			source: 'template_order_item'
		view:
			label: 'Order Items'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				fields: ['inventory_id', 'type_id', 'therapy_id']
				label: ['Drug', 'Type', 'Therapy']
				width: [50, 25, 25]

	supply_kit_id:
		model:
			source: 'inventory_supply_kit'
		view:
			label: 'Supply Kit'

model:
	access:
		create:     []
		create_all: ['admin','pharm','csr','cm','physician']
		delete:     ['admin','pharm','csr','cm','physician']
		read:       ['admin','pharm','csr','cm','physician']
		read_all:   ['admin','pharm','csr','cm','physician']
		request:    []
		review:     ['admin','pharm','csr','cm','physician']
		update:     []
		update_all: ['admin','pharm','csr','cm','physician']
		write:      ['admin','pharm','csr','cm','physician']
	bundle: ['templates']
	indexes:
		many: [
			['name']
		]
		unique: [
			['name']
		]
	name: ['name', 'supply_kit_id']
	sections_group: [
		'Order Template':
			sections: [
				'Details':
					fields: ['name']
				'Template Items':
					fields: ['subform_order']
			]
	]

view:
	comment: 'Order Template'
	find:
		basic: ['name']
	grid:
		fields: ['name', 'created_by', 'created_on']
		sort: ['-id']
	label: 'Order Template'
	open: 'edit'