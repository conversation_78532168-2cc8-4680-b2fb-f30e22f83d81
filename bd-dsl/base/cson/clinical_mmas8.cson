fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_mmas8.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	# Health Improvement
	forget:
		model:
			multi:false
			required: true

			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you sometimes forget to take your medication?'
			validate: [
					name: 'MMAS8ScoreValidate'
			]

	did_not_take:
		model:
			multi:false
			required: true

			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'People sometimes miss taking their medications for reasons other than forgetting. Over the past 2 weeks, were there any days when you did not take your medication?'
			validate: [
					name: 'MMAS8ScoreValidate'
			]

	se_stopped_taking:
		model:
			multi:false
			required: true

			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you ever cut back or stopped taking your medication without telling your doctor because you felt worse when you took it?'
			validate: [
					name: 'MMAS8ScoreValidate'
			]

	forget_travel:
		model:
			multi:false
			required: true

			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'When you travel or leave home, do you sometimes forget to bring your medication?'
			validate: [
					name: 'MMAS8ScoreValidate'
			]

	taken_yesterday:
		model:
			multi:false
			required: true

			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Did you take all your medication yesterday?'
			validate: [
					name: 'MMAS8ScoreValidate'
			]

	well_stopped_taking:
		model:
			multi:false
			required: true

			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'When you feel like your symptoms are under control, do you sometimes stop taking your medication?'
			validate: [
					name: 'MMAS8ScoreValidate'
			]

	feel_hassled:
		model:
			multi:false
			required: true

			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Taking medication every day is a real inconvenience for some people. Do you ever feel hassled about sticking to your treatment plan?'
			validate: [
					name: 'MMAS8ScoreValidate'
			]

	remembering:
		model:
			multi:false
			required: true

			source: ['Never/Rarely', 'Once in a while', 'Sometimes', 'Usually', 'All the time']
		view:
			control: 'radio'
			label: 'How often do you have difficulty remembering to take all your medication?'
			validate: [
					name: 'MMAS8ScoreValidate'
			]

	score:
		model:
			min: 0
			max: 100
			rounding: 0.01
			type: 'decimal'
		view:
			note: '8 = High Adherence, 6-7 = Medium Adherence, < 6 = Low Adherence'
			label: 'Assessment Score'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_mmas8:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'MMAS-8':
			fields: ['last_assessment_date', 'assessment_date']
		'Questionnaire':
			fields: ['forget', 'did_not_take', 'se_stopped_taking', 'forget_travel',
			'taken_yesterday', 'well_stopped_taking', 'feel_hassled', 'remembering', 'score']
			prefill: 'clinical_mmas8'

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Morisky Medication Adherence (MMAS-8)'
	grid:
		fields: ['created_on', 'assessment_date', 'created_by', 'score']
		sort: ['-id']
	label: 'Morisky Medication Adherence (MMAS-8)'
