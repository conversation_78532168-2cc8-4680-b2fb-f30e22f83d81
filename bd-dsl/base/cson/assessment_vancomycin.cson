fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	trough_goal:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'If applicable'
			label: 'Vanco trough goal (mcg/ml)'

	trough_value:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'If available'
			label: 'Vanco trough (mcg/ml)'

	trough_date:
		model:
			type: 'datetime'
		view:
			note: 'If available'
			label: 'Vanco trough date/time'

	trough_true:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['trough_true_details']
		view:
			control: 'radio'
			label: 'True trough?'

	trough_true_details:
		view:
			label: 'True trough details'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Vancomycin Assessment':
			fields: ['trough_goal', 'trough_value', 'trough_date', 'trough_true', 'trough_true_details']

view:
	comment: 'Patient > Careplan > Assessment > Vancomycin'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Vancomycin'
	open: 'read'
