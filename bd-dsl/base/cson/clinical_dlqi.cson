fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_dlqi.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	itchy:
		model:
			required: true

			max: 64
			source: ['Very much', 'A lot', 'A little', 'Not at all', 'Not relevant']
		view:
			control: 'radio'
			label: "Over the last week, how itchy, sore, painful, or stinging has the patient's skin been?"

	self_conscious:
		model:
			required: true

			max: 64
			source: ['Very much', 'A lot', 'A little', 'Not at all', 'Not relevant']
		view:
			control: 'radio'
			label: "Over the last week, how embarrassed or self-conscious has the patient been because of his/her skin?"

	going_out:
		model:
			required: true

			max: 64
			source: ['Very much', 'A lot', 'A little', 'Not at all', 'Not relevant']
		view:
			control: 'radio'
			label: "Over the last week, how much has the patient's skin interfered with going shopping or looking after his/her home or garden?"

	clothes:
		model:
			required: true

			max: 64
			source: ['Very much', 'A lot', 'A little', 'Not at all', 'Not relevant']
		view:
			control: 'radio'
			label: "Over the last week, how much has the patient's skin influenced the clothes he/she wears?"

	social:
		model:
			required: true

			max: 64
			source: ['Very much', 'A lot', 'A little', 'Not at all', 'Not relevant']
		view:
			control: 'radio'
			label: "Over the last week, how much has the patient's skin affected any social or leisure activities?"

	sports:
		model:
			required: true

			max: 64
			source: ['Very much', 'A lot', 'A little', 'Not at all', 'Not relevant']
		view:
			control: 'radio'
			label: "Over the last week, how much has the patient's skin made it difficult to do any sport?"

	working:
		model:
			required: true

			max: 64
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['working_details']
		view:
			control: 'radio'
			label: "Over the last week, has the patient's skin prevented him/her from working or studying?"

	working_details:
		model:
			required: true

			max: 64
			source: ['A lot', 'A little', 'Not at all']
		view:
			control: 'radio'
			label: "Over the last week, how much has the patient's skin been a problem at work or studying?"

	friends:
		model:
			required: true

			max: 64
			source: ['Very much', 'A lot', 'A little', 'Not at all', 'Not relevant']
		view:
			control: 'radio'
			label: "Over the last week, how much has the patient's skin created problems with his/her partner or any close friends or relatives?"

	treatment_problems:
		model:
			required: true

			max: 64
			source: ['Very much', 'A lot', 'A little', 'Not at all', 'Not relevant']
		view:
			control: 'radio'
			label: "Over the last week, how much of a problem has the treatment for the patient's skin been, for example by making your home messy, or by taking up time?"


model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_dlqi:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Dermatology Life Quality Index (DLQI)':
			fields: ['last_assessment_date', 'assessment_date']
		'Questionnaire':
			fields: ['itchy', 'self_conscious', 'going_out', 'clothes', 'social', 'sports',
			'working', 'working_details', 'friends', 'treatment_problems']
			prefill: 'clinical_dlqi'
view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Dermatology Life Quality Index (DLQI)'
	find:
		basic: ['assessment_date']
	grid:
		fields: ['created_on', 'assessment_date', 'created_by']
		sort: ['-id']
	label: 'Dermatology Life Quality Index (DLQI)'
