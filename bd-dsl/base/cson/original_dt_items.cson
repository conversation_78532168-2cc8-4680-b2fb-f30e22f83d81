fields:
	
	rx_no:
		model:
			type: 'text'
		view:
			label: 'Rx #'
			readonly: false

	dispense_quantity:
		model:
			type: 'int'
		view:
			label: 'Quantity'

	hcpc_code:
		model:
			type: 'text'
		view:
			label: 'HCPC Code'

	drug:
		model:
			type: 'text'
		view:
			label: 'Drug'

	ndc_code:
		model:
			type: 'text'
		view:
			label: 'NDC Code'

	comment:
		model:
			required: false
		view:
			label: 'Comment'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     []
		read:       ['admin','pharm','csr','cm', 'nurse']
		read_all:   ['admin','pharm','csr','cm', 'nurse']
		request:    []
		review:     ['admin','pharm','csr','cm', 'nurse']
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['drug', 'dispense_quantity']
		]
	name: ['drug']
	sections:
		'Original DT Items':
			hide_header: true
			indent: false
			fields: ['rx_no', 'dispense_quantity', 'hcpc_code', 'drug', 'ndc_code']

view:
	hide_cardmenu: true
	comment: 'Original DT Items'
	grid:
		fields: ['rx_no', 'dispense_quantity', 'hcpc_code', 'drug', 'ndc_code']
		sort: ['-id']
	label: 'Original DT Items'
	open: 'edit'