fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'csr', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'csr', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]

view:
	comment: 'Patient > Careplan > Encounter > Ocrevus'
	label: 'Patient Encounter: Ocrevus'
