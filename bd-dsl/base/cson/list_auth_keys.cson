fields:
	code:
		model:
			max: 64
			required: true
		view:
			columns: 2
			label: 'Code'
			findunique: true

	authkey:
		view:
			columns: 2
			label: 'Authorization Key'
			findunique: true

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 2
			control: 'radio'
			label: 'Can Sync Record'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 2
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['manage']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['code']
		]
	name: '{code}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'authkey', 'allow_sync', 'active']

view:
	comment: 'Manage > Authorizaion Keys'
	find:
		basic: ['code', 'authkey']
	grid:
		fields: ['code', 'authkey']
		sort: ['code']
	label: 'Authorization Keys'
	open: 'read'
