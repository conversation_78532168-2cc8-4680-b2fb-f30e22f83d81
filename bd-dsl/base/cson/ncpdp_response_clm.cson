fields:

	rx_svc_no_ref_qualifier:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '455-EM'
		view:
			columns: 3
			note: '455-EM'
			label: 'Svc Ref Num Qual'
			readonly: true

	rx_svc_no:
		model:
			type: 'decimal'
		view:
			columns: 3
			note: '402-D2'
			label: 'Svc Ref Num ()'
			readonly: true

	subform_pref_prod:
		model:
			multi: true
			type: 'subform'
			source: 'ncpdp_response_clm_pref'
		view:
			label: 'Preferred Product'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['rx_svc_no_ref_qualifier', 'rx_svc_no']
	sections_group: [
		'Claim':
			sections: [
				'Details':
					hide_header: true
					indent: false
					fields: ['rx_svc_no_ref_qualifier', 'rx_svc_no']

				'Preferred Product(s)':
					hide_header: true
					indent: false
					fields: ['subform_pref_prod']

			]
		]

view:
	dimensions:
		width: '50%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Response Claim'
	grid:
		fields: ['rx_svc_no_ref_qualifier', 'rx_svc_no']
		sort: ['-created_on']
	label: 'Response Claim'
	open: 'read'