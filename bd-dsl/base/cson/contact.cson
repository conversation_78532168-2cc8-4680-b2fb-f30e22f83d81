fields:
	first:
		model:
			max: 64
			required: true
		view:
			label: 'First Name'
			note: 'Letters, numbers, comma, \', -, . only'
			validate: [
					name: 'NameValida<PERSON>'
			]
			columns: 3

	middle:
		model:
			max: 64
			required: true
		view:
			label: 'Middle Name'
			note: 'Letters, numbers, comma, \', -, . only'
			validate: [
					name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
			]
			columns: 3

	last:
		model:
			max: 64
			required: true
		view:
			label: 'Last Name'
			note: 'Letters, numbers, comma, \', -, . only'
			validate: [
					name: 'Name<PERSON>alida<PERSON>'
			]
			columns: 3

	title:
		model:
			max: 8
			required: true
		view:
			label: 'Title'
			columns: 3

	address:
		model:
			max: 128
		view:
			label: 'Address'
			class: "api_prefill"
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'address': ['addressLabel']
					'city': ['city']
					'state_id': ['stateCode']
					'zip': ['postalCode']
			]
			columns: 'addr_1'

	city:
		model:
			type: 'text'
		view:
			label: 'City'
			columns: 'addr_city'

	state_id:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			label: 'State'
			columns: 'addr_state'

	zip:
		model:
			max: 10
			min: 5
		view:
			format: 'us_zip'
			label: 'Zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'zip'
						city:'city'
						state:'state'
			]
			columns: 'addr_zip'

	phone:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Phone #'
			columns: 2

	fax:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Fax #'
			columns: 2

	email:
		model:
			max: 64
			min: 6
			required: false
		view:
			label: 'Email Address'
			validate: [
					name: 'EmailValidator'
			]
			columns: 2

	preferred_communication:
		model:
			source: ['Phone', 'Fax', 'Email']
		view:
			control: 'radio'
			label: 'Preferred Communication Route'
			columns: 2

	notes:
		view:
			control: 'area'
			label: 'Notes'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr','liaison', 'pharm']
		write:      ['admin', 'csr','liaison', 'pharm']
	indexes:
		many: [
			['first', 'last', 'title']
			['phone']
			['email']
		]

	name: '{last}, {first} {title} {phone} {email}'

	sections:
		'Name':
			fields: ['first', 'last', 'title']
		'Location':
			fields: ['address', 'city', 'state_id', 'zip', 'phone', 'fax', 'email', 'preferred_communication']
		'Notes':
			fields: ['notes']

view:
	hide_cardmenu: true
	comment: 'Manage > Contact'
	find:
		basic: ['first', 'last']
		advanced: ['city', 'state_id']
	grid:
		fields: ['first', 'last', 'title', 'address', 'phone']
		sort: ['last']
	label: 'Contact'
