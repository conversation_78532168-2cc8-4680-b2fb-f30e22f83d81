#TABLE RPEIGL0_GEN_DF_MSTR_LINK
fields:
    code:
        model:
            max: 64
            required: true
        view:
            label: 'Code'

    #DOSAGE_FORM_ID
    dosage_form_id:
        model:
            type: 'int'
        view:
            label: 'Dosage Form ID'
            findunique: true
            readonly: true
            columns: 3

    #GCDF
    gcdf:
        model:
            type: 'text'
        view:
            label: 'Generic Code Dosage Form'
            findunique: true
            readonly: true
            columns: 3

    #PREFERRED_DOSAGE_FORM_IND
    preferred_dosage_form_ind:
        model:
            type: 'int'
        view:
            label: 'Preferred Dosage Form Indicator'
            columns: 3

model:
    access:
        create:     []
        create_all: ['admin']
        delete:     ['admin']
        read:       ['admin']
        read_all:   ['admin']
        request:    []
        update:     []
        update_all: ['admin']
        write:      ['admin']
    bundle: ['reference']
    sync_mode: 'full'
    name: "{dosage_form_id} - {gcdf}"
    indexes:
        many: [
            ['dosage_form_id']
            ['gcdf']
            ['preferred_dosage_form_ind']
        ]
    sections:
        'Details':
            hide_header: true
            indent: false
            fields: ['dosage_form_id', 'gcdf', 'preferred_dosage_form_ind']

view:
    comment: 'Manage > List FDB Generic Dosage Form Master Link'
    find:
        basic: ['dosage_form_id', 'gcdf']
    grid:
        fields: ['dosage_form_id', 'gcdf', 'preferred_dosage_form_ind']
    label: 'List FDB Generic Dosage Form Master Link'
