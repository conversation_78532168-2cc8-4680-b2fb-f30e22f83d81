fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	time_taken:
		model:
			required: true
			type: 'datetime'
		view:
			label: 'Time Administered'
			columns: 3

	order_no:
		model:
			prefill: ['parent.order_no']
		view:
			label: 'Order #'
			readonly: true
			offscreen: true

	med_administered:
		model:
			prefill: ['encounter_rec_admin']
			required: true
			source: 'careplan_order_rx'
			type: 'int'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				order_no:
					'dynamic': '{order_no}'
				discontinued:
					'static': '!Yes'
				type_id:
					'static': 'Primary'
		view:
			control: 'select'
			label: 'Medication Administered'
			columns: 3
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/careplan_order_rx/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'dose_unit_id': ['dose_unit_id']
			]

	dose_given:
		model:
			type: 'decimal'
			rounding: 0.001
			required: true
			max: 9999999.999 
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 3
			label: 'Dose'

	dose_unit_id:
		model:
			required: true
			source: 'list_unit'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Dosing Unit'
			readonly: true
			transform: [
				{
					name: 'UpdateUnitFieldNote'
					target: ['dose_given']
				}
			]

	comments:
		view:
			label: 'Comments'
			control: 'area'

model:
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_rec_admin:
			link:
				careplan_id: 'careplan_id'
			max: 'id'
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: '{time_taken} : {med_administered} {dose_given} {dose_unit_id}'
	sections:
		'Med Administration':
			hide_header: true
			indent: false
			fields: ['order_no', 'time_taken', 'med_administered',
			'dose_given', 'dose_unit_id', 'comments']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Encounter > Admin > Rectal'
	grid:
		fields: ['time_taken', 'med_administered', 'dose_given', 'dose_unit_id']
	label: 'Admin Recatal'
