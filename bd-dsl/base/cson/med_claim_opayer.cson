fields:

	patient_id:
		model:
			type: 'int'
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	cob_insurance_id:
		model:
			type: 'int'
			source: 'patient_insurance'
			prefill: ['parent.insurance_id']
		view:
			label: 'Insurance'
			offscreen: true
			readonly: true

	cob_payer_id:
		model:
			type: 'int'
			source: 'payer'
			prefill: ['parent.cob_payer_id']
		view:
			label: 'Payer'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/payer/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'other_payer_organization_name': ['organization'],
					'other_payer_identifier': ['mm_payer_id'],
					'other_payer_address':
						'type': 'subform'
						'field': 'other_payer_address'
						'fields':
							'address1': ['pr.address1']
							'address2': ['pr.address2']
							'city': ['pr.city']
							'state': ['pr.state_id']
							'postal_code': ['pr.zip']
					'other_payer_identifier_type_code':
						'type': 'conditional'
						'type_id':
							if:
								'MCRB': 'XV'
								'MCRD': 'XV'
								'MEDI': 'PI'
								'CMMED': 'PI'
								'CMPBM': 'PI'
								'SELF': 'PI'
								'COPAY': 'PI'
								'PAP': 'PI'
								'FOUND': 'PI'
								'OTHER': 'PI'
			]

	other_payer_organization_name:
		model:
			min: 1
			max: 60
			required: true
		view:
			columns: 2
			label: 'Organization Name'
			reference: 'NM103'
			_meta:
				location: '2330B NM1'
				field: '03'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherPayerName.otherPayerOrganizationName'

	other_payer_identifier_type_code:
		model:
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'NM108-2330B'
		view:
			columns: 4
			label: 'Identification Code'
			reference: 'NM108'
			_meta:
				location: '2330B NM1'
				field: '08'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherPayerName.otherPayerIdentifierTypeCode'

	other_payer_identifier:
		model:
			required: true
			min: 2
			max: 80
		view:
			columns: 4
			label: 'Other PayerIdentifier'
			reference: 'NM109'
			_meta:
				location: '2330B NM1'
				field: '09'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherPayerName.otherPayerIdentifier'

	other_payer_adjudication_or_payment_date:
		model:
			type: 'date'
		view:
			columns: -4
			label: 'Adjustment/Payment Date'
			note: 'Must be in MM/DD/YYYY format'
			reference: 'DTP03 DTP01=573'
			_meta:
				location: '2330B DTP'
				field: '03'
				code: '573'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherPayerName.otherPayerAdjudicationOrPaymentDate'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	other_payer_claim_adjustment_indicator:
		model:
			source:
				'Y': 'Yes'
		view:
			columns: 4
			label: 'Claim had adjustments?'
			control: 'checkbox'
			class: 'checkbox-only'
			reference: 'REF02 REF01=T4'
			_meta:
				location: '2330B REF'
				code: 'T4'
				field: '02'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherPayerName.otherPayerClaimAdjustmentIndicator'
			offscreen: true
			readonly: true

	pa_id:
		model:
			source: 'patient_prior_auth'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': '5'
				insurance_id:
					'dynamic': '{cob_insurance_id}'
		view:
			columns: -2
			label: 'Prior Auth'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_prior_auth/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'other_payer_prior_authorization_number': ['number']
			]

	other_payer_prior_authorization_number:
		model:
			min: 1
			max: 50
		view:
			columns: 4
			label: 'Prior Auth #'
			reference: 'REF02 REF01=G1'
			_meta:
				location: '2330B REF'
				code: 'G1'
				field: '02'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherPayerName.otherPayerPriorAuthorizationNumber'

	other_payer_claim_control_number:
		model:
			min: 1
			max: 50
		view:
			columns: 4
			label: 'Claim Control Number (CCN)'
			reference: 'REF02 REF01=F8'
			_meta:
				location: '2330B REF'
				code: 'F8'
				field: '02'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherPayerName.otherPayerClaimControlNumber'

	other_payer_secondary_identifier:
		model:
			multi: true
			source: 'med_claim_opayer_id'
			type: 'subform'
		view:
			note: 'Max 2'
			label: 'Secondary Identifier(s)'
			grid:
				add: 'inline'
				edit: true
				fields: ['qualifier', 'identifier']
				label: ['Qualifier', 'ID']
				width: [50, 50]
			max_count: 2

	other_payer_address:
		model:
			type: 'subform'
			multi: false
			source: 'med_claim_address_opay'
		view:
			label: 'Address'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['other_payer_organization_name', 'other_payer_identifier_type_code']
	sections_group: [
		'Other Payer':
			hide_header: true
			sections: [
				'Other Payer':
					hide_header: true
					tab: 'Payer'
					fields: ['patient_id', 'cob_insurance_id', 'cob_payer_id', 'other_payer_organization_name', 'other_payer_identifier_type_code',
					'other_payer_identifier', 'other_payer_adjudication_or_payment_date',
					'other_payer_claim_adjustment_indicator', 'pa_id', 'other_payer_prior_authorization_number',
					'other_payer_claim_control_number']
				'Address':
					hide_header: true
					indent: false
					tab: 'Payer'
					fields: ['other_payer_address']
				'Secondary Identifier(s)':
					hide_header: true
					indent: false
					tab: 'Secondary ID'
					fields: ['other_payer_secondary_identifier']
			]
	]
view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	reference: ['2330B', '2330C', '2330D', '2330E', '2330F', '2330G']
	comment: 'Med Claim Other Payer'
	grid:
		fields: ['other_payer_organization_name', 'other_payer_identifier', 'other_payer_adjudication_or_payment_date', 'other_payer_prior_authorization_number']
		sort: ['-created_on']
	label: 'Med Claim Other Payer'
	open: 'read'