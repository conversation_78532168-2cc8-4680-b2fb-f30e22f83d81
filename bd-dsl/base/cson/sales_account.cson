fields:

	name:
		model:
			required: true
		view:
			label: 'Display Name'
			columns: 3

	type:
		model:
			required: true
			source: ['Candidate', 'Contact', 'Physician', 'Organization']
			if:
				'Candidate':
					fields: ['dob', 'gender','firstname','lastname', 'middlename', 'inhibitors']
					sections: ['Primary Therapy & Drug', 'Primary Diagnosis', 'H&P']
				'Contact':
					fields: ['dob', 'gender', 'firstname','lastname', 'middlename']
					sections: ['Contact Info', 'Contact Status']
				'Physician':
					fields: ['npi_number', 'ig_grams', 'dob', 'gender', 'firstname','lastname', 'middlename']
				'Organization':
					fields: ['website', 'facility_name']
		view:
			readonly: true
			# control: 'radio'
			label: 'Type'
			columns: 3

	firstname:
		model:
			search: 'A'
			access:
				read: ['cm', 'cma', 'liaison', 'nurse', 'patient', 'physician']
			max: 32
			min: 1
			required: true
		view:
			label: 'First Name'
			note: 'Letters, numbers, comma, \', -, . only'
			columns: 3
			validate: [{
					name: 'UpdateTabLabel'
					fields: ['firstname', 'middlename', 'lastname']
					},
					{
						name: 'Name<PERSON>alida<PERSON>'
					}
					]

	lastname:
		model:
			search: 'B'
			access:
				read: ['cm', 'cma', 'liaison', 'nurse', 'patient', 'physician']
			max: 32
			min: 1
			required: true
		view:
			label: 'Last Name'
			note: 'Letters, numbers, \', -, . only'
			columns: 3
			validate: [{
					name: 'UpdateTabLabel'
					fields: ['firstname', 'middlename', 'lastname']
					},
					{
						name: 'NameValidator'
					}
					]

	middlename:
		model:
			search: 'C'
			access:
				read: ['cm', 'cma', 'liaison', 'nurse', 'patient', 'physician']
			max: 32
			min: 0
		view:
			label: 'Middle Name'
			note: 'Letters, numbers, \', -, . only'
			columns: 3
			validate: [{
					name: 'UpdateTabLabel'
					fields: ['firstname', 'middlename', 'lastname'] },
					{
						name: 'NameValidator'
					}]

	dob:
		model:
			type: 'date'
		view:
			control: 'input'
			label: 'Date of Birth'
			note: 'Must be in MM/DD/YYYY format'
			columns: 3
			validate: [
					name: 'DOBValidator'
			]

	gender:
		model:
			access:
				read: ['cm', 'cma', 'liaison', 'nurse', 'patient', 'physician']
			max: 8
			min: 1
			required: true
			source: ['Female', 'Male']
		view:
			control: 'radio'
			label: 'Sex'
			columns: 3
	
	inhibitors:
		view:
			label: 'Inhibitors'
			columns: 3
	
	preferred_communication:
		model:
			source: ['Phone', 'Fax', 'Email', 'Portal']
		view:
			control: 'radio'
			label: 'Preferred Communication Route'
			columns: 3

	primary_contact:
		view:
			offscreen: true
			readonly: true
			label: 'Primary Contact'
			columns: 3

	territory_id:
		model:
			required: true
			source: 'sales_territory'
		view:
			label: 'Territory Code:'
			columns: 3
			transform: [
				name: 'SelectPrefill'
				url: '/form/sales_territory/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'sales_rep': ['commissioned_sales_rep']
			]

	sales_rep:
		model:
			source:'user'
			sourcefilter:
				role:
					'static': 'liaison'
			required: true
		view:
			label: 'Sales Rep:'
			template: '{{user.id}}'
			columns: 3

	status:
		model:
			default: 'Pending'
			required: true
			source: [
				'Lead',
				'Prospect',
				'Opportunity',
				'Active',
				'On-Hold',
				'Inactive'
				]
			if:
				'On-Hold':
					fields: ['onhold_note', 'onhold_reminder_date']
				'Prospect':
					sections: ['Prospect']
				'Opportunity':
					sections: ['Opportunity']
		view:
			class: 'status'
			label: 'Status:'
			columns: 3
			validate: [
					name: 'CalculateReminder90'
			]

	likelihood:
		model:
			source: ['Low', 'Medium', 'High']
		view:
			control: 'radio'
			label: 'Likelihood?'
			columns: 3

	amount:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Amount'
			columns: 3

	close_date:
		model:
			type: 'date'
		view:
			label: 'Close Date'
			columns: 3

	target_date:
		model:
			type: 'date'
		view:
			label: 'Target Close Date'
			columns: 3

	industry:
		model:
			source: ['Speciality', 'Infusion', 'DME', 'Hospital', 'payer', 'Manufacturer', 'Other']
			if:
				'Other':
					fields: ['industry_details']
		view:
			control: 'radio'
			label: 'Industry'
			columns: 3

	industry_details:
		model:
			required: true
		view:
			label: 'Details'
			columns: 3

	size:
		model:
			source: ['Local', 'Regional', 'National']
		view:
			control: 'radio'
			label: 'Size'
			columns: 3

	revenue:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Annual Revenue'
			columns: 3

	locations:
		model:
			type: 'decimal'
			rounding: 1
		view:
			label: 'Number of Locations'
			columns: 3

	therapies:
		model:
			multi: true
			source: ['Factor/Hemophilia', 'Hepatitis C', 'IVIG', 'Inotropes', 'Lemtrada', 'Oncology/Chemotherapy', 'TNF', 'TPN', 'Radicava', 'Other']
			if:
				'Other':
					fields: ['therapies_details']
		view:
			control: 'checkbox'
			note: 'Select all that apply'
			label: 'Therapies'
			columns: -2

	therapies_details:
		model:
			required: true
		view:
			label: 'Details'
			control: 'area'
			columns: 2

	urac:
		model:
			source: ['No, but looking to', 'No, not interested', 'Yes']
		view:
			control: 'radio'
			label: 'URAC cerified?'
			columns: 3

	software:
		model:
			multi: true
			source: ['Therigy', 'Asembia', 'MHA', 'CPR+', 'Rockpond', 'McKesson', 'QS1', 'New Leaf', 'PioneerRX', 'ScriptMed', 'Salesforce', 'Other']
			if:
				'Other':
					fields: ['software_details']
		view:
			control: 'checkbox'
			note: 'Select all that apply'
			label: 'Current Software'
			columns: -2

	software_details:
		model:
			required: true
		view:
			label: 'Details'
			control: 'area'
			columns: 2

	onhold_note:
		model:
			required: true
		view:
			label: 'On-Hold Note'
			columns: 3

	onhold_reminder_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'On-Hold Reminder Date'
			columns: 3

	phone:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Phone #'
			columns: 3

	phone_alt:
		view:
			format: 'us_phone'
			label: 'Alt. Phone'
			columns: 3

	fax:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Fax #'
			columns: 3

	phone_office:
		view:
			format: 'us_phone'
			label: 'Office Phone'
			columns: 3

	email:
		view:
			label: 'Email'
			columns: 3
			validate: [
					name: 'EmailValidator'
			]

	notes:
		model:
			subfields:
				date:
					label: 'Date'
					type: 'timestamp'
					readonly: true
				user:
					label: 'User'
					source: '{{user.displayname}}'
					type: 'text'
					readonly: true
				note:
					label: 'Note'
					type: 'text'
			type: 'json'
		view:
			control: 'grid'
			label: 'Notes'

	street:
		view:
			label: 'Street 1'
			columns: 'addr_1'
			class: "address_fill"
			# transform: [
			# 	name: ''
			# 	fields: ['street', 'street2', 'city', 'state', 'zip']
			# ]
			class: "api_prefill"
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'street': ['addressLabel']
					'city': ['city']
					'state': ['stateCode']
					'zip': ['postalCode']
			]


			# show my dropdown written in react
			## if class is present, as you type, you see dropdown, when clicked on dropdown validator will run to select one of the fields shown on dropdown
			# validate: [
			# 		name: 'AutoCompleteAddress'
			# ]

	street2:
		view:
			label: 'Street 2'
			columns: 'addr_2'

	city:
		view:
			label: 'City'
			columns: 'addr_city'

	state:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			label: 'State'
			columns: 'addr_state'

	zip:
		model:
			max: 10
			min: 5
		view:
			columns: 'addr_zip'
			format: 'us_zip'
			label: 'Zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'zip'
						city:'city'
						state:'state'
			]

	website:
		view:
			label: 'Website'
			columns: 3

	import_id:
		model:
			required: false
			source: 'sales_call_import'
		view:
			readonly: true
			offscreen: true
			columns: 3

	owner:
		model:
			required: true
			source: 'user'
			sourcefilter:
				role:
					static: ['admin', 'liaison']
		view:
			label: 'Lead Owner'
			template: '{{user.id}}'
			columns: 3

	salutation:
		view:
			label: 'Salutation'
			columns: 3

	title:
		model:
			required: true
		view:
			label: 'Title'
			columns: 3

	role:
		model:
			required: true
		view:
			label: 'Role'
			columns: 3

	decision_maker:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Decision Maker?"
			columns: 3

	linkedin_id:
		view:
			label: 'LinkedIn ID'
			columns: 3

	url:
		view:
			label: 'URL'
			columns: 3

	lead_status:
		model:
			multi: false
			source: ['Initial Outreach', 'Initial Contact', 'Ongoing Contact', 'Cold']
		view:
			control: 'checkbox'
			label: "Lead Status"
			columns: 3

	rating:
		model:
			source: ['1', '2', '3', '4', '5']
		view:
			note: '1 = No Contact - 5 = Ongoing Relationship'
			control: 'radio'
			label: "Rating"
			columns: 3

	opt_out:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Opt-Out?"
			columns: 3

	lead_source:
		view:
			label: 'Lead Source'
			columns: 3

	date_entered:
		model:
			type: 'date'
		view:
			label: 'Date Entered'
			template: '{{now}}'
			columns: 3

	next_contact:
		model:
			type: 'date'
		view:
			label: 'Next Contact'
			template: '{{now}}'
			columns: 3

	next_action:
		view:
			label: 'Next Action'
			columns: 3

	attempts:
		model:
			type: 'decimal'
			rounding: 1
		view:
			label: 'Attempts'
			columns: 3

	client:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Current client?"
			columns: 3

	product_type:
		view:
			label: 'Product Type'
			columns: 3

	description:
		view:
			control: 'area'
			label: 'Description'
			columns: -1

###### 	CONTACT 
######		DETAILS
######			ABOVE
	
	npi_number:
		model:
			source: 'physician'
		view:
			label: 'NPI Number'
			columns: 3

	ig_grams:
		model:
			type: 'decimal'
		view:
			label: 'IG Grams'
			columns: 3

	facility_name:
		model:
			source: 'facility'
		view:
			label: 'Facility Name'
			columns: 3

	therapy_1:
		model:
			max: 64
			if:
				'*':
					sections: ['Secondary Therapy & Drug']
			required: false
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Primary Therapy'
			columns: 3

	therapy_2:
		model:
			max: 64
			if:
				'*':
					sections: ['Tertiary Therapy & Drug']
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Secondary Therapy'
			columns: 3

	therapy_3:
		model:
			max: 64
			if:
				'*':
					sections: ['Quaternary Therapy & Drug']
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Tertiary Therapy'
			columns: 3

	therapy_4:
		model:
			max: 64
			if:
				'*':
					sections: ['Quinary Therapy & Drug']
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Quaternary Therapy'
			columns: 3

	therapy_5:
		model:
			max: 64
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Quinary Therapy'
			columns: 3

	drug_brand_1:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Primary Drug Brand'
			columns: 3

	drug_brand_2:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Secondary Drug Brand'
			columns: 3

	drug_brand_3:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Tertiary Drug Brand'
			columns: 3

	drug_brand_4:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Quaternary Drug Brand'
			columns: 3

	drug_brand_5:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Quinary Drug Brand'
			columns: 3

	dx_1:
		model:
			if:
				'*':
					sections: ['Secondary Diagnosis']
			required: false
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			label: 'Primary Diagnosis'
			columns: 3

	dx_date_1:
		model:
			type: 'date'
		view:
			label: 'Primary Diagnosis Onset Date'
			columns: 3

	dx_by_1:
		model:
			source: 'physician'
			type: 'int'
		view:
			label: 'Primary Diagnosed By'
			columns: 3
			validate: [
					name: 'CareplanPhysicianOnboardingValidator'
			]

	dx_2:
		model:
			if:
				'*':
					sections: ['Tertiary Diagnosis']
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			label: 'Secondary Diagnosis'
			columns: 3

	dx_date_2:
		model:
			type: 'date'
		view:
			label: 'Secondary Diagnosis Onset Date'
			columns: 3

	dx_by_2:
		model:
			source: 'physician'
			type: 'int'
		view:
			label: 'Secondary Diagnosed By'
			columns: 3
			validate: [
					name: 'CareplanPhysicianOnboardingValidator'
			]

	dx_3:
		model:
			if:
				'*':
					sections: ['Quaternary Diagnosis']
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			label: 'Tertiary Diagnosis'
			columns: 3

	dx_date_3:
		model:
			type: 'date'
		view:
			label: 'Tertiary Diagnosis Onset Date'
			columns: 3

	dx_by_3:
		model:
			source: 'physician'
			type: 'int'
		view:
			label: 'Tertiary Diagnosed By'
			columns: 3
			validate: [
					name: 'CareplanPhysicianOnboardingValidator'
			]

	dx_4:
		model:
			if:
				'*':
					sections: ['Quinary Diagnosis']
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			label: 'Quaternary Diagnosis'
			columns: 3

	dx_date_4:
		model:
			type: 'date'
		view:
			label: 'Quaternary Diagnosis Onset Date'
			columns: 3

	dx_by_4:
		model:
			source: 'physician'
			type: 'int'
		view:
			label: 'Quaternary Diagnosed By'
			columns: 3
			validate: [
					name: 'CareplanPhysicianOnboardingValidator'
			]

	dx_5:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			label: 'Quinary Diagnosis'
			columns: 3

	dx_date_5:
		model:
			type: 'date'
		view:
			label: 'Quinary Diagnosis Onset Date'
			columns: 3

	dx_by_5:
		model:
			source: 'physician'
			type: 'int'
		view:
			label: 'Quinary Diagnosed By'
			columns: 3
			validate: [
					name: 'CareplanPhysicianOnboardingValidator'
			]

	height:
		model:
			max: 250
			min: 15
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'unit'
			label: 'Height (cm)'
			note: 'E.g.: 143, 143cm, 1.43m, 56in, 56", 4\' 8", 4 8'
			columns: 3
			transform: [
					name: 'HeightTransform'
			]

	weight:
		model:
			max: 1000
			min: 1
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'unit'
			label: 'Weight (Kg)'
			note: 'E.g.: 78, 78kg, 172lbs'
			columns: 3
			transform: [
					name: 'WeightTransform'
			]

	activity_level:
		model:
			source: ['Up with walker', 'Up with crutches', 'Up as tolerated', 'Up with walker', 'Sedentary', 'Up with assistance', 'Up ad lib', 'Newborn', 'Up ad lib with wheelchair', 'Wheelchair', 'Wheelchair, Electric', 'Bedrest with Bathroom Privileges', 'Bed rest', 'Infant', 'Maximum assistance', 'Up with wheelchair']

		view:
			label: 'Activity Level'
			columns: 3

	subform_attachment:
		model:
			multi: true
			source: 'sales_attachment'
			type: 'subform'
		view:
			label: 'File Attachments'
			note: 'Max 100MB. Only documents, images, and archives supported.'
	
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison']
		delete:     ['admin']
		read:       ['admin', 'csr', 'liaison']
		read_all:   ['admin', 'csr', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'liaison']
		write:      ['admin', 'csr', 'liaison']
	reportable: true
	indexes:
		unique: [
			['name', 'sales_rep']
		]
	name: '{name} {city}, {state} {zip} P:{phone}'
	sections: 
		'Account Type':
			fields: ['type']
		'Sales Rep': 
			fields: ['territory_id', 'sales_rep', 'status', 'onhold_note', 'onhold_reminder_date', 'website' ]
		'Demographics':
			fields: ['name', 'firstname', 'lastname', 'middlename', 'dob', 'gender', 'inhibitors', 'preferred_communication', 'npi_number', 'ig_grams', 'facility_name', 'linkedin_id', 'url','lead_source']
		'Phone':
			fields: ['phone', 'phone_alt', 'fax', 'phone_office', 'email']
		'Address':
			fields: ['street', 'street2', 'city', 'state', 'zip' ]
		'Contact Info':
			fields: ['owner', 'salutation', 'title', 'role', 'decision_maker', 'date_entered', 'next_contact', 'next_action', 'attempts','description']
		'Contact Status':
			fields: ['lead_status', 'rating', 'opt_out', 'client', 'product_type']
		'H&P':
			fields: ['height', 'weight', 'activity_level']
		'Opportunity':
			fields: ['likelihood', 'amount', 'close_date', 'target_date']
		'Prospect':
			fields: ['industry', 'industry_details', 'size', 'revenue',
			'locations', 'urac', 'therapies', 'therapies_details', 'software', 'software_details']
		'Primary Therapy & Drug':
			fields: ['therapy_1', 'drug_brand_1']
		'Secondary Therapy & Drug':
			fields: ['therapy_2', 'drug_brand_2']
		'Tertiary Therapy & Drug':
			fields: ['therapy_3', 'drug_brand_3']
		'Quaternary Therapy & Drug':
			fields: ['therapy_4', 'drug_brand_4']
		'Quinary Therapy & Drug':
			fields: ['therapy_5', 'drug_brand_5']
		'Primary Diagnosis':
			fields: ['dx_1', 'dx_date_1', 'dx_by_1']
		'Secondary Diagnosis':
			fields: ['dx_2', 'dx_date_2', 'dx_by_2']
		'Tertiary Diagnosis':
			fields: ['dx_3', 'dx_date_3', 'dx_by_3']
		'Quaternary Diagnosis':
			fields: ['dx_4', 'dx_date_4', 'dx_by_4']
		'Quinary Diagnosis':
			fields: ['dx_5', 'dx_date_5', 'dx_by_5']
		'Notes':
			fields: ['notes']
		'File Attachments':
			fields: ['subform_attachment'] # subform


view:
	comment: 'Manage > Demographics'
	find:
		basic: ['name', 'sales_rep', 'status', 'city', 'state']
	grid:
		fields: ['name', 'primary_contact', 'status', 'city', 'state', 'phone', 'fax']
		sort: ['name']
	label: 'Sales Account'