fields:

	code:
		model:
			required: true
		view:
			label: 'Code'
			columns: 2

	name:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 2

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: []
		write:      []
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['name']
		]
		many: [
			['code']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name', 'code']

view:
	comment: 'Manage > Surescripts Order Generation Options'
	find:
		basic: ['name']
	grid:
		fields: ['name']
		sort: ['name']
	label: 'Surescripts Order Generation Options'
	open: 'read'
