fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	raw_barcode:
		model:
			type: 'text'
			max: 160
		view:
			class: 'barcode'
			columns: 1
			control: 'barcode'
			label: 'Scan Barcode'
			validate: [{
				name: 'BarcodeParseValidator'
				fields:
					raw: 'barcode'
					gtin: 'gtin'
					lot: 'lot_no'
					serial: 'serial_no'
					quantity: 'quantity'
					expiration: 'expiration'
			}]

	barcode:
		model:
			type: 'text'
			if:
				'*':
					fields: ['gtin', 'serial_no']
		view:
			label: 'Barcode'
			readonly: true
			offscreen: true

	gtin:
		model:
			required: false
			type: 'text'
		view:
			columns: 3
			label: 'GTIN'

	lot_no:
		model:
			required: true
			type: 'text'
		view:
			columns: 3
			label: 'Lot'

	serial_no:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Serial No'

	quantity:
		model:
			min: 1
			required: true
			rounding: 1
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 3
			label: 'Quantity'

	expiration:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Expiration'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['lot_no', 'quantity', 'expiration']
	sections:
		'Lot Used':
			hide_header: true
			indent: false
			fields: ['barcode', 'raw_barcode', 'gtin', 'lot_no', 'serial_no', 'quantity', 'expiration']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Encounter > Admin > Lot Used'
	grid:
		fields: ['lot_no', 'quantity', 'expiration']
	label: 'Lot Used'
