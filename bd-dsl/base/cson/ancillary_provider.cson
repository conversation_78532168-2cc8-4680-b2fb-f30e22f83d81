fields:

	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	type:
		model:
			required: true
			source: ['HTC', 'Other']
			if:
				'*':
					fields: ['name']
					sections: ['Address']
		view:
			control: 'radio'
			label: 'Type'

	name:
		model:
			required: true
		view:
			label: 'Name'
			validate: [
				{
					name: 'UpdateTabLabel'
					fields: ['name']
				}
			]
			findunique: true

	phone:
		model:
			required: true
			max: 21
		view:
			format: 'us_phone'
			label: 'Phone'

	fax:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Fax'

	address1:
		view:
			columns: 'addr_1'
			label: 'Address 1'

	address2:
		view:
			columns: 'addr_2'
			label: 'Address 2'

	city:
		view:
			columns: 'addr_city'
			label: 'City'

	state_id:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'State'

	zip:
		model:
			max: 10
			min: 5
		view:
			columns: 'addr_zip'
			format: 'us_zip'
			label: 'Zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'zip'
						city:'city'
						state:'state_id'
			]

	notes:
		view:
			control: 'area'
			label: 'Notes'

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'ancillary_provider'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Ancillary Provider'
					anc_provider_id: '{code}'
					direct_attachment: 'Yes'
					source: 'Scanned Document'
					form_name: 'ancillary_provider'
					form_code: '{code}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'pharm']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'pharm', 'physician']
		request:    []
		update:     ['nurse']
		update_all: ['admin','csr', 'liaison', 'pharm']
		write:      ['admin','csr', 'liaison', 'pharm']
	indexes:
		unique: [
			['name']
			['code']
		]
	name: ['name']
	bundle: ['location']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'type', 'name', 'phone', 'fax']
			tab: 'Ancillary Provider'
		'Address':
			indent: false
			fields: ['address1', 'address2', 'city', 'state_id', 'zip']
			tab: 'Ancillary Provider'
		'Notes':
			indent: false
			fields: ['notes']
			tab: 'Ancillary Provider'
		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'

view:
	hide_cardmenu: true
	comment: 'Ancillary Provider'
	find:
		basic: ['name', 'type', 'phone']
	grid:
		fields: ['name', 'type', 'phone', 'fax', 'notes']
		sort: ['name']
	label: 'Ancillary Provider'
	open: 'read'
