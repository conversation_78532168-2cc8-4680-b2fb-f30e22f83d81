fields:

	pricing_update_no:
		view:
			label: 'Pricing Update No'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			source: 'inventory'
		view:
			columns: 2
			label: 'Drug'
			readonly: true

	obsdtec:
		model:
			type: 'date'
		view:
			label: 'Date Obsolete'
			columns: 4
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin','pharm','csr','cm', 'nurse']
		read_all:   ['admin','pharm','csr','cm', 'nurse']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['pricing_update_no'],
			['inventory_id']
		]

	name: ['inventory_id']
	sections_group: [
		'Inventory Drug Obsolete Update':
			hide_header: true
			indent: false
			fields: ['pricing_update_no', 'inventory_id', 'obsdtec']
	]

view:
	dimensions:
		width: '45%'
		height: '45%'
	hide_cardmenu: true
	comment: 'Inventory Drug Obsolete Update'
	find:
		basic: ['inventory_id']
		advanced: []
	grid:
		fields: ['inventory_id', 'obsdtec']
		sort: ['-id']
	label: 'Inventory Drug Obsolete Update'
	open: 'read'