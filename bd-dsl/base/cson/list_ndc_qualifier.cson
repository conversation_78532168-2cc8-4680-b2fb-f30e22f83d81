fields:

	code:
		model:
			max: 64
			required: true
		view:
			columns: 2
			label: 'Code'
			findunique: true

	name:
		model:
			required: true
			max: 128
		view:
			columns: 2
			label: 'Name'
			findunique: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: '{code} - {name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name']

view:
	comment: 'Manage > NDC Qualifier'
	find:
		basic: ['code','name']
	grid:
		fields: ['code','name']
		sort: ['code']
	label: 'NDC Qualifier'
	open: 'read'
