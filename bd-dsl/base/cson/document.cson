fields:

	external_id:
		model:
			required: false
			if:
				'*':
					fields: ['orig_filename']
		view:
			label: 'External ID'
			readonly: true
			offscreen: true

	direct_attachment:
		model:
			source: ['Yes']
			if:
				'!':
					fields: ['assigned_to']
		view:
			label: 'Direct Attachment'
			control: 'checkbox'
			class: 'checkbox-only'
			readonly: true
			offscreen: true

	# Document
	source:
		model:
			default: 'Scanned Document'
			source: ['Fax', 'Scanned Document', 'External File/Document']
			if:
				'Fax':
					sections: ['Fax']
		view:
			columns: 2
			control: 'radio'
			label: 'Document Source'

	assigned_to:
		model:
			source: ['Patient', 'Physician', 'Insurance Company', 'Alt. Payer', 'Nursing Agency', 'Inventory Item',
			'User', 'Supplier', 'Ancillary Provider', 'Copay Program', 'PAP Program', 'Patient Form']
			if:
				'Patient':
					fields: ['patient_id']
					prefill:
						form_name: 'patient'
				'Physician':
					fields: ['physician_id']
					prefill:
						form_name: 'physician'
				'Insurance Company':
					fields: ['payer_id']
					require_fields: ['payer_id']
					prefill:
						form_name: 'payer'
				'Alt. Payer':
					fields: ['alt_payer_id']
					prefill:
						form_name: 'list_alt_payer'
				'Nursing Agency':
					fields: ['agency_id']
					prefill:
						form_name: 'nurse_agency'
				'User':
					fields: ['user_id']
					prefill:
						form_name: 'user'
				'Supplier':
					fields: ['supplier_id']
					prefill:
						form_name: 'supplier'
				'Ancillary Provider':
					fields: ['anc_provider_id']
					prefill:
						form_name: 'ancillary_provider'
				'Inventory Item':
					fields: ['inventory_id']
					prefill:
						form_name: 'inventory'
				'Copay Program':
					fields: ['copay_program_id']
					prefill:
						form_name: 'list_copay_program'
				'PAP Program':
					fields: ['pap_program_id']
					prefill:
						form_name: 'list_pap_program'
				'Patient Form':
					fields: ['form_filter']
		view:
			control: 'checkbox'
			class: 'checkbox checkbox-4'
			label: 'Assigned To'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'assigned_to_filter'
					overwrite: true
				}
			]

	form_code:
		model:
			required: false
		view:
			label: 'Direct Form Code'
			readonly: true
			offscreen: true

	form_name:
		model:
			required: false
		view:
			label: 'Form Name'
			readonly: true
			offscreen: true

	form_filter:
		model:
			source:
				careplan_delivery_tick: 'Delivery Ticket'
				careplan_order: 'Order'
				careplan_order_item: 'Therapy Set Order'
				careplan_order_rx: 'Patient Prescription'
				careplan_nursing_485: 'Nursing Visit'
				treatment_plan_nursing: 'Nursing Visit'
				careplan_order_bv: 'Nursing Visit'
				po: 'Purchase Order'
				billing_invoice: 'Invoice'
				billing_posting: 'Cash Posting'
				encounter_agency: 'Agency Visit'
				encounter: 'Nursing Visit'
				careplan_schedule_encounter: 'Scheduled Visit'
				patient_assistance: 'Patient Assistance'
				patient_bill_note: 'Billing Note'
				patient_labresult: 'Lab Results'
				patient_prior_auth: 'Patient Authorization'
				assessment: 'Initial Assessment'
				ongoing: 'Ongoing Assessment'
			required: false
			if:
				'careplan_delivery_tick':
					fields: ['patient_id', 'patient_form_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Delivery Ticket'
					prefill:
						form_name: 'careplan_delivery_tick'
				'careplan_order':
					fields: ['patient_id', 'send_on_med_claim_order', 'patient_form_id', 'type_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Order'
					prefill:
						form_name: 'careplan_order'
				'careplan_order_rx':
					fields: ['patient_id', 'send_on_med_claim_order', 'patient_form_id', 'type_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Prescription'
					prefill:
						form_name: 'careplan_order_rx'
				'careplan_order_item':
					fields: ['patient_id', 'send_on_med_claim_order', 'patient_form_id', 'type_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Therapy Set Order'
					prefill:
						form_name: 'careplan_order_item'
				'careplan_nursing_485':
					fields: ['patient_id', 'patient_form_id', 'type_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Nursing Visit'
					prefill:
						form_name: 'careplan_nursing_485'
				'treatment_plan_nursing':
					fields: ['patient_id', 'patient_form_id', 'type_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Nursing Visit'
					prefill:
						form_name: 'treatment_plan_nursing'
				'careplan_order_bv':
					fields: ['patient_id', 'patient_form_id', 'type_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Nursing Visit'
					prefill:
						form_name: 'careplan_order_bv'
				'po':
					fields: ['site_form_id']
					require_fields: ['site_id']
					field_label:
						patient_form_id: 'default'
					prefill:
						form_name: 'po'
				'billing_invoice':
					fields: ['patient_id', 'payer_id_filter', 'form_patient_payer_id']
					require_fields: ['patient_id']
					field_label:
						'form_patient_payer_id': 'Patient Billing Invoice'
					prefill:
						form_name: 'billing_invoice'
				'billing_posting':
					fields: ['patient_id', 'payer_id_filter', 'form_patient_payer_id']
					require_fields: ['patient_id']
					field_label:
						'form_patient_payer_id': 'Patient Billing Posting'
					prefill:
						form_name: 'billing_posting'
				'encounter_agency':
					fields: ['patient_id', 'patient_form_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Agency Visit'
					prefill:
						form_name: 'encounter_agency'
				'encounter':
					fields: ['patient_id', 'patient_form_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Nursing Visit'
					prefill:
						form_name: 'encounter'
				'careplan_schedule_encounter':
					fields: ['patient_id', 'patient_form_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Nursing Visit'
					prefill:
						form_name: 'careplan_schedule_encounter'
				'patient_assistance':
					fields: ['patient_id', 'patient_form_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Assistance'
					prefill:
						form_name: 'patient_assistance'
				'patient_bill_note':
					fields: ['patient_id', 'patient_form_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Billing Note'
					prefill:
						form_name: 'patient_bill_note'
				'patient_labresult':
					fields: ['patient_id', 'patient_form_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Lab Results'
					prefill:
						form_name: 'patient_labresult'
				'patient_prior_auth':
					fields: ['patient_id', 'patient_form_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Prior Authorization'
					prefill:
						form_name: 'patient_prior_auth'
				'assessment':
					fields: ['patient_id', 'patient_form_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Assessment'
					prefill:
						form_name: 'assessment'
				'ongoing':
					fields: ['patient_id', 'patient_form_id']
					require_fields: ['patient_id']
					field_label:
						'patient_form_id': 'Patient Ongoing Assessment'
					prefill:
						form_name: 'ongoing'
		view:
			label: 'Direct Form Name'
			transform: [
				{
					name: 'ClearFieldsIfChanged'
					fields: ['patient_form_id']
				}
			]
			control: 'select'

	type_id:
		model:
			source: 'list_doc_type'
			sourceid: 'code'
			sourcefilter:
				active:
					'static': 'Yes'
		view:
			columns: 2
			label: 'Document Type'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'name'
					overwrite: false
				}
			]

	flag_id:
		model:
			required: false
			multi: true
			source: 'list_flag'
		view:
			columns: 4
			label: 'Flags'

	file_path:
		model:
			required: true
			type: 'json'
		view:
			class: 'media-preview'
			control: 'file'
			label: 'File Attachment'
			note: 'Max 100MB. Only documents, images, and archives supported.'

	file_pages:
		model:
			type: 'int'
		view:
			label: 'Page Count'
			readonly: true
			offscreen: true

	file_data:
		model:
			type: 'json'
		view:
			label: 'Extracted Text'
			readonly: true
			offscreen: true

	# Links
	site_id:
		model:
			required: false
			source: 'site'
			type: 'int'
		view:
			columns: 3
			label: 'Site'

	site_form_id:
		model:
			required: true
			source: '{form_name}'
			sourcefilter:
				site_id:
					'dynamic': '{site_id}'
			if:
				'*':
					fields: ['view_site_form']
		view:
			label: 'Select Form'
			control: 'select'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/{form_name}/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'code': ['form_code']
			]

	view_site_form:
		model:
			default: '{"url":"{#/{form_name}/{site_form_id}/read}","title":"View Form", "icon": "fa fa-link"}'
			type: 'json'
			save: false
		view:
			label: ''
			control: 'link'
			columns: 3
			offscreen: false
			readonly: true

	patient_id:
		model:
			type: 'int'
			source: 'patient'
		view:
			columns: 3
			label: 'Patient'

	patient_form_id:
		model:
			required: false 
			source: '{form_name}'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
			if:
				'*':
					fields: ['view_patient_form']
					trigger: ['view_patient_form']
		view:
			label: 'Select Form'
			control: 'select'
			class: 'select_prefill'
			columns: 3
			transform: [
				name: 'SelectPrefill'
				url: '/form/{form_name}/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'code': ['form_code']
			]

	view_patient_form:
		model:
			default: '{"url":"clararx://flyout/{form_filter}/{patient_form_id}","title":"View Form", "icon": "fa fa-link"}'
			type: 'json'
			save: false
		view:
			label: ''
			control: 'link'
			columns: 3
			offscreen: false
			readonly: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Inventory Item'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'form_code'
					overwrite: true
				}
			]

	physician_id:
		model:
			required: true
			sourceid: 'code'
			source: 'physician'
		view:
			columns: 3
			label: 'Physician'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'form_code'
					overwrite: true
				}
			]

	payer_id:
		model:
			required: true
			sourceid: 'code'
			source: 'payer'
		view:
			columns: 3
			label: 'Payer'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'form_code'
					overwrite: true
				}
			]

	payer_id_filter:
		model:
			required: false
			source: 'payer'
			type: 'int'
		view:
			columns: 3
			label: 'Payer'

	form_patient_payer_id:
		model:
			required: true
			source: 'payer'
			sourceid: 'code'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				payer_id:
					'dynamic': '{payer_id_filter}'
			if:
				'*':
					fields: ['view_patient_payer_form']
		view:
			columns: 3
			label: 'Select Payer'
			control: 'select'
			class: 'select_prefill'
			readonly: true
			transform: [
				name: 'SelectPrefill'
				url: '/form/{form_name}/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'code': ['form_code']
			]

	view_patient_payer_form:
		model:
			default: '{"url":"{#/{form_name}/{form_patient_payer_id}/read}","title":"View Form", "icon": "fa fa-link"}'
			type: 'json'
			save: false
		view:
			label: ''
			control: 'link'
			columns: 3
			offscreen: false
			readonly: true

	alt_payer_id:
		model:
			required: true
			sourceid: 'code'
			source: 'list_alt_payer'
		view:
			columns: 3
			label: 'Alt. Payer'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'form_code'
					overwrite: true
				}
			]

	agency_id:
		model:
			required: true
			source: 'nurse_agency'
			sourceid: 'code'
			sourcefilter:
				active:
					'static': 'Yes'
		view:
			columns: 3
			label: 'Nursing Agency'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'form_code'
					overwrite: true
				}
			]

	user_id:
		model:
			required: true
			source: 'user'
			sourceid: 'code'
		view:
			columns: 3
			label: 'User'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'form_code'
					overwrite: true
				}
			]

	supplier_id:
		model:
			required: true
			sourceid: 'code'
			source: 'list_supplier'
			if:
				'*':
					prefill:
						form_name: 'supplier'
		view:
			columns: 3
			label: 'Supplier'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'form_code'
					overwrite: true
				}
			]

	anc_provider_id:
		model:
			required: true
			sourceid: 'code'
			source: 'ancillary_provider'
			if:
				'*':
					prefill:
						form_name: 'ancillary_provider'
		view:
			columns: 3
			label: 'Ancillary Provider'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'form_code'
					overwrite: true
				}
			]

	copay_program_id:
		model:
			required: true
			source: 'list_copay_program'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Copay Program'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'form_code'
					overwrite: true
				}
			]

	pap_program_id:
		model:
			required: true
			source: 'list_pap_program'
			sourceid: 'code'
		view:
			columns: 3
			label: 'PAP Program'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'form_code'
					overwrite: true
				}
			]

	print_on_dt:
		model:
			source: ['Yes', 'First Fill Only']
		view:
			columns: 3
			label: 'Print On'
			control: 'radio'

	name:
		model:
			required: true
		view:
			columns: -3
			label: 'Filename'

	send_on_med_claim_rental:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['sent_insr_id', 'dme_attachment_transmission_code', 'form_identifier']
					prefill:
						flag_id: 17
		view:
			columns: 3
			control: 'checkbox'
			label: 'Send with Medical Claim?'
			class: 'checkbox-only'

	send_on_med_claim_order:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['sent_insr_id', 'order_attachment_report_type_code', 'order_attachment_transmission_code']
		view:
			columns: 3
			control: 'checkbox'
			label: 'Send with Medical Claim?'
			class: 'checkbox-only'

	sent_insr_id:
		model:
			multi: true
			source: 'patient_insurance'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
				type_id:
					'static': '!SELF'
				billing_method_id:
					'static': ['mm']
		view:
			columns: 3
			label: 'Already Sent to Payer(s)'

	order_attachment_report_type_code:
		model:
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'PWK01'
		view:
			columns: 3
			label: 'Report Type'
			reference: 'PWK01'
			class: 'claim-field'


	order_attachment_transmission_code:
		model:
			default: 'EL'
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'PWK02'
		view:
			columns: 3
			label: 'Transmission Code'
			note: 'If electronically, will be sent on medical claim submission.'
			reference: 'PWK02'
			class: 'claim-field'

	dme_attachment_transmission_code:
		model:
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'PWK02-DME-Code'
		view:
			columns: 3
			label: 'DME CMN Transmission Code'
			reference: 'PWK02'
			class: 'claim-field'

	form_identifier:
		model:
			required: true
			min: 1
			max: 30
		view:
			columns: 3
			label: 'Form Industry Code'
			note: 'i.e. DME 03.03'
			reference: 'LQ02'
			class: 'claim-field'

	# Fax-related
	fax_id:
		view:
			columns: 3
			label: 'Fax ID'
			readonly: true

	received:
		model:
			type: 'datetime'
		view:
			columns: 3
			label: 'Received'
			readonly: true
	to_phone:
		view:
			columns: 3
			label: 'To Phone'
			readonly: true

	from_phone:
		view:
			columns: 3
			label: 'From Phone'
			readonly: true

	from_name:
		view:
			columns: 3
			label: 'From Name'
			readonly: true

	from_loc:
		view:
			columns: 3
			label: 'From Location'
			readonly: true

	# Footer
	comments:
		view:
			control: 'area'
			label: 'Comments'
	orig_filename:
		view:
			columns: 3
			label: 'Original Filename'
			readonly: true
	sent_dt:
		model:
			type: 'datetime'
		view:
			columns: 3
			label: 'Sent Date/Time'
			readonly: true
	doc_status:
		view:
			columns: 3
			label: 'Document Status'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']

	indexes:
		many: [
			['fax_id']
			['patient_id']
			['inventory_id']
			['physician_id']
			['payer_id']
			['alt_payer_id']
			['agency_id']
			['supplier_id']
			['anc_provider_id']
			['copay_program_id']
			['pap_program_id']
			['form_name']
			['form_filter']
			['site_id']
			['form_code']
			['user_id', 'external_id']
			['external_id']
		]

	name: 'Source: {source} Type: {type_id} Pages: {file_pages} Received: {received} From: {from_phone}'

	sections:
		'File':
			hide_header: true
			indent: false
			fields: ['file_path']
			tab: 'File'

		'Category':
			hide_header: true
			indent: false
			fields: [
						'direct_attachment', 'form_code', 'assigned_to', 'form_name', 'form_filter', 'source', 'type_id', 'flag_id'
					]
			tab: 'Meta Data'

		'Links/Name':
			hide_header: true
			indent: false
			fields: [
						'site_id', 'site_form_id', 'view_site_form', 'patient_id',
						'patient_form_id', 'view_patient_form', 'inventory_id',
						'physician_id', 'payer_id', 'payer_id_filter', 'form_patient_payer_id', 'view_patient_payer_form', 'alt_payer_id', 'agency_id', 'user_id',
						'supplier_id', 'anc_provider_id', 'copay_program_id', 'pap_program_id', 'print_on_dt', 'name'
					]
			tab: 'Meta Data'

		'Med Claim':
			hide_header: true
			indent: false
			fields: [
						'send_on_med_claim_rental', 'send_on_med_claim_order',
						'sent_insr_id', 'order_attachment_report_type_code', 'order_attachment_transmission_code',
						'order_attachment_report_type_code', 'dme_attachment_transmission_code',
						'form_identifier'
					]
			tab: 'Meta Data'

		'Fax':
			hide_header: true
			indent: false
			fields: [
						'fax_id', 'sent_dt','received', 'to_phone',
						'from_phone', 'from_name', 'from_loc'
					]
			tab: 'Meta Data'

		'Comments':
			hide_header: true
			indent: false
			fields: ['orig_filename', 'sent_dt', 'doc_status', 'comments']
			tab: 'Meta Data'

view:
	dimensions:
		width: '75%'
		height: '75%'
	comment: 'Document / File'
	find:
		basic: ['source', 'assigned_to', 'type_id', 'flag_id', 'patient_id', 'from_phone', 'from_name']
	grid:
		fields: ['source', 'type_id', 'flag_id', 'file_pages', 'patient_id', 'received', 'from_phone', 'from_name', 'comments']
		sort: ['-id']
	label: 'Document / File'
	open: 'read'
