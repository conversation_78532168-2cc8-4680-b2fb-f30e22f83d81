fields:

	query:
		model:
			type: 'json'
			subfields:
				code:
					label: 'Code'
					type: 'text'
					style:
						width: '20%'
				sql:
					label: 'SQL'
					type: 'area'
					style:
						width: '80%'
		view:
			control: 'grid'
			label: 'Queries'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	save: false
	name: ['id']
	sections:
		'Main':
			fields: ['query']
view:
	comment: 'View Report Query'
	grid:
		fields: []
	label: 'Report Queries'
	hide_cardmenu: true
	open: 'edit'
