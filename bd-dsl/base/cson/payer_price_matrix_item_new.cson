fields:
	payer_price_matrix_id:
		model:
			type: 'int'
			required: true
		view:
			offscreen: true
			readonly: true
			label: 'Assigned Shared Contract'

	price_matrix_item_id:
		model:
			required: true
			source: 'payer_price_matrix_item'
		view:
			form_link_enabled: true
			label: 'Assigned Shared Contract Item'
			offscreen: true
			readonly: true

	payer_contract_id:
		model:
			required: true
			source: 'payer_contract'
		view:
			form_link_enabled: true
			columns: 2
			readonly: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
		view:
			form_link_enabled: true
			columns: 2
			readonly: true
			label: 'Inventory Item'

	reviewed:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['reviewed_by', 'reviewed_datetime']
				'!':
					prefill:
						reviewed_by: ''
						reviewed_datetime: ''
		view:
			columns: 4
			control: 'checkbox'
			label: 'Reviewed?'
			class: 'checkbox-only'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						reviewed: 'Yes'
					dest: 'reviewed_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						reviewed: 'Yes'
					dest: 'reviewed_by'
				}
			]

	reviewed_by:
		model:
			source: 'user'
		view:
			label: 'Reviewed By'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'confirmed'
					source_fields: ['confirmed_by', 'reviewed_datetime']
					separator: ' @ '
				}
			]

	reviewed_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			label: 'Reviewed Date/Time'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['payer_price_matrix_id',
			'price_matrix_item_id',
			'payer_contract_id',
			'inventory_id',
			'reviewed']
		]
	reportable: true
	name: '{payer_contract_id_auto_name} {inventory_id_auto_name}'

	sections:
		'Contact Item Information':
			hide_header: true
			indent: false
			fields: ['payer_contract_id', 'inventory_id', 'reviewed']

view:
	dimensions:
		width: '45%'
		height: '45%'
	comment: 'Payer > Added Contract Item'
	grid:
		fields: ['inventory_id', 'payer_contract_id', 'reviewed']
		sort: ['-inventory_id']
	label: 'Added Contract Item'
	open: 'edit'