fields:

	claim_no:
		view:
			label: 'Claim #'
			readonly: true

	request_d0_raw:
		view:
			control: 'area'
			label: 'Raw D0 request'
			readonly: true

	request_d0_b64:
		view:
			label: 'Raw D0 request (Base64)'
			readonly: true

	request_json_data:
		model:
			type: 'json'
		view:
			label: 'Request JSON Data'
			readonly: true

	response_json_data:
		model:
			type: 'json'
		view:
			label: 'Response JSON Data'
			readonly: true

	response_d0_raw:
		view:
			control: 'area'
			label: 'Raw D0 response'
			readonly: true

	response_d0_b64:
		view:
			label: 'Raw D0 response (Base64)'
			readonly: true

	reversal_information:
		model:
			type: 'json'
		view:
			label: 'Reversal Information'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['claim_no']

	sections:
		'Response Log':
			fields: ['claim_no', 'request_d0_raw', 'request_d0_b64',
			'request_json_data', 'response_d0_raw', 'response_d0_b64',
			'response_json_data', 'reversal_information']

view:
	dimensions:
		width: '85%'
		height: '85%'
	hide_cardmenu: true
	comment: 'NCPDP Response Log'
	find:
		basic: ['claim_no']
	grid:
		fields: ['created_on', 'created_by', 'claim_no', 'response_json_data']
		width: [20, 15, 15, 50]
		sort: ['-created_on']
	label: 'NCPDP Response Log'
	open: 'read'