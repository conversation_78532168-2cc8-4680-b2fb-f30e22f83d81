fields:

	code:
		model:
			max: 64
			required: true
		view:
			columns: 3
			label: 'Code'
			findunique: true

	name:
		model:
			required: true
		view:
			columns: 3
			label: 'Name'
			findunique: true

	template_type:
		model:
			default: 'PO'
			required: true
			source: ['PO', 'IV', 'Injection', 'Factor', 'Compound', 'TPN']
			if:
				'Factor':
					prefill:
						refill_tracking: 'Doses'
					readonly:
						fields: ['refill_tracking']
		view:
			label: 'Template Type'
			control: 'radio'
			columns: 3

	refill_tracking:
		model:
			required: true
			source: ['Refills', 'Doses']
		view:
			columns: 3
			label: 'Refill Tracking'
			control: 'radio'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 3
			control: 'radio'
			label: 'Can Sync Record'

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 3
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['setup']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['code']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'refill_tracking', 'template_type', 'allow_sync', 'active']

view:
	comment: 'Manage > RX Template'
	find:
		basic: ['code', 'name', 'template_type', 'refill_tracking']
	grid:
		fields: ['code', 'name', 'template_type', 'refill_tracking']
		sort: ['code']
	label: 'RX Template'
	open: 'read'
