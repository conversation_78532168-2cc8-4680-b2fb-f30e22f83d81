fields:

	name:
		model:
			required: true
			min: 1
			max: 60
		view:
			columns: 2
			label: 'Name'
			reference: 'PER02'
			_meta:
				location: '1000A PER'
				code: 'IC'
				type: 'many'
				field: ['02', '01']
				path: 'submitter.contactInformation.name'

	email:
		model:
			max: 256
			min: 1
			required: false
		view:
			columns: 2
			label: 'Email Address'
			reference: 'PER04 PER03 = EM'
			_meta:
				location: '1000A PER'
				code: 'EM'
				type: 'many'
				field: ['04', '08']
				path: 'submitter.contactInformation.email'
			validate: [
					name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'
			]

	phone_number:
		model:
			required: true
			max: 21
		view:
			columns: -2
			format: 'us_phone'
			label: 'Phone #'
			reference: 'PER04 PER03 = TE'
			_meta:
				location: '1000A PER'
				code: 'TE'
				type: 'many'
				field: ['04', '06', '08']
				path: 'submitter.contactInformation.phoneNumber'

	fax_number:
		model:
			max: 21
		view:
			columns: 2
			format: 'us_phone'
			label: 'Fax #'
			reference: 'PER04 PER03 = FX'
			_meta:
				location: '1000A PER'
				code: 'FX'
				type: 'many'
				field: ['04', '06', '08']
				path: 'submitter.contactInformation.faxNumber'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['name']
	sections:
		'Contact Information':
			hide_header: true
			fields: ['name', 'email', 'phone_number', 'fax_number']

view:
	dimensions:
		width: '65%'
		height: '45%'
	hide_cardmenu: true
	reference: '1000A'
	comment: 'Submitter Contact Information'
	grid:
		fields: ['name', 'email', 'phone_number', 'fax_number']
		width: [30, 30, 20, 20]
		sort: ['-created_on']
	label: 'Submitter Contact Information'
	open: 'read'