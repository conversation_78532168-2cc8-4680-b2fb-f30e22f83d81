fields:
	sales_account_id:
		model:
			required: true
			source: 'sales_account'
			type: 'int'
		view:
			label: 'Sales Account'
			columns: 3

	is_primary:
		model:
			source: ['No', 'Yes']
		view:
			label: 'Primary?'
			control: 'radio'
			note: 'Only a single primary contact will appear on the sales account list'
			columns: 3

	first:
		model:
			max: 64
			required: true
		view:
			label: 'First Name'
			note: 'Letters, numbers, comma, \', -, . only'
			columns: 3
			validate: [
					name: 'NameValida<PERSON>'
			]

	middle:
		model:
			max: 64
			required: true
		view:
			label: 'Middle Name'
			note: 'Letters, numbers, comma, \', -, . only'
			columns: 3
			validate: [
					name: 'NameValidator'
			]

	last:
		model:
			max: 64
			required: false
		view:
			label: 'Last Name'
			note: 'Letters, numbers, comma, \', -, . only'
			columns: 3
			validate: [
					name: '<PERSON><PERSON>alida<PERSON>'
			]

	contact_tags:
		model:
			# multi: truex
			required: false
			source: ['Decision Maker', 'Referral Coordinator']
		view:
			control: 'checkbox'
			label: 'Contact Tags'
			columns: 3

	title:
		model:
			max: 128
			required: false
		view:
			label: 'Title'
			columns: 3

	reminders:
		view:
			label: 'Reminders'
			note: 'Appears in the snapshot area'
			control: 'area'

	phone:
		model:
			prefill: ['sales_account_contact']
			max: 21
		view:
			format: 'us_phone'
			label: 'Phone #'
			columns: 3

	cell_phone:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Cell Phone  #'
			columns: 3

	fax:
		model:
			prefill: ['sales_account_contact']
			max: 21
		view:
			format: 'us_phone'
			label: 'Fax #'
			columns: 3

	preferred_communication:
		model:
			source: ['Phone', 'Cell', 'Fax', 'Email']
		view:
			control: 'radio'
			label: 'Preferred Communication Route'
			columns: 3

	email:
		model:
			max: 64
			min: 6
			required: false
		view:
			label: 'Email Address'
			columns: 3
			validate: [
					name: 'EmailValidator'
			]

	notes:
		model:
			subfields:
				date:
					label: 'Date'
					type: 'timestamp'
					readonly: true
				user:
					label: 'User'
					source: '{{user.displayname}}'
					type: 'text'
					readonly: true
				note:
					label: 'Note'
					type: 'text'
			type: 'json'
		view:
			control: 'grid'
			label: 'Notes'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison']
		delete:     ['admin']
		read:       ['admin', 'csr', 'liaison']
		read_all:   ['admin', 'csr', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'liaison']
		write:      ['admin', 'csr', 'liaison']
	indexes:
		many: [
			['first', 'last', 'title']
			['phone']
		]

	name: '{last}, {first} {title} {phone}'
	prefill:
		sales_account_contact:
			link:
				sales_account_id: 'sales_account_id'

	sections:
		'Contact Info.':
			fields: ['sales_account_id', 'first', 'last', 'title', 'contact_tags', 'is_primary']
		'Contact':
			fields: ['phone', 'cell_phone', 'fax', 'email', 'preferred_communication']
		'Notes':
			fields: ['reminders', 'notes']

view:
	comment: 'Sales Account > Account Contact'
	find:
		basic: ['first', 'last', 'contact_tags']
	grid:
		fields: ['last', 'first', 'title', 'contact_tags', 'phone', 'cell_phone', 'fax', 'is_primary']
		sort: ['last']
	label: 'Account Contact'
