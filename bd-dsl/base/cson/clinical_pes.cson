fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_pes.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	# Survey Participation
	mood:
		model:
			required: true

			max: 64
			source: ['Not at all', 'A little', 'Moderately', 'Quite a bit', 'To an extreme degree']
		view:
			control: 'radio'
			label: 'Mood'

	walk:
		model:
			required: true

			max: 64
			source: ['Not at all', 'A little', 'Moderately', 'Quite a bit', 'To an extreme degree']
		view:
			control: 'radio'
			label: 'Ability to walk or move around'

	sleep:
		model:
			required: true

			max: 64
			source: ['Not at all', 'A little', 'Moderately', 'Quite a bit', 'To an extreme degree']
		view:
			control: 'radio'
			label: 'Sleep'

	work:
		model:
			required: true

			max: 64
			source: ['Not at all', 'A little', 'Moderately', 'Quite a bit', 'To an extreme degree']
		view:
			control: 'radio'
			label: 'Normal work (both outside your home and at home)'

	rec_activities:
		model:
			required: true

			max: 64
			source: ['Not at all', 'A little', 'Moderately', 'Quite a bit', 'To an extreme degree']
		view:
			control: 'radio'
			label: 'Recreational activities'

	enjoyment:
		model:
			required: true

			max: 64
			source: ['Not at all', 'A little', 'Moderately', 'Quite a bit', 'To an extreme degree']
		view:
			control: 'radio'
			label: 'Enjoyment of life'
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_pes:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'MOS Pain Effects Scale (PES)':
			note: 'Individuals with MS can sometimes experience unpleasant sensory symptoms as a result of their MS (e.g., pain, tingling, burning). The next set of questions covers pain and other unpleasant sensations, and how they affect the patient. Select that best answer that indicates the extent to which patient’s sensory symptoms (including pain) interfered with that aspect of life during the past 4 weeks. Please answer every question. If patient is not sure which answer to select, please choose the one answer that comes closest to describing.'
			fields: ['last_assessment_date','assessment_date']
		'Questionnaire':
			note: "During the past 4 weeks, how much did these symptoms interfere with the patient’s"
			fields: ['mood', 'walk', 'sleep', 'work', 'rec_activities', 'enjoyment']
			prefill: 'clinical_pes'
view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > MOS Pain Effects Scale (PES)'
	find:
		basic: ['assessment_date']
	grid:
		fields: ['created_on', 'assessment_date', 'created_by']
		sort: ['-id']
	label: 'MOS Pain Effects Scale (PES)'
