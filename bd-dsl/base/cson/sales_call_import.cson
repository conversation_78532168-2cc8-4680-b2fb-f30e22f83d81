fields:

	file:
		model:
			required: true
			type: 'json'
		view:
			control: 'file'
			label: 'Document / File'
			note: 'Only CSV documents are supported.'
			columns: 2

	force_import:
		model:
			default: 'No'
			required: false
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Force Reimport this file.'
			columns: 2

	last_run:
		model:
			type: 'datetime'
		view:
			label: 'Last Run'
			readonly: true
			columns: 2

	last_run_status:
		model:
			required: false
		view:
			label: 'Last Run Status'
			readonly: true
			columns: 2
	
	logs:
		model:
			type: 'text'
		view:
			label: 'Logs'
			control: 'area'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison']
		delete:     ['admin']
		read:       ['admin', 'csr', 'liaison']
		read_all:   ['admin', 'csr', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'liaison']
		write:      ['admin', 'csr', 'liaison']
	name: ['created_on', 'last_run', 'last_run_status']
	sections:
		'Import File':
			fields: ['file', 'force_import']
		'Stats':
			fields: ['last_run', 'last_run_status', 'logs']

view:
	comment: 'Manage > Import Sales Calls'
	grid:
		fields: ['last_run', 'last_run_status', 'created_by', 'created_on']
		sort: ['-id']
	label: 'Import Sales Calls'
