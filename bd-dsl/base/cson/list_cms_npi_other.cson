fields:

	npi:
		model:
			source: 'list_cms_npi'
			sourceid: 'code'
		view:
			columns: 3
			label: 'NPI'
			findunique: true

	other_provider_identifier:
		view:
			columns: 3
			label: 'Other Provider Identifier'

	other_provider_identifier_type_code:
		view:
			columns: 3
			label: 'Other Provider Identifier Type Code_1'

	other_provider_identifier_state:
		view:
			columns: 3
			label: 'Other Provider Identifier State_1'

	other_provider_identifier_issuer:
		view:
			columns: 3
			label: 'Other Provider Identifier Issuer_1'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		many: [
			['npi']
		]
	name: ['npi']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['npi', 'other_provider_identifier', 'other_provider_identifier_type_code', 'other_provider_identifier_state', 'other_provider_identifier_issuer']

view:
	comment: 'Manage > Billing Other Provider Identifier'
	find:
		basic: ['npi']
	grid:
		fields: ['npi', 'other_provider_identifier']
	label: 'Billing Other Provider Identifier'
