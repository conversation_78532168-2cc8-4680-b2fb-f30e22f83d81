fields:

	code:
		model:
			required: true
		view:
			columns: 2
			label: 'Code'
			readonly: true

	name:
		model:
			search: 'A'
			required: true
			max: 128
		view:
			columns: 2
			label: 'Exception'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['lists']
	sync_mode: 'full'
	indexes:
		many: [
				['code']
			]
	name: '{code} - {name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name']

view:
	comment: 'Manage > Delivery Ticket Exception'
	find:
		basic: ['code', 'name']
	grid:
		fields: ['code', 'name']
		width: [15, 50]
		sort: ['name']
	label: 'Delivery Ticket Exception'
	open: 'read'
