fields:

	name:
		model:
			required: true
		view:
			label: 'Name'
	
	code:
		model:
			required: true
		view:
			label: 'Team Code'

	members:
		model:
			source: 'user'
			multi: true
		view:
			label: 'Members'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
model:
	access:
		create:     ['admin']
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    ['admin']
		update:     ['admin']
		update_all: ['admin']
		write:      ['admin']
	name: '{name} ({code})'
	bundle: ['setup']
	sync_mode: 'mixed'
	sections:
		'Main':
			fields: ['name', 'code', 'members', 'allow_sync', 'active']
	indexes:
		many: [
			['name']
			['code']
		]
		unique: [
			['name']
		]

view:
	comment: 'Workflow Queue Team'
	find:
		basic: ['name', 'members']
	grid:
		fields: ['name', 'members']
	label: 'Workflow Queue Team'
	open: 'read'
