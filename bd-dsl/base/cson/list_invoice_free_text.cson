fields:

	name:
		model:
			required: true
			max: 128
		view:
			columns: 3
			label: 'Free Form Message'
			findunique: true

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 2
			control: 'radio'
			label: 'Active'
			findfilter: 'Yes'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 3
			control: 'radio'
			label: 'Can Sync Record'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['lists']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['name']
		]
	name: '{name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name', 'active', 'allow_sync']

view:
	comment: 'Manage > Free Text Message'
	find:
		basic: ['name']
	grid:
		fields: ['name', 'active', 'allow_sync']
		sort: ['name']
	label: 'Free Text Message'
	open: 'read'
