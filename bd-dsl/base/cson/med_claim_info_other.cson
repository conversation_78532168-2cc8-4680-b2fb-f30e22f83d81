fields:
	site_id:
		model:
			prefill: ['parent.site_id']
			type: 'int'
			source: 'site'
		view:
			label: 'Site'
			readonly: true
			offscreen: true

	patient_id:
		model:
			type: 'int'
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true
			class: 'select_prefill'

	payer_id:
		model:
			prefill: ['parent.payer_id']
			type: 'int'
			source: 'payer'
		view:
			label: 'Payer'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/payer/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'mm_send_contract_pricing': ['mm_send_contract_pricing']
			]

	mm_send_contract_pricing:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['claim_contract_information']
					sections: ['Contract']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Send Contract Pricing Info?"
			readonly: true
			offscreen: true

	claim_date_information:
		model:
			multi: false
			source: 'med_claim_dates'
			type: 'subform'
		view:
			label: 'Dates'

	claim_contract_information:
		model:
			multi: false
			source: 'med_claim_contract'
			type: 'subform'
		view:
			label: 'Contract Information'
			grid:
				add: 'flyout'
				edit: true
				fields: ['contract_type_code', 'contract_amount', 'contract_percentage', 'terms_discount_percentage']
				label: ['Type', 'Amt', 'Pct', '%']
				width: [40, 20, 20, 20]
			max_count: 1

	condition_information:
		model:
			multi: true
			source: 'med_claim_cond'
			type: 'subform'
		view:
			note: 'Max 2'
			label: 'Condition Codes'
			grid:
				add: 'inline'
				edit: true
				fields: ['condition_codes']
				label: ['Codes']
				width: [100]
			max_count: 2

	file_information_list:
		model:
			multi: true
			source: 'med_claim_file'
			type: 'subform'
		view:
			reference: 'K301'
			note: 'Max 10'
			label: 'Files'
			grid:
				add: 'inline'
				edit: true
				fields: ['file', 'comments']
				label: ['File', 'Comments']
				width: [40, 60]
			max_count: 10

model:
	prefill:
		patient:
			link:
				id: 'patient_id'
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name:['patient_id']
	sections_group: [
		'Claim Info Other':
			hide_header: true
			sections: [
				'Links':
					hide_header: true
					indent: false
					tab: 'Dates'
					tab_toggle: true
					fields: ['site_id', 'patient_id', 'payer_id', 'mm_send_contract_pricing']
				'Dates':
					hide_header: true
					indent: false
					tab: 'Dates'
					tab_toggle: true
					fields: ['claim_date_information']
				'Contract':
					hide_header: true
					indent: false
					tab: 'Contract'
					tab_toggle: true
					fields: ['claim_contract_information']
				'Conditional':
					hide_header: true
					indent: false
					tab: 'Conditional'
					tab_toggle: true
					fields: ['condition_information']
				'Files':
					hide_header: true
					indent: false
					tab: 'Files'
					fields: ['file_information_list']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	reference: '2300'
	comment: 'Claim Info Other'
	hide_header: true
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Claim Info Other'
	open: 'read'