fields:
    drug:
        view:
            label: 'Drug'
            columns: 2
            readonly: true

    bill_quantity:
        model:
            min: 1
            type: 'decimal'
            max: 9999999.999
            rounding: 0.001
            required: true
        view:
            columns: 4
            class: 'numeral'
            format: '0,0.[000000]'
            label: 'Bill Quantity (EA)'
            
    metric_unit_each:
        model:
            type: 'decimal'
            max: 9999999.999
        view:
            class: 'numeral fdb-field'
            format: '0,0.[000000]'
            label: 'Metric Units/Each'
            offscreen: true
            readonly: true

    charge_quantity_ea:
        model:
            required: false
            type: 'decimal'
            max: 9999999.999
            rounding: 0.001
        view:
            class: 'numeral'
            format: '0,0.[000000]'
            label: 'Charge Quantity (EA)'
            readonly: true
            offscreen: true

    charge_quantity:
        model:
            type: 'decimal'
            max: 9999999.999
            rounding: 0.001
        view:
            class: 'numeral claim-field'
            format: '0,0.[000000]'
            columns: 4
            label: 'Metric Quantity'
            readonly: true

    charge_unit:
        model:
            required: true
        view:
            label: 'Charge Unit'
            readonly: true
            offscreen: true
            transform: [{
                name: 'UpdateFieldNote'
                target: 'charge_quantity'
            }
            ]

    billing_unit_id:
        model:
            required: true
            source: 'list_unit'
            sourceid: 'code'
            sourcefilter:
                    code:
                        'static': ['each', 'mL', 'gram']
        view:
            control: 'select'
            label: 'Metric Unit'
            class: 'fdb-field'
            readonly: true
            offscreen: true

    expected_copay:
        model:
            type: 'decimal'
            rounding: 0.01
            max: 999999.99
        view:
            columns: -4
            label: 'Expected Copay'
            class: 'numeral money'
            format: '$0,0.00'
            readonly: true

    billed:
        model:
            type: 'decimal'
            rounding: 0.01
            max: 999999.99
        view:
            columns: 4
            label: 'Bill'
            class: 'numeral money'
            format: '$0,0.00'
            readonly: true

    expected:
        model:
            type: 'decimal'
            rounding: 0.01
            max: 999999.99
        view:
            columns: 4
            label: 'Expected'
            class: 'numeral money'
            format: '$0,0.00'
            readonly: true

    pricing_source:
        model:
            source:
                part_b_asp: "MCR Part B ASP",
                part_b_dme: "MCR Part B Durable Medical Equipment, Prosthetics, Orthotics & Supplies (DMEPOS)",
                payer_contract: 'Payer Contract',
                shared_contract: 'Shared Contract',
                list: 'List Price',
                cob_override: 'COB Override Amount'
            if:
                'payer_contract':
                    fields: ['shared_contract']
        view:
            columns: -2
            label: 'Pricing Source'
            readonly: true

    shared_contract:
        model:
            source: 'payer_contract'
            sourceid: 'id'
        view:
            columns: 2
            label: 'Shared Contract'
            readonly: true

model:
    access:
        create:     []
        create_all: []
        delete:     []
        read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
        read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
        request:    []
        update:     []
        update_all: []
        write:      []
    save: false
    name: 'Item Pricing Summary'
    sections:
        'Item Pricing Summary':
            hide_header: true
            indent: false
            fields: ['drug', 'bill_quantity', 'metric_unit_each', 'charge_quantity_ea',
            'charge_quantity', 'charge_unit', 'billing_unit_id', 'expected_copay', 'billed', 'expected',
            'pricing_source', 'shared_contract']

view:
    dimensions:
        width: '44%'
        height: '35%'
    hide_cardmenu: true
    comment: 'Item Pricing Summary'
    grid:
        fields: ['expected_copay', 'billed', 'expected']
        sort: ['-created_on']
    label: 'Item Pricing Summary'
    open: 'read'