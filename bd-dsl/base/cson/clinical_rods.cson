fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_rods.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	# Health Improvement
	read:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Read a newspaper/book?'
			validate: [
					name: 'RODSScoreValidate'
			]

	eat:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Eat?'
			validate: [
					name: 'RODSScoreValidate'
			]

	brush:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Brush your teeth?'
			validate: [
					name: 'RODSScoreValidate'
			]

	wash_upper:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Wash upper body?'
			validate: [
					name: 'RODSScoreValidate'
			]

	sit:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Sit on a toilet?'
			validate: [
					name: 'RODSScoreValidate'
			]

	make_sandwich:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Make a sandwich?'
			validate: [
					name: 'RODSScoreValidate'
			]

	dress:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Dress upper body?'
			validate: [
					name: 'RODSScoreValidate'
			]

	wash_lower:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Wash lower body?'
			validate: [
					name: 'RODSScoreValidate'
			]

	move_chair:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Move a chair?'
			validate: [
					name: 'RODSScoreValidate'
			]

	turn_key:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Turn a key in a lock?'
			validate: [
					name: 'RODSScoreValidate'
			]

	go_to_dr:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Go to the general practitioner?'
			validate: [
					name: 'RODSScoreValidate'
			]

	take_shower:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Take a shower?'
			validate: [
					name: 'RODSScoreValidate'
			]

	do_dishes:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Do the dishes?'
			validate: [
					name: 'RODSScoreValidate'
			]

	do_shopping:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Do the shopping?'
			validate: [
					name: 'RODSScoreValidate'
			]

	catch_ball:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Catch an object (e.g., ball)?'
			validate: [
					name: 'RODSScoreValidate'
			]

	bend_down:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Bend and pick up an object?'
			validate: [
					name: 'RODSScoreValidate'
			]

	walk_up_stairs:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Walk one flight of stairs?'
			validate: [
					name: 'RODSScoreValidate'
			]

	travel:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Travel by public transportation?'
			validate: [
					name: 'RODSScoreValidate'
			]

	walk:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Walk and avoid obstacles?'
			validate: [
					name: 'RODSScoreValidate'
			]

	walk_outside:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Walk outdoor < 1 km?'
			validate: [
					name: 'RODSScoreValidate'
			]

	carry_object:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Carry and put down a heavy object?'
			validate: [
					name: 'RODSScoreValidate'
			]

	dance:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Dance?'
			validate: [
					name: 'RODSScoreValidate'
			]

	stand:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Stand for hours?'
			validate: [
					name: 'RODSScoreValidate'
			]

	run:
		model:
			multi:false
			required: true

			source: ['Not possible to perform', 'Possible, but with some difficulty', 'Possible, without any difficulty']
		view:
			control: 'checkbox'
			label: 'Run?'
			validate: [
					name: 'RODSScoreValidate'
			]

	score:
		model:
			min: 0
			max: 100
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Assessment Score'
			readonly: true

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_rods:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'RODS':
			fields: ['last_assessment_date', 'assessment_date']
		'Questionnaire':
			note: 'This is a questionnaire about the relationship between daily activities and your health. Your answers give information about how your polyneuropathy affects your daily and social activities and to what degree you are able to perform your usual activities.\n
Answer each question by marking the correct box. If you are not sure about your ability to perform a task, you are still requested to mark an answer that comes as close as possible to your judged ability to complete such a task. All questions should be completed. You can only choose one answer to each question. If you situation fluctuates, your answer should be based on how you usually perform the task.\n
If you need assistance or you are using special devices to perform the activity, you are requested to mark "possible, but with some difficulty ". In case you never perform the activity due to your polyneuropathy mark "not possible".'
			fields: ['read', 'eat', 'brush', 'wash_upper', 'sit', 'make_sandwich', 'dress', 'wash_lower',
			'move_chair', 'turn_key', 'go_to_dr', 'take_shower', 'do_dishes', 'do_shopping', 'catch_ball', 'bend_down',
			'walk_up_stairs', 'travel', 'walk', 'walk_outside', 'carry_object', 'dance', 'stand', 'run', 'score']
			prefill: 'clinical_rods'

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > RODS'
	grid:
		fields: ['created_on', 'assessment_date', 'created_by']
		sort: ['-id']
	label: 'RODS'
