fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			offscreen: true
			readonly: true
	external_id:
		view:
			label: 'External Id'
			readonly: true
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['patient_id']
	sections:
		'TPN Additive Label':
			hide_header: true
			indent: false
			fields: ['patient_id']

view:
	comment: 'Manage > TPN Additive Label'
	find:
		basic: ['patient_id']
	grid:
		fields: ['patient_id']
		sort: ['created_on']
	label: 'TPN Additive Label'
