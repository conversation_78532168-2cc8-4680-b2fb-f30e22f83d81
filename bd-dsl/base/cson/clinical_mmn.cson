fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Difficulties
	diff_write:
		model:
			required: true
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable or illegible, 1 = Difficulty, 2 = Normal)'
			label: 'Writing'

	diff_utensils:
		model:
			required: true
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable or illegible, 1 = Difficulty or very clumsy, 2 = Normal)'
			label: 'Using utensils'

	diff_walk:
		model:
			required: true
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficult or requires assistance, 2 = Normal)'
			label: 'Walking 10 feet'

	diff_dress:
		model:
			required: true
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Dressing'

	diff_lift:
		model:
			required: true
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Partial, 2 = Normal)'
			label: 'Lifting arms (straight) over head'

	diff_stand:
		model:
			required: true
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficult, 2 = Normal)'
			label: 'Stand on one foot for 5 seconds (using either foot)'

	diff_climb:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficult or requires assistance, 2 = Normal)'
			label: 'Climbing 3 stairs'

	diff_bed:
		model:
			required: true
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficult or requires assistance, 2 = Normal)'
			label: 'Getting up from bed or chairs'

	diff_rhndweak:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Right hand weakness'

	diff_lhndweak:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Left hand weakness'

	diff_rlegweak:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Right leg / foot weakness'

	diff_llegweak:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Left leg / foot weakness'

	# Wellness
	distance_walk:
		model:
			max: 32
			source: ['Less than 10 feet', '11-50 feet', '51-100 feet', '101-150 feet', '> 150 feet']
		view:
			control: 'radio'
			label: 'How far can you walk comfortably'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_mmn:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Difficulties':
			note: 'On a scale of zero to two (0 - 2), with 0 meaning Extreme Difficulty and 2 meaning Normal, please rate your difficulty today with:'
			fields: ['diff_write', 'diff_utensils', 'diff_walk', 'diff_dress', 'diff_lift', 'diff_stand', 'diff_climb', 'diff_bed', 'diff_rhndweak', 'diff_lhndweak', 'diff_rlegweak', 'diff_llegweak']
		'Wellness':
			fields: [ 'distance_walk']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Multifocal Motor Neuropathy'
	label: 'Multifocal Motor Neuropathy'
