fields:
	code:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 2

	associated_therapy_id:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			multi: true
		view:
			label: 'Therapy'
			columns: 2

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 2
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['lists']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['code']
		]
	name: ['code']
	sections:
		'Dosing Type':
			fields: ['code', 'associated_therapy_id', 'allow_sync', 'active']

view:
	comment: 'Manage > Dosing Type'
	find:
		basic: ['code', 'associated_therapy_id']
	grid:
		fields: ['code', 'associated_therapy_id']
		sort: ['code']
	label: 'Dosing Type'
	open: 'read'
