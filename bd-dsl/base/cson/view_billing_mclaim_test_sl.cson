fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			columns: 2
			control: 'select'
			label: 'Patient'
			offscreen: true
			readonly: true

	date_of_service:
		model:
			required: true
			type: 'date'
		view:
			columns: -3
			label: 'Start Dt'
			note: 'Service Date Start'
			template: '{{now}}'

	date_of_service_end:
		model:
			required: false
			type: 'date'
		view:
			columns: 3
			note: 'Service Date End'
			label: 'End Dt'

	inventory_id:
		model:
			required: true
			source: 'inventory'
			query: 'select_inv_in_stock'
			querytemplate: 'inventoryTemplate'
			sourcefilter:
				active:
					'static': 'Yes'
				type:
					'static': ['Drug', 'Billable']
		view:
			columns: -3
			label: 'Inventory Item'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'description': ['name']
					'hcpc_id': ['hcpc_id']
					'hcpc_unit': ['hcpc_unit']
					'hcpc_quantity': ['hcpc_quantity']
			]

	hcpc_id:
		model:
			source: 'list_fdb_medicare_desc'
			sourceid: 'code'
			if:
				'*':
					prefill:
						procedure_identifier: 'HC'
		view:
			label: 'HCPC'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_fdb_medicare_desc/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'procedure_code': ['mcr_ref'],
			]

	procedure_identifier:
		model:
			required: true
			default: 'HC'
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SV101'
		view:
			columns: 3
			label: 'Proc Identifier'
			reference: 'SV101'
			readonly: true

	procedure_code:
		model:
			required: true
			min: 1
			max: 48
		view:
			columns: 3
			label: 'Proc Code'
			reference: 'SV101'
			readonly: true

	description:
		model:
			min: 1
			max: 50
		view:
			label: 'Description'
			reference: 'SV101-07'
			readonly: true

	service_unit_count:
		model:
			required: true
			max: 99999.999
			min: 0.001
			rounding: 0.001
			type: 'decimal'
		view:
			columns: 4
			label: 'Charge Units'
			reference: 'SV104'

	line_item_charge_amount:
		model:
			required: true
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Charge Amount'

	hcpc_unit:
		view:
			label: 'HCPC Unit'
			columns: 4
			readonly: true

	hcpc_quantity:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			note: 'Each'
			label: 'HCPC Units (EA)'
			columns: 4
			readonly: true

	dx_id_1:
		model:
			source: 'patient_diagnosis'
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
			if:
				'*':
					fields: ['dx_id_2']
		view:
			columns: 2
			label: 'Diagnosis 1'
			note: 'Add diagnosis to claim before adding to service line'
			reference: 'SV107-01'

	dx_id_2:
		model:
			source: 'patient_diagnosis'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
			if:
				'*':
					fields: ['dx_id_3']
		view:
			columns: 2
			label: 'Diagnosis 2'
			reference: 'SV107-02'

	dx_id_3:
		model:
			source: 'patient_diagnosis'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
			if:
				'*':
					fields: ['dx_id_4']
		view:
			columns: 2
			label: 'Diagnosis 3'
			reference: 'SV107-03'

	dx_id_4:
		model:
			source: 'patient_diagnosis'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			columns: 2
			label: 'Diagnosis 4'
			reference: 'SV107-04'

	modifier_1:
		model:
			min: 2
			max: 2
			if:
				'*':
					fields: ['modifier_2']
		view:
			columns: 'modifier'
			label: 'Modifier 1'
			reference: 'SV101-03'

	modifier_2:
		model:
			min: 2
			max: 2
			if:
				'*':
					fields: ['modifier_3']
		view:
			columns: 'modifier'
			label: 'Modifier 2'
			reference: 'SV101-04'

	modifier_3:
		model:
			min: 2
			max: 2
			if:
				'*':
					fields: ['modifier_4']
		view:
			columns: 'modifier'
			label: 'Modifier 3'
			reference: 'SV101-04'

	modifier_4:
		model:
			min: 2
			max: 2
		view:
			columns: 'modifier'
			label: 'Modifier 4'
			reference: 'SV101-04'

model:
	save: false
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
	reportable: false

	name: ['inventory_id']
	sections:
		'Service Line':
			fields: ['date_of_service', 'date_of_service_end', 'inventory_id', 'hcpc_id',
			'procedure_identifier', 'procedure_code', 'description',
			'service_unit_count','line_item_charge_amount', 'hcpc_unit', 'hcpc_quantity',
			'dx_id_1', 'dx_id_2', 'dx_id_3', 'dx_id_4',
			'modifier_1', 'modifier_2', 'modifier_3', 'modifier_4']

view:
	hide_cardmenu: true
	comment: 'Service Line Claims Test'
	grid:
		fields: ['date_of_service', 'inventory_id', 'hcpc_unit', 'service_unit_count', 'line_item_charge_amount', 'dx_id_1']
		sort: ['-id']
	label: 'Service Line Claims Test'
