fields:
	rx_no:
		model:
			type: 'text'
		view:
			label: 'Rx #'
			readonly: true
			offscreen: true

	external_id:
		model:
			type: 'int'
		view:
			label: 'External ID'
			readonly: true
			offscreen: true

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	is_vehicle:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Is Vehicle?'
			offscreen: true
			readonly: true

	is_container:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Is Container?'
			offscreen: true
			readonly: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
			sourcefilter:
				type:
					'static': ['Drug', 'Supply']
				active:
					'static': 'Yes'
		view:
			columns: 2
			label: 'Item'

	dispense_quantity:
		model:
			min: 1
			type: 'decimal'
			max: 9999999.999
			rounding: 1
			required: true
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Dispense Quantity'

	is_primary_ingredient:
		model:
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Primary Ingredient?'
			columns: 4

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['inventory_id', 'dispense_quantity']
	indexes:
		many: [
			['inventory_id']
		]
	sections:
		'Compound Item':
			hide_header: true
			indent: false
			fields: ['rx_no', 'inventory_id', 'dispense_quantity', 'is_primary_ingredient']

view:
	dimensions:
		width: '65%'
		height: '25%'
	hide_cardmenu: true
	comment: 'Patient > Prescription > Compound Item'
	grid:
		fields: ['is_primary_ingredient', 'inventory_id', 'dispense_quantity']
		sort: ['-created_on']
	label: 'Compound Item'
	open: 'read'
