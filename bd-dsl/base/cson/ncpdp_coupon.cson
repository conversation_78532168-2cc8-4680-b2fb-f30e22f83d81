fields:
	coupon_type:
		model:
			required: true
			source: 'list_ncpdp_ext_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '485-KE'
		view:
			columns: 1
			reference: '485-KE'
			note: '485-KE'
			label: 'Coupon Type'

	coupon_number:
		model:
			max: 15
			required: true
		view:
			columns: 2
			reference: '486-ME'
			note: '486-ME'
			label: 'Coupon Number'

	coupon_value_amount:
		model:
			required: false
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 2
			reference: '487-NE'
			note: '487-NE'
			label: 'Coupon Amount'
			class: 'numeral money'
			format: '$0,0.00'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['coupon_type', 'coupon_number', 'coupon_value_amount']
	sections:
		'Coupon':
			hide_header: true
			indent: false
			fields: ['coupon_type', 'coupon_number', 'coupon_value_amount']

view:
	dimensions:
		width: '50%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Coupon'
	grid:
		fields: ['coupon_type', 'coupon_number', 'coupon_value_amount']
		sort: ['-created_on']
	label: 'Coupon'
	open: 'read'