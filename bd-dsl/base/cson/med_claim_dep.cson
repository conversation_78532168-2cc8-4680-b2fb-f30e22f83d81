fields:

	relationship_to_subscriber_code:
		model:
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'PAT01'
		view:
			columns: 3
			label: 'Relationship to Subscriber'
			reference: 'PAT01'
			_meta:
				location: '2000C PAT'
				field: '01'
				path: 'dependent.relationshipToSubscriberCode'

	member_id:
		model:
			min: 2
			max: 80
		view:
			columns: 3
			label: 'Member ID'
			reference: 'REF02 REF01=1W'
			_meta:
				location: '2010CA REF'
				code: '1W'
				field: '02'
				path: 'dependent.memberId'

	ssn:
		model:
			max: 11
			min: 9
		view:
			format: 'ssn'
			label: 'SSN'
			reference: 'REF02 REF01=SY'
			validate: [
					name: 'SSNValidator'
			]
			_meta:
				location: '2010CA REF'
				code: 'SY'
				field: '01'
				path: 'dependent.ssn'
			offscreen: true
			readonly: true

	first_name:
		model:
			required: true
			min: 1
			max: 35
		view:
			columns: -3
			label: 'First Name'
			reference: 'NM104'
			_meta:
				location: '2010CA NM1'
				field: '04'
				path: 'dependent.firstName'

	last_name:
		model:
			min: 1
			max: 60
		view:
			columns: 3
			label: 'Last Name'
			reference: 'NM103'
			_meta:
				location: '2010CA NM1'
				field: '03'
				path: 'dependent.lastName'

	middle_name:
		model:
			min: 1
			max: 10
		view:
			columns: 3
			label: 'Middle Name'
			reference: 'NM105'
			_meta:
				location: '2010CA NM1'
				field: '05'
				path: 'dependent.middleName'

	suffix:
		model:
			min: 1
			max: 25
		view:
			label: 'Suffix'
			reference: 'NM107'
			readonly: true
			offscreen: true
			_meta:
				location: '2010CA NM1'
				field: '07'
				path: 'dependent.suffix'

	date_of_birth:
		model:
			type: 'date'
			required: true
		view:
			columns: -3
			label: 'DOB'
			note: 'Must be in MM/DD/YYYY format'
			reference: 'DMG02'
			_meta:
				location: '2010CA DMG'
				code: 'D8'
				field: '02'
				path: 'dependent.dateOfBirth'
			validate: [
					name: 'DOBValidator'
			]

	gender:
		model:
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'DMG03'
		view:
			columns: 3
			label: 'Gender'
			reference: 'DMG03'
			_meta:
				location: '2010CA DMG'
				field: '03'
				path: 'dependent.gender'

	address:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_address_dep'
		view:
			label: 'Address'

	contact_information:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_dep_cont'
		view:
			label: 'Contact Information'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['first_name', 'last_name']
	indexes:
		many: [
			['member_id']
			['relationship_to_subscriber_code']
		]
	sections_group: [
		'Dependent':
			hide_header: true
			sections: [
				'Dependent Information':
					hide_header: true
					fields: ['relationship_to_subscriber_code', 'member_id', 'ssn',
					'first_name', 'last_name', 'middle_name', 'suffix', 'date_of_birth', 'gender'
					]
				'Address':
					hide_header: true
					indent: false
					fields: ['address']
				'Contact Information':
					hide_header: true
					indent: false
					fields: ['contact_information']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	reference: '2010CA'
	comment: 'Dependent'
	grid:
		fields: ['relationship_to_subscriber_code', 'first_name', 'last_name', 'member_id']
		width: [30, 30, 20, 20]
		sort: ['-created_on']
	label: 'Dependent'
	open: 'read'