fields:
	close_no:
		model:
			type: 'text'
		view:
			label: 'Closing #'
			readonly: true
			offscreen: true

	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	ticket_no:
		model:
			type: 'text'
		view:
			label: 'Ticket #'
			note: 'Generated at the time of save'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			offscreen: true
			readonly: true
			label: 'Patient ID'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			offscreen: true
			readonly: true
			label: 'Care Plan ID'

	status:
		model:
			source:
				delivery_ticket: 'Delivery Ticket Creation' # This is a new delivery ticket, not associated with a claim
				ready_to_fill: 'Ready to Fill' # This is a delivery ticket that is ready to be filled and prescription has been verified or started from a refill request
				order_ver: 'Pending Order Verification' # This is a delivery ticket that has been filled and is await pharmacist verification
				pending_conf: 'Pending Confirmation' # This is a delivery ticket that has been filled and is await pharmacist confirmation
				ready_to_bill: 'Ready to Bill' # This is a delivery ticket that has been confirmed by the pharmacist and is ready to be billed
				confirmed: 'Confirmed' # This is a delivery ticket that has been confirmed and has no charges
				billed: 'Billed' # This is a delivery ticket that has been billed for all charges on the ticket
				voided: 'Voided' # This is a delivery ticket that has been voided
			if:
				'ready_to_fill':
					sections: ['Work Order Scans', 'Pharm Tech Verification', 'Assessment']
					fields: ['show_labels', 'subform_assessment']
				'order_ver':
					sections: ['Work Order Scans', 'Pharm Tech Verification']
					fields: ['show_verification', 'show_labels']
					readonly:
						fields: ['tech_verified', 'tech_supplies_verified']
				'pending_conf':
					sections: ['Pharm Tech Verification', 'Confirmation Details', 'Confirmed']
					fields: ['show_verification', 'show_labels', 'subform_confirmation']
					readonly:
						fields: ['tech_verified', 'tech_signature', 'pharm_signature', 'tech_supplies_verified']
				'ready_to_bill':
					sections: ['Documents', 'Pharm Tech Verification', 'Confirmed', 'Confirmed Charges', 'Assessment']
					fields: ['show_verification', 'show_labels', 'subform_assessment']
					readonly:
						fields: ['tech_verified', 'tech_signature', 'pharm_signature', 'tech_supplies_verified',  'confirmed']
				'billed':
					sections: ['Documents', 'Pharm Tech Verification', 'Confirmed', 'Confirmed Charges', 'Assessment']
					fields: ['show_verification', 'show_labels', 'subform_assessment']
					readonly:
						fields: ['tech_verified', 'tech_signature', 'pharm_signature', 'tech_supplies_verified', 'confirmed']
				'confirmed':
					sections: ['Documents', 'Pharm Tech Verification', 'Confirmed', 'Assessment']
					fields: ['show_verification', 'show_labels', 'subform_assessment']
					readonly:
						fields: ['tech_verified', 'tech_signature', 'pharm_signature', 'tech_supplies_verified', 'confirmed']
				'voided':
					sections: ['Documents', 'Pharm Tech Verification', 'Confirmed', 'Confirmed Charges', 'Assessment']
					fields: ['show_verification', 'show_labels', 'subform_assessment']
					readonly:
						fields: ['tech_verified', 'tech_signature', 'pharm_signature', 'tech_supplies_verified', 'confirmed']
		view:
			columns: 3
			label: 'Status'
			readonly: true
			transform: [
				{
					name: 'OpenDeliveryTicketTab'
				}
			]

	requires_assessment:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			label: 'Requires Assessment?'
			readonly: true
			offscreen: true

	show_verification:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['pharm_signature']
		view:
			control: 'checkbox'
			label: 'Show Pharmacist Signature?'
			readonly: true
			offscreen: true

	show_labels:
		model:
			source: ['Yes']
			if:
				'Yes':
					sections: ['Labels']
		view:
			control: 'checkbox'
			label: 'Show Labels?'
			readonly: true
			offscreen: true

	ready_to_fill:
		model:
			source: ['Yes']
		view:
			columns: 2
			label: 'Ready To Fill?'
			control: 'checkbox'
			class: 'checkbox-only'
			offscreen: true
			transform: [
				{
					name: 'CheckPrescriptionReadyToFill'
				}
			]

	missing_signed_dt:
		model:
			source: ['Yes']
		view:
			columns: 2
			label: 'Missing Signed DT?'
			control: 'checkbox'
			class: 'checkbox-only'
			offscreen: true
			readonly: true

	site_id:
		model:
			required: true
			source: 'site'
		view:
			columns: 3
			label: 'Site'
			transform: [
				{
					name: 'UpdateSiteOnTicketItems'
				}
			]

	delivery_instructions:
		model:
			prefill: ['careplan_delivery_tick']
		view:
			control: 'area'
			label: 'Delivery Instructions'
			offscreen: true
			readonly: true

	service_from:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			label: 'From'

	service_to:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			label: 'To'

	delivery_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 4
			label: 'Delivery Date'
			template: '{{next_business_day}}'
			validate: [
				{
					name: 'UpdateLastThroughDates'
				}
			]

	rx_id:
		model:
			multi: true
			source: 'careplan_order_rx'
		view:
			label: 'Care Plan Rx ID'
			readonly: true
			offscreen: true

	subform_delivery_log:
		model:
			type: 'subform'
			source: 'careplan_delivery_log'
			multi: false
		view:
			label: 'Delivery Log'

	embed_item:
		model:
			multi: true
			sourcefilter:
				ticket_no:
					'dynamic': '{ticket_no}'
		view:
			embed:
				form: 'careplan_dt_item'
				selectable: false
			grid:
				add: 'none'
				rank: 'none'
				edit: true
				hide_cardmenu: true
				deleteif:
					status: ['delivery_ticket', 'ready_to_fill', 'order_ver', 'pending_conf']
				label: ['P', 'B', 'Item', 'HCPC', 'Quantity']
				fields: ['print', 'bill', 'inventory_id', 'hcpc_code', 'dispense_quantity']
				width: [10, 10, 40, 20, 20]
			label: 'Delivery Ticket Items'
			transform: [
				{
					name: 'SumPatientResponsibility'
				}
			]

	labels:
		model:
			multi: true
			source: 'careplan_dt_label'
			type: 'subform'
		view:
			grid:
				add: 'none'
				rank: 'none'
				edit: true
				hide_cardmenu: true
				label: ['Type', 'Prescription', '# Labels']
				fields: ['label_type', 'inventory_id', 'number_of_labels']
				width: [25, 60, 15]

	embed_labels:
		model:
			sourcefilter:
				ticket_no:
					'dynamic': '{ticket_no}'
			multi: true
		view:
			embed:
				query: 'cd_labels_embed'
				selectable: false
			grid:
				label: ['Inventory Item', 'Rx No', '# Labels']
				fields: ['inventory_name', 'rx_no', 'number_of_labels']
				width: [60, 25, 15]

	biller_id:
		model:
			prefill: ['careplan_delivery_tick']
			source: 'user'
			sourcefilter:
				role:
					static: ['cm', 'cma']
		view:
			columns: 4
			label: 'Biller'

	total_pt_responsibility:
		model:
			default: 0.00
			rounding: 0.01
			type: 'decimal'
			min: 0
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Patient Responsibility'
			readonly: true

	embed_payment:
		model:
			sourcefilter:
				ticket_no:
					'dynamic': '{ticket_no}'
				void:
					'static': '!Yes'
		view:
			embed:
				form: 'billing_cash'
				selectable: false
				add_preset:
					trigger_location: 'Patient Account'
					patient_id: '{patient_id}'
					ticket_no: '{ticket_no}'
			grid:
				edit: true
				add: 'flyout'
				fields: ['amount', 'post_datetime']
				label: ['Amount $', 'Payment Date']
				width: [25, 75]
			label: 'Patient Payments'

	raw_barcode:
		model:
			type: 'text'
			max: 160
		view:
			class: 'barcode barcode-continues'
			columns: 1
			control: 'barcode'
			label: 'Scan Barcode'
			validate: [{
				name: 'BarcodeParseValidator'
				fields:
					raw: 'item_barcode'
					gtin: 'item_barcode'
			}]
			transform: [{
				name: 'ContinuousBarcode'
				type: 'barcode-continues'
			}]

	item_barcode:
		model:
			type: 'text'
			max: 160
		view:
			label: 'Item Barcode'
			note: 'GS1/GTIN, UPC, or internal'
			readonly: true
			offscreen: true

	pulled_all_items:
		model: 
			source: ['Yes']
			if:
				'Yes':
					fields: ['tech_verified']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Pulled All Items?'
			readonly: true
			offscreen: true

	tech_verified:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['tech_supplies_verified', 'tech_verified_by', 'tech_verified_datetime', 'tech_signature']
		view:
			columns: 4
			control: 'checkbox'
			label: 'Pulled Items Verified?'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						tech_verified: 'Yes'
					dest: 'tech_verified_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						tech_verified: 'Yes'
					dest: 'tech_verified_by'
				}
			]

	check_verify_warning:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			label: 'Check Verify Warning'
			readonly: true
			offscreen: true

	tech_supplies_verified:
		model:
			required: true
			source: ['Yes', 'N/A']
		view:
			control: 'checkbox'
			label: 'Supplies Verified?'
			columns: 4

	tech_verified_by:
		model:
			source: 'user'
		view:
			label: 'Validated By'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'tech_verified'
					source_fields: ['tech_verified_by', 'tech_verified_datetime']
					separator: ' @ '
				}
			]

	tech_verified_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			label: 'Verified Date/Time'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'tech_verified'
					source_fields: ['tech_verified_by', 'tech_verified_datetime']
					separator: ' @ '
				}
			]

	tech_signature:
		model:
			required: true
			type: 'json'
			if:
				'*':
					prefill:
						tech_verified: 'Yes'
		view:
			columns: 4
			note: 'I confirm the information on this dispense is accurate to ensure patient safety, medication safety, and programming safety.'
			control: 'esign'
			label: 'Pharm Tech E-Signature'

	verified:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['verified_by', 'verified_datetime']
		view:
			columns: 4
			control: 'checkbox'
			label: 'Pharmacist Verified?'
			offscreen: true
			readonly: true
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						verified: 'Yes'
					dest: 'verified_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						verified: 'Yes'
					dest: 'verified_by'
				}
			]

	verified_by:
		model:
			source: 'user'
		view:
			label: 'Validated By'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'verified'
					source_fields: ['verified_by', 'verified_datetime']
					separator: ' @ '
				}
			]

	verified_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			label: 'Verified Date/Time'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'verified'
					source_fields: ['verified_by', 'verified_datetime']
					separator: ' @ '
				}
			]

	pharm_signature:
		model:
			required: false
			type: 'json'
			if:
				'*':
					prefill:
						verified: 'Yes'
				'!':
					prefill:
						verified: ''
		view:
			columns: 4
			note: 'I confirm the information on this dispense is accurate to ensure patient safety, medication safety, and programming safety.'
			control: 'esign'
			label: 'Pharmacist E-Signature'
			validate: [
				{
					name: 'CheckPulledItemsVerified'
				}
			]

	confirmed:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['confirmed_by', 'confirmed_datetime']
				'!':
					prefill:
						confirmed_by: ''
						confirmed_datetime: ''
		view:
			columns: 4
			control: 'checkbox'
			label: 'Confirmed?'
			class: 'checkbox-only'
			validate: [
				{
					name: 'ValidateExceptionsResolved'
				},
				{
					name: 'PrefillCurrentDateTime'
					condition:
						confirmed: 'Yes'
					dest: 'confirmed_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						confirmed: 'Yes'
					dest: 'confirmed_by'
				}
			]

	confirmed_by:
		model:
			source: 'user'
		view:
			label: 'Confirmed By'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'confirmed'
					source_fields: ['confirmed_by', 'confirmed_datetime']
					separator: ' @ '
				}
			]
	
	confirmed_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			label: 'Confirmed Date/Time'
			readonly: true
			offscreen: true

	void:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['voided_datetime', 'void_reason_id']
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Void Delivery Ticket?'
			findfilter: '!Yes'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						void: 'Yes'
					dest: 'voided_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						void: 'Yes'
					dest: 'voided_by'
				}
			]

	void_reason_id:
		model:
			required: true
			source: 'list_void_reason'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			label: 'Void Reason'

	voided_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Voided Date'
			readonly: true

	voided_by:
		model:
			source: 'user'
		view:
			label: 'Voided By'
			readonly: true

	summary:
		model:
			type: 'text'
		view:
			columns: 2
			label: 'Summary'
			readonly: true
			offscreen: true

	subform_confirmation:
		model:
			type: 'subform'
			source: 'careplan_dt_confirm'
			multi: false
		view:
			label: 'Confirmation'

	charge_line:
		model:
			multi: false
			sourcefilter:
				ticket_no:
					'dynamic': '{ticket_no}'
		view:
			embed:
				query: 'dt_charge_summary'
				selectable: false
			grid:
				edit: false
				rank: 'none'
				add: 'none'
				delete: false
				label: ['Item', 'Quantity', 'Exp $', 'Cost $', 'Paid $', 'NDC', 'HCPC', 'Payer']
				fields: ['item', 'quantity', 'expected', 'cost', 'paid', 'formatted_ndc', 'hcpc_code', 'payer']
				width: [20, 10, 10, 10, 10, 10, 10, 20]
			label: 'Charge Lines'

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'careplan_delivery_tick'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Other Form'
					direct_attachment: 'Yes'
					source: 'Scanned Document'
					type_id: 'Signed Delivery Ticket'
					form_name: 'careplan_delivery_tick'
					form_code: '{code}'
					form_filter: 'careplan_delivery_tick'
					patient_id: '{patient_id}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

	assessment_form:
		view:
			label: 'Assessment Form'
			readonly: true
			offscreen: true

	subform_assessment:
		model:
			type: 'subform'
			multi: false
			source: '{assessment_form}'
			sourcefilter:
				'assessment': {}
				'ongoing': {}
		view:
			label: 'Assessment'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
			['site_id']
			['biller_id']
			['ticket_no']
			['rx_id']
		]
		unique: [
			['code']
		]
	reportable: true
	name: ['summary']

	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		careplan_delivery_tick:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections_group: [
		'Delivery Ticket':
			hide_header: true
			indent: false
			sections: [
				'Assessment':
					hide_header: true
					indent: false
					fields: ['subform_assessment']
					tab: 'Assessment'

				'Delivery Ticket Items':
					hide_header: true
					indent: false
					fields: ['embed_item', 'ready_to_fill']
					tab: 'Items'

				'Work Order Scans':
					hide_header: true
					indent: false
					tab: 'Items'
					fields: ['raw_barcode', 'item_barcode']

				'Pharm Tech Verification':
					hide_header: true
					indent: false
					tab: 'Items'
					fields: ['check_verify_warning', 'pulled_all_items', 'tech_verified', 'tech_supplies_verified', 'tech_verified_by', 'tech_verified_datetime', 'tech_signature',
					'verified', 'verified_by', 'verified_datetime', 'pharm_signature']

				'Delivery Instructions / Service Dates':
					tab: 'Shipping'
					hide_header: true
					indent: false
					fields: ['assessment_form', 'rx_id','status', 'code', 'ticket_no', 'site_id', 'delivery_instructions',
					'service_from', 'service_to', 'delivery_date', 'requires_assessment', 'show_verification', 'show_labels']

				'Shipping':
					hide_header: true
					indent: false
					tab: 'Shipping'
					fields: ['subform_delivery_log']

				'Labels':
					hide_header: true
					indent: false
					fields: ['embed_labels']
					tab: 'Labels'

				'Patient Responsibility':
					hide_header: true
					indent: false
					fields: ['biller_id', 'total_pt_responsibility']
					tab: 'Copay'

				'Payments':
					hide_header: true
					indent: false
					fields: ['embed_payment']
					tab: 'Copay'

				'Documents':
					hide_header: true
					indent: false
					fields: ['embed_document']
					tab: 'Assigned Documents'

				'Confirmation Details':
					hide_header: true
					indent: false
					fields: ['subform_confirmation']
					tab: 'Confirmation'

				'Confirmed':
					hide_header: true
					indent: false
					tab: 'Confirmation'
					fields: ['confirmed', 'confirmed_by', 'confirmed_datetime']

				'Confirmed Charges':
					hide_header: true
					indent: false
					fields: ['charge_line']
					tab: 'Confirmed Charges'

				'Void':
					hide_header: true
					indent: false
					modal: true
					fields: ['void', 'voided_datetime', 'voided_by', 'void_reason_id']

			]
	]

view:
	hide_cardmenu: true
	hide_header: true
	validate: [
		{
			name: "DateOrderValidator"
			fields: [
				"service_from",
				"delivery_date"
			]
			error: "Delivery date cannot be before service from date"
		},
		{
			name: "DateOrderValidator"
			fields: [
				"delivery_date",
				"service_to"
			]
			error: "Delivery date cannot be after service to date"
		},
		{
			name: "DateOrderValidator"
			fields: [
				"service_from",
				"service_to"
			]
			error: "Service from date cannot be after service to date"
		}
	]
	block:
		validate: [
			name: 'DeliveryTicketBlock'
		]
	comment: 'Patient > Care Plan Delivery Ticket'
	grid:
		fields: ['service_from', 'service_to', 'delivery_date', 'status', 'summary']
		width: [15, 15, 15, 15, 45]
		sort: ['-delivery_date']
	find:
		basic: ['site_id', 'rx_id', 'status', 'void']
	label: 'Patient Delivery Ticket'
	open: 'edit'
