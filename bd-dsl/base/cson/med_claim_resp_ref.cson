fields:

	time_of_response:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Response Date/Time'
			readonly: true

	claim_type:
		view:
			columns: 4
			label: 'Claim Type'
			readonly: true

	format_version:
		view:
			columns: 4
			label: 'Format Version'
			readonly: true

	rhclaim_number:
		view:
			columns: 4
			label: 'Change Healthcare Claim #'
			readonly: true

	correlation_id:
		view:
			columns: 4
			label: 'Correlation ID'
			readonly: true

	submitter_id:
		view:
			columns: 4
			label: 'Submitter ID'
			readonly: true

	payer_id:
		view:
			columns: 4
			label: 'Payer ID'
			readonly: true

	customer_claim_number:
		view:
			columns: 4
			label: 'Claim #'
			readonly: true

	patient_control_number:
		view:
			columns: 4
			label: 'MRN'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['time_of_response']
	sections:
		'Claim Reference':
			hide_header: true
			fields: ['time_of_response', 'claim_type', 'format_version',
			'rhclaim_number', 'correlation_id',  'submitter_id',
			'payer_id', 'customer_claim_number', 'patient_control_number']

view:
	dimensions:
		width: '65%'
		height: '55%'
	hide_cardmenu: true
	comment: 'Claim Reference'
	grid:
		fields: ['time_of_response', 'rhclaim_number', 'correlation_id', 'customer_claim_number', 'patient_control_number']
		sort: ['-created_on']
	label: 'Claim Reference'
	open: 'read'