#TABLE: RPEMMOE2_MONO
#could not open the form
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#pemono
	pemono:
		model:
			type: 'int'
		view:
			label: 'Patient Education Monograph Code'
			readonly: true
			columns: 3

	#PEMONOE_SN
	pemonoe_sn:
		model:
			type: 'int'
		view:
			label: 'Patient Education Text Sequence Number (Standard)'
			readonly: true
			columns: 3

	#PEMTXTEI
	pemtxtei:
		model:
			type: 'text'
		view:
			label: 'Patient Education Text Identifier (Standard)'
			readonly: true
			columns: 3

	#PEMTXTE
	pemtxte:
		model:
			type: 'text'
		view:
			label: 'Patient Education Text (Standard)'
			readonly: true
			columns: 3

	#PEMGNDR
	pemgndr:
		model:
			type: 'text'
		view:
			label: 'This column is not currently being used	'
			readonly: true
			columns: 3

	#PEMAGE
	pemage:
		model:
			type: 'text'
		view:
			label: 'This column is not currently being used	'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{pemono}"
	indexes:
		many: [
			['pemono']
		]
	sections:
		'Patient Education':
			fields: ['pemono', 'pemonoe_sn', 'pemtxtei', 'pemtxte', 'pemgndr', 'pemage']

view:
	comment: 'Manage > List FDB Patient Education Monograph Text Table'
	find:
		basic: ['pemono']
	grid:
		fields: ['pemono', 'pemonoe_sn', 'pemtxtei', 'pemtxte', 'pemgndr', 'pemage']
	label: 'List FDB Patient Education Monograph Text Table'
