fields:

	direction:
		model:
			prefill: ['parent.direction']
			source:
				'IN': 'Inbox'
				'OUT': 'Outbox'
			if:
				'IN':
					readonly:
						fields: ['source', 'effective_date', 'expiration_date',
						'adverse_event_text', 'drug_product_text',
						'reaction_text', 'severity_text']
					fields: ['adverse_event_code_id', 'drug_product_code', 'drug_product_qualifier_id',
					'reaction_code_id', 'severity_code_id']
				'OUT':
					require_fields: ['adverse_event_text', 'drug_product_text', 'source']
		view:
			label: 'Direction'
			readonly: true
			offscreen: true

	#Body.<MessageType>.AllergyOrAdverseEvent.Allergies.SourceOfInformation
	source:
		model:
			source:
				'P': 'Patient or Patient Representative reported'
				'C': 'Clinician or other healthcare provider reported'
		view:
			label: 'Source of Information'

	#Body.<MessageType>.AllergyOrAdverseEvent.Allergies.EffectiveDate.Date
	effective_date:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Effective Date'

	#Body.<MessageType>.AllergyOrAdverseEvent.Allergies.ExpirationDate.Date
	expiration_date:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Expiration Date'

	#Body.<MessageType>.AllergyOrAdverseEvent.Allergies.AdverseEvent.Code
	adverse_event_code_id:
		view:
			columns: 2
			note: 'SNOMED Code'
			label: 'Adverse Event Code'
			readonly: true

	#Body.<MessageType>.AllergyOrAdverseEvent.Allergies.AdverseEvent.Text
	adverse_event_text:
		model:
			type: 'text'
		view:
			columns: 2
			label: 'Adverse Event Description'

	#Body.<MessageType>.AllergyOrAdverseEvent.Allergies.DrugProductCoded.Code
	drug_product_code:
		model:
			type: 'text'
		view:
			columns: 2
			label: 'Allergy Product Code'
			readonly: true

	#Body.<MessageType>.AllergyOrAdverseEvent.Allergies.DrugProductCoded.Qualifier
	drug_product_qualifier_id:
		model:
			source: 'list_ss_drug_coded_qualifier'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Allergy Product Qualifier'
			readonly: true

	#Body.<MessageType>.AllergyOrAdverseEvent.Allergies.DrugProductCoded.Text
	drug_product_text:
		view:
			label: 'Allergy Product Description'

	#Body.<MessageType>.AllergyOrAdverseEvent.Allergies.ReactionCoded.Code
	reaction_code_id:
		view:
			columns: 2
			note: 'SNOMED Code'
			label: 'Reaction Code'
			readonly: true

	#Body.<MessageType>.AllergyOrAdverseEvent.Allergies.ReactionCoded.Text
	reaction_text:
		view:
			columns: 2
			label: 'Reaction Description'

	#Body.<MessageType>.AllergyOrAdverseEvent.Allergies.SeverityCoded.Code
	severity_code_id:
		model:
			source: 'list_snomed_severity'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Severity Code'
			readonly: true

	#Body.<MessageType>.AllergyOrAdverseEvent.Allergies.SeverityCoded.Text
	severity_text:
		view:
			columns: 2
			label: 'Severity Description'

model:
	name: ['adverse_event_text', 'drug_product_text', 'reaction_text', 'severity_text']
	indexes:
		many: [
			['adverse_event_code_id']
			['drug_product_qualifier_id']
			['reaction_code_id']
			['severity_code_id']
		]
	sections:
		'Allergy':
			fields: ['direction', 'source', 'effective_date', 'expiration_date',
			'adverse_event_code_id', 'adverse_event_text', 'drug_product_code',
			'drug_product_qualifier_id', 'drug_product_text', 'reaction_code_id',
			'reaction_text', 'severity_code_id', 'severity_text']
view:
	hide_cardmenu: true
	comment: 'SureScripts Allergy'
	grid:
		fields: ['source', 'effective_date', 'expiration_date', 'adverse_event_text', 'drug_product_text', 'reaction_text', 'severity_text']
		sort: ['-created_on']
	label: 'Allergy'
	open: 'read'
