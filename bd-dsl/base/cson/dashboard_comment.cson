fields:
	comment:
		model:
			type: 'text'
		view:
			control: 'area'
			label: 'Comment'
	updated_on:
		view:
			label: 'Commented On'
	updated_by:
		view:
			label: 'Comment By'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	name: ['comment']
	sections:
		'Main':
			fields: ['comment']
	indexes:
		many: [
			['created_by']
			['updated_by']
		]

view:
	comment: 'Dashboard > Comment'
	grid:
		fields: ['updated_on', 'updated_by', 'comment']
	label: 'Dashboard Alert Comments'
	open: 'edit'
