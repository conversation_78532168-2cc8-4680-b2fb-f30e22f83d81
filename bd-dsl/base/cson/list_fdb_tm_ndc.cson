fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	ndc:
		model:
			type: 'text'
		view:
			label: 'National Drug Code'
			columns: 3

	tm_name_type_id:
		model:
			type: 'int'
		view:
			label: 'TM Name Type ID'
			columns: 3

	tm_source_id:
		model:
			type: 'int'
		view:
			label: 'TM Editorial Source ID'
			columns: 3

	tm_ind:
		model:
			type: 'int'
		view:
			label: 'TM Indicator'
			columns: 3

	tm_alt_ndc_desc:
		model:
			type: 'text'
		view:
			label: 'TM Altered NDC Description'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		many: [
			['ndc']
		]
	name: ['ndc','tm_alt_ndc_desc']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['ndc','tm_name_type_id','tm_source_id','tm_ind','tm_alt_ndc_desc']

view:
	comment: 'Manage > List FDB Tall Man'
	find:
		basic: ['ndc','tm_name_type_id']
	grid:
		fields: ['ndc','tm_name_type_id']
	label: 'List FDB Tall Man'
