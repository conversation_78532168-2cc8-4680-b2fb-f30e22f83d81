fields:

	url:
		view:
			columns: 4
			label: 'URL'
			readonly: true
			_meta:
				path: 'transactions[].payer.technicalContactInformation.contactMethods[*].url'

	email:
		view:
			columns: 4
			label: 'Email'
			readonly: true
			_meta:
				path: 'transactions[].payer.technicalContactInformation.contactMethods[*].email'

	fax:
		view:
			columns: 4
			label: 'Fax'
			format: 'us_phone'
			readonly: true
			_meta:
				path: 'transactions[].payer.technicalContactInformation.contactMethods[*].fax'

	phone:
		view:
			columns: -4
			label: 'Phone'
			format: 'us_phone'
			readonly: true
			_meta:
				path: 'transactions[].payer.technicalContactInformation.contactMethods[*].phone'

	phone_extension:
		view:
			columns: 4
			label: 'Phone Extension'
			readonly: true
			_meta:
				path: 'transactions[].payer.technicalContactInformation.contactMethods[*].phoneExtension'

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: 'Payer Contact Information'
	sections:
		'Payer Contact Information':
			fields: ['url', 'email', 'fax', 'phone', 'phone_extension']

view:
	dimensions:
		width: '55%'
		height: '45%'
	hide_cardmenu: true
	comment: 'Payer Contact Information'
	grid:
		fields: ['email', 'fax', 'phone', 'phone_extension']
		label: ['Email', 'Fax', 'Phone', 'Phone Extension']
		width: [20, 20, 20, 20]
		sort: ['-created_on']
	label: 'Payer Contact Information'
	open: 'read'