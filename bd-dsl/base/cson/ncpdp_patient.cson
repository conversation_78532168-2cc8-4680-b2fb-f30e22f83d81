fields:
	transaction_code:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			prefill: ['parent.transaction_code']
			sourcefilter:
				field:
					'static': '103-A3'
			if:
				'B1': # Drug Billing
					fields: ['patient_id_qualifier', 'patient_claim_id', 'patient_phone', 'patient_email_address', 'employer_id']
					require_fields: ['patient_date_of_birth', 'patient_gender_code', 'patient_last_name', 'patient_first_name']
				'B3': # Drug Reverse and Bill
					fields: ['patient_id_qualifier', 'patient_claim_id', 'patient_phone', 'patient_email_address', 'employer_id']
					require_fields: ['patient_date_of_birth', 'patient_gender_code', 'patient_last_name', 'patient_first_name']
				'S1': # Service Billing
					fields: ['patient_id_qualifier', 'patient_claim_id', 'patient_phone', 'patient_email_address', 'employer_id']
					require_fields: ['patient_date_of_birth', 'patient_gender_code', 'patient_last_name', 'patient_first_name']
				'S3': # Service Reverse and Bill
					fields: ['patient_id_qualifier', 'patient_claim_id', 'patient_phone', 'patient_email_address', 'employer_id']
					require_fields: ['patient_date_of_birth', 'patient_gender_code', 'patient_last_name', 'patient_first_name']
		view:
			label: 'Transmission Code'
			offscreen: true
			readonly: true

	patient_id:
		model:
			type: 'int'
			required: true
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	insurance_id:
		model:
			type: 'int'
			required: false
			source: 'patient_insurance'
		view:
			label: 'Insurance'
			readonly: true
			offscreen: true

	pt_rel_code:
		view:
			label: 'Subscriber Relationship Code'
			readonly: true
			offscreen: true

	payer_type_id:
		model:
			source: 'list_payer_type'
			sourceid: 'code'
		view:
			label: 'Payer Type'
			readonly: true
			offscreen: true

	patient_id_qualifier:
		model:
			default: "EA",
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '331-CX'
		view:
			columns: 2
			reference: '331-CX'
			note: '331-CX'
			label: 'Patient ID Qualifier'
			_meta:
				copy_forward: true

	patient_claim_id:
		model:
			max: 20
		view:
			columns: 4
			reference: '332-CY'
			note: '332-CY'
			label: 'Patient ID'
			_meta:
				copy_forward: true

	patient_date_of_birth:
		model:
			type: 'date'
		view:
			columns: 4
			reference: '304-C4'
			note: '304-C4'
			label: 'Patient DOB'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	patient_gender_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '305-C5'
			if:
				'2': # Female
					fields: ['pregnancy_indicator']
				'0': # Not specified
					fields: ['pregnancy_indicator']
				'1': # Male
					prefill:
						pregnancy_indicator: ''
		view:
			columns: 4
			reference: '305-C5'
			note: '305-C5'
			label: 'Gender'

	patient_first_name:
		model:
			max: 12
		view:
			columns: -4
			reference: '310-CA'
			note: '310-CA'
			label: 'First Name'

	patient_last_name:
		model:
			max: 15
			required: true
		view:
			columns: 4
			reference: '311-CB'
			note: '311-CB'
			label: 'Last Name'

	employer_id:
		model:
			max: 15
		view:
			reference: '333-CZ'
			note: '333-CZ'
			label: 'Employer ID'
			_meta:
				copy_forward: true
			offscreen: true
			readonly: true

	patient_phone:
		model:
			max: 21
		view:
			columns: 4
			reference: '326-CQ'
			note: '326-CQ'
			format: 'us_phone'
			label: 'Phone'

	patient_email_address:
		model:
			max: 80
		view:
			columns: 2
			reference: '350-HN'
			note: '350-HN'
			label: 'Patient Email Address'
			_meta:
				copy_forward: true
			validate: [
					name: 'EmailValidator'
			]

	patient_street_address:
		model:
			max: 30
		view:
			reference: '322-CM'
			note: '322-CM'
			label: 'Address'
			class: "api_prefill"
			columns: 'addr_1'
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'patient_street_address': ['addressLabel']
					'patient_city_address': ['city']
					'patient_state': ['stateCode']
					'patient_zip': ['postalCode']
			]

	patient_city_address:
		model:
			max: 20
		view:
			columns: 'addr_city'
			reference: '323-CN'
			note: '323-CN'
			label: 'City'

	patient_state:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			reference: '324-CO'
			note: '324-CO'
			label: 'State'

	patient_zip:
		model:
			max: 10
			min: 5
		view:
			columns: 'addr_zip'
			reference: '325-CP'
			note: '325-CP'
			format: 'us_zip'
			label: 'Zip'

	place_of_service:
		model:
			source: 'list_ncpdp_ext_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '307-C7'
		view:
			columns: 2
			reference: '307-C7'
			note: '307-C7'
			label: 'Place of Service'
			_meta:
				copy_forward: true

	patient_residence:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '384-4X'
		view:
			columns: 2
			reference: '384-4X'
			note: '384-4X'
			label: 'Place of Residence Code'
			_meta:
				copy_forward: true

	pregnancy_indicator:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '335-2C'
		view:
			columns: 2
			reference: '335-2C'
			note: '335-2C'
			label: 'Pregnancy Indicator'
			_meta:
				copy_forward: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
			['insurance_id']
		]

	name: ['patient_id', 'transaction_code', 'insurance_id', 'payer_type_id']
	sections:
		'Patient':
			hide_header: true
			indent: false
			fields: ['transaction_code', 'patient_id', 'insurance_id', 'pt_rel_code',
			'payer_type_id', 'patient_id_qualifier', 'patient_claim_id', 'employer_id',
			'patient_first_name', 'patient_last_name', 'patient_date_of_birth',  'patient_gender_code', 'patient_phone']

		'Flags':
			hide_header: true
			indent: false
			fields: ['place_of_service', 'patient_residence', 'pregnancy_indicator']

		'Contact Info':
			hide_header: true
			indent: false
			fields: ['patient_email_address', 'patient_street_address',
			'patient_city_address', 'patient_state', 'patient_zip']

view:
	comment: 'Patient > NCPDP'
	grid:
		fields: ['patient_first_name', 'patient_last_name', 'patient_id_qualifier', 'patient_claim_id']
		width: [30, 30, 20, 20]
		sort: ['-created_on']
	label: 'Patient NCPDP'
	open: 'read'