fields:

	calendar_id:
		model:
			source: 'calendar'
			type: 'int'
			transform: [
					name: 'AutoNameFk'
					]
		view:
			label: 'Calendar'

	user_id:
		model:
			required: true
			source: 'user'
			sourcefilter:
				role:
					static: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		view:
			label: 'Scheduled For'
			template: '{{user.id}}'

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
			if:
				'*':
					fields: ['order_id']
		view:
			label: 'Patient'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status_id:
					'static': ['1', '5']
		view:
			columns: 2
			label: 'Referral'

	event_start_date:   #appointment_start
		model:
			required: true
			type: 'date'
		view:
			label: 'Visit Start Date'
			validate: [
				name: 'PopulateEventEndDate'
			]

	event_start_time: 
		model:
			access:
				read: ['patient']
			required: true
			type: 'time'
		view:
			label: 'Visit Start Time'
			validate: [
				name: 'PopulateEventEndTime'
			]

	event_end_time: 
		model:
			access:
				read: ['patient']
			required: true
			type: 'time'
		view:
			label: 'Visit End Time'

	override_start_date:
		model:
			required: false
			type: 'date'
		view:
			label: 'Overrride Start Date'

	override_start_time:
		model:
			type: 'time'
		view:
			label: 'Override Start Time'


	override_end_time:
		model:
			type: 'time'
		view:
			label: 'Override End Time'


#effective Flow
	effective_start_date:
		model:
			if:
				'*':
					fields: ['override_start_date','override_start_time','override_end_time']
			access:
				read: ['patient']
			required: false
			type: 'date'
		view:
			label: 'Effective Start Date'
			offscreen: true
			readonly: true

	effective_start_time:
		model:
			type: 'time'
		view:
			label: 'Effective Start Time'
			offscreen: true
			readonly: true

	effective_end_date: # effective_end_date
		model:

			required: false
			type: 'date'
		view:
			label: 'Effective End Date'
			offscreen: true
			readonly: true

	effective_end_time:
		model:
			type: 'time'
		view:
			label: 'Effective End Time'
			offscreen: true
			readonly: true

	appointment_status:
		model:
			default: 'Scheduled'
			source:['Confirmed', 'Scheduled', 'Need Orders', 'Hold/Pending', 'Missed', 'Canceled']
			if:
				'Missed':
					fields: ['missed_reason']
				'Canceled':
					fields: ['missed_reason']
		view:
			class: 'status'
			label: 'Appointment Status'


	missed_reason:
		model:
			source:['Sick', 'Insurance', 'Hospitalized', 'MD Appointment', 'No Show', 'Inactive Orders', 'Other']
			if:
				'Other':
					fields: ['missed_reason_other']
		view:
			label: 'Missed/Canceled Reason'

	missed_reason_other:
		model:
			required: true
		view:
			label: 'Missed/Canceled Reason Other'

	completed_on:
		model:
			required: true
			type: 'date'
		view:
			label: 'Completed On'

	notes:
		model:
			max: 512
		view:
			control: 'area'
			label: 'Notes'
			note: 'Not visible to patients / physicians'

	portal_display:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			default: 'Yes'
		view:
			control: 'radio'
			note: 'Shows up as "Upcoming Nursing Visit"'
			label: 'Display on Patient Portal?'

	file_attachment:
		model:
			type: 'json'
		view:
			control: 'file'
			label: 'File Attachment'
			note: 'Max 100MB. Only documents, images, and archives supported.'

# new field for event_series_id
	event_series_id:
		model:
			required: false
			source: 'schedule_event_series'
			type: 'int'
		view:
			label: 'Event Series ID'
			offscreen: true
			readonly: true

	hide_series:
		model:
			access:
				read: ['patient']
			max: 3
			min: 2
			required: false
			default: 'No'
			source: ['No', 'Yes']
			save: false
			if:
				'No':
					fields: ['series']
		view:
			control: 'radio'
			offscreen: true

	series: #series 
		model:
			max: 3
			min: 2
			required: false
			default: 'No'
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['Setup Repeat Event Appointments']
					field_label:
						event_start_date: 'Schedule Start'
				'No':
					field_label:
						event_start_date: 'Schedule Date'
		view:
			control: 'radio'
			label: 'Schedule Repeat Appointments?'
			# offscreen: true
			# readonly: true

	repeat_period: # repeat_period
		model:
			required: true
			source: ['Daily', 'Weekly', 'Monthly']
			if:
				'Daily':
					fields: ['repeat_daily']
				'Weekly':
					fields: ['repeat_weekly', 'repeat_on']
				'Monthly':
					fields: ['repeat_monthly', 'repeat_by']
		view:
			control: 'radio'
			label: 'Repeat Period'

	repeat_daily: #repeat_daily
		model:
			default: 1
			required: true
			type: 'int'
		view:
			label: 'Repeat Every'
			note: 'Days'

	repeat_weekly: #repeat_weekly
		model:
			default: 1
			required: true
			type: 'int'
		view:
			label: 'Repeat Every'
			note: 'Weeks'

	repeat_monthly:
		model:
			default: 1
			required: true
			type: 'int'
		view:
			label: 'Repeat Every'
			note: 'Months'

	repeat_on: #repeat_on
		model:
			source: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
			# multi:true

			required: true
		view:
			control: 'checkbox'
			label: 'Repeat On'

	repeat_by: #repeat_by
		model:
			required: true
			source: ['Day of the Month', 'Day of the Week']
		view:
			control: 'radio'
			label: 'Repeat on the Same'

	event_end_date:  #event_end_date
		model:
			access:
				read: ['patient']
			required: true
			type: 'date'
		view:
			label: 'End Date for Repeat Event'
			validate: [
				name: 'ScheduledEndValidate'
			]

	therapy_1:
		model:
			max: 64
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Primary Therapy'
			readonly:false

	encounter_id:
		model:
			type: 'int'
		view:
			label: 'Linked Encounter ID'
			offscreen: true
			readonly: true

	travel_time_total:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			readonly: true
			label: 'Total Travel Time (hrs)'

	time_total:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			readonly: true
			label: 'Total Visit Time (hrs)'

	dirty:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			default: 'Yes'
		view:
			control: 'radio'


model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	transform: [
		name: 'SetEffectiveDateAndTime'
		source:
			event_start_date: 'event_start_date',
			event_start_time: 'event_start_time',
			event_end_date: 'event_end_date',
			event_end_time: 'event_end_time',
			override_start_date: 'override_start_date',
			override_start_time: 'override_start_time',
			override_end_data: 'override_end_data',
			override_end_time: 'override_end_time'
		destination:
			effective_start_date: 'effective_start_date',
			effective_start_time: 'effective_start_time',
			effective_end_date: 'effective_end_date',
			effective_end_time: 'effective_end_time'
		]
	transform_post: [
		name: 'PermissionAccess'
		arguments:
			user: 'user_id'
			patient: 'patient_id'
			log: false
		name: 'RRuleTransformUpdate'
		source:
			dtstart: 'effective_start_date',
			until: 'effective_end_date',
			freq: 'repeat_period',
			interval_daily: 'repeat_daily',
			interval_weekly: 'repeat_weekly',
			interval_monthly: 'repeat_monthly',
			byweekday: 'repeat_on',
			monthweekday: 'repeat_by',
			event_start_time: 'effective_start_time',
			event_end_time: 'effective_end_time'
		destination: 'event_end_time'
		]
	indexes:
		many: [
			['event_start_date']
			['patient_id']
			['order_id']
			['user_id']
			['event_series_id']
			['event_start_date', 'event_start_time']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				patient_id: 'patient_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	name: '{effective_start_date} {effective_start_time}'
	sections_group: [
		'Schedule Event':
			sections: [
				'Schedule':
					fields: [ 'patient_id', 'order_id', 'user_id', 'calendar_id', 'therapy_1', 'event_series_id','appointment_status', 'missed_reason', 'missed_reason_other', 'notes', 'portal_display','hide_series', 'series']
				'Setup Appointment':
					fields: ['event_start_date', 'event_start_time', 'event_end_time', 'override_start_date', 'override_start_time',  'override_end_time', 'effective_start_date',  'effective_start_time', 'effective_end_date', 'effective_end_time']
				'Setup Repeat Event Appointments':
					fields: [
						'repeat_period','repeat_daily', 'repeat_weekly', 'repeat_monthly','repeat_by',
						'event_end_date', 'repeat_on',
						]
				'File Attachments':
					fields: ['file_attachment']
				]
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Schedule > Encounter'
	find:
		basic: ['appointment_status','calendar_id', 'user_id', 'patient_id','therapy_1']
	grid:
		fields: ['event_start_date', 'appointment_status', 'therapy_1', 'notes']
		sort: ['-event_start_date', '-event_start_time']
	label: 'Schedule Event'
	open: 'read'