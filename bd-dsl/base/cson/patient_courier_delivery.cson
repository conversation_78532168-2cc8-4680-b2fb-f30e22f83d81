fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	late:
		model:
			source: ['No', 'Yes']
		view:
			columns: 3
			label: 'Is this a late entry?'
			control: 'radio'

	date:
		model:
			required: true
			type: 'date'
		view:
			columns: 3
			label: 'Delivery Date'

	pharmacy_time:
		model:
			required: true
			type: 'time'
		view:
			columns: 3
			label: 'Time delivery left pharmacy'

	delivery_time:
		model:
			required: true
			type: 'time'
		view:
			columns: 3
			label: 'Time delivered to patient'

	notes:
		view:
			control: 'area'
			label: 'Notes'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id', 'order_id', 'date', 'delivery_time']
	sections:
		'Patient Courier Delivery':
			fields: ['date', 'pharmacy_time', 'late', 'delivery_time', 'notes']
	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Patient Courier Delivery"
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Patient Courier Delivery'
	grid:
		fields: ['created_on', 'created_by', 'late', 'date', 'pharmacy_time','delivery_time']
		sort: ['-created_on']
	label: 'Patient Courier Delivery'
	open: 'read'
