fields:

    prescription_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Prescription Date'
            note: 'Must be in MM/DD/YYYY format'
            reference: 'DTP03 DPT01=471'
            _meta:
                location: '2400 DPT'
                field: '03'
                code: '471'
                path: 'claimInformation.serviceLines[{idx1-50}].serviceLineDateInformation.prescriptionDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    certification_revision_or_recertification_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Certification/Revision Date'
            note: 'Must be in MM/DD/YYYY format'
            reference: 'DTP03 DPT01=607'
            _meta:
                location: '2400 DPT'
                field: '03'
                code: '607'
                path: 'claimInformation.serviceLines[{idx1-50}].serviceLineDateInformation.certificationRevisionOrRecertificationDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    begin_therapy_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Begin Therapy Date'
            note: 'Must be in MM/DD/YYYY format'
            reference: 'DTP03 DPT01=463'
            _meta:
                location: '2400 DPT'
                field: '03'
                code: '463'
                path: 'claimInformation.serviceLines[{idx1-50}].serviceLineDateInformation.beginTherapyDate'

    last_certification_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Last Certification Date'
            note: 'Must be in MM/DD/YYYY format'
            reference: 'DTP03 DPT01=461'
            _meta:
                location: '2400 DPT'
                field: '03'
                code: '461'
                path: 'claimInformation.serviceLines[{idx1-50}].serviceLineDateInformation.lastCertificationDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    treatment_or_therapy_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Treatment/Therapy Date'
            note: 'Must be in MM/DD/YYYY format'
            reference: 'DTP03 DPT01=304'
            _meta:
                location: '2400 DPT'
                field: '03'
                code: '304'
                path: 'claimInformation.serviceLines[{idx1-50}].serviceLineDateInformation.treatmentOrTherapyDate'

    hemoglobin_test_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Hemoglobin Test Date'
            note: 'Must be in MM/DD/YYYY format'
            reference: 'DTP03 DPT01=738'
            _meta:
                location: '2400 DPT'
                field: '03'
                code: '738'
                path: 'claimInformation.serviceLines[{idx1-50}].serviceLineDateInformation.hemoglobinTestDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    serum_creatine_test_date:
        model:
            type: 'date'
        view:
            columns: -3
            label: 'Serum/Creatine Test Date'
            note: 'Must be in MM/DD/YYYY format'
            reference: 'DTP03 DPT01=739'
            _meta:
                location: '2400 DPT'
                field: '03'
                code: '739'
                path: 'claimInformation.serviceLines[{idx1-50}].serviceLineDateInformation.serumCreatineTestDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    shipped_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Shipped Date'
            note: 'Must be in MM/DD/YYYY format'
            reference: 'DTP03 DPT01=011'
            _meta:
                location: '2400 DPT'
                field: '03'
                code: '011'
                path: 'claimInformation.serviceLines[{idx1-50}].serviceLineDateInformation.shippedDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    initial_treatment_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Initial Treatment Date'
            note: 'Must be in MM/DD/YYYY format'
            reference: 'DTP03 DPT01=454'
            _meta:
                location: '2400 DPT'
                field: '03'
                code: '454'
                path: 'claimInformation.serviceLines[{idx1-50}].serviceLineDateInformation.initialTreatmentDate'
model:
    access:
        create:     []
        create_all: ['admin', 'csr', 'pharm']
        delete:     ['admin', 'pharm']
        read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
        read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
        request:    []
        update:     []
        update_all: ['admin', 'csr', 'pharm']
        write:      ['admin', 'csr', 'pharm']
    name:['prescription_date']
    sections:
        'Dates':
            hide_header: true
            fields: ['prescription_date', 'certification_revision_or_recertification_date', 'begin_therapy_date',
                    'last_certification_date', 'treatment_or_therapy_date', 'hemoglobin_test_date',
                    'serum_creatine_test_date', 'shipped_date', 'initial_treatment_date']

view:
    dimensions:
        width: '85%'
        height: '65%'
    hide_cardmenu: true
    reference: '2400'
    comment: 'Service Dates'
    grid:
        fields: ['prescription_date', 'begin_therapy_date', 'treatment_or_therapy_date', 'shipped_date']
        sort: ['-created_on']
    label: 'Service Dates'
    open: 'read'