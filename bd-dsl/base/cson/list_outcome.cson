fields:

	name:
		model:
			required: true
		view:
			columns: 2
			label: 'Name'
			findunique: true

	type:
		model:
			required: true
			source: ['Pharmacy', 'Shipping', 'Nursing']
		view:
			columns: 2
			control: 'radio'
			label: 'Related Type'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 2
			control: 'radio'
			label: 'Can Sync Record'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 2
			control: 'radio'
			label: 'Active'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['lists']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['name', 'type']
		]
	name: ['name']
	sections:
		'Adverse Event/Complaint Outcome':
			fields: ['name', 'type', 'allow_sync', 'active']

view:
	comment: 'Manage > Adverse Event/Complaint Outcome'
	find:
		basic: ['name']
	grid:
		fields: ['name', 'type', 'created_by', 'created_on']
		sort: ['name']
	label: 'AE/Complaint Outcome'
	open: 'read'
