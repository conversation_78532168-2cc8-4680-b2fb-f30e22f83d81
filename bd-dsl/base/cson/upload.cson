fields:
    name:
        model:
            required: true
            search: 'A'
        view:
            label: 'Name'


    hash_key:
        model:
            required: true

    mime:
        model:
            required: true
    
    upload_type:
        model:
            required: true

model:
    access:
        create:     []
        create_all: ['admin','pharm']
        delete:     ['admin','pharm']
        read:       ['admin','pharm']
        read_all:   ['admin','pharm']
        request:    []
        review:     ['admin','pharm']
        update:     []
        update_all: ['admin','pharm']
        write:      ['admin','pharm']
    bundle: ['upload']
    name: '{name}'

    sections_group: [
       
    ]

view:
    comment: 'Upload assets'
    find:
        basic: ['name']
    label: 'Upload assets'
    open: 'edit'