fields:

	name:
		model:
			max: 128
			required: true
		view:
			label: 'Subject'
			findunique: true

	template:
		view:
			control: 'area'
			note: 'Pulls into note body'
			label: 'Template'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ['templates']
	indexes:
		unique: [
			['name']
		]
	name: ['name']
	sections:
		'Main':
			fields: ['name', 'template']

view:
	comment: 'Manage > Progress Note Template'
	find:
		basic: ['name', 'template']
	grid:
		fields: ['name', 'template']
		sort: ['name']
	label: 'Progress Note Template'
