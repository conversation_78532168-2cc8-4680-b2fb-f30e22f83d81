fields:
	name:
		model:
			max: 256
			required: true
		view:
			label: 'Component Name'

	short_name:
		model:
			max: 24
			required: true
		view:
			label: 'Short Name'

	unit:
		view:
			label: 'Unit'

	norm_low:
		model:
			type: 'decimal'
		view:
			label: 'Normal Low Value'

	norm_high:
		model:
			type: 'decimal'
		view:
			label: 'Normal High Value'

	severe_low:
		model:
			type: 'decimal'
		view:
			label: 'Severe Low Value'

	severe_high:
		model:
			type: 'decimal'
		view:
			label: 'Severe High Value'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['name']
		]
	name: ['short_name']
	sections:
		'Main':
			fields: ['name', 'short_name']
		'Measurement':
			fields: ['unit', 'norm_low', 'norm_high', 'severe_low', 'severe_high', 'allow_sync', 'active']

view:
	hide_cardmenu: true
	comment: 'Manage > Lab Component'
	find:
		basic: ['name']
	grid:
		fields: ['name', 'short_name', 'norm_low', 'norm_high', 'severe_low', 'severe_high']
		sort: ['name']
	label: 'Lab Component'
	open: 'read'
