fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			readonly: true
			offscreen: true

	order_no:
		view:
			label: 'Order #'
			readonly: true
			offscreen: true

	odi_flt:
		model:
			multi: true
			source: 'careplan_order_rx'
		view:
			readonly: true
			offscreen: true

	order_item_id:
		model:
			required: true
			source: 'careplan_order_rx'
			sourcefilter:
				order_no:
					'dynamic': '{order_no}'
				id:
					'dynamic': '{odi_flt}'
		view:
			label: 'Prescription'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/careplan_order_rx/?limit=1&filter=id:'
				fields:
					'refill_tracking': ['refill_tracking'],
					'doses_to_prep': ['doses_to_prep'],
					'doses_remaining': ['doses_remaining'],
			]

	refill_tracking:
		model:
			required: false
			source: ['Refills', 'Doses']
			if:
				'Doses':
					fields: ['doses_to_prep', 'doses_remaining']
		view:
			label: 'Refill Tracking'
			offscreen: true
			readonly: true

	doses_to_prep:
		model:
			min: 1
			required: true
			type: 'int'
		view:
			columns: 4
			label: 'Doses to Dispense'
			validate: [
				{
					name: "CompareValidator"
					fields: [
						"doses_to_prep"
						"doses_remaining"
					]
					require: "lte"
					error: "Cannot be more than Doses Remaining"
				}
			]

	doses_remaining:
		model:
			required: true
			type: 'int'
		view:
			columns: 2
			label: '# Doses Remaining'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']

	reportable: false
	name: ['created_on', 'created_by']

	sections:
		'Select Prescription':
			hide_header: true
			indent: false
			fields: ['patient_id', 'order_no', 'odi_flt', 'order_item_id', 'refill_tracking',
			'doses_to_prep', 'doses_remaining']

view:
	hide_cardmenu: true
	comment: 'Patient > Select Prescription'
	grid:
		fields: ['order_no', 'order_item_id']
	label: 'Select Prescription'
	open: 'read'
