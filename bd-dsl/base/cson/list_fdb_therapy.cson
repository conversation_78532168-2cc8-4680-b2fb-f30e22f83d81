#TABLE RETCTBL0_ETC_ID
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	etc_id:
		model:
			type: 'int'
		view:
			label: 'Identifier'
			columns: 3

	etc_name:
		model:
			type: 'text'
		view:
			label: 'Name'
			columns: 3

	etc_ultimate_child_ind:
		model:
			type: 'text'
		view:
			label: 'Ultimate Child Indicator'
			columns: 3

	etc_drug_concept_link_ind:
		model:
			type: 'text'
		view:
			label: 'Drug Concept Link Indicator'
			columns: 3

	etc_parent_etc_id:
		model:
			type: 'int'
		view:
			label: 'Parent ETC Identifier'
			columns: 3

	etc_formulary_level_ind:
		model:
			type: 'text'
		view:
			label: 'Formulary Level Indicator'
			columns: 3

	etc_presentation_seqno:
		model:
			type: 'int'
		view:
			label: 'Presentation Sequence Number'
			columns: 3

	etc_ultimate_parent_etc_id:
		model:
			type: 'int'
		view:
			label: 'Ultimate Parent ETC Identifier'
			columns: 3

	etc_hierarchy_level:
		model:
			type: 'int'
		view:
			label: 'Hierarchy Level'
			columns: 3

	etc_sort_number:
		model:
			type: 'int'
		view:
			label: 'Sort Number'
			columns: 3

	etc_retired_ind:
		model:
			type: 'text'
		view:
			label: 'Retired Indicator'
			columns: 3

	etc_retired_date:
		model:
			type: 'date'
		view:
			label: 'Retired Date'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['etc_id', 'etc_name']
	indexes:
		many: [
			['etc_id']
		]
	sections:
		'ETC Description':
			fields: ['etc_id', 'etc_name', 'etc_ultimate_child_ind', 'etc_drug_concept_link_ind',
			'etc_parent_etc_id', 'etc_formulary_level_ind', 'etc_presentation_seqno', 'etc_ultimate_parent_etc_id',
			'etc_hierarchy_level', 'etc_sort_number', 'etc_retired_ind', 'etc_retired_date']

view:
	comment: 'Manage > List FDB Therapy Classification'
	find:
		basic: ['etc_id']
	grid:
		fields: ['etc_id', 'etc_name']
	label: 'List FDB Therapy Classification'
