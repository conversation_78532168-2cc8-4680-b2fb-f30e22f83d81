fields:

	reviewed_on:
		model:
			type: 'datetime'
		view:
			label: 'Reviewed On'
			readonly: true
			offscreen: true

	reviewed_by:
		model:
			source: 'user'
		view:
			label: 'Reviewed By'
			readonly: true
			offscreen: true

	canceled_message_id:
		model:
			type: 'int'
		view:
			label: 'Canceled Message ID'
			readonly: true
			offscreen: true

	is_active_message:
		model:
			source: ['Yes']
		view:
			label: 'Is Active Message'
			readonly: true
			offscreen: true

	#Header.DigatalSignatureIndicator
	digital_signature_indicator:
		model:
			source:
				'false': 'No'
				'true': 'Yes'
		view:
			label: 'Digitaly Signed'
			readonly: true
			offscreen: true

	direction:
		model:
			source:
				'IN': 'Inbox'
				'OUT': 'Outbox'
			if:
				'IN':
					readonly:
						fields: ['priority_flag']
				'OUT':
					fields: ['message_status']
					readonly:
						fields: ['patient_id', 'physician_id', 'pharmacy_order_id']
		view:
			label: 'Direction'
			readonly: true
			offscreen: true
			findfilter: 'IN'

	status_icons:
		model:
			multi: true
			source:
				'pending': 'Pending' # TODO should be set on the client side for outbound
				'sent': 'Sent'
				'error': 'Error'
				'replied': 'Replied'
				'high_priority': 'High Priority'
				'new': 'Un-processed'
				'info': 'Informational'
				'denied': 'Denied'
				'approved': 'Approved'
				'canceled': 'Canceled'
		view:
			label: 'Status'
			readonly: true
			offscreen: true

	#Header.SentTime
	sent_dt:
		model:
			type: 'datetime'
		view:
			columns: 3
			label: 'Sent DateTime'
			readonly: true

	site_id:
		model:
			source: 'site'
		view:
			columns: 3
			label: 'Site'
			readonly: true

	#Body.<MessageType>.UrgencyIndicatorCode
	priority_flag:
		model:
			default: 'S'
			source:
				'S': 'Standard'
				'X': 'Urgent'
		view:
			columns: 3.1
			control: 'radio'
			label: 'Priority'
			note: 'The priority of the message.'

	processed:
		model:
			default: 'No'
			source: ['No', 'Yes']
		view:
			columns: 3
			label: 'Processed'
			readonly: true
			offscreen: true

	#Local datetime parsed by nes
	processed_dt:
		model:
			type: 'datetime'
		view:
			columns: 3
			label: 'Processed DateTime'
			readonly: true
			offscreen: true

	followup_count:
		model:
			default: 0
			type: 'int'
			if:
				'>0':
					sections: ['Follow-up Requests']
					fields: ['subform_followup', 'last_followup_dt']
		view:
			label: 'Follow-up Count'
			readonly: true
			offscreen: true

	last_followup_dt:
		model:
			type: 'datetime'
		view:
			label: 'Last Follow-up DateTime'
			readonly: true

	warning:
		model:
			multi: false
			source:
				'has_renewal': 'Newer renewal RX response request exists. Please use that message for any future actions.'
				'has_change': 'There is a pending change RX/renewal RX request. Please wait for response before requesting future changes.'
				'canceled': 'This prescription has been canceled and all response options have been disabled.'
		view:
			label: 'Warning Header'
			readonly: true
			offscreen: true

	show_last_action:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['last_action']
		view:
			label: 'Show Last Action'
			readonly: true
			offscreen: true

	last_action:
		view:
			label: 'Last Action'
			readonly: true

	patient_id:
		model:
			source: 'patient'
			if:
				'*':
					fields: ['pharmacy_order_id']
		view:
			columns: 3
			label: 'Matching Patient'

	physician_id:
		model:
			source: 'physician'
		view:
			columns: 3
			label: 'Matching Physician Record'

	pharmacy_order_id:
		model:
			source: 'careplan_order'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			columns: 3
			label: 'Order'

	show_options:
		model:
			source: ['Observations', 'Benefits', 'Allergies',
			'Supervisor', 'Agent', 'Follow-up Prescriber', 'DUE', 'Codified Notes', 'REMS',
			'Practice Location', 'Order Group', 'RX Group', 'Change Response']
			if:
				'Observations':
					sections: ['Observations']
					fields: ['subform_observation']
				'Benefits':
					sections: ['Benefits']
					fields: ['subform_benefit']
				'Allergies':
					sections: ['Allergies']
					fields: ['subform_allergy']
				'Supervisor':
					sections: ['Supervisor']
					fields: ['supervisor_has_license', 'supervisor_first_name', 'supervisor_last_name',
					'supervisor_spec_id', 'supervisor_phone', 'supervisor_extension', 'supervisor_fax']
				'Agent':
					fields: ['prescriber_agent_first_name', 'prescriber_agent_last_name']
				'Follow-up Prescriber':
					fields: ['fu_prescriber_last_name', 'fu_prescriber_first_name',
					'fu_prescriber_has_license', 'fu_prescriber_spec_id', 'fu_prescriber_address_1', 'fu_prescriber_address_2', 'fu_prescriber_city',
					'fu_prescriber_state', 'fu_prescriber_zip', 'fu_prescriber_phone', 'fu_prescriber_extension', 'fu_prescriber_fax']
					sections: ['Follow-up Prescriber']
				'Order Group':
					fields: ['order_group_no', 'order_group_icnt', 'order_group_tcnt', 'order_group_reason']
				'RX Group':
					fields: ['rx_group_no', 'rx_group_icnt', 'rx_group_tcnt', 'rx_group_reason']
				'DUE':
					sections: ['Drug Utilization Evaluation']
					require_fields: ['subform_due']
				'Codified Notes':
					sections: ['Codified Notes']
					fields: ['subform_cnote']
				'REMS':
					sections: ['REMS']
					fields: ['patient_rems', 'prescriber_checked_rems', 'rems_risk_category', 'rems_authorization_number']
				'Practice Location':
					sections: ['Practice Location']
					fields: ['prescriber_loc_has_license', 'prescriber_loc_name']
				'Change Response':
					sections: ['Change Response']
			multi: true
		view:
			label: 'Has Options'
			readonly: true
			offscreen: true

	#Header.To (Site NCPDPID or SPI)
	send_to:
		view:
			label: 'To'
			readonly: true
			offscreen: true

	#Header.From (Site NCPDPID or SPI)
	sent_from:
		view:
			label: 'From'
			readonly: true
			offscreen: true

	message_status:
		model:
			default: 'Pending'
			source: ['Pending', 'Sent', 'Verified', 'Error']
			if:
				'Error':
					sections: ['Error']
					fields: ['error_code_id', 'error_desc_code_id', 'error_description']
		view:
			class: 'status'
			label: 'Message Status'
			readonly: true

	error_code_id:
		view:
			columns: 2
			label: 'Error Code'
			readonly: true

	error_desc_code_id:
		view:
			columns: 2
			label: 'Error Description Code'
			readonly: true

	error_description:
		view:
			label: 'Error Description'
			readonly: true

	#Header.SenderSoftware.SenderSoftwareDeveloper
	sender_software_developer:
		view:
			label: 'Sender Software Developer'
			readonly: true
			offscreen: true

	#Header.SenderSoftware.SenderSoftwareProduct
	sender_software_product:
		view:
			label: 'Sender Software Product'
			readonly: true
			offscreen: true

	#Header.SenderSoftware.SenderSoftwareVersionRelease
	sender_software_version:
		view:
			label: 'Sender Software Version'
			readonly: true
			offscreen: true

	#Header.PrescriberOrderGroup.OrderGroupNumber
	order_group_no:
		view:
			columns: 2
			label: 'Order Group Number'
			readonly: true

	#Header.RxReferenceOrderGroup.OrderGroupNumber
	rx_group_no:
		view:
			columns: 3
			label: 'RX Group Number'
			readonly: true

	#Header.PrescriberOrderGroup.ItemCountInOrderGroup
	order_group_icnt:
		model:
			type: 'int'
		view:
			columns: 3
			label: '# Order Group Items'
			readonly: true

	#Header.RxReferenceOrderGroup.ItemCountInOrderGroup
	rx_group_icnt:
		model:
			type: 'int'
		view:
			columns: 3
			label: '# Rx Group Items'
			readonly: true

	#Header.PrescriberOrderGroup.TotalCountForOrderGroup
	order_group_tcnt:
		model:
			type: 'int'
		view:
			columns: 3
			label: 'Total # Order Group Items'
			readonly: true

	#Header.RxReferenceOrderGroup.TotalCountForOrderGroup
	rx_group_tcnt:
		model:
			type: 'int'
		view:
			columns: 3
			label: 'Total # Rx Group Items'
			readonly: true

	#Header.PrescriberOrderGroup.OrderGroupReason
	order_group_reason:
		model:
			source: 'list_ss_group_reason'
			sourceid: 'code'
		view:
			label: 'Order Group Reason'
			readonly: true

	#Header.RxReferenceOrderGroup.OrderGroupReason
	rx_group_reason:
		model:
			source: 'list_ss_group_reason'
			sourceid: 'code'
		view:
			label: 'Order Group Reason'
			readonly: true

	#Header.MessageID
	message_id:
		view:
			columns: 2
			label: 'Message ID'
			note: 'The unique number assigned to each message.'
			readonly: true

	#Header.RelatesToMessageID
	related_message_id:
		view:
			columns: 2
			label: 'Relates to Message ID'
			readonly: true

	# Used for tracking change request responses
	response_message_id:
		view:
			label: 'Related Response MessageID'
			readonly: true
			offscreen: true

	#Header.PrescriberOrderNumber
	physician_order_id:
		view:
			columns: 2
			label: 'Prescriber Order Number'
			note: 'The prescription number created by the prescribing system.'
			readonly: true

	#Header.RxReferenceNumber
	pharmacy_rx_no:
		view:
			columns: 2
			label: 'Pharmacy RX Number'
			note: 'The prescription number assigned by the pharmacy.'
			readonly: true

	#Body.<Tag>
	message_type:
		model:
			source:
				'NewRx': 'New Rx'
				'RxRenewalRequest': 'Renewal Request'
				'RxRenewalResponse': 'Renewal Response'
				'RxChangeRequest': 'Change Request'
				'CancelRx': 'Cancel Rx'
				'CancelRxResponse': 'Cancel Response'
				'RxChangeResponse': 'Change Response'
			if:
				'CancelRx':
					sections: ['Patient', 'Prescriber', 'Medication', 'Diagnosis Clinical Qualifier', 'Diagnoses']
					readonly:
						fields: ['patient_id', 'physician_id', 'pharmacy_order_id'] #Don't allow linked here, order is canceled'
				'RxRenewalRequest':
					sections: ['Medication Dispensed', 'Renewal Request','Patient', 'Prescriber', 'Medication', 'Diagnosis Clinical Qualifier', 'Diagnoses']
					fields: ['disp_description', 'disp_product_code_qualifier_id', 'disp_product_code',
					'disp_dea_schedule_id', 'disp_quantity', 'disp_quantity_qualifier_id', 'disp_quantity_uom_id', 'disp_sig', 'disp_daw',
					'disp_last_disp_date', 'request_refills', 'disp_pa_number', 'disp_pa_status_id', 'disp_drug_cvg_ids', 'disp_note']
					readonly: 
						fields: ['subform_due', 'subform_allergy', 'subform_benefit']
				'RxRenewalResponse':
					sections: ['Renewal Response','Patient', 'Prescriber', 'Medication', 'Diagnosis Clinical Qualifier', 'Diagnoses']
					fields: ['renewal_status']
					readonly:
						fields: ['subform_due', 'subform_allergy', 'subform_benefit']
				'RxChangeRequest':
					sections: ['Change Request','Patient', 'Prescriber', 'Medication', 'Diagnosis Clinical Qualifier', 'Diagnoses']
					fields: ['chg_type_id', 'chg_reason']
				'CancelRxResponse':
					sections: ['Patient', 'Prescriber', 'Medication', 'Cancel Response']
					fields: ['cancel_status']
					readonly:
						fields: ['subform_due', 'subform_allergy', 'subform_benefit', 'priority_flag']
				'*':
					sections: ['Patient', 'Prescriber', 'Medication', 'Diagnosis Clinical Qualifier', 'Diagnoses']
		view:
			label: 'Message Type'
			readonly: true

	lock_chg_type:
		model:
			source: ['Yes']
			if:
				'Yes':
					readonly:
						fields: ['chg_type_id']
		view:
			label: 'Lock in Change Type?'
			offscreen: true
			readonly: true

	# Body.RxChangeRequest.MessageRequestCode
	# Preset based on action button
	chg_type_id:
		model:
			source: 'list_ss_chg_code'
			sourceid: 'code'
			sourcefilter:
				code:
					'dynamic': '{chg_allowed_chg_id}'
			if:
				'P':
					sections: ['Benefits']
					fields: ['pa_warning']
					readonly:
						fields: ['subform_due', 'subform_allergy']
				'G':
					sections: ['Change Medication']
					fields: ['subform_chg_med']
					require_fields: ['subform_chg_med']
					readonly:
						fields: ['subform_due', 'subform_allergy', 'subform_benefit']
				'T':
					sections: ['Change Medication', 'Drug Utilization Evaluation', 'Allergies']
					fields: ['subform_chg_med', 'subform_allergy', 'subform_due', 'allergy_warning']
					require_fields: ['subform_chg_med']
				'D':
					sections: ['Change Medication', 'Drug Utilization Evaluation', 'Allergies']
					fields: ['subform_chg_med', 'subform_due', 'subform_allergy', 'allergy_warning']
					require_fields: ['subform_chg_med']
				'OS':
					sections: ['Change Medication']
					fields: ['subform_chg_med']
					require_fields: ['subform_chg_med']
					readonly:
						fields: ['subform_due', 'subform_allergy', 'subform_benefit']
				'U':
					fields: ['chg_type_sc_id']
					readonly:
						fields: ['subform_due', 'subform_allergy', 'subform_benefit']
				'S':
					note: 'Please put your clarification request in the "Reason for Request"'
					sections: ['Clarify Medication']
					fields: ['subform_chg_med']
					require_fields: ['chg_reason']
		view:
			label: 'Type of change requested'

	#TODO Load subcode information
	chg_sc_aw_chg_id:
		model:
			source: 'list_ss_chg_subcode'
			sourceid: 'code'
			multi: true
		view:
			label: 'Change Request Code'
			readonly: true
			offscreen: true

	# Body.MessageRequestSubCode
	chg_type_sc_id:
		model:
			multi: true
			required: true
			source: 'list_ss_chg_subcode'
			sourceid: 'code'
			sourcefilter:
				code:
					'dynamic': '{chg_sc_aw_chg_id}'
			validate: [
				{
					name: "CheckSubCodeCombinations"
					fields: [
						"chg_type_sc_id",
						"chg_type_id"
					]
				}
			]
		view:
			control: 'checkbox'
			class: 'checkbox checkbox-2'
			note: 'Select one or many, J & H cannot be used together'
			label: 'Information Requested from the Prescriber:'

	# Body.ChangeReasonText
	chg_reason:
		model:
			max: 260
		view:
			control: 'area'
			label: 'Reason for Request'

	allergy_warning:
		model:
			multi: true
			source: ["<span color='orange'>You can add relevant allergy/due information to the request to help the provider.</span>"]
		view:
			control: 'checkbox'
			label: 'Warning'
			class: 'list'
			readonly: true

	pa_warning:
		model:
			multi: true
			source: ["<span color='orange'>You can add relevant insurance coverage information in the Benefits section to help the provider.</span>"]
		view:
			control: 'checkbox'
			label: 'Warning'
			class: 'list'
			readonly: true

	patient_name_display:
		view:
			label: 'Patient Name'
			readonly: true
			offscreen: true

	#Body.<MessageType>.Patient.HumanPatient.Name.FirstName
	patient_first_name:
		view:
			columns: 3
			label: 'Patient First Name'
			readonly: true

	#Body.<MessageType>.Patient.HumanPatient.Name.LastName
	patient_last_name:
		view:
			columns: 3
			label: 'Patient Last Name'
			readonly: true

	#Body.<MessageType>.Patient.HumanPatient.Name.MiddleName
	patient_middle_name:
		view:
			columns: 3
			label: 'Patient Middle Name'
			readonly: true

	#Body.<MessageType>.Patient.HumanPatient.Name.DateOfBirth.Date
	patient_dob:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Patient DOB'
			readonly: true

	#Body.<MessageType>.Patient.HumanPatient.Gender
	patient_gender:
		view:
			columns: 3
			label: 'Patient Gender'
			readonly: true

	#Body.<MessageType>.Patient.HumanPatient.Identification.SocialSecurity
	patient_ssn:
		view:
			columns: 3
			label: 'Patient SSN'
			readonly: true

	#Body.<MessageType>.Patient.HumanPatient.Address.AddressLine1
	patient_home_street_1:
		view:
			columns: 'addr_1'
			label: 'Patient Address'
			note: 'The patient’s street address.'
			readonly: true

	#Body.<MessageType>.Patient.HumanPatient.Address.AddressLine2
	patient_home_street_2:
		view:
			columns: 'addr_2'
			label: 'Patient Address 2'
			note: 'The patient’s street address.'
			readonly: true

	#Body.<MessageType>.Patient.HumanPatient.Address.City
	patient_home_city:
		view:
			columns: 'addr_city'
			label: 'Patient City'
			note: 'The patient’s city.'
			readonly: true

	#Body.<MessageType>.Patient.HumanPatient.Address.StateProvince
	patient_home_state:
		model:
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'Patient State'
			note: 'The patient’s state.'
			readonly: true

	#Body.<MessageType>.Patient.HumanPatient.Address.PostalCode
	patient_home_zip:
		view:
			columns: 'addr_zip'
			label: 'Patient Zip'
			note: 'The patient’s zip code.'
			readonly: true

	#Body.<MessageType>.Patient.HumanPatient.CommunicationNumbers.PrimaryTelephone.Number
	patient_phone:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Cell Phone'
			readonly: true

	#Body.<MessageType>.Patient.HumanPatient.Identification.MedicalRecordIdentificationNumber
	patient_mrn:
		view:
			columns: 3
			note: "In Provider's MRN"
			label: 'Patient MRN'
			readonly: true

	#Body.<MessageType>.Patient.HumanPatient.Identification.MedicareNumber
	patient_medicare:
		view:
			columns: 3
			label: 'Medicare Number'
			readonly: true

	#Body.<MessageType>.Patient.HumanPatient.Identification.MedicaidNumber
	patient_medicaid:
		view:
			columns: 3
			label: 'Medicaid Number'
			readonly: true

	prescriber_has_license:
		model:
			multi: true
			source: ['NPI', 'DEA', 'REMS', 'STATECS', 'STATE', 'MEDICARE', 'MEDICAID', 'CERTIFICATE', 'NADEAN']
			if:
				'NPI':
					fields: ['prescriber_npi']
				'DEA':
					fields: ['prescriber_dea']
				'REMS':
					fields: ['prescriber_rems']
				'STATE':
					fields: ['prescriber_state_lic']
				'STATECS':
					fields: ['prescriber_state_cs_lic']
				'MEDICARE':
					fields: ['prescriber_medicare']
				'MEDICAID':
					fields: ['prescriber_medicaid']
				'CERTIFICATE':
					fields: ['prescriber_certificate_to_prescribe']
				'NADEAN':
					fields: ['prescriber_2000waiver_id']
		view:
			label: 'Has License'
			readonly: true
			offscreen: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Identification.NPI
	prescriber_npi:
		view:
			columns: 3
			label: 'NPI'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Identification.DEANumber
	prescriber_dea:
		view:
			columns: 3
			label: 'DEA'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Identification.REMSHealthcareSettingEnrollmentID
	prescriber_rems:
		view:
			columns: 3
			label: 'REMS ID'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Identification.StateControlSubstanceNumber
	prescriber_state_cs_lic:
		view:
			columns: 3
			label: 'State Controlled Substance License'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Identification.MedicareNumber
	prescriber_medicare:
		view:
			columns: 3
			label: 'Medicare Number'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Identification.MedicaidNumber
	prescriber_medicaid:
		view:
			columns: 3
			label: 'Medicaid Number'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Identification.StateLicenseNumber
	prescriber_state_lic:
		view:
			columns: 3
			label: 'State License Number'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Identification.CertificateToPrescribe
	prescriber_certificate_to_prescribe:
		view:
			columns: 3
			label: 'Certificate to Prescribe'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Identification.Data2000WaiverID
	prescriber_2000waiver_id:
		view:
			columns: 3
			label: 'NADEAN'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Specialty
	prescriber_spec_id:
		model:
			source: 'list_nucc_taxonomy'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Specialty'
			readonly: true

	prescriber_name_display:
		view:
			label: 'Prescriber Name'
			readonly: true
			offscreen: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Name.LastName
	prescriber_last_name:
		view:
			columns: 3
			label: 'Last Name'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Name.FirstName
	prescriber_first_name:
		view:
			columns: 3.1
			label: 'First Name'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Address.AddressLine1
	prescriber_address_1:
		view:
			columns: 'addr_1'
			label: 'Address'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Address.AddressLine2
	prescriber_address_2:
		view:
			columns: 'addr_2'
			label: 'Address 2'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Address.City
	prescriber_city:
		view:
			columns: 'addr_city'
			label: 'City'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Address.StateProvince
	prescriber_state:
		model:
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'State'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.Address.PostalCode
	prescriber_zip:
		view:
			columns: 'addr_zip'
			label: 'Zip'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Number
	prescriber_phone:
		model:
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Phone'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Extension
	prescriber_extension:
		view:
			columns: 3
			label: 'Extension'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.CommunicationNumbers.Fax.Number
	prescriber_fax:
		model:
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Fax'

	#Body.<MessageType>.Prescriber.NonVeterinarian.PracticeLocation.BusinessName
	prescriber_loc_name:
		view:
			label: 'Practice Location Name'
			readonly: true

	prescriber_loc_has_license:
		model:
			multi: true
			source: ['NCPDPID', 'DEA', 'REMS', 'STATECS', 'STATE', 'MEDICARE', 'MEDICAID']
			if:
				'NCPDPID':
					fields: ['prescriber_loc_ncpdp_id']
				'DEA':
					fields: ['prescriber_loc_dea']
				'REMS':
					fields: ['prescriber_loc_rems']
				'STATE':
					fields: ['prescriber_loc_state_lic']
				'STATECS':
					fields: ['prescriber_loc_state_cs_lic']
				'MEDICARE':
					fields: ['prescriber_loc_medicare']
				'MEDICAID':
					fields: ['prescriber_loc_medicaid']
		view:
			label: 'Has License'
			readonly: true
			offscreen: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.PracticeLocation.Identification.NCPDPID
	prescriber_loc_ncpdp_id:
		view:
			columns: 3
			label: 'NCPDP ID'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.PracticeLocation.Identification.DEANumber
	prescriber_loc_dea:
		view:
			columns: 3
			label: 'DEA'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.PracticeLocation.Identification.REMSHealthcareSettingEnrollmentID
	prescriber_loc_rems:
		view:
			columns: 3
			label: 'REMS ID'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.PracticeLocation.Identification.StateControlSubstanceNumber
	prescriber_loc_state_cs_lic:
		view:
			columns: 3
			label: 'State Controlled Substance License'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.PracticeLocation.Identification.MedicareNumber
	prescriber_loc_medicare:
		view:
			columns: 3
			label: 'Medicare Number'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.PracticeLocation.Identification.MedicaidNumber
	prescriber_loc_medicaid:
		view:
			columns: 3
			label: 'Medicaid Number'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.PracticeLocation.Identification.StateLicenseNumber
	prescriber_loc_state_lic:
		view:
			columns: 3
			label: 'State License Number'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.PrescriberAgent.Name.FirstName
	prescriber_agent_first_name:
		view:
			columns: 3
			label: 'Prescriber Agent First Name'
			readonly: true

	#Body.<MessageType>.Prescriber.NonVeterinarian.PrescriberAgent.Name.LastName
	prescriber_agent_last_name:
		view:
			columns: 3.1
			label: 'Prescriber Agent Last Name'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Name.LastName
	fu_prescriber_last_name:
		view:
			columns: 3
			label: 'Last Name'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Name.FirstName
	fu_prescriber_first_name:
		view:
			columns: 3.1
			label: 'First Name'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Address.AddressLine1
	fu_prescriber_address_1:
		view:
			columns: 'addr_1'
			label: 'Address'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Address.AddressLine2
	fu_prescriber_address_2:
		view:
			columns: 'addr_2'
			label: 'Address 2'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Address.City
	fu_prescriber_city:
		view:
			columns: 'addr_city'
			label: 'City'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Address.StateProvince
	fu_prescriber_state:
		model:
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'State'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Address.PostalCode
	fu_prescriber_zip:
		view:
			columns: 'addr_zip'
			label: 'Zip'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Number
	fu_prescriber_phone:
		model:
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Phone'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Extension
	fu_prescriber_extension:
		view:
			columns: 3
			label: 'Extension'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.CommunicationNumbers.Fax.Number
	fu_prescriber_fax:
		model:
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Fax'
			readonly: true

	fu_prescriber_has_license:
		model:
			multi: true
			source: ['NPI', 'DEA', 'REMS', 'STATECS', 'STATE', 'MEDICARE', 'MEDICAID', 'CERTIFICATE', 'NADEAN']
			if:
				'NPI':
					fields: ['fu_prescriber_npi']
				'DEA':
					fields: ['fu_prescriber_dea']
				'REMS':
					fields: ['fu_prescriber_rems']
				'STATE':
					fields: ['fu_prescriber_state_lic']
				'STATECS':
					fields: ['fu_prescriber_state_cs_lic']
				'MEDICARE':
					fields: ['fu_prescriber_medicare']
				'MEDICAID':
					fields: ['fu_prescriber_medicaid']
				'CERTIFICATE':
					fields: ['fu_prescriber_certificate_to_prescribe']
				'NADEAN':
					fields: ['fu_prescriber_2000waiver_id']
		view:
			label: 'Has License'
			readonly: true
			offscreen: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Identification.NPI
	fu_prescriber_npi:
		view:
			columns: 3
			label: 'NPI'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Identification.DEANumber
	fu_prescriber_dea:
		view:
			columns: 3
			label: 'DEA'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Identification.REMSHealthcareSettingEnrollmentID
	fu_prescriber_rems:
		view:
			columns: 3
			label: 'REMS ID'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Identification.StateControlSubstanceNumber
	fu_prescriber_state_cs_lic:
		view:
			columns: 3
			label: 'State Controlled Substance License'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Identification.MedicareNumber
	fu_prescriber_medicare:
		view:
			columns: 3
			label: 'Medicare Number'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Identification.MedicaidNumber
	fu_prescriber_medicaid:
		view:
			columns: 3
			label: 'Medicaid Number'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Identification.StateLicenseNumber
	fu_prescriber_state_lic:
		view:
			columns: 3
			label: 'State License Number'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Identification.CertificateToPrescribe
	fu_prescriber_certificate_to_prescribe:
		view:
			columns: 3
			label: 'Certificate to Prescribe'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Identification.Data2000WaiverID
	fu_prescriber_2000waiver_id:
		view:
			columns: 3
			label: 'NADEAN'
			readonly: true

	#Body.<MessageType>.FollowUpPrescriber.NonVeterinarian.Specialty
	fu_prescriber_spec_id:
		model:
			source: 'list_nucc_taxonomy'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Specialty'
			readonly: true

	#Body.<MessageType>.Supervisor.NonVeterinarian.Name.FirstName
	supervisor_first_name:
		view:
			columns: 3
			label: 'First Name'
			readonly: true

	#Body.<MessageType>.Supervisor.NonVeterinarian.Name.LastName
	supervisor_last_name:
		view:
			columns: 3.1
			label: 'Last Name'
			readonly: true

	supervisor_has_license:
		model:
			multi: true
			source: ['NPI', 'DEA', 'REMS', 'STATECS', 'STATE', 'MEDICARE', 'MEDICAID', 'CERTIFICATE', 'NADEAN']
			if:
				'NPI':
					fields: ['supervisor_npi']
				'DEA':
					fields: ['supervisor_dea']
				'REMS':
					fields: ['supervisor_rems']
				'STATE':
					fields: ['supervisor_state_lic']
				'STATECS':
					fields: ['supervisor_state_cs_lic']
				'MEDICARE':
					fields: ['supervisor_medicare']
				'MEDICAID':
					fields: ['supervisor_medicaid']
				'CERTIFICATE':
					fields: ['supervisor_certificate_to_prescribe']
				'NADEAN':
					fields: ['supervisor_2000waiver_id']
		view:
			label: 'Has License'
			readonly: true
			offscreen: true

	#Body.<MessageType>.Supervisor.NonVeterinarian.Identification.NPI
	supervisor_npi:
		view:
			columns: 3
			label: 'NPI'
			readonly: true

	#Body.<MessageType>.Supervisor.NonVeterinarian.Identification.DEANumber
	supervisor_dea:
		view:
			columns: 3
			label: 'DEA'
			readonly: true

	#Body.<MessageType>.Supervisor.NonVeterinarian.Identification.REMSHealthcareSettingEnrollmentID
	supervisor_rems:
		view:
			columns: 3
			label: 'REMS ID'
			readonly: true

	#Body.<MessageType>.Supervisor.NonVeterinarian.Identification.StateControlSubstanceNumber
	supervisor_state_cs_lic:
		view:
			columns: 3
			label: 'State Controlled Substance License'
			readonly: true

	#Body.<MessageType>.Supervisor.NonVeterinarian.Identification.MedicareNumber
	supervisor_medicare:
		view:
			columns: 3
			label: 'Medicare Number'
			readonly: true

	#Body.<MessageType>.Supervisor.NonVeterinarian.Identification.MedicaidNumber
	supervisor_medicaid:
		view:
			columns: 3
			label: 'Medicaid Number'
			readonly: true

	#Body.<MessageType>.Supervisor.NonVeterinarian.Identification.StateLicenseNumber
	supervisor_state_lic:
		view:
			columns: 3
			label: 'State License Number'
			readonly: true

	#Body.<MessageType>.Supervisor.NonVeterinarian.Identification.CertificateToPrescribe
	supervisor_certificate_to_prescribe:
		view:
			columns: 3
			label: 'Certificate to Prescribe'
			readonly: true

	#Body.<MessageType>.Supervisor.NonVeterinarian.Identification.Data2000WaiverID
	supervisor_2000waiver_id:
		view:
			columns: 3
			label: 'NADEAN'
			readonly: true

	#Body.<MessageType>.Supervisor.NonVeterinarian.Specialty
	supervisor_spec_id:
		model:
			source: 'list_nucc_taxonomy'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Specialty'
			readonly: true

	#Body.<MessageType>.Supervisor.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Number
	supervisor_phone:
		model:
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Phone'
			readonly: true

	#Body.<MessageType>.Supervisor.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Extension
	supervisor_extension:
		view:
			columns: 3
			label: 'Extension'
			readonly: true

	#Body.<MessageType>.Supervisor.NonVeterinarian.CommunicationNumbers.Fax.Number
	supervisor_fax:
		model:
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Fax'
			readonly: true

	#Body.<MessageType>.AllergyOrAdverseEvent.NoKnownAllergies
	nka:
		model:
			source:
				'Y': 'Yes'
		view:
			label: 'No Known Allergies?'
			control: 'checkbox'
			class: 'checkbox-only'
			readonly: true

	# Renewal Request, Medication Dispensed
	#Body.<MessageType>.MedicationDispensed.DrugDescription
	disp_description:
		model:
			required: true
			max: 105
		view:
			label: 'Drug Description'

	#Body.<MessageType>.MedicationDispensed.DrugCoded.ProductCode.Qualifier
	disp_product_code_qualifier_id:
		model:
			source: 'list_ss_product_qualifier'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Product Code Qualifier'

	#Body.<MessageType>.MedicationDispensed.DrugCoded.ProductCode.Code
	disp_product_code:
		view:
			columns: 3
			label: 'Product Code'

	#Body.<MessageType>.MedicationDispensed.DrugCoded.DEASchedule.Code
	disp_dea_schedule_id:
		model:
			source: 'list_dea_schedule'
			sourceid: 'code'
		view:
			columns: 3
			label: 'DEA Schedule'

	#Body.<MessageType>.MedicationDispensed.Quantity.Value
	disp_quantity:
		model:
			required: true
			type: 'decimal'
		view:
			columns: 3
			label: 'Quantity'

	#Body.<MessageType>.MedicationDispensed.Quantity.CodeListQualifier
	disp_quantity_qualifier_id:
		model:
			default: '87'
			source: 'list_ss_quantity_qualifier'
			sourceid: 'code'
			required: true
		view:
			columns: 3
			label: 'Quantity Qualifier'
			readonly: true

	#Body.<MessageType>.MedicationDispensed.Quantity.QuantityUnitOfMeasure.Code
	disp_quantity_uom_id:
		model:
			default: 'C64933'
			source: 'list_ncpdp_quantity_unit_msr'
			sourceid: 'code'
			required: true
		view:
			columns: 3
			label: 'Quantity UOM'
			readonly: true

	#Body.<MessageType>.MedicationDispensed.Sig.SigText
	disp_sig:
		view:
			columns: 2
			label: 'Sig'
			readonly: true

	#Body.<MessageType>.MedicationDispensed.Substitutions
	disp_daw:
		model:
			source:
				'0': 'Substitution Allowed'
				'1': 'Dispense as Written'
		view:
			columns: 2
			control: 'radio'
			label: 'Dispense as Written'
			readonly: true

	#Body.<MessageType>.MedicationDispensed.LastFillDate.Date
	disp_last_disp_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 2
			label: 'Last Dispense Date'
			readonly: true

	#Body.<MessageType>.MedicationDispensed.Note
	disp_note:
		model:
			max: 210
		view:
			columns: 2
			label: 'Note to Prescriber'

	# Renewal Request
	#Body.<MessageType>.MedicationDispensed.PharmacyRequestedRefills
	request_refills:
		model:
			prefill: ['surescripts_message.refills']
			required: false
			type: 'int'
			min: 1
			max: 99
			validate: [
				{
					name: "DEAMAXRefills"
					fields: [
						"dea_schedule_id",
						"request_refills"
					]
				}
			]
		view:
			columns: 2
			note: 'requested refills = first dispense + remaining refills. Leave blank to leave it up to the physician.'
			label: 'Request Refills'

	# TODO Auto-populate from the last dispense
	#Body.<MessageType>.MedicationDispensed.RefillsRemaining
	refills_remaining:
		model:
			required: true
			type: 'int'
			min: 0
			max: 99
		view:
			columns: 2
			label: 'Refills Remaining'

	#Body.<MessageType>.MedicationDispensed.PriorAuthorizationStatus
	disp_pa_status_id:
		model:
			source: 'list_ss_pa_status'
			sourceid: 'code'
			if:
				'A':
					fields: ['disp_pa_number']
		view:
			columns: 3
			note: 'For the refill request'
			label: 'Prior Authorization Status'

	#Body.<MessageType>.MedicationDispensed.PriorAuthorization
	disp_pa_number:
		model:
			required: true
		view:
			columns: 3
			label: 'Prior Authorization Number'

	#Body.<MessageType>.MedicationDispensed.DrugCoverageStatusCode
	#TODO Be smart and lookup on the formulary if PA is required
	disp_drug_cvg_ids:
		model:
			multi: true
			source: 'list_ss_cvg_status'
			sourceid: 'code'
			max: 5
			sourcefilter:
				'code':
					'static': ['PR', 'AP', 'PA', 'NF', 'NR', 'DC']
		view:
			columns: 3
			label: 'Drug Coverage Status'

	# Change Request

	# Only 'P' and 'U' are valid for controlled substances
	# 'P' should only be sent if a prior auth has not already been sent
	# 'MedicationRequested' should be sent unless 'P' or 'U'
	chg_allowed_chg_id:
		model:
			source: 'list_ss_chg_code'
			sourceid: 'code'
			multi: true
		view:
			label: 'Change Request Code'
			readonly: true
			offscreen: true

	#Response.<Approved/Denied/ApprovedWithChanges/Validated>
	chg_status:
		model:
			source: ['Approved', 'Denied', 'ApprovedWithChanges', 'Validated']
			if:
				'Approved':
					fields: ['chg_approved_note']
				'ApprovedWithChanges':
					fields: ['chg_approved_note']
				'Denied':
					fields: ['chg_dr_code_id', 'chg_denied_reason']
				'Validated':
					fields: ['chg_vr_cd_id', 'chg_validated_note']
		view:
			class: 'status'
			control: 'radio'
			label: 'Cancel Request Response'
			readonly: true

	#Response.Denied.ReasonCode
	chg_dr_code_id:
		model:
			multi: true
			source: 'list_ss_denial_reason'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Denied Reason Code'
			readonly: true

	#Response.Denied.DenialReason
	chg_denied_reason:
		view:
			columns: 2
			control: 'area'
			label: 'Denied Reason'
			readonly: true

	#Response.<ResponseType>.Note
	chg_approved_note:
		view:
			label: 'Note to Pharmacy'
			readonly: true

	#Response.Validated.ReasonCode
	chg_vr_cd_id:
		model:
			source: 'list_ss_chg_valid_reason'
			sourceid: 'code'
			multi: true
		view:
			columns: 2
			label: 'Validation Reason Code'
			readonly: true

	#Response.Validated.Note
	chg_validated_note:
		view:
			columns: 2
			label: 'Note to Pharmacy'
			readonly: true

	# Renewal Response
	#Body.RxRenewalResponse.Response.<ResponseCode>
	renewal_status:
		model:
			source: ['Approved', 'Denied', 'ApprovedWithChanges', 'Replace']
			if:
				'Denied':
					fields: ['renewal_denial_reason_code', 'renewal_denied_reason']
				'ApprovedWithChanges':
					fields: ['renewal_note']
				'Approved':
					fields: ['renewal_note']
				'Replace':
					fields: ['renewal_note']
		view:
			columns: 2
			control: 'radio'
			label: 'Renewal Response'
			readonly: true

	#Body.RxRenewalResponse.Response.Denied.ReasonCode
	renewal_denial_reason_code:
		model:
			multi: true
			source:
				'AA': 'Patient unknown to the Provider'
				'AB': 'Patient never under Provider care'
				'AC': 'Patient no longer under Provider care'
				'AD': 'Patient has requested refill too soon'
				'AE': 'Medication never prescribed for the patient'
				'AF': 'Patient should contact Provider first'
				'AG': 'Fill/Refill not appropriate'
				'AM': 'Patient needs appointment'
				'AP': 'Request already responded to by other means (e.g. phone or fax)'
				'BE': 'Medication denied at patient request'
				'CZ': 'Patient had allergy to requested medication'
				'DA': 'Medication has been discontinued'
		view:
			columns: 2
			label: 'Denied Reason Code'
			readonly: true

	#Body.RxRenewalResponse.Response.Denied.DenialReason
	renewal_denied_reason:
		view:
			control: 'area'
			label: 'Denied Reason'
			readonly: true

	#Body.RxRenewalResponse.Response.<ResponseType>.Note
	renewal_note:
		view:
			label: 'Note to Pharmacy'
			readonly: true

	#Cancel Request Response
	#Body.CancelRxResponse.Response.<ResponseType>
	cancel_status:
		model:
			required: true
			default: 'Approved'
			source: ['Approved', 'Denied']
			if:
				'Approved':
					fields: ['cancel_note', 'cancel_approved_requires_note']
				'Denied':
					fields: ['cancel_denied_reason_code', 'cancel_denied_reason']
		view:
			columns: 2
			control: 'radio'
			label: 'Cancel Request Response'
			readonly: true

	#Body.CancelRxResponse.Response.ReasonCode
	cancel_denied_reason_code:
		model:
			source:
				'AA': 'Patient unknown to the Provider'
				'AB': 'Patient never under Provider care'
				'AC': 'Patient no longer under Provider care'
				'AP': 'Request already responded to by other means (e.g. phone or fax)'
				'AR': 'Unable to cancel prescription; prescription was transferred to another pharmacy'
			if:
				'!':
					require_fields: ['cancel_denied_reason']
		view:
			columns: 2
			label: 'Denied Reason Code'

	#Body.CancelRxResponse.Response.DenialReason
	cancel_denied_reason:
		view:
			control: 'area'
			label: 'Denied Reason'

	cancel_approved_requires_note:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					require_fields: ['cancel_note']
		view:
			label: 'Approved Cancelation response note'
			readonly: true
			offscreen: true

	#Body.RxRenewalResponse.Response.Approved.Note
	cancel_note:
		model:
			max: 70
		view:
			note: 'Requires note if already dispensed'
			label: 'Note to Physician'

	# Medication
	#Body.<MessageType>.<MedicationType>.DrugDescription
	description:
		view:
			label: 'Drug Description'
			readonly: true

	#Body.<MessageType>.<MedicationType>.DrugCoded.ProductCode.Qualifier
	product_code_qualifier_id:
		model:
			source: 'list_ss_product_qualifier'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Product Code Qualifier'
			readonly: true

	#Body.<MessageType>.<MedicationType>.DrugCoded.ProductCode.Code
	product_code:
		view:
			columns: 3
			label: 'Product Code'
			readonly: true

	#Body.<MessageType>.<MedicationType>.DrugCoded.DrugDBCode.Qualifier
	drug_db_qualifier_id:
		model:
			source: 'list_ss_drug_db_qualifier'
			sourceid: 'code'
		view:
			label: 'Drug DB Qualifier'
			readonly: true
			offscreen: true

	#Body.<MessageType>.<MedicationType>.DrugCoded.DrugDBCode.Code
	drug_db_code:
		view:
			label: 'Drug DB Code'
			readonly: true
			offscreen: true

	#Body.<MessageType>.<MedicationType>.DrugCoded.DEASchedule.Code
	dea_schedule_id:
		model:
			source: 'list_dea_schedule'
			sourceid: 'code'
		view:
			columns: 3
			label: 'DEA Schedule'
			readonly: true

	fdb_id:
		model:
			source: 'list_fdb_ndc'
			sourceid: 'code'
		view:
			label: 'Matching FDB Item'
			readonly: true

	#Body.<MessageType>.<MedicationType>.Strength.StrengthValue
	strength:
		view:
			columns: 3
			label: 'Strength'
			readonly: true

	#Body.<MessageType>.<MedicationType>.Strength.StrengthForm.Code
	strength_form_id:
		model:
			source: 'list_ncpdp_strength_form'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Strength Form'
			readonly: true

	#Body.<MessageType>.<MedicationType>.Strength.StrengthUnitOfMeasure.Code
	strength_uom_id:
		model:
			source: 'list_ncpdp_strength_unit_msr'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Strength UOM'
			readonly: true

	#Body.<MessageType>.<MedicationType>.Quantity.Value
	quantity:
		model:
			type: 'decimal'
		view:
			columns: 3
			label: 'Quantity'
			readonly: true

	#Body.<MessageType>.<MedicationType>.Quantity.CodeListQualifier
	quantity_qualifier_id:
		model:
			source: 'list_ss_quantity_qualifier'
			sourceid: 'code'
			if:
				'CF':
					sections: ['Compound Components']
					fields: ['subform_compound', 'compound_dosage_form_id']
		view:
			columns: 3
			label: 'Quantity Qualifier'
			readonly: true

	#Body.<MessageType>.<MedicationType>.Quantity.QuantityUnitOfMeasure.Code
	quantity_uom_id:
		model:
			source: 'list_ncpdp_quantity_unit_msr'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Quantity UOM'
			readonly: true

	#Body.<MessageType>.<MedicationType>.DaysSupply
	day_supply:
		model:
			type: 'int'
		view:
			label: 'Day Supply'
			readonly: true

	#Body.<MessageType>.<MedicationType>.CompoundInformation.FinalCompoundPharmaceuticalDosageForm
	compound_dosage_form_id:
		model:
			source: 'list_ncpdp_strength_form'
			sourceid: 'code'
		view:
			label: 'Compound Final Dosage Form'
			readonly: true

	#Body.<MessageType>.<MedicationType>.WrittenDate.Date
	written_date:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Written Date'
			readonly: true

	#Body.<MessageType>.<MedicationType>.OtherMedicationDate.OtherMedicationDateQualifier = StartDate
	#Body.<MessageType>.<MedicationType>.OtherMedicationDate.OtherMedicationDate.Date
	start_date:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Start Date'
			readonly: true

	#Body.<MessageType>.<MedicationType>.OtherMedicationDate.OtherMedicationDateQualifier = ExpirationDate
	#Body.<MessageType>.<MedicationType>.OtherMedicationDate.OtherMedicationDate.Date
	expiration_date:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Expiration Date'
			readonly: true

	#Body.<MessageType>.<MedicationType>.OtherMedicationDate.OtherMedicationDateQualifier = EffectiveDate
	#Body.<MessageType>.<MedicationType>.OtherMedicationDate.OtherMedicationDate.Date
	effective_date:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Expiration Date'
			readonly: true

	#Body.<MessageType>.<MedicationType>.Substitutions
	daw:
		model:
			source:
				'0': 'Substitution Allowed'
				'1': 'Dispense as Written'
			if:
				'*':
					fields: ['daw_code_reason']
		view:
			columns: 2
			control: 'radio'
			label: 'Dispense as Written'
			readonly: true

	#Body.<MessageType>.<MedicationType>.ReasonForSubstitutionCodeUsed
	daw_code_reason:
		model:
			type: 'text'
		view:
			columns: 2
			note: 'Must be "Brand Medically Necessary" for Medicaid patients'
			label: 'DAW Code Reason'
			readonly: true

	#Body.<MessageType>.<MedicationType>.NumberOfRefills
	refills:
		model:
			type: 'int'
		view:
			label: 'Authorized Refills'
			readonly: true

	#Body.<MessageType>.<MedicationType>.PriorAuthorization
	pa_number:
		view:
			columns: 3
			label: 'Prior Authorization Number'
			readonly: true

	#Body.<MessageType>.<MedicationType>.PriorAuthorizationStatus
	pa_status_id:
		model:
			source: 'list_ss_pa_status'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Prior Authorization Status'
			readonly: true

	#Body.<MessageType>.<MedicationType>.DrugCoverageStatusCode
	drug_cvg_status_id:
		model:
			source: 'list_ss_cvg_status'
			sourceid: 'code'
			multi: true
		view:
			columns: 3
			label: 'Drug Coverage Status'
			readonly: true

	#Body.<MessageType>.<MedicationType>.DoNotFill
	do_not_fill:
		model:
			prefill: ['surescripts_message']
			source:
				'Y': 'Do Not Fill'
				'H': 'Hold'
				'E': 'Do Not Fill - Emergency Oral Called-In'
		view:
			control: 'radio'
			label: 'Do Not Fill Flag'
			readonly: true

	#Body.<MessageType>.<MedicationType>.Note
	note:
		view:
			label: 'Note to Pharmacy'
			readonly: true

	#Body.<MessageType>.<MedicationType>.Sig.SigText
	sig:
		view:
			label: 'Sig'
			readonly: true

	#Body.<MessageType>.<MedicationType>.DeliveryRequest
	delivery_request:
		model:
			source:
				'NO DELIVERY': 'No delivery required'
				'FIRST FILL DELIVERY': 'Deliver only first fill of prescription'
				'ALL FILLS DELIVERY': 'Deliver all fills of prescription'
		view:
			columns: 2
			label: 'Delivery Request'
			readonly: true

	#Body.<MessageType>.<MedicationType>.DeliveryLocation
	delivery_location:
		model:
			source:
				'HOME': "Use patient's address"
				'FACILITY': 'Use facility address'
				'CONTACT PATIENT FOR DELIVERY': 'Contact patient for delivery preference'
				'AGENCY OF SERVICE': 'Use agency of service address'
				'PROVIDER': 'Use provider address'
		view:
			columns: 2
			label: 'Delivery Location'
			readonly: true

	#Body.<MessageType>.<MedicationType>.FlavoringRequested
	flavoring_requested:
		model:
			source:
				'N': 'No'
				'Y': 'Yes'
		view:
			control: 'radio'
			label: 'Flavoring Requested?'
			note: 'See notes for flavoring preference'
			readonly: true

	#Body.<MessageType>.Patient.HumanPatient.Identification.REMSPatientID
	patient_rems:
		view:
			columns: 2
			label: 'Patient REMS ID'
			readonly: true

	#Body.<MessageType>.<MedicationType>.PrescriberCheckedREMS
	prescriber_checked_rems:
		model:
			source:
				'A': 'Prescriber has checked REMS and the prescriber’s actions have been completed'
				'B': 'Prescriber has checked REMS and the prescriber’s actions are not yet completed'
				'N': 'Prescriber has not checked REMS'
		view:
			columns: 2
			label: 'Prescriber Checked REMS?'
			readonly: true

	#Body.<MessageType>.<MedicationType>.REMSPatientRiskCategory
	rems_risk_category:
		model:
			source:
				'AFRP': 'Adult female of reproductive potential'
				'AFNRP': 'Adult female not of reproductive potential'
				'AM': 'Adult male'
				'MC': 'Male child'
				'CFRP': 'Child female of reproductive potential'
				'CFNRP': 'Child female not of reproductive potential'
		view:
			columns: 2
			label: 'REMS Risk Category'
			readonly: true

	#Body.<MessageType>.<MedicationType>.REMSAuthorizationNumber
	rems_authorization_number:
		view:
			columns: 2
			label: 'REMS Authorization Number'
			readonly: true

	#Body.<MessageType>.BenefitsCoordination.ProhibitRenewalRequest
	prohibit_renewal_request:
		model:
			source:
				'false': 'No'
				'true': 'Yes'
		view:
			control: 'radio'
			label: 'Prohibit Renewal Request?'
			readonly: true
			offscreen: true

	#Body.<MessageType>.Observation
	subform_observation:
		model:
			source: 'ss_observation'
			multi: true
			type: 'subform'
			max: 10
		view:
			label: 'Patient Observations'
			readonly: true

	#Body.<MessageType>.AllergyOrAdverseEvent
	subform_allergy:
		model:
			source: 'ss_allergy'
			multi: true
			type: 'subform'
		view:
			label: 'Patient Allergies'

	#Body.<MessageType>.BenefitsCoordination
	subform_benefit:
		model:
			source: 'ss_benefit'
			multi: true
			type: 'subform'
			max: 4
		view:
			label: 'Patient Benefits'

	#Body.<MessageType>.MedicationRequested.DrugUseEvaluation
	subform_due:
		model:
			source: 'ss_due'
			multi: true
			max: 5
			type: 'subform'
		view:
			label: 'Drug Utilization Evaluation'

	#Body.<MessageType>.<MedicationType>.CompoundInformation
	subform_compound:
		model:
			type: 'subform'
			multi: true
			source: 'ss_compound'
			max: 25
		view:
			label: 'Compound Components'
			readonly: true

	#ClinicalInformationQualifier
	clinical_info_qualifier:
		model:
			source:
				'1': 'Prescriber Supplied Diagnosis'
				'2': 'Pharmacy Inferred'
		view:
			note: 'Should always be 1 for incoming prescriptions'
			label: 'Clinical Information Qualifier'
			readonly: true

	#Body.<MessageType>.<MedicationType>.Diagnosis
	subform_diagnosis:
		model:
			type: 'subform'
			multi: true
			source: 'ss_diagnosis'
			max: 2
		view:
			label: 'Diagnosis'
			readonly: true

	#Body.<MessageType>.<MedicationType>.PatientCodifiedNote
	subform_cnote:
		model:
			type: 'subform'
			multi: true
			source: 'ss_codified_note'
			max: 10
		view:
			label: 'Codified Note'
			readonly: true

	subform_chg_med:
		model:
			type: 'subform'
			multi: true
			required: false
			source: 'ss_chg_med'
		view:
			note: 'Add one of more medications as needed to suggest alternatives'
			label: 'Change Medication'

	subform_followup:
		model:
			type: 'subform'
			multi: true
			source: 'ss_followup'
		view:
			label: 'Follow-up Prescriber'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: []
		write:      []
	prefill:
		surescripts_message:
			link:
				physician_order_id: 'physician_order_id'
			min: 'created_on'
	indexes:
		many: [
			['message_id']
			['related_message_id']
			['response_message_id']
			['patient_id']
			['site_id']
			['physician_id']
			['product_code_qualifier_id']
			['drug_db_qualifier_id']
			['dea_schedule_id']
			['strength_form_id']
			['strength_uom_id']
			['quantity_qualifier_id']
			['quantity_uom_id']
			['pa_status_id']
			['fdb_id']
			['chg_type_id']
			['chg_sc_aw_chg_id']
			['chg_type_sc_id']
			['chg_dr_code_id']
			['chg_vr_cd_id']
			['chg_status']
		]
		unique: [
			['message_id']
		]
	name: ['patient_id', 'message_id', 'direction', 'message_type']
	sections_group: [
		'Message':
			#DO NOT CHANGE, Response logic depends on this sections appearing under Message group with existing name
			sections: [
				'Error':
					fields: ['error_code_id', 'error_desc_code_id', 'error_description']
				'Header':
					fields: ['direction', 'digital_signature_indicator', 'sent_dt', 'site_id', 'priority_flag', 'processed', 'processed_dt',
					'send_to', 'sent_from', 'status_icons', 'followup_count', 'last_followup_dt', 'message_status', 'show_last_action', 'last_action',
					'show_options', 'message_id', 'related_message_id', 'order_group_no',
					'order_group_icnt', 'order_group_tcnt', 'order_group_reason',
					'rx_group_no', 'rx_group_icnt', 'rx_group_tcnt', 'rx_group_reason',
					'physician_order_id', 'pharmacy_rx_no', 'prohibit_renewal_request', 'message_type']
				'Change Request':
					fields: ['chg_allowed_chg_id', 'lock_chg_type', 'chg_type_id', 'chg_sc_aw_chg_id', 'chg_type_sc_id', 'chg_reason']
				'Links':
					fields: ['patient_id', 'physician_id', 'pharmacy_order_id']
				'Patient':
					fields: ['patient_name_display', 'patient_first_name', 'patient_last_name',
					'patient_middle_name', 'patient_dob', 'patient_gender', 'patient_ssn', 'patient_home_street_1', 'patient_home_street_2', 'patient_home_city',
					'patient_home_state', 'patient_home_zip', 'patient_phone', 'patient_mrn', 'patient_medicare', 'patient_medicaid', 'nka']
		
				'Change Medication':
					fields: ['subform_chg_med']
				'Clarify Medication':
					fields: ['subform_chg_med']
				'Change Response':
					fields: ['chg_status', 'chg_dr_code_id', 'chg_denied_reason', 'chg_approved_note', 'chg_vr_cd_id', 'chg_validated_note']
				'Cancel Response':
					fields: ['cancel_status', 'cancel_denied_reason_code', 'cancel_denied_reason', 'cancel_approved_requires_note', 'cancel_note']
				'Renewal Response':
					fields: ['renewal_status', 'renewal_denial_reason_code', 'renewal_denied_reason', 'renewal_note']

				'Prescriber':
					fields: ['prescriber_name_display', 'prescriber_has_license', 'prescriber_last_name', 'prescriber_first_name',
					'prescriber_npi', 'prescriber_dea', 'prescriber_rems',
					'prescriber_medicare', 'prescriber_medicaid', 'prescriber_state_cs_lic', 'prescriber_state_lic', 'prescriber_certificate_to_prescribe',
					'prescriber_2000waiver_id', 'prescriber_spec_id', 'prescriber_address_1', 'prescriber_address_2', 'prescriber_city',
					'prescriber_state', 'prescriber_zip', 'prescriber_phone', 'prescriber_extension', 'prescriber_fax',
					'prescriber_agent_first_name', 'prescriber_agent_last_name']
				'Practice Location':
					fields: ['prescriber_loc_has_license', 'prescriber_loc_name', 'prescriber_loc_ncpdp_id', 'prescriber_loc_dea', 'prescriber_loc_rems',
					'prescriber_loc_state_cs_lic', 'prescriber_loc_medicare', 'prescriber_loc_medicaid']
				'Supervisor':
					fields: ['supervisor_first_name', 'supervisor_last_name', 'supervisor_has_license', 'supervisor_npi', 'supervisor_dea',
					'supervisor_rems', 'supervisor_state_lic', 'supervisor_state_cs_lic', 'supervisor_medicare', 'supervisor_medicaid',
					'supervisor_certificate_to_prescribe', 'supervisor_2000waiver_id', 'supervisor_spec_id', 'supervisor_phone', 'supervisor_extension', 'supervisor_fax']
				'Follow-up Prescriber':
					fields: ['fu_prescriber_has_license', 'fu_prescriber_last_name', 'fu_prescriber_first_name',
					'fu_prescriber_npi', 'fu_prescriber_dea', 'fu_prescriber_rems', 'fu_prescriber_medicare', 'fu_prescriber_medicaid',
					'fu_prescriber_state_cs_lic', 'fu_prescriber_state_lic', 'fu_prescriber_certificate_to_prescribe', 'fu_prescriber_2000waiver_id',
					'fu_prescriber_spec_id', 'fu_prescriber_address_1', 'fu_prescriber_address_2', 'fu_prescriber_city',
					'fu_prescriber_state', 'fu_prescriber_zip', 'fu_prescriber_phone', 'fu_prescriber_extension', 'fu_prescriber_fax']
				'Medication':
					fields: ['description', 'product_code_qualifier_id', 'product_code',
					'drug_db_qualifier_id', 'drug_db_code', 'dea_schedule_id', 'fdb_id',
					'strength', 'strength_form_id', 'strength_uom_id', 'quantity', 'quantity_qualifier_id',
					'quantity_uom_id', 'day_supply', 'compound_dosage_form_id', 'written_date',
					'start_date', 'expiration_date', 'effective_date', 'daw', 'daw_code_reason', 'refills',
					'pa_number', 'pa_status_id', 'drug_cvg_status_id', 'do_not_fill',
					'note', 'sig', 'delivery_request', 'delivery_location', 'flavoring_requested']
				
				'Renewal Request':
					fields: ['request_refills', 'refills_remaining', 'disp_pa_status_id', 'disp_pa_number', 'disp_drug_cvg_ids']
				'Medication Dispensed':
					fields: ['disp_description', 'disp_product_code_qualifier_id', 'disp_product_code',
					'disp_dea_schedule_id', 'disp_quantity', 'disp_quantity_qualifier_id', 'disp_quantity_uom_id', 'disp_sig', 'disp_daw',
					'disp_last_disp_date', 'disp_note']

				'REMS':
					fields: ['patient_rems', 'rems_authorization_number', 'prescriber_checked_rems', 'rems_risk_category']
				'Diagnosis Clinical Qualifier':
					fields: ['clinical_info_qualifier']
				'Diagnoses':
					fields: ['subform_diagnosis']
				'Compound Components':
					fields: ['subform_compound']
				'Codified Notes':
					fields: ['subform_cnote']
				'Drug Utilization Evaluation':
					fields: ['subform_due']
				'Allergies':
					fields: ['subform_allergy']
				'Benefits':
					fields: ['subform_benefit']
				'Observations':
					fields: ['subform_observation']
				'Follow-up Requests':
					fields: ['subform_followup']
			]
		]

view:
	hide_cardmenu: true
	comment: 'SureScripts Message'
	find:
		basic: ['processed', 'message_status', 'message_type', 'priority_flag', 'patient_id', 'site_id', 'physician_id']
		advanced: ['message_id', 'physician_order_id', 'pharmacy_rx_no']
	grid:
		fields: ['status_icons', 'message_type', 'patient_name_display', 'prescriber_name_display', 'description', 'sig', 'pharmacy_order_id']
		width: [5, 10, 20, 20, 20, 15, 10]
		sort: ['-created_on']
	label: 'SureScripts Message'
	open: 'read'