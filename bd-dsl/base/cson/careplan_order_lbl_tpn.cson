fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			offscreen: true
			readonly: true

	external_id:
		view:
			label: 'External Id'
			readonly: true

	rx_no:
		model:
			type: 'text'
		view:
			label: 'Rx #'
			readonly: true
			offscreen: true

	inv_fltr_id:
		model:
			required: false
			source: 'inventory'
			multi: true
		view:
			label: 'Workticket Items'
			readonly: true
			offscreen: true

	line1:
		model:
			dynamic:
				source: 'list_label_header'
			max: 37
		view:
			label: '1.'
			class: 'label-line groups top'
			transform: [
					name: 'WrapLine'
					max: 37
					next_field: 'line2_pt'
			]

	line2_pt:
		model:
			required: false
			max: 23
		view:
			label: '2.'
			class: 'label-line groups middle'
			columns: 2
			transform: [
					name: 'WrapLine'
					max: 23
					next_field: 'line2_doc'
			]

	line2_doc:
		model:
			required: false
			max: 17
		view:
			label: '2. Doctor'
			class: 'no-label label-line groups middle'
			columns: 2

	line3_rx:
		view:
			label: '3.'
			class: 'label-line groups middle'
			columns: 2
			readonly: true

	line3_date:
		model:
			required: false
			type: 'date'
		view:
			label: '3. Date'
			class: 'no-label label-line groups middle'
			columns: 2

	#LABELS.line5
	line6:
		model:
			max: 45
			query: 'select_rx_items'
			dynamic:
				source: 'careplan_order_rx_cp'
			sourcefilter:
				rx_no:
					'dynamic': '{rx_no}'
		view:
			label: '6.'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line7'
			]

	#LABELS.line6
	line7:
		model:
			max: 45
			query: 'select_rx_items'
			dynamic:
				source: 'careplan_order_rx_cp'
			sourcefilter:
				rx_no:
					'dynamic': '{rx_no}'
		view:
			label: '7.'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line8'
			]

	#LABELS.line7
	line8:
		model:
			max: 45
			query: 'select_rx_items'
			dynamic:
				source: 'careplan_order_rx_cp'
			sourcefilter:
				rx_no:
					'dynamic': '{rx_no}'
		view:
			label: '8.'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line9'
			]

	#LABELS.line9
	line9:
		model:
			max: 45
		view:
			label: '9.'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line_directions'
			]

	#LABELS.directns
	line_directions:
		model:
			max: 45
			required: false
		view:
			label: '11.'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line11'
			]
			validate: [{
				name: 'ReverseParentPrefill'
				copy:
					'line_directions': 'rxform_line1'
				}
			]

	#LABELS.line11
	line11:
		model:
			max: 45
		view:
			label: '12.'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line12'
			]
			validate: [{
				name: 'ReverseParentPrefill'
				copy:
					'line11': 'rxform_line2'
				}
			]

	#LABELS.line12
	line12:
		model:
			max: 45
		view:
			label: '13.'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line13'
			]
			validate: [{
				name: 'ReverseParentPrefill'
				copy:
					'line12': 'rxform_line3'
				}
			]

	#LABELS.line13
	line13:
		model:
			max: 45
		view:
			label: '14.'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line14'
			]
			validate: [{
				name: 'ReverseParentPrefill'
				copy:
					'line13': 'rxform_line4'
				}
			]

	#LABELS.line14
	line14:
		model:
			max: 45
		view:
			label: '15.'
			class: 'label-line groups middle'
			validate: [{
				name: 'ReverseParentPrefill'
				copy:
					'line14': 'rxform_line5'
				}
			]

	#LABELS.storage
	storage_id:
		model:
			source: 'list_storage'
			sourceid: 'code'
		view:
			label: '16. Storage'
			columns: 2
			class: 'label-line groups middle'

	#LABELS.expdate
	exp_date:
		model:
			type: 'date'
		view:
			label: '17. Use By Date'
			columns: 2
			class: 'label-line groups middle'

	quantity:
		model:
			required: false
			type: 'int'
		view:
			class: 'label-line groups middle'
			label: 'Quantity'
			columns: 2

	inits:
		model:
			source: 'list_rph_labels'
			sourceid: 'code'
		view:
			columns: 2
			label: 'RPH Initials'
			class: 'label-line groups bottom'
			validate: [
				{
					name: 'RphPromptForPassword'
				}
			]

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['patient_id']
	sections:
		'TPN Label':
			hide_header: true
			indent: false
			fields: ['patient_id', 'rx_no', 'line1', 'line2_pt', 'line2_doc', 'line3_rx', 'line3_date',
			'line6', 'line7', 'line8', 'line9', 'line_directions', 'line11', 'line12',
			'line13', 'line14', 'storage_id', 'exp_date', 'quantity', 'inits']

view:
	comment: 'Manage > TPN Label'
	find:
		basic: ['patient_id']
	grid:
		fields: ['patient_id', 'line_directions', 'quantity']
		sort: ['created_on']
	label: 'TPN Label'
