fields:

	infusion_suite_id:
		model:
			type: 'int'
			source: 'infusion_suite'
		view:
			label: 'Infusion Suite'
			columns: 2
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/infusion_suite/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'organization_name': ['organization_name'],
					'phone_number': ['phone_number'],
					'phone_name': ['phone_name'],
					'npi': ['npi'],
					'address':
						'type': 'subform'
						'field': 'address'
						'fields':
							'address1': ['pr.address1']
							'address2': ['pr.address2']
							'city': ['pr.city']
							'state': ['pr.state_id']
							'postal_code': ['pr.postal_code']
			]

	organization_name:
		model:
			min: 1
			max: 50
			required: true
		view:
			columns: 2
			label: 'Service Facility Name'
			reference: 'NM103'
			_meta:
				location: '2420C NM1'
				field: '03'
				path: 'claimInformation.serviceFacilityLocation.organizationName'

	npi:
		model:
			required: true
			type: 'text'
			max: 10
		view:
			label: 'NPI'
			reference: 'NM109'
			_meta:
				location: '2420C NM1'
				field: '09'
				path: 'claimInformation.serviceFacilityLocation.npi'
			columns: -3
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid NPI, must be 10 digits'
			}]

	phone_name:
		model:
			min: 1
			max: 60
		view:
			columns: 3
			label: 'Phone Name'
			reference: 'PER02'
			_meta:
				location: '2310C PER'
				field: '02'
				path: 'claimInformation.serviceFacilityLocation.phoneName'
			note: 'Letters, numbers, comma, \', -, . only'
			validate: [
					name: 'NameValidator'
			]

	phone_number:
		model:
			required: true
			min: 1
			max: 256
		view:
			columns: 3
			reference: 'PER04'
			format: 'us_phone'
			label: "Phone #"
			_meta:
				location: '2310C PER'
				field: '04'
				path: 'claimInformation.serviceFacilityLocation.phoneNumber'

	address:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_address_fac'
		view:
			label: 'Address'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['infusion_suite_id']
		]

	name: ['organization_name']
	sections_group: [
		'Service Location':
			hide_header: true
			sections: [
				'Details':
					hide_header: true
					fields: ['infusion_suite_id', 'organization_name', 'npi',
					'phone_name', 'phone_number']
				'Address':
					indent: false
					hide_header: true
					fields: ['address']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	reference: '2310C'
	comment: 'Service Location'
	hide_header: true
	grid:
		fields: ['organization_name', 'npi']
		width: [75, 25]
		sort: ['-created_on']
	label: 'Service Location'
	open: 'read'