#TABLE: RPEIUC0_UOM_CONVERSION
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#FROM_UOM_MSTR_ID
	from_uom_mstr_id:
		model:
			type: 'int'
			required: true
		view:
			label: 'From Master Unit of Measure Identifier'
			readonly: true
			columns: 3

	#TO_UOM_MSTR_ID
	to_uom_mstr_id:
		model:
			type: 'int'
			required: true
		view:
			label: 'To Master Unit of Measure Identifier'
			readonly: true
			columns: 3

	#UOM_CONVERSION_FACTOR
	uom_conversion_factor:
		model:
			type: 'decimal'
			required: true
		view:
			label: 'Unit Conversion Factor'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{from_uom_mstr_id} -> {to_uom_mstr_id}"
	indexes:
		many: [
			['from_uom_mstr_id']
			['to_uom_mstr_id']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['from_uom_mstr_id', 'to_uom_mstr_id', 'uom_conversion_factor']

view:
	comment: 'Manage > List FDB Unit Conversion Table'
	find:
		basic: ['from_uom_mstr_id', 'to_uom_mstr_id']
	grid:
		fields: ['from_uom_mstr_id', 'to_uom_mstr_id', 'uom_conversion_factor']
	label: 'List FDB Unit Conversion Table'
