
fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Demographics
	gender:
		model:
			if:
				'Female':
					fields: ['are_preg', 'will_be_pregnant']
			prefill: ['patient']
		view:
			label: 'Sex'
			offscreen: true
			readonly: true

	will_be_pregnant:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Do you plan to get pregnant while on this medication?"
			columns: 3

	mutations:
		model:
			source: ['EGFR', 'ALK', 'BRAF V600E', 'BRAF V600K', 'CLL with 17p deletion', 'Other']
			if:
				'Other':
					fields: ['mutations_other']
			multi: true
		view:
			control: 'checkbox'
			label: 'Confirmed Mutations'
			columns: 3

	mutations_other:
		view:
			control: 'area'
			label: 'Other Mutations'
			columns: 3

	scr_value:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Recent SCr Value'
			columns: -3

	scr_date:
		model:
			type: 'date'
		view:
			label: 'Recent SCr Date'
			columns: 3

	gfr_value:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Recent GFR Value'
			columns: 3

	gfr_date:
		model:
			type: 'date'
		view:
			label: 'Recent GFR Date'
			columns: 3

	are_preg:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: ' Are you currently pregnant or have given birth in the last 6 weeks?'
			columns: 3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Chemotherapy Questionnaire':
			indent: false
			tab: 'Questionnaire'
			fields: ['gender', 'are_preg', 'will_be_pregnant', 'mutations', 'mutations_other','scr_value', 'scr_date', 'gfr_value', 'gfr_date']

view:
	comment: 'Patient > Careplan > Assessment > Chemotherapy'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Chemotherapy'
	open: 'read'
