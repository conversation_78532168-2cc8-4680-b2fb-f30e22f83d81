fields: #list_fdb_med_table

	#medid
	code:
		model:
			required: true
		view:
			readonly: true
			offscreen: true
			label: 'FDB MEDID'

	#med_medid_desc
	name:
		model:
			search: 'A'
			required: true
			max: 128
		view:
			columns: 3
			label: 'Name'
			findunique: true

	brand_name_id:
		model:
			required: true
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Short Brand Name'
			columns: 3
			readonly: true

	billing_unit_id:
		model:
			required: true
			source: 'list_unit'
			sourceid: 'code'
			sourcefilter:
					code:
						'static': ['each', 'mL', 'gram']
		view:
			control: 'select'
			label: 'Billing Unit'
			columns: 3
			readonly: true

	dea_schedule_id:
		model:
			source: 'list_dea_schedule'
			sourceid: 'code'
		view:
			label: 'DEA Schedule'
			columns: 3

	storage_id:
		model:
			source: 'list_storage'
			sourceid: 'code'
		view:
			label: 'Storage'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: '{name}'
	sections:
		'FDB Drug Brand Group':
			fields: ['code', 'name', 'brand_name_id', 'billing_unit_id','dea_schedule_id', 'storage_id']

view:
	comment: 'Manage > FDB Drug Brand Group'
	find:
		basic: ['code', 'name']
	grid:
		fields: ['code', 'name']
		sort: ['name']
	label: 'FDB Drug Brand Group'
	open: 'read'
