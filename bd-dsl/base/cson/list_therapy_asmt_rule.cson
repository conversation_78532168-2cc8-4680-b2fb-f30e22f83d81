fields:
	code:
		model:
			max: 64
			required: true
			source: 'list_therapy_ovrd_asmt'
			sourceid: 'code'
		view:
			label: 'Override Assessment Code'
			findunique: true
			columns: 3
			transform: [
					name: 'LowerCase'
			]

	dx_id:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Diagnosis'

	route_id:
		model:
			source: 'list_route'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Route'

	brand_name_id:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Drug Brand'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['workflow']
	indexes:
		unique: [
			['code']
		]
	name: ['code']
	sections:
		'Therapy Assessment Rule':
			fields: ['code', 'dx_id', 'route_id', 'brand_name_id']
view:
	comment: 'Manage > Therapy Assessment Rule'
	find:
		basic: ['code']
	grid:
		fields: ['code', 'dx_id', 'route_id', 'brand_name_id']
		sort: ['code']
	label: 'Therapy Assessment Rule'
	open: 'read'
