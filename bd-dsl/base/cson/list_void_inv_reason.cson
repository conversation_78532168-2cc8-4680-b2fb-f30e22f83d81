fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'
			findunique: true
			columns: 2

	name:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 2

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 2

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['lists']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['code']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'allow_sync', 'active']

view:
	comment: 'Manage > Void Do Not Restore Inventory Reason'
	find:
		basic: ['code', 'name']
	grid:
		fields: ['code', 'name']
		sort: ['code']
	label: 'Void Do Not Restore Inventory Reason'
	open: 'read'
