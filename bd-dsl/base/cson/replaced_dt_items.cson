fields:
	
	rx_id:
		model:
			type: 'int'
		view:
			readonly: true
			label: 'Rx Id'

	drug:
		model:
			type: 'text'
		view:
			label: 'Original Drug'
			readonly: true
	
	hcpc_code:
		model:
			type: 'text'
		view:
			label: 'HCPC Code'
			readonly: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
			query: 'rplc_item_qu'
			querytemplate: 'defaultTemplate'
			sourcefilter:
				rx_id:
					'dynamic': '{rx_id}'
		view:
			label: 'Replacement Drug'
			columns: 2
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'hcpc_code': ['hcpc_code'],
					'formatted_ndc': ['formatted_ndc'],
			]

	formatted_ndc:
		model:
			type: 'text'
		view:
			label: 'NDC Code'
			columns: 2
			readonly: true
	
	dispense_quantity:
		model:
			type: 'int'
		view:
			label: 'Quantity'

	replacement_reason_id:
		model:
			required: true
			source: 'list_dt_replacement_rsn'
		view:
			label: 'Replacement Reason'
			columns: 4

	update_rx:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Update Prescription?'
			columns: 4

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     []
		read:       ['admin','pharm','csr','cm', 'nurse']
		read_all:   ['admin','pharm','csr','cm', 'nurse']
		request:    []
		review:     ['admin','pharm','csr','cm', 'nurse']
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['drug', 'inventory_id']
		]
	name: ['inventory_id']
	sections:
		'Replaced DT Items':
			hide_header: true
			indent: false
			fields: ['rx_id', 'drug', 'inventory_id', 'hcpc_code', 'formatted_ndc', 'dispense_quantity', 'replacement_reason_id', 'update_rx']

view:
	hide_cardmenu: true
	comment: 'Replaced DT Items'
	grid:
		fields: ['drug', 'inventory_id', 'hcpc_code', 'formatted_ndc']
		sort: ['-id']
	label: 'Replaced DT Items'
	open: 'edit'