fields:

	infusion_suite_id:
		model:
			type: 'int'
			source: 'infusion_suite'
		view:
			label: 'Infusion Suite'
			columns: 3
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/infusion_suite/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'organization_name': ['organization_name'],
					'npi': ['npi'],
					'address':
						'type': 'subform'
						'field': 'address'
						'fields':
							'address1': ['pr.address1']
							'address2': ['pr.address2']
							'city': ['pr.city']
							'state_id': ['pr.state_id']
							'postal_code': ['pr.postal_code']
			]

	organization_name:
		model:
			min: 1
			max: 50
			required: true
		view:
			columns: 3
			label: 'Service Facility Name'

	npi:
		model:
			required: true
			type: 'text'
			max: 10
		view:
			label: 'NPI'
			columns: 3
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid NPI, must be 10 digits'
			}]

	address1:
		model:
			min: 1
			max: 35
		view:
			columns: 'addr_1'
			label: 'Address'

	address2:
		model:
			min: 1
			max: 35
		view:
			columns: 'addr_2'
			label: 'Address 2'

	city:
		model:
			min: 1
			max: 60
		view:
			columns: 'addr_city'
			label: 'City'

	state_id:
		model:
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'State'

	postal_code:
		model:
			max: 15
			min: 3
		view:
			columns: 'addr_zip'
			format: 'us_zip'
			label: 'Zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'postal_code'
						city:'city'
						state:'state_id'
			]

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['organization_name']
		]

	name: ['organization_name']
	sections_group: [
		'Service Location':
			hide_header: true
			sections: [
				'Details':
					hide_header: true
					fields: ['infusion_suite_id', 'organization_name', 'npi',
					'address1', 'address2', 'city', 'state_id', 'postal_code']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Service Location (1500)'
	hide_header: true
	grid:
		fields: ['organization_name', 'npi', 'city']
		width: [50, 25, 25]
		sort: ['-created_on']
	label: 'Service Location (1500)'
	open: 'read'