fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	lab_id:
		model:
			source: 'list_lab'
			required: true
		view:
			control: 'select'
			label: 'Lab Order'
			columns: 3

	lab_comments:
		view:
			control: 'area'
			label: 'Lab Draw Comments'
			columns: 3

	active:
		model:
			default: 'Yes'
			max: 32
			min: 1
			source: ['Yes']
		view:
			columns: 3
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Active?'
			findfilter: 'Yes'

	final_draw_frequency:
		view:
			label: 'Draw Frequency'
			columns: 3
			offscreen: true
			readonly: true

	draw_frequency:
		model:
			required: true
			source: ['Once', 'Monthly', 'Quarterly', 'Annually', 'Every Visit', 'Other']
			if:
				'Monthly':
					fields: ['next_draw_date']
				'Quarterly':
					fields: ['next_draw_date']
				'Annually':
					fields: ['next_draw_date']
				'Other':
					fields: ['draw_frequency_other']
		view:
			control: 'radio'
			label: 'Draw Frequency?'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'final_draw_frequency'
					overwrite: true
					condition:
						cond: 'neq'
						value: 'Other'
				}
			]
			columns: -3

	draw_frequency_other:
		model:
			required: true
		view:
			label: 'Draw Frequency Other'
			columns: 3
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'final_draw_frequency'
					overwrite: true
				}
			]

	next_draw_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Next Draw Date'
			columns: 3

	draw_history:
		model:
			multi: true
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				parent_id:
					'dynamic': '{order_id}'
				parent_form:
					'static': 'careplan_order'
				active:
					'static': 'Yes'
				loid:
					'dynamic': '{id}'
		view:
			embed:
				form: 'encounter_lab_draw'
				selectable: false
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['draw_datetime', 'lab', 'lab_other', 'amount_drawn', 'comment']
				label: ['Draw Dt/Tm', 'Lab', 'Lab Other', 'Amount (ml)', 'Comment']
				width: [30, 20, 15, 20, 15]
			label: 'Draw History'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
	bundle: ['patient', 'order']
	name: ['lab_id']
	indexes:
		unique: [
			['patient_id']
			['lab_id']
		]
	sections_group: [
		'Labs':
			fields: ['lab_id', 'lab_comments', 'active']
		'Draw Frequency':
			fields: ['draw_frequency', 'draw_frequency_other', 'final_draw_frequency', 'next_draw_date']
		'Draw History':
			fields: ['draw_history']
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Order > Labs Order'
	grid:
		fields: ['lab_id', 'draw_frequency', 'draw_frequency_other', 'next_draw_date', 'active']
		sort: ['-id']
	find:
		basic: ['lab_id', 'draw_frequency', 'active']
	label: 'Labs Order'
