fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			required: true
			source: 'careplan_order'
			type: 'int'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status_id:
					'static': ['1', '5']
		view:
			columns: 2
			label: 'Referral'
			readonly: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/careplan_order/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'order_no': ['order_no']
					'prescriber_id': ['prescriber_id']
			]

	order_no:
		model:
			required: true
		view:
			columns: 2
			label: 'Referral No'
			readonly: true

	prescriber_id:
		model:
			prefill: ['treatment_plan_nursing']
			required: true
			source: 'patient_prescriber'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			columns: 2
			label: 'Prescriber'
			note: '24'

	start_of_care_date:
		model:
			prefill: ['treatment_plan_nursing']
			required: true
			type: 'date'
		view:
			label: 'Start of Care Date'
			note: '2'
			columns: 2

	certification_period:
		model:
			prefill: ['treatment_plan_nursing']
			required: true
			type: 'int'
		view:
			label: 'Certification Perid'
			note: 'months'
			columns: 2

	dx_ids:
		model:
			prefill: ['treatment_plan_nursing']
			required: true
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			embed:
				form: 'patient_diagnosis'
				selectable: true
			grid:
				edit: false
				rank: 'none'
				fields: ['rank', 'dx_id', 'diagnosis_start_date']
				label: ['Rank', 'Diagnosis', 'Start Date']
				width: [10, 75, 15]
				selectall: true
			label: 'Diagnosis'

	patient_medications:
		model:
			prefill: ['treatment_plan_nursing']
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': 'Active'
		view:
			embed:
				form: 'patient_medication'
				selectable: true
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				rank: 'none'
				fields: ['fdb_id', 'medication_dose', 'medication_frequency', 'start_date']
				label: ['Medication', 'Dose', 'Frequency', 'Start Dt']
				width: [35, 25, 25, 15]
				selectall: true
			label: 'Medication Profile'

	patient_interactions:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			readonly: true
			embed:
				form: 'interactions'
				selectable: false
			grid:
				add: 'none'
				hide_cardmenu: true
				edit:true
				rank: 'none'
				fields: ['created_by', 'has_da', 'has_dd']
				label: ['Created By','Drug Allergy', 'Drug Drug']
				width: [30, 30, 30]
			label: 'Interactions'

	patient_interaction_btn:
		model:
			source: ['Get Interactions']
		view:
			columns: 2
			class: 'dsl-button'
			control: 'checkbox'
			label: 'Patient Interaction'
			validate: [
				name: 'DrugAllergyInteraction'
			]

	discontinued_medications:
		model:
			prefill: ['treatment_plan_nursing']
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': 'Active'
		view:
			embed:
				form: 'patient_medication'
				selectable: true
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['fdb_id', 'medication_dose', 'end_date', 'discontinued_reason']
				label: ['Medication', 'Dose', 'End Dt', 'Discontinue Reason']
				width: [30, 20, 25, 25]
				selectall: true
			label: 'Discontinued Medications'

	active_rx:
		model:
			prefill: ['treatment_plan_nursing']
			multi: true
			sourcefilter:
				order_no:
					'dynamic': '{order_no}'
				discontinued:
					'static': '!Yes'
		view:
			embed:
				query: 'active_prescriptions'
				selectable: true
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['inventory_id', 'type_id', 'dose', 'dose_unit_id', 'frequency_id']
				label: ['Drug', 'Type', 'Dose', 'Unit', 'Freq']
				width: [30, 15, 10, 20, 25]
				selectall: true
			label: 'Active Prescriptions'

	# Care Plan Changes
	problems:
		model:
			subfields:
				template_id:
					type: 'int'
					readonly: true
					offscreen: true
				date:
					label: 'Date'
					type: 'timestamp'
					readonly: true
					style:
						width: '20%'
				problem:
					label: 'Problem'
					type: 'text'
					dynamic: 'careplan_problem'
					style:
						width: '50%'
				compeleted:
					type: 'checkbox'
					label: 'Complete'
					style:
						width: '10%'
				compeleted_date:
					label: 'Comp Date'
					type: 'text'
					readonly: true
					style:
						width: '20%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Problems'
			transform: [
					name: 'MarkCompDate'
					field: 'problems'
			]

	goals:
		model:
			subfields:
				template_id:
					type: 'int'
					readonly: true
					offscreen: true
				date:
					label: 'Date'
					type: 'timestamp'
					readonly: true
					style:
						width: '20%'
				goal:
					label: 'Goal'
					type: 'text'
					dynamic: 'careplan_goal'
					style:
						width: '50%'
				compeleted:
					type: 'checkbox'
					label: 'Complete'
					style:
						width: '10%'
				compeleted_date:
					label: 'Comp Date'
					type: 'text'
					readonly: true
					style:
						width: '20%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Goals'
			transform: [
					name: 'MarkCompDate'
					field: 'goals'
			]

	interventions:
		model:
			subfields:
				template_id:
					type: 'int'
					readonly: true
					offscreen: true
				date:
					label: 'Date'
					type: 'timestamp'
					readonly: true
					style:
						width: '20%'
				intervention:
					label: 'Intervention'
					type: 'text'
					dynamic: 'careplan_intervention'
					style:
						width: '50%'
				compeleted:
					type: 'checkbox'
					label: 'Complete'
					style:
						width: '10%'
				compeleted_date:
					label: 'Comp Date'
					type: 'text'
					readonly: true
					style:
						width: '20%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Interventions'
			transform: [
					name: 'MarkCompDate'
					field: 'interventions'
			]

	# Functional Limitations
	functional_lim:
		model:
			multi: true
			source: ['Amputation', 'Bowel/Bladder (Incontinence)',
					'Contracture', 'Hearing', 'Paralysis', 'Endurance',
					'Ambulation', 'Speech', 'Legally Blind',
					'Dyspnea With Minimal Exertion', 'Other (Specify)']
			if:
				'Other (Specify)':
					fields: ['functional_lim_other']
		view:
			control: 'checkbox'
			label: 'Select all functional limitations'
			note: '18A'
			columns: 2

	functional_lim_other:
		view:
			label: 'Functional limitations Other'
			columns: 2

	# Mental Status
	mental_status:
		model:
			multi: true
			source: ['Oriented', 'Comatose', 'Forgetful',
					'Depressed', 'Disoriented',
					'Lethargic', 'Agitated',
					'Other']
			if:
				'Other':
					fields: ['mental_status_other']
		view:
			control: 'checkbox'
			label: 'Mental Status'
			note: '19'
			columns: 2

	mental_status_other:
		model:
			required: true
		view:
			label: 'Mental Status Other'
			columns: 2

	prognosis:
		model:
			source: ['Poor', 'Guarded', 'Fair', 'Good', 'Excellent']
		view:
			control: 'radio'
			label: 'Prognosis'
			note: '20'
			columns: 2

	# Discharge / Rehab
	rehab_pot:
		model:
			prefill: ['treatment_plan_nursing']
			source: ['Good for Stated Goals', 'Fair for Stated Goals', 'Poor for Stated Goals']
		view:
			control: 'radio'
			label: "Rehab Potential"
			note: '22'

	note:
		model:
			prefill: ['treatment_plan_nursing']
			max: 4096
		view:
			control: 'area'
			label: 'Care Plan Note'

	rn_signature:
		model:
			required: true
			type: 'json'
		view:
			control: 'esign'
			label: 'Nurse E-Signature'


	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'treatment_plan_nursing'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Other Form'
					direct_attachment: 'Yes'
					form_code: '{code}'
					form_name: 'treatment_plan_nursing'
					source: 'Scanned Document'
					patient_id: '{patient_id}'
					form_filter: 'treatment_plan_nursing'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin', 'cm', 'cma', 'nurse', 'pharm']
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['start_of_care_date', 'certification_period']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
			['order_id']
			['order_no']
		]
		unique: [
			['code']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		treatment_plan_nursing:
			link:
				order_id: 'order_id'
	sections_group: [
		'Associated Records':
			hide_header: true
			indent: true
			tab: 'Header'
			fields: ['code', 'order_id', 'order_no','prescriber_id']
		'Header':
			hide_header: true
			indent: true
			tab: 'Header'
			fields: ['start_of_care_date', 'certification_period']
		'Diagnoses':
			hide_header: true
			indent: true
			tab: 'Diagnoses'
			fields: ['dx_ids']
		'Active Medications':
			indent: true
			tab: 'Medications/Orders'
			fields: ['patient_medications']
		'DUR - DD DA Interaction':
			hide_header: true
			indent: false
			tab: 'Medications/Orders'
			fields: ['patient_interaction_btn']
		'DUR - Interaction':
			hide_header: true
			indent: false
			tab: 'Medications/Orders'
			fields: ['patient_interactions']
		'Discontinued Medications':
			indent: true
			tab: 'Medications/Orders'
			fields: ['discontinued_medications']
		'Active Prescriptions':
			indent: true
			tab: 'Medications/Orders'
			fields: ['active_rx']
		'Functional Limitations':
			prefill: 'treatment_plan_nursing'
			indent: true
			tab: 'Conditions'
			fields: ['functional_lim', 'functional_lim_other']
		'Mental Status':
			prefill: 'treatment_plan_nursing'
			indent: true
			tab: 'Conditions'
			fields: ['mental_status', 'mental_status_other']
		'Problems':
			prefill: 'treatment_plan_nursing'
			indent: true
			tab: 'Prob/Goal/Int'
			fields: ['problems']
		'Goals':
			prefill: 'treatment_plan_nursing'
			indent: true
			tab: 'Prob/Goal/Int'
			fields: ['goals']
		'Interventions':
			indent: true
			tab: 'Prob/Goal/Int'
			fields: ['interventions']
		'Discharge Plans':
			indent: true
			tab: 'Discharge'
			fields: ['prognosis', 'rehab_pot', 'note', 'rn_signature']
		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'
	]

view:
	comment: 'Patient > Careplan > Treatment Plan'
	grid:
		fields: ['created_on', 'created_by', 'reviewed_by', 'reviewed_on', 'start_of_care_date', 'certification_period']
		sort: ['created_on']
	label: 'Patient Treatment Plan'
	open: 'read'
