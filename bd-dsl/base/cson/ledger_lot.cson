fields:

	ledger_id:
		model:
			source: 'ledger_inventory'
		view:
			readonly: true
			offscreen: true

	source_form:
		view:
			readonly: true
			offscreen: true

	source_id:
		model:
			type: 'int'
			required: false
		view:
			readonly: true
			offscreen: true

	source_no:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	transaction_type:
		model:
			required: true
			source: ['Dispense', 'Transfer', 'Purchase', 'Recall', 'Adjustment', 'Void']
		view:
			label: 'Transaction Type'
			readonly: true

	transaction_date:
		model:
			type: 'date'
		view:
			label: 'Transaction Date'
			readonly: true

	po_id:
		model:
			required: false
			source: 'po'
		view:
			label: 'PO'
			readonly: true

	supplier_id:
		model:
			source: 'list_supplier'
		view:
			label: 'Supplier'
			readonly: true

	site_id:
		model:
			required: true
			source: 'site'
		view:
			label: 'Site'
			readonly: true

	patient_id:
		model:
			required: false
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true

	careplan_id:
		model:
			required: false
			source: 'careplan'
			type: 'int'
		view:
			label: 'Care Plan'
			readonly: true

	order_id:
		model:
			required: false
			source: 'careplan_order'
			type: 'int'
		view:
			label: 'Referral ID'
			readonly: true

	order_no:
		view:
			label: 'Referral No'
			readonly: true

	order_item_id:
		model:
			source: 'careplan_order_rx'
			required: false
			type: 'int'
		view:
			label: 'Prescription'
			readonly: true

	physician_order_id:
		view:
			label: 'Prescriber Order #'
			note: 'Associated Surescripts Physician Order #'
			readonly: true

	rx_no:
		view:
			label: ''
			readonly: true

	ticket_no:
		model:
			type: 'text'
		view:
			label: 'Ticket #'
			readonly: true
			offscreen: true

	ticket_item_no:
		model:
			type: 'text'
		view:
			label: 'Ticket Item #'
			readonly: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
		view:
			label: 'Ordered Item'
			readonly: true

	acquisition_cost:
		model:
			rounding: 0.00001
			type: 'decimal'
		view:
			label: 'Acquisition Cost'
			class: 'numeral money'
			format: '$0,0.0000'
			readonly: true

	acquisition_cost_ea:
		model:
			rounding: 0.00001
			type: 'decimal'
		view:
			label: 'Acquisition Cost Per Unit'
			class: 'numeral money'
			format: '$0,0.0000'
			readonly: true

	lot_no:
		model:
			required: true
		view:
			label: 'Lot No'
			readonly: true

	quantity:
		model:
			required: true
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Quantity'
			readonly: true

	description:
		view:
			label: 'Description'
			readonly: true

	is_340b:
		model:
			max: 3
			source: ['Yes']
			default: 'Yes'
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Dispense was 340B Eligible?'
			readonly: true

	raw_barcode:
		view:
			label: 'Item Barcode'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     []
		read:       ['admin','pharm','csr','cm', 'nurse']
		read_all:   ['admin','pharm','csr','cm', 'nurse']
		request:    []
		review:     ['admin','pharm','csr','cm', 'nurse']
		update:     []
		update_all: []
		write:      []
	bundle: ['audit']
	indexes:
		many: [
			['source_form'],
			['source_id'],
			['source_no'],
			['site_id'],
			['transaction_type'],
			['lot_no'],
			['inventory_id'],
			['patient_id'],
			['careplan_id'],
			['po_id'],
			['supplier_id'],
			['order_id'],
			['order_no'],
			['order_item_id'],
			['physician_order_id'],
			['rx_no'],
			['ticket_no'],
			['ticket_item_no'],
			['raw_barcode']
		]

	name: ['transaction_type', 'lot_no', 'ticket_no', 'ticket_item_no']
	sections:
		'Ledger Lot Entry':
			fields: ['transaction_type', 'site_id', 'patient_id', 'order_id', 'order_no', 'order_item_id',
			'physician_order_id', 'rx_no', 'lot_no', 'quantity', 'description', 'is_340b', 'raw_barcode',
			'acquisition_cost', 'acquisition_cost_ea']
view:
	hide_cardmenu: true
	comment: 'Ledger Lot Entry'
	find:
		basic: ['transaction_type', 'site_id', 'inventory_id', 'rx_no']
		advanced: ['patient_id', 'po_id', 'supplier_id', 'lot_no', 'is_340b']
	grid:
		fields: ['patient_id', 'inventory_id', 'lot_no', 'transaction_type', 'transaction_date', 'description']
		sort: ['-id']
	label: 'Ledger Lot Entry'
	open: 'edit'