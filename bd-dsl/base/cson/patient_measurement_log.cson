fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	pediatric:
		model:
			prefill: ['patient']
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['ibw', 'ibw_percentage', 'abw']
		view:
			control: 'radio'
			label: "Pediatric patient?"
			offscreen: true
			readonly: true

	date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Date'
			columns: 3
			template: '{{now}}'

	weight:
		model:
			max: 1000
			min: 1
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'unit'
			label: 'Confirm Weight (Kg)'
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]
			validate: [
				name: 'CalculateHeightWeightMeasures'
			]
			columns: 3

	height:
		model:
			prefill: ['patient_measurement_log']
			max: 250
			min: 15
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'unit'
			label: 'Height (cm)'
			note: 'E.g.: 143, 143cm, 1.43m, 56in, 56", 4\' 8", 4 8'
			transform: [
					name: 'HeightTransform'
			]
			validate: [
				name: 'CalculateHeightWeightMeasures'
			]
			columns: 3

	ibw:
		model:
			max: 1000
			min: 1
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'unit'
			label: 'IBW (Kg)'
			readonly: true
			columns: 3
			transform: [
					name: 'WeightTransform'
			]

	ibw_percentage:
		model:
			max: 100
			min: 1
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'IBW %'
			class: 'numeral'
			format: 'percent'
			columns: 3
			readonly: true

	abw:
		model:
			max: 1000
			min: 1
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'unit'
			label: 'Adjusted BW'
			note: 'E.g.: 78, 78kg, 172lbs'
			columns: 3
			readonly: true

	lbw:
		model:
			max: 1000
			min: 1
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'unit'
			transform: [
					name: 'WeightTransform'
			]
			label: 'LBW (Kg)'
			readonly: true
			columns: 3

	bsa:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'BSA (m2)'
			readonly: true
			columns: 3
			validate: [
				name: 'CalculateBSA'
			]

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	envoy_external_tag:
		model:
			type: 'text'
		view:
			label: 'Envoy External Tag'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['patient-form']
	name: ['patient_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		patient_measurement_log:
			link:
				patient_id: 'patient_id'
	indexes:
		many: [
			['patient_id']
		]
	sections:
		'Measurement Log':
			hide_header: true
			fields: ['patient_id', 'date', 'weight', 'height', 'ibw', 'lbw', 'bsa']

view:
	dimensions:
		width: '75%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Patient > Measurement Log'
	grid:
		fields: ['created_by', 'date', 'weight', 'height']
		sort: ['-id']
	label: 'Measurement Log'
	open: 'read'
