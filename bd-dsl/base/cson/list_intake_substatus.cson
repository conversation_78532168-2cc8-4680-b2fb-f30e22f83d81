fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'
			findunique: true
			columns: 3

	name:
		model:
			required: true
			max: 128
		view:
			label: 'Name'
			findunique: true
			columns: 3

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 3

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['status']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['name']
		]
		many: [
			['code']
		]
	name: '{code} - {name}'
	sections:
		'Intake Substatus':
			fields: ['code', 'name', 'allow_sync', 'active']

view:
	comment: 'Manage > Intake Substatus'
	find:
		basic: ['code', 'name']
	grid:
		fields: ['code', 'name']
		sort: ['code']
	label: 'Intake Substatus'
	open: 'read'
