fields:

	code_type:
		model:
			source: ['CPT', 'HCPC', 'A-Code', 'B-Code', 'E-Code', 'G-Code', 'J-Code', 'K-Code', 'Q-Code', 'S-Code', 'T-Code']
			required: true
			if:
				'CPT':
					fields: ['cpt_type', 'therapy_id']
				'E-Code':
					fields: ['dme_frequency']
				'B-Code':
					fields: ['bcode_type']
				'S-Code':
					fields: ['scode_type']
				'A-Code':
					fields: ['supply_frequency']
				'G-Code':
					fields: ['gcode_type']
				'K-Code':
					fields: ['kcode_type']
				'T-Code':
					fields: ['nursing_frequency']
		view:
			columns: 3
			label: 'Code Type'

	cpt_type:
		model:
			source: ['Nursing', 'Other']
			if:
				'Nursing':
					fields: ['nursing_frequency']
		view:
			control: 'radio'
			columns: 3
			label: 'CPT Type'

	bcode_type:
		model:
			source: ['DME', 'Supplies']
			if:
				'DME':
					fields: ['dme_frequency']
				'Supplies':
					fields: ['supply_frequency']
		view:
			control: 'radio'
			columns: 3
			label: 'B-Code Type'

	gcode_type:
		model:
			source: ['Drug Administration', 'Other']
			if:
				'Drug Administration':
					fields: ['admin_frequency', 'therapy_id']
		view:
			control: 'radio'
			columns: 3
			label: 'G-Code Type'

	kcode_type:
		model:
			source: ['DME', 'Supplies']
			if:
				'Supplies':
					fields: ['supply_frequency']
				'DME':
					fields: ['dme_frequency']
		view:
			control: 'radio'
			columns: 3
			label: 'K-Code Type'

	scode_type:
		model:
			source: ['Nursing', 'Supplies']
			if:
				'Supplies':
					fields: ['supply_frequency']
				'Nursing':
					fields: ['nursing_frequency']
		view:
			control: 'radio'
			columns: 3
			label: 'S-Code Type'

	code:
		model:
			required: true
		view:
			columns: 3
			label: 'Code'

	name:
		model:
			max: 128
			required: true
		view:
			columns: 3
			label: 'Name'
			findunique: true

	dme_frequency:
		model:
			required: true
			source: ['Daily', 'Weekly', 'Monthly', 'Purchase']
		view:
			columns: 3
			control: 'radio'
			label: 'DME Frequency'

	supply_frequency:
		model:
			required: true
			source: ['Daily', 'Weekly', 'Monthly', 'Per Each', 'Per Package', 'Per Calorie']
		view:
			columns: 3
			control: 'radio'
			label: 'Supplies Frequency'

	nursing_frequency:
		model:
			required: true
			source: ['15 Minutes', '30 Minutes', '1 Hour', '2 Hours', 'Per Visit']
		view:
			columns: 3
			control: 'radio'
			label: 'Nursing Frequency'

	admin_frequency:
		model:
			required: true
			source: ['15 Minutes', '30 Minutes', '1 Hour', 'Per Visit']
		view:
			columns: 3
			control: 'radio'
			label: 'Drug Administration Frequency'

	therapy_id:
		model:
			multi: true
			required: false
			source: 'list_therapy'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Therapy'

	active:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			columns: 3
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Active'
			findfilter: 'Yes'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['billing']
	sync_mode: 'full'
	indexes:
		unique: [
			['code', 'name']
		]
	name: "{name} ({code})"
	sections:
		'Details':
			fields: ['code_type', 'cpt_type', 'bcode_type', 'gcode_type', 'kcode_type',
			'scode_type', 'code', 'name', 'therapy_id', 'dme_frequency',
			'supply_frequency', 'nursing_frequency', 'admin_frequency', 'active']

view:
	comment: 'Manage > Billing Code'
	find:
		basic: ['code_type', 'code', 'name', 'therapy_id']
	grid:
		fields: ['created_on', 'created_by', 'code_type', 'name', 'code', 'therapy_id']
		sort: ['code']
	label: 'Billing Code'
	open: 'read'
