#TABLE: RPEIUM0_UOM_MSTR
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#UOM_MSTR_ID
	uom_mstr_id:
		model:
			type: 'int'
		view:
			label: 'Master Identifier'
			readonly: true
			columns: 3

	#UOM_MSTR_DESC
	uom_mstr_desc:
		view:
			label: 'Master Description'
			readonly: true
			columns: 3

	#UOM_MSTR_ABBR
	uom_mstr_abbr:
		view:
			label: 'Master Abbreviation'
			readonly: true
			columns: 3

	#UOM_MSTR_PLURAL_DESC
	uom_mstr_plural_desc:
		view:
			label: 'Master Plural'
			readonly: true
			columns: 3

	#UOM_MSTR_PREFERRED_DESC
	uom_mstr_preferred_desc:
		view:
			label: 'Master Preferred'
			readonly: true
			columns: 3

	#UOM_MSTR_PLURAL_ABBR
	uom_mstr_plural_abbr:
		view:
			label: 'Master Plural Abbreviation'
			readonly: true
			columns: 3

	#UOM_MSTR_PLURAL_PREFERRED_DESC	
	uom_mstr_plural_preferred_desc:
		view:
			label: 'Master Plural Preferred'
			readonly: true
			columns: 3

	#UOM_STDS_ORG_ABBR
	uom_stds_org_abbr:
		view:
			label: 'Short Standards Organization'
			readonly: true
			columns: 3

	#UOM_STDS_ORG_DESC
	uom_stds_org_desc:
		view:
			label: 'Long Standards Organization'
			readonly: true
			columns: 3

	#UOM_PLURAL_STDS_ORG_DESC
	uom_plural_stds_org_desc:
		view:
			label: 'Long Plural Standards Organization'
			readonly: true
			columns: 3

	#UOM_PLURAL_STDS_ORG_ABBR
	uom_plural_stds_org_abbr:
		view:
			label: 'Short Plural Standards Organization'
			readonly: true
			columns: 3

	#UOM_MSTR_COMPONENT1_ID
	uom_mstr_component1_id:
		model:
			type: 'int'
		view:
			label: 'Master Component 1'
			readonly: true
			columns: 3

	#UOM_MSTR_COMPONENT2_ID
	uom_mstr_component2_id:
		model:
			type: 'int'
		view:
			label: 'Master Component 2'
			readonly: true
			columns: 3

	#UOM_MSTR_COMPONENT3_ID
	uom_mstr_component3_id:
		model:
			type: 'int'
		view:
			label: 'Master Component 3'
			readonly: true
			columns: 3

	#UOM_TYPE_CD
	uom_type_cd:
		model:
			type: 'int'
		view:
			label: 'Type Code'
			readonly: true
			columns: 3

	#RATIO_IND
	ratio_ind:
		model:
			type: 'int'
		view:
			label: 'Ratio Indicator'
			readonly: true
			columns: 2

	#RATE_IND
	rate_ind:
		model:
			type: 'int'
		view:
			label: 'Rate Indicator'
			readonly: true
			columns: 2

	#DOSE_IND
	dose_ind:
		model:
			type: 'int'
		view:
			label: 'Dose Indicator'
			readonly: true
			columns: 2

	#INTERVAL_IND
	interval_ind:
		model:
			type: 'int'
		view:
			label: 'Interval Indicator'
			readonly: true
			columns: 2

	#PATIENT_PARAM_REQ_CD
	patient_param_req_cd:
		model:
			type: 'int'
		view:
			label: 'Patient Parameter Required Code'
			readonly: true
			columns: 2

	#PARAM_INCORP_UOM_ID
	param_incorp_uom_id:
		model:
			type: 'int'
		view:
			label: 'Parameter Incorporated'
			readonly: true
			columns: 3

	#PEDIATRIC_DOSE_TEXT_IND
	pediatric_dose_text_ind:
		model:
			type: 'int'
		view:
			label: 'Pediatric Dose Text Indicator'
			readonly: true
			columns: 2

	#COMPARISON_UOM_ID
	comparison_uom_id:
		model:
			type: 'int'
		view:
			label: 'Least Common Denominator'
			readonly: true
			columns: 3

	#DR2_UNITS
	dr2_units:
		view:
			label: 'Dose Range Check Module (DRCM) Units Code'
			readonly: true
			columns: 2

	#POEUNITCDE
	poeunitcde:
		model:
			type: 'int'
		view:
			label: 'POEM Unit Code'
			readonly: true
			columns: 2

	#UOM_ID
	uom_id:
		model:
			type: 'int'
		view:
			label: 'Strength'
			readonly: true
			columns: 3

	#OVW_UOM_ID
	ovw_uom_id:
		model:
			type: 'int'
		view:
			label: 'OrderKnowledge'
			readonly: true
			columns: 3
model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['uom_mstr_desc']
	indexes:
		many: [
			['uom_mstr_id']
			['uom_id']
			['ovw_uom_id']
			['param_incorp_uom_id']
			['comparison_uom_id']
		]
	sections:
		'Information':
			tab: 'Information'
			fields: ['patient_param_req_cd','pediatric_dose_text_ind', 'dr2_units', 'poeunitcde']
		'Description':
			tab: 'Units of Measure Description'
			fields: ['uom_type_cd', 'uom_mstr_desc', 'uom_mstr_abbr', 'uom_mstr_preferred_desc',
			'uom_mstr_plural_desc', 'uom_mstr_plural_abbr', 'uom_mstr_plural_preferred_desc',
			'uom_plural_stds_org_abbr', 'uom_plural_stds_org_desc', 'uom_stds_org_abbr' ,'uom_stds_org_desc']
		'Indentifiers':
			tab: 'Units of Measure Description'
			fields: ['uom_mstr_id', 'uom_mstr_component1_id', 'uom_mstr_component2_id', 'uom_mstr_component3_id',
			'param_incorp_uom_id', 'comparison_uom_id', 'uom_id', 'ovw_uom_id']
		'Indicator':
			tab: 'Information'
			fields: ['ratio_ind', 'rate_ind', 'dose_ind', 'interval_ind']

view:
	comment: 'Manage > List FDB Unit Master'
	find:
		basic: ['uom_mstr_id']
	grid:
		fields: ['uom_mstr_id', 'uom_mstr_desc', 'uom_mstr_plural_desc', 'uom_stds_org_abbr']
	label: 'List FDB Unit Master'
