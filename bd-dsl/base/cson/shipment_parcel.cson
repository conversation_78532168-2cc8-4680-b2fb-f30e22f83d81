fields:

    delivery_ticket_id:
        model:
            required: true
            source: 'careplan_delivery_tick'
        view:
            label: 'delivery ticket id'
            offscreen: true

    parcel_object_id:
        model:
            required: true
        view:
            offscreen: true
            readonly: true
            label: 'Parcel Id'

    height:
        model:
            required: true
            type: 'decimal'
            default: 5.0
        view:
            label: 'Parcel Height'
    
    width:
        model:
            required: true
            type: 'decimal'
            default: 5.0
        view:
            label: 'Parcel Width'

    length:
        model:
            required: true
            type: 'decimal'
            default: 5.0
        view:
            label: 'Parcel Length'

    distance_unit:
        model:
            required: true
            default: 'in'
            source: ['cm', 'in', 'ft', 'm', 'mm', 'yd']
        view:
            label: 'Distance Unit'

    weight:
        model:
            required: true
            type: 'decimal'
            default: 2.0
        view:
            label: 'Parcel Weight'

    mass_unit:
        model:
            required: true
            default: 'lb'
            source: ['g', 'kg', 'lb', 'oz']
        view:
            label: 'Mass Unit'



model:
    access:
        create:     []
        create_all: ['admin']
        delete:     ['admin']
        read:       ['admin']
        read_all:   ['admin']
        request:    []
        update:     []
        update_all: ['admin']
        write:      ['admin']
    bundle: ['setup']
    indexes: 
        unique: []
    
    name: '{provider}'
    sections:
        'Main':
            fields: ['height', 'width', 'length', 'distance_unit', 'weight', 'mass_unit']

view:
    hide_cardmenu: true
    comment: 'Delivery Ticket > Shipments > Parcel'
    grid:
        fields: ['height', 'width', 'length', 'distance_unit', 'weight', 'mass_unit']
    label: 'Parcel'
    open: 'read'
