fields:

	code:
		view:
			columns: 2
			label: 'Error Code'
			readonly: true

	description:
		view:
			control: 'area'
			label: 'Error Description'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['code', 'description']
	sections:
		'Failure':
			hide_header: true
			fields: ['code', 'description']

view:
	dimensions:
		width: '55%'
		height: '55%'
	hide_cardmenu: true
	comment: 'Failure'
	grid:
		fields: ['code', 'description']
		width: [25, 75]
		sort: ['-created_on']
	label: 'Failure'
	open: 'read'