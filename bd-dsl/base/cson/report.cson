fields:

	name:
		model:
			required: true
		view:
			label: 'Name'

	code:
		model:
			required: true
		view:
			# offscreen: false
			# readonly: false
			label: 'Code'

	type:
		model:
			required: false
			source: ['Chart', 'Label', 'Page', 'Page Repeating', 'Template']
		view:
			control: 'radio'
			label: 'Type'


	json_data:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			readonly: true
			label: 'Design Definations'
	
	query:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			readonly: true
			label: 'Query Definations'

	parameter_name_1:
		model:
			required: false
			if:
				'*':
					fields: ['parameter_name_2', 'parameter_prompt_2']
		view:
			readonly: true
			label: 'Parameter Name 1'

	parameter_prompt_1:
		model:
			required: false
			if:
				'*':
					fields: ['parameter_name_2', 'parameter_prompt_2']
		view:
			readonly: true
			label: 'Parameter Prompt 1'

	parameter_name_2:
		model:
			required: false
			if:
				'*':
					fields: ['parameter_name_3', 'parameter_prompt_3']
		view:
			readonly: true
			label: 'Parameter Name 2'

	parameter_prompt_2:
		model:
			required: false
			if:
				'*':
					fields: ['parameter_name_3', 'parameter_prompt_3']
		view:
			readonly: true
			label: 'Parameter Prompt 2'

	parameter_name_3:
		model:
			required: false
			if:
				'*':
					fields: ['parameter_name_4', 'parameter_prompt_4']
		view:
			readonly: true
			label: 'Parameter Name 3'

	parameter_prompt_3:
		model:
			required: false
			if:
				'*':
					fields: ['parameter_name_4', 'parameter_prompt_4']
		view:
			readonly: true
			label: 'Parameter Prompt 3'

	parameter_name_4:
		model:
			required: false
		view:
			label: 'Parameter Name 4'
			readonly: true

	parameter_prompt_4:
		model:
			required: false
		view:
			label: 'Parameter Prompt 4'
			readonly: true

	module:
		model:
			type: 'int'
			source: 'module'
			required: true
		view:
			label: 'Module'
	submodule:
		model:
			source: ['Fax']
		view:
			label: 'Submodule'
	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'


model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['setup']
	name: '{name}'
	sync_mode: 'mixed'
	indexes:
		unique:[
			['code']
		]
	sections:
		'Main':
			fields: ['name', 'code','type', 'module', 'submodule']
		'Parameters':
			fields: ['parameter_name_1', 'parameter_prompt_1','parameter_name_2', 'parameter_prompt_2', 'parameter_name_3', 'parameter_prompt_3', 'parameter_name_4', 'parameter_prompt_4', 'allow_sync', 'active']

view:
	comment: 'Report'
	grid:
		fields: ['name', 'code', 'module', 'submodule', 'type', 'allow_sync', 'active']
		sort: ['name', 'code']
	label: 'Report'
	open: 'read'
