fields:

	claim_no:
		view:
			label: 'Claim #'
			note: 'Claim # against the invoice'
			readonly: true
			offscreen: true

	patient_id:
		model:
			type: 'int'
			required: true
			source: 'patient'
		view:
			label: 'ID'
			readonly: true
			offscreen: true

	site_id:
		model:
			type: 'int'
			source: 'site'
		view:
			label: 'Site ID'
			readonly: true
			offscreen: true

	payer_id:
		model:
			type: 'int'
			source: 'payer'
		view:
			label: 'Payer ID'
			readonly: true
			offscreen: true

	reviewed:
		model:
			source: ['Yes']
		view:
			columns: 3
			control: 'checkbox'
			label: 'Reviewed?'
			class: 'checkbox-only'

	reviewed_by:
		model:
			type: 'int'
			source: 'user'
		view:
			columns: 3
			label: 'Reviewed By'
			template: '{{user.id}}'

	notes:
		view:
			control: 'area'
			label: 'Notes'

	# Claim Dates
	claim_statement_period_start:
		model:
			type: 'date'
		view:
			columns: -4
			label: 'Statement Period Start'
			note: 'DTM02 where DTM01=232'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimStatementPeriodStart'

	claim_statement_period_end:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Statement Period End'
			note: 'DTM02 where DTM01=233'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimStatementPeriodEnd'

	coverage_expiration_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Coverage Expiration Date'
			note: 'DTM02 where DTM01=036'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].coverageExpirationDate'

	claim_received_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Claim Received Date'
			note: 'DTM02 where DTM01=050'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimReceivedDate'

	# Other Claim Related IDs
	group_number:
		view:
			columns: 4
			label: 'Group Number'
			note: 'REF02 where REF01=6P'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].otherClaimRelatedIdentification.groupNumber'

	authorization_number:
		view:
			columns: 4
			label: 'Authorization Number'
			note: 'REF02 where REF01=BB'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].otherClaimRelatedIdentification.authorizationNumber'

	prior_authorization_number:
		view:
			columns: 4
			label: 'Prior Authorization Number'
			note: 'REF02 where REF01=G1'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].otherClaimRelatedIdentification.priorAuthorizationNumber'

	# Claim Payment Information
	patient_control_number:
		view:
			columns: 4
			label: 'Control Number'
			note: 'CLP01'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimPaymentInfo.patientControlNumber'

	claim_status_code:
		model:
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CLP02'
		view:
			columns: 4
			label: 'Claim Status Code'
			note: 'CLP01'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimPaymentInfo.claimStatusCode'

	# Patient Information
	patient_last_name:
		view:
			columns: -4
			label: 'Last Name'
			note: 'NM103'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].patientName.lastName'

	patient_first_name:
		view:
			columns: 4
			label: 'First Name'
			note: 'NM104'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].patientName.firstName'

	patient_middle_name:
		view:
			columns: 4
			label: 'Middle Name'
			note: 'NM105'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].patientName.middleName'

	patient_suffix:
		view:
			columns: 4
			label: 'Suffix'
			note: 'NM107'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].patientName.suffix'

	patient_member_id:
		view:
			columns: 4
			label: 'Member ID'
			note: 'NM109 where NM108=MI'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].patientName.memberId'

	# Supplemental Information
	coverage_amount:
		model:
			type: 'decimal'
			max: **********.99
			rounding: 0.01
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Coverage Amount'
			note: 'LOOP 2100; AMT02 where AMT01 = AU'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimSupplementalInformation.coverageAmount'

	discount_amount:
		model:
			type: 'decimal'
			max: **********.99
			rounding: 0.01
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Discount Amount'
			note: 'LOOP 2100; AMT02 where AMT01 = DB'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimSupplementalInformation.discountAmount'

	per_day_limit:
		view:
			columns: 4
			label: 'Per Day Limit'
			note: 'LOOP 2100; AMT02 where AMT01 = DY'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimSupplementalInformation.perDayLimit'

	pt_amount_paid:
		model:
			type: 'decimal'
			max: **********.99
			rounding: 0.01
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Discount Amount'
			note: 'LOOP 2100; AMT02 where AMT01 = F5'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimSupplementalInformation.patientAmountPaid'

	negative_balance:
		model:
			type: 'decimal'
			max: **********.99
			rounding: 0.01
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Negative Ledger Balance'
			note: 'LOOP 2100; AMT02 where AMT01 = NL'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimSupplementalInformation.negativeLedgerBalance'

	covered_actual_quantity:
		view:
			columns: 4
			label: 'Covered Actual Quantity'
			note: 'LOOP 2100; QTY02 where QTY01 = CA'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimSupplementalInformationQuantities.coveredActual'

	co_insured_actual_quantity:
		view:
			columns: 4
			label: 'Co-Insured Actual Quantity'
			note: 'LOOP 2100; QTY02 where QTY01 = CD'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimSupplementalInformationQuantities.coInsuredActual'

	non_covered_estimated_quantity:
		view:
			columns: 4
			label: 'Non-Covered Estimated Quantity'
			note: 'LOOP 2100; QTY02 where QTY01 = NE'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimSupplementalInformationQuantities.nonCoveredEstimated'

	visits_actual_quantity:
		view:
			columns: 4
			label: 'Visits Quantity'
			note: 'LOOP 2100; QTY02 where QTY01 = VI'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimSupplementalInformationQuantities.visits'

	prescriptions_quantity:
		view:
			columns: 4
			label: 'Prescriptions Quantity'
			note: 'LOOP 2100; QTY02 where QTY01 = PS'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimSupplementalInformationQuantities.prescription'

	# Subscriber Information
	subscriber_last_name:
		view:
			columns: -4
			label: 'Last Name'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].subscriber.lastName'

	subscriber_first_name:
		view:
			columns: 4
			label: 'First Name'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].subscriber.firstName'

	subscriber_member_id:
		view:
			columns: 4
			label: 'Member ID'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].subscriber.memberId'

	# Other Subscriber
	osub_organization_name:
		view:
			columns: 4
			label: 'Organization Name'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].otherSubscriber.organizationName'
	
	osub_member_id:
		view:
			columns: 4
			label: 'Member ID'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].otherSubscriber.memberId'

	osub_tax_id:
		view:
			columns: 4
			label: 'Tax ID'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].otherSubscriber.taxId'
	
	osub_last_name:
		view:
			columns: -4
			label: 'Last Name'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].otherSubscriber.lastName'

	osub_first_name:
		view:
			columns: 4
			label: 'First Name'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].otherSubscriber.firstName'

	osub_middle_name:
		view:
			columns: 4
			label: 'Middle Name'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].otherSubscriber.middleName'
	
	osub_suffix:
		view:
			columns: 4
			label: 'Suffix'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].otherSubscriber.suffix'

	# Rendering Provider (Pharmacy)
	rendering_provider_name:
		view:
			columns: -4
			label: 'Pharmacy'
			note: 'NM103'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].renderingProvider.organizationName'

	rendering_provider_npi:
		view:
			columns: 4
			label: 'Pharmacy NPI'
			note: 'NM109 where NM108=XX'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].renderingProvider.npi'

	# Claim Contact Information
	claim_contact_name:
		view:
			columns: 4
			label: 'Claim Contact Name'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimContactInformation.contactName'

	claim_contact_methods:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_835_cnt'
		view:
			label: 'Claim Contact Methods'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimContactInformation.contactMethods[*]'

	# Crossover payer
	cx_payer_id:
		model:
			type: 'int'
			source: 'payer'
		view:
			columns: 4
			label: 'Matched Payer'

	cx_organization_name:
		view:
			columns: 4
			label: 'Organization Name'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].crossoverCarrier.organizationName'

	cx_bcbs_plan_id:
		view:
			columns: 4
			label: 'BCBS Plan ID'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].crossoverCarrier.blueCrossBlueShieldAssociationPlanCode'
	
	cx_tax_id:
		view:
			columns: 4
			label: 'Tax ID'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].crossoverCarrier.taxId'

	cx_naci_plan_id:
		view:
			columns: 4
			label: 'NACI Plan ID'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].crossoverCarrier.nationalAssociationOfInsuranceCommissionersIdentification'

	cx_payer_id_text:
		view:
			columns: 4
			label: 'Payer ID'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].crossoverCarrier.payerId'

	cx_pharmacy_process_no:
		view:
			columns: 4
			label: 'Pharmacy Process No'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].crossoverCarrier.pharmacyProcessNumber'
	
	cx_cms_plan_id:
		view:
			columns: 4
			label: 'CMS Plan ID'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].crossoverCarrier.centersForMedicareAndMedicaidServicesPlanId'

	# Priority Corrected Payer
	cr_payer_id:
		model:
			type: 'int'
			source: 'payer'
		view:
			columns: 4
			label: 'Matched Payer'

	cr_organization_name:
		view:
			columns: 4
			label: 'Organization Name'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].correctedPriorityPayer.organizationName'

	cr_bcbs_plan_id:
		view:
			columns: 4
			label: 'BCBS Plan ID'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].correctedPriorityPayer.blueCrossBlueShieldAssociationPlanCode'
	
	cr_tax_id:
		view:
			columns: 4
			label: 'Tax ID'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].correctedPriorityPayer.taxId'

	cr_naci_plan_id:
		view:
			columns: 4
			label: 'NACI Plan ID'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].correctedPriorityPayer.nationalAssociationOfInsuranceCommissionersIdentification'

	cr_payer_id_text:
		view:
			columns: 4
			label: 'Payer ID'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].correctedPriorityPayer.payerId'

	cr_pharmacy_process_no:
		view:
			columns: 4
			label: 'Pharmacy Process No'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].correctedPriorityPayer.pharmacyProcessNumber'
	
	cr_cms_plan_id:
		view:
			columns: 4
			label: 'CMS Plan ID'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].correctedPriorityPayer.centersForMedicareAndMedicaidServicesPlanId'

	mcr_reimbursement_rate:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: -4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Medicare Reimbursement Rate'
			reference: 'MOA01'
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].outpatientAdjudication.reimbursementRate'

	mcr_hcpcs_payable_amount:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Medicare Patient Responsibility Amount'
			reference: 'MOA02'
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].outpatientAdjudication.claimHCPCSPayableAmount'

	rmk_cd:
		model:
			required: false
			multi: true
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'RARC'
		view:
			columns: 4
			max_count: 5
			label: 'Medicare Remark Codes'
			note: 'MOA03 – MOA07'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].outpatientAdjudication.claimPaymentRemarkCode1-5'

	# Payments
	total_charge_amount:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Charge Amount'
			readonly: true

	total_paid_amount:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Paid Amount'
			readonly: true

	total_adjusted_amount:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Adjusted Amount'
			readonly: true

	total_pt_pay:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Patient Responsibility'
			readonly: true

	total_cost:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			label: 'Cost'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_profit:
		model:
			type: 'decimal'
		view:
			columns: 4
			label: 'Profit' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	margin:
		model:
			type: 'decimal'
			rounding: 0.00001
		view:
			columns: 4
			label: 'Margin'
			class: 'numeral discount'
			format: 'percent'
			readonly: true

	total_awp:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			columns: -4
			label: 'Total AWP Billed'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_awp_percentage:
		model:
			type: 'decimal'
			rounding: 0.00001
		view:
			columns: 4
			label: 'Total AWP -%'
			class: 'numeral discount'
			format: 'percent'
			readonly: true

	claim_payments:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_835_pmt'
		view:
			label: 'Claim Payments'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*]'

	# Claim Adjustments
	claim_adjustments:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_835_adj'
		view:
			label: 'Claim Adjustments'
			readonly: true
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].claimAdjustments'

	# Service Lines
	service_lines:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_835_sl'
		view:
			label: 'Service Lines'
			readonly: true
			grid:
				fields: ['line_item_control_number', 'adjudicated_procedure_code', 'line_item_charge_amount', 'line_item_provider_payment_amount', 'service_date']
				label: ['Line #', 'Procedure', 'Charge', 'Paid', 'Service Date']
				width: [15, 25, 20, 20, 20]
			_meta:
				path: 'transactions[*].detailInfo[*].paymentInfo[*].serviceLines'

	response_id:
		model:
			type: 'int'
			required: true
		view:
			label: 'Response ID'
			readonly: true
			offscreen: true

	embed_batch:
		model:
			sourcefilter:
				response_id:
					'dynamic': '{response_id}'
			multi: false
		view:
			embed:
				query: 'med_claim_resp_835_batch'
				selectable: false
			grid:
				fields: ['production_date', 'check_or_eft_trace_number', 'total_actual_provider_payment_amount']
				label: ['Date', 'Check/EFT Trace #', 'Total Payment Amount']
				width: [20, 50, 30]

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: 'Claim Payment Advice Response (835)'
	indexes:
		unique: [
			['response_id']
		]
		many: [
			['response_id']
			['claim_no']
			['patient_id']
			['payer_id']
			['patient_control_number']
			['claim_status_code']
		]
	sections_group: [
		'Claim Payment Advice Response (835)':
			hide_header: true
			sections: [
				'Claim Information':
					tab: 'Summary'
					fields: ['claim_no', 'patient_id', 'site_id', 'payer_id',
					'claim_statement_period_start', 'claim_statement_period_end', 'coverage_expiration_date',
					'claim_received_date', 'group_number', 'authorization_number', 'prior_authorization_number',
					'claim_status_code', 'patient_control_number','patient_last_name', 'patient_first_name',
					'patient_middle_name', 'patient_suffix', 'patient_member_id', 'subscriber_last_name',
					'subscriber_first_name', 'subscriber_member_id','rendering_provider_name', 'rendering_provider_npi']
				'Totals':
					tab: 'Summary'
					fields: ['total_charge_amount', 'total_paid_amount', 'total_adjusted_amount', 'total_pt_pay',
					'total_cost', 'total_profit', 'margin', 'total_awp', 'total_awp_percentage']
				'Payments':
					tab: 'Summary'
					fields: ['claim_payments']
				'Claim Adjustments':
					tab: 'Summary'
					fields: ['claim_adjustments']
				'Service Lines':
					tab: 'Service Lines'
					fields: ['service_lines']
				'Batch Response':
					tab: 'Summary'
					fields: ['embed_batch']
				'Reviewed':
					hide_header: true
					tab: 'Summary'
					fields: ['reviewed', 'reviewed_by', 'notes']
				'Supplemental Information':
					tab: 'Supplemental'
					fields: ['coverage_amount', 'discount_amount', 'per_day_limit', 'pt_amount_paid', 'negative_balance',
					'covered_actual_quantity', 'co_insured_actual_quantity', 'non_covered_estimated_quantity',
					'visits_actual_quantity', 'prescriptions_quantity']
				'Other Subscriber':
					tab: 'Supplemental'
					fields: ['osub_organization_name', 'osub_member_id', 'osub_tax_id', 'osub_last_name',
					'osub_first_name', 'osub_middle_name', 'osub_suffix']
				'Crossover Payer':
					hide_header: true
					tab: 'Crossover Payer'
					fields: ['cx_payer_id', 'cx_organization_name', 'cx_bcbs_plan_id', 'cx_tax_id', 'cx_naci_plan_id',
					'cx_payer_id_text', 'cx_pharmacy_process_no', 'cx_cms_plan_id']
				'Priority Corrected Payer':
					hide_header: true
					tab: 'Corrected Payer'
					fields: ['cr_payer_id', 'cr_organization_name', 'cr_bcbs_plan_id', 'cr_tax_id', 'cr_naci_plan_id',
					'cr_payer_id_text', 'cr_pharmacy_process_no', 'cr_cms_plan_id']
				'Medicare':
					tab: 'Medicare'
					fields: ['mcr_reimbursement_rate', 'mcr_hcpcs_payable_amount', 'rmk_cd']
				'Claim Contact Information':
					hide_header: true
					tab: 'Support'
					fields: ['claim_contact_name']
				'Claim Contact Methods':
					hide_header: true
					tab: 'Support'
					fields: ['claim_contact_methods']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '85%'
	hide_cardmenu: true
	comment: 'Claim Payment Advice Response (835)'
	find:
		basic: ['patient_id', 'payer_id', 'site_id']
	grid:
		fields: ['total_charge_amount', 'total_paid_amount', 'total_adjusted_amount', 'total_pt_pay', 'total_profit']
		label: ['Total Charge $', 'Total Paid $', 'Total Adjusted $', 'Total Patient Pay $', 'Total Profit $']
		width: [20, 20, 20, 20, 20]
		sort: ['-created_on']
	label: 'Claim Payment Advice Response (835)'
	open: 'read'