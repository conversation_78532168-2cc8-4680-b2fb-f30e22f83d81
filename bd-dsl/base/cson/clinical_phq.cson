fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Questionnaire
	phq_doing_things:
		model:
			source: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day']
		view:
			control: 'radio'
			label: "Little interest or pleasure in doing things?"
			validate: [
					name: 'PHQScoreValidate'
			]

	phq_depressed:
		model:
			source: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day']
		view:
			control: 'radio'
			label: "Feeling down, depressed, or hopeless?"
			validate: [
					name: 'PHQScoreValidate'
			]

	phq_sleeping:
		model:
			source: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day']
		view:
			control: 'radio'
			label: "Trouble falling or staying asleep, or sleeping too much?"
			validate: [
					name: 'PHQScoreValidate'
			]

	phq_tired:
		model:
			source: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day']
		view:
			control: 'radio'
			label: "Feeling tired or having little energy?"
			validate: [
					name: 'PHQScoreValidate'
			]

	phq_appetite:
		model:
			source: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day']
		view:
			control: 'radio'
			label: "Poor appetite or overeating?"
			validate: [
					name: 'PHQScoreValidate'
			]

	phq_selfimage:
		model:
			source: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day']
		view:
			control: 'radio'
			label: "Feeling bad about yourself - or that you are a failure or have let yourself or your family down?"
			validate: [
					name: 'PHQScoreValidate'
			]

	phq_concentrating:
		model:
			source: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day']
		view:
			control: 'radio'
			label: "Trouble concentrating on things, such as reading the newspaper or watching television?"
			validate: [
					name: 'PHQScoreValidate'
			]

	phq_moving_change:
		model:
			source: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day']
		view:
			control: 'radio'
			label: "Moving or speaking so slowly that other people could have noticed? Or the opposite - being so fidgety or restless that you have been moving around a lot more than usual?"
			validate: [
					name: 'PHQScoreValidate'
			]

	phq_suicidal:
		model:
			source: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day']
		view:
			control: 'radio'
			label: "Thoughts that you would be better off dead, or of hurting yourself in some way?"
			validate: [
					name: 'PHQScoreValidate'
			]

	score:
		model:
			min: 0
			max: 130
			type: 'int'
		view:
			note: 'Depression Severity: 0-4 none, 5-9 mild, 10-14 moderate, 15-19 moderately severe, 20-27 severe'
			label: 'Assessment Score'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_phq:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Questionnaire':
			fields: ['phq_doing_things', 'phq_depressed', 'phq_sleeping', 'phq_tired', 'phq_appetite', 'phq_selfimage', 'phq_concentrating', 'phq_moving_change', 'phq_suicidal', 'score']
			prefill: 'clinical_phq'
view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Patient Health Questionnaire'
	label: 'Patient Health/Depression Questionnaire (PHQ-9)'
