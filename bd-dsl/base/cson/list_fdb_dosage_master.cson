#TABLE RPEIDM0_DOSAGE_FORM_MSTR
fields:
    code:
        model:
            max: 64
            required: true
        view:
            label: 'Code'

    #DOSAGE_FORM_ID
    dosage_form_id:
        model:
            type: 'int'
        view:
            label: 'Dosage Form ID'
            findunique: true
            readonly: true
            columns: 3

    #DOSAGE_FORM_DESC_SHORT
    dosage_form_desc_short:
        model:
            type: 'text'
        view:
            label: 'Short Description'
            columns: 3

    #DOSAGE_FORM_DESC_LONG
    dosage_form_desc_long:
        model:
            type: 'text'
        view:
            label: 'Long Description'
            columns: 3

    #DOSAGE_FORM_RETIRE_DT
    dosage_form_retire_dt:
        model:
            type: 'date'
        view:
            label: 'Retire Date'
            columns: 3

    #UOM_MSTR_ID
    uom_mstr_id:
        model:
            type: 'int'
        view:
            label: 'UOM Master ID'
            columns: 3

model:
    access:
        create:     []
        create_all: ['admin']
        delete:     ['admin']
        read:       ['admin']
        read_all:   ['admin']
        request:    []
        update:     []
        update_all: ['admin']
        write:      ['admin']
    bundle: ['reference']
    sync_mode: 'full'
    name: "{dosage_form_id} - {dosage_form_desc_short}"
    indexes:
        many: [
            ['dosage_form_id']
            ['uom_mstr_id']
        ]
    sections:
        'Details':
            hide_header: true
            indent: false
            fields: ['dosage_form_id', 'dosage_form_desc_short',
            'dosage_form_desc_long', 'dosage_form_retire_dt', 'uom_mstr_id']

view:
    comment: 'Manage > List FDB Dosage Form Master'
    find:
        basic: ['dosage_form_id', 'dosage_form_desc_short']
    grid:
        fields: ['dosage_form_id', 'dosage_form_desc_short', 'dosage_form_desc_long', 'dosage_form_retire_dt']
    label: 'List FDB Dosage Form Master'
