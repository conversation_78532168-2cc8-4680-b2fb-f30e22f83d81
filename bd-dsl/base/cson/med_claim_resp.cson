fields:

	response_id:
		model:
			type: 'int'
		view:
			label: 'Response ID'
			readonly: true
			offscreen: true

	claim_no:
		view:
			label: 'Claim #'
			note: 'Claim # against the invoice'
			readonly: true
			offscreen: true

	patient_id:
		model:
			type: 'int'
			required: true
			source: 'patient'
		view:
			label: 'Patient ID'
			readonly: true
			offscreen: true

	request_raw_x12:
		view:
			control: 'raw'
			label: 'Raw X12 response to Change Change Healthcare'
			readonly: true

	request_raw_json:
		model:
			type: 'json'
		view:
			label: 'Raw Json request to Change Change Healthcare'
			readonly: true
			class: 'json-viewer'

	response_raw_x12:
		view:
			control: 'raw'
			label: 'Raw X12 response from Change Change Healthcare'
			readonly: true
	
	response_raw_json:
		model:
			type: 'json'
		view:
			label: 'Raw Json response from Change Change Healthcare'
			readonly: true
			class: 'json-viewer'

	response_type:
		model:
			source: ['Validation', 'Submission']
		view:
			label: 'Response Type'
			readonly: true
			offscreen: true

	control_number:
		view:
			columns: 4
			label: 'Control Number'
			readonly: true

	trading_partner_service_id:
		view:
			columns: 4
			label: 'Trading Partner Service ID'
			readonly: true

	status:
		view:
			class: 'status'
			columns: 4
			label: 'Status'
			readonly: true

	edit_status:
		view:
			class: 'status'
			columns: 4
			note: 'If SUCCESS, claim was forwarded to the payer for review'
			label: 'Edit Status'
			readonly: true

	payer:
		model:
			type: 'subform'
			multi: false
			source: 'med_claim_resp_ch_pyr'
		view:
			label: 'Payer'
			readonly: true

	claim_reference:
		model:
			type: 'subform'
			multi: false
			source: 'med_claim_resp_ref'
		view:
			label: 'Claim Reference'
			readonly: true

	errors:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_err'
		view:
			label: 'Errors'
			readonly: true

	edit_responses:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_edit'
		view:
			label: 'Edit Responses'
			readonly: true

	failure:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_fail'
		view:
			label: 'Failures'
			readonly: true

	response_meta:
		model:
			type: 'subform'
			multi: false
			source: 'med_claim_resp_ch_meta'
		view:
			label: 'Response Meta Data'
			readonly: true

	raw:
		model:
			type: 'json'
			multi: false
		view:
			label: 'Raw Response'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['response_type']
	indexes:
		many: [
			['claim_no']
		]
	sections_group: [
		'Clearinghouse Response':
			hide_header: true
			sections: [
				'Header':
					tab: 'Header'
					hide_header: true
					fields: ['claim_no', 'control_number', 'trading_partner_service_id', 'status', 'edit_status']
				'Payer':
					tab: 'Header'
					hide_header: true
					indent: false
					fields: ['payer']
				'Claim Reference':
					tab: 'Reference'
					hide_header: true
					indent: false
					fields: ['claim_reference']
				'Errors':
					tab: 'Errors'
					hide_header: true
					indent: false
					fields: ['errors']
				'Edit Responses':
					tab: 'Edit Responses'
					hide_header: true
					indent: false
					fields: ['edit_responses']
				'Failures':
					tab: 'Failures'
					hide_header: true
					indent: false
					fields: ['failure']
				'Meta Data':
					tab: 'Meta Data'
					hide_header: true
					indent: false
					fields: ['response_meta']
				'Raw Request':
					hide_header: true
					indent: false
					tab: 'Raw'
					fields: ['request_raw_x12', 'request_raw_json']
				'Raw Response':
					hide_header: true
					indent: false
					tab: 'Raw'
					fields: ['response_raw_x12', 'response_raw_json']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Clearinghouse Response (999)'
	grid:
		fields: ['response_type', 'status', 'edit_status']
		label: ['Response Type', 'Status', 'Edit Status']
		width: [30, 35, 35]
		sort: ['-created_on']
	label: 'Clearinghouse Response (999)'
	open: 'read'