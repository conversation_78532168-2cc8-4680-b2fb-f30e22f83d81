#TABLE: RADIMMA5_MSTR

fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#DDI_CODEX
	ddi_codex:
		model:
			type: 'int'
		view:
			label: 'Expanded Interaction Code'
			readonly: true
			columns: 3

	#DDI_DES
	ddi_des:
		view:
			label: 'Interaction Description'
			readonly: true
			columns: 3

	#DDI_SL
	ddi_sl:
		view:
			label: 'Interaction Severity Level'
			readonly: true
			columns: 3

	#DDI_MONOX
	ddi_monox:
		model:
			type: 'int'
		view:
			label: 'Interaction Expanded Monograph Number'
			readonly: true
			columns: 3

	#DDI_PGEDI
	ddi_pgedi:
		view:
			label: 'Interaction Page References EDI'
			readonly: true
			columns: 3

	#DDI_TREE
	ddi_tree:
		model:
			type: 'int'
		view:
			label: 'This column is not currently being used.'
			readonly: true
			columns: 3

	#DDI_MFGI
	ddi_mfgi:
		view:
			label: 'Manufacturer Info'
			readonly: true
			columns: 3

	#DDI_TRIALI
	ddi_triali:
		view:
			label: 'Human Clinical Trial'
			readonly: true
			columns: 3

	#DDI_CASEI
	ddi_casei:
		view:
			label: 'Case Reports'
			readonly: true
			columns: 3

	#DDI_ABSI
	ddi_absi:
		view:
			label: 'Meeting Abstract'
			readonly: true
			columns: 3

	#DDI_IVASI
	ddi_ivasi:
		view:
			label: 'In Vitro/Animal Study'
			readonly: true
			columns: 3

	#DDI_REVI
	ddi_revi:
		view:
			label: 'Review'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{ddi_des}"
	indexes:
		many: [
			['ddi_codex']
		]
	sections:
		'Information':
			fields: ['ddi_codex', 'ddi_des', 'ddi_sl', 'ddi_monox', 'ddi_pgedi', 'ddi_tree']
		'Interaction Reference Category Indicator':
			fields: ['ddi_mfgi', 'ddi_triali', 'ddi_casei', 'ddi_absi', 'ddi_ivasi', 'ddi_revi']

view:
	comment: 'Manage > List FDB Interaction Master Table'
	find:
		basic: ['ddi_codex']
	grid:
		fields: ['ddi_codex', 'ddi_des', 'ddi_sl', 'ddi_monox', 'ddi_pgedi', 'ddi_mfgi', 'ddi_triali', 'ddi_casei', 'ddi_absi', 'ddi_ivasi', 'ddi_revi']
	label: 'List FDB Interaction Master Table'
