fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_moqlq.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	fractures:
		model:
			required: true

			max: 64
			source: ['All of the time', 'Most of the time', 'A good bit of the time',
			'Some of the time', 'A little of the time', 'Hardly any of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'How often in the last 2 weeks has the patient felt afraid of fractures?'

	falling:
		model:
			required: true

			max: 64
			source: ['All of the time', 'Most of the time', 'A good bit of the time',
			'Some of the time', 'A little of the time', 'Hardly any of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'How often in the last 2 weeks has the patient felt afraid of falling?'

	carry:
		model:
			required: true

			max: 64
			source: ['Extremely difficult - impossible to do', 'Very difficult - almost impossible', 'Quite a bit difficult',
			'Moderately difficult', 'Somewhat difficult', 'A little difficult', 'Not difficult', 'Not applicable']
		view:
			control: 'radio'
			label: 'How difficult has it been for the patient to carry things in the last 2 weeks because of his/her back problems due to osteoporosis?'

	distress_pain:
		model:
			required: true

			max: 64
			source: ['Extreme distress or discomfort', 'Very much distress or discomfort', 'Quite a bit of distress or discomfort',
			'Moderate distress or discomfort', 'Some distress or discomfort', 'A little distress or discomfort', 'No distress or discomfort']
		view:
			control: 'radio'
			label: 'How much distress or discomfort has the patient had because of pain in the last 2 weeks?'

	lift:
		model:
			required: true

			max: 64
			source: ['Extremely difficult - impossible to do', 'Very difficult - almost impossible', 'Quite a bit difficult',
			'Moderately difficult', 'Somewhat difficult', 'A little difficult', 'Not difficult', 'Not applicable']
		view:
			control: 'radio'
			label: 'How difficult has it been for the patient to lift things in the last 2 weeks?'

	travel:
		model:
			required: true

			max: 64
			source: ['Extremely difficult - impossible to do', 'Very difficult - almost impossible', 'Quite a bit difficult',
			'Moderately difficult', 'Somewhat difficult', 'A little difficult', 'Not difficult', 'Not applicable']
		view:
			control: 'radio'
			label: 'How difficult has it been for the patient to travel in the last 2 weeks?'

	housework:
		model:
			required: true

			max: 64
			source: ['Extremely difficult - impossible to do', 'Very difficult - almost impossible', 'Quite a bit difficult',
			'Moderately difficult', 'Somewhat difficult', 'A little difficult', 'Not difficult', 'Not applicable']
		view:
			control: 'radio'
			label: 'How difficult has it been for the patient to do housework in the last 2 weeks?'

	vacation:
		model:
			required: true

			max: 64
			source: ['Extremely difficult - impossible to do', 'Very difficult - almost impossible', 'Quite a bit difficult',
			'Moderately difficult', 'Somewhat difficult', 'A little difficult', 'Not difficult', 'Not applicable']
		view:
			control: 'radio'
			label: 'How difficult has it been for the patient to take the type of vacation or holiday he/she enjoys because of his/her back problems due to osteoporosis?'

	distress:
		model:
			required: true

			max: 64
			source: ['Extreme distress or discomfort', 'Very much distress or discomfort', 'Quite a bit of distress or discomfort',
			'Moderate distress or discomfort', 'Some distress or discomfort', 'A little distress or discomfort', 'No distress or discomfort']
		view:
			control: 'radio'
			label: 'How much distress or discomfort has the patient had in the last 2 weeks because it has been painful to stand for a long time?'

	vacuum:
		model:
			required: true

			max: 64
			source: ['Extremely difficult - impossible to do', 'Very difficult - almost impossible', 'Quite a bit difficult',
			'Moderately difficult', 'Somewhat difficult', 'A little difficult', 'Not difficult', 'Not applicable']
		view:
			control: 'radio'
			label: 'How difficult has it been for the patient to vacuum in the last 2 weeks?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_moqlq:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Mini-Osteoporosis Quality of Life Questionnaire (mini-OQLQ)':
			fields: ['last_assessment_date', 'assessment_date']
		'Questionnaire':
			fields: ['fractures', 'falling', 'carry', 'distress_pain', 'lift', 'travel',
			'housework', 'vacation', 'distress', 'vacuum']
			prefill: 'clinical_moqlq'
view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Mini-Osteoporosis Quality of Life Questionnaire (mini-OQLQ)'
	find:
		basic: ['assessment_date']
	grid:
		fields: ['created_on', 'assessment_date', 'created_by']
		sort: ['-id']
	label: 'Mini-Osteoporosis Quality of Life Questionnaire (mini-OQLQ)'
