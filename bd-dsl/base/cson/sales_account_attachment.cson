fields:
	# Links
	sales_account_id:
		model:
			required: true
			source: 'sales_account'
			type: 'int'

	flag:
		model:
			multi: true
			source: 'list_flag'
		view:
			label: 'Flags'

	file:
		model:
			required: true
			type: 'json'
		view:
			control: 'file'
			label: 'Document / File'
			note: 'Max 100MB. Only documents, images, and archives supported.'

	comments:
		model:
			max: 1024
		view:
			control: 'area'
			label: 'Comments'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison']
		delete:     ['admin']
		read:       ['admin', 'csr', 'liaison']
		read_all:   ['admin', 'csr', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'liaison']
		write:      ['admin', 'csr', 'liaison']
	indexes:
		many: [
			['sales_account_id']
		]
	name: ['sales_account_id', 'flag']
	sections:
		'Main':
			fields: ['flag', 'file', 'comments']

view:
	comment: 'Account > File Attachments'
	find:
		basic: ['flag']
	grid:
		fields: ['created_on', 'created_by', 'file', 'flag', 'comments']
		sort: ['-id']
	label: 'Account File Attachment'
