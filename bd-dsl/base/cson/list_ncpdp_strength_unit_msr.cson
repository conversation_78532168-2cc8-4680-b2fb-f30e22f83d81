fields:

	code:
		model:
			max: 64
			required: true
		view:
			columns: 2
			label: 'Code'
			findunique: true
			readonly: true

	name:
		model:
			required: true
			max: 128
		view:
			columns: 2
			label: 'Name'
			findunique: true
			readonly: true

	unit_id:
		model:
			source: 'list_unit'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Mapped Unit'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: '{code} - {name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'unit_id']

view:
	comment: 'Manage > NCPDP Strength Unit Measurement Code'
	find:
		basic: ['code','name','unit_id']
	grid:
		fields: ['code','name','unit_id']
		sort: ['code']
	label: 'NCPDP Strength Unit Measurement Code'
	open: 'read'
