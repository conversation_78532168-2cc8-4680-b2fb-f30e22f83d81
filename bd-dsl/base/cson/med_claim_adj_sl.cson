fields:
	adjustment_group_code:
		model:
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CAS01'
		view:
			columns: 3
			label: 'Adjustment Group Code'
			reference: 'CAS01'
			_meta:
				location: '2430 CAS'
				field: '01'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].claimAdjustmentInformation[{idx1-5}].adjustmentGroupCode'

	adjustment_details:
		model:
			required: true
			multi: true
			source: 'med_claim_adj_sl_dl'
			type: 'subform'
		view:
			note: 'Max 6'
			label: 'Adjustment Details'
			grid:
				add: 'inline'
				edit: true
				fields: ['adjustment_reason_code', 'adjustment_amount', 'adjustment_quantity']
				label: ['Reason', 'Amount', 'Quantity']
				width: [60, 20, 20]
			max_count: 6

model:
	prefill:
		patient:
			link:
				id: 'patient_id'
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['adjustment_group_code']
		]

	name:['adjustment_group_code']
	sections_group: [
		'Service Line Adjustment':
			hide_header: true
			sections: [
				'Group Code':
					hide_header: true
					fields: ['adjustment_group_code']
				'Details':
					indent: false
					hide_header: true
					fields: ['adjustment_details']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	reference: '2320'
	comment: 'Service Line Adjustment'
	grid:
		fields: ['adjustment_group_code']
		sort: ['-created_on']
	label: 'Service Line Adjustment'
	open: 'read'