fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	medicare_hits:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Medicare HITS?'

	comment:
		view:
			control: 'area'
			label: 'Comments'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'pharm']
		delete:     ['admin', 'csr', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'pharm']
	name: '{patient_id} {careplan_id} ({medicate_hit})'
	sections:
		'Intake Medicare Assessment':
			fields: ['medicare_hits']
		'Comments':
			fields: ['comment']

view:
	hide_cardmenu: true
	comment: 'Patient > Intake Medicare Assessment'
	grid:
		fields: ['created_on', 'created_by', 'medicare_hits', 'comment']
	label: 'Intake Medicare Assessment'
	open: 'read'
