fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'
			findunique: true
			columns: 2

	name:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 2

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 2

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['lists']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['code']
		]
	name: '{code} - {name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'allow_sync', 'active']

view:
	comment: 'Manage > Payer Required Document'
	find:
		basic: ['code','name','active']
	grid:
		fields: ['code','name','active']
		sort: ['code']
	label: 'Payer Required Document'
	open: 'read'
