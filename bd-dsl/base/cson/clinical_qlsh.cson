fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_sibdq.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	important_stress:
		model:
			required: true

			max: 64
			source: ['Not important', 'Slightly important', 'Moderately important', 'Very important', 'Extremely important']
		view:
			control: 'radio'
			label: 'Ability to handle stress'

	important_appearance:
		model:
			required: true

			max: 64
			source: ['Not important', 'Slightly important', 'Moderately important', 'Very important', 'Extremely important']
		view:
			control: 'radio'
			label: 'Body shape/ appearance'

	important_confidence:
		model:
			required: true

			max: 64
			source: ['Not important', 'Slightly important', 'Moderately important', 'Very important', 'Extremely important']
		view:
			control: 'radio'
			label: 'Self-confidence'

	important_sex:
		model:
			required: true

			max: 64
			source: ['Not important', 'Slightly important', 'Moderately important', 'Very important', 'Extremely important']
		view:
			control: 'radio'
			label: 'Ability to become sexually aroused'

	important_concentration:
		model:
			required: true

			max: 64
			source: ['Not important', 'Slightly important', 'Moderately important', 'Very important', 'Extremely important']
		view:
			control: 'radio'
			label: 'Ability to concentrate'

	important_endurance:
		model:
			required: true

			max: 64
			source: ['Not important', 'Slightly important', 'Moderately important', 'Very important', 'Extremely important']
		view:
			control: 'radio'
			label: 'Physical endurance'

	important_drive:
		model:
			required: true

			max: 64
			source: ['Not important', 'Slightly important', 'Moderately important', 'Very important', 'Extremely important']
		view:
			control: 'radio'
			label: 'Initiative / drive'

	important_anger:
		model:
			required: true

			max: 64
			source: ['Not important', 'Slightly important', 'Moderately important', 'Very important', 'Extremely important']
		view:
			control: 'radio'
			label: 'Your ability to deal with anger'

	important_coping:
		model:
			required: true

			max: 64
			source: ['Not important', 'Slightly important', 'Moderately important', 'Very important', 'Extremely important']
		view:
			control: 'radio'
			label: 'Being able to stand the disturbances and noise of everyday life'

	satisfied_stress:
		model:
			required: true

			max: 64
			source: ['Dissatisfied', 'Slightly dissatisfied', 'Slightly satisfied', 'Moderately satisfied', 'Very satisfied']
		view:
			control: 'radio'
			label: 'Ability to handle stress'

	satisfied_appearance:
		model:
			required: true

			max: 64
			source: ['Dissatisfied', 'Slightly dissatisfied', 'Slightly satisfied', 'Moderately satisfied', 'Very satisfied']
		view:
			control: 'radio'
			label: 'Body shape/ appearance'

	satisfied_confidence:
		model:
			required: true

			max: 64
			source: ['Dissatisfied', 'Slightly dissatisfied', 'Slightly satisfied', 'Moderately satisfied', 'Very satisfied']
		view:
			control: 'radio'
			label: 'Self-confidence'

	satisfied_sex:
		model:
			required: true

			max: 64
			source: ['Dissatisfied', 'Slightly dissatisfied', 'Slightly satisfied', 'Moderately satisfied', 'Very satisfied']
		view:
			control: 'radio'
			label: 'Ability to become sexually aroused'

	satisfied_concentration:
		model:
			required: true

			max: 64
			source: ['Dissatisfied', 'Slightly dissatisfied', 'Slightly satisfied', 'Moderately satisfied', 'Very satisfied']
		view:
			control: 'radio'
			label: 'Ability to concentrate'

	satisfied_endurance:
		model:
			required: true

			max: 64
			source: ['Dissatisfied', 'Slightly dissatisfied', 'Slightly satisfied', 'Moderately satisfied', 'Very satisfied']
		view:
			control: 'radio'
			label: 'Physical endurance'

	satisfied_drive:
		model:
			required: true

			max: 64
			source: ['Dissatisfied', 'Slightly dissatisfied', 'Slightly satisfied', 'Moderately satisfied', 'Very satisfied']
		view:
			control: 'radio'
			label: 'Initiative / drive'

	satisfied_anger:
		model:
			required: true

			max: 64
			source: ['Dissatisfied', 'Slightly dissatisfied', 'Slightly satisfied', 'Moderately satisfied', 'Very satisfied']
		view:
			control: 'radio'
			label: 'Your ability to deal with anger'

	satisfied_coping:
		model:
			required: true

			max: 64
			source: ['Dissatisfied', 'Slightly dissatisfied', 'Slightly satisfied', 'Moderately satisfied', 'Very satisfied']
		view:
			control: 'radio'
			label: 'Being able to stand the disturbances and noise of everyday life'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_qlsh:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Questions on Life Satisfaction – Hypopituitarism (QLS-H) Participation':
			fields: ['last_assessment_date', 'assessment_date']
		'Importance':
			note: "How important are the following areas to the patient's health"
			fields: ['important_stress', 'important_appearance', 'important_confidence',
			'important_sex', 'important_concentration', 'important_endurance', 'important_drive',
			'important_anger', 'important_coping']
			prefill: 'clinical_qlsh'
		'Satisfaction':
			note: "How satisfied is the patient with the following areas, over the least four weeks:"
			fields: ['satisfied_stress', 'satisfied_appearance', 'satisfied_confidence',
			'satisfied_sex', 'satisfied_concentration', 'satisfied_endurance', 'satisfied_drive',
			'satisfied_anger', 'satisfied_coping']
			prefill: 'clinical_qlsh'
view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Questions on Life Satisfaction – Hypopituitarism (QLS-H)'
	find:
		basic: ['assessment_date']
	grid:
		fields: ['created_on', 'assessment_date', 'created_by']
		sort: ['-id']
	label: 'Questions on Life Satisfaction – Hypopituitarism (QLS-H)'
