fields:
	code:
		model:
			max: 128
			required: true
		view:
			label: 'Code'
			findunique: true
			columns: 3

	name:
		model:
			max: 256
			required: true
		view:
			label: 'Description'
			findunique: true
			columns: 3

	label_string:
		model:
			required: true
			max: 256
		view:
			label: 'Label'
			note: 'Added to the label directions'
			columns: 3

	multiplier:
		model:
			type: 'decimal'
			rounding: 0.0000001
			required: true
		view:
			label: 'Multiplier'
			note: 'times per therapy day'
			columns: 3

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 3

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ['dispense']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['code']
		]
	name: ['code', 'name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code','name', 'label_string', 'multiplier', 'allow_sync', 'active']

view:
	comment: 'Manage > Frequency'
	find:
		basic: ['code']
	grid:
		fields: ['code','name','multiplier']
		sort: ['code']
	label: 'Frequency'
