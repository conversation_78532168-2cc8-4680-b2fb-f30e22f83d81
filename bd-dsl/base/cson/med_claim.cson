fields:

	uuid:
		view:
			label: 'UUID'
			readonly: true
			offscreen: true

	invoice_no:
		view:
			label: 'Invoice #'
			readonly: true
			offscreen: true

	is_test:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Test Claim?"
			readonly: true
			offscreen: true

	parent_claim_no:
		view:
			label: 'Parent Claim #'
			note: 'For COB Scenario'
			readonly: true
			offscreen: true

	claim_no:
		view:
			label: 'Claim #'
			readonly: true
			offscreen: true

	usage_indicator:
		model:
			default: 'P'
			source:
				'P': 'Production'
				'T': 'Test'
		view:
			control: 'radio'
			reference: 'ISA15'
			label: "Usage Indicator"
			_meta:
				location: 'ISA15'
				path: 'usageIndicator'
			readonly: true
			offscreen: true

	service_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 4
			label: 'Service Date'
			note: 'MM/DD/YYYY'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	site_id:
		model:
			type: 'int'
			required: true
			source: 'site'
			prefill: ['parent.site_id']
		view:
			columns: 2
			label: 'Site'
			readonly: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/site/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'pay_to_address':
						'type': 'subform'
						'field': 'pay_to_address'
						'fields':
							'address1': ['bill_address1']
							'address2': ['bill_address2']
							'city': ['bill_city']
							'state': ['bill_state_id']
							'postal_code': ['bill_zip']
			]

	patient_id:
		model:
			type: 'int'
			required: true
			source: 'patient'
		view:
			columns: 2
			label: 'Patient'
			readonly: true

	insurance_id:
		model:
			type: 'int'
			required: true
			source: 'patient_insurance'
			prefill: ['parent.insurance_id']
		view:
			columns: 2
			label: 'Insurance'
			readonly: true

	payer_id:
		model:
			type: 'int'
			required: true
			source: 'payer'
			prefill: ['parent.payer_id']
		view:
			columns: 2
			label: 'Payer'
			readonly: true

	organization_name:
		model:
			required: true
		view:
			columns: 2
			label: 'Payer Organization Name'
			readonly: true
			offscreen: true

	control_number:
		model:
			required: false
			max: 9
			min: 9
		view:
			columns: 4
			note: 'Unique identifier assigned by Clara'
			label: 'Control Number'
			reference: 'ST02'
			_meta:
				location: 'Header ST02'
				path: 'controlNumber'
			readonly: true

	status:
		model:
			required: true
			source: ["Validated", 'Sent', 'Processing',
			'Received', 'Reversed', 'Accepted', 'Forwarded',
			'Rejected', 'Revised', 'Warning', 'Denied',
			'On-hold', 'Error', 'Awaiting Requested Information',
			'Request for Additional Information', 'Under Review',
			'Partially Paid', 'Margin', 'Payable']
		view:
			class: 'status'
			columns: 2
			label: 'Status'
			readonly: true

	substatus_id:
		model:
			source: 'list_billing_csstatus'
			sourceid: 'code'
			track: true
			sourcefilter:
				status_id:
					'dynamic': '{status_id}'
		view:
			columns: 2
			control: 'select'
			label: 'Claim Substatus'

	trading_partner_name:
		model:
			required: true
			min: 1
			max: 60
		view:
			columns: -2
			label: 'Trading Partner Name'
			reference: '010BB-NM103'
			_meta:
				location: '2010BB NM1'
				field: '03'
				path: 'tradingPartnerName'

	trading_partner_service_id:
		model:
			min: 2
			max: 80
		view:
			columns: 2
			label: 'Trading Partner Service ID'
			reference: '2010BB-NM109'
			_meta:
				location: '2010BB NM1'
				field: '09'
				path: 'tradingPartnerServiceId'

	receiver:
		model:
			required: true
			type: 'subform'
			multi: false
			source: 'med_claim_receiver'
		view:
			label: 'Payer'
			reference: '1000B'

	submitter:
		model:
			required: true
			type: 'subform'
			multi: false
			source: 'med_claim_submitter'
		view:
			label: 'Submitter'
			reference: '1000A'

	pay_to_address:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_address_pay'
		view:
			label: 'Pay-To Address'
			reference: '2010AB'

	subscriber:
		model:
			required: true
			type: 'subform'
			multi: false
			source: 'med_claim_subscriber'
		view:
			label: 'Subscriber'
			reference: '2000B'

	dependent_required:
		model:
			default: 'No'
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['dependent']
					sections: ['Dependent']
		view:
			label: 'Dependent Required?'
			validate: [
				name: 'LoadDependentInformation'
			]
			offscreen: true
			readonly: true

	dependent:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_dep'
		view:
			label: 'Dependent'
			reference: '2010CA'

	claim_information:
		model:
			required: true
			type: 'subform'
			multi: false
			source: 'med_claim_info'
		view:
			label: 'Claim'
			reference: '2300'

	billed:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Billed'
			readonly: true

	expected:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Expected'
			readonly: true

	paid:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Paid'
			readonly: true

	copay:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Copay'
			readonly: true

	tax:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			label: 'Tax'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			offscreen: true

	providers:
		model:
			required: true
			type: 'subform'
			multi: false
			source: 'med_claim_provs'
		view:
			label: 'Providers'

	ch_responses:
		model:
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				claim_no:
					'dynamic': '{claim_no}'
		view:
			embed:
				form: 'med_claim_resp'
				selectable: false
			grid:
				add: 'none'
				rank: 'none'
				edit: false
				fields: ['response_type', 'status', 'edit_status']
				label: ['Response Type', 'Status', 'Edit Status']
				width: [30, 35, 35]
			label: 'Clearinghouse Responses'

	payer_status_responses:
		model:
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				claim_no:
					'dynamic': '{claim_no}'
		view:
			embed:
				form: 'med_claim_resp_277'
				selectable: false
			grid:
				add: 'none'
				rank: 'none'
				edit: false
				fields: ['reviewed', 'claim_status_information_effective_date', 'status_code', 'status_code_value']
				label: ['Reviewed?', 'Date', 'Status Code', 'Status Code Value']
				width: [15, 20, 20, 45]
			label: 'Payer Status (277) Responses'

	payer_final_responses:
		model:
			multi: true
			sourcefilter:
				claim_no:
					'dynamic': '{claim_no}'
		view:
			embed:
				form: 'med_claim_resp_835'
				selectable: false
			grid:
				add: 'none'
				rank: 'none'
				edit: false
				fields: ['reviewed', 'claim_status_code', 'total_charge_amount', 'total_paid_amount', 'total_pt_pay']
				label: ['Reviewed?', 'Claim Status', 'Tot Charge Amt', 'Tot Paid Amt', 'Tot Pt Resp Amt']
				width: [15, 20, 20, 20, 20]
			label: 'Payer Final (835) Responses'

	void:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['voided_datetime', 'void_reason_id']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Void Medical Claim?'
			findfilter: '!Yes'
			class: 'checkbox-only'
			readonly: true
			offscreen: true

	void_reason_id:
		model:
			required: true
			source: 'list_void_reason_billing'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			label: 'Void Reason'
			readonly: true
			offscreen: true

	voided_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Voided Date'
			template: '{{now}}'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['site_id']
			['patient_id']
			['insurance_id']
			['payer_id']
		]

	name: '{status} {substatus_id_auto_name} PT:{patient_id_auto_name} PY:{payer_id_auto_name} EX:${expected} PD:${paid}'

	sections_group: [
		'Medical Professional Claim':
			hide_header: true
			sections: [
				'Header':
					hide_header: true
					tab: 'Header'
					fields: ['invoice_no', 'parent_claim_no',
					'usage_indicator', 'service_date', 'control_number', 'site_id', 'patient_id', 'insurance_id', 'payer_id',
					'organization_name', 'status', 'substatus_id', 'trading_partner_name', 'trading_partner_service_id','dependent_required']
				'Payer':
					indent: false
					tab: 'Payer'
					fields: ['receiver']
				'Submitter':
					indent: false
					tab: 'Submitter'
					fields: ['submitter']
				'Pay To Address':
					hide_header: true
					indent: false
					tab: 'Submitter'
					fields: ['pay_to_address']
				'Patient':
					hide_header: true
					indent: false
					tab: 'Patient'
					fields: ['subscriber']
				'Dependent':
					tab: 'Dependent'
					indent: false
					fields: ['dependent']
				'Claim':
					hide_header: true
					indent: false
					tab: 'Claim'
					fields: ['claim_information']
				'Providers':
					hide_header: true
					indent: false
					tab: 'Providers'
					fields: ['providers']
				'ClearingHouse Response':
					tab: 'Responses'
					fields: ['ch_responses']
				'Status Check Response':
					tab: 'Responses'
					fields: ['payer_status_responses']
				'Payer Response':
					tab: 'Responses'
					fields: ['payer_final_responses']
				'Totals':
					hide_header: true
					indent: false
					fields: ['expected', 'billed', 'paid', 'copay', 'tax']
			]
	]

view:
	hide_cardmenu: true
	block:
		validate: [
			name: 'MedClaimBlock'
		]
	comment: 'Medical Claim (837p)'
	find:
		basic: ['site_id', 'patient_id', 'insurance_id', 'payer_id', 'status', 'service_date']
		advanced: ['control_number']
	grid:
		fields: ['service_date', 'status', 'patient_id', 'payer_id']
		width: [20, 20, 30, 30]
		sort: ['-created_on']
	label: 'Medical Claim (837p)'
	open: 'read'