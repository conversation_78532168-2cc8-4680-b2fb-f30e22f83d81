fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	location:
		model:
			required: true
		view:
			label: 'Location'

	# Wound Details
	length:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Length (cm)'

	width:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Width (cm)'

	depth:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Depth (cm)'


	# Wound details
	drainage:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['odor', 'type', 'color', 'amount']
		view:
			control: 'radio'
			label: 'Does the wound have drainage?'

	odor:
		model:
			source: ['Faint', 'Moderate', 'Strong']
		view:
			control: 'radio'
			label: 'Odor'

	type:
		model:
			source: ['Serous', 'Sanguinous', 'Serosanguinous', 'Purulent', 'Seropurulent']
		view:
			control: 'radio'
			label: 'Type'

	color:
		model:
			source: ['Clear', 'Pale Yellow', 'Red', 'Dark Brown', 'Blue-Green']
		view:
			control: 'radio'
			label: 'Color'

	amount:
		model:
			source: ['Desiccated', 'Minimal', 'Moderate', 'Severe']
		view:
			control: 'radio'
			label: 'Amount'

	# Wound Details
	stage:
		model:
			source: ['1', '2', '3', '4']
		view:
			control: 'radio'
			note: 'Stage 1 = A persistent area of skin redness <br>Stage 2 = A partial thickness is loss of skin layers that is superficial and presents clinically as an abrasion, blister or shallow crater<br>Stage 3 = A full thickness of skin is lost, exposing the subcutaneous tissues, which presents clinically as deep craters with or without undermining adjacent tissue.<br>Stage 4 = A full thickness of skin and subcutaneous tissue is lost, exposing muscle and/or bone.'
			label: 'Stage Of Pressure Store'

	eschar:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Eschar'

	slough:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Slough'

	inflammation:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Inflammation'

	undermining:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Undermining'

	treatment:
		model:
			max: 8192
		view:
			control: 'area'
			label: "Wound Treatment"

	comment:
		model:
			max: 4096
		view:
			control: 'area'
			label: "Wound Comment"

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['order_id', 'type', 'amount']
	sections:
		'Wound Location':
			fields: ['location']
		'Wound Measurement':
			fields: ['length', 'width', 'depth']
		'Wound Drainage':
			fields: ['drainage', 'odor', 'type', 'color', 'amount']
		'Wound Details':
			fields: ['stage', 'eschar', 'slough', 'inflammation', 'undermining']
		'Wound Treatment':
			fields: ['treatment', 'comment']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Checklist > Wound > Details'
	grid:
		fields: ['location', 'length', 'width', 'depth', 'stage']
	label: 'Wound Details'
