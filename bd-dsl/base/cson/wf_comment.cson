fields:

	wf_queue_id:
		model:
			required: true
			source: 'wf_queue'
		view:
			label: 'Workflow Queue'

	user_id:
		model:
			source: 'user'
			required: true
		view:
			label: 'Commenter'

	comment:
		model:
			required: true
			type: 'text'
		view:
			control: 'area'
			label: 'Comment'
model:
	access:
		create:     ['admin']
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    ['admin']
		update:     ['admin']
		update_all: ['admin']
		write:      ['admin']
	name: '{user_id_auto_name} - {comment}'
	indexes:
		many: [
			['user_id', 'wf_queue_id']
			['wf_queue_id']
			['user_id']
		]
	sections:
		'Main':
			fields: ['wf_queue_id', 'user_id', 'comment']

view:
	comment: 'Workflow Queue Comments'
	find:
		basic: ['wf_queue_id', 'user_id']
	grid:
		fields: ['wf_queue_id', 'user_id']
	label: 'Workflow Queue Comments'
	open: 'read'
