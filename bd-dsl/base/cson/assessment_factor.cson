fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	patient_screened:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'The patient has been screened and deemed competent to administer factor replacement therapy without medical supervision.'

	preferred_package:
		model:
			prefill: ['assessment_factor']
			multi: true
			source: ['Separate Doses', 'Separate Supply Kits', 'Bulk Supply Bags', 'Dose and Supplies together']
		view:
			control: 'checkbox'
			label: 'Preferred Packaging'

	# Medication Storage Assessment
	medication_storage:
		model:
			source: ['Refrigerated', 'Room Temperature', 'Other']
			if:
				'Other':
					fields: ['medication_storage_other']
		view:
			control: 'radio'
			label: "How do you store your factor?"

	medication_storage_other:
		view:
			label: 'Factor Storage Other'

	check_expiration_reminder:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['check_expiration_reminder_details']
		view:
			control: 'radio'
			label: "Patient reminded to check expiration dates and rotate stock?"

	check_expiration_reminder_details:
		view:
			label: 'Why was the patient not reminded?'

	htc_care:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Does the patient obtain care from an HTC?'

	htc_details:
		view:
			control: 'area'
			label: 'HTC comment:'

	activity_level:
		model:
			source: ['<1%', '1%-5%', '>5%', 'Unknown', 'N/A']
		view:
			control: 'radio'
			label: "What is the patient's current Factor VIII / Factor IX activity level?"

	track_bleeds:
		model:
			max: 3
			required: false
			source: ['No', 'Yes']
		view:
			control: 'radio'
			note: '(bleed log, notebook, phone app, etc.)'
			label: 'Does the patient track their bleeds?'
	
	track_bleeds_comment:
		view:
			control: 'area'
			label: 'Patient Bleed Tracking Comment'

	track_bleeds_how:
		model:
			required: true
		view:
			control: 'area'
			label: 'If so, what method do you use and how often are you tracking them?'

	track_bleeds_why:
		model:
			required: true
		view:
			control: 'area'
			label: 'If not, what is the reason?'
			
	therapy_regimen:
		model:
			multi: false
			source: ['Prophylaxis for Factor Replacement', 'On-Demand', 'Prophylaxis and On-Demand', 'Immune Tolerance Induction', 'Surgery', 'Prophylaxis for Non-Factor Replacement (i.e. Hemlibra)']
		view:
			control: 'checkbox'
			label: 'Therapy Regimen'

	has_hcv:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['has_hcv_details']
		view:
			control: 'radio'
			label: 'Does patient have a history of Hepatitis C?'

	has_hcv_details:
		view:
			control: 'area'
			label: 'Hepatitis C Treatment Details'

	has_hiv:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hiv_drugs', 'has_hiv_details']
		view:
			control: 'radio'
			label: "Does patient have a history of HIV?"

	hiv_drugs:
		model:
			multi: true
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'HIV Drug(s)'

	has_hiv_details:
		view:
			label: "HIV treatment details"
			control: 'area'

	excercise:
		view:
			label: 'Exercise/Activity level'
			control: 'area'

	have_inhibitors_history:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_inhibitors_history_details', 'took_immune_tolerance_treatment']
		view:
			control: 'radio'
			label: "Does the patient have a history of inhibitors?"

	have_inhibitors_history_details:
		view:
			label: "History of inhibitors details"

	took_immune_tolerance_treatment:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['immune_tolerance_comment']
		view:
			control: 'radio'
			label: "Received immune tolerance treatment?"

	immune_tolerance_comment:
		view:
			label: "Immune tolerance comment"

	current_inhib_pt:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is this patient a current inhibitor patient?'

	# Factor Joint Assessment
	target_joints:
		model:
			multi: true
			source: ["Left Knee", "Right Knee", "Left Ankle", "Right Ankle", "Left Hip", "Right Hip", "Left Elbow", "Right Elbow", "Left Hand", "Right Hand", "Left Shoulder", "Right Shoulder", "Head/Neck", "Other", 'N/A']
			if:
				"Other":
					fields: ["target_joints_other"]
		view:
			control: 'checkbox'
			label: "Target Joints"

	target_joints_other:
		view:
			label: "Other Target Joints"

	bleed_history:
		view:
			control: 'area'
			note: 'for the month'
			label: "Patient's Bleeding History"

	dose_tracking:
		model:
			required: false
			multi: true
			subfields:
				field_int:
					label: 'Doses Left'
					type: 'decimal'
					format: '0,0'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				discontinued:
					'static': 'null'
				therapy_id:
					'static': 'factor'
		view:
			embed:
				form: 'careplan_order_rx'
				selectable: false
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['inventory_id', 'dose_type_id']
				label: ['Drug', 'Dose Type']
				width: [75, 25]
				selectall: true
			label: 'Active Prescriptions'

	admin_method:
		model:
			source: ['Self', 'Parent or Caregiver', 'Healthcare professional', 'Other']
			if:
				'Other':
					fields: ['admin_method_details']
		view:
			control: 'radio'
			label: 'How does the patient administer factor?'

	admin_method_details:
		model:
			required: true
		view:
			label: 'Administration Method Details:'

	# Support Questions
	nurse_support:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['patient_trained']
		view:
			control: 'radio'
			label: 'Will a nurse be required for the first infusion?'

	patient_trained:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is the patient/caregiver already trained?'

	educate_tracking:
		model:
			max: 3
			required: false
			source: ['No', 'Yes', 'NA']
		view:
			control: 'radio'
			label: 'Patient educated about importance of tracking bleeds?'
	
	educate_tracking_comment:
		view:
			control: 'area'
			label: 'Bleed Tracking Education Comment'

	educate_reporting:
		model:
			max: 3
			required: false
			source: ['No', 'Yes', 'NA']
		view:
			control: 'radio'
			label: 'Does the patient report their monthly bleeds to the pharmacy?'
	
	educate_reporting_comment:
		view:
			control: 'area'
			label: 'Bleed Reporting Education Comment'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	name: ['patient_id', 'careplan_id']
	sections_group: [
		'Factor Pre-Assessment':
			area: 'preassessment'
			fields: ['htc_care', 'htc_details', 'activity_level', 'therapy_regimen',
			'has_hcv', 'has_hcv_details', 'has_hiv', 'hiv_drugs', 'has_hiv_details']
		'Factor Patient Assessment':
			fields: ['excercise', 'have_inhibitors_history', 'have_inhibitors_history_details', 
			'took_immune_tolerance_treatment', 'immune_tolerance_comment', 'current_inhib_pt',
			'target_joints', 'target_joints_other', 'bleed_history']
		'Factor Dose Tracking':
			fields: ['dose_tracking']
		'Factor Administration Assessment':
			fields: ['admin_method', 'admin_method_details', 'nurse_support', 'patient_trained',
			'patient_screened']
		'Factor Medication Storage Assessment':
			fields: ['preferred_package', 'medication_storage',
			'medication_storage_other', 'check_expiration_reminder', 'check_expiration_reminder_details']
		'Factor Hemophilia Bleed Reporting':
			fields: ['educate_tracking', 'educate_tracking_comment', 'educate_reporting', 'educate_reporting_comment'
			'track_bleeds', 'track_bleeds_how', 'track_bleeds_why', 'track_bleeds_comment']
	]
view:
	comment: 'Patient > Careplan > Assessment > Factor'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Factor'
	open: 'read'
