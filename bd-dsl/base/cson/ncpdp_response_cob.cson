fields:

	other_id_count:
		model:
			type: 'int'
			required: true
		view:
			columns: 2
			label: 'Other Payer Count'
			readonly: true

	subform_opayer:
		model:
			type: 'subform'
			multi: true
			source: 'ncpdp_resp_opayer'
		view:
			grid:
				add: 'none'
				edit: false
				fields: [ 'other_id', 'other_pcn', 'other_cardholder_id', 'other_group_no', 'ben_eff_date', 'ben_trm_date']
				label: ['BIN', 'PCN', 'Cardholder ID', 'Group No', 'Benefit Eff Dt', 'Benefit Term Dt']
				width: [15, 15, 25, 15, 15, 15]
			reference: '337-4C'
			note: '337-4C'
			label: 'Other Payers'
			max_count: 9
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['other_id_count']
	sections:
		'COB':
			hide_header: true
			indent: false
			fields: ['other_id_count']
		'Other Payers':
			hide_header: true
			indent: false
			fields:['subform_opayer']

view:
	dimensions:
		width: '65%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Response COB'
	grid:
		fields: ['other_id_count']
		width: [100]
		sort: ['-created_on']
	label: 'Response COB'
	open: 'read'