fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	have_headaches:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields : ['type_headache', 'headache_assoc', 'loc_headache', 'headache_scale',
								'freq_headache', 'headache_dur']
		view:
			control: 'radio'
			label: 'Do you have a history of headaches?'

	type_headache:
		model:
			max: 9
			min: 1
			source: ['Migraines', 'Sinus', 'Cluster', 'Tension']
		view:
			control: 'radio'
			label: 'Type of Headaches'

	headache_assoc:
		model:
			max: 9
			min: 1
			multi: true
			source: ['Nausea', 'Light Sensitivity', 'Sound Sensitivity', 'Smell Sensitivity', 'Aura', 'Menses']
		view:
			control: 'checkbox'
			label: 'Associated With:'

	loc_headache:
		model:
			max: 10
			min: 1
			source: ['Unilateral', 'Bilateral']
		view:
			control: 'radio'
			label: 'Location of Headaches'

	headache_scale:
		model:
			max: 1
			min: 1
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Pain Scale'

	freq_headache:
		model:
			max: 32
			min: 1
			source: ['Daily', 'Weekly', 'Monthly']
		view:
			label: 'Frequency of Headaches'

	headache_dur:
		model:
			max: 32
			min: 1
			source: ['Minutes', 'Hours', 'Days']
		view:
			label: 'Duration'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	name: ['careplan_id', 'order_id']
	sections:
		'Headaches Risk Assessment':
			fields: ['have_headaches', 'type_headache', 'headache_assoc', 'loc_headache',
						'headache_scale', 'freq_headache', 'headache_dur']

view:
	comment: 'Comorbid Condition > Headaches Risk'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Headaches Risk'
	open: 'read'
