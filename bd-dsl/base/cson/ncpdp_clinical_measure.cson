fields:
	patient_id:
		model:
			prefill: ['parent.patient_id']
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	measurement_date:
		model:
			type: 'date'
		view:
			columns: 3
			reference: '494-ZE'
			note: '494-ZE'
			label: 'Date'
			template: '{{now}}'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	measurement_time:
		model:
			type: 'time'
		view:
			columns: 3
			reference: '495-H1'
			note: '495-H1'
			label: 'Time'

	measurement_dimension:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '492-WE'
		view:
			columns: 3
			reference: '496-H2'
			note: '496-H2'
			label: 'Dimension'

	measurement_unit:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '497-H3'
		view:
			columns: 3
			reference: '497-H3'
			note: '497-H3'
			label: 'Unit'

	measurement_value:
		model:
			max: 15
			required: true
		view:
			columns: 3
			reference: '499-H4'
			note: '499-H4'
			label: 'Value'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: '{measurement_date} {measurement_dimension} ({measurement_unit})'
	sections:
		'Measurement':
			hide_header: true
			indent: false
			fields: ['measurement_date', 'measurement_time', 'measurement_dimension', 'measurement_unit', 'measurement_value']

view:
	dimensions:
		width: '65%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Measurement'
	grid:
		fields: ['measurement_date', 'measurement_time', 'measurement_dimension', 'measurement_unit', 'measurement_value']
		sort: ['-created_on']
	label: 'Measurement'
	open: 'read'