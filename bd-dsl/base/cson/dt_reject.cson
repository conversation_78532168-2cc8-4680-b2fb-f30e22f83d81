fields:

	ticket_no:
		model:
			required: true
		view:
			label: 'Ticket No'
			readonly: true
			offscreen: true

	status:
		model:
			required: true
			source:
				order_ver: 'Order Verification'
				pending_conf: 'Ticket Confirmation'
		view:
			label: 'Ticket Stage'
			readonly: true

	reason:
		model:
			required: true
		view:
			label: 'Reject Reason'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr','liaison', 'pharm']
		write:      ['admin', 'csr','liaison', 'pharm']

	name: 'Delivery Ticket Reject'

	sections:
		'Rejection':
			hide_header: true
			fields: ['ticket_no','status', 'reason']

view:
	dimensions:
		width: '55%'
		height: '33%'
	hide_cardmenu: true
	comment: 'Delivery Ticket Reject'
	find:
		basic: ['status', 'reason']
		advanced: ['status', 'reason']
	grid:
		fields: ['ticket_no', 'status', 'reason']
		sort: ['status', 'reason']
	label: 'Delivery Ticket Reject'
