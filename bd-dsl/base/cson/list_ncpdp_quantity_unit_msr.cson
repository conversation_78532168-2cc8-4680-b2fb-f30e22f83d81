fields:

	code:
		model:
			max: 64
			required: true
		view:
			columns: 2
			label: 'Code'
			findunique: true

	name:
		model:
			required: true
			max: 128
		view:
			columns: 2
			label: 'Name'
			findunique: true

	unit_map_id:
		model:
			required: true
			source: 'list_unit'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Dispensing Unit'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: '{code} - {name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'unit_map_id']

view:
	comment: 'Manage > NCPDP Quantity Unit Measurement Code'
	find:
		basic: ['code', 'name', 'unit_map_id']
	grid:
		fields: ['code', 'name', 'unit_map_id']
		sort: ['code']
	label: 'NCPDP Quantity Unit Measurement Code'
	open: 'read'