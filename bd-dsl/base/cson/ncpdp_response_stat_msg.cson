fields:

	add_msg:
		view:
			note: '526-FQ'
			label: 'Additional Message'
			readonly: true

	add_msg_qualifier:
		model:
			multi: false
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '132-UH'
		view:
			note: '132-UH'
			label: 'Addtl Msg Qualifier'
			readonly: true

	add_msg_cont:
		model:
			multi: false
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '131-UG'
		view:
			note: '131-UG'
			label: 'Addtl Msg Cont Indent'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['add_msg', 'add_msg_qualifier']
	sections:
		'Additional Message':
			hide_header: true
			indent: false
			fields: ['add_msg', 'add_msg_qualifier', 'add_msg_cont']

view:
	dimensions:
		width: '50%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Additional Message'
	grid:
		fields: ['add_msg']
		width: [100]
		sort: ['-created_on']
	label: 'Additional Message'
	open: 'read'