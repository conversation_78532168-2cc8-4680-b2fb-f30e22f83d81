fields:
	patient_id:
		model:
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'

	sms_status:
		view:
			class: 'status'
			label: 'SMS Status'
			readonly: true

	message_id:
		view:
			label: 'Message ID'
			readonly: true

	phone_number:
		view:
			label: 'Phone #'
			readonly: true

	error_code:
		view:
			label: 'Undelivered Error Code'
			offscreen: true
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['audit']
	prefill:
		patient:
			link:
				id: 'patient_id'
	name: ['phone_number' , 'sms_status', 'created_on']
	sections:
		'SMS Audit':
					fields: ['patient_id', 'phone_number', 'message_id', 'sms_status']

view:
	hide_cardmenu: true
	comment: 'SMS Audit'
	find:
		basic: ['patient_id', 'phone_number', 'message_id']
	grid:
		fields: ['created_on', 'patient_id', 'message_id', 'sms_status']
		sort: ['-created_on']
	label: 'SMS Audit'
