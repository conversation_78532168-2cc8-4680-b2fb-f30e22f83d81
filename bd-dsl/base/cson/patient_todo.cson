fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	parent_form:
		model:
			required: false
		view:
			label: 'Parent Form'
			readonly: true
			offscreen: true

	due_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 2
			label: 'Due Date'

	task:
		model:
			required: true
		view:
			columns: 2
			label: 'To Do Description'

	assign_to_id:
		model:
			multi:true
			required: true
			source: 'user'
			sourcefilter:
				role:
					static: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse','physician','payer', 'pharm']
		view:
			note: 'Select all that apply'
			label: 'Assign To:'

	cc_response_id:
		model:
			multi:true
			required: false
			source: 'user'
		view:
			note: 'Select all that apply'
			label: 'CC Response To:'

	resolved:
		model:
			source: ['Yes', 'No']
			if:
				'Yes':
					fields: ['resolved_date', 'resolved_response']
			default: 'No'
		view:
			columns: 2
			label: 'Resolved?'

	resolved_date:
		model:
			type: 'date'
			required: false
		view:
			columns: 2
			label: 'Resolved Date'

	resolved_response:
		view:
			control: 'area'
			label: 'Resolved Response'

	notes:
		view:
			control: 'area'
			label: 'Notes'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
	name: ['patient_id', 'task']
	sections:
		'To Do Tasks':
			fields: ['parent_form','due_date', 'task',
			'assign_to_id','cc_response_id', 'resolved',
			'resolved_date','resolved_response', 'notes']

view:
	hide_cardmenu: true
	comment: 'Patient > To Do Tasks'
	grid:
		fields: ['due_date', 'task', 'assign_to_id', 'cc_response_id', 'resolved', 'resolved_date']
		sort: ['-resolved', '-id']
	label: 'To Do Task'
	open: 'read'
