fields:

	trigger_location:
		model:
			source:
				delivery_tick: 'Delivery Ticket'
				payer_account: 'Payer Account'
				patient_account: 'Patient Account'
			if:
				'delivery_tick':
					fields: ['patient_id', 'receipt_url']
				'payer_account':
					fields: ['check_no', 'patient_id_filter', 'payer_id', 'payer_id_filter', 'charge_line_postings', 'applied_amount']
					sections: ['Outstanding AR']
				'patient_account':
					fields: ['patient_id', 'receipt_url', 'charge_line_postings', 'applied_amount']
					sections: ['Outstanding AR']
		view:
			label: 'Trigger Location'
			readonly: true
			offscreen: true

	ticket_no:
		model:
			type: 'text'
		view:
			label: 'Ticket #'
			readonly: true
			offscreen: true

	close_no:
		model:
			type: 'text'
		view:
			label: 'Closing #'
			readonly: true
			offscreen: true

	created_by:
		model:
			source: 'user'
			type: 'int'
			if:
				'!':
					sections: ['Filters']
				'*':
					readonly:
						fields: ['check_no', 'amount', 'unapplied_amount', 'post_datetime', 'site_id', 'payer_id', 'patient_id', 'receipt_url', 'charge_line_postings']
		view:
			label: 'Created By'
			readonly: true
			offscreen: true

	locked:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['locked_datetime', 'locked_by']
		view:
			columns: 3
			control: 'checkbox'
			label: 'Locked'
			readonly: true
			offscreen: true

	locked_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Locked Date/Time'
			readonly: true
			offscreen: true

	locked_by:
		model:
			source: 'user'
		view:
			label: 'Locked By'
			readonly: true
			offscreen: true

	payment_method:
		model:
			source: ['Check', 'Cash', 'Credit Card', 'Debit Card', 'NCPDP', 'ERN', 'Other']
			if:
				'Credit Card':
					fields: ['receipt_url']
				'Debit Card':
					fields: ['receipt_url']
				'Check':
					fields: ['check_no']
		view:
			control: 'radio'
			columns: 3
			label: 'Payment Method'

	check_no:
		model:
			type: 'text'
			required: true
		view:
			columns: 3
			label: 'Check Number'

	amount:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 3
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Amount'

	unapplied_amount:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			class: 'numeral money'
			readonly: true
			format: '$0,0.00'
			label: 'Unapplied Amount'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'unapplied_amount'
					overwrite: true
				}
			]

	unapplied_cash_available:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Unapplied Cash Available'
			readonly: true
			offscreen: true

	applied_amount:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Applied Amount'
			offscreen: true
			readonly: true

	post_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Posting Date/Time'
			template: '{{now}}'
			validate: [
				{
					name: 'ValidatePostDate'
				}
			]

	posting_no:
		model:
			type: 'text'
		view:
			columns: 2
			label: 'Posting #'
			note: 'Generated at save'
			readonly: true

	patient_id:
		model:
			required: false
			source: 'patient'
			type: 'int'
		view:
			columns: 2
			label: 'Patient'
			transform: [{
				name: 'CopyFieldToField'
				to: 'patient_id_filter'
				overwrite: true
			}]

	receipt_url:
		model:
			required: false
		view:
			columns: 2
			format: 'url'
			label: 'Receipt URL'
			readonly: true

	payer_id:
		model:
			required: false
			source: 'payer'
			type: 'int'
			if:
				'*':
					readonly:
						fields: ['payer_id_filter']
		view:
			columns: 2
			label: 'Payer'
			transform: [{
				name: 'CopyFieldToField'
				to: 'payer_id_filter'
				overwrite: true
			}]

	site_id:
		model:
			required: false
			source: 'site'
		view:
			columns: 4
			label: 'Site'

	patient_id_filter:
		model:
			required: false
			source: 'patient'
			type: 'int'
			sourcefilter:
				site_id:
					'dynamic': '{site_id}'
		view:
			columns: 4
			label: 'Patient'

	payer_id_filter:
		model:
			required: false
			source: 'payer'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
		view:
			label: 'Payer'
			readonly: true
			offscreen: true

	clear_filters:
		model:
			source: ['Reset']
		view:
			columns: 4
			class: 'dsl-button'
			control: 'checkbox'
			transform: [{
				name: 'ClearValues'
				fields: ['site_id', 'patient_id_filter', 'payer_id_filter']
				ignore_readonly: true
			}]
			label: 'Clear Filters'

	show_void_warnings:
		model:
			source: ['No','Yes']
			if:
				'Yes':
					fields: ['void_warnings']
		view:
			control: 'radio'
			label: 'Show Void Warnings?'
			offscreen: true
			readonly: true

	void_warnings:
		model:
			source: ['']
		view:
			label: 'Warning'
			class: 'list'
			readonly: true

	void:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['show_void_warnings', 'voided_datetime', 'void_reason_id']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Void Posting?'
			findfilter: '!Yes'
			class: 'checkbox-only'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						void: 'Yes'
					dest: 'voided_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						void: 'Yes'
					dest: 'voided_by'
				}
			]

	void_reason_id:
		model:
			required: true
			source: 'list_void_reason_billing'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Void Reason'

	voided_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Voided Date'
			readonly: true

	voided_by:
		model:
			source: 'user'
		view:
			label: 'Voided By'
			readonly: true

	charge_line_postings:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			label: 'Outstanding AR'

model:
	access:
		create:     []
		create_all: ['admin', 'cm']
		delete:     []
		read:       ['admin', 'cm']
		read_all:   ['admin', 'cm']
		request:    []
		update:     []
		update_all: []
		write:      ['admin', 'cm']
	reportable: true
	name: '${check_no} ${post_datetime} Available:${unapplied_cash_available}'

	sections_group: [
		'Main':
			hide_header: true
			indent: false
			fields: ['trigger_location', 'ticket_no', 'created_by', 'payer_id', 'patient_id', 'check_no', 'amount', 'unapplied_amount', 'unapplied_cash_available', 'applied_amount', 'post_datetime', 'posting_no']
		'Filters': # Don not touch filters section doing scope based styling (or change styling in bd/tshomebase/src/dsl-form/styles/billing_posting.less)
			fields: [ 'site_id', 'payer_id_filter', 'patient_id_filter','clear_filters']
		'Outstanding AR':
			hide_header: true
			indent: false
			fields: ['charge_line_postings']
		'Void':
			modal: true
			fields: ['show_void_warnings', 'void_warnings', 'void', 'voided_datetime', 'voided_by', 'void_reason_id']
	]

view:
	dimensions:
        width: '75%'
        height: '75%'
	hide_cardmenu: true
	validate: [
		{
			name: "RequiredOne"
			fields: ['patient_id', 'payer_id']
		},
		{
			name: "PostingBalanceAmountValidator"
			error: "Applied Amount cannot be less than the sum of the Invoice Charge Lines"
		}
	]
	block:
		validate: [
			name: 'PostingBlock'
			fields: ['show_void_warnings', 'void_warnings', 'void', 'void_reason_id', 'voided_datetime']
		]
	comment: 'Cash Posting'
	find:
		basic: ['patient_id', 'payer_id', 'site_id', 'posting_no', 'check_no']
	grid:
		fields: ['posting_no', 'patient_id', 'payer_id', 'check_no', 'amount']
		sort: ['-created_on']
	label: 'Cash Posting'
