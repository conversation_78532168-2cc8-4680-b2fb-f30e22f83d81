fields:
	code:
		model:
			type: 'text'
			max: 64
			required: true
		view:
			label: 'Code'
			readonly: true
			columns: 3

	routed_med_id:
		model:
			type: 'int'
			max: 8
			required: true
		view:
			label: 'MED Routed Medication ID (Stable ID)'
			readonly: true
			columns: 3

	med_name_id:
		model:
			type: 'int'
			max: 8
			required: true
		view:
			label: 'MED Medication Name ID (Stable ID)'
			readonly: true
			columns: 3

	med_route_id:
		model:
			type: 'int'
			max: 5
			required: true
		view:
			label: 'MED Route ID'
			readonly: true
			columns: 3

	med_routed_med_id_desc:
		model:
			type: 'text'
			max: 60
			required: true
		view:
			label: 'MED Routed Medication Description'
			readonly: true
			columns: 3

	med_status_cd:
		model:
			type: 'text'
			max: 1
			required: true
		view:
			label: 'MED Medication Status Code'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: '{routed_med_id} {med_name_id}'
	indexes:
		many: [
			['routed_med_id']
			['med_name_id']
			['med_route_id']
		]

	sections:
		'Routed Medication':
			tab: 'Information'
			fields: [
				'code',
				'routed_med_id',
				'med_name_id',
				'med_route_id',
				'med_routed_med_id_desc',
				'med_status_cd'
			]

view:
	comment: 'Manage > List FDB RMIRMID1 Routed Med'
	find:
		basic: ['routed_med_id', 'med_name_id']
	grid:
		fields: [
			'routed_med_id',
			'med_name_id',
			'med_route_id',
			'med_routed_med_id_desc',
			'med_status_cd'
		]
	label: 'List FDB RMIRMID1 Routed Med'