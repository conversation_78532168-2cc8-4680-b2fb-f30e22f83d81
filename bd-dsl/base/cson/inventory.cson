fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	external_id:
		view:
			label: 'External ID'
			readonly: true
			offscreen: true

	type:
		model:
			required: true
			default: 'Drug'
			source: ['Drug', 'Compound', 'Supply', 'Equipment Rental', 'Billable']
			if:
				'Compound':
					fields: ['name', 'route_id', 'therapy_id', 'type_id', 'label_name', 'storage_id',
					'dea_schedule_id','requires_nursing', 'sp_pk_indicator', 'is_billed_compound', 'billing_unit_id', 'list_price',
					'taxable', 'therapy_id', 'requires_nursing', 'lot_tracking', 'serial_tracking', 'hcpc_modifier', 'hcpc_unit']
					sections: ['Item Info', 'Label', 'Dispense', 'Compound', 'Dosing', 'Units', 'HCPC']
					readonly:
						fields: ['is_billed_compound']
					prefill:
						is_billed_compound: 'Yes'
				'Drug':
					fields: ['created_on', 'hcpc_id', 'name', 'fdb_id', 'formatted_ndc', 'label_name', 'warning_label',
					'brand_name_id', 'additional_descriptor', 'generic_name', 'therapy_class', 'gcn_seqno', 'medid',
					'dea_schedule_id', 'speciality_flag', 'obsolete_date', 'type_id', 'route_id',
					'therapy_id', 'storage_id', 'asp_price', 'awp_price_pkg', 'awp_price',
					'list_price', 'wac_price_pkg', 'wac_price', 'add_price1', 'add_price2',
					'lot_tracking', 'serial_tracking', 'ndc', 'last_cost_ea', 'last_cost_pkg', 'requires_nursing', 'compound_ingredient',
					'billing_unit_id', 'manufacturer_id', 'quantity_per_package', 'package_unit', 'product_type', 'dosage_form_type',
					'avg_acq_cost_brand', 'avg_acq_cost_gen', 'ful', 'last_update_at','taxable', 'drug_category_id', 'hcpc_unit',
					'therapy_id', 'requires_nursing','primary_supplier_id', 'last_supplier_id','strength', 'strength_unit_id', 'hcpc_modifier']
					readonly:
						fields: ['label_name', 'billing_unit_id', 'hcpc_id', 'hcpc_quantity']
					require_fields: ['ndc', 'manufacturer_id']
					sections: ['Item Info', 'Label', 'Dispense', 'Dosing', 'Units', 'HCPC', 'Site Info']
					prefill:
						is_billed_compound: ''
				'Supply':
					fields: ['lot_tracking', 'supply_fdb_id', 'use_dummy_ndc', 'created_on', 'name', 'supply_category_id', 'upc', 'upin', 'sku',
					'last_cost_pu', 'last_cost_cont', 'supply_billable','primary_supplier_id', 'last_supplier_id', 'iv_container', 'hcpc_unit_limit']
					sections: ['Item Info', 'Supply Units', 'Site Info']
					prefill:
						is_billed_compound: ''
						revenue_code_id: 'PerD'
						price_code_id: '02'
				'Equipment Rental':
					readonly:
						fields: ['serial_tracking']
					prefill:
						serial_tracking: 'Yes'
						is_billed_compound: ''
					require_fields: ['serial_tracking']
					fields: ['created_on','requires_cmn','name', 'purchase_cost', 'daily_rental_cost', 'monthly_rental_cost',
					'purchase_list_price', 'billable_code_id', 'daily_rental_list_price', 'default_return_status', 'sales_type', 'rental_category_id', 'requires_rental_purchase_letter', 'days_between_checks', 'days_between_pm',
					'monthly_rental_list_price', 'udi', 'serial_tracking', 'last_supplier_id', 'created_by', 'hcpc_rental_modifier', 'hcpc_purchase_modifier', 'hcpc_allowable']
					sections: ['Item Info', 'HCPC', 'Rental Items']
				'Billable':
					fields: ['name', 'requires_cmn', 'actual_cost', 'list_price', 'add_price1', 'add_price2', 'nursing_related', 'billable_code_id', 'hcpc_modifier', 'description']
					sections: ['HCPC']
					prefill:
						is_billed_compound: ''
						revenue_code_id: 'PerD'
						price_code_id: '04'
		view:
			label: 'Type'
			readonly: true
			offscreen: true
			columns: 4
			validate: [
				name: 'InventoryType'
			]

	active:
		model:
			max: 32
			source: ['Yes']
			default: 'Yes'
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Active'
			columns: 4

	tpn_ingredient:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'TPN Ingredient?'
			columns: 4
			offscreen: true

	supply_fdb_id:
		model:
			required: false
			source: 'list_fdb_ndc'
			sourceid: 'code'
			sourcefilter:
				inpcki:
					'static': '!1'
				hcfa_ps:
					'static': '!0'
				cl:
					'static': ['O', 'Q']
			if:
				'*':
					fields: ['formatted_ndc', 'ndc']
					prefill:
						supply_billable: 'Yes'
		view:
			note: 'Must be outer package NDC'
			label: 'Master Item'
			columns: 4
			validate: [
				name: 'LoadFDBSupplyData'
			]

	use_dummy_ndc:
		model:
			source: ['Yes']
			if:
				'!':
					fields: ['supply_fdb_id']
					prefill:
						supply_fdb_id: ''
				'Yes':
					prefill:
						formatted_ndc: '00000-0000-00'
						ndc: '00000000000'
		view:
			label: 'Use Dummy NDC?'
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			note: 'Can only be used for medical payers through NCPDP.'

	fdb_id:
		model:
			required: true
			source: 'list_fdb_ndc'
			sourceid: 'code'
			sourcefilter:
				inpcki:
					'static': '!1'
				cl:
					'static': ['F', 'O']
		view:
			note: 'Must be outer package NDC'
			label: 'Master Drug Item'
			columns: 4
			validate: [
				name: 'LoadFDBData'
			]

	name:
		model:
			required: true
			search: 'A'
		view:
			columns: 4
			label: 'Name'
			findunique: true

	rental_category_id:
		model:
			source: 'list_rental_category'
			sourceid: 'code'
		view:
			label: 'Rental Category'
			columns: 4

	supply_category_id:
		model:
			source: 'list_supply_category'
			sourceid: 'code'
		view:
			label: 'Supply Category'
			columns: 4

	default_return_status:
		model:
			source: 'list_rental_status'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Default Return Status'

	sales_type:
		model:
			required: true
			source: ['Sales', 'Rentals', 'Rentals and Sales']
			default: 'Rentals and Sales'
			if:
				'Sales':
					fields: ['hcpc_purchase_modifier', 'purchase_list_price']
				'Rentals':
					fields: ['hcpc_rental_modifier', 'daily_rental_list_price',  'monthly_rental_list_price']
				'Rentals and Sales':
					fields: ['hcpc_purchase_modifier', 'hcpc_rental_modifier',
					'purchase_list_price', 'daily_rental_list_price', 'monthly_rental_list_price']
		view:
			columns: 2
			label: 'Sales Type'

	requires_rental_purchase_letter:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			note: 'At 1 Month'
			label: 'Requires Rental/Purchase Letter?'

	days_between_pm:
		model:
			required: false
			type: 'int'
			min: 1
		view:
			columns: 4
			label: 'Days Between PM'

	days_between_checks:
		model:
			required: false
			type: 'int'
			min: 1
		view:
			columns: 4
			label: 'Days Between Checks'

	description:
		view:
			control: 'area'
			label: 'Description'

	#list_fdb_ndc.BN
	brand_name_id:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
			required: true
		view:
			label: 'Short Brand Name'
			columns: 4
			readonly: true
			class: 'fdb-field'

	additional_descriptor:
		view:
			label: 'Additional Descriptor'
			columns: 4
			readonly: true
			class: 'fdb-field'

	#list_fdb_med_table.med_medid_desc
	#Links through list_fdb_ndc_to_medid.ndc to list_fdb_med_table.medid
	generic_name:
		model:
			required: true
			search: 'B'
		view:
			label: 'Generic Name / Brand Name'
			class: 'fdb-field'
			columns: 4
			readonly: true

	#list_fdb_ndc.LBLRID
	manufacturer_id:
		model:
			required: false
			source: 'list_manufacturer'
			sourceid: 'code'
		view:
			label: 'Manufacturer'
			class: 'fdb-field'
			columns: 4
			readonly: true

	#list_fdb_ndc.PS
	quantity_each:
		model:
			required: true
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
			min: 0.001
		view:
			class: 'numeral fdb-field'
			format: '0,0.[000000]'
			label: 'Quantity Per Each'
			columns: 4
			readonly: true
			offscreen: true

	#Normalized Metric Units
	metric_unit_each:
		model:
			required: true
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
			min: 0.001
		view:
			class: 'numeral fdb-field'
			format: '0,0.[000000]'
			label: 'Metric Units/Each'
			columns: 4
			readonly: true

	strength:
		model:
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
			min: 0.001
		view:
			class: 'numeral fdb-field'
			format: '0,0.[000000]'
			label: 'Strength'
			columns: 4
			readonly: true

	strength_unit_id:
		model:
			source: 'list_unit'
			sourceid: 'code'
		view:
			class: 'fdb-field'
			label: 'Strength Unit'
			columns: 4
			readonly: true

	# Measurement
	#list_fdb_ndc.DF (1 = 'each', 2 = 'mL', 3 = 'gram'), other units not supported by FDB
	billing_unit_id:
		model:
			required: true
			source: 'list_unit'
			sourceid: 'code'
			sourcefilter:
					code:
						'static': ['each', 'mL', 'gram']
			if:
				'mL':
					fields: ['is_reconstituted']
					prefill:
						report_unit_id: 'mL'
				'each':
					prefill:
						report_unit_id: 'each'
				'gram':
					prefill:
						report_unit_id: 'gram'
		view:
			control: 'select'
			label: 'Metric Unit'
			class: 'fdb-field'
			columns: 4
			transform: [
				{
					name: 'UpdateUnitFieldNote'
					target: ['metric_unit_each']
				}
			]

	package_unit:
		view:
			label: 'Package Unit'
			class: 'fdb-field'
			columns: 4
			readonly: true

	quantity_per_case:
		model:
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
			min: 0.001
		view:
			class: 'numeral fdb-field'
			format: '0,0.[000000]'
			label: 'Quantity / Case'
			columns: 4
			readonly: true

	drug_category_id:
		model:
			required: true
			default: 'Raw Drug'
			source: 'list_drug_category'
			sourceid: 'code'
		view:
			label: 'Drug Category'
			columns: 4

	report_unit_id:
		model:
			required: true
			source: 'list_unit'
			sourceid: 'code'
			sourcefilter:
				base_unit:
					'static': 'Yes'
				code:
					'dynamic': '{flt_disp_unit}'
				active:
					'static': 'Yes'
		view:
			control: 'select'
			label: 'Reporting Unit'
			columns: 4

	quantity_per_package:
		model:
			required: true
			type: 'decimal'
			max: 9999999
			rounding: 1
			min: 1
		view:
			label: 'Quantity Per Package'
			columns: 4
			validate: [
				name: 'RecalcAWPWACPackage'
			]

	nursing_related:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['initial_visit', 'nursing_hours']
					prefill:
						revenue_code_id: 'RNV'
						price_code_id: '08'
				'!':
					fields: ['dme_related']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			columns: 4
			label: 'Nursing Related?'

	dme_related:
		model:
			source: ['Yes']
			if:
				'!':
					fields: ['nursing_related']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			columns: 4
			label: 'DME Related?'

	initial_visit:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			columns: 4
			label: 'Initial Visit?'

	nursing_hours:
		model:
			default: 1
			rounding: 0.25
			required: true
			type: 'decimal'
		view:
			columns: 4
			label: 'Nursing Hours Per Unit'

	billable_code_id:
		model:
			required: true
			source: 'list_billing_code'
			sourceid: 'code'
		view:
			class: 'select_prefill fdb-field'
			columns: 4
			label: 'Billable Code'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_billing_code/?limit=1&fields=list&sort=name&page_number=0&filter=code:'
				fields:
					'name': '{{pr.name}}',
					'hcpc_code': '{{pr.code}}'
			]

	supply_billable:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['billable_code_id', 'wac_price', 'wac_price_pkg', 'awp_price','awp_price_pkg',
					'add_price1', 'add_price2', 'list_price', 'hcpc_modifier']
					sections: ['HCPC']
		view:
			columns: 4
			label: 'Supply Billable?'
			control: 'checkbox'
			class: 'checkbox-only'

	# Identifiers
	#list_fdb_ndc.NDC
	ndc:
		model:
			search: 'A'
		view:
			label: 'NDC'
			columns: 4
			findunique: true
			class: 'fdb-field'
			readonly: true
			offscreen: true

	formatted_ndc:
		model:
			type: 'text'
			search: 'A'
		view:
			label: 'Formatted NDC'
			columns: 4
			findunique: true
			class: 'fdb-field'
			readonly: true

	upc:
		model:
			search: 'A'
		view:
			label: 'UPC'
			columns: 4
			findunique: true

	upin:
		model:
			search: 'A'
		view:
			label: 'UPIN'
			columns: 4
			findunique: true
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{12,14}$'
				error: 'Invalid UPIN. Must be 12-14 digits.'
			}]

	sku:
		model:
			search: 'A'
		view:
			label: 'SKU'
			columns: 4
			findunique: true

	udi:
		model:
			search: 'A'
		view:
			label: 'UDI'
			columns: 4
			findunique: true
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{1,4}\\s?\\d{6,18}(\\s?\\d{1,18})?$'
				error: 'Invalid UDI. Must be a GS1, HIBCC, or ICCBBA formatted UDI.'
			}]

	#list_fdb_ndc.DEA
	# Need to transform to the NCI number
	# 1 = 'C48672'
	# 2 = 'C48675'
	# 3 = 'C48676'
	# 4 = 'C48677'
	# 5 = 'C48679'
	dea_schedule_id:
		model:
			source: 'list_dea_schedule'
			sourceid: 'code'
		view:
			class: 'fdb-field'
			label: 'DEA Schedule'
			columns: 4
			readonly: true

	#list_fdb_ndc.gcn_seqno
	gcn_seqno:
		model:
			required: false
		view:
			readonly: true
			label: 'FDB GCN Sequence Number'
			class: 'fdb-field'
			columns: 4
			validate: [
				name: 'SettingCautionLabels'
			]

	#list_fdb_ndc_to_medid where ndc = list_fdb_med_table.ndc, and prefill in list_fdb_ndc_to_medid.medid
	medid:
		model:
			required: false
		view:
			readonly: true
			class: 'fdb-field'
			label: 'FDB MEDID'
			columns: 4

	#list_fdb_therapy.etc_name
	#Links through list_fdb_ndc_to_therapy.ndc to list_fdb_therapy.etcid
	therapy_class:
		view:
			readonly: true
			class: 'fdb-field'
			label: 'Therapy Class'
			columns: 4
			findunique: true

	speciality_flag:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Speciality Drug?'
			columns: 4

	iv_container:
		model:
			default: 'No'
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['iv_container_volume']
					prefill:
						product_type: 'Container'
				'No':
					prefill:
						product_type: ''
		view:
			control: 'radio'
			label: 'IV Container?'
			columns: 4

	iv_container_volume:
		model:
			required: true
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
			min: 0.001
		view:
			label: 'Volume'
			class: 'fdb-field'
			columns: 4

	compound_ingredient:
		model:
			source: ['Yes']
			if:
				'Yes':
					sections: ['Dispense']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Compound Ingredient?'
			columns: 4

	#list_fdb_ndc.OBSDTEC
	obsolete_date:
		model:
			type: 'date'
		view:
			label: 'Date of Obsolescence'
			class: 'fdb-field'
			columns: 4
			readonly: true

	item_barcode:
		model:
			max: 40
		view:
			columns: 4
			label: 'Item Barcode'
			note: 'GS1/GTIN, UPC, or internal'

	raw_barcode:
		model:
			max: 80
		view:
			class: 'barcode'
			columns: 1
			control: 'barcode'
			label: 'Assign Barcode to Item'
			validate: [{
				name: 'BarcodeParseValidator'
				fields:
					raw: 'item_barcode'
					gtin: 'item_barcode'
			}]

	# Supply Units
	purchase_unit:
		model:
			required: true
			source: ['Box', 'Carton', 'Case', 'Batch', 'Packet', 'Each']
		view:
			control: 'radio'
			label: 'Purchase Unit'
			columns: 4

	quantity_per_container:
		model:
			required: true
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
			min: 0.001
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Containers / Purchase Unit'
			columns: 4

	container_unit:
		model:
			required: true
			multi: false
			source: ['Tube', 'Inches', 'Foot', 'Pack', 'Tray', 'Bag', 'Box', 'Vials', 'Each']
		view:
			class: 'checkbox checkbox-4'
			control: 'checkbox'
			label: 'Container Unit'
			columns: 4

	each_per_container:
		model:
			required: true
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
			min: 0.001
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Eaches / Container Unit'
			columns: 4

	# Pricing
	add_price1:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			label: 'Additional Price 1 (EA)'
			class: 'numeral money'
			format: '$0,0.0000'
			columns: 4
			validate: [{
				name: 'SettingListPrice'
			}]

	add_price2:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			label: 'Additional Price 2 (EA)'
			class: 'numeral money'
			format: '$0,0.0000'
			columns: 4
			validate: [{
				name: 'SettingListPrice'
			}]

	# list_fdb_ndc_price.price where price_type = 9 (WAC)
	# Linked through NDC to list_fdb_ndc_price.ndc
	# REMEMBER TO PULL THE LATEST PRICE!
	wac_price:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			label: 'Whole Acquisition Cost (EA)'
			columns: 4
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			validate: [{
				name: 'RecalcAWPWACPackage'
			}, {
				name: 'SettingListPrice'
			}]

	# list_fdb_ndc_price.price where price_type = 10 (WAC Package)
	# Linked through NDC to list_fdb_ndc_price.ndc
	# REMEMBER TO PULL THE LATEST PRICE!
	wac_price_pkg:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			label: 'Whole Acquisition Cost / Package'
			columns: 4
			class: 'numeral fdb-field'
			format: '$0,0.0000'

	revenue_code_id:
		model:
			required: true
			source: 'list_revenue_code'
			sourceid: 'code'
		view:
			label: 'Revenue Code'
			columns: 4

	price_code_id:
		model:
			source: 'list_price_code'
			sourceid: 'code'
			required: true
		view:
			label: 'Price Code'
			columns: 4
			validate:[
				name:'SettingListPrice'
			]

	# This is based off your price code the price code table
	# list_price_code.code = price_code_id.code
	# Find the price code in the setup (currently site_price_code_item)
	# and then find the basis in price_formula_id
	# Use that basis * multiplier to get the list price
	# (i.e. price_formula_id = 'A' and multiplier = 1.2 then list = awp_price * 1.2)
	list_price:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
		view:
			note: 'Each'
			label: 'List Price'
			class: 'numeral money'
			format: '$0,0.0000'
			columns: 4

	actual_cost:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
		view:
			note: 'Each'
			label: 'Actual Cost'
			class: 'numeral money'
			format: '$0,0.0000'
			columns: 4

	asp_price:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			note: 'Each'
			label: 'ASP Price'
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			columns: 4
			validate: [{
				name: 'SettingListPrice'
			}]

	# wac_price * 1.2
	awp_price:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.0000
		view:
			note: 'Each'
			label: 'AWP Price'
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			columns: 4
			validate: [{
				name: 'RecalcAWPWACPackage'
			}, {
				name: 'SettingListPrice'
			}]

	awp_price_pkg:
		model:
			rounding: 0.00001
			type: 'decimal'
		view:
			note: 'Pkg'
			label: 'AWP Price Package'
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			columns: 4

	awp_basis:
		model:
			source: ['WAC * 1.2', 'SWP']
		view:
			label: 'AWP Basis'
			offscreen: true
			readonly: true
			transform: [
				{
					name: 'UpdateFieldNote'
					target: 'awp_price_pkg'
				},
				{
					name: 'UpdateFieldNote'
					target: 'awp_price'
				}
			]

	#Rental Pricing
	purchase_cost:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
		view:
			label: 'Purchase Cost - EA'
			class: 'numeral'
			format: '$0,0.0000'
			columns: 4

	daily_rental_cost:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
		view:
			label: 'Daily Rental - Cost'
			class: 'numeral'
			format: '$0,0.0000'
			columns: 4

	monthly_rental_cost:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
		view:
			label: 'Monthly Rental - Cost'
			class: 'numeral'
			format: '$0,0.0000'
			columns: 4

	purchase_list_price:
		model:
			rounding: 0.00001
			type: 'decimal'
		view:
			label: 'Retail Sales Price'
			class: 'numeral'
			format: '$0,0.0000'
			columns: 4

	daily_rental_list_price:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
		view:
			label: 'Daily Rental - List Price'
			class: 'numeral'
			format: '$0,0.0000'
			columns: 4

	monthly_rental_list_price:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
		view:
			label: 'Monthly Rental - List Price'
			class: 'numeral'
			format: '$0,0.0000'
			columns: 4

	last_cost_pkg:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0
		view:
			class: 'numeral'
			format: '$0,0.0000'
			label: 'Last Cost / Pkg'
			columns: 4
			readonly: true

	last_cost_ea:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0
		view:
			class: 'numeral'
			format: '$0,0.0000'
			label: 'Last Cost (EA)'
			columns: 4
			readonly: true

	last_cost_cont:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0
		view:
			class: 'numeral'
			format: '$0,0.0000'
			label: 'Last Cost / Cont'
			columns: 4
			readonly: true

	last_cost_pu:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0
		view:
			class: 'numeral'
			format: '$0,0.0000'
			label: 'Last Cost / PU'
			columns: 4
			readonly: true

	primary_supplier_id:
		model:
			source: 'list_supplier'
		view:
			label: 'Primary Supplier'
			columns: 4

	last_supplier_id:
		model:
			source: 'list_supplier'
		view:
			label: 'Last Supplier'
			columns: 4

	taxable:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Taxable?'
			readonly: true
			offscreen: true

	# Compound
	comp_dsg_fm_code:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '450-EF'
		view:
			columns: 4
			label: 'Compound Dosage Form Code'
			note: '450-EF'
			class: 'claim-field'

	compound_type:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '996-G1'
		view:
			columns: 4
			note: '996-G1'
			label: 'Compound Type'
			class: 'claim-field'

	comp_disp_unit:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '451-EG'
		view:
			label: 'Compound Dispensing Unit'
			note: '451-EG'
			reference: '451-EG'
			class: 'claim-field'

	# link through NDC @ list_fdb_medicare_mstr.ndc pick mcr_ref from the row and then
	# where mcr_ref = list_fdb_medicare_desc.mcr_ref use id as hcpc_id
	hcpc_id:
		model:
			source: 'list_fdb_medicare_desc'
			sourceid: 'code'
		view:
			columns: 4
			label: 'HCPC'
			class: 'select_prefill fdb-field'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_fdb_medicare_desc/?limit=1&filter=code:'
				fields:
					'hcpc_code': ['mcr_ref']
			]

	hcpc_code:
		model:
			if:
				'*':
					require_fields: ['hcpc_quantity']
		view:
			label: 'HCPC Code'
			columns: 4
			readonly: true

	hcpc_unit:
		view:
			label: 'HCPC Unit'
			class: 'fdb-field'
			columns: 4
			readonly: true
			transform: [
				{
					name: 'UpdateFieldNote'
					target: 'hcpc_quantity'
				}
			]

	hcpc_allowable:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0
		view:
			label: 'Allowable / Unit'
			columns: 4

	#list_cms_ndc_hcpc_cw.bill_units
	# Link through HCPC at list_cms_ndc_hcpc_cw.hcpc
	hcpc_quantity:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			note: 'Each'
			label: 'HCPC Units (EA)'
			columns: 4
			validate: [
				{
					name: 'CalculateMCRCharge'
				}
			]

	hcpc_unit_limit:
		model:
			min: 1
			type: 'int'
			if:
				'*':
					fields: ['hcpc_limit_freq']
		view:
			columns: 4
			note: 'Each'
			label: 'Limit'

	#Freq. - The frequency and limit amounts will be set within the SPM and tracked by the system on both working and confirmed delivery tickets. 
	hcpc_limit_freq:
		model:
			required: true
			source: ['Day', 'Week', 'Month', 'Quarter', 'Bi-Annual', 'Annual']
		view:
			columns: 4
			control: 'radio'
			label: 'Freq'

	#list_fdb_medicare_desc.mcr_bcdesc
	# Link through HCPC where HCPC = list_fdb_medicare_desc.mcr_ref
	cmn_description:
		view:
			label: 'CMN Description'
			class: 'fdb-field'
			columns: 1

	hcpc_price:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			label: 'MCR Allowable / Unit'
			class: 'numeral'
			format: '$0,0.0000'
			columns: 4

	hcpc_modifier:
		model:
			max: 2
		view:
			label: 'Default Modifier'
			columns: 4
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Z]{2}$'
				error: 'Invalid Modifier, must be 2 upper case characters'
			}]

	hcpc_rental_modifier:
		model:
			max: 2
		view:
			label: 'Rental Modifier'
			columns: 4
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Z]{2}$'
				error: 'Invalid Modifier, must be 2 upper case characters'
			}]

	hcpc_purchase_modifier:
		model:
			max: 2
		view:
			label: 'Purchase Modifier'
			columns: 4
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Z]{2}$'
				error: 'Invalid Modifier, must be 2 upper case characters'
			}]

	accept_assignment:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Accept Assignment?'
			columns: 4

	hcpc_billable:
		model:
			source: ['Yes']
			default: 'Yes'
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Billable?'
			columns: 4

	hcpc_not_covered:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'MCR Not Covered?'
			columns: 4

	hcpc_span_dates:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'MCR Span Dates?'
			columns: 4

	hcpc_daily_bill:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Daily Bill?'
			columns: 4

	requires_cmn:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Requires CMN?'
			columns: 4

	embed_rental_item:
		model:
			multi: true
			sourcefilter:
				inventory_id:
					'dynamic': '{id}'
				active:
					'static': 'Yes'
		view:
			embed:
				form: 'inventory_rental_log'
				selectable: false
			grid:
				edit: true
				rank: 'none'
				add: 'none'
				fields: ['serial_no', 'status', 'patient_id', 'site_id', 'next_pm_date']
				label: ['Serial #', 'Status', 'In Use By', 'Site', 'Next PM']
				width: [15, 20, 25, 25, 15]
			label: 'Rental Items'

	embed_special_price:
		model:
			multi: true
			sourcefilter:
				inventory_id:
					'dynamic': '{id}'
		view:
			embed:
				query: 'inventory_special_prices'
				selectable: false
			grid:
				edit: true
				rank: 'none'
				add: 'none'
				fields: ['site', 'payer', 'formula_special', 'special_price', 'formula_expected', 'expected_price']
				label: ['Site', 'Payer', 'Spec Formula', 'Special $', 'Exp Formula', 'Expected $']
				width: [20, 20, 15, 15, 15, 15]
			label: 'Special Prices'

	embed_site_stock:
		model:
			multi: true
			sourcefilter:
				inventory_id:
					'dynamic': '{id}'
		view:
			embed:
				query: 'inventory_site_stock'
				selectable: false
			grid:
				edit: true
				rank: 'none'
				add: 'none'
				fields: ['site', 'on_hand', 'min_quantity', 'max_quantity', 'bin_location']
				label: ['Site', 'Quantity', 'Min Qty', 'Max Qty', 'Bin']
				width: [40, 15, 15, 15, 15]
			label: 'Site Info'

	adult_min:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Adult Min'
			readonly: true
			columns: 4

	adult_max:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Adult Max'
			readonly: true
			columns: 4

	ped_min:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Ped Min'
			readonly: true
			columns: 4

	ped_max:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Ped Max'
			readonly: true
			columns: 4

	product_type:
		model:
			source: ['Container', 'Diluent', 'Multi-Use', 'Primary Drug']
			required: false
		view:
			label: 'Product Type'
			control: 'radio'
			columns: 2

	dosage_form_type:
		model:
			source: ['Device', 'Enteral', 'Powder', 'Solution', 'Tablet/Capsule']
			if:
				'Solution':
					fields: ['concentration', 'concentration_unit', 'volume', 'volume_unit_id']
			required: false
		view:
			label: 'Dosage Form Type'
			control: 'radio'
			columns: 2

	type_id:
		model:
			required: true
			default: 'Primary'
			source: 'list_dispense_type'
			sourceid: 'code'
		view:
			label: 'Type Dispense'
			columns: 2

	volume:
		model:
			required: false
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
			min: 0.001
		view:
			label: 'Volume'
			class: 'numeral fdb-field'
			format: '0,0.[000000]'
			columns: 4

	volume_unit_id:
		model:
			source: 'list_unit'
			sourceid: 'code'
		view:
			class: 'fdb-field'
			label: 'Volume Unit'
			columns: 4

	concentration:
		model:
			required: false
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
			min: 0.001
		view:
			label: 'Concentration'
			class: 'numeral fdb-field'
			format: '0,0.[000000]'
			columns: 4

	concentration_unit:
		model:
			required: false
		view:
			label: 'Concentration Unit'
			class: 'fdb-field'
			columns: 4

	# list_fdb_route_to_clara.route_id
	# Linked from list_fdb_route_to_clara.ndc to list_fdb_ndc_to_route.clinical_rt_id
	# Then list_fdb_route_to_clara.rt_id = clinical_rt_id
	# NOTE: some drugs may have more than 1 route, just get the first one
	route_id:
		model:
			required: true
			source: 'list_route'
			sourceid: 'code'
			sourcefilter:
				code:
					'dynamic': '{available_routes}'
			if:
				'IV':
					prefill:
						rx_template_id: 'IV'
				'IV':
					prefill:
						rx_template_id: 'IV'
				'SQ':
					prefill:
						rx_template_id: 'IV'
				'IJ':
					prefill:
						rx_template_id: 'Injection'
				'PO':
					prefill:
						rx_template_id: 'PO'
		view:
			label: 'Default Route'
			class: 'fdb-field'
			columns: 2

	available_routes:
		model:
			multi: true
			required: false
			source: 'list_route'
			sourceid: 'code'
		view:
			label: 'Available Routes'
			class: 'fdb-field'
			columns: 2
			readonly: true
			offscreen: true

	therapy_id:
		model:
			max: 64
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Therapy'
			columns: 2

	rx_template_id:
		model:
			source: 'list_rx_template'
			sourceid: 'code'
			sourcefilter:
				active: 
					'static': 'Yes'
		view:
			label: 'RX Format'
			columns: 2

	is_reconstituted:
		model:
			required: true
			source: ['No', 'Yes']
			default: 'No'
			if:
				'Yes':
					fields: ['is_billed_compound']
		view:
			columns: 4
			control: 'radio'
			label: 'Does product require reconstituting?'

	requires_nursing:
		model:
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Requires nursing?'
			columns: 4

	auto_dc:
		model:
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Auto DC?'
			columns: 4

	is_specialty:
		model:
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Default Specialty?'
			note: 'If Pharmacy Payer is primary payer, follows SPRx Pathway on Rx Verification'
			columns: 4

	is_billed_compound:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 4
			control: 'radio'
			note: 'If No, ingredients will be billed in NCPDP compound segment but flagged as "Not a Compound" unless payer settings dictate ingredients billed on separate claims.'
			label: 'Is product billed as a compound?'

	dosing_unit_per_each:
		model:
			required: true
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
			min: 0.001
		view:
			class: 'numeral fdb-field'
			format: '0,0.[000000]'
			label: 'Dosing Unit Per Each'
			columns: 4

	default_dosing_unit_id:
		model:
			required: true
			source: 'list_unit'
			sourceid: 'code'
			sourcefilter:
				dose_unit:
					'static': 'Yes'
				active:
					'static': 'Yes'
				base_unit:
					'static': 'Yes'
		view:
			control: 'select'
			label: 'Default Dosing Unit'
			columns: 4.1
			class: 'select_prefill'

	sp_pk_indicator:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '429-DT'
		view:
			note: '429-DT'
			columns: 4
			label: 'Default Special Packaging Indicator'
			offscreen: true
			readonly: true
			class: 'claim-field'

	lot_tracking:
		model:
			source: ['Yes']
			default: 'Yes'
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Track Lots?'
			columns: 4

	serial_tracking:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Track Serial Numbers?'
			columns: 4

	#list_fdb_ndc.LN
	label_name:
		view:
			label: 'Label Name'
			class: 'fdb-field'
			columns: 2

	# list_fdb_storage_to_clara.storage_id
	# linked through list_fdb_ndc_attribute.ndc and
	# get record where attribute_type_cd = 0055 and attribute_val
	# then look up list_fdb_storage_to_clara.attribute_val in mapping table
	storage_id:
		model:
			source: 'list_storage'
			sourceid: 'code'
		view:
			label: 'Storage'
			columns: 2

	label_description:
		view:
			label: 'Drug Description'
			columns: 2

	warning_label:
		model:
			dynamic:
				source: 'list_label_header'
		view:
			label: 'Warning Label'
			columns: 2

	# This one is complex, you'll need the GCN_SEQNO from list_fdb_ndc.gcn_seqno
	# Linked from the NDC
	# Then fetch all entries from list_fdb_gcnseq_label_link where
	# list_fdb_gcnseq_label_link.gcn_seqno = list_fdb_ndc.gcn_seqno (also keep the lbl_prty from here)
	# Next, fetch the warning from list_fdb_warning_label_info where
	# list_fdb_warning_label_info.lbl_warn = list_fdb_gcnseq_label_link.lbl_warn
	# list_fdb_warning_label_info.lbl_desc = the warning description
	# list_fdb_warning_label_info.lbl_textsn = the sequence number (i.e. line 1,2,3)
	# and list_fdb_gcnseq_label_link.lbl_prty is sort priority (asc order)
	# Caution_label1-5 should be the first 5 LINES based on the priority. Do not
	# add a line if the full text won't fit (i.e. you have 3 lines left but the
	# the next label has 4 sequence numbers)
	embed_labels:
		model:
			sourcefilter:
				gcn_seqno:
					'dynamic': '{gcn_seqno}'
			multi: true
		view:
			embed:
				query: 'labels_embed_query'
				selectable: false
			grid:
				fields: ['lbl_prty', 'lbl_desc']
				label: ['Prority', 'Label Description']
				width: [15, 85]
				edit: false
			max_count: 4
			label: 'Caution Labels'
			validate: [
				name: 'SetCautionLabelsInv'
			]

	note:
		model:
			type: 'text'
		view:
			control: 'area'
			label: 'Notes'

	ful:
		model:
			type: 'decimal'
		view:
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			label: 'Federal Upper Limit'
			columns: 4

	avg_acq_cost_brand:
		model:
			type: 'decimal'
		view:
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			label: 'Ntl Avg Acq Cst Brd Drg'
			columns: 4

	avg_acq_cost_gen:
		model:
			type: 'decimal'
		view:
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			label: 'Ntl Avg Acq Cst Gen Drg'
			columns: 4

	last_update_at:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Last FDB Prices Update'
			readonly: true

	created_on:
		model:
			if:
				'*':
					sections: ['Suppliers', 'Special Prices', 'Site Info']
					fields: ['embed_supplier', 'embed_special_price', 'embed_site_stock']
		view:
			offscreen: true
			readonly: true

	created_by:
		model:
			if:
				'*':
					sections: ['Rental Items']
					fields: ['embed_rental_item']
		view:
			offscreen: true
			readonly: true

	embed_supplier:
		model:
			multi: false
			sourcefilter:
				inventory_id:
					'dynamic': '{id}'
		view:
			embed:
				query: 'inventory_suppliers'
				selectable: false
			grid:
				edit: false
				rank: 'none'
				add: 'none'
				fields: ['supplier', 'date', 'site', 'reorder_no', 'cost_ea']
				label: ['Supplier', 'Date', 'Site', 'Reorder #', 'Cost Ea']
				width: [20, 25, 20, 20, 15]
			label: 'Suppliers'
			readonly: true

	reorder_no:
		model:
			type: 'text'
		view:
			columns: 4
			label: 'Reorder #'

	form_id:
		view:
			offscreen: true
			readonly: true

	embed_supplier_pick:
		model:
			multi: false
			required: true
			sourcefilter:
				inventory_id:
					'dynamic': '{form_id}'
		view:
			embed:
				query: 'inventory_suppliers'
				selectable: true
			grid:
				edit: false
				rank: 'none'
				fields: ['supplier', 'date', 'site', 'reorder_no', 'cost_ea']
				label: ['Supplier', 'Date', 'Site', 'Reorder #', 'Cost Ea']
				width: [20, 25, 20, 20, 15]
			label: 'Suppliers'

	min_qty:
		model:
			type: 'int'
		view:
			columns: 4
			label: 'Min Quantity'

	max_qty:
		model:
			type: 'int'
		view:
			columns: 4
			label: 'Max Quantity'

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'inventory'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					direct_attachment: 'Yes'
					inventory_id: '{code}'
					assigned_to: 'Inventory Item'
					source: 'Scanned Document'
					form_code: '{code}'
					form_name: 'inventory'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['inventory']
	indexes:
		unique: [
			['fdb_id', 'external_id']
			['fdb_id', 'item_barcode']
			['fdb_id']
			['code']
		]
		many: [
			['gcn_seqno']
			['medid']
			['brand_name_id']
			['manufacturer_id']
			['billing_unit_id']
			['strength_unit_id']
			['volume_unit_id']
			['billable_code_id']
			['report_unit_id']
			['revenue_code_id']
			['price_code_id']
			['hcpc_id']
			['hcpc_code']
			['hcpc_unit']
			['hcpc_modifier']
			['hcpc_rental_modifier']
			['hcpc_purchase_modifier']
			['upc']
			['udi']
			['upin']
			['sku']
			['type_id']
			['route_id']
			['therapy_id']
			['external_id']
		]
	name: ['name']

	sections_group: [
		'Main':
			fields: ['created_on', 'code', 'type', 'fdb_id', 'use_dummy_ndc', 'active', 'name', 'supply_category_id', 'brand_name_id', 'additional_descriptor',
			'generic_name', 'manufacturer_id', 'lot_tracking', 'serial_tracking', 'min_qty', 'max_qty', 'description']
		'Item Info':
			hide_header: true
			indent: false
			tab: 'Item Info'
			fields: ['ndc', 'formatted_ndc', 'upc', 'upin', 'sku', 'udi', 'dea_schedule_id', 'gcn_seqno',
			'medid', 'therapy_class', 'rental_category_id','obsolete_date', 'speciality_flag', 'iv_container', 'iv_container_volume',
			'default_return_status', 'sales_type', 'requires_rental_purchase_letter', 'days_between_checks', 'days_between_pm',
			'compound_ingredient']
		'Supply Units':
			hide_header: true
			indent: false
			tab: 'Item Info'
			fields: ['purchase_unit', 'quantity_per_container', 'container_unit', 'each_per_container']
		'Units':
			hide_header: true
			indent: false
			tab: 'Item Info'
			fields: ['strength', 'strength_unit_id', 'quantity_each', 'metric_unit_each', 'billing_unit_id', 'report_unit_id',
			'quantity_per_package', 'package_unit', 'quantity_per_case', 'drug_category_id', 'sp_pk_indicator']
		'Barcode':
			hide_header: true
			indent: false
			tab: 'Item Info'
			fields: ['item_barcode', 'raw_barcode']
		'Compound':
			hide_header: true
			indent: false
			tab: 'Compound'
			fields: ['comp_dsg_fm_code', 'compound_type', 'comp_disp_unit']
		'Pricing':
			hide_header: true
			indent: false
			tab: 'Billing'
			fields: ['revenue_code_id', 'price_code_id',  'list_price','taxable', 'actual_cost', 'asp_price',
			'wac_price', 'wac_price_pkg', 'awp_price','awp_price_pkg', 'awp_basis', 'add_price1', 'add_price2', 'purchase_list_price', 'daily_rental_list_price',
			'monthly_rental_list_price', 'avg_acq_cost_brand', 'avg_acq_cost_gen', 'ful', 'purchase_cost', 'daily_rental_cost',
			'monthly_rental_cost', 'last_cost_ea', 'last_cost_pkg', 'last_cost_pu', 'last_cost_cont', 'primary_supplier_id', 'last_supplier_id', 'last_update_at']
		'HCPC':
			hide_header: true
			indent: false
			tab: 'Billing'
			fields: ['nursing_related', 'dme_related', 'initial_visit', 'nursing_hours', 'billable_code_id',
			'hcpc_id', 'hcpc_code', 'hcpc_quantity', 'hcpc_unit', 'cmn_description', 'hcpc_price', 'hcpc_modifier', 'hcpc_rental_modifier', 'hcpc_purchase_modifier',
			'hcpc_unit_limit', 'hcpc_limit_freq', 'accept_assignment', 'hcpc_billable', 'hcpc_not_covered', 'hcpc_span_dates', 'hcpc_daily_bill', 'requires_cmn']
		'Rental Items':
			hide_header: true
			indent: false
			tab: 'Rental Items'
			fields: ['embed_rental_item']
		'Special Prices':
			hide_header: true
			indent: false
			tab: 'Special Prices'
			fields: ['embed_special_price']
		'Site Info':
			hide_header: true
			indent: false
			tab: 'Site Info'
			fields: ['embed_site_stock']
		'Dosing':
			hide_header: true
			indent: false
			tab: 'Dispense/Dosing'
			fields: ['is_reconstituted', 'is_billed_compound', 'dosing_unit_per_each', 'default_dosing_unit_id']
		'Dispense':
			hide_header: true
			indent: false
			tab: 'Dispense/Dosing'
			fields: ['product_type', 'dosage_form_type', 'volume', 'volume_unit_id',
			'concentration_unit', 'concentration','type_id', 'route_id', 'available_routes', 'rx_template_id',
			'therapy_id', 'requires_nursing', 'auto_dc', 'is_specialty']
		'Label':
			hide_header: true
			indent: false
			tab: 'Label'
			fields: ['label_name', 'storage_id', 'label_description', 'warning_label', 'embed_labels']
		'Reorder':
			note: 'Select PO to Reorder'
			hide_header: true
			indent: false
			modal: true
			tab: 'Reorder'
			fields: ['form_id', 'embed_supplier_pick', 'reorder_no']
		'Suppliers':
			hide_header: true
			indent: false
			tab: 'Suppliers'
			fields: ['embed_supplier']
		'Notes':
			hide_header: true
			indent: false
			tab: 'Notes'
			fields: ['note']
		'Documents':
			hide_header: true
			indent: false
			tab: 'Assigned Documents'
			fields: ['embed_document']
	]

view:
	dimensions:
		width: '85%'
		height: '75%'
	hide_cardmenu: true
	comment: 'Manage > Inventory Item'
	find:
		basic: ['name', 'fdb_id', 'type', 'formatted_ndc', 'hcpc_id']
	grid:
		fields: ['name', 'type', 'hcpc_id'] # overriding it in react inventory.tsx
		sort: ['name']
	label: 'Inventory Item'
	open: 'read'
