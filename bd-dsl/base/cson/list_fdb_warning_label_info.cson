#TABLE: RLBLWD0_DESC
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#LANG_CD
	lang_cd:
		model:
			type: 'int'
		view:
			label: 'Language Code'
			readonly: true
			columns: 3

	# LBL_WARN
	lbl_warn:
		view:
			label: 'Prioritized Label Warning Code'
			readonly: true
			columns: 3

	# LBL_TEXTSN
	lbl_textsn:
		model:
			type: 'int'
		view:
			label: 'Prioritized Label Warning Text Sequence Number'
			readonly: true
			columns: 3

	# LBL_DESC
	lbl_desc:
		view:
			label: 'Prioritized Label Warning Code Description'
			readonly: true
			columns: 3

	# LBLGNDR
	lblgndr:
		view:
			label: 'Prioritized Label Warning Gender-Specific Text Indicator'
			readonly: true
			columns: 3

	# LBLAGE
	lblage:
		view:
			label: 'Prioritized Label Warning Stage of Life-Specific Text Indicator'
			readonly: true
			columns: 3

	# LBLPREG
	lblpreg:
		view:
			label: 'Prioritized Label Warning Pregnancy-Specific Text Indicator'
			readonly: true
			columns: 3

	# LBLINFO
	lblinfo:
		view:
			label: 'Prioritized Label Warning Informational Text-Only Indicator'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['lbl_warn', 'lbl_textsn', 'lbl_desc']
	indexes:
		many: [
			['lbl_warn']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['lbl_warn', 'lbl_textsn', 'lbl_desc', 'lblgndr', 'lblage', 'lblpreg', 'lblinfo']

view:
	comment: 'Manage > List FDB Prioritized Label Warning'
	find:
		basic: ['lbl_warn']
	grid:
		fields: ['lbl_warn', 'lbl_textsn', 'lbl_desc']
	label: 'List FDB Prioritized Label Warning'
