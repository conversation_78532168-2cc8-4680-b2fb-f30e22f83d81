#TABLE: RDAMXSH0_ALRGN_XSENSE_HIST
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#REPL_DAM_ALRGN_XSENSE
	repl_dam_alrgn_xsense:
		model:
			type: 'int'
		view:
			label: 'Replacement DAM Cross-Sensitive Allergen Group Code	'
			readonly: true
			columns: 2

	#PREV_DAM_ALRGN_XSENSE
	prev_dam_alrgn_xsense:
		model:
			type: 'int'
		view:
			label: 'Previous DAM Cross-Sensitive Allergen Group Code'
			readonly: true
			columns: 2

	#DAM_ALRGN_XSENSE_REPL_EFF_DT
	dam_alrgn_xsense_repl_eff_dt:
		model:
			type: 'int'
		view:
			label: 'DAM Cross-Sensitive Allergen Group Code Replacement Effective Date	'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{repl_dam_alrgn_xsense} {prev_dam_alrgn_xsense} {dam_alrgn_xsense_repl_eff_dt}"
	indexes:
		many: [
			['repl_dam_alrgn_xsense']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['repl_dam_alrgn_xsense', 'prev_dam_alrgn_xsense', 'dam_alrgn_xsense_repl_eff_dt']

view:
	comment: 'Manage > List FDB DAM Cross-Sensitive Allergen Group Code History Table'
	find:
		basic: ['repl_dam_alrgn_xsense', 'prev_dam_alrgn_xsense', 'dam_alrgn_xsense_repl_eff_dt']
	grid:
		fields: ['repl_dam_alrgn_xsense', 'prev_dam_alrgn_xsense', 'dam_alrgn_xsense_repl_eff_dt']
	label: 'List FDB DAM Cross-Sensitive Allergen Group Code History Table'
