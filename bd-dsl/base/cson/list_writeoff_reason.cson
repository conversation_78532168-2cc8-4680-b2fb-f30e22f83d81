fields:
	code:
		model:
			required: true
		view:
			columns: 3
			label: 'Code'
			findunique: true

	name:
		model:
			required: true
		view:
			columns: 3
			label: 'Name'
			findunique: true

	bad_debt:
		model:
			source: ['Yes']
		view:
			columns: 3
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Bad Debt?'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 3
			control: 'radio'
			label: 'Can Sync Record'

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 3
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['lists']
	sync_mode: 'mixed'
	indexes:
		many: [
			['code']
			['bad_debt']
		]
		unique: [
			['code', 'name']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'bad_debt', 'allow_sync', 'active']

view:
	comment: 'Manage > Writeoff Reason'
	find:
		basic: ['code', 'name', 'bad_debt']
	grid:
		fields: ['code', 'name', 'bad_debt']
		sort: ['name']
	label: 'Writeoff Reason'
	open: 'read'
