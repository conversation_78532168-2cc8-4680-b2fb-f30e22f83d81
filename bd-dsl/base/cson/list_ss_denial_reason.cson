fields:

	name:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 2

	code:
		model:
			required: true
		view:
			label: 'Code'
			findunique: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name']

view:
	comment: 'Manage > Surescripts Denial Reason'
	find:
		basic: ['code', 'name']
	grid:
		fields: ['code', 'name']
		sort: ['name']
	label: 'Surescripts Denial Reason'
	open: 'read'
