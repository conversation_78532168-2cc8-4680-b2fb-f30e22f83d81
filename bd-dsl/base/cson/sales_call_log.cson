fields:
	call_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Call Date'
			findrange: true
			columns: 3

	sales_rep_name:
		view:
			label:'Sales Rep'
			readonly: true
			template: '{{user.displayname}}'
			findunique: true
			columns: 3

	sales_account_id:
		model:
			required: false
			source: 'sales_account'
			type: 'int'
			if:
				'*':
					fields: ['phy_id', 'contacts_id']
		view:
			label: 'Sales Account'
			columns: 3

	phy_id:
		model:
			multi: true
			source: 'sales_account_physician'
			sourcefilter:
				sales_account_id:
					'dynamic': '{sales_account_id}'
		view:
			label: 'Physicians'
			columns: -1

	contacts_id:
		model:
			multi: true
			source: 'sales_account_contact'
			sourcefilter:
				sales_account_id:
					'dynamic': '{sales_account_id}'
		view:
			label: 'Contacts'
			columns: -1

	physician_name:
		view:
			label: 'Physician'
			findunique: true
			offscreen: true
			readonly: true
			columns: 3

	patient:
		model:
			type: 'int'
			source: 'patient'
		view:
			label: 'Patient'
			columns: 3

	prospect:
		model:
			required: false
		view:
			label: 'Prospect Name'
			findunique: true
			columns: 3

	client:
		model:
			required: false
		view:
			control: 'area'
			label: 'Note'
			columns: -1
	
	client_type:
		model:
			required: true
			source: ['Active', 'Chapter', 'Coworker', 'HTC', 'Manufacturer', 'Prospect', 'Patient', 'Supervisor', 'Physician']
		view:
			label: 'Client Type'
			columns: 3
	
	event_activity:
		model:
			source: ['Conference','Email','Lunch/Dinner',
                    'Meeting','Phone Call', 'Video Meeting', 'Talk/Text','Visit']
		view:
			label: 'Event/Activity'
			columns: 3
	
	status:
		model:
			source:['Active','Prospect']
			required: true
		view:
			class: 'status'
			label: 'Status'
			columns: 3

	objective:
		model:
			source:['Check In','Follow Up','Set Up Meeting']
		view:
			label: 'Objective'
			columns: 3
    
	rating:
		model:
			source:['A','B','C']
		view:
			label: 'Rating'
			columns: 3

	followup_date:
		model:
			type: 'date'
			required: false
		view:
			label: 'Follow Up Date'
			readonly: true
			note: 'Please Use Follow-up Task Below'
			columns: 3

	followup_comments:
		model:
			required: false
		view:
			control: 'area'
			label: 'Follow Up Comments'
			note: 'Please Use Follow-up Task Below'
			columns: -1

	time_range:
		model:
			source:['Last Week', 'Last Month', 'Last Quarter']
		view:
			offscreen:true
			label: 'Time Range'
			control: 'select'
			columns: 3

	subform_followup:
		model:
			multi: true
			source: 'sales_task'
			type: 'subform'
		view:
			label: 'Follow-Up Tasks'

	import_id:
		model:
			required: false
			source: 'sales_call_import'
		view:
			readonly: true
			offscreen: true
			columns: 3

	date_contacted:
		model:
			required: true
			type: 'date'
		view:
			label: 'Date Contacted'
			template: '{{now}}'
			columns: 2

	method:
		model:
			source: ['Call', 'Email', 'Letter', 'Other']
			if:
				'Other':
					fields: ['method_description']
		view:
			control: 'radio'
			label: 'Contact Methods'
			columns: 2

	method_description:
		model:
			required: true
		view:
			label: 'Details:'
			control: 'area'
			columns: 3

	gift:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['gift_details']
					sections: ['Courtesy Details']
		view:
			control: 'radio'
			label: 'Gift Included?'
			columns: -2

	gift_details:
		model:
			required: true
		view:
			label: 'Details:'
			columns: 2

	response:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['response_rating']
		view:
			control: 'radio'
			label: 'Received Response/Attempt Successful?'
			columns: -2

	response_rating:
		model:
			required: true
			source: ['1', '2', '3', '4', '5']
		view:
			note: '1 = Poor Response - 5 = Great Response'
			control: 'radio'
			label: "Response Rating"
			columns: 2

	notes:
		view:
			control: 'area'
			label: 'Notes'
			columns: -1

	date_activity:
		model:
			required: true
			type: 'date'
		view:
			label: 'Date Contacted'
			template: '{{now}}'
			columns: 3

	description:
		view:
			control: 'area'
			label: 'Description'
			columns: -1

	task:
		view:
			label: 'Follow-up Tasks'
			columns: 3

	reminder_date:
		model:
			type: 'date'
		view:
			label: 'Reminder Date'
			columns: 3

	date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Date'
			template: '{{now}}'
			findrange: true
			columns: 2

	type:
		model:
			required: true
			source: ['Referral Source', 'Non-Referral Source','Patient']
			if:
				'Referral Source':
					fields: ['phys_in_office','non_presc',
					'non_presc_num','purpose','courtesy_provided','num_individuals',
					'cost','cost_per_provider','tot_year']
				'Non-Referral Source':
					fields: ['company_name', 'employer', 'employer_location',
					'courtesy_provided', 'location',
					'cost', 'individuals', 'topic']
				'Patient':
					fields: ['pat_type', 'pat_id','provided','location','cost',
					'individuals','topic','tot_year_pt']
		view:
			label: 'Type'
			control: 'radio'
			columns: 2

	dr_id:
		model:
			multi: true
			required: true
			source: 'sales_account_physician'
			sourcefilter:
				sales_account_id:
					'dynamic': '{sales_account_id}'
		view:
			label: 'Physicians'
			columns: -1

	pat_type:
		model:
			required: true
			source: ['Active', 'Prospect']
			if:
				'Active':
					require_fields: ['pat_id']
				'Prospect':
					fields: ['pat_name']
		view:
			label: 'Type'
			control: 'radio'
			columns: 3

	pat_id:
		model:
			source: 'patient'
		view:
			label: 'Patient'
			columns: 3

	pat_name:
		model:
			required: true
		view:
			label: 'Patient Name'
			columns: 3

	phys_in_office:
		model:
			required: true
			type: 'int'
		view:
			label: '# of Physicians at Office'
			note: '(that location only)'
			columns: 3

	non_presc:
		model:
			required: false
		view:
			label: 'Non-Physician Prescribers'
			note: '(that location only)'
			columns: 3

	non_presc_num:
		model:
			required: false
			type: 'int'
		view:
			label: '# of Non-Physician Prescribers'
			note: '(that location only)'
			columns: 3
	
	purpose:
		model:
			required: true
		view:
			label: 'Purpose of Meeting'
			columns: 3
	
	courtesy_provided:
		model:
			required: true
		view:
			label: 'Courtesy Provided'
			note: '(include location if other than office)'
			columns: 3

	provided:
		model:
			required: true
		view:
			label: 'What was provided?'
			columns: 3

	num_individuals:
		model:
			required: true
			type: 'int'
		view:
			label: '# of Individuals Present'
			columns: 3

	individuals:
		model:
			required: true
		view:
			label: 'Individuals Present'
			columns: 3

	topic:
		model:
			required: true
		view:
			label: 'Topic of Discussion'
			columns: 3

	company_name:
		model:
			required: true
		view:
			label: 'Company Name'
			columns: 3

	employer:
		model:
			required: true
		view:
			label: 'Employer'
			columns: 3

	employer_location:
		model:
			required: true
		view:
			label: 'Employer Location'
			columns: 3

	location:
		model:
			required: true
		view:
			label: 'Location of Meeting'
			columns: 3

	cost:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Cost'
			columns: 3

	cost_per_provider:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Cost Per Provider'
			readonly: true
			columns: 3

	tot_year:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Total for Year as of this Date'
			readonly: true
			columns: 3

	tot_year_pt:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Total Spent on this Patient this Year'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison']
		delete:     ['admin']
		read:       ['admin', 'csr', 'liaison']
		read_all:   ['admin', 'csr', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'liaison']
		write:      ['admin', 'csr', 'liaison']
	reportable: true
	indexes:
		unique: [
			['sales_rep_name', 'sales_account_id']
		]
	name: ['sales_account_id', 'sales_rep_name', 'call_date']
	sections_group: [
		'Call Log':
			sections: [
				'Call Information':
					fields: ['call_date','sales_rep_name', 'sales_account_id', 'patient', 'prospect', 'phy_id',
					'contacts_id','client','client_type',
					'event_activity','status','objective','rating','followup_date','followup_comments']
				'Follow Up':
					fields: ['subform_followup']
				'Contact Log':
					fields: ['date_contacted', 'method', 'method_description', 'gift', 'gift_details', 'response', 'response_rating', 'notes']
				'Activity Log':
					fields: ['date_activity', 'task', 'reminder_date', 'description']
				'Courtesy Details':
					fields: ['date', 'type', 'dr_id', 'pat_type', 'pat_name', 'pat_id', 'phys_in_office',
							'non_presc', 'non_presc_num', 'purpose', 'courtesy_provided', 'provided',
							'num_individuals', 'individuals', 'topic','company_name', 'employer',
							'employer_location', 'location', 'cost', 'cost_per_provider', 'tot_year', 'tot_year_pt']
			]
	]

view:
	comment: 'Manage > Call_Log'
	find:
		basic: ['call_date', 'sales_rep_name','sales_account_id', 'phy_id']
		advanced: ['contacts_id', 'patient', 'prospect', 'client_type','rating', 'time_range']
	grid:
		fields: ['call_date','sales_rep_name', 'sales_account_id', 'phy_id', 'client', 'client_type', 'event_activity', 'prospect']
		sort: ['-call_date','sales_rep_name','phy_id','event_activity']
	label: 'Call Log'
