fields:
	patient_id:
		model:
			prefill: ['parent.patient_id']
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	dx_id:
		model:
			type: 'int'
			required: true
			source: 'patient_diagnosis'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			columns: 3
			label: 'Diagnosis'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_diagnosis/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'dx_code': ['dx_id']
			]

	dx_code_qualifier:
		model:
			default: '02' # ICD-10
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '492-WE'
		view:
			columns: 3
			reference: '492-WE'
			note: '492-WE'
			label: 'Diagnosis Qualifier'
			readonly: true

	dx_code:
		model:
			max: 15
		view:
			columns: 3
			reference: '424-DO'
			note: '424-DO'
			label: 'Diagnosis Code'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Z][0-9][0-9A-Z](\.[0-9A-Z]{1,4}|[0-9A-Z]{1,4})$'
				error: 'Diagnosis Code must be 3-5 alphanumeric characters with optional period and 1-4 additional characters"'
			}]

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
			['dx_id']
		]

	name: ['patient_id', 'dx_id', 'dx_code_qualifier', 'dx_code']
	sections:
		'Patient Diagnosis':
			hide_header: true
			indent: false
			fields: ['patient_id', 'dx_id', 'dx_code_qualifier', 'dx_code']
view:
	dimensions:
		width: '50%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Patient Diagnosis'
	grid:
		fields: ['dx_id', 'dx_code_qualifier', 'dx_code']
		sort: ['-created_on']
	label: 'Patient Diagnosis'
	open: 'read'