fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	external_id:
		view:
			label: 'External Id'
			readonly: true
			offscreen: true

	rx_no:
		model:
			if:
				'*':
					fields: ['type_id']
					readonly:
						fields: ['fdb_id', 'type_id', 'medication_dose', 'medication_frequency', 'prescribed_by',
						'prescribed_by_id', 'start_date', 'reported_by', 'status', 'end_date']
				'!':
					fields: ['reported_by']
					prefill:
						status: 'Active'
		view:
			label: ''
			readonly: true

	fdb_id:
		model:
			required: true
			source: 'list_fdb_ndc'
			sourceid: 'code'
			sourcefilter:
				inpcki:
					'static': '!1'
		view:
			note: 'Must be outer package NDC'
			label: 'Master Drug Item'
			columns: 3
			class: 'select_prefill fdb-field'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_fdb_ndc/?limit=1&fields=list&sort=name&page_number=0&filter=code:'
				fields:
					'brand_name_id': ['bn']
			]

	brand_name_id:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
			required: true
		view:
			label: 'Short Brand Name'
			columns: 3
			readonly: true
			offscreen: true
			class: 'fdb-field'

	type_id:
		model:
			required: false
			default: 'Primary'
			source: 'list_dispense_type'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Type Dispense'

	medication_dose:
		model:
			required: true
		view:
			columns: 3
			note: 'How much do you take?'
			label: 'Medication Dose'

	medication_frequency:
		model:
			max: 256
		view:
			columns: 3
			note: 'How often do you take it'
			label: 'Medication Frequency'

	prescribed_by:
		model:
			source: ['Self-Prescribed', 'Physician']
			if:
				'Physician':
					fields: ['prescribed_by_id']
			max: 256
		view:
			columns: 3
			control: 'radio'
			note: 'Who told you to take this medication'
			label: 'Prescribed By'

	prescribed_by_id:
		model:
			required: true
			source: 'patient_prescriber'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			columns: 3
			label: 'Prescribing Physician'

	reported_by:
		model:
			required: true
			source: ['Physician', 'Patient Reported']
		view:
			columns: 3
			label: 'Reported By'

	note:
		model:
			access:
				read: ['patient']
				write: ['cm', 'cma', 'liaison', 'nurse', 'patient']
		view:
			label: 'Notes'
			control: 'area'

	status:
		model:
			source: ['Active', 'Discontinued']
			if:
				'Discontinued':
					fields: ['start_date', 'end_date', 'discontinued_reason']
				'Active':
					fields: ['start_date']
		view:
			class: 'status'
			columns: 3
			findfilter: 'Active'
			control: 'radio'
			label: 'Status'

	start_date:
		model:
			type: 'date'
		view:
			columns: 3
			note: 'The approximate date you started taking the medication'
			label: 'Start Date'

	end_date:
		model:
			type: 'date'
		view:
			columns: 3
			note: 'The date you stopped taking the medication'
			label: 'End Date'

	discontinued_reason:
		model:
			required: true
			source: 'list_dc_reason'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Discontinue Reason'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     ['patient']
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['patient']
		review:     ['admin', 'pharm']
		update:     ['cm', 'cma', 'liaison', 'nurse', 'patient']
		update_all: ['admin', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
			['fdb_id']
			['rx_no']
		]
	sections:
		'Medication':
			fields: ['rx_no', 'fdb_id', 'medication_dose', 'medication_frequency', 'prescribed_by',
			'prescribed_by_id', 'reported_by','status', 'start_date', 'end_date', 'discontinued_reason', 'note', 'brand_name_id']
	name: '{brand_name_id} {medication_dose} {medication_frequency}'

view:
	dimensions:
        width: '75%'
        height: '75%'
	hide_cardmenu: true
	comment: 'Patient > Medication Profile'
	find:
		basic: ['fdb_id', 'reported_by','status']
	grid:
		fields: ['created_on', 'fdb_id', 'medication_dose', 'medication_frequency', 'reported_by', 'status']
		sort: ['created_on', 'fdb_id', 'medication_dose']
	icon: 'medication'
	label: 'Medication Profile'
	open: 'read'