fields:

	patient_id:
		model:
			type: 'int'
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	site_id:
		model:
			type: 'int'
			source: 'site'
			prefill: ['parent.site_id']
		view:
			label: 'Site'
			readonly: true
			offscreen: true

	billing:
		model:
			required: true
			type: 'subform'
			multi: false
			source: 'med_claim_prov_bill'
		view:
			label: 'Billing Provider'
			reference: '2010AA'

	referring:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_prov_ref'
		view:
			label: 'Referring Provider'
			reference: '2310A'

	ordering:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_prov_ord'
		view:
			label: 'Referring Provider'
			reference: '2420E'

	rendering:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_prov_rend'
		view:
			label: 'Rendering Provider'
			reference: '2310B'

	supervising:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_prov_sup'
		view:
			label: 'Supervising Provider'
			reference: '2310D'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['id','created_by','created_on']
	sections_group: [
		'Providers':
			hide_header: true
			sections: [
				'Main':
					hide_header: true
					indent: false
					fields: ['patient_id', 'site_id']
				'Billing Provider':
					hide_header: true
					indent: false
					tab: 'Billing'
					fields: ['billing']
				'Referring Provider':
					hide_header: true
					indent: false
					tab: 'Referring'
					tab_toggle: true
					fields: ['referring']
				'Ordering Provider':
					hide_header: true
					indent: false
					tab: 'Ordering'
					tab_toggle: true
					fields: ['ordering']
				'Rendering Provider':
					hide_header: true
					indent: false
					tab: 'Rendering'
					tab_toggle: true
					fields: ['rendering']
				'Supervising Provider':
					hide_header: true
					indent: false
					tab: 'Supervising'
					tab_toggle: true
					fields: ['supervising']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Providers'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Providers'
	open: 'read'