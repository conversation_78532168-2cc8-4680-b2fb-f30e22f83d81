fields:
	rep_id:
		model:
			required: true
			source: 'user'
			sourcefilter:
				role:
					'static': ['liaison']
		view: 
			label: 'Sales Rep'

	progress_note:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Progress Note'

	allergy:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Allergies'

	medication:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Medications'

	Orders:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Orders'

	dispense:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Dispense'

	prior_auth:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Insurance Auth' # triggers notification for patient_insurance_authorization events

	intervention:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Interversions'

	insurance:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Insurance'

	external_document:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'External Documents'

	new_Careplan_referral:
		model:
			source: ['No', 'Yes']
			default: 'Yes'
		view:
			control: 'radio'
			label: 'Careplan Referral'

	issue_ticket:
		model:
			source: ['No', 'Yes']
			default: 'Yes'
		view:
			control: 'radio'
			label: 'Insurance'

model:
	access:
		create: ['admin', 'liaison']
		create_all: ['admin', 'liaison']
		delete: ['admin', 'liaison']
		read: ['admin', 'liaison']
		read_all: ['admin', 'liaison']
		request: ['admin', 'liaison']
		update: ['admin', 'liaison']
		update_all: ['admin', 'liaison']
		write: ['admin', 'liaison']
	name: '{rep_id}'
	indexes:
		unique: [
			['rep_id']
		]
	sections:
		'Notifcations For':
			fields: ['rep_id']
		'Patient Notifications':
			fields: ['progress_note', 'allergy', 'medication', 'Orders', 'dispense', 'prior_auth', 'intervention', 'insurance', 'external_document']
		'General Notifications':
			fields: ['new_Careplan_referral','issue_ticket']
view:
	comment: 'Sales Notification'
	grid:
		fields: ['rep_id']
	label: 'Sale Notifications'
	open: 'read'
