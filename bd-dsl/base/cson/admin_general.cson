fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			required: false
			source: 'careplan_order'
			type: 'int'
		view:
			label: 'Referral Id'

	delivery_ticket_id:
		model:
			required: true
			source: 'careplan_delivery_tick'
			type: 'int'
		view:
			label: 'Dilvery Tick Id'

	# Admin Step
	start_time:
		model:
			required: true
			type: 'time'
		view:
			label: 'Time Started'
			template: '{{now}}'

	comment:
		model:
			max: 4096
		view:
			control: 'area'
			label: "Administration Step Comment"

	# Primary Admin
	primary_admin_id:
		model:
			required: true
			type: 'int'
			source: 'patient_medication'
			sourcefilter:
				type_id:
					'static': 'Primary'
				dt_id:
					'dynamic': '{delivery_ticket_id}'
			if:
				'*':
					fields: ['dose_given','lot_details']
		view:
			control: 'select'
			label: 'Primary Drug Administered'

	#TODO Need to auto-populate units from primary drug in label
	dose_given:
		model:
			required: true
			rounding: 0.05
			type: 'decimal'
		view:
			label: 'Dose Given (units)'

	#TODO Pull in drug lot details and have them check it with a number?
	lot_details:
		model:
			subfields:
				lot:
					label: 'Drug Lot#'
				expiration:
					label: 'Expiration Date'
					type: 'date'
			type: 'json'
		view:
			control: 'grid'
			label: 'Drug/Vial Details'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		admin_general:
			link:
				careplan_id: 'careplan_id'
			max: 'id'
	name: '{start_time} : {dose_given}'
	sections:
		'Admin Step':
			fields: ['start_time']
		'Primary Administration':
			note: 'Check the drug label with the order before administering'
			fields: ['primary_admin_id','dose_given', 'lot_details']
		'Admin Step Comment':
			fields: ['comment']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Encounter > General > Admin'
	grid:
		fields: ['start_time', 'primary_admin_id', 'dose_given', 'comment']
	label: 'General Admin Step'
