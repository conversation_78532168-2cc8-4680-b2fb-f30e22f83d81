fields:
	patient_id:
		model:
			type: 'int'
			required: false
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	insurance_id:
		model:
			type: 'int'
			required: false
			source: 'patient_insurance'
			prefill: ['parent.insurance_id']
		view:
			label: 'Insurance'
			readonly: true
			offscreen: true

	claim_number:
		model:
			default: '0000'
			required: false
			min: 1
			max: 50
		view:
			columns: 3
			label: 'Claim #'
			note: 'Generated at the time of saving'
			reference: 'REF02 REF01=D9'
			readonly: true
			_meta:
				location: '2300 REF'
				field: '02'
				code: 'D9'
				path: 'claimInformation.claimSupplementalInformation.claimNumber'

	claim_control_number:
		model:
			min: 1
			max: 50
		view:
			columns: 3
			note: 'Assigned by the payer'
			label: 'Claim Control Number (CCN)'
			reference: 'REF02 REF01=F8'
			_meta:
				location: '2300 REF'
				field: '02'
				code: 'F8'
				path: 'claimInformation.claimSupplementalInformation.claimControlNumber'

	medicare_crossover_reference_id:
		model:
			min: 1
			max: 50
		view:
			columns: 3
			label: 'MCR Crossover Reference ID'
			note: 'Assigned by MCR in a COB scenario'
			reference: 'REF02 REF01=F5'
			_meta:
				location: '2300 REF'
				field: '02'
				code: 'F5'
				path: 'claimInformation.claimSupplementalInformation.medicareCrossoverReferenceId'

	pa_id:
		model:
			type: 'int'
			source: 'patient_prior_auth'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': '5'
				insurance_id:
					'dynamic': '{insurance_id}'
		view:
			columns: -3
			label: 'Prior Auth'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_prior_auth/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'prior_authorization_number': ['number']
			]

	prior_authorization_number:
		model:
			min: 1
			max: 50
		view:
			columns: 3
			label: 'Prior Auth #'
			reference: 'REF02 REF01=G1'
			_meta:
				location: '2300 REF'
				field: '02'
				code: 'G1'
				path: 'claimInformation.claimSupplementalInformation.priorAuthorizationNumber'

	service_authorization_exception_code:
		model:
			required: false
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'REF02-4N'
		view:
			readonly: true
			offscreen: true
			label: 'Service Auth Exception Code'
			reference: 'REF02 REF01=4N'
			_meta:
				location: '2300 REF'
				field: '02'
				code: '4N'
				path: 'claimInformation.claimSupplementalInformation.serviceAuthorizationExceptionCode'

	repriced_claim_number:
		model:
			min: 1
			max: 50
		view:
			label: 'Repriced Claim #'
			note: 'Assigned by the Repricer (i.e. PPO)'
			offscreen: true
			readonly: true
			reference: 'REF02 REF01=9A'
			_meta:
				location: '2300 REF'
				field: '02'
				code: '9A'
				path: 'claimInformation.claimSupplementalInformation.repricedClaimNumber'

	adjusted_repriced_claim_number:
		model:
			min: 1
			max: 50
		view:
			columns: 3
			offscreen: true
			readonly: true
			label: 'Adj Repriced Claim #'
			note: 'Assigned by the Repricer (i.e. PPO)'
			reference: 'REF02 REF01=9C'
			_meta:
				location: '2300 REF'
				field: '02'
				code: '9C'
				path: 'claimInformation.claimSupplementalInformation.adjustedRepricedClaimNumber'

	medical_record_number:
		model:
			prefill: ['patient.mrn']
			min: 1
			max: 50
		view:
			columns: 3
			label: 'MRN'
			reference: 'REF02 REF01=EA'
			readonly: true
			_meta:
				location: '2300 REF'
				field: '02'
				code: 'EA'
				path: 'claimInformation.claimSupplementalInformation.medicalRecordNumber'

	report_information:
		model:
			multi: true
			source: 'med_claim_report'
			type: 'subform'
		view:
			note: 'Max 1'
			label: 'Attachment'
			grid:
				add: 'inline'
				edit: true
				fields: ['attachment_report_type_code', 'attachment_control_number']
				label: ['Type','#']
				width: [70, 30]
			max_count: 1

model:
	prefill:
		patient:
			link:
				id: 'patient_id'
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['patient_id', 'insurance_id']
	indexes:
		many: [
			['patient_id']
			['insurance_id']
			['claim_number']
			['claim_control_number']
		]

	sections_group: [
		'Supplemental':
			hide_header: true
			sections: [
				'Supplemental Information':
					hide_header: true
					fields: ['patient_id', 'insurance_id', 'claim_number',
					'claim_control_number', 'medicare_crossover_reference_id', 'pa_id', 'prior_authorization_number',
					'service_authorization_exception_code', 'repriced_claim_number',
					'adjusted_repriced_claim_number', 'medical_record_number',
					]
				'Report Information':
					indent: false
					fields: ['report_information']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	reference: '2300'
	comment: 'Supplemental'
	hide_header: true
	grid:
		fields: ['claim_number', 'claim_control_number', 'prior_authorization_number', 'medical_record_number']
		width: [30, 30, 20, 20]
		sort: ['-created_on']
	label: 'Supplemental'
	open: 'read'