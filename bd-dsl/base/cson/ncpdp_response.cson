fields:

	bin_number:
		model:
			required: true
			min: 6
			max: 6
		view:
			columns: 4
			label: 'BIN'
			note: '101-A1'
			reference: '101-A1'
			readonly: true
			offscreen: true

	patient_id:
		model:
			type: 'int'
			required: true
			source: 'patient'
		view:
			label: 'Patient ID'
			readonly: true
			offscreen: true

	claim_no:
		view:
			label: 'Claim #'
			note: 'Claim # against the invoice'
			readonly: true
			offscreen: true

	response_status:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '501-F1'
		view:
			columns: 3
			note: '501-F1'
			label: 'Response Status'
			readonly: true
			offscreen: true

	version_number:
		model:
			default: "D0"
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '102-A2'
		view:
			columns: 3
			label: 'NCPDP Version'
			note: '102-A2'
			readonly: true
			offscreen: true

	transaction_code:
		view:
			columns: 3
			label: 'Transaction Code'
			note: '103-A3'
			readonly: true
			offscreen: true
			transform: [
				{
					name: "UpdateFormLabel"
					if:
						'B1': 'NCPDP Pharmacy Drug Claim Response'
						'B2': 'NCPDP Pharmacy Drug Claim Reversal Response'
						'B3': 'NCPDP Pharmacy Drug Claim Rebill Response'
						'S1': 'NCPDP Pharmacy Service Claim Response'
						'S2': 'NCPDP Pharmacy Service Claim Reversal Response'
						'S3': 'NCPDP Pharmacy Service Claim Rebill Response'
						'E1': 'NCPDP E1 Eligibility Response'
				}
			]
			validate: [{
				name: 'RemoveSectionTabs'
			}]

	service_provider_qualifier:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '202-B2'
		view:
			columns: 3
			note: '202-B2'
			label: 'Service Provider ID Qualifier'
			readonly: true
			offscreen: true

	service_provider_id:
		view:
			columns: 3
			note: '201-B1'
			label: 'Service Provider ID'
			readonly: true
			offscreen: true

	date_of_service:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Date of Service'
			note: '401-D1'
			readonly: true
			offscreen: true

	response_msg:
		model:
			multi: false
			type: 'subform'
			source: 'ncpdp_response_msg'
		view:
			grid:
				edit: false
				add: 'none'
				fields: ['message']
				label: ['Msg']
			label: 'Message'
			readonly: true

	response_insur:
		model:
			multi: false
			type: 'subform'
			source: 'ncpdp_response_insur'
		view:
			label: 'Insurance'
			readonly: true
			offscreen: true

	response_docs:
		model:
			multi: false
			type: 'subform'
			source: 'ncpdp_response_insur_add'
		view:
			label: 'Additional Insurance'
			readonly: true

	response_pat:
		model:
			multi: false
			type: 'subform'
			source: 'ncpdp_response_pt'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	response_stat:
		model:
			multi: false
			type: 'subform'
			source: 'ncpdp_response_stat'
		view:
			class: 'status'
			label: 'Status'
			readonly: true

	response_claim:
		model:
			multi: false
			type: 'subform'
			source: 'ncpdp_response_clm'
		view:
			label: 'Claim'
			readonly: true
			offscreen: true

	response_pricing:
		model:
			multi: false
			type: 'subform'
			source: 'ncpdp_response_prc'
		view:
			label: 'Pricing'
			readonly: true

	response_dur:
		model:
			multi: true
			type: 'subform'
			source: 'ncpdp_response_dur'
		view:
			label: 'DUR/PPS'
			readonly: true
			offscreen: true

	response_cob:
		model:
			multi: true
			type: 'subform'
			source: 'ncpdp_response_cob'
		view:
			label: 'COB'
			readonly: true
			offscreen: true

	show_segments:
		model:
			multi: true
			source: ['Insurance', 'Insurance Additional Information', 'Claim', 'Pricing', 'DUR/PPS', 'COB', 'Patient', 'Message']
			if:
				'Insurance':
					sections: ['Insurance']
					fields: ['response_insur']
				'Insurance Additional Information':
					sections: ['Insurance Additional Information']
					fields: ['response_docs']
				'Claim':
					sections: ['Claim']
					fields: ['response_claim']
				'Pricing':
					sections: ['Pricing']
					fields: ['response_pricing']
				'DUR/PPS':
					sections: ['DUR/PPS']
					fields: ['response_dur']
				'COB':
					sections: ['COB']
					fields: ['response_cob']
				'Patient':
					sections: ['Patient']
					fields: ['response_pat']
				'Message':
					sections: ['Message']
					fields: ['response_msg']
		view:
			label: 'Show Segments'
			readonly: true
			offscreen: true 
			validate: [{
				name: 'RemoveSectionTabs'
			}]

	# Original Submitted Values
	ing_cst_sub:
		model:
			required: true
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			min: 0.00
		view:
			columns: 3
			reference: '409-D9'
			note: '409-D9'
			label: 'Ingredient Cost'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	disp_fee_sub:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			reference: '412-DC'
			note: '412-DC'
			label: 'Dispensing Fee'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	pro_svc_fee_sub:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			required: true
		view:
			columns: 3
			reference: '477-BE'
			note: '477-BE'
			label: 'Professional Service Fee'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	incv_amt_sub:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			reference: '438-E3'
			note: '438-E3'
			label: 'Incentive Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	pt_pd_amt_sub:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			reference: '433-DX'
			note: '433-DX'
			label: 'Pt Paid Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			_meta:
				copy_forward: true

	u_and_c_charge:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 4
			reference: '426-DQ'
			note: '426-DQ'
			label: 'U&C Charge'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	gross_amount_due:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			min: 0.00
		view:
			columns: 3
			reference: '430-DU'
			note: '430-DU'
			label: 'Gross Amt Due'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	#fields copied over from pricing segment
	total_paid:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '509-F9'
			label: 'Total Amount Paid'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	pt_pay_amt:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '505-F5'
			label: 'Patient Pay Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	# field copied over from message segment
	message:
		view:
			columns: 3
			control: 'area'
			note: '504-F4'
			label: 'Message'
			readonly: true

	# field copied over from status segment
	transaction_response_status:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '112-AN'
		view:
			columns: 3
			note: '112-AN'
			label: 'Transaction Status'
			readonly: true

	request_json_data:
		model:
			type: 'json'
		view:
			label: 'Request JSON Data'
			class: 'json-viewer'
			readonly: true

	response_json_data:
		model:
			type: 'json'
		view:
			label: 'Response JSON Data'
			class: 'json-viewer'
			readonly: true

	request_d0_raw:
		view:
			control: 'raw'
			label: 'Raw D0 request'
			readonly: true
			offscreen: true

	response_d0_raw:
		view:
			control: 'raw'
			label: 'Raw D0 response'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
			['claim_no']
		]

	name: ['version_number', 'response_status']
	sections_group: [
		'Header':
			hide_header: true
			indent: false
			tab: 'Header'
			fields: ['claim_no', 'show_segments','response_status', 'version_number', 'transaction_code',
			'service_provider_qualifier', 'service_provider_id']
		'Status':
			hide_header: true
			indent: false
			tab: 'Header'
			fields: ['response_stat']
		'Message':
			hide_header: true
			indent: false
			tab: 'Header'
			fields: ['response_msg']
		'Original Submission':
			hide_header: true
			indent: false
			tab: 'Header'
			fields: ['ing_cst_sub', 'disp_fee_sub', 'pro_svc_fee_sub', 'incv_amt_sub', 'pt_pd_amt_sub', 'u_and_c_charge', 'gross_amount_due']
		'Paid':
			hide_header: true
			indent: false
			tab: 'Header'
			fields: ['total_paid', 'pt_pay_amt']
		'Insurance':
			hide_header: true
			indent: false
			tab: 'Insurance'
			fields: ['response_insur']
		'Insurance Additional Information':
			hide_header: true
			indent: false
			tab: 'Insurance'
			fields: ['response_docs']
		'Patient':
			hide_header: true
			indent: false
			tab: 'Patient'
			fields: ['response_pat']
		'Claim':
			hide_header: true
			indent: false
			tab: 'Claim'
			fields: ['response_claim']
		'Pricing':
			hide_header: true
			indent: false
			tab: 'Pricing'
			fields: ['response_pricing']
		'DUR/PPS':
			hide_header: true
			indent: false
			tab: 'DUR/PPS'
			fields: ['response_dur']
		'COB':
			hide_header: true
			indent: false
			tab: 'COB'
			fields: ['response_cob']
		'Raw Request':
			hide_header: true
			indent: false
			tab: 'Raw'
			fields: ['request_d0_raw', 'request_json_data']
		'Raw Response':
			hide_header: true
			indent: false
			tab: 'Raw'
			fields: ['response_d0_raw', 'response_json_data']
		]

view:
	dimensions:
		width: '90%'
		height: '85%'
	hide_cardmenu: true
	comment: 'NCPDP Response'
	find:
		basic: ['response_status']
		advanced: ['response_status']
	grid:
		fields: ['response_status']
		sort: ['-created_on']
	label: 'NCPDP Response'
	open: 'read'