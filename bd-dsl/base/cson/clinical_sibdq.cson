fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_sibdq.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	fatigue:
		model:
			required: true

			max: 64
			source: ['All the time', 'Most of the time', 'A good bit of the time', 'Some of the time', 'A little of the time', 'Hardly any of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'How often has the feeling of fatigue or being tired and worn out been a problem for the patient during the last 2 weeks?'

	social_engagement:
		model:
			required: true

			max: 64
			source: ['All the time', 'Most of the time', 'A good bit of the time', 'Some of the time', 'A little of the time', 'Hardly any of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'How often has the patient had to delay or cancel a social engagement because of bowel problems during the last 2 weeks?'

	sports:
		model:
			required: true

			max: 64
			source: ['A great deal of difficulty; activities made impossible', 'A lot of difficulty', 'A fair bit of difficulty', 'Some difficulty', 'A little difficulty', 'Hardly any difficulty', 'No difficulty; 
the bowel problem did not limit sports or leisure activities']
		view:
			control: 'radio'
			label: 'How much difficulty has the patient had, as a result of bowel problems, doing leisure or sports activities he/she would have liked to have done over the last 2 weeks?'

	pain:
		model:
			required: true

			max: 64
			source: ['All the time', 'Most of the time', 'A good bit of the time', 'Some of the time', 'A little of the time', 'Hardly any of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'How often has the patient been troubled by pain in the abdomen during the last 2 weeks?'

	depressed:
		model:
			required: true

			max: 64
			source: ['All the time', 'Most of the time', 'A good bit of the time', 'Some of the time', 'A little of the time', 'Hardly any of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'How often has the patient felt depressed or discouraged during the last 2 weeks?'

	passing_gas:
		model:
			required: true

			max: 64
			source: ['A major problem', 'A big problem', 'A significant problem', 'Some problem', 'A little trouble', 'Hardly any trouble', 'No trouble']
		view:
			control: 'radio'
			label: 'Overall, how much of a problem has the patient had with passing large amounts of gas in the last 2 weeks?'

	weight_problems:
		model:
			required: true

			max: 64
			source: ['A major problem', 'A big problem', 'A significant problem', 'Some problem', 'A little trouble', 'Hardly any trouble', 'No trouble']
		view:
			control: 'radio'
			label: 'Overall, how much of a problem has the patient had maintaining or getting to the weight he/she would like to be in the last 2 weeks?'

	felt_relaxed:
		model:
			required: true

			max: 64
			source: ['None of the time', 'A little of the time', 'Some of the time', 'A good bit of the time', 'Most of the time', 'Almost all of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'How often have you felt relaxed and free of tension during the last 2 weeks?'

	been_troubled:
		model:
			required: true

			max: 64
			source: ['All the time', 'Most of the time', 'A good bit of the time', 'Some of the time', 'A little of the time', 'Hardly any of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'How often has the patient been troubled by a feeling of having to go to the bathroom even though bowels were empty during the last 2 weeks?'

	felt_angry:
		model:
			required: true

			max: 64
			source: ['All the time', 'Most of the time', 'A good bit of the time', 'Some of the time', 'A little of the time', 'Hardly any of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'How often has the patient felt angry as a result of bowel problem during the last 2 weeks?'


model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_sibdq:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Short Inflammatory Bowel Disease Questionnaire (SIBDQ) Participation':
			fields: ['last_assessment_date', 'assessment_date']
		'Questionnaire':
			fields: ['fatigue', 'social_engagement', 'sports', 'pain', 'depressed',
			'passing_gas', 'weight_problems', 'felt_relaxed', 'been_troubled', 'felt_angry']
			prefill: 'clinical_sibdq'

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Short Inflammatory Bowel Disease Questionnaire (SIBDQ)'
	find:
		basic: ['assessment_date']
	grid:
		fields: ['created_on', 'assessment_date', 'created_by']
		sort: ['-id']
	label: 'Short Inflammatory Bowel Disease Questionnaire (SIBDQ)'
