#RHICL1_HIC_HICLSEQNO_LINK
fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#HICL_SEQNO
	hicl_seqno:
		model:
			type: 'int'
		view:
			label: 'Ingredient List Identifier (Stable ID)'
			note: '(formerly the Hierarchical Ingredient Code List Sequence Number)'
			columns: 2

	#HIC_SEQN
	hic_seqn:
		model:
			type: 'int'
		view:
			label: 'Hierarchical Ingredient Code Sequence Number (Stable ID)'
			readonly: true
			columns: 2

	#HIC_REL_NO
	hic_rel_no:
		model:
			type: 'int'
		view:
			label: 'Hierarchical Ingredient Code Relative Number'
			readonly: true
			columns: 2

	#HIC
	hic:
		view:
			label: 'Hierarchical Ingredient Code'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['hic']
	indexes:
		many: [
			['hic']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['hic', 'hic_seqn', 'hic_rel_no']

view:
	comment: 'Manage > List FDB HICL_SEQNO/HIC Relation Table'
	find:
		basic: ['hic']
	grid:
		fields: ['hic']
	label: 'List FDB HICL_SEQNO/HIC Relation Table'
