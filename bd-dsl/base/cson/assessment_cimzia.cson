fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Support Questions
	injection_training:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields:['injection_training_type']
		view:
			control: 'radio'
			label: 'Will patient need injection training/nurse support?'

	injection_training_type:
		model:
			max: 3
			source: {physician_train:'Physician office to train patient to use prefilled syringes', nurse_train:'Home Health Nurse to train patient to use prefilled syringes', physician_lyo_powder:'Physician office to administer Lyophilized powder(LYO)', nurse_lyo_powder:'Home Health Nurse to administer Lyophilized powder(LYO)'}
		view:
			control: 'radio'
			label: 'Training/nurse Support Type'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Cimzia Training':
				fields: ['injection_training', 'injection_training_type']
view:
	comment: 'Patient > Careplan > Assessment > Cimzia'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Assessment Questionnaire: Cimzia'
	open: 'read'
