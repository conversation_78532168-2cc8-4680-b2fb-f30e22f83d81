fields:

	code:
		model:
			required: true
		view:
			label: 'Code'
			columns: 3

	name:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 3

	linked_action:
		model:
			source:
				'clarification': 'Request Clarification'
				'generic': 'Request Change (Generic)'
				'therapeutic': 'Request Change (Substitution)'
				'allergy': 'Request Change (Allergy)'
				'oos': 'Request Change (OOS)'
		view:
			label: 'Linked Action'
			readonly: true

	dea_restricted:
		model:
			multi: false
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'DEA Schedule Restricted?'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name', 'code', 'dea_restricted', 'linked_action']

view:
	comment: 'Manage > Surescripts Change Code'
	find:
		basic: ['name', 'dea_restricted']
	grid:
		fields: ['name', 'dea_restricted']
		sort: ['name']
	label: 'Surescripts Change Code'
	open: 'read'
