fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_no:
		model:
			prefill: ['parent.order_no']
		view:
			label: 'Order #'
			readonly: true
			offscreen: true

	time_given:
		model:
			required: true
			type: 'time'
		view:
			label: 'Time Given'
			template: '{{now}}'
			columns: 3

	flush_id:
		model:
			required: true
			multi: false
			source: 'careplan_order_rx'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				order_no:
					'dynamic': '{order_no}'
				discontinued:
					'static': '!Yes'
				type_id:
					'static': 'Flush'
		view:
			label: 'Select Flush Used'
			columns: 3

	step:
		model:
			required: true
			source: ['Before', 'Between', 'After', 'PRN']
		view:
			label: 'Step'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	name: ['time_given', 'flush_id', 'step']
	sections:
		'Details':
			fields: ['order_no', 'time_given', 'flush_id', 'step']

view:
	comment: 'Patient > Careplan > Encounter > Admin Flush'
	grid:
		fields: ['time_given', 'flush_id', 'step']
	label: 'Admin Flush'
