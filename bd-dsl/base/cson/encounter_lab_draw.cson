fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	order_id:
		model:
			required: false
			source: 'careplan_order'
			prefill: ['parent.order_id']
		view:
			control: 'select'
			label: 'Referral'
			readonly: true
			columns: 3

	draw_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			label: 'Draw Date/Time'
			template: '{{now}}'
			columns: 3

	lab_orders:
		model:
			multi: true
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				parent_id:
					'dynamic': '{order_id}'
				parent_form:
					'static': 'careplan_order'
				active:
					'static': 'Yes'
		view:
			embed:
				form: 'careplan_order_lab'
				selectable: true
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['lab_id', 'draw_frequency', 'draw_frequency_other', 'next_draw_date']
				label: ['Lab', 'Draw Freq', 'Draw Freq Other', 'Next Draw Date']
				width: [30, 20, 25, 20]
			label: 'Active Lab Orders'
			note: 'Select all that apply'

	loid:
		model:
			multi: true
			required: false
			source: 'careplan_order_lab'
		view:
			control: 'select'
			label: 'Lab Order'
			columns: 3
			offscreen: true
			readonly: true

	lab_final:
		view:
			label: 'Lab'
			readonly: true
			offscreen: true

	lab:
		model:
			prefill: ['encounter_lab_draw']
			required: true
			source: ['Quest', 'LabCorp', 'Other']
			if:
				'Other':
					fields: ['lab_other']
		view:
			control: 'radio'
			label: 'Laboratory'
			columns: 3
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'lab_final'
					overwrite: true
					condition:
						cond: 'neq'
						value: 'Other'
				}
			]

	lab_other:
		model:
			prefill: ['encounter_lab_draw']
			required: true
		view:
			label: 'Laboratory Other'
			columns: 3
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'lab_final'
					overwrite: true
					condition:
						cond: 'neq'
						value: 'Other'
				}
			]

	labs_source:
		model:
			prefill: ['encounter_lab_draw']
			required: true
			source: ['Peripheral draw', 'Drawn from catheter', 'Discarded blood']
		view:
			control: 'radio'
			label: 'Labs Source'
			columns: 3

	amount_drawn:
		model:
			required: true
			type: 'decimal'
			rounding: 0.01
		view:
			label: 'Amount Drawn (ml)'
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 3

	comment:
		view:
			control: 'area'
			label: 'Comments'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	bundle: ['patient']
	name: ['order_id']
	prefill:
		encounter_lab_draw:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
	indexes:
		unique: [
			['patient_id']
			['loid']
			['order_id']
		]
	sections_group: [
		'Lab Draw':
			hide_header: true
			indent: false
			fields: ['order_id', 'draw_datetime']
		'Lab Order(s)':
			fields: ['lab_orders']
		'Draw Details':
			fields: ['lab', 'lab_other', 'lab_final', 'labs_source', 'amount_drawn', 'comment']
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Lab Drawn'
	grid:
		fields: ['draw_datetime', 'lab', 'lab_other', 'labs_source', 'amount_drawn', 'comment'] # dynamic status is displayed in prefilled, rtag is hidden
		sort: ['draw_datetime']
	label: 'Lab Drawn'
