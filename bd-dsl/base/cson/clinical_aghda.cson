fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_aghda.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	# Survey Participation

	fatigue:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have to struggle to finish jobs'

	sleepy:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Feel a strong need to sleep during the day'

	lonely:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Often feel lonely even when I am with other people'

	rereading:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have to read things several times before they sink in'

	friends:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have difficulty making friends'

	effort:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'It takes a lot of effort for me to do simple tasks'

	emotions:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have difficulty controlling my emotions'

	lose_track:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Often lose track of what I have to say'

	confidence:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Lacking in confidence'

	motivation:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have to push myself to do things'

	tense:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Often feel very tense'

	let_down:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Feel as if I let people down'

	fitin:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Find it hard to mix with people'

	worn_out:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Feel worn out even when I've not done anything"

	feel_low:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "There are times when I feel very low"

	responsibility:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Avoid responsibility if possible"

	avoid_people:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Avoid mixing with people I don't know"

	burden:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Feel as if I am a burden to people"

	forgetful:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Often forget what people have said to me"

	plan_ahead:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Find it difficult to plan ahead"

	irritated:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Easily irritated by other people"

	too_tired:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Often feel too tired to do the things that I ought to do"

	force_myself:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Have to force myself to do all the things that need doing"

	force_awake:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Often have to force myself to stay awake"

	memory:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Memory lets me down"

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_aghda:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Adult Growth Hormone Deficiency Assessment (AGHDA)':
			fields: ['last_assessment_date', 'assessment_date']
		'Questionnaire':
			note: 'Intended to be used for patients 21 years or older. Please have the patient respond yes or no to the following statements'
			fields: ['fatigue', 'sleepy', 'lonely', 'rereading', 'friends', 'effort',
			'emotions', 'lose_track', 'confidence', 'motivation', 'tense', 'let_down',
			'fitin', 'worn_out', 'feel_low', 'responsibility', 'avoid_people', 'burden',
			'forgetful', 'plan_ahead', 'irritated', 'too_tired', 'force_myself', 'force_awake',
			'memory']
			prefill: 'clinical_aghda'

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Adult Growth Hormone Deficiency Assessment (AGHDA)'
	find:
		basic: ['assessment_date']
	grid:
		fields: ['created_on', 'assessment_date', 'created_by']
		sort: ['-id']
	label: 'Adult Growth Hormone Deficiency Assessment (AGHDA)'
