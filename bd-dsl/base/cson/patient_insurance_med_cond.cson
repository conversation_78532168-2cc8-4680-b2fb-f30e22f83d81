fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			validate: [
					name: 'LoadActivePayers'
			]

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	active_payer_id:
		model:
			source: 'payer'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
			required: false
		view:
			columns: 2
			label: 'Active Payers'
			offscreen: true
			readonly: true
			validate: [
					name: 'LoadActivePayers'
			]

	previous_payer_id:
		model:
			source: 'payer'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
			required: true
			sourcefilter:
				id:
					'dynamic': '{active_payer_id}'
		view:
			columns: 2
			label: 'Previous Payer'

	sec_payer_id:
		view:
			columns: 2
			label: 'Payer ID'
			class: 'claim-field'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{3,10}$'
				error: 'Invalid Payer ID, must be 1-60 alpha numeric characters'
			}]

	insurance_type_code:
		model:
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR05'
		view:
			columns: 2
			label: 'Insurance Type Code'
			note: 'Used in Electronic Medical Claim'
			class: 'claim-field'

	payment_responsibility_level_code:
		model:
			required: false
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR01'
		view:
			columns: 2
			label: 'Responsibility Level Code'
			class: 'claim-field'

	claim_filing_code:
		model:
			required: false
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR09'
		view:
			columns: 2
			label: 'Insurance Type'
			reference: 'SBR09'
			class: 'claim-field'

	accident_date:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Injury Date'
			reference: 'DTP03'
			class: 'claim-field'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	symptom_date:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Symptom Onset Date'
			reference: 'DTP03'
			class: 'claim-field'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	bill_notes:
		view:
			control: 'area'
			label: 'Bill Notes'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['previous_payer_id']
		]
	name: ['patient_id', 'previous_payer_id', 'insurance_type_code', 'claim_filing_code']
	sections:
		'Previous Payer Conditional':
			fields: ['patient_id', 'active_payer_id', 'previous_payer_id', 'sec_payer_id', 'insurance_type_code',
			'payment_responsibility_level_code', 'claim_filing_code', 'accident_date', 'symptom_date', 'bill_notes']

view:
	hide_cardmenu: true
	comment: 'Patient > Insurance Med Conditional'
	find:
		basic: ['previous_payer_id']
	grid:
		fields: ['previous_payer_id', 'accident_date', 'symptom_date', 'insurance_type_code', 'bill_notes']
		sort: ['-created_on']
	label: 'Insurance Med Conditional'
