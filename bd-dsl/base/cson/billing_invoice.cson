fields:

	applied_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Applied Date/Time'
			readonly: true
			offscreen: true

	series_uuid:
		view:
			label: 'Series UUID'

	followup_date:
		model:
			type: 'date'
		view:
			label: 'Followup Date'
			readonly: true
			offscreen: true

	account_id:
		model:
			required: true
			source: 'billing_account'
		view:
			label: 'Account'
			readonly: true
			offscreen: true

	close_no:
		model:
			type: 'text'
		view:
			label: 'Closing #'
			readonly: true
			offscreen: true

	rx_id:
		model:
			multi: true
			source: 'careplan_order_rx'
		view:
			label: 'Prescriptions'
			readonly: true
			offscreen: true

	is_dirty:
		model:
			required: false
			source: ['Yes']
		view:
			label: 'Is Dirty?'
			control: 'checkbox'
			class: 'checkbox-only'
			note: 'Indicates the charges have been modified and claim needs to be re-submitted'
			readonly: true
			offscreen: true

	locked:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['locked_datetime', 'locked_by']
		view:
			columns: 3
			control: 'checkbox'
			label: 'Locked'
			readonly: true
			offscreen: true

	locked_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Locked Date/Time'
			readonly: true
			offscreen: true

	locked_by:
		model:
			source: 'user'
		view:
			label: 'Locked By'
			readonly: true
			offscreen: true

	delivery_ticket_id:
		model:
			source: 'careplan_delivery_tick'
		view:
			label: 'Delivery Ticket'
			readonly: true
			offscreen: true

	encounter_id:
		model:
			multi: true
			source: 'encounter'
		view:
			label: 'Encounter'
			readonly: true
			offscreen: true

	site_id:
		model:
			required: true
			source: 'site'
		view:
			columns: 2
			label: 'Site'
			readonly: true

	master_invoice_no:
		model:
			type: 'text'
		view:
			label: 'Master Invoice No'
			readonly: true
			offscreen: true

	invoice_no:
		model:
			type: 'text'
		view:
			columns: 4
			label: 'Invoice #'
			note: 'Generated at save'
			readonly: true

	claim_no:
		model:
			type: 'text'
		view:
			label: 'Claim No'
			readonly: true
			offscreen: true

	is_master_invoice:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['show_cost']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Is Master Invoice?"
			readonly: true
			offscreen: true

	parent_invoice_no:
		model:
			type: 'text'
		view:
			label: 'Parent Invoice #'
			note: 'For COB Claims'
			readonly: true
			offscreen: true

	invoice_type:
		model:
			source: ['Payer', 'Patient']
		view:
			columns: 4
			control: 'radio'
			label: 'Invoice Type'
			readonly: true
			offscreen: true

	status:
		model:
			required: true
			default: 'Open'
			source: ['Accepted', 'Open', 'Confirmed', 'Voided', '$0']
			if:
				'Confirmed':
					fields: ['revenue_accepted_posted', 'confirmed_by', 'confirmed_datetime']
				'Open':
					fields: ['on_hold']
		view:
			columns: -2
			control: 'radio'
			label: 'Invoice Status'
			findfilter: ['Accepted', 'Open', 'Confirmed']
			readonly: true
			offscreen: true

	revenue_accepted_posted:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['is_master_invoice', 'view_total_expected', 'view_total_paid',
					'view_total_cost', 'view_total_adjusted', 'view_total_balance_due']
					prefill:
						show_cost: 'ledger_view'
				'!':
					fields: ['total_expected', 'total_paid', 'total_cost', 'total_balance_due']
					prefill:
						show_cost: 'unposted_view'
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Revenue Accepted and Posted?'
			readonly: true
			offscreen: true

	on_hold:
		model:
			source: ['Yes']
		view:
			columns: 4
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'On Hold?'

	auth_flag:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Prior Authorization Required?'

	post_datetime:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Post Date/Time'	
			validate: [
				{
					name: 'ValidatePostDate'
				}
			]

	confirmed_by:
		model:
			source: 'user'
		view:
			columns: 4
			label: 'Confirmed By'
			readonly: true

	confirmed_datetime:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Confirmed Date/Time'
			readonly: true

	revenue_accepted:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['accepted_by', 'accepted_datetime']
		view:
			control: 'checkbox'
			label: 'Revenue Accepted?'
			readonly: true
			offscreen: true

	accepted_by:
		model:
			source: 'user'
		view:
			columns: 2
			label: 'Accepted By'
			readonly: true

	accepted_datetime:
		model:
			type: 'datetime'
		view:
			columns: 2
			label: 'Accepted Date/Time'
			readonly: true

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			columns: -2
			label: 'Patient'
			readonly: true

	date_of_service:
		model:
			required: true
			type: 'date'
		view:
			columns: -2
			label: 'DOS Start'
			readonly: true

	date_of_service_end:
		model:
			required: false
			type: 'date'
		view:
			columns: 2
			label: 'DOS End'
			readonly: true

	insurance_id:
		model:
			source: 'patient_insurance'
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			form_link_enabled: true
			columns: -2
			control: 'select'
			class: 'select_prefill'
			label: 'Insurance'
			readonly: true
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_insurance/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'payer_id': ['payer_id']
			]

	payer_id:
		model:
			source: 'payer'
			required: true
		view:
			columns: 2
			label: 'Payer'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/payer/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'type_id': ['type_id'],
					'collector_id': ['collector_id']
			]

	collector_id:
		model:
			source: 'wf_queue_team'
			sourceid: 'code'
			sourcefilter:
				code:
					static: 'Collector'
			default: 'Collector'
		view:
			columns: 2
			label: 'Collector Team'

	bill_for_denial:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['next_insurance_id']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Bill For Denial?'
			readonly: true

	next_insurance_id:
		model:
			required: true
			source: 'patient_insurance'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
				id:
					'dynamic': '!{insurance_id}'
				billing_method_id:
					'static': 'mm'
		view:
			columns: 2
			label: 'Next Insurance'
			note: 'The next insurance used for pricing in COB scenarios'

	# Pulls in from insurance ID above
	type_id:
		model:
			required: false
			source: 'list_payer_type'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Type'
			readonly: true

	billing_method_id:
		model:
			default: 'generic'
			source: 'list_billing_method'
			sourceid: 'code'
			required: true
			if:
				'ncpdp':
					fields: ['subform_pharmacy', 'total_insurance_paid', 'total_pt_pay', 'revenue_accepted', 'ncpdp_open_paid', 'auth_flag']
					sections: ['Pharmacy Claim']
					readonly:
						fields: ['status']
				'generic':
					sections: ['Payment Details', 'Service Details', 'Patient Info', 'Billing Information', 'Insured']
					readonly:
						fields: ['status', 'total_paid']
				'mm':
					fields: ['subform_medical',  'total_insurance_paid', 'total_pt_pay', 'bill_for_denial', 'auth_flag']
					sections: ['Medical Claim']
					readonly:
						fields: ['status', 'total_paid']
				'cms1500':
					fields: ['subform_1500', 'total_insurance_paid', 'total_pt_pay', 'auth_flag']
					sections: ['Medical Paper Claim']
		view:
			columns: 2
			control: 'select'
			label: 'Billing Method'
			readonly: true
			offscreen: true

	ncpdp_open_paid:
		model:
			required: false
			source: ['Yes']
			if:
				'!Yes':
					readonly: 
						fields: ['total_paid']
		view:
			columns: 2
			label: 'NCPDP Open Paid?'
			readonly: true
			offscreen: true

	billed_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Date/Time Billed'
			template: '{{now}}'

	subform_pharmacy:
		model:
			multi: false
			source: 'ncpdp'
			type: 'subform'
		view:
			label: 'NCPDP Claim'

	subform_medical:
		model:
			multi: false
			source: 'med_claim'
			type: 'subform'
		view:
			label: 'Electronic Medical Claim'

	subform_1500:
		model:
			multi: false
			source: 'med_claim_1500'
			type: 'subform'
		view:
			label: 'Paper Medical Claim'

	total_billed:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 3
			label: 'Total Billed' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	view_total_expected:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			save: false
		view:
			columns: 3
			label: 'Expected Amount' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_expected:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 3
			label: 'Expected Amount' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	show_cost:
		model:
			source: ['ledger_view', 'unposted_view']
			if:
				'ledger_view':
					fields: ['view_total_cost']
				'unposted_view':
					fields: ['total_cost']
		view:
			label: 'Show Cost'
			readonly: true
			offscreen: true 

	total_cost:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			label: 'Estimated Total Cost'
			note: 'Final COGS are calculated when revenue is booked'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	view_total_cost:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
			save: false
		view:
			columns: 3
			label: 'Total Cost' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_tax:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			label: 'Total Tax' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			offscreen: true

	total_pt_pay:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
			min: 0.00
		view:
			columns: 3
			label: 'Total Patient Pay' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_insurance_paid:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			label: 'Total Claim Paid' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_paid:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
			min: 0.00
		view:
			columns: 3
			label: 'Total Invoice Paid' 
			class: 'numeral money'
			format: '$0,0.00'

	view_total_paid:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
			save: false
		view:
			columns: 3
			label: 'Total Paid'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	view_total_adjusted:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
			save: false
		view:
			columns: 3
			label: 'Adjusted Amount' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	view_total_balance_due:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
			save: false
		view:
			columns: 3
			label: 'Balance Due' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			validate: [
				{
					name: 'FetchInvoiceBalanceDue'
				}
			]

	total_balance_due:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
			save: false
		view:
			columns: 3
			label: 'Balance Due' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			validate: [
				{
					name: 'InvoiceCalcTotalBalanceDue'
				}
			]

	unapplied_cash_available:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['available_unapplied_cash', 'allocated_unapplied_cash']
			save: false
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Unapplied Cash Available?'
			readonly: true
			offscreen: true

	available_unapplied_cash:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			label: 'Available Unapplied Cash' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			validate: [
				{
					name: 'FetchUnappliedCashBalance'
				}
			]

	allocated_unapplied_cash:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			min: 0.01
		view:
			columns: 3
			label: 'Allocated Cash'
			class: 'numeral money'
			format: '$0,0.00'
			validate: [
				{
					name: 'ValidateAllocatedCash'
				}
			]

	show_void_warnings:
		model:
			source: ['No','Yes']
			if:
				'Yes':
					fields: ['void_warnings']
		view:
			control: 'radio'
			label: 'Show Void Warnings?'
			offscreen: true
			readonly: true

	void_warnings:
		model:
			source: ['<span style="color:red;">This invoice requires approval to void.</span>', '<span style="color:red;">This claim has child COB claims that will be reversed when this claim is voided.</span>']
		view:
			label: 'Warning'
			class: 'list'
			readonly: true

	zeroed:
		model:
			source: ['Yes']
		view:
			columns: -4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Zero Invoice?'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						zeroed: 'Yes'
					dest: 'zeroed_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						zeroed: 'Yes'
					dest: 'zeroed_by'
				}
			]

	zeroed_reason_id:
		model:
			required: true
			source: 'list_void_reason_billing'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			label: 'Zeroed Reason'
			columns: 2

	zeroed_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 4
			label: 'Zeroed Date/Time'
			readonly: true
			template: '{{now}}'

	zeroed_by:
		model:
			source: 'user'
		view:
			label: 'Zeroed By'
			readonly: true
			columns: 4

	flt_insur:
		model:
			multi: true
			source: 'patient_insurance'
		view:
			label: 'Available Next Insurances'
			readonly: true
			offscreen: true

	zeroed_next_insurance:
		model:
			source: 'patient_insurance'
			sourcefilter:
				id:
					'dynamic': '{flt_insur}'
				patient_id:
					'dynamic': '{patient_id}'
		view:
			label: 'Move charges to other payer?'
			columns: 2

	void:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['show_void_warnings', 'voided_datetime', 'void_reason_id']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Void Claim?'
			findfilter: '!Yes'
			class: 'checkbox-only'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						void: 'Yes'
					dest: 'voided_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						void: 'Yes'
					dest: 'voided_by'
				}
			]

	void_reason_id:
		model:
			required: true
			source: 'list_void_reason_billing'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			label: 'Void Reason'

	voided_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Voided Date'
			readonly: true

	voided_by:
		model:
			source: 'user'
		view:
			label: 'Voided By'
			readonly: true

	embed_approvals:
		model:
			required: true
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				source_form:
					'static': 'billing_invoice'
				source_id:
					'dynamic': '{id}'
		view:
			embed:
				form: 'security_approval'
				selectable: false
			grid:
				edit: false
				rank: 'none'
				add: 'none'
				fields: ['request_from', 'action_id', 'description', 'status']
				label: ['From', 'Action', 'Desc', 'Status']
				width: [20, 20, 40, 20]
			label: 'Security Approvals'
			readonly: true
			offscreen: true

	patient_name:
		model:
			search: 'A'
			max: 35
			min: 1
			required: true
		view:
			label: 'Name'
			note: 'Letters, numbers, comma, \', -, . only'
			validate: [
				{
					name: 'NameValidator'
				}
			]
			columns: 3

	patient_address:
		model:
			max: 128
			required: true
		view:
			columns: 'addr_1'
			label: 'Address'
			class: "api_prefill"
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'address': ['addressLabel']
					'city': ['city']
					'state_id': ['stateCode']
					'zip': ['postalCode']
			]

	patient_city:
		model:
			max: 64
			required: true
		view:
			label: 'City'
			columns: 'addr_city'

	patient_state:
		model:
			max: 2
			min: 2
			required: true
			source: 'list_us_state'
			sourceid: 'code'
		view:
			label: 'State'
			columns: 'addr_state'

	patient_zip:
		model:
			max: 10
			min: 5
			required: true
		view:
			format: 'us_zip'
			label: 'Zip'
			columns: 'addr_zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'zip'
						city:'city'
						state_id:'state_id'
			]

	responsible_party:
		model:
			type: 'text'
		view:
			label: 'Responsible Party'

	send_bill_to:
		model:
			type: 'text'
		view:
			label: 'Send Bill To'

	billing_city:
		model:
			max: 64
			required: true
		view:
			label: 'City'
			columns: 'addr_city'

	billing_state:
		model:
			max: 2
			min: 2
			required: true
			source: 'list_us_state'
			sourceid: 'code'
		view:
			label: 'State'
			columns: 'addr_state'

	billing_zip:
		model:
			max: 10
			min: 5
			required: true
		view:
			format: 'us_zip'
			label: 'Zip'
			columns: 'addr_zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'zip'
						city:'city'
						state_id:'state_id'
			]

	policy_no:
		model:
			type: 'text'
		view:
			label: 'Policy #'

	billing_group_no:
		model:
			type: 'text'
		view:
			label: 'Billing Group #'

	prior_auth_no:
		model:
			type: 'text'
		view:
			label: 'Prior Auth #'

	insured_name:
		model:
			search: 'A'
			max: 35
			min: 1
			required: true
		view:
			label: 'Name'
			note: 'Letters, numbers, comma, \', -, . only'
			validate: [
				{
					name: 'NameValidator'
				}
			]
			columns: 3

	insured_address:
		model:
			max: 128
			required: true
		view:
			columns: 'addr_1'
			label: 'Address'
			class: "api_prefill"
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'address': ['addressLabel']
					'city': ['city']
					'state_id': ['stateCode']
					'zip': ['postalCode']
			]

	insured_city:
		model:
			max: 64
			required: true
		view:
			label: 'City'
			columns: 'addr_city'

	insured_state:
		model:
			max: 2
			min: 2
			required: true
			source: 'list_us_state'
			sourceid: 'code'
		view:
			label: 'State'
			columns: 'addr_state'

	insured_zip:
		model:
			max: 10
			min: 5
			required: true
		view:
			format: 'us_zip'
			label: 'Zip'
			columns: 'addr_zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'zip'
						city:'city'
						state_id:'state_id'
			]

	services_provided:
		model:
			dynamic:
				source: 'list_invoice_services_provided'
		view:
			label: 'Services Provided'
			columns: 1

	free_text:
		model:
			dynamic:
				source: 'list_invoice_free_text'
		view:
			label: 'Free Form Message'
			columns: 1

	terms:
		model:
			dynamic:
				source: 'list_invoice_terms'
		view:
			label: 'Additional Terms'
			columns: 1

	due_days:
		model:
			type: 'int'
			required: true
		view:
			columns: 2
			label: 'Net Due Days'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
	reportable: true
	indexes:
		unique: [
			['invoice_no']
		]
		many: [
			['auth_flag']
			['patient_id']
			['invoice_no']
			['encounter_id']
			['delivery_ticket_id']
			['site_id']
			['master_invoice_no']
			['parent_invoice_no']
			['status']
			['post_datetime']
			['revenue_accepted_posted']
			['revenue_accepted']
			['accepted_by']
			['accepted_datetime']
			['invoice_type']
			['insurance_id']
			['payer_id']
			['void_reason_id']
		]
	name: '{invoice_no} DT:{date_of_service}'

	sections_group: [
		'Pharmacy Claim':
			tab: 'Pharmacy Claim'
			hide_header: true
			indent: false
			fields: ['subform_pharmacy']
		'Medical Claim':
			tab: 'Major Medical Claim'
			hide_header: true
			indent: false
			fields: ['subform_medical']
		'Medical Paper Claim':
			tab: 'CMS 1500 Claim'
			hide_header: true
			indent: false
			fields: ['subform_1500']
		'Invoice Details':
			hide_header: true
			indent: false
			tab: 'Invoice'
			fields: ['account_id', 'rx_id', 'close_no', 'is_dirty', 'followup_date', 'site_id', 'locked', 'locked_datetime', 'locked_by',
			'delivery_ticket_id', 'encounter_id', 'claim_no', 'master_invoice_no', 'invoice_no', 'parent_invoice_no', 'invoice_type', 'status',
			'patient_id', 'date_of_service', 'date_of_service_end', 'billed_datetime',
			'revenue_accepted_posted', 'show_cost', 'is_master_invoice', 'post_datetime', 'confirmed_by', 'confirmed_datetime', 'revenue_accepted', 'accepted_by','accepted_datetime','bill_for_denial',
			'insurance_id', 'payer_id', 'collector_id', 'billing_method_id', 'type_id', 'next_insurance_id', 'ncpdp_open_paid', 'auth_flag']
		'Total':
			hide_header: true
			indent: false
			tab: 'Invoice'
			fields: ['total_billed', 'total_expected', 'view_total_expected', 'total_cost',  'view_total_cost', 'total_tax', 'total_pt_pay', 'total_paid', 'view_total_paid', 'view_total_adjusted', 'view_total_balance_due', 'total_balance_due', 'unapplied_cash_available', 'available_unapplied_cash', 'allocated_unapplied_cash']
		'Service Details':
			hide_header: true
			indent: false
			tab: 'Invoice'
			fields: ['services_provided', 'free_text', 'terms']
		'Payment Details':
			hide_header: true
			indent: false
			tab: 'Invoice'
			fields: ['due_days']
		'Billing Information':
			hide_header: true
			indent: false
			tab: 'Billing Information'
			fields: ['responsible_party', 'send_bill_to', 'billing_city', 'billing_state', 'billing_zip', 'policy_no', 'billing_group_no','prior_auth_no']
		'Patient Info':
			indent: false
			tab: 'Billing Information'
			fields: ['patient_name', 'patient_address', 'patient_city', 'patient_state', 'patient_zip']
		'Insured':
			indent: false
			tab: 'Billing Information'
			fields: ['insured_name', 'insured_address', 'insured_city', 'insured_state', 'insured_zip']
		'On-Hold':
			hide_header: true
			indent: false
			compact: true
			fields: ['on_hold']
			tab: 'Invoice'
		'Void':
			modal: true
			fields: ['show_void_warnings', 'void_warnings', 'void', 'voided_datetime', 'voided_by', 'void_reason_id']
		'Zero':
			modal: true
			note: 'Zeroing an invoice will add a credit adjustment against the invoice to reverse the invoice in the finance ledger.'
			fields: ['patient_id', 'zeroed', 'zeroed_datetime', 'zeroed_by', 'zeroed_reason_id', 'flt_insur', 'zeroed_next_insurance']
	]
view:
	hide_cardmenu: true
	block:
		validate: [
			name: 'InvoiceBlock'
		]
	comment: 'Invoice'
	find:
		basic: ['invoice_no', 'status', 'void']
	grid:
		fields: ['invoice_no', 'status', 'patient_id', 'insurance_id', 'billed_datetime', 'total_paid', 'total_expected']
		sort: ['-invoice_no']
	label: 'Invoice'
