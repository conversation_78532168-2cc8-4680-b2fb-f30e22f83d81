fields:
    document_id:
        model:
            type: 'int'
            source: 'document'
        view:
            columns: 3
            label: 'Attachment ID'
            readonly: true
            offscreen: true

    attachment_report_type_code:
        model:
            required: true
            min: 2
            max: 2
            source: 'list_med_claim_ecl'
            sourceid: 'code'
            sourcefilter:
                field:
                    'static': 'PWK01'
        view:
            columns: 3
            label: 'Report Type'
            reference: 'PWK01'
            _meta:
                location: '2400 PWK'
                field: '01'
                path: 'claimInformation.serviceLines[{idx1-50}].claimSupplementalInformation[{idx1-10}].attachmentReportTypeCode'

    attachment_transmission_code:
        model:
            required: true
            min: 2
            max: 2
            source: 'list_med_claim_ecl'
            sourceid: 'code'
            sourcefilter:
                field:
                    'static': 'PWK02'
        view:
            columns: 3
            label: 'Trans Code'
            reference: 'PWK02'
            _meta:
                location: '2400 PWK'
                field: '02'
                path: 'claimInformation.serviceLines[{idx1-50}].claimSupplementalInformation[{idx1-10}].attachmentTransmissionCode'

    attachment_control_number:
        model:
            min: 2
            max: 80
        view:
            columns: 3
            label: 'Ctl #'
            reference: 'PWK05'
            _meta:
                location: '2400 PWK'
                field: '05'
                path: 'claimInformation.serviceLines[{idx1-50}].claimSupplementalInformation[{idx1-10}].attachmentControlNumber'

model:
    access:
        create:     []
        create_all: ['admin', 'csr', 'pharm']
        delete:     ['admin', 'pharm']
        read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
        read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
        request:    []
        update:     []
        update_all: ['admin', 'csr', 'pharm']
        write:      ['admin', 'csr', 'pharm']
    name:['attachment_report_type_code', 'attachment_transmission_code']
    sections:
        'Service Line Supplemental Information':
            hide_header: true
            fields: ['attachment_report_type_code', 'attachment_transmission_code', 'attachment_control_number']

view:
    dimensions:
        width: '55%'
        height: '55%'
    hide_cardmenu: true
    reference: '2400'
    comment: 'Service Line Supplemental Information'
    grid:
        fields: ['attachment_report_type_code', 'attachment_transmission_code', 'attachment_control_number']
        width: [50, 20, 30]
        sort: ['-created_on']
    label: 'Service Line Supplemental Information'
    open: 'read'