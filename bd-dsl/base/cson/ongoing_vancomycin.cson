fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	vancomycin_trough_value:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'If available'
			label: 'Vanco trough (mcg/ml)'

	vancomycin_trough_date:
		model:
			type: 'datetime'
		view:
			note: 'If available'
			label: 'Vanco trough date/time'

	vancomycin_trough_true:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['vancomycin_trough_true_details']
		view:
			control: 'radio'
			label: 'True trough?'

	vancomycin_trough_true_details:
		view:
			label: 'True trough details'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	sections:
		'Vancomycin Followup':
			fields: ['vancomycin_trough_value', 'vancomycin_trough_date', 'vancomycin_trough_true','vancomycin_trough_true_details']

view:
	comment: 'Patient > Careplan > Ongoing > Vancomycin'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Ongoing Assessment: Vancomycin'
	open: 'read'
