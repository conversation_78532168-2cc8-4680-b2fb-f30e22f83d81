fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			offscreen: true
			readonly: true

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan'
			offscreen: true
			readonly: true

	last_dispense_delivery_tick_id:
		model:
			type: 'int'
		view:
			label: 'Last Delivery Ticket ID'
			note: 'Used when payer bills rentals in arrears'
			readonly: true
			offscreen: true

	checked_in:
		model:
			source: ['Yes']
		view:
			label: 'Checked In'
			readonly: true
			offscreen: true

	order_format:
		model:
			required: true
			source: ['Single Prescription', 'Therapy Set', 'Supply Order']
			if:
				'Therapy Set':
					prefill:
						show_therapy_set_bill_as_kit: 'Yes'
		view:
			label: 'Order Format'
			offscreen: true
			readonly: true

	show_therapy_set_bill_as_kit:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['part_of_kit']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			readonly: true
			offscreen: true

	associated_rx_id:
		model:
			required: false
			type: 'int'
			source: 'careplan_order_rx'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status_id:
					'static': ['1', '5']
		view:
			label: 'Associated Rx'
			columns: 2
			class: 'select_prefill'
			transform: [
				suppress_refetch: true 
				name: 'SelectPrefill'
				url: '/form/careplan_order_rx/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'site_id': ['site_id']
					'rx_no': ['rx_no']
			]

	rx_no:
		view:
			label: 'Associated Rx No'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateFieldNote'
					target: 'associated_rx_id'
				}
			]

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			readonly: true
			offscreen: true
			validate: [
				{
					"name": "CopyFromParent",
					"copy": {
						"site_id": "site_id"
					}
				}
			]

	status_id:
		model:
			required: true
			default: '1'
			source: 'list_order_status'
			sourceid: 'code'
			track: true
			if:
				'5':
					require_fields: ['associated_rx_id']
		view:
			columns: 2
			label: 'Status'
			validate: [
				{
					name: 'CheckPayerSupplyPARequirements'
				}
			]

	inventory_id:
		model:
			required: true
			source: 'inventory'
			query: 'select_inv_in_stock'
			querytemplate: 'inventoryTemplate'
			sourcefilter:
				type:
					'static': ['Supply', 'Billable', 'Equipment Rental']
			if:
				'*':
					fields: ['type']
		view:
			form_link_enabled: true
			columns: 2
			label: 'Item'
			class: 'select_prefill'
			transform: [{
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'supply_billable': ['supply_billable']
					'type': ['type'],
					'quantity_per_container': ['quantity_per_container']
					'container_unit': ['container_unit']
					'hcpc_code': ['hcpc_code']
					'ndc': ['ndc']
					'formatted_ndc': ['formatted_ndc']
					'upc': ['upc']
					'upin': ['upin']
			}
			]

	hcpc_code:
		view:
			columns: 4
			class: 'claim-field'
			label: 'HCPC Code'
			readonly: true

	ndc:
		view:
			label: 'NDC'
			offscreen: true
			readonly: true

	formatted_ndc:
		view:
			columns: 4
			label: 'NDC'
			readonly: true

	upc:
		view:
			columns: 4
			label: 'UPC'
			readonly: true

	upin:
		view:
			columns: 4
			label: 'UPIN'
			readonly: true

	quantity_per_container:
		model:
			default: 1
			required: false
			type: 'decimal'
			max: 9999999.999
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Container Units'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'inventory_id'
					source_fields: ['quantity_per_container', 'container_unit']
					separator: ' per '
				}
			]

	container_unit:
		model:
			default: 'container'
		view:
			label: 'Container Unit'
			readonly: true
			offscreen: true

	dispense_unit:
		model:
			default: 'each'
			required: true
			source: ['each', 'container']
			if:
				'container':
					fields: ['dispense_containers']
					readonly:
						fields: ['dispense_quantity']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Dispense Unit'

	dispense_containers:
		model:
			min: 1
			default: 1
			type: 'int'
			required: true
		view:
			columns: 4
			label: 'Dispense Quantity'
			note: 'containers'
			transform: [
				{
					name: 'MultiplyFields'
					fields: ['quantity_per_container', 'dispense_containers']
					destination: 'dispense_quantity'
				}
			]

	rental_type:
		model:
			required: true
			source: ['Purchase', 'Rental']
			if:
				'Rental':
					fields: ['frequency_code', 'day_supply', 'last_billed']
					prefill:
						day_supply: '28'
		view:
			label: 'Rental Type'
			columns: -4

	frequency_code:
		model:
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SV506'
		view:
			columns: 4
			class: 'claim-field'
			label: 'Rental Frequency'
			reference: 'SV506'

	last_billed:
		model:
			type: 'date'
			required: false
		view:
			columns: 4
			label: 'Last Billed'
			readonly: true

	dispense_quantity:
		model:
			default: 1
			min: 1
			type: 'decimal'
			max: 9999999.999
			rounding: 1
			required: true
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Dispense Quantity'
			note: 'each'

	day_supply:
		model:
			default: 30
			required: true
			max: 365
			min: 1
			type: 'int'
		view:
			columns: 4
			class: 'claim-field'
			label: 'Rental Days'

	one_time_only:
		model:
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: '1x Only?'
			columns: 4

	type:
		model:
			required: true
			source: ['Supply', 'Billable', 'Equipment Rental']
			if:
				'Equipment Rental':
					fields: ['rental_type', 'upc', 'hcpc_code', 'dispense_quantity']
					prefill:
						dispense_quantity: '1'
					readonly:
						fields: ['dispense_quantity']
					note: ''
				'Supply':
					fields: ['formatted_ndc', 'upin', 'upc', 'hcpc_code', 'one_time_only', 'dispense_unit', 'quantity_per_container', 'container_unit', 'dispense_quantity', 'supply_billable']
				'Billable':
					fields: ['dispense_quantity', 'hcpc_code']
					note: ''
		view:
			label: 'Item Type'
			offscreen: true
			readonly: true

	supply_billable:
		model:
			required: false
			source: ['Yes']
			if:
				'!':
					prefill:
						billing_method: 'Do Not Bill'
					readonly:
						fields: ['billing_method']
				'Yes':
					fields: ['show_therapy_set_bill_as_kit']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Billable?'
			readonly: true
			offscreen: true

	part_of_kit:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Billed as Kit?'

	payer_order_block:
		model:
			source: ['Yes']
			if:
				'Yes':
					prefill:
						status_id: '1'
		view:
			control: 'checkbox'
			label: 'Payer Order Block'
			readonly: true
			offscreen: true

	billing_method:
		model:
			default: 'Insurance'
			required: false
			source: ['Insurance', 'Self Pay', 'Do Not Bill']
			if:
				'Insurance':
					fields: ['supplies_pa_id', 'insurance_id']
		view:
			columns: -4
			control: 'radio'
			label: 'Billing Method'

	insurance_id:
		model:
			required: true
			source: 'patient_insurance'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			label: 'Primary Payer'
			columns: 2

	supplies_pa_id:
		model:
			required: false
			source: 'patient_prior_auth'
			sourcefilter:
				insurance_id:
					'dynamic': '{insurance_id}'
				patient_id:
					'dynamic': '{patient_id}'
				status_id:
					'static': ['5', '1', '2', '3', '4', '8']
				pa_type:
					'static': 'Drug'
		view:
			add_preset:
				insurance_id: '{insurance_id}'
				patient_id: '{patient_id}'
				status_id: '1'
				pa_type: ['Drug']
			form_link_enabled: true
			columns: 4
			label: 'Authorization'

	auth_flag:
		model:
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Requires Auth?'
			columns: 4

	comments:
		view:
			columns: 2
			label: 'Comments'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['inventory_id']
	indexes:
		many: [
			['site_id']
			['patient_id']
			['careplan_id']
			['inventory_id']
			['associated_rx_id']
	]

	sections_group: [

		'Order Info':
			hide_header: true
			indent: false
			fields: ['site_id', 'careplan_id', 'patient_id', 'associated_rx_id', 'rx_no', 'status_id', 'inventory_id', 'type', 'supply_billable',
			'show_therapy_set_bill_as_kit', 'ndc', 'formatted_ndc', 'upc', 'upin', 'hcpc_code', 'quantity_per_container', 'container_unit', 'dispense_unit',
			'dispense_containers', 'dispense_quantity', 'one_time_only', 'rental_type', 'frequency_code', 'last_billed', 'day_supply', 'payer_order_block']

		'Billing':
			hide_header: true
			indent: false
			fields: ['part_of_kit', 'billing_method', 'insurance_id', 'supplies_pa_id', 'auth_flag']

		'Billing Alerts/Notes':
			hide_header: true
			indent: false
			fields: ['comments']

	]

view:
	dimensions:
		width: '75%'
		height: '45%'
	hide_cardmenu: true
	comment: 'Patient > Order'
	grid:
		fields: ['associated_rx_id', 'inventory_id', 'dispense_quantity', 'status_id']
		sort: ['-created_on']
	find:
		basic: ['inventory_id', 'status_id']
	label: 'Order'
	open: 'edit'
