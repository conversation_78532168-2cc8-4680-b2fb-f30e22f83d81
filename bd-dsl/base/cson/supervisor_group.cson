fields:

	supervisor_id:
		model:
			required: true
			source: "user"
		view:
			label: 'Supervisor'

	team_members:
		model:
			required: true
			multi: true
			source: "user"
		view:
			label: 'Team Members'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ['setup']
	indexes:
		unique: [
			['supervisor_id']
		]
	name: ['supervisor_id']
	sections_group: [
		'Supervisor':
			fields: ['supervisor_id']
		'Team':
			fields: ['team_members']
	]

view:
	comment: 'Manage > Supervisor Group'
	find:
		basic: ['supervisor_id']
	grid:
		fields: ['supervisor_id', 'team_members']
		sort: ['supervisor_id']
	label: 'Supervisor Group'
