fields:

	invoice_no:
		view:
			label: 'Invoice #'
			readonly: true
			offscreen: true

	group_uuid:
		view:
			label: 'Claim Group UUID'
			readonly: true
			offscreen: true

	order_no:
		view:
			label: 'Order #'
			readonly: true
			offscreen: true

	auth_flag:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Prior Authorization Required?'
			readonly: true
			offscreen: true

	is_test:
		model:
			source: ['Yes']
			if:
				'!':
					fields: ['substatus_id']
					readonly:
						fields: ['date_of_service']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Is Test Claim?"
			readonly: true
			offscreen: true
			validate: [
				{
					name: 'ParentPrefill'
					fields: [
						'ncpdp_compound.is_test'
					]
				}
			]

	claim_no:
		view:
			label: 'Claim #'
			readonly: true
			offscreen: true

	trans_show_comp:
		model:
			save: false
		view:
			offscreen: true
			readonly: true

	parent_claim_no:
		view:
			label: 'Parent Claim #'
			note: 'For COB Scenario'
			readonly: true
			offscreen: true

	never_comp_seg:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Never Transmit Compound Segment"
			note: 'From Payer Settings'
			readonly: true
			offscreen: true

	order_id:
		model:
			source: 'careplan_order'
		view:
			label: 'Order'
			readonly: true
			offscreen: true

	order_item_id:
		model:
			source: 'careplan_order_item'
		view:
			label: 'Order Item'
			readonly: true
			offscreen: true
	
	orderp_item_id:
		model:
			source: 'careplan_orderp_item'
		view:
			label: 'Orderp Item'
			readonly: true
			offscreen: true

	rx_no:
		view:
			label: ''
			readonly: true
			offscreen: true

	delivery_ticket_id:
		model:
			source: 'careplan_delivery_tick'
		view:
			label: 'Delivery Ticket'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			type: 'int'
			source: 'inventory'
		view:
			label: 'Drug'
			readonly: true
			offscreen: true

	status:
		model:
			required: false
			source: ["Approved", "Benefit", "Payable", "Margin", "Captured", "Rejected", "Reversed", "Reversal Rejected", "Rebill Rejected", "PA Deferred", "Duplicate"]
		view:
			columns: 2
			class: 'status'
			label: 'Status'
			readonly: true

	substatus_id:
		model:
			source: 'list_billing_csstatus'
			sourceid: 'code'
			track: true
			sourcefilter:
				status_id:
					'dynamic': '{status_id}'
		view:
			columns: 2
			control: 'select'
			label: 'Claim Substatus'
			class: 'status'

	site_id:
		model:
			type: 'int'
			required: false
			source: 'site'
		view:
			columns: 2
			label: 'Site'
			readonly: true
			offscreen: true

	patient_id:
		model:
			type: 'int'
			required: true
			source: 'patient'
		view:
			columns: 2
			label: 'Patient'
			readonly: true
			offscreen: true

	insurance_id:
		model:
			type: 'int'
			required: false
			source: 'patient_insurance'
		view:
			columns: 2
			label: 'Payer'
			readonly: true
			validate: [
				{
					name: 'ParentPrefill'
					fields: [
						'ncpdp_cob.pinsurance_id',
					]
				}
			]

	payer_id:
		model:
			type: 'int'
			required: false
			source: 'payer'
		view:
			columns: 2
			label: 'Payer'
			readonly: true
			offscreen: true

	pt_rel_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '306-C6'
		view:
			label: 'Patient Relationship'
			offscreen: true
			readonly: true

	software_vendor_id:
		model:
			source: 'list_software_cert'
			sourceid: 'code'
		view:
			label: 'Software Vendor ID'
			note: '110-AK'
			columns: 2

	version_number:
		model:
			required: true
			default: "D0"
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '102-A2'
		view:
			label: 'NCPDP Version'
			note: '102-A2'
			reference: '102-A2'
			readonly: true
			offscreen: true

	# B1 - Claim Billing
	# B2 - Reversal
	# B3 - Reverse and Rebill
	# E1 - Eligilibty Check
	# S1 - Service Billing
	# S2 - Service Reversal
	# S3 - Service Reverse and Rebill
	transaction_code:
		model:
			required: true
			default: "B1"
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '103-A3'
			if:
				'B1': # Drug Billing
					sections: ['Response History', 'Patient', 'Claim', 'Prescriber']
					require_fields: ['segment_insurance', 'segment_claim', 'insurance_id', 'site_id', 'payer_id']
					fields: ['embed_response_history', 'segment_patient', 'segment_prescriber', 'segment_claim', 'parent_claim_no', 'billed', 'expected', 'cost', 'paid', 'copay', 'tax']
				'B2': # Reversal
					sections: ['Response History', 'Claim']
					require_fields: ['segment_claim', 'insurance_id', 'site_id', 'payer_id']
					fields: ['embed_response_history', 'segment_claim', 'billed', 'expected', 'cost', 'paid', 'copay', 'tax']

				'B3': # Drug Reverse and Bill
					sections: ['Response History', 'Patient', 'Claim', 'Prescriber']
					require_fields: ['segment_insurance', 'segment_claim', 'insurance_id', 'site_id', 'payer_id']
					fields: ['embed_response_history', 'segment_patient', 'segment_prescriber', 'segment_claim', 'parent_claim_no',
					'billed', 'expected', 'cost', 'paid', 'copay', 'tax']

				'S1': # Service Billing
					sections: ['Response History', 'Patient', 'Claim',  'Prescriber']
					require_fields: ['segment_insurance', 'segment_claim', 'insurance_id', 'site_id', 'payer_id']
					fields: ['embed_response_history', 'segment_patient', 'segment_prescriber', 'segment_claim', 'parent_claim_no', 'billed', 'expected', 'cost', 'paid', 'copay', 'tax']
				
				'S2': # Service Reversal
					sections: ['Response History','Claim']
					require_fields: ['segment_claim', 'insurance_id', 'site_id']
					fields: ['embed_response_history', 'segment_claim','billed', 'expected', 'cost', 'paid', 'copay', 'tax']

				'S3': # Service Reverse and Bill
					sections: ['Response History', 'Patient', 'Claim', 'Prescriber']
					require_fields: ['segment_insurance', 'segment_claim', 'insurance_id', 'site_id', 'payer_id']
					fields: ['embed_response_history', 'segment_patient', 'segment_prescriber', 'parent_claim_no',
					'segment_claim',  'billed', 'expected', 'cost', 'paid', 'copay', 'tax']

				'E1': # Elgibility Check
					sections: ['E1 Response History','Patient']
					require_fields: ['segment_insurance']
					fields: ['embed_e1_response_history', 'segment_patient', 'hide_prescriber_in_e1']
		view:
			offscreen: true
			readonly: true
			label: 'Trans Code'
			note: '103-A3'
			reference: '103-A3'
			validate: [
				{
					name: 'ParentPrefill'
					fields: [
						'ncpdp_cob.transaction_code',
						'ncpdp_dur.transaction_code'
					]
				}
			]
			transform: [
				{
					name: 'MergeFields'
					src_fields: ['never_comp_seg', 'transaction_code']
					separator: '-'
					dest_field: 'trans_show_comp'
				},
				{
					name: "UpdateFormLabel"
					if:
						'B1': 'NCPDP Pharmacy Drug Claim'
						'B2': 'NCPDP Pharmacy Drug Claim Reversal'
						'B3': 'NCPDP Pharmacy Drug Claim Rebill'
						'S1': 'NCPDP Pharmacy Service Claim'
						'S2': 'NCPDP Pharmacy Service Claim Reversal'
						'S3': 'NCPDP Pharmacy Service Claim Rebill'
						'E1': 'NCPDP Pharmacy Eligibility Check'
				}
			]

	bin_number:
		model:
			required: true
			min: 6
			max: 6
		view:
			columns: 4
			label: 'BIN'
			note: '101-A1'
			reference: '101-A1'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{6}$'
				error: 'Invalid BIN, must be 6 digits'
			}]

	process_control_number:
		model:
			max: 10
			required: true
		view:
			columns: 4
			label: 'PCN'
			note: '104-A4'
			reference: '104-A4'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{1,10}$'
				error: 'Invalid PCN, must be 1-10 alphanumeric characters'
			}]

	svc_prov_id_qualifier:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '202-B2'
		view:
			columns: -2
			label: 'Service Prov ID Qualifier'
			note: '202-B2'
			reference: '202-B2'

	svc_prov_id:
		model:
			required: true
		view:
			columns: 4
			label: 'Service Prov ID'
			note: '201-B1'
			reference: '201-B1'

	date_of_service:
		model:
			required: true
			type: 'date'
		view:
			columns: -4
			label: 'Date of Service'
			note: '401-D1'
			reference: '401-D1'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]
			_meta:
				copy_forward: true

	comp_dsg_fm_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '450-EF'
		view:
			columns: 2
			label: 'Compound Dosage Form Code'
			note: '450-EF'
			reference: '450-EF'
			_meta:
				copy_forward: true 

	comp_disp_unit:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '451-EG'
			if:
				'2':
					require_fields: ['comp_dsg_fm_code']
				'3':
					require_fields: ['comp_dsg_fm_code']
		view:
			columns: 2
			label: 'Compound Dispensing Unit'
			note: '451-EG'
			reference: '451-EG'
			_meta:
				copy_forward: true

	segment_patient:
		model:
			type: 'subform'
			multi: false
			source: 'ncpdp_patient'
		view:
			note: 'Segment 01'
			label: 'Patient'

	segment_pharmacy:
		model:
			required: true
			type: 'subform'
			multi: false
			source: 'ncpdp_pharmacy'
		view:
			note: 'Segment 02'
			label: 'Pharmacy'
			_meta:
				copy_forward: true

	hide_prescriber_in_e1:
		model:
			default: 'No'
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['segment_prescriber']
					sections: ['Prescriber']
		view:
			label: 'Hide Prescriber Segment in E1?'
			offscreen: true
			readonly: true

	segment_prescriber:
		model:
			type: 'subform'
			multi: false
			source: 'ncpdp_prescriber'
		view:
			note: 'Segment 03'
			label: 'Prescriber'
			_meta:
				copy_forward: true

	segment_insurance:
		model:
			required: true
			type: 'subform'
			multi: false
			source: 'ncpdp_insurance'
		view:
			note: 'Segment 03'
			label: 'Insurance'

	segment_cob:
		model:
			type: 'subform'
			multi: false
			source: 'ncpdp_cob'
			if:
				'*':
					prefill:
						segment_claim: [{ require_other_coverage_code: "Yes" }]
				'!':
					prefill:
						segment_claim: [{ require_other_coverage_code: '' }]
		view:
			note: 'Segment 05'
			label: 'COB'

	segment_workers_comp:
		model:
			type: 'subform'
			multi: false
			source: 'ncpdp_worker_comp'
		view:
			note: 'Segment 06'
			label: "Worker's Comp"
			_meta:
				copy_forward: true
			offscreen: true
			readonly: true

	segment_claim:
		model:
			type: 'subform'
			multi: false
			source: 'ncpdp_claim'
		view:
			note: 'Segment 07'
			label: 'Claim'

	segment_dur:
		model:
			type: 'subform'
			multi: false
			source: 'ncpdp_dur'
		view:
			note: 'Segment 08'
			label: 'DUR/PPS'

	segment_coupon:
		model:
			type: 'subform'
			multi: false
			source: 'ncpdp_coupon'
		view:
			grid:
				add: 'inline'
				edit: true
				fields: ['coupon_type', 'coupon_number', 'coupon_value_amount']
				label: ['Type', '#', 'Amt']
				width: [25, 50, 25]
			max_count: 1
			note: 'Segment 09'
			label: 'Coupon'
			_meta:
				copy_forward: true

	show_compound:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['segment_compound']
					sections: ['Compound']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Show Compound?'
			offscreen: true
			readonly: true

	segment_compound:
		model:
			type: 'subform'
			multi: true
			source: 'ncpdp_compound'
		view:
			grid:
				add: 'inline'
				edit: true
				fields: ['product_id', 'cmp_ing_qty', 'cmp_ing_cost', 'cmp_ing_cost_basis', 'cmp_ing_md_code']
				label: ['Drug', 'Quantity', 'Cost', 'Basis', 'Code']
				width: [30, 10, 20, 20, 20]
			note: 'Segment 10'
			label: 'Compound Ingredients'
			max_count: 25
			readonly: true
			offscreen: true

	segment_pricing:
		model:
			type: 'subform'
			multi: false
			source: 'ncpdp_pricing'
		view:
			note: 'Segment 11'
			label: 'Pricing'
			readonly: true
			offscreen: true

	segment_clinical:
		model:
			type: 'subform'
			multi: false
			source: 'ncpdp_clinical'
		view:
			note: 'Segment 13'
			label: 'Clinical'

	segment_docs:
		model:
			type: 'subform'
			multi: false
			source: 'ncpdp_doc'
		view:
			note: 'Segment 14'
			label: 'Additional Documentation'
			offscreen: true
			readonly: true
			_meta:
				copy_forward: true

	segment_facility:
		model:
			type: 'subform'
			multi: false
			source: 'ncpdp_facility'
		view:
			note: 'Segment 15'
			label: 'Facility'
			offscreen: true
			readonly: true
			_meta:
				copy_forward: true

	segment_narrative:
		model:
			type: 'subform'
			multi: false
			source: 'ncpdp_narrative'
		view:
			note: 'Segment 16'
			label: 'Narrative'
			_meta:
				copy_forward: true

	billed:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Billed'
			readonly: true
			offscreen: true

	expected:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Expected'
			readonly: true
			offscreen: true

	paid:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Paid'
			readonly: true
			offscreen: true

	copay:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			label: 'Copay'
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			offscreen: true

	tax:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			label: 'Tax Paid'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			offscreen: true

	cost:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			label: 'Cost'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			offscreen: true

	void:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['voided_datetime', 'void_reason_id']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Void Medical Claim?'
			findfilter: '!Yes'
			class: 'checkbox-only'
			readonly: true
			offscreen: true

	void_reason_id:
		model:
			required: true
			source: 'list_void_reason_billing'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			label: 'Void Reason'
			readonly: true
			offscreen: true

	voided_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Voided Date'
			template: '{{now}}'
			readonly: true
			offscreen: true

	request_json_data:
		model:
			type: 'json'
		view:
			class: 'json-viewer'
			label: ' '
			readonly: true
			offscreen: true

	response_json_data:
		model:
			type: 'json'
		view:
			class: 'json-viewer'
			label: ' '
			readonly: true
			offscreen: true

	request_d0_raw:
		view:
			control: 'raw'
			label: 'Raw D0 request'
			readonly: true
			offscreen: true

	response_d0_raw:
		view:
			control: 'raw'
			label: 'Raw D0 response'
			readonly: true
			offscreen: true

	embed_response_history:
		model:
			required: false
			sourcefilter:
				claim_no:
					'dynamic': '{claim_no}'
		view:
			embed:
				form: 'ncpdp_response_summary'
				selectable: false
			grid:
				add: 'none'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['response_datetime', 'claim_status', 'total_paid_amount', 'pt_pay_amt', 'total_profit', 'margin']
				label: ['Resp Dt/Tm', 'Claim Status', 'Paid $', 'Copay $', 'Profit $', 'Margin %']
				width: [20, 20, 15, 15, 15, 15]
			label: 'Response History'
			readonly: true

	embed_e1_response_history:
		model:
			required: false
			sourcefilter:
				claim_no:
					'dynamic': '{claim_no}'
		view:
			embed:
				form: 'ncpdp_response_elig_summary'
				selectable: false
			grid:
				add: 'none'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['response_datetime', 'claim_status', 'transaction_response_status', 'message']
				label: ['Resp Dt/Tm', 'Claim Status', 'Trans Status', 'Message']
				width: [20, 20, 20, 40]
			label: 'Response History'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['site_id']
			['patient_id']
			['insurance_id']
			['payer_id']
			['claim_no']
			['invoice_no']
			['parent_claim_no']
		]

	name: '{status}-{substatus_id_auto_name} {inventory_id_auto_name} ${billed}'

	sections_group: [
		'NCPDP Claim':
			hide_header: true
			sections: [
				'Header':
					hide_header: true
					indent: false
					tab: 'Header'
					fields: ['invoice_no', 'claim_no', 'parent_claim_no', 'transaction_code',
							'hide_prescriber_in_e1', 'trans_show_comp','never_comp_seg',
							'status', 'substatus_id', 'inventory_id', 'site_id', 'patient_id', 'date_of_service', 'software_vendor_id', 'insurance_id', 'payer_id',
							'pt_rel_code', 'version_number', 'bin_number', 'process_control_number', 'svc_prov_id_qualifier', 'svc_prov_id',
							'comp_dsg_fm_code', 'comp_disp_unit', 'show_compound']
				'Response History':
					hide_header: true
					indent: false
					tab: 'Header'
					fields: ['embed_response_history']
				'E1 Response History':
					hide_header: true
					indent: false
					tab: 'Header'
					fields: ['embed_e1_response_history']
				'Patient':
					hide_header: true
					indent: false
					tab: 'Patient'
					fields: ['segment_patient']
				'Pharmacy':
					hide_header: true
					indent: false
					tab: 'Pharmacy'
					fields: ['segment_pharmacy']
				'Prescriber':
					hide_header: true
					indent: false
					tab: 'Prescriber'
					fields: ['segment_prescriber']
				'Insurance':
					hide_header: true
					indent: false
					tab: 'Insurance'
					fields: ['segment_insurance']
				'Claim':
					hide_header: true
					indent: false
					tab: 'Claim'
					fields: ['segment_claim']
				'Compound':
					hide_header: true
					indent: false
					tab: 'Compound'
					fields: ['segment_compound']
				'Narrative':
					modal: true
					hide_header: true
					indent: false
					tab: 'Header'
					fields: ['segment_narrative']
				'Coupon':
					hide_header: true
					indent: false
					modal: true
					tab: 'Insurance'
					fields: ['segment_coupon']
				'COB':
					hide_header: true
					indent: false
					tab_toggle: true
					tab: 'COB / Other Payers'
					fields: ['segment_cob']
				'Clinical':
					hide_header: true
					indent: false
					modal: true
					tab: 'Dx/Measures'
					fields: ['segment_clinical']
				'DUR/PPS':
					hide_header: true
					indent: false
					modal: true
					tab: 'DUR/PPS'
					fields: ['segment_dur']
			]
	]

view:
	block:
		validate: [
			name: 'NCPDPBlock'
		]
	comment: 'NCPDP Transaction'
	find:
		basic: ['site_id', 'patient_id', 'insurance_id', 'payer_id', 'status',  'substatus_id', 'date_of_service']
		advanced: ['transaction_code']
	grid:
		fields: ['status', 'substatus_id', 'payer_id', 'inventory_id', 'expected', 'paid']
		width: [15, 15, 25, 25, 10, 10]
		sort: ['-created_on']
	label: 'NCPDP Transaction'
	open: 'edit'