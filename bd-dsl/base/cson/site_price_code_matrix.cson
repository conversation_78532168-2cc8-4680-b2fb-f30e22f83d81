fields:

	name:
		model:
			required: true
		view:
			label: 'Site Shared Contract Name'

	site_id:
		model:
			required: true
			source: 'site'
		view:
			label: 'Assigned Site(s)'

	subform_codes:
		model:
			type: 'subform'
			source: 'site_price_code_item'
			multi: true
		view:
			grid:
				edit: true
				add: 'inline'
				fields: ['price_code_id','price_formula_id', 'multiplier']
				label: ['Code', 'Formula', 'Multiplier']
				width: [30, 40, 30]
			label: 'Pricing Code Items'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['billing']
	indexes:
		unique: [
			['site_id']
		]

	reportable: true
	name: '{name} {site_id_auto_name}'

	sections_group: [
		'Site Price Code Matrix':
			sections: [
				'Site Details':
					fields: ['name','site_id']
				'Price Code Matrix':
					fields: ['subform_codes']
			]
		]

view:
	hide_cardmenu: true
	comment: 'Payer > Site Price Code Matrix'
	grid:
		fields: ['name', 'site_id']
		sort: ['-name']
	label: 'Site Price Code Matrix'
	open: 'read'