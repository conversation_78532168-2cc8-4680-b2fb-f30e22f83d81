fields:

    shipment_date:
        model:
            required: true
            type: 'datetime'
        view:
            label: 'Shipment Date'

    order_id:
        model:
            required: true
            source: 'careplan_order'
        view:
            label: 'Order'
            offscreen: true
            readonly: true

    order_item_id:
        model:
            multi: true
            required: false
            source: 'careplan_order_rx'
        view:
            label: 'Prescription'
            offscreen: true
            readonly: true

    delivery_ticket_id:
        model:
            required: true
            source: 'careplan_delivery_tick'
        view:
            label: 'Delivery Ticket'
            offscreen: true
            readonly: true

    view_delivery_ticket:
        model:
            default: '{"url":"{#/patient/{patient_id}/careplan_delivery_tick/{delivery_ticket_id}/read}","title":"View Label", "icon": "fa fa-link"}'
            type: 'json'
            save: false
        view:
            label: ''
            control: 'link'
            offscreen: false
            readonly: true

    patient_id:
        model:
            required: true
            source: 'patient'
            type: 'int'
        view:
            offscreen: true
            readonly: true
            label: 'Patient'

    rate_id:
        model:
            required: true
        view:
            offscreen: true
            label: 'Rate Id'

    shipment_id:
        model:
            required: true
        view:
            offscreen: true
            readonly: true
            label: 'Shipment Id'

    amount:
        model:
            required: true
            type: 'decimal'
        view:
            label: 'Amount'
            readonly: true
    
    currency:
        model:
            required: true
        view:
            label: 'Currency'
            readonly: true
    
    provider:
        model:
            required: true
        view:
            label: 'Provider'
            readonly: true

    estimated_days:
        model:
            required: true
        view:
            label: 'Estimated Days'
            readonly: true
    
    service_level:
        model:
            required: true
        view:
            label: 'Service Level'
            readonly: true

    shipment_status:
        model:
            required: true
        view:
            class: 'status'
            label: 'Shipment Status'
            readonly: true

    tracking_status:
        model:
            required: true
        view:
            class: 'status'
            label: 'Tracking Status'
            readonly: true
    tracking_number:
        model:
            required: true
        view:
            label: 'Tracking Number'
            readonly: true
    label_url:
        model:
            required: true
        view:
            label: 'Label URL'
            readonly: true
    tracking_url:
        model:
            required: true
        view:
            label: 'Tracking URL'
            readonly: true
            validate: [
                {
                    name: 'SetTrackingUrl'
                }
            ]
    subform_parcel:
        model:
            multi: true
            source: 'shipment_parcel'
            type: 'subform'
        view:
            label: 'Parcels'
            grid:
                add: 'flyout'
                hide_cardmenu: true
                edit: false
                fields: ['height', 'width', 'length', 'weight']
                label: ['Ht', 'Wd', 'Ln', 'Wt']
                width: [25, 25, 25, 25]

    view_label:
        model:
            default: '{"url":"{label_url}","title":"View Label", "icon": "fa fa-link"}'
            type: 'json'
            save: false
        view:
            label: ''
            control: 'link'
            offscreen: false
            readonly: true

    view_tracking:
        model:
            default: '{"url":"{tracking_url}","title":"View Shipment Status", "icon": "fa fa-link"}'
            type: 'json'
            save: false
        view:
            label: ''
            control: 'link'
            offscreen: false
            readonly: true


model:
    access:
        create:     []
        create_all: ['admin']
        delete:     ['admin']
        read:       ['admin']
        read_all:   ['admin']
        request:    []
        update:     []
        update_all: ['admin']
        write:      ['admin']
    bundle: ['setup']
    indexes: 
        unique: []
    
    name: '{delivery_ticket_id} - {shipment_date}'
    sections_group: [
        'Shipment Details':
            fields: ['shipment_date', 'delivery_ticket_id', 'view_delivery_ticket', 'patient_id', 'currency', 'amount', 'provider', 'estimated_days', 'service_level', 'shipment_status', 'tracking_status', 'tracking_number', 'view_label', 'view_tracking']
        'Parcel Details':
            fields: ['subform_parcel']
    ]

view:
    hide_cardmenu: true
    comment: 'Delivery Ticket > Shipments'
    grid:
        fields: ['shipment_date', 'delivery_ticket_id', 'amount', 'currency', 'provider', 'estimated_days', 'service_level', 'shipment_status', 'tracking_status', 'tracking_number']
        sort: ['-id']
    label: 'Shipments'
    open: 'read'
