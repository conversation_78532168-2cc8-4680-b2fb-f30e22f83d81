fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	date:
		model:
			type: 'date'
		view:
			label: 'Bleed Episode Date'
			columns: 4

	cause:
		model:
			source: ['Spontaneous', 'Minor trauma', 'Major Trauma', 'Surgery', 'Other']
			if:
				'Other':
					fields: ['cause_other']
		view:
			control: 'radio'
			label: "Cause"
			columns:4

	cause_other:
		view:
			label: "Other Cause"
			columns: 3.1

	severity:
		model:
			source: ["Mild", "Serious", "Life Threatening"]
		view:
			note: "Mild (Cutaneous, Superficial)  Serious (Joint, Muscle, Mucous Membranes)  Life Threatening (Intracranial, Neck/Throat, Gastrointestinal) "
			control: 'radio'
			label: "Severity"
			columns: 3

	site:
		model:
			multi: true
			source: ["Left Knee", "Right Knee", "Left Ankle", "Right Ankle", "Left Hip", "Right Hip", "Left Elbow", "Right Elbow", "Left Hand", "Right Hand", "Left Shoulder", "Right Shoulder", "Head/Neck", "Gastrointestinal", "Nose", "Menstrual", "Oral", "Skin", "Muscle", "Other"]
			if:
				'Nose':
					fields: ['site_location']
				'Menstrual':
					fields: ['site_location']
				'Oral':
					fields: ['site_location']
				'Skin':
					fields: ['site_location']
				'Muscle':
					fields: ['site_location']
				'Other':
					fields: ['site_other']
		view:
			control: 'checkbox'
			label: "Site"
			columns: 3.1

	site_location:
		view:
			label: "Location Details"
			columns: 4

	site_other:
		view:
			label: "Other Site"
			columns: 4

	duration:
		model:
			type: 'decimal'
			rounding: 1
		view:
			label: 'Duration of bleed'
			columns:3

	duration_type:
		model:
			source: ['Hours', 'Days']
		view:
			label: 'Duration Type'
			control: 'radio'
			columns:3

	doses_used:
		model:
			type: 'int'
			rounding: 1
		view:
			label: 'Number of On Demand doses used to treat bleed'
			columns:3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
	prefill:
		patient:
			link:
				id: 'patient_id'
	name: '{date} {site} severity: {severity}'
	sections:
		'Bleed Event Details':
					fields: ['date', 'cause', 'severity', 'cause_other', 'site',  'site_location', 'site_other', 'duration', 'doses_used', 'duration_type']

	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Bleed Event"
	]
view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Assessment > Factor > Bleed'
	find:
		basic: ['severity', 'site', 'date']
	grid:
		fields: ['created_on', 'created_by', 'date', 'cause', 'severity', 'site', 'duration', 'duration_type', 'doses_used']
		sort: ['-created_on']
	label: 'Assessment Questionnaire: Factor Bleed Event'
