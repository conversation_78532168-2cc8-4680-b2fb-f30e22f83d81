fields:

	charge_no:
		model:
			prefill: ['parent.charge_no']
			required: false
		view:
			label: 'Charge No'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			source: 'inventory'
			required: true
			query: 'select_inv_in_stock'
			querytemplate: 'inventoryTemplate'
			sourcefilter:
				type:
					'static': ['Drug']
				active:
					'static': 'Yes'
		view:
			form_link_enabled: true
			columns: 2
			label: 'Drug'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'national_drug_code': ['ndc'],
					'measurement_unit_code':
						'type': 'conditional'
						'billing_unit_id':
							if:
								'each': 'UN'
								'mL': 'ML'
								'gram': 'GR'
			]

	service_id_qualifier:
		model:
			default: 'N4'
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'LIN02'
		view:
			columns: 2
			label: 'Service ID Qualifier'
			reference: 'LIN02'
			readonly: true
			_meta:
				location: '2410 LIN'
				field: '02'
				path: 'claimInformation.serviceLines[{idx1-50}].drugIdentification.serviceIdQualifier'

	national_drug_code:
		model:
			required: true
			min: 1
			max: 48
		view:
			columns: 4
			label: 'NDC'
			reference: 'LIN'
			readonly: true
			_meta:
				location: '2410 LIN'
				field: '03'
				path: 'claimInformation.serviceLines[{idx1-50}].drugIdentification.nationalDrugCode'

	national_drug_unit_count:
		model:
			required: true
			max: 99999999999.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			label: 'Charge Units'
			reference: 'CTP'
			_meta:
				location: '2410 CTP'
				field: '04'
				path: 'claimInformation.serviceLines[{idx1-50}].drugIdentification.nationalDrugUnitCount'

	measurement_unit_code:
		model:
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CTP05-01'
		view:
			columns: 4
			label: 'Unit Code'
			reference: 'CTP05'
			readonly: true
			_meta:
				location: '2410 CTP05'
				field: '-01'
				path: 'claimInformation.serviceLines[{idx1-50}].drugIdentification.measurementUnitCode'

	link_sequence_number:
		model:
			prefill: ['parent.assigned_number']
			max: 999999
			min: 1
			type: 'int'
		view:
			columns: 4
			label: 'Sequence Number'
			readonly: true
			note: 'Used to reference other linked service lines'
			reference: 'REF02 REF01=VY'
			_meta:
				location: '2410 REF'
				field: '02'
				code: 'VY'
				path: 'claimInformation.serviceLines[{idx1-50}].drugIdentification.linkSequenceNumber'

	pharmacy_prescription_number:
		model:
			min: 1
			max: 50
		view:
			columns: 4
			label: ''
			reference: 'REF02 REF01=XZ'
			_meta:
				location: '2410 REF'
				field: '02'
				code: 'XZ'
				path: 'claimInformation.serviceLines[{idx1-50}].drugIdentification.pharmacyPrescriptionNumber'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['inventory_id', 'national_drug_code']
	sections:
		'Drug Identification':
			hide_header: true
			fields: ['charge_no', 'inventory_id', 'service_id_qualifier', 'national_drug_code',
					'national_drug_unit_count', 'measurement_unit_code', 'link_sequence_number',
					'pharmacy_prescription_number']

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	reference: '2410'
	comment: 'Drug Identification'
	grid:
		fields: ['inventory_id', 'national_drug_unit_count', 'measurement_unit_code']
		sort: ['-created_on']
	label: 'Drug Identification'
	open: 'read'