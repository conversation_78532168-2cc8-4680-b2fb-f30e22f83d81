fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	gender:
		model:
			if:
				'Female':
					fields: ['are_preg']
			prefill: ['patient']
		view:
			label: 'Sex'
			offscreen: true
			readonly: true

	has_sulfite_allergy:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you allergic to sulfite?'

	has_asthma:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with Asthma?'

	are_preg:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: ' Are you currently pregnant or have given birth in the last 6 weeks?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'csr', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Radicava Assessment':
			fields: ['gender', 'has_sulfite_allergy', 'has_asthma', 'are_preg']
view:
	comment: 'Patient > Careplan > Assessment > Radicava'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Radicava'
	open: 'read'
