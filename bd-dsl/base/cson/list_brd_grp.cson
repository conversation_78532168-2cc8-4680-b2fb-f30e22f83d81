fields:

	fdb_brd_grp_id:
		model:
			required: false
			source: 'list_fdb_brd_grp'
			sourceid: 'code'
		view:
			label: 'FDB Drug Brand Group'
			columns: 3
			class: 'select_prefill fdb-field'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_fdb_brd_grp/?limit=1&fields=list&sort=name&page_number=0&filter=code:'
				fields:
					'code': ['code'],
					'name': ['name'],
					'billing_unit_id': ['billing_unit_id'],
					'brand_name_id': ['brand_name_id'],
					'storage_id': ['storage_id']
			]

	concentration:
		model:
			required: true
		view:
			label: 'Concentration'
			note: 'Set to LOWEST concentration in the group'
			columns: 3
			validate: [
				name: 'InventoryValidateConcentration'
			]

	code:
		model:
			required: true
		view:
			columns: 3
			readonly: true
			label: 'FDB MEDID'

	active:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			columns: 3
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Active?'

	#med_medid_desc
	name:
		model:
			search: 'A'
			required: true
			max: 128
		view:
			columns: 3
			label: 'Name'
			findunique: true

	brand_name_id:
		model:
			required: true
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Short Brand Name'
			columns: 3
			readonly: true

	therapy_id:
		model:
			max: 64
			source: 'list_therapy'
			sourceid: 'code'
			required: true
		view:
			label: 'Therapy'
			columns: 3

	billing_unit_id:
		model:
			required: true
			source: 'list_unit'
			sourceid: 'code'
			sourcefilter:
					code:
						'static': ['each', 'mL', 'gram']
		view:
			control: 'select'
			label: 'Billing Unit'
			columns: 3
			readonly: true

	flt_dose_unit:
		model:
			source: 'list_unit'
			sourceid: 'code'
			multi: true
			sourcefilter:
				dose_unit:
					'static': 'Yes'
		view:
			label: 'Dose Units'
			offscreen: true
			readonly: true

	default_dosing_unit_id:
		model:
			required: true
			source: 'list_unit'
			sourceid: 'code'
			sourcefilter:
				dose_unit:
					'static': 'Yes'
				code:
					'dynamic': '{flt_dose_unit}'
				active:
					'static': 'Yes'
		view:
			control: 'select'
			label: 'Default Dosing Unit'
			columns: 3.1
			transform: [
				name: 'CopyForwardSelect2ToMulti'
				dest: 'dosing_unit_id'
			]

	dosing_unit_id:
		model:
			required: true
			multi: true
			source: 'list_unit'
			sourceid: 'code'
			sourcefilter:
				dose_unit:
					'static': 'Yes'
				code:
					'dynamic': '{flt_dose_unit}'
				active:
					'static': 'Yes'
		view:
			columns: 3
			control: 'select'
			label: 'Available Dosing Units'

	flt_disp_unit:
		model:
			source: 'list_unit'
			sourceid: 'code'
			multi: true
		view:
			label: 'Dispensing Units'
			offscreen: true
			readonly: true

	route_id:
		model:
			required: true
			source: 'list_route'
			sourceid: 'code'
		view:
			label: 'Route'
			columns: 3

	dea_schedule_id:
		model:
			source: 'list_dea_schedule'
			sourceid: 'code'
		view:
			label: 'DEA Schedule'
			columns: 3

	sp_pk_indicator:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '429-DT'
		view:
			columns: 3
			note: '429-DT'
			label: 'Default Special Packaging Indicator'
			offscreen: true
			readonly: true

	storage_id:
		model:
			source: 'list_storage'
			sourceid: 'code'
		view:
			label: 'Storage'
			columns: 3

	taxable:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Taxable?'
			columns: 3
model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['inventory']
	indexes:
		many: [
				['code']
				['fdb_brd_grp_id']
				['brand_name_id']
				['therapy_id']
				['billing_unit_id']
				['dosing_unit_id']
				['default_dosing_unit_id']
			]
	name: '{name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['fdb_brd_grp_id', 'code', 'active','name', 
			'brand_name_id', 'therapy_id', 'concentration', 'billing_unit_id',
			'default_dosing_unit_id', 'dosing_unit_id', 'flt_dose_unit', 'flt_disp_unit','route_id',
			'dea_schedule_id', 'storage_id', 'sp_pk_indicator', 'taxable']

view:
	comment: 'Manage > Drug Brand Group'
	find:
		basic: ['code', 'name', 'therapy_id', 'active']
	grid:
		fields: ['code', 'name', 'therapy_id', 'active']
		width: [15, 50, 25, 10]
		sort: ['name']
	label: 'Drug Brand Group'
	open: 'read'
