fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			required: false
			source: 'careplan_order'
			type: 'int'
		view:
			label: 'Referral Id'

	#TODO Need to pass in delivery ticket ID from parent form
	delivery_ticket_id:
		model:
			required: true
			source: 'careplan_delivery_tick'
			type: 'int'
		view:
			label: 'Delivery Ticket Id'

	# Admin Step
	start_time:
		model:
			required: true
			type: 'time'
		view:
			label: 'Time Started'
			template: '{{now}}'

	comment:
		model:
			max: 4096
		view:
			control: 'area'
			label: "Administration Step Comment"

	# Pre-infusion Flush
	pre_flush_id:
		model:
			type: 'int'
			source: 'patient_medication'
			sourcefilter:
				type_id:
					'static': 'Hydration'
				dt_id:
					'dynamic': '{delivery_ticket_id}'
			if:
				'*':
					fields: ['pre_flush_vol','pre_flush_comment']
		view:
			control: 'select'
			label: 'Pre-infusion flush'

	pre_flush_vol:
		model:
			type: 'decimal'
			min: 0
		view:
			label: 'Pre-infusion (mls)'

	pre_flush_comment:
		view:
			label: 'Pre-infusion comment'

	# Hydration
	hydration_id:
		model:
			type: 'int'
			source: 'patient_medication'
			sourcefilter:
				type_id:
					'static': 'Hydration'
				dt_id:
					'dynamic': '{delivery_ticket_id}'
			if:
				'*':
					fields: ['hydration_vol','hydration_comment']
		view:
			control: 'select'
			label: 'Hydration'

	hydration_vol:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Hydration Volume Infused (mls)'

	hydration_comment:
		view:
			label: 'Hydration Comment'

	# Primary Admin
	primary_admin_id:
		model:
			required: true
			type: 'int'
			source: 'patient_medication'
			sourcefilter:
				type_id:
					'static': 'Primary'
				dt_id:
					'dynamic': '{delivery_ticket_id}'
			if:
				'*':
					fields: ['dose_given','dose_time','lot_details']
		view:
			control: 'select'
			label: 'Primary Drug Administered'

	#TODO Need to auto-populate units from primary drug in label
	dose_given:
		model:
			required: true
			rounding: 0.05
			type: 'decimal'
		view:
			label: 'Dose Given ({units})'

	waste:
		model:
			rounding: 0.05
			type: 'decimal'
		view:
			label: 'Waste ({units})'

	#TODO Make it a smart field where '5H' converts to 300
	dose_time:
		model:
			required: true
			rounding: 0.05
			type: 'decimal'
		view:
			note: 'Enter {hours}H to automatically covert hours to minutes'
			label: 'Over Time Period (minutes)'

	#TODO Pull in drug lot details and have them check it with a number?
	lot_details:
		model:
			subfields:
				lot:
					label: 'Drug Lot#'
				expiration:
					label: 'Expiration Date'
					type: 'date'
			type: 'json'
		view:
			control: 'grid'
			label: 'Drug/Vial Details'

	# Post infusion flush
	post_flush_id:
		model:
			type: 'int'
			source: 'patient_medication'
			sourcefilter:
				type_id:
					'static': 'Flush'
				dt_id:
					'dynamic': '{delivery_ticket_id}'
			if:
				'*':
					fields: ['post_flush_vol','post_flush_comment']
		view:
			control: 'select'
			label: 'Pre-infusion flush'

	post_flush_vol:
		model:
			type: 'decimal'
			min: 0
		view:
			label: 'Pre-infusion (mls)'

	post_flush_comment:
		view:
			label: 'Pre-infusion comment'


model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		admin_catheter:
			link:
				careplan_id: 'careplan_id'
			max: 'id'
	name: '{start_time} : {dose_given} over {dose_time} hours'
	sections:
		'Admin Step':
			fields: ['start_time']
		'Pre-Infusion Flush':
			fields: ['pre_flush_id', 'pre_flush_vol', 'pre_flush_comment']
			prefill: 'encounter_admin_iv'
		'Hydration':
			fields: ['hydration_id', 'hydration_vol', 'hydration_comment']
			prefill: 'encounter_admin_iv'
		'Primary Administration':
			note: 'Check the drug label with the order before administering'
			fields: ['primary_admin_id','dose_given','dose_time', 'waste', 'lot_details']
		'Post-Infusion Flush':
			fields: ['post_flush_id', 'post_flush_vol','post_flush_comment']
			prefill: 'encounter_admin_iv'
		'Admin Step Comment':
			fields: ['comment']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Encounter > IV > Admin'
	grid:
		fields: ['start_time', 'primary_admin_id', 'dose_given', 'dose_time', 'comment']
	label: 'IV Admin Step'
