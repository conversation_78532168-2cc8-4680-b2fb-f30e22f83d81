fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Main
	time_taken:
		model:
			required: true
			type: 'time'
		view:
			label: 'Time'
			template: '{{now}}'
			columns: 3

	temp:
		model:
			min: 89
			max: 110
			type: 'decimal'
		view:
			label: 'Temp (F)'
			columns: -3

	temp_method:
		model:
			max: 12
			source: ['PO', 'Rectal', 'Tympanic', 'Temporal', 'Ax', 'Other']
		view:
			control: 'radio'
			label: 'Temp Method'
			columns: 3

	pulse:
		model:
			type: 'int'
			max: 220
			min: 60
		view:
			note: 'bpm (60-220)'
			label: 'Pulse'
			columns: -3

	pulse_method:
		model:
			max: 12
			source: ['Apical', 'Radial']
		view:
			control: 'radio'
			label: 'Pulse Method'
			columns: 3

	bp:
		model:
			if:
				'*':
					require_fields: ['bp_method', 'bp_loc']
		view:
			note: 'systolic / diastolic'
			label: 'Blood Pressure'
			columns: 3
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\s*(\\d{2,3})\\s*\\/\\s*(\\d{2,3})\\s*$'
				error: 'Invalid Blood Pressure, must be systolic / diastolic'
			}]

	bp_method:
		model:
			max: 12
			source: ['Sitting', 'Standing', 'Lying', 'Brachial']
		view:
			control: 'radio'
			label: 'BP Method'
			columns: 3

	bp_loc:
		model:
			max: 12
			source: ['Right Arm', 'Left Arm', 'Right Leg', 'Left Leg']
		view:
			control: 'radio'
			label: 'BP Location'
			columns: 3

	o2_sat:
		model:
			type: 'decimal'
			min: 1
			max: 100
			rounding: 1
		view:
			label: 'O2 Sat (%)'
			columns: -3
			class: 'numeral'
			format: 'percent'

	titration:
		model:
			type: 'int'
		view:
			label: 'Titration Rate'
			columns: -3

	titration_type:
		model:
			default: 'mL/hr'
			source: ['mL/hr', 'gtt/min']
		view:
			control: 'radio'
			label: 'Titration Rate Type'
			columns: 3

	comment:
		view:
			control: 'area'
			label: 'Comment'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	sections:
		'Infusion Log':
			fields: ['time_taken','temp', 'temp_method', 'pulse',
			'pulse_method', 'bp', 'bp_method', 'bp_loc', 'o2_sat',
			'titration', 'titration_type', 'comment']

view:
	comment: 'Patient > Careplan > Encounter > Vitals'
	grid:
		fields: ['time_taken', 'titration', 'titration_type', 'temp', 'pulse', 'bp', 'comment']
	label: 'Patient Encounter: Vitals'
