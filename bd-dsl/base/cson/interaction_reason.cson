fields:

	reason:
		model:
			type:'text'
		view:
			label: 'Reason to Administer'
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ['manage']
	sections_group: [
		'Reason':
			fields: ['reason']
	]
	name: ['reason']

view:
	comment: 'Manage > Interaction Reason'
	find:
		basic: ['reason']
	grid:
		fields: ['reason']
		sort: ['reason']
	label: 'Interaction Reason'