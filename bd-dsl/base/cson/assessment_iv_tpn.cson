fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	pediatric:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			prefill: ['assessment_tpn']
			if:
				'Yes':
					fields: ['wt_50', 'wt_pt', 'ht_pt']
				'No':
					fields: ['ibw', 'ibw_percentage', 'abw']
		view:
			control: 'radio'
			label: "Pediatric patient?"
			columns:3

	wt_50:
		model:
			max: 1000
			min: 1
			required: true
			rounding: 0.01
			type: 'decimal'
			prefill: ['assessment_tpn']
		view:
			class: 'unit'
			label: 'Weight at 50%ile'
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]
			columns:3

	wt_pt:
		model:
			max: 100
			min: 1
			required: true
			rounding: 0.01
			type: 'decimal'
			prefill: ['assessment_tpn']
		view:
			label: 'Weight %ile'
			columns:3

	ht_pt:
		model:
			max: 100
			min: 1
			required: true
			rounding: 0.01
			type: 'decimal'
			prefill: ['assessment_tpn']
		view:
			label: 'Height %ile'
			columns:3

	ibw:
		model:
			max: 1000
			min: 1
			required: true
			rounding: 0.01
			type: 'decimal'
			prefill: ['assessment_tpn']
		view:
			class: 'unit'
			label: 'IBW'
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]
			columns:3

	ibw_percentage:
		model:
			max: 100
			min: 1
			required: true
			rounding: 0.01
			type: 'decimal'
			prefill: ['assessment_tpn']
		view:
			label: '%IBW'
			class: 'numeral'
			format: 'percent'
			columns:3
	abw:
		model:
			max: 1000
			min: 1
			rounding: 0.01
			type: 'decimal'
			prefill: ['assessment_tpn']
		view:
			class: 'unit'
			label: 'Adjusted BW'
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]
			columns:3

	egg_allergy:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you allergic or have a sensitivity to eggs, fish,  peanut protein, soy or soybean products?'
			columns:3

	latex_allergy:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Does patient have a latex allergy?'
			columns:3

	have_documents:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Hospital nutritional assessment, dietitian's clinical notes, Medicare supporting documentation available?"
			columns:3

	activity_factor:
		model:
			max: 64
			min: 1
			source: {0:'Sedentary', 1:'Normal', 2:'Active', 3:'Very Active'}
		view:
			control: 'radio'
			label: "Patient's estimated Activity Factor?"
			columns:3

	stress_factor:
		model:
			max: 64
			min: 1
			source: {0:'Trauma, stressed, or surgical patient or underweight', 1:'Severe burn patient'}
		view:
			control: 'radio'
			label: "Patient's Stress Factor?"
			columns:3

	prepreg_weight:
		model:
			max: 1000
			min: 1
			rounding: 0.01
			type: 'decimal'
			prefill: ['assessment_tpn']
		view:
			class: 'unit'
			label: 'Pre-Pregnancy Weight'
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]
			columns:3

	any_drains:
		model:
			max: 3
			min: 1
			source: ['PleureX', 'Jackson-Pratt(JP)', 'Chest/abdominal tube', 'Other']
			if:
				'Other':
					fields: ['any_drains_other']
		view:
			control: 'radio'
			label: 'Any type of drains?'
			columns:3

	any_drains_other:
		model:
			max: 128
		view:
			label: 'Drain Type Other'
			columns:3

	goal_calorie:
		model:
			max: 20000
			min: 1
			type: 'decimal'
			required: true
		view:
			label: 'Calorie Goal (kcal)'
			columns:3

	goal_protein:
		model:
			max: 20000
			min: 1
			type: 'decimal'
			required: true
		view:
			label: 'Protein Goal (gm/kg)'
			columns:3

	goal_fluid:
		model:
			max: 20000
			min: 1
			type: 'decimal'
			required: true
		view:
			label: 'Fluid Goal (mL)'
			columns:3

	nutr_meds:
		view:
			control: 'area'
			label: 'Nutrition Related Meds'
			columns:3

	recommend:
		view:
			control: 'area'
			label: 'Progress Notes/Recommendations'
			columns:3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections_group: [
		'TPN Questionnaire':
			sections: [
				'TPN Dosing Weight':
					fields: ['pediatric', 'wt_50', 'wt_pt', 'ht_pt', 'ibw', 'ibw_percentage', 'abw']
				'TPN General Assessment':
					fields: ['egg_allergy', 'latex_allergy', 'have_documents',
					'activity_factor', 'stress_factor']
				'TPN Treatment Condition':
					fields: ['any_drains', 'any_drains_other']
				'TPN Therapy Goals':
					fields: ['goal_calorie', 'goal_protein', 'goal_fluid',
					'nutr_meds', 'recommend']
			]
	]
view:
	comment: 'Patient > Careplan > Assessment > TPN'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Assessment Questionnaire: TPN'
	open: 'read'
