fields:

	pricing_update_no:
		view:
			label: 'Pricing Update No'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			source: 'inventory'
		view:
			columns: 2
			label: 'Drug'
			readonly: true

	prev_list_price:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
		view:
			note: 'Each'
			label: 'Previous List Price'
			class: 'numeral money'
			format: '$0,0.0000'
			columns: 4
			readonly: true

	updated_list_price:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
		view:
			note: 'Each'
			label: 'Updated List Price'
			class: 'numeral money'
			format: '$0,0.0000'
			columns: 4
			readonly: true

	prev_awp_price:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			note: 'Each'
			label: 'Previous AWP Price'
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			columns: 4
			readonly: true

	updated_awp_price:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
		view:
			note: 'Each'
			label: 'Updated AWP Price'
			class: 'numeral money'
			format: '$0,0.0000'
			columns: 4
			readonly: true

	prev_awp_price_pkg:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			note: 'Pkg'
			label: 'Previous AWP Price Package'
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			columns: 4
			readonly: true

	updated_awp_price_pkg:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			note: 'Pkg'
			label: 'Updated AWP Price Package'
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			columns: 4
			readonly: true

	prev_wac_price:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			label: 'Previous Whole Acquisition Cost (EA)'
			columns: 4
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			readonly: true

	updated_wac_price:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			label: 'Updated Whole Acquisition Cost (EA)'
			columns: 4
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			readonly: true

	prev_wac_price_pkg:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			label: 'Previous Whole Acquisition Cost / Package'
			columns: 4
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			readonly: true

	updated_wac_price_pkg:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			label: 'Updated Whole Acquisition Cost / Package'
			columns: 4
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			readonly: true

	prev_asp_price:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			note: 'Each'
			label: 'ASP Price'
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			columns: 4
			readonly: true

	updated_asp_price:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			note: 'Each'
			label: 'Updated ASP Price'
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			columns: 4
			readonly: true

	prev_avg_acq_cost_brand:
		model:
			type: 'decimal'
		view:
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			label: 'Previous Ntl Avg Acq Cst Brd Drg'
			columns: 4
			readonly: true

	updated_avg_acq_cost_brand:
		model:
			type: 'decimal'
		view:
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			label: 'Updated Ntl Avg Acq Cst Brd Drg'
			columns: 4
			readonly: true

	prev_avg_acq_cost_gen:
		model:
			type: 'decimal'
		view:
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			label: 'Previous Ntl Avg Acq Cst Gen Drg'
			columns: 4
			readonly: true

	updated_avg_acq_cost_gen:
		model:
			type: 'decimal'
		view:
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			label: 'Updated Ntl Avg Acq Cst Gen Drg'
			columns: 4
			readonly: true

	prev_ful:
		model:
			type: 'decimal'
		view:
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			label: 'Federal Upper Limit'
			columns: 4
			readonly: true

	updated_ful:
		model:
			type: 'decimal'
		view:
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			label: 'Updated Federal Upper Limit'
			columns: 4
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin','pharm','csr','cm', 'nurse']
		read_all:   ['admin','pharm','csr','cm', 'nurse']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['pricing_update_no'],
			['inventory_id'],
			['prev_list_price'],
			['updated_list_price'],
			['prev_awp_price'],
			['updated_awp_price'],
			['prev_awp_price_pkg'],
			['updated_awp_price_pkg'],
			['prev_wac_price'],
			['updated_wac_price'],
			['prev_wac_price_pkg'],
			['updated_wac_price_pkg'],
			['prev_asp_price'],
			['updated_asp_price']
		]

	name: ['inventory_id']
	sections:
		'Inventory Drug Pricing Update':
			hide_header: true
			indent: false
			fields: ['pricing_update_no', 'inventory_id', 'prev_list_price', 'updated_list_price', 'prev_awp_price',
			'updated_awp_price', 'prev_awp_price_pkg', 'updated_awp_price_pkg', 'prev_wac_price',
			'updated_wac_price', 'prev_wac_price_pkg', 'updated_wac_price_pkg', 'prev_asp_price',
			'updated_asp_price', 'prev_avg_acq_cost_brand', 'updated_avg_acq_cost_brand',
			'prev_avg_acq_cost_gen', 'updated_avg_acq_cost_gen', 'prev_ful', 'updated_ful']

view:
	dimensions:
		width: '75%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Inventory Drug Pricing Update'
	find:
		basic: ['inventory_id']
		advanced: []
	grid:
		fields: ['inventory_id', 'updated_list_price', 'updated_awp_price', 'updated_wac_price', 'updated_asp_price']
		sort: ['-id']
	label: 'Inventory Drug Pricing Update'
	open: 'edit'