fields:
	# Links
	patient_id:
		model:
			required: true
			type: 'int'
		view:
			label: 'Patient Id'
			offscreen: true

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	init_valid:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Initial Validator',
			offscreen: true,
			validate: [
				name: 'DrugAllergyInitInteraction'
			]

	has_da:
		model:
			type: 'text'
		view:
			columns: 3
			offscreen: true,
			label: 'Drug Allergy'

	has_dd:
		model:
			type: 'text'
		view:
			columns: 3
			offscreen: true,
			label: 'Drug Drug'

	da_interaction:
		model:
			source: 'da_interactions'
			multi: true
			required: false
			type: 'subform'
		view:
			readonly: true
			grid:
				add: 'none'
				edit: false
				fields: ['ndc', 'alrgn', 'description', 'type' ]
				label: ['Drug', 'Allergy', 'Description', 'Type']
				width: [25, 25, 35, 15]
			label: 'Drug Allergy Interactions'

	dd_interaction:
		model:
			source: 'dd_interactions'
			multi: true
			required: false
			type: 'subform'
		view:
			readonly: true
			grid:
				add: 'none'
				edit: false
				text_trim: 256 
				fields: ['ndc_1', 'ndc_2', 'description', 'sl']
				label: ['Drug 1', 'Drug 2', 'Description', 'Severity Level']
				width: [15, 15, 55, 15]
			label: 'Drug Drug Interactions'

	check_dd_interaction:
		model:
			source: ['Yes', 'No']
			if:
				'Yes':
					fields: ['dd_interaction_note', 'cont_reason']
		view:
			offscreen: true
			readonly: true
			control:'radio'

	dd_interaction_note:
		model:
			type:'text'
		view:
			control: 'area'
			readonly: true
			label: 'Interaction Notes'

	cont_reason:
		model:
			source:'interaction_reason'
		view:
			control: 'select'
			label: 'Reason to Administer'
			validate:[
				name:'InteractionsReason'
			]

model:
	access:
		create:     ['patient']
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['patient']
		review:     ['admin', 'pharm']
		update:     ['cm', 'cma', 'liaison', 'nurse', 'patient']
		update_all: ['admin', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		# unique: [
		# 		['patient_id','careplan_id']
		# ]
	sections_group: [
		'Interactions':
			fields: ['has_da', 'has_dd', 'check_dd_interaction', 'dd_interaction_note', 'cont_reason', 'init_valid', 'patient_id'],
		
		'DUR - Allergy Interaction':
			hide_header: false
			indent: false
			tab: 'DUR'
			fields: ['da_interaction'] # subform

		'DUR - Drug Interaction':
			hide_header: false
			indent: false
			tab: 'DUR'
			fields: ['dd_interaction'] # subform
	]
	name: ['patient_id','careplan_id']

view:
	dimensions:
		width: '80%'
		height: '65%'
	comment: 'Patient > Interaction'
	find:
		basic: ['patient_id', 'careplan_id']
	grid:
		fields: ['created_on', 'patient_id', 'careplan_id']
		sort: ['created_on', 'patient_id', 'careplan_id']
	icon: 'medication'
	label: 'Drug Allergy Interactions'
	open:'read'