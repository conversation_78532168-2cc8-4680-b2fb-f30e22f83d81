fields:
	patient_id:
		model:
			prefill: ['parent.patient_id']
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	transaction_code:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '103-A3'
		view:
			label: 'Transmission Code'
			offscreen: true
			readonly: true

	subform_dur:
		model:
			type: 'subform'
			multi: true
			source: 'ncpdp_dur_itm'
		view:
			note: 'Segment 08'
			label: 'DUR/PPS'
			max_count: 9
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['rsn_for_svc_code', 'prf_svc_code', 'dur_pps_loe']
				label: ['Svc Rsn', 'Svc Code', 'LOE']
				width: [25, 35, 40]
			_meta:
				copy_forward: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['created_on', 'created_by']
	sections_group: [
		'Header':
			hide_header: true
			indent: false
			fields: ['transaction_code']
		'DUR/PPS':
			indent: false
			hide_header: true
			note: 'Max 9 Entries'
			fields: ['subform_dur']
		]

view:
	dimensions:
		width: '90%'
		height: '65%'
	comment: 'DUR/PPS'
	grid:
		fields: ['created_on', 'created_by']
		sort: ['-created_on']
	label: 'DUR/PPS'
	open: 'read'