fields:
	user_id:
		model:
			multi:true
			source: 'user'
			sourcefilter:
				role:
					static: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse','physician','payer', 'pharm']
			type: 'int'
		view:
			label: 'Recipients'
	patient_id:
		model:
			source: 'patient'
			type: 'int'
		view:
			label: 'Link Patient'

model:
	access:
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
	name:['user_id', 'patient_id']
	save: false

view:
	comment: 'Setup > View > Communication'
	find:
		basic: ['user_id', 'patient_id']
	label: 'Communication'
