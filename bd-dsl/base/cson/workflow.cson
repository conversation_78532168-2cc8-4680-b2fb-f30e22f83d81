fields:

	name:
		model:
			required: false
		view:
			label: 'Name'

	code:
		model:
			required: true
		view:
			label: 'Slug'

	type:
		model:
			required: true
			source: ['Queue', 'Wizard', 'Review']
		view:
			control: 'radio'
			label: 'WorkFlow Type'

	graph_data:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			readonly: true
			label: 'Defination'

	node_order:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			readonly: true
			label: 'Defination'

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
			default: 'Yes'
		view:
			control: 'radio'
			label: 'Active'

	sort_order:
		model:
			type: 'int'
		view:
			label: 'Sort Order'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'


model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['setup']
	name: '{name}'
	sync_mode: 'mixed'
	sections:
		'Main':
			fields: ['name', 'code', 'type', 'sort_order', 'allow_sync', 'active']

view:
	comment: 'Workflow'
	find:
		basic: ['name', 'code', 'type', 'active', 'created_by']
	grid:
		fields: ['name', 'code', 'type', 'sort_order', 'allow_sync', 'active']
	label: 'Workflow'
	open: 'read'
