fields:

	code:
		model:
			required: true
		view:
			label: 'Code'
			findunique: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['dispense']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: ['code']
	sections:
		'Dispense Type':
			fields: ['code']

view:
	comment: 'Manage > Dispense Type'
	find:
		basic: ['code']
	grid:
		fields: ['code']
		sort: ['code']
	label: 'Dispense Type'
	open: 'read'
