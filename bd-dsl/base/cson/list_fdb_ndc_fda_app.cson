#TABLE: RAPPLNA0_FDA_NDC_APPL
fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'
			columns: 4

	#ndc
	ndc:
		model:
			type: 'text'
		view:
			label: 'NDC'
			findunique: true
			columns: 4

	appl_no:
		view:
			label: 'Application Number'
			findunique: true
			columns: 4

	appl_type_cd:
		model:
			type: 'int'
		view:
			label: 'FDA Drug Application Type Code'
			readonly: true
			columns: 4

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['ndc']
	indexes:
		many: [
			['ndc']
			['appl_no']
			['appl_type_cd']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['ndc', 'appl_no', 'appl_type_cd']

view:
	comment: 'Manage > List FDB NDC FDA Application Table'
	find:
		basic: ['ndc', 'appl_no', 'appl_type_cd']
	grid:
		fields: ['ndc', 'appl_no', 'appl_type_cd']
	label: 'List FDB NDC FDA Application Table'
