fields:

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site ID'
			readonly: true

	order_id:
		model:
			required: true
			source: 'careplan_order'
			type: 'int'

	order_status_id:
		model:
			required: false
			source: 'list_order_status'
			sourceid: 'code'
			default: '1'
		view:
			label: 'Status'

	intake_substatus_id:
		model:
			required: false
			source: 'list_intake_substatus'
			sourceid: 'code'
			default: 'A'
		view:
			label: 'Substatus'

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan'
			readonly: true

	physician_order_id:
		view:
			label: 'Prescriber Order Number'
			note: 'Associated Surescripts Physician Order Number'
			readonly: true

	event_id:
		model:
			required: true
			source: 'list_intake_event'
			sourceid: 'code'
		view:
			label: 'Event'
			readonly: true

model:
	access:
		create:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'payer', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'payer', 'physician']
		request:    []
		update:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['audit']
	indexes:
		many: [
			['site_id']
			['patient_id'],
			['order_id'],
			['order_status_id'],
			['intake_substatus_id'],
			['careplan_id'],
			['event_id']
		]
	name: ['patient_id', 'order_id', 'event_id']
	reportable: true

	sections:
		'Order Event Audit Trail':
			fields: [ 'site_id', 'patient_id', 'order_id', 'order_status_id', 'intake_substatus_id',
			'careplan_id', 'event_id']

view:
	find:
		basic: ['patient_id', 'event_id', 'site_id', 'careplan_id']
		advanced: ['order_status_id', 'intake_substatus_id']
	comment: 'Intake > Event Audit Trail'
	grid:
		fields: ['site_id', 'patient_id', 'order_id', 'careplan_id', 'order_status_id', 'intake_substatus_id', 'event_id']
		sort: ['-created_on']
	label: 'Intake Event Audit Trail'
	open: 'read'

