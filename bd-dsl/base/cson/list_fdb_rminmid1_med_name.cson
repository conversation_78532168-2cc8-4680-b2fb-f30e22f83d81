fields:
	code:
		model:
			type: 'text'
			max: 64
			required: true
		view:
			label: 'Code'
			readonly: true
			columns: 3

	med_name_id:
		model:
			type: 'int'
			max: 8
			required: true
		view:
			label: 'MED Medication Name ID (Stable ID)'
			readonly: true
			columns: 3

	med_name:
		model:
			type: 'text'
			max: 30
			required: true
		view:
			label: 'MED Medication Name'
			readonly: true
			columns: 3

	med_name_type_cd:
		model:
			type: 'text'
			max: 1
			required: true
		view:
			label: 'MED Medication Name Type Code'
			readonly: true
			columns: 3

	med_status_cd:
		model:
			type: 'text'
			max: 1
			required: true
		view:
			label: 'MED Medication Status Code'
			readonly: true
			columns: 3

model:
	access:
			create:     []
			create_all: ['admin']
			delete:     ['admin']
			read:       ['admin']
			read_all:   ['admin']
			request:    []
			update:     []
			update_all: ['admin']
			write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: '{med_name_id} {med_name}'
	indexes:
		many: [
			['med_name']
			['med_name_type_cd']
		]

	sections:
		'Medication Name':
			tab: 'Information'
			fields: [
				'code',
				'med_name_id',
				'med_name',
				'med_name_type_cd',
				'med_status_cd'
			]

view:
	comment: 'Manage > List FDB RMINMID1 Med Name'
	find:
		basic: ['med_name_id', 'med_name']
	grid:
		fields: [
			'med_name_id',
			'med_name',
			'med_name_type_cd',
			'med_status_cd'
		]
	label: 'List FDB RMINMID1 Med Name'