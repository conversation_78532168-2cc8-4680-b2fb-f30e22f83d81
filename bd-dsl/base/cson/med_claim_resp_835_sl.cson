fields:

	line_item_control_number:
		view:
			columns: 4
			label: 'Line Item Control Number'
			note: 'REF02 where REF01=6R'
			readonly: true
			_meta:
				path: 'lineItemControlNumber'

	service_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Service Date'
			note: 'DTM02 where DTM01=472'
			readonly: true
			_meta:
				path: 'serviceDate'

	service_start_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Service Start Date'
			note: 'DTM02 where DTM01=150'
			readonly: true
			_meta:
				path: 'serviceStartDate'

	service_end_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Service End Date'
			note: 'DTM02 where DTM01=151'
			readonly: true
			_meta:
				path: 'serviceEndDate'

	# Service Payment Information
	product_or_service_id_qualifier:
		view:
			columns: 4
			label: 'Service ID Qualifier'
			note: 'SVC01-1'
			readonly: true
			offscreen: true
			_meta:
				path: 'servicePaymentInformation.productOrServiceIDQualifier'

	product_or_service_id_qualifier_value:
		view:
			columns: 2
			label: 'Service Type'
			note: 'SVC01-1'
			readonly: true
			_meta:
				path: 'servicePaymentInformation.productOrServiceIDQualifierValue'

	adjudicated_procedure_code:
		view:
			columns: 4
			label: 'Procedure Code'
			note: 'SVC01-2'
			readonly: true
			_meta:
				path: 'servicePaymentInformation.adjudicatedProcedureCode'

	adjudicated_procedure_modifier_1:
		view:
			columns: 'modifier'
			label: 'Mod 1'
			note: 'SVC01-3'
			readonly: true
			_meta:
				path: 'servicePaymentInformation.adjudicatedProcedureModifierCodes[0]'

	adjudicated_procedure_modifier_2:
		view:
			columns: 'modifier'
			label: 'Mod 2'
			note: 'SVC01-4'
			readonly: true
			_meta:
				path: 'servicePaymentInformation.adjudicatedProcedureModifierCodes[1]'

	adjudicated_procedure_modifier_3:
		view:
			columns: 'modifier'
			label: 'Mod 3'
			note: 'SVC01-5'
			readonly: true
			_meta:
				path: 'servicePaymentInformation.adjudicatedProcedureModifierCodes[2]'

	adjudicated_procedure_modifier_4:
		view:
			columns: 'modifier'
			label: 'Mod 4'
			note: 'SVC01-6'
			readonly: true
			_meta:
				path: 'servicePaymentInformation.adjudicatedProcedureModifierCodes[3]'

	line_item_charge_amount:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Line Charge Amount'
			note: 'SVC02'
			readonly: true
			_meta:
				path: 'servicePaymentInformation.lineItemChargeAmount'

	line_item_provider_payment_amount:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Line Payment Amount'
			note: 'SVC03'
			readonly: true
			_meta:
				path: 'servicePaymentInformation.lineItemProviderPaymentAmount'

	revenue_code:
		view:
			columns: 4
			label: 'Revenue Code'
			note: 'SVC04'
			readonly: true
			_meta:
				path: 'servicePaymentInformation.nationalUniformBillingCommitteeRevenueCode'

	units_of_service_paid_count:
		model:
			max: 99999.999
			min: 0.001
			rounding: 0.001
			type: 'decimal'
		view:
			columns: 4
			label: 'Units Paid'
			class: 'numeral'
			format: '0,0.[000000]'
			note: 'SVC05'
			readonly: true
			_meta:
				path: 'servicePaymentInformation.unitsOfServicePaidCount'

	original_units_of_service_count:
		model:
			max: 99999.999
			min: 0.001
			rounding: 0.001
			type: 'decimal'
		view:
			columns: 4
			label: 'Original Units'
			class: 'numeral'
			format: '0,0.[000000]'
			note: 'SVC07'
			readonly: true
			_meta:
				path: 'servicePaymentInformation.originalUnitsOfServiceCount'

	# Submitted Service Information

	submitted_procedure_code_description:
		view:
			columns: 2
			label: 'Procedure Description'
			note: 'SVC06-7'
			readonly: true
			_meta:
				path: 'servicePaymentInformation.submittedProcedureCodeDescription'

	submitted_procedure_code:
		view:
			columns: 4
			label: 'Submitted Procedure Code'
			note: 'SVC06-2'
			readonly: true
			_meta:
				path: 'servicePaymentInformation.submittedAdjudicatedProcedureCode'

	# Service Identification
	authorization_number:
		view:
			columns: 4
			label: 'Authorization Number'
			note: 'REF02 where REF01=BB'
			readonly: true
			_meta:
				path: 'serviceIdentification.authorizationNumber'

	prior_authorization_number:
		view:
			columns: 4
			label: 'Prior Authorization Number'
			note: 'REF02 where REF01=G1'
			readonly: true
			_meta:
				path: 'serviceIdentification.priorAuthorizationNumber'

	location_number:
		view:
			columns: 4
			label: 'Location Number'
			note: 'REF02 where REF01=LU'
			readonly: true
			_meta:
				path: 'serviceIdentification.locationNumber'

	pre_determination_number:
		view:
			columns: 4
			label: 'Pre-Determination Of Benefits Number'
			note: 'REF02 where REF01=G3'
			readonly: true
			_meta:
				path: 'serviceIdentification.preDeterminationOfBenefitsNumber'

	# Rendering Provider Information
	rendering_provider_npi:
		view:
			columns: 4
			label: 'Rendering Provider NPI'
			note: 'REF02 where REF01=HPI'
			readonly: true
			_meta:
				path: 'renderingProviderInformation.npi'

	rendering_provider_ncpdp:
		view:
			columns: 4
			label: 'NCPDP Number'
			note: 'REF02 where REF01=D3'
			readonly: true
			_meta:
				path: 'renderingProviderInformation.nationalCouncilForPrescriptionDrugProgramPharmacyNumber'

	# Service Supplemental Amounts
	allowed_actual:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Allowed Amount'
			note: 'AMT02 where AMT01=B6'
			readonly: true
			_meta:
				path: 'serviceSupplementalAmounts.allowedActual'

	deduction_amount:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Deduction Amount'
			note: 'AMT02 where AMT01=KH'
			readonly: true
			_meta:
				path: 'serviceSupplementalAmounts.deductionAmount'

	# Health Care Remark Codes
	rmk_cd:
		model:
			required: false
			multi: true
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'RARC'
		view:
			columns: 4
			label: 'Remark Codes'
			note: 'LOOP 2110; LQ'
			readonly: true
			_meta:
				path: 'healthCareCheckRemarkCodes[*]'

	# Service Line Adjustments
	service_adjustments:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_835_sl_adj'
		view:
			label: 'Service Line Adjustments'
			readonly: true
			grid:
				fields: ['adjustment_reason_code', 'adjustment_amount', 'adjustment_quantity']
				label: ['Reason', 'Amount', 'Quantity']
				width: [60, 20, 20]
			_meta:
				path: 'serviceAdjustments'

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: 'Service Lines'
	indexes:
		many: [
			['line_item_control_number']
			['service_date']
			['adjudicated_procedure_code']
			['submitted_procedure_code']
		]
	sections_group: [
		'Service Line Details':
			hide_header: true
			sections: [
				'Service Information':
					hide_header: true
					fields: ['line_item_control_number', 'service_date', 'service_start_date', 'service_end_date',
					'product_or_service_id_qualifier', 'product_or_service_id_qualifier_value',
					'adjudicated_procedure_code', 'adjudicated_procedure_modifier_1', 'adjudicated_procedure_modifier_2',
					'adjudicated_procedure_modifier_3', 'adjudicated_procedure_modifier_4']
				'Payment Information':
					fields: ['line_item_charge_amount', 'line_item_provider_payment_amount', 'revenue_code',
					'units_of_service_paid_count', 'original_units_of_service_count',
					'allowed_actual', 'deduction_amount']
				'Submitted Information':
					fields: ['submitted_procedure_code_description', 'submitted_procedure_code']
				'Authorization':
					fields: ['authorization_number', 'prior_authorization_number', 'location_number', 'pre_determination_number']
				'Rendering Provider':
					fields: ['rendering_provider_npi', 'rendering_provider_ncpdp']
				'Remark Codes':
					fields: ['rmk_cd']
				'Service Line Adjustments':
					indent: false
					fields: ['service_adjustments']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '85%'
	hide_cardmenu: true
	comment: 'Service Lines'
	grid:
		fields: ['line_item_control_number', 'submitted_procedure_code_description', 'line_item_charge_amount', 'line_item_provider_payment_amount', 'service_date']
		label: ['Line #', 'Description', 'Charge', 'Paid', 'Service Date']
		width: [10, 30, 20, 20, 20]
		sort: ['line_item_control_number']
	label: 'Service Lines'
	open: 'read'