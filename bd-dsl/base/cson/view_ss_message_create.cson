fields:

	ss_message_id:
		model:
			type: 'int'
			required: true
			source: 'ss_message'
		view:
			label: 'SS Message Id'
			readonly: true
			offscreen: true

	fltr:
		model:
			multi: true
			required: true
			source: 'list_ss_prefill'
			sourceid: 'code'
		view:
			label: 'Prefill Form'
			offscreen: true
			readonly: true

	slct_frm:
		model:
			multi: true
			required: true
			source: 'list_ss_prefill'
			sourceid: 'code'
			sourcefilter:
				code:
					'dynamic': '{fltr}'
		view:
			class: 'checkbox checkbox-4'
			control: 'checkbox'
			label: 'Select Form(s)'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: 'Surescripts Form Generator'
	sections:
		'Select Form(s)':
			hide_header: true
			indent: false
			fields: ['ss_message_id', 'fltr', 'slct_frm']

view:
	dimensions:
		width: '55%'
		height: '55%'
	hide_header: true
	comment: 'Surescripts Form Generator'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Surescripts Form Generator'
	open: 'edit'