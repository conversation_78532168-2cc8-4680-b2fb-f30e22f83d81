fields:

	patient:
		view:
			label: 'Patient'
			readonly: true
			columns: 2

	insurance:
		view:
			label: 'Insurance'
			readonly: true
			columns: 2

	subform_item:
		model:
			multi: true
			type: 'subform'
			source: 'view_rx_pricing_item'
		view:
			grid:
				add: 'none'
				hide_cardmenu: true
				edit: false
				allow_read_wo_id: true
				fields: ['drug', 'bill_quantity', 'expected_copay', 'billed', 'expected']
				label: ['Drug', 'Quantity', 'Copay $', 'Billed $', 'Expected $']
				width: [40, 15, 15, 15, 15]
			label: 'Pricing Breakdown'
			readonly: true

	expected_copay:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: -3
			label: 'Expected Copay'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_billed:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			label: 'Total Bill'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_expected:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			label: 'Total Expected'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	save: false
	name: 'Pricing Estimates'
	sections_group: [
		'Pricing Estimates':
			hide_header: true
			indent: false
			fields: ['insurance', 'patient']
		'Items':
			hide_header: true
			indent: false
			fields: ['subform_item']
		'Totals':
			hide_header: true
			indent: false
			fields: ['expected_copay', 'total_billed', 'total_expected']
		]

view:
	dimensions:
		width: '75%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Pricing Estimates'
	grid:
		fields: ['expected_copay', 'total_billed', 'total_expected']
		sort: ['-created_on']
	label: 'Pricing Estimates'
	open: 'read'