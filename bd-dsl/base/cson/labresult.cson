fields:
	name:
		model:
			max: 256
			required: true
		view:
			label: 'Lab Name'

	short_name:
		model:
			max: 256
			required: true
		view:
			label: 'Short Name'

	type:
		model:
			source: ['Components', 'Boolean']
			default: 'Components'
			if:
				'Components':
					fields:['components']
			required: true
		view:
			label: 'Type'

	components:
		model:
			source:'labcomponent'
			multi: true
			required: true
		view:
			control: 'select'
			label: 'Components'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['manage']
	indexes:
		unique: [
			['name']
		]
	name: '{name} ({short_name})'
	sections:
		'Main':
			fields: ['name', 'short_name', 'type', 'components']

view:
	hide_cardmenu: true
	comment: 'Manage > Lab Result'
	find:
		basic: ['name', 'short_name']
	grid:
		fields: ['name', 'short_name', 'type', 'components']
		sort: ['name']
	label: 'Lab Result'
	open: 'read'
