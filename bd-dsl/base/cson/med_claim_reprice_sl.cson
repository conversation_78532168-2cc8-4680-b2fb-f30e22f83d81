fields:

	pricing_methodology_code:
		model:
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'HCP01'
		view:
			columns: 2
			label: 'Pricing Methodology Code'
			reference: 'HCP01'
			_meta:
				location: '2300 HCP'
				field: '01'
				path: 'claimInformation.serviceLines[{idx1-50}].linePricingRepricingInformation.pricingMethodologyCode'

	repriced_allowed_amount:
		model:
			required: true
			max: 9999999999.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Reprice Allowed Amt'
			reference: 'HCP02'
			_meta:
				location: '2300 HCP'
				field: '02'
				path: 'claimInformation.serviceLines[{idx1-50}].linePricingRepricingInformation.repricedAllowedAmount'

	repriced_saving_amount:
		model:
			max: 9999999999.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Reprice Saving Amt'
			reference: 'HCP03'
			_meta:
				location: '2300 HCP'
				field: '03'
				path: 'claimInformation.serviceLines[{idx1-50}].linePricingRepricingInformation.repricedSavingAmount'

	repricing_organization_identifier:
		model:
			max: 50
			min: 1
		view:
			columns: 4
			label: 'Reprice Org ID'
			reference: 'HCP04'
			_meta:
				location: '2300 HCP'
				field: '04'
				path: 'claimInformation.serviceLines[{idx1-50}].linePricingRepricingInformation.repricingOrganizationIdentifier'

	repricing_per_diem_or_flat_rate_amount:
		model:
			max: 9999999999.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Per Diem/Flat Rate Amt'
			reference: 'HCP05'
			_meta:
				location: '2300 HCP'
				field: '05'
				path: 'claimInformation.serviceLines[{idx1-50}].linePricingRepricingInformation.repricingPerDiemOrFlatRateAmount'

	reject_reason_code:
		model:
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'HCP13'
		view:
			columns: 2
			label: 'Reject Reason Code'
			reference: 'HCP13'
			_meta:
				location: '2300 HCP'
				field: '13'
				path: 'claimInformation.serviceLines[{idx1-50}].linePricingRepricingInformation.rejectReasonCode'

	policy_compliance_code:
		model:
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'HCP14'
		view:
			columns: 2
			label: 'Policy Compliance Code'
			reference: 'HCP14'
			_meta:
				location: '2300 HCP'
				field: '14'
				path: 'claimInformation.serviceLines[{idx1-50}].linePricingRepricingInformation.policyComplianceCode'

	exception_code:
		model:
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'HCP15'
		view:
			columns: 2
			label: 'Exception Code'
			reference: 'HCP15'
			_meta:
				location: '2300 HCP'
				field: '15'
				path: 'claimInformation.serviceLines[{idx1-50}].linePricingRepricingInformation.exceptionCode'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['pricing_methodology_code']
	sections:
		'Repricing Service Line':
			hide_header: true
			fields: ['pricing_methodology_code', 'repriced_allowed_amount',
			'repriced_saving_amount', 'repricing_organization_identifier', 'repricing_per_diem_or_flat_rate_amount',
			'reject_reason_code', 'policy_compliance_code', 'exception_code']

view:
	dimensions:
		width: '65%'
		height: '55%'
	hide_cardmenu: true
	reference: '2400'
	comment: 'Repricing Service Line'
	grid:
		fields: ['pricing_methodology_code', 'repriced_allowed_amount', 'reject_reason_code', 'exception_code']
		width: [25, 25, 25, 25]
		sort: ['-created_on']
	label: 'Repricing Service Line'
	open: 'read'