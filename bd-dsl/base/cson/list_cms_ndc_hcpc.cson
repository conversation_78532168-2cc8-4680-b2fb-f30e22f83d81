fields:

	# First column, CODE field
	hcpc:
		view:
			columns: 3
			label: 'HCPC'
			findunique: true
			readonly: true

	# Co-insurance Percentage
	co_insurance_per:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			columns: 3
			label: 'Co-insurance Percentage'
			readonly: true

	# Payment Limit
	payment_limit:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			columns: 3
			label: 'Payment Limit'
			readonly: true

	short_desc:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Short Description'

	hcpcs_dosage:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'HCPCS Dosage'
	#Vaccine AWP%
	vaccine_awp_per:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			columns: 3
			label: 'Vaccine AWP%'
			readonly: true
	#Vaccine Limit
	vaccine_limit:
		model:
			type: 'int'
		view:
			columns: 3
			label: 'Vaccine Limit'
			readonly: true

	#Blood AWP%
	blood_awp_per:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			columns: 3
			label: 'Blood AWP%'
			readonly: true
	#Blood limit
	blood_limit:
		model:
			type: 'int'
		view:
			columns: 3
			label: 'Blood Limit'
			readonly: true
	#Clotting Factor
	clotting_factor:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Clotting Factor'
	#Notes
	notes:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Notes'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['hcpc', 'co_insurance_per', 'hcpcs_dosage']
	indexes:
		many: [
			['hcpc']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['hcpc', 'co_insurance_per', 'payment_limit',
			'short_desc', 'hcpcs_dosage', 'vaccine_awp_per',
			'vaccine_limit', 'blood_awp_per', 'blood_limit',
			'clotting_factor', 'notes']

view:
	comment: 'Manage > List CMS HCPC'
	find:
		basic: ['hcpc']
	grid:
		fields: ['hcpc', 'short_desc', 'co_insurance_per']
	label: 'List CMS HCPC'
