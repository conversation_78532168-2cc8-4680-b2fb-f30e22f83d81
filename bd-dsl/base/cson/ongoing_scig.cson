fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# SubQ IG Followup
	had_infection:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Have you had any infections or received antibiotic treatment since your last SubQ Ig administration?'

	seen_since_last_subq:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Have you seen your physician since your last SubQ Ig treatment?'

	patient_has_fever:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Is patient reporting fever of 100.5 F (38 C) or higher?'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		ongoing_subqig:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'SubQ IG Followup':
			note: 'Ask the patient the following questions'
			fields: ['had_infection', 'patient_has_fever', 'seen_since_last_subq']

view:
	comment: 'Patient > Careplan > Ongoing > SubQ IG'
	grid:
		fields: ['had_infection', 'seen_since_last_subq']
	label: 'Ongoing Assessment: SubQ  IG'
	open: 'read'
