fields:

	oth_amt_qualifier:
		model:
			multi: false
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '564-J3'
		view:
			columns: 3
			note: '564-J3'
			label: 'Other Amount Qualifier'
			readonly: true

	oth_amt_paid:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '565-J4'
			label: 'Other Amount Paid'
			class: 'numeral money'
			readonly: true
			format: '$0,0.00'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['oth_amt_qualifier', 'oth_amt_paid']
	sections:
		'Other Amount Paid':
			hide_header: true
			indent: false
			fields: ['oth_amt_qualifier', 'oth_amt_paid']

view:
	dimensions:
		width: '50%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Other Amount Paid'
	grid:
		fields: ['oth_amt_qualifier', 'oth_amt_paid']
		sort: ['-created_on']
	label: 'Other Amount Paid'
	open: 'read'