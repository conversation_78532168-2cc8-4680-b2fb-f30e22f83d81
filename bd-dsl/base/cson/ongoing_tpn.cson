fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Demographics
	gender:
		model:
			prefill: ['patient']
			if:
				'Female':
					fields: ['is_pregnant']
		view:
			offscreen: true
			readonly: true
			label: 'Sex'

	pmh:
		model:
			prefill: ['ongoing_tpn']
		view:
			control: 'area'
			label: 'Past Medical History'

	weight_goal:
		model:
			max: 1000
			min: 1
			required: true
			rounding: 0.01
			type: 'decimal'
			prefill: ['careplan']
		view:
			class: 'unit'
			label: "What is the patient's goal or usual healthy weight (Kg)?"
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]

	pediatric:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			prefill: ['ongoing_iv_tpn']
			if:
				'Yes':
					fields: ['wt_50', 'wt_pt', 'ht_pt']
				'No':
					fields: ['ibw', 'ibw_percentage', 'abw']
		view:
			control: 'radio'
			label: "Pediatric patient?"

	wt_50:
		model:
			max: 1000
			min: 1
			rounding: 0.01
			type: 'decimal'
			prefill: ['ongoing_iv_tpn']
		view:
			class: 'numeral'
			format: 'percent'
			label: 'Weight at 50%ile'
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]

	wt_pt:
		model:
			max: 100
			min: 1
			rounding: 0.01
			type: 'decimal'
			prefill: ['ongoing_iv_tpn']
		view:
			label: 'Weight %ile'
			class: 'numeral'
			format: 'percent'

	ht_pt:
		model:
			max: 100
			min: 1
			rounding: 0.01
			type: 'decimal'
			prefill: ['ongoing_iv_tpn']
		view:
			label: 'Height %ile'
			class: 'numeral'
			format: 'percent'
	ibw:
		model:
			max: 1000
			min: 1
			rounding: 0.01
			type: 'decimal'
			prefill: ['ongoing_iv_tpn']
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'IBW'
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]

	ibw_percentage:
		model:
			max: 100
			min: 1
			rounding: 0.01
			type: 'decimal'
			prefill: ['ongoing_iv_tpn']
		view:
			label: '%IBW'
			class: 'numeral'
			format: 'percent'

	abw:
		model:
			max: 1000
			min: 1
			rounding: 0.01
			type: 'decimal'
			prefill: ['ongoing_iv_tpn']
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Adjusted BW'
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]

	is_pregnant:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			prefill: ['ongoing_iv_tpn']
			if:
				'Yes':
					fields: ['prepreg_weight']
		view:
			control: 'radio'
			label: "Currently pregnant?"

	prepreg_weight:
		model:
			max: 1000
			min: 1
			required: true
			rounding: 0.01
			type: 'decimal'
			prefill: ['ongoing_iv_tpn']
		view:
			class: 'unit'
			label: 'Pre-Pregnancy Weight'
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]

	type:
		model:
			source: ['Initial Follow Up', 'Ongoing Follow Up', 'Home Start Evaluation', 'Nutrition Screen']
		view:
			control: 'radio'
			label: 'Assessment Type'

	goal_calorie:
		model:
			max: 20000
			min: 1
			type: 'decimal'
			prefill: ['ongoing_iv_tpn', 'assessment_tpn']
			required: true
		view:
			label: 'Calorie Goal (kcal)'

	goal_protein:
		model:
			max: 20000
			min: 1
			type: 'decimal'
			required: true
		view:
			label: 'Protein Goal (gm/kg)'

	goal_fluid:
		model:
			max: 20000
			min: 1
			type: 'decimal'
			required: true
		view:
			label: 'Fluid Goal (mL)'

	needs_met:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Meeting estimated needs?'

	tolerating:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Tolerating current TPN formulation?'

	nutr_meds:
		view:
			control: 'area'
			label: 'Nutrition Related Meds'

	recommend:
		view:
			control: 'area'
			label: 'Progress Notes/Recommendations'

	any_drains:
		model:
			max: 3
			min: 1
			source: ['PleureX', 'Jackson-Pratt(JP)', 'Chest/abdominal tube', 'Other']
			if:
				'Other':
					fields: ['any_drains_other']
		view:
			control: 'radio'
			label: 'Any type of drains?'

	any_drains_other:
		model:
			max: 128
		view:
			label: 'Drain Type Other'

	needs_change_lab:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['needs_change_lab_details']
		view:
			control: 'radio'
			label: 'Are changes to electrolytes, trace elements, vitamin, amino acid, fat, and glucose required based on recent labs and patient assessment?'

	needs_change_lab_details:
		view:
			control: 'area'
			label: 'Change details'

	pt_met_goals:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient meeting nutritional goals, oral Careplan, fluid balance (input/output) and weight goal set by the ordering physician?'

	pt_compliant:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient tolerating TPN therapy and compliant with prescribed regimen?'

	pt_rpt_bwl_move:
		model:
			max: 10
			min: 1
			type: 'decimal'
		view:
			note: '(x per week)'
			label: 'Bowel movement Frequency'

	pt_rpt_urine:
		model:
			max: 32
			min: 1
			source: ['Clear','Cloudy','Strong Odor']
		view:
			control: 'radio'
			label: 'Urine Color'

	pt_rpt_urine_freq_day:
		model:
			max: 100
			min: 1
			type: 'decimal'
		view:
			note: '(x times per day)'
			label: 'Urination Frequency (daytime)'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		ongoing_iv_tpn:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
		assessment_tpn:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'TPN H&P':
			prefill: 'ongoing_iv_tpn'
			fields: ['gender', 'pmh', 'weight_goal',
			'pediatric', 'wt_50', 'wt_pt', 'ht_pt', 'ibw', 'ibw_percentage',
			'abw', 'is_pregnant', 'prepreg_weight']
		'TPN Nutritional Report':
			prefill: 'ongoing_iv_tpn'
			fields: ['type', 'goal_calorie', 'goal_protein', 'goal_fluid',
			'needs_met', 'tolerating', 'nutr_meds', 'recommend']
		'TPN Treatment Condition':
			fields: ['any_drains', 'any_drains_other']
		'TPN Assessment':
			fields: ['needs_change_lab', 'needs_change_lab_details', 'pt_met_goals',
			'pt_compliant','pt_rpt_bwl_move', 'pt_rpt_urine',
			'pt_rpt_urine_freq_day']

view:
	comment: 'Patient > Careplan > Ongoing > TPN'
	grid:
		fields: ['goal_calorie', 'goal_protein', 'goal_fluid', 'needs_met']
	label: 'Ongoing Assessment: TPN'
	open: 'read'
