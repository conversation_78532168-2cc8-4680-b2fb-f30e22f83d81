fields:
	display:
		model:
			type: 'text'
	slug:
		model:
			type: 'text'
	type:
		model:
			source: ['user', 'role', 'group']
	member:
		model:
			multi: true
			type: 'int'
			source: "user"

model:
	access:
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['pharm']
	indexes:
		many: [
			['auto_name']
		]
	name: '{display}'
	reportable: false

view:
	hide_cardmenu: true
	comment: 'Core > Permissions Group'
	label: 'Permissions Group'
