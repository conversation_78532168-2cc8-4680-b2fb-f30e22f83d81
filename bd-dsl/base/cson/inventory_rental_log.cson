fields:

	ticket_no:
		model:
			required: false
		view:
			columns: 4
			label: 'Ticket No'
			readonly: true
			offscreen: true

	ticket_item_no:
		model:
			required: false
		view:
			columns: 4
			label: 'Ticket Item No'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
		view:
			columns: 4
			label: 'Rental Item'
			readonly: true

	serial_no:
		model:
			required: true
		view:
			columns: 4
			label: 'Serial No'
			readonly: true

	active:
		model:
			source: ['Yes']
			default: 'Yes'
			if:
				'!':
					fields: ['inactive_datetime']
					readonly:
						fields: ['in_stock']
					prefill:
						in_stock: 'No'
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Active'
			columns: 4
			findfilter: 'Yes'

	inactive_datetime:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Inactive Date/Time'
			readonly: true

	in_stock:
		model: 
			required: true
			source: ['No', 'Yes', 'Purchased']
			default: 'Yes'
			if:
				'Purchased':
					fields: ['patient_id']
				'No':
					fields: ['patient_id']
		view:
			control: 'radio'
			label: 'In-Stock?'
			readonly: true
			columns: 4

	status:
		model:
			source: 'list_rental_status'
			sourceid: 'code'
		view:
			class: 'status'
			columns: 2
			label: 'Status'

	site_id:
		model:
			type: 'int'
			source: 'site'
			required: true
		view:
			columns: 4
			label: 'Site Location'
			readonly: true

	in_service_datetime:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'In-Service Date/Time'

	last_checked_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Last Check Date'

	next_check_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Next Check Date'

	last_pm_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Last PM Date'
			readonly: true

	next_pm_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Next PM Date'

	patient_id:
		model:
			type: 'int'
			source: 'patient'
		view:
			columns: 2
			label: 'Checked Out By'

	checked_out_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Checked Out Date'

	note:
		model:
			type: 'text'
		view:
			control: 'area'
			label: 'Notes'

	rental_events:
		model:
			multi: true
			type: 'subform'
			source: 'inventory_rental_event'
		view:
			label: 'Events'

model:
	access:
		create:     []
		create_all: []
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['inventory']
	indexes:
		many: [
			['inventory_id'],
			['serial_no'],
			['site_id'],
			['status'],
			['active'],
			['in_stock'],
			['patient_id']
		]
	name: ['inventory_id', 'serial_no', 'site_id', 'status']
	sections_group: [
			'Rental Item':
				fields: ['inventory_id', 'serial_no', 'active', 'inactive_datetime', 'in_stock',
				'status', 'site_id', 'in_service_datetime', 'last_checked_date',
				'next_check_date', 'last_pm_date', 'next_pm_date',
				'patient_id', 'checked_out_date', 'note']
			'Events':
				fields: ['rental_events']
	]

view:
	hide_cardmenu: true
	comment: 'Rental Device Log'
	find:
		basic: ['inventory_id', 'status', 'site_id', 'patient_id']
	grid:
		fields: ['inventory_id', 'status', 'site_id', 'patient_id', 'next_check_date', 'next_pm_date']
		sort: ['-id']
	label: 'Rental Device Log'
	open: 'read'
