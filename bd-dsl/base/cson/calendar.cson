fields:
	name:
		model:
			max: 128
			required: true
		view:
			label: 'Calendar'
	color:
		model:
			max: 16
			required: true
		view:
			label: 'Color (HEX)'

	default_event_duration:
		model:
			type: 'int'
		view:
			label: 'Default Event Duration (minutes)'

	default_event_span:
		model:
			type: 'int'
		view:
			label: 'Default Span for Recurring Events (months)'
model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['setup']
	indexes:
		unique: [
			['name']
		]
	name: ['name']
	sections:
		'Main':
			fields: ['name', 'color','default_event_duration','default_event_span']

view:
	comment: 'Manage > Calendar'
	find:
		basic: ['name']
	grid:
		fields: ['name', 'color']
		sort: ['name']
	label: 'Calendar'
