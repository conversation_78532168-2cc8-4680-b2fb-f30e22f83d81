fields:

	inventory_id:
		model:
			required: true
			source: 'inventory'
			sourcefilter:
				type:
					'static': ['Supply']
		view:
			columns: 2
			label: 'Inventory Item'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&filter=id:'
				fields:
					'quantity_per_container': ['quantity_per_container']
					'container_unit': ['container_unit']
			]

	quantity_per_container:
		model:
			default: 1
			required: false
			type: 'decimal'
			max: 9999999.999
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Container Units'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'inventory_id'
					source_fields: ['quantity_per_container', 'container_unit']
					separator: ' per '
				}
			]

	container_unit:
		model:
			default: 'container'
		view:
			label: 'Container Unit'
			readonly: true
			offscreen: true

	dispense_unit:
		model:
			default: 'each'
			required: true
			source: ['each', 'container']
			if:
				'container':
					fields: ['dispense_containers']
					readonly:
						fields: ['dispense_quantity']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Dispense Unit'

	dispense_containers:
		model:
			min: 1
			default: 1
			type: 'int'
			required: true
		view:
			columns: 4
			label: 'Dispense Quantity'
			note: 'containers'
			transform: [
				{
					name: 'MultiplyFields'
					fields: ['quantity_per_container', 'dispense_containers']
					destination: 'dispense_quantity'
				}
			]

	dispense_quantity:
		model:
			min: 1
			default: 1
			type: 'int'
			required: true
		view:
			columns: 4
			label: 'Dispense Quantity'
			note: 'each'

	external_id:
		view:
			label: 'External ID'
			readonly: true
			offscreen: true

	part_of_kit:
		model:
			default: 'Yes'
			source: ['Yes']
			if:
				'!':
					fields: ['bill']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Billed as Kit?'

	bill:
		model:
			required: false
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Billable?'

	one_time_only:
		model:
			max: 4
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: '1x Only?'
			columns: 4

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['inventory_id']
		]
	name: '{inventory_id} {dispense_quantity}'
	sections:
		'Supply Kit Item':
			hide_header: true
			indent: false
			fields: ['inventory_id', 'quantity_per_container',  'container_unit',
			'dispense_unit', 'dispense_containers', 'dispense_quantity',
			'part_of_kit', 'bill', 'one_time_only']

view:
	dimensions:
		width: '60%'
		height: '45%'
	hide_cardmenu: true
	comment: 'Supply Kit Item'
	grid:
		fields: ['inventory_id', 'dispense_quantity', 'part_of_kit', 'bill',  'one_time_only']
		width: [25, 15, 15, 15, 15]
		sort: ['-id']
	label: 'Supply Kit Item'
	open: 'edit'