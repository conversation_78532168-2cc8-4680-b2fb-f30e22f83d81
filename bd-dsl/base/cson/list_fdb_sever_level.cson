fields: #RADIMSL1_SEVER_LEVEL
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#DDI_SL
	ddi_sl:
		view:
			label: 'Severity Level'
			readonly: true
			columns: 2

	#DDI_SLSN
	ddi_slsn:
		model:
			type: 'int'
		view:
			label: 'Severity Level Text Sequence Number'
			readonly: true
			columns: 2

	#DDI_SLTXT
	ddi_sltxt:
		view:
			label: 'Severity Level Text'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['ddi_sl', 'ddi_slsn', 'ddi_sltxt']
	indexes:
		many: [
			['ddi_sl']
		]
	sections:
		'Drug-Drug Interactions':
			fields: ['ddi_sl', 'ddi_slsn', 'ddi_sltxt']

view:
	comment: 'Manage > List FDB Drug-Drug Interaction Severity Levels Table'
	find:
		basic: ['ddi_sl']
	grid:
		fields: ['ddi_sl', 'ddi_slsn', 'ddi_sltxt']
	label: 'List FDB Drug-Drug Interaction Severity Levels Table'
