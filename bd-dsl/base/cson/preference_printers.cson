fields:
	document_printer:
		model:
			type: 'text'
			max: 64
		view:
			label: 'Document Printer'

	cpr_report_printer:
		model:
			type: 'text'
			max: 64
		view:
			label: 'CPR+ Report Printer'

	cpr_form_printer:
		model:
			type: 'text'
			max: 64
		view:
			label: 'CPR+ Form Printer'

	pharmacy_po_label_printer:
		model:
			type: 'text'
			max: 64
		view:
			label: 'Pharmacy PO Label Printer'

	pharmacy_work_order_printer:
		model:
			type: 'text'
			max: 64
		view:
			label: 'Pharmacy Work Order Printer'

	delivery_ticket_printer:
		model:
			type: 'text'
			max: 64
		view:
			label: 'Delivery Ticket Printer'

	labelsship_printer:
		model:
			type: 'text'
			max: 64
		view:
			label: 'Labelsship Printer'

	pharmacy_label_printer:
		model:
			type: 'text'
			max: 64
		view:
			label: 'Pharmacy Label Printer'

	cms_1500_form_printer:
		model:
			type: 'text'
			max: 64
		view:
			label: 'CMS 1500 Form Printer'

	physician_cover_letter_printer:
		model:
			type: 'text'
			max: 64
		view:
			label: 'Physician Cover Letter Printer'

	syringe_label_printer:
		model:
			type: 'text'
			max: 64
		view:
			label: 'Syringe Label Printer'

	pharmacy_tpn_label_printer:
		model:
			type: 'text'
			max: 64
		view:
			label: 'Pharmacy TPN Label Printer'

	picklist_printer:
		model:
			type: 'text'
			max: 64
		view:
			label: 'Pick List Printer'

	verbal_order_printer:
		model:
			type: 'text'
			max: 64
		view:
			label: 'Verbal Order Printer'

	site_id:
		model:
			source: 'site'
		view:
			label: 'Site ID'

model:
	access:
		create:     []
		create_all: ['admin', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'nurse', 'pharm']
		write:      ['admin', 'nurse', 'pharm']
	bundle: ['setup']
	name: ['site_id']
	sections:
		'Printers':
			fields: ['site_id', 'document_printer','cpr_report_printer','cpr_form_printer','pharmacy_po_label_printer','pharmacy_work_order_printer','delivery_ticket_printer','labelsship_printer','pharmacy_label_printer','cms_1500_form_printer','physician_cover_letter_printer','syringe_label_printer','pharmacy_tpn_label_printer', 'picklist_printer',
			'verbal_order_printer'
			]
view:
	comment: 'Settings > Printers'
	find:
		basic: ['site_id']
	grid:
		fields: ['site_id']
	label: 'Printers'
	open: 'read'