#TABLE: RDAMGHC0_HIC_ALRGN_GRP_LINK
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#HIC_SEQN
	hic_seqn:
		model:
			type: 'int'
		view:
			label: 'Hierarchical Ingredient Code Sequence Number'
			readonly: true
			columns: 2

	#DAM_ALRGN_GRP
	dam_alrgn_grp:
		model:
			type: 'int'
		view:
			label: 'DAM Specific Allergen Group Code (Stable ID)'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{hic_seqn} {dam_alrgn_grp}"
	indexes:
		many: [
			['hic_seqn']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['hic_seqn', 'dam_alrgn_grp']

view:
	comment: 'Manage > List FDB Allergen Group Link Table'
	find:
		basic: ['hic_seqn', 'dam_alrgn_grp']
	grid:
		fields: ['hic_seqn', 'dam_alrgn_grp']
	label: 'List FDB Allergen Group Link Table'
