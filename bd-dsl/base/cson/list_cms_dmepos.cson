fields:
	hcpcs:
		view:
			label: 'HCPCS'
			findunique: true
			readonly: true

	mod:
		model:
			type: 'text'
		view:
			label: 'Mod'

	mod2:
		model:
			type: 'text'
		view:
			label: 'Mod2'

	juris:
		model:
			type: 'text'
		view:
			label: 'JURIS'

	catg:
		model:
			type: 'text'
		view:
			label: 'CATG'

	ceiling:
		model:
			type: 'decimal'
		view:
			label: 'Ceiling'

	floor:
		model:
			type: 'decimal'
		view:
			label: 'Floor'

	al_nr:
		model:
			type: 'decimal'
		view:
			label: 'AL (NR)'

	al_r:
		model:
			type: 'decimal'
		view:
			label: 'AL (R)'

	ar_nr:
		model:
			type: 'decimal'
		view:
			label: 'AR (NR)'

	ar_r:
		model:
			type: 'decimal'
		view:
			label: 'AR (R)'

	az_nr:
		model:
			type: 'decimal'
		view:
			label: 'AZ (NR)'

	az_r:
		model:
			type: 'decimal'
		view:
			label: 'AZ (R)'

	ca_nr:
		model:
			type: 'decimal'
		view:
			label: 'CA (NR)'

	ca_r:
		model:
			type: 'decimal'
		view:
			label: 'CA (R)'

	co_nr:
		model:
			type: 'decimal'
		view:
			label: 'CO (NR)'

	co_r:
		model:
			type: 'decimal'
		view:
			label: 'CO (R)'

	ct_nr:
		model:
			type: 'decimal'
		view:
			label: 'CT (NR)'

	ct_r:
		model:
			type: 'decimal'
		view:
			label: 'CT (R)'

	dc_nr:
		model:
			type: 'decimal'
		view:
			label: 'DC (NR)'

	dc_r:
		model:
			type: 'decimal'
		view:
			label: 'DC (R)'

	de_nr:
		model:
			type: 'decimal'
		view:
			label: 'DE (NR)'

	de_r:
		model:
			type: 'decimal'
		view:
			label: 'DE (R)'

	fl_nr:
		model:
			type: 'decimal'
		view:
			label: 'FL (NR)'

	fl_r:
		model:
			type: 'decimal'
		view:
			label: 'FL (R)'

	ga_nr:
		model:
			type: 'decimal'
		view:
			label: 'GA (NR)'

	ga_r:
		model:
			type: 'decimal'
		view:
			label: 'GA (R)'

	ia_nr:
		model:
			type: 'decimal'
		view:
			label: 'IA (NR)'

	ia_r:
		model:
			type: 'decimal'
		view:
			label: 'IA (R)'

	id_nr:
		model:
			type: 'decimal'
		view:
			label: 'ID (NR)'

	id_r:
		model:
			type: 'decimal'
		view:
			label: 'ID (R)'

	il_nr:
		model:
			type: 'decimal'
		view:
			label: 'IL (NR)'

	il_r:
		model:
			type: 'decimal'
		view:
			label: 'IL (R)'

	in_nr:
		model:
			type: 'decimal'
		view:
			label: 'IN (NR)'

	in_r:
		model:
			type: 'decimal'
		view:
			label: 'IN (R)'

	ks_nr:
		model:
			type: 'decimal'
		view:
			label: 'KS (NR)'

	ks_r:
		model:
			type: 'decimal'
		view:
			label: 'KS (R)'

	ky_nr:
		model:
			type: 'decimal'
		view:
			label: 'KY (NR)'

	ky_r:
		model:
			type: 'decimal'
		view:
			label: 'KY (R)'

	la_nr:
		model:
			type: 'decimal'
		view:
			label: 'LA (NR)'

	la_r:
		model:
			type: 'decimal'
		view:
			label: 'LA (R)'

	ma_nr:
		model:
			type: 'decimal'
		view:
			label: 'MA (NR)'

	ma_r:
		model:
			type: 'decimal'
		view:
			label: 'MA (R)'

	md_nr:
		model:
			type: 'decimal'
		view:
			label: 'MD (NR)'

	md_r:
		model:
			type: 'decimal'
		view:
			label: 'MD (R)'

	me_nr:
		model:
			type: 'decimal'
		view:
			label: 'ME (NR)'

	me_r:
		model:
			type: 'decimal'
		view:
			label: 'ME (R)'

	mi_nr:
		model:
			type: 'decimal'
		view:
			label: 'MI (NR)'

	mi_r:
		model:
			type: 'decimal'
		view:
			label: 'MI (R)'

	mn_nr:
		model:
			type: 'decimal'
		view:
			label: 'MN (NR)'

	mn_r:
		model:
			type: 'decimal'
		view:
			label: 'MN (R)'

	mo_nr:
		model:
			type: 'decimal'
		view:
			label: 'MO (NR)'

	mo_r:
		model:
			type: 'decimal'
		view:
			label: 'MO (R)'

	ms_nr:
		model:
			type: 'decimal'
		view:
			label: 'MS (NR)'

	ms_r:
		model:
			type: 'decimal'
		view:
			label: 'MS (R)'

	mt_nr:
		model:
			type: 'decimal'
		view:
			label: 'MT (NR)'

	mt_r:
		model:
			type: 'decimal'
		view:
			label: 'MT (R)'

	nc_nr:
		model:
			type: 'decimal'
		view:
			label: 'NC (NR)'

	nc_r:
		model:
			type: 'decimal'
		view:
			label: 'NC (R)'

	nd_nr:
		model:
			type: 'decimal'
		view:
			label: 'ND (NR)'

	nd_r:
		model:
			type: 'decimal'
		view:
			label: 'ND (R)'

	ne_nr:
		model:
			type: 'decimal'
		view:
			label: 'NE (NR)'

	ne_r:
		model:
			type: 'decimal'
		view:
			label: 'NE (R)'

	nh_nr:
		model:
			type: 'decimal'
		view:
			label: 'NH (NR)'

	nh_r:
		model:
			type: 'decimal'
		view:
			label: 'NH (R)'

	nj_nr:
		model:
			type: 'decimal'
		view:
			label: 'NJ (NR)'

	nj_r:
		model:
			type: 'decimal'
		view:
			label: 'NJ (R)'

	nm_nr:
		model:
			type: 'decimal'
		view:
			label: 'NM (NR)'

	nm_r:
		model:
			type: 'decimal'
		view:
			label: 'NM (R)'

	nv_nr:
		model:
			type: 'decimal'
		view:
			label: 'NV (NR)'

	nv_r:
		model:
			type: 'decimal'
		view:
			label: 'NV (R)'

	ny_nr:
		model:
			type: 'decimal'
		view:
			label: 'NY (NR)'

	ny_r:
		model:
			type: 'decimal'
		view:
			label: 'NY (R)'

	oh_nr:
		model:
			type: 'decimal'
		view:
			label: 'OH (NR)'

	oh_r:
		model:
			type: 'decimal'
		view:
			label: 'OH (R)'

	ok_nr:
		model:
			type: 'decimal'
		view:
			label: 'OK (NR)'

	ok_r:
		model:
			type: 'decimal'
		view:
			label: 'OK (R)'

	or_nr:
		model:
			type: 'decimal'
		view:
			label: 'OR (NR)'

	or_r:
		model:
			type: 'decimal'
		view:
			label: 'OR (R)'

	pa_nr:
		model:
			type: 'decimal'
		view:
			label: 'PA (NR)'

	pa_r:
		model:
			type: 'decimal'
		view:
			label: 'PA (R)'

	ri_nr:
		model:
			type: 'decimal'
		view:
			label: 'RI (NR)'

	ri_r:
		model:
			type: 'decimal'
		view:
			label: 'RI (R)'

	sc_nr:
		model:
			type: 'decimal'
		view:
			label: 'SC (NR)'

	sc_r:
		model:
			type: 'decimal'
		view:
			label: 'SC (R)'

	sd_nr:
		model:
			type: 'decimal'
		view:
			label: 'SD (NR)'

	sd_r:
		model:
			type: 'decimal'
		view:
			label: 'SD (R)'

	tn_nr:
		model:
			type: 'decimal'
		view:
			label: 'TN (NR)'

	tn_r:
		model:
			type: 'decimal'
		view:
			label: 'TN (R)'

	tx_nr:
		model:
			type: 'decimal'
		view:
			label: 'TX (NR)'

	tx_r:
		model:
			type: 'decimal'
		view:
			label: 'TX (R)'

	ut_nr:
		model:
			type: 'decimal'
		view:
			label: 'UT (NR)'

	ut_r:
		model:
			type: 'decimal'
		view:
			label: 'UT (R)'

	va_nr:
		model:
			type: 'decimal'
		view:
			label: 'VA (NR)'

	va_r:
		model:
			type: 'decimal'
		view:
			label: 'VA (R)'

	vt_nr:
		model:
			type: 'decimal'
		view:
			label: 'VT (NR)'

	vt_r:
		model:
			type: 'decimal'
		view:
			label: 'VT (R)'

	wa_nr:
		model:
			type: 'decimal'
		view:
			label: 'WA (NR)'

	wa_r:
		model:
			type: 'decimal'
		view:
			label: 'WA (R)'

	wi_nr:
		model:
			type: 'decimal'
		view:
			label: 'WI (NR)'

	wi_r:
		model:
			type: 'decimal'
		view:
			label: 'WI (R)'

	wv_nr:
		model:
			type: 'decimal'
		view:
			label: 'WV (NR)'

	wv_r:
		model:
			type: 'decimal'
		view:
			label: 'WV (R)'

	wy_nr:
		model:
			type: 'decimal'
		view:
			label: 'WY (NR)'

	wy_r:
		model:
			type: 'decimal'
		view:
			label: 'WY (R)'

	ak_nr:
		model:
			type: 'decimal'
		view:
			label: 'AK (NR)'

	ak_r:
		model:
			type: 'decimal'
		view:
			label: 'AK (R)'

	hi_nr:
		model:
			type: 'decimal'
		view:
			label: 'HI (NR)'

	hi_r:
		model:
			type: 'decimal'
		view:
			label: 'HI (R)'

	pr_nr:
		model:
			type: 'decimal'
		view:
			label: 'PR (NR)'

	pr_r:
		model:
			type: 'decimal'
		view:
			label: 'PR (R)'

	vi_nr:
		model:
			type: 'decimal'
		view:
			label: 'VI (NR)'

	vi_r:
		model:
			type: 'decimal'
		view:
			label: 'VI (R)'

	description:
		model:
			type: 'text'
		view:
			label: 'Description'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['hcpcs']
	indexes:
		many: [
			['hcpcs']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['hcpcs', 'description']

view:
	comment: 'Manage > List CMS DMEPOS'
	find:
		basic: ['hcpcs']
	grid:
		fields: ['hcpcs']
	label: 'List CMS DMEPOS'