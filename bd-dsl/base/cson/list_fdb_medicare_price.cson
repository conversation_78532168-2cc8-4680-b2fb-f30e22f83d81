#TABLE RMCRPB0_MEDICARE_HCPC_PB_PRICE
#Why only first 4 fields are showing??
fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	hcpc:
		model:
			type: 'text'
		view:
			label: 'HCFA Common Procedure Code'
			columns: 3

	ndc:
		model:
			type: 'text'
		view:
			label: 'National Drug Code'
			columns: 3

	hcpc_pbc1:
		model:
			type: 'date'
		view:
			label: 'HCPC Part B/NOC Price Effective Date'
			columns: 3

	hcpc_pbp1:
		model:
			type: 'decimal'
			rounding: 0.00001
		view:
			label: 'HCPC Part B/NOC Price'
			columns: 3

	hcpc_pbc2:
		model:
			type: 'date'
		view:
			label: 'HCPC Part B/NOC Previous Price Effective Date'
			columns: 3

	hcpc_pbp2:
		model:
			type: 'decimal'
			rounding: 0.00001
		view:
			label: 'HCPC Part B/NOC Previous Price'
			columns: 3

	hcpc_pbc3:
		model:
			type: 'date'
		view:
			label: 'HCPC Part B/NOC 2nd Previous Price Effective Date'
			columns: 3

	hcpc_pbp3:
		model:
			type: 'decimal'
			rounding: 0.00001
		view:
			label: 'HCPC Part B/NOC 2nd Previous Price'
			columns: 3

	hcpc_pbc4:
		model:
			type: 'date'
		view:
			label: 'HCPC Part B/NOC 3rd Previous Price Effective Date'
			columns: 3

	hcpc_pbp4:
		model:
			type: 'decimal'
			rounding: 0.00001
		view:
			label: 'HCPC Part B/NOC 3rd Previous Price'
			columns: 3

	hcpc_pbc5:
		model:
			type: 'date'
		view:
			label: 'HCPC Part B/NOC 4th Previous Price Effective Date'
			columns: 3

	hcpc_pbp5:
		model:
			type: 'decimal'
			rounding: 0.00001
		view:
			label: 'HCPC Part B/NOC 4th Previous Price'
			columns: 3

	hcpc_pbc6:
		model:
			type: 'date'
		view:
			label: 'HCPC Part B/NOC 5th Previous Price Effective Date'
			columns: 3

	hcpc_pbp6:
		model:
			type: 'decimal'
			rounding: 0.00001
		view:
			label: 'HCPC Part B/NOC 5th Previous Price'
			columns: 3

	hcpc_pbc7:
		model:
			type: 'date'
		view:
			label: 'HCPC Part B/NOC 6th Previous Price Effective Date'
			columns: 3

	hcpc_pbp7:
		model:
			type: 'decimal'
			rounding: 0.00001
		view:
			label: 'HCPC Part B/NOC 6th Previous Price'
			columns: 3

	hcpc_pbc8:
		model:
			type: 'date'
		view:
			label: 'HCPC Part B/NOC 7th Previous Price Effective Date'
			columns: 3

	hcpc_pbp8:
		model:
			type: 'decimal'
			rounding: 0.00001
		view:
			label: 'HCPC Part B/NOC 7th Previous Price'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['hcpc', 'hcpc_pbp1']
	indexes:
		many: [
			['hcpc']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['hcpc', 'ndc', 'hcpc_pbc1', 'hcpc_pbp1', 'hcpc_pbc2', 'hcpc_pbp2', 'hcpc_pbc3',
			'hcpc_pbp3', 'hcpc_pbc4', 'hcpc_pbp4', 'hcpc_pbc5', 'hcpc_pbp5', 'hcpc_pbc6', 'hcpc_pbp6',
			'hcpc_pbc7', 'hcpc_pbp7', 'hcpc_pbc8', 'hcpc_pbp8']

view:
	comment: 'Manage > List FDB Medicare Price Table'
	find:
		basic: ['hcpc']
	grid:
		fields: ['hcpc', 'ndc', 'hcpc_pbc1', 'hcpc_pbp1']
	label: 'List FDB Medicare Price Table'
