fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	medical_hist:
		model:
			multi: true
			source: ['Chemical Dependency', 'CV', 'Endocrine', 'Eye', 'GI',
			'GU', 'Glasses', 'Head/Neck', 'Immune', 'Musculoskeletal',
			'Neuro', 'OB/GYN', 'Pain', 'PSY/SOC', 'SIG. MED HX', 'Skin', 'Other']
			if:
				'*':
					fields: ['medical_hist_desc']
		view:
			columns: 2
			control: 'checkbox'
			note: 'Check all that apply'
			label: 'Medical History'

	medical_hist_desc:
		model:
			required: true
		view:
			columns: 2
			control: 'area'
			label: 'Medical History Details'

	surgical_hist_check:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['surgical_hist']
		view:
			columns: 2
			control: 'radio'
			label: 'Any Surgical History?'

	surgical_hist:
		model:
			required: true
		view:
			columns: 2
			label: 'Surgical History Details'
			control: 'area'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	bundle: ['patient']
	name: ['patient_id', 'medical_hist']
	sections: 
		'Medical/Surgical History':
			fields: ['medical_hist', 'medical_hist_desc', 'surgical_hist_check', 'surgical_hist']
view:
	dimensions:
        width: '75%'
        height: '75%'
	hide_cardmenu: true
	label: 'Medical/Surgical History'
