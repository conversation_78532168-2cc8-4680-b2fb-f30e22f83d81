fields:
	payer_price_matrix_id:
		model:
			type: 'int'
			required: true
		view:
			offscreen: true
			label: 'Assigned Shared Contract'

	contract_type:
		model:
			source: ['Contract', 'Shared Contract']
			if:
				'Shared Contract':
					fields: ['special_price', 'special_price_multiplier', 'special_price_formula_id',
					'override_special_formula_id', 'override_special_price_multiplier', 'device_sale_price_special',
					'daily_rental_price_special', 'monthly_rental_price_special']
		view:
			columns: 3
			label: 'Contract Type'
			control: 'radio'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
			query: 'select_inv_price_matrix_item'
			sourcefilter:
				_x1:
					'dynamic': '{payer_price_matrix_id}'
				_x2:
					'dynamic': '{inventory_id}'
		view:
			columns: 2
			readonly: true
			label: 'Inventory Item'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'hcpc_code': ['hcpc_code'],
					'formatted_ndc': ['formatted_ndc'],
					'manufacturer_id': ['manufacturer_id'],
					'type': ['type'],
					'price_code_id': ['price_code_id']
					'awp_price': ['awp_price_pkg'],
					'wac_price': ['wac_price_pkg'],
					'list_price': ['list_price'],
					'last_cost': ['last_cost_ea'],
					'special_price_formula_id': ['special_price_formula_id'],
					'expected_price_formula_id': ['expected_price_formula_id'],
					'special_price_multiplier': ['special_price_multiplier'],
					'expected_price_multiplier': ['expected_price_multiplier']
			]

	hcpc_code:
		view:
			label: 'HCPC Code'
			readonly: true
			columns: 4

	special_code:
		view:
			label: 'Special Code'
			columns: 4
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Z][0-9]{4}$'
				error: 'Invalid HCPC Code, must be 1-10 alphanumeric characters'
			}]

	formatted_ndc:
		model:
			type: 'text'
			search: 'A'
		view:
			label: 'NDC'
			columns: 4
			findunique: true
			class: 'fdb-field'
			readonly: true

	manufacturer_id:
		model:
			required: false
			source: 'list_manufacturer'
			sourceid: 'code'
		view:
			label: 'Manufacturer'
			class: 'fdb-field'
			columns: 4
			readonly: true

	external_id:
		model:
			required: false
		view:
			label: 'External ID'
	type:
		model:
			required: true
			source: ['Drug', 'Compound', 'Supply', 'Equipment Rental', 'Billable']
			prefill: ['inventory']
			if:
				'Drug':
					sections: ['Drug/Supply Item']
					fields: ['auth_required']
				'Compound':
					sections: ['Drug/Supply Item']
					fields: ['auth_required']
				'Supply':
					sections: ['Drug/Supply Item']
				'Equipment Rental':
					sections: ['Device']
					fields: ['auth_required']
		view:
			control: 'radio'
			label: 'Type'
			readonly: true

	price_code_id:
		model:
			source: 'list_price_code'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Price Code'
			readonly: true

	# If $0 then item is billable but $0.00 is put in for the expected
	billable:
		model:
			required: true
			source: ['No', 'Yes', '$0']
			default: 'Yes'
		view:
			control: 'radio'
			columns: 3
			label: 'Billable by contract?'

	# Set if an item is strickly not covered by the payor. Should alert a dispense with a payor and an item not covered. If set to block, item should not be available for selection.
	not_covered:
		model:
			source: ['No', 'Yes', 'Block']
		view:
			columns: 3
			control: 'radio'
			label: 'Not covered by SPM?'

	# Set if an authorization is required prior to billing. Should check during a dispense.
	# If set to yes, then show a warning, if set to Block then don't allow the dispense to continue without one.
	auth_required:
		model:
			source: ['No', 'Yes', 'Block']
		view: 
			columns: 3
			control: 'radio'
			label: 'Authorization required?'

	# This is required documents for Medicare. A popup should appear during dispensing if required
	rdc_ids:
		model:
			multi: true
			sourceid: 'code'
			source: 'list_required_doc'
		view:
			label: 'Required Docs'

	modifier_1:
		model:
			min: 2
			max: 2
		view:
			columns: 'modifier'
			label: 'Modifier 1'

	modifier_2:
		model:
			min: 2
			max: 2
		view:
			columns: 'modifier'
			label: 'Modifier 2'

	modifier_3:
		model:
			min: 2
			max: 2
		view:
			columns: 'modifier'
			label: 'Modifier 3'

	modifier_4:
		model:
			min: 2
			max: 2
		view:
			columns: 'modifier'
			label: 'Modifier 4'

	last_cost:
		model:
			rounding: 0.00001
			type: 'decimal'
			save: false
		view:
			columns: 4
			note: 'Each'
			label: 'Cost'
			class: 'numeral'
			format:'$0,0.0000'
			readonly: true

	awp_price:
		model:
			rounding: 0.00001
			type: 'decimal'
			save: false
		view:
			columns: 4
			note: 'Each'
			label: 'AWP'
			class: 'numeral'
			format:'$0,0.0000'
			readonly: true

	wac_price:
		model:
			rounding: 0.00001
			type: 'decimal'
			save: false
		view:
			columns: 4
			note: 'Each'
			label: 'WAC'
			class: 'numeral'
			format:'$0,0.0000'
			readonly: true

	add_price1:
		model:
			rounding: 0.00001
			type: 'decimal'
			save: false
		view:
			columns: 4
			note: 'Each'
			label: 'Additional Price 1'
			class: 'numeral'
			format:'$0,0.0000'
			readonly: true
			
	add_price2:
		model:
			rounding: 0.00001
			type: 'decimal'
			save: false
		view:
			columns: 4
			note: 'Each'
			label: 'Additional Price 2'
			class: 'numeral'
			format:'$0,0.0000'
			readonly: true

	list_price:
		model:
			rounding: 0.00001
			type: 'decimal'
			save: false
		view:
			columns: 4
			note: 'Each'
			label: 'List'
			class: 'numeral'
			format:'$0,0.0000'
			readonly: true

	special_price:
		model:
			rounding: 0.00001
			type: 'decimal'
		view:
			columns: 4
			label: 'Special Price'
			class: 'numeral'
			format:'$0,0.0000'
			readonly: true

	expected_price:
		model:
			rounding: 0.00001
			type: 'decimal'
		view:
			columns: 4
			label: 'Expected Price'
			class: 'numeral'
			format:'$0,0.0000'
			readonly: true

	# It is a common practice of Payors to set limits on the number of times a particular item may be dispensed over a period of time. To avoid going over those limits, the Limit field may be used to record the limit set by day or month in the SPM. On the Delivery Ticket, if a user attempts to add a quantity greater than the amount assigned for the given period of time, the user will receive a warning message
	limits:
		model:
			required: true
			default: 'No Limits'
			source: ['Units', 'Fills', 'No Limits']
			if:
				'Units':
					fields: ['unit_limit', 'limit_freq']
				'Fills':
					fields: ['refills_limit']
		view:
			columns: 2
			control: 'radio'
			label: 'Payer Limits'

	refills_limit:
		model:
			required: true
			type: 'int'
			min: 1
			max: 99
		view:
			columns: 4
			label: 'Refill Limit'

	unit_limit:
		model:
			rounding: 1
			type: 'int'
			if:
				'*':
					fields: ['limit_freq']
		view:
			columns: 4
			note: 'Each'
			label: 'Unit Limit'

	#Freq. - The frequency and limit amounts will be set within the SPM and tracked by the system on both working and confirmed delivery tickets. 
	limit_freq:
		model:
			required: true
			source: ['Day', 'Week', 'Month', 'Quarter', 'Bi-Annual', 'Annual']
		view:
			columns: 4
			control: 'radio'
			label: 'Limit Frequency'

	# Use A for AWP, L for List Price, C for Cost, W for WAC, 1 for Additional Price 1, 2 for Additional Price 2 from inventory * multiplier 
	# Should use the site_price_matrix to determine by pricing code
	special_price_formula_id:
		model:
			source: 'list_price_basis'
			sourceid: 'code'
			save: false
		view:
			columns: -4
			label: 'Special Price Basis'
			readonly: true

	special_price_multiplier:
		model:
			rounding: 0.00001
			type: 'decimal'
			save: false
		view:
			columns: 4
			note: 'Each'
			label: 'Special Price Multiplier'
			readonly: true

	# If set, should override above
	override_special_formula_id:
		model:
			source: 'list_price_basis'
			sourceid: 'code'
			if:
				'*':
					require_fields: ['override_special_price_multiplier']
		view:
			columns: 4
			label: 'Override Special Price Basis'
			validate:[
				name: 'CalculateSpecialPrice'
			]

	override_special_price_multiplier:
		model:
			rounding: 0.00001
			type: 'decimal'
			if:
				'*':
					require_fields: ['override_special_formula_id']
		view:
			columns: 4
			note: 'Each'
			label: 'Override Special Price Multiplier'
			validate:[
				name: 'CalculateSpecialPrice'
			]

	expected_price_formula_id:
		model:
			source: 'list_price_basis'
			sourceid: 'code'
			save: false
		view:
			columns: -4
			label: 'Expected Price Basis'
			readonly: true

	expected_price_multiplier:
		model:
			rounding: 0.00001
			type: 'decimal'
			save: false
		view:
			columns: 4
			note: 'Each'
			label: 'Expected Price Multiplier'
			readonly: true

	# If set, should override above
	override_expected_formula_id:
		model:
			source: 'list_price_basis'
			sourceid: 'code'
			if:
				'*':
					require_fields: ['override_expected_price_multiplier']
		view:
			columns: 4
			label: 'Override Expected Price Basis'
			validate:[
				name: 'CalculateExpectedPrice'
			]

	override_expected_price_multiplier:
		model:
			rounding: 0.00001
			type: 'decimal'
			if:
				'*':
					require_fields: ['override_expected_formula_id']
		view:
			columns: 4
			note: 'Each'
			label: 'Override Expected Price Multiplier'
			validate:[
				name: 'CalculateExpectedPrice'
			]

	# Device
	device_sale_price_special:
		model:
			rounding: 0.00001
			type: 'decimal'
		view:
			columns: -2
			label: 'Sale Price (Special)'
			class: 'numeral'
			format:'$0,0.0000'

	device_sale_price_expected:
		model:
			rounding: 0.00001
			type: 'decimal'
		view:
			columns: 2
			label: 'Sale Price (Expected)'
			class: 'numeral'
			format:'$0,0.0000'

	daily_rental_price_special:
		model:
			rounding: 0.00001
			type: 'decimal'
		view:
			columns: -2
			label: 'Daily Rental Price (Special)'
			class: 'numeral'
			format:'$0,0.0000'

	daily_rental_price_expected:
		model:
			rounding: 0.00001
			type: 'decimal'
		view:
			columns: 2
			label: 'Daily Rental Price (Expected)'
			class: 'numeral'
			format:'$0,0.0000'

	monthly_rental_price_special:
		model:
			rounding: 0.00001
			type: 'decimal'
		view:
			columns: -2
			label: 'Monthly Rental Price (Special)'
			class: 'numeral'
			format:'$0,0.0000'

	monthly_rental_price_expected:
		model:
			rounding: 0.00001
			type: 'decimal'
		view:
			columns: 2
			label: 'Monthly Rental Price (Expected)'
			class: 'numeral'
			format:'$0,0.0000'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['inventory_id',
			'price_code_id',
			'type',
			'hcpc_code',
			'special_code']
		]
		unique: [
			['inventory_id', 'payer_price_matrix_id']
		]
	prefill:
		inventory:
			link:
				id: 'inventory_id'
	reportable: true
	name: '{inventory_id}'

	sections:
		'Item Information':
			fields: ['inventory_id', 'contract_type', 'hcpc_code', 'formatted_ndc', 'manufacturer_id', 'special_code', 'price_code_id', 'type', 'billable',
			'not_covered', 'auth_required', 'rdc_ids', 'modifier_1', 'modifier_2', 'modifier_3', 'modifier_4']
		'Drug/Supply Item':
			fields: ['last_cost', 'awp_price', 'wac_price', 'list_price', 'add_price1', 'add_price2',
			'limits', 'refills_limit', 'unit_limit', 'limit_freq', 'special_price_formula_id',
			'special_price_multiplier', 'override_special_formula_id',
			'override_special_price_multiplier', 'special_price',
			'expected_price_formula_id', 'expected_price_multiplier',
			'override_expected_formula_id', 'override_expected_price_multiplier',
			'expected_price']
		'Device':
			fields: ['device_sale_price_special',
			'device_sale_price_expected', 'daily_rental_price_special',
			'daily_rental_price_expected', 'monthly_rental_price_special',
			'monthly_rental_price_expected']

view:
	comment: 'Payer > Shared Contract Item'
	grid:
		fields: ['inventory_id', 'price_code_id', 'type', 'billable', 'expected_price']
		sort: ['-inventory_id']
	label: 'Shared Contract Item'
	open: 'read'