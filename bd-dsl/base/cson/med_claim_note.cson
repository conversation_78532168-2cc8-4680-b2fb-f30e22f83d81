fields:

	additional_information:
		model:
			min: 1
			max: 80
		view:
			label: 'Additional Information'
			control: 'area'
			reference: 'NTE02 NTE01=ADD'
			_meta:
				location: '2300 NTE'
				field: '02'
				code: 'ADD'
				path: 'claimInformation.claimNote.additionalInformation'

	certification_narrative:
		model:
			min: 1
			max: 80
		view:
			label: 'Certification Narrative'
			control: 'area'
			reference: 'NTE02 NTE01=CER'
			_meta:
				location: '2300 NTE'
				field: '02'
				code: 'CER'
				path: 'claimInformation.claimNote.certificationNarrative'

	goal_rehab_or_discharge_plans:
		model:
			min: 1
			max: 80
		view:
			label: 'Goal/Rehab or Discharge Plans'
			control: 'area'
			reference: 'NTE02 NTE01=DCP'
			_meta:
				location: '2300 NTE'
				field: '02'
				code: 'DCP'
				path: 'claimInformation.claimNote.goalRehabOrDischargePlans'

	diagnosis_description:
		model:
			min: 1
			max: 80
		view:
			label: 'Diagnosis Description'
			control: 'area'
			reference: 'NTE02 NTE01=DGN'
			_meta:
				location: '2300 NTE'
				field: '02'
				code: 'DGN'
				path: 'claimInformation.claimNote.diagnosisDescription'

	third_part_org_notes:
		model:
			min: 1
			max: 80
		view:
			label: '3rd-Party Organization Notes'
			control: 'area'
			reference: 'NTE02 NTE01=TPO'
			_meta:
				location: '2300 NTE'
				field: '02'
				code: 'TPO'
				path: 'claimInformation.claimNote.thirdPartOrgNotes'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		delete:     ['admin', 'pharm', 'patient']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
	name: ['diagnosis_description','additional_information']
	sections:
		'Additional Info':
			hide_header: true
			tab: 'Addtl Info'
			fields: ['additional_information']
		'Diagnosis Description':
			hide_header: true
			tab: 'Dx Desc'
			fields: ['diagnosis_description']
		'Certification Narrative':
			hide_header: true
			tab: 'Cert Narr'
			fields: ['certification_narrative']
		'Goals/Rehab or Discharge Plans':
			hide_header: true
			tab: 'Goals D/C'
			fields: ['goal_rehab_or_discharge_plans']
		'Third Party Org Notes':
			hide_header: true
			tab: '3rd-Party'
			fields: ['third_part_org_notes']

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	reference: '2300'
	comment: 'Medical Claim > Notes'
	grid:
		fields: ['created_on', 'created_by', 'additional_information']
		sort: ['-id']
	label: 'Notes'
