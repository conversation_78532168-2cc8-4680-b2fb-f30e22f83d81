fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	functional_lim:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['Functional Limitations']
		view:
			control: 'radio'
			label: 'Does patient have any functional limitations?'
			columns: 2

	functional_mobility:
		model:
			required: true
			source: ['1','2','3','4','5','6','7','8','9','10']
		view:
			control: 'radio'
			note: 'i.e. Walking and moving around'
			label: 'Mobility'
			columns: 2

	functional_position:
		model:
			required: true
			source: ['1','2','3','4','5','6','7','8','9','10']
		view:
			control: 'radio'
			label: 'Changing and maintaining body position'
			columns: 2

	functional_carry:
		model:
			required: true
			source: ['1','2','3','4','5','6','7','8','9','10']
		view:
			control: 'radio'
			label: 'Carrying, moving, and handling objects'
			columns: 2

	functional_selfcare:
		model:
			required: true
			source: ['1','2','3','4','5','6','7','8','9','10']
		view:
			control: 'radio'
			note: 'i.e. Changing clothes, going to the restroom, bathing'
			label: 'Self care'
			columns: 2

	functional_swallowing:
		model:
			required: true
			source: ['1','2','3','4','5','6','7','8','9','10']
		view:
			control: 'radio'
			label: 'Swallowing'
			columns: 2

	functional_speech:
		model:
			required: true
			source: ['1','2','3','4','5','6','7','8','9','10']
		view:
			control: 'radio'
			note: 'i.e. problems saying sounds, syllables, or words'
			label: 'Motor Speech'
			columns: 2

	functional_comprehension:
		model:
			required: true
			source: ['1','2','3','4','5','6','7','8','9','10']
		view:
			control: 'radio'
			label: 'Spoken language comprehension'
			columns: 2

	functional_expression:
		model:
			required: true
			source: ['1','2','3','4','5','6','7','8','9','10']
		view:
			control: 'radio'
			label: 'Spoken language expression'
			columns: 2

	functional_attention:
		model:
			required: true
			source: ['1','2','3','4','5','6','7','8','9','10']
		view:
			control: 'radio'
			note: 'i.e. ability to focus on a task'
			label: 'Attention'
			columns: 2

	functional_memory:
		model:
			required: true
			source: ['1','2','3','4','5','6','7','8','9','10']
		view:
			control: 'radio'
			label: 'Memory'
			columns: 2

	functional_voice:
		model:
			required: true
			source: ['1','2','3','4','5','6','7','8','9','10']
		view:
			control: 'radio'
			label: 'Voice'
			columns: 2

	functional_other_speech:
		model:
			required: true
			source: ['1','2','3','4','5','6','7','8','9','10']
		view:
			control: 'radio'
			note: 'i.e. problems speaking because of disease state like swollen tongue or stroke'
			label: 'Other speech pathology'
			columns: 2

	functional_lim_com:
		view:
			label: 'Functional limitations Comment'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_tag:
		model:
			type: 'text'
		view:
			label: 'Envoy External Tag'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_funct_limits:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['functional_mobility']
	sections:
		'Functional Limitations':
			prefill: 'clinical_funct_limits'
			note: 'Rate the following functional limitations from 1-10 with 1 = not limited at all and 10 = being severely limited'
			fields: ['functional_mobility', 'functional_position',
			'functional_carry', 'functional_selfcare', 'functional_swallowing',
			'functional_speech', 'functional_comprehension',
			'functional_expression', 'functional_attention',
			'functional_memory', 'functional_voice', 'functional_other_speech',
			'functional_lim_com']
view:
	hide_cardmenu: true
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-id']
	comment: 'Patient > Careplan > Clinical > Functional Limitations'
	label: 'Functional Limitations'
