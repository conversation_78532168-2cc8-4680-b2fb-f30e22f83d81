fields:

	label_type:
		model:
			source: ['PO', 'IV', 'TPN', 'Syringe', 'Additives', 'Shipping']
			if:
				'!Shipping':
					fields: ['copy_label_changes']
		view:
			label: 'Label'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			required: false
			source: 'inventory'
		view:
			label: 'Drug'
			readonly: true
			columns: 2

	number_of_labels:
		model:
			default: 1
			required: true
			type: 'int'
			min: 1
			max: 10
		view:
			columns: 4
			label: 'Number of Labels'

	copy_label_changes:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			label: 'Copy changes back to Prescription?'
			class: 'checkbox-only'
			columns: 4

	rx_no:
		model:
			type: 'text'
		view:
			label: 'RX Number'
			class: 'claim-field'
			readonly: true
			offscreen: true

	label_form:
		view:
			label: 'Label Form'
			offscreen: true
			readonly: true

	subform_label:
		model:
			type: 'subform'
			multi: false
			source: '{label_form}'
			sourcefilter:
				'careplan_order_lbl_inj': {}
				'careplan_order_lbl_iv': {}
				'careplan_order_lbl_po': {}
				'careplan_order_lbl_tpn': {}
				'careplan_order_lbl_tpn_addv': {}
				'careplan_order_lbl_syr': {}
		view:
			label: 'Label Subform'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: "{label_type}"
	prefill:
		patient:
			link:
				id: 'patient_id'
	indexes:
		many: [
			['inventory_id']
			['rx_no']
		]

	sections_group: [

		'Label':
			indent: false
			hide_header: true
			fields: ['label_type', 'inventory_id','number_of_labels', 'copy_label_changes', 'rx_no', 'label_form']

		'Label':
			indent: false
			hide_header: true
			fields: ['subform_label']
	]

view:
	dimensions:
		width: '75%'
		height: '55%'
	hide_cardmenu: true
	comment: 'Patient > Prescription Label'
	grid:
		fields: ['label_type', 'number_of_labels', 'inventory_id']
		sort: ['-created_on']
	label: 'Prescription Label'
	open: 'read'
