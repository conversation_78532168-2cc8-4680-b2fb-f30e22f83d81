fields:
	doc_type_id:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '369-2Q'
		view:
			columns: 3
			reference: '369-2Q'
			note: '369-2Q'
			label: 'Document Type ID'

	request_status:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '373-2U'
			if:
				'2':
					require_fields: ['request_period_recert_revised_date']
				'3':
					require_fields: ['request_period_recert_revised_date']
		view:
			columns: 3
			reference: '373-2U'
			note: '373-2U'
			label: 'Request Status'

	request_period_begin_date:
		model:
			type: 'date'
		view:
			columns: 3
			reference: '374-2V'
			note: '374-2V'
			label: 'Request Period Begin Date'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	request_period_recert_revised_date:
		model:
			type: 'date'
		view:
			columns: 3
			reference: '375-2W'
			note: '375-2W'
			label: 'Request Period Recert/Revised Date'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	length_of_need:
		model:
			max: 999
			if:
				'*':
					require_fields: ['length_of_need_qualifier']
		view:
			columns: 3
			reference: '370-2R'
			note: '370-2R'
			label: 'Length of Need'

	length_of_need_qualifier:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '371-2S'
			if:
				'*':
					require_fields: ['length_of_need']
		view:
			columns: 3
			reference: '371-2S'
			note: '371-2S'
			label: 'Length of Need Qualifier'

	prescriber_supplier_date_signed:
		model:
			type: 'date'
		view:
			columns: 3
			reference: '372-2T'
			note: '372-2T'
			label: 'Prescriber/Supplier Date Signed'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	supporting_documentation:
		model:
			max: 65
		view:
			columns: 3
			reference: '376-2X'
			note: '376-2X'
			label: 'Supporting Documentation'

	subform_answer:
		model:
			type: 'subform'
			multi: true
			source: 'ncpdp_doc_answer'
		view:
			grid:
				add: 'inline'
				edit: true
				fields: ['question_number_letter', 'question_percent_response', 'question_date_response', 'question_dollar_amount_response', 'question_numeric_response', 'question_alphanumeric_response']
				label: ['Q#', '%', 'Dt', '$', '#', 'Txt']
				width: [20, 10, 20, 20, 10, 20]
			max_count: 50
			label: 'Answers'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['doc_type_id', 'request_status', 'request_period_begin_date']
	sections_group: [
		'Details':
			hide_header: true
			indent: false
			fields: ['doc_type_id', 'request_status', 'request_period_begin_date',
			'request_period_recert_revised_date', 'length_of_need', 'length_of_need_qualifier',
			'prescriber_supplier_date_signed', 'supporting_documentation']

		'Answers':
			note: 'Max 50 Entries'
			fields: ['subform_answer']
		]

view:
	dimensions:
		width: '90%'
		height: '85%'
	hide_cardmenu: true
	validate: [
		{
			name: "DateOrderValidator"
			fields: [
				"request_period_begin_date"
				"request_period_recert_revised_date"
			]
			error: "Request Period Begin Date cannot be after Request Period Recert/Revised Date"
		}
	]
	comment: 'Addl. Doc'
	grid:
		fields: ['doc_type_id', 'request_status', 'request_period_begin_date', 'request_period_recert_revised_date']
		sort: ['-created_on']
	label: 'Addl. Doc'
	open: 'read'