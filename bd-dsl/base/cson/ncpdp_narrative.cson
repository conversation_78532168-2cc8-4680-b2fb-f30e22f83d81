fields:
	narrative_message:
		model:
			max: 200
		view:
			reference: '390-BM'
			note: '390-BM'
			control: 'area'
			label: 'Narrative'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['narrative_message']
	sections:
		'Narrative':
			hide_header: true
			indent: false
			fields: ['narrative_message']

view:
	dimensions:
		width: '65%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Narrative'
	grid:
		fields: ['narrative_message']
		sort: ['-created_on']
	label: 'Narrative'
	open: 'read'