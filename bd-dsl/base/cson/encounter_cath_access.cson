fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	site_cond:
		model:
			required: true
			max: 128
			source: ['WNL', 'Tender/Painful', 'Drainage', 'Edema', 'Ecchymosis', 'Erythema', 'Sutures']
		view:
			control: 'checkbox'
			note: 'select all that apply'
			label: 'Site Condition'
			columns: 2

	phlebitis:
		model:
			required: false
			max: 3
			source: ['0', '1', '2', '3', '4']
		view:
			control: 'radio'
			label: 'Phlebitis'
			columns: 2

	site_asymptomatic:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['site_asymptomatic_details']
		view:
			label: 'Was the insertion site asymptomatic?'
			columns: -2

	site_asymptomatic_details:
		model:
			required: true
		view:
			label: 'Details'
			columns: 2

	site_irritation:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			label: 'Any skin irritation around insertion site?'
			columns: 2

	sticks:
		model:
			required: true
			type: 'int'
		view:
			label: 'Number Of Sticks'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	name: ['site_cond']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['site_cond', 'phlebitis',
			'site_asymptomatic', 'site_asymptomatic_details',
			'site_irritation', 'sticks']

view:
	comment: 'Patient > Careplan > Encounter > Catheter Access'
	grid:
		fields: ['site_cond', 'phlebitis', 'site_asymptomatic', 'site_irritation']
	label: 'Catheter Access'
