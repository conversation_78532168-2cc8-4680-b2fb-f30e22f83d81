fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			control: 'select'
			label: 'Patient'
			offscreen: true
			readonly: true
			validate:[
				name:'SetInsurance'
			]

	insurance_id:
		model:
			source: 'patient_insurance'
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				billing_method_id:
					'static': 'mm'
				active:
					'static': 'Yes'
		view:
			columns: 3
			control: 'select'
			label: 'Insurance'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_insurance/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'payer_id': ['payer_id'],
			]

	payer_id:
		model:
			type: 'int'
			source: 'payer'
			required: true
		view:
			columns: 3
			label: 'Payer'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/payer/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'claim_filing_code': ['mm_claim_filing_indicator_code'],
					'place_of_service_code': ['mm_default_service_place_id']
			]

	claim_filing_code:
		model:
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR09'
		view:
			columns: 3
			label: 'Payer Type'
			note: 'Claim Filing Code (SBR09)'
			reference: 'SBR09'
			class: 'claim-field'

	prescriber_id:
		model:
			prefill: ['patient_prescriber.id']
			required: true
			type: 'int'
			source: 'patient_prescriber'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			columns: 3
			label: 'Prescriber'

	claim_charge_amount:
		model:
			max: **********.99
			min: 0.00
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			columns: -3
			class: 'numeral'
			format:'$0,0.00'
			label: 'Charge Amt'
			note: 'Aggregated from Service Lines'
			reference: 'CLM02'

	place_of_service_code:
		model:
			default: '12'
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SV105'
		view:
			columns: 3
			label: 'Place of Service Code'
			reference: 'SV105'
			class: 'claim-field'

	subform_sl:
		model:
			required: true
			multi: true
			source: 'view_billing_mclaim_test_sl'
			type: 'subform'
		view:
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['date_of_service', 'inventory_id', 'service_unit_count', 'line_item_charge_amount']
				label: ['Date', 'Item', 'Quantity', 'Amt']
				width: [20, 40, 20, 20]
			label: 'Service Lines'

model:
	save: false
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
	reportable: false

	name: ['patient_id', 'insurance_id']
	sections_group: [
		'Medical Test Claim':
			sections: [
				'Claim Details':
					fields: ['patient_id', 'insurance_id',
					'payer_id', 'claim_filing_code', 'prescriber_id',
					'claim_charge_amount', 'place_of_service_code']
				'Serivice Lines':
					fields: ['subform_sl']
			]
		]

view:
	hide_cardmenu: true
	comment: 'Medical Test Claim'
	find:
		basic: ['insurance_id']
	grid:
		fields: ['patient_id', 'insurance_id']
		sort: ['-id']
	label: 'Medical Test Claim'
