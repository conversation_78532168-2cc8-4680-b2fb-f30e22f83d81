#TABLE: RPEMOGC0_MONO_GCNSEQNO_LINK
#could not open the form
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#pemono
	pemono:
		model:
			type: 'int'
		view:
			label: 'Patient Education Monograph Code'
			readonly: true
			columns: 3

	#gcn_seqno
	gcn_seqno:
		model:
			type: 'int'
		view:
			label: 'Clinical Formulation ID (Stable ID)'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{pemono}"
	indexes:
		many: [
			['pemono']
		]
	sections:
		'Patient Education':
			fields: ['pemono', 'gcn_seqno']

view:
	comment: 'Manage > List FDB Patient Education Monograph Link Table'
	find:
		basic: ['pemono']
	grid:
		fields: ['pemono', 'gcn_seqno']
	label: 'List FDB Patient Education Monograph Link Table'
