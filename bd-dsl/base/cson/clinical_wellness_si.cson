fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Wellness
	since_last:
		model:
			max: 64
			min: 1
			source: ['Improved', 'Improved, then relapsed', 'No change (stable)', 'Relapsed since last injection (worsening)', 'N/A']
			if:
				'Improved, then relapsed':
					fields: ['days_relapse']
				'Relapsed since last injection (worsening)':
					fields: ['days_relapse']
		view:
			control: 'select'
			label: 'Since Last Injection'

	days_relapse:
		model:
			type: 'int'
			min: 1
			required: true
		view:
			label: 'How many days after your last injection until your symptoms reappeared?'

	compared_wellness:
		model:
			max: 12
			source: ['Better', 'Same', 'Worse']
		view:
			control: 'radio'
			label: 'Compared to onset of my disease, as of today I am:'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_wellness_si:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Wellness (Self Injectable)':
			fields: ['since_last', 'days_relapse', 'compared_wellness']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical Wellness (Self Injectable)'
	label: 'Patient Clinical Wellness (Self Injectable)'
