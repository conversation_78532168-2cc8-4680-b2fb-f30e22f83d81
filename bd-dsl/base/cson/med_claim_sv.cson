fields:

	patient_id:
		model:
			type: 'int'
			required: false
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	charge_no:
		model:
			type: 'text'
		view:
			label: 'Linked Ledger Charge Line'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			source: 'inventory'
			required: true
		view:
			form_link_enabled: true
			columns: -2
			label: 'Service Item'
			readonly: true

	type:
		model:
			required: true
			default: 'Drug'
			source: ['Drug', 'Compound', 'Supply', 'Equipment Rental', 'Billable']
		view:
			label: 'Type'
			readonly: true
			offscreen: true

	procedure_identifier:
		model:
			required: true
			default: 'HC'
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SV101'
		view:
			columns: 4
			label: 'Procedure Identifier'
			reference: 'SV101'
			_meta:
				location: '2400 SV1'
				field: '01-01'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.procedureIdentifier'

	procedure_code:
		model:
			required: true
			min: 1
			max: 48
		view:
			columns: 4
			label: 'Procedure Code'
			reference: 'SV101-02'
			_meta:
				location: '2400 SV1'
				field: '01-02'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.procedureCode'

	description:
		model:
			required: true
			min: 1
			max: 50
		view:
			label: 'Description'
			reference: 'SV101-07'
			readonly: true
			_meta:
				location: '2400 SV1'
				field: '01-07'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.description'

	service_unit_count:
		model:
			required: true
			max: 99999.999
			min: 0.001
			rounding: 0.001
			type: 'decimal'
		view:
			columns: -4
			label: 'Charge Units'
			reference: 'SV104'
			class: 'numeral'
			format: '0,0.[000000]'
			readonly: true
			_meta:
				location: '2400 SV1'
				field: '04'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.serviceUnitCount'

	measurement_unit:
		model:
			default: 'UN'
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SV103'
		view:
			columns: 4
			label: 'Unit'
			reference: 'SV103'
			_meta:
				location: '2400 SV1'
				field: '03'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.measurementUnit'

	line_item_charge_amount:
		model:
			required: true
			max: 9999999999.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Charge Amount'
			reference: 'SV102'
			readonly: true
			_meta:
				location: '2400 SV1'
				field: '02'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.lineItemChargeAmount'

	emergency_indicator:
		model:
			source: ['Yes']
		view:
			columns: -4
			control: 'checkbox'
			label: 'Emergency Related?'
			class: 'checkbox-only'
			reference: 'SV109'
			_meta:
				location: '2400 SV1'
				field: '09'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.emergencyIndicator'

	epsdt_indicator:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			label: 'EPSDT Related?'
			class: 'checkbox-only'
			reference: 'SV111'
			_meta:
				location: '2400 SV1'
				field: '11'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.epsdtIndicator'

	copay_status_code:
		model:
			source:
				'0': 'Yes'
		view:
			columns: 4
			control: 'checkbox'
			label: 'Copay Exempt?'
			class: 'checkbox-only'
			reference: 'SV115'
			_meta:
				location: '2400 SV1'
				field: '15'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.copayStatusCode'

	place_of_service_code:
		model:
			default: '12'
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SV105'
		view:
			columns: 4
			label: 'Place of Service Code'
			reference: 'SV105'
			_meta:
				location: '2400 SV1'
				field: '05'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.placeOfServiceCode'

	modifier_1:
		model:
			min: 2
			max: 2
		view:
			columns: 'modifier'
			label: 'Modifier 1'
			readonly: true
			reference: 'SV101-03'
			_meta:
				location: '2400 SV1'
				field: '01-03'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.procedureModifiers[0]'
			validate: [{
				name: 'ReverseParentPrefill'
			}
			]

	modifier_2:
		model:
			min: 2
			max: 2
			if:
				'*':
					require_fields: ['modifier_1']
		view:
			columns: 'modifier'
			label: 'Modifier 2'
			readonly: true
			reference: 'SV101-04'
			_meta:
				location: '2400 SV1'
				field: '01-04'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.procedureModifiers[1]'

	modifier_3:
		model:
			min: 2
			max: 2
			if:
				'*':
					require_fields: ['modifier_2']
		view:
			columns: 'modifier'
			label: 'Modifier 3'
			readonly: true
			reference: 'SV101-05'
			_meta:
				location: '2400 SV1'
				field: '01-05'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.procedureModifiers[2]'

	modifier_4:
		model:
			min: 2
			max: 2
			if:
				'*':
					require_fields: ['modifier_3']
		view:
			columns: 'modifier'
			label: 'Modifier 4'
			readonly: true
			reference: 'SV101-06'
			_meta:
				location: '2400 SV1'
				field: '01-06'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.procedureModifiers[3]'

	dx_filter:
		model:
			multi: true
			source: 'patient_diagnosis'
		view:
			label: 'Filter IDs for diagnosis'
			validate: [
				name: 'AddClaimDiagnoses'
			]
			readonly: true
			offscreen: true

	dx_id_1:
		model:
			source: 'patient_diagnosis'
			required: true
			sourcefilter:
				id:
					'dynamic': '{dx_filter}'
				patient_id:
					'dynamic': '{patient_id}'
			if:
				'*':
					fields: ['dx_id_2']
		view:
			columns: -2
			label: 'Diagnosis 1'
			reference: 'SV107-01'
			_meta:
				location: '2400 SV1'
				field: '07-01'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.compositeDiagnosisCodePointers.diagnosisCodePointers[0]'
			validate: [{
				name: 'ReverseParentPrefill'
			}
			]

	dx_id_2:
		model:
			source: 'patient_diagnosis'
			sourcefilter:
				id:
					'dynamic': '{dx_filter}'
				patient_id:
					'dynamic': '{patient_id}'
			if:
				'*':
					fields: ['dx_id_3']
		view:
			columns: 2
			label: 'Diagnosis 2'
			reference: 'SV107-02'
			_meta:
				location: '2400 SV1'
				field: '07-02'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.compositeDiagnosisCodePointers.diagnosisCodePointers[1]'

	dx_id_3:
		model:
			source: 'patient_diagnosis'
			sourcefilter:
				id:
					'dynamic': '{dx_filter}'
				patient_id:
					'dynamic': '{patient_id}'
			if:
				'*':
					fields: ['dx_id_4']
		view:
			columns: 2
			label: 'Diagnosis 3'
			reference: 'SV107-03'
			_meta:
				location: '2400 SV1'
				field: '07-03'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.compositeDiagnosisCodePointers.diagnosisCodePointers[2]'

	dx_id_4:
		model:
			source: 'patient_diagnosis'
			sourcefilter:
				id:
					'dynamic': '{dx_filter}'
				patient_id:
					'dynamic': '{patient_id}'
		view:
			columns: 2
			label: 'Diagnosis 4'
			reference: 'SV107-04'
			_meta:
				location: '2400 SV1'
				field: '07-04'
				path: 'claimInformation.serviceLines[{idx1-50}].professionalService.compositeDiagnosisCodePointers.diagnosisCodePointers[3]'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['inventory_id']
	indexes:
		many: [
			['inventory_id']
			['procedure_code']
		]
	sections:
		'Service':
			hide_header: true
			fields: ['charge_no', 'inventory_id', 'type', 
			'procedure_identifier', 'procedure_code', 'description',
			'service_unit_count', 'measurement_unit', 'line_item_charge_amount',
			'emergency_indicator', 'epsdt_indicator', 'copay_status_code', 'place_of_service_code'
			'modifier_1', 'modifier_2', 'modifier_3', 'modifier_4',
			'dx_filter', 'dx_id_1', 'dx_id_2', 'dx_id_3', 'dx_id_4']
view:
	dimensions:
		width: '75%'
		height: '65%'
	hide_cardmenu: true
	reference: '2400'
	comment: 'Service'
	grid:
		fields: ['inventory_id', 'service_unit_count', 'measurement_unit', 'line_item_charge_amount', 'modifier_1']
		width: [40, 15, 15, 15, 15]
		sort: ['-created_on']
	label: 'Service'
	open: 'read'