fields:

	firstname:
		model:
			search: 'A'
			max: 35
			min: 1
			required: true
		view:
			label: 'First Name'
			note: 'Letters, numbers, comma, \', -, . only'
			validate: [{
					name: 'UpdateTabLabel'
					fields: ['firstname', 'middlename', 'lastname']
			},
			{
				name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
			}
			]
			columns: 4

	lastname:
		model:
			search: 'B'
			max: 60
			min: 1
			required: true
		view:
			label: 'Last Name'
			note: 'Letters, numbers, \', -, . only'
			validate: [{
					name: 'UpdateTabLabel'
					fields: ['firstname', 'middlename', 'lastname']
			},
			{
				name: 'NameValidator'
			}
			]
			columns: 4

	middlename:
		model:
			search: 'C'
			max: 25
			min: 0
		view:
			label: 'Middle Name'
			note: 'Letters, numbers, \', -, . only'
			validate: [{
					name: 'UpdateTabLabel'
					fields: ['firstname', 'middlename', 'lastname']
			},
			{
				name: 'NameValidator'
			}
			]
			columns: 4

	site_id:
		model:
			required: true
			source: 'site'
			sourceid: 'id'
		view:
			label: 'Site'
			columns: 3

	team_id:
		model:
			source: 'list_team'
		view:
			label: 'Team'
			columns: 3

	referral_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Referral Date'
			template: '{{now}}'
			columns: 3

	referral_source_id:
		model:
			source: 'sales_account'
		view:
			label: 'Referral Source'
			columns: 3

	referrer_id:
		model:
			source: 'physician'
			type: 'int'
		view:
			label: 'Referring Physician'
			validate: [
					name: 'CareplanPhysicianOnboardingValidator'
			]
			columns: 3
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/physician/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'territory_id': ['territory_id']
			]

	territory_id:
		model:
			source: 'sales_territory'
		view:
			note: 'Set at the physician level'
			label: 'Sales Territory'
			columns: 3
			readonly: true

	dob:
		model:
			required: false
			type: 'date'
		view:
			columns: 3
			control: 'input'
			label: 'Date of Birth'
			note: 'Must be in MM/DD/YYYY format'
			validate: [{
					name: 'DOBValidator'
			},
			{
				name: 'PediatricCheck'
				age: 18
			}
			]

	gender:
		model:
			max: 8
			min: 1
			required: true
			source: ['Female', 'Male']
		view:
			control: 'radio'
			label: 'Sex at birth'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'pharm']
		request:    ['patient']
		update:     ['nurse', 'patient']
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	save: false
	name: 'Create Patient'
	sections:
		'Demographics':
			fields: ['firstname', 'lastname', 'middlename', 'site_id', 'team_id', 'referral_date', 'referral_source_id', 'referrer_id', 'territory_id', 'dob', 'gender']

view:
	dimensions:
		width: '80%'
		height: '75%'
	hide_cardmenu: true
	comment: 'Create Patient'
	grid:
		fields: ['lastname', 'firstname', 'dob']
		sort: ['lastname', 'firstname']
	icon: "person"
	label: 'View Create Patient'
	open: 'read'
