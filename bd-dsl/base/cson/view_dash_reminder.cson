fields:
	status:
		model:
			source: ['Open', 'In-Process', 'Follow-up', 'Done']
		view:
			findfilter: 'Open'
			label: 'Status'

	show_all:
		model:
			source: ['Myself', 'Everyone']
		view:
			findfilter: 'Myself'
			label: 'Show Cards For'

	assign_to:
		model:
			source: 'permission_group'
			type: 'int'
		view:
			label: 'Assigned To'

	patient_id:
		model:
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'

model:
	access:
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	name: '{patient_id} {status} assign_to: {assign_to}'
	save: false

view:
	comment: 'Setup > View > Dashboard > Reminder'
	find:
		basic: ['status', 'show_all', 'assign_to', 'patient_id']
	label: 'Find: Reminder'
