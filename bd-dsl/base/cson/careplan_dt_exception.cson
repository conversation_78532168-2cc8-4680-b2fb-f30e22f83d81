fields:

	description:
		model:
			required: false
			type: 'text'
		view:
			columns: 1
			label: 'Description'
			readonly: true

	dt_exception_id:
		model:
			source: 'list_dt_exception'
			sourceid: 'code'
			if:
				'INVMIS':
					fields: ['billed_inventory_id']
				'QTYMIS':
					fields: ['dispense_quantity', 'bill_quantity']
				'PYRWRG':
					fields: ['bill_insurance_id', 'expected_insurance_id']
				'BILLMIS':
					fields: ['inventory_id']
		view:
			columns: 2
			label: 'Exception'
			readonly: true

	exception_by:
		model:
			source: 'user'
		view:
			columns: 4
			label: 'Generated By'
			readonly: true

	exception_datetime:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Exception Datetime'
			readonly: true

	invoice_id:
		model:
			type: 'int'
		view:
			label: 'Invoice'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			source: 'inventory'
		view:
			form_link_enabled: true
			columns: -2
			label: 'Dispensed Inventory Item'
			readonly: true

	billed_inventory_id:
		model:
			source: 'inventory'
		view:
			form_link_enabled: true
			columns: 2
			label: 'Billed Inventory Item'
			readonly: true

	dispense_quantity:
		model:
			type: 'int'
		view:
			columns: 4
			label: 'Dispense Quantity'
			readonly: true

	bill_quantity:
		model:
			type: 'int'
		view:
			columns: 4
			label: 'Related Bill Quantity'
			readonly: true

	bill_insurance_id:
		model:
			source: 'patient_insurance'
		view:
			columns: 4
			label: 'Expected Payer'
			readonly: true

	expected_insurance_id:
		model:
			source: 'patient_insurance'
		view:
			columns: 4
			label: 'Expected Payer'
			readonly: true

	resolved:
		model:
			source: ['Yes']
		view:
			columns: -4
			label: 'Resolved'
			control: 'checkbox'
			class: 'checkbox-only'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						resolved: 'Yes'
					dest: 'resolved_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						resolved: 'Yes'
					dest: 'resolved_by'
				}
			]

	resolved_by:
		model:
			source: 'user'
		view:
			columns: 4
			label: 'Resolved By'
			readonly: true

	resolved_datetime:
		model:
			type: 'datetime'
		view:
			columns: 2
			label: 'Resolved Datetime'
			readonly: true

	comment:
		model:
			required: false
		view:
			label: 'Comments'

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['inventory_id']
			['billed_inventory_id']
			['bill_insurance_id']
			['expected_insurance_id']
			['dt_exception_id']
		]
	name: ['dt_exception_id']
	sections:
		'Delivery Ticket Exception':
			hide_header: true
			indent: false
			fields: ['description', 'dt_exception_id', 'exception_by', 'exception_datetime', 'invoice_id', 'inventory_id', 'billed_inventory_id', 'dispense_quantity',
			'bill_quantity', 'bill_insurance_id', 'expected_insurance_id', 'resolved', 'resolved_by', 'resolved_datetime', 'comment']

view:
	hide_cardmenu: true
	comment: 'Delivery Ticket Confirmation Exceptions'
	grid:
		fields: ['description', 'resolved', 'comment']
		width: [50, 15, 35]
		sort: ['-id']
	label: 'Delivery Ticket Confirmation Exceptions'
	open: 'edit'
	block:
		update:
			if: 'resolved'
			except: ['empty']