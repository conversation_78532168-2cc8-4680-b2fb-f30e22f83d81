fields:

	payer_name:
		view:
			columns: 3
			label: 'Payer Name'

	imn_payer_id:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'IMN Payer ID'

	legacy_payer_id:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Legacy CHC Payer ID'
			readonly: true

	rpa_eligibility_id:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'RPA Payer ID - Eligibility'
			readonly: true

	rpa_claim_status_id:
		model:
			type: 'text'
		view:
			class: 'status'
			columns: 3
			label: 'RPA Payer ID - Claim Status'

	eligibillity_status:
		model:
			type: 'text'
		view:
			class: 'status'
			columns: 3
			label: 'Status Eligibility'

	stand_in_indicator_eligibillity:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Stand-in Indicator Eligibility'

	enrollment_required_eligibillity:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Enrollment Req\'d Eligibility'

	status_claim:
		model:
			type: 'text'
		view:
			class: 'status'
			columns: 3
			label: 'Status Claim'

	stand_in_indicator_claim:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Stand-in Indicator Claim'

	enrollment_required_claim:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Enrollment Req\'d Claim'

	status_auth:
		model:
			type: 'text'
		view:
			class: 'status'
			columns: 3
			label: 'Status Auth'

	stand_in_indicator_auth:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Stand-in Indicator Auth'

	enrollment_required_auth:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Enrollment Req\'d Auth'

	status_referral:
		model:
			type: 'text'
		view:
			class: 'status'
			columns: 3
			label: 'Status Referral'

	stand_in_indicator_referral:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Stand-in Indicator Referral'

	enrollment_required_referral:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Enrollment Req\'d Referral'

	status_notification:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Status Notification'

	stand_in_indicator_notification:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Stand-in Indicator Notification'

	enrollment_required_notification:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Enrollment Req\'d Notification'

	service_restored:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Service Restored'

	active_date:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Active Date'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['payer_name']
	indexes:
		many: [
			['imn_payer_id']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['payer_name', 'imn_payer_id', 'legacy_payer_id', 'rpa_eligibility_id', 'rpa_claim_status_id', 'eligibillity_status', 'stand_in_indicator_eligibillity', 'enrollment_required_eligibillity', 'status_claim', 'stand_in_indicator_claim', 'enrollment_required_claim', 'status_auth', 'stand_in_indicator_auth', 'enrollment_required_auth', 'status_referral', 'stand_in_indicator_referral', 'enrollment_required_referral', 'status_notification', 'stand_in_indicator_notification', 'enrollment_required_notification', 'service_restored', 'active_date']

view:
	comment: 'Manage > List CHC Payer'
	find:
		basic: ['payer_name']
	grid:
		fields: ['payer_name', 'imn_payer_id', 'active_date']
	label: 'List CHC Payer'