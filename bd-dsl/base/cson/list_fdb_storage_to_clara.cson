fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#NDC_ATTRIBUTE_VALUE
	attribute_val:
		model:
			required: true
		view:
			label: 'Attribute Value'
			findunique: true
			columns: 2

	storage_id:
		model:
			required: true
			source: 'list_storage'
			sourceid: 'code'
		view:
			label: 'Storage'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['attribute_val', 'storage_id']
	indexes:
		many: [
			['attribute_val']
			['storage_id']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['attribute_val', 'storage_id']

view:
	comment: 'Manage > List FDB Storage to Clara Storage'
	find:
		basic: ['storage_id']
	grid:
		fields: ['attribute_val', 'storage_id']
	label: 'List FDB Storage to Clara Storage'
