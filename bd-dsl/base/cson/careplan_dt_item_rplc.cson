fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	rx_id:
		model:
			type: 'int'
		view:
			label: 'Rx Id'
			offscreen: true
			readonly: true

	processed:
		model:
			source: ['Yes']
			if:
				'Yes':
					readonly:
						fields: ['original_inventory_id', 'inventory_id', 'replacement_reason_id', 'comment']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Processed?'
			readonly: true
			offscreen: true

	original_inventory_id:
		model:
			required: true
			source: 'inventory'
		view:
			label: 'Original Item'
			columns: 2
			readonly: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
			query: 'rplc_item_qu'
			querytemplate: 'defaultTemplate'
			sourcefilter:
				rx_id:
					'dynamic': '{rx_id}'
		view:
			label: 'Replacement Item'
			columns: 2

	replacement_reason_id:
		model:
			required: true
			source: 'list_dt_replacement_rsn'
		view:
			label: 'Replacement Reason'
			columns: 4

	update_rx:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Update Prescription?'
			columns: 4

	comment:
		model:
			required: false
		view:
			label: 'Comment'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     []
		read:       ['admin','pharm','csr','cm', 'nurse']
		read_all:   ['admin','pharm','csr','cm', 'nurse']
		request:    []
		review:     ['admin','pharm','csr','cm', 'nurse']
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['original_inventory_id'],
			['inventory_id'],
			['replacement_reason_id']
		]
	name: ['original_inventory_id', 'replacement_reason_id']
	sections:
		'Delivery Ticket Replacement Log':
			hide_header: true
			indent: false
			fields: ['rx_id', 'original_inventory_id', 'inventory_id', 'replacement_reason_id', 'processed', 'update_rx', 'comment']

view:
	hide_cardmenu: true
	comment: 'Delivery Ticket Replacement Log'
	grid:
		fields: ['original_inventory_id', 'inventory_id', 'replacement_reason_id', 'processed', 'update_rx', 'comment']
		sort: ['-id']
	label: 'Drug Item'
	open: 'edit'