fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	diagnosis_a:
		model:
			required: true
			source: ['Mild (baseline FVIII >5%)', 'Moderate (baseline FVIII between 1-5%)',
			'Severe (baseline FVIII <1%)']
		view:
			control: 'radio'
			label: 'Hemophilia A (Factor VIII Deficiency)'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Hemophilia A Pre-Assessment':
			area: 'preassessment'
			fields: ['diagnosis_a']

view:
	comment: 'Patient > Careplan > Assessment > Hemophilia A'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Hemophilia A'
	open: 'read'
