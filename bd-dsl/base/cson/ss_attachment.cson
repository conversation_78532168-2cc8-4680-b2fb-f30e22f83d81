fields:

	#Body.AttachmentWithControlNumber.AttachmentControlNumber
	control_number:
		model:
			max: 64
		view:
			label: 'Control Number'
			readonly: true

	#Body.AttachmentWithControlNumber.AttachmentSource
	#Body.Attachment.AttachmentSource
	source:
		view:
			label: 'Source'
			readonly: true

	#Body.AttachmentWithControlNumber.MIMEType
	#Body.Attachment.MIMEType
	mime_type:
		model:
			max: 64
		view:
			label: 'MIME Type'
			readonly: true

	#Body.AttachmentWithControlNumber.AttachmentData
	#Body.Attachment.AttachmentData
	file:
		model:
			type: 'json'
		view:
			control: 'file'
			label: 'Document / File'
			note: 'Max 100MB. Only documents, images, and archives supported.'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: ['control_number', 'source', 'mime_type']
	sections:
		'File Attachment':
			fields: ['control_number', 'source', 'mime_type', 'file']

view:
	hide_cardmenu: true
	comment: 'Surescripts > File Attachments'
	grid:
		fields: ['control_number', 'source', 'mime_type']
		sort: ['-id']
	label: 'Surescripts File Attachment'
