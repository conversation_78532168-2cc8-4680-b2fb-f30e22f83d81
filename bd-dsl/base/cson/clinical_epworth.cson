fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_epworth.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	# Questionnaire
	pt_particiates:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'Yes'
			if:
				'Yes':
					sections: ['Epworth Sleepiness Scale (ESS) Questionnaire']
					fields: ['assessment_date']
			prefill: ['clinical_epworth']
		view:
			findfilter: 'Yes'
			control: 'radio'
			label: 'Will a Epworth Sleepiness Scale assessment be completed today?'

	reading:
		model:
			required: true
			multi:false
			source:
				3: 'High chance of dozing'
				2: 'Moderate chance of dozing'
				1: 'Slight chance of dozing'
				0: 'Would never doze'
		view:
			control: 'checkbox'
			label: 'Sitting and reading'
			validate: [
					name: 'EPWORTHScoreValidate'
			]

	watching_tv:
		model:
			required: true
			multi:false
			source:
				3: 'High chance of dozing'
				2: 'Moderate chance of dozing'
				1: 'Slight chance of dozing'
				0: 'Would never doze'
		view:
			control: 'checkbox'
			label: 'Watching TV'
			validate: [
					name: 'EPWORTHScoreValidate'
			]

	sitting_public:
		model:
			required: true
			multi:false
			source:
				3: 'High chance of dozing'
				2: 'Moderate chance of dozing'
				1: 'Slight chance of dozing'
				0: 'Would never doze'
		view:
			control: 'checkbox'
			label: 'Sitting, inactive in a public place (e.g. a theatre or a meeting)'
			validate: [
					name: 'EPWORTHScoreValidate'
			]

	passenger_car:
		model:
			required: true
			multi:false
			source:
				3: 'High chance of dozing'
				2: 'Moderate chance of dozing'
				1: 'Slight chance of dozing'
				0: 'Would never doze'
		view:
			control: 'checkbox'
			label: 'As a passenger in a car for an hour without a break'
			validate: [
					name: 'EPWORTHScoreValidate'
			]

	lying_down:
		model:
			required: true
			multi:false
			source:
				3: 'High chance of dozing'
				2: 'Moderate chance of dozing'
				1: 'Slight chance of dozing'
				0: 'Would never doze'
		view:
			control: 'checkbox'
			label: 'Lying down to rest in the afternoon when circumstances permit'
			validate: [
					name: 'EPWORTHScoreValidate'
			]

	sitting_talking:
		model:
			required: true
			multi:false
			source:
				3: 'High chance of dozing'
				2: 'Moderate chance of dozing'
				1: 'Slight chance of dozing'
				0: 'Would never doze'
		view:
			control: 'checkbox'
			label: 'Sitting and talking to someone'
			validate: [
					name: 'EPWORTHScoreValidate'
			]

	sitting_quietly:
		model:
			required: true
			multi:false
			source:
				3: 'High chance of dozing'
				2: 'Moderate chance of dozing'
				1: 'Slight chance of dozing'
				0: 'Would never doze'
		view:
			control: 'checkbox'
			label: 'Sitting quietly after a lunch without alcohol'
			validate: [
					name: 'EPWORTHScoreValidate'
			]

	in_car:
		model:
			required: true
			multi:false
			source:
				3: 'High chance of dozing'
				2: 'Moderate chance of dozing'
				1: 'Slight chance of dozing'
				0: 'Would never doze'
		view:
			control: 'checkbox'
			label: 'In a car, while stopped for a few minutes in the traffic'
			validate: [
					name: 'EPWORTHScoreValidate'
			]

	score:
		model:
			rounding: 0.1
			type: 'decimal'
			if:
				'<6':
					note: 'Lower Normal Daytime Sleepiness'
				'<11':
					note: 'Higher Normal Daytime Sleepiness'
				'<13':
					note: 'Mild Excessive Daytime Sleepines'
				'<16':
					note: 'Moderate Excessive Daytime Sleepiness'
				'>15':
					note: 'Severe Excessive Daytime Sleepiness'
		view:
			label: 'Score'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_epworth:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['careplan_id']
	sections:
		'Epworth Sleepiness Scale (ESS) Questionnaire':
			fields: ['last_assessment_date', 'assessment_date']
		'Questionnaire':
			note: 'How likely are you to doze off or fall asleep in the following situations, in contrast to feeling just tired?\n\nThis refers to your usual way of life in recent times.\nEven if you haven’t done some of these things recently try to work out how they would have affected you.\n\nUse the following scale to choose the most appropriate answer for each situation:'
			fields: ['reading', 'watching_tv', 'sitting_public',
			'passenger_car', 'lying_down', 'sitting_talking',
			'sitting_quietly', 'in_car', 'score']
			prefill: 'clinical_epworth'
view:
	hide_cardmenu: true
	find:
		basic: ['assessment_date']
	grid:
		fields: ['created_on', 'created_by', 'assessment_date', 'score']
		sort: ['-id']
	comment: 'Patient > Careplan > Clinical > Epworth Sleepiness Scale (ESS)'
	label: 'Epworth Sleepiness Scale (ESS)'
