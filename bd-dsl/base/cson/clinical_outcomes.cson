fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	followup_time:
		model:
			max: 3
			min: 1
			required: false
			source: ['24 hours post visit', '1 week prior', 'Other']
			if:
				'Other':
					fields: ['followup_time_other']
		view:
			columns: 3
			control: 'radio'
			label: 'Therapy Administration Follow-up Time'

	followup_time_other:
		model:
			required: false
		view:
			columns: 3
			label: 'Follow-up Time Other'

	feeling:
		model:
			max: 3
			min: 1
			required: false
			source: ['Poor', 'Fair', 'Good', 'Very Good', 'Excellent']
		view:
			columns: 3
			control: 'radio'
			label: 'How are you feeling today?'

	days_missed:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
			required: true
			if:
				'Yes':
					fields: ['days_missed_reason', 'days_missed_count']
		view:
			columns: 3
			control: 'radio'
			label: 'Have you missed any School/Work?'

	days_missed_reason:
		model:
			required: true
			source: ['Due to ADR', 'Due to treating condition', 'Due to secondary condition', 'Avoidable due to lack of medication/supplies/nursing']
		view:
			columns: 3
			control: 'radio'
			label: 'Days missed reason'

	days_missed_count:
		model:
			type: 'int'
			min: 1
			max: 365
			required: true
		view:
			columns: 3
			label: 'How many days?'

	unplanned_dr_visit:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
			required: true
			if:
				'Yes':
					fields: ['unplanned_dr_visit_reason', 'unplanned_dr_visit_date', 'unplanned_dr_visit_outcome', 'unplanned_dr_visit_details']
		view:
			columns: 3
			control: 'radio'
			label: 'Any unscheduled Physician visit?'

	unplanned_dr_visit_reason:
		model:
			required: true
			source: ['Due to ADR', 'Due to treating condition', 'Due to secondary condition', 'Avoidable due to lack of medication/supplies/nursing']
			if:
				'Due to treating condition':
					fields: ['unplanned_dr_visit_reason_event']
		view:
			columns: 3
			control: 'radio'
			label: 'Physician visit reason'

	unplanned_dr_visit_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 3
			label: 'Physician visit date'

	unplanned_dr_visit_reason_event:
		model:
			access:
				read: ['patient']
			multi: true
			source: ['Adverse Event - Infused Drug Related', 'Adverse Event - Equipment Related', 'Adverse Event - Access Device Infection', 'Adverse Event - Access Device Related - Other than Infection', 'Change in Eligibility', 'Insufficient response', 'Unknown Reason', 'Other']
			if:
				'Other':
					fields: ['unplanned_dr_visit_reason_event_other']
		view:
			columns: 3
			control: 'checkbox'
			note: 'Check all that apply'
			label: 'Physician visit related to therapy due to:'

	unplanned_dr_visit_reason_event_other:
		model:
			access:
				read: ['patient']
			required: true
		view:
			columns: 3
			label: 'Physician visit related to therapy due to (Other):'

	unplanned_dr_visit_outcome:
		model:
			access:
				read: ['patient']
			required: true
			source: ['Continuation of home infusion services with no interruption', 'Interruption of services, followed by resumption of care with therapy changes', 'Interruption of services, followed by resumption of care without therapy changes', 'Home Infusion services discontinued']
		view:
			columns: 3
			control: 'select'
			label: 'Outcome of Physician visit'

	unplanned_dr_visit_details:
		view:
			columns: 3
			label: 'Physician visit details'

	unplanned_er_visit:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
			required: true
			if:
				'Yes':
					fields: ['unplanned_er_visit_date', 'unplanned_er_visit_reason', 'unplanned_er_visit_outcome', 'unplanned_er_visit_details']
		view:
			columns: 3
			control: 'radio'
			label: 'Any ER visit?'

	unplanned_er_visit_reason:
		model:
			required: true
			max: 32
			source: ['ADR', 'Due to treating condition', 'Due to secondary condition']
			if:
				'Due to treating condition':
					fields: ['unplanned_er_visit_reason_event']
		view:
			columns: 3
			control: 'radio'
			label: 'Why did you visit the emergency room?'

	unplanned_er_visit_reason_event:
		model:
			multi: true
			source: ['Adverse Event - Infused Drug Related', 'Adverse Event - Equipment Related', 'Adverse Event - Access Device Infection', 'Adverse Event - Access Device Related - Other than Infection', 'Change in Eligibility', 'Insufficient response', 'Unknown Reason', 'Other']
			if:
				'Other':
					fields: ['unplanned_er_visit_reason_event_other']
		view:
			columns: 3
			control: 'checkbox'
			note: 'Check all that apply'
			label: 'Emergency room visit related to therapy due to:'

	unplanned_er_visit_reason_event_other:
		view:
			columns: 3
			label: 'Emergency room visit related to therapy due to (Other):'

	unplanned_er_visit_outcome:
		model:
			access:
				read: ['patient']
			required: true
			source: ['Continuation of home infusion services with no interruption', 'Interruption of services, followed by resumption of care with therapy changes', 'Interruption of services, followed by resumption of care without therapy changes', 'Home Infusion services discontinued']
		view:
			columns: 3
			control: 'select'
			label: 'Outcome of Emergency room visit'

	unplanned_er_visit_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 3
			label: 'ER visit date'

	unplanned_er_visit_details:
		view:
			columns: 3
			label: 'ER visit visit details'

	hospitalized:
		model:
			access:
				read: ['patient']
			required: true
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['hospitalized_date', 'hospitalized_reason', 'hospitalized_outcome']
		view:
			columns: 3
			control: 'radio'
			label: 'Were you hospitalized since the last visit?'

	hospitalized_date:
		model:
			access:
				read: ['patient']
			type: 'date'
			required: true
		view:
			columns: 3
			label: 'Hospitalization Date'

	hospitalized_where:
		view:
			label: 'Hospital Name and location'
			columns: 3

	hospitalized_phone:
		view:
			label: 'Hospital Phone Number'
			format: 'us_phone'
			columns: 3

	hospitalized_discharge:
		model:
			type: 'date'
		view:
			label: 'Discharge Date'
			columns: 3

	hospitalized_services:
		model:
			access:
				read: ['patient']
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hospitalized_services_note']
		view:
			control: 'radio'
			label: 'Any additional services required in the home after this hospitalization?'
			columns: 3

	hospitalized_services_note:
		model:
			required: true
		view:
			control: 'area'
			label: 'Details'

	hospitalized_reason:
		model:
			access:
				read: ['patient']
			max: 32
			required: true
			source: ['ADR', 'Due to treating condition', 'Due to secondary condition', 'Avoidable due to lack of medication/supplies/nursing']
			if:
				'Due to treating condition':
					fields: ['hospitalized_reason_event']
		view:
			columns: 3
			control: 'radio'
			label: 'Why were you admitted to the hospital?'

	hospitalized_reason_event:
		model:
			access:
				read: ['patient']
			multi: true
			source: ['Adverse Event - Infused Drug Related', 'Adverse Event - Equipment Related', 'Adverse Event - Access Device Infection', 'Adverse Event - Access Device Related - Other than Infection', 'Change in Eligibility', 'Insufficient response', 'Unknown Reason', 'Other']
			if:
				'Other':
					fields: ['hospitalized_reason_event_other']
		view:
			columns: 3
			control: 'checkbox'
			note: 'Check all that apply'
			label: 'Hospital admission related to therapy due to:'

	hospitalized_reason_event_other:
		model:
			access:
				read: ['patient']
		view:
			columns: 3
			label: 'Hospital admission related to therapy due to (Other):'

	hospitalized_outcome:
		model:
			access:
				read: ['patient']
			required: true
			source: ['Resumption of home infusion services with therapy changes', 'Resumption of home infusion services without therapy changes', 'Home infusion services discontinued', 'Pending/remains hospitalized at time of reporting']
		view:
			columns: 3
			control: 'select'
			label: 'Outcome of hospitalization'

	provider_visit:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'No':
					note: 'Ask patient if they need help scheduling a visit'
				'Yes':
					fields: ['provider_visit_date']
		view:
			columns: 3
			label: 'Have you been seen by your healthcare provider recently?'

	provider_visit_date:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Last healthcare provider visit date:'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_tag:
		model:
			type: 'text'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_outcomes:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id']
	sections:
		'Clinical Therapy Outcomes':
			hide_header: true
			indent: false
			fields: ['followup_time', 'followup_time_other', 'feeling', 'days_missed',
			'days_missed_reason', 'days_missed_count', 'unplanned_dr_visit',
			'unplanned_dr_visit_reason', 'unplanned_dr_visit_reason_event',
			'unplanned_dr_visit_reason_event_other', 'unplanned_dr_visit_date',
			'unplanned_dr_visit_outcome', 'unplanned_dr_visit_details',
			'unplanned_er_visit', 'unplanned_er_visit_date', 'unplanned_er_visit_reason',
			'unplanned_er_visit_reason_event', 'unplanned_er_visit_reason_event_other',
			'unplanned_er_visit_outcome', 'unplanned_er_visit_details', 'hospitalized',
			'hospitalized_date', 'hospitalized_where', 'hospitalized_phone', 'hospitalized_discharge', 'hospitalized_services', 'hospitalized_services_note', 'hospitalized_reason', 'hospitalized_reason_event',
			'hospitalized_reason_event_other', 'hospitalized_outcome', 'provider_visit',
			'provider_visit_date']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Clinical Outcomes'
	label: 'Clinical Outcomes'
