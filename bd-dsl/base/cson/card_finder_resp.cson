fields:

	patient_id:
		model:
			type: 'int'
		view:
			label: 'Patient ID'
			readonly: true
			offscreen: true

	embed_insurances:
		model:
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				form: 'card_finder_insurances'
				selectable: true
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['payer_id', 'bin', 'pcn', 'cardholder_id']
				label: ['Payer', 'BIN', 'PCN', 'Cardholder ID']
				width: [38, 31, 31, 31]
			label: 'Insurances Found'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'pharm']

	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['patient_id']
			tab: 'Details'
		'Insurances':
			hide_header: false
			indent: false
			fields: ['embed_insurances']
			tab: 'Results'
	name: '{patient_id}'

view:
	dimensions:
		width: '80%'
		height: '55%'
	hide_cardmenu: true
	comment: 'Patient > Card Finder'
	find:
		basic: ['auto_name']
	grid:
		fields: ['auto_name']
	label: 'Patient Card Finder'
