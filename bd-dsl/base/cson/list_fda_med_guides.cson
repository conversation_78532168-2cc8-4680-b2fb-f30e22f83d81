fields:

	# Drug Name
	drug_name:
		view:
			label: 'Drug Name'
			findunique: true
			readonly: true
			columns: 3

	#Active Ingredient
	active_ingredient:
		view:
			label: 'Active Ingredient'
			readonly: true
			columns: 3

	#Form;Route
	form_route:
		view:
			label: 'Form;Route'
			readonly: true
			columns: 3

	#Appl. No.
	application_no:
		view:
			label: 'Application No'
			readonly: true
			columns: 3

	#Company
	company:
		view:
			label: 'Company'
			readonly: true
			columns: 3

	#Date
	date:
		model:
			type: 'date'
		view:
			label: 'Date'
			readonly: true
			columns: 3

	link:
		view:
			label: 'Link'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['drug_name', 'application_no']
	indexes:
		many: [
			['application_no']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['drug_name', 'active_ingredient', 'form_route', 'application_no',
			'company', 'date', 'link']

view:
	comment: 'Manage > List FDA Medication Guides'
	find:
		basic: ['drug_name', 'application_no']
	grid:
		fields: ['drug_name', 'active_ingredient', 'form_route', 'application_no', 'date', 'link']
	label: 'List FDA Medication Guides'
