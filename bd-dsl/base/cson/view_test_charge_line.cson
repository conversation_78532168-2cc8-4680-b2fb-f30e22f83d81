fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			readonly: true
			offscreen: true

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
			prefill: ['parent.site_id']
		view:
			columns: 2
			label: 'Site'
			readonly: true
			offscreen: true

	claim_no:
		model:
			required: true
			type: 'text'
		view:
			label: 'Claim No'
			offscreen: true
			readonly: true

	insurance_id:
		model:
			source: 'patient_insurance'
			required: true
		view:
			control: 'select'
			label: 'Insurance'
			readonly: true
			offscreen: true

	payer_id:
		model:
			source: 'payer'
			required: true
		view:
			label: 'Payer'
			readonly: true
			offscreen: true

	charge_line:
		model:
			multi: true
			sourcefilter:
				claim_no:
					'dynamic': '{claim_no}'
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				form: 'test_charge_line'
				selectable: false
				add_preset:
					claim_no: '{claim_no}'
					site_id: '{site_id}'
					insurance_id: '{insurance_id}'
					payer_id: '{payer_id}'
					billing_method_id: 'ncpdp'
					patient_id: '{patient_id}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				label: ['Item', 'Quantity', 'Exp $', 'Gross Amt $', 'NDC']
				fields: ['inventory_id', 'bill_quantity', 'expected', 'gross_amount_due', 'formatted_ndc']
				width: [45, 10, 15, 15, 15]
			label: 'Charge Lines'
			note: 'Max 25'
			max_count: 25
			transform: [
				{
					name: 'RefreshClaim'
				}
			]
model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: []
	save: false
	name: 'Test Claim Charge Lines'
	sections:
		'Record Info':
			hide_header: true
			indent: false
			fields: ['claim_no', 'insurance_id', 'payer_id', 'patient_id', 'site_id']
		'Charge Line':
			hide_header: true
			indent: false
			note: '<i>Associated test claim charge lines. Updates here will automatically apply to the test claim.</i>'
			fields: ['charge_line']

view:
	hide_header: true
	hide_cardmenu: true
	comment: 'Manage > Test Claim Charge Lines'
	label: 'Test Claim Charge Lines'
	open: 'edit'
