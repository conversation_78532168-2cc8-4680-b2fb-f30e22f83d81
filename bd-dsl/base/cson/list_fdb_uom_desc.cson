#TABLE: RPEIUT0_UOM_TYPE_DESC
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#UOM_TYPE_CD
	uom_type_cd:
		model:
			type: 'int'
		view:
			label: 'Unit of Measure Type Code'
			readonly: true
			columns: 2

	#UOM_TYPE_CD_DESC
	uom_type_cd_desc:
		view:
			label: 'Unit of Measure Type Code Description'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{uom_type_cd_desc}"
	indexes:
		many: [
			['uom_type_cd']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['uom_type_cd', 'uom_type_cd_desc']

view:
	comment: 'Manage > List FDB Unit of Measure Type Description Table'
	find:
		basic: ['uom_type_cd', 'uom_type_cd_desc']
	grid:
		fields: ['uom_type_cd', 'uom_type_cd_desc']
	label: 'List FDB Unit of Measure Type Description Table'
