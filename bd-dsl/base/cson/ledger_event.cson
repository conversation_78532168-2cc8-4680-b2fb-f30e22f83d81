fields:
	event_name:
		view:
			label: 'Event Name'

	old_value:
		view:
			label: 'Old Value'

	new_value:
		view:
			label: 'New Value'

	entity:
		view:
			label: 'Subscriber'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['audit']
	name: ['entity','event_name']
	sections:
		'Main':
			fields: ['event_name', 'entity','old_value','new_value']

view:
	hide_cardmenu: true
	comment: 'Manage > Ledger Event'
	find:
		basic: ['entity','event_name']
	grid:
		fields: ['entity','event_name']
		sort: ['entity']
	label: 'Ledger Event'
