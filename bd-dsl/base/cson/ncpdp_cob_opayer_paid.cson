fields:
	paid_qualifier:
		model:
			default: '07'
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '342-HC'
		view:
			columns: 2
			reference: '342-HC'
			note: '342-HC'
			label: 'Paid Amount Qualifier'

	other_payer_amount_paid:
		model:
			required: true
			type: 'decimal'
			rounding: 0.01
			max: 99999999.99
		view:
			columns: 2
			reference: '431-DV'
			note: '431-DV'
			label: 'Paid Amount'
			class: 'numeral money'
			format: '$0,0.00'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['paid_qualifier', 'other_payer_amount_paid']
	sections:
		'Other Payer Paid':
			hide_header: true
			indent: false
			fields: ['paid_qualifier', 'other_payer_amount_paid']

view:
	dimensions:
		width: '50%'
		height: '25%'
	hide_cardmenu: true
	comment: 'Other Payer Paid'
	grid:
		fields: ['paid_qualifier', 'other_payer_amount_paid']
		sort: ['-created_on']
	label: 'Other Payer Paid'
	open: 'read'