fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Assessment Questions
	psoriasis_loc:
		model:
			max: 128
			multi: true
			source:['Hands','Feet','Face','Scalp','Groin','Nails','Other']
			if:
				'Other':
					fields: ['psoriasis_loc_other']
		view:
			control: 'checkbox'
			note: 'Select any that apply'
			label: 'Location of psoriasis'

	psoriasis_loc_other:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Location of psoriasis other'

	psoriasis_severe:
		model:
			max: 128
			source:['Mild (up to 3% BSA)','Moderate (3-10% BSA)','Severe (greater than 10% BSA)']
			if:
				'Severe (greater than 10% BSA)':
					fields: ['psoriasis_bsa']
		view:
			control: 'radio'
			note: 'Select any that apply'
			label: 'Psoriasis Severity'

	psoriasis_bsa:
		model:
			min: 1
			max: 100
			type: 'int'
		view:
			label: 'BSA (%)'

	palm_cover_cnt:
		model:
			type: 'int'
			min: 1
			max: 100
		view:
			label: 'If you had to take the palm of your hand and cover up all of the patches of psoriasis on your body today, how many palms of your hand do you think that it would take?'
			note: 'One palm of your hand is equal to about 1% of your body surface area (BSA). If your psoriasis is only scattered small dots, try to imagine combining them together into one patch. Please remember to include your scalp and back if affected. Do not include areas in which psoriasis has faded, leaving only changes in the color of the skin.'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Psoriasis General Assessment':
			fields: ['psoriasis_loc', 'psoriasis_loc_other', 'psoriasis_severe', 'psoriasis_bsa', 'palm_cover_cnt']
view:
	comment: 'Patient > Careplan > Assessment > Psoriasis'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Assessment Questionnaire: Psoriasis'
	open: 'read'
