fields:

	name:
		model:
			required: true
		view:
			label: 'Template Name'

	code:
		model:
			required: true
			access:
				write: ['-admin']
		view:
			offscreen: false
			readonly: false
			label: 'Template Code'

	type:
		model:
			search: 'B'
			required: true
			source: [ 'Chart', 'Label', 'Page', 'Page Repeating']
		view:
			control: 'radio'
			label: 'Report Type'


	is_page_less:
		model:
			default: 'No'
			required: true
			source: [ 'Yes', 'No']
			if:
				'No':
					fields: ['page_width', 'page_height']
		view:
			control: 'radio'
			label: 'Is Report type Page less?'


	page_width:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Page Width(In)'

	page_height:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'Page Height(In)'

	json_data:
		model:
			required: false
			type: 'json'
		view:
			offscreen: false
			readonly: false
			label: 'Design Defination '

	module:
		model:
			type: 'int'
			source: 'module'
			required: true
		view:
			label: 'Module'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: []
		write:      ['admin']
	bundle: ['setup']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['type']
		]
	name: '{name}'
	sections:
		'Main':
			fields: ['name', 'code','type', 'is_page_less', 'module' ,'page_width', 'page_height', 'allow_sync', 'active']

view:
	comment: 'Report Template'
	grid:
		fields: ['name', 'type', 'code','page_width', 'page_height']
		sort: ['name', 'type']
	label: 'Report Template'
	open: 'read'
