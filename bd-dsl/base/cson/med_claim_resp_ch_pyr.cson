fields:

	payer_id:
		view:
			columns: 2
			label: 'Payer ID'
			readonly: true

	payer_name:
		view:
			columns: 2
			label: 'Payer Name'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['payer_id', 'payer_name']
	sections:
		'Payer':
			hide_header: true
			fields: ['payer_id', 'payer_name']

view:
	dimensions:
		width: '55%'
		height: '55%'
	hide_cardmenu: true
	comment: 'Med Claim Response Payer'
	grid:
		fields: ['payer_id', 'payer_name']
		sort: ['-created_on']
	label: 'Med Claim Response Payer'
	open: 'read'