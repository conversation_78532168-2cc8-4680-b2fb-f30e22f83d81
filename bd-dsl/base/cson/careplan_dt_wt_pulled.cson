fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			readonly: true
			offscreen: true

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	ticket_no:
		model:
			type: 'text'
		view:
			label: 'Ticket #'
			readonly: true
			offscreen: true

	ticket_item_no:
		model:
			type: 'text'
		view:
			label: 'Ticket Item #'
			readonly: true
			offscreen: true

	site_id:
		model:
			prefill: ['parent.site_id']
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site Id'
			readonly: true
			offscreen: true

	status:
		model:
			source:
				delivery_ticket: 'Delivery Ticket Creation' # This is a new delivery ticket, not associated with a claim
				ready_to_fill: 'Ready to Fill' # This is a delivery ticket that is ready to be filled and prescription has been verified or started from a refill request
				order_ver: 'Pending Order Verification' # This is a delivery ticket that has been filled and is await pharmacist verification
				pending_conf: 'Pending Confirmation' # This is a delivery ticket that has been filled and is await pharmacist confirmation
				ready_to_bill: 'Ready to Bill' # This is a delivery ticket that has been confirmed by the pharmacist and is ready to be billed
				billed: 'Billed' # This is a delivery ticket that has been billed for all charges on the ticket
				voided: 'Voided' # This is a delivery ticket that has been voided
			if:
				'order_ver':
					fields: ['item_verified']
				'pending_conf':
					fields: ['item_verified']
					readonly:
						fields: ['inventory_id', 'lot_id', 'expiration_date', 'serial_id', 'dispensed_quantity', 'void']
				'ready_to_bill':
					fields: ['item_verified']
					readonly:
						fields: ['inventory_id', 'lot_id', 'expiration_date', 'serial_id', 'dispensed_quantity', 'void']
				'billed':
					fields: ['item_verified']
					readonly:
						fields: ['inventory_id', 'lot_id', 'expiration_date', 'serial_id', 'dispensed_quantity', 'void']
				'voided':
					fields: ['item_verified']
					readonly:
						fields: ['inventory_id', 'lot_id', 'expiration_date', 'serial_id', 'dispensed_quantity', 'void']
		view:
			label: 'Status'
			readonly: true
			offscreen: true

	item_pulled_from_stock:
		model: 
			source: ['Yes']
			if:
				'Yes':
					readonly:
						fields: ['inventory_id', 'lot_id', 'serial_id', 'lot_no', 'serial_no', 'dispensed_quantity']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Item Pulled from Stock?'
			readonly: true
			offscreen: true

	item_verified:
		model: 
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Item Verified?'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
		view:
			columns: 2
			label: 'Inventory Item'
			readonly: true
			validate: [{
				name: 'LoadInventoryData'
			}]

	inventory_type:
		model:
			source: ['Drug', 'Supply', 'Equipment Rental', 'Billable']
		view:
			label: 'Inventory Type'
			readonly: true
			offscreen: true

	raw_barcode:
		model:
			max: 40
		view:
			columns: 4
			label: 'Item Barcode'
			note: 'GS1/GTIN, UPC, or internal'
			readonly: true
			offscreen: true

	lot_tracking:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['lot_id', 'lot_no']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Track Lots?'
			readonly: true
			offscreen: true
			validate: [{
				name: 'AddAvailableLots'
			}]

	serial_tracking:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['serial_id', 'serial_no']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Track Serial Numbers?'
			readonly: true
			offscreen: true
			validate: [{
				name: 'AddAvailableSerials'
			}]

	flt_lot:
		model:
			required: false
			source: 'inventory_lot'
			multi: true
		view:
			label: 'Available Lots'
			readonly: true
			offscreen: true

	lot_id:
		model:
			required: true
			source: 'inventory_lot'
			query: 'select_lot_in_stock'
			querytemplate: 'inventoryTemplate'
			sourcefilter:
				site_id:
					'dynamic': '{site_id}'
				inventory_id:
					'dynamic': '{inventory_id}'
				id:
					'dynamic': '{flt_lot}'
		view:
			columns: -2
			label: 'Lot'
			validate: [{
				name: 'LoadLotData'
			}]

	lot_no:
		model:
			required: true
		view:
			columns: 4
			label: 'Lot Number'
			readonly: true

	expiration_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Expiration Date'
			readonly: true
			validate: [
				name: 'DateValidator'
				require: 'future'
			]

	flt_ser:
		model:
			required: false
			source: 'inventory_serial'
			multi: true
		view:
			label: 'Available Serials'
			readonly: true
			offscreen: true

	serial_id:
		model:
			required: true
			source: 'inventory_serial'
			sourcefilter:
				id:
					'dynamic': '{flt_ser}'
				site_id:
					'dynamic': '{site_id}'
				inventory_id:
					'dynamic': '{inventory_id}'
			query: 'select_serial_in_stock'
			querytemplate: 'inventoryTemplate'
		view:
			columns: -2
			label: 'Serial'
			validate: [{
				name: 'LoadSerialData'
			}]

	serial_no:
		model:
			required: true
		view:
			columns: 4
			label: 'Serial Number'
			readonly: true

	dispensed_quantity:
		model:
			required: true
			type: 'decimal'
			max: 9999999.999
			rounding: 1
			min: 1
		view:
			columns: -4
			label: 'Quantity'
			class: 'numeral'
			format: '0,0.[000000]'
			validate: [
				{
					name: "CompareValidator"
					fields: [
						"dispensed_quantity"
						"quantity_on_hand"
					]
					require: "lte"
					error: "Quantity cannot be more than Quantity On Hand"
				},
				{
					name: "CompareValidator"
					fields: [
						"dispensed_quantity"
						"quantity_needed"
					]
					require: "lte"
					error: "Quantity cannot be more than Quantity Needed"
				}
			]

	dispensed_unit_id:
		model:
			required: true
			source: 'list_unit'
			sourceid: 'code'
		view:
			label: 'Unit'
			readonly: true
			offscreen: true

	quantity_needed:
		model:
			prefill: ['parent.quantity_needed']
			required: true
			type: 'decimal'
			rounding: 0.001
		view:
			columns: 4
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Remaining Quantity'
			readonly: true

	quantity_on_hand:
		model:
			required: true
			type: 'decimal'
			rounding: 0.001
		view:
			columns: 4
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Quantity On Hand'
			readonly: true
			validate: [{
				name: 'AutoFillQuantityPulled'
			}]

	void:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['voided_datetime', 'void_reason_id']
					readonly:
						fields: ['inventory_id', 'lot_id', 'expiration_date', 'serial_id', 'dispensed_quantity']
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Void Pulled Item?'
			findfilter: '!Yes'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						void: 'Yes'
					dest: 'voided_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						void: 'Yes'
					dest: 'voided_by'
				}
			]

	void_reason_id:
		model:
			required: true
			source: 'list_void_reason'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			label: 'Void Reason'

	voided_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Voided Date'
			readonly: true

	voided_by:
		model:
			source: 'user'
		view:
			label: 'Voided By'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
	indexes:
		many: [
			['ticket_no']
			['ticket_item_no']
			['site_id']
			['inventory_id']
			['inventory_type']
			['serial_id']
			['serial_no']
			['lot_id']
			['lot_no']
		]
	reportable: true

	name: ['inventory_id', 'lot_id', 'serial_id', 'dispensed_quantity']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Pulled Work Ticket Item':
			hide_header: true
			indent: false
			fields: ['ticket_no','ticket_item_no', 'patient_id', 'site_id', 'status', 'item_verified',
			'inventory_id', 'inventory_type', 'raw_barcode', 'lot_tracking', 'serial_tracking', 'flt_lot','lot_id', 'lot_no',
			'expiration_date', 'flt_ser', 'serial_id', 'serial_no', 'dispensed_quantity', 'dispensed_unit_id', 'quantity_on_hand', 'quantity_needed']
		'Void':
			hide_header: true
			indent: false
			modal: true
			fields: ['void', 'voided_datetime', 'voided_by', 'void_reason_id']
view:
	dimensions:
		width: '65%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Patient > Pulled Work Ticket Item'
	grid:
		fields: ['inventory_id', 'lot_id', 'serial_id', 'dispensed_quantity', 'dispensed_unit_id']
		sort: ['-created_on']
	label: 'Pulled Work Ticket Item'
	open: 'read'
	block:
		update:
			if: 'void'
			except: ['empty']