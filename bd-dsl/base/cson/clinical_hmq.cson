fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_hmq.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	# Survey Participation

	overall_effectiveness_occur:
		model:
			required: true

			max: 32
			source: ['Very Satisfied', 'Somewhat Satisfied', 'Neutral', 'Somewhat Dissatisfied', 'Very Dissatisfied', 'Does Not Apply to Me']
		view:
			control: 'radio'
			label: 'The overall effectiveness of treatment you currently use when headache attacks occur?'

	overall_effectiveness_prevent:
		model:
			required: true

			max: 32
			source: ['Very Satisfied', 'Somewhat Satisfied', 'Neutral', 'Somewhat Dissatisfied', 'Very Dissatisfied', 'Does Not Apply to Me']
		view:
			control: 'radio'
			label: 'The overall effectiveness of treatment you currently use to prevent headache attacks from occuring?'

	overall_effectiveness_frequency:
		model:
			required: true

			max: 32
			source: ['Very Satisfied', 'Somewhat Satisfied', 'Neutral', 'Somewhat Dissatisfied', 'Very Dissatisfied']
		view:
			control: 'radio'
			label: 'The overall effectiveness of your current treatment on frequency of your headache symptoms?'

	overall_effectiveness_severity:
		model:
			required: true

			max: 32
			source: ['Very Satisfied', 'Somewhat Satisfied', 'Neutral', 'Somewhat Dissatisfied', 'Very Dissatisfied']
		view:
			control: 'radio'
			label: 'The overall effectiveness of your current treatment on the severity of your headache symptoms?'

	ability_self_manage:
		model:
			required: true

			max: 32
			source: ['Very Satisfied', 'Somewhat Satisfied', 'Neutral', 'Somewhat Dissatisfied', 'Very Dissatisfied']
		view:
			control: 'radio'
			label: 'Your ability to self-manage headache symptoms?'

	avoid_conditions:
		model:
			required: true

			max: 32
			source: ['Very Satisfied', 'Somewhat Satisfied', 'Neutral', 'Somewhat Dissatisfied', 'Very Dissatisfied']
		view:
			control: 'radio'
			label: 'Your ability to avoid conditions that may cause headache symptoms to occur?'

	treatment_cost:
		model:
			required: true

			max: 32
			source: ['Very Satisfied', 'Somewhat Satisfied', 'Neutral', 'Somewhat Dissatisfied', 'Very Dissatisfied', 'Does Not Apply to Me']
		view:
			control: 'radio'
			label: 'The amount of money you spend on headache symptom treatments?'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_hmq:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Headache Management Questionnaire Participation':
			fields: ['last_assessment_date', 'assessment_date']
		'Headache Management Questionnaire':
			note: 'Please rate each of the following seven questions by select the most appropriate answer'
			fields: ['overall_effectiveness_occur', 'overall_effectiveness_prevent',
			'overall_effectiveness_frequency', 'overall_effectiveness_severity',
			'ability_self_manage', 'avoid_conditions', 'treatment_cost']
			prefill: 'clinical_hmq'

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Headache Management Questionnaire'
	find:
		basic: ['assessment_date']
	grid:
		fields: ['created_on', 'assessment_date', 'created_by']
		sort: ['-id']
	label: 'Headache Management Questionnaire'
