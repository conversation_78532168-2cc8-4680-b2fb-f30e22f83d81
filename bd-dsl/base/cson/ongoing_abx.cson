fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	taking_probiotic:
		model:
			prefill: ['ongoing_antibiotic']
			required: false
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you currently taking a probiotic or eating yogurt on a regular basis?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		ongoing_antibiotic:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Antibiotic Followup':
			note: 'Ask the patient the following questions'
			fields: ['taking_probiotic']

view:
	comment: 'Patient > Careplan > Ongoing > Antibiotic'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Ongoing Assessment: Antibiotic'
	open: 'read'
