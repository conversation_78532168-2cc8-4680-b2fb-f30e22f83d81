fields:

	show_reprice_loop:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['claim_pricing_repricing_information']
					sections: ['Repricing']
		view:
			label: 'Show repricing loop?'
			note: 'Specific to payer settings'
			offscreen: true
			readonly: true

	site_id:
		model:
			prefill: ['parent.site_id']
			type: 'int'
			source: 'site'
		view:
			label: 'Site'
			readonly: true
			offscreen: true

	patient_id:
		model:
			type: 'int'
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	insurance_id:
		model:
			prefill: ['parent.insurance_id']
			type: 'int'
			source: 'patient_insurance'
		view:
			label: 'Insurance'
			readonly: true
			offscreen: true

	payer_id:
		model:
			type: 'int'
			source: 'payer'
			prefill: ['parent.payer_id']
		view:
			columns: 3
			label: 'Payer'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/payer/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'claim_filing_code': ['mm_claim_filing_indicator_code'],
					'place_of_service_code': ['mm_default_service_place_id']
			]
			readonly: true
			offscreen: true

	patient_control_number:
		model:
			required: true
		view:
			columns: -4
			label: 'Patient Control Number'
			note: 'UUID linking patient to claim'
			readonly: true
			reference: 'CLM01'
			_meta:
				location: '2300 CLM'
				field: '01'
				path: 'claimInformation.patientControlNumber'

	gender:
		model:
			prefill: ['patient.gender']
			source: ['Female', 'Male']
			if:
				'Female':
					fields: ['pregnancy_indicator']
		view:
			label: 'Gender'
			offscreen: true
			readonly: true

	claim_filing_code:
		model:
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR09'
		view:
			columns: 2
			label: 'Insurance Type'
			reference: 'SBR09'
			_meta:
				location: '2000B SBR'
				field: '09'
				path: 'claimInformation.claimFilingCode'

	place_of_service_code:
		model:
			required: true
			min: 1
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CLM05-01'
		view:
			columns: 2
			label: 'Place of Service Code'
			reference: 'CLM05-01'
			_meta:
				location: '2300 CLM'
				field: '05-01'
				path: 'claimInformation.placeOfServiceCode'

	plan_participation_code:
		model:
			default: 'A'
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CLM07'
		view:
			columns: 2
			label: 'Plan Participate Code'
			reference: 'CLM07'
			_meta:
				location: '2300 CLM'
				field: '07'
				path: 'claimInformation.planParticipationCode'

	special_program_code:
		model:
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CLM12'
		view:
			label: 'Special Program Code'
			reference: 'CLM12'
			_meta:
				location: '2300 CLM'
				field: '12'
				path: 'claimInformation.specialProgramCode'
			readonly: true
			offscreen: true

	claim_charge_amount:
		model:
			max: **********.99
			min: 0.00
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			columns: -4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Charge Amt'
			note: 'Aggregated from Service Lines'
			reference: 'CLM02'
			_meta:
				location: '2300 CLM'
				field: '02'
				path: 'claimInformation.claimChargeAmount'

	patient_amount_paid:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Patient Paid Amt'
			reference: 'ATM02'
			_meta:
				location: '2300 AMT'
				field: '02'
				path: 'claimInformation.patientAmountPaid'

	claim_frequency_code:
		model:
			default: '1'
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CLM05-03'
		view:
			columns: 2
			label: 'Claim Frequency Code'
			reference: 'CLM05-03'
			_meta:
				location: '2300 CLM'
				field: '05-03'
				path: 'claimInformation.claimFrequencyCode'

	signature_indicator:
		model:
			default: 'Y'
			source:
				'Y': 'Yes'
			required: true
		view:
			columns: 4
			control: 'checkbox'
			label: 'Prov Sig on File?'
			class: 'checkbox-only'
			reference: 'CLM06'
			_meta:
				location: '2300 CLM'
				field: '06'
				path: 'claimInformation.signatureIndicator'

	patient_signature_source_code:
		model:
			default: 'P'
			source:
				'P': 'Yes'
		view:
			columns: 4
			control: 'checkbox'
			label: 'Pt Sig Gen by Pharm?'
			class: 'checkbox-only'
			reference: 'CLM10'
			_meta:
				location: '2300 CLM'
				field: '10'
				path: 'claimInformation.patientSignatureSourceCode'

	homebound_indicator:
		model:
			source:
				'Y': 'Yes'
		view:
			columns: 4
			control: 'checkbox'
			label: 'Pt is Homebound?'
			class: 'checkbox-only'
			reference: 'CRC02'
			_meta:
				location: '2300 CRC'
				code: '07'
				field: '02'
				path: 'claimInformation.homeboundIndicator'

	release_information_code:
		model:
			default: 'Y'
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CLM09'
		view:
			columns: 2
			label: 'Release of Info Obtained?'
			reference: 'CLM09'
			_meta:
				location: '2300 CLM'
				field: '09'
				path: 'claimInformation.releaseInformationCode'

	benefits_assignment_certification_indicator:
		model:
			default: 'Y'
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CLM08'
		view:
			columns: 2
			label: 'Benefits Assignment Cert Indicator'
			reference: 'CLM08'
			_meta:
				location: '2300 CLM'
				field: '08'
				path: 'claimInformation.benefitsAssignmentCertificationIndicator'

	pregnancy_indicator:
		model:
			source:
				'Y': 'Yes'
		view:
			columns: 4
			control: 'checkbox'
			label: 'Pregnancy Indicator'
			class: 'checkbox-only'
			reference: 'PAT09'
			_meta:
				location: ['2000B PAT', '2000C PAT']
				field: '09'
				path: 'dependent.pregnancyIndicator'

	delay_reason_code:
		model:
			min: 1
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CLM20'
		view:
			columns: 2
			label: 'Delay Reason Code'
			reference: 'CLM20'
			_meta:
				location: '2300 CLM'
				field: '20'
				path: 'claimInformation.delayReasonCode'

	related_cause_code:
		model:
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CLM11'
			if:
				'AA':
					fields: ['auto_accident_state_code']
		view:
			label: 'Related Cause Code?'
			reference: 'CLM11-01'
			_meta:
				location: '2300 CLM'
				field: '11-01'
				path: 'claimInformation.relatedCauseCode'
			offscreen: true
			readonly: true

	auto_accident_state_code:
		model:
			required: false
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			reference: 'CLM11-04'
			columns: 'addr_state'
			label: 'Auto Accident Occured In State'
			_meta:
				location: '2300 CLM'
				field: '11-04'
				path: 'claimInformation.autoAccidentStateCode'
			offscreen: true
			readonly: true

	death_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Death Date'
			note: 'Must be in MM/DD/YYYY format'
			reference: 'PAT06'
			_meta:
				location: ['2000B PAT', '2000C PAT']
				field: '06'
				path: 'claimInformation.deathDate'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	patient_weight:
		model:
			max: 1000
			min: 1
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'unit'
			format: '0,0.[000000]'
			label: 'Weight (Kg)'
			note: 'E.g.: 78, 78kg, 172lbs'
			reference: 'PAT08'
			_meta:
				location: ['2000B PAT', '2000C PAT']
				code: '01'
				field: '08'
				path: 'claimInformation.patientWeight'
			transform: [
					name: 'WeightTransform'
			]

	health_care_code_information:
		model:
			multi: true
			source: 'med_claim_dx'
			type: 'subform'
			required: true
		view:
			note: 'Max 12'
			label: 'Diagnosis'
			grid:
				add: 'inline'
				edit: true
				fields: ['dx_id','diagnosis_code']
				label: ['Diagnosis', 'Code']
				width: [80, 20]
			max_count: 12

	claim_pricing_repricing_information:
		model:
			multi: false
			source: 'med_claim_reprice'
			type: 'subform'
		view:
			label: 'Pricing/Repricing'
			note: 'Automatically populated from payer response'
			reference: '2300'
			offscreen: true
			readonly: true

	service_facility_location:
		model:
			multi: false
			source: 'med_claim_facility'
			type: 'subform'
		view:
			label: 'Facility'
			grid:
				add: 'inline'
				edit: true
				fields: ['organization_name', 'phone_name', 'phone_number']
				label: ['Org Name', 'Name', 'Phone']
				width: [50, 25, 25]
			max_count: 1
			offscreen: true
			readonly: true

	other_subscriber_information:
		model:
			multi: true
			source: 'med_claim_osub'
			type: 'subform'
		view:
			grid:
				add: 'flyout'
				edit: true
				fields: ['cob_insurance_id', 'payment_responsibility_level_code', 'payer_paid_amount', 'non_covered_charge_amount', 'remaining_patient_liability']
				label: ['Payer', 'Resp Level', 'Paid', 'Non-Cov', 'Remaining']
				width: [35, 20, 15, 15, 15]
			note: 'Max 10'
			label: 'Other Subscriber'
			max_count: 10

	service_lines:
		model:
			multi: true
			required: true
			source: 'med_claim_sl'
			type: 'subform'
		view:
			note: 'Max 50'
			label: 'Service Line'
			grid:
				add: 'none'
				hide_cardmenu: true
				edit: true
				fields: ['assigned_number', 'inventory_id',  'service_unit_count','measurement_unit']
				label: ['#', 'Item', 'Quantity', 'Unit']
				width: [10, 55, 15, 20]
			max_count: 50

	claim_note:
		model:
			multi: false
			source: 'med_claim_note'
			type: 'subform'
		view:
			label: 'Note'

	claim_supplemental_information:
		model:
			multi: false
			source: 'med_claim_supplemental'
			type: 'subform'
		view:
			label: 'Supplemental'

	claim_info_other:
		model:
			multi: false
			type: 'subform'
			source: 'med_claim_info_other'
		view:
			label: 'Claim Info Other'
model:
	prefill:
		patient:
			link:
				id: 'patient_id'
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
			['insurance_id']
			['payer_id']
			['site_id']
		]

	name: ['patient_control_number', 'claim_filing_code']
	sections_group: [
		'Claim':
			hide_header: true
			sections: [
				'Details':
					hide_header: true
					tab: 'Details'
					fields: ['show_reprice_loop', 'site_id', 'patient_id', 'insurance_id',
					'payer_id', 'patient_control_number', 'patient_weight', 'claim_filing_code', 'delay_reason_code',
					'place_of_service_code', 'plan_participation_code', 'claim_frequency_code',
					'special_program_code', 'claim_charge_amount', 'patient_amount_paid',
					'signature_indicator', 'patient_signature_source_code', 'release_information_code',
					'benefits_assignment_certification_indicator', 'gender', 'related_cause_code',
					'auto_accident_state_code', 'death_date', 'homebound_indicator', 'pregnancy_indicator']
				'Diagnosis':
					hide_header: true
					indent: false
					tab: 'Details'
					fields: ['health_care_code_information']
				'Service Lines':
					hide_header: true
					indent: false
					tab: 'Service Lines'
					fields: ['service_lines']
				'Service Location':
					hide_header: true
					indent: false
					tab_toggle: true
					tab: 'Service Location'
					fields: ['service_facility_location']
				'Supplemental':
					hide_header: true
					indent: false
					tab: 'Supplemental'
					tab_toggle: true
					fields: ['claim_supplemental_information']
				'Other Subscriber Information':
					hide_header: true
					indent: false
					tab: 'COB'
					fields: ['other_subscriber_information']
				'Repricing':
					hide_header: true
					indent: false
					tab: 'COB'
					tab_toggle: true
					fields: ['claim_pricing_repricing_information']
				'Notes':
					hide_header: true
					indent: false
					tab: 'Notes'
					tab_toggle: true
					fields: ['claim_note']
				'Other':
					hide_header: true
					indent: false
					tab: 'Other'
					tab_toggle: true
					fields: ['claim_info_other']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '85%'
	hide_cardmenu: true
	reference: '2300'
	comment: 'Claim Info'
	hide_header: true
	grid:
		fields: ['claim_filing_code', 'claim_frequency_code', 'claim_charge_amount', 'patient_amount_paid']
		sort: ['-created_on']
	label: 'Claim info'
	open: 'read'