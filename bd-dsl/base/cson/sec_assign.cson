fields:
	type:
		model:
			source: 
				user_role: 'User Role',
				user_rule: 'User Rule',
				group_role: 'Group Role',
				group_rule: 'Group Rule',
				user_group: 'User Group',
				role_rule: 'Role Rule',
				user_dsl: 'User DSL',
				group_dsl: 'Group Dsl',
				role_dsl: 'Role Dsl'
			required: true
			if:
				'user_group':
					fields: ['user_id', 'sec_group', 'start_date', 'end_date']
				'user_role':
					fields: ['user_id', 'role', 'start_date', 'end_date']
				'user_rule':
					fields: ['user_id', 'rule']
				'group_role':
					fields: ['sec_group', 'role']
				'group_rule':
					fields: ['sec_group', 'rule']
				'role_rule':
					fields: ['role', 'rule']
		view:
			label: 'Type'
			readonly: true

	user_id:
		model:
			source: 'user'
		view:
			label: 'User Name'

	sec_group:
		model:
			source: 'sec_group'
		view:
			label: 'Group'

	role:
		model:
			source: 'sec_role'
		view:
			label: 'Role'

	rule:
		model:
			source: 'sec_rule'
		view:
			label: 'Rule'

	dsl:
		model:
			type: 'json'
		view:
			label: 'DSL Access'
			control: 'area'

	start_date:
		model:
			type: 'date'
		view:
			label: 'Start Date'

	end_date:
		model:
			type: 'date'
		view:
			label: 'End Date'

	site:
		model:
			source: 'site'
		view:
			label: 'Site'

	agency:
		model:
			source: 'ancillary_provider'
		view:
			label: 'Agency'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['setup']
	sections:
		'Security Assign':
			fields: ['type', 'user_id', 'sec_group', 'role', 'rule', 'start_date', 'end_date', 'allow_sync', 'active']
	name: '{user_id}'
	sync_mode: 'mixed'

view:
	hide_cardmenu: true
	comment: 'Manage > Security Assign'
	find:
		basic: ['rule']
	grid:
		fields: ['created_on', 'created_by', 'rule']
		sort: ['-created_on']
	label: 'Security Assign'
