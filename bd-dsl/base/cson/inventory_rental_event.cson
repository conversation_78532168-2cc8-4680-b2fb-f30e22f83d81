fields:

	rental_id:
		view:
			columns: 4
			label: 'Rental Log'
			readonly: true
			offscreen: true

	event_datetime:
		model:
			type: 'datetime'
			required: true
		view:
			columns: 2
			label: 'Date/Time'
			template: '{{now}}'

	type:
		model:
			multi: false
			required: true
			source: ['Check', 'Clean', 'Preventative Maintenance', 'Repair', 'Test/Diagnostics']
			if:
				'Check':
					sections: ['Check']
					fields: ['next_check_date']
				'Preventative Maintenance':
					sections: ['Preventative Maintenance']
					fields: ['high_down_occ', 'low_down_occ', 'up_occ',
					'door_sensor', 'air_in_sensor', 'aux_alarm',
					'clean_inspect_dam', 'vol_delivery', 'biomed_menu', 'next_pm_date']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Event Type'

	high_down_occ:
		model:
			required: true
			source: ['Pass', 'Fail']
			if:
				'Fail':
					require_fields: ['note']
		view:
			control: 'radio'
			label: 'High Down-stream Occlusion'
			columns: 4

	low_down_occ:
		model:
			required: true
			source: ['Pass', 'Fail']
			if:
				'Fail':
					require_fields: ['note']
		view:
			control: 'radio'
			label: 'Low Down-stream Occlusion'
			columns: 4

	up_occ:
		model:
			required: true
			source: ['Pass', 'Fail']
			if:
				'Fail':
					require_fields: ['note']
		view:
			control: 'radio'
			label: 'Up-stream Occlusion'
			columns: 4

	door_sensor:
		model:
			required: true
			source: ['Pass', 'Fail']
			if:
				'Fail':
					require_fields: ['note']
		view:
			control: 'radio'
			label: 'Door Sensor'
			columns: 4

	air_in_sensor:
		model:
			required: true
			source: ['Pass', 'Fail']
			if:
				'Fail':
					require_fields: ['note']
		view:
			control: 'radio'
			label: 'Air in Line Sensor'
			columns: 4

	aux_alarm:
		model:
			required: true
			source: ['Pass', 'Fail']
			if:
				'Fail':
					require_fields: ['note']
		view:
			control: 'radio'
			label: 'Auxiliary Alarm'
			columns: 4

	clean_inspect_dam:
		model:
			required: true
			source: ['Pass', 'Fail']
			if:
				'Fail':
					require_fields: ['note']
		view:
			control: 'radio'
			label: 'Clean/Inspect Pump Damage'
			columns: 4

	vol_delivery:
		model:
			required: true
			source: ['Pass', 'Fail']
			if:
				'Fail':
					require_fields: ['note']
		view:
			control: 'radio'
			label: 'Volumetric Delivery'
			columns: 4

	biomed_menu:
		model:
			required: true
			source: ['Pass', 'Fail']
			if:
				'Fail':
					require_fields: ['note']
		view:
			control: 'radio'
			label: 'Biomed Menu Verified'
			columns: 4

	next_pm_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Next PM Date'
			columns: 4

	next_check_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Next Check Date'
			columns: 4

	note:
		model:
			type: 'text'
		view:
			control: 'area'
			label: 'Notes'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'pharm']
		write:      ['admin', 'pharm']
	name: ['event_datetime', 'type']
	sections:
		'Device Event Log':
			fields: ['rental_id', 'event_datetime', 'type']
		'Preventative Maintenance':
			fields: ['high_down_occ', 'low_down_occ', 'up_occ', 'door_sensor',
			'air_in_sensor', 'aux_alarm', 'clean_inspect_dam', 'vol_delivery', 'biomed_menu',
			'next_pm_date']
		'Check':
			fields: ['next_check_date']
		'Notes':
			fields: ['note']
view:
	hide_cardmenu: true
	comment: 'Device Event Log'
	grid:
		fields: ['event_datetime', 'type', 'note', 'next_pm_date', 'next_check_date']
		sort: ['-event_datetime']
	label: 'Device Event Log'
	open: 'read'
