fields:

	other_coverage_type:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '338-5C'
		view:
			columns: 4
			reference: '338-5C'
			note: '338-5C'
			label: 'Coverage Type'
			readonly: true

	other_id_qualifier:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '339-6C'
		view:
			columns: -4
			reference: '339-6C'
			note: '339-6C'
			label: 'ID Qualifier'

	other_id:
		model:
			max: 10
		view:
			columns: 4
			reference: '340-7C'
			note: '340-7C'
			label: 'BIN'
			readonly: true

	other_pcn:
		view:
			columns: 4
			label: 'PCN'
			note: '991-MH'
			readonly: true

	other_cardholder_id:
		view:
			columns: 4
			label: 'Cardholder #'
			note: '356-NU'
			readonly: true

	other_group_no:
		view:
			columns: 4
			label: 'Group #'
			note: '992-MJ'
			readonly: true

	other_person_code:
		view:
			columns: 4
			label: 'Person Code'
			note: '142-UV'
			readonly: true

	other_help_phone:
		view:
			columns: 4
			note: '127-UB'
			format: 'us_phone'
			label: 'Help Desk Phone #'
			readonly: true

	other_rel_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '143-UW'
		view:
			columns: 2
			note: '143-UW'
			label: 'Pt Rel Code'
			readonly: true

	ben_eff_date:
		model:
			type: 'date'
		view:
			columns: 4
			note: '144-UX'
			label: 'Benefit Eff Date'
			readonly: true

	ben_trm_date:
		model:
			type: 'date'
		view:
			columns: 4
			note: '145-UY'
			label: 'Benefit Term Date'
			readonly: true


model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['other_id']
			['other_pcn']
			['other_cardholder_id']
		]

	name: 'Other Payer'
	sections:
		'Other Payer':
			hide_header: true
			indent: false
			fields: ['other_coverage_type', 'other_id_qualifier', 'other_id', 'other_pcn', 'other_cardholder_id',
			'other_group_no', 'other_person_code', 'other_help_phone', 'other_rel_code',
			'ben_eff_date', 'ben_trm_date']

view:
	dimensions:
		width: '75%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Other Payer'
	grid:
		fields: ['other_coverage_type', 'other_id', 'other_pcn', 'other_cardholder_id', 'other_id']
		width: [20, 20, 20, 20, 20]
		sort: ['-created_on']
	label: 'Other Payer'
	open: 'read'