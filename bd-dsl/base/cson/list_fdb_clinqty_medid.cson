#TABLE RCQMED0_CLNQTY_MEDID
fields:
    code:
        model:
            max: 64
            required: true
        view:
            label: 'Code'

    #MEDID
    medid:
        view:
            label: 'MEDID'
            findunique: true
            readonly: true
            columns: 3

    #MEDID_SN
    medid_sn:
        model:
            type: 'int'
        view:
            label: 'MEDID Serial Number'
            findunique: true
            readonly: true
            columns: 3

    #CLNQTY_SUBUNIT_QTY
    clnqty_subunit_qty:
        model:
            type: 'int'
            rounding: 0.00000000000000001
        view:
            label: 'Clinical Quantity Sub-Unit Quantity'
            columns: 3

    clnqty_subuom_desc:
        model:
            type: 'text'
        view:
            label: 'Clinical Quantity Sub-Unit Description'
            columns: 3

    clnqty_pkg_desc:
        model:
            type: 'text'
        view:
            label: 'Clinical Quantity Package Description'
            columns: 3

    clnqty_desc:
        model:
            type: 'text'
        view:
            label: 'Clinical Quantity Description'
            columns: 3

    erx_qty:
        model:
            type: 'int'
            rounding: 0.00000000000000001
        view:
            label: 'ERX Quantity'
            columns: 3

    erx_script_uom_desc:
        model:
            type: 'text'
        view:
            label: 'ERX NCPDP SCRIPT Quantity Qualifier Description'
            columns: 3

    erx_script_potunit_cd:
        model:
            type: 'text'
        view:
            label: 'ERX NCPDP SCRIPT Quantity Qualifier Code'
            columns: 3

model:
    access:
        create:     []
        create_all: ['admin']
        delete:     ['admin']
        read:       ['admin']
        read_all:   ['admin']
        request:    []
        update:     []
        update_all: ['admin']
        write:      ['admin']
    bundle: ['reference']
    sync_mode: 'full'
    name: "{medid} - {medid_sn} - {clnqty_desc}"
    indexes:
        many: [
            ['medid']
            ['medid_sn']
            ['erx_script_potunit_cd']
        ]
    sections:
        'Details':
            hide_header: true
            indent: false
            fields: ['medid', 'medid_sn', 'clnqty_subunit_qty',
            'clnqty_subuom_desc', 'clnqty_pkg_desc', 'clnqty_desc',
            'erx_qty', 'erx_script_uom_desc', 'erx_script_potunit_cd']

view:
    comment: 'Manage > List FDB Clinical Quantity MEDID'
    find:
        basic: ['medid', 'medid_sn']
    grid:
        fields: ['medid', 'medid_sn', 'clnqty_desc', 'erx_qty', 'erx_script_uom_desc']
    label: 'List FDB Clinical Quantity MEDID'
