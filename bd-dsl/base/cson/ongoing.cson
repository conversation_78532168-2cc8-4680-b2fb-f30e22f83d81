fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			required: false
			source: 'careplan_order'
			type: 'int'
		view:
			label: 'Order'
			readonly: true
			offscreen: true
			columns: 3
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/careplan_order/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'requires_nursing': ['requires_nursing'],
			]

	order_no:
		view:
			label: 'Order #'
			readonly: true
			columns: 3

	requires_nursing:
		model:
			multi: false
			source: ['Yes']
			if:
				'Yes':
					sections: ['Nursing Notes']
					fields: ['nursing_notes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Requires nursing?'
			readonly: true
			offscreen: true

	order_item_id:
		model:
			multi: true
			source: 'careplan_order_rx'
			sourcefilter:
				order_no:
					'dynamic': '{order_no}'
				parent_form:
					'static': 'careplan_order'
		view:
			label: 'Prescriptions'
			columns: 3

	route_id:
		model:
			required: false
			source: 'list_route'
			sourceid: 'code'
			if:
				'IV':
					sections: ['Catheter Log']
					fields: ['catheter_log']
				'PEG':
					sections: ['Catheter Log']
					fields: ['catheter_log']
				'IVP':
					sections: ['Catheter Log']
					fields: ['catheter_log']
		view:
			readonly: true
			offscreen: true
			label: 'Primary Drug Route'

	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Primary Therapy'

	therapy_2:
		model:
			source: 'list_therapy'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Therapy'

	therapy_3:
		model:
			source: 'list_therapy'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Therapy'

	therapy_4:
		model:
			source: 'list_therapy'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Therapy'

	therapy_5:
		model:
			source: 'list_therapy'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Therapy'

	subform_therapy_1:
		model:
			source: 'ongoing_{therapy_1}'
			sourcefilter:
				'ongoing_factor': {}
				'ongoing_aat': {}
				'ongoing_ig': {}
				'ongoing_subqig': {}
				'ongoing_tnf': {}
				'ongoing_tpn': {}
				'ongoing_steroid': {}
				'ongoing_inotrope': {}
				'ongoing_chemotherapy': {}
				'ongoing_bisphosphonates': {}
			type: 'subform'
		view:
			label: 'Primary Therapy Assessment'

	subform_therapy_2:
		model:
			source: 'ongoing_{therapy_2}'
			sourcefilter:
				'ongoing_factor': {}
				'ongoing_aat': {}
				'ongoing_ig': {}
				'ongoing_subqig': {}
				'ongoing_tnf': {}
				'ongoing_tpn': {}
				'ongoing_steroid': {}
				'ongoing_inotrope': {}
				'ongoing_chemotherapy': {}
				'ongoing_bisphosphonates': {}
			type: 'subform'
		view:
			label: 'Secondary Therapy Assessment'

	subform_therapy_3:
		model:
			source: 'ongoing_{therapy_3}'
			sourcefilter:
				'ongoing_factor': {}
				'ongoing_aat': {}
				'ongoing_ig': {}
				'ongoing_subqig': {}
				'ongoing_tnf': {}
				'ongoing_tpn': {}
				'ongoing_steroid': {}
				'ongoing_inotrope': {}
				'ongoing_chemotherapy': {}
				'ongoing_bisphosphonates': {}
			type: 'subform'
		view:
			label: 'Tertiary Therapy Assessment'

	subform_therapy_4:
		model:
			source: 'ongoing_{therapy_4}'
			sourcefilter:
				'ongoing_factor': {}
				'ongoing_aat': {}
				'ongoing_ig': {}
				'ongoing_subqig': {}
				'ongoing_tnf': {}
				'ongoing_tpn': {}
				'ongoing_steroid': {}
				'ongoing_inotrope': {}
				'ongoing_chemotherapy': {}
				'ongoing_bisphosphonates': {}
			type: 'subform'
		view:
			label: 'Quaternary Therapy Assessment'

	subform_therapy_5:
		model:
			source: 'ongoing_{therapy_5}'
			sourcefilter:
				'ongoing_factor': {}
				'ongoing_aat': {}
				'ongoing_ig': {}
				'ongoing_subqig': {}
				'ongoing_tnf': {}
				'ongoing_tpn': {}
				'ongoing_steroid': {}
				'ongoing_inotrope': {}
				'ongoing_chemotherapy': {}
				'ongoing_bisphosphonates': {}
			type: 'subform'
		view:
			label: 'Quinary Therapy Assessment'

	brand_1:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Primary Brand'

	brand_2:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Brand'

	brand_3:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Brand'

	brand_4:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Brand'

	brand_5:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Brand'

	subform_brand_1:
		model:
			source: 'ongoing_{brand_1}'
			sourcefilter:
				'ongoing_cimzia': {}
				'ongoing_dupixent': {}
				'ongoing_enbrel': {}
				'ongoing_humira': {}
				'ongoing_krystexxa': {}
				'ongoing_lemtrada': {}
				'ongoing_ocrevus': {}
				'ongoing_orencia': {}
				'ongoing_radicava': {}
				'ongoing_remicade': {}
				'ongoing_rituxan': {}
				'ongoing_simponi': {}
				'ongoing_simponiaria': {}
				'ongoing_soliris': {}
				'ongoing_stelara': {}
				'ongoing_tysabri': {}
				'ongoing_vyvgart': {}
				'ongoing_vancomycin': {}
				'ongoing_methyl': {}
			type: 'subform'
		view:
			label: 'Primary Drug Brand Assessment'

	subform_brand_2:
		model:
			source: 'ongoing_{brand_2}'
			sourcefilter:
				'ongoing_cimzia': {}
				'ongoing_dupixent': {}
				'ongoing_enbrel': {}
				'ongoing_humira': {}
				'ongoing_krystexxa': {}
				'ongoing_lemtrada': {}
				'ongoing_ocrevus': {}
				'ongoing_orencia': {}
				'ongoing_radicava': {}
				'ongoing_remicade': {}
				'ongoing_rituxan': {}
				'ongoing_simponi': {}
				'ongoing_simponiaria': {}
				'ongoing_soliris': {}
				'ongoing_stelara': {}
				'ongoing_tysabri': {}
				'ongoing_vyvgart': {}
				'ongoing_vancomycin': {}
				'ongoing_methyl': {}
			type: 'subform'
		view:
			label: 'Secondary Drug Brand Assessment'

	subform_brand_3:
		model:
			source: 'ongoing_{brand_3}'
			sourcefilter:
				'ongoing_cimzia': {}
				'ongoing_dupixent': {}
				'ongoing_enbrel': {}
				'ongoing_humira': {}
				'ongoing_krystexxa': {}
				'ongoing_lemtrada': {}
				'ongoing_ocrevus': {}
				'ongoing_orencia': {}
				'ongoing_radicava': {}
				'ongoing_remicade': {}
				'ongoing_rituxan': {}
				'ongoing_simponi': {}
				'ongoing_simponiaria': {}
				'ongoing_soliris': {}
				'ongoing_stelara': {}
				'ongoing_tysabri': {}
				'ongoing_vyvgart': {}
				'ongoing_vancomycin': {}
				'ongoing_methyl': {}
			type: 'subform'
		view:
			label: 'Tertiary Drug Brand Assessment'

	subform_brand_4:
		model:
			source: 'ongoing_{brand_4}'
			sourcefilter:
				'ongoing_cimzia': {}
				'ongoing_dupixent': {}
				'ongoing_enbrel': {}
				'ongoing_humira': {}
				'ongoing_krystexxa': {}
				'ongoing_lemtrada': {}
				'ongoing_ocrevus': {}
				'ongoing_orencia': {}
				'ongoing_radicava': {}
				'ongoing_remicade': {}
				'ongoing_rituxan': {}
				'ongoing_simponi': {}
				'ongoing_simponiaria': {}
				'ongoing_soliris': {}
				'ongoing_stelara': {}
				'ongoing_tysabri': {}
				'ongoing_vyvgart': {}
				'ongoing_vancomycin': {}
				'ongoing_methyl': {}
			type: 'subform'
		view:
			label: 'Quaternary Drug Brand Assessment'

	subform_brand_5:
		model:
			source: 'ongoing_{brand_5}'
			sourcefilter:
				'ongoing_cimzia': {}
				'ongoing_dupixent': {}
				'ongoing_enbrel': {}
				'ongoing_humira': {}
				'ongoing_krystexxa': {}
				'ongoing_lemtrada': {}
				'ongoing_ocrevus': {}
				'ongoing_orencia': {}
				'ongoing_radicava': {}
				'ongoing_remicade': {}
				'ongoing_rituxan': {}
				'ongoing_simponi': {}
				'ongoing_simponiaria': {}
				'ongoing_soliris': {}
				'ongoing_stelara': {}
				'ongoing_tysabri': {}
				'ongoing_vyvgart': {}
				'ongoing_vancomycin': {}
				'ongoing_methyl': {}
			type: 'subform'
		view:
			label: 'Quinary Drug Brand Assessment'

	disease_1:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Primary Disease'

	disease_2:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Disease'

	disease_3:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Disease'

	disease_4:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Disease'

	disease_5:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Disease'

	subform_disease_1:
		model:
			source: 'ongoing_{disease_1}'
			sourcefilter:
				'ongoing_asthma': {}
				'ongoing_hiv': {}
				'ongoing_hepc': {}
				'ongoing_hepb': {}
				'ongoing_hemob': {}
				'ongoing_hemoa': {}
				'ongoing_ra': {}
				'ongoing_psoriasis': {}
				'ongoing_ms': {}
				'ongoing_vwd': {}
			type: 'subform'
		view:
			label: 'Primary Disease Assessment'

	subform_disease_2:
		model:
			source: 'ongoing_{disease_2}'
			sourcefilter:
				'ongoing_asthma': {}
				'ongoing_hiv': {}
				'ongoing_hepc': {}
				'ongoing_hepb': {}
				'ongoing_hemob': {}
				'ongoing_hemoa': {}
				'ongoing_ra': {}
				'ongoing_psoriasis': {}
				'ongoing_ms': {}
				'ongoing_vwd': {}
			type: 'subform'
		view:
			label: 'Secondary Disease Assessment'

	subform_disease_3:
		model:
			source: 'ongoing_{disease_3}'
			sourcefilter:
				'ongoing_asthma': {}
				'ongoing_hiv': {}
				'ongoing_hepc': {}
				'ongoing_hepb': {}
				'ongoing_hemob': {}
				'ongoing_hemoa': {}
				'ongoing_ra': {}
				'ongoing_psoriasis': {}
				'ongoing_ms': {}
				'ongoing_vwd': {}
			type: 'subform'
		view:
			label: 'Tertiary Disease Assessment'

	subform_disease_4:
		model:
			source: 'ongoing_{disease_4}'
			sourcefilter:
				'ongoing_asthma': {}
				'ongoing_hiv': {}
				'ongoing_hepc': {}
				'ongoing_hepb': {}
				'ongoing_hemob': {}
				'ongoing_hemoa': {}
				'ongoing_ra': {}
				'ongoing_psoriasis': {}
				'ongoing_ms': {}
				'ongoing_vwd': {}
			type: 'subform'
		view:
			label: 'Quaternary Disease Assessment'

	subform_disease_5:
		model:
			source: 'ongoing_{disease_5}'
			sourcefilter:
				'ongoing_asthma': {}
				'ongoing_hiv': {}
				'ongoing_hepc': {}
				'ongoing_hepb': {}
				'ongoing_hemob': {}
				'ongoing_hemoa': {}
				'ongoing_ra': {}
				'ongoing_psoriasis': {}
				'ongoing_ms': {}
				'ongoing_vwd': {}
			type: 'subform'
		view:
			label: 'Quinary Disease Assessment'

	clinical_1:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['subform_clinical_1']
					sections: ['Primary Clinical Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Clinical Assessment'

	clinical_2:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['subform_clinical_2']
					sections: ['Secondary Clinical Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Clinical Assessment'

	clinical_3:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['subform_clinical_3']
					sections: ['Tertiary Clinical Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Clinical Assessment'

	clinical_4:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['subform_clinical_4']
					sections: ['Quaternary Clinical Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Clinical Assessment'

	clinical_5:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['subform_clinical_5']
					sections: ['Quinary Clinical Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Clinical Assessment'

	subform_clinical_1:
		model:
			source: 'clinical_{clinical_1}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
			type: 'subform'
		view:
			label: 'Primary Clinical Assessment'

	subform_clinical_2:
		model:
			source: 'clinical_{clinical_2}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
			type: 'subform'
		view:
			label: 'Secondary Clinical Assessment'

	subform_clinical_3:
		model:
			source: 'clinical_{clinical_3}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
			type: 'subform'
		view:
			label: 'Tertiary Clinical Assessment'

	subform_clinical_4:
		model:
			source: 'clinical_{clinical_4}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
			type: 'subform'
		view:
			label: 'Quaternary Clinical Assessment'

	subform_clinical_5:
		model:
			source: 'clinical_{clinical_5}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
			type: 'subform'
		view:
			label: 'Quinary Clinical Assessment'

	patient_medications:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': 'Active'
		view:
			embed:
				form: 'patient_medication'
				selectable: false
				add_preset:
					reported_by: 'Patient Reported'
					status: 'Active'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['fdb_id', 'medication_dose', 'medication_frequency', 'start_date']
				label: ['Medication', 'Dose', 'Frequency', 'Start Dt']
				width: [35, 25, 25, 15]
			label: 'Active Medications'

	patient_interactions:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			readonly: true
			embed:
				form: 'interactions'
				selectable: false
			grid:
				add: 'none'
				hide_cardmenu: true
				edit:true
				rank: 'none'
				fields: ['created_by', 'has_da', 'has_dd']
				label: ['Created By','Drug Allergy', 'Drug Drug']
				width: [30, 30, 30]
			label: 'Interactions'

	patient_interaction_btn:
		model:
			source: ['Get Interactions']
		view:
			columns: 2
			class: 'dsl-button'
			control: 'checkbox'
			label: 'Patient Interaction'
			validate: [
				name: 'DrugAllergyInteraction'
			]

	catheter_log:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				form: 'patient_catheter'
				selectable: false
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				rank: 'none'
				fields: ['date_placed', 'date_discontinued', 'device', 'type_id']
				label: ['Date Placed', 'Date Discontinued', 'Device', 'Type']
				width: [15, 15, 35, 35]
			label: 'Catheter Log'

	patient_allergies:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				end_date:
					'dynamic': ''
		view:
			embed:
				form: 'patient_allergy'
				selectable: false
				add_preset:
					status: 'Active'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				rank: 'none'
				fields: ['allergen_id', 'reaction_id', 'severity_id', 'start_date']
				label: ['Allergen', 'Reaction', 'Severity', 'Start Dt']
				width: [35, 25, 25, 15]
			label: 'Active Allergies'

	nursing_notes:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				order_id:
					'dynamic': '{order_id}'
		view:
			embed:
				form: 'encounter'
				selectable: false
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['created_by', 'contact_date', 'contact_reason', 'pharm_notes']
				label: ['Created By', 'Contact Date', 'Contact Reason', 'Pharmacy Notes']
				width: [20, 20, 20, 40]
			label: 'Nursing Notes'
			readonly: true

	measurement_log:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				form: 'patient_measurement_log'
				selectable: false
			grid:
				add: 'inline'
				rank: 'none'
				edit: true
				fields: ['created_by', 'date', 'height', 'weight']
				width: [40, 20, 20, 20]
			label: 'Measurement Log'

	new_meds:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Add new medications in DUR tab and run DUR checks'
		view:
			control: 'radio'
			label: 'Any change in medication profile?'
			columns: 3

	new_allergies_check:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					note: 'Add new allergies in DUR tab and run DUR checks'
		view:
			control: 'radio'
			label: 'Do you have any new allergies?'
			columns: 3

	symptoms_filter:
		model:
			multi: true
			source: 'list_symptom'
			if:
				'*':
					fields: ['symptoms']
					sections: ['Active Symptoms Review']
		view:
			label: 'Symptoms'
			readonly: true
			offscreen: true

	symptoms:
		model:
			prefill: ['ongoing.symptoms']
			multi: true
			required: false
			sourcefilter:
				id:
					'dynamic': '{symptoms_filter}'
				active:
					'static': 'Yes'
		view:
			embed:
				form: 'list_symptom'
				selectable: true
			grid:
				add: 'none'
				rank: 'none'
				edit: false
				fields: ['name']
				label: ['Symptom']
				width: [100]
			label: 'Active Symptoms'
			note: 'Select all that apply'

	experiencing_pain:
		model:
			required: true 
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pain_scale']
		view:
			columns: 3
			label: 'Are you experiencing any pain?'
			control: 'radio'

	pain_scale:
		model:
			required: true
			source: ['1' ,'2' ,'3' ,'4' ,'5' ,'6' ,'7' ,'8' ,'9' ,'10']
			if:
				'*':
					fields: ['pain_management']
		view:
			columns: 3
			control: 'radio'
			label: 'Pain Scale'

	pain_management:
		model:
			required: true
		view:
			columns: 3
			label: 'What are you doing to control the pain?'
			control: 'area'

	response_to_therapy:
		model:
			required: true 
			source: ['Worsening','No Change/Stable','Improvement']
			if:
				'Worsening':
					fields: ['response_to_therapy_rn']
					note: 'Forward to pharmacist for intervention and review'
				'Improvement':
					fields: ['response_to_therapy_rn']
		view:
			columns: 2.1
			control: 'radio'
			label: 'Response to therapy'

	response_to_therapy_rn:
		model:
			required: true
		view:
			columns: 2.1
			control: 'area'
			label: 'Reason'

	new_events:
		model:
			multi: true
			source: ['ER Visit', 'Hospitalization', 'Unscheduled Physician Visit', 'Adverse Drug Event', 'Any IV infusion related issues?', 'Missed Dose', 'New Medical Condition']
			if:
				'Missed Dose':
					sections: ['Missed Medications']
					fields: ['missed_medications', 'new_events_details']
				'*':
					fields: ['new_events_details']
		view:
			columns: -2.1
			control: 'checkbox'
			label: 'Any new medical events?'

	new_events_details:
		model:
			required: true 
		view:
			columns: 2
			control: 'area'
			label: 'Details'

	missed_medications:
		model:
			required: true
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': 'Active'
		view:
			embed:
				form: 'patient_medication'
				selectable: true
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['fdb_id', 'medication_dose', 'medication_frequency', 'start_date']
				label: ['Medication', 'Dose', 'Frequency', 'Start Dt']
				width: [35, 25, 25, 15]
			label: 'Missed Medication List'
			note: 'Select all that apply'

	pharmacist_questions:
		model:
			max: 128
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pharm_quest_dtl']
		view:
			control: 'radio'
			label: 'Do you have any questions for the Pharmacist?'
			columns: 2.1

	pharm_quest_dtl:
		model:
			required: true
		view:
			control: 'area'
			label: 'Details'
			columns: 2

	intervention:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['subform_intervention']
					sections: ['Intervention Details']
		view:
			control: 'radio'
			label: 'Intervention necessary?'

	subform_intervention:
		model:
			access:
				read: ['patient']
			source: 'patient_event'
			multi: true
			type: 'subform'
		view:
			label: 'Interventions'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				fields: ['category', 'resolved', 'outcome', 'comments']
				label: ['Category', 'Resolved?', 'Outcome', 'Comments']
				width: [20, 20, 35, 25]

	contact_notes:
		view:
			control: 'area'
			label: 'Assessment Notes'
			columns:2

	requires_pharmacist_review:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pharmacist_signature','counseling_performed']
		view:
			control: 'radio'
			label: 'Requires Pharmacist Review?'
			offscreen: true
			readonly: true

	counseling_performed:
		model:
			required: true
			multi: true
			source: ['Directions for use', 'Plan of care', 'Potential ADRs', 'Adherence', 'Meds, Supply storage, Disposal', 'How to contact the pharmacy']
		view:
			control: 'checkbox'
			label: 'Pt is educated on/or counseling session performed on:'

	pharmacist_signature:
		model:
			required: true
			type: 'json'
			access:
				write: ['-csr', '-cm', '-cma']
		view:
			note: 'I confirm the information on this document is accurate to ensure patient safety, medication safety, and programming safety.'
			control: 'esign'
			label: 'Pharmacist E-Signature'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	collections: ['ongoing_form']
	bundle: ['patient-form']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
			['order_id']
			['order_no']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		assessment:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
		ongoing:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'created_on']
	sections_group: [
		'Associated Records':
			fields: ['order_id', 'order_no', 'order_item_id', 'requires_nursing',
			'route_id', 'therapy_1', 'therapy_2', 'therapy_3',
			'therapy_4', 'therapy_5', 'brand_1',
			'brand_2', 'brand_3', 'brand_4',
			'brand_5', 'disease_1', 'disease_2', 'disease_3',
			'disease_4', 'disease_5', 'clinical_1', 'clinical_2', 'clinical_3',
			'clinical_4', 'clinical_5']

		'DUR - Medications':
			hide_header: true
			indent: false
			tab: 'DUR'
			fields: ['patient_medications']		

		'DUR - Allergies':
			hide_header: true
			indent: false
			tab: 'DUR'
			fields: ['patient_allergies']

		'DUR - DD DA Interaction':
			hide_header: true
			indent: false
			tab: 'DUR'
			fields: ['patient_interaction_btn']

		'DUR - Interaction':
			hide_header: true
			indent: false
			tab: 'DUR'
			fields: ['patient_interactions']

		'Nursing Notes':
			hide_header: true
			indent: false
			tab: 'Nursing'
			fields: ['nursing_notes']

		'Patient Questionnaire':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['response_to_therapy', 'response_to_therapy_rn', 'new_meds',
			'new_allergies_check', 'experiencing_pain', 'pain_scale', 'pain_management',
			'new_events', 'new_events_details', 'symptoms_filter']

		'Active Symptoms Review':
			fields: ['symptoms']

		'Missed Medications':
			indent: false
			tab: 'Assessment'
			fields: ['missed_medications']

		'H&P':
			indent: false
			tab: 'Assessment'
			fields: ['measurement_log']

		'Catheter Log':
			indent: false
			tab: 'Assessment'
			fields: ['catheter_log']

		'Primary Therapy Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_1'] # subform
		'Secondary Therapy Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_2'] # subform
		'Tertiary Therapy Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_3'] # subform
		'Quaternary Therapy Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_4'] # subform
		'Quinary Therapy Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_5'] # subform

		'Primary Drug Brand Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_1'] # subform
		'Secondary Drug Brand Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_2'] # subform
		'Tertiary Drug Brand Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_3'] # subform
		'Quaternary Drug Brand Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_4'] # subform
		'Quinary Drug Brand Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_5']

		'Primary Disease Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_1'] # subform
		'Secondary Disease Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_2'] # subform
		'Tertiary Disease Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_3'] # subform
		'Quaternary Disease Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_4'] # subform
		'Quinary Disease Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_5']

		'Primary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_clinical_1'] # subform
		'Secondary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_clinical_2'] # subform
		'Tertiary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_clinical_3'] # subform
		'Quaternary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_clinical_4'] # subform
		'Quinary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_clinical_5']

		'Pharmacist Questions':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			fields: ['pharmacist_questions', 'pharm_quest_dtl']

		'Interventions':
			indent: false
			tab: 'Post-Assessment'
			fields: ['intervention']
		'Intervention Details':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			note: 'Document any ADR or Catheter related event as an intervention'
			fields: ['subform_intervention']

		'Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			fields: ['contact_notes', 'requires_pharmacist_review', 'counseling_performed','pharmacist_signature']
	]

view:
	comment: 'Patient > Careplan > Ongoing Assessment'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by', 'reviewed_on', 'reviewed_by', 'contact_notes']
		sort: ['-created_on']
	label: 'Ongoing Assessment'
	open: 'read'
