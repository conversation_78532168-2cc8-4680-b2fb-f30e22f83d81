fields:

	name:
		model:
			required: true
		view:
			label: 'Note'
			findunique: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['lists']
	indexes:
		unique: [
			['name']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name']

view:
	comment: 'Manage > Workticket Label Note'
	find:
		basic: ['name']
	grid:
		fields: ['name']
		sort: ['name']
	label: 'Workticket Label Note'
	open: 'read'
