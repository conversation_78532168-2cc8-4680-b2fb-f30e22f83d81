fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	has_cpk_lab:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['cpk_lab_level']
		view:
			control: 'radio'
			label: 'Is Creatine phosphokinase (CPK) lab level available?'

	cpk_lab_level:
		model:
			min: 1
			max: 5000
			type: 'int'
			required: true
		view:
			note: 'U/L (units per liter)'
			label: 'Creatine phosphokinase (CPK) Level'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	sections:
		'Daptomycin Followup':
			fields: ['has_cpk_lab', 'cpk_lab_level']

view:
	comment: 'Patient > Careplan > Ongoing > Daptomycin'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Ongoing Assessment: Daptomycin'
	open: 'read'
