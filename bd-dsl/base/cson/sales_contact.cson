fields:
	owner:
		model:
			required: true
			source: 'user'
			sourcefilter:
				role:
					static: ['admin', 'liaison']
		view:
			label: 'Lead Owner'
			template: '{{user.id}}'

	prospect:
		model:
			source: 'sales_prospect'
			type: 'int'
		view:
			label: 'Prospect'

	salutation:
		view:
			label: 'Salutation'

	firstname:
		model:
			required: true
		view:
			label: 'First'
			validate: [
					name: '<PERSON><PERSON><PERSON>da<PERSON>'
			]

	middlename:
		model:
			required: false
		view:
			label: 'Middle'
			validate: [
					name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
			]

	lastname:
		model:
			required: true
		view:
			label: 'Last'
			validate: [
					name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
			]

	title:
		model:
			required: true
		view:
			label: 'Title'

	role:
		model:
			required: true
		view:
			label: 'Role'

	decision_maker:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Decision Maker?"

	phone_office:
		view:
			format: 'us_phone'
			label: 'Office Phone'

	phone_mobile:
		view:
			format: 'us_phone'
			label: 'Mobile Phone'

	phone_alt:
		view:
			format: 'us_phone'
			label: 'Alt. Phone'

	phone_fax:
		view:
			format: 'us_phone'
			label: 'Fax'

	email:
		view:
			label: '<PERSON><PERSON> Address'
			validate: [
					name: 'Email<PERSON>alidator'
			]

	linkedin_id:
		view:
			label: 'LinkedIn ID'

	url:
		view:
			label: 'URL'

	lead_status:
		model:
			multi: false
			source: ['Initial Outreach', 'Initial Contact', 'Ongoing Contact', 'Cold']
		view:
			class: 'status'
			control: 'checkbox'
			label: "Lead Status"

	rating:
		model:
			source: ['1', '2', '3', '4', '5']
		view:
			note: '1 = No Contact - 5 = Ongoing Relationship'
			control: 'radio'
			label: "Rating"

	opt_out:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Opt-Out?"

	lead_source:
		view:
			label: 'Lead Source'

	date_entered:
		model:
			type: 'date'
		view:
			label: 'Date Entered'
			template: '{{now}}'

	subform_contact:
		model:
			multi: true
			source: 'sales_contact_log'
			type: 'subform'
		view:
			label: 'Contact Log'

	next_contact:
		model:
			type: 'date'
		view:
			label: 'Next Contact'
			template: '{{now}}'

	next_action:
		view:
			label: 'Next Action'

	attempts:
		model:
			type: 'decimal'
			rounding: 1
		view:
			label: 'Attempts'

	client:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Current client?"

	product_type:
		view:
			label: 'Product Type'

	dob:
		model:
			type: 'date'
		view:
			control: 'input'
			label: 'Date of Birth'
			note: 'Must be in MM/DD/YYYY format'
			validate: [
					name: 'DOBValidator'
			]

	description:
		view:
			control: 'area'
			label: 'Description'

	subform_attachment:
		model:
			multi: true
			source: 'sales_attachment'
			type: 'subform'
		view:
			label: 'File Attachments'
			note: 'Max 100MB. Only documents, images, and archives supported.'

model:
	access:
		create:     []
		create_all: ['admin', 'liaison']
		delete:     ['admin', 'liaison']
		read:       ['admin', 'liaison']
		read_all:   ['admin', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'liaison']
		write:      ['admin', 'liaison']
	indexes:
		unique: [
			['firstname', 'lastname', 'middlename', 'dob']
		]
	name: ['firstname', 'lastname', 'middlename']
	sections:
		'Contact Info':
			fields: ['owner', 'prospect', 'firstname', 'lastname', 'middlename', 'salutation', 'title',
			'role', 'decision_maker', 'phone_office', 'phone_mobile', 'phone_alt', 'phone_fax',
			'email', 'linkedin_id', 'url', 'lead_source', 'date_entered', 'dob', 'description']
		'Status':
			fields: ['lead_status', 'rating', 'opt_out', 'client', 'product_type']
		'Contact Log':
			fields: ['subform_contact']
		'Actions':
			fields: ['next_contact', 'next_action', 'attempts']
		'File Attachments':
			fields: ['subform_attachment']

view:
	comment: 'Manage > Sales Contact'
	find:
		basic: ['owner', 'prospect', 'lastname', 'firstname', 'role', 'next_contact', 'next_action', 'lead_source']
	grid:
		fields: ['owner', 'prospect', 'lastname', 'firstname', 'role', 'next_contact', 'next_action']
		sort: ['lastname']
	label: 'Sales Contact'
	open: 'read'