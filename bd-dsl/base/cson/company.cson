# ============================ Note ===============================
# Please make sure to add default values for all fields if possible
# If a field has a default value, it will be automatically treated as a configuration on client side
# Please make sure update CompanyConfig interface in window.ts

fields:
	name:
		view:
			label: 'Company Name'
			columns: 1

	logo:
		model:
			type: 'json'
		view:
			control: 'file'
			label: 'Logo'
			class: 'media-preview'
			note: 'Max 100MB. Only images are supported.'
			columns: 4

	street:
		model:
			max: 32
		view:
			label: 'Street 1'
			class: "api_prefill"
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey: 'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'street': ['addressLabel']
					'city': ['city']
					'state_id': ['stateCode']
					'zip': ['postalCode']
			]
			columns: 'addr_1'

	street2:
		model:
			max: 32
		view:
			label: 'Street 2'
			columns: 'addr_2'

	city:
		model:
			max: 32
		view:
			label: 'City'
			columns: 'addr_city'

	state_id:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'State'

	zip:
		model:
			max: 32
		view:
			format: 'us_zip'
			label: 'Zip Code'
			columns: 'addr_zip'

	phone:
		model:
			max: 32
		view:
			format: 'us_phone'
			label: 'Phone #'
			columns: -4

	fax:
		model:
			max: 32
		view:
			format: 'us_phone'
			label: 'Fax #'
			columns: 4

	support:
		model:
			max: 32
		view:
			format: 'us_phone'
			label: 'Support Number'
			columns: 4

	supmail:
		model:
			max: 64
			min: 6
		view:
			label: 'Support Email'
			validate: [
				name: 'EmailValidator'
			]
			columns: 4

	bi_refresh:
		model:
			type: 'int'
			default: 180
		view:
			label: 'Reports Refresh Time'
			note: '*Seconds - Only if report is visible'
			columns: 4

	kb_refresh:
		model:
			type: 'int'
			default: 6
		view:
			label: 'Auto Workflow Refresh Time'
			note: '*Seconds, Only if worflow is visible'
			columns: 4

	snap_refresh:
		model:
			type: 'int'
			default: 30
		view:
			label: 'Auto Snap Refresh Time'
			note: '*Seconds'
			columns: 4

	jwt_timeout:
		model:
			max: 86400
			type: 'int'
		view:
			label: 'Browser Session Duration'
			note: '*Seconds'
			columns: 4

	ui_lock:
		model:
			type: 'int'
			default: 7200
		view:
			label: 'UI Lock After Inactivity'
			note: '*Seconds'
			columns: 4

	filter_wc:
		model:
			source: ['No', 'Yes']
			default: 'Yes'
		view:
			control: 'radio'
			label: 'Wild Card Search'
			note: '*Auto-appends * to all searches in filters'
			columns: 4

	prevent_datetime_focus_popup:
		model:
			source:
				'add': 'Add'
				'addfill': 'AddFill'
				'edit': 'Edit'
			multi: false
		view:
			control: 'radio'
			label: 'Prevent First Datetime Focus Popup'
			note: 'Stop Datetime Picker from popping up on first focus.'
			columns: 4

	no_download:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'No Download File'
			note: '*Prevent download file (shows preview instead)'
			columns: 4

	toast_form_level_errors:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Toast Form Level Errors'
			note: '*Show form level errors in toast'
			columns: 4

	separate_numbers:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['number_separator', 'decimal_separator']
			default: 'Yes'
		view:
			control: 'radio'
			label: 'Separate Numbers'
			note: '*Separate numbers based on delimiter'
			columns: 4

	number_separator:
		model:
			max: 32
			type: 'text'
			default: ','
		view:
			label: 'Number Separator'
			note: '*Separator for numbers i.e. 1,000,000 (comma)'
			columns: 4

	decimal_separator:
		model:
			max: 32
			type: 'text'
			default: '.'
		view:
			label: 'Decimal Separator'
			note: '*Separator for decimal i.e. 1.000000 (period)'
			columns: 4

	format_currency:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['currency_prefix']
			default: 'Yes'
		view:
			control: 'radio'
			label: 'Format Currency'
			note: '*Formats currency fields in data grid and prints'
			columns: 4

	currency_prefix:
		model:
			max: 32
			type: 'text'
			default: '$'
		view:
			label: 'Currency Prefix'
			note: '*Prefix for currency fields in data grid and prints'
			columns: 4

	esign_title:
		model:
			source: ['No', 'Yes']
			default: 'Yes'
		view:
			control: 'radio'
			label: 'Esign Title'
			note: '*Show user title for esign fields.'
			columns: 4

	pdf_logo:
		model:
			type: 'json'
		view:
			control: 'file'
			label: 'PDF Logo'
			note: 'Max 100MB. Only images are supported.'
			columns: 4

	max_collections:
		model:
			type: 'int'
			default: 5
		view:
			label: 'Max Collections'
			note: '*Max number of collections to show in data grid'
			columns: 4

	max_grid_columns:
		model:
			type: 'int'
			default: 10
		view:
			label: 'Max Grid Columns'
			note: '*Max number of columns to show in data grid'
			columns: 4

	max_grid_rows:
		model:
			type: 'int'
			default: 100
		view:
			label: 'Max Grid Rows'
			note: '*Max number of rows to show in data grid'
			columns: 4

	max_filter_rows:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			label: 'Max Filter Rows'
			note: '*Max number of results to show in while searching'
			columns: 4

	max_data_rows:
		model:
			type: 'int'
			default: 1000
		view:
			label: 'Max Data Rows'
			note: '*Max number of rows to fetch'
			columns: 4

	error_msg:
		view:
			label: 'Unexpected Error Message'
			control: 'area'
			note: '*Message to show when unexpected error occurs'
			columns: 2

	default_event_duration:
		model:
			type: 'int'
		view:
			label: 'Default Visit Duration (minutes)'
			columns: 4

	default_event_span:
		model:
			type: 'int'
		view:
			label: 'Default Span for Recurring Events (months)'
			columns: 4

	cal_default_view:
		model:
			source: ['Month', 'Week', 'Day']
			default: 'Month'
		view:
			control: 'radio'
			label: 'Default Calendar View'
			columns: 4

	weekends:
		model:
			source: ['Yes', 'No']
			default: 'Yes'
		view:
			control: 'radio'
			label: 'Include Saturday/Sunday columns in calendar views'
			columns: 4

	hidden_days:
		model:
			source: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
			multi:true
		view:
			control: 'checkbox'
			class: 'checkbox-7'
			label: 'Exclude certain days-of-the-week from being displayed'
			columns: 2

	first_day:
		model:
			source: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
		view:
			control: 'radio'
			label: 'The day that each week begins'
			columns: 2

	# Security
	password_strength:
		model:
			required: true
			source: ['Medium', 'Strong', 'Very Strong']
			default: 'Medium'
		view:
			control: 'radio'
			label: 'Password Strength'
			note: 'Medium = At least 8 characters, 1 lower/upper case, 1 number, 1 special character, Strong = 10 character min, Medium = 8 character min'
			columns: 2

	default_lot_expire_days:
		model:
			type: 'int'
			default: 365
			min: 1
		view:
			label: 'Default Drug Lot Expire Days'
			note: 'If lot expiration is sooner, then uses that'
			columns: 4

	default_order_days:
		model:
			type: 'int'
			default: 365
			min: 1
		view:
			label: 'Default Order Start/Stop Days'
			note: 'Default number of days for start / stop days from order date'
			columns: 4

	default_order_expire_days:
		model:
			type: 'int'
			default: 365
			min:1
		view:
			label: 'Default Order Expire Days'
			note: 'Default number of days for expiration of order from written date'
			columns: 4

	next_delivery_day:
		model:
			type: 'int'
			default: 6
			min: 0
		view:
			label: 'Default Shipping Days'
			note: 'Default business days from Refill Date to Delivery Date'
			columns: 4

	refill_days_out:
		model:
			type: 'int'
			default: 7
			min: 0
		view:
			label: 'Refill Days Out'
			note: 'Default number of days before next compounding/refill date to be allowed'
			columns: 4

	require_test_claim:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			label: 'Require Payable Test Claim if Rx follows IV Pathway upon Order Completion?'
			note: 'If Yes, Test Claim swimlane will need to be enabled. Contact Support to enable.'
			columns: 4

	clinical_assmts:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			multi: true
		view:
			label: 'Clinical Assessments'
			note: 'Clinical assessments to be included patient menu MAX 5'
			max_count: 5
			columns: 1

	pmp_program_name:
		model:
			max: 256
			default: 'Clinical Therapy Management'
		view:
			label: 'Clinical Therapy Management Program Name'
			columns: 2

	auth_days_out:
		model:
			type: 'int'
			default: 30
		view:
			label: 'Authorization Days Out'
			note: 'Default number of days out from authorization date to to show in expiring auth queue.'
			columns: 4

	use_test_claim:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			label: 'Require Test Claim on Rx Complete for non-Specialty Rx?'
			columns: 4

	use_px_team:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['px_note_template', 'px_inter_types', 'px_inter_primary_type', 'px_note_type']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Use Patient Experience (PX) Team'
			offscreen: true
			readonly: true

	px_note_template:
		model:
			required: true
			max: 4096
			dynamic:
				source: 'list_note_subject'
		view:
			columns: 4
			label: 'Default PX Note Template'
			offscreen: true
			readonly: true

	px_inter_types:
		model:
			required: true
			multi: true
			source: 'list_intervention_type'
			sourceid: 'code'
		view:
			label: 'PX Intervention Types'
			offscreen: true
			readonly: true

	px_inter_primary_type:
		model:
			required: true
			multi: false
			source: 'list_intervention_type'
			sourceid: 'code'
		view:
			label: 'PX Primary Intervention Type'
			offscreen: true
			readonly: true

	px_note_type:
		model:
			required: true
			source: 'list_note_type'
			sourceid: 'code'
		view:
			label: 'PX Progress Note Type'
			offscreen: true
			readonly: true

	intake_inter_types:
		model:
			required: true
			multi: true
			source: 'list_intervention_type'
			sourceid: 'code'
		view:
			label: 'Intake Intervention Types'
			columns: 1

	intake_inter_primary_type:
		model:
			required: true
			multi: false
			source: 'list_intervention_type'
			sourceid: 'code'
		view:
			label: 'Intake Primary Intervention Type'
			columns: 2

	nurse_inter_types:
		model:
			required: true
			multi: true
			source: 'list_intervention_type'
			sourceid: 'code'
		view:
			label: 'Nursing Intervention Types'
			columns: 1

	nurse_careplan_type:
		model:
			default: 'Treatment Plan'
			required: true
			source: ['Treatment Plan', 'Multidisciplinary Care Plan']
		view:
			control: 'radio'
			label: 'Nursing Care Plan Type'
			note: 'Nursing uses CMS-485 for MCR patients and Care Plan Type for all others'
			columns: 4

	dt_inventory_check:
		model:
			source: ['Yes']
			default: 'Yes'
		view:
			note: 'If you work on delivery tickets ahead of fills, or source just-in-time then turn off'
			label: 'Enforce Inventory Supply Check On Delivery Ticket Creation'
			control: 'checkbox'
			class: 'checkbox-only'
			offscreen: true
			readonly: true

	auto_test_claim:
		model:
			source: ['Yes']
			default: 'Yes'
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Automatically run NCPDP test claim during delivery ticket generation?'
			note: 'If no, claim will be generated for biller in the appropriate queue to manage and review.'
			offscreen: true
			readonly: true

	adjustment_threshold:
		model:
			min: 0.00
			default: 10.00
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format:'$0,0.00'
			note: 'Only a single adjustment can be pending approval at one time per invoice'
			label: 'Threshold amount of adjustment before manager approval is required'
			columns: 4

	ncpdp_cash_posting:
		model:
			source: ['Yes']
			default: 'Yes'
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Auto-post cash payments for payable NCPDP claims?'
			columns: 4

	ncpdp_claim_exception_days:
		model:
			type: 'int'
			default: 14
		view:
			label: 'NCPDP Claim Exception Days'
			note: 'Number of days to from claim DOS to confirmed delivery before claim shows up in Claim Exception Queue'
			columns: 4

	fiscal_year_start:
		model:
			type: 'date'
		view:
			label: 'Fiscal Year Start Period'
			note: 'Only used month and day, not year. January 1st if not set.'
			columns: 4

	cogs_method:
		model:
			source: ['FIFO', 'LIFO']
			default: 'FIFO'
		view:
			label: 'COGS Method'
			columns: 4

	default_due_days:
		model:
			type: 'int'
			default: 30
		view:
			label: 'Default Due Days for Patient Invoices'
			columns: 4

	default_services_provided:
		model:
			dynamic:
				source: 'list_invoice_services_provided'
		view:
			label: 'Default Services Provided'
			columns: 2

	default_free_text:
		model:
			dynamic:
				source: 'list_invoice_free_text'
		view:
			label: 'Default Free Form Message'
			columns: 2

	default_terms:
		model:
			dynamic:
				source: 'list_invoice_terms'
		view:
			label: 'Default Additional Terms'
			columns: 2

	bg_color:
		model:
			type: 'color'
			default: '#F8F1EC'
		view:
			label: 'Background Color'
			columns: 4

	fg_color:
		model:
			type: 'color'
			default: '#F5F7FA'
		view:
			label: 'Foreground Color'
			columns: 4

	px_survey_type:
		model:
			multi: true
			source:
				clinical_mgadl: 'Myasthenia Gravis Activities of Daily Living (MG-ADL)'
				clinical_incat: 'INCAT'
				clinical_mfis5: 'Modified Fatigue Impact Scale (MFIS-5)'
				clinical_rods: 'RODS'
				clinical_qol_factor: 'Quality Of Life (Factor)'
				clinical_qol_peds: 'Quality Of Life (PEDS)'
				clinical_sf12v2: 'Quality Of Life'
			default: 'clinical_rarevoice_survey'
		view:
			control: 'radio'
			label: 'PX Survey Type'
			columns: 4
			note: 'Max 3'
			max_count: 3
			offscreen: true
			readonly: true

	footer_statement:
		model:
			max: 256
		view:
			label: 'Footer Statement'
			control: 'area'


	fax_provider:
		model:
			source: ['N/A', 'ifax']
			default: 'N/A'
			if:
				'ifax':
					fields: ['fax_environment']
					note: 'https://www.ifaxapp.com/'
		view:
			control: 'radio'
			label: 'Fax Provider'
	fax_environment:
		model:
			source: ['Production', 'Development']
			default: 'Development'
			if:
				'Development':
					fields: ['fax_api_key_dev', 'subform_fax_numbers']
				'Production':
					fields: ['fax_api_key_prod', 'subform_fax_numbers']
		view:
			control: 'radio'
			label: 'Fax Environment'

	fax_api_key_prod:
		model:
			type: 'text'
			required: true
		view:
			label: 'Fax API Key (Production)'

	fax_api_key_dev:
		model:
			type: 'text'
			required: true
		view:
			label: 'Fax API Key (Development)'

	subform_fax_numbers:
		model:
			multi: true
			source: 'fax_numbers'
			type: 'subform'
		view:
			label: 'Fax Numbers'
			grid:
				hide_cardmenu: true
				edit: true
				add: 'inline'
				fields: ['site_id', 'fax_number', 'is_default', 'number_label', 'from_caller_id']
				label: ['Site', 'Fax Number', 'Default Fax #?', 'Label', 'From Caller ID']

model:
	access:
		create: []
		create_all: ['admin', 'nurse', 'pharm']
		delete: ['admin', 'pharm']
		read: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request: []
		update: []
		update_all: ['admin', 'nurse', 'pharm']
		write: ['admin', 'nurse', 'pharm']
	bundle: ['setup']
	name: 'Company Configuration'
	sections:
		'Contact Info':
			fields: ['name','street','street2','city','state_id','zip','phone','fax',
			'support','supmail']
			tab: 'General'
			hide_header: true
		'Number / Currency':
			fields: ['separate_numbers','number_separator','decimal_separator','format_currency','currency_prefix']
			tab: 'General'

		'Theme / UI':
			fields: ['logo', 'pdf_logo', 'bg_color', 'fg_color', 'toast_form_level_errors', 'esign_title', 'footer_statement']
			tab: 'Theme / UI'
			hide_header: true
		'Preferences':
			fields: ['bi_refresh','kb_refresh','snap_refresh','filter_wc','no_download', 'prevent_datetime_focus_popup']
			tab: 'Preferences'
			hide_header: true
		'Data Grid':
			fields: ['max_collections','max_grid_columns','max_grid_rows','max_filter_rows','max_data_rows']
			tab: 'Preferences'
		'Calendar':
			hide_header: true
			fields: ['first_day','default_event_duration','default_event_span', 'cal_default_view','weekends', 'hidden_days']
			tab: 'Calendar'
		'Security':
			hide_header: true
			fields: ['password_strength', 'jwt_timeout', 'ui_lock']
			tab: 'Security'
		'Intake':
			fields: ['intake_inter_types', 'intake_inter_primary_type']
			tab: 'Intake'
			hide_header: true
		'Dispensing':
			fields: ['default_lot_expire_days', 'default_order_days', 'default_order_expire_days',
			'next_delivery_day', 'refill_days_out', 'require_test_claim']
			tab: 'Dispensing'
			hide_header: true
		'Clinical':
			fields: ['clinical_assmts', 'pmp_program_name']
			tab: 'Clinical'
			hide_header: true
		'Nursing':
			fields: ['nurse_inter_types', 'nurse_careplan_type']
			tab: 'Nursing'
			hide_header: true
		'Finance':
			fields: ['fiscal_year_start', 'cogs_method', 'adjustment_threshold']
			tab: 'Billing'
			hide_header: false
		'Workflow':
			fields: ['auth_days_out', 'use_test_claim', 'adjustment_threshold']
			tab: 'Billing'
			hide_header: false
		'Claims':
			fields: ['ncpdp_cash_posting', 'ncpdp_claim_exception_days']
			tab: 'Billing'
			hide_header: false
		'Patient Invoicing':
			fields: ['default_due_days', 'default_services_provided', 'default_free_text', 'default_terms']
			tab: 'Billing'
			hide_header: false
		'Integrations':
			tab: 'Integrations'
			hide_header: true
		'Faxing':
			fields: ['fax_provider', 'fax_environment', 'fax_api_key_prod', 'fax_api_key_dev']
			tab: 'Integrations'
		'Fax Numbers':
			fields: ['subform_fax_numbers']
			hide_header: true
			indent: false
			tab: 'Integrations'
view:
	hide_cardmenu: true
	comment: 'Settings > Configuration'
	find:
		basic: []
	grid:
		fields: []
	label: 'Configuration'
	open: 'read'
