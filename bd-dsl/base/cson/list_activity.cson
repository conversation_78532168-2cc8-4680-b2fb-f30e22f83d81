fields:
	name:
		model:
			required: true
		view:
			columns: 3
			label: 'Name'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 3
			control: 'radio'
			label: 'Can Sync Record'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 3
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['lists']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['name']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name', 'allow_sync', 'active']
view:
	comment: 'Manage > Activity'
	find:
		basic: ['name']
	grid:
		fields: ['name', 'allow_sync', 'active']
		sort: ['name']
	label: 'Activity'
	open: 'read'
