fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Support Questions
	nurse_support:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'No':
					fields:['patient_trained']
		view:
			control: 'radio'
			label: 'Will a nurse be required for the first infusion?'

	patient_trained:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is the patient/caregiver already trained?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'AAT Nurse Support':
				fields: ['nurse_support', 'patient_trained']
view:
	comment: 'Patient > Careplan > Assessment > AAT'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Assessment Questionnaire: AAT'
	open: 'read'
