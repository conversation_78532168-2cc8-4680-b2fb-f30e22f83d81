fields:

	npi:
		view:
			label: 'NPI'
			findunique: true
			columns: 3

	pecos_asct_cntl_id:
		view:
			label: 'PECOS CNTL ID'
			columns: 3

	enrlmt_id:
		view:
			label: 'Enrollment ID'
			columns: 3

	provider_type_cd:
		view:
			label: 'Provider Type CD'
			columns: 3

	provider_type_desc:
		view:
			label: 'Provider Type Description'
			columns: 3

	state_cd:
		view:
			label: 'State CD'
			columns: 3

	first_name:
		view:
			label: 'Provider First Name'
			columns: 3

	mdl_name:
		view:
			label: 'Provider Middle Name'
			columns: 3

	last_name:
		view:
			label: 'Provider Last Name'
			columns: 3

	org_name:
		view:
			label: 'Provider Organization Name'
			columns: 3

	gndr_sw:
		view:
			label: 'Gender SW'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		many: [
			['npi']
		]

	name: ['npi']
	sections:
		"Provider's Name":
			fields: ['first_name', 'mdl_name', 'last_name']
		'Details':
			fields: ['npi', 'pecos_asct_cntl_id', 'enrlmt_id', 'provider_type_cd','gndr_sw',
			'state_cd', 'org_name', 'provider_type_desc']

view:
	comment: 'Manage > List PECOS NPI'
	find:
		basic: ['npi', 'first_name', 'last_name']
	grid:
		fields: ['npi', 'first_name', 'last_name']
	label: 'List PECOS NPI'
