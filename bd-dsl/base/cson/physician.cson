fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	first:
		model:
			search: 'A'
			max: 64
			required: true
		view:
			label: 'First Name'
			note: 'Letters, numbers, comma, \', -, . only'
			columns: 4
			validate: [
					name: '<PERSON><PERSON>alida<PERSON>'
			]

	last:
		model:
			search: 'C'
			max: 64
			required: true
		view:
			label: 'Last Name'
			note: 'Letters, numbers, comma, \', -, . only'
			columns: 4
			validate: [
					name: 'NameValida<PERSON>'
			]

	title:
		model:
			default: 'MD'
			max: 8
			required: true
		view:
			columns: 4
			label: 'Title'

	npi:
		model:
			search: 'D'
			max: 10
		view:
			columns: 4
			label: 'NPI'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid NPI, must be 10 digits'
			},{
				name: "CheckPecosEnrolled"
			}]

	dea:
		model:
			max: 16
		view:
			columns: 4
			label: 'DEA'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z]{2}\\d{7}$'
				error: 'Invalid DEA number, DEA numbers must be 2 letters followed by 7 numbers'
			}]

	mcd:
		view:
			columns: 4
			label: 'MCD Provider #'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{6,15}$'
				error: 'Invalid Medicaid Number'
			}]

	mcr_ptan_id:
		view:
			columns: 4
			label: 'MCR Provider # (PTAN)'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{5,12}$'
				error: 'Medicare Provider Number (PTAN), must be 5-12 alphanumeric characters"'
			}]

	state_license:
		view:
			columns: 4
			label: 'State License #'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{5,20}$'
				error: 'Invalid State License Number'
			}]

	state_cs_license:
		view:
			columns: 4
			label: 'State CS License #'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{5,20}$'
				error: 'Invalid State Controlled Substance License Number'
			}]

	taxonomy_id:
		model:
			source: 'list_nucc_taxonomy'
			sourceid: 'code'
		view:
			columns: 4
			label: 'Physician Specialty'
			note: 'Specialty for the physician (NUCC Taxonomy Code)'

	rems:
		view:
			columns: 4
			label: 'REMs ID'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{5,20}$'
				error: 'Invalid REMs ID'
			}]

	pecos_enrolled:
		model:
			max: 3
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'PECOS Enrolled?'

	primary_phone:
		model:
			max: 21
		view:
			columns: 4
			format: 'us_phone'
			label: 'Phone #'

	primary_fax:
		model:
			max: 21
		view:
			columns: 4
			format: 'us_phone'
			label: 'Fax #'

	preferred_communication:
		model:
			source: ['Phone', 'SMS', 'Fax', 'Email', 'Portal']
		view:
			columns: 4
			control: 'select'
			label: 'Preferred Communication Route'

	portal_access:
		model:
			save: false
			source:
				'0': 'No'
				'1': 'Yes'
			default: '0'
			if:
				'1':
					fields: ['email']
		view:
			columns: 4
			control: 'radio'
			label: 'Create Physician Portal Access Account?'

	user_id:
		model:
			source: 'user'
			if:
				'!':
					fields: ['portal_access']
		view:
			columns: 4
			readonly: true
			note: 'Link to user record associated with portal access'
			label: 'User Record'

	email:
		model:
			max: 64
			min: 6
			required: false
		view:
			columns: 4
			label: 'Email Address'
			note: 'Must be a valid email address for Portal Access'
			validate: [
					name: 'EmailValidator'
			]

	territory_id:
		model:
			required: true
			source: 'sales_territory'
		view:
			label: 'Sales Territory'
			columns: 4

	is_test:
		model:
			max: 3
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			note: 'Marking Yes will exclude this physician from any outcomes reporting'
			label: 'Is this physician a test physician?'

	subform_location:
		model:
			source: 'physician_location'
			type: 'subform'
			multi: true
		view:
			label: 'Location'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				fields: ['name', 'addresslabel', 'is_primary', 'phone_number', 'fax_number']
				label: ['Name', 'Address', 'Primary?', 'Phone', 'Fax']
				width: [20, 40, 10, 15, 15]

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'physician'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					direct_attachment: 'Yes'
					physician_id: '{code}'
					assigned_to: 'Physician'
					source: 'Scanned Document'
					form_code: '{code}'
					form_name: 'physician'
					form_filter: 'physician'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

	notes:
		view:
			control: 'area'
			label: 'Notes'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr','liaison', 'pharm']
		write:      ['admin', 'csr','liaison', 'pharm']
	indexes:
		many: [
			['first', 'last', 'title']
			['dea']
			['npi']
			['user_id']
		]
		unique: [
			['code']
			['npi']
		]

	name: '{last}, {first} {title} {npi}'

	sections_group: [
		'Name':
			hide_header: true
			indent: false
			fields: ['code', 'last', 'first', 'title']
			tab: 'Physician'
		'Credentialing':
			fields: ['npi', 'dea', 'mcd', 'mcr_ptan_id', 'state_license', 'state_cs_license',
			'taxonomy_id', 'rems', 'pecos_enrolled']
			tab: 'Physician'
		'Contact Info':
			fields: ['primary_phone', 'primary_fax', 'preferred_communication']
			tab: 'Physician'
		'Account':
			fields: ['portal_access', 'email', 'user_id', 'territory_id', 'is_test']
			tab: 'Physician'
		'Notes':
			fields: ['notes']
			tab: 'Physician'
		'Locations':
			fields: ['subform_location']
			tab: 'Physician'
		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'
		]

view:
	hide_cardmenu: true
	comment: 'Manage > Physician'
	find:
		basic: ['first', 'last']
	grid:
		fields: ['first', 'last', 'title', 'primary_phone', 'primary_fax', 'npi']
		sort: ['last']
	label: 'Physician'
