fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			required: true
			source: 'careplan_order'
			type: 'int'
		view:
			columns: 2
			label: 'Order'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/careplan_order/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'order_no': ['order_no'],
					'prescriber_id': ['prescriber_id']
			]

	order_no:
		model:
			required: true
		view:
			columns: 2
			label: 'Order #'
			readonly: true

	insurance_id:
		model:
			prefill: ['careplan_nursing_485']
			required: true
			source: 'patient_insurance'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
				type_id:
					'static': ['MCRB', 'MCRD']
		view:
			columns: 2
			label: 'Medicare Insurance Record'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_insurance/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'medicare_number': ['medicare_number'],
			]

	prescriber_id:
		model:
			prefill: ['careplan_nursing_485']
			required: true
			source: 'patient_prescriber'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			columns: 2
			label: 'Prescriber'
			note: '24'

	site_id:
		model:
			prefill: ['careplan_nursing_485']
			required: true
			source: 'site'
		view:
			label: 'Site'
			note: '5/7'
			columns: 2

	medicare_number:
		model:
			prefill: ['careplan_nursing_485']
			required: true
			max: 14
		view:
			label: 'Medicare/HIC Number'
			note: '1'
			columns: 2
			validate: [{
				name: 'RegExValidator'
				pattern: '^[AC-HJ-KM-NP-RT-Y][0-9]{3}[AC-HJ-KM-NP-RT-Y]{2}[0-9]{2}[AC-HJ-KM-NP-RT-Y][0-9]{2}$'
				error: 'Invalid Medicaid Number'
			}]

	start_of_care_date:
		model:
			prefill: ['careplan_nursing_485']
			required: true
			type: 'date'
		view:
			label: 'Start of Care Date'
			note: '2'
			columns: 2

	cert_start:
		model:
			prefill: ['careplan_nursing_485.cert_end']
			required: true
			type: 'date'
		view:
			label: 'Certification Start Date'
			template: '{{now}}'
			note: '3'
			columns: 2
			validate: [{
				name: 'AddDateDays'
				field: 'cert_end'
				days: 60
			}]

	cert_end:
		model:
			required: true
			type: 'date'
		view:
			label: 'Certification End Date'
			columns: 2

	patient_address_id:
		model:
			prefill: ['careplan_nursing_485', 'patient_address']
			required: true
			source: 'patient_address'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			label: 'Patient Address'
			note: '6'
			columns: 2

	dx_ids:
		model:
			prefill: ['careplan_nursing_485']
			required: true
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			embed:
				form: 'patient_diagnosis'
			grid:
				edit: false
				rank: 'none'
				fields: ['rank', 'dx_id', 'diagnosis_start_date']
				label: ['Rank', 'Diagnosis', 'Start Date']
				width: [15, 60, 25]
				selectall: true
			max_count: 5
			label: 'Diagnosis'
			note: '11/13 Max 5'

	patient_medications:
		model:
			prefill: ['careplan_nursing_485']
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': 'Active'
		view:
			embed:
				form: 'patient_medication'
				add_preset:
					reported_by: 'Nurse'
					status: 'Active'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				rank: 'none'
				fields: ['fdb_id', 'medication_dose', 'medication_frequency', 'start_date']
				label: ['Medication', 'Dose', 'Frequency', 'Start Dt']
				width: [35, 25, 25, 15]
				selectall: true
			label: 'Medication Profile'
			note: '10'

	patient_interactions:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			readonly: true
			embed:
				form: 'interactions'
				selectable: false
			grid:
				add: 'none'
				hide_cardmenu: true
				edit:true
				rank: 'none'
				fields: ['created_by', 'has_da', 'has_dd']
				label: ['Created By','Drug Allergy', 'Drug Drug']
				width: [30, 30, 30]
			label: 'Interactions'

	patient_interaction_btn:
		model:
			source: ['Get Interactions']
		view:
			columns: 2
			class: 'dsl-button'
			control: 'checkbox'
			label: 'Patient Interaction'
			validate: [
				name: 'DrugAllergyInteraction'
			]

	discontinued_medications:
		model:
			prefill: ['careplan_nursing_485']
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': 'Active'
		view:
			embed:
				form: 'patient_medication'
			grid:
				add: 'none'
				edit: true
				rank: 'none'
				fields: ['fdb_id', 'medication_dose', 'end_date', 'discontinued_reason']
				label: ['Medication', 'Dose', 'End Dt', 'Discontinue Reason']
				width: [30, 20, 25, 25]
				selectall: true
			label: 'Discontinued Medications'
			note: '10'

	active_rx:
		model:
			prefill: ['careplan_nursing_485']
			multi: true
			sourcefilter:
				order_no:
					'dynamic': '{order_no}'
		view:
			embed:
				query: 'active_prescriptions'
				selectable: true
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['inventory_id', 'type_id', 'dose', 'dose_unit_id', 'frequency_id']
				label: ['Drug', 'Type', 'Dose', 'Unit', 'Freq']
				width: [30, 15, 10, 20, 25]
				selectall: true
			label: 'Active Prescriptions'
			note: '21'

	supplies:
		model:
			prefill: ['careplan_nursing_485']
			multi: true
			sourcefilter:
				order_no:
					'dynamic': '{order_no}'
				discontinued:
					'static': 'Yes'
		view:
			embed:
				form: 'careplan_order_supply'
				selectable: true
			grid:
				rank: 'none'
				add: 'none'
				edit: false
				fields: ['inventory_id', 'dispense_quantity', 'supply_billable', 'one_time_only']
				label: ['Item', 'Quantity', 'Billable?', 'One-Time?']
				width: [50, 15, 20, 15]
				selectall: true
			label: 'Supplies'

	rentals:
		model:
			prefill: ['careplan_nursing_485']
			multi: true
			sourcefilter:
				order_no:
					'dynamic': '{order_no}'
				discontinued:
					'static': 'Yes'
		view:
			embed:
				form: 'careplan_order_rental'
				selectable: true
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['inventory_id', 'rental_type', 'frequency_code']
				label: ['Item', 'Type', 'Freq']
				width: [40, 30, 30]
				selectall: true
			label: 'DME/Rentals'

	# Safety Measures
	safety_measures:
		model:
			multi: true
			source: ['O2 Precautions', 'Seizure Precautions', 'Transfer/Amb Precautions',
					"Assistance with ADL's", 'Keep Pathways Clear',
					'Remove Throw Rugs', 'Use of Assistive Devices',
					'Proper Medical Waste Disposal', 'Infection Control Measures', 'Proper Sharps Disposal', 'Universal Precatuions',
					'Call 911 for Emergency', 'Other (Specify)']
			if:
				'Other (Specify)':
					fields: ['safety_measures_other']
		view:
			control: 'checkbox'
			label: 'Safety Measures'
			columns: 2
			note: '15'

	safety_measures_other:
		model:
			required: true
		view:
			columns: 2
			label: 'Safety Measures Other'
			note: '15'

	# Care Plan Changes
	problems:
		model:
			subfields:
				template_id:
					type: 'int'
					readonly: true
					offscreen: true
				date:
					label: 'Date'
					type: 'timestamp'
					readonly: true
					style:
						width: '20%'
				problem:
					label: 'Problem'
					type: 'text'
					dynamic: 'careplan_problem'
					style:
						width: '50%'
				compeleted:
					type: 'checkbox'
					label: 'Complete'
					style:
						width: '10%'
				compeleted_date:
					label: 'Comp Date'
					type: 'text'
					readonly: true
					style:
						width: '20%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Problems'
			transform: [
					name: 'MarkCompDate'
					field: 'problems'
			]

	goals:
		model:
			subfields:
				template_id:
					type: 'int'
					readonly: true
					offscreen: true
				date:
					label: 'Date'
					type: 'timestamp'
					readonly: true
					style:
						width: '20%'
				goal:
					label: 'Goal'
					type: 'text'
					dynamic: 'careplan_goal'
					style:
						width: '50%'
				compeleted:
					type: 'checkbox'
					label: 'Complete'
					style:
						width: '10%'
				compeleted_date:
					label: 'Comp Date'
					type: 'text'
					readonly: true
					style:
						width: '20%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Goals'
			transform: [
					name: 'MarkCompDate'
					field: 'goals'
			]

	interventions:
		model:
			subfields:
				template_id:
					type: 'int'
					readonly: true
					offscreen: true
				date:
					label: 'Date'
					type: 'timestamp'
					readonly: true
					style:
						width: '20%'
				intervention:
					label: 'Intervention'
					type: 'text'
					dynamic: 'careplan_intervention'
					style:
						width: '50%'
				compeleted:
					type: 'checkbox'
					label: 'Complete'
					style:
						width: '10%'
				compeleted_date:
					label: 'Comp Date'
					type: 'text'
					readonly: true
					style:
						width: '20%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Interventions'
			transform: [
					name: 'MarkCompDate'
					field: 'interventions'
			]

	# Functional Limitations
	functional_lim:
		model:
			prefill: ['careplan']
			multi: true
			source: ['Amputation', 'Bowel/Bladder (Incontinence)',
					'Contracture', 'Hearing', 'Paralysis', 'Endurance',
					'Ambulation', 'Speech', 'Legally Blind',
					'Dyspnea With Minimal Exertion', 'Other (Specify)']
			if:
				'Other (Specify)':
					fields: ['functional_lim_other']
		view:
			control: 'checkbox'
			label: 'Select all functional limitations'
			note: '18A'
			columns: 2

	functional_lim_other:
		model:
			prefill: ['careplan']
		view:
			label: 'Functional limitations Other'
			columns: 2

	# Mental Status
	mental_status:
		model:
			multi: true
			source: ['Oriented', 'Comatose', 'Forgetful',
					'Depressed', 'Disoriented',
					'Lethargic', 'Agitated',
					'Other']
			if:
				'Other':
					fields: ['mental_status_other']
		view:
			control: 'checkbox'
			label: 'Mental Status'
			note: '19'
			columns: 2

	mental_status_other:
		model:
			required: true
		view:
			label: 'Mental Status Other'
			columns: 2

	# Activities Permitted
	activities_permitted:
		model:
			multi: true
			source: ['Complete Bedrest', 'Bedrest BRP', 'Up As Tolerated',
					'Transfer Bed/Chair', 'Exercises Prescribed',
					'Partial Weight Bearing', 'Independent At Home',
					'Crutches', 'Cane', 'Wheelchair', 'Walker',
					'No Restrictions', 'Other (Specify)']
			if:
				'Other (Specify)':
					fields: ['activities_permitted_other']
		view:
			control: 'checkbox'
			label: 'Activities Permitted'
			note: '18B'
			columns: 2

	activities_permitted_other:
		model:
			required: true
		view:
			label: 'Activities Permitted Other'
			columns: 2

	prognosis:
		model:
			prefill: ['careplan_nursing_485']
			source: ['Poor', 'Guarded', 'Fair', 'Good', 'Excellent']
		view:
			control: 'radio'
			label: 'Prognosis'
			note: '20'
			columns: 2

	# Discharge / Rehab
	rehab_pot:
		model:
			prefill: ['careplan_nursing_485']
			source: ['Good for Stated Goals', 'Fair for Stated Goals', 'Poor for Stated Goals']
		view:
			control: 'radio'
			label: "Rehab Potential"
			note: '22'

	discharge_plans:
		model:
			prefill: ['careplan_nursing_485']
			source: ['Patient will require on-going skilled nursing visits for administration of therapy at home',
					'Discharge when goals met/no further skilled nursing needed',
					'Discharge to self care/family and care of MD']
		view:
			control: 'select'
			label: "Discharge Plans"
			note: '22'

	note:
		model:
			prefill: ['careplan_nursing_485']
			max: 4096
		view:
			control: 'area'
			label: 'Care Plan Note'

	rn_signature:
		model:
			required: true
			type: 'json'
		view:
			control: 'esign'
			label: 'Nurse E-Signature'

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'careplan_nursing_485'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Other Form'
					direct_attachment: 'Yes'
					form_code: '{code}'
					form_name: 'careplan_nursing_485'
					form_filter: 'careplan_nursing_485'
					source: 'Scanned Document'
					type_id: 'Nursing Care Plan (485)'
					patient_id: '{patient_id}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin', 'cm', 'cma', 'nurse', 'pharm']
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['patient_id', 'start_of_care_date', 'cert_start', 'cert_end']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
			['order_id']
			['order_no']
		]
		unique: [
			['code']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan_nursing_485:
			link:
				order_id: 'order_id'
	sections_group: [
		'Associated Records':
			hide_header: true
			indent: true
			tab: 'Header'
			fields: ['code', 'order_id', 'order_no', 'insurance_id', 'prescriber_id',
			'site_id', 'patient_address_id']
		'Header':
			hide_header: true
			indent: true
			tab: 'Header'
			fields: ['medicare_number', 'start_of_care_date', 'cert_start', 'cert_end']
		'Diagnoses':
			hide_header: true
			indent: true
			tab: 'Diagnoses'
			fields: ['dx_ids']
		'Active Medications':
			indent: true
			tab: 'Medications/Orders'
			fields: ['patient_medications']

		'DUR - DD DA Interaction':
			hide_header: true
			indent: false
			tab: 'Medications/Orders'
			fields: ['patient_interaction_btn']

		'DUR - Interaction':
			hide_header: true
			indent: false
			tab: 'Medications/Orders'
			fields: ['patient_interactions']

		'Discontinued Medications':
			indent: true
			tab: 'Medications/Orders'
			fields: ['discontinued_medications']
		'Active Prescriptions':
			indent: true
			tab: 'Medications/Orders'
			fields: ['active_rx']
		'Supplies':
			indent: true
			tab: 'Supplies/DME'
			fields: ['supplies']
		'DME/Rentals':
			indent: true
			tab: 'Supplies/DME'
			fields: ['rentals']
		'Safety Measures':
			prefill: 'careplan_nursing_485'
			indent: true
			tab: 'Conditions'
			fields: ['safety_measures', 'safety_measures_other']
		'Functional Limitations':
			prefill: 'careplan_nursing_485'
			indent: true
			tab: 'Conditions'
			fields: ['functional_lim', 'functional_lim_other']
		'Mental Status':
			prefill: 'careplan_nursing_485'
			indent: true
			tab: 'Conditions'
			fields: ['mental_status', 'mental_status_other']
		'Activities Permitted':
			prefill: 'careplan_nursing_485'
			indent: true
			tab: 'Conditions'
			fields: ['activities_permitted', 'activities_permitted_other']
		'Problems':
			prefill: 'careplan_nursing_485'
			indent: true
			tab: 'Prob/Goal/Int'
			fields: ['problems']
		'Goals':
			prefill: 'careplan_nursing_485'
			indent: true
			tab: 'Prob/Goal/Int'
			fields: ['goals']
		'Interventions':
			indent: true
			tab: 'Prob/Goal/Int'
			fields: ['interventions']
		'Discharge Plans':
			indent: true
			tab: 'Discharge'
			fields: ['prognosis', 'rehab_pot', 'discharge_plans', 'note', 'rn_signature']
		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'
	]

view:
	hide_cardmenu: true
	validate: [
		{
			name: "DateOrderValidator"
			fields: [
				"cert_start",
				"cert_end"
			]
			
			error: "Certification End Date cannot be lesser than Certification Start Date"
		}
	]
	comment: 'Patient > Careplan > Care Plan Nursing (485)'
	grid:
		fields: ['created_on', 'created_by', 'reviewed_by', 'reviewed_on', 'cert_start', 'cert_end']
		sort: ['created_on']
	label: 'Patient Care Plan Nursing (485)'
	open: 'read'
