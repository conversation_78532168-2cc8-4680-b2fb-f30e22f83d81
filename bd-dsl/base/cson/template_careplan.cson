fields:

	name:
		model:
			max: 64
			required: true
		view:
			label: 'Name'

	problems:
		model:
			subfields:
				problem:
					label: 'Problem'
					type: 'text'
			type: 'json'
		view:
			control: 'grid'
			label: 'Problems'

	goals:
		model:
			subfields:
				goal:
					label: 'Goal'
					type: 'text'
			type: 'json'
		view:
			control: 'grid'
			label: 'Goals'

	interventions:
		model:
			subfields:
				intervention:
					label: 'Intervention'
					type: 'text'
			type: 'json'
		view:
			control: 'grid'
			label: 'Interventions'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['templates']
	indexes:
		many: [
			['name']
		]
		unique: [
			['name']
		]
	name: '{name}'
	sections:
		'Care Plan Template':
			fields: ['name', 'problems', 'goals', 'interventions']

view:
	comment: 'Manage > Care Plan Template'
	find:
		basic: ['name']
	grid:
		fields: ['name', 'created_by', 'created_on', 'updated_on', 'updated_by']
		sort: ['name']
	label: 'Care Plan Template'
