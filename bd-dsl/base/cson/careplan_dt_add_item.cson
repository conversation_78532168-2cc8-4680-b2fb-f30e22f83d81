fields:

	ticket_no:
		model:
			required: true
		view:
			label: 'Ticket No'
			readonly: true
			offscreen: true

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			readonly: true
			offscreen: true

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			readonly: true
			offscreen: true

	rx_fltr:
		model:
			multi: true
			source: 'careplan_order_rx'
		view:
			label: 'Associated Rx'
			offscreen: true
			readonly: true

	add_skt:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['supply_kit_id', 'billable_id']
					prefill:
						inventory_id: ''
				'!':
					fields: ['inventory_id']
					prefill:
						supply_kit_id: ''
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Add Supply Kit?'
			columns: 2

	rx_id:
		model:
			required: true
			source: 'careplan_order_rx'
			sourcefilter:
				'id':
					'dynamic': '{rx_fltr}'
		view:
			label: 'Associated Rx'
			columns: 2

	inventory_id:
		model:
			required: true
			source: 'inventory'
			query: 'select_inv_in_stock_dt'
			querytemplate: 'inventoryTemplate'
			sourcefilter:
				type:
					'static': ['Supply', 'Billable', 'Equipment Rental']
				active:
					'static': 'Yes'
			if:
				'*':
					fields: ['type']
		view:
			form_link_enabled: true
			columns: 2
			label: 'Item'
			class: 'select_prefill'
			transform: [{
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'type': ['type'],
					'quantity_per_container': ['quantity_per_container']
					'container_unit': ['container_unit']
			}
			]

	type:
		model:
			required: true
			source: ['Supply', 'Billable', 'Equipment Rental']
			if:
				'Equipment Rental':
					fields: ['rental_type']
					note: ''
				'Supply':
					fields: ['dispense_unit', 'quantity_per_container', 'container_unit', 'dispense_quantity']
				'Billable':
					fields: ['dispense_quantity']
					note: ''
		view:
			label: 'Item Type'
			offscreen: true
			readonly: true

	quantity_per_container:
		model:
			default: 1
			required: false
			type: 'decimal'
			max: 9999999.999
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Container Units'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'inventory_id'
					source_fields: ['quantity_per_container', 'container_unit']
					separator: ' per '
				}
			]

	container_unit:
		model:
			default: 'container'
		view:
			label: 'Container Unit'
			readonly: true
			offscreen: true

	dispense_unit:
		model:
			default: 'each'
			required: true
			source: ['each', 'container']
			if:
				'container':
					fields: ['dispense_containers']
					readonly:
						fields: ['dispense_quantity']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Dispense Unit'

	dispense_containers:
		model:
			min: 1
			default: 1
			type: 'int'
			required: true
		view:
			columns: 4
			label: 'Dispense Quantity'
			note: 'containers'
			transform: [
				{
					name: 'MultiplyFields'
					fields: ['quantity_per_container', 'dispense_containers']
					destination: 'dispense_quantity'
				}
			]

	rental_type:
		model:
			required: true
			source: ['Purchase', 'Rental']
			if:
				'Rental':
					fields: ['frequency_code', 'day_supply']
		view:
			label: 'Rental Type'
			columns: 4

	frequency_code:
		model:
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SV506'
		view:
			columns: 4
			class: 'claim-field'
			label: 'Rental Frequency'
			reference: 'SV506'

	dispense_quantity:
		model:
			default: 1
			min: 1
			type: 'decimal'
			max: 9999999.999
			rounding: 1
			required: true
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Dispense Quantity'
			note: 'each'

	day_supply:
		model:
			default: 30
			required: true
			max: 365
			min: 1
			type: 'int'
		view:
			columns: 4
			class: 'claim-field'
			label: 'Rental Days'

	# Supply Kit field
	supply_kit_id:
		model:
			required: true
			source: 'inventory_supply_kit'
			if:
				'*':
					sections: ['Supply Kit Preview']
					fields: ['preview_skt']
		view:
			columns: 2
			label: 'Select Supply Kit'
			class: 'select_prefill'
			transform: [{
				name: 'SelectPrefill'
				url: '/form/inventory_supply_kit/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'billable_id': ['billable_id'],
			},
			{
				name: 'EmbedRefresh'
				fields: ['preview_skt']
			}
			]

	billable_id:
		model:
			required: false
			source: 'inventory'
			sourcefilter:
				type:
					'static': ['Billable']
		view:
			columns: 2
			label: 'Supply Kit Billable'
			readonly: true

	preview_skt:
		model:
			multi: true
			sourcefilter:
				_x1:
					'dynamic': '{ticket_no}'
				_x2:
					'dynamic': '{supply_kit_id}'
		view:
			embed:
				query: 'ticket_sk_preview'
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['inventory_id', 'dispense_quantity_formatted', 'part_of_kit', 'bill', 'one_time_only']
				label: ['Item', 'Quantity',  'Billed as Kit?', 'Bill?', '1x Only']
				width: [40, 15, 15, 15, 15]
				selectall: true
			label: 'Preview Supply Kit Contents'

model:
	save: false
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']

	reportable: false
	name: ['created_on', 'created_by']

	sections:
		'Add Item':
			indent: false
			hide_header: true
			fields: ['ticket_no', 'patient_id', 'site_id', 'rx_fltr',
			'add_skt', 'rx_id', 'inventory_id','type', 'rental_type',
			'frequency_code', 'quantity_per_container',  'container_unit', 
			'dispense_unit', 'dispense_containers', 'dispense_quantity',
			'day_supply', 'supply_kit_id', 'billable_id']
		'Supply Kit Preview':	
			indent: false
			hide_header: true
			fields: ['preview_skt']
view:
	dimensions:
		width: '65%'
		height: '55%'
	hide_cardmenu: true
	comment: 'Add Item'
	grid:
		fields: ['ticket_no', 'patient_id', 'site_id', 'rx_id']
	label: 'Add Item'
	open: 'edit'
