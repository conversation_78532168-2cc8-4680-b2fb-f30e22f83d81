fields:
	date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Date'
			template: '{{now}}'
			findrange: true

	type:
		model:
			required: true
			source: ['Referral Source', 'Non-Referral Source','Patient']
			if:
				'Referral Source':
					fields: ['sales_account_id','phys_in_office','non_presc',
					'non_presc_num','purpose','courtesy_provided','num_individuals',
					'cost','cost_per_provider','tot_year']
				'Non-Referral Source':
					fields: ['company_name', 'employer', 'employer_location',
					'courtesy_provided', 'location',
					'cost', 'individuals', 'topic']
				'Patient':
					fields: ['pat_type', 'pat_id','provided','location','cost',
					'individuals','topic','tot_year_pt']
		view:
			label: 'Type'
			control: 'radio'

	sales_account_id:
		model:
			required: true
			source: 'sales_account'
			type: 'int'
			if:
				'*':
					fields: ['dr_id']
		view:
			label: 'Sales Account'
			transform: [
					name: 'CalcCost'
			]

	dr_id:
		model:
			multi: true
			required: true
			source: 'sales_account_physician'
			sourcefilter:
				sales_account_id:
					'dynamic': '{sales_account_id}'
		view:
			label: 'Physicians'

	pat_type:
		model:
			required: true
			source: ['Active', 'Prospect']
			if:
				'Active':
					require_fields: ['pat_id']
				'Prospect':
					fields: ['pat_name']
		view:
			label: 'Type'
			control: 'radio'

	pat_id:
		model:
			source: 'patient'
		view:
			label: 'Patient'
			transform: [
					name: 'CalcCost'
			]

	pat_name:
		model:
			required: true
		view:
			label: 'Patient Name'

	phys_in_office:
		model:
			required: true
			type: 'int'
		view:
			label: '# of Physicians at Office'
			note: '(that location only)'
			transform: [
					name: 'CalcCost'
			]

	non_presc:
		model:
			required: false
		view:
			label: 'Non-Physician Prescribers'
			note: '(that location only)'

	non_presc_num:
		model:
			required: false
			type: 'int'
		view:
			label: '# of Non-Physician Prescribers'
			note: '(that location only)'
	
	purpose:
		model:
			required: true
		view:
			label: 'Purpose of Meeting'
	
	courtesy_provided:
		model:
			required: true
		view:
			label: 'Courtesy Provided'
			note: '(include location if other than office)'

	provided:
		model:
			required: true
		view:
			label: 'What was provided?'

	num_individuals:
		model:
			required: true
			type: 'int'
		view:
			label: '# of Individuals Present'

	individuals:
		model:
			required: true
		view:
			label: 'Individuals Present'

	topic:
		model:
			required: true
		view:
			label: 'Topic of Discussion'

	company_name:
		model:
			required: true
		view:
			label: 'Company Name'

	employer:
		model:
			required: true
		view:
			label: 'Employer'

	employer_location:
		model:
			required: true
		view:
			label: 'Employer Location'

	location:
		model:
			required: true
		view:
			label: 'Location of Meeting'

	cost:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Cost'
			transform: [
					name: 'CalcCost'
			]

	cost_per_provider:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Cost Per Provider'
			readonly: true

	tot_year:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Total for Year as of this Date'
			readonly: true

	tot_year_pt:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Total Spent on this Patient this Year'
			readonly: true

	subform_attachment:
		model:
			multi: true
			source: 'sales_attachment'
			type: 'subform'
		view:
			label: 'File Attachments'
			note: 'Max 100MB. Only documents, images, and archives supported.'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison']
		delete:     ['admin']
		read:       ['admin', 'csr', 'liaison']
		read_all:   ['admin', 'csr', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'liaison']
		write:      ['admin', 'csr', 'liaison']
	reportable: true
	indexes:
		unique: [
			['type', 'pat_name']
		]
	name: ['date', 'pat_name', 'type', 'sales_account_id']
	sections_group: [
		'Courtesy Call':
			sections: [
				'Details':
					fields: ['date', 'type','sales_account_id', 'dr_id',
					'pat_type', 'pat_name', 'pat_id', 'phys_in_office', 'non_presc', 'non_presc_num',
					'purpose', 'courtesy_provided', 'provided', 'num_individuals',
					'individuals', 'topic','company_name', 'employer', 'employer_location',
					'location', 'cost', 'cost_per_provider', 'tot_year',
					'tot_year_pt']
				'Attachments':
					fields: ['subform_attachment']
			]
	]
view:
	comment: 'Courtesy Call'
	find:
		basic: ['date', 'type', 'sales_account_id', 'dr_id', 'pat_id', 'pat_type']
	grid:
		fields: ['date', 'type', 'sales_account_id', 'dr_id', 'pat_id', 'location', 'topic', 'cost']
		sort: ['-date']
	label: 'Courtesy Call'
