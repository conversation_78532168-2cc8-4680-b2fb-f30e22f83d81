fields:

	pricing_update_no:
		view:
			label: 'Pricing Update No'
			readonly: true
			offscreen: true

	price_matrix_item_id:
		model:
			required: true
			source: 'payer_price_matrix_item'
		view:
			label: 'Price Matrix Item'
			readonly: true
			offscreen: true

	contract_id:
		model:
			required: true
			source: 'payer_contract'
		view:
			columns: 2
			label: 'Contract'
			readonly: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
		view:
			columns: 2
			label: 'Item'
			readonly: true

	prev_expected_price:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'Each'
			label: 'Previous Expected Price'
			readonly: true
			columns: 4

	updated_expected_price:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'Each'
			label: 'Updated Expected Price'
			readonly: true
			columns: 4

	prev_special_price:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'Each'
			label: 'Previous Special Price'
			readonly: true
			columns: 4

	updated_special_price:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'Each'
			label: 'Updated Special Price'
			readonly: true
			columns: 4

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin','pharm','csr','cm', 'nurse']
		read_all:   ['admin','pharm','csr','cm', 'nurse']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      []
	bundle: ['audit']
	indexes:
		many: [
			['pricing_update_no'],
			['inventory_id'],
			['prev_expected_price'],
			['updated_expected_price'],
			['prev_special_price'],
			['updated_special_price']
		]

	name: ['inventory_id']
	sections:
		'Contract Pricing Update Ledger':
			hide_header: true
			indent: false
			fields: ['pricing_update_no', 'contract_id', 'inventory_id', 'prev_expected_price', 'updated_expected_price', 'prev_special_price',
			'updated_special_price']

view:
	dimensions:
		width: '45%'
		height: '45%'
	hide_cardmenu: true
	comment: 'Contract Pricing Update Ledger'
	find:
		basic: ['inventory_id']
		advanced: []
	grid:
		fields: ['inventory_id', 'updated_expected_price', 'updated_special_price']
		sort: ['-id']
	label: 'Contract Pricing Update Ledger'
	open: 'read'