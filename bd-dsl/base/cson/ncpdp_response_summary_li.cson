fields:
	response_uuid:
		model:
			type: 'text'
		view:
			label: 'Response UUID'
			readonly: true
			offscreen: true

	type:
		model:
			source: ['Ingr Cost', 'Disp Fee', 'Incent Amt', 'Other Amt']
		view:
			columns: 2
			control: 'radio'
			label: 'Type'
			readonly: true

	billed_amount:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 4
			label: 'Billed Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	paid_amount:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 4
			label: 'Paid Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	discount_amount:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			columns: 4
			label: 'Discount Amount'
			class: 'numeral discount'
			format: '$0,0.00'
			readonly: true

	paid_percentage:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 100
		view:
			columns: 4
			label: 'Paid %'
			class: 'numeral discount'
			format: 'percent'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['response_uuid']
		]
	name: 'Response Pricing'
	sections:
		'Totals':
			fields: ['response_uuid', 'type', 'billed_amount', 'paid_amount', 'discount_amount', 'paid_percentage']

view:
	dimensions:
		width: '80%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Response Pricing'
	grid:
		fields: ['type', 'billed_amount', 'paid_amount', 'discount_amount', 'paid_percentage']
		sort: ['-created_on']
	label: 'Response Pricing'
	open: 'read'
