fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Questionnaire Participation
	participates:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'No'
			if:
				'Yes':
					sections: ['Questionnaire']
		view:
			findfilter: 'Yes'
			control: 'radio'
			label: 'Will a Myasthenia Gravis Activities of Daily Living (MG-ADL) be completed on this visit?'

	# Questionnaire
	talking:
		model:
			source: ['Normal', 'Intermittent slurring or nasal speech', 'Constant slurring or nasal speech, but can be understood', 'Difficult-to-understand speech']
		view:
			control: 'radio'
			label: "Talking"
			validate: [
					name: 'MGADLScoreValidate'
			]

	chewing:
		model:
			source: ['Normal', 'Fatigue with solid food', 'Fatigue with soft food', 'Gastric tube']
		view:
			control: 'radio'
			label: "Chewing"
			validate: [
					name: 'MGADLScoreValidate'
			]

	swallowing:
		model:
			source: ['Normal', 'Rare episode of choking', 'Frequent choking necssitating changes in diet', 'Gastric tube']
		view:
			control: 'radio'
			label: "Swallowing"
			validate: [
					name: 'MGADLScoreValidate'
			]

	breathing:
		model:
			source: ['Normal', 'Shortness of breath with exertion', 'Shortness of breath at rest', 'Ventilator dependence']
		view:
			control: 'radio'
			label: "Breathing"
			validate: [
					name: 'MGADLScoreValidate'
			]

	grooming:
		model:
			source: ['None', 'Extra effort, but no rest periods needed', 'Rest periods needed', 'Cannot do one of these functions']
		view:
			control: 'radio'
			label: "Impairment of ability to brush teeth or comb hair"
			validate: [
					name: 'MGADLScoreValidate'
			]

	arise:
		model:
			source: ['None', 'Mild, sometimes uses arms', 'Moderate, always uses arms', 'Severe, requires assistance']
		view:
			control: 'radio'
			label: "Impairment of ability to arise from a chair"
			validate: [
					name: 'MGADLScoreValidate'
			]

	vision:
		model:
			source: ['None', 'Occurs, but not daily', 'Daily, but not constant', 'Constant']
		view:
			control: 'radio'
			label: "Double vision"
			validate: [
					name: 'MGADLScoreValidate'
			]

	eyelid:
		model:
			source: ['None', 'Occurs, but not daily', 'Daily, but not constant', 'Constant']
		view:
			control: 'radio'
			label: "Eyelid droop"
			validate: [
					name: 'MGADLScoreValidate'
			]

	score:
		model:
			min: 0
			max: 24
			type: 'int'
		view:
			label: 'Assessment Score'
			readonly: true

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_mgadl:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Questionnaire':
			fields: ['talking', 'chewing', 'swallowing', 'breathing', 'grooming', 'arise', 'vision', 'eyelid', 'score']
			prefill: 'clinical_mgadl'
view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Myasthenia Gravis Activities of Daily Living (MG-ADL)'
	label: 'Myasthenia Gravis Activities of Daily Living (MG-ADL)'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by', 'score']
		sort: ['-id']