fields:
	name:
		model:
			max: 128
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 2

	slug:
		model:
			required: true
		view:
			label: 'Slug'
			columns: 2

	dashboard_id:
		model:
			type: 'text'
			required: true
		view:
			label: 'Dashboard ID'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	indexes:
		unique: [
			['slug']
		]
	bundle: ['manage']
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name', 'slug', 'dashboard_id']

view:
	comment: 'BI Dashboard'
	find:
		basic: ['name', 'slug']
	grid:
		fields: ['name', 'slug']
		sort: ['name', 'slug']
	label: 'Embed Dashboard'
