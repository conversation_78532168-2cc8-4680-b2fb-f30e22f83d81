fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Difficulties
	diff_exercise:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = Normal)'
			label: 'Intolerance to exercise'

	diff_speech:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unintelligible, 1 = Slurred, 2 = Normal)'
			label: 'Talking / Slurred Speech'

	diff_chew:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Chewing'

	diff_swallow:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficulty (i.e. choking), 2 = Normal)'
			label: 'Swallowing'

	diff_hold_arms:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Partial, 2 = Normal)'
			label: 'Lifting arms (straight) over head'

	diff_breath_flat:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severely impaired, 1 = Moderately impaired, 2 = Normal)'
			label: 'Breathing while lying flat'

	diff_breath_act:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severely impaired, 1 = Moderately impaired, 2 = Normal)'
			label: 'Breathing with activity'

	diff_droop:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe (impairs vision), 1 = Moderate, 2 = Normal)'
			label: 'Drooping eyelids'

	diff_blur:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate or intermittent, 2 = Normal)'
			label: 'Double or blurred vision'

	diff_fatigue:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Fatigue'

	diff_muscle:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Muscle ache'

	diff_walk:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficult or requires assistance, 2 = Normal)'
			label: 'Walking 10 feet'

	# Timed arm suspensions
	arms_sus:
		model:
			max: 256
			min: 1
			multi:false
			source: ['Unable to raise both arms', 'Either arm less than 60 seconds', 'Both arms more than 60 seconds']
		view:
			control: 'radio'
			label: 'Raise Arms'

	# Timed upgaze
	gaze:
		model:
			max: 256
			min: 1
			multi:false
			source: ['No upgaze', 'Upgaze less than 60 seconds', 'Upgaze more than 60 seconds']
		view:
			control: 'radio'
			label: 'Upgaze'

	# Pulse oximetry
	oxy_sat:
		model:
			min: 1
			max: 100
			type: 'int'
		view:
			label: 'Oxygen saturation (%)'

	oxy_sat_time:
		model:
			type: 'time'
		view:
			label: 'Time of day'

	# Wellness
	treat_reason:
		model:
			max: 32
			source: ['Prevent recurrence?', 'Improve current symptoms?']
		view:
			control: 'radio'
			label: 'Are you being treated to:'


model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_mygrav:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Difficulties':
			note: 'On a scale of zero to two (0 - 2), with 0 meaning Extreme Difficulty and 2 meaning Normal, please rate your difficulty today with:'
			fields: ['diff_exercise', 'diff_speech', 'diff_chew', 'diff_swallow', 'diff_hold_arms',
				'diff_breath_flat', 'diff_breath_act', 'diff_droop', 'diff_blur', 'diff_fatigue', 'diff_muscle',
				'diff_walk']
		'Timed arm suspensions':
			note: 'Time how long the patient can hold his/her arms out without falling (0-60 seconds).'
			fields: ['arms_sus']
		'Timed upgaze':
			note: 'Time how long the patient can gaze upward without his/her eyelids drooping (fatigable weakness)'
			fields: ['gaze']
		'Pulse oximetry':
			fields: ['oxy_sat', 'oxy_sat_time']
		'Wellness':
			fields: ['treat_reason']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Myasthenia Gravis Difficulties'
	label: 'Myasthenia Gravis Difficulties'
