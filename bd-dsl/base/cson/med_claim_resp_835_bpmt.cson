fields:

	assigned_number:
		view:
			columns: 4
			label: 'Assigned Number'
			readonly: true
			_meta:
				path: 'transactions[].detailInfo[*].assignedNumber'

	provider_identifier:
		view:
			columns: 4
			label: 'Provider Identifier'
			readonly: true
			_meta:
				path: 'transactions[].detailInfo[*].providerSummaryInformation.providerIdentifier'

	facility_type_code:
		view:
			columns: 4
			label: 'Facility Type Code'
			readonly: true
			_meta:
				path: 'transactions[].detailInfo[*].providerSupplementalSummaryInformation.facilityTypeCode'

	fiscal_period_date:
		view:
			columns: 4
			label: 'Fiscal Period Date'
			readonly: true
			_meta:
				path: 'transactions[].detailInfo[*].providerSupplementalSummaryInformation.fiscalPeriodDate'

	total_claim_count:
		view:
			columns: -4
			label: 'Total Claim Count'
			readonly: true
			_meta:
				path: 'transactions[].detailInfo[*].providerSupplementalSummaryInformation.totalClaimCount'

	total_claim_charge_amount:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Claim Charge Amount'
			readonly: true
			_meta:
				path: 'transactions[].detailInfo[*].providerSupplementalSummaryInformation.totalClaimChargeAmount'

	total_hcpcs_reported_charge_amount:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: -4
			class: 'numeral money'
			label: 'Total HCPCS Reported Charge Amount'
			readonly: true
			_meta:
				path: 'transactions[].detailInfo[*].providerSupplementalSummaryInformation.totalHCPCSReportedChargeAmount'

	total_hcpcs_payable_amount:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			label: 'Total HCPCS Payable Amount'	
			readonly: true
			_meta:
				path: 'transactions[].detailInfo[*].providerSupplementalSummaryInformation.totalHCPCSPayableAmount'

	total_patient_reimbursement_amount:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Patient Reimbursement Amount'
			readonly: true
			_meta:
				path: 'transactions[].detailInfo[*].providerSupplementalSummaryInformation.totalPatientReimbursementAmount'

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: 'Batch Payment Details'
	sections:
		'Batch Payment Details':
			fields: ['assigned_number', 'provider_identifier', 'facility_type_code',
			'fiscal_period_date', 'total_claim_count', 'total_claim_charge_amount',
			'total_hcpcs_reported_charge_amount', 'total_hcpcs_payable_amount',
			'total_patient_reimbursement_amount']
view:
	dimensions:
		width: '85%'
		height: '45%'
	hide_cardmenu: true
	comment: 'Batch Payment Details'
	grid:
		fields: ['total_claim_count', 'total_claim_charge_amount', 'total_hcpcs_payable_amount', 'total_patient_reimbursement_amount']
		label: ['# Claims', 'Total Charge $', 'Total Paid $', 'Total Pt Reimbursed $']
		width: [10, 30, 30, 30]
		sort: ['-created_on']
	label: 'Batch Payment Details'
	open: 'read'