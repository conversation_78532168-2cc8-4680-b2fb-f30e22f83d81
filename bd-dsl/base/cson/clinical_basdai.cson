fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_basdai.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	# Questionnaire
	fatigue:
		model:
			multi:false
			source:
				0: '0 = None'
				1: '1'
				2: '2'
				3: '3'
				4: '4'
				5: '5 = Moderate'
				6: '6'
				7: '7'
				8: '8'
				9: '9'
				10: '10 = Severe'
		view:
			control: 'checkbox'
			note: 'How would you describe the overall level of fatigue/tiredness you have experienced?'
			validate: [
					name: 'BASDAIScoreValidate'
			]

	pain:
		model:
			multi:false
			source:
				0: '0 = None'
				1: '1'
				2: '2'
				3: '3'
				4: '4'
				5: '5 = Moderate'
				6: '6'
				7: '7'
				8: '8'
				9: '9'
				10: '10 = Severe'
		view:
			control: 'checkbox'
			note: 'How would you describe the overall level of AS neck, back, or hip pain you have had?'
			validate: [
					name: 'BASDAIScoreValidate'
			]

	swelling:
		model:
			multi:false
			source:
				0: '0 = None'
				1: '1'
				2: '2'
				3: '3'
				4: '4'
				5: '5 = Moderate'
				6: '6'
				7: '7'
				8: '8'
				9: '9'
				10: '10 = Severe'
		view:
			control: 'checkbox'
			note: 'How would you describe the overall level of pain/swelling in joints other than neck, back, or hips you have had?'
			validate: [
					name: 'BASDAIScoreValidate'
			]

	discomfort_pressure:
		model:
			multi:false
			source:
				0: '0 = None'
				1: '1'
				2: '2'
				3: '3'
				4: '4'
				5: '5 = Moderate'
				6: '6'
				7: '7'
				8: '8'
				9: '9'
				10: '10 = Severe'
		view:
			control: 'checkbox'
			note: 'How would you describe the overall level of discomfort you have had from any areas tender to touch or pressure?'
			validate: [
					name: 'BASDAIScoreValidate'
			]

	discomfort_wakeup:
		model:
			multi:false
			source:
				0: '0 = None'
				1: '1'
				2: '2'
				3: '3'
				4: '4'
				5: '5 = Moderate'
				6: '6'
				7: '7'
				8: '8'
				9: '9'
				10: '10 = Severe'
		view:
			control: 'checkbox'
			note: 'How would you describe the overall level of discomfort you have had from the time you wake up?'
			validate: [
					name: 'BASDAIScoreValidate'
			]

	stiffness:
		model:
			multi:false
			source:
				0: '0 = 0 hours'
				1: '1'
				2: '2'
				3: '3'
				4: '4'
				5: '5 = 1 hour'
				6: '6'
				7: '7'
				8: '8'
				9: '9'
				10: '10 = 2 or more hours'
		view:
			control: 'checkbox'
			note: 'How long does your morning stiffness last from the time you wake up?'
			validate: [
					name: 'BASDAIScoreValidate'
			]

	score:
		model:
			rounding: 0.1
			type: 'decimal'
		view:
			label: 'BASDAI Score'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_basdai:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'BASDAI Questionnaire':
			fields: ['last_assessment_date', 'assessment_date']
		'Questionnaire':
			note: 'Please indicate your answer relating to the past week'
			fields: ['fatigue', 'pain', 'swelling', 'discomfort_pressure', 'discomfort_wakeup', 'stiffness', 'score']
			prefill: 'clinical_bandai'
view:
	hide_cardmenu: true
	grid:
		fields: ['created_on', 'created_by', 'assessment_date', 'score']
		sort: ['-id']
	comment: 'Patient > Careplan > Clinical >  Bath Ankylosing Spondylitis Disease Activity Index (BASDAI)'
	label: 'Bath Ankylosing Spondylitis Disease Activity Index (BASDAI)'
