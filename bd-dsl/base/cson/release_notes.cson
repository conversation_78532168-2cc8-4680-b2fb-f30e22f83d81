fields:
	user_id:
		model:
			required: true
			source: 'user'
			type: 'int'
		view:
			label: 'User ID'
			readonly: true
			offscreen: true

	version:
		model:
			type: 'text'
		view:
			label: 'Version'
			readonly: true

	notes:
		model:
			type: 'text'
		view:
			label: 'Note'
			control: 'area'
			readonly: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		delete:     ['admin', 'pharm']
		read:       []
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
	bundle: ['audit']
	indexes:
		many: [
			['user_id']
		]

	name: ['version']
	sections:
			'Release Notes':
				fields: ['user_id', 'version', 'notes']

view:
	comment: 'Release Note'
	label: 'Release Notes'
	grid:
		fields: ['user_id', 'version', 'notes']
	open: 'read'

