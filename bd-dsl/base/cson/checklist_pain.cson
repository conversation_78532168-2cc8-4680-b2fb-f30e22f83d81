fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	contact_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	contact_time:
		model:
			type: 'time'
			required: true
		view:
			label: 'Time'
			template: '{{now}}'

	pain_locations:
		model:
			subfields:
				int_ext:
					label: 'Internal/External'
					source: ['Internal', 'External']
					type: 'text'
				location:
					label: 'Location'
					type: 'text'
				description:
					label: 'Description'
					type: 'text'
			type: 'json'
		view:
			control: 'grid'
			label: 'Pain Location(s)'

	patient_participates_pain:
		model:
			max: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pain_present', 'pain_worst', 'pain_best',
					'pain_accept', 'pain_qual', 'pain_begin', 'pain_trigger',
					'pain_pattern', 'pain_worse', 'pain_time', 'pain_better',
					'pain_symp', 'pain_effects']
		view:
			control: 'radio'
			note: 'No if non-verbal or adolescent'
			label: 'Will patient perform pain assessment?'

	pain_present:
		model:
			max: 2
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Present Pain Intensity'

	pain_worst:
		model:
			max: 2
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Worst Pain Intensity'

	pain_best:
		model:
			max: 2
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Best Pain Intensity'

	pain_accept:
		model:
			max: 2
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Acceptable Pain Intensity'

	pain_qual:
		view:
			control: 'area'
			note: "Use patient's own words"
			label: 'Quality of Pain'

	pain_begin:
		view:
			label: 'How and when does pain begin?'

	pain_trigger:
		view:
			label: 'What triggers your pain?'

	pain_pattern:
		view:
			control: 'area'
			note: 'Continuous vs. Intermittent. Onset, duration or rhythms of pain.'
			label: 'Patterns or changes of pain.'

	pain_worse:
		view:
			note: 'during movement, exercise'
			label: 'What makes the pain worse?'

	pain_time:
		model:
			multi: true
			source: ['Morning', 'Afternoon', 'Evening']
		view:
			control: 'checkbox'
			label: 'What time of day does the pain occur?'

	pain_better:
		view:
			note: 'movement, taking of pain meds'
			label: 'What makes the pain better?'

	pain_symp:
		view:
			control: 'area'
			label: 'Other symptoms accompanying pain'

	pain_effects:
		model:
			multi: true
			source: ['Sleep', 'Appetite', 'Physical Activity', 'Relationships with others', 'Concentration', 'Emotions']
			if:
				'Sleep':
					fields: ['sleep_effects']
				'Appetite':
					fields: ['appetite_effects']
				'Physical Activity':
					fields: ['activity_effects']
				'Relationships with others':
					fields: ['relationship_effects']
				'Concentration':
					fields: ['concentration_effects']
				'Emotions':
					fields: ['emotion_effects']
		view:
			control: 'checkbox'
			label: 'Pain Effects:'

	sleep_effects:
		model:
			required: true
		view:
			label: 'Pain Effects On Sleep'

	appetite_effects:
		model:
			required: true
		view:
			label: 'Pain Effects On Appetite'

	activity_effects:
		model:
			required: true
		view:
			label: 'Pain Effects On Physical Activity'

	relationship_effects:
		model:
			required: true
		view:
			label: 'Pain Effects On Relationships'

	concentration_effects:
		model:
			required: true
		view:
			label: 'Pain Effects On Concentration'

	emotion_effects:
		model:
			required: true
		view:
			label: 'Pain Effects On Emotions'

	patient_participates_wongbaker:
		model:
			max: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['wong_baker_score', 'observed_by']
		view:
			control: 'radio'
			label: 'Will patient perform wong baker score?'

	wong_baker_score:
		model:
			source: ['No Hurt (0)', 'Hurts Little Bit (2)',
			'Hurts Little More (4)', 'Hurts Even More (6)',
			'Hurts Whole Lot (8)', 'Hurts Worst (10)', 'N/A']
		view:
			control: 'radio'
			label: 'Wong-Baker Score'

	observed_by:
		model:
			source: ['Nurse', 'Parent', 'Caretaker']
		view:
			control: 'radio'
			label: 'Pain Scale Observed By:'

	patient_participates_flacc:
		model:
			max: 2
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['flacc_face', 'flacc_legs', 'flacc_activity', 'flacc_cry', 'flacc_consolability', 'flacc_observed_by', 'flacc_score']
		view:
			control: 'radio'
			label: 'Will patient perform FLACC assessment?'
	
	flacc_face:
		model:
			source: ['No particular expression or smile', 'Occasional grimace or frown, withdrawn, uninterested',
			'Frequent to constant quivering chin, clenched jaw', 'N/A']
		view:
			control: 'radio'
			label: 'Face'
			validate: [
				name: 'FLACCScoreValidate'
			]

	flacc_legs:
		model:
			source: ['Normal position or relaxed', 'Uneasy, restless, tense',
			'Kicking, or legs drawn up', 'N/A']
		view:
			control: 'radio'
			label: 'Legs'
			validate: [
				name: 'FLACCScoreValidate'
			]

	flacc_activity:
		model:
			source: ['Lying quietly, normal position, moves easily', 'Squirming, shifting, back and forth, tense',
			'Arched, rigid or jerking', 'N/A']
		view:
			control: 'radio'
			label: 'Activity'
			validate: [
				name: 'FLACCScoreValidate'
			]

	flacc_cry:
		model:
			source: ['No cry (awake or asleep)', 'Moans or whimpers; occasional complaint',
			'Crying steadily, screams or sobs, frequent complaints', 'N/A']
		view:
			control: 'radio'
			label: 'Cry'
			validate: [
				name: 'FLACCScoreValidate'
			]

	flacc_consolability:
		model:
			source: ['Content, relaxed', 'Reassured by occasional touching, hugging or being talked to, distractible',
			'Difficult to console or comfort', 'N/A']
		view:
			control: 'radio'
			label: 'Consolability'
			validate: [
				name: 'FLACCScoreValidate'
			]

	flacc_observed_by:
		model:
			source: ['Nurse', 'Parent', 'Caretaker']
		view:
			control: 'radio'
			label: 'FLACC Scale Observed By:'

	flacc_score:
		view:
			readonly: true
			label: 'FLACC Score'

	pain_med:
		view:
			label: 'Current Pain Medications'

	pain_med_effect:
		model:
			source: ['Effective', 'Not Effective']
			if:
				'Not Effective':
					fields: ['physician_notified']
		view:
			control: 'radio'
			label: 'Current Pain Medications Regimen Effective'

	physician_notified:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Physician Notified?'

	pain_management_supportive:
		model:
			multi: true
			source: ['Cryo-Cuff', 'Ice', 'Elevation', 'Rest',
			'Immobilization', 'Heat', 'Assistive Device',
			'Distraction', 'Other']
			if:
				'Other':
					fields: ['pain_management_supportive_details']
		view:
			control: 'checkbox'
			note: 'Check all that apply'
			label: 'Supportive Pain Management'

	pain_management_supportive_details:
		view:
			label: 'Supportive Pain Management Details'

	comment:
		view:
			control: 'area'
			label: 'Comments'
	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['careplan_id', 'order_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		checklist_pain:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections_group: [
		'Patient Pain Assessment':
			sections: [
				'Contact Date/Time':
					fields: ['contact_date', 'contact_time']
				'Pain Location(s)':
					fields: ['pain_locations']
				'Pain Details':
					fields: ['patient_participates_pain', 'pain_present', 'pain_worst', 'pain_best',
					'pain_accept', 'pain_qual', 'pain_begin', 'pain_trigger',
					'pain_pattern', 'pain_worse', 'pain_time', 'pain_better',
					'pain_symp', 'pain_effects', 'sleep_effects',
					'appetite_effects', 'activity_effects',
					'relationship_effects', 'concentration_effects',
					'emotion_effects']
				'Wong-Baker Score':
					fields: ['patient_participates_wongbaker', 'wong_baker_score', 'observed_by']
				'FLACC Score':
					note: 'Pain measurement scale for children 2 months to 7 years that are unable to communicate their pain'
					fields: ['patient_participates_flacc', 'flacc_face', 'flacc_legs', 'flacc_activity', 'flacc_cry', 'flacc_consolability', 'flacc_observed_by', 'flacc_score']
				'Pain Regimen':
					fields: ['pain_med', 'pain_med_effect',
					'physician_notified', 'pain_management_supportive', 'pain_management_supportive_details']
				'Comments':
					fields: ['comment']
			]
	]

	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Patient Pain Assessment"
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Checklist > Pain'
	grid:
		fields: ['created_on', 'created_by', 'pain_present', 'pain_effects']
		sort: ['created_on']
	label: 'Patient Pain Assessment'
	open: 'read'
