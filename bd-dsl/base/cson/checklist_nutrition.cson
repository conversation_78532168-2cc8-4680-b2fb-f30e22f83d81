fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	contact_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	contact_time:
		model:
			type: 'time'
			required: true
		view:
			label: 'Time'
			template: '{{now}}'

	score:
		model:
			type: 'int'
		view:
			readonly: true
			label: 'Score'

	have_ill:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			note: '2 points'
			label: 'I have an illness or condition that made me change the kind and/or amount of food I eat'
			validate: [
					name: 'NoteScoreValidate'
			]

	eat_few:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			note: '3 points'
			label: 'I eat fewer than 2 meals per day'
			validate: [
					name: 'NoteScoreValidate'
			]

	eat_fruit:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			note: '2 points'
			label: 'I eat few fruits, vegetables, or milk products'
			validate: [
					name: 'NoteScoreValidate'
			]

	drink_beer:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			note: '2 points'
			label: 'I have 3 or more drinks of beer, liquor, or wine almost every day'
			validate: [
					name: 'NoteScoreValidate'
			]

	tooth_prob:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			note: '2 points'
			label: 'I have a tooth or mouth problem that makes it hard for me to eat'
			validate: [
					name: 'NoteScoreValidate'
			]

	money_prob:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			note: '4 points'
			label: "I don't always have enough money to buy the food that I need"
			validate: [
					name: 'NoteScoreValidate'
			]

	eat_alone:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			note: '1 points'
			label: "I eat alone most of the time"
			validate: [
					name: 'NoteScoreValidate'
			]

	take_drugs:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			note: '1 points'
			label: "I take 3 or more different prescribed or over-the-counter drugs per day"
			validate: [
					name: 'NoteScoreValidate'
			]

	weight_change:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			note: '2 points'
			label: "Without wanting to, I have lost or gained 10 or more pounds of the last 6 months"
			validate: [
					name: 'NoteScoreValidate'
			]

	unable_shop:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			note: '2 points'
			label: "I'm sometimes unable to shop, cook, and/or feed myself"
			validate: [
					name: 'NoteScoreValidate'
			]

	comment:
		view:
			control: 'area'
			label: 'Comments'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		checklist_nutrition:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['careplan_id', 'order_id']
	sections:
		'Contact Date/Time':
			fields: ['contact_date', 'contact_time']
		'Nutritional Assessment':
			note: '0-2 points: No Risk, 3-5 points: Moderate Risk (Educate Patient), 6-21 points: High Risk (Notify Physician)'
			fields: ['score', 'have_ill', 'eat_few', 'eat_fruit', 'drink_beer',
					 'tooth_prob', 'money_prob', 'eat_alone', 'take_drugs',
					 'weight_change', 'unable_shop']
			prefill: 'checklist_nutrition'
		'Comments':
			fields: ['comment']
	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Patient Nutritional Assessment"
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Checklist > Nutrition'
	grid:
		fields: ['created_on', 'have_ill', 'score']
		sort: ['created_on']
	label: 'Patient Nutritional Assessment'
	open: 'read'
