fields:

	applied_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Applied Date/Time'
			readonly: true
			offscreen: true

	post_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Post Date/Time'
			readonly: true
			offscreen: true

	zeroed:
		model:
			source: ['Yes']
		view:
			columns: -4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Zero Invoice?'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						zeroed: 'Yes'
					dest: 'zeroed_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						zeroed: 'Yes'
					dest: 'zeroed_by'
				}
			]

	zeroed_reason_id:
		model:
			required: true
			source: 'list_void_reason_billing'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			label: 'Zeroed Reason'
			columns: 2

	zeroed_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 4
			label: 'Zeroed Date/Time'
			readonly: true
			template: '{{now}}'

	zeroed_by:
		model:
			source: 'user'
		view:
			label: 'Zeroed By'
			readonly: true
			columns: 4

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true

	order_rx_id:
		model:
			source: 'careplan_order_rx'
			required: false
			type: 'int'
			if:
				'*':
					readonly:
						fields: ['inventory_id', 'bill_quantity']
		view:
			label: 'Prescription'
			readonly: true
			offscreen: true

	claim_no:
		model:
			required: false
			type: 'text'
		view:
			label: 'Claim #'
			readonly: true
			offscreen: true

	calc_invoice_split_no:
		model:
			required: false
			type: 'text'
		view:
			label: 'Split Invoice #'
			readonly: true
			offscreen: true

	fill_number:
		model:
			type: 'int'
		view:
			label: 'Fill #'
			readonly: true
			offscreen: true

	rental_id:
		model:
			source: 'careplan_order_rental'
			required: false
			type: 'int'
		view:
			label: 'Prescription ID'
			readonly: true
			offscreen: true

	parent_charge_no:
		model:
			type: 'text'
			if:
				'!':
					fields: ['show_cost']
				'*':
					readonly:
						fields: ['bill_quantity', 'insurance_id']
		view:
			label: 'Parent Charge #'
			note: 'Used in COB scenarios/patient copays to relate charges'
			readonly: true
			offscreen: true

	master_charge_no:
		model:
			type: 'text'
		view:
			label: 'Master Charge #'
			note: 'Used in COB scenarios/patient copays to relate charges'
			readonly: true
			offscreen: true

	charge_no:
		model:
			type: 'text'
		view:
			label: 'Charge #'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	carry_over_charge_no:
		model:
			type: 'text'
		view:
			label: 'Carry Over Charge #'
			readonly: true
			offscreen: true

	close_no:
		model:
			type: 'text'
		view:
			label: 'Closing #'
			readonly: true
			offscreen: true

	encounter_id:
		model:
			required: false
			type: 'int'
		view:
			label: 'Encounter'
			readonly: true
			offscreen: true

	ticket_no:
		model:
			type: 'text'
		view:
			label: 'Ticket #'
			readonly: true
			offscreen: true

	ticket_item_no:
		model:
			type: 'text'
		view:
			label: 'Ticket Item #'
			readonly: true
			offscreen: true

	wt_id:
		model:
			multi: true
			source: 'careplan_dt_wt_pulled'
		view:
			label: 'Careplan WT Pulled'
			readonly: true
			offscreen: true

	invoice_no:
		model:
			type: 'text'
		view:
			label: 'Invoice #'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/billing_invoice/?limit=1&fields=list&sort=id&page_number=0&filter=invoice_no:'
				fields:
					'invoice_status': ['status']
					'revenue_accepted_posted': ['revenue_accepted_posted']
			]
			readonly: true
			offscreen: true

	invoice_status:
		model:
			source: ['Accepted', 'Open', 'Confirmed', 'Voided', '$0']
		view:
			class: 'status'
			control: 'radio'
			label: 'Invoice Status'
			readonly: true
			offscreen: true

	revenue_accepted_posted:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['view_total_paid', 'view_total_expected', 'view_total_adjusted', 'view_total_balance_due']
					readonly:
						fields: ['procm', 'bill_quantity', 'dispense_fee',
							'expected', 'billed', 'copay', 'cost_basis', 'list_price',
							'incv_amt_sub', 'pt_pd_amt_sub', 'inventory_id', 'rental_type',
							'frequency_code', 'modifier_1', 'modifier_2', 'modifier_3', 'modifier_4']
					prefill:
						show_cost: 'ledger_view'
				'!':
					fields: ['paid', 'expected', 'total_adjusted', 'total_balance_due']
					prefill:
						show_cost: 'unposted_view'
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Revenue Accepted and Posted?'
			readonly: true
			offscreen: true

	show_cost:
		model:
			source: ['ledger_view', 'unposted_view']
			if:
				'ledger_view':
					fields: ['view_total_cost']
				'unposted_view':
					fields: ['total_cost']
		view:
			label: 'Show Cost'
			readonly: true
			offscreen: true 

	compound_no:
		model:
			type: 'text'
		view:
			label: 'Compound #'
			readonly: true
			offscreen: true

	is_primary_drug:
		model:
			required: false
			source: ['Yes']
		view:
			label: 'Is Primary Drug?'
			control: 'checkbox'
			class: 'checkbox-only'
			readonly: true
			offscreen: true

	is_primary_drug_ncpdp:
		model:
			required: false
			source: ['Yes']
			if:
				'Yes':
					fields: ['dispense_fee', 'incv_amt_sub', 'pt_pd_amt_sub']
					prefill:
						is_primary_drug: 'Yes'
				'!':
					prefill:
						is_primary_drug: ''
		view:
			label: 'Is Primary Drug?'
			control: 'checkbox'
			class: 'checkbox-only'

	is_dirty:
		model:
			required: false
			source: ['Yes']
		view:
			label: 'Is Dirty?'
			control: 'checkbox'
			class: 'checkbox-only'
			readonly: true
			offscreen: true

	locked:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['locked_datetime', 'locked_by']
		view:
			control: 'checkbox'
			label: 'Locked'
			readonly: true
			offscreen: true

	locked_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Locked Date/Time'
			readonly: true
			offscreen: true

	locked_by:
		model:
			source: 'user'
		view:
			label: 'Locked By'
			readonly: true
			offscreen: true

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			readonly: true
			offscreen: true

	rx_no:
		model:
			type: 'text'
		view:
			columns: 4
			class: 'claim-field'
			label: 'RX #'
			readonly: true

	inventory_type_filter:
		model:
			required: true
			multi: true
			source: ['Drug', 'Supply', 'Equipment Rental', 'Billable']
			default: ['Drug', 'Supply', 'Equipment Rental', 'Billable']
		view:
			label: 'Inventory Type'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			source: 'inventory'
			required: true
			query: 'select_inv_in_stock'
			querytemplate: 'inventoryTemplate'
			sourcefilter:
				type:
					'dynamic': '{inventory_type_filter}'
		view:
			form_link_enabled: true
			label: 'Item'
			columns: 2
			class: 'select_prefill'
			transform: [{
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'inventory_type': ['type']
					'ndc': ['ndc']
					'formatted_ndc': ['formatted_ndc']
					'upc': ['upc']
					'upin': ['upin']
					'billing_unit_id': ['billing_unit_id']
					'metric_unit_each': ['metric_unit_each']
					'description': ['name']
			},
			{
				name: 'UpdateInventoryPricing'
			}
			]

	description:
		model:
			type: 'text'
		view:
			label: 'Description'

	inventory_type:
		model:
			source: ['Drug', 'Supply', 'Equipment Rental', 'Billable']
			if:
				'Drug':
					fields: ['ndc']
					readonly:
						fields: ['charge_quantity']
				'Compound':
					readonly:
						fields: ['charge_quantity']
				'Supply':
					fields: ['upc', 'upin', 'ndc']
				'Equipment Rental':
					fields: ['rental_type']
		view:
			label: 'Inventory Type'
			readonly: true
			offscreen: true

	hcpc_code:
		view:
			columns: 4
			label: 'HCPC'
			readonly: true

	gcn_seqno:
		view:
			label: 'GCN Seq No'
			readonly: true
			offscreen: true

	ndc:
		view:
			label: 'NDC'
			readonly: true
			offscreen: true

	formatted_ndc:
		view:
			label: 'NDC'
			columns: 4
			readonly: true

	upc:
		view:
			label: 'UPC'
			columns: 4
			readonly: true

	upin:
		view:
			label: 'UPIN'
			columns: 4
			readonly: true

	bill_quantity:
		model:
			min: 1
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
			required: true
		view:
			columns: 4
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Bill Quantity (EA)'
			validate: [
				{
					name: 'UpdateTotals'
				}
			]

	metric_unit_each:
		model:
			type: 'decimal'
			max: 9999999.999
		view:
			class: 'numeral fdb-field'
			format: '0,0.[000000]'
			label: 'Metric Units/Each'
			offscreen: true
			readonly: true

	charge_quantity_ea:
		model:
			required: false
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Charge Quantity (EA)'
			readonly: true
			offscreen: true

	charge_quantity:
		model:
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
		view:
			class: 'numeral claim-field'
			format: '0,0.[000000]'
			columns: 4
			label: 'Charge Quantity'
			readonly: true

	charge_unit:
		model:
			required: false
		view:
			label: 'Charge Unit'
			readonly: true
			offscreen: true
			transform: [{
				name: 'UpdateFieldNote'
				target: 'charge_quantity'
			}
			]

	hcpc_quantity:
		model:
			required: false
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
		view:
			columns: 4
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'HCPC Quantity'
			readonly: true

	hcpc_unit:
		model:
			required: false
		view:
			label: 'HCPC Unit'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateFieldNote'
					target: 'hcpc_quantity'
				}
			]

	metric_quantity:
		model:
			required: false
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
		view:
			columns: 4
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Metric Quantity'
			readonly: true

	billing_unit_id:
		model:
			required: true
			source: 'list_unit'
			sourceid: 'code'
			sourcefilter:
					code:
						'static': ['each', 'mL', 'gram']
		view:
			control: 'select'
			label: 'Metric Unit'
			class: 'fdb-field'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateFieldNote'
					target: 'metric_quantity'
				}
			]

	rental_type:
		model:
			required: false
			source: ['Purchase', 'Rental']
			if:
				'Rental':
					fields: ['frequency_code', 'date_of_service', 'date_of_service_end']
		view:
			label: 'Rental Type'
			transform: [
				{
					name: 'UpdateInventoryPricing'
				}
			]
			columns: 4

	date_of_service:
		model:
			required: true
			type: 'date'
		view:
			label: 'Date of Service'
			columns: 4

	date_of_service_end:
		model:
			required: true
			type: 'date'
		view:
			label: 'Date of Service End'
			columns: 4

	frequency_code:
		model:
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SV506'
		view:
			columns: 4
			class: 'claim-field'
			label: 'Rental Frequency'
			reference: 'SV506'
			transform: [
				{
					name: 'UpdateInventoryPricing'
				}
			]

	insurance_id:
		model:
			source: 'patient_insurance'
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			form_link_enabled: true
			columns: 2
			control: 'select'
			label: 'Insurance'
			readonly: true

	payer_id:
		model:
			source: 'payer'
			required: true
			if:
				'>1':
					fields: ['pricing_source', 'shared_contract_id', 'calc_cost_ea',
					'total_cost', 'copay', 'revenue_code_id', 'charge_quantity', 'charge_quantity_ea', 'charge_unit']
		view:
			columns: 2
			label: 'Payer'
			class: 'claim-field'
			readonly: true

	billing_method_id:
		model:
			required: true
			source: 'list_billing_method'
			sourceid: 'code'
			if:
				'mm':
					fields: ['description', 'modifier_1', 'modifier_2', 'modifier_3', 'modifier_4']
					field_label:
						billed: 'default'
					prefill:
						inventory_type_filter: ['Drug', 'Supply', 'Equipment Rental', 'Billable']
				'cms1500':
					fields: ['description', 'modifier_1', 'modifier_2', 'modifier_3', 'modifier_4']
					prefill:
						inventory_type_filter: ['Drug', 'Supply', 'Equipment Rental', 'Billable']
					field_label:
						billed: 'default'
				'ncpdp':
					fields: ['procm', 'is_primary_drug_ncpdp', 'cost_basis', 'compound_no']
					field_label:
						billed: 'default'
					prefill:
						inventory_type_filter: ['Drug']
				'generic':
					fields: ['description']
					field_label:
						billed: 'Total Price'
					prefill:
						inventory_type_filter: ['Drug', 'Supply', 'Equipment Rental', 'Billable']
		view:
			label: 'Billing Method'
			readonly: true
			offscreen: true

	pricing_source:
		model:
			source:
				part_b_asp: "MCR Part B ASP",
				part_b_dme: "MCR Part B Durable Medical Equipment, Prosthetics, Orthotics & Supplies (DMEPOS)",
				payer_contract: 'Payer Contract',
				shared_contract: 'Shared Contract',
				list: 'List Price',
				cob_override: 'COB Override Amount',
				copay: 'Copay'
			if:
				'payer_contract':
					fields: ['shared_contract_id']
		view:
			columns: 2
			label: 'Pricing Source'
			readonly: true

	shared_contract_id:
		model:
			source: 'payer_contract'
			sourceid: 'id'
		view:
			columns: 2
			label: 'Contract'
			readonly: true

	modifier_1:
		model:
			min: 2
			max: 2
			if:
				'*':
					fields: ['modifier_2']
		view:
			class: 'claim-field'
			columns: 'modifier'
			label: 'Modifier 1'

	modifier_2:
		model:
			min: 2
			max: 2
			if:
				'*':
					fields: ['modifier_3']
		view:
			class: 'claim-field'
			columns: 'modifier'
			label: 'Modifier 2'

	modifier_3:
		model:
			min: 2
			max: 2
			if:
				'*':
					fields: ['modifier_4']
		view:
			class: 'claim-field'
			columns: 'modifier'
			label: 'Modifier 3'

	modifier_4:
		model:
			min: 2
			max: 2
		view:
			class: 'claim-field'
			columns: 'modifier'
			label: 'Modifier 4'

	procm:
		model:
			multi: true
			source: 'list_ncpdp_ext_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '459-ER'
		view:
			columns: 2
			max_count: 4
			class: 'claim-field'
			reference: '459-ER'
			note: '459-ER - Max 4'
			label: 'Procedure Modifier Code(s)'

	billed:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
			min: 0
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Billed Amount'
			validate: [
				{
					name: "CompareValidator"
					fields: [
						"expected"
						"billed"
					]
					require: "lte"
					error: "Billed amount must be greater than or equal to Expected amount"
				},
				{
					name: 'UpdateBilledEa'
				}
			]

	calc_billed_ea:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
			min: 0
		view:
			class: 'numeral money'
			format: '$0,0.0000'
			label: 'Billed (EA)'
			readonly: true
			offscreen: true
			validate: [
				{
					name: 'UpdateTotals'
				}
			]

	dispense_fee:
		model:
			required: false
			rounding: 0.01
			max: 999999.99
			type: 'decimal'
		view:
			class: 'numeral money'
			format: '$0,0.00'
			note: '412-DC'
			columns: 4
			label: 'Dispense Fee'
			validate: [
				{
					name: 'UpdateTotals'
				}
			]

	incv_amt_sub:
		model:
			required: false
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			default: 0.00
		view:
			columns: 4
			reference: '438-E3'
			note: '438-E3'
			label: 'Incentive Amount'
			class: 'numeral money'
			format: '$0,0.00'
			validate: [
				{
					name: 'UpdateTotals'
				}
			]

	gross_amount_due:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			min: 0.00
		view:
			columns: 4
			reference: '430-DU'
			note: '430-DU'
			label: 'Claim Gross Amt Due'
			class: 'numeral money'
			format: '$0,0.00'

	cost_basis:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '423-DN'
		view:
			columns: 4
			class: 'claim-field'
			reference: '423-DN'
			note: '423-DN'
			label: 'Basis of Cost Determination'

	pt_pd_amt_sub:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			min: 0.00
			default: 0.00
			required: true
		view:
			columns: 4
			reference: '433-DX'
			note: '433-DX'
			label: 'Pt Paid Amount'
			class: 'numeral money'
			format: '$0,0.00'

	expected:
		model:
			rounding: 0.01
			type: 'decimal'
			min: 0
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Expected'
			validate: [
				{
					name: "CompareValidator"
					fields: [
						"expected"
						"billed"
					]
					require: "lte"
					error: "Billed amount must be greater than or equal to Expected amount"
				},
				{
					name: 'UpdateExpectedEa'
				}
			]

	awp_price:
		model:
			rounding: 0.00001
			type: 'decimal'
		view:
			note: 'Each'
			label: 'AWP Price'
			class: 'numeral fdb-field'
			format: '$0,0.0000'
			columns: 4
			readonly: true

	view_total_expected:
		model:
			rounding: 0.01
			type: 'decimal'
			save: false
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.0000'
			label: 'Expected'

	calc_expected_ea:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
			min: 0
		view:
			label: 'Expected (EA)' 
			class: 'numeral money'
			format: '$0,0.0000'
			readonly: true
			offscreen: true
			validate: [
				{
					name: 'UpdateTotals'
				}
			]

	list_price:
		model:
			rounding: 0.01
			type: 'decimal'
			min: 0
		view:
			columns: 4
			class: 'numeral claim-field'
			format: '$0,0.00'
			label: 'Usual / Cust Charge'

	calc_list_ea:
		model:
			rounding: 0.00001
			type: 'decimal'
			min: 0
		view:
			class: 'numeral money'
			format: '$0,0.0000'
			label: 'List Price (EA)'
			readonly: true
			offscreen: true

	total_cost:
		model:
			rounding: 0.00001
			type: 'decimal'
			min: 0
		view:
			columns: 4
			label: 'Estimated Cost'
			note: 'Final COGS are calculated when revenue is booked'
			class: 'numeral money'
			format: '$0,0.0000'
			readonly: true

	view_total_cost:
		model:
			rounding: 0.01
			type: 'decimal'
			min: 0
			save: false
		view:
			columns: 4
			label: 'Cost'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	calc_cost_ea:
		model:
			rounding: 0.00001
			type: 'decimal'
			min: 0
		view:
			label: 'Last Cost (EA)'
			class: 'numeral money'
			format: '$0,0.0000'
			readonly: true
			offscreen: true

	copay:
		model:
			default: 0.00
			rounding: 0.01
			type: 'decimal'
			min: 0
		view:
			columns: 4
			class: 'numeral money'
			note: 'Populated from claim response if MM or NCPDP'
			format: '$0,0.00'
			label: 'Copay'

	paid:
		model:
			default: 0.00
			rounding: 0.01
			type: 'decimal'
			min: 0
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Paid'
			readonly: true

	view_total_paid:
		model:
			default: 0.00
			rounding: 0.01
			type: 'decimal'
			min: 0
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Paid'
			readonly: true

	total_adjusted:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Adjusted Amount' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	view_total_adjusted:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Adjusted Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_balance_due:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Balance Due'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	view_total_balance_due:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Balance Due'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			validate: [
				{
					name: 'ChargeLineCalcTotalBalanceDue'
				}
			]

	revenue_code_id:
		model:
			required: false
			source: 'list_revenue_code'
			sourceid: 'code'
		view:
			label: 'Revenue Code'
			columns: 4

	taxable:
		model:
			source: ['Yes']
			if:
				'*':
					fields: ['tax_code_id', 'flat_tax_amt', 'sales_tax', 'per_sales_tax_rate_used', 'sales_tax_basis']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Taxable?'
			readonly: true
			offscreen: true

	tax_code_id:
		model:
			required: true
			source: 'list_tax_code'
			sourceid: 'code'
			type: 'text'
		view:
			label: 'Tax Code'
			readonly: true
			offscreen: true

	flat_tax_amt:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			min: 0
			default: 0.00
		view:
			reference: '481-HA'
			note: '481-HA'
			label: 'Flat Tax Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			offscreen: true

	sales_tax:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			required: false
			min: 0
			default: 0.00
		view:
			reference: '482-GE'
			label: 'Sales Tax'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			offscreen: true

	per_sales_tax_rate_used:
		model:
			type: 'decimal'
			rounding: 0.00001
			max: 100
			required: false
		view:
			reference: '483-HE'
			note: '483-HE'
			label: 'Percentage Sales Tax Rate Used'
			class: 'numeral claim-field'
			format: 'percent'
			readonly: true
			offscreen: true

	sales_tax_basis:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			required: false
			sourcefilter:
				field:
					'static': '484-JE'
				code:
					'static': ['02', '03']
			if:
				'*':
					require_fields: ['per_sales_tax_rate_used', 'per_sales_tax_rate_used']
		view:
			reference: '484-JE'
			note: '484-JE'
			class: 'claim-field'
			label: 'Sales Tax Basis'
			readonly: true
			offscreen: true

	void:
		model:
			source: ['Yes']
			if:
				'Yes':
					require_fields: ['voided_datetime', 'void_reason_id', 'voided_by']
		view:
			columns: 4
			control: 'checkbox'
			label: 'Void Charge Line?'
			findfilter: '!Yes'
			class: 'checkbox-only'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						void: 'Yes'
					dest: 'voided_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						void: 'Yes'
					dest: 'voided_by'
				}
			]

	void_reason_id:
		model:
			required: true
			source: 'list_void_reason_billing'
			sourceid: 'code'
		view:
			label: 'Void Reason'
			columns: 2

	voided_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			label: 'Voided Date'
			readonly: true
			columns: 4

	voided_by:
		model:
			source: 'user'
		view:
			label: 'Voided By'
			readonly: true
			columns: 4

	update_pricing:
		model:
			source: ['Update Pricing']
		view:
			columns: 2
			class: 'dsl-button'
			control: 'checkbox'
			label: ' '
			transform: [
				name: 'UpdateInventoryPricing'
			]

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin','pharm','csr','cm', 'nurse']
		read_all:   ['admin','pharm','csr','cm', 'nurse']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      []
	bundle: ['audit']
	indexes:
		unique: [
			['charge_no']
		]
		many: [
			['patient_id']
			['order_rx_id']
			['rental_id']
			['parent_charge_no']
			['master_charge_no']
			['charge_no']
			['close_no']
			['ticket_no']
			['ticket_item_no']
			['wt_id']
			['invoice_no']
			['claim_no']
			['compound_no']
			['site_id']
			['rx_no']
			['inventory_id']
			['inventory_type']
			['hcpc_code']
			['ndc']
			['upc']
			['upin']
			['billing_unit_id']
			['insurance_id']
			['payer_id']
			['billing_method_id']
			['pricing_source']
			['shared_contract_id']
			['calc_invoice_split_no']
		]
	name: "{patient_id_auto_name} {inventory_id_auto_name} {bill_quantity} ${expected}"
	sections_group: [
		'Item Info':
			hide_header: true
			indent: false
			tab: 'Charge'
			fields: ['order_rx_id', 'calc_invoice_split_no', 'invoice_no', 'claim_no', 'ticket_no', 'invoice_status', 'is_dirty',
			'master_charge_no', 'parent_charge_no', 'compound_no',
			'is_primary_drug', 'locked', 'site_id', 'rx_no', 'inventory_type_filter', 'inventory_id', 'inventory_type',
			'hcpc_code', 'ndc', 'formatted_ndc', 'upc', 'upin', 'description',
			'bill_quantity', 'metric_unit_each', 'charge_quantity', 'charge_quantity_ea', 'charge_unit', 'metric_quantity', 'billing_unit_id',
			'hcpc_quantity', 'hcpc_unit',  'rental_type', 'date_of_service', 'date_of_service_end', 'frequency_code',
			'modifier_1', 'modifier_2', 'modifier_3', 'modifier_4', 'procm']
		'Charge Info':
			indent: false
			tab: 'Charge'
			fields: ['billed', 'calc_billed_ea', 'dispense_fee', 'incv_amt_sub', 'gross_amount_due', 
			'cost_basis', 'pt_pd_amt_sub', 'expected', 'awp_price', 'view_total_expected', 'calc_expected_ea', 'list_price',
			'calc_list_ea', 'calc_cost_ea', 'view_total_cost', 'total_cost', 'copay', 'paid', 'view_total_paid',
			'total_adjusted', 'view_total_adjusted', 'total_balance_due', 'view_total_balance_due',
			'revenue_code_id', 'revenue_accepted_posted', 'show_cost', 'update_pricing']
		'Payer':
			hide_header: true
			indent: false
			tab: 'Payer'
			fields: ['insurance_id', 'payer_id', 'billing_method_id', 'pricing_source', 'shared_contract_id']
		'Primary Drug':
			hide_header: true
			indent: false
			fields: ['is_primary_drug_ncpdp']
			compact: true
		'Zero':
			modal: true
			note: 'Zeroing an invoice will add a credit adjustment against the invoice to reverse the invoice in the finance ledger.'
			fields: ['patient_id', 'zeroed', 'zeroed_datetime', 'zeroed_by', 'zeroed_reason_id']
		'Void':
			modal: true
			note: 'Voiding a charge line will remove it from the invoice and the finance ledger.'
			fields: ['patient_id', 'void', 'voided_datetime', 'voided_by', 'void_reason_id']
	]

view:
	dimensions:
		width: '65%'
		height: '65%'
	hide_cardmenu: true
	block:
		validate: [
			name: 'ChargeLineBlock'
		]
	comment: 'Charge Line'
	find:
		basic: ['patient_id', 'insurance_id', 'payer_id', 'inventory_id']
		advanced: ['ticket_no', 'close_no', 'invoice_no', 'charge_no', 'parent_charge_no']
	grid:
		label: ['Item', 'Quantity', 'Exp $', 'Billed $']
		fields: ['inventory_id', 'bill_quantity', 'expected', 'billed']
		width: [45, 15, 20, 20]
		sort: ['-id']
	label: 'Charge Line'
	open: 'edit'
