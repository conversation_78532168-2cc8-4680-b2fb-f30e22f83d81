fields:
	flag_id:
		model:
			required: false
			multi: true
			source: 'list_flag'
		view:
			label: 'Flags'

	file:
		model:
			required: true
			type: 'json'
		view:
			control: 'file'
			label: 'Document / File'
			note: 'Max 100MB. Only documents, images, and archives supported.'

	comments:
		model:
			max: 1024
		view:
			control: 'area'
			label: 'Comments'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		delete:     ['admin', 'pharm', 'patient']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
	name: ['flag_id']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['flag_id', 'file', 'comments']

view:
	comment: 'Sales > File Attachments'
	find:
		basic: ['flag_id']
	grid:
		fields: ['created_on', 'created_by', 'file', 'flag_id', 'comments']
		sort: ['-id']
	label: 'Sales File Attachment'
