fields:

	code:
		model:
			max: 64
			required: true
		view:
			columns: 2
			label: 'Code'
			findunique: true

	name:
		model:
			max: 128
			required: true
		view:
			label: 'Subject'
			columns: 2
			findunique: true

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
			default: 'Yes'
		view:
			columns: 2
			control: 'radio'
			label: 'Active'
			findfilter: 'Yes'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 3
			control: 'radio'
			label: 'Can Sync Record'
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ['lists']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['code']
		]
	name: ['name']
	sections:
		'Details':
			fields: ['code', 'name', 'active', 'allow_sync']

view:
	comment: 'Manage > Note Type'
	find:
		basic: ['code', 'name', 'active']
	grid:
		fields: ['code', 'name', 'active']
		sort: ['name']
	label: 'Note Type'