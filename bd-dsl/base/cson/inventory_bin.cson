fields:

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			columns: 2
			label: 'Site'

	inventory_id:
		model:
			required: true
			source: 'inventory'
			sourceid: 'id'
			type: 'int'
		view:
			columns: 2
			label: 'Inventory'

	min_quantity:
		model:
			required: false
			type: 'int'
			min: 0
		view:
			columns: 2
			label: 'Min Desired Quantity'
			validate: [
				{
					name: "CompareValidator"
					fields: [
						"min_quantity"
						"max_quantity"
					]
					require: "lte"
					error: "Max Desired Quantity must be greater than or equal to Min Desired Quantity"
				}
			]

	max_quantity:
		model:
			required: false
			type: 'int'
			min: 0
		view:
			columns: 2
			label: 'Max Desired Quantity'
			validate: [
				{
					name: "CompareValidator"
					fields: [
						"min_quantity"
						"max_quantity"
					]
					require: "lte"
					error: "Max Desired Quantity must be greater than or equal to Min Desired Quantity"
				}
			]

	bin:
		view:
			columns: 2
			label: 'Bin Location'


model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['site_id']
		]
		unique: [
			['site_id', 'inventory_id']
		]
	name: ['inventory_id', 'site_id']
	bundle: ['inventory']
	sections:
		'Site Inventory Item Info':
			hide_header: true
			indent: false
			fields: ['site_id', 'inventory_id', 'max_quantity', 'min_quantity']

view:
	hide_cardmenu: true
	comment: 'Inventory Bin'
	grid:
		fields: ['site_id', 'inventory_id', 'max_quantity', 'min_quantity', 'bin']
		sort: ['-id']
	label: 'Bin Location'
	open: 'edit'
