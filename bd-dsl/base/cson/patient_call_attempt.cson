fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	#TODO Should auto-calculate based on the order and previous fills
	attempt_no:
		model:
			type: 'int'
		view:
			columns: 2
			label: 'Call Attempt # Since Last Fill'
			readonly: true

	left_vm:
		model:
			source: ['No', 'Yes']
			required: false
		view:
			columns: 2
			label: 'Left VM?'
			control: 'radio'

	note:
		model:
			type: 'text'
		view:
			control: 'area'
			label: 'Comments'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	bundle: ['patient']
	name: ['patient_id', 'attempt_no', 'created_on']
	sections:
		'Call Attempt':
			fields: ['attempt_no', 'left_vm', 'note']

view:
	hide_cardmenu: true
	comment: 'Patient > Call Attempt'
	find:
		basic: ['attempt_no', 'left_vm']
		advanced: ['note']
	grid:
		fields: ['attempt_no', 'left_vm', 'note']
		sort: ['-created_on']
	label: 'Call Attempt'
	open: 'read'
