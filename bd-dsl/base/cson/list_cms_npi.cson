fields:

	code:
		view:
			columns: 2
			label: 'NPI'
			findunique: true

	provider_organization_name:
		view:
			columns: 2
			label: 'Provider Organization Name (Legal Business Name)'

	provider_last_name:
		view:
			columns: 2
			label: 'Provider Last Name (Legal Name)'

	provider_first_name:
		view:
			columns: 2
			label: 'Provider First Name'

	provider_middle_name:
		view:
			columns: 2
			label: 'Provider Middle Name'

	provider_credential_text:
		view:
			columns: 2
			label: 'Provider Credential Text'

	provider_other_organization_name:
		view:
			columns: 2
			label: 'Provider Other Organization Name'

	provider_first_line_business_practice_location_address:
		view:
			columns: 2
			label: 'Provider First Line Business Practice Location Address'

	provider_second_line_business_practice_location_address:
		view:
			columns: 2
			label: 'Provider Second Line Business Practice Location Address'

	provider_business_practice_location_address_city_name:
		view:
			columns: 2
			label: 'Provider Business Practice Location Address City Name'

	provider_business_practice_location_address_state_name:
		view:
			columns: 2
			label: 'Provider Business Practice Location Address State Name'

	provider_business_practice_location_address_postal_code:
		view:
			columns: 2
			label: 'Provider Business Practice Location Address Postal Code'

	provider_business_practice_location_address_telephone_number:
		view:
			columns: 2
			label: 'Provider Business Practice Location Address Telephone Number'

	provider_business_practice_location_address_fax_number:
		view:
			columns: 2
			label: 'Provider Business Practice Location Address Fax Number'


model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		many: [
			['auto_name']
		]
		unique: [
			['code']
		]

	name: ['code']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'provider_other_organization_name', 'provider_last_name', 'provider_first_name', 'provider_middle_name'
			'provider_credential_text', 'provider_organization_name', 'provider_first_line_business_practice_location_address'
			'provider_second_line_business_practice_location_address', 'provider_business_practice_location_address_city_name',
			'provider_business_practice_location_address_state_name', 'provider_business_practice_location_address_postal_code'
			'provider_business_practice_location_address_telephone_number', 'provider_business_practice_location_address_fax_number']

view:
	comment: 'Manage > List CMS NPI'
	find:
		basic: ['code']
	grid:
		fields: ['code', 'provider_first_name', 'provider_middle_name']
	label: 'List CMS NPI'
