fields:

	rx_no:
		view:
			label: ''
			readonly: true
			offscreen: true

	charge_no:
		model:
			type: 'text'
		view:
			label: 'Linked Ledger Charge Line'
			readonly: true
			offscreen: true

	patient_id:
		model:
			type: 'int'
			required: false
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	service_date:
		model:
			required: true
			type: 'date'
		view:
			columns: -3
			label: 'Service Date Start'
			note: 'Must be in MM/DD/YYYY format'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	service_date_end:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Service Date End'
			note: 'Must be in MM/DD/YYYY format'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	inventory_id:
		model:
			source: 'inventory'
			required: true
		view:
			form_link_enabled: true
			columns: -3
			label: 'Service Item'
			readonly: true

	type:
		model:
			default: 'Drug'
			source: ['Drug', 'Compound', 'Supply', 'Equipment Rental', 'Billable']
		view:
			label: 'Type'
			readonly: true
			offscreen: true

	procedure_code:
		model:
			required: true
			min: 1
			max: 48
		view:
			columns: -3
			label: 'Procedure Code'

	line_item_charge_amount:
		model:
			required: true
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 3
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Charge Amount'
			readonly: true

	line_item_paid_amount:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 3
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Paid Amount'
			validate: [
				{
					name: "CompareValidator"
					fields: [
						"line_item_paid_amount"
						"line_item_charge_amount"
					]
					require: "lte"
					error: "Paid amount must be less than or equal to Charge amount"
				}
			]

	service_unit_count:
		model:
			required: true
			max: 99999.999
			min: 0.001
			rounding: 0.001
			type: 'decimal'
		view:
			columns: 3
			label: 'Charge Units'
			readonly: true

	measurement_unit:
		model:
			default: 'UN'
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SV103'
		view:
			columns: 3
			label: 'Unit'

	emergency_indicator:
		model:
			source:
				'Y': 'Yes'
		view:
			columns: -3
			control: 'checkbox'
			label: 'Emergency Related?'
			class: 'checkbox-only'

	epsdt_indicator:
		model:
			source:
				'Y': 'Yes'
		view:
			columns: 3
			control: 'checkbox'
			label: 'EPSDT Related?'
			class: 'checkbox-only'

	place_of_service_code:
		model:
			default: '12'
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SV105'
		view:
			columns: -3
			label: 'Place of Service Code'

	modifier_1:
		model:
			min: 2
			max: 2
			if:
				'*':
					fields: ['modifier_2']
		view:
			columns: 'modifier'
			label: 'Modifier 1'
			readonly: true

	modifier_2:
		model:
			min: 2
			max: 2
			if:
				'*':
					fields: ['modifier_3']
		view:
			columns: 'modifier'
			label: 'Modifier 2'
			readonly: true

	modifier_3:
		model:
			min: 2
			max: 2
			if:
				'*':
					fields: ['modifier_4']
		view:
			columns: 'modifier'
			label: 'Modifier 3'
			readonly: true

	modifier_4:
		model:
			min: 2
			max: 2
		view:
			columns: 'modifier'
			label: 'Modifier 4'
			readonly: true

	dx_filter:
		model:
			multi: true
			source: 'patient_diagnosis'
		view:
			label: 'Filter IDs for diagnosis'
			validate: [
				name: 'AddClaimDiagnoses1500'
			]
			readonly: true
			offscreen: true

	dx_id_1:
		model:
			source: 'patient_diagnosis'
			required: true
			sourcefilter:
				id:
					'dynamic': '{dx_filter}'
			if:
				'*':
					fields: ['dx_id_2']
		view:
			columns: 3
			label: 'Diagnosis 1'
			note: 'Add diagnosis to claim before adding to service line'

	dx_id_2:
		model:
			source: 'patient_diagnosis'
			sourcefilter:
				id:
					'dynamic': '{dx_filter}'
			if:
				'*':
					fields: ['dx_id_3']
		view:
			columns: 3
			label: 'Diagnosis 2'

	dx_id_3:
		model:
			source: 'patient_diagnosis'
			sourcefilter:
				id:
					'dynamic': '{dx_filter}'
			if:
				'*':
					fields: ['dx_id_4']
		view:
			columns: 3
			label: 'Diagnosis 3'

	dx_id_4:
		model:
			source: 'patient_diagnosis'
			sourcefilter:
				id:
					'dynamic': '{dx_filter}'
		view:
			columns: 3
			label: 'Diagnosis 4'

	supplemental_info:
		model:
			max: 112
		view:
			label: 'Supplemental Info'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['service_date', 'service_date_end']
	sections:
		'Service Line':
			hide_header: true
			fields: ['charge_no', 'patient_id', 'service_date', 'service_date_end',
			'inventory_id', 'type', 'procedure_code', 'line_item_charge_amount',
			'line_item_paid_amount', 'measurement_unit', 'service_unit_count', 'emergency_indicator', 'epsdt_indicator', 'place_of_service_code',
			'modifier_1', 'modifier_2', 'modifier_3', 'modifier_4', 'dx_filter', 'dx_id_1', 'dx_id_2', 'dx_id_3', 'dx_id_4',
			'supplemental_info']

view:
	dimensions:
		width: '85%'
		height: '75%'
	hide_cardmenu: true
	validate: [
		{
			name: "DateOrderValidator"
			fields: [
				"service_date",
				"service_date_end"
			]
			error: "Service Date End cannot be before Service Date"
		}
	]
	hide_header: true
	comment: 'Service Line Claims (1500)'	
	grid:
		fields: ['inventory_id', 'service_unit_count', 'measurement_unit',  'dx_id_1', 'modifier_1']
		width: [40, 10, 10, 30, 10]
		sort: ['-created_on']
	label: 'Service Line Claims (1500)'
	open: 'read'