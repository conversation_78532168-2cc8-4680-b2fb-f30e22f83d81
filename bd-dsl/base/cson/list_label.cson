fields:

	name:
		model:
			max: 128
			required: true
		view:
			label: 'Name'
			columns: 2

	site_id:
		model:
			type: 'int'
			source: 'site'
			required: true
		view:
			label: 'Assigned Sites'
			columns: 2

	report_id:
		model:
			source: 'report'
			sourceid: 'code'
			required: true
		view:
			label: 'Label Report'
			columns: 2

	label_format:
		model:
			required: true
			source: ['PO', 'IV', 'TPN', 'Injection']
			if:
				'TPN':
					fields: ['tpn_additives_label']
				'IV':
					fields: ['add_syringe_label']
		view:
			control: 'checkbox'
			class: 'checkbox checkbox-4'
			label: 'Label Format'
			columns: 2

	default_no_copies:
		model:
			default: 1
			required: true
			type: 'int'
			min: 1
			max: 10
		view:
			columns: 4
			label: '# of Copies'
			note: 'Defaults # of label copies'

	tpn_additives_label:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['report_additives_id', 'default_no_additives_copies']
		view:
			control: 'checkbox'
			label: 'TPN Additives Label?'
			note: 'If yes, select additives report'
			columns: 4

	add_syringe_label:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['report_syringe_id', 'default_no_syringe_copies']
		view:
			control: 'checkbox'
			label: 'Add Syringe Label?'
			columns: 4

	report_syringe_id:
		model:
			source: 'report'
			sourceid: 'code'
			required: true
		view:
			label: 'Syringe Report'
			columns: 4

	default_no_syringe_copies:
		model:
			default: 1
			required: true
			type: 'int'
			min: 1
			max: 10
		view:
			columns: 4
			label: '# of Syringe Copies'
			note: 'Defaults # of label copies'

	default_no_additives_copies:
		model:
			default: 1
			required: true
			type: 'int'
			min: 1
			max: 10
		view:
			columns: 4
			label: '# of Additives Copies'
			note: 'Defaults # of label copies'

	report_additives_id:
		model:
			source: 'report'
			sourceid: 'code'
			required: true
		view:
			label: 'Additives Report'
			columns: 4

	active:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			control: 'checkbox'
			label: 'Active?'
			columns: 4
			findfilter: 'Yes'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ['lists']
	indexes:
		unique: [
			['site_id', 'label_format', 'active']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name', 'site_id', 'report_id', 'label_format', 'default_no_copies',
			'tpn_additives_label', 'add_syringe_label', 'report_syringe_id', 'report_additives_id',
			'active']

view:
	comment: 'Manage > Label'
	find:
		basic: ['name', 'site_id', 'label_format']
	grid:
		fields: ['name', 'site_id', 'label_format', 'report_id']
		sort: ['name']
	label: 'Label'
