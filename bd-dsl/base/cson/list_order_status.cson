fields:

	code:
		model:
			max: 64
			required: true
		view:
			columns: 2
			label: 'Code'
			findunique: true

	name:
		model:
			required: true
			max: 128
		view:
			columns: 2
			label: 'Name'
			findunique: true
	
	notification_type:
		model:
			source: ['Ok', 'Warning', 'Error', 'Critical', 'Inactive']
			default: 'Inactive'
		view:
			columns: 2.1
			control: 'radio'
			label: 'Notification Type'

	patient_status_id:
		model:
			required: false
			source: 'list_patient_status'
			sourceid: 'code'
			if:
				'*':
					fields: ['all_orders']
		view:
			class: 'status'
			label: 'Patient Status'
			note: 'Will automatically set the patient status of the item when set'
			columns: 3

	all_orders:
		model:
			source: ['Yes', 'No']
			default: 'Yes'
			required: true
		view:
			control: 'radio'
			label: 'All orders must be on the same status?'
			columns: 3

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['status']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['code']
		]
	name: '{code} - {name}'
	sections:
		'Order Status':
			fields: ['code', 'name', 'patient_status_id',
			'all_orders', 'allow_sync',
			'active', 'notification_type']

view:
	comment: 'Manage > Order Status'
	find:
		basic: ['code','name', 'active']
	grid:
		fields: ['code','name', 'patient_status_id','all_orders', 'active']
		sort: ['code']
	label: 'Order Status'
	open: 'read'
