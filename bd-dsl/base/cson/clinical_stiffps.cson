fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Difficulties
	diff_arm:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = No stiffness)'
			label: 'Stiffness in arms'

	diff_trunk:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = No stiffness)'
			label: 'Stiffness in trunk / back / abdomen'

	diff_leg:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = No stiffness)'
			label: 'Stiffness in legs'

	diff_climbing:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Require assistance, 2 = Normal)'
			label: 'Climbing 3 stairs'

	diff_bed:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Require assistance, 2 = Normal)'
			label: 'Getting out of bed or rising from sitting position'

	diff_sens:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = Normal)'
			label: 'Heightened sensitivity to stimuli such as noise, touch and emotional distress'

	diff_balance:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Stand on one foot for 5 seconds (using either foot)'

	diff_sleep:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable to sleep, 1 = Frequently unable to sleep, 2 = Sleep well)'
			label: 'Sleeping (related to stiffness / discomfort)'

	diff_cramp:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Cramping / muscle spasms'

	diff_pain:
		model:
			max: 2
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			note: '(0 = No pain  -  10 = Severe pain)'
			label: 'Pain'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_stiffps:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Difficulties':
			note: 'On a scale of zero to two (0 - 2) (unless specified), with 0 meaning Extreme Difficulty and 2 meaning Normal, please rate your difficulty today with:'
			fields: ['diff_arm', 'diff_trunk', 'diff_leg', 'diff_climbing', 'diff_bed', 'diff_sens',
				'diff_balance', 'diff_sleep', 'diff_cramp', 'diff_pain']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Stiff Person Syndrome'
	label: 'Stiff Person Syndrome'
