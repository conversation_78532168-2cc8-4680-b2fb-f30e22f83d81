fields:
	code:
		model:
			search: 'A'
			max: 64
			min: 1
			required: true
			transform: [
					name: 'LowerCase'
			]
			
		view:
			label: 'Table Name'
			note: 'SINGULAR FORM (use letters, numbers, \_ only)'
	
	form_label:
		model:
			search: 'B'
			required: false
		view:
			label: 'Form Label'
			note: 'Form Label'

	bundle:
		model:
			required: false
			multi: true
			# source:
			# 	'patient': 'Patient'
			# 	'careplan': 'careplan'
			# 	'encounter': 'Encounter'
			# 	'ongoing': 'Ongoing'
			# 	'reference': 'Reference'
			# 	'manage': 'Manage'
			# 	'schedule': 'Schedule'
			# 	'selfreport': 'Self Report'
		view:
			label: 'Bundle'

	data:
		model:
			required: true
		view:
			class: 'ace_cs'
			control: 'area'
			label: 'DSL (.coffee format)'

	comment:
		model:
			max: 128
			search: 'C'
		view:
			label: 'Comment'

	checksum:
		model:
			max: 128
		view:
			label: 'Checksum'

	table_autoname:
		view:
			label: 'Table Autoname'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	indexes:
		fulltext: ['comment', 'data']
		unique: [
			['code']
		]
	name: ['code']
	reportable: false
	sections:
		'Main':
			fields: ['form_label', 'code', 'comment', 'data']

view:
	comment: 'Manage > Coffee DSL (Structure of DSL forms)'
	find:
		basic: ['form_label', 'code', 'data']
		advanced: ['comment']
	grid:
		fields: ['form_label', 'code', 'comment', 'updated_on', 'updated_by']
		sort: ['code', 'comment']
	label: '~ Coffee DSL'
	open: 'read'
