fields:

	site_id:
		model:
			prefill: ['parent.site_id']
			required: true
			source: 'site'
			type: 'int'
		view:
			columns: 2
			label: 'Site'
			findmulti: true

	po_id:
		model:
			required: false
			source: 'po'

	gtin:
		model:
			required: false
			type: 'text'
		view:
			columns: 4
			label: 'GTIN'

	inventory_id:
		model:
			required: true
			source: 'inventory'
			sourcefilter:
				type:
					'static': ['Drug', 'Supply', 'Compound Ingredient', 'Equipment Rental']
				active:
					'static': 'Yes'
		view:
			label: 'Inventory Item'
			class: 'select_prefill'
			columns: 2
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'lot_tracking': ['lot_tracking'],
					'serial_tracking': ['serial_tracking'],
					'type': ['type']
			]

	type:
		model:
			source: ['Drug', 'Supply', 'Equipment Rental', 'Billable']
			if:
				'Compound':
					require_fields: ['expiration']
				'Drug':
					require_fields: ['expiration']
				'Equipment Rental':
					readonly:
						fields: ['quantity']
		view:
			label: 'Type'
			readonly: true
			offscreen: true

	lot_tracking:
		model:
			source: ['Yes']
			if:
				'Yes':
					require_fields: ['lot_no']
				'!':
					readonly:
						fields: ['lot_no']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Track Lots?'
			readonly: true
			offscreen: true

	serial_tracking:
		model:
			source: ['Yes']
			if:
				'Yes':
					require_fields: ['serial_no']
				'!':
					readonly:
						fields: ['serial_no']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Track Serial Numbers?'
			readonly: true
			offscreen: true

	lot_no:
		model:
			required: false
		view:
			columns: 4
			label: 'Lot No'
			validate: [
				{
					name: 'RegExValidator'
					pattern: '^[A-Z0-9\.\-]{4,20}$'
					error: 'Invalid Lot Number'
				},
				{
					name: 'LockLotInventory'
				}
			]

	serial_no:
		model:
			required: false
		view:
			columns: 4
			label: 'Serial No'
			validate: [
				{
					name: 'RegExValidator'
					pattern: '^[A-Z0-9\.\-]{4,30}$'
					error: 'Invalid Serial Number'
				},
				{
					name: 'LockLotInventory'
				}
			]

	quantity:
		model:
			min: 1
			required: true
			rounding: 1
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0'
			columns: -4
			label: 'Quantity'

	# Aqusition cost in ledger is ch * quantity
	cost_each:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Cost Each'
			class: 'numeral money'
			format: '$0,0.00'

	expiration:
		model:
			type: 'date'
		view:
			columns: 4
			control: 'input'
			label: 'Expiration Date'
			validate: [
				name: 'DateValidator'
				require: 'future'
			]

model:
	save: false
	access:
		create:     []
		create_all: ['admin','pharm','csr','cm']
		delete:     []
		read:       ['admin','pharm','csr','cm']
		read_all:   ['admin','pharm','csr','cm']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      ['admin','pharm','csr','cm']
	name:['inventory_id']
	sections:
		'Inventory Lot':
			hide_header: true
			indent: false
			fields: ['site_id', 'inventory_id', 'type', 'lot_tracking', 'serial_tracking', 'lot_no', 'serial_no', 'quantity', 'cost_each', 'expiration']

view:
	hide_cardmenu: true
	comment: 'Received Lot'
	grid:
		fields: ['site_id', 'inventory_id', 'type', 'lot_tracking', 'serial_tracking', 'lot_no', 'serial_no', 'quantity', 'cost_each', 'expiration']
		sort: ['-id']
	label: 'Received Lot'
	open: 'edit'