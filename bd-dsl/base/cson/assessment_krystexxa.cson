fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	gout:
		model:
			multi: true
			source: ["Serum uric acid (sUA) <6mg/dL, <5mg/dL in patients with severe gout. <3mg/dL are not recommended long-term",
			'Suppress Gout symptoms – pain, swelling, redness','Improve QOL',
			'Prevent crystal deposition','Prevent bone erosion','Provide patient friendly treatment (regimen, ROA, cost, minimal AE, etc)']
		view:
			control: 'checkbox'
			label: 'Gout - Goals during therapy:'
			class: 'list'
			readonly: true

	gout_notes:
		view:
			control: 'area'
			label: 'Notes'

	krystexxa:
		model:
			multi: true
			source: ["An FDA-approved patient medication guide must be dispensed with this medication",
			'Kyrstexxa is not recommended for the treatment of asymptomatic hyperuricemia',
			'Discontinue use of oral anti-hyperuricemic agents prior to initiating Krystexxa and do not initiate during the course of therapy',
			'An increase in gout flares is frequently observed upon initiation of anti-hyperuricemic therapy, including Krystexxa. If flares occur during treatment, Krystexxa does NOT need to be discontinued. Gout flare prophylaxis with NSAIDS or colchicine is recommended starting 1 week before initiation of Krystexxa and lasting at least 6 months.',
			'If you have questions about screening for G6PD deficiency, please call Horizon at ************.']
		view:
			control: 'checkbox'
			label: 'Krystexxa Special Alerts:'
			class: 'list'
			readonly: true

	krystexxa_warn:
		model:
			multi: true
			source: ['Anaphylaxis and infusion reactions have been reported during and after administration of Krystexxa. Anaphylaxis may occur with any infusion, including a first infusion, and generally manifests within 2 hours of the infusion. Although, delayed-type hypersensitivity reactions have also been reported. *The risk of anaphylaxis and infusion reaction sis higher in patient’s who have lost therapeutic response. In the event of anaphylaxis or infusion reaction, the infusion should be slowed, or stopped and restarted at a slower rate.']
		view:
			control: 'checkbox'
			label: 'Krystexxa Warning'
			class: 'list'
			readonly: true

	krystexxa_warn_ack:
		model:
			required: true
			source: ['Acknowledged']
		view:
			control: 'checkbox'
			label: 'Krystexxa Warning Acknowledgement'

	normal_g6pd:
		model:
			source: ['No', 'Yes']
			required: true
			if:
				'No':
					fields: ['normal_g6pd_warn', 'normal_g6pd_warn_ack']
					require_fields: ['symptoms_notes']
		view:
			control: 'radio'
			label: 'Patient has normal G6PD activity from lab tests'

	normal_g6pd_warn:
		model:
			multi: true
			source: ['Do NOT administer Krystexxa to patients with G6PD deficiency']
		view:
			control: 'checkbox'
			label: 'WARNING'
			class: 'list'
			readonly: true

	normal_g6pd_warn_ack:
		model:
			required: true
			source: ['Acknowledged']
		view:
			control: 'checkbox'
			label: 'Warning Acknowledgement'

	normal_g6pd_notes:
		model:
			required: true
		view:
			control: 'area'
			label: 'Notes'

	uratel_disc:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['uratel_disc_warn','uratel_disc_warn_ack', 'uratel_disc_notes']
		view:
			control: 'radio'
			label: 'Patient has discontinued urate-lowering therapies'

	uratel_disc_warn:
		model:
			multi: true
			source: ['Advise physician and patient that Krystexxa is not recommended in conjunction with oral urate-lowering medications']
		view:
			control: 'checkbox'
			label: 'WARNING'
			class: 'list'
			readonly: true

	uratel_disc_warn_ack:
		model:
			required: true
			source: ['Acknowledged']
		view:
			control: 'checkbox'
			label: 'Warning Acknowledgement'

	uratel_disc_notes:
		model:
			required: true
		view:
			control: 'area'
			label: 'Notes'

	goutf_prophy:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['goutf_prophy_notes']
				'Yes':
					fields: ['goutf_prophy_med']
		view:
			control: 'radio'
			note:'Either NSAIDs or colchicine is recommended, beginning at least 1 week prior to initiation of Krystexxa treatment and continuing for at least 6 months'
			label: 'Patient has gout flare prophylaxis on board'
	
	goutf_prophy_notes:
		model:
			required: true
		view:
			control: 'area'
			label: 'Notes'

	goutf_prophy_med:
		model:
			required: true
		view:
			control: 'area'
			label: 'Medications'

	urate_reminder:
		model:
			multi: true
			source: ['Remind patient why they are not taking oral urate-lowering therapies and ensure they are taking gout flare prophylaxis']
		view:
			control: 'checkbox'
			label: 'Reminder'
			class: 'list'
			readonly: true

	flares_reminder:
		model:
			multi: true
			source: ['Remind patient that they may have gout flares, and Krystexxa therapy can be continued regardless of gout']
		view:
			control: 'checkbox'
			label: 'Reminder'
			class: 'list'
			readonly: true

	flareups:
		view:
			note: 'if any'
			label: 'Frequency of flare-ups'

	symptoms_notes:
		view:
			control: 'area'
			label: 'Notes'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Krystexxa Assessment':
			fields: ['gout','gout_notes','krystexxa','krystexxa_warn',
			'krystexxa_warn_ack','normal_g6pd','normal_g6pd_warn', 'normal_g6pd_warn_ack', 'normal_g6pd_notes',
			'uratel_disc','uratel_disc_warn', 'uratel_disc_warn_ack', 'uratel_disc_notes', 'goutf_prophy', 'goutf_prophy_notes', 'goutf_prophy_med',
			'urate_reminder', 'flares_reminder']
		'Gout Symptoms':
			fields: ['flareups', 'symptoms_notes']

view:
	comment: 'Patient > Careplan > Intial Assessment > Krystexxa'
	label: 'Intial Assessment: Krystexxa'
	open: 'read'
