fields:

	day:
		model:
			source: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
		view:
			label: 'Day'
	
	start_time:
		model:
			default: '9:00 AM'
			type: 'time'
		view:
			label: 'Open Time'
	
	close_time:
		model:
			default: '5:00 PM'
			type: 'time'
		view:
			label: 'Close Time'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write: ['admin', 'pharm']

	name: '{day} {start_time} - {close_time}'
	sections:
		'Site Business Hours':
			fields: ['day' , 'start_time', 'close_time']

view:
	hide_cardmenu: true
	comment: 'Site > Business Hours'
	grid:
		fields: ['day', 'start_time', 'close_time']
		sort: ['-created_on']
	label: 'Site Business Hours'
	open: 'edit'