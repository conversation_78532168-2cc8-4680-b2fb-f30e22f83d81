fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Hepatitis B Screen
	hepb_screening_negative:
		model:
			source: ['No', 'Yes']
			required: true
			if:
				'No':
					fields: ['hepb_screening_warning']
		view:
			label: 'Has Hepatitis B screening been performed and results negative?'
			control: 'radio'

	hepb_screening_warning:
		model:
			default: 'Prior to initiating OCREVUS, perform Hepatitis B virus (HBV) screening. OCREVUS is contraindicated in patients with active HBV confirmed by positive results for HBsAg and anti-HBV tests. For patients who are negative for surface antigen [HBsAg] and positive for HB core antibody [HBcAb+] or are carriers of HBV [HBsAg+], consult liver disease experts before starting and during treatment.'
		view:
			label: 'Hepatitis B Screening Warning'
			control: 'area'
			readonly: true

	had_vaccinations:
		model:
			source: ['No', 'Yes']
			required: true
			if:
				'Yes':
					fields: ['had_vaccinations_warning']
		view:
			label: 'Has <PERSON><PERSON> had any vaccinations within the past 6 weeks?'
			control: 'radio'

	had_vaccinations_warning:
		model:
			default: 'Because vaccination with live-attenuated or live vaccines is not recommended during treatment and after discontinuation until B-cell repletion, administer all necessary immunizations according to immunization guidelines at least 6 weeks prior to initiation of OCREVUS.'
		view:
			label: 'Vaccinations Warning'
			control: 'area'
			readonly: true

	had_steroid_premed:
		model:
			source: ['No', 'Yes']
			required: true
			if:
				'No':
					fields: ['had_steroid_premed_warning', 'notified_physician']
		view:
			label: 'Has Patient been ordered steroid premed?'
			control: 'radio'

	had_steroid_premed_warning:
		model:
			default: 'Pre-medicate with 100 mg of methylprednisolone (or an equivalent corticosteroid) administered intravenously approximately 30 minutes prior to each OCREVUS infusion to reduce the frequency and severity of infusion reactions [see Warnings and Precautions (5.1)]. Pre-medicate with an antihistamine (e.g., diphenhydramine) approximately 30-60 minutes prior to each OCREVUS infusion to further reduce the frequency and severity of infusion reactions. The addition of an antipyretic (e.g., acetaminophen) may also be considered.'
		view:
			label: 'Steroid Premed Warning'
			control: 'area'
			readonly: true

	notified_physician:
		model:
			source: ['No', 'Yes']
			required: true
			if:
				'No':
					fields: ['notified_physician_explain']
		view:
			label: 'Physician notified to obtain orders?'
			control: 'radio'

	notified_physician_explain:
		view:
			label: 'Why not?'
			control: 'area'

	infection_risk:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Has patient been counseled about increased risk of infection from Ocrevus?'
			control: 'radio'

	malignancy_risk:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Has patient been counseled about increased risk of breast cancer from Ocrevus?'
			control: 'radio'

	contraception:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Has patient been counseled about need for both male and female contraception during the course therapy?'
			control: 'radio'

	vaccinations:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Has the patient been counseled to not receive any live vaccinations during the course of therapy?'
			control: 'radio'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'csr', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Hepatitis B Screen':
			fields: ['hepb_screening_negative', 'hepb_screening_warning']
		'Vaccinations':
			fields: ['had_vaccinations', 'had_vaccinations_warning']
		'Steroid Premed':
			fields: ['had_steroid_premed', 'had_steroid_premed_warning', 'notified_physician', 'notified_physician_explain']
		'Ocrevus Assessment':
			fields: [ 'infection_risk', 'malignancy_risk', 'contraception', 'vaccinations']
view:
	comment: 'Patient > Careplan > Assessment > Ocrevus'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Ocrevus'
	open: 'read'
