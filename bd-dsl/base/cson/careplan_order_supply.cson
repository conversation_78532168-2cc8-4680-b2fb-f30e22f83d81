fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	external_id:
		view:
			label: 'External ID'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
			query: 'select_inv_in_stock'
			querytemplate: 'inventoryTemplate'
			sourcefilter:
				type:
					'static': ['Supply']
				active:
					'static': 'Yes'
		view:
			columns: 2
			label: 'Supply'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'supply_billable': ['supply_billable']
					'quantity_per_package': ['quantity_per_package']
					'container_unit': ['container_unit']
					'hcpc_code': ['hcpc_code']
					'ndc': ['ndc']
					'formatted_ndc': ['formatted_ndc']
					'upc': ['upc']
					'upin': ['upin']
			]
			validate:[
				{
					name: 'MatchFields'
					parentField: 'subform_supply'
					errorMessage = 'Duplicate item entered, please select a different item.'
					matchingField = 'inventory_id'
					type: 'subform'
				}
			]

	hcpc_code:
		view:
			columns: 4
			class: 'claim-field'
			label: 'HCPC Code'
			readonly: true

	ndc:
		view:
			label: 'NDC'
			offscreen: true
			readonly: true

	formatted_ndc:
		view:
			columns: 4
			label: 'NDC'
			readonly: true

	upc:
		view:
			columns: 4
			label: 'UPC'
			readonly: true

	upin:
		view:
			columns: 4
			label: 'UPIN'
			readonly: true

	quantity_per_container:
		model:
			default: 1
			required: false
			type: 'decimal'
			max: 9999999.999
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Container Units'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'inventory_id'
					source_fields: ['quantity_per_container', 'container_unit']
					separator: ' per '
				}
			]

	container_unit:
		model:
			default: 'container'
		view:
			label: 'Container Unit'
			readonly: true
			offscreen: true

	dispense_unit:
		model:
			default: 'each'
			required: true
			source: ['each', 'container']
			if:
				'container':
					fields: ['dispense_containers']
					readonly:
						fields: ['dispense_quantity']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Dispense Unit'

	dispense_containers:
		model:
			min: 1
			default: 1
			type: 'int'
			required: true
		view:
			columns: 4
			label: 'Dispense Quantity'
			note: 'containers'
			transform: [
				{
					name: 'MultiplyFields'
					fields: ['quantity_per_container', 'dispense_containers']
					destination: 'dispense_quantity'
				}
			]

	dispense_quantity:
		model:
			type: 'int'
			required: true
		view:
			readonly: true
			columns: 4
			label: 'Total Dispense Quantity'

	bill_supplies:
		model:
			prefill: ['parent.bill_supplies']
			source: ['Yes']
			if:
				'!':
					prefill:
						supply_billable: ''
					readonly:
						fields: ['supply_billable']
		view:
			control: 'radio'
			label: 'Billing Method'
			readonly: true
			offscreen: true

	supply_billable:
		model:
			prefill: ['parent.bill_supplies']
			required: false
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Billable?'

	part_of_kit:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Billed as Kit?'

	one_time_only:
		model:
			max: 4
			source: ['Yes']
			default: 'Yes'
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: '1x Only?'
			columns: 3

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['inventory_id']
		]
	name: '{inventory_id} {dispense_quantity}'
	sections:
		'Order Supply':
			fields: ['inventory_id', 'hcpc_code', 'ndc',
			'formatted_ndc', 'upc', 'upin', 'container_unit', 'dispense_unit',
			'dispense_containers', 'dispense_quantity', 'bill_supplies', 'supply_billable', 'part_of_kit', 'one_time_only']

view:
	hide_cardmenu: true
	comment: 'Supply'
	grid:
		fields: ['inventory_id', 'dispense_quantity', 'supply_billable', 'part_of_kit', 'one_time_only']
		width: [40, 15, 15, 15, 15]
		sort: ['-id']
	label: 'Supply'
	open: 'edit'