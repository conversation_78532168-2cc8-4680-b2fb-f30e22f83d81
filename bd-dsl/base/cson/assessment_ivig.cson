fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Demographics
	gender:
		model:
			if:
				'Female':
					fields: ['on_birthcon', 'are_preg', 'are_postmeno']
			prefill: ['patient']
		view:
			label: 'Sex'
			offscreen: true
			readonly: true

	delivery_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Drug delivery date'
			columns: 2

	# Renal Assessment
	has_diabetes:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['has_insulin']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with diabetes?'
			columns: 2

	has_insulin:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you depend on insulin to regulate blood sugar?'
			columns: 2

	had_kidney_disease:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with kidney disease?'
			columns: 2

	meds_taking:
		model:
			max: 32
			min: 1
			multi: true
			source: ['Acyclovir','Aminoglycosides','Amphotericin','Atripla','Cisplatin','Diuretics (loops, thiazides)','NSAIDS',
				'Prograf','Proton-Pump Inhibitors','Tenofovir','Viread','Truvada','Other','None']
			if:
				'Other':
					fields: ['meds_taking_other']
		view:
			control: 'checkbox'
			label: 'Are you currently taking any of the following (concomitant nephrotoxic) drugs?'
			columns: -2

	meds_taking_other:
		view:
			label: 'Other Drugs'
			columns: 2

	urine_drop:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you noticed a drop in urine output?'
			columns: 2

	has_htn:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pressure_controlled']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with hypertension (HTN)?'
			columns: 2

	pressure_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your blood pressure currently controlled?'
			columns: 2

	high_chol:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['chol_controlled']
		view:
			control: 'radio'
			label: 'Is your cholesterol level high?'
			columns: -2

	chol_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your cholesterol currently controlled?'
			columns: 2

	heart_cond:
		model:
			max: 64
			min: 1
			multi: true
			source: ['Congestive heart failure (CHF)', 'Cardiomyopathy', 'Valve Disease', 'Congenital Defects', 'Atrial Fibrillation', 'Angina', 'Pulmonary disease', 'Other', 'None']
			if:
				'Other':
					fields: ['heart_cond_other']
		view:
			control: 'checkbox'
			label: 'Do you suffer from any of the following heart diseases?'
			columns: -2

	heart_cond_other:
		view:
			control: 'area'
			label: 'Other Heart Diseases'
			columns: 2

	fam_cond:
		model:
			max: 64
			min: 1
			multi: true
			source: ['Coronary Artery Disease (CAD/Atherosclerotic)', 'Angina', 'Deep Vein Thrombosis (DVT)',
						'Cerebral Infarction', 'Myocardial Infarction', 'Other', 'None']
			if:
				'Other':
					fields: ['fam_cond_other']
		view:
			control: 'checkbox'
			label: 'Do you or any of your Parents or siblings have a history of:'
			columns: -2

	fam_cond_other:
		view:
			control: 'area'
			label: 'Other Family History'
			columns: 2

	per_immob:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you experience prolonged periods of immobilization?'
			columns: 2

	had_throm:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with thrombophilia?'
			columns: 2

	had_sic:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with sickle cell anemia?'
			columns: 2

	has_lowbp:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_sep']
		view:
			control: 'radio'
			label: 'Do your hands/extremities feel cold and clammy (do you experience low BP)?'
			columns: -2

	had_sep:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any recent cases of sepsis or infection in blood?'
			columns: 2

	had_can:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_can_details']
		view:
			control: 'radio'
			label: 'Are you currently or have you recently been on cancer treatment?'
			columns: -2

	had_can_details:
		view:
			label: 'What type of cancer and when?'
			columns: 2

	on_birthcon:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you currently on any birth control or hormone replacement pills?'
			columns: 2

	are_preg:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: ' Are you currently pregnant or have given birth in the last 6 weeks?'
			columns: 2

	are_postmeno:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: ' Are you postmenopausal?'
			columns: 2

	have_headaches:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['type_headache', 'headache_assoc', 'loc_headache', 'headache_scale', 'freq_headache', 'headache_dur']
		view:
			control: 'radio'
			label: 'Does the patient have headaches?'
			columns: -2

	type_headache:
		model:
			max: 9
			min: 1
			source: ['Migraines', 'Sinus', 'Cluster', 'Tension']
		view:
			control: 'radio'
			label: 'Type of Headaches'
			columns: 2

	headache_assoc:
		model:
			max: 9
			min: 1
			multi: true
			source: ['Nausea', 'Light Sensitivity', 'Sound Sensitivity', 'Smell Sensitivity', 'Aura', 'Menses']
		view:
			control: 'checkbox'
			label: 'Associated With:'
			columns: 2

	loc_headache:
		model:
			max: 10
			min: 1
			source: ['Unilateral', 'Bilateral']
		view:
			control: 'radio'
			label: 'Location of Headaches'
			columns: 2

	headache_scale:
		model:
			max: 1
			min: 1
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Pain Scale'
			columns: 2

	freq_headache:
		model:
			max: 32
			min: 1
			source: ['Daily', 'Weekly', 'Monthly']
		view:
			label: 'Frequency of Headaches'
			columns: 2

	headache_dur:
		model:
			max: 32
			min: 1
			source: ['Minutes', 'Hours', 'Days']
		view:
			label: 'Duration'
			control: 'radio'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['patient', 'careplan']
	name: 'IVIG Assessment'
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
	sections:
		'IG Renal Disease Risk Assessment':
			fields: ['gender','has_diabetes', 'has_insulin', 'had_kidney_disease', 'meds_taking', 'meds_taking_other', 'urine_drop']
		'IG Thromboembolic Risk Assessment':
			fields: ['has_htn', 'pressure_controlled', 'high_chol', 'chol_controlled', 'heart_cond', 'heart_cond_other', 'fam_cond', 'fam_cond_other', 'per_immob',
						'had_throm', 'had_sic', 'has_lowbp', 'had_sep', 'had_can', 'had_can_details', 'on_birthcon',
						'are_preg', 'are_postmeno']
		'IG Headache Assessment':
			fields: ['have_headaches', 'type_headache', 'headache_assoc', 'loc_headache',
						'headache_scale', 'freq_headache', 'headache_dur']
		'IG Drug Details':
			fields: ['delivery_date']

view:
	comment: 'Patient > Intake > Assessment > IG'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
	label: 'Assessment Questionnaire: IG'
	open: 'read'
