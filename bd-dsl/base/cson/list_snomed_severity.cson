fields:

	code:
		model:
			required: true
			type: 'text'
		view:
			label: 'Code'
			columns: 2

	name:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 2

	storage_id:
		model:
			source: 'list_storage'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Storage'

	admin_directions:
		model:
			required: true
			max: 45
		view:
			label: 'Admin Directions'
			note: 'Max 45'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name']

view:
	comment: 'Manage > SNOMED Reaction Severity'
	find:
		basic: ['code', 'name']
	grid:
		fields: ['code', 'name']
		sort: ['name']
	label: 'SNOMED Reaction Severity'
	open: 'read'
