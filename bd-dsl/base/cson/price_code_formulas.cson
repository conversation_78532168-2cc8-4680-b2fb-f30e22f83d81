fields:
	contract_type:
		model:
			source: ['Contract', 'Shared Contract']
			if:
				'Contract':
					readonly:
						fields: ['special_price_basis', 'special_price_multiplier']
		view:
			columns: 4
			label: 'Contract Type'
			control: 'radio'
			readonly: true
			offscreen: true

	code:
		view:
			label: 'Formula Code'
			columns: 4
			findunique: true
			note: 'This is the code that will be used to link the contracts to the price code formulas'
	external_id:
		view:
			label: 'External ID'
			offscreen: true
			readonly: true
	active:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			columns: 4
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Active?'
			findfilter: 'Yes'
	expected_price_basis:
		model:
			source: 'list_price_basis'
			sourceid: 'code'
		view:
			columns: 4
			label: 'Price Basis'
	special_price_basis:
		model:
			source: 'list_price_basis'
			sourceid: 'code'
		view:
			columns: 4
			label: 'Price Basis'
	code_category:
		model:
			required: true
			source: 'list_price_code'
			sourceid: 'code'
		view:
			columns: 4
			label: 'Price Category'
	expected_price_multiplier:
		model:
			rounding: 0.00001
			type: 'decimal'
			if:
				'*':
					fields: ['expected_price_basis']
					require_fields: ['expected_price_basis']
		view:
			columns: 4
			label: 'Expected Price Multiplier'
	special_price_multiplier:
		model:
			rounding: 0.00001
			type: 'decimal'
			if:
				'*':
					fields: ['special_price_basis']
					require_fields: ['special_price_basis']
		view:
			columns: 4
			label: 'Special Price Multiplier'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison',  'pharm']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	reportable: true
	indexes:
		unique: [
			['code', 'code_category', 'active']
		]
	name: '{code_category} - {expected_price_basis} - {special_price_basis}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['contract_type', 'code', 'code_category', 'expected_price_basis', 'expected_price_multiplier', 'special_price_basis', 'special_price_multiplier', 'active']
		
view:
	dimensions:
		width: '85%'
		height: '65%'
	comment: 'Manage > Price Code Formulas'
	find:
		basic: ['code', 'active', 'expected_price_basis', 'special_price_basis']
	grid:
		fields: ['code', 'active', 'expected_price_basis', 'expected_price_multiplier', 'special_price_basis', 'special_price_multiplier']
		sort: ['code', 'code_category']
	label: 'Price Code Formulas'
	open: 'read'
