fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	recent_infections:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['infusion_safety']
			required: true
		view:
			label: 'Has the patient had any recent infections, fevers or symptoms of infection?'
			control: 'radio'

	infusion_safety:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					note: 'Please Contact Physician'
			required: true
		view:
			label: 'Is it still safe to go on with infusion as scheduled?'
			control: 'radio'

	contraceptive_remind:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Has patient been reminded and counseled about need for both male and female contraception during the course therapy?'
			control: 'radio'

	vaccinations_remind:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Has the patient been reminded and counseled to not receive any live vaccinations during the course of therapy?'
			control: 'radio'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'nurse', 'pharm']
		read:       ['admin', 'csr', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'csr', 'nurse', 'pharm']
	indexes:
		many: [
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		ongoing_ocrevus:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Ocrevus Assessment':
			fields: ['recent_infections', 'infusion_safety', 'contraceptive_remind', 'vaccinations_remind']

view:
	comment: 'Patient > Careplan > Ongoing > Ocrecus'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Ongoing Assessment: Ocrecus'
	open: 'read'
