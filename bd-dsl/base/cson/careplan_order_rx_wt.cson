fields:


	rx_no:
		model:
			type: 'text'
		view:
			label: 'Rx #'
			readonly: true
			offscreen: true

	external_id:
		model:
			type: 'int'
		view:
			label: 'External ID'
			readonly: true
			offscreen: true

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	medid:
		model:
			required: false
		view:
			readonly: true
			offscreen: true
			class: 'fdb-field'
			label: 'FDB MEDID'

	inventory_id:
		model:
			required: true
			source: 'inventory'
			sourcefilter:
				type:
					'static': ['Drug']
				active:
					'static': 'Yes'
				medid:
					'dynamic': '{medid}'
		view:
			columns: 2
			label: 'Vial Size'

	dispense_quantity:
		model:
			min: 1
			type: 'decimal'
			max: 9999999.999
			rounding: 1
			required: true
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 2
			label: 'Dispense Quantity'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['inventory_id', 'dispense_quantity']
	indexes:
		many: [
			['inventory_id']
		]
	sections:
		'Work Order Item':
			hide_header: true
			indent: false
			fields: ['rx_no', 'medid', 'inventory_id', 'dispense_quantity']

view:
	dimensions:
		width: '65%'
		height: '25%'
	hide_cardmenu: true
	comment: 'Patient > Prescription > Work Order Item'
	grid:
		fields: ['inventory_id', 'dispense_quantity']
		sort: ['-created_on']
	find:
		basic: ['inventory_id', 'dispense_quantity']
	label: 'Work Order Item'
	open: 'read'
