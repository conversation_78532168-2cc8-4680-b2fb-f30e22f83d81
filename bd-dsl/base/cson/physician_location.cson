fields:

	name:
		model:
			required: true
		view:
			columns: 2
			label: 'Location Name'
	external_id:
		view:
			offscreen: true
			readonly: true
	is_primary:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Primary Practice Location?'
			class: 'checkbox-only'
	addresslabel:
		view:
			label: 'Address'
			offscreen: true
			readonly: true

	address1:
		model:
			max: 128
		view:
			columns: 'addr_1'
			label: 'Address 1'
			class: "api_prefill"
			transform: [{
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'address1': ['addressLabel']
					'city': ['city']
					'state_id': ['stateCode']
					'zip': ['postalCode']
			},
			{
				name: 'BuildAddressLabel'
				dest_field: 'addresslabel'
				fields:
					'street': ['address1']
					'street2': ['address2']
					'city': ['city']
					'state': ['state_id']
					'zip': ['zip']
				
			}
			]

	address2:
		model:
			max: 128
		view:
			columns: 'addr_2'
			label: 'Address 2'
			transform: [{
				name: 'BuildAddressLabel'
				dest_field: 'addresslabel'
				fields:
					'street': ['address1']
					'street2': ['address2']
					'city': ['city']
					'state': ['state_id']
					'zip': ['zip']
				
			}]

	city:
		model:
			type: 'text'
		view:
			columns: 'addr_city'
			label: 'City'
			transform: [{
				name: 'BuildAddressLabel'
				dest_field: 'addresslabel'
				fields:
					'street': ['address1']
					'street2': ['address2']
					'city': ['city']
					'state': ['state_id']
					'zip': ['zip']
				
			}]

	state_id:
		model:
			required: true 
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'State'
			transform: [{
				name: 'BuildAddressLabel'
				dest_field: 'addresslabel'
				fields:
					'street': ['address1']
					'street2': ['address2']
					'city': ['city']
					'state': ['state_id']
					'zip': ['zip']
				
			}]

	zip:
		model:
			max: 10
			min: 5
		view:
			format: 'us_zip'
			label: 'Zip'
			columns: 'addr_zip'
			transform: [{
					name: 'CityStateTransform'
					fields:
						zip:'zip'
						city:'city'
						state:'state'
			},
			{
				name: 'BuildAddressLabel'
				dest_field: 'addresslabel'
				fields:
					'street': ['address1']
					'street2': ['address2']
					'city': ['city']
					'state': ['state_id']
					'zip': ['zip']
			}]

	phone_number:
		model:
			required: false
			max: 21
		view:
			columns: 2
			format: 'us_phone'
			label: 'Phone #'

	fax_number:
		model:
			max: 21
		view:
			columns: 2
			format: 'us_phone'
			label: 'Fax #'

	notes:
		view:
			control: 'area'
			label: 'Notes'

	# Credentialing
	ncpdp_id:
		view:
			columns: 3
			label: 'NCPDP ID'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{7}$'
				error: 'Invalid NCPDP ID, must be 7 digits long'
			}]

	state_cs_license:
		view:
			columns: 3
			label: 'State CS License #'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{5,20}$'
				error: 'Invalid State Controlled Substance License Number'
			}]

	dea:
		model:
			max: 16
		view:
			columns: 3
			label: 'DEA'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z]{2}\\d{7}$'
				error: 'Invalid DEA number, DEA numbers must be 2 letters followed by 7 numbers'
			}]

	mcd:
		view:
			columns: 3
			label: 'MCD #'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{6,15}$'
				error: 'Invalid Medicaid Number'
			}]

	mcr_ptan_id:
		view:
			columns: 3
			label: 'MCR Provider # (PTAN)'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{5,12}$'
				error: 'Medicare Provider Number (PTAN), must be 5-12 alphanumeric characters"'
			}]

	rems:
		view:
			columns: 3
			label: 'REMs ID'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{5,20}$'
				error: 'Invalid REMs ID'
			}]
	email:
		model:
			required: false
			max: 128
		view:
			label: 'Email Address'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr','liaison', 'pharm']
		write:      ['admin', 'csr','liaison', 'pharm']

	name: ['name', 'zip']
	sections:
		'Location':
			fields: ['addresslabel', 'name', 'is_primary', 'address1', 'address2', 'city', 'state_id', 'zip', 'phone_number', 'fax_number', 'email', 'notes']
		'Credentialing':
			fields: ['ncpdp_id', 'state_cs_license', 'dea', 'mcd', 'mcr_ptan_id', 'rems']

view:
	dimensions:
		width: '90%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Manage > Physician Location'
	find:
		advanced: ['city', 'state_id', 'zip']
	grid:
		fields: ['is_primary', 'name', 'city', 'state_id', 'zip', 'phone_number', 'fax_number']
	label: 'Physician Location'
