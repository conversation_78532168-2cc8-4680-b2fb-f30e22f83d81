fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	type:
		model:
			required: true
			source: ['Locked','Unlocked']
			if:
				'Locked':
					fields: ['locked_reason']
				'Unlocked':
					fields: ['unlock_reason']
		view:
			label: 'Type'
			readonly: true
			columns: 4

	unlocked_locked_on:
		model:
			required: true
			type: 'datetime'
		view:
			label: 'Locked/Unlocked On'
			readonly: true
			columns: 4

	unlocked_locked_by:
		model:
			required: true
			source: 'user'
		view:
			label: 'Locked/Unlocked By'
			readonly: true
			columns: 4

	locked_reason:
		model:
			source: ['Approved by Care Coordinator', 'Ready to Bill', 'Locked for Posting']
		view:
			label: 'Locked Reason'
			readonly: true
			columns: 2

	unlock_reason:
		model:
			source: ['Corrections', 'Billing/Time Adjustments', 'Other']
		view:
			label: 'Unlocked Reason'
			readonly: true
			columns: 2

	final_reason:
		view:
			label: 'Reason'
			readonly: true
			offscreen: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	bundle: ['audit']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id']

	sections:
		'Encounter Lock/Unlock History':
			hide_header: true
			indent: false
			fields: ['type', 'unlocked_locked_on', 'unlocked_locked_by',
			'locked_reason', 'unlock_reason']

view:
	comment: 'Patient > Careplan > Encounter > Encounter Lock/Unlock History'
	grid:
		fields: ['type', 'unlocked_locked_on', 'unlocked_locked_by', 'final_reason']
		sort: ['created_on']
	label: 'Encounter Lock/Unlock History'
	open: 'read'
