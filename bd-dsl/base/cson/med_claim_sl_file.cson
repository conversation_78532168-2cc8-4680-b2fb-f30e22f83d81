fields:

	file:
		model:
			required: true
			type: 'json'
		view:
			control: 'file'
			label: 'Document / File'
			note: 'Max 100MB. Only documents, images, and archives supported.'
			_meta:
				location: '2400 K3'
				field: '01'
				path: 'claimInformation.serviceLines[{idx1-50}].fileInformation[{idx1-10}]'

	comments:
		model:
			max: 1024
		view:
			control: 'area'
			label: 'Comments'
			note: 'INTERNAL USE ONLY.'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		delete:     ['admin', 'pharm', 'patient']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
	name: ['file']
	sections:
		'Service Line Attachment':
			hide_header: true
			fields: ['file', 'comments']

view:
	dimensions:
			width: '65%'
			height: '45%'
	hide_cardmenu: true
	reference: '2400'
	comment: 'Medical Claim > Service Line Attachment'
	hide_header: true
	grid:
		fields: ['created_on', 'created_by', 'file', 'comments']
		sort: ['-id']
	label: 'Service Line Attachment'
