fields:
	is_test:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Is Test Claim?"
			readonly: true
			offscreen: true

	transaction_code:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '103-A3'
			if:
				'B1': # Drug Billing
					fields: ['product_id', 'proc_mod_code', 'quantity_prescribed',
					'quantity_dispensed', 'compound_code', 'daw_code', 'date_rx_written', 'number_of_refills_authorized',
					'rx_origin_code', 'sub_clar_code', 'sp_pk_indicator', 'sched_rx_no', 'unit_of_measure', 'level_of_service', 'pa_id',
					'pt_assign_indicator', 'admin_route']
					require_fields: ['fill_number', 'quantity_dispensed', 'day_supply', 'date_rx_written']
				'B3': # Drug Reverse and Bill
					fields: ['product_id', 'proc_mod_code',
					'quantity_dispensed', 'compound_code', 'daw_code', 'date_rx_written', 'number_of_refills_authorized',
					'rx_origin_code', 'sub_clar_code', 'sp_pk_indicator', 'sched_rx_no', 'unit_of_measure', 'level_of_service', 'pa_id',
					'pt_assign_indicator', 'admin_route']
					require_fields: ['fill_number', 'quantity_dispensed', 'day_supply', 'date_rx_written']
				'S1': # Service Billing
					fields: ['service_id', 'quantity_prescribed', 'proc_mod_code',
					'quantity_dispensed', 'date_rx_written', 'number_of_refills_authorized', 'og_service_id', 'og_rx_id_qualifier',
					'og_rx_id', 'og_rx_quantity', 'sched_rx_no', 'level_of_service', 'pa_id',
					'pt_assign_indicator']
				'S3': # Service Reverse and Bill
					fields: ['service_id', 'quantity_prescribed', 'proc_mod_code',
					'quantity_dispensed', 'date_rx_written', 'number_of_refills_authorized', 'og_service_id', 'og_rx_id_qualifier',
					'og_rx_id', 'og_rx_quantity', 'sched_rx_no', 'level_of_service', 'pa_id',
					'pt_assign_indicator']
		view:
			label: 'Transmission Code'
			offscreen: true
			readonly: true

	rx_svc_no_ref_qualifier:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '455-EM'
		view:
			reference: '455-EM'
			note: '455-EM'
			label: ' Qualifier'
			readonly: true
			offscreen: true

	rx_svc_no:
		model:
			required: true
			type: 'text'
		view:
			columns: 4
			reference: '402-D2'
			note: '402-D2'
			label: 'Prescription #'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{1,12}$'
				error: 'Invalid Prescription Number, must be 1-12 digits'
			}]

	fill_number:
		model:
			type: 'int'
			max: 99
		view:
			columns: 4
			reference: '403-D3'
			note: '403-D3 - Max 99'
			label: 'Fill #'

	sched_rx_no:
		model:
			max: 12
		view:
			reference: '454-EK'
			note: '454-EK - Used for controlled substances'
			label: 'Scheduled '
			readonly: true
			offscreen: true
			_meta:
				copy_forward: true

	patient_id:
		model:
			required: true
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	order_id:
		model:
			type: 'int'
			source: 'careplan_order'
		view:
			label: 'Order'
			readonly: true
			offscreen: true

	insurance_id:
		model:
			type: 'int'
			required: true
			source: 'patient_insurance'
		view:
			label: 'Insurance'
			readonly: true
			offscreen: true

	payer_id:
		model:
			source: 'payer'
		view:
			label: 'Payer'
			readonly: true
			offscreen: true

	product_id:
		model:
			source: 'inventory'
			sourcefilter:
				type:
					'static': 'Drug'
				active:
					'static': 'Yes'
		view:
			form_link_enabled: true
			columns: 2
			label: 'Drug'
			readonly: true

	service_id:
		model:
			required: true
			source: 'inventory'
			sourcefilter:
				type:
					'static': 'Billable'
				active:
					'static': 'Yes'
		view:
			form_link_enabled: true
			columns: 2
			label: 'Billable'
			readonly: true

	prod_svc_id_qualifier:
		model:
			default: '03'
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '436-E1'
		view:
			columns: 4
			reference: '436-E1'
			note: '436-E1'
			label: 'Product ID Qualifier'
			readonly: true

	prod_svc_id:
		model:
			max: 19
			required: true
		view:
			columns: 4
			reference: '407-D7'
			note: '407-D7'
			label: 'Product ID'

	proc_mod_code:
		model:
			multi: true
			source: 'list_ncpdp_ext_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '459-ER'
		view:
			columns: 2
			reference: '459-ER'
			note: '459-ER - Max 4'
			label: 'Procedure Modifier Code(s)'
			max_count: 4
			readonly: true
			offscreen: true
			_meta:
				copy_forward: true

	quantity_dispensed:
		model:
			min: 0.001
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			reference: '442-E7'
			note: '442-E7'
			label: 'Quantity Dispensed'

	unit_of_measure:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '600-28'
		view:
			columns: 4
			reference: '600-28'
			note: '600-28'
			label: 'UOM'

	day_supply:
		model:
			type: 'int'
			required: true
			max: 999
		view:
			columns: 4
			reference: '405-D5'
			note: '405-D5 - Max 999'
			label: 'Days Supplied'
			validate: [{
				name: 'PromptToChangeRx'
			}]

	number_of_refills_authorized:
		model:
			type: 'int'
			max: 99
		view:
			columns: 4
			reference: '415-DF'
			note: '415-DF - Max 99'
			label: 'Authorized Refills'

	daw_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '408-D8'
		view:
			columns: 2
			reference: '408-D8'
			note: '408-D8'
			label: 'DAW'
			_meta:
				copy_forward: true

	date_rx_written:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			reference: '414-DE'
			note: '414-DE'
			label: 'Date Prescription Written'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	quantity_prescribed:
		model:
			min: 0.001
			type: 'decimal'
			rounding: 0.001
			required: true
			max: 9999999.999
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			reference: '460-ET'
			note: '460-ET'
			label: 'Quantity Prescribed'

	admin_route:
		model:
			source: 'list_ncpdp_route'
			sourceid: 'code'
			sourcefilter:
				payer_id:
					'dynamic': '{payer_id}'
			query: 'select_available_ncpdp_admin_routes'
		view:
			columns: 3
			reference: '995-E2'
			note: '995-E2'
			label: 'Route of Admin'
			_meta:
				copy_forward: true

	og_product_id:
		model:
			required: false
			source: 'list_fdb_ndc'
			sourceid: 'code'
			if:
				'*':
					fields: ['og_rx_id_qualifier', 'og_rx_id', 'og_rx_quantity']
					prefill:
						og_rx_id_qualifier: '03'
				'!':
					prefill:
						og_rx_id_qualifier: ''
		view:
			label: 'Original Drug'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_fdb_ndc/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'ndc': ['og_rx_id'],
			]

	og_service_id:
		model:
			required: false
			source: 'list_billing_code'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Original Billable'
			readonly: true
			offscreen: true

	og_rx_id_qualifier:
		model:
			default: '03'
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '453-EJ'
		view:
			columns: 2
			reference: '453-EJ'
			note: '453-EJ'
			label: 'Original Product ID Qualifier'

	og_rx_id:
		model:
			required: true
			max: 19
		view:
			columns: 2
			reference: '445-EA'
			note: '445-EA'
			label: 'Original Product ID'

	og_rx_quantity:
		model:
			required: true
			type: 'decimal'
			
			max: 9999999.999
			rounding: 0.001
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 2
			reference: '446-EB'
			note: '446-EB'
			label: 'Original Quantity'
			_meta:
				copy_forward: true

	pa_id:
		model:
			source: 'patient_prior_auth'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': '5'
				insurance_id:
					'dynamic': '{insurance_id}'
		view:
			columns: 2
			label: 'Prior Authorization'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_prior_auth/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'pa_no_submitted': ['number'],
			]
			_meta:
				copy_forward: true

	pa_type_code:
		model:
			required: false
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '461-EU'
			if:
				'*':
					require_fields: ['pa_no_submitted']
		view:
			columns: -2
			reference: '461-EU'
			note: '461-EU'
			label: 'Prior Authorization ID Type'
			_meta:
				copy_forward: true

	pa_no_submitted:
		model:
			required: false
			max: 99999999999
			type: 'decimal'
			rounding: 1
			if:
				'*':
					require_fields: ['pa_type_code']
		view:
			columns: 2
			reference: '462-EV'
			note: '462-EV'
			label: 'Prior Authorization #'
			_meta:
				copy_forward: true

	inter_auth_type_id:
		model:
			max: 2
		view:
			columns: 2
			label: 'Inter Auth ID Qualifier'
			reference: '463-EW'
			note: '463-EW'
			offscreen: true
			readonly: true

	inter_auth_id:
		model:
			max: 20
		view:
			columns: 2
			label: 'Inter Auth ID'
			reference: '464-EX'
			note: '464-EX'
			offscreen: true
			readonly: true

	compound_code:
		model:
			default: '1'
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '406-D6'
			if:
				'2':
					fields: ['compound_type']
		view:
			columns: 2
			reference: '406-D6'
			note: '406-D6'
			label: 'Compound Code'
			_meta:
				copy_forward: true

	compound_type:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '996-G1'
		view:
			columns: 2
			reference: '996-G1'
			note: '996-G1'
			label: 'Compound Type'
			_meta:
				copy_forward: true

	rx_origin_code:
		model:
			default: '3'
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '419-DJ'
		view:
			columns: 2
			reference: '419-DJ'
			note: '419-DJ'
			label: 'Prescription Origin Code'
			_meta:
				copy_forward: true

	sub_clar_code:
		model:
			multi: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '420-DK'
		view:
			columns: 2
			reference: '420-DK'
			note: '420-DK'
			label: 'Sub Clarification Code'
			max_count: 3
			_meta:
				copy_forward: true

	require_other_coverage_code:
		model:
			source: ['Yes']
			if:
				'Yes':
					require_fields: ['other_coverage_code']
		view:
			control: 'checkbox'
			label: 'Req Other Coverage Code?'
			class: 'checkbox-only'
			offscreen: true
			readonly: true

	other_coverage_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '308-C8'
		view:
			columns: 2
			reference: '308-C8'
			note: '308-C8'
			label: 'Other Coverage Code'
			_meta:
				copy_forward: true

	sp_pk_indicator:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '429-DT'
		view:
			columns: 2
			reference: '429-DT'
			note: '429-DT'
			label: 'Special Pack Indicator'
			_meta:
				copy_forward: true

	level_of_service:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '418-DI'
		view:
			columns: 2
			reference: '418-DI'
			note: '418-DI'
			label: 'Level of Service'
			_meta:
				copy_forward: true

	dispensing_status:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '343-HD'
			if:
				'C':
					require_fields: ['qty_to_disp', 'ds_to_disp', 'delay_reason_code', 'associated_rx_svc_no', 'associated_rx_service_date']
				'P':
					require_fields: ['qty_to_disp', 'ds_to_disp', 'delay_reason_code']
		view:
			columns: 2
			reference: '343-HD'
			note: '343-HD'
			label: 'Dispensing Status'
			offscreen: true
			readonly: true
			_meta:
				copy_forward: true

	delay_reason_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '357-NV'
		view:
			columns: 2
			reference: '357-NV'
			note: '357-NV'
			label: 'Delay Reason Code'
			_meta:
				copy_forward: true
			offscreen: true
			readonly: true

	pt_assign_indicator:
		model:
			default: 'Y'
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '391-MT'
		view:
			columns: 2
			reference: '391-MT'
			note: '391-MT'
			label: 'Patient Assign Indicator'
			_meta:
				copy_forward: true

	pharmacy_service_type:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '147-U7'
		view:
			columns: 2
			reference: '147-U7'
			note: '147-U7'
			label: 'Pharmacy Service Type'
			_meta:
				copy_forward: true

	associated_rx_svc_no:
		model:
			type: 'decimal'
			max: 999999999999
			if:
				'*':
					require_fields: ['associated_rx_service_date']
		view:
			columns: 4
			reference: '456-EN'
			note: '456-EN'
			label: 'Partial Fill '
			_meta:
				copy_forward: true
			offscreen: true
			readonly: true

	associated_rx_service_date:
		model:
			type: 'date'
			if:
				'*':
					require_fields: ['associated_rx_svc_no']
		view:
			columns: 4
			reference: '457-EP'
			note: '457-EP'
			label: 'Part Fill Date'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]
			_meta:
				copy_forward: true
			offscreen: true
			readonly: true

	qty_to_disp:
		model:
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			reference: '344-HF'
			note: '344-HF'
			label: 'Qty Intend to Disp'
			offscreen: true
			readonly: true

	ds_to_disp:
		model:
			type: 'int'
			max: 999
		view:
			columns: 4
			reference: '345-HG'
			note: '345-HG - Max 999'
			label: 'DS Intend to Disp'
			offscreen: true
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: '{order_id} {transaction_code} {quantity_prescribed} ({unit_of_measure})'
	sections:

		'Dispensed Product/Service':
			hide_header: true
			indent: false
			tab: 'Dispensed'
			fields: ['is_test', 'transaction_code', 'rx_svc_no_ref_qualifier', 'rx_svc_no', 'sched_rx_no',
			'date_rx_written',  'fill_number', 'daw_code', 'patient_id', 'insurance_id', 'product_id', 'service_id',
			'prod_svc_id_qualifier', 'prod_svc_id', 'quantity_dispensed', 'quantity_prescribed',
			'unit_of_measure', 'day_supply', 'number_of_refills_authorized', 'admin_route', 'proc_mod_code', 
			'compound_code', 'compound_type',
			'rx_origin_code', 'sub_clar_code', 'require_other_coverage_code', 'other_coverage_code',
			'sp_pk_indicator', 'level_of_service', 'dispensing_status', 'delay_reason_code',
			'pt_assign_indicator', 'pharmacy_service_type']

		'Prior Auth':
			hide_header: true
			indent: false
			tab: 'Prior Auth'
			fields: ['pa_id', 'pa_type_code', 'pa_no_submitted']

		# 'Intermediary Auth':
		# 	hide_header: true
		# 	indent: false
		# 	tab: 'Intermediary Auth'
		# 	fields: ['inter_auth_type_id', 'inter_auth_id']

		'Originally Prescribed Product/Service':
			hide_header: true
			indent: false
			tab: 'Changed Product/Service'
			note: '<i>Used when therapeutic substitution has occurred or when a DUR alert has been resolved by changing medications or quantities</i>'
			fields: ['og_product_id', 'og_service_id', 'og_rx_id_qualifier',
			'og_rx_id', 'og_rx_quantity']

		# 'Partial Fill':
		# 	hide_header: true
		# 	indent: false
		# 	tab: 'Partial Fill'
		# 	fields: ['associated_rx_svc_no', 'associated_rx_service_date', 'qty_to_disp', 'ds_to_disp']

view:
	hide_cardmenu: true
	comment: 'Claim'
	grid:
		fields: ['product_id', 'service_id', 'day_supply', 'quantity_dispensed']
		sort: ['-created_on']
	label: 'Claim'
	open: 'read'