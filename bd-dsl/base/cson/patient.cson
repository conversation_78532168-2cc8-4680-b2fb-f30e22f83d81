fields:

	status_id:
		model:
			default: '1'
			max: 32
			min: 1
			source: 'list_patient_status'
			sourceid: 'code'
			required: true
			if:
				'4':
					fields: ['onhold_reason']
				'8':
					fields: ['death_date', 'death_cause', 'death_confirm']
				'7':
					fields: ['cancellation_reason']
		view:
			class: 'status'
			label: 'Patient Chart Status'
			columns: 4
			findfilter: ['1', '2', '3', '4']

	onhold_reason:
		model:
			required: true
		view:
			columns: 4
			label: 'On-hold Reason'

	cancellation_reason:
		model:
			source: ['Patient Cannot afford Copay']
		view:
			columns: 4
			label: 'Cancellation Reason'

	death_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 4
			label: 'Date of Death'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	death_confirm:
		model:
			max: 3
			required: false
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Has the cause of death been confirmed by Post-mortem?'

	death_cause:
		model:
			required: true
		view:
			columns: 4
			label: 'Cause of Death'

	status_code:
		view:
			label: 'Code Status'
			readonly: true
			offscreen: true
			findunique: true

	patient_tag_id:
		model:
			multi: true
			source: 'list_tag'
		view:
			label: 'Patient Tags'
			columns: 4

	firstname:
		model:
			search: 'A'
			max: 35
			min: 1
			required: true
		view:
			label: 'First Name'
			note: 'Letters, numbers, comma, \', -, . only'
			validate: [{
					name: 'UpdateTabLabel'
					fields: ['firstname', 'middlename', 'lastname']
			},
			{
				name: 'NameValidator'
			}
			]
			columns: 4

	lastname:
		model:
			search: 'B'
			max: 60
			min: 1
			required: true
		view:
			label: 'Last Name'
			note: 'Letters, numbers, \', -, . only'
			validate: [{
					name: 'UpdateTabLabel'
					fields: ['firstname', 'middlename', 'lastname']
			},
			{
				name: 'NameValidator'
			}
			]
			columns: 4

	middlename:
		model:
			search: 'C'
			max: 25
			min: 0
		view:
			label: 'Middle Name'
			note: 'Letters, numbers, \', -, . only'
			validate: [{
					name: 'UpdateTabLabel'
					fields: ['firstname', 'middlename', 'lastname']
			},
			{
				name: 'NameValidator'
			}
			]
			columns: 4

	site_id:
		model:
			required: true
			source: 'site'
			sourceid: 'id'
		view:
			label: 'Site'
			columns: 4

	team_id:
		model:
			source: 'list_team'
		view:
			label: 'Team'
			columns: 4

	referral_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Referral Date'
			template: '{{now}}'
			columns: 4

	referral_source_id:
		model:
			source: 'sales_account'
		view:
			label: 'Referral Source'
			columns: 4

	referrer_id:
		model:
			source: 'physician'
			type: 'int'
		view:
			label: 'Referring Physician'
			validate: [
					name: 'CareplanPhysicianOnboardingValidator'
			]
			columns: 4
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/physician/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'territory_id': ['territory_id'],
					'referral_phone': ['primary_phone'],
					'referral_fax': ['primary_fax']
			]

	primary_prescriber_id:
		model:
			type: 'int'
		view:
			offscreen: true
			readonly: true

	referral_phone:
		model:
			max: 21
		view:
			columns: 4
			format: 'us_phone'
			label: 'Referring Physician Phone #'

	referral_fax:
		model:
			max: 21
		view:
			columns: 4
			format: 'us_phone'
			label: 'Referring Physician Fax #'

	territory_id:
		model:
			source: 'sales_territory'
		view:
			note: 'Set at the physician level'
			label: 'Sales Territory'
			columns: 4
			readonly: true

	facility_id:
		model:
			source: 'facility'
			type: 'int'
		view:
			label: 'Facility'
			columns: 4

	mrn:
		model:
			required: false
		view:
			label: 'MRN'
			note: 'auto-generated on save'
			readonly: true
			offscreen: true

	ssn:
		model:
			max: 11
			min: 9
		view:
			format: 'ssn'
			label: 'SSN'
			note: '9-digit number only'
			validate: [
					name: 'SSNValidator'
			]
			offscreen: true
			readonly: true

	dob:
		model:
			required: true
			type: 'date'
		view:
			columns: 4
			control: 'input'
			label: 'Date of Birth'
			note: 'Must be in MM/DD/YYYY format'
			validate: [{
					name: 'DOBValidator'
			},
			{
				name: 'PediatricCheck'
				age: 18
			}
			]

	pediatric:
		model:
			default: 'No'
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Pediatric patient?"
			columns: 4

	gender:
		model:
			max: 8
			min: 1
			required: true
			source: ['Female', 'Male']
		view:
			control: 'radio'
			label: 'Sex at birth'
			columns: 4

	language:
		model:
			default: 'English'
			source: ['English', 'Spanish', 'Chinese', 'French', 'German', 'Italian', 'Japanese', 'Korean']
		view:
			label: 'Language'
			columns: 4

	marital_status:
		model:
			source:
				'A': 'Annulled'
				'D': 'Divorced'
				'I': 'Interlocutory'
				'L': 'Legally Separated'
				'M': 'Married'
				'P': 'Polygamous'
				'S': 'Never Married'
				'T': 'Domestic Partner'
				'U': 'Unmarried'
				'W': 'Widowed'
				'UNK': 'Unknown'
		view:
			control: 'radio'
			label: 'Marital Status'
			columns: 4

	code_status:
		model:
			default: 'N/A'
			source: ['Full', 'DNR', 'N/A', 'Other']
			if:
				'Other':
					fields: ['code_status_other']
		view:
			control: 'radio'
			label: 'Code Status'
			columns: 4

	code_status_other:
		model:
			required: true
		view:
			label: 'Code Status Other'
			columns: 2

	health_declaration:
		model:
			multi: true
			source: ['Living Will', 'Advanced Directives', 'Power of Attorney', 'Four', 'Five', 'None']
			if:
				'Power of Attorney':
					fields: ['power_of_attorney_details']
				'Advanced Directives':
					fields: ['advanced_directives_details']
		view:
			control: 'checkbox'
			max_count: 4
			class: 'checkbox checkbox-2'
			note: 'Select all that apply'
			label: 'Health Declaration(s)'
			columns: 2

	advanced_directives_details:
		model:
			required: true
		view:
			label: 'Advanced Directives Details'
			control: 'area'
			columns: 2

	power_of_attorney_details:
		model:
			required: true
		view:
			label: 'Power of Attorney'
			control: 'area'
			columns: 2

	external_id:
		model:
			required: false
		view:
			label: 'CPR+ MRN'
			columns: 4
			readonly: true

	external_ids:
		model:
				type: 'json'
				subfields:
					type_id:
						label: 'System'
						source: 'list_system'
						type: 'text'
					external_id:
						label: 'ID'
						type: 'text'
					comment:
						label: 'Comment'
						type: 'text'
			view:
				control: 'grid'
				note: 'These identifiers are the link back to other EMRs/systems'
				label: 'External IDs'

	careteam_pharmacist_id:
		model:
			source: 'user'
			sourcefilter:
				role:
					'static': 'pharm'
				group_role:
					'static': '!tech'
		view:
			label: 'Assigned Pharmacist'
			offscreen: true
			readonly: true

	careteam_nurse_id:
		model:
			source: 'user'
			sourcefilter:
				role:
					'static': ['nurse', 'admin', 'pharm']
			type: 'int'
		view:
			label: 'Primary RN'
			note: 'Care Team - Nurse'
			columns: 4
			offscreen: true
			readonly: true

	careteam_pt_advocate_id:
		model:
			source: 'user'
			type: 'int'
		view:
			label: 'Patient Experience'
			offscreen: true
			readonly: true

	measurement_log:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{id}'
		view:
			embed:
				form: 'patient_measurement_log'
				selectable: false
				add_preset:
					patient_id: '{id}'
			grid:
				add: 'inline'
				rank: 'none'
				edit: true
				fields: ['created_by', 'date', 'height', 'weight']
				width: [40, 20, 20, 20]
			#label: 'Measurement Log'

	phone_cell:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Cell Phone'
			columns: 4

	phone_home:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Home Phone'
			columns: 4

	phone_work:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Work Phone'
			columns: 4

	phone_primary:
		model:
			max: 16
			source: ['Cell Phone', 'Home Phone', 'Work Phone']
			if:
				'Cell Phone':
					require_fields: ['phone_cell']
				'Home Phone':
					require_fields: ['phone_home']
				'Work Phone':
					require_fields: ['phone_work']
		view:
			control: 'radio'
			label: 'Primary Phone'
			columns: 4

	email:
		model:
			max: 64
			min: 6
			required: false
		view:
			label: 'Email Address'
			note: 'Must be a valid email address for Portal Access'
			validate: [
					name: 'EmailValidator'
			]
			columns: 4

	user_id:
		model:
			source: 'user'
		view:
			note: 'Link to user record associated with portal access'
			label: 'User Record'
			offscreen: true
			readonly: true

	is_test:
		model:
			max: 3
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			note: 'Marking Yes will exclude this patient from any outcomes reporting'
			label: 'Test patient?'
			columns: 4

	is_prn:
		model:
			max: 3
			source: ['Yes']
			if:
				'Yes':
					fields: ['is_prn_set_date']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			note: 'Marking Yes will exclude this patient from any adherence outcome reporting (i.e. PDC)'
			label: 'Excluded from adherence tracking?'
			columns: 4
			validate: [
				{
					name: 'PrefillCurrentDate'
					condition:
						is_prn: 'Yes'
					dest: 'is_prn_set_date'
				}
			]

	is_prn_set_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Exclusion Date'
			columns: 4

	auto_dc_pt:
		model:
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Auto DC?'
			note: 'When no more pending / active orders, patient will be discontinued'
			columns: 4

	clinical_alert:
		view:
			label: 'Clinical Alert Message'
			control: 'area'
			columns: 2

	billing_alert:
		model:
			access:
				read: ['cm', 'cma', 'admin']
		view:
			label: 'Billing Alert Message'
			control: 'area'
			columns: 2

	embed_contacts:
		model:
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{id}'
		view:
			embed:
				form: 'patient_contact'
				selectable: false
				add_preset:
					patient_id: '{id}'
			grid:
				edit: true
				add: 'inline'
				hide_cardmenu: true
				rank: 'none'
				fields: ['name', 'relation_id', 'type', 'cell', 'home']
				label: ['Name', 'Relationship', 'Type', 'Cell #', 'Home #']
				width: [30, 20, 20, 15, 15]
			#label: 'Emergency Contacts'

	embed_address:
		model:
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{id}'
		view:
			embed:
				form: 'patient_address'
				selectable: false
				add_preset:
					patient_id: '{id}'
			grid:
				edit: false
				add: 'inline'
				hide_cardmenu: true
				rank: 'none'
				fields: ['address_type', 'street', 'city', 'state_id', 'zip']
				label: ['Type', 'Address', 'City', 'State', 'Zip']
				width: [15, 35, 20, 15, 15]
			#label: 'Addresses'

	embed_document:
		model:
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{id}'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					patient_id: '{id}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			#label: 'Assigned Documents'

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'pharm']
		request:    ['patient']
		update:     ['nurse', 'patient']
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	indexes:
		fulltext: ['firstname', 'lastname', 'middlename', 'ssn']
		many: [
			['is_test']
			['status_id']
			['territory_id']
			['site_id']
			['ssn', 'firstname', 'lastname']
			['dob', 'firstname', 'lastname']
			['mrn', 'firstname', 'lastname']
		]
		unique: [
			['mrn']
			['firstname', 'lastname', 'middlename', 'dob']
		]

	#CM:2013-05-05 - DO NOT CHANGE the following from array to Python '{}' format
	name: ['mrn', 'firstname', 'middlename', 'lastname']

	sections_group: [
		'Chart State':
			#hide_header: true
			indent: false
			tab: 'Demographics'
			fields: ['firstname', 'lastname', 'middlename', 'status_id', 'onhold_reason',
			'cancellation_reason', 'death_date', 'death_cause', 'death_confirm', 'mrn'
			]
		'Contact Info':
			indent: false
			hide_header: true
			tab: 'Demographics'
			fields: ['phone_primary', 'phone_cell', 'phone_home', 'phone_work', 'email', 'language', 'dob', 'gender', 'marital_status' ]
		'Service':
			indent: false
			tab: 'Demographics'
			fields: ['site_id', 'team_id',  'referral_date',
			'referral_source_id', 'referrer_id', 'referral_phone', 'referral_fax', 'facility_id', 'territory_id']
		'Identity':
			hide_header: true
			indent: false
			tab: 'Demographics'
			fields: ['ssn',
			'code_status', 'code_status_other', 'health_declaration',
			'power_of_attorney_details']
		'Address':
			indent: false
			#hide_header: true
			tab: 'Demographics'
			fields: ['embed_address']
		'Emergency Contacts':
			indent: false
			#hide_header: true
			tab: 'Demographics'
			fields: ['embed_contacts']
		'Measurement Log':
			indent: false
			tab: 'Measurements'
			fields: ['measurement_log']
		'Documents':
			#hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'
		'Settings':
			indent: false
			#hide_header: true
			tab: 'Settings'
			fields: ['external_id', 'is_test', 'is_prn', 'is_prn_set_date', 'auto_dc_pt', 'patient_tag_id']
		'Alerts':
			indent: false
			hide_header: true
			tab: 'Settings'
			fields: ['clinical_alert', 'billing_alert']
		'Billing Alert':
			indent: false
			hide_header: true
			modal: true
			fields: ['billing_alert']
		'Clinical Alert':
			indent: false
			hide_header: true
			modal: true
			fields: ['clinical_alert']
		'External IDs':
			indent: false
			hide_header: true
			tab: 'Settings'
			fields: ['external_ids']
	]
	transform_post: [
		{
			name: "PermissionAccess"
			arguments:
				user: "user_id"
				patient: "id"
				log: false
		},
		{
			name: "CreateBillingAccountOnInsert"
		}
	]

view:
	dimensions:
        width: '80%'
        height: '80%'
	hide_cardmenu: true
	hide_header: true
	comment: 'Patient'
	find:
		basic: ['status_code', 'lastname', 'firstname', 'referral_source_id', 'referrer_id', 'site_id', 'team_id', 'status_id']
		advanced: ['ssn', 'dob', 'external_id' , 'mrn']
	grid:
		fields: ['lastname', 'firstname', 'dob', 'phone_cell', 'team_id', 'status_id', 'mrn']
		sort: ['lastname', 'firstname']
	icon: "person"
	label: 'Patient'
	open: 'read'
