fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	hcpc:
		model:
			source: 'list_hcpc'
			sourceid: 'code'
		view:
			label: 'HCPC'

	state:
		model:
			source: 'list_us_state'
			sourceid: 'code'
		view:
			label: 'State'

	rural_fee:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Rural Fee'

	non_rural_fee:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Non Rural Fee'

	effective_date:
		model:
			type: 'date'
		view:
			label: 'Effective Date'

	ceiling:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Ceiling'
	floor:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Floor'

	description:
		view:
			label: 'Description'

	# mod_1:
	# 	model:
	# 		type: 'decimal'
	# 	view:
	# 		label: 'MOD 1'

	# mod_2:
	# 	model:
	# 		type: 'decimal'
	# 	view:
	# 		label: 'Non Rural Fee'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	indexes:
		unique: [
			['code']
		]
	name: '{hcpc} - {description}' # TODO: hcpc - description
	sections:
		'Main':
			fields: ['code', 'hcpc', 'state',
			'rural_fee','non_rural_fee','effective_date',
			'ceiling','floor','description']

view:
	comment: 'Manage > Billing Fee Schedule Medicare'
	find:
		basic: ['code', 'hcpc']
	grid:
		fields: ['code', 'hcpc','state','rural_fee','non_rural_fee','effective_date', 'description']
		sort: ['-code']
	label: 'Billing Fee Schedule Medicare'
