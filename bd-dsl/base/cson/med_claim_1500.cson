fields:

	invoice_no:
		view:
			label: 'Invoice #'
			readonly: true
			offscreen: true

	parent_claim_no:
		view:
			label: 'Parent Claim #'
			note: 'For COB Scenario'
			readonly: true
			offscreen: true
			validate: [
				name: 'Check1500ServiceLinesLock'
			]

	claim_no:
		view:
			label: 'Claim #'
			readonly: true
			offscreen: true

	patient_id:
		model:
			type: 'int'
			required: true
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'payer_id': ['payer_id']
					'subscriber_first_name': ['beneficiary_fname']
					'subscriber_middle_name': ['beneficiary_mname']
					'subscriber_last_name': ['beneficiary_lname']
					'subscriber_dob': ['beneficiary_dob']
					'subscriber_gender': ['beneficiary_gender']
					'subscriber_relationship_id': ['medical_relationship_id']
					'subscriber_member_id': ['cardholder_id']
					'subscriber_insurance_group_or_policy_number': ['group_number', 'policy_number'],
					'subscriber_address1': ['home_street'],
					'subscriber_address2': ['home_street2'],
					'subscriber_city': ['home_city'],
					'subscriber_state_id': ['home_state_id'],
					'subscriber_postal_code': ['home_zip']
			]

	status:
		model:
			required: true
			default: "Pending"
			source: ["Pending", 'Sent', 'Reversed', 'Denied', 'Payable']
			if:
				'Payable':
					require_fields: ['paid']
		view:
			class: 'status'
			columns: 2
			label: 'Status'

	substatus_id:
		model:
			source: 'list_billing_csstatus'
			sourceid: 'code'
			track: true
			sourcefilter:
				status_id:
					'dynamic': '{status_id}'
		view:
			columns: 2
			control: 'select'
			label: 'Claim Substatus'

	insurance_id:
		model:
			prefill: ['parent.insurance_id']
			type: 'int'
			source: 'patient_insurance'
		view:
			columns: 3
			label: 'Insurance'
			offscreen: true
			readonly: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_insurance/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'payer_id': ['payer_id']
					'subscriber_first_name': ['beneficiary_fname']
					'subscriber_middle_name': ['beneficiary_mname']
					'subscriber_last_name': ['beneficiary_lname']
					'subscriber_dob': ['beneficiary_dob']
					'subscriber_gender': ['beneficiary_gender']
					'subscriber_relationship_id': ['medical_relationship_id']
					'subscriber_member_id': ['cardholder_id']
					'subscriber_insurance_group_or_policy_number': ['group_number', 'policy_number']
			]

	payer_id:
		model:
			type: 'int'
			source: 'payer'
			prefill: ['parent.payer_id']
		view:
			label: 'Payer'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/payer/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'subscriber_payer_organization_name': ['organization']
					'insurance_type': ['cms_1']
			]

	insurance_type:
		model:
			required: true
			source: ['Medicare', 'Medicaid', 'ChampVA', 'Group Health Plan', 'FECA Black Lung', 'Other']
		view:
			columns: 3
			label: 'Insurance Type'
			note: '1'

	claim_resubmission_code:
		model:
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CLM05-03'
				code:
					'static': ['7', '8']
			if:
				'*':
					require_fields: ['control_number']
		view:
			columns: -3
			label: 'Claim Resubmission Code'
			note: '22'

	control_number:
		model:
			min: 1
			max: 50
		view:
			columns: 3
			label: 'Payer Claim Control Number (PCCN)'

	claim_codes:
		model:
			max: 50
		view:
			columns: 3
			label: 'Claim Codes'
			note: '10d'

	related_cause_code:
		model:
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CLM11'
			if:
				'AA':
					fields: ['auto_accident_state_code']
		view:
			label: 'Related Cause Code?'
			note: '10a-c'
			validate: [
				name: 'ClearStateCode'
			]
			offscreen: true
			readonly: true

	auto_accident_state_code:
		model:
			required: false
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			note: '10b'
			columns: 'addr_state'
			label: 'Auto Accident Occured In State'
			offscreen: true
			readonly: true

	additional_information:
		model:
			min: 1
			max: 80
		view:
			label: 'Additional Information'
			control: 'area'
			note: '24'

	patient_first_name:
		model:
			required: true
			min: 1
			max: 35
		view:
			columns: -3
			label: 'First Name'
			note: '2'

	patient_last_name:
		model:
			required: true
			min: 1
			max: 60
		view:
			columns: 3
			label: 'Last Name'

	patient_middle_name:
		model:
			min: 1
			max: 10
		view:
			columns: 3
			label: 'Middle Name'

	patient_dob:
		model:
			type: 'date'
		view:
			columns: -3
			label: 'Patient DOB'
			note: '3'

	patient_gender:
		model:
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'DMG03'
		view:
			columns: 3
			label: 'Gender'
			note: '3'

	patient_phone_number:
		model:
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Phone #'

	patient_address1:
		model:
			min: 1
			max: 35
		view:
			columns: 'addr_1'
			note: '5'
			label: 'Address'

	patient_address2:
		model:
			min: 1
			max: 35
		view:
			columns: 'addr_2'
			label: 'Address 2'

	patient_city:
		model:
			min: 1
			max: 60
		view:
			columns: 'addr_city'
			label: 'City'

	patient_state_id:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'State'

	patient_postal_code:
		model:
			max: 15
			min: 3
		view:
			columns: 'addr_zip'
			format: 'us_zip'
			label: 'Zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip: 'patient_postal_code'
						city: 'patient_city'
						state: 'patient_state_id'
			]

	patient_account_number:
		model:
			min: 1
			max: 50
		view:
			columns: 2
			label: 'Patient Account Number'
			note: '26'
			readonly: true

	patient_signature_date:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Patient Signature Date'
			note: '12'
			readonly: true

	#Subscriber
	subscriber_payer_organization_name:
		model:
			min: 1
			max: 60
			required: true
		view:
			columns: 'addr_city'
			label: 'Organization Name'
			note: '11c'

	subscriber_member_id:
		model:
			required: true
			min: 1
			max: 25
		view:
			columns: 3
			label: 'Member ID'
			note: '1a'

	subscriber_insurance_group_or_policy_number:
		model:
			min: 1
			max: 50
		view:
			columns: 3
			label: 'Group/Policy #'
			note: '11'

	pa_id:
		model:
			type: 'int'
			source: 'patient_prior_auth'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': '5'
				insurance_id:
					'dynamic': '{insurance_id}'
		view:
			columns: -3
			label: 'Prior Auth'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_prior_auth/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'prior_authorization_number': ['number']
			]

	prior_authorization_number:
		view:
			columns: 3
			label: 'Prior Auth #'
			note: '22'

	subscriber_payment_responsibility_level_code:
		model:
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR01'
		view:
			columns: 3
			label: 'Responsibility Level Code'
			note: '11d'

	subscriber_relationship_id:
		model:
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'PAT01'
		view:
			columns: 3
			label: 'Relationship to Subscriber'
			note: '6'

	subscriber_first_name:
		model:
			min: 1
			max: 35
		view:
			columns: -3
			label: 'First Name'
			note: '9'

	subscriber_last_name:
		model:
			required: true
			min: 1
			max: 35
		view:
			columns: 3
			label: 'Last Name'

	subscriber_middle_name:
		model:
			min: 1
			max: 35
		view:
			columns: 3
			label: 'Middle Name'

	subscriber_dob:
		model:
			type: 'date'
		view:
			columns: -3
			label: 'DOB'
			note: '11a'

	subscriber_gender:
		model:
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'DMG03'
		view:
			columns: 3
			label: 'Gender'
			note: '11a'

	subscriber_phone_number:
		model:
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Phone #'

	subscriber_address1:
		model:
			min: 1
			max: 35
		view:
			columns: 'addr_1'
			note: '5'
			label: 'Address'

	subscriber_address2:
		model:
			min: 1
			max: 35
		view:
			columns: 'addr_2'
			label: 'Address 2'

	subscriber_city:
		model:
			min: 1
			max: 60
		view:
			columns: 'addr_city'
			label: 'City'

	subscriber_state_id:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'State'

	subscriber_postal_code:
		model:
			max: 15
			min: 3
		view:
			columns: 'addr_zip'
			format: 'us_zip'
			label: 'Zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip: 'subscriber_postal_code'
						city: 'subscriber_city'
						state: 'subscriber_state_id'
			]

	referring_provider_id:
		model:
			required: true
			type: 'int'
			source: 'patient_prescriber'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			columns: -3
			note: '17'
			label: 'Referring Provider'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_prescriber/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'referring_provider_physician_id': ['physician_id'],
			]

	referring_provider_physician_id:
		model:
			required: true
			type: 'int'
			source: 'physician'
		view:
			label: 'Physician'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/physician/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'referring_provider_last_name': ['last'],
					'referring_provider_first_name': ['first'],
					'referring_provider_npi': ['npi'],
					'referring_provider_state_license_number': ['state_license'],
					'referring_provider_taxonomy_id': ['taxonomy_id']
			]

	referring_provider_first_name:
		model:
			min: 1
			max: 35
		view:
			columns: -3
			label: 'First Name'

	referring_provider_last_name:
		model:
			required: true
			min: 1
			max: 60
		view:
			columns: 3
			label: 'Last Name'

	referring_provider_middle_name:
		model:
			min: 1
			max: 35
		view:
			columns: 3
			label: 'Middle Name'

	referring_provider_npi:
		model:
			max: 10
		view:
			columns: 3
			label: 'NPI'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid NPI, must be 10 digits'
			}]

	referring_provider_state_license_number:
		model:
			min: 1
			max: 50
		view:
			columns: 3
			label: 'State License Number'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{5,20}$'
				error: 'Invalid State License Number'
			}]

	referring_provider_taxonomy_id:
		model:
			source: 'list_nucc_taxonomy'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Physician Specialty'
			note: 'Specialty for the physician (NUCC Taxonomy Code)'

	#Billing Provider
	site_id:
		model:
			type: 'int'
			required: true
			source: 'site'
		view:
			columns: 3
			label: 'Site'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/site/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'bill_organization_name': ['bill_name', 'name'],
					'npi': ['npi'],
					'bill_tax_id': ['tax_id'],
					'bill_address1': ['bill_address1'],
					'bill_address2': ['bill_address2'],
					'bill_city': ['bill_city'],
					'bill_state_id': ['bill_state_id'],
					'bill_zip': ['bill_zip'],
					'bill_phone': ['bill_phone']
			]

	bill_organization_name:
		model:
			required: true
			max: 60
		view:
			columns: 3
			label: 'Organization Name'

	npi:
		model:
			required: true
			max: 10
		view:
			columns: 3
			label: 'NPI'
			findunique: true
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid NPI, must be 10 digits'
			}]

	bill_address1:
		model:
			required: true
			max: 35
		view:
			columns: 'addr_1'
			label: 'Address 1'

	bill_address2:
		model:
			max: 35
		view:
			columns: 'addr_2'
			label: 'Address 2'

	bill_city:
		model:
			required: true
			type: 'text'
		view:
			columns: 'addr_city'
			label: 'City'

	bill_state_id:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
			required: true
		view:
			columns: 'addr_state'
			label: 'State'

	bill_zip:
		model:
			max: 10
			min: 5
			required: true
		view:
			columns: 'addr_zip'
			format: 'us_zip'
			label: 'Zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip: 'bill_zip'
						city: 'bill_city'
						state: 'bill_state_id'
			]

	bill_phone:
		model:
			required: true
			max: 21
		view:
			columns: 2
			format: 'us_phone'
			label: 'Phone #'

	bill_tax_id:
		model:
			min: 1
			max: 50
		view:
			columns: 4
			label: 'Tax ID'
			note: '25'

	bill_alt_provider_id:
		model:
			min: 1
			max: 50
		view:
			columns: 4
			label: 'Alternative Provider ID'
			note: '33b'

	referring_provider_qualifier:
		model:
			min: 1
			max: 50
		view:
			columns: 2
			label: 'Referring Provider ID Qualifier'
			note: '17a'
			readonly: true

	referring_provider_alt_id:
		model:
			min: 1
			max: 50
		view:
			columns: 2
			label: 'Referring Provider ID'
			note: '17a'
			readonly: true

	subform_dx:
		model:
			multi: true
			source: 'med_claim_1500_dx'
			type: 'subform'
			required: true
		view:
			note: 'Max 12'
			label: 'Diagnosis'
			grid:
				add: 'inline'
				edit: true
				fields: ['dx_id', 'diagnosis_code']
				label: ['Diagnosis', 'Code']
				width: [75, 25]
			max_count: 12

	subform_sl:
		model:
			multi: true
			source: 'med_claim_1500_sl'
			type: 'subform'
			required: true
		view:
			note: 'Max 50'
			label: 'Service Line'
			grid:
				add: 'inline'
				edit: true
				fields: ['procedure_code', 'service_unit_count', 'measurement_unit', 'line_item_charge_amount', 'dx_id_1']
				label: ['Code', 'Quantity', 'Unit', 'Chrg', 'Dx']
				width: [20, 15, 15, 15, 30]
			max_count: 50

	#service facility
	subform_sf:
		model:
			multi: false
			source: 'med_claim_1500_sf'
			type: 'subform'
			required: false
		view:
			label: 'Service Facility'
			offscreen: true
			readonly: true

	#Dates
	symptom_date:
		model:
			type: 'date'
		view:
			offscreen: true
			readonly: true
			label: 'Symptom Date'
			note: '14'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]
	
	other_date_type:
		model:
			source: ['Accident Date', 'Last Seen Date', 'Acute Manifestation Date', 'Initial Treatment Date', 'Prescription Date']
			if:
				'*':
					require_fields: ['other_date']
		view:
			offscreen: true
			readonly: true
			label: 'Other Date Type'
			note: '15'

	other_date:
		model:
			type: 'date'
		view:
			offscreen: true
			readonly: true
			label: 'Other Date'
			note: '15'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	last_worked_date:
		model:
			type: 'date'
		view:
			offscreen: true
			readonly: true
			label: 'Last Worked Date'
			note: '16'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	authorized_return_to_work_date:
		model:
			type: 'date'
		view:
			offscreen: true
			readonly: true
			label: 'Authorized Return to Work Date'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	admission_date:
		model:
			type: 'date'
		view:
			offscreen: true
			readonly: true
			note: '18'
			label: 'Admission Date'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	discharge_date:
		model:
			type: 'date'
		view:
			offscreen: true
			readonly: true
			label: 'Discharge Date'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	billed:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Billed'
			readonly: true

	expected:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Expected'
			readonly: true

	paid:
		model:
			rounding: 0.01
			type: 'decimal'
			min: 0.00
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Paid'
			note: 'Set the paid amount in the service lines'
			readonly: true

	copay:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			class: 'numeral money'
			label: 'Copay'
			format: '$0,0.00'

	#COB
	cob_insurance_id:
		model:
			required: true
			type: 'int'
			source: 'patient_insurance'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
				type_id:
					'static': '!SELF'
				billing_method_id:
					'static': ['mm', 'cms1500']
		view:
			columns: 3
			label: 'Insurance'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_insurance/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'cob_payer_id': ['payer_id']
					'cob_first_name': ['beneficiary_fname']
					'cob_middle_name': ['beneficiary_mname']
					'cob_last_name': ['beneficiary_lname']
					'cob_insurance_group_or_policy_number': ['group_number', 'policy_number']
			]

	cob_payer_id:
		model:
			type: 'int'
			source: 'payer'
		view:
			label: 'Payer'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/payer/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'cob_organization_name': ['organization']
			]

	cob_organization_name:
		model:
			required: true
			max: 60
			min: 1
		view:
			columns: 3
			note: '9d'
			label: 'Organization Name'

	cob_insurance_group_or_policy_number:
		model:
			min: 1
			max: 50
		view:
			columns: 3
			label: 'Group/Policy #'
			note: '9a'

	other_payer_claim_control_number:
		model:
			min: 1
			max: 50
		view:
			columns: 3
			label: 'Other Claim Control #'
			note: '11b'

	cob_first_name:
		model:
			max: 35
			min: 1
		view:
			columns: -3
			label: 'First Name'
			note: 'Letters, numbers, \', -, . only'

	cob_last_name:
		model:
			required: true
			max: 60
			min: 1
		view:
			columns: 3
			label: 'Middle Name'
			note: 'Letters, numbers, \', -, . only'

	cob_middle_name:
		model:
			max: 25
			min: 1
		view:
			columns: 3
			label: 'Middle Name'
			note: 'Letters, numbers, \', -, . only'

	void:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['voided_datetime', 'void_reason_id']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Void Claim?'
			findfilter: '!Yes'
			class: 'checkbox-only'
			readonly: true
			offscreen: true

	void_reason_id:
		model:
			required: true
			source: 'list_void_reason_billing'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			label: 'Void Reason'
			readonly: true
			offscreen: true

	voided_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Voided Date'
			template: '{{now}}'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['site_id']
			['patient_id']
			['insurance_id']
			['payer_id']
		]

	name: '{status} {substatus_id_auto_name} PT:{patient_id_auto_name} PY:{payer_id_auto_name} EX:${expected}'

	sections_group: [
		'Header':
			hide_header: true
			tab: 'Header'
			fields: ['invoice_no',  'patient_id', 'status', 'substatus_id',
			'insurance_id', 'payer_id', 'insurance_type','claim_resubmission_code',
			'control_number', 'claim_codes', 'related_cause_code', 'auto_accident_state_code',
			'additional_information', 'billed', 'expected', 'paid', 'copay']
		'Patient':
			hide_header: true
			indent: false
			tab: 'Patient'
			fields: ['patient_first_name', 'patient_last_name', 'patient_middle_name',
			'patient_dob', 'patient_gender', 'patient_phone_number', 'patient_address1', 'patient_address2',
			'patient_city', 'patient_state_id', 'patient_postal_code', 'patient_account_number', 'patient_signature_date']
		'Subscriber':
			hide_header: true
			indent: false
			tab: 'Subscriber'
			fields: ['subscriber_payer_organization_name', 'subscriber_member_id',
			'subscriber_insurance_group_or_policy_number', 'pa_id', 'prior_authorization_number',
			'subscriber_payment_responsibility_level_code', 'subscriber_relationship_id', 'subscriber_first_name',
			'subscriber_last_name', 'subscriber_middle_name', 'subscriber_dob', 'subscriber_gender',
			'subscriber_phone_number', 'subscriber_address1', 'subscriber_address2', 'subscriber_city',
			'subscriber_state_id', 'subscriber_postal_code']
		'Referring Provider':
			hide_header: true
			indent: false
			tab: 'Referring'
			fields: ['referring_provider_id', 'referring_provider_physician_id',
			'referring_provider_first_name', 'referring_provider_last_name', 'referring_provider_middle_name',
			'referring_provider_npi', 'referring_provider_state_license_number', 'referring_provider_taxonomy_id',
			'referring_provider_qualifier', 'referring_provider_alt_id']
		'Billing Provider':
			hide_header: true
			indent: false
			tab: 'Billing'
			fields: ['site_id', 'bill_organization_name', 'npi', 'bill_address1',
			'bill_address2', 'bill_city', 'bill_state_id', 'bill_zip', 'bill_phone', 'bill_tax_id', 'bill_alt_provider_id']
		'Diagnoses':
			hide_header: true
			indent: false
			tab: 'Diagnoses'
			fields: ['subform_dx']
		'Service Line':
			hide_header: true
			indent: false
			tab: 'Service Line'
			fields: ['subform_sl']
		'Service Facility':
			hide_header: true
			indent: false
			tab: 'Facility'
			tab_toggle: true
			fields: ['subform_sf']
		'COB':
			hide_header: true
			indent: false
			tab_toggle: true
			tab: 'COB'
			fields: ['cob_insurance_id', 'cob_payer_id', 'cob_organization_name',
			'cob_insurance_group_or_policy_number', 'other_payer_claim_control_number',
			'cob_first_name', 'cob_last_name', 'cob_middle_name']
		'Void':
			modal: true
			fields: ['void', 'voided_datetime', 'void_reason_id']
	]

view:
	dimensions:
		width: '85%'
		height: '95%'
	hide_cardmenu: true
	validate: [
		{
			name: "CheckDiagnoses1500"
		}
	]
	block:
		validate: [
			name: 'CMS1500Block'
		]
	comment: 'Medical Claim (1500)'
	find:
		basic: ['site_id', 'patient_id', 'insurance_id', 'payer_id', 'status']
	grid:
		fields: ['control_number', 'status', 'patient_id', 'payer_id']
		width: [20, 20, 30, 30]
		sort: ['-created_on']
	label: 'Medical Claim (1500)'
	open: 'read'