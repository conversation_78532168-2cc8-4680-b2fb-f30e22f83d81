fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	shortness_breath:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['shortness_breath_comment']
		view:
			control: 'radio'
			label: 'Are you having any shortness of breath or chest pain?'
			columns:3

	shortness_breath_comment:
		view:
			label: 'Shortness of Breath/Chest Pain Comment'
			columns:3


	used_diuretic:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['used_diuretic_details']
		view:
			control: 'radio'
			label: 'Have you needed to use your diuretic rescue medications this week?'
			columns:3

	used_diuretic_details:
		view:
			control: 'area'
			label: 'Diuretic Medication Details'
			columns:3

	heart_rate:
		model:
			required: true
			type: 'int'
		view:
			note: 'bpm'
			label: 'What is your heart rate today?'
			columns:3

	had_palpitations:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you noticed any palpitations?'
			columns:3

	day_supply:
		model:
			required: true
			type: 'int'
		view:
			note: '#'
			label: 'Day Supply on Hand:'
			columns:3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		ongoing_inotrope:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Ongoing Inotropic':
			note: 'Ask the patient the following questions'
			fields: ['used_diuretic', 'used_diuretic_details', 'heart_rate', 'had_palpitations', 'day_supply']

view:
	comment: 'Patient > Careplan > Ongoing > Inotropic'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Ongoing Assessment: Inotropic'
	open: 'read'
