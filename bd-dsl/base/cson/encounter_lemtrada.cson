
fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	rems_enrolled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'No':
					note: 'Close assessment and notify physician'
				'Yes':
					sections: ['Lemtrada Risks',  'Lemtrada Checklist']
		view:
			control: 'radio'
			label: 'Patient enrolled in REMS verified by calling ************?'
			columns: 2

	ordered_prophylaxis:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Has herpetic prophylaxis been ordered and patient counseled on importance of adherence to viral prophylaxis?'
			columns: 2

	checklist_rems_form_received:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Lemtrada REMS Prescription ordering form received?'
			columns: 2

	checklist_rems_certified:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Prescriber has been Lemtrada REMS certified?'
			columns: 2

	checklist_solumedrol_dispensed:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Solu-medrol ordered and dispensed?'
			columns: 2

	checklist_benadryl_dispensed:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Benadryl and Tylenol ordered and dispensed?'
			columns: 2

	checklist_patient_guide:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Patient provided with copy of Lemtrada Patient Guide?'
			columns: 2

	checklist_need_to_know:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Patient provided with copy of  "What you need to know?"'
			columns: 2

	alert:
		model:
			default: 'Complete post infusion checklist assessment and fax to ************ EVEN IF PATIENT NOT INFUSED!! Report all suspected AE to Genzyme ************'
		view:
			control: 'area'
			label: 'Lemtrada Post Visit Alert'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['careplan_id', 'order_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections_group: [
		'Lemtrada Questionnaire':
			note: 'Nurse must measure vitals for 2 hours post infusion'
			sections: [
				'REMS Enrollment':
					fields: ['rems_enrolled']
				'Lemtrada Risks':
					fields: ['ordered_prophylaxis']
				'Lemtrada Checklist':
					fields: ['checklist_rems_form_received',  'checklist_rems_certified', 'checklist_solumedrol_dispensed',
					'checklist_benadryl_dispensed', 'checklist_patient_guide', 'checklist_need_to_know']
				'Lemtrada Post Visit':
					area: 'footer'
					fields: ['alert']
			]
		]
view:
	comment: 'Patient > Careplan > Encounter > Lemtrada'
	label: 'Patient Encounter: Lemtrada'
