fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	name:
		model:
			required: true
		view:
			columns: 2
			label: 'Name'
			findunique: true

	bname_id:
		model:
			multi: true
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Linked Brand Name'

	phone:
		model:
			max: 21
		view:
			columns: 4
			format: 'us_phone'
			label: 'Phone #'

	fax:
		model:
			max: 21
		view:
			columns: 4
			format: 'us_phone'
			label: 'Fax #'

	days_prior_to_start_renewal:
		model:
			type: 'int'
		view:
			columns: 4
			label: 'Days Prior to Start Renewal'

	reenrollment_process:
		view:
			columns: 2
			control: 'area'
			label: 'Re-enrollment process'

	active:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			columns: 4
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Active?'
			findfilter: 'Yes'

	bin:
		view:
			columns: 4
			label: 'BIN'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{6}$'
				error: 'Invalid BIN, must be 6 digits'
			}]

	pcn:
		view:
			columns: 4
			label: 'PCN'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{1,10}$'
				error: 'Invalid PCN, must be 1-10 alphanumeric characters'
			}]

	renewal_auto:
		model:
			source: ['No', 'Yes']
		view:
			columns: 4
			control: 'radio'
			label: 'Auto-Renewal?'

	url:
		view:
			label: 'Reference URL'
			format: 'url'

	notes:
		view:
			control: 'area'
			label: 'Notes'

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'list_alt_payer'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					direct_attachment: 'Yes'
					alt_payer_id: '{code}'
					assigned_to: 'Alt. Payer'
					source: 'Scanned Document'
					form_code: '{code}'
					form_filter: 'list_alt_payer'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['assistance']
	indexes:
		unique: [
			['name']
			['bname_id']
			['code']
		]
	name: ['name']
	sections_group:[
		'Program Details':
			fields: ['code', 'name','bname_id','phone','fax',
			'days_prior_to_start_renewal', 'reenrollment_process', 'active', 'renewal_auto',
			'bin','pcn', 'url', 'notes']
			tab: 'Program Details'
		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'
	]

view:
	comment: 'Manage > Alternative Payer'
	find:
		basic: ['name','bname_id']
	grid:
		fields: ['name','bname_id','phone','fax', 'days_prior_to_start_renewal', 'active']
		sort: ['name']
	label: 'Alternative Payer'
	open: 'read'