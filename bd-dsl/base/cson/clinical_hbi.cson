fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
		view:
			label: 'Careplan Id'

	well_being:
		model:
			required: true
			source:
				well: 'Very well'
				slighty_below: 'Slightly below par'
				poor: 'Poor'
				very_poor: 'Very poor'
				terrible: 'Terrible'
		view:
			control: 'radio'
			note: 'Yesterday'
			label: 'General well-being'

	abdominal_pain:
		model:
			required: true
			source:
				none: 'None'
				mild: 'Mild'
				moderate: 'Moderate'
				severe: 'Severe'
		view:
			control: 'radio'
			note: 'Yesterday'
			label: 'Abdominal pain'

	stools_per_day:
		model:
			min: 0
			rounding: 1
			type: 'decimal'
			required: true
		view:
			note: 'Yesterday'
			label: 'Number of liquid or soft stools per day'

	abdominal_mass:
		model:
			required: true
			source:
				none: 'None'
				dubious: 'Dubious'
				definite: 'Definite'
				tender: 'Definite and tender'
		view:
			control: 'radio'
			label: 'Abdominal mass'

	abdominal_mass:
		model:
			multi: true
			required: true
			source:
				none: 'None'
				arthralgia: 'Arthralgia'
				uveitis: 'Uveitis'
				erythema_nodosum: 'Erythema nodosum'
				aphthous_ulcers: 'Aphthous ulcers'
				pyoderma_gangrenosum: 'Pyoderma gangrenosum'
				anal_fissure: 'Anal fissure'
				new_fistula: 'New fistula'
				abscess: 'Abscess'
		view:
			control: 'checkbox'
			note: 'check any that apply'
			label: 'Complications'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_hbi:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Harvey-Bradshaw Index':
			fields: ['well_being', 'abdominal_pain', 'stools_per_day' , 'abdominal_mass']
			prefill: 'clinical_hbi'

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Harvey-Bradshaw Index'
	label: 'Harvey-Bradshaw Index'
