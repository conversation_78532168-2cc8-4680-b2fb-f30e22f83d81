fields:

	change_to:
		model:
			prefill: ['parent.send_to']
		view:
			label: 'To'
			readonly: true
			offscreen: true

	change_type:
		model:
			required: true
			source: ['Drug', 'Dosing', '# of Refills', 'Quantity', 'Day Supply']
			if:
				'Drug':
					fields: ['change_to', 'fdb_id', 'pc_qualifier_id', 'product_code',
					'quantity', 'qty_qualifier_id', 'quantity_uom_id',
					'day_supply', 'refills', 'sig', 'daw', 'note']
				'Dosing':
					fields: ['sig', 'note', 'daw']
				'# of Refills':
					fields: ['refills', 'note', 'daw']
				'Quantity':
					fields: ['quantity', 'qty_qualifier_id', 'quantity_uom_id', 'note', 'daw']
				'Day Supply':
					fields: ['day_supply', 'note', 'daw']
		view:
			control: 'checkbox'
			label: 'Change Request To:'

	fdb_id:
		model:
			required: true
			source: 'list_fdb_ndc'
			sourceid: 'code'
			prefill: ['parent.fdb_id']
		view:
			note: 'Leave as the original item if no change is needed'
			label: 'Suggested Change to Item'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_fdb_ndc/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'description': ['ln60']
					'dea_schedule_id':
						'dea':
							if:
								'1': 'C48672'
								'2': 'C48675'
								'3': 'C48676'
								'4': 'C48677'
								'5': 'C48679'
					'product_code': ['ndc']
			]

	#Body.MedicationRequested.DrugDescription
	description:
		model:
			prefill: ['parent.description']
		view:
			label: 'Drug Description'
			note: 'Loaded from First DataBank Description'
			readonly: true

	#Body.MedicationRequested.DrugCoded.ProductCode.Qualifier
	pc_qualifier_id:
		model:
			prefill: ['parent.product_code_qualifier_id']
			source: 'list_ss_product_qualifier'
			sourceid: 'code'
			default: 'ND'
		view:
			columns: 3
			label: 'Product Code Qualifier'
			readonly: true

	#Body.MedicationRequested.DrugCoded.ProductCode.Code
	product_code:
		model:
			prefill: ['parent.product_code']
		view:
			columns: 3
			label: 'NDC'
			readonly: true

	#Body.MedicationRequested.DrugCoded.DEASchedule.Code
	dea_schedule_id:
		model:
			prefill: ['parent.dea_schedule_id']
			source: 'list_dea_schedule'
			sourceid: 'code'
			validate: [
				{
					name: "DEADaySupply"
					fields: [
						"dea_schedule_id"
						"day_supply"
					]
				},
				{
					name: "DEAMAXRefills"
					fields: [
						"dea_schedule_id"
						"refills"
					]
				},
				{
					name: "CSServiceLevel"
					fields: [
						"dea_schedule_id"
						"change_to"
					]
				}
			]
		view:
			columns: 3
			label: 'DEA Schedule'
			readonly: true

	#Body.MedicationRequested.Quantity.Value
	quantity:
		model:
			prefill: ['parent.quantity']
			required: true
			rounding: 0.001
			type: 'decimal'
			max: 9999999.999
		view:
			columns: 3
			note: 'Leave as original value if no changes needed'
			label: 'Quantity'

	#Body.MedicationRequested.Quantity.CodeListQualifier
	qty_qualifier_id:
		model:
			default: 'QS'
			source: 'list_ss_quantity_qualifier'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Quantity Qualifier'

	#Body.MedicationRequested.Quantity.QuantityUnitOfMeasure.Code
	quantity_uom_id:
		model:
			prefill: ['parent.quantity_uom_id']
			required: true
			source: 'list_ncpdp_quantity_unit_msr'
			sourceid: 'code'
			if:
				'C64933':
					fields: ['ea_warning']
		view:
			columns: 3
			label: 'Quantity UOM'

	ea_warning:
		model:
			multi: true
			source: ["<span color='#D26158'>Quantity unit of measure code “C64933 (Each)” is only to be used for products that are not measured in volume or weight and can only be expressed in units of one/each, such as canes, wheelchairs, various braces or orthotics and other DME supplies.</span>"]
		view:
			control: 'checkbox'
			label: 'Warning'
			class: 'list'
			readonly: true

	#Body.MedicationRequested.DaysSupply
	day_supply:
		model:
			prefill: ['parent.day_supply']
			required: true
			max: 365
			min: 1
			type: 'int'
			validate: [
				{
					name: "DEADaySupply"
					fields: [
						"dea_schedule_id"
						"day_supply"
					]
				}
			]
		view:
			columns: 2
			note: 'Leave as original value if no changes needed. Must be > 0.'
			label: 'Day Supply'

	#Body.MedicationRequested.NumberOfRefills
	refills:
		model:
			prefill: ['parent.refills']
			required: true
			type: 'int'
			min: 1
			max: 99
			validate: [
				{
					name: "DEAMAXRefills"
					fields: [
						"dea_schedule_id"
						"refills"
					]
				}
			]
		view:
			columns: 2
			note: 'requested refills = first dispense + remaining refills.'
			label: 'Request Refills'

	#Body.MedicationRequested.Sig.SigText
	sig:
		model:
			max: 10000
			required: true
			prefill: ['parent.sig']
		view:
			columns: 2
			label: 'Sig'

	#Body.MedicationRequested.Substitutions
	daw:
		model:
			prefill: ['parent.daw']
			default: '0'
			source:
				'0': 'Substitution Allowed'
				'1': 'Dispense as Written'
			required: true
		view:
			columns: 2
			control: 'radio'
			label: 'Requested Dispense as Written'

	#Body.MedicationRequested.Note
	note:
		model:
			max: 210
		view:
			label: 'Note to Prescriber'

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['change_type']
			['fdb_id']
		]
	name: '{change_type} {fdb_id} to {change_to}'
	sections:
		'Medication Change/Alternatives':
			fields: ['change_to', 'change_type', 'fdb_id',
			'description', 'pc_qualifier_id', 'product_code',
			'quantity', 'qty_qualifier_id', 'quantity_uom_id',
			'day_supply', 'refills', 'sig', 'daw', 'note']
view:
	hide_cardmenu: true
	comment: 'Surescripts Medication Change/Alternatives'
	grid:
		fields: ['fdb_id', 'quantity', 'day_supply', 'refills', 'sig']
		sort: ['-created_on']
	label: 'Medication Change/Alternatives'
	open: 'read'
