fields:

	message:
		view:
			control: 'area'
			note: '504-F4'
			label: 'Message'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['message']
	sections:
		'Message':
			hide_header: true
			indent: false
			fields: ['message']

view:
	dimensions:
		width: '50%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Response Message'
	grid:
		fields: ['message']
		sort: ['-created_on']
	label: 'Response Message'
	open: 'read'