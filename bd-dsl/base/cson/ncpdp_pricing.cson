fields:
	is_test:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Is Test Claim?"
			readonly: true
			offscreen: true

	parent_claim_no:
		model:
			type: 'text'
		view:
			label: 'Parent Claim in COB Scenario'
			readonly: true
			offscreen: true

	transaction_code:
		model:
			prefill: ['parent.transaction_code']
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '103-A3'
			if:
				'B1': # Drug Billing
					fields: ['ing_cst_sub', 'disp_fee_sub', 'pt_pd_amt_sub', 'incv_amt_sub', 'subform_oclaim',
					'u_and_c_charge', 'cost_basis']
					sections: ['Other Charge Amounts']
					require_fields: ['gross_amount_due']
				'B2': # Claim Reversal
					fields: ['incv_amt_sub']
				'B3': # Drug Reverse and Bill
					fields: ['ing_cst_sub', 'disp_fee_sub', 'pt_pd_amt_sub', 'incv_amt_sub', 'subform_oclaim',
					'u_and_c_charge', 'cost_basis']
					sections: ['Other Charge Amounts']
					require_fields: ['gross_amount_due']
				'S1': # Service Billing
					fields: ['pro_svc_fee_sub', 'pt_pd_amt_sub', 'subform_oclaim', 'u_and_c_charge']
					sections: ['Other Charge Amounts']
					require_fields: ['gross_amount_due']
				'S3': # Service Reverse and Bill
					fields: ['pro_svc_fee_sub', 'pt_pd_amt_sub', 'subform_oclaim', 'u_and_c_charge']
					sections: ['Other Charge Amounts']
					require_fields: ['gross_amount_due']
		view:
			label: 'Transmission Code'
			offscreen: true
			readonly: true

	payer_id:
		model:
			type: 'int'
			required: true
			source: 'payer'
		view:
			label: 'Payer'
			readonly: true
			offscreen: true

	tax_code_id:
		model:
			source: 'list_tax_code'
			sourceid: 'code'
		view:
			label: 'Tax Code ID'
			readonly: true
			offscreen: true

	ing_cst_sub:
		model:
			required: true
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			min: 0.00
		view:
			columns: 4
			reference: '409-D9'
			note: '409-D9'
			label: 'Ingredient Cost'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	disp_fee_sub:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 4
			reference: '412-DC'
			note: '412-DC'
			label: 'Dispensing Fee'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			_meta:
				copy_forward: true

	pro_svc_fee_sub:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			required: true
		view:
			columns: 4
			reference: '477-BE'
			note: '477-BE'
			label: 'Professional Service Fee'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			_meta:
				copy_forward: true

	incv_amt_sub:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			min: 0.00
		view:
			columns: 4
			reference: '438-E3'
			note: '438-E3'
			label: 'Incentive Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			_meta:
				copy_forward: true

	pt_pd_amt_sub:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 4
			reference: '433-DX'
			note: '433-DX'
			label: 'Pt Paid Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			_meta:
				copy_forward: true

	u_and_c_charge:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 4
			reference: '426-DQ'
			note: '426-DQ'
			label: 'U&C Charge'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	flat_tax_amt:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			if:
				'*':
					require_fields: ['sales_tax_basis', 'per_sales_tax_rate_used']
		view:
			reference: '481-HA'
			note: '481-HA'
			label: 'Flat Tax Amount'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			offscreen: true

	sales_tax:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			if:
				'*':
					require_fields: ['sales_tax_basis', 'per_sales_tax_rate_used']
		view:
			reference: '482-GE'
			note: '482-GE'
			label: 'Sales Tax Submitted'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true
			offscreen: true

	per_sales_tax_rate_used:
		model:
			type: 'decimal'
			rounding: 0.00001
			max: 100
			if:
				'*':
					require_fields: ['sales_tax_basis', 'sales_tax']
		view:
			reference: '483-HE'
			note: '483-HE'
			label: 'Percentage Sales Tax Rate Used'
			class: 'numeral'
			format: 'percent'
			readonly: true
			offscreen: true

	sales_tax_basis:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '484-JE'
			if:
				'*':
					require_fields: ['per_sales_tax_rate_used', 'per_sales_tax_rate_used']
		view:
			columns: 4
			reference: '484-JE'
			note: '484-JE'
			label: 'Sales Tax Basis'
			readonly: true
			offscreen: true

	gross_amount_due:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			min: 0.00
		view:
			columns: 4
			reference: '430-DU'
			note: '430-DU'
			label: 'Gross Amt Due'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	cost_basis:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '423-DN'
		view:
			columns: 2
			reference: '423-DN'
			note: '423-DN'
			label: 'Basis of Cost Determination'
			readonly: true
			offscreen: true

	subform_oclaim:
		model:
			type: 'subform'
			multi: true
			source: 'ncpdp_pricing_osub'
		view:
			max_count: 3
			label: 'Other Charge Amounts'
			readonly: true
			_meta:
				copy_forward: true
			transform: [
				name: 'CalcGrossAmountDue'
			]

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['transaction_code', 'payer_id', 'disp_fee_sub']
	sections_group: [
		'Pricing':
			hide_header: true
			indent: false
			fields: ['is_test', 'transaction_code', 'payer_id', 'ing_cst_sub',
			'disp_fee_sub', 'pro_svc_fee_sub', 'incv_amt_sub', 'u_and_c_charge']

		'Tax':
			hide_header: true
			indent: false
			fields: ['flat_tax_amt', 'sales_tax', 'per_sales_tax_rate_used']

		'Due Amount':
			hide_header: true
			indent: false
			fields: ['gross_amount_due', 'cost_basis', 'sales_tax_basis', 'pt_pd_amt_sub']

		'Other Charge Amounts':
			note: 'COB Max 3 Entries'
			fields: ['subform_oclaim']
		]

view:
	comment: 'Pricing'
	grid:
		fields: ['ing_cst_sub', 'pt_pd_amt_sub', 'gross_amount_due', 'cost_basis']
		width: [25, 25, 25, 25]
		sort: ['-created_on']
	label: 'Pricing'
	open: 'read'
