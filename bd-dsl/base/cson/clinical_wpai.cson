fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_wpai.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	# Questionnaire
	wpai_paid_emp:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['wpai_miss_work', 'wpai_miss_work_other', 'wpai_total_work_hours', 'wpai_affect_work']
		view:
			control: 'radio'
			label: 'Are you currently in paid employment?'

	wpai_miss_work:
		model:
			max: 168
			min: 0
			type: 'int'
		view:
			label: 'During the past seven days, how many hours did you miss from work because of problems associated with your condition?'
			note: 'Include hours you missed on sick days, times you went in late, left early, etc. because of condition. Do not include time you missed to participate in this study.'

	wpai_miss_work_other:
		model:
			max: 168
			min: 0
			type: 'int'
		view:
			label: 'During the past seven days, how many hours did you miss from work because of any other reason, such as vacation, holidays, time off to participate in this study?'

	wpai_total_work_hours:
		model:
			max: 168
			min: 0
			type: 'int'
		view:
			label: 'During the past seven days, how many hours did you actually work?'

	wpai_affect_work:
		model:
			max: 10
			min: 1
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'During the past seven days, how much did condition affect your productivity while you were working?'
			note: 'If condition affected your work only a little, choose a low number. Choose a high number if condition affected your work a great deal.'

	wpai_affect_nonwork:
		model:
			max: 10
			min: 1
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'During the past seven days, how much did condition affect your ability to do your regular daily activities, other than work at a job?'
			note: 'If condition affected your activities only a little, choose a low number. Choose a high number if condition affected your activities a great deal.'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_wpai:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Work Productivity and Activity Impairment Questionnaire (WPAI)':
			fields: ['last_assessment_date', 'assessment_date']
			prefill: 'clinical_wpai'
		'Questionnaire':
			note: 'Ask the patient the following questions'
			fields: ['wpai_paid_emp', 'wpai_miss_work', 'wpai_miss_work_other', 'wpai_total_work_hours', 'wpai_affect_work', 'wpai_affect_nonwork']
			prefill: 'clinical_wpai'
view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Work Productivity and Activity Impairment Questionnaire'
	label: 'Work Productivity and Activity Impairment Questionnaire (WPAI)'
