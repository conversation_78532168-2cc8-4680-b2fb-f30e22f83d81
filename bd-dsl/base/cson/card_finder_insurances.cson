fields:

	patient_id:
		model:
			source: 'patient'
			type: 'int'
		view:
			columns: 3
			label: 'Patient ID'
			offscreen: true
			readonly: true

	patient_insurance_id:
		model:
			source: 'patient_insurance'
			type: 'int'
		view:
			columns: 3
			label: 'Patient Insurance ID'
			offscreen: true
			readonly: true

	mrn:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'MRN'
			readonly: true

	payer_id:
		model:
			source: 'payer'
			type: 'int'
		view:
			columns:3
			label: 'Payer'
			readonly: true

	bin:
		model:
			type: 'text'
		view:
			columns:3
			label: 'BIN'
			readonly: true

	pcn:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'PCN'
			readonly: true

	cardholder_id:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Cardholder ID'
			readonly: true

	group_number:
		model:
			max: 50
		view:
			columns: 3
			label: 'Group Number'
			readonly: true

	person_code:
		view:
			columns: 3
			label: 'Person Code'
			readonly: true

	effective_date:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Effective Date'
			readonly: true
	termination_date:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Termination Date'
			readonly: true

	insurance_contact_phone:
		model:
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Insurance Contact Phone'
			readonly: true

	billing_method_id:
		model:
			source: 'list_billing_method'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Billing Method ID'
			readonly: true

	type_id:
		model:
			source: 'list_payer_type'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Type'
			readonly: true

	pharmacy_relationship_id:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			default: '1'
			sourcefilter:
				field:
					'static': '306-C6'
		view:
			columns: 3
			control: 'select'
			label: 'Relationship to Subscriber'
			class: 'claim-field'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'pharm']

	sections_group: [
		'Insurance Information':
			fields: ['patient_id', 'patient_insurance_id', 'payer_id', 'bin', 'pcn','billing_method_id', 'type_id', 'mrn',
			'cardholder_id', 'group_number', 'person_code', 'pharmacy_relationship_id', 'effective_date', 'termination_date', 'insurance_contact_phone']
	]

	name: 'Card Finder Result'

view:
	comment: 'Patient > Card Finder Missing Insurances'
	find:
		basic: ['payer_id']
	grid:
		fields: ['payer_id']
	label: 'Insurances To Be Added'
