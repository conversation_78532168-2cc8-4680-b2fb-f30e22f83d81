fields:
	charge_no:
		model:
			prefill: ['parent.charge_no']
			required: false
		view:
			label: 'Charge No'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			required: false
			source: 'inventory'
			sourcefilter:
				type:
					'static': ['Equipment Rental']
				active:
					'static': 'Yes'
		view:
			columns: -2
			label: 'Equipment'
			readonly: true
			offscreen: true

	days:
		model:
			type: 'int'
			max: 999
			min: 1
		view:
			columns: 4
			readonly: true
			label: 'Days/Units'
			reference: 'SV503'
			_meta:
				location: '2410 SV5'
				field: '03'
				path: 'claimInformation.serviceLines[{idx1-50}].durableMedicalEquipmentService.days'

	frequency_code:
		model:
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SV506'
		view:
			columns: 4
			readonly: true
			label: 'Frequency Code'
			reference: 'SV506'
			_meta:
				location: '2410 SV5'
				field: '06'
				path: 'claimInformation.serviceLines[{idx1-50}].durableMedicalEquipmentService.frequencyCode'

	rental_price:
		model:
			max: **********.99
			min: 0.00
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Rental Price'
			reference: 'SV504'
			readonly: true
			_meta:
				location: '2410 SV5'
				field: '04'
				path: 'claimInformation.serviceLines[{idx1-50}].durableMedicalEquipmentService.rentalPrice'

	purchase_price:
		model:
			max: **********.99
			min: 0.00
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Purchase Price'
			reference: 'SV505'
			_meta:
				location: '2410 SV5'
				field: '05'
				path: 'claimInformation.serviceLines[{idx1-50}].durableMedicalEquipmentService.purchasePrice'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['inventory_id', 'days', 'frequency_code']
	sections:
		'DME':
			hide_header: true
			fields: ['charge_no', 'inventory_id', 'days', 'frequency_code', 'rental_price', 'purchase_price']

view:
	dimensions:
		width: '85%'
		height: '55%'
	hide_cardmenu: true
	reference: '2400'
	comment: 'DME'
	grid:
		fields: ['inventory_id', 'days', 'frequency_code', 'rental_price', 'purchase_price']
		width: [30, 30, 20, 20]
		sort: ['-created_on']
	label: 'DME'
	open: 'read'