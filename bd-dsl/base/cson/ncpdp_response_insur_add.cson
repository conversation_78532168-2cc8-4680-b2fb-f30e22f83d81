fields:

	medi_part_d_cov_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '139-UR'
		view:
			columns: 3
			note: '139-UR'
			label: 'MCD Coverage Code'
			readonly: true

	cms_low_income_sharing:
		view:
			columns: 3
			note: '138-UQ'
			label: 'CMS LICS Level'
			readonly: true

	contract_number:
		view:
			columns: 3
			note: '138-UQ'
			label: 'Contract #'
			readonly: true

	formulary_id:
		view:
			columns: 3
			note: '926-FF'
			label: 'Formulary ID'
			readonly: true
	
	benefit_id:
		view:
			columns: 3
			note: '757-U6'
			label: 'Benefit ID'
			readonly: true

	medi_part_d_eff_date:
		model:
			type: 'date'
		view:
			columns: 2
			note: '140-US'
			label: 'Next MCD Eff Date'
			readonly: true

	medi_part_d_trm_date:
		model:
			type: 'date'
		view:
			columns: 2
			note: '141-UT'
			label: 'Next MCD Term Date'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['medi_part_d_cov_code', 'formulary_id', 'benefit_id']
	sections:
		'Insurance Additional Information':
			hide_header: true
			indent: false
			fields: ['medi_part_d_cov_code', 'cms_low_income_sharing', 'contract_number', 'formulary_id',
			'benefit_id', 'medi_part_d_eff_date', 'medi_part_d_trm_date']

view:
	dimensions:
		width: '65%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Insurance Additional Information'
	grid:
		fields: ['medi_part_d_cov_code', 'cms_low_income_sharing', 'contract_number', 'formulary_id', 'benefit_id', 'medi_part_d_eff_date']
		sort: ['-created_on']
	label: 'Insurance Additional Information'
	open: 'read'