fields:

    symptom_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Symptom Start Date'
            reference: 'DTP03'
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '431'
                path: 'claimInformation.claimDateInformation.symptomDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    initial_treatment_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Initial Treatment Date'
            reference: 'DTP03'
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '454'
                path: 'claimInformation.claimDateInformation.initialTreatmentDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    last_seen_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Last Seen Date'
            reference: 'DTP03'
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '304'
                path: 'claimInformation.claimDateInformation.lastSeenDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    acute_manifestation_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Acute Manifestation Date'
            reference: 'DTP03'
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '453'
                path: 'claimInformation.claimDateInformation.acuteManifestationDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    accident_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Accident Date'
            reference: 'DTP03'
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '439'
                path: 'claimInformation.claimDateInformation.accidentDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    last_menstrual_period_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Last Menstrual Period Date'
            reference: 'DTP03'
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '484'
                path: 'claimInformation.claimDateInformation.lastMenstrualPeriodDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    disability_begin_date:
        model:
            type: 'date'
        view:
            columns: -3
            label: 'Disability Begin Date'
            reference: 'DTP03'
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '438'
                path: 'claimInformation.claimDateInformation.disabilityBeginDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    disability_end_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Disability End Date'
            reference: 'DTP03'
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '360'
                path: 'claimInformation.claimDateInformation.disabilityEndDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    last_worked_date:
        model:
            type: 'date'
        view:
            offscreen: true
            readonly: true
            label: 'Last Worked Date'
            reference: 'DTP03'
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '297'
                path: 'claimInformation.claimDateInformation.disabilityEndDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    repricer_received_date:
        model:
            type: 'date'
        view:
            label: 'Repricer Received Date'
            reference: 'DTP03'
            offscreen: true
            readonly: true
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '050'
                path: 'claimInformation.claimDateInformation.repricerReceivedDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    authorized_return_to_work_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Authorized Return to Work Date'
            reference: 'DTP03'
            offscreen: true
            readonly: true
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '296'
                path: 'claimInformation.claimDateInformation.authorizedReturnToWorkDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    first_contact_date:
        model:
            type: 'date'
        view:
            columns: -3
            label: 'First Contact Date'
            reference: 'DTP03'
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '444'
                path: 'claimInformation.claimDateInformation.firstContactDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    admission_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'Admin Date'
            reference: 'DTP03'
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '435'
                path: 'claimInformation.claimDateInformation.admissionDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    discharge_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'D/C Date'
            reference: 'DTP03'
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '096'
                path: 'claimInformation.claimDateInformation.dischargeDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    assumed_and_relinquished_care_begin_date:
        model:
            type: 'date'
        view:
            columns: -3
            label: 'ARC Begin Date'
            reference: 'DTP03'
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '090'
                path: 'claimInformation.claimDateInformation.assumedAndRelinquishedCareBeginDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

    assumed_and_relinquished_care_end_date:
        model:
            type: 'date'
        view:
            columns: 3
            label: 'ARC End Date'
            reference: 'DTP03'
            _meta:
                location: '2300 DTP'
                field: '03'
                code: '091'
                path: 'claimInformation.claimDateInformation.assumedAndRelinquishedCareEndDate'
            validate: [
                name: 'DateValidator'
                require: 'past'
            ]

model:
    access:
        create:     []
        create_all: ['admin', 'csr', 'pharm']
        delete:     ['admin', 'pharm']
        read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
        read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
        request:    []
        update:     []
        update_all: ['admin', 'csr', 'pharm']
        write:      ['admin', 'csr', 'pharm']
    name: ['symptom_date', 'initial_treatment_date', 'last_seen_date']
    sections:
        'Dates':
            hide_header: true
            fields: ['symptom_date', 'initial_treatment_date', 'last_seen_date',
                    'acute_manifestation_date', 'accident_date', 'last_menstrual_period_date',
                    'disability_begin_date', 'disability_end_date', 'last_worked_date',
                    'repricer_received_date', 'authorized_return_to_work_date',
                    'first_contact_date', 'admission_date', 'discharge_date',
                    'assumed_and_relinquished_care_begin_date', 'assumed_and_relinquished_care_end_date',
                    ]

view:
    dimensions:
        width: '85%'
        height: '65%'
    hide_cardmenu: true
    validate: [
        {
            name: "DateOrderValidator"
            fields: [
                "disability_begin_date",
                "disability_end_date"
            ]
            error: "Disability End Date cannot be before Disability Begin Date"
        },
        {
            name: "DateOrderValidator"
            fields: [
                "admission_date",
                "discharge_date"
            ]
            error: "Discharge Date cannot be before Admission Date"
        },
        {
            name: "DateOrderValidator"
            fields: [
                "last_worked_date",
                "authorized_return_to_work_date"
            ]
            error: "Authorized Return to Work Date cannot be before Last Worked Date"
        },
        {
            name: "DateOrderValidator"
            fields: [
                "assumed_and_relinquished_care_begin_date",
                "assumed_and_relinquished_care_end_date"
            ]
            error: "ARC End Date cannot be before ARC Begin Date"
        }
    ],
    reference: '2300'
    comment: 'Dates'
    grid:
        fields: ['symptom_date', 'initial_treatment_date', 'last_seen_date', 'first_contact_date']
        width: [25, 25, 25, 25]
        sort: ['-created_on']
    label: 'Dates'
    open: 'read'