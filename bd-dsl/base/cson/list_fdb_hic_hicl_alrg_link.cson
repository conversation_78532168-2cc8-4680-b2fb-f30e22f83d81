#TABLE: RDAMHHA0_HIC_HICL_ALG_LINK
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#HICL_SEQNO
	hicl_seqno:
		model:
			type: 'int'
		view:
			label: 'Ingredient List Identifier (Stable ID)	'
			readonly: true
			columns: 2
			note: '(formerly the Hierarchical Ingredient Code List Sequence Number)'

	#DAM_ALRGN_HIC_SEQN
	dam_alrgn_hic_seqn:
		model:
			type: 'int'
		view:
			label: 'DAM Allergen Hierarchical Ingredient Code Sequence Number (Stable ID)'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{hicl_seqno} {dam_alrgn_hic_seqn}"
	indexes:
		many: [
			['hicl_seqno']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['hicl_seqno', 'dam_alrgn_hic_seqn']

view:
	comment: 'Manage > List FDB Drug Allergy Screening HICL_SEQNO/HIC Relation Table'
	find:
		basic: ['hicl_seqno', 'dam_alrgn_hic_seqn']
	grid:
		fields: ['hicl_seqno', 'dam_alrgn_hic_seqn']
	label: 'List FDB Drug Allergy Screening HICL_SEQNO/HIC Relation Table'
