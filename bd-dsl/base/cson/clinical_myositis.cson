fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Difficulties
	diff_bed:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Require assistance, 2 = Normal)'
			label: 'Getting out of bed or rising from sitting position'

	diff_head:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficult, 2 = Normal)'
			label: 'Holding your head up'

	diff_climbing:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Require assistance, 2 = Normal)'
			label: 'Climbing 3 stairs'

	diff_chewing:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Chewing'

	diff_swallow:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficulty (i.e. choking), 2 = Normal)'
			label: 'Swallowing'

	diff_breath:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate-Mild, 2 = Normal)'
			label: 'Difficulty in breathing'

	diff_raise:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Partial, 2 = Normal)'
			label: 'Lifting arms (straight) over head'

	diff_voice:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = Normal)'
			label: 'Changes in voice (hoarseness)'

	diff_rash:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = Normal)'
			label: 'Skin rash / scaly dry rough skin (Dermatomyositis only)'

	diff_balance:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Stand on one foot for 5 seconds (using either foot)'

	diff_walk:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficult or requires assistance, 2 = Normal)'
			label: 'Walking 10 feet'

	diff_pain:
		model:
			max: 2
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			note: '(0 = No pain  -  10 = Severe pain)'
			label: 'Muscle pain'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_myositis:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Difficulties':
			note: 'On a scale of zero to two (0 - 2) (unless specified), with 0 meaning Extreme Difficulty and 2 meaning Normal, please rate your difficulty today with:'
			fields: ['diff_bed', 'diff_head', 'diff_climbing', 'diff_chewing', 'diff_swallow', 'diff_breath', 'diff_raise', 'diff_voice',
				'diff_rash', 'diff_balance', 'diff_walk', 'diff_pain']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Myositis Difficulties'
	label: 'Myositis Difficulties'
