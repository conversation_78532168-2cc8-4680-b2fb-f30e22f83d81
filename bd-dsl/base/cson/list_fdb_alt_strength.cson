#TABLE: RPEINS0_NDC_STR_LINK
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	ndc:
		view:
			label: 'NDC'
			findunique: true
			readonly: true
			columns: 2

	#HIC_SEQN
	hic_seqn:
		model:
			type: 'int'
		view:
			label: 'HIC Sequence Number'
			findunique: true
			readonly: true
			columns: 2

	#STR_CONC_TYPE_ID
	str_conc_type_id:
		model:
			type: 'int'
		view:
			label: 'Strength Concentration Type Identifier'
			readonly: true
			columns: 2

	#STRENGTH_STATUS_CODE
	strength_status_code:
		model:
			type: 'int'
		view:
			label: 'Strength Status Code'
			readonly: true
			columns: 2

	#INGREDIENT_STR
	ingredient_str:
		model:
			type: 'decimal'
		view:
			label: 'Ingredient Strength'
			readonly: true
			columns: 2

	#INGREDIENT_UOM_MSTR_ID
	ingredient_uom_mstr_id:
		model:
			type: 'int'
		view:
			label: 'Ingredient UOM Master ID'
			readonly: true
			columns: 2

	#STRENGTH_TYP_CODE
	strength_typ_code:
		model:
			type: 'int'
		view:
			label: 'Strength Type Code'
			readonly: true
			columns: 2

	#VOLUME
	volume:
		model:
			type: 'decimal'
		view:
			label: 'Volume'
			readonly: true
			columns: 2

	#VOLUME_UOM_MSTR_ID
	volume_uom_mstr_id:
		model:
			type: 'int'
		view:
			label: 'Volume UOM Master ID'
			readonly: true
			columns: 2

 	#ALT_STR
	alt_str:
		model:
			type: 'decimal'
		view:
			label: 'Alternative Ingredient Strength'
			readonly: true
			columns: 2

	#ALT_STR_UOM_MSTR_ID
	alt_str_uom_mstr_id:
		model:
			type: 'int'
		view:
			label: 'Alternative Ingredient Strength UOM Master ID'
			readonly: true
			columns: 2

	#ALT_STRENGTH_TYP_CODE
	alt_strength_typ_code:
		model:
			type: 'int'
		view:
			label: 'Alternative Strength Type Code'
			readonly: true
			columns: 2

 	#TIME_VALUE
	time_value:
		model:
			type: 'decimal'
		view:
			label: 'Ingredient Strength Time Value'
			readonly: true
			columns: 2

	#TIME_UOM_MSTR_ID
	time_uom_mstr_id:
		model:
			type: 'int'
		view:
			label: 'Ingredient Strength Time Unit of Measure Master Identifier'
			readonly: true
			columns: 2

	#RANGE_MAX
	range_max:
		model:
			type: 'decimal'
		view:
			label: 'Ingredient Strength Range Maximum'
			readonly: true
			columns: 2

	#RANGE_MIN
	range_min:
		model:
			type: 'decimal'
		view:
			label: 'Ingredient Strength Range Minimum'
			readonly: true
			columns: 2
			

	#DOSAGE_FORM_ATTRIBUTE_ID	
	dosage_form_attribute_id:
		model:
			type: 'int'
		view:
			label: 'Dosage Form Attribute ID'
			readonly: true
			columns: 2

	#INGREDIENT_SORT_ORDER
	ingredient_sort_order:
		model:
			type: 'int'
		view:
			label: 'Ingredient Sort Order'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{ndc} {hic_seqn} {ingredient_str} {ingredient_uom_mstr_id}"
	indexes:
		many: [
			['ndc']
			['hic_seqn']
			['str_conc_type_id']
			['ingredient_uom_mstr_id']
			['volume_uom_mstr_id']
			['alt_str_uom_mstr_id']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['ndc', 'hic_seqn', 'str_conc_type_id', 'strength_status_code',
			'ingredient_str', 'ingredient_uom_mstr_id', 'strength_typ_code', 'volume', 'volume_uom_mstr_id', 'alt_str',
			'alt_str_uom_mstr_id', 'alt_strength_typ_code', 'time_value', 'time_uom_mstr_id',
			'range_max', 'range_min', 'dosage_form_attribute_id', 'ingredient_sort_order']

view:
	comment: 'Manage > Alternative NDC Ingredient Strength Link Table'
	find:
		basic: ['ndc', 'hic_seqn', 'ingredient_str', 'ingredient_uom_mstr_id']
	grid:
		fields: ['ndc', 'hic_seqn', 'ingredient_str', 'ingredient_uom_mstr_id']
	label: 'Alternative NDC Ingredient Strength Link Table'
