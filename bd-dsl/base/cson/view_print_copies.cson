fields:
	list:
		model:
			source: 
				'delivery_ticket_info':'Delivery Ticket'
				'po_label': 'Labels'
				'Pharmacy_PO_Work_Order': 'Work Order'
				
			if:
				'delivery_ticket_info':
					fields: ['no_of_delivery_ticket', 'delivery_ticket_printer']
				'po_label':
					fields: ['no_of_labels', 'labels_ticket_printer']
				'Pharmacy_PO_Work_Order':
					fields: ['no_of_work_order', 'work_order_printer']
			multi: true
		view:
			control: 'checkbox'
			label: 'Choose Documents to Print'
			columns: 1

	no_of_delivery_ticket:
		model:
			required: true
			type: 'int'
		view:
			label: 'Number of Delivery Ticket Copies'
			columns: 2

	delivery_ticket_printer:
		model:
			source: []
			required: true
		view:
			label: 'Delivery Ticket Printer'
			columns: 2

	no_of_labels:
		model:
			required: true
			type: 'int'
		view:
			label: 'Number of Label Copies'
			columns: 2

	labels_ticket_printer:
		model:
			source: []
			required: true
		view:
			label: 'Label Printer'
			columns: 2

	no_of_work_order:
		model:
			required: true
			type: 'int'
		view:
			label: 'Number of Work Order Copies'
			columns: 2

	work_order_printer:
		model:
			source: []
			required: true
		view:
			label: 'Work Order Printer'
			columns: 2
model:
	save: false
	access:
		create:     []
		create_all: []
		delete:     []
		read:       []
		read_all:   []
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      []
	name: ['list']
	sections_group: [
		'Print Options':
			hide_header: true
			indent: false
			fields: ['list', 'no_of_delivery_ticket', 'delivery_ticket_printer',  'no_of_labels', 'labels_ticket_printer', 'no_of_work_order', 'work_order_printer' ]
	]

view:
	hide_cardmenu: true
	comment: 'Print Options'
	grid:
		fields: ['list']
		sort: ['-id']
	label: 'Print Options'
	open: 'edit'