fields:
	benefit_stage_qualifier:
		model:
			required: false
			source: 'list_ncpdp_ext_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '393-MV'
		view:
			columns: 2
			reference: '393-MV'
			note: '393-MV'
			label: 'Benefit Stage'

	benefit_stage_amount:
		model:
			required: true
			type: 'decimal'
			rounding: 0.01
			max: 99999999.99
		view:
			columns: 2
			reference: '394-MW'
			note: '394-MW'
			label: 'Benefit Stage Amount'
			class: 'numeral money'
			format: '$0,0.00'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['benefit_stage_qualifier', 'benefit_stage_amount']
	sections:
		'Benefit Stage':
			hide_header: true
			indent: false
			fields: ['benefit_stage_qualifier', 'benefit_stage_amount']

view:
	dimensions:
		width: '50%'
		height: '25%'
	hide_cardmenu: true
	comment: 'Benefit Stage'
	grid:
		fields: ['benefit_stage_qualifier', 'benefit_stage_amount']
		sort: ['-created_on']
	label: 'Benefit Stage'
	open: 'read'