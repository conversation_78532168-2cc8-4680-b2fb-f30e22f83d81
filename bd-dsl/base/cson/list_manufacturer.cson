fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

#FDB RLBLRID3_LBLR_DESC
	mfg:
		model:
			max: 64
			required: true
		view:
			columns: 2.1
			label: 'Manufacturer Name'
			findunique: true

	lblrid:
		view:
			columns: 3
			label: 'Labeler Identifier'

	lblrind:
		view:
			columns: 3
			label: 'Labeler Indicator Code'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm', 'cm', 'cma']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'pharm', 'cm', 'cma']
		write:      ['admin', 'pharm', 'cm', 'cma']
	bundle: ['lists']
	sync_mode: 'full'
	indexes:
		unique: [
			['lblrid']
		]
		many:[
			['code']
			['mfg']
		]
	name: ['mfg']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['mfg', 'lblrid', 'lblrind']

view:
	comment: 'Manage > Manufacturer'
	find:
		basic: ['mfg']
	grid:
		fields: ['mfg']
		sort: ['mfg']
	label: 'Manufacturer'
