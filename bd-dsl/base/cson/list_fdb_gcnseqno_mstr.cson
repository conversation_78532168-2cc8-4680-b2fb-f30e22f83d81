#TABLE: RGCNSEQ4_GCNSEQNO_MSTR
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#GCN_SEQNO
	gcn_seqno:
		model:
			type: 'int'
		view:
			label: 'Clinical Formulation ID (Stable ID)'
			readonly: true
			columns: 3

	#HIC3
	hic3:
		view:
			label: 'Hierarchical Specific Therapeutic Class Code (Stable ID)'
			readonly: true
			columns: 3

	#HICL_SEQNO
	hicl_seqno:
		model:
			type: 'int'
		view:
			label: 'Ingredient List Identifier (Stable ID)'
			readonly: true
			columns: 3
			note: '(formerly the Hierarchical Ingredient Code LIst Sequence Number)'

	#GCDF
	gcdf:
		view:
			label: 'Dosage Form Code (2-character)'
			readonly: true
			columns: 3

	#GCRT
	gcrt:
		view:
			label: 'Route of Administration Code (1-character)'
			readonly: true
			columns: 3

	#STR
	str:
		view:
			label: 'Drug Strength Description'
			readonly: true
			columns: 3

	#GTC
	gtc:
		model:
			type: 'int'
		view:
			label: 'Therapeutic Class Code, Generic'
			readonly: true
			columns: 3

	#TC
	tc:
		model:
			type: 'int'
		view:
			label: 'Therapeutic Class Code, Standard'
			readonly: true
			columns: 3

	#DCC
	dcc:
		view:
			label: 'Ingredient Status Code'
			readonly: true
			columns: 3

	#HIC3_SEQN
	hic3_seqn:
		model:
			type: 'int'
		view:
			label: 'Hierarchical Specific Therapeutic Class Code Sequence Number (Stable ID)'
			readonly: true
			columns: 3

	#STR60
	str60:
		view:
			label: 'Drug Strength Description - 60'
			readonly: true
			columns: 3

	#GCNSEQ_GI
	gcnseq_gi:
		view:
			label: 'GCN_SEQNO-Level Multi-Source/Single Source Indicator'
			readonly: true
			columns: 3

	#GENDER
	gender:
		model:
			type: 'int'
		view:
			label: 'Ingredient Status Code'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{str60}"
	indexes:
		many: [
			['gcn_seqno']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['gcn_seqno', 'hic3', 'hicl_seqno', 'gcdf', 'gcrt', 'str', 'gtc', 'tc', 'dcc', 'hic3_seqn', 'str60', 'gcnseq_gi', 'gender']

view:
	comment: 'Manage > List FDB Clinical Formulation ID Table'
	find:
		basic: ['gcn_seqno', 'hic3', 'hicl_seqno', 'gcdf']
	grid:
		fields: ['gcn_seqno', 'hic3', 'hicl_seqno', 'gcdf']
	label: 'List FDB Clinical Formulation ID Table'
