fields:

	patient_id:
		model:
			required: false
			type: 'int'
			source: 'patient'
		view:
			label: 'Patient Id'

	firstname:
		model:
			prefill: ['patient.firstname']
		view:
			label: 'First Name'
			readonly: true
			offscreen: true

	lastname:
		model:
			prefill: ['patient.lastname']
		view:
			label: 'Last Name'
			readonly: true
			offscreen: true

	address_type:
		model:
			source: ['Home', 'Shipping', 'Other']
			if:
				'Shipping':
					fields: ['ship_location', 'ship_site_of_service', 'ship_to']
				'Home':
					fields: ['ship_sameas']
		view:
			control: 'radio'
			label: 'Address Type'
			columns: 4
			validate: [{
				name: 'BuildNameValue'
				dest: 'ship_to'
				fields: ['firstname', 'lastname']
			}]

	ship_location:
		model:
			source: ['Infusion Center', 'Physician Office', 'Other']
			if:
				'Physician Office':
					prefill:
						ship_site_of_service: 'MD Office'
					trigger: ['physician_id']
		view:
			control: 'radio'
			label: 'Administration Location'
			columns: 4

	ship_sameas:
		model:
			default: 'Yes'
			source: ['Yes']
			if:
				'Yes':
					fields: ['ship_to']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Same As Shipping Address?'
			columns: 4
			validate: [{
				name: 'BuildNameValue'
				dest: 'ship_to'
				fields: ['firstname', 'lastname']
			}]

	ship_to:
		view:
			label: 'Ship To'
			columns: 4

	ship_site_of_service:
		model:
			source: ['MD Office', 'Outpatient facility', 'SNF/LTC']
			if:
				'MD Office':
					fields: ['prescriber_id']
					prefill:
						prescriber_id: 'patient_prescriber.id'
		view:
			control: 'radio'
			label: 'Site of service'
			columns: 4

	prescriber_id:
		model:
			source: 'patient_prescriber'
			query: 'select_patient_prescriber_address'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			columns: 4
			label: 'Prescriber'
			class: 'select_prefill'
			transform: [
				suppress_refetch: true 
				name: 'SelectPrefill'
				url: '/query/select_patient_prescriber_address/?filter=id:'
				fields:
					'street': ['address1']
					'street2': ['address2']
					'city': ['city']
					'state_id': ['state_id']
					'zip': ['zip']
			]

	addresslabel:
		view:
			label: 'Address'
			offscreen: true
			readonly: true

	street:
		model:
			max: 128
			min: 4
		view:
			columns: 'addr_1'
			label: 'Street'
			class: "api_prefill"
			transform: [{
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'street': ['addressLabel']
					'city': ['city']
					'state_id': ['stateCode']
					'zip': ['postalCode']
			},
			{
				name: 'BuildAddressLabel'
				dest_field: 'addresslabel'
				fields:
					'street': ['street']
					'street2': ['street2']
					'city': ['city']
					'state': ['state_id']
					'zip': ['zip']
				
			}
			]

	street2:
		model:
			max: 128
		view:
			columns: 'addr_2'
			label: 'Street 2'
			transform: [{
				name: 'BuildAddressLabel'
				dest_field: 'addresslabel'
				fields:
					'street': ['street']
					'street2': ['street2']
					'city': ['city']
					'state': ['state_id']
					'zip': ['zip']
				
			}]

	city:
		model:
			max: 128
			min: 1
		view:
			columns: 'addr_city'
			label: 'City'
			transform: [{
				name: 'BuildAddressLabel'
				dest_field: 'addresslabel'
				fields:
					'street': ['street']
					'street2': ['street2']
					'city': ['city']
					'state': ['state_id']
					'zip': ['zip']
				
			}]

	state_id:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			label: 'State'
			columns: 'addr_state'
			transform: [{
				name: 'BuildAddressLabel'
				dest_field: 'addresslabel'
				fields:
					'street': ['street']
					'street2': ['street2']
					'city': ['city']
					'state': ['state_id']
					'zip': ['zip']
				
			}]

	zip:
		model:
			max: 10
			min: 5
		view:
			format: 'us_zip'
			label: 'Zip'
			columns: 'addr_zip'
			transform: [{
				name: 'BuildAddressLabel'
				dest_field: 'addresslabel'
				fields:
					'street': ['street']
					'street2': ['street2']
					'city': ['city']
					'state': ['state_id']
					'zip': ['zip']
				
			},
			{
				name: 'CityStateTransform'
				fields:
					zip: 'zip'
					city: 'city'
					state: 'state_id'
			}]

model:
	prefill:
		patient:
			link:
				id: 'patient_id'
		patient_prescriber:
			link:
				patient_id: 'patient_id'
			filter:
				primary: 'Yes'
			max: 'created_on'
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'pharm']
		request:    ['patient']
		update:     ['nurse', 'patient']
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	indexes:
		many: [
			['street', 'street2', 'city', 'state_id', 'zip']
		]
		unique: [
			['street', 'street2', 'city', 'state_id', 'zip']
		]

	name: '{address_type} {street} {city},{state_id} {zip}'

	sections:
		'Address':
			hide_header: true
			indent: false
			tab: 'Address'
			fields: ['firstname', 'lastname', 'address_type', 'addresslabel', 'ship_location', 'ship_sameas', 'ship_to',
			'ship_site_of_service', 'prescriber_id', 'street', 'street2', 'city',
			'state_id', 'zip']

view:
	dimensions:
		width: '75%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Patient Address'
	find:
		basic: ['address_type', 'ship_location', 'ship_site_of_service']
	grid:
		fields: ['address_type', 'street', 'street2', 'city', 'state_id', 'zip']
		width: [15, 25, 20, 15, 10, 15]
	label: 'Patient Address'
	open: 'read'
