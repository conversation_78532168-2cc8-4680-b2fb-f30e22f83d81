fields:

	cash_no:
		view:
			note: 'Associated Cash Posting Number if processed'
			label: 'Cash No'
			readonly: true
			offscreen: true

	payer_id:
		model:
			type: 'int'
			source: 'payer'
		view:
			label: 'Payer ID'
			readonly: true
			offscreen: true

	# Transaction Level Fields
	control_number:
		view:
			columns: 4
			label: 'Batch Control Number'
			note: 'ST02'
			readonly: true
			_meta:
				path: 'transactions[].controlNumber'

	production_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Production Date'
			note: 'DTM02 where DTM01=405'
			readonly: true
			_meta:
				path: 'transactions[].productionDate'

	# Financial Information
	check_or_eft_trace_number:
		view:
			columns: 4
			label: 'Check/EFT Trace Number'
			note: 'TRN02'
			readonly: true
			_meta:
				path: 'transactions[].paymentAndRemitReassociationDetails.checkOrEFTTraceNumber'

	total_actual_provider_payment_amount:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Payment Amount'
			note: 'BPR02'
			readonly: true
			_meta:
				path: 'transactions[].financialInformation.totalActualProviderPaymentAmount'

	credit_or_debit_flag_code:
		model:
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'BPR03'
		view:
			columns: 4
			control: 'radio'
			label: 'Credit/Debit Flag Code'
			note: 'BPR03'
			readonly: true
			_meta:
				path: 'transactions[].financialInformation.creditOrDebitFlagCode'

	payment_method_code:
		model:
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'BPR04'
		view:
			columns: 4
			label: 'Payment Method'
			note: 'BPR04'
			readonly: true
			_meta:
				path: 'transactions[].financialInformation.paymentMethodCode'

	payment_format_code:
		model:
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'BPR05'
		view:
			columns: 4
			label: 'Payment Format Code'
			note: 'BPR05'
			readonly: true
			_meta:
				path: 'transactions[].financialInformation.paymentFormatCode'

	sender_dfi_id_number_qualifier:
		model:
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'BPR06'
		view:
			columns: 4
			label: 'Sender DFI ID Number Qualifier'
			note: 'BPR06'
			readonly: true
			_meta:
				path: 'transactions[].financialInformation.senderDfiIdNumberQualifier'

	sender_dfi_identifier:
		view:
			columns: 4
			label: 'Sender DFI Identifier'
			note: 'BPR07'
			readonly: true
			_meta:
				path: 'transactions[].financialInformation.senderAccountDetails.senderDfiIdentifier'

	sender_account_number_qualifier:
		model:
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'BPR08'
		view:
			columns: 4
			label: 'Sender Account Number Qualifier'
			note: 'BPR08'
			readonly: true
			_meta:
				path: 'transactions[].financialInformation.senderAccountDetails.senderAccountNumberQualifier'

	sender_account_number:
		view:
			columns: 4
			label: 'Sender Account Number'
			note: 'BPR09'
			readonly: true
			_meta:
				path: 'transactions[].financialInformation.senderAccountDetails.senderAccountNumber'

	receiver_dfi_id_number_qualifier:
		model:
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'BPR12'
		view:
			columns: 4
			label: 'Receiver DFI ID Number Qualifier'
			note: 'BPR12'
			readonly: true
			_meta:
				path: 'transactions[].financialInformation.receiverAccountDetails.receiverDfiIdNumberQualifier'

	receiver_dfi_identification_number:
		view:
			columns: 4
			label: 'Receiver DFI Identification Number'
			note: 'BPR13'
			readonly: true
			_meta:
				path: 'transactions[].financialInformation.receiverAccountDetails.receiverDfiIdentificationNumber'

	receiver_account_number_qualifier:
		model:
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'BPR14'
		view:
			columns: 4
			label: 'Receiver Account Number Qualifier'
			note: 'BPR14'
			readonly: true
			_meta:
				path: 'transactions[].financialInformation.receiverAccountDetails.receiverAccountNumberQualifier'

	receiver_account_number:
		view:
			columns: 4
			label: 'Receiver Account Number'
			note: 'BPR15'
			readonly: true
			_meta:
				path: 'transactions[].financialInformation.receiverAccountDetails.receiverAccountNumber'

	check_issue_or_eft_effective_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Check/EFT Effective Date'
			note: 'BPR16'
			readonly: true
			_meta:
				path: 'transactions[].financialInformation.checkIssueOrEFTEffectiveDate'

	# Payer Information
	payer_name:
		view:
			columns: 4
			label: 'Payer Name'
			note: 'N102'
			readonly: true
			_meta:
				path: 'transactions[].payer.name'

	payer_identification_number:
		view:
			columns: 4
			label: 'Payer ID'
			note: 'REF02 where REF01=2U'
			readonly: true
			_meta:
				path: 'transactions[].payer.payerIdentificationNumber'

	payer_cms_plan_id:
		view:
			columns: 4
			label: 'CMS Plan ID'
			note: 'N104 where N103=XV'
			readonly: true
			_meta:
				path: 'transactions[].payer.centersForMedicareAndMedicaidServicesPlanId'

	payer_address_1:
		view:
			columns: 'addr_1'
			label: 'Payer Address 1'
			readonly: true
			_meta:
				path: 'transactions[].payer.address.address1'

	payer_address_2:
		view:
			columns: 'addr_2'
			label: 'Payer Address 2'
			readonly: true
			_meta:
				path: 'transactions[].payer.address.address2'

	payer_city:
		view:
			columns: 'addr_city'
			label: 'Payer City'
			readonly: true
			_meta:
				path: 'transactions[].payer.address.city'

	payer_state:
		view:
			columns: 'addr_state'
			label: 'Payer State'
			readonly: true
			_meta:
				path: 'transactions[].payer.address.state'

	payer_zip:
		view:
			columns: 'addr_zip'
			label: 'Payer Zip'
			readonly: true
			_meta:
				path: 'transactions[].payer.address.postalCode'

	payer_contact_name:
		view:
			columns: 4
			label: 'Payer Contact Name'
			note: 'PER02'
			readonly: true
			_meta:
				path: 'transactions[].payer.businessContactInformation.contactName'

	payer_contact_methods:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_835_pcnt'
		view:
			label: 'Payer Contact Methods'
			readonly: true
			_meta:
				path: 'transactions[].payer.businessContactInformation.contactMethods[*]'

	technical_contact_name:
		view:
			columns: 4
			label: 'Technical Contact Name'
			note: 'LOOP 1000A; PER02'
			readonly: true
			_meta:
				path: 'transactions[].payer.technicalContactInformation.contactName'

	technical_contact_methods:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_835_btcm'
		view:
			label: 'Technical Contact Methods'
			readonly: true
			_meta:
				path: 'transactions[].payer.technicalContactInformation.contactMethods[*]'

	# Payee (Pharmacy) Information
	payee_name:
		view:
			columns: 4
			label: 'Pharmacy Name'
			note: 'N102'
			readonly: true
			_meta:
				path: 'transactions[].payee.name'

	payee_npi:
		view:
			columns: 4
			label: 'Pharmacy NPI'
			note: 'N104 where N103=XX'
			readonly: true
			_meta:
				path: 'transactions[].payee.npi'

	payee_tax_id:
		view:
			columns: 4
			label: 'Pharmacy Tax ID'
			note: 'N104 where N103=FI'
			readonly: true
			_meta:
				path: 'transactions[].payee.taxId'

	payee_ncpdp_number:
		view:
			columns: 4
			label: 'NCPDP Number'
			note: 'REF02 where REF01=D3'
			readonly: true
			_meta:
				path: 'transactions[].payee.nationalCouncilForPrescriptionDrugProgramsPharmacyNumber'

	remittance_delivery_method_name:
		view:
			columns: 4
			label: 'Remittance Delivery Method'
			readonly: true
			_meta:
				path: 'transactions[].payee.remittanceDeliveryMethod.name'

	remittance_delivery_method_email:
		view:
			columns: 4
			label: 'Remittance Delivery Method Email'
			readonly: true
			_meta:
				path: 'transactions[].payee.remittanceDeliveryMethod.email'

	remittance_delivery_method_ftp:
		view:
			columns: 4
			label: 'Remittance Delivery Method FTP'
			readonly: true
			_meta:
				path: 'transactions[].payee.remittanceDeliveryMethod.ftp'

	remittance_delivery_method_on_line:
		view:
			columns: 4
			label: 'Remittance Delivery Method On Line'
			readonly: true
			_meta:
				path: 'transactions[].payee.remittanceDeliveryMethod.onLine'

	# Batch Payments
	provider_level_payments:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_835_bpmt'
		view:
			label: 'Provider Level Payments'
			readonly: true
			_meta:
				path: 'transactions[].detailInfo[*].providerSummaryInformation'

	# Provider Adjustments (Top Level)
	provider_adjustments:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_835_prov_adj'
		view:
			label: 'Provider Adjustments'
			readonly: true
			_meta:
				path: 'transactions[].providerAdjustments[].adjustments'

	total_payment_amount_processed:
		model:
			type: 'decimal'
			required: false
			rounding: 0.01
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Payment Amount Processed'

	claims_processed:
		model:
			type: 'int'
			required: false
		view:
			columns: 4
			label: 'Claims Processed'

	# Meta and Response Data
	response_meta:
		model:
			type: 'subform'
			multi: false
			source: 'med_claim_resp_ch_meta'
		view:
			label: 'Response Meta Data'
			readonly: true
			_meta:
				path: 'meta'

	response_id:
		model:
			type: 'int'
			required: true
		view:
			label: 'Response ID'
			readonly: true
			offscreen: true

	response_raw_json:
		model:
			type: 'json'
		view:
			label: 'Raw Response Json'
			class: 'json-viewer'
			readonly: true

	raw_report_url:
		view:
			label: 'Raw Report URL'
			readonly: true
			offscreen: true

	s3_filehash:
		view:
			label: 'S3 File Hash'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: '{production_date} ${total_payment_amount_processed}'
	indexes:
		unique: [
			['control_number']
		]
		many: [
			['control_number']
			['response_id']
			['payer_id']
			['payee_npi']
			['payer_name']
			['payer_identification_number']
			['check_or_eft_trace_number']
		]
	sections_group: [
		'Claim Payment Advice Batch Response (835)':
			hide_header: true
			sections: [
				'Payment Summary':
					hide_header: true
					tab: 'Summary'
					fields: ['response_id', 'payer_id', 'control_number', 'production_date', 'total_payment_amount_processed', 'claims_processed']
				'Sender':
					tab: 'Summary'
					fields: ['payer_name', 'payer_identification_number', 'payer_cms_plan_id',
					'payer_address_1', 'payer_address_2', 'payer_city', 'payer_state', 'payer_zip']
				'Receiver':
					tab: 'Summary'
					fields: ['payee_name', 'payee_npi', 'payee_tax_id', 'payee_ncpdp_number', 'remittance_delivery_method_name',
					'remittance_delivery_method_email', 'remittance_delivery_method_ftp', 'remittance_delivery_method_on_line',
					'receiver_dfi_id_number_qualifier', 'receiver_dfi_identification_number', 'receiver_account_number_qualifier',
					'receiver_account_number']
				'Payment Information':
					tab: 'Summary'
					fields: ['total_actual_provider_payment_amount', 'credit_or_debit_flag_code', 'payment_method_code',
					'payment_format_code', 'check_or_eft_trace_number', 'check_issue_or_eft_effective_date']
				'Batch Adjustments':
					tab: 'Summary'
					fields: ['provider_adjustments']
				'Payer Contact':
					tab: 'Payer Contact'
					fields: ['payer_contact_name']
				'Payer Contact Methods':
					tab: 'Payer Contact'
					fields: ['payer_contact_methods']
				'Payer TechnicalContact':
					tab: 'Payer Contact'
					fields: ['technical_contact_name']
				'Payer Technical Contact Methods':
					tab: 'Payer Contact'
					fields: ['technical_contact_methods']
				'Response Meta Data':
					hide_header: true
					tab: 'Meta Data'
					fields: ['response_meta']
				'Response Raw Response':
					hide_header: true
					tab: 'Meta Data'
					fields: ['response_raw_json']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '85%'
	hide_cardmenu: true
	comment: 'Claim Payment Advice Batch Response (835)'
	grid:
		fields: ['production_date', 'check_or_eft_trace_number', 'total_actual_provider_payment_amount']
		label: ['Date', 'Check/EFT Trace #', 'Total Payment Amount']
		width: [20, 50, 30]
		sort: ['-created_on']
	label: 'Claim Payment Advice Batch Response (835)'
	open: 'read'