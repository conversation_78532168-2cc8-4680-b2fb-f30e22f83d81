fields:
		
	missing_quantity:
		model:
			source: ['Yes', 'No']
			default: 'No'
			if:
				'Yes':
					fields: ['quantity']
					require_fields: ['quantity']
		view:
			columns: 2
			control: 'radio'
			label: 'Missing Quantity'
			readonly: true
			offscreen: true	
	
	missing_dispensed_quantity:
		model:
			source: ['Yes', 'No']
			default: 'No'
			if:
				'Yes':
					fields: ['dispensed_quantity']
					require_fields: ['dispensed_quantity', 'dispensed_unit_id']
		view:
			columns: 2
			control: 'radio'
			label: 'Missing Dispense Quantity'
			readonly: true
			offscreen: true	
	
	missing_expiration:
		model:
			source: ['Yes', 'No']
			default: 'No'
			if:
				'Yes':
					fields: ['expiration']
					require_fields: ['expiration']
		view:
			columns: 2
			control: 'radio'
			label: 'Missing Expiration'
			readonly: true
			offscreen: true	
	
	missing_lot_no:
		model:
			source: ['Yes', 'No']
			default: 'No'
			if:
				'Yes':	
					fields: ['lot_no']
					require_fields: ['lot_no']
		view:
			columns: 2
			control: 'radio'
			label: 'Missing Lot No'
			readonly: true
			offscreen: true
	
	missing_serial_no:
		model:
			source: ['Yes', 'No']
			default: 'No'
			if:
				'Yes':
					fields: ['serial_no']
					require_fields: ['serial_no']
		view:
			columns: 2
			control: 'radio'
			label: 'Missing Serial No'
			readonly: true
			offscreen: true
	
	quantity:
		model:
			min: 1
			rounding: 1
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Quantity'
			columns: 4
			readonly: true
			offscreen: true

	dispensed_quantity:
		model:
			type: 'decimal'
			rounding: 0.001
		view:
			columns: 4
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Dispense Quantity'

	dispensed_unit_id:
		model:
			source: 'list_unit'
			sourceid: 'code'
		view:
			columns: 4
			label: 'Unit of Measure'
			readonly: true
			transform: [
				{
					name: 'UpdateUnitFieldNote'
					target: ['dispensed_quantity']
				}
			]

	expiration:
		model:
			type: 'date'
		view:
			label: 'Expiration'
			columns: 4
			validate: [
				name: 'DateValidator'
				require: 'future'
			]

	lot_no:
		model:
			type: 'text'
		view:
			label: 'Lot'
			columns: 4

	serial_no:
		model:
			type: 'text'
		view:
			label: 'Serial No'
			columns: 4

model:
	save: false
	bundle: []
	name: 'View Missing Scan Data'
	sections:
		'Scanned Item Data':
			fields: ['missing_quantity', 'missing_dispensed_quantity', 'missing_expiration', 'missing_lot_no', 'missing_serial_no', 'dispensed_quantity', 'dispensed_unit_id', 'expiration', 'lot_no', 'serial_no']
view:
	comment: 'Manage > Missing Scan Data'
	label: 'Missing Scan Data'
	open: 'read'
