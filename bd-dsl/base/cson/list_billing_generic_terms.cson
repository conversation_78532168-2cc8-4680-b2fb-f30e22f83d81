fields:

	name:
		model:
			required: true
			max: 128
		view:
			label: 'Name'
			findunique: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['billing']
	indexes:
		unique: [
			['name']
		]
	name: '{name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name']

view:
	comment: 'Manage > Payment Terms'
	find:
		basic: ['name']
	grid:
		fields: ['name']
		sort: ['name']
	label: 'Payment Terms'
	open: 'read'
