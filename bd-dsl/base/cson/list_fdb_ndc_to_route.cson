#TABLE: RPEINR0_NDC_RT_RELATION
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	ndc:
		view:
			label: 'NDC'
			findunique: true
			readonly: true
			columns: 3

	#PARENT_RT_ID
	parent_rt_id:
		view:
			label: 'Parent Route ID'
			readonly: true
			columns: 3

	#CLINICAL_RT_ID
	clinical_rt_id:
		view:
			label: 'Clinical Route ID'
			readonly: true
			columns: 3

	rt_labeled_id:
		model:
			type: 'int'
		view:
			label: 'Route Labeled Identifier'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['ndc', 'clinical_rt_id']
	indexes:
		many: [
			['ndc']
			['parent_rt_id']
			['clinical_rt_id']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['ndc', 'parent_rt_id', 'clinical_rt_id']

view:
	comment: 'Manage > List FDB NDC -> Route ID Crosswalk'
	find:
		basic: ['ndc', 'parent_rt_id', 'clinical_rt_id']
	grid:
		fields: ['ndc', 'parent_rt_id', 'clinical_rt_id']
	label: 'List FDB NDC -> Route ID Crosswalk'
