fields:

	direction:
		model:
			prefill: ['parent.direction']
			source:
				'IN': 'Inbox'
				'OUT': 'Outbox'
			if:
				'IN':
					readonly:
						fields: ['service_reason_id',
						'pservice_rsn_id', 'service_res_id', 'clinical_significance', 'ack_reason']
					fields: ['coagent_code', 'coagent_qualifier_id', 'coagent_description']
				'OUT':
					fields: ['coagent_id']
		view:
			label: 'Direction'
			readonly: true
			offscreen: true

	#Used to link back to the proper compound on an outbound message
	compound_no:
		model:
			type: 'int'
		view:
			label: 'Compound Number'
			readonly: true
			offscreen: true

	#ServiceReasonCode
	service_reason_id:
		model:
			source: 'list_ss_service_reason_code'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Service Reason Code'

	#ProfessionalServiceCode
	pservice_rsn_id:
		model:
			source: 'list_ss_pro_service_rsn_code'
			sourceid: 'code'
			if:
				'ZZ':
					require_fields: ['ack_reason']
		view:
			columns: 3
			label: 'Professional Service Code'

	#ServiceResultCode
	service_res_id:
		model:
			multi: true
			source: 'list_ss_service_result_code'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Service Result Code'
			readonly: true

	coagent_id:
		model:
			source: 'list_fdb_ndc'
			sourceid: 'code'
			if:
				'*':
					fields: ['coagent_code', 'coagent_qualifier_id', 'coagent_description']
		view:
			columns: 3
			label: 'Co-Agent'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_fdb_ndc/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'coagent_description': ['ln60']
			]

	#CoAgent.CoAgentCode.Code
	coagent_code:
		view:
			columns: 3
			label: 'Co-Agent Code'
			readonly: true

	#CoAgent.CoAgentCode.Qualifier
	coagent_qualifier_id:
		model:
			default: '03'
			source: 'list_ss_coagent_qualifier'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Co-Agent Qualifier'
			readonly: true

	#CoAgent.CoAgentCode.Description
	coagent_description:
		view:
			label: 'Description'

	#ClinicalSignificanceCode
	clinical_significance:
		model:
			source:
				'1': 'Major'
				'2': 'Moderate'
				'3': 'Minor'
				'9': 'Undetermined'
		view:
			columns: 2
			control: 'radio'
			label: 'Clinical Significance'

	#AcknowledgementReason
	ack_reason:
		model:
			max: 100
		view:
			columns: 2
			label: 'Acknowledgement Reason'
model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['service_reason_id']
			['pservice_rsn_id']
			['service_res_id']
		]
	name: '{coagent_description} {clinical_significance}'
	sections:
		'Drug Utilization Evaluation':
			fields: ['direction', 'compound_no', 'service_reason_id', 'pservice_rsn_id', 'coagent_id',
			'coagent_code', 'coagent_qualifier_id', 'coagent_description',
			'service_res_id', 'clinical_significance', 'ack_reason']
view:
	hide_cardmenu: true
	comment: 'Surescripts Drug Utilization Evaluation'
	grid:
		fields: ['service_reason_id', 'pservice_rsn_id', 'coagent_id', 'coagent_description', 'clinical_significance', 'service_res_id', 'ack_reason']
		sort: ['-created_on']
	label: 'Drug Utilization Evaluation'
	open: 'read'
