fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	ndc:
		model:
			type: 'text'
		view:
			label: 'National Drug Code'
			readonly: true
			columns: 3

	lblrid:
		model:
			type: 'text'
		view:
			label: 'Labeler Identifier'
			readonly: true
			columns: 3

	gcn_seqno:
		model:
			type: 'int'
		view:
			label: 'Clinical Formulation ID (Stable ID)'
			readonly: true
			columns: 3

	ps:
		model:
			type: 'decimal'
			rounding: 0.001
		view:
			label: 'Package Size'
			readonly: true
			columns: 3

	df:
		model:
			type: 'text'
		view:
			label: 'Drug Form Code'
			readonly: true
			columns: 3

	ad:
		model:
			type: 'text'
		view:
			label: 'Additional Descriptor'
			readonly: true
			columns: 3

	ln:
		model:
			type: 'text'
		view:
			label: 'Label Name'
			readonly: true
			columns: 3

	bn:
		model:
			type: 'text'
		view:
			label: 'Brand Name'
			readonly: true
			columns: 3

	pndc:
		model:
			type: 'text'
		view:
			label: 'Previous National Drug Code'
			readonly: true
			columns: 3

	repndc:
		model:
			type: 'text'
		view:
			label: 'Replacement National Drug Code'
			readonly: true
			columns: 3

	ndcfi:
		model:
			type: 'text'
		view:
			label: 'NDC Format'
			readonly: true
			columns: 3

	daddnc:
		model:
			type: 'date'
		view:
			label: 'Date of Add—NDC'
			readonly: true
			columns: 3

	dupdc:
		model:
			type: 'date'
		view:
			label: 'Date of Update—NDC'
			readonly: true
			columns: 3

	desi:
		model:
			type: 'text'
		view:
			label: 'DESI Drug'
			readonly: true
			columns: 3

	desdtec:
		model:
			type: 'date'
		view:
			label: 'DESI Status Change Effective Date'
			readonly: true
			columns: 3

	desi2:
		model:
			type: 'text'
		view:
			label: 'DESI2 Drug'
			readonly: true
			columns: 3

	des2dtec:
		model:
			type: 'date'
		view:
			label: 'DESI2 Status Change Effective Date'
			readonly: true
			columns: 3

	dea:
		model:
			type: 'text'
		view:
			label: 'Drug Enforcement Administration Code'
			readonly: true
			columns: 3

	cl:
		model:
			type: 'text'
		view:
			label: 'Class'
			readonly: true
			columns: 3

	gpi:
		model:
			type: 'text'
		view:
			label: 'This column is not currently being used.'
			readonly: true
			offscreen: true
			columns: 3

	hosp:
		model:
			type: 'text'
		view:
			label: 'Hospital Selection'
			readonly: true
			columns: 3

	innov:
		model:
			type: 'text'
		view:
			label: 'Innovator'
			readonly: true
			columns: 3

	ipi:
		model:
			type: 'text'
		view:
			label: 'Institutional Product'
			readonly: true
			columns: 3

	mini:
		model:
			type: 'text'
		view:
			label: 'Mini Selection'
			readonly: true
			columns: 3

	maint:
		model:
			type: 'text'
		view:
			label: 'Maintenance Drug'
			readonly: true
			columns: 3

	obc:
		model:
			type: 'text'
		view:
			label: 'Orange Book Code'
			readonly: true
			columns: 3

	obsdtec:
		model:
			type: 'date'
		view:
			label: 'Obsolete Date'
			readonly: true
			columns: 3

	ppi:
		model:
			type: 'text'
		view:
			label: 'Patient Package Insert'
			readonly: true
			columns: 3

	stpk:
		model:
			type: 'text'
		view:
			label: 'Standard Package'
			readonly: true
			columns: 3

	repack:
		model:
			type: 'text'
		view:
			label: 'Repackaged'
			readonly: true
			columns: 3

	top200:
		model:
			type: 'text'
		view:
			label: 'Top 200 Drugs'
			readonly: true
			columns: 3

	ud:
		model:
			type: 'text'
		view:
			label: 'Unit Dose'
			readonly: true
			columns: 3

	csp:
		model:
			type: 'int'
		view:
			label: 'Case Pack'
			readonly: true
			columns: 3

	ndl_gdge:
		model:
			type: 'decimal'
			rounding: 0.001
		view:
			label: 'Needle Gauge'
			readonly: true
			columns: 3

	ndl_lngth:
		model:
			type: 'decimal'
			rounding: 0.001
		view:
			label: 'Needle Length'
			readonly: true
			columns: 3

	syr_cpcty:
		model:
			type: 'decimal'
			rounding: 0.001
		view:
			label: 'Syringe Capacity'
			readonly: true
			columns: 3

	shlf_pck:
		model:
			type: 'int'
		view:
			label: 'Shelf Pack'
			readonly: true
			columns: 3

	shipper:
		model:
			type: 'int'
		view:
			label: 'Shipper Quantity'
			readonly: true
			columns: 3

	hcfa_fda:
		model:
			type: 'text'
		view:
			label: 'FDA Therapeutic Equivalency Code'
			readonly: true
			columns: 3

	hcfa_unit:
		model:
			type: 'text'
		view:
			label: 'HCFA Unit'
			readonly: true
			columns: 3

	hcfa_ps:
		model:
			type: 'decimal'
			rounding: 0.001
		view:
			label: 'HCFA Units Per Package'
			readonly: true
			columns: 3

	hcfa_appc:
		model:
			type: 'date'
		view:
			label: 'HCFA FDA Approval Date'
			readonly: true
			columns: 3

	hcfa_mrkc:
		model:
			type: 'date'
		view:
			label: 'HCFA Market Entry Date'
			readonly: true
			columns: 3

	hcfa_trmc:
		model:
			type: 'date'
		view:
			label: 'HCFA Termination Date'
			readonly: true
			columns: 3

	hcfa_typ:
		model:
			type: 'text'
		view:
			label: 'HCFA Drug Type Code'
			readonly: true
			columns: 3

	hcfa_desc1:
		model:
			type: 'date'
		view:
			label: 'HCFA DESI Effective Date'
			readonly: true
			columns: 3

	hcfa_desi1:
		model:
			type: 'text'
		view:
			label: 'HCFA DESI Code'
			readonly: true
			columns: 3

	uu:
		model:
			type: 'text'
		view:
			label: 'Unit of Use'
			readonly: true
			columns: 3

	pd:
		model:
			type: 'text'
		view:
			label: 'Package Description'
			readonly: true
			columns: 3

	ln25:
		model:
			type: 'text'
		view:
			label: 'This column is not currently being used.'
			readonly: true
			offscreen: true
			columns: 3

	ln25i:
		model:
			type: 'text'
		view:
			label: 'Label Name - 25/Generic Name Use'
			readonly: true
			columns: 3

	gpidc:
		model:
			type: 'int'
		view:
			label: 'This column is not currently being used.'
			readonly: true
			offscreen: true
			columns: 3

	bbdc:
		model:
			type: 'int'
		view:
			label: 'This column is not currently being used.'
			readonly: true
			offscreen: true
			columns: 3

	home:
		model:
			type: 'text'
		view:
			label: 'Home Health Selection'
			readonly: true
			columns: 3

	inpcki:
		model:
			type: 'text'
		view:
			label: 'Inner Package'
			readonly: true
			columns: 3

	outpcki:
		model:
			type: 'text'
		view:
			label: 'Outer Package'
			readonly: true
			columns: 3

	obc_exp:
		model:
			type: 'text'
		view:
			label: 'Expanded Orange Book Code'
			readonly: true
			columns: 3

	ps_equiv:
		model:
			type: 'decimal'
			rounding: 0.001
		view:
			label: 'Package Size Equivalent Value'
			readonly: true
			columns: 3

	plblr:
		model:
			type: 'text'
		view:
			label: 'Private Labeler'
			readonly: true
			columns: 3

	top50gen:
		model:
			type: 'text'
		view:
			label: 'Top 50 Generics'
			readonly: true
			columns: 3

	obc3:
		model:
			type: 'text'
		view:
			label: 'Orange Book Code; three-byte version'
			readonly: true
			columns: 3

	gmi:
		model:
			type: 'text'
		view:
			label: 'Generic Manufacturer'
			readonly: true
			columns: 3

	gni:
		model:
			type: 'text'
		view:
			label: 'Generic Name'
			readonly: true
			columns: 3

	gsi:
		model:
			type: 'text'
		view:
			label: 'This column is not currently being used.'
			readonly: true
			offscreen: true
			columns: 3

	gti:
		model:
			type: 'text'
		view:
			label: 'Therapeutic Equivalence'
			readonly: true
			columns: 3

	ndcgi1:
		model:
			type: 'text'
		view:
			label: 'Multi-Source/Single Source Indicator (NDC-Level)'
			readonly: true
			columns: 3

	hcfa_dc:
		model:
			type: 'text'
		view:
			label: 'HCFA Drug Category'
			readonly: true
			columns: 3

	ln60:
		model:
			type: 'text'
		view:
			label: 'Label Name - 60'
			readonly: true
			columns: 3

	formatted_ndc:
		model:
			type: 'text'
		view:
			label: 'Formatted NDC'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: '{ln} {formatted_ndc}'
	indexes:
		unique:[
			['code']
		]
		many: [
			['ndc']
			['gcn_seqno']
			['bn']
			['ln']
			['dea']
			['hcfa_fda']
			['hcfa_unit']
			['hcfa_ps']
		]
	sections:
		'NDC':
			tab: 'Information'
			fields: ['ndc', 'formatted_ndc', 'pndc', 'repndc', 'daddnc', 'dupdc']
		'Drug Information':
			tab: 'Information'
			fields: ['lblrid', 'bn', 'ln', 'ln60', 'df', 'shipper', 'ps','ps_equiv', 'dea']
		'Syringe Description':
			tab: 'Information'
			fields: ['ndl_gdge', 'ndl_lngth', 'syr_cpcty']
		'Indicators':
			tab: 'Indicators'
			fields: ['ndcfi', 'hosp', 'innov', 'ipi', 'mini', 'maint', 'ppi', 'stpk', 'repack', 'top200',
			'ud', 'uu', 'pd', 'ln25i', 'home', 'inpcki', 'outpcki', 'plblr', 'gmi', 'gni', 'gti']
		'Clinical Information':
			tab: 'Indicators'
			fields:['gcn_seqno', 'ad','cl','csp', 'shlf_pck','top50gen','obc', 'obc_exp', 'obc3', 'gpi',
			'obsdtec', 'hcfa_fda', 'ln25','gpidc', 'bbdc', 'gsi', 'ndcgi1']
		'HCFA':
			tab: 'Indicators'
			fields: ['hcfa_unit', 'hcfa_ps', 'hcfa_dc', 'hcfa_typ', 'hcfa_appc', 'hcfa_mrkc', 'hcfa_trmc']
		'DESI':
			tab: 'Indicators'
			fields: ['desi', 'desdtec', 'desi2', 'des2dtec', 'hcfa_desi1', 'hcfa_desc1']

view:
	comment: 'Manage > List FDB NDC'
	find:
		basic: ['ndc', 'bn', 'ndcfi']
	grid:
		fields: ['ndc', 'bn', 'ps', 'df', 'uu', 'ln25i']
	label: 'List FDB NDC'
