fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# General Assessment
	infusion_sites:
		model:
			type: 'decimal'
			rounding: 1
		view:
			label: 'How many infusion sites?'

	have_vaccine:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_vaccine_date', 'have_vaccine_details']
		view:
			control: 'radio'
			label: "Have you received any recent vaccines or plan to receive any vaccines while receiving IG therapy?"

	have_vaccine_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Vaccines Date'

	have_vaccine_details:
		model:
			required: true
		view:
			label: 'Vaccines Detail'

	have_antibiotic:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Are you currently receiving antibiotic therapy or have active infection?"

	have_infection:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Has the patient had any chronic or recent infections?"

	have_seizure:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Do you have a seizure disorder?"

	will_be_pregnant:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Do you plan to get pregnant while on this medication?"

	nurse_present:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will nurse be present for the entire treatment?"

	adult_present:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will an additional adult be present for the entire treatment?"

	adult_present_after:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will an additional adult be present for 2 to 3 hours after treatment is completed?"

	physician_available:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will the ordering physician be available by phone during the treatment?"

	# Training
	requires_training:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			if:
				"No":
					fields:["no_training_reason"]
				"Yes":
					fields:["training_by"]
		view:
			label: 'Will patient need training for SubQ IG administration?'
			control: 'radio'

	no_training_reason:
		model:
			source: {had_subq:"Patient has used SubQ IV before", physician_trained:"Patient has been trained by Physician Office", nurse_trained:"Patient has been trained by other nurse to use SubQ IV", other:"Other"}
			required: true
		view:
			label: 'Why does the patient not require training?'

	training_by:
		model:
			source: {physician:"Physician-office", nurse:"Home Health Nurse", ats:"ATS"}
			required: true
			if:
				nurse:
					fields:['payer_pay_visit']
		view:
			label: 'Who will train the patient?'

	payer_pay_visit:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes', 'NA']
			required: true
		view:
			label: 'Will payer cover additional RN visit hours?'
			control: 'radio'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'SubQ IG General Assessment':
			fields: [ 'infusion_sites', 'have_vaccine', 'have_vaccine_details', 'have_vaccine_date',
			'have_antibiotic', 'have_seizure','will_be_pregnant', 'nurse_present', 'adult_present',
			'adult_present_after', 'physician_available']
		'SubQ IG Training':
			fields: ['requires_training', 'no_training_reason', 'training_by', 'payer_pay_visit']
view:
	comment: 'Patient > Careplan > Assessment > Subq IG'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Assessment Questionnaire: Subq IG'
	open: 'read'
