fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	type:
		model:
			required: true
			source: ['Catheter Infection', 'Infectious Disease']
			if:
				'Infectious Disease':
					fields: ['infectious_disease', 'infectious_disease_confirmed', 'infection_disease_treatment', 'infection_disease_route']
				'Catheter Infection':
					fields: ['line_type', 'infection_date', 'outcome']
		view:
			columns: 3
			control: 'radio'
			label: 'Type'

	line_type:
		model:
			required: true
			source: ["Central", "Peripheral"]
		view:
			columns: 3
			control: 'radio'
			label: 'Line Type'

	infection_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 3
			label: 'Date of reported infection'

	outcome:
		model:
			required: true
			multi: true
			source: ["Hospitalization", "Line removed", "Antibiotics given"]
		view:
			control: 'checkbox'
			label: 'Outcome'

	infectious_disease:
		model:
			multi: true
			required: true
			source: ['Abscess', 'Sepsis', 'C. difficile', 'Fungal/yeast infection', 'Ear Infection'
			'Upper respiratory infection (i.e. rhinitis, Strep throat, common cold, pharyngitis, laryngitis)',
			'Lower respiratory infection (i.e. pneumonia, bronchitis)',
			'COVID-19 infection', 'Influenza', 'Sinus infection', 'Cellulitis',
			'Urinary tract infection', 'GI infection (including viral)',
			'MSSA/MRSA', 'Wound', 'Hepatitis B/C', 'Shingles/Chicken Pox',
			'TB', 'Unknown cause (notate symptoms in comments)', 'Other']
			if:
				'Other':
					fields: ['infectious_disease_other']
		view:
			control: 'checkbox'
			label: 'Infectious Disease'

	infectious_disease_other:
		model:
			required: true
		view:
			label: 'Infectious Disease Other'

	infection_disease_treatment:
		model:
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Infection requires medication?'

	infection_disease_route:
		model:
			source: ['IV', 'Oral']
			if:
				'IV':
					fields: ['pat_med_id']
				'Oral':
					fields: ['pat_med_id']
		view:
			columns: 3
			control: 'radio'
			label: ' Medication Route'

	pat_med_id:
		model:
			source: 'patient_medication'
			sourcefilter:
				patient_id:
					dynamic: '{patient_id}'
				status:
					static: ['Active']
			multi: true
		view:
			control: 'select'
			label: 'Medication'

	infectious_disease_confirmed:
		model:
			required: true
			source: ['Confirmed', 'Unconfirmed']
		view:
			columns: 3
			control: 'radio'
			label: 'Confirmed or unconfirmed illness?'

	comments:
		view:
			control: 'area'
			label: 'Comments'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['physician']
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'type', 'infection_disease_route', 'infectious_disease']
	sections:
		'Infection Control Details':
			fields: ['type', 'line_type', 'infection_date', 'outcome',
			'infectious_disease', 'infectious_disease_other', 'infection_disease_treatment',
			'infection_disease_route', 'infectious_disease_confirmed', 'pat_med_id']
		'Comments':
			fields: ['comments']

	transform_post: [
			name: "AutoNote"
			arguments:
				subject: "Infection Control"
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Infection Control'
	grid:
		fields: ['created_on', 'created_by', 'type', 'outcome', 'infectious_disease', 'pat_med_id', 'comments']
		sort: ['-id']
	label: 'Patient Infection Control'
	open: 'read'
