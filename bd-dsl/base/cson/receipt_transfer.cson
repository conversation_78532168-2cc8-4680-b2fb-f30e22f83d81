fields:

	receipt_no:
		view:
			label: 'Receipt No'
			readonly: true
			offscreen: true
			findunique: true

	receipt_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Receipt Date'
			readonly: true

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			columns: 3
			label: 'From Site'
			readonly: true
			findmulti: true

	to_site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			columns: 3
			label: 'To Site'
			readonly: true
			findmulti: true

	void:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['voided_datetime', 'void_reason_id']
		view:
			columns: 3
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Void Transfer?'
			readonly: true
			validate: [
				{
					name: 'VoidTransfer'
				},
				{
					name: 'PrefillCurrentDateTime'
					condition:
						void: 'Yes'
					dest: 'voided_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						void: 'Yes'
					dest: 'voided_by'
				}
			]

	void_reason_id:
		model:
			required: true
			source: 'list_void_reason'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			label: 'Void Reason'

	voided_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Voided Date'
			readonly: true

	voided_by:
		model:
			source: 'user'
		view:
			label: 'Voided By'
			readonly: true

	subform_items:
		model:
			multi: true
			source: 'receipt_transfer_item'
			type: 'subform'
		view:
			label: 'Items'
			grid:
				add: 'none'
				hide_cardmenu: true
				edit: false
				delete: false
				fields: ['inventory_id', 'serial_id', 'lot_id', 'quantity']
				label: ['Item', 'Serial', 'Lot', 'Quantity']
				width: [35, 25, 25, 15]
			readonly: true

	status:
		model:
			source: ['Pending', 'Accepted', 'Lost', 'Voided']
			default: 'Pending'
		view:
			class: 'status'
			columns: 3
			readonly: true
			label: 'Status'

	status_by:
		model:
			source: 'user'
		view:
			columns: 3
			label: 'Status By'

	status_on:
		model:
			type: 'datetime'
		view:
			columns: 3
			label: 'Status On'

	status_comment:
		view:
			control: 'area'
			label: "Status Comment"

	transfer_type:
		model:
			source: ['Instant Transfer', 'Require Acceptance']
			default: 'Instant Transfer'
		view:
			columns: 3
			label: 'Transfer Type'
model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin','pharm','csr','cm','nurse']
		read_all:   ['admin','pharm','csr','cm','nurse']
		request:    []
		review:     []
		update:     []
		update_all: ['admin','pharm','csr','cm']
		write:      ['admin','pharm','csr','cm']
	bundle: ['inventory']
	indexes:
		many: [
			['site_id']
			['to_site_id']
			['receipt_no']
		]
	name: '{receipt_no} {receipt_date} {site_id} to {to_site_id}'
	sections:
		'Inventory Transfer Receipt':
			fields: ['site_id', 'to_site_id']
		'Inventory Transfer Receipt Item':
			fields: ['subform_items']
		'Transfer Status':
			fields: ['status', 'status_by', 'status_on']
			modal: true
		'Void':
			modal: true
			fields: ['void', 'voided_datetime', 'voided_by', 'void_reason_id']
		'Status Comment':
			hide_header: true
			fields: ['status_comment']
view:
	hide_cardmenu: true
	block:
		update:
			if: 'void'
			except: ['empty']
	comment: 'Inventory Transfer Receipt'
	find:
		basic: ['receipt_no', 'site_id']
	grid:
		fields: ['receipt_date', 'site_id', 'to_site_id']
		sort: ['-id']
	label: 'Inventory Transfer Receipt'
	open: 'edit'