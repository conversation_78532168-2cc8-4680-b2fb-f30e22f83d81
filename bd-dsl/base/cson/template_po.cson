fields:

	name:
		model:
			required: true
			search: 'A'
		view:
			label: 'Name'

	site_id:
		model:
			required: true
			multi: true
			source: 'site'
		view:
			columns: 2
			label: 'Site'

	supplier:
		model:
			required: false
			source: 'list_supplier'
		view:
			columns: 2
			label: 'Supplier'

	subform_items:
		model:
			multi: true
			source: 'po_item'
			type: 'subform'
		view:
			label: 'Items'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				fields: ['inventory_id', 'qty_pkg', 'package_cost', 'item_cost']
				label: ['Item', 'Quantity', 'Pack $', 'Per $']
				width: [55, 15, 15, 15]

	comments:
		view:
			label: 'Comments'
			control: 'area'

	summary:
		view:
			label: 'Items'
			offscreen: true
			readonly: true
model:
	access:
		create:     []
		create_all: ['admin','pharm','csr','cm','physician','nurse']
		delete:     ['admin','pharm','csr','cm','physician','nurse']
		read:       ['admin','pharm','csr','cm','physician','nurse']
		read_all:   ['admin','pharm','csr','cm','physician','nurse']
		request:    []
		review:     ['admin','pharm','csr','cm','physician','nurse']
		update:     []
		update_all: ['admin','pharm','csr','cm','physician','nurse']
		write:      ['admin','pharm','csr','cm','physician','nurse']
	bundle: ['inventory']
	name: '{name}'

	sections_group: [
		'Purchase Order Template':
			hide_header: true
			indent: false
			sections: [
				'Details':
					hide_header: true
					indent: false
					fields: ['name', 'site_id','supplier']
				'Items':
					fields: ['subform_items']
				'Comments':
					fields: ['comments']
			]
	]

view:
	comment: 'Purchase Order Template'
	find:
		basic: ['site_id', 'supplier']
	grid:
		fields: ['created_by','created_on', 'name', 'site_id', 'supplier', 'summary']
		sort: ['-id']
	label: 'Purchase Order Template'
	open: 'edit'