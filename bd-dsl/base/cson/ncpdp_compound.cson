fields:

	charge_no:
		model:
			required: false
			type: 'text'
		view:
			label: 'Charge #'
			readonly: true
			offscreen: true

	product_id:
		model:
			required: true
			source: 'inventory'
			sourcefilter:
				type:
					'static': ['Drug', 'Supply']
				active:
					'static': 'Yes'
				ndc:
					'static': '!null'
		view:
			form_link_enabled: true
			label: 'Selected Ingredient Item'
			readonly: true

	cmp_id_qualifier:
		model:
			default: '03'
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '488-RE'
		view:
			columns: 3
			reference: '488-RE'
			note: '488-RE'
			label: 'Product ID Qualifier'
			readonly: true

	cmp_id:
		model:
			max: 19
			required: true
		view:
			columns: 3
			reference: '489-TE'
			note: '489-TE'
			label: 'Product ID'
			readonly: true

	cmp_ing_qty:
		model:
			type: 'decimal'
			required: true
			max: 9999999.999
			rounding: 0.001
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 3
			reference: '448-ED'
			note: '448-ED'
			label: 'Quantity'
			readonly: true

	cmp_ing_cost:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			if:
				'*':
					fields: ['cmp_ing_cost_basis']
		view:
			columns: 3
			reference: '449-EE'
			note: '449-EE'
			label: 'Cost'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	cmp_ing_cost_basis:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '490-UE'
		view:
			columns: 3
			reference: '490-UE'
			note: '490-UE'
			label: 'Basis of Cost Determination'
			readonly: true

	cmp_ing_md_code:
		model:
			multi: true
			source: 'list_ncpdp_ext_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '363-2H'
		view:
			columns: 3
			max_count: 10
			reference: '363-2H'
			note: '363-2H - Max 10'
			label: 'Compound Ingredient Modifier Code(s)'
			readonly: true
			_meta:
				copy_forward: true
				link: 'product_id'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['cmp_id_qualifier', 'product_id', 'cmp_ing_md_code']
	sections:
		'Compound Ingredient':
			hide_header: true
			indent: false
			fields: ['product_id', 'cmp_id_qualifier', 'cmp_id',
			'cmp_ing_qty', 'cmp_ing_cost', 'cmp_ing_cost_basis',
			'cmp_ing_md_code']

view:
	dimensions:
		width: '50%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Compound Ingredient'
	grid:
		fields: ['product_id', 'cmp_ing_qty', 'cmp_ing_cost', 'cmp_ing_md_code']
		width: [40, 10, 20, 30]
		sort: ['-created_on']
	label: 'Compound Ingredient'
	open: 'read'