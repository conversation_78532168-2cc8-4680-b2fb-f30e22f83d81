fields:

	invoice_no:
		model:
			required: true
			type: 'text'
		view:
			label: 'Invoice #'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/billing_invoice/?limit=1&filter=invoice_no:'
				fields:
					'patient_id': ['patient_id'],
					'site_id': ['site_id'],
					'revenue_accepted_posted': ['revenue_accepted_posted'],
					'claim_no': ['claim_no'],
					'rx_ids': ['rx_id'],
					'master_invoice_no': ['master_invoice_no'],
					'delivery_ticket_id': ['delivery_ticket_id'],
					'insurance_id': ['insurance_id'],
					'payer_id': ['payer_id'],
					'billing_method_id': ['billing_method_id'],
					'invoice_status': ['status'],
			]

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			readonly: true
			offscreen: true

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			readonly: true
			offscreen: true

	claim_no:
		model:
			type: 'text'
		view:
			label: 'Claim No'
			readonly: true
			offscreen: true

	rx_ids:
		model:
			multi: true
			source: 'careplan_order_rx'
		view:
			label: 'Prescriptions'
			readonly: true
			offscreen: true

	rx_id:
		model:
			multi: false
			source: 'careplan_order_rx'
		view:
			label: 'Prescription'
			readonly: true
			offscreen: true
			transform: [
				name: 'SelectPrefill'
				url: '/form/careplan_order_rx/?limit=1&filter=id:'
				fields:
					'rx_no': ['rx_no']
			]
			# TODO: For multiple prescriptions, this will be a selection before adding a charge line

	rx_no:
		model:
			type: 'text'
		view:
			class: 'claim-field'
			label: 'RX Number'
			readonly: true
			offscreen: true

	master_invoice_no:
		model:
			source: 'billing_invoice'
		view:
			label: 'Master Invoice #'
			readonly: true
			offscreen: true

	delivery_ticket_id:
		model:
			source: 'careplan_delivery_tick'
		view:
			label: 'Delivery Ticket'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/careplan_delivery_tick/?limit=1&filter=id:'
				fields:
					'ticket_no': ['ticket_no'],
			]

	revenue_accepted_posted:
		model:
			source: ['Yes']
			if:
				'Yes':
					readonly:
						fields: ['charge_line', 'generic_charge_line', 'mm_charge_line']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Revenue Accepted and Posted?'
			readonly: true
			offscreen: true

	ticket_no:
		model:
			type: 'text'
		view:
			label: 'Ticket #'
			readonly: true
			offscreen: true

	invoice_status:
		model:
			required: false
			default: 'Open'
			source: ['Accepted', 'Open', 'Confirmed', 'Voided', '$0']
			if:
				'Voided':
					readonly:
						fields: ['charge_line', 'generic_charge_line', 'mm_charge_line']
		view:
			control: 'radio'
			label: 'Invoice Status'
			readonly: true
			offscreen: true

	insurance_id:
		model:
			source: 'patient_insurance'
			required: true
		view:
			control: 'select'
			label: 'Insurance'
			readonly: true
			offscreen: true

	payer_id:
		model:
			source: 'payer'
			required: true
		view:
			label: 'Payer'
			readonly: true
			offscreen: true

	billing_method_id:
		model:
			required: true
			source: 'list_billing_method'
			sourceid: 'code'
			if:
				'generic':
					fields: ['generic_charge_line']
				'mm':
					fields: ['mm_charge_line']
				'cms1500':
					fields: ['mm_charge_line']
				'ncpdp':
					fields: ['charge_line']
		view:
			label: 'Billing Method'
			readonly: true
			offscreen: true

	add_new_type_filter:
		model:
			required: true
			source: ['Billable']
			default: ['Billable']
		view:
			label: 'Add New Type Filter'
			readonly: true
			offscreen: true

	generic_charge_line:
		model:
			multi: true
			sourcefilter:
				invoice_no:
					'dynamic': '{invoice_no}'
				patient_id:
					'dynamic': '{patient_id}'
				void:
					'static': '!Yes'
		view:
			embed:
				form: 'ledger_charge_line'
				selectable: false
				add_preset:
					rx_no: '{rx_no}'
					site_id: '{site_id}'
					invoice_status: '{invoice_status}'
					invoice_no: '{invoice_no}'
					ticket_no: '{ticket_no}'
					order_rx_id: '{rx_id}'
					insurance_id: '{insurance_id}'
					payer_id: 1
					billing_method_id: 'generic'
					patient_id: '{patient_id}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				deleteif:
					revenue_accepted_posted: '!Yes'
				label: ['Quantity', 'Description', 'Total Price $', 'Expected $']
				fields: ['bill_quantity', 'description', 'gross_amount_due', 'expected']
				width: [10, 55, 15, 15]
			label: 'Charge Lines'
			transform: [
					{
						name: 'RefreshInvoice'
					}
				]

	mm_charge_line:
		model:
			multi: true
			sourcefilter:
				invoice_no:
					'dynamic': '{invoice_no}'
				patient_id:
					'dynamic': '{patient_id}'
				void:
					'static': '!Yes'
		view:
			embed:
				form: 'ledger_charge_line'
				selectable: false
				add_preset:
					rx_no: '{rx_no}'
					site_id: '{site_id}'
					claim_no: '{claim_no}'
					invoice_status: '{invoice_status}'
					invoice_no: '{invoice_no}'
					ticket_no: '{ticket_no}'
					order_rx_id: '{rx_id}'
					insurance_id: '{insurance_id}'
					payer_id: '{payer_id}'
					billing_method_id: '{billing_method_id}'
					patient_id: '{patient_id}'
					is_dirty: 'Yes'
					inventory_type_filter: '{add_new_type_filter}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				deleteif:
					revenue_accepted_posted: '!Yes'
					is_primary_drug: '!Yes'
				label: ['Item', 'Quantity', 'Exp $', 'Gross Amt $', 'Cost $', 'HCPC']
				fields: ['inventory_id', 'bill_quantity', 'expected', 'gross_amount_due', 'total_cost', 'hcpc_code']
				width: [35, 10, 15, 15, 10, 15]
			label: 'Charge Lines'
			max_count: 50
			note: 'Max 50'
			transform: [
					{
						name: 'RefreshInvoice'
					}
				]

	charge_line:
		model:
			multi: true
			sourcefilter:
				invoice_no:
					'dynamic': '{invoice_no}'
				patient_id:
					'dynamic': '{patient_id}'
				void:
					'static': '!Yes'
		view:
			embed:
				form: 'ledger_charge_line'
				selectable: false
				add_preset:
					rx_no: '{rx_no}'
					site_id: '{site_id}'
					claim_no: '{claim_no}'
					invoice_status: '{invoice_status}'
					invoice_no: '{invoice_no}'
					ticket_no: '{ticket_no}'
					order_rx_id: '{rx_id}'
					insurance_id: '{insurance_id}'
					payer_id: '{payer_id}'
					billing_method_id: '{billing_method_id}'
					patient_id: '{patient_id}'
					is_dirty: 'Yes'
					inventory_type_filter: '{add_new_type_filter}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				deleteif:
					revenue_accepted_posted: '!Yes'
					is_primary_drug: '!Yes'
				label: ['Item', 'Quantity', 'Exp $', 'Gross Amt $', 'Cost $', 'NDC']
				fields: ['inventory_id', 'bill_quantity', 'expected', 'gross_amount_due', 'total_cost', 'formatted_ndc']
				width: [35, 10, 15, 15, 10, 15]
			label: 'Charge Lines'
			max_count: 25
			note: 'Max 25'
			transform: [
					{
						name: 'RefreshInvoice'
					}
				]

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: []
	save: false
	name: 'Invoice Charge Lines'
	sections:
		'Charge Line Group Settings':
			hide_header: true
			indent: false
			fields: ['patient_id', 'invoice_no', 'invoice_status', 'revenue_accepted_posted',
			'delivery_ticket_id', 'ticket_no', 'site_id', 'insurance_id', 'payer_id', 'billing_method_id',
			'claim_no', 'rx_no', 'rx_ids', 'rx_id']
		'Charge Lines':
			hide_header: true
			indent: false
			note: '<i>Associated invoice charge lines. Updates here will automatically apply to the claim/invoice.</i>'
			fields: ['charge_line', 'generic_charge_line', 'mm_charge_line']

view:
	block:
		validate: [
			name: 'InvoiceChargeLinesBlock'
		]
	hide_header: true
	hide_cardmenu: true
	comment: 'Manage > Invoice Charge Lines'
	label: 'Invoice Charge Lines'
	open: 'edit'
