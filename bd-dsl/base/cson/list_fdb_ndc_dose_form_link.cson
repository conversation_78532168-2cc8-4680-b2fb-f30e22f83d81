fields: #RPEIND0_NDC_DF_LINK

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#NDC
	ndc:
		model:
			type: 'text'
		view:
			label: 'National Drug Code'
			columns: 3

	#DOSAGE_FORM_ID
	dosage_form_id:
		model:
			type: 'int'
		view:
			label: 'Dosage Form ID'
			readonly: true
			columns: 2

	#DOSAGE_FORM_TYPE_ID
	dosage_form_type_id:
		model:
			type: 'int'
		view:
			label: 'Dosage Form Type ID'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['ndc', 'dosage_form_id', 'dosage_form_type_id']
	indexes:
		many: [
			['ndc']
			['dosage_form_id']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['ndc', 'dosage_form_id', 'dosage_form_type_id']

view:
	comment: 'Manage > List FDB NDC/Dosage Form Link Table'
	find:
		basic: ['ndc', 'dosage_form_id', 'dosage_form_type_id']
	grid:
		fields: ['ndc', 'dosage_form_id', 'dosage_form_type_id']
	label: 'List FDB NDC/Dosage Form Link Table'
