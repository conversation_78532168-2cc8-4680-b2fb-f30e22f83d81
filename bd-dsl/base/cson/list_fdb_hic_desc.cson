#TABLE: RHICD5_HIC_DESC
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#HIC_SEQN
	hic_seqn:
		model:
			type: 'int'
		view:
			label: 'Hierarchical Ingredient Code Sequence Number'
			readonly: true
			columns: 3

	#HIC
	hic:
		view:
			label: 'Hierarchical Ingredient Code'
			readonly: true
			columns: 3

	#HIC_DESC
	hic_desc:
		view:
			label: 'Hierarchical Ingredient Code Description'
			readonly: true
			columns: 3

	#HIC_ROOT
	hic_root:
		model:
			type: 'int'
		view:
			label: 'Parent HIC4 Sequence Number'
			readonly: true
			columns: 3

	#HIC_POTENTIALLY_INACTV_IND
	hic_potentially_inactv_ind:
		model:
			type: 'int'
		view:
			label: 'Potentially Inactive Indicator'
			readonly: true
			columns: 3

	#ING_STATUS_CD
	ing_status_cd:
		model:
			type: 'int'
		view:
			label: 'Ingredient Status Code'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{hic_desc}"
	indexes:
		many: [
			['hic_seqn']
		]
	sections:
		'Info':
			fields: ['hic_seqn', 'hic', 'hic_desc', 'hic_root', 'hic_potentially_inactv_ind', 'ing_status_cd']

view:
	comment: 'Manage > List FDB Hierarchical Ingredient Code Description Table'
	find:
		basic: ['hic_seqn', 'hic', 'hic_desc']
	grid:
		fields: ['hic_seqn', 'hic', 'hic_desc']
	label: 'List FDB Hierarchical Ingredient Code Description Table'
