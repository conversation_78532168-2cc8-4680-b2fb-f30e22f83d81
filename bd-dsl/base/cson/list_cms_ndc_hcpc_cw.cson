fields:

	# First column, CODE field
	hcpc:
		view:
			columns: 3
			label: 'HCPC'
			findunique: true
			readonly: true

	ndc:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'National Drug Code'

	# BILLUNITS	
	bill_units:
		model:
			type: 'decimal'
			rounding: 0.001
		view:
			columns: 3
			label: 'Bill Units'
			readonly: true

	# BILLUNITSPKG
	package_bill_units:
		model:
			type: 'decimal'
			rounding: 0.001
		view:
			columns: 3
			label: 'Package Bill Units'
			readonly: true

	short_desc:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Shor Description'

	labeler_name:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Labeler Name'

	drug_name:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Drug Name'

	hcpcs_dosage:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'HCPCS Dosage'

	pkg_size:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			columns: 3
			label: 'Package Size'
			readonly: true

	pkg_qty:
		model:
			type: 'int'
		view:
			columns: 3
			label: 'Package Quantity'
			readonly: true

	price_type:
		model:
			source:['ASP', 'AWP', 'OPPS']
		view:
			columns: 3
			control: 'radio'
			label: 'Type'

	conversion_factor:
		model:
			type: 'decimal'
			rounding: 0.001
		view:
			columns: 3
			label: 'Conversion Factor'
			readonly: true

	hcpcs_quantity:
		model:
			type: 'decimal'
			rounding: 0.001
		view:
			columns: 3
			label: 'HCPC Quantity'
			readonly: true

	hcpcs_unit:
		view:
			columns: 3
			label: 'HCPC Unit'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['hcpc', 'bill_units', 'ndc']
	indexes:
		many: [
			['hcpc']
			['bill_units']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['hcpc', 'ndc', 'bill_units', 'package_bill_units',
			'short_desc', 'labeler_name', 'drug_name', 'hcpcs_dosage',
			'pkg_size', 'pkg_qty', 'price_type', 'conversion_factor',
			'hcpcs_quantity', 'hcpcs_unit']

view:
	comment: 'Manage > List CMS HCPC -> Billable Units'
	find:
		basic: ['hcpc']
	grid:
		fields: ['hcpc', 'bill_units', 'package_bill_units']
	label: 'List CMS HCPC -> Billable Units'
