fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_mfis5.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	# Survey Participation
	alert:
		model:
			required: true

			max: 64
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Almost always']
		view:
			control: 'radio'
			label: 'Have been less alert'

	ability:
		model:
			required: true

			max: 64
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Almost always']
		view:
			control: 'radio'
			label: 'Have been limited in my ability to do things away from home'

	effort:
		model:
			required: true

			max: 64
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Almost always']
		view:
			control: 'radio'
			label: 'Have had trouble maintaining physical effort for long periods'

	tasks:
		model:
			required: true

			max: 64
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Almost always']
		view:
			control: 'radio'
			label: 'Have been less able to complete tasks that require physical effort'

	concentrating:
		model:
			required: true

			max: 64
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Almost always']
		view:
			control: 'radio'
			label: 'Have had trouble concentrating'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_mfis5:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Modified Fatigue Impact Scale (MFIS-5)':
			note: 'Following is a list of statements that describe how fatigue may affect a person. Fatigue is a feeling of physical tiredness and lack of energy that many people experience from time to time. In medical conditions like MS, feelings of fatigue can occur more often and have a greater impact than usual. Select the number that best indicates how often fatigue has affected the patient in this way during the past 4 weeks. Please answer every question. If patient is not sure which answer to select, please choose the one answer that comes closest to describing'
			fields: ['last_assessment_date', 'assessment_date']
		'Questionnaire':
			note: "Because of the patient’s fatigue during the past 4 weeks, he/she"
			fields: ['alert', 'ability', 'effort', 'tasks', 'concentrating']
			prefill: 'clinical_mfis5'
view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Modified Fatigue Impact Scale (MFIS-5)'
	find:
		basic: ['assessment_date']
	grid:
		fields: ['created_on', 'assessment_date', 'created_by']
		sort: ['-id']
	label: 'Modified Fatigue Impact Scale (MFIS-5)'
