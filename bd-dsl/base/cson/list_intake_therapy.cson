fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'
			findunique: true
			columns: 3

	name:
		model:
			required: true
			max: 128
		view:
			label: 'Name'
			findunique: true
			columns: 3

	associated_therapy_id:
		model:
			required: true
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Assessments Therapy'
			columns: 3

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 3

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['lists']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['code']
			['name']
		]
	name: '{name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'associated_therapy_id', 'allow_sync', 'active']

view:
	comment: 'Manage > Intake Therapies'
	find:
		basic: ['code', 'name','associated_therapy_id']
	grid:
		fields: ['code', 'name','associated_therapy_id']
		sort: ['code']
	label: 'Intake Therapies'
	open: 'read'
