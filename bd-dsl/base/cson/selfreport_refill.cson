fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	new_meds:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['new_meds_details']
		view:
			control: 'radio'
			label: 'Are you taking any new medications of suppliments since your last refill?'

	new_meds_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'New medications or herbal supplements details:'

	new_allergies_check:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields:['new_allergies']
		view:
			control:'radio'
			label: 'Do you have any new allergies?'

	new_allergies:
		view:
			label: 'New allergies details:'

	#TO-DO Needs to pull forward from central list
	symptoms_list:
		model:
			subfields:
				_meta:
					label: 'Prefilled/Additional'
					type: 'text'
			type: 'json'
		view:
			control: 'grid'
			note: 'Check all disease specific symptoms'
			label: 'Symptoms List'
			offscreen: true

	experiencing_pain:
		model:
			required: true 
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pain_scale']
		view:
			label: 'Are you experiencing any pain?'
			control: 'radio'

	pain_scale:
		model:
			required: true
			source: ['1' ,'2' ,'3' ,'4' ,'5' ,'6' ,'7' ,'8' ,'9' ,'10']
			if:
				'*':
					fields: ['pain_management']
		view:
			control: 'radio'
			label: 'Pain Scale'

	pain_management:
		model:
			required: true
		view:
			label: 'What are you doing to control the pain?'
			control: 'area'

	new_visits:
		model:
			max: 3
			source: ['No','Yes']
			required: true 
			if:
				'Yes':
					fields:['new_visits_details']
		view:
			control:'radio'
			label: 'Any ER Visit/Hospital/Unplanned Physician visits?'

	new_visits_details:
		model:
			required: true 
		view:
			control:'area'
			label: 'AE Details'

	missed_medications:
		model:
			max: 3
			source: ['No', 'Yes']
			required: true
			if:
				'Yes':
					fields: ['missed_med_list', 'missed_medications_reason_list']
		view:
			control: 'radio'
			label: 'Did you missed any doses of your medication?'

	missed_med_list:
		model:
			subfields:
				missed:
					label: 'Missed?'
					type: 'checkbox'
					style:
						width: '10%'
				description:
					label: 'Drug'
					type: 'text'
					style:
						width: '50%'
				comment:
					label: "Comment"
					type: 'text'
					style:
						width: '40%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Which Medications Missed?'

	missed_medications_reason_list:
		model:
			multi: true
			source: ['Could not afford them', 'Forgot to take them', 'Forgot to pick up a refill', "Missed a doctor’s appointment", 'Other']
			if:
				'Other':
					fields: ['missed_medications_reason']
		view:
			control: 'checkbox'
			label: 'Reason missed'

	missed_medications_reason:
		model:
			required: true
		view:
			label: 'Reason Missed Other'

	response_to_therapy:
		model:
			prefill: ['med_refill']
			required: true 
			source: ['Worsening','No Change/Stable','Improvement']
			if:
				'Worsening':
					fields: ['response_to_therapy_rn']
				'Improvement':
					fields: ['response_to_therapy_rn']
		view:
			control:'radio'
			label: 'Response to therapy'

	response_to_therapy_rn:
		model:
			required: true
		view:
			control: 'area'
			label: 'Reason'

	new_events:
		model:
			multi: true
			source: ['ER Visit', 'Hospitalization', 'Unscheduled Physician Visit', 'Adverse Drug Event', 'Any IV infusion related issues?', 'Missed Dose', 'New Medical Condition']
			if:
				'Missed Dose':
					fields: ['missed_med_list', 'new_events_details']
				'*':
					fields: ['new_events_details']
		view:
			control: 'checkbox'
			label: 'Any new medical events?'

	new_events_details:
		model:
			required: true 
		view:
			control: 'area'
			label: 'Details'

	pharmacist_questions:
		model:
			max: 128
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pharmacist_questions_details']
		view:
			control: 'radio'
			label: 'Do you have any questions for the Pharmacist?'

	pharmacist_questions_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Details'
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'nurse', 'patient', 'pharm']
		delete:     ['admin']
		read:       ['admin', 'csr', 'cm', 'nurse', 'patient', 'pharm']
		read_all:   ['admin', 'csr', 'cm', 'nurse', 'patient', 'pharm']
		request:    []
		review:     ['admin', 'nurse', 'pharm','patient']
		update:     []
		update_all: ['admin', 'patient']
		write:      ['admin', 'patient']
	name:['patient_id']
	indexes:
		many: [
			['patient_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
	sections:
		'Questionnaire':
					fields: ['response_to_therapy', 'response_to_therapy_rn', 'new_meds', 'new_meds_details',
					'new_allergies_check', 'new_allergies', 'symptoms_list', 'experiencing_pain', 'pain_scale', 'pain_management',
					'new_events', 'new_events_details', 'missed_med_list','missed_medications_reason_list', 'missed_medications_reason','pharmacist_questions', 'pharmacist_questions_details']
view:
	hide_cardmenu: true
	comment: 'Patient >  Medication Refill SMS'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Medication Refill'
	open: 'edit'
