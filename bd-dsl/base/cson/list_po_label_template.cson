fields:

	name:
		model:
			max: 128
			required: true
		view:
			label: 'Name'
			columns: 2

	#LABELS.directns
	line_directions:
		model:
			required: true
		view:
			label: 'Directions'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line11'
			]

	line11:
		view:
			label: '11.'
			class: 'no-label label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line12'
			]

	line12:
		view:
			label: '12.'
			class: 'no-label label-line groups middle'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'

	active:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			control: 'checkbox'
			label: 'Active?'
			columns: 4
			findfilter: 'Yes'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ['dispense']
	indexes:
		unique: [
			['name', 'active']
		]
	name: ['name', 'line_directions']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name', 'line_directions', 'line11', 'line12', 'allow_sync', 'active']

view:
	comment: 'Manage > PO Label Template'
	find:
		basic: ['name', 'active']
	grid:
		fields: ['name', 'active']
		sort: ['name']
	label: 'PO Label Template'
