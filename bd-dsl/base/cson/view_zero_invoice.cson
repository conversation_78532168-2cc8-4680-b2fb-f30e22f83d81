fields:

	# Links
	invoice_no:
		model:
			required: true
		view:
			readonly: true
			label: 'Invoice #'
			columns: 4

	date_of_service:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'DOS Start'
			readonly: true

	date_of_service_end:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'DOS End'
			readonly: true

	site_id:
		model:
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			columns: 4
			readonly: true

	patient_id:
		model:	
			required: true
			source: 'patient'
		view:
			label: 'Patient'
			columns: 2
			readonly: true 

	insurance_id:
		model:
			source: 'patient_insurance'
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			columns: 2
			control: 'select'
			class: 'select_prefill'
			label: 'Insurance'
			readonly: true

	payer_id:
		model:
			source: 'payer'
			required: true
		view:
			label: 'Payer'
			readonly: true
			offscreen: true

	transfer_insurance_id:
		model:
			source: 'patient_insurance'
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
				payer_id:
					'dynamic': '!{payer_id}'
		view:
			control: 'select'
			label: 'Transfer Balance to Payer'
			class: 'select_prefill'

	charge_line:
		model:
			required: false
			multi: true
			sourcefilter:
				invoice_no:
					'dynamic': '{invoice_no}'
				patient_id:
					'dynamic': '{patient_id}'
				zeroed:
					'static': '!Yes'
				void:
					'static': '!Yes'
		view:
			embed:
				query: 'ar_invoice_charge_lines'
				selectable: true
			grid:
				edit: false
				selectall: true
				rank: 'none'
				add: 'none'
				label: ['Description', 'Quantity', 'Exp $', 'Cost $', 'NDC', 'HCPC']
				fields: ['description', 'quantity', 'expected', 'total_cost', 'formatted_ndc', 'hcpc_code']
				width: [45, 10, 15, 10, 10, 10]
			label: 'Charge Lines'

model:
	save: false
	access:
		create:     []
		create_all: ['admin','pharm', 'cm']
		delete:     []
		read:       ['admin','pharm', 'cm']
		read_all:   ['admin','pharm', 'cm']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      ['admin','pharm', 'cm']

	name: 'Zero Invoice'
	sections_group: [
		'Zero Invoice':
			hide_header: true
			indent: false
			fields: ['invoice_no', 'date_of_service', 'date_of_service_end',
			'site_id', 'patient_id', 'insurance_id', 'payer_id', 'transfer_insurance_id']
		'Charge Lines':
			hide_header: true
			indent: false
			fields: ['charge_line']
	]

view:
	dimensions:
		width: '80%'
		height: '75%'
	hide_cardmenu: true
	comment: 'Zero Invoice'
	grid:
		fields: ['patient_id', 'site_id']
		sort: ['-id']
	label: 'Zero Invoice'
	open: 'edit'