fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	type:
		model:
			source: ['Nurse', 'Agency']
			required: true
			default: 'Agency'
		view:
			columns: 3
			label: 'Type'

	name:
		model:
			max: 64
			required: true
		view:
			columns: 3
			label: 'Name'
			note: 'Letters, numbers, comma, \', -, . only'

	active:
		model:
			source: ['Yes']
			default: 'Yes'
		view:
			columns: 3
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Active'
			findfilter: 'Yes'

	site_id:
		model:
			multi: true
			source: 'site'
			required: true
		view:
			columns: 3
			label: 'Sites'

	calendar_id:
		model:
			source: 'calendar'
			required: false
		view:
			columns: 3
			label: 'Calendar'

	uses_external_nursing_note:
		model:
			required: true
			source: ['No', 'Yes']
			default: 'No'
		view:
			columns: 3
			control: 'radio'
			label: 'Uses External Nursing Note?'

	phone:
		model:
			required: true
			max: 21
		view:
			columns: -3
			format: 'us_phone'
			label: 'Phone'

	fax:
		model:
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Fax'

	email:
		model:
			max: 64
			min: 6
			required: false
		view:
			columns: 3
			label: 'Email Address'
			note: 'Must be a valid email address for Portal Access'
			validate: [
					name: 'EmailValidator'
			]

	address1:
		model:
			max: 128
		view:
			columns: 'addr_1'
			label: 'Address 1'
			class: "api_prefill"
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'address1': ['addressLabel']
					'city': ['city']
					'state_id': ['stateCode']
					'zip': ['postalCode']
			]

	address2:
		model:
			max: 128
		view:
			columns: 'addr_2'
			label: 'Address 2'

	city:
		model:
			type: 'text'
		view:
			columns: 'addr_city'
			label: 'City'

	state_id:
		model:
			required: true 
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'State'

	zip:
		model:
			max: 10
			min: 5
		view:
			columns: 'addr_zip'
			format: 'us_zip'
			label: 'Zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'zip'
						city:'city'
						state:'state'
			]

	npi:
		model:
			max: 10
		view:
			columns: 3
			label: 'NPI'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid NPI, must be 10 digits'
			}]

	ins_expire_date:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Liability Insurance Expiration'

	license_exp_date:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Business License Expiration'

	license:
		model:
			type: 'json'
			subfields:
				state:
					label: 'State'
					source: 'list_us_state'
				license:
					label: 'License #'
					type: 'text'
		view:
			control: 'grid'
			label: 'State License #'

	agency_does_billing:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['rate', 'mileage_rate', 'overtime']
			default: 'No'
		view:
			columns: 3
			control: 'radio'
			label: 'Does agency handle their own billing?'
			note: 'If yes, associated nursing notes will not generate billable charges'

	rate:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			label: 'Hourly Rate ($)'

	mileage_rate:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			label: 'Mileage Rate ($)'

	overtime:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['overtime_threshold', 'overtime_rate']
		view:
			columns: 3
			label: 'Overtime Rate?'
			control: 'checkbox'
			class: 'checkbox-only'

	overtime_threshold:
		model:
			required: true
			type: 'int'
			min: 1
		view:
			columns: 3
			note: 'hours'
			label: 'Overtime Threshold'

	overtime_rate:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			label: 'Overtime Rate ($)'

	subform_contact:
		model:
			type: 'subform'
			source: 'nurse_agency_contact'
			multi: true
		view:
			label: 'Contacts'
			grid:
				edit: true
				add: 'inline'
				fields: ['name', 'phone', 'fax', 'email', 'primary_poc']
				label: ['Name', 'Phone', 'Fax', 'Email', 'Primary']
				width: [30, 20, 20, 20, 10]

	notes:
		view:
			control: 'area'
			label: 'Notes'

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'nurse_agency'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					direct_attachment: 'Yes'
					agency_id: '{code}'
					assigned_to: 'Nursing Agency'
					source: 'Scanned Document'
					form_code: '{code}'
					form_name: 'nurse_agency'
					form_filter: 'nurse_agency'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr','liaison', 'pharm']
		write:      ['admin', 'csr','liaison', 'pharm']
	bundle: ['manage']
	indexes:
		many: [
			['name']
			['npi']
		]
		unique: [
			['code']
		]

	name: '{name} P:{phone} F:{fax}'

	sections_group: [
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'type', 'name', 'active', 'site_id', 'calendar_id', 'uses_external_nursing_note', 'phone', 'fax',
			'email', 'address1', 'address2', 'city', 'state_id', 'zip']
			tab: 'Agency'
		'Credentialing':
			fields: ['npi', 'ins_expire_date', 'license_exp_date', 'license']
			tab: 'Agency'
		'Rates':
			fields: ['agency_does_billing', 'rate', 'mileage_rate','overtime','overtime_threshold','overtime_rate']
			tab: 'Agency'
		'Contacts':
			fields: ['subform_contact']
			tab: 'Agency'
		'Notes':
			fields: ['notes']
			tab: 'Agency'
		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'
		]

view:
	validate: [
		{
			name: "AgencyCalendarCheck"
			fields: [
				"calendar_id",
				"name"
			]
			error: "Selected calendar is already assigned to another nursing agency"
		}
	]
	comment: 'Manage > Nursing Provider'
	find:
		basic: ['name', 'calendar_id', 'site_id', 'active']
	grid:
		fields: ['name', 'calendar_id', 'phone', 'fax', 'rate', 'site_id']
		sort: ['name']
	label: 'Nursing Provider'
