fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'
			findunique: true
			columns: 3

	name:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 3

	snomed_code:
		model:
			required: true
		view:
			label: 'SNOMED Code'
			findunique: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'snomed_code']

view:
	comment: 'Manage > Administration Route'
	find:
		basic: ['code', 'name', 'snomed_code']
	grid:
		fields: ['code', 'name', 'snomed_code']
		sort: ['code']
	label: 'Administration Route'
	open: 'read'
