fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	injection_training:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields:['training_site']
		view:
			control: 'radio'
			label: 'Will patient need Humira injection training/nurse support?'

	training_site:
		model:
			max: 3
			source: {home:"Patient's home", clinical_site:'Clinical site', physician_office:'Physician office'}
		view:
			control: 'radio'
			label: 'MyHumira Nurse to provide education & training for Sub-Q injection at'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Humira Training':
				fields: ['injection_training', 'training_site']
view:
	comment: 'Patient > Careplan > Assessment > Humira'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Assessment Questionnaire: Humira'
	open: 'read'
