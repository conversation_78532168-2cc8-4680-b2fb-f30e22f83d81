#TABLE: RETCNDC0_ETC_NDC
fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	ndc:
		model:
			type: 'text'
		view:
			label: 'National Drug Code'
			columns: 2

	etc_id:
		model:
			type: 'int'
		view:
			label: 'ETC Identifier'
			columns: 2

	etc_common_use_ind:
		model:
			type: 'text'
		view:
			label: 'ETC Common Use Indicator'
			columns: 2

	etc_default_use_ind:
		model:
			type: 'text'
		view:
			label: 'ETC Default Use Indicator'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['ndc', 'etc_id']
	indexes:
		many: [
			['ndc']
			['etc_id']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['ndc', 'etc_id', 'etc_common_use_ind', 'etc_default_use_ind']

view:
	comment: 'Manage > List FDB NDC -> ETCID Crosswalk'
	find:
		basic: ['ndc', 'etc_id']
	grid:
		fields: ['ndc', 'etc_id']
	label: 'List FDB NDC -> ETCID Crosswalk'
