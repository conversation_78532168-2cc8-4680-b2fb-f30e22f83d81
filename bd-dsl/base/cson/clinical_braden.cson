fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_braden.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	# Questionnaire
	pt_particiates:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'Yes'
			if:
				'Yes':
					sections: ['Braden Scale']
					fields: ['assessment_date']
			prefill: ['clinical_braden']
		view:
			findfilter: 'Yes'
			control: 'radio'
			label: 'Will a Braden Scale assessment be completed today?'

	sensory_instructions:
		model:
			multi: true
			source: ['<b>1. COMPLETELY LIMITED</b> - Unresponsive (does not moan, flinch, or grasp) to painful stimuli, due to diminished level of consciousness or sedation, OR limited ability to feel pain over most of body surface.',
			'<b>2. VERY LIMITED</b> - Responds only to painful stimuli. Cannot communicate discomfort except by moaning or restlessness, OR has a sensory impairment which limits the ability to feel pain or discomfort over 1⁄2 of body.',
			'<b>3. SLIGHTLY LIMITED</b> - Responds to verbal commands but cannot always communicate discomfort or need to be turned, OR has some sensory impairment which limits ability to feel pain or discomfort in 1 or 2 extremities.',
			'<b>4. NO IMPAIRMENT</b> - Responds to verbal commands. Has no sensory deficit which would limit ability to feel or voice pain or discomfort.']
		view:
			control: 'checkbox'
			label: 'Sensory Perception Instructions'
			note: 'Ability to respond meaningfully to pressure-related discomfort'
			class: 'list'
			readonly: true

	sensory_score:
		model:
			required: true
			multi:false
			source:
				4: 'NO IMPAIRMENT'
				3: 'SLIGHTLY LIMITED'
				2: 'VERY LIMITED'
				1: 'COMPLETELY LIMITED'
		view:
			control: 'checkbox'
			label: 'Sensory Perception Score'
			validate: [
					name: 'BradenScoreValidate'
			]

	moisture_instructions:
		model:
			multi: true
			source: ['<b>1. CONSTANTLY MOIST</b> - Skin is kept moist almost constantly by perspiration, urine, etc. Dampness is detected every time patient is moved or turned.',
			'<b>2. OFTEN MOIST</b> - Skin is often but not always moist. Linen must be changed at least once a shift.',
			'<b>3. OCCASIONALLY MOIST</b> - Skin is occasionally moist, requiring an extra linen change approximately once a day.',
			'<b>4. RARELY MOIST</b> - Skin is usually dry; linen only requires changing at routine intervals.']
		view:
			control: 'checkbox'
			label: 'Moisture Instructions'
			note: 'Degree to which skin is exposed to moisture'
			class: 'list'
			readonly: true

	moisture_score:
		model:
			required: true
			multi:false
			source:
				4: 'RARELY MOIST'
				3: 'OCCASIONALLY MOIST'
				2: 'OFTEN MOIST'
				1: 'CONSTANTLY MOIST'
		view:
			control: 'checkbox'
			label: 'Moisture Score'
			validate: [
					name: 'BradenScoreValidate'
			]

	activity_instructions:
		model:
			multi: true
			source: ['<b>1. BEDFAST</b> - Confined to bed.',
			'<b>2. CHAIRFAST</b> - Ability to walk severely limited or nonexistent. Cannot bear own weight and/or must be assisted into chair or wheelchair.',
			'<b>3. WALKS OCCASIONALLY</b> - Walks occasionally during day, but for very short distances, with or without assistance. Spends majority of each shift in bed or chair.',
			'<b>4. WALKS FREQUENTLY</b> - Walks outside the room at least twice a day and inside room at least once every 2 hours during waking hours.']
		view:
			control: 'checkbox'
			label: 'Activity Instructions'
			note: 'Degree of physical activity'
			class: 'list'
			readonly: true

	activity_score:
		model:
			required: true
			multi:false
			source:
				4: 'WALKS FREQUENTLY'
				3: 'WALKS OCCASIONALLY'
				2: 'CHAIRFAST'
				1: 'BEDFAST'
		view:
			control: 'checkbox'
			label: 'Activity Score'
			validate: [
					name: 'BradenScoreValidate'
			]

	mobility_instructions:
		model:
			multi: true
			source: ['<b>1. COMPLETELY IMMOBILE</b> - Does not make even slight changes in body or extremity position without assistance.',
			'<b>2. VERY LIMITED</b> - Makes occasional slight changes in body or extremity position but unable to make frequent or significant changes independently.',
			'<b>3. SLIGHTLY LIMITED</b> - Makes frequent though slight changes in body or extremity position independently.',
			'<b>4. NO LIMITATIONS</b> - Makes major and frequent changes in position without assistance.']
		view:
			control: 'checkbox'
			label: 'Mobility Instructions'
			note: 'Ability to change and control body position'
			class: 'list'
			readonly: true

	mobility_score:
		model:
			required: true
			multi:false
			source:
				4: 'NO LIMITATIONS'
				3: 'SLIGHTLY LIMITED'
				2: 'VERY LIMITED'
				1: 'COMPLETELY IMMOBILE'
		view:
			control: 'checkbox'
			label: 'Mobility Score'
			validate: [
					name: 'BradenScoreValidate'
			]

	nutrition_instructions:
		model:
			multi: true
			source: ['<b>1. VERY POOR</b> - Never eats a complete meal. Rarely eats more than 1/3 of any food offered. Eats 2 servings or less of protein (meat or dairy products) per day. Takes fluids poorly. Does not take a liquid dietary supplement, OR is NPO and/or maintained on clear liquids or IV for more than 5 days.',
			'<b>2. PROBABLY INADEQUATE</b> - Rarely eats a complete meal and generally eats only about 1⁄2 of any food offered. Protein Careplan includes only 3 servings of meat or dairy products per day. Occasionally will take a dietary supplement OR receives less than optimum amount of liquid diet or tube feeding.',
			'<b>3. ADEQUATE</b> - Eats over half of most meals. Eats a total of 4 servings of protein (meat, dairy products) each day. Occasionally refuses a meal, but will usually take a supplement if offered, OR is on a tube feeding or TPN regimen, which probably meets most of nutritional needs.',
			'<b>4. EXCELLENT</b> - Eats most of every meal. Never refuses a meal. Usually eats a total of 4 or more servings of meat and dairy products. Occasionally eats between meals. Does not require supplementation.']
		view:
			control: 'checkbox'
			label: 'Nutrition Instructions'
			note: 'Usual food Careplan pattern <br>NPO: Nothing by mouth.<br>IV: Intravenously.<br>TPN: Total parenteral nutrition.'
			class: 'list'
			readonly: true

	nutrition_score:
		model:
			required: true
			multi:false
			source:
				4: 'EXCELLENT'
				3: 'ADEQUATE'
				2: 'PROBABLY INADEQUATE'
				1: 'VERY POOR'
		view:
			control: 'checkbox'
			label: 'Nutrition Score'
			validate: [
					name: 'BradenScoreValidate'
			]

	friction_instructions:
		model:
			multi: true
			source: ['<b>1. PROBLEM</b> - Requires moderate to maximum assistance in moving. Complete lifting without sliding against sheets is impossible. Frequently slides down in bed or chair, requiring frequent repositioning with maximum assistance. Spasticity, contractures, or agitation leads to almost constant friction.',
			'<b>2. POTENTIAL PROBLEM</b> - Moves feebly or requires minimum assistance. During a move, skin probably slides to some extent against sheets, chair, restraints, or other devices. Maintains relatively good position in chair or bed most of the time but occasionally slides down.',
			'<b>3. NO APPARENT PROBLEM</b> - Moves in bed and in chair independently and has sufficient muscle strength to lift up completely during move. Maintains good position in bed or chair at all times.']
		view:
			control: 'checkbox'
			label: 'Friction and Shear Instructions'
			class: 'list'
			readonly: true

	friction_score:
		model:
			required: true
			multi:false
			source:
				3: 'NO APPARENT PROBLEM'
				2: 'POTENTIAL PROBLEM'
				1: 'PROBLEM'
		view:
			control: 'checkbox'
			label: 'Friction and Shear Score'
			validate: [
					name: 'BradenScoreValidate'
			]

	score:
		model:
			rounding: 0.1
			type: 'decimal'
		view:
			note: 'SEVERE RISK: <= 9<br>HIGH RISK: 10-12<br>MODERATE RISK: 13-14<br>MILD RISK 15-18'
			label: 'Braden Scale Score'
			readonly: true

	evaluator_sign:
		model:
			required: true
			type: 'json'
		view:
			control: 'esign'
			label: 'Evaluator E-Signature'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_braden:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Braden Scale':
			fields: ['last_assessment_date', 'assessment_date']
		'Questionnaire':
			note: 'Ask the patient the answer that best describes their usual abilities OVER THE COURSE OF THE LAST WEEK'
			fields: ['sensory_instructions', 'sensory_score', 'moisture_instructions', 'moisture_score',
			 'activity_instructions', 'activity_score', 'mobility_instructions', 'mobility_score',
			 'nutrition_instructions', 'nutrition_score', 'friction_instructions', 'friction_score', 'score', 'evaluator_sign']
			prefill: 'clinical_braden'
view:
	hide_cardmenu: true
	find:
		basic: ['assessment_date']
	grid:
		fields: ['created_on', 'created_by', 'assessment_date', 'score']
		sort: ['-id']
	comment: 'Patient > Careplan > Clinical > Braden Scale'
	label: 'Braden Scale'
