fields:

	pricing_update_no:
		view:
			label: 'Pricing Update No'
			readonly: true
			offscreen: true

	action:
		model:
			source: ['Load Pricing', 'Ignore']
		view:
			control: 'radio'
			columns: 4
			label: 'Action'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						action: 'Load Pricing'
					dest: 'loaded_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						action: 'Load Pricing'
					dest: 'loaded_by'
				}
			]

	loaded_by:
		model:
			source: 'user'
		view:
			label: 'Confirmed By'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'action'
					source_fields: ['loaded_by', 'loaded_datetime']
					separator: ' @ '
				}
			]

	loaded_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Loaded On'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'action'
					source_fields: ['loaded_by', 'loaded_datetime']
					separator: ' @ '
				}
			]

	pricing_update_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Pricing Update Date'
			readonly: true

	last_pricing_update_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Last Pricing Update Date'
			readonly: true

	status:
		model:
			source: ['Pending', 'Loading', 'Completed', 'Error', 'Expired']
			if:
				'Pending':
					fields: ['action']
				'Loading':
					fields: ['started_loading']
				'Completed':
					fields: ['completed_loading', 'total_loading_time']
				'Error':
					fields: ['error_message', 'action']
		view:
			control: 'radio'
			columns: 4
			label: 'Status'
			readonly: true

	started_loading:
		model:
			type: 'datetime'
		view:
			columns: -4
			label: 'Started Loading On'
			readonly: true

	completed_loading:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Completed Loading On'
			readonly: true

	total_loading_time:
		model:
			type: 'decimal'
		view:
			columns: 4
			note: 'Minutes'
			label: 'Total Loading Time'
			readonly: true

	error_message:
		model:
			type: 'text'
		view:
			label: 'Error Message'
			readonly: true

	pricing_updates:
		model:
			multi: true
			sourcefilter:
				pricing_update_no:
					'dynamic': '{pricing_update_no}'
		view:
			embed:
				form: 'inventory_pricing_upt_item'
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['inventory_id', 'prev_awp_price', 'updated_awp_price', 'prev_wac_price', 'updated_wac_price', 'prev_asp_price', 'updated_asp_price']
				label: ['Item', 'Prev Awp Price', 'Updated Awp Price', 'Prev Wac Price', 'Updated Wac Price', 'Prev Asp Price', 'Updated Asp Price']
				width: [28, 12, 12, 12, 12, 12, 12]
				selectall: true
			label: 'Pricing Updates'
			readonly: true 

	contract_updates:
		model:
			multi: true
			sourcefilter:
				pricing_update_no:
					'dynamic': '{pricing_update_no}'
		view:
			embed:
				form: 'inventory_pricing_cont_upt_item'
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['inventory_id', 'prev_expected_price', 'updated_expected_price', 'prev_special_price', 'updated_special_price']
				label: ['Item', 'Prev Expected Price', 'Updated Expected Price', 'Prev Special Price', 'Updated Special Price']
				width: [28, 12, 12, 12, 12]
				selectall: true
			label: 'Contract Updates'
			readonly: true

	updated_ndcs:
		model:
			multi: true
		view:
			embed:
				form: 'inventory_pricing_upt_ndc'
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['inventory_id', 'pndc', 'repndc']
				label: ['Drug', 'Previous NDC', 'Updated NDC']
				width: [60, 20, 20]
			label: 'Updated NDCs'
			columns: 2
			readonly: true

	obsolete_drugs:
		model:
			multi: true
		view:
			embed:
				form: 'inventory_pricing_upt_obs'
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['inventory_id', 'obsdtec']
				label: ['Drug', 'Date Obsolete']
				width: [80, 20]
			label: 'Obsolete Drugs'
			note: 'Will mark inventory item as inactive'
			columns: 2
			readonly: true 

	new_drugs:
		model:
			multi: true
			sourcefilter:
				_x1:
					'dynamic': '{last_pricing_update_date}'
				_x2:
					'dynamic': '{loaded_datetime}'
		view:
			embed:
				query: 'ndc_updates'
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['ln', 'formatted_ndc', 'ps', 'billing_unit_id', 'hcfa_ps', 'hcfa_unit', 'daddnc']
				label: ['Drug', 'Formatted NDC', 'PS', 'Unit', 'HCFA PS', 'HCFA Unit', 'Date Added']
				width: [28, 12, 12, 12, 12, 12, 12]
			label: 'New Drugs'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin','pharm','csr','cm', 'nurse']
		read_all:   ['admin','pharm','csr','cm', 'nurse']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      []
	bundle: ['inventory']
	indexes:
		many: [
			['action'],
			['status']
		]

	name: ['created_on']
	sections_group: [
		'Inventory Pricing Update':
			hide_header: true
			indent: false
			fields: ['pricing_update_no', 'action', 'pricing_update_date', 'last_pricing_update_date', 'status',
			'error_message',  'started_loading', 'completed_loading', 'total_loading_time']
		'Pricing Updates':
			hide_header: true
			indent: false
			fields: ['pricing_updates']
		'Contract Updates':
			hide_header: true
			indent: false
			fields: ['contract_updates']
		'NDC Updates':
			hide_header: true
			indent: false
			fields: ['updated_ndcs', 'obsolete_drugs', 'new_drugs']
	]

view:
	dimensions:
		width: '90%'
		height: '85%'
	block:
		update:
			if: 'action'
			except: ['empty', 'Ignore']
	hide_cardmenu: true
	comment: 'Inventory Pricing Update'
	find:
		basic: ['status', 'action']
		advanced: []
	grid:
		fields: ['created_on', 'action', 'status']
		sort: ['-id']
	label: 'Inventory Pricing Update'
	open: 'edit'