fields:

	# Prefill with current site info
	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			columns: 2
			label: 'Site'
			findmulti: true
			readonly: true
			offscreen: false

	po_id:
		model:
			required: false
			source: 'po'
			sourcefilter:
				site_id:
					'dynamic': '{site_id}'
				status_id:
					'static': 'ORD'
		view:
			columns: 2
			note: 'Select purchase order to pull items into receivables'
			label: 'PO'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/po/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				lock_fields: ['supplier_id']
				fields:
					'supplier_id': ['supplier_id'],
					'subform_lots':
						'type': 'subform'
						'field': 'subform_items'
						'fields':
							'po_id': ['pr.id']
							'site_id': ['pr.site_id']
							'inventory_id': ['sf.inventory_id']
							'quantity': ['sf.total_ordered']
							'cost_each': ['sf.cost_each']
							'lot_tracking': ['sf.lot_tracking']
							'serial_tracking': ['sf.serial_tracking']
							'type': ['sf.type']
			]
			readonly: true

	receipt_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 3
			label: 'Receipt Date'
			template: '{{now}}'

	#TODO Lock supplier to PO supplier
	supplier_id:
		model:
			required: false
			search: 'B'
			source: 'list_supplier'
		view:
			columns: 3
			label: 'Supplier'

	file_attachment:
		model:
			type: 'json'
		view:
			columns: 3
			control: 'file'
			label: 'File Attachment'
			note: 'Max 100MB. Only documents, images, and archives supported.'

	subform_lots:
		model:
			required: true
			multi: true
			source: 'view_inventory_lot'
			type: 'subform'
		view:
			label: 'Lots'
			grid:
				edit: true
				add: 'inline'
				split: true
				copy: ['inventory_id', 'quantity', 'cost_each']
				fields: ['inventory_id', 'lot_no', 'serial_no', 'quantity', 'expiration', 'cost_each']
				label: ['Item', 'Lot', 'Serial', 'Quantity', 'Expiration', 'Cost']
				width: [25, 15, 15, 10, 15, 15]

	raw_barcode:
		model:
			type: 'text'
			max: 160
		view:
			class: 'barcode barcode-continues'
			columns: 1
			control: 'barcode'
			label: 'Scan Barcode'
			validate: [{
				name: 'BarcodeParseValidator'
				fields:
					raw: 'item_barcode'
					gtin: 'item_barcode'
			}]
			transform: [{
				name: 'ContinuousBarcodePO'
				type: 'barcode-continues'
			}]

	item_barcode:
		model:
			type: 'text'
			max: 160
		view:
			label: 'Item Barcode'
			note: 'GS1/GTIN, UPC, or internal'
			readonly: true
			offscreen: true


model:
	save: false
	access:
		create:     []
		create_all: ['admin','pharm','csr','cm']
		delete:     []
		read:       ['admin','pharm','csr','cm']
		read_all:   ['admin','pharm','csr','cm']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      ['admin','pharm','csr','cm']
	name:['po_id']
	sections_group: [
		'Details':
			hide_header: true
			indent: false
			fields: [ 'site_id', 'po_id', 'receipt_date', 'supplier_id', 'file_attachment']
		'Received Lots':
			fields: ['subform_lots']
		'Barcode Scanner':
			hide_header: true
			indent: false
			fields: ['item_barcode', 'raw_barcode']
	]

view:
	hide_cardmenu: true
	comment: 'Inventory Lots'
	grid:
		fields: ['created_by', 'created_on', 'updated_on', 'updated_by']
		sort: ['-id']
	label: 'Inventory Lots'
	open: 'edit'