fields:
	date_of_injury:
		model:
			type: 'date'
			required: true
		view:
			reference: '434-DY'
			note: '434-DY'
			label: 'Date of Injury'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	employer_name:
		model:
			max: 30
		view:
			reference: '315-CF'
			note: '315-CF'
			label: 'Name'

	employer_address:
		model:
			max: 30
		view:
			reference: '316-CG'
			note: '316-CG'
			label: 'Address'
			columns: 'addr_1'
			class: "api_prefill"
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'employer_address': ['addressLabel']
					'employer_city': ['city']
					'employer_state': ['stateCode']
					'employer_zip': ['postalCode']
			]

	employer_city:
		model:
			max: 20
		view:
			columns: 'addr_city'
			reference: '317-CH'
			note: '317-CH'
			label: 'City'

	employer_state:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			reference: '318-CI'
			note: '318-CI'
			label: 'State'

	employer_zip:
		model:
			max: 10
			min: 5
		view:
			columns: 'addr_zip'
			reference: '319-CJ'
			note: '319-CJ'
			format: 'us_zip'
			label: 'Zip'

	employer_phone:
		model:
			max: 21
		view:
			columns: 2
			reference: '320-CK'
			note: '320-CK'
			format: 'us_phone'
			label: 'Phone #'

	employer_contact_name:
		model:
			max: 30
		view:
			columns: 2
			reference: '321-CL'
			note: '321-CL'
			label: 'Contact Name'

	carrier_id:
		model:
			max: 10
		view:
			columns: 3
			reference: '327-CR'
			note: '327-CR'
			label: 'Carrier ID'

	claim_reference_id:
		model:
			max: 30
		view:
			columns: 3
			reference: '435-DZ'
			note: '435-DZ'
			label: 'Carrier ID'

	bill_type_indicator:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '117-TR'
		view:
			columns: 3
			reference: '117-TR'
			note: '117-TR'
			label: 'Billing Entity Type Qualifier'

	pay_to_qualifier:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '118-TS'
			if:
				'*':
					require_fields: ['pay_to_id']
		view:
			columns: 2
			reference: '118-TS'
			note: '118-TS'
			label: 'ID Qualifier'

	pay_to_id:
		model:
			max: 15
			if:
				'*':
					require_fields: ['pay_to_qualifier']
		view:
			columns: 2
			reference: '119-TT'
			note: '119-TT'
			label: 'ID'

	pay_to_name:
		model:
			max: 20
		view:
			reference: '120-TU'
			note: '120-TU'
			label: 'Name'

	pay_to_address:	
		model:
			max: 30
		view:
			columns: 'addr_1'
			reference: '121-TV'
			note: '121-TV'
			label: 'Address'

	pay_to_city:
		model:
			max: 20
		view:
			columns: 'addr_city'
			reference: '122-TW'
			note: '122-TW'
			label: 'City'

	pay_to_state:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			reference: '123-TX'
			note: '123-TX'
			label: 'State'

	pay_to_zip:
		model:
			max: 10
			min: 5
		view:
			columns: 'addr_zip'
			reference: '124-TY'
			note: '124-TY'
			format: 'us_zip'
			label: 'Zip'

	gen_prod_id_qualifier:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '125-TZ'
			if:
				'*':
					require_fields: ['gen_prod_id']
		view:
			columns: 2
			reference: '125-TZ'
			note: '125-TZ'
			label: 'Product ID Qualifier'

	gen_prod_id:
		model:
			max: 19
			if:
				'*':
					require_fields: ['gen_prod_id_qualifier']
		view:
			columns: 2
			reference: '126-UA'
			note: '126-UA'
			label: 'Product ID'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['employer_name', 'claim_reference_id']
	sections:
		'Injury':
			fields: ['date_of_injury']
		
		'Employer':
			fields: ['employer_name', 'employer_address', 'employer_city',
			'employer_state', 'employer_zip', 'employer_phone', 'employer_contact_name']

		'Carrier Info':
			fields: ['carrier_id', 'claim_reference_id', 'bill_type_indicator']

		'Pay To':
			fields: ['pay_to_qualifier', 'pay_to_id', 'pay_to_name', 'pay_to_address', 'pay_to_city', 'pay_to_state', 'pay_to_zip']

		'Generic Equivalent Product':
			fields: ['gen_prod_id_qualifier', 'gen_prod_id']
view:
	dimensions:
		width: '90%'
		height: '85%'
	comment: "Worker's Comp"
	grid:
		fields: ['date_of_injury', 'employer_name', 'carrier_id', 'claim_reference_id', 'bill_type_indicator']
		sort: ['-created_on']
	label: "Worker's Comp"
	open: 'read'