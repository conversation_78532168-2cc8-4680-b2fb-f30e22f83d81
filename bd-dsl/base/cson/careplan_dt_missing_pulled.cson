fields:

	prompt_for_missing_fields:
		model:
			multi: true
			source: ['dispensed_quantity', 'expiration', 'lot_no', 'serial_no']
			if:
				'expiration':
					fields: ['missing_expiration']
				'dispensed_quantity':
					fields: ['dispensed_quantity']
				'lot_no':
					fields: ['missing_lot_no']
				'serial_no':
					fields: ['missing_serial_no']
		view:
			control: 'radio'
			label: 'Prompt for Missing Quantity?'
			readonly: true
			offscreen: true

	dispensed_quantity:
		model:
			required: true
			type: 'decimal'
			rounding: 0.001
		view:
			columns: 2
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Dispense Quantity'

	dispensed_unit_id:
		model:
			source: 'list_unit'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Unit of Measure'
			readonly: true
			transform: [
				{
					name: 'UpdateUnitFieldNote'
					target: ['dispensed_quantity']
				}
			]

	missing_expiration:
		model:
			required: true
			type: 'date'
		view:
			columns: 2
			label: 'Expiration'
			validate: [
				name: 'DateValidator'
				require: 'future'
			]

	missing_lot_no:
		model:
			required: true
			type: 'text'
		view:
			columns: 2
			label: 'Lot No'

	missing_serial_no:
		model:
			required: true
			type: 'text'
		view:
			label: 'Serial No'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']

	reportable: false
	name: ['created_on', 'created_by']

	sections:
		'Scanned Item Data':
			hide_header: true
			modal: true
			fields: ['prompt_for_missing_fields', 'dispensed_quantity', 'dispensed_unit_id',
			'missing_expiration', 'missing_lot_no', 'missing_serial_no']

view:
	comment: 'Patient > Missing Pulled Item Fields'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Missing Pulled Item Fields'
	open: 'read'
