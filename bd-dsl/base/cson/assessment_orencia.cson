fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Orencia specific questions
	orencia_injection_training:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Will patient need Orencia Sub-Q injection training/nurse support?'

	nurse_present:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will nurse be present for the entire infusion?"

	adult_present:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will an additional adult be present for the entire infusion?"

	adult_present_after:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will an additional adult be present for 2 to 3 hours after infusion completed?"

	physician_available:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will the ordering physician be available by phone during the infusion?"

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Orencia Assessment':
				fields: ['orencia_injection_training',
				'nurse_present', 'adult_present',
				'adult_present_after', 'physician_available']
view:
	comment: 'Patient > Careplan > Assessment > Orencia'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Assessment Questionnaire: Orencia'
	open: 'read'
