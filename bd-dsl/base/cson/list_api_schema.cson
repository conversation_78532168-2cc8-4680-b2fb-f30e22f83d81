fields:

	code:
		model:
			max: 64
			required: true
		view:
			columns: 3
			label: 'Path'
			note: 'i.e. /dispense?func=calc_dispense_quantity'

	name:
		model:
			required: true
			max: 128
		view:
			columns: 3
			label: 'Name'
			findunique: true

	type:
		model:
			source: ['POST', 'GET']
			default: 'POST'
		view:
			columns: 3
			label: 'Type'

	description:
		view:
			control: 'area'
			label: 'Description'

	schema:
		model:
			type: 'json'
		view:
			label: 'Schema'
			readonly: true

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 3
			control: 'radio'
			label: 'Can Sync Record'

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 3
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['code']
		]
	name: '{code} - {name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'type', 'description', 'schema', 'allow_sync', 'active']

view:
	comment: 'Manage > API Schema'
	find:
		basic: ['code', 'name', 'type', 'active']
	grid:
		fields: ['code', 'name', 'type', 'description']
		sort: ['code']
	label: 'API Schema'
	open: 'read'
