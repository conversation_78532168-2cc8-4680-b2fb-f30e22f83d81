#TABLE: RROUTED3_ROUTE_DESC
fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#GCRT
	gcrt:
		view:
			label: 'Route of Administration Code (1-character)'
			findunique: true
			columns: 3

	#RT
	rt:
		view:
			label: 'Route Description'
			columns: 3

	#GCRT2
	gcrt2:
		view:
			label: 'Route of Administration Code (2-character)'
			columns: 3

	#GCRT_DESC
	gcrt_desc:
		view:
			label: 'Route of Administration Description'
			columns: 3

	#SYSTEMIC
	systemic:
		view:
			label: 'Systemic Indicator'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['rt']
	indexes:
		many: [
			['gcrt']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['rt', 'gcrt', 'gcrt2', 'gcrt_desc', 'systemic']

view:
	comment: 'Manage > List FDB Route Abbreviation'
	find:
		basic: ['rt']
	grid:
		fields: ['rt', 'gcrt', 'gcrt2', 'gcrt_desc', 'systemic']
	label: 'List FDB Route Abbreviation'
