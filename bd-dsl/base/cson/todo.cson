fields:

	user_id:
		model:
			required: true
			source: "user"
		view:
			label: 'Assigned User'
			columns: 3

	patient_id:
		model:
			required: true
			source: "patient"
		view:
			label: 'Assigned Patient'
			columns: 3

	title:
		model:
			required: true
			type: "text"
		view:
			label: 'Title'
			columns: 3

	description:
		model:
			required: true
		view:
			control: 'area'
			label: 'Description'
			columns: -2

	status:
		model:
			required: true
			type: 'text'
		view:
			class: 'status'
			label: 'Status'
			columns: 3

	due_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Due Date'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	name: '{patient_id_auto_name}'
	sections:
		'Main':
			fields: ['user_id', 'patient_id', 'title', 'status', 'due_date', 'description']

view:
	comment: 'Todo Cards'
	find:
		basic: ['user_id', 'patient_id', 'title', 'status']
	grid:
		fields: ['user_id', 'patient_id', 'title', 'status']
	label: 'Todo Cards'
	open: 'read'
