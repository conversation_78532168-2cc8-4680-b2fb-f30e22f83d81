fields:
	patient_id:
		model:
			type: 'int'
			required: true
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	dx_id:
		model:
			type: 'int',
			required: true
			source: 'patient_diagnosis'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			columns: 3
			label: 'Diagnosis'
			validate: [
				name: 'LoadPatientDX'
			]

	diagnosis_type_code:
		model:
			required: true
			min: 1
			max: 3
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'HI0*'
		view:
			columns: 3
			label: 'Dx Code Type'
			reference: ['HI01-01','HI02-01','HI03-01','HI04-01','HI05-01','HI06-01','HI07-01','HI08-01','HI09-01','HI10-01','HI11-01','HI12-01']
			readonly: true
			_meta:
				location: '2440 HI'
				code: ['ABK', 'ABF']
				field: ['01-01', '02-01', '03-01', '04-01', '05-01', '06-01', '07-01', '08-01', '09-01', '10-01', '11-01', '12-01']
				type: 'index'
				path: 'claimInformation.healthCareCodeInformation[{idx1-12}].diagnosisTypeCode'

	diagnosis_code:
		model:
			required: true
			min: 1
			max: 30
		view:
			columns: 3
			label: 'Dx Code'
			reference: ['HI01-02','HI02-02','HI03-02','HI04-02','HI05-02','HI06-02','HI07-02','HI08-02','HI09-02','HI10-02','HI11-02','HI12-02']
			readonly: true
			_meta:
				location: '2440 HI'
				code: ['ABK', 'ABF']
				field: ['01-02', '02-02', '03-02', '04-02', '05-02', '06-02', '07-02', '08-02', '09-02', '10-02', '11-02', '12-02']
				type: 'index'
				path: 'claimInformation.healthCareCodeInformation[{idx1-12}].diagnosisCode'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		delete:     ['admin', 'pharm', 'patient']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
	indexes:
		many: [
			['patient_id']
			['dx_id']
		]
	name:['dx_id', 'diagnosis_code', 'diagnosis_type_code']
	sections:
		'Diagnosis':
			hide_header: true
			fields: ['patient_id', 'dx_id', 'diagnosis_type_code', 'diagnosis_code']

view:
	dimensions:
		width: '85%'
		height: '55%'
	hide_cardmenu: true
	reference: '2300'
	comment: 'Med Claim Diagnosis'
	grid:
		fields: ['patient_id', 'dx_id', 'diagnosis_type_code', 'diagnosis_code']
		sort: ['-id']
	label: 'Med Claim Diagnosis'
