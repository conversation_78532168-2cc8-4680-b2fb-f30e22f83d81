fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Morse Fall Scale
	score:
		model:
			min: 0
			max: 130
			type: 'int'
		view:
			label: 'Fall Score'
			readonly: true
			columns: 2

	fall_history:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			note: 'Immediate or within 3 months'
			label: 'Does the patient have a history of falling?'
			columns: 2
			validate: [
					name: 'FallScoreValidate'
			]

	secondary_dx:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			note: 'Immediate or within 3 months'
			label: 'Does the patient have a secondary diagnosis?'
			columns: 2
			validate: [
					name: 'FallScoreValidate'
			]

	iv_patient:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			note: 'Immediate or within 3 months'
			label: 'Does the patient have an intravenous apparatus or a heparin lock inserted?'
			columns: 2
			validate: [
					name: 'FallScoreValidate'
			]

	fall_ambulatory:
		model:
			source: {bed: 'Bed rest/nurse assist', crutches: 'Crutches/cane/walker', furniture: 'Furniture'}
		view:
			control: 'radio'
			label: 'Ambulatory Aid'
			columns: 2
			validate: [
					name: 'FallScoreValidate'
			]

	fall_gait:
		model:
			source: {normal: 'Normal/bedrest/immobile', weak: 'Weak', impaired: 'Impaired'}
		view:
			control: 'radio'
			label: 'Gait/Transferring'
			columns: 2
			validate: [
					name: 'FallScoreValidate'
			]

	fall_mental:
		model:
			source: {oriented: 'Oriented to own ability', forgets: 'Forgets limitations'}
		view:
			control: 'radio'
			label: 'Mental status'
			columns: 2
			validate: [
					name: 'FallScoreValidate'
			]

	comment:
		view:
			control: 'area'
			label: 'Comments'

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true
	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: 'Fall Score: {score}'
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		checklist_fall:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Patient Fall Assessment':
			hide_header: true
			indent: false
			note: '0-24 = No Risk - Good basic nursing care\n25-50 = Low Risk - Implement standard fall interventions\n≥50 = High Risk - Implement high-risk interventions'
			fields: ['fall_history', 'secondary_dx', 'iv_patient',
			'fall_ambulatory', 'fall_gait', 'fall_mental', 'score']
			prefill: 'checklist_fall'
		'Comments':
			fields: ['comment']
	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Patient Fall Assessment"
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Checklist > Fall'
	grid:
		fields: ['created_on', 'created_by', 'score']
		sort: ['created_on']
	label: 'Patient Fall Assessment'
	open: 'read'
