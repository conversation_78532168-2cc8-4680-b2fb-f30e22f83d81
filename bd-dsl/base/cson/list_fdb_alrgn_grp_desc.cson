#TABLE: RDAMAGD1_ALRGN_GRP_DESC
fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	dam_alrgn_grp:
		model:
			type: 'int'
		view:
			label: 'DAM Allergen Group'
			columns: 2
	
	dam_alrgn_grp_desc:
		view:
			label: 'DAM Allegren Group Description'
			columns: 2
	
	dam_grp_potentially_inactv_ind:
		model:
			type: 'int'
		view:
			label: 'DAM Group Potentially Inactive Ind'
			columns: 2
	
	dam_alrgn_grp_status_cd:
		model:
			type: 'int'
		view:
			label: 'DAM Allergen Group Status Cd'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		many: [
			['dam_alrgn_grp']
			['dam_alrgn_grp_desc']
		]
	name: ['dam_alrgn_grp_desc']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['dam_alrgn_grp', 'dam_alrgn_grp_desc', 'dam_grp_potentially_inactv_ind', 'dam_alrgn_grp_status_cd']

view:
	comment: 'Manage > List FDB Allergen Group'
	find:
		basic: ['dam_alrgn_grp_desc']
	grid:
		fields: ['dam_alrgn_grp_desc']
	label: 'List FDB Allergen Group'
