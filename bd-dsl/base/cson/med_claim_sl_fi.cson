fields:

    form_type_code:
        model:
            required: true
            min: 2
            max: 2
            source: 'list_med_claim_ecl'
            sourceid: 'code'
            sourcefilter:
                field:
                    'static': 'LQ01'
        view:
            columns: 2
            label: 'Form Type Code'
            reference: 'LQ01'
            readonly: true
            _meta:
                location: '2440 LQ'
                field: '01'
                path: 'claimInformation.serviceLines[{idx1-50}].formIdentification[{idx1-99}].formTypeCode'

    form_identifier:
        model:
            required: true
            min: 1
            max: 30
        view:
            columns: 2
            label: 'Form Industry Code'
            reference: 'LQ02'
            _meta:
                location: '2440 LQ'
                field: '02'
                path: 'claimInformation.serviceLines[{idx1-50}].formIdentification[{idx1-99}].formIdentifier'

model:
    access:
        create:     []
        create_all: ['admin', 'csr', 'pharm']
        delete:     ['admin', 'pharm']
        read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
        read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
        request:    []
        update:     []
        update_all: ['admin', 'csr', 'pharm']
        write:      ['admin', 'csr', 'pharm']
    name: ['form_type_code', 'form_identifier']
    sections:
        'Service Line Form Identification':
            hide_header: true
            fields: ['form_type_code', 'form_identifier']

view:
    dimensions:
        width: '45%'
        height: '45%'
    hide_cardmenu: true
    reference: '2440 LQ'
    comment: 'Service Line Form Identification'
    grid:
        fields: ['form_type_code', 'form_identifier']
        sort: ['-created_on']
    label: 'Service Line Form Identification'
    open: 'read'