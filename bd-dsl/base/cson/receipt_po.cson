fields:

	receipt_no:
		model:
			search: 'A'
		view:
			label: 'Receipt No'
			readonly: true
			offscreen: true
			findunique: true

	receipt_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Receipt Date'
			readonly: true

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			findmulti: true
			readonly: true

	po_id:
		model:
			search: 'B'
			required: false
			source: 'po'
		view:
			label: 'Association PO'
			readonly: true

	supplier_id:
		model:
			search: 'B'
			required: false
			source: 'list_supplier'
		view:
			label: 'Supplier'
			readonly: true

	total_cost:
		model:
			search: 'C'
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			label: 'Total Cost'
			class: 'numeral'
			format:'$0,0.00'
			readonly: true

	receipt_file:
		model:
			type: 'json'
		view:
			control: 'file'
			label: 'File Attachment'
			note: 'Max 100MB. Only documents, images, and archives supported.'
			readonly: true

	subform_items:
		model:
			multi: true
			source: 'receipt_po_item'
			type: 'subform'
		view:
			label: 'Items'
			grid:
				add: 'none'
				edit: false
				fields: ['inventory_id', 'quantity', 'cost_each']
				label: ['Item', 'Quantity', 'Per $']
				width: [50, 20, 30]
			readonly: true

	void:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['voided_datetime', 'void_reason_id']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Void Purchase Order?'
			validate: [
					name: 'VoidPurchaseOrder'
			]

	void_reason_id:
		model:
			required: true
			source: 'list_void_reason'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			label: 'Void Reason'

	voided_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			label: 'Voided Date'
			template: '{{now}}'
model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin','pharm','csr','cm','nurse']
		read_all:   ['admin','pharm','csr','cm','nurse']
		request:    []
		review:     []
		update:     []
		update_all: ['admin','pharm','csr','cm']
		write:      ['admin','pharm','csr','cm']
	bundle: ['inventory']
	indexes:
		many: [
			['site_id']
			['po_id']
			['receipt_no']
		]
	name: ['receipt_no', 'po_id', 'site_id']
	sections_group: [
		'Purchase Order Receipt':
			hide_header: true
			indent: false
			sections: [
				'Details':
					hide_header: true
					indent: false
					fields: ['receipt_no', 'receipt_date', 'site_id', 'po_id', 'supplier_id', 'receipt_file']
				'Items':
					fields: ['subform_items']
				'Total':
					fields: ['total_cost']
				'Void':
					fields: ['void', 'voided_datetime', 'void_reason_id']
			]
	]


view:
	hide_cardmenu: true
	block:
		update:
			if: 'void'
			except: ['empty']
	comment: 'Purchase Order Receipt'
	find:
		basic: ['receipt_no', 'site_id', 'supplier_id', 'po_id']
	grid:
		fields: ['receipt_no', 'receipt_date', 'po_id', 'supplier_id', 'site_id', 'total_cost']
		sort: ['-id']
	label: 'Purchase Order Receipt'
	open: 'edit'