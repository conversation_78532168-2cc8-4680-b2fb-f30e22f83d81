fields:
	# Link
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	name:
		view:
			columns: 1
			label: 'Name'

	relation_id:
		model:
			required: true
			source: 'list_relationship'
		view:
			columns: 2
			label: 'Relation'

	type:
		model:
			source: ['Primary', 'Emergency', 'Both', 'Caregiver', 'Other']
		view:
			columns: 2
			label: 'Type'

	cell:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Cell Phone'
			columns: 3

	home:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Home Phone'
			columns: 3

	work:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Work Phone'
			columns: 3


model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['patient']
		review:     ['admin', 'pharm']
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
			['type']
		]
	sections:
		'Main':
			fields: ['name', 'relation_id', 'type', 'cell', 'home', 'work']
	name: '{name} ({type})'

view:
	dimensions:
		width: '75%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Patient > Contact'
	find:
		basic: ['name', 'relation_id', 'type']
	grid:
		fields: ['name', 'relation_id', 'type', 'cell', 'home', 'work']
	label: 'Patient Contacts'
	open: 'read'
