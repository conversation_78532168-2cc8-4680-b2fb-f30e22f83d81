#TABLE: RMEDMHL0_MED_HICLSEQNO_LINK
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#MED_CONCEPT_ID
	med_concept_id:
		model:
			type: 'int'
		view:
			label: 'MED Concept ID'
			readonly: true
			columns: 3

	#MED_CONCEPT_ID_TYP
	med_concept_id_typ:
		model:
			type: 'int'
		view:
			label: 'MED Concept ID Type'
			readonly: true
			columns: 3

	#HICL_SEQNO
	hicl_seqno:
		model:
			type: 'int'
		view:
			label: 'Ingredient List Identifier (Stable ID)'
			note: '(formerly the Hierarchical Ingredient Code List Sequence Number)'
			readonly: true
			columns: 3

	#MED_CONCEPT_HICL_SRC_CD
	med_concept_hicl_src_cd:
		model:
			type: 'int'
		view:
			label: 'MED Concept HICL_SEQNO Source Code'
			readonly: true
			columns: 3

	#MED_CONCEPT_OBSDATEC
	med_concept_obsdatec:
		model:
			type: 'int'
		view:
			label: 'MED Concept Obsolete Date'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{med_concept_id}"
	indexes:
		many: [
			['med_concept_id_typ']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['med_concept_id', 'med_concept_id_typ', 'hicl_seqno', 'med_concept_hicl_src_cd', 'med_concept_obsdatec']

view:
	comment: 'Manage > List FDB MED MED Concept/HICL_SEQNO Relation Table'
	find:
		basic: ['med_concept_id_typ', 'hicl_seqno', 'med_concept_id']
	grid:
		fields: ['med_concept_id_typ', 'hicl_seqno', 'med_concept_id']
	label: 'List FDB MED MED Concept/HICL_SEQNO Relation Table'
