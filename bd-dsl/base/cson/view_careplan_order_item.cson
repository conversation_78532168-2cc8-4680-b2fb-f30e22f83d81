fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			offscreen: true
			readonly: true

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'
			offscreen: true
			readonly: true

	item_id_type:
		model:
			type: 'text'
			source: ['careplan_orderp_item', 'careplan_order_item']
		view:
			label: 'Item Id Type'
			offscreen: true
			readonly: true

	item_id:
		model:
			type: 'int'
		view:
			label: 'Item Id'
			offscreen: true
			readonly: true

	rx_verified:
		model:
			source: ['Yes']
			if:
				'Yes':
					readonly:
						fields: ['inventory_id', 'frequency_id', 'route_id', 'rx_template_id', 'frequency_type',
						'frequency_weekly', 'allowed_variance']
		view:
			label: 'Rx Verified'
			readonly: true
			offscreen: true

	order_id:
		model:
			type: 'int'
		view:
			label: 'Order Id'
			offscreen: true
			readonly: true

	file_path:
		model:
			required: false
			type: 'json'
		view:
			class: 'media-preview'
			control: 'file'
			label: 'File Attachment'
			note: 'Max 100MB. Only documents, images, and archives supported.'

	document_id:
		model:
			type: 'int'
		view:
			label: 'Document Id'
			readonly: true
			offscreen: true

	rx_no:
		model:
			type: 'text'
		view:
			columns: 2
			label: 'RX Number'
			readonly: true

	dose_str:
		view:
			columns: 2
			label: 'Dose'
			readonly: true

	is_erx:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['ss_description', 'physician_order_id']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Is Surescripts Order?'
			offscreen: true
			readonly: true

	ss_description:
		view:
			columns: 2
			note: 'From original SureScripts Message'
			label: 'Drug Description'
			readonly: true

	physician_order_id:
		model:
			if:
				'*':
					fields: ['sig', 'ss_description']
		view:
			columns: 2
			label: 'Prescriber Order Number'
			note: 'Associated Surescripts Physician Order Number'
			readonly: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
		view:
			columns: 2
			label: 'Drug'
			class: 'select_prefill'
			readonly: true

	formatted_ndc:
		view:
			label: 'NDC'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'inventory_id'
					source_fields: ['manufacturer_id', 'formatted_ndc', 'hcpc_code']
					separator: ' '
				}
			]

	hcpc_code:
		view:
			label: 'HCPC'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'inventory_id'
					source_fields: ['manufacturer_id', 'formatted_ndc', 'hcpc_code']
					separator: ' '
				}
			]

	manufacturer_id:
		model:
			required: false
			source: 'list_manufacturer'
			sourceid: 'code'
		view:
			label: 'Manufacturer'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'inventory_id'
					source_fields: ['manufacturer_id', 'formatted_ndc', 'hcpc_code']
					separator: ' '
				}
			]

	sig:
		view:
			label: 'Sig'
			readonly: true

	requires_infusion_time:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['infuse_length', 'infuse_time', 'infuse_for']
					require_fields: ['infuse_length', 'infuse_time', 'infuse_for']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Requires Infusion Time?'
			readonly: true
			offscreen: true

	requires_therapy_days:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['therapy_days']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Requires Therapy Days?'
			readonly: true
			offscreen: true

	patient_param_required:
		model:
			source: ['Weight (Kg)', 'BSA (m²)', 'Lesions', 'cm² per Lesion', '1.73 m²', 'grams of carbohydrate']
			if:
				'Weight (Kg)':
					fields: ['weight']
				'BSA (m²)':
					fields: ['bsa']
				'Lesions':
					fields: ['lesions']
				'cm² per Lesion':
					fields: ['cm2_per_lesion']
				'1.73 m²':
					fields: ['m2_173']
				'grams of carbohydrate':
					fields: ['grams_of_carbohydrate']
		view:
			control: 'radio'
			label: 'Patient Parameter Required'
			readonly: true
			offscreen: true

	weight:
		model:
			max: 1000
			min: 1
			rounding: 0.01
			type: 'decimal'
		view:
			readonly: true
			columns: 2
			class: 'unit'
			label: 'Confirm Weight (Kg)'
			note: 'E.g.: 78, 78kg, 172lbs'

	bsa:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'BSA m²'
			readonly: true
			columns: 2

	lesions:
		model:
			required: true
			type: 'int'
		view:
			columns: 2
			label: '# Lesions'
			readonly: true

	therapy_days:
		model:
			required: true
			type: 'int'
		view:
			columns: 2
			label: '# Therapy Days'
			readonly: true

	cm2_per_lesion:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'cm² per Lesion'
			columns: 2
			readonly: true

	m2_173:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: '1.73 m²'
			columns: 2
			readonly: true

	grams_of_carbohydrate:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'grams of carbohydrate'
			columns: 2
			readonly: true

	route_id:
		model:
			required: false
			source: 'list_route'
			sourceid: 'code'
			if:
				'IV':
					fields: ['infuse_length']
		view:
			columns: 2
			label: 'Route'
			validate: [
				{
					name: 'SetHasIVOnParent'
				}
			]

	frequency_id:
		model:
			required: false
			source: 'list_frequency'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Frequency'
			class: 'select_prefill'

	frequency_multiplier:
		model:
			type: 'decimal'
			rounding: 1
		view:
			label: 'Multiplier'
			note: 'times per week'
			offscreen: true
			readonly: true

	frequency_label:
		view:
			label: 'Frequency'
			offscreen: true
			readonly: true

	infuse_length:
		model:
			multi: false
			source:
				continuously: 'Continuous'
				intermittently: 'Intermittent'
			if:
				'continuously':
					fields: ['infuse_for', 'infuse_time']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Continuous/Intermittent'

	infuse_for:
		model:
			min: 0
			type: 'decimal'
			rounding: 0.01
			required: false
			if:
				'*':
					require_fields: ['infuse_time', 'infuse_length']
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 2
			label: 'Infuse for:'

	infuse_time:
		model:
			source: 
				hours: 'Hrs'
				minutes: 'Min'
			if:
				'*':
					require_fields: ['infuse_for', 'infuse_length']
		view:
			columns: 2
			control: 'radio'
			label: 'Min/Hrs'

	therapy_id:
		model:
			required: false
			max: 64
			source: 'list_therapy'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Therapy'
			readonly: true

	status_id:
		model:
			required: true
			source: 'list_order_status'
			sourceid: 'code'
			sourcefilter:
				code:
					'static': ['1','2','4', '5']
			if:
				'4':
					fields: ['onhold_reason']
				'2':
					fields: ['discontinued_date']
		view:
			columns: -2
			label: 'Status'
			readonly: true

	onhold_reason:
		model:
			required: true
		view:
			label: 'On-hold Reason'
			columns: 2
			readonly: true

	discontinued_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 2
			label: 'Discontinued Date'
			readonly: true

	rx_template_id:
		model:
			source: 'list_rx_template'
			sourceid: 'code'
			sourcefilter:
				active:
					'static': 'Yes'
				template_type:
					source: ['PO', 'IV', 'Injection', 'Factor', 'Compound', 'TPN']
			required: true
		view:
			readonly: true
			offscreen: true
			label: 'RX Format'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_rx_template/?limit=1&fields=list&sort=name&page_number=0&filter=code:'
				fields:
					'template_type': ['template_type'],
			]

	template_type:
		model:
			required: false
			source: ['PO', 'IV', 'Injection', 'Factor', 'Compound', 'TPN']
			if:
				'Factor':
					prefill:
						frequency_id: 'UAD'
					readonly:
						fields: ['frequency_id']
					fields: [ 'frequency_type']
		view:
			label: 'Template Type'
			control: 'radio'
			transform: [
				{
					name: 'SetDoseLabel'
				}
			]
			readonly: true
			offscreen: true

	allowed_variance:
		model:
			required: true
			type: 'decimal'
			min: 1
			max: 10
			rounding: 1
		view:
			columns: 4
			label: 'Variance %'
			note: 'Max 10%'
			class: 'numeral'
			format: 'percent'

	# Factor Dosing
	frequency_type:
		model:
			required: true
			source: ['PRN', 'Daily', 'Weekly', 'Other']
			if:
				'Weekly':
					fields: ['frequency_weekly']
		view:
			columns: 2
			control: 'radio'
			label: 'Factor Frequency'

	frequency_weekly:
		model:
			required: true
			multi: true
			source:
				sunday: "Sunday"
				monday: "Monday"
				tuesday: "Tuesday"
				wednesday: "Wednesday"
				thursday: "Thursday"
				friday: "Friday"
				saturday: "Saturday"
		view:
			control: 'checkbox'
			label: 'Week Day Frequency'
			columns: 2

	insurance_id:
		model:
			required: false
			source: 'patient_insurance'
		view:
			label: 'Primary Payer'
			readonly: true
			offscreen: true

	drug_pa_id:
		model:
			required: false
			source: 'patient_prior_auth'
			sourcefilter:
				insurance_id:
					'dynamic': '{insurance_id}'
				patient_id:
					'dynamic': '{patient_id}'
				status_id:
					'static': ['5', '1', '2', '3', '4', '8']
				pa_type:
					'static': 'Drug'
		view:
			add_preset:
				insurance_id: '{insurance_id}'
				patient_id: '{patient_id}'
				status_id: '1'
				pa_type: ['Drug']
			form_link_enabled: true
			columns: 2
			label: 'Authorization'

	payer_ids:
		model:
			multi: true
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
				billing_method_id:
					'static': ['ncpdp', 'mm', 'cms1500']
		view:
			embed:
				form: 'patient_insurance'
				selectable: true
			grid:
				fields: ['type_id', 'payer_id', 'effective_date', 'bill_for_denial']
				width: [20, 40, 20, 20]
				rank: 'local'
			label: 'Payers'
			note: 'Payers'

	dx_ids:
		model:
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			embed:
				form: 'patient_diagnosis'
				selectable: true
			grid:
				edit: true
				fields: ['dx_id']
				label: ['Diagnosis']
				rank: 'local'
			max_count: 12
			label: 'Diagnosis'
			note: 'Override Therapy Set'

	comments:
		view:
			columns: 1
			label: 'Comments'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: "Order"

	sections_group: [

		'Prescription File':
			tab: 'Fax'
			hide_header: true
			indent: false
			fields: ['file_path', 'document_id']

		'Order Info':
			tab: 'Order'
			hide_header: true
			indent: false
			fields: ['patient_id', 'careplan_id', 'item_id_type', 'item_id', 'order_id', 'rx_verified', 'rx_no', 'dose_str', 'is_erx', 'ss_description', 'physician_order_id',
			'inventory_id', 'formatted_ndc', 'hcpc_code', 'manufacturer_id', 'sig', 'requires_infusion_time',
			'requires_therapy_days', 'patient_param_required', 'weight', 'bsa', 'lesions', 'cm2_per_lesion',
			'm2_173', 'grams_of_carbohydrate', 'therapy_days', 'patient_param_required',
			'requires_infusion_time', 'requires_therapy_days', 'route_id', 'infuse_length', 'infuse_for', 'infuse_time', 'frequency_type',
			'frequency_weekly', 'frequency_id', 'frequency_multiplier', 'frequency_label', 'drug_pa_id', 'insurance_id']

		'Diagnosis/Billing':
			hide_header: true
			indent: false
			tab: 'Order'
			fields: ['payer_ids', 'dx_ids']

		'Rx Info':
			tab: 'Order'
			hide_header: true
			indent: false
			fields: ['comments', 'status_id', 'onhold_reason', 'discontinued_date', 'therapy_id', 'rx_template_id', 'template_type']

	]

view:
	dimensions:
		width: '90%'
		height: '75%'
	hide_cardmenu: true
	comment: 'Patient > Order'
	grid:
		fields: ['inventory_id', 'status_id']
		sort: ['-created_on']
	find:
		basic: ['inventory_id', 'status_id']
	label: 'Order'
	open: 'edit'
