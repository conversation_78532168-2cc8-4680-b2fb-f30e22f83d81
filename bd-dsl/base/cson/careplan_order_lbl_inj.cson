fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			offscreen: true
			readonly: true
	external_id:
		view:
			label: 'External Id'
			readonly: true

	#LABELS.directns
	line_directions:
		model:
			required: false
		view:
			label: 'Directions'
			class: 'label-line groups top'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line11'
			]
			validate: [{
				name: 'ReverseParentPrefill'
				copy:
					'line_directions': 'rxform_line1'
				}
			]

	#LABELS.line11
	line11:
		view:
			label: '2.'
			class: 'no-label label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line12'
			]
			validate: [{
				name: 'ReverseParentPrefill'
				copy:
					'line11': 'rxform_line2'
				}
			]

	#LABELS.line12
	line12:
		view:
			label: '3.'
			class: 'no-label label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line13'
			]
			validate: [{
				name: 'ReverseParentPrefill'
				copy:
					'line12': 'rxform_line3'
				}
			]

	#LABELS.line13
	line13:
		view:
			label: '4.'
			class: 'no-label label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line14'
			]
			validate: [{
				name: 'ReverseParentPrefill'
				copy:
					'line13': 'rxform_line4'
				}
			]

	#LABELS.line14
	line14:
		model:
			max: 45
		view:
			label: '5.'
			class: 'no-label label-line groups middle'
			validate: [{
				name: 'ReverseParentPrefill'
				copy:
					'line14': 'rxform_line5'
				}
			]

	inits:
		model:
			source: 'list_rph_labels'
			sourceid: 'code'
		view:
			columns: 2
			label: 'RPH Initials'
			class: 'label-line groups bottom'
			validate: [
				{
					name: 'RphPromptForPassword'
				}
			]

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['patient_id']
	sections:
		'Injection Label':
			hide_header: true
			indent: false
			fields: ['line_directions', 'line11', 'line12', 'line13', 'line14', 'inits']

view:
	comment: 'Manage > Injection Label'
	find:
		basic: ['patient_id']
	grid:
		fields: ['patient_id']
		sort: ['created_on']
	label: 'Syringe Label'
