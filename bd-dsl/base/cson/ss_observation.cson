fields:

	#Measurement.VitalSign
	type_id:
		model:
			source: 'list_loinc_code'
			sourceid: 'code'
		view:
			label: 'Type'
			readonly: true

	#Measurement.LOINCVersion
	loinc_version:
		view:
			label: 'LOINC Version'
			readonly: true
			offscreen: true

	#Measurement.Value
	value:
		model:
			type: 'decimal'
		view:
			columns: 3
			label: 'Value'
			readonly: true

	#Measurement.UnitOfMeasure
	unit_id:
		model:
			source: 'list_ucum_code'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Unit'
			readonly: true

	#Measurement.UCUMVersion
	ucum_version:
		view:
			label: 'UCUM Version'
			readonly: true
			offscreen: true

	#Measurement.ObservationDate.Date
	observation_date:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Observation Date'
			readonly: true

	#ObservationNotes
	notes:
		model:
			type: 'text'
		view:
			label: 'Notes'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: ['type_id', 'value', 'unit_id']
	indexes:
		many: [
			['type_id']
			['unit_id']
		]
	sections:
		'Observation':
			fields: ['observation_date', 'type_id', 'loinc_version', 'ucum_version', 'value', 'unit_id', 'notes']
view:
	comment: 'SureScripts Observation'
	grid:
		fields: ['observation_date', 'type_id', 'value', 'unit_id', 'notes']
		sort: ['-created_on']
	label: 'Observation'
	open: 'read'
