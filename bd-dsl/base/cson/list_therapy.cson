fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'
			findunique: true
			columns: 3
			transform: [
					name: 'LowerCase'
			]

	name:
		model:
			max: 128
		view:
			label: 'Name'
			findunique: true
			columns: 3

	type:
		model:
			required: true
			source:
				speciality: 'Speciality'
				traditional: 'Traditional'
				dme: 'DME'
		view:
			control: 'radio'
			label: 'Type'
			columns: 3

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 2
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 2

	assessment_rules:
		model:
			multi: true
			source: 'list_therapy_asmt_rule'
			type: 'subform'
		view:
			label: 'Assessment Rules'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['code', 'dx_id', 'route_id', 'brand_name_id']
				label: ['Override Code', 'Diagnosis', 'Route', 'Drug Brand']
				width: [25, 25, 25, 25]

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['lists']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['code']
			['name']
		]
	name: '{name}'
	sections_group: [
		'Therapy':
			fields: ['code', 'name', 'type', 'allow_sync', 'active']
		'Assessment Rules':
			fields: ['assessment_rules']
	]
view:
	comment: 'Manage > Therapies'
	find:
		basic: ['code', 'name']
	grid:
		fields: ['code', 'name']
		sort: ['code']
	label: 'Therapies'
	open: 'read'
