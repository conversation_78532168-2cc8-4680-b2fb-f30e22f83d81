#TABLE RPEIGS0_GCNSEQNO_STR_LINK
fields:
    code:
        model:
            max: 64
            required: true
        view:
            label: 'Code'

    #GCN_SEQNO
    gcn_seqno:
        model:
            type: 'int'
        view:
            label: 'GCN Sequence Number'
            findunique: true
            readonly: true
            columns: 3

    #HIC_SEQN
    hic_seqn:
        model:
            type: 'int'
        view:
            label: 'HIC Sequence Number'
            findunique: true
            readonly: true
            columns: 3

    #STR_CONC_TYPE_ID
    str_conc_type_id:
        model:
            type: 'int'
        view:
            label: 'Strength Concentration Type ID'
            findunique: true
            readonly: true
            columns: 3

    #STR_SEQ
    str_seq:
        model:
            type: 'int'
        view:
            label: 'Strength Sequence'
            columns: 3

    #STRENGTH_STATUS_CODE
    strength_status_code:
        model:
            type: 'int'
        view:
            label: 'Strength Status Code'
            columns: 3

    #INGREDIENT_STR
    ingredient_str:
        model:
            type: 'decimal'
            rounding: 0.000001
        view:
            label: 'Ingredient Strength'
            columns: 3

    #INGREDIENT_UOM_MSTR_ID
    ingredient_uom_mstr_id:
        model:
            type: 'int'
        view:
            label: 'Ingredient UOM Master ID'
            columns: 3

    #STRENGTH_TYP_CODE
    strength_typ_code:
        model:
            type: 'int'
        view:
            label: 'Strength Type Code'
            columns: 3

    #VOLUME
    volume:
        model:
            type: 'decimal'
            rounding: 0.000001
        view:
            label: 'Volume'
            columns: 3

    #VOLUME_UOM_MSTR_ID
    volume_uom_mstr_id:
        model:
            type: 'int'
        view:
            label: 'Volume UOM Master ID'
            columns: 3

    #ALT_STR
    alt_str:
        model:
            type: 'decimal'
            rounding: 0.000001
        view:
            label: 'Alternative Strength'
            columns: 3

    #ALT_STR_UOM_MSTR_ID
    alt_str_uom_mstr_id:
        model:
            type: 'int'
        view:
            label: 'Alternative Strength UOM Master ID'
            columns: 3

    #ALT_STRENGTH_TYP_CODE
    alt_strength_typ_code:
        model:
            type: 'int'
        view:
            label: 'Alternative Strength Type Code'
            columns: 3

    #TIME_VALUE
    time_value:
        model:
            type: 'time'
            rounding: 0.001
        view:
            label: 'Time Value'
            columns: 3

    #TIME_UOM_MSTR_ID
    time_uom_mstr_id:
        model:
            type: 'int'
        view:
            label: 'Time UOM Master ID'
            columns: 3

    #RANGE_MAX
    range_max:
        model:
            type: 'decimal'
            rounding: 0.000001
        view:
            label: 'Range Maximum'
            columns: 3

    #RANGE_MIN
    range_min:
        model:
            type: 'decimal'
            rounding: 0.000001
        view:
            label: 'Range Minimum'
            columns: 3

    #DOSAGE_FORM_ATTRIBUTE_ID
    dosage_form_attribute_id:
        model:
            type: 'int'
        view:
            label: 'Dosage Form Attribute ID'
            columns: 3

    #INGREDIENT_SORT_ORDER
    ingredient_sort_order:
        model:
            type: 'int'
        view:
            label: 'Ingredient Sort Order'
            columns: 3

    #LINK_INACTIVE_DATE
    link_inactive_date:
        model:
            type: 'date'
        view:
            label: 'Link Inactive Date'
            columns: 3

model:
    access:
        create:     []
        create_all: ['admin']
        delete:     ['admin']
        read:       ['admin']
        read_all:   ['admin']
        request:    []
        update:     []
        update_all: ['admin']
        write:      ['admin']
    bundle: ['reference']
    sync_mode: 'full'
    name: "{gcn_seqno} - {hic_seqn} - {str_conc_type_id}"
    indexes:
        many: [
            ['gcn_seqno']
            ['hic_seqn']
            ['str_conc_type_id']
            ['ingredient_uom_mstr_id']
            ['volume_uom_mstr_id']
            ['alt_str_uom_mstr_id']
            ['time_uom_mstr_id']
            ['dosage_form_attribute_id']
        ]
    sections:
        'Details':
            hide_header: true
            indent: false
            fields: [
                'gcn_seqno', 'hic_seqn', 'str_conc_type_id', 'str_seq',
                'strength_status_code', 'ingredient_str', 'ingredient_uom_mstr_id',
                'strength_typ_code', 'volume', 'volume_uom_mstr_id', 'alt_str',
                'alt_str_uom_mstr_id', 'alt_strength_typ_code', 'time_value',
                'time_uom_mstr_id', 'range_max', 'range_min',
                'dosage_form_attribute_id', 'ingredient_sort_order',
                'link_inactive_date'
            ]

view:
    comment: 'Manage > List FDB GCN Sequence Number Strength Link'
    find:
        basic: ['gcn_seqno', 'hic_seqn', 'str_conc_type_id']
    grid:
        fields: ['gcn_seqno', 'hic_seqn', 'str_conc_type_id', 'ingredient_str', 'volume']
    label: 'List FDB GCN Sequence Number Strength Link'