fields:

	rsn_for_svc:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '439-E4'
		view:
			columns: 3
			note: '439-E4'
			label: 'Rsn for Svc Code'
			readonly: true

	clin_sig_code:
		model:
			multi: false
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '528-FS'
		view:
			columns: 3
			note: '528-FS'
			label: 'Clin Sig Code'
			readonly: true

	opharmacy_indicator:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '529-FT'
		view:
			columns: 3
			note: '529-FT'
			label: 'Oth Pharm Indic'
			readonly: true

	ofill_date:
		model:
			type: 'date'
		view:
			columns: 3
			note: '530-FU'
			label: 'Prev Date of Fill'
			readonly: true

	prev_fill_qty:
		model:
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 3
			note: '531-FV'
			label: 'Quantity Dispensed'
			readonly: true

	db_indicator:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '532-FW'
		view:
			columns: 3
			note: '532-FW'
			label: 'Database Ind'
			readonly: true

	opresc_indicator:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '533-FX'
		view:
			columns: 3
			note: '533-FX'
			label: 'Oth Prescriber Ind'
			readonly: true

	dur_free_text:
		view:
			note: '544-FY'
			label: 'DUR Msg'
			control: 'area'
			readonly: true

	dur_add_text:
		view:
			note: '570-NS'
			label: 'DUR Add Msg'
			control: 'area'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['rsn_for_svc', 'clin_sig_code', 'opharmacy_indicator']
	sections:
		'DUR/PPS':
			hide_header: true
			indent: false
			fields: ['rsn_for_svc', 'clin_sig_code', 'opharmacy_indicator',
			'ofill_date', 'prev_fill_qty', 'db_indicator', 'opresc_indicator',
			'dur_free_text', 'dur_add_text']

view:
	dimensions:
		width: '75%'
		height: '75%'
	hide_cardmenu: true
	comment: 'Response DUR/PPS'
	grid:
		fields: ['rsn_for_svc', 'clin_sig_code', 'ofill_date', 'prev_fill_qty', 'dur_free_text']
		width: [15, 15, 15, 15, 40]
		sort: ['-created_on']
	label: 'Response DUR/PPS'
	open: 'read'