#TABLE: RDAMXHC0_HIC_ALRGN_XSENSE_LINK
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#HIC_SEQN
	hic_seqn:
		model:
			type: 'int'
		view:
			label: 'Hierarchical Ingredient Code Sequence Number'
			readonly: true
			columns: 2

	#dam_alrgn_xsense
	dam_alrgn_xsense:
		model:
			type: 'int'
		view:
			label: 'DAM Specific Allergen Group Code (Stable ID)'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{hic_seqn} {dam_alrgn_xsense}"
	indexes:
		many: [
			['hic_seqn']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['hic_seqn', 'dam_alrgn_xsense']

view:
	comment: 'Manage > List FDB DAM Ingredient/Cross-Sensitivity Link Table'
	find:
		basic: ['hic_seqn', 'dam_alrgn_xsense']
	grid:
		fields: ['hic_seqn', 'dam_alrgn_xsense']
	label: 'List FDB DAM Ingredient/Cross-Sensitivity Link Table'
