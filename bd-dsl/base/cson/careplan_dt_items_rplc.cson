fields:

	delivery_ticket_id:
		model:
			required: true
			type: 'int'
		view:
			label: 'Delivery Ticket Id'
			readonly: true
			offscreen: true

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
	
	ticket_no:
		model:
			required: false
			type: 'text'
		view:
			label: 'Ticket #'
			note: 'Generated at the time of save'
			readonly: true
			offscreen: true

	subform_original_items:
		model:
			required: false
			multi: true
			source: 'original_dt_items'
			type: 'subform'
		view:
			label: 'Original Delivery Ticket Items'
			grid:
				edit: false
				add: 'none'
				split: true
				copy: ['drug', 'hcpc_code', 'dispense_quantity', 'ndc_code']
				fields: ['drug', 'hcpc_code', 'dispense_quantity', 'ndc_code']
				label: ['Item', 'HCPC', 'Quantity', 'NDC']
				width: [25, 25, 25, 25]
	
	subform_replaced_items:
		model:
			required: false
			multi: true
			source: 'replaced_dt_items'
			type: 'subform'
		view:
			label: 'Replaced Delivery Ticket Items'
			grid:
				edit: true
				add: 'none'
				split: true
				copy: ['drug', 'inventory_id','hcpc_code', 'dispense_quantity', 'formatted_ndc', 'replacement_reason_id' ]
				fields: ['drug', 'inventory_id','hcpc_code', 'dispense_quantity', 'formatted_ndc', 'replacement_reason_id' ]
				label: ['Original Drug', 'Replaced Drug', 'HCPC', 'Quantity', 'NDC', 'Replacement Reason']
				width: [20, 20, 10, 20, 10, 20]

	comment:
		model:
			required: false
		view:
			label: 'Comment'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     []
		read:       ['admin','pharm','csr','cm', 'nurse']
		read_all:   ['admin','pharm','csr','cm', 'nurse']
		request:    []
		review:     ['admin','pharm','csr','cm', 'nurse']
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['patient_id']
		]
	name: ['patient_id']
	sections:
		'Delivery Ticket Replacement Log':
			hide_header: true
			indent: false
			fields: ['ticket_no', 'delivery_ticket_id']
		'Original Items':
			fields: ['subform_original_items']
		'Replaced Items':
			fields: ['subform_replaced_items']

view:
	hide_cardmenu: true
	comment: 'Delivery Ticket Replacement Log'
	grid:
		fields: ['ticket_no']
		sort: ['-id']
	label: 'Drug Items Replacement Log'
	open: 'edit'