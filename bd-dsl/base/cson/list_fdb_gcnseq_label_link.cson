#TABLE RLBLWGC0_GCNSEQNO_LINK
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	# GCN_SEQNO
	gcn_seqno:
		model:
			type: 'int'
		view:
			label: 'GCN Sequence Number'
			readonly: true
			findunique: true
			columns: 3

	# LBL_WARN
	lbl_warn:
		view:
			label: 'Prioritized Label Warning Code'
			readonly: true
			columns: 3

	# LBL_PRTY
	lbl_prty:
		model:
			type: 'int'
		view:
			label: 'Prioritized Label Warning Relative Priority'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['gcn_seqno', 'lbl_warn', 'lbl_prty']
	indexes:
		many: [
			['gcn_seqno']
			['lbl_warn']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['gcn_seqno', 'lbl_warn', 'lbl_prty']

view:
	comment: 'Manage > List FDB GCN Label Link'
	find:
		basic: ['gcn_seqno']
	grid:
		fields: ['gcn_seqno', 'lbl_warn', 'lbl_prty']
	label: 'List FDB GCN Label Link'
