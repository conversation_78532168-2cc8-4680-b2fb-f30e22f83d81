fields:

	name:
		model:
			required: true
		view:
			label: 'Name'
			validate: [
				{
					name: 'UpdateTabLabel'
					fields: ['name']
				}
			]
			findunique: true
			columns: 3

	site_id:
		model:
			source: 'site'
			multi: true
		view:
			label: 'Assigned Site(s)'

	account_no:
		view:
			label: 'Account #'
			findunique: true

	phone:
		model:
			required: true
			max: 21
		view:
			format: 'us_phone'
			label: 'Phone'
			columns: 3

	fax:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Fax'
			columns: 3

	street:
		view:
			label: 'Street 1'
			columns: 'addr_1'
			class: "api_prefill"
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'street': ['addressLabel']
					'city': ['city']
					'state_id': ['stateCode']
					'zip': ['postalCode']
					'county': ['county']
			]

	street2:
		view:
			label: 'Street 2'
			columns: 'addr_2'

	city:
		view:
			label: 'City'
			columns: 'addr_city'

	county:
		view:
			label: 'County'
			columns: 'addr_county'

	state_id:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			label: 'State'
			columns: 'addr_state'

	zip:
		model:
			max: 10
			min: 5
		view:
			format: 'us_zip'
			label: 'Zip'
			columns: 'addr_zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'zip'
						city:'city'
						state:'state_id'
			]

	notes:
		view:
			control: 'area'
			label: 'Notes'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'pharm']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'pharm', 'physician']
		request:    []
		update:     ['nurse']
		update_all: ['admin','csr', 'liaison', 'pharm']
		write:      ['admin','csr', 'liaison', 'pharm']
	indexes:
		unique: [
			['name', 'account_no']
		]
	name: ['name']
	bundle: ['lists']
	sections:
		'Details':
			fields: ['name', 'phone', 'fax']
		'Account Details':
			fields: ['site_id', 'account_no']
		'Address':
			fields: ['street', 'street2', 'city', 'state_id', 'zip', 'county']
		'Notes':
			fields: ['notes']

view:
	comment: 'Courier Service'
	find:
		basic: ['name', 'phone']
	grid:
		fields: ['name', 'phone', 'fax', 'notes']
		sort: ['name']
	label: 'Courier Service'
	open: 'read'
