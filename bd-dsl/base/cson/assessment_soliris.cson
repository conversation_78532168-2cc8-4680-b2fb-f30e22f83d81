fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	meningitis_vaccinated:
		model:
			source: ['No', 'Yes']
			required: true
			if:
				'No':
					fields: ['meningitis_vaccinated_plan']
		view:
			label: 'Have you been vaccinated against meningitis?'
			control: 'radio'

	meningitis_vaccinated_plan:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					note: 'Pharmacist, please contact physician'
			required: true
		view:
			label: 'Do you plan on getting vaccinated prior to the start of infusion?'
			control: 'radio'

	meningococcal_symptoms:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Do you understand if you experience any of these symptoms to report to your nearest emergency room?'
			control: 'radio'

	rems_card:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Have you received the Soliris patient safety card (REMS drug)?'
			control: 'radio'

	fungal_history:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['fungal_history_details']
			required: true
		view:
			label: 'Do you have a history of fungal infections?'
			control: 'radio'

	fungal_exposure:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Any recent exposure to fungus or travel to areas where fungal infections are more prevalent?'
			control: 'radio'
	 
	fungal_history_details:
		model:
			required: true
		view:
			label: 'Details'
			control: 'area'

	vaccinations:
		model:
			source: ['No', 'Yes', 'Unknown']
			if:
				'Unknown':
					note: 'It is important to be up to date on all vaccinations with Soliris. This medication can increase your susceptibility to infections.'
				'No':
					note: 'It is important to be up to date on all vaccinations with Soliris. This medication can increase your susceptibility to infections.'
			required: true
		view:
			label: 'Are you up to date on all your vaccinations?'
			control: 'radio'

	contraception:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			note: 'This medication can cause fetal abnormalities, it is important not to get pregnant during the course of treatment'
			label: 'Do you have a plan for contraception?'
			control: 'radio'

	storage:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			note: 'Medication needs to be refrigerated and protected from light until the nurse arrives to infuse you'
			label: 'Went over storage of medication?'
			control: 'radio'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'csr', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Meningitis Vaccination':
			note: 'There have been people who have fatal infections from meningitis when taking Soliris so it is very important to receive this vaccination at least 2 weeks prior to Soliris infusion'
			fields: ['meningitis_vaccinated', 'meningitis_vaccinated_plan']
		'Meningococcal Infections':
			note: 'Signs and symptoms of meningococcal infections include the following: Headache with N/V or a fever, Headache with a stiff neck, fever and a rash, confusion, muscle aches with flu-like symptoms, eyes sensitive to light'
			fields: ['meningococcal_symptoms']
		'Soliris Assessment':
			note: 'Soliris is generally well tolerated but there is a risk of serious adverse events that can happen. Make sure to report to your nurse any symptoms or feelings you have during the infusion. The most common adverse reaction in patients who use this medication for MG is muscle and joint pain'
			fields: ['rems_card', 'fungal_history', 'fungal_history_details', 'fungal_exposure', 'vaccinations', 'contraception', 'storage']
view:
	comment: 'Patient > Careplan > Assessment > Soliris'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Soliris'
	open: 'read'
