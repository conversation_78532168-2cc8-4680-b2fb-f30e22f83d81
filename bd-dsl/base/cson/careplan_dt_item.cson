fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	status:
		model:
			source:
				delivery_ticket: 'Delivery Ticket Creation' # This is a new delivery ticket, not associated with a claim
				ready_to_fill: 'Ready to Fill' # This is a delivery ticket that is ready to be filled and prescription has been verified or started from a refill request
				order_ver: 'Pending Order Verification' # This is a delivery ticket that has been filled and is await pharmacist verification
				pending_conf: 'Pending Confirmation' # This is a delivery ticket that has been filled and is await pharmacist confirmation
				ready_to_bill: 'Ready to Bill' # This is a delivery ticket that has been confirmed by the pharmacist and is ready to be billed
				billed: 'Billed' # This is a delivery ticket that has been billed for all charges on the ticket
				voided: 'Voided' # This is a delivery ticket that has been voided
			if:
				'ready_to_fill':
					fields: ['needs_scanned', 'total_cost']
				'order_ver':
					fields: ['needs_scanned', 'total_cost']
				'pending_conf':
					fields: ['needs_scanned', 'total_cost']
					readonly:
						fields: ['print', 'bill', 'inventory_id', 'part_of_kit', 'rental_type', 'is_340b',
						'frequency_code', 'day_supply', 'dispense_quantity',
						'expected_ea', 'list_ea', 'billed_ea', 'copay', 'include_cmpd_instr_wo', 'copy_changes_to_rx', 'last_through_date',
						'compounding_instructions', 'med_review_wo', 'weight', 'height', 'doses_prepared', 'containers_prepared', 'embed_wt_pulled']
						sections: ['Pre-Invoice Review']
				'ready_to_bill':
					fields: ['needs_scanned', 'total_cost']
					readonly:
						fields: ['print', 'bill', 'inventory_id', 'part_of_kit', 'rental_type', 'is_340b',
						'frequency_code', 'day_supply', 'dispense_quantity',
						'expected_ea', 'list_ea', 'billed_ea', 'copay', 'include_cmpd_instr_wo', 'copy_changes_to_rx', 'last_through_date',
						'compounding_instructions', 'med_review_wo', 'weight', 'height', 'doses_prepared', 'containers_prepared', 'embed_wt_pulled']
						sections: ['Pre-Invoice Review']
				'billed':
					fields: ['needs_scanned', 'total_cost']
					readonly:
						fields: ['print', 'bill', 'inventory_id', 'part_of_kit', 'rental_type', 'is_340b',
						'frequency_code', 'day_supply', 'dispense_quantity',
						'expected_ea', 'list_ea', 'billed_ea', 'copay', 'include_cmpd_instr_wo', 'copy_changes_to_rx', 'last_through_date',
						'compounding_instructions', 'med_review_wo', 'weight', 'height', 'doses_prepared', 'containers_prepared', 'embed_wt_pulled']
						sections: ['Pre-Invoice Review']
				'voided':
					fields: ['needs_scanned', 'total_cost']
					readonly:
						fields: ['print', 'bill', 'inventory_id', 'part_of_kit', 'rental_type', 'is_340b',
						'frequency_code', 'day_supply', 'dispense_quantity', 
						'expected_ea', 'list_ea', 'billed_ea', 'copay','include_cmpd_instr_wo', 'copy_changes_to_rx', 'last_through_date',
						'compounding_instructions', 'med_review_wo', 'weight', 'height', 'doses_prepared', 'containers_prepared', 'embed_wt_pulled']
						sections: ['Pre-Invoice Review']
		view:
			label: 'Status'
			readonly: true
			offscreen: true

	needs_scanned:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['embed_wt_pulled', 'pulled_all_items']
					sections: ['Work Order']
		view:
			label: 'Needs Work Order?'
			control: 'checkbox'
			class: 'checkbox-only'
			offscreen: true
			readonly: true

	site_id:
		model:
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			readonly: true
			offscreen: true
			validate: [
				{
					name: 'UpdateInsuranceEstimatedTotals'
				}
			]

	ticket_no:
		model:
			required: true
			type: 'text'
		view:
			label: 'Ticket #'
			readonly: true
			offscreen: true

	ticket_item_no:
		model:
			required: true
			type: 'text'
		view:
			label: 'Ticket Item #'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	is_primary_drug:
		model:
			required: false
			source: ['Yes']
			if:
				'Yes':
					fields: ['refill_tracking', 'template_type', 'last_through_date', 'med_review_wo', 'weight', 'height',
					'bsa', 'no_of_containers', 'qty_per_container', 'ml_per_day', 'ml_kg_day', 'ml_m2_day',
					'final_dose_quantity', 'final_day_quantity', 'final_kg_day_quantity', 'final_m2_day_quantity']
				'!Yes':
					prefill:
						allow_dispense_quantity_update: 'Yes'
		view:
			label: 'Is Primary Drug?'
			readonly: true
			offscreen: true

	rx_id:
		model:
			required: true
			type: 'int'
			source: 'careplan_order_rx'
		view:
			label: 'Assigned Rx'
			columns: 2
			readonly: true

	refill_tracking:
		model:
			required: false
			source: ['Refills', 'Doses']
			if:
				'Doses':
					fields: ['doses_remaining', 'doses_prepared']
				'Refills':
					fields: ['refills_remaining']
		view:
			columns: 2
			label: 'Refill Tracking'
			control: 'radio'
			readonly: true
			offscreen: true

	refills_remaining:
		model:
			required: false
			type: 'int'
		view:
			columns: 4
			label: '# Refills Remaining'
			readonly: true

	doses_remaining:
		model:
			required: false
			type: 'int'
		view:
			columns: 4
			label: '# Doses Remaining'
			readonly: true

	template_type:
		model:
			required: true
			source: ['PO', 'IV', 'Injection', 'Factor', 'Compound', 'TPN']
			if:
				'IV':
					fields: ['compounding_instructions']
					sections: ['Compounding Instructions']
				'Compound':
					fields: ['compounding_instructions',
					'doses_to_prep', 'doses_per_container', 'containers_to_prep', 'containers_prepared']
					sections: ['Compounding Instructions', 'Medication Review', 'Vehicle/Container']
				'TPN':
					fields: ['compounding_instructions', 'doses_to_prep', 'doses_per_container', 'containers_to_prep', 'containers_prepared']
					sections: ['Compounding Instructions', 'Medication Review', 'Vehicle/Container']
		view:
			label: 'Template Type'
			control: 'radio'
			readonly: true
			offscreen: true

	print:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Print?'

	bill:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['billing_method', 'expected_ea', 'list_ea', 'billed_ea', 'copay']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Bill?'

	inventory_id:
		model:
			required: false
			source: 'inventory'
			sourcefilter:
				type:
					'dynamic': '{type}'
		view:
			label: 'Inventory Item'
			columns: -2
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'type': ['type']
					'hcpc_code': ['hcpc_code']
					'ndc': ['ndc']
					'formatted_ndc': ['formatted_ndc']
					'supply_billable': ['supply_billable']
			]
			validate: [
				{
					name: 'UpdateInsuranceEstimatedTotals'
				}
			]

	type:
		model:
			required: true
			source: ['Drug', 'Compound', 'Supply', 'Equipment Rental', 'Billable']
			if:
				'Supply':
					fields: ['part_of_kit', 'supply_billable']
					prefill:
						allow_dispense_quantity_update: 'Yes'
				'Drug':
					fields: ['is_340b', 'report_quantity', 'report_quantity_ea', 'report_unit_id',
					'replace_item_btn', 'is_primary_drug', 'day_supply']
					readonly:
						fields: ['inventory_id']
					prefill:
						needs_scanned: 'Yes'
				'Compound':
					fields: ['is_340b', 'is_primary_drug', 'day_supply']
					readonly:
						fields: ['inventory_id']
					prefill:
						needs_scanned: 'Yes'
				'Equipment Rental':
					fields: ['rental_type']
					prefill:
						needs_scanned: 'Yes'
				'Billable':
					readonly:
						fields: ['bill']
					prefill:
						allow_dispense_quantity_update: 'Yes'
						bill: 'Yes'
		view:
			label: 'Type'
			readonly: true
			offscreen: true
			validate: [
				{
					name: 'ChangeDispensedLabelToChargeQuantity'
				}
			]

	hcpc_code:
		view:
			label: 'HCPC Code'
			columns: 4
			readonly: true

	ndc:
		view:
			label: 'NDC'
			class: 'claim-field'
			readonly: true
			offscreen: true

	formatted_ndc:
		view:
			label: 'Formatted NDC'
			columns: 4
			readonly: true

	is_340b:
		model:
			max: 4
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: '340B?'

	replace_item_btn:
		model:
			source: ['Replace Item']
		view:
			columns: 4
			class: 'checkbox-only dsl-button'
			control: 'checkbox'
			label: 'Replace Item'
			validate: [
				name: 'ReplaceItem'
			]

	part_of_kit:
		model: 
			source: ['Yes']
			if:
				'Yes':
					readonly:
						fields: ['bill']
					prefill:
						'bill': ''
		view:
			columns: -4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Billed as Kit?'

	supply_billable:
		model:
			source: ['Yes']
			if:
				'Yes':
					prefill:
						'bill': 'Yes'
				'!':
					fields: ['supply_not_billable_warning']
					readonly:
						fields: ['bill']
					prefill:
						'bill': ''
		view:
			label: 'Supply Billable?'
			control: 'checkbox'
			class: 'checkbox-only'
			offscreen: true
			readonly: true

	supply_not_billable_warning:
		model:
			multi: true
			source: ['This supply item is set to not be billable from the inventory.']
		view:
			columns: 2
			control: 'checkbox'
			label: "Warning"
			class: 'list'
			readonly: true

	rental_type:
		model:
			required: true
			source: ['Purchase', 'Rental']
			if:
				'Rental':
					fields: ['frequency_code', 'day_supply']
		view:
			label: 'Rental Type'
			columns: -4
			validate: [
				{
					name: 'UpdateInsuranceEstimatedTotals'
				}
			]

	frequency_code:
		model:
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SV506'
		view:
			columns: 4
			class: 'claim-field'
			label: 'Rental Frequency'
			reference: 'SV506'

	day_supply:
		model:
			required: true
			max: 365
			min: 1
			type: 'int'
		view:
			columns: 4
			class: 'claim-field'
			label: 'Days Supplied'
			validate: [
				{
					name: 'CalculateLastThroughDate'
				}
			]

	last_through_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 4
			label: 'Last Through'

	allow_dispense_quantity_update:
		model:
			source: ['Yes']
			if:
				'!Yes':
					readonly:
						fields: ['dispense_quantity']
		view:
			label: 'Supply Billable?'
			control: 'checkbox'
			class: 'checkbox-only'
			readonly: true
			offscreen: true

	dispense_quantity:
		model:
			min: 1
			type: 'decimal'
			max: 9999999.999
			rounding: 1
			required: true
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Dispensed Quantity'
			validate: [
				{
					name: 'UpdateQuantityNeeded'
				}
			]

	dispense_unit:
		model:
			required: true
			source: 'list_unit'
			sourceid: 'code'
		view:
			columns: 4
			label: 'Unit'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateUnitFieldNote'
					target: ['dispense_quantity'],
				}
			]

	report_quantity:
		model:
			min: 1
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
			required: true
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Report Quantity'
			note: 'Calculated after save.'
			readonly: true
			offscreen: true

	report_quantity_ea:
		model:
			min: 1
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
			required: true
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Report Quantity (EA)'
			note: 'Per Bill Unit'
			readonly: true
			offscreen: true

	report_unit_id:
		model:
			required: false
			source: 'list_unit'
			sourceid: 'code'
		view:
			label: 'Report Unit'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateUnitFieldNote'
					target: ['report_quantity_ea', 'report_quantity']
				}
			]

	billing_method:
		model:
			required: true
			source: ['Insurance', 'Self Pay', 'Do Not Bill']
			if:
				'Insurance':
					fields: ['insurance_id', 'accept_assignment']
				'Do Not Bill':
					fields: ['drug_not_billable_warning']
					prefill:
						bill: ''
					readonly:
						fields: ['bill']
				'Self Pay':
					prefill:
						accept_assignment: ''
		view:
			control: 'radio'
			label: 'Billing Method'

	drug_not_billable_warning:
		model:
			multi: true
			source: ['This drug is set to do not bill on the order.']
		view:
			columns: 2
			control: 'checkbox'
			label: "Warning"
			class: 'list'
			readonly: true

	insurance_id:
		model:
			source: 'patient_insurance'
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
				billing_method_id:
					'static': ['cms1500', 'mm', 'ncpdp']
			if:
				'*':
					fields: ['expected_ea', 'billed_ea', 'copay']
		view:
			columns: 2
			control: 'select'
			label: 'Insurance'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_insurance/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'billing_method_id': 'billing_method_id'
			]
			validate: [
				{
					name: 'UpdateInsuranceEstimatedTotals'
				},
				{
					name: 'RunChangedPayerAlerts'
				}
			]

	billing_method_id:
		model:
			source: 'list_billing_method'
			sourceid: 'code'
			if:
				'ncpdp':
					readonly: 
						fields: ['copay']
		view:
			label: 'Billing Method'
			readonly: true
			offscreen: true

	accept_assignment:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Accept Assignment?'
			note: 'If no, the insurance will not be billed for this item.'
			columns: 4

	expected_ea:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
			min: 0
		view:
			columns: -4
			label: 'Expected (EA)' 
			class: 'numeral money'
			format: '$0,0.0000'

	list_ea:
		model:
			rounding: 0.00001
			type: 'decimal'
			min: 0
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.0000'
			label: 'List Price (EA)'

	billed_ea:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
			min: 0
		view:
			columns: 4
			label: 'Bill (EA)' 
			class: 'numeral money'
			format: '$0,0.0000'

	total_cost:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
			min: 0
		view:
			columns: 4
			label: 'Total Cost' 
			class: 'numeral money'
			format: '$0,0.0000'
			readonly: true
			note: 'Automatically updates when items are pulled.'

	copay:
		model:
			prefill: ['careplan_order_rx_disp.copay']
			default: 0.00
			rounding: 0.01
			type: 'decimal'
			min: 0
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Copay'

	embed_wt_pulled:
		model:
			multi: true
			sourcefilter:
				ticket_item_no:
					'dynamic': '{ticket_item_no}'
				dispensed_unit_id:
					'dynamic': '{dispense_unit}'
				void:
					'static': '!Yes'
		view:
			embed:
				form: 'careplan_dt_wt_pulled'
				selectable: false
				add_preset:
					ticket_no: '{ticket_no}'
					ticket_item_no: '{ticket_item_no}'
					inventory_id: '{inventory_id}'
					site_id: '{site_id}'
					quantity_needed: '{quantity_needed}'
			grid:
				edit: true
				hide_cardmenu: true
				add: 'flyout'
				fields: ['inventory_id', 'dispensed_quantity', 'quantity_on_hand', 'lot_no', 'serial_no', 'expiration_date']
				label: ['Item', 'Quantity', 'Qty On Hand', 'Lot', 'Serial', 'Exp Dt']
				width: [25, 15, 15, 15, 15, 15]
			label: 'Items Pulled'
			validate: [
				{
					name: 'CheckPulledComplete'
				},
				{
					name: 'UpdateLabels'
				}
			]

	quantity_needed:
		model:
			required: true
			type: 'decimal'
			rounding: 0.001
		view:
			columns: 4
			label: 'Quantity Needed'
			readonly: true
			offscreen: true

	pulled_all_items:
		model: 
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Pulled All Items?'
			readonly: true
			offscreen: true

	# Compounding Instructions
	doses_to_prep:
		model:
			min: 1
			required: false
			type: 'int'
		view:
			columns: 4
			label: 'Doses to Prep'
			readonly: true

	doses_per_container:
		model:
			required: false
			type: 'int'
		view:
			columns: 4
			label: 'Doses/Container'
			readonly: true

	containers_to_prep:
		model:
			required: false
			type: 'int'
		view:
			columns: 4.1
			label: 'Containers to Prep'
			readonly: true

	doses_prepared:
		model:
			required: true
			type: 'int'
		view:
			columns: 4
			validate: [
				{
					name: "CompareValidator"
					fields: [
						"doses_prepared",
						"doses_remaining"
					]
					require: "lte"
					error: "Doses prepared must be less than Doses Remaining"
				},
				{
					name: 'UpdateLabels'
				}
			]

	containers_prepared:
		model:
			required: true
			type: 'int'
		view:
			columns: 4
			label: 'Containers to Prep'

	include_cmpd_instr_wo:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Include on Work Order?'
			columns: 2

	compound_instructions_changed:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['copy_changes_to_rx']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Compound Instructions Changed?'
			offscreen: true
			readonly: true

	copy_changes_to_rx:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Copy changes back to Prescription?'
			columns: 2

	auto_compounding_instructions:
		view:
			control: 'area'
			label: 'Auto Generated'
			readonly: true
			columns: -2

	compounding_instructions:
		view:
			control: 'area'
			label: 'Compounding/Reconstitution Instructions'
			columns: 2
			validate: [
				{
					name: 'CheckForCompoundingInstrChanges'
				}
			]

	container_id:
		model:
			source: 'inventory'
			sourceid: 'id'
			if:
				'!':
					readonly:
						fields: ['vehicle_id']
		view:
			label: 'Container'
			columns: -2
			readonly: true

	vehicle_id:
		model:
			source: 'inventory'
			sourceid: 'id'
			if:
				'!':
					readonly:
						fields: ['container_id']
		view:
			label: 'Vehicle'
			columns: 2
			readonly: true

	volume_per_dose:
		model:
			type: 'decimal'
			rounding: 0.001
			required: false
			max: 9999999.999
			min: 0.001
			if:
				'!':
					readonly:
						fields: ['overfill']
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Volume Per Dose'
			note: 'mL'
			readonly: true

	overfill:
		model:
			type: 'decimal'
			rounding: 0.001
			required: false
			max: 9999999.999
			min: 0.001
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Overfill'
			note: 'mL'
			readonly: true

	# Medication Review
	med_review_wo:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Include on Work Order?'
			columns: 4

	weight:
		model:
			rounding: 0.01
			type: 'decimal'
			prefill: ['patient_measurement_log']
		view:
			columns: 4
			class: 'unit'
			label: 'Weight (Kg)'
			validate: [
				{
					name: 'UpdateMedicationReview'
				}
			]

	height:
		model:
			prefill: ['patient_measurement_log']
			max: 250
			min: 15
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'unit'
			label: 'Height (cm)'
			note: 'E.g.: 143, 143cm, 1.43m, 56in, 56", 4\' 8", 4 8'
			validate: [
				{
					name: 'UpdateMedicationReview'
				}
			]

	bsa:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'BSA m²'
			columns: 4
			readonly: true

	no_of_containers:
		model:
			type: 'int'
		view:
			label: '# of Containers'
			offscreen: true
			readonly: true

	qty_per_container:
		model:
			type: 'int'
		view:
			label: 'Qty / Container'
			offscreen: true
			readonly: true

	ml_per_day:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'mL / Day'
			readonly: true
			columns: 4

	ml_kg_day:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'mL / Kg / Day'
			readonly: true
			columns: 4

	ml_m2_day:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'mL / m² / Day'
			readonly: true
			columns: 4

	final_dose_quantity:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Amount / Dose'
			readonly: true
			columns: 4

	final_day_quantity:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Amount / Day'
			readonly: true
			columns: 4

	final_m2_day_quantity:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Amount / m² / Day'
			readonly: true
			columns: 4

	final_kg_day_quantity:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Amount / Kg / Day'
			readonly: true
			columns: 4

	embed_rplc:
		model:
			multi: true
			sourcefilter:
				original_inventory_id:
					'dynamic': '{inventory_id}'
		view:
			embed:
				form: 'careplan_dt_item_rplc'
				selectable: false
				add_preset:
					rx_id: '{rx_id}'
					original_inventory_id: '{inventory_id}'
			grid:
				edit: true
				hide_cardmenu: true
				add: 'flyout'
				fields: ['original_inventory_id', 'replacement_reason_id', 'update_rx', 'comment']
				label: ['Original Item', 'Reason', 'Update Rx?', 'Comment']
				width: [25, 15, 15, 15]
			label: 'Replacement Log'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     []
		read:       ['admin','pharm','csr','cm', 'nurse']
		read_all:   ['admin','pharm','csr','cm', 'nurse']
		request:    []
		review:     ['admin','pharm','csr','cm', 'nurse']
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['site_id']
			['patient_id']
			['careplan_id']
			['inventory_id']
			['ticket_no']
			['ticket_item_no']
			['rx_id']
			['insurance_id']
			['hcpc_code']
			['ndc']
		]
	name: ['inventory_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		patient_measurement_log:
			link:
				id: 'patient_id'
			max: 'created_on'
		careplan_order_rx_disp:
			link:
				rx_id: 'rx_id'
			max: 'created_on'
	sections_group: [
		'Delivery Ticket Item':
			hide_header: true
			indent: false
			sections: [
				'Details':
					hide_header: true
					indent: false
					fields: ['status', 'site_id', 'rx_id', 'ticket_no', 'ticket_item_no',
					'is_primary_drug', 'needs_scanned', 'print', 'bill', 'pulled_all_items', 'inventory_id',
					'type', 'hcpc_code', 'ndc', 'formatted_ndc', 'is_340b', 'part_of_kit', 'supply_billable',
					'rental_type', 'frequency_code', 'day_supply', 'last_through_date', 'allow_dispense_quantity_update',
					'dispense_quantity', 'dispense_unit', 'quantity_needed', 'report_quantity', 'report_quantity_ea', 'report_unit_id',
					'refill_tracking', 'refills_remaining', 'doses_remaining']
					tab: 'Details'

				'Pre-Invoice Review':
					hide_header: false
					indent: false
					fields: ['supply_not_billable_warning', 'drug_not_billable_warning', 'billing_method', 'insurance_id', 'billing_method_id', 'accept_assignment', 'expected_ea', 'list_ea', 'billed_ea', 'copay', 'total_cost']
					tab: 'Details'

				'Compounding Instructions':
					hide_header: true
					indent: false
					fields: ['doses_to_prep', 'doses_per_container', 'containers_to_prep', 'doses_prepared', 'containers_prepared', 'include_cmpd_instr_wo',
					'compound_instructions_changed', 'copy_changes_to_rx', 'auto_compounding_instructions', 'compounding_instructions']
					tab: 'Compounding Instructions'

				'Vehicle/Container':
					hide_header: true
					indent: false
					fields: ['container_id', 'vehicle_id', 'volume_per_dose', 'overfill']
					tab: 'Compounding Instructions'

				'Medication Review':
					indent: false
					tab: 'Compounding Instructions'
					note: 'Calculations for final concentration'
					fields: ['med_review_wo', 'weight', 'height', 'bsa', 'no_of_containers', 'qty_per_container',
					'ml_per_day', 'ml_kg_day', 'ml_m2_day', 'final_dose_quantity', 'final_day_quantity',
					'final_kg_day_quantity', 'final_m2_day_quantity']

				'Work Order':
					hide_header: true
					indent: false
					fields: ['embed_wt_pulled']
					tab: 'Work Order'

			]
	]

view:
	dimensions:
		width: '60%'
		height: '65%'
	block:
		validate: [
			name: 'DeliveryTicketBlock'
		]
	hide_cardmenu: true
	comment: 'Delivery Ticket Item'
	grid:
		fields: ['inventory_id', 'dispense_quantity', 'dispense_unit', 'formatted_ndc', 'hcpc_code']
		sort: ['-id']
	label: 'Delivery Ticket Item'
	open: 'edit'