fields:

	patient_id:
		model:
			type: 'int'
			required: true
			source: 'patient'
		view:
			label: 'Patient ID'
			readonly: true
			offscreen: true

	response_id:
		model:
			type: 'int'
			source: 'ncpdp_response'
		view:
			label: 'NCPDP Response'
			readonly: true
			offscreen: true

	response_uuid:
		model:
			type: 'text'
		view:
			label: 'Response UUID'
			readonly: true
			offscreen: true

	response_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Response Date/Time'
			columns: 2
			readonly: true

	response_status:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '501-F1'
		view:
			columns: 2
			note: '501-F1'
			label: 'Response Status'
			readonly: true

	transaction_response_status:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '112-AN'
			if:
				'R':
					fields: ['embed_rejmsg']
					sections: ['Reject Message']
		view:
			columns: 4
			note: '112-AN'
			class: 'status'
			label: 'Transaction Status'
			readonly: true

	patient_first_name:
		model:
			max: 12
		view:
			columns: -4
			note: '310-CA'
			label: 'First Name'
			readonly: true

	patient_last_name:
		view:
			columns: 4
			note: '311-CB'
			label: 'Last Name'
			readonly: true

	patient_date_of_birth:
		model:
			type: 'date'
		view:
			columns: 4
			note: '304-C4'
			label: 'DOB'
			readonly: true

	embed_rejmsg:
		model:
			multi: true
			sourcefilter:
				response_uuid:
					'dynamic': '{response_uuid}'
		view:
			embed:
				form: 'ncpdp_response_summary_rejmsg'
				selectable: false
			grid:
				edit: false
				rank: 'none'
				add: 'none'
				fields: ['reject_code']
				label: ['Reject Reason']
				width: [100]
			label: 'Reject Message'
			readonly: true

	embed_cf_payer:
		model:
			multi: true
			sourcefilter:
				response_id:
					'dynamic': '{response_id}'
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				query: 'cardfinder_payers'
				add_form: 'patient_insurance'
				add_preset:
					patient_id: '{patient_id}'
					payer_id: '{payer_id}'
					group_number: '{group_number}'
					person_code: '{person_code}'
					bin: '{bin}'
					pcn: '{pcn}'
					cardholder_id: '{cardholder_id}'
					effective_date: '{effective_date}'
					termination_date: '{termination_date}'
					pharmacy_relationship_id: '{pharmacy_relationship_id}'
				selectable: false
			grid:
				edit: false
				rank: 'none'
				add: 'flyout'
				fields: ['payer_id', 'bin', 'pcn', 'group_number', 'termination_date', 'cardholder_id']
				label: ['Payer', 'BIN', 'PCN', 'Group #', 'Benefit Term Dt', 'Cardholder ID']
				width: [35, 10, 15, 15, 15, 15]
			label: 'Card Finder Payer'

	embed_addtl_msg:
		model:
			multi: true
			sourcefilter:
				response_uuid:
					'dynamic': '{response_uuid}'
		view:
			embed:
				form: 'ncpdp_response_summary_msg'
				selectable: false
			grid:
				edit: false
				rank: 'none'
				add: 'none'
				fields: ['add_msg', 'add_msg_qualifier']
				label: ['Message', 'Qualifier']
				width: [75, 25]
			label: 'Additional Message'
			readonly: true

	message:
		view:
			control: 'area'
			note: '504-F4'
			label: 'Message'
			readonly: true

	request_json_data:
		model:
			type: 'json'
		view:
			class: 'json-viewer'
			label: ' '
			readonly: true

	response_json_data:
		model:
			type: 'json'
		view:
			class: 'json-viewer'
			label: ' '
			readonly: true

	request_d0_raw:
		view:
			control: 'raw'
			label: 'Raw D0 request'
			readonly: true

	response_d0_raw:
		view:
			control: 'raw'
			label: 'Raw D0 response'
			readonly: true
model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['response_uuid']
			['patient_id']
		]

	name: ['response_uuid']
	sections_group: [
		'Response Summary':
			tab: 'Results'
			hide_header: true
			indent: false
			fields: ['response_id', 'response_uuid', 'patient_id', 'response_datetime',
			'response_status', 'patient_first_name', 'patient_last_name', 'patient_date_of_birth', 'transaction_response_status']
		'Found Payers':
			tab: 'Results'
			hide_header: true
			indent: false
			fields: ['embed_cf_payer']
		'Reject Message':
			tab: 'Results'
			hide_header: true
			indent: false
			fields: ['embed_rejmsg']
		'Additional Message':
			tab: 'Results'
			hide_header: true
			indent: false
			fields: ['embed_addtl_msg']
		'Request':
			indent: false
			tab: 'Raw'
			fields: ['request_json_data']
		'Request D0':
			indent: false
			tab: 'Raw'
			fields: ['request_d0_raw']
		'Response':
			indent: false
			tab: 'Raw'
			fields: ['response_json_data']
		'Response D0':
			indent: false
			tab: 'Raw'
			fields: ['response_d0_raw']
		]

view:
	dimensions:
		width: '90%'
		height: '85%'
	hide_cardmenu: true
	comment: 'Card Finder Summary'
	find:
		basic: ['patient_id']
	grid:
		fields: ['response_uuid', 'patient_id', 'response_datetime', 'response_status', 'transaction_response_status']
		sort: ['-created_on']
	label: 'Card Finder Summary'
	open: 'read'