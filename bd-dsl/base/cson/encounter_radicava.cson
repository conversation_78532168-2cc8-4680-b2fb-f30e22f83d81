fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	caregiver_demo:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
		view:
			control: 'radio'
			label: 'Patient/caregiver able to return demonstrate proper Radicava preparation, administration (if applicable)?'

	add_training:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['add_training_details']
		view:
			control: 'radio'
			label: 'Will patient/caregiver need additional training?'

	add_training_details:
		model:
			required: true
		view:
			label: 'Describe additional education requirements'

	admin_adr:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['allergic_reactions', 'common_reactions']
		view:
			control: 'radio'
			label: 'Does patient report any ADRs associated with administration of Radicava or that develop shortly after administration?'

	allergic_reactions:
		model:
			multi: true
			source: ['Hives', 'Trouble breathing or swallowing', 'Itching', 
			'Swelling of the lips, tongue, face', 'Dizziness', 'Wheezing', 'Fainting', 'Asthma attack']
			if:
				'*':
					note: "Contact the patient's healthcare provider right away or direct patient to the nearest 
emergency room"
		view:
			control: 'checkbox'
			label: 'Does patient report any of the following allergic side effects?'

	common_reactions:
		model:
			multi: true
			source: ['Trouble walking', 'Headache', 'Rash']
		view:
			control: 'checkbox'
			label: 'Does patient report any of the following side effects?'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['careplan_id', 'order_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		encounter_radicava:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Drug Administration':
			fields: ['caregiver_demo', 'add_training', 'add_training_details', 'admin_adr', 'allergic_reactions', 'common_reactions']
			prefill: 'encounter_radicava'

view:
	comment: 'Patient > Careplan > Encounter > Radicava'
	label: "Patient Encounter: Radicava"
