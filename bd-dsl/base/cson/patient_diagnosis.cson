fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
	external_id:
		view:
			label: 'External Id'
			readonly: true
			offscreen: true
	careplan_id:
		model:
			required: false
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	dx_id:
		model:
			required: true
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Diagnosis'

	rank:
		model:
			type: 'int'
		view:
			columns: 2
			label: 'Rank'
			findunique: true

	diagnosis_start_date:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Start Date'

	diagnosis_stop_date:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Stop Date'

	active:
		model:
			default: 'Yes'
			max: 32
			min: 1
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Active?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    ['csr']
		update:     []
		update_all: ['admin']
		write:      ['admin', 'csr','liaison', 'pharm']

	bundle: ['patient']
	name: '{rank} - {dx_id_auto_name}'
	indexes:
		unique: [
			['patient_id', 'careplan_id', 'rank', 'active','archived']
		]
	sections_group: [
		'Diagnosis':
			fields: ['dx_id', 'rank', 'diagnosis_start_date', 'diagnosis_stop_date', 'active']
	]
view:
	dimensions:
        width: '75%'
        height: '60%'
	hide_cardmenu: true
	comment: 'Patient -> Patient DX'
	find:
		basic: ['dx_id', 'rank', 'active']
	grid:
		fields: ['dx_id', 'rank', 'diagnosis_start_date', 'diagnosis_stop_date']
		sort: ['-rank']
	icon: 'diagnosis'
	label: 'Patient Diagnosis'
