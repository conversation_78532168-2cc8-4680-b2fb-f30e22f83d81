fields:

	clinical_assmt_id:
		model:
			required: true
			source: 'list_clinical_asmt'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Clinical Assessment'

	therapy_id:
		model:
			source: 'list_therapy'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Therapy'

	dx_id:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Diagnosis'

	route_id:
		model:
			source: 'list_route'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Route'

	brand_name_id:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Drug Brand'

	cadence_days:
		model:
			required: true
			type: 'int'
		view:
			label: 'Cadence (Days)'
			note: 'Request every X days, set to 0 for one-time request'
			columns: 3

	active:
		model:
			default: 'Yes'
			source: ['No', 'Yes']
			required: true
		view:
			control: 'radio'
			label: 'Active'
			columns: 3
model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['workflow']

	indexes:
		unique: [
			['clinical_assmt_id', 'therapy_id', 'dx_id', 'route_id', 'brand_name_id']
		]
	name: ['clinical_assmt_id', 'cadence_days']
	sections_group: [
		'Clinical Assessment Rule':
			fields: ['clinical_assmt_id', 'therapy_id', 'dx_id', 'route_id', 'brand_name_id', 'cadence_days', 'active']
	]
view:
	comment: 'Manage > Clinical Assessment Rule'
	find:
		basic: ['clinical_assmt_id', 'therapy_id', 'dx_id', 'route_id', 'brand_name_id', 'active']
	grid:
		fields: ['clinical_assmt_id', 'therapy_id', 'dx_id', 'route_id', 'brand_name_id', 'cadence_days']
		sort: ['clinical_assmt_id']
	label: 'Clinical Assessment Rule'
	open: 'read'
