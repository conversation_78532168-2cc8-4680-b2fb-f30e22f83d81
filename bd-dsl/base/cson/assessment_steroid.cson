fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			columns: 2

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'
			columns: 2

	gender:
		model:
			prefill: ['patient']
			if:
				'Female':
					fields: ['is_pregnant', 'will_be_pregnant']
		view:
			offscreen: true
			readonly: true

	# Assessment Questions

	immuno_therapy:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Are you currently taking or plan to start immunosuppressant therapy?"
			columns: 2

	have_tb_exposure:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Have you had any recent exposure to tuberculosis (TB), HBV, or mycoses?"
			columns: 2

	have_measles_exposure:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Have you had any recent exposure to Measles, Shingles, or Varicella?"
			columns: 2

	had_seizure:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Do you have a history of seizures?"
			columns: 2

	is_pregnant:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Are you currently pregnant, or breast feeding?"
			columns: 2

	will_be_pregnant:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Do you plan to get pregnant while on this medication?"
			columns: 2

	# Support Questions
	has_ems:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Does patient have access to EMS (w/in 15 minutes)?"
			columns: 2

	nurse_present:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will nurse be present for the entire infusion?"
			columns: 2

	adult_present:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will an additional adult be present for the entire infusion?"
			columns: 2

	adult_present_after:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will an additional adult be present for 2 to 3 hours after infusion completed?"
			columns: 2

	physician_available:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Will the ordering physician be available by phone during the infusion?"
			columns: 2

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Steroid General Assessment':
			hide_header: false
			indent: false
			fields: ['gender', 'immuno_therapy',
			'have_tb_exposure', 'have_measles_exposure', 'had_seizure', 'is_pregnant',
			'will_be_pregnant', 'has_ems', 'nurse_present', 'adult_present',
			'adult_present_after', 'physician_available']
view:
	comment: 'Patient > Careplan > Assessment > Steroid'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Steroid'
	open: 'read'
