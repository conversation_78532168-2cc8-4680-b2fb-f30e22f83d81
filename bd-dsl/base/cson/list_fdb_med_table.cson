#TABLE: RMIID1_MED
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	medid:
		model:
			type: 'int'
		view:
			label: 'MED Medication ID (Stable ID)'
			columns: 3

	routed_dosage_form_med_id:
		model:
			type: 'int'
		view:
			label: 'MED Routed Dosage Form Medication ID (Stable ID)'
			columns: 3

	med_strength:
		model:
			type: 'text'
		view:
			label: 'MED Strength'
			columns: 3

	med_strength_uom:
		model:
			type: 'text'
		view:
			label: 'MED Strength Unit of Measure'
			columns: 3

	med_medid_desc:
		model:
			type: 'text'
		view:
			label: 'MED Medication Description'
			columns: 3

	gcn_seqno:
		model:
			type: 'int'
		view:
			label: 'Clinical Formulation ID (Stable ID)'
			columns: 3

	med_gcnseqno_assign_cd:
		model:
			type: 'text'
		view:
			label: 'MED GCN_SEQNO Assignment Code'
			columns: 3

	med_name_source_cd:
		model:
			type: 'text'
		view:
			label: 'MED Medication Name Source Code'
			columns: 3

	med_ref_fed_legend_ind:
		model:
			type: 'text'
		view:
			label: 'MED Reference Federal Legend Indicator'
			columns: 3

	med_ref_dea_cd:
		model:
			type: 'text'
		view:
			label: 'MED Reference Federal DEA Class Code'
			columns: 3

	med_ref_multi_source_cd:
		model:
			type: 'text'
		view:
			label: 'MED Reference Multi-Source Code'
			columns: 3

	med_ref_gen_drug_name_cd:
		model:
			type: 'text'
		view:
			label: 'MED Reference Generic Medication Name Code'
			columns: 3

	med_ref_gen_comp_price_cd:
		model:
			type: 'text'
		view:
			label: 'MED Reference Generic Comparative Price Code'
			columns: 3

	med_ref_gen_spread_cd:
		model:
			type: 'text'
		view:
			label: 'MED Reference Generic Comparative Price Code'
			columns: 3

	med_ref_innov_ind:
		model:
			type: 'text'
		view:
			label: 'MED Reference Innovator Indicator'
			columns: 3

	med_ref_gen_thera_equ_cd:
		model:
			type: 'text'
		view:
			label: 'MED Reference Generic Therapeutic Equivalence Code'
			columns: 3

	med_ref_desi_ind:
		model:
			type: 'text'
		view:
			label: 'MED Reference DESI Indicator'
			columns: 3

	med_ref_desi2_ind:
		model:
			type: 'text'
		view:
			label: 'MED Reference DESI2 Indicator'
			columns: 3

	med_status_cd:
		model:
			type: 'text'
		view:
			label: 'MED Medication Status Code'
			columns: 3

	generic_medid:
		model:
			type: 'int'
		view:
			label: 'MED Generic Medication Identifier'
			columns: 3


model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['medid', 'med_medid_desc']
	indexes:
		many: [
			['medid']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['medid', 'routed_dosage_form_med_id', 'med_strength', 'med_strength_uom', 'med_medid_desc']

view:
	comment: 'Manage > List FDB Medication Table'
	find:
		basic: ['medid']
	grid:
		fields: ['medid', 'routed_dosage_form_med_id', 'med_strength', 'med_strength_uom', 'med_medid_desc']
	label: 'List FDB Medication Table'
