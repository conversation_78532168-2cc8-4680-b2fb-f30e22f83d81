fields:

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site ID'
			readonly: true

	patient_id:
		model:
			required: true 
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true

	careplan_id:
		model:
			required: false
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan'
			readonly: true

	order_id:
		model:
			source: 'careplan_order'
			required: false
			type: 'int'
		view:
			label: 'Referral ID'
			readonly: true

	event_id:
		model:
			required: true
			source: 'list_patient_event'
			sourceid: 'code'
		view:
			label: 'Event'
			readonly: true
model:
	access:
		create:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'payer', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'payer', 'physician']
		request:    []
		update:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['audit']
	indexes:
		many: [
			['site_id'],
			['patient_id'],
			['careplan_id'],
			['event_id'],
			['order_id']
		]
	name: ['patient_id', 'order_id', 'event_id']
	reportable: true

	sections:
		'Patient Event Audit Trail':
			fields: ['site_id', 'patient_id', 'careplan_id', 'order_id', 'event_id']

view:
	find:
		basic: ['patient_id', 'event_id', 'site_id']
	comment: 'Patient > Event Audit Trail'
	grid:
		fields: ['site_id', 'patient_id', 'careplan_id', 'order_id', 'event_id']
		sort: ['-created_on']
	label: 'Patient Event Audit Trail'
	open: 'read'