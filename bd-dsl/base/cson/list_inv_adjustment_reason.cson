fields:

	code:
		model:
			max: 64
			search: 'A'
			required: true
		view:
			label: 'Code'
			columns: 2
			findunique: true

	name:
		model:
			search: 'A'
			required: true
		view:
			label: 'Name'
			columns: 2
			findunique: true

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			columns: 2
			label: 'Can Sync Record'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			columns: 2
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['inventory']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['code']
		]
	name: '{code} - {name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'allow_sync', 'active']

view:
	comment: 'Manage > Inventory Adjustment Reason'
	find:
		basic: ['code','name']
	grid:
		fields: ['name']
		sort: ['name']
	label: 'Inventory Adjustment Reason'
	open: 'read'
