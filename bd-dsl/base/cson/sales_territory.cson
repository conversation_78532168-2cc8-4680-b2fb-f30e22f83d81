fields:
	external_id:
		view:
			label: 'External ID'
			readonly: true
			offscreen: true

	territory_code:
		model:
			required: true
			max: 10
		view:
			columns: 2
			label: 'Territory Code'

	territory_name:
		model:
			required: true
		view:
			columns: 2
			label: 'Territory Name'

	assigned_sales_rep:
		model:
			multi: false
			source: 'user'
			sourcefilter:
				role:
					'static': ['csr','admin']
		view:
			columns: 3
			note: 'This controls access to view the associated patient chart'
			label: 'Sales Rep'

	commissioned_sales_rep:
		model:
			multi: false
			required: false
			source: 'user'
			sourcefilter:
				role:
					'static': ['csr','admin']
		view:
			columns: 3
			label: 'Commissioned Sales Rep'

	assigned_sales_manager:
		model:
			multi: false
			source: 'user'
			sourcefilter:
				role:
					'static': 'liaison'
		view:
			columns: 3
			label: 'Sales Manager'

model:
	access:
		create:     []
		create_all: ['admin', 'liaison']
		delete:     ['admin', 'liaison']
		read:       ['admin', 'liaison']
		read_all:   ['admin', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'liaison']
		write:      ['admin', 'liaison']
	name: '{assigned_sales_rep_auto_name} - {territory_name}'
	indexes:
		unique: [
			['territory_code']
			['territory_name']
		]
	sections:
		'Sales Territories':
			fields: ['territory_code', 'territory_name', 'assigned_sales_rep', 'commissioned_sales_rep', 'assigned_sales_manager']

view:
	hide_cardmenu: true
	find:
		basic: ['territory_code','territory_name', 'assigned_sales_rep', 'commissioned_sales_rep','assigned_sales_manager']
	grid:
		fields: ['territory_code', 'territory_name', 'assigned_sales_rep', 'commissioned_sales_rep', 'assigned_sales_manager']
	label: 'Sales Territories'
