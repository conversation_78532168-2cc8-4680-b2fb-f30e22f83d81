fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			readonly: true
			offscreen: true

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'
			readonly: true
			offscreen: true

	subform_charges:
		model:
			required: false
			multi: true
			type: 'subform'
			source: 'ledger_charge_line'
		view:
			grid:
				edit: true
				rank: 'none'
				add: 'none'
				fields: ['inventory_id', 'payer_id', 'bill_quantity', 'expected', 'gross_amount_due', 'total_cost']
				label: ['Item', 'Payer', 'Quantity', 'Exp $', 'Gross Amt $', 'Cost $']
				width: [25, 25, 10, 15, 15, 10]
			label: 'New Charges'

	lgl_fltr:
		model:
			multi: true
			source: 'ledger_charge_line'
			type: 'int'
		view:
			label: 'Existing Charge Line Filter'
			offscreen: true
			readonly: true

	embed_charge_lines:
		model:
			multi: true
			sourcefilter:
				id:
					'dynamic': '{lgl_fltr}'
				patient_id:
					'dynamic': '{patient_id}'
				invoice_no:
					'static': null
				void:
					'static': '!Yes'
		view:
			embed:
				form: 'ledger_charge_line'
				selectable: true
			grid:
				edit: false
				rank: 'none'
				add: 'none'
				selectall: true
				fields: ['inventory_id', 'payer_id', 'bill_quantity', 'expected', 'gross_amount_due', 'total_cost']
				label: ['Item', 'Payer', 'Quantity', 'Exp $', 'Gross Amt $', 'Cost $']
				width: [25, 25, 10, 15, 15, 10]
			label: 'Unlinked Charges'

	subform_exceptions:
		model:
			required: false
			multi: true
			type: 'subform'
			source: 'careplan_dt_exception'
		view:
			grid:
				edit: true
				rank: 'none'
				add: 'none'
				fields: ['dt_exception_id', 'inventory_id', 'description', 'resolved']
				label: ['Exception', 'Item', 'Description', 'Resolved']
				width: [20, 25, 40, 15]
			label: 'Confirmation Exceptions Log'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
	name: 'Delivery Ticket Confirmation'
	sections_group: [
		'New Charges':
			hide_header: true
			indent: false
			fields: ['subform_charges']
		'Billed Charges':
			note: 'Select to link charges to the delivery ticket'
			hide_header: true
			indent: false
			fields: ['embed_charge_lines']
		'Confirmation Exceptions Log':
			hide_header: true
			indent: false
			fields: ['subform_exceptions']
	]

view:
	hide_cardmenu: true
	comment: 'Delivery Ticket Confirmation'
	grid:
		fields: ['created_on', 'created_by', 'careplan_id', 'patient_id']
		sort: ['-created_on']
	label: 'Delivery Ticket Confirmation'
	open: 'edit'
