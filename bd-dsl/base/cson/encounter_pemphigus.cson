fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	# Wellness
	pemp_blister_loc:
		model:
			max: 32
			multi: true
			source: ['Mouth', 'Face/neck', 'Upper Extremities', 'Lower Extremities', 'Trunk/Abdomen', 'Scalp']
		view:
			control: 'checkbox'
			note: 'Select all that apply'
			label: 'Location of blistering'
			columns: 2

	pemp_lession_quan:
		model:
			max: 32
			source: ['Many', 'Few', 'None']
		view:
			control: 'radio'
			label: 'Quantity of lesions'
			columns: 2

	pemp_lession_type:
		model:
			max: 32
			source: ['Disseminated', 'Localized', 'None']
		view:
			control: 'radio'
			label: 'Type of lesions'
			columns: 2

	pemp_is_first:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['pemp_since_last', 'pemp_lession_char']
		view:
			control: 'radio'
			label: "Is this the patient's first infusion?"
			columns: 2

	pemp_lession_char:
		model:
			max: 64
			multi: false
			source: ['New (intact w/clear fluid)', 'Healing (Open w/new skin growth)', 'Completely healed']
		view:
			control: 'radio'
			label: 'Characteristics of lesions'
			columns: 2

	pemp_since_last:
		model:
			max: 128
			multi: false
			source: ['Worsening (new lesions since last visit)', 'No change', 'Improved (Lesions are healing, no new lesions)']
		view:
			control: 'radio'
			label: 'Since last infusion:'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		encounter_pemphigus:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['careplan_id', 'order_id']
	sections:
		'Pemphigus Follow-up':
			fields: ['pemp_blister_loc', 'pemp_lession_quan', 'pemp_lession_type', 'pemp_is_first', 'pemp_lession_char','pemp_since_last']

view:
	comment: 'Patient > Careplan > Encounter > Pemphigus'
	label: 'Patient Encounter: Pemphigus'
