fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	meningococcal_infections:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Did you go over the risk of meningococcal infections again with patient?'
			control: 'radio'

	infections_risk:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Did you counsel on risk of other infections while on Soliris?'
			control: 'radio'

	recent_infections:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Did the patient report any recent serious infections?'
			control: 'radio'

	mg_crisis:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['mg_crisis_episodes']
			required: true
		view:
			label: 'Have you had any MG crisis since last treatment?'
			control: 'radio'

	mg_crisis_episodes:
		model:
			min: 0
			type: 'int'
			required: true
		view:
			label: 'How many episodes'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'csr', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Meningococcal Infections':
			note: 'Signs and symptoms of meningococcal infections include the following: Headache with N/V or a fever, Headache with a stiff neck, fever and a rash, confusion, muscle aches with flu-like symptoms, eyes sensitive to light'
			fields: ['meningococcal_infections']
		'Soliris Assessment':
			fields: ['infections_risk', 'recent_infections', 'mg_crisis', 'mg_crisis_episodes']
view:
	comment: 'Patient > Careplan > Assessment > Soliris'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Ongoing Questionnaire: Soliris'
	open: 'read'
