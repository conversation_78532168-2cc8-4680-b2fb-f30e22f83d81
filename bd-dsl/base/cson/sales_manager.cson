fields:
	rule_hash:
		view:
			label: 'Ignore, rule hash'
			offscreen: true
			readonly: true
			columns: 3

	type:
		model:
			default: 'IG'
			source:['IG', 'Hemo']
			if:
				'IG':
					fields: ['seed_date', 'seed_date2', 'goals', 'goals_hist', 'last_ytd']
				'Hemo':
					fields: ['seed_date3']
		view:
			control: 'radio'
			label: 'Type?'
			columns: 3

	seed_date:
		model:
			type: 'datetime'
			required: false
		view:
			note: 'Should be a Friday at EOD, auto-updates'
			label: 'Seed Date'
			label: 'Weekly Sales Report Seed Date'
			columns: 3

	seed_date2:
		model:
			type: 'date'
			required: false
		view:
			note: 'The date to kick off the quarterly goal metrics'
			label: 'Seed Date 2'
			template: '{{now}}'
			columns: 3

	seed_date3:
		model:
			type: 'datetime'
			required: false
		view:
			note: 'Should be a Friday at EOD, auto-updates'
			label: 'Hemo Report Seed Date'
			columns: 3

	last_email:
		model:
			type: 'datetime'
		view:
			label: 'Last Summary Email'
			readonly: true
			columns: 3

	manager_name:
		model:
			required: true
		view:
			label:'Manager Name'
			columns: 3

	manager_phone:
		model:
			required: true
			max: 21
		view:
			format: 'us_phone'
			label: 'Manager Cell Number'
			columns: 3

	manager_email:
		model:
			max: 64
			min: 6
			required: false
		view:
			label: 'Manager Email Address'
			note: 'Must be a valid email address'
			columns: 3
			validate: [
					name: 'EmailValidator'
			]

	rep_id:
		model:
			required: true
			multi:true
			source: 'user'
			type: 'int'
			sourcefilter:
				sales_code:
					'static': '!null'
		view:
			label: 'Sales Reps'
			columns: -1

	last_ytd:
		model:
			default: 'No'
			source:['No', 'Yes']
		view:
			control: 'radio'
			label: 'Include Last YTD Revenue?'
			columns: 3

	goals:
		model:
			subfields:
				salescode:
					label: 'Sales Code'
				ig_gram:
					label: 'IG Grams Goal'
					type: 'decimal'
				ig_gram_met:
					label: 'IG Grams Met'
					type: 'decimal'
					readonly: true
				revenue:
					label: 'Revenue Goal'
					type: 'decimal'
				revenue_met:
					label: 'Revenue Met'
					type: 'decimal'
					readonly: true
				comment:
					label: 'Comment'
					type: 'text'
			type: 'json'
		view:
			control: 'grid'
			label: 'Quarterly Goals'

	goals_hist:
		model:
			subfields:
				date:
					type: 'date'
					label: 'Date'
				salescode:
					label: 'Sales Code'
				ig_gram:
					label: 'IG Grams Goal'
					type: 'decimal'
				ig_gram_met:
					label: 'IG Grams Met'
					type: 'decimal'
				revenue:
					label: 'Revenue Goal'
					type: 'decimal'
				revenue_met:
					label: 'Revenue Met'
					type: 'decimal'
				comment:
					label: 'Comment'
					type: 'text'
			type: 'json'
		view:
			control: 'grid'
			label: 'Goals History'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	reportable: true
	bundle: ['setup']
	name: '{manager_name} ({type})'
	sections:
		'Sales Manager':
			fields: [ 'type', 'seed_date','seed_date2', 'seed_date3', 'manager_name', 'manager_phone',
			'manager_email', 'rep_id', 'last_email', 'goals', 'goals_hist']
		'Weekly Report Options':
			fields: ['last_ytd']

view:
	comment: 'Manage > Sales Manager'
	find:
		basic: ['manager_name', 'rep_id']
	grid:
		fields: ['seed_date','manager_name', 'type', 'rep_id', 'last_email']
		sort: ['-seed_date']
	label: 'Sales Manager'
