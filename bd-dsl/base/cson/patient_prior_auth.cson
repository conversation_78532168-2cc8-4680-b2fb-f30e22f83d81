fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	external_id:
		view:
			label: 'External ID'
			columns: 3
			readonly: true
			offscreen: true

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	insurance_id:
		model:
			source: 'patient_insurance'
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
				type_id:
					'static': '!SELF'
				billing_method_id:
					'static': ['mm', 'cms1500', 'ncpdp']
		view:
			columns: 2
			label: 'Insurance'
			class: 'select_prefill'
			transform: [{
					name: 'LoadExpectedPrice'
			},
			{
				name: 'SelectPrefill'
				url: '/form/patient_insurance/?limit=1&filter=id:'
				fields:
					'billing_method_id': ['billing_method_id']
			}
			]

	auth_type:
		model:
			required: true
			default: 'Initial'
			source: ['Initial', 'Renewal']
		view:
			columns: 2
			control: 'radio'
			label: 'Type'

	billing_method_id:
		model:
			source: 'list_billing_method'
			sourceid: 'code'
			if:
				'ncpdp':
					prefill:
						pa_type: ['Drug']
					readonly:
						fields: ['pa_type']
		view:
			label: 'Billing Method'
			offscreen: true
			readonly: true

	status_id:
		model:
			required: true
			default: '1'
			source: 'list_pa_status'
			sourceid: 'code'
			if:
				'1': # Pending
					fields: ['clinic_docs_requested', 'request_id']
					require_fields: ['clinic_docs_requested']
				'5': # Approved
					sections: ['Prior Auth Billing Information', 'Limits']
					trigger: ['pa_type']
					require_fields: ['submission_datetime', 'rental_coverage_type']
				'6': # Denied
					fields: ['denied_datetime', 'denial_letter', 'submission_datetime', 'submission_method']
				'2': # Submitted PA
					fields: ['submission_datetime', 'submission_method', 'request_id']
				'3': # Submitted CoverMyMeds
					fields: ['submission_datetime', 'submission_method', 'cmm_number', 'request_id']
				'4': # PA Forms Received
					fields: ['submission_datetime', 'submission_method', 'request_id']
				'9': # Appealing
					fields: ['denied_datetime', 'denial_letter', 'submission_datetime', 'submission_method', 'request_id']
				'*':
					fields: ['request_id']
		view:
			class: 'status'
			columns: 2
			control: 'select'
			label: 'Status'
			findfilter: '!7'
			transform: [
				{
					name: 'LoadExpectedPrice'
				},
				{
					name: 'PrefillCurrentDateTime'
					condition:
						status_id: '6'
					dest: 'denied_datetime'
				},
				{
					name: 'PrefillCurrentDateTime'
					condition:
						status_id: '!1'
					dest: 'submission_datetime'
				}
			]

	pa_type:
		model:
			required: true
			multi: true
			default: ['Drug']
			source: ['Drug', 'Nursing', 'DME', 'Supplies']
			if:
				'Drug':
					prefill:
						drug_approval_date: '{{now}}'
					fields: ['hc_id', 'dose_approved', 'limits', 'drug_approval_date', 'drug_effective_date']
				'Nursing':
					prefill:
						nurse_approval_date: '{{now}}'
					fields: ['nc_id', 'nursing_limits', 'nurse_approval_date', 'nurse_effective_date']
				'DME':
					prefill:
						dme_approval_date: '{{now}}'
					sections: ['Rental']
					fields: ['dme_approval_date', 'dme_effective_date']
				'Supplies':
					fields: ['sc_id','supplies_approval_date', 'supplies_effective_date']
					prefill:
						supplies_approval_date: '{{now}}'
		view:
			columns: 2
			label: 'Type'

	submission_datetime:
		model:
			type: 'datetime'
			required: true
		view:
			columns: 4
			label: 'Date Submitted/Time'

	submission_method:
		model:
			source: ['Phone', 'Online Portal', 'Fax', 'Email']
		view:
			columns: 2.1
			control: 'radio'
			label: 'Mode of Submission'

	contact_phone:
		model:
			max: 21
		view:
			columns: 4
			format: 'us_phone'
			label: 'Telephone #'

	contact_fax:
		model:
			max: 21
		view:
			columns: 4
			format: 'us_phone'
			label: 'Fax #'

	contact_representative:
		view:
			columns: 2
			label: 'Representative'

	request_id:
		view:
			columns: 4
			label: 'Pending PA/Request ID:'

	clinic_docs_requested:
		model:
			source: ['Yes', 'No']
			if:
				'Yes':
					fields: ['clinic_doc_status_id']
					require_fields: ['clinic_doc_status_id']
		view:
			columns: 2
			control: 'radio'
			label: 'Are additional PA clinical docs required?'

	clinic_doc_status_id:
		model:
			multi: false
			source: 'list_pa_clinical_doc_status'
			sourceid: 'code'
			if:
				'1':
					fields: ['clc_dc_id']
		view:
			columns: 2
			label: 'PA Clinical Docs Status'

	clc_dc_id:
		model:
			multi: true
			source: 'list_pa_clinical_doc'
			sourceid: 'code'
		view:
			columns: 2
			control: 'select'
			label: 'Documentation/ Labs Requested'

	denied_datetime:
		model:
			type: 'datetime'
			required: true
		view:
			columns: 4
			label: 'Denial Date/Time'

	denial_letter:
		model:
			required: true
			source: ['Yes', 'No']
			if:
				'Yes':
					fields: ['denial_letter_status_id']
		view:
			columns: 4
			control: 'radio'
			label: 'Denial letter requested?'

	denial_letter_status_id:
		model:
			required: true
			multi: false
			source: 'list_pa_denial_letter_status'
			sourceid: 'code'
			if:
				'2':
					fields: ['pa_denied_id', 'appeal_eligible']
		view:
			columns: 2
			label: 'Denial letter Status'

	pa_denied_id:
		model:
			multi: false
			source: 'list_pa_denied_reason'
			sourceid: 'code'
		view:
			columns: 2
			label: 'PA Denied for'

	appeal_eligible:
		model:
			source: ['Yes', 'No']
			if:
				'Yes':
					fields: ['appeal_docs', 'appeal_datetime']
					prefill:
						status_id: '9'
		view:
			columns: 4
			control: 'radio'
			label: 'Appeal eligible?'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						appeal_eligible: 'Yes'
					dest: 'appeal_datetime'
				}
			]

	appeal_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 4
			label: 'Appeal Submission Date/Time'

	appeal_docs:
		model:
			required: true
			source: ['Yes', 'Not Needed']
			if:
				'Yes':
					fields: ['appeal_docs_status_id']
				'Not Needed':
					fields: ['appeal_letter_status_id']
		view:
			columns: 4
			control: 'radio'
			label: 'Additional Appeal docs requested?'

	appeal_docs_status_id:
		model:
			multi: false
			source: 'list_pa_appeal_doc_status'
			sourceid: 'code'
			if:
				'2 - Received':
					fields: ['appeal_letter_status_id']
		view:
			columns: 2
			label: 'Appeal Docs Status'

	appeal_letter_status_id:
		model:
			multi: false
			default: '1'
			source: 'list_pa_appeal_letter_status'
			sourceid: 'code'
			if:
				'2':
					fields: ['appeal_status_id']
				'4':
					fields: ['appeal_status_id']
		view:
			columns: 2
			label: 'Letter of Appeal Status'

	appeal_status_id:
		model:
			source: 'list_pa_appeal_status'
			sourceid: 'code'
			if:
				'2':
					note: 'Change status to Approved and document authorization number and expiration date'
				'3':
					fields: ['appeal_denied_upheld_rsn_id']
		view:
			columns: 2
			label: 'Appeal Status'
	
	appeal_denied_upheld_rsn_id:
		model:
			source: 'list_pa_appeal_denied_upheld_rsn'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Appeal Denied/Upheld for'

	cmm_number:
		model:
			required: true
		view:
			label: 'CoverMyMeds Key Number'
			columns: 4

	comment:
		model:
			type: 'text'
		view:
			control: 'area'
			label: 'Comments'
			columns: 2

# Prior Auth Info
	approval_method:
		model:
			source: ['Phone', 'Online Portal', 'Fax', 'Email']
		view:
			columns: 2
			control: 'radio'
			label: 'Mode of Approval'

	approval_letter_requested:
		model:
			required: true
			source: ['Yes', 'No']
		view:
			columns: 2
			control: 'radio'
			label: 'Approval letter requested?'

	drug_approval_date:
		model:
			type: 'date'
			required: true
		view:
			columns: -4
			label: 'Date Approved'
			validate: [
				{
					name: 'CopyForwardValue'
					overwrite: true
					field: ['nurse_approval_date', 'dme_approval_date', 'supplies_approval_date']
				}
			]

	drug_effective_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			label: 'Effective Date'
			validate: [
				{
					name: 'CopyForwardValue'
					field: ['nurse_effective_date', 'dme_effective_date', 'supplies_effective_date']
					overwrite: true
				}
			]

	dose_approved:
		view:
			columns: 4
			label: 'Dose Approved'

	hc_id:
		model:
			multi: true
			source: 'list_fdb_medicare_desc'
			sourceid: 'code'
		view:
			columns: 2
			label: 'J-Codes Approved'

	nurse_approval_date:
		model:
			type: 'date'
			required: true
		view:
			columns: -4
			label: 'Date Approved'

	nurse_effective_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			label: 'Effective Date'

	nc_id:
		model:
			multi: true
			source: 'list_billing_code'
			sourceid: 'code'
			sourcefilter:
				code_type:
					'static': ['T-Code', 'CPT']
		view:
			columns: 2
			label: 'Nursing Codes Approved'

	dme_approval_date:
		model:
			type: 'date'
			required: true
		view:
			columns: -4
			label: 'Date Approved'

	dme_effective_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			label: 'Effective Date'

	supplies_approval_date:
		model:
			type: 'date'
			required: true
		view:
			columns: -4
			label: 'Date Approved'

	supplies_effective_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			label: 'Effective Date'

	sc_id:
		model:
			multi: true
			source: 'list_billing_code'
			sourceid: 'code'
			sourcefilter:
				code_type:
					'static': ['A-Code', 'S-Code', 'B-Code', 'K-Code', 'G-Code']
		view:
			columns: 2
			label: 'Supply Codes Approved'

	number:
		model:
			required: true
		view:
			columns: 4
			label: 'Authorization Number'

	effective_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 4
			label: 'Effective Date'

	# TODO: Need to add events to a billing calendar to track expirations
	expire_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 4
			label: 'Expiration Date'

	follow_up_date:
		model:
			type: 'date'
		view:
			label: 'Follow Up Date'
			columns: 4

	limits:
		model:
			multi: false
			required: true
			source: ['Total Units', 'Units/Frequency', 'Fills', 'No Limits']
			if:
				'Total Units':
					fields: ['unit_limit']
				'Units/Frequency':
					fields: ['unit_limit', 'limit_freq']
				'Fills':
					fields: ['refills_limit']
		view:
			columns: 2
			class: 'checkbox checkbox-2'
			control: 'checkbox'
			label: 'Drug Limits'

	unit_limit:
		model:
			required: true
			type: 'int'
			min: 1
		view:
			columns: 4
			label: 'Billable Units Approved'

	limit_freq:
		model:
			multi: false
			required: true
			source: ['Day', 'Week', 'Month', 'Quarter', 'Bi-Annual', 'Annual']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Limit Frequency'

	refills_limit:
		model:
			required: true
			type: 'int'
		view:
			columns: 4
			label: '# Fills Limit'

	nursing_limits:
		model:
			multi: false
			required: true
			source: ['Number of Visits', 'Number of Visits/Frequency', 'No Limits']
			if:
				'Number of Visits/Frequency':
					fields: ['visit_limit', 'visit_limit_freq']
				'Number of Visits':
					fields: ['visit_limit']
		view:
			columns: 2
			class: 'checkbox'
			label: 'Nursing Limits'

	visit_limit:
		model:
			required: true
			type: 'int'
		view:
			columns: 4
			label: 'Visits Limit'

	visit_limit_freq:
		model:
			multi: false
			required: true
			source: ['Day', 'Week', 'Month', 'Quarter', 'Bi-Annual', 'Annual']
		view:
			columns: 2
			class: 'checkbox'
			label: 'Visits Limit Frequency'

	expected_price:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Exp Price $'

	use_negotiated_rate:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Use negotiated rate?'

	billing_comment:
		model:
			type: 'text'
		view:
			control: 'area'
			label: 'Billing Comments'
			columns: 2

	rental_coverage_type:
		model:
			required: true
			source: ['Rental', 'Purchase']
			if:
				'Rental':
					fields: ['rental_price_approved']
				'Purchase':
					fields: ['rental_purchase_price_approved']
		view:
			columns: 4
			control: 'radio'
			label: 'Rental Coverage Type'

	rental_purchase_price_approved:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Approv Pur Price $'

	rental_price_approved:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			note: 'per unit'
			label: 'Approv Rent Price $'

	#TODO Add a calendar for expiring prior auths
	calendar_id:
		model:
			source: 'calendar'
			type: 'int'
			transform: [
					name: 'AutoNameFk'
					]
			default: 10
		view:
			label: 'Calendar'
			offscreen: true
			readonly: true

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'patient_prior_auth'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Other Form'
					direct_attachment: 'Yes'
					form_code: '{code}'
					form_name: 'patient_prior_auth'
					source: 'Scanned Document'
					patient_id: '{patient_id}'
					form_filter: 'patient_prior_auth'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true

model:
	access:
		create:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'payer', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'payer', 'physician']
		request:    []
		update:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
			['hc_id']
			['nc_id']
			['status_id']
		]
		unique: [
			['code']
		]
	reportable: true
	prefill:
		patient:
			link:
				id: 'patient_id'
	name: '{insurance_id_auto_name} {date} {status_id}'

	sections_group: [
		'Prior Auth Billing Information':
			hide_header: true
			indent: false
			tab: 'Approval'
			fields: ['approval_method', 'approval_letter_requested',
			'drug_approval_date', 'drug_effective_date', 'dose_approved', 'hc_id', 
			'nurse_approval_date', 'nurse_effective_date', 'nc_id',
			'dme_approval_date', 'dme_effective_date',
			'supplies_approval_date', 'supplies_effective_date', 'sc_id',
			'number', 'effective_date', 'expire_date', 'follow_up_date']
		'Limits':
			hide_header: true
			indent: false
			tab: 'Approval'
			fields: ['limits', 'unit_limit', 'refills_limit', 'nursing_limits', 'visit_limit',
			'visit_limit_freq', 'limit_freq', 'expected_price', 'use_negotiated_rate', 'billing_comment']
		'Rental':
			hide_header: true
			indent: false
			tab: 'Approval'
			fields: ['rental_coverage_type', 'rental_price_approved', 'rental_purchase_price_approved']
		'Prior Authorization':
			hide_header: true
			indent: false
			tab: 'Authorization'
			fields: ['insurance_id', 'auth_type', 'billing_method_id', 'status_id', 'pa_type',
			'clinic_docs_requested', 'clinic_doc_status_id', 'clc_dc_id',
			'denied_datetime','denial_letter', 'denial_letter_status_id',
			'pa_denied_id', 'appeal_eligible', 'appeal_datetime', 'appeal_docs',
			'appeal_docs_status_id', 'appeal_letter_status_id',
			'appeal_status_id', 'appeal_denied_upheld_rsn_id',
			'submission_datetime', 'submission_method', 'contact_phone', 'contact_fax', 'contact_representative',
			'request_id', 'cmm_number', 'comment']

		'Documents':
			hide_header: true
			indent: false
			fields: ['code', 'embed_document']
			tab: 'Assigned Documents'

		]
	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Prior Authorization"
	]

view:
	dimensions:
        width: '75%'
        height: '75%'
	hide_cardmenu: true
	comment: 'Patient > Prior Authorization'
	grid:
		fields: ['created_on','insurance_id', 'status_id', 'number', 'expire_date', 'comment']
		sort: ['-created_on']
	find:
		basic: ['status_id', 'insurance_id']

	label: 'Prior Authorization'
	open: 'read'