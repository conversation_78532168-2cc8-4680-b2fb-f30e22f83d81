fields:
	name:
		model:
			max: 128
			required: true
		view:
			label: 'Intervention'
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ['manage','patient-form']
	indexes:
		unique: [
			['name']
		]
	name: ['name']
	sections:
		'Main':
			fields: ['name']

view:
	hide_cardmenu: true
	comment: 'Manage > Care Plan Interventions'
	find:
		basic: ['name']
	grid:
		fields: ['name']
		sort: ['name']
	label: 'Care Plan Interventions'
