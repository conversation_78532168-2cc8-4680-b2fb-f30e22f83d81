fields:

	npi:
		model:
			source: 'list_cms_npi'
			sourceid: 'code'
		view:
			columns: 2
			label: 'NPI'
			findunique: true

	healthcare_provider_taxonomy_group:
		view:
			columns: 2
			label: 'Healthcare Provider Taxonomy Group'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		many: [
			['npi']
		]
	name: ['npi']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['npi', 'healthcare_provider_taxonomy_group']

view:
	comment: 'Manage > Billing Healthcare Taxonomy Group'
	find:
		basic: ['npi']
	grid:
		fields: ['npi', 'healthcare_provider_taxonomy_group']
	label: 'Billing Healthcare Taxonomy Group'
