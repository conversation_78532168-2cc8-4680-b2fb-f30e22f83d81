fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Stelara specific questions
	injection_training:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Will patient need Stelara injection training/nurse support?'

	self_injectable:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient eligible for self-injection?'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Stelara Training':
				fields: ['injection_training', 'self_injectable']
view:
	comment: 'Patient > Careplan > Assessment > Stelara'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Stelara'
	open: 'read'
