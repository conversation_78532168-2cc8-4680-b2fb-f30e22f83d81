fields:

	dx_id:
		model:
			multi: true
			required: true
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Diagnosis'

	assessment_code:
		model:
			required: true
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			control: 'select'
			label: 'Assessment Code'
			columns: 2

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 2

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 2
model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['workflow']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['dx_id']
		]
	name: ['dx_id']
	sections:
		'Disease Assessment Map':
			fields: ['dx_id', 'assessment_code', 'allow_sync', 'active']

view:
	comment: 'Manage > Disease Assessment Map'
	find:
		basic: ['dx_id']
	grid:
		fields: ['dx_id', 'assessment_code']
		sort: ['dx_id']
	label: 'Disease Assessment Map'
	open: 'read'
