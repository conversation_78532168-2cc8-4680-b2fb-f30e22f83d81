fields:

	health_care_claim_status_category_code:
		view:
			columns: -4
			label: 'Health Care Claim Status Category Code'
			note: 'STC01-1, STC010-1, STC11-1'
			readonly: true
			_meta:
				path: 'healthCareClaimStatusCategoryCode'

	health_care_claim_status_category_code_value:
		view:
			columns: 2
			label: 'Health Care Claim Status Category Code Value'
			note: 'STC01-1, STC010-1, STC11-1'
			readonly: true
			_meta:
				path: 'healthCareClaimStatusCategoryCodeValue'

	status_information_effective_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Status Information Effective Date'
			reference: 'Loop 2200C STC02'
			readonly: true

	status_code:
		view:
			columns: -4
			label: 'Status Code'
			note: 'STC01-2, STC010-2, STC11-2'
			readonly: true
			_meta:
				path: 'statusCode'

	status_code_value:
		view:
			columns: 2
			label: 'Status Code Value'
			note: 'STC01-2, STC010-2, STC11-2'
			readonly: true
			_meta:
				path: 'statusCodeValue'

	entity_identifier_code:
		view:
			columns: -4
			label: 'Entity Identifier Code'
			note: 'STC01-3, STC010-3, STC11-3'
			readonly: true
			_meta:
				path: 'entityIdentifierCode'

	entity_identifier_code_value:
		view:
			columns: 4
			label: 'Entity Identifier Code Value'
			note: 'STC01-3, STC010-3, STC11-3'
			readonly: true
			_meta:
				path: 'entityIdentifierCodeValue'

	ncpdp_reject_payment_codes:
		view:
			columns: 4
			label: 'NCPDP Reject Payment Codes'
			note: 'STC01-4, STC010-4, STC11-4'
			readonly: true
			_meta:
				path: 'nationalCouncilForPrescriptionDrugProgramsRejectPaymentCodes'

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: 'Claim Status History'
	indexes:
		many: [
			['health_care_claim_status_category_code']
			['status_code']
			['entity_identifier_code']
		]
	sections:
		'Claim Status History':
			hide_header: true
			fields: ['health_care_claim_status_category_code', 'health_care_claim_status_category_code_value',
			'status_information_effective_date', 'status_code', 'status_code_value', 'entity_identifier_code', 
			'entity_identifier_code_value', 'ncpdp_reject_payment_codes']

view:
	dimensions:
		width: '85%'
		height: '55%'
	hide_cardmenu: true
	comment: 'Claim Status History'
	grid:
		fields: ['status_information_effective_date', 'status_code_value', 'health_care_claim_status_category_code_value', 'ncpdp_reject_payment_codes']
		label: ['Status', 'Status Category', 'NCPDP Reject Payment Codes']
		width: [15, 30, 25, 30]
		sort: ['-created_on']
	label: 'Claim Status History'
	open: 'read'