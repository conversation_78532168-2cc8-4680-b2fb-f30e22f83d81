fields:

	last_verification_status:
		view:
			label: 'Last Verification Status'
			readonly:true
			offscreen:true

	last_verification_req_id:
		view:
			label: 'Last Verification Request ID'
			readonly:true
			offscreen:true

	last_verification_sent_date:
		model:
			type: 'date'
		view:
			label: 'Last Verification Sent Date'
			readonly:true
			offscreen:true

	last_verification_file:
		view:
			label: 'Last Verification File'
			readonly: true
			offscreen: true

	dos_start_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			control: 'input'
			label: 'Svc Start Date'
			template: '{{now}}'

	dos_end_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			control: 'input'
			label: 'Svc End Date'
			template: '{{now}}'

	patient_id:
		model:
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	reference_id:
		model:
			type: 'text'
		view:
			label: 'MRN'
			readonly: true
			offscreen: true

	#ask patrick - <PERSON>: you didn't ask me - Shoaib: you ignored my texts
	internal_id:
		model:
			required: false
			type: 'text'
		view:
			label: 'Internal ID'
			readonly: true
			offscreen: true

	include_text_response:
		model:
			default: 'False'
			source: ['True', 'False']
		view:
			columns: 4
			control: 'radio'
			label: 'Include Full Text Response?'

	patient_insurance_id:
		model:
			source: 'patient_insurance'
			type: 'int'
		view:
			columns: -4
			label: 'Patient Insurance ID'
			readonly: true

	# On the basis of payer_id - optional
	payer_name:
		model:
			type: 'text'
		view:
			columns: 4
			label: 'Payer Name'

	#ask patrick
	#pi.payer_id.pverify_payer_id Required
	payer_code:
		model:
			type: 'text'
			required: true
		view:
			columns: 4
			label: 'Payer PVerify Code'

	#ask patrick all prover
	provider_first_name:
		model:
			type: 'text'
		view:
			label: 'Provider First Name'
			readonly: true
			offscreen: true

	provider_middle_name:
		model:
			type: 'text'
		view:
			label: 'Provider Middle Name'
			readonly: true
			offscreen: true

	# patient.site_id.name Required
	provider_last_name:
		model:
			type: 'text'
			required: true
		view:
			label: 'Provider Last Name'
			readonly: true
			offscreen: true

	# patient.site_id.npi Required
	provider_npi:
		model:
			type: 'text'
			required: true
		view:
			columns: -4
			label: 'Site NPI'

	provider_pin:
		model:
			type: 'text'
			required: false
		view:
			columns: 4
			label: 'Site Medi-Cal PIN'

	provider_taxonomy:
		model:
			source: 'list_nucc_taxonomy'
			sourceid: 'code'
		view:
			columns: 4
			label: 'Taxonomy'

	practice_type_code:
		model:
			source: 'list_pverify_practice_type'
			sourceid: 'code'
		view:
			columns: 4
			label: 'Practice Type Code'

	#patient.firstname
	subscriber_first_name:
		model:
			type: 'text'
			required: true
		view:
			columns: -4
			label: 'Subscriber First Name'

	#patient.lastname
	subscriber_last_name:
		model:
			type: 'text'
			required: true
		view:
			columns: 4
			label: 'Subscriber Last Name'

	#pi.cardholder_id
	subscriber_member_id:
		model:
			type: 'text'
			required: true
		view:
			columns: 4
			label: 'Subscriber Member Id'

	subscriber_dob:
		model:
			type: 'date'
			required: true
		view:
			control: 'input'
			columns: 4
			label: 'Subscriber DOB'

	subscriber_ssn:
		model:
			type: 'text'
		view:
			label: 'Subscriber SSN'
			validate: [
					name: 'SSNValidator'
			]

	#pi.medical_relationship_id == 18 True add if logic
	is_subscriber_patient:
		model:
			source: ['True', 'False']
			if:
				'True':
					fields:['subscriber_first_name', 'subscriber_last_name', 'subscriber_dob', 'subscriber_ssn']
				'False':
					sections: ['Dependent']
		view:
			offscreen: true
			readonly: true
			control: 'radio'
			label: 'Is Subscriber Patient?'

	dependent_relation_with_subscriber:
		model:
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR02'
		view:
			columns: 4
			label: 'Relationship to Subscriber'

	#pi.beneficiary_dob
	dependent_patient_dob:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			control: 'input'
			label: 'Dependent DOB'

	dependent_patient_gender:
		model:
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'DMG03'
		view:
			columns: 4
			label: 'Dependent Gender'

	#pi.beneficiary_fname
	dependent_patient_first_name:
		model:
			type: 'text'
			required: true
		view:
			columns: -4
			label: 'Dependent First Name'

	dependent_patient_middle_name:
		model:
			type: 'text'
		view:
			columns: 4
			label: 'Dependent Middle Name'

	#pi.beneficiary_lname
	dependent_patient_last_name:
		model:
			type: 'text'
			required: true
		view:
			columns: 4
			label: 'Dependent Last Name'

	#patient.site_id.name
	location:
		model:
			type: 'text'
			required: false
		view:
			label: 'Location'
			readonly: true
			offscreen: true

	#ask patrick
	customer_id:
		model:
			type: 'text'
		view:
			label: 'Customer Id'
			readonly: true
			offscreen: true

	response_json_data:
		model:
			type: 'json'
		view:
			label: 'Response (Json)'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'pharm']
	indexes:
		unique: ['patient_id']
	sections_group: [
		'Service Dates':
			fields: ['dos_start_date','dos_end_date', 'reference_id', 'internal_id',
			'include_text_response', 'patient_id','location', 'customer_id', 'response_json_data']
		'Payer':
			fields: ['patient_insurance_id', 'payer_name', 'payer_code', 'provider_first_name',
			'provider_middle_name', 'provider_last_name', 'provider_npi',
			'provider_pin', 'provider_taxonomy', 'practice_type_code']
		'Subscriber':
			fields: ['subscriber_first_name', 'subscriber_last_name', 'subscriber_member_id',
			'subscriber_dob', 'subscriber_ssn', 'is_subscriber_patient']
		'Dependent':
			fields: ['dependent_relation_with_subscriber',
			'dependent_patient_dob', 'dependent_patient_gender', 'dependent_patient_first_name',
			'dependent_patient_middle_name', 'dependent_patient_last_name']
	]
	name: '{reference_id} : {provider_last_name}'

view:
	comment: 'Patient > Check Eligibility'
	find:
		basic: ['reference_id']
	grid:
		fields: ['reference_id']
	label: 'Patient Check Eligibility'
