fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	user_id:
		model:
			required: true
			source: 'user'
			sourcefilter:
				role:
					static: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		view:
			label: 'Scheduled For'
			template: '{{user.id}}'

	calendar_id:
		model:
			source: 'calendar'
			type: 'int'
			transform: [
					name: 'AutoNameFk'
					]
		view:
			label: 'Calendar'

	appointment_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Scheduled Date'

	appointment_start:
		model:
			required: true
			type: 'time'
		view:
			label: 'Start Time'

	appointment_end:
		model:
			type: 'time'
		view:
			label: 'End Time'

	appointment_status:
		model:
			default: '1 - Scheduled'
			source:['1 - Scheduled','2 - Confirmed', '3 - Need Orders', '4 - Hold/Pending', '5 - Missed', '6 - Canceled']
			if:
				'5 - Missed':
					fields: ['missed_reason']
				'6 - Canceled':
					fields: ['missed_reason']
		view:
			label: 'Appointment Status'

	missed_reason:
		model:
			source:['Sick', 'Insurance', 'Hospitalized', 'MD Appointment', 'No Show', 'Inactive Orders', 'Other']
			if:
				'Other':
					fields: ['missed_reason_other']
		view:
			label: 'Missed/Canceled Reason'

	missed_reason_other:
		model:
			required: true
		view:
			label: 'Missed/Canceled Reason Other'

	completed_on:
		model:
			required: true
			type: 'date'
		view:
			label: 'Completed On'

	notes:
		model:
			max: 512
		view:
			control: 'area'
			label: 'Notes'
			note: 'Not visible to patients / physicians'

	# Status
	drug_shipped:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Drug Shipped?'

	active:
		model:
			max: 3
			min: 2
			required: true
			default: 'Yes'
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Schedule Active?'

	portal_display:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			default: 'Yes'
		view:
			control: 'radio'
			note: 'Shows up as "Upcoming Nursing Visit"'
			label: 'Display on Patient Portal?'

	reoccur_appointments:
		model:
			multi: false
			source: 'patient_sch_enc_rpt_appt'
			type: 'subform'
		view:
			label: 'Reoccurred appointment'
			note: 'Set appointments that can be reoccurred'

	sh_reoccur:
		model:
			max: 3
			min: 2
			required: false
			default: 'No'
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['reoccur']
		view:
			control: 'radio'
			label: 'Hide/Show Reoccur?'
			offscreen: true

	reoccur:
		model:
			max: 3
			min: 2
			required: false
			default: 'No'
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['Setup Repeat Schedule Appointments']
					fields: ['reoccur_appointments']
					field_label:
						appointment_date: 'Schedule Start'
				'No':
					field_label:
						appointment_date: 'Scheduled Date'
		view:
			control: 'radio'
			label: 'Schedule Repeat Appointments?'

	pat_sch_enc:
		model:
			type: 'int'
		view:
			label: 'patient schedule encounter'
			offscreen: true

	encounter_id:
		model:
			type: 'int'
		view:
			label: 'Linked Encounter ID'
			offscreen: true
			readonly: true

	ical:
		view:
			control: 'area'
			readonly: true
			offscreen: true
			label: 'iCal Recurring Rule'

	schedule_period:
		model:
			required: true
			source: ['Daily', 'Weekly', 'Monthly']
			if:
				'Daily':
					fields: ['schedule_repeat_daily']
				'Weekly':
					fields: ['schedule_repeat_weekly', 'schedule_repeat_on']
				'Monthly':
					fields: ['schedule_repeat_monthly', 'schedule_repeat_by']
		view:
			control: 'radio'
			label: 'Repeats'

	schedule_repeat_daily:
		model:
			default: 1
			required: true
			type: 'int'
		view:
			label: 'Repeat Every'
			note: 'Days'

	schedule_repeat_weekly:
		model:
			default: 1
			required: true
			type: 'int'
		view:
			label: 'Repeat Every'
			note: 'Weeks'

	schedule_repeat_monthly:
		model:
			default: 1
			required: true
			type: 'int'
		view:
			label: 'Repeat Every'
			note: 'Months'

	schedule_repeat_on:
		model:
			multi:true
			required: true
			source: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
		view:
			control: 'checkbox'
			label: 'Repeat On'

	schedule_repeat_by:
		model:
			required: true
			source: ['Day of the Month', 'Day of the Week']
		view:
			control: 'radio'
			label: 'Repeat on the Same'

	nxt_visit_reminder:
		model:
			type: 'date'
		view:
			offscreen: true

	schedule_end:
		model:
			type: 'date'
		view:
			label: 'End Date'
			validate: [
				name: 'ScheduledEndValidate'
			]
	override_event_date:
		view:
			offscreen: true
			label: 'Override Event Date'

	override_appointment_start:
		view:
			offscreen: true
			label: 'Override Start Time'

	hide_reoccur:
		model:
			default: 'No'
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['reoccur']
		view:
			offscreen: true

	travel_time_total:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			readonly: true
			label: 'Total Travel Time (hrs)'

	time_total:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			readonly: true
			label: 'Total Visit Time (hrs)'

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'careplan_schedule_encounter'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Other Form'
					direct_attachment: 'Yes'
					source: 'Scanned Document'
					form_name: 'careplan_schedule_encounter'
					form_code: '{code}'
					form_filter: 'careplan_schedule_encounter'
					patient_id: '{patient_id}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	transform: [
			name: 'PermissionAccess'
			arguments:
				user: 'user_id'
				patient: 'patient_id'
				log: false
				name: 'generate_ical_pat_sch_enc'
		]
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	transform: [
		name: 'PermissionAccess'
		arguments:
			user: 'user_id'
			patient: 'patient_id'
			log: false
			

		name: 'RRuleTransform'
		source:
			dtstart: 'appointment_date',
			until: 'schedule_end',
			freq: 'schedule_period',
			interval_daily: 'schedule_repeat_daily',
			interval_weekly: 'schedule_repeat_weekly',
			interval_monthly: 'schedule_repeat_monthly',
			byweekday: 'schedule_repeat_on',
			monthweekday: 'schedule_repeat_by',
			appointment_start: 'appointment_start',
			appointment_end: 'appointment_end'
		destination: 'ical'
		]
	indexes:
		many: [
			['active']
			['appointment_date']
			['patient_id']
			['user_id']
			['appointment_date', 'appointment_start']
		]
		unique: [
			['code']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				patient_id: 'patient_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	name: '{appointment_date:%m}/{appointment_date:%d}/{appointment_date:%y} {appointment_start:%I}:{appointment_start:%M}{appointment_start:%p}'
	sections_group: [
		'Schedule':
				hide_header: true
				indent: false
				fields: ['code', 'ical', 'patient_id', 'user_id', 'calendar_id','sh_reoccur','reoccur', 'pat_sch_enc', 'override_appointment_start', 'override_event_date']
				tab: 'Schedule'
			'Setup Appointment':
				indent: false
				fields: ['appointment_date', 'appointment_start', 'appointment_end', 'appointment_status', 'missed_reason', 'missed_reason_other', 'notes', 'drug_shipped', 'active', 'portal_display']
				tab: 'Schedule'
			'Ignore Repeat Subform':
				indent: false
				fields: ['reoccur_appointments']#subform
			'Setup Repeat Schedule Appointments':
				indent: false
				fields: [
					'schedule_period','schedule_repeat_daily', 'schedule_repeat_weekly', 'schedule_repeat_monthly','schedule_repeat_by',
					'schedule_repeat_on', 'schedule_end'
					]
				tab: 'Schedule'
			'Assigned Documents':
				hide_header: true
				indent: false
				fields: ['embed_document']
				tab: 'Assigned Documents'
	]

view:
	comment: 'Patient > Schedule > Encounter'
	find:
		basic: ['appointment_status', 'user_id']
	grid:
		fields: ['appointment_date', 'appointment_status', 'user_id', 'active', 'notes']
		sort: ['-appointment_date', '-appointment_start']
	label: 'Schedule: Nurse Visits'
	open: 'read'
