fields:

	mid:
		model:
			source: 'list_billing_method'
			sourceid: 'code'
			multi: true
			required: true
		view:
			label: 'Claim Type'

	code:
		model:
			max: 64
			required: true
		view:
			columns: 3
			label: 'Code'

	name:
		model:
			required: true
			max: 128
		view:
			columns: 3
			label: 'Name'
			findunique: true

	short_name:
		model:
			required: true
			max: 12
		view:
			columns: 3
			label: 'Short Name'

	show_in_ar_report:
		model:
			source: ['Yes']
		view:
			columns: 3
			label: 'Show in A/R Summary Report?'
			control: 'checkbox'
			class: 'checkbox-only'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 3
			control: 'radio'
			label: 'Can Sync Record'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 3
			control: 'radio'
			label: 'Active'
model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['status']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['code']
		]
	name: '{code} - {name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['mid', 'code', 'name', 'short_name', 'show_in_ar_report', 'allow_sync', 'active']

view:
	comment: 'Manage > Claim Status'
	find:
		basic: ['mid','code','name', 'short_name']
	grid:
		fields: ['mid','code','name','short_name', 'show_in_ar_report']
		sort: ['code']
	label: 'Claim Status'
	open: 'read'
