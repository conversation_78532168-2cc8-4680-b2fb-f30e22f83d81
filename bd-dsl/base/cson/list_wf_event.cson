fields:
	external_id:
		view:
			label: 'External ID'
			readonly: true
			offscreen: true

	code:
		model:
			max: 128
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 3
			transform: [
					name: 'LowerCase'
			]

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 2
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['workflow']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['code']
		]
	name: '{code}'
	sections_group: [
		'Last Event':
			fields: ['code', 'allow_sync', 'active']
	]
view:
	comment: 'Manage > Workflow > Last Event'
	find:
		basic: ['code']
	grid:
		fields: ['code']
		sort: ['code']
	label: 'Last Event'
	open: 'read'
