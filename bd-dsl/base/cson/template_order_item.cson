fields:

	inventory_id:
		model:
			required: true
			source: 'inventory'
			query: 'select_inv_in_stock'
			querytemplate: 'inventoryTemplate'
			sourcefilter:
				type:
					'static': ['Drug', 'Compound']
				active:
					'static': 'Yes'
			if:
				'*':
					fields: ['rx_template_id']
		view:
			columns: 2
			label: 'Drug'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'route_id': ['route_id'],
					'type_id': ['type_id'],
					'therapy_id': ['therapy_id'],
					'rx_template_id': ['rx_template_id'],
					'hcpc_code': ['hcpc_code'],
					'formatted_ndc': ['formatted_ndc'],
					'manufacturer_id': ['manufacturer_id']
			]

	formatted_ndc:
		view:
			label: 'NDC'
			readonly: true
			columns: -4

	hcpc_code:
		view:
			label: 'HCPC'
			readonly: true
			columns: 4

	manufacturer_id:
		model:
			required: false
			source: 'list_manufacturer'
			sourceid: 'code'
		view:
			label: 'Manufacturer'
			columns: 2
			readonly: true

	route_id:
		model:
			required: false
			source: 'list_route'
			sourceid: 'code'
		view:
			columns: -4
			label: 'Route'

	therapy_id:
		model:
			required: false
			max: 64
			source: 'list_therapy'
			sourceid: 'code'
		view:
			columns: 4
			label: 'Therapy'

	billing_method:
		model:
			required: false
			source: ['Insurance', 'Self Pay', 'Do Not Bill']
			if:
				'Insurance':
					fields: ['bv_flag']
		view:
			columns: 4
			control: 'radio'
			label: 'Billing Method'

	auth_flag:
		model:
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Requires Auth?'
			columns: 4

	bv_flag:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Requires BV?'
			columns: 4

	rx_template_id:
		model:
			source: 'list_rx_template'
			sourceid: 'code'
			sourcefilter:
				active: 
					'static': 'Yes'
				template_type:
					source: ['PO', 'IV', 'Injection', 'Factor', 'Compound', 'TPN']
			required: true
		view:
			columns: 4
			label: 'RX Format'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_rx_template/?limit=1&fields=list&sort=name&page_number=0&filter=code:'
				fields:
					'template_type': ['template_type'],
			]

	template_type:
		model:
			required: false
			source: ['PO', 'IV', 'Injection', 'Factor', 'Compound', 'TPN']
		view:
			label: 'Template Type'
			control: 'radio'
			readonly: true
			offscreen: true

	bill_notes:
		view:
			columns: 3
			note: 'Appears in the pre-invoice review of the work ticket'
			label: 'Billing Notes'
			control: 'area'

	comments:
		view:
			columns: 2
			label: 'Comments'

	type_id:
		model:
			required: true
			default: 'Primary'
			source: 'list_dispense_type'
			sourceid: 'code'
		view:
			columns: 4
			label: 'Type'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['inventory_id']

	indexes:
		many: [
			['therapy_id']
			['inventory_id']
			['type_id']
			['manufacturer_id']
			['hcpc_code']
			['formatted_ndc']
			['rx_template_id']
		]

	sections_group: [

		'Order Info':
			hide_header: true
			indent: false
			fields: ['inventory_id', 'formatted_ndc', 'hcpc_code', 'manufacturer_id', 'route_id', 'therapy_id', 'comments']
			tab: 'Order Info'

		'Rx Info':
			indent: false
			fields: ['type_id', 'rx_template_id', 'template_type']
			tab: 'Order Info'

		'Billing':
			hide_header: true
			indent: false
			fields: ['billing_method']
			tab: 'Billing'

		'Billing Alerts/Notes':
			hide_header: true
			indent: false
			fields: ['bill_notes', 'auth_flag', 'bv_flag']
			tab: 'Billing'

	]

view:
	hide_cardmenu: true
	comment: 'Patient > Order'
	grid:
		fields: ['inventory_id']
		sort: ['-created_on']
	find:
		basic: ['inventory_id']
	label: 'Order'
	open: 'read'
