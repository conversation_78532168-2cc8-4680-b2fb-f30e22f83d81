fields:
	method:
		model:
			source: ['Call', 'Email', 'Letter', 'Other']
			if:
				'Other':
					fields: ['method_description']
		view:
			control: 'radio'
			label: 'Contact Methods'
			columns: 2

	method_description:
		model:
			required: true
		view:
			label: 'Details:'
			control: 'area'
			columns: -1

	gift:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['gift_details']
					sections: ['Courtesy Details']
		view:
			control: 'radio'
			label: 'Gift Included?'
			columns: 3

	gift_details:
		model:
			required: true
		view:
			label: 'Details:'
			control: 'area'
			columns: -1

	response:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['response_rating']
		view:
			control: 'radio'
			label: 'Received Response/Attempt Successful?'
			columns: 3

	response_rating:
		model:
			required: true
			source: ['1', '2', '3', '4', '5']
		view:
			note: '1 = Poor Response - 5 = Great Response'
			control: 'radio'
			label: "Response Rating"
			columns: 3

	notes:
		view:
			control: 'area'
			label: 'Notes'
			columns: -1

	date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Date of Contact'
			template: '{{now}}'
			findrange: true
			columns: 2

	type:
		model:
			required: true
			source: ['Referral Source', 'Non-Referral Source','Patient']
			if:
				'Referral Source':
					fields: ['sales_account_id','phys_in_office','non_presc',
					'non_presc_num','purpose','courtesy_provided','num_individuals',
					'cost','cost_per_provider','tot_year']
				'Non-Referral Source':
					fields: ['company_name', 'employer', 'employer_location',
					'courtesy_provided', 'location',
					'cost', 'individuals', 'topic']
				'Patient':
					fields: ['pat_type', 'pat_id','provided','location','cost',
					'individuals','topic','tot_year_pt']
		view:
			columns: 2
			label: 'Type'
			control: 'radio'

	sales_account_id:
		model:
			required: true
			source: 'sales_account'
			type: 'int'
			if:
				'*':
					fields: ['dr_id']
		view:
			label: 'Sales Account'
			columns: 3

	dr_id:
		model:
			multi: true
			required: true
			source: 'sales_account_physician'
			sourcefilter:
				sales_account_id:
					'dynamic': '{sales_account_id}'
		view:
			label: 'Physicians'
			columns: 3

	pat_type:
		model:
			required: true
			source: ['Active', 'Prospect']
			if:
				'Active':
					require_fields: ['pat_id']
				'Prospect':
					fields: ['pat_name']
		view:
			label: 'Type'
			control: 'radio'
			columns: 3

	pat_id:
		model:
			source: 'patient'
		view:
			label: 'Patient'
			columns: 3

	pat_name:
		model:
			required: true
		view:
			label: 'Patient Name'
			columns: 3

	phys_in_office:
		model:
			required: true
			type: 'int'
		view:
			label: '# of Physicians at Office'
			note: '(that location only)'
			columns: 3

	non_presc:
		model:
			required: false
		view:
			label: 'Non-Physician Prescribers'
			note: '(that location only)'
			columns: 3

	non_presc_num:
		model:
			required: false
			type: 'int'
		view:
			label: '# of Non-Physician Prescribers'
			note: '(that location only)'
			columns: 3
	
	purpose:
		model:
			required: true
		view:
			label: 'Purpose of Meeting'
			columns: 3
	
	courtesy_provided:
		model:
			required: true
		view:
			label: 'Courtesy Provided'
			note: '(include location if other than office)'
			columns: 3

	provided:
		model:
			required: true
		view:
			label: 'What was provided?'
			columns: 3

	num_individuals:
		model:
			required: true
			type: 'int'
		view:
			label: '# of Individuals Present'
			columns: 3

	individuals:
		model:
			required: true
		view:
			label: 'Individuals Present'
			columns: 3

	topic:
		model:
			required: true
		view:
			label: 'Topic of Discussion'
			columns: 3

	company_name:
		model:
			required: true
		view:
			label: 'Company Name'
			columns: 3

	employer:
		model:
			required: true
		view:
			label: 'Employer'
			columns: 3

	employer_location:
		model:
			required: true
		view:
			label: 'Employer Location'
			columns: 3

	location:
		model:
			required: true
		view:
			label: 'Location of Meeting'
			columns: 3

	cost:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Cost'
			columns: 3

	cost_per_provider:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Cost Per Provider'
			readonly: true
			columns: 3

	tot_year:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Total for Year as of this Date'
			readonly: true
			columns: 3

	tot_year_pt:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Total Spent on this Patient this Year'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'liaison']
		delete:     ['admin', 'liaison']
		read:       ['admin', 'liaison']
		read_all:   ['admin', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'liaison']
		write:      ['admin', 'liaison']
	name: ['type', 'date', 'pat_name', 'sales_account_id']
	sections:
		'Contact Details':
			fields: ['date', 'method', 'method_description', 'gift', 'response', 'response_rating', 'gift_details', 'notes']
		'Courtesy Details':
			fields: ['type','sales_account_id', 'dr_id',
					'pat_type', 'pat_name', 'pat_id', 'phys_in_office', 'non_presc', 'non_presc_num',
					'purpose', 'courtesy_provided', 'provided', 'num_individuals',
					'individuals', 'topic','company_name', 'employer', 'employer_location',
					'location', 'cost', 'cost_per_provider', 'tot_year',
					'tot_year_pt']

view:
	comment: 'Sales > Sales Contact Log'
	find:
		basic: ['date', 'method', 'response', 'response_rating', 'notes']
	grid:
		fields:  ['date', 'method', 'response', 'response_rating', 'notes']
		sort: ['date']
	label: 'Sales Contact Log'
	open: 'read'