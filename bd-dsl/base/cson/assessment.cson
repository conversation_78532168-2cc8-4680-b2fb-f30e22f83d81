fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	route_id:
		model:
			required: false
			source: 'list_route'
			sourceid: 'code'
		view:
			readonly: true
			offscreen: true
			label: 'Primary Drug Route'

	delivery_ticket_status:
		model:
			source:
				delivery_ticket: 'Delivery Ticket Creation' # This is a new delivery ticket, not associated with a claim
				ready_to_fill: 'Ready to Fill' # This is a delivery ticket that is ready to be filled and prescription has been verified or started from a refill request
				order_ver: 'Pending Order Verification' # This is a delivery ticket that has been filled and is await pharmacist verification
				pending_conf: 'Pending Confirmation' # This is a delivery ticket that has been filled and is await pharmacist confirmation
				ready_to_bill: 'Ready to Bill' # This is a delivery ticket that has been confirmed by the pharmacist and is ready to be billed
				billed: 'Billed' # This is a delivery ticket that has been billed for all charges on the ticket
				voided: 'Voided' # This is a delivery ticket that has been voided
		view:
			label: 'Status'
			readonly: true
			offscreen: true

	dx_1:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			columns: 2
			readonly: true
			label: 'Primary Diagnosis'
	
	dx_2:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			readonly: true
			offscreen: true
			label: 'Secondary Diagnosis'

	dx_3:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			readonly: true
			offscreen: true
			label: 'Secondary Diagnosis'

	dx_4:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			readonly: true
			offscreen: true
			label: 'Tertiary Diagnosis'

	dx_5:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			readonly: true
			offscreen: true
			label: 'Quaternary Diagnosis'

	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections: ['Factor Bleed Events Log']
					fields: ['subform_bleed_events']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Therapy'

	therapy_2:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections: ['Factor Bleed Events Log']
					fields: ['subform_bleed_events']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Therapy'

	therapy_3:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections: ['Factor Bleed Events Log']
					fields: ['subform_bleed_events']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Therapy'

	therapy_4:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections: ['Factor Bleed Events Log']
					fields: ['subform_bleed_events']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Therapy'

	therapy_5:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections: ['Factor Bleed Events Log']
					fields: ['subform_bleed_events']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Therapy'

	subform_therapy_1:
		model:
			source: 'assessment_{therapy_1}'
			sourcefilter:
				'assessment_factor': {}
				'assessment_aat': {}
				'assessment_ivig': {}
				'assessment_subqig': {}
				'assessment_tnf': {}
				'assessment_tpn': {}
				'assessment_steroid': {}
				'assessment_inotrope': {}
				'assessment_chemotherapy': {}
				'assessment_bisphosphonates': {}
			type: 'subform'
		view:
			label: 'Primary Therapy Assessment'

	subform_therapy_2:
		model:
			source: 'assessment_{therapy_2}'
			sourcefilter:
				'assessment_factor': {}
				'assessment_aat': {}
				'assessment_ivig': {}
				'assessment_subqig': {}
				'assessment_tnf': {}
				'assessment_tpn': {}
				'assessment_steroid': {}
				'assessment_inotrope': {}
				'assessment_chemotherapy': {}
				'assessment_bisphosphonates': {}
			type: 'subform'
		view:
			label: 'Secondary Therapy Assessment'

	subform_therapy_3:
		model:
			source: 'assessment_{therapy_3}'
			sourcefilter:
				'assessment_factor': {}
				'assessment_aat': {}
				'assessment_ivig': {}
				'assessment_subqig': {}
				'assessment_tnf': {}
				'assessment_tpn': {}
				'assessment_steroid': {}
				'assessment_inotrope': {}
				'assessment_chemotherapy': {}
				'assessment_bisphosphonates': {}
			type: 'subform'
		view:
			label: 'Tertiary Therapy Assessment'

	subform_therapy_4:
		model:
			source: 'assessment_{therapy_4}'
			sourcefilter:
				'assessment_factor': {}
				'assessment_aat': {}
				'assessment_ivig': {}
				'assessment_subqig': {}
				'assessment_tnf': {}
				'assessment_tpn': {}
				'assessment_steroid': {}
				'assessment_inotrope': {}
				'assessment_chemotherapy': {}
				'assessment_bisphosphonates': {}
			type: 'subform'
		view:
			label: 'Quaternary Therapy Assessment'

	subform_therapy_5:
		model:
			source: 'assessment_{therapy_5}'
			sourcefilter:
				'assessment_factor': {}
				'assessment_aat': {}
				'assessment_ivig': {}
				'assessment_subqig': {}
				'assessment_tnf': {}
				'assessment_tpn': {}
				'assessment_steroid': {}
				'assessment_inotrope': {}
				'assessment_chemotherapy': {}
				'assessment_bisphosphonates': {}
			type: 'subform'
		view:
			label: 'Quinary Therapy Assessment'

	brand_1:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Primary Brand'

	brand_2:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Brand'

	brand_3:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Brand'

	brand_4:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Brand'

	brand_5:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Brand'

	subform_brand_1:
		model:
			source: 'assessment_{brand_1}'
			sourcefilter:
				'assessment_cimzia': {}
				'assessment_dupixent': {}
				'assessment_enbrel': {}
				'assessment_humira': {}
				'assessment_krystexxa': {}
				'assessment_lemtrada': {}
				'assessment_ocrevus': {}
				'assessment_orencia': {}
				'assessment_radicava': {}
				'assessment_remicade': {}
				'assessment_rituxan': {}
				'assessment_simponi': {}
				'assessment_simponiaria': {}
				'assessment_soliris': {}
				'assessment_stelara': {}
				'assessment_tysabri': {}
				'assessment_vyvgart': {}
				'assessment_vancomycin': {}
			type: 'subform'
		view:
			label: 'Primary Drug Brand Assessment'

	subform_brand_2:
		model:
			source: 'assessment_{brand_2}'
			sourcefilter:
				'assessment_cimzia': {}
				'assessment_dupixent': {}
				'assessment_enbrel': {}
				'assessment_humira': {}
				'assessment_krystexxa': {}
				'assessment_lemtrada': {}
				'assessment_ocrevus': {}
				'assessment_orencia': {}
				'assessment_radicava': {}
				'assessment_remicade': {}
				'assessment_rituxan': {}
				'assessment_simponi': {}
				'assessment_simponiaria': {}
				'assessment_soliris': {}
				'assessment_stelara': {}
				'assessment_tysabri': {}
				'assessment_vyvgart': {}
				'assessment_vancomycin': {}
			type: 'subform'
		view:
			label: 'Secondary Drug Brand Assessment'

	subform_brand_3:
		model:
			source: 'assessment_{brand_3}'
			sourcefilter:
				'assessment_cimzia': {}
				'assessment_dupixent': {}
				'assessment_enbrel': {}
				'assessment_humira': {}
				'assessment_krystexxa': {}
				'assessment_lemtrada': {}
				'assessment_ocrevus': {}
				'assessment_orencia': {}
				'assessment_radicava': {}
				'assessment_remicade': {}
				'assessment_rituxan': {}
				'assessment_simponi': {}
				'assessment_simponiaria': {}
				'assessment_soliris': {}
				'assessment_stelara': {}
				'assessment_tysabri': {}
				'assessment_vyvgart': {}
				'assessment_vancomycin': {}
			type: 'subform'
		view:
			label: 'Tertiary Drug Brand Assessment'

	subform_brand_4:
		model:
			source: 'assessment_{brand_4}'
			sourcefilter:
				'assessment_cimzia': {}
				'assessment_dupixent': {}
				'assessment_enbrel': {}
				'assessment_humira': {}
				'assessment_krystexxa': {}
				'assessment_lemtrada': {}
				'assessment_ocrevus': {}
				'assessment_orencia': {}
				'assessment_radicava': {}
				'assessment_remicade': {}
				'assessment_rituxan': {}
				'assessment_simponi': {}
				'assessment_simponiaria': {}
				'assessment_soliris': {}
				'assessment_stelara': {}
				'assessment_tysabri': {}
				'assessment_vyvgart': {}
				'assessment_vancomycin': {}
			type: 'subform'
		view:
			label: 'Quaternary Drug Brand Assessment'

	subform_brand_5:
		model:
			source: 'assessment_{brand_5}'
			sourcefilter:
				'assessment_cimzia': {}
				'assessment_dupixent': {}
				'assessment_enbrel': {}
				'assessment_humira': {}
				'assessment_krystexxa': {}
				'assessment_lemtrada': {}
				'assessment_ocrevus': {}
				'assessment_orencia': {}
				'assessment_radicava': {}
				'assessment_remicade': {}
				'assessment_rituxan': {}
				'assessment_simponi': {}
				'assessment_simponiaria': {}
				'assessment_soliris': {}
				'assessment_stelara': {}
				'assessment_tysabri': {}
				'assessment_vyvgart': {}
				'assessment_vancomycin': {}
			type: 'subform'
		view:
			label: 'Quinary Drug Brand Assessment'

	disease_1:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Primary Disease'

	disease_2:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Disease'

	disease_3:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Disease'

	disease_4:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Disease'

	disease_5:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Disease'

	subform_disease_1:
		model:
			source: 'assessment_{disease_1}'
			sourcefilter:
				'assessment_asthma': {}
				'assessment_hiv': {}
				'assessment_hepc': {}
				'assessment_hepb': {}
				'assessment_hemob': {}
				'assessment_hemoa': {}
				'assessment_ra': {}
				'assessment_psoriasis': {}
				'assessment_ms': {}
				'assessment_vwd': {}
			type: 'subform'
		view:
			label: 'Primary Disease Assessment'

	subform_disease_2:
		model:
			source: 'assessment_{disease_2}'
			sourcefilter:
				'assessment_asthma': {}
				'assessment_hiv': {}
				'assessment_hepc': {}
				'assessment_hepb': {}
				'assessment_hemob': {}
				'assessment_hemoa': {}
				'assessment_ra': {}
				'assessment_psoriasis': {}
				'assessment_ms': {}
				'assessment_vwd': {}
			type: 'subform'
		view:
			label: 'Secondary Disease Assessment'

	subform_disease_3:
		model:
			source: 'assessment_{disease_3}'
			sourcefilter:
				'assessment_asthma': {}
				'assessment_hiv': {}
				'assessment_hepc': {}
				'assessment_hepb': {}
				'assessment_hemob': {}
				'assessment_hemoa': {}
				'assessment_ra': {}
				'assessment_psoriasis': {}
				'assessment_ms': {}
				'assessment_vwd': {}
			type: 'subform'
		view:
			label: 'Tertiary Disease Assessment'

	subform_disease_4:
		model:
			source: 'assessment_{disease_4}'
			sourcefilter:
				'assessment_asthma': {}
				'assessment_hiv': {}
				'assessment_hepc': {}
				'assessment_hepb': {}
				'assessment_hemob': {}
				'assessment_hemoa': {}
				'assessment_ra': {}
				'assessment_psoriasis': {}
				'assessment_ms': {}
				'assessment_vwd': {}
			type: 'subform'
		view:
			label: 'Quaternary Disease Assessment'

	subform_disease_5:
		model:
			source: 'assessment_{disease_5}'
			sourcefilter:
				'assessment_asthma': {}
				'assessment_hiv': {}
				'assessment_hepc': {}
				'assessment_hepb': {}
				'assessment_hemob': {}
				'assessment_hemoa': {}
				'assessment_ra': {}
				'assessment_psoriasis': {}
				'assessment_ms': {}
				'assessment_vwd': {}
			type: 'subform'
		view:
			label: 'Quinary Disease Assessment'


	clinical_1:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['subform_clinical_1']
					sections: ['Primary Clinical Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Clinical Assessment'

	clinical_2:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['subform_clinical_2']
					sections: ['Secondary Clinical Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Clinical Assessment'

	clinical_3:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['subform_clinical_3']
					sections: ['Tertiary Clinical Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Clinical Assessment'

	clinical_4:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['subform_clinical_4']
					sections: ['Quaternary Clinical Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Clinical Assessment'

	clinical_5:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['subform_clinical_5']
					sections: ['Quinary Clinical Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Clinical Assessment'

	subform_clinical_1:
		model:
			source: 'clinical_{clinical_1}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
			type: 'subform'
		view:
			label: 'Primary Clinical Assessment'

	subform_clinical_2:
		model:
			source: 'clinical_{clinical_2}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
			type: 'subform'
		view:
			label: 'Secondary Clinical Assessment'

	subform_clinical_3:
		model:
			source: 'clinical_{clinical_3}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
			type: 'subform'
		view:
			label: 'Tertiary Clinical Assessment'

	subform_clinical_4:
		model:
			source: 'clinical_{clinical_4}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
			type: 'subform'
		view:
			label: 'Quaternary Clinical Assessment'

	subform_clinical_5:
		model:
			source: 'clinical_{clinical_5}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
			type: 'subform'
		view:
			label: 'Quinary Clinical Assessment'

	measurement_log:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				form: 'patient_measurement_log'
				selectable: false
			grid:
				add: 'inline'
				edit: true
				rank: 'none'
				fields: ['created_by', 'date', 'height', 'weight']
				width: [40, 20, 20, 20]
			label: 'Measurement Log'
			readonly: true

	subform_pmp:
		model:
			required: true
			multi: false 
			type: 'subform'
			source: 'patient_pmp_enrollment_log'
		view:
			label: 'Patient Management Program (PMP) Enrollment'

	patient_medications:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': 'Active'
		view:
			embed:
				form: 'patient_medication'
				selectable: false
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['fdb_id', 'medication_dose', 'medication_frequency', 'start_date']
				label: ['Medication', 'Dose', 'Frequency', 'Start Dt']
				width: [35, 25, 25, 15]
			label: 'Active Medications'

	patient_allergies:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				end_date:
					'dynamic': ''
		view:
			embed:
				form: 'patient_allergy'
				selectable: false
				add_preset:
					status: 'Active'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['allergen_id', 'reaction_id', 'severity_id', 'start_date']
				label: ['Allergen', 'Reaction', 'Severity', 'Start Dt']
				width: [35, 25, 25, 15]
			label: 'Active Allergies'

	patient_interactions:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			readonly: true
			embed:
				form: 'interactions'
				selectable: false
			grid:
				add: 'none'
				hide_cardmenu: true
				edit: true
				rank: 'none'
				fields: ['created_by', 'has_da', 'has_dd']
				label: ['Created By','Drug Allergy', 'Drug Drug']
				width: [30, 30, 30]
			label: 'Interactions'

	patient_medical_hx:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				end_date:
					'dynamic': ''
		view:
			embed:
				form: 'patient_medical_hx'
				selectable: false
			grid:
				add: 'none'
				edit: true
				rank: 'none'
				fields: ['created_by', 'created_on', 'medical_hist', 'medical_hist_desc']
				label: ['Created By', 'Created On', 'Medical History', 'Details']
				width: [20, 15, 35, 30]
			label: 'Medical History'

	patient_interaction_btn:
		model:
			source: ['Get Interactions']
		view:
			columns: 2
			class: 'dsl-button'
			control: 'checkbox'
			label: 'Patient Interaction'
			validate: [
				name: 'DrugAllergyInteraction'
			]

	subform_bleed_events:
		model:
			access:
				read: ['patient']
			source: 'factor_bleed'
			multi: true
			type: 'subform'
		view:
			label: 'Factor Bleed Events Log'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['date', 'cause', 'severity', 'site']
				label: ['Date', 'Cause', 'Severity', 'Site']
				width: [20, 25, 20, 35]

	admin_location:
		model:
			source: ['Physician office', 'Home', 'Infusion Suite', 'Other']
			required: true
			if:
				'Physician office':
					fields: ['reviewed_by']
				'*':
					fields: ['subform_pmp']
					sections: ['Patient Management Program (PMP) Enrollment']
		view:
			columns: 2
			control: 'radio'
			label: 'Med Administration Location'
			transform: [
				{
					name: 'EmbedRefresh'
					fields: ['patient_medications', 'patient_allergies']
				}
			]

	counseling_performed:
		model:
			required: true
			multi: true
			source: ['Directions for use', 'Plan of care', 'Potential ADRs', 'Adherence', 'Meds, Supply storage, Disposal', 'How to contact the pharmacy']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Pt is educated on/or counseling session performed on:'

	contact_notes:
		view:
			columns: 2
			control: 'area'
			label: 'Assessment Notes'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['patient-form']
	name: ['patient_id', 'created_on']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
			['therapy_1']
			['therapy_2']
			['therapy_3']
			['therapy_4']
			['therapy_5']
			['brand_1']
			['brand_2']
			['brand_3']
			['brand_4']
			['brand_5']
			['disease_1']
			['disease_2']
			['disease_3']
			['disease_4']
			['disease_5']
			['clinical_1']
			['clinical_2']
			['clinical_3']
			['clinical_4']
			['clinical_5']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections_group: [

			'Associated Records':
				fields: ['route_id', 'therapy_1', 'therapy_2', 'therapy_3',
				'therapy_4', 'therapy_5', 'brand_1',
				'brand_2', 'brand_3', 'brand_4',
				'brand_5', 'disease_1', 'disease_2', 'disease_3',
				'disease_4', 'disease_5', 'clinical_1', 'clinical_2', 'clinical_3',
				'clinical_4', 'clinical_5', 'dx_1', 'dx_2', 'dx_3', 'dx_4', 'dx_5']

			'H&P':
				hide_header: true
				indent: false
				tab: 'H&P'
				fields: ['measurement_log']

			'Medical History':
				indent: false
				tab: 'H&P'
				fields: ['patient_medical_hx']

			'Patient Management Program (PMP) Enrollment':
				indent: false
				tab: 'H&P'
				fields: ['subform_pmp']

			'DUR - Medications':
				hide_header: true
				indent: false
				tab: 'DUR'
				fields: ['patient_medications']

			'DUR - Allergies':
				hide_header: true
				indent: false
				tab: 'DUR'
				fields: ['patient_allergies']
			
			'DUR - DD DA Interaction':
				hide_header: true
				indent: false
				tab: 'DUR'
				fields: ['patient_interaction_btn']

			'DUR - Interaction':
				hide_header: true
				indent: false
				tab: 'DUR'
				fields: ['patient_interactions']

			'Primary Therapy Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_therapy_1'] # subform
			'Secondary Therapy Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_therapy_2'] # subform
			'Tertiary Therapy Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_therapy_3'] # subform
			'Quaternary Therapy Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_therapy_4'] # subform
			'Quinary Therapy Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_therapy_5'] # subform

			'Primary Drug Brand Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_brand_1'] # subform
			'Secondary Drug Brand Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_brand_2'] # subform
			'Tertiary Drug Brand Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_brand_3'] # subform
			'Quaternary Drug Brand Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_brand_4'] # subform
			'Quinary Drug Brand Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_brand_5']

			'Primary Disease Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_disease_1'] # subform
			'Secondary Disease Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_disease_2'] # subform
			'Tertiary Disease Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_disease_3'] # subform
			'Quaternary Disease Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_disease_4'] # subform
			'Quinary Disease Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_disease_5']

			'Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				fields: ['admin_location']

			'Primary Therapy Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'admin'
				fields: ['subform_therapy_1'] # subform
			'Secondary Therapy Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'admin'
				fields: ['subform_therapy_2'] # subform
			'Tertiary Therapy Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'admin'
				fields: ['subform_therapy_3'] # subform
			'Quaternary Therapy Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'admin'
				fields: ['subform_therapy_4'] # subform
			'Quinary Therapy Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'admin'
				fields: ['subform_therapy_5']

			'Primary Drug Brand Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'admin'
				fields: ['subform_brand_1'] # subform
			'Secondary Drug Brand Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'admin'
				fields: ['subform_brand_2'] # subform
			'Tertiary Drug Brand Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'admin'
				fields: ['subform_brand_3'] # subform
			'Quaternary Drug Brand Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'admin'
				fields: ['subform_brand_4'] # subform
			'Quinary Drug Brand Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'admin'
				fields: ['subform_brand_5']

			'Primary Disease Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'admin'
				fields: ['subform_disease_1'] # subform
			'Secondary Disease Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'admin'
				fields: ['subform_disease_2'] # subform
			'Tertiary Disease Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'admin'
				fields: ['subform_disease_3'] # subform
			'Quaternary Disease Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'admin'
				fields: ['subform_disease_4'] # subform
			'Quinary Disease Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'admin'
				fields: ['subform_disease_5']

			'Primary Therapy Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_therapy_1'] # subform
			'Secondary Therapy Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_therapy_2'] # subform
			'Tertiary Therapy Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_therapy_3'] # subform
			'Quaternary Therapy Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_therapy_4'] # subform
			'Quinary Therapy Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_therapy_5'] # subform

			'Primary Drug Brand Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_brand_1'] # subform
			'Secondary Drug Brand Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_brand_2'] # subform
			'Tertiary Drug Brand Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_brand_3'] # subform
			'Quaternary Drug Brand Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_brand_4'] # subform
			'Quinary Drug Brand Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_brand_5']

			'Primary Disease Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_disease_1'] # subform
			'Secondary Disease Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_disease_2'] # subform
			'Tertiary Disease Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_disease_3'] # subform
			'Quaternary Disease Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_disease_4'] # subform
			'Quinary Disease Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Assessment'
				fields: ['subform_disease_5']

			'Primary Clinical Assessment':
				hide_header: true
				indent: false
				tab: 'Clinical'
				fields: ['subform_clinical_1'] # subform
			'Secondary Clinical Assessment':
				hide_header: true
				indent: false
				tab: 'Clinical'
				fields: ['subform_clinical_2'] # subform
			'Tertiary Clinical Assessment':
				hide_header: true
				indent: false
				tab: 'Clinical'
				fields: ['subform_clinical_3'] # subform
			'Quaternary Clinical Assessment':
				hide_header: true
				indent: false
				tab: 'Clinical'
				fields: ['subform_clinical_4'] # subform
			'Quinary Clinical Assessment':
				hide_header: true
				indent: false
				tab: 'Clinical'
				fields: ['subform_clinical_5']
			'Factor Bleed Events Log':
				indent: false
				tab: 'Clinical'
				fields: ['subform_bleed_events']

			'Primary Therapy Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				area: 'footer'
				fields: ['subform_therapy_1'] # subform
			'Secondary Therapy Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				area: 'footer'
				fields: ['subform_therapy_2'] # subform
			'Tertiary Therapy Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				area: 'footer'
				fields: ['subform_therapy_3'] # subform
			'Quaternary Therapy Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				area: 'footer'
				fields: ['subform_therapy_4'] # subform
			'Quinary Therapy Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				area: 'footer'
				fields: ['subform_therapy_5'] # subform

			'Primary Drug Brand Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				area: 'footer'
				fields: ['subform_brand_1'] # subform
			'Secondary Drug Brand Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				area: 'footer'
				fields: ['subform_brand_2'] # subform
			'Tertiary Drug Brand Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				area: 'footer'
				fields: ['subform_brand_3'] # subform
			'Quaternary Drug Brand Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				area: 'footer'
				fields: ['subform_brand_4'] # subform
			'Quinary Drug Brand Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				area: 'footer'
				fields: ['subform_brand_5']

			'Primary Disease Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				area: 'footer'
				fields: ['subform_disease_1'] # subform
			'Secondary Disease Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				area: 'footer'
				fields: ['subform_disease_2'] # subform
			'Tertiary Disease Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				area: 'footer'
				fields: ['subform_disease_3'] # subform
			'Quaternary Disease Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				area: 'footer'
				fields: ['subform_disease_4'] # subform
			'Quinary Disease Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				area: 'footer'
				fields: ['subform_disease_5']

			'Post Assessment':
				hide_header: true
				indent: false
				tab: 'Sign-off'
				fields: ['counseling_performed', 'contact_notes']
	]

view:
	comment: 'Patient > Careplan > Assessment'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-id']
	label: 'Assessment Questionnaire'
	open: 'read'
