fields:
	ncit_subset_code:
		view:
			columns: 2
			label: 'NCIt subset Concept code'
			note: 'The NCIt concept code attached to the subset concept. NCIt Codes are unique strings that begin with a C and are followed by a series of digits.'
	ncpdp_subset_preferred_term:
		view:
			columns: 2
			label: 'NCPDP Preferred term for the subset concept'
			note: 'The NCPDP preferred term attached to the subset concept.'
	ncit_code:
		view:
			columns: 2
			label: 'NCIt Concept code'
			note: 'A unique and permanent identifier attached to every concept. The actual string carries no information other than to provide a machine readable piece of identification to every concept.'
	ncpdp_preferred_term:
		view:
			columns: 2
			label: 'NCPDP Preferred term'
			note: 'The term chosen by NCPDP for use as its Preferred Term.'
	ncpdp_synonym:
		view:
			columns: 2.1
			label: 'NCPDP Synonym'
			note: 'An additional term chosen by NCPDP that has an equivalent meaning to the Preferred Term.'
	ncit_preferred_term:
		view:
			columns: 2.1
			label: 'NCIt Preferred term'
			note: 'The term chosen by NCI EVS as most unambiguous and widely used in the biomedical community.'
	nci_definition:
		view:
			columns: 2
			label: 'NCI Text Definition'
			note: 'A text definition of the term created by an NCI EVS subject matter expert.'
model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['ncit_code']
		]
	name: '{ncpdp_preferred_term} - ({ncit_code})'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['ncit_code', 'ncit_subset_code', 'ncit_preferred_term', 'ncpdp_preferred_term', 'ncpdp_subset_preferred_term', 'ncpdp_synonym', 'nci_definition']

view:
	comment: 'Manage > List NCPDP Terminology'
	find:
		basic: ['ncpdp_preferred_term', 'ncpdp_synonym', 'ncit_preferred_term', 'nci_definition']
	grid:
		fields: ['ncpdp_preferred_term', 'ncpdp_synonym', 'ncit_preferred_term', 'nci_definition']
	label: 'List NCPDP Terms'
