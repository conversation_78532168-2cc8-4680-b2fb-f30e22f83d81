fields:
	hash:
		model:
			required: true
			type: 'text'
		view:
			label: 'Hash'

	tag: # Links with wf_queue
		model:
			required: true
			type: 'text'
		view:
			label: 'Tag'
	wf_code:
		model:
			required: false
		view:
			label: 'Workflow Code'

	wf_node:
		model:
			required: false
		view:
			label: 'Workflow Node'

	cleared_on:
		model:
			required: false
			type: 'datetime'
		view:
			label: 'Cleared On'
	
	row_data:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			readonly: true
			label: 'Row Data'

model:
	access:
		create:     ['admin']
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    ['admin']
		update:     ['admin']
		update_all: ['admin']
		write:      ['admin']
	name: '{tag} {wf_code} {wf_node}'
	sections:
		'Main':
			fields:  ['tag', 'wf_code', 'wf_node', 'cleared_on', 'row_data']
	indexes:
		many: [
			['tag','hash', 'wf_code', 'wf_node']
			['tag', 'hash', 'cleared_on']
			['tag', 'wf_code', 'wf_node', 'cleared_on']
			['wf_code', 'wf_node', 'cleared_on']
			['cleared_on']
			['wf_node']
		]
		unique: [
			['tag', 'wf_code', 'wf_node'],
		]

view:
	comment: 'Workflow Queue Node'
	find:
		basic:  ['tag', 'wf_code', 'wf_node', 'cleared_on']
	grid:
		fields:  ['tag', 'wf_code', 'wf_node', 'cleared_on']
	label: 'Workflow Queue Node'
	open: 'read'
