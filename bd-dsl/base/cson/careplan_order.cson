fields:

	external_id:
		model:
			type: 'text'
		view:
			label: 'External ID'
			readonly: true
			offscreen: true

	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			class: 'select_prefill'
			offscreen: true
			readonly: true
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'team_id': ['team_id']
					'referral_source_id': ['referral_source_id']
					'prescriber_id': ['primary_prescriber_id']
			]

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_no:
		model:
			required: false
		view:
			label: 'Order #'
			readonly: true
			offscreen: true

	order_format:
		model:
			required: true
			source: ['Single Prescription', 'Therapy Set', 'Supply Order']
			if:
				'Therapy Set':
					sections: ['Diagnosis', 'Billing', 'Payers', 'Patient Assistance', 'Pharmacy',
					'Orders', 'Active Prescriptions',
					'Supplies', 'Labs Completed', 'Labs', 'Rentals', 'D/C']
					fields: ['therapy_id', 'therapy_set_dc', 'requires_nursing', 'prescriber_id', 'team_id', 'referral_source_id', 'physician_id']
					prefill:
						tab_label: 'Referral'
				'Single Prescription':
					fields: ['subform_single_order', 'embed_document', 'prescriber_id', 'team_id', 'referral_source_id', 'physician_id']
					sections: ['Single Prescription', 'Documents']
					prefill:
						tab_label: 'Order'
				'Supply Order':
					fields: ['subform_single_supply']
					sections: ['Single Supply Order']
					prefill:
						tab_label: 'Supply'
		view:
			label: 'Order Type'
			control: 'radio'
			readonly: true
			offscreen: true
			transform: [
				{
					name: "UpdateFormLabel"
					if:
						'Single Prescription': 'Order'
						'Therapy Set': 'Referral Therapy Set'
				}
			]

	tab_label:
		model:
			source: ['Order', 'Referral']
		view:
			label: 'Tab Label'
			readonly: true
			offscreen: true
			validate: [{
					name: 'UpdateTabLabel'
					fields: ['tab_label']
			}
			]

	single_rx_dc:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'D/C'
			offscreen: true
			readonly: true

	therapy_set_dc:
		model:
			multi: false
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'D/C'
			validate: [
				{
					name: 'DiscontinueTherapySetChecks'
				}
			]

	subform_single_order:
		model:
			multi: false
			source: 'careplan_orderp_item'
			type: 'subform'
		view:
			label: 'Order'

	subform_single_supply:
		model:
			multi: false
			source: 'careplan_orders_item'
			type: 'subform'
		view:
			label: 'Supply'

	site_id:
		model:
			prefill: ['patient']
			required: true
			source: 'site'
		view:
			columns: 4
			label: 'Site'
			validate: [
				{
					name: 'CopyForwardValueToChild'
					field: 'site_id'
					subform_field: 'subform_single_order'
					overwrite: true
				},
				{
					name: 'CopyForwardValueToChild'
					field: 'site_id'
					subform_field: 'subform_single_supply'
					overwrite: true
				}
			]

	team_id:
		model:
			prefill: ['patient']
			required: false
			source: 'list_team'
			type: 'int'
		view:
			label: 'Team'
			columns: 4

	therapy_id:
		model:
			required: false # marked as required by tab completed check
			max: 64
			multi: true
			source: 'list_therapy'
			sourceid: 'code'
		view:
			columns: 4
			label: 'Therapy'
			validate: [
				{
					name: 'ShowHideFactorButton'
				}
			]

	prescriber_id:
		model:
			prefill: ['careplan_order']
			required: true
			source: 'patient_prescriber'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				careplan_id:
					'dynamic': '{careplan_id}'
				is_primary:
					'static': 'Yes'
		view:
			columns: 4
			label: 'Prescriber'
			add_preset:
				patient_id: '{patient_id}'
			form_link_enabled: true
			class: 'select_prefill'
			transform: [
				{
					name: 'SelectPrefill'
					url: '/form/patient_prescriber/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
					fields:
						'physician_id': ['physician_id']
				}
			]

	physician_id:
		model:
			source: 'physician'
		view:
			label: 'Referring Physician'
			offscreen: true
			readonly: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/physician/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'territory_id': ['territory_id']
			]

	referral_source_id:
		model:
			source: 'sales_account'
		view:
			label: 'Referral Source'
			columns: 4

	territory_id:
		model:
			required: false
			source: 'sales_territory'
		view:
			label: 'Sales Territory'
			offscreen: true
			readonly: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/sales_territory/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'commissioned_sales_rep': ['commissioned_sales_rep']
			]

	commissioned_sales_rep:
		model:
			source: 'user'
		view:
			label: 'Sales Rep'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'physician_id'
					source_fields: ['territory_id', 'commissioned_sales_rep']
					separator: ' - '
				}
			]

	summary:
		view:
			label: 'Summary'
			offscreen: true
			readonly: true

	assistance_notes:
		model:
			required: false
			type: 'text'
		view:
			control: 'area'
			label: 'Assistance Notes'
			columns: 2

	requires_nursing:
		model:
			multi: false
			source: ['Yes']
			if:
				'Yes':
					sections: ['Nursing', 'Rentals', 'Supplies']
					fields: ['nurse_billable_id', 'nursing_insurance_id', 'nursing_pa_id']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Requires nursing?'
			columns: 4

	nursing_status:
		model:
			required: false # marked as required by tab completed check
			default: '1'
			source: 'list_order_nursing_status'
			sourceid: 'code'
			if:
				'3':
					fields: ['nursing_agency_id', 'first_visit_date']
					sections: ['Nursing']
		view:
			label: 'Nursing Status'
			columns: 4

	nursing_agency_id:
		model:
			required: false # marked as required by tab completed check
			source: 'nurse_agency'
			sourcefilter:
				active:
					'static': 'Yes'
				site_id:
					'dynamic': '{site_id}'
		view:
			label: 'Nursing Provider'
			columns: 4

	infusion_suite_id:
		model:
			type: 'int'
			source: 'infusion_suite'
		view:
			columns: 4
			label: 'Infusion Suite'

	first_visit_date:
		model:
			type: 'date'
		view:
			label: 'First visit date:'
			columns: 4

	requires_assistance:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['assistance_notes']
		view:
			columns: 4
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Patient in need of financial assistance?'

	template_id:
		model:
			source: 'template_order'
		view:
			columns: 2
			label: 'Order Set Template'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/template_order/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'supply_kit_id': ['supply_kit_id'],
					'subform_order':
						'type': 'subform'
						'field': 'subform_order'
						'fields':
							'type_id': ['sf.type_id']
							'inventory_id': ['sf.inventory_id']
							'formatted_ndc': ['sf.formatted_ndc']
							'hcpc_code': ['sf.hcpc_code']
							'manufacturer_id': ['sf.manufacturer_id']
							'route_id': ['sf.route_id']
							'therapy_id': ['sf.therapy_id']
							'rx_template_id': ['sf.rx_template_id']
							'billing_method': ['sf.billing_method']
							'auth_flag': ['sf.auth_flag']
							'bv_flag': ['sf.bv_flag']
							'template_type': ['sf.template_type']
							'bill_notes': ['sf.bill_notes']
							'comments': ['sf.comments']
			]

	supply_kit_id:
		model:
			source: 'inventory_supply_kit'
			sourcefilter:
				site_id:
					'dynamic': '{site_id}'
		view:
			label: 'Supply Kit'
			class: 'select_prefill'
			columns: 2
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory_supply_kit/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'supply_billable_id': ['billable_id']
					'subform_supply':
						'type': 'subform'
						'field': 'subform_supply'
						'fields':
							'inventory_id': ['sf.inventory_id']
							'dispense_quantity': ['sf.dispense_quantity']
							'supply_billable': ['sf.bill']
							'part_of_kit': ['sf.part_of_kit']
							'one_time_only': ['sf.one_time_only']
			]

	written_date:
		model:
			required: false # marked as required by tab completed check
			type: 'date'
		view:
			note: 'Orders automatically expire after 1 year'
			label: 'Written Date'
			template: '{{now}}'
			columns: -3
			offscreen: true
			transform: [
				{
					name: 'CalculateExpirationDate'
				}
			]
			validate: [{
					name: 'AddStartStopDates'
			}
			]

	expiration_date:
		model:
			required: false # marked as required by tab completed check
			type: 'date'
		view:
			columns: 4
			offscreen: true
			label: 'Referral Expiration Date'
			transform: [
				{
					name: 'PreventLongExpiration'
				}
			]

	dx_ids:
		model:
			required: false # marked as required by tab completed check
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			embed:
				form: 'patient_diagnosis'
				selectable: true
			grid:
				edit: true
				fields: ['dx_id']
				label: ['Diagnosis']
				rank: 'local'
			max_count: 12
			label: 'Diagnosis'

	billing_method:
		model:
			required: true
			source: ['Insurance', 'Self Pay', 'Do Not Bill']
			if:
				'Insurance':
					fields: ['payer_ids']
				'Self Pay':
					fields: ['payer_ids']
				'Do Not Bill':
					fields: ['dnb_reason_id']
		view:
			columns: 4
			control: 'radio'
			label: 'Billing Method'

	dnb_reason_id:
		model:
			required: true
			source: 'list_dnb_reason'
			sourceid: 'code'
		view:
			columns: 4
			control: 'radio'
			label: 'Do Not Bill Reason'

	payer_ids:
		model:
			multi: true
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			embed:
				form: 'patient_insurance'
				selectable: true
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['type_id', 'payer_id', 'effective_date', 'bill_for_denial']
				width: [20, 40, 20, 20]
				rank: 'local'
			label: 'Payers'
			validate: [
				{
					name: 'AggregateInsuranceIds'
				}
			]

	insurance_id:
		model:
			multi: true
			required: false
			source: 'patient_insurance'
			type: 'int'
		view:
			label: 'Insurance IDs'
			offscreen: true
			readonly: true

	patient_assistance:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				order_id:
					'dynamic': '{id}'
		view:
			embed:
				form: 'patient_assistance'
				selectable: false
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['created_by', 'type', 'assistance_status', 'notes']
				width: [20, 20, 20, 40]
			label: 'Patient Assistance'
			readonly: true

	start_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 4
			note: 'This can be an estimated start date until the actual start date is available.'
			label: 'Start Date'

	stop_date:
		model:
			required: false
			type: 'date'
		view:
			columns: 4
			label: 'Stop Date'

	active_rx:
		model:
			required: false
			multi: true
			source: 'careplan_order_rx'
			sourcefilter:
				discontinued:
					'static': '!Yes'
		view:
			embed:
				selectable: false
			grid:
				hide_cardmenu: true
				add: 'flyout'
				edit: true
				fields: ['rx_no', 'inventory_id', 'next_fill_date', 'next_delivery_date', 'status']
				label: ['Rx No','Drug', 'Next Fill', 'Next Delivery', 'Status']
				width: [15, 35, 15, 15, 20]
			label: 'Active Prescriptions'

	subform_order:
		model:
			type: 'subform'
			multi: true
			required: false
			source: 'careplan_order_item'
		view:
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				deleteif:
					rx_no: '!'
				fields: ['inventory_id', 'status_id', 'intake_substatus_id', 'dose', 'frequency_id']
				label: ['Drug', 'Status', 'Substatus', 'Dose', 'Freq']
				width: [25, 20, 20, 15, 15]
			label: 'Pending/Active/On-Hold Orders'

	subform_supply:
		model:
			multi: true
			source: 'careplan_order_supply'
			type: 'subform'
			if:
				'*':
					fields: ['bill_supplies']
		view:
			label: 'Supply Item'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['inventory_id', 'dispense_quantity', 'supply_billable', 'one_time_only']
				label: ['Item', 'Quantity', 'Billable?', 'One-Time?']
				width: [35, 15, 20, 15]

	bill_supplies:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['supplies_insurance_id', 'supply_billable_id', 'supplies_pa_id']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Supplies Billable?'

	supplies_insurance_id:
		model:
			required: true
			source: 'patient_insurance'
			type: 'int'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			label: 'Supplies Payer'
			columns: 4

	supplies_pa_id:
		model:
			required: false
			source: 'patient_prior_auth'
			sourcefilter:
				insurance_id:
					'dynamic': '{supplies_insurance_id}'
				patient_id:
					'dynamic': '{patient_id}'
				status_id:
					'static': ['5', '1', '2', '3', '4', '8']
				pa_type:
					'static': 'Supplies'
		view:
			columns: -2
			label: 'Supplies Authorization'


	supplies_pa_create:
		model:
			source: ['Create PA']
		view:
			columns: 4
			class: 'dsl-button'
			control: 'checkbox'
			label: 'Create PA'
			validate: [
				name: 'CreateSuppliesPA'
			]

	supply_billable_id:
		model:
			required: false
			source: 'inventory'
			sourcefilter:
				type:
					'static': ['Billable']
				billable_code_type:
					'static': ['HCPC', 'A-Code', 'S-Code', 'B-Code', 'K-Code', 'G-Code']
				active:
					'static': 'Yes'
		view:
			form_link_enabled: true
			columns: 4
			label: 'Supplies Billable'
			note: 'Non-kit billable codes can be set in the supply items'
			class: 'claim-field'

	nursing_insurance_id:
		model:
			required: true
			source: 'patient_insurance'
			type: 'int'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			columns: 4
			label: 'Nursing Payer'

	nursing_pa_id:
		model:
			required: false
			source: 'patient_prior_auth'
			sourcefilter:
				insurance_id:
					'dynamic': '{insurance_id}'
				patient_id:
					'dynamic': '{patient_id}'
				status_id:
					'static': ['5', '1', '2', '3', '4', '8']
				pa_type:
					'static': 'Nursing'
		view:
			columns: -2
			label: 'Nursing Authorization'

	rental_insurance_id:
		model:
			required: false
			source: 'patient_insurance'
			type: 'int'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			columns: 4
			label: 'Rental Insurance'
			class: 'select_prefill'
			readonly: true

	rental_pa_id:
		model:
			required: false
			source: 'patient_prior_auth'
			sourcefilter:
				pa_type:
					'static': 'DME'
				patient_id:
					'dynamic': '{patient_id}'
				status_id:
					'static': ['5', '1', '2', '3', '4', '8']
				insurance_id:
					'dynamic': '{rental_insurance_id}'
		view:
			columns: -2
			label: 'Rental Authorization'

	rental_billable_id:
		model:
			required: false 
			source: 'inventory'
			sourcefilter:
				type:
					'static': ['Billable']
				dme_related:
					'static': 'Yes'
				active:
					'static': 'Yes'
		view:
			form_link_enabled: true
			columns: 4
			label: 'Rental Billable Code'
			class: 'claim-field'

	nurse_billable_id:
		model:
			required: false 
			multi: true
			source: 'inventory'
			sourcefilter:
				type:
					'static': ['Billable']
				nursing_related:
					'static': 'Yes'
				active:
					'static': 'Yes'
		view:
			form_link_enabled: true
			columns: 4
			label: 'Nursing Billable Codes'
			class: 'claim-field'

	subform_rental:
		model:
			multi: true
			source: 'careplan_order_rental'
			type: 'subform'
		view:
			label: 'Rental Item'
			grid:
				add: 'inline'
				edit: true
				fields: ['inventory_id', 'rental_type', 'rental_insurance_id', 'frequency_code']
				label: ['Equipment', 'Type', 'Payer', 'Freq']
				width: [40, 20, 20, 20]

	subform_lab:
		model:
			multi: true
			source: 'careplan_order_lab'
			type: 'subform'
		view:
			label: 'Lab Order'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['lab_id', 'draw_frequency', 'next_draw_date', 'active']
				label: ['Lab', 'Draw Frequency', 'Next Draw Dt', 'Active']
				width: [35, 30, 20, 15]

	show_void_warnings:
		model:
			source: ['No','Yes']
			if:
				'Yes':
					fields: ['void_warnings']
		view:
			control: 'radio'
			label: 'Show Void Warnings?'
			offscreen: true
			readonly: true

	void_warnings:
		model:
			multi: true
			source: ['']
		view:
			control: 'checkbox'
			label: 'Warning'
			class: 'list'
			readonly: true

	void:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['voided_datetime', 'void_reason_id']
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Void Referral?'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						void: 'Yes'
					dest: 'voided_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						void: 'Yes'
					dest: 'voided_by'
				}
			]

	voided_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Voided Date'
			readonly: true

	voided_by:
		model:
			source: 'user'
		view:
			label: 'Voided By'
			readonly: true

	void_reason_id:
		model:
			required: true
			source: 'list_void_reason'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			label: 'Void Reason'

	no_labs_orders:
		model:
			source: ['Yes']
		view:
			columns: 1
			control: 'checkbox'
			class: 'checkbox-only section-required-check'
			label: 'No Lab Orders'

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'careplan_order'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Other Form'
					direct_attachment: 'Yes'
					source: 'Scanned Document'
					form_code: '{code}'
					form_name: 'careplan_order'
					form_filter: 'careplan_order'
					patient_id: '{patient_id}'
					site_id: '{site_id}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
	name: '{summary}'

	reportable: true
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan_order:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		patient_diagnosis:
			link:
				patient_id: 'patient_id'
			filter:
				active: 'Yes'
				rank: 1
			max: 'created_on'
	indexes:
		many: [
			['patient_id']
			['careplan_id']
			['order_format']
			['supplies_pa_id']
			['supplies_insurance_id']
			['nursing_pa_id']
			['rental_pa_id']
			['rental_insurance_id']
			['nursing_insurance_id']
		]
		unique: [
			['code']
		]
	sections_group: [
		'Referral Details':
			hide_header: true
			indent: false
			fields: ['code', 'order_format', 'tab_label', 'single_rx_dc', 'site_id', 'order_no', 'team_id', 'referral_source_id',
			'therapy_id', 'prescriber_id', 'physician_id', 'territory_id', 'commissioned_sales_rep',
			'requires_nursing']
			tab: 'Referral Info'
		'Single Prescription':
			hide_header: true
			indent: false
			fields: ['subform_single_order']
			tab: 'Referral Info'
		'Single Supply Order':
			hide_header: true
			indent: false
			fields: ['subform_single_supply']
			tab: 'Referral Info'
		'Diagnosis':
			hide_header: true
			indent: false
			fields: ['dx_ids']
			tab: 'Referral Info'
		'D/C':
			hide_header: true
			indent: false
			compact: true
			fields: ['therapy_set_dc']
			tab: 'Referral Info'
		'Billing':
			hide_header: true
			indent: false
			fields: ['billing_method', 'requires_assistance', 'assistance_notes', 'bill_supplies',
			'supplies_insurance_id', 'supply_billable_id', 'supplies_pa_id',
			'nursing_insurance_id', 'nurse_billable_id', 'nursing_pa_id', 'rental_insurance_id', 'rental_pa_id', 'rental_billable_id']
			tab: 'Billing'
		'Payers':
			hide_header: true
			indent: false
			fields: ['payer_ids']
			tab: 'Billing'
		'Patient Assistance':
			hide_header: true
			indent: false
			fields: ['patient_assistance']
			tab: 'Billing'
		'Nursing':
			hide_header: true
			indent: false
			fields: ['nursing_status', 'nursing_agency_id', 'infusion_suite_id','first_visit_date']
			tab: 'Nursing'
		'Pharmacy':
			hide_header: true
			indent: false
			fields: ['template_id', 'supply_kit_id', 'written_date', 'expiration_date',
			'start_date', 'stop_date']
			tab: 'Pharmacy'
		'Orders':
			hide_header: true
			indent: false
			fields: ['subform_order']
			tab: 'Orders/Rx'
		'Active Prescriptions':
			hide_header: true
			indent: false
			fields: ['active_rx']
			tab: 'Orders/Rx'
		'Supplies':
			indent: false
			fields: ['subform_supply']
			tab: 'Supplies'
		'Rentals':
			indent: false
			fields: ['subform_rental']
			tab: 'Rentals'
		'Labs Completed':
			hide_header: true
			indent: false
			fields: ['no_labs_orders']
			compact: true
			tab: 'Labs'
		'Labs':
			indent: false
			fields: ['subform_lab']
			tab: 'Labs'
		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'
		'Void':
			modal: true
			fields: ['show_void_warnings', 'void_warnings', 'void', 'voided_datetime', 'voided_by', 'void_reason_id']
	]

view:
	dimensions:
		width: '85%'
		height: '75%'
	hide_cardmenu: true
	block:
		validate: [
			name: 'OrderBlock'
			allowed_fields: ['void', 'void_reason_id', 'voided_datetime']
		]
	validate: [
		{
			name: "SSCheckEventFilters"
		},
		{
			name: 'CheckInsuranceDenial'
		}
	]
	comment: 'Patient > Patient Referral'
	grid:
		fields: ['summary', 'prescriber_id', 'written_date', 'void']
		sort: ['-created_on']
	find:
		basic: ['therapy_id', 'prescriber_id', 'void']
	label: 'Patient Referral'
	open: 'edit'