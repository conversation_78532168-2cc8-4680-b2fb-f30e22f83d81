#TABLE: RDAMGRH0_ALRGN_GRP_HIST
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#REPL_DAM_ALRGN_GRP
	repl_dam_alrgn_grp:
		model:
			type: 'int'
		view:
			label: 'Replacement DAM Specific Allergen Group Code'
			readonly: true
			columns: 2

	#PREV_DAM_ALRGN_GRP
	prev_dam_alrgn_grp:
		model:
			type: 'int'
		view:
			label: 'Previous DAM Specific Allergen Group Code'
			readonly: true
			columns: 2

	#DAM_ALRGN_GRP_REPL_EFF_DT
	dam_alrgn_grp_repl_eff_dt:
		model:
			type: 'date'
		view:
			label: 'DAM Specific Allergen Group Code Replacement Effective Date	'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{repl_dam_alrgn_grp} {prev_dam_alrgn_grp} {dam_alrgn_grp_repl_eff_dt}"
	indexes:
		many: [
			['repl_dam_alrgn_grp']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['repl_dam_alrgn_grp', 'prev_dam_alrgn_grp', 'dam_alrgn_grp_repl_eff_dt']

view:
	comment: 'Manage > List FDB DAM Specific Allergen Group Code History Table'
	find:
		basic: ['repl_dam_alrgn_grp', 'prev_dam_alrgn_grp', 'dam_alrgn_grp_repl_eff_dt']
	grid:
		fields: ['repl_dam_alrgn_grp', 'prev_dam_alrgn_grp', 'dam_alrgn_grp_repl_eff_dt']
	label: 'List FDB DAM Specific Allergen Group Code History Table'
