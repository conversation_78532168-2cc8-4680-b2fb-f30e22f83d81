#TABLE: RADIMMO5_MONO
#could not open the form
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#DDI_MONOX
	ddi_monox:
		model:
			type: 'int'
		view:
			label: 'Expanded Monograph Number'
			readonly: true
			columns: 3

	#ADI_MONOSN
	adi_monosn:
		model:
			type: 'int'
		view:
			label: 'Monograph Text Sequence Number'
			readonly: true
			columns: 3

	#IAMIDENTN
	iamidentn:
		view:
			label: 'Monograph Line Identifier'
			readonly: true
			columns: 3

	#IAMTEXTN
	iamtextn:
		view:
			label: 'Monograph Text'
			readonly: true
			columns: 3

	#IAMREFCAT
	iamrefcat:
		view:
			label: 'Reference Category Line Identifier'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{iamtextn}"
	indexes:
		many: [
			['ddi_monox']
		]
	sections:
		'Drug-Drug Interactions':
			fields: ['ddi_monox', 'adi_monosn', 'iamidentn', 'iamrefcat', 'iamtextn']

view:
	comment: 'Manage > List FDB Drug-Drug Interaction Monograph Text Table'
	find:
		basic: ['ddi_monox']
	grid:
		fields: ['ddi_monox', 'adi_monosn', 'iamidentn', 'iamtextn', 'iamrefcat']
	label: 'List FDB Drug-Drug Interaction Monograph Text Table'
