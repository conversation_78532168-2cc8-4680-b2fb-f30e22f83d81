fields:
	benefit_stage_qualifier:
		model:
			required: true
			source: 'list_ncpdp_ext_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '393-MV'
		view:
			columns: 3
			note: '393-MV'
			label: 'Benefit Stage'

	benefit_stage_amount:
		model:
			required: true
			type: 'decimal'
			rounding: 0.01
			max: 99999999.99
		view:
			columns: 3
			note: '394-MW'
			label: 'Benefit Stage Amt'
			class: 'numeral money'
			format: '$0,0.00'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['benefit_stage_qualifier', 'benefit_stage_amount']
	sections:
		'Benefit Stage':
			hide_header: true
			indent: false
			fields: ['benefit_stage_qualifier', 'benefit_stage_amount']

view:
	dimensions:
		width: '50%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Response Benefit Stage'
	grid:
		fields: ['benefit_stage_qualifier', 'benefit_stage_amount']
		sort: ['-created_on']
	label: 'Response Benefit Stage'
	open: 'read'