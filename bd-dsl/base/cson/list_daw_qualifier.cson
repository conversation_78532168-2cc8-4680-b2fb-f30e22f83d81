fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'
			columns: 2

	name:
		model:
			required: true
			max: 128
		view:
			label: 'Name'
			findunique: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: '{code} - {name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name']

view:
	comment: 'Manage > DAW Qualifier'
	find:
		basic: ['code','name']
	grid:
		fields: ['code','name']
		sort: ['code']
	label: 'DAW Qualifier'
	open: 'read'
