fields:
	# Link
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
	

	date_placed:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Date placed/Accessed'

	date_discontinued:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Date discontinued/Deceased'

	device:
		model:
			source: 'list_catheter_device'
			sourceid: 'code'
			if:
				'other':
					fields: ['device_other']
		view:
			columns: 2
			control: 'select'
			label: 'Drug delivery device'

	device_other:
		view:
			columns: 2
			label: 'Drug delivery device Other'

	type_id:
		model:
			required: true
			source: 'list_catheter_type'
			sourceid: 'code'
			if:
				'picc_midline':
					fields: ['mid_circ', 'lumens']
				'port':
					fields: ['lumens']
				'other':
					fields: ['type_other']
		view:
			columns: -2
			control: 'select'
			label: 'Type'

	type_other:
		model:
			required: true
		view:
			columns: 2
			label: 'Type Other'

	lumens:
		model:
			max: 32
			source: ['1', '2', '3', '4', '5']
		view:
			columns: 2
			control: 'radio'
			label: 'Number of lumens'

	mid_circ:
		model:
			prefill: ['patient_catheter']
			type: 'int'
			min: 1
		view:
			columns: 2
			label: 'Mid-arm circumference (cm)'

	location:
		model:
			max: 32
			source: ['Antecubital', 'Hand', 'Chest', 'Forearm', 'Upper arm', 'Other']
			if:
				'Other':
					fields: ['location_details']
		view:
			columns: -2
			control: 'radio'
			label: 'Location'

	location_details:
		view:
			columns: 2
			control: 'area'
			label: 'Location Details'

	side:
		model:
			max: 32
			source: ['Right', 'Left']
		view:
			columns: -2
			control: 'radio'
			label: 'Side'

	dressing:
		model:
			max: 32
			source: ['Occlusive', 'Gauze & tape', 'Other']
			if:
				'Other':
					fields: ['dressing_details']
		view:
			columns: 2
			control: 'radio'
			label: 'Dressing Type'

	dressing_details:
		view:
			columns: 2
			control: 'area'
			label: 'Dressing Type Details'

	gauge:
		model:
			type: 'int'
		view:
			columns: 2
			label: 'Needle Size (Gauge)'

	needle_length:
		model:
			type: 'int'
		view:
			columns: -2
			label: 'Needle length'

	length_type:
		model:
			source: ['mm', 'inches']
		view:
			columns: 2
			control: 'radio'
			label: 'Needle length Type'

	facility:
		view:
			columns: 2
			label: 'Facility where placed:'

	self_infuse:
		model:
			source: ['No', 'Yes']
		view:
			columns: -2
			control: 'radio'
			label: 'Patient is competent to self-infuse without medical supervision?'

	flush:
		view:
			control: 'area'
			note: 'if provided'
			label: 'Flush recommendation'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	prefill:
		patient_catheter:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['patient']
		review:     ['admin', 'pharm']
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	bundle: ['patient']
	name: ['patient_id', 'created_on']
	indexes:
		many: [
			['patient_id']
			['type_id']
		]
	sections:
		'Patient Catheter Information':
			fields: ['date_placed', 'date_discontinued', 'device', 'device_other', 'type_id', 'type_other', 'lumens',
			'mid_circ', 'location', 'location_details', 'side', 'dressing',
			'dressing_details', 'gauge', 'needle_length', 'length_type', 'facility', 'flush']

view:
	hide_cardmenu: true
	comment: 'Patient > Catheter'
	find:
		basic: ['device', 'type_id']
	grid:
		fields: ['created_by', 'created_on', 'device', 'type_id', 'location']
	label: 'Patient Catheter'
	open: 'read'
