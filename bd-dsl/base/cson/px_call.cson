fields:

	# Links
	patient_id:
		model:
			access:
				read: ['nurse', 'pharm']
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	type:
		model:
			source: ['Initial', 'Ongoing']
			required: true
		view:
			columns: 2
			control: 'radio'
			label: 'Type'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'nurse', 'pharm', 'csr']
		delete:     ['admin']
		read:       ['admin', 'nurse', 'pharm', 'patient', 'csr']
		read_all:   ['admin', 'nurse', 'pharm', 'patient', 'csr']
		request:    []
		update:     ['admin', 'nurse', 'pharm', 'csr']
		review:     []
		update_all: ['admin', 'nurse', 'pharm', 'csr']
		write:      ['admin', 'nurse', 'pharm', 'patient', 'csr']
	bundle: ['patient']
	name: ['patient_id', 'type']
	indexes:
		many: [
			['patient_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				patient_id: 'patient_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	sections_group: [
		'Patient Experience Call':
			sections: [
				'Type':
					fields: ['type']
			]
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Patient Experience Call'
	grid:
		fields: ['created_on', 'created_by']
		sort: ['-created_on']
	label: 'Patient Experience Call'
	open: 'read'
