#TABLE:RDAMAPM0_ALRGN_PICKLIST_MSTR
fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#RDAMAPM0_ALRGN_PICKLIST_MSTR.DAM_CONCEPT_ID
	dam_concept_id:
		model:
			type:'int'
		view:
			label: 'DAM Allergen Group'
			columns: 2.1

	#RDAMAPM0_ALRGN_PICKLIST_MSTR.DAM_CONCEPT_ID_TYP
	dam_concept_id_typ:
		model:
			type:'int'
		view:
			label: 'DAM Allergen Group Description'
			columns: 2

	#RDAMAPM0_ALRGN_PICKLIST_MSTR.DAM_CONCEPT_ID_TYP_DESC
	dam_concept_id_desc:
		view:
			label: 'Allergy Description'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		many: [
			['code']
			['dam_concept_id']
		]
	name: ['dam_concept_id_desc']
	sections:
		'FDB Allergen Picklist Master':
			fields: ['dam_concept_id', 'dam_concept_id_typ', 'dam_concept_id_desc']

view:
	comment: 'Manage > FDB Allergen Picklist Master'
	find:
		basic: ['dam_concept_id_desc']
	grid:
		fields: ['dam_concept_id', 'dam_concept_id_typ', 'dam_concept_id_desc']
	label: 'FDB Allergen Picklist Master'
