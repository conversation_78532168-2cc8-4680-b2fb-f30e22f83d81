fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	external_id:
		view:
			label: 'External ID'
			readonly: true
			offscreen: true

	name:
		model:
			required: true
		view:
			label: 'Name'
			validate: [
				{
					name: 'UpdateTabLabel'
					fields: ['name']
				}
			]
			findunique: true
			columns: 3

	site_id:
		model:
			source: 'site'
			multi: true
		view:
			label: 'Assigned Site(s)'

	account_no:
		view:
			label: 'Account #'

	terms:
		view:
			label: 'Terms'

	return_info:
		view:
			label: 'Return Info'

	phone:
		model:
			required: true
			max: 21
		view:
			format: 'us_phone'
			label: 'Phone'
			columns: 3

	fax:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Fax'
			columns: 3

	street:
		view:
			label: 'Street 1'
			class: "api_prefill"
			columns: 'addr_1'
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'street': ['addressLabel']
					'city': ['city']
					'state': ['stateCode']
					'zip': ['postalCode']
					'county': ['countryCode']
			]

	street2:
		view:
			label: 'Street 2'
			columns: 'addr_2'

	city:
		view:
			label: 'City'
			columns: 'addr_city'

	county:
		view:
			label: 'County'
			columns: 'addr_county'

	state_id:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			label: 'State'
			columns: 'addr_state'

	zip:
		model:
			max: 10
			min: 5
		view:
			format: 'us_zip'
			label: 'Zip'
			columns: 'addr_zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'zip'
						city:'city'
						state:'state_id'
			]

	contact_name:
		view:
			label: 'Contact Name'
			columns: 3

	notes:
		view:
			control: 'area'
			label: 'Notes'

	edi_enabled:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['EDI Options']
		view:
			label: 'Uses EDI Transmissions?'
			columns: 2

	# EDI Options
	interchange_rec_id:
		view:
			label: 'Interchange Receiver ID'
			columns: 3

	interchange_cn:
		view:
			label: 'Interchange Control Number'
			columns: 3

	usage_indicator:
		model:
			source: ['Test', 'Production']
		view:
			control: 'radio'
			label: 'Usage Indicator'
			columns: 3

	segment_term:
		view:
			label: 'Segment Terminator'
			columns: 3

	id_code_qualifier:
		view:
			label: 'Identifier Code Qualifier'
			columns: 3

	id_code:
		view:
			label: 'Identifier Code'
			columns: 3

	reference_id_qualifier:
		view:
			label: 'Reference Identifier Qualifier'
			columns: 3

	reference_id:
		view:
			label: 'Reference Identifier'
			columns: 3

	entity_id_code:
		view:
			label: 'Entity Identifier Code'
			columns: 3

	prod_service_id_qualifier:
		view:
			label: 'Product / Service ID Qualifier'
			columns: 3

	send_company_address:
		model:
			source: ['No', 'Yes']
		view:
			label: 'Send company address?'
			columns: 3

	msd_enabled:
		model:
			source: ['No', 'Yes']
		view:
			label: 'MSD Automated Ordering?'
			columns: 2

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'list_supplier'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					direct_attachment: 'Yes'
					supplier_id: '{code}'
					assigned_to: 'Supplier'
					source: 'Scanned Document'
					form_code: '{code}'
					form_name: 'list_supplier'
					form_filter: 'list_supplier'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'pharm']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'pharm', 'physician']
		request:    []
		update:     ['nurse']
		update_all: ['admin','csr', 'liaison', 'pharm']
		write:      ['admin','csr', 'liaison', 'pharm']
	indexes:
		unique: [
			['name', 'account_no']
			['code']
		]
	name: ['name']
	bundle: ['inventory']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'phone', 'fax']
			tab: 'Supplier'
		'Account Details':
			indent: false
			fields: ['site_id', 'account_no', 'terms', 'return_info', 'contact_name', 'edi_enabled', 'msd_enabled']
			tab: 'Supplier'
		'EDI Options':
			indent: false
			fields: ['interchange_rec_id', 'interchange_cn', 'usage_indicator',
			'segment_term', 'id_code_qualifier', 'id_code', 'reference_id_qualifier',
			'reference_id', 'entity_id_code', 'prod_service_id_qualifier', 'send_company_address']
			tab: 'Supplier'
		'Address':
			indent: false
			fields: ['street', 'street2', 'city', 'state_id', 'zip', 'county']
			tab: 'Supplier'
		'Notes':
			indent: false
			fields: ['notes']
			tab: 'Supplier'
		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'

view:
	comment: 'Supplier'
	find:
		basic: ['name', 'phone']
	grid:
		fields: ['name', 'phone', 'fax', 'notes']
		sort: ['name']
	label: 'Supplier'
	open: 'read'
