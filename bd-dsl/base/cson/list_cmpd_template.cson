fields:

	name:
		model:
			max: 64
			required: true
		view:
			columns: 4
			label: 'Name'

	compounding_instructions:
		model:
			required: true
		view:
			control: 'area'
			label: 'Compounding/Reconstitution Instructions'
			columns: 3

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 4
			control: 'radio'
			label: 'Can Sync Record'

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 4
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['dispense']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['name']
		]
	name: ['name']
	sections:
		'Compounding Instructions Template':
			fields: ['name', 'compounding_instructions', 'allow_sync', 'active']
	
view:
	comment: 'Manage > Compounding Instructions Template'
	find:
		basic: ['name']
	grid:
		fields: ['name','compounding_instructions']
		sort: ['name']
	label: 'Compounding Instructions Template'
	open: 'read'
