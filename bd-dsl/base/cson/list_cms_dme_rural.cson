fields:
	state:
		model:
			type: 'text'
		view:
			label: 'STATE'

	dmeposruralzipcode:
		model:
			type: 'text'
		view:
			label: 'DMEPOS RURAL ZIP CODE'

	yearqtr:
		model:
			type: 'text'
		view:
			label: 'YEAR/QTR'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['state']
	indexes:
		many: [
			['dmeposruralzipcode']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['state', 'dmeposruralzipcode']

view:
	comment: 'Manage > List CMS DME Rural'
	find:
		basic: ['dmeposruralzipcode']
	grid:
		fields: ['dmeposruralzipcode']
	label: 'List CMS DME Rural'