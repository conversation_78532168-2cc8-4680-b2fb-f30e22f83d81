fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status_id:
					'static': ['1', '5']
			if:
				'*':
					fields: ['order_no', 'order_item_id']
		view:
			columns: 2
			label: 'Referral'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/careplan_order/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'order_no': ['order_no']
			]

	order_no:
		view:
			label: 'Referral No'
			readonly: true
			columns: 2

	order_item_id:
		model:
			multi: false
			source: 'careplan_order_rx'
			sourcefilter:
				order_no:
					'dynamic': '{order_no}'
		view:
			columns: 2
			label: 'Prescription'

	category:
		model:
			required: true
			source: ['Adverse Event', 'Complaint', 'Intervention']
			if:
				'Complaint':
					fields: ['complaint_type']
				'Adverse Event':
					fields: ['ae_type', 'ae_date', 'affect_health']
				'Intervention':
					fields: ['inter_type']
		view:
			columns: 2
			control: 'radio'
			label: 'Category'

	intr_typ_ftr:
		model:
			multi: true
			source: 'list_intervention_type'
			sourceid: 'code'
		view:
			label: 'Intervention Type Filter'
			readonly: true
			offscreen: true

	inter_type:
		model:
			required: true
			multi: true
			source: 'list_intervention_type'
			sourceid: 'code'
			sourcefilter:
				active:
					'static': 'Yes'
				code:
					'dynamic': '{intr_typ_ftr}'
		view:
			columns: 2
			control: 'checkbox'
			label: 'Intervention Type'

	ae_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 2
			label: 'Adverse Event Date'
			template: '{{now}}'

	complaint_type:
		model:
			required: true
			source: ['Shipping Related', 'Pharmacy Related', 'Nursing Related', 'Other']
			if:
				'Shipping Related':
					sections: ['Shipping Related']
					fields: ['shipping_issue_complaint','resolved_shipping']
				'Pharmacy Related':
					sections: ['Pharmacy Related']
					fields: ['pharmacy_issue_complaint','resolved_pharmacy']
				'Nursing Related':
					sections: ['Nursing Related']
					fields: ['nursing_issue_complaint', 'resolved_nursing']
				'Other':
					sections: ['Other Details']
					fields: ['resolved_other']

		view:
			columns: 2
			control: 'radio'
			label: 'Complaint Related To:'

	ae_type:
		model:
			required: true
			source: ['Shipping Related', 'Pharmacy Related', 'Nursing Related', 'Other']
			if:
				'Shipping Related':
					sections: ['Shipping Related']
					fields: ['shipping_issue_ae_code','resolved_shipping']
				'Pharmacy Related':
					sections: ['Pharmacy Related']
					fields: ['pharmacy_issue_ae_code','resolved_pharmacy']
				'Nursing Related':
					sections: ['Nursing Related']
					fields: ['nursing_issue_ae_code', 'resolved_nursing']
				'Other':
					sections: ['Other Details']
					fields: ['resolved_other']
		view:
			columns: 2
			control: 'radio'
			label: 'Adverse Event Related To:'

	nursing_issue_complaint:
		model:
			required: true
			source: 'list_nursing_issue'
			sourcefilter:
				ae_only:
					'static': '!Yes'
		view:
			columns: 2
			control: 'checkbox'
			label: 'Nursing Complaint Type'

	pharmacy_issue_complaint:
		model:
			required: true
			source: 'list_pharmacy_issue'
			sourcefilter:
				ae_only:
					'static': '!Yes'
		view:
			columns: 2
			control: 'checkbox'
			label: 'Pharmacy Complaint Type'

	shipping_issue_complaint:
		model:
			required: true
			source: 'list_shipping_issue'
			sourcefilter:
				ae_only:
					'static': '!Yes'
		view:
			columns: 2
			control: 'checkbox'
			label: 'Shipping Complaint Type'

	nursing_issue_ae_code:
		model:
			required: true
			source: 'list_nursing_issue'
			sourceid: 'code'
			sourcefilter:
				complaint_only:
					'static': '!Yes'
		view:
			columns: 2
			control: 'checkbox'
			label: 'Nursing AE Type'

	pharmacy_issue_ae_code:
		model:
			required: true
			source: 'list_pharmacy_issue'
			sourceid: 'code'
			if:
				'Experienced Side-Effect (Drug Related)':
					fields: ['reaction_id', 'medwatch_completed']
			sourcefilter:
				complaint_only:
					'static': '!Yes'
		view:
			columns: 2
			control: 'checkbox'
			label: 'Pharmacy AE Type'

	reaction_id:
		model:
			required: true
			multi: true
			source: 'list_reaction'
		view:
			columns: 2
			label: 'Reactions Experienced'

	shipping_issue_ae_code:
		model:
			required: true
			source: 'list_shipping_issue'
			sourceid: 'code'
			sourcefilter:
				complaint_only:
					'static': '!Yes'
		view:
			columns: 2
			control: 'checkbox'
			label: 'Shipping AE Type'

	medwatch_completed:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Was a MedWatch completed?'

	description_nursing:
		model:
			required: true
		view:
			control: 'area'
			label: 'Description of issue'

	description_pharmacy:
		model:
			required: true
		view:
			control: 'area'
			label: 'Description of issue'

	description_shipping:
		model:
			required: true
		view:
			control: 'area'
			label: 'Description of issue'

	affect_health:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Did the issue affect the patient’s health/disrupt or delay prescribed therapy?'

	cold_chain_management:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Is this entry related to Cold Chain Management?'

	resolved:
		model:
			source: ["No", "Yes"]
			default: "No"
		view:
			columns: 2
			control: 'radio'
			label: 'Issue Resolved?'
			readonly: true
			offscreen: true

	resolved_pharmacy:
		model:
			source: ["No", "Yes"]
			default: "No"
			if:
				'Yes':
					fields: ['resolution_date', 'outcome_pharmacy']
		view:
			columns: 2
			control: 'radio'
			label: 'Issue Resolved?'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'resolved'
					overwrite: true
				}
			]

	resolution_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 2
			label: 'Resolution date'

	resolved_shipping:
		model:
			source: ["No", "Yes"]
			default: "No"
			if:
				'Yes':
					fields: ['resolution_date', 'outcome_shipping']
		view:
			columns: 2
			control: 'radio'
			label: 'Issue Resolved?'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'resolved'
					overwrite: true
				}
			]
	resolved_nursing:
		model:
			source: ["No", "Yes"]
			default: "No"
			if:
				'Yes':
					fields: ['resolution_date', 'outcome_nursing']
		view:
			columns: 2
			control: 'radio'
			label: 'Issue Resolved?'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'resolved'
					overwrite: true
				}
			]

	resolved_other:
		model:
			source: ["No", "Yes"]
			default: "No"
			if:
				'Yes':
					fields: ['resolution_date']
		view:
			columns: 2
			control: 'radio'
			label: 'Issue Resolved?'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'resolved'
					overwrite: true
				}
			]

	outcome:
		model:
			multi: true
			source: 'list_outcome'
		view:
			columns: 2
			label: 'Outcome'
			control: 'checkbox'
			readonly: true
			offscreen: true

	outcome_pharmacy:
		model:
			required: true
			multi: true
			source: 'list_outcome'
			sourcefilter:
				type:
					'static': 'Pharmacy'
		view:
			columns: 2
			label: 'Outcome'
			control: 'checkbox'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'outcome'
					overwrite: true
				}
			]

	outcome_shipping:
		model:
			required: true
			multi: true
			source: 'list_outcome'
			sourcefilter:
				type:
					'static': 'Shipping'
		view:
			columns: 2
			label: 'Outcome'
			control: 'checkbox'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'outcome'
					overwrite: true
				}
			]

	outcome_nursing:
		model:
			required: true
			multi: true
			source: 'list_outcome'
			sourcefilter:
				type:
					'static': 'Nursing'
		view:
			columns: 2
			label: 'Outcome'
			control: 'checkbox'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'outcome'
					overwrite: true
				}
			]

	outcome_details:
		view:
			control: 'area'
			label: 'Outcome Details'

	other_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Other Notes'

	comments:
		model:
			required: false
		view:
			control: 'area'
			label: 'Comments'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin','liaison', 'nurse', 'pharm']
		update:     []
		update_all: ['admin','csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin','csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
			['order_id']
			['order_no']
			['order_item_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
	name: ['patient_id', 'order_id', 'category', 'intr_typ_ftr', 'inter_type', 'complaint_type', 'ae_type']
	sections:
		'Adverse Event/Complaint Details':
			fields: ['order_id',  'order_no', 'order_item_id', 'category', 'ae_date', 'ae_type', 'complaint_type']
		'Shipping Related':
			hide_header: true
			fields: ['shipping_issue_ae_code','shipping_issue_complaint','description_shipping', 'cold_chain_management']
			tab: 'Shipping Related'
		'Pharmacy Related':
			hide_header: true
			fields: ['pharmacy_issue_ae_code', 'reaction_id', 'medwatch_completed', 'pharmacy_issue_complaint','description_pharmacy']
			tab: 'Pharmacy Related'
		'Nursing Related':
			hide_header: true
			fields: ['nursing_issue_ae_code','nursing_issue_complaint','description_nursing']
			tab: 'Nursing Related'
		'Other Details':
			fields: ['other_details']
			tab: 'Other Details'
		'Outcome':
			fields: ['affect_health', 'resolved_pharmacy', 'resolved_shipping',
			'resolved_nursing', 'resolved_other', 'resolution_date',
			'resolved', 'outcome']
			tab: 'Outcome'
		'Comments':
			fields: ['comments']

	transform_post: [
			name: "AutoNote"
			arguments:
				subject: "Adverse Event/Complaint"
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Intake > Adverse Event/Complaint'
	find:
		basic: ['category', 'ae_type', 'complaint_type']
	grid:
		fields: ['created_on', 'created_by', 'category', 'ae_type', 'complaint_type','affect_health']
		sort: ['-id']
	label: 'Adverse Event/Complaint'
	open: 'read'
