fields:

	patient_id:
		model:
			required: true
			source: 'patient'
		view:
			label: 'For Patient'

	rep_id:
		model:
			required: true
			source: 'user'
			sourcefilter:
				role:
					'static': ['liaison']
		view: 
			label: 'Sales Rep'

	progress_note:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Progress Note'

	allergy:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Allergies'

	medication:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Medications'

	orders:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Orders'

	dispense:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Dispense'

	prior_auth:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Insurance Auth' # triggers notification for patient_insurance_authorization events

	intervention:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Interversions'
	
	external_document:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'External Documents'

	insurance:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Insurance'

	external_document:
		model:
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'External Document'

model:
	access:
		create: ['admin', 'liaison']
		create_all: ['admin', 'liaison']
		delete: ['admin', 'liaison']
		read: ['admin', 'liaison']
		read_all: ['admin', 'liaison']
		request: ['admin', 'liaison']
		update: ['admin', 'liaison']
		update_all: ['admin', 'liaison']
		write: ['admin', 'liaison']
	name: '{patient_id} ({rep_id})'
	indexes:
		many: [
			['patient_id']
			['rep_id']
			['patient_id', 'rep_id']
		]
		unique: [
			['patient_id', 'rep_id']
		]
	sections:
		'Notifcations For':
			fields: ['patient_id','rep_id']
		'Notification':
			fields: ['progress_note', 'allergy', 'medication', 'orders', 'dispense', 'prior_auth', 'intervention', 'insurance', 'external_document']
view:
	comment: 'Patient > Sales Notification'
	grid:
		fields: ['patient_id','rep_id']
		sort: ['patient_id']
	label: 'Sale Rep Notifications'
	open: 'read'
