fields:

	site_id:
		model:
			multi: true
			required: false
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			columns: 2
			transform: [{
				name: 'EmbedRefresh',
				fields: ['invoices']
			}]

	patient_id:
		model:
			multi: true
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			columns: 2
			transform: [{
				name: 'EmbedRefresh',
				fields: ['invoices']
			}]

	type_id:
		model:
			multi: true
			required: true
			source: 'list_payer_type'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Type'
			transform: [{
				name: 'EmbedRefresh',
				fields: ['invoices']
			}]

	therapy_id:
		model:
			required: true
			max: 64
			multi: true
			source: 'list_therapy'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Therapy'
			transform: [{
				name: 'EmbedRefresh',
				fields: ['invoices']
			}]

	collector_id:
		model:
			multi: true
			source: 'wf_queue_team'
			sourceid: 'code'
			sourcefilter:
				code:
					static: 'Collector'
			default: 'Collector'
		view:
			columns: 2
			label: 'Collector Team'
			transform: [{
				name: 'EmbedRefresh',
				fields: ['invoices']
			}]

	subs_id:
		model:
			multi: true
			source: 'list_billing_csstatus'
			sourceid: 'code'
			track: true
			sourcefilter:
				status_id:
					'dynamic': '{status_id}'
		view:
			columns: 2.1
			control: 'select'
			label: 'Claim Substatus'
			class: 'status'
			transform: [{
				name: 'EmbedRefresh',
				fields: ['invoices']
			}]

	ar_aging_less_than:
		model:
			type: 'int'
		view:
			columns: 'modifier'
			label: 'A/R Aging <'
			transform: [{
				name: 'EmbedRefresh',
				fields: ['invoices']
			}]

	ar_aging_greater_than:
		model:
			type: 'int'
			if:
				'*':
					fields: ['fu_days_per']
		view:
			columns: 'modifier'
			label: '>'
			transform: [{
				name: 'EmbedRefresh',
				fields: ['invoices']
			}]

	fu_days_less_than:
		model:
			type: 'int'
			if:
				'*':
					fields: ['fu_days_per']
		view:
			columns: 'modifier'
			label: 'Followup Days <'
			transform: [{
				name: 'EmbedRefresh',
				fields: ['invoices']
			}]

	fu_days_per:
		model:
			default: 'Billed'
			source: ['Billed', 'DOS']
		view:
			columns: 4
			control: 'radio'
			label: 'Per'
			transform: [{
				name: 'EmbedRefresh',
				fields: ['invoices']
			}]

	balance_less_than:
		model:
			type: 'int'
			if:
				'*':
					fields: ['balance_per']
		view:
			columns: 'modifier'
			label: 'Balance <'
			transform: [{
				name: 'EmbedRefresh',
				fields: ['invoices']
			}]

	balance_greater_than:
		model:
			type: 'int'
			if:
				'*':
					fields: ['balance_per']
		view:
			columns: 'modifier'
			label: '>'
			transform: [{
				name: 'EmbedRefresh',
				fields: ['invoices']
			}]

	balance_per:
		model:
			source: ['Patient', 'Claim']
		view:
			columns: 4
			control: 'radio'
			label: 'Per'
			transform: [{
				name: 'EmbedRefresh',
				fields: ['invoices']
			}]

	invoices:
		model:
			multi: true
			sourcefilter:
				site_id:
					'dynamic': '{site_id}'
				patient_id:
					'dynamic': '{patient_id}'
				type_id:
					'dynamic': '{type_id}'
				therapy_id:
					'dynamic': '{therapy_id}'
				collector_id:
					'dynamic': '{collector_id}'
				substatus_id:
					'dynamic': '{subs_id}'
				ar_aging_less_than:
					'dynamic': '{ar_aging_less_than}'
				ar_aging_greater_than:
					'dynamic': '{ar_aging_greater_than}'
				fu_days_less_than:
					'dynamic': '{fu_days_less_than}'
				fu_days_per:
					'dynamic': '{fu_days_per}'
				balance_less_than:
					'dynamic': '{balance_less_than}'
				balance_per:
					'dynamic': '{balance_per}'
				balance_greater_than:
					'dynamic': '{balance_greater_than}'
		view:
			embed:
				query: 'ar_manager_invoices'
				request_type: 'POST'
				selectable: true
			grid:
				edit: true
				rank: 'none'
				add: 'none'
				label: ['Date Billed','Invoice No', 'Patient', 'Payer', 'Expected', 'Balance', 'Profit %', 'Claim Status']
				fields: ['billed_date', 'invoice_no', 'patient_name', 'payer', 'expected', 'balance', 'profit_margin', 'claim_substatus']
				width: [10, 10, 20, 15, 10, 10, 10, 15]
				subfields_label: ['Billed', 'Profit', 'Cost', 'DOS Start', 'DOS End', 'A/R Aging', 'Followup Date', 'Site']
				subfields: ['billed', 'profit', 'cost', 'dos_start', 'dos_end', 'ar_aging', 'followup_date', 'site_name']
				subfields_width: [10, 10, 10, 10, 10, 10, 20, 20]
			label: 'Invoices'
			validate: [
				name: 'CollectEmbedFieldIds',
				target: 'inv_ids'
			]

	inv_ids:
		model:
			multi: true
			source: 'billing_invoice'
			if:
				'*':
					fields: ['total_billed', 'total_expected', 'total_cost', 'total_paid', 'total_adjusted', 'total_balance_due', 'total_profit', 'total_margin']
		view:
			label: 'Invoices'
			readonly: true
			offscreen: true
			transform: [
				name: 'LoadARInvoiceData'
			]

	total_billed:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			label: 'Billed' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_expected:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			label: 'Amount' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_cost:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Cost' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_paid:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
			min: 0.00
		view:
			columns: 4
			label: 'Paid' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_adjusted:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Adjusted' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_balance_due:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Balance Due' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_profit:
		model:
			required: false
			type: 'decimal'
			default: 0.00
			min: 0.00
		view:
			columns: 4
			label: 'Profit'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_margin:
		model:
			type: 'decimal'
			rounding: 0.00001
			max: 100
		view:
			columns: 4
			label: 'Margin'
			class: 'numeral discount'
			format: 'percent'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: []
	save: false
	name: 'A/R Manager'
	sections:
		'A/R Manager':
			hide_header: true
			indent: false
			fields: ['site_id', 'patient_id', 'type_id', 'therapy_id',
			'collector_id', 'subs_id', 'ar_aging_less_than',
			'ar_aging_greater_than', 'fu_days_less_than', 'fu_days_per', 'balance_less_than',
			'balance_greater_than', 'balance_per', 'inv_ids']
		'Invoices':
			hide_header: true
			indent: false
			fields: ['invoices']
		'Totals':
			hide_header: true
			indent: false
			fields: ['total_billed', 'total_expected', 'total_cost', 'total_paid',
			'total_adjusted', 'total_balance_due', 'total_profit', 'total_margin']

view:
	hide_header: true
	hide_cardmenu: true
	comment: 'Billing > A/R Manager'
	label: 'A/R Manager'
	open: 'edit'
