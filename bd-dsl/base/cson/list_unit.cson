fields:
	#list_fdb_unit_master.uom_mstr_id
	uom_mstr_id:
		model:
			type: 'int'
		view:
			label: 'Unit of Measure ID'
			findunique: true
			columns: 3
			readonly: true

	#list_fdb_unit_master.uom_stds_org_abbr
	code:
		model:
			required: true
			search: 'A'
		view:
			label: 'Unit of Measure Code'
			findunique: true
			columns: 3
			readonly: true

	#list_fdb_unit_master.uom_mstr_desc
	name:
		model:
			required: true
			search: 'A'
		view:
			label: 'Name'
			findunique: true
			columns: 3
			readonly: true

	#list_fdb_unit_master.dose_ind = '1' = 'Yes' Else 'No'
	dose_unit:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['requires_infusion_time', 'requires_therapy_days']
		view:
			control: 'radio'
			label: 'Dosing Unit?'
			class: 'checkbox-only'
			columns: 3
			readonly: true

	#list_fdb_unit_master.ratio_ind = '0' = 'Yes' Else 'No'
	base_unit:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Base Unit?'
			note: 'Base units can only be comprised on a list unit (i.e. mg/kg is an invalid base unit)'
			class: 'checkbox-only'
			columns: 3
			readonly: true

	#list_fdb_unit_master.rate_ind = '1' Else 'No'
	rate_unit:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Rate Unit?'
			note: 'Unit that contains a time component and used in Administration Rate (i.e. unit/minute)'
			class: 'checkbox-only'
			columns: 3
			readonly: true

	#list_fdb_unit_master.ratio_ind + 1
	unit_components:
		model:
			required: true
			max: 3
			min: 1
			type: 'int'
		view:
			label: 'Unit Components'
			columns: 3
			readonly: true

	#list_fdb_unit_master.uom_type_cd = '0001' then 'Weight'
	#list_fdb_unit_master.uom_type_cd = '0002' then 'Volume'
	#list_fdb_unit_master.uom_type_cd = '0003' then 'Time'
	#list_fdb_unit_master.uom_type_cd = '0004' then 'Quantity'
	#list_fdb_unit_master.uom_type_cd = '0005' then 'Length'
	unit_basis:
		model:
			required: true
			source: ['Weight', 'Volume', 'Time', 'Quantity', 'Length', 'Area', 'Concentration', 'Radiation', 'Energy', 'Pressure', 'Protective Factor', 'Viscosity', 'Fluorescence']
		view:
			control: 'radio'
			label: 'Unit Basis'
			columns: 3
			readonly: true

	#list_fdb_unit_master.uom_mstr_component1_id where list_unit.uom_mstr_id = uom_mstr_component1_id
	unit_component_1:
		model:
			required: false
			source: 'list_unit'
			sourceid: 'code'
		view:
			label: 'Unit Component 1'
			columns: 3
			readonly: true

	#list_fdb_unit_master.uom_mstr_component2_id where list_unit.uom_mstr_id = uom_mstr_component2_id
	unit_component_2:
		model:
			required: false
			source: 'list_unit'
			sourceid: 'code'
		view:
			label: 'Unit Component 2'
			columns: 3
			readonly: true

	#list_fdb_unit_master.uom_mstr_component3_id where list_unit.uom_mstr_id = uom_mstr_component3_id
	unit_component_3:
		model:
			required: false
			source: 'list_unit'
			sourceid: 'code'
		view:
			label: 'Unit Component 3'
			columns: 3
			readonly: true

	#list_fdb_unit_master.patient_param_required
	patient_param_required:
		model:
			source: ['Weight (Kg)', 'BSA (m²)', 'Lesions', 'cm² per Lesion', '1.73 m²', 'grams of carbohydrate']
		view:
			control: 'radio'
			label: 'Patient Parameter Required'
			columns: 3
			readonly: true

	requires_infusion_time:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Requires Infusion Time?'
			columns: 3
			readonly: true

	requires_therapy_days:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Requires Therapy Days?'
			columns: 3
			readonly: true

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 3

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 3


model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['reference']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['code']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code','name', 'dose_unit', 'unit_basis', 'base_unit', 'rate_unit',
			'unit_components', 'unit_component_1', 'unit_component_2', 'unit_component_3',
			'patient_param_required', 'requires_infusion_time', 'requires_therapy_days', 'allow_sync', 'active']

view:
	comment: 'Manage > Measurement Unit'
	find:
		basic: ['code','name', 'dose_unit', 'unit_basis']
	grid:
		fields: ['code','name', 'dose_unit', 'unit_basis']
		sort: ['code']
	label: 'Measurement Unit'
	open: 'read'
