fields:
	claim_no:
		view:
			label: 'Claim #'
			note: 'Claim # against the invoice'
			readonly: true
			offscreen: true

	patient_id:
		model:
			type: 'int'
			source: 'patient'
		view:
			label: 'Patient ID'
			readonly: true
			offscreen: true

	site_id:
		model:
			type: 'int'
			source: 'site'
		view:
			label: 'Site ID'
			readonly: true
			offscreen: true

	payer_id:
		model:
			type: 'int'
			source: 'payer'
		view:
			label: 'Payer ID'
			readonly: true
			offscreen: true

	reviewed:
		model:
			source: ['Yes']
		view:
			columns: 3
			control: 'checkbox'
			label: 'Reviewed?'
			class: 'checkbox-only'

	reviewed_by:
		model:
			type: 'int'
			source: 'user'
		view:
			columns: 3
			label: 'Reviewed By'
			template: '{{user.id}}'

	notes:
		view:
			control: 'area'
			label: 'Notes'

	control_number:
		view:
			columns: 4
			label: 'Control Number'
			note: 'ST02'
			readonly: true
			_meta:
				path: 'transactions[].controlNumber'

	claim_transaction_batch_number:
		view:
			columns: 4
			label: 'Claim Transaction Batch Number'
			note: 'ST03'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimTransactionBatchNumber'

	trading_partner_claim_number:
		view:
			columns: 4
			label: 'Trading Partner Claim Number'
			note: 'Loop 2200[D|E] REF02 where REF01=1K'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].claims[].claimStatus.tradingPartnerClaimNumber'

	bill_type_identifier:
		view:
			columns: 4
			label: 'Bill Type Identifier'
			note: 'Loop 2200[D|E] REF02 where REF01=BLT'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].claims[].claimStatus.billTypeIdentifier'

	reference_identification:
		view:
			columns: 4
			label: 'Reference Identification'
			note: 'BHT03'
			readonly: true
			_meta:
				path: 'transactions[].referenceIdentification'

	service_provider_organization_name:
		view:
			columns: -2
			label: 'Service Provider Name'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].serviceProvider.organizationName'

	service_provider_npi:
		view:
			columns: 4
			label: 'Service Provider NPI'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].serviceProvider.npi'

	service_begin_date:
		model:
			type: 'date'
		view:
			columns: -4
			label: 'Service Begin Date'
			note: '2100D DTP01'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].claims[].claimStatus.claimServiceBeginDate'

	service_end_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Service End Date'
			note: '2100D DTP01'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].claims[].claimStatus.claimServiceEndDate'

	service_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Service Date'
			note: '2100D DTP01'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].claims[].claimStatus.claimServiceDate'

	transaction_set_creation_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Transaction Set Creation Date'
			note: 'BHT04'
			readonly: true
			_meta:
				path: 'transactions[].transactionSetCreationDate'

	transaction_set_creation_time:
		model:
			type: 'time'
		view:
			columns: 4
			label: 'Transaction Set Creation Time'
			note: 'BHT05'
			readonly: true
			_meta:
				path: 'transactions[].transactionSetCreationTime'

	subscriber_last_name:
		view:
			columns: 4
			label: 'Subscriber Last Name'
			note: '2100D NM105'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].subscriber.lastName'

	subscriber_first_name:
		view:
			columns: 4
			label: 'Subscriber First Name'
			note: '2100D NM104'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].subscriber.firstName'

	subscriber_middle_name:
		view:
			columns: 4
			label: 'Subscriber Middle Name'
			note: '2100D NM105'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].subscriber.middleName'

	subscriber_suffix:
		view:
			columns: 4
			label: 'Subscriber Suffix'
			note: '2100D NM107'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].subscriber.suffix'

	patient_account_number:
		view:
			columns: 4
			label: 'Patient Account Number'
			note: 'Loop 2200[D|E] REF02 where REF01=EJ'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].claims[].claimStatus.patientAccountNumber'

	subscriber_member_id:
		view:
			columns: 4
			label: 'Subscriber Member ID'
			note: '2100D NM109 NM108=MI'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].subscriber.memberId'

	dependent_last_name:
		view:
			columns: -4
			label: 'Dependent Last Name'
			note: '2100E NM103 NM102=1'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].dependent.lastName'

	dependent_first_name:
		view:
			columns: 4
			label: 'Dependent First Name'
			note: '2100E NM104'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].dependent.firstName'

	dependent_middle_name:
		view:
			columns: 4
			label: 'Dependent Middle Name'
			note: '2100E NM105'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].dependent.middleName'

	dependent_suffix:
		view:
			columns: 4
			label: 'Dependent Suffix'
			note: '2100E NM107'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].dependent.suffix'

	payer_organization_name:
		view:
			columns: 4
			label: 'Organization Name'
			note: 'NM103'
			readonly: true
			_meta:
				path: 'transactions[].payers[].organizationName'

	payer_identification:
		view:
			columns: 4
			label: 'Payer Identification'
			note: 'NM109 NM108=PI'
			readonly: true
			_meta:
				path: 'transactions[].payers[].payerIdentification'

	payer_centers_for_medicare_and_medicaid_service_plan_id:
		view:
			columns: 4
			label: 'CMS Plan ID'
			note: 'NM109 NM108=XV'
			readonly: true
			_meta:
				path: 'transactions[].payers[].centersForMedicareAndMedicaidServicePlanId'

	service_provider_status_information_effective_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Batch Status Effective Date'
			note: 'Loop 2200C STC02'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].serviceProviderClaimStatuses[].statusInformationEffectiveDate'

	service_provider_health_care_claim_status_category_code:
		view:
			columns: -4
			label: 'Batch Status Category Code'
			note: 'STC01-1, STC010-1, STC11-1'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].serviceProviderClaimStatuses[].serviceProviderStatuses[].healthCareClaimStatusCategoryCode'

	service_provider_health_care_claim_status_category_code_value:
		view:
			columns: 2
			label: 'Batch Status Category Code Value'
			note: 'STC01-1, STC010-1, STC11-1'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].serviceProviderClaimStatuses[].serviceProviderStatuses[].healthCareClaimStatusCategoryCodeValue'

	service_provider_status_code:
		view:
			columns: -4
			label: 'Batch Status Code'
			note: 'STC01-2, STC010-2, STC11-2'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].serviceProviderClaimStatuses[].serviceProviderStatuses[].statusCode'

	service_provider_status_code_value:
		view:
			columns: 2
			label: 'Batch Status Code Value'
			note: 'STC01-2, STC010-2, STC11-2'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].serviceProviderClaimStatuses[].serviceProviderStatuses[].statusCodeValue'

	service_provider_entity_identifier_code:
		view:
			columns: -4
			label: 'Batch Entity ID Code'
			note: 'STC01-3, STC010-3, STC11-3'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].serviceProviderClaimStatuses[].serviceProviderStatuses[].entityIdentifierCode'

	service_provider_entity_identifier_code_value:
		view:
			columns: 4
			label: 'Batch Entity ID Code Value'
			note: 'STC01-3, STC010-3, STC11-3'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].serviceProviderClaimStatuses[].serviceProviderStatuses[].entityIdentifierCodeValue'

	claim_total_claim_charge_amount:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: -4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Charge Amount'
			note: '2100D AMT01'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].claims[].claimStatus.informationClaimStatuses[].totalClaimChargeAmount'

	claim_payment_amount:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Payment Amount'
			note: '2100D AMT02'
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].claims[].claimStatus.informationClaimStatuses[].claimPaymentAmount'

	claim_adjudicated_finalized_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Adjudicated Finalized Date'
			note: '2100D DTP01'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].claims[].claimStatus.informationClaimStatuses[].adjudicatedFinalizedDate'

	claim_remittance_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Remittance Date'
			note: '2100D DTP01'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].claims[].claimStatus.informationClaimStatuses[].remittanceDate'

	claim_remittance_trace_number:
		view:
			columns: 4
			label: 'Remittance Trace Number'
			note: '2100D TRN01'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].claims[].claimStatus.informationClaimStatuses[].remittanceTraceNumber'

	payer_contact_information:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_py_cont'
		view:
			label: 'Payer Contact Information'
			readonly: true
			_meta:
				path: 'transactions[].payers[].payerContactInformation'

	clearinghouse_trace_number:
		view:
			columns: 4
			label: 'Clearinghouse Trace Number'
			note: 'ST02'
			readonly: true
			_meta:
				path: 'referencedTransactionTraceNumber'

	pharmacy_prescription_number:
		view:
			columns: 4
			label: 'Pharmacy Prescription Number'
			note: 'Loop 2200[D|E] REF02 where REF01=XZ'
			readonly: true
			_meta:
				path: 'pharmacyPrescriptionNumber'

	voucher_identifier:
		view:
			columns: 4
			label: 'Voucher Identifier'
			note: 'Loop 2200[D|E] REF02 where REF01=VV'
			readonly: true
			_meta:
				path: 'voucherIdentifier'

	claim_status_information_effective_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Status Information Effective Date'
			note: 'Loop 2200[D|E] STC02'
			readonly: true
			_meta:
				path: 'informationClaimStatuses[].statusInformationEffectiveDate'

	claim_total_claim_charge_amount:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: -4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Claim Charge Amount'
			note: 'Loop 2200[D|E] STC04'
			readonly: true
			_meta:
				path: 'informationClaimStatuses[].totalClaimChargeAmount'

	claim_payment_amount:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Payment Amount'
			note: 'Loop 2200[D|E] STC05'
			_meta:
				path: 'informationClaimStatuses[].claimPaymentAmount'

	adjudicated_finalized_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Adjudicated Finalized Date'
			note: 'Loop 2200[D|E] STC06'
			readonly: true
			_meta:
				path: 'informationClaimStatuses[].adjudicatedFinalizedDate'

	remittance_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Remittance Date'
			note: 'Loop 2200[D|E] STC08'
			readonly: true
			_meta:
				path: 'informationClaimStatuses[].remittanceDate'

	claim_remittance_trace_number:
		view:
			columns: 4
			label: 'Remittance Trace Number'
			note: 'Loop 2200[D|E] STC09'
			readonly: true
			_meta:
				path: 'informationClaimStatuses[].remittanceTraceNumber'

	health_care_claim_status_category_code:
		view:
			columns: -4
			label: 'Health Care Claim Status Category Code'
			note: 'STC01-1, STC010-1, STC11-1'
			readonly: true
			_meta:
				path: 'informationClaimStatuses[].informationStatuses[].healthCareClaimStatusCategoryCode'

	health_care_claim_status_category_code_value:
		view:
			columns: 2
			label: 'Health Care Claim Status Category Code Value'
			note: 'STC01-1, STC010-1, STC11-1'
			readonly: true
			_meta:
				path: 'informationClaimStatuses[].informationStatuses[].healthCareClaimStatusCategoryCodeValue'

	status_code:
		view:
			columns: -4
			label: 'Status Code'
			note: 'STC01-2, STC010-2, STC11-2'
			readonly: true
			_meta:
				path: 'informationClaimStatuses[].informationStatuses[].statusCode'

	status_code_value:
		view:
			columns: 2
			label: 'Status Code Value'
			note: 'STC01-2, STC010-2, STC11-2'
			readonly: true
			_meta:
				path: 'informationClaimStatuses[].informationStatuses[].statusCodeValue'

	entity_identifier_code:
		view:
			columns: -4
			label: 'Entity Identifier Code'
			note: 'STC01-3, STC010-3, STC11-3'
			readonly: true
			_meta:
				path: 'informationClaimStatuses[].informationStatuses[].entityIdentifierCode'

	entity_identifier_code_value:
		view:
			columns: 4
			label: 'Entity Identifier Code Value'
			note: 'STC01-3, STC010-3, STC11-3'
			readonly: true
			_meta:
				path: 'informationClaimStatuses[].informationStatuses[].entityIdentifierCodeValue'

	ncpdp_reject_payment_codes:
		view:
			columns: 4
			label: 'NCPDP Reject Payment Codes'
			note: 'STC01-4, STC010-4, STC11-4'
			readonly: true
			_meta:
				path: 'informationClaimStatuses[].informationStatuses[].nationalCouncilForPrescriptionDrugProgramsRejectPaymentCodes'

	claim_status:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_277_st_hst'
		view:
			label: 'Claim Status'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].claims[].claimStatus'

	service_lines:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_277_sl'
		view:
			label: 'Service Lines'
			readonly: true
			_meta:
				path: 'transactions[].payers[].claimStatusTransactions[].claimStatusDetails[].patientClaimStatusDetails[].claims[].serviceLines'

	response_id:
		model:
			type: 'int'
			required: true
		view:
			label: 'Response ID'
			readonly: true
			offscreen: true

	response_raw_json:
		model:
			type: 'json'
		view:
			label: 'Raw Response Json'
			class: 'json-viewer'
			readonly: true

	raw_report_url:
		view:
			label: 'Raw Report URL'
			readonly: true
			offscreen: true

	s3_filehash:
		view:
			label: 'S3 File Hash'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: 'Claim Status Response (277)'
	indexes:
		unique: [
			['response_id']
		]
		many: [
			['response_id']
			['claim_no']
		]
	sections_group: [
		'Claim Status Response (277)':
			hide_header: true
			sections: [
				'Summary':
					hide_header: true
					tab: 'Summary'
					fields: ['service_begin_date', 'service_end_date', 'service_date','status_code', 'status_code_value']
				'Payer':
					tab: 'Summary'
					fields: ['payer_organization_name', 'payer_identification',
					'payer_centers_for_medicare_and_medicaid_service_plan_id']
				'Payer Contact Information':
					tab: 'Summary'
					fields: ['payer_contact_information']
				'Service Provider':
					tab: 'Summary'
					fields: ['service_provider_organization_name', 'service_provider_npi']
				'Subscriber':
					tab: 'Summary'
					fields: ['subscriber_last_name', 'subscriber_first_name', 'subscriber_middle_name',
					'subscriber_suffix', 'patient_account_number', 'subscriber_member_id', 'dependent_last_name', 'dependent_first_name',
					'dependent_middle_name', 'dependent_suffix']
				'Batch Status':
					tab: 'Summary'
					fields: ['service_provider_status_information_effective_date',
					'service_provider_health_care_claim_status_category_code', 'service_provider_health_care_claim_status_category_code_value',
					'service_provider_status_code', 'service_provider_status_code_value', 'service_provider_entity_identifier_code',
					'service_provider_entity_identifier_code_value']
				'Claim Amounts':
					tab: 'Summary'
					fields: ['claim_total_claim_charge_amount', 'claim_payment_amount', 'claim_adjudicated_finalized_date',
					'claim_remittance_date', 'claim_remittance_trace_number']
				'Claim Status':
					tab: 'Summary'
					fields: ['claim_status_information_effective_date', 'claim_total_claim_charge_amount', 'claim_payment_amount',
					'adjudicated_finalized_date', 'remittance_date', 'claim_remittance_trace_number', 'health_care_claim_status_category_code', 'health_care_claim_status_category_code_value',
					'entity_identifier_code', 'entity_identifier_code_value',
					'ncpdp_reject_payment_codes']
				'Claim Status History':
					tab: 'Summary'
					fields: ['claim_status']
				'Service Lines':
					tab: 'Summary'
					fields: ['service_lines']
				'Reviewed':
					hide_header: true
					tab: 'Summary'
					fields: ['reviewed', 'reviewed_by', 'notes']
				'Meta Data Claim':
					hide_header: true
					indent: false
					tab: 'Meta Data'
					fields: ['claim_no', 'patient_id', 'site_id', 'control_number', 
					'claim_transaction_batch_number', 'trading_partner_claim_number',
					'bill_type_identifier', 'reference_identification', 'transaction_set_creation_date',
					'transaction_set_creation_time']
				'Raw Response':
					hide_header: true
					indent: false
					tab: 'Meta Data'
					fields: ['response_raw_json']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '85%'
	hide_cardmenu: true
	comment: 'Claim Status Response (277)'
	find:
		basic: ['patient_id', 'payer_id', 'site_id', 'service_date']
	grid:
		fields: ['patient_id', 'payer_id', 'status_code_value', 'service_date', 'claim_payment_amount']
		label: ['Patient', 'Payer', 'Status', 'Service Date', 'Paid']
		width: [20, 20, 20,15, 15]
		sort: ['-created_on']
	label: 'Claim Status Response (277)'
	open: 'read'