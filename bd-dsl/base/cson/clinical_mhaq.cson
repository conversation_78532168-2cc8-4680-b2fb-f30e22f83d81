fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_mhaq.assessment_date']
		view:
			note: 'Recommended 2x per year'
			label: 'Last Assessment Date'
			readonly: true

	# Questionnaire
	mhaq_dress:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control: 'radio'
			label: 'Dress yourself, including tying shoelaces and doing buttons?'
			validate: [
					name: 'MHAQScoreValidate'
			]

	mhaq_bed:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control: 'radio'
			label: 'Get in and out of bed?'
			validate: [
					name: 'MHAQScoreValidate'
			]

	mhaq_glass:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control: 'radio'
			label: 'Lift a full cup or glass to your mouth?'
			validate: [
					name: '<PERSON>HAQScoreValidate'
			]

	mhaq_walk:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control: 'radio'
			label: 'Walk outdoors on flat ground?'
			validate: [
					name: 'MHAQScoreValidate'
			]

	mhaq_wash:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control: 'radio'
			label: 'Wash and dry your entire body?'
			validate: [
					name: 'MHAQScoreValidate'
			]

	mhaq_bend:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control: 'radio'
			label: 'Bend down to pick up clothing from the floor?'
			validate: [
					name: 'MHAQScoreValidate'
			]

	mhaq_faucet:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control: 'radio'
			label: 'Turn regular faucets on and off?'
			validate: [
					name: 'MHAQScoreValidate'
			]

	mhaq_vehicle:
		model:
			source:
				easy: 'Without any difficulty'
				some: 'With some difficulty'
				moderate: 'With much difficulty'
				impossible: 'Unable to do'
		view:
			control: 'radio'
			label: 'Get in and out of a bus, car, train, or airplane?'
			validate: [
					name: 'MHAQScoreValidate'
			]

	score:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'Values <0.3 are considered normal'
			label: 'Assessment Score'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_mhaq:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'MHAQ':
			fields: ['last_assessment_date', 'assessment_date']
			prefill: 'clinical_mhaq'
		'Questionnaire':
			note: 'Ask the patient the answer that best describes their usual abilities OVER THE COURSE OF THE LAST WEEK'
			fields: ['mhaq_dress', 'mhaq_bed', 'mhaq_glass', 'mhaq_walk', 'mhaq_wash', 'mhaq_bend', 'mhaq_faucet', 'mhaq_vehicle', 'score']
			prefill: 'clinical_mhaq'
view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Modified Health Assessment Questionnaire'
	grid:
		fields: ['created_on', 'created_by']
		sort: ['-id']
	label: 'Modified Health Assessment Questionnaire (mHAQ)'
