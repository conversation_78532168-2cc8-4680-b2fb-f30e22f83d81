fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	trough_value:
		model:
			type: 'int'
		view:
			note: 'If available'
			label: 'Aminoglycoside trough (mcg/ml)'

	trough_date:
		model:
			type: 'date'
		view:
			note: 'If available'
			label: 'Aminoglycoside trough date'

	peak_value:
		model:
			type: 'int'
		view:
			note: 'If available'
			label: 'Aminoglycoside peak (mcg/ml)'

	peak_date:
		model:
			type: 'date'
		view:
			note: 'If available'
			label: 'Aminoglycoside peak date'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		ongoing_aminoglycoside_4:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Aminoglycoside Aminoglycoside 4th Fill':
			fields: ['trough_value', 'trough_date', 'peak_value', 'peak_date']

view:
	comment: 'Patient > Careplan > Ongoing > Aminoglycoside 4th Fill'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Ongoing Assessment: Aminoglycoside 4th Fill'
	open: 'read'
