fields:
	site_id:
		model:
			prefill: ['parent.site_id', 'patient.site_id']
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site Id'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/site/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'is_specialty': ['default_is_specialty']
			]

	add_prescription_flow:
		model:
			save: false
			source: ['Yes']
			if:
				'Yes':
					prefill:
						order_complete: 'Yes'
						status_id: '1'
					readonly:
						fields: ['order_complete', 'status_id']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Add Prescription Flow'
			offscreen: true
			readonly: true

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			offscreen: true
			readonly: true

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	external_id:
		model:
			type: 'int'
			required: false
			if:
				'*':
					fields: ['imported_dose']
				'!':
					fields: ['dose', 'dose_unit_id', 'frequency_id']
		view:
			label: 'External ID'
			readonly: true
			offscreen: true

	rx_no:
		model:
			type: 'text'
			if:
				'*':
					readonly:
						fields: ['inventory_id', 'written_date', 'expiration_date', 'start_date', 'stop_date', 'dose', 'dose_unit_id', 'frequency_id', 'route_id', 'rx_template_id', 'is_specialty',
						'bsa', 'lesions', 'cm2_per_lesion', 'm2_173', 'grams_of_carbohydrate', 'therapy_days', 'imported_dose', 'patient_param_required', 'requires_infusion_time', 'requires_therapy_days',
						'prescription_provided_in', 'dose_range_1', 'dose_range_2', 'allowed_variance', 'frequency_type', 'frequency_weekly', 'allowed_variance', 'frequency_id', 'frequency_multiplier', 'frequency_label',
						'next_fill_number', 'written_date', 'start_date', 'stop_date', 'expiration_date', 'infuse_length', 'infuse_for', 'infuse_time', 'order_complete']
		view:
			label: 'Rx #'
			readonly: true
			offscreen: true

	condensed_view:
		model:
			save: false
			source: ['Yes']
			if:
				'*':
					readonly:
						fields: ['dose', 'dose_unit_id', 'inventory_id', 'route_id', 'rx_template_id', 'is_specialty', 'payer_ids']
				'!':
					sections: ['Billing Alerts/Notes']
					fields: ['intake_substatus_id', 'written_date', 'expiration_date', 'start_date', 'stop_date', 'payer_order_block']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Condensed View'
			offscreen: true
			readonly: true

	is_erx:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['ss_description', 'physician_order_id', 'ss_daw', 'ss_quantity', 'ss_quantity_qualifier_id', 'ss_compound_dosage_form_id']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Is Surescripts Order?'
			offscreen: true
			readonly: true

	ss_description:
		view:
			columns: 2
			note: 'From original SureScripts Message'
			label: 'Drug Description'
			readonly: true

	ss_day_supply:
		model:
			type: 'int'
		view:
			label: 'Day Supply'
			readonly: true
			columns: 4

	ss_daw:
		model:
			source:
				'0': 'Substitution Allowed'
				'1': 'Dispense as Written'
			if:
				'*':
					fields: ['ss_daw_code_reason']
		view:
			columns: 4
			control: 'radio'
			label: 'Dispense as Written'
			readonly: true

	ss_daw_code_reason:
		model:
			type: 'text'
		view:
			columns: 4
			label: 'DAW Code Reason'
			readonly: true

	ss_quantity:
		model:
			type: 'decimal'
		view:
			columns: 4
			label: 'Quantity'
			readonly: true

	ss_quantity_qualifier_id:
		model:
			source: 'list_ss_quantity_qualifier'
			sourceid: 'code'
			if:
				'CF':
					sections: ['Compound Components']
					fields: ['embed_ss_compound', 'ss_compound_dosage_form_id']
		view:
			columns: 3
			label: 'Quantity Qualifier'
			readonly: true

	ss_compound_dosage_form_id:
		model:
			source: 'list_ncpdp_strength_form'
			sourceid: 'code'
		view:
			label: 'Compound Final Dosage Form'
			readonly: true

	embed_ss_compound:
		model:
			multi: true
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				parent_id:
					'dynamic': '{ss_message_id}'
				parent_form:
					'static': 'ss_message'
		view:
			embed:
				form: 'ss_compound'
				selectable: false
			grid:
				fields: ['description', 'strength', 'strength_uom_id', 'quantity', 'quantity_uom_id']
				width: [40, 15, 15, 15, 15]
				rank: 'local'
			label: 'Compound Components'
			columns: 1
			readonly: true

	physician_order_id:
		model:
			if:
				'*':
					readonly:
						fields: ['start_date', 'expiration_date', 'refills']
					fields: ['ss_message_id', 'sig', 'ss_description']
		view:
			columns: 4
			label: 'Prescriber Order #'
			note: 'Surescripts Physician Order #'
			readonly: true

	ss_message_id:
		model:
			required: false
			type: 'int'
		view:
			label: 'SS Message ID'
			readonly: true
			offscreen: true

	refills:
		model:
			required: false
			type: 'int'
		view:
			columns: -2
			label: '# Refills'

	inventory_id:
		model:
			required: true
			source: 'inventory'
			query: 'select_inv_in_stock'
			querytemplate: 'inventoryTemplate'
			sourcefilter:
				type:
					'static': ['Drug', 'Compound']
				active:
					'static': 'Yes'
		view:
			form_link_enabled: true
			columns: 4
			label: 'Drug'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'dose_unit_id': ['default_dosing_unit_id'],
					'route_id': ['route_id'],
					'type_id': ['type_id'],
					'therapy_id': ['therapy_id'],
					'rx_template_id': ['rx_template_id'],
					'hcpc_code': ['hcpc_code'],
					'formatted_ndc': ['formatted_ndc'],
					'manufacturer_id': ['manufacturer_id']
			]
			validate: [
				{
					name: 'CheckPayerPARequirements'
				},
				{
					name: 'SetDosingUnitFilter'
				}
			]

	formatted_ndc:
		view:
			label: 'NDC'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'inventory_id'
					source_fields: ['manufacturer_id', 'formatted_ndc', 'hcpc_code']
					separator: ' '
				}
			]

	hcpc_code:
		view:
			label: 'HCPC'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'inventory_id'
					source_fields: ['manufacturer_id', 'formatted_ndc', 'hcpc_code']
					separator: ' '
				}
			]

	manufacturer_id:
		model:
			required: false
			source: 'list_manufacturer'
			sourceid: 'code'
		view:
			label: 'Manufacturer'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'inventory_id'
					source_fields: ['manufacturer_id', 'formatted_ndc', 'hcpc_code']
					separator: ' '
				}
			]

	sig:
		view:
			label: 'Sig'
			readonly: true

	dunit_filter_id:
		model:
			required: true
			multi: true
			source: 'list_unit'
			sourceid: 'code'
		view:
			control: 'select'
			label: 'Available Dosing Units'
			readonly: true
			offscreen: true

	dose_range_1:
		model:
			type: 'decimal'
			rounding: 0.001
			required: true
			max: 9999999.999 
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Dose Range Start'
			transform: [
				{
					name: "CalculateAllowedVariance"
				},
				{
					name: 'CalculateTargetDose'
				}
			]
			validate: [
				{
					name: "CompareValidator"
					fields: [
						"dose_range_1"
						"dose_range_2"
					]
					require: "lte"
					error: "Dose Range Start must be less than Dose Range End"
				}
			]

	dose_range_2:
		model:
			type: 'decimal'
			rounding: 0.001
			required: true
			max: 9999999.999 
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Dose Range End'
			transform: [
				{
					name: "CalculateAllowedVariance"
				},
				{
					name: 'CalculateTargetDose'
				}
			]
			validate: [
				{
					name: "CompareValidator"
					fields: [
						"dose_range_1"
						"dose_range_2"
					]
					require: "lte"
					error: "Dose Range Start must be less than Dose Range End"
				}
			]

	prescription_provided_in:
		model:
			required: true
			source: ['Range', 'Variance']
			if:
				'Range':
					fields: ['dose_range_1', 'dose_range_2', 'allowed_variance']
					readonly:
						fields: ['dose', 'allowed_variance']
				'Variance':
					fields: ['dose', 'allowed_variance']
		view:
			columns: 4
			control: 'radio'
			label: 'Prescription Format'

	imported_dose:
		view:
			label: 'CPR Dose'
			readonly: true
			columns: 4

	dose:
		model:
			type: 'decimal'
			rounding: 0.001
			required: false
			max: 9999999.999 
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Dose'

	dose_unit_id:
		model:
			required: false
			source: 'list_unit'
			sourceid: 'code'
			sourcefilter:
				code:
					'dynamic': '{dunit_filter_id}'
		view:
			columns: 4
			label: 'Dosing Unit'
			class: 'select_prefill'
			transform: [{
				name: 'SelectPrefill'
				url: '/form/list_unit/?limit=1&fields=list&sort=name&page_number=0&filter=code:'
				fields:
					'patient_param_required': ['patient_param_required']
					'requires_infusion_time': ['requires_infusion_time']
					'requires_therapy_days': ['requires_therapy_days']
			}
			]

	requires_infusion_time:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['infuse_length', 'infuse_time', 'infuse_for']
					require_fields: ['infuse_length', 'infuse_time', 'infuse_for']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Requires Infusion Time?'
			readonly: true
			offscreen: true

	requires_therapy_days:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['therapy_days']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Requires Therapy Days?'
			readonly: true
			offscreen: true

	patient_param_required:
		model:
			source: ['Weight (Kg)', 'BSA (m²)', 'Lesions', 'cm² per Lesion', '1.73 m²', 'grams of carbohydrate']
			if:
				'Weight (Kg)':
					fields: ['weight']
				'BSA (m²)':
					fields: ['bsa']
				'Lesions':
					fields: ['lesions']
				'cm² per Lesion':
					fields: ['cm2_per_lesion']
				'1.73 m²':
					fields: ['m2_173']
				'grams of carbohydrate':
					fields: ['grams_of_carbohydrate']
		view:
			control: 'radio'
			label: 'Patient Parameter Required'
			readonly: true
			offscreen: true

	weight:
		model:
			max: 1000
			min: 1
			rounding: 0.01
			type: 'decimal'
			prefill: ['patient_measurement_log']
			required: true
		view:
			columns: 4
			class: 'unit'
			label: 'Confirm Weight (Kg)'
			note: 'E.g.: 78, 78kg, 172lbs'
			transform: [
					name: 'WeightTransform'
			]

	bsa:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'BSA m²'
			readonly: true
			columns: 4

	lesions:
		model:
			required: true
			type: 'int'
		view:
			columns: 4
			label: '# Lesions'

	therapy_days:
		model:
			required: true
			type: 'int'
		view:
			columns: 4
			label: '# Therapy Days'

	cm2_per_lesion:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'cm² per Lesion'
			columns: 4

	m2_173:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: '1.73 m²'
			columns: 4

	grams_of_carbohydrate:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'grams of carbohydrate'
			columns: 4

	route_id:
		model:
			required: false
			source: 'list_route'
			sourceid: 'code'
			if:
				'IV':
					fields: ['infuse_length']
		view:
			columns: -4
			label: 'Route'
			validate: [
				{
					name: 'SetHasIVOnParent'
				}
			]

	frequency_id:
		model:
			required: false
			source: 'list_frequency'
			sourceid: 'code'
		view:
			columns: 4
			label: 'Frequency'
			class: 'select_prefill'
			transform: [
				{
					name: 'SelectPrefill'
					url: '/form/list_frequency/?limit=1&fields=list&sort=name&page_number=0&filter=code:'
					fields:
						'frequency_label': ['label_string'],
						'frequency_multiplier': ['multiplier']
				}
			]

	frequency_multiplier:
		model:
			type: 'decimal'
			rounding: 1
		view:
			label: 'Multiplier'
			note: 'times per week'
			offscreen: true
			readonly: true

	frequency_label:
		view:
			label: 'Frequency'
			offscreen: true
			readonly: true

	next_fill_number:
		model:
			min: 1
			default: 1
			type: 'int'
		view:
			label: 'Next Fill #'
			offscreen: true
			readonly: true

	infuse_length:
		model:
			multi: false
			source:
				continuously: 'Continuous'
				intermittently: 'Intermittent'
			if:
				'continuously':
					fields: ['infuse_for', 'infuse_time']
		view:
			columns: 4
			control: 'checkbox'
			label: 'Continuous/Intermittent'

	infuse_for:
		model:
			min: 0
			type: 'decimal'
			rounding: 0.01
			required: false
			if:
				'*':
					require_fields: ['infuse_time', 'infuse_length']
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Infuse for:'

	infuse_time:
		model:
			source: 
				hours: 'Hrs'
				minutes: 'Min'
			if:
				'*':
					require_fields: ['infuse_for', 'infuse_length']
		view:
			columns: 4
			control: 'radio'
			label: 'Min/Hrs'

	written_date:
		model:
			required: false
			type: 'date'
		view:
			template: '{{now}}'
			label: 'Written Date'
			columns: 4
			offscreen: true
			readonly: true
			transform: [
				{
					name: 'CalculateExpirationDate'
				}
			]

	expiration_date:
		model:
			required: false
			type: 'date'
		view:
			columns: 4
			label: 'Expiration Date'
			offscreen: true
			readonly: true
			transform: [
				{
					name: 'PreventLongExpiration'
				}
			]

	start_date:
		model:
			type: 'date'
		view:
			columns: 4
			note: 'This can be an estimated start date.'
			label: 'Start Date'
			transform: [
				{
					name: 'SetExpirationDate'
				}
			]

	stop_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Stop Date'

	therapy_id:
		model:
			required: false
			max: 64
			source: 'list_therapy'
			sourceid: 'code'
		view:
			columns: 4
			label: 'Therapy'

	status_id:
		model:
			required: true
			default: '1'
			source: 'list_order_status'
			sourceid: 'code'
			track: true
			if:
				'4':
					fields: ['onhold_reason']
				'2':
					fields: ['discontinued_date']
				'3':
					fields: ['nogo_date']
					require_fields: ['intake_substatus_id']
		view:
			columns: -4
			label: 'Status'
			findfilter: '!H'
			validate: [
				{
					name: 'PrefillCurrentDate'
					condition:
						status_id: '3'
					dest: 'nogo_date'
				},
				{
					name: 'PrefillCurrentDate'
					condition:
						status_id: '2'
					dest: 'discontinued_date'
				},
				{
					name: 'BlockRXOnDiscontinued'
				}
			]

	onhold_reason:
		model:
			required: true
		view:
			label: 'On-hold Reason'
			columns: 2

	discontinued_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 4
			label: 'Discontinued Date'

	nogo_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 4
			label: 'Nogo Date'

	last_event_id:
		model:
			source: 'list_wf_event'
			sourceid: 'code'
		view:
			label: 'Last Event'
			readonly: true
			offscreen: true

	last_event_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Last Event Datetime'
			readonly: true
			offscreen: true

	last_event_by:
		model:
			source: 'user'
		view:
			label: 'Last Event By'
			readonly: true
			offscreen: true

	intake_substatus_id:
		model:
			required: false # marked as required by tab completed check
			source: 'list_intake_substatus'
			sourceid: 'code'
			default: 'A'
			track: true
		view:
			label: 'Intake Substatus'
			columns: 4

	billing_method:
		model:
			default: 'Insurance'
			required: false
			source: ['Insurance', 'Self Pay', 'Do Not Bill']
			if:
				'Insurance':
					fields: ['drug_pa_id']
				'Do Not Bill':
					fields: ['dnb_reason_id']
		view:
			columns: -4
			control: 'radio'
			label: 'Billing Method'
			validate: [
				{
					name: 'CheckPayerPARequirements'
				}
			]
			transform: [
				{
					name: 'LoadDefaultPayerAndDx'
				}
			]

	dnb_reason_id:
		model:
			required: true
			source: 'list_dnb_reason'
			sourceid: 'code'
		view:
			columns: 4
			control: 'radio'
			label: 'Do Not Bill Reason'

	insurance_id:
		model:
			required: false
			source: 'patient_insurance'
		view:
			label: 'Primary Payer'
			readonly: true
			offscreen: true

	drug_pa_id:
		model:
			required: false
			source: 'patient_prior_auth'
			sourcefilter:
				insurance_id:
					'dynamic': '{insurance_id}'
				patient_id:
					'dynamic': '{patient_id}'
				status_id:
					'static': ['5', '1', '2', '3', '4', '8']
				pa_type:
					'static': 'Drug'
		view:
			add_preset:
				insurance_id: '{insurance_id}'
				patient_id: '{patient_id}'
				status_id: '1'
				pa_type: ['Drug']
			form_link_enabled: true
			columns: 4
			label: 'Authorization'

	payer_ids:
		model:
			multi: true
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			embed:
				form: 'patient_insurance'
				selectable: true
			grid:
				fields: ['type_id', 'payer_id', 'effective_date', 'bill_for_denial']
				width: [20, 40, 20, 20]
				rank: 'local'
			label: 'Payers'
			columns: 2
			validate: [
				{
					name: 'CheckPayerPARequirements'
				}
			]

	auth_flag:
		model:
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Requires Auth?'
			columns: 4

	bv_flag:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Requires BV?'
			columns: 4

	has_ncpdp_payer:
		model:
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Has NCPDP Payer as Primary?'
			offscreen: true
			readonly: true

	is_specialty:
		model:
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Specialty?'
			columns: 4

	dx_ids:
		model:
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			embed:
				form: 'patient_diagnosis'
				selectable: true
			grid:
				edit: true
				fields: ['dx_id']
				label: ['Diagnosis']
				rank: 'local'
			max_count: 12
			columns: 2
			label: 'Diagnosis'

	rx_template_id:
		model:
			source: 'list_rx_template'
			sourceid: 'code'
			sourcefilter:
				active: 
					'static': 'Yes'
				template_type:
					source: ['PO', 'IV', 'Injection', 'Factor', 'Compound', 'TPN']
			required: true
		view:
			columns: 4
			label: 'RX Format'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_rx_template/?limit=1&fields=list&sort=name&page_number=0&filter=code:'
				fields:
					'template_type': ['template_type']
			]

	template_type:
		model:
			required: false
			source: ['PO', 'IV', 'Injection', 'Factor', 'Compound', 'TPN']
			if:
				'Factor':
					prefill:
						frequency_id: 'UAD'
					readonly:
						fields: ['frequency_id']
					fields: ['prescription_provided_in', 'frequency_type']
		view:
			label: 'Template Type'
			control: 'radio'
			transform: [
				{
					name: 'SetDoseLabel'
				}
			]
			readonly: true
			offscreen: true

	bill_notes:
		view:
			columns: 2
			note: 'Appears in the pre-invoice review of the work ticket'
			label: 'Billing Notes'

	# Factor Dosing
	frequency_type:
		model:
			required: true
			source: ['PRN', 'Daily', 'Weekly', 'Other']
			if:
				'Weekly':
					fields: ['frequency_weekly']
		view:
			columns: 2
			control: 'radio'
			label: 'Factor Frequency'

	frequency_weekly:
		model:
			required: true
			multi: true
			source:
				sunday: "Sunday"
				monday: "Monday"
				tuesday: "Tuesday"
				wednesday: "Wednesday"
				thursday: "Thursday"
				friday: "Friday"
				saturday: "Saturday"
		view:
			control: 'checkbox'
			label: 'Week Day Frequency'
			columns: 2

	allowed_variance:
		model:
			required: true
			type: 'decimal'
			min: 1
			max: 10
			rounding: 1
		view:
			columns: 4
			label: 'Variance %'
			note: 'Max 10%'
			class: 'numeral'
			format: 'percent'

	comments:
		view:
			columns: 2
			label: 'Comments'

	type_id:
		model:
			required: true
			default: 'Primary'
			source: 'list_dispense_type'
			sourceid: 'code'
		view:
			label: 'Type'

	is_refill:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['refill_rx_id']
		view:
			columns: 4
			control: 'checkbox'
			label: 'Refill?'
			class: 'checkbox-only'

	refill_rx_id:
		model:
			required: true
			source: 'careplan_order_rx'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				inventory_id:
					'dynamic': '{inventory_id}'
				status:
					'static': '!Denied'
		view:
			form_link_enabled: true
			columns: 4
			label: 'Original Prescription'

	stat_order:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			label: 'Stat Order'
			class: 'checkbox-only'

	payer_order_block:
		model:
			source: ['Yes']
			if:
				'Yes':
					prefill:
						order_complete: ''
						completed_by: ''
						completed_datetime: ''
					readonly:
						fields: ['order_complete']
		view:
			control: 'checkbox'
			label: 'Payer Order Block'
			readonly: true
			offscreen: true

	order_complete:
		model:
			source: ['Yes']
			if:
				'Yes':
					require_fields: ['billing_method','therapy_id', 'route_id', 'rx_template_id']
				'!':
					prefill:
						completed_by: ''
						completed_datetime: ''
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Order Complete?'
			columns: 4
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						order_complete: 'Yes'
					dest: 'completed_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						order_complete: 'Yes'
					dest: 'completed_by'
				}
			]

	completed_by:
		model:
			source: 'user'
		view:
			label: 'Complete By'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'order_complete'
					source_fields: ['completed_by', 'completed_datetime']
					separator: ' @ '
				}
			]

	completed_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Complete Date/Time'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'order_complete'
					source_fields: ['completed_by', 'completed_datetime']
					separator: ' @ '
				}
			]

	summary:
		view:
			label: 'Summary'
			offscreen: true
			readonly: true
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: "{summary}"
	prefill:
		patient:
			link:
				id: 'patient_id'
		patient_measurement_log:
			link:
				id: 'patient_id'
			max: 'created_on'
		careplan_orderp_item:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
		careplan_order_item:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
		company:
			filter:
				id: 1
			max: 'created_on'
	indexes:
		many: [
			['site_id']
			['patient_id']
			['careplan_id']
			['therapy_id']
			['frequency_id']
			['inventory_id']
			['type_id']
			['manufacturer_id']
			['hcpc_code']
			['formatted_ndc']
			['rx_template_id']
			['rx_no']
			['status_id']
			['intake_substatus_id']
	]

	sections_group: [

		'Order Info':
			hide_header: true
			indent: false
			fields: ['site_id', 'patient_id', 'external_id', 'condensed_view', 'is_erx', 'ss_description', 'ss_daw', 'ss_daw_code_reason', 'ss_day_supply', 'ss_quantity',
			'ss_quantity_qualifier_id', 'ss_compound_dosage_form_id', 'physician_order_id', 'ss_message_id', 'inventory_id',
			'formatted_ndc', 'hcpc_code', 'manufacturer_id', 'sig', 'dunit_filter_id', 'prescription_provided_in', 'dose_range_1', 'dose_range_2', 'dose', 'dose_unit_id',
			'weight', 'bsa', 'lesions', 'cm2_per_lesion', 'm2_173', 'grams_of_carbohydrate', 'therapy_days', 'refills', 'imported_dose', 'patient_param_required', 'requires_infusion_time', 'requires_therapy_days',
			'route_id', 'infuse_length', 'infuse_for', 'infuse_time', 'frequency_type', 'frequency_weekly', 'allowed_variance', 'frequency_id', 'frequency_multiplier', 'frequency_label',
			'next_fill_number', 'written_date', 'start_date', 'expiration_date', 'stop_date', 'billing_method', 'dnb_reason_id', 'drug_pa_id', 'auth_flag']

		'Compound Components':
			hide_header: true
			indent: false
			fields: ['embed_ss_compound']

		'Diagnosis/Billing':
			hide_header: true
			indent: false
			fields: ['payer_ids', 'dx_ids', 'insurance_id']

		'Billing Alerts/Notes':
			hide_header: true
			indent: false
			fields: ['bill_notes', 'comments','has_ncpdp_payer']

		'Rx Info':
			hide_header: true
			indent: false
			fields: ['status_id', 'intake_substatus_id', 'onhold_reason', 'discontinued_date', 'nogo_date', 'rx_no', 'therapy_id', 'type_id',
			'rx_template_id', 'template_type',  'bv_flag',  'is_specialty', 'is_refill', 'refill_rx_id', 'stat_order',
			'order_complete', 'payer_order_block', 'completed_by', 'completed_datetime', 'add_prescription_flow']

	]

view:
	dimensions:
		width: '90%'
		height: '75%'
	hide_cardmenu: true
	comment: 'Patient > Order'
	grid:
		fields: ['inventory_id', 'status_id', 'intake_substatus_id']
		sort: ['-created_on']
	find:
		basic: ['inventory_id', 'status_id', 'intake_substatus_id']
	label: 'Order'
	open: 'edit'
