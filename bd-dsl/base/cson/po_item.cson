fields:

	template_id:
		model:
			type: 'int'
		view:
			label: 'Associated PO Template'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
			query: 'select_inv_in_stock'
			querytemplate: 'inventoryTemplate'
			sourcefilter:
				type:
					'static': ['Drug', 'Supply', 'Equipment Rental']
				active:
					'static': 'Yes'
		view:
			columns: 2
			label: 'Ordered Item'
			class: 'select_prefill'
			validate: [
				{
					name: 'InventoryPOItemLastCost'
				},
				{
					name: 'SetPurchaseUnitForSupply'
				}
			]
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'lot_tracking': ['lot_tracking'],
					'serial_tracking': ['serial_tracking'],
					'type': ['type'],
					'formatted_ndc': ['formatted_ndc'],
					'upin': ['upin'],
					'upc': ['upc']
			]

	formatted_ndc:
		view:
			label: 'Formatted NDC'
			readonly: true
			columns: 4

	upin:
		view:
			label: 'UPIN'
			readonly: true
			columns: 4

	upc:
		view:
			label: 'UPC'
			columns: 4
			readonly: true

	type:
		model:
			source: ['Drug', 'Supply', 'Equipment Rental', 'Billable']
		view:
			label: 'Type'
			readonly: true
			offscreen: true

	lot_tracking:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Track Lots?'
			readonly: true
			offscreen: true

	serial_tracking:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Track Serial Numbers?'
			readonly: true
			offscreen: true

	qty_pkg:
		model:
			required: true
			prefill: ['po_item']
			type: 'decimal'
			rounding: 1
		view:
			class: 'numeral'
			format: '0,0'
			columns: -4
			label: 'Quantity'
			transform: [
				{
					name: 'MultiplyFields'
					fields: ['qty_pkg', 'ordered']
					destination: 'total_ordered'
				}
			]

	ordered:
		model:
			required: true
			rounding: 1
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0'
			columns: 4
			label: '# Pkg Ordered'
			transform: [
				{
				name:'MultiplyFields'
				fields:['package_cost', 'ordered']
				destination: 'item_cost'
				},
				{
					name: 'MultiplyFields'
					fields: ['qty_pkg', 'ordered']
					destination: 'total_ordered'
				}
			]

	purchase_unit:
		model:
			required: true
			source: ['Box', 'Carton', 'Case', 'Batch', 'Packet', 'Each']
		view:
			control: 'radio'
			label: 'Purchase Unit'
			columns: 4
			offscreen: true
			readonly: true
			transform: [
				{
					name: 'UpdateUnitFieldNote'
					target:['qty_pkg']
				}
			]

	total_ordered:
		model:
			rounding: 1
			type: 'decimal'
		view:
			label: 'Total Ordered'
			columns: 4
			class: 'numeral'
			format: '0,0'
			readonly: true
			transform: [
				{
					name: 'DivideFields'
					fields: ['item_cost', 'total_ordered']
					destination: 'cost_each'
				}
			]

	package_cost:
		model:
			prefill: ['po_item']
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: -4
			label: 'Cost / Pkg'
			class: 'numeral'
			format:'$0,0.00'
			transform: [
				name:'MultiplyFields'
				fields: ['package_cost', 'ordered']
				destination: 'item_cost'
			]

	item_cost:
		model:
			rounding: 0.00001
			type: 'decimal'
		view:
			columns: 4
			readonly: true
			label: 'Item Cost'
			class: 'numeral money'
			format: '$0,0.0000'
			transform: [
				{
					name: 'DivideFields'
					fields: ['item_cost', 'total_ordered']
					destination: 'cost_each'
				}
			]

	cost_each:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Cost / Item'
			class: 'numeral money'
			format: '$0,0.0000'
			transform: [

			]

	comments:
		view:
			columns: 2
			label: 'Comments'
			control: 'area'

model:
	access:
		create:     []
		create_all: ['admin','pharm','csr','cm','physician','nurse']
		delete:     ['admin','pharm','csr','cm','physician','nurse']
		read:       ['admin','pharm','csr','cm','physician','nurse']
		read_all:   ['admin','pharm','csr','cm','physician','nurse']
		request:    []
		review:     ['admin','pharm','csr','cm','physician','nurse']
		update:     []
		update_all: ['admin','pharm','csr','cm','physician','nurse']
		write:      ['admin','pharm','csr','cm','physician','nurse']
	name: '{inventory_id} QTY:{ordered}'

	indexes:
		many: [
			['inventory_id']
		]

	sections:
		'Purchase Item':
			hide_header: true
			indent: false
			fields: ['inventory_id', 'qty_pkg', 'ordered', 'total_ordered', 'purchase_unit', 'package_cost', 'item_cost', 'cost_each',
			'type', 'lot_tracking', 'serial_tracking', 'comments']
view:
	dimensions:
		width: '90%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Purchase Item'
	find:
		basic: ['inventory_id']
	grid:
		fields: ['inventory_id', 'qty_pkg', 'ordered', 'package_cost', 'item_cost']
		sort: ['-id']
	label: 'Purchase Item'
	open: 'edit'
