fields:

	code:
		model:
			required: true
		view:
			columns: 2
			label: 'Assessment Code'
			transform: [
					name: 'LowerCase'
			]

	name:
		model:
			required: true
		view:
			columns: 2
			label: 'Assessment Name'

	description:
		view:
			columns: 2
			label: 'Description'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 2

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['workflow']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['code']
		]
	name: [ 'name', 'code']
	sections:
		'Disease Assessment':
			fields: ['code', 'name', 'description', 'allow_sync', 'active']

view:
	comment: 'Manage > Disease Assessment'
	find:
		basic: ['code']
	grid:
		fields: ['code', 'name', 'description', 'allow_sync', 'active']
		sort: ['code']
	label: 'Disease Assessment'
	open: 'read'
