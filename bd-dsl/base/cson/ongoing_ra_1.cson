fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# RA Questionnaire
	med_comfort:
		model:
			source:
				very: 'Very comfortable'
				moderate: 'Somewhat comfortable'
				little: 'Not comfortable'
				none: 'Afraid of needles'
		view:
			control: 'radio'
			label: 'What is your comfort level with having to inject yourself to administer the medication?'

	med_train:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['med_train_details']
		view:
			control: 'radio'
			label: 'Would you like additional injection training?'

	med_train_details:
		model:
			required: true
		view:
			label: 'Details'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		ongoing_ra_1:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Rheumatoid Arthritis 1st Refill':
			fields: ['med_comfort', 'med_train', 'med_train_details']

view:
	comment: 'Patient > Careplan > Ongoing > Rheumatoid Arthritis 1st Refill'
	grid:
		fields: ['med_comfort', 'med_train']
	label: 'Ongoing Assessment: Rheumatoid Arthritis 1st Refill'
	open: 'read'
