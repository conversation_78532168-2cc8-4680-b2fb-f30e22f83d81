fields:
	patient_id:
		model:
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	patient_insurance_id:
		model:
			source: 'patient_insurance'
			type: 'int'
		view:
			columns: 4
			label: 'Subscriber Insurance'
			readonly: true

	control_number:
		model:
			type: 'text'
		view:
			label: 'Control Number'
			readonly: true
			offscreen: true

	#pi.cardholder_id
	subscriber_member_id:
		model:
			type: 'text'
			required: true
		view:
			columns: 4
			label: 'Subscriber Member ID'

	#patient.firstname
	subscriber_first_name:
		model:
			type: 'text'
			required: true
		view:
			columns: 4
			label: 'Subscriber First Name'

	#patient.lastname
	subscriber_last_name:
		model:
			type: 'text'
			required: true
		view:
			columns: 4
			label: 'Subscriber Last Name'

	subscriber_dob:
		model:
			type: 'date'
			required: true
		view:
			control: 'input'
			columns: 4
			label: 'Subscriber DOB'

	subscriber_gender:
		model:
			max: 8
			min: 1
			required: true
			source: ['Female', 'Male']
		view:
			control: 'radio'
			label: 'Sex at birth'
			columns: 3

	patient_mrn:
		model:
			type: 'text'
		view:
			label: 'Patient MRN'
			columns: 4
			readonly: true

	subscriber_street:
		model:
			max: 128
			min: 4
		view:
			columns: 'addr_1'
			label: 'Street'

	subscriber_street2:
		model:
			max: 128
		view:
			columns: 'addr_2'
			label: 'Street 2'

	subscriber_city:
		model:
			max: 128
			min: 1
		view:
			columns: 'addr_city'
			label: 'City'

	subscriber_state_id:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			label: 'State'
			columns: 'addr_state'

	subscriber_zip:
		model:
			max: 10
			min: 5
		view:
			format: 'us_zip'
			label: 'Zip'
			columns: 'addr_zip'

	organization_name:
		model:
			type: 'text'
		view:
			columns: 4
			label: 'Organization Name'

	last_verification_req_id:
		view:
			label: 'Last Verification Control Number'
			readonly:true
			offscreen:true

	last_verification_sent_date:
		model:
			type: 'date'
		view:
			label: 'Last Verification Sent Date'
			readonly:true
			offscreen:true

	response_json_data:
		model:
			type: 'json'
		view:
			label: 'Response (Json)'
			readonly: true
			offscreen: true

	trading_partner_service_id:
		model:
			type: 'text'
		view:
			columns: 4
			readonly:true
			label: 'Trading Partner Service ID'
	
	submitter_transaction_identifier:
		view:
			label: 'Last Verification Control Number'
			readonly:true
			offscreen:true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'pharm']
	sections_group: [
		'Insurance':
			fields: ['subscriber_member_id','patient_insurance_id','control_number']
		'Subscriber':
			fields: ['patient_mrn','subscriber_first_name','subscriber_last_name','subscriber_dob','subscriber_street',
			'subscriber_street2','subscriber_city', 'subscriber_state_id','subscriber_zip','subscriber_gender']
		'Payer':
			fields: ['trading_partner_service_id','organization_name','submitter_transaction_identifier']
	]
	name: '{submitter_transaction_identifier} : {organization_name}'

view:
	comment: 'Patient > Check Eligibility 270'
	find:
		basic: ['submitter_transaction_identifier']
	grid:
		fields: ['submitter_transaction_identifier']
	label: 'Patient Check Eligibility 270'