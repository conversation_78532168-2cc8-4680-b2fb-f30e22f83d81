fields:
	patient_id:
		model:
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	docusign_status:
		model:
			default: 'Pending'
			source: ['Pending', 'Signed']
		view:
			control: 'radio'
			label: 'Docusign Status'
			readonly: true

	docusign_sent_date:
		model:
			type: 'datetime'
		view:
			label: 'Envelop Successfully Sent on'

#   This is docusign envelop id
	documents_id:
		view:
			label: 'Documents ID'
			readonly: true

	welcome_call_id:
		view:
			label: 'Welcome Call ID'
			readonly: true

	user_email:
		view:
			label: 'Patient Email'
			readonly: true

	docusign_form:
		view:
			label: 'Form Pending Signature'

	error_code:
		view:
			label: 'Undelivered Error Code'
			offscreen: true
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	prefill:
		patient:
			link:
				id: 'patient_id'
	name: ['documents_id', 'docusign_status']
	sections:
		'Docusign Audit':
					fields: ['patient_id', 'user_email', 'documents_id', 'docusign_status']

view:
	comment: 'Docusign Audit'
	find:
		basic: ['patient_id', 'user_email', 'documents_id']
	grid:
		fields: ['created_on', 'patient_id', 'documents_id', 'docusign_status']
		sort: ['-created_on']
	label: 'Docusign Audit'
