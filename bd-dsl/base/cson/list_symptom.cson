fields:

	name:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 2

	single_select:
		model:
			required: true
			default: 'No'
			source: ['No','Yes']
		view:
			label: 'Single Select Only'
			note: 'Will deselect other symptoms when selected'
			columns: 2

	therapy_id:
		model:
			multi: true
			source: 'list_therapy'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Therapies'

	drug_id:
		model:
			multi: true
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Drugs'

	dx_id:
		model:
			multi: true
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Diagnosis'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 2

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ['lists']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['name']
		]
	name: ['name']
	sections:
		'Symptom':
			fields: ['name', 'single_select']
		'Associations':
			fields: ['therapy_id', 'drug_id', 'dx_id', 'allow_sync', 'active']

view:
	comment: 'Symptom'
	find:
		basic: ['name', 'therapy_id', 'drug_id']
	grid:
		fields: ['name', 'therapy_id', 'drug_id']
	label: 'Symptom'
	open: 'read'
