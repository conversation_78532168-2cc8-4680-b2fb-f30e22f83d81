fields:
	user_id:
		model:
			source: 'user'
		view:
			label: 'User Id'

	user_display:
		model:
			type: 'text'
		view:
			note: 'This is for things like wfd, ebridged, etc.. that do not have a user account'

	form:
		model:
			type: 'text'
		view:
			note: 'This is the name of the form that changed'

	method:
		model:
			source: ['create', 'read', 'update', 'delete', 'datetime']
		view:
			note: 'The original method used to change data.'

	modification_on:
		model:
			type: 'datetime'
		view:
			note: 'datetime the change occured, usually the form\'s updated_on. Or the stamp from datetime events'

	dashboard_id:
		model:
			source: 'dashboard'
		view:
			note: 'dashboard that was affected'

	dashboard_status:
		model:
			type: 'text'
		view:
			note: 'Status as a result from the change'

	previous:
		model:
			type: 'json'
		view:
			note: 'Data of the form as it was *before*'

	current:
		model:
			type: 'json'
		view:
			note: 'Diff of the previous, e.g. fields of the main form that changed.'

model:
	name: ['dashboard_status', 'dashboard_id', 'user_id']
	indexes:
		many: [
			['user_id']
			['dashboard_id']
		]
view:
	comment: 'Dashboard Audit'
	label: '~ Dashboard Audit'
