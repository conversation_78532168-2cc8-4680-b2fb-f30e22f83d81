fields:

	date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Date Contacted'
			template: '{{now}}'

	description:
		view:
			control: 'area'
			label: 'Description'

	task:
		view:
			label: 'Follow-up Tasks'

	reminder_date:
		model:
			type: 'date'
		view:
			label: 'Reminder Date'

model:
	access:
		create:     []
		create_all: ['admin', 'liaison']
		delete:     ['admin', 'liaison']
		read:       ['admin', 'liaison']
		read_all:   ['admin', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'liaison']
		write:      ['admin', 'liaison']
	name: ['date', 'task']
	sections:
		'Contact Details':
			fields: ['date', 'description', 'task', 'reminder_date']

view:
	comment: 'Sales > Sales Activity Log'
	find:
		basic: ['date', 'description', 'task', 'reminder_date']
	grid:
		fields:  ['date', 'description', 'task', 'reminder_date']
		sort: ['date']
	label: 'Sales Activity Log'
	open: 'read'