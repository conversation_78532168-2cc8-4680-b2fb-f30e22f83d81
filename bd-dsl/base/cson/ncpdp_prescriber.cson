fields:
	transaction_code:
		model:
			prefill: ['parent.transaction_code']
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '103-A3'
			if:
				'B1': # Drug Billing
					fields: ['dr_phone', 'dr_street_address', 'dr_city', 'dr_state', 'dr_zip']
				'B3': # Drug Reverse and <PERSON>
					fields: ['dr_phone', 'dr_street_address', 'dr_city', 'dr_state', 'dr_zip']
				'S1': # Service Billing
					fields: ['dr_phone', 'dr_street_address', 'dr_city', 'dr_state', 'dr_zip']
				'S3': # Service Reverse and Bill
					fields: ['dr_phone', 'dr_street_address', 'dr_city', 'dr_state', 'dr_zip']
		view:
			label: 'Transmission Code'
			offscreen: true
			readonly: true

	physician_id:
		model:
			source: 'physician'
		view:
			label: 'Physician'
			readonly: true
			offscreen: true

	primary_physician_id:
		model:
			source: 'physician'
		view:
			label: 'Primary Physician'
			readonly: true
			offscreen: true

	dr_id_qualifier:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '466-EZ'
			if:
				'*':
					require_fields: ['dr_id']
		view:
			columns: 2
			reference: '466-EZ'
			note: '466-EZ'
			label: 'Prescriber ID Qualifier'

	dr_id:
		model:
			max: 15
			if:
				'*':
					require_fields: ['dr_id_qualifier']
		view:
			columns: 2
			reference: '411-DB'
			note: '411-DB'
			label: 'Prescriber ID'

	dr_last_name:
		model:
			max: 15
		view:
			columns: -4
			reference: '427-DR'
			note: '427-DR'
			label: 'Last Name'

	dr_first_name:
		model:
			max: 15
		view:
			columns: 4
			reference: '364-2J'
			note: '364-2J'
			label: 'First Name'

	pri_dr_id_qualifier:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '468-2E'
			if:
				'*':
					require_fields: ['pri_dr_id']
		view:
			columns: 2
			reference: '468-2E'
			note: '468-2E'
			label: 'PCP ID Qualifier'

	pri_dr_id:
		model:
			max: 15
			if:
				'*':
					require_fields: ['pri_dr_id_qualifier']
		view:
			columns: 4
			reference: '421-DL'
			note: '421-DL'
			label: 'PCP ID'

	pri_dr_last_name:
		model:
			max: 15
		view:
			columns: 4
			reference: '470-4E'
			note: '470-4E'
			label: 'PCP Last Name'

	dr_street_address:
		model:
			max: 30
		view:
			reference: '365-2K'
			note: '365-2K'
			label: 'Address'
			class: "api_prefill"
			columns: 'addr_1'
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'dr_street_address': ['addressLabel']
					'dr_city': ['city']
					'dr_state': ['stateCode']
					'dr_zip': ['postalCode']
			]

	dr_city:
		model:
			max: 20
		view:
			columns: 'addr_city'
			reference: '366-2M'
			note: '366-2M'
			label: 'City'

	dr_state:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			reference: '367-2N'
			note: '367-2N'
			label: 'State'

	dr_zip:
		model:
			max: 10
			min: 5
		view:
			columns: 'addr_zip'
			reference: '368-2P'
			note: '368-2P'
			format: 'us_zip'
			label: 'Zip'

	dr_phone:
		model:
			max: 21
		view:
			columns: 4
			reference: '498-PM'
			note: '498-PM'
			format: 'us_phone'
			label: 'Phone #'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['dr_id', 'dr_id_qualifier', 'transaction_code', 'dr_last_name', 'dr_first_name']
	sections:
		'Prescriber':
			hide_header: true
			indent: false
			fields: ['transaction_code', 'physician_id',
			'dr_id_qualifier', 'dr_id', 'dr_last_name', 'dr_first_name', 'dr_phone',
			'dr_street_address', 'dr_city', 'dr_state', 'dr_zip']

		'Primary Care Provider':
			indent: false
			hide_header: true
			fields: ['primary_physician_id', 'pri_dr_id_qualifier', 'pri_dr_id'
			'pri_dr_last_name']

view:
	comment: 'Prescriber'
	grid:
		fields: ['dr_id_qualifier', 'dr_id', 'dr_last_name', 'dr_first_name']
		width: [25, 25, 25, 25]
		sort: ['-created_on']
	label: 'Prescriber'
	open: 'read'