fields:
	name:
		model:
			max: 32
			min: 2
			required: true
		view:
			label: 'Name'
	regex:
		model:
			max: 32
			min: 2
			required: true
		view:
			label: 'Regex'
	help_text:
		model:
			max: 128
			min: 2
			required: true
		view:
			label: 'Help Text'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['setup']
	indexes:
		unique: [
			['name']
			['regex']
		]
	name: '{name} - {regex} - {help_text}'
	reportable: false
	sections:
		'Default':
			fields: ['name', 'regex', 'help_text']

view:
	comment: 'Manage > Password Policy'
	find:
		basic: ['name', 'regex', 'help_text']
	grid:
		fields: ['name', 'regex', 'help_text']
		sort: ['name']
	label: 'Password Policy'
