fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['encounter_radai.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	# Survey Participation
	pt_radai_particiates:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'Yes'
			if:
				'Yes':
					sections: ['Questionnaire', 'Daily Pain Assessment']
					fields: ['assessment_date']
			prefill: ['encounter_radai']
		view:
			findfilter: 'Yes'
			control: 'radio'
			label: 'Will a Rheumatoid Arthritis Disease Activity Index (RADAI) assessment be completed today?'

	active:
		model:
			required: true

			max: 64
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			note: 'On a scale from 0 to 10 with 0 being completely inactive and 10 being extremely active.'
			label: "In general, how active has the patient's arthritis been over the past 6 months?"

	tenderness:
		model:
			required: true

			max: 64
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			note: 'On a scale from 0 to 10 with 0 being completely inactive and 10 being extremely active.'
			label: "In terms of joint tenderness and swelling, how active is the patient's arthritis today?"

	pain:
		model:
			required: true

			max: 64
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			note: 'On a scale from 0 to 10 with 0 being no pain and 10 being unbearable pain.'
			label: "How much arthritis pain does the patient feel today?"

	joint_stiffness:
		model:
			required: true

			max: 64
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['joint_stiffness_length']
		view:
			control: 'radio'
			label: "Were the patient's joints stiff when he/she woke up today?"

	joint_stiffness_length:
		model:
			required: true

			max: 64
			source: ['Less than 30 minutes', '30 minutes to 1 hour', '1 to 2 hours', '2 to 4 hours', 'More than 4 hours', 'All day']
		view:
			control: 'radio'
			label: "How long did the stiffness last?"

	pain_shoulders:
		model:
			required: true

			max: 64
			source: ['None', 'Mild', 'Moderate', 'Severe']
		view:
			control: 'radio'
			label: "Shoulders"

	pain_elbows:
		model:
			required: true

			max: 64
			source: ['None', 'Mild', 'Moderate', 'Severe']
		view:
			control: 'radio'
			label: "Elbows"

	pain_wrists:
		model:
			required: true

			max: 64
			source: ['None', 'Mild', 'Moderate', 'Severe']
		view:
			control: 'radio'
			label: "Wrists"

	pain_fingers:
		model:
			required: true

			max: 64
			source: ['None', 'Mild', 'Moderate', 'Severe']
		view:
			control: 'radio'
			label: "Fingers"

	pain_hips:
		model:
			required: true

			max: 64
			source: ['None', 'Mild', 'Moderate', 'Severe']
		view:
			control: 'radio'
			label: "Hips"

	pain_knees:
		model:
			required: true

			max: 64
			source: ['None', 'Mild', 'Moderate', 'Severe']
		view:
			control: 'radio'
			label: "Knees"

	pain_ankles:
		model:
			required: true

			max: 64
			source: ['None', 'Mild', 'Moderate', 'Severe']
		view:
			control: 'radio'
			label: "Ankles"

	pain_toes:
		model:
			required: true

			max: 64
			source: ['None', 'Mild', 'Moderate', 'Severe']
		view:
			control: 'radio'
			label: "Toes"

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		encounter_radai:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Rheumatoid Arthritis Disease Activity Index (RADAI) Participation':
			fields: ['last_assessment_date', 'pt_radai_particiates', 'assessment_date']
		'Questionnaire':
			fields: ['active', 'tenderness', 'pain', 'joint_stiffness', 'joint_stiffness_length']
			prefill: 'encounter_radai'
		'Daily Pain Assessment':
			note: 'Indicate the amount of pain (none, mild, moderate, or severe) the patient is having today in each of the joint areas listed:'
			fields: ['pain_shoulders', 'pain_elbows', 'pain_wrists', 'pain_fingers', 'pain_hips', 'pain_knees', 'pain_ankles', 'pain_toes']
			prefill: 'encounter_radai'
view:
	hide_cardmenu: true
	comment: 'Patient > Careplan >  Rheumatoid Arthritis Disease Activity Index (RADAI)'
	find:
		basic: ['pt_radai_particiates']
	grid:
		fields: ['created_on', 'assessment_date', 'created_by']
		sort: ['-id']
	label: 'Clinical: Rheumatoid Arthritis Disease Activity Index (RADAI)'
