fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	contact_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Contact Date'

	# Wound Progress Note
	healing:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Optimal Wound/Incision Healing?'

	decub_heal:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Decubitis Healing?'

	decub_heal_details:
		view:
			label: "Decubitis Details"

	treat_respond:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Wound/Incision/Decubitis responding to treatment?'

	regimen_change:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['regimen_change_details']
		view:
			control: 'radio'
			label: 'Treatment Regimen Changed?'

	regimen_change_details:
		model:
			max: 8192
		view:
			control: 'area'
			label: "Change Details"

	comment:
		model:
			max: 8192
		view:
			control: 'area'
			label: "Wound Status Comment"

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['careplan_id', 'order_id']
	sections:
		'Contact Date':
			fields: ['contact_date']
		'Wound Status':
			fields: ['healing', 'decub_heal', 'decub_heal_details',
		 	'treat_respond', 'regimen_change', 'regimen_change_details',]
		'Comment':
			fields: ['comment']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Checklist > Wound > Note'
	grid:
		fields: ['contact_date', 'healing', 'regimen_change', 'regimen_change_details']
	label: 'Wound Weekly Progress Note'
