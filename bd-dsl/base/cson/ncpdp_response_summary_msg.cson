fields:
	response_uuid:
		model:
			type: 'text'
		view:
			label: 'Response UUID'
			readonly: true
			offscreen: true

	add_msg:
		view:
			note: '526-FQ'
			label: 'Additional Message'
			readonly: true

	add_msg_qualifier:
		model:
			multi: false
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '132-UH'
		view:
			note: '132-UH'
			label: 'Addtl Msg Qualifier'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['response_uuid']
		]
	name: 'Response Additional Message'
	sections:
		'Additional Message':
			hide_header: true
			indent: false
			fields: ['response_uuid', 'add_msg', 'add_msg_qualifier']

view:
	dimensions:
		width: '80%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Additional Message'
	grid:
		fields: ['add_msg', 'add_msg_qualifier']
		width: [50, 50]
		sort: ['-created_on']
	label: 'Additional Message'
	open: 'read'