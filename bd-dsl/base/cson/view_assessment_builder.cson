fields:
	# Links
	patient_id:
		model:
			required: false
			source: 'patient'
			type: 'int'
		view:
			readonly: true
			label: 'Patient Id'

	careplan_id:
		model:
			required: false
			source: 'careplan'
			type: 'int'
		view:
			readonly: true
			label: 'Careplan Id'

	site_id:
		model:
			required: false
			prefill: ['patient.site_id']
			source: 'site'
			type: 'int'
		view:
			readonly: true
			label: 'Site'

	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'*':
					fields: ['therapy_2']
		view:
			label: 'Primary Therapy'
	
	therapy_2:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'*':
					fields: ['therapy_3']
		view:
			label: 'Secondary Therapy'

	therapy_3:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'*':
					fields: ['therapy_4']
		view:
			label: 'Tertiary Therapy'

	therapy_4:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'*':
					fields: ['therapy_5']
		view:
			label: 'Quaternary Therapy'

	therapy_5:
		model:
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Quinary Therapy'

	
	brand_1:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['brand_2']
		view:
			label: 'Primary Brand'

	brand_2:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['brand_3']
		view:

			label: 'Secondary Brand'

	brand_3:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['brand_4']
		view:
			label: 'Tertiary Brand'

	brand_4:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['brand_5']
		view:
			label: 'Quaternary Brand'

	brand_5:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['disease_2']
		view:
			label: 'Quinary Brand'

	disease_1:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['disease_2']
		view:
			label: 'Primary Disease'

	disease_2:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['disease_3']
		view:
			label: 'Secondary Disease'

	disease_3:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['disease_4']
		view:
			label: 'Tertiary Disease'

	disease_4:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['disease_5']
		view:
			label: 'Quaternary Disease'

	disease_5:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['disease_5']
		view:
			label: 'Quinary Disease'
	
	clinical_1:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['clinical_2']
		view:
			label: 'Primary Clinical Assessment'

	clinical_2:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['clinical_3']
		view:
			label: 'Secondary Clinical Assessment'

	clinical_3:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['clinical_4']
		view:
			label: 'Tertiary Clinical Assessment'

	clinical_4:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['clinical_5']
		view:
			label: 'Quaternary Clinical Assessment'

	clinical_5:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
		view:
			label: 'Quinary Clinical Assessment'




model:
	save: false
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	access:
		create:     ['admin', 'pharm', 'csr', 'cm', 'nurse']
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm', 'csr', 'nurse']
		read:       ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm', 'liaison', 'crn', 'patient','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm', 'liaison','physician']
		request:    []
		update:     ['admin', 'pharm', 'csr', 'cm', 'nurse','liaison', 'crn','physician']
		update_all: ['admin', 'pharm','physician']
		write:      ['admin', 'pharm', 'csr', 'cm', 'nurse','liaison', 'crn','physician']
	bundle: []
	name: ['patient_id', 'careplan_id', 'site_id']
	sections_group: [
		'Basic Info':
			fields: ['patient_id', 'careplan_id', 'site_id']
		'Therapies':
			fields: ['therapy_1', 'therapy_2', 'therapy_3', 'therapy_4', 'therapy_5']
			note: "Don't duplicate therapies"
		'Brands':
			fields: ['brand_1', 'brand_2', 'brand_3', 'brand_4', 'brand_5']
			note: "Don't duplicate brands"
		'Diseases':
			fields: ['disease_1', 'disease_2', 'disease_3', 'disease_4', 'disease_5']
			note: "Don't duplicate diseases"
		'Clinical Assessments':
			fields: ['clinical_1', 'clinical_2', 'clinical_3', 'clinical_4', 'clinical_5']
			note: "Don't duplicate clinical assessments"
	]

view:
	comment: 'Patient > Assessment Builder'
	grid:
		fields: ['created_by', 'therapy_1', 'therapy_2', 'therapy_3', 'therapy_4', 'therapy_5']
		sort: ['-id']
	label: 'Assessment Builder'
	open: 'edit'