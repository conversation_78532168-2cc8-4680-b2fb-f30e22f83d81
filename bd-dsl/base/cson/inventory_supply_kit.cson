fields:

	external_id:
		view:
			label: 'External ID'
			readonly: true
			offscreen: true

	site_id:
		model:
			multi: true
			required: true
			source: 'site'
		view:
			columns: 3
			label: 'Site(s)'

	name:
		model:
			required: true
			search: 'A'
		view:
			columns: 3
			label: 'Name'
			findunique: true 

	billable_id:
		model:
			required: false
			source: 'inventory'
			sourcefilter:
				type:
					'static': ['Billable']
		view:
			columns: 3
			label: 'Supply Kit Billable'

	subform_item:
		model:
			required: true
			multi: true
			type: 'subform'
			source: 'inventory_sk_item'
		view:
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['inventory_id', 'dispense_quantity', 'one_time_only']
				label: ['Item', 'Quantity', '1x Only']
				width: [45, 15, 10]
			label: 'Supply Item'

	comments:
		view:
			control: 'area'
			label: 'Comments'

model:
	access:
		create:     []
		create_all: ['admin','pharm']
		delete:     ['admin','pharm']
		read:       ['admin','pharm']
		read_all:   ['admin','pharm']
		request:    []
		review:     ['admin','pharm']
		update:     []
		update_all: ['admin','pharm']
		write:      ['admin','pharm']
	bundle: ['templates']
	name: '{site_id_auto_name} {name}'

	sections_group: [
		'Supply Kit Template':
			sections: [
				'Details':
					fields: ['site_id', 'name']
				'Billing':
					fields: ['billable_id']
				'Supplies':
					fields: ['subform_item']
				'Comments':
					fields: ['comments']
			]
	]

view:
	hide_cardmenu: true
	comment: 'Supply Kit Template'
	find:
		basic: ['site_id', 'name', 'billable_id']
	grid:
		fields: ['name', 'site_id', 'billable_id', 'comments']
		sort: ['-id']
	label: 'Supply Kit Template'
	open: 'edit'