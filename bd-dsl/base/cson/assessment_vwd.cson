fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	diagnosis_vwd:
		model:
			required: true
			source: ['Type 1', 'Type 2A', 'Type 2B', 'Type 2M', 'Type 2N', 'Type 3']
		view:
			control: 'radio'
			label: 'Von Willebrand Disease'

model:
	sections:
		'Von Willebrand Disease Pre-Assessment':
			area: 'preassessment'
			fields: ['diagnosis_vwd']
	name: ['patient_id', 'careplan_id']
view:
	comment: 'Patient > Careplan > Assessment > Von Willebrand Disease'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Von Willebrand Disease'
	open: 'read'
