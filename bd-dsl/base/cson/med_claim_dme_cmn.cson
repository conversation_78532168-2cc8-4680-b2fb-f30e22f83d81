fields:

	attachment_transmission_code:
		model:
			required: false
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'PWK02-DME-Code'
		view:
			columns: 2
			label: 'Transmission Code'
			reference: 'PWK02'
			_meta:
				location: '2400 PWK'
				field: '02'
				code: 'CT'
				path: 'claimInformation.serviceLines[{idx1-50}].durableMedicalEquipmentCertificateOfMedicalNecessity.attachmentTransmissionCode'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['attachment_transmission_code']
	sections:
		'DME CMN':
			hide_header: true
			fields: ['attachment_transmission_code']

view:
	dimensions:
		width: '85%'
		height: '45%'
	hide_cardmenu: true
	reference: '2400'
	comment: 'DME CMN'
	grid:
		fields: ['attachment_transmission_code']
		sort: ['-created_on']
	label: 'DME CMN'
	open: 'read'