fields:

	clearinghouse:
		model:
			required: true
			max: 128
			source: ['Change Healthcare']
		view:
			control: 'radio'
			label: 'Clearinghouse'
			findunique: true
			columns: 2

	payer:
		model:
			required: true
			max: 128
		view:
			label: 'Payer'
			findunique: true
			columns: 2

	code:
		model:
			required: true
		view:
			label: 'Certification ID'
			columns: 2

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 2
			control: 'radio'
			label: 'Can Sync Record'

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 2
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['setup']
	sync_mode: 'mixed'
	indexes:
		many: [
			['code']
		]
		unique: [
			['clearinghouse', 'payer', 'code']
		]
	name: '{clearinghouse} - {payer} - {code}'
	sections:
		'Main':
			fields: ['clearinghouse', 'payer', 'code', 'allow_sync', 'active']

view:
	comment: 'Manage > Software Certification ID'
	find:
		basic: ['clearinghouse', 'payer', 'code']
	grid:
		fields: ['clearinghouse', 'payer', 'code', 'allow_sync', 'active']
		sort: ['clearinghouse']
	label: 'Software Certification ID'
	open: 'read'
