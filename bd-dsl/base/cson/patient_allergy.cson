fields:

	life_threatening:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes', 'Unknown']
		view:
			control: 'radio'
			label: 'Is the allergy life threatening?'
			columns: 4

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			offscreen: true
			readonly: true

	allergen_id:
		model:
			required: true
			source: 'list_fdb_alrgn_mstr'
			sourceid: 'code'
			if:
				'!':
					fields: ['allergen_not_found']
		view:
			columns: 4
			label: 'Allergen Name'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_fdb_alrgn_mstr/?limit=1&filter=code:'
				fields:
					'allergen_name': ['dam_concept_id_desc']
			]

	allergen_name:
		view:
			label: 'Allergen Name'
			offscreen: true
			readonly: true
			findunique: true

	allergen_not_found:
		model:
			required: false
			source: ['Yes']
			if:
				'Yes':
					fields: ['unmapped_allergen']
				'!':
					require_fields: ['allergen_id']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Allergen Not Found In System?'
			note: 'It is important to map the allergen if available for DUR checks'
			columns: 4

	unmapped_allergen:
		view:
			label: 'Unmapped Allergen'
			note: 'This is the allergen that was not found in the FDB database'
			columns: 2
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'allergen_name'
					overwrite: true
				}
			]

	reaction_id:
		model:
			source: 'list_reaction'
			multi: true
		view:
			label: 'Reaction(s)'
			columns: 2

	note:
		view:
			label: 'Additional Notes'
			control: 'area'

	severity_id:
		model:
			required: false
			source: 'list_snomed_severity'
			sourceid: 'code'
		view:
			columns: 4
			label: 'Severity'

	active:
		model:
			required: true
			default: 'Yes'
			source: ['Yes']
			if:
				'!':
					fields: ['end_date']
		view:
			columns: 4
			label: 'Active?'
			findfilter: 'Yes'

	start_date:
		model:
			type: 'date'
		view:
			columns: 4
			note: 'Approximate date that allergy was first noted'
			label: 'Start Date'

	end_date:
		model:
			type: 'date'
		view:
			columns: 4
			note: 'Approximate date that allergy subsided'
			label: 'End Date'

	external_id:
		view:
			label: 'External Id'
			readonly: true
			offscreen: true

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['patient']
		review:     ['admin', 'pharm']
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
			['allergen_id', 'start_date', 'end_date']
			['allergen_id']
			['allergen_name']
		]
		unique: [
			['patient_id', 'allergen_id', 'start_date']
		]
	sections:
		'Allergen':
			fields: ['allergen_id', 'allergen_name', 'allergen_not_found', 'unmapped_allergen', 'severity_id',
			'reaction_id', 'active', 'start_date', 'end_date', 'note']
	name: '{allergen_name}'

view:
	dimensions:
		width: '75%'
		height: '50%'
	hide_cardmenu: true
	validate: [
		{
			name: "DateOrderValidator"
			fields: [
				"start_date",
				"end_date"
			]
			error: "End Date cannot be lesser than Start Date"
		}
	]
	comment: 'Patient > Allergies'
	find:
		basic: ['allergen_name', 'active']
	grid:
		fields: ['created_on', 'allergen_name', 'reaction_id', 'severity_id', 'start_date', 'end_date', 'reviewed_by', 'reviewed_on']
		sort: ['-start_date', '-end_date', 'allergen_name']
	label: 'Allergies'
	open: 'read'
