fields:
	field:
		view:
			columns: 4
			label: 'Field'
			readonly: true

	value:
		view:
			columns: 4
			label: 'Value'
			readonly: true

	code:
		view:
			columns: 4
			label: 'Error Code'
			readonly: true

	location:
		view:
			columns: 4
			label: 'Location'
			readonly: true

	description:
		view:
			control: 'area'
			label: 'Error Description'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['field', 'value', 'code']
	sections:
		'Claim Response Error':
			hide_header: true
			fields: ['field', 'value', 'code', 'location', 'description']

view:
	dimensions:
		width: '55%'
		height: '55%'
	hide_cardmenu: true
	comment: 'Claim Response Error'
	grid:
		fields: ['field', 'value', 'code', 'location', 'description']
		width: [15, 15, 15, 15, 40]
		sort: ['-created_on']
	label: 'Claim Response Error'
	open: 'read'