#RMCRRD1_MEDICARE_REG_REF_DESC
fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#MCR_REF
	mcr_ref:
		view:
			label: 'HCPC'
			findunique: true
			columns: 2

	#MCR_REFSN
	mcr_refsn:
		model:
			type: 'int'
		view:
			label: 'Medicare Reference Code Sequence Number'
			readonly: true
			columns: 2

	#MCR_BC
	mcr_bc:
		view:
			label: 'Medicare Billing Code'
			readonly: true
			findunique: true
			columns: 2

	#MCR_BCDESC
	mcr_bcdesc:
		view:
			label: 'Medicare Billing Code Description'
			readonly: true
			findunique: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['mcr_bc', 'mcr_bcdesc']
	indexes:
		many: [
			['code']
			['mcr_ref']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['mcr_ref', 'mcr_refsn', 'mcr_bc', 'mcr_bcdesc']

view:
	comment: 'Manage > List FDB Medicare Region Reference Description'
	find:
		basic: ['mcr_ref']
	grid:
		fields: ['mcr_ref', 'mcr_refsn', 'mcr_bc', 'mcr_bcdesc']
	label: 'List FDB Medicare Region Reference Description'
