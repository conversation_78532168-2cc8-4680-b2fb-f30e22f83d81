fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	pmp_benefits:
		model:
			required: true
			multi: true
			source: ["Goal: Keep patient compliant to appropriate adherence level and ensure patient’s understanding of therapy -PDC score >90% for Specialty Disease States including, but not limited to Oncology, RA, Hep C, IVIG, and Multiple Sclerosis -PDC score >80% for all other non-specialty disease states",
			'Goal: Minimize Patient Adverse Reactions, Site Infections, Hospitalizations and ER visits',
			'Goal: Minimize Patient issues in administration and side effects',
			'Goal: Maintain monthly follow-ups if patient has multiple comorbidities, multiple specialty medications, or classifies as a “high-risk” patient per P&P']
		view:
			columns: 2
			label: 'The benefits of getting enrolled in the Patient Management Program'
			control: 'checkbox'

	pmp_benefits_optout:
		model:
			max: 12
			source: ['Opt-Out', 'Opt-In']
			required: true
			default: 'Opt-In'
		view:
			columns: 2
			control: 'radio'
			label: 'Pat<PERSON> understands the benefits and wish to Opt-In/Opt-Out of Patient Management Program?'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['patient-form']
	name: ['patient_id']
	indexes:
		many: [
			['patient_id']
		]
	sections:
		'Patient Management Program Enrollment Log':
			fields: ['pmp_benefits', 'pmp_benefits_optout']

view:
	comment: 'Patient > Patient Management Program Enrollment Log'
	grid:
		fields: ['created_by', 'created_on', 'pmp_benefits', 'pmp_benefits_optout']
		sort: ['-id']
	label: 'Patient Management Program Enrollment Log'
	open: 'read'
