fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	allergic_reactions:
		model:
			multi:true
			source: ['Hives', 'Trouble breathing or swallowing', 'Itching', 
			'Swelling of the lips, tongue, face', 'Dizziness', 'Wheezing', 'Fainting', 'Asthma attack']
			if:
				'*':
					note: 'Direct patient to tell their healthcare provider right away or go to the nearest emergency room'
		view:
			control: 'checkbox'
			label: 'Have you had any of the following allergic side effects since your last administration?'

	common_reactions:
		model:
			multi:true
			source: ['Bruising', 'Trouble walking', 'Headache', 
			'Rash', 'Eczema', 'Respiratory failure, respiratory disorder, hypoxia', "Tinea infection (Ringworm, athlete's foot and jock itch)"]
		view:
			control: 'checkbox'
			label: 'Have you had any of the following side effects since your last administration?'

	other_reactions:
		model:
			multi: true
			source: 'list_reaction'
		view:
			label: 'Any other reactions?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		ongoing_radicava:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Radicava Followup':
			note: 'Ask the patient the following questions'
			fields: ['allergic_reactions', 'common_reactions', 'other_reactions']

view:
	comment: 'Patient > Careplan > Ongoing > Radicava'
	grid:
		fields: ['allergic_reactions', 'common_reactions', 'other_reactions']
	label: 'Ongoing Assessment: Radicava'
	open: 'read'
