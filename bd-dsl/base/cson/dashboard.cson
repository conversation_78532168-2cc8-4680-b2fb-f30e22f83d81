fields:
	description:
		model:
			type: 'text'
		view:
			control: 'area'
			label: 'Description'
			readonly: true

	category:
		model:
			type: 'text'
		view:
			label: 'Category'

	tab:
		model:
			type: 'text'
		view:
			label: 'Tab'

	status:
		model:
			access:
				write: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
			type: 'text'
			source: ['Open', 'Done']
		view:
			control: 'radio'
			findfilter: 'Open'
			label: 'Status'

	assign_to:
		model:
			access:
				write: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
			type: 'int'
			source: 'permission_group'
		view:
			label: 'Assigned To'

	comment:
		model:
			access:
				write: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
			type: 'subform'
			multi: true
			source: 'dashboard_comment'
		view:
			label: 'Comment'

	source_form:
		model:
			type: 'text'
		view:
			label: 'Source'

	board:
		model:
			type: 'text'
		view:
			label: 'Board'

	source_id:
		model:
			type: 'int'
		view:
			label: 'Source Id'

	patient_id:
		model:
			type: 'int'
			source: 'patient'
		view:
			label: 'Patient Id'

	rule:
		model:
			type: 'text'
		view:
			label: 'Rule'
	tag:
		model:
			type: 'text'
		view:
			label: 'Tag'

	filter1:
		model:
			type: 'int'
		view:
			label: 'Filter #1'

	filter2:
		model:
			type: 'int'
		view:
			label: 'Filter #2'

	filter3:
		model:
			type: 'int'
		view:
			label: 'Filter #3'

	filter4:
		model:
			type: 'int'
		view:
			label: 'Filter #4'

	filter5:
		model:
			type: 'text'
		view:
			label: 'Filter #5'

	filter6:
		model:
			type: 'text'
		view:
			label: 'Filter #6'

	filter7:
		model:
			type: 'text'
		view:
			label: 'Filter #7'

	filter8:
		model:
			type: 'text'
		view:
			label: 'Filter #8'

	action:
		model:
			type: 'json'
		view:
			label: 'Action'

	followup_by:
		model:
			source: 'user'
			type: 'int'
		view:
			label: 'Follow-up By'

	followup_on:
		model:
			type: 'date'
		view:
			label: 'Follow-up Date'

	inprocess_by:
		model:
			source: 'user'
			type: 'int'
		view:
			label: 'In-Process By'

	inprocess_on:
		model:
			type: 'date'
		view:
			label: 'In-Process Date'

	# this is data intended to pass for the named workflow rule
	# specified in rule.
	data:
		model:
			type: 'json'
		view:
			label: 'Data'

	# this is the data intended for the client to render into a
	# dashboard card
	display:
		model:
			type: 'json'
		view:
			label: 'Display'

	inactive_hidden:
		model:
			default: 'No'
			source: ['No', 'Yes']
		view:
			label: 'Inactive Hidden'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		update_all: []
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	name: ['board', 'category', 'status']
	indexes:
		many: [
			['tag']
			['board']
			['status']
			['category']
			['category', 'status', 'board']
			['category', 'board']
			['patient_id']
			['assign_to']
			['created_by']
			['created_on']
			['updated_by']
			['updated_on']
			['change_by']
			['archived']
			['deleted']
			['inprocess_by']
		]
	sections:
		'Alert':
			fields: ['description', 'assign_to', 'status']
		'Comments':
			fields: ['comment']
view:
	comment: 'Dashboard'
	find:
		basic: ['status']
		advanced: ['description']
	grid:
		fields: ['created_on', 'assign_to', 'description', 'status']
		sort: ['id']
	label: 'Dashboard Items'
	open: 'edit'
