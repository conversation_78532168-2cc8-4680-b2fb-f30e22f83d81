fields:

	field:
		view:
			columns: 2
			label: 'Field'

	code:
		view:
			columns: 2
			label: 'Code'

	name:
		view:
			columns: 2
			label: 'Name'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		many: [
			['code']
		]
	name: '{code} - {name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['field', 'code', 'name']

view:
	comment: 'Manage > Medical Claim ECL'
	find:
		basic: ['field', 'code', 'name']
	grid:
		fields: ['field', 'code', 'name']
		sort: ['code']
	label: 'Medical Claim ECL'
	open: 'read'
