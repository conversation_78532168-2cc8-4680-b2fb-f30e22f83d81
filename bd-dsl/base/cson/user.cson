fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	last_login:
		model:
			type: 'datetime'
		view:
			label: 'Last Login Date/Time'
			readonly: true
			columns: 4

	last_login_ip:
		model:
			type: 'text'
		view:
			label: 'Last Login IP'
			readonly: true
			columns: 4

	last_comm_notify:
		model:
			type: 'datetime'
		view:
			label: 'Last Notification Date/Time'
			readonly: true
			columns: 4

	last_login_location:
		model:
			type: 'text'
		view:
			label: 'Last Login Location'
			readonly: true
			columns: 4

	job_title:
		model:
			type: 'text'
		view:
			label: 'Job Title'
			columns: 4

	firstname:
		model:
			max: 32
			min: 2
			required: true
		view:
			label: 'First Name'
			note: 'Letters, numbers, comma, \', -, . only'
			validate: [
					name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
			]
			columns: 4

	lastname:
		model:
			max: 32
			min: 2
			required: true
		view:
			label: 'Last Name'
			note: 'Letters, numbers, comma, \', -, . only'
			validate: [
					name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
			]
			columns: 4

	displayname:
		model:
			max: 64
			min: 3
			required: true
		view:
			label: 'Display Name'
			note: 'Letters, numbers, comma, \', -, . only'
			validate: [
					name: 'Name<PERSON><PERSON>da<PERSON>'
			]
			columns: 4

	username:
		model:
			max: 256
			min: 3
			required: true
			transform: [
				name: 'LowerCase'
			]
		view:
			label: 'User Name'
			note: 'Letters & numbers only'
			columns: 4

	password:
		model:
			max: 32
			min: 8
			type: 'password'
			validate: [
					name: 'PasswordStrengthValidator'
					fields: ['password']
			]
		view:
			label: 'Password'
			note: 'Letters & numbers required'
			validate: [
					name: 'PasswordMatchValidator'
					fields: ['password', 'password2']
					error: 'Passwords do not match!'
			]
			columns: -4

	password2:
		model:
			max: 32
			min: 8
			save: false
			type: 'password'
			validate: [
					name: 'PasswordStrengthValidator'
					fields: ['password']
			]
		view:
			label: 'Confirm Password'
			note: 'Letters & numbers required'
			validate: [
					name: 'PasswordMatchValidator'
					fields: ['password', 'password2']
					error: 'Passwords do not match!'
			]
			columns: 4

	password_expiration_date:
		model:
			type: 'date'
		view:
			label: 'Password Expire Date'
			validate: [
				name: 'DateValidator'
				require: 'future'
			]
			columns: 4

	external_id:
		view:
			note: 'ID in external system like CPR+, Salesforce, etc.'
			label: 'External ID'
			columns: 4

	pin:
		model:
			max: 4
			min: 4
			type: 'password'
		view:
			columns: 4
			label: 'Pin'
			note: '4-digit number only'
			validate: [{
					name: 'PasswordMatchValidator'
					fields: ['pin', 'pin2']
					error: 'Pins do not match!' },
					{
						name: 'PinStrengthValidator'
					}
			]

	pin2:
		model:
			max: 4
			min: 4
			save: false
			type: 'password'
		view:
			label: 'Confirm Pin'
			note: '4-digit number only'
			columns: 4
			validate: [{
					name: 'PasswordMatchValidator'
					fields: ['pin', 'pin2']
					error: 'Pins do not match!' },
					{
						name: 'PinStrengthValidator'
					}
			]

	email:
		model:
			max: 64
			min: 6
			required: true
			validate: [
					name: 'EmailUniqueValidator'
					fields: ['email', 'username']
			]
		view:
			label: 'Email Address'
			note: 'Must be a valid email address'
			validate: [
					name: 'EmailValidator'
			]
			columns: 4

	image_url:
		view:
			label: 'User Photo'
			note: 'HTTPS URL link to user photo'
			columns: 2
			validate: [
					name: 'SecureURLValidator'
			]

	role:
		model:
			required: true # if not required true then update can access auth funs accordingly
			source:
				'admin':     'Administrator'
				'patient':   'Patient'
				'payer':     'payer'
				'pharm':     'Pharmacist'
				'physician': 'Physician'
				'csr':       'CSR'
				'nurse':     'Nurse'
				'liaison':   'Liaison'
				'cma':       'Case Management Assistant'
				'cm':        'Case Manager'
				'system':    'System'
				'billing': 'Billing'
		view:
			label: 'User Role'
			columns: 4

	sales_code:
		model:
			type: 'text'
		view:
			label: 'Sales Code'
			columns: 4

	comm_portal:
		model:
			default: 'No'
			required: false
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Can receive communication from any Portal user?'

	issue_ticket:
		model:
			default: 'No'
			required: false
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Able to submit issue tickets?'
			columns: 4
			offscreen: true
			

	rec_sms:
		model:
			default: 'No'
			required: false
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['phone_cell']
		view:
			control: 'radio'
			label: 'Able to receive SMS messages?'
			columns: 4

	timezone_id:
		model:
			required: true
			source: ['Pacific/Honolulu','America/Anchorage','America/Los_Angeles','America/Denver','America/Chicago','America/New_York']
		view:
			columns: 2
			label: 'Timezone'

	phone_cell:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Cell Phone'
			columns: 4

	demo_user:
		model:
			required: false
			source: ['No', 'Yes']
		view:
			control: 'radio'
			note:  'A demo user loads mock data for the reports when logged in'
			label: 'Demo User'
			offscreen: true
			readonly: true

	password_reset:
		model:
			access:
				write: ['cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
			save: false
			source:
				'0': 'No'
				'1': 'Yes'
			default: '0'
		view:
			control: 'radio'
			label: 'Password Reset'
			note: 'Select Yes to force a password reset'
			columns: 4

	change_password_token:
		model:
			type: 'text'
		view:
			label: 'Change Password Token'
			control: 'input'

	twofa_enable:
		model:
			required: false
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['twofa_country_code', 'twofa_external_id']
		view:
			control: 'radio'
			note:  'User must have a VALID email and phone number on file.'
			label: 'Enable 2-Factor Authentication'

	twofa_external_id:
		model:
			required: false
			type: 'text'
		view:
			label: '2-Factor Auth Token'
			readonly: true

	twofa_country_code:
		model:
			required: false
			type: 'decimal'
			rounding: 1
			default: 1
		view:
			label: 'Country Code for 2-Factor Auth'

	authentication_type:
		model:
			required: true
			default: 'password'
			source:
				'password': 'Password'
			if:
				'password':
					fields: ['password', 'password2', 'password_reset', 'password_expiration_date']
		view:
			control: 'radio'
			label: 'Authentication Type'

	external_authentication_id:
		model:
			type: 'text'
			max: 255
		view:
			label: 'Single Sign-On ID'
			note: "E.g. MS Entra (Azure AD) email, Google Workspace email"
			columns: 4

	ext:
		model:
			type: 'text'
		view:
			label: "User's Extension"
			readonly: true
			offscreen: true

	is_admin:
		model:
			default: 'No'
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is Admin'
			columns: 4

	is_test:
		model:
			default: 'Yes'
			source: ['No', 'Yes']
		view:
			label: 'Is Test'
			offscreen: true
			readonly: true

	category:
		model:
			source: ['Internal', 'External']
		view:
			label: 'Category'
			columns: 4

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'user'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					direct_attachment: 'Yes'
					user_id: '{code}'
					assigned_to: 'User'
					source: 'Scanned Document'
					form_code: '{code}'
					form_name: 'document'
					form_filter: 'user'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'


	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     ['cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		update_all: ['admin']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['setup']
	indexes:
		fulltext: ['firstname', 'lastname', 'displayname', 'username']
		many: [
			['username', 'password']
			['firstname', 'lastname', 'displayname']
			['id', 'role']
			['role', 'username']
			['email']
			['role']
			['sales_code']
		]
		unique: [
			['code']
			['username']
			['email']
		]
	name: '{firstname} {lastname} ({job_title})'
	reportable: true
	sections_group: [
		'Name':
			hide_header: true
			indent: false
			fields: ['code', 'firstname', 'lastname', 'displayname', 'job_title', 'image_url', 'timezone_id']
			tab: 'Settings'
		'Contact':
			indent: false
			fields: ['email', 'rec_sms', 'phone_cell']
			tab: 'Settings'
		'Login':
			indent: false
			fields: ['username', 'external_authentication_id', 'sales_code', 'external_id',
			'password', 'password2', 'password_reset', 'password_expiration_date']
			tab: 'Settings'
	'Access':
			indent: false
			fields: ['category','role', 'issue_ticket','is_admin']
			tab: 'Settings'
		'Activity':
			indent: false
			fields: ['last_login', 'last_login_ip', 'last_login_location', 'last_comm_notify']
			tab: 'Settings'
		'Password':
			indent: false
			fields: []
			tab: 'Settings'
		'Time Zone':
			indent: false
			fields: []
			tab: 'Settings'
		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'
	]

	transform: [
			name: 'JobTitleTransform'
			fields: [ 'role' , 'job_title' ]
		# ,
		# 	name: 'GenerateTokenIf'
		# 	arguments:
		# 		qualifier: 'password_reset'
		# 		token_field: 'change_password_token'
		# ,
		# 	name: 'OnUpdateSetFieldIf'
		# 	arguments:
		# 		qualifier: 'password'
		# 		field: 'password_reset'
		# 		value: 0
		# ,
		# 	name: 'OnUpdateSetFieldIf'
		# 	arguments:
		# 		qualifier: 'password'
		# 		field: 'change_password_token'
		# 		value: null
		# ,
		# 	name: 'OnUpdateIf'
		# 	arguments:
		# 		qualifier: 'password'
		# 		fun: 'delete_session'
		# 		field: 'id'
	]
	transform_post: [
		name: 'PermissionGroup'
	]
view:
	validate: [
			name: 'NotMatch'
			fields: ['password', 'username']
			error: 'Password cannot be the same as the username!'
	]
	comment: 'Manage > Users'
	find:
		basic: ['role', 'job_title', 'username', 'lastname', 'firstname', 'category']
		advanced: ['displayname']
	grid:
		fields: ['role', 'username', 'firstname', 'lastname', 'job_title', 'email', 'sales_code', 'category', 'last_login']
		sort: ['role', 'job_title', 'username', 'lastname', 'firstname', 'sales_code']
	icon: 'person'
	label: 'Users'
