fields:

	code:
		model:
			max: 64
			required: true
		view:
			columns: 3
			label: 'Code'
			findunique: true

	name:
		model:
			required: true
			max: 128
		view:
			columns: 3
			label: 'Name'
			findunique: true

	payer_id:
		model:
			multi: true
			source: 'payer'
		view:
			columns: 3
			label: 'Payer Specific?'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 3
			control: 'radio'
			label: 'Can Sync Record'

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 3
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['lists']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['code']
		]
		many:
			[
				['code']
				['payer_id']
			]
	name: '{code} - {name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'payer_id', 'allow_sync', 'active']

view:
	comment: 'Manage > NCPDP Route'
	find:
		basic: ['code','name','payer_id']
	grid:
		fields: ['code','name','payer_id','allow_sync','active']
		sort: ['code']
	label: ' NCPDP Route'
	open: 'read'
