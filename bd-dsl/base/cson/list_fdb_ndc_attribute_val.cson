fields: #RNDCVD0_NDC_ATTRIBUTE_VALU_DSC

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'
	#NDC_ATTRIBUTE_TYPE_CD
	ndc_attribute_type_cd:
		model:
			type:'int'
		view:
			label: 'Attribute Type'
			readonly: true
			columns: 3

	#NDC_ATTRIBUTE_VALUE
	ndc_attribute_value:
		view:
			label: 'Attribute Value'
			readonly: true
			columns: 3

	#NDC_ATTRIBUTE_VALUE_DSC
	ndc_attribute_value_dsc:
		view:
			label: 'Attribute Description'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['ndc_attribute_type_cd', 'ndc_attribute_value', 'ndc_attribute_value_dsc']
	indexes:
		many: [
			['ndc_attribute_type_cd']
			['ndc_attribute_value']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['ndc_attribute_type_cd', 'ndc_attribute_value', 'ndc_attribute_value_dsc']

view:
	comment: 'Manage > List FDB NDC Attribute Values'
	find:
		basic: ['ndc_attribute_type_cd']
	grid:
		fields: ['ndc_attribute_type_cd', 'ndc_attribute_value', 'ndc_attribute_value_dsc']
	label: 'List FDB NDC Attribute Values'
