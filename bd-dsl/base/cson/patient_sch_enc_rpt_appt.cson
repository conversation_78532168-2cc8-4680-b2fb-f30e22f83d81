fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	# Schedule
	nxt_appt_generated:
		model:
			max:3
			min:2
			default: 'No'
			source: ['Yes', 'No']
		view:
			offscreen: true
			label: 'Next Appointment Generated'

	nxt_visit:
		model:
			type: 'date'
		view:
			offscreen: true
			label: 'Next Visit'

	schedule_period:
		model:
			required: true
			source: ['Daily', 'Weekly', 'Monthly']
			if:
				'Daily':
					fields: ['schedule_repeat_day']
				'Weekly':
					fields: ['schedule_repeat_week', 'schedule_repeat_on']
				'Monthly':
					fields: ['schedule_repeat_month', 'schedule_repeat_by']
		view:
			control: 'radio'
			label: 'Repeats'

	schedule_repeat_day:
		model:
			default: 1
			required: true
			type: 'int'
		view:
			label: 'Repeat Every'
			note: 'Days'

	schedule_repeat_week:
		model:
			default: 1
			required: true
			type: 'int'
		view:
			label: 'Repeat Every'
			note: 'Weeks'

	schedule_repeat_month:
		model:
			default: 1
			required: true
			type: 'int'
		view:
			label: 'Repeat Every'
			note: 'Months'

	schedule_repeat_on:
		model:
			multi:true
			required: true
			source: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
		view:
			control: 'checkbox'
			label: 'Repeat On'

	schedule_repeat_by:
		model:
			required: true
			source: ['Day of the Month', 'Day of the Week']
		view:
			control: 'radio'
			label: 'Repeat on the Same'

	nxt_visit_reminder:
		model:
			type: 'date'
		view:
			offscreen: true
			label: 'Next Visit Reminder'

	schedule_end:
		model:
			type: 'date'
		view:
			label: 'End Date'
			validate: [
				name: 'ScheduledEndValidate'
			]

	prv_appointment_referral:
		model:
			type: 'text'
		view:
			offscreen: true
			label: 'Prev Appointment Referral'

	# Status
	active:
		model:
			max: 3
			min: 2
			default: 'Yes'
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Schedule Active?'
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['nxt_visit'],
			['nxt_visit_reminder']
		]
	name: ['patient_id', 'order_id', 'schedule_period']
	sections:
		'Setup Repeat Schedule Appointments':
			fields: [
				'nxt_visit', 'schedule_period',
				'schedule_repeat_day', 'schedule_repeat_week', 'schedule_repeat_month','schedule_repeat_by',
				'schedule_repeat_on', 'schedule_end', 'nxt_visit_reminder', 'prv_appointment_referral',
				'active']


view:
	comment: 'Patient > Schedule > Encounter > Reoccurred > Appointments'
	grid:
		fields: ['nxt_visit', 'schedule_period', 'nxt_visit_reminder', 'active']
		sort: ['-nxt_visit']
	label: 'Reoccurred Appointments: Calender Events'
