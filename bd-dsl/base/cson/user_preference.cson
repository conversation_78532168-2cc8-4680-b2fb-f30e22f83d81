fields:
	user_id:
		model:
			max: 32
			required: true
			source: 'user'
		view:
			label: 'User ID'
			readonly: true

	default_prefill_site_id:
		model:
			required: false
			source: 'site'
		view:
			label: 'Default Site'

	view_mode:
		model:
			max: 32
			type: 'text'
			required: true
			default: 'normal'
			source: ['normal','compact','rob mode']
		view:
			label: 'Screen Layout'
			
	search_shortcut_key:
		model:
			max: 32
			required: true
			type: 'text'
			default: 'f1'
			source:
				'f1':   'F1'
				'f2':   'F2'
				'f3':   'F3'
		view:
			label: 'Keyboard Shortcut For Search Bar'

	printers:
		model:
			type: 'json'
			subfields:
				ip:
					label: 'IP'
					type: 'text'
				device_type:
					label: 'Device Typee'
					type: 'text'
				document_printer:
					label: 'Document Printer'
					type: 'text'
				label_printer:
					label: 'Label Printer'
					type: 'text'
		view:
			control: 'grid'
			label: 'Printer Configuration'

	subform_printers:
		model:
			multi: true
			type: 'subform'
			source: 'preference_printers'
		view:
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				fields: ['site_id', 'document_printer', 'pharmacy_label_printer']
				label: ['Site ID', 'Document Printer', 'Label Printer']
				width: [55, 15, 15]

	grid_configurations:
		model:
			type: 'json'
		view:
			label: 'Grid Configuration'
			offscreen: false

	user_signature:
		model:
			type: 'json'
		view:
			control: 'esign'
			label: 'User Signarure'
			offscreen: false
model:
	access:
		create:     []
		create_all: ['admin', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'nurse', 'pharm']
		write:      ['admin', 'nurse', 'pharm']
	bundle: ['setup']
	indexes:
		unique: [
			['user_id']
		]
	name: ['view_mode','search_shortcut_key']
	sections:
		'Preferences':
			fields: ['view_mode','search_shortcut_key', 'default_prefill_site_id', 'user_signature']
		'Printers':
			fields: ['subform_printers']
view:
	comment: 'Settings > Preferences'
	find:
		basic: ['view_mode','search_shortcut_key']
	grid:
		fields: ['view_mode','search_shortcut_key']
	label: 'Preferences'
	open: 'read'
