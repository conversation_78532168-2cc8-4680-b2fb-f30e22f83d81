fields:
	patient_id:
		model:
			type: 'int'
			required: true
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	dx_id:
		model:
			type: 'int',
			required: true
			source: 'patient_diagnosis'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			columns: 3
			label: 'Diagnosis'
			validate: [
				name: 'LoadPatientDX'
			]

	diagnosis_code:
		model:
			required: true
			min: 1
			max: 30
		view:
			columns: 3
			label: 'Dx Code'
			note: '21A-L'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		delete:     ['admin', 'pharm', 'patient']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
	indexes:
		many: [
			['patient_id']
			['dx_id']
		]
	name:['dx_id', 'diagnosis_code']
	sections:
		'Diagnosis':
			hide_header: true
			fields: ['patient_id', 'dx_id', 'diagnosis_code']

view:
	dimensions:
		width: '75%'
		height: '45%'
	hide_cardmenu: true
	comment: 'Med Claim Diagnosis (1500)'
	grid:
		fields: ['patient_id', 'dx_id', 'diagnosis_code']
		sort: ['-id']
	label: 'Med Claim Diagnosis (1500)'
