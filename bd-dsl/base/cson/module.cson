fields:
	name:
		model:
			required: true
		view:
			label: 'Name'
	code:
		model:
			required: true
		view:
			label: 'Code'

	nav_placement:
		model:
			source: 
				top: 'Top'
				bottom: 'Bottom'
		view:
			label: 'NavBar Placement'

	sort_order:
		model:
			type: 'int'
		view:
			label: 'Sort Order'

	path:
		model:
			type: 'text'
		view:
			label: 'Path'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'


model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     ['admin']
		update_all: ['admin']
		write:      ['admin']
	indexes:
		unique: [
			['name']
		]
	name: ['name']
	bundle: ['system']
	sync_mode: 'mixed'
	sections:
		'Module':
			fields: ['name', 'code', 'path', 'nav_placement', 'sort_order', 'allow_sync', 'active']


view:
	comment: 'Module'
	find:
		basic: ['name', 'code', 'active', 'nav_placement', 'sort_order', 'path']
	grid:
		fields: ['name', 'code', 'active', 'nav_placement', 'sort_order', 'path']
		sort: ['name']
	label: 'Module'
	open: 'read'
