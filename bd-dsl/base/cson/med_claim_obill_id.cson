fields:

	qualifier:
		model:
			required: true
			min: 2
			max: 3
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '2330G-REF01'
		view:
			columns: 2
			label: 'Indentifier Qualifier'
			reference: 'REF01'
			_meta:
				location: '2330G REF'
				field: '01'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherPayerBillingProvider[0].otherPayerBillingProviderIdentifier[{idx1-3}].qualifier'

	identifier:
		model:
			required: true
			min: 1
			max: 50
		view:
			columns: 2
			label: 'Indentifier'
			reference: 'REF02'
			_meta:
				location: '2330G REF'
				field: '02'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherPayerBillingProvider[0].otherPayerBillingProviderIdentifier[{idx1-3}].identifier'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		delete:     ['admin', 'pharm', 'patient']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
	name: ['qualifier', 'identifier']
	sections:
		'Billing Provider Identifier':
			hide_header: true
			fields: ['qualifier', 'identifier']

view:
	dimensions:
		width: '65%'
		height: '45%'
	hide_cardmenu: true
	reference: '2330G'
	comment: 'Billing Provider Identifier'
	grid:
		fields: ['qualifier', 'identifier']
		sort: ['-id']
	label: 'Billing Provider Identifier'
