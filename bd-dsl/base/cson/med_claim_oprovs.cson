fields:

	other_payer_billing_provider:
		model:
			multi: false
			source: 'med_claim_oprov_bill'
			type: 'subform'
		view:
			label: 'Billing Prov'

	other_payer_referring_provider:
		model:
			multi: false
			source: 'med_claim_oprov_ref'
			type: 'subform'
		view:
			label: 'Referring Prov'

	other_payer_rendering_provider:
		model:
			multi: false
			source: 'med_claim_oprov_rend'
			type: 'subform'
		view:
			label: 'Rendering Prov'

	other_payer_supervising_provider:
		model:
			multi: false
			source: 'med_claim_oprov_sup'
			type: 'subform'
		view:
			label: 'Supervising Prov'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['id','created_by','created_on']
	sections_group: [
		'Providers':
			hide_header: true
			sections: [
				'Billing Provider':
					hide_header: true
					indent: false
					tab: 'Billing'
					tab_toggle: true
					fields: ['other_payer_billing_provider']
				'Referring Provider':
					hide_header: true
					indent: false
					tab: 'Referring'
					tab_toggle: true
					fields: ['other_payer_referring_provider']
				'Rendering Provider':
					hide_header: true
					indent: false
					tab: 'Rendering'
					tab_toggle: true
					fields: ['other_payer_rendering_provider']
				'Supervising Provider':
					hide_header: true
					indent: false
					tab: 'Supervising'
					tab_toggle: true
					fields: ['other_payer_supervising_provider']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Med Claim Providers'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Med Claim Providers'
	open: 'read'