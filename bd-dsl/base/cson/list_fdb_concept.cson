#TABLE: RDAMCA0_CONCEPT
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#DAM_CONCEPT_ID
	dam_concept_id:
		model:
			type: 'int'
		view:
			label: 'DAM Concept ID'
			readonly: true
			columns: 3

	#DAM_CONCEPT_ID_TYP
	dam_concept_id_typ:
		model:
			type: 'int'
		view:
			label: 'DAM Concept Type ID'
			readonly: true
			columns: 3

	#DAM_CONCEPT_ID_DESC
	dam_concept_id_desc:
		view:
			label: 'DAM Concept Type Description'
			readonly: true
			columns: 3

	#DAM_PICKLIST_IND
	dam_picklist_ind:
		view:
			label: 'DAM Picklist Indicator'
			readonly: true
			columns: 3

	#DAM_MED_IND
	dam_med_ind:
		view:
			label: 'DAM Spans Medication Indicator'
			readonly: true
			columns: 3

	#DAM_FOOD_IND
	dam_food_ind:
		view:
			label: 'DAM Spans Food Indicator'
			readonly: true
			columns: 3

	#DAM_ENVIRON_AGENT_IND
	dam_environ_agent_ind:
		view:
			label: 'DAM Spans Environment Agent Indicator'
			readonly: true
			columns: 3

	#DAM_NON_ALRGN_IND
	dam_non_alrgn_ind:
		view:
			label: 'DAM Non Allergen Indicator'
			readonly: true
			columns: 3

	#DAM_CONCEPT_STATUS_CD
	dam_concept_status_cd:
		view:
			label: 'DAM Concept Status Code'
			readonly: true
			columns: 3
model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{dam_concept_id_desc}"
	indexes:
		many: [
			['dam_concept_id']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['dam_concept_id', 'dam_concept_id_typ', 'dam_concept_id_desc', 'dam_picklist_ind', 'dam_med_ind', 'dam_food_ind', 'dam_environ_agent_ind', 'dam_non_alrgn_ind', 'dam_concept_status_cd']

view:
	comment: 'Manage > List FDB Drug Allergy Concept Attributes Table'
	find:
		basic: ['dam_concept_id', 'dam_concept_id_typ', 'dam_concept_id_desc', 'dam_picklist_ind', 'dam_med_ind', 'dam_food_ind', 'dam_environ_agent_ind', 'dam_non_alrgn_ind', 'dam_concept_status_cd']
	grid:
		fields: ['dam_concept_id', 'dam_concept_id_typ', 'dam_concept_id_desc', 'dam_picklist_ind', 'dam_med_ind', 'dam_food_ind', 'dam_environ_agent_ind', 'dam_non_alrgn_ind', 'dam_concept_status_cd']
	label: 'List FDB Drug Allergy Concept Attributes Table'
