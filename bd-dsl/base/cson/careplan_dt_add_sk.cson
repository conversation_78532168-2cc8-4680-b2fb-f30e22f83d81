fields:

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			readonly: true
			offscreen: true

	supply_kit_id:
		model:
			required: true
			source: 'inventory_supply_kit'
			sourcefilter:
				site_id:
					'dynamic': '{site_id}'
		view:
			columns: 2
			label: 'Supply Kit'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory_supply_kit/?limit=1&filter=id:'
				fields:
					'billable_id': ['billable_id'],
					'subform_item':
						'type': 'subform'
						'field': 'subform_item'
						'fields':
							'inventory_id': ['sf.inventory_id']
							'dispense_quantity': ['sf.dispense_quantity']
							'bill': ['sf.bill']
							'part_of_kit': ['sf.part_of_kit']
							'one_time_only': ['sf.one_time_only']
			]

	billable_id:
		model:
			required: false
			source: 'inventory'
		view:
			columns: 2
			label: 'Supply Kit Billable'
			readonly: true

	subform_item:
		model:
			multi: true
			type: 'subform'
			source: 'careplan_dt_add_skpi'
		view:
			grid:
				add: 'none'
				hide_cardmenu: true
				edit: false
				fields: ['inventory_id', 'dispense_quantity', 'part_of_kit', 'one_time_only']
				label: ['Item', 'Quantity', 'Part of Kit', '1x Only']
				width: [60, 15, 15, 10]
			label: 'Supply Item'

	comments:
		view:
			control: 'area'
			label: 'Supply Kit Comments'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']

	reportable: false
	name: ['created_on', 'created_by']

	sections_group: [
		'Select Supply Kit':
			hide_header: true
			indent: false
			fields: ['site_id', 'supply_kit_id', 'billable_id']
		'Supply Kit Preview':
			hide_header: true
			indent: false
			fields: ['subform_item']
		'Comments':
			hide_header: true
			indent: false
			fields: ['comments']
	]
view:
	hide_cardmenu: true
	comment: 'Patient > Select Supply Kit'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Select Supply Kit'
	open: 'read'
