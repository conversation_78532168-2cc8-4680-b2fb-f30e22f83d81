fields:
	code:
		model:
			type: 'text'
			max: 64
			required: true
		view:
			label: 'Code'
			readonly: true
			columns: 3

	routed_med_id:
		model:
			type: 'int'
			max: 8
			required: true
		view:
			label: 'MED Routed Medication ID (Stable ID)'
			readonly: true
			columns: 3

	med_dosage_form_id:
		model:
			type: 'int'
			max: 5
			required: true
		view:
			label: 'MED Dosage Form ID'
			readonly: true
			columns: 3

	med_status_cd:
		model:
			type: 'text'
			max: 1
			required: true
		view:
			label: 'MED Medication Status Code'
			readonly: true
			columns: 3

	routed_dosage_form_med_id:
		model:
			type: 'int'
			max: 8
			required: true
		view:
			label: 'MED Routed Dosage Form Medication ID (Stable ID)'
			readonly: true
			columns: 3

	med_routed_df_med_id_desc:
		model:
			type: 'text'
			max: 60
			required: true
		view:
			label: 'MED Routed Dosage Form Medication Description'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: '{routed_med_id} {med_dosage_form_id}'
	indexes:
		many: [
			['routed_med_id']
			['med_dosage_form_id']
			['routed_dosage_form_med_id']
		]

	sections:
		'Routed Dose Form Medication':
			tab: 'Information'
			fields: [
				'code',
				'routed_med_id',
				'med_dosage_form_id',
				'med_status_cd',
				'routed_dosage_form_med_id',
				'med_routed_df_med_id_desc'
			]

view:
	comment: 'Manage > List FDB RMIDFID1 Routed Dose Form Med'
	find:
		basic: ['routed_med_id', 'med_dosage_form_id']
	grid:
		fields: [
			'routed_med_id',
			'med_dosage_form_id',
			'med_status_cd',
			'routed_dosage_form_med_id',
			'med_routed_df_med_id_desc'
		]
	label: 'List FDB RMIDFID1 Routed Dose Form Med'