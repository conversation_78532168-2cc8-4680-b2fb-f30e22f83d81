fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			readonly: true
			offscreen: true

	order_id:
		model:
			required: true
			multi: false
			source: 'careplan_order'
		view:
			label: 'Order'
			offscreen: true
			readonly: true

	rflt:
		model:
			multi: true
			source: 'careplan_order_rental'
		view:
			readonly: true
			offscreen: true

	rental_id:
		model:
			required: true
			source: 'careplan_order_rental'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				parent_id:
					'dynamic': '{order_id}'
				parent_form:
					'static': 'careplan_order'
				serial_no:
					'static': 'null'
				id:
					'dynamic': '{rflt}'
		view:
			label: 'Rental'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']

	reportable: false
	name: ['created_on', 'created_by']

	sections:
		'Select Rental':
			hide_header: true
			indent: false
			fields: ['patient_id', 'order_id', 'rflt', 'rental_id']

view:
	hide_cardmenu: true
	comment: 'Patient > Select Rental'
	grid:
		fields: ['order_id', 'rental_id']
	label: 'Select Rental'
	open: 'read'
