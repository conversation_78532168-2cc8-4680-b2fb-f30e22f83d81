fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	order_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 2
			label: 'Order Date'
	
	ordering_physician_id:
		model:
			source: 'physician'
		view:
			label: 'Ordering Physician'
			columns: 2

	function_limitation_id:
		model:
			query: 'select_functional_limitation'
			dynamic:
				source: 'list_functional_limitation'
		view:
			class: 'label-line groups top',
			label: 'Functional Limits'
			columns: 2
		
	start_of_care:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Start of Care'
			readonly: true
			offscreen: true
			validate: [
				name: 'FillOrderRXStartDate'
			]

	verbal_order_add1:
		model:
			query: 'select_pot_order'
			dynamic:
				source: 'list_pot_order'
			max: 70
		view:
			class: 'label-line groups top'
			label: '1'
			columns: 2.1

	verbal_order_add2:
		model:
			query: 'select_pot_order'
			dynamic:
				source: 'list_pot_order'
			max: 70
		view:
			class: 'label-line groups top'
			label: '2'
			columns: 2.1

	verbal_order_add3:
		model:
			query: 'select_pot_order'
			dynamic:
				source: 'list_pot_order'
			max: 70
		view:
			class: 'label-line groups top'
			label: '3'
			columns: 2.1

	verbal_order_add4:
		model:
			query: 'select_pot_order'
			dynamic:
				source: 'list_pot_order'
			max: 70
		view:
			class: 'label-line groups top'
			label: '4'
			columns: 2.1

	verbal_order_add5:
		model:
			query: 'select_pot_order'
			dynamic:
				source: 'list_pot_order'
			max: 70
		view:
			class: 'label-line groups top'
			label: '5'
			columns: 2.1

	verbal_order_add6:
		model:
			query: 'select_pot_order'
			dynamic:
				source: 'list_pot_order'
			max: 70
		view:
			class: 'label-line groups top'
			label: '6'
			columns: 2.1

	verbal_order_add7:
		model:
			query: 'select_pot_order'
			dynamic:
				source: 'list_pot_order'
			max: 70
		view:
			class: 'label-line groups top'
			label: '7'
			columns: 2.1

	verbal_order_add8:
		model:
			query: 'select_pot_order'
			dynamic:
				source: 'list_pot_order'
			max: 70
		view:
			class: 'label-line groups top'
			label: '8'
			columns: 2.1

	verbal_order_add9:
		model:
			query: 'select_pot_order'
			dynamic:
				source: 'list_pot_order'
			max: 70
		view:
			class: 'label-line groups top'
			label: '9'
			columns: 2.1

	verbal_order_add10:
		model:
			query: 'select_pot_order'
			dynamic:
				source: 'list_pot_order'
			max: 70
		view:
			class: 'label-line groups top'
			label: '10'
			columns: 2.1

	verbal_order_add11:
		model:
			query: 'select_pot_order'
			dynamic:
				source: 'list_pot_order'
			max: 70
		view:
			class: 'label-line groups top'
			label: '11'
			columns: 2.1

	verbal_order_add12:
		model:
			query: 'select_pot_order'
			dynamic:
				source: 'list_pot_order'
			max: 70
		view:
			class: 'label-line groups top'
			label: '12'
			columns: 2.1

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ["patient-form"]
	indexes:
		many: [
			['patient_id']
			['function_limitation_id']
		]
	reportable: true
	name: ['patient_id', 'function_limitation_id', 'created_on']
	sections:
		'Verbal Order':
			fields: ['order_date', 'ordering_physician_id', 'function_limitation_id', 'start_of_care' ]
		'NEW / VERBAL ORDER': 
			fields: [ 'verbal_order_add1', 'verbal_order_add2', 'verbal_order_add3', 'verbal_order_add4', 
			'verbal_order_add5', 'verbal_order_add6', 'verbal_order_add7', 'verbal_order_add8', 
			'verbal_order_add9', 'verbal_order_add10', 'verbal_order_add11', 'verbal_order_add12' ]

view:
	dimensions:
		width: '75%'
		height: '75%'
	comment: 'Patient > Verbal Orders'
	find:
		basic: ['created_on', 'order_date',]

		advanced: ['function_limitation_id']
	grid:
		fields: ['created_on', 'ordering_physician_id', 'function_limitation_id', 'order_date']
		width: [20, 15, 20, 45]
		sort: ['-created_on']
	label: 'Verbal Order'
	open: 'read'
