
fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	scr_achieved:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields:['scr_achieved_details']
		view:
			control: 'radio'
			label: 'Has SVR been achieved?'

	scr_achieved_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Why not?'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	sections:
		'Ongoing HepC - SVR':
			fields: ['scr_achieved', 'scr_achieved_details']

view:
	comment: 'Patient > Careplan > Ongoing > Hepatitis C'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Ongoing Assessment: Hepatitis C'
	open: 'read'
