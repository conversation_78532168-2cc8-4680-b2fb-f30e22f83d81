fields:

	calendar_id:
		model:
			source: 'calendar'
			type: 'int'
			transform: [
					name: 'AutoNameFk'
					]
		view:
			label: 'Calendar'

	user_id:
		model:
			required: true
			source: 'user'
			sourcefilter:
				role:
					static: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		view:
			label: 'Scheduled For'
			template: '{{user.id}}'

	patient_id:
		model:
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'

	event_start_date: # event_start_date
		model:
			required: true
			type: 'date'
		view:
			label: 'Event Start Date'

	event_start_time: # event_start_time
		model:
			required: true
			type: 'time'
		view:
			label: 'Event Start Time'

	event_end_date: # event_end_date
		model:
			required: true
			type: 'date'
		view:
			label: 'Event End Date'

	event_end_time: # event_end_time
		model:
			type: 'time'
		view:
			label: 'Event End Time'

	appointment_status:
		model:
			default: 'Scheduled'
			source:['Confirmed', 'Scheduled', 'Need Orders', 'Hold/Pending', 'Missed', 'Canceled']
			if:
				'Missed':
					fields: ['missed_reason']
				'Canceled':
					fields: ['missed_reason']
		view:
			label: 'Appointment Status'


	missed_reason:
		model:
			source:['Sick', 'Insurance', 'Hospitalized', 'MD Appointment', 'No Show', 'Inactive Orders', 'Other']
			if:
				'Other':
					fields: ['missed_reason_other']
		view:
			label: 'Missed/Canceled Reason'

	missed_reason_other:
		model:
			required: true
		view:
			label: 'Missed/Canceled Reason Other'

	completed_on:
		model:
			required: true
			type: 'date'
		view:
			label: 'Completed On'

	notes:
		model:
			max: 512
		view:
			control: 'area'
			label: 'Notes'
			note: 'Not visible to patients / physicians'

	portal_display:
		model:
			max: 3
			min: 2
			source: ['No', 'Yes']
			default: 'Yes'
		view:
			control: 'radio'
			note: 'Shows up as "Upcoming Nursing Visit"'
			label: 'Display on Patient Portal?'

	file_attachment:
		model:
			type: 'json'
		view:
			control: 'file'
			label: 'File Attachment'
			note: 'Max 100MB. Only documents, images, and archives supported.'

	series: #series 
		model:
			max: 3
			min: 2
			required: false
			default: 'No'
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['Setup Repeat Event Appointments']
					field_label:
						event_start_date: 'Schedule Start'
				'No':
					field_label:
						event_start_date: 'Schedule Date'
		view:
			control: 'radio'
			label: 'Schedule Repeat Appointments?'
			# offscreen: true
			# readonly: true

	repeat_period: # repeat_period
		model:
			required: true
			source: ['Daily', 'Weekly', 'Monthly']
			if:
				'Daily':
					fields: ['repeat_daily']
				'Weekly':
					fields: ['repeat_weekly', 'repeat_on']
				'Monthly':
					fields: ['repeat_monthly', 'repeat_by']
		view:
			control: 'radio'
			label: 'Repeat Period'

	repeat_daily: #repeat_daily
		model:
			default: 1
			required: true
			type: 'int'
		view:
			label: 'Repeat Every'
			note: 'Days'

	repeat_weekly: #repeat_weekly
		model:
			default: 1
			required: true
			type: 'int'
		view:
			label: 'Repeat Every'
			note: 'Weeks'

	repeat_monthly: #repeat_monthly
		model:
			default: 1
			required: true
			type: 'int'
		view:
			label: 'Repeat Every'
			note: 'Months'

	repeat_on: #repeat_on
		model:
			source: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
			# multi:true

			required: true
		view:
			control: 'checkbox'
			label: 'Repeat On'

	repeat_by: #repeat_by
		model:
			required: true
			source: ['Day of the Month', 'Day of the Week']
		view:
			control: 'radio'
			label: 'Repeat on the Same'

	event_end:
		model:
			type: 'date'
		view:
			label: 'End Date'
			validate: [
				name: 'ScheduledEndValidate'
			]

	therapy_1:
		model:
			max: 64
			prefill: ['careplan']
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Primary Therapy'
			readonly:false

	encounter_id:
		model:
			type: 'int'
		view:
			label: 'Linked Encounter ID'
			offscreen: true
			readonly: true

	travel_time_total:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			readonly: true
			label: 'Total Travel Time (hrs)'

	time_total:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			readonly: true
			label: 'Total Visit Time (hrs)'

	ical:
		view:
			control: 'area'
			readonly: true
			offscreen: true
			label: 'iCal Recurring Rule'


	# external_order:
	# 	model:
	# 		source: 'careplan_order'
	# 		sourcefilter:
	# 			patient_id:
	# 				'dynamic': '{patient_id}'
	# 			type:
	# 				'static': 'Primary'
	# 			discontinued:
	# 				'static': 'No'
	# 	view:
	# 		label: 'Linked Order'
	# nxt_visit_reminder:
	# 	model:
	# 		type: 'date'
	# 	view:
	# 		offscreen: true
	
	# override_event_date:
	# 	view:
	# 		offscreen: true
	# 		label: 'Override Event Date'

	# override_appointment_start:
	# 	view:
	# 		offscreen: true
	# 		label: 'Override Start Time'

	# hide_reoccur:
	# 	model:
	# 		default: 'No'
	# 		source: ['No', 'Yes']
	# 		if:
	# 			'No':
	# 				fields: ['series']
	# 	view:
	# 		offscreen: true
	# pat_sch_enc:
	# 	model:
	# 		access:
	# 			read: ['patient']
	# 		type: 'int'
	# 	view:
	# 		label: 'patient schedule encounter'
	# 		offscreen: true
	# Status
	# drug_shipped:
	# 	model:
	# 		max: 3
	# 		min: 2
	# 		source: ['No', 'Yes']
	# 	view:
	# 		control: 'radio'
	# 		label: 'Drug Shipped?'
	# active:
	# 	model:
	# 		access:
	# 			read: ['patient']
	# 		max: 3
	# 		min: 2
	# 		required: true
	# 		default: 'Yes'
	# 		source: ['No', 'Yes']
	# 	view:
	# 		control: 'radio'
	# 		label: 'Schedule Active?'
	# reoccur_appointments:
	# 	model:
	# 		multi: false
	# 		source: 'patient_sch_enc_rpt_appt'
	# 		type: 'subform'
	# 	view:
	# 		label: 'Reoccurred appointment'
	# 		note: 'Set appointments that can be reoccurred'

	# sh_reoccur:
	# 	model:
	# 		access:
	# 			read: ['patient']
	# 		max: 3
	# 		min: 2
	# 		required: false
	# 		default: 'No'
	# 		source: ['No', 'Yes']
	# 		if:
	# 			'Yes':
	# 				fields: ['series']
	# 	view:
	# 		control: 'radio'
	# 		label: 'Hide/Show Reoccur?'
	# 		offscreen: true


	# ical:
	# 	model:
	# 		access:
	# 			read: ['patient']
	# 	view:
	# 		control: 'area'
	# 		readonly: true
	# 		offscreen: true
	# 		label: 'iCal Recurring Rule'


model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	transform_post: [
		name: 'PermissionAccess'
		arguments:
			user: 'user_id'
			patient: 'patient_id'
			log: false
		name: 'RRuleTransform'
		source:
			dtstart: 'effective_start_date',
			until: 'effective_end_date',
			freq: 'repeat_period',
			interval_daily: 'repeat_daily',
			interval_weekly: 'repeat_weekly',
			interval_monthly: 'repeat_monthly',
			byweekday: 'repeat_on',
			monthweekday: 'repeat_by',
			event_start_time: 'effective_start_time',
			event_end_time: 'effective_end_time'
		destination: 'event_end_time'
		]
	indexes:
		many: [
			['event_start_date']
			['patient_id']
			['user_id']
			['event_start_date', 'event_start_time']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				patient_id: 'patient_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	name: '{effective_start_date} {effective_start_time}'
	sections_group: [
		'Schedule Event Series':
			sections: [
				'Schedule':
					fields: [ 'patient_id', 'user_id', 'calendar_id', 'therapy_1','series', 'ical']
				'Setup Appointment':
					fields: ['event_start_date', 'event_start_time', 'event_end_date', 'event_end_time', 'appointment_status', 'missed_reason', 'missed_reason_other', 'notes', 'portal_display']
				'Setup Repeat Event Appointments':
					fields: [
						'repeat_period','repeat_daily', 'repeat_weekly', 'repeat_monthly','repeat_by',
						'repeat_on', 'event_end'
						]
				'File Attachments':
					fields: ['file_attachment']
				]
	]

view:
	comment: 'Patient > Schedule Event Series'
	find:
		basic: ['appointment_status', 'therapy_1', 'event_start_date']
	grid:
		fields: ['event_start_date', 'appointment_status', 'therapy_1', 'notes']
		sort: ['-event_start_date', '-event_start_time']
	label: 'Schedule Event Series'
	open: 'read'
