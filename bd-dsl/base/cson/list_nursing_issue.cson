fields:

	code:
		model:
			required: true
		view:
			columns: 2
			label: 'Name'
			findunique: true

	ae_only:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'No'
		view:
			columns: 2
			control: 'radio'
			label: 'AE Related Only?'

	complaint_only:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'No'
		view:
			columns: 2.1
			control: 'radio'
			label: 'Complaint Related Only?'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 2
			control: 'radio'
			label: 'Can Sync Record'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 2
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['lists']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['code']
		]
	name: ['code']
	sections:
		'Adverse Event/Complaint Nursing Issue':
			fields: ['code', 'ae_only', 'complaint_only', 'allow_sync', 'active']

view:
	comment: 'Manage > Adverse Event/Complaint Nursing'
	find:
		basic: ['ae_only', 'complaint_only']
	grid:
		fields: ['code', 'ae_only', 'complaint_only', 'created_by', 'created_on']
		sort: ['code']
	label: 'AE/Complaint Nursing'
	open: 'read'
