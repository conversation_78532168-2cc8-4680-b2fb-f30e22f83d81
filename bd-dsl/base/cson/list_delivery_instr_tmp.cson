fields:

	name:
		model:
			required: true
		view:
			control: 'area'
			label: 'Name'
			findunique: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['lists']
	indexes:
		unique: [
			['name']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name']

view:
	comment: 'Manage > Delivery Instructions Template'
	find:
		basic: ['name']
	grid:
		fields: ['name']
		sort: ['name']
	label: 'Delivery Instructions Template'
	open: 'read'
