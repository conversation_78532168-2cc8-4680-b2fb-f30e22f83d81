fields:

	repricing_organization_identifier:
		model:
			max: 50
			min: 1
		view:
			columns: 4
			label: 'Repric Org ID'
			note: 'ID of the repricing entity'
			reference: 'HCP04'
			_meta:
				location: '2300 HCP'
				field: '04'
				path: 'claimInformation.claimPricingRepricingInformation.repricingOrganizationIdentifier'

	pricing_methodology_code:
		model:
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'HCP01'
		view:
			columns: 2
			label: 'Pricing Meth Code'
			reference: 'HCP01'
			_meta:
				location: '2300 HCP'
				field: '01'
				path: 'claimInformation.claimPricingRepricingInformation.pricingMethodologyCode'

	exception_code:
		model:
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'HCP15'
		view:
			columns: 2
			label: 'Exception Code'
			reference: 'HCP15'
			_meta:
				location: '2300 HCP'
				field: '15'
				path: 'claimInformation.claimPricingRepricingInformation.exceptionCode'

	repriced_allowed_amount:
		model:
			required: true
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: -4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Allowed Amount'
			reference: 'HCP02'
			_meta:
				location: '2300 HCP'
				field: '02'
				path: 'claimInformation.claimPricingRepricingInformation.repricedAllowedAmount'

	repriced_saving_amount:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Saving Amount'
			reference: 'HCP03'
			_meta:
				location: '2300 HCP'
				field: '03'
				path: 'claimInformation.claimPricingRepricingInformation.repricedSavingAmount'

	repricing_per_diem_or_flat_rate_amount:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Per Diem/Flat Rate Amt'
			reference: 'HCP05'
			_meta:
				location: '2300 HCP'
				field: '05'
				path: 'claimInformation.claimPricingRepricingInformation.repricingPerDiemOrFlatRateAmount'

	repriced_approved_ambulatory_patient_group_code:
		model:
			max: 50
			min: 1
		view:
			label: 'Approv Amb Pt Grp Code'
			reference: 'HCP06'
			offscreen: true
			readonly: true
			_meta:
				location: '2300 HCP'
				field: '06'
				path: 'claimInformation.claimPricingRepricingInformation.repricedApprovedAmbulatoryPatientGroupCode'

	repriced_approved_ambulatory_patient_group_amount:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 3
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Approv Amb Pt Grp Amt'
			reference: 'HCP07'
			offscreen: true
			readonly: true
			_meta:
				location: '2300 HCP'
				field: '07'
				path: 'claimInformation.claimPricingRepricingInformation.repricedApprovedAmbulatoryPatientGroupAmount'

	reject_reason_code:
		model:
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'HCP13'
		view:
			columns: -2
			label: 'Reject Reason Code'
			reference: 'HCP13'
			_meta:
				location: '2300 HCP'
				field: '13'
				path: 'claimInformation.claimPricingRepricingInformation.rejectReasonCode'

	policy_compliance_code:
		model:
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'HCP14'
		view:
			columns: 2
			label: 'Policy Compliance Code'
			reference: 'HCP14'
			_meta:
				location: '2300 HCP'
				field: '14'
				path: 'claimInformation.claimPricingRepricingInformation.policyComplianceCode'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['repricing_organization_identifier']
	sections:
		'Repricing':
			hide_header: true
			fields: ['pricing_methodology_code', 'exception_code', 'repricing_organization_identifier', 
			'repriced_allowed_amount', 'repriced_saving_amount', 'repricing_per_diem_or_flat_rate_amount',
			'repriced_approved_ambulatory_patient_group_code', 'repriced_approved_ambulatory_patient_group_amount',
			'reject_reason_code', 'policy_compliance_code']

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	reference: '2300'
	comment: 'Repricing'
	grid:
		fields: ['pricing_methodology_code', 'repriced_allowed_amount', 'reject_reason_code', 'exception_code']
		width: [25, 25, 25, 25]
		sort: ['-created_on']
	label: 'Repricing'
	open: 'read'