fields:

	other_insured_qualifier:
		model:
			default: '1' # Person Entity
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'NM102'
		view:
			columns: 2
			label: 'Other Insured Qualifier'
			reference: 'NM102'
			_meta:
				location: '2330A NM1'
				field: '02'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherInsuredQualifier'

	other_insured_identifier_type_code:
		model:
			default: 'MI' # Member ID
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'NM108-2320'
		view:
			columns: 4
			label: 'Other Insured ID Type Code'
			reference: 'NM108'
			_meta:
				location: '2330A NM1'
				field: '08'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherInsuredIdentifierTypeCode'

	other_insured_identifier:
		model:
			required: true
			min: 2
			max: 80
		view:
			columns: 4
			label: 'Other Insured ID'
			note: 'Member ID'
			reference: 'NM109'
			_meta:
				location: '2330A NM1'
				field: '09'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherInsuredIdentifier'

	other_insured_additional_identifier:
		model:
			min: 1
			max: 50
		view:
			label: 'Other Insured Addtl ID'
			note: 'SSN'
			reference: 'REF02 REF01=SY'
			_meta:
				location: '2330A REF'
				code: 'SY'
				field: '02'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherInsuredAdditionalIdentifier'
			offscreen: true
			readonly: true

	other_insured_first_name:
		model:
			min: 1
			max: 35
		view:
			columns: -4
			label: 'First Name'
			reference: 'NM102'
			_meta:
				location: '2330A NM1'
				field: '02'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherInsuredFirstName'

	other_insured_last_name:
		model:
			required: true
			min: 1
			max: 35
		view:
			columns: 4
			label: 'Last Name'
			reference: 'NM103'
			_meta:
				location: '2330A NM1'
				field: '03'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherInsuredLastName'

	other_insured_middle_name:
		model:
			min: 1
			max: 35
		view:
			columns: 4
			label: 'Middle Name'
			reference: 'NM105'
			_meta:
				location: '2330A NM1'
				field: '05'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherInsuredMiddleName'

	other_insured_name_suffix:
		model:
			min: 1
			max: 35
		view:
			label: 'Suffix'
			reference: 'NM107'
			readonly: true
			offscreen: true
			_meta:
				location: '2330A NM1'
				field: '07'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherInsuredNameSuffix'

	other_insured_address:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_address_osub'
		view:
			label: 'Address'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['other_insured_qualifier','other_insured_first_name', 'other_insured_last_name']
	sections_group: [
		'Other Subscriber Name':
			hide_header: true
			sections: [
				'Insurance':
					hide_header: true
					fields: ['other_insured_qualifier', 'other_insured_identifier_type_code', 'other_insured_identifier',
					'other_insured_additional_identifier']
				'Name':
					hide_header: true
					fields: [ 'other_insured_first_name', 'other_insured_last_name', 'other_insured_middle_name', 'other_insured_name_suffix',]
				'Address':
					hide_header: true
					indent: false
					fields: ['other_insured_address']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	reference: '2330A'
	comment: 'Other Subscriber Name'
	grid:
		fields: ['created_on', 'created_by', 'other_insured_first_name', 'other_insured_last_name']
		sort: ['-created_on']
	label: 'Other Subscriber Name'
	open: 'read'