fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	onesource_enrolled:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['onesource_enrolled_no']
		view:
			control: 'radio'
			label: 'Has patient enrolled with OneSource/Alexion?'

	onesource_enrolled_no:
		view:
			control: 'area'
			label: 'Comments'

	required_vac:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['required_vac_no']
		view:
			control: 'radio'
			label: 'Has patient received required vaccinations (Meningitis B and Quadrivalent)?'

	required_vac_no:
		view:
			control: 'area'
			label: 'Comments'

	achr_pos:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['achr_pos_no']
		view:
			control: 'radio'
			label: 'Is patient positive for AcHR antibody?'

	achr_pos_no:
		view:
			control: 'area'
			label: 'Comments'

	comment:
		view:
			control: 'area'
			label: 'Comments'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'pharm']
		delete:     ['admin', 'csr', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'pharm']
	name: '{patient_id} {careplan_id} onesource enrolled ? : {onesource_enrolled}'
	sections:
		'Soliris Assessment':
			fields: ['onesource_enrolled', 'onesource_enrolled_no',
			'required_vac', 'required_vac_no', 'achr_pos', 'achr_pos_no']

view:
	hide_cardmenu: true
	comment: 'Patient > Intake Soliris Assessment'
	grid:
		fields: ['created_on', 'created_by', 'onesource_enrolled', 'onesource_enrolled_no']
	label: 'Intake Soliris Assessment'
	open: 'read'
