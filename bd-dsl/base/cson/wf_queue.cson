fields:
	tag:
		model:
			required: true
			type: 'text'
		view:
			label: 'Tag'

	cleared_on:
		model:
			required: false
			type: 'datetime'
		view:
			label: 'Cleared On'

	careplan_id:
		model:
			required: false
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan'
	
	priority:
		model:
			required: false
			type: 'int'
		view:
			label: 'Priority'
	
	patient_id:
		model:
			source: 'patient'
		view:
			label: 'Patient'

	site_id:
		model:
			source: 'site'
		view:
			label: 'Site'

	order_1: 
		model:
			source: 'careplan_order'
		view:
			label: 'Referral 1'

	order_2: 
		model:
			source: 'careplan_order'
		view:
			label: 'Referral 2'
	
	order_3: 
		model:
			source: 'careplan_order'
		view:
			label: 'Referral 3'

	order_4: 
		model:
			source: 'careplan_order'
		view:
			label: 'Referral 4'
	
	order_5:
		model:
			source: 'careplan_order'
		view:
			label: 'Referral 5'

	order_item_1:
		model:
			source: 'careplan_order_item'
		view:
			label: 'Order 1'

	order_item_2:
		model:
			source: 'careplan_order_item'
		view:
			label: 'Order 2'

	order_item_3:
		model:
			source: 'careplan_order_item'
		view:
			label: 'Order 3'

	order_item_4:
		model:
			source: 'careplan_order_item'
		view:
			label: 'Order 4'

	order_item_5:
		model:
			source: 'careplan_order_item'
		view:
			label: 'Order 5'

	orderp_item_1:
		model:
			source: 'careplan_orderp_item'
		view:
			label: 'Single Prescription Order 1'

	orderp_item_2:
		model:
			source: 'careplan_orderp_item'
		view:
			label: 'Single Prescription Order 2'

	orderp_item_3:
		model:
			source: 'careplan_orderp_item'
		view:
			label: 'Single Prescription Order 3'

	orderp_item_4:
		model:
			source: 'careplan_orderp_item'
		view:
			label: 'Single Prescription Order 4'

	orderp_item_5:
		model:
			source: 'careplan_orderp_item'
		view:
			label: 'Single Prescription Order 5'

	prescription_id:
		model:
			source: 'careplan_order_rx'
		view:
			label: 'Prescription'

	physician_id: 
		model:
			source: 'physician'
		view:
			label: 'Physician'

	therapy_1:
		model:
			max: 64
			required: false
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Primary Therapy'

	therapy_2:
		model:
			max: 64
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Secondary Therapy'

	therapy_3:
		model:
			max: 64
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Tertiary Therapy'

	therapy_4:
		model:
			max: 64
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Quaternary Therapy'

	therapy_5:
		model:
			max: 64
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Quinary Therapy'
	
	drug_brand_1:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Primary Drug Brand'
	
	drug_brand_2:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Secondary Drug Brand'
	
	drug_brand_3:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Tertiary Drug Brand'

	drug_brand_4:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Quaternary Drug Brand'

	drug_brand_5:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			label: 'Quinary Drug Brand'

	dx_1:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			label: 'Primary Diagnosis'

	dx_2:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			label: 'Secondary Diagnosis'

	dx_3:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			label: 'Tertiary Diagnosis'

	dx_4:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			label: 'Quaternary Diagnosis'

	dx_5:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			label: 'Quinary Diagnosis'
		
	assigned_team:
		model:
			source: 'wf_queue_team'
		view:
			label: 'Assigned Team'
	
	assigned_user:
		model:
			source: 'user'
		view:
			label: 'Assigned User'

	due_date:
		model:
			type: 'datetime'
		view:
			label: 'Due Date'
	
	snooze_until:
		model:
			type: 'datetime'
		view:
			label: 'Snooze Until'

	status:
		model:
			source: 'list_wf_queue_status'
		view:
			label: 'Status'

	prescriber_id:
		model:
			source: 'patient_prescriber'
		view:
			label: 'Prescriber ID'

model:
	access:
		create:     ['admin']
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    ['admin']
		update:     ['admin']
		update_all: ['admin']
		write:      ['admin']
	name: '{tag}'
	indexes:
		many: [
			['tag', 'cleared_on']
			['priority']
			['cleared_on']
			['patient_id']
			['assigned_team']
			['assigned_user']
			['prescription_id']
		]
		unique: [
			['tag']
		]
	sections:
		'Worflow Item Details':
			fields: ['status', 'tag', 'cleared_on', 'priority', 'due_date']
		'Assigned To':
			fields: ['assigned_team', 'assigned_user']
		'Links':
			fields: ['patient_id', 'careplan_id', 'physician_id']
		'Order':
			fields: ['order_1', 'order_2', 'order_3', 'order_4', 'order_5']
		'Order Items':
			fields: ['order_item_1', 'order_item_2', 'order_item_3', 'order_item_4', 'order_item_5']
		'Order Items (Single Prescription)':
			fields: ['orderp_item_1', 'orderp_item_2', 'orderp_item_3', 'orderp_item_4', 'orderp_item_5']
		'Prescription':
			fields: ['prescription_id']
		'Therapy':
			fields: ['therapy_1', 'therapy_2', 'therapy_3', 'therapy_4', 'therapy_5']
		'Diagnosis':
			fields: ['dx_1', 'dx_2', 'dx_3', 'dx_4', 'dx_5']
		'Drug Brand':
			fields: ['drug_brand_1', 'drug_brand_2', 'drug_brand_3', 'drug_brand_4', 'drug_brand_5']
view:
	comment: 'Workflow Queue'
	find:
		basic: ['tag', 'cleared_on', 'priority', 'patient_id', 'physician_id', 'assigned_team', 'assigned_user', 'due_date']
	grid:
		fields: ['tag', 'cleared_on', 'priority', 'patient_id', 'physician_id', 'assigned_team', 'assigned_user', 'due_date']
	label: 'Workflow Queue'
	open: 'read'
