fields:

	bname_id:
		model:
			multi: true
			required: true
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Drug Brand'

	assessment_code:
		model:
			required: true
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			control: 'select'
			label: 'Assessment Code'
			columns: 2

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 2

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 2
model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['workflow']
	sync_mode: 'mixed'
	name: ['bname_id', 'assessment_code']
	indexes:
		unique: [
			['bname_id']
		]
	sections:
		'Drug Brand Assessment Map':
			fields: ['bname_id', 'assessment_code', 'allow_sync', 'active']

view:
	comment: 'Manage > Drug Brand Assessment Map'
	find:
		basic: ['bname_id']
	grid:
		fields: ['bname_id', 'assessment_code']
		sort: ['bname_id']
	label: 'Drug Brand Assessment Map'
	open: 'read'
