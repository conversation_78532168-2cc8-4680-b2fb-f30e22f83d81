fields:

	other_payer_primary_identifier:
		model:
			min: 2
			max: 80
			required: true
		view:
			columns: 4
			label: 'Primary Payer ID'
			reference: 'SVD01'
			_meta:
				location: '2430 SVD'
				field: '01'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].otherPayerPrimaryIdentifier'

	procedure_code:
		model:
			required: true
			min: 1
			max: 48
		view:
			columns: 4
			label: 'Proc Code'
			reference: 'SVD03-02'
			_meta:
				location: '2430 SVD'
				field: '03-02'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].procedureCode'

	service_id_qualifier:
		model:
			required: true
			default: 'HC'
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SVD03-01'
		view:
			columns: 2
			label: 'Procedure Qualifier'
			reference: 'SVD03-01'
			_meta:
				location: '2430 SVD'
				field: '03-01'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].serviceIdQualifier'

	procedure_code_description:
		model:
			min: 1
			max: 50
		view:
			columns: 2
			label: 'Description'
			reference: 'SVD03-07'
			_meta:
				location: '2430 SVD'
				field: '03-07'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].procedureCodeDescription'

	paid_service_unit_count:
		model:
			required: true
			max: 99999.999
			min: 0.001
			rounding: 0.001
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Charge Units'
			reference: 'SVD05'
			_meta:
				location: '2430 SVD'
				field: '05'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].paidServiceUnitCount'

	adjudication_or_payment_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			label: 'Adj/Pmt Date'
			note: 'Must be in MM/DD/YYYY format'
			reference: 'DTP03 DTP01=573'
			_meta:
				location: '2430 DTP'
				field: '03'
				code: 'D8'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].adjudicationOrPaymentDate'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

	service_line_paid_amount:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
			required: true
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Paid Amount'
			reference: 'SVD02'
			_meta:
				location: '2430 SVD'
				field: '02'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].serviceLinePaidAmount'

	remaining_patient_liability:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Remaining Liability Amount'
			reference: 'AMT02 AMT01=EAF'
			_meta:
				location: '2430 AMT'
				field: '02'
				code: 'EAF'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].remainingPatientLiability'

	modifier_1:
		model:
			min: 2
			max: 2
		view:
			columns: 'modifier'
			label: 'Modifier 1'
			reference: 'SVD03-03'
			_meta:
				location: '2430 SVD'
				field: '03-03'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].procedureModifier[0]'

	modifier_2:
		model:
			min: 2
			max: 2
			if:
				'*':
					require_fields: ['modifier_1']
		view:
			columns: 'modifier'
			label: 'Modifier 2'
			reference: 'SVD03-04'
			_meta:
				location: '2430 SVD'
				field: '03-04'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].procedureModifier[1]'

	modifier_3:
		model:
			min: 2
			max: 2
			if:
				'*':
					require_fields: ['modifier_2']
		view:
			columns: 'modifier'
			label: 'Modifier 3'
			reference: 'SVD03-05'
			_meta:
				location: '2430 SVD'
				field: '03-05'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].procedureModifier[2]'

	modifier_4:
		model:
			min: 2
			max: 2
			if:
				'*':
					require_fields: ['modifier_3']
		view:
			columns: 'modifier'
			label: 'Modifier 4'
			reference: 'SVD03-06'
			_meta:
				location: '2430 SVD'
				field: '03-06'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].procedureModifier[3]'

	claim_adjustment_information:
		model:
			multi: true
			source: 'med_claim_adj_sl'
			type: 'subform'
		view:
			note: 'Max 5'
			label: 'Adjustments'
			max_count: 5

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['other_payer_primary_identifier']
	sections_group: [
		'Service Line Adjudication':
			hide_header: true
			sections: [
				'Service':
					hide_header: true
					fields: ['other_payer_primary_identifier', 'procedure_code', 'service_id_qualifier','procedure_code_description', 'paid_service_unit_count',
					'adjudication_or_payment_date', 'service_line_paid_amount', 'remaining_patient_liability']
				'Modifiers':
					hide_header: true
					fields: ['modifier_1', 'modifier_2', 'modifier_3', 'modifier_4']
				'Adjustments':
					hide_header: true
					indent: false
					fields: ['claim_adjustment_information']
			]
	]
view:
	dimensions:
		width: '85%'
		height: '75%'
	hide_cardmenu: true
	reference: '2340'
	comment: 'Service Line Adjudication'
	grid:
		fields: ['other_payer_primary_identifier', 'procedure_code', 'adjudication_or_payment_date', 'service_line_paid_amount', 'remaining_patient_liability']
		sort: ['-created_on']
	label: 'Service Line Adjudication'
	open: 'read'