fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	inventory_id:
		model:
			required: true
			source: 'inventory'
			sourcefilter:
				therapy_id:
					'static': 'factor'
				active:
					'static': 'Yes'
		view:
			label: 'Drug'
			columns: 2

	prescription_provided_in:
		model:
			required: true
			source: ['Range', 'Variance']
			default: 'Variance'
			if:
				'Range':
					fields: ['dose_range_1', 'dose_range_2', 'dose_unit']
					readonly:
						fields: ['allowed_variance']
				'Variance':
					fields: ['dose', 'dose_unit']
		view:
			columns: 2
			control: 'radio'
			label: 'Prescription Format'

	dose_type_id:
		model:
			multi: false
			required: true
			source: ['Prophylaxis/Bleed', 'Prophylaxis', 'Bleed', 'Procedure']
		view:
			columns: 2
			class: 'checkbox checkbox-2'
			control: 'checkbox'
			label: 'Used For'

	max_doses_per_fill:
		model:
			required: true
			type: 'int'
			min: 1
			max: 99
		view:
			label: 'Max Doses/Fill'
			columns: 4
			validate: [
				{
					name: "CompareValidator"
					fields: [
						"max_doses_per_fill"
						"doses_allowed"
					]
					require: "lte"
					error: "Max Doses/Fill must be less than Doses Allowed"
				}
		]

	doses_allowed:
		model:
			required: true
			type: 'int'
			min: 1
			max: 99
		view:
			label: 'Max Doses/Rx'
			columns: 4
			validate: [
				{
					name: "CompareValidator"
					fields: [
						"max_doses_per_fill"
						"doses_allowed"
					]
					require: "lte"
					error: "Max Doses/Fill must be less than Doses Allowed"
				}
		]

	dose_str:
		model:
			required: true
		view:
			label: 'Dose'
			readonly: true
			offscreen: true

	dose:
		model:
			type: 'decimal'
			rounding: 1
			required: true
			max: 9999999.999 
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Dose'
			validate: [
				{
					name: "BuildFactorDoseString"
				}
			]

	dose_range_1:
		model:
			type: 'decimal'
			rounding: 1
			required: true
			max: 9999999.999 
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Dose Range Start'
			transform: [
				{
					name: "CalculateAllowedVariance"
				}
			]
			validate: [
				{
					name: "BuildFactorDoseString"
				}
			]

	dose_range_2:
		model:
			type: 'decimal'
			rounding: 1
			required: true
			max: 9999999.999 
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Dose Range End'
			transform: [
				{
					name: "CalculateAllowedVariance"
				}
			]
			validate: [
				{
					name: "BuildFactorDoseString"
				}
			]

	dose_unit_id:
		model:
			required: true
			source: 'list_unit'
			sourceid: 'code'
		view:
			label: 'Units'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateUnitFieldNote'
					target: ['dose', 'dose_range_1', 'dose_range_2']
				}
			]

	dose_unit:
		model:
			required: true
			source:
				"units": "Units"
				"IU": "IU"
				"MU": "MU"
		view:
			label: 'Units'
			columns: 4
			control: 'radio'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'dose_unit_id'
					overwrite: true
				},
				{
					name: "BuildFactorDoseString"
				}
			]

	allowed_variance:
		model:
			required: true
			type: 'decimal'
			rounding: 1
			min: 1
			max: 10
		view:
			columns: 4
			format: 'percent'
			class: 'numeral'
			label: 'Variance %'
			note: 'Max 10%'

	frequency_type:
		model:
			required: true
			source: ['PRN', 'Daily', 'Weekly', 'Other']
			if:
				'Weekly':
					fields: ['frequency_weekly']
		view:
			columns: 2
			control: 'radio'
			label: 'Frequency'

	frequency_weekly:
		model:
			required: true
			multi: true
			source:
				sunday: "Sunday"
				monday: "Monday"
				tuesday: "Tuesday"
				wednesday: "Wednesday"
				thursday: "Thursday"
				friday: "Friday"
				saturday: "Saturday"
		view:
			control: 'checkbox'
			label: 'Week Day Frequency'
			columns: 2

	admin_directions:
		model:
			max: 270
		view:
			label: 'Admin Directions'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['inventory_id']
	indexes:
		many: [
			['inventory_id']
		]
	sections:
		'Factor Drug':
			hide_header: true
			indent: false
			fields: ['inventory_id','prescription_provided_in',
			'max_doses_per_fill', 'doses_allowed',
			'dose_type_id']
		'Dosing':
			hide_header: true
			indent: false
			fields: ['dose', 'dose_range_1', 'dose_range_2', 'dose_unit', 'dose_unit_id', 'dose_str', 'allowed_variance']
		'Frequency':
			hide_header: true
			indent: false
			fields: ['frequency_type', 'frequency_weekly']
		'Admin Directions':
			hide_header: true
			indent: false
			fields: ['admin_directions']
view:
	dimensions:
		width: '90%'
		height: '45%'
	hide_cardmenu: true
	comment: 'Patient > Patient Prescription > Factor Drug'
	grid:
		fields: ['inventory_id']
		sort: ['-created_on']
	label: 'Factor Drug'
	open: 'read'
