fields:

	npi:
		model:
			source: 'list_cms_npi'
			sourceid: 'code'
		view:
			columns: 3
			label: 'NPI'
			findunique: true

	healthcare_provider_taxonomy_code:
		view:
			columns: 3
			label: 'Healthcare Provider Taxonomy Code'

	provider_license_number:
		view:
			columns: 3
			label: 'Provider License Number'

	provider_license_number_state_code:
		view:
			columns: 3
			label: 'Provider License Number State Code'

	healthcare_provider_primary_taxonomy_switch:
		view:
			columns: 3
			label: 'Healthcare Provider Primary Taxonomy Switch'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		many: [
			['npi']
		]
	name: ['npi']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['npi', 'healthcare_provider_taxonomy_code', 'provider_license_number', 'provider_license_number_state_code', 'healthcare_provider_primary_taxonomy_switch']

view:
	comment: 'Manage > Billing State Provider Identifier'
	find:
		basic: ['npi']
	grid:
		fields: ['npi', 'healthcare_provider_taxonomy_code']
	label: 'Billing State Provider Identifier'
