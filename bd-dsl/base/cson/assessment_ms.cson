fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	type:
		model:
			required: true
			source: ['Relapsing-remitting', 'Secondary-progressive',
			'Primary-progressive', 'Other', 'Unknown']
			if:
				'Other':
					fields: ['type_details']
		view:
			control: 'radio'
			label: 'Type of MS'

	type_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Details'

	exacerbations_last_month:
		model:
			type: 'int'
			required: true
		view:
			label: 'Number of exacerbations last month'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Multiple Sclerosis Questionnaire':
			fields: ['type', 'type_details', 'exacerbations_last_month']
view:
	comment: 'Patient > Careplan > Assessment > Multiple Sclerosis'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Multiple Sclerosis'
	open: 'read'