fields:

	code:
		model:
			max: 64
			required: true
		view:
			columns: 3
			label: 'Code'

	name:
		model:
			required: true
			max: 128
		view:
			columns: 3
			label: 'Name'
			findunique: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['status']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: '{code} - {name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name']

view:
	comment: 'Manage > Dispensing Status'
	find:
		basic: ['code','name']
	grid:
		fields: ['code','name']
		sort: ['code']
	label: 'Dispensing Status'
	open: 'read'
