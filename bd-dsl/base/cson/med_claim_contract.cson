fields:

    contract_type_code:
        model:
            required: true
            min: 2
            max: 2
            source: 'list_med_claim_ecl'
            sourceid: 'code'
            sourcefilter:
                field:
                    'static': 'CN101'
        view:
            columns: 3
            label: 'Type Code'
            reference: 'CN101'
            _meta:
                location: '2300 CN1'
                field: '01'
                path: 'claimInformation.claimContractInformation.contractTypeCode'

    contract_code:
        model:
            min: 1
            max: 50
        view:
            columns: 3
            label: 'Contract Code'
            reference: 'CN104'
            _meta:
                location: '2300 CN1'
                field: '04'
                path: 'claimInformation.claimContractInformation.contractCode'

    contract_version_identifier:
        model:
            min: 1
            max: 30
        view:
            columns: 3
            label: 'Version Identifier'
            reference: 'CN106'
            _meta:
                location: '2300 CN1'
                field: '06'
                path: 'claimInformation.claimContractInformation.contractVersionIdentifier'

    contract_amount:
        model:
            max: 9999999999.99
            min: 0.00
            rounding: 0.01
            type: 'decimal'
        view:
            columns: -3
            class: 'numeral money'
            format: '$0,0.00'
            label: 'Amount'
            reference: 'CN102'
            _meta:
                location: '2300 CN1'
                field: '02'
                path: 'claimInformation.claimContractInformation.contractAmount'

    contract_percentage:
        model:
            type: 'decimal'
            max: 9999.99
            min: 0.00
        view:
            columns: 3
            label: 'Contract %'
            class: 'numeral discount'
            format: 'percent'
            reference: 'CN103'
            _meta:
                location: '2300 CN1'
                field: '03'
                path: 'claimInformation.claimContractInformation.contractPercentage'

    terms_discount_percentage:
        model:
            type: 'decimal'
            max: 9999.99
            min: 0.00
        view:
            columns: 3
            label: 'Terms Discount %'
            class: 'numeral discount'
            format: 'percent'
            reference: 'CN105'
            _meta:
                location: '2300 CN1'
                field: '05'
                path: 'claimInformation.claimContractInformation.termsDiscountPercentage'

model:
    access:
        create:     []
        create_all: ['admin', 'csr', 'pharm']
        delete:     ['admin', 'pharm']
        read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
        read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
        request:    []
        update:     []
        update_all: ['admin', 'csr', 'pharm']
        write:      ['admin', 'csr', 'pharm']
    name:['contract_type_code','contract_code','contract_amount']
    sections:
        'Contract':
            hide_header: true
            fields: ['contract_type_code', 'contract_code', 'contract_version_identifier',
            'contract_amount', 'contract_percentage', 'terms_discount_percentage']

view:
    dimensions:
        width: '85%'
        height: '55%'
    hide_cardmenu: true
    reference: '2300'
    comment: 'Contract'
    hide_header: true
    grid:
        fields: ['contract_type_code', 'contract_amount', 'contract_percentage', 'terms_discount_percentage']
        width: [25, 25, 25, 25]
        sort: ['-created_on']
    label: 'Contract'
    open: 'read'