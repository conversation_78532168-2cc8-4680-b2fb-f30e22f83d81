fields:

	code:
		model:
			max: 64
			required: true
		view:
			columns: 3
			label: 'Code'
			transform: [
					name: 'LowerCase'
			]

	name:
		model:
			required: true
			max: 128
		view:
			columns: 3
			label: 'Name'
			findunique: true

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 3
			control: 'radio'
			label: 'Can Sync Record'

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 3
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['lists']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['code']
		]
	name: ['name']
	sections:
		'Billing Note Subject':
			fields: ['code', 'name', 'allow_sync', 'active']

view:
	comment: 'Manage > Billing Note Subject'
	find:
		basic: ['code','name']
	grid:
		fields: ['code','name']
		sort: ['code']
	label: 'Billing Note Subject'
	open: 'read'
