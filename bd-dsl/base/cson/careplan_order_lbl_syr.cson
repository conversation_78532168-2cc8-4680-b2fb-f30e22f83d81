fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			offscreen: true
			readonly: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'line1_pt': '{{pr.firstname}} {{pr.lastname}}',
			]

	physician_id:
		model:
			source: 'physician'
		view:
			label: 'Referring Physician'
			offscreen: true
			readonly: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/physician/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'line1_doc': '{{pr.first}} {{pr.last}} {{pr.title}}'
			]

	external_id:
		view:
			label: 'External Id'
			readonly: true

	line1_pt:
		model:
			required: false
			max: 23
		view:
			label: '1.'
			class: 'label-line groups top'
			columns: 2

	line1_doc:
		model:
			required: false
			max: 17
		view:
			label: '1. Doctor'
			class: 'no-label label-line groups top'
			columns: 2

	line3_rx:
		view:
			label: '2.'
			class: 'label-line groups middle'
			columns: 2
			readonly: true

	line3_date:
		model:
			type: 'date'
		view:
			label: '2. Date'
			class: 'no-label label-line groups middle'
			columns: 2
			template: '{{now}}'

	#LABELS.line9
	line9:
		view:
			label: '3.'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 41
					next_field: 'line21'
			]

	#LABELS.line21
	line21:
		view:
			label: '4.'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 50
					next_field: 'line22'
			]

	#LABELS.line22
	line22:
		model:
			max: 50
		view:
			label: '5.'
			class: 'label-line groups middle'

	#LABELS.storage
	storage_id:
		model:
			source: 'list_storage'
			sourceid: 'code'
		view:
			label: '6. Storage'
			class: 'label-line groups middle'
			columns: 2

	#LABELS.expdate
	exp_date:
		model:
			type: 'date'
		view:
			label: '7. Use By Date'
			columns: 2
			class: 'label-line groups middle'

	inits:
		model:
			source: 'list_rph_labels'
			sourceid: 'code'
		view:
			columns: 2
			label: 'RPH Initials'
			class: 'label-line groups bottom'
			validate: [
				{
					name: 'RphPromptForPassword'
				}
			]

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['patient_id']
	sections:
		'Syringe Label':
			hide_header: true
			indent: false
			fields: ['patient_id', 'physician_id', 'line1_pt', 'line1_doc', 'line3_rx', 'line3_date',
			'line9', 'line21', 'line22', 'storage_id', 'exp_date', 'inits']

view:
	comment: 'Manage > Syringe Label'
	find:
		basic: ['patient_id']
	grid:
		fields: ['patient_id']
		sort: ['created_on']
	label: 'Syringe Label'
