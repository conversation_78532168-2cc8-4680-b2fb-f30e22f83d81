fields:

	other_payer_service_facility_location_secondary_identifier:
		model:
			required: false
			multi: true
			source: 'med_claim_ofac_id'
			type: 'subform'
		view:
			note: 'Max 3'
			label: 'Identifier(s)'
			grid:
				add: 'inline'
				edit: true
				fields: ['qualifier', 'identifier']
				label: ['Qualifier', 'ID']
				width: [50, 50]
			max_count: 3

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	sections_group: [
		'Service Facility':
			hide_header: true
			sections: [
				'Secondary Identifier(s)':
					hide_header: true
					indent: false
					fields: ['other_payer_service_facility_location_secondary_identifier']
			]
	]
	name: ['id','created_by','created_on']
view:
	dimensions:
		width: '45%'
		height: '45%'
	hide_cardmenu: true
	reference: '2330E'
	comment: 'Service Facility'
	grid:
		fields: ['created_on', 'created_by']
		sort: ['-created_on']
	label: 'Service Facility'
	open: 'read'