fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_hfqol.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	swelling:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['swelling_rating']
		view:
			control: 'radio'
			label: 'Causing swelling in the patient’s ankles or legs?'

	swelling_rating:
		model:
			required: true
			source: ['0', '1', '2', '3', '4', '5']
		view:
			control: 'radio'
			note: '0 being No/Does not apply and 5 being very much'
			label: 'how much the patient’s life was affected?'

	rest:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['rest_rating']
		view:
			control: 'radio'
			label: 'Making the patient sit or lie down to rest during the day?'

	rest_rating:
		model:
			required: true
			source: ['0', '1', '2', '3', '4', '5']
		view:
			control: 'radio'
			note: '0 being No/Does not apply and 5 being very much'
			label: 'how much the patient’s life was affected?'

	climbing:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['climbing_rating']
		view:
			control: 'radio'
			label: 'Making the patient walking about or climbing stairs difficult?'

	climbing_rating:
		model:
			required: true
			source: ['0', '1', '2', '3', '4', '5']
		view:
			control: 'radio'
			note: '0 being No/Does not apply and 5 being very much'
			label: 'how much the patient’s life was affected?'

	sleeping:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['sleeping_rating']
		view:
			control: 'radio'
			label: 'Making the patient sleeping well at night difficult?'

	sleeping_rating:
		model:
			required: true
			source: ['0', '1', '2', '3', '4', '5']
		view:
			control: 'radio'
			note: '0 being No/Does not apply and 5 being very much'
			label: 'how much the patient’s life was affected?'

	breathing:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['breathing_rating']
		view:
			control: 'radio'
			label: 'Making the patient short of breath?'

	breathing_rating:
		model:
			required: true
			source: ['0', '1', '2', '3', '4', '5']
		view:
			control: 'radio'
			note: '0 being No/Does not apply and 5 being very much'
			label: 'how much the patient’s life was affected?'

	tired:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['tired_rating']
		view:
			control: 'radio'
			label: 'Making the patient tired, fatigued, or low on energy?'

	tired_rating:
		model:
			required: true
			source: ['0', '1', '2', '3', '4', '5']
		view:
			control: 'radio'
			note: '0 being No/Does not apply and 5 being very much'
			label: 'how much the patient’s life was affected?'

	hospital:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hospital_rating']
		view:
			control: 'radio'
			label: 'Making the patient stay in a hospital?'

	hospital_rating:
		model:
			required: true
			source: ['0', '1', '2', '3', '4', '5']
		view:
			control: 'radio'
			note: '0 being No/Does not apply and 5 being very much'
			label: 'how much the patient’s life was affected?'

	side_effects:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['side_effects_rating']
		view:
			control: 'radio'
			label: 'Giving the patient side effects from treatments?'

	side_effects_rating:
		model:
			required: true
			source: ['0', '1', '2', '3', '4', '5']
		view:
			control: 'radio'
			note: '0 being No/Does not apply and 5 being very much'
			label: 'how much the patient’s life was affected?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_hfqol:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Heart Failure Quality Of Life Participation':
			fields: ['last_assessment_date', 'assessment_date']
		'Questionnaire':
			note: 'Did the patient’s HF prevent him/her from living as he/she wanted during the past month by'
			fields: ['swelling', 'swelling_rating', 'rest', 'rest_rating',
			'climbing', 'climbing_rating', 'sleeping', 'sleeping_rating',
			'tired', 'tired_rating', 'hospital', 'hospital_rating',
			'side_effects', 'side_effects_rating']
			prefill: 'clinical_hfqol'
view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Heart Failure Quality Of Life'
	grid:
		fields: ['created_on', 'created_by']
		sort: ['-id']
	label: 'Heart Failure Quality Of Life'
