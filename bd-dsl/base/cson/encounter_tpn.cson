fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	# Environment
	env_tpn_store:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Refrigerator storage appropriate to store TPN and additives?'
			columns: 2

	env_tpn_clean:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Clean workspace to prepare TPN for administration?'
			columns: 2

	# TPN Administration
	caregiver_demo_tpn:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['caregiver_add_train']
		view:
			control: 'radio'
			label: 'Patient/caregiver able to return demonstrate proper TPN preparation, tubing attachment and infusion pump operation?'
			columns: 2

	caregiver_add_train:
		model:
			required: true
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['caregiver_add_train_comment']
		view:
			control: 'radio'
			label: 'Will patient/caregiver need additional training?'
			columns: 2

	caregiver_add_train_comment:
		model:
			required: true
		view:
			control: 'area'
			label: 'Describe additional education requirement to be independent with TPN administration and care?'
			columns: 2

	# TPN Additives
	drug1_given:
		model:
			required: true
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Was Multivitamin Additive added to TPN bag?'
			columns: 2

	drug1_comment:
		view:
			label: 'Multivitamin Additive comment'
			columns: 2

	drug2_given:
		model:
			required: true
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Was Famotidine Additive added to TPN bag?'
			columns: 2

	drug2_comment:
		view:
			label: 'Famotidine Additive comment'
			columns: 2

	drug3_given:
		model:
			required: true
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Was Insulin-Regular Additive added to TPN bag?'
			columns: 2

	drug3_comment:
		view:
			label: 'Insulin-Regular Additive comment'
			columns: 2

	drug4_given:
		model:
			required: true
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Was Heparin Additive added to TPN bag?'
			columns: 2

	drug4_comment:
		view:
			label: 'Heparin Additive comment'
			columns: 2

	add_drugs_given:
		model:
			required: true
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Was Additional Additives added to TPN bag?'
			columns: 2

	add_drugs_given_comment:
		view:
			label: 'Additional Additives comment'
			columns: 2

	current_glucose:
		model:
			rounding: 0.01
			max: 300
			type: 'decimal'
		view:
			label: 'Current Glucose Levels (mg/dL)'
			columns: 2

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		encounter_tpn:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['careplan_id', 'order_id']
	sections:
		'TPN Environment':
			fields: ['env_tpn_store', 'env_tpn_clean']
			prefill: 'encounter_tpn'
		'TPN Administration':
			fields: ['caregiver_demo_tpn', 'caregiver_add_train', 'caregiver_add_train_comment']
			prefill: 'encounter_tpn'
		'TPN Additives':
			fields: ['drug1_given', 'drug1_comment','drug2_given', 'drug2_comment', 'drug3_given', 'drug3_comment', 'drug4_given', 'drug4_comment', 'add_drugs_given', 'add_drugs_given_comment']
			prefill: 'encounter_tpn'
		'TPN Goal Tracking':
			fields: ['current_glucose']

view:
	comment: 'Patient > Careplan > Encounter > TPN'
	label: 'Patient Encounter: TPN'
