fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'
			findunique: true

	description:
		model:
			required: true
		view:
			label: 'Description'
model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: ['code']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'description']

view:
	comment: 'Manage > HCPC'
	find:
		basic: ['code', 'description']
	grid:
		fields: ['code', 'description']
		sort: ['-code']
	label: 'HCPC'
