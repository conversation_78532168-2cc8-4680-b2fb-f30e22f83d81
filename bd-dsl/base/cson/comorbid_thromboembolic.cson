fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true


	# Demographics
	# TODO: Should also check for age here, only 7-50
	gender:
		model:
			if:
				'Female':
					fields: ['on_birthcon', 'are_preg', 'are_postmeno', 'will_be_pregnant']
			prefill: ['patient']
		view:
			label: 'Sex'
			offscreen: true
			readonly: true

	on_birthcon:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you currently on any birth control or hormone replacement pills?'

	are_preg:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: ' Are you currently pregnant or have given birth in the last 6 weeks?'

	will_be_pregnant:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Do you plan to get pregnant while on this medication?"

	are_postmeno:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: ' Are you postmenopausal?'

	has_htn:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pressure_controlled']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with hypertension (HTN)?'

	pressure_controlled:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your blood pressure currently controlled?'

	high_chol:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['chol_controlled']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with high cholesterol?'

	chol_controlled:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is your cholesterol currently controlled?'

	heart_cond:
		model:
			max: 64
			min: 1
			multi: true
			source: ['Congestive heart failure (CHF)', 'Cardiomyopathy', 'Valve Disease', 'Congenital Defects', 'Atrial Fibrillation', 'Angina', 'Pulmonary disease', 'Other', 'None']
			if:
				'Other':
					fields: ['heart_cond_other']
		view:
			control: 'checkbox'
			label: 'Do you suffer from any of the following heart diseases?'

	heart_cond_other:
		view:
			control: 'area'
			label: 'Other Heart Diseases'

	fam_hist:
		model:
			multi: true
			source: ['Coronary Artery Disease (CAD/Atherosclerotic)', 'Angina', 'Deep Vein Thrombosis (DVT)', 'Cerebral Infarction'
				, 'Myocardial Infarction', 'Other', 'None']
			if:
				'Other':
					fields: ['fam_hist_other']
		view:
			control: 'checkbox'
			label: 'Do you or any of your Parents or siblings have a history of the following:'

	fam_hist_other:
		view:
			control: 'area'
			label: 'Other Family History'

	immobil_periods:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you experience prolonged periods of immobilization?'

	diag_thomb:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you been diagnosed with thrombophilia?'

	clam_hands:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do your hands/extremities feel cold and clammy (experience low BP)?'

	had_sepsis:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any recent cases of sepsis or infection in blood?'

	had_can:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_can_details']
		view:
			control: 'radio'
			label: 'Are you currently or have you recently been on cancer treatment?'

	had_can_details:
		model:
			required: true
		view:
			label: 'What type of cancer and when?'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	name: ['careplan_id', 'order_id']
	sections:
		'Thromboembolic Risk Assessment':
			fields: ['has_htn', 'pressure_controlled', 'high_chol', 'chol_controlled',
			'heart_cond', 'heart_cond_other', 'fam_hist', 'fam_hist_other',
			'immobil_periods', 'diag_thomb', 'clam_hands', 'had_sepsis', 'had_can', 'had_can_details',
			'on_birthcon', 'are_preg', 'are_postmeno', 'will_be_pregnant']

view:
	comment: 'Comorbid Condition > Thromboembolic Risk'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Thromboembolic Risk'
	open: 'read'
