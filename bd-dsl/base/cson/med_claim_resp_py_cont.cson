fields:

	contact_name:
		view:
			columns: 4
			label: 'Contact Name'
			note: 'PER PER02'
			readonly: true

	electronic_data_inter_change_access_number:
		view:
			columns: 4
			label: 'EDI #'
			note: 'PER04-06-08 PER03-05-7=ED'
			readonly: true

	email:
		view:
			columns: 3
			label: 'Email'
			note: 'PER04-06 PER03-05 = EM'
			readonly: true

	fax:
		view:
			columns: -3
			label: 'Fax'
			format: 'us_phone'
			note: 'PER04-06 PER03-05 = FX'
			readonly: true

	phone:
		view:
			columns: 3
			label: 'Phone'
			format: 'us_phone'
			note: 'PER04-06 PER03-05 = TE'
			readonly: true

	phone_extension:
		view:
			columns: 3
			label: 'Phone Extension'
			note: 'PER06-08 PER05-07 = EX'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: 'Payer Status Response Contact Information'
	sections:
		'Contact Information':
			hide_header: true
			fields: ['electronic_data_inter_change_access_number', 'email', 'fax', 'phone', 'phone_extension']

view:
	dimensions:
		width: '65%'
		height: '55%'
	hide_cardmenu: true
	comment: 'Payer Status Response Contact Information'
	grid:
		fields: ['contact_name', 'email', 'fax', 'phone', 'phone_extension']
		label: ['Name', 'Email', 'Fax', 'Phone', 'Phone Extension']
		width: [25, 20, 20, 20, 15]
		sort: ['-created_on']
	label: 'Payer Status Response Contact Information'
	open: 'read'