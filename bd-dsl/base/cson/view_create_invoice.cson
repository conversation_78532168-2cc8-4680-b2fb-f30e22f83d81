fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			offscreen: true
			readonly: true
			label: 'Patient ID'

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			readonly: true
			offscreen: true

	ticket_no:
		model:
			type: 'text'
		view:
			label: 'Ticket #'
			readonly: true
			offscreen: true

	calc_invoice_split_no:
		model:
			required: false
			type: 'text'
		view:
			label: 'Split Invoice #'
			readonly: true
			offscreen: true

	insurance_id:
		model:
			source: 'patient_insurance'
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			control: 'select'
			label: 'Insurance'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_insurance/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'payer_id': ['payer_id']
					'billing_method_id': ['billing_method_id']
			]

	payer_id:
		model:
			source: 'payer'
			required: true
		view:
			label: 'Payer'
			readonly: true
			offscreen: true

	billing_method_id:
		model:
			required: true
			source: 'list_billing_method'
			sourceid: 'code'
			if:
				'generic':
					fields: ['generic_charge_line']
				'mm':
					fields: ['mm_charge_line']
				'cms1500':
					fields: ['mm_charge_line']
				'ncpdp':
					fields: ['charge_line']
		view:
			label: 'Billing Method'
			readonly: true
			offscreen: true

	add_new_type_filter:
		model:
			required: true
			source: ['Billable']
			default: ['Billable']
		view:
			label: 'Add New Type Filter'
			readonly: true
			offscreen: true

	generic_charge_line:
		model:
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				calc_invoice_split_no:
					'dynamic': '{calc_invoice_split_no}'
				ticket_no:
					'dynamic': '{ticket_no}'
				invoice_no:
					'static': 'null'
				void:
					'static': '!Yes'
		view:
			embed:
				form: 'ledger_charge_line'
				selectable: false
				add_preset:
					invoice_status: '{invoice_status}'
					invoice_no: '{invoice_no}'
					ticket_no: '{ticket_no}'
					order_rx_id: '{site_id}'
					insurance_id: '{insurance_id}'
					payer_id: 1
					billing_method_id: 'generic'
					patient_id: '{patient_id}'
					calc_invoice_split_no: '{calc_invoice_split_no}'
			grid:
				edit: true
				rank: 'none'
				add: 'none'
				label: ['Quantity', 'Description', 'Total Price $', 'Expected $']
				fields: ['bill_quantity', 'description', 'gross_amount_due', 'expected']
				width: [10, 55, 15, 15]
			label: 'Charge Lines'

	mm_charge_line:
		model:
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				calc_invoice_split_no:
					'dynamic': '{calc_invoice_split_no}'
				ticket_no:
					'dynamic': '{ticket_no}'
				invoice_no:
					'static': 'null'
				void:
					'static': '!Yes'
		view:
			embed:
				form: 'ledger_charge_line'
				selectable: false
				add_preset:
					claim_no: '{claim_no}'
					invoice_status: '{invoice_status}'
					invoice_no: '{invoice_no}'
					ticket_no: '{ticket_no}'
					order_rx_id: '{site_id}'
					insurance_id: '{insurance_id}'
					payer_id: '{payer_id}'
					billing_method_id: '{billing_method_id}'
					patient_id: '{patient_id}'
					calc_invoice_split_no: '{calc_invoice_split_no}'
					is_dirty: 'Yes'
					inventory_type_filter: '{add_new_type_filter}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				label: ['Item', 'Quantity', 'Exp $', 'Gross Amt $', 'Cost $', 'HCPC']
				fields: ['inventory_id', 'bill_quantity', 'expected', 'gross_amount_due', 'total_cost', 'hcpc_code']
				width: [35, 10, 15, 15, 10, 15]
			label: 'Charge Lines'
			max_count: 50
			note: 'Max 50'

	charge_line:
		model:
			required: true
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				calc_invoice_split_no:
					'dynamic': '{calc_invoice_split_no}'
				ticket_no:
					'dynamic': '{ticket_no}'
				invoice_no:
					'static': 'null'
				void:
					'static': '!Yes'
		view:
			embed:
				form: 'ledger_charge_line'
				selectable: true
				add_preset:
					calc_invoice_split_no: '{calc_invoice_split_no}'
					ticket_no: '{ticket_no}'
					site_id: '{site_id}'
					insurance_id: '{insurance_id}'
					payer_id: '{payer_id}'
					billing_method_id: '{billing_method_id}'
					patient_id: '{patient_id}'
					is_dirty: 'Yes'
					inventory_type_filter: '{add_new_type_filter}'
			grid:
				edit: true
				selectall: true
				rank: 'none'
				add: 'none'
				label: ['Item', 'Quantity', 'Exp $', 'Gross Amt $', 'Cost $', 'NDC']
				fields: ['inventory_id', 'bill_quantity', 'expected', 'gross_amount_due', 'total_cost', 'formatted_ndc']
				width: [35, 10, 15, 15, 10, 15]
			label: 'Selected Open Ticket Charges'

	other_charge_line:
		model:
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				calc_invoice_split_no:
					'dynamic': '!{calc_invoice_split_no}'
				ticket_no:
					'dynamic': '{ticket_no}'
				invoice_no:
					'static': 'null'
				void:
					'static': '!Yes'
		view:
			embed:
				form: 'ledger_charge_line'
				selectable: true
			grid:
				edit: false
				selectall: false
				rank: 'none'
				add: 'none'
				label: ['Item', 'Payer', 'Quantity', 'Exp $', 'NDC']
				fields: ['inventory_id', 'payer_id', 'bill_quantity', 'expected', 'formatted_ndc']
				width: [35, 25, 10, 15, 15]
			label: 'Open Delivery Ticket Charges'

model:
	save: false
	access:
		create:     []
		create_all: ['admin','pharm', 'cm']
		delete:     []
		read:       ['admin','pharm', 'cm']
		read_all:   ['admin','pharm', 'cm']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      ['admin','pharm', 'cm']

	name: ['patient_id', 'site_id']
	sections_group: [
		'Create Invoice':
			hide_header: true
			indent: false
			fields: ['patient_id', 'site_id', 'ticket_no', 'calc_invoice_split_no', 'insurance_id', 'payer_id', 'billing_method_id', 'add_new_type_filter']
		'Selected Charge Lines':
			hide_header: true
			indent: false
			fields: ['charge_line']
		'Open DeliveryTicket Charges':
			hide_header: true
			indent: false
			fields: ['other_charge_line']
	]

view:
	dimensions:
		width: '80%'
		height: '75%'
	hide_cardmenu: true
	comment: 'Create Invoice'
	grid:
		fields: ['patient_id', 'site_id']
		sort: ['-id']
	label: 'Create Invoice'
	open: 'edit'