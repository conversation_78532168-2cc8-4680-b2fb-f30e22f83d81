fields:

	batch_no:
		view:
			label: 'Batch No'
			columns: 4
			readonly: true 

	invoice_id:
		model:
			source: 'billing_invoice'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
		view:
			label: 'Invoice ID'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			source: 'inventory'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
		view:
			label: 'Inventory ID'
			readonly: true
			offscreen: true

	ticket_no:
		model:
			type: 'text'
		view:
			label: 'Ticket #'
			readonly: true

	ticket_item_no:
		model:
			type: 'text'
		view:
			label: 'Ticket Item #'
			readonly: true

	invoice_no:
		model:
			type: 'text'
		view:
			label: 'Invoice #'
			readonly: true
			columns: 4

	charge_line_id:
		model:
			source: 'ledger_charge_line'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
		view:
			label: 'Charge Line ID'
			readonly: true
			offscreen: true

	wt_pulled_id:
		model:
			source: 'careplan_dt_wt_pulled'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
		view:
			label: 'WT Pulled ID'
			readonly: true
			offscreen: true

	charge_no:
		model:
			type: 'text'
		view:
			label: 'Charge #'
			readonly: true
			columns: 4

	ledger_inventory_id:
		model:
			source: 'ledger_inventory'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
		view:
			label: 'Inventory ID'
			readonly: true
			offscreen: true

	ledger_lot_id:
		model:
			source: 'ledger_lot'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
		view:
			label: 'Lot ID'
			readonly: true
			offscreen: true

	ledger_serial_id:
		model:
			source: 'ledger_serial'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
		view:
			label: 'Serial ID'
			readonly: true
			offscreen: true

	quantity:
		model:
			rounding: 1
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Quantity'
			readonly: true

	po_id:
		model:
			required: false
			source: 'po'
		view:
			label: 'PO'
			readonly: true

	site_id:
		model:
			source: 'site'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
		view:
			label: 'Site ID'
			readonly: true

	account_id:
		model:
			source: 'billing_account'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
		view:
			columns: 2
			label: 'Account'
			readonly: true

	check_no:
		view:
			columns: 4
			label: 'Check Number(s)'
			readonly: true 

	source_id:
		model:
			type: 'int'
			required: false
		view:
			readonly: true
			offscreen: true

	source_form:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	transaction_datetime:
		model:
			type: 'datetime'
		view:
			columns: 2
			label: 'Transaction Date/Time'
			readonly: true

	reversal_for_id:
		model:
			type: 'int'
			source: 'ledger_finance'
			transform: [
				name: 'AutoNameFk'
				]
		view:
			label: 'Reversal For'
			readonly: true

	post_datetime:
		model:
			type: 'datetime'
		view:
			columns: 2
			label: 'Post Date/Time'
			readonly: true

	transaction_type:
		model:
			required: true
			source: ['Adjustment', 'Adjustment Reversal', 'Chargeback', 'Chargeback Reversal', 
			'Refund', 'Refund Reversal', 'Recall', 'Recall Reversal',
			'Purchase', 'Purchase Reversal', 'Transfer', 'Transfer Reversal',
			'Posting', 'Posting Reversal', 'Writeoff', 'Writeoff Reversal',
			'Cash Allocation', 'Cash Allocation Reversal',
			'Dispense', 'Dispense Reversal', 'Cost Recognition',
			'Inventory Writeoff', 'Inventory Writeoff Reversal']	
			if:
				'Adjustment':
					fields: ['adjustment_reason_id']
				'Adjustment Reversal':
					fields: ['adjustment_reason_id']
				'Writeoff':
					fields: ['writeoff_reason_id']
				'Writeoff Reversal':
					fields: ['writeoff_reason_id']
		view:
			columns: 2
			label: 'Transaction Type'
			readonly: true

	adjustment_reason_id:
		model:
			source: 'list_adjustment_reason'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Adjustment Reason'
			readonly: true 

	writeoff_reason_id:
		model:
			source: 'list_writeoff_reason'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Writeoff Reason'
			readonly: true 

	account_type:
		model:
			required: true
			source: ['AR', 'Purchases Clearing', 'Revenue',
			'Contra-Revenue', 'Expense', 'Liabilities',
			'Cash', 'Unapplied Cash', 'COGS', 'Inventory']
			if:
				'COGS':
					fields: ['inventory_id']
				'Inventory':
					fields: ['inventory_id']
		view:
			columns: 2
			label: 'Account Type'
			readonly: true

	credit:
		model:
			required: true
			type: 'decimal'
		view:
			columns: 2
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Credit'
			readonly: true

	debit:
		model:
			required: true
			type: 'decimal'
		view:
		
			columns: 2
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Debit'
			readonly: true

	notes:
		model:
			required: false
		view:
			label: 'Notes'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      []
	bundle: ['audit']
	indexes:
		many: [
			['invoice_id'],
			['charge_line_id'],
			['account_id'],
			['source_id'],
			['source_form'],
			['post_datetime'],
			['account_type'],
			['transaction_type'],
			['credit'],
			['debit'],
			['invoice_no'],
			['charge_no'],
			['adjustment_reason_id'],
			['writeoff_reason_id'],
			['ticket_item_no'],
			['ticket_no'],
			['wt_pulled_id'],
			['ledger_inventory_id'],
			['ledger_lot_id'],
			['ledger_serial_id'],
			['quantity'],
			['po_id'],
			['reversal_for_id'],
			['inventory_id']
		]
	name: ['account_id', 'credit', 'debit']
	sections_group: [
			'Account Details':
				fields: ['account_id', 'account_type', 'site_id']
			'Transaction Details':
				fields: ['invoice_id', 'invoice_no', 'charge_no', 'post_datetime', 'transaction_datetime', 'transaction_type']
			'COGs/Inventory':
				fields: ['po_id', 'inventory_id', 'ledger_inventory_id', 'ledger_lot_id', 'ledger_serial_id', 'quantity']
			'Amounts':
				fields: ['credit', 'debit']
			'Notes':
				fields: ['notes']
	]

view:
	hide_cardmenu: true
	comment: 'Accounting Ledger'
	find:
		basic: ['account_id']
	grid:
		fields: ['account_id', 'account_type', 'post_datetime', 'transaction_type', 'post_datetime', 'credit', 'debit']
		width: [30, 20, 20, 20, 20, 10, 10]
		sort: ['-id']
	label: 'Accounting Ledger'
	open: 'read'