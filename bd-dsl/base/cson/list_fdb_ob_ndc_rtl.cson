#TABLE: ROBCNDC0_OBC_NDC
fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'
			columns: 4

	#ndc
	ndc:
		model:
			type: 'int'
		view:
			label: 'NDC'
			findunique: true
			columns: 4

	obc3:
		view:
			label: 'Orange Book Code'
			findunique: true
			columns: 4

	gcn:
		model:
			type: 'int'
		view:
			label: 'Formulation ID'
			readonly: true
			columns: 4

	gcn_seqno:
		model:
			type: 'int'
		view:
			label: 'Clinical Formulation ID (Stable ID)'
			readonly: true
			columns: 4

	gti:
		model:
			type: 'text'
		view:
			label: 'Therapeutic Equivalence'
			readonly: true
			columns: 4

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['ndc']
	indexes:
		many: [
			['ndc']
			['obc3']
			['gcn']
			['gcn_seqno']
			['gti']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['ndc', 'obc3', 'gcn', 'gcn_seqno', 'gti']

view:
	comment: 'Manage > List FDB Orange Book NDC Relationship Table'
	find:
		basic: ['ndc', 'obc3', 'gcn', 'gcn_seqno', 'gti']
	grid:
		fields: ['ndc', 'obc3', 'gcn', 'gcn_seqno', 'gti']
	label: 'List FDB Orange Book NDC Relationship Table'
