fields:

	send_cert:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['certification_type_code', 'durable_medical_equipment_duration_in_months']
		view:
			columns: 4
			control: 'checkbox'
			label: 'Send DME Cert?'
			class: 'checkbox-only'
			reference: 'CRC02'

	certification_type_code:
		model:
			default: 'I'
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CR301'
		view:
			columns: 2
			label: 'Certification Type Code'
			reference: 'CR301'
			_meta:
				location: '2400 CR3'
				field: '01'
				path: 'claimInformation.serviceLines[{idx1-50}].durableMedicalEquipmentCertification.certificationTypeCode'

	durable_medical_equipment_duration_in_months:
		model:
			required: true
			max: 999999999999999
			min: 1
			rounding: 1
			type: 'decimal'
		view:
			columns: 4
			label: 'Duration'
			note: 'In Months'
			reference: 'CR303 CR302=MO'
			_meta:
				location: '2400 CR3'
				field: '03'
				path: 'claimInformation.serviceLines[{idx1-50}].durableMedicalEquipmentCertification.durableMedicalEquipmentDurationInMonths'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['certification_type_code','send_cert','durable_medical_equipment_duration_in_months']
	sections:
		'DME Certification':
			hide_header: true
			fields: ['send_cert', 'durable_medical_equipment_duration_in_months', 'certification_type_code']

view:
	dimensions:
		width: '85%'
		height: '45%'
	hide_cardmenu: true
	reference: '2400'
	comment: 'DME Certification'
	grid:
		fields: ['certification_type_code', 'durable_medical_equipment_duration_in_months']
		sort: ['-created_on']
	label: 'DME Certification'
	open: 'read'