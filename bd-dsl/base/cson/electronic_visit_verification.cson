fields:
	user_id:
		model:
			max: 32
			required: true
			source: 'user'
		view:
			label: 'User Id'

	patient_id:
		model:
			max: 32
			required: true
			source: 'patient'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Patient Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	firstname:
		model:
			max: 32
			min: 1
			required: true
		view:
			label: 'First Name'
			note: 'Letters, numbers, comma, \', -, . only'
			validate: [{
					name: 'UpdateTabLabel'
					fields: ['firstname', 'middlename', 'lastname']
					},
					{
						name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
					}
			]

	lastname:
		model:
			max: 32
			min: 1
			required: true
		view:
			label: 'Last Name'
			note: 'Letters, numbers, \', -, . only'
			validate: [{
					name: 'UpdateTabLabel'
					fields: ['firstname', 'middlename', 'lastname']
					},
					{
						name: '<PERSON>Valida<PERSON>'
					}
			]
	middlename:
		model:
			max: 32
			min: 0
		view:
			label: 'Middle Name'
			note: 'Letters, numbers, \', -, . only'
			validate: [{
					name: 'UpdateTabLabel'
					fields: ['firstname', 'middlename', 'lastname']
					},
					{
						name: 'Name<PERSON><PERSON>da<PERSON>'
					}
			]

	visit_start_time:
		model:
			type: 'time'
			required: true
		view:
			label: 'Start Time'
			template: '{{now}}'

	visit_end_time:
		model:
			type: 'time'
			required: true
		view:
			label: 'End Time'
			template: '{{now}}'

	geo_location:
		model:
			type: 'json'
			required: true
		view:
			control: 'esign'
			label: 'Geo Location'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'pharm']
		request:    ['patient']
		update:     ['nurse', 'patient']
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	indexes:
		fulltext: ['firstname', 'lastname', 'middlename']

	name: ['firstname', 'lastname', 'user_id']
	sections_group: [
		'Demographics':
			sections: [
				'Name':
					fields: ['firstname', 'lastname', 'middlename','visit_start_time','visit_end_time','geo_location']
			]
	]

view:
	comment: 'Electronic Visit Verification'
	find:
		basic: ['lastname', 'firstname']
	grid:
		fields: ['lastname', 'firstname']
		sort: ['lastname', 'firstname']
	label: 'Electronic Visit Verification'
	open: 'read'
