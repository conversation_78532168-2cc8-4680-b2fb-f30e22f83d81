fields:
	site_id:
		model:
			type: 'int'
			required: true
			source: 'site'
		view:
			columns: 2
			label: 'Site'
			readonly: true

	provider_id_qualifier:
		model:
			default: '05'
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '465-EY'
			if:
				'*':
					require_fields: ['provider_id']
		view:
			columns: -2
			reference: '465-EY'
			note: '465-EY'
			label: 'Pharm ID Qual'

	provider_id:
		model:
			max: 15
			if:
				'*':
					require_fields: ['provider_id_qualifier']
					prefill:
						provider_id_qualifier: '05'
		view:
			columns: 4
			reference: '444-E9'
			note: '444-E9'
			label: 'Pharm ID'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['site_id', 'provider_id', 'provider_id_qualifier']
	sections:
		'Pharmacy Provider':
			hide_header: true
			indent: false
			fields: ['site_id', 'provider_id_qualifier', 'provider_id']

view:
	hide_cardmenu: true
	comment: 'Pharmacy Provider'
	grid:
		fields: ['site_id', 'provider_id_qualifier', 'provider_id']
		sort: ['-created_on']
	label: 'Pharmacy Provider'
	open: 'read'