fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	gender:
		model:
			prefill: ['patient']
			if:
				'Female':
					fields: ['will_be_pregnant']
		view:
			label: 'Gender'
			offscreen: true
			readonly: true

	# Assessment Questions
	immuno_drugs:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['immuno_drugs_details']
		view:
			control: 'radio'
			label: 'Are you currently taking or plan to start immunosuppressant therapy?'

	immuno_drugs_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Immunosuppressant Drugs Taken'

	have_vaccine:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_vaccine_details']
		view:
			control: 'radio'
			label: "Have you received any recent vaccines or plan to receive any vaccines while receiving TNF-inhibitor therapy?"

	have_vaccine_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Vaccines Detail'

	have_antibiotic:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_antibiotic_details']
		view:
			control: 'radio'
			label: "Are you currently receiving antibiotic therapy or have active infection?"

	have_antibiotic_details:
		model:
			required: true
		view:
			control: 'area'
			label: "Antibiotic details"

	have_exposure:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_exposure_details']
		view:
			control: 'radio'
			label: "Have you had any recent exposure to tuberculosis (TB), HBV, or mycoses?"

	have_exposure_details:
		model:
			required: true
		view:
			control: 'area'
			label: "Exposure details"

	have_seizure:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_seizure_details']
		view:
			control: 'radio'
			label: "Do you have seizure disorder or CNS demyelinating disorder?"

	have_seizure_details:
		model:
			required: true
		view:
			control: 'area'
			label: "Seizure/CNS details"

	have_malignancy:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_malignancy_details']
		view:
			control: 'radio'
			label: "Do you have a history of malignancy or myelosuppression?"

	have_malignancy_details:
		model:
			required: true
		view:
			control: 'area'
			label: "Malignancy or myelosuppression details"

	will_be_pregnant:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Do you plan to get pregnant while on this medication?"

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'TNF-Inhibitor General Assessment':
			fields: ['immuno_drugs', 'immuno_drugs_details', 'have_vaccine',
			'have_vaccine_details', 'have_antibiotic', 'have_antibiotic_details',
			'have_exposure', 'have_exposure_details',
			'have_seizure', 'have_seizure_details', 'have_malignancy',
			'have_malignancy_details','gender', 'will_be_pregnant']
view:
	comment: 'Patient > Careplan > Assessment > TNF-Inhibitor'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Assessment Questionnaire: TNF-Inhibitor'
	open: 'read'
