fields:
	code:
		model:
			type: 'text'
			max: 64
			required: true
		view:
			label: 'Code'
			readonly: true
			columns: 3

	gcn_seqno:
		model:
			type: 'int'
			max: 6
			required: true
		view:
			label: 'Clinical Formulation ID (Stable ID)'
			readonly: true
			columns: 3

	medid:
		model:
			type: 'int'
			max: 8
			required: true
		view:
			label: 'MED Medication ID (Stable ID)'
			readonly: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: '{gcn_seqno} {medid}'
	indexes:
		many: [
			['gcn_seqno']
			['medid']
		]

	sections:
		'Clinical Formulation Link':
			tab: 'Information'
			fields: [
				'code',
				'gcn_seqno',
				'medid'
			]

view:
	comment: 'Manage > List FDB RMIGC1 Medid GCNSeqno Link'
	find:
		basic: ['gcn_seqno', 'medid']
	grid:
		fields: [
			'gcn_seqno',
			'medid'
		]
	label: 'List FDB RMIGC1 Medid GCNSeqno Link'