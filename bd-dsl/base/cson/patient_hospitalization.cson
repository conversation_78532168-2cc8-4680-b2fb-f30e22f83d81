fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	hospital:
		model:
			required: true
		view:
			columns: 3
			label: 'Hospital'

	room:
		view:
			columns: 3
			label: 'Room'

	date:
		model:
			required: true
			type: 'date'
		view:
			columns: 3
			label: 'Hospitalization Date'

	discharge_date:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Discharge date (if known)?'

	note:
		view:
			control: 'area'
			label: 'Additional Notes'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['patient']
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
			['hospital', 'date']
		]
		unique: [
			['patient_id', 'hospital', 'date']
		]
	name: ['patient_id', 'date', 'hospital']
	sections:
		'Hospitalization':
			fields: ['hospital', 'room', 'date', 'discharge_date', 'note']

	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Hospitalization"
	]

view:
	hide_cardmenu: true
	validate: [
		{
			name: "DateOrderValidator"
			fields: [
				"date",
				"discharge_date"
			]
			error: "Discharge Date cannot be lesser than Hospitalization Date"
		}
	]
	comment: 'Patient > Hospitalization'
	find:
		basic: ['hospital']
	grid:
		fields: ['hospital', 'date', 'discharge_date']
		sort: ['-date']
	label: 'Patient Hospitalization'
