fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	disease_1:
		model:
			prefill: ['parent.disease_1']
			if:
				'ra':
					sections: ['TNF-RA Questionnaire', 'TNF-RA Barriers to Compliance Assessment', 'TNF-RA 7 Day Care Calling Participation']
				'psoriasis':
					sections: ['Patient Questionnaire: TNF - Psoriasis']
		view:
			label: 'Primary Disease'
			offscreen: true
			readonly: true

	disease_2:
		model:
			prefill: ['parent.disease_2']
			if:
				'ra':
					sections: ['TNF-RA Questionnaire', 'TNF-RA Barriers to Compliance Assessment', 'TNF-RA 7 Day Care Calling Participation']
				'psoriasis':
					sections: ['Patient Questionnaire: TNF - Psoriasis']
		view:
			label: 'Secondary Disease'
			offscreen: true
			readonly: true

	disease_3:
		model:
			prefill: ['parent.disease_3']
			if:
				'ra':
					sections: ['TNF-RA Questionnaire', 'TNF-RA Barriers to Compliance Assessment', 'TNF-RA 7 Day Care Calling Participation']
				'psoriasis':
					sections: ['Patient Questionnaire: TNF - Psoriasis']
		view:
			label: 'Tertiary Disease'
			offscreen: true
			readonly: true

	disease_4:
		model:
			prefill: ['parent.disease_4']
			if:
				'ra':
					sections: ['TNF-RA Questionnaire', 'TNF-RA Barriers to Compliance Assessment', 'TNF-RA 7 Day Care Calling Participation']
				'psoriasis':
					sections: ['Patient Questionnaire: TNF - Psoriasis']
		view:
			label: 'Quaternary Disease'
			offscreen: true
			readonly: true

	disease_5:
		model:
			prefill: ['parent.disease_5']
			if:
				'ra':
					sections: ['TNF-RA Questionnaire', 'TNF-RA Barriers to Compliance Assessment', 'TNF-RA 7 Day Care Calling Participation']
				'psoriasis':
					sections: ['Patient Questionnaire: TNF - Psoriasis']
		view:
			label: 'Quinary Disease'
			offscreen: true
			readonly: true

	# TNF-RA 7 Day Care Calling
	ra_new_particiates:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['ra_start_take']
		view:
			control: 'radio'
			label: 'Is this a new RA patient start?'

	ra_start_take:
		model:
			max: 32
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['TNF-RA 7 Day Care Calling']
		view:
			control: 'radio'
			label: 'Have you started taking your medication?'

	ra_had_se:
		model:
			multi: true
			source: ['Side effects', 'Trouble remembering', 'Trouble following the directions for use', 'Can’t afford cost of drug', 'Worsening of condition', 'Do not feel medication is working', 'Difficulty with injection', 'Injection site reaction(s)', 'Recent hospitalization', 'None', 'Other']
			if:
				'Side effects':
					fields: ['ra_toxic_rating', 'ra_issue_resolve']
				'Injection site reaction(s)':
					fields: ['ra_toxic_rating', 'ra_issue_resolve']
				'Other':
					fields: ['ra_had_se_other' , 'ra_issue_resolve']
				'*':
					fields: ['ra_issue_resolve']
		view:
			note: 'check all that apply'
			control: 'checkbox'
			label: 'Have you experienced any difficulty with your drug therapy regimen?'

	ra_had_se_other:
		model:
			required: true
		view:
			label: 'Other Difficulties'

	ra_toxic_rating:
		model:
			multi: true
			source: ['1', '2', '3', '4']
		view:
			control: 'radio'
			note: '1 - Mild, 2 - Moderate (no intervention required), 3 - Severe (limits daily activity), 4 - Potentially Life Threatening (ER visit or hospitalization warranted)'
			label: 'Toxicity Rating'

	ra_issue_resolve:
		model:
			required: true
			source: ['Follow up required', 'Resolved with patient', 'No action required', 'Adverse drug event documented & reported', 'Other']
			if:
				'Other':
					fields: ['ra_issue_resolve_other']
		view:
			control: 'select'
			label: 'Problem Resolution'

	ra_issue_resolve_other:
		model:
			required: true
		view:
			control: 'select'
			label: 'Problem Resolution - Other'

	# TNF-RA Questionnaire
	ra_knowledge:
		model:
			source:
				very: 'Very knowledgeable'
				moderate: 'Moderately knowledgeable'
				little: 'Not knowledgeable'
				none: 'No understanding'
		view:
			control: 'radio'
			label: 'How would you rate your understanding of Rheumatoid Arthritis?'

	ra_med_knowledge:
		model:
			source:
				very: 'Very knowledgeable'
				moderate: 'Moderately knowledgeable'
				little: 'Not knowledgeable'
				none: 'No understanding'
		view:
			control: 'radio'
			label: 'How would you rate your understanding of the drug therapy being used to treat RA?'

	ra_med_expect:
		model:
			source: ['Yes', 'No', 'Maybe']
		view:
			control: 'radio'
			label: 'Do you understand what the medications you have been prescribed are supposed to do?'

	ra_med_take:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you know how and when to take the medications that have been prescribed?'

	ra_med_call:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Would you like a call from a pharmacist to discuss your medication therapy?'

	ra_med_comfort:
		model:
			source:
				very: 'Very comfortable'
				moderate: 'Somewhat comfortable'
				little: 'Not comfortable'
				none: 'Afraid of needles'
		view:
			control: 'radio'
			label: 'What is your comfort level with having to inject yourself to administer the medication?'

	ra_med_train:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Would you like additional injection training?'

	ra_depress:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'In the past 30 days, have you felt down, depressed, or hopeless or felt little interest in doing things that you normally enjoy?'

	ra_missed_days:
		model:
			source: ['0', '1', '2', '3', '4 or more']
		view:
			control: 'radio'
			label: 'In the last 30 days, how many days from work have you missed because of your condition?'

	ra_status:
		model:
			source: ['Better', 'Worse', 'Same']
		view:
			control: 'radio'
			label: 'How would you describe your Rheumatoid Arthritis?'

	# TNF-RA Barriers to Compliance Assessment
	ra_comp_rem:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Trouble remembering?'

	ra_comp_follow:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Trouble following directions for use?'

	ra_comp_se:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Side effects?'

	ra_comp_site:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Injection site reactions?'

	ra_comp_cost:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Cannot afford cost of drug (i.e. copay)?'

	ra_comp_worse:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Worsening of condition?'

	ra_comp_feel:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do not feel medication is working?'

	ra_comp_hosp:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Recent hospitalization?'

	ra_comp_other:
		view:
			label: 'Other Reason'

	palm_cover_cnt:
		model:
			type: 'int'
			min: 1
			max: 100
		view:
			label: 'If you had to take the palm of your hand and cover up all of the patches of psoriasis on your body today, how many palms of your hand do you think that it would take?'
			note: 'One palm of your hand is equal to about 1% of your body surface area (BSA). If your psoriasis is only scattered small dots, try to imagine combining them together into one patch. Please remember to include your scalp and back if affected. Do not include areas in which psoriasis has faded, leaving only changes in the color of the skin.'

	# follow-up
	have_fever:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient reporting fever of 100.5 F (38 C) or higher?'

	infection:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['infection_symptoms']
		view:
			control: 'radio'
			label: 'Is patient reporting any signs or symptoms of an infection?'

	infection_symptoms:
		model:
			source: ['Fever/Chills', 'Sore Throat', 'Mouth Sores', 'Cough', 'Changes in amount of sputum or color', 'Sinus Pain', 'Ear Pain',
			'Non-healing Wound', 'Pain when urinating','Anal itching or pain', 'Other']
			multi: true
			required: true
			if:
				'Other':
					fields: ['infection_symptoms_other']
		view:
			control: 'checkbox'
			label: 'Infection Symptoms Reported'

	infection_symptoms_other:
		model:
			required: true
		view:
			label: 'Infection Symptoms Reported Other'

	had_infection:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had an infection or received antibiotic treatment since your last TNF-inhibitor treatment?'

	pt_report_rash:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient reporting lupus like rash on face or other parts of the body?'

	pt_report_pain:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient reporting muscle or joint pain, shortness of breath or chest pain, or swelling of the arms or legs?'

	pt_report_dizzy:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient reporting any changes in eyesight, seizures, dizziness or passing out?'

	pt_report_wt_gain:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient reporting large weight gain?'

	pt_report_stool:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is patient reporting black, tarry, or bloody stools?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	name: ['patient_id', 'careplan_id']
	bundle: ['patient', 'careplan']
	indexes:
		many: [
			['careplan_id']
			['patient_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		ongoing_tnf:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
		assessment_tnf:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Therapy Details':
			fields: ['disease_1', 'disease_2', 'disease_3', 'disease_4', 'disease_5']
		'TNF-RA 7 Day Care Calling Participation':
			fields: ['ra_new_particiates', 'ra_start_take']
		'TNF-RA 7 Day Care Calling':
			fields: ['ra_had_se', 'ra_had_se_other', 'ra_toxic_rating', 'ra_issue_resolve', 'ra_issue_resolve_other']
		'TNF-RA Questionnaire':
			fields: ['ra_knowledge', 'ra_med_knowledge', 'ra_med_expect', 'ra_med_take', 'ra_med_call', 'ra_med_comfort', 'ra_med_train', 'ra_depress', 'ra_missed_days', 'ra_status']
		'TNF-RA Barriers to Compliance Assessment':
			note: 'Do any of the following PREVENT you from GETTING or TAKING your medication on time?'
			fields: ['ra_comp_rem', 'ra_comp_follow', 'ra_comp_se', 'ra_comp_site', 'ra_comp_cost', 'ra_comp_worse', 'ra_comp_feel', 'ra_comp_hosp', 'ra_comp_other']
		'Patient Questionnaire: TNF - Psoriasis':
			fields: ['palm_cover_cnt']
		'Followup':
			note: 'Ask the patient the following questions'
			fields: ['have_fever', 'infection', 'infection_symptoms', 'infection_symptoms_other', 'had_infection', 'pt_report_rash',
						'pt_report_pain', 'pt_report_dizzy', 'pt_report_wt_gain', 'pt_report_stool']
view:
	comment: 'Patient > Careplan > Ongoing > TNF-Inhibitor'
	grid:
		fields: ['ra_had_se', 'ra_had_se_other']
	label: 'Ongoing Assessment: TNF-Inhibitor'
	open: 'read'
