fields:

	code:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 3

	ae_only:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'AE Related Only?'
			columns: 3

	complaint_only:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Complaint Related Only?'
			columns: 3

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 3

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['manage']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['code']
		]
	name: ['code']
	sections:
		'AAdverse EventE/Complaint Shipping Issue':
			fields: ['code', 'ae_only', 'complaint_only', 'allow_sync', 'active']

view:
	comment: 'Manage > Adverse Event/Complaint Pharmacy'
	find:
		basic: ['ae_only', 'complaint_only']
	grid:
		fields: ['code', 'ae_only', 'complaint_only', 'created_by', 'created_on']
		sort: ['code']
	label: 'AE/Complaint Pharmacy'
	open: 'read'
