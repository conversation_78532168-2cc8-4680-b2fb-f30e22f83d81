fields:

	#Type
	#Diagnosis.Primary/Secondary
	type:
		model:
			source: ['Primary', 'Secondary']
		view:
			columns: 3
			control: 'radio'
			label: 'Type'
			readonly: true

	#Code
	dx_code:
		view:
			columns: 3
			label: 'Code'
			readonly: true

	#Qualifier
	dx_code_qualifier_id:
		model:
			source: 'list_ss_dx_qualifier'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Code Qualifier'
			readonly: true

	dx_id:
		model:
			source: 'list_diagnosis'
			sourceid: 'code'
		view:
			label: 'Matching System Diagnosis'
			readonly: true

	#Description
	dx_desc:
		view:
			label: 'Description'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: ['dx_code', 'dx_desc']
	indexes:
		many: [
			['dx_code']
			['dx_code_qualifier_id']
			['dx_id']
		]
	sections:
		'Diagnosis':
			fields: ['type', 'dx_code',
			'dx_code_qualifier_id', 'dx_id', 'dx_desc']
view:
	hide_cardmenu: true
	comment: 'Surescripts Diagnosis'
	grid:
		fields: ['type', 'dx_code', 'dx_code_qualifier_id', 'dx_id', 'dx_desc']
		sort: ['-created_on']
	label: 'Surescripts Diagnosis'
	open: 'read'
