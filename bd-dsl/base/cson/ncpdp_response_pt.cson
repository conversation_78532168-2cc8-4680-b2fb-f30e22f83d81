fields:

	patient_first_name:
		model:
			max: 12
		view:
			columns: 3
			note: '310-CA'
			label: 'First Name'
			readonly: true

	patient_last_name:
		model:
			max: 15
		view:
			columns: 3
			note: '311-CB'
			label: 'Last Name'
			readonly: true

	patient_date_of_birth:
		model:
			type: 'date'
		view:
			columns: 3
			note: '304-C4'
			label: 'Patient DOB'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['patient_first_name', 'patient_last_name', 'patient_date_of_birth']
	sections:
		'Patient':
			hide_header: true
			indent: false
			fields: ['patient_first_name', 'patient_last_name', 'patient_date_of_birth']

view:
	dimensions:
		width: '50%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Patient NCPDP Response'
	grid:
		fields: ['patient_first_name', 'patient_last_name', 'patient_date_of_birth']
		sort: ['-created_on']
	label: 'Patient NCPDP Response'
	open: 'read'