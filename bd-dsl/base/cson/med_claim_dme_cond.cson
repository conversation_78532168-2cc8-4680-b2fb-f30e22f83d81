fields:

	certification_condition_indicator:
		model:
			source:
				'Y': 'Yes'
			if:
				'Y':
					fields: ['condition_indicator', 'condition_indicator_code']
		view:
			columns: 4
			control: 'checkbox'
			label: 'Cert Cond Indicator'
			class: 'checkbox-only'
			reference: 'CRC02'
			_meta:
				location: '2400 CRC'
				field: '02'
				code: '09'
				path: 'claimInformation.serviceLines[{idx1-50}].conditionIndicatorDurableMedicalEquipment.certificationConditionIndicator'

	condition_indicator:
		model:
			default: '12'
			min: 2
			max: 2
			required: true
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CRC03'
		view:
			columns: 4
			label: 'Condition Indicator'
			reference: 'CRC03'
			_meta:
				location: '2400 CRC'
				field: '03'
				path: 'claimInformation.serviceLines[{idx1-50}].conditionIndicatorDurableMedicalEquipment.conditionIndicator'

	condition_indicator_code:
		model:
			default: '38'
			min: 2
			max: 2
			required: true
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CRC04'
		view:
			columns: 2
			label: 'Condition Indicator Code'
			reference: 'CRC04'
			_meta:
				location: '2400 CRC'
				field: '04'
				path: 'claimInformation.serviceLines[{idx1-50}].conditionIndicatorDurableMedicalEquipment.conditionIndicatorCode'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['condition_indicator','condition_indicator_code']
	sections:
		'DME Condition':
			hide_header: true
			fields: ['certification_condition_indicator', 'condition_indicator', 'condition_indicator_code']

view:
	dimensions:
		width: '85%'
		height: '55%'
	hide_cardmenu: true
	reference: '2400'
	comment: 'DME Condition'
	grid:
		fields: ['certification_condition_indicator', 'condition_indicator', 'condition_indicator_code']
		sort: ['-created_on']
	label: 'DME Condition'
	open: 'read'