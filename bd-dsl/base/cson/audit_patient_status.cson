fields:

	patient_id:
		model:
			required: false 
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true

	careplan_id:
		model:
			required: false
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan'
			readonly: true

	prior_auth_id:
		model:
			required: false
			type: 'int'
		view:
			label: 'Prior Auth ID'
			readonly: true

	order_id:
		model:
			required: false
			type: 'int'
		view:
			label: 'Order ID'
			readonly: true

	prev_status:
		view:
			label: 'Previous Status'
			readonly: true

	new_status:
		view:
			label: 'New Status'
			readonly: true

	prev_substatus:
		view:
			label: 'Previous Substatus'
			readonly: true

	new_substatus:
		view:
			label: 'New Substatus'
			readonly: true

model:
	access:
		create:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'payer', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'payer', 'physician']
		request:    []
		update:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['audit']
	indexes:
		many: [
			['patient_id'],
			['careplan_id'],
			['prior_auth_id'],
			['order_id']
		]
	name: ['patient_id', 'order_id', 'new_status']
	reportable: true

	sections:
		'Status Audit Trail':
			fields: ['patient_id', 'careplan_id', 'prior_auth_id',
			'order_id', 'prev_status', 'new_status', 'prev_substatus', 'new_substatus']

view:
	comment: 'Patient > Status Audit Trail'
	grid:
		fields: ['created_on', 'patient_id','careplan_id', 'prior_auth_id',
			'order_id', 'prev_status', 'new_status', 'prev_substatus', 'new_substatus']
		sort: ['-created_on']
	label: 'Status Audit Trail'
	open: 'read'