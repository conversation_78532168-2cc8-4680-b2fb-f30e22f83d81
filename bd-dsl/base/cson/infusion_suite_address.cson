fields:

	address1:
		model:
			required: true
			min: 1
			max: 35
		view:
			columns: 'addr_1'
			label: 'Address'
			class: "api_prefill"
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode', 'postalCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'address1': ['addressLabel']
					'city': ['city']
					'state_id': ['stateCode']
					'postal_code': ['postalCode']
			]

	address2:
		model:
			min: 1
			max: 35
		view:
			columns: 'addr_2'
			label: 'Address 2'

	city:
		model:
			required: true
			min: 1
			max: 60
		view:
			columns: 'addr_city'
			label: 'City'

	state_id:
		model:
			required: true
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'State'

	postal_code:
		model:
			required: true
			max: 15
			min: 3
		view:
			columns: 'addr_zip'
			format: 'us_zip'
			label: 'Zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'postal_code'
						city:'city'
						state:'state'
			]

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['city', 'state_id', 'postal_code']
	sections:
		'Infusion Suite Address':
			hide_header: true
			fields: ['address1', 'address2', 'city', 'state_id', 'postal_code']

view:
	hide_cardmenu: true
	comment: 'Infusion Suite Address'
	grid:
		fields: ['address1', 'address2', 'city', 'state_id', 'postal_code']
		width: [30, 30, 20, 10, 10]
		sort: ['-created_on']
	label: 'Infusion Suite Address'
	open: 'read'