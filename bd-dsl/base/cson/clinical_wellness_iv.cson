fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Wellness
	since_last:
		model:
			max: 64
			min: 1
			source: ['Improved', 'Improved, then relapsed', 'No change (stable)', 'Relapsed since last infusion (worsening)', 'N/A (First Lifetime Infusion)']
			if:
				'Improved, then relapsed':
					fields: ['days_relapse', 'last_relapse']
				'Relapsed since last infusion (worsening)':
					fields: ['days_relapse', 'last_relapse']
		view:
			control: 'select'
			label: 'Since Last Infusion'

	days_relapse:
		model:
			type: 'int'
			min: 1
			required: true
		view:
			label: 'How many days after your last infusion until your symptoms reappeared?'

	last_relapse:
		model:
			type: 'date'
			required: true
		view:
			label: 'Date of last relapse'

	compared_wellness:
		model:
			max: 12
			source: ['Better', 'Same', 'Worse']
		view:
			control: 'radio'
			label: 'Compared to onset of my disease, as of today I am:'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_wellness_iv:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Wellness (IV)':
			fields: ['since_last', 'days_relapse', 'last_relapse', 'compared_wellness']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical Wellness (IV)'
	label: 'Patient Clinical Wellness (IV)'
