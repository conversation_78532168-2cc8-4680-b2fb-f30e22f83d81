fields:
	therapy:
		model:
			default: 'IVIG'
			max: 64
			required: true
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Therapy'
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Letter Code'
	mimetype:
		model:
			max: 128
		view:
			label: 'Mime Type'
	template:
		model:
			max: 16348
		view:
			control: 'area'
			label: 'HTML Template'
model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['templates']
	indexes:
		fulltext: ['template']
		unique: [
			['therapy', 'code']
		]
	reportable: false
	name: ['therapy', 'code']
	sections:
		'Main':
				fields: ['therapy', 'code', 'mimetype', 'template']
view:
	comment: 'Setup > Letter Template'
	find:
		basic: ['therapy', 'code', 'mimetype']
		advanced: ['template']
	grid:
		fields: ['therapy', 'code', 'mimetype']
	label: 'Letter Template'
