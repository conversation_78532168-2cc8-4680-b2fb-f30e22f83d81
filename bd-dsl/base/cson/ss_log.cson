fields:

	direction:
		model:
			source:
				'IN': 'Inbox'
				'OUT': 'Outbox'
		view:
			label: 'Direction'
			readonly: true

	message_id:
		view:
			label: 'MessageID'
			readonly: true
			findunique: true

	message_xml:
		model:
			type: 'xml'
		view:
			control: 'xml'
			label: 'Message XML Content'
			readonly: true

	message_json:
		model:
			type: 'json'
		view:
			label: 'Message JSON Content'
			readonly: true
			offscreen: true

	message_hash:
		view:
			label: 'Encryped Message Hash'
			readonly: true
			offscreen: true

	message_type:
		view:
			label: 'Message Type'
			readonly: true
			findunique: true

	error_code_id:
		view:
			label: 'Error Code'
			readonly: true

	error_desc_code_id:
		view:
			label: 'Error Description Code'
			readonly: true

	error_description:
		view:
			label: 'Error Description'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: []
		write:      []
	bundle: ['audit']
	name: ['message_id', 'direction', 'message_type']
	sections:
		'SureScripts Message Log':
			fields: ['direction', 'message_id', 'message_type',
			'message_xml', 'error_code_id', 'error_desc_code_id', 'error_description']
view:
	hide_cardmenu: true
	find:
		basic: ['direction', 'message_id', 'message_type', 'error_code_id', 'error_desc_code_id']
	comment: 'SureScripts Message Log'
	grid:
		fields: ['direction', 'message_id', 'message_type', 'error_code_id', 'error_desc_code_id', 'error_description']
		sort: ['-created_on']
	label: 'SureScripts Message Log'
	open: 'read'
