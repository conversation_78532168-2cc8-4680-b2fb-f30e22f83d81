fields:
  assigned_team:
    model:
      required: false
      source: "wf_queue_team"
    view:
      label: 'Assigned Team'

  assigned_user:
    model:
      required: true
      source: "user"
    view:
      label: 'Assigned User'
      
  patient:
    model:
      required: false
      source:'patient'
    view:
      label: 'patient'
  site:
    model:
      required: false
      source:'site'
    view:
      label: 'site'

    
  status:
    model:
      source: 'list_wf_queue_status'
    view:
      label: 'Status'
model:
  access:
    create:     []
    create_all: ['admin']
    delete:     ['admin']
    read:       ['admin']
    read_all:   ['admin']
    request:    []
    update:     []
    update_all: ['admin']
    write:      ['admin']
  name: '{name}'
  sections:
    'Main':
      fields: ['assigned_team', 'assigned_user' ,'patient', 'status']

view:
  comment: 'Swim Lane Cards'
  find:
    basic: ['assigned_team', 'assigned_user','patient', 'status']
  grid:
    fields: ['assigned_team', 'assigned_user','patient', 'status']
  label: 'Swim Lane Cards'
  open: 'read'
