fields:

	claim_adjustment_group_code:
		view:
			columns: 2
			label: 'Group Code'
			note: 'CAS01'
			readonly: true
			_meta:
				path: 'claimAdjustmentGroupCode'

	claim_adjustment_group_code_value:
		view:
			columns: 2
			label: 'Group'
			note: 'CAS01'
			readonly: true
			_meta:
				path: 'claimAdjustmentGroupCodeValue'

	adjustment_reason_code:
		model:
			min: 1
			max: 5
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CARC'
		view:
			columns: 4
			label: 'Reason Code'
			note: 'CAS02; CAS05; CAS08; CAS11; CAS14; CAS17'
			readonly: true
			_meta:
				path: 'adjustmentReasonCode'

	adjustment_amount:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Adjustment Amount'
			note: 'CAS03; CAS06; CAS09; CAS12; CAS15; CAS18'
			readonly: true
			_meta:
				path: 'adjustmentAmount'

	adjustment_quantity:
		model:
			rounding: 0.001
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Quantity'
			note: 'CAS04; CAS07; CAS10; CAS13; CAS16; CAS19'
			readonly: true
			_meta:
				path: 'adjustmentQuantity'

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: 'Claim Adjustments'
	indexes:
		many: [
			['claim_adjustment_group_code']
			['adjustment_reason_code']
		]
	sections:
		'Claim Adjustment':
			hide_header: true
			fields: ['claim_adjustment_group_code', 'claim_adjustment_group_code_value',
			'adjustment_reason_code', 'adjustment_amount', 'adjustment_quantity']

view:
	dimensions:
		width: '65%'
		height: '45%'
	hide_cardmenu: true
	comment: 'Claim Adjustments'
	grid:
		fields: ['adjustment_reason_code', 'adjustment_amount', 'adjustment_quantity']
		label: ['Reason', 'Amount', 'Quantity']
		width: [60, 20, 20]
		sort: ['-created_on']
	label: 'Claim Adjustments'
	open: 'read'