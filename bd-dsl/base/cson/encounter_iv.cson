fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	had_pain_site:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_pain_location']
		view:
			control: 'radio'
			label: "Have you had any episodes of pain or tenderness at the injection sites?"

	had_pain_location:
		view:
			control: 'area'
			label: 'Location of Pain/tenderness:'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['careplan_id', 'order_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		encounter_iv:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Patient Questionnaire':
			area:'questions'
			fields: ['had_pain_site', 'had_pain_location']

view:
	comment: 'Patient > Careplan > Encounter > IV'
	label: 'Patient Encounter: IV'
