fields:
    value:
        model:
            required: true
            max: 50
            min: 1
        view:
            label: 'Referral Number'
            _meta:
                location: '2400 REF'
                field: '02'
                code: '9F'
                path: 'claimInformation.serviceLines[{idx1-50}].serviceLineReferenceInformation.referralNumber[{idx1-5}]'

model:
    access:
        create:     []
        create_all: ['admin', 'csr', 'pharm']
        delete:     ['admin', 'pharm']
        read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
        read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
        request:    []
        update:     []
        update_all: ['admin', 'csr', 'pharm']
        write:      ['admin', 'csr', 'pharm']
    name:['value']
    sections:
        'Referral Number':
            hide_header: true
            fields: ['value']

view:
    dimensions:
        width: '45%'
        height: '45%'
    hide_cardmenu: true
    comment: 'Referral Number'
    grid:
        fields: ['value']
        sort: ['-created_on']
    label: 'Referral Number'
    open: 'read'