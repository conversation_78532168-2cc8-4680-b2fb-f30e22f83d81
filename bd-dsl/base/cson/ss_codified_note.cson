fields:

	#Qualifier
	qualifier_id:
		model:
			source: 'list_ss_note_qualifier'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Qualifier ID'
			readonly: true

	#Value
	value:
		model:
			type: 'int'
		view:
			columns: 2
			label: 'Value'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['qualifier_id']
		]
	name: ['qualifier_id', 'value']
	sections:
		'Codified Note':
			fields: ['qualifier_id', 'value']
view:
	hide_cardmenu: true
	comment: 'Surescripts Codified Note'
	grid:
		fields: ['qualifier_id', 'value']
		sort: ['-created_on']
	label: 'Codified Note'
	open: 'read'
