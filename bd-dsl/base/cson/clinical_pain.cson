fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	pain_scale:
		model:
			required: true
			source: ['0', '1' ,'2' ,'3' ,'4' ,'5' ,'6' ,'7' ,'8' ,'9' ,'10']
			if:
				'*':
					fields: ['pain_management']
		view:
			control: 'radio'
			label: 'Pain Scale'

	pain_management:
		model:
			required: true
		view:
			label: 'What are you doing to control the pain?'
			control: 'area'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_stiffps:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Pain Assessment':
			fields: ['pain_scale', 'pain_management']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Pain Assessment'
	label: 'Pain Assessment'
