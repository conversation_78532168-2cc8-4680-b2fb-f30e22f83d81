fields:
	patient_id:
		model:
			type: 'int'
			required: true
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	prescriber_id:
		model:
			required: true
			type: 'int'
			source: 'patient_prescriber'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			columns: 2
			label: 'Prescriber'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_prescriber/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'physician_id': ['physician_id'],
					'provider_type': 'OrderingProvider',
			]

	physician_id:
		model:
			required: true
			type: 'int'
			source: 'physician'
		view:
			label: 'Physician'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/physician/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'last_name': ['last'],
					'first_name': ['first'],
					'npi': ['npi'],
					'state_license_number': ['state_license'],
					'taxonomy_code': ['taxonomy_id'],
					'contact_information':
						'type': 'subform'
						'field': 'contact_information'
						'fields':
							'name': ['pr.name']
							'phone_number': ['pr.phone']
							'fax_number': ['pr.fax']
			]

	provider_type:
		model:
			default: 'OrderingProvider'
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'NM101'
		view:
			columns: 2
			label: 'Provider Type'
			reference: 'NM101'
			readonly: true
			offscreen: true
			_meta:
				path: 'ordering.providerType'

	last_name:
		model:
			required: true
			min: 1
			max: 60
		view:
			columns: -4
			label: 'Last Name'
			reference: 'NM103'
			_meta:
				location: '2420E NM1'
				field: '03'
				path: 'ordering.lastName'

	first_name:
		model:
			required: true
			min: 1
			max: 35
		view:
			columns: 4
			label: 'First Name'
			reference: 'NM104'
			_meta:
				location: '2420E NM1'
				field: '04'
				path: 'ordering.firstName'

	npi:
		model:
			required: true
			type: 'text'
			max: 10
		view:
			label: 'NPI'
			reference: 'NM109'
			_meta:
				location: '2420E NM1'
				field: '09'
				path: 'ordering.npi'
			columns: 4
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid NPI, must be 10 digits'
			}]

	commercial_number:
		model:
			min: 1
			max: 50
		view:
			columns: 4
			label: 'Commercial Number'
			note: 'Sometimes assigned by Payer'
			reference: 'REF02 REF01=G2'
			_meta:
				location: '2420E REF'
				field: '02'
				code: 'G2'
				path: 'ordering.commercialNumber'

	state_license_number:
		model:
			min: 1
			max: 50
		view:
			columns: 4
			label: 'State License Number'
			reference: 'REF02 REF01=0B'
			_meta:
				location: '2420E REF'
				field: '02'
				code: '0B'
				path: 'ordering.stateLicenseNumber'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{5,20}$'
				error: 'Invalid State License Number'
			}]

	payer_identification_number:
		model:
			min: 1
			max: 50
		view:
			columns: 4
			label: 'Payer ID Number'
			note: 'Sometimes assigned by Payer'
			reference: 'REF02 REF01=2U'
			_meta:
				location: '2420E REF'
				field: '02'
				code: '2U'
				path: 'ordering.payerIdentificationNumber'

	taxonomy_code:
		model:
			source: 'list_nucc_taxonomy'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Taxonomy Code'
			note: 'Specialty for the physician (NUCC Taxonomy Code)'
			_meta:
				location: '2420E PRV'
				field: '03'
				path: 'ordering.taxonomyCode'

	contact_information:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_contact_oprov'
		view:
			label: 'Contact Information'

	address:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_address_oprov'
		view:
			label: 'Address'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
			['prescriber_id']
			['physician_id']
		]

	name:['prescriber_id', 'physician_id','provider_type']
	sections_group: [
		'Physician':
			hide_header: true
			fields: ['patient_id', 'prescriber_id', 'physician_id',
			'provider_type', 'last_name', 'first_name']
		'License Information':
			hide_header: true
			fields: ['npi', 'commercial_number', 'state_license_number',
			'payer_identification_number', 'taxonomy_code']
		'Contact Information':
			hide_header: true
			indent: false
			fields: ['contact_information']
		'Address':
			hide_header: true
			indent: false
			fields: ['address']
	]

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	reference: '2420E'
	comment: 'Ordering Provider'
	grid:
		fields: ['physician_id', 'npi', 'commercial_number', 'state_license_number']
		sort: ['-created_on']
	label: 'Ordering Provider'
	open: 'read'