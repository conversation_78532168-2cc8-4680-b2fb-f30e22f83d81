fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Brand Name'

	name:
		model:
			search:'A'
			max: 64
			required: true
		view:
			label: 'Name'
			findunique: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['inventory']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name:['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name']

view:
	comment: 'Manage > Short Brand Name'
	find:
		basic: ['name']
	grid:
		fields: ['name']
		sort: ['name']
	label: 'Short Brand Name'
