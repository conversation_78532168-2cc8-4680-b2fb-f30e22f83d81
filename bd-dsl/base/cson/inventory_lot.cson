fields:

	receipt_no:
		view:
			label: 'Receipt No'
			readonly: true
			offscreen: true
			findunique: true

	receipt_form:
		view:
			label: 'Receipt Form'
			readonly: true
			offscreen: true

	receipt_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Receipt Date'
			readonly: true
			offscreen: true

	site_id:
		model:
			required: true
			search: 'A'
			source: 'site'
		view:
			label: 'Site'
			readonly: true

	inventory_id:
		model:
			required: true
			search: 'A'
			source: 'inventory'
		view:
			label: 'Inventory Item'
			readonly: true

	lot_no:
		model:
			required: true
			search: 'B'
		view:
			label: 'Lot No'
			readonly: true

	expiration_date:
		model:
			type: 'date'
		view:
			label: 'Expiration Date'

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: 'ST:{site_id_auto_name} L:{lot_no} EXP:{expiration_date}'
	bundle: ['inventory']
	indexes:
		many: [
			['site_id'],
			['inventory_id'],
			['lot_no'],
			['receipt_no']
		]
	sections:
		'Inventory Lot':
			fields: ['site_id', 'inventory_id', 'lot_no', 'expiration_date']

view:
	hide_cardmenu: true
	comment: 'Inventory Lot'
	find:
		basic: ['site_id', 'inventory_id', 'lot_no']
	grid:
		fields: ['site_id', 'inventory_id', 'lot_no', 'expiration_date']
		sort: ['-id']
	label: 'Inventory Lot'
	open: 'edit'