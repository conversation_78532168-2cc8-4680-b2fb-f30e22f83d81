fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Demographics
	gender:
		model:
			if:
				'Female':
					fields: ['are_preg', 'birth_control', 'are_postmen']
			prefill: ['patient']
		view:
			label: 'Sex'
			offscreen: true
			readonly: true

	# HIV Details:
	creatinine_levels:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'mg/dL'
			label: 'Creatinine level'
			columns: 3

	cd4_count:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'CD4 count'
			columns: 3

	viral_load:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'copies/mL'
			label: 'Viral load'
			columns: 3

	elevated_alt:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Elevated serum ALT level?'
			columns: 3

	cirr_biop:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_cirrhosis']
		view:
			control: 'radio'
			label: 'Cirrhosis and or Liver Biopsy performed?'
			columns: 3

	have_cirrhosis:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cirrhosis_type', 'fibrosis_stage', 'child_pugh_score']
		view:
			control: 'radio'
			label: 'Cirrhosis confirmed?'
			columns: 3

	cirrhosis_type:
		model:
			max: 3
			min: 1
			source: ['Compensated', 'Decompensated']
		view:
			control: 'radio'
			label: 'Cirrhosis Type'
			columns: 3

	fibrosis_stage:
		model:
			source: ['0', '1', '2', '3', '4']
		view:
			control: 'radio'
			label: 'Fibrosis Stage'
			columns: 3

	child_pugh_score:
		model:
			type:'int'
		view:
			label: 'Child-Pugh Score'
			columns: 3

	# General Assessment
	cancer:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you currently or have you recently been on cancer treatment?'
			columns: 2

	tattoos:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you have any tattoos?'
			columns: 3

	has_hepb:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Did you have a history Hepatitis B?"
			columns: 3

	have_exposure:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Have you had any recent exposure to tuberculosis (TB), HBV, or mycoses?"
			columns: 2

	travel:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['travel_where']
		view:
			control: 'radio'
			label: 'Any recent international travel?'
			columns: 2

	travel_where:
		view:
			label: 'Where?'
			columns: 2

	are_preg:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you currently pregnant or have given birth in the last 6 weeks?'

	birth_control:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you currently on any birth control or hormone replacement pills?'

	are_postmen:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Are you postmenopausal?'

	# Renal Assessment
	has_diabetes:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['has_insulin']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with diabetes?'
			columns: 3

	has_insulin:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you depend on insulin to regulate blood sugar?'
			columns: 3

	had_kidney_disease:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you ever been diagnosed with kidney disease?'
			columns: 3

	meds_taking:
		model:
			max: 32
			min: 1
			multi: true
			source:['Acyclovir','Aminoglycosides','Amphotericin','Atripla','Cisplatin','Diuretics (loops, thiazides)','NSAIDS',
				'Prograf','Proton-Pump Inhibitors','Tenofovir','Viread','Truvada','Other','None']
			if:
				'Other':
					fields: ['meds_taking_other']
		view:
			control: 'checkbox'
			label: 'Are you currently taking any of the following (concomitant nephrotoxic) drugs?'
			columns: 2

	meds_taking_other:
		view:
			label: 'Other Drugs'
			columns: 2

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections_group: [
		'HIV Questionnaire':
			sections: [
				'HIV Condition Details':
					area: 'preassessment'
					fields: ['creatinine_levels', 'cd4_count', 'viral_load', 'elevated_alt', 'cirr_biop', 'have_cirrhosis', 'cirrhosis_type', 'fibrosis_stage', 'child_pugh_score']
				'HIV General Assessment':
					fields: ['gender', 'are_preg', 'birth_control', 'are_postmen', 'cancer', 'tattoos', 'has_hepb', 'have_exposure', 'travel', 'travel_where']
				'HIV Renal Disease Risk Assessment':
					fields: ['has_diabetes', 'has_insulin', 'had_kidney_disease', 'meds_taking', 'meds_taking_other']
			]
		]
view:
	comment: 'Patient > Careplan > Assessment > HIV'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: HIV'
	open: 'read'
