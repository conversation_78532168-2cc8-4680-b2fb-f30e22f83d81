fields:
	patient_id:
		model:
			type: 'int'
			required: true
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true
			class: 'select_prefill'

	cob_insurance_id:
		model:
			type: 'int'
			required: true
			source: 'patient_insurance'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
				billing_method_id:
					'static': 'mm'
		view:
			columns: 2
			label: 'Insurance'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_insurance/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'individual_relationship_code': ['medical_relationship_id'],
					'insurance_type_code': ['insurance_type_code'],
					'cob_payer_id': ['payer_id'],
					'insurance_group_or_policy_number': ['group_number', 'policy_number'],
					'other_insured_group_name': ['group_name']
					'other_payer_name':
						'type': 'subform'
						'field': 'other_payer_name'
						'fields':
							'cob_insurance_id': ['pr.id']
			]

	cob_payer_id:
		model:
			type: 'int'
			source: 'payer'
		view:
			label: 'Payer'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/payer/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'claim_filing_indicator_code': ['mm_claim_filing_indicator_code']
					'cob_payer_type_id': ['type_id']
					'other_payer_name':
						'type': 'subform'
						'field': 'other_payer_name'
						'fields':
							'cob_payer_id': ['pr.id']
			]

	cob_payer_type_id:
		model:
			source: 'list_payer_type'
			sourceid: 'code'
			if:
				'MCRB':
					require_fields: ['mcr_reimbursement_rate', 'mcr_hcpcs_payable_amount', 'mcr_rcodes']
		view:
			label: 'Payer Type'
			readonly: true
			offscreen: true

	payment_responsibility_level_code:
		model:
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR01'
		view:
			columns: 4
			label: 'Resp Level Code'
			reference: 'SBR01'
			_meta:
				location: '2320 SBR'
				field: '01'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].paymentResponsibilityLevelCode'

	individual_relationship_code:
		model:
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR02'
		view:
			columns: 4
			label: 'Relationship Code'
			reference: 'SBR02'
			_meta:
				location: '2320 SBR'
				field: '02'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].individualRelationshipCode'
			validate: [
				name: 'LoadCOBPtInfo'
			]

	insurance_type_code:
		model:
			min: 1
			max: 3
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR05'
		view:
			columns: 2
			label: 'Insurance Type Code'
			reference: 'SBR05'
			_meta:
				location: '2320 SBR'
				field: '05'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].insuranceTypeCode'

	claim_filing_indicator_code:
		model:
			required: true
			min: 1
			max: 3
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR09'
		view:
			columns: 4
			label: 'Claim Filing Code'
			reference: 'SBR09'
			_meta:
				location: '2320 SBR'
				field: '09'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].claimFilingIndicatorCode'

	benefits_assignment_certification_indicator:
		model:
			default: 'Y'
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'O103'
		view:
			columns: 4
			label: 'Benefits Assign Cert Ind'
			reference: 'OI03'
			_meta:
				location: '2320 OI'
				field: '03'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].benefitsAssignmentCertificationIndicator'

	patient_signature_generate_for_patient:
		model:
			default: 'P'
			source:
				'P': 'Yes'
		view:
			columns: 4
			control: 'checkbox'
			label: "Sig Gen for Pt?"
			note: "Check if signatured generated for patient because patient wasn't physically present"
			class: 'checkbox-only'
			reference: 'O104'
			_meta:
				location: '2320 OI'
				field: '04'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].patientSignatureGenerateForPatient'

	insurance_group_or_policy_number:
		model:
			min: 1
			max: 50
		view:
			columns: 4
			label: 'Group/Policy #'
			reference: 'SBR03'
			_meta:
				location: '2320 SBR'
				field: '03'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].insuranceGroupOrPolicyNumber'

	other_insured_group_name:
		model:
			min: 1
			max: 60
		view:
			columns: 4
			label: 'Group Name'
			reference: 'SBR04'
			_meta:
				location: '2320 SBR'
				field: '04'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherInsuredGroupName'

	release_of_information_code:
		model:
			default: 'Y'
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'O106'
		view:
			columns: 4
			label: 'Release of Info Code'
			reference: 'OI06'
			_meta:
				location: '2320 OI'
				field: '06'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].releaseOfInformationCode'

	other_subscriber_name:
		model:
			required: true
			multi: false
			source: 'med_claim_osub_nm'
			type: 'subform'
		view:
			label: 'Other Subscr Name'

	other_payer_name:
		model:
			required: true
			multi: false
			source: 'med_claim_opayer'
			type: 'subform'
		view:
			label: 'Other Payer'

	payer_paid_amount:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Payer Paid Amount'
			reference: 'AMT02 AMT01=D'
			_meta:
				location: '2320 AMT'
				code: 'D'
				field: '02'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].payerPaidAmount'

	non_covered_charge_amount:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Non-Covered Charge Amount'
			reference: 'AMT02 AMT01=A8'
			_meta:
				location: '2320 AMT'
				code: 'A8'
				field: '02'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].nonCoveredChargeAmount'

	remaining_patient_liability:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Remaining Patient Liability'
			reference: 'AMT02 AMT01=EAF'
			_meta:
				location: '2320 AMT'
				code: 'EAF'
				field: '02'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].remainingPatientLiability'

	mcr_reimbursement_rate:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: -4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Medicare Reimbursement Rate'
			reference: 'MOA01'

	mcr_hcpcs_payable_amount:
		model:
			max: **********.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Medicare Patient Responsibility Amount'
			reference: 'MOA02'

	mcr_rcodes:
		model:
			required: false
			multi: true
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'RARC'
		view:
			columns: 4
			max_count: 5
			label: 'Medicare Remark Codes'
			note: 'MOA03 – MOA07'
			readonly: true

	claim_level_adjustments:
		model:
			multi: true
			source: 'med_claim_adj'
			type: 'subform'
		view:
			note: 'Max 5'
			label: 'Adjustments'
			max_count: 5

	providers:
		model:
			type: 'subform'
			multi: false
			source: 'med_claim_oprovs'
		view:
			label: 'Providers'

	other_payer_service_facility_location:
		model:
			multi: false
			source: 'med_claim_ofacility'
			type: 'subform'
		view:
			label: 'Facility'
			offscreen: true
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['payment_responsibility_level_code', 'individual_relationship_code']
	sections_group: [
		'COB':
			hide_header: true
			sections: [
				'Payer':
					hide_header: true
					indent: false
					tab: 'Insurance'
					fields: ['other_payer_name']
				'Insurance':
					hide_header: true
					tab: 'Insurance'
					fields: ['patient_id', 'cob_insurance_id',  'cob_payer_id', 'cob_payer_type_id', 'payment_responsibility_level_code', 'individual_relationship_code',
					'insurance_type_code', 'claim_filing_indicator_code',
					'benefits_assignment_certification_indicator', 'patient_signature_generate_for_patient',
					'insurance_group_or_policy_number', 'other_insured_group_name', 'release_of_information_code']
				'Subscriber':
					hide_header: true
					indent: false
					tab: 'Insurance'
					fields: ['other_subscriber_name']
				'Paid':
					hide_header: true
					indent: false
					tab: 'Paid'
					fields: ['payer_paid_amount', 'non_covered_charge_amount', 'remaining_patient_liability']
				'Medicare':
					hide_header: true
					indent: false
					tab: 'Medicare'
					fields: ['mcr_reimbursement_rate', 'mcr_hcpcs_payable_amount', 'mcr_rcodes']
				'Adjustments':
					indent: false
					tab: 'Adjustments'
					tab_toggle: true
					fields: ['claim_level_adjustments']
				'Providers':
					hide_header: true
					indent: false
					tab: 'Providers'
					tab_toggle: true
					fields: ['providers']
				'Facility':
					hide_header: true
					indent: false
					tab: 'Facility'
					tab_toggle: true
					fields: ['other_payer_service_facility_location']
			]
	]
view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	reference: '2320'
	comment: 'Med Claim COB'
	hide_header: true
	grid:
		fields: ['insurance_type_code', 'payer_paid_amount', 'non_covered_charge_amount', 'remaining_patient_liability']
		width: [40, 20, 20, 20]
		sort: ['-created_on']
	label: 'Med Claim COB'
	open: 'read'