fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	purpose:
		model:
			required: true
			source:
				palliative: 'Palliative'
				bridge: 'Bridge to transplant'
		view:
			control: 'radio'
			label: 'Purpose of Inotropic therapy?'
			columns: 2.1

	had_fever:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_fever_comment']
		view:
			control: 'radio'
			label: 'Have you had any fevers this week?'
			columns: 2

	had_fever_comment:
		view:
			label: 'Fever Comment'
			columns: 2

	swelling:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['swelling_comment']
		view:
			control: 'radio'
			label: 'Do you have any swelling in your feet, legs, stomach, or hands?'
			columns: -2

	swelling_comment:
		view:
			label: 'Swelling Comment'
			columns: 2

	shortness_breath:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['shortness_breath_comment']
		view:
			control: 'radio'
			label: 'Are you having any shortness of breath or chest pain?'
			columns: -2

	shortness_breath_comment:
		view:
			label: 'Shortness of Breath/Chest Pain Comment'
			columns: 2

	heart_rate:
		model:
			required: true
			type: 'int'
		view:
			note: 'bpm'
			label: 'What is your heart rate today?'
			columns: -2

	had_palpitations:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you noticed any palpitations?'
			columns: -3

	had_dizziness:
		model:
			required: true
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Have you had any dizziness?'
			columns: 3

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Inotropic General Assessment':
			indent: false
			tab: 'Inotropic General'
			fields: ['purpose','had_fever', 'had_fever_comment',
			'swelling', 'swelling_comment',
			'shortness_breath', 'shortness_breath_comment',
			'had_palpitations', 'had_dizziness', 'heart_rate']
view:
	comment: 'Patient > Careplan > Assessment > Inotropic'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
	label: 'Assessment Questionnaire: Inotropic'
	open: 'read'
