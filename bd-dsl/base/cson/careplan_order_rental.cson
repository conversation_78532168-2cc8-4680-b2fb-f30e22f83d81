fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	last_dispense_date:
		model:
			type: 'date'
			required: false
		view:
			columns: 4
			label: 'Dispense Date'
			readonly: true

	last_billed:
		model:
			type: 'date'
			required: false
		view:
			columns: 4
			label: 'Last Billed'
			readonly: true

	last_dispense_delivery_tick_id:
		model:
			type: 'int'
		view:
			label: 'Last Delivery Ticket ID'
			note: 'Used when payer bills rentals in arrears'
			readonly: true
			offscreen: true

	checked_in:
		model:
			source: ['Yes']
		view:
			label: 'Checked In'
			readonly: true
			offscreen: true

	serial_no:
		model:
			required: false
			search: 'B'
		view:
			columns: 4
			label: 'Serial No'
			readonly: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
			sourcefilter:
				type:
					'static': ['Equipment Rental']
				active:
					'static': 'Yes'
		view:
			columns: -2
			label: 'Equipment'
			class: 'select_prefill fdb-field'
			validate: [
				{
					name: 'MatchFields'
					parentField: 'subform_rental'
					errorMessage = 'Duplicate item entered, please select a different item.'
					matchingField = 'inventory_id'
					type: 'subform'
				}
			]

	rental_type:
		model:
			required: true
			default: 'Rental'
			source: ['Purchase', 'Rental']
			if:
				'Rental':
					fields: ['rental_billable']
				'Purchase':
					fields: ['rental_insurance_id', 'send_cert', 'rental_pa_id']
		view:
			columns: 4
			label: 'Rental Type'

	rental_billable:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['rental_insurance_id', 'frequency_code', 'show_max_rental_warning', 'max_rental_claims', 'send_cert', 'rental_pa_id']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			columns: -4

	rental_insurance_id:
		model:
			required: true
			source: 'patient_insurance'
			type: 'int'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			columns: 4
			label: 'Rental Insurance'
			class: 'select_prefill'
			readonly: true
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_insurance/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'payer_id': ['payer_id']
			]

	payer_id:
		model:
			source: 'payer'
			type: 'int'
		view:
			label: 'Payer'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/payer/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'max_rental_claims': ['max_rental_claims'],
					'no_recurring_billing': ['no_recurring_billing'],
					'daily_bill_rental': ['daily_bill_rental']
			]

	no_recurring_billing:
		model:
			source: ['Yes']
			if:
				'Yes':
					prefill:
						rental_type: 'Purchase'
					readonly:
						fields: ['rental_type']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Do not create recurring billing?'
			offscreen: true
			readonly: true

	daily_bill_rental:
		model:
			source: ['Yes']
			if:
				'Yes':
					prefill:
						frequency_code: '6'
					readonly:
						fields: ['frequency_code']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			note: 'If set, defaults the frequency code to Daily'
			label: 'Daily Bill Rental?'
			offscreen: true
			readonly: true

	max_rental_claims:
		model:
			required: true
			default: 18
			min: 0
			type: 'int'
		view:
			columns: 4
			note: '0 for no limit'
			label: 'Max Number of Rental Claims'

	show_max_rental_warning:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['max_rental_warning']
		view:
			offscreen: true
			readonly: true
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Show Max Rental Warning?'

	max_rental_warning:
		model:
			multi: true
			source: ["<span color='#D26158'>Max number of rental claims reached.</span>"]
		view:
			control: 'checkbox'
			label: 'Warning'
			class: 'list'
			readonly: true

	rental_pa_id:
		model:
			required: false
			source: 'patient_prior_auth'
			sourcefilter:
				pa_type:
					'static': 'DME'
				patient_id:
					'dynamic': '{patient_id}'
				status_id:
					'static': ['5', '1', '2', '3', '4', '8']
				insurance_id:
					'dynamic': '{rental_insurance_id}'
		view:
			columns: -2
			label: 'Rental Authorization'

	frequency_code:
		model:
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SV506'
		view:
			columns: 4
			class: 'claim-field'
			label: 'Rental Frequency'

	send_cert:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['certification_type_code', 'durable_medical_equipment_duration_in_months']
		view:
			columns: 4
			control: 'checkbox'
			label: 'Send DME Certification Information On Claim?'
			note: 'On Medical Claim'
			class: 'checkbox-only'

	certification_type_code:
		model:
			default: 'I'
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CR301'
		view:
			columns: 4
			class: 'claim-field'
			label: 'Certification Type Code'

	durable_medical_equipment_duration_in_months:
		model:
			required: true
			max: 999999999999999
			min: 1
			rounding: 1
			type: 'decimal'
		view:
			columns: 4
			class: 'claim-field'
			label: 'Certification Duration (Months)'

	rental_notes:
		model:
			required: false
			type: 'text'
		view:
			control: 'area'
			label: 'Rental Notes'

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'careplan_order_rental'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Other Form'
					direct_attachment: 'Yes'
					source: 'Scanned Document'
					form_code: '{code}'
					form_name: 'careplan_order_rental'
					form_filter: 'careplan_order_rental'
					patient_id: '{patient_id}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: '{inventory_id_auto_name}'
	prefill:
		patient:
			link:
				id: 'patient_id'
	indexes:
		many: [
			['patient_id']
			['careplan_id']
			['inventory_id']
			['serial_no']
			['rental_pa_id']
		]
		unique: [
			['code']
		]
	sections_group: [
		'Rental':
			hide_header: true
			indent: false
			fields: ['code', 'show_max_rental_warning', 'max_rental_warning', 'inventory_id', 'rental_type', 'rental_billable', 'rental_insurance_id', 'rental_pa_id', 'frequency_code',
			'payer_id', 'no_recurring_billing', 'daily_bill_rental', 'max_rental_claims', 'send_cert', 'certification_type_code', 'durable_medical_equipment_duration_in_months', 'rental_notes',]
			tab: 'Rental'
		'Dispense Information':
			indent: false
			fields: ['last_dispense_date', 'last_billed', 'serial_no']
			tab: 'Rental'
		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Rental'
	grid:
		fields: ['inventory_id', 'rental_type', 'frequency_code', 'rental_notes']
		sort: ['-created_on']
	find:
		basic: ['inventory_id', 'rental_type']
	label: 'Rental'
	open: 'read'
