fields:

	patient_id:
		model:
			required: false
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
	external_id:
		view:
			label: 'External Id'
			readonly: true

	line_directions:
		model:
			required: false
		view:
			label: 'Directions'
			class: 'label-line groups top'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line11'
			]
			validate: [{
				name: 'ReverseParentPrefill'
				copy:
					'line_directions': 'rxform_line1'
				}
			]

	line11:
		view:
			label: 'Line 2'
			class: 'no-label label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line12'
			]
			validate: [{
				name: 'ReverseParentPrefill'
				copy:
					'line11': 'rxform_line2'
				}
			]

	line12:
		model:
			max: 45
		view:
			label: 'Line 3'
			class: 'no-label label-line groups middle'
			validate: [{
				name: 'ReverseParentPrefill'
				copy:
					'line12': 'rxform_line3'
				}
			]

	inits:
		model:
			source: 'list_rph_labels'
			sourceid: 'code'
		view:
			columns: 4
			label: 'RPH Initials'
			class: 'label-line groups bottom'
			validate: [
				{
					name: 'RphPromptForPassword'
				}
			]
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['patient_id']
	sections:
		'PO Label':
			hide_header: true
			indent: false
			fields: ['line_directions', 'line11', 'line12', 'inits']

view:
	comment: 'Manage > PO Label'
	find:
		basic: ['patient_id']
	grid:
		fields: ['patient_id']
		sort: ['created_on']
	label: 'PO Label'
