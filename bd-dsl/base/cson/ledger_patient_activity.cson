fields:

	localized_datetime:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Datetime'
			readonly: true

	user_id:
		model:
			source: 'user'
		view:
			columns: 4
			label: 'User'
			readonly: true

	patient_id:
		model:
			source: 'patient'
		view:
			columns: 4
			label: 'Patient'
			readonly: true

	site_id:
		model:
			source: 'site'
		view:
			columns: 4
			label: 'Site'
			readonly: true

	form:
		view:
			columns: -4
			label: 'Form'
			readonly: true

	form_id:
		model:
			type: 'int'
		view:
			columns: 4
			label: 'Form ID'
			readonly: true

	field:
		view:
			columns: -4
			label: 'Field'
			readonly: true

	old_value:
		view:
			columns: 4
			label: 'Old Value'
			readonly: true

	new_value:
		view:
			columns: 4
			label: 'New Value'
			readonly: true

	description:
		view:
			label: 'Description'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
	reportable: true
	bundle: ['audit']
	indexes:
		many: [
			['localized_datetime']
			['user_id']
			['patient_id']
			['site_id']
			['form']
			['form_id']
			['field']
			['old_value']
			['new_value']
			['description']
		]
	name: ['localized_datetime', 'user_id', 'description']

	sections:
		'Details':
			fields: ['localized_datetime', 'user_id', 'patient_id', 'site_id', 'form', 'form_id', 'field', 'old_value', 'new_value', 'description']

view:
	hide_cardmenu: true
	comment: 'Patient Activity Log'
	find:
		basic: ['user_id', 'patient_id', 'site_id']
	grid:
		fields: ['localized_datetime', 'user_id', 'description']
		sort: ['-localized_datetime']
	label: 'Patient Activity Log'
