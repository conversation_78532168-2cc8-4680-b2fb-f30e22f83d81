fields:

	signature:
		model:
			required: true
			type: 'json'
		view:
			note: 'I confirm to unlock the accounting period in question'
			control: 'esign'
			label: 'E-Signature'

	note:
		view:
			control: 'area'
			label: 'Notes'

model:
	access:
		create:     []
		create_all: ['admin', 'cm']
		delete:     []
		read:       ['admin', 'cm']
		read_all:   ['admin', 'cm']
		request:    []
		update:     ['cm']
		update_all: ['admin']
		write:      ['admin', 'cm']

	reportable: true
	name: ['created_on', 'created_by']
	sections:
		'Period Closing Lock':
			fields: ['signature','note']
view:
	comment: 'Period Closing Lock Log'
	grid:
		fields: ['created_on','created_by', 'note']
		sort: ['-created_on']
	label: 'Period Closing Lock Log'
