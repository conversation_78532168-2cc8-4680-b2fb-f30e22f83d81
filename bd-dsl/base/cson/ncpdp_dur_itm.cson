fields:
	patient_id:
		model:
			prefill: ['parent.patient_id']
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	transaction_code:
		model:
			prefill: ['parent.transaction_code']
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '103-A3'
			if:
				'B1': # Drug Billing
					fields: ['co_agt_itm_id', 'co_agt_id_qualifier', 'co_agt_id']
				'B3': # Drug Reverse and Bill
					fields: ['co_agt_itm_id', 'co_agt_id_qualifier', 'co_agt_id']
				'S1': # Service Billing
					fields: ['co_agt_itm_id', 'co_agt_id_qualifier', 'co_agt_id']
		view:
			label: 'Transmission Code'
			offscreen: true
			readonly: true

	rsn_for_svc_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '439-E4'
		view:
			columns: 4
			reference: '439-E4'
			note: '439-E4'
			label: 'Conflict'

	prf_svc_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '440-E5'
		view:
			columns: 4
			reference: '440-E5'
			note: '440-E5'
			label: 'Intervention'

	rst_svc_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '441-E6'
		view:
			columns: 4
			reference: '441-E6'
			note: '441-E6'
			label: 'Outcome'

	dur_pps_loe:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '474-8E'
		view:
			columns: 4
			reference: '474-8E'
			note: '474-8E'
			label: 'DUR/PPS Level of Effort'

	co_agt_itm_id:
		model:
			source: 'inventory'
			sourcefilter:
				type:
					'static': 'Drug'
				active:
					'static': 'Yes'
		view:
			form_link_enabled: true
			columns: -2
			label: 'Co-Agent Drug'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'co_agt_id': ['ndc']
			]

	co_agt_id_qualifier:
		model:
			default: '03'
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '475-J9'
			if:
				'*':
					require_fields: ['co_agt_id']
		view:
			columns: 4
			reference: '475-J9'
			note: '475-J9'
			label: 'Co-Agent Qualifier'

	co_agt_id:
		model:
			max: 19
			if:
				'*':
					require_fields: ['co_agt_id_qualifier']
		view:
			columns: 4
			reference: '476-H6'
			note: '476-H6'
			label: 'Co-Agent ID'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['transaction_code', 'rsn_for_svc_code', 'prf_svc_code']
	sections:
		'DUR/PPS':
			hide_header: true
			indent: false
			fields: ['rsn_for_svc_code', 'prf_svc_code', 'rst_svc_code', 'dur_pps_loe',
			'co_agt_itm_id', 'co_agt_id_qualifier', 'co_agt_id']

view:
	dimensions:
		width: '75%'
		height: '65%'
	comment: 'DUR/PPS'
	grid:
		fields: ['rsn_for_svc_code', 'prf_svc_code', 'rst_svc_code', 'dur_pps_loe']
		sort: ['-created_on']
	label: 'DUR/PPS'
	open: 'read'