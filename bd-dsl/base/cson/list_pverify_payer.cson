fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'
			findunique: true
			columns: 3

	name:
		model:
			required: true
			max: 128
		view:
			label: 'Name'
			findunique: true
			columns: 3

	type:
		view:
			label: 'Type'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'type']

view:
	comment: 'Manage > pVerify Payer'
	find:
		basic: ['code','name', 'type']
	grid:
		fields: ['code','name', 'type']
		sort: ['code']
	label: 'pVerify Payer'
	open: 'read'
