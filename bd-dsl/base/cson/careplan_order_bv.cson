fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			required: true
			source: 'careplan_order'
			type: 'int'
		view:
			label: 'Order'
			class: 'select_prefill'
			validate: [{
				name: 'BuildBVOrderInsuranceFilter'
			}]
			transform: [
				name: 'SelectPrefill'
				url: '/form/careplan_order/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'therapy_id': ['therapy_id']
					'ins_filter': ['insurance_id']
					'order_no': ['order_no']
			]

	order_no:
		model:
			required: true
		view:
			label: 'Order No'
			readonly: true
			offscreen: true

	therapy_id:
		model:
			required: true
			max: 64
			multi: true
			source: 'list_therapy'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Therapy'

	brand_name_id:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
			if:
				'OCREVUS':
					sections: ['Genentech Access Solutions']
				'OCREVUS ZUNOVO':
					sections: ['Genentech Access Solutions']
		view:
			columns: 2
			label: 'Drug Brand'

	genentech_enrolled:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['genentech_id']
				'No':
					fields: ['genentech_no_why', 'genentech_comment']
		view:
			control: 'radio'
			label: 'Has patient enrolled with Genentech Access Solutions?'
			columns: 2

	genentech_id:
		view:
			label: 'ID #'
			columns: 2

	genentech_no_why:
		model:
			multi: false
			source: ['Form has been sent to patient for completion',
			'Form has been submitted to Genentech for processing']
		view:
			control: 'checkbox'
			label: 'Submission Status'

	genentech_comment:
		view:
			control: 'area'
			label: 'Comments'

	ins_filter:
		model:
			source: 'patient_insurance'
			multi: true
		view:
			label: 'Filtered order insurance records'
			offscreen: true
			readonly: true

	insurance_id:
		model:
			source: 'patient_insurance'
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				id:
					'dynamic': '{ins_filter}'
				type_id:
					'static': '!SELF'
		view:
			columns: 2
			class: 'select_prefill'
			label: 'Insurance Record'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_insurance/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'payer_id': ['payer_id']
					'group_number': ['group_number']
					'bin': ['bin']
					'pcn': ['pcn']
					'person_code': ['person_code']
					'plan_name': ['plan_name']
					'effective_date': ['effective_date']
			]

	beneficiary_fname:
		model:
			prefill: ['patient.firstname']
		view:
			label: 'First Name'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'MergeFields'
					src_fields: ['beneficiary_fname', 'beneficiary_lname']
					separator: ' '
					dest_field: 'subscriber'
				}
			]

	beneficiary_lname:
		model:
			prefill: ['patient.lastname']
		view:
			label: 'Last Name'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'MergeFields'
					src_fields: ['beneficiary_fname', 'beneficiary_lname']
					separator: ' '
					dest_field: 'subscriber'
				}
			]

	payer_id:
		model:
			source: 'payer'
			required: false
		view:
			columns: 2
			label: 'Payer'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/payer/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'inn_oon': ['inn_oon'],
					'payer_representative': ['payer_representative']
					'payer_fax': ['fax']
					'payer_phone': ['phone']
					'type_id': ['type_id']
					'days_timely_filing': ['days_timely_filing']
					'req_auth_for': ['req_auth_for']
			]

	req_auth_for:
		model:
			multi: true
			default: ['Medication', 'Supplies', 'Nursing', 'DME']
			source: ['Medication', 'Supplies', 'Nursing', 'DME']
			if:
				'Supplies':
					prefill:
						supplies_auth_required: 'Yes'
				'DME':
					prefill:
						dme_auth_required: 'Yes'
		view:
			label: "Auth Required for Supplies/DME?"
			offscreen: true
			readonly: true

	payer_phone:
		model:
			required: true
			max: 21
		view:
			columns: 2
			format: 'us_phone'
			label: 'Payer Phone'

	payer_fax:
		model:
			max: 21
		view:
			columns: 2
			format: 'us_phone'
			label: 'Payer Fax'

	payer_representative:
		view:
			columns: 2
			label: 'Representative Name'

	call_reference:
		view:
			columns: 2
			label: 'Call Reference #'

	type_id:
		model:
			required: true
			source: 'list_payer_type'
			sourceid: 'code'
			if:
				'MCRB':
					fields: ['group_number', 'inn_oon', 'payer_for', 'medicare_hits']
					sections: ['Coverage']
				'MEDI':
					fields: ['group_number', 'inn_oon', 'payer_for']
					sections: ['Coverage']
				'CMMMED':
					fields: ['group_number', 'inn_oon', 'payer_for']
					sections: ['Coverage']
				'CMPBM':
					fields: ['plan_name', 'bin', 'pcn', 'med_auth_required', 'hcpc_id']
					sections: ['Coverage']
				'MCRD':
					fields: ['bin', 'pcn', 'inn_oon', 'medicare_hits', 'med_auth_required', 'hcpc_id']
					sections: ['Coverage']
				'*':
					fields: ['inn_oon', 'payer_for']
					sections: ['Coverage']
		view:
			columns: 2
			control: 'select'
			label: 'Type'
			readonly: true
			offscreen: true

	payer_for:
		model:
			multi: true
			default: ['Medication', 'Supplies', 'Nursing', 'DME']
			source: ['Medication', 'Supplies', 'Nursing', 'DME']
			if:
				'Medication':
					fields: ['med_auth_required', 'hcpc_id']
				'Supplies':
					fields: ['supplies_auth_required', 'sply_code_id']
				'Nursing':
					fields: ['nurse_auth_required', 'nurse_code_id']
				'DME':
					fields: ['dme_auth_required', 'dme_code_id']
		view:
			control: 'checkbox'
			label: 'Payor for:'
			columns: 2

	effective_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 2
			label: 'Effective Date'

	group_number:
		model:
			required: true
		view:
			columns: 2
			label: 'Group #'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{1,20}$'
				error: 'Invalid Group Number, must be 1-20 alphanumeric characters'
			}]

	subscriber:
		model:
			required: true
		view:
			columns: 2
			label: 'Policy Subscriber'

	plan_name:
		view:
			columns: 2
			label: 'Plan Name'

	bin:
		model:
			required: true
		view:
			columns: 2
			label: 'BIN #'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{6}$'
				error: 'Invalid BIN, must be 6 digits'
			}]

	pcn:
		model:
			required: true
		view:
			columns: 2
			label: 'PCN #'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{1,10}$'
				error: 'Invalid PCN, must be 1-10 alphanumeric characters'
			}]

	person_code:
		view:
			columns: 2
			label: 'Person Code'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{1,4}$'
				error: 'Invalid Person Code, must be 1-4 alphanumeric characters'
			}]

	plan_year:
		view:
			columns: 2
			label: 'Calendar/Policy Year'
			transform: [{
				name: 'MomentPrefill'
				format: 'YYYY'
			}]

	#Prefill from payer record
	days_timely_filing:
		model:
			type: 'int'
		view:
			columns: 2
			note: 'days'
			label: 'Timely Filing'

	inn_deductible:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 2
			class: 'numeral money'
			format: '$0,0.00'
			label: 'INN Deductible'

	onn_deductible:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 2
			class: 'numeral money'
			format: '$0,0.00'
			label: 'ONN Deductible'

	inn_oon:
		model:
			required: true
			source: ['INN', 'OON', 'Network doesn’t apply']
			if:
				'INN':
					fields: ['onn_deductible', 'onn_ded_met', 'onn_coinsurance', 'onn_oop_max', 'onn_oop_met', 'onn_cross_accum', 'inn_deductible', 'inn_ded_met', 'inn_coinsurance', 'inn_oop_max', 'inn_oop_met']
				'OON':
					fields: ['onn_deductible', 'onn_ded_met', 'onn_coinsurance', 'onn_oop_max', 'onn_oop_met', 'onn_cross_accum', 'inn_deductible', 'inn_ded_met', 'inn_coinsurance', 'inn_oop_max', 'inn_oop_met']
		view:
			columns: 2
			control: 'radio'
			label: 'INN or OON?'

	inn_oop_max:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 2
			class: 'numeral money'
			format: '$0,0.00'
			label: 'INN OOP Max'

	inn_oop_met:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 2
			class: 'numeral money'
			format: '$0,0.00'
			label: 'INN OOP Met'

	inn_ded_met:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 2
			class: 'numeral money'
			format: '$0,0.00'
			label: 'INN Deductible Met'

	inn_coinsurance:
		view:
			columns: 2
			label: 'INN Coinsurance'

	onn_oop_max:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 2
			class: 'numeral money'
			format: '$0,0.00'
			label: 'ONN OOP Max'

	onn_oop_met:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 2
			class: 'numeral money'
			format: '$0,0.00'
			label: 'ONN OOP Met'

	onn_ded_met:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 2
			class: 'numeral money'
			format: '$0,0.00'
			label: 'ONN Deductible Met'

	onn_coinsurance:
		view:
			columns: 2
			label: 'ONN Coinsurance'

	onn_cross_accum:
		view:
			columns: 2
			label: 'Cross Accumulation'

	copay_accumulator:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['copay_accumulator_amount']
		view:
			columns: 2
			control: 'radio'
			label: 'Copay Accumulator?'

	copay_accumulator_amount:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 2
			class: 'numeral money'
			format: '$0,0.00'
			label: 'How much left?'

	med_auth_required:
		model:
			prefill: ['careplan_order_bv']
			default: 'Yes'
			source: ['No', 'Yes']
			required: true
		view:
			columns: 2
			control: 'radio'
			label: 'Prior Auth Required?'

	hcpc_id:
		model:
			prefill: ['careplan_order_bv']
			multi: true
			source: 'list_fdb_medicare_desc'
			sourceid: 'code'
		view:
			columns: 2
			label: 'HCPC Codes'
			class: 'fdb-field'

	nurse_auth_required:
		model:
			prefill: ['careplan_order_bv']
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Nursing Authorization Required?'

	nurse_code_id:
		model:
			prefill: ['careplan_order_bv']
			multi: true
			source: 'list_billing_code'
			sourceid: 'code'
			sourcefilter:
				code_type:
					'static': 'CPT'
		view:
			columns: 2
			label: 'Nursing Billable Codes'

	supplies_auth_required:
		model:
			prefill: ['careplan_order_bv']
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Supplies Authorization Required?'

	sply_code_id:
		model:
			prefill: ['careplan_order_bv']
			multi: true
			source: 'list_billing_code'
			sourceid: 'code'
			sourcefilter:
				code_type:
					'static': ['HCPC', 'A-Code', 'S-Code', 'B-Code', 'K-Code', 'G-Code']
		view:
			columns: 2
			label: 'Supplies Billable Codes'

	dme_auth_required:
		model:
			prefill: ['careplan_order_bv']
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'DME Authorization Required?'

	dme_code_id:
		model:
			prefill: ['careplan_order_bv']
			multi: true
			source: 'list_billing_code'
			sourceid: 'code'
			sourcefilter:
				code_type:
					'static': ['HCPC', 'B-Code', 'E-Code', 'K-Code']
		view:
			columns: 2
			label: 'DME Billable Codes'

	#SCIG only
	medicare_hits:
		model:
			prefill: ['careplan_order_bv']
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			note: 'SCIG Only'
			label: 'Medicare HITS Required?'

	copay:
		model:
			rounding: 0.01
			type: 'decimal'
			min: 0
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Copay'

	comment:
		view:
			columns: 2
			control: 'area'
			label: 'Comments'

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'careplan_order_bv'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Other Form'
					direct_attachment: 'Yes'
					source: 'Scanned Document'
					form_code: '{code}'
					form_name: 'careplan_order_bv'
					form_filter: 'careplan_order_bv'
					patient_id: '{patient_id}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'
model:
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan_order_bv:
			link:
				payer_id: 'payer_id'
				brand_name_id: 'brand_name_id'
			max: 'created_on'
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'pharm']
		delete:     ['admin', 'csr', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'pharm']
	name: '{insurance_id} {type_id} ({inn_oon})'
	indexes:
		many: [
			['patient_id']
			['order_id']
			['order_no']
			['insurance_id']
			['type_id']
			['brand_name_id']
			['payer_id']
		]
		unique: [
			['code']
		]
	sections:
		'Payer Details':
			hide_header: true
			indent: true
			tab: 'Verification'
			fields: ['code', 'order_id', 'order_no', 'therapy_id', 'brand_name_id', 'ins_filter', 'insurance_id', 'payer_id',
			'req_auth_for', 'type_id', 'inn_oon', 'payer_phone', 'payer_fax',
			'payer_representative', 'call_reference', 'group_number', 'bin', 'pcn', 'person_code']
		'Benefits Summary':
			hide_header: true
			indent: true
			tab: 'Verification'
			fields: ['effective_date', 'beneficiary_fname', 'beneficiary_lname', 'subscriber', 'plan_name', 'plan_year', 'days_timely_filing',
			'inn_deductible', 'inn_ded_met', 'inn_coinsurance', 'inn_oop_max', 'inn_oop_met',
			'onn_deductible', 'onn_ded_met', 'onn_coinsurance', 'onn_oop_max',
			'onn_oop_met', 'onn_cross_accum', 'copay_accumulator', 'copay_accumulator_amount', 'copay']
		'Medicare HITS':
			hide_header: true
			indent: true
			tab: 'Verification'
			fields: ['medicare_hits']
		'Comments':
			hide_header: true
			indent: true
			tab: 'Verification'
			fields: ['comment']
		'Coverage':
			hide_header: true
			indent: true
			tab: 'Coverage'
			fields: ['payer_for', 'med_auth_required', 'hcpc_id', 'nurse_auth_required', 'nurse_code_id',
			'supplies_auth_required', 'sply_code_id', 'dme_auth_required', 'dme_code_id']
		'Genentech Access Solutions':
			hide_header: true
			indent: true
			tab: 'Genentech'
			fields: ['genentech_enrolled', 'genentech_id','genentech_no_why', 'genentech_comment']
		'Documents':
			fields: ['embed_document']
			tab: 'Assigned Documents'
view:
	hide_cardmenu: true
	comment: 'Patient > Intake Benefits Verification'
	find:
		basic: ['insurance_id', 'type_id', 'inn_oon']
	grid:
		fields: ['created_on', 'created_by', 'insurance_id', 'type_id', 'inn_oon']
	label: 'Patient Intake Benefits Verification'
	open: 'read'
