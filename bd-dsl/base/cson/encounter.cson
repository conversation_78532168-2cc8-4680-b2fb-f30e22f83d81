fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true

	locked:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['locked_datetime', 'locked_by']
		view:
			control: 'checkbox'
			label: 'Locked'
			readonly: true
			offscreen: true

	locked_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Locked Date/Time'
			readonly: true
			offscreen: true

	locked_by:
		model:
			source: 'user'
		view:
			label: 'Locked By'
			readonly: true
			offscreen: true

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	agency_id:
		model:
			required: true
			source: 'nurse_agency'
			type: 'int'
		view:
			label: 'Nursing Agency'
			class: 'select_prefill'
			columns: 3
			transform: [
				name: 'SelectPrefill'
				url: '/form/nurse_agency/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'agency_does_billing': ['agency_does_billing'],
					'external_note_contact_check': ['uses_external_nursing_note']
					'uses_external_nursing_note': ['uses_external_nursing_note']
			]

	agency_does_billing:
		model:
			required: false
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Does agency handle their own billing?'
			note: 'If yes, associated nursing notes will not generate billable charges'
			readonly: true
			offscreen: true

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			readonly: true

	contact_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Contact Date'
			columns: 2

	contact_location:
		model:
			source:
				infusion_center: 'Infusion Center'
				physician_office: 'Physician Office'
				home: "Patient's Home"
				other: 'Other'
			if:
				'home':
					fields: ['external_note_contact_check']
		view:
			control: 'radio'
			label: 'Contact Location'
			columns: 2

	delivery_ticket_id:
		model:
			prefill: ['careplan_delivery_tick.id']
			required: true
			source: 'careplan_delivery_tick'
			type: 'int'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				void:
					'static': '!Yes'
				verified:
					'static': 'Yes'
			if:
				'*':
					fields: ['order_id']
		view:
			form_link_enabled: true
			label: 'Delivery Ticket'
			class: 'select_prefill'
			columns: 3
			transform: [
				{
				name: 'SelectPrefill'
				url: '/form/careplan_delivery_tick/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'site_id': ['site_id']
					'order_id': ['order_id']
					'order_no': ['order_no']
				},
				{
					name: 'AddVisitNumber'
				}
			]

	visit_number:
		model:
			required: true
			type: 'int'
		view:
			label: 'Visit Number'
			readonly: true
			columns: 3

	order_id:
		model:
			required: false
			source: 'careplan_order'
			type: 'int'
		view:
			label: 'Referral'
			readonly: true
			columns: -2

	order_no:
		view:
			columns: 3
			label: 'Referral No'
			readonly: true

	external_note_contact_check:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					sections: ['Analphylaxis Kit']
					fields: ['environment_assessment_id', 'check_fall_score']
		view:
			control: 'radio'
			label: 'Agency Uses External Nursing Note?'
			readonly: true
			offscreen: true

	uses_external_nursing_note:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['patient_medical_hx', 'contact_reason', 'nurse_careplan_type', 'labs_are_due']
					sections: ['Medical History', 'Medication Profile', 'Allergies']
		view:
			control: 'radio'
			label: 'Agency Uses External Nursing Note?'
			readonly: true
			offscreen: true

	labs_are_due:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['subform_lab_draw']
					sections: ['Labs']
		view:
			control: 'checkbox'
			label: 'Labs are due'
			readonly: true
			offscreen: true

	has_premeds:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['premeds']
					sections: ['Pre-Medications']
		view:
			control: 'checkbox'
			label: 'Has Pre-Medications'
			readonly: true
			offscreen: true

	has_postmeds:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['postmeds']
					sections: ['Post-Medications']
		view:
			control: 'checkbox'
			label: 'Has Post-Medications'
			readonly: true
			offscreen: true

	has_hydration:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['prehydration', 'posthydration']
					sections: ['Pre-Hydration', 'Post-Hydration']
		view:
			control: 'checkbox'
			label: 'Has Hydration'
			readonly: true
			offscreen: true

	has_flushes:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['subform_flush']
					sections: ['Flushes']
		view:
			control: 'checkbox'
			label: 'Has Flushes'
			readonly: true
			offscreen: true

	route_id:
		model:
			required: false
			source: 'list_route'
			sourceid: 'code'
			if:
				'IV':
					sections: ['Catheter Log', 'Catheter Access Assessment']
					fields: ['catheter_log', 'subform_access']
				'PEG':
					sections: ['Catheter Log', 'Catheter Access Assessment']
					fields: ['catheter_log', 'subform_access']
				'IVP':
					sections: ['Catheter Log', 'Catheter Access Assessment']
					fields: ['catheter_log', 'subform_access']
		view:
			readonly: true
			offscreen: true
			label: 'Primary Drug Route'

	patient_medical_hx:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				end_date:
					'dynamic': ''
		view:
			embed:
				form: 'patient_medical_hx'
				selectable: false
			grid:
				add: 'none'
				edit: true
				rank: 'none'
				fields: ['created_by', 'created_on', 'medical_hist', 'medical_hist_desc']
				label: ['Created By', 'Created On', 'Medical History', 'Details']
				width: [20, 15, 35, 30]
			label: 'Medical History'
			readonly: true

	subform_lab_draw:
		model:
			multi: true
			required: true
			source: 'encounter_lab_draw'
		view:
			grid:
				add: 'none'
				edit: false
				fields: ['draw_datetime', 'lab_final', 'labs_source', 'amount_drawn']
				label: ['Draw Dt/Tm', 'Lab', 'Source', 'Amt Drawn (ml)']
				width: [25, 20, 30, 25]
			label: 'Active Lab Orders'

	patient_medications:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': 'Active'
		view:
			embed:
				form: 'patient_medication'
				selectable: false
				add_preset:
					reported_by: 'Nurse'
					status: 'Active'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				rank: 'none'
				fields: ['fdb_id', 'medication_dose', 'medication_frequency', 'start_date']
				label: ['Medication', 'Dose', 'Frequency', 'Start Dt']
				width: [35, 25, 25, 15]
			label: 'Active Medications'

	patient_interactions:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			readonly: true
			embed:
				form: 'interactions'
				selectable: false
			grid:
				add: 'none'
				hide_cardmenu: true
				edit: true
				rank: 'none'
				fields: ['created_by', 'has_da', 'has_dd']
				label: ['Created By','Drug Allergy', 'Drug Drug']
				width: [30, 30, 30]
			label: 'Interactions'

	patient_interaction_btn:
		model:
			source: ['Get Interactions']
		view:
			columns: 2
			class: 'dsl-button'
			control: 'checkbox'
			label: 'Patient Interaction'
			validate: [
				name: 'DrugAllergyInteraction'
			]

	patient_allergies:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				end_date:
					'dynamic': ''
		view:
			embed:
				form: 'patient_allergy'
				selectable: false
				add_preset:
					active: 'Yes'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['allergen_id', 'reaction_id', 'severity_id', 'start_date']
				label: ['Allergen', 'Reaction', 'Severity', 'Start Dt']
				width: [35, 25, 25, 15]
			label: 'Active Allergies'

	subform_environment:
		model:
			multi: false
			source: 'encounter_environment'
			type: 'subform'
		view:
			label: 'Environment Assessment'

	embed_environment:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				end_date:
					'dynamic': ''
		view:
			embed:
				form: 'encounter_environment'
				selectable: false
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['created_on', 'created_by', 'cand', 'comments']
				label: ['Created On', 'Created By', 'Candidacy', 'Comments']
				width: [25, 25, 15, 35]
			label: 'Previous Environment Assessment'

	# Analphylaxis Kit
	anaphylaxis_kit:
		model:
			source: ['No', 'Yes', 'NA']
			if:
				'No':
					fields: ['anaphylaxis_kit_why', 'anaphylaxis_kit_notified']
				'Yes':
					fields: ['anaphylaxis_kit_expire']
		view:
			label: 'Anaphylaxis kit in home?'
			columns: -2

	anaphylaxis_kit_why:
		model:
			required: true
		view:
			label: 'Reason'
			columns: 2

	anaphylaxis_kit_notified:
		model:
			required: true
			source: ['Yes', 'No']
			if:
				'No':
					fields: ['pharmacy_not_notified_why']
		view:
			control: 'checkbox'
			label: 'Pharmacy notified?'
			columns: 2

	pharmacy_not_notified_why:
		view:
			label: 'Pharmacy not Notified Reason'
			columns: 2

	anaphylaxis_kit_expire:
		model:
			required: true
			type: 'date'
		view:
			control: 'radio'
			label: 'Anaphylaxis kit Expiration Date'
			columns: 2

	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections: ['Bleed Log', 'Primary Therapy Questionnaire', 'Primary Therapy Assessment', 'Primary Therapy Post Visit']
					fields: ['bleed_log', 'subform_therapy_1']
				'*':
					sections: ['Primary Therapy Questionnaire', 'Primary Therapy Assessment', 'Primary Therapy Post Visit']
					fields: ['subform_therapy_1']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Therapy'

	therapy_2:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections: ['Bleed Log', 'Secondary Therapy Questionnaire', 'Secondary Therapy Assessment', 'Secondary Therapy Post Visit']
					fields: ['bleed_log', 'subform_therapy_2']
				'*':
					sections: ['Secondary Therapy Questionnaire', 'Secondary Therapy Assessment', 'Secondary Therapy Post Visit']
					fields: ['subform_therapy_2']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Therapy'

	therapy_3:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections: ['Bleed Log', 'Tertiary Therapy Questionnaire', 'Tertiary Therapy Assessment', 'Tertiary Therapy Post Visit']
					fields: ['bleed_log', 'subform_therapy_3']
				'*':
					sections: ['Tertiary Therapy Questionnaire', 'Tertiary Therapy Assessment', 'Tertiary Therapy Post Visit']
					fields: ['subform_therapy_3']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Therapy'

	therapy_4:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections: ['Bleed Log', 'Quaternary Therapy Questionnaire', 'Quaternary Therapy Assessment', 'Quaternary Therapy Post Visit']
					fields: ['bleed_log', 'subform_therapy_4']
				'*':
					sections: ['Quaternary Therapy Questionnaire', 'Quaternary Therapy Assessment', 'Quaternary Therapy Post Visit']
					fields: ['subform_therapy_4']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Therapy'

	therapy_5:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections: ['Bleed Log', 'Quinary Therapy Questionnaire', 'Quinary Therapy Assessment', 'Quinary Therapy Post Visit']
					fields: ['bleed_log', 'subform_therapy_5']
				'*':
					sections: ['Quinary Therapy Questionnaire', 'Quinary Therapy Assessment', 'Quinary Therapy Post Visit']
					fields: ['subform_therapy_5']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Therapy'

	subform_therapy_1:
		model:
			source: 'encounter_{therapy_1}'
			sourcefilter:
				'encounter_tysabri': {}
				'encounter_antibiotic': {}
				'encounter_hepc': {}
				'encounter_hepb': {}
				'encounter_lemtrada': {}
				'encounter_biologic': {}
				'encounter_ms': {}
				'encounter_nursing_admin': {}
				'encounter_enzyme': {}
				'encounter_hiv': {}
				'encounter_factor': {}
				'encounter_aat': {}
				'encounter_ig': {}
				'encounter_subqig': {}
				'encounter_tnf': {}
				'encounter_tpn': {}
				'encounter_steroid': {}
#				'encounter_inotrope': {}
#				'encounter_chemotherapy': {}
#				'encounter_bisphosphonates': {}
			type: 'subform'
		view:
			label: 'Primary Therapy Assessment'

	subform_therapy_2:
		model:
			source: 'encounter_{therapy_2}'
			sourcefilter:
				'encounter_tysabri': {}
				'encounter_antibiotic': {}
				'encounter_hepc': {}
				'encounter_hepb': {}
				'encounter_lemtrada': {}
				'encounter_biologic': {}
				'encounter_ms': {}
				'encounter_nursing_admin': {}
				'encounter_enzyme': {}
				'encounter_hiv': {}
				'encounter_factor': {}
				'encounter_aat': {}
				'encounter_ig': {}
				'encounter_subqig': {}
				'encounter_tnf': {}
				'encounter_tpn': {}
				'encounter_steroid': {}
#				'encounter_inotrope': {}
#				'encounter_chemotherapy': {}
#				'encounter_bisphosphonates': {}
			type: 'subform'
		view:
			label: 'Secondary Therapy Assessment'

	subform_therapy_3:
		model:
			source: 'encounter_{therapy_3}'
			sourcefilter:
				'encounter_factor': {}
				'encounter_aat': {}
				'encounter_ig': {}
				'encounter_subqig': {}
				'encounter_tnf': {}
				'encounter_tpn': {}
				'encounter_steroid': {}
#				'encounter_inotrope': {}
#				'encounter_chemotherapy': {}
#				'encounter_bisphosphonates': {}
			type: 'subform'
		view:
			label: 'Tertiary Therapy Assessment'

	subform_therapy_4:
		model:
			source: 'encounter_{therapy_4}'
			sourcefilter:
				'encounter_factor': {}
				'encounter_aat': {}
				'encounter_ig': {}
				'encounter_subqig': {}
				'encounter_tnf': {}
				'encounter_tpn': {}
				'encounter_steroid': {}
#				'encounter_inotrope': {}
#				'encounter_chemotherapy': {}
#				'encounter_bisphosphonates': {}
			type: 'subform'
		view:
			label: 'Quaternary Therapy Assessment'

	subform_therapy_5:
		model:
			source: 'encounter_{therapy_5}'
			sourcefilter:
				'encounter_factor': {}
				'encounter_aat': {}
				'encounter_ig': {}
				'encounter_subqig': {}
				'encounter_tnf': {}
				'encounter_tpn': {}
				'encounter_steroid': {}
#				'encounter_inotrope': {}
#				'encounter_chemotherapy': {}
#				'encounter_bisphosphonates': {}
			type: 'subform'
		view:
			label: 'Quinary Therapy Assessment'

	subform_route_admin:
		model:
			multi: true
			source: 'encounter_{route_id}_admin'
			sourcefilter:
				'encounter_iv_admin': {}
				'encounter_im_admin': {}
				'encounter_inj_admin': {}
				'encounter_ivp_admin': {}
				'encounter_nas_admin': {}
				'encounter_opth_admin': {}
				'encounter_otc_admin': {}
				'encounter_peg_admin': {}
				'encounter_po_admin': {}
				'encounter_rec_admin': {}
				'encounter_sl_admin': {}
				'encounter_sq_admin': {}
				'encounter_top_admin': {}
			type: 'subform'
		view:
			label: 'Administration Assessment'

	brand_1:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Primary Brand Questionnaire', 'Primary Brand Assessment', 'Primary Brand Post Visit']
					fields: ['subform_brand_1']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Brand'

	brand_2:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Secondary Brand Questionnaire', 'Secondary Brand Assessment', 'Secondary Brand Post Visit']
					fields: ['subform_brand_2']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Brand'

	brand_3:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Tertiary Brand Questionnaire', 'Tertiary Brand Assessment', 'Tertiary Brand Post Visit']
					fields: ['subform_brand_3']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Brand'

	brand_4:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Quaternary Brand Questionnaire', 'Quaternary Brand Assessment', 'Quaternary Brand Post Visit']
					fields: ['subform_brand_4']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Brand'

	brand_5:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Quinary Brand Questionnaire', 'Quinary Brand Assessment', 'Quinary Brand Post Visit']
					fields: ['subform_brand_5']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Brand'

	subform_brand_1:
		model:
			source: 'encounter_{brand_1}'
			sourcefilter:
				'encounter_cimzia': {}
				'encounter_dupixent': {}
				'encounter_enbrel': {}
				'encounter_humira': {}
				'encounter_krystexxa': {}
				'encounter_lemtrada': {}
				'encounter_ocrevus': {}
				'encounter_orencia': {}
				'encounter_radicava': {}
				'encounter_remicade': {}
				'encounter_rituxan': {}
				'encounter_simponi': {}
				'encounter_simponiaria': {}
				'encounter_soliris': {}
				'encounter_stelara': {}
				'encounter_tysabri': {}
				'encounter_vyvgart': {}
				'encounter_vancomycin': {}
				'encounter_methyl': {}
			type: 'subform'
		view:
			label: 'Primary Drug Brand Assessment'

	subform_brand_2:
		model:
			source: 'encounter_{brand_2}'
			sourcefilter:
				'encounter_cimzia': {}
				'encounter_dupixent': {}
				'encounter_enbrel': {}
				'encounter_humira': {}
				'encounter_krystexxa': {}
				'encounter_lemtrada': {}
				'encounter_ocrevus': {}
				'encounter_orencia': {}
				'encounter_radicava': {}
				'encounter_remicade': {}
				'encounter_rituxan': {}
				'encounter_simponi': {}
				'encounter_simponiaria': {}
				'encounter_soliris': {}
				'encounter_stelara': {}
				'encounter_tysabri': {}
				'encounter_vyvgart': {}
				'encounter_vancomycin': {}
				'encounter_methyl': {}
			type: 'subform'
		view:
			label: 'Secondary Drug Brand Assessment'

	subform_brand_3:
		model:
			source: 'encounter_{brand_3}'
			sourcefilter:
				'encounter_cimzia': {}
				'encounter_dupixent': {}
				'encounter_enbrel': {}
				'encounter_humira': {}
				'encounter_krystexxa': {}
				'encounter_lemtrada': {}
				'encounter_ocrevus': {}
				'encounter_orencia': {}
				'encounter_radicava': {}
				'encounter_remicade': {}
				'encounter_rituxan': {}
				'encounter_simponi': {}
				'encounter_simponiaria': {}
				'encounter_soliris': {}
				'encounter_stelara': {}
				'encounter_tysabri': {}
				'encounter_vyvgart': {}
				'encounter_vancomycin': {}
				'encounter_methyl': {}
			type: 'subform'
		view:
			label: 'Tertiary Drug Brand Assessment'

	subform_brand_4:
		model:
			source: 'encounter_{brand_4}'
			sourcefilter:
				'encounter_cimzia': {}
				'encounter_dupixent': {}
				'encounter_enbrel': {}
				'encounter_humira': {}
				'encounter_krystexxa': {}
				'encounter_lemtrada': {}
				'encounter_ocrevus': {}
				'encounter_orencia': {}
				'encounter_radicava': {}
				'encounter_remicade': {}
				'encounter_rituxan': {}
				'encounter_simponi': {}
				'encounter_simponiaria': {}
				'encounter_soliris': {}
				'encounter_stelara': {}
				'encounter_tysabri': {}
				'encounter_vyvgart': {}
				'encounter_vancomycin': {}
				'encounter_methyl': {}
			type: 'subform'
		view:
			label: 'Quaternary Drug Brand Assessment'

	subform_brand_5:
		model:
			source: 'encounter_{brand_5}'
			sourcefilter:
				'encounter_cimzia': {}
				'encounter_dupixent': {}
				'encounter_enbrel': {}
				'encounter_humira': {}
				'encounter_krystexxa': {}
				'encounter_lemtrada': {}
				'encounter_ocrevus': {}
				'encounter_orencia': {}
				'encounter_radicava': {}
				'encounter_remicade': {}
				'encounter_rituxan': {}
				'encounter_simponi': {}
				'encounter_simponiaria': {}
				'encounter_soliris': {}
				'encounter_stelara': {}
				'encounter_tysabri': {}
				'encounter_vyvgart': {}
				'encounter_vancomycin': {}
				'encounter_methyl': {}
			type: 'subform'
		view:
			label: 'Quinary Drug Brand Assessment'

	disease_1:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Primary Disease Questionnaire', 'Primary Disease Assessment', 'Primary Disease Post Visit']
					fields: ['subform_disease_1']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Disease'

	disease_2:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Secondary Disease Questionnaire', 'Secondary Disease Assessment', 'Secondary Disease Post Visit']
					fields: ['subform_disease_2']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Disease'

	disease_3:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Tertiary Disease Questionnaire', 'Tertiary Disease Assessment', 'Tertiary Disease Post Visit']
					fields: ['subform_disease_3']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Disease'

	disease_4:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Quaternary Disease Questionnaire', 'Quaternary Disease Assessment', 'Quaternary Disease Post Visit']
					fields: ['subform_disease_4']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Disease'

	disease_5:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Quinary Disease Questionnaire', 'Quinary Disease Assessment', 'Quinary Disease Post Visit']
					fields: ['subform_disease_5']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Disease'

	subform_disease_1:
		model:
			source: 'encounter_{disease_1}'
			sourcefilter:
				'encounter_asthma': {}
				'encounter_hiv': {}
				'encounter_hepc': {}
				'encounter_hepb': {}
				'encounter_hemob': {}
				'encounter_hemoa': {}
				'encounter_ra': {}
				'encounter_psoriasis': {}
				'encounter_ms': {}
				'encounter_vwd': {}
			type: 'subform'
		view:
			label: 'Primary Disease Assessment'

	subform_disease_2:
		model:
			source: 'encounter_{disease_2}'
			sourcefilter:
				'encounter_asthma': {}
				'encounter_hiv': {}
				'encounter_hepc': {}
				'encounter_hepb': {}
				'encounter_hemob': {}
				'encounter_hemoa': {}
				'encounter_ra': {}
				'encounter_psoriasis': {}
				'encounter_ms': {}
				'encounter_vwd': {}
			type: 'subform'
		view:
			label: 'Secondary Disease Assessment'

	subform_disease_3:
		model:
			source: 'encounter_{disease_3}'
			sourcefilter:
				'encounter_asthma': {}
				'encounter_hiv': {}
				'encounter_hepc': {}
				'encounter_hepb': {}
				'encounter_hemob': {}
				'encounter_hemoa': {}
				'encounter_ra': {}
				'encounter_psoriasis': {}
				'encounter_ms': {}
				'encounter_vwd': {}
			type: 'subform'
		view:
			label: 'Tertiary Disease Assessment'

	subform_disease_4:
		model:
			source: 'encounter_{disease_4}'
			sourcefilter:
				'encounter_asthma': {}
				'encounter_hiv': {}
				'encounter_hepc': {}
				'encounter_hepb': {}
				'encounter_hemob': {}
				'encounter_hemoa': {}
				'encounter_ra': {}
				'encounter_psoriasis': {}
				'encounter_ms': {}
				'encounter_vwd': {}
			type: 'subform'
		view:
			label: 'Quaternary Disease Assessment'

	subform_disease_5:
		model:
			source: 'encounter_{disease_5}'
			sourcefilter:
				'encounter_asthma': {}
				'encounter_hiv': {}
				'encounter_hepc': {}
				'encounter_hepb': {}
				'encounter_hemob': {}
				'encounter_hemoa': {}
				'encounter_ra': {}
				'encounter_psoriasis': {}
				'encounter_ms': {}
				'encounter_vwd': {}
			type: 'subform'
		view:
			label: 'Quinary Disease Assessment'

	clinical_1:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Primary Clinical Assessment']
					fields: ['subform_clinical_1']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Clinical Assessment'

	clinical_2:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Secondary Clinical Assessment']
					fields: ['subform_clinical_2']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Clinical Assessment'

	clinical_3:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Tertiary Clinical Assessment']
					fields: ['subform_clinical_3']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Clinical Assessment'

	clinical_4:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Quaternary Clinical Assessment']
					fields: ['subform_clinical_4']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Clinical Assessment'

	clinical_5:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Quinary Clinical Assessment']
					fields: ['subform_clinical_5']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Clinical Assessment'

	subform_clinical_1:
		model:
			source: 'clinical_{clinical_1}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
				'clinical_pain': {}
			type: 'subform'
		view:
			label: 'Primary Clinical Assessment'

	subform_clinical_2:
		model:
			source: 'clinical_{clinical_2}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
				'clinical_pain': {}
			type: 'subform'
		view:
			label: 'Secondary Clinical Assessment'

	subform_clinical_3:
		model:
			source: 'clinical_{clinical_3}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
				'clinical_pain': {}
			type: 'subform'
		view:
			label: 'Tertiary Clinical Assessment'

	subform_clinical_4:
		model:
			source: 'clinical_{clinical_4}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
				'clinical_pain': {}
			type: 'subform'
		view:
			label: 'Quaternary Clinical Assessment'

	subform_clinical_5:
		model:
			source: 'clinical_{clinical_5}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
				'clinical_pain': {}
			type: 'subform'
		view:
			label: 'Quinary Clinical Assessment'

	check_fall_score:
		model:
			prefill: ['checklist_fall.score']
			if:
				'*':
					fields: ['fall_risk_score']
					sections: ['Fall Risk Score']
				'!':
					sections: ['Fall Risk Assessment']
					fields: ['subform_checklist_fall']
			required: false
			type: 'int'
		view:
			label: 'Fall Risk Score'
			readonly: true
			offscreen: true

	subform_checklist_fall:
		model:
			multi: false
			source: 'checklist_fall'
			type: 'subform'
		view:
			label: 'Fall Risk Assessment'

	environment_assessment_id:
		model:
			prefill: ['encounter_environment.id']
			required: false
			source: 'encounter_environment'
			type: 'int'
			if:
				'*':
					fields: ['embed_environment']
					sections: ['Previous Environment Assessments']
				'!':
					fields: ['subform_environment']
					sections: ['Environment Assessment']
		view:
			offscreen: true
			readonly: true
			label: 'Environment Assessment'

	travel_time_st:
		model:
			type: 'time'
		view:
			label: 'Travel Time To Start'
			note: "Start time of the travel to the patient's location"
			columns: 2
			transform: [
					name: 'TimeDifference'
					fields: ['travel_time_st', 'travel_time_ed', 'travel_time_from_st', 'travel_time_from_ed', 'travel_time_total']
			]

	travel_time_ed:
		model:
			type: 'time'
		view:
			label: 'Travel Time To End'
			note: "End time of the travel to the patient's location"
			columns: 2
			transform: [
					name: 'TimeDifference'
					fields: ['travel_time_st', 'travel_time_ed', 'travel_time_from_st', 'travel_time_from_ed', 'travel_time_total']
			]

	time_in:
		model:
			required: true
			type: 'time'
		view:
			note: "Start time of the visit"
			label: 'Visit Time In'
			columns: 2
			transform: [
					name: 'TimeDifference'
					fields: ['time_in', 'time_out', 'time_total']
			]

	contact_reason:
		model:
			required: true
			multi: true
			source: ['Assessment', 'Instructions/Demonstration', 'Port Access',
			'IV Start/Restart', 'Line Care', 'Medication Admin',
			'Dressing Change', 'Teaching Only', 'Other']
			if:
				'Other':
					sections: ['Signatures Requested']
					fields: ['contact_details']
				'Assessment':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments', 'Measurement Log', 'Review Of Systems', 'Response to Therapy/Teaching-Education Reviewed', 'Signatures Requested']
					fields: ['subform_ros','measurement_log']
				'Instructions/Demonstration':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments', 'Measurement Log', 'Review Of Systems', 'Response to Therapy/Teaching-Education Reviewed', 'Signatures Requested']
					fields: ['subform_ros', 'measurement_log']
				'Port Access':
					sections: ['Vital Signs (Baseline)', 'Measurement Log',
					'Review Of Systems', 'Response to Therapy/Teaching-Education Reviewed', 'Signatures Requested']
					fields: ['subform_ros','measurement_log']
				'IV Start/Restart':
					sections: ['Vital Signs (Baseline)','Measurement Log',
					'Review Of Systems', 'Response to Therapy/Teaching-Education Reviewed', 'Signatures Requested']
					fields: ['subform_ros', 'measurement_log']
				'Line Care':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments',
					'Measurement Log', 'Review Of Systems',
					'Response to Therapy/Teaching-Education Reviewed', 'Signatures Requested']
					fields: ['subform_ros', 'measurement_log']
				'Medication Admin':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments', 'Lots Used',
					'Infusion/Vitals Log', 'Measurement Log', 'Drug Admin', 'Review Of Systems',
					'Response to Therapy/Teaching-Education Reviewed', 'Signatures Requested']
					fields: ['subform_ros', 'measurement_log'
					'therapy_1', 'therapy_2', 'therapy_3', 'therapy_4', 'therapy_5',
					'clinical_1', 'clinical_2', 'clinical_3', 'clinical_4', 'clinical_5',
					'disease_1', 'disease_2', 'disease_3', 'disease_4', 'disease_5',
					'brand_1', 'brand_2', 'brand_3', 'brand_4', 'brand_5',
					'subform_route_admin', 'subform_vital', 'subform_lots', 'route_id']
				'Dressing Change':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments',
					'Measurement Log', 'Review Of Systems',
					'Response to Therapy/Teaching-Education Reviewed', 'Signatures Requested']
					fields: ['subform_ros', 'measurement_log']
				'Teaching Only':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments',
					'Measurement Log', 'Review Of Systems',
					'Response to Therapy/Teaching-Education Reviewed', 'Signatures Requested']
					fields: ['subform_ros', 'measurement_log']
		view:
			control: 'checkbox'
			note: 'Select all that apply'
			label: 'Reason for Visit'
			columns: -2

	contact_details:
		view:
			control: 'area'
			label: 'Reason For Contact Details'
			columns: 2

	# Careplan
	nurse_careplan_type:
		model:
			prefill: ['company.nurse_careplan_type']
			source: ['Nursing Care Plan', 'Multidisciplinary Care Plan']
			if:
				'Nursing Care Plan':
					fields: ['insurance_type_id']
				'Multidisciplinary Care Plan':
					fields: ['careplan']
					sections: ['Multidisciplinary Care Plan']
		view:
			control: 'radio'
			label: 'Nursing Care Plan Type'
			offscreen: true
			readonly: true

	insurance_type_id:
		model:
			prefill: ['patient_insurance.type_id']
			source: 'list_payer_type'
			sourceid: 'code'
			if:
				'*':
					fields: ['cms485_careplan']
					sections: ['Nursing Care Plan (485)']
				'!':
					fields: ['treatment_plan']
					sections: ['Treatment Plan']
		view:
			label: 'Insurance Type'
			readonly: true
			offscreen: true

	careplan:
		model:
			required: false
			sourcefilter:
				id:
					'dynamic': '{careplan_id}'
		view:
			embed:
				form: 'careplan'
				selectable: false
			grid:
				add: 'none'
				edit: true
				rank: 'none'
				fields: ['status_id', 'therapy_start', 'therapy_end', 'updated_on', 'updated_by']
				label: ['Status', 'Start', 'End', 'Updated On', 'Updated By']
				width: [20, 15, 15, 25, 25]
			label: 'Multidisciplinary Careplan'

	cms485_careplan:
		model:
			required: false
			sourcefilter:
				order_id:
					'dynamic': '{order_id}'
		view:
			embed:
				form: 'careplan_nursing_485'
				selectable: false
				add_preset:
					order_id: '{order_id}'
					order_no: '{order_no}'
					site_id: '{site_id}'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				rank: 'none'
				fields: ['cert_start', 'cert_end', 'prognosis', 'note']
				label: ['Cert Start', 'Cert End', 'Prognosis', 'Note']
				width: [15, 15, 25, 45]
			label: 'Nursing Care Plan (485)'

	treatment_plan:
		model:
			required: false
			sourcefilter:
				order_id:
					'dynamic': '{order_id}'
		view:
			embed:
				form: 'treatment_plan_nursing'
				selectable: false
				add_preset:
					order_id: '{order_id}'
					order_no: '{order_no}'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['start_of_care_date', 'certification_period', 'prognosis', 'note']
				label: ['SOC', 'Cert Per (Mth)', 'Prognosis', 'Note']
				width: [15, 20, 25, 40]
			label: 'Treatment Plan'

	catheter_log:
		model:
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				form: 'patient_catheter'
				selectable: true
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				rank: 'none'
				fields: ['date_placed', 'date_discontinued', 'device', 'type_id']
				label: ['Date Placed', 'Date Discontinued', 'Device', 'Type']
				width: [15, 15, 35, 35]
			label: 'Device Accessed'

	subform_access:
		model:
			required: false
			multi: true
			source: 'encounter_cath_access'
		view:
			label: 'Catheter Access Assessment'

	measurement_log:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{id}'
		view:
			embed:
				form: 'patient_measurement_log'
				selectable: false
			grid:
				add: 'inline'
				rank: 'none'
				edit: true
				fields: ['created_by', 'date', 'height', 'weight']
				width: [40, 20, 20, 20]
			label: 'Measurement Log'

	# Vitals (baseline)
	bp:
		view:
			note: 'systolic / diastolic'
			label: 'Blood Pressure'
			columns: 4
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\s*(\\d{2,3})\\s*\\/\\s*(\\d{2,3})\\s*$'
				error: 'Invalid Blood Pressure, must be systolic / diastolic'
			}]

	pulse:
		model:
			type: 'int'
			max: 220
			min: 60
		view:
			note: 'bpm (60-220)'
			label: 'Pulse'
			columns: 4

	temp:
		model:
			min: 89
			max: 110
			type: 'decimal'
		view:
			note: 'F (89-110)'
			label: 'Temp'
			columns: 4

	vital_comments:
		view:
			control: 'area'
			label: 'Comments'

	premeds:
		model:
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				type_id:
					'static': ['Premedication', 'Ancillary']
				status:
					'static': 'Active'
		view:
			embed:
				form: 'patient_medication'
				selectable: true
				add_preset:
					patient_id: '{patient_id}'
					status: 'Active'
					type_id: 'Premedication'
					reported_by: 'Nurse'
			grid:
				add: 'flyout'
				edit: false
				rank: 'none'
				fields: ['fdb_id', 'medication_dose']
				label: ['Drug', 'Dose']
				width: [40, 60]
			label: 'Select Pre-Meds Taken'

	prehydration:
		model:
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': 'Active'
				type_id:
					'static': ['Hydration', 'Ancillary']
		view:
			embed:
				form: 'patient_medication'
				selectable: true
				add_preset:
					patient_id: '{patient_id}'
					status: 'Active'
					type_id: 'Hydration'
					reported_by: 'Nurse'
			grid:
				add: 'flyout'
				edit: false
				rank: 'none'
				fields: ['fdb_id', 'medication_dose']
				label: ['Drug', 'Dose']
				width: [40, 60]
			label: 'Select Pre-Hydration Given'

	subform_lots:
		model:
			required: false
			multi: true
			source: 'encounter_lot_used'
		view:
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['lot_no', 'quantity', 'expiration']
				label: ['Lot', 'Quantity', 'Exp']
				width: [30, 30, 40]
			label: 'Lots Used'

	subform_flush:
		model:
			required: false
			multi: true
			source: 'encounter_admin_flush'
		view:
			grid:
				add: 'inline'
				edit: true
				fields: ['time_given', 'flush_id', 'step']
				label: ['Time', 'Flush', 'Step']
				width: [20, 55, 25]
			label: 'Select Flushes Used'

	subform_vital:
		model:
			source: 'encounter_vital'
			multi: true
			type: 'subform'
		view:
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['time_taken', 'temp', 'pulse', 'titration', 'titration_type']
				label: ['Time', 'Temp', 'Pulse', 'Titr', 'Titr Type']
				width: [20, 20, 15, 20, 25]
			label: 'Infusion Log'

	posthydration:
		model:
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': 'Active'
				type_id:
					'static': ['Hydration', 'Ancillary']
		view:
			embed:
				form: 'patient_medication'
				selectable: true
				add_preset:
					patient_id: '{patient_id}'
					status: 'Active'
					type_id: 'Hydration'
					reported_by: 'Nurse'
			grid:
				add: 'flyout'
				edit: false
				rank: 'none'
				fields: ['fdb_id', 'medication_dose']
				label: ['Drug', 'Dose']
				width: [40, 60]
			label: 'Select Post-Hydration Given'

	postmeds:
		model:
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': 'Active'
				type_id:
					'static': ['Postmedication', 'Ancillary']
		view:
			embed:
				form: 'patient_medication'
				selectable: true
				add_preset:
					patient_id: '{patient_id}'
					status: 'Active'
					type_id: 'Postmedication'
					reported_by: 'Nurse'
			grid:
				add: 'flyout'
				edit: false
				rank: 'none'
				fields: ['fdb_id', 'medication_dose']
				label: ['Drug', 'Dose']
				width: [40, 60]
			label: 'Select Post-Meds Taken'

	subform_ros:
		model:
			multi: false
			source: 'encounter_ros'
			type: 'subform'
		view:
			label: 'Review of Systems'

	bleed_log:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				form: 'factor_bleed'
				selectable: false
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				rank: 'none'
				fields: ['cause', 'site', 'duration', 'duration_type','severity']
				label: ['Cause', 'Site(s)', 'Dur', 'Dur Type', 'Severity']
				width: [20, 25, 15, 20, 20]
			label: 'Bleed Log'

	fall_risk_score:
		model:
			prefill: ['checklist_fall.score']
			required: false
			type: 'int'
		view:
			label: 'Fall Risk Score'
			readonly: true
			columns: 4

	fall_risk_score_by:
		model:
			prefill: ['checklist_fall.created_by']
			required: false
			type: 'int'
			source: 'user'
		view:
			label: 'Score By'
			columns: 4

	fall_risk_score_datetime:
		model:
			prefill: ['checklist_fall.created_on']
			type: 'datetime'
		view:
			label: 'Score Datetime'
			columns: 4

	# Post Visit
	post_observations:
		model:
			required: true
			multi: true
			source: ['Pt. tolerated therapy w/o adverse outcome', 'Progressing toward goals', 'Other']
			if:
				'Other':
					fields: ['post_observations_note']
		view:
			control: 'checkbox'
			label: 'Post Infusion Observations'
			columns: 2

	post_observations_note:
		model:
			required: true
		view:
			control: 'area'
			label: 'Other Post Observations'
			columns: 2

	new_problems:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['new_problems_list', 'new_problems_notified', 'new_problems_comment']
					highlight: '#FA8072'
		view:
			control: 'radio'
			label: 'New problems identified?'
			columns: -2

	new_problems_list:
		model:
			multi: true
			source: ['Adverse Drug Reaction', 'Dose Missed', 'Non-adherence',
			'Admitted to Hospital/ER', 'New Medications Started', "Therapy DC'd"]
		view:
			control: 'checkbox'
			note: 'select all that apply'
			label: 'New Problems'
			columns: 2

	new_problems_notified:
		model:
			multi: true
			source: ['MD Notified', 'Addressed/resolved', 'Needs further action']
		view:
			control: 'checkbox'
			label: 'New Problem Action'
			columns: 2

	new_problems_comment:
		view:
			control: 'area'
			label: 'Comments'
			columns: 2

	teaching:
		model:
			multi: true
			source: ['Medication Administration/Site Access Care', 'Disease State/Reporting',
			'Infection Control/Hand Hygiene', 'Supplies/Orders', 'Signs & Symptoms to report',
			'Client/Cg Verbalize Understanding', 'Independent', 'Return Demonstration',
			'Additional Teaching']
		view:
			note: 'check all that apply'
			control: 'checkbox'
			label: 'Teaching'
			columns: -2

	time_out:
		model:
			required: true
			type: 'time'
		view:
			note: "End time of the visit"
			label: 'Visit Time Out'
			columns: 2
			transform: [
					name: 'TimeDifference'
					fields: ['time_in', 'time_out', 'time_total']
			]

	time_total:
		model:
			required: true
			rounding: 0.25
			type: 'decimal'
		view:
			label: 'Total Visit Time (hrs)'
			columns: 2

	travel_time_from_st:
		model:
			type: 'time'
		view:
			label: 'Travel Time From Start'
			note: "Start time of the travel to the patient's location"
			columns: -2
			transform: [
					name: 'TimeDifference'
					fields: ['travel_time_st', 'travel_time_ed', 'travel_time_from_st', 'travel_time_from_ed', 'travel_time_total']
			]

	travel_time_from_ed:
		model:
			type: 'time'
		view:
			label: 'Travel Time From End'
			note: "End time of the travel from the patient's location"
			columns: 2
			transform: [
					name: 'TimeDifference'
					fields: ['travel_time_st', 'travel_time_ed', 'travel_time_from_st', 'travel_time_from_ed', 'travel_time_total']
			]

	travel_time_total:
		model:
			rounding: 0.25
			type: 'decimal'
		view:
			label: 'Total Travel Time (hrs)'
			columns: -2

	total_mileage:
		model:
			rounding: 1
			type: 'decimal'
		view:
			label: 'Total Mileage'
			columns: 2

	next_visit_scheduled:
		model:
			required: false
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['next_infusion']
		view:
			label: 'Next nursing visit already scheduled?'
			control: 'radio'
			columns: -2

	next_infusion:
		model:
			required: true
			type: 'date'
		view:
			label: 'Next Infusion Date'
			columns: 2

	additional_notes:
		view:
			control: 'area'
			note: 'Patient Diagnosis Specific Progress'
			label: 'Additional Notes'

	client_signature:
		model:
			required: false
			type: 'json'
		view:
			control: 'esign'
			label: 'Client/other E-Signature'
			columns: 2

	rn_signature:
		model:
			required: true
			type: 'json'
		view:
			control: 'esign'
			label: 'Nurse E-Signature'
			columns: 2

	pharm_warning:
		model:
			multi: true
			source: ["<span style='color:red;'>Report any Patient Safety Issues (i.e. ADRs) directly to the pharmacy immediately.</span>",
			"<span style='color:red;'>Nursing notes below will be reviewed at the next pharmacy assessment and may not be seen prior to that.</span>"]
		view:
			control: 'checkbox'
			label: 'Warning'
			class: 'list'
			readonly: true

	pharm_notes:
		view:
			control: 'area'
			label: 'Notes to Pharmacy'

	ready_for_review:
		model:
			required: false
			source: ['Yes']
			if:
				'Yes':
					fields: ['approved']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Ready for Review'
			columns: -2

	review_status:
		model:
			default: 'Pending'
			required: false
			source: ['Pending', 'Ready for Review', 'Approved', 'Denied']
			if:
				'Ready for Review':
					fields: ['approved']
		view:
			control: 'radio'
			class: 'checkbox-only'
			label: 'Review Status'
			readonly: true
			offscreen: true

	approved:
		model:
			required: false
			source: ['Yes', 'No']
			if:
				'Yes':
					fields: ['approved_by', 'approved_datetime', 'cc_signature']
				'No':
					fields: ['denied_by', 'denied_datetime', 'denied_reason']
		view:
			control: 'radio'
			class: 'checkbox-only'
			label: 'Approved?'
			columns: 2
			validate: [
				{
					name: 'PrefillCurrentUser'
					condition:
						approved: 'Yes'
					dest: 'approved_by'
				},
				{
					name: 'PrefillCurrentDateTime'
					condition:
						approved: 'Yes'
					dest: 'approved_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						approved: 'No'
					dest: 'denied_by'
				},
				{
					name: 'PrefillCurrentDateTime'
					condition:
						approved: 'No'
					dest: 'denied_datetime'
				}
			]

	denied_by:
		model:
			required: true
			source: 'user'
		view:
			columns: 2
			label: 'Denied By'
			readonly: true

	denied_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Denied Timestamp'
			readonly: true

	denied_reason:
		model:
			required: true
		view:
			control: 'area'
			label: 'Denied Notes'
			columns: 2

	approved_by:
		model:
			required: true
			source: 'user'
		view:
			label: 'Validated By'
			columns: 2
			readonly: true

	approved_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Approved Timestamp'
			readonly: true

	cc_signature:
		model:
			required: false
			type: 'json'
			access:
				read: ['-crn']
				write: ['-nurse', '-cma', '-csr', '-crn']
		view:
			control: 'esign'
			label: 'Review by RN Clinical Coordinator'
			columns: 2

	subform_intervention:
		model:
			source: 'patient_event'
			multi: true
			type: 'subform'
		view:
			grid:
				fields: ['created_on', 'created_by', 'category', 'ae_type', 'complaint_type','affect_health']
				label: ['Created On', 'Created By', 'Category', 'A.E. Related To.', 'Comp. Related To.', 'Issue affect patient’s health/therapy?']
			label: 'Interventions/ADE'

	subform_lock_history:
		model:
			multi: true
			source: 'ledger_enc_lock_history'
			type: 'subform'
		view:
			label: 'Encounter Lock/Unlock History'
			grid:
				add: 'none'
				edit: false
				fields: ['type', 'unlocked_locked_on', 'unlocked_locked_by', 'final_reason']
				label: ['Type', 'On', 'By', 'Reason']
				width: [15, 25, 25, 35]
			readonly: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'encounter'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Other Form'
					direct_attachment: 'Yes'
					source: 'Scanned Document'
					form_name: 'encounter'
					form_code: '{code}'
					form_filter: 'encounter'
					patient_id: '{patient_id}'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
		careplan_delivery_tick:
			link:
				patient_id: 'patient_id'
			filter:
				status: ['ready_to_bill', 'billed']
				void: '!Yes'
			max: 'created_on'
		encounter_environment:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
		company:
			filter:
				id: 1
			max: 'created_on'
		patient_insurance:
			link:
				patient_id: 'patient_id'
			filter:
				active: 'Yes'
				type_id: ['MCRB', 'MCRD']
			max: 'created_on'
		checklist_fall:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
	access:
		create:     ['admin', 'pharm', 'csr', 'cm', 'nurse']
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm', 'csr', 'nurse']
		read:       ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm', 'liaison', 'crn', 'patient','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm', 'liaison','physician']
		request:    []
		update:     ['admin', 'pharm', 'csr', 'cm', 'nurse','liaison', 'crn','physician']
		update_all: ['admin', 'pharm','physician']
		write:      ['admin', 'pharm', 'csr', 'cm', 'nurse','liaison', 'crn','physician']
	bundle: ['patient-form']
	name: ['contact_date', 'contact_reason']
	indexes:
		many: [
			['patient_id']
			['order_id']
			['order_no']
			['route_id']
		]
		unique: [
			['code']
		]
	sections_group: [
		'Visit Info':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['code', 'agency_id', 'agency_does_billing', 'site_id', 'contact_date', 'contact_location', 'environment_assessment_id',
			'delivery_ticket_id', 'visit_number', 'order_id', 'order_no', 'route_id',
			'nurse_careplan_type', 'insurance_type_id',
			'check_fall_score', 'labs_are_due', 'has_premeds',
			'has_postmeds', 'has_hydration', 'has_flushes']

		'Visit Start':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['travel_time_st', 'travel_time_ed', 'time_in', 'contact_reason',
			'contact_details', 'external_note_contact_check', 'uses_external_nursing_note']

		'Measurement Log':
			note: 'Include any updated measurements'
			indent: false
			tab: 'Pre-Visit'
			fields: ['measurement_log']

		'Medical History':
			indent: false
			tab: 'Pre-Visit'
			fields: ['patient_medical_hx']

		'Labs':
			indent: false
			tab: 'Pre-Visit'
			fields: ['subform_lab_draw']

		'Medication Profile':
			indent: false
			tab: 'Pre-Visit'
			fields: ['patient_medications']

		'Allergies':
			indent: false
			tab: 'Pre-Visit'
			fields: ['patient_allergies']

		'DUR - DD DA Interaction':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['patient_interaction_btn']

		'DUR - Interaction':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['patient_interactions']

		'Previous Environment Assessments':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['embed_environment']

		'Environment Assessment':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['subform_environment']

		'Analphylaxis Kit':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['anaphylaxis_kit', 'anaphylaxis_kit_expire', 'anaphylaxis_kit_why', 'anaphylaxis_kit_notified', 'pharmacy_not_notified_why']

		'Primary Therapy Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_therapy_1'] # subform
		'Secondary Therapy Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_therapy_2'] # subform
		'Tertiary Therapy Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_therapy_3'] # subform
		'Quaternary Therapy Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_therapy_4'] # subform
		'Quinary Therapy Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_therapy_5'] # subform

		'Primary Disease Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_disease_1'] # subform
		'Secondary Disease Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_disease_2'] # subform
		'Tertiary Disease Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_disease_3'] # subform
		'Quaternary Disease Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_disease_4'] # subform
		'Quinary Disease Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_disease_5'] # subform

		'Primary Brand Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_brand_1'] # subform
		'Secondary Brand Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_brand_2'] # subform
		'Tertiary Brand Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_brand_3'] # subform
		'Quaternary Brand Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_brand_4'] # subform
		'Quinary Brand Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_brand_5'] # subform

		'Bleed Log':
			hide_header: true
			indent: false
			tab: 'Bleed Log'
			fields: ['bleed_log'] # subform

		#'POC Review and Care Coordination':
		'Multidisciplinary Care Plan':
			hide_header: true
			indent: false
			tab: 'Care Plan'
			fields: ['careplan']
		'Nursing Care Plan (485)':
			hide_header: true
			indent: false
			tab: 'Care Plan'
			fields: ['cms485_careplan']
		'Treatment Plan':
			hide_header: true
			indent: false
			tab: 'Treatment Plan'
			fields: ['treatment_plan']

		'Catheter Log':
			hide_header: true
			indent: false
			tab: 'Access'
			fields: ['catheter_log']
		'Catheter Access Assessment':
			hide_header: true
			indent: false
			tab: 'Access'
			fields: ['subform_access']

		'Vital Signs (Baseline)':
			hide_header: true
			indent: false
			tab: 'Vitals/Admin'
			fields: ['bp', 'pulse', 'temp']
		'Vital Signs Comments':
			hide_header: true
			indent: false
			tab: 'Vitals/Admin'
			fields: ['vital_comments']

		'Pre-Medications':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['premeds']
		'Pre-Hydration':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['prehydration']

		'Drug Admin':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['subform_route_admin'] # subform

		'Lots Used':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['subform_lots']

		'Flushes':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['subform_flush']

		'Infusion/Vitals Log':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['subform_vital']

		'Post-Hydration':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['posthydration']
		'Post-Medications':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['postmeds']

		'Review Of Systems':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_ros'] # subform

		'Primary Therapy Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_1'] # subform
		'Secondary Therapy Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_2'] # subform
		'Tertiary Therapy Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_3'] # subform
		'Quaternary Therapy Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_4'] # subform
		'Quinary Therapy Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_5'] # subform

		'Primary Disease Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_1'] # subform
		'Secondary Disease Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_2'] # subform
		'Tertiary Disease Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_3'] # subform
		'Quaternary Disease Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_4'] # subform
		'Quinary Disease Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_5'] # subform

		'Primary Brand Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_1'] # subform
		'Secondary Brand Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_2'] # subform
		'Tertiary Brand Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_3'] # subform
		'Quaternary Brand Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_4'] # subform
		'Quinary Brand Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_5'] # subform

		'Primary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_clinical_1'] # subform
		'Secondary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_clinical_2'] # subform
		'Tertiary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_clinical_3'] # subform
		'Quaternary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_clinical_4'] # subform
		'Quinary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_clinical_5']

		'Fall Risk Assessment':
			indent: false
			tab: 'Assessment'
			fields: ['subform_checklist_fall'] # subform

		'Fall Risk Score':
			indent: false
			tab: 'Assessment'
			note: '0-24 = No Risk - Good basic nursing care\n25-50 = Low Risk - Implement standard fall interventions\n≥50 = High Risk - Implement high-risk interventions'
			fields: ['fall_risk_score', 'fall_risk_score_by', 'fall_risk_score_datetime']

		'Primary Therapy Post Visit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			area: 'footer'
			fields: ['subform_therapy_1'] # subform
		'Secondary Therapy Post Visit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			area: 'footer'
			fields: ['subform_therapy_2'] # subform
		'Tertiary Therapy Post Visit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			area: 'footer'
			fields: ['subform_therapy_3'] # subform
		'Quaternary Therapy Post Visit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			area: 'footer'
			fields: ['subform_therapy_4'] # subform
		'Quinary Therapy Post Visit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			area: 'footer'
			fields: ['subform_therapy_5'] # subform

		'Primary Disease Post Visit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			area: 'footer'
			fields: ['subform_disease_1'] # subform
		'Secondary Disease Post Visit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			area: 'footer'
			fields: ['subform_disease_2'] # subform
		'Tertiary Disease Post Visit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			area: 'footer'
			fields: ['subform_disease_3'] # subform
		'Quaternary Disease Post Visit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			area: 'footer'
			fields: ['subform_disease_4'] # subform
		'Quinary Disease Post Visit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			area: 'footer'
			fields: ['subform_disease_5'] # subform

		'Primary Brand Post Visit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			area: 'footer'
			fields: ['subform_brand_1'] # subform
		'Secondary Brand Post Visit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			area: 'footer'
			fields: ['subform_brand_2'] # subform
		'Tertiary Brand Post Visit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			area: 'footer'
			fields: ['subform_brand_3'] # subform
		'Quaternary Brand Post Visit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			area: 'footer'
			fields: ['subform_brand_4'] # subform
		'Quinary Brand Post Visit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			area: 'footer'
			fields: ['subform_brand_5'] # subform

		'Response to Therapy/Teaching-Education Reviewed':
			indent: false
			tab: 'Assessment'
			fields: ['post_observations', 'post_observations_note', 'new_problems', 'new_problems_list',
			'new_problems_notified', 'new_problems_comment', 'teaching']

		'Post Visit':
			hide_header: true
			indent: false
			tab: 'Review'
			fields: ['time_out', 'time_total', 'travel_time_from_st', 'travel_time_from_ed',
			'travel_time_total', 'total_mileage', 'next_visit_scheduled', 'next_infusion', 'additional_notes']

		'Signatures Requested':
			indent: false
			tab: 'Review'
			fields: ['client_signature', 'rn_signature']

		'Post Visit Review':
			indent: false
			tab: 'Review'
			fields: ['pharm_warning', 'pharm_notes', 'ready_for_review', 'review_status']

		'Approve':
			modal: true
			indent: false
			fields: ['approved_by', 'approved_datetime', 'cc_signature']
		'Denied':
			modal: true
			indent: false
			fields: ['denied_by', 'denied_datetime', 'denied_reason']
		'Locked/Unlock History':
			indent: false
			tab: 'Review'
			fields: ['subform_lock_history']

		'Interventions/ADE':
			note: 'Document any ADEs or Catheter related event as an intervention'
			fields: ['subform_intervention']

		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'

	]

view:
	comment: 'Patient > Careplan > Encounter'
	grid:
		fields: ['created_by', 'contact_date', 'time_in', 'time_out', 'contact_reason', 'time_total', 'travel_time_total']
		sort: ['-id']
	label: 'Patient Encounter'
	open: 'read'
	block:
		validate: [
			name: 'EncounterBlock'
		]