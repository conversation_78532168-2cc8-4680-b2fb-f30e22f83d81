fields:

	provider_identifier:
		view:
			columns: 4
			label: 'Provider Identifier'
			note: 'PLB01'
			readonly: true
			_meta:
				path: 'providerIdentifier'

	fiscal_period_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Fiscal Period Date'
			note: 'PLB02'
			readonly: true
			_meta:
				path: 'fiscalPeriodDate'

	adjustment_reason_code:
		view:
			columns: 2
			label: 'Reason Code'
			note: 'PLB03-1; PLB05-1; PLB07-1; PLB09-1; PLB11-1; PLB13-1'
			readonly: true
			_meta:
				path: 'adjustmentReasonCode'

	adjustment_reason_code_value:
		view:
			columns: 2
			label: 'Reason'
			note: 'PLB03-1; PLB05-1; PLB07-1; PLB09-1; PLB11-1; PLB13-1'
			readonly: true
			_meta:
				path: 'adjustmentReasonCodeValue'

	provider_adjustment_identifier:
		view:
			columns: 4
			label: 'Adjustment Identifier'
			note: 'PLB03-2; PLB05-2; PLB07-2; PLB09-2; PLB11-2; PLB13-2'
			readonly: true
			_meta:
				path: 'providerAdjustmentIdentifier'

	provider_adjustment_amount:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Adjustment Amount'
			note: 'PLB04; PLB06; PLB08; PLB10; PLB12; PLB14'
			readonly: true
			_meta:
				path: 'providerAdjustmentAmount'

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: 'Provider Level Adjustments'
	indexes:
		many: [
			['provider_identifier']
			['fiscal_period_date']
			['adjustment_reason_code']
		]
	sections:
		'Provider Adjustment':
			hide_header: true
			fields: ['provider_identifier', 'fiscal_period_date',
			'adjustment_reason_code', 'adjustment_reason_code_value', 
			'provider_adjustment_identifier', 'provider_adjustment_amount']

view:
	dimensions:
		width: '65%'
		height: '45%'
	hide_cardmenu: true
	comment: 'Provider Level Adjustments'
	grid:
		fields: ['adjustment_reason_code_value', 'provider_adjustment_amount']
		label: ['Reason', 'Amount']
		width: [60, 20]
		sort: ['-created_on']
	label: 'Provider Adjustments'
	open: 'read'