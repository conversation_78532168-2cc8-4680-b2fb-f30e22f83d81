fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			readonly: true
			offscreen: true

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'
			readonly: true
			offscreen: true

	status:
		model:
			source:
				delivery_ticket: 'Delivery Ticket Creation' # This is a new delivery ticket, not associated with a claim
				ready_to_fill: 'Ready to Fill' # This is a delivery ticket that is ready to be filled and prescription has been verified or started from a refill request
				order_ver: 'Pending Order Verification' # This is a delivery ticket that has been filled and is await pharmacist verification
				pending_conf: 'Pending Confirmation' # This is a delivery ticket that has been filled and is await pharmacist confirmation
				ready_to_bill: 'Ready to Bill' # This is a delivery ticket that has been confirmed by the pharmacist and is ready to be billed
				billed: 'Billed' # This is a delivery ticket that has been billed for all charges on the ticket
				voided: 'Voided' # This is a delivery ticket that has been voided
			if:
				'delivery_ticket':
					prefill:
						ssfltr: ['PENDING']
						shipment_status_id: 'PENDING'
				'ready_to_fill':
					prefill:
						ssfltr: ['PENDING']
						shipment_status_id: 'PENDING'
					require_fields: ['ship_location', 'ship_to', 'ship_street', 'ship_city', 'ship_state_id', 'ship_zip']
				'order_ver':
					prefill:
						ssfltr: ['PENDING']
						shipment_status_id: 'PENDING'
					require_fields: ['ship_location', 'ship_to', 'ship_street', 'ship_city', 'ship_state_id', 'ship_zip']
				'ready_to_bill':
					prefill:
						ssfltr: ['SHIPPED', 'DELIVERED']
						shipment_status_id: 'PENDING'
					require_fields: ['ship_location', 'ship_to', 'ship_street', 'ship_city', 'ship_state_id', 'ship_zip']
				'billed':
					prefill:
						ssfltr: ['SHIPPED', 'DELIVERED', 'DAMAGED', 'LOST']
						shipment_status_id: 'DELIVERED'
					require_fields: ['ship_location', 'ship_to', 'ship_street', 'ship_city', 'ship_state_id', 'ship_zip']
				'voided':
					readonly: 
						fields: ['shipment_status_id', 'embed_ship_address', 'ship_location', 'ship_to', 'ship_street', 'ship_street2', 
						'ship_city', 'ship_state_id', 'ship_zip', 'ship_delivery_instructions', 'ship_method_id', 'ship_courier_id', 
						'ship_driver_id', 'ship_date', 'tracking_no', 'shipping_cost', 'delivered_date', 'confirmed_datetime', 'confirmed_by',
						'shipping_label']
		view:
			label: 'Delivery Ticket Status'
			readonly: true
			offscreen: true
			validate: [
				{
					name: 'LoadDeliveryTicketStatus'
				}
			]

	embed_ship_address:
		model:
			multi: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				form: 'patient_address'
				selectable: true
			grid:
				fields: ['address_type', 'addresslabel']
				rank: 'none'
			label: 'Shipping Address'
			validate: [
				{
					name: 'LoadShippingAddress'
				}
			]

	# Shipping information
	ship_location:
		model:
			default: 'Home'
			prefill: ['careplan_delivery_log']
			source: ['Home', 'Infusion Center', 'Physician Office']
		view:
			columns: 2
			control: 'radio'
			label: 'Administration Location'

	ship_to:
		model:
			prefill: ['careplan_delivery_log']
		view:
			columns: 2
			label: 'Shipping Name'

	ship_street:
		model:
			prefill: ['careplan_delivery_log']
			max: 128
			min: 4
		view:
			columns: 'addr_1'
			label: 'Street'
			class: "api_prefill"
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey: 'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'ship_street': ['addressLabel']
					'ship_city': ['city']
					'ship_state_id': ['stateCode']
					'ship_zip': ['postalCode']
			]

	ship_street2:
		model:
			prefill: ['careplan_delivery_log']
			max: 128
		view:
			columns: 'addr_2'
			label: 'Street 2'

	ship_city:
		model:
			prefill: ['careplan_delivery_log']
			max: 128
			min: 1
		view:
			columns: 'addr_city'
			label: 'City'
			readonly: true

	ship_state_id:
		model:
			prefill: ['careplan_delivery_log']
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'State'
			readonly: true

	ship_zip:
		model:
			prefill: ['careplan_delivery_log']
			max: 10
			min: 5
		view:
			format: 'us_zip'
			columns: 'addr_zip'
			label: 'Zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'ship_zip'
						city:'ship_city'
						state:'ship_state_id'
			]
			readonly: true

	ship_delivery_instructions:
		model:
			dynamic:
				source: 'list_delivery_instr_tmp'
		view:
			columns: 2
			control: 'area'
			label: 'Delivery Instructions'

	ship_method_id:
		model:
			required: false
			prefill: ['careplan_delivery_log']
			source: 'list_shipping_method'
			sourceid: 'code'
			if:
				'Courier Service':
					fields: ['ship_courier_id']
				'Driver':
					fields: ['ship_driver_id']
				'*':
					fields: ['tracking_no', 'shipping_cost']
		view:
			columns: 4
			label: 'Shipping Method'

	ship_courier_id:
		model:
			prefill: ['careplan_delivery_log']
			source: 'list_courier'
		view:
			columns: 4
			label: 'Driver'

	ship_driver_id:
		model:
			prefill: ['careplan_delivery_log']
			source: 'user'
			sourcefilter:
				role:
					static: ['driver']
		view:
			columns: 4
			label: 'Driver'

	ssfltr:
		model:
			multi: true
			source: 'list_shipment_status'
			sourceid: 'code'
		view:
			label: 'Shipment Status Filter'
			offscreen: true
			readonly: true

	shipment_status_id:
		model:
			source: 'list_shipment_status'
			sourceid: 'code'
			required: true
			default: 'PENDING'
			sourcefilter:
				code:
					'dynamic': '{ssfltr}'
			if:
				'SHIPPED':
					require_fields: ['ship_method_id', 'ship_date']
				'DELIVERED':
					require_fields: ['ship_method_id', 'ship_date']
					fields: ['delivered_date', 'confirmed_datetime', 'confirmed_by']
				'LOST':
					require_fields: ['ship_method_id']
				'DAMAGED':
					require_fields: ['ship_method_id']
		view:
			columns: 4
			label: 'Shipment Status'
			validate: [
				{
					name: 'PrefillCurrentDate'
					condition:
						shipment_status_id: 'SHIPPED'
					dest: 'ship_date'
				},
				{
					name: 'PrefillCurrentDate'
					condition:
						shipment_status_id: 'DELIVERED'
					dest: 'delivered_date'
				},
				{
					name: 'PrefillCurrentDateTime'
					condition:
						shipment_status_id: 'DELIVERED'
					dest: 'confirmed_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						shipment_status_id: 'DELIVERED'
					dest: 'confirmed_by'
				}
			]

	ship_date:
		model:
			required: false
			type: 'date'
		view:
			columns: 4
			label: 'Ship Date'

	tracking_no:
		model:
			required: true
		view:
			columns: 4
			label: 'Tracking Number'

	shipping_cost:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			label: 'Shipping Cost'
			class: 'numeral money'
			format: '$0,0.00'

	delivered_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Delivered Date'

	confirmed_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 4
			label: 'Delivery Confirmed Date'

	confirmed_by:
		model:
			required: true
			source: 'user'
		view:
			columns: 4
			label: 'Confirmed By'
			readonly: true

	shipping_label:
		model:
			type: 'json'
		view:
			class: 'media-preview'
			control: 'file'
			label: 'Shipping Label'
			note: 'Max 100MB. Only images are supported.'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['shipment_status_id', 'ship_date', 'delivered_date']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
		careplan_delivery_log:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
	sections_group: [
		'Delivery Confirmation Log':
			sections: [
				'Delivery Confirmation':
					hide_header: true
					indent: false
					tab: 'Shipment'
					fields: ['ssfltr', 'shipment_status_id', 'ship_method_id',  'ship_date', 'tracking_no', 'shipping_cost', 'delivered_date', 'confirmed_datetime', 'confirmed_by']

				'Shipping Address':
					hide_header: true
					indent: false
					tab: 'Shipment'
					fields: ['embed_ship_address']
				'Shipping Information':
					hide_header: true
					indent: false
					tab: 'Shipment'
					fields: ['status', 'ship_to', 'ship_location',
					'ship_street', 'ship_street2', 'ship_city', 'ship_state_id', 'ship_zip']
				'Shipping Method':
					hide_header: true
					indent: false
					tab: 'Shipment'
					fields: ['ship_delivery_instructions', 'ship_driver_id', 'ship_courier_id']
				'Shipping Label':
					hide_header: true
					indent: false
					tab: 'Shipping Label'
					fields: ['shipping_label']
			]
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Care Plan Delivery Confirmation Log'
	grid:
		fields: ['created_on', 'shipment_status_id','ship_date', 'ship_method_id', 'tracking_no', 'delivered_date']
		sort: ['-created_on']
	label: 'Patient Delivery Confirmation Log'
	open: 'read'
