fields:
	user_id:
		model:
			required: true
			source: 'user'
		view:
			label: 'User'

	source_form:
		model:
			type: 'text'
		view:
			label: 'Source Form'

	source_form_id:
		model:
			type: 'int'
		view:
			label: 'Source Id'

	form_event:
		model:
			source: ['Create', 'Update']
			type: 'text'
		view:
			label: 'Form Event'

	field_name:
		model:
			type: 'text'
		view:
			label: 'Field Name'

	field_value_txt:
		model:
			type: 'text'
		view:
			label: 'Field Value Text'

	field_value_int:
		model:
			type: 'int'
		view:
			label: 'Field Int'

	field_value_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Field Datetime'

	field_value_date:
		model:
			type: 'date'
		view:
			label: 'Field Date'

	field_value_dec:
		model:
			type: 'decimal'
		view:
			label: 'Field Desc'

	prev_field_value_txt:
		model:
			type: 'text'
		view:
			label: 'Prev Field Text'

	prev_field_value_int:
		model:
			type: 'int'
		view:
			label: 'Prev Field Int'

	prev_field_value_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Prev Field Datetime'

	prev_field_value_date:
		model:
			type: 'date'
		view:
			label: 'Prev Field Date'

	prev_field_value_dec:
		model:
			type: 'decimal'
		view:
			label: 'Prev Field Desc'

	group_uuid:
		model:
			type: 'text'
		view:
			label: 'Group uuid'

model:
	bundle: ['audit']
	indexes:
		many: [
			['source_form'],
			['source_form_id'],
			['form_event'],
			['field_name'],
			['field_value_txt'],
			['field_value_int'],
			['field_value_datetime'],
			['field_value_date'],
			['field_value_dec'],
			['prev_field_value_txt'],
			['prev_field_value_int'],
			['prev_field_value_datetime'],
			['prev_field_value_date'],
			['prev_field_value_dec'],
			['group_uuid']
		]
	name: ['source_form', 'source_form_id', 'form_event', 'field_name']
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','patient']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','patient']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','patient']
		update:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','patient']
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','patient']
view:
	comment: 'Form Tracker'
	label: '~ Form Tracker'
