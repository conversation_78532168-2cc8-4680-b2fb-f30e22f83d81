fields:

	entity_type_qualifier:
		model:
			default: '2'
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'NM102'
		view:
			columns: 3
			label: 'Entity Type Qualifier'
			reference: 'NM102'
			_meta:
				location: '2330D NM1'
				field: '02'
				path: 'claimInformation.otherSubscriberInformation[{idx1-10}].otherPayerRenderingProvider[0].entityTypeQualifier'

	other_payer_rendering_provider_secondary_identifier:
		model:
			required: true
			if:
				'*':
					require_fields: ['entity_type_qualifier']
			multi: true
			source: 'med_claim_orend_id'
			type: 'subform'
		view:
			note: 'Max 3'
			label: 'Identifier(s)'
			grid:
				add: 'inline'
				edit: true
				fields: ['qualifier', 'identifier']
				label: ['Qualifier', 'ID']
				width: [50, 50]
			max_count: 3

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['entity_type_qualifier']
	sections_group: [
		'Rendering Provider':
			hide_header: true
			sections: [
				'Type':
					hide_header: true
					fields: ['entity_type_qualifier']
				'Identifier(s)':
					hide_header: true
					indent: false
					fields: ['other_payer_rendering_provider_secondary_identifier']
			]
	]
view:
	dimensions:
		width: '45%'
		height: '45%'
	hide_cardmenu: true
	reference: '2330D'
	comment: 'Med Claim Rendering Provider'
	grid:
		fields: ['entity_type_qualifier']
		sort: ['-created_on']
	label: 'Med Claim Rendering Provider'
	open: 'read'