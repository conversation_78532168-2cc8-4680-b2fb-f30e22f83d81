fields:

	reject_code:
		model:
			multi: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '511-FB'
		view:
			note: '511-FB'
			label: 'Payer Reject Code'
			readonly: true

	reject_count:
		model:
			type: 'int'
		view:
			note: '546-4F'
			label: 'Payer Reject Field Occurence Indicator'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['reject_code', 'reject_count']
	sections:
		'Reject Code':
			hide_header: true
			indent: false
			fields: ['reject_code', 'reject_count']

view:
	dimensions:
		width: '50%'
		height: '50%'
	hide_cardmenu: true
	comment: 'Reject Code'
	grid:
		fields: ['reject_code', 'reject_count']
		sort: ['-created_on']
	label: 'Reject Code'
	open: 'read'