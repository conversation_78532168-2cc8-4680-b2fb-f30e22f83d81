fields:

	code:
		model:
			required: true
		view:
			columns: 2
			label: 'Assessment Code'

	name:
		view:
			columns: 2
			label: 'Name'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 2

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['lists']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['code']
		]
	name: ['name']
	sections:
		'Infectious Disease':
			fields: ['code', 'name', 'allow_sync', 'active']

view:
	comment: 'Manage > Infectious Disease'
	find:
		basic: ['code']
	grid:
		fields: ['code', 'name', 'allow_sync', 'active']
		sort: ['code']
	label: 'Infectious Disease'
	open: 'read'
