fields:

	inventory_id:
		model:
			source: 'inventory'
			sourcefilter:
				type:
					'static': ['Supply']
		view:
			columns: 3
			label: 'Inventory Item'
			readonly: true

	dispense_quantity:
		model:
			type: 'int'
		view:
			columns: 3
			label: 'Dispense Quantity'
			readonly: true

	bill:
		model:
			required: false
			source: ['Yes']
		view:
			columns: 3
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Billable?'
			readonly: true

	part_of_kit:
		model:
			source: ['Yes']
		view:
			columns: 3
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Billed as Kit?'
			readonly: true

	one_time_only:
		model:
			max: 4
			source: ['Yes']
			default: 'Yes'
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: '1x Only?'
			columns: 3
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	indexes:
		many: [
			['inventory_id']
		]
	name: '{inventory_id} {dispense_quantity}'
	sections:
		'Supply Kit Item':
			fields: ['inventory_id', 'dispense_quantity', 'bill', 'part_of_kit', 'one_time_only']

view:
	hide_cardmenu: true
	comment: 'Supply Kit Preview Item'
	grid:
		fields: ['inventory_id', 'dispense_quantity', 'bill', 'part_of_kit', 'one_time_only']
		width: [40, 15, 15, 15, 15]
		sort: ['-id']
	label: 'Supply Kit Preview Item'
	open: 'edit'