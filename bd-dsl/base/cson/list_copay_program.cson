fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	name:
		model:
			max: 64
			required: true
		view:
			label: 'Program Name'
			findunique: true
			columns: 2

	bname_id:
		model:
			multi: true
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Linked Brand Names'

	phone:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Phone #'
			columns: 2

	fax:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Fax #'
			columns: 2

	maximum_amount:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Maximum Amount'
			columns: 2

	active:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			columns: 2
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Active?'
			findfilter: 'Yes'

	auto_renews:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 2
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Auto-renews?'

	limitations:
		view:
			control: 'area'
			label: 'Limitations'

	reenrollment_process:
		view:
			control: 'area'
			label: 'Re-enrollment process'

	url:
		view:
			label: 'Reference URL'
			format: 'url'

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'list_copay_program'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Copay Program'
					direct_attachment: 'Yes'
					copay_program_id: '{code}'
					source: 'Scanned Document'
					form_code: '{code}'
					form_name: 'list_copay_program'
					form_filter: 'list_copay_program'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['assistance']
	indexes:
		many: [
			['name']
			['bname_id']
		]
		unique: [
			['name']
			['code']
		]
	name: '{name}'
	sections_group: [
		'Program Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'maximum_amount', 'phone', 'fax',
			'bname_id', 'active', 'auto_renews', 'limitations', 'reenrollment_process', 'url']
			tab: 'Program'
		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'
	]

view:
	comment: 'Manage > Manufacturer Copay Program'
	find:
		basic: ['name', 'bname_id']
	grid:
		fields: ['name', 'bname_id', 'phone', 'fax', 'maximum_amount', 'limitations']
		sort: ['name']
	label: 'Manufacturer Copay Program'
