fields:

	patient_id:
		model:
			required: true
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	payer_filter:
		model:
			multi: true
			source: 'payer'
		view:
			label: 'Available Insurances'
			readonly: true
			offscreen: true

	current_insurance_id:
		model:
			required: true
			source: 'patient_insurance'
		view:
			columns: 2
			label: 'Current Payer'
			readonly: true

	total_balance_due:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Total Remaining Balance' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	total_pt_pay:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Total Patient Pay' 
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	next_insurance_id:
		model:
			required: true
			source: 'patient_insurance'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				payer_id:
					'dynamic': '{payer_filter}'
		view:
			columns: -2
			label: 'Next Payer'

model:
	save: false
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']

	name: ['patient_id', 'current_insurance_id', 'next_insurance_id']

	sections:
		'Next Payer':
			tab: 'Next Payer'
			hide_header: true
			indent: false
			fields: ['patient_id', 'current_insurance_id', 'payer_filter',
			'total_balance_due', 'total_pt_pay', 'next_insurance_id']

view:
	hide_cardmenu: true
	comment: 'Next Payer'
	grid:
		fields: ['patient_id', 'current_insurance_id', 'next_insurance_id']
		sort: ['-patient_id']
	label: 'Next Payer'
