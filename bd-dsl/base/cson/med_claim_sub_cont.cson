fields:

	name:
		model:
			min: 1
			max: 60
		view:
			columns: 3
			label: 'Contact Name'
			reference: 'PER02'
			_meta:
				location: '2000B PER'
				code: 'IC'
				field: '02'
				path: 'subscriber.contactInformation.name'

	email:
		model:
			max: 256
			min: 1
			required: false
		view:
			columns: 3
			label: 'Email Address'
			reference: 'PER04 PER03 = EM'
			_meta:
				location: '2000B PER'
				code: 'EM'
				type: 'many'
				field: ['04', '06', '08']
				path: 'subscriber.contactInformation.email'
			validate: [
					name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'
			]

	phone_number:
		model:
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Contact Phone #'
			reference: 'PER04 PER03 = TE'
			_meta:
				location: '2000B PER'
				code: 'TE'
				field: ['04', '06', '08']
				type: 'many'
				path: 'subscriber.contactInformation.phoneNumber'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['name']
	sections:
		'Subscriber Contact Information':
			hide_header: true
			fields: ['name', 'email', 'phone_number']

view:
	dimensions:
		width: '75%'
		height: '45%'
	hide_cardmenu: true
	comment: 'Subscriber Contact Information'
	grid:
		fields: ['name', 'email', 'phone_number']
		width: [50, 25, 25]
		sort: ['-created_on']
	label: 'Subscriber Contact Information'
	open: 'read'