fields:
	patient_id:
		model:
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	careplan_id:
		model:
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan'
			readonly: true
			offscreen: true

	order_id:
		model:
			source: 'careplan_order'
		view:
			label: 'Order'
			readonly: true
			offscreen: true

	test_claims:
		model:
			required: false 
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				order_id:
					'dynamic': '{order_id}'
		view:
			label: 'Test Claims'
			embed:
				query: 'order_test_claims'
				selectable: false
			grid:
				edit: true
				add: 'none'
				rank: 'none'
				fields: ['status', 'date_of_service', 'inventory_id', 'payer_id', 'expected', 'paid']
				label: ['Status', 'DOS', 'Pri Inv.', 'Payer', 'Exp $', 'Paid $']
				width: [10, 10, 25, 25, 15, 15]

	invoices:
		model:
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				order_id:
					'dynamic': '{order_id}'
		view:
			label: 'Invoices'
			embed:
				query: 'order_invoices'
				selectable: false
			grid:
				edit: false
				add: 'none'
				rank: 'none'
				fields: ['status', 'date_of_service', 'payer_id', 'inventory_id', 'total_expected', 'total_paid']
				label: ['Status', 'DOS', 'Payer', 'Pri. Inv', 'Exp $', 'Paid $']
				tooltip: ['substatus']
				width: [10, 10, 25, 25, 15, 15]

model:
	save: false
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
	name: ['created_by']

	sections_group: [
		'Invoices':
			hide_header: true
			indent: false
			fields: ['invoices']
			tab: 'Invoices'
		'Test Claims':
			hide_header: true
			indent: false
			fields: ['test_claims']
			tab: 'Test Claims'
	]
view:
	hide_cardmenu: true
	comment: 'View Claims'
	find:
		basic: ['patient_id']
	grid:
		fields: ['order_id', 'patient_id', 'created_by']
		sort: ['-created_on']
	label: 'View Claims'
