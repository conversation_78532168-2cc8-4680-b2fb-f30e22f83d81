fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	patient_fax_file:
		model:
			type: 'json'
			access:
				read: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		view:
			control: 'file'
			label: 'Document / File'
			readonly: true

	comments:
		model:
			max: 1024
		view:
			control: 'area'
			label: 'Comments'

model:
	access:
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		update:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
	name: ['created_on', 'order_id']
	sections:
		'Fax File':
			fields: ['patient_fax_file', 'comments']

view:
	comment: 'Patient > Fax File Attachments'
	grid:
		fields: ['created_on', 'created_by', 'patient_fax_file', 'comments']
		sort: ['-id']
	label: 'Patient Fax File Attachment'
