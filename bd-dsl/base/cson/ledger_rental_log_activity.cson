fields:

	ticket_no:
		model:
			required: false
		view:
			columns: 4
			label: 'Ticket No'
			readonly: true
			offscreen: true

	ticket_item_no:
		model:
			required: false
		view:
			columns: 4
			label: 'Ticket Item No'
			readonly: true
			offscreen: true

	localized_datetime:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Datetime'
			readonly: true

	user_id:
		model:
			source: 'user'
		view:
			columns: 4
			label: 'User'
			readonly: true

	inventory_id:
		model:
			source: 'inventory'
		view:
			columns: 4
			label: 'Inventory'
			readonly: true

	serial_no:
		model:
			required: true
		view:
			columns: 4
			label: 'Serial No'
			readonly: true

	patient_id:
		model:
			source: 'patient'
		view:
			columns: 4
			label: 'Patient'
			readonly: true

	previous_patient_id:
		model:
			source: 'patient'
		view:
			columns: 4
			label: 'Previous Patient'
			readonly: true

	site_id:
		model:
			source: 'site'
		view:
			columns: 4
			label: 'Site'
			readonly: true

	field:
		view:
			columns: -4
			label: 'Field'
			readonly: true

	old_value:
		view:
			columns: 4
			label: 'Old Value'
			readonly: true

	new_value:
		view:
			columns: 4
			label: 'New Value'
			readonly: true

	description:
		view:
			label: 'Description'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
	reportable: true
	bundle: ['audit']
	indexes:
		many: [
			['localized_datetime']
			['user_id']
			['inventory_id']
			['serial_no']
			['patient_id']
			['previous_patient_id']
			['site_id']
			['field']
			['old_value']
			['new_value']
			['description']
		]
	name: ['localized_datetime', 'user_id', 'description']

	sections:
		'Details':
			fields: ['localized_datetime', 'user_id', 'patient_id', 'previous_patient_id', 'site_id', 
			'inventory_id', 'serial_no', 'field', 'old_value', 'new_value', 'description']

view:
	hide_cardmenu: true
	comment: 'Rental Log Activity Log'
	find:
		basic: ['user_id', 'patient_id', 'site_id', 'inventory_id', 'serial_no']
	grid:
		fields: ['localized_datetime', 'user_id', 'description']
		sort: ['-localized_datetime']
	label: 'Rental Log Activity Log'
