#RNP3_NDC_PRICE
fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	ndc:
		view:
			label: 'NDC'
			findunique: true
			readonly: true
			columns: 2

	price_type:
		view:
			label: 'Price Type'
			readonly: true
			columns: 2

	price_effective_dt:
		model:
			type: 'date'
		view:
			label: 'Price Effective Date'
			readonly: true
			columns: 2

	price:
		model:
			type: 'decimal'
		view:
			label: 'Price'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['ndc']
	indexes:
		many: [
			['ndc']
			['price_type']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['ndc', 'price_type', 'price_effective_dt', 'price']

view:
	comment: 'Manage > List FDB NDC Price'
	find:
		basic: ['ndc']
	grid:
		fields: ['ndc']
	label: 'List FDB NDC Price'
