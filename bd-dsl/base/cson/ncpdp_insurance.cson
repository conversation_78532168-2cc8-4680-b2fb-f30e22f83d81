fields:
	transaction_code:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			prefill: ['parent.transaction_code']
			sourcefilter:
				field:
					'static': '103-A3'
			if:
				'B1': # Drug Billing
					fields: ['pt_rel_code', 'plan_id', 'medigap_id', 'dr_accept_indicator',
					'partd_facility','person_code', 'elig_clar_code']
				'B2': # Reversal
					fields: ['medigap_id']
				'B3': # Drug Reverse and Bill
					fields: ['pt_rel_code', 'plan_id', 'medigap_id', 'dr_accept_indicator',
					'partd_facility', 'person_code', 'elig_clar_code']
				'S1': # Service Billing
					fields: ['pt_rel_code', 'plan_id', 'medigap_id', 'dr_accept_indicator',
					'person_code', 'elig_clar_code']
				'S2': # Service Reversal
					fields: ['medigap_id']
				'S3': # Service Reverse and Bill
					fields: ['pt_rel_code', 'plan_id', 'medigap_id', 'dr_accept_indicator',
					'person_code', 'elig_clar_code']
				'E1': # Elgibility Check
					fields: ['pt_rel_code', 'person_code', 'elig_clar_code']
		view:
			label: 'Transmission Code'
			offscreen: true
			readonly: true

	insurance_id:
		model:
			type: 'int'
			required: true
			source: 'patient_insurance'
		view:
			label: 'Insurance'
			readonly: true
			columns: 2

	payer_type_id:
		model:
			source: 'list_payer_type'
			sourceid: 'code'
			if:
				'MEDI':
					fields: ['mcd_id_no', 'mcd_indicator']
		view:
			label: 'Payer Type'
			offscreen: true
			readonly: true

	card_holder_id:
		model:
			max: 20
			required: true
		view:
			columns: 4
			reference: '302-C2'
			note: '302-C2'
			label: 'Cardholder ID'

	pt_rel_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '306-C6'
			if:
				'!1':
					fields: ['card_holder_first_name', 'card_holder_last_name']
		view:
			columns: -2
			reference: '306-C6'
			note: '306-C6'
			label: 'Subscriber Relationship Code'

	card_holder_first_name:
		model:
			max: 12
		view:
			columns: -2
			reference: '312-CC'
			note: '312-CC'
			label: 'Cardholder First Name'

	card_holder_last_name:
		model:
			max: 15
		view:
			columns: 2
			reference: '313-CD'
			note: '313-CD'
			label: 'Cardholder Last Name'

	plan_id:
		model:
			max: 8
		view:
			columns: 4
			reference: '524-FO'
			note: '524-FO'
			label: 'Plan ID'

	elig_clar_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '309-C9'
		view:
			columns: 2
			reference: '309-C9'
			note: '309-C9'
			label: 'Elig Clarification Code'
			_meta:
				copy_forward: true

	group_id:
		model:
			max: 15
		view:
			columns: 4
			reference: '301-C1'
			note: '301-C1'
			label: 'Group Number'
			_meta:
				copy_forward: true

	person_code:
		model:
			max: 3
		view:
			columns: 4
			reference: '303-C3'
			note: '303-C3'
			label: 'Person Code'
			_meta:
				copy_forward: true

	home_plan:
		model:
			max: 3
		view:
			columns: 4
			reference: '314-CE'
			note: '314-CE'
			label: 'Home Plan'

	medigap_id:
		model:
			max: 20
		view:
			reference: '359-2A'
			note: '359-2A'
			label: 'MediGap ID'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{5,10}$'
				error: 'Invalid MediGap ID'
			}]
			_meta:
				copy_forward: true
			offscreen: true
			readonly: true

	mcd_indicator:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '360-2B'
		view:
			columns: 4
			reference: '360-2B'
			note: '360-2B'
			label: 'MCD Cov State'
			_meta:
				copy_forward: true

	dr_accept_indicator:
		model:
			default: 'Y'
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '361-2D'
		view:
			columns: 2
			reference: '361-2D'
			note: '361-2D'
			label: 'Pharmacy Accepts Assignment?'
			_meta:
				copy_forward: true

	partd_facility:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '997-G2'
		view:
			columns: 2
			reference: '997-G2'
			note: '997-G2'
			label: 'Pt in CMS Part D Facility?'
			_meta:
				copy_forward: true

	mcd_id_no:
		model:
			max: 20
		view:
			columns: 4
			reference: '115-N5'
			note: '115-N5'
			label: 'Medicaid ID'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{9,13}$'
				error: 'Invalid Medicaid Number, must be 9-13 characters long'
			}]
			_meta:
				copy_forward: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['transaction_code', 'insurance_id', 'payer_type_id']
	sections:
		'Insurance':
			hide_header: true
			indent: false
			fields: ['transaction_code', 'insurance_id', 'payer_type_id',
			'card_holder_id', 'plan_id', 'group_id', 'person_code', 
			'card_holder_first_name', 'card_holder_last_name',
			'pt_rel_code','elig_clar_code',  'dr_accept_indicator', 'home_plan']

		'Medicare/Medicaid':
			indent: false
			hide_header: true
			fields: ['medigap_id', 'mcd_indicator', 'partd_facility', 'mcd_id_no']

view:
	comment: 'Insurance'
	grid:
		fields: ['card_holder_id', 'group_id', 'person_code', 'mcd_id_no', 'pt_rel_code']
		sort: ['-created_on']
	label: 'Insurance'
	open: 'read'