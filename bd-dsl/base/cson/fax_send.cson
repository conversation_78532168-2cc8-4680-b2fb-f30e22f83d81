fields:
	sent_dt:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Sent At'
			template: '{{now}}'	
			transform: [
				name: 'EmbedRefresh',
				fields: ['fax_item_embed']
			]
			offscreen: true
	fax_id:
		model:
			type: 'text'
		view:	
			label: 'Fax ID'
			readonly: true
			offscreen: true
			transform: [{
				name: 'GenerateUUID'
			},
			{
				name: 'EmbedRefresh',
				fields: ['fax_item_embed']
			}]
	status:
		model:
			source: ['new', 'scheduled']
			default: 'new'
			if: 
				'new':
					fields: ['sent_dt']
				'scheduled':
					fields: ['scheduled_dt']
					require_fields: ['scheduled_dt']
		view:
			columns: 1
			label: 'Fax Status'
	fax_type:
		model:
			source: ['Template', 'Document']
			default: 'Document'
			if:
				'Template':
					fields: ['template_id', 'template_code']
				'Document':
					sections: ['Fax Documents']
	
		view:
			columns: 1
			label: 'Fax Type'
	template_id:
		model:
			source: 'report'
			sourcefilter:
				submodule: 
					'static': 'fax'
		view:
			label: 'Select Fax Template'
			control: 'select'
			class: 'select_prefill'
			transform: [{
				name: 'SelectPrefill'
				url: 'api/form/report/?limit=1&fields=list&sort=id&filter=submodule:'
				fields:
					'template_code': ['code']
			},
			{
				name: 'EmbedRefresh',
				fields: ['fax_item_embed']
			}
			]
	template_code:
		view:
			label: 'Template Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'EmbedRefresh',
				fields: ['fax_item_embed']
			]
	patient_id:
		model:
			source: 'patient'
			type: 'int'
			sourcefilter:
				site_id:
					'dynamic': '{site_id}'
			required: true
		view:
			label: 'Selected Patient'
			columns: 4
			transform: [
				name: 'EmbedRefresh',
				fields: ['fax_item_embed']
			]
	site_id:
		model:
			source: 'site'
			sourcefilter:
				id:
					'dynamic': '{site_id}'
			required: true
		view:
			label: 'Selected Site'
			columns: 4
			transform: [
				name: 'EmbedRefresh',
				fields: ['fax_item_embed']
			]
	scheduled_dt:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Scheduled At'
	fax_to:
		model:
			source: 'fax_numbers'
			sourcefilter:
				site_id:
					'dynamic': '{site_id}'
				is_default:
					'static': 'Yes'
		view:
			label: 'Select Fax Number'
			control: 'select'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: 'api/form/fax_numbers/?limit=1&fields=list&sort=id&filter=id:'
				fields:
					'fax_number': ['from_number']
					'site_id_auto_name': ['from_name']
			]

	to_number:
		model:
			type: 'text'
			required: true
		view:
			columns: 2
			label: 'To Number'
			format: 'us_phone'
			transform: [
				name: 'EmbedRefresh',
				fields: ['fax_item_embed']
			]
	to_name:
		model:
			type: 'text'
		view:
			columns: 2
			label: 'Recipient Name'
			note: 'This will be used as the recipient name on the cover page'
			transform: [
				name: 'EmbedRefresh',
				fields: ['fax_item_embed']
			]

	from_number:
		model:
			type: 'text'
		view:
			columns: 2
			label: 'From Number'
			format: 'us_phone'
			transform: [
				name: 'EmbedRefresh',
				fields: ['fax_item_embed']
			]
	from_num:
		model:
			required: true
			source: 'fax_numbers'
			sourcefilter:
				site_id:
					'dynamic': '{site_id}'
				is_default:
					'static': 'Yes'
			if:
				'*':
					readonly: 
						fields: ['from_number']
		view:
			label: 'Select Fax Number'
			control: 'select'
			class: 'select_prefill'
			transform: [{
				name: 'SelectPrefill'
				url: 'api/form/fax_numbers/?limit=1&fields=list&sort=id&filter=id:'
				fields:
					'from_name': ['site_id_auto_name']
					'from_number': ['fax_number']
			},
			{
				name: 'EmbedRefresh',
				fields: ['fax_item_embed']
			}
			]

	from_name:
		model:
			type: 'text'
		view:
			columns: 2
			label: 'Sender Name'
			note: 'This will be used as the sender name on the cover page'
			transform: [
				name: 'EmbedRefresh',
				fields: ['fax_item_embed']
			]
	message:
		model:
			type: 'text'
		view:
			control: 'area'
			label: 'Message'
			note: 'This will be used as comment on the cover page'
			transform: [
				name: 'EmbedRefresh',
				fields: ['fax_item_embed']
			]
	fax_quality:
		model:
			source: ['Low', 'Standard', 'HD']
			default: 'Standard'
		view:
			label: 'Fax Quality'
			columns: 3
	user_id:
		model:
			source: 'user'
		view:
			label: 'User'
			template: '{{user.id}}'
			readonly: true
			transform: [
				name: 'EmbedRefresh',
				fields: ['fax_item_embed']
			]

	fax_item_embed:
		model:
			required: false
			multi: true
			sourcefilter:
				fax_id:
					'dynamic': '{fax_id}'
				source:
					'static': 'Fax'
		view:
			embed:
				form: 'document'
				add_preset:
					assigned_to: 'Patient'
					patient_id: '{patient_id}'
					source: 'Fax'
					fax_id: '{fax_id}'
					sent_dt: '{sent_dt}'
					site_id: '{site_id}'
					user_id: '{user_id}'
			label: 'Attach Documents'
			grid:
				add: 'inline'
				hide_cardmenu: true
				edit: true
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]

model:
	save:false
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		review:     ['admin', 'cm', 'cma', 'nurse', 'pharm']
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
	name: '{status} {from_number} {to_number}'
	indexes:
		many: [
			['user_id']
			['to_number']
			['from_number']
			['status']
		]
		unique: [
			['fax_id']
		]
	bundle: ['fax']	
	prefill:
		user:
			link:
				id: 'user_id'
		site:
			link:
				id: 'site_id'
		patient:
			link:
				id: 'patient_id'
	sections:
		'Send Fax':
			fields: ['fax_id', 'sent_dt', 'status', 'site_id', 'fax_type', 'template_id', 'template_code', 'patient_id', 'from_num', 'from_name', 'from_number',  'to_number', 'to_name',  'scheduled_dt', 'message', 'fax_quality', 'user_id']
		'Fax Documents':
			fields: ['fax_item_embed']
view:
	dimensions:
        width: '80%'
        height: '80%'
	comment: 'Fax > Send Fax'
	find:
		basic: ['site_id', 'status', 'to_number', 'from_number', 'sent_dt']
	grid:
		fields: ['created_on', 'site_id', 'status', 'to_number', 'from_number', 'sent_dt']
		sort: ['-created_on', '-sent_dt']
	label: 'Send Fax'
	open: 'edit'