fields:

	code:
		model:
			required: true
		view:
			label: 'Code'
			columns: 3

	name:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name', 'code']

view:
	comment: 'Manage > Surescripts Message Prefill'
	find:
		basic: ['name', 'code']
	grid:
		fields: ['name', 'code']
		sort: ['name']
	label: 'Surescripts Message Prefill'
	open: 'read'
