fields:
	date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Reminder Date'
			findrange: true
			columns: 3

	sales_account_id:
		model:
			# required: true
			source: 'sales_account'
			type: 'int'
		view:
			label: 'Sales Account'
			columns: 3

	time:
		model:
			default: '9:00 am'
			required: true
			type: 'time'
		view:
			label: 'Reminder Time'
			columns: 3

	completed:
		model:
			required: true
			source: ['No', 'Yes']
			default: 'No'
		view:
			control: 'radio'
			label: 'Completed?'
			findfilter: 'No'
			columns: 3
	name:
		model:
			required: true
		view:
			label: 'Task'
			columns: 3

	details:
		model:
			required: false
		view:
			control: 'area'
			label: 'Tasks Details'
			columns: -1

	time_range:
		model:
			source:['This Week', 'This Month', 'This Quarter']
		view:
			offscreen:true
			label: 'Time Range'
			control: 'select'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'liaison']
		delete:     ['admin']
		read:       ['admin', 'csr', 'liaison']
		read_all:   ['admin', 'csr', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'liaison']
		write:      ['admin', 'csr', 'liaison']
	reportable: true
	indexes:
		unique: [
			['sales_account_id','name']
		]
	name: ['name', 'sales_account_id', 'date']
	sections:
		'Task':
			fields: ['sales_account_id', 'date','time', 'name', 'completed', 'details' ]

view:
	comment: 'Tasks'
	find:
		basic: ['sales_account_id', 'date', 'time', 'name', 'time_range', 'completed', 'details']
	grid:
		fields: ['date', 'time', 'name', 'details', 'sales_account_id', 'completed']
		sort: ['-date','-time']
	label: 'Tasks'
