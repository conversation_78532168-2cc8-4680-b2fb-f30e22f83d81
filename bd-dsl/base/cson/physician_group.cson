fields:
	group_name:
		model:
			max: 64
			required: true
		view:
			label: 'Group Name'

	delegates:
		model:
			subfields:
				firstname:
					label: 'First Name'
				lastname:
					label: 'Last Name'
				email:
					label: 'Email Address'
			type: 'json'
			
		view:
			control: 'grid'
			label: 'Physician Delegates'
			note: 'If the users do not have an account it will be created. Users must have a Physician role'
	
	approved:
		model:
			multi: true
			type: 'text'
			source: "user"
		view:
			label: 'Approved Delegates'
			offscreen: false
			readonly: true
	
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'liaison', 'csr', 'pharm']
		write:      ['admin', 'csr','liaison', 'pharm']
	indexes:
		unique: [
			['group_name']
		]
	name: '{group_name}'
	sections_group: [
		'Physician Group':
			note: 'Group to add delegates to'
			fields: ['group_name']
		'Delegates':
			fields: ['delegates', 'approved']
	]
view:
	hide_cardmenu: true
	validate: [
				name: 'GridEmailValidator'
				fields: ['delegates']
				error: 'Email Address must be unique'
			]
	comment: 'Manage > Physician Group'
	find:
		basic: ['group_name']
	grid:
		fields: ['group_name', 'approved']
		sort: ['group_name']
	label: 'Physician Group'
