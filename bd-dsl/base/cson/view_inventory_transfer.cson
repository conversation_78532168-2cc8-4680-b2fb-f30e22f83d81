fields:

	# Prefill with current site info
	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			columns: 3
			label: 'From Site'
			findmulti: true
			validate: [
				{
					name: 'CheckSites'
				}
			]

	to_site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			columns: 3
			label: 'To Site'
			findmulti: true
			validate: [
				{
					name: 'CheckSites'
				}
			]

	subform_items:
		model:
			multi: true
			source: 'view_inventory_transfer_item'
			type: 'subform'
		view:
			label: 'Items'
			grid:
				edit: true
				add: 'inline'
				split: true
				fields: ['inventory_id', 'serial_id', 'lot_id', 'quantity']
				label: ['Item', 'Serial', 'Lot', 'Quantity']
				width: [35, 25, 25, 15]

	tracking_no:
		view:
			columns: 3
			label: 'Tracking Number'
	comments:
		view:
			control: 'area'
			label: 'Comments'

model:
	save: false
	access:
		create:     []
		create_all: ['admin','pharm','csr','cm']
		delete:     []
		read:       ['admin','pharm','csr','cm']
		read_all:   ['admin','pharm','csr','cm']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      ['admin','pharm','csr','cm']

	name: '{site_id} to {to_site_id}'
	sections:
		'Inventory Transfer':
			fields: [ 'site_id', 'to_site_id', 'tracking_no', 'comments']
		'Items':
			fields: ['subform_items']

view:
	comment: 'Inventory Transfer'
	grid:
		fields: ['site_id', 'to_site_id', 'comments']
		sort: ['-id']
	label: 'Inventory Transfer'
	open: 'edit'