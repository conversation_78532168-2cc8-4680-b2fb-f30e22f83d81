fields:
	name:
		model:
			max: 128
			required: true
		view:
			label: 'Flag Name'
			findunique: true
			columns: 3

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
			columns: 3

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['manage']
	sync_mode: 'mixed'
	indexes:
		unique: [
			['name']
		]
	name: ['name']
	sections:
		'File Flag':
			fields: ['name','allow_sync','active']

view:
	comment: 'Manage > File Flag'
	find:
		basic: ['name']
	grid:
		fields: ['name']
		sort: ['name']
	label: 'File Flag'
	open: 'read'
