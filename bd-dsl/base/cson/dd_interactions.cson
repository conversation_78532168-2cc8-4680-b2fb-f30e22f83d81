fields:
	# Links
	ndc_1:
		model:
			required: true
			search: 'A'
		view:
			columns: 3
			label: 'Drug 1'
			findunique: true
	
	ndc_2:
		model:
			required: true
		view:
			columns: 3
			label: 'Drug 2'

	sl:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Severity Level'

	description:
		view:
			label: 'Description'

model:
	access:
		create:     ['patient']
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['patient']
		review:     ['admin', 'pharm']
		update:     ['cm', 'cma', 'liaison', 'nurse', 'patient']
		update_all: ['admin', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		# unique: [
		# 		['careplan_id']
		# ]
	sections_group: [
		'Drug Drug Interactions':
			fields: ['ndc_1', 'ndc_2', 'sl', 'description']
	]
	name: ['ndc_1']

view:
	comment: 'Patient > Drug Drug Interactions'
	find:
		basic: ['ndc_1']
	grid:
		fields: ['created_on']
		sort: ['created_on']
	icon: 'medication'
	label: 'Drug Drug Interactions'
	open:'read'