#TABLE RPRDUOM0_PRICE_QTY_UOM
fields:
    code:
        model:
            max: 64
            required: true
        view:
            label: 'Code'

    #PRICE_UOM_ID
    price_uom_id:
        model:
            type: 'int'
        view:
            label: 'Price UOM ID'
            findunique: true
            readonly: true
            columns: 3

    #PRICE_UOM_DESC
    price_uom_desc:
        model:
            type: 'text'
        view:
            label: 'Price UOM Description'
            columns: 3

model:
    access:
        create:     []
        create_all: ['admin']
        delete:     ['admin']
        read:       ['admin']
        read_all:   ['admin']
        request:    []
        update:     []
        update_all: ['admin']
        write:      ['admin']
    bundle: ['reference']
    sync_mode: 'full'
    name: "{price_uom_id} - {price_uom_desc}"
    indexes:
        many: [
            ['price_uom_id']
        ]
    sections:
        'Details':
            hide_header: true
            indent: false
            fields: ['price_uom_id', 'price_uom_desc']

view:
    comment: 'Manage > List FDB Price Quantity UOM'
    find:
        basic: ['price_uom_id']
    grid:
        fields: ['price_uom_id', 'price_uom_desc']
    label: 'List FDB Price Quantity UOM' 