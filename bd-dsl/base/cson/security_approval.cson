fields:

	assigned_team_id:
		model:
			source: 'wf_queue_team'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Assigned Team'
			readonly: true

	payer_id:
		model:
			type: 'int'
			source: 'payer'
		view:
			columns: 2
			label: 'Payer'
			readonly: true

	patient_id:
		model:
			type: 'int'
			source: 'patient'
		view:
			columns: 2
			label: 'Patient'
			readonly: true

	source_id:
		model:
			type: 'int'
			required: false
		view:
			readonly: true
			offscreen: true
			label: 'Source Id'

	source_form:
		model:
			required: false
		view:
			readonly: true
			offscreen: true
			label: 'Source Form'

	request_datetime:
		model:
			type: 'datetime'
		view:
			columns: 2
			label: 'Request Date/Time'
			template: '{{now}}'
			readonly: true

	request_from:
		model:
			type: 'int'
			source: 'user'
		view:
			columns: 2
			label: 'Request From'
			readonly: true

	action_id:
		model:
			multi: true
			required: true
			source: 'list_security_action'
			sourceid: 'code'
		view:
			columns: 2
			readonly: true
			label: 'Action Id'

	description:
		model:
			required: true
		view:
			label: 'Description'
			readonly: true

	status:
		model:
			required: true
			default: 'Pending'
			source: ['Pending', 'Approved', 'Rejected']
			if:
				'*':
					fields: ['decision_by', 'decision_on']
		view:
			columns: 2
			class: 'status'
			control: 'radio'
			label: 'Approval Decision'
			findfilter: 'Pending'
			validate: [
				{
					name: 'PrefillCurrentUser'
					condition:
						status: '*'
					dest: 'decision_by'
				},
				{
					name: 'PrefillCurrentDateTime'
					condition:
						status: '*'
					dest: 'decision_on'
				}
			]

	decision_by:
		model:
			type: 'int'
			source: 'user'
		view:
			columns: -2
			label: 'Decision By'
			readonly: true

	decision_on:
		model:
			type: 'datetime'
		view:
			columns: 2
			label: 'Decision Date/Time'
			readonly: true

	action_form_id:
		model:
			type: 'int'
			required: false
		view:
			readonly: true
			offscreen: true
			label: 'Action Form Id'

	action_form:
		model:
			required: true
		view:
			readonly: true
			offscreen: true
			label: 'Action Form'

	action_type:
		model:
			source: ['Create', 'Update', 'Create/Update', 'Post', 'Function']
			if:
				'Post':
					fields: ['action_path']
		view:
			label: 'Action Type'
			readonly: true
			offscreen: true

	action_path:
		model:
			type: 'text'
		view:
			label: 'Action Path'
			readonly: true
			offscreen: true

	action_data:
		model:
			type: 'json'
		view:
			label: 'Action Data'
			readonly: true
			offscreen: true

	action_complete:
		model:
			source: ['Yes']
		view:
			columns: 2
			label: 'Action Complete'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm']
		delete:     []
		read:       ['admin', 'cm']
		read_all:   ['admin', 'cm']
		request:    []
		update:     ['admin', 'cm']
		update_all: ['admin', 'cm']
		write:      ['admin', 'cm']
	reportable: true
	name: ['patient_id', 'assigned_team_id', 'action_id', 'status']
	sections:
		'Approval Request':
			fields: ['assigned_team_id', 'payer_id', 'patient_id', 'request_datetime', 'request_from', 'action_id', 'description']
		'Approval Decision':
			fields: ['status','decision_by','decision_on']
view:
	hide_cardmenu: true
	block:
		update:
			if: 'status'
			except: ['Pending']
	comment: 'Approval Request'
	grid:
		fields: ['patient_id', 'request_from', 'action_id', 'description', 'status']
		sort: ['-created_on']
	label: 'Approval Request'
