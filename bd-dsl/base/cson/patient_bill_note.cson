fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
			if:
				'*':
					sections: ['Links']
					fields: ['link_type']
		view:
			label: 'Patient Id'
			columns: 4

	followup_date:
		model:
			required: false
			type: 'date'
			if:
				'*':
					fields: ['followup_note']
		view:
			columns: 4
			label: 'Followup Date'
			validate: [
				name: 'Date<PERSON>alida<PERSON>'
				require: 'future'
			]

	followup_note:
		model:
			required: false
		view:
			columns: 2
			label: 'Followup Note'

	subject:
		model:
			required: true
			max: 4096
			dynamic:
				source: 'list_bill_note_subject'
		view:
			label: 'Subject'
			columns: -2
			findunique: true

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'patient_bill_note'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Other Form'
					direct_attachment: 'Yes'
					source: 'Scanned Document'
					form_code: '{code}'
					form_name: 'patient_bill_note'
					patient_id: '{patient_id}'
					form_filter: 'patient_bill_note'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

	link_type:
		model:
			required: false
			source: ['Insurance', 'Invoice', 'Authorization', 'Order', 'Prescription']
			if:
				'Insurance':
					fields: ['embed_insurance']
					sections: ['Links Embed']
				'Invoice':
					fields: ['embed_invoice']
					sections: ['Links Embed']
				'Authorization':
					fields: ['embed_pa']
					sections: ['Links Embed']
				'Order':
					fields: ['embed_order']
					sections: ['Links Embed']
				'Prescription':
					fields: ['embed_rx']
					sections: ['Links Embed']
		view:
			control: 'radio'
			label: 'Link Type'
			columns: 2

	embed_insurance:
		model:
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			embed:
				selectable: true
				form: 'patient_insurance'
			grid:
				add: 'none'
				rank: 'none'
				edit: false
				fields: ['payer_id', 'type_id', 'cardholder_id', 'policy_number', 'bin', 'pcn', 'rank']
				label: ['Payer', 'Type', 'ID', 'Policy', 'BIN', 'PCN', 'Rank']
				width: [25, 15, 15, 15, 10, 10, 10]
			label: 'Insurance(s)'
			validate: [
				name: 'CollectEmbedFieldIds'
				target: 'insurance_ids'
			]

	embed_invoice:
		model:
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				selectable: true
				query: 'master_open_invoice_embed'
			grid:
				add: 'none'
				rank: 'none'
				edit: false
				fields: ['billed_date', 'invoice_no', 'payer', 'claim_substatus', 'billed', 'expected', 'balance']
				label: ['Date Billed', 'Invoice No', 'Payer', 'Claim Status', 'billed', 'expected', 'balance']
				width: [15, 15, 25, 15, 10, 10, 10]
			label: 'Invoice(s)'
			validate: [
				name: 'CollectEmbedFieldIds'
				target: 'invoice_ids'
			]

	embed_pa:
		model:
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': ['1', '3', '2','4', '5', '6', '9']
		view:
			embed:
				selectable: true
				form: 'patient_prior_auth'
			grid:
				add: 'none'
				rank: 'none'
				edit: false
				fields: ['insurance_id', 'auth_type', 'status_id', 'pa_type', 'submission_datetime', 'number']
				label: ['Payer', 'Type', 'Status', 'PA Type', 'Sub Date', 'Auth No']
				width: [20, 15, 15, 20, 20, 10]
			label: 'Authorization(s)'
			validate: [
				name: 'CollectEmbedFieldIds'
				target: 'pa_ids'
			]

	embed_order:
		model:
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				selectable: true
				query: 'embed_master_orders'
			grid:
				add: 'none'
				rank: 'none'
				edit: false
				fields: ['order_no', 'order_date', 'order_type', 'order_status']
				label: ['Order #', 'Order Date', 'Order Type', 'Order Status']
				width: [15, 15, 20, 15]
			label: 'Order(s)'
			validate: [
				name: 'CollectOrderItemFields'
			]

	embed_rx:
		model:
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				selectable: true
				query: 'embed_master_rx'
			grid:
				add: 'none'
				rank: 'none'
				edit: false
				fields: ['rx_no', 'drug', 'last_fill_date', 'next_fill_date', 'dispense_status', 'payer']
				label: ['Rx No', 'Drug', 'Last Fill Date', 'Next Fill Date', 'Disp Status', 'Payer']
				width: [10, 25, 15, 15, 15, 20]
			label: 'Prescription(s)'
			validate: [
				name: 'CollectEmbedFieldIds'
				target: 'rx_ids'
			]

	invoice_ids:
		model:
			multi: true
			source: 'billing_invoice'
		view:
			label: 'Invoice(s)'
			readonly: true
			offscreen: true

	pa_ids:
		model:
			multi: true
			source: 'patient_prior_auth'
		view:
			label: 'PA(s)'
			readonly: true
			offscreen: true

	orderp_ids:
		model:
			multi: true
			source: 'careplan_orderp_item'
		view:
			label: 'Single Prescription Order(s)'
			readonly: true
			offscreen: true

	orderi_ids:
		model:
			multi: true
			source: 'careplan_order_item'
		view:
			label: 'Therapy Set Order(s)'
			readonly: true
			offscreen: true

	insurance_ids:
		model:
			multi: true
			source: 'patient_insurance'
		view:
			label: 'Insurance'
			readonly: true
			offscreen: true

	rx_ids:
		model:
			multi: true
			source: 'careplan_order_rx'
		view:
			label: 'Prescription(s)'
			readonly: true
			offscreen: true

	note:
		view:
			control: 'area'
			label: 'Note'

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	name: ['patient_id']
	indexes:
		many: [
			['patient_id']
			['rx_ids']
			['insurance_ids']
			['orderi_ids']
			['orderp_ids']
			['pa_ids']
			['invoice_ids']
		]
		unique: [
			['code']
		]
	reportable: true
	sections_group: [
		'Billing Note':
			hide_header: true
			indent: false
			fields: ['code', 'patient_id', 'followup_date', 'followup_note', 'subject', 'link_type', 'note']
			tab: 'Note'
		'Links':
			indent: false
			fields: ['link_type', 'invoice_ids', 'pa_ids', 'orderp_ids', 'orderi_ids', 'insurance_ids', 'rx_ids']
			tab: 'Note'
		'Links Embed':
			hide_header: true
			indent: false
			fields: ['embed_invoice', 'embed_pa', 'embed_order', 'embed_rx', 'embed_insurance']
			tab: 'Links'
		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'
	]

view:
	dimensions:
		width: '90%'
		height: '75%'
	hide_cardmenu: true
	comment: 'Patient > Billing Assessment'
	find:
		basic: ['created_on', 'created_by', 'subject']
	grid:
		fields: ['created_on', 'created_by','subject', 'note']
		sort: ['-created_on']
	label: 'Billing Assessment'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']