fields:

	rx_no:
		model:
			required: false
			source: 'careplan_order_rx'
			type: 'text'
		view:
			label: ''
			readonly: true
			offscreen: true

	order_no:
		model:
			required: false
			type: 'text'
		view:
			label: 'Order #'
			readonly: true
			offscreen: true

	order_item_id:
		model:
			required: false
			source: 'careplan_order_item'
			type: 'int'
		view:
			label: 'Order Item Id'
			readonly: true
			offscreen: true
	
	orderp_item_id:
		model:
			required: false
			source: 'careplan_orderp_item'
			type: 'int'
		view:
			label: 'Order (Single Prescription) ID'
			readonly: true
			offscreen: true

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true
			columns: 2

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			columns: 2

	insurance_id:
		model:
			prefill: ['patient_insurance.id']
			source: 'patient_insurance'
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				billing_method_id:
					'static': 'ncpdp'
				active:
					'static': 'Yes'
		view:
			columns: 2
			class: 'select_prefill'
			label: 'Insurance'
			readonly: true
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_insurance/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'payer_id': ['payer_id']
			]

	payer_id:
		model:
			source: 'payer'
			required: true
		view:
			label: 'Payer'
			readonly: true
			offscreen: true

	prescriber_id:
		model:
			prefill: ['patient_prescriber.id']
			required: true
			type: 'int'
			source: 'patient_prescriber'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			columns: 2
			label: 'Prescriber'

	inventory_id:
		model:
			required: true
			source: 'inventory'
			query: 'select_inv_in_stock'
			querytemplate: 'inventoryTemplate'
			sourcefilter:
				active:
					'static': 'Yes'
				type:
					'static': ['Drug', 'Compound']
		view:
			columns: -2
			label: 'Drug'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'type': ['type']
					'compound_type': ['compound_type']
					'comp_dsg_fm_code': ['comp_dsg_fm_code']
					'comp_disp_unit': ['comp_disp_unit']
					'route_id': ['route_id']
			]

	route_id:
		model:
			required: true
			source: 'list_route'
			sourceid: 'code'
		view:
			columns: 4
			label: 'Route'

	type:
		model:
			if:
				'Compound':
					prefill:
						compound_code: '2'
						require_fields: ['compound_type', 'comp_dsg_fm_code', 'comp_disp_unit']
				'Drug':
					prefill:
						compound_code: '1'
		view:
			label: 'Inventory Type'
			readonly: true
			offscreen: true

	date_of_service:
		model:
			required: true
			type: 'date'
		view:
			columns: 4
			label: 'Date of Service'
			template: '{{now}}'

	dispense_quantity:
		model:
			type: 'decimal'
			max: 9999999.999
			rounding: 1
			min: 1
			required: true
		view:
			columns: 4
			label: 'Quantity'
			class: 'numeral'
			format: '0,0.[000000]'

	day_supply:
		model:
			type: 'int'
			required: true
			max: 999
			default: 28
		view:
			columns: 4
			reference: '405-D5'
			note: '405-D5 - Max 999'
			label: 'Days Supplied'

	compound_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '406-D6'
		view:
			columns: -4
			reference: '406-D6'
			note: '406-D6'
			label: 'Compound Code'

	compound_type:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '996-G1'
		view:
			columns: 4
			label: 'Compound Type'

	comp_dsg_fm_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '450-EF'
		view:
			columns: 4
			label: 'Compound Dosage Form Code'
			note: '450-EF'
			reference: '450-EF'

	comp_disp_unit:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '451-EG'
		view:
			columns: 4
			label: 'Compound Dispensing Unit'
			note: '451-EG'
			reference: '451-EG'

model:
	save: false
	prefill:
		patient:
			link:
				id: 'patient_id'
		patient_prescriber:
			link:
				patient_id: 'patient_id'
			filter:
				primary: 'Yes'
			max: 'created_on'
		patient_insurance:
			link:
				patient_id: 'patient_id'
			filter:
				billing_method_id: 'ncpdp'
			min: 'rank'
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
	reportable: false
	name: ['patient_id', 'insurance_id', 'inventory_id', 'prescriber_id']
	sections_group: [
		'Pharmacy Test Claim':
			hide_cardmenu: true
			hide_header: true
			sections: [
				'Claim Details':
					hide_header: true
					fields: ['rx_no', 'order_no', 'order_item_id', 'orderp_item_id', 'patient_id',  'insurance_id', 'prescriber_id', 'site_id', 'payer_id',
					'inventory_id', 'route_id', 'date_of_service','dispense_quantity', 'day_supply', 'type']
				'Compound Details':
					fields: ['compound_code', 'compound_type', 'comp_dsg_fm_code', 'comp_disp_unit']
			]
		]

view:
	dimensions:
		width: '75%'
		height: '800px'
	hide_cardmenu: true
	comment: 'Pharmacy Test Claim'
	find:
		basic: ['insurance_id']
	grid:
		fields: ['insurance_id', 'inventory_id', 'prescriber_id', 'dispense_quantity']
		sort: ['-id']
	label: 'Pharmacy Test Claim'
