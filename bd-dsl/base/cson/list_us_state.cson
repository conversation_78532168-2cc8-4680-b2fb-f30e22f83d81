fields:
	code:
		model:
			max: 2
			min: 2
			required: true
			transform: [
					name: 'UpperCase'
			]
		view:
			label: 'State Code'
			columns: 2
	name:
		model:
			max: 32
			min: 2
			required: true
		view:
			label: 'State Name'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'payer', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'payer', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
			['name']
		]
	name: ['code']
	sections:
		'Default':
			fields: ['code', 'name']

view:
	comment: 'Manage > US States'
	find:
		basic: ['code', 'name']
	grid:
		fields: ['code', 'name']
		sort: ['code']
	label: 'US States'
