fields:

	inventory_id:
		model:
			required: true
			source: 'inventory'
		view:
			label: 'Transferred Item'
			readonly: true

	quantity:
		model:
			required: true
			rounding: 1
			type: 'decimal'
		view:
			label: 'Quantity'
			readonly: true

	cost_each:
		model:
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
		view:
			label: 'Cost Each'
			class: 'numeral'
			format:'$0,0.0000'
model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin','pharm','csr','cm','nurse']
		read_all:   ['admin','pharm','csr','cm','nurse']
		request:    []
		review:     []
		update:     []
		update_all: ['admin','pharm','csr','cm']
		write:      ['admin','pharm','csr','cm']

	name: ['inventory_id', 'cost_each', 'quantity']
	sections:
		'PO Item Receipt':
			fields: ['inventory_id', 'quantity', 'cost_each']

view:
	hide_cardmenu: true
	comment: 'PO Item Receipt'
	grid:
		fields: ['inventory_id', 'quantity', 'cost_each']
		sort: ['-id']
	label: 'PO Item Receipt'
	open: 'edit'