fields:

	claim_no:
		model:
			type: 'text'
		view:
			label: 'Claim No'
			readonly: true

	response_type:
		model:
			source: ['999', '835', '277']
		view:
			control: 'radio'
			label: 'Response Type'
			readonly: true

	response_source:
		model:
			source: ['Client', 'Admin Server']
		view:
			label: 'Response Source'
			readonly: true

	response_meta:
		model:
			type: 'json'
		view:
			label: 'Meta Data'
			readonly: true

	request_raw_x12:
		view:
			label: 'Raw X12 response'
			readonly: true

	request_raw_json:
		model:
			type: 'json'
		view:
			label: 'Raw Json request'
			readonly: true

	response_raw_x12:
		view:
			label: 'Raw X12 response'
			readonly: true

	response_raw_json:
		model:
			type: 'json'
		view:
			label: 'Raw Json response'
			readonly: true

	raw_report_url:
		view:
			label: 'Raw Report URL'
			readonly: true
			offscreen: true

	s3_filehash:
		view:
			label: 'S3 File Hash'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['response_type']
	indexes:
		many: [
			['response_type']
		]

	sections:
		'Response Log':
			fields: ['claim_no', 'response_type', 'response_meta', 'request_raw_x12', 'request_raw_json', 'response_raw_x12', 'response_raw_json']

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Medical Claim Response Log'
	find:
		basic: ['created_on', 'response_type', 'response_source']
	grid:
		fields: ['created_on', 'created_by', 'response_type', 'response_source']
		width: [25, 25, 25, 25]
		sort: ['-created_on']
	label: 'Medical Claim Response Log'
	open: 'read'