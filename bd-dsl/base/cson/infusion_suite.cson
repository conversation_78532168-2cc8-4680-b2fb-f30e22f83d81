fields:

	organization_name:
		model:
			min: 1
			max: 50
			required: true
		view:
			columns: 2
			label: 'Service Facility Name'
			reference: 'NM103'

	nurse_id:
			model:
				multi: true
				source: 'user'
				sourcefilter:
					role:
						static: ['nurse']
			view:
				columns: 2
				label: 'Assigned Nurses'

	npi:
		model:
			required: true
			type: 'text'
			max: 10
		view:
			label: 'NPI'
			reference: 'NM109'
			columns: -3
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid NPI, must be 10 digits'
			}]

	phone_name:
		model:
			min: 1
			max: 60
		view:
			columns: 3
			label: 'Phone Name'
			reference: 'PER02'
			note: 'Letters, numbers, comma, \', -, . only'
			validate: [
					name: '<PERSON><PERSON>alida<PERSON>'
			]

	phone_number:
		model:
			required: true
			min: 1
			max: 256
		view:
			columns: 3
			reference: 'PER04'
			format: 'us_phone'
			label: "Phone #"

	address:
		model:
			required: true
			type: 'subform'
			multi: false
			source: 'infusion_suite_address'
		view:
			label: 'Address'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ['location']
	indexes:
		many: [
			['organization_name']
			['nurse_id']
		]

	name: ['organization_name']
	sections_group: [
		'Service Location':
			hide_header: true
			sections: [
				'Details':
					hide_header: true
					fields: ['organization_name', 'nurse_id', 'npi',
					'phone_name', 'phone_number']
				'Address':
					indent: false
					hide_header: true
					fields: ['address']
			]
	]

view:
	hide_cardmenu: true
	comment: 'Infusion Suite'
	hide_header: true
	grid:
		fields: ['organization_name', 'phone_name', 'phone_number']
		width: [50, 25, 25]
		sort: ['-created_on']
	label: 'Infusion Suite'
	open: 'read'