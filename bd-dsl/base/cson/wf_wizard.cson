fields:
	wizard_code:
		model:
			required: true
		view:
			label: 'Code'

	patient_id:
		model:
			source: 'patient'
		view:
			label: 'Patient'
	
	link_ids:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			readonly: true
			label: 'Link IDs'
	
	prefill_data:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			readonly: true
			label: 'Prefill Data'

	graph_data:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			readonly: true
			label: 'Defination'

	form_map:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			readonly: true
			label: 'Form Map'
	
	step_order:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			readonly: true
			label: 'step_order'


	wizard_step:
		model:
			source: 'wf_wizard_step'
			type: 'subform'
			multi: true
		view:
			label: 'Wizard Step'
model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	name: '{wizard_code} - {patient_id}'
	bundle: ['setup']
	sections:
		'Main':
			fields: ['wizard_code', 'patient_id']
		'Wizard Steps':
			fields: ['wizard_step']

view:
	comment: 'Worflow Wizard'
	find:
		basic: ['wizard_code', 'patient_id']
	grid:
		fields: ['wizard_code', 'patient_id']
	label: 'Worflow Wizard'
	open: 'read'
