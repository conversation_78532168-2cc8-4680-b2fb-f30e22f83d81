#TABLE: RMCRMA1_MEDICARE_MSTR
fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	ndc:
		model:
			type: 'text'
		view:
			label: 'National Drug Code'
			columns: 3

	mcr_region:
		model:
			type: 'text'
		view:
			label: 'Medicare Region'
			columns: 3

	mcr_datec:
		model:
			type: 'date'
		view:
			label: 'Medicare Billing Code Effective Date'
			columns: 3

	mcr_ref:
		model:
			type: 'text'
		view:
			label: 'Medicare Reference Code'
			columns: 3

	mcr_cov:
		model:
			type: 'text'
		view:
			label: 'Medicare Coverage Code'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['ndc', 'mcr_ref']
	indexes:
		many: [
			['ndc']
			['mcr_ref']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['ndc', 'mcr_region', 'mcr_datec', 'mcr_ref', 'mcr_cov']

view:
	comment: 'Manage > List FDB Medicare Master'
	find:
		basic: ['ndc', 'mcr_ref']
	grid:
		fields: ['ndc', 'mcr_region', 'mcr_datec', 'mcr_ref', 'mcr_cov']
	label: 'List FDB Medicare Master'
