fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	indication:
		model:
			max: 3
			required: true
			source: ['Atopic Dermatitis', 'Asthma']
			if:
				'Atopic Dermatitis':
					fields: ['concomitant_topical_therapy']
				'Asthma':
					fields: ['indication_asthma', 'exacerbations_per_month', 'most_recent_exacerbation', 'asthmaticus_count', 'asthmaticus_counseling']
		view:
			control: 'radio'
			label: 'Indication'

	indication_asthma:
		model:
			max: 3
			required: true
			source: ['With an eosinophilic type', 'With oral steroid dependence']
			if:
				'With an eosinophilic type':
					fields: ['indication_asthma_labs']
		view:
			control: 'radio'
			label: 'Indication Asthma'

	indication_asthma_labs:
		view:
			label: 'Labs'

	conjunctivitis:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you have a history of or currently being treated for conjunctivitis or keratitis?'

	parasitic_infection:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you have a history of or currently being treated for parasitic infection?'
	
	concomitant_topical_therapy:
		view:
			control: 'area'
			label: 'Concomitant topical therapies:'

	exacerbations_per_month:
		model:
			type: 'int'
		view:
			label: 'Number of asthma exacerbations in the past 6 months?'
	
	most_recent_exacerbation:
		view:
			label: 'Most recent asthma exacerbation'

	asthmaticus_count:
		model:
			type: 'int'
		view:
			label: 'Number of times patient experienced status asthmaticus?'

	asthmaticus_counseling:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Discussed with patient this medication should not be used to relieve an acute bronchospasm or status asthmaticus?'

	interactions:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Patient counseled on avoiding live vaccines while on this medication?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		assessment_dupixent:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Dupixent Pre-Assessment':
			area: 'preassessment'
			fields: ['indication', 'indication_asthma', 'indication_asthma_labs', 'conjunctivitis',
			'parasitic_infection']
		'Dupixent Assessment':
			fields: ['concomitant_topical_therapy', 'exacerbations_per_month', 'most_recent_exacerbation',
			'asthmaticus_count', 'asthmaticus_counseling']
		'Dupixent Drug Interactions':
			fields: ['interactions']
view:
	comment: 'Patient > Careplan > Assessment > Dupixent'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Initial Assessment: Dupixent'
	open: 'read'