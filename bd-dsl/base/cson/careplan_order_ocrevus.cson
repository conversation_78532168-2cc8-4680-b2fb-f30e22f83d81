fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	enrolled:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['id']
				'No':
					fields: ['no_why', 'comment']
		view:
			control: 'radio'
			label: 'Has patient enrolled with Genentech Access Solutions?'

	id:
		view:
			label: 'ID #'
	
	no_why:
		model:
			source: ['Form has been sent to patient for completion',
			'Form has been submitted to Genentech for processing']
		view:
			control: 'radio'
			label: 'Submission Status'

	comment:
		view:
			control: 'area'
			label: 'Comments'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'pharm']
		delete:     ['admin', 'csr', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'pharm']

	name: ['id', 'patient_id', 'careplan_id']
	sections:
		'Genentech Access Solutions':
			fields: ['enrolled', 'id',
			'no_why', 'comment']

view:
	hide_cardmenu: true
	comment: 'Patient > Intake Ocrevus Assessment'
	grid:
		fields: ['created_on', 'created_by', 'enrolled', 'id']
	label: 'Intake Ocrevus Assessment'
	open: 'read'
