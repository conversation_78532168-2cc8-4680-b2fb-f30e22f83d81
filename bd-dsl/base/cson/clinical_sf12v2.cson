fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'
			columns: 3

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_sf12v2.assessment_date']
		view:
			note: 'Recommended 2x per year'
			label: 'Last Assessment Date'
			readonly: true
			columns: 3

	# Health Improvement
	health_general:
		model:
			max: 32
			source: ['Excellent', 'Very Good', 'Good', 'Fair', 'Poor']
		view:
			control: 'radio'
			label: 'In general, would you say your health is?'
			columns: 2

	# Limitations

	limitation_moderate:
		model:
			max: 32
			source: ['Yes, limited a lot', 'Yes, limited a little', 'No, not limited at all']
		view:
			control: 'radio'
			label: 'Moderate activities, such as moving a table, pushing a vacuum cleaner, bowling, or playing golf'
			columns: 2

	limitation_climbing_many:
		model:
			max: 32
			source: ['Yes, limited a lot', 'Yes, limited a little', 'No, not limited at all']
		view:
			control: 'radio'
			label: 'Climbing several flights of stairs'
			columns: 2

	# Daily Activities (Physical)
	phys_accomplish:
		model:
			max: 64
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Accomplished less than you would like'
			columns: 2

	phys_limited:
		model:
			max: 64
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Were limited in the kind of work or other activities'
			columns: 2

	# Daily Activities (Emotional)
	emo_accomplish:
		model:
			max: 3
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Accomplished less than you would like'
			columns: 2

	emo_careless:
		model:
			max: 3
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Did work or other activities less carefully than usual'
			columns: 2

	# General
	gen_pain_interfere:
		model:
			max: 32
			source: ['Not at all', 'A little bit', 'Moderately', 'Quite a bit', 'Extremely']
		view:
			control: 'radio'
			label: 'During the past 4 weeks, how much did pain interfere with your normal work (including both work outside the home and housework)?'
			columns: 2

	# 4-Week Review
	wkchk_calm:
		model:
			max: 32
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Have you felt calm and peaceful?'
			columns: 2

	wkchk_energy:
		model:
			max: 32
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Did you have a lot of energy?'
			columns: 2

	wkchk_downhearted:
		model:
			max: 32
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Have you felt downhearted and depressed?'
			columns: 2

	gen_social_interfere:
		model:
			max: 32
			source: ['All the time', 'Most of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'During the past 4 weeks, how much of the time has your physical health or emotional problems interfered with your social activities (like visiting friends, relatives, etc.)?'
			columns: 2

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_sf12v2:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Quality Of Life Survey':
			fields: ['assessment_date', 'last_assessment_date']
		'Health Improvement':
			fields: ['health_general']
			prefill: 'clinical_sf12v2'
		'Limitations':
			note: 'The following questions are about activities you might do during a typical day. Does your health now limit you in these activities? If so, how much?'
			fields: ['limitation_moderate', 'limitation_climbing_many']
			prefill: 'clinical_sf12v2'
		'Daily Activities (Physical)':
			note: 'During the past 4 weeks, how much of the time have you had any of the following problems with your work or other regular daily activities as a result of your physical health?'
			fields: ['phys_accomplish', 'phys_limited']
			prefill: 'clinical_sf12v2'
		'Daily Activities (Emotional)':
			note: 'During the past 4 weeks, have you had any of the following problems with your work or other regular daily activities as a result of any emotional problems (such as feeling depressed or anxious)?'
			fields: ['emo_accomplish', 'emo_careless']
			prefill: 'clinical_sf12v2'
		'Pain':
			fields: ['gen_pain_interfere']
			prefill: 'clinical_sf12v2'
		'4-Week Review':
			note: 'These questions are about how you feel and how things have been with you during the past 4 weeks. For each question, please give the one answer that comes closest to the way you have been feeling. How much of the time during the past 4 weeks...'
			fields: ['wkchk_calm', 'wkchk_energy', 'wkchk_downhearted']
			prefill: 'clinical_sf12v2'
		'Social':
			fields: ['gen_social_interfere']
			prefill: 'clinical_sf12v2'

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Quality of Life (SF12)'
	find:
		basic: ['assessment_date']
	grid:
		fields: ['created_on', 'assessment_date', 'created_by']
		sort: ['-id']
	label: 'Quality of Life (SF12)'
