fields:

	code:
		model:
			max: 64
			required: false
		view:
			label: 'NCI Code'
			columns: 3

	name:
		model:
			required: true
			max: 128
		view:
			label: 'Name'
			findunique: true
			columns: 3

	fdb_map:
		model:
			multi: false
			source:
				'1': 'Schedule 1'
				'2': 'Schedule 2'
				'3': 'Schedule 3'
				'4': 'Schedule 4'
				'5': 'Schedule 5'
		view:
			label: 'FDB DEA schedule Map'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: '{code} - {name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'fdb_map']

view:
	comment: 'Manage > DEA Schedule'
	find:
		basic: ['code', 'name']
	grid:
		fields: ['code', 'name']
		sort: ['code']
	label: 'DEA Schedule'
	open: 'read'
