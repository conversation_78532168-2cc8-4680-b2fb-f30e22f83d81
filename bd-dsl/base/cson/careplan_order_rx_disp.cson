fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			offscreen: true
			readonly: true

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'
			offscreen: true
			readonly: true

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site Id'
			offscreen: true
			readonly: true

	delivery_ticket_file_path:
		model:
			type: 'json'
		view:
			class: 'media-preview'
			control: 'file'
			label: 'Delivery Ticket'
			note: 'Max 100MB. Only images are supported.'
			readonly: true

	shipping_label_file_path:
		model:
			type: 'json'
		view:
			class: 'media-preview'
			control: 'file'
			label: 'Shipping Label'
			note: 'Max 100MB. Only images are supported.'
			readonly: true

	label_file_path:
		model:
			type: 'json'
		view:
			class: 'media-preview'
			control: 'file'
			label: 'Label'
			note: 'Max 100MB. Only images are supported.'
			readonly: true

	rx_no:
		model:
			type: 'text'
		view:
			label: 'Rx #'
			readonly: true
			offscreen: true

	rx_id:
		model:
			required: true
			source: 'careplan_order_rx'
			type: 'int'
		view:
			label: 'Associated Rx'
			readonly: true
			offscreen: true

	master_invoice_no:
		model:
			type: 'text'
		view:
			label: 'Master Invoice No'
			readonly: true
			offscreen: true

	test_claim_id:
		model:
			source: 'ncpdp'
			type: 'int'
		view:
			label: 'Test Claim'
			readonly: true

	order_no:
		model:
			required: false
		view:
			label: 'Order #'
			readonly: true
			offscreen: true

	delivery_ticket_id:
		model:
			source: 'careplan_delivery_tick'
			type: 'int'
		view:
			label: 'Delivery Ticket'
			readonly: true
			offscreen: true

	is_ncpdp:
		model:
			source: ['Yes']
		view:
			class: 'checkbox checkbox-only'
			control: 'checkbox'
			label: 'Is NCPDP?'
			readonly: true
			offscreen: true

	is_specialty:
		model:
			source: ['Yes']
		view:
			class: 'checkbox checkbox-only'
			control: 'checkbox'
			label: 'Is Specialty?'
			readonly: true
			offscreen: true

	fill_number:
		model:
			required: false
			type: 'int'
		view:
			label: 'Fill #'
			readonly: true
			columns: 4

	template_type:
		model:
			required: false
			source: ['PO', 'IV', 'Injection', 'Factor', 'Compound', 'TPN']
			if:
				'IV':
					fields: ['next_delivery_date']
				'Compound':
					fields: ['next_delivery_date']
				'TPN':
					fields: ['next_delivery_date']
		view:
			label: 'Template Type'
			control: 'radio'
			readonly: true
			offscreen: true

	next_fill_date:
		model:
			required: false
			type: 'date'
		view:
			label: 'Next Fill Date'
			readonly: true
			columns: 4

	status:
		model:
			required: false
			source: ['Order Entry/Setup', 'Claims to Adjudicate', 'Test Claim', 'Rx Verification', 'Ready to Contact', 'Ready to Refill', 'Print Labels / Fill Rx', 'Order Verification', 'Delivery Ticket Confirmation', 'Confirmed', 'Void']
		view:
			control: 'select'
			label: 'Status'
			readonly: true
			columns: 2

	next_delivery_date:
		model:
			required: false
			type: 'date'
		view:
			label: 'Next Delivery Date'
			readonly: true
			columns: 4

	last_dispense_date:
		model:
			required: false
			type: 'date'
		view:
			label: 'Last Fill Date'
			readonly: true
			columns: 4

	last_through_date:
		model:
			required: false
			type: 'date'
		view:
			label: 'Last Through'
			readonly: true
			columns: 4

	copay:
		model:
			default: 0.00
			rounding: 0.01
			type: 'decimal'
			min: 0
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Copay'

	last_event_id:
		model:
			source: 'list_wf_event'
			sourceid: 'code'
		view:
			label: 'Last Event'
			columns: 4

	refill_tracking:
		model:
			required: false
			source: ['Refills', 'Doses']
			if:
				'Doses':
					fields: ['doses_allowed', 'doses_remaining']
				'Refills':
					fields: ['refills', 'refills_remaining']
		view:
			columns: 2
			label: 'Refill Tracking'
			control: 'radio'
			readonly: true
			offscreen: true

	refills:
		model:
			type: 'int'
		view:
			columns: 4
			label: '# Refills'
			readonly: true

	refills_remaining:
		model:
			type: 'int'
		view:
			columns: 4
			label: '# Refills Remaining'
			readonly: true

	doses_allowed:
		model:
			type: 'int'
		view:
			columns: 4
			label: '# Doses Allowed'
			readonly: true

	doses_remaining:
		model:
			required: false
			type: 'int'
		view:
			columns: 4
			note: 'Recalculated after each work ticket is completed'
			label: '# Doses Remaining'
			readonly: true

	embed_delivery_ticket:
		model:
			required: false
			sourcefilter:
				id:
					'dynamic': '{delivery_ticket_id}'
				patient_id:
					'dynamic': '{patient_id}'
		view:
			readonly: true
			embed:
				form: 'careplan_delivery_tick'
				selectable: false
			grid:
				add: 'none'
				hide_cardmenu: true
				edit:true
				rank: 'none'
				fields: ['ticket_no', 'status', 'summary', 'tech_verified_by', 'verified_by', 'confirmed_by']
				label: ['Ticket No', 'Status', 'Summary', 'Tech', 'Verified By', 'Created By', 'Confirmed By']
				width: [10, 10, 20, 20, 20, 20]
			label: 'Delivery Ticket'

	embed_invoices:
		model:
			required: false
			sourcefilter:
				rx_id:
					'dynamic': '{rx_id}'
				patient_id:
					'dynamic': '{patient_id}'
		view:
			readonly: true
			embed:
				query: 'invoice_details_view'
				selectable: false
			grid:
				add: 'none'
				hide_cardmenu: true
				edit:true
				rank: 'none'
				fields: ['payer_id', 'invoice_no', 'billed_datetime', 'total_billed', 'total_expected', 'total_pt_pay']
				label: ['Payer', 'Invoice No', 'Billed Date/Time', 'Billed $', 'Expected $', 'Copay $']
				width: [35, 15, 20, 10, 10, 10]
			label: 'Invoices'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		request:    []
		update:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm','physician']
	name: ['rx_id']

	reportable: true
	indexes:
		many: [
			['patient_id']
			['careplan_id']
			['delivery_ticket_id']
			['rx_no']
			['rx_id']
			['test_claim_id']
			['order_no']
			['status']
			['master_invoice_no']
		]
	sections_group: [
		'Dispense Details':
			hide_header: true
			indent: false
			tab: 'Dispense Info'
			fields: ['site_id', 'rx_no', 'order_no', 'rx_id', 'is_ncpdp', 'master_invoice_no', 'is_specialty','fill_number',
			'next_fill_date', 'status', 'refill_tracking', 'refills', 'refills_remaining',
			'doses_allowed', 'doses_remaining', 'next_delivery_date', 'last_dispense_date', 'last_through_date', 'copay', 'delivery_ticket_id', 'test_claim_id']
		'Delivery Ticket':
			hide_header: true
			indent: false
			tab: 'Delivery Ticket'
			fields: ['delivery_ticket_file_path']
		'Delivery Ticket Form':
			hide_header: true
			indent: false
			tab: 'Delivery Ticket Form'
			fields: ['embed_delivery_ticket']
		'Shipping Label':
			hide_header: true
			indent: false
			tab: 'Shipping Label'
			fields: ['shipping_label_file_path']
		'Label':
			hide_header: true
			indent: false
			tab: 'Label'
			fields: ['label_file_path']

		'Invoices':
			hide_header: true
			indent: false
			tab: 'Invoices'
			fields: ['embed_invoices']
	]

view:
	dimensions:
		width: '90%'
		height: '85%'
	hide_cardmenu: true
	comment: 'Patient > Prescription Dispense Record'
	grid:
		fields: ['created_on', 'created_by', 'fill_number', 'status']
		sort: ['-created_on']
	label: 'Prescription Dispense Record'
	open: 'read'
