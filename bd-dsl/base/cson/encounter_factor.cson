fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	# Disease Specific Participation
	bleeding_episodes:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['bleeding_episodes_cnt']
		view:
			control: 'radio'
			label: 'Have you had any episodes of bleeding?'

	bleeding_episodes_cnt:
		model:
			min: 0
			type: 'int'
			required: true
		view:
			label: 'Number of bleeding episodes per week'

	joint_pain:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['joint_pain_scale']
		view:
			control: 'radio'
			label: 'Have you had any problems with your joints?'

	joint_pain_scale:
		model:
			required: true
			max: 2
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Based on a scale of 1 to 10 (10 being the highest), my pain usually averages around'

	joint_lesions:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you have any permanent joint lesion?'

	had_hemo:
		model:
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Did you have repeated hemorrhages since last visit?'

	had_surgery:
		model:
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Did you need any surgery because of your hemophilia since last month?'

	# Drug Administration

	factor_caregiver_demo:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
		view:
			control: 'radio'
			label: 'Patient/caregiver able to return demonstrate proper factor preparation and administration (if applicable)?'

	add_training:
		model:
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['add_training_details']
		view:
			control: 'radio'
			label: 'Will patient/caregiver need additional training?'

	add_training_details:
		model:
			required: true
		view:
			label: 'Describe additional education requirements'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		encounter_factor:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['careplan_id', 'order_id']
	sections:
		'Patient Questionnaire - Factor':
			area: 'questions'
			fields: ['bleeding_episodes', 'bleeding_episodes_cnt', 'joint_pain', 'joint_pain_scale', 'joint_lesions', 'had_hemo', 'had_surgery']
		'Drug Administration':
			fields: ['factor_caregiver_demo', 'add_training', 'add_training_details']
			prefill: 'encounter_factor'

view:
	comment: 'Patient > Careplan > Encounter > Factor'
	label: "Patient Encounter: Factor"
