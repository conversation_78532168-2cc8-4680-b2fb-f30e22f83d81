fields:
	code:
		model:
			type: 'text'
			required: true
		view:
			label: 'Code'

	name:
		model:
			type: 'text'
		view:
			label: 'Name'
	
	description:
		model:
			type: 'text'
		view:
			label: 'Description'
			control: 'area'
	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'


model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['setup']
	sections:
		'Security Group':
			fields: ['code', 'name', 'description', 'allow_sync', 'active']
	name: '{name}'
	sync_mode: 'mixed'

view:
	hide_cardmenu: true
	comment: 'Manage > Security Group'
	find:
		basic: ['name']
	grid:
		fields: ['code', 'name', 'description', 'active']
		sort: ['-created_on']
	label: 'Security Group'
