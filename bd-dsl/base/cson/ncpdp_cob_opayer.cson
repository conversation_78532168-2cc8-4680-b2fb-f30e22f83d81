fields:

	patient_id:
		model:
			type: 'int'
			source: 'patient'
			prefill: ['parent.patient_id']
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	pinsurance_id:
		model:
			source: 'patient_insurance'
			type: 'int'
			prefill: ['parent.pinsurance_id']
		view:
			label: 'Insurance'
			readonly: true
			offscreen: true

	insurance_id:
		model:
			type: 'int'
			required: true
			source: 'patient_insurance'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				id:
					'dynamic': '!{pinsurance_id}'
			if:
				'*':
					prefill:
						other_id_qualifier: '03'
		view:
			columns: 2
			label: 'Insurance'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/payer/?limit=1&filter=id:'
				fields:
					'payer_id': ['payer_id'],
					'other_id': ['bin']
			]

	payer_id:
		model:
			type: 'int'
			required: true
			source: 'payer'
		view:
			columns: 2
			label: 'Payer'
			readonly: true
			offscreen: true

	other_coverage_type:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '338-5C'
		view:
			columns: 4
			reference: '338-5C'
			note: '338-5C'
			label: 'Coverage Type'
			_meta:
				copy_forward: true
				link: 'payer_id'

	internal_control_number:
		model:
			max: 30
		view:
			columns: 4
			reference: '993-A7'
			note: '993-A7'
			label: 'ICN'
			offscreen: true
			readonly: true
			_meta:
				copy_forward: true
				link: 'payer_id'

	other_id_qualifier:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '339-6C'
			if:
				'*':
					require_fields: ['other_id']
		view:
			columns: -2
			reference: '339-6C'
			note: '339-6C'
			label: 'ID Qualifier'
			_meta:
				copy_forward: true
				link: 'payer_id'

	other_id:
		model:
			max: 10
			if:
				'*':
					require_fields: ['other_id_qualifier']
		view:
			columns: 4
			reference: '340-7C'
			note: '340-7C'
			label: 'Other Payer ID'
			_meta:
				copy_forward: true
				link: 'payer_id'

	other_date:
		model:
			type: 'date'
		view:
			columns: 4
			reference: '443-E8'
			note: '443-E8'
			label: 'Paid Date'
			validate: [
				name: 'DateValidator'
				require: 'past'
			]

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
			['insurance_id']
			['payer_id']
		]

	name: ['insurance_id', 'payer_id', 'other_coverage_type']
	sections:
		'Other Payer':
			hide_header: true
			indent: false
			fields: ['patient_id', 'pinsurance_id', 'insurance_id', 'payer_id', 'other_coverage_type',
			'internal_control_number', 'other_id_qualifier', 'other_id', 'other_date']

view:
	dimensions:
		width: '75%'
		height: '50%'
	hide_cardmenu: true
	comment: 'COB Other Payer'
	grid:
		fields: ['other_date', 'payer_id', 'other_coverage_type', 'other_id_qualifier', 'other_id']
		width: [20, 20, 20, 20, 20]
		sort: ['-created_on']
	label: 'Other Payer'
	open: 'read'