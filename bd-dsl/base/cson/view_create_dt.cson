fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			columns: 4
			readonly: true
			label: 'Patient'

	site_id:
		model:
			required: true
			source: 'site'
		view:
			columns: 4
			label: 'Site'

	rx_id:
		model:
			required: true
			source: 'careplan_order_rx'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status_id:
					'static': ['1', '5']
		view:
			label: 'Associated Rx'
			columns: -2
			readonly: false
			validate: [
				{
					name: 'LoadNextFillPrescriptionData'
				}
			]
			transform: [{
				name: 'EmbedRefresh'
				fields: ['dispense_sorder']
			}]

	dispense_prescription:
		model:
			required: true
			multi: true
			sourcefilter:
				site_id:
					'dynamic': '{site_id}'
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': ['Ready to Contact', 'Print Labels / Fill Rx']
				delivery_ticket_id:
					'static': 'null'
		view:
			embed:
				form: 'careplan_order_rx_disp'
				selectable: true
			grid:
				add: 'none'
				rank: 'none'
				edit: false
				selectall: false
				fields: ['rx_id', 'last_dispense_date', 'last_through_date', 'copay', 'last_event_id']
				label: ['Drug Name', 'Last Fill', 'Last Through', 'Copay', 'Last Event']
				width: [30, 15, 15, 15, 25]
			columns: 2
			label: 'Prescriptions'

	dispense_sorder:
		model:
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': ['1', '5']
				associated_rx_id:
					'dynamic': '{rx_id}'
		view:
			embed:
				form: 'careplan_orders_item'
				selectable: true
			grid:
				rank: 'none'
				add: 'none'
				edit: false
				selectall: true
				fields: ['inventory_id', 'hcpc_code', 'formatted_ndc', 'dispense_quantity']
				label: ['Item', 'HCPC', 'NDC', 'Quantity']
				width: [50, 15, 20, 15]
			label: 'Supply Orders'
			columns: 2

	service_from:
		model:
			required: true
			type: 'date'
		view:
			label: 'Service From'
			columns: 4

	service_to:
		model:
			required: true
			type: 'date'
		view:
			label: 'Service To'
			columns: 4

	supply_kit_id:
		model:
			source: 'inventory_supply_kit'
			sourcefilter:
				site_id:
					'dynamic': '{site_id}'
		view:
			label: 'Supply Kit'
			columns: 4
			transform: [{
				name: 'EmbedRefresh'
				fields: ['preview_skt']
			}]

	working_ticket_id:
		model:
			required: false
			multi: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': ['delivery_ticket', 'ready_to_fill', 'order_ver', 'pending_conf']
				void:
					'static': '!Yes'
				site_id:
					'dynamic': '{site_id}'
			if:
				'!':
					fields: ['service_from', 'service_to']
		view:
			embed:
				form: 'careplan_delivery_tick'
				selectable: true
			grid:
				rank: 'none'
				add: 'none'
				edit: false
				selectall: true
				fields: ['service_from', 'service_to', 'status', 'created_on', 'created_by']
				label: ['From', 'To', 'Status', 'Created On', 'Created By']
				width: [15, 15, 30, 20, 20]
			note: 'Select to add to existing ticket'
			columns: -2
			label: 'Add To Working Ticket'

	preview_skt:
		model:
			multi: true
			sourcefilter:
				parent_id:
					'dynamic': '{supply_kit_id}'
				parent_form:
					'static': 'inventory_supply_kit'
		view:
			embed:
				form: 'inventory_sk_item'
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['inventory_id', 'dispense_quantity', 'part_of_kit', 'bill', 'one_time_only']
				label: ['Item', 'Quantity', 'Billed as Kit?', 'Bill?', '1x Only']
				width: [40, 15, 15, 15, 15]
				selectall: true
			columns: 2
			label: 'Preview Supply Kit Contents'

model:
	save: false
	access:
		create:     []
		create_all: ['admin','pharm', 'cm']
		delete:     []
		read:       ['admin','pharm', 'cm']
		read_all:   ['admin','pharm', 'cm']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      ['admin','pharm', 'cm']
	prefill:
		patient:
			link:
				id: 'patient_id'

	name: ['patient_id', 'site_id']
	sections_group: [
		'Delivery Ticket':
			hide_header: true
			indent: false
			fields: ['patient_id', 'rx_id', 'site_id']
		'Dispense':
			hide_header: true
			indent: false
			fields: ['dispense_prescription', 'dispense_sorder']
		'Working Delivery Tickets/Supply Kit':
			hide_header: true
			indent: false
			fields: ['service_from', 'service_to', 'supply_kit_id', 'working_ticket_id', 'preview_skt']
	]

view:
	validate: [
		{
			name: "DTCreateBlockMissingItems"
		}
	]
	dimensions:
		width: '75%'
		height: '85%'
	hide_cardmenu: true
	comment: 'Create Delivery Ticket'
	grid:
		fields: ['patient_id', 'site_id', 'supply_kit_id']
		sort: ['-id']
	label: 'Create Delivery Ticket'
	open: 'edit'