#RNDCAT0_NDC_ATTRIBUTE
fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#NDC
	ndc:
		view:
			label: 'NDC'
			findunique: true
			readonly: true
			columns: 2

	#NDC_ATTRIBUTE_TYPE_CD
	# 8 = FDA Application number, used to reference med guides
	ndc_attribute_type_cd:
		model:
			type: 'int'
		view:
			label: 'Attribute Type'
			readonly: true
			columns: 2

	#NDC_ATTRIBUTE_SN
	ndc_attribute_sn:
		model:
			type: 'int'
		view:
			label: 'Attribute SN'
			readonly: true
			columns: 2

	#NDC_ATTRIBUTE_VALUE
	ndc_attribute_value:
		view:
			label: 'Attribute Value'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['ndc', 'ndc_attribute_type_cd', 'ndc_attribute_value']
	indexes:
		many: [
			['ndc']
			['ndc_attribute_type_cd']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['ndc', 'ndc_attribute_type_cd', 'ndc_attribute_sn', 'ndc_attribute_value']

view:
	comment: 'Manage > List FDB NDC Attributes'
	find:
		basic: ['ndc']
	grid:
		fields: ['ndc', 'ndc_attribute_type_cd', 'ndc_attribute_sn', 'ndc_attribute_value']
	label: 'List FDB NDC Attributes'
