fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'
			offscreen: true
			readonly: true

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	active_payer_id:
		model:
			source: 'payer'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
			required: false
		view:
			columns: 2
			label: 'Active Payers'
			offscreen: true
			readonly: true
			validate: [
					name: 'LoadActivePayers'
			]

	previous_payer_id:
		model:
			source: 'payer'
			type: 'int'
			transform: [
				name: 'AutoNameFk'
				]
			required: true
			sourcefilter:
				id:
					'dynamic': '{active_payer_id}'
		view:
			columns: 2
			label: 'Previous Payer'

	plan_name:
		view:
			columns: 2
			label: 'Plan ID/Name'
			class: 'claim-field'

	person_code:
		view:
			columns: 2
			label: 'Person Code'
			class: 'claim-field'

	partd_facility:
		model:
			source:
				'Y': 'Yes'
		view:
			columns: 2
			control: 'checkbox'
			label: 'Resides in CMS Part D Facility?'
			class: 'claim-field'
			offscreen: true
			readonly: true

	bill_notes:
		view:
			control: 'area'
			label: 'Bill Notes'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['previous_payer_id']
		]
	name: ['patient_id', 'previous_payer_id', 'plan_name', 'person_code']
	sections:
		'Previous Payer Conditional':
			fields: ['patient_id', 'active_payer_id', 'previous_payer_id', 'plan_name', 'person_code', 'partd_facility', 'bill_notes']

view:
	dimensions:
		width: '90%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Patient > Insurance Conditional'
	find:
		basic: ['previous_payer_id']
	grid:
		fields: ['previous_payer_id', 'plan_name', 'person_code', 'partd_facility', 'bill_notes']
		sort:['-created_on']
	label: 'Insurance Conditional'
