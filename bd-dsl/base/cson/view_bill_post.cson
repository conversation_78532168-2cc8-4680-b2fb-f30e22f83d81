fields:
	invoice_no:
		model:
			type: 'text'
		view:
			columns: 4
			label: 'Invoice #'
			readonly: true

	total_expected:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
			min: 0.00
		view:
			columns: 4
			label: 'Confirm Expected Amount'
			class: 'numeral money'
			format: '$0,0.00'

	total_pt_pay:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
			min: 0.00
		view:
			columns: 4
			note: 'Will generate patient charge in Ready-to-Bill Manager'
			label: 'Confirm Patient Pay Amount'
			class: 'numeral money'
			format: '$0,0.00'

	last_close_date:
		model:
			type: 'date'
		view:
			label: 'Last Close Period'
			readonly: true
			offscreen: true

	post_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 4
			label: 'Post Date/Time'	
			validate: [
				{
					name: 'CheckClosedPeriod'
				}
			]

	accepted_by:
		model:
			source: 'user'
		view:
			columns: 2
			label: 'Accepted By'
			readonly: true
			template: '{{user.id}}'

	accepted_datetime:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Accepted Date/Time'
			readonly: true
			template: '{{now}}'

	show_cob:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['generate_cob']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Generate COB?'
			offscreen: true
			readonly: true

	generate_cob:
		model:
			default: 'Yes'
			source: ['Yes']
			if:
				'Yes':
					sections: ['Next Payer']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Generate COB?'
			columns: 4

	insurance_filter:
		model:
			multi: true
			source: 'patient_insurance'
		view:
			label: 'Available Insurances'
			readonly: true
			offscreen: true

	current_insurance_id:
		model:
			required: true
			source: 'patient_insurance'
		view:
			columns: 2
			label: 'Current Payer'
			readonly: true

	next_insurance_id:
		model:
			required: true
			source: 'patient_insurance'
			sourcefilter:
				id:
					'dynamic': '{insurance_filter}'
		view:
			columns: 2
			label: 'Next Payer'

model:
	save: false
	name: ['created_on', 'created_by']
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	sections_group: [
		'Accept Revenue':
			hide_header: true
			indent: false
			fields: ['invoice_no', 'total_expected', 'total_pt_pay', 'last_close_date', 'post_datetime', 
			'accepted_by', 'accepted_datetime', 'show_cob', 'generate_cob']
		'Next Payer':
			tab: 'Next Payer'
			hide_header: true
			indent: false
			fields: ['current_insurance_id', 'insurance_filter', 'next_insurance_id']
	]

view:
	hide_cardmenu: true
	validate: [
		{
			name: "DateOrderValidator"
			fields: [
				"last_close_date",
				"post_datetime"
			]
			error: "Post date cannot be before last close date"
		}
	]
	comment: 'Accept Revenue'
	grid:
		fields: ['created_on', 'invoice_no', 'total_expected', 'last_close_date', 'post_datetime']
		sort: ['-created_on']
	label: 'Accept Revenue'
	open: 'edit'