fields:
	site_id:
		model:
			type: 'int'
			source: 'site'
			prefill: ['parent.site_id']
		view:
			columns: 2
			label: 'Submitting Site'
			readonly: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/site/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'organization_name': ['name'],
					'contact_information':
						'type': 'subform'
						'field': 'contact_information'
						'fields':
							'name': ['pr.name']
							'phone_number': ['pr.phone']
							'fax_number': ['pr.fax']
			]

	organization_name:
		model:
			required: true
			min: 1
			max: 60
		view:
			columns: 2
			label: 'Organization Name'
			reference: 'NM103'
			_meta:
				location: '1000A NM1'
				field: '03'
				path: 'submitter.organizationName'

	contact_information:
		model:
			required: true
			type: 'subform'
			multi: false
			source: 'med_claim_smt_cont'
		view:
			label: 'Contact Information'
			reference: '1000A'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['site_id', 'organization_name']
	indexes:
		many: [
			['organization_name']
			['site_id']
		]

	sections_group: [
		'Submitter':
			hide_header: true
			sections: [
				'Site':
					hide_header: true
					fields: ['site_id', 'organization_name']
				'Contact Information':
					hide_header: true
					indent: false
					fields: ['contact_information']
			]
	]

view:
	dimensions:
		width: '75%'
		height: '45%'
	hide_cardmenu: true
	reference: '1000A'
	comment: 'Submitter'
	hide_header: true
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by', 'organization_name']
		width: [20, 20, 20, 20, 20]
		sort: ['-created_on']
	label: 'Submitter'
	open: 'read'