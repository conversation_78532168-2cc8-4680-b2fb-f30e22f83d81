fields:

	name:
		model:
			max: 128
			required: true
		view:
			label: 'Name'
			columns: 2

	line1:
		model:
			dynamic:
				source: 'list_label_header'
			max: 37
		view:
			label: '1.'
			class: 'label-line groups top'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line6'
			]

	line6:
		model:
			max: 45
			#dynamic:
			#	query: 'workticket_items'
			sourcefilter:
				id:
					'dynamic': '{inv_fltr_id}'
		view:
			label: '6.'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line7'
			]

	line7:
		model:
			max: 45
			#dynamic:
			#	query: 'workticket_items'
			sourcefilter:
				id:
					'dynamic': '{inv_fltr_id}'
		view:
			label: '7.'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line8'
			]

	#LABELS.line7
	line8:
		model:
			max: 45
			#dynamic:
			#	query: 'workticket_items'
			sourcefilter:
				id:
					'dynamic': '{inv_fltr_id}'
		view:
			label: '8.'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line9'
			]

	#LABELS.line9
	line9:
		view:
			label: '9.'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line_directions'
			]

	#LABELS.directns
	line_directions:
		model:
			required: true
		view:
			label: 'Directions'
			class: 'label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line11'
			]

	line11:
		view:
			label: '11.'
			class: 'no-label label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line12'
			]

	line12:
		view:
			label: '12.'
			class: 'no-label label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line13'
			]

	line13:
		view:
			label: '13.'
			class: 'no-label label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'line14'
			]

	line14:
		model:
			max: 45
		view:
			label: '14.'
			class: 'no-label label-line groups middle'

	storage_id:
		model:
			source: 'list_storage'
			sourceid: 'code'
		view:
			label: 'Storage'
			class: 'label-line groups bottom'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'

	active:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			control: 'checkbox'
			label: 'Active?'
			columns: 4
			findfilter: 'Yes'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ['dispense']
	indexes:
		unique: [
			['name', 'active']
		]
	name: ['name', 'line_directions']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name', 'line1', 'line6', 'line7', 'line8', 'line9', 'line_directions', 'line11', 'line12', 'line13', 'line14', 'allow_sync', 'active']

view:
	comment: 'Manage > PO Label Template'
	find:
		basic: ['name', 'active']
	grid:
		fields: ['name', 'active']
		sort: ['name']
	label: 'PO Label Template'
