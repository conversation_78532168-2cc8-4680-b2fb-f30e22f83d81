fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	infections_or_fever:
		model:
			source: ['No', 'Yes']
			required: true
			if:
				'Yes':
					fields: ['infections_or_fever_details']
		view:
			control: 'radio'
			label: 'Do you have a current infection or fever?'

	infections_or_fever_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Details'

	cardiac_history:
		model:
			source: ['No', 'Yes']
			required: true
			if:
				'Yes':
					fields: ['cardiac_history_details']
		view:
			control: 'radio'
			label: 'Do you have any cardiac related history?'

	cardiac_history_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Details'

	patient_counseling:
		model:
			required: true
			multi: true
			source: ['Severe mucocutaneous reactions',
			'Hepatitis B reactivation',
			'Progressive multifocal leukoencephalopathy (PML)',
			'Treat infections before receiving Rituxan',
			'May reduce the response to live vaccines',
			'Vials must be stored refrigerated until use']
		view:
			control: 'checkbox'
			label: 'Patient has been screened for the following:'

	gender:
		model:
			if:
				'Female':
					fields: ['screen_pregnancy']
			prefill: ['patient']
		view:
			label: 'Sex'
			offscreen: true
			readonly: true

	screen_pregnancy:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Screen patient for pregnancy or possible pregnancy, breastfeeding, lactation?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'csr', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections: 
		'Rituxan Patient Assessment':
			fields: ['infections_or_fever', 'infections_or_fever_details',
			'cardiac_history', 'cardiac_history_details']
		'Rituxan Counseling':
			fields: ['patient_counseling', 'screen_pregnancy']
view:
	comment: 'Patient > Careplan > Assessment > Rituxan'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Rituxan'
	open: 'read'
