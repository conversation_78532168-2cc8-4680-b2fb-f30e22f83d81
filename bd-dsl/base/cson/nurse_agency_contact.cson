fields:

	name:
		model:
			max: 64
			required: true
		view:
			columns: 3
			label: 'Name'
			note: 'Letters, numbers, comma, \', -, . only'

	primary_poc:
		model:
			source: ['Yes']
			required: false
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			columns: 3
			label: 'Primary POC'

	phone:
		model:
			max: 21
		view:
			columns: -3
			format: 'us_phone'
			label: 'Phone'

	fax:
		model:
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Fax'

	email:
		model:
			max: 64
			min: 6
			required: false
		view:
			columns: 3
			label: 'Email Address'
			note: 'Must be a valid email address for Portal Access'
			validate: [
					name: 'EmailValidator'
			]

	comment:
		view:
			label: 'Comment'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr','liaison', 'pharm']
		write:      ['admin', 'csr','liaison', 'pharm']
	bundle: ['nursing']
	indexes:
		many: [
			['name']
		]

	name: '{name} P:{phone} F:{fax}'

	sections:
		"Details":
			hide_header: true
			indent: false
			fields: ['name', 'primary_poc', 'phone', 'fax', 'email', 'comment']

view:
	comment: 'Manage > Nursing Provider > Contact'
	grid:
		fields: ['name', 'primary_poc', 'phone', 'fax', 'email', 'comment']
		sort: ['name']
	label: 'Nursing Agency Provider Contact'
