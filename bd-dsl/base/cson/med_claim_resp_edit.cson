fields:
	phase_id:
		view:
			columns: 4
			label: 'Phase ID'
			readonly: true

	reference_id:
		view:
			columns: 4
			label: 'Reference ID'
			readonly: true

	qualifier_code:
		view:
			columns: 4
			label: 'Qualifier Code'
			readonly: true

	claim_core_path:
		view:
			columns: 4
			label: 'Claim Core Path'
			readonly: true

	field_index:
		view:
			columns: 4
			label: 'Field Index'
			readonly: true

	allow_override:
		view:
			columns: 4
			label: 'Allow Override'
			readonly: true

	loop:
		view:
			columns: -4
			label: 'Loop'
			readonly: true

	segment:
		view:
			columns: 4
			label: 'Segment'
			readonly: true

	element:
		view:
			columns: 4
			label: 'Element'
			readonly: true

	edit_name:
		view:
			columns: -4
			label: 'Edit Name'
			readonly: true

	edit_activity:
		view:
			columns: 4
			label: 'Edit Activity'
			readonly: true

	error_description:
		view:
			control: 'area'
			label: 'Error Description'
			readonly: true

	bad_data:
		view:
			control: 'area'
			label: 'Bad Data'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['phase_id', 'reference_id']
	sections:
		'Edit Response':
			hide_header: true
			fields: ['phase_id', 'reference_id', 'qualifier_code',
			'claim_core_path', 'field_index',  'allow_override',
			'loop', 'segment', 'element', 'edit_name', 'edit_activity',
			'error_description', 'bad_data']

view:
	dimensions:
		width: '55%'
		height: '55%'
	hide_cardmenu: true
	comment: 'Edit Response'
	grid:
		fields: ['loop', 'segment', 'element', 'error_description', 'bad_data']
		width: [15, 15, 15, 30, 25]
		sort: ['-created_on']
	label: 'Edit Response'
	open: 'read'