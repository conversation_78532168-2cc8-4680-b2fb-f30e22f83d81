fields:
    patient_id:
        model:
            type: 'int'
            required: false
            source: 'patient'
        view:
            label: 'Patient'
            readonly: true
            offscreen: true

    repriced_line_item_reference_number:
        model:
            max: 50
            min: 1
        view:
            label: 'Itm Ref #'
            reference: 'REF02 REF01=9B'
            offscreen: true
            readonly: true
            _meta:
                location: '2400 REF'
                field: '02'
                code: '9B'
                path: 'claimInformation.serviceLines[{idx1-50}].serviceLineReferenceInformation.repricedLineItemReferenceNumber'

    adjusted_repriced_line_item_reference_number:
        model:
            max: 50
            min: 1
        view:
            offscreen: true
            readonly: true
            label: 'Adj Itm Ref #'
            reference: 'REF02 REF01=9D'
            _meta:
                location: '2400 REF'
                field: '02'
                code: '9D'
                path: 'claimInformation.serviceLines[{idx1-50}].serviceLineReferenceInformation.adjustedRepricedLineItemReferenceNumber'

    referral_number:
        model:
            multi: true
            source: 'med_claim_sl_ref_rn'
            type: 'subform'
        view:
            grid:
                add: 'inline'
                edit: true
                fields: ['value']
                label: ['Referral Number']
                width: [100]
            label: 'Referral Number'
            note: 'Max 5'
            max_count: 5

    prior_authorization:
        model:
            multi: true
            source: 'med_claim_sl_ref_pa'
            type: 'subform'
        view:
            grid:
                add: 'inline'
                edit: true
                fields: ['pa_id', 'prior_authorization_or_referral_number']
                label: ['Prior Authorization', '#']
                width: [50, 50]
            label: 'Prior Auth'
            note: 'Max 5'
            max_count: 5

model:
    access:
        create:     []
        create_all: ['admin', 'csr', 'pharm']
        delete:     ['admin', 'pharm']
        read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
        read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
        request:    []
        update:     []
        update_all: ['admin', 'csr', 'pharm']
        write:      ['admin', 'csr', 'pharm']
    name:['patient_id']
    sections_group: [
        'Prior Authorization':
            indent: false
            fields: ['prior_authorization']
        'Referral Numbers':
            fields: ['referral_number']
    ]

view:
    dimensions:
        width: '85%'
        height: '65%'
    hide_cardmenu: true
    reference: '2400'
    comment: 'Reference'
    grid:
        fields: ['repriced_line_item_reference_number', 'referral_number']
        sort: ['-created_on']
    label: 'Reference'
    open: 'read'