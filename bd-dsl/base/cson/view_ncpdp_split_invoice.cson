fields:

	invoice_no:
		model:
			type: 'text'
		view:
			label: 'Invoice Number'
			note: 'Generated at save'
			readonly: true
			offscreen: true

	claim_no:
		model:
			type: 'int'
		view:
			label: 'Claim #'
			note: 'Claim # against the invoice'
			readonly: true
			offscreen: true

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			columns: 3
			label: 'Patient'
			readonly: true

	insurance_id:
		model:
			source: 'patient_insurance'
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			columns: 3
			control: 'select'
			label: 'Primary Payer'
			readonly: true

	date_of_service:
		model:
			required: true
			type: 'date'
		view:
			label: 'Date of Service'

model:
	save: false
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['invoice_no', 'claim_no', 'patient_id']
	sections_group: [
		'Split Details':
			fields: ['patient_id', 'insurance_id', 'date_of_service']

	]
view:
	comment: 'Split Claim Lines'
	grid:
		fields: ['created_on', 'created_by', 'claim_no']
		sort: ['-created_on']
	label: 'Split Claim Lines'
	open: 'read'