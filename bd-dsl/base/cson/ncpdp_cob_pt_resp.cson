fields:
	pt_resp_amt_qualifier:
		model:
			default: '06'
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '351-NP'
		view:
			columns: 2
			reference: '351-NP'
			note: '351-NP'
			label: 'Patient Resp Qualifier'

	pt_resp_amt:
		model:
			required: true
			type: 'decimal'
			rounding: 0.01
			max: 99999999.99
		view:
			columns: 2
			reference: '352-NQ'
			note: '352-NQ'
			label: 'Patient Resp Amount'
			class: 'numeral money'
			format: '$0,0.00'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['pt_resp_amt_qualifier', 'pt_resp_amt']
	sections:
		'Patient Responsibility':
			hide_header: true
			indent: false
			fields: ['pt_resp_amt_qualifier', 'pt_resp_amt']

view:
	dimensions:
		width: '50%'
		height: '25%'
	hide_cardmenu: true
	comment: 'Patient Responsibility'
	grid:
		fields: ['pt_resp_amt_qualifier', 'pt_resp_amt']
		sort: ['-created_on']
	label: 'Patient Responsibility'
	open: 'read'