fields:

	name:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 2

	form_code:
		model:
			required: true
		view:
			note: 'must match code used in sourcefilter for field'
			label: 'Unique form code'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['lists']
	indexes:
		unique: [
			['name','form_code']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name', 'form_code']

view:
	comment: 'Manage > Steps List'
	find:
		basic: ['name', 'form_code']
	grid:
		fields: ['name' , 'form_code']
		sort: ['name']
	label: 'Steps List'
	open: 'read'
