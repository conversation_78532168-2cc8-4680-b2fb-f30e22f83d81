fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	opt_out:
		model:
			multi: false
			source: ['No', 'Yes']
			default: 'No'
			if:
				'No':
					fields: ['portal_preference']
		view:
			columns: 3
			control: 'radio'
			label: 'Opt-Out?'

	portal_preference:
		model:
			multi: true
			source: ['Web Portal', 'SMS']
			if:
				'Web Portal':
					fields: ['email']
				'SMS':
					fields: ['phone_cell']
		view:
			columns: 3
			control: 'checkbox'
			label: 'Portal Access'

	email:
		model:
			prefill: ['patient']
			max: 64
			min: 6
			required: true
			validate: [
					name: 'EmailUniqueValidator'
					fields: ['email', 'patient_id']
			]
		view:
			columns: 3
			label: 'Email Address'
			note: 'Must be a valid email address for Portal Access'

	firstname:
		model:
			prefill: ['patient']
		view:
			offscreen: true
			readonly: true
			label: 'First Name'

	lastname:
		model:
			prefill: ['patient']
		view:
			offscreen: true
			readonly: true
			label: 'Last Name'

	middlename:
		model:
			prefill: ['patient']
		view:
			offscreen: true
			readonly: true
			label: 'Middle Name'

	phone_cell:
		model:
			required: true
			prefill: ['patient']
			max: 21
		view:
			columns: 3
			format: 'us_phone'
			label: 'Cell Phone'
			validate: [
					name: 'UniquePhoneValidator'
			]

	last_portal_onboarding_email:
		model:
			type: 'datetime'
		view:
			columns: 3
			label: 'Last Portal Onboarding Email Sent'
			readonly: true

	last_portal_signon:
		model:
			type: 'datetime'
		view:
			columns: 3
			label: 'Last Portal Sign-on Date/Time'
			readonly: true

	last_portal_communication_email:
		model:
			type: 'datetime'
		view:
			columns: 3
			label: 'Last Portal Communication Email Sent'
			readonly: true

	last_portal_assessment_email:
		model:
			type: 'datetime'
		view:
			columns: 3
			label: 'Last Portal Self Assessment Email Sent'
			readonly: true

	last_sms_sent:
		model:
			type: 'datetime'
		view:
			columns: 3
			label: 'Last SMS Sent Date/Time'
			readonly: true

	last_sms_content:
		view:
			label: 'Last SMS Content'
			readonly: true

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
		unique: [
			['phone_cell']
			['email']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
	name: ['patient_id', 'email', 'phone_cell']
	sections:
		'Patient Portal':
			fields: ['firstname', 'lastname', 'opt_out', 'portal_preference', 'email', 'phone_cell']
		'Metrics':
			fields: ['last_portal_onboarding_email', 'last_portal_signon', 'last_portal_communication_email', 'last_portal_assessment_email', 'last_sms_sent', 'last_sms_content']
view:
	comment: 'Patient > Patient Portal'
	grid:
		fields: ['created_on', 'created_by', 'portal_preference', 'last_portal_signon', 'last_sms_sent']
		sort: ['-created_on']
	label: 'Patient Portal'
	max_rows: 1
	open: 'read'
