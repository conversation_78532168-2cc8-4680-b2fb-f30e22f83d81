fields:

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site ID'
			readonly: true

	order_id:
		model:
			required: true
			source: 'careplan_order'
			type: 'int'

	patient_id:
		model:
			required: true 
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan'
			readonly: true

	event_id:
		model:
			required: true
			source: 'list_nurse_event'
			sourceid: 'code'
		view:
			label: 'Event'
			readonly: true

	nurse_id:
		model:
			required: true
			source: 'user'
			type: 'int'
		view:
			label: 'Nurse'
			readonly: true

	schedule_id:
		model:
			required: false
			source: 'careplan_schedule_encounter'
			type: 'int'
		view:
			label: 'Schedule'
			readonly: true

	encounter_id:
		model:
			required: false
			source: 'encounter'
			type: 'int'
		view:
			label: 'Encounter'
			readonly: true

model:
	access:
		create:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'payer', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'payer', 'physician']
		request:    []
		update:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['audit']
	indexes:
		many: [
			['site_id'],
			['patient_id'],
			['careplan_id'],
			['event_id'],
			['order_id'],
			['schedule_id'],
			['encounter_id'],
			['nurse_id']
		]
	name: ['nurse_id', 'order_id', 'event_id']
	reportable: true

	sections:
		'Nurse Event Audit Trail':
			fields: ['site_id', 'patient_id','careplan_id', 'order_id', 'event_id', 'nurse_id',
			'schedule_id', 'encounter_id']

view:
	find:
		basic: ['patient_id', 'event_id', 'site_id', 'nurse_id']
	comment: 'Nurse > Event Audit Trail'
	grid:
		fields: ['site_id', 'patient_id', 'nurse_id', 'schedule_id', 'encounter_id', 'event_id']
		sort: ['-created_on']
	label: 'Nurse Event Audit Trail'
	open: 'read'