fields:
	name:
		model:
			required: true
		view:
			columns: 2
			label: 'Name'
			findunique: true
	code:
		model:
			required: true
		view:
			columns: 2
			label: 'Code'
			findunique: true

	field:
		model:
			required: false
		view:
			columns: 2
			label: 'Field'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['billing']
	sync_mode: 'full'

	indexes:
		unique: [
			['code']
		]
	name: '{code} - {field}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'field']

view:
	comment: 'Manage > NCPDP Reject Code To Fields Map'
	find:
		basic: ['name', 'code', 'field']
	grid:
		fields: ['code', 'name', 'field']
		sort: ['name']
	label: 'NCPDP Reject Code To Field Map'
	open: 'read'
