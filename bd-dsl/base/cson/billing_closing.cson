fields:

	close_no:
		model:
			type: 'text'
		view:
			label: 'Closing #'
			readonly: true
			offscreen: true

	locked_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Records Locked Date/Time'
			readonly: true
			offscreen: true

	start_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 2
			label: 'Closing Date Start'
			readonly: true
			validate: [
				{
					name: 'CloseOfMonthFillUp'
				}
			]

	end_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 2
			validate: [
				{
					name: 'CloseOfMonthFillUp'
				}
			]
			label: 'Closing End Start'
			template: '{{now}}'

	tot_ar:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total A/R'
			readonly: true 

	tot_ap:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total A/P'
			readonly: true

	tot_cost:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			class: 'numeral'
			format:'$0,0.00'
			label: 'Total Cost'
			readonly: true

	tot_credits:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			class: 'numeral'
			format:'$0,0.00'
			label: 'Total Credits'
			readonly: true

	tot_debits:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			class: 'numeral'
			format:'$0,0.00'
			label: 'Total Debits'
			readonly: true

	last_start:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Last Closing Period Start'
			readonly: true

	last_end:
		model:
			type: 'date'
		view:
			columns: 3
			label: 'Last Closing Period End'
			readonly: true

	last_tot_ar:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: -3
			class: 'numeral'
			format:'$0,0.00'
			label: 'Total A/R'
			readonly: true

	last_tot_ap:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total A/P'
			readonly: true

    # Add revenue in period 
	# Add revenue / day
	# Add collections in period
	# Add collections / day
	# Total Shipments in period
	# Avg Shipments / day
	# Total patients serviced in period
	# Total new starts in period 
	# Add some charts and graph
	last_tot_cost:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			class: 'numeral money'
			label: 'Last Total Cost'
			format: '$0,0.00'
			readonly: true

	last_tot_credits:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Credits'
			readonly: true

	last_tot_debits:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 3
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Total Debits'
			readonly: true

	associated_records:
		model:
			required: false
			type: 'json'
		view:
			label: 'Associated Records'
			readonly: true
			offscreen: true

	locked_period:
		model:
			required: false
			source: ['Yes','No']
			default: 'Yes'
		view:
			label: 'Locked Period'
			readonly: true
			offscreen: true

	needs_refreshed:
		model:
			required: false
			source: ['Yes','No']
			default: 'Yes'
		view:
			label: 'Needs Refreshed'
			readonly: true
			offscreen: true

	subform_unlock:
		model:
			required: false
			multi: true
			type: 'subform'
			source: 'billing_closing_unlock'
		view:
			grid:
				add: 'none'
				edit: false
				fields: ['created_on', 'created_by', 'unlock_reason_id', 'note']
				label: ['Date', 'User', 'Reason', 'Note']
				width: [20, 20, 25, 35]
			label: 'Unlock History'
			readonly: true

	subform_lock:
		model:
			required: false
			multi: true
			type: 'subform'
			source: 'billing_closing_lock'
		view:
			grid:
				add: 'none'
				edit: false
				fields: ['created_on', 'created_by', 'note']
				label: ['Date', 'User', 'Note']
				width: [20, 20, 60]
			label: 'Lock History'
			readonly: true

	void:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['voided_datetime', 'void_reason_id']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Void Claim?'
			findfilter: '!Yes'
			class: 'checkbox-only'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						void: 'Yes'
					dest: 'voided_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						void: 'Yes'
					dest: 'voided_by'
				}
			]

	voided_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 2
			label: 'Voided Date'
			readonly: true

	voided_by:
		model:
			source: 'user'
		view:
			label: 'Voided By'
			readonly: true

	void_reason_id:
		model:
			required: true
			source: 'list_void_reason_billing'
			sourcefilter:
				code:
					'static': '!Automatic'
			sourceid: 'code'
		view:
			label: 'Void Reason'

model:
	access:
		create:     ['admin', 'cm']
		create_all: ['admin', 'cm']
		delete:     []
		read:       ['admin', 'cm']
		read_all:   ['admin', 'cm']
		request:    []
		update:     []
		update_all: []
		write:      ['admin', 'cm']
	reportable: true
	name: ['start_date', 'end_date']
	sections_group: [
		'Period Closing':
			sections: [
				'Details':
					fields: ['locked_datetime', 'locked_period', 'needs_refreshed', 'associated_records', 'start_date','end_date']
				'Last Closing Details':
					fields: ['last_start','last_end','last_tot_ar',
					'last_tot_ap', 'last_tot_cost', 'last_tot_credits',
					'last_tot_debits']
				'This Closing Period':
					fields: ['tot_ar','tot_ap','tot_cost', 'tot_credits','tot_debits']
			]
			'Void':
				modal: true
				fields: ['void', 'voided_datetime', 'voided_by', 'void_reason_id']
			'Unlock History':
				fields: ['subform_unlock']
			'Lock History':
				fields: ['subform_lock']
		]
view:
	validate: [
		{
			name: "DateOrderValidator"
			fields: [
				"start_date",
				"end_date"
			]
			error: "Closing end date cannot be before closing start date"
		}
	]
	hide_cardmenu: true
	comment: 'Period Closing'
	find:
		basic: ['start_date','end_date','locked_period', 'void']
	grid:
		fields: ['created_on','created_by','start_date','end_date','locked_period','tot_ar','tot_ap']
		sort: ['-start_date']
	label: 'Period Closing'
