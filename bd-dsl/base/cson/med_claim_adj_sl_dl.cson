fields:
	adjustment_reason_code:
		model:
			required: true
			min: 1
			max: 5
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CARC'
		view:
			columns: 3
			label: 'Reason Code'
			reference: ['CAS02', 'CAS05', 'CAS08', 'CAS11', 'CAS14', 'CAS17']
			_meta:
				location: '2430 CAS'
				field: ['02', '05', '08', '11', '14', '17']
				type: 'index'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].claimAdjustmentInformation[{idx1-5}].adjustmentDetails[{idx1-6}].adjustmentReasonCode'

	adjustment_amount:
		model:
			max: 9999999999.99
			min: 0.00
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 3
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Amount'
			reference: ['CAS03', 'CAS06', 'CAS09', 'CAS12', 'CAS15', 'CAS18']
			_meta:
				location: '2430 CAS'
				field: ['03', '06', '09', '12', '15', '18']
				type: 'index'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].claimAdjustmentInformation[{idx1-5}].adjustmentDetails[{idx1-6}].adjustmentAmount'

	adjustment_quantity:
		model:
			max: 9999999999999.99
			min: 0.00
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 3
			label: 'Quantity'
			reference: ['CAS04', 'CAS07', 'CAS10', 'CAS13', 'CAS16', 'CAS19']
			_meta:
				location: '2430 CAS'
				field: ['04', '07', '10', '13', '16', '19']
				type: 'index'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].claimAdjustmentInformation[{idx1-5}].adjustmentDetails[{idx1-6}].adjustmentQuantity'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['adjustment_reason_code','adjustment_amount','adjustment_quantity']
	sections:
		'Adjustment Details':
			hide_header: true
			fields: ['adjustment_reason_code', 'adjustment_amount', 'adjustment_quantity']

view:
	dimensions:
		width: '85%'
		height: '45%'
	hide_cardmenu: true
	reference: '2320'
	comment: 'Adjustment Details'
	grid:
		fields: ['adjustment_reason_code', 'adjustment_amount', 'adjustment_quantity']
		sort: ['-created_on']
	label: 'Service Line Adjustment Details'
	open: 'read'