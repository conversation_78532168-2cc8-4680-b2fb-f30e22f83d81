fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	diagnosis_b:
		model:
			source: ['Mild (baseline IX >5%)', 'Moderate (baseline IX between 1-5%)','<PERSON>vere (baseline IX <1%)']
		view:
			control: 'radio'
			label: 'Hemophilia B (Factor IX Deficiency)'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
	sections:
		'Hemophilia B Pre-Assessment':
			area: 'preassessment'
			fields: ['diagnosis_b']

view:
	comment: 'Patient > Careplan > Assessment > Hemophilia B'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Hemophilia B'
	open: 'read'
