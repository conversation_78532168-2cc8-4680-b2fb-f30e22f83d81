fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: '<PERSON>rateUUID'
			]

	date:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Assessment Date'
			template: '{{now}}'

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			required: false
			source: 'careplan_order'
		view:
			label: 'Order'
			readonly: true
			offscreen: true

	brand_name_id:
		model:
			required: true
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Brand Name'
			transform: [{
				name: 'EmbedRefresh'
				fields: ['embed_copay_program', 'embed_pap_program', 'embed_alt_payer']
			}]

	embed_insurances:
		model:
			required: false
			multi: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			embed:
				form: 'patient_insurance'
				selectable: false
			grid:
				add: 'none'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['payer_id', 'type_id', 'payer_level']
				label: ['Payer', 'Type', 'Level']
				width: [70, 20, 10]
			label: 'Patient Insurances'

	embed_copay_program:
		model:
			required: false
			multi: false
			sourcefilter:
				bname_id:
					'dynamic': '{brand_name_id}'
				active:
					'static': 'Yes'
		view:
			embed:
				form: 'list_copay_program'
				selectable: true
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['name', 'maximum_amount', 'phone', 'url']
				label: ['Name', 'Max $', 'Phone #', 'URL']
				width: [25, 15, 25, 35]
			label: 'Select Copay Program'

	embed_pap_program:
		model:
			required: false
			multi: false
			sourcefilter:
				bname_id:
					'dynamic': '{brand_name_id}'
				active:
					'static': 'Yes'
		view:
			embed:
				form: 'list_pap_program'
				selectable: true
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['name', 'threshold', 'threshold_size', 'funding_threshold', 'funding_threshold_type']
				label: ['Name', 'Thres $', 'Thres Type', 'Fund Thres $', 'Fund Thres Type']
				width: [30, 15, 15, 20, 20]
			label: 'Select PAP Program'

	embed_alt_payer:
		model:
			required: false
			multi: false
			sourcefilter:
				bname_id:
					'dynamic': '{brand_name_id}'
				active:
					'static': 'Yes'
		view:
			embed:
				form: 'list_alt_payer'
				selectable: true
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['name', 'phone', 'url', 'notes']
				label: ['Name', 'Phone #', 'URL', 'Notes']
				width: [25, 15, 25, 35]
			label: 'Select Alt. Payer Program'

	asistance_notes:
		model:
			required: false
		view:
			columns: 2
			control: 'area'
			label: 'Assistance Notes'
			readonly: true

	type:
		model:
			required: true
			multi: false
			source:
				copay: 'Manufacturer Copay Program'
				pap: 'Manufacturer PAP Program'
				hardship: 'Hardship Waiver'
				grant: 'Foundation/Grant Enrollment'
				mental: 'Mental Health'
				physical: 'Physical Therapy or Exercise Equipment/Gym Memberships'
				scholarship: 'Scholarship'
				not_eligible: 'Not Eligible'
			if:
				'copay':
					sections: ['Manufacturer Copay']
				'pap':
					sections: ['Manufacturer PAP']
				'grant':
					sections: ['Other Assistance']
				'physical':
					sections: ['Other Assistance']
				'mental':
					sections: ['Other Assistance']
				'scholarship':
					sections: ['Other Assistance']
				'hardship':
					sections: ['Hardship Waiver']
		view:
			columns: 2
			class: 'checkbox checkbox-2'
			control: 'checkbox'
			label: 'Program Type'

	assistance_status:
		model:
			source: ['Pending', 'Applied', 'Denied', 'Appealing', 'Withdrawn']
			default: 'Pending'
			required: true
		view:
			columns: 2
			control: 'radio'
			label: 'Assistance Program Status'

	hardship_waiver_notes:
		model:
			required: true
		view:
			columns: 2
			control: 'area'
			label: 'Hardship Waiver Notes'

	notes:
		model:
			required: false
		view:
			control: 'area'
			label: 'Notes'

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	comments:
		model:
			required: false
		view:
			columns: 2
			control: 'area'
			label: 'Comments'
			readonly: true

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'patient_assistance'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'Other Form'
					direct_attachment: 'Yes'
					form_code: '{code}'
					form_name: 'patient_assistance'
					source: 'Scanned Document'
					type_id: 'Patient Assistance'
					patient_id: '{patient_id}'
					form_filter: 'patient_assistance'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
			['brand_name_id']
		]
		unique: [
			['code']
		]
	reportable: true
	name: ['type', 'assistance_status']
	sections_group: [
		'Patient Insurances':
			hide_header: true
			indent: false
			fields: ['embed_insurances']
			tab: 'Assistance'
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'date','brand_name_id',
			'asistance_notes', 'type', 'assistance_status']
			tab: 'Assistance'
		'Manufacturer Copay':
			indent: false
			fields: ['embed_copay_program']
			tab: 'Assistance'
		'Manufacturer PAP':
			indent: false
			fields: ['embed_pap_program']
			tab: 'Assistance'
		'Other Assistance':
			indent: false
			fields: ['embed_alt_payer']
			tab: 'Assistance'
		'Hardship Waiver':
			indent: false
			fields: ['hardship_waiver_notes']
			tab: 'Assistance'
		'Notes':
			indent: false
			fields: ['notes']
			tab: 'Assistance'
		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'
	]
view:
	hide_cardmenu: true
	comment: 'Patient > Patient Assistance'
	grid:
		fields: ['created_on', 'created_by', 'type', 'notes']
		sort: ['-created_on']
	find:
		basic: ['type', 'assistance_status']
	label: 'Patient Assistance'
	open: 'read'