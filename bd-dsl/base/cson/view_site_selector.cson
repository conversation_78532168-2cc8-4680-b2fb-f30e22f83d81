fields:
	site_id:
		model:
			required: true
			multi: true
			source: 'site'
		view:
			columns: 1
			label: 'Access Site'
model:
	save: false
	access:
		create:     []
		create_all: []
		delete:     []
		read:       []
		read_all:   []
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      []
	name: ['site_id']
	sections_group: [
		'Site Selector':
			hide_header: true
			indent: false
			fields: ['site_id']
	]

view:
	hide_cardmenu: true
	comment: 'Site Selector'
	grid:
		fields: ['site_id']
		sort: ['-id']
	label: 'Site Selector'
	open: 'edit'