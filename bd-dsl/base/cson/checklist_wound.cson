fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	# Wounds
	wounds_id:
		model:
			multi: true
			source: 'checklist_wound_details'
			type: 'subform'
		view:
			label: 'Patient Wounds'

	# Wounds Progress
	comments:
		model:
			max: 8192
		view:
			control: 'area'
			note: 'tissue color, granulation, response to care, etc.'
			label: "Comments"

	# Weekly Update
	notes_id:
		model:
			multi: true
			source: 'checklist_wound_note'
			type: 'subform'
		view:
			label: 'Wounds Progress Notes'

	# Wound Management
	patient_indep:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['patient_indep_reason', 'patient_indep_reason_details']
		view:
			control: 'radio'
			label: 'Patient/Caregiver independent with wound care management?'

	patient_indep_reason:
		model:
			source: ['Patient Unwilling/Unable', 'Caregiver Unwilling/Unable']
		view:
			control: 'radio'
			label: 'Nurse performing wound care because:'

	patient_indep_reason_details:
		view:
			label: 'Nurse performing wound care details'

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
		checklist_wound:
			link:
				'careplan_id': 'careplan_id'
			max: 'created_on'
	name: ['careplan_id', 'order_id']
	sections:
		'Patient Wounds':
			fields: ['wounds_id']
		'Comments':
			fields: ['comments']
		'Weekly Wound Progress Note':
			fields: ['notes_id']
		'Wound Management':
			fields: ['patient_indep', 'patient_indep_reason', 'patient_indep_reason_details']

	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Patient Wound Progress Note"
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Checklist > Wound'
	grid:
		fields: ['created_on', 'created_by', 'patient_indep']
		sort: ['created_on']
	label: 'Patient Wound Progress Note'
	max_rows: 1
	open: 'read'
