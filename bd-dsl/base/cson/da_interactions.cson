fields:
	# Links

	ndc:
		model:
			required: true
			search: 'A'
		view:
			columns: 3
			label: 'Name'
			findunique: true
	
	alrgn:
		model:
			required: true
		view:
			columns: 3
			label: 'Allergen'

	type:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'Type'

	description:
		model:
			type: 'text'
		view:
			control: 'area'
			label: 'Description'

model:
	access:
		create:     ['patient']
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['patient']
		review:     ['admin', 'pharm']
		update:     ['cm', 'cma', 'liaison', 'nurse', 'patient']
		update_all: ['admin', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		# unique: [
		# 		['ndc']
		# ]
	sections_group: [
		'Drug Allergy Interactions':
			fields: ['ndc', 'alrgn', 'type', 'description']
	]
	name: ['ndc']

view:
	comment: 'Patient > Drug Allergy Interactions'
	find:
		basic: ['ndc']
	grid:
		fields: ['created_on']
		sort: ['created_on']
	icon: 'medication'
	label: 'Drug Allergy Interactions'
	open:'read'