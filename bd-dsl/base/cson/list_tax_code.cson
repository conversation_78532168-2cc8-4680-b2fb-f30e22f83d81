fields:

	code:
		model:
			max: 64
			required: true
		view:
			columns: 2
			label: 'Code'
			findunique: true

	name:
		model:
			required: true
			max: 128
		view:
			columns: 2
			label: 'Name'
			findunique: true

	reporting_city:
		model:
			max: 128
			required: true
		view:
			columns: 2
			label: 'Reporting City'

	city_tax_rate:
		model:
			required: true
			type: 'decimal'
			rounding: 0.01
			default: 0.00
			max: 100
			min: 0
		view:
			columns: 2
			class: 'numeral'
			format:'$0,0.00'
			label: 'City Tax Rate %'

	reporting_county:
		model:
			max: 128
			required: true
		view:
			columns: 2
			label: 'Reporting County'

	county_tax_rate:
		model:
			required: true
			type: 'decimal'
			rounding: 0.01
			default: 0.00
			max: 100
			min: 0
		view:
			columns: 2
			class: 'numeral'
			format:'$0,0.00'
			label: 'County Tax Rate %'

	reporting_state:
		model:
			source: 'list_us_state'
			sourceid: 'code'
			required: true
		view:
			columns: 'addr_state'
			label: 'Reporting State'

	state_tax_rate:
		model:
			required: true
			type: 'decimal'
			rounding: 0.01
			default: 0.00
			max: 100
			min: 0
		view:
			columns: 3
			class: 'numeral'
			format:'$0,0.00'
			label: 'State Tax Rate %'

	origin_based_tax:
		model:
			source: ['Yes']
		view:
			columns: 3
			control: 'checkbox'
			class: 'checkbox-only'
			note: "Supported by few state like TX, tax is based on the pharmacy site location rather than the patient's delivery address"
			label: 'Tax is based on the origin vs the destination'

	state_flat_tax:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['flat_tax']
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'State flat tax per prescription?'

	flat_tax:
		model:
			type: 'decimal'
			rounding: 0.01
			required: true
			min: 0
		view:
			columns: 2
			class: 'numeral'
			format:'$0,0.00'
			label: 'Flat Tax'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['setup']
	indexes:
		unique: [
			['code']
		]
	name: '{code} - {name}'
	sections:
		'Tax Rate':
			fields: ['code', 'name', 'reporting_city', 'city_tax_rate',
			'reporting_county', 'county_tax_rate', 'reporting_state',
			'state_tax_rate', 'origin_based_tax']
		'Flat Tax':
			note: 'Some states use a flat tax rate per perscription. (i.e. $1.99 per perscription)'
			fields: ['state_flat_tax', 'flat_tax']
view:
	comment: 'Manage > Tax Code'
	find:
		basic: ['code','name']
	grid:
		fields: ['name', 'reporting_city', 'city_tax_rate', 'county_tax_rate', 'reporting_state','state_tax_rate']
		sort: ['code']
	label: 'Tax Code'
	open: 'read'
