fields:

	name:
		model:
			required: true
		view:
			label: 'Name'

	code:
		model:
			required: true
		view:
			label: 'Code'

	subject:
		model:
			required: true
		view:
			label: 'Subject'

	template_html:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			readonly: true
			label: 'HTML Design'

	template_data:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			readonly: true
			label: 'Design JSON Definations'
	

	module:
		model:
			type: 'int'
			source: 'module'
			required: true
		view:
			label: 'Module'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'


model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['templates']
	indexes: 
		unique: ['code', 'archived', 'module']
	
	name: '{name} - {code}'
	sync_mode: 'mixed'
	sections:
		'Main':
			fields: ['name', 'code','subject', 'module', 'allow_sync', 'active']

view:
	comment: 'Email Template'
	grid:
		fields: ['name', 'subject', 'code']
	label: 'Email Template'
	open: 'read'
