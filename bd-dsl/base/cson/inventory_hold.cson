fields:

	patient_id:
		model:
			required: false
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true

	delivery_tick_id:
		model:
			required: false
			source: 'careplan_delivery_tick'
		view:
			label: 'Delivery Ticket'
			readonly: true

	site_id:
		model:
			required: true
			search: 'A'
			source: 'site'
		view:
			label: 'Site'
			readonly: true

	inventory_id:
		model:
			required: true
			search: 'A'
			source: 'inventory'
		view:
			label: 'Inventory Item'
			readonly: true

	quantity:
		model:
			min: 1
			required: true
			search: 'B'
			rounding: 1
			type: 'decimal'
		view:
			label: 'Quantity'

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	bundle: ['inventory']
	name: ['delivery_tick_id', 'inventory_id', 'quantity']
	sections:
		'Inventory Serial':
			fields: ['site_id', 'inventory_id', 'quantity']

view:
	hide_cardmenu: true
	comment: 'Inventory Hold'
	find:
		basic: ['patient_id', 'delivery_tick_id', 'site_id', 'inventory_id']
	grid:
		fields: ['site_id', 'inventory_id', 'quantity']
		sort: ['-id']
	label: 'Inventory Hold'
	open: 'edit'