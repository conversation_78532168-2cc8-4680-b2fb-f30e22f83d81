fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Questionnaire Participation

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_pas2.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	# Questionnaire
	pas2_stand_up:
		model:
			required: true
			source: ['Without Any Difficulty', 'With Some Difficulty', 'With Much Difficulty', 'Unable To Do']
		view:
			control: 'radio'
			label: "Stand up from a straight chair?"

	pas2_walk_outside:
		model:
			required: true
			source: ['Without Any Difficulty', 'With Some Difficulty', 'With Much Difficulty', 'Unable To Do']
		view:
			control: 'radio'
			label: "Walk outdoors on flat ground?"

	pas2_get_off_toilet:
		model:
			required: true
			source: ['Without Any Difficulty', 'With Some Difficulty', 'With Much Difficulty', 'Unable To Do']
		view:
			control: 'radio'
			label: "Get on/off toilet?"

	pas2_reach_up:
		model:
			required: true
			source: ['Without Any Difficulty', 'With Some Difficulty', 'With Much Difficulty', 'Unable To Do']
		view:
			control: 'radio'
			label: "Reach and get down a 5 pound object (such as a bag of sugar) from just above your head?"

	pas2_open_car_door:
		model:
			required: true
			source: ['Without Any Difficulty', 'With Some Difficulty', 'With Much Difficulty', 'Unable To Do']
		view:
			control: 'radio'
			label: "Open car doors?"

	pas2_do_outside_work:
		model:
			required: true
			source: ['Without Any Difficulty', 'With Some Difficulty', 'With Much Difficulty', 'Unable To Do']
		view:
			control: 'radio'
			label: "Do outside work (such as yard work)?"

	pas2_wait_in_line:
		model:
			required: true
			source: ['Without Any Difficulty', 'With Some Difficulty', 'With Much Difficulty', 'Unable To Do']
		view:
			control: 'radio'
			label: "Wait in a line for 15 minutes?"

	pas2_lift_heavy_objects:
		model:
			required: true
			source: ['Without Any Difficulty', 'With Some Difficulty', 'With Much Difficulty', 'Unable To Do']
		view:
			control: 'radio'
			label: "Lift heavy objects?"

	pas2_move_heavy_objects:
		model:
			required: true
			source: ['Without Any Difficulty', 'With Some Difficulty', 'With Much Difficulty', 'Unable To Do']
		view:
			control: 'radio'
			label: "Move heavy objects?"

	pas2_go_up_stairs:
		model:
			required: true
			source: ['Without Any Difficulty', 'With Some Difficulty', 'With Much Difficulty', 'Unable To Do']
		view:
			control: 'radio'
			label: "Go up two or more flights of stairs?"

	pas2_pain:
		model:
			required: true
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			note: '0 = No Pain, 10 = Severe Pain'
			label: "Pain"

	pas2_wellness:
		model:
			required: true
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			note: '0 = Very Well, 10 = Very Poor'
			label: "Wellness"

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_pas2:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Patient Activity Scale-II (PAS-II)':
			fields: ['last_assessment_date', 'assessment_date']
		'Questionnaire':
			note: 'We are interested in learning how your illness affects your ability to function in daily life. Select an option which best describes your usual abilities OVER THE PAST WEEK: \n\n Are you able to'
			fields: ['pas2_stand_up', 'pas2_walk_outside', 'pas2_get_off_toilet', 'pas2_reach_up', 'pas2_open_car_door',
			'pas2_do_outside_work', 'pas2_wait_in_line', 'pas2_lift_heavy_objects', 'pas2_move_heavy_objects',
			'pas2_go_up_stairs']
			prefill: 'clinical_pas2'
		'Questionnaire Pain':
			note: 'How much pain have you had because of your illness in the past week? Select an option that best describes the severity of your pain on a scale of 0-10'
			fields: ['pas2_pain']
			prefill: 'clinical_pas2'
		'Questionnaire Wellness':
			note: 'Considering ALL THE WAYS THAT YOUR ILLNESS AFFECTS YOU, RATE HOW YOU ARE DOING on the following scale. Select an option below that best describes how you are doing on a scale of 0-10.'
			fields: ['pas2_wellness']
			prefill: 'clinical_pas2'
view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Patient Activity Scale-II'
	label: 'Patient Activity Scale-II (PAS-II)'
