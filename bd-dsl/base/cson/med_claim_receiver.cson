fields:

	payer_id:
		model:
			required: true
			type: 'int'
			source: 'payer'
		view:
			columns: 2
			label: 'Payer'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/payer/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'organization_name': ['organization']
			]

	organization_name:
		model:
			required: true
			min: 1
			max: 60
		view:
			columns: 2
			label: 'Organization Name'
			reference: 'NM103'
			_meta:
				location: '1000B NM1'
				field: '03'
				path: 'receiver.organizationName'

	address:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_address_pyr'
		view:
			label: 'Address'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['payer_id', 'organization_name']
	indexes:
		many: [
			['payer_id']
			['organization_name']
		]
	sections_group: [
			'Payer Details':
				hide_header: true
				sections: [
					'Main':
						hide_header: true
						indent: false
						fields: ['payer_id', 'organization_name']
					'Address':
						hide_header: true
						indent: false
						fields: ['address']
					
				]
		]

view:
	dimensions:
		width: '65%'
		height: '45%'
	hide_cardmenu: true
	reference: '1000B'
	comment: 'Payer'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by', 'organization_name']
		width: [20, 20, 20, 20, 20]
		sort: ['-created_on']
	label: 'Receiver'
	open: 'read'