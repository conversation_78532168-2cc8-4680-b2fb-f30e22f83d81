fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Weekly Hours Worked
	hrs_worked_7:
		model:
			max: 97
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'If more than 97, enter 97.'
			label: 'About how many hours altogether did you work in the past 7 days?'

	exp_worked_7:
		model:
			max: 97
			rounding: 0.01
			type: 'decimal'
		view:
			note: 'If it varies, estimate the average. If more than 97, enter 97.'
			label: 'How many hours does your employer expect you to work in a typical 7-day week?'

	# Monthly Work History
	missed_work_sick:
		model:
			max: 28
			rounding: 1
			type: 'decimal'
		view:
			note: 'Please include only days missed for your own health.'
			label: 'Miss an entire work day because of problems with your physical or mental health?'

	missed_work_other:
		model:
			max: 28
			rounding: 1
			type: 'decimal'
		view:
			note: 'including vacation'
			label: 'Miss an entire work day for any other reason?'

	missed_work_sick_part:
		model:
			max: 28
			rounding: 1
			type: 'decimal'
		view:
			note: 'Please include only days missed for your own health.'
			label: 'Miss part of a work day because of problems with your physical or mental health?'

	missed_work_other_part:
		model:
			max: 28
			rounding: 1
			type: 'decimal'
		view:
			note: 'including vacation'
			label: 'Miss part of a work day for any other reason?'

	worked_extra:
		model:
			max: 28
			rounding: 1
			type: 'decimal'
		view:
			label: 'Come in early, go home late, or work on your day off?'

	hrs_worked_28:
		model:
			max: 97
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'About how many hours altogether did you work in the past 4 weeks (28 days)?'

	# Performance
	job_perf_typ:
		model:
			max: 2
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'On a scale from 0 to 10 where 0 is the worst job performance anyone could have at your job and 10 is the performance of a top worker, how would you rate the usual performance of most workers in a job similar to yours?'

	job_perf_me_long:
		model:
			max: 2
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Using the same 0-to-10 scale, how would you rate your usual job performance over the past year or two?'

	job_perf_me_short:
		model:
			max: 2
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: ' Using the same 0-to-10 scale, how would you rate your overall job performance on the days you worked during the past 4 weeks (28 days)?'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_hpq:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Weekly Work History':
			note: 'Should be filled out the first visit and then every 6 months'
			fields: ['hrs_worked_7', 'exp_worked_7']
			prefill: 'clinical_hpq'
		'Monthly Work History':
			note: 'Now please think of your work experiences over the past 4 weeks (28 days). In the spaces provided below, write the number of days you spent in each of the following work situations. In the past 4 weeks (28 days), how many days did you...'
			fields: ['missed_work_sick', 'missed_work_other', 'missed_work_sick_part', 'missed_work_other_part', 'worked_extra', 'hrs_worked_28']
			prefill: 'clinical_hpq'
		'Performance Questionnaire':
			fields: ['job_perf_typ', 'job_perf_me_long', 'job_perf_me_short']
			prefill: 'clinical_hpq'
view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > Health and Work Performance Questionnaire (HPQ)'
	label: 'Health and Work Performance Questionnaire (HPQ)'
