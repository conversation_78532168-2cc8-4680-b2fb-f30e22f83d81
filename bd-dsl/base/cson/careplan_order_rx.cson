fields:

	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	site_id:
		model:
			source: 'site'
		view:
			columns: 4
			label: 'Site'
			offscreen: true
			readonly: true

	external_id:
		model:
			type: 'int'
		view:
			label: 'External ID'
			readonly: true
			offscreen: true

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_no:
		view:
			label: 'Order #'
			readonly: true
			offscreen: true

	therapy_id:
		model:
			max: 64
			multi: false
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Therapy'
			offscreen: true
			readonly: true

	last_dispense_date:
		model:
			type: 'date'
			required: false
		view:
			label: 'Dispense Date'
			readonly: true
			offscreen: true

	status_id:
		model:
			required: false
			default: '1'
			source: 'list_order_status'
			sourceid: 'code'
		view:
			label: 'Order Status'
			readonly: true
			offscreen: true

	last_through_date:
		model:
			type: 'date'
			required: false
		view:
			label: 'Through Date'
			readonly: true
			offscreen: true

	last_dispense_delivery_tick_id:
		model:
			type: 'int'
		view:
			label: 'Last Delivery Ticket ID'
			readonly: true
			offscreen: true

	discontinued:
		model:
			source: ['Yes']
		view:
			label: 'Discontinued'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			required: true
			source: 'inventory'
		view:
			columns: 2
			label: 'Drug'
			readonly: true
			form_link_enabled: true
			class: 'select_prefill fdb-field'
			transform: [
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'compound_type': ['compound_type'],
					'comp_dsg_fm_code': ['comp_dsg_fm_code'],
					'auto_dc': ['auto_dc'],
					'medid': ['medid'],
					'gcn_seqno': ['gcn_seqno'],
					'comp_disp_unit': ['comp_disp_unit']
			]

	medid:
		model:
			required: false
		view:
			readonly: true
			offscreen: true
			class: 'fdb-field'
			label: 'FDB MEDID'

	gcn_seqno:
		model:
			required: false
		view:
			readonly: true
			offscreen: true
			label: 'FDB GCN Sequence Number'
			class: 'fdb-field'

	select_rx_template_id:
		model:
			required: true
			source: 'list_rx_template'
			sourceid: 'code'
			sourcefilter:
				active: 
					'static': 'Yes'
				template_type:
					source: ['PO', 'IV', 'Injection', 'Factor', 'Compound', 'TPN']
		view:
			label: 'RX Format'

	template_type:
		model:
			required: true
			source: ['PO', 'IV', 'Injection', 'Factor', 'Compound', 'TPN']
			if:
				'IV':
					fields: ['rx_label_template_id', 'dose_type_id', 'rxform_line4', 'next_delivery_date', 'rxform_line5', 'wt_item_embed']
					sections: ['Compounding Instructions', 'Work Ticket']
					prefill:
						require_wt_items: 'Yes'
						require_compound_items: ''
				'Compound':
					fields: ['rx_label_template_id', 'max_doses_per_fill', 'doses_to_prep', 'doses_per_container', 'containers_to_prep', 'container_id', 'compound_type', 'comp_dsg_fm_code', 'comp_disp_unit',
					'vehicle_id', 'volume_per_dose', 'overfill', 'next_delivery_date', 'rxform_line4', 'rxform_line5', 'wt_cp_item_embed']
					sections: ['Compounding Instructions', 'Medication Review', 'Compounding Record']
					prefill:
						require_wt_items: ''
						require_compound_items: 'Yes'
				'TPN':
					fields: ['rx_label_template_id', 'max_doses_per_fill', 'doses_to_prep', 'doses_per_container', 'containers_to_prep', 'container_id', 'compound_type', 'comp_dsg_fm_code',
					'vehicle_id', 'volume_per_dose', 'overfill', 'next_delivery_date', 'rxform_line4', 'rxform_line5', 'wt_cp_item_embed']
					sections: ['Compounding Instructions', 'Medication Review', 'Compounding Record']
					prefill:
						compound_type: '05'
						comp_dsg_fm_code: '12'
						require_wt_items: ''
						require_compound_items: 'Yes'
				'PO':
					fields: ['po_template_id', 'dispense_quantity']
					prefill:
						require_wt_items: ''
						require_compound_items: ''
				'Injection':
					fields: ['po_template_id', 'dispense_quantity']
					prefill:
						require_wt_items: ''
						require_compound_items: ''
				'Factor':
					fields: ['po_template_id', 'dispense_quantity']
					prefill:
						require_wt_items: ''
						require_compound_items: ''
		view:
			label: 'Template Type'
			control: 'radio'
			offscreen: true
			readonly: true

	status:
		model:
			default: 'Setup'
			source: ['Setup', 'Complete', 'Verified', 'Denied']
			if:
				'Verified':
					readonly:
						fields: ['completed_by', 'completed_datetime', 'verified_by', 'verified_datetime', 'rx_verified', 'rx_denied', 'rx_complete',
						'inventory_id', 'dispense_quantity', 'day_supply']
				'Complete':
					readonly:
						fields: ['completed_by', 'completed_datetime']
					fields: ['rx_verified', 'rx_denied']
				'Denied':
					readonly:
						fields: ['completed_by', 'completed_datetime', 'denied_by', 'denied_datetime']
					fields: ['rx_verified', 'rx_denied', 'rx_complete']
				'*':
					fields: ['rx_complete']
		view:
			label: 'Status'
			control: 'radio'
			readonly: true
			offscreen: true

	rx_no:
		model:
			type: 'text'
		view:
			columns: 4
			label: 'Rx #'
			class: 'claim-field'
			readonly: true

	rx_origin_id:
		model:
			required: false
			default: '4'
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '419-DJ'
		view:
			columns: 4
			label: "Rx Origin Code"
			class: 'claim-field'

	daw_code:
		model:
			default: '0'
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '408-D8'
		view:
			columns: 2
			label: 'DAW'
			class: 'claim-field'

	dose_type_id:
		model:
			source: 'list_dose_type'
			sourceid: 'code'
			sourcefilter:
				associated_therapy_id:
					'dynamic': '{therapy_id}'
			if:
				'Loading':
					fields: ['end_fill_number']
				'Maintenance':
					fields: ['start_fill_number']
		view:
			columns: 4
			note: 'Only if applicable'
			label: 'Dose Type'

	day_supply:
		model:
			min: 1
			required: false
			type: 'int'
		view:
			columns: 4
			note: 'Days provided with delivery'
			label: 'Days Supplied'
			transform: [
				{
					name: 'CalculateNextFillDate'
				}
			]

	dispense_quantity:
		model:
			min: 1
			type: 'decimal'
			max: 9999999.999
			rounding: 1
			required: false
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Dispense Quantity'
			validate: [
				{
					name: 'ParentPrefill'
					fields: [
						'careplan_order_lbl_tpn.quantity',
					]
				},
				{
					name: 'CopyForwardValue'
					field: 'label_quantity'
					overwrite: true
				}
			]

	start_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			label: 'Start Date'
			transform: [
				{
					name: 'CalculateNextFillDate'
				}
			]

	written_date:
		model:
			required: false
			type: 'date'
		view:
			columns: 4
			label: 'Written Date'
			readonly: false
			transform: [
				{
					name: 'CalculateExpirationDate'
				}
			]

	expiration_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Expiration Date'
			transform: [
				{
					name: 'PreventLongExpiration'
				}
			]

	next_fill_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Next Fill Date'

	next_delivery_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Next Delivery Date'

	label_id:
		model:
			source: 'list_label'
		view:
			label: 'Label'
			readonly: true
			offscreen: true

	number_of_labels:
		model:
			default: 1
			required: false
			type: 'int'
			min: 1
			max: 10
		view:
			columns: 4
			label: 'Number of Labels'

	label_form:
		view:
			label: 'Label Form'
			offscreen: true
			readonly: true

	tpn_additives_label:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['subform_additives_label']
					sections: ['TPN Additives Label']
		view:
			control: 'checkbox'
			label: 'Add TPN AdditivesLabel?'
			offscreen: true
			readonly: true

	add_syringe_label:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['subform_syringe_label']
					sections: ['Syringe Label']
		view:
			control: 'checkbox'
			label: 'Add Syringe Label?'
			offscreen: true
			readonly: true

	po_template_id:
		model:
			source: 'list_po_label_template'
			query: 'select_po_label_template'
			querytemplate: 'labelTemplate'
			sourcefilter:
				active:
					'static': 'Yes'
		view:
			label: 'PO Label Template'
			columns: 2
			class: 'select_prefill'
			transform: [
				suppress_refetch: true 
				name: 'SelectPrefill'
				url: '/query/select_po_label_template/?filter=id:'
				fields:
					'rxform_line1': ['line_directions']
					'rxform_line2': ['line11']
					'rxform_line3': ['line12']
					'subform_label':
						'type': 'subform'
						'field': 'subform_label'
						'fields':
							'line_directions': ['pr.line_directions']
							'line11': ['pr.line11']
							'line12': ['pr.line12']
			]

	rx_label_template_id:
		model:
			source: 'list_rx_label_template'
			query: 'select_rx_label_template'
			querytemplate: 'labelTemplate'
			sourcefilter:
				active:
					'static': 'Yes'
		view:
			label: 'Rx Template'
			columns: 2
			class: 'select_prefill'
			transform: [
				suppress_refetch: true 
				name: 'SelectPrefill'
				url: '/query/select_rx_label_template/?filter=id:'
				fields:
					'rxform_line1': ['line_directions']
					'rxform_line2': ['line11']
					'rxform_line3': ['line12']
					'rxform_line4': ['line13']
					'rxform_line5': ['line14']
					'subform_label':
						'type': 'subform'
						'field': 'subform_label'
						'fields':
							'line1': ['pr.line1']
							'line6': ['pr.line6']
							'line7': ['pr.line7']
							'line8': ['pr.line8']
							'line9': ['pr.line9']
							'line_directions': ['pr.line_directions']
							'line11': ['pr.line11']
							'line12': ['pr.line12']
							'line13': ['pr.line13']
							'line14': ['pr.line14']
			]

	subform_label:
		model:
			type: 'subform'
			multi: false
			source: '{label_form}'
			sourcefilter:
				'careplan_order_lbl_inj': {}
				'careplan_order_lbl_iv': {}
				'careplan_order_lbl_po': {}
				'careplan_order_lbl_tpn': {}
		view:
			label: 'Label Subform'

	subform_additives_label:
		model:
			type: 'subform'
			multi: false
			source: 'careplan_order_lbl_tpn_addv'
		view:
			label: 'Additives Label'

	subform_syringe_label:
		model:
			type: 'subform'
			multi: false
			source: 'careplan_order_lbl_syr'
		view:
			label: 'Syringe Label'

	notes:
		view:
			columns: 1
			label: 'Notes'

	requires_nursing:
		model:
			prefill: ['parent.requires_nursing']
			source: ['Yes']
			if:
				'Yes':
					sections: ['Nursing Instructions']
		view:
			control: 'checkbox'
			label: 'Requires nursing?'
			class: 'checkbox-only'
			offscreen: true
			readonly: true

	is_340b:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: '340B?'
			columns: 4

	#LABELS.bagsdisp
	label_quantity:
		model:
			type: 'decimal'
			rounding: 1
			required: true
		view:
			columns: 4
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Label Quantity'

	nursing_notes:
		view:
			columns: 2
			control: 'area'
			label: 'Nursing Notes'

	nursing_instructions:
		view:
			columns: 2
			control: 'area'
			label: 'Nursing Instructions'

	bill_notes:
		view:
			columns: 2
			label: 'Billing Notes'
			control: 'area'

	rx_on_file:
		model:
			source: ['Yes']
		view:
			label: 'Rx on File?'
			class: 'checkbox-only'
			control: 'checkbox'
			columns: 4

	auto_dc:
		model:
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Auto DC?'
			note: 'When no more refills, order will be discontinued'
			columns: 4

	is_refill:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			label: 'Authorized Refill?'
			readonly: true
			offscreen: true

	refill_rx_id:
		model:
			type: 'int'
		view:
			label: 'Refill Rx Id'
			readonly: true
			offscreen: true

	is_specialty:
		model:
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Specialty?'
			columns: 4

	rx_complete:
		model:
			source: ['Yes']
			if:
				'Yes':
					require_fields: ['dispense_quantity', 'rx_origin_id', 'daw_code', 'day_supply', 'start_date', 'expiration_date', 'number_of_labels', 'next_fill_date']
					fields: ['rx_verified', 'completed_by', 'completed_datetime', 'embed_fill']
					sections: ['Past Fills']
				'!':
					readonly:
						fields: ['rx_verified']
					prefill:
						completed_by: ''
						completed_datetime: ''
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Rx Setup Complete?'
			columns: 4
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						rx_complete: 'Yes'
					dest: 'completed_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						rx_complete: 'Yes'
					dest: 'completed_by'
				},
				{
					name: 'DrugAllergyInteractionRx'
				}
			]

	require_wt_items:
		model:
			source: ['Yes']
			if:
				'Yes':
					require_fields: ['wt_item_embed']
		view:
			label: 'Require WT Items?'
			readonly: true
			offscreen: true

	require_compound_items:
		model:
			source: ['Yes']
			if:
				'Yes':
					require_fields: [ 'wt_cp_item_embed']
		view:
			label: 'Require Compound Items?'
			readonly: true
			offscreen: true

	completed_by:
		model:
			source: 'user'
		view:
			label: 'Completed By'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'rx_complete'
					source_fields: ['completed_by', 'completed_datetime']
					separator: ' @ '
				}
			]

	completed_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Completed Date/Time'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'rx_complete'
					source_fields: ['completed_by', 'completed_datetime']
					separator: ' @ '
				}
			]

	verification_complete:
		model:
			source: ['Yes']
			if:
				'Yes':
					readonly:
						fields: ['daw_code', 'inventory_id', 'day_supply', 'dispense_quantity']
		view:
			label: 'Lock Field?'
			readonly: true
			offscreen: true

	rx_verified:
		model:
			source: ['Yes'] #TODO: Shoaib, need to check security here and disable button if no access  to verify prescription
			if:
				'Yes':
					fields: ['verified_by', 'verified_datetime']
					prefill:
						rx_denied: ''
						denied_datetime: ''
						denied_by: ''
				'!':
					prefill:
						verified_by: ''
						verified_datetime: ''
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Rx Verified?'
			columns: 4
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						rx_verified: 'Yes'
					dest: 'verified_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						rx_verified: 'Yes'
					dest: 'verified_by'
				}
			]

	verified_by:
		model:
			source: 'user'
		view:
			columns: 4
			label: 'Verified By'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'rx_verified'
					source_fields: ['verified_by', 'verified_datetime']
					separator: ' @ '
				}
			]

	verified_datetime:
		model:
			type: 'datetime'
		view:
			columns: 4
			label: 'Verified Date/Time'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'rx_verified'
					source_fields: ['verified_by', 'verified_datetime']
					separator: ' @ '
				}
			]

	rx_denied:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['denied_by', 'denied_datetime']
					prefill:
						rx_verified: ''
						verified_datetime: ''
						verified_by: ''
				'!':
					prefill:
						denied_by: ''
						denied_datetime: ''
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Rx Denied?'
			columns: 4
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						rx_denied: 'Yes'
					dest: 'denied_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						rx_denied: 'Yes'
					dest: 'denied_by'
				}
			]

	denied_by:
		model:
			source: 'user'
		view:
			label: 'Denied By'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'rx_denied'
					source_fields: ['denied_by', 'denied_datetime']
					separator: ' @ '
				}
			]

	denied_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Denied Date/Time'
			readonly: true
			offscreen: true
			transform: [
				{
					name: 'UpdateCombinedNoteField'
					target: 'rx_denied'
					source_fields: ['denied_by', 'denied_datetime']
					separator: ' @ '
				}
			]

	#Rx Info
	next_fill_number:
		model:
			min: 1
			default: 1
			type: 'int'
			required: true
		view:
			columns: 4
			label: 'Next Fill #'
			offscreen: true

	end_fill_number:
		model:
			min: 1
			default: 1
			type: 'int'
			required: true
		view:
			columns: 4
			note: 'The Fill # at which to end the loading dose'
			label: 'End Loading Dose Fill #'
			readonly: true

	start_fill_number:
		model:
			required: true
			min: 1
			default: 1
			type: 'int'
		view:
			columns: 4
			note: 'The Fill # at which to start the maintenance dose'
			label: 'Start Maintenance Dosing Fill #'
			readonly: true

	refill_tracking:
		model:
			required: false
			source: ['Refills', 'Doses']
			if:
				'Doses':
					fields: ['doses_allowed', 'doses_remaining', 'max_doses_per_fill']
				'Refills':
					fields: ['refills', 'refills_remaining']
		view:
			label: 'Refill Tracking'
			control: 'radio'
			readonly: true
			offscreen: true

	refills:
		model:
			required: false
			type: 'int'
		view:
			columns: -4
			label: '# Refills Allowed'
			transform: [
				{
					name: 'CalculateRefillsRemaining'
				}
			]

	refills_remaining:
		model:
			required: false
			type: 'int'
		view:
			columns: 4
			readonly: true
			label: '# Refills Remaining'

	doses_allowed:
		model:
			required: false
			type: 'int'
		view:
			columns: 2
			label: '# Doses Allowed'
			transform: [
				{
					name: 'CalculateDosesRemaining'
				}
			]
			validate: [
				{
					name: "CompareValidator"
					fields: [
						"max_doses_per_fill"
						"doses_allowed"
					]
					require: "lte"
					error: "Max Doses/Fill must be less than Doses Allowed"
				}
		]

	max_doses_per_fill:
		model:
			required: false
			type: 'int'
			min: 1
		view:
			label: 'Max Doses/Fill'
			columns: 4

	doses_remaining:
		model:
			required: false
			type: 'int'
		view:
			columns: 4
			note: 'Recalculated after each work ticket is completed'
			label: '# Doses Remaining'
			readonly: true

	doses_dispensed:
		model:
			default: 0
			required: false
			type: 'int'
		view:
			label: '# Doses Dispensed'
			readonly: true
			offscreen: true

	rx_template_id:
		model:
			source: 'list_rx_template'
			sourceid: 'code'
			required: true
		view:
			offscreen: true
			readonly: true
			columns: 4
			label: 'RX Format'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_rx_template/?limit=1&fields=list&sort=name&page_number=0&filter=code:'
				fields:
					'template_type': ['template_type'],
					'refill_tracking': ['refill_tracking'],
			]

	dea_schedule_id:
		model:
			source: 'list_dea_schedule'
			sourceid: 'code'
		view:
			columns: 4
			label: 'DEA Schedule'

	sched_rx_no:
		model:
			max: 12
		view:
			note: 'Used in NCPDP Claim 454-EK. Required by law in some states.'
			label: 'Scheduled Prescription ID Number'
			class: 'claim-field'
			readonly: true
			offscreen: true

	#IV/Compound
	comp_dsg_fm_code:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '450-EF'
		view:
			columns: 4
			label: 'Compound Dosage Form Code'
			note: '450-EF'
			class: 'claim-field'

	compound_type:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '996-G1'
		view:
			columns: 4
			label: 'Compound Type'
			class: 'claim-field'
			note: '996-G1'

	comp_disp_unit:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '451-EG'
		view:
			label: 'Compound Dispensing Unit'
			note: '451-EG'
			reference: '451-EG'
			class: 'claim-field'

	#Compounded/TPN
	doses_to_prep:
		model:
			min: 1
			required: false
			type: 'int'
		view:
			columns: 4
			label: 'Doses to Prep'
			transform: [
				{
					name: 'CalculateContainersToPrep'
				}
			]

	doses_per_container:
		model:
			required: false
			type: 'int'
		view:
			columns: 4
			label: 'Doses/Container'
			transform: [
				{
					name: 'CalculateContainersToPrep'
				}
			]

	containers_to_prep:
		model:
			required: false
			type: 'int'
		view:
			columns: 4
			label: 'Containers to Prep'
			readonly: true

	container_id:
		model:
			source: 'inventory'
			sourceid: 'id'
			sourcefilter:
				product_type:
					'static': ['Container', 'Multi-Use']
				active:
					'static': 'Yes'
			if:
				'!':
					readonly:
						fields: ['vehicle_id']
		view:
			label: 'Container'
			columns: -2
			transform: [{
				name: 'ClearFieldsIfNull'
				fields: ['vehicle_id']
			}]
			validate: [
				{
					name: 'AddContainerToWt'
				}
			]

	vehicle_id:
		model:
			source: 'inventory'
			sourceid: 'id'
			sourcefilter:
				drug_category_id:
					'static': ['IV Fluid']
				active:
					'static': 'Yes'
			if:
				'!':
					readonly:
						fields: ['volume_per_dose']
		view:
			label: 'Vehicle'
			columns: 2
			transform: [{
				name: 'ClearFieldsIfNull'
				fields: ['volume_per_dose']
			}]
			validate: [
				{
					name: 'AddVehicleToWt'
				}
			]

	volume_per_dose:
		model:
			type: 'decimal'
			rounding: 0.001
			required: false
			max: 9999999.999
			min: 0.001
			if:
				'!':
					readonly:
						fields: ['overfill']
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Volume Per Dose'
			note: 'mL'
			transform: [{
				name: 'ClearFieldsIfNull'
				fields: ['overfill']
			}]
			validate: [
				{
					name: 'AddVehicleToWt'
				}
			]

	overfill:
		model:
			type: 'decimal'
			rounding: 0.001
			required: false
			max: 9999999.999
			min: 0.001
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			columns: 4
			label: 'Overfill'
			note: 'mL'
			validate: [
				{
					name: 'AddVehicleToWt'
				}
			]

	include_cmpd_instr_wo:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Include on Work Order?'
			columns: 2

	comp_template_id:
		model:
			source: 'list_cmpd_template'
			query: 'select_rx_compounding_instructions'
			querytemplate: 'cmpInstrTemplate'
			sourcefilter:
				active:
					'static': 'Yes'
		view:
			label: 'Compounding Template'
			columns: 2
			class: 'select_prefill'
			transform: [
				{
				suppress_refetch: true 
				name: 'SelectPrefill'
				url: '/query/select_rx_compounding_instructions/?filter=id:'
				fields:
					'compounding_instructions': ['compounding_instructions']
				}
			]

	auto_compounding_instructions:
		view:
			control: 'area'
			label: 'Auto Generated'
			readonly: true
			columns: 1

	compounding_instructions:
		view:
			control: 'area'
			label: 'Compounding/Reconstitution Instructions'
			columns: 1

	# Medication Review
	med_review_wo:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Include on Work Order?'
			columns: 4

	weight:
		model:
			rounding: 0.01
			type: 'decimal'
			prefill: ['patient_measurement_log']
		view:
			columns: 4
			class: 'unit'
			label: 'Weight (Kg)'
			readonly: true
			validate: [
				{
					name: 'UpdateMedicationReview'
				}
			]

	height:
		model:
			prefill: ['patient_measurement_log']
			max: 250
			min: 15
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'unit'
			label: 'Height (cm)'
			note: 'E.g.: 143, 143cm, 1.43m, 56in, 56", 4\' 8", 4 8'
			validate: [
				{
					name: 'UpdateMedicationReview'
				}
			]

	bsa:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'BSA m²'
			readonly: true
			columns: 4

	no_of_containers:
		model:
			type: 'int'
		view:
			label: '# of Containers'
			readonly: true
			offscreen: true

	qty_per_container:
		model:
			type: 'int'
		view:
			label: 'Qty / Container'
			readonly: true
			offscreen: true

	ml_per_day:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'mL / Day'
			readonly: true
			columns: 4

	ml_kg_day:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'mL / Kg / Day'
			readonly: true
			columns: 4

	ml_m2_day:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'mL / m² / Day'
			readonly: true
			columns: 4

	final_dose_quantity:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Amount / Dose'
			readonly: true
			columns: 4

	final_day_quantity:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Amount / Day'
			readonly: true
			columns: 4

	final_m2_day_quantity:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Amount / m² / Day'
			readonly: true
			columns: 4

	final_kg_day_quantity:
		model:
			required: false
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Amount / Kg / Day'
			readonly: true
			columns: 4

	# RX Form
	# LINE9 + frequency string (same as OT.DESCRIP)
	rx_form_presc_order:
		view:
			label: 'Prescription Order'

	rxform_line1:
		model:
			required: false
		view:
			label: 'Directions'
			class: 'label-line groups top'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'rxform_line2'
			]
			validate: [
				{
					name: 'ParentPrefill'
					fields: [
						'careplan_order_lbl_iv.line_directions',
						'careplan_order_lbl_po.line_directions',
						'careplan_order_lbl_tpn.line_directions',
						'careplan_order_lbl_inj.line_directions'
					]
				}
			]

	rxform_line2:
		model:
			required: false
		view:
			label: '2.'
			class: 'no-label label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'rxform_line3'
			]
			validate: [
				{
					name: 'ParentPrefill'
					fields: [
						'careplan_order_lbl_iv.line11',
						'careplan_order_lbl_po.line11',
						'careplan_order_lbl_tpn.line11',
						'careplan_order_lbl_inj.line11'
					]
				}
			]

	rxform_line3:
		model:
			required: false
		view:
			label: '3.'
			class: 'no-label label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'rxform_line4'
			]
			validate: [
				{
					name: 'ParentPrefill'
					fields: [
						'careplan_order_lbl_iv.line12',
						'careplan_order_lbl_po.line12',
						'careplan_order_lbl_tpn.line12',
						'careplan_order_lbl_inj.line12'
					]
				}
			]

	rxform_line4:
		model:
			required: false
		view:
			label: '4.'
			class: 'no-label label-line groups middle'
			transform: [
					name: 'WrapLine'
					max: 45
					next_field: 'rxform_line5'
			]
			validate: [
				{
					name: 'ParentPrefill'
					fields: [
						'careplan_order_lbl_iv.line13',
						'careplan_order_lbl_tpn.line13',
						'careplan_order_lbl_inj.line13'
					]
				}
			]

	rxform_line5:
		model:
			required: false
			max: 45
		view:
			label: '5.'
			class: 'no-label label-line groups bottom'
			validate: [
				{
					name: 'ParentPrefill'
					fields: [
						'careplan_order_lbl_iv.line14',
						'careplan_order_lbl_tpn.line14',
						'careplan_order_lbl_inj.line14'
					]
				}
			]

	rxform_dr:
		model:
			source: 'patient_prescriber'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			columns: 2
			label: 'Prescriber'
			class: 'select_prefill label-line'
			transform: [
				{
					name: 'SelectPrefill'
					url: '/form/patient_prescriber/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
					fields:
						'physician_id': ['physician_id']
				}
			]

	physician_id:
		model:
			source: 'physician'
		view:
			label: 'Referring Physician'
			offscreen: true
			readonly: true

	rxform_print_cover_letter:
		model:
			source: ['Yes']
		view:
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Print Cover Letter?'
			columns: 4

	rxform_add1:
		model:
			query: 'select_pot_order'
			dynamic:
				source: 'list_pot_order'
			max: 70
		view:
			class: 'label-line groups top'
			label: 'Add Order 1'

	rxform_add2:
		model:
			query: 'select_pot_order'
			dynamic:
				source: 'list_pot_order'
			max: 70
		view:
			class: 'label-line groups middle'
			label: 'Add Order 2'

	rxform_add3:
		model:
			query: 'select_pot_order'
			dynamic:
				source: 'list_pot_order'
			max: 70
		view:
			class: 'label-line groups bottom'
			label: 'Add Order 3'

	# Work Ticket
	wt_item_embed:
		model:
			required: false
			multi: true
			sourcefilter:
				rx_no:
					'dynamic': '{rx_no}'
		view:
			embed:
				form: 'careplan_order_rx_wt'
				add_preset:
					medid: '{medid}'
					rx_no: '{rx_no}'
					patient_id: '{patient_id}'
			label: 'Work Ticket Item'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['inventory_id', 'dispense_quantity']
				label: ['Item', 'Quantity']
				width: [80, 20]
			validate: [
				{
					name: 'CheckUniqueItems'
				},
				{
					name: 'GenerateMedicationReview'
				}
			]

	wt_cp_item_embed:
		model:
			required: false
			multi: true
			sourcefilter:
				rx_no:
					'dynamic': '{rx_no}'
		view:
			embed:
				form: 'careplan_order_rx_cp'
				add_preset:
					rx_no: '{rx_no}'
					patient_id: '{patient_id}'
			label: 'Compound Item'
			grid:
				add: 'flyout'
				rank: 'none'
				hide_cardmenu: true
				edit: true
				fields: ['is_primary_ingredient', 'inventory_id', 'dispense_quantity']
				label: ['Active Ingred?', 'Item', 'Quantity']
				width: [15, 65, 20]
			validate: [
				{
					name: 'CheckUniqueItems'
				},
				{
					name: 'GenerateMedicationReview'
				}
			]

	embed_fill:
		model:
			required: false
			multi: true
			sourcefilter:
				rx_no:
					'dynamic': '{rx_no}'
				void:
					'static': '!Yes'
		view:
			embed:
				form: 'careplan_order_rx_disp'
				selectable: false
			grid:
				add: 'none'
				edit: true
				hide_cardmenu: true
				fields: ['created_by', 'created_on', 'fill_number', 'status']
				label: ['Created By', 'Created On', 'Fill #', 'Status']
				width: [20, 35, 20, 25]
				rank: 'none'
			label: 'Dispense Records'
			readonly: true

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'careplan_order_rx'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					form_code: '{code}'
					form_name: 'careplan_order_rx'
					patient_id: '{patient_id}'
					source: 'Scanned Document'
					form_filter: 'careplan_order_rx'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: "{inventory_id}"
	prefill:
		patient:
			link:
				id: 'patient_id'
		patient_measurement_log:
			link:
				id: 'patient_id'
			max: 'created_on'
	indexes:
		unique: [
			['rx_no']
		]
		many: [
			['patient_id']
			['careplan_id']
			['therapy_id']
			['inventory_id']
			['dose_type_id']
			['rx_no']
			['vehicle_id']
			['container_id']
			['refill_rx_id']
		]

	sections_group: [

		'Sign Off':
			hide_header: true
			indent: false
			tab: 'Prescription'
			fields: ['status_id', 'completed_by', 'completed_datetime']

		'Dispensing Info':
			indent: false
			hide_header: true
			tab: 'Prescription'
			fields: ['rx_template_id','refill_tracking', 'inventory_id', 'rx_no', 'code', 'day_supply', 'po_template_id', 'rx_label_template_id', 'dispense_quantity', 'rx_origin_id', 'daw_code', 'max_doses_per_fill', 'doses_to_prep', 'doses_per_container', 'containers_to_prep',
			'container_id', 'vehicle_id', 'volume_per_dose', 'overfill', 'written_date', 'start_date', 'expiration_date', 'next_fill_date', 'next_delivery_date', 'template_type',
			'number_of_labels', 'notes', 'label_form', 'tpn_additives_label', 'add_syringe_label', 'require_wt_items', 'require_compound_items']

		'Label':
			hide_header: true
			indent: false
			tab: 'Prescription'
			fields: ['subform_label']

		'Rx Info':
			hide_header: true
			indent: false
			tab: 'Prescription'
			fields: ['rxform_dr', 'physician_id', 'dose_type_id', 'refills', 'refills_remaining', 'doses_allowed', 'doses_remaining', 'max_doses_per_fill', 'next_fill_number', 'start_fill_number', 'end_fill_number', 'dea_schedule_id', 'sched_rx_no',
			'is_340b', 'rx_on_file', 'auto_dc', 'requires_nursing', 'is_refill', 'refill_rx_id', 'is_specialty', 'rx_complete', 'rx_verified', 'verified_by', 'verified_datetime',
			'rx_denied', 'denied_by', 'denied_datetime', 'verification_complete']


		'Work Ticket':
			indent: false
			hide_header: true
			tab: 'Setup'
			fields: ['wt_item_embed']

		'Compounding Record':
			indent: false
			hide_header: true
			tab: 'Setup'
			fields: ['wt_cp_item_embed']

		'Compounding Instructions':
			indent: false
			hide_header: true
			tab: 'Compounding Instructions'
			fields: ['include_cmpd_instr_wo', 'comp_template_id', 'auto_compounding_instructions', 'compounding_instructions',
			'comp_dsg_fm_code', 'compound_type']

		'Medication Review':
			indent: false
			tab: 'Compounding Instructions'
			fields: ['med_review_wo', 'weight', 'bsa', 'no_of_containers', 'qty_per_container',
			'ml_per_day', 'ml_kg_day', 'ml_m2_day', 'final_dose_quantity', 'final_day_quantity',
			'final_kg_day_quantity', 'final_m2_day_quantity']

		'Nursing Instructions':
			indent: false
			hide_header: true
			tab: 'Nursing Instructions'
			fields: ['nursing_instructions']

		'Rx Form':
			indent: false
			hide_header: true
			tab: 'Rx Form'
			fields: ['rx_form_presc_order', 'rxform_line1', 'rxform_line2', 'rxform_line3',
			'rxform_line4', 'rxform_line5', 'rxform_print_cover_letter',
			'rxform_add1', 'rxform_add2', 'rxform_add3']

		'TPN Additives Label':
			hide_header: true
			indent: false
			tab: 'Additives Label'
			fields: ['subform_additives_label']

		'Syringe Label':
			hide_header: true
			indent: false
			fields: ['subform_syringe_label']
			tab: 'Syringe Label'

		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'

		'Select Rx Template':
			hide_header: true
			indent: false
			modal: true
			fields: ['select_rx_template_id']

		'Past Fills':
			hide_header: true
			indent: false
			fields: ['embed_fill']
			tab: 'Fills'
	]

view:
	validate: [
		{
			name: 'InteractionChecks'
		},
		{
			name: 'CheckRxCompletedAndGenerateClaims'
		},
		{
			name: 'CheckRxDeniedAndReverseClaims'
		},
		{
			name: "DateOrderValidator"
			fields: [
				"start_date",
				"next_fill_date"
			]
			error: "Next fill date cannot be before start date"
		},
		{
			name: "DateOrderValidator"
			fields: [
				"start_date"
				"expiration_date",
			]
			error: "Expiration date cannot be before start date"
		},
		{
			name: "DateOrderValidator"
			fields: [
				"next_fill_date",
				"expiration_date"
			]
			error: "Next fill date cannot be after expiration date"
		},
		{
			name: "DateOrderValidator"
			fields: [
				"written_date",
				"next_fill_date"
			]
			error: "Next fill date cannot be before written date"
		},
		{
			name: "DateOrderValidator"
			fields: [
				"next_delivery_date",
				"expiration_date"
			]
			error: "Next delivery date cannot be before after expiration date"
		}
	]
	hide_cardmenu: true
	comment: 'Patient > Prescription'
	grid:
		fields: ['inventory_id', 'rx_no', 'dispense_quantity', 'refills_remaining']
		sort: ['-created_on']
	find:
		basic: ['inventory_id', 'rx_no']
	label: 'Prescription'
	open: 'edit'
