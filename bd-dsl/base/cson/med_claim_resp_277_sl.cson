fields:

	line_item_control_number:
		view:
			columns: 4
			label: 'Line Item Control Number'
			note: 'Loop 2220[D|E] REF02'
			readonly: true
			_meta:
				path: 'lineItemControlNumber'

	service_line_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Service Line Date'
			note: 'Loop 2220[D|E] DTP03 where DTP02=D8'
			readonly: true
			_meta:
				path: 'serviceLineDate'

	begin_service_line_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Begin Service Line Date'
			note: 'Loop 2220[D|E] DTP03 where DTP02=RD8'
			readonly: true
			_meta:
				path: 'beginServiceLineDate'

	end_service_line_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'End Service Line Date'
			note: 'Loop 2220[D|E] DTP03 where DTP02=RD8'
			readonly: true
			_meta:
				path: 'endServiceLineDate'

	inventory_id:
		model:
			source: 'inventory'
			type: 'int'
		view:
			columns: 2
			label: 'Inventory Item'
			readonly: true

	service_id_qualifier_code:
		view:
			columns: 4
			label: 'Service ID Qualifier Code'
			note: 'Loop 2220[D|E] SVC01-01'
			readonly: true
			offscreen: true
			_meta:
				path: 'service.serviceIdQualifierCode'

	service_id_qualifier_code_value:
		view:
			columns: 4
			label: 'Service ID Qualifier Code Value'
			note: 'Loop 2220[D|E] SVC01-01'
			readonly: true
			_meta:
				path: 'service.serviceIdQualifierCodeValue'

	procedure_code:
		view:
			columns: 4
			label: 'Procedure Code'
			note: 'Loop 2220[D|E] SVC01-02'
			readonly: true
			_meta:
				path: 'service.procedureCode'

	modifier_1:
		view:
			columns: -4
			label: 'Modifier 1'
			note: 'Loop 2220[D|E] SVC01-03'
			readonly: true
			_meta:
				path: 'service.procedureModifiers[0]'

	modifier_2:
		view:
			columns: 4
			label: 'Modifier 2'
			note: 'Loop 2220[D|E] SVC01-04'
			readonly: true
			_meta:
				path: 'service.procedureModifiers[1]'

	modifier_3:
		view:
			columns: 4
			label: 'Modifier 3'
			note: 'Loop 2220[D|E] SVC01-05'
			readonly: true
			_meta:
				path: 'service.procedureModifiers[2]'

	modifier_4:
		view:
			columns: 4
			label: 'Modifier 4'
			note: 'Loop 2220[D|E] SVC01-06'
			readonly: true
			_meta:
				path: 'service.procedureModifiers[3]'

	charge_amount:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: -4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Charge Amount'
			note: 'Loop 2220[D|E] SVC02'
			readonly: true
			_meta:
				path: 'service.chargeAmount'

	amount_paid:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Paid Amount'
			note: 'Loop 2220[D|E] SVC03'
			readonly: true
			_meta:
				path: 'service.amountPaid'

	revenue_code:
		view:
			columns: 4
			label: 'Revenue Code'
			note: 'Loop 2220[D|E] SVC04'
			readonly: true
			_meta:
				path: 'service.revenueCode'

	submitted_units:
		model:
			rounding: 0.001
			type: 'decimal'
		view:
			columns: 4
			label: 'Charge Units'
			class: 'numeral'
			format: '0,0.[000000]'
			note: 'Loop 2220[D|E] SVC07'
			readonly: true
			_meta:
				path: 'service.submittedUnits'

	status_effective_date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Status Effective Date'
			note: 'Loop 2220[D|E] STC02'
			readonly: true
			_meta:
				path: 'serviceClaimStatuses[].effectiveDate'

	health_care_claim_status_category_code:
		view:
			columns: -4
			label: 'Health Care Claim Status Category Code'
			note: 'STC01-1, STC010-1, STC11-1'
			readonly: true
			_meta:
				path: 'serviceClaimStatuses[].serviceStatuses[].healthCareClaimStatusCategoryCode'

	health_care_claim_status_category_code_value:
		view:
			columns: 2
			label: 'Health Care Claim Status Category Code Value'
			note: 'STC01-1, STC010-1, STC11-1'
			readonly: true
			_meta:
				path: 'serviceClaimStatuses[].serviceStatuses[].healthCareClaimStatusCategoryCodeValue'

	status_code:
		view:
			columns: -4
			label: 'Status Code'
			note: 'STC01-2, STC010-2, STC11-2'
			readonly: true
			_meta:
				path: 'serviceClaimStatuses[].serviceStatuses[].statusCode'

	status_code_value:
		view:
			columns: 2
			label: 'Status Code Value'
			note: 'STC01-2, STC010-2, STC11-2'
			readonly: true
			_meta:
				path: 'serviceClaimStatuses[].serviceStatuses[].statusCodeValue'

	entity_identifier_code:
		view:
			columns: -4
			label: 'Entity Identifier Code'
			note: 'STC01-3, STC010-3, STC11-3'
			readonly: true
			_meta:
				path: 'serviceClaimStatuses[].serviceStatuses[].entityIdentifierCode'

	entity_identifier_code_value:
		view:
			columns: 4
			label: 'Entity Identifier Code Value'
			note: 'STC01-3, STC010-3, STC11-3'
			readonly: true
			_meta:
				path: 'serviceClaimStatuses[].serviceStatuses[].entityIdentifierCodeValue'

	ncpdp_reject_payment_codes:
		view:
			columns: 4
			label: 'NCPDP Reject Payment Codes'
			note: 'STC01-4, STC010-4, STC11-4'
			readonly: true
			_meta:
				path: 'serviceClaimStatuses[].serviceStatuses[].nationalCouncilForPrescriptionDrugProgramsRejectPaymentCodes'

	status_history:
		model:
			type: 'subform'
			multi: true
			source: 'med_claim_resp_277_sl_hst'
		view:
			label: 'Status History'
			readonly: true
			_meta:
				path: 'serviceClaimStatuses[].serviceStatuses'

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: 'Claim Status Service Line'
	indexes:
		many: [
			['line_item_control_number']
			['service_line_date']
			['begin_service_line_date']
			['end_service_line_date']
			['procedure_code']
			['modifier_1']
			['modifier_2']
			['modifier_3']
			['modifier_4']
			['status_effective_date']
			['health_care_claim_status_category_code']
			['status_code']
			['entity_identifier_code']
		]
	sections_group: [
		'Claim Status Service Line':
			hide_header: true
			sections: [
				'Summary':
					hide_header: true
					fields: ['line_item_control_number', 'service_line_date', 'begin_service_line_date',
					'end_service_line_date', 'service_id_qualifier_code', 'service_id_qualifier_code_value',
					'procedure_code', 'modifier_1', 'modifier_2', 'modifier_3', 'modifier_4',
					'charge_amount', 'amount_paid', 'revenue_code', 'submitted_units',
					'status_effective_date', 'health_care_claim_status_category_code', 'health_care_claim_status_category_code_value',
					'status_code', 'status_code_value', 'entity_identifier_code', 'entity_identifier_code_value',
					'ncpdp_reject_payment_codes']
				'Status History':
					indent: false
					fields: ['status_history']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Claim Status Service Line'
	grid:
		fields: ['inventory_id', 'status_code_value', 'health_care_claim_status_category_code_value', 'amount_paid']
		label: ['Inventory ID', 'Status Code', 'Status Category', 'Paid Amount']
		width: [30, 30, 25, 15]
		sort: ['-created_on']
	label: 'Claim Status'
	open: 'read'