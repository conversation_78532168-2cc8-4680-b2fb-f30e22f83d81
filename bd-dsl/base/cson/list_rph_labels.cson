fields:

	firstname:
		model:
			max: 32
			min: 2
			required: true
		view:
			label: 'First Name'
			note: 'Letters, numbers, comma, \', -, . only'
			validate: [
					name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
			]
			columns: 3

	lastname:
		model:
			max: 32
			min: 2
			required: true
		view:
			label: 'Last Name'
			note: 'Letters, numbers, comma, \', -, . only'
			validate: [
					name: '<PERSON><PERSON><PERSON>da<PERSON>'
			]
			columns: 3

	professional_designation:
		model:
			type: 'text'
			max: 12
			required: true
		view:
			label: 'Professional Designation'
			note: '12 characters'
			columns: 3

	password:
		model:
			max: 32
			min: 3
			required: true
		view:
			label: 'Password'
			note: 'Letters & numbers required'
			columns: 3

	npi:
		model:
			type: 'text'
			max: 10
		view:
			note: 'Required for some payers'
			label: 'Pharmacist NPI'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid NPI, must be 10 digits'
			}]
			columns: 3

	license_number:
		model:
			type: 'text'
			max: 12
		view:
			label: 'License Number'
			columns: 3

	code:
		model:
			max: 3
			min: 2
			required: true
		view:
			columns: 3
			label: 'Initials'
			note: '2-3 characters'
			findunique: true

	user_id:
		model:
			source: 'user'
			sourceid: 'id'
		view:
			columns: 3
			label: 'Associated User'

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 3
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['lists']
	indexes:
		unique: [
			['code', 'active']
		]
	name: '{code} -{firstname} {lastname} {professional_designation}'
	sections:
		'Label Rph Designation':
			hide_header: true
			indent: false
			fields: ['firstname', 'lastname', 'professional_designation',
			'password', 'npi', 'license_number','code', 'user_id', 'active']

view:
	comment: 'Manage > Label Rph Designation'
	find:
		basic: ['code', 'active']
	grid:
		fields: ['firstname', 'lastname', 'professional_designation', 'code', 'active']
		sort: ['code']
	label: 'Label Rph Designation'
	open: 'read'
