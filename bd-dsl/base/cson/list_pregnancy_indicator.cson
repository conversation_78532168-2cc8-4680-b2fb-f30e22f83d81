fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'
			findunique: true

	description:
		model:
			required: true
			max: 128
		view:
			label: 'Description'
			findunique: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['code']
		]
	name: '{code} - {description}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'description']

view:
	comment: 'Manage > Pregnancy Indicator'
	find:
		basic: ['code','description']
	grid:
		fields: ['code','description']
		sort: ['code']
	label: 'Pregnancy Indicator'
	open: 'read'
