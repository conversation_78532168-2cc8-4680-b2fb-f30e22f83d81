fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status_id:
					'static': ['1', '5']
			if:
				'*':
					fields: ['order_item_id']
		view:
			columns: 2
			label: 'Referral'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/careplan_order/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'order_no': ['order_no']
			]

	order_no:
		view:
			label: 'Referral No'
			readonly: true
			columns: 2

	order_item_id:
		model:
			source: 'careplan_order_rx'
			sourcefilter:
				order_no:
					'dynamic': '{order_no}'
		view:
			columns: 2
			label: 'Prescription'

	note_type:
		model:
			required: true
			source: 'list_note_type'
			sourcefilter:
				active:
					'static': 'Yes'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Note Type'

	subject:
		model:
			required: true
			max: 4096
			dynamic:
				source: 'list_note_subject'
		view:
			columns: 2
			label: 'Subject'

	note:
		model:
			required: true
			type: 'text'
		view:
			control: 'area'
			label: 'Notes'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	user_id:
		model:
			source: 'user'
		view:
			control: 'select'
			label: 'Note By'
			readonly: true
			template: '{{user.id}}'

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	template_tag:
		model:
			type: 'text'
		view:
			label: 'Template Tag'
			readonly: true
			offscreen: true

	delivery_ticket_id:
		model:
			source: 'careplan_delivery_tick'
		view:
			label: 'Delivery Ticket'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ["patient-form"]
	indexes:
		many: [
			['patient_id']
			['careplan_id']
			['order_id']
			['order_no']
			['order_item_id']
		]
	reportable: true
	name: ['patient_id', 'note_type', 'subject', 'created_on']
	sections:
		'Progress Note':
			fields: ['order_id', 'order_no', 'order_item_id', 'user_id', 'note_type', 'subject', 'note', 'delivery_ticket_id']

view:
	dimensions:
        width: '75%'
        height: '75%'
	comment: 'Patient > Progress Notes'
	find:
		basic: ['created_on', 'subject']

		advanced: ['note']
	grid:
		fields: ['created_on', 'user_id', 'subject', 'note']
		width: [20, 15, 20, 45]
		sort: ['-created_on']
	label: 'Progress Note'
	open: 'read'
