fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_hat_qol.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	normal_life:
		model:
			required: false

			max: 64
			source: ['All the time', 'A lot of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Taking medicine has made it hard to live a normal life'

	feel_better:
		model:
			required: false

			max: 64
			source: ['All the time', 'A lot of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Taking medicine has made the patient feel better'

	more_sick:
		model:
			required: false

			max: 64
			source: ['All the time', 'A lot of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Taking medicine has made the patient feel more sick than he/she is'

	fighting:
		model:
			required: false

			max: 64
			source: ['All the time', 'A lot of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Taking medicine has made the patient feel as if he/she is fighting HIV'

	enjoy_life:
		model:
			required: false

			max: 64
			source: ['All the time', 'A lot of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Has the patient enjoyed living'

	will_to_live:
		model:
			required: false

			max: 64
			source: ['All the time', 'A lot of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Has the patient felt a strong will to live'

	content:
		model:
			required: false

			max: 64
			source: ['All the time', 'A lot of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Has the patient been content with his/her life'

	control:
		model:
			required: false

			max: 64
			source: ['All the time', 'A lot of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Has the patient felt in control of his/her life'

	self_image:
		model:
			required: false

			max: 64
			source: ['All the time', 'A lot of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Has the patient felt good about himself/herself'

	motivation:
		model:
			required: false

			max: 64
			source: ['All the time', 'A lot of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Has the patient felt motivated to do things'

	social:
		model:
			required: false

			max: 64
			source: ['All the time', 'A lot of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Has the patient been satisfied with how socially active he/she is'

	healthy:
		model:
			required: false

			max: 64
			source: ['All the time', 'A lot of the time', 'Some of the time', 'A little of the time', 'None of the time']
		view:
			control: 'radio'
			label: 'Has the patient been pleased with how healthy he/she has been'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_hat_qol:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'HIV/AIDS Quality Of Life Survey':
			fields: ['last_assessment_date', 'assessment_date']
		'4-Week Review':
			note: 'The following questions ask how the patient has felt about HIV medications in the past 4 weeks'
			fields: ['normal_life', 'feel_better', 'more_sick', 'fighting']
			prefill: 'clinical_hat_qol'
		'4-Week Review Satisfaction':
			note: 'The following questions ask about the patient’s life satisfaction in the past 4 weeks'
			fields: ['enjoy_life', 'will_to_live', 'content', 'control', 'self_image', 'motivation', 'social', 'healthy']
			prefill: 'clinical_hat_qol'

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Clinical > HIV/AIDS-targeted Quality of Life (HAT-QOL)'
	find:
		basic: ['assessment_date']
	grid:
		fields: ['created_on', 'assessment_date', 'created_by']
		sort: ['-id']
	label: 'HIV/AIDS-targeted Quality of Life (HAT-QOL)'
