fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Patient Id'

	htc_care:
		model:
			max: 3
			prefill: ['ongoing_factor', 'assessment_factor']
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Does the patient obtain care from an HTC?'

	htc_details:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
		view:
			columns: 2
			control: 'area'
			label: 'HTC comment:'

	therapy_regimen:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
			multi: false
			source: ['Prophylaxis for Factor Replacement', 'On-Demand', 'Prophylaxis and On-Demand', 'Immune Tolerance Induction', 'Surgery', 'Prophylaxis for Non-Factor Replacement (i.e. Hemlibra)']
		view:
			columns: -2
			control: 'checkbox'
			label: 'Therapy Regimen'

	preferred_package:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
			multi: true
			source: ['Separate Doses', 'Separate Supply Kits', 'Bulk Supply Bags', 'Do<PERSON> and Supplies together']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Preferred Packaging'

	educate_tracking:
		model:
			max: 3
			required: false
			source: ['No', 'Yes', 'NA']
		view:
			columns: -2
			control: 'radio'
			label: 'Patient educated about importance of tracking bleeds?'

	educate_tracking_comment:
		view:
			columns: 2
			control: 'area'
			label: 'Bleed Tracking Education Comment'

	educate_reporting:
		model:
			max: 3
			required: false
			source: ['No', 'Yes', 'NA']
		view:
			columns: -2
			control: 'radio'
			label: 'Does the patient report their monthly bleeds to the pharmacy?'
	
	educate_reporting_comment:
		view:
			columns: 2
			control: 'area'
			label: 'Bleed Reporting Education Comment'

	# Factor Joint Assessment
	target_joints:
		model:
			multi: true
			prefill: ['ongoing_factor', 'assessment_factor']
			source: ["Left Knee", "Right Knee", "Left Ankle", "Right Ankle", "Left Hip", "Right Hip", "Left Elbow", "Right Elbow", "Left Hand", "Right Hand", "Left Shoulder", "Right Shoulder", "Head/Neck", "Other"]
			if:
				"Other":
					fields: ["target_joints_other"]
		view:
			columns: -2
			control: 'checkbox'
			label: "Target Joints"

	target_joints_other:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
		view:
			columns: 2
			label: "Other Target Joints"

	pain_scale:
		model:
			required: true
			source: ['N/A','0','1','2','3','4','5','6','7','8','9','10']
			if:
				'N/A':
					fields: ['pain_scale_why']
		view:
			columns: -2
			control: 'radio'
			label: 'What is your pain on a scale 0-10'

	pain_scale_why:
		view:
			columns: 2
			control: 'area'
			label: 'Details'

	rom:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['rom_change']
		view:
			columns: -2
			control: 'radio'
			label: 'Have you noticed a change in your range of motion?'

	rom_change:
		model:
			max: 3
			required: true
			source: ['Improved', 'Declined']
		view:
			columns: 2
			control: 'radio'
			label: 'Range of motion change:'

	track_bleeds:
		model:
			max: 3
			required: false
			source: ['No', 'Yes']
		view:
			columns: -2
			control: 'radio'
			note: '(bleed log, notebook, phone app, etc.)'
			label: 'Does the patient track their bleeds?'

	track_bleeds_how:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
			required: false
		view:
			columns: 2
			control: 'area'
			label: 'If so, what method do you use and how often are you tracking them?'

	track_bleeds_why:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
			required: false
		view:
			columns: 2
			control: 'area'
			label: 'If not, what is the reason?'

	track_bleeds_comment:
		view:
			columns: 2
			control: 'area'
			label: 'Patient Bleed Tracking Comment'

	bleed_history:
		view:
			control: 'area'
			note: 'for the month'
			label: "Patient's Bleeding History"

	review_bleeds:
		model:
			max: 3
			required: false
			source: ['No', 'Yes']
		view:
			columns: -2
			control: 'radio'
			label: 'Did you review the bleed log for any patient reported bleeds?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		ongoing_factor:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
		assessment_factor:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Factor Pre-Assessment':
			area: 'preassessment'
			fields: ['htc_care', 'htc_details', 'therapy_regimen', 'preferred_package']
		'Factor Patient Assessment':
			prefill: 'ongoing_factor'
			fields: ['target_joints', 'target_joints_other',
			'pain_scale', 'pain_scale_why', 'bleed_history', 'rom', 'rom_change']
		'Hemophilia Bleed Reporting':
			area: 'footer'
			fields: ['educate_tracking', 'educate_tracking_comment',
			'educate_reporting', 'educate_reporting_comment',
			'track_bleeds', 'track_bleeds_how', 'track_bleeds_why',
			'track_bleeds_comment', 'review_bleeds']

view:
	comment: 'Patient > Careplan > Ongoing > Factor'
	grid:
		fields: ['created_on', 'created_by']
	label: 'Ongoing Assessment: Factor'
	open: 'read'