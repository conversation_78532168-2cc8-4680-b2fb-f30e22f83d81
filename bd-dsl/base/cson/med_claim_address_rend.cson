fields:

	address1:
		model:
			min: 1
			max: 35
		view:
			columns: 'addr_1'
			reference: 'N301'
			label: 'Address'
			class: "api_prefill"
			_meta:
				location: '2310B N3'
				field: '01'
				path: 'rendering.address.address1'
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'address1': ['addressLabel']
					'city': ['city']
					'state': ['stateCode']
					'postal_code': ['postalCode']
			]

	address2:
		model:
			min: 1
			max: 35
		view:
			columns: 'addr_2'
			reference: 'N302'
			label: 'Address 2'
			_meta:
				location: '2310B N3'
				field: '02'
				path: 'rendering.address.address2'

	city:
		model:
			min: 1
			max: 60
		view:
			reference: 'N401'
			columns: 'addr_city'
			label: 'City'
			_meta:
				location: '2310B N4'
				field: '01'
				path: 'rendering.address.city'

	state:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			reference: 'N402'
			columns: 'addr_state'
			label: 'State'
			_meta:
				location: '2310B N4'
				field: '02'
				path: 'rendering.address.state'

	postal_code:
		model:
			max: 15
			min: 3
		view:
			columns: 'addr_zip'
			format: 'us_zip'
			label: 'Zip'
			reference: 'N403'
			_meta:
				location: '2310B N4'
				field: '03'
				path: 'rendering.address.postalCode'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'postal_code'
						city:'city'
						state:'state'
			]

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['city','state', 'postal_code']
	sections:
		'Rendering Provider Address':
			hide_header: true
			fields: ['address1', 'address2', 'city', 'state', 'postal_code']

view:
	dimensions:
		width: '85%'
		height: '45%'
	hide_cardmenu: true
	comment: 'Rendering Provider Address'
	grid:
		fields: ['address1', 'address2', 'city', 'state', 'postal_code']
		width: [30, 30, 20, 10, 10]
		sort: ['-created_on']
	label: 'Rendering Provider Address'
	open: 'read'