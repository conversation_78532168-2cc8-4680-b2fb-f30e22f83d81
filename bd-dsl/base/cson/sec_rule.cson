fields:
	type:
		model:
			source: ['Rule', 'RLS Global', 'RLS Access']
			if:
				'RLS Global':
					fields: ['table_name', 'rule_link']
				'RLS Access':
					fields: ['table_name', 'query_value', 'cache_result', 'rule_link']
				'Rule':
					fields: ['path']
		view:
			label: 'Type'
			note: 'RLS Global Restrict Access, RLS Acess Grant Access'
	description:
		model:
			required: true
		view:
			label: 'Description'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'
	module:
		model:
			type: 'int'
			source: 'module'
			required: true
		view:
			label: 'Module'
	submodule:
		model:
			type: 'text'
		view:
			label: 'Sub-module'
	path:
		model:
			type: 'text'
		view:
			label: 'Path'
			transform: [
				name: 'SetRulePath'
			]
	table_name:
		model:
			type: 'text'
			source: 'coffeedsl'
			sourceid: 'code'
		view:
			label: 'Table'
	rule_key:
		model:
			type: 'text'
		view:
			label: 'Rule Key'
	rule_link:
		view:
			label: 'Rule Link'
			note: 'Any table with this field name will also be filtered by this rule'
	default_value:
		model:
			type: 'text'
		view:
			label: 'Default Value'
			note: 'Use -1111 for int column and -zzz for string column while RLS Global Restriction'
	query_value:
		view:
			control: 'area'
			label: 'Query Value'
			note: 'Use $1 for logged in user'
	cache_result:
		model:
			source: ['Yes', 'No']
			default: 'Yes'
		view:
			label: 'Cache Result?'
			note: 'Do you want to cache query result on login or run this query as subquery in where clause of view call'
	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'


model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['setup']
	sections:
		'Security Rule':
			fields: ['type', 'description', 'module', 'submodule', 'path', 'table_name', 'cache_result', 'rule_key', 'default_value', 'query_value', 'allow_sync', 'active']
	name: '{description}'
	sync_mode: 'mixed'


view:
	hide_cardmenu: true
	comment: 'Manage > Security Rule'
	find:
		basic: ['type', 'description', 'module', 'submodule', 'path', 'table_name', 'cache_result', 'allow_sync', 'active']
	grid:
		fields: ['type', 'description', 'module', 'submodule']
		sort: ['-created_on']
	label: 'Security Rule'
