fields:

	patient_id:
		model:
			type: 'int'
			required: true
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			required: true
			source: 'careplan_order'
			type: 'int'
		view:
			label: 'Referral Id'

	subform_factor:
		model:
			multi: true
			type: 'subform'
			source: 'careplan_order_factor'
		view:
			label: 'Factor Drugs'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				fields: ['inventory_id', 'dose_str', 'allowed_variance']
				label: ['Drug', 'Dose', 'Var %']
				width: [50, 30, 20]

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name: ['patient_id']
	sections_group: [
		'Factor Drugs':
			hide_header: true
			indent: false
			fields: ['subform_factor']
	]

view:
	hide_header: true
	comment: 'Factor Drugs'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Factor Drugs'
	open: 'edit'