fields:

	prod_id_qualifier:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '552-AP'
		view:
			columns: 3
			note: '552-AP'
			label: 'Prod ID Qual'
			readonly: true

	prod_id:
		view:
			columns: 3
			note: '553-AR'
			label: 'Prod ID'
			readonly: true

	pref_prod_incent:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '554-AS'
			label: 'Pref Prod Incent'
			class: 'numeral money'
			readonly: true
			format: '$0,0.00'

	pre_prod_cs_incent:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
		view:
			columns: 3
			note: '555-AT'
			label: 'Pref Prod Cost Share Incent'
			class: 'numeral money'
			readonly: true
			format: '$0,0.00'

	prod_desc:
		view:
			columns: 3
			note: '556-AU'
			label: 'Pref Prod Desc'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['prod_id_qualifier', 'prod_id', 'pref_prod_incent']
	sections:
		'Preferred Product':
			hide_header: true
			indent: false
			fields: ['prod_id_qualifier', 'prod_id', 'pref_prod_incent',
			'pre_prod_cs_incent', 'prod_desc']

view:
	dimensions:
		width: '65%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Preferred Product'
	grid:
		fields: ['prod_desc', 'pref_prod_incent', 'pre_prod_cs_incent']
		width: [50, 25, 25]
		sort: ['-created_on']
	label: 'Preferred Product'
	open: 'read'