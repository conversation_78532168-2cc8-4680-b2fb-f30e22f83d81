fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			multi: false
			source: 'careplan_order'
		view:
			label: 'Referral'
			readonly: true

	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections:['Hemophilia Safety']
		view:
			label: 'Primary Therapy'
			offscreen: true
			readonly: true

	therapy_2:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections:['Hemophilia Safety']
		view:
			label: 'Secondary Therapy'
			offscreen: true
			readonly: true

	therapy_3:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections:['Hemophilia Safety']
		view:
			label: 'Tertiary Therapy'
			offscreen: true
			readonly: true

	therapy_4:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections:['Hemophilia Safety']
		view:
			label: 'Quaternary Therapy'
			offscreen: true
			readonly: true

	therapy_5:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections:['Hemophilia Safety']
		view:
			label: 'Quinary Therapy'
			offscreen: true
			readonly: true

	contact_date:
		model:
			required: true
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	contact_time:
		model:
			type: 'time'
			required: true
		view:
			label: 'Time'
			template: '{{now}}'

	clear_pathways:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['clear_pathways_instr', 'clear_pathways_resp']
		view:
			control: 'radio'
			label: 'Clear Pathways'

	clear_pathways_instr:
		view:
			label: 'Instruction or Recommendation'

	clear_pathways_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	floors:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['floors_instr', 'floors_resp']
		view:
			control: 'radio'
			label: 'Floors'

	floors_instr:
		view:
			label: 'Instruction or Recommendation'

	floors_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	furniture:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['furniture_instr', 'furniture_resp']
		view:
			control: 'radio'
			label: 'Furniture Layout'

	furniture_instr:
		view:
			label: 'Instruction or Recommendation'

	furniture_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	lighting:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['lighting_instr', 'lighting_resp']
		view:
			control: 'radio'
			label: 'Adequate Lighting'

	lighting_instr:
		view:
			label: 'Instruction or Recommendation'

	lighting_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	stairs:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['stairs_instr', 'stairs_resp']
		view:
			control: 'radio'
			label: 'Stairs'

	stairs_instr:
		view:
			label: 'Instruction or Recommendation'

	stairs_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	phone:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['phone_instr', 'phone_resp']
		view:
			control: 'radio'
			label: 'Phone Access'

	phone_instr:
		view:
			label: 'Instruction or Recommendation'

	phone_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	emergency_num:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['emergency_num_instr', 'emergency_num_resp']
		view:
			control: 'radio'
			label: 'Emergency Numbers'

	emergency_num_instr:
		view:
			label: 'Instruction or Recommendation'

	emergency_num_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	clutter:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['clutter_num_instr', 'clutter_num_resp']
		view:
			control: 'radio'
			label: 'Clutter'

	clutter_num_instr:
		view:
			label: 'Instruction or Recommendation'

	clutter_num_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	rugs:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['rugs_instr', 'rugs_resp']
		view:
			control: 'radio'
			label: 'Throw Rugs'

	rugs_instr:
		view:
			label: 'Instruction or Recommendation'

	rugs_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	exits:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['exits_instr', 'exits_resp']
		view:
			control: 'radio'
			label: 'Clear Exits'

	exits_instr:
		view:
			label: 'Instruction or Recommendation'

	exits_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	abuse:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['abuse_instr', 'abuse_resp']
		view:
			control: 'radio'
			label: 'Suspected patient or child abuse'

	abuse_instr:
		view:
			label: 'Instruction or Recommendation'

	abuse_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	shower:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['shower_instr', 'shower_resp']
		view:
			control: 'radio'
			label: 'Tub or Shower'

	shower_instr:
		view:
			label: 'Instruction or Recommendation'

	shower_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	toilet:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['toilet_instr', 'toilet_resp']
		view:
			control: 'radio'
			label: 'Toilet'

	toilet_instr:
		view:
			label: 'Instruction or Recommendation'

	toilet_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	wc_accessible:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['wc_accessible_instr', 'wc_accessible_resp']
		view:
			control: 'radio'
			label: 'W/C Accessibility'

	wc_accessible_instr:
		view:
			label: 'Instruction or Recommendation'

	wc_accessible_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	bath_bar:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['bath_bar_instr', 'bath_bar_resp']
		view:
			control: 'radio'
			label: 'Bars in Bathroom'

	bath_bar_instr:
		view:
			label: 'Instruction or Recommendation'

	bath_bar_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	bath_mat:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['bath_mat_instr', 'bath_mat_resp']
		view:
			control: 'radio'
			label: 'Non-Slip Mat in Bathroom'

	bath_mat_instr:
		view:
			label: 'Instruction or Recommendation'

	bath_mat_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	exit_plan:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['exit_plan_instr', 'exit_plan_resp']
		view:
			control: 'radio'
			label: 'Emergency Exit Plan'

	exit_plan_instr:
		view:
			label: 'Instruction or Recommendation'

	exit_plan_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	smoking:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['smoking_instr', 'smoking_resp']
		view:
			control: 'radio'
			label: 'Smoking Precautions'

	smoking_instr:
		view:
			label: 'Instruction or Recommendation'

	smoking_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	cords:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['cords_instr', 'cords_resp']
		view:
			control: 'radio'
			label: 'Frayed Cords'

	cords_instr:
		view:
			label: 'Instruction or Recommendation'

	cords_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	proper_use:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['proper_use_instr', 'proper_use_resp']
		view:
			control: 'radio'
			label: 'Safe and proper use of Medical Equipment'

	proper_use_instr:
		view:
			label: 'Instruction or Recommendation'

	proper_use_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	o2_use:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['o2_use_instr', 'o2_use_resp']
		view:
			control: 'radio'
			label: 'Oxygen use and Storage'

	o2_use_instr:
		view:
			label: 'Instruction or Recommendation'

	o2_use_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	backup:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['backup_instr', 'backup_resp']
		view:
			control: 'radio'
			label: 'Back-Up Systems'

	backup_instr:
		view:
			label: 'Instruction or Recommendation'

	backup_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	pump_used:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['pump_used_instr', 'pump_used_resp']
		view:
			control: 'radio'
			label: 'Infusion Pump Used'

	pump_used_instr:
		view:
			label: 'Instruction or Recommendation'

	pump_used_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	fridge:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['fridge_instr', 'fridge_resp']
		view:
			control: 'radio'
			label: 'Adequate refrigeration'

	fridge_instr:
		view:
			label: 'Instruction or Recommendation'

	fridge_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	storage:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['storage_instr', 'storage_resp']
		view:
			control: 'radio'
			label: 'Storage and Labels'

	storage_instr:
		view:
			label: 'Instruction or Recommendation'

	storage_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	schedule:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['schedule_instr', 'schedule_resp']
		view:
			control: 'radio'
			label: 'Medication Schedule'

	schedule_instr:
		view:
			label: 'Instruction or Recommendation'

	schedule_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	precaution:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['precaution_instr', 'precaution_resp']
		view:
			control: 'radio'
			note: 'including hand washing and ABHR'
			label: 'Universal Precautions'

	precaution_instr:
		view:
			label: 'Instruction or Recommendation'

	precaution_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	waste:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['waste_instr', 'waste_resp']
		view:
			control: 'radio'
			label: 'Waste Disposal'

	waste_instr:
		view:
			label: 'Instruction or Recommendation'

	waste_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	sharp_disp:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['sharp_disp_instr', 'sharp_disp_resp']
		view:
			control: 'radio'
			label: 'Sharp Disposal'

	sharp_disp_instr:
		view:
			label: 'Instruction or Recommendation'

	sharp_disp_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	emerg_prep:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['emerg_prep_instr', 'emerg_prep_resp']
		view:
			control: 'radio'
			label: 'Emergency and disaster preparedness'

	emerg_prep_instr:
		view:
			label: 'Instruction or Recommendation'

	emerg_prep_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	alone:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['alone_instr', 'alone_resp']
		view:
			control: 'radio'
			label: 'Leave Alone'

	alone_instr:
		view:
			label: 'Instruction or Recommendation'

	alone_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	wheelchair:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['wheelchair_instr', 'wheelchair_resp']
		view:
			control: 'radio'
			label: 'Wheelchair Bound'

	wheelchair_instr:
		view:
			label: 'Instruction or Recommendation'

	wheelchair_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	bed:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['bed_instr', 'bed_resp']
		view:
			control: 'radio'
			label: 'Bed Bound'

	bed_instr:
		view:
			label: 'Instruction or Recommendation'

	bed_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	insulin:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['insulin_instr', 'insulin_resp']
		view:
			control: 'radio'
			label: 'Insulin dependent diabetic'

	insulin_instr:
		view:
			label: 'Instruction or Recommendation'

	insulin_resp:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Response'

	smoke_dect:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Smoke Detectors:  Date of Last Battery Change, Working order?'

	fire_ext:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Fire Extinguisher:  Did Indicator Read Full?'

	fire_ext_date:
		model:
			type: 'date'
		view:
			label: 'Fire Extinguisher: Expiration Date'

	factor_safety:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['factor_safety_instr']
		view:
			control: 'radio'
			label: 'Factor Safety: Comfy cap, Knee pads, Assistive devices, Joint stabilizers?'

	factor_safety_instr:
		view:
			label: 'Instruction or Recommendation'

	# Post-Assessment
	vendor_name:
		view:
			label: 'Pharmacy / DME Vendor Name'

	vendor_phone:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Pharmacy / DME Vendor Phone'

	equip_working:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'No':
					fields: ['equip_comment', 'equip_notified']
		view:
			control: 'radio'
			label: 'Equipment, apparatus, appliance good working condition'

	equip_comment:
		view:
			label: 'Equipment, apparatus, appliance problem'

	equip_notified:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Vendor Notified of Problem'

	pt_fallen:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['pt_fallen_comment']
		view:
			control: 'radio'
			label: 'Has patient fallen in last 3 months?'

	pt_fallen_comment:
		model:
			max: 4096
		view:
			control: 'area'
			label: 'Patient fall frequency, time of day fall(s) occur, type of injury, etc'

	o2_support:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['o2_support_risk']
		view:
			control: 'radio'
			label: 'Patient on long-term Oxygen Therapy?'

	o2_support_risk:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
		view:
			control: 'radio'
			label: 'Clinical Staff instructed patient/caregiver regarding risks associated with long term oxygen therapy such as home fire?'

	report_instr:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'Yes':
					fields: ['report_instr_comments']
		view:
			control: 'radio'
			label: 'Instructed on how to report concerns about safety'

	report_instr_comments:
		view:
			label: 'Comment on Instructions'

	pt_understands:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Patient/CG verbalized understanding?'

	pt_add_education:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pt_add_education_details']
		view:
			control: 'radio'
			label: 'Further education needed?'

	pt_add_education_details:
		view:
			control: 'area'
			label: 'Further education needed details'

	appropiate:
		model:
			max: 3
			source: ['Yes', 'No', 'N/A']
			if:
				'No':
					fields: ['appropiate_comments']
		view:
			control: 'radio'
			label: 'Home Appropriate for home health care'

	appropiate_comments:
		view:
			label: "Why isn't home appropriate?"

	overall_comments:
		view:
			label: 'Overall Comments on Assessment'
	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		checklist_safety:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['careplan_id', 'order_id', 'therapy_1']
	sections_group: [
		'Patient Safety Assessment':
			sections: [
				'Contact Date/Time':
					fields: ['contact_date', 'contact_time']
				'Discussed With Patient or Caregiver':
					note: 'Mark Yes if the material has been reviewed with the patient'
					fields: ['therapy_1', 'therapy_2', 'therapy_3', 'therapy_4', 'therapy_5',
						'clear_pathways', 'clear_pathways_instr', 'clear_pathways_resp',
						'floors', 'floors_instr', 'floors_resp',
						'furniture', 'furniture_instr', 'furniture_resp',
						'lighting', 'lighting_instr', 'lighting_resp',
						'stairs', 'stairs_instr', 'stairs_resp',
						'phone', 'phone_instr', 'phone_resp',
						'emergency_num', 'emergency_num_instr', 'emergency_num_resp',
						'clutter', 'clutter_num_instr', 'clutter_num_resp',
						'rugs', 'rugs_instr', 'rugs_resp',
						'exits', 'exits_instr', 'exits_resp',
						'abuse', 'abuse_instr', 'abuse_resp',
						'shower', 'shower_instr', 'shower_resp',
						'toilet', 'toilet_instr', 'toilet_resp',
						'wc_accessible', 'wc_accessible_instr', 'wc_accessible_resp',
						'bath_bar', 'bath_bar_instr', 'bath_bar_resp',
						'bath_mat', 'bath_mat_instr', 'bath_mat_resp',
						'exit_plan', 'exit_plan_instr', 'exit_plan_resp',
						'smoking', 'smoking_instr', 'smoking_resp',
						'cords', 'cords_instr', 'cords_resp',
						'proper_use', 'proper_use_instr', 'proper_use_resp',
						'o2_use', 'o2_use_instr', 'o2_use_resp',
						'backup', 'backup_instr', 'backup_resp',
						'pump_used', 'pump_used_instr', 'pump_used_resp',
						'fridge', 'fridge_instr', 'fridge_resp',
						'storage', 'storage_instr', 'storage_resp',
						'schedule', 'schedule_instr', 'schedule_resp',
						'precaution', 'precaution_instr',
						'precaution_resp',
						'waste', 'waste_instr', 'waste_resp',
						'sharp_disp', 'sharp_disp_instr', 'sharp_disp_resp',
						'emerg_prep', 'emerg_prep_instr', 'emerg_prep_resp',
						'alone', 'alone_instr', 'alone_resp',
						'wheelchair', 'wheelchair_instr', 'wheelchair_resp',
						'bed', 'bed_instr', 'bed_resp',
						'insulin', 'insulin_instr', 'insulin_resp',
						'smoke_dect', 'fire_ext', 'fire_ext_date']
					prefill: 'checklist_safety'
				'Hemophilia Safety':
					fields: ['factor_safety', 'factor_safety_instr']
				'Patient Assessment':
					fields: ['vendor_name', 'vendor_phone',
							 'report_instr', 'report_instr_comments',
							 'equip_working', 'equip_comment',
							 'equip_notified', 'pt_fallen',
							 'pt_fallen_comment', 'o2_support',
							 'o2_support_risk',]
					prefill: 'checklist_safety'
				'Post-Assessment':
					fields: ['pt_understands', 'pt_add_education', 'pt_add_education_details',  'appropiate',
							 'appropiate_comments', 'overall_comments']
					prefill: 'checklist_safety'
			]
	]

	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Patient Safety Assessment"
	]

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan > Checklist > Safety'
	grid:
		fields: ['created_on', 'vendor_name', 'overall_comments']
		sort: ['created_on']
	label: 'Patient Safety Assessment'
	open: 'read'
