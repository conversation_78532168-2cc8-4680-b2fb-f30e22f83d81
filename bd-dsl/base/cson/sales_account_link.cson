fields:
	type:
		model:
			source:
				candidate_contact: 'Candidate / Contact',
				candidate_physician: 'Candidate / Physician',
				candidate_organization: 'Candidate / Organization',
				contact_physician: 'Contcat / Physician',
				contact_organization: 'Contact / Organization',
				physician_organization: 'Physician / Organization',
				organization_suborganization: 'Organization / Sub Organization'
			required: true
			if:
				'candidate_contact':
					fields: ['entity_patient_id','entity_contact_id', 'relation']
				'candidate_physician':
					fields: ['entity_patient_id', 'entity_physician_id']
				'candidate_organization':
					fields: ['entity_patient_id', 'entity_organization_id']
				'contact_physician':
					fields:	['entity_contact_id', 'entity_physician_id', 'relation']
				'contact_organization':
					fields:	['entity_contact_id', 'entity_organization_id', 'relation']
				'physician_organization':
					fields: ['entity_physician_id', 'entity_organization_id', 'relation']
				'organization_suborganization':
					fields: ['entity_organization_id', 'entity_sub_organization_id']
		view:
			label: 'Type'
			readonly: true
			columns: 2

	entity_patient_id:
		model:
			source: 'sales_account'
			sourcefilter:
				type:
					'static': 'Candidate'
		view:
			label: 'Candidate'
			columns: 2

	entity_contact_id:
		model:
			source: 'sales_account'
			sourcefilter:
				type:
					'static': 'Contact'
		view:
			label: 'Contact'
			columns: 2

	entity_physician_id:
		model:
			source: 'sales_account'
			sourcefilter:
				type:
					'static': 'Physician'
		view:
			label: 'Physician'
			columns: 2

	entity_organization_id:
		model:
			source: 'sales_account'
			sourcefilter:
				type:
					'static': 'Organization'
		view:
			label: 'Organization'
			columns: 2

	entity_sub_organization_id:
		model:
			source: 'sales_account'
			sourcefilter:
				type:
					'static': 'Organization'
		view:
			label: 'Sub Organization'
			columns: 2

	relation:
		view:
			label: 'Relation'
			note: 'E.g. Spouse, Son, Co-Worker'
			columns: 2

	priority:
		view:
			label: 'Priority'
			note: 'E.g. Emergency, Primary, Secondary'
			columns: 2

	comment:
		view:
			label: 'Comment'
			control: 'area'

model:
	access:
		create:     []
		create_all: ['admin', 'liaison']
		delete:     ['admin', 'liaison']
		read:       ['admin', 'liaison']
		read_all:   ['admin', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'liaison']
		write:      ['admin', 'liaison']
	indexes:
		unique: [
			['entity_patient_id', 'entity_contact_id', 'entity_physician_id', 'entity_organization_id', 'entity_sub_organization_id']
		]
	name: ['type', 'relation']
	sections:
        'Account Links':
            fields: ['type', 'entity_patient_id', 'entity_contact_id', 'entity_physician_id', 'entity_organization_id','entity_sub_organization_id', 'relation', 'priority', 'comment']

view:
	grid:
		fields: ['entity_patient_id']
	label: 'Account Links'