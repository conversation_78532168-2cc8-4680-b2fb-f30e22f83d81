fields:

	code:
		model:
			required: true
		view:
			label: 'Code'
			columns: 2

	name:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		unique: [
			['name']
		]
		many: [
			['code']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name', 'code']

view:
	comment: 'Manage > Surescripts Service Level' 
	find:
		basic: ['name']
	grid:
		fields: ['name']
		sort: ['name']
	label: 'Surescripts Service Level'
	open: 'read'
