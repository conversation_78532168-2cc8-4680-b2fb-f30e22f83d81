fields:

	type:
		model:
			source: ['HTC', 'Nursing Home', 'Other']
		view:
			control: 'radio'
			label: 'Type'

	name:
		model:
			max: 64
			required: true
		view:
			label: 'Name'

	address:
		model:
			max: 128
			required: true
		view:
			columns: 'addr_1'
			label: 'Address'
			class: "api_prefill"
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'address': ['addressLabel']
					'city': ['city']
					'state_id': ['stateCode']
					'zip': ['postalCode']
			]

	city:
		model:
			max: 64
			required: true
		view:
			label: 'City'
			columns: 'addr_city'
	state_id:
		model:
			max: 2
			min: 2
			required: true
			source: 'list_us_state'
			sourceid: 'code'
		view:
			label: 'State'
			columns: 'addr_state'
	zip:
		model:
			max: 10
			min: 5
			required: true
		view:
			format: 'us_zip'
			label: 'Zip'
			columns: 'addr_zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'zip'
						city:'city'
						state_id:'state_id'
			]

	phone:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Phone #'

	fax:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Fax #'

	contact:
		model:
			type: 'json'
			subfields:
				name:
					label: 'Name'
					type: 'text'
				primary:
					label: 'Primary POC'
					source: ['Yes', 'No']
					type: 'text'
				role:
					label: 'Role'
					type: 'text'
				work:
					label: 'Phone'
					type: 'text'
				email:
					label: 'Email'
					type: 'text'
				comment:
					label: 'Comment'
					type: 'text'
		view:
			control: 'grid'
			label: 'Contacts'

	notes:
		view:
			control: 'area'
			label: 'Notes'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ['location']
	indexes:
		unique: [
			['name', 'address', 'city', 'state_id', 'zip']
		]
	name: '{name} {address} {city}, {state_id} {zip}'
	sections:
		'Main':
			fields: ['type', 'name', 'address', 'city', 'state_id', 'zip', 'phone', 'fax']
		'Contacts':
			fields: ['contact']
		'Notes':
			fields: ['notes']

view:
	hide_cardmenu: true
	comment: 'Manage > Facility'
	find:
		basic: ['name']
		advanced: ['address', 'city', 'state_id', 'zip', 'type']
	grid:
		fields: ['type', 'name', 'address', 'city', 'state_id', 'zip', 'phone', 'fax']
		sort: ['name']
	label: 'Facility'
