fields:

	patient_id:
		model:
			type: 'int'
			source: 'patient'
			if:
				'>0':
					fields: ['insurance_id']
					readonly:
						fields: ['payer_id']
				'*':
					require_fields: ['payer_id']
		view:
			label: 'Patient ID'
			readonly: true
			offscreen: true

	direction:
		model:
			prefill: ['parent.direction']
			source:
				'IN': 'Inbox'
				'OUT': 'Outbox'
			if:
				'IN':
					readonly:
						fields: ['payer_id', 'payer_type_id', 'payer_level', 'pbm_participant_id',
						'person_code', 'group_id', 'group_name', 'pbm_member_id', 'relationship_code',
						'cardholder_id', 'cardholder_first_name', 'cardholder_last_name', 'payer_phone']
					fields: ['prohibit_renewal_request']
				'OUT':
					fields: ['patient_id']
		view:
			label: 'Direction'
			readonly: true
			offscreen: true

	#Body.<MessageType>.BenefitsCoordination.PayerType
	payer_type_id:
		model:
			source: 'list_ss_payer_type'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Payer Type'

	#Body.<MessageType>.BenefitsCoordination.PayerResponsibilityCode
	payer_level:
		model:
			source:
				'P': 'Primary'
				'S': 'Secondary'
				'T': 'Tertiary'
				'U': 'Unknown'
				'PP': 'Private Pay'
		view:
			columns: 3
			label: 'Payer Level'

	insurance_id:
		model:
			source: 'patient_insurance'
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
				type_id:
					'static': '!SELF'
				billing_method_id:
					'static': ['mm', 'cms1500', 'ncpdp']
		view:
			columns: 3
			control: 'select'
			label: 'Patient Insurance'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_insurance/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'payer_id': ['payer_id'],
					'bin': ['bin'],
					'pcn': ['pcn'],
					'group_id': ['group_number'],
					'person_code': ['person_code'],
					'cardholder_id': ['cardholder_id'],
					'beneficiary_fname': ['cardholder_first_name'],
					'beneficiary_lname': ['cardholder_last_name']
					'relationship_code': ['pharmacy_relationship_id']
			]

	payer_id:
		model:
			source: 'payer'
		view:
			columns: 2
			label: 'Payer'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/payor/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				overwrite_false: ['bin', 'pcn']
				fields:
					'payer_name': ['organization'],
					'bin': ['bin'],
					'pcn': ['pcn'],
					'naic_id': ['naic_id'],
					'hp_id': ['hp_id'],
					'payer_phone': ['phone'],
					'payer_fax': ['fax']
			]

	#Body.<MessageType>.BenefitsCoordination.PayerName
	payer_name:
		view:
			columns: 2
			label: 'Payer Name'
			readonly: true

	#Body.<MessageType>.BenefitsCoordination.PayerIdentification.PayerID
	pbm_participant_id:
		view:
			columns: 3
			label: 'PBM Participant ID'

	#Body.<MessageType>.BenefitsCoordination.PayerIdentification.IINNumber
	bin:
		model:
			type: 'int'
		view:
			columns: 3
			label: 'BIN'
			readonly: true

	#Body.<MessageType>.BenefitsCoordination.PayerIdentification.ProcessorIdentificationNumber
	pcn:
		view:
			columns: 3
			label: 'PCN'
			readonly: true

	#Body.<MessageType>.BenefitsCoordination.PayerIdentification.NAICCode
	naic_id:
		view:
			columns: 3
			label: 'National Association of Insurance Commissioners (NAID)'
			readonly: true

	#Body.<MessageType>.BenefitsCoordination.PayerIdentification.StandardUniqueHealthPlanIdentifier
	hp_id:
		view:
			columns: 3
			label: 'Health Plan Identifier (HPID)'
			readonly: true

	#Body.<MessageType>.BenefitsCoordination.PersonCode
	person_code:
		view:
			columns: 3
			label: 'Person Code'

	#Body.<MessageType>.BenefitsCoordination.GroupID
	group_id:
		view:
			columns: 3
			label: 'Group Number'

	#Body.<MessageType>.BenefitsCoordination.GroupName
	group_name:
		view:
			columns: 3
			label: 'Group Name'

	#Body.<MessageType>.BenefitsCoordination.PBMMemberID
	pbm_member_id:
		view:
			columns: 3
			label: 'PBM Member ID'

	#Body.<MessageType>.BenefitsCoordination.PatientRelationshipCode
	relationship_code:
		model:
			default: '1'
			source:
				'1': 'Cardholder'
				'2': 'Spouse'
				'3': 'Child'
				'4': 'Other'
		view:
			control: 'radio'
			label: 'Relationship Code'

	#Body.<MessageType>.BenefitsCoordination.CardholderID
	cardholder_id:
		view:
			columns: 3
			label: 'Cardholder ID'

	#Body.<MessageType>.BenefitsCoordination.CardHolderName.FirstName
	cardholder_first_name:
		model:
			prefill: ['parent.patient_first_name']
		view:
			columns: 3
			label: 'Cardholder First Name'

	#Body.<MessageType>.BenefitsCoordination.CardHolderName.LastName
	cardholder_last_name:
		model:
			prefill: ['parent.patient_last_name']
		view:
			columns: 3
			label: 'Cardholder Last Name'

	#Body.<MessageType>.BenefitsCoordination.CommunicationNumbers.PrimaryTelephone.Number
	payer_phone:
		model:
			max: 21
		view:
			columns: 2
			format: 'us_phone'
			label: 'Fax'

	#Body.<MessageType>.BenefitsCoordination.CommunicationNumbers.Fax.Number
	payer_fax:
		model:
			max: 21
		view:
			columns: 2
			format: 'us_phone'
			label: 'Fax'

	#Body.<MessageType>.BenefitsCoordination.ProhibitRenewalRequest
	prohibit_renewal_request:
		model:
			source:
				'false': 'No'
				'true': 'Yes'
		view:
			control: 'radio'
			label: 'Prohibit Renewal Request?'
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: []
		write:      []
	name: ['payer_name', 'bin', 'pcn', 'group_id']
	indexes:
		many: [
			['payer_type_id']
			['bin']
			['pcn']
			['naic_id']
			['hp_id']
			['group_id']
			['pbm_member_id']
			['cardholder_id']
		]
	sections:
		'Benefits Coordination':
			fields: ['patient_id', 'direction', 'payer_type_id', 'payer_level', 'payer_id', 'payer_name', 'pbm_participant_id', 'bin',
			'pcn', 'naic_id', 'hp_id', 'person_code', 'group_id', 'group_name', 'pbm_member_id', 'relationship_code', 'cardholder_id',
			'cardholder_first_name', 'cardholder_last_name', 'payer_phone', 'payer_fax', 'prohibit_renewal_request']
view:
	hide_cardmenu: true
	comment: 'SureScripts Benefits Coordination'
	grid:
		fields: ['payer_type_id', 'payer_name', 'pbm_member_id', 'bin', 'pcn', 'cardholder_id', 'group_id']
		sort: ['-created_on']
	label: 'Benefits Coordination'
	open: 'read'
