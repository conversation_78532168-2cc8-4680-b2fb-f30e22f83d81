fields:

	name:
		model:
			search: 'B'
			required: true
		view:
			columns: 3
			label: 'Job Name'

	code:
		model:
			search: 'A'
			required: true
		view:
			columns: 3
			label: 'Job Code'

	path:
		model:
			search: 'B'
			type: 'text'
		view:
			columns: 3
			label: 'NES API Path'
			note: 'No ?params. Use Parameters grid instead.'

	description:
		model:
			search: 'C'
		view:
			columns: 1
			control: 'area'
			label: 'Description'

	schedule:
		model:
			required: true
		view:
			columns: 3
			label: 'Schedule (cron expression)'
			note: 'See: https://www.uptimia.com/cron/every-3rd-monday'

	retry_limit:
		model:
			type: 'int'
			default: 3
		view:
			columns: 3
			label: 'Retry attempts'

	retry_delay:
		model:
			type: 'int'
			default: 60
		view:
			columns: 3
			label: 'Retry delay (seconds)'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 3
			control: 'radio'
			label: 'Can Sync Record'

	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 3
			control: 'radio'
			label: 'Active'
			note: 'Selecting No will stop this job from running'

	params:
		model:
			type: 'json'
			subfields:
				job_key:
					label: 'Key'
					style:
						width: '20%'
				job_value:
					label: 'Value'
					style:
						width: '40%'
				job_note:
					label: 'Notes'
					style:
						width: '40%'
		view:
			control: 'grid'
			label: 'Parameters'
			note: 'This will be in the POST request'

	last_run_results:
		model:
			search: 'C'
		view:
			control: 'area'
			label: 'Last Run Results'
			readonly: true

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	indexes:
		unique: [
			['code']
		]
	name: '{name}'
	sync_mode: 'mixed'
	sections:
		'Job':
			fields: ['name', 'code', 'path', 'description']
		'Schedule':
			fields: ['schedule', 'retry_limit', 'retry_delay', 'allow_sync', 'active']
		'Parameters':
			fields: ['params']
		'Run History':
			fields: ['last_run_results']

view:
	comment: 'Workflow Job'
	grid:
		fields: ['name', 'code', 'description', 'allow_sync', 'active']
		sort: ['name', 'code']
	label: 'Workflow Job'
	open: 'read'
