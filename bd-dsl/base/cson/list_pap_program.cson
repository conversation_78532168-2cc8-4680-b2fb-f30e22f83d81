fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	name:
		model:
			max: 64
			required: true
		view:
			columns: 2
			label: 'Program Name'
			findunique: true

	bname_id:
		model:
			required: true
			multi: true
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Linked Brand Names'

	phone:
		model:
			max: 21
		view:
			columns: 2
			format: 'us_phone'
			label: 'Phone #'

	fax:
		model:
			max: 21
		view:
			columns: 2
			format: 'us_phone'
			label: 'Fax #'

	threshold:
		model:
			type: 'decimal'
			rounding: 0.01
			if:
				'*':
					fields: ['threshold_size']
		view:
			columns: 2
			class: 'numeral money'
			format: '$0,0.00'
			note: 'if any'
			label: 'Income Threshold ($)'

	threshold_size:
		model:
			type: 'decimal'
			rounding: 1
			required: true
		view:
			columns: 2
			label: 'Threshold household size'

	funding_threshold:
		model:
			type: 'decimal'
			rounding: 0.01
			if:
				'*':
					fields: ['funding_threshold_type']
		view:
			columns: 2
			class: 'numeral money'
			format: '$0,0.00'
			note: 'if any'
			label: 'Funding Threshold ($)'

	funding_threshold_type:
		model:
			required: false
			source: ['Per Fill', 'Per Year']
		view:
			columns: 2
			control: 'radio'
			label: 'Funding Threshold Type'

	url:
		view:
			label: 'Reference URL'
			format: 'url'

	days_prior:
		model:
			type: 'int'
			default: 60
		view:
			columns: 2
			label: 'Days Prior to Start Renewal'

	renewal_auto:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Auto-Renewal?'

	reenrollment_process:
		view:
			columns: 2
			control: 'area'
			label: 'Re-enrollment process'

	active:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			columns: 2
			class: 'checkbox-only'
			control: 'checkbox'
			label: 'Active?'
			findfilter: 'Yes'

	notes:
		view:
			control: 'area'
			label: 'Notes'

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'list_pap_program'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					assigned_to: 'PAP Program'
					pap_program_id: '{code}'
					direct_attachment: 'Yes'
					form_code: '{code}'
					form_name: 'list_pap_program'
					source: 'Scanned Document'
					form_filter: 'list_pap_program'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'payor', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'payor', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['assistance']
	indexes:
		many: [
			['name']
			['bname_id']
		]
		unique: [
			['name']
			['code']
		]
	name: '{name} {phone}'
	sections_group: [
		'Program Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'bname_id', 'phone', 'fax', 'threshold',
			'threshold_size', 'funding_threshold', 'funding_threshold_type',
			'days_prior', 'renewal_auto', 'reenrollment_process', 'active', 'url', 'notes']
		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'
	]

view:
	comment: 'Manage > Manufacturer Assistance Program'
	find:
		basic: ['name', 'bname_id']
	grid:
		fields: ['name', 'bname_id', 'phone', 'fax', 'days_prior']
		sort: ['name']
	label: 'Manufacturer Assistance Program'
