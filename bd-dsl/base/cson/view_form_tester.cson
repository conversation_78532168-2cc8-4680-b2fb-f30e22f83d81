fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	form_name:
		model:
			required: true
			source:
				assessment: "Initial Assessment"
				ongoing: "Ongoing Assessment"
				encounter: "Nursing Encounter"
				assessment_nurse: "Nurse Assessment"
			if:
				'assessment':
					fields: ['override_order_settings_other']
				'ongoing':
					fields: ['override_order_settings_other']
				'encounter':
					fields: ['override_order_settings_other']
				'assessment_nurse':
					fields: ['override_nurseasmt_settings_nurse']
		view:
			control: 'select'
			label: 'Form Name'
			columns: 2

	order_id:
		model:
			required: true
			source: 'careplan_order'
			type: 'int'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status_id:
					'static': ['1', '5']
		view:
			note: 'Must be active or pending'
			label: 'Referral'
			columns: 2
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/careplan_order/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'therapy_1': ['therapy_id'],
			]

	override_order_settings_other:
		model:
			required: true
			default: "Yes"
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['therapy_1', 'disease_1', 'brand_1', 'clinical_1', 'route_id']
		view:
			control: 'radio'
			label: 'Override Order Settings'
			columns: 2

	override_nurseasmt_settings_nurse:
		model:
			required: true
			default: "Yes"
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['therapy_1', 'disease_1', 'brand_1']
		view:
			control: 'radio'
			label: 'Override Order Settings'
			columns: 2

	route_id:
		model:
			required: false
			source: 'list_route'
			sourceid: 'code'
		view:
			label: 'Primary Drug Route'
			columns: -4

	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'*':
					fields: ['therapy_2']
		view:
			label: 'Therapy 1'
			columns: 4
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['therapy_1', 'therapy_2', 'therapy_3', 'therapy_4', 'therapy_5']
					error: "All therapies must be unique"
				}
			]

	therapy_2:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'*':
					fields: ['therapy_3']
		view:
			label: 'Therapy 2'
			columns: 4
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['therapy_1', 'therapy_2', 'therapy_3', 'therapy_4', 'therapy_5']
					error: "All therapies must be unique"
				}
			]

	therapy_3:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'*':
					fields: ['therapy_4']
		view:
			label: 'Therapy 3'
			columns: 4
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['therapy_1', 'therapy_2', 'therapy_3', 'therapy_4', 'therapy_5']
					error: "All therapies must be unique"
				}
			]

	therapy_4:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'*':
					fields: ['therapy_5']
		view:
			label: 'Therapy 4'
			columns: 4
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['therapy_1', 'therapy_2', 'therapy_3', 'therapy_4', 'therapy_5']
					error: "All therapies must be unique"
				}
			]

	therapy_5:
		model:
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Therapy 5'
			columns: 4
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['therapy_1', 'therapy_2', 'therapy_3', 'therapy_4', 'therapy_5']
					error: "All therapies must be unique"
				}
			]
	
	brand_1:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['brand_2']
		view:
			label: 'Primary Brand'
			columns: 4
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['brand_1', 'brand_2', 'brand_3', 'brand_4', 'brand_5']
					error: "All brands must be unique"
				}
			]

	brand_2:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['brand_3']
		view:
			label: 'Secondary Brand'
			columns: 4
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['brand_1', 'brand_2', 'brand_3', 'brand_4', 'brand_5']
					error: "All brands must be unique"
				}
			]

	brand_3:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['brand_4']
		view:
			label: 'Tertiary Brand'
			columns: 4
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['brand_1', 'brand_2', 'brand_3', 'brand_4', 'brand_5']
					error: "All brands must be unique"
				}
			]

	brand_4:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['brand_5']
		view:
			label: 'Quaternary Brand'
			columns: 4
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['brand_1', 'brand_2', 'brand_3', 'brand_4', 'brand_5']
					error: "All brands must be unique"
				}
			]

	brand_5:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			label: 'Quinary Brand'
			columns: 4
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['brand_1', 'brand_2', 'brand_3', 'brand_4', 'brand_5']
					error: "All brands must be unique"
				}
			]

	clinical_1:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['clinical_2']
		view:
			label: 'Primary Clinical Assessment'
			columns: 2
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['clinical_1', 'clinical_2', 'clinical_3', 'clinical_4', 'clinical_5']
					error: "All clinical assessments must be unique"
				}
			]

	clinical_2:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['clinical_3']
		view:
			label: 'Secondary Clinical Assessment'
			columns: 2
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['clinical_1', 'clinical_2', 'clinical_3', 'clinical_4', 'clinical_5']
					error: "All clinical assessments must be unique"
				}
			]

	clinical_3:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['clinical_4']
		view:
			label: 'Tertiary Clinical Assessment'
			columns: 2
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['clinical_1', 'clinical_2', 'clinical_3', 'clinical_4', 'clinical_5']
					error: "All clinical assessments must be unique"
				}
			]

	clinical_4:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['clinical_5']
		view:
			label: 'Quaternary Clinical Assessment'
			columns: 2
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['clinical_1', 'clinical_2', 'clinical_3', 'clinical_4', 'clinical_5']
					error: "All clinical assessments must be unique"
				}
			]

	clinical_5:
		model:
			source: 'list_clinical_asmt'
			sourceid: 'code'
		view:
			label: 'Quinary Clinical Assessment'
			columns: 2
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['clinical_1', 'clinical_2', 'clinical_3', 'clinical_4', 'clinical_5']
					error: "All clinical assessments must be unique"
				}
			]

	disease_1:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['disease_2']
		view:
			label: 'Primary Disease'
			columns: 4
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['disease_1', 'disease_2', 'disease_3', 'disease_4', 'disease_5']
					error: "All diseases must be unique"
				}
			]

	disease_2:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['disease_3']
		view:
			label: 'Secondary Disease'
			columns: 4
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['disease_1', 'disease_2', 'disease_3', 'disease_4', 'disease_5']
					error: "All diseases must be unique"
				}
			]

	disease_3:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['disease_4']
		view:
			label: 'Tertiary Disease'
			columns: 4
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['disease_1', 'disease_2', 'disease_3', 'disease_4', 'disease_5']
					error: "All diseases must be unique"
				}
			]

	disease_4:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'*':
					fields: ['disease_5']
		view:
			label: 'Quaternary Disease'
			columns: 4
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['disease_1', 'disease_2', 'disease_3', 'disease_4', 'disease_5']
					error: "All diseases must be unique"
				}
			]

	disease_5:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
		view:
			label: 'Quinary Disease'
			columns: 4
			validate: [
				{
					name: 'UniqueValidator'
					fields: ['disease_1', 'disease_2', 'disease_3', 'disease_4', 'disease_5']
					error: "All diseases must be unique"
				}
			]
model:
	save: false
	access:
		create:     []
		create_all: ['admin','pharm', 'cm']
		delete:     []
		read:       ['admin','pharm', 'cm']
		read_all:   ['admin','pharm', 'cm']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      ['admin','pharm', 'cm']
	prefill:
		patient:
			link:
				id: 'patient_id'
	name: ['patient_id']
	sections:
		"Form Tester":
			fields: ['patient_id', 'order_id','form_name', 'override_order_settings_other', 'override_nurseasmt_settings_nurse',
			'route_id', 'therapy_1', 'therapy_2', 'therapy_3', 'therapy_4', 'therapy_5',
			'brand_1', 'brand_2', 'brand_3', 'brand_4', 'brand_5',
			'disease_1', 'disease_2', 'disease_3', 'disease_4', 'disease_5',
			'clinical_1', 'clinical_2', 'clinical_3', 'clinical_4', 'clinical_5']

view:
	comment: 'Workflow Form Tester'
	grid:
		fields: ['patient_id', 'order_id', 'therapy_1', 'brand_1', 'clinical_1']
		sort: ['-id']
	label: 'Workflow Form Tester'
	open: 'edit'