fields:
    patient_id:
        model:
            type: 'int'
            required: false
            source: 'patient'
        view:
            label: 'Patient'
            readonly: true
            offscreen: true

    insurance_id:
        model:
            type: 'int'
            required: false
            source: 'patient_insurance'
        view:
            label: 'Insurance'
            readonly: true
            offscreen: true

    pa_id:
        model:
            source: 'patient_prior_auth'
            sourcefilter:
                patient_id:
                    'dynamic': '{patient_id}'
                status:
                    'static': '5'
                insurance_id:
                    'dynamic': '{insurance_id}'
        view:
            columns: 2
            label: 'Prior Auth'
            class: 'select_prefill'
            transform: [
                name: 'SelectPrefill'
                url: '/form/patient_prior_auth/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
                fields:
                    'prior_authorization_number': ['number']
            ]

    prior_authorization_or_referral_number:
        model:
            required: true
            max: 50
            min: 1
        view:
            columns: 2
            label: 'Prior Auth #/Ref #'
            reference: 'REF02 REF01=G1'
            _meta:
                location: '2400 REF'
                field: '02'
                code: 'G1'
                path: 'claimInformation.serviceLines[{idx1-50}].serviceLineReferenceInformation.priorAuthorization[{idx1-5}].priorAuthorizationOrReferralNumber'

model:
    access:
        create:     []
        create_all: ['admin', 'csr', 'pharm']
        delete:     ['admin', 'pharm']
        read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
        read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
        request:    []
        update:     []
        update_all: ['admin', 'csr', 'pharm']
        write:      ['admin', 'csr', 'pharm']
    name:['patient_id', 'pa_id', 'prior_authorization_or_referral_number']
    sections:
        'Prior Authorization':
            hide_header: true
            fields: ['patient_id', 'insurance_id', 'pa_id', 'prior_authorization_or_referral_number']

view:
    dimensions:
        width: '85%'
        height: '55%'
    hide_cardmenu: true
    reference: '2400'
    comment: 'Service Line Prior Authorization'
    grid:
        fields: ['prior_authorization_or_referral_number']
        sort: ['-created_on']
    label: 'Service Line Prior Authorization'
    open: 'read'