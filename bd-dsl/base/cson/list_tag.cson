fields:
	name:
		model:
			max: 128
			required: true
		view:
			label: 'Tag Name'
			findunique: true
			columns: 2

	color:
		model:
			max: 16
			required: true
		view:
			label: 'Color (HEX)'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ['lists']
	indexes:
		unique: [
			['name']
		]
	name: ['name']
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['name', 'color']

view:
	comment: 'Manage > Patient Tag'
	find:
		basic: ['name']
	grid:
		fields: ['name']
		sort: ['name']
	label: 'Patient Tag'
