fields:

	facility_name:
		model:
			max: 30
		view:
			columns: 3
			reference: '385-3Q'
			note: '385-3Q'
			label: 'Facility Name'

	facility_id:
		model:
			max: 10
		view:
			columns: 3
			reference: '336-8C'
			note: '336-8C'
			label: 'Facility ID'

	facility_street_address:
		model:
			max: 30
		view:
			reference: '386-3U'
			note: '386-3U'
			label: 'Address'
			class: "api_prefill"
			columns: 'addr_1'
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'facility_street_address': ['addressLabel']
					'facility_city_address': ['city']
					'facility_state_province_address': ['stateCode']
					'facility_zip_postal_zone': ['postalCode']
			]

	facility_city_address:
		model:
			max: 20
		view:
			columns: 'addr_city'
			reference: '388-5J'
			note: '388-5J'
			label: 'City'

	facility_state_province_address:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			reference: '387-3V'
			note: '387-3V'
			label: 'State'

	facility_zip_postal_zone:
		model:
			max: 10
			min: 5
		view:
			columns: 'addr_zip'
			reference: '389-6D'
			note: '389-6D'
			format: 'us_zip'
			label: 'Zip'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']

	name: ['facility_name', 'facility_id']
	sections:
		'Facility':
			hide_header: true
			indent: false
			fields: ['facility_name', 'facility_id', 'facility_street_address', 'facility_city_address',
			'facility_state_province_address', 'facility_zip_postal_zone']

view:
	dimensions:
		width: '65%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Facility'
	grid:
		fields: ['facility_id', 'facility_name', 'facility_city_address', 'facility_state_province_address']
		width: [10, 30, 30, 30]
		sort: ['-created_on']
	label: 'Facility'
	open: 'read'