#TABLE: RNDCINH0_NDC_INACTV_LINK
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#HIC_SEQN
	hic_seqn:
		model:
			type: 'int'
		view:
			label: 'Hierarchical Ingredient Code Sequence Number (Stable ID)'
			readonly: true
			columns: 2

	#NDC
	ndc:
		view:
			label: 'National Drug Code'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{hic_seqn} {ndc}"
	indexes:
		many: [
			['ndc']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['hic_seqn', 'ndc']

view:
	comment: 'Manage > List FDB NDC/HIC_SEQN Inactive Ingredient Relation Table'
	find:
		basic: ['hic_seqn', 'ndc']
	grid:
		fields: ['hic_seqn', 'ndc']
	label: 'List FDB NDC/HIC_SEQN Inactive Ingredient Relation Table'
