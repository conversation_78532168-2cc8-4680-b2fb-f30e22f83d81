fields:

	code:
		model:
			required: true
		view:
			label: 'Code'

	dsl_form:
		view:
			label: 'DSL Form'
	
	type:
		view:
			label: 'Node Type'

	wizard_table_column:
		view:
			label: 'Wizard Table Column'
	
	wizard_query_param:
		view:
			label: 'Wizard Query Param'
	
	dsl_form_section:
		view:
			label: 'DSL Form Section'

	form_id:
		model:
			type: 'int'
		view:
			label: 'Form'
	
	form_data:
		model:
			type: 'json'
		view:
			offscreen: true
			readonly: true
			label: 'Form Data'

	description:
		view:
			label: 'Description'

	label:
		view:
			label: 'Label'

   
model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	name: '{wizard_code} - {patient_id}'
	sections:
		'Main':
			fields: ['code', 'dsl_form', 'description', 'label', 'form_id']

view:
	comment: 'Worflow Wizard Step'
	find:
		basic: ['code', 'dsl_form', 'description', 'label', 'form_id']
	grid:
		fields: ['code', 'dsl_form', 'description', 'label', 'form_id']
	label: 'Worflow Wizard Step'
	open: 'read'
