fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Demographics
	gender:
		model:
			if:
				'Female':
					fields: ['are_preg']
			prefill: ['patient']
		view:
			label: 'Sex'
			offscreen: true
			readonly: true


	# Prefill
	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Primary Therapy'

	therapy_2:
		model:
			source: 'list_therapy'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Therapy'

	therapy_3:
		model:
			source: 'list_therapy'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Therapy'

	therapy_4:
		model:
			source: 'list_therapy'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Therapy'

	therapy_5:
		model:
			source: 'list_therapy'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Therapy'

	brand_1:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Primary Brand'

	brand_2:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Brand'

	brand_3:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Brand'

	brand_4:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Brand'

	brand_5:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Brand'

	disease_1:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'ms':
					fields: ['exacerbation']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Disease'

	disease_2:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'ms':
					fields: ['exacerbation']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Disease'

	disease_3:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'ms':
					fields: ['exacerbation']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Disease'

	disease_4:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'ms':
					fields: ['exacerbation']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Disease'

	disease_5:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'ms':
					fields: ['exacerbation']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Disease'

	contact_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 3
			label: 'Assessment Date'
			template: '{{now}}'

	contact_time:
		model:
			type: 'time'
			required: true
		view:
			columns: 3
			label: 'Time'
			template: '{{now}}'

	measurement_log:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				form: 'patient_measurement_log'
				selectable: false
			grid:
				edit: false
				add: 'inline'
				rank: 'none'
				fields: ['created_by', 'date', 'height', 'weight']
				width: [40, 20, 20, 20]
			label: 'Measurement Log'
			readonly: true

	patient_medications:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': ['Active', 'Pending']
		view:
			embed:
				form: 'patient_medication'
				selectable: false
				add_preset:
					reported_by: 'Nurse'
					status: 'Active'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				rank: 'none'
				fields: ['fdb_id', 'medication_dose', 'medication_frequency', 'start_date']
				label: ['Medication', 'Dose', 'Frequency', 'Start Dt']
				width: [35, 25, 25, 15]
			label: 'Active/Pending Medications'
			readonly: true

	patient_interaction_btn:
		model:
			source: ['Get Interactions']
		view:
			columns: 2
			class: 'dsl-button'
			control: 'checkbox'
			label: 'Patient Interaction'
			validate: [
				name: 'DrugAllergyInteraction'
			]

	patient_interactions:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			readonly: true
			embed:
				form: 'interactions'
				selectable: false
			grid:
				add: 'none'
				hide_cardmenu: true
				edit:true
				rank: 'none'
				fields: ['created_by', 'has_da', 'has_dd']
				label: ['Created By','Drug Allergy', 'Drug Drug']
				width: [30, 30, 30]
			label: 'Interactions'


	patient_allergies:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				end_date:
					'dynamic': ''
		view:
			embed:
				form: 'patient_allergy'
				selectable: false
				add_preset:
					status: 'Active'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				rank: 'none'
				fields: ['allergen_id', 'reaction_id', 'severity_id', 'start_date']
				label: ['Allergen', 'Reaction', 'Severity', 'Start Dt']
				width: [35, 25, 25, 15]
			label: 'Active Allergies'
			readonly: true

	weight_normal:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['weight_change']
		view:
			columns: -3
			control: 'radio'
			label: "Is the patient's weight normal for them?"

	weight_change:
		model:
			max: 3
			source: ['Loss', 'Gain']
		view:
			columns: 3
			control: 'radio'
			label: "Type of weight change:"

	symptoms_filter:
		model:
			multi: true
			source: 'list_symptom'
			if:
				'*':
					fields: ['symptoms', 'symptom_review_date']
					sections: ['Active Symptoms Review']
		view:
			label: 'Symptoms'
			readonly: true
			offscreen: true

	symptom_review_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 3
			label: 'Symptoms Review Date'
			template: '{{now}}'

	symptoms:
		model:
			multi: true
			required: false
			sourcefilter:
				id:
					'dynamic': '{symptoms_filter}'
				active:
					'static': 'Yes'
		view:
			embed:
				form: 'list_symptom'
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['name']
				label: ['Symptom']
				width: [100]
			label: 'Active Symptoms'
			note: 'Select all that apply'

	# Patient Questions
	exacerbation:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Are you having an exacerbation of your MS?'

	are_preg:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['are_lact']
		view:
			columns: -3
			control: 'radio'
			label: 'Are you currently pregnant or have given birth in the last 6 weeks?'

	are_lact:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Are you currently lactating?'

	dx_date:
		model:
			type: 'date'
		view:
			columns: -3
			label: 'Date Diagnosed'

	ss_when_dx:
		view:
			columns: 3
			label: 'S/S at time of diagnosis'

	ss_now:
		view:
			columns: 3
			label: "What S/S brought you into the doctor's office now?"

	have_fridge:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: -3
			control: 'radio'
			label: 'Do you have adequate refrigeration?'

	have_pain:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pain_int', 'pain_loc']
		view:
			columns: -3
			control: 'radio'
			label: 'Do you have any pain?'

	pain_int:
		model:
			max: 2
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			columns: 3
			control: 'radio'
			label: 'Pain Intensity'

	pain_loc:
		view:
			columns: 3
			label: 'Pain Location'

	nut_diet:
		model:
			max: 8
			min: 1
			source: ['Poor', 'Fair', 'Good']
		view:
			columns: -3
			control: 'radio'
			label: "How has your diet been?"

	nut_app:
		model:
			max: 8
			min: 1
			source: ['Poor', 'Fair', 'Good']
		view:
			columns: 3
			control: 'radio'
			label: "How has your appetite been?"

	nut_parenteral:
		model:
			max: 3
			min: 1
			source: ['Parenteral', 'Enteral Feeding', 'None']
		view:
			columns: 3
			control: 'radio'
			label: "Are you undergoing any nutrition support?"

	nut_comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'Nutrition Comments/Pertinent History'

	# Teaching Assessment
	aware_admin:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: -3
			control: 'radio'
			note: 'eclipse, IV push, etc'
			label: 'How drug will be administered?'

	aware_participate:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Expectation of adherence?'

	aware_support:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: -3
			control: 'radio'
			label: 'We have a nurses and RPh on call 24/7?'

	aware_careplan_hospital:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Care Plan - Hospital Readmissions'

	aware_careplan_pain:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Care Plan - Pain Level'

	aware_careplan_fall:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Care Plan - Fall Prevention'

	aware_careplan_line:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Care Plan - Line Maintenance'

	aware_careplan_food:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Care Plan - Nutrition'

	aware_careplan_therapy:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Care Plan - Therapy'

	aware_careplan_labs:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Care Plan - Labs'

	# Therapy Support
	taken_before:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['med_tolerated']
		view:
			columns: -3
			control: 'radio'
			label: 'Has the patient taken the medication before?'

	med_tolerated:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Was it tolerated?'

	participate:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: -3
			control: 'radio'
			label: 'Does the patient agree to participate in therapy or have family member who will administer?'

	nurse_agency:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['nurse_agency_contact']
		view:
			columns: 3
			control: 'radio'
			label: 'Are we performing the nursing?'

	nurse_agency_contact:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Does patient know how to contact the nursing agency?'

	# Catheter Requirements
	catheter_log:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				form: 'patient_catheter'
				selectable: false
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				rank: 'none'
				fields: ['date_placed', 'date_discontinued', 'device', 'type_id']
				label: ['Date Placed', 'Date Discontinued', 'Device', 'Type']
				width: [15, 15, 35, 35]
			label: 'Catheter Log'
			readonly: true

	# Pharmacist Questions
	have_questions:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pharm_questions']
		view:
			columns: 2
			control: 'radio'
			label: 'Does patient have any questions for the pharmacist?'

	pharm_questions:
		view:
			columns: 2
			control: 'area'
			label: 'Pharmacist Questions'

	comment:
		view:
			control: 'area'
			label: 'Comments'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	sections_group: [
		'Medications':
			indent: false
			tab: 'H&P'
			fields: ['patient_medications']

		'Allergies':
			indent: false
			tab: 'H&P'
			fields: ['patient_allergies']

		'DUR - DD DA Interaction':
				hide_header: true
				indent: false
				tab: 'H&P'
				fields: ['patient_interaction_btn']

		'DUR - Interaction':
			hide_header: true
			indent: false
			tab: 'H&P'
			fields: ['patient_interactions']

		'Measurement Log':
			indent: false
			tab: 'H&P'
			fields: ['measurement_log']

		'H&P':
			hide_header: true
			indent: false
			tab: 'H&P'
			fields: ['gender','therapy_1', 'therapy_2',
			'therapy_3', 'therapy_4', 'therapy_5',
			'weight_normal', 'weight_change', 'symptoms_filter','symptom_review_date']

		'Active Symptoms Review':
			fields: ['symptoms']

		'Contact Date/Time':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['contact_date', 'contact_time']
		'Patient Questionnaire':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['dx_date', 'ss_when_dx', 'ss_now', 'exacerbation', 'are_preg', 'are_lact', 'have_pain', 'pain_int', 'pain_loc', 'nut_diet', 'nut_app', 'nut_parenteral', 'nut_comments']
		'Teaching Assessment':
			indent: false
			tab: 'Assessment'
			note: 'Is patient aware of the following...'
			fields: ['aware_admin', 'aware_participate',
			'aware_support', 'aware_careplan_hospital',
			'aware_careplan_fall', 'aware_careplan_pain',
			'aware_careplan_line', 'aware_careplan_food',
			'aware_careplan_therapy', 'aware_careplan_labs']
		'Therapy Support':
			indent: false
			tab: 'Assessment'
			fields: ['have_fridge', 'taken_before', 'med_tolerated', 'participate', 'nurse_agency', 'nurse_agency_contact']
		'Catheter Requirements':
			hide_header: true
			indent: false
			tab: 'Catheter'
			note: 'Please review Patient Catheter Info before adding new Catheter'
			fields: ['catheter_log']
		'Pharmacist Questions':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			fields: ['have_questions', 'pharm_questions']
		'Comments':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			fields: ['comment']
	]

	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Initial Nursing Assessment"
	]

view:
	comment: 'Patient > Careplan > Nursing Assessment'
	grid:
		fields: ['created_on', 'updated_on']
		sort: ['-id']
	label: 'Nursing Assessment'
	open: 'read'
