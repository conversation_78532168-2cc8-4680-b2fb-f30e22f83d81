#TABLE: RMINDC1_NDC_MEDID
fields:
	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	ndc:
		view:
			label: 'NDC'
			findunique: true
			readonly: true
			columns: 2

	medid:
		model:
			type: 'int'
		view:
			label: 'MEDID'
			readonly: true
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: ['ndc', 'medid']
	indexes:
		many: [
			['ndc']
			['medid']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['ndc', 'medid']

view:
	comment: 'Manage > List FDB NDC -> MEDID Crosswalk'
	find:
		basic: ['ndc', 'medid']
	grid:
		fields: ['ndc', 'medid']
	label: 'List FDB NDC -> MEDID Crosswalk'
