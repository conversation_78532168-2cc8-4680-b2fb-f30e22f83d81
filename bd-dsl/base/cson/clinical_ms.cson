fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Difficulties
	diff_write:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable or illegible, 1 = Difficulty, 2 = Normal)'
			label: 'Writing'

	diff_utensils:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficult or very clumsy, 2 = Normal)'
			label: 'Using utensils'

	diff_dress:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Getting dressed'

	diff_blurred_vision:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Blind, 1 = Impaired vision, 2 = Normal)'
			label: 'Blurred or double vision / loss of sight'

	diff_stiff:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = No stiffness)'
			label: 'Stiff legs'

	diff_speech:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unintelligible, 1 = Slurred, 2 = Normal)'
			label: 'Speech'

	diff_swallow:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Swallowing'

	diff_cramps:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = None)'
			label: 'Cramps / spasms / spasticity'

	diff_numb:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = None)'
			label: 'Numbness / tingling / buzzing / vibration'

	diff_ftdp:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = None)'
			label: 'Foot drop'

	diff_cord:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = None)'
			label: 'Loss of coordination'

	diff_motor:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = None)'
			label: 'Tremor with fine motor skills'

	diff_vertigo:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Mild-Moderate, 2 = None)'
			label: 'Vertigo'

	diff_balance:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Stand on one foot for 5 seconds (using either foot)'

	diff_weak:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = None)'
			label: 'Muscle Weakness'

	diff_cog:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = None)'
			label: 'Cognition'

	diff_bladder:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Total incontinence, 1 = Partial incontinence, 2 = No incontinence)'
			label: 'Bladder / Bowel function'

	diff_fatigue:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = None)'
			label: 'Fatigue'

	diff_walk:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficult or requires assistance, 2 = Normal)'
			label: 'Walking 10 feet'

	diff_pain:
		model:
			max: 2
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			note: '(0 = No pain  -  10 = Severe pain)'
			label: 'Pain'

	# Wellness
	distance_walk:
		model:
			max: 32
			source: ['Less than 10 feet', '11-50 feet', '51-100 feet', '101-150 feet', '> 150 feet']
		view:
			control: 'radio'
			label: 'How far can you walk comfortably'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		encounter_ms:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Difficulties':
			note: 'On a scale of zero to two (0 - 2) (unless specified), with 0 meaning Extreme Difficulty and 2 meaning Normal, please rate your difficulty today with:'
			fields: ['diff_write', 'diff_utensils', 'diff_dress', 'diff_blurred_vision', 'diff_stiff', 'diff_speech', 'diff_swallow', 'diff_cramps',
				'diff_numb', 'diff_ftdp', 'diff_cord', 'diff_motor', 'diff_vertigo', 'diff_balance',
				'diff_weak', 'diff_cog', 'diff_bladder', 'diff_fatigue', 'diff_walk', 'diff_pain', 'distance_walk']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan >  Multiple Sclerosis Difficulties'
	label: 'Clinical: Multiple Sclerosis Difficulties'
