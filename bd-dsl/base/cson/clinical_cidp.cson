fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Difficulties
	diff_write:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable or illegible, 1 = Difficulty, 2 = Normal)'
			label: 'Writing'

	diff_utensils:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficult or very clumsy, 2 = Normal)'
			label: 'Using utensils'

	diff_dress:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Getting dressed'

	diff_lift:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Partial, 2 = Normal)'
			label: 'Lifting arms (straight) over head'

	diff_climbing:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficult or requires assistance, 2 = Normal)'
			label: 'Climbing 3 stairs'

	diff_bed:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficult or requires assistance, 2 = Normal)'
			label: 'Getting up from bed or chairs'

	diff_balance:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficulty, 2 = Normal)'
			label: 'Stand on one foot for 5 seconds (using either foot)'

	diff_walk:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Unable, 1 = Difficult or requires assistance, 2 = Normal)'
			label: 'Walking 10 feet'

	# Weakness
	weakness_rhand:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Right hand'

	weakness_lhand:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Left hand'

	weakness_rleg:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Right leg/foot'

	weakness_lleg:
		model:
			max: 2
			source: ['0', '1', '2']
		view:
			control: 'radio'
			note: '(0 = Severe, 1 = Moderate, 2 = Normal)'
			label: 'Left leg/foot'

	# Disability Score
	arm_disability:
		model:
			max: 256
			min: 1
			multi:false
			source: ['Inability to use either arm for any purposeful movement', 'Difficulty in using one or both arms in daily activity', 'No upper limb problems']
		view:
			control: 'radio'
			note: 'Check one'
			label: 'Arm Disability'

	leg_disability:
		model:
			max: 256
			min: 1
			multi:false
			source: ['Unable to stand and walk', 'Difficulty in walking', 'Walking not affected']
		view:
			control: 'radio'
			note: 'Check one'
			label: 'Leg Disability'

	numbness:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['numbness_legs', 'numbness_arms', 'numbness_symmetrical']
		view:
			control: 'radio'
			label: 'Do you have any numbness?'

	numbness_legs:
		model:
			max: 32
			multi:true
			source: ['Toes', 'Foot/Ankle', 'Ankle - Mid Shin', 'Mid Shin - Knee', 'Above Knee']
		view:
			control: 'checkbox'
			note: 'Select all areas of reported numbness'
			label: 'Legs Numbness'

	numbness_arms:
		model:
			max: 32
			multi: true
			source: ['Fingers', 'Hand/Wrist', 'Elbow', 'Full Arm']
		view:
			control: 'checkbox'
			note: 'Select all areas of reported numbness'
			label: 'Arms Numbness'

	numbness_symmetrical:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['numbness_leg_level', 'numbness_arms_level']
		view:
			control: 'radio'
			label: 'Is the numbness symmetrical in both legs or both arms?'

	numbness_leg_level:
		model:
			max: 3
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			note: 'Assess the numbness level from 1 (mild) - 10 (severe)'
			label: 'Legs Numbness Level'

	numbness_arms_level:
		model:
			max: 3
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			note: 'Assess the numbness level from 1 (mild) - 10 (severe)'
			label: 'Arms Numbness Level'

	distance_walk:
		model:
			max: 32
			source: ['Less than 10 feet', '11-50 feet', '51-100 feet', '101-150 feet', '> 150 feet']
		view:
			control: 'radio'
			label: 'How far can you walk comfortably'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_alsfrs_cidp:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Difficulties':
			note: 'On a scale of zero to two (0 - 2), with 0 meaning Extreme Difficulty and 2 meaning Normal, please rate your difficulty today with:'
			fields: ['diff_write', 'diff_utensils', 'diff_dress', 'diff_lift',
			'diff_climbing', 'diff_bed', 'diff_balance', 'diff_walk']
		'Weakness':
			note: 'On a scale from zero to two (0 - 2), with zero meaning Severe weakness and 2 meaning Normal, please rate the weakness in your limbs'
			fields: ['weakness_rhand','weakness_lhand','weakness_rleg','weakness_lleg']
		'Disability Score':
			fields: ['arm_disability', 'leg_disability']
		'Wellness':
			fields: ['numbness', 'numbness_legs', 'numbness_arms',
			'numbness_symmetrical', 'numbness_leg_level',
			'numbness_arms_level', 'distance_walk']

view:
	hide_cardmenu: true
	comment: 'Patient > Careplan >  IG > CIDP'
	label: 'Clinical: IG - CIDP'
