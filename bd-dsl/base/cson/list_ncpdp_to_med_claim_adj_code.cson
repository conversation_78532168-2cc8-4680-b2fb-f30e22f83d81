fields:

	ncpdp_reject_code:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '511-FB'
		view:
			reference: '472-6E'
			note: '472-6E'
			label: 'NCPDP Reject Code'
			columns: 4

	mm_adj_reason_code:
		model:
			required: true
			min: 1
			max: 5
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CARC'
		view:
			columns: 4
			label: 'Major Medical Adjustment Reason Code'

	mm_adj_group_code:
		model:
			required: true
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CAS01'
		view:
			columns: 4
			label: 'Adjustment Group Code'
			reference: 'CAS01'
			_meta:
				location: '2430 CAS'
				field: '01'
				path: 'claimInformation.serviceLines[{idx1-50}].lineAdjudicationInformation[{idx1-15}].claimAdjustmentInformation[{idx1-5}].adjustmentGroupCode'

	priority_rank:
		model:
			type: 'int'
			required: true
		view:
			label: 'Priority Rank'
			columns: 4

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	indexes:
		many: [
			['ncpdp_reject_code']
		]
	name: '{ncpdp_reject_code} - {mm_adj_reason_code}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['ncpdp_reject_code', 'mm_adj_reason_code', 'mm_adj_group_code', 'priority_rank']

view:
	comment: 'Manage > NCPDP to Major Medical Adjustment Reason Code'
	find:
		basic: ['ncpdp_reject_code', 'mm_adj_reason_code', 'mm_adj_group_code', 'priority_rank']
	grid:
		fields: ['ncpdp_reject_code', 'mm_adj_reason_code', 'mm_adj_group_code', 'priority_rank']
		sort: ['ncpdp_reject_code']
	label: 'NCPDP to Major Medical Adjustment Reason Code'
	open: 'read'
