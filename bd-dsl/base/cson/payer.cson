fields:
	code:
		view:
			label: 'Code'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	is_self_pay:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Self Pay'
			readonly: true
			offscreen: true

	external_id:
		model:
			required: false
		view:
			label: 'CPR+ ID'
			readonly: true
			offscreen: true

	ss_benefit_id:
		model:
			type: 'int'
		view:
			label: 'Associated SureScripts Benefit Record'
			readonly: true
			offscreen: true

	#INSCOMP.ORG
	organization:
		model:
			required: true
			search: 'A'
		view:
			columns: 2
			label: 'Organization'
			findunique: true

	#INSCOMP.ID
	short_code:
		model:
			search: 'A'
			required: true
			max: 80
		view:
			columns: 2
			label: 'Insurance Short Code'
			note: 'BCBS, UHC, etc.'
			findunique: true

	#Default: Yes
	#INSCOMP.Inactive { '0': 'Yes' }
	active:
		model:
			source: ['Yes']
			default: 'Yes'
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Active'
			findfilter: 'Yes'

	type_id:
		model:
			required: true
			source: 'list_payer_type'
			sourceid: 'code'
			if:
				'MCRB':
					fields: ['mac_id']
					prefill:
						cms_1: 'Medicare'
						split_items: ['Supplies', 'DME']
						cms_10: 'MCR #'
				'MCRD':
					prefill:
						cms_1: 'Medicare'
						ncpdp_sec_claims_ingred_cost: 'Send Primary Claim Amount'
						ncpdp_sec_claims_gross_amount_due: 'Send Primary Claim Amount'
				'MEDI':
					fields: ['medicaid_id']
					prefill:
						cms_1: 'Medicaid'
						ncpdp_sec_claims_ingred_cost: 'Send Primary Claim Amount'
						ncpdp_sec_claims_gross_amount_due: 'Send Primary Claim Amount'
						cms_10: 'MCD Provider #'
				'CMMED':
					fields: ['inn_oon']
					prefill:
						cms_1: 'Group Health Plan'
				'CMPBM':
					fields: ['inn_oon']
					prefill:
						cms_1: 'Group Health Plan'
						ncpdp_sec_claims_ingred_cost: 'Send Primary Claim Amount'
						ncpdp_sec_claims_gross_amount_due: 'Send Primary Claim Amount'
				'PAP':
					fields: ['pap_program_id']
					prefill:
						ncpdp_sec_claims_ingred_cost: 'Send Primary Claim Amount'
						ncpdp_sec_claims_gross_amount_due: 'Send Primary Claim Amount'
				'COPAY':
					fields: ['cap_program_id']
					prefill:
						billing_method_id: 'ncpdp'
						ncpdp_sec_claims_ingred_cost: 'Send Co-pay Amount'
						ncpdp_sec_claims_gross_amount_due: 'Send Co-pay Amount'
				'COUPON':
					prefill:
						billing_method_id: 'ncpdp'
						ncpdp_sec_claims_ingred_cost: 'Send Co-pay Amoun'
						ncpdp_sec_claims_gross_amount_due: 'Send Co-pay Amoun'
				'FOUND':
					fields: ['other_program_id']
					prefill:
						ncpdp_sec_claims_ingred_cost: 'Send Primary Claim Amount'
						ncpdp_sec_claims_gross_amount_due: 'Send Primary Claim Amount'
		view:
			columns: 2
			label: 'Type'

	#Copay/PAP/Foundational Assistance Program
	pap_program_id:
		model:
			required: true
			source: 'list_pap_program'
		view:
			add_preset:
				name: '{organization}'
				active: 'Yes'
				bin: '{bin}'
				pcn: '{pcn}'
				phone: '{phone}'
				fax: '{fax}'
			form_link_enabled: true
			columns: 2
			label: 'PAP Program'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_pap_program/?limit=1&filter=id:'
				fields:
					'organization': ['name']
			]

	cap_program_id:
		model:
			required: false
			source: 'list_copay_program'
		view:
			add_preset:
				name: '{organization}'
				active: 'Yes'
				bin: '{bin}'
				pcn: '{pcn}'
				phone: '{phone}'
				fax: '{fax}'
			form_link_enabled: true
			columns: 2
			label: 'Copay Assistance Program'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_copay_program/?limit=1&filter=id:'
				fields:
					'organization': ['name']
			]

	other_program_id:
		model:
			required: true
			source: 'list_alt_payer'
		view:
			add_preset:
				name: '{organization}'
				active: 'Yes'
				bin: '{bin}'
				pcn: '{pcn}'
				phone: '{phone}'
				fax: '{fax}'
			form_link_enabled: true
			columns: 2
			label: 'Other Assistance Program'
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_alt_payer/?limit=1&filter=id:'
				fields:
					'organization': ['name']
					'bin': ['bin']
					'pcn': ['pcn']
			]

	#INSCOMP.INVOICETYP { '1': 'mm', 'E': 'mm', 'M': 'ncpdp', '3': 'generic', '4': 'cms1500' }
	billing_method_id:
		model:
			required: true
			source: 'list_billing_method'
			sourceid: 'code'
			if:
				'mm':
					sections: ['270/Advanced Eligibility (pVerify)', 'Electronic Medical Claims Setup - Medical',
					'Setup Options', 'Electronic Medical Claims Setup - Global', 'Electronic Medical Secondary / COB']
					fields: ['cover_supplies', 'cover_dme', 'req_auth_for', 'split_items', 'always_bill_for_denial', 'factor_allowed_variance']
					prefill:
						payer_bill_in_hcpc: 'Yes'
				'cms1500':
					fields: ['box_24j', 'cover_supplies', 'cover_dme', 'req_auth_for', 'split_items', 'split_pa', 'always_bill_for_denial', 'factor_allowed_variance']
					sections: ['1500 Options', 'Setup Options']
					prefill:
						payer_bill_in_hcpc: 'Yes'
				'ncpdp':
					fields: ['bill_mm_and_ncpdp', 'min_accept_margin', 'auto_adjust_expected', 'bin', 'auto_transfer_write_off', 'factor_allowed_variance']
					sections: ['Electronic Pharmacy Claims Setup', 'Electronic Pharmacy Secondary / COB',
					'E1/Eligibility', 'Setup Options', 'Electronic Medical Secondary / COB']
		view:
			label: 'Billing Method'
			columns: 2

	inn_oon:
		model:
			search: 'B'
			required: true
			source: ['INN', 'OON']
			if:
				'INN':
					fields: ['inn_site_id']
		view:
			columns: 4
			control: 'radio'
			label: 'INN/OON'

	inn_site_id:
		model:
			multi: true
			required: true
			source: 'site'
		view:
			columns: -2
			label: 'INN for which sites?'

	#INSCOMP.PHONE
	phone:
		model:
			search: 'C'
			max: 21
		view:
			columns: 4
			format: 'us_phone'
			label: 'Payer Phone'

	#INSCOMP.FAX
	fax:
		model:
			max: 21
		view:
			columns: 4
			format: 'us_phone'
			label: 'Payer Fax'

	#INSCOMP.ADDRESS
	address1:
		model:
			max: 35
		view:
			label: 'Address 1'
			columns: 'addr_1'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'baddress1'
				}
			]
			class: "api_prefill"
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'address1': ['addressLabel']
					'city': ['city']
					'state_id': ['stateCode']
					'zip': ['postalCode']
			]

	address2:
		model:
			max: 35
		view:
			label: 'Address 2'
			columns: 'addr_2'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'baddress2'
				}
			]

	#INSCOMP.CITY
	city:
		model:
			min: 1
			max: 60
			type: 'text'
		view:
			columns: 'addr_city'
			label: 'City'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'bcity'
				}
			]

	#INSCOMP.ST
	state_id:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'State'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'bstate_id'
				}
			]

	#INSCOMP.ZIP
	zip:
		model:
			max: 10
			min: 5
		view:
			columns: 'addr_zip'
			format: 'us_zip'
			label: 'Zip'
			validate: [
				{
					name: 'CopyForwardValue'
					field: 'bzip'
				}
			]
			transform: [
					name: 'CityStateTransform'
					fields:
						zip: 'zip'
						city: 'city'
						state: 'state'
			]

	# Primary Billing Information
	#INSCOMP.SBORG
	borganization:
		view:
			columns: 2
			label: 'Organization'

	#INSCOMP.SBCONTACT
	attn:
		view:
			columns: 4
			label: 'Attn'

	#INSCOMP.SBADDRESS
	baddress1:
		model:
			max: 35
		view:
			label: 'Address 1'
			columns: 'addr_1'

	baddress2:
		model:
			max: 35
		view:
			label: 'Address 2'
			columns: 'addr_2'

	#INSCOMP.SBCITY
	bcity:
		model:
			min: 1
			max: 60
			type: 'text'
		view:
			columns: 'addr_city'
			label: 'City'

	#INSCOMP.SBST
	bstate_id:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'State'

	#INSCOMP.SBZIP
	bzip:
		model:
			max: 10
			min: 5
		view:
			columns: 'addr_zip'
			format: 'us_zip'
			label: 'Zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						bzip: 'bzip'
						bcity: 'bcity'
						bstate: 'bstate'
			]

	#INSCOMP.PROVIDER
	provider_no:
		view:
			columns: 4
			label: 'Provider Number'

	#INSCOMP.USE24J { '1': 'Yes' }
	box_24j:
		model:
			source: ['Yes']
			default: 'Yes'
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Populate Box 24J?'

	mac_id:
		model:
			required: false
		view:
			columns: 2
			label: 'Medicare Administrative Contractors (MACs)'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Z]{3}\\d{4}$'
				error: 'Invalid MAC ID'
			}]

	medicaid_id:
		model:
			required: true
		view:
			columns: 4
			label: 'Medicaid ID'
			validate: [{
				name: 'RegExValidator'
				pattern: ['^\\d{10}$', '^\\d{9}$', '^[A-Za-z0-9]{8,12}$', '^[A-Z]{2}\\d{6}[A-Z]?$']
				error: 'Invalid Medicaid ID'
			}]

	#INSCOMP.NAICID
	naic_id:
		model:
			type: 'int'
			max: 5
		view:
			label: 'National Association of Insurance Commissioners (NAID)'
			offscreen: true
			readonly: true
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{5}$'
				error: 'Invalid NAID, must be 5 digits'
			}]

	#INSCOMP.HPID
	hp_id:
		model:
			type: 'int'
			max: 10
		view:
			offscreen: true
			readonly: true
			label: 'Health Plan Identifier (HPID)'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{10}$'
				error: 'Invalid HPID, must be 10 digits'
			}]

	hin_id:
		model:
			type: 'int'
			max: 10
		view:
			offscreen: true
			readonly: true
			label: 'Health Industry Number (HIN)'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z][A-Za-z0-9]{8}$'
				error: 'Invalid HPID, must be 8 alphanumeric characters'
			}]

	coba_id:
		model:
			type: 'int'
			max: 10
		view:
			offscreen: true
			readonly: true
			label: 'Coordination of Benefits Agreement (COBA)'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{5}$'
				error: 'Invalid COBA ID, must be 5 digits'
			}]

	#CMS 1500 options
	cms_1:
		model:
			source: ['Medicare', 'Medicaid', 'ChampVA', 'Group Health Plan', 'FECA Black Lung', 'Other']
		view:
			columns: 2
			control: 'radio'
			label: 'Box 1, Insurance Type'

	cms_2:
		model:
			multi: true
			source: ['NDC', 'NDC Qualifier', 'NDC Unit Qual/Units', 'NDC Unit Price']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Box 24, Supplemental Info'

	#INSCOMP.Columnk { 'Y': 'Yes' }
	cms_3:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Include Physician's NPI and Taxonomy in 24J"

	#INSCOMP.mrnopid { 'M': 'MRN', 'P: 'Patient ID', '#': 'Invoice Number' }
	cms_4:
		model:
			source: ['MRN', 'Patient ID', 'Invoice Number']
		view:
			columns: 2
			control: 'radio'
			label: "Box 26, Patient's Account #"

	#INSCOMP.SOC { 'Y': 'Yes' }
	cms_5:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Box 12, SOC Date"

	#INSCOMP.plserv
	cms_6:
		model:
			default: '12'
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SV105'
		view:
			columns: 2
			label: "Default Place of Service"

	#INSCOMP.year4dig { 'Y': 'Yes' }
	cms_7:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Dates have four digit year"

	#INSCOMP.PgTot { 'Y': 'Yes' }
	cms_8:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Total charges on each page"

	#INSCOMP.ShoPay2ND { 'Y': 'Yes' }
	cms_9:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Print Amt. Paid when 2ND"

	#INSCOMP.Box17a { 'M': 'MCD Provider #', 'D': 'DEA #', 'L': 'State License #', 'R: 'MCR #', 'T': 'Taxonomy Code' }
	cms_10:
		model:
			source: ['MCD Provider #', 'DEA #', 'State License #', 'MCR #', 'Taxonomy Code']
		view:
			columns: 2
			control: 'radio'
			label: "Box 17a, ID # for Referring Provider"

	#INSCOMP.EIN
	cms_11:
		view:
			columns: 4
			label: "Alternative Tax ID"

	cms_12:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['cms_12_qualifier', 'cms_12_id']
		view:
			columns: 2
			control: 'radio'
			label: "Rendering Provider ID Alternative ID (Box 33b)"

	cms_12_qualifier:
		model:
			required: true
			min: 2
			max: 2
		view:
			columns: 4
			label: "Rendering Provider ID Qualifier"

	cms_12_id:
		model:
			required: true
			min: 1
			max: 50
		view:
			columns: 4
			label: "Billing Provider ID"

	#INSCOMP.Cms1500_Printbox17qual { '1': 'Yes' }
	cms_13:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Print Box 17 Qualifier"

	#INSCOMP.Cms1500_Printdecimal_Diagcode { '1': 'Yes' }
	cms_14:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Print Decimals with Diagnosis Codes"

	notes:
		view:
			control: 'area'
			label: 'Notes'

	biller_id:
		model:
			source: 'wf_queue_team'
			sourceid: 'code'
			sourcefilter:
				code:
					static: 'Biller'
			default: 'Biller'
		view:
			columns: 2
			label: 'Default Biller Team'

	collector_id:
		model:
			source: 'wf_queue_team'
			sourceid: 'code'
			sourcefilter:
				code:
					static: 'Collector'
			default: 'Collector'
		view:
			columns: 2
			label: 'Default Collector Team'

	payer_bill_in_hcpc:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Payer Bill in HCPCs?"

	#INSCOMP.Authrequirement { 'O': 'By Order', 'B': 'By Hcpc', 'A': 'Always', 'N': 'Never' }
	req_auth:
		model:
			source: ['By Order', 'By Hcpc', 'Always', 'Never']
			if:
				'By Order':
					fields: ['req_auth_for']
				'By Hcpc':
					fields: ['req_auth_for']
				'Always':
					fields: ['req_auth_for']
		view:
			columns: 2
			control: 'radio'
			label: "Payer Requires Authorization:"

	req_auth_for:
		model:
			multi: true
			default: ['Medication', 'Supplies', 'Nursing', 'DME']
			source: ['Medication', 'Supplies', 'Nursing', 'DME']
		view:
			columns: 2
			class: 'checkbox checkbox-2'
			control: 'checkbox'
			label: "Auth Required For"

	split_items:
		model:
			multi: true
			source: ['By Prescription', 'Supplies', 'DME']
		view:
			columns: 4
			control: 'checkbox'
			label: "Split Items onto separate invoices"

	split_pa:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Split items with a PA onto separate invoices"
			note: '1500 only'

	#INSCOMP.cmnrequired { '1': 'Yes' }
	req_signed_cmn:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Signed CMN Required Prior to Billing?"

	#INSCOMP.TimelyFiling
	days_timely_filing:
		model:
			type: 'int'
		view:
			columns: 4
			note: 'Leave blank for N/A'
			label: "Number of days from DOS for Timely Filing"

	#INSCOMP.PercentCov
	default_coverage:
		model:
			type: 'int'
			min: 1
			max: 100
		view:
			columns: 4
			note: '%'
			class: 'numeral discount'
			format: 'percent'
			label: "Default Percent of Coverage"

	#Check this checkbox to "tag" this payor as a Supplementary Payor to Medicare (for example, AARP). This setting is used in conjunction with the Point of Sale functionality to determine the patient's responsibility. When checked, CPR+ does not calculate a patient responsibility for any covered service by Medicare and assumes that the patient will pay in full for any services that are NOT covered by Medicare.
	#INSCOMP.SupplementaryPayor { '1': 'Yes' }
	supplementary_payer:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Is this a supplementary payer to Medicare?"

	#Checking this option identifies this payor as a 340B payor. When using the 340B Export Interface, any 340B items that are dispensed and billed to this payor are included in the export.
	payer_340b:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "340b Payer?"

	#With this option checked, when rental claims are created from the Recurring Rentals manager, the From Date remains as is (the cycle date of the item) and the To Date reflects the end of the rental period. So, CPR+ does the following based on this setting:
	#When the rental period begins on the first date of the month, CPR+ "knows" how many days there are in that calendar month. 
	#When the rental period begins on the 30th or 31st, the item's To Date reflects the correct end date for those months not containing 29 or 30 days.
	#All other rental date spans follow a monthly period: e.g. from the 2nd to the 1st; from the 15th to the 14th.
	#INSCOMP.Spanrecrent { '1': 'Yes' }
	span_rental_dates:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Span Rental Dates"

	bill_mm_and_ncpdp:
		model:
			source: ['Yes']
			if:
				'Yes':
					sections: ['270/Advanced Eligibility (pVerify)', 'Electronic Medical Claims Setup - Medical',
					'Setup Options', 'Electronic Medical Claims Setup - Global', 'Electronic Medical Secondary / COB']
					fields: ['cover_supplies', 'cover_dme', 'req_auth_for', 'split_items', 'always_bill_for_denial', 'factor_allowed_variance']
					prefill:
						payer_bill_in_hcpc: 'Yes'
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			note: 'If checked, sends NCPDP claim and Major Medical claim'
			label: "Bill Major Medical and NCPDP Claims"

	#This option sets an acceptable percentage of profit above cost at the payor level. When in the Claims to Adjudicate list, claims that do not meet this criteria show up with a status of "Margin" to alert the user. 
	#If the "Auto Move SpRx Claim if Minimum Profit Margin is Met?" option is checked and the amount paid is within the Acceptable Profit Margin percentage, the claim will automatically move to the next SpRx list
	#INSCOMP.ProfitMargin
	min_accept_margin:
		model:
			type: 'decimal'
			max: 100
			min: 0.00
			rounding: 0.01
		view:
			columns: 4
			note: '% (NCPDP Claims)'
			class: 'numeral discount'
			format: 'percent'
			label: "Minimum Acceptable Profit Margin"

	auto_adjust_expected:
		model:
			default: 'Yes'
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Auto Adjust Expected Price based on Paid Amount?"
			note: 'Only applies if Minimum Acceptable Profit Margin is set'

	#INSCOMP.Billarrears { '1': 'Yes' }
	bill_recur_arrears:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			note: 'Line Item Only'
			label: "Bill Recurring Rental Claims in Arrears?"

	#If option #38 is checked, a payment threshold percentage must be entered for this option. When a percentage is set (for example 5% [+/-]) and the primary payor pays an amount that falls within this threshold, CPR+ will then "write-off" the difference between the expected and the allowable amounts. The remaining balance is transferred to the secondary payor and placed in the appropriate queue.
	auto_transfer_write_off:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['auto_transfer_margin', 'auto_transfer_adj_id']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Auto-Adjustment write off for Threshold?"

	#This option sets an acceptable percentage of profit above cost at the payor level. When in the Claims to Adjudicate list, claims that do not meet this criteria show up with a status of "Margin" to alert the user. 
	#If the "Auto Move SpRx Claim if Minimum Profit Margin is Met?" option is checked and the amount paid is within the Acceptable Profit Margin percentage, the claim will automatically move to the next SpRx list
	auto_transfer_margin:
		model:
			required: true
			type: 'decimal'
			max: 100
			min: 0.00
			rounding: 0.01
		view:
			columns: 4
			note: '% of Expected'
			class: 'numeral discount'
			format: 'percent'
			label: "Auto-Adjustment write off Threshold"

	auto_transfer_adj_id:
		model:
			required: true
			source: 'list_adjustment_reason'
			sourceid: 'code'
		view:
			columns: 2
			label: "Auto Transfer Adjustment Code"

	#INSCOMP.Billdiscardquantity { '1': 'Single Use Drug', '2': 'All Discarded Drugs', '3': 'None' }
	discarded_meds:
		model:
			source: ['Single Use Drug', 'All Discarded Drugs', 'None']
		view:
			columns: 2
			control: 'radio'
			label: "Supports Billing of Discarded Compounded Medications"

	cover_supplies:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Does Payer Cover Supplies (Per Diem)?"

	cover_dme:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['max_rental_claims', 'no_recurring_billing', 'daily_bill_rental']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Does Payer Cover DME/Pump Rentals?"

	max_rental_claims:
		model:
			required: true
			default: 18
			min: 0
			type: 'int'
		view:
			columns: 4
			note: '0 for no limit'
			label: 'Max Number of Rental Claims'

	no_recurring_billing:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Do not create recurring billing?'

	daily_bill_rental:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			note: 'If set, defaults the frequency code to Daily'
			label: 'Daily Bill Rental?'

	#INSCOMP.Alwaysbillasdenial { '1': 'Yes' }
	always_bill_for_denial:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Always Bill for Denial?'

	factor_allowed_variance:
		model:
			type: 'decimal'
			min: 1
			max: 10
			rounding: 1
		view:
			columns: 4
			label: 'Factor Variance Allowed %'
			note: 'Max 10%, Factor Drugs Only'
			class: 'numeral'
			format: 'percent'

	pverify_payer_id:
		model:
			source: 'list_pverify_payer'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Associated pVerify Payer'

	# Medical Claims Settings
	mm_hold_claims_dos_end:
		model:
			source: ['Yes']
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Hold Claims until after DOS End"

	mm_send_billing_prov_commercial_number:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['mm_billing_commercial_number']
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Add Billing Provider Commercial Number to Claim?"

	#INSCOMP.SECONDID
	mm_billing_commercial_number:
		model:
			required: true
			min: 1
			max: 50
		view:
			columns: 2
			label: 'Billing Provider Commercial Number'
			class: 'claim-field'

	mm_send_rendering_prov_commercial_number:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['mm_rendering_commercial_number']
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Add Rendering Provider Commercial Number to Claim?"

	#INSCOMP.RENDPROVID
	mm_rendering_commercial_number:
		model:
			required: true
			min: 1
			max: 50
		view:
			columns: 2
			label: 'Rendering Provider Commercial Number'
			class: 'claim-field'

	mm_auto_transfer_adjustment:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['mm_auto_trans_adj_codes', 'mm_auto_transfer_adjustment_reason']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Auto-Adjustments for adjustment codes?"

	mm_auto_trans_adj_codes:
		model:
			required: true
			multi: true
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CARC'
		view:
			columns: 2
			label: "Create Adjustment for Claim Submission Fee Code(s)"
			class: 'claim-field'

	mm_auto_transfer_adjustment_reason:
		model:
			required: true
			source: 'list_adjustment_reason'
			sourceid: 'code'
		view:
			columns: 2
			label: "Adjustment Reason"

	#INSCOMP.Needndc { '1': 'Yes' }
	mm_submit_ndc_rx_info:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Submit NDC/RX Information?"

	mm_sos_only:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Transmit Only Start of Service?"

	mm_default_service_place_id:
		model:
			default: '12'
			required: true
			min: 1
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CLM05-01'
		view:
			columns: 2
			label: 'Default place of service'
			note: 'CLM05-01'
			class: 'claim-field'

	#INSCOMP.NEICPID
	mm_payer_id:
		model:
			required: false
		view:
			columns: 4
			note: 'Clearinghouse Payer Name is now used'
			label: 'Legacy Clearinghouse Payer ID'
			readonly: true
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{3,10}$'
				error: 'Invalid Payer ID, must be 1-60 alpha numeric characters'
			}]

	mm_payer_name:
		model:
			required: true
		view:
			columns: 4
			label: 'Clearinghouse Payer Name'

	mm_claim_filing_indicator_code:
		model:
			required: true
			min: 1
			max: 3
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR09'
		view:
			columns: 2
			label: 'Claim Filing Indicator Code'
			reference: 'SBR09'
			class: 'claim-field'

	#INSCOMP.Usecontractinfo { '1': 'Yes' }
	mm_send_contract_pricing:
		model:
			source: ['Yes']
			if:
				"Yes":
					require_fields: ['assigned_contract_id']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Send Contract Pricing Info?"

	mm_calc_perc_sales_tax:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Calculate and Transmit % Sales Tax"
			note: 'AMT02 AMT01=T'
			readonly: true
			offscreen: true

	mm_copy_ordering_md_to_ref:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Copy Ordering MD to Referring MD?"

	#INSCOMP.Mcalsendupn { '1': 'Yes' }
	mm_send_upin_supplies:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Send UPIN for Supplies (Medi-Cal)?"

	mm_response_type:
		model:
			required: true
			default: 'Electronic'
			source: ['Electronic', 'Paper']
		view:
			columns: 4
			label: 'Expected Response Type'

	# Medical COB
	mm_sec_claim_filing_indicator_code:
		model:
			required: true
			min: 1
			max: 3
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR09'
		view:
			columns: 4
			label: 'Secondary Claim Filing Indicator Code'
			reference: 'SBR09'
			class: 'claim-field'

	mm_sec_payer_id:
		view:
			columns: 4
			label: 'Secondary Claims Payer ID'
			class: 'claim-field'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9]{3,10}$'
				error: 'Invalid Payer ID, must be 1-60 alpha numeric characters'
			}]

	mm_sec_send_identifier:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['mm_sec_id_qualifier', 'mm_sec_id']
			default: 'Yes'
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "On Secondary Claim send Additional Payer ID?"

	mm_sec_id_qualifier:
		model:
			required: true
			min: 2
			max: 3
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '2330B-REF01'
		view:
			columns: 2
			label: 'Additional Payer ID Qualifier'
			class: 'claim-field'

	#INSCOMP.payeeid
	mm_sec_id:
		model:
			required: true
			min: 1
			max: 50
		view:
			columns: 4
			label: 'Additional Payer ID'
			class: 'claim-field'

	mm_sec_claims_pr_code:
		model:
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR01'
		view:
			columns: 2
			label: "Other Payer - Patient Responsibility Code"
			class: 'claim-field'

	# Electronic Setup
	#INSCOMP.SPIDQUAL
	ncpdp_pharmacy_qualifier_id:
		model:
			default: '01'
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '202-B2'
				code:
					'static': ['01', '03', '04', '05', '07', '08', '11', '12', '14', '99']
			required: true
			if:
				'01': # NPI
					fields: ['ncpdp_pharmacy_pull_id']
				'03': # BCBS
					fields: ['ncpdp_pharmacy_pull_id']
				'04': # Medicare
					fields: ['ncpdp_pharmacy_pull_id']
				'05': # Medicaid
					fields: ['ncpdp_pharmacy_pull_id']
				'07': # NCPDP / NABP
					fields: ['ncpdp_pharmacy_pull_id']
				'08': # State License
					fields: ['ncpdp_pharmacy_pull_id']
				'11': # Tax ID
					fields: ['ncpdp_pharmacy_pull_id']
				'12': # DEA
					fields: ['ncpdp_pharmacy_pull_id']
				'14': # Plan Specific
					fields: ['ncpdp_pharmacy_id']
				'99': # Other
					fields: ['ncpdp_pharmacy_id']
		view:
			columns: 4
			label: 'NABP / ID # Qualifier'
			note: '(202-B2)'
			class: 'claim-field'

	#INSCOMP.Pullb1fromclient { '1': 'Yes' }
	ncpdp_pharmacy_pull_id:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['ncpdp_pharmacy_id']
			default: 'Yes'
		view:
			columns: 4
			control: 'radio'
			note: 'If Yes, uses respective Site Identifier'
			label: "Pull ID from Company"

	#INSCOMP.PHARMNO
	ncpdp_pharmacy_id:
		model:
			required: true
		view:
			columns: 4
			label: 'Pharamacy #'
			note: '(201-B1)'
			class: 'claim-field'

	#INSCOMP.PCN
	pcn:
		model:
			required: false
			max: 10
		view:
			columns: 4
			label: 'PCN'
			note: '(104-A4)'
			class: 'claim-field'
			validate: [{
				name: 'RegExValidator'
				pattern: '^[A-Za-z0-9\\-]{1,10}$'
				error: 'Invalid PCN, must be 1-10 alphanumeric characters'
			}]

	#INSCOMP.BINNO
	bin:
		model:
			required: true
			max: 6
		view:
			columns: 4
			label: 'BIN'
			note: '(101-A1)'
			class: 'claim-field'
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\d{6}$'
				error: 'Invalid BIN, must be 6 digits'
			}]

	#INSCOMP.MDFIELD {'N': '01', 'M': '05', 'D': '12', 'L': '08', 'R': '04', 'U': '06', 'H': '15'}
	prescriber_no_field_id:
		model:
			source: 'list_ncpdp_ecl'
			sourcefilter:
				field:
					'static': '466-EZ'
				code:
					'static': ['01', '04', '05', '08', '12', '13']
			sourceid: 'code'
			default: '01'
		view:
			columns: 2
			label: "Precriber ID Qualifier"
			note: '(466-EZ)'
			class: 'claim-field'

	pcp_no_field_id:
		model:
			source: 'list_ncpdp_ecl'
			sourcefilter:
				field:
					'static': '466-EZ'
				code:
					'static': ['01', '04', '05', '08', '12', '13']
			sourceid: 'code'
			default: '01'
		view:
			columns: 2
			label: "PCP ID Qualifier"
			note: '(466-EZ)'
			class: 'claim-field'

	#INSCOMP.DEFCUSTLOC { 1: '01', '2': '02', '3': '03', '4': '04', '5': '05', '6': '06', '7': '07', '8': '08', '9': '09', '11': '11', '12': '12', '13': '13', '14': '14', '15': '15', '16': '16', '17': '17', '20': '20', '21': '21', '22': '22', '23': '23', '24': '24', '25': '25', '26': '26', '31': '31', '32': '32', '33': '33', '34': '34', '41': '41', '49': '49', '50': '50', '51': '51', '53': '53', '54': '54', '55': '55', '56': '56', '57': '57', '60': '60', '61': '61', '65': '65', '71': '71', '72': '72', '81': '81', '99': '99'}
	default_service_place_id:
		model:
			source: 'list_ncpdp_ext_ecl'
			sourcefilter:
				field:
					'static': '307-C7'
			sourceid: 'code'
			default: '12'
		view:
			columns: 2
			label: 'Default Place of Service'
			note: '(307-C7)'
			class: 'claim-field'

	software_vendor_id:
		model:
			source: 'list_software_cert'
			sourceid: 'code'
		view:
			columns: 2
			label: 'Software Certification ID'
			note: '(110-AK)'
			class: 'claim-field'
			
	#INSCOMP.DEFDFEE
	default_dispense_fee:
		model:
			rounding: 0.01
			type: 'decimal'
			min: 0.01
		view:
			columns: 4
			label: 'Default Dispense Fee'
			note: '(412-DC)'
			class: 'numeral money'
			format: '$0,0.00'

	base_ing_cost_on_id:
		model:
			required: true
			source: 'list_ncpdp_ecl'
			sourcefilter:
				field:
					'static': '423-DN'
				code:
					'static': ['00', '01', '05', '07', '10', '12']
			sourceid: 'code'
			default: '00'
		view:
			columns: 2
			label: "Ingredient Cost Determination Basis"
			note: '(423-DN) Default = Expected Price, Usual & Customary = List Price'
			class: 'claim-field'

	#INSCOMP.TXPST { 'Y': 'Yes' }
	calc_perc_sales_tax:
		model:
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Calculate and Transmit % Sales Tax Fields"
			note: '(481-HA/482-GE/483-HE/484-JE)'
			readonly: true
			offscreen: true

	#INSCOMP.TXPST { '03': '03', '15': '15' }
	compound_qualifier_id:
		model:
			source: 'list_ncpdp_ecl'
			sourcefilter:
				field:
					'static': '488-RE'
				code:
					'static': ['03', '15']
			sourceid: 'code'
		view:
			columns: 2
			label: "P/S ID Qualifier For Compound Claims"
			note: '(488-RE)'
			class: 'claim-field'

	compound_sub_clarification_code:
		model:
			source: ['Yes']
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Accept compound partial claims with uncovered ingredients?"
			note: "(420-DK) - Will add '8' modifier to submission clarification codes"

	noncompound_drug_qualifier_id:
		model:
			source: 'list_ncpdp_ecl'
			sourcefilter:
				field:
					'static': '436-E1'
				code:
					'static': ['00', '03']
			sourceid: 'code'
			default: '03'
		view:
			columns: 2
			label: "P/S ID Qualifier For Non-Compound Drug Claims"
			note: '(436-E1)'
			class: 'claim-field'

	service_qualifier_id:
		model:
			source: 'list_ncpdp_ecl'
			sourcefilter:
				field:
					'static': '436-E1'
				code:
					'static': ['00', '06', '07', '08', '09']
			sourceid: 'code'
			default: '08'
		view:
			label: "P/S ID Qualifier For Service Claims"
			note: '(436-E1)'
			class: 'claim-field'
			offscreen: true
			readonly: true

	#INSCOMP.NOCMPD { 'Y': 'Yes' }
	never_comp_seg:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Never Transmit Compound Segment"
			note: 'Segment 10'

	auto_split_noncompound:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			note: 'For non-compounded drugs, i.e. IG'
			label: "Auto Split ingredients?"

	#INSCOMP.IMPORTDIAG { 'Y': 'Yes' }
	send_dx_code:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Import Patient DX code?"
			note: '(424-DO)'

	send_dx_period:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Send Formatted Diagnosis Code?"
			note: 'with periods'

	#INSCOMP.LOSSERVICECODE
	default_los_id:
		model:
			source: 'list_ncpdp_ecl'
			sourcefilter:
				field:
					'static': '418-DI'
				code:
					'static': ['0', '6', '7', '8', '9']
			sourceid: 'code'
		view:
			columns: 2
			label: "Default Level of Service Code (DI)"
			note: "(418-DI)"
			class: 'claim-field'

	#INSCOMP.Revgroup { '1': 'Yes' }
	transmit_grp_no_reversal:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Transmit Group Number in Reversal"
			note: '(301-C1)'

	#INSCOMP.Revcardholder { '1': 'Yes' }
	transmit_card_id_reversal:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Transmit Cardholder ID in Reversal"
			note: '(302-C2)'

	#INSCOMP.DEFRXORIGIN
	default_origin_id:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '419-DJ'
			default: '4'
		view:
			columns: 2
			label: "Default RX Origin (DJ)"
			note: '(419-DJ)'
			class: 'claim-field'

	#INSCOMP.DefPAType
	default_pa_type_id:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '461-EU'
		view:
			columns: 2
			label: "Default Prior Auth Type Code (EU)"
			note: '(461-EU)'
			class: 'claim-field'

	#INSCOMP.Usegrtnpi { '1': 'Yes' }
	send_pharmacist_npi:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Send Pharmacist NPI (E9)"
			note: '(444-E9) - If checked, Site PIC NPI is sent instead of site information'

	#INSCOMP.blankpersoncode { '1': 'Yes' }
	send_blank_person_code:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Send Blank Person Code (C3)"
			note: "(303-C3)"

	swap_uc_and_gross:
		model:
			source: ['Yes']
		view:
			columns: 4
			note: 'If Gross Amount Due (430-DU) greater than U&C (426-DQ), swap values'
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Swap 430-DU and 426-DQ"

	send_pt_email:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Send Patient Email Address"
			note: "350-HN"

	default_service_id:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '147-U7'
				code:
					'static': ['1', '2', '3', '4', '6', '7', '8', '99']
			default: "1"
		view:
			columns: 2
			label: "Default Pharmacy Service Type"
			note: "(147-U7)"
			class: 'claim-field'

	#INSCOMP.DefaultPlaceofResidence
	default_place_of_res_id:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '384-4X'
		view:
			columns: 2
			label: "Default Place of Residence"
			note: "(384-4X)"
			class: 'claim-field'

	#INSCOMP.Sendunitofmeasure { '1': 'Yes' }
	send_uom:
		model:
			source: ['Yes']
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: "Send Unit of Measure"
			note: "(600-28)"

	#INSCOMP.DefaultSalesTaxBasis
	default_tax_base_id:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '484-JE'
				code:
					'static': ['02', '03']
		view:
			columns: 2
			control: 'select'
			label: "Default Tax Basis"
			note: "(484-JE)"
			class: 'claim-field'
			offscreen: true
			readonly: true

	# Secondary / COB

	ncpdp_default_other_coverage_code:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '308-C8'
		view:
			columns: 2
			reference: '308-C8'
			note: '308-C8'
			class: 'claim-field'
			label: 'Other Coverage Code'

	#INSCOMP.Popprimarypaidamountdisabled { '1': 'Yes' }
	ncpdp_send_primary_payer_amt_paid:
		model:
			source: ['Yes']
			default: 'Yes'
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only claim-field'
			label: "Send Primary Payer Amount Paid"
			note: "(431-DV)"

	ncpdp_send_benefits_stage:
		model:
			source: ['Yes']
			default: 'Yes'
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only claim-field'
			label: "Send Benefits Stage"

	ncpdp_send_sec_charge:
		model:
			source: ['Yes']
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only claim-field'
			note: '479-H8/480-H9'
			label: "Send Secondary Charges"

	#INSCOMP.D9secondary { '1': 'Send Zeros', '2': 'Send Primary Claim Amount', '3': 'Send Co-pay Amount' }
	ncpdp_sec_claims_ingred_cost:
		model:
			source: ['Send Zeros', 'Send Primary Claim Amount', 'Send Co-pay Amount']
			default: 'Send Primary Claim Amount'
		view:
			columns: -2
			label: "For Secondary Claims, Ingredient Cost (409-D9) Field, Send"

	ncpdp_sec_claims_gross_amount_due:
		model:
			source: ['Send Zeros', 'Send Primary Claim Amount', 'Send Co-pay Amount']
			default: 'Send Primary Claim Amount'
		view:
			columns: 2
			label: "For Secondary Claims, Gross Amount Due (430-DU) Field, Send"

	#INSCOMP.Sendf5indx { '1': 'Yes'}
	ncpdp_pt_paid_amount_dx:
		model:
			source: ['Yes']
			default: 'Yes'
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only claim-field'
			note: '(505-F5) from Primary Claim Response goes to amount the pharmacy received from the patient (433-DX) on Secondary Claim'
			label: "Patient Paid Amount"

	ncpdp_pt_res_codes:
		model:
			default: '06'
			multi: true
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '351-NP'
		view:
			columns: 2
			reference: '351-NP'
			note: '351-NP'
			class: 'claim-field'
			label: 'Other Payer - Patient Responsibility Entries to Create on Secondary'
			validate: [
				name: 'WarnIf06AndOthers'
			]

	ncpdp_sec_send_other_payer:
		model:
			source: ['Yes']
			default: 'Yes'
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only claim-field'
			label: "Send Other Payer Coverage / ID"
			note: '337-4C'

	ncpdp_e1_prov_required:
		model:
			source: ['Yes']
			default: 'Yes'
		view:
			columns: 2
			control: 'checkbox'
			class: 'checkbox-only'
			note: 'Segment 03'
			label: "Provider segment required to perform E1 checks?"

	subform_contact:
		model:
			multi: true
			source: 'contact'
			type: 'subform'
		view:
			label: 'Contact'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				fields: ['first', 'last', 'phone', 'fax', 'email']
				label: ['First', 'Last', 'Phone', 'Fax', 'Email']
				width: [20, 20, 20, 20, 20]

	assigned_contract_id:
		model:
			source: 'payer_contract'
		view:
			form_link_enabled: true
			label: 'Assigned Contract'
			columns: 2

	embed_document:
		model:
			multi: true
			sourcefilter:
				form_code:
					'dynamic': '{code}'
				form_name:
					'static': 'payer'
		view:
			embed:
				form: 'document'
				selectable: false
				add_preset:
					direct_attachment: 'Yes'
					payer_id: '{code}'
					assigned_to: 'Insurance Company'
					source: 'Scanned Document'
					form_code: '{code}'
					form_name: 'payer'
					form_filter: 'payer'
			grid:
				edit: true
				rank: 'none'
				add: 'flyout'
				fields: ['created_on', 'created_by', 'name', 'file_path']
				label: ['Created On', 'Created By', 'Name', 'File']
				width: [20, 20, 25, 35]
			label: 'Assigned Documents'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['billing']
	indexes:
		unique: [
			['organization', 'short_code']
			['external_id']

		]
		many: [
			['active']
			['code']
			['type_id']
			['billing_method_id']
			['state_id']
			['organization']
			['short_code']
			['inn_oon']
			['pap_program_id']
			['cap_program_id']
			['other_program_id']
			['inn_site_id']
			['mac_id']
			['medicaid_id']
			['hp_id']
			['naic_id']
			['hin_id']
			['coba_id']
			['biller_id']
			['mm_payer_id']
			['mm_payer_name']
			['ncpdp_pharmacy_id']
			['bin']
			['pcn']
			['software_vendor_id']
		]
	name: '{short_code} - {organization} - {inn_oon}'
	sections_group: [
		'General / Billing Info':
			hide_header: true
			indent: false
			tab: 'Billing',
			fields: ['code', 'ss_benefit_id', 'organization', 'short_code', 'phone', 'fax', 'billing_method_id', 'type_id',
			'pap_program_id', 'cap_program_id', 'other_program_id', 'address1', 'address2', 'city', 'state_id', 'zip', 'inn_oon', 'active', 'inn_site_id']

		'Primary Billing Address':
			tab: 'Billing',
			hide_header: true
			indent: false
			fields: ['borganization', 'attn', 'provider_no', 'baddress1', 'baddress2', 'bcity', 'bstate_id',
			'bzip', 'naic_id', 'mac_id', 'medicaid_id', 'hp_id', 'hin_id', 'coba_id', 'box_24j']

		'1500 Options':
			tab: '1500 Options'
			hide_header: true
			indent: false
			fields: ['cms_1','cms_6', 'cms_11', 'cms_4', 'cms_14', 'cms_2', 'cms_3', 'cms_5',
			'cms_7', 'cms_8', 'cms_9', 'cms_12', 'cms_12_qualifier', 'cms_12_id', 'cms_13']

		'Setup Options':
			tab: 'Setup'
			hide_header: true
			indent: false
			fields: ['biller_id', 'collector_id', 'payer_bill_in_hcpc', 'default_coverage', 'days_timely_filing', 'req_auth', 'req_auth_for', 'discarded_meds', 'split_items',
			'split_pa', 'bill_mm_and_ncpdp', 'min_accept_margin', 'auto_adjust_expected', 'auto_transfer_write_off', 'supplementary_payer', 'payer_340b',
			'bill_recur_arrears', 'auto_transfer_adj_id', 'cover_supplies', 'cover_dme', 'max_rental_claims',
			'no_recurring_billing', 'daily_bill_rental', 'always_bill_for_denial', 'factor_allowed_variance']

		'Electronic Medical Claims Setup - Global':
			tab: 'Electronic Medical'
			hide_header: true
			indent: false
			fields: ['mm_default_service_place_id', 'mm_hold_claims_dos_end', 'mm_send_billing_prov_commercial_number', 'mm_billing_commercial_number',
			'mm_send_rendering_prov_commercial_number', 'mm_rendering_commercial_number',
			'mm_auto_transfer_adjustment', 'mm_auto_trans_adj_codes', 'mm_auto_transfer_adjustment_reason',
			'mm_submit_ndc_rx_info', 'mm_sos_only']

		'Electronic Medical Claims Setup - Medical':
			tab: 'Electronic Medical'
			hide_header: true
			indent: false
			fields: ['mm_payer_id', 'mm_payer_name', 'mm_claim_filing_indicator_code',
			'mm_send_contract_pricing', 'mm_calc_perc_sales_tax', 'mm_copy_ordering_md_to_ref',
			'mm_send_upin_supplies', 'mm_response_type']

		'270/Advanced Eligibility (pVerify)':
			tab: 'Electronic Medical'
			hide_header: true
			indent: false
			fields: ['pverify_payer_id']

		'Electronic Pharmacy Claims Setup':
			tab: 'Electronic Pharmacy'
			hide_header: true
			indent: false
			fields: ['ncpdp_pharmacy_qualifier_id', 'ncpdp_pharmacy_pull_id', 'ncpdp_pharmacy_id',
			'pcn', 'bin', 'prescriber_no_field_id', 'pcp_no_field_id', 'default_service_place_id',
			'software_vendor_id', 'default_dispense_fee', 'base_ing_cost_on_id', 'compound_qualifier_id', 'noncompound_drug_qualifier_id',
			'service_qualifier_id', 'default_los_id', 'default_origin_id', 'default_pa_type_id','default_service_id',
			'default_place_of_res_id', 'default_tax_base_id', 'calc_perc_sales_tax',
			'compound_sub_clarification_code', 'never_comp_seg', 'auto_split_noncompound', 'send_dx_code', 'send_dx_period',
			'transmit_grp_no_reversal','transmit_card_id_reversal','swap_uc_and_gross',
			'send_uom', 'send_pharmacist_npi', 'send_blank_person_code', 'send_pt_email']

		'E1/Eligibility':
			tab: 'Electronic Pharmacy'
			hide_header: true
			indent: false
			fields: ['ncpdp_e1_prov_required']
		
		'Electronic Medical Secondary / COB':
			tab: 'COB'
			hide_header: true
			indent: false
			note: 'Settings when plan is listed as the COB payer'
			fields: ['mm_sec_claim_filing_indicator_code', 'mm_sec_payer_id', 'mm_sec_send_identifier', 'mm_sec_id_qualifier', 'mm_sec_id',
			'mm_sec_claims_pr_code']

		'Electronic Pharmacy Secondary / COB':
			tab: 'COB'
			hide_header: true
			indent: false
			note: 'Settings when plan is listed as the COB payer'
			fields: ['ncpdp_default_other_coverage_code','ncpdp_send_primary_payer_amt_paid', 'ncpdp_send_benefits_stage', 'ncpdp_send_sec_charge',
			'ncpdp_sec_claims_ingred_cost', 'ncpdp_sec_claims_gross_amount_due', 'ncpdp_pt_paid_amount_dx', 'ncpdp_pt_res_codes',
			'ncpdp_sec_send_other_payer']

		'Contacts':
			tab: 'Contacts',
			hide_header: true
			indent: false
			fields: ['subform_contact']

		'Pricing':
			tab: 'Pricing',
			hide_header: true
			indent: false
			fields: ['assigned_contract_id']

		'Notes':
			tab: 'Notes',
			hide_header: true
			indent: false
			fields: ['notes']

		'Documents':
			hide_header: true
			indent: false
			fields: ['embed_document']
			tab: 'Assigned Documents'

	]

	transform_post: [
			name: "CreateBillingAccountOnInsert"
	]

view:
	dimensions:
        width: '80%'
        height: '80%'
	hide_cardmenu: true
	comment: 'Insurance Company'
	find:
		basic: ['active', 'inn_oon', 'type_id', 'short_code', 'inn_site_id', 'state_id']
	grid:
		fields: ['organization', 'short_code', 'type_id', 'billing_method_id', 'inn_oon', 'state_id']
		sort: ['-organization']
	label: 'Insurance Company'
	open: 'read'
