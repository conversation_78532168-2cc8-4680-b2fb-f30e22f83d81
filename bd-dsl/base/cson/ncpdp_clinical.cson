fields:
	patient_id:
		model:
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true

	subform_diagnosis:
		model:
			type: 'subform'
			multi: true
			source: 'ncpdp_clinical_dx'
		view:
			grid:
				add: 'inline'
				edit: true
				fields: ['dx_id', 'dx_code']
				label: ['Diagnosis', 'Code']
				width: [80, 20]
			label: 'Diagnosis'
			max_count: 5

	subform_measurement:
		model:
			type: 'subform'
			multi: true
			source: 'ncpdp_clinical_measure'
		view:
			grid:
				add: 'flyout'
				edit: true
				fields: ['measurement_date', 'measurement_time', 'measurement_unit', 'measurement_value']
				label: ['Date', 'Time', 'Unit', 'Val']
				width: [25, 25, 25, 25]
			label: 'Measurement'
			max_count: 5
			transform: [
					name: 'BuildClinicalDescription'
			]
			_meta:
				copy_forward: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	indexes:
		many: [
			['patient_id']
		]

	name: ['patient_id']
	sections_group: [
		'Header':
			hide_header: true
			indent: false
			fields: ['patient_id']
		'Diagnosis':
			indent: false
			note: 'Max 5 Entries'
			fields: ['subform_diagnosis']

		'Measurement':
			indent: false
			note: 'Max 5 Entries'
			fields: ['subform_measurement']
		]

view:
	dimensions:
		width: '90%'
		height: '65%'
	hide_cardmenu: true
	comment: 'Clinical'
	grid:
		fields: ['patient_id']
		sort: ['-created_on']
	label: 'Clinical'
	open: 'read'