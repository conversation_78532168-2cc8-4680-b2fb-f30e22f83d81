fields:
	name:
		model:
			required: true
		view:
			label: 'Name'
			findunique: true
			columns: 2
	code:
		model:
			required: true
		view:
			label: 'Code'
			findunique: true
			columns: 2

	rank:
		model:
			required: true
			type: 'int'
		view:
			columns: 2
			label: 'Rank'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 2
			control: 'radio'
			label: 'Can Sync Record'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			columns: 2
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['billing']
	sync_mode: 'mixed'

	indexes:
		unique: [
			['code']
		]
	name: '{code} - {name}'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['code', 'name', 'rank','allow_sync', 'active']

view:
	comment: 'Manage > COB Response Code Rank'
	find:
		basic: ['name', 'code']
	grid:
		fields: ['code', 'name', 'rank']
		sort: ['name']
	label: 'COB Response Code Rank'
	open: 'read'
