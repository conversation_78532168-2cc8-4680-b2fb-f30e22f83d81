fields:

	site_id:
		model:
			multi: true
			required: true
			source: 'site'
		view:
			columns: 3
			label: 'Assigned Site(s)'
	external_id:
		model:
			type: 'text'
		view:
			columns: 3
			label: 'External ID'
			offscreen: true

	contract_type:
		model:
			source: ['Contract', 'Shared Contract']
		view:
			columns: 3
			label: 'Contract Type'
			control: 'radio'
			readonly: true
			offscreen: true
			validate: [
				{
					name: 'ParentPrefill'
					fields: [
						'price_code_formulas.contract_type',
					]
				}
			]

	name:
		model:
			required: true
		view:
			columns: 3
			label: 'Contract Name'

	price_code_formulas:
		model:
			required: false
			multi: true
			type: 'subform'
			source: 'price_code_formulas'
		view:
			grid:
				add: 'inline'
				edit: true
				hide_cardmenu: true
				fields: ['code_category','expected_price_multiplier',  'expected_price_basis',  'special_price_multiplier', 'special_price_basis', 'active']
				label: ['Price Category', 'Expected Price Multiplier', 'Expected Price Basis', 'Special Price Multiplier', 'Special Price Basis', 'Active']
				width: [25, 15, 15, 15, 15, 15]
			label: 'Pricing Code Formulas'
			note: 'Override Pricing Code Formulas'

	assigned_matrix_id:
		model:
			source: 'payer_price_matrix'
		view:
			columns: 3
			label: 'Assigned Shared Contracts'
			note: 'Pricing Matrix'
			readonly: true
	
	copied_from_matrix_id:
		model:
			source: 'payer_price_matrix'
		view:
			columns: 3
			label: 'Copied From Matrix'
			offscreen: true
			readonly: true

	contract_type_code:
		model:
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'CN101'
		view:
			columns: 3
			label: 'Contract Type Code'
			reference: 'CN101'
			class: 'claim-field'

	contract_code:
		model:
			min: 1
			max: 50
		view:
			columns: 3
			label: 'Contract Code'
			note: 'If available, assigned by the payer'
			reference: 'CN104'
			class: 'claim-field'

	contract_version_identifier:
		model:
			min: 1
			max: 30
		view:
			columns: 3
			label: 'Contract Version ID'
			note: 'If available, assigned by the payer'
			reference: 'CN106'
			class: 'claim-field'

	contract_percentage:
		model:
			type: 'decimal'
			max: 9999.99
			min: 0.00
		view:
			columns: 3
			label: 'Contract %'
			class: 'numeral discount'
			format: 'percent'
			reference: 'CN103'

	terms_discount_percentage:
		model:
			type: 'decimal'
			max: 9999.99
			min: 0.00
		view:
			columns: 3
			label: 'Contract Terms Discount %'
			class: 'numeral discount'
			format: 'percent'
			reference: 'CN105'

	notes:
		view:
			control: 'area'
			label: 'Notes'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['billing']
	indexes:
		unique: [
			['name']
		]
		many: [
			['name']
		]
	reportable: true
	name: '{name}'

	sections:
		'Contract Details':
			fields: ['contract_type', 'name', 'copied_from_matrix_id']
		'Notes':
			fields: ['notes']
		'Shared Contract':
			fields: ['assigned_matrix_id']
		'Contract':
			note: 'Goes into the 2300 loop of the 837p if payer is set to send contract information'
			fields: ['contract_type_code', 'contract_code', 'contract_version_identifier',
			'contract_percentage', 'terms_discount_percentage']
		'Pricing Default Formulas':
			fields: ['price_code_formulas']

view:
	hide_cardmenu: true
	comment: 'Payer > Payer Contract'
	grid:
		fields: ['name', 'notes', 'assigned_matrix_id']
		sort: ['name']
	label: 'Payer Contract'
	open: 'read'
