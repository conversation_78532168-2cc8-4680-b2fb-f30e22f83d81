fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			readonly: true
			offscreen: true

	charge_no:
		model:
			required: false
			type: 'text'
		view:
			label: 'Charge #'
			readonly: true
			offscreen: true
			transform: [
				name: 'GenerateUUID'
			]

	claim_no:
		model:
			required: false
			type: 'text'
		view:
			label: 'Claim #'
			readonly: true
			offscreen: true

	calc_invoice_split_no:
		model:
			required: false
			type: 'text'
		view:
			label: 'Split Invoice #'
			readonly: true
			offscreen: true

	order_rx_id:
		model:
			source: 'careplan_order_rx'
			required: false
			type: 'int'
			if:
				'*':
					readonly:
						fields: ['inventory_id', 'bill_quantity']
		view:
			label: 'Prescription'
			readonly: true
			offscreen: true

	compound_no:
		model:
			type: 'text'
		view:
			label: 'Compound #'
			readonly: true
			offscreen: true

	is_primary_drug_ncpdp:
		model:
			required: false
			source: ['Yes']
			if:
				'Yes':
					fields: ['dispense_fee', 'incv_amt_sub', 'pt_pd_amt_sub']
		view:
			label: 'Is Primary Drug?'
			control: 'checkbox'
			class: 'checkbox-only'
			validate: [
				{
					name: 'CheckPrimaryDrug'
				}
			]

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			readonly: true
			offscreen: true

	rx_no:
		model:
			type: 'text'
		view:
			class: 'claim-field'
			label: 'RX Number'
			readonly: true
			offscreen: true

	inventory_id:
		model:
			source: 'inventory'
			required: true
			query: 'select_inv_in_stock'
			querytemplate: 'inventoryTemplate'
			sourcefilter:
				type:
					'static': ['Drug']
		view:
			label: 'Item'
			columns: 2
			class: 'select_prefill'
			transform: [{
					name: 'UpdateInventoryPricing'
			},
			{
				name: 'SelectPrefill'
				url: '/form/inventory/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'ndc': ['ndc']
					'formatted_ndc': ['formatted_ndc']
					'billing_unit_id': ['billing_unit_id']
			}
			]

	hcpc_code:
		view:
			columns: 4
			label: 'HCPC'
			readonly: true

	gcn_seqno:
		view:
			label: 'GCN Seq No'
			readonly: true
			offscreen: true

	ndc:
		view:
			label: 'NDC'
			readonly: true
			offscreen: true

	formatted_ndc:
		view:
			label: 'NDC'
			columns: 4
			readonly: true

	bill_quantity:
		model:
			min: 1
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
			required: true
		view:
			columns: 4
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Bill Quantity (EA)'
			validate: [
				{
					name: 'UpdateTotals'
				}
			]

	charge_quantity_ea:
		model:
			required: false
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Charge Quantity (EA)'
			readonly: true
			offscreen: true

	charge_quantity:
		model:
			type: 'decimal'
			max: 9999999.999
			rounding: 0.001
		view:
			class: 'numeral claim-field'
			format: '0,0.[000000]'
			columns: 4
			label: 'Metric Quantity'
			readonly: true

	charge_unit:
		model:
			required: true
		view:
			label: 'Charge Unit'
			readonly: true
			offscreen: true
			transform: [{
				name: 'UpdateFieldNote'
				target: 'charge_quantity'
			}
			]

	billing_unit_id:
		model:
			required: true
			source: 'list_unit'
			sourceid: 'code'
			sourcefilter:
					code:
						'static': ['each', 'mL', 'gram']
		view:
			control: 'select'
			label: 'Metric Unit'
			class: 'fdb-field'
			readonly: true
			offscreen: true

	insurance_id:
		model:
			source: 'patient_insurance'
			required: true
		view:
			columns: 2
			control: 'select'
			label: 'Payer'
			readonly: true

	payer_id:
		model:
			source: 'payer'
			required: true
		view:
			label: 'Payer'
			class: 'claim-field'
			readonly: true
			offscreen: true

	billing_method_id:
		model:
			required: true
			source: 'list_billing_method'
			sourceid: 'code'
		view:
			label: 'Billing Method'
			readonly: true
			offscreen: true

	pricing_source:
		model:
			source:
				part_b_asp: "MCR Part B ASP",
				part_b_dme: "MCR Part B Durable Medical Equipment, Prosthetics, Orthotics & Supplies (DMEPOS)",
				payer_contract: 'Payer Contract',
				shared_contract: 'Shared Contract',
				list: 'List Price',
				cob_override: 'COB Override Amount'
			if:
				'payer_contract':
					fields: ['shared_contract_id']
		view:
			columns: 2
			label: 'Pricing Source'
			readonly: true

	shared_contract_id:
		model:
			source: 'payer_contract'
			sourceid: 'id'
		view:
			columns: 2
			label: 'Shared Contract'
			readonly: true

	procm:
		model:
			multi: true
			source: 'list_ncpdp_ext_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '459-ER'
		view:
			class: 'claim-field'
			columns: 2
			max_count: 4
			reference: '459-ER'
			note: '459-ER - Max 4'
			label: 'Procedure Modifier Code(s)'

	billed:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
			min: 0
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Billed Amount'
			validate: [
				{
					name: "CompareValidator"
					fields: [
						"expected"
						"billed"
					]
					require: "lte"
					error: "Billed amount must be greater than or equal to Expected amount"
				},
				{
					name: 'UpdateBilledEa'
				}
			]

	calc_billed_ea:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
			default: 0.00
			min: 0
		view:
			class: 'numeral money'
			format: '$0,0.0000'
			label: 'Billed (EA)'
			readonly: true
			offscreen: true

	dispense_fee:
		model:
			rounding: 0.01
			max: 999999.99
			type: 'decimal'
			min: 0.01
		view:
			class: 'numeral money'
			format: '$0,0.00'
			note: '412-DC'
			columns: 4
			label: 'Dispense Fee'
			validate: [
				{
					name: 'UpdateTotals'
				}
			]

	incv_amt_sub:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			min: 0.00
		view:
			columns: 4
			reference: '438-E3'
			note: '438-E3'
			label: 'Incentive Amount'
			class: 'numeral money'
			format: '$0,0.00'
			validate: [
				{
					name: 'UpdateTotals'
				}
			]

	gross_amount_due:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			min: 0.00
		view:
			columns: 4
			reference: '430-DU'
			note: '430-DU'
			label: 'Gross Amt Due'
			class: 'numeral money'
			format: '$0,0.00'
			readonly: true

	cost_basis:
		model:
			source: 'list_ncpdp_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': '423-DN'
		view:
			columns: -4
			class: 'claim-field'
			reference: '423-DN'
			note: '423-DN'
			label: 'Basis of Cost Determination'

	pt_pd_amt_sub:
		model:
			type: 'decimal'
			rounding: 0.01
			max: 999999.99
			min: 0.00
		view:
			columns: 4
			reference: '433-DX'
			note: '433-DX'
			label: 'Pt Paid Amount'
			class: 'numeral money'
			format: '$0,0.00'

	expected:
		model:
			rounding: 0.01
			type: 'decimal'
			min: 0
		view:
			columns: 4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Expected'
			validate: [
				{
					name: "CompareValidator"
					fields: [
						"expected"
						"billed"
					]
					require: "lte"
					error: "Billed amount must be greater than or equal to Expected amount"
				},
				{
					name: 'UpdateExpectedEa'
				}
			]

	calc_expected_ea:
		model:
			required: true
			rounding: 0.00001
			type: 'decimal'
			min: 0
		view:
			label: 'Expected (EA)' 
			class: 'numeral money'
			format: '$0,0.0000'
			readonly: true
			offscreen: true

	list_price:
		model:
			rounding: 0.01
			type: 'decimal'
			min: 0
		view:
			columns: -4
			class: 'numeral money'
			format: '$0,0.00'
			label: 'Usual / Cust Charge'

	calc_list_ea:
		model:
			rounding: 0.01
			type: 'decimal'
			min: 0
		view:
			class: 'numeral money'
			format: '$0,0.00'
			label: 'List Price (EA)'
			readonly: true
			offscreen: true

	void:
		model:
			source: ['Yes']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Void Charge Line?'
			findfilter: '!Yes'
			class: 'checkbox-only'
			offscreen: true
			readonly: true

model:
	access:
		create:     []
		create_all: []
		delete:     []
		read:       ['admin','pharm','csr','cm', 'nurse']
		read_all:   ['admin','pharm','csr','cm', 'nurse']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      []
	bundle: ['audit']
	indexes:
		unique: [
			['charge_no']
		]
		many: [
			['patient_id']
			['order_rx_id']
			['charge_no']
			['compound_no']
			['site_id']
			['inventory_id']
			['hcpc_code']
			['ndc']
			['billing_unit_id']
			['insurance_id']
			['payer_id']
			['billing_method_id']
			['pricing_source']
			['shared_contract_id']
			['calc_invoice_split_no']
		]
	name: "{patient_id_auto_name} {inventory_id_auto_name} {bill_quantity} ${expected}"
	sections_group: [
		'Item Info':
			hide_header: true
			indent: false
			tab: 'Charge'
			fields: ['claim_no', 'charge_no', 'compound_no', 'site_id', 'inventory_id', 'hcpc_code', 'ndc', 'formatted_ndc', 'procm',
			'bill_quantity', 'charge_quantity', 'charge_quantity_ea', 'charge_unit', 'billing_unit_id']
		'Charge Info':
			indent: false
			tab: 'Charge'
			fields: ['billed', 'calc_billed_ea', 'dispense_fee', 'incv_amt_sub', 'gross_amount_due', 
			'cost_basis', 'pt_pd_amt_sub', 'expected', 'calc_expected_ea',  'list_price',
			'calc_list_ea', 'void']
		'Payer':
			hide_header: true
			indent: false
			tab: 'Payer'
			fields: ['insurance_id', 'payer_id', 'billing_method_id', 'pricing_source', 'shared_contract_id']
		'Primary Drug':
			hide_header: true
			indent: false
			fields: ['is_primary_drug_ncpdp']
			compact: true
	]

view:
	dimensions:
		width: '75%'
		height: '85%'
	hide_cardmenu: true
	comment: 'Charge Line'
	find:
		basic: ['patient_id', 'insurance_id', 'payer_id']
		advanced: ['charge_no']
	grid:
		label: ['Item', 'Quantity', 'Unit', 'Exp $', 'Billed $']
		fields: ['inventory_id', 'bill_quantity', 'charge_unit', 'expected', 'billed']
		width: [30, 15, 15, 20, 20]
		sort: ['-id']
	label: 'Charge Line'
	open: 'edit'
