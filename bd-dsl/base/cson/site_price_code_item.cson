fields:

	price_code_id:
		model:
			source: 'list_price_code'
			sourceid: 'code'
			required: true
		view:
			label: 'Price Code'
			columns: 3

	price_formula_id:
		model:
			source: 'list_price_basis'
			sourceid: 'code'
			required: true
		view:
			label: 'Price Basis'
			columns: 3

	multiplier:
		model:
			type: 'decimal'
			rounding: 0.00001
			default: 1.0
			required: true
		view:
			note: 'Price Basis * Multiplier'
			label: 'Multiplier'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	indexes:
		many: [
			['price_code_id','price_formula_id']
		]
		unique: [
			['price_code_id']
		]
	reportable: true

	name: "{price_code_id} {price_formula_id} - {multiplier}x"
	sections:
		'Site Price Code Item':
			fields: ['price_code_id','price_formula_id', 'multiplier']

view:
	hide_cardmenu: true
	comment: 'Payer > Site Price Code Item'
	grid:
		fields: ['price_code_id','price_formula_id', 'multiplier']
		sort: ['-created_on']
	label: 'Site Price Code Item'
	open: 'read'