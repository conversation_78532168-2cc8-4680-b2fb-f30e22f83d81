fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	sua_levels:
		model:
			prefill:['ongoing_krystexxa','assessment_krystexxa']
			source: ['No', 'Yes']
			required: true
			if:
				'No':
					fields: ['sua_levels_warn', 'sua_levels_warn_ack']
				'Yes':
					fields: ['sua_levels_val']
		view:
			control: 'radio'
			note:'preferably in the last 48 hours'
			label: 'Serum uric acid (sUA) levels were tested?'

	sua_levels_warn:
		model:
			multi: true
			source: ['Do not continue. Notify physician of need to obtain pre-infusion sUA levels prior to next infusion']
		view:
			control: 'checkbox'
			label: 'WARNING'
			class: 'list'
			readonly: true

	sua_levels_warn_ack:
		model:
			required: true
			source: ['Acknowledged']
		view:
			control: 'checkbox'
			label: 'Warning Acknowledgement'

	sua_levels_val:
		model:
			required: true
			rounding: 0.01
			type: 'decimal'
		view:
			label: 'sUA level (mg/dL)'

	sua_levels_val:
		model:
			prefill:['ongoing_krystexxa','assessment_krystexxa']
			subfields:
				date:
					label: 'Date'
					type: 'timestamp'
					readonly: true
				user:
					label: 'User'
					source: '{{user.displayname}}'
					type: 'text'
					readonly: true
				level:
					label: 'sUA level (mg/dL)'
					type: 'text'
			type: 'json'
		view:
			control: 'grid'
			note: 'If pre-infusion sUA is >6mg/dL, contact physician and consider discontinuing Krystexxa therapy, particularly after two pre-infusion SUA labels are >6mg/dL.'
			label: 'sUA Levels (History)'

	urate_reminder:
		model:
			multi: true
			source: ['Remind patient why they are not taking oral urate-lowering therapies and ensure they are taking gout flare prophylaxis']
		view:
			control: 'checkbox'
			label: 'Reminder'
			class: 'list'
			readonly: true

	flares_reminder:
		model:
			multi: true
			source: ['Remind patient that they may have gout flares, and Krystexxa therapy can be continued regardless of gout']
		view:
			control: 'checkbox'
			label: 'Reminder'
			class: 'list'
			readonly: true

	flareups:
		model:
			prefill:['ongoing_krystexxa','assessment_krystexxa']
		view:
			note: 'if any'
			label: 'Frequency of flare-ups'

	symptoms_other:
		model:
			prefill:['ongoing_krystexxa','assessment_krystexxa']
			required: true
		view:
			label: 'Other Details'

	symptoms_notes:
		model:
			prefill:['ongoing_krystexxa','assessment_krystexxa']
		view:
			control: 'area'
			label: 'Notes'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		assessment_krystexxa:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
		ongoing_krystexxa:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Krystexxa Special Alerts':
			area: 'alerts'
			fields: ['sua_levels', 'sua_levels_warn', 'sua_levels_warn_ack', 'sua_levels_val']
		'Gout Symptoms':
			fields: ['flareups', 'symptoms_notes']

view:
	comment: 'Patient > Careplan > Ongoing Assessment > Krystexxa'
	label: 'Ongoing Assessment: Krystexxa'
	open: 'read'
