fields:

	company:
		model:
			required: true
		view:
			label: 'Company'

	industry:
		model:
			source: ['Speciality', 'Infusion', 'DME', 'Hospital', 'payer', 'Manufacturer', 'Other']
			if:
				'Other':
					fields: ['industry_details']
		view:
			control: 'radio'
			label: 'Industry'

	industry_details:
		model:
			required: true
		view:
			label: 'Details'

	size:
		model:
			source: ['Local', 'Regional', 'National']
		view:
			control: 'radio'
			label: 'Size'

	revenue:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			class: 'numeral'
			format:'$0,0.00'
			label: 'Annual Revenue'

	locations:
		model:
			type: 'decimal'
			rounding: 1
		view:
			label: 'Number of Locations'

	patients:
		model:
			type: 'decimal'
			rounding: 1
		view:
			label: 'Number of Active Patients'

	therapies:
		model:
			# multi: true
			source: ['Factor/Hemophilia', 'Hepatitis C', 'IVIG', 'Inotropes', 'Lemtrada', 'Oncology/Chemotherapy', 'TNF', 'TPN', 'Radicava', 'Other']
			if:
				'Other':
					fields: ['therapies_details']
		view:
			control: 'checkbox'
			note: 'Select all that apply'
			label: 'Therapies'

	therapies_details:
		model:
			required: true
		view:
			label: 'Details'

	urac:
		model:
			source: ['No, but looking to', 'No, not interested', 'Yes']
		view:
			control: 'radio'
			label: 'URAC cerified?'

	software:
		model:
			# multi: true
			source: ['Therigy', 'Asembia', 'MHA', 'CPR+', 'Rockpond', 'McKesson', 'QS1', 'New Leaf', 'PioneerRX', 'ScriptMed', 'Salesforce', 'Other']
			if:
				'Other':
					fields: ['software_details']
		view:
			control: 'checkbox'
			note: 'Select all that apply'
			label: 'Current Software'

	software_details:
		model:
			required: true
		view:
			label: 'Details'

	street:
		model:
			max: 128
			min: 4
		view:
			columns: 'addr_1'
			label: 'Street'
			class: "api_prefill"
			transform: [
				name: 'APIPrefill'
				url: 'https://api.radar.io/v1/search/autocomplete?country=US&query='
				display: ['addressLabel','street','city','state','countryCode']
				robj: 'addresses'
				authkey:'radarapi'
				uniqueby: 'formattedAddress'
				fields:
					'street': ['addressLabel']
					'city': ['city']
					'state': ['stateCode']
					'zip': ['postalCode']
					'county': ['county']
			]

	street2:
		model:
			max: 128
		view:
			columns: 'addr_2'
			label: 'Street 2'

	city:
		model:
			max: 128
			min: 1
		view:
			columns: 'addr_city'
			label: 'City'

	county:
		model:
			max: 128
			min: 1
		view:
			label: 'County'
			columns: 'addr_county'

	state:
		model:
			max: 2
			min: 2
			source: 'list_us_state'
			sourceid: 'code'
		view:
			columns: 'addr_state'
			label: 'State'

	zip:
		model:
			max: 10
			min: 5
		view:
			columns: 'addr_zip'
			format: 'us_zip'
			label: 'Zip'
			transform: [
					name: 'CityStateTransform'
					fields:
						zip:'home_zip'
						city:'home_city'
						state:'home_state'
			]

	phone_main:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Main Line'

	phone_fax:
		model:
			max: 21
		view:
			format: 'us_phone'
			label: 'Fax Line'

	description:
		view:
			control: 'area'
			label: 'Description'

model:
	access:
		create:     []
		create_all: ['admin', 'liaison']
		delete:     ['admin', 'liaison']
		read:       ['admin', 'liaison']
		read_all:   ['admin', 'liaison']
		request:    []
		update:     []
		update_all: ['admin', 'liaison']
		write:      ['admin', 'liaison']
	indexes:
		unique: [
			['company']
		]
	name: ['company']
	sections:
		'Prospect Info':
			fields: ['company', 'industry', 'industry_details', 'size', 'revenue', 'locations', 'patients', 'therapies',
			'therapies_details', 'urac', 'software', 'software_details']
		'Address':
			fields: ['street', 'street2', 'zip', 'city', 'state', 'county']
		'Phone':
			fields: ['phone_main', 'phone_fax']
		'Notes':
			fields: ['description']

view:
	comment: 'Manage > Sales Prospect'
	find:
		basic: ['company', 'industry', 'patients', 'therapies', 'urac', 'software']
	grid:
		fields: ['company', 'industry', 'patients', 'therapies', 'urac', 'software']
		sort: ['company']
	label: 'Sales Prospect'
	open: 'read'