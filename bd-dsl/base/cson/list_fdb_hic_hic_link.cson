#TABLE: RHICHCR0_HIC_HIC_LINK
fields:

	code:
		model:
			max: 64
			required: true
		view:
			label: 'Code'

	#HIC_SEQN
	hic_seqn:
		model:
			type: 'int'
		view:
			label: 'Hierarchical Ingredient Code Sequence Number (Stable ID)'
			readonly: true
			columns: 2

	#RELATED_HIC_SEQN
	related_hic_seqn:
		model:
			type: 'int'
		view:
			label: 'Related Hierarchical Ingredient Code Sequence Number'
			readonly: true
			columns: 2


model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['reference']
	sync_mode: 'full'
	name: "{hic_seqn} {related_hic_seqn}"
	indexes:
		many: [
			['related_hic_seqn']
		]
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['hic_seqn', 'related_hic_seqn']

view:
	comment: 'Manage > List FDB HIC_SEQN/HIC_SEQN Link Table'
	find:
		basic: ['hic_seqn', 'related_hic_seqn']
	grid:
		fields: ['hic_seqn', 'related_hic_seqn']
	label: 'List FDB HIC_SEQN/HIC_SEQN Link Table'
