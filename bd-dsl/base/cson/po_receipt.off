fields:

	close_no:
		model:
			required: false
		view:
			label: 'Close No'
			readonly: true

	receipt_no:
		model:
			search: 'A'
		view:
			label: 'Receipt No'
			readonly: true
			offscreen: true
			findunique: true

	po_id:
		model:
			search: 'B'
			required: false
			source: 'po'
		view:
			label: 'Association PO'
			readonly: true
			columns: 2
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/po/?limit=1&fields=list&sort=name&page_number=0&filter=id:'
				fields:
					'site_id': ['site'],
					'supplier_id': ['supplier'],
					'is_340b': ['is_340b'],
					'subform_items':
						'type': 'subform'
						'field': 'subform_items'
						'fields':
							'po_item_id': ['pr.id']
							'inventory_id': ['sf.inventory_id']
							'qty_pkg': ['sf.qty_pkg']
							'ordered': ['sf.total_ordered']
							'package_cost': ['sf.package_cost']
							'item_cost': ['sf.item_cost']
			]

	supplier_id:
		model:
			search: 'B'
			required: false
			source: 'list_supplier'
		view:
			label: 'Supplier'
			readonly: true
			columns: 2

	site_id:
		model:
			required: true
			source: 'site'
			type: 'int'
		view:
			label: 'Site'
			readonly: true
			columns: 4

	order_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			label: 'Ordered Date'
			readonly: true

	received_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			label: 'Received Date'
			template: '{{now}}'

	is_340b:
		model:
			max: 3
			source: ['Yes']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: '340B?'
			columns: 4

	subform_items:
		model:
			multi: true
			source: 'po_receipt_item'
			type: 'subform'
		view:
			label: 'Received Items'
			grid:
				edit: true
				add: 'flyout'
				deleteif:
					po_item_id: '!'
				fields: ['inventory_id', 'ordered', 'confirmed_quantity', 'item_cost', 'total_value']
				label: ['Item', 'Ordered', 'Confirmed', 'Per $', 'Total $']
				width: [40, 15, 15, 15, 15]
			transform: [{
				name: 'MapSubform'
				fields:
					'total_value':
						'transform': 'Sum'
						'fields': ['total_value']
			},
			{
				name: 'MapSubform'
				fields:
					'inventory_id':
						'transform': 'Reduce'
						'fields': ['inventory_id']
			}
			]

	total_value:
		model:
			rounding: 0.01
			type: 'decimal'
			default: 0.00
		view:
			columns: 4
			label: 'Total Value'
			class: 'numeral'
			format:'$0,0.00'
			readonly: true

	quantity_confirmed:
		model:
			required: true
			source: ['Yes']
		view:
			columns: 4
			label: 'Quantity Confirmed'
			control: 'checkbox'
			class: 'checkbox-only'
			readonly: true
			validate: [
				{
					name: 'CheckForPOExceptions'
				}
			]

	has_exceptions:
		model:
			source: ['Yes']
			if:
				'Yes':
					sections: ['Exceptions']
					fields: ['subform_exceptions']
		view:
			label: 'Exception'
			control: 'checkbox'
			class: 'checkbox-only'
			readonly: true
			offscreen: true

	subform_exceptions:
		model:
			multi: true
			source: 'po_receipt_exception'
			type: 'subform'
		view:
			label: 'Items'
			grid:
				edit: true
				add: 'none'
				delete: false
				fields: ['type', 'inventory_id', 'ordered', 'confirmed_quantity', 'supplier_notified', 'manager_notified']
				label: ['Type', 'Item', 'Ordered', 'Confirmed', 'Supplier Notified', 'Manager Notified']
				width: [15, 25, 15, 15, 15, 15]

	po_bog:
		model:
			required: false
			type: 'json'
		view:
			class: 'media-preview'
			control: 'file'
			label: 'Bill of Goods'
			note: 'Max 100MB. Only documents, images, and archives supported.'

	raw_barcode:
		model:
			type: 'text'
			max: 160
		view:
			class: 'barcode barcode-continues'
			columns: 1
			control: 'barcode'
			label: 'Scan Barcode'
			validate: [{
				name: 'BarcodeParseValidator'
				fields:
					raw: 'item_barcode'
					gtin: 'item_barcode'
			}]
			transform: [{
				name: 'ContinuousBarcodePO'
				type: 'barcode-continues'
			}]

	item_barcode:
		model:
			type: 'text'
			max: 160
		view:
			label: 'Item Barcode'
			note: 'GS1/GTIN, UPC, or internal'
			readonly: true
			offscreen: true

	void:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['voided_datetime', 'void_reason_id']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Void Warehouse Receipt?'
			findfilter: '!Yes'
			class: 'checkbox-only'
			validate: [
				{
					name: 'PrefillCurrentDateTime'
					condition:
						void: 'Yes'
					dest: 'voided_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						void: 'Yes'
					dest: 'voided_by'
				}
			]

	void_reason_id:
		model:
			required: true
			source: 'list_void_reason_po'
			sourceid: 'code'
		view:
			label: 'Void Reason'
			columns: 2

	voided_datetime:
		model:
			required: true
			type: 'datetime'
		view:
			columns: 4
			label: 'Voided Date'
			readonly: true

	voided_by:
		model:
			source: 'user'
		view:
			label: 'Voided By'
			readonly: true
			columns: 4

model:
	access:
		create:     ['admin','pharm','csr','cm']
		create_all: ['admin','pharm','csr','cm']
		delete:     []
		read:       ['admin','pharm','csr','cm']
		read_all:   ['admin','pharm','csr','cm']
		request:    []
		review:     []
		update:     []
		update_all: []
		write:      []
	bundle: ['inventory']
	indexes:
		unique: [
			['po_id']
		]
		many: [
			['close_no']
			['receipt_no']
			['site_id']
			['po_id']
			['order_date']
		]
	name: ['receipt_no', 'po_id', 'site_id']
	sections_group: [
		'Details':
			hide_header: true
			indent: false
			fields: ['po_id', 'supplier_id', 'site_id', 'order_date', 'received_date', 'is_340b']
			tab: 'Warehouse Receipt'
		'Received Items':
			hide_header: true
			indent: false
			fields: ['subform_items']
			tab: 'Warehouse Receipt'
		'Confirmation':
			hide_header: true
			indent: false
			fields: ['total_value', 'quantity_confirmed', 'has_exceptions']
			tab: 'Warehouse Receipt'
		'Exceptions':
			hide_header: true
			indent: false
			fields: ['subform_exceptions']
			tab: 'Warehouse Receipt'
		'Barcode Scanner':
			hide_header: true
			indent: false
			fields: ['item_barcode', 'raw_barcode']
			tab: 'Warehouse Receipt'
		'Void':
			modal: true
			fields: ['void', 'voided_datetime', 'voided_by', 'void_reason_id']
		'Bill of Goods':
			hide_header: true
			indent: false
			fields: ['po_bog']
			tab: 'Bill of Goods'
	]

view:
	dimensions:
		width: '90%'
		height: '85%'
	hide_cardmenu: true
	comment: 'Warehouse Receipt'
	find:
		basic: ['receipt_no', 'site_id', 'supplier_id', 'order_date', 'received_date']
	grid:
		fields: ['receipt_no', 'received_date', 'po_id', 'supplier_id', 'site_id', 'total_value']
		sort: ['-id']
	label: 'Warehouse Receipt'
	open: 'edit'