fields:

	insurance_id:
		model:
			type: 'int'
			source: 'patient_insurance'
			prefill: ['parent.insurance_id']
		view:
			label: 'Insurance'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient_insurance/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'medical_relationship_id': ['medical_relationship_id'],
					'group_number': ['group_name', 'group_number'],
					'member_id': ["cardholder_id"],
					'policy_number': ['policy_number'],
					'insurance_type_code': ['insurance_type_code']
			]

	medical_relationship_id:
		model:
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'PAT01'
			if:
				'18': # Self
					fields: ['patient_id']
		view:
			label: 'Relationship to Subscriber'
			readonly: true
			offscreen: true
			validate: [
				name: 'CheckIfDependentRequired'
			]

	patient_id:
		model:
			type: 'int'
			source: 'patient'
		view:
			label: 'Patient'
			readonly: true
			offscreen: true
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/patient/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'first_name': ['firstname'],
					'last_name': ['lastname'],
					'middle_name': ['middlename'],
					'date_of_birth': ['dob'],
					'gender':
						'type': 'conditional'
						'gender':
							if:
								'Male': 'M'
								'Female': 'F'
					'contact_information':
						'type': 'subform'
						'field': 'contact_information'
						'fields':
							'name': '{{pr.firstname}} {{pr.lastname}}',
							'email': ['pr.email']
							'phone_number': ['pr.phone_cell', 'pr.phone_home']
			]

	first_name:
		model:
			min: 1
			max: 35
		view:
			columns: -4
			label: 'First Name'
			reference: 'NM104'
			_meta:
				location: '2010BA NM1'
				field: '04'
				path: 'subscriber.firstName'

	last_name:
		model:
			min: 1
			max: 60
		view:
			columns: 4
			label: 'Last Name'
			reference: 'NM103'
			_meta:
				location: '2010BA NM1'
				field: '03'
				path: 'subscriber.lastName'

	middle_name:
		model:
			min: 1
			max: 25
		view:
			columns: 4
			label: 'Middle Name'
			reference: 'NM105'
			_meta:
				location: '2010BA NM1'
				field: '05'
				path: 'subscriber.middleName'

	suffix:
		model:
			min: 1
			max: 10
		view:
			label: 'Suffix'
			reference: 'NM107'
			offscreen: true
			readonly: true
			_meta:
				location: '2010BA NM1'
				field: '07'
				path: 'subscriber.suffix'

	date_of_birth:
		model:
			type: 'date'
		view:
			columns: -4
			label: 'DOB'
			note: 'Must be in MM/DD/YYYY format'
			reference: 'DMG02'
			_meta:
				location: '2010BA DMG'
				field: '02'
				path: 'subscriber.dateOfBirth'
			validate: [
					name: 'DOBValidator'
			]

	gender:
		model:
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'DMG03'
		view:
			columns: 4
			label: 'Gender'
			reference: 'DMG03'
			_meta:
				location: '2010BA DMG'
				field: '03'
				path: 'subscriber.gender'

	member_id:
		model:
			required: true
			min: 2
			max: 80
		view:
			columns: 4
			label: 'Member ID'
			reference: 'NM109'
			_meta:
				location: '2010BA NM1'
				field: '09'
				path: 'subscriber.memberId'

	policy_number:
		model:
			min: 1
			max: 50
		view:
			columns: 4
			label: 'Policy Number'
			reference: 'SBR03'
			_meta:
				location: '2000B SBR'
				field: '03'
				path: 'subscriber.policyNumber'

	ssn:
		model:
			max: 11
			min: 9
		view:
			format: 'ssn'
			label: 'SSN'
			reference: 'REF02'
			_meta:
				location: '2010BA REF'
				code: 'SY'
				field: '02'
				path: 'subscriber.ssn'
			offscreen: true
			readonly: true

	group_number:
		model:
			min: 1
			max: 50
		view:
			columns: 4
			label: 'Group Number'
			reference: 'SBR04'
			_meta:
				location: '2010BA SBR'
				field: '04'
				path: 'subscriber.groupNumber'

	payment_responsibility_level_code:
		model:
			required: true
			min: 1
			max: 1
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR01'
		view:
			columns: 4
			label: 'Resp Level Code'
			reference: 'SBR01'
			_meta:
				location: '2000B SBR'
				field: '01'
				path: 'subscriber.paymentResponsibilityLevelCode'

	insurance_type_code:
		model:
			required: false
			min: 2
			max: 2
			source: 'list_med_claim_ecl'
			sourceid: 'code'
			sourcefilter:
				field:
					'static': 'SBR05'
		view:
			columns: 4
			label: 'Insurance Type Code'
			reference: 'SBR05'
			_meta:
				location: '2000B SBR'
				field: '05'
				path: 'subscriber.insuranceTypeCode'

	address:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_address_sub'
		view:
			label: 'Address'

	contact_information:
		model:
			required: false
			type: 'subform'
			multi: false
			source: 'med_claim_sub_cont'
		view:
			label: 'Contact Information'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	name:['first_name', 'last_name']
	indexes:
		many: [
			['member_id']
		]

	sections_group: [
		'Subscriber':
			hide_header: true
			sections: [
				'Member Information':
					hide_header: true
					fields: ['insurance_id', 'medical_relationship_id', 'patient_id',
					'first_name', 'last_name', 'middle_name', 'date_of_birth',
					'gender', 'payment_responsibility_level_code', 'insurance_type_code',
					'member_id', 'policy_number', 'group_number', 'ssn']
				'Address':
					hide_header: true
					indent: false
					fields: ['address']
				'Contact Information':
					hide_header: true
					indent: false
					fields: ['contact_information']
			]
	]

view:
	dimensions:
		width: '85%'
		height: '65%'
	hide_cardmenu: true
	reference: '2010BA'
	comment: 'Subscriber'
	hide_header: true
	grid:
		fields: ['member_id', 'policy_number', 'group_number']
		width: [30, 30, 20]
		sort: ['-created_on']
	label: 'Subscriber'
	open: 'read'