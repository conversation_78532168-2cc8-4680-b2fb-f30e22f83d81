-- CM:2024-07-07 - enable this to support consistency checks
-- https://www.postgresql.org/docs/current/amcheck.html

CREATE EXTENSION IF NOT EXISTS amcheck;

-- If a table or index appears corrupt, run the following manually:

-- Check B-Tree index:
-- If this returns an error, you should run the queries below if possible
-- You can try to rebuild indexes with REINDEX INDEX CONCURRENTLY <indexname>;

-- SELECT
--     bt_index_check(indexrelid, TRUE)
-- FROM pg_index i
-- JOIN pg_class c ON c.oid = i.indexrelid
-- JOIN pg_am am ON c.relam = am.oid
-- WHERE am.amname = 'btree'


-- Check B-Tree index in depth:
-- WARNING: This will cause a ton of load with locking on the DB server

-- SELECT
--     bt_index_parent_check(indexrelid, TRUE, TRUE)
-- FROM pg_index i
-- JOIN pg_class c ON c.oid = i.indexrelid
-- JOIN pg_am am ON c.relam = am.oid
-- WHERE am.amname = 'btree'


-- Check Heap:
-- WARNING: This will cause a ton of load with locking on the DB server

-- SELECT
--     verify_heapam(c.oid::regclass, FALSE, TRUE)
-- FROM pg_class c
-- WHERE c.relkind = 'r' -- 'r' stands for ordinary tables
