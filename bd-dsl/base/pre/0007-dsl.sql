--
-- PostgreSQL database dump
--

-- Dumped from database version 14.6
-- Dumped by pg_dump version 14.10 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: dsl; Type: TABLE; Schema: public; Owner: hb
--

CREATE TABLE public.dsl (
    id integer NOT NULL,
    key character varying(64),
    value character varying,
    comment character varying,
    archived boolean,
    code character varying(64),
    updated_by integer,
    updated_on timestamp without time zone,
    created_by integer,
    created_on timestamp without time zone
);


ALTER TABLE public.dsl OWNER TO hb;

--
-- Name: dsl_id_seq; Type: SEQUENCE; Schema: public; Owner: hb
--

CREATE SEQUENCE public.dsl_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.dsl_id_seq OWNER TO hb;

--
-- Name: dsl_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: hb
--

ALTER SEQUENCE public.dsl_id_seq OWNED BY public.dsl.id;


--
-- Name: dsl id; Type: DEFAULT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.dsl ALTER COLUMN id SET DEFAULT nextval('public.dsl_id_seq'::regclass);


--
-- Data for Name: dsl; Type: TABLE DATA; Schema: public; Owner: hb
--

COPY public.dsl (id, key, value, comment, archived, code, updated_by, updated_on, created_by, created_on) FROM stdin;
1	user	{"fields":{"reviewed_on":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"datetime","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"picker","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Reviewed On","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"deleted":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Deleted","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"archived":{"view":{"note":"","transform":[],"readonly":false,"control":"select","format":"","label":"Archived","validate":[],"class":""},"model":{"max":null,"rounding":null,"required":false,"min":null,"prefill":[],"type":"boolean","autoinsert":true,"save":true,"subfields":{},"validate":[],"transform_filter":[],"sourceid":"id","multi":false,"active":true,"transform":[],"if":{},"template":null,"source":null,"default":false,"transform_post":[],"sourcefilter":{},"subfields_sort":[]}},"created_by":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":"user","source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"int","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"select","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Created By","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"id":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"int","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"ID","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"updated_on":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"datetime","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"picker","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Updated On","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"auto_name":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Auto Name","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"change_type":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":[null,"create","read","update","delete"],"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"select","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Change Type","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"change_data":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"json","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Change Request","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"created_on":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"datetime","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"picker","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Created On","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"change_by":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":"user","source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"int","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"select","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Change By","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"updated_by":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":"user","source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"int","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"select","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Updated By","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"change_on":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"datetime","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"picker","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Change On","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"reviewed_by":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":"user","source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"int","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"select","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Reviewed By","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"last_login":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"datetime","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"picker","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Last Login Date/Time","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"last_login_ip":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Last Login IP","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"last_comm_notify":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"datetime","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"picker","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Last Notification Date/Time","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"last_login_location":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Last Login Location","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"job_title":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Job Title","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"firstname":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":32,"min":2,"multi":false,"prefill":[],"required":true,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[{"name":"NameValidator"}],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"First Name","note":"Letters, numbers, comma, ', -, . only","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"lastname":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":32,"min":2,"multi":false,"prefill":[],"required":true,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[{"name":"NameValidator"}],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Last Name","note":"Letters, numbers, comma, ', -, . only","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"displayname":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":64,"min":3,"multi":false,"prefill":[],"required":true,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[{"name":"NameValidator"}],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Display Name","note":"Letters, numbers, comma, ', -, . only","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"username":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":256,"min":3,"multi":false,"prefill":[],"required":true,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[{"name":"LowerCase"}],"transform_filter":[],"transform_post":[],"type":"text","validate":[{"name":"UserNameValidator"}],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"User Name","note":"Letters & numbers only","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"password":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":32,"min":8,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"password","validate":[{"name":"PasswordStrengthValidator"}],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Password","note":"Letters & numbers required","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[{"name":"PasswordMatchValidator","fields":["password","password2"],"error":"Passwords do not match!"}]}},"password2":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":32,"min":8,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":false,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"password","validate":[{"name":"PasswordStrengthValidator"}],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Confirm Password","note":"Letters & numbers required","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[{"name":"PasswordMatchValidator","fields":["password","password2"],"error":"Passwords do not match!"}]}},"password_expiration_date":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"date","validate":[{"name":"DateValidator","require":"future"}],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"picker","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Password Expire Date","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"external_id":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"External ID","note":"ID in external system like CPR+","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"pin":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":4,"min":4,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"password","validate":[{"name":"PinStrengthValidator"}],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Pin","note":"4-digit number only","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[{"name":"PasswordMatchValidator","fields":["pin","pin2"],"error":"Pins do not match!"}]}},"pin2":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":4,"min":4,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":false,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"password","validate":[{"name":"PinStrengthValidator"}],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Confirm Pin","note":"4-digit number only","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[{"name":"PasswordMatchValidator","fields":["pin","pin2"],"error":"Pins do not match!"}]}},"email":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":64,"min":6,"multi":false,"prefill":[],"required":true,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[{"name":"EmailValidator"}],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Email Address","note":"Must be a valid email address","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"image_url":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[{"name":"SecureURLValidator"}],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"User Photo","note":"HTTPS URL link to user photo","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"role":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{"physician":{"fields":[],"note":"","sections":["Pharmacy"],"require_fields":[],"highlight":null}},"max":null,"min":null,"multi":false,"prefill":[],"required":true,"required_all":false,"rounding":null,"save":true,"search":null,"source":{"admin":"Administrator","patient":"Patient","payer":"payer","pharm":"Pharmacist","physician":"Physician","csr":"CSR","nurse":"Nurse","liaison":"Liaison","cma":"Case Management Assistant","cm":"Case Manager","system":"System"},"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"select","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"User Role","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"group_role":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":true,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":{"admin":"Administrator","patient":"Patient","payer":"payer","pharm":"Pharmacist","tech":"Pharm Tech","physician":"Physician","physician_delegate":"Physician Delegate","physician_npi_delegate":"Physician NPI Delegate","supervisor":"Nursing Supervisor","csr":"CSR","nurse":"Nurse","liaison":"Liaison","cma":"Case Management Assistant","cm":"Case Manager","system":"System"},"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"checkbox","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Group Roles","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"sales_code":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Sales Code","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"comm_portal":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":"No","if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":["No","Yes"],"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"radio","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Can receive communication from any Portal user?","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"issue_ticket":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":"No","if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":["No","Yes"],"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"radio","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Able to submit issue tickets?","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"rec_sms":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":"No","if":{"Yes":{"fields":["phone_cell"],"note":"","sections":[],"require_fields":[],"highlight":null}},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":["No","Yes"],"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"radio","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Able to receive SMS messages?","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"initials":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":3,"min":2,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Initials","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"npi":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Pharmacist NPI","note":"Required for some payers","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"phone_cell":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":21,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"us_phone","label":"Cell Phone","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"demo_user":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":["No","Yes"],"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"radio","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Demo User","note":"A demo user loads mock data for the reports when logged in","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"password_reset":{"model":{"access":{"read":[],"if":null,"write":["cm","cma","csr","liaison","nurse","pharm"]},"active":true,"autoinsert":false,"default":"0","if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":false,"search":null,"source":{"0":"No","1":"Yes"},"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"radio","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Password Reset","note":"Select Yes to force a password reset","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"change_password_token":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Change Password Token","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"twofa_enable":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{"Yes":{"fields":["twofa_country_code","twofa_external_id"],"note":"","sections":[],"require_fields":[],"highlight":null}},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":["No","Yes"],"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"radio","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Enable 2-Factor Authentication","note":"User must have a VALID email and phone number on file.","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"twofa_external_id":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"2-Factor Auth Token","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"twofa_country_code":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":1,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":1,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"decimal","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Country Code for 2-Factor Auth","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"authentication_type":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":"password","if":{"password":{"fields":["password","password2","password_reset","password_expiration_date","twofa_enable"],"note":"","sections":[],"require_fields":[],"highlight":null}},"max":null,"min":null,"multi":false,"prefill":[],"required":true,"required_all":false,"rounding":null,"save":true,"search":null,"source":{"password":"Password"},"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"radio","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Authentication Type","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"external_authentication_id":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":255,"min":null,"multi":false,"prefill":[],"required":true,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Authentication ID","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"ext":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Users Extension","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}}},"model":{"access":{"create":["admin"],"create_all":["admin"],"read":["admin","cm","cma","csr","liaison","nurse","pharm"],"read_all":["admin","cm","cma","csr","liaison","nurse","pharm"],"update":["cm","cma","csr","liaison","nurse","pharm"],"update_all":["admin"],"delete":["admin"],"request":[],"review":[],"rereview":[],"write":["admin","cm","cma","csr","liaison","nurse","pharm"]},"bundle":["setup"],"collections":[],"indexes":{"gin":[],"fulltext":["firstname","lastname","displayname","username"],"lower":[],"many":[["username","password"],["firstname","lastname","displayname"],["id","role"],["role","username"],["email"],["role"],["sales_code"]],"unique":[["username"],["email"]]},"name":"{firstname} {lastname} ({job_title})","prefill":{},"reportable":true,"required_if":null,"save":true,"sections_group":[],"sections":{"Name":{"fields":["firstname","lastname","displayname","job_title"],"group":{},"note":"","area":"header","prefill":null},"Access":{"fields":["username","authentication_type","password","password2","password_reset","password_expiration_date","twofa_enable","twofa_country_code","twofa_external_id","external_id","role","group_role","sales_code","comm_portal","issue_ticket","last_login","last_login_ip","last_login_location","last_comm_notify"],"group":{},"note":"","area":"header","prefill":null},"Contact":{"fields":["email","phone_cell","rec_sms"],"group":{},"note":"","area":"header","prefill":null},"Pharmacy":{"fields":["initials","npi"],"group":{},"note":"","area":"header","prefill":null},"Photo":{"fields":["image_url"],"group":{},"note":"","area":"header","prefill":null}},"sections_order":["Name","Access","Contact","Pharmacy","Photo"],"sync_mode":"none","transform":[{"name":"JobTitleTransform","fields":["role","job_title"]}],"transform_filter":[],"transform_post":[{"name":"PermissionGroup"}],"validate":[{"name":"NotMatch","fields":["password","username"],"error":"Password cannot be the same as the username!"}]},"view":{"block":{"print":{"if":null,"except":[]},"update":{"if":null,"except":[]},"validate":[]},"comment":"Manage > Users","find":{"advanced":["displayname","group_role"],"basic":["role","job_title","username","lastname","firstname"]},"grid":{"fields":["role","job_title","username","lastname","firstname","group_role","sales_code","last_login"],"sort":["role","job_title","username","lastname","firstname","group_role","sales_code"],"style":{},"menu":[],"hide_columns":[]},"label":"Users","max_rows":null,"open":"read","transform":[],"validate":[{"name":"PasswordMatchValidator","fields":["password","password2"],"error":"Passwords do not match!"}]}}	\N	\N	\N	\N	2024-03-05 20:55:51.406	\N	2019-07-02 23:38:14.859117
2	coffeedsl	{"fields":{"reviewed_on":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"datetime","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"picker","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Reviewed On","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"deleted":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Deleted","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"archived":{"view":{"note":"","transform":[],"readonly":false,"control":"select","format":"","label":"Archived","validate":[],"class":""},"model":{"max":null,"rounding":null,"required":false,"min":null,"prefill":[],"type":"boolean","autoinsert":true,"save":true,"subfields":{},"validate":[],"transform_filter":[],"sourceid":"id","multi":false,"active":true,"transform":[],"if":{},"template":null,"source":null,"default":false,"transform_post":[],"sourcefilter":{},"subfields_sort":[]}},"created_by":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":"user","source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"int","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"select","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Created By","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"id":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"int","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"ID","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"updated_on":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"datetime","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"picker","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Updated On","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"auto_name":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Auto Name","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"change_type":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":[null,"create","read","update","delete"],"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"select","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Change Type","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"change_data":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"json","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Change Request","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"created_on":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"datetime","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"picker","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Created On","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"change_by":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":"user","source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"int","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"select","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Change By","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"updated_by":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":"user","source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"int","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"select","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Updated By","note":"","offscreen":false,"readonly":true,"template":null,"transform":[],"validate":[]}},"change_on":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"datetime","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"picker","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Change On","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"reviewed_by":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":true,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":"user","source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"int","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"select","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Reviewed By","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"code":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":64,"min":1,"multi":false,"prefill":[],"required":true,"required_all":false,"rounding":null,"save":true,"search":"B","source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[{"name":"LowerCase"}],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Table Name","note":"SINGULAR FORM (use letters, numbers, _ only)","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"form_label":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":"A","source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Form Label","note":"Form Label","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"bundle":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":true,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Bundle","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"data":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":null,"min":null,"multi":false,"prefill":[],"required":true,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"ace_cs","control":"area","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"DSL (.coffee format)","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"comment":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":128,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":"C","source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Comment","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}},"checksum":{"model":{"access":{"read":[],"if":null,"write":[]},"active":true,"autoinsert":false,"default":null,"if":{},"max":128,"min":null,"multi":false,"prefill":[],"required":false,"required_all":false,"rounding":null,"save":true,"search":null,"source":null,"source_order":null,"sourceid":"id","sourcefilter":{},"subfields":{},"subfields_sort":[],"template":null,"transform":[],"transform_filter":[],"transform_post":[],"type":"text","validate":[],"dynamic":{"source":null,"type":"text"}},"view":{"class":"","control":"input","highlight":null,"subform_col_trim":null,"findfilter":null,"gridfields":[],"gridtooltip":[],"gridedit":false,"gridadd":"flyout","findmulti":false,"findunique":false,"findrange":false,"findwildcard":false,"requireall_bypass":false,"requireif_bypass":false,"format":"","label":"Checksum","note":"","offscreen":false,"readonly":false,"template":null,"transform":[],"validate":[]}}},"model":{"access":{"create":["admin"],"create_all":["admin"],"read":["admin"],"read_all":["admin"],"update":[],"update_all":["admin"],"delete":["admin"],"request":[],"review":[],"rereview":[],"write":["admin"]},"bundle":["reference"],"collections":[],"indexes":{"gin":[],"fulltext":["comment","data"],"lower":[],"many":[],"unique":[["code"]]},"name":["code"],"prefill":{},"reportable":false,"required_if":null,"save":true,"sections_group":[],"sections":{"Main":{"fields":["form_label","code","comment","data"],"group":{},"note":"","area":"header","prefill":null}},"sections_order":["Main"],"sync_mode":"none","transform":[],"transform_filter":[],"transform_post":[],"validate":[]},"view":{"block":{"print":{"if":null,"except":[]},"update":{"if":null,"except":[]},"validate":[]},"comment":"Manage > Coffee DSL (Structure of DSL forms)","find":{"advanced":["comment"],"basic":["form_label","code","data"]},"grid":{"fields":["form_label","code","comment","updated_on","updated_by"],"sort":["code","comment"],"style":{},"menu":[],"hide_columns":[]},"label":"~ Coffee DSL","max_rows":null,"open":"read","transform":[],"validate":[]}}	\N	\N	\N	\N	2024-03-05 20:56:10.573	\N	2018-04-12 04:27:09.018785
\.


--
-- Name: dsl_id_seq; Type: SEQUENCE SET; Schema: public; Owner: hb
--

SELECT pg_catalog.setval('public.dsl_id_seq', 3, true);


--
-- Name: dsl dsl_pkey; Type: CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.dsl
    ADD CONSTRAINT dsl_pkey PRIMARY KEY (id);


--
-- PostgreSQL database dump complete
--

