CREATE OR <PERSON><PERSON>LACE FUNCTION crx_get_auto_name(table_name text, column_name text, record anyelement default null,source_filter text default '')
RETURNS text AS $$
DECLARE
    auto_name_value text;
    query text;
BEGIN
    -- Check if the column name is either 'id' or 'code'
    IF column_name <> 'id' AND column_name <> 'code' THEN
        RAISE EXCEPTION 'Unidentified column: %', column_name;
    END IF;

    IF column_name = 'id' THEN
        record := record::int;
    END IF;

    IF source_filter <> '' THEN
        query := format('SELECT auto_name FROM %I WHERE %I = $1 %s', table_name, column_name, TRIM(BOTH '"' FROM source_filter));
    ELSE
        query := format('SELECT auto_name FROM %I WHERE %I = $1', table_name, column_name);
    END IF;

    -- Execute the query
    EXECUTE query INTO auto_name_value USING record;

    RETURN auto_name_value;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

CREATE OR REPLACE FUNCTION crx_set_auto_name(table_name TEXT, row_value RECORD, TG_OP TEXT) RETURNS TEXT AS $$
DECLARE
    template TEXT;
    auto_name TEXT := '';
    template_string TEXT;
    val TEXT;
    start_pos INT;
    end_pos INT;
    aname_legnth INT;
    aname_code TEXT;
    part TEXT;
    last_key_exists BOOLEAN := TRUE;
    ref_table TEXT;
    ref_column TEXT;
    ref_value_column TEXT;
    ref_value TEXT;
    ref_query TEXT;
    id_seq TEXT;
    next_id TEXT;
    where_clause TEXT;
    column_type TEXT;
BEGIN
    -- Remove the prefix 'form_' from the table name to get the aname_code
    aname_code := REGEXP_REPLACE(table_name, '^form_', '');
    id_seq := table_name || '_id_seq';
    aname_legnth := 64;
    -- Retrieve the template from the configuration table
    SELECT table_autoname INTO template
    FROM form_coffeedsl fc
    WHERE fc.code = aname_code;

    IF TG_OP = 'INSERT' THEN
        IF row_value.id IS NOT NULL THEN
            next_id := row_value.id::TEXT;
        ELSE
            EXECUTE 'SELECT (last_value)::TEXT AS next_value FROM ' || id_seq INTO next_id;
        END IF;
    END IF;
    IF TG_OP = 'UPDATE' THEN
        next_id = row_value.id::TEXT;
    END IF;

    IF template IS NULL THEN
        RETURN next_id;
    END IF;

    -- Replace placeholders in the template with actual values from the row_value
    start_pos := POSITION('{' IN template);
    WHILE start_pos > 0 LOOP
        end_pos := POSITION('}' IN template);
        IF end_pos = 0 THEN
            RAISE EXCEPTION 'Unmatched { in template %', template;
        END IF;

        template_string := TRIM(SUBSTRING(template FROM start_pos + 1 FOR end_pos - start_pos - 1));

        -- Check if the template_string contains a reference to another table
        IF POSITION('=' IN template_string) > 0 THEN
            -- Parse the reference
            ref_table := TRIM(SUBSTRING(template_string FROM 1 FOR POSITION('.' IN template_string) - 1));
            ref_column := TRIM(SUBSTRING(template_string FROM POSITION('.' IN template_string) + 1 FOR POSITION('=' IN template_string) - POSITION('.' IN template_string) - 1));

            -- Check if there are additional conditions
            IF POSITION('AND' IN template_string) > 0 THEN
                ref_value_column := TRIM(SUBSTRING(template_string FROM POSITION('=' IN template_string) + 2 FOR POSITION('AND' IN template_string) - POSITION('=' IN template_string) - 2));
                where_clause := TRIM(SUBSTRING(template_string FROM POSITION('AND' IN template_string)));
            ELSE
                ref_value_column := TRIM(SUBSTRING(template_string FROM POSITION('=' IN template_string) + 2));
                where_clause := '';
            END IF;

            -- Get the val of the reference column from the row_value
            EXECUTE 'SELECT ($1).' || ref_value_column INTO ref_value USING row_value;

            IF ref_column = 'id' THEN
                ref_query := 'SELECT ' || ref_table || '.auto_name FROM ' || ref_table || ' WHERE ' || ref_table || '.' || ref_column || ' = $1::INTEGER ' || where_clause;
            ELSE
                ref_query := 'SELECT ' || ref_table || '.auto_name FROM ' || ref_table || ' WHERE ' || ref_table || '.' || ref_column || ' = $1::TEXT ' || where_clause;
            END IF;

            EXECUTE ref_query INTO val USING ref_value;
        ELSE
            -- Check if the template_string exists in the row_value
            BEGIN

                EXECUTE format(
                    'SELECT data_type FROM information_schema.columns 
                     WHERE table_name = %L AND column_name = %L',
                    table_name, template_string
                ) INTO column_type;

                EXECUTE 'SELECT ($1).' || template_string INTO val USING row_value;
                -- Format datetime values to AM/PM format
                IF column_type IN ('timestamp', 'timestamp without time zone', 'timestamp with time zone') 
                   AND val IS NOT NULL THEN
                    val := to_char(val::timestamp, 'MM/DD/YYYY HH12:MI AM');
                END IF;
                -- handle case of multi true array
            EXCEPTION WHEN OTHERS THEN
                val := ''; -- Default val if the template_string does not exist
            END;
        END IF;

        -- Append the part before the template_string
        IF start_pos > 1 THEN
            part := SUBSTRING(template FROM 1 FOR start_pos - 1);
        ELSE
            part := '';
        END IF;

        -- Append the part, val, if the val exists
        IF val IS NOT NULL AND val <> '' THEN
            auto_name := auto_name || part || val;
            last_key_exists := TRUE;
        ELSE
            last_key_exists := FALSE;
        END IF;

        -- Remove the processed part from the template
        template := SUBSTRING(template FROM end_pos + 1);
        start_pos := POSITION('{' IN template);
    END LOOP;

    -- Append any remaining part of the template if the last template_string exists
    IF last_key_exists THEN
        auto_name := auto_name || template;
    END IF;

    -- Remove any trailing delimiters and empty parentheses
    auto_name := TRIM(REGEXP_REPLACE(auto_name, '(\s*-\s*)+(\(\s*\))?$', ''));
    IF auto_name = '' OR auto_name IS NULL THEN
        return next_id;
    END IF;
    IF LENGTH(auto_name) > aname_legnth THEN
        auto_name := LEFT(auto_name, aname_legnth - 3) || '...';
    END IF;
    RETURN auto_name;
END;
$$ LANGUAGE plpgsql;
