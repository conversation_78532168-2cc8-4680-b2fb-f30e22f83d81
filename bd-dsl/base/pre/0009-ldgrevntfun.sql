CREATE OR R<PERSON>LACE FUNCTION crx_ledger_event() RETURNS TRIGGER AS $$
DECLARE
    job_data jsonb;
    job_name text;
    is_old_value_valid boolean := TRUE;
    is_new_value_valid boolean := TRUE;
BEGIN
  IF TG_WHEN != 'AFTER' OR TG_LEVEL != 'ROW' THEN
    RAISE TRIGGER_PROTOCOL_VIOLATED USING
    MESSAGE = 'function "crx_ledger_event" must be fired AFTER ROW';
  END IF;

  -- Check if new and old values are valid JSON
  BEGIN
    PERFORM NEW.old_value::jsonb;
    EXCEPTION WHEN invalid_text_representation THEN
    is_old_value_valid := FALSE;
  END;

  BEGIN
    PERFORM NEW.new_value::jsonb;
    EXCEPTION WHEN invalid_text_representation THEN
    is_new_value_valid := FALSE;
  END;

  -- Raise an error if either old_value or new_value is not a valid JSON
  IF NOT is_old_value_valid THEN
    RAISE EXCEPTION 'old_value is not a valid JSON: %', NEW.old_value;
  END IF;

  IF NOT is_new_value_valid THEN
    RAISE EXCEPTION 'new_value is not a valid JSON: %', NEW.new_value;
  END IF;

  -- Check if event_name is text and is not empty
  IF pg_typeof(NEW.event_name) NOT IN ('text'::regtype, 'char'::regtype, 'character varying'::regtype) OR NEW.event_name IS NULL OR NEW.event_name = '' THEN
    RAISE EXCEPTION 'event_name must be a non-empty text value: %', NEW.event_name;
  END IF;

  IF TG_OP != 'INSERT' AND TG_OP != 'UPDATE' THEN
    RAISE TRIGGER_PROTOCOL_VIOLATED USING
    MESSAGE = 'function "crx_ledger_event" must be fired for INSERT or UPDATE';
  END IF;

  -- Create JSON with keys table_name, old_data, new_data
  job_name := NEW.event_name;
  job_data := jsonb_build_object(
    'old_value', to_jsonb(NEW.old_value::json),
    'new_value', to_jsonb(NEW.new_value::json),
    'entity', NEW.entity
  );

  INSERT INTO pgboss.job (
    name,
    data
  ) VALUES (
    job_name,
    job_data
  );
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;