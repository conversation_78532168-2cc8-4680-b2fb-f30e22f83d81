CREATE OR REPLACE FUNCTION update_patient_rank() 
RETURNS TRIGGER AS $$
DECLARE
    max_rank INT;
    existing_rank INT;
    query TEXT;
    table_name TEXT := TG_ARGV[0];
BEGIN
    -- Construct the query to get the maximum rank
    query := 'SELECT MAX(rank) FROM ' || table_name ||
             ' WHERE patient_id = $1 AND careplan_id = $2 AND (archived IS NULL OR archived IS FALSE) AND (deleted IS NULL OR deleted IS FALSE)';

    -- Execute the query
    EXECUTE query INTO max_rank USING NEW.patient_id, NEW.careplan_id;

    -- If rank is NULL
    IF NEW.rank IS NULL THEN
        -- Set rank to max_rank + 1
        NEW.rank := COALESCE(max_rank, 0) + 1;
        -- Prepend the rank value to the existing auto_name
        NEW.auto_name := NEW.rank::INT::TEXT || NEW.auto_name;
    ELSE
        -- Construct the query to check the existing rank
        query := 'SELECT rank FROM ' || table_name ||
                 ' WHERE patient_id = $1 AND careplan_id = $2 AND rank = $3 AND (archived IS NULL OR archived IS FALSE) AND (deleted IS NULL OR deleted IS FALSE)';

        -- Execute the query
        EXECUTE query INTO existing_rank USING NEW.patient_id, NEW.careplan_id, NEW.rank;

        -- If the rank exists, set rank to max_rank + 1
        IF existing_rank IS NOT NULL THEN
            NEW.rank := max_rank + 1;
            -- Prepend the rank value to the existing auto_name
            NEW.auto_name := NEW.rank::INT::TEXT || NEW.auto_name;
        ELSE
            -- Otherwise, retain the input value for rank
            NEW.rank := NEW.rank;
        END IF;
    END IF;

    -- Handle archived or deleted records
    IF NEW.archived IS TRUE OR NEW.deleted IS TRUE THEN
        NEW.rank := NULL;
    END IF;


    RETURN NEW;

END;
$$ LANGUAGE plpgsql;
