DROP FUNCTION IF EXISTS next_number;
CREATE OR <PERSON><PERSON>LACE FUNCTION public.crx_next_number(document_text text, doc_date date DEFAULT CURRENT_DATE)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    next_seq text;
    next_number INTEGER;
    row_data record;
    sequence record;
    counter int := 0;
BEGIN
    -- Take an advisory lock on the series name
    PERFORM pg_advisory_xact_lock(hashtext(document_text));

    SELECT id,
           last_number_used,
           prefix,
           series,
           suffix,
           digit_length
    INTO   row_data
    FROM   form_number_series
    WHERE  series = document_text
    AND    active != 'No'
    AND    COALESCE(archived, false) IS NOT TRUE
    AND    COALESCE(deleted, false) IS NOT TRUE
    AND    upper(sys_period) IS NULL
    AND    (
        (start_date IS NULL AND end_date IS NULL)
        OR
        (start_date IS NULL AND doc_date <= end_date)
        OR
        (end_date IS NULL AND doc_date >= start_date)
        OR
        (doc_date BETWEEN start_date AND end_date)
    )
    LIMIT 1;

    -- handle falsy value
    IF row_data.id is NULL THEN
        RAISE 'PG Fun crx_next_number error: No Record Found Params series: "%", doc_date "%"', document_text, doc_date;
    END IF;

    counter := 0;
    IF row_data.last_number_used IS NULL THEN
        next_number := 0;
    ELSE
        next_number := row_data.last_number_used;
    END IF;

    LOOP
        counter = counter + 1;
        IF counter >= 100 THEN
            RAISE 'PG Fun crx_next_number error: Counter limit reached. Failed to make unique next Number';
        END IF;
        next_number := next_number + 1;
        next_seq := next_number::text;
        IF row_data.digit_length IS NOT NULL AND LENGTH(next_seq) < row_data.digit_length THEN
            next_seq := LPAD(next_number::text, row_data.digit_length, '0');
        END IF;

        -- Concatenate parts and TODO caolesce to ensure NULL issues don't happen
        next_seq := COALESCE(row_data.prefix,'') || next_seq || COALESCE(row_data.suffix,'');

        SELECT COUNT(1) > 0 as found
        INTO sequence
        FROM form_number_series
        WHERE last_sequence_number_used = next_seq
        AND series = document_text
        AND upper(sys_period) IS NULL;

        IF sequence.found = FALSE THEN
            UPDATE form_number_series
            SET    last_number_used = next_number,
                   last_sequence_number_used = next_seq
            WHERE  id = row_data.id
            AND    upper(sys_period) IS NULL;
            RETURN next_seq;
        END IF;
    END LOOP;
END;
$function$
