--
-- PostgreSQL database dump
--

-- Dumped from database version 14.6
-- Dumped by pg_dump version 14.10 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: form_coffeedsl; Type: TABLE; Schema: public; Owner: hb
--

CREATE TABLE public.form_coffeedsl (
    archived boolean,
    change_type character varying(150),
    change_by integer,
    reviewed_by integer,
    deleted boolean,
    created_by integer,
    auto_name character varying,
    created_on timestamp without time zone,
    reviewed_on timestamp without time zone,
    change_on timestamp without time zone,
    updated_on timestamp without time zone,
    change_data text,
    id integer NOT NULL,
    updated_by integer,
    comment character varying(128),
    code character varying(64),
    checksum character varying(128),
    data character varying,
    sys_period tstzrange DEFAULT tstzrange(CURRENT_TIMESTAMP, NULL::timestamp with time zone) NOT NULL,
    form_label character varying(10000),
    bundle character varying(10000),
    search tsvector GENERATED ALWAYS AS ((((setweight(to_tsvector('english'::regconfig, (COALESCE(code, ''::character varying))::text), 'B'::"char") || setweight(to_tsvector('english'::regconfig, (COALESCE(form_label, ''::character varying))::text), 'A'::"char")) || setweight(to_tsvector('english'::regconfig, (COALESCE(comment, ''::character varying))::text), 'C'::"char")) || setweight(to_tsvector('english'::regconfig, (COALESCE(auto_name, ''::character varying))::text), 'D'::"char"))) STORED
);


ALTER TABLE public.form_coffeedsl OWNER TO hb;

--
-- Name: form_coffeedsl_id_seq; Type: SEQUENCE; Schema: public; Owner: hb
--

CREATE SEQUENCE public.form_coffeedsl_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.form_coffeedsl_id_seq OWNER TO hb;

--
-- Name: form_coffeedsl_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: hb
--

ALTER SEQUENCE public.form_coffeedsl_id_seq OWNED BY public.form_coffeedsl.id;


--
-- Name: form_coffeedsl id; Type: DEFAULT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_coffeedsl ALTER COLUMN id SET DEFAULT nextval('public.form_coffeedsl_id_seq'::regclass);


--
-- Data for Name: form_coffeedsl; Type: TABLE DATA; Schema: public; Owner: hb
--

COPY public.form_coffeedsl (archived, change_type, change_by, reviewed_by, deleted, created_by, auto_name, created_on, reviewed_on, change_on, updated_on, change_data, id, updated_by, comment, code, checksum, data, sys_period, form_label, bundle) FROM stdin;
f	\N	\N	\N	f	\N		2018-04-12 04:30:42.491128	\N	\N	2024-03-05 20:55:52	\N	1	1	Manage > Users	user	d3f74f0c8ef6facedec4009cd46b27cfc9fb14e3	fields:\n\n\tlast_login:\n\t\tmodel:\n\t\t\ttype: 'datetime'\n\t\tview:\n\t\t\tlabel: 'Last Login Date/Time'\n\t\t\treadonly: true\n\n\tlast_login_ip:\n\t\tmodel:\n\t\t\ttype: 'text'\n\t\tview:\n\t\t\tlabel: 'Last Login IP'\n\t\t\treadonly: true\n\n\tlast_comm_notify:\n\t\tmodel:\n\t\t\ttype: 'datetime'\n\t\tview:\n\t\t\tlabel: 'Last Notification Date/Time'\n\t\t\treadonly: true\n\n\tlast_login_location:\n\t\tmodel:\n\t\t\ttype: 'text'\n\t\tview:\n\t\t\tlabel: 'Last Login Location'\n\t\t\treadonly: true\n\n\tjob_title:\n\t\tmodel:\n\t\t\ttype: 'text'\n\t\tview:\n\t\t\tlabel: 'Job Title'\n\n\tfirstname:\n\t\tmodel:\n\t\t\tmax: 32\n\t\t\tmin: 2\n\t\t\trequired: true\n\t\t\tvalidate: [\n\t\t\t\t\tname: 'NameValidator'\n\t\t\t]\n\t\tview:\n\t\t\tlabel: 'First Name'\n\t\t\tnote: 'Letters, numbers, comma, \\', -, . only'\n\n\tlastname:\n\t\tmodel:\n\t\t\tmax: 32\n\t\t\tmin: 2\n\t\t\trequired: true\n\t\t\tvalidate: [\n\t\t\t\t\tname: 'NameValidator'\n\t\t\t]\n\t\tview:\n\t\t\tlabel: 'Last Name'\n\t\t\tnote: 'Letters, numbers, comma, \\', -, . only'\n\n\tdisplayname:\n\t\tmodel:\n\t\t\tmax: 64\n\t\t\tmin: 3\n\t\t\trequired: true\n\t\t\tvalidate: [\n\t\t\t\t\tname: 'NameValidator'\n\t\t\t]\n\t\tview:\n\t\t\tlabel: 'Display Name'\n\t\t\tnote: 'Letters, numbers, comma, \\', -, . only'\n\n\tusername:\n\t\tmodel:\n\t\t\tmax: 256\n\t\t\tmin: 3\n\t\t\trequired: true\n\t\t\ttransform:[\n\t\t\t\tname: 'LowerCase'\n\t\t\t]\n\t\t\tvalidate: [\n\t\t\t\t\tname: 'UserNameValidator'\n\t\t\t]\n\t\tview:\n\t\t\tlabel: 'User Name'\n\t\t\tnote: 'Letters & numbers only'\n\n\tpassword:\n\t\tmodel:\n\t\t\tmax: 32\n\t\t\tmin: 8\n\t\t\ttype: 'password'\n\t\t\tvalidate: [\n\t\t\t\t\tname: 'PasswordStrengthValidator'\n\t\t\t]\n\t\tview:\n\t\t\tlabel: 'Password'\n\t\t\tnote: 'Letters & numbers required'\n\t\t\tvalidate: [\n\t\t\t\t\tname: 'PasswordMatchValidator'\n\t\t\t\t\tfields: ['password', 'password2']\n\t\t\t\t\terror: 'Passwords do not match!'\n\t\t\t]\n\n\tpassword2:\n\t\tmodel:\n\t\t\tmax: 32\n\t\t\tmin: 8\n\t\t\tsave: false\n\t\t\ttype: 'password'\n\t\t\tvalidate: [\n\t\t\t\t\tname: 'PasswordStrengthValidator'\n\t\t\t]\n\t\tview:\n\t\t\tlabel: 'Confirm Password'\n\t\t\tnote: 'Letters & numbers required'\n\t\t\tvalidate: [\n\t\t\t\t\tname: 'PasswordMatchValidator'\n\t\t\t\t\tfields: ['password', 'password2']\n\t\t\t\t\terror: 'Passwords do not match!'\n\t\t\t]\n\n\tpassword_expiration_date:\n\t\tmodel:\n\t\t\ttype: 'date'\n\t\t\tvalidate:[\n\t\t\t\tname: 'DateValidator'\n\t\t\t\trequire: 'future'\n\t\t\t]\n\t\tview:\n\t\t\tlabel: 'Password Expire Date'\n\n\texternal_id:\n\t\tview:\n\t\t\tnote: 'ID in external system like CPR+'\n\t\t\tlabel: 'External ID'\n\n\tpin:\n\t\tmodel:\n\t\t\tmax: 4\n\t\t\tmin: 4\n\t\t\ttype: 'password'\n\t\t\tvalidate: [\n\t\t\t\t\tname: 'PinStrengthValidator'\n\t\t\t]\n\t\tview:\n\t\t\tlabel: 'Pin'\n\t\t\tnote: '4-digit number only'\n\t\t\tvalidate: [\n\t\t\t\t\tname: 'PasswordMatchValidator'\n\t\t\t\t\tfields: ['pin', 'pin2']\n\t\t\t\t\terror: 'Pins do not match!'\n\t\t\t]\n\n\tpin2:\n\t\tmodel:\n\t\t\tmax: 4\n\t\t\tmin: 4\n\t\t\tsave: false\n\t\t\ttype: 'password'\n\t\t\tvalidate: [\n\t\t\t\t\tname: 'PinStrengthValidator'\n\t\t\t]\n\t\tview:\n\t\t\tlabel: 'Confirm Pin'\n\t\t\tnote: '4-digit number only'\n\t\t\tvalidate: [\n\t\t\t\t\tname: 'PasswordMatchValidator'\n\t\t\t\t\tfields: ['pin', 'pin2']\n\t\t\t\t\terror: 'Pins do not match!'\n\t\t\t]\n\n\temail:\n\t\tmodel:\n\t\t\tmax: 64\n\t\t\tmin: 6\n\t\t\trequired: true\n\t\t\tvalidate: [\n\t\t\t\t\tname: 'EmailValidator'\n\t\t\t]\n\t\tview:\n\t\t\tlabel: 'Email Address'\n\t\t\tnote: 'Must be a valid email address'\n\n\timage_url:\n\t\tmodel:\n\t\t\tvalidate: [\n\t\t\t\t\tname: 'SecureURLValidator'\n\t\t\t]\n\t\tview:\n\t\t\tlabel: 'User Photo'\n\t\t\tnote: 'HTTPS URL link to user photo'\n\n\trole:\n\t\tmodel:\n\t\t\trequired: true\n\t\t\tsource:\n\t\t\t\t'admin':     'Administrator'\n\t\t\t\t'patient':   'Patient'\n\t\t\t\t'payer':     'payer'\n\t\t\t\t'pharm':     'Pharmacist'\n\t\t\t\t'physician': 'Physician'\n\t\t\t\t'csr':       'CSR'\n\t\t\t\t'nurse':     'Nurse'\n\t\t\t\t'liaison':   'Liaison'\n\t\t\t\t'cma':       'Case Management Assistant'\n\t\t\t\t'cm':        'Case Manager'\n\t\t\t\t'system':    'System'\n\t\t\tif:\n\t\t\t\t'physician':\n\t\t\t\t\tsections: ['Pharmacy']\n\t\tview:\n\t\t\tlabel: 'User Role'\n\n\tgroup_role:\n\t\tmodel:\n\t\t\tmulti: true\n\t\t\tsource:\n\t\t\t\t'admin':     'Administrator'\n\t\t\t\t'patient':   'Patient'\n\t\t\t\t'payer':     'payer'\n\t\t\t\t'pharm':     'Pharmacist'\n\t\t\t\t'tech':      'Pharm Tech'\n\t\t\t\t'physician': 'Physician'\n\t\t\t\t'physician_delegate': 'Physician Delegate'\n\t\t\t\t'physician_npi_delegate': 'Physician NPI Delegate'\n\t\t\t\t'supervisor': 'Nursing Supervisor'\n\t\t\t\t'csr':       'CSR'\n\t\t\t\t'nurse':     'Nurse'\n\t\t\t\t'liaison':   'Liaison'\n\t\t\t\t'cma':       'Case Management Assistant'\n\t\t\t\t'cm':        'Case Manager'\n\t\t\t\t'system':    'System'\n\t\tview:\n\t\t\tlabel: 'Group Roles'\n\t\t\tcontrol: 'checkbox'\n\n\tsales_code:\n\t\tmodel:\n\t\t\ttype: 'text'\n\t\tview:\n\t\t\tlabel: 'Sales Code'\n\n\tcomm_portal:\n\t\tmodel:\n\t\t\tdefault: 'No'\n\t\t\trequired: false\n\t\t\tsource: ['No', 'Yes']\n\t\tview:\n\t\t\tcontrol: 'radio'\n\t\t\tlabel: 'Can receive communication from any Portal user?'\n\n\tissue_ticket:\n\t\tmodel:\n\t\t\tdefault: 'No'\n\t\t\trequired: false\n\t\t\tsource: ['No', 'Yes']\n\t\tview:\n\t\t\tcontrol: 'radio'\n\t\t\tlabel: 'Able to submit issue tickets?'\n\n\trec_sms:\n\t\tmodel:\n\t\t\tdefault: 'No'\n\t\t\trequired: false\n\t\t\tsource: ['No', 'Yes']\n\t\t\tif:\n\t\t\t\t'Yes':\n\t\t\t\t\tfields: ['phone_cell']\n\t\tview:\n\t\t\tcontrol: 'radio'\n\t\t\tlabel: 'Able to receive SMS messages?'\n\n\tinitials:\n\t\tmodel:\n\t\t\tmax: 3\n\t\t\tmin: 2\n\t\t\ttype: 'text'\n\t\tview:\n\t\t\tlabel: 'Initials'\n\n\tnpi:\n\t\tmodel:\n\t\t\ttype: 'text'\n\t\tview:\n\t\t\tnote: 'Required for some payers'\n\t\t\tlabel: 'Pharmacist NPI'\n\n\tphone_cell:\n\t\tmodel:\n\t\t\tmax: 21\n\t\tview:\n\t\t\tformat: 'us_phone'\n\t\t\tlabel: 'Cell Phone'\n\n\tdemo_user:\n\t\tmodel:\n\t\t\trequired: false\n\t\t\tsource: ['No', 'Yes']\n\t\tview:\n\t\t\tcontrol: 'radio'\n\t\t\tnote:  'A demo user loads mock data for the reports when logged in'\n\t\t\tlabel: 'Demo User'\n\n\tpassword_reset:\n\t\tmodel:\n\t\t\taccess:\n\t\t\t\twrite: ['cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']\n\t\t\tsave: false\n\t\t\tsource:\n\t\t\t\t'0': 'No'\n\t\t\t\t'1': 'Yes'\n\t\t\tdefault: '0'\n\t\tview:\n\t\t\tcontrol: 'radio'\n\t\t\tlabel: 'Password Reset'\n\t\t\tnote: 'Select Yes to force a password reset'\n\n\tchange_password_token:\n\t\tmodel:\n\t\t\ttype: 'text'\n\t\tview:\n\t\t\tlabel: 'Change Password Token'\n\t\t\tcontrol: 'input'\n\n\ttwofa_enable:\n\t\tmodel:\n\t\t\trequired: false\n\t\t\tsource: ['No', 'Yes']\n\t\t\tif:\n\t\t\t\t'Yes':\n\t\t\t\t\tfields: ['twofa_country_code', 'twofa_external_id']\n\t\tview:\n\t\t\tcontrol: 'radio'\n\t\t\tnote:  'User must have a VALID email and phone number on file.'\n\t\t\tlabel: 'Enable 2-Factor Authentication'\n\n\ttwofa_external_id:\n\t\tmodel:\n\t\t\trequired: false\n\t\t\ttype: 'text'\n\t\tview:\n\t\t\tlabel: '2-Factor Auth Token'\n\t\t\treadonly: true\n\n\ttwofa_country_code:\n\t\tmodel:\n\t\t\trequired: false\n\t\t\ttype: 'decimal'\n\t\t\trounding: 1\n\t\t\tdefault: 1\n\t\tview:\n\t\t\tlabel: 'Country Code for 2-Factor Auth'\n\n\tauthentication_type:\n\t\tmodel:\n\t\t\trequired: true\n\t\t\tdefault: 'password'\n\t\t\tsource:\n\t\t\t\t'password': 'Password'\n\t\t\tif:\n\t\t\t\t'password':\n\t\t\t\t\tfields: ['password', 'password2', 'password_reset', 'password_expiration_date', 'twofa_enable']\n\t\tview:\n\t\t\tcontrol: 'radio'\n\t\t\tlabel: 'Authentication Type'\n\n\texternal_authentication_id:\n\t\tmodel:\n\t\t\trequired: true\n\t\t\ttype: 'text'\n\t\t\tmax: 255\n\t\tview:\n\t\t\tlabel: 'Authentication ID'\n\n\text:\n\t\tmodel:\n\t\t\ttype: 'text'\n\t\tview:\n\t\t\tlabel: 'Users Extension'\n\nmodel:\n\taccess:\n\t\tcreate:     []\n\t\tcreate_all: ['admin']\n\t\tdelete:     ['admin']\n\t\tread:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']\n\t\tread_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']\n\t\trequest:    []\n\t\tupdate:     ['cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']\n\t\tupdate_all: ['admin']\n\t\twrite:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']\n\tbundle: ['setup']\n\tindexes:\n\t\tfulltext: ['firstname', 'lastname', 'displayname', 'username']\n\t\tmany: [\n\t\t\t['username', 'password']\n\t\t\t['firstname', 'lastname', 'displayname']\n\t\t\t['id', 'role']\n\t\t\t['role', 'username']\n\t\t\t['email']\n\t\t\t['role']\n\t\t\t['sales_code']\n\t\t]\n\t\tunique: [\n\t\t\t['username']\n\t\t\t['email']\n\t\t]\n\tname: '{firstname} {lastname} ({job_title})'\n\treportable: true\n\tsections:\n\t\t'Name':\n\t\t\tfields: ['firstname', 'lastname', 'displayname', 'job_title']\n\t\t'Access':\n\t\t\tfields: ['username', 'authentication_type', 'password', 'password2', 'password_reset', 'password_expiration_date', 'twofa_enable', 'twofa_country_code',\n\t\t\t'twofa_external_id', 'external_id', 'role', 'group_role', 'sales_code', 'comm_portal', 'issue_ticket', 'last_login', 'last_login_ip', 'last_login_location', 'last_comm_notify']\n\t\t'Contact':\n\t\t\tfields: ['email', 'phone_cell', 'rec_sms']\n\t\t'Pharmacy':\n\t\t\tfields: ['initials', 'npi']\n\t\t'Photo':\n\t\t\tfields: ['image_url']\n\ttransform: [\n\t\t\tname: 'JobTitleTransform'\n\t\t\tfields: [ 'role' , 'job_title' ]\n\t\t# ,\n\t\t# \tname: 'GenerateTokenIf'\n\t\t# \targuments:\n\t\t# \t\tqualifier: 'password_reset'\n\t\t# \t\ttoken_field: 'change_password_token'\n\t\t# ,\n\t\t# \tname: 'OnUpdateSetFieldIf'\n\t\t# \targuments:\n\t\t# \t\tqualifier: 'password'\n\t\t# \t\tfield: 'password_reset'\n\t\t# \t\tvalue: 0\n\t\t# ,\n\t\t# \tname: 'OnUpdateSetFieldIf'\n\t\t# \targuments:\n\t\t# \t\tqualifier: 'password'\n\t\t# \t\tfield: 'change_password_token'\n\t\t# \t\tvalue: null\n\t\t# ,\n\t\t# \tname: 'OnUpdateIf'\n\t\t# \targuments:\n\t\t# \t\tqualifier: 'password'\n\t\t# \t\tfun: 'delete_session'\n\t\t# \t\tfield: 'id'\n\t]\n\ttransform_post: [\n\t\tname: 'PermissionGroup'\n\t]\n\tvalidate: [\n\t\t\tname: 'NotMatch'\n\t\t\tfields: ['password', 'username']\n\t\t\terror: 'Password cannot be the same as the username!'\n\t]\n\nview:\n\tcomment: 'Manage > Users'\n\tfind:\n\t\tbasic: ['role', 'job_title', 'username', 'lastname', 'firstname']\n\t\tadvanced: ['displayname', 'group_role']\n\tgrid:\n\t\tfields: ['role', 'job_title', 'username', 'lastname', 'firstname', 'group_role', 'sales_code', 'last_login']\n\t\tsort: ['role', 'job_title', 'username', 'lastname', 'firstname', 'group_role', 'sales_code']\n\tlabel: 'Users'\n\tvalidate: [\n\t\t\tname: 'PasswordMatchValidator'\n\t\t\tfields: ['password', 'password2']\n\t\t\terror: 'Passwords do not match!'\n\t]	["2024-03-05 20:55:52.148647+00",)	Users	{setup}
\.


--
-- Name: form_coffeedsl_id_seq; Type: SEQUENCE SET; Schema: public; Owner: hb
--

SELECT pg_catalog.setval('public.form_coffeedsl_id_seq', 2, true);


--
-- Name: form_coffeedsl form_coffeedsl_pkey; Type: CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_coffeedsl
    ADD CONSTRAINT form_coffeedsl_pkey PRIMARY KEY (id);


--
-- Name: form_coffeedsl idx6807d9951572149cf0e7e6ca65c8d693591e45dae15271af669ba0fc; Type: CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_coffeedsl
    ADD CONSTRAINT idx6807d9951572149cf0e7e6ca65c8d693591e45dae15271af669ba0fc UNIQUE (code);


--
-- Name: idx4ef920172ffa770e49de989ac858810eea39dec16d76b312e132f9d3; Type: INDEX; Schema: public; Owner: hb
--

CREATE INDEX idx4ef920172ffa770e49de989ac858810eea39dec16d76b312e132f9d3 ON public.form_coffeedsl USING btree (archived);


--
-- Name: idx5f997140eb7a05b3e2962f2abf6d56f67f8df723fa4ddb69a4c2f3d5; Type: INDEX; Schema: public; Owner: hb
--

CREATE INDEX idx5f997140eb7a05b3e2962f2abf6d56f67f8df723fa4ddb69a4c2f3d5 ON public.form_coffeedsl USING btree (deleted);


--
-- Name: idx_form_coffeedsl_search; Type: INDEX; Schema: public; Owner: hb
--

CREATE INDEX idx_form_coffeedsl_search ON public.form_coffeedsl USING gin (search);

--
-- Name: create log coffee table
--

ALTER TABLE IF EXISTS public.form_coffeedsl ADD COLUMN IF NOT EXISTS sys_period tstzrange NOT NULL DEFAULT tstzrange (current_timestamp, NULL);
CREATE TABLE IF NOT EXISTS public.log_coffee (LIKE public.form_coffeedsl);
CREATE OR REPLACE TRIGGER crx_versioning_trigger AFTER
INSERT
OR
UPDATE
OR
DELETE
ON public.form_coffeedsl FOR EACH ROW EXECUTE PROCEDURE public.crx_versioning('sys_period', 'log_coffee', TRUE);


--
-- Name: form_coffeedsl form_coffeedsl_change_by_fk; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_coffeedsl
    ADD CONSTRAINT form_coffeedsl_change_by_fk FOREIGN KEY (change_by) REFERENCES public.form_user(id);


--
-- Name: form_coffeedsl form_coffeedsl_change_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_coffeedsl
    ADD CONSTRAINT form_coffeedsl_change_by_fkey FOREIGN KEY (change_by) REFERENCES public.form_user(id);


--
-- Name: form_coffeedsl form_coffeedsl_created_by_fk; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_coffeedsl
    ADD CONSTRAINT form_coffeedsl_created_by_fk FOREIGN KEY (created_by) REFERENCES public.form_user(id);


--
-- Name: form_coffeedsl form_coffeedsl_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_coffeedsl
    ADD CONSTRAINT form_coffeedsl_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.form_user(id);


--
-- Name: form_coffeedsl form_coffeedsl_reviewed_by_fk; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_coffeedsl
    ADD CONSTRAINT form_coffeedsl_reviewed_by_fk FOREIGN KEY (reviewed_by) REFERENCES public.form_user(id);


--
-- Name: form_coffeedsl form_coffeedsl_reviewed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_coffeedsl
    ADD CONSTRAINT form_coffeedsl_reviewed_by_fkey FOREIGN KEY (reviewed_by) REFERENCES public.form_user(id);


--
-- Name: form_coffeedsl form_coffeedsl_updated_by_fk; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_coffeedsl
    ADD CONSTRAINT form_coffeedsl_updated_by_fk FOREIGN KEY (updated_by) REFERENCES public.form_user(id);


--
-- Name: form_coffeedsl form_coffeedsl_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_coffeedsl
    ADD CONSTRAINT form_coffeedsl_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.form_user(id);