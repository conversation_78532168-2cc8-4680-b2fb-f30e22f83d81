
CREATE OR REPLACE FUNCTION crx_pre_upsert_fun() RETURNS TRIGGER AS $$
DECLARE
  time_stamp_to_use timestamptz;
  manipulate jsonb;
  history_table text;
  sys_period text;
BEGIN
    sys_period := TG_ARGV[0];
    history_table := TG_ARGV[1];
    IF to_regclass(history_table) IS NULL THEN
        RAISE 'relation "%" does not exist', history_table;
    END IF;
    IF NOT EXISTS(SELECT * FROM pg_attribute WHERE attrelid = history_table::regclass AND attname = sys_period AND NOT attisdropped) THEN
      RAISE 'history relation "%" does not contain system period column "%"', history_table, sys_period USING
      HINT = 'history relation must contain system period column with the same name and data type as the versioned one';
    END IF;
    IF TG_OP = 'UPDATE' OR TG_OP = 'INSERT' THEN
      IF history_table LIKE 'log_%' THEN
          NEW.auto_name := crx_set_auto_name(TG_TABLE_NAME, NEW, TG_OP);
      END IF;
      time_stamp_to_use := CURRENT_TIMESTAMP;
      manipulate := jsonb_set('{}'::jsonb, ('{' || sys_period || '}')::text[], to_jsonb(tstzrange(time_stamp_to_use, null, '[)')));
      RETURN jsonb_populate_record(NEW, manipulate);
    END IF;
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;