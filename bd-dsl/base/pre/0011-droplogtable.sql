CREATE OR REPLACE FUNCTION recreate_log_table_and_trigger(table_name TEXT) RETURNS VOID AS $$
DECLARE
    log_table_name TEXT;
    prefix TEXT := 'form_';
    pLog TEXT := 'log_';
BEGIN
    log_table_name := pLog || substring(
        table_name
        FROM
            LENGTH(prefix) + 1
    );
    EXECUTE format('DROP TABLE IF EXISTS %I;', log_table_name);
    EXECUTE format(
        'CREATE TABLE IF NOT EXISTS %I (LIKE %I);',
        log_table_name,
        table_name
    );
END;
$$ LANGUAGE plpgsql;