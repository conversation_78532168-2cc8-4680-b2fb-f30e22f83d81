--
-- PostgreSQL database dump
--

-- Dumped from database version 14.6
-- Dumped by pg_dump version 14.10 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: form_user; Type: TABLE; Schema: public; Owner: hb
--

CREATE TABLE public.form_user (
    id integer NOT NULL,
    reviewed_by integer,
    created_on timestamp without time zone,
    change_on timestamp without time zone,
    change_by integer,
    archived boolean,
    created_by integer,
    auto_name character varying,
    updated_by integer,
    deleted boolean,
    change_type character varying(150),
    reviewed_on timestamp without time zone,
    updated_on timestamp without time zone,
    change_data text,
    password_expiration_date date,
    demo_user character varying(133),
    pin character varying(128),
    change_password_token character varying,
    role character varying(167),
    email character varying(64),
    job_title character varying,
    username character varying(256),
    displayname character varying(64),
    firstname character varying(32),
    lastname character varying(32),
    password character varying(128),
    external_id character varying,
    comm_portal character varying(133),
    image_url character varying,
    workflow_role character varying(179),
    sales_code character varying,
    rec_sms character varying(133),
    phone_cell character varying(21),
    issue_ticket character varying(133),
    twofa_external_id character varying,
    twofa_country_code numeric(16,4),
    external_authentication_id character varying(255),
    authentication_type character varying(8),
    twofa_enable character varying(133),
    last_login timestamp without time zone,
    last_login_ip character varying,
    last_login_location character varying,
    last_comm_notify timestamp without time zone,
    ext character varying,
    password2 character varying(255),
    pin2 character varying(255),
    password_reset character varying(10000),
    sys_period tstzrange DEFAULT tstzrange(CURRENT_TIMESTAMP, NULL::timestamp with time zone) NOT NULL,
    initials character varying(10000),
    npi character varying(10000)
);


ALTER TABLE public.form_user OWNER TO hb;

--
-- Name: form_user_id_seq; Type: SEQUENCE; Schema: public; Owner: hb
--

CREATE SEQUENCE public.form_user_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.form_user_id_seq OWNER TO hb;

--
-- Name: form_user_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: hb
--

ALTER SEQUENCE public.form_user_id_seq OWNED BY public.form_user.id;


--
-- Name: form_user id; Type: DEFAULT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_user ALTER COLUMN id SET DEFAULT nextval('public.form_user_id_seq'::regclass);


--
-- Data for Name: form_user; Type: TABLE DATA; Schema: public; Owner: hb
--

COPY public.form_user (id, reviewed_by, created_on, change_on, change_by, archived, created_by, auto_name, updated_by, deleted, change_type, reviewed_on, updated_on, change_data, password_expiration_date, demo_user, pin, change_password_token, role, email, job_title, username, displayname, firstname, lastname, password, external_id, comm_portal, image_url, workflow_role, sales_code, rec_sms, phone_cell, issue_ticket, twofa_external_id, twofa_country_code, external_authentication_id, authentication_type, twofa_enable, last_login, last_login_ip, last_login_location, last_comm_notify, ext, password2, pin2, password_reset, sys_period, initials, npi) FROM stdin;
1	\N	2019-08-26 14:33:28.719477	\N	\N	f	1	cadmin	1	f	\N	\N	2024-03-06 18:58:59	\N	\N	\N	\N	\N	admin	<EMAIL>	Admin	cadmin	ClaraAdmin	Clara	Admin	$p5k2$$7ip3spnoc$ltWl0PkNGvackncOVXfs6DtE1C7p3Jkj	\N	No	\N	\N	\N	No	\N	No	\N	\N	\N	password	\N	2024-03-06 18:58:59.557		\N	\N	\N	\N	\N	1	["2024-03-06 18:58:59.715024+00",)	\N	\N
2	\N	2019-08-26 14:33:28.719477	\N	\N	f	1	ctest	1	f	\N	\N	2024-03-06 18:58:59	\N	\N	\N	\N	\N	admin	<EMAIL>	Admin	ctest	ClaraTest	Clara	Test	$p5k2$$vke43di7e$fneIQ58RvELGNKDfzaIRQbF4zYSO9Ces	\N	No	\N	\N	\N	No	\N	No	\N	\N	\N	password	\N	2024-03-06 18:58:59.557		\N	\N	\N	\N	\N	1	["2024-03-06 18:58:59.715024+00",)	\N	\N
\.

--
-- Name: form_user_id_seq; Type: SEQUENCE SET; Schema: public; Owner: hb
--

SELECT pg_catalog.setval('public.form_user_id_seq', 1, true);


--
-- Name: form_user form_user_pkey; Type: CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_user
    ADD CONSTRAINT form_user_pkey PRIMARY KEY (id);


--
-- Name: form_user idx3775d5fac5c32e0cf181a896fc95caedb1e6c165b06b5439f6601a20; Type: CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_user
    ADD CONSTRAINT idx3775d5fac5c32e0cf181a896fc95caedb1e6c165b06b5439f6601a20 UNIQUE (username);


--
-- Name: form_user idx815a23ad9a39207a13906c516aee9b4fcc0819b37dc9b8beaacec54b; Type: CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_user
    ADD CONSTRAINT idx815a23ad9a39207a13906c516aee9b4fcc0819b37dc9b8beaacec54b UNIQUE (email);


--
-- Name: idx21fcae63e353643035c89b7b0c1a03aa82c175365a188c5d5e3b4e2a; Type: INDEX; Schema: public; Owner: hb
--

CREATE INDEX idx21fcae63e353643035c89b7b0c1a03aa82c175365a188c5d5e3b4e2a ON public.form_user USING btree (role, username);


--
-- Name: idx34ec60744a4a9a1ec3389580998be5c06bbbaf975524b0783816ac21; Type: INDEX; Schema: public; Owner: hb
--

CREATE INDEX idx34ec60744a4a9a1ec3389580998be5c06bbbaf975524b0783816ac21 ON public.form_user USING btree (deleted);


--
-- Name: idx49142041d6b3bcf5faa7c29d184ac9d5ff5fd42bfe2fe6a719988542; Type: INDEX; Schema: public; Owner: hb
--

CREATE INDEX idx49142041d6b3bcf5faa7c29d184ac9d5ff5fd42bfe2fe6a719988542 ON public.form_user USING btree (email);


--
-- Name: idx850515818f6b8e4fba76af2ecf9c5bcc6399414a032a1dffeb1e5acf; Type: INDEX; Schema: public; Owner: hb
--

CREATE INDEX idx850515818f6b8e4fba76af2ecf9c5bcc6399414a032a1dffeb1e5acf ON public.form_user USING btree (password, username);


--
-- Name: idx98d90d8920ae507f118bfa402b6bccab1ef1318221d21fc9eb5a7360; Type: INDEX; Schema: public; Owner: hb
--

CREATE INDEX idx98d90d8920ae507f118bfa402b6bccab1ef1318221d21fc9eb5a7360 ON public.form_user USING btree (role);


--
-- Name: idx9e75e4b34d5b0fee4e2f801ab5f26da1c9f6a6968cbcae0a72375bf8; Type: INDEX; Schema: public; Owner: hb
--

CREATE INDEX idx9e75e4b34d5b0fee4e2f801ab5f26da1c9f6a6968cbcae0a72375bf8 ON public.form_user USING btree (sales_code);


--
-- Name: idxab507f924b29feec68252c493b69cc9920e69a2cfd50353d8b71dc5d; Type: INDEX; Schema: public; Owner: hb
--

CREATE INDEX idxab507f924b29feec68252c493b69cc9920e69a2cfd50353d8b71dc5d ON public.form_user USING btree (archived);


--
-- Name: idxb9b180cf79904175f0395c097b99f70d6e89eb860c3afea88cea9285; Type: INDEX; Schema: public; Owner: hb
--

CREATE INDEX idxb9b180cf79904175f0395c097b99f70d6e89eb860c3afea88cea9285 ON public.form_user USING btree (displayname, firstname, lastname);


--
-- Name: idxd14a028c2a3a2bc9476102bb288234c415a2b01f828ea62ac5b3e42f; Type: INDEX; Schema: public; Owner: hb
--

CREATE UNIQUE INDEX idxd14a028c2a3a2bc9476102bb288234c415a2b01f828ea62ac5b3e42f ON public.form_user USING btree (username);


--
-- Name: idxdd71c95c49570750724c2d045773e91d5c30f50d09917d7e38a5a1cf; Type: INDEX; Schema: public; Owner: hb
--

CREATE INDEX idxdd71c95c49570750724c2d045773e91d5c30f50d09917d7e38a5a1cf ON public.form_user USING btree (id, role);

--
-- Name: create log user table
--

ALTER TABLE IF EXISTS public.form_user ADD COLUMN IF NOT EXISTS sys_period tstzrange NOT NULL DEFAULT tstzrange (current_timestamp, NULL);
CREATE TABLE IF NOT EXISTS public.log_user (LIKE public.form_user);
CREATE OR REPLACE TRIGGER crx_versioning_trigger AFTER
INSERT
OR
UPDATE
OR
DELETE
ON public.form_user FOR EACH ROW EXECUTE PROCEDURE public.crx_versioning('sys_period', 'log_user', TRUE);

--
-- Name: form_user form_user_change_by_fk; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_user
    ADD CONSTRAINT form_user_change_by_fk FOREIGN KEY (change_by) REFERENCES public.form_user(id);


--
-- Name: form_user form_user_change_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_user
    ADD CONSTRAINT form_user_change_by_fkey FOREIGN KEY (change_by) REFERENCES public.form_user(id);


--
-- Name: form_user form_user_created_by_fk; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_user
    ADD CONSTRAINT form_user_created_by_fk FOREIGN KEY (created_by) REFERENCES public.form_user(id);


--
-- Name: form_user form_user_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_user
    ADD CONSTRAINT form_user_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.form_user(id);


--
-- Name: form_user form_user_reviewed_by_fk; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_user
    ADD CONSTRAINT form_user_reviewed_by_fk FOREIGN KEY (reviewed_by) REFERENCES public.form_user(id);


--
-- Name: form_user form_user_reviewed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_user
    ADD CONSTRAINT form_user_reviewed_by_fkey FOREIGN KEY (reviewed_by) REFERENCES public.form_user(id);


--
-- Name: form_user form_user_updated_by_fk; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_user
    ADD CONSTRAINT form_user_updated_by_fk FOREIGN KEY (updated_by) REFERENCES public.form_user(id);


--
-- Name: form_user form_user_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: hb
--

ALTER TABLE ONLY public.form_user
    ADD CONSTRAINT form_user_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.form_user(id);


--
-- PostgreSQL database dump complete
--

