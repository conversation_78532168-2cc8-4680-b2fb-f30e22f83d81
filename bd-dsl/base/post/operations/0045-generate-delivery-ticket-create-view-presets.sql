
DO $$ BEGIN
  PERFORM drop_all_function_signatures('create_delivery_ticket_create_view_presets');
END $$;
CREATE OR REPLACE FUNCTION create_delivery_ticket_create_view_presets(
  p_rx_id integer
) RETURNS json AS $BODY$
DECLARE
    v_params jsonb;
    v_supply_kit_id integer;
    v_service_from date;
    v_service_to date;
    v_site_id integer;
    v_patient_id integer;
    v_working_dispense_id integer;
    v_working_supply_order_ids integer[];
    v_results json;
BEGIN

    RAISE LOG 'create_delivery_ticket_create_view_presets: Creating delivery ticket creation view presets for Rx ID: %', p_rx_id;
    -- Build parameters JSON for logging
    v_params := jsonb_build_object(
        'p_rx_id', p_rx_id
    );

    IF p_rx_id IS NULL THEN
        INSERT INTO dispensing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Missing Rx ID',
            'Exception in create_delivery_ticket_create_view_presets',
            'FUNCTION',
            current_schema(),
            'view_create_dt',
            v_params
        );
      RAISE EXCEPTION 'Rx ID is required to generate delivery ticket presets';
    END IF;

    SELECT
        st.service_from,
        st.service_to
    INTO v_service_from,
         v_service_to
    FROM calc_delivery_ticket_service_dates(p_rx_id) st;

    SELECT supply_kit_id,
           site_id,
           patient_id
    INTO v_supply_kit_id,
         v_site_id,
         v_patient_id
    FROM vw_rx_order rx
    WHERE rx.rx_id = p_rx_id;

    SELECT working_dispense_id
    INTO v_working_dispense_id
    FROM vw_rx_order rx
    WHERE rx.rx_id = p_rx_id;

    SELECT json_agg(jsonb_build_object('id', osi.id))
    INTO v_working_supply_order_ids
    FROM form_careplan_orders_item osi
    WHERE osi.associated_rx_id = p_rx_id;

    RAISE LOG 'create_delivery_ticket_create_view_presets: Supply kit ID: %, Site ID: %, Patient ID: %', v_supply_kit_id, v_site_id, v_patient_id;

    SELECT jsonb_build_object(
        'rx_id', p_rx_id,
        'patient_id', v_patient_id,
        'site_id', v_site_id,
        'service_from', CASE WHEN v_service_from IS NOT NULL THEN TO_CHAR(v_service_from, 'MM/DD/YYYY') ELSE NULL END,
        'service_to', CASE WHEN v_service_to IS NOT NULL THEN TO_CHAR(v_service_to, 'MM/DD/YYYY') ELSE NULL END,
        'supply_kit_id', v_supply_kit_id,
        'dispense_prescription', json_build_array(jsonb_build_object('id',v_working_dispense_id)),
        'dispense_sorder', v_working_supply_order_ids
    ) INTO v_results;

    RETURN v_results;

    EXCEPTION WHEN OTHERS THEN
        -- Log error
        INSERT INTO dispensing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in create_delivery_ticket_create_view_presets',
            'FUNCTION',
            current_schema(),
            'view_create_dt',
            v_params
        );
        
    RAISE WARNING 'Failed to generate delivery ticket assessment presets: %', SQLERRM;
    RETURN NULL;
END;
$BODY$ LANGUAGE plpgsql;