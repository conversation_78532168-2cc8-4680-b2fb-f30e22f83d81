CREATE OR REPLACE FUNCTION ncpdp_record_to_json(
  p_ncpdp_claim ncpdp_record
) RETURNS json AS $$
DECLARE
  v_ncpdp_json json := NULL;
  v_patient_json json := NULL;
  v_prescriber_json json := NULL;
  v_pharmacy_json json := NULL;
  v_insurance_json json := NULL;
  v_claim_json json := NULL;
  v_pricing_json json := NULL;
  v_clinical_json json := NULL;
  v_narrative_json json := NULL;
  v_cob_json json := NULL;
  v_dur_json json := NULL;
BEGIN
  -- Return NULL if input is NULL
  IF p_ncpdp_claim IS NULL THEN
    RETURN NULL;
  END IF;
  
  -- Process patient segment if not null
  IF p_ncpdp_claim.segment_patient IS NOT NULL THEN
    BEGIN
      -- Access individual fields directly to avoid composite type string conversion
      WITH patient_fields AS (
        SELECT 
          ROW_NUMBER() OVER () as patient_row,
          p.transaction_code,
          p.patient_id,
          p.insurance_id,
          p.pt_rel_code,
          p.payer_type_id,
          p.patient_id_qualifier,
          p.patient_claim_id,
          p.patient_date_of_birth,
          p.patient_gender_code,
          p.patient_first_name,
          p.patient_last_name,
          p.patient_phone,
          p.patient_email_address,
          p.patient_street_address,
          p.patient_city_address,
          p.patient_state,
          p.patient_zip,
          p.place_of_service,
          p.patient_residence,
          p.pregnancy_indicator
        FROM unnest(p_ncpdp_claim.segment_patient) p
      )
      SELECT json_agg(
        json_build_object(
          'transaction_code', transaction_code,
          'patient_id', patient_id,
          'insurance_id', insurance_id,
          'pt_rel_code', pt_rel_code,
          'payer_type_id', payer_type_id,
          'patient_id_qualifier', patient_id_qualifier,
          'patient_claim_id', patient_claim_id,
          'patient_date_of_birth', patient_date_of_birth,
          'patient_gender_code', patient_gender_code,
          'patient_first_name', patient_first_name,
          'patient_last_name', patient_last_name,
          'patient_phone', patient_phone,
          'patient_email_address', patient_email_address,
          'patient_street_address', patient_street_address,
          'patient_city_address', patient_city_address,
          'patient_state', patient_state,
          'patient_zip', patient_zip,
          'place_of_service', place_of_service,
          'patient_residence', patient_residence,
          'pregnancy_indicator', pregnancy_indicator
        )
      ) INTO v_patient_json
      FROM patient_fields;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in patient segment: %', SQLERRM;
      -- Simpler approach for patient segment
      BEGIN
        SELECT json_agg(
          json_build_object(
            'transaction_code', COALESCE(p.transaction_code, ''),
            'patient_id', COALESCE(p.patient_id, 0),
            'insurance_id', COALESCE(p.insurance_id, 0),
            'patient_first_name', COALESCE(p.patient_first_name, ''),
            'patient_last_name', COALESCE(p.patient_last_name, '')
          )
        ) INTO v_patient_json
        FROM unnest(p_ncpdp_claim.segment_patient) p;
        
        IF v_patient_json IS NULL THEN
          v_patient_json := json_build_array(
            json_build_object(
              'error', 'Could not parse patient segment'
            )
          );
        END IF;
      EXCEPTION WHEN OTHERS THEN
        v_patient_json := NULL;
      END;
    END;
  END IF;

  -- Process prescriber segment if not null
  IF p_ncpdp_claim.segment_prescriber IS NOT NULL THEN
    BEGIN
      -- Access individual fields directly
      WITH prescriber_fields AS (
        SELECT 
          ROW_NUMBER() OVER () as prescriber_row,
          p.transaction_code,
          p.physician_id,
          p.dr_id_qualifier,
          p.dr_id,
          p.dr_last_name,
          p.dr_first_name,
          p.dr_street_address,
          p.dr_city,
          p.dr_state,
          p.dr_zip,
          p.dr_phone,
          p.primary_physician_id,
          p.pri_dr_id_qualifier,
          p.pri_dr_id,
          p.pri_dr_last_name
        FROM unnest(p_ncpdp_claim.segment_prescriber) p
      )
      SELECT json_agg(
        json_build_object(
          'transaction_code', transaction_code,
          'physician_id', physician_id,
          'dr_id_qualifier', dr_id_qualifier,
          'dr_id', dr_id,
          'dr_last_name', dr_last_name,
          'dr_first_name', dr_first_name,
          'dr_street_address', dr_street_address,
          'dr_city', dr_city,
          'dr_state', dr_state,
          'dr_zip', dr_zip,
          'dr_phone', dr_phone,
          'primary_physician_id', primary_physician_id,
          'pri_dr_id_qualifier', pri_dr_id_qualifier,
          'pri_dr_id', pri_dr_id,
          'pri_dr_last_name', pri_dr_last_name
        )
      ) INTO v_prescriber_json
      FROM prescriber_fields;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in prescriber segment: %', SQLERRM;
      -- Simpler approach for prescriber segment
      BEGIN
        SELECT json_agg(
          json_build_object(
            'transaction_code', COALESCE(p.transaction_code, ''),
            'physician_id', COALESCE(p.physician_id, 0),
            'dr_last_name', COALESCE(p.dr_last_name, ''),
            'dr_first_name', COALESCE(p.dr_first_name, '')
          )
        ) INTO v_prescriber_json
        FROM unnest(p_ncpdp_claim.segment_prescriber) p;
        
        IF v_prescriber_json IS NULL THEN
          v_prescriber_json := json_build_array(
            json_build_object(
              'error', 'Could not parse prescriber segment'
            )
          );
        END IF;
      EXCEPTION WHEN OTHERS THEN
        v_prescriber_json := NULL;
      END;
    END;
  END IF;

  -- Process pharmacy segment if not null
  IF p_ncpdp_claim.segment_pharmacy IS NOT NULL THEN
    BEGIN
      -- Access individual fields directly
      WITH pharmacy_fields AS (
        SELECT 
          ROW_NUMBER() OVER () as pharmacy_row,
          p.site_id,
          p.provider_id_qualifier,
          p.provider_id
        FROM unnest(p_ncpdp_claim.segment_pharmacy) p
      )
      SELECT json_agg(
        json_build_object(
          'site_id', site_id,
          'provider_id_qualifier', provider_id_qualifier,
          'provider_id', provider_id
        )
      ) INTO v_pharmacy_json
      FROM pharmacy_fields;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in pharmacy segment: %', SQLERRM;
      -- Simpler approach for pharmacy segment
      BEGIN
        SELECT json_agg(
          json_build_object(
            'site_id', COALESCE(p.site_id, 0),
            'provider_id_qualifier', COALESCE(p.provider_id_qualifier, ''),
            'provider_id', COALESCE(p.provider_id, '')
          )
        ) INTO v_pharmacy_json
        FROM unnest(p_ncpdp_claim.segment_pharmacy) p;
        
        IF v_pharmacy_json IS NULL THEN
          v_pharmacy_json := json_build_array(
            json_build_object(
              'error', 'Could not parse pharmacy segment'
            )
          );
        END IF;
      EXCEPTION WHEN OTHERS THEN
        v_pharmacy_json := NULL;
      END;
    END;
  END IF;

  -- Process insurance segment if not null
  IF p_ncpdp_claim.segment_insurance IS NOT NULL THEN
    BEGIN
      -- Access individual fields directly
      WITH insurance_fields AS (
        SELECT 
          ROW_NUMBER() OVER () as insurance_row,
          i.transaction_code,
          i.insurance_id,
          i.payer_type_id,
          i.card_holder_id,
          i.pt_rel_code,
          i.card_holder_first_name,
          i.card_holder_last_name,
          i.plan_id,
          i.group_id,
          i.person_code,
          i.home_plan,
          i.medigap_id,
          i.mcd_indicator,
          i.dr_accept_indicator,
          i.elig_clar_code,
          i.partd_facility,
          i.mcd_id_no
        FROM unnest(p_ncpdp_claim.segment_insurance) i
      )
      SELECT json_agg(
        json_build_object(
          'transaction_code', transaction_code,
          'insurance_id', insurance_id,
          'payer_type_id', payer_type_id,
          'card_holder_id', card_holder_id,
          'pt_rel_code', pt_rel_code,
          'card_holder_first_name', card_holder_first_name,
          'card_holder_last_name', card_holder_last_name,
          'plan_id', plan_id,
          'group_id', group_id,
          'person_code', person_code,
          'home_plan', home_plan,
          'medigap_id', medigap_id,
          'mcd_indicator', mcd_indicator,
          'dr_accept_indicator', dr_accept_indicator,
          'elig_clar_code', elig_clar_code,
          'partd_facility', partd_facility,
          'mcd_id_no', mcd_id_no
        )
      ) INTO v_insurance_json
      FROM insurance_fields;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in insurance segment: %', SQLERRM;
      -- Simpler approach for insurance segment
      BEGIN
        SELECT json_agg(
          json_build_object(
            'transaction_code', COALESCE(i.transaction_code, ''),
            'insurance_id', COALESCE(i.insurance_id, 0),
            'payer_type_id', COALESCE(i.payer_type_id, ''),
            'card_holder_id', COALESCE(i.card_holder_id, '')
          )
        ) INTO v_insurance_json
        FROM unnest(p_ncpdp_claim.segment_insurance) i;
        
        IF v_insurance_json IS NULL THEN
          v_insurance_json := json_build_array(
            json_build_object(
              'error', 'Could not parse insurance segment'
            )
          );
        END IF;
      EXCEPTION WHEN OTHERS THEN
        v_insurance_json := NULL;
      END;
    END;
  END IF;

  -- Process claim segment if not null
  IF p_ncpdp_claim.segment_claim IS NOT NULL THEN
    BEGIN
      -- Access individual fields directly
      WITH claim_fields AS (
        SELECT 
          ROW_NUMBER() OVER () as claim_row,
          c.is_test,
          c.transaction_code,
          c.rx_svc_no_ref_qualifier,
          c.rx_svc_no,
          c.fill_number,
          c.sched_rx_no,
          c.patient_id,
          c.order_id,
          c.insurance_id,
          c.payer_id,
          c.product_id,
          c.service_id,
          c.prod_svc_id_qualifier,
          c.prod_svc_id,
          c.quantity_dispensed,
          c.unit_of_measure,
          c.day_supply,
          c.number_of_refills_authorized,
          c.daw_code,
          c.date_rx_written,
          c.quantity_prescribed,
          c.admin_route,
          c.pa_id,
          c.pa_type_code,
          c.pa_no_submitted,
          c.compound_code,
          c.compound_type,
          c.rx_origin_code,
          c.sub_clar_code,
          c.other_coverage_code,
          c.require_other_coverage_code,
          c.sp_pk_indicator,
          c.level_of_service,
          c.pt_assign_indicator,
          c.pharmacy_service_type,
          c.proc_mod_code
        FROM unnest(p_ncpdp_claim.segment_claim) c
      )
      SELECT json_agg(
        json_build_object(
          'is_test', is_test,
          'transaction_code', transaction_code,
          'rx_svc_no_ref_qualifier', rx_svc_no_ref_qualifier,
          'rx_svc_no', rx_svc_no,
          'fill_number', fill_number,
          'sched_rx_no', sched_rx_no,
          'patient_id', patient_id,
          'order_id', order_id,
          'insurance_id', insurance_id,
          'payer_id', payer_id,
          'product_id', product_id,
          'service_id', service_id,
          'prod_svc_id_qualifier', prod_svc_id_qualifier,
          'prod_svc_id', prod_svc_id,
          'quantity_dispensed', quantity_dispensed,
          'unit_of_measure', unit_of_measure,
          'day_supply', day_supply,
          'number_of_refills_authorized', number_of_refills_authorized,
          'daw_code', daw_code,
          'date_rx_written', date_rx_written,
          'quantity_prescribed', quantity_prescribed,
          'admin_route', admin_route,
          'pa_id', pa_id,
          'pa_type_code', pa_type_code,
          'pa_no_submitted', pa_no_submitted,
          'compound_code', compound_code,
          'compound_type', compound_type,
          'rx_origin_code', rx_origin_code,
          'sub_clar_code', sub_clar_code,
          'other_coverage_code', other_coverage_code,
          'require_other_coverage_code', require_other_coverage_code,
          'sp_pk_indicator', sp_pk_indicator,
          'level_of_service', level_of_service,
          'pt_assign_indicator', pt_assign_indicator,
          'pharmacy_service_type', pharmacy_service_type,
          'proc_mod_code', proc_mod_code
        )
      ) INTO v_claim_json
      FROM claim_fields;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in claim segment: %', SQLERRM;
      -- Simpler approach for claim segment
      BEGIN
        SELECT json_agg(
          json_build_object(
            'transaction_code', COALESCE(c.transaction_code, ''),
            'rx_svc_no', COALESCE(c.rx_svc_no, ''),
            'fill_number', COALESCE(c.fill_number, 0),
            'day_supply', COALESCE(c.day_supply, 0),
            'quantity_dispensed', COALESCE(c.quantity_dispensed, 0)
          )
        ) INTO v_claim_json
        FROM unnest(p_ncpdp_claim.segment_claim) c;
        
        IF v_claim_json IS NULL THEN
          v_claim_json := json_build_array(
            json_build_object(
              'error', 'Could not parse claim segment'
            )
          );
        END IF;
      EXCEPTION WHEN OTHERS THEN
        v_claim_json := NULL;
      END;
    END;
  END IF;

  -- Process pricing segment if not null
  IF p_ncpdp_claim.segment_pricing IS NOT NULL THEN
    BEGIN
      -- Access individual fields directly
      WITH pricing_fields AS (
        SELECT 
          ROW_NUMBER() OVER () as pricing_row,
          p.is_test,
          p.parent_claim_no,
          p.transaction_code,
          p.payer_id,
          p.ing_cst_sub,
          p.disp_fee_sub,
          p.pro_svc_fee_sub,
          p.pt_pd_amt_sub,
          p.u_and_c_charge,
          p.incv_amt_sub,
          p.flat_tax_amt,
          p.gross_amount_due,
          p.cost_basis,
          p.subform_oclaim
        FROM unnest(p_ncpdp_claim.segment_pricing) p
      ),
      subform_data AS (
        SELECT 
          pf.pricing_row,
          (SELECT 
            CASE WHEN pf.subform_oclaim IS NOT NULL THEN
              json_agg(
                json_build_object(
                  'o_amt_sub_qualifier', oc.o_amt_sub_qualifier,
                  'o_sub_sub', oc.o_sub_sub
                )
              )
            ELSE NULL
            END
            FROM unnest(pf.subform_oclaim) oc
          ) as oclaim_json
        FROM pricing_fields pf
      )
      SELECT json_agg(
        json_build_object(
          'is_test', pf.is_test,
          'parent_claim_no', pf.parent_claim_no,
          'transaction_code', pf.transaction_code,
          'payer_id', pf.payer_id,
          'ing_cst_sub', pf.ing_cst_sub,
          'disp_fee_sub', pf.disp_fee_sub,
          'pro_svc_fee_sub', pf.pro_svc_fee_sub,
          'pt_pd_amt_sub', pf.pt_pd_amt_sub,
          'u_and_c_charge', pf.u_and_c_charge,
          'incv_amt_sub', pf.incv_amt_sub,
          'flat_tax_amt', pf.flat_tax_amt,
          'gross_amount_due', pf.gross_amount_due,
          'cost_basis', pf.cost_basis,
          'subform_oclaim', sd.oclaim_json
        )
      ) INTO v_pricing_json
      FROM pricing_fields pf
      JOIN subform_data sd ON pf.pricing_row = sd.pricing_row;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in pricing segment: %', SQLERRM;
      -- Simpler approach for pricing segment
      BEGIN
        SELECT json_agg(
          json_build_object(
            'transaction_code', COALESCE(p.transaction_code, ''),
            'payer_id', COALESCE(p.payer_id, 0),
            'ing_cst_sub', COALESCE(p.ing_cst_sub, 0),
            'disp_fee_sub', COALESCE(p.disp_fee_sub, 0),
            'gross_amount_due', COALESCE(p.gross_amount_due, 0),
            'cost_basis', COALESCE(p.cost_basis, '')
          )
        ) INTO v_pricing_json
        FROM unnest(p_ncpdp_claim.segment_pricing) p;
        
        IF v_pricing_json IS NULL THEN
          v_pricing_json := json_build_array(
            json_build_object(
              'error', 'Could not parse pricing segment'
            )
          );
        END IF;
      EXCEPTION WHEN OTHERS THEN
        v_pricing_json := NULL;
      END;
    END;
  END IF;
  
  -- Process clinical segment if not null
  IF p_ncpdp_claim.segment_clinical IS NOT NULL THEN
    BEGIN
      -- Access individual fields directly
      WITH clinical_fields AS (
        SELECT 
          ROW_NUMBER() OVER () as clinical_row,
          c.patient_id,
          c.subform_diagnosis,
          c.subform_measurement
        FROM unnest(p_ncpdp_claim.segment_clinical) c
      ),
      diagnosis_data AS (
        SELECT 
          cf.clinical_row,
          (SELECT 
            CASE WHEN cf.subform_diagnosis IS NOT NULL THEN
              json_agg(
                json_build_object(
                  'patient_id', d.patient_id,
                  'dx_id', d.dx_id,
                  'dx_code_qualifier', d.dx_code_qualifier,
                  'dx_code', d.dx_code
                )
              )
            ELSE NULL
            END
            FROM unnest(cf.subform_diagnosis) d
          ) as diagnosis_json
        FROM clinical_fields cf
      ),
      measurement_data AS (
        SELECT 
          cf.clinical_row,
          (SELECT 
            CASE WHEN cf.subform_measurement IS NOT NULL THEN
              json_agg(
                json_build_object(
                  'patient_id', m.patient_id,
                  'measurement_date', m.measurement_date,
                  'measurement_time', m.measurement_time,
                  'measurement_dimension', m.measurement_dimension,
                  'measurement_unit', m.measurement_unit,
                  'measurement_value', m.measurement_value
                )
              )
            ELSE NULL
            END
            FROM unnest(cf.subform_measurement) m
          ) as measurement_json
        FROM clinical_fields cf
      )
      SELECT json_agg(
        json_build_object(
          'patient_id', cf.patient_id,
          'subform_diagnosis', dd.diagnosis_json,
          'subform_measurement', md.measurement_json
        )
      ) INTO v_clinical_json
      FROM clinical_fields cf
      JOIN diagnosis_data dd ON cf.clinical_row = dd.clinical_row
      JOIN measurement_data md ON cf.clinical_row = md.clinical_row;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in clinical segment: %', SQLERRM;
      -- Simpler approach for clinical segment
      BEGIN
        SELECT json_agg(
          json_build_object(
            'patient_id', COALESCE(c.patient_id, 0)
          )
        ) INTO v_clinical_json
        FROM unnest(p_ncpdp_claim.segment_clinical) c;
        
        IF v_clinical_json IS NULL THEN
          v_clinical_json := json_build_array(
            json_build_object(
              'error', 'Could not parse clinical segment'
            )
          );
        END IF;
      EXCEPTION WHEN OTHERS THEN
        v_clinical_json := NULL;
      END;
    END;
  END IF;
  
  -- Process narrative segment if not null
  IF p_ncpdp_claim.segment_narrative IS NOT NULL THEN
    BEGIN
      -- Access individual fields directly
      WITH narrative_fields AS (
        SELECT 
          ROW_NUMBER() OVER () as narrative_row,
          n.narrative_message
        FROM unnest(p_ncpdp_claim.segment_narrative) n
      )
      SELECT json_agg(
        json_build_object(
          'narrative_message', narrative_message
        )
      ) INTO v_narrative_json
      FROM narrative_fields;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in narrative segment: %', SQLERRM;
      -- Simpler approach for narrative segment
      BEGIN
        SELECT json_agg(
          json_build_object(
            'narrative_message', COALESCE(n.narrative_message, '')
          )
        ) INTO v_narrative_json
        FROM unnest(p_ncpdp_claim.segment_narrative) n;
        
        IF v_narrative_json IS NULL THEN
          v_narrative_json := json_build_array(
            json_build_object(
              'error', 'Could not parse narrative segment'
            )
          );
        END IF;
      EXCEPTION WHEN OTHERS THEN
        v_narrative_json := NULL;
      END;
    END;
  END IF;
  
  -- Process COB segment if not null
  IF p_ncpdp_claim.segment_cob IS NOT NULL THEN
    BEGIN
      -- Access individual fields directly
      WITH cob_fields AS (
        SELECT 
          ROW_NUMBER() OVER () as cob_row,
          c.patient_id,
          c.transaction_code,
          c.pinsurance_id,
          c.subform_opayer,
          c.other_reject_code,
          c.subform_paid,
          c.subform_resp,
          c.subform_benefit,
          c.paid_total,
          c.pt_responsability_total
        FROM unnest(p_ncpdp_claim.segment_cob) c
      ),
      opayer_data AS (
        SELECT 
          cf.cob_row,
          (SELECT 
            CASE WHEN cf.subform_opayer IS NOT NULL THEN
              json_agg(
                json_build_object(
                  'patient_id', o.patient_id,
                  'pinsurance_id', o.pinsurance_id,
                  'insurance_id', o.insurance_id,
                  'payer_id', o.payer_id,
                  'other_coverage_type', o.other_coverage_type,
                  'internal_control_number', o.internal_control_number,
                  'other_id_qualifier', o.other_id_qualifier,
                  'other_id', o.other_id,
                  'other_date', o.other_date
                )
              )
            ELSE NULL
            END
            FROM unnest(cf.subform_opayer) o
          ) as opayer_json
        FROM cob_fields cf
      ),
      paid_data AS (
        SELECT 
          cf.cob_row,
          (SELECT 
            CASE WHEN cf.subform_paid IS NOT NULL THEN
              json_agg(
                json_build_object(
                  'paid_qualifier', p.paid_qualifier,
                  'other_payer_amount_paid', p.other_payer_amount_paid
                )
              )
            ELSE NULL
            END
            FROM unnest(cf.subform_paid) p
          ) as paid_json
        FROM cob_fields cf
      ),
      resp_data AS (
        SELECT 
          cf.cob_row,
          (SELECT 
            CASE WHEN cf.subform_resp IS NOT NULL THEN
              json_agg(
                json_build_object(
                  'pt_resp_amt_qualifier', r.pt_resp_amt_qualifier,
                  'pt_resp_amt', r.pt_resp_amt
                )
              )
            ELSE NULL
            END
            FROM unnest(cf.subform_resp) r
          ) as resp_json
        FROM cob_fields cf
      ),
      benefit_data AS (
        SELECT 
          cf.cob_row,
          (SELECT 
            CASE WHEN cf.subform_benefit IS NOT NULL THEN
              json_agg(
                json_build_object(
                  'benefit_stage_qualifier', b.benefit_stage_qualifier,
                  'benefit_stage_amount', b.benefit_stage_amount
                )
              )
            ELSE NULL
            END
            FROM unnest(cf.subform_benefit) b
          ) as benefit_json
        FROM cob_fields cf
      )
      SELECT json_agg(
        json_build_object(
          'patient_id', cf.patient_id,
          'transaction_code', cf.transaction_code,
          'pinsurance_id', cf.pinsurance_id,
          'subform_opayer', od.opayer_json,
          'other_reject_code', cf.other_reject_code,
          'subform_paid', pd.paid_json,
          'subform_resp', rd.resp_json,
          'subform_benefit', bd.benefit_json,
          'paid_total', cf.paid_total,
          'pt_responsability_total', cf.pt_responsability_total
        )
      ) INTO v_cob_json
      FROM cob_fields cf
      JOIN opayer_data od ON cf.cob_row = od.cob_row
      JOIN paid_data pd ON cf.cob_row = pd.cob_row
      JOIN resp_data rd ON cf.cob_row = rd.cob_row
      JOIN benefit_data bd ON cf.cob_row = bd.cob_row;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in COB segment: %', SQLERRM;
      -- Simpler approach for COB segment
      BEGIN
        SELECT json_agg(
          json_build_object(
            'patient_id', COALESCE(c.patient_id, 0),
            'transaction_code', COALESCE(c.transaction_code, ''),
            'pinsurance_id', COALESCE(c.pinsurance_id, 0),
            'paid_total', COALESCE(c.paid_total, 0),
            'pt_responsability_total', COALESCE(c.pt_responsability_total, 0)
          )
        ) INTO v_cob_json
        FROM unnest(p_ncpdp_claim.segment_cob) c;
        
        IF v_cob_json IS NULL THEN
          v_cob_json := json_build_array(
            json_build_object(
              'error', 'Could not parse COB segment'
            )
          );
        END IF;
      EXCEPTION WHEN OTHERS THEN
        v_cob_json := NULL;
      END;
    END;
  END IF;
  
  -- Process DUR segment if not null
  IF p_ncpdp_claim.segment_dur IS NOT NULL THEN
    BEGIN
      -- Access individual fields directly
      WITH dur_fields AS (
        SELECT 
          ROW_NUMBER() OVER () as dur_row,
          d.patient_id,
          d.transaction_code,
          d.subform_dur
        FROM unnest(p_ncpdp_claim.segment_dur) d
      ),
      dur_items_data AS (
        SELECT 
          df.dur_row,
          (SELECT 
            CASE WHEN df.subform_dur IS NOT NULL THEN
              json_agg(
                json_build_object(
                  'patient_id', i.patient_id,
                  'transaction_code', i.transaction_code,
                  'rsn_for_svc_code', i.rsn_for_svc_code,
                  'prf_svc_code', i.prf_svc_code,
                  'rst_svc_code', i.rst_svc_code,
                  'dur_pps_loe', i.dur_pps_loe,
                  'co_agt_itm_id', i.co_agt_itm_id,
                  'co_agt_id_qualifier', i.co_agt_id_qualifier,
                  'co_agt_id', i.co_agt_id
                )
              )
            ELSE NULL
            END
            FROM unnest(df.subform_dur) i
          ) as dur_items_json
        FROM dur_fields df
      )
      SELECT json_agg(
        json_build_object(
          'patient_id', df.patient_id,
          'transaction_code', df.transaction_code,
          'subform_dur', did.dur_items_json
        )
      ) INTO v_dur_json
      FROM dur_fields df
      JOIN dur_items_data did ON df.dur_row = did.dur_row;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in DUR segment: %', SQLERRM;
      -- Simpler approach for DUR segment
      BEGIN
        SELECT json_agg(
          json_build_object(
            'patient_id', COALESCE(d.patient_id, 0),
            'transaction_code', COALESCE(d.transaction_code, '')
          )
        ) INTO v_dur_json
        FROM unnest(p_ncpdp_claim.segment_dur) d;
        
        IF v_dur_json IS NULL THEN
          v_dur_json := json_build_array(
            json_build_object(
              'error', 'Could not parse DUR segment'
            )
          );
        END IF;
      EXCEPTION WHEN OTHERS THEN
        v_dur_json := NULL;
      END;
    END;
  END IF;

  -- Create the full JSON object with all segments
  v_ncpdp_json := json_build_object(
    'is_test', p_ncpdp_claim.is_test,
    'claim_no', p_ncpdp_claim.claim_no,
    'parent_claim_no', p_ncpdp_claim.parent_claim_no,
    'never_comp_seg', p_ncpdp_claim.never_comp_seg,
    'order_id', p_ncpdp_claim.order_id,
    'order_no', p_ncpdp_claim.order_no,
    'order_item_id', p_ncpdp_claim.order_item_id,
    'orderp_item_id', p_ncpdp_claim.orderp_item_id,
    'rx_no', p_ncpdp_claim.rx_no,
    'comp_dsg_fm_code', p_ncpdp_claim.comp_dsg_fm_code,
    'comp_disp_unit', p_ncpdp_claim.comp_disp_unit,
    'inventory_id', p_ncpdp_claim.inventory_id,
    'substatus_id', p_ncpdp_claim.substatus_id,
    'site_id', p_ncpdp_claim.site_id,
    'patient_id', p_ncpdp_claim.patient_id,
    'insurance_id', p_ncpdp_claim.insurance_id,
    'payer_id', p_ncpdp_claim.payer_id,
    'software_vendor_id', p_ncpdp_claim.software_vendor_id,
    'version_number', p_ncpdp_claim.version_number,
    'svc_prov_id_qualifier', p_ncpdp_claim.svc_prov_id_qualifier,
    'svc_prov_id', p_ncpdp_claim.svc_prov_id,
    'transaction_code', p_ncpdp_claim.transaction_code,
    'bin_number', p_ncpdp_claim.bin_number,
    'process_control_number', p_ncpdp_claim.process_control_number,
    'date_of_service', p_ncpdp_claim.date_of_service,
    
    -- Include all processed segment data
    'segment_patient', v_patient_json,
    'segment_prescriber', v_prescriber_json,
    'segment_pharmacy', v_pharmacy_json,
    'segment_insurance', v_insurance_json,
    'segment_claim', v_claim_json,
    'segment_pricing', v_pricing_json,
    'segment_clinical', v_clinical_json,
    'segment_narrative', v_narrative_json,
    'segment_cob', v_cob_json,
    'segment_dur', v_dur_json,
    
    'tabif_cob', p_ncpdp_claim.tabif_cob,
    'billed', p_ncpdp_claim.billed,
    'expected', p_ncpdp_claim.expected,
    'cost', p_ncpdp_claim.cost
  );

  RETURN json_strip_nulls(v_ncpdp_json);
END;
$$ LANGUAGE plpgsql STABLE;


