
DO $$ BEGIN
  PERFORM drop_all_function_signatures('calculate_medication_metrics');
END $$;
CREATE OR REPLACE FUNCTION calculate_medication_metrics(
    volume_per_dose NUMERIC DEFAULT NULL,
    height NUMERIC DEFAULT NULL,  -- in cm
    weight NUMERIC DEFAULT NULL,  -- in kg
    day_supply NUMERIC DEFAULT NULL,
    doses_to_prep NUMERIC DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    result JSON;
    bsa NUMERIC;
    ml_per_day NUMERIC;
    ml_kg_day NUMERIC;
    ml_m2_day NUMERIC;
    final_dose_quantity NUMERIC;
    final_day_quantity NUMERIC;
    final_m2_day_quantity NUMERIC;
    final_kg_day_quantity NUMERIC;
BEGIN
    -- Calculate BSA using the Mosteller formula: BSA = SQRT((height × weight) / 3600)
    -- Height in cm, weight in kg
    IF height IS NOT NULL AND weight IS NOT NULL THEN
        bsa := SQRT((height * weight) / 3600);
    ELSE
        bsa := NULL;
    END IF;
    
    -- Calculate mL per day
    IF volume_per_dose IS NOT NULL AND doses_to_prep IS NOT NULL AND day_supply IS NOT NULL AND day_supply > 0 THEN
        ml_per_day := (volume_per_dose * doses_to_prep) / day_supply;
    ELSE
        ml_per_day := NULL;
    END IF;
    
    -- Calculate mL per kg per day
    IF ml_per_day IS NOT NULL AND weight IS NOT NULL AND weight > 0 THEN
        ml_kg_day := ml_per_day / weight;
    ELSE
        ml_kg_day := NULL;
    END IF;
    
    -- Calculate mL per m² per day
    IF ml_per_day IS NOT NULL AND bsa IS NOT NULL AND bsa > 0 THEN
        ml_m2_day := ml_per_day / bsa;
    ELSE
        ml_m2_day := NULL;
    END IF;
    
    -- Calculate final dose quantity (Dispense Quantity / Dose)
    IF doses_to_prep IS NOT NULL AND doses_to_prep > 0 THEN
        final_dose_quantity := volume_per_dose;
    ELSE
        final_dose_quantity := NULL;
    END IF;
    
    -- Calculate final day quantity (Dispense Quantity / Day)
    IF day_supply IS NOT NULL AND day_supply > 0 THEN
        final_day_quantity := ml_per_day;
    ELSE
        final_day_quantity := NULL;
    END IF;
    
    -- Calculate final m² day quantity (Dispense Quantity / m² / Day)
    IF bsa IS NOT NULL AND bsa > 0 THEN
        final_m2_day_quantity := ml_m2_day;
    ELSE
        final_m2_day_quantity := NULL;
    END IF;
    
    -- Calculate final kg day quantity (Dispense Quantity / Kg / Day)
    IF weight IS NOT NULL AND weight > 0 THEN
        final_kg_day_quantity := ml_kg_day;
    ELSE
        final_kg_day_quantity := NULL;
    END IF;
    
    -- Construct the JSON result
    result := json_build_object(
        'bsa', ROUND(bsa::NUMERIC, 2),
        'ml_per_day', ROUND(ml_per_day::NUMERIC, 2),
        'ml_kg_day', ROUND(ml_kg_day::NUMERIC, 2),
        'ml_m2_day', ROUND(ml_m2_day::NUMERIC, 2),
        'final_dose_quantity', ROUND(final_dose_quantity::NUMERIC, 2),
        'final_day_quantity', ROUND(final_day_quantity::NUMERIC, 2),
        'final_m2_day_quantity', ROUND(final_m2_day_quantity::NUMERIC, 2),
        'final_kg_day_quantity', ROUND(final_kg_day_quantity::NUMERIC, 2)
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;