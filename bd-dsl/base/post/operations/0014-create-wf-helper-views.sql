CREATE EXTENSION IF NOT EXISTS pg_trgm;

CREATE INDEX IF NOT EXISTS idx_form_careplan_order_patient_id ON form_careplan_order(patient_id);
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_rx_no ON form_careplan_order_rx(rx_no);
CREATE INDEX IF NOT EXISTS idx_form_inventory_id ON form_inventory(id) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_physician_id ON form_physician(id) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;
DROP INDEX IF EXISTS idx_form_patient_insurance_patient_id_active;
CREATE INDEX IF NOT EXISTS idx_form_patient_insurance_patient_id_active ON form_patient_insurance(patient_id) WHERE COALESCE(active, 'No') = 'Yes' AND archived IS NOT TRUE AND deleted IS NOT TRUE;
DROP INDEX IF EXISTS idx_form_careplan_delivery_tick_status;
CREATE INDEX IF NOT EXISTS idx_form_careplan_delivery_tick_status ON form_careplan_delivery_tick(status) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE AND COALESCE(void, 'No') <> 'Yes';
DROP INDEX IF EXISTS idx_form_billing_invoice_status;
CREATE INDEX IF NOT EXISTS idx_form_billing_invoice_status ON form_billing_invoice(status) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE AND COALESCE(void, 'No') <> 'Yes';
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_disp_rx_no ON form_careplan_order_rx_disp(rx_no);
DROP INDEX IF EXISTS idx_patient_prescriber_patient_id_primary;
CREATE INDEX IF NOT EXISTS idx_patient_prescriber_patient_id_primary ON form_patient_prescriber(patient_id, is_primary) WHERE COALESCE(is_primary, 'No') = 'Yes';
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_rx_no_refill ON form_careplan_order_rx(rx_no, is_refill);
CREATE INDEX IF NOT EXISTS idx_form_patient_prior_auth_status_id ON form_patient_prior_auth(status_id) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;
DROP INDEX IF EXISTS idx_form_ledger_charge_line_calc_invoice_split_no;
CREATE INDEX IF NOT EXISTS idx_form_ledger_charge_line_calc_invoice_split_no ON form_ledger_charge_line(calc_invoice_split_no) WHERE COALESCE(void, 'No') <> 'Yes' AND COALESCE(zeroed, 'No') <> 'Yes';
DROP INDEX IF EXISTS idx_patient_diagnosis_dx_id;
CREATE INDEX IF NOT EXISTS idx_patient_diagnosis_dx_id ON form_patient_diagnosis(dx_id) WHERE COALESCE(active, 'No') = 'Yes' AND archived IS NOT TRUE AND deleted IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_form_careplan_delivery_tick_service_from_to ON form_careplan_delivery_tick(service_from, service_to);
CREATE INDEX IF NOT EXISTS idx_form_billing_invoice_date_of_service ON form_billing_invoice(date_of_service);
CREATE INDEX IF NOT EXISTS idx_form_patient_prior_auth_expire_date ON form_patient_prior_auth(expire_date) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

-- Additional recommended indexes for better query performance

-- Indexes for frequently used join conditions
CREATE INDEX IF NOT EXISTS idx_form_patient_deleted_archived ON form_patient(id) WHERE deleted IS NOT TRUE AND archived IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_items_patient_id ON form_careplan_order_item(patient_id);
CREATE INDEX IF NOT EXISTS idx_form_careplan_orderp_item_patient_id ON form_careplan_orderp_item(patient_id);
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_disp_delivery_ticket_id ON form_careplan_order_rx_disp(delivery_ticket_id);
CREATE INDEX IF NOT EXISTS idx_form_document_form_name_type_id ON form_document(form_name, type_id, patient_id);

-- Indexes for frequently filtered non-null conditions
CREATE INDEX IF NOT EXISTS idx_form_delivery_tick_delivery_date ON form_careplan_delivery_tick(delivery_date) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_rx_complete ON form_careplan_order_rx(rx_complete) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

-- Composite indexes for common join patterns
DROP INDEX IF EXISTS idx_form_billing_invoice_lookup;
CREATE INDEX IF NOT EXISTS idx_form_billing_invoice_lookup ON form_billing_invoice(invoice_no, id) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE AND COALESCE(void, 'No') <> 'Yes';
CREATE INDEX IF NOT EXISTS idx_form_ledger_charge_line_inventory_invoice ON form_ledger_charge_line(inventory_id, invoice_no) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

-- Indexes for relationship tables
CREATE INDEX IF NOT EXISTS idx_sf_form_careplan_order_to_careplan_order_item ON sf_form_careplan_order_to_careplan_order_item(form_careplan_order_fk, form_careplan_order_item_fk) WHERE archive IS NOT TRUE AND delete IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_sf_form_careplan_order_to_careplan_orderp_item ON sf_form_careplan_order_to_careplan_orderp_item(form_careplan_order_fk, form_careplan_orderp_item_fk) WHERE archive IS NOT TRUE AND delete IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_gr_billing_invoice_rx_id ON gr_form_billing_invoice_rx_id_to_careplan_order_rx_id(form_billing_invoice_fk, form_careplan_order_rx_fk);

-- Indexes for date-based sorting/filtering
CREATE INDEX IF NOT EXISTS idx_form_billing_invoice_date_of_service_status ON form_billing_invoice(date_of_service, status) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_form_careplan_delivery_tick_service_dates ON form_careplan_delivery_tick(service_from, service_to) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

-- Indexes for lookup tables with conditional joins
CREATE INDEX IF NOT EXISTS idx_form_list_order_status_code ON form_list_order_status(code) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_form_list_dispense_type_code ON form_list_dispense_type(code) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_form_sales_account_id ON form_sales_account(id) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_form_sales_territory_id ON form_sales_territory(id) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

-- Indexes for lateral joins and subqueries
DROP INDEX IF EXISTS idx_form_patient_diagnosis_active;
CREATE INDEX IF NOT EXISTS idx_form_patient_diagnosis_active ON form_patient_diagnosis(id, dx_id) WHERE COALESCE(active, 'No') = 'Yes' AND archived IS NOT TRUE AND deleted IS NOT TRUE;
DROP INDEX IF EXISTS idx_form_patient_prescriber_primary;
CREATE INDEX IF NOT EXISTS idx_form_patient_prescriber_primary ON form_patient_prescriber(patient_id, physician_id) WHERE COALESCE(is_primary, 'No') = 'Yes';

-- Indexes for frequently used in JSON/JSONB operations
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_item_dx_ids ON form_careplan_order_item USING gin(dx_ids gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_form_careplan_orderp_item_dx_ids ON form_careplan_orderp_item USING gin(dx_ids gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_dx_ids ON form_careplan_order USING gin(dx_ids gin_trgm_ops);

-- Indexes for queries with COALESCE on multiple fields
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_therapy_id ON form_careplan_order_rx(therapy_id) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_item_therapy_id ON form_careplan_order_item(therapy_id) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_form_careplan_orderp_item_therapy_id ON form_careplan_orderp_item(therapy_id) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

-- Indexes for views that use to_date function on formatted dates
CREATE INDEX IF NOT EXISTS idx_form_careplan_delivery_tick_delivery_date_txt ON form_careplan_delivery_tick(delivery_date) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_form_patient_prior_auth_expire_date_parsed ON form_patient_prior_auth((expire_date::date)) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

-- Specialized index for grouped_charges aggregation
DROP INDEX IF EXISTS idx_form_ledger_charge_line_calc_invoice_split_no_expected;
CREATE INDEX IF NOT EXISTS idx_form_ledger_charge_line_calc_invoice_split_no_expected ON form_ledger_charge_line(calc_invoice_split_no, expected) WHERE COALESCE(void, 'No') <> 'Yes' AND COALESCE(zeroed, 'No') <> 'Yes';

-- Indexes for wf_queue_team lookups
CREATE INDEX IF NOT EXISTS idx_form_wf_queue_team_code ON form_wf_queue_team(code) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_form_payer_collector_id ON form_payer(collector_id) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_patient_status_archive_delete ON form_patient(status_id, archived, deleted);

CREATE INDEX IF NOT EXISTS idx_patient_prescriber_primary ON form_patient_prescriber(patient_id, is_primary);

-- Improve join performance between patient and team
CREATE INDEX IF NOT EXISTS idx_patient_team_id 
ON form_patient(team_id)
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

-- Improve physician lookup
CREATE INDEX IF NOT EXISTS idx_physician_archive_delete
ON form_physician(id, archived, deleted)
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

-- Index for order status lookups
CREATE INDEX IF NOT EXISTS idx_order_status_code
ON form_list_order_status(code)
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

-- Index for patient diagnosis
CREATE INDEX IF NOT EXISTS idx_patient_diagnosis_dx_id
ON form_patient_diagnosis(dx_id)
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_sfco_form_careplan_order_fk
ON sf_form_careplan_order_to_careplan_order_item(form_careplan_order_fk)
WHERE archive IS NOT TRUE AND delete IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_sfco_form_careplan_orderp_fk
ON sf_form_careplan_order_to_careplan_orderp_item(form_careplan_order_fk)
WHERE archive IS NOT TRUE AND delete IS NOT TRUE;

-- This index should help with the ready_to_refill filter
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_disp_status 
ON form_careplan_order_rx_disp(status)
WHERE status = 'Ready to Refill';

-- Index on patient ID which appears to be used frequently
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_patient_id 
ON form_careplan_order(patient_id) 
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

-- Improve RX lookups
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_multi 
ON form_careplan_order_rx(rx_no, archived, deleted);

CREATE INDEX IF NOT EXISTS idx_form_patient_insurance_payer_id
ON form_patient_insurance(payer_id)
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_rx_no_order_no
ON form_careplan_order_rx(rx_no, order_no, archived, deleted)
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

-- Optimize the first LATERAL join
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_disp_rx_no_id
ON form_careplan_order_rx_disp(rx_no, id);

-- Optimize subquery for diagnosis lookup
DROP INDEX IF EXISTS idx_form_patient_diagnosis_id_active_dx;
CREATE INDEX IF NOT EXISTS idx_form_patient_diagnosis_id_active_dx
ON form_patient_diagnosis(id, active, dx_id)
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE AND COALESCE(active, 'No') = 'Yes';

-- Optimize the document lookup
CREATE INDEX IF NOT EXISTS idx_form_document_composite
ON form_document(form_name, type_id, patient_id, form_code, created_on)
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE AND type_id = 'Prescription';

CREATE INDEX IF NOT EXISTS idx_sf_form_careplan_order_item_fk_archive_delete
ON sf_form_careplan_order_to_careplan_order_item(form_careplan_order_item_fk, form_careplan_order_fk)
WHERE archive IS NOT TRUE AND delete IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_sf_form_careplan_orderp_item_fk_archive_delete
ON sf_form_careplan_order_to_careplan_orderp_item(form_careplan_orderp_item_fk, form_careplan_order_fk)
WHERE archive IS NOT TRUE AND delete IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_disp_ready_to_refill
ON form_careplan_order_rx_disp(rx_no)
WHERE status = 'Ready to Refill';

-- Focus on active insurance
DROP INDEX IF EXISTS idx_form_patient_insurance_active_patient_payer;
CREATE INDEX IF NOT EXISTS idx_form_patient_insurance_active_patient_payer
ON form_patient_insurance(patient_id, payer_id, active)
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE AND COALESCE(active, 'No') = 'Yes';

CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_covering 
ON form_careplan_order_rx(
    rx_no, 
    order_no, 
    inventory_id, 
    therapy_id, 
    is_refill, 
    day_supply
)
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_form_careplan_order_covering
ON form_careplan_order(
    id, 
    patient_id, 
    physician_id, 
    territory_id, 
    team_id
)
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE AND COALESCE(void, 'No') <> 'Yes';

CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_disp_ready_to_fill
ON form_careplan_order_rx_disp(rx_no, status)
WHERE status = 'Ready to Fill';

CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_disp_print_labels_fill_rx
ON form_careplan_order_rx_disp(rx_no, status)
WHERE status = 'Print Labels / Fill Rx';

CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_disp_order_verification
ON form_careplan_order_rx_disp(rx_no, status)
WHERE status = 'Order Verification';

CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_disp_delivery_ticket_confirmation
ON form_careplan_order_rx_disp(rx_no, status)
WHERE status = 'Delivery Ticket Confirmation';

CREATE INDEX IF NOT EXISTS idx_form_inventory_type
ON form_inventory(id, type)
WHERE type IN ('Drug', 'Compound');

CREATE INDEX IF NOT EXISTS idx_form_ledger_inventory_inventory_site
ON form_ledger_inventory(inventory_id, site_id)
WHERE deleted IS NOT TRUE AND archived IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_form_patient_territory
ON form_patient(territory_id, status_id)
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_order_no
ON form_careplan_order_rx(order_no, archived, deleted)
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_disp_status ON form_careplan_order_rx_disp(status);

CREATE INDEX IF NOT EXISTS idx_form_careplan_order_item_status_archive_delete 
  ON form_careplan_order_item(status_id, archived, deleted);

CREATE INDEX IF NOT EXISTS idx_form_careplan_orderp_item_status_archive_delete 
  ON form_careplan_orderp_item(status_id, archived, deleted);

ANALYZE form_careplan_order_rx_disp;
ANALYZE form_careplan_order;
ANALYZE form_careplan_order_rx;
ANALYZE form_patient;


CREATE OR REPLACE VIEW vw_wf_patients_base AS
SELECT
    'patient' as form_name,
    pt.id as form_id,
    pt.site_id,
    pt.status_id,
    pt.team_id,
    pt.id as patient_id,
    st.id as site,
    st.auto_name as site_auto_name,
    pt.id as patient,
    pt.auto_name as patient_auto_name,
    pt.territory_id as territory,
    st.auto_name as territory_auto_name,
    st.commissioned_sales_rep as sales_rep,
    salesrep.auto_name as sales_rep_auto_name,
    pt.referral_source_id as referral_source,
    sa.auto_name as referral_source_auto_name,
    pt.status_id as patient_status,
    ps.auto_name as patient_status_auto_name,
    pt.onhold_reason,
    pt.cancellation_reason,
    TO_CHAR(pt.dob, 'MM/DD/YYYY') as "DOB",
    TO_CHAR(COALESCE(pt.updated_on,pt.created_on), 'MM/DD/YYYY') AS last_touched,
    COALESCE(cb.displayname, CONCAT(cb.firstname, ' ', cb.lastname)) AS last_touched_by,
    pt.firstname AS first_name,
    pt.lastname AS last_name,
    pt.mrn AS "MRN",
    tm.id as team,
    tm.auto_name AS team_auto_name,
    ptpp.physician,
    ptpp.physician_auto_name
FROM form_patient pt
LEFT JOIN form_list_team tm ON pt.team_id = tm.id
LEFT JOIN LATERAL (SELECT 
	ph.id as physician,
	TRIM(CONCAT(ph.last, ', ', ph.first, ' ', ph.title)) as physician_auto_name
	FROM form_patient_prescriber pp 
	INNER JOIN form_physician ph ON ph.id = pp.physician_id
	WHERE pp.patient_id = pt.id
	ORDER BY CASE WHEN COALESCE(pp.is_primary, 'No') = 'Yes' THEN 1 ELSE 0 END DESC
LIMIT 1) ptpp ON true
LEFT JOIN form_user cb ON cb.id = COALESCE(pt.updated_by, pt.created_by)
LEFT JOIN form_list_patient_status ps ON ps.code = pt.status_id
LEFT JOIN form_sales_account sa ON sa.id = pt.referral_source_id
LEFT JOIN form_site site ON site.id = pt.site_id
LEFT JOIN form_sales_territory st ON st.id = pt.territory_id 
LEFT JOIN form_user salesrep ON salesrep.id = st.commissioned_sales_rep
WHERE pt.archived IS NOT TRUE AND pt.deleted IS NOT TRUE AND pt.status_id IN ('1', '2', '3', '4');


CREATE OR REPLACE VIEW vw_wf_orders_base AS
SELECT
    'careplan_order_item' as form_name,
    oi.id as form_id,

    -- Linked IDs
    rxd.delivery_ticket_id,
    cpor.refill_rx_id,
    oi.drug_pa_id as pa_id,
    cpor.id as rx_id,
    co.id as order_id,
    co.patient_id,
    COALESCE(co.site_id, pt.site_id) as site_id,
    COALESCE(co.team_id, pt.team_id) as team_id,
    COALESCE(co.physician_id, pt.physician) as physician_id,
    rxd.id as working_dispense_id,
    ins.id as insurance_id,
    ins.payer_id,
    ins.billing_method_id,
    ins.next_insurance_id,
    co.supplies_insurance_id,
    co.supply_billable_id,
    co.supplies_pa_id,
    cpor.rx_no,
    co.order_no,
    oi.status_id as status_id,

    -- Patient Data
    pt.status_id as patient_status,
    pt.patient_status_auto_name as patient_status_auto_name,
    pt.cancellation_reason,
    pt."DOB",
    pt.first_name,
    pt.last_name,
    pt."MRN",
    COALESCE(tm.id, pt.team) as team,
    COALESCE(tm.auto_name, pt.team_auto_name) AS team_auto_name,

    -- Status fields
    cpor.rx_complete,
    cpor.rx_verified,
    cpor.rx_denied,
    CASE WHEN COALESCE(cpor.is_refill, 'No') = 'Yes' THEN '✓' ELSE NULL END as is_refill,
    oi.order_complete,
    CASE WHEN COALESCE(co.bill_supplies, 'No') = 'Yes' THEN '✓' ELSE NULL END as bill_supplies,
    CASE WHEN COALESCE(ins.bill_for_denial, 'No') = 'Yes' THEN '✓' ELSE NULL END as bill_for_denial,

    -- Column Data
    cpor.rx_no as "Rx #",
    COALESCE(sa.id, pt.referral_source) as referral_source,
    COALESCE(sa.auto_name, pt.referral_source_auto_name) as referral_source_auto_name,
    COALESCE(st.id, pt.territory) as territory,
    COALESCE(st.auto_name, pt.territory_auto_name) as territory_auto_name,
    COALESCE(salesrep.id, pt.sales_rep) as sales_rep,
    COALESCE(salesrep.auto_name, pt.sales_rep_auto_name) as sales_rep_auto_name,
    th.id as therapy,
    th.auto_name as therapy_auto_name,
    os.auto_name as order_status_auto_name,
    os.id as order_status,
    CASE WHEN COALESCE(cpor.start_date, oi.start_date, co.start_date) IS NOT NULL THEN TO_CHAR(COALESCE(cpor.start_date, oi.start_date, co.start_date), 'MM/DD/YYYY') ELSE NULL END as start_date,
    CASE WHEN COALESCE(oi.stop_date, co.stop_date) IS NOT NULL THEN TO_CHAR(COALESCE(oi.stop_date, co.stop_date), 'MM/DD/YYYY') ELSE NULL END as stop_date,
    CASE WHEN COALESCE(cpor.expiration_date, oi.expiration_date, co.expiration_date) IS NOT NULL THEN TO_CHAR(COALESCE(cpor.expiration_date, oi.expiration_date, co.expiration_date), 'MM/DD/YYYY') ELSE NULL END as expiration_date,
    CASE WHEN cpor.next_fill_date IS NOT NULL THEN TO_CHAR(cpor.next_fill_date, 'MM/DD/YYYY') ELSE NULL END as next_fill_date,
    CASE WHEN COALESCE(cpor.written_date, oi.written_date, co.written_date) IS NOT NULL THEN TO_CHAR(COALESCE(oi.written_date, co.written_date), 'MM/DD/YYYY') ELSE NULL END as written_date,
    CASE WHEN cpor.last_through_date IS NOT NULL THEN TO_CHAR(cpor.last_through_date, 'MM/DD/YYYY') ELSE NULL END as last_through_date,
    CASE WHEN cpor.dispense_quantity IS NOT NULL THEN format_numeric(cpor.dispense_quantity::numeric) ELSE NULL END as quantity,
    CASE WHEN cpor.day_supply IS NOT NULL THEN format_numeric(cpor.day_supply::numeric) ELSE NULL END as "DS",
    rxd.fill_status,
    dst.id as dispense_type,
    dst.auto_name as dispense_type_auto_name,
    CASE WHEN COALESCE(cpor.is_340b, 'No') = 'Yes' THEN '✓' ELSE NULL END as is_340b,
    oi.billing_method as billing_method,
    CASE
        WHEN COALESCE(oi.billing_method, co.billing_method) = 'Do Not Bill' THEN NULL
        ELSE '✓'
    END as bill,
    pyt.id as payer_type,
    pyt.auto_name as payer_type_auto_name,
    CASE WHEN ins.effective_date IS NOT NULL THEN TO_CHAR(ins.effective_date, 'MM/DD/YYYY') ELSE NULL END as ins_eff_date,
    CASE WHEN COALESCE(oi.is_specialty, 'No') = 'Yes' THEN '✓'::text ELSE NULL::text END as is_specialty,
    CASE WHEN COALESCE(co.requires_nursing, 'No') = 'Yes' THEN '✓'::text ELSE NULL::text END as requires_nursing,
    ons.id::text as nursing_status,
    ons.auto_name::text as nursing_status_auto_name,
    na.id::text as nursing_agency,
    na.auto_name::text as nursing_agency_auto_name,
    inf.id::text as infusion_suite,
    inf.auto_name::text as infusion_suite_auto_name,
    CASE WHEN co.first_visit_date IS NOT NULL THEN TO_CHAR(co.first_visit_date, 'MM/DD/YYYY') ELSE NULL END as first_visit_date,
    CASE WHEN COALESCE(oi.stat_order, 'No') = 'Yes' THEN '✚' ELSE NULL END as stat,
    iss.id as intake_substatus,
    iss.auto_name as intake_substatus_auto_name,
    dx.diagnosis,
    dx.diagnosis_auto_name,
    oi.auth_flag,
    oi.bv_flag,
    CASE WHEN COALESCE(oi.auth_flag, 'No') = 'Yes' THEN '⚠' ELSE NULL END as needs_auth,
    CASE WHEN COALESCE(oi.bv_flag, 'No') = 'Yes' THEN '⚠' ELSE NULL END as needs_bv,
    COALESCE(oi.onhold_reason, pt.onhold_reason) as onhold_reason,
    doc.document_file_path,
    doc.document_id,
    doc.document_name,
    CASE WHEN COALESCE(oi.is_erx, 'No') = 'Yes' THEN '✓' ELSE NULL END as is_erx,
    inv.formatted_ndc as "NDC",
    inv.hcpc_code as "HCPC",
    inv.id as drug,
    inv.auto_name as drug_auto_name,
    brd.id as drug_brand,
    brd.auto_name as drug_brand_auto_name,
    man.id as manufacturer,
    man.auto_name as manufacturer_auto_name,
    ph.id as physician,
    CONCAT(ph.last, ', ', ph.first, ' ', ph.title) as physician_auto_name,
    wfe.code as last_event,
    wfe.auto_name as last_event_auto_name,
    oi.last_event_by,
    usr.auto_name as last_event_by_auto_name,
    TO_CHAR(oi.last_event_datetime, 'MM/DD/YYYY HH:MI AM') as last_event_on,
    CASE 
        WHEN rxd.copay >= 0 THEN format_currency(rxd.copay::numeric)
        ELSE NULL
    END AS patient_pay,
    py.id as payer,
    py.auto_name as payer_auto_name
FROM form_careplan_order_item oi
INNER JOIN vw_wf_patients_base pt ON pt.patient_id = oi.patient_id
INNER JOIN sf_form_careplan_order_to_careplan_order_item sfco
    ON sfco.form_careplan_order_item_fk = oi.id 
    AND sfco.archive IS NOT TRUE 
    AND sfco.delete IS NOT TRUE 
INNER JOIN form_careplan_order co 
    ON co.id = sfco.form_careplan_order_fk
    AND co.archived IS NOT TRUE
    AND co.deleted IS NOT TRUE
    AND COALESCE(co.void, 'No') <> 'Yes'
LEFT JOIN form_careplan_order_rx cpor 
    ON cpor.rx_no = oi.rx_no
    AND cpor.archived IS NOT TRUE
    AND cpor.deleted IS NOT TRUE
LEFT JOIN form_list_order_nursing_status ons ON ons.code = co.nursing_status
    AND ons.archived IS NOT TRUE
    AND ons.deleted IS NOT TRUE
LEFT JOIN form_nurse_agency na ON na.id = co.nursing_agency_id
    AND na.archived IS NOT TRUE
    AND na.deleted IS NOT TRUE
LEFT JOIN form_infusion_suite inf ON inf.id = co.infusion_suite_id
    AND inf.archived IS NOT TRUE
    AND inf.deleted IS NOT TRUE
LEFT JOIN LATERAL (SELECT status as fill_status,
delivery_ticket_id,
id,
copay
FROM form_careplan_order_rx_disp
WHERE rx_no = oi.rx_no
ORDER BY id DESC
LIMIT 1) rxd ON true
LEFT JOIN form_list_dispense_type dst ON dst.code = oi.type_id
    AND dst.archived IS NOT TRUE
    AND dst.deleted IS NOT TRUE
LEFT JOIN form_list_order_status os ON os.code = oi.status_id
    AND os.archived IS NOT TRUE
    AND os.deleted IS NOT TRUE
LEFT JOIN form_sales_account sa ON sa.id = co.referral_source_id
    AND sa.archived IS NOT TRUE
    AND sa.deleted IS NOT TRUE
LEFT JOIN form_sales_territory st ON st.id = co.territory_id
    AND st.archived IS NOT TRUE
    AND st.deleted IS NOT TRUE
LEFT JOIN form_user salesrep ON salesrep.id = st.commissioned_sales_rep
    AND salesrep.archived IS NOT TRUE
    AND salesrep.deleted IS NOT TRUE
LEFT JOIN form_list_therapy th ON th.code = COALESCE(cpor.therapy_id, oi.therapy_id)
    AND th.archived IS NOT TRUE
    AND th.deleted IS NOT TRUE
LEFT JOIN form_inventory inv ON inv.id = COALESCE(cpor.inventory_id, oi.inventory_id)
    AND inv.archived IS NOT TRUE
    AND inv.deleted IS NOT TRUE
LEFT JOIN form_list_intake_substatus iss ON iss.code = oi.intake_substatus_id
    AND iss.archived IS NOT TRUE
    AND iss.deleted IS NOT TRUE
LEFT JOIN form_list_fdb_drug_brand brd ON inv.brand_name_id = brd.code
    AND brd.archived IS NOT TRUE
    AND brd.deleted IS NOT TRUE
LEFT JOIN form_list_manufacturer man
    ON man.code = inv.manufacturer_id
    AND man.archived IS NOT TRUE
    AND man.deleted IS NOT TRUE
LEFT JOIN LATERAL (
    SELECT pi.* 
    FROM form_patient_insurance pi
    WHERE pi.id IN (SELECT * FROM get_insurance_ids_for_joining(oi.payer_ids, co.payer_ids, '1'))
      AND pi.archived IS NOT TRUE
      AND pi.deleted IS NOT TRUE
      AND COALESCE(pi.active, 'No') = 'Yes'
      AND pi.patient_id = pt.patient_id
    LIMIT 1
) ins ON true
LEFT JOIN form_physician ph ON ph.id = COALESCE(co.physician_id, pt.physician)
    AND ph.archived IS NOT TRUE
    AND ph.deleted IS NOT TRUE
LEFT JOIN form_list_team tm ON tm.id = co.team_id
    AND tm.archived IS NOT TRUE
    AND tm.deleted IS NOT TRUE
LEFT JOIN form_payer py ON py.id = ins.payer_id
    AND py.archived IS NOT TRUE 
    AND py.deleted IS NOT TRUE
LEFT JOIN form_list_payer_type pyt ON pyt.code = py.type_id
    AND pyt.archived IS NOT TRUE 
    AND pyt.deleted IS NOT TRUE
LEFT JOIN LATERAL (
    SELECT
        ptdx.id as patient_dx_id,
        ptdx.dx_id as dx_id,
        dx.id as diagnosis,
        dx.name as diagnosis_auto_name
    FROM jsonb_array_elements(coalesce_if_empty_json(
        coalesce_if_empty_json(
        coalesce_if_empty_json(oi.dx_ids, co.dx_ids), 
        co.dx_ids), 
        '[]')::jsonb) AS elem
    INNER JOIN form_patient_diagnosis ptdx 
        ON ptdx.id = (elem->>'id')::int 
        AND ptdx.archived IS NOT TRUE 
        AND ptdx.deleted IS NOT TRUE 
        AND COALESCE(ptdx.active, 'No') = 'Yes'
    INNER JOIN form_list_diagnosis dx 
        ON dx.code = ptdx.dx_id 
        AND dx.archived IS NOT TRUE 
        AND dx.deleted IS NOT TRUE
    GROUP BY (elem->>'rank')::int, ptdx.id, ptdx.dx_id, dx.name, dx.id
    ORDER BY (elem->>'rank')::int ASC
    LIMIT 1
) AS dx ON true
LEFT JOIN form_careplan_delivery_tick dt 
    ON dt.id = rxd.delivery_ticket_id
    AND dt.archived IS NOT TRUE
    AND dt.deleted IS NOT TRUE
LEFT JOIN form_user usr ON usr.id = oi.last_event_by
    AND usr.archived IS NOT TRUE
    AND usr.deleted IS NOT TRUE
LEFT JOIN form_list_wf_event wfe ON wfe.code = oi.last_event_id
    AND wfe.archived IS NOT TRUE
    AND wfe.deleted IS NOT TRUE
LEFT JOIN LATERAL (
    SELECT 
        doc.id as document_id,
        doc.name as document_name,
        doc.file_path as document_file_path
    FROM form_document doc
    WHERE
    doc.form_name = 'careplan_order_item'
    AND doc.type_id = 'Prescription'
    AND doc.patient_id = oi.patient_id
    AND doc.form_code = oi.code
    AND doc.archived IS NOT TRUE
    AND doc.deleted IS NOT TRUE
    ORDER BY doc.created_on DESC
    LIMIT 1
) doc ON TRUE
WHERE oi.status_id IN ('1', '4', '5')

UNION ALL

SELECT
    'careplan_order' as form_name,
    co.id as form_id,

    -- Linked IDs
    rxd.delivery_ticket_id,
    cpor.refill_rx_id,
    opi.drug_pa_id as pa_id,
    cpor.id as rx_id,
    co.id as order_id,
    co.patient_id,
    co.site_id as site_id,
    co.team_id as team_id,
    co.physician_id,
    rxd.id as working_dispense_id,
    ins.id as insurance_id,
    ins.payer_id,
    ins.billing_method_id,
    ins.next_insurance_id,
    co.supplies_insurance_id,
    co.supply_billable_id,
    co.supplies_pa_id,
    cpor.rx_no,
    co.order_no,
    opi.status_id as status_id,

    -- Patient Data
    pt.status_id as patient_status,
    pt.patient_status_auto_name as patient_status_auto_name,
    pt.cancellation_reason,
    pt."DOB",
    pt.first_name,
    pt.last_name,
    pt."MRN",
    COALESCE(tm.id, pt.team) as team,
    COALESCE(tm.auto_name, pt.team_auto_name) AS team_auto_name,

    -- Status fields
    cpor.rx_complete,
    cpor.rx_verified,
    cpor.rx_denied,
    CASE WHEN COALESCE(cpor.is_refill, 'No') = 'Yes' THEN '✓' ELSE NULL END as is_refill,
    opi.order_complete,
    CASE WHEN COALESCE(co.bill_supplies, 'No') = 'Yes' THEN '✓' ELSE NULL END as bill_supplies,
    CASE WHEN COALESCE(ins.bill_for_denial, 'No') = 'Yes' THEN '✓' ELSE NULL END as bill_for_denial,

    cpor.rx_no as "Rx #",
    -- Column Data
    COALESCE(sa.id, pt.referral_source) as referral_source,
    COALESCE(sa.auto_name, pt.referral_source_auto_name) as referral_source_auto_name,
    COALESCE(st.id, pt.territory) as territory,
    COALESCE(st.auto_name, pt.territory_auto_name) as territory_auto_name,
    COALESCE(salesrep.id, pt.sales_rep) as sales_rep,
    COALESCE(salesrep.auto_name, pt.sales_rep_auto_name) as sales_rep_auto_name,
    th.id as therapy,
    th.auto_name as therapy_auto_name,
    os.auto_name as order_status_auto_name,
    os.id as order_status,
    CASE WHEN COALESCE(cpor.start_date, opi.start_date) IS NOT NULL THEN TO_CHAR(COALESCE(cpor.start_date, opi.start_date), 'MM/DD/YYYY') ELSE NULL END as start_date,
    CASE WHEN opi.stop_date IS NOT NULL THEN TO_CHAR(opi.stop_date, 'MM/DD/YYYY') ELSE NULL END as stop_date,
    CASE WHEN COALESCE(cpor.expiration_date, opi.expiration_date) IS NOT NULL THEN TO_CHAR(COALESCE(cpor.expiration_date, opi.expiration_date), 'MM/DD/YYYY') ELSE NULL END as expiration_date,
    CASE WHEN cpor.next_fill_date IS NOT NULL THEN TO_CHAR(cpor.next_fill_date, 'MM/DD/YYYY') ELSE NULL END as next_fill_date,
    CASE WHEN COALESCE(cpor.written_date, opi.written_date) IS NOT NULL THEN TO_CHAR(COALESCE(cpor.written_date, opi.written_date), 'MM/DD/YYYY') ELSE NULL END as written_date,
    CASE WHEN cpor.last_through_date IS NOT NULL THEN TO_CHAR(cpor.last_through_date, 'MM/DD/YYYY') ELSE NULL END as last_through_date,
    CASE WHEN cpor.dispense_quantity IS NOT NULL THEN format_numeric(cpor.dispense_quantity::numeric) ELSE NULL END as quantity,
    CASE WHEN cpor.day_supply IS NOT NULL THEN format_numeric(cpor.day_supply::numeric) ELSE NULL END as "DS",
    rxd.fill_status,
    dst.id as dispense_type,
    dst.auto_name as dispense_type_auto_name,
    CASE WHEN COALESCE(cpor.is_340b, 'No') = 'Yes' THEN '✓' ELSE NULL END as is_340b,
    opi.billing_method as billing_method,
    CASE
        WHEN COALESCE(opi.billing_method, co.billing_method) = 'Do Not Bill' THEN NULL
        ELSE '✓'
    END as bill,
    pyt.id as payer_type,
    pyt.auto_name as payer_type_auto_name,
    CASE WHEN ins.effective_date IS NOT NULL THEN TO_CHAR(ins.effective_date, 'MM/DD/YYYY') ELSE NULL END as ins_eff_date,
    CASE WHEN COALESCE(opi.is_specialty, 'No') = 'Yes' THEN '✓' ELSE NULL END as is_specialty,
    CASE WHEN COALESCE(co.requires_nursing, 'No') = 'Yes' THEN '✓'::text ELSE NULL::text END as requires_nursing,
    ons.id::text as nursing_status,
    ons.auto_name::text as nursing_status_auto_name,
    na.id::text as nursing_agency,
    na.auto_name::text as nursing_agency_auto_name,
    inf.id::text as infusion_suite,
    inf.auto_name::text as infusion_suite_auto_name,
    CASE WHEN co.first_visit_date IS NOT NULL THEN TO_CHAR(co.first_visit_date, 'MM/DD/YYYY') ELSE NULL END as first_visit_date,
    CASE WHEN COALESCE(opi.stat_order, 'No') = 'Yes' THEN '✚' ELSE NULL END as stat,
    iss.id as intake_substatus,
    iss.auto_name as intake_substatus_auto_name,
    dx.diagnosis,
    dx.diagnosis_auto_name,
    COALESCE(opi.auth_flag, 'No') as auth_flag,
    COALESCE(opi.bv_flag, 'No') as bv_flag,
    CASE WHEN COALESCE(opi.auth_flag, 'No') = 'Yes' THEN '⚠' ELSE NULL END as needs_auth,
    CASE WHEN COALESCE(opi.bv_flag, 'No') = 'Yes' THEN '⚠' ELSE NULL END as needs_bv,
    COALESCE(opi.onhold_reason, pt.onhold_reason) as onhold_reason,
    doc.document_file_path,
    doc.document_id,
    doc.document_name,
    CASE WHEN COALESCE(opi.is_erx, 'No') = 'Yes' THEN '✓' ELSE NULL END as is_erx,
    inv.formatted_ndc as "NDC",
    inv.hcpc_code as "HCPC",
    inv.id as drug,
    inv.auto_name as drug_auto_name,
    brd.id as drug_brand,
    brd.auto_name as drug_brand_auto_name,
    man.id as manufacturer,
    man.auto_name as manufacturer_auto_name,
    ph.id as physician,
    CONCAT(ph.last, ', ', ph.first, ' ', ph.title) as physician_auto_name,
    wfe.code as last_event,
    wfe.auto_name as last_event_auto_name,
    opi.last_event_by,
    usr.auto_name as last_event_by_auto_name,
    TO_CHAR(opi.last_event_datetime, 'MM/DD/YYYY HH:MI AM') as last_event_on,
    CASE 
        WHEN rxd.copay >= 0 THEN format_currency(rxd.copay::numeric)
        ELSE NULL
    END AS patient_pay,
    py.id as payer,
    py.auto_name as payer_auto_name
FROM form_careplan_orderp_item opi
INNER JOIN vw_wf_patients_base pt ON pt.patient_id = opi.patient_id
INNER JOIN sf_form_careplan_order_to_careplan_orderp_item sfco
    ON sfco.form_careplan_orderp_item_fk = opi.id 
    AND sfco.archive IS NOT TRUE 
    AND sfco.delete IS NOT TRUE 
INNER JOIN form_careplan_order co 
    ON co.id = sfco.form_careplan_order_fk
    AND co.archived IS NOT TRUE
    AND co.deleted IS NOT TRUE
    AND COALESCE(co.void, 'No') <> 'Yes'
LEFT JOIN form_physician ph ON ph.id = COALESCE(co.physician_id, pt.physician)
    AND ph.archived IS NOT TRUE
    AND ph.deleted IS NOT TRUE
LEFT JOIN form_list_dispense_type dst ON dst.code = opi.type_id
    AND dst.archived IS NOT TRUE
    AND dst.deleted IS NOT TRUE
LEFT JOIN form_list_order_status os ON os.code = opi.status_id
    AND os.archived IS NOT TRUE
    AND os.deleted IS NOT TRUE
LEFT JOIN form_sales_account sa ON sa.id = co.referral_source_id
    AND sa.archived IS NOT TRUE
    AND sa.deleted IS NOT TRUE
LEFT JOIN form_sales_territory st ON st.id = co.territory_id
    AND st.archived IS NOT TRUE
    AND st.deleted IS NOT TRUE
LEFT JOIN form_user salesrep ON salesrep.id = st.commissioned_sales_rep
    AND salesrep.archived IS NOT TRUE
    AND salesrep.deleted IS NOT TRUE
LEFT JOIN form_careplan_order_rx cpor 
    ON cpor.rx_no = opi.rx_no
    AND cpor.archived IS NOT TRUE
    AND cpor.deleted IS NOT TRUE
LEFT JOIN form_list_therapy th ON th.code = COALESCE(cpor.therapy_id, opi.therapy_id)
    AND th.archived IS NOT TRUE
    AND th.deleted IS NOT TRUE
LEFT JOIN LATERAL (SELECT status as fill_status,
delivery_ticket_id,
id,
copay
FROM form_careplan_order_rx_disp
WHERE rx_no = opi.rx_no
ORDER BY id DESC
LIMIT 1) rxd ON true
LEFT JOIN form_inventory inv ON inv.id = COALESCE(cpor.inventory_id, opi.inventory_id)
    AND inv.archived IS NOT TRUE
    AND inv.deleted IS NOT TRUE
LEFT JOIN form_list_intake_substatus iss ON iss.code = opi.intake_substatus_id
    AND iss.archived IS NOT TRUE
    AND iss.deleted IS NOT TRUE
LEFT JOIN form_list_fdb_drug_brand brd ON inv.brand_name_id = brd.code
    AND brd.archived IS NOT TRUE
    AND brd.deleted IS NOT TRUE
LEFT JOIN form_list_manufacturer man
    ON man.code = inv.manufacturer_id
    AND man.archived IS NOT TRUE
    AND man.deleted IS NOT TRUE
LEFT JOIN LATERAL (
    SELECT pi.* 
    FROM form_patient_insurance pi
    WHERE pi.id IN (SELECT * FROM get_insurance_ids_for_joining(opi.payer_ids, NULL::text, '1'))
      AND pi.archived IS NOT TRUE
      AND pi.deleted IS NOT TRUE
      AND COALESCE(pi.active, 'No') = 'Yes'
      AND pi.patient_id = pt.patient_id
    LIMIT 1
) ins ON true
LEFT JOIN form_payer py ON py.id = ins.payer_id
    AND py.archived IS NOT TRUE 
    AND py.deleted IS NOT TRUE
LEFT JOIN form_list_payer_type pyt ON pyt.code = py.type_id
    AND pyt.archived IS NOT TRUE 
    AND pyt.deleted IS NOT TRUE
LEFT JOIN form_list_team tm ON tm.id = co.team_id
    AND tm.archived IS NOT TRUE
    AND tm.deleted IS NOT TRUE
LEFT JOIN form_list_order_nursing_status ons ON ons.code = co.nursing_status
    AND ons.archived IS NOT TRUE
    AND ons.deleted IS NOT TRUE
LEFT JOIN form_nurse_agency na ON na.id = co.nursing_agency_id
    AND na.archived IS NOT TRUE
    AND na.deleted IS NOT TRUE
LEFT JOIN form_infusion_suite inf ON inf.id = co.infusion_suite_id
    AND inf.archived IS NOT TRUE
    AND inf.deleted IS NOT TRUE
LEFT JOIN LATERAL (
    SELECT
        ptdx.id as patient_dx_id,
        ptdx.dx_id as dx_id,
        dx.id as diagnosis,
        dx.name as diagnosis_auto_name
    FROM jsonb_array_elements(coalesce_if_empty_json(
        coalesce_if_empty_json(
        coalesce_if_empty_json(opi.dx_ids, co.dx_ids), 
        co.dx_ids), 
        '[]')::jsonb) AS elem
    INNER JOIN form_patient_diagnosis ptdx 
        ON ptdx.id = (elem->>'id')::int 
        AND ptdx.archived IS NOT TRUE 
        AND ptdx.deleted IS NOT TRUE 
        AND COALESCE(ptdx.active, 'No') = 'Yes'
    INNER JOIN form_list_diagnosis dx 
        ON dx.code = ptdx.dx_id 
        AND dx.archived IS NOT TRUE 
        AND dx.deleted IS NOT TRUE
    GROUP BY (elem->>'rank')::int, ptdx.id, ptdx.dx_id, dx.name, dx.id
    ORDER BY (elem->>'rank')::int ASC
    LIMIT 1
) AS dx ON true
LEFT JOIN form_user usr ON usr.id = opi.last_event_by
    AND usr.archived IS NOT TRUE
    AND usr.deleted IS NOT TRUE
LEFT JOIN form_list_wf_event wfe ON wfe.code = opi.last_event_id
    AND wfe.archived IS NOT TRUE
    AND wfe.deleted IS NOT TRUE
LEFT JOIN form_careplan_delivery_tick dt 
    ON dt.id = rxd.delivery_ticket_id
    AND dt.archived IS NOT TRUE
    AND dt.deleted IS NOT TRUE
    AND COALESCE(dt.void, 'No') <> 'Yes'
LEFT JOIN LATERAL (
    SELECT 
        doc.id as document_id,
        doc.name as document_name,
        doc.file_path as document_file_path
    FROM form_document doc
    WHERE
    doc.form_name = 'careplan_order'
    AND doc.type_id = 'Prescription'
    AND doc.patient_id = co.patient_id
    AND doc.form_code = co.code
    AND doc.archived IS NOT TRUE
    AND doc.deleted IS NOT TRUE
    ORDER BY doc.created_on DESC
    LIMIT 1
) doc ON TRUE
WHERE opi.status_id IN ('1', '4', '5');

CREATE OR REPLACE VIEW vw_wf_rx_base AS
SELECT

    -- Linked IDs
    ord.delivery_ticket_id,
    ord.refill_rx_id,
    ord.pa_id,
    ord.rx_id,
    ord.order_id,
    ord.patient_id,
    ord.site_id,
    ord.team_id,
    ord.physician_id,
    ord.working_dispense_id,
    ord.insurance_id,
    ord.payer_id,
    ord.billing_method_id,
    ord.next_insurance_id,
    ord.supplies_insurance_id,
    ord.supply_billable_id,
    ord.supplies_pa_id,
    ord.rx_no,
    ord.order_no,
    ord.status_id,

    -- Patient Data
    ord.patient_status,
    ord.patient_status_auto_name,
    ord.cancellation_reason,
    ord."DOB",
    ord.first_name,
    ord.last_name,
    ord."MRN",
    ord.team,
    ord.team_auto_name,

    -- Status fields
    ord.rx_complete,
    ord.rx_verified,
    ord.rx_denied,
    ord.is_refill,
    ord.order_complete,
    ord.bill_supplies,
    ord.bill_for_denial,

    -- Column Data
    ord."Rx #",
    ord.referral_source,
    ord.referral_source_auto_name,
    ord.territory,
    ord.territory_auto_name,
    ord.sales_rep,
    ord.sales_rep_auto_name,
    ord.therapy,
    ord.therapy_auto_name,
    ord.order_status_auto_name,
    ord.order_status,
    ord.start_date,
    ord.stop_date,
    ord.expiration_date,
    ord.next_fill_date,
    ord.written_date,
    ord.last_through_date,
    rx.dispense_quantity as raw_quantity,
    format_numeric(rx.dispense_quantity::numeric) as quantity,
    rx.day_supply,
    CASE 
    	WHEN rx.next_delivery_date IS NOT NULL THEN TO_CHAR(rx.next_delivery_date, 'MM/DD/YYYY')
    	ELSE NULL::text
    END as next_delivery_date,
    ord.fill_status,
    ord.dispense_type,
    ord.dispense_type_auto_name,
    ord.is_340b,
    ord.billing_method,
    ord.bill,
    ord.payer_type,
    ord.payer_type_auto_name,
    ord.ins_eff_date,
    ord.is_specialty,
    ord.requires_nursing,
    ord.nursing_status,
    ord.nursing_status_auto_name,
    ord.nursing_agency,
    ord.nursing_agency_auto_name,
    ord.infusion_suite,
    ord.infusion_suite_auto_name,
    ord.first_visit_date,
    ord.stat,
    ord.intake_substatus,
    ord.intake_substatus_auto_name,
    ord.diagnosis,
    ord.diagnosis_auto_name,
    ord.auth_flag,
    ord.bv_flag,
    ord.needs_auth,
    ord.needs_bv,
    ord.onhold_reason,
    ord.document_file_path,
    ord.document_id,
    ord.document_name,
    ord.is_erx,
    ord."NDC",
    ord."HCPC",
    ord.drug,
    ord.drug_auto_name,
    ord.drug_brand,
    ord.drug_brand_auto_name,
    ord.manufacturer,
    ord.manufacturer_auto_name,
    ord.physician,
    ord.physician_auto_name,
    ord.last_event,
    ord.last_event_auto_name,
    ord.last_event_by,
    ord.last_event_by_auto_name,
    ord.last_event_on,
    ord.patient_pay,
    CASE
        WHEN pt.phone_primary = 'Cell Phone' THEN pt.phone_cell
        WHEN pt.phone_primary = 'Home Phone' THEN pt.phone_home
        WHEN pt.phone_primary = 'Work Phone' THEN pt.phone_work
        ELSE COALESCE(pt.phone_cell, pt.phone_home, pt.phone_work)
    END AS phone
FROM vw_wf_orders_base ord
INNER JOIN form_careplan_order_rx rx ON rx.id = ord.rx_id
INNER JOIN form_patient pt ON pt.id = ord.patient_id 
    AND pt.archived IS NOT TRUE
    AND pt.deleted IS NOT TRUE;

CREATE OR REPLACE VIEW vw_wf_invoice_highest_priced_item_info AS
SELECT DISTINCT ON (lgl.invoice_no)
  lgl.invoice_no,
  invi.id as inventory_id,
  invi.id AS drug,
  invi.auto_name AS drug_auto_name,
  brd.id AS drug_brand,
  brd.auto_name AS drug_brand_auto_name,
  man.id AS manufacturer,
  man.auto_name AS manufacturer_auto_name,
  invi.formatted_ndc as "NDC",
  invi.hcpc_code as "HCPC",
  format_numeric(lgl.bill_quantity::numeric) as quantity,
  lgl.bill_quantity as raw_quantity
FROM form_ledger_charge_line lgl
INNER JOIN form_inventory invi ON invi.id = lgl.inventory_id
LEFT JOIN form_list_fdb_drug_brand brd ON invi.brand_name_id = brd.code AND
    brd.archived IS NOT TRUE
    AND brd.deleted IS NOT TRUE
LEFT JOIN form_list_manufacturer man ON man.code = invi.manufacturer_id AND
    man.archived IS NOT TRUE
    AND man.deleted IS NOT TRUE
WHERE lgl.archived IS NOT TRUE
  AND lgl.deleted IS NOT TRUE
  AND COALESCE(lgl.void, 'No') <> 'Yes'
  AND COALESCE(lgl.zeroed, 'No') <> 'Yes'
ORDER BY lgl.invoice_no, lgl.expected DESC;


CREATE OR REPLACE VIEW vw_wf_claims_based_queues AS
WITH claim_base AS (
    SELECT DISTINCT ON (bi.invoice_no)
        -- Links
        bi.invoice_no,
        bi.id as invoice_id,
        rxd.delivery_ticket_id,
        rxd.rx_id,
        bi.revenue_accepted,
        bi.revenue_accepted_posted,
        bi.site_id,
        bi.patient_id,
        bi.payer_id,
        bi.type_id,
        bi.billing_method_id,
        bi.bill_for_denial,
        bi.auth_flag,
        bi.date_of_service,
        hpi.inventory_id,

        -- Column Data
        st.id as site,
        st.auto_name as site_auto_name,
        py.id as payer,
        py.auto_name as payer_auto_name,
        pty.id as payer_type,
        pty.code as payer_type_code,
        pty.auto_name as payer_type_auto_name,
        bi.status as invoice_status,
        wft.id as collector,
        wft.auto_name as collector_auto_name,
        usr.id as biller,
        hpi.drug,
        hpi.drug_auto_name,
        hpi.drug_brand,
        hpi.drug_brand_auto_name,
        hpi.manufacturer,
        hpi.manufacturer_auto_name,
        hpi."NDC",
        hpi."HCPC",
        hpi.quantity,
        hpi.raw_quantity,
        CASE 
            WHEN usr.id IS NOT NULL THEN COALESCE(usr.auto_name, CONCAT(usr.firstname, ' ', usr.lastname))
            ELSE NULL
        END as biller_auto_name,
        CASE WHEN bi.total_pt_pay IS NOT NULL THEN format_currency(COALESCE(bi.total_pt_pay::numeric,0.00::numeric)) ELSE NULL END as patient_pay,
        CASE WHEN bi.total_expected IS NOT NULL THEN format_currency(COALESCE(bi.total_expected::numeric,0.00::numeric)) ELSE NULL END as expected,
        CASE WHEN bi.total_cost IS NOT NULL THEN format_currency(COALESCE(bi.total_cost::numeric,0.00::numeric)) ELSE NULL END as cost,
        CASE WHEN bi.total_billed IS NOT NULL THEN format_currency(COALESCE(bi.total_billed::numeric,0.00::numeric)) ELSE NULL END as billed,
        TO_CHAR(bi.date_of_service, 'MM/DD/YYYY') as "DOS"
    FROM form_billing_invoice bi
    INNER JOIN form_payer py ON py.id = bi.payer_id
        AND py.archived IS NOT TRUE
        AND py.deleted IS NOT TRUE
    INNER JOIN form_site st ON st.id = bi.site_id
        AND st.archived IS NOT TRUE
        AND st.deleted IS NOT TRUE
    LEFT JOIN vw_wf_invoice_highest_priced_item_info hpi ON hpi.invoice_no = bi.invoice_no
    LEFT JOIN form_list_payer_type pty ON pty.code = bi.type_id
        AND pty.archived IS NOT TRUE
        AND pty.deleted IS NOT TRUE
    LEFT JOIN form_wf_queue_team wft ON wft.code = bi.collector_id
        AND wft.archived IS NOT TRUE
        AND wft.deleted IS NOT TRUE
    INNER JOIN gr_form_billing_invoice_rx_id_to_careplan_order_rx_id grrx
        ON grrx.form_billing_invoice_fk = bi.id
    INNER JOIN form_careplan_order_rx_disp rxd ON rxd.rx_id = grrx.form_careplan_order_rx_fk
    LEFT JOIN form_careplan_delivery_tick dt ON dt.id = rxd.delivery_ticket_id
    LEFT JOIN form_user usr ON usr.id = dt.biller_id
        AND usr.archived IS NOT TRUE
        AND usr.deleted IS NOT TRUE
    WHERE bi.archived IS NOT TRUE
    AND bi.deleted IS NOT TRUE
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes'
    AND bi.delivery_ticket_id IS NULL
    AND (bi.status IS NULL OR bi.status IN ('Open', 'Confirmed'))
    AND COALESCE(bi.is_master_invoice, 'No') = 'Yes'
    AND bi.billing_method_id IN ('mm', 'ncpdp')
),
ncpdp_claims AS (
    SELECT 
        sfncpdp.form_billing_invoice_fk,
        ncpdp.status,
        subs.id as claim_substatus,
        subs.auto_name as claim_substatus_auto_name,
        ncpdp.claim_no
    FROM sf_form_billing_invoice_to_ncpdp sfncpdp
    JOIN form_ncpdp ncpdp ON ncpdp.id = sfncpdp.form_ncpdp_fk
        AND ncpdp.archived IS NOT TRUE
        AND ncpdp.deleted IS NOT TRUE
    LEFT JOIN form_list_billing_csstatus subs ON subs.code = ncpdp.substatus_id    
        AND subs.archived IS NOT TRUE
        AND subs.deleted IS NOT TRUE
    WHERE sfncpdp.archive IS NOT TRUE
    AND sfncpdp.delete IS NOT TRUE
),
med_claims AS (
    SELECT 
        sfmc.form_billing_invoice_fk,
        mc.status,
        subs.id as claim_substatus,
        subs.auto_name as claim_substatus_auto_name,
        mc.claim_no
    FROM sf_form_billing_invoice_to_med_claim sfmc
    JOIN form_med_claim mc ON mc.id = sfmc.form_med_claim_fk
        AND mc.archived IS NOT TRUE
        AND mc.deleted IS NOT TRUE
    LEFT JOIN form_list_billing_csstatus subs ON subs.code = mc.substatus_id
        AND subs.archived IS NOT TRUE
        AND subs.deleted IS NOT TRUE
    WHERE sfmc.archive IS NOT TRUE
    AND sfmc.delete IS NOT TRUE
)
SELECT
    'billing_invoice' as form_name,
    cb.invoice_id as form_id,
    cb.invoice_no,

    -- Linked IDs
    cb.site_id,
    cb.patient_id,
    cb.payer_id,
    cb.delivery_ticket_id,
    cb.invoice_id,
    cb.type_id,
    cb.billing_method_id,
    rx.refill_rx_id,
    rx.pa_id,
    rx.rx_id,
    rx.order_id,
    rx.team_id,
    rx.physician_id,
    rx.working_dispense_id,
    rx.insurance_id,
    rx.next_insurance_id,
    rx.supplies_insurance_id,
    rx.supply_billable_id,
    rx.supplies_pa_id,
    rx.rx_no,
    rx.order_no,
    rx.status_id,
    cb.inventory_id,

    -- Patient Data
    rx.patient_status,
    rx.patient_status_auto_name,
    rx.onhold_reason,
    rx.cancellation_reason,
    rx."DOB",
    rx.first_name,
    rx.last_name,
    rx."MRN",
    rx.team,
    rx.team_auto_name,

    -- Column Data
    rx.referral_source,
    rx.referral_source_auto_name,
    rx.territory,
    rx.territory_auto_name,
    rx.sales_rep,
    rx.sales_rep_auto_name,
    rx.therapy,
    rx.therapy_auto_name,
    rx.order_status_auto_name,
    rx.order_status,
    rx.stop_date,
    rx.expiration_date,
    rx.next_fill_date,
    rx.last_through_date,
    rx.day_supply,
    rx.fill_status,
    rx.dispense_type,
    rx.dispense_type_auto_name,
    rx.is_340b,
    rx.diagnosis,
    rx.diagnosis_auto_name,
    COALESCE(cb.auth_flag, rx.auth_flag) as auth_flag,
    CASE WHEN COALESCE(cb.auth_flag, rx.auth_flag) = 'Yes' THEN '⚠' ELSE NULL END as needs_auth,
    rx.physician,
    rx.physician_auto_name,
    rx.last_event,
    rx.last_event_auto_name,
    rx.last_event_by,
    rx.last_event_by_auto_name,
    rx.last_event_on,

    cb."HCPC",
    cb."NDC",
    cb.site,
    cb.site_auto_name,
    cb.payer,
    cb.payer_auto_name,
    cb.payer_type,
    cb.payer_type_code,
    cb.payer_type_auto_name,
    cb.patient_pay,
    cb.expected,
    cb.cost,
    cb.billed,
    cb."DOS",
    cb.collector,
    cb.collector_auto_name,
    cb.biller,
    cb.biller_auto_name,
    cb.drug,
    cb.drug_auto_name,
    cb.quantity,
    cb.raw_quantity,
    cb.drug_brand,
    cb.drug_brand_auto_name,
    cb.manufacturer,
    cb.manufacturer_auto_name,
    cb.revenue_accepted,
    cb.revenue_accepted_posted,
    cb.invoice_status,
    COALESCE(nc.status, mc.status) as claim_status,
    COALESCE(nc.claim_substatus, mc.claim_substatus) as claim_substatus,
    COALESCE(nc.claim_substatus_auto_name, mc.claim_substatus_auto_name) as claim_substatus_auto_name,

    CASE 
        WHEN nc.status IN ('Payable', 'Margin', 'Captured', 'Reversal Rejected', 'Rebill Rejected', 'PA Deferred', 'Duplicate') THEN 'Yes'
        WHEN mc.status IN ('Sent', 'Processing', 'Forwarded', 'Received', 'Accepted', 'Revised', 'Warning', 'On-hold', 'Awaiting Requested Information', 'Request for Additional Information', 'Under Review', 'Partially Paid', 'Payable') THEN 'Yes'
        ELSE 'No' 
    END as is_rebillable,
    CASE 
        WHEN nc.status IN ('Payable', 'Margin', 'Captured', 'Reversal Rejected', 'Rebill Rejected', 'PA Deferred', 'Duplicate') THEN 'Yes'
        WHEN mc.status IN ('Sent', 'Processing', 'Forwarded', 'Received', 'Accepted', 'Revised', 'Warning', 'On-hold', 'Awaiting Requested Information', 'Request for Additional Information', 'Under Review', 'Partially Paid', 'Payable') THEN 'Yes'
        ELSE 'No' 
    END as is_reversable,
    CASE 
        WHEN COALESCE(nc.status, mc.status) IS NULL THEN 'Yes' 
        ELSE 'No'
    END as can_be_adjudicated,
    CASE 
        WHEN COALESCE(nc.status, mc.status) IN ('Margin', 'PA Deferred', 'Duplicate') THEN '#FFD4B2'
        WHEN COALESCE(nc.status, mc.status) IN ('Reversal Rejected', 'Rebill Rejected', 'Error', 'Denied', 'Rejected') THEN '#FFCCCC'
        WHEN COALESCE(nc.status, mc.status) IN ('Partially Paid', 'Payable') THEN '#C2F2E3'
        ELSE NULL
    END as row_color,
    COALESCE(nc.claim_no, mc.claim_no)::text as claim_no
FROM claim_base cb
INNER JOIN vw_wf_rx_base rx ON rx.rx_id = cb.rx_id
LEFT JOIN ncpdp_claims nc ON nc.form_billing_invoice_fk = cb.invoice_id
LEFT JOIN med_claims mc ON mc.form_billing_invoice_fk = cb.invoice_id
ORDER BY cb.date_of_service ASC;

CREATE OR REPLACE VIEW vw_wf_dt_based_queue AS
WITH dt_items AS (
    SELECT DISTINCT
        dti.ticket_no,
        dti.ticket_item_no,
        dti.rx_id,
        dti.site_id,
        dti.delivery_ticket_id,
        dti.inventory_id,
        dti.dispense_quantity,
        dti.expected,
        dti.copay,
        dti.inventory_type as type
    FROM vw_delivery_items dti 
),
item_priority AS (
    SELECT *,
        CASE dti.type
            WHEN 'Drug' THEN 1
            WHEN 'Equipment Rental' THEN 2
            WHEN 'Billable' THEN 3
            WHEN 'Supply' THEN 4
            ELSE 99
        END AS priority_rank
    FROM dt_items dti
),
ticket_best_rank AS (
    SELECT
        delivery_ticket_id,
        MIN(priority_rank) AS best_rank
    FROM item_priority
    GROUP BY delivery_ticket_id
),
selected_ticket_items AS (
    SELECT ip.*
    FROM item_priority ip
    JOIN ticket_best_rank tbr
      ON ip.delivery_ticket_id = tbr.delivery_ticket_id
     AND ip.priority_rank = tbr.best_rank
),
primary_ticket_items AS (
    SELECT
        dti.*
    FROM selected_ticket_items dti
    JOIN form_careplan_dt_item fci
      ON fci.ticket_no = dti.ticket_no
     AND fci.ticket_item_no = dti.ticket_item_no
    WHERE fci.is_primary_drug = 'Yes'
),
rx_dispense_data AS (
    SELECT DISTINCT ON (rxd.rx_no)
        rxd.rx_id,
        rxd.rx_no,
        rxd.master_invoice_no,
        rxd.id as working_dispense_id,
        rxd.delivery_ticket_id,
        rxd.status as fill_status,
        rxd.refills,
        rxd.refills_remaining,
        rxd.doses_allowed,
        rxd.doses_remaining
    FROM dt_items dti
    JOIN form_careplan_order_rx_disp rxd ON rxd.rx_id = dti.rx_id
    WHERE rxd.archived IS NOT TRUE
    AND rxd.deleted IS NOT TRUE
    ORDER BY rxd.rx_no, rxd.id DESC
),
first_last_invoice_per_primary AS (
    SELECT
        pti.rx_id,
        MIN(bi.invoice_id) AS first_invoice_id,
        MAX(bi.invoice_id) AS last_invoice_id
    FROM primary_ticket_items pti
    JOIN rx_dispense_data disp ON disp.rx_id = pti.rx_id
    LEFT JOIN vw_invoice_claim_response_details bi 
        ON bi.master_invoice_no = disp.master_invoice_no
    WHERE bi.revenue_accepted = 'Yes'
    GROUP BY pti.rx_id
),
first_invoice_amounts AS (
    SELECT DISTINCT ON (fi.rx_id)
        fi.rx_id,
        bi.expected
    FROM first_last_invoice_per_primary fi
    JOIN vw_invoice_claim_response_details bi
      ON bi.invoice_id = fi.first_invoice_id
    ORDER BY fi.rx_id
),
last_invoice_amounts AS (
    SELECT DISTINCT ON (li.rx_id)
        li.rx_id,
        bi.copay
    FROM first_last_invoice_per_primary li
    JOIN vw_invoice_claim_response_details bi
      ON bi.invoice_id = li.last_invoice_id
    ORDER BY li.rx_id DESC
),
pricing_summary AS (
    SELECT
        dti.delivery_ticket_id,
        dti.rx_id,
        dti.ticket_item_no,
        format_currency(COALESCE(lastinv.copay, dti.copay)::numeric) AS copay,
        format_currency(COALESCE(firstinv.expected, dti.expected)::numeric) AS expected
    FROM selected_ticket_items dti
    LEFT JOIN last_invoice_amounts lastinv ON lastinv.rx_id = dti.rx_id
    LEFT JOIN first_invoice_amounts firstinv ON firstinv.rx_id = dti.rx_id
),
reject_reason AS (
    SELECT
        dtr.ticket_no,
        dtr.reason
    FROM form_dt_reject dtr
    WHERE dtr.archived IS NOT TRUE
    AND dtr.deleted IS NOT TRUE
),
delivery_base AS (
    SELECT
        dt.id,
        dt.careplan_id,
        dt.patient_id,
        dt.site_id,
        dt.status,
        dt.ticket_no,

        dt.ready_to_fill,
        dt.pulled_all_items,
        dt.tech_verified,
        dt.verified,
        dt.confirmed,
        dt.tech_supplies_verified,

        pt.patient_status,
        pt.patient_status_auto_name,
        pt."DOB",
        pt.first_name,
        pt.last_name,
        pt."MRN",
        pt.team,
        pt.team_auto_name,

        -- Join to selected_ticket_items for per-row item info
        sti.ticket_item_no,
        sti.rx_id,
        sti.inventory_id,
        sti.dispense_quantity AS quantity,
        fi.id as drug,
        fi.name as drug_auto_name,
        
        rx."Rx #" as "Rx #",
        rx.physician,
        rx.physician_auto_name,
        rx.last_event,
        rx.last_event_auto_name,
        rx.last_event_by,
        rx.last_event_by_auto_name,
        rx.last_event_on,
        COALESCE(rx.next_delivery_date, rx.next_fill_date) as next_delivery_date,
        rx.next_fill_date,
        rx.fill_status,
        rx.therapy,
        rx.therapy_auto_name,
        rx.is_refill,

        st.name as site,
        st.auto_name as site_auto_name,
        CASE WHEN COALESCE(dt.missing_signed_dt, 'No') = 'Yes' THEN '✓' ELSE NULL END as missing_signed_dt,
        TO_CHAR(dt.service_from, 'MM/DD/YYYY') as from_date,
        TO_CHAR(dt.service_to, 'MM/DD/YYYY') as to_date,
        TO_CHAR(dt.delivery_date, 'MM/DD/YYYY') as delivery_date,
        usr.id as biller,
        usr.auto_name as biller_auto_name

    FROM form_careplan_delivery_tick dt
    -- Join to selected_ticket_items for per-row context
    JOIN selected_ticket_items sti ON sti.delivery_ticket_id = dt.id
    -- For inventory/drug info
    LEFT JOIN form_inventory fi ON fi.id = sti.inventory_id AND fi.archived IS NOT TRUE AND fi.deleted IS NOT TRUE
    -- For Rx/prescriber info (optional, if rx_id exists)
    LEFT JOIN vw_wf_rx_base rx ON rx.rx_id = sti.rx_id
    LEFT JOIN form_user usr ON usr.id = dt.biller_id AND usr.archived IS NOT TRUE AND usr.deleted IS NOT TRUE
    INNER JOIN vw_wf_patients_base pt ON pt.patient_id = dt.patient_id
    INNER JOIN form_site st ON st.id = dt.site_id AND st.archived IS NOT TRUE AND st.deleted IS NOT TRUE
    WHERE dt.status IN ('delivery_ticket', 'ready_to_fill', 'order_ver', 'pending_conf', 'ready_to_bill')
        AND dt.archived IS NOT TRUE
        AND dt.deleted IS NOT TRUE
        AND COALESCE(dt.void, 'No') <> 'Yes'
),
shipping_info AS (
    SELECT 
    	sfdl.form_careplan_delivery_tick_fk as delivery_ticket_id,
        patient_id,
        ship_method_id,
        tracking_no,
        sm.id as ship_method,
        sm.auto_name as ship_method_auto_name
    FROM form_careplan_delivery_log dl
    INNER JOIN sf_form_careplan_delivery_tick_to_careplan_delivery_log sfdl ON sfdl.form_careplan_delivery_log_fk = dl.id
    LEFT JOIN form_list_shipping_method sm ON sm.code = dl.ship_method_id
        AND sm.archived IS NOT TRUE
        AND sm.deleted IS NOT TRUE
    WHERE dl.archived IS NOT TRUE
    AND dl.deleted IS NOT TRUE
    AND dl.ship_method_id IS NOT NULL
)
SELECT
    'careplan_delivery_tick' as form_name,
    dt.id as form_id,
    dt.id,
    dt.id as delivery_ticket_id,
    dt.careplan_id,
    dt.patient_id,
    dt.site_id,
    dt.status,
    dt.ticket_no,
    dt.ready_to_fill,
    dt.pulled_all_items,
    dt.tech_verified,
    dt.verified,
    dt.confirmed,
    dt.tech_supplies_verified,
    dt.patient_status,
    dt.patient_status_auto_name,
    dt."DOB",
    dt.first_name,
    dt.last_name,
    dt."MRN",
    dt.team,
    dt.team_auto_name,
    dt.last_event,
    dt.last_event_auto_name,
    dt.last_event_by,
    dt.last_event_by_auto_name,
    dt.last_event_on,
    dt.fill_status,
    dt.drug,
    dt.drug_auto_name,
    dt.quantity,
    dt.physician,
    dt.physician_auto_name,
    dt.next_delivery_date,
    dt.next_fill_date,
    dt.therapy,
    dt.therapy_auto_name,
    dt.site,
    dt.site_auto_name,
    dt.missing_signed_dt,
    dt.from_date,
    dt.to_date,
    dt.biller,
    dt.biller_auto_name,
    dt.delivery_date,
    ship.ship_method,
    ship.ship_method_auto_name,
    prc.copay,
    prc.expected,
    CASE 
        WHEN EXISTS (
            SELECT 1 
            FROM form_careplan_delivery_tick dtp 
            WHERE dtp.patient_id = dt.patient_id 
            AND dtp.id < dt.id
        ) THEN NULL::text
        ELSE '✓'
    END AS new_patient,
    dt.is_refill,
    ship.tracking_no,
    rr.reason as reject_reason
FROM delivery_base dt
JOIN selected_ticket_items sti 
    ON sti.delivery_ticket_id = dt.id
   AND sti.rx_id = dt.rx_id
   AND sti.ticket_item_no = dt.ticket_item_no
LEFT JOIN pricing_summary prc 
    ON prc.delivery_ticket_id = sti.delivery_ticket_id
   AND prc.rx_id = sti.rx_id
   AND prc.ticket_item_no = sti.ticket_item_no
LEFT JOIN shipping_info ship ON ship.delivery_ticket_id = dt.id
LEFT JOIN reject_reason rr ON rr.ticket_no = dt.ticket_no
ORDER BY to_date(dt.next_delivery_date, 'MM/DD/YYYY') ASC;

CREATE OR REPLACE VIEW vw_wf_charges_based_queue AS
WITH charge_base AS (
    SELECT
        -- Links
        lgl.id,
        lgl.inventory_id,
        lgl.compound_no,
        lgl.calc_invoice_split_no,
        lgl.charge_no,
        lgl.patient_id,
        lgl.parent_charge_no,
        lgl.master_charge_no,
        lgl.billed,
        lgl.expected,
        lgl.view_total_cost,
        lgl.copay,
        lgl.list_price,
        lgl.order_rx_id,
        lgl.fill_number,
        lgl.ticket_no,
        lgl.ticket_item_no,
        lgl.hcpc_code,
        lgl.formatted_ndc,
        lgl.payer_id,
        lgl.insurance_id,
        lgl.billing_method_id,
        lgl.site_id
    FROM form_ledger_charge_line lgl
    WHERE COALESCE(lgl.void, 'No') <> 'Yes' 
    AND COALESCE(lgl.zeroed, 'No') <> 'Yes'
    AND lgl.invoice_no IS NULL
    AND lgl.ticket_no IS NOT NULL
    AND lgl.deleted IS NOT TRUE
    AND lgl.archived IS NOT TRUE
),
grouped_charges AS (
    SELECT
        FIRST_VALUE(cb.id) OVER (
            PARTITION BY cb.calc_invoice_split_no 
            ORDER BY cb.expected::numeric DESC
        ) as highest_expected_charge_line_id,
        CASE WHEN cb.billed IS NOT NULL THEN format_currency(COALESCE(SUM(cb.billed::numeric),0.00::numeric)) ELSE NULL END as billed,
        CASE WHEN cb.expected IS NOT NULL THEN format_currency(COALESCE(SUM(cb.expected::numeric),0.00::numeric)) ELSE NULL END as expected,
        CASE WHEN cb.view_total_cost IS NOT NULL THEN format_currency(COALESCE(SUM(cb.view_total_cost::numeric),0.00::numeric)) ELSE NULL END as cost,
        CASE WHEN cb.copay IS NOT NULL THEN format_currency(COALESCE(SUM(cb.copay::numeric),0.00::numeric)) ELSE NULL END as patient_pay,
        cb.calc_invoice_split_no
    FROM charge_base cb
    GROUP BY cb.calc_invoice_split_no,cb.id,cb.billed,cb.expected,cb.view_total_cost,cb.copay
),
dt_items AS (
    SELECT DISTINCT
        dti.ticket_no,
        dti.ticket_item_no,
        dti.rx_id,
        dti.site_id,
        dti.delivery_ticket_id,
        dti.inventory_id,
        dti.dispense_quantity,
        dti.expected as expected_amount,
        dti.copay
    FROM vw_delivery_items dti
    WHERE EXISTS (
        SELECT 1
        FROM charge_base cb
        WHERE cb.ticket_no = dti.ticket_no

        UNION

        SELECT 1
        FROM form_billing_invoice bi
        WHERE bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
        AND COALESCE(bi.void, 'No') <> 'Yes'
        AND COALESCE(bi.zeroed, 'No') <> 'Yes'
        AND bi.delivery_ticket_id = dti.delivery_ticket_id
        AND bi.status IN ('Open', 'Accepted')
    )
),
high_price_filter as (
    SELECT DISTINCT ON (dti.delivery_ticket_id)
        dti.ticket_item_no
    FROM dt_items dti
    ORDER BY dti.delivery_ticket_id, dti.expected_amount DESC
),
highest_priced_drug AS (
    SELECT DISTINCT ON (dti.delivery_ticket_id)
        -- Links
        dti.site_id,
        dti.inventory_id,
        dti.ticket_no,
        dti.ticket_item_no,
        dti.rx_id,
        dti.delivery_ticket_id,

        -- Column Data
        rx."Rx #",
        rx.physician,
        rx.physician_auto_name,
        rx.last_event,
        rx.last_event_auto_name,
        rx.last_event_by,
        rx.last_event_by_auto_name,
        rx.last_event_on,
        COALESCE(rx.next_delivery_date, rx.next_fill_date) as next_delivery_date,
        rx.next_fill_date,
        rx.fill_status,
        rx.therapy,
        rx.therapy_auto_name,
        rx.is_refill,
        inv.id as drug,
        inv.name as drug_auto_name,
        dti.dispense_quantity as quantity
    FROM dt_items dti
    INNER JOIN high_price_filter hpf ON hpf.ticket_item_no = dti.ticket_item_no
    INNER JOIN form_inventory inv ON inv.id = dti.inventory_id
        AND inv.archived IS NOT TRUE
        AND inv.deleted IS NOT TRUE
    INNER JOIN vw_wf_rx_base rx ON rx.rx_id = dti.rx_id
),
delivery_base AS (
    SELECT
        pt.patient_status,
        pt.patient_status_auto_name,
        pt."DOB",
        pt.first_name,
        pt.last_name,
        pt."MRN",
        pt.team,
        pt.team_auto_name,

        hbd.last_event,
        hbd.last_event_auto_name,
        hbd.last_event_by,
        hbd.last_event_by_auto_name,
        hbd.last_event_on,
        hbd.fill_status,
        hbd.drug,
        hbd.drug_auto_name,
        hbd.quantity,
        hbd.physician,
        hbd.physician_auto_name,
        hbd.next_delivery_date,
        hbd.next_fill_date,
        hbd.therapy,
        hbd.is_refill,
        hbd.therapy_auto_name,
        st.name as site,
        st.auto_name as site_auto_name,
        dt.service_from as from_date,
        dt.service_to as to_date,
        dt.delivery_date,
        dt.ticket_no,
        dt.id,
        usr.id as biller,
        usr.auto_name as biller_auto_name
    FROM form_careplan_delivery_tick dt
    LEFT JOIN form_user usr ON usr.id = dt.biller_id
        AND usr.archived IS NOT TRUE
        AND usr.deleted IS NOT TRUE
    INNER JOIN vw_wf_patients_base pt ON pt.patient_id = dt.patient_id
    LEFT JOIN highest_priced_drug hbd ON hbd.delivery_ticket_id = dt.id
    INNER JOIN form_site st ON st.id = dt.site_id
        AND st.archived IS NOT TRUE
        AND st.deleted IS NOT TRUE
    WHERE dt.status IN ('ready_to_bill', 'billed')
    AND dt.archived IS NOT TRUE
    AND dt.deleted IS NOT TRUE
    AND COALESCE(dt.void, 'No') <> 'Yes'
),
grouped_charges_summary AS (
    SELECT
        'view_create_invoice' as form_name,
        NULL::integer as form_id,
        dt.ticket_no::text as ticket_no,
        cb.calc_invoice_split_no::text as invoice_no,
        cb.calc_invoice_split_no::text as calc_invoice_split_no,
        dt.id as delivery_ticket_id,
        cb.site_id,
        cb.patient_id,
        pi.payer_id,
        pi.id as insurance_id,
        pi.type_id,
        pi.billing_method_id,
        pi.bill_for_denial,
        null::text as auth_flag,

        -- Column Data
        dt.patient_status,
        dt.patient_status_auto_name,
        dt."DOB",
        dt.first_name,
        dt.last_name,
        dt."MRN",
        dt.team,
        dt.team_auto_name,
        dt.last_event,
        dt.last_event_auto_name,
        dt.last_event_by,
        dt.last_event_by_auto_name,
        dt.last_event_on,
        dt.physician,
        dt.physician_auto_name,
        dt.therapy,
        dt.therapy_auto_name,
        dt.site,
        dt.site_auto_name,
        TO_CHAR(dt.from_date, 'MM/DD/YYYY') as from_date,
        TO_CHAR(dt.to_date, 'MM/DD/YYYY') as to_date,
        dt.biller,
        dt.biller_auto_name,
        inv.hcpc_code as "HCPC",
        inv.formatted_ndc as "NDC",
        gc.billed::text as billed,
        gc.expected::text as expected,
        gc.cost::text as cost,
        gc.patient_pay::text as patient_pay,
        py.id as payer,
        py.auto_name as payer_auto_name,
        pty.id as payer_type,
        pty.auto_name as payer_type_auto_name,
        wft.id as collector,
        wft.auto_name as collector_auto_name,
        bmt.id as billing_method,
        bmt.auto_name as billing_method_auto_name,
        inv.id as drug,
        inv.auto_name as drug_auto_name,
        NULL::text as claim_no
    FROM grouped_charges gc
    LEFT JOIN charge_base cb ON cb.id = gc.highest_expected_charge_line_id
    INNER JOIN delivery_base dt ON dt.ticket_no = cb.ticket_no
    INNER JOIN form_payer py ON py.id = cb.payer_id
        AND py.archived IS NOT TRUE
        AND py.deleted IS NOT TRUE
    INNER JOIN form_patient_insurance pi ON pi.id = cb.insurance_id
        AND pi.archived IS NOT TRUE
        AND pi.deleted IS NOT TRUE
    INNER JOIN form_inventory inv ON inv.id = cb.inventory_id
        AND inv.archived IS NOT TRUE
        AND inv.deleted IS NOT TRUE
    LEFT JOIN form_list_payer_type pty ON pty.code::text = py.type_id
        AND pty.archived IS NOT TRUE
        AND pty.deleted IS NOT TRUE
    LEFT JOIN form_wf_queue_team wft ON wft.code = py.collector_id
        AND wft.archived IS NOT TRUE
        AND wft.deleted IS NOT TRUE
    LEFT JOIN form_list_billing_method bmt ON bmt.code = cb.billing_method_id
        AND bmt.archived IS NOT TRUE
        AND bmt.deleted IS NOT TRUE
),
billing_invoices AS (
    SELECT DISTINCT ON (bi.invoice_no)
        -- Links
        'billing_invoice' as form_name,
        bi.id as form_id,
        dt.ticket_no::text as ticket_no,
        bi.invoice_no::text as invoice_no,
        NULL::text as calc_invoice_split_no,
        bi.delivery_ticket_id,
        bi.site_id,
        bi.patient_id,
        bi.payer_id,
        bi.insurance_id,
        bi.type_id,
        bi.billing_method_id,
        bi.bill_for_denial,
		bi.auth_flag::text as auth_flag,
		
        -- Column Data
        dt.patient_status,
        dt.patient_status_auto_name,
        dt."DOB",
        dt.first_name,
        dt.last_name,
        dt."MRN",
        dt.team,
        dt.team_auto_name,
        dt.last_event,
        dt.last_event_auto_name,
        dt.last_event_by,
        dt.last_event_by_auto_name,
        dt.last_event_on,
        dt.physician,
        dt.physician_auto_name,
        dt.therapy,
        dt.therapy_auto_name,
        dt.site,
        dt.site_auto_name,
        TO_CHAR(dt.from_date, 'MM/DD/YYYY') as from_date,
        TO_CHAR(dt.to_date, 'MM/DD/YYYY') as to_date,
        dt.biller,
        dt.biller_auto_name,
        lgl.hcpc_code as "HCPC",
        lgl.formatted_ndc as "NDC",
        CASE WHEN bi.total_billed IS NOT NULL THEN format_currency(COALESCE(bi.total_billed::numeric,0.00::numeric))::text ELSE NULL::text END as billed,
        CASE WHEN bi.total_expected IS NOT NULL THEN format_currency(COALESCE(bi.total_expected::numeric,0.00::numeric))::text ELSE NULL::text END as expected,
        CASE WHEN bi.total_cost IS NOT NULL THEN format_currency(COALESCE(bi.total_cost::numeric,0.00::numeric)) ELSE NULL::text END as cost,
        CASE WHEN bi.total_pt_pay IS NOT NULL THEN format_currency(COALESCE(bi.total_pt_pay::numeric,0.00::numeric)) ELSE NULL::text END as patient_pay,
        py.id as payer,
        py.auto_name as payer_auto_name,
        pty.id as payer_type,
        pty.auto_name as payer_type_auto_name,
        wft.id as collector,
        wft.auto_name as collector_auto_name,
        bmt.id as billing_method,
        bmt.auto_name as billing_method_auto_name,
        hpi.inventory_id as drug,
        hpi.drug_auto_name,
        COALESCE(nc.claim_no, mc.claim_no, pc.claim_no)::text as claim_no
    FROM form_billing_invoice bi
    INNER JOIN delivery_base dt ON dt.id = bi.delivery_ticket_id
    INNER JOIN vw_wf_invoice_highest_priced_item_info hpi ON hpi.invoice_no = bi.invoice_no
    INNER JOIN form_payer py ON py.id = bi.payer_id
        AND py.archived IS NOT TRUE
        AND py.deleted IS NOT TRUE
    LEFT JOIN form_list_payer_type pty ON pty.code::text = py.type_id
        AND pty.archived IS NOT TRUE
        AND pty.deleted IS NOT TRUE
    LEFT JOIN form_wf_queue_team wft ON wft.code = bi.collector_id
        AND wft.archived IS NOT TRUE
        AND wft.deleted IS NOT TRUE
    INNER JOIN gr_form_billing_invoice_rx_id_to_careplan_order_rx_id grrx
        ON grrx.form_billing_invoice_fk = bi.id
    INNER JOIN form_careplan_order_rx_disp rxd ON rxd.rx_id = grrx.form_careplan_order_rx_fk
        AND rxd.archived IS NOT TRUE
        AND rxd.deleted IS NOT TRUE
    LEFT JOIN form_list_billing_method bmt ON bmt.code = bi.billing_method_id
        AND bmt.archived IS NOT TRUE
        AND bmt.deleted IS NOT TRUE
    LEFT JOIN form_ledger_charge_line lgl ON lgl.invoice_no = bi.invoice_no
        AND lgl.archived IS NOT TRUE
        AND lgl.deleted IS NOT TRUE
        AND COALESCE(lgl.void, 'No') <> 'Yes'
        AND COALESCE(lgl.zeroed, 'No') <> 'Yes'
    LEFT JOIN sf_form_billing_invoice_to_med_claim_1500 bitpc ON bitpc.form_billing_invoice_fk = bi.id 
        AND bitpc.archive IS NOT TRUE
        AND bitpc.delete IS NOT TRUE
    LEFT JOIN form_med_claim_1500 pc ON pc.id = bitpc.form_med_claim_1500_fk 
        AND COALESCE(pc.void, 'No') <> 'Yes'
        AND pc.archived IS NOT TRUE
        AND pc.deleted IS NOT TRUE
    LEFT JOIN sf_form_billing_invoice_to_med_claim bitmc ON bitmc.form_billing_invoice_fk = bi.id 
        AND bitmc.archive IS NOT TRUE
        AND bitmc.delete IS NOT TRUE
    LEFT JOIN form_med_claim mc ON mc.id = bitmc.form_med_claim_fk 
        AND COALESCE(mc.void, 'No') <> 'Yes' 
        AND mc.archived IS NOT TRUE
        AND mc.deleted IS NOT TRUE
    LEFT JOIN sf_form_billing_invoice_to_ncpdp bitnc ON bitnc.form_billing_invoice_fk = bi.id 
        AND bitnc.archive IS NOT TRUE
        AND bitnc.delete IS NOT TRUE
    LEFT JOIN form_ncpdp nc ON nc.id = bitnc.form_ncpdp_fk 
        AND COALESCE(nc.void, 'No') <> 'Yes' 
        AND nc.archived IS NOT TRUE
        AND nc.deleted IS NOT TRUE
    INNER JOIN form_list_billing_csstatus subs ON subs.code = COALESCE(nc.substatus_id, mc.substatus_id, pc.substatus_id)
        AND subs.archived IS NOT TRUE
        AND subs.deleted IS NOT TRUE
    WHERE bi.archived IS NOT TRUE
    AND bi.deleted IS NOT TRUE
    AND (subs.code IN ('101', '105', '102') -- Ready, Paid, Captured
    OR (bi.delivery_ticket_id IS NOT NULL AND COALESCE(bi.revenue_accepted_posted, 'No') <> 'Yes'))
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes'
),
grouped_all AS (
    SELECT * FROM grouped_charges_summary
    UNION ALL
    SELECT * FROM billing_invoices
)
SELECT
   ga.*
FROM grouped_all ga
ORDER BY ga.from_date ASC;

CREATE OR REPLACE VIEW vw_wf_pa_based_queue AS
WITH latest_pa AS (
    SELECT DISTINCT ON (pa.insurance_id, pa.pa_type) 
        pa.id,
        pa.insurance_id,
        pa.pa_type,
        pa.expire_date,
        pa.created_on
    FROM form_patient_prior_auth pa
    WHERE pa.archived IS NOT TRUE 
    AND pa.deleted IS NOT TRUE
    ORDER BY pa.insurance_id, pa.pa_type, pa.created_on DESC
),
pa_base AS (
    SELECT
        pa.id,
        pa.patient_id,
        pa.insurance_id,
        pa.pa_type,
        pa.auth_type,
        pa.status_id,
        pa.drug_approval_date,
        pa.dme_approval_date,
        pa.nurse_approval_date,
        pa.effective_date,
        pa.expire_date,
        pa.limits,
        pa.unit_limit,
        pa.limit_freq,
        pa.refills_limit,
        pa.submission_datetime,
        pa.submission_method,
        pa.request_id,
        pa.clinic_docs_requested,
        pa.cmm_number,
        pa.denied_datetime,
        pa.appeal_datetime,
        pa.appeal_docs,
        pa.number,
        pa.calendar_id,
        pa.expected_price,
        pa.billing_comment,
        pa.created_on,
        pa.created_by
    FROM form_patient_prior_auth pa
    INNER JOIN latest_pa lpa ON lpa.id = pa.id
    WHERE pa.archived IS NOT TRUE
    AND pa.deleted IS NOT TRUE
    AND pa.status_id NOT IN ('7')
),
hcpc_codes AS (
    SELECT
        grhcpc.form_patient_prior_auth_fk,
        STRING_AGG(grhcpc.form_list_fdb_medicare_desc_fk, ', ') as codes
    FROM gr_form_patient_prior_auth_hc_id_to_list_fdb_medicare_desc_id grhcpc
    GROUP BY grhcpc.form_patient_prior_auth_fk
)
SELECT DISTINCT
    'patient_prior_auth' as form_name,
    pa.id as form_id,

    -- Links
    py.id as payer_id,
    py.type_id as payer_type_id,
    ins.id as insurance_id,
    pt.patient_id,
    pt.site_id,
    pa.status_id,
    pa.calendar_id,

    -- Column Data
    pt.last_name,
    pt.first_name,
    pt.site,
    pt.site_auto_name,
    pt.physician,
    pt.physician_auto_name,
    pt.team_id,
    pt.team,
    pt.team_auto_name,
    pt.patient_status,
    pt.patient_status_auto_name,
    pt.territory,
    pt.territory_auto_name,
    pt.sales_rep,
    pt.sales_rep_auto_name,
    pt.referral_source,
    pt.referral_source_auto_name,
    py.id as payer,
    py.organization as payer_auto_name,
    pt."MRN",
    pa.pa_type as pa_type,
    CASE WHEN pa.effective_date IS NOT NULL THEN TO_CHAR(pa.effective_date, 'MM/DD/YYYY') ELSE NULL::text END as effective_date,
    CASE WHEN pa.expire_date IS NOT NULL THEN TO_CHAR(pa.expire_date, 'MM/DD/YYYY') ELSE NULL::text END as expire_date,
    pa.expire_date as expire_date_raw, -- Added raw field for ORDER BY
    CASE WHEN COALESCE(pa.drug_approval_date,pa.dme_approval_date,pa.nurse_approval_date) IS NOT NULL THEN TO_CHAR(GREATEST(pa.drug_approval_date,pa.dme_approval_date,pa.nurse_approval_date), 'MM/DD/YYYY') ELSE NULL::text END as approval_date,
    CASE
        WHEN pa.submission_datetime IS NOT NULL THEN TO_CHAR(pa.submission_datetime, 'MM/DD/YYYY HH:MI AM')
        ELSE NULL::text
    END as submission_datetime,
    CASE
        WHEN pa.status_id = '5' AND pa.limits = 'Total Units' THEN CONCAT(pa.unit_limit, ' units')
        WHEN pa.status_id = '5' AND pa.limits =  'Units/Frequency' THEN CONCAT(pa.unit_limit, ' units per ', pa.limit_freq)
        WHEN pa.status_id = '5' AND pa.limits = 'Fills' THEN CONCAT(pa.refills_limit, ' refills')
        WHEN pa.status_id = '5' THEN 'No limits'
        ELSE NULL::text
    END as limit,
    CASE
        WHEN pa.status_id NOT IN ('5', '6') THEN pa.request_id
        ELSE NULL::text
    END as request_id,
    CASE
        WHEN pa.status_id IN ('1') THEN pa.clinic_docs_requested
        ELSE NULL::text
    END as clinic_docs_requested,
    CASE
        WHEN pa.status_id IN ('3') THEN pa.cmm_number
        ELSE NULL::text
    END as cmm_number,
    pastat.name as status,
    pastat.auto_name as status_auto_name,
    CASE
        WHEN pa.status_id IN ('6', '8') THEN TO_CHAR(pa.denied_datetime, 'MM/DD/YYYY HH:MI AM')
        ELSE NULL::text
    END as denied_datetime,
    CASE
        WHEN pa.status_id IN ('8') THEN TO_CHAR(pa.appeal_datetime, 'MM/DD/YYYY HH:MI AM')
        ELSE NULL::text
    END as appeal_datetime,
    CASE
        WHEN pa.status_id IN ('8') THEN pa.appeal_docs
        ELSE NULL::text
    END as appeal_docs,
    pa.submission_method as submission_method,
    pa.number as auth_number,
    CASE
        WHEN pa.expected_price IS NOT NULL THEN format_currency(pa.expected_price::numeric)
        ELSE NULL::text
    END as "Exp $",
    pa.billing_comment as comments,
    hcpc.codes as "HCPCS",
    TO_CHAR(pa.created_on, 'MM/DD/YYYY HH:MI AM') as created_on,
    COALESCE(usr.displayname, CONCAT(usr.firstname, ' ', usr.lastname)) as created_by,
    replace(trim(both '{}' from pa.pa_type), ',', ', ') as auth_for,
    pa.auth_type
FROM pa_base pa
INNER JOIN form_user usr ON usr.id = pa.created_by
    AND usr.archived IS NOT TRUE
    AND usr.deleted IS NOT TRUE
INNER JOIN form_list_pa_status pastat ON pastat.code = pa.status_id
    AND pastat.archived IS NOT TRUE
    AND pastat.deleted IS NOT TRUE
INNER JOIN form_patient_insurance ins ON ins.id = pa.insurance_id
    AND ins.archived IS NOT TRUE
    AND ins.deleted IS NOT TRUE
    AND COALESCE(ins.active, 'No') = 'Yes'
INNER JOIN vw_wf_patients_base pt ON pt.patient_id = pa.patient_id
    AND pt.status_id IN ('1', '2', '3')
LEFT JOIN form_careplan_order_item itm ON itm.drug_pa_id = pa.id
    AND itm.archived IS NOT TRUE
    AND itm.deleted IS NOT TRUE
LEFT JOIN form_careplan_orderp_item itmp ON itmp.drug_pa_id = pa.id
LEFT JOIN form_careplan_order_rental rnt ON rnt.rental_pa_id = pa.id
    AND rnt.archived IS NOT TRUE
    AND rnt.deleted IS NOT TRUE
LEFT JOIN form_careplan_order cos ON cos.supplies_pa_id = pa.id
    AND cos.archived IS NOT TRUE
    AND cos.deleted IS NOT TRUE
LEFT JOIN form_careplan_order con ON con.nursing_pa_id = pa.id
    AND con.archived IS NOT TRUE
    AND con.deleted IS NOT TRUE
LEFT JOIN form_inventory inv ON inv.id = COALESCE(itm.inventory_id, itmp.inventory_id, rnt.inventory_id)
	AND inv.archived IS NOT TRUE
	AND inv.deleted IS NOT TRUE
INNER JOIN form_payer py ON py.id = ins.payer_id
    AND py.archived IS NOT TRUE
    AND py.deleted IS NOT TRUE
LEFT JOIN hcpc_codes hcpc ON hcpc.form_patient_prior_auth_fk = pa.id
ORDER BY expire_date_raw DESC;
