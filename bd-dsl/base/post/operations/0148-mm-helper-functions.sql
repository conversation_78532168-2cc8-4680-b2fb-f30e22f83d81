CREATE OR REPLACE FUNCTION build_mm_claim_dx_loop(
  p_charge_lines charge_line_with_split[],
  p_parent_claim_no text DEFAULT NULL
) RETURNS mm_dx_info[] AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_dx_info[];
  v_error_message text;
  v_params jsonb;
  v_patient_id integer;
BEGIN

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'parent_claim_no', p_parent_claim_no
  );

  BEGIN

    -- Log function call
    PERFORM log_billing_function(
      'build_mm_dx_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM claim diagnosis loop for parent claim no: %', p_parent_claim_no;

    -- If parent claim is provided, copy diagnosis from parent claim
    IF p_parent_claim_no IS NOT NULL THEN
        -- Get diagnosis information from parent medical claim using proper subform joining
        WITH parent_claim_dx AS (
            SELECT DISTINCT
                mc.patient_id::integer as patient_id,
                dx.dx_id::integer as dx_id,
                dx.diagnosis_type_code::text as diagnosis_type_code,
                dx.diagnosis_code::text as diagnosis_code,
                dx.id::integer as id
            FROM form_med_claim mc
            INNER JOIN sf_form_med_claim_to_med_claim_info sf_info ON sf_info.form_med_claim_fk = mc.id
            INNER JOIN form_med_claim_info ci ON ci.id = sf_info.form_med_claim_info_fk
            INNER JOIN sf_form_med_claim_info_to_med_claim_dx sf_dx ON sf_dx.form_med_claim_info_fk = ci.id
            INNER JOIN form_med_claim_dx dx ON dx.id = sf_dx.form_med_claim_dx_fk
            WHERE mc.claim_no = p_parent_claim_no
              AND mc.deleted IS NOT TRUE 
              AND mc.archived IS NOT TRUE
              AND sf_info.delete IS NOT TRUE 
              AND sf_info.archive IS NOT TRUE
              AND ci.deleted IS NOT TRUE 
              AND ci.archived IS NOT TRUE
              AND sf_dx.delete IS NOT TRUE 
              AND sf_dx.archive IS NOT TRUE
              AND dx.deleted IS NOT TRUE 
              AND dx.archived IS NOT TRUE
              AND dx.diagnosis_code IS NOT NULL
        )
        SELECT array_agg(
            (
                pcd.patient_id,
                pcd.dx_id,
                pcd.diagnosis_type_code,
                pcd.diagnosis_code
            )::mm_dx_info
            ORDER BY pcd.id ASC
        )
        INTO v_result
        FROM parent_claim_dx pcd;

        -- If found in medical claim, return it
        IF v_result IS NOT NULL AND array_length(v_result, 1) > 0 THEN
            RAISE LOG 'Found % diagnosis codes from parent medical claim', array_length(v_result, 1);
            
            -- Log success
            PERFORM log_billing_function(
              'build_mm_dx_loop'::tracked_function,
              v_params,
              NULL::jsonb,
              NULL::text,
              clock_timestamp() - v_start_time
            );
            
            RETURN v_result;
        END IF;

        -- If not found in medical claim, try NCPDP claim using proper subform joining
        WITH parent_ncpdp_dx AS (
            SELECT DISTINCT
                nc.patient_id::integer as patient_id,
                pd.dx_id::text as dx_id,
                dx.code::text as diagnosis_code,
                pd.id::integer as id,
                ROW_NUMBER() OVER (ORDER BY pd.id) as row_num
            FROM form_ncpdp nc
            INNER JOIN sf_form_ncpdp_to_ncpdp_clinical sf_clinical ON sf_clinical.form_ncpdp_fk = nc.id
            INNER JOIN form_ncpdp_clinical clinical ON clinical.id = sf_clinical.form_ncpdp_clinical_fk
            INNER JOIN sf_form_ncpdp_clinical_to_ncpdp_clinical_dx sf_dx ON sf_dx.form_ncpdp_clinical_fk = clinical.id
            INNER JOIN form_ncpdp_clinical_dx cdx ON cdx.id = sf_dx.form_ncpdp_clinical_dx_fk
            INNER JOIN form_patient_diagnosis pd ON pd.id = cdx.dx_id
            INNER JOIN form_list_diagnosis dx ON dx.code = pd.dx_id
            WHERE nc.claim_no = p_parent_claim_no
              AND nc.deleted IS NOT TRUE 
              AND nc.archived IS NOT TRUE
              AND sf_clinical.delete IS NOT TRUE 
              AND sf_clinical.archive IS NOT TRUE
              AND clinical.deleted IS NOT TRUE 
              AND clinical.archived IS NOT TRUE
              AND sf_dx.delete IS NOT TRUE 
              AND sf_dx.archive IS NOT TRUE
              AND cdx.deleted IS NOT TRUE 
              AND cdx.archived IS NOT TRUE
              AND pd.deleted IS NOT TRUE 
              AND pd.archived IS NOT TRUE
              AND dx.deleted IS NOT TRUE 
              AND dx.archived IS NOT TRUE
              AND dx.code IS NOT NULL
        )
        SELECT array_agg(
            (
                pnd.patient_id,
                pnd.id,  -- This is already the patient_diagnosis.id from the CTE
                CASE 
                    WHEN pnd.row_num = 1 THEN 'ABK'  -- Primary diagnosis (ICD-10-CM Principal)
                    ELSE 'ABF'                       -- Secondary diagnosis (ICD-10-CM)
                END::text,
                pnd.diagnosis_code
            )::mm_dx_info
            ORDER BY pnd.row_num
        )
        INTO v_result
        FROM parent_ncpdp_dx pnd;

        -- If found in NCPDP claim, return it
        IF v_result IS NOT NULL AND array_length(v_result, 1) > 0 THEN
            RAISE LOG 'Found % diagnosis codes from parent NCPDP claim', array_length(v_result, 1);
            
            -- Log success
            PERFORM log_billing_function(
              'build_mm_dx_loop'::tracked_function,
              v_params,
              NULL::jsonb,
              NULL::text,
              clock_timestamp() - v_start_time
            );
            
            RETURN v_result;
        END IF;

        -- If not found in NCPDP claim, try CMS-1500 claim using proper subform joining
        WITH parent_1500_dx AS (
            SELECT DISTINCT
                mc.patient_id::integer as patient_id,
                dx.dx_id::integer as dx_id,
                dx.diagnosis_code::text as diagnosis_code,
                dx.id::integer as id,
                ROW_NUMBER() OVER (ORDER BY dx.id) as row_num
            FROM form_med_claim_1500 mc
            INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_dx sf_dx 
                ON sf_dx.form_med_claim_1500_fk = mc.id
                AND sf_dx.delete IS NOT TRUE 
                AND sf_dx.archive IS NOT TRUE
            INNER JOIN form_med_claim_1500_dx dx 
                ON dx.id = sf_dx.form_med_claim_1500_dx_fk
                AND dx.deleted IS NOT TRUE
                AND dx.archived IS NOT TRUE
            WHERE mc.claim_no = p_parent_claim_no
              AND mc.deleted IS NOT TRUE 
              AND mc.archived IS NOT TRUE
              AND dx.diagnosis_code IS NOT NULL
        )
        SELECT array_agg(
            (
                p1500d.patient_id,
                p1500d.dx_id,
                CASE 
                    WHEN p1500d.row_num = 1 THEN 'ABK'  -- Primary diagnosis (ICD-10-CM Principal)
                    ELSE 'ABF'                         -- Secondary diagnosis (ICD-10-CM)
                END::text,
                p1500d.diagnosis_code
            )::mm_dx_info
            ORDER BY p1500d.row_num
        )
        INTO v_result
        FROM parent_1500_dx p1500d;

        -- If found in CMS-1500 claim, return it
        IF v_result IS NOT NULL AND array_length(v_result, 1) > 0 THEN
            RAISE LOG 'Found % diagnosis codes from parent CMS-1500 claim', array_length(v_result, 1);
            
            -- Log success
            PERFORM log_billing_function(
              'build_mm_dx_loop'::tracked_function,
              v_params,
              NULL::jsonb,
              NULL::text,
              clock_timestamp() - v_start_time
            );
            
            RETURN v_result;
        END IF;
    END IF;

    -- If no parent claim or no diagnosis found in parent, extract from charge lines
    IF p_charge_lines IS NULL OR array_length(p_charge_lines, 1) = 0 THEN
        RAISE LOG 'No charge lines provided, returning empty diagnosis loop';
        RETURN NULL::mm_dx_info[];
    END IF;

    -- Get patient_id from first charge line
    SELECT cl.patient_id::integer
    INTO v_patient_id
    FROM unnest(p_charge_lines) cl
    WHERE cl.patient_id IS NOT NULL
    LIMIT 1;

    IF v_patient_id IS NULL THEN
        RAISE EXCEPTION 'Patient ID not found in charge lines';
    END IF;

    -- Extract diagnosis codes from charge lines via vw_order_raw
    WITH unique_rx_nos AS (
        SELECT DISTINCT cl.rx_no
        FROM unnest(p_charge_lines) cl
        WHERE cl.rx_no IS NOT NULL
    ),
    diagnosis_raw AS (
        SELECT 
            vr.rx_no,
            dx_elem.value as dx_element
        FROM unique_rx_nos urn
        INNER JOIN vw_order_raw vr ON vr.rx_no = urn.rx_no
        CROSS JOIN LATERAL jsonb_array_elements(
            CASE 
                WHEN vr.dx_ids IS NOT NULL AND vr.dx_ids != '' THEN vr.dx_ids::jsonb
                ELSE '[]'::jsonb
            END
        ) AS dx_elem(value)
        WHERE vr.dx_ids IS NOT NULL AND vr.dx_ids != ''
    ),
    diagnosis_parsed AS (
        SELECT 
            (dx_element->>'id')::integer as patient_dx_id,
            dx_element->>'dx_id' as dx_code,
            COALESCE((dx_element->>'rank')::integer, 999) as rank_order,
            COALESCE((dx_element->>'sort_order')::integer, 999) as sort_order
        FROM diagnosis_raw
        WHERE dx_element->>'dx_id' IS NOT NULL
          AND dx_element->>'id' IS NOT NULL
    ),
    unique_diagnosis AS (
        SELECT DISTINCT 
            patient_dx_id,
            dx_code,
            rank_order,
            sort_order
        FROM diagnosis_parsed
    ),
    ranked_diagnosis AS (
        SELECT 
            patient_dx_id,
            dx_code,
            rank_order,
            sort_order,
            ROW_NUMBER() OVER (ORDER BY rank_order, sort_order, patient_dx_id) as row_num
        FROM unique_diagnosis
    )
    SELECT array_agg(
        (
            v_patient_id::integer,
            rd.patient_dx_id::integer,
            CASE 
                WHEN rd.row_num = 1 THEN 'ABK'  -- Primary diagnosis (ICD-10-CM Principal)
                ELSE 'ABF'                      -- Secondary diagnosis (ICD-10-CM)
            END::text,
            rd.dx_code::text
        )::mm_dx_info
        ORDER BY rd.row_num
    )
    INTO v_result
    FROM ranked_diagnosis rd
    WHERE rd.row_num <= 12  -- Maximum of 12 diagnoses allowed
    AND rd.dx_code IS NOT NULL;

    -- Return result (could be NULL if no diagnoses found)
    v_result := COALESCE(v_result, ARRAY[]::mm_dx_info[]);

    RAISE LOG 'MM claim diagnosis loop build result: % diagnosis codes found', 
              COALESCE(array_length(v_result, 1), 0);

    -- Log success
    PERFORM log_billing_function(
      'build_mm_dx_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_claim_dx_loop',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'build_mm_claim_dx_loop',
        'parent_claim_no', p_parent_claim_no
      )
    );

    PERFORM log_billing_function(
      'build_mm_dx_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION build_mm_service_facility_segment(
  p_charge_lines charge_line_with_split[],
  p_parent_claim_no text DEFAULT NULL
) RETURNS mm_service_facility_info AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_service_facility_info;
  v_error_message text;
  v_params jsonb;
  v_infusion_suite_id integer;
  v_address_info mm_address_info;
BEGIN

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build minimal parameters JSON for logging
  v_params := jsonb_build_object(
    'parent_claim_no', p_parent_claim_no,
    'charge_lines_count', array_length(p_charge_lines, 1)
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
      'build_mm_service_facility_segment'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM service facility segment for parent claim no: %', p_parent_claim_no;

    -- If parent claim is provided, get service facility from parent claim
    IF p_parent_claim_no IS NOT NULL THEN
        RAISE LOG 'Looking for service facility from parent claim: %', p_parent_claim_no;
        
        -- Get service facility from parent medical claim
        WITH parent_facility AS (
            SELECT 
                mcf.infusion_suite_id::integer as infusion_suite_id,
                mcf.organization_name::text as organization_name,
                mcf.npi::text as npi,
                mcf.phone_name::text as phone_name,
                mcf.phone_number::text as phone_number,
                addr.address1::text as address1,
                addr.address2::text as address2,
                addr.city::text as city,
                addr.state::text as state,
                addr.postal_code::text as postal_code
            FROM form_med_claim mc
            INNER JOIN sf_form_med_claim_to_med_claim_info sf_info ON sf_info.form_med_claim_fk = mc.id
            INNER JOIN form_med_claim_info ci ON ci.id = sf_info.form_med_claim_info_fk
            INNER JOIN sf_form_med_claim_info_to_med_claim_facility sf_fac ON sf_fac.form_med_claim_info_fk = ci.id
            INNER JOIN form_med_claim_facility mcf ON mcf.id = sf_fac.form_med_claim_facility_fk
            LEFT JOIN LATERAL (
                SELECT 
                    addr.address1::text as address1,
                    addr.address2::text as address2,
                    addr.city::text as city,
                    addr.state::text as state,
                    addr.postal_code::text as postal_code
                FROM sf_form_med_claim_facility_to_med_claim_address_fac sf_addr
                INNER JOIN form_med_claim_address_fac addr ON addr.id = sf_addr.form_med_claim_address_fac_fk
                WHERE sf_addr.form_med_claim_facility_fk = mcf.id
                  AND sf_addr.delete IS NOT TRUE 
                  AND sf_addr.archive IS NOT TRUE
                  AND addr.deleted IS NOT TRUE 
                  AND addr.archived IS NOT TRUE
                ORDER BY addr.created_on DESC
                LIMIT 1
            ) addr ON true
            WHERE mc.claim_no = p_parent_claim_no
              AND mc.deleted IS NOT TRUE 
              AND mc.archived IS NOT TRUE
              AND sf_info.delete IS NOT TRUE 
              AND sf_info.archive IS NOT TRUE
              AND ci.deleted IS NOT TRUE 
              AND ci.archived IS NOT TRUE
              AND sf_fac.delete IS NOT TRUE 
              AND sf_fac.archive IS NOT TRUE
              AND mcf.deleted IS NOT TRUE 
              AND mcf.archived IS NOT TRUE
        )
        SELECT 
            pf.infusion_suite_id,
            pf.organization_name,
            pf.npi,
            pf.phone_name,
            pf.phone_number,
            pf.address1,
            pf.address2,
            pf.city,
            pf.state,
            pf.postal_code
        INTO 
            v_result.infusion_suite_id,
            v_result.organization_name,
            v_result.npi,
            v_result.phone_name,
            v_result.phone_number,
            v_address_info.address1,
            v_address_info.address2,
            v_address_info.city,
            v_address_info.state,
            v_address_info.postal_code
        FROM parent_facility pf
        LIMIT 1;

        -- If found in parent claim, return it
        IF v_result.infusion_suite_id IS NOT NULL THEN
            RAISE LOG 'Found service facility from parent claim: % (%)', v_result.organization_name, v_result.npi;
            
            -- Build address array
            v_result.address := ARRAY[v_address_info];
            
            -- Log success
            PERFORM log_billing_function(
              'build_mm_service_facility_segment'::tracked_function,
              v_params::jsonb,
              NULL::jsonb,
              NULL::text,
              clock_timestamp() - v_start_time
            );
            
            RETURN v_result;
        END IF;
        
        -- If not found in medical claim, try CMS-1500 claim
        WITH parent_1500_facility AS (
            SELECT 
                mcf.infusion_suite_id::integer as infusion_suite_id,
                mcf.organization_name::text as organization_name,
                mcf.npi::text as npi,
                NULL::text as phone_name,    -- med_claim_1500_sf doesn't have phone fields
                NULL::text as phone_number,  -- med_claim_1500_sf doesn't have phone fields
                -- Get address fields directly from med_claim_1500_sf
                mcf.address1::text as address1,
                mcf.address2::text as address2,
                mcf.city::text as city,
                mcf.state_id::text as state,  -- Note: this table uses state_id
                mcf.postal_code::text as postal_code
            FROM form_med_claim_1500 mc1500
            INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_sf sf_fac ON sf_fac.form_med_claim_1500_fk = mc1500.id
            INNER JOIN form_med_claim_1500_sf mcf ON mcf.id = sf_fac.form_med_claim_1500_sf_fk
            WHERE mc1500.claim_no = p_parent_claim_no
              AND mc1500.deleted IS NOT TRUE 
              AND mc1500.archived IS NOT TRUE
              AND sf_fac.delete IS NOT TRUE 
              AND sf_fac.archive IS NOT TRUE
              AND mcf.deleted IS NOT TRUE 
              AND mcf.archived IS NOT TRUE
        )
        SELECT 
            pf.infusion_suite_id,
            pf.organization_name,
            pf.npi,
            pf.phone_name,
            pf.phone_number,
            pf.address1,
            pf.address2,
            pf.city,
            pf.state,
            pf.postal_code
        INTO 
            v_result.infusion_suite_id,
            v_result.organization_name,
            v_result.npi,
            v_result.phone_name,
            v_result.phone_number,
            v_address_info.address1,
            v_address_info.address2,
            v_address_info.city,
            v_address_info.state,
            v_address_info.postal_code
        FROM parent_1500_facility pf
        LIMIT 1;

        -- If found in CMS-1500 claim, return it
        IF v_result.infusion_suite_id IS NOT NULL THEN
            RAISE LOG 'Found service facility from parent CMS-1500 claim: % (%)', v_result.organization_name, v_result.npi;
            
            -- Build address array
            v_result.address := ARRAY[v_address_info];
            
            -- Log success
            PERFORM log_billing_function(
              'build_mm_service_facility_segment'::tracked_function,
              v_params::jsonb,
              NULL::jsonb,
              NULL::text,
              clock_timestamp() - v_start_time
            );
            
            RETURN v_result;
        END IF;
        
        RAISE LOG 'No service facility found in parent claims, falling back to charge line lookup';
    END IF;

    -- Fallback: Get infusion suite ID from the first charge line that has one
    WITH infusion_suite_lookup AS (
        SELECT DISTINCT vr.infusion_suite_id
        FROM unnest(p_charge_lines) cl
        INNER JOIN vw_order_raw vr ON vr.rx_no = cl.rx_no
        WHERE vr.infusion_suite_id IS NOT NULL
        LIMIT 1
    )
    SELECT infusion_suite_id 
    INTO v_infusion_suite_id
    FROM infusion_suite_lookup;

    -- If no infusion suite found, return null
    IF v_infusion_suite_id IS NULL THEN
        RAISE LOG 'No infusion suite found in charge lines, returning null';
        RETURN NULL;
    END IF;

    -- Get infusion suite details and address
    SELECT 
        isuite.id::integer as infusion_suite_id,
        isuite.npi::text as npi,
        isuite.organization_name::text as organization_name,
        isuite.phone_name::text as phone_name,
        isuite.phone_number::text as phone_number,
        addr.address1::text as address1,
        addr.address2::text as address2,
        addr.city::text as city,
        addr.state::text as state,
        addr.postal_code::text as postal_code
    INTO 
        v_result.infusion_suite_id,
        v_result.npi,
        v_result.organization_name,
        v_result.phone_name,
        v_result.phone_number,
        v_address_info.address1,
        v_address_info.address2,
        v_address_info.city,
        v_address_info.state,
        v_address_info.postal_code
    FROM form_infusion_suite isuite
    LEFT JOIN LATERAL (
        SELECT 
            addr.address1::text as address1,
            addr.address2::text as address2,
            addr.city::text as city,
            addr.state::text as state,
            addr.postal_code::text as postal_code
        FROM sf_form_infusion_suite_to_infusion_suite_address sf
        INNER JOIN form_infusion_suite_address addr ON addr.id = sf.form_infusion_suite_address_fk
        WHERE sf.form_infusion_suite_fk = isuite.id
          AND sf.delete IS NOT TRUE 
          AND sf.archive IS NOT TRUE
          AND addr.deleted IS NOT TRUE 
          AND addr.archived IS NOT TRUE
        ORDER BY addr.created_on DESC
        LIMIT 1
    ) addr ON true
    WHERE isuite.id = v_infusion_suite_id
      AND isuite.deleted IS NOT TRUE 
      AND isuite.archived IS NOT TRUE;

    -- Validate we found the infusion suite
    IF v_result.infusion_suite_id IS NULL THEN
        RAISE EXCEPTION 'Unable to locate service facility with ID %', v_infusion_suite_id;
    END IF;

    -- Build address array
    v_result.address := ARRAY[v_address_info];

    RAISE LOG 'MM service facility segment build result: % (% - %)', 
              v_result.infusion_suite_id, v_result.organization_name, v_result.npi;

    -- Log success
    PERFORM log_billing_function(
      'build_mm_service_facility_segment'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_service_facility_segment',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'build_mm_service_facility_segment'
      )
    );

    PERFORM log_billing_function(
      'build_mm_service_facility_segment'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION build_mm_service_lines_loop(
  p_insurance_id integer,
  p_payer_id integer,
  p_charge_lines charge_line_with_split[],
  p_diagnosis_codes mm_dx_info[] DEFAULT NULL,
  p_parent_claim_no text DEFAULT NULL
) RETURNS mm_service_line_info[] AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_service_line_info[];
  v_error_message text;
  v_params jsonb;
  v_is_cob boolean := FALSE;
  v_patient_id integer;
  v_site_id integer;
  v_send_sales_tax boolean := FALSE;
  v_send_sos_only boolean := FALSE;
  v_service_line mm_service_line_info;
  v_charge_line charge_line_with_split;
  v_line_number integer := 0;
  v_inventory_rec mm_inventory_info;
  v_service_date_from text;
  v_service_date_to text;
BEGIN

  -- Record start time
  v_start_time := clock_timestamp();

  IF p_insurance_id IS NULL THEN
    RAISE EXCEPTION 'Insurance ID cannot be NULL';
  END IF;

  IF p_payer_id IS NULL THEN
    RAISE EXCEPTION 'Payer ID cannot be NULL';
  END IF;

  IF p_charge_lines IS NULL OR array_length(p_charge_lines, 1) = 0 THEN
    RAISE EXCEPTION 'Charge lines cannot be NULL or empty';
  END IF;

  -- Check maximum service lines constraint
  IF array_length(p_charge_lines, 1) > 50 THEN
    RAISE EXCEPTION 'Cannot have more than 50 service lines, found %', array_length(p_charge_lines, 1);
  END IF;

  v_params := jsonb_build_object(
    'insurance_id', p_insurance_id,
    'payer_id', p_payer_id,
    'diagnosis_count', COALESCE(array_length(p_diagnosis_codes, 1), 0),
    'parent_claim_no', p_parent_claim_no
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
      'build_mm_service_lines_loop'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM service lines loop for insurance ID: %, payer ID: %', p_insurance_id, p_payer_id;

    -- Determine if this is a COB claim
    v_is_cob := (p_parent_claim_no IS NOT NULL);

    -- Get patient_id and site_id from first charge line
    SELECT cl.patient_id::integer, cl.site_id::integer
    INTO v_patient_id, v_site_id
    FROM unnest(p_charge_lines) cl
    WHERE cl.patient_id IS NOT NULL AND cl.site_id IS NOT NULL
    LIMIT 1;

    IF v_patient_id IS NULL OR v_site_id IS NULL THEN
        RAISE EXCEPTION 'Patient ID and Site ID are required in charge lines';
    END IF;

    -- Check if payer calculates sales tax
    SELECT COALESCE(payer.mm_calc_perc_sales_tax::text, 'No') = 'Yes',
    COALESCE(payer.mm_sos_only::text, 'No') = 'Yes'
    INTO v_send_sales_tax, v_send_sos_only
    FROM form_payer payer
    WHERE payer.id = p_payer_id
      AND payer.deleted IS NOT TRUE 
      AND payer.archived IS NOT TRUE;

    -- Initialize result array
    v_result := ARRAY[]::mm_service_line_info[];

    -- Build service lines from charge lines
    FOR v_charge_line IN SELECT * FROM unnest(p_charge_lines) LOOP
        v_line_number := v_line_number + 1;

        RAISE LOG 'Processing charge line - charge_no=%, inventory_type=%, upin=%', 
            v_charge_line.charge_no, v_charge_line.inventory_type, v_charge_line.upin;

        -- Build inventory record from charge line data (map inventory_type to type field)
        v_inventory_rec.id := v_charge_line.inventory_id::integer;
        v_inventory_rec.description := v_charge_line.description::text;
        v_inventory_rec.ndc := v_charge_line.ndc::text;
        v_inventory_rec.type := v_charge_line.inventory_type::text;
        v_inventory_rec.rental_type := v_charge_line.rental_type::text;
        v_inventory_rec.frequency_code := v_charge_line.frequency_code::text;
        v_inventory_rec.upin := v_charge_line.upin::text;

        RAISE LOG 'Built inventory record - id: %, type: %, ndc: %, description: %, upin: %', 
            v_inventory_rec.id, v_inventory_rec.type, v_inventory_rec.ndc, v_inventory_rec.description, v_inventory_rec.upin;

        -- Build all service line segments FIRST
        DECLARE
            v_sl_reference_info mm_service_line_reference_info[];
            v_professional_service mm_professional_service_info[];
            v_drug_identification mm_drug_identification_info[];
            v_dme_service mm_dme_info[];
            v_dme_cmn mm_dme_cmn_info[];
            v_dme_cert mm_dme_cert_info[];
            v_dme_condition mm_dme_condition_info[];
            v_sl_dates mm_service_line_date_info[];
            v_sl_supplemental mm_sl_supplemental_info[];
            v_file_info mm_file_info[];
            v_cob_adjustments mm_sl_adjustment_info[];
            v_payer_submit_ndc boolean := FALSE;
        BEGIN
            -- Build service line reference information
            v_sl_reference_info := build_mm_sl_reference_info(v_charge_line, p_insurance_id);

            -- Build professional service segment
            WITH professional_service_result AS (
                SELECT build_mm_sl_professional_service(v_charge_line, v_inventory_rec, p_diagnosis_codes) as prof_service
            )
            SELECT CASE WHEN psr.prof_service IS NOT NULL THEN ARRAY[psr.prof_service] ELSE NULL::mm_professional_service_info[] END
            INTO v_professional_service
            FROM professional_service_result psr;

            -- Build drug identification segment (for drug inventory types)
            IF v_inventory_rec.type = 'Drug' THEN
                SELECT COALESCE(payer.mm_submit_ndc_rx_info::text, 'No') = 'Yes'
                INTO v_payer_submit_ndc
                FROM form_payer payer
                WHERE payer.id = p_payer_id
                  AND payer.deleted IS NOT TRUE 
                  AND payer.archived IS NOT TRUE;

                WITH drug_id_result AS (
                    SELECT build_mm_sl_drug_identification(v_charge_line, v_inventory_rec, v_line_number, v_payer_submit_ndc) as drug_id
                )
                SELECT CASE WHEN dir.drug_id IS NOT NULL THEN ARRAY[dir.drug_id] ELSE NULL::mm_drug_identification_info[] END
                INTO v_drug_identification
                FROM drug_id_result dir;
            ELSE
                v_drug_identification := NULL::mm_drug_identification_info[];
            END IF;

            -- Build DME segments (for equipment rental inventory types)
            v_dme_cmn := NULL::mm_dme_cmn_info[];
            v_dme_condition := NULL::mm_dme_condition_info[];
            IF v_inventory_rec.type = 'Equipment Rental' THEN
                WITH dme_service_result AS (
                    SELECT build_mm_sl_dme_service(v_charge_line, v_inventory_rec) as dme_service
                )
                SELECT CASE WHEN dsr.dme_service IS NOT NULL THEN ARRAY[dsr.dme_service] ELSE NULL::mm_dme_info[] END
                INTO v_dme_service
                FROM dme_service_result dsr;
                
                -- Build DME certification
                v_dme_cert := build_mm_sl_dme_certification(v_charge_line);
            ELSE
                v_dme_service := NULL::mm_dme_info[];
                v_dme_cert := NULL::mm_dme_cert_info[];
            END IF;

            -- Build service line dates
            WITH dates_result AS (
                SELECT build_mm_sl_dates(v_charge_line) as dates_info
            )
            SELECT CASE WHEN dr.dates_info IS NOT NULL THEN ARRAY[dr.dates_info] ELSE NULL::mm_service_line_date_info[] END
            INTO v_sl_dates
            FROM dates_result dr;

            -- Build service line supplemental information
            v_sl_supplemental := build_mm_sl_supplemental(v_charge_line, p_insurance_id);

            -- Build file information from documents attached to this prescription
            DECLARE
                v_document_rec record;
                v_file_path_json jsonb;
                v_filename text;
                v_file_info_item mm_file_info;
                v_already_sent boolean := FALSE;
            BEGIN
                v_file_info := '{}'; -- Initialize as empty array
                
                -- Look for document records attached to this prescription for medical claims
                FOR v_document_rec IN
                    SELECT 
                        d.id::integer as id,
                        d.file_path::text as file_path,
                        d.send_on_med_claim_order::text as send_on_med_claim_order
                    FROM form_document d
                    INNER JOIN form_careplan_order_rx rx
                      ON rx.rx_no = v_charge_line.rx_no
                      AND rx.deleted IS NOT TRUE
                      AND rx.archived IS NOT TRUE
                    WHERE d.form_filter = 'careplan_order_rx'
                      AND d.form_code = rx.code::text
                      AND COALESCE(d.send_on_med_claim_order, 'No') = 'Yes'
                      AND d.deleted IS NOT TRUE 
                      AND d.archived IS NOT TRUE
                LOOP
                    -- Check if this document has already been sent to the current insurance
                    -- using the gerund table gr_form_document_sent_insr_id_to_patient_insurance_id
                    SELECT EXISTS(
                        SELECT 1 
                        FROM gr_form_document_sent_insr_id_to_patient_insurance_id gr
                        WHERE gr.form_document_fk = v_document_rec.id::integer
                          AND gr.form_patient_insurance_fk = p_insurance_id
                    )::boolean INTO v_already_sent;

                    -- Only include if not already sent to this insurance
                    IF NOT v_already_sent THEN
                        -- Extract filename from file_path JSON if it exists
                        v_filename := NULL;
                        IF v_document_rec.file_path IS NOT NULL THEN
                            BEGIN
                                v_file_path_json := v_document_rec.file_path::jsonb;
                                v_filename := v_file_path_json->>'filename';
                            EXCEPTION WHEN OTHERS THEN
                                v_filename := NULL;
                            END;
                        END IF;

                        -- Build file info item
                        SELECT 
                            COALESCE(v_filename, 'Document_' || v_document_rec.id::text)::text,
                            ('Attached document for prescription ' || v_charge_line.rx_no::text)::text
                        INTO 
                            v_file_info_item.file,
                            v_file_info_item.comments;

                        -- Add to file info array
                        v_file_info := v_file_info || ARRAY[v_file_info_item];
                    END IF;
                END LOOP;
                
                -- Set to NULL if no files found
                IF array_length(v_file_info, 1) = 0 THEN
                    v_file_info := NULL::mm_file_info[];
                END IF;
            END;

            -- Build COB adjustments if this is a COB claim and we had service lines adjustments on the parent claim(s)

            IF v_is_cob THEN
                WITH cob_adjustments_result AS (
                    SELECT build_mm_sl_cob_adjustments(v_charge_line, p_parent_claim_no, p_payer_id) as cob_adjustments
                )
                SELECT CASE WHEN car.cob_adjustments IS NOT NULL THEN car.cob_adjustments ELSE NULL::mm_sl_adjustment_info[] END
                INTO v_cob_adjustments
                FROM cob_adjustments_result car;
            ELSE
                v_cob_adjustments := NULL::mm_sl_adjustment_info[];
            END IF;

            -- Now build the complete service line structure
            -- Get service dates from delivery ticket or encounter
            v_service_date_from := NULL;
            v_service_date_to := NULL;
            
            -- Try to get service dates from delivery ticket first
            IF v_charge_line.ticket_no IS NOT NULL THEN
                SELECT 
                    TO_CHAR(dt.service_from, 'MM/DD/YYYY'),
                    TO_CHAR(dt.service_to, 'MM/DD/YYYY')
                INTO v_service_date_from, v_service_date_to
                FROM form_careplan_delivery_tick dt
                WHERE dt.ticket_no = v_charge_line.ticket_no
                  AND dt.deleted IS NOT TRUE 
                  AND dt.archived IS NOT TRUE
                  AND COALESCE(dt.void, 'No') <> 'Yes';
            END IF;

                -- If no delivery ticket dates, try encounter
                IF v_service_date_from IS NULL AND v_charge_line.encounter_id IS NOT NULL THEN
                    SELECT TO_CHAR(enc.contact_date, 'MM/DD/YYYY')
                    INTO v_service_date_from
                    FROM form_encounter enc
                    WHERE enc.id = v_charge_line.encounter_id
                      AND enc.deleted IS NOT TRUE 
                      AND enc.archived IS NOT TRUE;

                    v_service_date_to := v_service_date_from; -- Same date for encounters
                END IF;

            -- Fall back to charge line dates if no other source
            IF v_service_date_from IS NULL THEN
                v_service_date_from := TO_CHAR(v_charge_line.date_of_service, 'MM/DD/YYYY');
                v_service_date_to := TO_CHAR(v_charge_line.date_of_service_end, 'MM/DD/YYYY');
            END IF;

            v_service_line.claim_no := NULL::text;
            v_service_line.charge_no := v_charge_line.charge_no::text;
            v_service_line.lock_sv := CASE 
                WHEN p_parent_claim_no IS NOT NULL THEN 'Yes'::text
                ELSE NULL::text
            END;
            v_service_line.patient_id := v_patient_id::integer;
            v_service_line.site_id := v_site_id::integer;
            v_service_line.payer_id := p_payer_id::integer;
            v_service_line.mm_calc_perc_sales_tax := CASE WHEN v_send_sales_tax THEN 'Yes' ELSE NULL END::text;
            v_service_line.inventory_id := v_charge_line.inventory_id::integer;
            v_service_line.measurement_unit := 'UN'::text; -- DefaultMeasurementUnits
            v_service_line.service_unit_count := v_charge_line.charge_quantity::numeric;
            v_service_line.dx_id_1 := CASE
                WHEN p_diagnosis_codes IS NOT NULL AND array_length(p_diagnosis_codes, 1) > 0 
                THEN (p_diagnosis_codes[1]).dx_id
                ELSE NULL
            END::integer;
            v_service_line.modifier_1 := v_charge_line.modifier_1::text;
            v_service_line.line_item_charge_amount := v_charge_line.billed::numeric;
            v_service_line.assigned_number := v_line_number::integer;
            v_service_line.provider_control_number := v_charge_line.master_charge_no::text;
            v_service_line.service_date := v_service_date_from::text;
            v_service_line.service_date_end := CASE WHEN v_send_sos_only THEN NULL::text ELSE v_service_date_to::text END;
            v_service_line.service_line_reference_information := v_sl_reference_info;
            v_service_line.professional_service := v_professional_service;
            v_service_line.drug_identification := v_drug_identification;
            v_service_line.durable_medical_equipment_service := v_dme_service;
            v_service_line.durable_medical_equipment_certificate_of_medical_necessity := v_dme_cmn;
            v_service_line.durable_medical_equipment_certification := v_dme_cert;
            v_service_line.condition_indicator_durable_medical_equipment := v_dme_condition;
            v_service_line.service_line_date_information := v_sl_dates;
            v_service_line.additional_notes := NULL::text;
            v_service_line.goal_rehab_or_discharge_plans := NULL::text;
            v_service_line.third_party_organization_notes := NULL::text;
            v_service_line.line_pricing_repricing_information := NULL::mm_sl_repricing_info[];
            v_service_line.line_adjudication_information := v_cob_adjustments;
            v_service_line.service_line_supplemental_information := v_sl_supplemental;
            v_service_line.file_information := v_file_info;
        END;

        -- Add service line to result array
        v_result := v_result || ARRAY[v_service_line];
    END LOOP;

    RAISE LOG 'MM service lines loop build result: % service lines built', array_length(v_result, 1);

    -- Log success
    PERFORM log_billing_function(
      'build_mm_service_lines_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_service_lines_loop',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'build_mm_service_lines_loop',
        'insurance_id', p_insurance_id,
        'payer_id', p_payer_id,
        'parent_claim_no', p_parent_claim_no
      )
    );

    PERFORM log_billing_function(
      'build_mm_service_lines_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION build_mm_supplemental_segment(
  p_patient_id integer,
  p_insurance_id integer,
  p_payer_id integer,
  p_charge_lines charge_line_with_split[],
  p_claim_number text DEFAULT 'CLAIM_NO_PLACEHOLDER',
  p_parent_claim_no text DEFAULT NULL
) RETURNS mm_supplemental_info AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_supplemental_info;
  v_error_message text;
  v_params jsonb;
  v_medical_record_number text;
  v_pa_id integer;
  v_prior_auth_number text;
BEGIN

  -- Record start time
  v_start_time := clock_timestamp();

  IF p_patient_id IS NULL THEN
    RAISE EXCEPTION 'Patient ID cannot be NULL';
  END IF;

  IF p_insurance_id IS NULL THEN
    RAISE EXCEPTION 'Insurance ID cannot be NULL';
  END IF;

  IF p_payer_id IS NULL THEN
    RAISE EXCEPTION 'Payer ID cannot be NULL';
  END IF;

  v_params := jsonb_build_object(
    'patient_id', p_patient_id,
    'insurance_id', p_insurance_id,
    'payer_id', p_payer_id,
    'claim_number', p_claim_number,
    'parent_claim_no', p_parent_claim_no
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
      'build_mm_supplemental_segment'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM supplemental segment for patient ID: %, insurance ID: %, payer ID: %, parent_claim_no: %', p_patient_id, p_insurance_id, p_payer_id, p_parent_claim_no;

    -- Get patient medical record number
    SELECT pt.mrn::text
    INTO v_medical_record_number
    FROM form_patient pt
    WHERE pt.id = p_patient_id
      AND pt.deleted IS NOT TRUE 
      AND pt.archived IS NOT TRUE;

    -- If parent claim is provided, try to get PA info from parent claim
    IF p_parent_claim_no IS NOT NULL THEN
        -- Check if parent is an NCPDP claim
        IF EXISTS(SELECT 1 FROM form_ncpdp WHERE claim_no = p_parent_claim_no AND deleted IS NOT TRUE AND archived IS NOT TRUE) THEN
            SELECT ncl.pa_id::integer, ncl.pa_no_submitted::text
            INTO v_pa_id, v_prior_auth_number
            FROM form_ncpdp nc
            INNER JOIN sf_form_ncpdp_to_ncpdp_claim sf 
                ON sf.form_ncpdp_fk = nc.id 
                AND sf.delete IS NOT TRUE 
                AND sf.archive IS NOT TRUE
            INNER JOIN form_ncpdp_claim ncl 
                ON ncl.id = sf.form_ncpdp_claim_fk
                AND ncl.deleted IS NOT TRUE
                AND ncl.archived IS NOT TRUE
            WHERE nc.claim_no = p_parent_claim_no
              AND nc.deleted IS NOT TRUE 
              AND nc.archived IS NOT TRUE
              AND (ncl.pa_id IS NOT NULL OR ncl.pa_no_submitted IS NOT NULL)
            LIMIT 1;
              
            IF v_pa_id IS NOT NULL OR v_prior_auth_number IS NOT NULL THEN
                RAISE LOG 'Found PA info from parent NCPDP claim: pa_id=%, pa_no=%', v_pa_id, v_prior_auth_number;
            END IF;
            
        -- Check if parent is a medical claim
        ELSIF EXISTS(SELECT 1 FROM form_med_claim WHERE claim_no = p_parent_claim_no AND deleted IS NOT TRUE AND archived IS NOT TRUE) THEN
            SELECT mcs.pa_id::integer, mcs.prior_authorization_number::text
            INTO v_pa_id, v_prior_auth_number
            FROM form_med_claim mc
            INNER JOIN sf_form_med_claim_to_med_claim_info sf_mci
                ON sf_mci.form_med_claim_fk = mc.id 
                AND sf_mci.delete IS NOT TRUE 
                AND sf_mci.archive IS NOT TRUE
            INNER JOIN form_med_claim_info mci 
                ON mci.id = sf_mci.form_med_claim_info_fk
                AND mci.deleted IS NOT TRUE
                AND mci.archived IS NOT TRUE
            INNER JOIN sf_form_med_claim_info_to_med_claim_supplemental sf_mcs
                ON sf_mcs.form_med_claim_info_fk = mci.id 
                AND sf_mcs.delete IS NOT TRUE 
                AND sf_mcs.archive IS NOT TRUE
            INNER JOIN form_med_claim_supplemental mcs 
                ON mcs.id = sf_mcs.form_med_claim_supplemental_fk
                AND mcs.deleted IS NOT TRUE
                AND mcs.archived IS NOT TRUE
            WHERE mc.claim_no = p_parent_claim_no
              AND mc.deleted IS NOT TRUE 
              AND mc.archived IS NOT TRUE
              AND (mcs.pa_id IS NOT NULL OR mcs.prior_authorization_number IS NOT NULL)
            LIMIT 1;
              
            IF v_pa_id IS NOT NULL OR v_prior_auth_number IS NOT NULL THEN
                RAISE LOG 'Found PA info from parent medical claim: pa_id=%, pa_no=%', v_pa_id, v_prior_auth_number;
            END IF;
            
        -- Check if parent is a 1500 claim
        ELSIF EXISTS(SELECT 1 FROM form_med_claim_1500 WHERE claim_no = p_parent_claim_no AND deleted IS NOT TRUE AND archived IS NOT TRUE) THEN
            SELECT mc1500.pa_id::integer, mc1500.prior_authorization_number::text
            INTO v_pa_id,v_prior_auth_number
            FROM form_med_claim_1500 mc1500
            WHERE mc1500.claim_no = p_parent_claim_no
              AND mc1500.deleted IS NOT TRUE 
              AND mc1500.archived IS NOT TRUE
              AND mc1500.prior_authorization_number IS NOT NULL;
              
            IF v_prior_auth_number IS NOT NULL THEN
                RAISE LOG 'Found PA info from parent 1500 claim: pa_no=%', v_prior_auth_number;
            END IF;
        END IF;
    END IF;

    -- Build the supplemental segment (prior authorization moved to service line level)
    SELECT
        p_patient_id::integer,
        p_insurance_id::integer,
        p_claim_number::text,
        v_pa_id::integer,
        v_prior_auth_number::text,
        v_medical_record_number::text,
        NULL::mm_med_claim_report_info[]  -- med_claim_report (not implementing attachments yet)
    INTO
        v_result.patient_id,
        v_result.insurance_id,
        v_result.claim_number,
        v_result.pa_id,
        v_result.prior_authorization_number,
        v_result.medical_record_number,
        v_result.med_claim_report;

    RAISE LOG 'MM supplemental segment build result: MRN: %, PA ID: %, PA No: %', v_medical_record_number, v_pa_id, v_prior_auth_number;

    -- Log success
    PERFORM log_billing_function(
      'build_mm_supplemental_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_supplemental_segment',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'build_mm_supplemental_segment',
        'patient_id', p_patient_id,
        'insurance_id', p_insurance_id,
        'payer_id', p_payer_id,
        'parent_claim_no', p_parent_claim_no
      )
    );

    PERFORM log_billing_function(
      'build_mm_supplemental_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION build_mm_claim_info_other_segment(
  p_site_id integer,
  p_patient_id integer,
  p_insurance_id integer,
  p_payer_id integer,
  p_total_billed numeric DEFAULT 0,
  p_parent_claim_no text DEFAULT NULL
) RETURNS mm_other_info AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_other_info;
  v_error_message text;
  v_params jsonb;
  v_send_contract_pricing text;
  v_contract_info mm_contract_info;
  v_payer_contract_rec record;
  v_claim_date_info mm_claim_date_info[];
  v_date_info mm_claim_date_info;
  v_insurance_rules_rec record;
  v_parent_payer_id integer;
BEGIN

  -- Record start time
  v_start_time := clock_timestamp();

  IF p_site_id IS NULL THEN
    RAISE EXCEPTION 'Site ID cannot be NULL';
  END IF;

  IF p_patient_id IS NULL THEN
    RAISE EXCEPTION 'Patient ID cannot be NULL';
  END IF;

  IF p_payer_id IS NULL THEN
    RAISE EXCEPTION 'Payer ID cannot be NULL';
  END IF;

  IF p_insurance_id IS NULL THEN
    RAISE EXCEPTION 'Insurance ID cannot be NULL';
  END IF;

  v_params := jsonb_build_object(
    'site_id', p_site_id,
    'patient_id', p_patient_id,
    'insurance_id', p_insurance_id,
    'payer_id', p_payer_id,
    'total_billed', p_total_billed,
    'parent_claim_no', p_parent_claim_no
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
      'build_mm_claim_info_other_segment'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM claim info other segment for site ID: %, patient ID: %, payer ID: %, parent claim: %', p_site_id, p_patient_id, p_payer_id, p_parent_claim_no;

    -- Get parent payer_id if this is a COB claim
    IF p_parent_claim_no IS NOT NULL THEN
        SELECT pmc.payer_id::integer
        INTO v_parent_payer_id
        FROM form_med_claim pmc
        WHERE pmc.claim_no = p_parent_claim_no
          AND pmc.deleted IS NOT TRUE 
          AND pmc.archived IS NOT TRUE;
    END IF;

    -- Get payer contract sending setting
    SELECT COALESCE(payer.mm_send_contract_pricing::text, 'No')
    INTO v_send_contract_pricing
    FROM form_payer payer
    WHERE payer.id = p_payer_id
      AND payer.deleted IS NOT TRUE 
      AND payer.archived IS NOT TRUE;

    -- Build contract information if payer sends contract pricing
    IF v_send_contract_pricing = 'Yes' THEN
        -- Look for payer contract for this site
        SELECT 
            pc.contract_type_code::text as contract_type_code,
            pc.contract_code::text as contract_code,
            pc.contract_version_identifier::text as contract_version_identifier,
            pc.contract_percentage::numeric as contract_percentage,
            pc.terms_discount_percentage::numeric as terms_discount_percentage
        INTO v_payer_contract_rec
        FROM form_payer_contract pc
        WHERE EXISTS (
        SELECT 1 FROM gr_form_payer_contract_site_id_to_site_id gr_site
        WHERE gr_site.form_payer_contract_fk = pc.id
          AND gr_site.form_site_fk = p_site_id
        )
          AND pc.payer_id = p_payer_id
          AND pc.deleted IS NOT TRUE 
          AND pc.archived IS NOT TRUE
        ORDER BY pc.created_on DESC
        LIMIT 1;

        -- If contract found, build contract information
        IF v_payer_contract_rec.contract_code IS NOT NULL THEN
            SELECT 
                p_total_billed::numeric,
                v_payer_contract_rec.contract_type_code::text,
                v_payer_contract_rec.contract_code::text,
                v_payer_contract_rec.contract_version_identifier::text,
                v_payer_contract_rec.contract_percentage::numeric,
                v_payer_contract_rec.terms_discount_percentage::numeric
            INTO 
                v_contract_info.contract_amount,
                v_contract_info.contract_type_code,
                v_contract_info.contract_code,
                v_contract_info.contract_version_identifier,
                v_contract_info.contract_percentage,
                v_contract_info.terms_discount_percentage;
        ELSE
            RAISE LOG 'Payer % is set to send contract pricing but no contract exists for site %', p_payer_id, p_site_id;
        END IF;
    END IF;

    -- Check for patient insurance medical conditions with symptom_date or accident_date
    -- For COB claims, match against previous_payer_id using parent claim's payer_id
    SELECT 
        to_char(pimc.symptom_date, 'MM/DD/YYYY') as symptom_date,
        to_char(pimc.accident_date, 'MM/DD/YYYY') as accident_date
    INTO v_insurance_rules_rec
    FROM form_patient_insurance_med_cond pimc
    WHERE pimc.patient_id = p_patient_id
      AND (
        -- For primary claims, match active_payer_id
        (v_parent_payer_id IS NULL AND pimc.active_payer_id = p_payer_id)
        OR
        -- For COB claims, match previous_payer_id with parent claim's payer_id
        (v_parent_payer_id IS NOT NULL AND pimc.previous_payer_id = v_parent_payer_id)
      )
      AND pimc.deleted IS NOT TRUE 
      AND pimc.archived IS NOT TRUE
      AND (pimc.symptom_date IS NOT NULL OR pimc.accident_date IS NOT NULL)
    ORDER BY pimc.created_on DESC
    LIMIT 1;

    -- Build claim date information if dates are found
    IF v_insurance_rules_rec.symptom_date IS NOT NULL OR v_insurance_rules_rec.accident_date IS NOT NULL THEN
        SELECT 
            v_insurance_rules_rec.symptom_date::text,
            v_insurance_rules_rec.accident_date::text
        INTO 
            v_date_info.symptom_date,
            v_date_info.accident_date;

        v_claim_date_info := ARRAY[v_date_info];
    ELSE
        v_claim_date_info := NULL::mm_claim_date_info[];
    END IF;

    -- Build the claim info other segment
    SELECT 
        p_site_id::integer as site_id,
        p_patient_id::integer as patient_id,
        p_payer_id::integer as payer_id,
        v_send_contract_pricing::text as mm_send_contract_pricing,
        v_claim_date_info::mm_claim_date_info[],  -- claim_date_information
        CASE 
            WHEN v_claim_date_info IS NOT NULL THEN 'Yes'
            ELSE NULL
        END::text as tabif_claim_date_information,  -- tabif_dates
        CASE 
            WHEN v_contract_info.contract_code IS NOT NULL THEN ARRAY[v_contract_info]
            ELSE NULL::mm_contract_info[]
        END::mm_contract_info[] as claim_contract_information,  -- claim_contract_information
        CASE 
            WHEN v_contract_info.contract_code IS NOT NULL THEN 'Yes'
            ELSE NULL
        END::text as tabif_claim_contract_information,  -- tabif_contract
        NULL::mm_file_info[] as file_information_list  -- file_information_list (not implementing attachments yet)
    INTO 
        v_result.site_id,
        v_result.patient_id,
        v_result.payer_id,
        v_result.mm_send_contract_pricing,
        v_result.claim_date_information,
        v_result.tabif_claim_date_information,
        v_result.claim_contract_information,
        v_result.tabif_claim_contract_information,
        v_result.file_information_list;

    RAISE LOG 'MM claim info other segment build result: Contract Pricing: %, Contract Found: %', 
              v_send_contract_pricing, 
              CASE WHEN v_contract_info.contract_code IS NOT NULL THEN 'Yes' ELSE 'No' END;

    -- Log success
    PERFORM log_billing_function(
      'build_mm_claim_info_other_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_claim_info_other_segment',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'build_mm_claim_info_other_segment',
        'site_id', p_site_id,
        'patient_id', p_patient_id,
        'insurance_id', p_insurance_id,
        'payer_id', p_payer_id,
        'parent_claim_no', p_parent_claim_no,
        'parent_payer_id', v_parent_payer_id
      )
    );

    PERFORM log_billing_function(
      'build_mm_claim_info_other_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;-- Professional Service Line Helper Function

CREATE OR REPLACE FUNCTION build_mm_sl_professional_service(
  p_charge_line charge_line_with_split,
  p_inventory_rec mm_inventory_info,
  p_diagnosis_codes mm_dx_info[] DEFAULT NULL
) RETURNS mm_professional_service_info AS $BODY$
DECLARE
  v_result mm_professional_service_info;
  v_payer_id integer;
  v_default_service_place_id text;
  v_procedure_code text;
  v_procedure_identifier text;
  v_error_message text;
  v_start_time timestamp;
  v_bill_for_denial boolean := FALSE;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  RAISE LOG 'build_mm_sl_professional_service: Starting with rx_no=%, inventory_id=%, payer_id=%', 
    p_charge_line.rx_no, p_inventory_rec.id, p_charge_line.payer_id;

  -- For COB claims, service line information is inherited from parent claim
  -- Get payer information for place of service
  SELECT 
    payer.mm_default_service_place_id::text,
    payer.id::integer,
    COALESCE(payer.always_bill_for_denial, 'No') = 'Yes'
  INTO v_default_service_place_id, v_payer_id, v_bill_for_denial
  FROM form_payer payer
  WHERE payer.id = p_charge_line.payer_id
    AND payer.deleted IS NOT TRUE 
    AND payer.archived IS NOT TRUE;

  IF v_bill_for_denial IS FALSE THEN
    SELECT 
      COALESCE(ins.bill_for_denial, 'No') = 'Yes'
    INTO v_bill_for_denial
    FROM form_patient_insurance ins
    WHERE ins.patient_id = p_charge_line.patient_id
      AND ins.id = p_charge_line.insurance_id
      AND ins.deleted IS NOT TRUE
      AND ins.archived IS NOT TRUE;
  END IF;

  -- Use HCPC code from charge line if available, otherwise use J3490 for drugs and E1399 for equipment rental as the fall through
  SELECT 
    CASE 
      WHEN p_charge_line.hcpc_code IS NOT NULL AND LENGTH(TRIM(p_charge_line.hcpc_code)) > 0 
        THEN p_charge_line.hcpc_code
      WHEN p_charge_line.inventory_type = 'Equipment Rental'
        THEN 'E1399'  -- Non-specified DME code when no HCPC exists for equipment rental
      ELSE 'J3490'  -- Non-specified drug code when no HCPC exists
    END,
    'HC'::text  -- Always HC for HCPC codes
  INTO v_procedure_code, v_procedure_identifier;

  -- Check if this is a supply and payer wants UPIN
  IF p_inventory_rec.type IN ('Supply', 'Supplies') THEN
    DECLARE
      v_send_upin_supplies text;
    BEGIN
      -- Log the inventory record details
      RAISE LOG 'build_mm_sl_professional_service: Checking UPIN for Supply - inventory_id=%, type=%, upin=%', 
        p_inventory_rec.id, p_inventory_rec.type, p_inventory_rec.upin;
        
      -- Check payer setting for UPIN supplies
      SELECT COALESCE(payer.mm_send_upin_supplies::text, 'No')
      INTO v_send_upin_supplies
      FROM form_payer payer
      WHERE payer.id = p_charge_line.payer_id
        AND payer.deleted IS NOT TRUE 
        AND payer.archived IS NOT TRUE;
      
      RAISE LOG 'build_mm_sl_professional_service: Payer % setting mm_send_upin_supplies = %', 
        p_charge_line.payer_id, v_send_upin_supplies;
      
      -- If payer wants UPIN for supplies and UPIN exists, use it
      IF v_send_upin_supplies = 'Yes' AND p_inventory_rec.upin IS NOT NULL AND LENGTH(TRIM(p_inventory_rec.upin)) > 0 THEN
        v_procedure_identifier := 'ER';  -- UPIN identifier
        v_procedure_code := p_inventory_rec.upin;
        RAISE LOG 'build_mm_sl_professional_service: Using UPIN for supply - procedure_code=%, procedure_identifier=%', 
          v_procedure_code, v_procedure_identifier;
      ELSE
        RAISE LOG 'build_mm_sl_professional_service: NOT using UPIN - send_upin_supplies=%, upin=%', 
          v_send_upin_supplies, p_inventory_rec.upin;
      END IF;
    END;
  ELSE
    RAISE LOG 'build_mm_sl_professional_service: Not a supply, type=%, skipping UPIN check', p_inventory_rec.type;
  END IF;

  RAISE LOG 'build_mm_sl_professional_service: Using procedure_code=%, procedure_identifier=% for rx_no=%', 
    v_procedure_code, v_procedure_identifier, p_charge_line.rx_no;

  -- Build the professional service segment
  SELECT 
    p_charge_line.patient_id::integer,
    p_inventory_rec.type::text,
    p_charge_line.charge_no::text,
    p_charge_line.inventory_id::integer,
    COALESCE(p_charge_line.description, p_inventory_rec.description)::text,
    p_charge_line.billed::numeric,
    'UN'::text, -- measurement_unit (DefaultMeasurementUnits)
    p_charge_line.charge_quantity::numeric,
    COALESCE(v_default_service_place_id, '12')::text, -- place_of_service_code
    NULL::text, -- emergency_indicator
    NULL::text, -- epsdt_indicator
    NULL::text, -- copay_status_code
    CASE 
        WHEN p_diagnosis_codes IS NOT NULL AND array_length(p_diagnosis_codes, 1) > 0 
        THEN (SELECT array_agg((dx_info).dx_id) FROM unnest(p_diagnosis_codes) dx_info)
        ELSE NULL::integer[]
    END::integer[], -- dx_filter
    CASE 
        WHEN p_diagnosis_codes IS NOT NULL AND array_length(p_diagnosis_codes, 1) > 0 
        THEN (p_diagnosis_codes[1]).dx_id
        ELSE NULL 
    END::integer, -- dx_id_1
    CASE 
        WHEN p_diagnosis_codes IS NOT NULL AND array_length(p_diagnosis_codes, 1) > 1 
        THEN (p_diagnosis_codes[2]).dx_id
        ELSE NULL 
    END::integer, -- dx_id_2
    CASE 
        WHEN p_diagnosis_codes IS NOT NULL AND array_length(p_diagnosis_codes, 1) > 2 
        THEN (p_diagnosis_codes[3]).dx_id
        ELSE NULL 
    END::integer, -- dx_id_3
    CASE 
        WHEN p_diagnosis_codes IS NOT NULL AND array_length(p_diagnosis_codes, 1) > 3 
        THEN (p_diagnosis_codes[4]).dx_id
        ELSE NULL 
    END::integer, -- dx_id_4
    v_procedure_identifier::text,
    v_procedure_code::text,
    p_charge_line.modifier_1::text,
    p_charge_line.modifier_2::text,
    p_charge_line.modifier_3::text,
    p_charge_line.modifier_4::text
  INTO 
    v_result.patient_id,
    v_result.type,
    v_result.charge_no,
    v_result.inventory_id,
    v_result.description,
    v_result.line_item_charge_amount,
    v_result.measurement_unit,
    v_result.service_unit_count,
    v_result.place_of_service_code,
    v_result.emergency_indicator,
    v_result.epsdt_indicator,
    v_result.copay_status_code,
    v_result.dx_filter,
    v_result.dx_id_1,
    v_result.dx_id_2,
    v_result.dx_id_3,
    v_result.dx_id_4,
    v_result.procedure_identifier,
    v_result.procedure_code,
    v_result.modifier_1,
    v_result.modifier_2,
    v_result.modifier_3,
    v_result.modifier_4;

  IF v_bill_for_denial IS TRUE THEN
    RAISE LOG 'build_mm_sl_professional_service: Bill for denial is TRUE, adding modifier GZ';
    SELECT 
      CASE 
        WHEN v_result.modifier_1 IS NULL THEN 'GZ'::TEXT 
        ELSE v_result.modifier_1 
      END,
      CASE 
        WHEN v_result.modifier_1 IS NOT NULL AND v_result.modifier_2 IS NULL THEN 'GZ'::TEXT 
        ELSE v_result.modifier_2 
      END,
      CASE 
        WHEN v_result.modifier_1 IS NOT NULL AND v_result.modifier_2 IS NOT NULL AND v_result.modifier_3 IS NULL THEN 'GZ'::TEXT 
        ELSE v_result.modifier_3 
      END,
      CASE 
        WHEN v_result.modifier_1 IS NOT NULL AND v_result.modifier_2 IS NOT NULL AND v_result.modifier_3 IS NOT NULL THEN 'GZ'::TEXT 
        ELSE v_result.modifier_4 
      END
    INTO v_result.modifier_1, v_result.modifier_2, v_result.modifier_3, v_result.modifier_4;
  END IF;

  RAISE LOG 'build_mm_sl_professional_service: Built professional service info for rx_no=%, charge_amount=%', 
    p_charge_line.rx_no, v_result.line_item_charge_amount;

  RETURN v_result;

EXCEPTION WHEN OTHERS THEN
  v_error_message := SQLERRM;
  
  RAISE LOG 'build_mm_sl_professional_service: ERROR - % for rx_no=%, inventory_id=%', 
    v_error_message, p_charge_line.rx_no, p_inventory_rec.id;

  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    v_error_message,
    'Exception in build_mm_sl_professional_service',
    'FUNCTION',
    current_schema(),
    'med_claim',
    jsonb_build_object(
      'function_name', 'build_mm_sl_professional_service',
      'rx_no', p_charge_line.rx_no,
      'inventory_id', p_inventory_rec.id,
      'payer_id', p_charge_line.payer_id,
      'execution_time_ms', EXTRACT(EPOCH FROM (clock_timestamp() - v_start_time)) * 1000
    )
  );

  RAISE;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION build_mm_sl_drug_identification(
  p_charge_line charge_line_with_split,
  p_inventory_rec mm_inventory_info,
  p_line_number integer,
  p_payer_submit_ndc boolean DEFAULT FALSE
) RETURNS mm_drug_identification_info AS $BODY$
DECLARE
  v_result mm_drug_identification_info;
  v_error_message text;
  v_start_time timestamp;
  v_billing_unit_code text;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  RAISE LOG 'build_mm_sl_drug_identification: Starting with rx_no=%, inventory_type=%, submit_ndc=%', 
    p_charge_line.rx_no, p_inventory_rec.type, p_payer_submit_ndc;

  -- Only build if payer wants NDC info and this is a drug
  IF NOT p_payer_submit_ndc OR p_inventory_rec.type != 'Drug' THEN
    RAISE LOG 'build_mm_sl_drug_identification: Skipping - submit_ndc=% or type=% (not Drug)', 
      p_payer_submit_ndc, p_inventory_rec.type;
    RETURN NULL;
  END IF;

  SELECT unit.code::text as billing_unit_code
  INTO v_billing_unit_code
  FROM form_list_unit unit
  WHERE unit.code = p_charge_line.billing_unit_id  
    AND unit.deleted IS NOT TRUE 
    AND unit.archived IS NOT TRUE;

  -- Build drug identification segment using individual assignments
  v_result.charge_no := p_charge_line.charge_no::text;
  v_result.inventory_id := p_charge_line.inventory_id::integer;
  v_result.service_id_qualifier := 'N4'::text; -- service_id_qualifier (DefaultNDCQualifierCode)
  v_result.national_drug_code := p_inventory_rec.ndc::text;
  v_result.national_drug_unit_count := p_charge_line.metric_quantity::numeric;
  v_result.measurement_unit_code := CASE
    WHEN p_charge_line.billing_unit_id = 'gram' THEN 'GR'::text
    WHEN p_charge_line.billing_unit_id = 'mL' THEN 'ML'::text
  END::text;
  v_result.link_sequence_number := p_line_number::integer; -- Line number for linked service lines (handled in claim builder)
  v_result.pharmacy_prescription_number := p_charge_line.rx_no::text;

  RAISE LOG 'build_mm_sl_drug_identification: Built drug identification for rx_no=%, ndc=%', 
    p_charge_line.rx_no, v_result.national_drug_code;

  RETURN v_result;

EXCEPTION WHEN OTHERS THEN
  v_error_message := SQLERRM;
  
  RAISE LOG 'build_mm_sl_drug_identification: ERROR - % for rx_no=%, inventory_id=%', 
    v_error_message, p_charge_line.rx_no, p_inventory_rec.id;

  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    v_error_message,
    'Exception in build_mm_sl_drug_identification',
    'FUNCTION',
    current_schema(),
    'med_claim',
    jsonb_build_object(
      'function_name', 'build_mm_sl_drug_identification',
      'rx_no', p_charge_line.rx_no,
      'inventory_id', p_inventory_rec.id,
      'submit_ndc', p_payer_submit_ndc,
      'execution_time_ms', EXTRACT(EPOCH FROM (clock_timestamp() - v_start_time)) * 1000
    )
  );

  RAISE;
END;
$BODY$ LANGUAGE plpgsql VOLATILE; 

-- DME Service Helper Function
CREATE OR REPLACE FUNCTION build_mm_sl_dme_service(
  p_charge_line charge_line_with_split,
  p_inventory_rec mm_inventory_info
) RETURNS mm_dme_info AS $BODY$
DECLARE
  v_result mm_dme_info;
  v_error_message text;
  v_start_time timestamp;
  v_service_from date;
  v_service_to date;
  v_days integer;
  v_purchase_list_price numeric;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  RAISE LOG 'build_mm_sl_dme_service: Starting with rx_no=%, inventory_type=%, rental_type=%', 
    p_charge_line.rx_no, p_inventory_rec.type, p_charge_line.rental_type;

  -- Only build if this is equipment rental
  IF p_inventory_rec.type != 'Equipment Rental' THEN
    RAISE LOG 'build_mm_sl_dme_service: Skipping - not Equipment Rental (type=%)', p_inventory_rec.type;
    RETURN NULL;
  END IF;

  -- Skip if this is a purchase
  IF p_charge_line.rental_type = 'Purchase' THEN
    RAISE LOG 'build_mm_sl_dme_service: Skipping - rental_type is Purchase for rx_no=%', p_charge_line.rx_no;
    RETURN NULL;
  END IF;

  -- Get purchase list price from inventory table
  SELECT inv.purchase_list_price
  INTO v_purchase_list_price
  FROM form_inventory inv
  WHERE inv.id = p_charge_line.inventory_id
    AND inv.deleted IS NOT TRUE
    AND inv.archived IS NOT TRUE;

  -- Determine service_from and service_to dates
  IF p_charge_line.date_of_service IS NOT NULL AND p_charge_line.date_of_service_end IS NOT NULL THEN
    v_service_from := p_charge_line.date_of_service::date;
    v_service_to := p_charge_line.date_of_service_end::date;
  ELSIF p_charge_line.ticket_no IS NOT NULL THEN
    SELECT
      dt.service_from::date,
      dt.service_to::date
    INTO
      v_service_from,
      v_service_to
    FROM form_careplan_delivery_tick dt
    WHERE dt.ticket_no = p_charge_line.ticket_no
      AND dt.deleted IS NOT TRUE
      AND dt.archived IS NOT TRUE
      AND COALESCE(dt.void, 'No') <> 'Yes'
    LIMIT 1;
  END IF;

  -- Calculate days (inclusive)
  IF v_service_from IS NOT NULL AND v_service_to IS NOT NULL THEN
    v_days := (v_service_to - v_service_from) + 1;
    IF v_days < 1 THEN
      v_days := 1;
    END IF;
  ELSE
    -- Fallback to charge_quantity if dates are not available
    v_days := p_charge_line.charge_quantity::integer;
  END IF;

  -- Build DME segment using individual assignments
  v_result.charge_no := p_charge_line.charge_no::text;
  v_result.inventory_id := p_charge_line.inventory_id::integer;
  v_result.days := v_days::integer;
  v_result.frequency_code := p_inventory_rec.frequency_code::text;
  v_result.rental_price := p_charge_line.billed::numeric;
  v_result.purchase_price := v_purchase_list_price::numeric;

  RAISE LOG 'build_mm_sl_dme_service: Built DME service info for rx_no=%, days=%, rental_price=%', 
    p_charge_line.rx_no, v_result.days, v_result.rental_price;

  RETURN v_result;

EXCEPTION WHEN OTHERS THEN
  v_error_message := SQLERRM;
  
  RAISE LOG 'build_mm_sl_dme_service: ERROR - % for rx_no=%, inventory_id=%', 
    v_error_message, p_charge_line.rx_no, p_inventory_rec.id;

  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    v_error_message,
    'Exception in build_mm_sl_dme_service',
    'FUNCTION',
    current_schema(),
    'med_claim',
    jsonb_build_object(
      'function_name', 'build_mm_sl_dme_service',
      'rx_no', p_charge_line.rx_no,
      'inventory_id', p_inventory_rec.id,
      'rental_type', p_charge_line.rental_type,
      'execution_time_ms', EXTRACT(EPOCH FROM (clock_timestamp() - v_start_time)) * 1000
    )
  );

  RAISE;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

-- Service Line Dates Helper Function
CREATE OR REPLACE FUNCTION build_mm_sl_dates(
  p_charge_line charge_line_with_split
) RETURNS mm_service_line_date_info AS $BODY$
DECLARE
  v_result mm_service_line_date_info;
  v_delivery_date date;
  v_prescription_date text;
  v_error_message text;
  v_start_time timestamp;
  BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  RAISE LOG 'build_mm_sl_dates: Starting with rx_no=%, ticket_no=%', 
    p_charge_line.rx_no, p_charge_line.ticket_no;

  -- Get delivery ticket information if available
  IF p_charge_line.ticket_no IS NOT NULL THEN
    SELECT dt.delivery_date::date
    INTO v_delivery_date
    FROM form_careplan_delivery_tick dt
    WHERE dt.ticket_no = p_charge_line.ticket_no
      AND dt.deleted IS NOT TRUE 
      AND dt.archived IS NOT TRUE
      AND COALESCE(dt.void, 'No') <> 'Yes';
  END IF;

  -- Get prescription date if available
  IF p_charge_line.rx_no IS NOT NULL THEN
    SELECT TO_CHAR(COALESCE(rx.written_date::date, rx.start_date::date), 'MM/DD/YYYY')
    INTO v_prescription_date
    FROM form_careplan_order_rx rx
    WHERE rx.rx_no = p_charge_line.rx_no
      AND rx.deleted IS NOT TRUE
      AND rx.archived IS NOT TRUE;
  END IF;

  -- Build service line dates
  SELECT 
    v_prescription_date::text as prescription_date,
    CASE
      WHEN v_delivery_date IS NOT NULL 
      THEN TO_CHAR(v_delivery_date, 'MM/DD/YYYY')
      ELSE NULL
    END::text as shipped_date
  INTO 
    v_result.prescription_date,
    v_result.shipped_date;

  RAISE LOG 'build_mm_sl_dates: Built date info for rx_no=%, prescription_date=%, shipped_date=%', 
    p_charge_line.rx_no, v_result.prescription_date, v_result.shipped_date;

  RETURN v_result;

EXCEPTION WHEN OTHERS THEN
  v_error_message := SQLERRM;
  
  RAISE LOG 'build_mm_sl_dates: ERROR - % for rx_no=%, ticket_no=%', 
    v_error_message, p_charge_line.rx_no, p_charge_line.ticket_no;

  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    v_error_message,
    'Exception in build_mm_sl_dates',
    'FUNCTION',
    current_schema(),
    'med_claim',
    jsonb_build_object(
      'function_name', 'build_mm_sl_dates',
      'rx_no', p_charge_line.rx_no,
      'ticket_no', p_charge_line.ticket_no,
      'execution_time_ms', EXTRACT(EPOCH FROM (clock_timestamp() - v_start_time)) * 1000
    )
  );

  RAISE;
END;
$BODY$ LANGUAGE plpgsql VOLATILE; -- COB Service Line Adjustments Helper Function

-- Service Line Reference Information Helper Function
CREATE OR REPLACE FUNCTION build_mm_sl_reference_info(
  p_charge_line charge_line_with_split,
  p_insurance_id integer
) RETURNS mm_service_line_reference_info[] AS $BODY$
DECLARE
  v_result mm_service_line_reference_info[];
  v_reference_info mm_service_line_reference_info;
  v_prior_auth_array mm_service_line_reference_info_pa[];
  v_prior_auth_info mm_service_line_reference_info_pa;
  v_prior_auth_number text;
  v_pa_id integer;
  v_inventory_type text;
  v_payer_id integer;
  v_error_message text;
BEGIN
  RAISE LOG 'build_mm_sl_reference_info: Starting with rx_no=%, inventory_type=%, insurance_id=%', 
    p_charge_line.rx_no, p_charge_line.inventory_type, p_insurance_id;

  -- Get inventory type and payer ID for this charge line
  v_inventory_type := p_charge_line.inventory_type;
  v_payer_id := p_charge_line.payer_id;

  RAISE LOG 'build_mm_sl_reference_info: Retrieved inventory_type=%, payer_id=%', v_inventory_type, v_payer_id;

  -- Comprehensive prior authorization lookup logic
  -- Priority order: Patient-level > Insurance-level
  
  -- Step 1: Look for prescription-specific prior auths by inventory type
  WITH order_info AS (
      SELECT 
          cor.drug_pa_id,
          cor.nursing_pa_id,
          cor.rental_pa_id,
          cor.supplies_pa_id
      FROM vw_order_raw cor
      WHERE cor.rx_no = p_charge_line.rx_no
  )
  SELECT 
      CASE 
          WHEN v_inventory_type = 'Drug' AND oi.drug_pa_id IS NOT NULL THEN oi.drug_pa_id
          WHEN v_inventory_type = 'Nursing' AND oi.nursing_pa_id IS NOT NULL THEN oi.nursing_pa_id
          WHEN v_inventory_type = 'Equipment Rental' AND oi.rental_pa_id IS NOT NULL THEN oi.rental_pa_id
          WHEN v_inventory_type = 'Supplies' AND oi.supplies_pa_id IS NOT NULL THEN oi.supplies_pa_id
          ELSE NULL
      END
  INTO v_pa_id
  FROM order_info oi;

  -- Step 2: If no prescription-specific PA found, look for patient-level prior auths
  IF v_pa_id IS NULL THEN
      RAISE LOG 'build_mm_sl_reference_info: No prescription-specific PA found, searching patient-level PAs for patient_id=%, payer_id=%, inventory_type=%', 
        p_charge_line.patient_id, v_payer_id, v_inventory_type;

      -- Look for active patient prior auths for this payer, matching inventory type to auth_type
      SELECT pa.id::integer
      INTO v_pa_id
      FROM form_patient_prior_auth pa
      WHERE pa.patient_id = p_charge_line.patient_id
        AND pa.insurance_id = p_insurance_id
        AND pa.deleted IS NOT TRUE 
        AND pa.archived IS NOT TRUE
        AND (pa.expire_date::date IS NULL OR pa.expire_date::date >= CURRENT_DATE)
        AND pa.status_id::text = '5'
        AND (
            (v_inventory_type = 'Drug' AND pa.auth_type::text LIKE '%Drug%') OR
            (v_inventory_type = 'Nursing' AND pa.auth_type::text LIKE '%Nursing%') OR
            (v_inventory_type = 'Equipment Rental' AND pa.auth_type::text LIKE '%Equipment Rental%') OR
            (v_inventory_type = 'Supplies' AND pa.auth_type::text LIKE '%Supplies%')
        )
      ORDER BY 
          CASE 
              WHEN pa.auth_type::text LIKE '%Drug%' THEN 1
              WHEN pa.auth_type::text LIKE '%Nursing%' THEN 2
              WHEN pa.auth_type::text LIKE '%Equipment Rental%' THEN 3
              WHEN pa.auth_type::text LIKE '%Supplies%' THEN 4
              ELSE 5
          END,
          pa.created_on::timestamp DESC
      LIMIT 1;

      IF v_pa_id IS NOT NULL THEN
          RAISE LOG 'build_mm_sl_reference_info: Found patient-level PA with id=%', v_pa_id;
      END IF;
  END IF;

  -- Step 3: If still no PA found, look for insurance-level prior auths
  IF v_pa_id IS NULL THEN
      RAISE LOG 'build_mm_sl_reference_info: No patient-level PA found, searching insurance-level PAs for patient_id=%, insurance_id=%, inventory_type=%', 
        p_charge_line.patient_id, p_insurance_id, v_inventory_type;

      -- Look for insurance-level prior auths, matching inventory type to auth_type
      SELECT pa.id::integer
      INTO v_pa_id
      FROM form_patient_prior_auth pa
      INNER JOIN form_patient_insurance pi ON pi.id = p_insurance_id
      WHERE pa.patient_id = p_charge_line.patient_id
        AND pa.insurance_id = pi.id
        AND pa.deleted IS NOT TRUE
        AND pa.archived IS NOT TRUE
        AND (pa.expire_date::date IS NULL OR pa.expire_date::date >= CURRENT_DATE)
        AND pa.status_id::text = '5'
        AND (
            (v_inventory_type = 'Drug' AND pa.auth_type::text LIKE '%Drug%') OR
            (v_inventory_type = 'Nursing' AND pa.auth_type::text LIKE '%Nursing%') OR
            (v_inventory_type = 'Equipment Rental' AND pa.auth_type::text LIKE '%Equipment Rental%') OR
            (v_inventory_type = 'Supplies' AND pa.auth_type::text LIKE '%Supplies%')
        )
      ORDER BY 
          CASE 
              WHEN pa.auth_type::text LIKE '%Drug%' THEN 1
              WHEN pa.auth_type::text LIKE '%Nursing%' THEN 2
              WHEN pa.auth_type::text LIKE '%Equipment Rental%' THEN 3
              WHEN pa.auth_type::text LIKE '%Supplies%' THEN 4
              ELSE 5
          END,
          pa.created_on::timestamp DESC
      LIMIT 1;

      IF v_pa_id IS NOT NULL THEN
          RAISE LOG 'build_mm_sl_reference_info: Found insurance-level PA with id=%', v_pa_id;
      END IF;
  END IF;

  -- Get prior authorization number if we found a PA ID
  IF v_pa_id IS NOT NULL THEN
      RAISE LOG 'build_mm_sl_reference_info: Found PA ID=%, retrieving PA number', v_pa_id;

      SELECT pa.number::text
      INTO v_prior_auth_number
      FROM form_patient_prior_auth pa
      WHERE pa.id = v_pa_id
        AND pa.deleted IS NOT TRUE 
        AND pa.archived IS NOT TRUE;

      RAISE LOG 'build_mm_sl_reference_info: Retrieved PA number=%', v_prior_auth_number;
  ELSE
      RAISE LOG 'build_mm_sl_reference_info: No prior authorization found for rx_no=%', p_charge_line.rx_no;
  END IF;

  -- Build reference information if we have a prior auth
  IF v_prior_auth_number IS NOT NULL THEN
    RAISE LOG 'build_mm_sl_reference_info: Building reference info with PA number=%', v_prior_auth_number;

    -- Build the prior authorization info object
    SELECT 
      p_charge_line.patient_id::integer,
      p_insurance_id::integer,
      v_pa_id::integer,
      v_prior_auth_number::text
    INTO 
      v_prior_auth_info.patient_id,
      v_prior_auth_info.insurance_id,
      v_prior_auth_info.pa_id,
      v_prior_auth_info.prior_authorization_or_referral_number;

    -- Create array with the prior auth info
    v_prior_auth_array := ARRAY[v_prior_auth_info];

    -- Build the main reference info object
    SELECT 
      p_charge_line.patient_id::integer,
      v_prior_auth_array
    INTO 
      v_reference_info.patient_id,
      v_reference_info.prior_authorization;

    v_result := ARRAY[v_reference_info];
    
    RAISE LOG 'build_mm_sl_reference_info: Built reference info with prior auth for rx_no=%', p_charge_line.rx_no;
  ELSE
    RAISE LOG 'build_mm_sl_reference_info: No prior authorization found for patient_id=%, insurance_id=%', 
      p_charge_line.patient_id, p_insurance_id;
    v_result := NULL::mm_service_line_reference_info[];
  END IF;

  RAISE LOG 'build_mm_sl_reference_info: Function completed successfully for rx_no=%', p_charge_line.rx_no;
    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;
  
  RAISE LOG 'build_mm_sl_reference_info: ERROR - % for rx_no=%, insurance_id=%', 
    v_error_message, p_charge_line.rx_no, p_insurance_id;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
    'Exception in build_mm_sl_reference_info',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'build_mm_sl_reference_info',
        'rx_no', p_charge_line.rx_no,
        'insurance_id', p_insurance_id
      )
    );
    RAISE;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

-- DME Certification Helper Function
CREATE OR REPLACE FUNCTION build_mm_sl_dme_certification(
  p_charge_line charge_line_with_split
) RETURNS mm_dme_cert_info[] AS $BODY$
DECLARE
  v_result mm_dme_cert_info[];
  v_cert_info mm_dme_cert_info;
  v_send_cert text := 'No';
  v_certification_type_code text := 'I';
  v_duration_months integer := 12;
  v_payer_max_rental_months integer;
  v_error_message text;
BEGIN
  RAISE LOG 'build_mm_sl_dme_certification: Starting with rx_no=%, rental_type=%, payer_id=%', 
    p_charge_line.rx_no, p_charge_line.rental_type, p_charge_line.payer_id;

  -- Only process for equipment rental
  IF p_charge_line.rental_type = 'Purchase' THEN
    RAISE LOG 'build_mm_sl_dme_certification: Item is a purchase, not a rental, returning NULL for rx_no=%', p_charge_line.rx_no;
    RETURN NULL::mm_dme_cert_info[];
  END IF;

  -- Get max rental months from payer settings, default to 12 if not set
  SELECT COALESCE(payer.max_rental_claims::integer, 12)
  INTO v_payer_max_rental_months
  FROM form_payer payer
  WHERE payer.id = p_charge_line.payer_id
    AND payer.deleted IS NOT TRUE 
    AND payer.archived IS NOT TRUE;

  RAISE LOG 'build_mm_sl_dme_certification: Retrieved payer max rental months=% for payer_id=%', 
    v_payer_max_rental_months, p_charge_line.payer_id;

  -- Use charge line rental information instead of deprecated rental table
  -- Default values, can be overridden by payer settings
  v_send_cert := 'No'::text;
  v_certification_type_code := 'I'::text; -- Default to Initial
  v_duration_months := COALESCE(v_payer_max_rental_months, 12); -- Use payer setting or default to 12 months

  RAISE LOG 'build_mm_sl_dme_certification: Set duration_months=%, send_cert=% for rx_no=%', 
    v_duration_months, v_send_cert, p_charge_line.rx_no;

  -- Build certification info if needed
  IF v_send_cert = 'Yes' THEN
    RAISE LOG 'build_mm_sl_dme_certification: Building certification info for rx_no=%', p_charge_line.rx_no;

    SELECT 
      v_send_cert::text,
      COALESCE(v_certification_type_code, 'I')::text, -- DefaultDMECertificationTypeCode.INITIAL
      v_duration_months::integer
    INTO 
      v_cert_info.send_cert,
      v_cert_info.certification_type_code,
      v_cert_info.durable_medical_equipment_duration_in_months;

    v_result := ARRAY[v_cert_info];
    RAISE LOG 'build_mm_sl_dme_certification: Built certification array with % elements', array_length(v_result, 1);
  ELSE
    v_result := NULL::mm_dme_cert_info[];
    RAISE LOG 'build_mm_sl_dme_certification: No certification needed, returning NULL for rx_no=%', p_charge_line.rx_no;
  END IF;

  RAISE LOG 'build_mm_sl_dme_certification: Function completed successfully for rx_no=%', p_charge_line.rx_no;
  RETURN v_result;

EXCEPTION WHEN OTHERS THEN
  v_error_message := SQLERRM;
  
  RAISE LOG 'build_mm_sl_dme_certification: ERROR - % for rx_no=%', v_error_message, p_charge_line.rx_no;

  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    v_error_message,
    'Exception in build_mm_sl_dme_certification',
    'FUNCTION',
    current_schema(),
    'med_claim',
    jsonb_build_object(
      'function_name', 'build_mm_sl_dme_certification',
      'rx_no', p_charge_line.rx_no,
      'rental_type', p_charge_line.rental_type,
      'payer_id', p_charge_line.payer_id
    )
  );
  
    RAISE;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

-- Service Line Supplemental Information Helper Function
CREATE OR REPLACE FUNCTION build_mm_sl_supplemental(
  p_charge_line charge_line_with_split,
  p_insurance_id integer
) RETURNS mm_sl_supplemental_info[] AS $BODY$
DECLARE
  v_result mm_sl_supplemental_info[];
  v_supplemental_info mm_sl_supplemental_info;
  v_document_rec record;
  v_file_path_json jsonb;
  v_filename text;
  v_already_sent boolean := FALSE;
  v_error_message text;
BEGIN
  RAISE LOG 'build_mm_sl_supplemental: Starting with rx_no=%, insurance_id=%', 
    p_charge_line.rx_no, p_insurance_id;

  -- Look for document records attached to this prescription for medical claims
  FOR v_document_rec IN
    SELECT 
      d.id,
      d.file_path,
      d.send_on_med_claim_order,
      d.order_attachment_report_type_code,
      d.order_attachment_transmission_code
    FROM form_document d
    INNER JOIN form_careplan_order_rx rx
      ON rx.rx_no = p_charge_line.rx_no
      AND rx.deleted IS NOT TRUE
      AND rx.archived IS NOT TRUE
    WHERE d.form_filter = 'careplan_order_rx'
      AND d.form_code = rx.code::text
      AND COALESCE(d.send_on_med_claim_order, 'No') = 'Yes'
      AND d.deleted IS NOT TRUE 
      AND d.archived IS NOT TRUE
  LOOP
    RAISE LOG 'build_mm_sl_supplemental: Found document id=% for rx_no=%', v_document_rec.id, p_charge_line.rx_no;

    -- Check if this document has already been sent to the current insurance
    -- using the gerund table gr_form_document_sent_insr_id_to_patient_insurance_id
    SELECT EXISTS(
      SELECT 1 
      FROM gr_form_document_sent_insr_id_to_patient_insurance_id gr
      WHERE gr.form_document_fk = v_document_rec.id::integer
        AND gr.form_patient_insurance_fk = p_insurance_id
    )::boolean INTO v_already_sent;

    -- Only include if not already sent to this insurance
    IF NOT v_already_sent THEN
      RAISE LOG 'build_mm_sl_supplemental: Including document id=% for rx_no=% (not yet sent to insurance_id=%)', 
        v_document_rec.id, p_charge_line.rx_no, p_insurance_id;

      -- Extract filename from file_path JSON if it exists
      v_filename := NULL;
      IF v_document_rec.file_path IS NOT NULL THEN
        BEGIN
          v_file_path_json := v_document_rec.file_path::jsonb;
          v_filename := v_file_path_json->>'filename';
          RAISE LOG 'build_mm_sl_supplemental: Extracted filename=% from document id=%', v_filename, v_document_rec.id;
        EXCEPTION WHEN OTHERS THEN
          RAISE LOG 'build_mm_sl_supplemental: Could not parse file_path JSON for document id=%', v_document_rec.id;
        END;
      END IF;

      -- Build supplemental information for this document
      SELECT 
        v_document_rec.id::integer,
        v_document_rec.order_attachment_report_type_code::text, -- attachment_report_type_code (Supporting Documentation)
        v_document_rec.order_attachment_transmission_code::text, -- attachment_transmission_code (Available on Request)
        ('DOC_' || v_document_rec.id::text || '_' || gen_random_uuid()::text)::text -- attachment_control_number
      INTO
        v_supplemental_info.document_id,
        v_supplemental_info.attachment_report_type_code,
        v_supplemental_info.attachment_transmission_code,
        v_supplemental_info.attachment_control_number;

      -- Add to result array
      v_result := COALESCE(v_result, '{}') || ARRAY[v_supplemental_info];
      
      RAISE LOG 'build_mm_sl_supplemental: Added supplemental info for document id=%, filename=%', 
        v_document_rec.id, COALESCE(v_filename, 'NULL');
    END IF;
  END LOOP;

  -- Return result or NULL if no documents found
  IF v_result IS NULL OR array_length(v_result, 1) = 0 THEN
    RAISE LOG 'build_mm_sl_supplemental: No supplemental documents found for rx_no=%', p_charge_line.rx_no;
    RETURN NULL::mm_sl_supplemental_info[];
  ELSE
    RAISE LOG 'build_mm_sl_supplemental: Built supplemental array with % elements for rx_no=%', 
      array_length(v_result, 1), p_charge_line.rx_no;
    RETURN v_result;
  END IF;

EXCEPTION WHEN OTHERS THEN
  v_error_message := SQLERRM;
  
  RAISE LOG 'build_mm_sl_supplemental: ERROR - % for rx_no=%', v_error_message, p_charge_line.rx_no;

  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    v_error_message,
    'Exception in build_mm_sl_supplemental',
    'FUNCTION',
    current_schema(),
    'med_claim',
    jsonb_build_object(
      'function_name', 'build_mm_sl_supplemental',
      'rx_no', p_charge_line.rx_no,
      'insurance_id', p_insurance_id
    )
  );
  
  RAISE;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

-- Claim Level Adjustments Helper Function
CREATE OR REPLACE FUNCTION build_mm_claim_level_adjustments(
  p_parent_claim_no text
) RETURNS mm_claim_level_adjustment_info[] AS $BODY$
DECLARE
  v_result mm_claim_level_adjustment_info[];
  v_adjustment_info mm_claim_level_adjustment_info;
  v_adjustment_details mm_adjustment_detail_info[];
  v_error_message text;
  v_start_time timestamp;
  v_claim_adj_rec record;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  RAISE LOG 'build_mm_claim_level_adjustments: Starting with parent_claim_no=%', p_parent_claim_no;

  -- Initialize result array
  v_result := ARRAY[]::mm_claim_level_adjustment_info[];

  -- Get claim-level adjustments from 835 responses with proper limits
  DECLARE
    v_group_count integer := 0;
  BEGIN
    FOR v_claim_adj_rec IN
      WITH claim_adjustments AS (
        -- Get all claim-level adjustments for this claim
        SELECT DISTINCT
          r835.claim_no::text as claim_no,
          adj.claim_adjustment_group_code::text as claim_adjustment_group_code,
          adj.adjustment_reason_code::text as adjustment_reason_code,
          adj.adjustment_amount::numeric as adjustment_amount,
          adj.adjustment_quantity::numeric as adjustment_quantity,
          pmt.check_issue_or_eft_effective_date::date as check_issue_or_eft_effective_date
        FROM form_med_claim_resp_835 r835
        INNER JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_pmt sf_pmt 
            ON sf_pmt.form_med_claim_resp_835_fk = r835.id
            AND sf_pmt.delete IS NOT TRUE 
            AND sf_pmt.archive IS NOT TRUE
        INNER JOIN form_med_claim_resp_835_pmt pmt 
            ON pmt.id = sf_pmt.form_med_claim_resp_835_pmt_fk
            AND pmt.deleted IS NOT TRUE 
            AND pmt.archived IS NOT TRUE
        INNER JOIN sf_form_med_claim_resp_835_pmt_to_med_claim_resp_835_adj sf_adj
            ON sf_adj.form_med_claim_resp_835_pmt_fk = pmt.id
            AND sf_adj.delete IS NOT TRUE 
            AND sf_adj.archive IS NOT TRUE
        INNER JOIN form_med_claim_resp_835_adj adj 
            ON adj.id = sf_adj.form_med_claim_resp_835_adj_fk
            AND adj.deleted IS NOT TRUE
            AND adj.archived IS NOT TRUE
        WHERE r835.claim_no = p_parent_claim_no
          AND r835.deleted IS NOT TRUE
          AND r835.archived IS NOT TRUE
          AND (adj.claim_adjustment_group_code = 'PR' OR (adj.claim_adjustment_group_code = 'CO' AND adj.adjustment_reason_code = '96'))
        ORDER BY adj.claim_adjustment_group_code::text, adj.adjustment_amount::numeric DESC
      ),
      grouped_adjustments AS (
        -- Group adjustments by group code and prioritize groups
        SELECT 
          ca.claim_adjustment_group_code,
          array_agg(
            (
              ca.adjustment_reason_code,
              ca.adjustment_amount,
              COALESCE(ca.adjustment_quantity, 1::numeric)
            )::mm_adjustment_detail_info
            ORDER BY ca.adjustment_amount DESC
          ) as adjustment_details,
          -- Prioritize groups: PR first, then CO, then OA, then PI
          CASE 
            WHEN ca.claim_adjustment_group_code = 'PR' THEN 1
            ELSE 2
          END as group_priority
        FROM claim_adjustments ca
        GROUP BY ca.claim_adjustment_group_code
        ORDER BY group_priority
      )
      -- Final selection with limits applied
      SELECT 
        ga.claim_adjustment_group_code,
        CASE 
          WHEN array_length(ga.adjustment_details, 1) > 6 
          THEN ga.adjustment_details[1:6]  -- Limit to first 6 adjustments per group
          ELSE ga.adjustment_details
        END as adjustment_details
      FROM grouped_adjustments ga
    LOOP
      -- Check if we've reached the maximum number of adjustment groups (5)
      IF v_group_count >= 5 THEN
        RAISE LOG 'build_mm_claim_level_adjustments: Reached maximum of 5 adjustment groups, stopping processing';
        EXIT;
      END IF;
    RAISE LOG 'build_mm_claim_level_adjustments: Processing adjustment group=% with % adjustments', 
      v_claim_adj_rec.claim_adjustment_group_code, array_length(v_claim_adj_rec.adjustment_details, 1);

    -- Build the claim level adjustment info
    SELECT 
      v_claim_adj_rec.claim_adjustment_group_code::text,
      v_claim_adj_rec.adjustment_details
    INTO 
      v_adjustment_info.adjustment_group_code,
      v_adjustment_info.adjustment_details;

    -- Add to result array
    v_result := v_result || v_adjustment_info;
    
    -- Increment group count
    v_group_count := v_group_count + 1;

    RAISE LOG 'build_mm_claim_level_adjustments: Added adjustment group % with % details (group %/5)', 
      v_adjustment_info.adjustment_group_code, array_length(v_adjustment_info.adjustment_details, 1), v_group_count;
  END LOOP;
  END;

  RAISE LOG 'build_mm_claim_level_adjustments: Completed with % adjustment groups for claim_no=%', 
    array_length(v_result, 1), p_parent_claim_no;

  RETURN v_result;

EXCEPTION WHEN OTHERS THEN
  v_error_message := SQLERRM;
  
  RAISE LOG 'build_mm_claim_level_adjustments: ERROR - % for parent_claim_no=%', 
    v_error_message, p_parent_claim_no;

  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    v_error_message,
    'Exception in build_mm_claim_level_adjustments',
    'FUNCTION',
    current_schema(),
    'med_claim',
    jsonb_build_object(
      'function_name', 'build_mm_claim_level_adjustments',
      'parent_claim_no', p_parent_claim_no,
      'execution_time_ms', EXTRACT(EPOCH FROM (clock_timestamp() - v_start_time)) * 1000
    )
  );

  RAISE;
END;
$BODY$ LANGUAGE plpgsql VOLATILE; 

-- Apply MM Payer Settings Function
CREATE OR REPLACE FUNCTION apply_mm_payer_settings(
  p_payer_id integer,
  p_previous_payer_id integer DEFAULT NULL,
  p_insurance_id integer DEFAULT NULL,
  p_charge_lines charge_line_with_split[] DEFAULT NULL,
  claim_info jsonb DEFAULT NULL,
  service_lines jsonb DEFAULT NULL
) RETURNS jsonb AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result jsonb;
  v_error_message text;
  v_params jsonb;
  v_payer_settings record;
  v_insurance_settings record;
  v_substatus_id text;
  v_future_service_date boolean := FALSE;
  v_site_id integer;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'payer_id', p_payer_id,
    'previous_payer_id', p_previous_payer_id,
    'insurance_id', p_insurance_id,
    'has_charge_lines', (p_charge_lines IS NOT NULL AND array_length(p_charge_lines, 1) > 0),
    'has_claim_info', (claim_info IS NOT NULL),
    'has_service_lines', (service_lines IS NOT NULL)
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
      'apply_mm_payer_settings'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Applying MM payer settings for payer ID: %', p_payer_id;

    -- Get comprehensive payer and insurance settings
    -- Extract site_id from charge_lines
    SELECT DISTINCT cl.site_id::integer
    INTO v_site_id
    FROM unnest(p_charge_lines) cl
    WHERE cl.site_id IS NOT NULL
    LIMIT 1;
    
    IF v_site_id IS NULL THEN
        RAISE EXCEPTION 'Site ID not found in charge lines for payer settings lookup';
    END IF;
    
    SELECT * INTO v_payer_settings
    FROM get_insurance_claim_settings(p_insurance_id, v_site_id, p_payer_id);

    IF v_payer_settings IS NULL THEN
        RAISE EXCEPTION 'Payer settings not found for payer ID: %', p_payer_id;
    END IF;

    -- Handle UPIN for supplies (mm_send_upin_supplies)
    IF COALESCE(v_payer_settings.mm_send_upin_supplies, 'No') = 'Yes' AND service_lines IS NOT NULL THEN
        DECLARE
            v_service_line jsonb;
            v_inventory_id integer;
            v_inventory_upin text;
            v_updated_service_lines jsonb := '[]'::jsonb;
        BEGIN
            FOR v_service_line IN SELECT jsonb_array_elements(service_lines)
            LOOP
                -- Check if this is a supply type service line
                IF (v_service_line->>'type') = 'Supply' THEN
                    v_inventory_id := (v_service_line->>'inventory_id')::integer;
                    
                    -- Get inventory UPIN
                    SELECT upin INTO v_inventory_upin
                    FROM form_inventory
                    WHERE id = v_inventory_id
                      AND deleted IS NOT TRUE 
                      AND archived IS NOT TRUE;
                    
                    -- If UPIN is set, update procedure identifier and code
                    IF v_inventory_upin IS NOT NULL THEN
                        v_service_line := jsonb_set(v_service_line, '{procedure_identifier}', '"ER"');
                        v_service_line := jsonb_set(v_service_line, '{procedure_code}', to_jsonb(v_inventory_upin));
                    END IF;
                END IF;
                
                v_updated_service_lines := v_updated_service_lines || jsonb_build_array(v_service_line);
            END LOOP;
            
            service_lines := v_updated_service_lines;
        END;
    END IF;
    
    -- Check for future service dates if charge lines provided
    IF p_charge_lines IS NOT NULL AND array_length(p_charge_lines, 1) > 0 THEN
        SELECT EXISTS(
            SELECT 1 
            FROM unnest(p_charge_lines) cl
            WHERE cl.date_of_service_end::date > CURRENT_DATE
        )::boolean INTO v_future_service_date;
        
        -- Set substatus for future dates if payer setting is enabled
        IF v_future_service_date AND COALESCE(v_payer_settings.mm_hold_claims_dos_end::text, 'No') = 'Yes' THEN
            v_substatus_id := '114'; -- Hold for future date
        ELSE
          v_substatus_id := '101'; -- Ready to Bill
        END IF;
    END IF;

    -- Build comprehensive settings result
    v_result := jsonb_build_object(
        'claim_filing_indicator_code', COALESCE(v_payer_settings.mm_claim_filing_indicator_code, 'CI'),
        'default_service_place_id', COALESCE(v_payer_settings.mm_default_service_place_id, '11'),
        'sec_claim_filing_indicator_code', v_payer_settings.mm_sec_claim_filing_indicator_code,

        'send_contract_pricing', v_payer_settings.mm_send_contract_pricing,
        'calc_perc_sales_tax', v_payer_settings.mm_calc_perc_sales_tax,
        'submit_ndc_rx_info', v_payer_settings.mm_submit_ndc_rx_info,
        'sos_only', v_payer_settings.mm_sos_only,
        'send_billing_prov_commercial_number', v_payer_settings.mm_send_billing_prov_commercial_number,
        'billing_commercial_number', v_payer_settings.mm_billing_commercial_number,
        'send_rendering_prov_commercial_number', v_payer_settings.mm_send_rendering_prov_commercial_number,
        'rendering_commercial_number', v_payer_settings.mm_rendering_commercial_number,
        'sec_claims_pr_code', v_payer_settings.mm_sec_claims_pr_code,
        'substatus_id', v_substatus_id,
        'future_service_date', v_future_service_date,
        'contract_type_code', v_payer_settings.contract_type_code,
        'contract_code', v_payer_settings.contract_code,
        'contract_version_identifier', v_payer_settings.contract_version_identifier,
        'contract_percentage', v_payer_settings.contract_percentage,
        'terms_discount_percentage', v_payer_settings.terms_discount_percentage,
        -- Additional payer settings from med_claims_step1.mdc
        'send_upin_supplies', v_payer_settings.mm_send_upin_supplies,
        'hold_claims_dos_end', v_payer_settings.mm_hold_claims_dos_end,
        'daily_bill_rental', v_payer_settings.daily_bill_rental,
        'no_recurring_billing', v_payer_settings.no_recurring_billing,
        'max_rental_claims', v_payer_settings.max_rental_claims,
        'bill_recur_arrears', v_payer_settings.bill_recur_arrears,
        'span_rental_dates', v_payer_settings.span_rental_dates,
        'days_timely_filing', v_payer_settings.days_timely_filing,
        'medigap_id', v_payer_settings.medigap_id,
        'pverify_payer_id', v_payer_settings.pverify_payer_id,
        'payer_name', COALESCE(v_payer_settings.mm_payer_name, v_payer_settings.organization),
        'payer_id', v_payer_settings.mm_payer_id,
        'site_id', v_site_id
    );

    RAISE LOG 'MM payer settings applied successfully: %', v_result;

    -- Log success
    PERFORM log_billing_function(
      'apply_mm_payer_settings'::tracked_function,
      v_params,
      v_result,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in apply_mm_payer_settings',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'apply_mm_payer_settings',
        'payer_id', p_payer_id,
        'previous_payer_id', p_previous_payer_id,
        'insurance_id', p_insurance_id
      )
    );

    PERFORM log_billing_function(
      'apply_mm_payer_settings'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION safe_to_numeric(p_text TEXT) RETURNS NUMERIC AS $$
BEGIN
    IF p_text IS NULL OR p_text = '' THEN
        RETURN NULL;
    END IF;
    
    BEGIN
        -- Remove common currency symbols and commas
        RETURN REPLACE(REPLACE(REPLACE(p_text, '$', ''), ',', ''), ' ', '')::NUMERIC;
    EXCEPTION WHEN OTHERS THEN
        RETURN NULL;
    END;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION safe_to_date(p_text TEXT) RETURNS DATE AS $$
BEGIN
    IF p_text IS NULL OR p_text = '' THEN
        RETURN NULL;
    END IF;
    
    BEGIN
        RETURN p_text::DATE;
    EXCEPTION WHEN OTHERS THEN
        -- Try common date formats
        BEGIN
            RETURN TO_DATE(p_text, 'YYYYMMDD');
        EXCEPTION WHEN OTHERS THEN
            BEGIN
                RETURN TO_DATE(p_text, 'MM/DD/YYYY');
            EXCEPTION WHEN OTHERS THEN
                RETURN NULL;
            END;
        END;
    END;
END;
$$ LANGUAGE plpgsql;