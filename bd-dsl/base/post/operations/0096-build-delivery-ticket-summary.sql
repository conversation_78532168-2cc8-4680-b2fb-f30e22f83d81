DO $$ BEGIN
  PERFORM drop_all_function_signatures('build_delivery_ticket_summary');
END $$;
CREATE OR REPLACE FUNCTION build_delivery_ticket_summary()
RETURNS TRIGGER AS $$
DECLARE
  v_summary TEXT;
  v_inventory_name TEXT;
  v_service_from TEXT;
  v_service_to TEXT;
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);
  -- Separate SELECT INTO for multiple columns
  SELECT
    TO_CHAR(COALESCE(dt.service_from, CURRENT_DATE), 'MM/DD/YYYY'),
    TO_CHAR(COALESCE(dt.service_to, CURRENT_DATE), 'MM/DD/YYYY'),
    COALESCE(highest_priced_item.inventory_name, '-')
  INTO
    v_service_from,
    v_service_to,
    v_inventory_name
  FROM form_careplan_delivery_tick dt
  LEFT JOIN LATERAL (
    SELECT
      inventory_id,
      inv.name as inventory_name
    FROM vw_delivery_items dti
    INNER JOIN form_inventory inv ON inv.id = dti.inventory_id
      AND inv.archived IS NOT TRUE
      AND inv.deleted IS NOT TRUE
    WHERE dti.delivery_ticket_id = NEW.id
    ORDER BY dti.expected DESC
    LIMIT 1
  ) highest_priced_item ON true
  WHERE dt.id = NEW.id;
  
  NEW.summary := v_inventory_name;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER build_delivery_ticket_summary_trigger
BEFORE INSERT OR UPDATE ON form_careplan_delivery_tick
FOR EACH ROW
EXECUTE FUNCTION build_delivery_ticket_summary();