-- Main trigger function for prescription verification changes
DO $$ BEGIN
  PERFORM drop_all_function_signatures('log_patient_status_changes');
END $$;
CREATE OR REPLACE FUNCTION log_patient_status_changes()
    RETURNS TRIGGER
    AS $$
DECLARE
    user_id integer;
    localized_ts timestamp with time zone;
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);
    
    -- Skip if this is not the right table
    IF TG_TABLE_NAME != 'form_patient' THEN
        RETURN NEW;
    END IF;
    -- Determine the user ID from the context
    user_id := COALESCE(NEW.updated_by, NEW.created_by);
    -- Get localized timestamp
    localized_ts := get_user_localized_datetime(user_id);
    -- Handle status_id changes
    IF (TG_OP = 'INSERT' AND NEW.status_id IS NOT NULL) OR (TG_OP = 'UPDATE' AND COALESCE(OLD.status_id, '') != COALESCE(NEW.status_id, '')) THEN
        INSERT INTO form_ledger_patient_activity(localized_datetime, user_id, patient_id, site_id, form, form_id, field, old_value, new_value, description)
            VALUES (localized_ts, user_id, NEW.id, NEW.site_id, 'patient', NEW.id, 'status_id', COALESCE(OLD.status_id, ''), COALESCE(NEW.status_id, ''), 'Patient status changed');
    END IF;

    RETURN NEW;
END;
$$
LANGUAGE plpgsql;

-- Create the triggers
CREATE OR REPLACE TRIGGER patient_status_activity_trigger
    AFTER INSERT OR UPDATE ON form_patient
    FOR EACH ROW
    EXECUTE FUNCTION log_patient_status_changes();

-- First revoke all permissions and then grant only insert
REVOKE ALL ON form_ledger_patient_activity FROM PUBLIC;

GRANT INSERT ON form_ledger_patient_activity TO PUBLIC;

-- Prevent updates and deletes on the activity log
DO $$ BEGIN
  PERFORM drop_all_function_signatures('prevent_patient_ledger_modifications');
END $$;
CREATE OR REPLACE FUNCTION prevent_patient_ledger_modifications()
    RETURNS TRIGGER
    AS $$
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    RAISE EXCEPTION 'Modifications to patient activity log are not allowed';
END;
$$
LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER prevent_patient_ledger_modifications_trigger
    BEFORE UPDATE OR DELETE ON form_ledger_patient_activity
    FOR EACH ROW
    EXECUTE FUNCTION prevent_patient_ledger_modifications();
