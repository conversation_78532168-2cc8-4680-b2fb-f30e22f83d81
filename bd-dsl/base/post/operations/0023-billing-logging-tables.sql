CREATE OR REPLACE FUNCTION add_tracked_function_enum_value() RETURNS void AS $$
DECLARE
    function_name text;
    tracked_functions text[] := ARRAY[
        'build_ncpdp_claim',
        'build_insurance_ncpdp_segment',
        'build_claim_ncpdp_segment',
        'build_patient_ncpdp_segment',
        'build_pharmacy_ncpdp_segment',
        'build_prescriber_ncpdp_segment',
        'build_pricing_ncpdp_segment',
        'build_clinical_ncpdp_segment',
        'build_cob_ncpdp_segment',
        'build_compound_ncpdp_segment',
        'build_dur_ncpdp_segment',
        'get_claim_hierarchy',
        'get_other_payer_data',
        'get_other_payer_paid_data',
        'get_patient_responsibility_data',
        'get_benefits_data',
        'generate_confirmed_dt_presets',
        'get_ncpdp_pricing_calculations',
        'build_ncpdp_eligibility_claim',
        'build_ncpdp_cardfinder_claim',
        'generate_confirmed_dt_presets',
        'create_insurance_invoice_json',
        'create_ready_to_bill_charge_lines',
        'link_rx_and_invoice',
        'create_rx_fill_invoice',
        'create_cob_chargeline_presets',
        'create_cob_invoice',
        'get_claim_reject_codes',
        'build_mm_dx_loop',
        'build_mm_service_facility_segment',
        'build_mm_repricing_segment',
        'build_mm_other_subscriber_loop',
        'build_mm_service_lines_loop',
        'build_mm_supplemental_segment',
        'build_mm_claim_info_other_segment',
        'build_mm_provider_loop',
        'build_mm_receiver_segment',
        'build_mm_submitter_segment',
        'build_mm_subscriber_segment',
        'build_mm_dependent_segment',
        'build_mm_claim_info_segment',
        'build_mm_pay_to_segment',
        'build_mm_claim',
        'apply_mm_payer_settings',
        'build_mm_1500_claim',
        'build_mm_1500_cob_loop',
        'build_mm_1500_charge_lines_loop',
        'build_mm_1500_billing_provider_segment',
        'build_mm_1500_referring_provider_segment',
        'build_mm_1500_diagnosis_loop',
        'build_mm_1500_dates_segment',
        'build_mm_1500_subscriber_segment',
        'build_mm_1500_patient_segment',
        'build_mm_1500_cob_loop',
        'handle_mm_835_response',
        'ncpdp_to_mm_cob',
        'parse_999_response',
        'parse_277_response',
        'parse_835_response',
        'mm_277_handler',
        'mm_835_handler',
        'process_medical_claim_response',
        'build_mm_claim_from_ncpdp'
    ];
BEGIN
    -- Check if the enum exists
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tracked_function') THEN
        -- Loop through each function name and add if missing
        FOREACH function_name IN ARRAY tracked_functions LOOP
            -- Check if this function name already exists in the enum
            PERFORM 1 
            FROM pg_enum 
            WHERE enumtypid = 'tracked_function'::regtype 
            AND enumlabel = function_name;
            
            -- If it doesn't exist, add it
            IF NOT FOUND THEN
                EXECUTE format('ALTER TYPE tracked_function ADD VALUE %L', function_name);
                RAISE NOTICE 'Added enum value: %', function_name;
            END IF;
        END LOOP;
    ELSE
        -- Create the enum if it doesn't exist
        CREATE TYPE tracked_function AS ENUM (
        'build_ncpdp_claim',
        'build_insurance_ncpdp_segment',
        'build_claim_ncpdp_segment',
        'build_patient_ncpdp_segment',
        'build_pharmacy_ncpdp_segment',
        'build_prescriber_ncpdp_segment',
        'build_pricing_ncpdp_segment',
        'build_clinical_ncpdp_segment',
        'build_cob_ncpdp_segment',
        'build_compound_ncpdp_segment',
        'build_dur_ncpdp_segment',
        'get_claim_hierarchy',
        'get_other_payer_data',
        'get_other_payer_paid_data',
        'get_patient_responsibility_data',
        'get_benefits_data',
        'generate_confirmed_dt_presets',
        'get_ncpdp_pricing_calculations',
        'build_ncpdp_eligibility_claim',
        'build_ncpdp_cardfinder_claim',
        'generate_confirmed_dt_presets',
        'create_insurance_invoice_json',
        'create_ready_to_bill_charge_lines',
        'link_rx_and_invoice',
        'create_rx_fill_invoice',
        'create_cob_chargeline_presets',
        'create_cob_invoice',
        'get_claim_reject_codes',
        'build_mm_dx_loop',
        'build_mm_service_facility_segment',
        'build_mm_repricing_segment',
        'build_mm_other_subscriber_loop',
        'build_mm_service_lines_loop',
        'build_mm_supplemental_segment',
        'build_mm_claim_info_other_segment',
        'build_mm_provider_loop',
        'build_mm_receiver_segment',
        'build_mm_submitter_segment',
        'build_mm_subscriber_segment',
        'build_mm_dependent_segment',
        'build_mm_claim_info_segment',
        'build_mm_pay_to_segment',
        'build_mm_claim',
        'apply_mm_payer_settings',
        'build_mm_1500_claim',
        'build_mm_1500_cob_loop',
        'build_mm_1500_charge_lines_loop',
        'build_mm_1500_billing_provider_segment',
        'build_mm_1500_referring_provider_segment',
        'build_mm_1500_diagnosis_loop',
        'build_mm_1500_dates_segment',
        'build_mm_1500_subscriber_segment',
        'build_mm_1500_patient_segment',
        'build_mm_1500_cob_loop',
        'handle_mm_835_response',
        'ncpdp_to_mm_cob',
        'parse_999_response',
        'parse_277_response',
        'parse_835_response',
        'mm_277_handler',
        'mm_835_handler',
        'process_medical_claim_response',
        'build_mm_claim_from_ncpdp'
            );
        RAISE NOTICE 'Created tracked_function enum type with all values';
    END IF;
END;
$$ LANGUAGE plpgsql;


-- Execute the function
SELECT add_tracked_function_enum_value();

-- Clean up (optional)
-- Create the logging table if it doesn't exist
CREATE TABLE IF NOT EXISTS form_billing_function_log (
    id SERIAL PRIMARY KEY,
    function_name tracked_function NOT NULL,
    parameters jsonb NOT NULL,
    result jsonb,
    error_message text,
    execution_time interval,
    created_on timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster querying
CREATE INDEX IF NOT EXISTS idx_billing_function_log_function_name_created ON form_billing_function_log(function_name, created_on DESC);

CREATE OR REPLACE FUNCTION log_billing_function(
    p_function_name tracked_function,
    p_parameters jsonb,
    p_result jsonb DEFAULT NULL,
    p_error_message text DEFAULT NULL,
    p_execution_time interval DEFAULT NULL
) RETURNS void AS $BODY$
DECLARE
    v_row_count integer;
BEGIN
    -- Insert new log entry
    INSERT INTO form_billing_function_log (
        function_name,
        parameters,
        result,
        error_message,
        execution_time
    ) VALUES (
        p_function_name,
        p_parameters,
        p_result,
        p_error_message,
        p_execution_time
    );

    -- Get count of entries for this function
    SELECT COUNT(*) 
    INTO v_row_count
    FROM form_billing_function_log
    WHERE function_name = p_function_name;

    -- If more than 100 entries, delete oldest entries
    IF v_row_count > 100 THEN
        DELETE FROM form_billing_function_log
        WHERE id IN (
            SELECT id 
            FROM form_billing_function_log
            WHERE function_name = p_function_name
            ORDER BY created_on ASC
            LIMIT (v_row_count - 10)
        );
    END IF;
END;
$BODY$ LANGUAGE plpgsql;

-- Create the logging table if it doesn't exist
CREATE TABLE IF NOT EXISTS form_delivery_ticket_confirmation_log (
    id SERIAL PRIMARY KEY,
    function_name text NOT NULL,
    parameters jsonb NOT NULL,
    result jsonb,
    error_message text,
    execution_time interval,
    created_on timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster querying
CREATE INDEX IF NOT EXISTS idx_delivery_ticket_confirmation_log_function_name_created ON form_delivery_ticket_confirmation_log(function_name, created_on DESC);

CREATE OR REPLACE FUNCTION log_delivery_ticket_confirmation(
    p_function_name tracked_function,
    p_parameters jsonb,
    p_result jsonb DEFAULT NULL,
    p_error_message text DEFAULT NULL,
    p_execution_time interval DEFAULT NULL
) RETURNS void AS $BODY$
DECLARE
    v_row_count integer;
BEGIN
    -- Insert new log entry
    INSERT INTO form_delivery_ticket_confirmation_log (
        function_name,
        parameters,
        result,
        error_message,
        execution_time
    ) VALUES (
        p_function_name,
        p_parameters,
        p_result,
        p_error_message,
        p_execution_time
    );

    -- Get count of entries for this function
    SELECT COUNT(*) 
    INTO v_row_count
    FROM form_delivery_ticket_confirmation_log
    WHERE function_name = p_function_name::text;

    -- If more than 100 entries, delete oldest entries
    IF v_row_count > 100 THEN
        DELETE FROM form_delivery_ticket_confirmation_log
        WHERE id IN (
            SELECT id 
            FROM form_delivery_ticket_confirmation_log
            WHERE function_name = p_function_name::text
            ORDER BY created_on ASC
            LIMIT (v_row_count - 10)
        );
    END IF;
END;
$BODY$ LANGUAGE plpgsql;