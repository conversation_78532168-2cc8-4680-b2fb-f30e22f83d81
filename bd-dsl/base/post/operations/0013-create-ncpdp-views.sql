CREATE OR REPLACE VIEW vw_ncpdp_response_summary AS
SELECT
	resp.id as response_id,
    ncpdp.claim_no,
    ncpdp.invoice_no,
    ncpdp.patient_id,
    ncpdp.inventory_id,
    ncpdp.site_id,
    ncpdp.svc_prov_id,
    ncpdp.rx_no,
    ncpdp.date_of_service,
    claim.day_supply,
    ncpdp."cost"::numeric as total_cost,
    ROUND(COALESCE(rprc.opayer_amt_rec::numeric, 0.0) + COALESCE(rprc.total_paid::numeric,0.0),2) as reimbursement,
    CASE WHEN COALESCE(rprc.opayer_amt_rec::numeric, 0.0) + COALESCE(rprc.total_paid::numeric,0.0) > 0 THEN 
        ROUND((COALESCE(rprc.opayer_amt_rec::numeric, 0.0) + COALESCE(rprc.total_paid::numeric,0.0)) - COALESCE(ncpdp.cost::numeric,0.0),2)
    ELSE 0.0
    END as total_profit,
    CASE 
    	WHEN (COALESCE(rprc.opayer_amt_rec::numeric, 0.0) + COALESCE(rprc.total_paid::numeric,0.0)) > 0.0 THEN 
    		ROUND((COALESCE(rprc.opayer_amt_rec::numeric, 0.0) + COALESCE(rprc.total_paid::numeric,0.0) - COALESCE(ncpdp.cost::numeric,0.0)) / (COALESCE(rprc.opayer_amt_rec::numeric, 0.0) + COALESCE(rprc.total_paid::numeric,0.0)) * 100.0,4)
    ELSE 0.00
    END as margin,
    COALESCE(prc.gross_amount_due::numeric,0.0) as total_billed,
    COALESCE(rprc.total_paid::numeric,0.0) as total_paid,
    ROUND((COALESCE(prc.gross_amount_due::numeric,0.0) - COALESCE(rprc.total_paid::numeric,0.0)),2) as total_discount,
    CASE 
    	WHEN COALESCE(rprc.total_paid::numeric,0.0) > 0 THEN
    		ROUND((COALESCE(prc.gross_amount_due::numeric,0.0) - COALESCE(rprc.total_paid::numeric,0.0)) / COALESCE(rprc.total_paid::numeric,0.0) * 100.0, 4)
    	ELSE 0.0
    END as total_paid_percentage,
    COALESCE(inv.formatted_ndc, inv.ndc) as formatted_ndc,
    claim.quantity_dispensed,
    resp.created_on as response_datetime,
    ncpdp.status as claim_status,
    rins.group_id,
    rins.network_reimbursement_id,
    clpt.patient_first_name,
    clpt.patient_last_name,
    clpt.patient_date_of_birth,
    stat.transaction_response_status,
    stat.authorization_number,
    rclm.rx_svc_no_ref_qualifier,
    rclm.rx_svc_no,
    stat.transaction_ref_no,
    rmsg.message,
    rprc.pt_pay_amt,
    rprc.ing_cst_paid,
    rprc.disp_fee_paid,
    rprc.total_paid as total_paid_amount,
    CASE 
    	WHEN COALESCE(ncpdp.is_test,'No') = 'Yes' THEN COALESCE(tcls.total_awp::numeric, 0.0)
    	ELSE COALESCE(cls.total_awp::numeric, 0.0)
    END as total_awp,
    CASE 
    	WHEN COALESCE(rprc.total_paid::numeric,0.0) > 0 AND COALESCE(ncpdp.is_test,'No') = 'Yes' THEN
    		-ROUND((((COALESCE(rprc.total_paid::numeric,0.0) - COALESCE(tcls.total_awp::numeric, 0.0)) / COALESCE(rprc.total_paid::numeric,0.0) * 100.0) * -1), 4)
    	WHEN COALESCE(rprc.total_paid::numeric,0.0) > 0 THEN
      		-ROUND((((COALESCE(rprc.total_paid::numeric,0.0) - COALESCE(cls.total_awp::numeric, 0.0)) / COALESCE(rprc.total_paid::numeric,0.0) * 100.0) * -1), 4)
    	ELSE 0.0
    END as total_awp_percentage,
    resp.request_json_data,
    resp.response_json_data,
    resp.request_d0_raw,
    resp.response_d0_raw
FROM form_ncpdp_response resp
INNER JOIN form_ncpdp ncpdp ON ncpdp.claim_no = resp.claim_no
AND ncpdp.archived IS NOT TRUE
AND ncpdp.deleted IS NOT TRUE 
AND COALESCE(ncpdp.void, 'No') <> 'Yes'
INNER JOIN form_inventory inv ON inv.id = ncpdp.inventory_id
INNER JOIN sf_form_ncpdp_to_ncpdp_pricing sfprc ON sfprc.form_ncpdp_fk = ncpdp.id
AND sfprc.archive IS NOT TRUE
AND sfprc.delete IS NOT TRUE
INNER JOIN sf_form_ncpdp_to_ncpdp_patient sfpt ON sfpt.form_ncpdp_fk = ncpdp.id 
AND sfpt.archive IS NOT TRUE 
AND sfpt."delete" IS NOT TRUE 
INNER JOIN form_ncpdp_patient clpt ON clpt.id = sfpt.form_ncpdp_patient_fk
AND clpt.archived IS NOT TRUE 
AND clpt.deleted IS NOT TRUE
INNER JOIN form_ncpdp_pricing prc ON prc.id = sfprc.form_ncpdp_pricing_fk 
AND prc.archived IS NOT TRUE 
AND prc.deleted IS NOT TRUE
INNER JOIN sf_form_ncpdp_to_ncpdp_claim sfclm ON sfclm.form_ncpdp_fk = ncpdp.id 
AND sfclm."delete" IS NOT TRUE 
AND sfclm.archive IS NOT TRUE
INNER JOIN form_ncpdp_claim claim ON claim.id = sfclm.form_ncpdp_claim_fk
AND claim.archived IS NOT TRUE 
AND claim.deleted IS NOT TRUE
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_stat sfstat ON sfstat.form_ncpdp_response_fk = resp.id
AND sfstat.archive IS NOT TRUE
AND sfstat.delete IS NOT TRUE
LEFT JOIN form_ncpdp_response_stat stat ON stat.id = sfstat.form_ncpdp_response_stat_fk 
AND stat.archived IS NOT TRUE
AND stat.deleted IS NOT TRUE
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_prc sfrprc ON sfrprc.form_ncpdp_response_fk = resp.id
AND sfrprc.archive IS NOT TRUE
AND sfrprc.delete IS NOT TRUE
LEFT JOIN form_ncpdp_response_prc rprc ON rprc.id = sfrprc.form_ncpdp_response_prc_fk 
AND rprc.archived IS NOT TRUE 
AND rprc.deleted IS NOT TRUE
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_insur sfrin ON sfrin.form_ncpdp_response_fk = resp.id
AND sfrin."delete" IS NOT TRUE 
AND sfrin.archive IS NOT TRUE 
LEFT JOIN form_ncpdp_response_insur rins ON rins.id = sfrin.form_ncpdp_response_insur_fk
AND rins.archived IS NOT TRUE 
AND rins.deleted IS NOT TRUE 
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_clm sfrclm ON sfrclm.form_ncpdp_response_fk = resp.id 
AND sfrclm.archive IS NOT TRUE 
AND sfrclm."delete" IS NOT TRUE 
LEFT JOIN form_ncpdp_response_clm rclm ON rclm.id = sfrclm.form_ncpdp_response_clm_fk
AND rclm.deleted IS NOT TRUE 
AND rclm.archived IS NOT TRUE 
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_msg sfmsg ON sfmsg.form_ncpdp_response_fk = resp.id 
AND sfmsg.archive IS NOT TRUE 
AND sfmsg."delete" IS NOT TRUE 
LEFT JOIN form_ncpdp_response_msg rmsg ON rmsg.id = sfmsg.form_ncpdp_response_msg_fk
AND rmsg.archived IS NOT TRUE 
AND rmsg.deleted IS NOT TRUE 
LEFT JOIN LATERAL (SELECT 
	SUM(inv.awp_price::numeric * cl.bill_quantity::numeric) as total_awp
FROM form_test_charge_line cl 
INNER JOIN form_inventory inv ON inv.id = cl.inventory_id
WHERE cl.claim_no = ncpdp.claim_no
AND cl.archived IS NOT TRUE 
AND cl.deleted IS NOT TRUE 
AND COALESCE(cl.void, 'No') <> 'Yes') tcls ON TRUE
LEFT JOIN LATERAL (SELECT 
	SUM(inv.awp_price::numeric * cl.bill_quantity::numeric) as total_awp
FROM form_ledger_charge_line cl 
INNER JOIN form_inventory inv ON inv.id = cl.inventory_id
WHERE cl.claim_no = ncpdp.claim_no
AND cl.archived IS NOT TRUE 
AND cl.deleted IS NOT TRUE 
AND COALESCE(cl.void, 'No') <> 'Yes' AND COALESCE(cl.zeroed, 'No') <> 'Yes') cls ON TRUE;

CREATE OR REPLACE VIEW vw_ncpdp_response_elig_summary AS
SELECT
	resp.id as response_id,
    ncpdp.claim_no,
    ncpdp.patient_id,
    ncpdp.svc_prov_id,
    resp.created_on as response_datetime,
    ncpdp.status as claim_status,
    rins.group_id,
    rins.plan_id,
    rins.mcd_id_no,
    rins.mcd_agcy_no,
    rins.card_holder_id,
    rins.network_reimbursement_id,
    radd.medi_part_d_cov_code,
    radd.cms_low_income_sharing,
    radd.contract_number,
    radd.formulary_id,
    radd.benefit_id,
    radd.medi_part_d_eff_date,
    radd.medi_part_d_trm_date,
    clpt.patient_first_name,
    clpt.patient_last_name,
    clpt.patient_date_of_birth,
    stat.transaction_response_status,
    stat.authorization_number,
    rmsg.message,
    resp.request_json_data,
    resp.response_json_data,
    resp.request_d0_raw,
    resp.response_d0_raw
FROM form_ncpdp_response resp
INNER JOIN form_ncpdp ncpdp ON ncpdp.claim_no = resp.claim_no
AND ncpdp.archived IS NOT TRUE
AND ncpdp.deleted IS NOT TRUE 
AND COALESCE(ncpdp.void, 'No') <> 'Yes'
LEFT JOIN sf_form_ncpdp_to_ncpdp_patient sfpt ON sfpt.form_ncpdp_fk = ncpdp.id 
AND sfpt.archive IS NOT TRUE 
AND sfpt."delete" IS NOT TRUE 
LEFT JOIN form_ncpdp_patient clpt ON clpt.id = sfpt.form_ncpdp_patient_fk
AND clpt.archived IS NOT TRUE 
AND clpt.deleted IS NOT TRUE
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_stat sfstat ON sfstat.form_ncpdp_response_fk = resp.id
AND sfstat.archive IS NOT TRUE
AND sfstat.delete IS NOT TRUE
LEFT JOIN form_ncpdp_response_stat stat ON stat.id = sfstat.form_ncpdp_response_stat_fk 
AND stat.archived IS NOT TRUE
AND stat.deleted IS NOT TRUE
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_prc sfrprc ON sfrprc.form_ncpdp_response_fk = resp.id
AND sfrprc.archive IS NOT TRUE
AND sfrprc.delete IS NOT TRUE
LEFT JOIN form_ncpdp_response_prc rprc ON rprc.id = sfrprc.form_ncpdp_response_prc_fk 
AND rprc.archived IS NOT TRUE 
AND rprc.deleted IS NOT TRUE
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_insur sfrin ON sfrin.form_ncpdp_response_fk = resp.id
AND sfrin."delete" IS NOT TRUE 
AND sfrin.archive IS NOT TRUE 
LEFT JOIN form_ncpdp_response_insur rins ON rins.id = sfrin.form_ncpdp_response_insur_fk
AND rins.archived IS NOT TRUE 
AND rins.deleted IS NOT TRUE 
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_msg sfmsg ON sfmsg.form_ncpdp_response_fk = resp.id 
AND sfmsg.archive IS NOT TRUE 
AND sfmsg."delete" IS NOT TRUE 
LEFT JOIN form_ncpdp_response_msg rmsg ON rmsg.id = sfmsg.form_ncpdp_response_msg_fk
AND rmsg.archived IS NOT TRUE 
AND rmsg.deleted IS NOT TRUE
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_insur_add sfadd ON sfadd.form_ncpdp_response_fk = resp.id 
AND sfadd.archive IS NOT TRUE 
AND sfadd."delete" IS NOT TRUE 
LEFT JOIN form_ncpdp_response_insur_add radd ON radd.id = sfadd.form_ncpdp_response_insur_add_fk
AND radd.archived IS NOT TRUE 
AND radd.deleted IS NOT TRUE;

CREATE OR REPLACE VIEW vw_ncpdp_response_summary_lines AS
SELECT
	resp.id as response_id,
    'Ingr Cost' as type,
    prc.ing_cst_sub::numeric as billed_amount,
    COALESCE(rprc.ing_cst_paid::numeric, 0.0) as paid_amount,
    ROUND(COALESCE(prc.ing_cst_sub::numeric, 0.0) - COALESCE(rprc.ing_cst_paid::numeric, 0.0),2) as discount_amount,
    CASE 
    	WHEN COALESCE(rprc.ing_cst_paid::numeric,0.0) > 0 THEN
    		ROUND((COALESCE(prc.ing_cst_sub::numeric, 0.0) - COALESCE(rprc.ing_cst_paid::numeric,0.0)) / COALESCE(rprc.ing_cst_paid::numeric,0.0) * 100.0, 4)
    ELSE 0.0
    END as paid_percentage
FROM form_ncpdp_response resp
INNER JOIN form_ncpdp ncpdp ON ncpdp.claim_no = resp.claim_no
AND ncpdp.archived IS NOT TRUE 
AND ncpdp.deleted IS NOT TRUE 
AND COALESCE(ncpdp.void, 'No') <> 'Yes'
INNER JOIN sf_form_ncpdp_to_ncpdp_pricing sfprc ON sfprc.form_ncpdp_fk = ncpdp.id
AND sfprc.archive IS NOT TRUE
AND sfprc.delete IS NOT TRUE
INNER JOIN form_ncpdp_pricing prc ON prc.id = sfprc.form_ncpdp_pricing_fk 
AND prc.archived IS NOT TRUE 
AND prc.deleted IS NOT TRUE
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_prc sfrprc ON sfrprc.form_ncpdp_response_fk = resp.id
AND sfrprc.archive IS NOT TRUE
AND sfrprc.delete IS NOT TRUE
LEFT JOIN form_ncpdp_response_prc rprc ON rprc.id = sfrprc.form_ncpdp_response_prc_fk 
AND rprc.archived IS NOT TRUE 
AND rprc.deleted IS NOT TRUE
WHERE prc.ing_cst_sub::numeric > 0

UNION 

SELECT
	resp.id as response_id,
    'Disp Fee' as type,
    prc.disp_fee_sub::numeric as billed_amount,
    COALESCE(rprc.disp_fee_paid::numeric, 0.0) as paid_amount,
    ROUND(COALESCE(prc.disp_fee_sub::numeric, 0.0) - COALESCE(rprc.disp_fee_paid::numeric, 0.0),2) as discount_amount,
    CASE 
    	WHEN COALESCE(rprc.disp_fee_paid::numeric,0.0) > 0 THEN
    		ROUND((COALESCE(prc.disp_fee_sub::numeric, 0.0) - COALESCE(rprc.disp_fee_paid::numeric,0.0)) / COALESCE(rprc.disp_fee_paid::numeric,0.0) * 100.0, 4)
    ELSE 0.0
    END as paid_percentage
FROM form_ncpdp_response resp
INNER JOIN form_ncpdp ncpdp ON ncpdp.claim_no = resp.claim_no
AND ncpdp.archived IS NOT TRUE 
AND ncpdp.deleted IS NOT TRUE 
AND COALESCE(ncpdp.void, 'No') <> 'Yes'
INNER JOIN sf_form_ncpdp_to_ncpdp_pricing sfprc ON sfprc.form_ncpdp_fk = ncpdp.id
AND sfprc.archive IS NOT TRUE
AND sfprc.delete IS NOT TRUE
INNER JOIN form_ncpdp_pricing prc ON prc.id = sfprc.form_ncpdp_pricing_fk 
AND prc.archived IS NOT TRUE 
AND prc.deleted IS NOT TRUE
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_prc sfrprc ON sfrprc.form_ncpdp_response_fk = resp.id
AND sfrprc.archive IS NOT TRUE
AND sfrprc.delete IS NOT TRUE
LEFT JOIN form_ncpdp_response_prc rprc ON rprc.id = sfrprc.form_ncpdp_response_prc_fk 
AND rprc.archived IS NOT TRUE 
AND rprc.deleted IS NOT TRUE
WHERE prc.disp_fee_sub::numeric > 0

UNION 

SELECT
	resp.id as response_id,
    'Incent Amt' as type,
    prc.incv_amt_sub::numeric as billed_amount,
    COALESCE(rprc.incv_amt_paid::numeric, 0.0) as paid_amount,
    ROUND(COALESCE(prc.incv_amt_sub::numeric, 0.0) - COALESCE(rprc.incv_amt_paid::numeric, 0.0), 2) as discount_amount,
    CASE 
    	WHEN COALESCE(rprc.incv_amt_paid::numeric,0.0) > 0 THEN
    		ROUND((COALESCE(prc.incv_amt_sub::numeric, 0.0) - COALESCE(rprc.incv_amt_paid::numeric,0.0)) / COALESCE(rprc.incv_amt_paid::numeric,0.0) * 100.0, 4)
    ELSE 0.0
    END as paid_percentage
FROM form_ncpdp_response resp
INNER JOIN form_ncpdp ncpdp ON ncpdp.claim_no = resp.claim_no
AND ncpdp.archived IS NOT TRUE 
AND ncpdp.deleted IS NOT TRUE 
AND COALESCE(ncpdp.void, 'No') <> 'Yes'
INNER JOIN sf_form_ncpdp_to_ncpdp_pricing sfprc ON sfprc.form_ncpdp_fk = ncpdp.id
AND sfprc.archive IS NOT TRUE
AND sfprc.delete IS NOT TRUE
INNER JOIN form_ncpdp_pricing prc ON prc.id = sfprc.form_ncpdp_pricing_fk 
AND prc.archived IS NOT TRUE 
AND prc.deleted IS NOT TRUE
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_prc sfrprc ON sfrprc.form_ncpdp_response_fk = resp.id
AND sfrprc.archive IS NOT TRUE
AND sfrprc.delete IS NOT TRUE
LEFT JOIN form_ncpdp_response_prc rprc ON rprc.id = sfrprc.form_ncpdp_response_prc_fk 
AND rprc.archived IS NOT TRUE 
AND rprc.deleted IS NOT TRUE
WHERE prc.incv_amt_sub::numeric > 0

UNION 

SELECT
	resp.id as response_id,
    'Other Amt' as type,
    ROUND(oamt.total_oamount_sub::numeric, 2) as billed_amount,
    ROUND(COALESCE(opaid.total_oamount_paid::numeric, 0.0),2) as paid_amount,
    ROUND(COALESCE(oamt.total_oamount_sub::numeric, 0.0) - COALESCE(opaid.total_oamount_paid::numeric, 0.0), 2) as discount_amount,
    CASE 
    	WHEN COALESCE(opaid.total_oamount_paid::numeric,0.0) > 0 THEN
    		ROUND((COALESCE(oamt.total_oamount_sub::numeric, 0.0) - COALESCE(opaid.total_oamount_paid::numeric,0.0)) / COALESCE(opaid.total_oamount_paid::numeric,0.0) * 100.0, 4)
    ELSE 0.0
    END as paid_percentage
FROM form_ncpdp_response resp
INNER JOIN form_ncpdp ncpdp ON ncpdp.claim_no = resp.claim_no
AND ncpdp.archived IS NOT TRUE 
AND ncpdp.deleted IS NOT TRUE 
AND COALESCE(ncpdp.void, 'No') <> 'Yes'
INNER JOIN sf_form_ncpdp_to_ncpdp_pricing sfprc ON sfprc.form_ncpdp_fk = ncpdp.id
AND sfprc.archive IS NOT TRUE
AND sfprc.delete IS NOT TRUE
INNER JOIN form_ncpdp_pricing prc ON prc.id = sfprc.form_ncpdp_pricing_fk 
AND prc.archived IS NOT TRUE 
AND prc.deleted IS NOT TRUE
INNER JOIN LATERAL (
	SELECT 
		SUM(oamt.o_amt_sub::numeric) as total_oamount_sub
	FROM form_ncpdp_pricing_osub oamt
	INNER JOIN sf_form_ncpdp_pricing_to_ncpdp_pricing_osub sfoamt ON sfoamt.form_ncpdp_pricing_osub_fk = oamt.id 
	AND sfoamt.archive IS NOT TRUE 
	AND sfoamt."delete" IS NOT TRUE 
	WHERE oamt.archived IS NOT TRUE 
	AND oamt.deleted IS NOT TRUE 
	AND sfoamt.form_ncpdp_pricing_fk = prc.id
) oamt on true

LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_prc sfrprc ON sfrprc.form_ncpdp_response_fk = resp.id
AND sfrprc.archive IS NOT TRUE
AND sfrprc.delete IS NOT TRUE
LEFT JOIN form_ncpdp_response_prc rprc ON rprc.id = sfrprc.form_ncpdp_response_prc_fk 
AND rprc.archived IS NOT TRUE 
AND rprc.deleted IS NOT TRUE
LEFT JOIN LATERAL (
	SELECT 
		SUM(oprc.oth_amt_paid::numeric) as total_oamount_paid
	FROM form_ncpdp_response_prc_oth oprc
	INNER JOIN sf_form_ncpdp_response_prc_to_ncpdp_response_prc_oth sfoprc ON sfoprc.form_ncpdp_response_prc_fk = rprc.id 
	WHERE sfoprc.form_ncpdp_response_prc_oth_fk = oprc.id 
	AND oprc.archived IS NOT TRUE 
	AND oprc.deleted IS NOT TRUE 
) opaid on true
WHERE oamt.total_oamount_sub::numeric > 0;

CREATE OR REPLACE VIEW vw_ncpdp_response_summary_msg AS
SELECT
	resp.id as response_id,
    msg.add_msg,
    msg.add_msg_qualifier
FROM form_ncpdp_response resp
INNER JOIN form_ncpdp ncpdp ON ncpdp.claim_no = resp.claim_no
AND ncpdp.archived IS NOT TRUE 
AND ncpdp.deleted IS NOT TRUE 
AND COALESCE(ncpdp.void, 'No') <> 'Yes'
INNER JOIN sf_form_ncpdp_response_to_ncpdp_response_stat sfstat ON sfstat.form_ncpdp_response_fk = resp.id
AND sfstat.archive IS NOT TRUE
AND sfstat.delete IS NOT TRUE
INNER JOIN form_ncpdp_response_stat stat ON stat.id = sfstat.form_ncpdp_response_stat_fk 
AND stat.archived IS NOT TRUE
AND stat.deleted IS NOT TRUE
INNER JOIN sf_form_ncpdp_response_stat_to_ncpdp_response_stat_msg sfmsg ON sfmsg.form_ncpdp_response_stat_fk = stat.id
AND sfmsg."delete" IS NOT TRUE 
AND sfmsg.archive IS NOT TRUE 
INNER JOIN form_ncpdp_response_stat_msg msg ON msg.id = sfmsg.form_ncpdp_response_stat_msg_fk
AND msg.deleted IS NOT TRUE 
AND msg.archived IS NOT TRUE;

CREATE OR REPLACE VIEW vw_ncpdp_response_summary_rejmsg AS
SELECT
	resp.id as response_id,
    grrj.form_list_ncpdp_ecl_fk as reject_code
FROM form_ncpdp_response resp
INNER JOIN form_ncpdp ncpdp ON ncpdp.claim_no = resp.claim_no
AND ncpdp.archived IS NOT TRUE 
AND ncpdp.deleted IS NOT TRUE 
AND COALESCE(ncpdp.void, 'No') <> 'Yes'
INNER JOIN sf_form_ncpdp_response_to_ncpdp_response_stat sfstat ON sfstat.form_ncpdp_response_fk = resp.id
AND sfstat.archive IS NOT TRUE
AND sfstat.delete IS NOT TRUE
INNER JOIN form_ncpdp_response_stat stat ON stat.id = sfstat.form_ncpdp_response_stat_fk
AND stat.archived IS NOT TRUE
AND stat.deleted IS NOT TRUE
INNER JOIN sf_form_ncpdp_response_stat_to_ncpdp_response_stat_rj sfrj ON sfrj.form_ncpdp_response_stat_fk = stat.id
AND sfrj."delete" IS NOT TRUE
AND sfrj.archive IS NOT TRUE
INNER JOIN form_ncpdp_response_stat_rj rej ON rej.id = sfrj.form_ncpdp_response_stat_rj_fk
AND rej.archived IS NOT TRUE 
AND rej.deleted IS NOT TRUE 
INNER JOIN gr_form_ncpdp_response_stat_rj_reject_code_to_list_ncpdp_ecl_id grrj ON grrj.form_ncpdp_response_stat_rj_fk = rej.id;

CREATE OR REPLACE VIEW vw_ncpdp_routes AS
SELECT 
    ar.auto_name as auto_name,
    ar.code,
    py.id as payer_id
FROM 
    form_list_ncpdp_route ar
INNER JOIN gr_form_list_ncpdp_route_payer_id_to_payer_id grpy ON grpy.form_list_ncpdp_route_fk = ar.id
INNER JOIN form_payer py ON py.id = grpy.form_payer_fk 
AND py.archived IS NOT TRUE 
AND py.deleted IS NOT TRUE
WHERE 
    ar.deleted IS NOT TRUE 
    AND ar.archived IS NOT TRUE
    AND ar.deleted IS NOT TRUE
    AND COALESCE(ar.active, 'No') = 'Yes'

UNION 

SELECT 
    ar.auto_name as auto_name,
    ar.code,
    py.id as payer_id
FROM 
    form_list_ncpdp_route ar
LEFT JOIN gr_form_list_ncpdp_route_payer_id_to_payer_id grpy ON grpy.form_list_ncpdp_route_fk = ar.id
CROSS JOIN form_payer py
WHERE 
	grpy.form_payer_fk IS NULL
    AND ar.archived IS NOT TRUE
    AND ar.deleted IS NOT TRUE
    AND COALESCE(ar.active, 'No') = 'Yes';

CREATE OR REPLACE VIEW vw_ncpdp_response_cf_summary AS
SELECT
	resp.id as response_id,
    resp.patient_id,
    resp.created_on as response_datetime,
    resp.response_status,
    pt.patient_first_name,
    pt.patient_last_name,
    pt.patient_date_of_birth,
    stat.transaction_response_status,
    rmsg.message,
    resp.request_json_data,
    resp.response_json_data,
    resp.request_d0_raw,
    resp.response_d0_raw
FROM form_ncpdp_response resp
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_pt sftp ON sftp.form_ncpdp_response_fk = resp.id
AND sftp.archive IS NOT TRUE
AND sftp."delete" IS NOT TRUE
LEFT JOIN form_ncpdp_response_pt pt ON pt.id = sftp.form_ncpdp_response_pt_fk
AND pt.archived IS NOT TRUE
AND pt.deleted IS NOT TRUE
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_stat sfstat ON sfstat.form_ncpdp_response_fk = resp.id
AND sfstat.archive IS NOT TRUE
AND sfstat.delete IS NOT TRUE
LEFT JOIN form_ncpdp_response_stat stat ON stat.id = sfstat.form_ncpdp_response_stat_fk 
AND stat.archived IS NOT TRUE
AND stat.deleted IS NOT TRUE
LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_msg sfmsg ON sfmsg.form_ncpdp_response_fk = resp.id 
AND sfmsg.archive IS NOT TRUE 
AND sfmsg."delete" IS NOT TRUE
LEFT JOIN form_ncpdp_response_msg rmsg ON rmsg.id = sfmsg.form_ncpdp_response_msg_fk
AND rmsg.archived IS NOT TRUE 
AND rmsg.deleted IS NOT TRUE
WHERE resp.archived IS NOT TRUE
AND resp.deleted IS NOT TRUE
AND resp.bin_number = '610144';

DROP VIEW IF EXISTS vw_ncpdp_response_cf_payer;
CREATE OR REPLACE VIEW vw_ncpdp_response_cf_payer AS
SELECT
	resp.id as response_id,
    pi.id as insurance_id,
    resp.patient_id,
    pymtch.id as payer_id,
    cobpy.other_coverage_type,
    cobpy.other_id_qualifier,
    cobpy.other_id,
    cobpy.other_pcn,
    cobpy.other_group_no,
    cobpy.other_person_code,
    cobpy.other_help_phone,
    cobpy.other_rel_code,
    cobpy.ben_eff_date,
    cobpy.ben_trm_date,
    cobpy.other_cardholder_id
FROM form_ncpdp_response resp
INNER JOIN sf_form_ncpdp_response_to_ncpdp_response_cob sfcob ON sfcob.form_ncpdp_response_fk = resp.id
AND sfcob.archive IS NOT TRUE
AND sfcob."delete" IS NOT TRUE
INNER JOIN form_ncpdp_response_cob cob ON cob.id = sfcob.form_ncpdp_response_cob_fk
AND cob.archived IS NOT TRUE
AND cob.deleted IS NOT TRUE
INNER JOIN sf_form_ncpdp_response_cob_to_ncpdp_resp_opayer sfcobpy ON sfcobpy.form_ncpdp_response_cob_fk = cob.id
AND sfcobpy.archive IS NOT TRUE
AND sfcobpy."delete" IS NOT TRUE
INNER JOIN form_ncpdp_resp_opayer cobpy ON cobpy.id = sfcobpy.form_ncpdp_resp_opayer_fk
AND cobpy.archived IS NOT TRUE
AND cobpy.deleted IS NOT TRUE
LEFT JOIN LATERAL (
SELECT py.id
FROM form_payer py
WHERE py.bin = cobpy.other_id
AND (py.pcn = cobpy.other_pcn OR cobpy.other_pcn IN ('4444', '9999'))
AND cobpy.other_id IS NOT NULL
AND cobpy.other_pcn IS NOT NULL
AND py.archived IS NOT TRUE
AND py.deleted IS NOT TRUE
AND COALESCE(py.active, 'No') = 'Yes'
) pymtch ON true

INNER JOIN form_patient pt ON pt.id = resp.patient_id
AND pt.archived IS NOT TRUE
AND pt.deleted IS NOT TRUE
LEFT JOIN form_patient_insurance pi ON pi.payer_id = pymtch.id
AND pi.patient_id = pt.id
AND pi.archived IS NOT TRUE
AND pi.deleted IS NOT TRUE
WHERE resp.archived IS NOT TRUE
AND resp.deleted IS NOT TRUE
AND resp.bin_number = '610144';

CREATE OR REPLACE VIEW vw_cardfinder_payers AS
SELECT
	COALESCE(resp.insurance_id, resp.id) as id,
    COALESCE(resp.insurance_id, resp.id) as query_id,
	CASE WHEN resp.insurance_id IS NOT NULL THEN 'patient_insurance' ELSE 'ncpdp_response_cf_payer' END as query_form,
	ncpdpres.response_id,
    resp.insurance_id as insurance_id,
    resp.patient_id,
    py.id as payer_id,
    py.auto_name as payer_id_auto_name,
    resp.other_coverage_type,
    resp.other_id_qualifier,
    resp.other_id as bin,
    resp.other_pcn as pcn,
    resp.other_group_no as group_number,
    resp.other_person_code as person_code,
    resp.other_help_phone,
    resp.other_rel_code as pharmacy_relationship_id,
    resp.ben_eff_date as effective_date,
    resp.ben_trm_date as ben_trm_date,
    resp.other_cardholder_id as cardholder_id,
    CASE WHEN resp.insurance_id IS NOT NULL THEN '#AEEBD7' ELSE NULL END as __row_color
FROM form_ncpdp_response_cf_payer resp
INNER JOIN form_ncpdp_response_cf_summary ncpdpres ON ncpdpres.response_uuid = resp.response_uuid
LEFT JOIN form_payer py ON py.id = resp.payer_id
AND py.archived IS NOT TRUE
AND py.deleted IS NOT TRUE;