CREATE OR REPLACE FUNCTION build_mm_1500_diagnosis_loop(
  charge_lines charge_line_with_split[],
  p_parent_claim_no text DEFAULT NULL,
  p_payer_id integer DEFAULT NULL
) RETURNS mm_1500_diagnosis_info[] AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_1500_diagnosis_info[];
  v_error_message text;
  v_params jsonb;
  v_patient_id integer;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'parent_claim_no', p_parent_claim_no,
    'p_payer_id', p_payer_id
  );
  
  -- Get payer setting for diagnosis code formatting (cms_14)
  DECLARE
      v_use_formatted_diagnosis text := 'No';
  BEGIN
      IF p_payer_id IS NOT NULL THEN
          SELECT p.cms_14::text
          INTO v_use_formatted_diagnosis
          FROM form_payer p
          WHERE p.id = p_payer_id
            AND p.deleted IS NOT TRUE 
            AND p.archived IS NOT TRUE;
      END IF;
      
      v_use_formatted_diagnosis := COALESCE(v_use_formatted_diagnosis, 'No');
  END;

  BEGIN

    -- Log function call
    PERFORM log_billing_function(
      'build_mm_1500_diagnosis_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM 1500 claim diagnosis loop for parent claim no: %', p_parent_claim_no;

    -- If parent claim is provided, copy diagnosis from parent claim
    IF p_parent_claim_no IS NOT NULL THEN
        -- Get diagnosis information from parent medical claim using proper subform joining
        WITH parent_claim_dx AS (
            SELECT DISTINCT
                mc.patient_id::integer as patient_id,
                dx.dx_id::integer as dx_id,
                dx.diagnosis_code::text as diagnosis_code,
                dx.id::integer as id
            FROM form_med_claim_1500 mc
            INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_dx sf_dx ON sf_dx.form_med_claim_1500_fk = mc.id
            INNER JOIN form_med_claim_1500_dx dx ON dx.id = sf_dx.form_med_claim_1500_dx_fk
            WHERE mc.claim_no = p_parent_claim_no
              AND mc.deleted IS NOT TRUE 
              AND mc.archived IS NOT TRUE
              AND sf_dx.delete IS NOT TRUE 
              AND sf_dx.archive IS NOT TRUE
              AND dx.deleted IS NOT TRUE 
              AND dx.archived IS NOT TRUE
              AND dx.diagnosis_code IS NOT NULL
        )
        SELECT array_agg(
            (
                pcd.patient_id,
                pcd.dx_id,
                pcd.diagnosis_code
            )::mm_1500_diagnosis_info
            ORDER BY pcd.id ASC
        )
        INTO v_result
        FROM parent_claim_dx pcd;

        -- If found in medical claim, return it
        IF v_result IS NOT NULL AND array_length(v_result, 1) > 0 THEN
            RAISE LOG 'Found % diagnosis codes from parent medical claim', array_length(v_result, 1);
            
            -- Log success
            PERFORM log_billing_function(
              'build_mm_1500_diagnosis_loop'::tracked_function,
              v_params,
              NULL::jsonb,
              NULL::text,
              clock_timestamp() - v_start_time
            );
            
            RETURN v_result;
        END IF;

        -- If not found in medical claim, try electronic medical claim
        WITH parent_electronic_dx AS (
            SELECT DISTINCT
                mc.patient_id::integer as patient_id,
                dx.dx_id::integer as dx_id,
                dx.diagnosis_code::text as diagnosis_code,
                dx.id::integer as id
            FROM form_med_claim mc
            INNER JOIN sf_form_med_claim_to_med_claim_info sf_info ON sf_info.form_med_claim_fk = mc.id
            INNER JOIN form_med_claim_info ci ON ci.id = sf_info.form_med_claim_info_fk
            INNER JOIN sf_form_med_claim_info_to_med_claim_dx sf_dx ON sf_dx.form_med_claim_info_fk = ci.id
            INNER JOIN form_med_claim_dx dx ON dx.id = sf_dx.form_med_claim_dx_fk
            WHERE mc.claim_no = p_parent_claim_no
              AND mc.deleted IS NOT TRUE 
              AND mc.archived IS NOT TRUE
              AND sf_info.delete IS NOT TRUE 
              AND sf_info.archive IS NOT TRUE
              AND ci.deleted IS NOT TRUE 
              AND ci.archived IS NOT TRUE
              AND sf_dx.delete IS NOT TRUE 
              AND sf_dx.archive IS NOT TRUE
              AND dx.deleted IS NOT TRUE 
              AND dx.archived IS NOT TRUE
              AND dx.diagnosis_code IS NOT NULL
        )
        SELECT array_agg(
            (
                ped.patient_id,
                ped.dx_id,
                ped.diagnosis_code
            )::mm_1500_diagnosis_info
            ORDER BY ped.id ASC
        )
        INTO v_result
        FROM parent_electronic_dx ped;

        -- If found in electronic medical claim, return it
        IF v_result IS NOT NULL AND array_length(v_result, 1) > 0 THEN
            RAISE LOG 'Found % diagnosis codes from parent electronic medical claim', array_length(v_result, 1);
            
            -- Log success
            PERFORM log_billing_function(
              'build_mm_1500_diagnosis_loop'::tracked_function,
              v_params,
              NULL::jsonb,
              NULL::text,
              clock_timestamp() - v_start_time
            );
            
            RETURN v_result;
        END IF;

        -- If not found in medical claim, try NCPDP claim using proper subform joining
        WITH parent_ncpdp_dx AS (
            SELECT DISTINCT
                nc.patient_id::integer as patient_id,
                pd.id::integer as dx_id,
                dx.code::text as diagnosis_code,
                dx.icd_code::text as formatted_diagnosis_code,
                cdx.id::integer as id
            FROM form_ncpdp nc
            INNER JOIN sf_form_ncpdp_to_ncpdp_clinical sf_clinical ON sf_clinical.form_ncpdp_fk = nc.id
            INNER JOIN form_ncpdp_clinical clinical ON clinical.id = sf_clinical.form_ncpdp_clinical_fk
            INNER JOIN sf_form_ncpdp_clinical_to_ncpdp_clinical_dx sf_dx ON sf_dx.form_ncpdp_clinical_fk = clinical.id
            INNER JOIN form_ncpdp_clinical_dx cdx ON cdx.id = sf_dx.form_ncpdp_clinical_dx_fk
            INNER JOIN form_patient_diagnosis pd ON pd.id = cdx.dx_id
            INNER JOIN form_list_diagnosis dx ON dx.id = pd.dx_id
            WHERE nc.claim_no = p_parent_claim_no
              AND nc.deleted IS NOT TRUE 
              AND nc.archived IS NOT TRUE
              AND sf_clinical.delete IS NOT TRUE 
              AND sf_clinical.archive IS NOT TRUE
              AND clinical.deleted IS NOT TRUE 
              AND clinical.archived IS NOT TRUE
              AND sf_dx.delete IS NOT TRUE 
              AND sf_dx.archive IS NOT TRUE
              AND cdx.deleted IS NOT TRUE 
              AND cdx.archived IS NOT TRUE
              AND pd.deleted IS NOT TRUE 
              AND pd.archived IS NOT TRUE
              AND dx.deleted IS NOT TRUE 
              AND dx.archived IS NOT TRUE
              AND COALESCE(
                  CASE WHEN v_use_formatted_diagnosis = 'Yes' THEN dx.icd_code ELSE dx.code END,
                  dx.code
              ) IS NOT NULL
        )
        SELECT array_agg(
            (
                pnd.patient_id,
                pnd.dx_id,
                COALESCE(
                    CASE WHEN v_use_formatted_diagnosis = 'Yes' THEN pnd.formatted_diagnosis_code ELSE pnd.diagnosis_code END,
                    pnd.diagnosis_code
                )
            )::mm_1500_diagnosis_info
            ORDER BY pnd.id ASC
        )
        INTO v_result
        FROM parent_ncpdp_dx pnd;

        -- If found in NCPDP claim, return it
        IF v_result IS NOT NULL AND array_length(v_result, 1) > 0 THEN
            RAISE LOG 'Found % diagnosis codes from parent NCPDP claim', array_length(v_result, 1);
            
            -- Log success
            PERFORM log_billing_function(
              'build_mm_1500_diagnosis_loop'::tracked_function,
              v_params,
              NULL::jsonb,
              NULL::text,
              clock_timestamp() - v_start_time
            );
            
            RETURN v_result;
        END IF;
    END IF;

    -- If no parent claim or no diagnosis found in parent, extract from charge lines
    IF charge_lines IS NULL OR array_length(charge_lines, 1) = 0 THEN
        RAISE LOG 'No charge lines provided, returning empty diagnosis loop';
        RETURN NULL::mm_1500_diagnosis_info[];
    END IF;

    -- Get patient_id from first charge line
    SELECT cl.patient_id::integer
    INTO v_patient_id
    FROM unnest(charge_lines) cl
    WHERE cl.patient_id IS NOT NULL
    LIMIT 1;

    IF v_patient_id IS NULL THEN
        RAISE EXCEPTION 'Patient ID not found in charge lines';
    END IF;

    -- Extract diagnosis codes from charge lines via vw_order_raw
    WITH unique_rx_nos AS (
        SELECT DISTINCT cl.rx_no
        FROM unnest(charge_lines) cl
        WHERE cl.rx_no IS NOT NULL
    ),
    diagnosis_raw AS (
        SELECT 
            vr.rx_no,
            dx_elem.value as dx_element
        FROM unique_rx_nos urn
        INNER JOIN vw_order_raw vr ON vr.rx_no = urn.rx_no
        CROSS JOIN LATERAL jsonb_array_elements(
            CASE 
                WHEN vr.dx_ids IS NOT NULL AND vr.dx_ids != '' THEN vr.dx_ids::jsonb
                ELSE '[]'::jsonb
            END
        ) AS dx_elem(value)
        WHERE vr.dx_ids IS NOT NULL AND vr.dx_ids != ''
    ),
    diagnosis_parsed AS (
        SELECT 
            (dx_element->>'id')::integer as patient_dx_id,
            dx_element->>'dx_id' as dx_code,
            COALESCE((dx_element->>'rank')::integer, 999) as rank_order,
            COALESCE((dx_element->>'sort_order')::integer, 999) as sort_order
        FROM diagnosis_raw
        WHERE dx_element->>'dx_id' IS NOT NULL
          AND dx_element->>'id' IS NOT NULL
    ),
    unique_diagnosis AS (
        SELECT DISTINCT 
            patient_dx_id,
            dx_code,
            rank_order,
            sort_order
        FROM diagnosis_parsed
    ),
    ranked_diagnosis AS (
        SELECT 
            patient_dx_id,
            dx_code,
            rank_order,
            sort_order,
            ROW_NUMBER() OVER (ORDER BY rank_order, sort_order, patient_dx_id) as row_num
        FROM unique_diagnosis
    )
    SELECT array_agg(
        (
            v_patient_id::integer,
            rd.patient_dx_id::integer,
            rd.dx_code::text
        )::mm_1500_diagnosis_info
        ORDER BY rd.row_num
    )
    INTO v_result
    FROM ranked_diagnosis rd
    WHERE rd.row_num <= 12  -- Maximum of 12 diagnoses allowed
    AND rd.dx_code IS NOT NULL;

    -- Return result (could be NULL if no diagnoses found)
    v_result := COALESCE(v_result, ARRAY[]::mm_1500_diagnosis_info[]);

    RAISE LOG 'MM 1500 claim diagnosis loop build result: % diagnosis codes found', 
              COALESCE(array_length(v_result, 1), 0);

    -- Log success
    PERFORM log_billing_function(
      'build_mm_1500_diagnosis_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_1500_diagnosis_loop',
      'FUNCTION',
      current_schema(),
      'med_claim_1500',
      jsonb_build_object(
        'function_name', 'build_mm_1500_diagnosis_loop',
        'parent_claim_no', p_parent_claim_no
      )
    );

    PERFORM log_billing_function(
      'build_mm_1500_diagnosis_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION build_mm_1500_subscriber_segment(
  p_patient_id integer,
  p_insurance_id integer,
  p_charge_lines charge_line_with_split[],
  p_parent_claim_no text DEFAULT NULL,
  p_payer_id integer DEFAULT NULL
) RETURNS TABLE(
  subscriber_payer_organization_name text,
  subscriber_member_id text,
  subscriber_insurance_group_or_policy_number text,
  pa_id integer,
  prior_authorization_number text,
  subscriber_payment_responsibility_level_code text,
  subscriber_relationship_id text,
  subscriber_first_name text,
  subscriber_last_name text,
  subscriber_middle_name text,
  subscriber_dob text,
  subscriber_gender text,
  subscriber_phone_number text,
  subscriber_address1 text,
  subscriber_address2 text,
  subscriber_city text,
  subscriber_state_id text,
  subscriber_postal_code text,
  insurance_type text
) AS $BODY$
DECLARE
  v_start_time timestamp;
  v_error_message text;
  v_params jsonb;
  v_insurance_rec record;
  v_payer_rec record;
  v_patient_rec record;
  v_pa_id integer;
  v_pa_number text;
  v_pa_type_needed text;
  v_payment_responsibility_code text;
  v_subscriber_gender text;
  v_subscriber_phone text;
  v_address_rec record;
  v_insurance_type text;
BEGIN
  -- Check for null input parameters
  IF p_patient_id IS NULL THEN
    RAISE EXCEPTION 'Patient ID cannot be NULL';
  END IF;

  IF p_insurance_id IS NULL THEN
    RAISE EXCEPTION 'Insurance ID cannot be NULL';
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'parent_claim_no', p_parent_claim_no,
    'p_patient_id', p_patient_id,
    'p_insurance_id', p_insurance_id
  );

  BEGIN

    -- Log function call
    PERFORM log_billing_function(
      'build_mm_1500_subscriber_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM 1500 claim subscriber segment for patient ID: %, insurance ID: %', p_patient_id, p_insurance_id;

    -- Get insurance and payer information
    SELECT 
      pi.cardholder_id::text as member_id,
      COALESCE(pi.group_number, pi.policy_number)::text as group_or_policy_number,
      pi.medical_relationship_id::text as relationship_id,
      pi.beneficiary_fname::text as beneficiary_fname,
      pi.beneficiary_lname::text as beneficiary_lname,
      pi.beneficiary_mname::text as beneficiary_mname,
      TO_CHAR(pi.beneficiary_dob, 'MM/DD/YYYY')::text as beneficiary_dob,
      pi.beneficiary_gender::text as beneficiary_gender,
      pi.beneficiary_address1::text as beneficiary_address1,
      pi.beneficiary_address2::text as beneficiary_address2,
      pi.beneficiary_city::text as beneficiary_city,
      pi.beneficiary_state_id::text as beneficiary_state_id,
      pi.beneficiary_postal_code::text as beneficiary_zip,
      pi.payer_id::integer as payer_id
    INTO v_insurance_rec
    FROM form_patient_insurance pi
    WHERE pi.id = p_insurance_id
      AND pi.deleted IS NOT TRUE 
      AND pi.archived IS NOT TRUE;

    IF v_insurance_rec.member_id IS NULL THEN
        RAISE EXCEPTION 'Insurance record not found for ID: %', p_insurance_id;
    END IF;

    -- Get payer information including cms_1 setting for insurance type
    SELECT 
      p.organization::text as organization,
      COALESCE(p.cms_1, 'Other')::text as insurance_type
    INTO v_payer_rec
    FROM form_payer p
    WHERE p.id = v_insurance_rec.payer_id
      AND p.deleted IS NOT TRUE 
      AND p.archived IS NOT TRUE;
      
    v_insurance_type := v_payer_rec.insurance_type;

    -- Get patient information for self-insured cases
    SELECT 
      pt.firstname::text as firstname,
      pt.lastname::text as lastname,
      pt.middlename::text as middlename,
      TO_CHAR(pt.dob, 'MM/DD/YYYY')::text as dob,
      pt.gender::text as gender,
      pt.phone_primary::text as phone_primary,
      pt.phone_home::text as phone_home,
      pt.phone_cell::text as phone_cell,
      pt.phone_work::text as phone_work
    INTO v_patient_rec
    FROM form_patient pt
    WHERE pt.id = p_patient_id
      AND pt.deleted IS NOT TRUE 
      AND pt.archived IS NOT TRUE;

    -- Determine payment responsibility level code using same logic as electronic medical claims
    -- Priority: 1) patient_insurance_med_cond override, 2) payer override, 3) sequence-based default
    DECLARE
        v_med_cond_override text;
        v_payer_override text;
        v_sequence_number integer;
    BEGIN
        -- Check for patient insurance medical condition override
        v_med_cond_override := NULL;
        SELECT pimc.payment_responsibility_level_code
        INTO v_med_cond_override
        FROM form_patient_insurance_med_cond pimc
        WHERE pimc.patient_id = p_patient_id
          AND pimc.active_payer_id = v_insurance_rec.payer_id
          AND pimc.deleted IS NOT TRUE 
          AND pimc.archived IS NOT TRUE
          AND pimc.payment_responsibility_level_code IS NOT NULL
        ORDER BY pimc.created_on DESC
        LIMIT 1;

        -- Check for payer-level override (no override available at payer level)
        v_payer_override := NULL;
        SELECT p.mm_sec_claims_pr_code
        INTO v_payer_override
        FROM form_payer p
        WHERE p.id = v_insurance_rec.payer_id
          AND p.deleted IS NOT TRUE 
          AND p.archived IS NOT TRUE
          AND p.mm_sec_claims_pr_code IS NOT NULL;

        -- Handle NCPDP parent claim relationship code mapping
        IF p_parent_claim_no IS NOT NULL THEN
            -- Check if parent claim is NCPDP and get relationship mapping
            DECLARE
                v_parent_claim_type text;
                v_ncpdp_relationship text;
            BEGIN
                -- Determine parent claim type
                IF EXISTS (
                    SELECT 1 FROM form_ncpdp nc 
                    WHERE nc.claim_no = p_parent_claim_no 
                      AND nc.deleted IS NOT TRUE 
                      AND nc.archived IS NOT TRUE
                ) THEN
                    v_parent_claim_type := 'ncpdp';
                    
                    -- Get NCPDP relationship code and map to medical relationship
                    SELECT nc.pt_rel_code
                    INTO v_ncpdp_relationship
                    FROM form_ncpdp nc
                    WHERE nc.claim_no = p_parent_claim_no
                      AND nc.deleted IS NOT TRUE 
                      AND nc.archived IS NOT TRUE;
                    
                    -- Map NCPDP relationship codes to medical relationship codes (PAT01)
                    -- Based on the same mapping used in electronic medical claims
                    IF v_ncpdp_relationship IS NOT NULL THEN
                        v_insurance_rec.relationship_id := CASE v_ncpdp_relationship
                            WHEN '1' THEN '18'  -- Self -> Self (PAT01)
                            WHEN '2' THEN '01'  -- Spouse -> Spouse (PAT01)
                            WHEN '3' THEN '19'  -- Child -> Child (PAT01)
                            WHEN '4' THEN 'G8'  -- Other -> Other (PAT01)
                            ELSE '18'           -- Default to Self
                        END;
                    END IF;
                ELSE
                    v_parent_claim_type := 'medical';
                END IF;
            END;
        END IF;

        -- Determine sequence number for this claim
        IF p_parent_claim_no IS NOT NULL THEN
            -- Count previous claims in the chain (including all claim types)
            WITH claim_chain AS (
                -- CMS-1500 claims
                SELECT 
                    mc.claim_no,
                    mc.parent_claim_no,
                    mc.created_on,
                    'med_claim_1500' as claim_type
                FROM form_med_claim_1500 mc
                WHERE mc.patient_id = p_patient_id
                  AND (mc.claim_no = p_parent_claim_no OR mc.parent_claim_no IS NOT NULL)
                  AND mc.deleted IS NOT TRUE 
                  AND mc.archived IS NOT TRUE
                UNION
                -- Electronic medical claims
                SELECT 
                    mc.claim_no,
                    mc.parent_claim_no,
                    mc.created_on,
                    'med_claim' as claim_type
                FROM form_med_claim mc
                WHERE mc.patient_id = p_patient_id
                  AND (mc.claim_no = p_parent_claim_no OR mc.parent_claim_no IS NOT NULL)
                  AND mc.deleted IS NOT TRUE 
                  AND mc.archived IS NOT TRUE
                UNION
                -- NCPDP claims
                SELECT 
                    nc.claim_no,
                    nc.parent_claim_no,
                    nc.created_on,
                    'ncpdp' as claim_type
                FROM form_ncpdp nc
                WHERE nc.patient_id = p_patient_id
                  AND (nc.claim_no = p_parent_claim_no OR nc.parent_claim_no IS NOT NULL)
                  AND nc.deleted IS NOT TRUE 
                  AND nc.archived IS NOT TRUE
            ),
            ordered_chain AS (
                SELECT 
                    claim_no,
                    parent_claim_no,
                    created_on,
                    claim_type,
                    ROW_NUMBER() OVER (ORDER BY created_on) as sequence_num
                FROM claim_chain
            )
            SELECT MAX(oc.sequence_num) + 1
            INTO v_sequence_number
            FROM ordered_chain oc;
        ELSE
            v_sequence_number := 1;  -- Primary claim
        END IF;

        -- Apply priority logic
        IF v_med_cond_override IS NOT NULL THEN
            v_payment_responsibility_code := v_med_cond_override;
        ELSIF v_payer_override IS NOT NULL THEN
            v_payment_responsibility_code := v_payer_override;
        ELSE
            -- Use sequence-based default
            v_payment_responsibility_code := CASE v_sequence_number
                    WHEN 1 THEN 'P'
                    WHEN 2 THEN 'S' 
                    WHEN 3 THEN 'T'  
                    WHEN 4 THEN 'A' 
                    WHEN 5 THEN 'B' 
                    WHEN 6 THEN 'C'
                    WHEN 7 THEN 'D'
                    WHEN 8 THEN 'E'
                    WHEN 9 THEN 'F'
                    WHEN 10 THEN 'G'
                    WHEN 11 THEN 'H'
                    ELSE 'U'  -- Other
            END;
        END IF;
    END;

    -- Look up prior authorization using same priority logic as electronic medical claims
    -- Priority: Drug > Nursing > DME > Supplies (same as electronic medical claims)
    IF p_charge_lines IS NOT NULL AND array_length(p_charge_lines, 1) > 0 THEN
        -- Determine inventory types present in charge lines
        WITH charge_types AS (
            SELECT DISTINCT
                cl.inventory_type,
                cl.encounter_id,
                cl.rental_type,
                cl.rx_no
            FROM unnest(p_charge_lines) cl
            WHERE cl.rx_no IS NOT NULL
        ),
        pa_priority AS (
            SELECT 
                CASE 
                    WHEN EXISTS (SELECT 1 FROM charge_types WHERE inventory_type IN ('Drug', 'Compound')) THEN 'Drug'
                    WHEN EXISTS (SELECT 1 FROM charge_types WHERE encounter_id IS NOT NULL AND inventory_type = 'Billable') THEN 'Nursing'
                    WHEN EXISTS (SELECT 1 FROM charge_types WHERE inventory_type = 'Equipment Rental' OR (inventory_type = 'Billable' AND rental_type IS NOT NULL)) THEN 'DME'
                    WHEN EXISTS (SELECT 1 FROM charge_types WHERE inventory_type IN ('Supply', 'Billable')) THEN 'Supplies'
                    ELSE NULL
                END as pa_type_needed,
                CASE 
                    WHEN EXISTS (SELECT 1 FROM charge_types WHERE inventory_type IN ('Drug', 'Compound')) THEN 1
                    WHEN EXISTS (SELECT 1 FROM charge_types WHERE encounter_id IS NOT NULL AND inventory_type = 'Billable') THEN 2
                    WHEN EXISTS (SELECT 1 FROM charge_types WHERE inventory_type = 'Equipment Rental' OR (inventory_type = 'Billable' AND rental_type IS NOT NULL)) THEN 3
                    WHEN EXISTS (SELECT 1 FROM charge_types WHERE inventory_type IN ('Supply', 'Billable')) THEN 4
                    ELSE 5
                END as priority
        )
        SELECT pp.pa_type_needed
        INTO v_pa_type_needed
        FROM pa_priority pp
        WHERE pp.pa_type_needed IS NOT NULL;

        -- Try to get PA from vw_order_raw first (most accurate)
        IF v_pa_type_needed IS NOT NULL THEN
            WITH order_pa_lookup AS (
                SELECT DISTINCT
                    CASE v_pa_type_needed
                        WHEN 'Drug' THEN vr.drug_pa_id
                        WHEN 'Nursing' THEN vr.nursing_pa_id
                        WHEN 'DME' THEN vr.rental_pa_id
                        WHEN 'Supplies' THEN vr.supplies_pa_id
                        ELSE NULL
                    END as pa_id
                FROM unnest(p_charge_lines) cl
                INNER JOIN vw_order_raw vr ON vr.rx_no = cl.rx_no
                WHERE cl.rx_no IS NOT NULL
            )
            SELECT opl.pa_id
            INTO v_pa_id
            FROM order_pa_lookup opl
            WHERE opl.pa_id IS NOT NULL
            LIMIT 1;
        END IF;

        -- If no PA found in vw_order_raw, try manual lookup by type and insurance
        IF v_pa_id IS NULL AND v_pa_type_needed IS NOT NULL THEN
            SELECT pa.id
            INTO v_pa_id
            FROM form_patient_prior_auth pa
            WHERE pa.patient_id = p_patient_id
              AND pa.insurance_id = p_insurance_id
              AND pa.pa_type = v_pa_type_needed
              AND pa.status_id::text = '5'  -- Approved
              AND pa.deleted IS NOT TRUE 
              AND pa.archived IS NOT TRUE
              AND (pa.expire_date IS NULL OR pa.expire_date::date >= CURRENT_DATE)
            ORDER BY pa.created_on DESC
            LIMIT 1;
        END IF;

        -- If still no PA found, try by payer (for COB scenarios)
        IF v_pa_id IS NULL AND v_pa_type_needed IS NOT NULL THEN
            SELECT pa.id
            INTO v_pa_id
            FROM form_patient_prior_auth pa
            INNER JOIN form_patient_insurance pi ON pi.id = pa.insurance_id
            WHERE pa.patient_id = p_patient_id
              AND pi.id = p_insurance_id
              AND pa.pa_type = v_pa_type_needed
              AND pa.status_id::text = '5'  -- Approved
              AND pa.deleted IS NOT TRUE 
              AND pa.archived IS NOT TRUE
              AND (pa.expire_date IS NULL OR pa.expire_date::date >= CURRENT_DATE)
            ORDER BY pa.created_on DESC
            LIMIT 1;
        END IF;

        -- Get PA number if we found a PA
        IF v_pa_id IS NOT NULL THEN
            SELECT pa.number::text
            INTO v_pa_number
            FROM form_patient_prior_auth pa
            WHERE pa.id = v_pa_id;
        END IF;
    END IF;

    -- Determine subscriber details (self vs beneficiary)
    IF COALESCE(v_insurance_rec.relationship_id, '18') = '18' THEN
        -- Self-insured - use patient information
        -- Use same gender code mapping as electronic medical claims
        v_subscriber_gender := CASE v_patient_rec.gender
            WHEN 'Male' THEN 'M'
            WHEN 'Female' THEN 'F'
            ELSE 'U'  -- Unknown (same as electronic medical claims)
        END;
        
        v_subscriber_phone := CASE v_patient_rec.phone_primary
            WHEN 'Home Phone' THEN v_patient_rec.phone_home
            WHEN 'Cell Phone' THEN v_patient_rec.phone_cell
            WHEN 'Work Phone' THEN v_patient_rec.phone_work
            ELSE COALESCE(v_patient_rec.phone_home, v_patient_rec.phone_cell, v_patient_rec.phone_work)
        END;

        -- Get patient address for self-insured
        SELECT 
          pa.street::text as address1,
          pa.street2::text as address2,
          pa.city::text as city,
          pa.state_id::text as state_id,
          pa.zip::text as zip
        INTO v_address_rec
        FROM form_patient_address pa
        WHERE pa.patient_id = p_patient_id
          AND pa.deleted IS NOT TRUE 
          AND pa.archived IS NOT TRUE
          AND pa.address_type = 'Home'
        ORDER BY pa.created_on DESC
        LIMIT 1;
    ELSE
        -- Use beneficiary information
        v_subscriber_gender := COALESCE(v_insurance_rec.beneficiary_gender, 'U');
        v_subscriber_phone := NULL;  -- No phone for beneficiary
        
        -- Use beneficiary address
        v_address_rec.address1 := v_insurance_rec.beneficiary_address1;
        v_address_rec.address2 := v_insurance_rec.beneficiary_address2;
        v_address_rec.city := v_insurance_rec.beneficiary_city;
        v_address_rec.state_id := v_insurance_rec.beneficiary_state_id;
        v_address_rec.zip := v_insurance_rec.beneficiary_zip;
    END IF;

    -- Return the subscriber segment data
    RETURN QUERY SELECT
      v_payer_rec.organization::text,
      v_insurance_rec.member_id::text,
      v_insurance_rec.group_or_policy_number::text,
      v_pa_id::integer,
      v_pa_number::text,
      v_payment_responsibility_code::text,
      COALESCE(v_insurance_rec.relationship_id, '18')::text,
      CASE 
        WHEN COALESCE(v_insurance_rec.relationship_id, '18') = '18' THEN v_patient_rec.firstname
        ELSE v_insurance_rec.beneficiary_fname
      END::text,
      CASE 
        WHEN COALESCE(v_insurance_rec.relationship_id, '18') = '18' THEN v_patient_rec.lastname
        ELSE v_insurance_rec.beneficiary_lname
      END::text,
      CASE 
        WHEN COALESCE(v_insurance_rec.relationship_id, '18') = '18' THEN v_patient_rec.middlename
        ELSE v_insurance_rec.beneficiary_mname
      END::text,
      CASE 
        WHEN COALESCE(v_insurance_rec.relationship_id, '18') = '18' THEN v_patient_rec.dob
        ELSE v_insurance_rec.beneficiary_dob
      END::text,
      v_subscriber_gender::text,
      v_subscriber_phone::text,
      v_address_rec.address1::text,
      v_address_rec.address2::text,
      v_address_rec.city::text,
      v_address_rec.state_id::text,
      v_address_rec.zip::text,
      v_insurance_type::text;

    RAISE LOG 'MM 1500 claim subscriber segment build result: % (PA: %)', v_payer_rec.organization, COALESCE(v_pa_number, 'None');

    -- Log success
    PERFORM log_billing_function(
      'build_mm_1500_subscriber_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_1500_subscriber_segment',
      'FUNCTION',
      current_schema(),
      'med_claim_1500',
      jsonb_build_object(
        'function_name', 'build_mm_1500_subscriber_segment',
        'parent_claim_no', p_parent_claim_no,
        'p_patient_id', p_patient_id,
        'p_insurance_id', p_insurance_id
      )
    );

    PERFORM log_billing_function(
      'build_mm_1500_subscriber_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION build_mm_1500_patient_segment(
  p_patient_id integer
) RETURNS TABLE(
  patient_first_name text,
  patient_last_name text,
  patient_middle_name text,
  patient_dob text,
  patient_gender text,
  patient_phone_number text,
  patient_address1 text,
  patient_address2 text,
  patient_city text,
  patient_state_id text,
  patient_postal_code text
) AS $BODY$
DECLARE
  v_start_time timestamp;
  v_error_message text;
  v_params jsonb;
  v_patient_rec record;
  v_address_rec record;
  v_gender_code text;
  v_phone_number text;
BEGIN
  -- Check for null input parameters
  IF p_patient_id IS NULL THEN
    RAISE EXCEPTION 'Patient ID cannot be NULL';
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'p_patient_id', p_patient_id
  );

  BEGIN

    -- Log function call
    PERFORM log_billing_function(
      'build_mm_1500_patient_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM 1500 claim patient segment for patient ID: %', p_patient_id;

    -- Get patient information
    SELECT 
      pt.firstname::text as firstname,
      pt.lastname::text as lastname,
      pt.middlename::text as middlename,
      TO_CHAR(pt.dob, 'MM/DD/YYYY')::text as dob,
      pt.gender::text as gender,
      pt.phone_primary::text as phone_primary,
      pt.phone_home::text as phone_home,
      pt.phone_cell::text as phone_cell,
      pt.phone_work::text as phone_work
    INTO v_patient_rec
    FROM form_patient pt
    WHERE pt.id = p_patient_id
      AND pt.deleted IS NOT TRUE 
      AND pt.archived IS NOT TRUE;

    IF v_patient_rec.firstname IS NULL THEN
        RAISE EXCEPTION 'Patient record not found for ID: %', p_patient_id;
    END IF;

    -- Convert gender to code (same mapping as electronic medical claims)
    CASE v_patient_rec.gender
        WHEN 'Male' THEN v_gender_code := 'M';
        WHEN 'Female' THEN v_gender_code := 'F';
        ELSE v_gender_code := 'U';  -- Unknown
    END CASE;

    -- Determine primary phone number
    CASE v_patient_rec.phone_primary
        WHEN 'Home Phone' THEN v_phone_number := v_patient_rec.phone_home;
        WHEN 'Cell Phone' THEN v_phone_number := v_patient_rec.phone_cell;
        WHEN 'Work Phone' THEN v_phone_number := v_patient_rec.phone_work;
        ELSE v_phone_number := COALESCE(v_patient_rec.phone_home, v_patient_rec.phone_cell, v_patient_rec.phone_work);
    END CASE;

    -- Get patient address - prioritize Home, then Shipping, then Other
    SELECT 
      pa.street::text as address1,
      pa.street2::text as address2,
      pa.city::text as city,
      pa.state_id::text as state_id,
      pa.zip::text as zip
    INTO v_address_rec
    FROM form_patient_address pa
    WHERE pa.patient_id = p_patient_id
      AND pa.deleted IS NOT TRUE 
      AND pa.archived IS NOT TRUE
      AND pa.address_type = 'Home'
    ORDER BY pa.created_on DESC
    LIMIT 1;

    -- If no Home address, try Shipping
    IF v_address_rec.address1 IS NULL THEN
        SELECT 
          pa.street::text as address1,
          pa.street2::text as address2,
          pa.city::text as city,
          pa.state_id::text as state_id,
          pa.zip::text as zip
        INTO v_address_rec
        FROM form_patient_address pa
        WHERE pa.patient_id = p_patient_id
          AND pa.deleted IS NOT TRUE 
          AND pa.archived IS NOT TRUE
          AND pa.address_type = 'Shipping'
        ORDER BY pa.created_on DESC
        LIMIT 1;
    END IF;

    -- If no Shipping address, try Other
    IF v_address_rec.address1 IS NULL THEN
        SELECT 
          pa.street::text as address1,
          pa.street2::text as address2,
          pa.city::text as city,
          pa.state_id::text as state_id,
          pa.zip::text as zip
        INTO v_address_rec
        FROM form_patient_address pa
        WHERE pa.patient_id = p_patient_id
          AND pa.deleted IS NOT TRUE 
          AND pa.archived IS NOT TRUE
          AND pa.address_type = 'Other'
        ORDER BY pa.created_on DESC
        LIMIT 1;
    END IF;

    -- Return the patient segment data
    RETURN QUERY SELECT
      v_patient_rec.firstname::text,
      v_patient_rec.lastname::text,
      v_patient_rec.middlename::text,
      v_patient_rec.dob::text,
      v_gender_code::text,
      v_phone_number::text,
      v_address_rec.address1::text,
      v_address_rec.address2::text,
      v_address_rec.city::text,
      v_address_rec.state_id::text,
      v_address_rec.zip::text;

    RAISE LOG 'MM 1500 claim patient segment build result: % %', v_patient_rec.firstname, v_patient_rec.lastname;

    -- Log success
    PERFORM log_billing_function(
      'build_mm_1500_patient_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_1500_patient_segment',
      'FUNCTION',
      current_schema(),
      'med_claim_1500',
      jsonb_build_object(
        'function_name', 'build_mm_1500_patient_segment',
        'p_patient_id', p_patient_id
      )
    );

    PERFORM log_billing_function(
      'build_mm_1500_patient_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION build_mm_1500_referring_provider_segment(
  p_site_id integer,
  p_patient_id integer,
  p_payer_id integer,
  p_patient_prescriber_id integer,
  p_parent_claim_no text DEFAULT NULL
) RETURNS TABLE(
  referring_provider_id integer,
  referring_provider_physician_id integer,
  referring_provider_first_name text,
  referring_provider_last_name text,
  referring_provider_npi text,
  referring_provider_state_license_number text,
  referring_provider_taxonomy_id text
) AS $BODY$
DECLARE
  v_start_time timestamp;
  v_error_message text;
  v_params jsonb;
BEGIN
  -- Check for null input parameters
  IF p_patient_id IS NULL THEN
    RAISE EXCEPTION 'Patient ID cannot be NULL';
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'p_patient_id', p_patient_id,
    'p_payer_id', p_payer_id,
    'p_patient_prescriber_id', p_patient_prescriber_id,
    'p_parent_claim_no', p_parent_claim_no,
    'p_site_id', p_site_id
  );

  BEGIN

    -- Log function call
    PERFORM log_billing_function(
      'build_mm_1500_referring_provider_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM 1500 claim referring provider segment for patient prescriber ID: %', p_patient_prescriber_id;

    -- Return the referring provider segment data
    RETURN QUERY 
    SELECT 
      pp.id::integer as referring_provider_id,
      pp.physician_id::integer as referring_provider_physician_id,
      ph.first::text as referring_provider_first_name,
      ph.last::text as referring_provider_last_name,
      ph.npi::text as referring_provider_npi,
      ph.state_license::text as referring_provider_state_license_number,
      ph.taxonomy_id::text as referring_provider_taxonomy_id
    FROM form_patient_prescriber pp
    INNER JOIN form_physician ph ON ph.id = pp.physician_id
      AND ph.deleted IS NOT TRUE 
      AND ph.archived IS NOT TRUE
    WHERE pp.id = p_patient_prescriber_id
      AND pp.deleted IS NOT TRUE 
      AND pp.archived IS NOT TRUE;

    RAISE LOG 'MM 1500 claim referring provider segment build completed';

    -- Log success
    PERFORM log_billing_function(
      'build_mm_1500_referring_provider_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_1500_referring_provider_segment',
      'FUNCTION',
      current_schema(),
      'med_claim_1500',
      jsonb_build_object(
        'function_name', 'build_mm_1500_referring_provider_segment',
        'p_patient_id', p_patient_id,
        'p_payer_id', p_payer_id,
        'p_patient_prescriber_id', p_patient_prescriber_id,
        'p_parent_claim_no', p_parent_claim_no,
        'p_site_id', p_site_id
      )
    );

    PERFORM log_billing_function(
      'build_mm_1500_referring_provider_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION build_mm_1500_billing_provider_segment(
  p_site_id integer
) RETURNS TABLE(
  bill_organization_name text,
  npi text,
  bill_address1 text,
  bill_address2 text,
  bill_city text,
  bill_state_id text,
  bill_phone text
) AS $BODY$
DECLARE
  v_start_time timestamp;
  v_error_message text;
  v_params jsonb;
  v_site_rec record;
BEGIN
  -- Check for null input parameters
  IF p_site_id IS NULL THEN
    RAISE EXCEPTION 'Site ID cannot be NULL';
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'p_site_id', p_site_id
  );

  BEGIN

    -- Log function call
    PERFORM log_billing_function(
      'build_mm_1500_billing_provider_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM 1500 claim billing provider segment for site id: %', p_site_id;

    -- Get site information
    SELECT 
      COALESCE(s.bill_name, s.name)::text as bill_organization_name,
      s.npi::text as npi,
      s.tax_id::text as bill_tax_id,
      COALESCE(s.bill_address1, s.address1)::text as bill_address1,
      COALESCE(s.bill_address2, s.address2)::text as bill_address2,
      COALESCE(s.bill_city, s.city)::text as bill_city,
      COALESCE(s.bill_state_id, s.state_id)::text as bill_state_id,
      s.phone::text as bill_phone
    INTO v_site_rec
    FROM form_site s
    WHERE s.id = p_site_id
      AND s.deleted IS NOT TRUE 
      AND s.archived IS NOT TRUE;

    IF v_site_rec.bill_organization_name IS NULL THEN
        RAISE EXCEPTION 'Site record not found for ID: %', p_site_id;
    END IF;

    -- Return the billing provider segment data
    RETURN QUERY SELECT
      v_site_rec.bill_organization_name::text,
      v_site_rec.npi::text,
      v_site_rec.bill_address1::text,
      v_site_rec.bill_address2::text,
      v_site_rec.bill_city::text,
      v_site_rec.bill_state_id::text,
      v_site_rec.bill_phone::text;

    RAISE LOG 'MM 1500 claim billing provider segment build result: %', v_site_rec.bill_organization_name;

    -- Log success
    PERFORM log_billing_function(
      'build_mm_1500_billing_provider_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_1500_billing_provider_segment',
      'FUNCTION',
      current_schema(),
      'med_claim_1500',
      jsonb_build_object(
        'function_name', 'build_mm_1500_billing_provider_segment',
        'p_site_id', p_site_id
      )
    );

    PERFORM log_billing_function(
      'build_mm_1500_billing_provider_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION build_mm_1500_charge_lines_loop(
  p_site_id integer,
  p_patient_id integer,
  p_payer_id integer,
  p_charge_lines charge_line_with_split[],
  p_diagnoses mm_1500_diagnosis_info[] DEFAULT NULL,
  p_parent_claim_no text DEFAULT NULL,
  p_insurance_id integer DEFAULT NULL
) RETURNS mm_1500_service_line_info[] AS $BODY$
DECLARE
  v_start_time timestamp;
  v_error_message text;
  v_params jsonb;
  v_result mm_1500_service_line_info[];
  v_charge_line charge_line_with_split;
  v_service_line mm_1500_service_line_info;
  v_payer_settings record;
  v_procedure_details record;
  v_service_dates record;
  v_dx_filter integer[] := NULL;
  v_dx_id_1 integer := NULL;
  v_dx_id_2 integer := NULL;
  v_dx_id_3 integer := NULL;
  v_dx_id_4 integer := NULL;
  v_payer_rec record;
  v_supplemental_info text;
BEGIN
  -- Check for null input parameters
  IF p_charge_lines IS NULL OR array_length(p_charge_lines, 1) = 0 THEN
    RAISE EXCEPTION 'Charge lines cannot be NULL or empty';
  END IF;

  -- Check maximum service lines constraint (CMS-1500 limit)
  IF array_length(p_charge_lines, 1) > 50 THEN
    RAISE EXCEPTION 'Cannot have more than 50 service lines on CMS-1500, found %', array_length(p_charge_lines, 1);
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'p_site_id', p_site_id,
    'p_patient_id', p_patient_id,
    'p_payer_id', p_payer_id,
    'p_parent_claim_no', p_parent_claim_no
  );

  BEGIN

    -- Log function call
    PERFORM log_billing_function(
      'build_mm_1500_charge_lines_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM 1500 claim charge lines loop for % charge lines', array_length(p_charge_lines, 1);

    -- Get payer settings for procedure details and CMS-1500 specific settings
    SELECT * INTO v_payer_settings
    FROM get_insurance_claim_settings(p_insurance_id, p_site_id, p_payer_id);
    
    -- Get additional payer settings specific to CMS-1500
    SELECT 
        p.cms_1::text as insurance_type,
        p.cms_2::text as supplemental_info_types,
        p.cms_6::text as default_place_of_service,
        p.cms_14::text as use_formatted_diagnosis_code
    INTO v_payer_rec
    FROM form_payer p
    WHERE p.id = p_payer_id
      AND p.deleted IS NOT TRUE 
      AND p.archived IS NOT TRUE;

    -- Initialize result array
    v_result := ARRAY[]::mm_1500_service_line_info[];

    -- Process each charge line
    FOR v_charge_line IN SELECT * FROM unnest(p_charge_lines) LOOP

        -- Get service dates from delivery ticket or encounter
        SELECT 
            COALESCE(dt.service_from, enc.contact_date)::date as service_date,
            COALESCE(dt.service_to, enc.contact_date)::date as service_date_end
        INTO v_service_dates
        FROM form_ledger_charge_line cl
        LEFT JOIN form_careplan_delivery_tick dt ON dt.ticket_no = cl.ticket_no
            AND dt.deleted IS NOT TRUE 
            AND dt.archived IS NOT TRUE
            AND COALESCE(dt.void, 'No') <> 'Yes'
        LEFT JOIN form_encounter enc ON enc.id = cl.encounter_id
            AND enc.deleted IS NOT TRUE 
            AND enc.archived IS NOT TRUE
        WHERE cl.charge_no = v_charge_line.charge_no;

        -- Get procedure details based on inventory type (same logic as electronic medical claims)
        IF v_charge_line.inventory_type IN ('Drug', 'Compound') THEN
            -- For drugs, use HCPC if available, otherwise J3490 (same as electronic medical claims)
            SELECT 
                CASE 
                    WHEN v_charge_line.hcpc_code IS NOT NULL AND LENGTH(TRIM(v_charge_line.hcpc_code)) > 0 
                        THEN v_charge_line.hcpc_code
                    ELSE 'J3490'  -- Non-specified drug code (DefaultNonClassifiedDrugCode)
                END as procedure_code,
                'HC'::text as procedure_identifier
            INTO v_procedure_details;
        ELSIF v_charge_line.inventory_type IN ('Supply', 'Billable') THEN
            -- For supplies/billables, use hcpc_code
            SELECT 
                v_charge_line.hcpc_code::text as procedure_code,
                'HC'::text as procedure_identifier
            INTO v_procedure_details;
        ELSIF v_charge_line.inventory_type = 'Equipment Rental' THEN
            -- For equipment rental, use HCPC (same as electronic medical claims)
            SELECT 
                CASE 
                    WHEN v_charge_line.hcpc_code IS NOT NULL AND LENGTH(TRIM(v_charge_line.hcpc_code)) > 0 
                        THEN v_charge_line.hcpc_code
                    ELSE 'E1399'  -- Non-specified DME code
                END as procedure_code,
                'HC'::text as procedure_identifier
            INTO v_procedure_details;
        ELSE
            -- Default fallback
            SELECT 
                v_charge_line.hcpc_code::text as procedure_code,
                'HC'::text as procedure_identifier
            INTO v_procedure_details;
        END IF;

        -- Build diagnosis pointers (simplified - will use all diagnosis from claim)
        SELECT 
            CASE 
              WHEN p_diagnoses IS NOT NULL AND array_length(p_diagnoses, 1) > 0 
                  THEN (SELECT array_agg((dx_info).dx_id) FROM unnest(p_diagnoses) dx_info)
                  ELSE NULL::integer[]
              END::integer[] as dx_filter,
              CASE 
                  WHEN p_diagnoses IS NOT NULL AND array_length(p_diagnoses, 1) > 0 
                  THEN (p_diagnoses[1]).dx_id
                  ELSE NULL 
              END::integer as dx_id_1,
              CASE 
                  WHEN p_diagnoses IS NOT NULL AND array_length(p_diagnoses, 1) > 1 
                  THEN (p_diagnoses[2]).dx_id
                  ELSE NULL 
              END::integer as dx_id_2,
              CASE 
                  WHEN p_diagnoses IS NOT NULL AND array_length(p_diagnoses, 1) > 2 
                  THEN (p_diagnoses[3]).dx_id
                  ELSE NULL 
              END::integer as dx_id_3,
              CASE 
                  WHEN p_diagnoses IS NOT NULL AND array_length(p_diagnoses, 1) > 3 
                  THEN (p_diagnoses[4]).dx_id
                  ELSE NULL 
              END::integer as dx_id_4
          INTO v_dx_filter, v_dx_id_1, v_dx_id_2, v_dx_id_3, v_dx_id_4;

        -- Generate supplemental info based on cms_2 payer setting
        v_supplemental_info := NULL;
            IF v_payer_rec.supplemental_info_types IS NOT NULL AND LENGTH(TRIM(v_payer_rec.supplemental_info_types)) > 0 THEN
                DECLARE
                    v_info_parts text[] := ARRAY[]::text[];
                    v_cms2_setting text := v_payer_rec.supplemental_info_types;
                BEGIN
                    -- Add NDC if requested (but not if it's part of another setting)
                    IF (v_cms2_setting LIKE '%NDC%' AND 
                        v_cms2_setting NOT LIKE '%NDC Qualifier%' AND 
                        v_cms2_setting NOT LIKE '%NDC Unit%') AND 
                        v_charge_line.ndc IS NOT NULL THEN
                        v_info_parts := v_info_parts || ('NDC: ' || COALESCE(v_charge_line.formatted_ndc, v_charge_line.ndc));
                    END IF;
                    
                    -- Add NDC Qualifier if requested
                    IF v_cms2_setting LIKE '%NDC Qualifier%' OR LIKE '%NDC Unit Qual/Units%' THEN
                        v_info_parts := v_info_parts || 'QUALIFIER: N4';
                    END IF;
                    
                    -- Add NDC Units if requested (NDC Unit Qual/Units)
                    IF v_cms2_setting LIKE '%NDC Unit Qual/Units%' THEN
                        v_info_parts := v_info_parts || ('UNITS: ' || v_charge_line.metric_quantity::text);
                    END IF;
                    
                    -- Add NDC Unit Price if requested
                    IF v_cms2_setting LIKE '%NDC Unit Price%' THEN
                        v_info_parts := v_info_parts || ('UNIT PRICE: $' || v_charge_line.calc_billed_ea::text);
                    END IF;
                    
                    -- Join all parts with spaces
                    IF array_length(v_info_parts, 1) > 0 THEN
                        v_supplemental_info := array_to_string(v_info_parts, ' ');
                    END IF;
                END;
            END IF;

        -- Build the service line record
        v_service_line := (
            v_charge_line.rx_no::text,
            v_charge_line.charge_no::text,
            p_patient_id::integer,
            TO_CHAR(v_service_dates.service_date, 'MM/DD/YYYY')::text,
            TO_CHAR(v_service_dates.service_date_end, 'MM/DD/YYYY')::text,
            v_charge_line.inventory_id::integer,
            v_charge_line.inventory_type::text,
            v_charge_line.billed::numeric,
            v_charge_line.paid::numeric,
            'UN'::text,
            v_charge_line.bill_quantity::numeric,
            COALESCE(v_payer_rec.default_place_of_service, '12')::text,
            v_charge_line.modifier_1::text,
            v_charge_line.modifier_2::text,
            v_charge_line.modifier_3::text,
            v_charge_line.modifier_4::text,
            v_dx_filter::integer[],
            v_dx_id_1::integer,
            v_dx_id_2::integer,
            v_dx_id_3::integer,
            v_dx_id_4::integer,
            v_procedure_details.procedure_identifier::text,
            v_procedure_details.procedure_code::text,
            v_supplemental_info::text
        )::mm_1500_service_line_info;

        -- Add service line to result array
        v_result := v_result || ARRAY[v_service_line];

    END LOOP;

    RAISE LOG 'MM 1500 claim charge lines loop build result: % service lines built', array_length(v_result, 1);

    -- Log success
    PERFORM log_billing_function(
      'build_mm_1500_charge_lines_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_1500_charge_lines_loop',
      'FUNCTION',
      current_schema(),
      'med_claim_1500',
      jsonb_build_object(
        'function_name', 'build_mm_1500_charge_lines_loop',
        'p_site_id', p_site_id,
        'p_patient_id', p_patient_id,
        'p_payer_id', p_payer_id,
        'p_parent_claim_no', p_parent_claim_no
      )
    );

    PERFORM log_billing_function(
      'build_mm_1500_charge_lines_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION build_mm_1500_dates_segment(
  p_parent_claim_no text DEFAULT NULL
) RETURNS TABLE(
  symptom_date text,
  other_date_type text,
  other_date text,
  last_worked_date text,
  authorized_return_to_work_date text,
  admission_date text,
  discharge_date text
) AS $BODY$
DECLARE
  v_start_time timestamp;
  v_error_message text;
  v_params jsonb;
BEGIN
  -- Check for null input parameters
  IF p_parent_claim_no IS NULL THEN
    RAISE EXCEPTION 'Parent claim no cannot be NULL';
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'p_parent_claim_no', p_parent_claim_no
  );

  BEGIN

    -- Log function call
    PERFORM log_billing_function(
      'build_mm_1500_dates_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM 1500 claim dates segment for parent claim no: %', p_parent_claim_no;

    -- Copy dates from parent claim (check all claim types in chain)
    RETURN QUERY
    SELECT 
      TO_CHAR(mc.symptom_date, 'MM/DD/YYYY')::text as symptom_date,
      mc.other_date_type::text as other_date_type,
      TO_CHAR(mc.other_date, 'MM/DD/YYYY')::text as other_date,
      TO_CHAR(mc.last_worked_date, 'MM/DD/YYYY')::text as last_worked_date,
      TO_CHAR(mc.authorized_return_to_work_date, 'MM/DD/YYYY')::text as authorized_return_to_work_date,
      TO_CHAR(mc.admission_date, 'MM/DD/YYYY')::text as admission_date,
      TO_CHAR(mc.discharge_date, 'MM/DD/YYYY')::text as discharge_date
    FROM form_med_claim_1500 mc
    WHERE mc.claim_no = p_parent_claim_no
      AND mc.deleted IS NOT TRUE 
      AND mc.archived IS NOT TRUE
    UNION ALL
    SELECT 
      TO_CHAR(mc.symptom_date, 'MM/DD/YYYY')::text as symptom_date,
      mc.other_date_type::text as other_date_type,
      TO_CHAR(mc.other_date, 'MM/DD/YYYY')::text as other_date,
      TO_CHAR(mc.last_worked_date, 'MM/DD/YYYY')::text as last_worked_date,
      TO_CHAR(mc.authorized_return_to_work_date, 'MM/DD/YYYY')::text as authorized_return_to_work_date,
      TO_CHAR(mc.admission_date, 'MM/DD/YYYY')::text as admission_date,
      TO_CHAR(mc.discharge_date, 'MM/DD/YYYY')::text as discharge_date
    FROM form_med_claim mc
    WHERE mc.claim_no = p_parent_claim_no
      AND mc.deleted IS NOT TRUE 
      AND mc.archived IS NOT TRUE
    UNION ALL
    -- NCPDP claims don't have these date fields, so return NULLs
    SELECT 
      NULL::text as symptom_date,
      NULL::text as other_date_type,
      NULL::text as other_date,
      NULL::text as last_worked_date,
      NULL::text as authorized_return_to_work_date,
      NULL::text as admission_date,
      NULL::text as discharge_date
    FROM form_ncpdp nc
    WHERE nc.claim_no = p_parent_claim_no
      AND nc.deleted IS NOT TRUE 
      AND nc.archived IS NOT TRUE
    LIMIT 1;

    RAISE LOG 'MM 1500 claim dates segment build completed';

    -- Log success
    PERFORM log_billing_function(
      'build_mm_1500_dates_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_1500_dates_segment',
      'FUNCTION',
      current_schema(),
      'med_claim_1500',
      jsonb_build_object(
        'function_name', 'build_mm_1500_dates_segment',
        'p_parent_claim_no', p_parent_claim_no
      )
    );

    PERFORM log_billing_function(
      'build_mm_1500_dates_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION build_mm_1500_cob_loop(
  p_parent_claim_no text DEFAULT NULL
) RETURNS TABLE(
  cob_insurance_id integer,
  cob_payer_id integer,
  cob_organization_name text,
  cob_insurance_group_or_policy_number text,
  other_payer_claim_control_number text,
  cob_first_name text,
  cob_last_name text,
  cob_middle_name text
) AS $BODY$
DECLARE
  v_start_time timestamp;
  v_error_message text;
  v_params jsonb;
BEGIN
  -- Check for null input parameters
  IF p_parent_claim_no IS NULL THEN
    RAISE EXCEPTION 'Parent claim no cannot be NULL';
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'p_parent_claim_no', p_parent_claim_no
  );

  BEGIN

    -- Log function call
    PERFORM log_billing_function(
      'build_mm_1500_cob_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM 1500 claim COB segment for parent claim no: %', p_parent_claim_no;

    -- Get COB information from parent claim (check all claim types in chain)
    RETURN QUERY
    SELECT
      mc.insurance_id::integer as cob_insurance_id,
      mc.payer_id::integer as cob_payer_id,
      mc.subscriber_payer_organization_name::text as cob_organization_name,
      mc.subscriber_insurance_group_or_policy_number::text as cob_insurance_group_or_policy_number,
      mc.control_number::text as other_payer_claim_control_number,
      mc.subscriber_first_name::text as cob_first_name,
      mc.subscriber_last_name::text as cob_last_name,
      mc.subscriber_middle_name::text as cob_middle_name
    FROM form_med_claim_1500 mc
    WHERE mc.claim_no = p_parent_claim_no
      AND mc.deleted IS NOT TRUE 
      AND mc.archived IS NOT TRUE
    UNION ALL
    SELECT 
      mc.insurance_id::integer as cob_insurance_id,
      mc.payer_id::integer as cob_payer_id,
      py.organization::text as cob_organization_name,
      COALESCE(pi.group_number, pi.policy_number)::text as cob_insurance_group_or_policy_number,
      mc.control_number::text as other_payer_claim_control_number,
      CASE 
        WHEN COALESCE(pi.medical_relationship_id, '18') = '18' THEN pt.firstname
        ELSE pi.beneficiary_fname
      END::text as cob_first_name,
      CASE 
        WHEN COALESCE(pi.medical_relationship_id, '18') = '18' THEN pt.lastname
        ELSE pi.beneficiary_lname
      END::text as cob_last_name,
      CASE 
        WHEN COALESCE(pi.medical_relationship_id, '18') = '18' THEN pt.middlename
        ELSE pi.beneficiary_mname
      END::text as cob_middle_name
    FROM form_med_claim mc
    INNER JOIN form_patient_insurance pi ON pi.id = mc.insurance_id
      AND pi.deleted IS NOT TRUE 
      AND pi.archived IS NOT TRUE
    INNER JOIN form_payer py ON py.id = mc.payer_id
      AND py.deleted IS NOT TRUE 
      AND py.archived IS NOT TRUE
    INNER JOIN form_patient pt ON pt.id = mc.patient_id
      AND pt.deleted IS NOT TRUE 
      AND pt.archived IS NOT TRUE
    WHERE mc.claim_no = p_parent_claim_no
      AND mc.deleted IS NOT TRUE 
      AND mc.archived IS NOT TRUE
    UNION ALL
    SELECT 
      nc.insurance_id::integer as cob_insurance_id,
      nc.payer_id::integer as cob_payer_id,
      py.organization::text as cob_organization_name,
      COALESCE(pi.group_number, pi.policy_number)::text as cob_insurance_group_or_policy_number,
      nc.claim_no::text as other_payer_claim_control_number,  -- NCPDP uses claim_no as control number
      CASE 
        -- For NCPDP claims, check pharmacy_relationship_id and map to medical codes
        WHEN CASE COALESCE(pi.pharmacy_relationship_id, '1')
            WHEN '1' THEN '18'  -- Self -> Self (PAT01)
            WHEN '2' THEN '01'  -- Spouse -> Spouse (PAT01)
            WHEN '3' THEN '19'  -- Child -> Child (PAT01)
            WHEN '4' THEN 'G8'  -- Other -> Other (PAT01)
            ELSE '18'           -- Default to Self
        END = '18' THEN pt.firstname
        ELSE pi.beneficiary_fname
      END::text as cob_first_name,
      CASE 
        WHEN CASE COALESCE(pi.pharmacy_relationship_id, '1')
            WHEN '1' THEN '18'  -- Self -> Self (PAT01)
            WHEN '2' THEN '01'  -- Spouse -> Spouse (PAT01)
            WHEN '3' THEN '19'  -- Child -> Child (PAT01)
            WHEN '4' THEN 'G8'  -- Other -> Other (PAT01)
            ELSE '18'           -- Default to Self
        END = '18' THEN pt.lastname
        ELSE pi.beneficiary_lname
      END::text as cob_last_name,
      CASE 
        WHEN CASE COALESCE(pi.pharmacy_relationship_id, '1')
            WHEN '1' THEN '18'  -- Self -> Self (PAT01)
            WHEN '2' THEN '01'  -- Spouse -> Spouse (PAT01)
            WHEN '3' THEN '19'  -- Child -> Child (PAT01)
            WHEN '4' THEN 'G8'  -- Other -> Other (PAT01)
            ELSE '18'           -- Default to Self
        END = '18' THEN pt.middlename
        ELSE pi.beneficiary_mname
      END::text as cob_middle_name
    FROM form_ncpdp nc
    INNER JOIN form_patient_insurance pi ON pi.id = nc.insurance_id
      AND pi.deleted IS NOT TRUE 
      AND pi.archived IS NOT TRUE
    INNER JOIN form_payer py ON py.id = nc.payer_id
      AND py.deleted IS NOT TRUE 
      AND py.archived IS NOT TRUE
    INNER JOIN form_patient pt ON pt.id = nc.patient_id
      AND pt.deleted IS NOT TRUE 
      AND pt.archived IS NOT TRUE
    WHERE nc.claim_no = p_parent_claim_no
      AND nc.deleted IS NOT TRUE 
      AND nc.archived IS NOT TRUE
    LIMIT 1;

    RAISE LOG 'MM 1500 claim COB segment build completed';

    -- Log success
    PERFORM log_billing_function(
      'build_mm_1500_cob_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_1500_cob_loop',
      'FUNCTION',
      current_schema(),
      'med_claim_1500',
      jsonb_build_object(
        'function_name', 'build_mm_1500_cob_loop',
        'p_parent_claim_no', p_parent_claim_no
      )
    );

    PERFORM log_billing_function(
      'build_mm_1500_cob_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;