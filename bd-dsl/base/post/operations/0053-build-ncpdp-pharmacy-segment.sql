
CREATE OR REPLACE FUNCTION build_pharmacy_ncpdp_segment(
  p_insurance_id integer,
  p_previous_payer_id integer,
  p_patient_id integer,
  p_site_id integer
) RETURNS ncpdp_pharmacy AS $BODY$
DECLARE
  v_start_time timestamp;
  v_execution_time interval;
  v_result ncpdp_pharmacy;
  v_error_message text;
  v_params jsonb;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'insurance_id', p_insurance_id,
    'patient_id', p_patient_id,
    'site_id', p_site_id,
    'previous_payer_id', p_previous_payer_id
  );

  BEGIN  -- Start exception block
    -- Log function call
    PERFORM log_billing_function(
      'build_pharmacy_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );
    -- Validate required parameters
    IF p_patient_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Patient ID is required',
            'Validating required fields in build_pharmacy_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_pharmacy_ncpdp_segment',
                'patient_id', p_patient_id,
                'site_id', p_site_id,
                'insurance_id', p_insurance_id
            )
        );
        RAISE EXCEPTION 'Patient ID cannot be null';
    END IF;

    IF p_site_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Site ID is required',
            'Validating required fields in build_pharmacy_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_pharmacy_ncpdp_segment',
                'patient_id', p_patient_id,
                'site_id', p_site_id,
                'insurance_id', p_insurance_id
            )
        );
        RAISE EXCEPTION 'Site ID cannot be null';
    END IF;

    IF p_insurance_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Insurance ID is required',
            'Validating required fields in build_pharmacy_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_pharmacy_ncpdp_segment',
                'patient_id', p_patient_id,
                'site_id', p_site_id,
                'insurance_id', p_insurance_id
            )
        );
        RAISE EXCEPTION 'Insurance ID cannot be null';
    END IF;

    IF p_insurance_id IS NULL THEN
      SELECT p_site_id::integer as site_id,
        '05'::text as provider_id_qualifier,
        st.npi::text as provider_id
      INTO v_result
      FROM form_site st
      WHERE st.id = p_site_id;
    ELSE
        -- Get insurance settings once
      WITH insurance_info AS (
        SELECT * FROM get_insurance_claim_settings(p_insurance_id, p_site_id, p_previous_payer_id)
      )
      SELECT 
        p_site_id::integer as site_id,
        '05'::text as provider_id_qualifier,
        CASE 
          WHEN COALESCE(ins.send_pharmacist_npi, 'No') = 'Yes' THEN COALESCE(st.pic_npi, st.npi)::text
          ELSE st.npi::text
        END::text as provider_id
      INTO v_result
      FROM form_site st
      CROSS JOIN insurance_info ins
      WHERE st.id = p_site_id;
    END IF;

    -- Validate we got a result
    IF v_result IS NULL THEN
      INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
      ) VALUES (
        'Failed to build pharmacy segment',
        'Validating result in build_pharmacy_ncpdp_segment',
        'FUNCTION',
        current_schema(),
        'form_ncpdp',
        jsonb_build_object(
          'function_name', 'build_pharmacy_ncpdp_segment',
          'site_id', p_site_id
        )
      );
      RAISE EXCEPTION 'Failed to build pharmacy segment for site_id: %', p_site_id;
    END IF;

    -- Log successful completion
    PERFORM log_billing_function(
      'build_pharmacy_ncpdp_segment'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    -- Log error
    v_error_message := SQLERRM;
    
    -- Log to billing error log
    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_pharmacy_ncpdp_segment',
      'FUNCTION',
      current_schema(),
      'form_ncpdp',
      jsonb_build_object(
        'function_name', 'build_pharmacy_ncpdp_segment',
        'site_id', p_site_id,
        'patient_id', p_patient_id,
        'insurance_id', p_insurance_id
      )
    );

    -- Log to NCPDP function log
    PERFORM log_billing_function(
      'build_pharmacy_ncpdp_segment'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;