CREATE OR REPLACE FUNCTION build_mm_claim(
  p_insurance_id integer,
  p_payer_id integer,
  p_patient_id integer,
  p_site_id integer,
  p_date_of_service date,
  p_charge_lines charge_line_with_split[],
  p_date_of_service_end date DEFAULT NULL,
  p_parent_claim_no text DEFAULT NULL
) RETURNS mm_claim_record AS $BODY$
DECLARE
  v_receiver_segment mm_receiver_segment;
  v_submitter_segment mm_submitter_segment;
  v_pay_to_address mm_address_info;
  v_subscriber mm_subscriber_segment;
  v_dependent mm_dependent_segment;
  v_claim_information mm_claim_info_segment;
  v_providers mm_providers_loop;
  v_start_time timestamp;
  v_execution_time interval;
  v_result mm_claim_record;
  v_error_message text;
  v_params jsonb;
  v_parent_payer_id integer;
  v_expected numeric;
  v_is_cob boolean := FALSE;
  v_substatus_id text;
  v_payer_name text;
  v_trading_partner_id text;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build minimal parameters JSON for logging
  v_params := jsonb_build_object(
    'parent_claim_no', p_parent_claim_no,
    'insurance_id', p_insurance_id,
    'payer_id', p_payer_id,
    'patient_id', p_patient_id,
    'site_id', p_site_id
  );

  BEGIN  -- Start exception block
    -- Log function call
    BEGIN
      PERFORM log_billing_function(
        'build_mm_claim'::tracked_function,
        v_params::jsonb,
        NULL::jsonb,
        NULL::text,
        clock_timestamp() - v_start_time
      );
    EXCEPTION WHEN OTHERS THEN
      -- Ignore logging errors
      NULL;
    END;

    IF p_charge_lines IS NULL OR array_length(p_charge_lines, 1) = 0 THEN
      RAISE EXCEPTION 'Charge lines are required to build a claim';
    END IF;

    -- Validate required parameters
    IF p_insurance_id IS NULL THEN
        RAISE EXCEPTION 'Insurance ID cannot be null';
    END IF;
    
    IF p_payer_id IS NULL THEN
        RAISE EXCEPTION 'Payer ID cannot be null';
    END IF;

    IF p_site_id IS NULL THEN
        RAISE EXCEPTION 'Site ID cannot be null';
    END IF;

    IF p_patient_id IS NULL THEN
        RAISE EXCEPTION 'Patient ID cannot be null';
    END IF;

    -- Check if this is a COB claim
    v_is_cob := (p_parent_claim_no IS NOT NULL);
    
    IF v_is_cob THEN
        -- Extract parent payer ID for COB claims
        -- Check multiple claim tables for parent claim
        SELECT COALESCE(mc.payer_id, nc.payer_id, mc1500.payer_id)
        INTO v_parent_payer_id
        FROM (SELECT p_parent_claim_no AS claim_no) search
        LEFT JOIN form_med_claim mc 
            ON mc.claim_no = search.claim_no
            AND mc.deleted IS NOT TRUE 
            AND mc.archived IS NOT TRUE
        LEFT JOIN form_ncpdp nc 
            ON nc.claim_no = search.claim_no
            AND nc.deleted IS NOT TRUE 
            AND nc.archived IS NOT TRUE
        LEFT JOIN form_med_claim_1500 mc1500 
            ON mc1500.claim_no = search.claim_no
            AND mc1500.deleted IS NOT TRUE 
            AND mc1500.archived IS NOT TRUE;

        IF v_parent_payer_id IS NULL THEN
            RAISE EXCEPTION 'Parent payer not found for claim %', p_parent_claim_no;
        END IF;
        
        RAISE LOG 'Building COB claim for parent claim %', p_parent_claim_no;
    END IF;
    
    -- Sum up the billed amount from p_charge_lines
    v_expected := 0::numeric;
    IF p_charge_lines IS NOT NULL AND array_length(p_charge_lines, 1) IS NOT NULL THEN
      FOR i IN 1..array_length(p_charge_lines, 1) LOOP
        IF p_charge_lines[i] IS NOT NULL AND p_charge_lines[i].expected IS NOT NULL THEN
          v_expected := v_expected + COALESCE(p_charge_lines[i].expected, 0)::numeric;
        END IF;
      END LOOP;
    END IF;

    RAISE LOG 'build_mm_claim: Building segments for payer_id=%, patient_id=%, insurance_id=%', p_payer_id, p_patient_id, p_insurance_id;
    
    v_receiver_segment := build_mm_receiver_segment(p_payer_id, p_patient_id, p_insurance_id, p_parent_claim_no); -- Build receiver segment (payer information)
    RAISE LOG 'build_mm_claim: Receiver segment result: %', v_receiver_segment IS NOT NULL;
    
    v_submitter_segment := build_mm_submitter_segment(p_site_id); -- Build submitter segment (site information)
    RAISE LOG 'build_mm_claim: Submitter segment result: %', v_submitter_segment IS NOT NULL;
    
    v_pay_to_address := build_mm_pay_to_segment(p_site_id); -- Build pay-to address segment (billing address)
    RAISE LOG 'build_mm_claim: Pay-to address result: %', v_pay_to_address IS NOT NULL;
    
    v_subscriber := build_mm_subscriber_segment(p_patient_id, p_payer_id, p_insurance_id, p_parent_claim_no); -- Build subscriber segment (patient/insurance information)
    RAISE LOG 'build_mm_claim: Subscriber segment result: %', v_subscriber IS NOT NULL;
    
    v_dependent := build_mm_dependent_segment(p_patient_id, p_insurance_id); -- Build dependent segment (only if patient is not subscriber)
    RAISE LOG 'build_mm_claim: Dependent segment result: %', v_dependent IS NOT NULL;
    
    v_claim_information := build_mm_claim_info_segment(p_site_id, p_patient_id, p_insurance_id, p_payer_id, p_charge_lines, p_parent_claim_no);     -- Build claim information segment (the main body)
    RAISE LOG 'build_mm_claim: Claim information segment result: %', v_claim_information IS NOT NULL;
    
    v_providers := build_mm_provider_loop(p_patient_id, p_charge_lines);     -- Build providers loop
    RAISE LOG 'build_mm_claim: Providers loop result: %', v_providers IS NOT NULL;

    -- Extract substatus_id from claim information if it was set by payer settings
    BEGIN
        DECLARE
            v_payer_settings jsonb;
            v_previous_payer_id integer;
        BEGIN
            -- Get previous payer ID for COB claims
            IF v_is_cob THEN
                SELECT COALESCE(mc.payer_id, nc.payer_id, mc1500.payer_id)
                INTO v_previous_payer_id
                FROM (SELECT p_parent_claim_no AS claim_no) search
                LEFT JOIN form_med_claim mc 
                    ON mc.claim_no = search.claim_no
                    AND mc.deleted IS NOT TRUE 
                    AND mc.archived IS NOT TRUE
                LEFT JOIN form_ncpdp nc 
                    ON nc.claim_no = search.claim_no
                    AND nc.deleted IS NOT TRUE 
                    AND nc.archived IS NOT TRUE
                LEFT JOIN form_med_claim_1500 mc1500 
                    ON mc1500.claim_no = search.claim_no
                    AND mc1500.deleted IS NOT TRUE 
                    AND mc1500.archived IS NOT TRUE;
            END IF;

            -- Apply payer settings to get substatus_id
            v_payer_settings := apply_mm_payer_settings(
                p_payer_id, 
                v_previous_payer_id, 
                p_insurance_id, 
                p_charge_lines
            );

            -- Extract substatus_id
            v_substatus_id := (v_payer_settings->>'substatus_id')::text;
        END;
    EXCEPTION WHEN OTHERS THEN
        -- If there's an error getting payer settings, continue without substatus
        v_substatus_id := NULL;
    END;

    SELECT COALESCE(mm_payer_id::text, mm_payer_name::text, organization::text), COALESCE(mm_payer_name::text, organization::text)
    INTO v_trading_partner_id, v_payer_name
    FROM form_payer
    WHERE id = p_payer_id;

    -- Build the header fields using individual assignments to avoid composite type issues
    v_result.uuid := gen_random_uuid()::text;
    v_result.parent_claim_no := p_parent_claim_no::text;
    v_result.claim_no := NULL::integer;  -- will be set later when saving
    v_result.usage_indicator := 'P'::text;
    v_result.service_date := EXTRACT(EPOCH FROM p_date_of_service)::integer;
    v_result.site_id := p_site_id::integer;
    v_result.patient_id := p_patient_id::integer;
    v_result.insurance_id := p_insurance_id::integer;
    v_result.payer_id := p_payer_id::integer;
    v_result.organization_name := v_receiver_segment.organization_name::text;
    v_result.dependent_required := CASE WHEN v_dependent IS NOT NULL THEN 'Yes' ELSE 'No' END::text;
    v_result.control_number := NULL::text;  -- placeholder until claim number assigned
    v_result.status := NULL::text;  -- will be set when claim is saved
    v_result.substatus_id := v_substatus_id::text;
    v_result.trading_partner_service_id := v_trading_partner_id::text;
    v_result.trading_partner_name := v_payer_name::text;
    v_result.expected := v_expected::numeric;
    v_result.billed := v_claim_information.claim_charge_amount::numeric;
    
    -- Wrap single segments in arrays
    v_result.receiver := CASE WHEN v_receiver_segment.payer_id IS NOT NULL THEN ARRAY[v_receiver_segment] ELSE NULL::mm_receiver_segment[] END;
    v_result.submitter := CASE WHEN v_submitter_segment.site_id IS NOT NULL THEN ARRAY[v_submitter_segment] ELSE NULL::mm_submitter_segment[] END;
    v_result.pay_to_address := CASE WHEN v_pay_to_address.address1 IS NOT NULL OR v_pay_to_address.city IS NOT NULL THEN ARRAY[v_pay_to_address] ELSE NULL::mm_address_info[] END;
    v_result.subscriber := CASE WHEN v_subscriber.patient_id IS NOT NULL THEN ARRAY[v_subscriber] ELSE NULL::mm_subscriber_segment[] END;
    v_result.dependent := CASE WHEN v_dependent.relationship_to_subscriber_code IS NOT NULL THEN ARRAY[v_dependent] ELSE NULL::mm_dependent_segment[] END;
    v_result.claim_information := CASE WHEN v_claim_information.claim_charge_amount IS NOT NULL THEN ARRAY[v_claim_information] ELSE NULL::mm_claim_info_segment[] END;
    v_result.providers := CASE WHEN v_providers.patient_id IS NOT NULL THEN ARRAY[v_providers] ELSE NULL::mm_providers_loop[] END;

    -- Log successful completion
    BEGIN
      PERFORM log_billing_function(
        'build_mm_claim'::tracked_function,
        v_params,
        jsonb_build_object(
          'insurance_id', p_insurance_id,
          'patient_id', p_patient_id,
          'parent_claim_no', p_parent_claim_no,
          'is_cob', v_is_cob
        ),
        NULL::text,
        clock_timestamp() - v_start_time
      );
    EXCEPTION WHEN OTHERS THEN
      -- Ignore logging errors
      NULL;
    END;

    RETURN v_result;
  EXCEPTION WHEN OTHERS THEN
    -- Log error
    v_error_message := SQLERRM;
    
    -- Log to billing error log
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        v_error_message,
        'Exception in build_mm_claim',
        'FUNCTION',
        current_schema(),
        'form_med_claim',
        v_params
    );

    -- Log to med claim function log
    BEGIN
      PERFORM log_billing_function(
        'build_mm_claim'::tracked_function,
        v_params::jsonb,
        NULL::jsonb,
        v_error_message::text,
        clock_timestamp() - v_start_time
      );
    EXCEPTION WHEN OTHERS THEN
      -- Ignore logging errors
      NULL;
    END;
    
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION build_mm_claim_from_ncpdp(
  p_invoice_id integer
) RETURNS mm_claim_record AS $BODY$
DECLARE
  v_insurance_id integer;
  v_payer_id integer;
  v_patient_id integer;
  v_site_id integer;
  v_date_of_service date;
  v_invoice_no text;
  v_charge_lines charge_line_with_split[];
  v_date_of_service_end date DEFAULT NULL;
  v_parent_claim_no text DEFAULT NULL;

  v_receiver_segment mm_receiver_segment;
  v_submitter_segment mm_submitter_segment;
  v_pay_to_address mm_address_info;
  v_subscriber mm_subscriber_segment;
  v_dependent mm_dependent_segment;
  v_claim_information mm_claim_info_segment;
  v_providers mm_providers_loop;
  v_start_time timestamp;
  v_execution_time interval;
  v_result mm_claim_record;
  v_error_message text;
  v_params jsonb;
  v_parent_payer_id integer;
  v_expected numeric;
  v_is_cob boolean := FALSE;
  v_substatus_id text;
  v_payer_name text;
  v_trading_partner_id text;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build minimal parameters JSON for logging
  v_params := jsonb_build_object(
    'invoice_id', p_invoice_id
  );

  BEGIN  -- Start exception block
    -- Log function call
    BEGIN
      PERFORM log_billing_function(
        'build_mm_claim_from_ncpdp'::tracked_function,
        v_params::jsonb,
        NULL::jsonb,
        NULL::text,
        clock_timestamp() - v_start_time
      );
    EXCEPTION WHEN OTHERS THEN
      -- Ignore logging errors
      NULL;
    END;


    IF p_invoice_id IS NULL THEN
      RAISE EXCEPTION 'Invoice ID cannot be null';
    END IF;

    SELECT 
      invoice.insurance_id::integer,
      invoice.payer_id::integer,
      invoice.patient_id::integer,
      invoice.site_id::integer,
      invoice.date_of_service::date,
      invoice.date_of_service_end::date,
      invoice_no::text
    INTO
      v_insurance_id,
      v_payer_id,
      v_patient_id,
      v_site_id,
      v_date_of_service,
      v_date_of_service_end,
      v_invoice_no
    FROM form_billing_invoice invoice
    WHERE invoice.id = p_invoice_id;

    -- Validate required parameters
    IF v_insurance_id IS NULL THEN
        RAISE EXCEPTION 'Insurance ID cannot be null';
    END IF;
    
    IF v_payer_id IS NULL THEN
        RAISE EXCEPTION 'Payer ID cannot be null';
    END IF;

    IF v_site_id IS NULL THEN
        RAISE EXCEPTION 'Site ID cannot be null';
    END IF;

    IF v_patient_id IS NULL THEN
        RAISE EXCEPTION 'Patient ID cannot be null';
    END IF;


    WITH existing_charge_lines AS (
      SELECT
        lcl.id::integer as id,
        lcl.calc_invoice_split_no::text as calc_invoice_split_no,
        lcl.site_id::integer as site_id,
        lcl.patient_id::integer as patient_id,
        lcl.insurance_id::integer as insurance_id,
        lcl.payer_id::integer as payer_id,
        lcl.inventory_id::integer as inventory_id,
        lcl.shared_contract_id::integer as shared_contract_id,
        lcl.is_dirty::text as is_dirty,
        lcl.charge_no::text as charge_no,
        lcl.parent_charge_no::text as parent_charge_no,
        lcl.master_charge_no::text as master_charge_no,
        lcl.compound_no::text as compound_no,
        lcl.ticket_no::text as ticket_no,
        lcl.ticket_item_no::text as ticket_item_no,
        lcl.rental_id::integer as rental_id,
        lcl.order_rx_id::integer as order_rx_id,
        lcl.is_primary_drug::text as is_primary_drug,
        lcl.is_primary_drug_ncpdp::text as is_primary_drug_ncpdp,
        lcl.rx_no::text as rx_no,
        lcl.inventory_type_filter::text[] as inventory_type_filter,
        lcl.inventory_type::text as inventory_type,
        lcl.revenue_code_id::text as revenue_code_id,
        lcl.hcpc_code::text as hcpc_code,
        lcl.ndc::text as ndc,
        lcl.formatted_ndc::text as formatted_ndc,
        lcl.gcn_seqno::text as gcn_seqno,
        lcl.charge_quantity::numeric as charge_quantity,
        lcl.charge_quantity_ea::numeric as charge_quantity_ea,
        lcl.hcpc_quantity::numeric as hcpc_quantity,
        lcl.hcpc_unit::text as hcpc_unit,
        lcl.metric_quantity::numeric as metric_quantity,
        lcl.charge_unit::text as charge_unit,
        lcl.bill_quantity::numeric as bill_quantity,
        lcl.metric_unit_each::numeric as metric_unit_each,
        lcl.billing_unit_id::text as billing_unit_id,
        'mm'::text as billing_method_id,
        lcl.pricing_source::text as pricing_source,
        lcl.billed::numeric as billed,
        lcl.calc_billed_ea::numeric as calc_billed_ea,
        lcl.expected::numeric as expected,
        lcl.calc_expected_ea::numeric as calc_expected_ea,
        lcl.list_price::numeric as list_price,
        lcl.calc_list_ea::numeric as calc_list_ea,
        lcl.total_cost::numeric as total_cost,
        lcl.gross_amount_due::numeric as gross_amount_due,
        lcl.incv_amt_sub::numeric as incv_amt_sub,
        lcl.copay::numeric as copay,
        lcl.calc_cost_ea::numeric as calc_cost_ea,
        lcl.total_adjusted::numeric as total_adjusted,
        lcl.total_balance_due::numeric as total_balance_due,
        lcl.dispense_fee::numeric as dispense_fee,
        lcl.pt_pd_amt_sub::numeric as pt_pd_amt_sub,
        lcl.encounter_id::integer as encounter_id,
        lcl.description::text as description,
        lcl.upc::text as upc,
        lcl.upin::text as upin,
        lcl.cost_basis::text as cost_basis,
        lcl.awp_price::numeric as awp_price,
        lcl.modifier_1::text as modifier_1,
        lcl.modifier_2::text as modifier_2,
        lcl.modifier_3::text as modifier_3,
        lcl.modifier_4::text as modifier_4,
        lcl.rental_type::text as rental_type,
        lcl.frequency_code::text as frequency_code,
        lcl.fill_number::integer as fill_number,
        lcl.paid::numeric as paid,
        lcl.date_of_service::date as date_of_service,
        lcl.date_of_service_end::date as date_of_service_end
      FROM form_ledger_charge_line lcl
      WHERE lcl.invoice_no::text = v_invoice_no
    )
    SELECT array_agg((lcl.id::integer,
        lcl.calc_invoice_split_no,
        lcl.site_id,
        lcl.patient_id,
        lcl.insurance_id,
        lcl.payer_id,
        lcl.inventory_id,
        lcl.shared_contract_id,
        lcl.is_dirty,
        lcl.charge_no,
        lcl.parent_charge_no,
        lcl.master_charge_no,
        lcl.compound_no,
        lcl.ticket_no,
        lcl.ticket_item_no,
        lcl.rental_id,
        lcl.order_rx_id,
        lcl.is_primary_drug,
        lcl.is_primary_drug_ncpdp,
        lcl.rx_no,
        lcl.inventory_type_filter,
        lcl.inventory_type,
        lcl.revenue_code_id,
        lcl.hcpc_code,
        lcl.ndc,
        lcl.formatted_ndc,
        lcl.gcn_seqno,
        lcl.charge_quantity,
        lcl.charge_quantity_ea,
        lcl.hcpc_quantity,
        lcl.hcpc_unit,
        lcl.metric_quantity,
        lcl.charge_unit,
        lcl.bill_quantity,
        lcl.metric_unit_each,
        lcl.billing_unit_id,
        lcl.billing_method_id,
        lcl.pricing_source,
        lcl.billed,
        lcl.calc_billed_ea,
        lcl.expected,
        lcl.calc_expected_ea,
        lcl.list_price,
        lcl.calc_list_ea,
        lcl.total_cost,
        lcl.gross_amount_due,
        lcl.incv_amt_sub,
        lcl.copay,
        lcl.calc_cost_ea,
        lcl.total_adjusted,
        lcl.total_balance_due,
        lcl.dispense_fee,
        lcl.pt_pd_amt_sub,
        lcl.encounter_id,
        lcl.description,
        lcl.upc,
        lcl.upin,
        lcl.cost_basis,
        lcl.awp_price,
        lcl.modifier_1,
        lcl.modifier_2,
        lcl.modifier_3,
        lcl.modifier_4,
        lcl.rental_type,
        lcl.frequency_code,
        lcl.fill_number,
        lcl.paid,
        lcl.date_of_service,
        lcl.date_of_service_end)::charge_line_with_split)::charge_line_with_split[] 
    FROM existing_charge_lines lcl
    INTO v_charge_lines;

    IF v_charge_lines IS NOT NULL AND array_length(v_charge_lines, 1) IS NOT NULL THEN
      FOR i IN 1..array_length(v_charge_lines, 1) LOOP
        IF v_charge_lines[i] IS NOT NULL AND v_charge_lines[i].expected IS NOT NULL THEN
          v_expected := v_expected + COALESCE(v_charge_lines[i].expected, 0)::numeric;
        END IF;
      END LOOP;
    END IF;

    RAISE LOG 'build_mm_claim_from_ncpdp: Building segments for payer_id=%, patient_id=%, insurance_id=%', v_payer_id, v_patient_id, v_insurance_id;

    v_receiver_segment := build_mm_receiver_segment(v_payer_id, v_patient_id, v_insurance_id, v_parent_claim_no); -- Build receiver segment (payer information)
    RAISE LOG 'build_mm_claim_from_ncpdp: Receiver segment result: %', v_receiver_segment IS NOT NULL;
    
    v_submitter_segment := build_mm_submitter_segment(v_site_id); -- Build submitter segment (site information)
    RAISE LOG 'build_mm_claim_from_ncpdp: Submitter segment result: %', v_submitter_segment IS NOT NULL;
    
    v_pay_to_address := build_mm_pay_to_segment(v_site_id); -- Build pay-to address segment (billing address)
    RAISE LOG 'build_mm_claim_from_ncpdp: Pay-to address result: %', v_pay_to_address IS NOT NULL;
    
    v_subscriber := build_mm_subscriber_segment(v_patient_id, v_payer_id, v_insurance_id, v_parent_claim_no); -- Build subscriber segment (patient/insurance information)
    RAISE LOG 'build_mm_claim_from_ncpdp: Subscriber segment result: %', v_subscriber IS NOT NULL;
    
    v_dependent := build_mm_dependent_segment(v_patient_id, v_insurance_id); -- Build dependent segment (only if patient is not subscriber)
    RAISE LOG 'build_mm_claim_from_ncpdp: Dependent segment result: %', v_dependent IS NOT NULL;
    
    v_claim_information := build_mm_claim_info_segment(v_site_id, v_patient_id, v_insurance_id, v_payer_id, v_charge_lines, v_parent_claim_no);     -- Build claim information segment (the main body)
    RAISE LOG 'build_mm_claim_from_ncpdp: Claim information segment result: %', v_claim_information IS NOT NULL;
    
    v_providers := build_mm_provider_loop(v_patient_id, v_charge_lines);     -- Build providers loop
    RAISE LOG 'build_mm_claim_from_ncpdp: Providers loop result: %', v_providers IS NOT NULL;

    -- Extract substatus_id from claim information if it was set by payer settings
    BEGIN
        DECLARE
            v_payer_settings jsonb;
            v_previous_payer_id integer;
        BEGIN
            -- Get previous payer ID for COB claims
            IF v_is_cob THEN
                SELECT COALESCE(mc.payer_id, nc.payer_id, mc1500.payer_id)
                INTO v_previous_payer_id
                FROM (SELECT p_parent_claim_no AS claim_no) search
                LEFT JOIN form_med_claim mc 
                    ON mc.claim_no = search.claim_no
                    AND mc.deleted IS NOT TRUE 
                    AND mc.archived IS NOT TRUE
                LEFT JOIN form_ncpdp nc 
                    ON nc.claim_no = search.claim_no
                    AND nc.deleted IS NOT TRUE 
                    AND nc.archived IS NOT TRUE
                LEFT JOIN form_med_claim_1500 mc1500 
                    ON mc1500.claim_no = search.claim_no
                    AND mc1500.deleted IS NOT TRUE 
                    AND mc1500.archived IS NOT TRUE;
            END IF;

            -- Apply payer settings to get substatus_id
            v_payer_settings := apply_mm_payer_settings(
                p_payer_id, 
                v_previous_payer_id, 
                p_insurance_id, 
                p_charge_lines
            );

            -- Extract substatus_id
            v_substatus_id := (v_payer_settings->>'substatus_id')::text;
        END;
    EXCEPTION WHEN OTHERS THEN
        -- If there's an error getting payer settings, continue without substatus
        v_substatus_id := NULL;
    END;

    SELECT mm_payer_id::text, COALESCE(mm_payer_name::text, organization::text)
    INTO v_trading_partner_id, v_payer_name
    FROM form_payer
    WHERE id = p_payer_id;

    -- Build the header fields using individual assignments to avoid composite type issues
    v_result.uuid := gen_random_uuid()::text;
    v_result.parent_claim_no := p_parent_claim_no::text;
    v_result.claim_no := NULL::integer;  -- will be set later when saving
    v_result.usage_indicator := 'P'::text;
    v_result.service_date := EXTRACT(EPOCH FROM p_date_of_service)::integer;
    v_result.site_id := p_site_id::integer;
    v_result.patient_id := p_patient_id::integer;
    v_result.insurance_id := p_insurance_id::integer;
    v_result.payer_id := p_payer_id::integer;
    v_result.organization_name := v_receiver_segment.organization_name::text;
    v_result.dependent_required := CASE WHEN v_dependent IS NOT NULL THEN 'Yes' ELSE 'No' END::text;
    v_result.control_number := NULL::text;  -- placeholder until claim number assigned
    v_result.status := NULL::text;  -- will be set when claim is saved
    v_result.substatus_id := v_substatus_id::text;
    v_result.trading_partner_service_id := v_trading_partner_id::text;
    v_result.trading_partner_name := v_payer_name::text;
    v_result.expected := v_expected::numeric;
    v_result.billed := v_claim_information.claim_charge_amount::numeric;
    
    -- Wrap single segments in arrays
    v_result.receiver := CASE WHEN v_receiver_segment.payer_id IS NOT NULL THEN ARRAY[v_receiver_segment] ELSE NULL::mm_receiver_segment[] END;
    v_result.submitter := CASE WHEN v_submitter_segment.site_id IS NOT NULL THEN ARRAY[v_submitter_segment] ELSE NULL::mm_submitter_segment[] END;
    v_result.pay_to_address := CASE WHEN v_pay_to_address.address1 IS NOT NULL OR v_pay_to_address.city IS NOT NULL THEN ARRAY[v_pay_to_address] ELSE NULL::mm_address_info[] END;
    v_result.subscriber := CASE WHEN v_subscriber.patient_id IS NOT NULL THEN ARRAY[v_subscriber] ELSE NULL::mm_subscriber_segment[] END;
    v_result.dependent := CASE WHEN v_dependent.relationship_to_subscriber_code IS NOT NULL THEN ARRAY[v_dependent] ELSE NULL::mm_dependent_segment[] END;
    v_result.claim_information := CASE WHEN v_claim_information.claim_charge_amount IS NOT NULL THEN ARRAY[v_claim_information] ELSE NULL::mm_claim_info_segment[] END;
    v_result.providers := CASE WHEN v_providers.patient_id IS NOT NULL THEN ARRAY[v_providers] ELSE NULL::mm_providers_loop[] END;

    -- Log successful completion
    BEGIN
      PERFORM log_billing_function(
        'build_mm_claim'::tracked_function,
        v_params,
        jsonb_build_object(
          'insurance_id', p_insurance_id,
          'patient_id', p_patient_id,
          'parent_claim_no', p_parent_claim_no,
          'is_cob', v_is_cob
        ),
        NULL::text,
        clock_timestamp() - v_start_time
      );
    EXCEPTION WHEN OTHERS THEN
      -- Ignore logging errors
      NULL;
    END;

    RETURN v_result;
  EXCEPTION WHEN OTHERS THEN
    -- Log error
    v_error_message := SQLERRM;
    
    -- Log to billing error log
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        v_error_message,
        'Exception in build_mm_claim',
        'FUNCTION',
        current_schema(),
        'form_med_claim',
        v_params
    );

    -- Log to med claim function log
    BEGIN
      PERFORM log_billing_function(
        'build_mm_claim'::tracked_function,
        v_params::jsonb,
        NULL::jsonb,
        v_error_message::text,
        clock_timestamp() - v_start_time
      );
    EXCEPTION WHEN OTHERS THEN
      -- Ignore logging errors
      NULL;
    END;
    
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;