

/*

Need to run these as admin on the database
DROP EVENT TRIGGER IF EXISTS function_validator_trigger;

CREATE OR REPLACE FUNCTION function_validator_event_trigger() RETURNS event_trigger AS $$
DECLARE
  obj RECORD;
  function_schema TEXT;
  function_name TEXT;
  function_code TEXT;
  function_body TEXT;
  temp_pos INTEGER;
  -- Column validation variables
  col_ref RECORD;
  tbl_name TEXT;
  col_name TEXT;
  schema_name TEXT;
  -- Function call validation variables
  func_call_record RECORD;
  func_info RECORD;
  arg_list TEXT;
  args TEXT[];
  arg_count INTEGER;
  called_func_schema TEXT;
  called_func_name TEXT;
  -- Variable validation variables
  variable RECORD;
  declared_variables TEXT[];
  referenced_variables TEXT[];
  undeclared_var TEXT;
  table_references RECORD;
  dollar_delim text;
BEGIN
  -- Set dollar delimiter for extracting function bodies
  dollar_delim := E'\\$\\$'; -- Escaped dollar sign for use in position()

  -- Only run validation for CREATE FUNCTION events
  IF TG_TAG != 'CREATE FUNCTION' THEN
    RETURN;
  END IF;

  BEGIN
    -- Attempt to get DDL command info, but exit gracefully if it fails
    PERFORM 1 FROM pg_event_trigger_ddl_commands() LIMIT 1;
  EXCEPTION
    WHEN OTHERS THEN
      -- If we can't access pg_event_trigger_ddl_commands(), just exit silently
      -- This happens during script processing but not during actual trigger execution
      RETURN;
  END;

  -- Use a BEGIN/EXCEPTION block to protect the pg_event_trigger_ddl_commands() call
  BEGIN
    -- Process each DDL command
    FOR obj IN SELECT * FROM pg_event_trigger_ddl_commands() LOOP
      -- Skip validator functions to prevent recursion
      IF obj.object_identity LIKE '%validator%' OR
         obj.schema_name = 'pg_catalog' OR 
         obj.schema_name LIKE 'pg_%' THEN
        CONTINUE;
      END IF;
      
      function_schema := obj.schema_name;
      function_name := obj.object_identity;

      -- Get function definition
      SELECT pg_get_functiondef(obj.objid) INTO function_code;
      
      -- Extract function body
      temp_pos := position(dollar_delim in function_code);
      IF temp_pos > 0 THEN
        function_body := substring(function_code FROM temp_pos + 2);
        temp_pos := position(dollar_delim in function_body);
        IF temp_pos > 0 THEN
          function_body := substring(function_body FROM 1 FOR temp_pos - 1);
        END IF;
      END IF;
      
      -- PART 1: Column reference validation
      FOR col_ref IN
        SELECT DISTINCT
          trim(matches[1]) AS ref_table,
          trim(matches[2]) AS ref_column
        FROM regexp_matches(function_body, '([a-zA-Z0-9_]+)\.([a-zA-Z0-9_]+)', 'g') AS matches
      LOOP
        -- Skip special references
        CONTINUE WHEN col_ref.ref_table IN ('NEW', 'OLD', 'TG');
        
        -- Use current schema
        schema_name := function_schema;
        tbl_name := col_ref.ref_table;
        col_name := col_ref.ref_column;
        
        -- Check if table exists
        IF EXISTS (
          SELECT 1 FROM information_schema.tables
          WHERE table_schema = schema_name
          AND table_name = tbl_name
        ) THEN
          -- Check if column exists
          IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_schema = schema_name
            AND table_name = tbl_name
            AND column_name = col_name
          ) THEN
            RAISE NOTICE 'Table %.% does not have column %',
              schema_name, tbl_name, col_name;
          END IF;
        END IF;
      END LOOP;
      
      -- PART 2: Function call validation
      FOR func_call_record IN 
        SELECT 
          match[1] AS func_name, 
          match[2] AS args
        FROM regexp_matches(
          function_body, 
          '([a-zA-Z0-9_\.]+)\(([^\(\)]*)\)', 
          'g'
        ) AS matches(match)
      LOOP
        -- Determine if the name has a schema qualifier
        IF position('.' in func_call_record.func_name) > 0 THEN
          called_func_schema := split_part(func_call_record.func_name, '.', 1);
          called_func_name := split_part(func_call_record.func_name, '.', 2);
        ELSE
          called_func_schema := function_schema;
          called_func_name := func_call_record.func_name;
        END IF;
        
        arg_list := func_call_record.args;
        
        -- Skip language constructs that look like functions
        CONTINUE WHEN called_func_name IN (
          'IF', 'WHILE', 'LOOP', 'FOR', 'CASE', 'BEGIN', 'END', 'DECLARE',
          'ARRAY', 'ROW', 'ROWS', 'EXISTS', 'EXTRACT', 'CAST', 'COALESCE', 'NULLIF'
        );
        
        -- Count arguments
        IF length(trim(arg_list)) = 0 THEN
          arg_count := 0;
          args := ARRAY[]::text[];
        ELSE
          args := regexp_split_to_array(arg_list, ',');
          arg_count := array_length(args, 1);
        END IF;
        
        -- Look up the function in pg_proc
        SELECT 
          p.proname,
          p.pronargs,
          pg_get_function_arguments(p.oid) as arg_types
        INTO func_info
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = called_func_schema
        AND p.proname = called_func_name
        LIMIT 1;
        
        -- Validate the function exists and has correct argument count
        IF FOUND THEN
          IF func_info.pronargs != arg_count THEN
            RAISE NOTICE 'Function %.% calls %.% with % arguments, but it requires % arguments (signature: %)', 
                  function_schema, function_name, 
                  called_func_schema, called_func_name, 
                  arg_count, func_info.pronargs, func_info.arg_types;
          END IF;
        ELSE
          -- Check if the function exists at all
          SELECT p.proname
          INTO func_info
          FROM pg_proc p
          JOIN pg_namespace n ON p.pronamespace = n.oid
          WHERE n.nspname = called_func_schema
          AND p.proname = called_func_name
          LIMIT 1;
          
          IF NOT FOUND THEN
            RAISE NOTICE 'Function %.% calls non-existent function %.%', 
                  function_schema, function_name, 
                  called_func_schema, called_func_name;
          END IF;
        END IF;
      END LOOP;
      
      -- PART 3: Variable validation (for PLPGSQL functions)
      IF position('LANGUAGE plpgsql' in function_code) > 0 THEN
        declared_variables := ARRAY[]::text[];
        FOR variable IN 
          SELECT trim(both ' \t' from split_part(declaration, ' ', 1)) as var_name
          FROM regexp_split_to_table(function_body, E';\n|\n') as declaration
          WHERE declaration ~* '^\s*(DECLARE|[A-Za-z0-9_]+)\s+[A-Za-z0-9_]+\s+(ALIAS\s+FOR\s+|IS\s+|IN\s+OUT\s+|OUT\s+|INOUT\s+|IN\s+)?[A-Za-z0-9_%.()]+(\s+|$|\s*:=)'
        LOOP
          IF variable.var_name != 'DECLARE' AND variable.var_name != 'BEGIN' AND variable.var_name != 'END' THEN
            declared_variables := declared_variables || variable.var_name;
          END IF;
        END LOOP;
        
        referenced_variables := ARRAY[]::text[];
        FOR variable IN 
          SELECT DISTINCT trim(both ' \t' from match) as var_name
          FROM regexp_matches(function_body, '[^a-zA-Z0-9_](NEW|OLD|[a-zA-Z][a-zA-Z0-9_]*)', 'g') as matches(match)
        LOOP
          referenced_variables := referenced_variables || substring(variable.var_name from 2);
        END LOOP;
        
        FOREACH undeclared_var IN ARRAY referenced_variables
        LOOP
          IF undeclared_var NOT IN ('NEW', 'OLD', 'FOUND', 'SQLSTATE', 'TG_NAME', 'TG_WHEN', 'TG_LEVEL', 'TG_OP', 'TG_RELID', 'TG_TABLE_NAME', 'TG_TABLE_SCHEMA', 'TG_RELNAME', 'TG_NARGS', 'TG_ARGV') 
             AND undeclared_var !~ '^[0-9]+$'
             AND undeclared_var NOT IN (SELECT unnest(declared_variables))
             AND undeclared_var !~ '^\$[0-9]+$'
          THEN
            IF NOT EXISTS (
              SELECT 1
              FROM regexp_matches(function_code, 'CREATE.*FUNCTION.*\((.*)\)', 'i') as matches(args)
              WHERE matches.args ~* ('(^|,|\s)' || undeclared_var || '(\s|$|,)')
            ) THEN
              RAISE NOTICE 'Function %.% references undeclared variable: %', 
                    function_schema, function_name, undeclared_var;
            END IF;
          END IF;
        END LOOP;
        
        -- Table reference validation
        FOR table_references IN 
          SELECT DISTINCT match[1] as schema_name, match[2] as table_name
          FROM regexp_matches(function_body, 'FROM\s+([a-zA-Z0-9_]*\.)?([a-zA-Z0-9_]+)', 'ig') as matches(match)
        LOOP
          IF table_references.schema_name IS NULL THEN
            IF NOT EXISTS (
              SELECT 1 FROM information_schema.tables 
              WHERE table_name = table_references.table_name 
              AND table_schema = function_schema
            ) THEN
              RAISE NOTICE 'Function %.% references non-existent table: %.%', 
                    function_schema, function_name, function_schema, table_references.table_name;
            END IF;
          ELSE
            IF NOT EXISTS (
              SELECT 1 FROM information_schema.tables 
              WHERE table_name = table_references.table_name 
              AND table_schema = table_references.schema_name
            ) THEN
              RAISE NOTICE 'Function %.% references non-existent table: %.%', 
                    function_schema, function_name, table_references.schema_name, table_references.table_name;
            END IF;
          END IF;
        END LOOP;
      END IF;
    END LOOP;
  EXCEPTION
    WHEN OTHERS THEN
      -- Use NOTICE instead of EXCEPTION to prevent function creation from failing
      RAISE NOTICE 'Error during function validation: %', SQLERRM;
  END;
EXCEPTION
  WHEN others THEN
    -- Use NOTICE to just warn instead of preventing function creation
    RAISE NOTICE 'Function validation failed: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Create a direct event trigger
CREATE EVENT TRIGGER function_validator_trigger ON ddl_command_start
WHEN TAG IN ('CREATE FUNCTION')
EXECUTE FUNCTION function_validator_event_trigger();
*/

-- Helper function to demonstrate validation with fixed transaction handling
CREATE OR REPLACE FUNCTION demo_validate_function(
    func_definition text,
    strict_validation boolean DEFAULT false
) RETURNS text AS $func$
DECLARE
    validation_result text;
BEGIN
    BEGIN
        EXECUTE 'SAVEPOINT temp_function';
        
        EXECUTE func_definition;
        
        validation_result := 'Function validated successfully';
        
        EXECUTE 'ROLLBACK TO temp_function';
    EXCEPTION
        WHEN others THEN
            validation_result := 'Validation failed: ' || SQLERRM;
            
            IF strict_validation THEN
                BEGIN
                    EXECUTE 'ROLLBACK TO temp_function';
                EXCEPTION 
                    WHEN others THEN
                        NULL;
                END;
                RAISE EXCEPTION '%', validation_result;
            ELSE
                BEGIN
                    EXECUTE 'ROLLBACK TO temp_function';
                EXCEPTION 
                    WHEN others THEN
                        NULL;
                END;
            END IF;
    END;
    
    RETURN validation_result;
END;
$func$ LANGUAGE plpgsql;

-- Helper function to disable/enable the validators temporarily if needed
CREATE OR REPLACE FUNCTION toggle_validators(enable boolean) 
RETURNS void
AS $func$
BEGIN
    BEGIN
        IF enable THEN
            ALTER EVENT TRIGGER function_validator_trigger ENABLE;
            RAISE NOTICE 'Variable validator enabled';
        ELSE
            ALTER EVENT TRIGGER function_validator_trigger DISABLE;
            RAISE NOTICE 'Variable validator disabled';
        END IF;
    EXCEPTION
        WHEN undefined_object THEN
            RAISE NOTICE 'Function validator trigger not found';
    END;
    
    BEGIN
        IF enable THEN
            ALTER EVENT TRIGGER column_validator_trigger ENABLE;
            RAISE NOTICE 'Column validator enabled';
        ELSE
            ALTER EVENT TRIGGER column_validator_trigger DISABLE;
            RAISE NOTICE 'Column validator disabled';
        END IF;
    EXCEPTION
        WHEN undefined_object THEN
            RAISE NOTICE 'Column validator trigger not found';
    END;
    
    BEGIN
        IF enable THEN
            ALTER EVENT TRIGGER function_call_validator_trigger ENABLE;
            RAISE NOTICE 'Function call validator enabled';
        ELSE
            ALTER EVENT TRIGGER function_call_validator_trigger DISABLE;
            RAISE NOTICE 'Function call validator disabled';
        END IF;
    EXCEPTION
        WHEN undefined_object THEN
            RAISE NOTICE 'Function call validator trigger not found';
    END;
END;
$func$ LANGUAGE plpgsql;

-- Function to test a specific function call signature
CREATE OR REPLACE FUNCTION check_function_call(
    p_schema_name text,
    p_function_name text,
    p_arg_count integer
) RETURNS table(
    func_exists boolean,
    correct_arg_count boolean,
    actual_arg_count integer,
    signature text
) AS $func$
BEGIN
    RETURN QUERY
    SELECT 
        true as func_exists,
        p.pronargs = p_arg_count as correct_arg_count,
        p.pronargs as actual_arg_count,
        pg_get_function_arguments(p.oid) as signature
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = p_schema_name
    AND p.proname = p_function_name
    LIMIT 1;
    
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT 
            false as func_exists,
            false as correct_arg_count,
            0 as actual_arg_count,
            'Function not found' as signature;
    END IF;
END;
$func$ LANGUAGE plpgsql;
