-- Function to handle auto-discontinuation of patients
DO $$ BEGIN
  PERFORM drop_all_function_signatures('auto_dc_patient_func');
END $$;
CREATE OR REPLACE FUNCTION auto_dc_patient_func()
RETURNS TRIGGER AS $$
/**
 * Automatically discontinues a patient when they have no more active or pending orders
 * and their auto_dc_pt flag is set to 'Yes'
 * 
 * @param {trigger} - Postgres trigger context
 * @returns {trigger} - Returns the trigger for completion
 */
BEGIN
    -- Check if the status was changed to "2" or "3" or if the record was archived or deleted
    IF (TG_OP = 'UPDATE' AND (NEW.status_id IN ('2', '3') OR NEW.archived = TRUE OR NEW.deleted = TRUE)) OR 
       (TG_OP = 'INSERT' AND (NEW.status_id IN ('2', '3') OR NEW.archived = TRUE OR NEW.deleted = TRUE)) THEN
        
        -- Check if the patient has auto_dc_pt set to 'Yes'
        IF EXISTS (
            SELECT 1 
            FROM form_patient 
            WHERE id = NEW.patient_id 
            AND COALESCE(auto_dc_pt, 'No') = 'Yes'
        ) THEN
            
            -- Check if there are any active or pending orders for this patient
            IF NOT EXISTS (
                -- Check form_careplan_orderp_item
                SELECT 1 
                FROM form_careplan_orderp_item 
                WHERE patient_id = NEW.patient_id 
                AND status_id IN ('1', '4', '5')
                AND COALESCE(archived, FALSE) = FALSE
                AND COALESCE(deleted, FALSE) = FALSE
                
                UNION ALL
                
                -- Check form_careplan_order_item
                SELECT 1 
                FROM form_careplan_order_item 
                WHERE patient_id = NEW.patient_id 
                AND status_id IN ('1', '4', '5')
                AND COALESCE(archived, FALSE) = FALSE
                AND COALESCE(deleted, FALSE) = FALSE
            ) THEN
                -- No active or pending orders, update patient status to discontinued (6)
                UPDATE form_patient 
                SET status_id = '6' 
                WHERE id = NEW.patient_id;
            END IF;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for form_careplan_orderp_item
DROP TRIGGER IF EXISTS auto_dc_patient_orderp_item_trigger ON form_careplan_orderp_item;
CREATE TRIGGER auto_dc_patient_orderp_item_trigger
AFTER INSERT OR UPDATE OF status_id, archived, deleted
ON form_careplan_orderp_item
FOR EACH ROW
EXECUTE FUNCTION auto_dc_patient_func();

-- Create trigger for form_careplan_order_item
DROP TRIGGER IF EXISTS auto_dc_patient_order_item_trigger ON form_careplan_order_item;
CREATE TRIGGER auto_dc_patient_order_item_trigger
AFTER INSERT OR UPDATE OF status_id, archived, deleted
ON form_careplan_order_item
FOR EACH ROW
EXECUTE FUNCTION auto_dc_patient_func();
