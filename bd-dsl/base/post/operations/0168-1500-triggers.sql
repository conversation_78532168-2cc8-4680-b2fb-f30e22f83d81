-- CMS-1500 Medical Claim Triggers
-- Handles triggers specific to CMS-1500 (paper) medical claims

-- 1. Trigger to sync service dates for rental charge lines on CMS-1500 claims
CREATE OR REPLACE FUNCTION sync_rental_service_dates_cms1500()
RETURNS TRIGGER AS $$
DECLARE
    v_claim_no TEXT;
    v_claim_id INTEGER;
    v_payer_id INTEGER;
BEGIN
    -- Only process if billing_method_id is 'cms1500'
    IF NEW.billing_method_id != 'cms1500' THEN
        RETURN NEW;
    END IF;
    
    -- Only process rental charge lines (rental_type is set OR rental_id is set)
    IF NEW.rental_type IS NULL AND NEW.rental_id IS NULL THEN
        RETURN NEW;
    END IF;
    
    -- Only process if service dates changed
    IF TG_OP = 'UPDATE' AND 
       OLD.date_of_service IS NOT DISTINCT FROM NEW.date_of_service AND
       OLD.date_of_service_end IS NOT DISTINCT FROM NEW.date_of_service_end THEN
        RETURN NEW;
    END IF;
    
    -- Get claim information for CMS-1500 claim
    SELECT mc.id, mc.claim_no, mc.payer_id INTO v_claim_id, v_claim_no, v_payer_id
    FROM form_med_claim_1500 mc
    WHERE mc.invoice_no = NEW.invoice_no
    AND mc.deleted IS NOT TRUE
    AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    IF v_claim_id IS NOT NULL THEN
        -- Update CMS-1500 service line dates
        UPDATE form_med_claim_1500_sl sl
        SET service_date = NEW.date_of_service,
            service_date_end = COALESCE(NEW.date_of_service_end, NEW.date_of_service),
            updated_on = NEW.updated_on,
            updated_by = NEW.updated_by
        WHERE sl.id IN (
            SELECT sf_sl.form_med_claim_1500_sl_fk 
            FROM sf_form_med_claim_1500_to_med_claim_1500_sl sf_sl
            WHERE sf_sl.form_med_claim_1500_fk = v_claim_id
            AND sf_sl.delete IS NOT TRUE
            AND sf_sl.archive IS NOT TRUE
        )
        AND sl.charge_no = NEW.charge_no
        AND sl.deleted IS NOT TRUE
        AND sl.archived IS NOT TRUE;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for CMS-1500 rental service dates
DROP TRIGGER IF EXISTS tr_sync_rental_service_dates_cms1500 ON form_ledger_charge_line;

CREATE TRIGGER tr_sync_rental_service_dates_cms1500
    AFTER UPDATE OF date_of_service, date_of_service_end ON form_ledger_charge_line
    FOR EACH ROW
    WHEN (NEW.rental_type IS NOT NULL OR NEW.rental_id IS NOT NULL)
    EXECUTE FUNCTION sync_rental_service_dates_cms1500();

-- 2. Trigger to keep diagnoses in sync between CMS-1500 service lines and diagnosis entries
CREATE OR REPLACE FUNCTION sync_cms1500_service_line_diagnoses()
RETURNS TRIGGER AS $$
DECLARE
    v_claim_id INTEGER;
    v_dx_exists BOOLEAN;
    v_dx_id INTEGER;
    v_dx_code TEXT;
    v_patient_id INTEGER;
    v_dx_fields TEXT[] := ARRAY['dx_id_1', 'dx_id_2', 'dx_id_3', 'dx_id_4'];
    v_field TEXT;
    v_old_dx_id INTEGER;
    v_new_dx_id INTEGER;
    v_dx_used BOOLEAN;
    v_payer_id INTEGER;
    v_use_formatted_diagnosis TEXT;
BEGIN
    -- Get the claim this service line belongs to
    SELECT mc.id, mc.patient_id, mc.payer_id 
    INTO v_claim_id, v_patient_id, v_payer_id
    FROM form_med_claim_1500 mc
    INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_sl sf_sl
        ON sf_sl.form_med_claim_1500_fk = mc.id
        AND sf_sl.form_med_claim_1500_sl_fk = NEW.id
        AND sf_sl.delete IS NOT TRUE
        AND sf_sl.archive IS NOT TRUE
    WHERE mc.deleted IS NOT TRUE
    AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    IF v_claim_id IS NULL THEN
        RETURN NEW;
    END IF;
    
    -- Get payer setting for diagnosis code formatting
    SELECT COALESCE(p.cms_14, 'No')::text
    INTO v_use_formatted_diagnosis
    FROM form_payer p
    WHERE p.id = v_payer_id
    AND p.deleted IS NOT TRUE 
    AND p.archived IS NOT TRUE;
    
    -- Check each diagnosis field
    FOREACH v_field IN ARRAY v_dx_fields LOOP
        -- Get old and new values dynamically
        EXECUTE format('SELECT ($1).%I::INTEGER', v_field) INTO v_old_dx_id USING OLD;
        EXECUTE format('SELECT ($1).%I::INTEGER', v_field) INTO v_new_dx_id USING NEW;
        
        -- Skip if no change
        IF v_old_dx_id IS NOT DISTINCT FROM v_new_dx_id THEN
            CONTINUE;
        END IF;
        
        -- If new diagnosis added, ensure it exists in med_claim_1500_dx
        IF v_new_dx_id IS NOT NULL THEN
            -- Check if this diagnosis already exists for the claim
            SELECT EXISTS(
                SELECT 1
                FROM form_med_claim_1500_dx dx
                INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_dx sf_dx
                    ON sf_dx.form_med_claim_1500_dx_fk = dx.id
                    AND sf_dx.form_med_claim_1500_fk = v_claim_id
                    AND sf_dx.delete IS NOT TRUE
                    AND sf_dx.archive IS NOT TRUE
                WHERE dx.dx_id = v_new_dx_id
                AND dx.deleted IS NOT TRUE
                AND dx.archived IS NOT TRUE
            ) INTO v_dx_exists;
            
            IF NOT v_dx_exists THEN
                -- Get diagnosis code from patient diagnosis
                SELECT CASE 
                    WHEN v_use_formatted_diagnosis = 'Yes' THEN COALESCE(ld.icd_code, ld.code)
                    ELSE ld.code
                END
                INTO v_dx_code
                FROM form_patient_diagnosis pd
                INNER JOIN form_list_diagnosis ld ON ld.id = pd.dx_id::integer
                WHERE pd.id = v_new_dx_id
                AND pd.deleted IS NOT TRUE
                AND pd.archived IS NOT TRUE
                AND ld.deleted IS NOT TRUE
                AND ld.archived IS NOT TRUE;
                
                IF v_dx_code IS NOT NULL THEN
                    -- Insert new diagnosis
                    INSERT INTO form_med_claim_1500_dx (
                        patient_id,
                        dx_id,
                        diagnosis_code,
                        created_on,
                        created_by
                    )
                    VALUES (
                        v_patient_id,
                        v_new_dx_id,
                        v_dx_code,
                        CURRENT_TIMESTAMP,
                        COALESCE(NEW.updated_by, 1)
                    )
                    RETURNING id INTO v_dx_id;
                    
                    -- Link to claim
                    INSERT INTO sf_form_med_claim_1500_to_med_claim_1500_dx (
                        form_med_claim_1500_fk,
                        form_med_claim_1500_dx_fk,
                        delete,
                        archive
                    )
                    VALUES (
                        v_claim_id,
                        v_dx_id,
                        FALSE,
                        FALSE
                    );
                END IF;
            END IF;
        END IF;
        
        -- If old diagnosis removed, check if it's still used
        IF v_old_dx_id IS NOT NULL AND v_new_dx_id IS DISTINCT FROM v_old_dx_id THEN
            -- Check if any other service line still uses this diagnosis
            SELECT EXISTS(
                SELECT 1
                FROM form_med_claim_1500_sl sl2
                INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_sl sf_sl2
                    ON sf_sl2.form_med_claim_1500_sl_fk = sl2.id
                    AND sf_sl2.form_med_claim_1500_fk = v_claim_id
                    AND sf_sl2.delete IS NOT TRUE
                    AND sf_sl2.archive IS NOT TRUE
                WHERE sl2.id != NEW.id
                AND sl2.deleted IS NOT TRUE
                AND sl2.archived IS NOT TRUE
                AND (sl2.dx_id_1 = v_old_dx_id OR 
                     sl2.dx_id_2 = v_old_dx_id OR 
                     sl2.dx_id_3 = v_old_dx_id OR 
                     sl2.dx_id_4 = v_old_dx_id)
            ) INTO v_dx_used;
            
            IF NOT v_dx_used THEN
                -- Archive the diagnosis and its linking record
                UPDATE form_med_claim_1500_dx
                SET archived = TRUE,
                    updated_on = CURRENT_TIMESTAMP,
                    updated_by = COALESCE(NEW.updated_by, 1)
                WHERE dx_id = v_old_dx_id
                AND id IN (
                    SELECT dx.id
                    FROM form_med_claim_1500_dx dx
                    INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_dx sf_dx
                        ON sf_dx.form_med_claim_1500_dx_fk = dx.id
                        AND sf_dx.form_med_claim_1500_fk = v_claim_id
                        AND sf_dx.delete IS NOT TRUE
                    WHERE dx.dx_id = v_old_dx_id
                    AND dx.deleted IS NOT TRUE
                );
                
                UPDATE sf_form_med_claim_1500_to_med_claim_1500_dx
                SET archive = TRUE
                WHERE form_med_claim_1500_fk = v_claim_id
                AND form_med_claim_1500_dx_fk IN (
                    SELECT dx.id
                    FROM form_med_claim_1500_dx dx
                    WHERE dx.dx_id = v_old_dx_id
                    AND dx.archived IS TRUE
                );
            END IF;
        END IF;
    END LOOP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for CMS-1500 diagnosis sync
DROP TRIGGER IF EXISTS tr_sync_cms1500_service_line_diagnoses ON form_med_claim_1500_sl;

CREATE TRIGGER tr_sync_cms1500_service_line_diagnoses
    AFTER UPDATE OF dx_id_1, dx_id_2, dx_id_3, dx_id_4 ON form_med_claim_1500_sl
    FOR EACH ROW
    EXECUTE FUNCTION sync_cms1500_service_line_diagnoses();

-- 3. Trigger to handle new charge lines being added to CMS-1500 claims
CREATE OR REPLACE FUNCTION add_charge_line_to_cms1500_claim()
RETURNS TRIGGER AS $$
DECLARE
    v_claim_no TEXT;
    v_claim_id INTEGER;
    v_service_line_id INTEGER;
    v_insurance_id INTEGER;
    v_payer_id INTEGER;
    v_total_billed NUMERIC;
    v_total_expected NUMERIC;
    v_charge_line_array charge_line_with_split[];
    v_1500_diagnoses mm_1500_diagnosis_info[];
    v_1500_service_lines mm_1500_service_line_info[];
    v_1500_service_line mm_1500_service_line_info;
    v_dx_exists BOOLEAN;
    v_1500_dx_id INTEGER;
    v_1500_dx mm_1500_diagnosis_info;
BEGIN
    -- Only process if billing_method_id is 'cms1500'
    IF NEW.billing_method_id != 'cms1500' THEN
        RETURN NEW;
    END IF;
    
    -- Only process if invoice_no is set and not voided
    IF NEW.invoice_no IS NULL OR COALESCE(NEW.void, 'No') = 'Yes' OR COALESCE(NEW.zeroed, 'No') = 'Yes' THEN
        RETURN NEW;
    END IF;
    
    -- Check for CMS-1500 claim
    SELECT mc.id, mc.claim_no, mc.payer_id
    INTO v_claim_id, v_claim_no, v_payer_id
    FROM form_med_claim_1500 mc
    WHERE mc.invoice_no = NEW.invoice_no
    AND mc.deleted IS NOT TRUE
    AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Exit if no claim found
    IF v_claim_id IS NULL THEN
        RETURN NEW;
    END IF;
    
    -- Get insurance_id from the charge line
    v_insurance_id := NEW.insurance_id;
    
    -- Build charge_line_with_split array for the new charge line
    WITH charge_line_data AS (
        SELECT 
            NEW.id::integer as id,
            NEW.calc_invoice_split_no::text as calc_invoice_split_no,
            NEW.site_id::integer as site_id,
            NEW.patient_id::integer as patient_id,
            NEW.insurance_id::integer as insurance_id,
            v_payer_id::integer as payer_id,
            NEW.inventory_id::integer as inventory_id,
            NEW.shared_contract_id::integer as shared_contract_id,
            NEW.is_dirty::text as is_dirty,
            NEW.charge_no::text as charge_no,
            NEW.parent_charge_no::text as parent_charge_no,
            NEW.master_charge_no::text as master_charge_no,
            NEW.compound_no::text as compound_no,
            NEW.ticket_no::text as ticket_no,
            NEW.ticket_item_no::text as ticket_item_no,
            NEW.rental_id::integer as rental_id,
            NEW.order_rx_id::integer as order_rx_id,
            NEW.is_primary_drug::text as is_primary_drug,
            NEW.is_primary_drug_ncpdp::text as is_primary_drug_ncpdp,
            NEW.rx_no::text as rx_no,
            NEW.inventory_type_filter::text[] as inventory_type_filter,
            NEW.inventory_type::text as inventory_type,
            NEW.revenue_code_id::text as revenue_code_id,
            NEW.hcpc_code::text as hcpc_code,
            NEW.ndc::text as ndc,
            NEW.formatted_ndc::text as formatted_ndc,
            NEW.gcn_seqno::text as gcn_seqno,
            NEW.charge_quantity::numeric as charge_quantity,
            NEW.charge_quantity_ea::numeric as charge_quantity_ea,
            NEW.hcpc_quantity::numeric as hcpc_quantity,
            NEW.hcpc_unit::text as hcpc_unit,
            NEW.metric_quantity::numeric as metric_quantity,
            NEW.charge_unit::text as charge_unit,
            NEW.bill_quantity::numeric as bill_quantity,
            NEW.metric_unit_each::numeric as metric_unit_each,
            NEW.billing_unit_id::text as billing_unit_id,
            NEW.billing_method_id::text as billing_method_id,
            NEW.pricing_source::text as pricing_source,
            NEW.billed::numeric as billed,
            NEW.calc_billed_ea::numeric as calc_billed_ea,
            NEW.expected::numeric as expected,
            NEW.calc_expected_ea::numeric as calc_expected_ea,
            NEW.list_price::numeric as list_price,
            NEW.calc_list_ea::numeric as calc_list_ea,
            NEW.total_cost::numeric as total_cost,
            NEW.gross_amount_due::numeric as gross_amount_due,
            NEW.incv_amt_sub::numeric as incv_amt_sub,
            NEW.copay::numeric as copay,
            NEW.calc_cost_ea::numeric as calc_cost_ea,
            NEW.total_adjusted::numeric as total_adjusted,
            NEW.total_balance_due::numeric as total_balance_due,
            NEW.dispense_fee::numeric as dispense_fee,
            NEW.pt_pd_amt_sub::numeric as pt_pd_amt_sub,
            NEW.encounter_id::integer as encounter_id,
            NEW.description::text as description,
            NEW.upc::text as upc,
            NEW.upin::text as upin,
            NEW.cost_basis::text as cost_basis,
            NEW.awp_price::numeric as awp_price,
            NEW.modifier_1::text as modifier_1,
            NEW.modifier_2::text as modifier_2,
            NEW.modifier_3::text as modifier_3,
            NEW.modifier_4::text as modifier_4,
            NEW.rental_type::text as rental_type,
            NEW.frequency_code::text as frequency_code,
            NEW.fill_number::integer as fill_number,
            NEW.paid::numeric as paid,
            NEW.date_of_service::date as date_of_service,
            NEW.date_of_service_end::date as date_of_service_end
    )
    SELECT array_agg(
        (
            id,
            calc_invoice_split_no,
            site_id,
            patient_id,
            insurance_id,
            payer_id,
            inventory_id,
            shared_contract_id,
            is_dirty,
            charge_no,
            parent_charge_no,
            master_charge_no,
            compound_no,
            ticket_no,
            ticket_item_no,
            rental_id,
            order_rx_id,
            is_primary_drug,
            is_primary_drug_ncpdp,
            rx_no,
            inventory_type_filter,
            inventory_type,
            revenue_code_id,
            hcpc_code,
            ndc,
            formatted_ndc,
            gcn_seqno,
            charge_quantity,
            charge_quantity_ea,
            hcpc_quantity,
            hcpc_unit,
            metric_quantity,
            charge_unit,
            bill_quantity,
            metric_unit_each,
            billing_unit_id,
            billing_method_id,
            pricing_source,
            billed,
            calc_billed_ea,
            expected,
            calc_expected_ea,
            list_price,
            calc_list_ea,
            total_cost,
            gross_amount_due,
            incv_amt_sub,
            copay,
            calc_cost_ea,
            total_adjusted,
            total_balance_due,
            dispense_fee,
            pt_pd_amt_sub,
            encounter_id,
            description,
            upc,
            upin,
            cost_basis,
            awp_price,
            modifier_1,
            modifier_2,
            modifier_3,
            modifier_4,
            rental_type,
            frequency_code,
            fill_number,
            paid,
            date_of_service,
            date_of_service_end
        )::charge_line_with_split
    ) INTO v_charge_line_array
    FROM charge_line_data;
    
    -- Check if service line already exists
    SELECT sl.id INTO v_service_line_id
    FROM form_med_claim_1500_sl sl
    INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_sl sf_sl
        ON sf_sl.form_med_claim_1500_sl_fk = sl.id
        AND sf_sl.form_med_claim_1500_fk = v_claim_id
        AND sf_sl.delete IS NOT TRUE
        AND sf_sl.archive IS NOT TRUE
    WHERE sl.charge_no = NEW.charge_no
    AND sl.deleted IS NOT TRUE
    LIMIT 1;
    
    IF v_service_line_id IS NULL THEN
        -- Build diagnosis codes using CMS-1500 specific function
        v_1500_diagnoses := build_mm_1500_diagnosis_loop(
            v_charge_line_array,
            NULL,  -- no parent claim for new charge line
            v_payer_id
        );
        
        -- Build service lines using CMS-1500 specific function
        v_1500_service_lines := build_mm_1500_charge_lines_loop(
            NEW.site_id,
            NEW.patient_id,
            v_payer_id,
            v_charge_line_array,
            NULL,  -- no parent claim for new charge line
            NULL,  -- no parent claim no
            v_insurance_id  -- pass the insurance_id
        );
        
        IF array_length(v_1500_service_lines, 1) > 0 THEN
            v_1500_service_line := v_1500_service_lines[1];
            
            -- Create service line for CMS-1500
            INSERT INTO form_med_claim_1500_sl (
                rx_no,
                charge_no,
                patient_id,
                service_date,
                service_date_end,
                inventory_id,
                order_item_id,
                rental_id,
                type,
                line_item_charge_amount,
                line_item_paid_amount,
                measurement_unit,
                service_unit_count,
                place_of_service_code,
                modifier_1,
                modifier_2,
                modifier_3,
                modifier_4,
                dx_id_1,
                dx_id_2,
                dx_id_3,
                dx_id_4,
                procedure_code,
                supplemental_info,
                created_on,
                created_by
            )
            VALUES (
                v_1500_service_line.rx_no,
                v_1500_service_line.charge_no,
                v_1500_service_line.patient_id,
                v_1500_service_line.service_date::date,
                v_1500_service_line.service_date_end::date,
                v_1500_service_line.inventory_id,
                NEW.order_rx_id,  -- Map from charge line
                NEW.rental_id,    -- Map from charge line
                v_1500_service_line.type,
                v_1500_service_line.line_item_charge_amount,
                v_1500_service_line.line_item_paid_amount,
                v_1500_service_line.measurement_unit,
                v_1500_service_line.service_unit_count,
                v_1500_service_line.place_of_service_code,
                v_1500_service_line.modifier_1,
                v_1500_service_line.modifier_2,
                v_1500_service_line.modifier_3,
                v_1500_service_line.modifier_4,
                v_1500_service_line.dx_id_1,
                v_1500_service_line.dx_id_2,
                v_1500_service_line.dx_id_3,
                v_1500_service_line.dx_id_4,
                v_1500_service_line.procedure_code,
                v_1500_service_line.supplemental_info,
                NEW.created_on,
                NEW.created_by
            )
            RETURNING id INTO v_service_line_id;
            
            -- Create linking record to claim
            INSERT INTO sf_form_med_claim_1500_to_med_claim_1500_sl (
                form_med_claim_1500_fk,
                form_med_claim_1500_sl_fk,
                delete,
                archive
            )
            VALUES (
                v_claim_id,
                v_service_line_id,
                FALSE,
                FALSE
            );
            
            -- Add diagnosis records to the claim if not already present
            IF v_1500_diagnoses IS NOT NULL AND array_length(v_1500_diagnoses, 1) > 0 THEN
                FOREACH v_1500_dx IN ARRAY v_1500_diagnoses LOOP
                    -- Check if this diagnosis already exists for the claim
                    SELECT EXISTS(
                        SELECT 1
                        FROM form_med_claim_1500_dx dx
                        INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_dx sf_dx
                            ON sf_dx.form_med_claim_1500_dx_fk = dx.id
                            AND sf_dx.form_med_claim_1500_fk = v_claim_id
                            AND sf_dx.delete IS NOT TRUE
                            AND sf_dx.archive IS NOT TRUE
                        WHERE dx.dx_id = v_1500_dx.dx_id
                        AND dx.deleted IS NOT TRUE
                        AND dx.archived IS NOT TRUE
                    ) INTO v_dx_exists;
                    
                    IF NOT v_dx_exists THEN
                        -- Insert the diagnosis
                        INSERT INTO form_med_claim_1500_dx (
                            patient_id,
                            dx_id,
                            diagnosis_code,
                            created_on,
                            created_by
                        )
                        VALUES (
                            v_1500_dx.patient_id,
                            v_1500_dx.dx_id,
                            v_1500_dx.diagnosis_code,
                            NEW.created_on,
                            NEW.created_by
                        )
                        RETURNING id INTO v_1500_dx_id;
                        
                        -- Link to claim
                        INSERT INTO sf_form_med_claim_1500_to_med_claim_1500_dx (
                            form_med_claim_1500_fk,
                            form_med_claim_1500_dx_fk,
                            delete,
                            archive
                        )
                        VALUES (
                            v_claim_id,
                            v_1500_dx_id,
                            FALSE,
                            FALSE
                        );
                    END IF;
                END LOOP;
            END IF;
        END IF;
        
        -- Calculate new totals
        SELECT 
            SUM(lcl.billed),
            SUM(lcl.expected)
        INTO v_total_billed, v_total_expected
        FROM form_ledger_charge_line lcl
        WHERE lcl.invoice_no = NEW.invoice_no
        AND lcl.deleted IS NOT TRUE
        AND lcl.archived IS NOT TRUE
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes';
        
        -- Update CMS-1500 claim totals
        UPDATE form_med_claim_1500
        SET billed = v_total_billed,
            expected = v_total_expected,
            updated_on = NEW.updated_on,
            updated_by = NEW.updated_by
        WHERE id = v_claim_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for adding charge lines to CMS-1500 claims
DROP TRIGGER IF EXISTS tr_add_charge_line_to_cms1500_claim ON form_ledger_charge_line;

CREATE TRIGGER tr_add_charge_line_to_cms1500_claim
    AFTER INSERT ON form_ledger_charge_line
    FOR EACH ROW
    EXECUTE FUNCTION add_charge_line_to_cms1500_claim();

-- 4. Trigger to sync various fields from charge lines to CMS-1500 service lines
CREATE OR REPLACE FUNCTION sync_charge_line_fields_to_cms1500_claim()
RETURNS TRIGGER AS $$
DECLARE
    v_claim_no TEXT;
    v_claim_id INTEGER;
    v_payer_id INTEGER;
    v_insurance_id INTEGER;
    v_charge_line_array charge_line_with_split[];
    v_1500_service_lines mm_1500_service_line_info[];
    v_1500_service_line mm_1500_service_line_info;
    v_total_billed NUMERIC;
    v_total_expected NUMERIC;
BEGIN
    -- Only process if billing_method_id is 'cms1500'
    IF NEW.billing_method_id != 'cms1500' THEN
        RETURN NEW;
    END IF;
        -- Only process if invoice_no is set and not voided
    IF NEW.invoice_no IS NULL OR COALESCE(NEW.void, 'No') = 'Yes' OR COALESCE(NEW.zeroed, 'No') = 'Yes' THEN
        RETURN NEW;
    END IF;

    -- Only process if relevant fields changed
    IF TG_OP = 'UPDATE' AND 
       OLD.hcpc_code IS NOT DISTINCT FROM NEW.hcpc_code AND
       OLD.bill_quantity IS NOT DISTINCT FROM NEW.bill_quantity AND
       OLD.billed IS NOT DISTINCT FROM NEW.billed AND
       OLD.ndc IS NOT DISTINCT FROM NEW.ndc AND
       OLD.frequency_code IS NOT DISTINCT FROM NEW.frequency_code AND
       OLD.modifier_1 IS NOT DISTINCT FROM NEW.modifier_1 AND
       OLD.modifier_2 IS NOT DISTINCT FROM NEW.modifier_2 AND
       OLD.modifier_3 IS NOT DISTINCT FROM NEW.modifier_3 AND
       OLD.modifier_4 IS NOT DISTINCT FROM NEW.modifier_4 AND
       OLD.upin IS NOT DISTINCT FROM NEW.upin AND
       OLD.charge_quantity IS NOT DISTINCT FROM NEW.charge_quantity AND
       OLD.metric_quantity IS NOT DISTINCT FROM NEW.metric_quantity AND
       OLD.description IS NOT DISTINCT FROM NEW.description AND
       OLD.inventory_id IS NOT DISTINCT FROM NEW.inventory_id AND
       OLD.expected IS NOT DISTINCT FROM NEW.expected THEN
        RETURN NEW;
    END IF;
    
    -- Get CMS-1500 claim information
    SELECT mc.claim_no, mc.id, mc.payer_id
    INTO v_claim_no, v_claim_id, v_payer_id
    FROM form_med_claim_1500 mc
    WHERE mc.invoice_no = NEW.invoice_no
    AND mc.deleted IS NOT TRUE
    AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    IF v_claim_id IS NULL THEN
        RETURN NEW;
    END IF;
    
    -- Get insurance_id
    v_insurance_id := NEW.insurance_id;
    
    -- Build charge_line_with_split array for the updated charge line
    WITH charge_line_data AS (
        SELECT 
            NEW.id::integer as id,
            NEW.calc_invoice_split_no::text as calc_invoice_split_no,
            NEW.site_id::integer as site_id,
            NEW.patient_id::integer as patient_id,
            NEW.insurance_id::integer as insurance_id,
            v_payer_id::integer as payer_id,
            NEW.inventory_id::integer as inventory_id,
            NEW.shared_contract_id::integer as shared_contract_id,
            NEW.is_dirty::text as is_dirty,
            NEW.charge_no::text as charge_no,
            NEW.parent_charge_no::text as parent_charge_no,
            NEW.master_charge_no::text as master_charge_no,
            NEW.compound_no::text as compound_no,
            NEW.ticket_no::text as ticket_no,
            NEW.ticket_item_no::text as ticket_item_no,
            NEW.rental_id::integer as rental_id,
            NEW.order_rx_id::integer as order_rx_id,
            NEW.is_primary_drug::text as is_primary_drug,
            NEW.is_primary_drug_ncpdp::text as is_primary_drug_ncpdp,
            NEW.rx_no::text as rx_no,
            NEW.inventory_type_filter::text[] as inventory_type_filter,
            NEW.inventory_type::text as inventory_type,
            NEW.revenue_code_id::text as revenue_code_id,
            NEW.hcpc_code::text as hcpc_code,
            NEW.ndc::text as ndc,
            NEW.formatted_ndc::text as formatted_ndc,
            NEW.gcn_seqno::text as gcn_seqno,
            NEW.charge_quantity::numeric as charge_quantity,
            NEW.charge_quantity_ea::numeric as charge_quantity_ea,
            NEW.hcpc_quantity::numeric as hcpc_quantity,
            NEW.hcpc_unit::text as hcpc_unit,
            NEW.metric_quantity::numeric as metric_quantity,
            NEW.charge_unit::text as charge_unit,
            NEW.bill_quantity::numeric as bill_quantity,
            NEW.metric_unit_each::numeric as metric_unit_each,
            NEW.billing_unit_id::text as billing_unit_id,
            NEW.billing_method_id::text as billing_method_id,
            NEW.pricing_source::text as pricing_source,
            NEW.billed::numeric as billed,
            NEW.calc_billed_ea::numeric as calc_billed_ea,
            NEW.expected::numeric as expected,
            NEW.calc_expected_ea::numeric as calc_expected_ea,
            NEW.list_price::numeric as list_price,
            NEW.calc_list_ea::numeric as calc_list_ea,
            NEW.total_cost::numeric as total_cost,
            NEW.gross_amount_due::numeric as gross_amount_due,
            NEW.incv_amt_sub::numeric as incv_amt_sub,
            NEW.copay::numeric as copay,
            NEW.calc_cost_ea::numeric as calc_cost_ea,
            NEW.total_adjusted::numeric as total_adjusted,
            NEW.total_balance_due::numeric as total_balance_due,
            NEW.dispense_fee::numeric as dispense_fee,
            NEW.pt_pd_amt_sub::numeric as pt_pd_amt_sub,
            NEW.encounter_id::integer as encounter_id,
            NEW.description::text as description,
            NEW.upc::text as upc,
            NEW.upin::text as upin,
            NEW.cost_basis::text as cost_basis,
            NEW.awp_price::numeric as awp_price,
            NEW.modifier_1::text as modifier_1,
            NEW.modifier_2::text as modifier_2,
            NEW.modifier_3::text as modifier_3,
            NEW.modifier_4::text as modifier_4,
            NEW.rental_type::text as rental_type,
            NEW.frequency_code::text as frequency_code,
            NEW.fill_number::integer as fill_number,
            NEW.paid::numeric as paid,
            NEW.date_of_service::date as date_of_service,
            NEW.date_of_service_end::date as date_of_service_end
    )
    SELECT array_agg(
        (
            id,
            calc_invoice_split_no,
            site_id,
            patient_id,
            insurance_id,
            payer_id,
            inventory_id,
            shared_contract_id,
            is_dirty,
            charge_no,
            parent_charge_no,
            master_charge_no,
            compound_no,
            ticket_no,
            ticket_item_no,
            rental_id,
            order_rx_id,
            is_primary_drug,
            is_primary_drug_ncpdp,
            rx_no,
            inventory_type_filter,
            inventory_type,
            revenue_code_id,
            hcpc_code,
            ndc,
            formatted_ndc,
            gcn_seqno,
            charge_quantity,
            charge_quantity_ea,
            hcpc_quantity,
            hcpc_unit,
            metric_quantity,
            charge_unit,
            bill_quantity,
            metric_unit_each,
            billing_unit_id,
            billing_method_id,
            pricing_source,
            billed,
            calc_billed_ea,
            expected,
            calc_expected_ea,
            list_price,
            calc_list_ea,
            total_cost,
            gross_amount_due,
            incv_amt_sub,
            copay,
            calc_cost_ea,
            total_adjusted,
            total_balance_due,
            dispense_fee,
            pt_pd_amt_sub,
            encounter_id,
            description,
            upc,
            upin,
            cost_basis,
            awp_price,
            modifier_1,
            modifier_2,
            modifier_3,
            modifier_4,
            rental_type,
            frequency_code,
            fill_number,
            paid,
            date_of_service,
            date_of_service_end
        )::charge_line_with_split
    ) INTO v_charge_line_array
    FROM charge_line_data;
    
    -- Build service lines using CMS-1500 helper
    v_1500_service_lines := build_mm_1500_charge_lines_loop(
        NEW.site_id,
        NEW.patient_id,
        v_payer_id,
        v_charge_line_array,
        NULL,  -- no parent claim
        NULL,  -- no parent claim no
        v_insurance_id  -- pass the insurance_id
    );
    
    -- Update the service line with new values
    IF array_length(v_1500_service_lines, 1) > 0 THEN
        v_1500_service_line := v_1500_service_lines[1];
        
        UPDATE form_med_claim_1500_sl sl
        SET 
            inventory_id = NEW.inventory_id,
            order_item_id = NEW.order_rx_id,
            rental_id = NEW.rental_id,
            service_date = v_1500_service_line.service_date::date,
            service_date_end = v_1500_service_line.service_date_end::date,
            line_item_charge_amount = v_1500_service_line.line_item_charge_amount,
            line_item_paid_amount = v_1500_service_line.line_item_paid_amount,
            measurement_unit = v_1500_service_line.measurement_unit,
            service_unit_count = v_1500_service_line.service_unit_count,
            modifier_1 = v_1500_service_line.modifier_1,
            modifier_2 = v_1500_service_line.modifier_2,
            modifier_3 = v_1500_service_line.modifier_3,
            modifier_4 = v_1500_service_line.modifier_4,
            procedure_code = v_1500_service_line.procedure_code,
            supplemental_info = v_1500_service_line.supplemental_info,
            updated_on = NEW.updated_on,
            updated_by = NEW.updated_by
        WHERE sl.id IN (
            SELECT sf_sl.form_med_claim_1500_sl_fk 
            FROM sf_form_med_claim_1500_to_med_claim_1500_sl sf_sl
            WHERE sf_sl.form_med_claim_1500_fk = v_claim_id
            AND sf_sl.delete IS NOT TRUE
            AND sf_sl.archive IS NOT TRUE
        )
        AND sl.charge_no = NEW.charge_no
        AND sl.deleted IS NOT TRUE
        AND sl.archived IS NOT TRUE;
    END IF;

    -- Update CMS-1500 claim totals
    SELECT 
        SUM(COALESCE(lcl.billed, 0::NUMERIC)::NUMERIC),
        SUM(COALESCE(lcl.expected, 0::NUMERIC)::NUMERIC)
    INTO v_total_billed, v_total_expected
    FROM form_ledger_charge_line lcl
    WHERE lcl.invoice_no = NEW.invoice_no
    AND lcl.deleted IS NOT TRUE
    AND lcl.archived IS NOT TRUE
    AND COALESCE(lcl.void, 'No') <> 'Yes'
    AND COALESCE(lcl.zeroed, 'No') <> 'Yes';
    
    UPDATE form_med_claim_1500
    SET billed = v_total_billed,
        expected = v_total_expected,
        updated_on = NEW.updated_on,
        updated_by = NEW.updated_by
    WHERE id = v_claim_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for syncing charge line fields to CMS-1500
DROP TRIGGER IF EXISTS tr_sync_charge_fields_to_cms1500_claim ON form_ledger_charge_line;

CREATE TRIGGER tr_sync_charge_fields_to_cms1500_claim
    AFTER UPDATE OF hcpc_code, bill_quantity, billed, ndc, frequency_code, 
                    modifier_1, modifier_2, modifier_3, modifier_4, upin, 
                    charge_quantity, metric_quantity, description, 
                    inventory_id, expected
    ON form_ledger_charge_line
    FOR EACH ROW
    EXECUTE FUNCTION sync_charge_line_fields_to_cms1500_claim();

-- 5. Trigger to handle voided charge lines on CMS-1500 claims
CREATE OR REPLACE FUNCTION handle_voided_charge_line_cms1500()
RETURNS TRIGGER AS $$
DECLARE
    v_claim_no TEXT;
    v_claim_id INTEGER;
    v_total_billed NUMERIC;
    v_total_expected NUMERIC;
    v_service_line_id INTEGER;
    v_is_voided BOOLEAN;
    v_was_voided BOOLEAN;
BEGIN
    -- Only process if billing_method_id is 'cms1500'
    IF NEW.billing_method_id != 'cms1500' THEN
        RETURN NEW;
    END IF;
    
    -- Check if the charge line is now voided (archived, void, or zeroed)
    v_is_voided := (NEW.archived IS TRUE) OR 
                   (COALESCE(NEW.void, 'No') = 'Yes') OR 
                   (COALESCE(NEW.zeroed, 'No') = 'Yes');
    
    -- Check if it was previously voided
    v_was_voided := (OLD.archived IS TRUE) OR 
                    (COALESCE(OLD.void, 'No') = 'Yes') OR 
                    (COALESCE(OLD.zeroed, 'No') = 'Yes');
    
    -- Only process if void status changed
    IF v_is_voided = v_was_voided THEN
        RETURN NEW;
    END IF;
    
    -- Only process if we have an invoice number
    IF NEW.invoice_no IS NULL THEN
        RETURN NEW;
    END IF;
    
    -- Check for CMS-1500 claim
    SELECT mc.id, mc.claim_no INTO v_claim_id, v_claim_no
    FROM form_med_claim_1500 mc
    WHERE mc.invoice_no = NEW.invoice_no
    AND mc.deleted IS NOT TRUE
    AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    IF v_claim_id IS NOT NULL THEN
        -- Get the service line ID
        SELECT sl.id INTO v_service_line_id
        FROM form_med_claim_1500_sl sl
        INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_sl sf_sl
            ON sf_sl.form_med_claim_1500_sl_fk = sl.id
            AND sf_sl.form_med_claim_1500_fk = v_claim_id
            AND sf_sl.delete IS NOT TRUE
            AND sf_sl.archive IS NOT TRUE
        WHERE sl.charge_no = NEW.charge_no
        AND sl.deleted IS NOT TRUE
        AND sl.archived IS NOT TRUE
        LIMIT 1;
        
        IF v_service_line_id IS NOT NULL THEN
            -- Archive the service line
            UPDATE form_med_claim_1500_sl
            SET archived = TRUE,
                updated_on = NEW.updated_on,
                updated_by = NEW.updated_by
            WHERE id = v_service_line_id;
            
            -- Archive the linking record
            UPDATE sf_form_med_claim_1500_to_med_claim_1500_sl
            SET archive = TRUE
            WHERE form_med_claim_1500_sl_fk = v_service_line_id;
        END IF;
        
        -- Calculate new totals from all non-voided charge lines
        SELECT 
            SUM(lcl.billed),
            SUM(lcl.expected)
        INTO v_total_billed, v_total_expected
        FROM form_ledger_charge_line lcl
        WHERE lcl.invoice_no = NEW.invoice_no
        AND lcl.deleted IS NOT TRUE
        AND lcl.archived IS NOT TRUE
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes';
        
        -- Update CMS-1500 claim totals
        UPDATE form_med_claim_1500
        SET billed = v_total_billed,
            expected = v_total_expected,
            updated_on = NEW.updated_on,
            updated_by = NEW.updated_by
        WHERE id = v_claim_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for handling voided charge lines on CMS-1500
DROP TRIGGER IF EXISTS tr_handle_voided_charge_line_cms1500 ON form_ledger_charge_line;

CREATE TRIGGER tr_handle_voided_charge_line_cms1500
    AFTER UPDATE OF archived, void, zeroed ON form_ledger_charge_line
    FOR EACH ROW
    EXECUTE FUNCTION handle_voided_charge_line_cms1500();

-- Add comments
COMMENT ON FUNCTION sync_rental_service_dates_cms1500() IS 
'Updates service dates on CMS-1500 claims when rental charge line dates change';

COMMENT ON FUNCTION sync_cms1500_service_line_diagnoses() IS 
'Keeps diagnoses in sync between form_med_claim_1500_sl entries and form_med_claim_1500_dx entries';

COMMENT ON FUNCTION add_charge_line_to_cms1500_claim() IS 
'Automatically adds new charge lines to existing CMS-1500 claims when appropriate';

COMMENT ON FUNCTION sync_charge_line_fields_to_cms1500_claim() IS 
'Syncs various fields (HCPC, quantities, NDC, modifiers, etc.) from charge lines to CMS-1500 service lines';

COMMENT ON FUNCTION handle_voided_charge_line_cms1500() IS 
'Archives CMS-1500 service lines when charge lines are voided and updates claim totals';
