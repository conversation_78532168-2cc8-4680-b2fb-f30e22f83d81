
CREATE OR REPLACE FUNCTION get_delivery_ticket_item_presets(
  p_site_id integer,
  p_patient_id integer,
  p_careplan_id integer,
  p_inventory_id integer,
  p_rx_id integer,
  p_service_from date,
  p_billabe BOOLEAN,
  p_billing_method text DEFAULT NULL,
  p_dispense_quantity numeric DEFAULT NULL,
  p_dispense_unit text DEFAULT NULL,
  p_copay numeric DEFAULT NULL,
  p_day_supply integer DEFAULT NULL,
  p_insurance_id integer DEFAULT NULL,
  p_accept_assignment BOOLEAN DEFAULT TRUE,
  p_part_of_kit text DEFAULT NULL,
  p_rental_type text DEFAULT NULL,
  p_rental_id integer DEFAULT NULL,
  p_frequency_code text DEFAULT NULL
) RETURNS json AS $BODY$
DECLARE
    v_params jsonb;
    v_inventory_active boolean;
    v_inventory_name text;
    v_result jsonb;
BEGIN

  -- Build parameters JSON for logging
  v_params := json_build_object(
      'p_site_id', p_site_id,
      'p_patient_id', p_patient_id,
      'p_careplan_id', p_careplan_id,
      'p_inventory_id', p_inventory_id,
      'p_dispense_quantity', p_dispense_quantity,
      'p_rx_id', p_rx_id,
      'p_rental_id', p_rental_id,
      'p_part_of_kit', p_part_of_kit,
      'p_frequency_code', p_frequency_code,
      'p_dispense_quantity', p_dispense_quantity,
      'p_dispense_unit', p_dispense_unit,
      'p_rental_type', p_rental_type,
      'p_billing_method', p_billing_method,
      'p_insurance_id', p_insurance_id, 
      'p_accept_assignment', p_accept_assignment,
      'p_service_from', p_service_from,
      'p_copay', p_copay,
      'p_day_supply', p_day_supply,
      'p_billabe', p_billabe
  );

    RAISE LOG 'get_delivery_ticket_item_presets: Generating delivery ticket item presets for inventory ID: % Site ID: % Patient ID: % Careplan ID: % Rx ID: % Billing Method: % Service From: % Billable: %', p_inventory_id, p_site_id, p_patient_id, p_careplan_id, p_rx_id, p_billing_method, p_service_from, p_billabe;
    -- Validate required parameters
    IF p_inventory_id IS NULL OR p_rx_id IS NULL OR p_site_id IS NULL 
    OR p_billing_method IS NULL OR p_patient_id IS NULL OR p_careplan_id IS NULL 
    OR p_service_from IS NULL OR p_billabe IS NULL THEN
        INSERT INTO dispensing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Missing a required parameter (Inventory ID, Rx ID, Site ID, Billing Method, Patient ID, Careplan ID, Service From Date, Billable Flag) to generate delivery ticket item presets',
            'Exception in get_delivery_ticket_item_presets',
            'FUNCTION',
            current_schema(),
            'form_careplan_delivery_tick',
            v_params
        );
      RAISE EXCEPTION 'Missing a required parameter (Inventory ID, Rx ID, Site ID, Billing Method, Patient ID, Careplan ID, Service From Date, Billable Flag) to generate delivery ticket item presets';
    END IF;

    SELECT COALESCE(inv.active, 'No') = 'Yes',
           inv.name
    INTO v_inventory_active,
         v_inventory_name
    FROM form_inventory inv
    WHERE inv.id = p_inventory_id AND inv.archived IS NOT TRUE AND inv.deleted IS NOT TRUE;

    IF NOT v_inventory_active THEN
        INSERT INTO dispensing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Inventory item % is not active. Cannot dispense.',
            'Exception in get_delivery_ticket_item_presets',
            'FUNCTION',
            current_schema(),
            'form_inventory',
            v_params
        );  
        RAISE EXCEPTION 'Inventory item % is not active. Cannot dispense.', v_inventory_name;
    END IF;

    SELECT
        json_strip_nulls(json_build_object(
            'rx_id', p_rx_id,
            'code', gen_random_uuid(),
            'patient_id', p_patient_id,
            'careplan_id', p_careplan_id,
            'site_id', p_site_id,
            'needs_scanned', CASE
                WHEN inv.type = 'Billable' THEN NULL
                WHEN inv.type = 'Equipment Rental' THEN 'Yes'
                WHEN COALESCE(inv.lot_tracking, 'No') = 'Yes' OR COALESCE(inv.serial_tracking, 'No') = 'Yes' THEN 'Yes'
                ELSE NULL
            END,
            'rental_id', p_rental_id,
            'is_primary_drug', CASE
                WHEN rxfi.is_primary_drug = TRUE THEN 'Yes'
                ELSE NULL
            END,
            'template_type', CASE
                WHEN rxfi.is_primary_drug = TRUE THEN rx.template_type
                ELSE NULL
            END,
            'print', 'Yes',
            'bill', CASE
                WHEN p_billabe = FALSE THEN NULL
                ELSE 'Yes'
            END,
            'inventory_id', p_inventory_id,
            'type', inv.type,
            'hcpc_code', prc.hcpc_code,
            'ndc', inv.ndc,
            'formatted_ndc', inv.formatted_ndc,
            'is_340b', CASE
                WHEN COALESCE(inv.type,'') NOT IN ('Drug', 'Compound') THEN NULL
                ELSE rx.is_340b
            END,
            'part_of_kit', CASE
                WHEN COALESCE(inv.type,'') <> 'Supply' THEN NULL
                ELSE p_part_of_kit
            END,
            'supply_billable', CASE
                WHEN COALESCE(inv.type,'') <> 'Supply' THEN NULL
                WHEN p_billabe IS TRUE THEN 'Yes'
                ELSE NULL
            END,
            'rental_type', p_rental_type,
            'frequency_code', p_frequency_code,
            'day_supply', CASE
                WHEN COALESCE(inv.type,'') = 'Eqipment Rental' AND p_rental_type = 'Rental' THEN p_day_supply
                WHEN COALESCE(inv.type,'') NOT IN ('Drug', 'Compound') THEN NULL
                WHEN p_day_supply IS NOT NULL THEN p_day_supply
                ELSE rx.day_supply
            END,
            'last_through_date',
            CASE
                WHEN rxfi.is_primary_drug = TRUE THEN TO_CHAR((p_service_from + INTERVAL '1 day' * p_day_supply)::date, 'MM/DD/YYYY')
                ELSE NULL
            END,
            'allow_dispense_quantity_update', CASE 
	            WHEN inv.type NOT IN ('Drug', 'Compound') THEN 'Yes'
	            ELSE NULL
            END,
            'dispense_quantity', COALESCE(p_dispense_quantity, rxfi.dispense_quantity),
            'dispense_unit', 'each', -- TODO: handle compounds
            'billing_method', p_billing_method,
            'insurance_id', p_insurance_id,
            'accept_assignment', CASE
                WHEN p_accept_assignment = TRUE THEN 'Yes'
                ELSE NULL
            END,
            'expected_ea', prc.expected_ea,
            'list_ea', prc.list_ea,
            'billed_ea', prc.bill_ea,
            'copay', p_copay,
            'quantity_needed', CASE
                WHEN COALESCE(inv.type,'') NOT IN ('Drug', 'Compound') THEN p_dispense_quantity
                ELSE COALESCE(p_dispense_quantity, rxfi.dispense_quantity)
            END,
            'doses_to_prep', CASE
                WHEN rxfi.is_primary_drug = TRUE THEN rx.doses_to_prep
                ELSE NULL
            END,
            'doses_per_container', CASE
                WHEN rxfi.is_primary_drug = TRUE THEN rx.doses_per_container
                ELSE NULL
            END,
            'containers_to_prep',  CASE
                WHEN rxfi.is_primary_drug = TRUE THEN rx.containers_to_prep
                ELSE NULL
            END,
            'refill_tracking', CASE 
                WHEN rxfi.is_primary_drug = TRUE THEN rx.refill_tracking
                ELSE NULL
            END,
            'doses_remaining', CASE
                WHEN rxfi.is_primary_drug = TRUE THEN rx.doses_remaining
                ELSE NULL
            END,
            'refills_remaining', CASE
                WHEN rxfi.is_primary_drug = TRUE THEN rx.refills_remaining
                ELSE NULL
            END,
            'include_cmpd_instr_wo',CASE
                WHEN rxfi.is_primary_drug = TRUE THEN rx.include_cmpd_instr_wo
                ELSE NULL
            END,
            'auto_compounding_instructions', CASE
                WHEN rxfi.is_primary_drug = TRUE THEN rx.auto_compounding_instructions
                ELSE NULL
            END,
            'compounding_instructions', CASE
                WHEN rxfi.is_primary_drug = TRUE THEN rx.compounding_instructions
                ELSE NULL
            END,
            'container_id', CASE
                WHEN rxfi.is_primary_drug = TRUE THEN rx.container_id
                ELSE NULL
            END,
            'vehicle_id', CASE
                WHEN rxfi.is_primary_drug = TRUE THEN rx.vehicle_id
                ELSE NULL
            END,
            'volume_per_dose', CASE
                WHEN rxfi.is_primary_drug = TRUE THEN rx.volume_per_dose
                ELSE NULL
            END,
            'overfill', CASE
                WHEN rxfi.is_primary_drug = TRUE THEN rx.overfill
                ELSE NULL
            END,
            'med_review_wo', CASE
                WHEN rxfi.is_primary_drug = TRUE THEN rx.med_review_wo
                ELSE NULL
            END
        ))
    INTO v_result
    FROM form_inventory inv
    INNER JOIN get_inventory_pricing(p_inventory_id, p_insurance_id, p_site_id, p_patient_id) prc ON TRUE
    INNER JOIN vw_rx_order rx ON rx.rx_id = p_rx_id
    LEFT JOIN vw_rx_fill_items rxfi ON rxfi.rx_id = rx.rx_id AND p_inventory_id = rxfi.inventory_id AND rxfi.fill_status IN ('Ready to Contact', 'Ready to Refill')
    WHERE inv.id = p_inventory_id AND inv.archived IS NOT TRUE AND inv.deleted IS NOT TRUE AND COALESCE(inv.active, 'No') = 'Yes';

    RETURN v_result;

    EXCEPTION WHEN OTHERS THEN
        -- Log error
        INSERT INTO dispensing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in get_delivery_ticket_item_presets',
            'FUNCTION',
            current_schema(),
            'form_careplan_delivery_tick',
            v_params
        );
        
    RAISE WARNING 'Failed to generate delivery ticket item assessment presets: %', SQLERRM;
	RETURN NULL;
END;
$BODY$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION create_delivery_ticket_presets(
  p_wrx_ids integer[],
  p_rx_id integer,
  p_service_from text DEFAULT NULL,
  p_service_to text DEFAULT NULL,
  p_supply_kit_id integer DEFAULT NULL,
  p_supply_order_ids INTEGER[] DEFAULT ARRAY[]::INTEGER[],
  p_supply_kit_selected_ids INTEGER[] DEFAULT ARRAY[]::INTEGER[],
  p_working_delivery_ticket_id integer DEFAULT NULL
) RETURNS json AS $BODY$
DECLARE
    v_params jsonb;
    v_service_from date;
    v_service_to date;
    v_dt_confirmed boolean;
    v_existing_inventory_ids integer[];
    v_supply_kit_inventory_ids integer[];
    v_existing_rx_ids integer[];
    v_supply_kit_billable_id integer;
    v_new_dt_items jsonb[];
    v_ids integer[];
    v_rx_ids integer[];
    v_copay numeric;
    v_existing_copay numeric;
    v_total_pt_responsibility numeric;
    v_results json;
    v_item_result json;
BEGIN

    RAISE LOG 'create_delivery_ticket_presets: Creating delivery ticket presets for Rx Disp IDs: % and working delivery ticket ID: %', p_wrx_ids, p_working_delivery_ticket_id;
    -- Build parameters JSON for logging
    v_params := json_build_object(
        'p_wrx_ids', p_wrx_ids,
        'p_rx_id', p_rx_id,
        'p_supply_kit_id', p_supply_kit_id,
        'p_working_delivery_ticket_id', p_working_delivery_ticket_id,
        'p_service_from', p_service_from,
        'p_service_to', p_service_to
    );

    IF p_wrx_ids IS NULL THEN
        INSERT INTO dispensing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Missing working dispense ticket Ids',
            'Exception in create_delivery_ticket_presets',
            'FUNCTION',
            current_schema(),
            'form_careplan_delivery_tick',
            v_params
        );
      RAISE EXCEPTION 'Rx ID is required to generate delivery ticket presets';
    END IF;

    SELECT array_agg(rx_id)
    INTO v_rx_ids
    FROM form_careplan_order_rx_disp
    WHERE id = ANY(p_wrx_ids);

    IF p_supply_kit_id IS NOT NULL THEN
        SELECT
            array_agg(inventory_id)
        INTO v_supply_kit_inventory_ids
        FROM vw_supply_kit_items
        WHERE supply_kit_id = p_supply_kit_id;

        SELECT
            billable_id
        INTO v_supply_kit_billable_id
        FROM form_inventory_supply_kit
        WHERE id = p_supply_kit_id;
    END IF;

    RAISE LOG 'create_delivery_ticket_presets: Supply kitt billable ID: %', v_supply_kit_billable_id;

    SELECT
        COALESCE(SUM(iva.copay), 0)
        INTO v_copay
        FROM form_billing_invoice bi
        INNER JOIN vw_invoice_claim_response_details iva ON iva.invoice_id = bi.id
        INNER JOIN form_careplan_order_rx_disp rxd ON rxd.id IN (SELECT unnest(p_wrx_ids))
        WHERE bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
        AND bi.master_invoice_no = rxd.master_invoice_no
        AND COALESCE(bi.void, 'No') <> 'Yes'
        AND COALESCE(bi.zeroed, 'No') <> 'Yes';

    v_total_pt_responsibility := v_copay;
    RAISE LOG 'create_delivery_ticket_presets: Copay: %', v_copay;

    IF p_working_delivery_ticket_id IS NOT NULL THEN
        SELECT
            array_agg(inventory_id),
            array_agg(DISTINCT grdt.form_careplan_order_rx_fk)
        INTO v_existing_inventory_ids,
             v_existing_rx_ids
        FROM vw_delivery_items dti
        INNER JOIN gr_form_careplan_delivery_tick_rx_id_to_careplan_order_rx_id grdt ON grdt.form_careplan_delivery_tick_fk = dti.delivery_ticket_id
        WHERE dti.delivery_ticket_id = p_working_delivery_ticket_id;

        v_existing_rx_ids := ARRAY(SELECT DISTINCT unnest(v_existing_rx_ids || v_rx_ids));
        SELECT
            COALESCE(dt.service_from, CURRENT_DATE) as service_from,
            dt.service_to as service_to,
            COALESCE(dt.confirmed, 'No') = 'Yes' as dt_confirmed
        INTO v_service_from,
             v_service_to,
             v_dt_confirmed
        FROM form_careplan_delivery_tick dt
        WHERE dt.id = p_working_delivery_ticket_id;

        IF v_dt_confirmed THEN
            INSERT INTO dispensing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Delivery ticket is confirmed. Cannot add new items to a confirmed ticket.',
                'Exception in create_delivery_ticket_presets',
                'FUNCTION',
                current_schema(),
                'form_careplan_delivery_tick',
                v_params
            );
        RAISE EXCEPTION 'Delivery ticket is confirmed. Cannot add new items to a confirmed ticket.';
        END IF;
    ELSE
        v_existing_rx_ids := v_rx_ids;
        v_existing_inventory_ids := ARRAY[]::integer[]; -- Initialize to empty array if not existing

        IF p_service_from IS NULL OR p_service_to IS NULL THEN
            INSERT INTO dispensing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Missing Service Dates',
                'Exception in create_delivery_ticket_presets',
                'FUNCTION',
                current_schema(),
                'form_careplan_delivery_tick',
                v_params
            );
            RAISE EXCEPTION 'Service from and to dates are required to create a new delivery ticket.';
        END IF;

        v_service_from := TO_DATE(p_service_from, 'MM/DD/YYYY');
        v_service_to := TO_DATE(p_service_to, 'MM/DD/YYYY');

        SELECT
            COALESCE(SUM(dti.copay), 0)
            INTO v_existing_copay
            FROM vw_delivery_items dti
            WHERE dti.delivery_ticket_id = p_working_delivery_ticket_id;

        v_total_pt_responsibility := v_existing_copay + v_copay;
    END IF;

    RAISE LOG 'create_delivery_ticket_presets: Creating items for Rx IDs: %', v_rx_ids;

    -- Initialize the array
    v_new_dt_items := ARRAY[]::jsonb[];

    -- Process regular Rx items
    FOR v_item_result IN 
        SELECT
            get_delivery_ticket_item_presets(
                rx.site_id,
                rx.patient_id,
                rx.careplan_id,
                rxfi.inventory_id,
                rx.rx_id,
                v_service_from,
                CASE
                    WHEN rx.billing_method = 'Do Not Bill' THEN FALSE
                    ELSE TRUE
                END,
                rx.billing_method::text,
                rxfi.dispense_quantity::numeric,
                'each'::text,
                CASE
                    WHEN rxfi.is_primary_drug = TRUE AND v_copay > 0 THEN v_copay
                    ELSE NULL
                END,
                rx.day_supply,
                rx.insurance_id,
                CASE
                    WHEN rx.billing_method IN ( 'Do Not Bill') THEN FALSE
                    ELSE TRUE
                END
            ) as result
        FROM vw_rx_order rx
        INNER JOIN vw_rx_fill_items rxfi ON rx.rx_id = rxfi.rx_id AND rxfi.fill_status IN ('Ready to Contact', 'Ready to Refill')
        WHERE rx.rx_id IN (SELECT unnest(v_rx_ids)) AND rxfi.inventory_id NOT IN (SELECT unnest(v_existing_inventory_ids))
        GROUP BY rx.site_id, rx.patient_id, rx.careplan_id, rxfi.inventory_id, rx.rx_id, 
                 rx.billing_method, rxfi.dispense_quantity, rxfi.is_primary_drug, rx.day_supply, 
                 rx.insurance_id
    LOOP
        IF v_item_result IS NOT NULL THEN
            v_new_dt_items := v_new_dt_items || (v_item_result::jsonb);
        END IF;
    END LOOP;

    -- Process supply order items
    FOR v_item_result IN 
        SELECT
            get_delivery_ticket_item_presets(
                osi.site_id::integer,
                osi.patient_id::integer,
                osi.careplan_id::integer,
                osi.inventory_id::integer,
                osi.associated_rx_id::integer,
                v_service_from::date,
                CASE
                    WHEN osi.billing_method IN ('Do Not Bill') THEN FALSE
                    ELSE TRUE
                END,
                osi.billing_method::text,
                osi.dispense_quantity::numeric,
                'each'::text,
                NULL::numeric,
                osi.day_supply::integer,
                osi.insurance_id::integer,
                CASE
                    WHEN osi.billing_method IN ('Do Not Bill') THEN FALSE
                    ELSE TRUE
                END,
                osi.part_of_kit::text,
                osi.rental_type::text,
                NULL::integer,
                osi.frequency_code::text
            ) as result
        FROM vw_supply_order_items osi
        WHERE osi.id IN (SELECT unnest(p_supply_order_ids))
        AND (v_existing_inventory_ids IS NULL OR osi.inventory_id NOT IN (SELECT unnest(v_existing_inventory_ids)))
    LOOP
        IF v_item_result IS NOT NULL THEN
            v_new_dt_items := v_new_dt_items || (v_item_result::jsonb);
        END IF;
    END LOOP;

    -- Process supply kit items
    FOR v_item_result IN 
        SELECT
            get_delivery_ticket_item_presets(
                rx.site_id,
                rx.patient_id,
                rx.careplan_id,
                ski.inventory_id,
                rx.rx_id,
                v_service_from,
                CASE
                    WHEN COALESCE(ski.bill, 'No') = 'Yes' THEN TRUE
                    ELSE FALSE
                END,
                CASE
                    WHEN ski.bill = 'Yes' THEN
                        rx.billing_method::text
                END,
                ski.dispense_quantity::numeric,
                'each'::text,
                NULL::numeric,
                rx.day_supply,
                CASE 
                    WHEN ski.bill = 'Yes' THEN COALESCE(rx.supplies_insurance_id, rx.insurance_id)
                    ELSE NULL::integer
                END,
                CASE
                    WHEN rx.billing_method IN ('Do Not Bill') THEN FALSE
                    ELSE TRUE
                END,
                ski.part_of_kit::text
            )  as result
        FROM vw_supply_kit_items ski
        INNER JOIN vw_rx_order rx ON rx.rx_id = p_rx_id
        WHERE ski.supply_kit_item_id IN (SELECT unnest(p_supply_kit_selected_ids))
        AND (v_existing_inventory_ids IS NULL OR ski.inventory_id NOT IN (SELECT unnest(v_existing_inventory_ids)))
    LOOP
        IF v_item_result IS NOT NULL THEN
            v_new_dt_items := v_new_dt_items || (v_item_result::jsonb);
        END IF;
    END LOOP;
    RAISE LOG 'Created items %', v_new_dt_items;

    IF v_supply_kit_billable_id IS NOT NULL THEN 
        SELECT 
            get_delivery_ticket_item_presets(
                rx.site_id,
                rx.patient_id,
                rx.careplan_id,
                v_supply_kit_billable_id,
                rx.rx_id,
                v_service_from,
                'Yes'::text,
                rx.billing_method::text,
                1::numeric,
                'each'::text,
                NULL::numeric,
                rx.day_supply,
                rx.insurance_id,
                CASE
                    WHEN rx.billing_method IN ('Do Not Bill') THEN FALSE
                    ELSE TRUE
                END,
                'Yes'::text
            )  as result
        FROM vw_rx_order rx
        WHERE rx.rx_id = p_rx_id;
        RAISE LOG 'Added supply kit billable item ID %', v_supply_kit_billable_id;
    END IF;

    IF p_working_delivery_ticket_id IS NOT NULL THEN
        SELECT json_build_object(
            'careplan_delivery_tick', json_build_object(
            'rx_id', array_to_json(v_existing_rx_ids),
            'total_pt_responsibility', ROUND(v_total_pt_responsibility, 2)),
            'careplan_dt_item', v_new_dt_items
        ) INTO v_results;
    ELSE
        SELECT json_build_object(
            'careplan_delivery_tick', json_build_object(
            'patient_id', rx.patient_id,
            'careplan_id', rx.careplan_id,
            'status', 'delivery_ticket',
            'missing_signed_dt', 'Yes',
            'site_id', rx.site_id,
            'status', 'delivery_ticket',
            'service_from', p_service_from,
            'service_to', p_service_to,
            'delivery_date', p_service_from,
            'rx_id', array_to_json(v_existing_rx_ids),
            'total_pt_responsibility', ROUND(v_total_pt_responsibility, 2)),
            'careplan_dt_item', v_new_dt_items
        ) INTO v_results
        FROM vw_rx_order rx
        WHERE rx.rx_id = p_rx_id;
    END IF;

    RETURN v_results;

    EXCEPTION WHEN OTHERS THEN
        -- Log error
        INSERT INTO dispensing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in get_delivery_ticket_assessment_presets',
            'FUNCTION',
            current_schema(),
            'form_careplan_delivery_tick',
            json_build_object(
                'function_name', 'get_delivery_ticket_assessment_presets',
                'params', v_params
            )
        );
        
    RAISE WARNING 'Failed to generate delivery ticket presets: %', SQLERRM;
    RETURN NULL;
END;
$BODY$ LANGUAGE plpgsql;
