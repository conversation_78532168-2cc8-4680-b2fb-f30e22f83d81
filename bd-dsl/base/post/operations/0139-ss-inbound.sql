CREATE OR REPLACE FUNCTION parse_ss_inbound(p_ss_log_id INTEGER)
RETURNS TABLE (
    ss_message_id INTEGER,
    error_message TEXT,
    error_json_response JSONB
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_raw_message_json TEXT;
    v_message_json JSONB;
    v_surescripts_message_type TEXT;
    v_extracted_message_id TEXT;
    v_error_text TEXT;
    v_error_context TEXT;

    -- Common data variables (to be passed to helper functions)
    v_header_data JSONB;
    v_patient_data JSONB;
    v_prescriber_data JSONB;
    v_response_target_message_id TEXT;

    -- Variables for form_ss_message fields (will be populated from common data or within helpers)
    v_ss_digital_signature_indicator TEXT;
    v_ss_direction TEXT := 'IN';
    v_ss_sent_dt TIMESTAMP;
    v_ss_priority_flag TEXT;
    v_ss_processed TEXT := 'No';
    v_ss_processed_dt TIMESTAMP;
    v_ss_to TEXT;
    v_ss_from TEXT;
    v_ss_message_id_header TEXT;
    v_ss_related_message_id TEXT;
    v_ss_prescriber_order_number TEXT;
    v_ss_pharmacy_rx_no TEXT;
    v_ss_message_type TEXT;
    v_ss_patient_first_name TEXT;
    v_ss_patient_last_name TEXT;

    -- For capturing results from helper functions
    v_helper_ss_message_id INTEGER;
    v_helper_error_message TEXT;
    v_helper_error_json_response JSONB;

    -- Variables for proper error handling
    v_error_json_response JSONB;
    v_original_header JSONB;
    
    -- Variables for validation checks
    v_site_exists BOOLEAN;
    v_duplicate_message BOOLEAN;
    v_supported_message_type BOOLEAN;
    v_dea_schedule TEXT;
    v_prescriber_dea TEXT;
    v_written_date DATE;
    v_expiration_date DATE;
    v_today DATE := CURRENT_DATE;
    
    -- Loop variable
    r RECORD;

BEGIN
    RAISE LOG 'Starting Surescripts inbound message parsing for ss_log_id: %', p_ss_log_id;

    BEGIN
        SELECT fl.message_json INTO v_raw_message_json FROM form_ss_log fl WHERE fl.id = p_ss_log_id AND fl.deleted IS NOT TRUE AND fl.archived IS NOT TRUE;
        IF v_raw_message_json IS NULL THEN
            v_error_text := 'Surescripts message data not found for processing (Log ID: ' || p_ss_log_id || '). Please check the message log.';
            -- Internal error - no JSON response
            RETURN QUERY SELECT NULL::INTEGER, v_error_text, NULL::JSONB;
        END IF;
        v_message_json := v_raw_message_json::jsonb;
    EXCEPTION
        WHEN OTHERS THEN
            GET STACKED DIAGNOSTICS v_error_text = MESSAGE_TEXT, v_error_context = PG_EXCEPTION_CONTEXT;
            RAISE LOG 'Error retrieving/casting message_json for ss_log_id: %. Error: %, Context: %', p_ss_log_id, v_error_text, v_error_context;
            INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details)
            VALUES (v_error_text, v_error_context, 'DATABASE_OR_JSON_PARSE_ERROR', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_INBOUND_PARSER', jsonb_build_object('raw_json_preview', left(v_raw_message_json, 500)));
            -- Internal error - no JSON response
            RETURN QUERY SELECT NULL::INTEGER, ('Error accessing or parsing Surescripts message data (Log ID: ' || p_ss_log_id || '): ' || v_error_text), NULL::JSONB;
    END;

    RAISE LOG 'Successfully retrieved and cast message_json for ss_log_id: %', p_ss_log_id;

    -- Try to extract header for error response generation
    BEGIN
        v_header_data := v_message_json->'Message'->'Header';
        v_original_header := v_header_data;
        v_ss_to := v_header_data#>>'{To}';
        v_ss_from := v_header_data#>>'{From}';
        v_ss_message_id_header := v_header_data#>>'{MessageID}';
        v_ss_prescriber_order_number := v_header_data#>>'{PrescriberOrderNumber}';
        v_ss_pharmacy_rx_no := v_header_data#>>'{RxReferenceNumber}';
    EXCEPTION WHEN OTHERS THEN
        v_original_header := NULL;
    END;

    SELECT key INTO v_surescripts_message_type FROM jsonb_object_keys(v_message_json->'Message'->'Body') k(key) WHERE key <> ALL(ARRAY['Request', 'Response']);
    IF v_surescripts_message_type IS NULL THEN
        v_error_text := 'Could not determine Surescripts MessageType (Log ID: ' || p_ss_log_id || ').';
        RAISE LOG '%', v_error_text;
        INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details)
        VALUES ('Could not determine Surescripts MessageType', 'Message.Body keys: ' || (v_message_json->'Message'->'Body')::text, 'MESSAGE_TYPE_ERROR', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_INBOUND_PARSER', v_message_json);
        -- Internal error - no JSON response
        RETURN QUERY SELECT NULL::INTEGER, v_error_text, NULL::JSONB;
        RETURN;
    END IF;
    v_ss_message_type := v_surescripts_message_type;
    RAISE LOG 'Surescripts MessageType: % for ss_log_id: %', v_surescripts_message_type, p_ss_log_id;

    -- VALIDATION CHECK 1: Invalid site
    SELECT EXISTS(
        SELECT 1 FROM form_site s 
        WHERE (s.ss_organization_id = v_ss_to OR s.ncpdp_id = v_ss_to) 
        AND s.deleted IS NOT TRUE AND s.archived IS NOT TRUE
    ) INTO v_site_exists;

    IF NOT v_site_exists THEN
        v_error_text := 'Invalid receiving site identifier, does not match any given site at location. Value: ' || v_ss_to || ' Path: Message.Header.To';
        v_error_json_response := jsonb_build_object(
            'Message', jsonb_build_object(
                'Header', jsonb_build_object(
                    'RelatesToMessageID', v_ss_message_id_header,
                    'To', v_ss_from,
                    'From', v_ss_to,
                    'SentTime', TO_CHAR(NOW() AT TIME ZONE 'UTC', 'YYYYMMDDHH24MISS'),
                    'PrescriberOrderNumber', v_ss_prescriber_order_number
                ),
                'Body', jsonb_build_object(
                    'Error', jsonb_build_object(
                        'Code', '601',
                        'DescriptionCode', '210',
                        'Description', v_error_text
                    )
                )
            )
        );
        INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details)
        VALUES (v_error_text, 'Invalid site: ' || v_ss_to, 'INVALID_SITE', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_INBOUND_PARSER', v_message_json);
        RETURN QUERY SELECT NULL::INTEGER, v_error_text, v_error_json_response;
        RETURN;
    END IF;

    -- VALIDATION CHECK 2: Duplicate message
    SELECT EXISTS(
        SELECT 1 FROM form_ss_message msg 
        WHERE msg.message_id = v_ss_message_id_header 
        AND msg.deleted IS NOT TRUE AND msg.archived IS NOT TRUE
    ) INTO v_duplicate_message;
    
    IF v_duplicate_message THEN
        v_error_text := 'Message is a duplicate, did not process. Value: ' || v_ss_message_id_header || ' Path: Message.Header.MessageID';
        v_error_json_response := jsonb_build_object(
            'Message', jsonb_build_object(
                'Header', jsonb_build_object(
                    'RelatesToMessageID', v_ss_message_id_header,
                    'To', v_ss_from,
                    'From', v_ss_to,
                    'SentTime', TO_CHAR(NOW() AT TIME ZONE 'UTC', 'YYYYMMDDHH24MISS'),
                    'PrescriberOrderNumber', v_ss_prescriber_order_number
                ),
                'Body', jsonb_build_object(
                    'Error', jsonb_build_object(
                        'Code', '601',
                        'DescriptionCode', '220',
                        'Description', v_error_text
                    )
                )
            )
        );
        INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details)
        VALUES (v_error_text, 'Duplicate MessageID: ' || v_ss_message_id_header, 'DUPLICATE_MESSAGE', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_INBOUND_PARSER', v_message_json);
        RETURN QUERY SELECT NULL::INTEGER, v_error_text, v_error_json_response;
        RETURN;
    END IF;

    -- VALIDATION CHECK 3: Unsupported message type (skip for CancelRx)
    IF v_surescripts_message_type NOT IN ('CancelRx', 'Status', 'Verify', 'Error') THEN
        WITH site_service_levels AS (
            SELECT CASE sls.code
                WHEN 'New' THEN 'NewRx'
                WHEN 'Refill' THEN 'RxRenewalResponse'
                WHEN 'Change' THEN 'RxChangeResponse'
                ELSE sls.code
            END AS mapped_type
            FROM form_site s
            JOIN gr_form_site_ss_service_level_to_list_ss_service_level_id gr 
                ON gr.form_site_fk = s.id
            JOIN form_list_ss_service_level sls 
                ON sls.code = gr.form_list_ss_service_level_fk
            WHERE (s.ss_organization_id = v_ss_to OR s.ncpdp_id = v_ss_to)
            AND s.deleted IS NOT TRUE AND s.archived IS NOT TRUE
        )
        SELECT EXISTS(
            SELECT 1 FROM site_service_levels 
            WHERE mapped_type = v_surescripts_message_type
        ) INTO v_supported_message_type;
        
        IF NOT v_supported_message_type THEN
            v_error_text := 'Receiver does not support this message type. Value: Message.Body.' || v_surescripts_message_type || ' Path: Message.Body.' || v_surescripts_message_type;
            v_error_json_response := jsonb_build_object(
                'Message', jsonb_build_object(
                    'Header', jsonb_build_object(
                        'RelatesToMessageID', v_ss_message_id_header,
                        'To', v_ss_from,
                        'From', v_ss_to,
                        'SentTime', TO_CHAR(NOW() AT TIME ZONE 'UTC', 'YYYYMMDDHH24MISS'),
                        'PrescriberOrderNumber', v_ss_prescriber_order_number
                    ),
                    'Body', jsonb_build_object(
                        'Error', jsonb_build_object(
                            'Code', '700',
                            'DescriptionCode', '4040',
                            'Description', v_error_text
                        )
                    )
                )
            );
            INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details)
            VALUES (v_error_text, 'Unsupported MessageType: ' || v_surescripts_message_type, 'UNSUPPORTED_MESSAGE_TYPE', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_INBOUND_PARSER', v_message_json);
            RETURN QUERY SELECT NULL::INTEGER, v_error_text, v_error_json_response;
            RETURN;
        END IF;
    END IF;

    IF v_surescripts_message_type IN ('Status', 'Verify', 'Error') THEN
        v_response_target_message_id := v_message_json#>>'{Message,Header,RelatesToMessageID}';
        
        IF v_response_target_message_id IS NULL AND v_surescripts_message_type <> 'Error' THEN
            v_error_text := v_surescripts_message_type || ' received without a related message ID to update (Log ID: ' || p_ss_log_id || ').';
            RAISE LOG '%', v_error_text;
            INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details)
            VALUES (v_surescripts_message_type || ' message without RelatesToMessageID', 'Header: ' || (v_message_json->'Message'->'Header')::text, 'MISSING_RELATED_ID', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_INBOUND_HANDLER', v_message_json);
            -- Internal error - no JSON response
            RETURN QUERY SELECT NULL::INTEGER, v_error_text, NULL::JSONB;
        END IF;

        CASE v_surescripts_message_type
            WHEN 'Status' THEN 
                SELECT * INTO v_helper_ss_message_id, v_helper_error_message FROM _handle_status_response(v_message_json, v_response_target_message_id, p_ss_log_id);
                RETURN QUERY SELECT v_helper_ss_message_id, v_helper_error_message, NULL::JSONB;
            WHEN 'Verify' THEN 
                SELECT * INTO v_helper_ss_message_id, v_helper_error_message FROM _handle_verify_response(v_message_json, v_response_target_message_id, p_ss_log_id);
                RETURN QUERY SELECT v_helper_ss_message_id, v_helper_error_message, NULL::JSONB;
            WHEN 'Error' THEN 
                SELECT * INTO v_helper_ss_message_id, v_helper_error_message, v_helper_error_json_response FROM handle_ss_error_response(v_message_json, v_response_target_message_id, p_ss_log_id);
                RETURN QUERY SELECT v_helper_ss_message_id, v_helper_error_message, v_helper_error_json_response;
        END CASE;
        RETURN;
    END IF;

    v_patient_data := v_message_json->'Message'->'Body'->v_surescripts_message_type->'Patient';
    v_prescriber_data := v_message_json->'Message'->'Body'->v_surescripts_message_type->'Prescriber';
    RAISE NOTICE 'DEBUG: v_patient_data for ss_log_id %: %', p_ss_log_id, v_patient_data;
    v_extracted_message_id := v_header_data#>>'{MessageID}';
    v_ss_digital_signature_indicator := 
        CASE v_header_data#>>'{DigitalSignatureIndicator}'
            WHEN 'Yes' THEN 'true' WHEN 'No' THEN 'false' WHEN 'true' THEN 'true' WHEN 'false' THEN 'false' ELSE NULL END;
    v_ss_sent_dt := (v_header_data#>>'{SentTime}')::TIMESTAMP;
    v_ss_related_message_id := v_header_data#>>'{RelatesToMessageID}';
    v_ss_priority_flag := jsonb_extract_path_text(v_message_json, 'Message', 'Body', v_surescripts_message_type, 'UrgencyIndicatorCode');

    -- VALIDATION CHECK 4 & 5: For NewRx - check DEA for controlled substances and expiration dates
    IF v_surescripts_message_type = 'NewRx' THEN
        -- Check DEA schedule
        v_dea_schedule := v_message_json#>>'{Message,Body,NewRx,MedicationPrescribed,DrugCoded,DEASchedule,Code}';
        v_prescriber_dea := v_message_json#>>'{Message,Body,NewRx,Prescriber,NonVeterinarian,Identification,DEANumber}';
        
        IF v_dea_schedule IS NOT NULL AND v_dea_schedule != '' AND (v_prescriber_dea IS NULL OR v_prescriber_dea = '') THEN
            v_error_text := 'Prescriber DEA number is missing but medication is a controlled substance. Value: Message.Body.NewRx.Prescriber.NonVeterinarian.Identification.DEANumber Path: Message.Body.NewRx.Prescriber.NonVeterinarian.Identification.DEANumber';
            v_error_json_response := jsonb_build_object(
                'Message', jsonb_build_object(
                    'Header', jsonb_build_object(
                        'RelatesToMessageID', v_ss_message_id_header,
                        'To', v_ss_from,
                        'From', v_ss_to,
                        'SentTime', TO_CHAR(NOW() AT TIME ZONE 'UTC', 'YYYYMMDDHH24MISS'),
                        'PrescriberOrderNumber', v_ss_prescriber_order_number
                    ),
                    'Body', jsonb_build_object(
                        'Error', jsonb_build_object(
                            'Code', '601',
                            'DescriptionCode', '210',
                            'Description', v_error_text
                        )
                    )
                )
            );
            INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details)
            VALUES (v_error_text, 'DEA Schedule: ' || v_dea_schedule || ', Prescriber DEA: NULL', 'MISSING_DEA', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_INBOUND_PARSER', v_message_json);
            RETURN QUERY SELECT NULL::INTEGER, v_error_text, v_error_json_response;
            RETURN;
        END IF;
        
        -- Check written date and expiration date
        v_written_date := (COALESCE(
            v_message_json#>>'{Message,Body,NewRx,MedicationPrescribed,WrittenDate,Date}',
            v_message_json#>>'{Message,Body,NewRx,MedicationPrescribed,WrittenDate,DateTime}'
        ))::DATE;
        
        -- Check for expiration date in OtherMedicationDate array
        FOR r IN SELECT * FROM jsonb_array_elements(v_message_json#>'{Message,Body,NewRx,MedicationPrescribed,OtherMedicationDate}')
        LOOP
            IF r#>>'{OtherMedicationDateQualifier}' = 'ExpirationDate' THEN
                v_expiration_date := (COALESCE(r#>>'{OtherMedicationDate,Date}', r#>>'{OtherMedicationDate,DateTime}'))::DATE;
                EXIT;
            END IF;
        END LOOP;
        
        IF v_written_date IS NOT NULL AND v_written_date < (v_today - INTERVAL '365 days')::DATE THEN
            v_error_text := 'This prescription has already expired, the written date is > 365 days ago. Value: ' || v_written_date || ' Path: Message.Body.NewRx.MedicationPrescribed.WrittenDate.Date';
            v_error_json_response := jsonb_build_object(
                'Message', jsonb_build_object(
                    'Header', jsonb_build_object(
                        'RelatesToMessageID', v_ss_message_id_header,
                        'To', v_ss_from,
                        'From', v_ss_to,
                        'SentTime', TO_CHAR(NOW() AT TIME ZONE 'UTC', 'YYYYMMDDHH24MISS'),
                        'PrescriberOrderNumber', v_ss_prescriber_order_number
                    ),
                    'Body', jsonb_build_object(
                        'Error', jsonb_build_object(
                            'Code', '601',
                            'DescriptionCode', '210',
                            'Description', v_error_text
                        )
                    )
                )
            );
            INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details)
            VALUES (v_error_text, 'Written Date: ' || v_written_date, 'EXPIRED_PRESCRIPTION', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_INBOUND_PARSER', v_message_json);
            RETURN QUERY SELECT NULL::INTEGER, v_error_text, v_error_json_response;
            RETURN;
        END IF;
        
        IF v_expiration_date IS NOT NULL AND v_expiration_date <= v_today THEN
            v_error_text := 'This prescription has already expired. Value: ' || v_expiration_date || ' Path: Message.Body.NewRx.MedicationPrescribed.OtherMedicationDate.OtherMedicationDate.Date';
            v_error_json_response := jsonb_build_object(
                'Message', jsonb_build_object(
                    'Header', jsonb_build_object(
                        'RelatesToMessageID', v_ss_message_id_header,
                        'To', v_ss_from,
                        'From', v_ss_to,
                        'SentTime', TO_CHAR(NOW() AT TIME ZONE 'UTC', 'YYYYMMDDHH24MISS'),
                        'PrescriberOrderNumber', v_ss_prescriber_order_number
                    ),
                    'Body', jsonb_build_object(
                        'Error', jsonb_build_object(
                            'Code', '601',
                            'DescriptionCode', '210',
                            'Description', v_error_text
                        )
                    )
                )
            );
            INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details)
            VALUES (v_error_text, 'Expiration Date: ' || v_expiration_date, 'EXPIRED_PRESCRIPTION', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_INBOUND_PARSER', v_message_json);
            RETURN QUERY SELECT NULL::INTEGER, v_error_text, v_error_json_response;
            RETURN;
        END IF;
    END IF;

    -- VALIDATION CHECK 6: For RxRenewalResponse with controlled substance - check digital signature
    IF v_surescripts_message_type = 'RxRenewalResponse' THEN
        v_dea_schedule := v_message_json#>>'{Message,Body,RxRenewalResponse,MedicationResponse,DrugCoded,DEASchedule,Code}';
        
        IF v_dea_schedule IS NOT NULL AND v_dea_schedule != '' AND v_ss_digital_signature_indicator != 'true' THEN
            v_error_text := 'This prescription contains a controlled substance but is missing a digital signature. Path: Message.Header.DigitalSignature.DigitalSignatureIndicator';
            v_error_json_response := jsonb_build_object(
                'Message', jsonb_build_object(
                    'Header', jsonb_build_object(
                        'RelatesToMessageID', v_ss_message_id_header,
                        'To', v_ss_from,
                        'From', v_ss_to,
                        'SentTime', TO_CHAR(NOW() AT TIME ZONE 'UTC', 'YYYYMMDDHH24MISS'),
                        'RxReferenceNumber', v_ss_pharmacy_rx_no
                    ),
                    'Body', jsonb_build_object(
                        'Error', jsonb_build_object(
                            'Code', '601',
                            'DescriptionCode', '210',
                            'Description', v_error_text
                        )
                    )
                )
            );
            INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details)
            VALUES (v_error_text, 'DEA Schedule: ' || v_dea_schedule || ', Digital Signature: ' || COALESCE(v_ss_digital_signature_indicator, 'NULL'), 'MISSING_DIGITAL_SIGNATURE', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_INBOUND_PARSER', v_message_json);
            RETURN QUERY SELECT NULL::INTEGER, v_error_text, v_error_json_response;
            RETURN;
        END IF;
    END IF;

    CASE v_surescripts_message_type
        WHEN 'NewRx' THEN
            BEGIN
                SELECT * INTO v_helper_ss_message_id, v_helper_error_message FROM _parse_and_insert_newrx(p_ss_log_id, v_message_json, v_surescripts_message_type, v_header_data, v_patient_data, v_prescriber_data, v_ss_priority_flag);
                RETURN QUERY SELECT v_helper_ss_message_id, v_helper_error_message, NULL::JSONB;
            EXCEPTION WHEN OTHERS THEN
                GET STACKED DIAGNOSTICS v_error_text = MESSAGE_TEXT;
                -- Internal processing error - no JSON response
                RETURN QUERY SELECT NULL::INTEGER, v_error_text, NULL::JSONB;
            END;
        WHEN 'RxRenewalResponse' THEN
            BEGIN
                SELECT * INTO v_helper_ss_message_id, v_helper_error_message FROM _parse_and_insert_rxrenewalresponse(p_ss_log_id, v_message_json, v_surescripts_message_type, v_header_data, v_patient_data, v_prescriber_data, v_ss_priority_flag);
                RETURN QUERY SELECT v_helper_ss_message_id, v_helper_error_message, NULL::JSONB;
            EXCEPTION WHEN OTHERS THEN
                GET STACKED DIAGNOSTICS v_error_text = MESSAGE_TEXT;
                -- Internal processing error - no JSON response
                RETURN QUERY SELECT NULL::INTEGER, v_error_text, NULL::JSONB;
            END;
        WHEN 'RxChangeResponse' THEN
            BEGIN
                SELECT * INTO v_helper_ss_message_id, v_helper_error_message FROM _parse_and_insert_rxchangeresponse(p_ss_log_id, v_message_json, v_surescripts_message_type, v_header_data, v_patient_data, v_prescriber_data, v_ss_priority_flag);
                RETURN QUERY SELECT v_helper_ss_message_id, v_helper_error_message, NULL::JSONB;
            EXCEPTION WHEN OTHERS THEN
                GET STACKED DIAGNOSTICS v_error_text = MESSAGE_TEXT;
                -- Internal processing error - no JSON response
                RETURN QUERY SELECT NULL::INTEGER, v_error_text, NULL::JSONB;
            END;
        WHEN 'CancelRx' THEN
            BEGIN
                SELECT * INTO v_helper_ss_message_id, v_helper_error_message FROM _parse_and_insert_cancelrx(p_ss_log_id, v_message_json, v_surescripts_message_type, v_header_data, v_patient_data, v_prescriber_data, v_ss_priority_flag);
                RETURN QUERY SELECT v_helper_ss_message_id, v_helper_error_message, NULL::JSONB;
            EXCEPTION WHEN OTHERS THEN
                GET STACKED DIAGNOSTICS v_error_text = MESSAGE_TEXT;
                -- Internal processing error - no JSON response
                RETURN QUERY SELECT NULL::INTEGER, v_error_text, NULL::JSONB;
            END;
        ELSE
            v_error_text := 'Surescripts message processing for type ' || v_surescripts_message_type || ' is not yet fully implemented (Log ID: ' || p_ss_log_id || ').';
            RAISE LOG '%', v_error_text;
            INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details)
            VALUES ('Unhandled Surescripts MessageType for full parsing', 'MessageType: ' || v_surescripts_message_type, 'UNHANDLED_MESSAGE_TYPE', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_INBOUND_PARSER', v_message_json);
            -- Internal error - no JSON response
            RETURN QUERY SELECT NULL::INTEGER, v_error_text, NULL::JSONB;
            RETURN;
    END CASE;

EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_error_text = MESSAGE_TEXT, v_error_context = PG_EXCEPTION_CONTEXT;
        RAISE LOG 'Unhandled error in parse_ss_inbound for ss_log_id: %. Error: %, Context: %', p_ss_log_id, v_error_text, v_error_context;
        INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details)
        VALUES (v_error_text, v_error_context, 'UNHANDLED_EXCEPTION', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_INBOUND_PARSER', v_message_json);
        
        -- Internal error - no JSON response
        RETURN QUERY SELECT NULL::INTEGER, ('An unexpected error occurred during Surescripts message processing (Log ID: ' || p_ss_log_id || '): ' || v_error_text), NULL::JSONB;
END;
$$;
