DO $$ BEGIN
  PERFORM drop_all_function_signatures('prevent_update_if_void_function');
END $$;
CREATE OR REPLACE FUNCTION prevent_update_if_void_function()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);
    IF (COALESCE(OLD.void,'No') = 'Yes' AND COALESCE(NEW.void,'No') = 'Yes') THEN
        RAISE EXCEPTION 'Updates are not allowed if void is set to Yes';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to the relevant tables
CREATE OR REPLACE TRIGGER prevent_update_form_med_claim
BEFORE UPDATE ON form_med_claim
FOR EACH ROW
EXECUTE FUNCTION prevent_update_if_void_function();

CREATE OR REPLACE TRIGGER prevent_update_form_med_claim_1500
BEFORE UPDATE ON form_med_claim_1500
FOR EACH ROW
EXECUTE FUNCTION prevent_update_if_void_function();

CREATE OR REPLACE TRIGGER prevent_update_form_ncpdp
BEFORE UPDATE ON form_ncpdp
FOR EACH ROW
EXECUTE FUNCTION prevent_update_if_void_function();

CREATE OR REPLACE TRIGGER prevent_update_form_careplan_order
BEFORE UPDATE ON form_careplan_order
FOR EACH ROW
EXECUTE FUNCTION prevent_update_if_void_function();

CREATE OR REPLACE TRIGGER prevent_update_form_careplan_delivery_tick
BEFORE UPDATE ON form_careplan_delivery_tick
FOR EACH ROW
EXECUTE FUNCTION prevent_update_if_void_function();

CREATE OR REPLACE TRIGGER prevent_update_form_receipt_adjustment
BEFORE UPDATE ON form_receipt_adjustment
FOR EACH ROW
EXECUTE FUNCTION prevent_update_if_void_function();

CREATE OR REPLACE TRIGGER prevent_update_form_receipt_po
BEFORE UPDATE ON form_receipt_po
FOR EACH ROW
EXECUTE FUNCTION prevent_update_if_void_function();

CREATE OR REPLACE TRIGGER prevent_update_form_receipt_transfer
BEFORE UPDATE ON form_receipt_transfer
FOR EACH ROW
EXECUTE FUNCTION prevent_update_if_void_function();

CREATE OR REPLACE TRIGGER prevent_update_form_billing_closing
BEFORE UPDATE ON form_billing_closing
FOR EACH ROW
EXECUTE FUNCTION prevent_update_if_void_function();
