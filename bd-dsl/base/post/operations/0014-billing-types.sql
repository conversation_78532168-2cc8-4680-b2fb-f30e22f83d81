
DO $$ 
BEGIN
    -- Create type for reject codes if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'reject_code_type') THEN
        CREATE TYPE reject_code_type AS (
            reject_code text,
            reject_rank integer
        );
    END IF;

    -- Create type for other amount paid if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'oth_amt_type') THEN
        CREATE TYPE oth_amt_type AS (
            other_amt_qualifier text,
            other_amt_paid numeric
        );
    END IF;

    -- Create type for benefit amount if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'bft_amt_type') THEN
        CREATE TYPE bft_amt_type AS (
            benefit_stage_qualifier text,
            benefit_stage_amount numeric
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'payer_split_settings') THEN
        CREATE TYPE payer_split_settings AS (
            split_all boolean,
            split_by_medid boolean,
            split_by_rx boolean,
            split_by_supplies boolean,
            split_by_dme boolean,
            split_by_pa boolean
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'charge_line') THEN
        CREATE TYPE charge_line AS (
            site_id integer,
            patient_id integer,
            insurance_id integer,
            payer_id integer,
            inventory_id integer,
            shared_contract_id integer,
            is_dirty text,
            charge_no text,
            parent_charge_no text,
            master_charge_no text,
            compound_no text,
            ticket_no text,
            ticket_item_no text,
            rental_id integer,
            order_rx_id integer,
            is_primary_drug text,
            is_primary_drug_ncpdp text,
            rx_no text,
            inventory_type_filter text[],
            inventory_type text,
            revenue_code_id text,
            hcpc_code text,
            ndc text,
            formatted_ndc text,
            gcn_seqno text,
            charge_quantity numeric,
            charge_quantity_ea numeric,
            hcpc_quantity numeric,
            hcpc_unit text,
            metric_quantity numeric,
            charge_unit text,
            bill_quantity numeric,
            metric_unit_each numeric,
            billing_unit_id text,
            billing_method_id text,
            pricing_source text,
            billed numeric,
            calc_billed_ea numeric,
            expected numeric,
            calc_expected_ea numeric,
            list_price numeric,
            calc_list_ea numeric,
            total_cost numeric,
            gross_amount_due numeric,
            incv_amt_sub numeric,
            copay numeric,
            calc_cost_ea numeric,
            total_adjusted numeric,
            total_balance_due numeric,
            dispense_fee numeric,
            pt_pd_amt_sub numeric,
            encounter_id integer,
            description text,
            upc text,
            upin text,
            cost_basis text,
            awp_price numeric,
            modifier_1 text,
            modifier_2 text,
            modifier_3 text,
            modifier_4 text,
            rental_type text,
            frequency_code text,
            fill_number integer,
            paid numeric,
            date_of_service date,
            date_of_service_end date
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'charge_line_with_split') THEN
        CREATE TYPE charge_line_with_split AS (
            id integer,
            calc_invoice_split_no text,
            site_id integer,
            patient_id integer,
            insurance_id integer,
            payer_id integer,
            inventory_id integer,
            shared_contract_id integer,
            is_dirty text,
            charge_no text,
            parent_charge_no text,
            master_charge_no text,
            compound_no text,
            ticket_no text,
            ticket_item_no text,
            rental_id integer,
            order_rx_id integer,
            is_primary_drug text,
            is_primary_drug_ncpdp text,
            rx_no text,
            inventory_type_filter text[],
            inventory_type text,
            revenue_code_id text,
            hcpc_code text,
            ndc text,
            formatted_ndc text,
            gcn_seqno text,
            charge_quantity numeric,
            charge_quantity_ea numeric,
            hcpc_quantity numeric,
            hcpc_unit text,
            metric_quantity numeric,
            charge_unit text,
            bill_quantity numeric,
            metric_unit_each numeric,
            billing_unit_id text,
            billing_method_id text,
            pricing_source text,
            billed numeric,
            calc_billed_ea numeric,
            expected numeric,
            calc_expected_ea numeric,
            list_price numeric,
            calc_list_ea numeric,
            total_cost numeric,
            gross_amount_due numeric,
            incv_amt_sub numeric,
            copay numeric,
            calc_cost_ea numeric,
            total_adjusted numeric,
            total_balance_due numeric,
            dispense_fee numeric,
            pt_pd_amt_sub numeric,
            encounter_id integer,
            description text,
            upc text,
            upin text,
            cost_basis text,
            awp_price numeric,
            modifier_1 text,
            modifier_2 text,
            modifier_3 text,
            modifier_4 text,
            rental_type text,
            frequency_code text,
            fill_number integer,
            paid numeric,
            date_of_service date,
            date_of_service_end date
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_patient') THEN
        CREATE TYPE ncpdp_patient AS ( 
            transaction_code text,
            patient_id integer,
            insurance_id integer,
            pt_rel_code text,
            payer_type_id text,
            patient_id_qualifier text,
            patient_claim_id text,
            patient_date_of_birth text,
            patient_gender_code text,
            patient_first_name text,
            patient_last_name text,
            patient_phone text,
            patient_email_address text,
            patient_street_address text,
            patient_city_address text,
            patient_state text,
            patient_zip text,
            place_of_service text,
            patient_residence text,
            pregnancy_indicator text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_prescriber') THEN
        CREATE TYPE ncpdp_prescriber AS (
        transaction_code text,
        physician_id integer,
        dr_id_qualifier text,
        dr_id text,
        dr_last_name text,
        dr_first_name text,
        dr_street_address text,
        dr_city text,
        dr_state text,
        dr_zip text,
        dr_phone text,
        primary_physician_id integer,
        pri_dr_id_qualifier text,
        pri_dr_id text,
        pri_dr_last_name text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_pharmacy') THEN
        CREATE TYPE ncpdp_pharmacy AS (
        site_id integer,
        provider_id_qualifier text,
        provider_id text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_insurance') THEN
        CREATE TYPE ncpdp_insurance AS (
            transaction_code text,
            insurance_id integer,
            payer_type_id text,
            card_holder_id text,
            pt_rel_code text,
            card_holder_first_name text,
            card_holder_last_name text,
            plan_id text,
            group_id text,
            person_code text,
            home_plan text,
            medigap_id text,
            mcd_indicator text,
            dr_accept_indicator text,
            elig_clar_code text,
            partd_facility text,
            mcd_id_no text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_dx') THEN
        CREATE TYPE ncpdp_dx AS (
        patient_id integer,
        dx_id integer,
        dx_code_qualifier text,
        dx_code text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_measurement') THEN
        CREATE TYPE ncpdp_measurement AS (
        patient_id integer,
        measurement_date text,
        measurement_time text,
        measurement_dimension text,
        measurement_unit text,
        measurement_value text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_clinical') THEN
        CREATE TYPE ncpdp_clinical AS (
        patient_id integer,
        subform_diagnosis ncpdp_dx[],
        subform_measurement ncpdp_measurement[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_dur_item') THEN
        CREATE TYPE ncpdp_dur_item AS (
            patient_id integer,
            transaction_code text,
            rsn_for_svc_code text,
            prf_svc_code text,
            rst_svc_code text,
            dur_pps_loe text,
            co_agt_itm_id integer,
            co_agt_id_qualifier text,
            co_agt_id text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_dur') THEN
        CREATE TYPE ncpdp_dur AS (
            patient_id integer,
            transaction_code text,
            subform_dur ncpdp_dur_item[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_claim') THEN
        CREATE TYPE ncpdp_claim AS (
            is_test text,
            transaction_code text,
            rx_svc_no_ref_qualifier text,
            rx_svc_no text,
            fill_number integer,
            sched_rx_no text,
            patient_id integer,
            order_id integer,
            insurance_id integer,
            payer_id integer,
            product_id integer,
            service_id integer,
            prod_svc_id_qualifier text,
            prod_svc_id text,
            quantity_dispensed numeric,
            unit_of_measure text,
            day_supply integer,
            number_of_refills_authorized integer,
            daw_code text,
            date_rx_written text,
            quantity_prescribed numeric,
            admin_route text,
            pa_id integer,
            pa_type_code text,
            pa_no_submitted text,
            compound_code text,
            compound_type text,
            rx_origin_code text,
            sub_clar_code text[],
            other_coverage_code text,
            require_other_coverage_code text,
            sp_pk_indicator text,
            level_of_service text,
            pt_assign_indicator text,
            pharmacy_service_type text,
            proc_mod_code text[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_opaid') THEN
        CREATE TYPE ncpdp_opaid AS (
            o_amt_sub_qualifier text,
            o_sub_sub numeric
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_pricing') THEN
        CREATE TYPE ncpdp_pricing AS (
            is_test text,
            parent_claim_no text,
            transaction_code text,
            payer_id integer,
            ing_cst_sub numeric,
            disp_fee_sub numeric,
            pro_svc_fee_sub numeric,
            pt_pd_amt_sub numeric,
            u_and_c_charge numeric,
            incv_amt_sub numeric,
            flat_tax_amt numeric,
            gross_amount_due numeric,
            cost_basis text,
            subform_oclaim ncpdp_opaid[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_narrative') THEN
        CREATE TYPE ncpdp_narrative AS (
            narrative_message text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_cob_opayer') THEN
        CREATE TYPE ncpdp_cob_opayer AS (
            patient_id integer,
            pinsurance_id integer,
            insurance_id integer,
            payer_id integer,
            other_coverage_type text,
            internal_control_number text,
            other_id_qualifier text,
            other_id text,
            other_date text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_cob_opaid') THEN
        CREATE TYPE ncpdp_cob_opaid AS (
            paid_qualifier text,
            other_payer_amount_paid numeric
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_cob_ptresp') THEN
        CREATE TYPE ncpdp_cob_ptresp AS (
            pt_resp_amt_qualifier text,
            pt_resp_amt numeric
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_cob_benefit') THEN
        CREATE TYPE ncpdp_cob_benefit AS (
            benefit_stage_qualifier text,
            benefit_stage_amount numeric
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_cob') THEN
        CREATE TYPE ncpdp_cob AS (
            patient_id integer,
            transaction_code text,
            pinsurance_id integer,
            subform_opayer ncpdp_cob_opayer[],
            other_reject_code text[],
            subform_paid ncpdp_cob_opaid[],
            subform_resp ncpdp_cob_ptresp[],
            subform_benefit ncpdp_cob_benefit[],
            paid_total numeric,
            pt_responsability_total numeric
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ncpdp_record') THEN
        CREATE TYPE ncpdp_record AS (
            is_test text,
            claim_no text,
            parent_claim_no text,
            never_comp_seg text,
            order_id integer,
            order_no text,
            order_item_id integer,
            orderp_item_id integer,
            rx_no text,
            comp_dsg_fm_code text,
            comp_disp_unit text,
            inventory_id integer,
            substatus_id text,
            site_id integer,
            patient_id integer,
            insurance_id integer,
            payer_id integer,
            software_vendor_id text,
            version_number text,
            svc_prov_id_qualifier text,
            svc_prov_id text,
            transaction_code text,
            bin_number text,
            process_control_number text,
            date_of_service date,
            segment_patient ncpdp_patient[],
            segment_prescriber ncpdp_prescriber[],
            segment_pharmacy ncpdp_pharmacy[],
            segment_insurance ncpdp_insurance[],
            segment_claim ncpdp_claim[],
            segment_pricing ncpdp_pricing[],
            segment_clinical ncpdp_clinical[],
            segment_narrative ncpdp_narrative[],
            segment_cob ncpdp_cob[],
            tabif_cob text,
            segment_dur ncpdp_dur[],
            billed numeric,
            expected numeric,
            cost numeric
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'create_test_claim_result') THEN
        CREATE TYPE create_test_claim_result AS (
            ncpdp_claim ncpdp_record,
            charge_lines charge_line_with_split[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'insurance_invoice') THEN
        CREATE TYPE insurance_invoice AS (
            is_dirty text,
            series_uuid text,
            invoice_no text,
            master_invoice_no text,
            parent_invoice_no text,
            site_id integer,
            rx_id integer[],
            delivery_ticket_id integer,
            patient_id integer,
            insurance_id integer,
            payer_id integer,
            next_insurance_id integer,
            on_hold text,
            type_id text,
            status text,
            invoice_type text,
            account_id integer,
            bill_for_denial text,
            billed_datetime timestamp,
            billing_method_id text,
            date_of_service date,
            date_of_service_end date,
            subform_pharmacy ncpdp_record[],
            total_billed numeric,
            total_expected numeric,
            total_cost numeric,
            total_pt_pay numeric,
            total_balance_due numeric
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'dt_exception') THEN
        CREATE TYPE dt_exception AS (
            description text,
            dt_exception_id text,
            exception_by text,
            exception_datetime text,
            inventory_id integer,
            billed_inventory_id integer,
            invoice_id integer,
            dispense_quantity numeric,
            bill_quantity numeric,
            expected numeric,
            copay numeric,
            bill_insurance_id integer,
            expected_insurance_id integer
        );
    END IF;
END $$;
