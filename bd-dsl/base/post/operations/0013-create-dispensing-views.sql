-- View for delivery ticket items with all necessary joins
CREATE OR REPLACE VIEW vw_delivery_ticket_pulled_items AS
WITH scanned_items AS (
    SELECT
        dt.id as delivery_ticket_id,
        dt.ticket_no,
        dt.site_id,
        dt.patient_id,
        dt.void,
        dt.tech_verified,
        dt.verified,
        dt.confirmed,
        dti.id as dt_item_id,
        dti.ticket_item_no,
        dti.insurance_id,
        dti.rx_id,
        dti.rental_id,
        dti.last_through_date,
        CASE WHEN COALESCE(dti.is_primary_drug, 'No') = 'Yes' THEN TRUE ELSE FALSE END as is_primary_drug,
        wtp.inventory_id,
        wtp.pulled_quantity as dispense_quantity,
        rx.rx_no,
        rx.next_fill_number,
        rx.template_type,
        inv.type as inventory_type,
        inv.medid,
        inv.gcn_seqno,
        CASE
            WHEN inv.type IN ('Drug', 'Compound') THEN COALESCE(coi.drug_pa_id, opi.drug_pa_id)
            WHEN inv.type = 'Equipment Rental' THEN cor.rental_pa_id
            ELSE NULL
        END as pa_id,
        CASE
            WHEN COALESCE(dt.void, 'No') = 'Yes' THEN 'Delivery Ticket is void'
            WHEN COALESCE(dt.tech_verified, 'No') <> 'Yes' THEN 'Delivery Ticket has not been verified by the pharm tech'
            WHEN (dti.dispense_quantity IS NULL OR dti.dispense_quantity <= 0) THEN 'Invalid dispense quantity for delivery ticket item'
            ELSE NULL
        END as error,
        dti.copay,
        dti.print,
        dti.bill
    FROM form_careplan_delivery_tick dt
    INNER JOIN form_careplan_dt_item dti 
        ON dti.ticket_no = dt.ticket_no
        AND dti.archived IS NOT TRUE
        AND dti.deleted IS NOT TRUE
        AND COALESCE(dti.needs_scanned, 'No') = 'Yes'
    INNER JOIN form_careplan_order_rx rx 
        ON rx.id = dti.rx_id
        AND rx.archived IS NOT TRUE
        AND rx.deleted IS NOT TRUE
    CROSS JOIN get_work_ticket_pulled_quantity(dti.ticket_item_no) wtp
    INNER JOIN form_inventory inv 
        ON inv.id = wtp.inventory_id 
        AND inv.archived IS NOT TRUE
        AND inv.deleted IS NOT TRUE
    LEFT JOIN form_careplan_order_item coi 
        ON coi.rx_no = rx.rx_no
        AND coi.archived IS NOT TRUE
        AND coi.deleted IS NOT TRUE
    LEFT JOIN form_careplan_orderp_item opi 
        ON opi.rx_no = rx.rx_no
        AND opi.archived IS NOT TRUE
        AND opi.deleted IS NOT TRUE
    LEFT JOIN form_careplan_order_rental cor 
        ON cor.id = dti.rental_id
        AND cor.archived IS NOT TRUE
        AND cor.deleted IS NOT TRUE
    WHERE dt.archived IS NOT TRUE
    AND dt.deleted IS NOT TRUE
    AND COALESCE(dt.void, 'No') <> 'Yes'
), non_scanned_items AS (
    SELECT
        dt.id as delivery_ticket_id,
        dt.ticket_no,
        dt.site_id,
        dt.patient_id,
        dt.void,
        dt.tech_verified,
        dt.verified,
        dt.confirmed,
        dti.id as dt_item_id,
        dti.ticket_item_no,
        dti.insurance_id,
        dti.rx_id,
        dti.rental_id,
        NULL::date as last_through_date,
        FALSE as is_primary_drug,
        dti.inventory_id,
        dti.dispense_quantity,
        rx.rx_no,
        rx.next_fill_number,
        rx.template_type,
        inv.type as inventory_type,
        NULL::text as medid,
        NULL::text as gcn_seqno,
        CASE
            WHEN inv.type = 'Supply' THEN co.supplies_pa_id
            WHEN inv.type = 'Billable' AND COALESCE(inv.nursing_related, 'No') = 'Yes' THEN co.nursing_pa_id
            ELSE NULL
        END as pa_id,
        CASE
            WHEN COALESCE(dt.void, 'No') = 'Yes' THEN 'Delivery Ticket is void'
            WHEN COALESCE(dt.tech_verified, 'No') <> 'Yes' THEN 'Delivery Ticket has not been verified by the pharm tech'
            WHEN (dti.dispense_quantity IS NULL OR dti.dispense_quantity <= 0) THEN 'Invalid dispense quantity for delivery ticket item'
            ELSE NULL
        END as error,
        dti.copay,
        dti.print,
        dti.bill
    FROM form_careplan_delivery_tick dt
    INNER JOIN form_careplan_dt_item dti 
        ON dti.ticket_no = dt.ticket_no
        AND dti.archived IS NOT TRUE
        AND dti.deleted IS NOT TRUE
        AND COALESCE(dti.part_of_kit, 'No') <> 'Yes'
        AND dti.needs_scanned IS NULL
    INNER JOIN form_careplan_order_rx rx 
        ON rx.id = dti.rx_id
        AND rx.archived IS NOT TRUE
        AND rx.deleted IS NOT TRUE
    INNER JOIN form_careplan_order co 
        ON co.order_no = rx.order_no 
        AND co.archived IS NOT TRUE 
        AND co.deleted IS NOT TRUE
    INNER JOIN form_inventory inv 
        ON inv.id = dti.inventory_id 
        AND inv.archived IS NOT TRUE
        AND inv.deleted IS NOT TRUE
    WHERE dt.archived IS NOT TRUE
    AND dt.deleted IS NOT TRUE
    AND COALESCE(dt.void, 'No') <> 'Yes'
)
SELECT * FROM scanned_items
UNION ALL
SELECT * FROM non_scanned_items;

CREATE INDEX IF NOT EXISTS idx_vw_delivery_ticket_pulled_items_rx_id
ON form_careplan_dt_item(rx_id);

-- Index for the ticket_no field which is frequently joined
CREATE INDEX IF NOT EXISTS idx_vw_delivery_ticket_pulled_items_ticket_no
ON form_careplan_dt_item(ticket_no);

-- Comp
CREATE OR REPLACE VIEW vw_delivery_items AS
    SELECT
        dt.id as delivery_ticket_id,
        dt.ticket_no,
        dt.site_id,
        dt.patient_id,
        dt.careplan_id,
        dt.void,
        dt.ready_to_fill,
        dt.tech_verified,
        dt.verified,
        dt.confirmed,
        dti.id as dt_item_id,
        dti.ticket_item_no,
        dti.insurance_id,
        dti.inventory_id,
        dti.dispense_quantity,
        dti.dispense_unit,
        dti.frequency_code,
        dti.day_supply,
        dti.rx_id,
        dti.rental_id,
        dti.copay,
        CASE WHEN COALESCE(dti.is_primary_drug, 'No') = 'Yes' THEN TRUE ELSE FALSE END as is_primary_drug,
        rx.rx_no,
        rx.next_fill_number,
        rx.template_type,
        inv.type as inventory_type,
        inv.medid,
        inv.gcn_seqno,
        pi.not_covered,
        pi.limits,
        pi.refills_limit,
        pi.unit_limit,
        pi.limit_freq,
        pi.auth_required,
        pi.billable,
        pi.max_rental_claims,
        pi.daily_bill_rental,
        pi.no_recurring_billing,
        pi.required_doc_ids,
        pi.shared_contract_id,
        CASE
            WHEN inv.type IN ('Drug', 'Compound') THEN COALESCE(coi.drug_pa_id, opi.drug_pa_id)
            WHEN inv.type = 'Equipment Rental' THEN cor.rental_pa_id
            WHEN inv.type = 'Supply' THEN co.supplies_pa_id
            WHEN inv.type = 'Billable' AND COALESCE(inv.nursing_related, 'No') = 'Yes' THEN co.nursing_pa_id
            ELSE NULL
        END as pa_id,
        co.id as order_id,
        pi.expected_ea,
        pi.bill_ea,
        COALESCE(dti.dispense_quantity::numeric, 1) * COALESCE(pi.expected_ea::numeric, 0) as expected,
        dt.service_from,
        dt.service_to,
        dti.bill
    FROM form_careplan_delivery_tick dt
    INNER JOIN form_careplan_dt_item dti 
        ON dti.ticket_no = dt.ticket_no
        AND dti.patient_id = dt.patient_id
        AND dti.careplan_id = dt.careplan_id
        AND dti.archived IS NOT TRUE
        AND dti.deleted IS NOT TRUE
    LEFT JOIN get_inventory_pricing(dti.inventory_id, dti.insurance_id, dt.site_id, dt.patient_id) pi ON TRUE
    INNER JOIN form_careplan_order_rx rx 
        ON rx.id = dti.rx_id
        AND rx.archived IS NOT TRUE
        AND rx.deleted IS NOT TRUE
    INNER JOIN form_careplan_order co
    	ON co.order_no = rx.order_no
    	AND co.archived IS NOT TRUE
    	AND co.deleted IS NOT TRUE
    INNER JOIN form_inventory inv 
        ON inv.id = dti.inventory_id 
        AND inv.archived IS NOT TRUE
        AND inv.deleted IS NOT TRUE
    LEFT JOIN form_careplan_order_item coi 
        ON coi.rx_no = rx.rx_no
        AND coi.archived IS NOT TRUE
        AND coi.deleted IS NOT TRUE
    LEFT JOIN form_careplan_orderp_item opi 
        ON opi.rx_no = rx.rx_no
        AND opi.archived IS NOT TRUE
        AND opi.deleted IS NOT TRUE
    LEFT JOIN form_careplan_order_rental cor 
        ON cor.id = dti.rental_id
        AND cor.archived IS NOT TRUE
        AND cor.deleted IS NOT TRUE
    WHERE dt.archived IS NOT TRUE
    AND dt.deleted IS NOT TRUE
    AND COALESCE(dt.void, 'No') <> 'Yes';

CREATE OR REPLACE VIEW vw_dt_nursing_billing_information AS
    SELECT
        dt.id as dt_id,
        corx.id as rx_id,
        co.nursing_insurance_id as insurance_id,
        co.nursing_pa_id as pa_id,
        ins.payer_id,
        binv.billable_code_id as hcpc_code,
        binv.id as billable_inventory_id,
        binv.initial_visit,
        binv.nursing_related,
        binv.nursing_hours
    FROM form_careplan_delivery_tick dt
    INNER JOIN gr_form_careplan_delivery_tick_rx_id_to_careplan_order_rx_id grdt ON grdt.form_careplan_delivery_tick_fk = dt.id
    INNER JOIN form_careplan_order_rx corx ON corx.id = grdt.form_careplan_order_rx_fk
    AND corx.archived IS NOT TRUE
    AND corx.deleted IS NOT TRUE
    INNER JOIN form_careplan_order co ON co.order_no = corx.rx_no
    AND co.archived IS NOT TRUE
    AND co.deleted IS NOT TRUE
    AND COALESCE(co.void, 'No') <> 'Yes'
    LEFT JOIN form_patient_insurance ins ON ins.id = co.nursing_insurance_id
    AND ins.archived IS NOT TRUE
    AND ins.deleted IS NOT TRUE
    LEFT JOIN gr_form_careplan_order_nurse_billable_id_to_inventory_id grnbi ON grnbi.form_careplan_order_fk = co.id
    LEFT JOIN form_inventory binv ON binv.id = grnbi.form_inventory_fk
    AND binv.archived IS NOT TRUE
    AND binv.deleted IS NOT TRUE
    WHERE co.requires_nursing = 'Yes' AND co.nursing_insurance_id IS NOT NULL;

CREATE OR REPLACE VIEW vw_rx_fill_items AS
WITH insurance_info AS (
    SELECT get_next_insurance_id(rx.rx_id::integer, NULL::integer, TRUE::boolean) as insurance_id,
    rx.rx_id
    FROM vw_rx_order rx
),
cmp_items AS (
    SELECT
        rx.rx_id,
        rx.rx_no,
        rx.site_id,
        rx.patient_id,
        inv.id as inventory_id,
        ins.insurance_id,
        cpi.dispense_quantity::numeric as dispense_quantity,
        CASE 
            WHEN cpi.is_primary_ingredient = 'Yes' THEN TRUE 
            ELSE FALSE 
        END as is_primary_drug,
        CASE
            WHEN ins.insurance_id IS NULL THEN 'No insurance found for prescription'
            WHEN rx.status_id = '2' THEN 'Prescription is discontinued'
            WHEN rx.status_id = '3' THEN 'Prescription is no-go'
            WHEN rx.status_id = '4' THEN 'Prescription is on-hold'
            WHEN COALESCE(rx.rx_complete, 'No') <> 'Yes' THEN 'Prescription is not marked complete'
            WHEN rx.expiration_date IS NULL THEN 'Prescription expiration date is missing'
            WHEN rx.expiration_date < CURRENT_DATE THEN 'Prescription has expired'
            WHEN rx.written_date IS NOT NULL AND (CURRENT_DATE - rx.written_date) > 365 THEN 'Prescription was written more than 365 days ago'
            WHEN cpi.id IS NOT NULL AND (cpi.dispense_quantity IS NULL OR cpi.dispense_quantity <= 0) THEN 'Invalid dispense quantity for compound item ' || inv.name
            WHEN COALESCE(inv.active, 'No') <> 'Yes' THEN 'Compound/TPN inventory item is inactive ' || inv.name
        END as error,
        TRUE as is_compound,
        rx.fill_status
        FROM vw_rx_order rx
        INNER JOIN insurance_info ins ON ins.rx_id = rx.rx_id
        INNER JOIN form_careplan_order_rx_cp cpi ON cpi.rx_no = rx.rx_no
        AND cpi.archived IS NOT TRUE
        AND cpi.deleted IS NOT TRUE
        INNER JOIN form_inventory inv ON inv.id = cpi.inventory_id 
        AND inv.archived IS NOT TRUE
        AND inv.deleted IS NOT TRUE
    WHERE rx.template_type IN ('Compound', 'TPN') AND rx.fill_status IN ('Ready to Contact', 'Ready to Refill', 'Order Entry/Setup')
),
high_priced_item AS (
    SELECT high_item.inventory_id,
    rx.rx_no
    FROM vw_rx_order rx
    INNER JOIN insurance_info ins ON ins.rx_id = rx.rx_id
    LEFT JOIN LATERAL (
        SELECT
            inv.id as inventory_id
        FROM form_careplan_order_rx_wt wti
        INNER JOIN form_inventory inv ON inv.id = wti.inventory_id
        AND inv.archived IS NOT TRUE
        AND inv.deleted IS NOT TRUE
        CROSS JOIN get_inventory_pricing(inv.id, ins.insurance_id, rx.site_id, rx.patient_id) pi
        WHERE wti.rx_no = rx.rx_no
        AND wti.archived IS NOT TRUE
        AND wti.deleted IS NOT TRUE
        ORDER BY (pi.bill_ea * wti.dispense_quantity::numeric) DESC
        LIMIT 1
    ) high_item ON TRUE
),
iv_items AS (
  SELECT
    rx.rx_id,
    rx.rx_no,
    rx.site_id,
    rx.patient_id,
    inv.id as inventory_id,
    ins.insurance_id,
    wti.dispense_quantity::numeric as dispense_quantity,
    CASE 
        WHEN high_priced_item.inventory_id = inv.id THEN TRUE 
        ELSE FALSE 
    END as is_primary_drug,
    CASE
      WHEN ins.insurance_id IS NULL THEN 'No insurance found for prescription'
      WHEN rx.status_id = '2' THEN 'Prescription is discontinued'
      WHEN rx.status_id = '3' THEN 'Prescription is no-go'
      WHEN rx.status_id = '4' THEN 'Prescription is on-hold'
      WHEN COALESCE(rx.rx_complete, 'No') <> 'Yes' THEN 'Prescription is not marked complete'
      WHEN rx.expiration_date IS NULL THEN 'Prescription expiration date is missing'
      WHEN rx.next_fill_date IS NULL THEN 'Prescription next fill date is missing'
      WHEN rx.expiration_date < CURRENT_DATE THEN 'Prescription has expired'
      WHEN rx.written_date IS NOT NULL AND (CURRENT_DATE - rx.written_date) > 365 THEN 'Prescription was written more than 365 days ago'
      WHEN wti.id IS NOT NULL AND (wti.dispense_quantity IS NULL OR wti.dispense_quantity <= 0) THEN 'Invalid dispense quantity for work ticket item ' || inv.name
      WHEN COALESCE(inv.active, 'No') <> 'Yes' THEN 'Compound/TPN inventory item is inactive ' || inv.name
    END as error,
    FALSE as is_compound,
    rx.fill_status
  FROM vw_rx_order rx
  INNER JOIN insurance_info ins ON ins.rx_id = rx.rx_id
  INNER JOIN high_priced_item ON high_priced_item.rx_no = rx.rx_no
  INNER JOIN form_careplan_order_rx_wt wti ON wti.rx_no = rx.rx_no
  AND wti.archived IS NOT TRUE
  AND wti.deleted IS NOT TRUE
  INNER JOIN form_inventory inv ON inv.id = wti.inventory_id 
  AND inv.archived IS NOT TRUE
  AND inv.deleted IS NOT TRUE
  WHERE rx.template_type IN ('IV') AND rx.fill_status IN ('Ready to Contact', 'Ready to Refill', 'Order Entry/Setup')
),
po_item AS (
  SELECT
    rx.rx_id,
    rx.rx_no,
    rx.site_id,
    rx.patient_id,
    rx.inventory_id,
    ins.insurance_id,
    rx.dispense_quantity::numeric as dispense_quantity,
    TRUE as is_primary_drug,
    CASE
      WHEN ins.insurance_id IS NULL THEN 'No insurance found for prescription'
      WHEN rx.status_id = '2' THEN 'Prescription is discontinued'
      WHEN rx.status_id = '3' THEN 'Prescription is no-go'
      WHEN rx.status_id = '4' THEN 'Prescription is on-hold'
      WHEN COALESCE(rx.rx_complete, 'No') <> 'Yes' THEN 'Prescription is not marked complete'
      WHEN rx.expiration_date IS NULL THEN 'Prescription expiration date is missing'
      WHEN rx.next_fill_date IS NULL THEN 'Prescription next fill date is missing'
      WHEN rx.expiration_date < CURRENT_DATE THEN 'Prescription has expired'
      WHEN rx.written_date IS NOT NULL AND (CURRENT_DATE - rx.written_date) > 365 THEN 'Prescription was written more than 365 days ago'
      WHEN rx.dispense_quantity IS NULL OR rx.dispense_quantity <= 0 THEN 'Invalid dispense quantity for PO item ' || inv.name
      WHEN COALESCE(inv.active, 'No') <> 'Yes' THEN 'Inventory item is inactive ' || inv.name
    END as error,
    FALSE as is_compound,
    rx.fill_status
  FROM vw_rx_order rx
  INNER JOIN insurance_info ins ON ins.rx_id = rx.rx_id
  INNER JOIN form_inventory inv ON inv.id = rx.inventory_id AND inv.archived IS NOT TRUE AND inv.deleted IS NOT TRUE
  WHERE rx.template_type IN ('Factor', 'Injection', 'PO') AND rx.fill_status IN ('Ready to Contact', 'Ready to Refill', 'Order Entry/Setup')
)
SELECT * FROM cmp_items
UNION ALL
SELECT * FROM iv_items
UNION ALL
SELECT * FROM po_item;

CREATE OR REPLACE FUNCTION get_rx_pricing_json(p_rx_id INTEGER)
RETURNS JSONB AS
$$
DECLARE
  result_json JSONB;
BEGIN
  WITH item_pricing AS (
    SELECT
      inv.name as drug,
      TRIM(CONCAT(pt.mrn, ' - ', COALESCE(pt.firstname,''), ' ', COALESCE(pt.lastname,''))) as patient,
      COALESCE(rx.dispense_quantity::numeric, 1::numeric) as bill_quantity,
      pi.metric_unit_each,
      pi.charge_quantity_ea,
      CASE 
        WHEN pi.metric_unit_each::numeric > 0 THEN COALESCE(rx.dispense_quantity::numeric, 1::numeric) * pi.metric_unit_each::numeric 
        ELSE 0 END as metric_quantity,
      CASE 
        WHEN pi.charge_quantity_ea::numeric > 0 THEN ROUND(COALESCE(rx.dispense_quantity::numeric, 1::numeric) * pi.charge_quantity_ea::numeric, 2)
        ELSE 0
      END as charge_quantity,
      pi.charge_unit,
      pi.billing_unit_id,
      pi.hcpc_unit,
      pi.hcpc_quantity::numeric as hcpc_quantity,
      CASE 
        WHEN pi.expected_copay_ea::numeric > 0 THEN ROUND(COALESCE(rx.dispense_quantity::numeric, 1::numeric) * pi.expected_copay_ea::numeric, 2)
        ELSE 0.00
      END as expected_copay,
      CASE
        WHEN pi.bill_ea::numeric > 0 THEN ROUND(COALESCE(rx.dispense_quantity::numeric, 1::numeric) * pi.bill_ea::numeric, 2)
        ELSE 0.00 
      END as billed,
      CASE 
        WHEN pi.expected_ea::numeric > 0 THEN ROUND(COALESCE(rx.dispense_quantity::numeric, 1::numeric) * pi.expected_ea::numeric, 2) 
        ELSE 0.00
      END as expected,
      pi.pricing_source,
      pc.name as shared_contract,
      py.organization as insurance,
      rx.inventory_id,
      rx.insurance_id,
      rx.patient_id
    FROM vw_rx_fill_items rx
    LEFT JOIN get_inventory_pricing(rx.inventory_id, rx.insurance_id, rx.site_id, rx.patient_id) pi ON TRUE
    LEFT JOIN form_patient_insurance ins ON ins.id = rx.insurance_id
    LEFT JOIN form_payer py ON py.id = ins.payer_id
    LEFT JOIN form_payer_contract pc ON pc.id = pi.shared_contract_id
    INNER JOIN form_inventory inv ON inv.id = rx.inventory_id
    INNER JOIN form_patient pt ON pt.id = rx.patient_id
    WHERE rx.rx_id = p_rx_id
  )
  SELECT 
    jsonb_build_object(
      'patient', ip.patient,
      'insurance', ip.insurance,
      'expected_copay', ROUND(SUM(ip.expected_copay::numeric), 2),
      'total_billed', ROUND(SUM(ip.billed::numeric), 2),
      'total_expected', ROUND(SUM(ip.expected::numeric), 2),
      'subform_item', jsonb_agg(
        jsonb_build_object(
          'drug', ip.drug,
          'bill_quantity', ip.bill_quantity,
          'metric_unit_each', ip.metric_unit_each,
          'charge_quantity_ea', ip.charge_quantity_ea,
          'charge_quantity', ip.charge_quantity,
          'metric_quantity', ip.metric_quantity,
          'hcpc_quantity', ip.hcpc_quantity,
          'hcpc_unit', ip.hcpc_unit,
          'charge_unit', ip.charge_unit,
          'billing_unit_id', ip.billing_unit_id,
          'expected_copay', ip.expected_copay,
          'billed', ip.billed,
          'expected', ip.expected,
          'pricing_source', ip.pricing_source,
          'shared_contract', ip.shared_contract
        )
      )
    ) INTO result_json
  FROM item_pricing ip
  GROUP BY ip.patient, ip.insurance;

  RETURN result_json;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE VIEW vw_supply_order_items AS
  SELECT
    osi.id::integer as id,
    ord.order_no::text as order_no,
    osi.associated_rx_id::integer as associated_rx_id,
    osi.rx_no::text as rx_no,
    COALESCE(osi.ndc, '99999999911')::text as ndc,
    osi.hcpc_code::text as hcpc_code,
    osi.upin::text as upin,
    osi.site_id::integer as site_id,
    osi.rental_type::text as rental_type,
    osi.frequency_code::text as frequency_code,
    osi.patient_id::integer as patient_id,
    osi.inventory_id::integer as inventory_id,
    osi.insurance_id::integer as insurance_id,
    osi.dispense_quantity::numeric as dispense_quantity,
    osi.day_supply::integer as day_supply,
    osi.supplies_pa_id::integer as pa_id,
    TRUE as is_primary_drug,
    CASE
      WHEN osi.insurance_id IS NULL THEN 'No insurance found for order'
      WHEN osi.status_id = '2' THEN 'Order is discontinued'
      WHEN osi.status_id = '3' THEN 'Order is no-go'
      WHEN osi.status_id = '4' THEN 'Order is on-hold'
      WHEN osi.dispense_quantity IS NULL OR osi.dispense_quantity <= 0 THEN 'Invalid dispense quantity for supply item ' || inv.name
      WHEN COALESCE(inv.active, 'No') <> 'Yes' THEN 'Inventory item is inactive ' || inv.name
    END::text as error,
    FALSE as is_compound,
    osi.part_of_kit::text as part_of_kit,
    osi.careplan_id::integer as careplan_id,
    osi.billing_method::text as billing_method
  FROM form_careplan_orders_item osi
  INNER JOIN sf_form_careplan_order_to_careplan_orders_item sfcoi ON sfcoi.form_careplan_orders_item_fk = osi.id
  INNER JOIN form_careplan_order ord ON ord.id = sfcoi.form_careplan_order_fk
  INNER JOIN form_inventory inv ON inv.id = osi.inventory_id AND inv.archived IS NOT TRUE AND inv.deleted IS NOT TRUE;

CREATE OR REPLACE VIEW vw_careplan_order_wf_presets AS
WITH primary_dx AS (
    -- Pre-compute primary diagnoses once
    SELECT DISTINCT ON (patient_id)
        patient_id,
        dx_id AS primary_dx_id,
        id as pt_dx_id
    FROM form_patient_diagnosis
    WHERE COALESCE(active, 'No') = 'Yes' 
      AND archived IS NOT TRUE 
      AND deleted IS NOT TRUE
    ORDER BY patient_id, rank DESC
)
-- First UNION query
SELECT
    co.patient_id,
    co.careplan_id,
    co.id as order_id,
    lt.code as therapy_id,
    NULL as brand_name_id,
    NULL as route_id,
    COALESCE(
        -- Use LEFT JOIN LATERAL instead of set-returning function in WHERE
        ptdx.dx_id,
        pd.primary_dx_id
    ) as dx_id,
    COALESCE(
        ptdx.pt_dx_id,
        pd.pt_dx_id
    ) as pt_dx_id,
    NULL::integer as rx_id,
    NULL::integer as delivery_ticket_id
FROM form_careplan_order co
INNER JOIN gr_form_careplan_order_therapy_id_to_list_therapy_id grtco 
    ON grtco.form_careplan_order_fk = co.id
INNER JOIN form_list_therapy lt 
    ON lt.code = grtco.form_list_therapy_fk
-- Safe way to handle JSON arrays
LEFT JOIN LATERAL (
    SELECT dx.dx_id,
    dx.id as pt_dx_id
    FROM jsonb_array_elements(
        CASE 
            WHEN co.dx_ids IS NULL OR co.dx_ids = '' THEN '[]'::jsonb
            ELSE co.dx_ids::jsonb
        END
    ) AS elem(obj)
    JOIN form_patient_diagnosis dx ON (elem.obj->>'id')::integer = dx.id
    LIMIT 1
) AS ptdx ON true
LEFT JOIN primary_dx pd
    ON pd.patient_id = co.patient_id
WHERE co.archived IS NOT TRUE
  AND co.deleted IS NOT TRUE
  AND COALESCE(co.void, 'No') <> 'Yes'

UNION ALL -- Using UNION ALL instead of UNION can be faster when duplicates aren't a concern

-- Second UNION query
SELECT
    co.patient_id,
    co.careplan_id,
    co.id as order_id,
    coi.therapy_id,
    inv.brand_name_id,
    coi.route_id,
    COALESCE(
        ptdx.dx_id,
        pd.primary_dx_id
    ) as dx_id,
    COALESCE(
        ptdx.pt_dx_id,
        pd.pt_dx_id
    ) as pt_dx_id,
    rx.rx_id,
    rx.delivery_ticket_id
FROM form_careplan_order_item coi
INNER JOIN sf_form_careplan_order_to_careplan_order_item sfcoi 
    ON sfcoi.form_careplan_order_item_fk = coi.id
    AND sfcoi.archive IS NOT TRUE
    AND sfcoi.delete IS NOT TRUE
INNER JOIN form_inventory inv 
    ON inv.id = coi.inventory_id
    AND inv.archived IS NOT TRUE
    AND inv.deleted IS NOT TRUE
INNER JOIN form_careplan_order co 
    ON co.id = sfcoi.form_careplan_order_fk
    AND co.archived IS NOT TRUE
    AND co.deleted IS NOT TRUE
    AND COALESCE(co.void, 'No') <> 'Yes'
LEFT JOIN vw_rx_order rx 
    ON rx.rx_no = coi.rx_no
-- Safe way to handle JSON arrays
LEFT JOIN LATERAL (
    SELECT dx.dx_id,
    dx.id as pt_dx_id
    FROM jsonb_array_elements(
        CASE 
            WHEN coi.dx_ids IS NULL OR coi.dx_ids = '' THEN '[]'::jsonb
            ELSE coi.dx_ids::jsonb
        END
    ) AS elem(obj)
    JOIN form_patient_diagnosis dx ON (elem.obj->>'id')::integer = dx.id
    LIMIT 1
) AS ptdx ON true
LEFT JOIN primary_dx pd
    ON pd.patient_id = co.patient_id
WHERE coi.status_id IN ('1', '5')

UNION ALL -- Using UNION ALL instead of UNION can be faster when duplicates aren't a concern

-- Third UNION query
SELECT
    co.patient_id,
    co.careplan_id,
    co.id as order_id,
    copi.therapy_id,
    inv.brand_name_id,
    copi.route_id,
    COALESCE(
        -- Use LEFT JOIN LATERAL instead of set-returning function in WHERE
        ptdx.dx_id,
        pd.primary_dx_id
    ) as dx_id,
    COALESCE(
        ptdx.pt_dx_id,
        pd.pt_dx_id
    ) as pt_dx_id,
    rx.rx_id,
    rx.delivery_ticket_id
FROM form_careplan_orderp_item copi
INNER JOIN sf_form_careplan_order_to_careplan_orderp_item sfcoi 
    ON sfcoi.form_careplan_orderp_item_fk = copi.id
    AND sfcoi.archive IS NOT TRUE
    AND sfcoi.delete IS NOT TRUE
INNER JOIN form_inventory inv 
    ON inv.id = copi.inventory_id
    AND inv.archived IS NOT TRUE
    AND inv.deleted IS NOT TRUE
INNER JOIN form_careplan_order co 
    ON co.id = sfcoi.form_careplan_order_fk
    AND co.archived IS NOT TRUE
    AND co.deleted IS NOT TRUE
    AND COALESCE(co.void, 'No') <> 'Yes'
LEFT JOIN vw_rx_order rx 
    ON rx.rx_no = copi.rx_no
-- Safe way to handle JSON arrays
LEFT JOIN LATERAL (
    SELECT dx.dx_id,
    dx.id as pt_dx_id
    FROM jsonb_array_elements(
        CASE 
            WHEN copi.dx_ids IS NULL OR copi.dx_ids = '' THEN '[]'::jsonb
            ELSE copi.dx_ids::jsonb
        END
    ) AS elem(obj)
    JOIN form_patient_diagnosis dx ON (elem.obj->>'id')::integer = dx.id
    LIMIT 1
) AS ptdx ON true
LEFT JOIN primary_dx pd
    ON pd.patient_id = co.patient_id
WHERE copi.status_id IN ('1', '5');

CREATE OR REPLACE FUNCTION get_presets_by_rx(p_rx_id integer) 
RETURNS TABLE (
    patient_id integer,
    careplan_id integer,
    order_id integer,
    therapy_id text,
    brand_name_id text,
    route_id text,
    dx_id text
) AS $$
BEGIN
    -- This specialized query retrieves just what you need 
    RETURN QUERY
    WITH primary_dx AS (
        SELECT DISTINCT ON (patient_id)
            pd.patient_id,
            pd.dx_id AS primary_dx_id
        FROM form_patient_diagnosis pd
        JOIN (
            -- First find the patient ID for this rx to limit primary_dx search
            SELECT DISTINCT patient_id
            FROM (
                SELECT co.patient_id
                FROM form_careplan_order_item coi
                JOIN sf_form_careplan_order_to_careplan_order_item sfcoi 
                    ON sfcoi.form_careplan_order_item_fk = coi.id
                JOIN form_careplan_order co 
                    ON co.id = sfcoi.form_careplan_order_fk
                JOIN vw_rx_order rx 
                    ON rx.rx_no = coi.rx_no
                WHERE rx.rx_id = p_rx_id
                
                UNION ALL
                
                SELECT co.patient_id
                FROM form_careplan_orderp_item copi
                JOIN sf_form_careplan_order_to_careplan_orderp_item sfcoi 
                    ON sfcoi.form_careplan_orderp_item_fk = copi.id
                JOIN form_careplan_order co 
                    ON co.id = sfcoi.form_careplan_order_fk
                JOIN vw_rx_order rx 
                    ON rx.rx_id = copi.rx_no
                WHERE rx.rx_id = p_rx_id
            ) p
        ) target_patient 
            ON pd.patient_id = target_patient.patient_id
        WHERE COALESCE(pd.active, 'No') = 'Yes' 
          AND pd.archived IS NOT TRUE 
          AND pd.deleted IS NOT TRUE
        ORDER BY pd.patient_id, pd.rank DESC
    )
    -- Query just the relevant rows
    (
        SELECT
            co.patient_id,
            co.careplan_id,
            co.id as order_id,
            coi.therapy_id,
            inv.brand_name_id,
            coi.route_id,
            COALESCE(ptdx.dx_id, pd.primary_dx_id) as dx_id
        FROM form_careplan_order_item coi
        JOIN sf_form_careplan_order_to_careplan_order_item sfcoi 
            ON sfcoi.form_careplan_order_item_fk = coi.id
            AND sfcoi.archive IS NOT TRUE
            AND sfcoi.delete IS NOT TRUE
        JOIN form_inventory inv 
            ON inv.id = coi.inventory_id
            AND inv.archived IS NOT TRUE
            AND inv.deleted IS NOT TRUE
        JOIN form_careplan_order co 
            ON co.id = sfcoi.form_careplan_order_fk
            AND co.archived IS NOT TRUE
            AND co.deleted IS NOT TRUE
            AND COALESCE(co.void, 'No') <> 'Yes'
        JOIN vw_rx_order rx 
            ON rx.rx_no = coi.rx_no 
            AND rx.rx_id = p_rx_id
        LEFT JOIN LATERAL (
            SELECT dx.dx_id
            FROM jsonb_array_elements(
                CASE 
                    WHEN coi.dx_ids IS NULL OR coi.dx_ids = '' THEN '[]'::jsonb
                    ELSE coi.dx_ids::jsonb
                END
            ) AS elem(obj)
            JOIN form_patient_diagnosis dx ON (elem.obj->>'id')::integer = dx.id
            LIMIT 1
        ) AS ptdx ON true
        LEFT JOIN primary_dx pd
            ON pd.patient_id = co.patient_id
        WHERE coi.status_id IN ('1', '5')
    )
    UNION ALL
    (
        SELECT
            co.patient_id,
            co.careplan_id,
            co.id as order_id,
            copi.therapy_id,
            inv.brand_name_id,
            copi.route_id,
            COALESCE(ptdx.dx_id, pd.primary_dx_id) as dx_id
        FROM form_careplan_orderp_item copi
        JOIN sf_form_careplan_order_to_careplan_orderp_item sfcoi 
            ON sfcoi.form_careplan_orderp_item_fk = copi.id
            AND sfcoi.archive IS NOT TRUE
            AND sfcoi.delete IS NOT TRUE
        JOIN form_inventory inv 
            ON inv.id = copi.inventory_id
            AND inv.archived IS NOT TRUE
            AND inv.deleted IS NOT TRUE
        JOIN form_careplan_order co 
            ON co.id = sfcoi.form_careplan_order_fk
            AND co.archived IS NOT TRUE
            AND co.deleted IS NOT TRUE
            AND COALESCE(co.void, 'No') <> 'Yes'
        JOIN vw_rx_order rx 
            ON rx.rx_id = copi.rx_no 
            AND rx.rx_id = p_rx_id
        LEFT JOIN LATERAL (
            SELECT dx.dx_id
            FROM jsonb_array_elements(
                CASE 
                    WHEN copi.dx_ids IS NULL OR copi.dx_ids = '' THEN '[]'::jsonb
                    ELSE copi.dx_ids::jsonb
                END
            ) AS elem(obj)
            JOIN form_patient_diagnosis dx ON (elem.obj->>'id')::integer = dx.id
            LIMIT 1
        ) AS ptdx ON true
        LEFT JOIN primary_dx pd
            ON pd.patient_id = co.patient_id
        WHERE copi.status_id IN ('1', '5')
    );
END;
$$ LANGUAGE plpgsql;

-- Add essential indexes for the underlying tables
CREATE INDEX IF NOT EXISTS idx_form_patient_diagnosis_patient on form_patient_diagnosis(patient_id, active, archived, deleted, rank);
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_id_archived_deleted on form_careplan_order(id, archived, deleted, void);
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_item_status on form_careplan_order_item(status_id, rx_no) INCLUDE (therapy_id, route_id, inventory_id);
CREATE INDEX IF NOT EXISTS idx_form_careplan_orderp_item_status on form_careplan_orderp_item(status_id, rx_no) INCLUDE (therapy_id, route_id, inventory_id);
CREATE INDEX IF NOT EXISTS idx_inventory_brand on form_inventory(id, brand_name_id) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_sf_order_to_order_item_order_fk on sf_form_careplan_order_to_careplan_order_item(form_careplan_order_fk, form_careplan_order_item_fk) 
    WHERE archive IS NOT TRUE AND delete IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_sf_order_to_orderp_item_order_fk on sf_form_careplan_order_to_careplan_orderp_item(form_careplan_order_fk, form_careplan_orderp_item_fk) 
    WHERE archive IS NOT TRUE AND delete IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_gr_order_therapy on gr_form_careplan_order_therapy_id_to_list_therapy_id(form_careplan_order_fk, form_list_therapy_fk);


CREATE OR REPLACE FUNCTION get_presets_by_order(p_order_id integer) 
RETURNS TABLE (
    patient_id integer,
    careplan_id integer,
    therapy_id text,
    brand_name_id text,
    route_id text,
    dx_id text,
    rx_id integer
) AS $$
BEGIN
    -- This query directly goes after just the needed data
    RETURN QUERY
    WITH primary_dx AS (
        SELECT DISTINCT ON (patient_id)
            pd.patient_id,
            pd.dx_id AS primary_dx_id
        FROM form_patient_diagnosis pd
        JOIN form_careplan_order co ON co.patient_id = pd.patient_id
        WHERE co.id = p_order_id
          AND COALESCE(pd.active, 'No') = 'Yes' 
          AND pd.archived IS NOT TRUE 
          AND pd.deleted IS NOT TRUE
        ORDER BY pd.patient_id, pd.rank DESC
    )
    -- First part: Get therapy from order
    (
        SELECT
            co.patient_id,
            co.careplan_id,
            lt.code as therapy_id,
            NULL as brand_name_id,
            NULL as route_id,
            COALESCE(ptdx.dx_id, pd.primary_dx_id) as dx_id,
            NULL::integer as rx_id
        FROM form_careplan_order co
        JOIN gr_form_careplan_order_therapy_id_to_list_therapy_id grtco 
            ON grtco.form_careplan_order_fk = co.id
        JOIN form_list_therapy lt 
            ON lt.code = grtco.form_list_therapy_fk
        LEFT JOIN LATERAL (
            SELECT dx.dx_id
            FROM jsonb_array_elements(
                CASE 
                    WHEN co.dx_ids IS NULL OR co.dx_ids = '' THEN '[]'::jsonb
                    ELSE co.dx_ids::jsonb
                END
            ) AS elem(obj)
            JOIN form_patient_diagnosis dx ON (elem.obj->>'id')::integer = dx.id
            LIMIT 1
        ) AS ptdx ON true
        LEFT JOIN primary_dx pd
            ON pd.patient_id = co.patient_id
        WHERE co.id = p_order_id
          AND co.archived IS NOT TRUE
          AND co.deleted IS NOT TRUE
          AND COALESCE(co.void, 'No') <> 'Yes'
    )
    
    UNION ALL
    
    -- Second part: Get from order items
    (
        SELECT
            co.patient_id,
            co.careplan_id,
            coi.therapy_id,
            inv.brand_name_id,
            coi.route_id,
            COALESCE(ptdx.dx_id, pd.primary_dx_id) as dx_id,
            rx.rx_id
        FROM form_careplan_order_item coi
        JOIN sf_form_careplan_order_to_careplan_order_item sfcoi 
            ON sfcoi.form_careplan_order_item_fk = coi.id
            AND sfcoi.archive IS NOT TRUE
            AND sfcoi.delete IS NOT TRUE
        JOIN form_inventory inv 
            ON inv.id = coi.inventory_id
            AND inv.archived IS NOT TRUE
            AND inv.deleted IS NOT TRUE
        JOIN form_careplan_order co 
            ON co.id = sfcoi.form_careplan_order_fk
            AND co.id = p_order_id
            AND co.archived IS NOT TRUE
            AND co.deleted IS NOT TRUE
            AND COALESCE(co.void, 'No') <> 'Yes'
        LEFT JOIN vw_rx_order rx 
            ON rx.rx_no = coi.rx_no
        LEFT JOIN LATERAL (
            SELECT dx.dx_id
            FROM jsonb_array_elements(
                CASE 
                    WHEN coi.dx_ids IS NULL OR coi.dx_ids = '' THEN '[]'::jsonb
                    ELSE coi.dx_ids::jsonb
                END
            ) AS elem(obj)
            JOIN form_patient_diagnosis dx ON (elem.obj->>'id')::integer = dx.id
            LIMIT 1
        ) AS ptdx ON true
        LEFT JOIN primary_dx pd
            ON pd.patient_id = co.patient_id
        WHERE coi.status_id IN ('1', '5')
    )
    
    UNION ALL
    
    -- Third part: Get from orderp items
    (
        SELECT
            co.patient_id,
            co.careplan_id,
            copi.therapy_id,
            inv.brand_name_id,
            copi.route_id,
            COALESCE(ptdx.dx_id, pd.primary_dx_id) as dx_id,
            rx.rx_id
        FROM form_careplan_orderp_item copi
        JOIN sf_form_careplan_order_to_careplan_orderp_item sfcoi 
            ON sfcoi.form_careplan_orderp_item_fk = copi.id
            AND sfcoi.archive IS NOT TRUE
            AND sfcoi.delete IS NOT TRUE
        JOIN form_inventory inv 
            ON inv.id = copi.inventory_id
            AND inv.archived IS NOT TRUE
            AND inv.deleted IS NOT TRUE
        JOIN form_careplan_order co 
            ON co.id = sfcoi.form_careplan_order_fk
            AND co.id = p_order_id
            AND co.archived IS NOT TRUE
            AND co.deleted IS NOT TRUE
            AND COALESCE(co.void, 'No') <> 'Yes'
        LEFT JOIN vw_rx_order rx 
            ON rx.rx_id = copi.rx_no
        LEFT JOIN LATERAL (
            SELECT dx.dx_id
            FROM jsonb_array_elements(
                CASE 
                    WHEN copi.dx_ids IS NULL OR copi.dx_ids = '' THEN '[]'::jsonb
                    ELSE copi.dx_ids::jsonb
                END
            ) AS elem(obj)
            JOIN form_patient_diagnosis dx ON (elem.obj->>'id')::integer = dx.id
            LIMIT 1
        ) AS ptdx ON true
        LEFT JOIN primary_dx pd
            ON pd.patient_id = co.patient_id
        WHERE copi.status_id IN ('1', '5')
    );
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE VIEW vw_payer_contract AS
    SELECT py.id as payer_id,
    pc.id as shared_contract_id,
    st.id as site_id
    FROM form_payer py
    INNER JOIN form_site st ON st.archived IS NOT TRUE AND st.deleted IS NOT TRUE
    INNER JOIN gr_form_payer_contract_site_id_to_site_id pcs ON pcs.form_site_fk = st.id
    INNER JOIN sf_form_payer_to_payer_contract spc ON spc.form_payer_fk = py.id AND spc."delete" IS NOT TRUE AND spc.archive IS NOT TRUE  
    INNER JOIN form_payer_contract pc ON pc.id = spc.form_payer_contract_fk AND pcs.form_payer_contract_fk = pc.id AND pc.archived IS NOT TRUE AND pc.deleted IS NOT TRUE
    WHERE py.archived IS NOT TRUE AND py.deleted IS NOT TRUE;

CREATE OR REPLACE VIEW vw_working_delivery_tickets AS
    SELECT
        dt.id as delivery_ticket_id,
        dt.service_from,
        dt.service_to,
        dt.patient_id,
        dt.site_id
    FROM form_careplan_delivery_tick dt
    WHERE dt.archived IS NOT TRUE 
    AND dt.deleted IS NOT TRUE
    AND COALESCE(dt.confirmed, 'No') <> 'Yes'
    AND COALESCE(dt.void, 'No') <> 'Yes';

CREATE OR REPLACE VIEW vw_working_prescriptions AS
    SELECT
        rx.patient_id,
        rx.site_id,
        rx.rx_id,
        rx.fill_status,
        rx.working_dispense_id,
        rx.delivery_ticket_id,
        rx.rx_no,
        rx.last_event_id,
        rx.last_event_datetime,
        rx.last_event_by,
        rx.type_id
    FROM vw_rx_order rx;

CREATE OR REPLACE VIEW vw_rx_test_claim_settings AS
SELECT DISTINCT
    rxf.rx_id,
    rxf.rx_no,
    rxf.insurance_id,
    rxf.site_id,
    rxf.patient_id,
    rxf.dispense_quantity,
    rx.prescriber_id,
    rx.route_id,
    rx.comp_dsg_fm_code,
    rx.compound_type,
    rx.comp_disp_unit,
    rx.day_supply,
    rx.order_no
FROM vw_rx_fill_items rxf
INNER JOIN vw_rx_order rx ON rx.rx_id = rxf.rx_id
WHERE rxf.is_primary_drug = TRUE;


CREATE OR REPLACE VIEW vw_rx_past_fills AS
SELECT DISTINCT
    dti.rx_id,
    COUNT(DISTINCT dti.ticket_no) as fills,
    SUM(COALESCE(dti.doses_prepared, 0)) as doses_dispensed
FROM form_careplan_delivery_tick dt
INNER JOIN form_careplan_dt_item dti ON dti.ticket_no = dt.ticket_no AND COALESCE(dti.is_primary_drug, 'No') = 'Yes'
WHERE dt.archived IS NOT TRUE
AND dt.deleted IS NOT TRUE
AND COALESCE(dt.void, 'No') <> 'Yes'
AND dt.status IN ('ready_to_bill', 'billed', 'confirmed')
GROUP BY dti.rx_id;

CREATE OR REPLACE VIEW vw_working_prescriptions AS
    SELECT
        rx.patient_id,
        rx.site_id,
        rx.rx_id,
        rx.fill_status,
        rx.working_dispense_id,
        rx.delivery_ticket_id,
        rx.rx_no,
        rx.last_event_id,
        rx.last_event_datetime,
        rx.last_event_by,
        rx.type_id
    FROM vw_rx_order rx;