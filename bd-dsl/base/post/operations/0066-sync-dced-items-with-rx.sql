-- Function to sync discontinued status from order items to rx records
DO $$ BEGIN
  PERFORM drop_all_function_signatures('sync_discontinued_status_to_rx');
END $$;
CREATE OR REPLACE FUNCTION sync_discontinued_status_to_rx()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Only proceed if status_id was changed
    IF NEW.status_id IS DISTINCT FROM OLD.status_id THEN
        -- Update rx records based on status
        UPDATE form_careplan_order_rx rx
        SET discontinued = CASE 
            WHEN NEW.status_id IN ('2', '3') THEN 'Yes'
            WHEN NEW.status_id IN ('1', '5') THEN NULL
            ELSE discontinued
        END
        WHERE rx.rx_no = NEW.rx_no
        AND rx.archived IS NOT TRUE
        AND rx.deleted IS NOT TRUE;
    END IF;

    RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    -- Log error
    INSERT INTO dispensing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        SQLERRM,
        'Exception in sync_discontinued_status_to_rx',
        'FUNCTION',
        current_schema(),
        TG_TABLE_NAME,
        jsonb_build_object(
            'function_name', 'sync_discontinued_status_to_rx',
            'rx_no', NEW.rx_no,
            'status_id', NEW.status_id
        )
    );
    
    RAISE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER sync_discontinued_order_item_trigger
    AFTER UPDATE OF status_id ON form_careplan_order_item
    FOR EACH ROW
    EXECUTE FUNCTION sync_discontinued_status_to_rx();

CREATE OR REPLACE TRIGGER sync_discontinued_orderp_item_trigger
    AFTER UPDATE OF status_id ON form_careplan_orderp_item
    FOR EACH ROW
    EXECUTE FUNCTION sync_discontinued_status_to_rx();
