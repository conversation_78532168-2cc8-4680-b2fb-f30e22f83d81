-- Wrapper functions for all base views that power the swimlanes


CREATE OR REPLACE FUNCTION get_dt_based_queue_without_jit(p_fill_status_filter text DEFAULT NULL)
RETURNS SETOF vw_wf_dt_based_queue AS $$
BEGIN
    -- Disable JIT
    SET LOCAL jit = off;
    
    -- Return filtered results
    IF p_fill_status_filter IS NOT NULL THEN
        RETURN QUERY SELECT * FROM vw_wf_dt_based_queue 
                     WHERE fill_status = p_fill_status_filter;
    ELSE
        RETURN QUERY SELECT * FROM vw_wf_dt_based_queue;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_rx_base_without_jit(p_fill_status_filter text DEFAULT NULL)
RETURNS SETOF vw_wf_rx_base AS $$
BEGIN
    -- Disable JIT
    SET LOCAL jit = off;
    
    -- Return filtered results
    IF p_fill_status_filter IS NOT NULL THEN
        RETURN QUERY SELECT * FROM vw_wf_rx_base 
                     WHERE fill_status = p_fill_status_filter;
    ELSE
        RETURN QUERY SELECT * FROM vw_wf_rx_base;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_orders_base_without_jit(p_status_ids text[] DEFAULT NULL, p_bv_flag text DEFAULT NULL, p_auth_flag text DEFAULT NULL)
RETURNS SETOF vw_wf_orders_base AS $$
BEGIN
    -- Disable JIT
    SET LOCAL jit = off;
    
    -- Return filtered results based on multiple conditions
    RETURN QUERY 
    SELECT * FROM vw_wf_orders_base ob
    WHERE (p_status_ids IS NULL OR ob.status_id = ANY(p_status_ids))
    AND (p_bv_flag IS NULL OR ob.bv_flag = p_bv_flag)
    AND (p_auth_flag IS NULL OR ob.auth_flag = p_auth_flag);
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION get_patients_base_without_jit(p_patient_status_filter text DEFAULT NULL)
RETURNS SETOF vw_wf_patients_base AS $$
BEGIN
    -- Disable JIT
    SET LOCAL jit = off;
    
    -- Return filtered results
    IF p_patient_status_filter IS NOT NULL THEN
        RETURN QUERY SELECT * FROM vw_wf_patients_base 
                     WHERE patient_status = p_patient_status_filter;
    ELSE
        RETURN QUERY SELECT * FROM vw_wf_patients_base;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_claims_based_queues_without_jit(p_fill_status_filter text DEFAULT NULL, p_payer_type_filter text DEFAULT NULL)
RETURNS SETOF vw_wf_claims_based_queues AS $$
BEGIN
    -- Disable JIT
    SET LOCAL jit = off;
    
    -- Return filtered results
    RETURN QUERY 
    SELECT * FROM vw_wf_claims_based_queues
    WHERE (p_fill_status_filter IS NULL OR fill_status = p_fill_status_filter)
    AND (p_payer_type_filter IS NULL OR payer_type_code = p_payer_type_filter);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_charges_based_queue_without_jit()
RETURNS SETOF vw_wf_charges_based_queue AS $$
BEGIN
    -- Disable JIT
    SET LOCAL jit = off;
    
    -- Return all results
    RETURN QUERY SELECT * FROM vw_wf_charges_based_queue;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_pa_based_queue_without_jit(p_status_id_filter text DEFAULT NULL, p_expiring_filter boolean DEFAULT FALSE)
RETURNS SETOF vw_wf_pa_based_queue AS $$
DECLARE
    v_auth_days_out integer;
BEGIN
    -- Disable JIT
    SET LOCAL jit = off;
    
    -- Get auth days settings
    SELECT COALESCE(auth_days_out, 60) INTO v_auth_days_out FROM form_company WHERE id = 1;
    
    -- Return filtered results
    IF p_expiring_filter THEN
        RETURN QUERY 
        SELECT * FROM vw_wf_pa_based_queue pa
        WHERE to_date(pa.expire_date, 'MM/DD/YYYY') BETWEEN 
              CURRENT_DATE - (v_auth_days_out || ' days')::INTERVAL AND 
              CURRENT_DATE + (v_auth_days_out || ' days')::INTERVAL;
    ELSIF p_status_id_filter IS NOT NULL THEN
        RETURN QUERY 
        SELECT * FROM vw_wf_pa_based_queue 
        WHERE status_id = p_status_id_filter;
    ELSE
        RETURN QUERY SELECT * FROM vw_wf_pa_based_queue;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_inventory_on_hand_without_jit()
RETURNS SETOF vw_inventory_on_hand AS $$
BEGIN
    -- Disable JIT
    SET LOCAL jit = off;
    
    -- Return all results
    RETURN QUERY SELECT * FROM vw_inventory_on_hand;
END;
$$ LANGUAGE plpgsql;

-- === Intake Swimlanes === 
CREATE OR REPLACE VIEW vw_benefits_verification AS
SELECT
    CONCAT('benefits_verification:', ord.form_name, ':', ord.form_id) AS tag,
    ord.*
FROM get_orders_base_without_jit(ARRAY['1', '5'], 'Yes', NULL) ord 
WHERE ord.patient_status IN ('1', '2', '3');

CREATE OR REPLACE VIEW vw_on_hold_patients AS
SELECT
    CONCAT('on_hold_patients:', pt.patient_id) AS tag,
    pt.*
FROM get_patients_base_without_jit('4') pt;

CREATE OR REPLACE VIEW vw_on_hold_orders AS
SELECT
    CONCAT('on_hold_orders:', ord.form_name, ':', ord.form_id) AS tag,
    ord.*
FROM get_orders_base_without_jit(ARRAY['4'], NULL, NULL) ord
WHERE ord.patient_status IN ('1', '2', '3');

CREATE OR REPLACE VIEW vw_missing_authorizations AS
SELECT
    CONCAT('missing_authorizations:', ord.form_name, ':', ord.form_id) as tag,
    ord.*
FROM get_orders_base_without_jit(ARRAY['1', '5', '4'], NULL, 'Yes') ord;

-- === Nursing Swimlanes === 
/*
CREATE OR REPLACE VIEW vw_pending_scheduling AS
CREATE OR REPLACE VIEW vw_scheduling_exceptions AS
CREATE OR REPLACE VIEW vw_pending_treatment_plan AS
CREATE OR REPLACE VIEW vw_expiring_treatment_plan AS
CREATE OR REPLACE VIEW vw_pending_nursing_note AS
CREATE OR REPLACE VIEW vw_new_nursing_note AS 
*/

-- === Pharmacy Swimlanes === 
-- 'Order Entry/Setup'
CREATE OR REPLACE VIEW vw_order_setup AS
SELECT
    concat('order_prescription:', ord.form_name, ':', ord.form_id) AS tag,
    ord.*,
    CASE WHEN COALESCE(ord.rx_denied,'No') = 'Yes' THEN '#FFCCCC' -- Red 
    WHEN COALESCE(ord.auth_flag,'No') = 'Yes' THEN '#FFD4B2' -- Dark Peach
    WHEN COALESCE(ord.bv_flag,'No') = 'Yes' THEN '#FFEAD9' -- Light Peach
    ELSE NULL::text
    END as __row_color
FROM get_orders_base_without_jit(ARRAY['1', '5'], NULL, NULL) ord
WHERE (ord.fill_status = 'Order Entry/Setup' OR ord.rx_id IS NULL);

CREATE OR REPLACE VIEW vw_bv_required_orders AS
SELECT
    concat('bv_required_orders:', ord.form_name, ':', ord.form_id) AS tag,
    ord.*
FROM get_orders_base_without_jit(ARRAY['1'], 'Yes', NULL) ord;

CREATE OR REPLACE VIEW vw_rx_verification AS
SELECT
	concat('rx_verification:', rx.rx_id) AS tag,
	'careplan_order_rx' AS form_name,
	rx.rx_id AS form_id,
	rx.*,
	CASE
		WHEN COALESCE(rx.rx_denied, 'No') = 'Yes' THEN '#FFCCCC'
		WHEN COALESCE(rx.auth_flag, 'No') = 'Yes' THEN '#FFD4B2'
		WHEN COALESCE(rx.bv_flag, 'No') = 'Yes' THEN '#FFEAD9'
		WHEN rx.stat IS NOT NULL THEN '#769BC4'
		ELSE NULL::text
	END AS __row_color,
	summary.summary_id,
	summary.response_id,
	summary.claim_status,
	CASE
		WHEN COALESCE(summary.total_paid, 0) >= 0 THEN format_currency(summary.total_paid::NUMERIC)
		ELSE NULL
	END AS total_paid,
	CASE
		WHEN COALESCE(summary.pt_pay_amt, 0) >= 0 THEN format_currency (summary.pt_pay_amt::NUMERIC)
		ELSE NULL
	END AS expected_copay,
	CASE
		WHEN COALESCE(inv.last_cost_ea, 0) >= 0 THEN format_currency (inv.last_cost_ea::NUMERIC)
		ELSE NULL
	END AS last_cost
FROM
	get_rx_base_without_jit ('Rx Verification') rx
	LEFT JOIN LATERAL (
		SELECT
			rs.id AS summary_id,
			rs.response_id AS response_id,
			rs.claim_status,
			rs.total_paid,
			rs.pt_pay_amt,
			rs.inventory_id
		FROM
			form_ncpdp_response_summary rs
		WHERE
			rs.patient_id = rx.patient_id
			AND rs.rx_no = rx.rx_no
		ORDER BY
			rs.id DESC
		LIMIT
			1
	) AS summary ON TRUE
	LEFT JOIN form_inventory AS inv ON inv.id = summary.inventory_id
WHERE
	COALESCE(rx.rx_verified, 'No') <> 'Yes';

CREATE OR REPLACE VIEW vw_ready_to_refill AS
SELECT
    CONCAT('ready_to_refill:', rx.rx_id) AS tag,
    rx.*
FROM
    get_rx_base_without_jit('Ready to Refill') rx;

CREATE OR REPLACE VIEW vw_ready_to_contact AS
SELECT
    CONCAT('ready_to_contact:', rx.rx_id) AS tag,
    CASE
        WHEN rx.delivery_ticket_id IS NOT NULL THEN 'careplan_delivery_tick'
        ELSE 'view_create_dt'
    END as form_name,
    CASE
        WHEN rx.delivery_ticket_id IS NOT NULL THEN rx.delivery_ticket_id
        ELSE NULL
    END as form_id,
    rx.*,
    CASE
    WHEN rx.auth_flag = 'Yes' THEN '#FFD4B2' -- Orange
    WHEN rx.stat IS NOT NULL THEN '#769BC4' -- Blue
    ELSE NULL::text
    END as __row_color
FROM
    get_rx_base_without_jit('Ready to Contact') rx
WHERE rx.rx_id IS NOT NULL;

CREATE OR REPLACE VIEW vw_ready_to_print AS
WITH low_stock_items AS (
    SELECT
        inoh.site_id,
        inoh.inventory_id,
        inoh.quantity_on_hand as quantity_on_hand_raw,
        format_numeric(inoh.quantity_on_hand::numeric) as quantity_on_hand
    FROM get_inventory_on_hand_without_jit() inoh
)
SELECT
    CONCAT('ready_to_print:', dt.id) AS tag,
    dt.*,
    COALESCE(ls.quantity_on_hand, 0::text) as "QOH",
    CASE
    WHEN dt.reject_reason IS NOT NULL THEN '#FFCCCC'
    WHEN ls.quantity_on_hand_raw < dt.quantity THEN '#FFD4B2'
    WHEN ls.quantity_on_hand_raw IS NULL THEN '#FFCCCC'
    ELSE NULL::text
    END as __row_color
FROM
    get_dt_based_queue_without_jit('Print Labels / Fill Rx') dt
    LEFT JOIN low_stock_items ls ON ls.inventory_id = dt.drug AND ls.site_id = dt.site_id;

CREATE OR REPLACE VIEW vw_fill_rx AS
SELECT
    CONCAT('fill_rx:', dt.id) AS tag,
    dt.*
FROM
    get_dt_based_queue_without_jit('Ready to Fill') dt;

DROP VIEW IF EXISTS vw_order_verification CASCADE;
CREATE OR REPLACE VIEW vw_order_verification AS
SELECT
    CONCAT('order_verification:', dt.id) AS tag,
    dt.*
FROM
    get_dt_based_queue_without_jit('Order Verification') dt;

CREATE OR REPLACE VIEW vw_dt_confirmation AS
SELECT
    CONCAT('confirm_delivery_tick:', dt.id) AS tag,
    dt.*
FROM
    get_dt_based_queue_without_jit('Delivery Ticket Confirmation') dt;

/*
CREATE OR REPLACE VIEW vw_nursing_alert AS
CREATE OR REPLACE VIEW vw_complaints AS
CREATE OR REPLACE VIEW vw_interventions AS
CREATE OR REPLACE VIEW vw_nearmiss AS
CREATE OR REPLACE VIEW vw_incidents AS
*/

-- === Billing Swimlanes === 
-- Claims to Adjudicate Master
CREATE OR REPLACE VIEW vw_claims_to_adjudicate_master AS
WITH low_stock_items AS (
    SELECT
        inoh.site_id,
        inoh.inventory_id,
        inoh.quantity_on_hand as quantity_on_hand_raw,
        format_numeric(inoh.quantity_on_hand::numeric) as quantity_on_hand
    FROM get_inventory_on_hand_without_jit() inoh
)
SELECT
    concat('claims_to_adjudicate_mstr:', bi.invoice_id) AS tag,
    bi.*,
    COALESCE(ls.quantity_on_hand, 0::text) as "QOH",
    CASE
        WHEN bi.row_color IS NOT NULL THEN bi.row_color
        WHEN ls.quantity_on_hand_raw < bi.raw_quantity THEN '#EDAE7B'
        ELSE NULL::text
    END as __row_color,
    CASE
        WHEN ls.quantity_on_hand_raw < bi.raw_quantity THEN 'Yes'
        ELSE 'No'
    END as low_stock
FROM get_claims_based_queues_without_jit(NULL::text, NULL::text) bi
LEFT JOIN low_stock_items ls ON ls.inventory_id = bi.inventory_id AND ls.site_id = bi.site_id
WHERE COALESCE(bi.invoice_status, 'Pending') <> 'Confirmed';

CREATE OR REPLACE VIEW vw_claims_to_adjudicate AS
SELECT 
    CONCAT('claims_to_adjudicate:', bi.invoice_id) AS tag,
    bi.*,
    bi.row_color as __row_color
FROM get_claims_based_queues_without_jit('Claims to Adjudicate'::text, NULL::text) bi
WHERE bi.payer_type_code NOT IN ('COPAY');

CREATE OR REPLACE VIEW vw_claims_to_adjudicate_copay AS
SELECT 
    CONCAT('claims_to_adjudicate_copay:', bi.invoice_id) AS tag,
    bi.*,
    bi.row_color as __row_color
FROM get_claims_based_queues_without_jit('Claims to Adjudicate'::text, 'COPAY'::text) bi;

CREATE OR REPLACE VIEW vw_ready_to_bill AS
SELECT DISTINCT
    CONCAT('ready_to_bill:', rtb.form_name, ':', rtb.invoice_no) AS tag,
    rtb.*,
    CASE
    WHEN rtb.auth_flag = 'Yes' THEN '#FFD4B2'
    ELSE NULL::text
    END as __row_color
FROM get_charges_based_queue_without_jit() rtb;

CREATE OR REPLACE VIEW vw_authorization_tracking AS
SELECT DISTINCT
    CONCAT('authorization_tracking:', pa.form_id) AS tag,
    pa.*,
    CASE 
    WHEN to_date(pa.expire_date, 'MM/DD/YYYY') BETWEEN 
         CURRENT_DATE - ((SELECT COALESCE(auth_days_out, 60) FROM form_company WHERE id = 1) || ' days')::INTERVAL AND 
         CURRENT_DATE + ((SELECT COALESCE(auth_days_out, 60) FROM form_company WHERE id = 1) || ' days')::INTERVAL THEN '#FFEAD9'
    WHEN to_date(pa.expire_date,'MM/DD/YYYY') < CURRENT_DATE THEN '#FFD4B2'
    ELSE NULL::text
    END as __row_color
FROM get_pa_based_queue_without_jit() pa
WHERE pa.patient_status IN ('1', '2', '3');

CREATE OR REPLACE VIEW vw_expiring_auths AS
SELECT 
    CONCAT('expiring_auths:', pa.form_id) AS tag,
    pa.*
FROM get_pa_based_queue_without_jit(NULL, TRUE) pa
ORDER BY pa.expire_date DESC;

CREATE OR REPLACE VIEW vw_pending_authorizations AS
SELECT
    CONCAT('pending_authorizations:', pa.form_id) AS tag,
    pa.*
FROM get_pa_based_queue_without_jit('1') pa;


CREATE OR REPLACE VIEW vw_inventory_pricing_updates AS
WITH pricing_update_top_item AS (
    SELECT string_agg(inv.name, ',') as inventory_items FROM form_inventory_pricing_upt_item upi
    INNER JOIN form_inventory inv ON inv.id = upi.inventory_id
    GROUP BY updated_awp_price
    ORDER BY updated_awp_price DESC
    LIMIT 5
)
SELECT DISTINCT
    CONCAT('inventory_pricing_updates:', ':', pu.id) AS tag,
    pu.id as form_id,
    'inventory_pricing_update' as form_name,
    pu.status,
    pu.pricing_update_date as update_date,
    pu.error_message,
    t.inventory_items as "Top 5 Items",
    CASE 
    WHEN pu.status = 'Error' THEN '#FFD4B2'
    WHEN pu.status = 'Loading' THEN '#FFEAD9'
    ELSE NULL::text
    END as __row_color
FROM form_inventory_pricing_update pu
CROSS JOIN pricing_update_top_item t
WHERE pu.status IN ('Pending', 'Loading', 'Error');