
CREATE OR REPLACE FUNCTION build_dur_ncpdp_segment(
  p_patient_id integer,
  p_parent_claim_no text DEFAULT NULL,
  p_transaction_code text DEFAULT 'B1'
) RETURNS ncpdp_dur AS $BODY$
DECLARE
  v_start_time timestamp;
  v_execution_time interval;
  v_result ncpdp_dur;
  v_error_message text;
  v_params jsonb;
  v_has_data boolean := FALSE;
BEGIN
  -- Check for null input parameters
  IF p_patient_id IS NULL THEN
    RAISE EXCEPTION 'Patient ID cannot be NULL';
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'patient_id', p_patient_id,
    'parent_claim_no', p_parent_claim_no,
    'transaction_code', p_transaction_code
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
      'build_dur_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building DUR segment for parent claim: %', p_parent_claim_no;

    -- First check if we have any DUR data for this parent claim
    SELECT EXISTS (
      SELECT 1
      FROM form_ncpdp pclaim
      JOIN sf_form_ncpdp_to_ncpdp_dur sfdur ON sfdur.form_ncpdp_fk = pclaim.id 
        AND sfdur.delete IS NOT TRUE 
        AND sfdur.archive IS NOT TRUE
      JOIN form_ncpdp_dur ncpdur ON ncpdur.id = sfdur.form_ncpdp_dur_fk 
        AND ncpdur.archived IS NOT TRUE 
        AND ncpdur.deleted IS NOT TRUE
      JOIN sf_form_ncpdp_dur_to_ncpdp_dur_itm sfduritm ON sfduritm.form_ncpdp_dur_fk = ncpdur.id
        AND sfduritm.archive IS NOT TRUE
        AND sfduritm.delete IS NOT TRUE
      JOIN form_ncpdp_dur_itm ncpduritm ON ncpduritm.id = sfduritm.form_ncpdp_dur_itm_fk
        AND ncpduritm.archived IS NOT TRUE
        AND ncpduritm.deleted IS NOT TRUE
      WHERE pclaim.claim_no = p_parent_claim_no 
        AND p_parent_claim_no IS NOT NULL
      LIMIT 1
    ) INTO v_has_data;

    RAISE LOG 'DUR data exists for parent claim %: %', p_parent_claim_no, v_has_data;

    -- If we don't have any DUR data, return NULL to skip this segment
    IF NOT v_has_data AND p_parent_claim_no IS NOT NULL THEN
      RAISE LOG 'No DUR data found for parent claim: %, returning NULL', p_parent_claim_no;
      RETURN NULL::ncpdp_dur;
    END IF;

    WITH dur_items AS (
      SELECT 
            p_patient_id::integer AS patient_id,
            p_transaction_code::text AS transaction_code,
            ncpduritm.rsn_for_svc_code::text AS rsn_for_svc_code,
            ncpduritm.prf_svc_code::text AS prf_svc_code,
            ncpduritm.rst_svc_code::text AS rst_svc_code,
            ncpduritm.dur_pps_loe::text AS dur_pps_loe,
            ncpduritm.co_agt_itm_id::integer AS co_agt_itm_id,
            ncpduritm.co_agt_id_qualifier::text AS co_agt_id_qualifier,
            ncpduritm.co_agt_id::text AS co_agt_id
      FROM form_ncpdp pclaim
      JOIN sf_form_ncpdp_to_ncpdp_dur sfdur ON sfdur.form_ncpdp_fk = pclaim.id 
        AND sfdur.delete IS NOT TRUE 
        AND sfdur.archive IS NOT TRUE
      JOIN form_ncpdp_dur ncpdur ON ncpdur.id = sfdur.form_ncpdp_dur_fk 
        AND ncpdur.archived IS NOT TRUE 
        AND ncpdur.deleted IS NOT TRUE
      JOIN sf_form_ncpdp_dur_to_ncpdp_dur_itm sfduritm ON sfduritm.form_ncpdp_dur_fk = ncpdur.id
        AND sfduritm.archive IS NOT TRUE
        AND sfduritm.delete IS NOT TRUE
      JOIN form_ncpdp_dur_itm ncpduritm ON ncpduritm.id = sfduritm.form_ncpdp_dur_itm_fk
        AND ncpduritm.archived IS NOT TRUE
        AND ncpduritm.deleted IS NOT TRUE
      WHERE pclaim.claim_no = p_parent_claim_no 
        AND p_parent_claim_no IS NOT NULL
    )
    SELECT
      p_patient_id::integer as patient_id,
      p_transaction_code::text as transaction_code,
      COALESCE(
        ARRAY_AGG(resp::ncpdp_dur_item),
        NULL::ncpdp_dur_item[]
      )::ncpdp_dur_item[] as subform_dur
    INTO v_result.patient_id, v_result.transaction_code, v_result.subform_dur
    FROM (
      SELECT 
        patient_id,
        transaction_code,
        rsn_for_svc_code,
        prf_svc_code,
        rst_svc_code,
        dur_pps_loe,
        co_agt_itm_id,
        co_agt_id_qualifier,
        co_agt_id
      FROM dur_items
    ) resp;

    RAISE LOG 'DUR segment build result: %', v_result;

    -- If we don't have a valid subform_dur, return NULL
    IF v_result.subform_dur IS NULL OR array_length(v_result.subform_dur, 1) = 0 THEN
      RAISE LOG 'No valid DUR items found, returning NULL';
      RETURN NULL;
    END IF;

    -- Validate DUR length
    IF array_length(v_result.subform_dur, 1) > 9 THEN
      INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
      ) VALUES (
        'Maximum of 9 DUR codes allowed',
        'Validating DUR codes in build_dur_ncpdp_segment',
        'FUNCTION',
        current_schema(),
        'form_ncpdp',
        jsonb_build_object(
          'function_name', 'build_dur_ncpdp_segment',
          'patient_id', p_patient_id
        )
      );
      RAISE EXCEPTION 'Maximum of 9 DUR codes allowed';
    END IF;

    -- Log success
    PERFORM log_billing_function(
      'build_dur_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_dur_ncpdp_segment',
      'FUNCTION',
      current_schema(),
      'form_ncpdp',
      jsonb_build_object(
        'function_name', 'build_dur_ncpdp_segment',
        'patient_id', p_patient_id
      )
    );

    PERFORM log_billing_function(
      'build_dur_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;