
CREATE OR REPLACE FUNCTION get_claim_reject_codes(root_claim TEXT)
RETURNS text[] AS $BODY$
DECLARE
    v_start_time timestamp;
    v_execution_time interval;
    v_error_message text;
    v_params jsonb;
    v_result text[];
BEGIN
    -- Record start time
    v_start_time := clock_timestamp();

    -- Build parameters JSON for logging
    v_params := jsonb_build_object(
        'root_claim', root_claim
    );

    BEGIN  -- Start exception block
        -- Log function call
        PERFORM log_billing_function(
            'get_claim_reject_codes'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            NULL::text,
            clock_timestamp() - v_start_time
        );

        -- Validate required parameters
        IF root_claim IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Root Claim # cannot be null',
                'Validating required parameters in get_claim_reject_codes',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                jsonb_build_object(
                    'function_name', 'get_claim_reject_codes',
                    'root_claim', root_claim
                )
            );
            RAISE EXCEPTION 'Root Claim # cannot be null';
        END IF;

        -- Get claim hierarchy first
        WITH claim_hierarchy AS (
            SELECT * FROM get_claim_hierarchy(root_claim)
        ),
        -- Extract and rank reject codes
        ranked_codes AS (
            SELECT 
                (unnested.code_rank).reject_code::text AS code,
                (unnested.code_rank).reject_rank::integer AS rank
            FROM (
                SELECT unnest(ch.reject_codes) AS code_rank
                FROM claim_hierarchy ch
            ) AS unnested
            ORDER BY (unnested.code_rank).reject_rank
            LIMIT 5
        )
        -- Create the final JSON result
        SELECT array_agg(code) INTO v_result
        FROM ranked_codes;

        -- Log successful completion
        PERFORM log_billing_function(
            'get_claim_reject_codes'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            NULL::text,
            clock_timestamp() - v_start_time
        );

        RETURN v_result::text[];

    EXCEPTION WHEN OTHERS THEN
        -- Log error
        v_error_message := SQLERRM;
        
        -- Log to billing error log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            v_error_message,
            'Exception in get_claim_reject_codes',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'get_claim_reject_codes',
                'root_claim', root_claim
            )
        );

        -- Log to NCPDP function log
        PERFORM log_billing_function(
            'get_claim_reject_codes'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            v_error_message::text,
            clock_timestamp() - v_start_time
        );
        RAISE;
    END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;