
CREATE OR REPLACE VIEW vw_low_stock_inventory AS
SELECT DISTINCT ON (invoh.inventory_id, invoh.site_id)
    'inventory' as form_name,
    inv.id as form_id,
    inv.id as item,
    inv.auto_name as item_auto_name,
    inv.hcpc_code as hcpc,
    inv.formatted_ndc as ndc,
    inv.upc as upc,
    inv.upin as upin,
    invoh.site_id as site,
    st.name as site_auto_name,
    invoh.quantity_on_hand as quantity_raw,
    format_numeric(invoh.quantity_on_hand::numeric) AS "Quantity",
    
    inv.awp_price AS awp_price_raw,
    format_currency(inv.awp_price::numeric) AS "AWP Unit Price",

    inv.last_cost_ea as last_cost_raw,
    format_currency(inv.last_cost_ea::numeric) AS "Last Cost",

    sup.auto_name as last_supplier_auto_name,
    sup.id as last_supplier,

    man.code as manufacturer,
    man.auto_name as manufacturer_auto_name,
    invoh.min_quantity,
    invoh.max_quantity

    --- TODO Add rolling 90 day average reimburse amount
    FROM vw_inventory_on_hand invoh
    INNER JOIN form_site st ON st.id = invoh.site_id
    INNER JOIN form_inventory inv ON inv.id = invoh.inventory_id
    LEFT JOIN form_list_supplier sup ON sup.id = inv.last_supplier_id
    LEFT JOIN form_list_manufacturer man ON man.code = inv.manufacturer_id
