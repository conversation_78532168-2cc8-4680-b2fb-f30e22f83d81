DO $$ 
BEGIN
    -- Create type for benefit amount if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_1500_diagnosis_info') THEN
        CREATE TYPE mm_1500_diagnosis_info AS (
            patient_id integer,
            dx_id integer,
            diagnosis_code text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_1500_service_line_info') THEN
        CREATE TYPE mm_1500_service_line_info AS (
            rx_no text,
            charge_no text,
            patient_id integer,
            service_date text,
            service_date_end text,
            inventory_id integer,
            type text,
            line_item_charge_amount numeric,
            line_item_paid_amount numeric,
            measurement_unit text,
            service_unit_count numeric,
            place_of_service_code text,
            modifier_1 text,
            modifier_2 text,
            modifier_3 text,
            modifier_4 text,
            dx_filter integer[],
            dx_id_1 integer,
            dx_id_2 integer,
            dx_id_3 integer,
            dx_id_4 integer,
            procedure_identifier text,
            procedure_code text,
            supplemental_info text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_1500_claim_record') THEN
        CREATE TYPE mm_1500_claim_record AS (
            parent_claim_no text,
            claim_no text,
            patient_id integer,
            status text,
            substatus_id text,
            site_id integer,
            insurance_id integer,
            payer_id integer,
            insurance_type text,
            claim_resubmission_code text,
            control_number text,
            claim_codes text,
            patient_first_name text,
            patient_last_name text,
            patient_middle_name text,
            patient_dob text,
            patient_gender text,
            patient_phone_number text,
            patient_address1 text,
            patient_address2 text,
            patient_city text,
            patient_state_id text,
            patient_postal_code text,
            subscriber_payer_organization_name text,
            subscriber_member_id text,
            subscriber_insurance_group_or_policy_number text,
            pa_id integer,
            prior_authorization_number text,
            subscriber_payment_responsibility_level_code text,
            subscriber_relationship_id text,
            subscriber_first_name text,
            subscriber_last_name text,
            subscriber_middle_name text,
            subscriber_dob text,
            subscriber_gender text,
            subscriber_phone_number text,
            subscriber_address1 text,
            subscriber_address2 text,
            subscriber_city text,
            subscriber_state_id text,
            subscriber_postal_code text,
            referring_provider_id integer,
            referring_provider_physician_id integer,
            referring_provider_first_name text,
            referring_provider_last_name text,
            referring_provider_npi text,
            referring_provider_state_license_number text,
            referring_provider_taxonomy_id text,
            bill_organization_name text,
            npi text,
            bill_address1 text,
            bill_address2 text,
            bill_city text,
            bill_state_id text,
            bill_phone text,
            bill_tax_id text,
            bill_alt_provider_id text,
            patient_account_number text,
            referring_provider_qualifier text,
            referring_provider_alt_id text,
            patient_signature_date text,
            subform_dx mm_1500_diagnosis_info[],
            subform_sl mm_1500_service_line_info[],
            billed numeric,
            expected numeric,
            paid numeric,
            cob_insurance_id integer,
            cob_payer_id integer,
            cob_organization_name text,
            cob_insurance_group_or_policy_number text,
            other_payer_claim_control_number text,
            cob_first_name text,
            cob_last_name text,
            cob_middle_name text
        );
    END IF;

END $$;
