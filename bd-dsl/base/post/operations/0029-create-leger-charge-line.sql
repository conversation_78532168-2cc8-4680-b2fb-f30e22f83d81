DROP FUNCTION IF EXISTS create_ledger_charge_line;
CREATE OR REPLACE FUNCTION create_ledger_charge_line(
    p_inventory_id integer,
    p_insurance_id integer,
    p_site_id integer,
    p_patient_id integer,
    p_quantity numeric,
    p_is_primary_drug boolean DEFAULT TRUE,
    p_compound_no text DEFAULT NULL,
    p_order_rx_id integer DEFAULT NULL,
    p_rental_id integer DEFAULT NULL,
    p_ticket_no text DEFAULT NULL,
    p_ticket_item_no text DEFAULT NULL,
    p_encounter_id integer DEFAULT NULL,
    p_is_test boolean DEFAULT FALSE,
    p_parent_charge_no text DEFAULT NULL
) RETURNS charge_line AS $$
DECLARE
    error_text text;
    result_row record;
    v_master_charge_no text := NULL;
    v_parent_payer_id integer := NULL;
    v_parent_insurance_id integer := NULL;
    v_parent_claim_no text := NULL;
    v_parent_claim_data record := NULL;
    v_date_of_service date := NULL;
    v_date_of_service_end date := NULL;
    v_insurance_settings record;
    v_result charge_line;
BEGIN
    -- Get the result row with potential error
    RAISE LOG 'create_ledger_charge_line: p_inventory_id: % p_insurance_id: %, p_site_id: %, p_patient_id: %', p_inventory_id, p_insurance_id, p_site_id, p_patient_id;

    IF p_parent_charge_no IS NOT NULL THEN
        SELECT 
            lcl.master_charge_no, 
            lcl.payer_id,
            lcl.insurance_id AS parent_insurance_id,
            lcl.claim_no,
            lcl.date_of_service,
            lcl.date_of_service_end
        INTO 
            v_master_charge_no,
            v_parent_payer_id,
            v_parent_insurance_id,
            v_parent_claim_no,
            v_date_of_service,
            v_date_of_service_end
        FROM form_ledger_charge_line lcl 
        WHERE lcl.charge_no = p_parent_charge_no
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
        LIMIT 1;

        -- Try to get parent claim data if we have a claim number
        IF v_parent_claim_no IS NOT NULL THEN
            -- Get claim info from existing claim
            SELECT * INTO v_parent_claim_data
            FROM get_primary_ncpdp_claim_info(v_parent_claim_no);
        END IF;
    END IF;

    -- Get insurance settings
    SELECT * INTO v_insurance_settings
    FROM get_insurance_claim_settings(
        p_insurance_id,
        p_site_id, 
        v_parent_payer_id::integer
    );

    SELECT 
        p_site_id::integer as site_id,
        p_patient_id::integer as patient_id,
        p_insurance_id::integer as insurance_id,
        pi.payer_id::integer as payer_id,
        p_inventory_id::integer as inventory_id,
        pi.shared_contract_id::integer as shared_contract_id,
        CASE
            WHEN COALESCE(ins.active, 'No') <> 'Yes' THEN 'Insurance record is not active. Cannot generate COB invoice'::text
            WHEN py.id IS NULL THEN 'Payer record not found. Check that record is not archived or deleted'::text
            WHEN pi.charge_quantity_ea IS NULL OR pi.charge_quantity_ea <= 0 THEN 'Unable to calculate charge quantity. Check inventory setup'::text
            WHEN pi.bill_ea IS NULL THEN 'Unable to calculate billed each ($). Check inventory / pricing setup'::text
            WHEN pi.expected_ea IS NULL THEN 'Unable to calculate expected each ($). Check inventory / pricing setup'::text
            WHEN pi.billing_method_id IS NULL THEN 'Billing method is required'::text
            WHEN p_quantity <= 0 THEN 'Quantity must be greater than zero'::text
            ELSE NULL::text 
        END as error,
        CASE 
            WHEN p_is_test THEN NULL::text
            WHEN pi.billing_method_id IN ('ncpdp', 'mm') THEN 'Yes'::text
            ELSE NULL::text
        END as is_dirty,
        gen_random_uuid() as charge_no,
        p_parent_charge_no::text as parent_charge_no,
        v_master_charge_no::text as master_charge_no,
        p_compound_no::text as compound_no,
        p_ticket_no::text as ticket_no,
        p_ticket_item_no::text as ticket_item_no,
        p_rental_id::integer as rental_id,
        p_order_rx_id::integer as order_rx_id,
        CASE
            WHEN p_is_test IS TRUE THEN NULL::text
            WHEN p_is_primary_drug IS TRUE THEN 'Yes'::text
            ELSE NULL::text
        END as is_primary_drug,
        CASE
            WHEN pi.billing_method_id = 'ncpdp' AND p_is_primary_drug IS TRUE THEN 'Yes'::text
            ELSE NULL::text
        END as is_primary_drug_ncpdp,
        CASE
            WHEN p_is_test THEN ('00' || LPAD(FLOOR(RANDOM() * 10000000000)::text, 10, '0'))::text
            ELSE cor.rx_no::text
        END as rx_no,
        CASE
            WHEN p_is_test THEN ARRAY['Drug']::text[]
            WHEN pi.billing_method_id = 'ncpdp' AND p_is_primary_drug IS TRUE THEN ARRAY['Drug']::text[]
            WHEN pi.billing_method_id = 'ncpdp' THEN ARRAY['Drug', 'Supply']::text[]
            ELSE ARRAY['Drug', 'Supply', 'Equipment Rental', 'Billable']::text[]
        END as inventory_type_filter,
        pi.inventory_type::text as inventory_type,
        CASE
            WHEN p_is_test THEN NULL::text
            ELSE pi.revenue_code_id::text
        END as revenue_code_id,
        pi.hcpc_code::text as hcpc_code,
        pi.ndc::text as ndc,
        pi.formatted_ndc::text as formatted_ndc,
        pi.gcn_seqno::text as gcn_seqno,
        CASE
            WHEN pi.charge_quantity_ea > 0 THEN ROUND(p_quantity::numeric * pi.charge_quantity_ea::numeric, 3)::numeric
            ELSE NULL::numeric
        END as charge_quantity,
        pi.charge_quantity_ea::numeric as charge_quantity_ea,
        pi.hcpc_quantity::numeric as hcpc_quantity,
        pi.hcpc_unit::text as hcpc_unit,
        pi.metric_quantity::numeric as metric_quantity,
        pi.charge_unit::text as charge_unit,
        p_quantity::numeric as bill_quantity,
        pi.metric_unit_each::numeric as metric_unit_each,
        pi.billing_unit_id::text as billing_unit_id,
        pi.billing_method_id::text as billing_method_id,
        pi.pricing_source::text as pricing_source,
        CASE
            WHEN pi.bill_ea IS NOT NULL THEN 
                ROUND(pi.bill_ea::numeric * p_quantity, 2)::numeric
            ELSE NULL::numeric
        END as billed,
        pi.bill_ea::numeric as calc_billed_ea,
        CASE
            WHEN pi.expected_ea IS NOT NULL THEN 
                ROUND(pi.expected_ea::numeric * p_quantity, 2)::numeric
            ELSE NULL::numeric
        END as expected,
        pi.expected_ea::numeric as calc_expected_ea,
        CASE
            WHEN pi.list_ea IS NOT NULL THEN 
                ROUND(pi.list_ea::numeric * p_quantity, 2)::numeric
            ELSE NULL::numeric
        END as list_price,
        pi.list_ea::numeric as calc_list_ea,
        CASE
            WHEN p_is_test THEN NULL::numeric
            WHEN wt_cost.cost IS NOT NULL THEN wt_cost.cost::numeric
            WHEN pi.cost_ea IS NOT NULL THEN 
                ROUND(pi.cost_ea::numeric * p_quantity, 2)::numeric
            ELSE NULL::numeric
        END as total_cost,
        CASE
            WHEN p_is_primary_drug THEN (COALESCE(pi.dispense_fee::numeric, 0.0) + 
                ROUND(pi.bill_ea::numeric * p_quantity, 2))::numeric
            ELSE 
                ROUND(pi.bill_ea::numeric * p_quantity, 2)::numeric
        END as gross_amount_due,
        0.00::numeric as incv_amt_sub,
        CASE
            WHEN p_is_test THEN NULL::numeric
            WHEN dti.copay::numeric > 0 THEN dti.copay::numeric
            WHEN bv.copay::numeric > 0 THEN bv.copay::numeric
            WHEN pi.expected_copay_ea::numeric > 0 THEN ROUND(pi.expected_copay_ea::numeric * COALESCE(p_quantity, 1)::numeric, 2)::numeric
            ELSE 0.00::numeric
        END as copay,
        CASE 
            WHEN p_is_test THEN NULL::numeric
            WHEN wt_cost.cost IS NOT NULL THEN ROUND(wt_cost.cost::numeric/COALESCE(NULLIF(p_quantity, 0), 1), 4)::numeric
            ELSE pi.cost_ea::numeric
        END as calc_cost_ea,
        0.00::numeric as total_adjusted,
        CASE
            WHEN pi.expected_ea IS NOT NULL THEN 
                ROUND(pi.expected_ea::numeric * p_quantity, 2)::numeric
            ELSE NULL::numeric
        END as total_balance_due,
        CASE
            WHEN p_is_primary_drug AND pi.billing_method_id = 'ncpdp' AND pi.dispense_fee::numeric > 0 THEN pi.dispense_fee::numeric
            ELSE NULL::numeric
        END as dispense_fee,
        CASE
            WHEN p_is_test THEN NULL::numeric
            WHEN dti.copay::numeric > 0 THEN dti.copay::numeric
            WHEN bv.copay::numeric > 0 THEN bv.copay::numeric
            WHEN pi.expected_copay_ea::numeric > 0 THEN ROUND(pi.expected_copay_ea::numeric * COALESCE(p_quantity, 1)::numeric, 2)::numeric
            ELSE 0.00::numeric
        END as pt_pd_amt_sub,
        p_encounter_id::integer as encounter_id,
        CASE
            WHEN p_is_test THEN NULL::text
            ELSE pi.description::text
        END as description,
        CASE
            WHEN p_is_test THEN NULL::text
            ELSE pi.upc::text
        END as upc,
        pi.upin::text as upin,
        pi.cost_basis::text as cost_basis,
        pi.awp_price::numeric as awp_price,
        CASE
            WHEN p_is_test THEN NULL::text
            WHEN pi.billing_method_id IN ('mm', 'cms1500') THEN pi.modifier_1::text
            ELSE NULL::text
        END as modifier_1,
        CASE
            WHEN p_is_test THEN NULL::text
            WHEN pi.billing_method_id IN ('mm', 'cms1500') THEN pi.modifier_2::text
            ELSE NULL::text
        END as modifier_2,
        CASE
            WHEN p_is_test THEN NULL::text
            WHEN pi.billing_method_id IN ('mm', 'cms1500') THEN pi.modifier_3::text
            ELSE NULL::text
        END as modifier_3,
        CASE
            WHEN p_is_test THEN NULL::text
            WHEN pi.billing_method_id IN ('mm', 'cms1500') THEN pi.modifier_4::text
            ELSE NULL::text
        END as modifier_4,
        CASE
            WHEN p_is_test THEN NULL::text
            WHEN dti.rental_type IS NOT NULL AND dti.type = 'Equipment Rental' THEN dti.rental_type::text
            ELSE NULL::text
        END as rental_type,
        CASE
            WHEN p_is_test THEN NULL::text
            WHEN dti.frequency_code IS NOT NULL AND dti.type = 'Equipment Rental' THEN dti.frequency_code::text
            ELSE NULL::text
        END as frequency_code,
        COALESCE(disp.fill_number, 1)::integer as fill_number,
        0::numeric as paid,
        v_date_of_service::date as date_of_service,
        v_date_of_service_end::date as date_of_service_end
    INTO result_row
    FROM get_inventory_pricing(p_inventory_id, p_insurance_id, p_site_id, p_patient_id) pi
    INNER JOIN form_patient_insurance ins ON ins.id = p_insurance_id 
    AND ins.archived IS NOT TRUE 
    AND ins.deleted IS NOT TRUE
    INNER JOIN form_payer py ON py.id = ins.payer_id 
    AND py.archived IS NOT TRUE 
    AND py.deleted IS NOT TRUE
    LEFT JOIN form_careplan_dt_item dti ON dti.ticket_item_no = p_ticket_item_no 
    AND dti.archived IS NOT TRUE
    AND dti.deleted IS NOT TRUE
    LEFT JOIN form_careplan_order_rx cor ON cor.id = p_order_rx_id
    AND cor.archived IS NOT TRUE
    AND cor.deleted IS NOT TRUE
    LEFT JOIN form_careplan_order_bv bv ON bv.order_no = cor.order_no
    AND bv.archived IS NOT TRUE
    AND bv.deleted IS NOT TRUE
    LEFT JOIN LATERAL (SELECT
        ROUND(COALESCE(SUM(wtc.amount_raw)::numeric, 0::numeric)::numeric, 2) as cost
    FROM vw_ticket_item_cogs wtc
    WHERE wtc.ticket_no = p_ticket_no AND wtc.ticket_item_no = p_ticket_item_no) wt_cost ON true
    LEFT JOIN LATERAL (SELECT disp.fill_number 
    FROM form_careplan_order_rx_disp disp
    WHERE disp.rx_no = cor.rx_no
    AND disp.archived IS NOT TRUE
    AND disp.deleted IS NOT TRUE
    ORDER BY disp.fill_number DESC
    LIMIT 1) disp ON true;

    -- Check if there was an error
    IF result_row.error IS NOT NULL THEN
        -- Log the error
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            result_row.error,
            'Error creating ledger charge line',
            'FUNCTION',
            current_schema(),
            'ledger_charge_line',
            jsonb_build_object(
                'function_name', 'create_ledger_charge_line',
                'inventory_id', p_inventory_id,
                'insurance_id', p_insurance_id,
                'site_id', p_site_id,
                'patient_id', p_patient_id
            )
        );

        -- Raise the exception
        RAISE EXCEPTION 'Ledger Charge Line Error: %', result_row.error;
    END IF;

    -- Return the result if no error
    SELECT 
        result_row.site_id,
        result_row.patient_id,
        result_row.insurance_id,
        result_row.payer_id,
        result_row.inventory_id,
        result_row.shared_contract_id,
        result_row.is_dirty,
        result_row.charge_no,
        result_row.parent_charge_no,
        result_row.master_charge_no,
        result_row.compound_no,
        result_row.ticket_no,
        result_row.ticket_item_no,
        result_row.rental_id,
        result_row.order_rx_id,
        result_row.is_primary_drug,
        result_row.is_primary_drug_ncpdp,
        result_row.rx_no,
        result_row.inventory_type_filter,
        result_row.inventory_type,
        result_row.revenue_code_id,
        result_row.hcpc_code,
        result_row.ndc,
        result_row.formatted_ndc,
        result_row.gcn_seqno,
        result_row.charge_quantity,
        result_row.charge_quantity_ea,
        result_row.hcpc_quantity,
        result_row.hcpc_unit,
        result_row.metric_quantity,
        result_row.charge_unit,
        result_row.bill_quantity,
        result_row.metric_unit_each,
        result_row.billing_unit_id,
        result_row.billing_method_id,
        result_row.pricing_source,
        result_row.billed,
        result_row.calc_billed_ea,
        result_row.expected,
        result_row.calc_expected_ea,
        result_row.list_price,
        result_row.calc_list_ea,
        result_row.total_cost,
        result_row.gross_amount_due,
        result_row.incv_amt_sub,
        result_row.copay,
        result_row.calc_cost_ea,
        result_row.total_adjusted,
        result_row.total_balance_due,
        result_row.dispense_fee,
        result_row.pt_pd_amt_sub,
        result_row.encounter_id,
        result_row.description,
        result_row.upc,
        result_row.upin,
        result_row.cost_basis,
        result_row.awp_price,
        result_row.modifier_1,
        result_row.modifier_2,
        result_row.modifier_3,
        result_row.modifier_4,
        result_row.rental_type,
        result_row.frequency_code,
        result_row.fill_number,
        result_row.date_of_service,
        result_row.date_of_service_end
    INTO v_result;

    RETURN v_result;
END;
$$ LANGUAGE plpgsql VOLATILE;

DROP FUNCTION IF EXISTS create_cob_ledger_charge_line;
CREATE OR REPLACE FUNCTION create_cob_ledger_charge_line(
    p_insurance_id integer,
    p_parent_charge_no text
) RETURNS charge_line AS $$
DECLARE
    error_text text;
    v_parent_charge record;
    v_insurance_settings record;
    v_send_zeros boolean := FALSE;
    v_remaining_liability numeric;
    v_start_time timestamptz;
    v_error_text text;
    v_result charge_line;
BEGIN
    -- Record start time for performance tracking
    v_start_time := clock_timestamp();
    
    RAISE LOG 'Starting create_cob_ledger_charge_line for parent charge: %, insurance: %', 
        p_parent_charge_no, p_insurance_id;

    -- Get parent charge info and validate
    SELECT 
        lcl.*,
        CASE 
            WHEN bi.revenue_accepted_posted = 'Yes' THEN
                clarb.remaining_ar_balance
            ELSE 
                clparb.pending_ar_balance
        END as remaining_balance,
        CASE WHEN bi.revenue_accepted_posted = 'Yes' THEN
            clarb.remaining_ar_balance
        ELSE
            clparb.pending_copay_balance
        END as pending_copay_balance,
        COALESCE(clparb.applied_unapplied_cash, clarb.applied_unapplied_cash, 0)::numeric as applied_unapplied_cash,
        bi.revenue_accepted_posted,
        bi.master_invoice_no,
        bi.invoice_no,
        bi.id as invoice_id,
        lcl.fill_number,
        lcl.is_primary_drug
    INTO v_parent_charge
    FROM form_ledger_charge_line lcl
    INNER JOIN form_billing_invoice bi ON bi.invoice_no = lcl.invoice_no
        AND bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
        AND COALESCE(bi.void, 'No') <> 'Yes'
        AND COALESCE(bi.zeroed, 'No') <> 'Yes'
    LEFT JOIN vw_charge_line_remaining_ar_balance clarb ON clarb.charge_no = lcl.charge_no
    LEFT JOIN vw_charge_line_pending_ar_balance clparb ON clparb.charge_no = lcl.charge_no
    WHERE lcl.charge_no = p_parent_charge_no
    AND lcl.archived IS NOT TRUE
    AND lcl.deleted IS NOT TRUE
    AND COALESCE(lcl.void, 'No') <> 'Yes'
    AND COALESCE(lcl.zeroed, 'No') <> 'Yes';

    IF v_parent_charge IS NULL THEN
        RAISE EXCEPTION 'Parent charge line not found: %', p_parent_charge_no;
    END IF;

    -- Get insurance settings
    SELECT * INTO v_insurance_settings
    FROM get_insurance_claim_settings(
        p_insurance_id,
        v_parent_charge.site_id,
        v_parent_charge.payer_id
    );

    -- Get remaining liability based on settings and revenue status
    IF v_insurance_settings.billing_method_id = 'ncpdp' THEN
        IF v_insurance_settings.ncpdp_sec_claims_ingred_cost = 'Send Zeros' THEN
            v_send_zeros := TRUE;
            v_remaining_liability := 0.00::numeric;
        ELSIF v_insurance_settings.ncpdp_sec_claims_ingred_cost = 'Send Co-pay Amount' THEN
            v_remaining_liability := COALESCE(v_parent_charge.pending_copay_balance, 0)::numeric;
        ELSE
            -- For 'Send Balance' setting
            IF v_insurance_settings.type_id IN ('COPAY', 'SELF') THEN
                v_remaining_liability := COALESCE(v_parent_charge.pending_copay_balance, 0)::numeric;
            ELSE
                -- For regular COB, use remaining balance
                v_remaining_liability := COALESCE(v_parent_charge.remaining_balance, 0)::numeric;
            END IF;
        END IF;
    ELSE
        -- For non-NCPDP claims
        IF v_insurance_settings.type_id IN ('COPAY', 'SELF') THEN
            v_remaining_liability := COALESCE(v_parent_charge.pending_copay_balance, 0)::numeric;
        ELSE
            -- For regular COB, use remaining balance
            v_remaining_liability := COALESCE(v_parent_charge.remaining_balance, 0)::numeric;
        END IF;
    END IF;

    RAISE LOG 'v_remaining_liability: %, v_send_zeros: %', v_remaining_liability, v_send_zeros;
    -- Return if no remaining liability
    IF v_remaining_liability <= 0 AND v_send_zeros = FALSE THEN
        RETURN NULL::charge_line;
    END IF;

    SELECT 
        v_parent_charge.site_id::integer,
        v_parent_charge.patient_id::integer,
        p_insurance_id::integer,
        ins.payer_id::integer,
        v_parent_charge.inventory_id::integer,
        pi.shared_contract_id::integer,
        CASE 
            WHEN pi.billing_method_id IN ('ncpdp', 'mm') THEN 'Yes'::text
            ELSE NULL::text
        END AS is_dirty,
        gen_random_uuid() AS charge_no,
        p_parent_charge_no::text AS parent_charge_no,
        v_parent_charge.master_charge_no::text,
        v_parent_charge.compound_no::text,
        v_parent_charge.ticket_no::text,
        v_parent_charge.ticket_item_no::text,
        v_parent_charge.rental_id::integer,
        v_parent_charge.order_rx_id::integer,
        v_parent_charge.is_primary_drug::text,
        CASE
            WHEN pi.billing_method_id = 'ncpdp' AND COALESCE(v_parent_charge.is_primary_drug, 'No') = 'Yes' THEN 'Yes'::text
            ELSE NULL::text
        END AS is_primary_drug_ncpdp,
        v_parent_charge.rx_no::text,
        CASE
            WHEN pi.billing_method_id = 'ncpdp' AND COALESCE(v_parent_charge.is_primary_drug, 'No') = 'Yes' THEN ARRAY['Drug']::text[]
            WHEN pi.billing_method_id = 'ncpdp' THEN ARRAY['Drug', 'Supply']::text[]
            ELSE ARRAY['Drug', 'Supply', 'Equipment Rental', 'Billable']::text[]
        END AS inventory_type_filter,
        v_parent_charge.inventory_type::text,
        COALESCE(pi.revenue_code_id::text, v_parent_charge.revenue_code_id::text) AS revenue_code_id,
        COALESCE(pi.hcpc_code::text, v_parent_charge.hcpc_code::text) AS hcpc_code,
        COALESCE(v_parent_charge.ndc::text, pi.ndc::text) AS ndc,
        COALESCE(pi.formatted_ndc::text, v_parent_charge.formatted_ndc::text) AS formatted_ndc,
        COALESCE(v_parent_charge.gcn_seqno::text, pi.gcn_seqno::text) AS gcn_seqno,
        CASE 
            WHEN pi.charge_quantity_ea > 0 THEN ROUND(v_parent_charge.bill_quantity::numeric * pi.charge_quantity_ea::numeric, 2)::numeric
            ELSE NULL::numeric
        END AS charge_quantity,
        ROUND(pi.charge_quantity_ea::numeric, 4)::numeric AS charge_quantity_ea,
        COALESCE(pi.hcpc_quantity::numeric, v_parent_charge.hcpc_quantity::numeric) AS hcpc_quantity,
        COALESCE(pi.hcpc_unit::text, v_parent_charge.hcpc_unit::text) AS hcpc_unit,
        COALESCE(pi.metric_quantity::numeric, v_parent_charge.metric_quantity::numeric) AS metric_quantity,
        COALESCE(pi.charge_unit::text, v_parent_charge.charge_unit::text) AS charge_unit,
        v_parent_charge.bill_quantity::numeric,
        ROUND(pi.metric_unit_each::numeric, 4)::numeric AS metric_unit_each,
        pi.billing_unit_id::text as billing_unit_id,
        ins.billing_method_id::text as billing_method_id,
        CASE
            WHEN ins.payer_id = 1 THEN 'copay'::text
            ELSE 'cob_override'::text
        END AS pricing_source,
        CASE
            WHEN v_insurance_settings.ncpdp_sec_claims_ingred_cost = 'Send Zeros' THEN 0.00::numeric
            WHEN v_insurance_settings.ncpdp_sec_claims_ingred_cost = 'Send Co-pay Amount' THEN 
                v_remaining_liability::numeric
            ELSE v_parent_charge.billed::numeric
        END AS billed,
        v_parent_charge.calc_billed_ea::numeric,
        v_remaining_liability::numeric AS expected,
        ROUND(v_remaining_liability::numeric / COALESCE(NULLIF(v_parent_charge.bill_quantity, 0), 1)::numeric, 4)::numeric AS calc_expected_ea,
        v_parent_charge.list_price::numeric,
        v_parent_charge.calc_list_ea::numeric,
        NULL::numeric as total_cost,
        v_remaining_liability::numeric AS gross_amount_due,
        NULL::numeric AS incv_amt_sub,
        v_parent_charge.copay::numeric AS copay,
        NULL::numeric AS calc_cost_ea,
        NULL::numeric AS total_adjusted,
        v_remaining_liability::numeric AS total_balance_due,
        NULL::numeric as dispense_fee,
        CASE WHEN COALESCE(v_insurance_settings.ncpdp_pt_paid_amount_dx, 'No') = 'Yes' THEN
            v_parent_charge.copay::numeric
        ELSE
            0.00::numeric
        END AS pt_pd_amt_sub,
        v_parent_charge.encounter_id::integer,
        v_parent_charge.description::text,
        COALESCE(pi.upc::text, v_parent_charge.upc::text) AS upc,
        COALESCE(pi.upin::text, v_parent_charge.upin::text) AS upin,
        CASE
            WHEN ins.billing_method_id = 'ncpdp' THEN v_parent_charge.cost_basis::text
            ELSE NULL::text
        END AS cost_basis,
        v_parent_charge.awp_price::numeric,
        CASE
            WHEN ins.billing_method_id IN ('mm', 'cms1500') THEN pi.modifier_1::text
            ELSE NULL::text
        END AS modifier_1,
        CASE
            WHEN ins.billing_method_id IN ('mm', 'cms1500') THEN pi.modifier_2::text
            ELSE NULL::text
        END AS modifier_2,
        CASE
            WHEN ins.billing_method_id IN ('mm', 'cms1500') THEN pi.modifier_3::text
            ELSE NULL::text
        END AS modifier_3,
        CASE
            WHEN ins.billing_method_id IN ('mm', 'cms1500') THEN pi.modifier_4::text
            ELSE NULL::text
        END AS modifier_4,
        v_parent_charge.rental_type::text,
        v_parent_charge.frequency_code::text,
        v_parent_charge.fill_number::integer,
        CASE
            WHEN v_insurance_settings.type_id IN ('COPAY', 'SELF') THEN v_parent_charge.applied_unapplied_cash::numeric
            ELSE 0.00::numeric
        END AS paid,
        v_parent_charge.date_of_service::date as date_of_service,
        v_parent_charge.date_of_service_end::date as date_of_service_end
    FROM form_patient_insurance ins
    INTO v_result
    INNER JOIN form_payer py ON py.id = ins.payer_id 
        AND py.archived IS NOT TRUE 
        AND py.deleted IS NOT TRUE
    CROSS JOIN get_inventory_pricing(v_parent_charge.inventory_id, p_insurance_id, v_parent_charge.site_id, v_parent_charge.patient_id) pi
    WHERE ins.id = p_insurance_id
    AND ins.archived IS NOT TRUE 
    AND ins.deleted IS NOT TRUE
    AND CASE
        WHEN py.id <> 1 AND COALESCE(ins.active, 'No') <> 'Yes' THEN FALSE
        WHEN py.id IS NULL THEN FALSE
        WHEN py.id <> 1 AND (pi.charge_quantity_ea IS NULL OR pi.charge_quantity_ea <= 0) THEN FALSE
        WHEN py.id <> 1 AND pi.bill_ea IS NULL THEN FALSE
        WHEN py.id <> 1 AND pi.expected_ea IS NULL THEN FALSE
        WHEN pi.billing_method_id IS NULL THEN FALSE
        ELSE TRUE
    END;

    -- Check for errors using a separate query or logic if needed
    SELECT CASE
        WHEN py.id <> 1 AND COALESCE(ins.active, 'No') <> 'Yes' THEN 'Insurance record is not active. Cannot generate COB invoice'
        WHEN py.id IS NULL THEN 'Payer record not found. Check that record is not archived or deleted'
        WHEN py.id <> 1 AND (pi.charge_quantity_ea IS NULL OR pi.charge_quantity_ea <= 0) THEN 'Unable to calculate COB charge quantity. Check inventory setup'
        WHEN py.id <> 1 AND pi.bill_ea IS NULL THEN 'Unable to calculate billed each ($). Check inventory / pricing setup'
        WHEN py.id <> 1 AND pi.expected_ea IS NULL THEN 'Unable to calculate expected each ($). Check inventory / pricing setup'
        WHEN pi.billing_method_id IS NULL THEN 'Billing method is required'
        ELSE NULL
    END INTO v_error_text
    FROM form_patient_insurance ins
    INNER JOIN form_payer py ON py.id = ins.payer_id 
        AND py.archived IS NOT TRUE 
        AND py.deleted IS NOT TRUE
    CROSS JOIN get_inventory_pricing(v_parent_charge.inventory_id, p_insurance_id, v_parent_charge.site_id, v_parent_charge.patient_id) pi
    WHERE ins.id = p_insurance_id
    AND ins.archived IS NOT TRUE 
    AND ins.deleted IS NOT TRUE;

    IF v_error_text IS NOT NULL THEN
        RAISE EXCEPTION '%', v_error_text;
    END IF;

    RETURN v_result;
EXCEPTION WHEN OTHERS THEN
    -- Log error details
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        SQLERRM,
        'Error in create_cob_ledger_charge_line',
        'FUNCTION',
        current_schema(),
        'ledger_charge_line',
        jsonb_build_object(
            'parent_charge_no', p_parent_charge_no,
            'insurance_id', p_insurance_id,
            'execution_time_ms', extract(milliseconds from clock_timestamp() - v_start_time)
        )
    );
    RAISE;
END;
$$ LANGUAGE plpgsql;
