-- Dispense is negative amount and void is positive amount and adjustment is positive amount
CREATE OR REPLACE FUNCTION crx_inv_ledger()
RETURNS TRIGGER AS $$
DECLARE
    v_type VARCHAR(50);
    v_quantity NUMERIC;
    v_inventory_rec RECORD;
    v_old_record RECORD;
    v_available_quantity NUMERIC := 0;
    v_qc TEXT;
    v_ledger_inventory_id BIGINT;
    v_projected_quantity NUMERIC;
BEGIN
    -- Set default transaction type
    v_type := 'Dispense';
    v_quantity := NULL;
    -- Get inventory record
    SELECT * INTO v_inventory_rec 
    FROM form_inventory 
    WHERE id = COALESCE(NEW.inventory_id, OLD.inventory_id);

    RAISE LOG 'crx_inv_ledger: v_inventory_rec_id: %', v_inventory_rec.id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Inventory not found';
    END IF;

    IF TG_OP = 'INSERT' THEN
        RAISE LOG 'crx_inv_ledger: TG_OP: %', TG_OP;
        IF NEW.inventory_id IS NULL THEN
            RAISE EXCEPTION 'Inventory ID cannot be empty for ID %', NEW.id;
        END IF;

        IF COALESCE(NEW.void, 'No') = 'Yes' THEN
            RAISE EXCEPTION 'Void cannot be Yes for a new record';
        END IF;
        v_quantity := NEW.dispensed_quantity::NUMERIC * -1;
        RAISE LOG 'crx_inv_ledger: v_quantity: %', v_quantity;
    END IF;

    IF TG_OP = 'UPDATE' THEN

        IF COALESCE(NEW.void, 'No') = 'Yes' THEN
            v_type := 'Void';

            -- Check if already voided
            IF COALESCE(OLD.void, 'No') = 'Yes' THEN
                RETURN NEW;
            END IF;

            v_quantity := OLD.dispensed_quantity::NUMERIC;
            -- Check for existing void records
            IF EXISTS (
                SELECT 1 FROM form_ledger_inventory 
                WHERE source_form = 'careplan_dt_wt_pulled'
                AND source_id = COALESCE(NEW.id, OLD.id)
                AND transaction_type = 'Void'
            ) THEN
                RAISE EXCEPTION 'Void Ledger Record Already Exists for ledger_inventory';
            END IF;

            IF COALESCE(v_inventory_rec.lot_tracking, 'No')::text = 'Yes' AND EXISTS (
                SELECT 1 FROM form_ledger_lot
                WHERE source_form = 'careplan_dt_wt_pulled'
                AND source_id = COALESCE(NEW.id, OLD.id)
                AND transaction_type = 'Void'
            ) THEN
                RAISE EXCEPTION 'Void Ledger Record Already Exists for ledger_lot';
            END IF;

            IF COALESCE(v_inventory_rec.serial_tracking, 'No')::text = 'Yes' AND EXISTS (
                SELECT 1 FROM form_ledger_serial
                WHERE source_form = 'careplan_dt_wt_pulled'
                AND source_id = COALESCE(NEW.id, OLD.id)
                AND transaction_type = 'Void'
            ) THEN
                RAISE EXCEPTION 'Void Ledger Record Already Exists for ledger_serial';
            END IF;
        ELSE
            -- Calculate quantity differential for updates
            v_quantity := NEW.dispensed_quantity::NUMERIC - OLD.dispensed_quantity::NUMERIC;
            IF v_quantity < 0 THEN
                v_type := 'Adjustment';
            ELSIF v_quantity > 0 THEN
                v_type := 'Dispense';
            ELSE
                RETURN NEW;
            END IF;
            -- Invert the quantity since adj/void is + and dispense is -
            v_quantity := v_quantity * -1;
        END IF;
    END IF;

    -- Check if transaction would result in negative inventory
    SELECT COALESCE(SUM(quantity), 0) INTO v_projected_quantity
    FROM form_ledger_inventory
    WHERE inventory_id = v_inventory_rec.id
    AND archived IS NOT TRUE 
    AND deleted IS NOT TRUE;

    -- Add the new quantity to get projected total
    v_projected_quantity := v_projected_quantity + v_quantity;

    -- Prevent negative inventory unless it's a billable item
    IF v_inventory_rec.type::text <> 'Billable' AND v_projected_quantity < 0 THEN
        RAISE EXCEPTION 'Transaction would result in negative inventory. Item: %, Current: %, Change: %, Projected: %',
            v_inventory_rec.name, (v_projected_quantity - v_quantity), v_quantity, v_projected_quantity;
    END IF;

    -- Insert ledger records
    IF v_type = 'Dispense' THEN
        RAISE LOG 'crx_inv_ledger: v_type: %', v_type;
        IF COALESCE(v_inventory_rec.serial_tracking, 'No')::text = 'Yes' THEN
            -- Check Lot and Serial combination
            v_qc := 'Serial only';
            SELECT COALESCE(SUM(ils.quantity), 0) INTO v_available_quantity
            FROM form_ledger_serial ils
            WHERE ils.site_id = COALESCE(NEW.site_id, OLD.site_id)
                AND ils.serial_no = COALESCE(NEW.serial_no, OLD.serial_no)
                AND ils.archived IS NOT TRUE 
                AND ils.deleted IS NOT TRUE;
        ELSIF COALESCE(v_inventory_rec.lot_tracking, 'No')::text = 'Yes' THEN
            -- Check Lot only
            v_qc := 'Lot only';
            SELECT COALESCE(SUM(ill.quantity), 0) INTO v_available_quantity
            FROM form_ledger_lot ill
            WHERE ill.site_id = COALESCE(NEW.site_id, OLD.site_id)
                AND ill.lot_no = COALESCE(NEW.lot_no, OLD.lot_no)
                AND ill.archived IS NOT TRUE 
                AND ill.deleted IS NOT TRUE;
        ELSEIF v_inventory_rec.type::text <> 'Billable' THEN
            v_qc := 'Regular Inventory';
            SELECT COALESCE(SUM(il.quantity),0) INTO v_available_quantity
            FROM form_ledger_inventory il
            WHERE il.site_id = COALESCE(NEW.site_id,OLD.site_id)
                AND il.inventory_id = COALESCE(NEW.inventory_id, OLD.inventory_id)
                AND il.archived IS NOT TRUE 
                AND il.deleted IS NOT TRUE;
        END IF;

        -- If we checked stock (any of the above conditions was true)
        IF v_inventory_rec.type::text <> 'Billable' AND v_available_quantity IS NOT NULL AND ABS(v_quantity) > v_available_quantity THEN
            -- Determine which table has insufficient quantity
            RAISE EXCEPTION 'Insufficient stock available. Item: % Required: %, Available: %, Checked for: %', 
                v_inventory_rec.name, ABS(v_quantity), v_available_quantity, v_qc;
        END IF;
    END IF;

    INSERT INTO form_ledger_inventory (
        inventory_id,
        quantity,
        source_form,
        source_id,
        ticket_no,
        ticket_item_no,
        transaction_type,
        transaction_date,
        site_id,
        patient_id
    ) VALUES (
        v_inventory_rec.id,
        v_quantity,
        'careplan_dt_wt_pulled',
        NEW.id,
        COALESCE(NEW.ticket_no, OLD.ticket_no),
        COALESCE(NEW.ticket_item_no, OLD.ticket_item_no),
        v_type,
        CURRENT_DATE,
        COALESCE(NEW.site_id, OLD.site_id),
        COALESCE(NEW.patient_id, OLD.patient_id)
    ) RETURNING id INTO v_ledger_inventory_id;

    -- Insert serial ledger if needed
    IF COALESCE(v_inventory_rec.serial_tracking, 'No') = 'Yes' THEN
        -- If both lot and serial tracking are enabled, include lot info in serial ledger
        IF COALESCE(v_inventory_rec.lot_tracking, 'No') = 'Yes' THEN
            INSERT INTO form_ledger_serial (
                inventory_id,
                quantity,
                source_form,
                source_id,
                ticket_no,
                ticket_item_no,
                transaction_type,
                transaction_date,
                serial_no,
                lot_no,
                ledger_id,
                site_id,
                patient_id
            ) VALUES (
                v_inventory_rec.id,
                v_quantity,
                'careplan_dt_wt_pulled',
                NEW.id,
                COALESCE(NEW.ticket_no, OLD.ticket_no),
                COALESCE(NEW.ticket_item_no, OLD.ticket_item_no),
                v_type,
                CURRENT_DATE,
                COALESCE(NEW.serial_no, OLD.serial_no),
                COALESCE(NEW.lot_no, OLD.lot_no),
                v_ledger_inventory_id,
                COALESCE(NEW.site_id, OLD.site_id),
                COALESCE(NEW.patient_id, OLD.patient_id)
            );
            RETURN NEW;
        END IF;
        INSERT INTO form_ledger_serial (
            inventory_id,
            quantity,
            source_form,
            source_id,
            ticket_no,
            ticket_item_no,
            transaction_type,
            transaction_date,
            serial_no,
            ledger_id,
            site_id,
            patient_id
        ) VALUES (
            v_inventory_rec.id,
            v_quantity,
            'careplan_dt_wt_pulled',
            NEW.id,
            COALESCE(NEW.ticket_no, OLD.ticket_no),
            COALESCE(NEW.ticket_item_no, OLD.ticket_item_no),
            v_type,
            CURRENT_DATE,
            COALESCE(NEW.serial_no, OLD.serial_no),
            v_ledger_inventory_id,
            COALESCE(NEW.site_id, OLD.site_id),
            COALESCE(NEW.patient_id, OLD.patient_id)
        );
    END IF;

    -- Insert lot ledger if needed
    IF COALESCE(v_inventory_rec.lot_tracking, 'No') = 'Yes' THEN
        INSERT INTO form_ledger_lot (
            inventory_id,
            quantity,
            source_form,
            source_id,
            ticket_no,
            ticket_item_no,
            transaction_type,
            transaction_date,
            lot_no,
            ledger_id,
            site_id,
            patient_id
        ) VALUES (
            v_inventory_rec.id,
            v_quantity,
            'careplan_dt_wt_pulled',
            NEW.id,
            COALESCE(NEW.ticket_no, OLD.ticket_no),
            COALESCE(NEW.ticket_item_no, OLD.ticket_item_no),
            v_type,
            CURRENT_DATE,
            COALESCE(NEW.lot_no, OLD.lot_no),
            v_ledger_inventory_id,
            COALESCE(NEW.site_id, OLD.site_id),
            COALESCE(NEW.patient_id, OLD.patient_id)
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE OR REPLACE TRIGGER crx_inv_ledger_trigger
BEFORE INSERT OR UPDATE ON form_careplan_dt_wt_pulled
FOR EACH ROW
EXECUTE FUNCTION crx_inv_ledger();