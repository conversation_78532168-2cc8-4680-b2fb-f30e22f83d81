
DO $$ BEGIN
  PERFORM drop_all_function_signatures('update_primary_item_on_order_rx');
END $$;
CREATE OR REPLACE FUNCTION update_primary_item_on_order_rx()
RETURNS TRIGGER AS $$
/**
 * Ensures one and only one entry against form_careplan_order_rx has is_primary_ingredient set to 'Yes'
 * 
 * @param {NEW} - The new record being inserted or updated
 * @param {OLD} - The old record (for updates)
 * @returns {TRIGGER} - The trigger result
 */
BEGIN
    -- Wrap everything in a try-catch block
    BEGIN
        -- Check if inputs are not null
        IF NEW.rx_no IS NULL THEN
            RAISE EXCEPTION 'rx_no cannot be null';
        END IF;

        -- Case 1: If NE<PERSON> is primary and <PERSON><PERSON> was also primary, do nothing
        IF TG_OP = 'UPDATE' AND COALESCE(NEW.is_primary_ingredient, 'No') = 'Yes' AND COALESCE(OLD.is_primary_ingredient, 'No') = 'Yes' THEN
            -- No change needed
            RETURN NEW;
        END IF;

        -- Case 2: If NEW is being set to primary, clear any other primaries
        IF COALESCE(NEW.is_primary_ingredient, 'No') = 'Yes' THEN
            UPDATE form_careplan_order_rx_cp
            SET is_primary_ingredient = NULL::text
            WHERE rx_no = NEW.rx_no
            AND id != NEW.id
            AND COALESCE(is_primary_ingredient, 'No') = 'Yes'
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE;
            
            RETURN NEW;
        END IF;

        -- Case 3: If no primary exists (including if we just removed it), set the highest priced item as primary
        IF NOT EXISTS (
            SELECT 1 FROM form_careplan_order_rx_cp
            WHERE rx_no = NEW.rx_no
            AND COALESCE(is_primary_ingredient, 'No') = 'Yes'
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE
        ) THEN
            -- Find the item with the highest list_price and set it as primary
            UPDATE form_careplan_order_rx_cp
            SET is_primary_ingredient = 'Yes'
            WHERE id = (
                SELECT cp.id
                FROM form_careplan_order_rx_cp cp
                JOIN form_inventory inv ON inv.id = cp.inventory_id 
                AND inv.archived IS NOT TRUE 
                AND inv.deleted IS NOT TRUE
                WHERE cp.rx_no = NEW.rx_no
                AND cp.archived IS NOT TRUE
                AND cp.deleted IS NOT TRUE
                ORDER BY inv.list_price::numeric DESC NULLS LAST
                LIMIT 1
            );
        END IF;

        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        -- Catch any errors and re-raise them
        RAISE EXCEPTION '%', SQLERRM;
    END;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trg_update_primary_item_on_order_rx ON form_careplan_order_rx_cp;
CREATE TRIGGER trg_update_primary_item_on_order_rx
AFTER INSERT OR UPDATE ON form_careplan_order_rx_cp
FOR EACH ROW EXECUTE FUNCTION update_primary_item_on_order_rx();
