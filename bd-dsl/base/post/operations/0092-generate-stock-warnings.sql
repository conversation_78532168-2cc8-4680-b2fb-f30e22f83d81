DO $$ BEGIN
  PERFORM drop_all_function_signatures('check_item_stock');
END $$;
CREATE OR REPLACE FUNCTION check_item_stock(
    p_inventory_id INTEGER,
    p_site_id INTEGER
) RETURNS INTEGER AS $$
DECLARE
    v_params JSONB;
BEGIN

    -- Set parameters for potential error logging
    v_params := jsonb_build_object(
        'function_name', 'check_item_stock',
        'inventory_id', p_inventory_id,
        'site_id', p_site_id
    );

    IF p_inventory_id IS NULL OR p_site_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Required parameters cannot be null',
            'Validating parameters in check_item_stock',
            'FUNCTION',
            current_schema(),
            'form_inventory',
            v_params
        );
        RAISE EXCEPTION 'Required parameters cannot be null: inventory_id=% site_id=%', 
            p_inventory_id, p_site_id;
    END IF;

    RETURN (
        WITH StockData AS (
            SELECT SUM(lgi.quantity) as stock_quantity
            FROM form_ledger_inventory lgi
            INNER JOIN form_inventory inv ON inv.id = lgi.inventory_id
            WHERE lgi.inventory_id = p_inventory_id
            AND lgi.site_id = p_site_id
        )
        SELECT sd.stock_quantity as stock_quantity
        FROM StockData sd
        LIMIT 1
    );

END;
$$ LANGUAGE plpgsql STABLE;

DO $$ BEGIN
  PERFORM drop_all_function_signatures('generate_stock_warnings');
END $$;
CREATE OR REPLACE FUNCTION generate_stock_warnings(
    p_delivery_ticket_id INTEGER
) RETURNS JSONB AS $$
DECLARE
    v_warnings TEXT[] := '{}';
    v_block BOOLEAN := FALSE;
    v_error_message TEXT;
    v_params JSONB;
BEGIN

    -- Set parameters for potential error logging
    v_params := jsonb_build_object(
        'function_name', 'generate_stock_warnings',
        'delivery_ticket_id', p_delivery_ticket_id
    );
    IF p_delivery_ticket_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Delivery ticket id cannot be null',
            'Validating parameters in generate_stock_warnings',
            'FUNCTION',
            current_schema(),
            'form_delivery_ticket',
            v_params
        );
        RAISE EXCEPTION 'Required parameters cannot be null: delivery_ticket_id=%', 
            p_delivery_ticket_id;
    END IF;

    -- Collect all warnings and block status
    WITH DeliveryItems AS (
        SELECT
            SUM(dti.dispense_quantity) as total_dispense_quantity,
            check_item_stock(dti.inventory_id, dti.site_id) as stock_quantity,
            dti.inventory_id,
            dti.site_id
        FROM vw_delivery_items dti
        INNER JOIN form_inventory inv ON inv.id = dti.inventory_id AND inv.archived IS NOT TRUE AND inv.deleted IS NOT TRUE
        INNER JOIN form_site s ON s.id = dti.site_id AND s.archived IS NOT TRUE AND s.deleted IS NOT TRUE
        WHERE dti.delivery_ticket_id = p_delivery_ticket_id
        AND dti.billing_method = 'Insurance' AND COALESCE(dti.bill, 'No') = 'Yes' AND dti.insurance_id IS NOT NULL
        AND COALESCE(dti.confirmed, 'No') = 'No' AND COALESCE(dti.ready_to_fill, 'No') = 'No'
        GROUP BY dti.inventory_id, dti.site_id
    ),
    StockWarnings AS (
        SELECT
            'Insufficient stock for ' || inv.name || ' at ' || s.name || '. Ordered ' || di.total_dispense_quantity || ' but only ' || di.stock_quantity || ' in stock.' as warning
        FROM DeliveryItems di
        INNER JOIN form_inventory inv ON inv.id = di.inventory_id AND inv.archived IS NOT TRUE AND inv.deleted IS NOT TRUE
        INNER JOIN form_site s ON s.id = di.site_id AND s.archived IS NOT TRUE AND s.deleted IS NOT TRUE
        WHERE di.total_dispense_quantity > di.stock_quantity
    )
    SELECT 
        array_agg(warning) FILTER (WHERE warning IS NOT NULL) as warnings
    INTO v_warnings
    FROM StockWarnings;

    -- Return the JSON result
    RETURN jsonb_build_object(
        'warnings', COALESCE(jsonb_pretty(to_jsonb(v_warnings)), '[]'::jsonb)
    );

EXCEPTION WHEN OTHERS THEN
    -- Log error
    v_error_message := SQLERRM;
    
    -- Log to billing error log
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        v_error_message,
        'Exception in generate_stock_warnings',
        'FUNCTION',
        current_schema(),
        'form_delivery_ticket',
        v_params
    );
    
    -- Re-raise the exception
    RAISE;
END;
$$ LANGUAGE plpgsql VOLATILE;