INSERT INTO form_module (
    id,
    deleted,
    archived,
    auto_name,
    name,
    code,
    active,
    nav_placement,
    sort_order,
    path
)
VALUES
    (1, NULL, NULL, 'Queue', 'Queue', 'queue', 'Yes', 'top', 1, '/queue'),
    (2, NULL, NULL, 'Sales', 'Sales', 'sales', 'No', 'top', 2, '/sales'),
    (3, NULL, NULL, 'Patient', 'Patient', 'patient', 'Yes', 'top', 3, '/patient'),
    (4, NULL, NULL, 'Schedule', 'Schedule', 'schedule', 'Yes', 'top', 4, '/schedule'),
    (5, NULL, NULL, 'Inventory', 'Inventory', 'inventory', 'No', 'top', 5, '/inventory'),
    (6, NULL, NULL, 'Dispense', 'Dispense', 'dispense', 'No', 'top', 6, '/dispense'),
    (7, NULL, NULL, 'Billing', 'Billing', 'billing', 'No', 'top', 7, '/billing'),
    (8, NULL, NULL, 'Compliance', 'Compliance', 'compliance', 'No', 'top', 8, '/compliance'),
    (9, NULL, NULL, 'Analytics', 'Analytics', 'analytics', 'Yes', 'top', 9, '/analytics'),
    (10, NULL, NULL, 'Settings', 'Settings', 'settings', 'Yes', 'bottom', 10, '/settings'),
    (11, NULL, NULL, 'Referral', 'Referral', 'referral', 'Yes', 'top', 11, '/referral')
ON CONFLICT (id) DO NOTHING;

UPDATE form_module SET auto_name='Queue', code='queue', name='Queue' WHERE id = 1 AND code = 'dashboard';

UPDATE form_workflow SET type='Queue' WHERE type = 'Dashboard';