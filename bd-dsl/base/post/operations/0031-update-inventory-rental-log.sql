
DO $$ BEGIN
  PERFORM drop_all_function_signatures('update_inventory_rental_log');
END $$;
CREATE OR REPLACE FUNCTION update_inventory_rental_log()
RETURNS TRIGGER AS $$
DECLARE
    v_inventory_id INTEGER;
    v_check_period INTEGER;
    v_days_between_pm INTEGER;
    v_site_id INTEGER;
    v_inventory_type TEXT;
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    SELECT 
        id, 
        days_between_checks, 
        days_between_pm,
        type
    INTO 
        v_inventory_id, 
        v_check_period, 
        v_days_between_pm,
        v_inventory_type
    FROM form_inventory 
    WHERE id = NEW.inventory_id;

    -- Check if inventory type is 'Equipment Rental'
    IF v_inventory_type != 'Equipment Rental' THEN
        RETURN NEW;
    END IF;

    v_site_id := COALESCE(
        NEW.site_id, 
        (SELECT site_id FROM form_ledger_inventory WHERE id = NEW.ledger_id)
    );

    CASE NEW.transaction_type
        WHEN 'Purchase' THEN
            -- Update if record exists
            UPDATE form_inventory_rental_log
            SET 
                site_id = v_site_id,
                status = 'AVL',
                in_stock = 'Yes',
                active = 'Yes',
                patient_id = NULL,
                in_service_datetime = NEW.transaction_date,
                next_check_date = (NEW.transaction_date + INTERVAL '1 day' * COALESCE(v_check_period, 30))::DATE,
                next_pm_date = (NEW.transaction_date + INTERVAL '1 day' * COALESCE(v_days_between_pm, 90))::DATE
            WHERE inventory_id = NEW.inventory_id 
              AND serial_no = NEW.serial_no;

            -- Insert if no matching record exists
            IF NOT FOUND THEN
                INSERT INTO form_inventory_rental_log (
                    inventory_id, 
                    serial_no, 
                    site_id, 
                    status, 
                    in_stock, 
                    active, 
                    in_service_datetime,
                    next_check_date,
                    next_pm_date,
                    last_checked_date,
                    last_pm_date,
                    ticket_no,
                    ticket_item_no
                ) VALUES (
                    NEW.inventory_id,
                    NEW.serial_no,
                    v_site_id,
                    'AVL',
                    'Yes',
                    'Yes',
                    NEW.transaction_date,
                    (NEW.transaction_date + INTERVAL '1 day' * COALESCE(v_check_period, 30))::DATE,
                    (NEW.transaction_date + INTERVAL '1 day' * COALESCE(v_days_between_pm, 90))::DATE,
                    NEW.transaction_date,
                    NEW.transaction_date,
                    NULL::text,
                    NULL::text
                );
            END IF;

        WHEN 'Transfer' THEN
            -- Update site for Transfer
            IF NEW.quantity >= 1 THEN
                UPDATE form_inventory_rental_log
                SET 
                    site_id = v_site_id,
                    patient_id = NULL
                WHERE inventory_id = NEW.inventory_id 
                  AND serial_no = NEW.serial_no;
            END IF;

        WHEN 'Recall' THEN
            -- Update status for Recall
            UPDATE form_inventory_rental_log
            SET 
                status = 'RCL',
                in_stock = NULL,
                active = NULL,
                patient_id = NULL,
                inactive_datetime = NEW.transaction_date
            WHERE inventory_id = NEW.inventory_id 
              AND serial_no = NEW.serial_no;

        WHEN 'Adjustment', 'Void' THEN
            IF NEW.quantity < 0 THEN
                UPDATE form_inventory_rental_log
                SET 
                    status = 'RET',
                    in_stock = NULL,
                    active = NULL,
                    patient_id = NULL,
                    site_id = v_site_id,
                    inactive_datetime = NEW.transaction_date
                WHERE inventory_id = NEW.inventory_id 
                  AND serial_no = NEW.serial_no;
            ELSE
                UPDATE form_inventory_rental_log
                SET 
                    status = 'AVL',
                    in_stock = 'Yes',
                    active = 'Yes',
                    patient_id = NULL,
                    site_id = v_site_id,
                    ticket_no = NULL::text,
                    ticket_item_no = NULL::text
                WHERE inventory_id = NEW.inventory_id 
                  AND serial_no = NEW.serial_no;
            END IF;

        WHEN 'Dispense' THEN
            UPDATE form_inventory_rental_log
            SET 
                status = 'DIS',
                in_stock = NULL,
                checked_out_date = NEW.transaction_date,
                patient_id = NEW.patient_id,
                ticket_no = NULL::text,
                ticket_item_no = NULL::text
            WHERE inventory_id = NEW.inventory_id 
              AND serial_no = NEW.serial_no;
    END CASE;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE TRIGGER inventory_rental_log_trigger
AFTER INSERT OR UPDATE OR DELETE 
ON form_ledger_serial
FOR EACH ROW 
EXECUTE FUNCTION update_inventory_rental_log();