
CREATE OR REPLACE FUNCTION process_inventory_transaction() R<PERSON><PERSON>NS TRIGGER AS $$
DECLARE
    v_account_id INTEGER;
    v_creator INTEGER;
    v_success BOOLEAN := FALSE;
    v_cost NUMERIC;
    v_more_granular_exists BOOLEAN := FALSE;
    v_transfer_site_account_id INTEGER;
    v_is_positive BOOLEAN;
	v_cogs_method TEXT;
    v_inventory_type TEXT;
	v_po_record RECORD;
	v_ledger_record RECORD;
	p_lot_no TEXT := NULL;
	p_serial_no TEXT := NULL;
BEGIN
    -- Log function entry
    RAISE LOG 'Entering process_inventory_transaction for inventory ID: %, transaction type: %', 
             NEW.inventory_id, NEW.transaction_type;
        
    IF TG_NARGS > 0 AND TG_ARGV[0] = 'lot' THEN
        p_lot_no = NEW.lot_no;
    ELSIF TG_NARGS > 0 AND TG_ARGV[0] = 'serial' THEN
        p_serial_no = NEW.serial_no;
        p_lot_no = NEW.lot_no;
    END IF;

    -- Check if a more granular level exists for this transaction
    -- Skip if we're processing inventory but a lot exists, or if we're processing lot but a serial exists
    IF TG_TABLE_NAME = 'form_ledger_inventory' THEN
        -- Check if lot or serial exists
        SELECT EXISTS (
            SELECT 1 FROM form_ledger_lot
            WHERE inventory_id = NEW.inventory_id
            AND site_id = NEW.site_id
            AND transaction_type = NEW.transaction_type
            AND transaction_date = NEW.transaction_date
            AND COALESCE(po_id, 0) = COALESCE(NEW.po_id, 0)
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE
        ) OR EXISTS (
            SELECT 1 FROM form_ledger_serial
            WHERE inventory_id = NEW.inventory_id
            AND site_id = NEW.site_id
            AND transaction_type = NEW.transaction_type
            AND transaction_date = NEW.transaction_date
            AND COALESCE(po_id, 0) = COALESCE(NEW.po_id, 0)
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE
        ) INTO v_more_granular_exists;
        
        IF v_more_granular_exists THEN
            RAISE LOG 'Skipping inventory processing as more granular (lot/serial) entries exist for inventory ID: %', 
                     NEW.inventory_id;
            RETURN NEW;
        END IF;
    ELSIF TG_TABLE_NAME = 'form_ledger_lot' THEN
        -- Check if serial exists
        SELECT EXISTS (
            SELECT 1 FROM form_ledger_serial
            WHERE inventory_id = NEW.inventory_id
            AND site_id = NEW.site_id
            AND transaction_type = NEW.transaction_type
            AND transaction_date = NEW.transaction_date
            AND COALESCE(po_id, 0) = COALESCE(NEW.po_id, 0)
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE
        ) INTO v_more_granular_exists;
        
        IF v_more_granular_exists THEN
            RAISE LOG 'Skipping lot processing as more granular (serial) entries exist for inventory ID: %', 
                     NEW.inventory_id;
            RETURN NEW;
        END IF;
    END IF;
    
    -- Calculate cost
    v_cost := COALESCE(NEW.acquisition_cost, 0);
    IF v_cost <= 0 THEN
        -- If no acquisition cost directly, calculate from per-unit cost
        v_cost := COALESCE(NEW.acquisition_cost_ea, 0) * COALESCE(NEW.quantity, 0);
    END IF;
    
    -- Skip if cost is zero and this is a purchase (other transaction types might have zero cost)
    IF v_cost <= 0 AND NEW.transaction_type = 'Purchase' THEN
        RAISE LOG 'Skipping process_inventory_transaction, cost is zero for Purchase of inventory ID: %', 
                 NEW.inventory_id;
        RETURN NEW;
    END IF;
    
    -- Check if we're in a closed period
    IF check_closed_period(NEW.transaction_date::timestamp) = TRUE THEN
        -- Log error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Cannot post inventory transaction in a closed accounting period',
            'Error processing inventory transaction',
            'TRIGGER',
            current_schema(),
            TG_TABLE_NAME,
            jsonb_build_object(
                'function_name', 'process_inventory_transaction',
                'inventory_id', NEW.inventory_id,
                'transaction_date', NEW.transaction_date
            )
        );

        RAISE EXCEPTION 'Cannot post inventory transaction in a closed accounting period';
    END IF;

    BEGIN
        -- Determine who created/updated this record
        v_creator := COALESCE(NEW.updated_by, NEW.created_by);
        RAISE LOG 'Processing inventory transaction for inventory ID: %, creator: %', 
                 NEW.inventory_id, v_creator;
        
        -- Get site account ID
        BEGIN
            SELECT id INTO v_account_id 
            FROM form_billing_account
            WHERE type = 'Site'
            AND site_id = NEW.site_id
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE
            LIMIT 1;
            
            IF v_account_id IS NULL THEN
                -- Log error to billing_error_log
                INSERT INTO billing_error_log (
                    error_message,
                    error_context,
                    error_type,
                    schema_name,
                    table_name,
                    additional_details
                ) VALUES (
                    'Site account not found for inventory transaction',
                    'Error processing inventory transaction',
                    'TRIGGER',
                    current_schema(),
                    TG_TABLE_NAME,
                    jsonb_build_object(
                        'function_name', 'process_inventory_transaction',
                        'inventory_id', NEW.inventory_id,
                        'site_id', NEW.site_id
                    )
                );
                
                RAISE EXCEPTION 'Site account not found for inventory transaction (site_id: %)', NEW.site_id;
            END IF;
            
            RAISE LOG 'Retrieved account_id: % for inventory transaction', v_account_id;
        EXCEPTION WHEN OTHERS THEN
            -- Log error to billing_error_log
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                SQLERRM,
                'Error retrieving account ID for inventory transaction',
                'TRIGGER',
                current_schema(),
                TG_TABLE_NAME,
                jsonb_build_object(
                    'function_name', 'process_inventory_transaction',
                    'inventory_id', NEW.inventory_id
                )
            );
            
            RAISE;
        END;
        
        -- Determine if quantity is positive or negative for adjustments and transfers
        v_is_positive := COALESCE(NEW.quantity, 0) >= 0;
        
        -- Process based on transaction type
        CASE NEW.transaction_type
            -- Purchase transaction (Debit Inventory, Credit AP)
            WHEN 'Purchase' THEN
                -- Create Inventory debit entry (increasing the asset)
                RAISE LOG 'Creating Inventory debit entry for inventory ID: %, amount: %', 
                         NEW.inventory_id, v_cost;
                
                INSERT INTO form_ledger_finance (
                    po_id,
                    account_id, 
                    source_id, 
                    source_form,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    post_datetime, 
                    transaction_datetime,
                    account_type, 
                    transaction_type,
                    quantity,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    NEW.po_id,
                    v_account_id,
                    NEW.id,
                    TG_TABLE_NAME,
                    NEW.inventory_id,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                    COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                    get_site_timestamp(NEW.site_id),
                    'Inventory',
                    'Purchase',
                    NEW.quantity,
                    v_cost,
                    0.00,
                    CURRENT_TIMESTAMP,
                    v_creator,
                    'Inventory purchase: ' || COALESCE(NEW.description, '')
                );
                
                -- Create AP credit entry (increasing the liability)
                RAISE LOG 'Creating AP credit entry for inventory ID: %, amount: %', 
                         NEW.inventory_id, v_cost;
                
                INSERT INTO form_ledger_finance (
                    po_id,
                    account_id, 
                    source_id, 
                    source_form,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    post_datetime, 
                    transaction_datetime,
                    account_type, 
                    transaction_type,
                    quantity,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    NEW.po_id,
                    v_account_id,
                    NEW.id,
                    TG_TABLE_NAME,
                    NEW.inventory_id,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                    COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                    get_site_timestamp(NEW.site_id),
                    'Purchases Clearing',
                    'Purchase',
                    NEW.quantity,
                    0.00,
                    v_cost,
                    CURRENT_TIMESTAMP,
                    v_creator,
                    'Inventory purchase: ' || COALESCE(NEW.description, '')
                );

                INSERT INTO form_ledger_finance (
                    po_id,
                    account_id, 
                    source_id, 
                    source_form,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    post_datetime, 
                    transaction_datetime,
                    account_type, 
                    transaction_type,
                    quantity,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    NEW.po_id,
                    v_account_id,
                    NEW.id,
                    TG_TABLE_NAME,
                    NEW.inventory_id,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                    COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                    get_site_timestamp(NEW.site_id),
                    'Inventory',
                    'Cost Recognition',
                    ABS(NEW.quantity),
                    v_cost,
                    0.00,
                    CURRENT_TIMESTAMP,
                    v_creator,
                    'Inventory purchase: ' || COALESCE(NEW.description, '')
                );
                
                INSERT INTO form_ledger_finance (
                    po_id,
                    account_id, 
                    source_id, 
                    source_form,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    post_datetime, 
                    transaction_datetime,
                    account_type, 
                    transaction_type,
                    quantity,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    NEW.po_id,
                    v_account_id,
                    NEW.id,
                    TG_TABLE_NAME,
                    NEW.inventory_id,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                    COALESCE(NEW.transaction_date,get_site_timestamp(NEW.site_id)),
                    get_site_timestamp(NEW.site_id),
                    'Liabilities',
                    'Cost Recognition',
                    ABS(NEW.quantity),
                    0.00,
                    v_cost,
                    CURRENT_TIMESTAMP,
                    v_creator,
                    'Inventory purchase: ' || COALESCE(NEW.description, '')
                );

            -- Recall transaction (reverse of Purchase - Credit Inventory, Debit AP)
            WHEN 'Recall' THEN
                -- Create Inventory credit entry (decreasing the asset)
                RAISE LOG 'Creating Inventory credit entry for recall of inventory ID: %, amount: %', NEW.inventory_id, v_cost;
                
                INSERT INTO form_ledger_finance (
                    po_id,
                    account_id, 
                    source_id, 
                    source_form,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    post_datetime, 
                    transaction_datetime,
                    account_type, 
                    transaction_type,
                    quantity,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    NEW.po_id,
                    v_account_id,
                    NEW.id,
                    TG_TABLE_NAME,
                    NEW.inventory_id,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                    COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                    get_site_timestamp(NEW.site_id),
                    'Inventory',
                    'Recall',
                    ABS(NEW.quantity),
                    0.00,
                    v_cost,
                    CURRENT_TIMESTAMP,
                    v_creator,
                    'Inventory recall: ' || COALESCE(NEW.description, '')
                );
                
                -- Create AP debit entry (decreasing the liability)
                RAISE LOG 'Creating AP debit entry for recall of inventory ID: %, amount: %', 
                         NEW.inventory_id, v_cost;
                
                INSERT INTO form_ledger_finance (
                    po_id,
                    account_id, 
                    source_id, 
                    source_form,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    post_datetime, 
                    transaction_datetime,
                    account_type, 
                    transaction_type,
                    quantity,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    NEW.po_id,
                    v_account_id,
                    NEW.id,
                    TG_TABLE_NAME,
                    NEW.inventory_id,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                    CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                    COALESCE(NEW.transaction_date,get_site_timestamp(NEW.site_id)),
                    get_site_timestamp(NEW.site_id),
                    'Purchases Clearing',
                    'Recall',
                    ABS(NEW.quantity),
                    v_cost,
                    0.00,
                    CURRENT_TIMESTAMP,
                    v_creator,
                    'Inventory recall: ' || COALESCE(NEW.description, '')
                );
                
            -- Void transaction (same as Recall - Credit Inventory, Debit AP)
            WHEN 'Void' THEN
                -- Create Inventory credit entry (decreasing the asset)
                RAISE LOG 'Creating Inventory credit entry for void of inventory ID: %, amount: %', 
                         NEW.inventory_id, v_cost;
            
                IF NEW.source_form = 'careplan_dt_wt_pulled' THEN
                -- Create reverse entries for COGS and Inventory when voiding a careplan_dt_wt_pulled transaction
                RAISE LOG 'Creating reverse entries for void of inventory ID: %, wt_pulled_id: %', 
                         NEW.inventory_id, NEW.source_id;

                -- Loop through existing form_ledger_finance entries for this wt_pulled_id to reverse each one
                FOR v_ledger_record IN 
                    SELECT * FROM form_ledger_finance 
                    WHERE wt_pulled_id = NEW.source_id 
                    AND inventory_id = NEW.inventory_id
                    AND account_type IN ('Inventory', 'COGS')
                    AND transaction_type = 'Dispense'
                LOOP
                    -- TODO: Need to make sure we aren't doubling because the entries in form_ledger_inventory or form_ledger_lot
                    -- Reverse the Inventory entry (Debit Inventory to increase it back)
                    IF v_ledger_record.account_type = 'Inventory' THEN
                        INSERT INTO form_ledger_finance (
                            po_id,
                            account_id, 
                            source_id, 
                            source_form,
                            inventory_id,
                            ledger_inventory_id,
                            ledger_lot_id,
                            ledger_serial_id,
                            post_datetime, 
                            transaction_datetime,
                            account_type, 
                            transaction_type,
                            quantity,
                            debit, 
                            credit, 
                            created_on, 
                            created_by,
                            notes,
                            ticket_no,
                            ticket_item_no,
                            wt_pulled_id
                        ) VALUES (
                            v_ledger_record.po_id,
                            v_ledger_record.account_id,
                            NEW.id,
                            TG_TABLE_NAME,
                            NEW.inventory_id,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                            COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                            get_site_timestamp(NEW.site_id),
                            'Inventory',
                            'Void Reversal',
                            v_ledger_record.quantity,
                            v_ledger_record.credit,  -- Reverse the original credit to debit
                            0.00,
                            CURRENT_TIMESTAMP,
                            v_creator,
                            'Inventory void reversal: ' || COALESCE(NEW.description, ''),
                            NEW.ticket_no,
                            NEW.ticket_item_no,
                            NEW.source_id
                        );
                    END IF;
                    
                    -- Reverse the COGS entry (Credit COGS to decrease it back)
                    IF v_ledger_record.account_type = 'COGS' THEN
                        INSERT INTO form_ledger_finance (
                            po_id,
                            account_id, 
                            source_id, 
                            source_form,
                            inventory_id,
                            ledger_inventory_id,
                            ledger_lot_id,
                            ledger_serial_id,
                            post_datetime, 
                            transaction_datetime,
                            account_type, 
                            transaction_type,
                            quantity,
                            debit, 
                            credit, 
                            created_on, 
                            created_by,
                            notes,
                            ticket_no,
                            ticket_item_no,
                            wt_pulled_id
                        ) VALUES (
                            v_ledger_record.po_id,
                            v_ledger_record.account_id,
                            NEW.id,
                            TG_TABLE_NAME,
                            NEW.inventory_id,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                            COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                            get_site_timestamp(NEW.site_id),
                            'COGS',
                            'Void Reversal',
                            v_ledger_record.quantity,
                            0.00,
                            v_ledger_record.debit,  -- Reverse the original debit to credit
                            CURRENT_TIMESTAMP,
                            v_creator,
                            'COGS void reversal: ' || COALESCE(NEW.description, ''),
                            NEW.ticket_no,
                            NEW.ticket_item_no,
                            NEW.source_id
                        );
                    END IF;
                END LOOP;

                ELSE
                    -- TODO Need to add appropriate reversal categories
                    INSERT INTO form_ledger_finance (
                        po_id,
                        account_id, 
                        source_id, 
                        source_form,
                        inventory_id,
                        ledger_inventory_id,
                        ledger_lot_id,
                        ledger_serial_id,
                        post_datetime, 
                        transaction_datetime,
                        account_type, 
                        transaction_type,
                        quantity,
                        debit, 
                        credit, 
                        created_on, 
                        created_by,
                        notes
                    ) VALUES (
                        NEW.po_id,
                        v_account_id,
                        NEW.id,
                        TG_TABLE_NAME,
                        NEW.inventory_id,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                        COALESCE(NEW.transaction_date,get_site_timestamp(NEW.site_id)),
                        get_site_timestamp(NEW.site_id),
                        'Inventory',
                        'Writeoff',
                        ABS(NEW.quantity),
                        0.00,
                        v_cost,
                        CURRENT_TIMESTAMP,
                        v_creator,
                        'Inventory void: ' || COALESCE(NEW.description, '')
                    );
                    
                    -- Create AP debit entry (decreasing the liability)
                    RAISE LOG 'Creating AP debit entry for void of inventory ID: %, amount: %', 
                            NEW.inventory_id, v_cost;
                    
                    INSERT INTO form_ledger_finance (
                        po_id,
                        account_id, 
                        source_id, 
                        source_form,
                        inventory_id,
                        ledger_inventory_id,
                        ledger_lot_id,
                        ledger_serial_id,
                        post_datetime, 
                        transaction_datetime,
                        account_type, 
                        transaction_type,
                        quantity,
                        debit, 
                        credit, 
                        created_on, 
                        created_by,
                        notes
                    ) VALUES (
                        NEW.po_id,
                        v_account_id,
                        NEW.id,
                        TG_TABLE_NAME,
                        NEW.inventory_id,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                        COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                        get_site_timestamp(NEW.site_id),
                        'Purchases Clearing',
                        'Purchase Reversal',
                        ABS(NEW.quantity),
                        v_cost,
                        0.00,
                        CURRENT_TIMESTAMP,
                        v_creator,
                        'Inventory void: ' || COALESCE(NEW.description, '')
                    );

                    INSERT INTO form_ledger_finance (
                        po_id,
                        account_id, 
                        source_id, 
                        source_form,
                        inventory_id,
                        ledger_inventory_id,
                        ledger_lot_id,
                        ledger_serial_id,
                        post_datetime, 
                        transaction_datetime,
                        account_type, 
                        transaction_type,
                        quantity,
                        debit, 
                        credit, 
                        created_on, 
                        created_by,
                        notes
                    ) VALUES (
                        NEW.po_id,
                        v_account_id,
                        NEW.id,
                        TG_TABLE_NAME,
                        NEW.inventory_id,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                        COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                        get_site_timestamp(NEW.site_id),
                        'Inventory',
                        'Cost Recognition',
                        ABS(NEW.quantity),
                        v_cost,
                        0.00,
                        CURRENT_TIMESTAMP,
                        v_creator,
                        'Inventory dispense: ' || COALESCE(NEW.description, '')
                    );
                    
                    -- Create AP debit entry (decreasing the liability)
                    RAISE LOG 'Creating AP debit entry for dispense of inventory ID: %, amount: %', 
                            NEW.inventory_id, v_cost;
                    
                    INSERT INTO form_ledger_finance (
                        po_id,
                        account_id, 
                        source_id, 
                        source_form,
                        inventory_id,
                        ledger_inventory_id,
                        ledger_lot_id,
                        ledger_serial_id,
                        post_datetime, 
                        transaction_datetime,
                        account_type, 
                        transaction_type,
                        quantity,
                        debit, 
                        credit, 
                        created_on, 
                        created_by,
                        notes
                    ) VALUES (
                        NEW.po_id,
                        v_account_id,
                        NEW.id,
                        TG_TABLE_NAME,
                        NEW.inventory_id,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                        COALESCE(NEW.transaction_date,get_site_timestamp(NEW.site_id)),
                        get_site_timestamp(NEW.site_id),
                        'Liabilities',
                        'Cost Recognition',
                        ABS(NEW.quantity),
                        0.00,
                        v_cost,
                        CURRENT_TIMESTAMP,
                        v_creator,
                        'Inventory dispense: ' || COALESCE(NEW.description, '')
                    );

                END IF;
                
            -- Dispense transaction ( Debit Inventory, Credit AP)
            WHEN 'Dispense' THEN
                -- Create Inventory credit entry (decreasing the asset)
                RAISE LOG 'Creating Inventory credit entry for dispense of inventory ID: %, amount: %', 
                         NEW.inventory_id, v_cost;

                -- Get company's COGS method (with FIFO default)
                SELECT COALESCE(cogs_method, 'FIFO') INTO v_cogs_method 
                FROM form_company 
                WHERE id = 1;
                
                SELECT inv.type::text as type,(COALESCE(inv.actual_cost,0) * COALESCE(NEW.quantity,0))::numeric as actual_cost INTO v_inventory_type, v_cost
                FROM form_inventory inv
                WHERE inv.id = NEW.inventory_id;

                IF v_inventory_type = 'Billable' THEN
                    INSERT INTO form_ledger_finance (
                            account_id, 
                            source_id, 
                            source_form,
                            inventory_id,
                            ledger_inventory_id,
                            post_datetime, 
                            transaction_datetime,
                            account_type, 
                            transaction_type,
                            quantity,
                            debit, 
                            credit, 
                            created_on, 
                            created_by,
                            notes,
                            ticket_no,
                            ticket_item_no,
                            wt_pulled_id
                        ) VALUES (
                            v_account_id,
                            NEW.id,
                            TG_TABLE_NAME,
                            NEW.inventory_id,
                            NEW.id,
                            COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                            get_site_timestamp(NEW.site_id),
                            'Inventory',
                            'Dispense',
                            ABS(NEW.quantity),
                            0.00,
                            v_cost,
                            CURRENT_TIMESTAMP,
                            v_creator,
                            'Billable transfer out: ' || COALESCE(NEW.description, ''),
                            NEW.ticket_no,
                            NEW.ticket_item_no,
                            NEW.source_id
                        );
                        INSERT INTO form_ledger_finance (
                            account_id, 
                            source_id, 
                            source_form,
                            inventory_id,
                            ledger_inventory_id,
                            post_datetime, 
                            transaction_datetime,
                            account_type, 
                            transaction_type,
                            quantity,
                            debit, 
                            credit, 
                            created_on, 
                            created_by,
                            notes,
                            ticket_no,
                            ticket_item_no,
                            wt_pulled_id
                        ) VALUES (
                            v_account_id,
                            NEW.id,
                            TG_TABLE_NAME,
                            NEW.inventory_id,
                            NEW.id,
                            COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                            get_site_timestamp(NEW.site_id),
                            'COGS',
                            'Dispense',
                            ABS(NEW.quantity),
                            v_cost,
                            0.00,
                            CURRENT_TIMESTAMP,
                            v_creator,
                            'Billable transfer out: ' || COALESCE(NEW.description, ''),
                            NEW.ticket_no,
                            NEW.ticket_item_no,
                            NEW.source_id
                        );
                ELSE
                    -- Create a temporary table to store PO allocation
                    CREATE TEMP TABLE IF NOT EXISTS temp_po_allocation (
                        po_id INTEGER,
                        quantity NUMERIC,
                        unit_cost NUMERIC,
                        total_cost NUMERIC
                    ) ON COMMIT DROP;
                    -- Clear the temporary table
                    DELETE FROM temp_po_allocation;

                    -- Calculate how much to allocate from each PO based on FIFO/LIFO
                    INSERT INTO temp_po_allocation (po_id, quantity, unit_cost, total_cost)
                    SELECT * FROM calculate_po_allocation(
                        NEW.inventory_id, 
                        NEW.site_id, 
                        ABS(NEW.quantity)::numeric, 
                        v_cogs_method,
                        TG_TABLE_NAME,
                        p_lot_no,
                        p_serial_no
                    );
                    
                    -- Now create entries for each PO allocation
                    FOR v_po_record IN SELECT * FROM temp_po_allocation WHERE quantity > 0 LOOP
                        -- Credit Inventory at this site for this PO (reduce inventory)
                        INSERT INTO form_ledger_finance (
                            po_id,
                            account_id, 
                            source_id, 
                            source_form,
                            inventory_id,
                            ledger_inventory_id,
                            ledger_lot_id,
                            ledger_serial_id,
                            post_datetime, 
                            transaction_datetime,
                            account_type, 
                            transaction_type,
                            quantity,
                            debit, 
                            credit, 
                            created_on, 
                            created_by,
                            notes,
                            ticket_no,
                            ticket_item_no,
                            wt_pulled_id
                        ) VALUES (
                            v_po_record.po_id,
                            v_account_id,
                            NEW.id,
                            TG_TABLE_NAME,
                            NEW.inventory_id,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                            COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                            get_site_timestamp(NEW.site_id),
                            'Inventory',
                            'Dispense',
                            v_po_record.quantity,
                            0.00,
                            v_po_record.total_cost,
                            CURRENT_TIMESTAMP,
                            v_creator,
                            'Inventory transfer out: ' || COALESCE(NEW.description, ''),
                            NEW.ticket_no,
                            NEW.ticket_item_no,
                            NEW.source_id
                        );
                        INSERT INTO form_ledger_finance (
                            po_id,
                            account_id, 
                            source_id, 
                            source_form,
                            inventory_id,
                            ledger_inventory_id,
                            ledger_lot_id,
                            ledger_serial_id,
                            post_datetime, 
                            transaction_datetime,
                            account_type, 
                            transaction_type,
                            quantity,
                            debit, 
                            credit, 
                            created_on, 
                            created_by,
                            notes,
                            ticket_no,
                            ticket_item_no,
                            wt_pulled_id
                        ) VALUES (
                            v_po_record.po_id,
                            v_account_id,
                            NEW.id,
                            TG_TABLE_NAME,
                            NEW.inventory_id,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                            COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                            get_site_timestamp(NEW.site_id),
                            'COGS',
                            'Dispense',
                            v_po_record.quantity,
                            v_po_record.total_cost,
                            0.00,
                            CURRENT_TIMESTAMP,
                            v_creator,
                            'Inventory transfer out: ' || COALESCE(NEW.description, ''),
                            NEW.ticket_no,
                            NEW.ticket_item_no,
                            NEW.source_id
                        );
                    END LOOP;
                    
                    -- Drop the temporary table
                    DROP TABLE IF EXISTS temp_po_allocation;
                END IF;

            -- Adjustment transaction (depends on positive/negative adjustment)
            WHEN 'Adjustment' THEN
                IF v_is_positive THEN
                    -- Positive adjustment (add to inventory)
                    -- Create Inventory debit entry (increasing the asset)
                    RAISE LOG 'Creating Inventory debit entry for positive adjustment of inventory ID: %, amount: %', 
                             NEW.inventory_id, v_cost;
                    
                    INSERT INTO form_ledger_finance (
                        po_id,
                        account_id, 
                        source_id, 
                        source_form,
                        inventory_id,
                        ledger_inventory_id,
                        ledger_lot_id,
                        ledger_serial_id,
                        post_datetime, 
                        transaction_datetime,
                        account_type, 
                        transaction_type,
                        quantity,
                        debit, 
                        credit, 
                        created_on, 
                        created_by,
                        notes
                    ) VALUES (
                        NEW.po_id,
                        v_account_id,
                        NEW.id,
                        TG_TABLE_NAME,
                        NEW.inventory_id,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                        COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                        get_site_timestamp(NEW.site_id),
                        'Inventory',
                        'Adjustment',
                        NEW.quantity,
                        v_cost,
                        0.00,
                        CURRENT_TIMESTAMP,
                        v_creator,
                        'Inventory positive adjustment: ' || COALESCE(NEW.description, '')
                    );
                    
                    -- Create Inventory Adjustment credit entry (balancing entry)
                    RAISE LOG 'Creating Inventory Adjustment credit entry for positive adjustment of inventory ID: %, amount: %', 
                             NEW.inventory_id, v_cost;
                    
                    INSERT INTO form_ledger_finance (
                        po_id,
                        account_id, 
                        source_id, 
                        source_form,
                        inventory_id,
                        ledger_inventory_id,
                        ledger_lot_id,
                        ledger_serial_id,
                        post_datetime, 
                        transaction_datetime,
                        account_type, 
                        transaction_type,
                        quantity,
                        debit, 
                        credit, 
                        created_on, 
                        created_by,
                        notes
                    ) VALUES (
                        NEW.po_id,
                        v_account_id,
                        NEW.id,
                        TG_TABLE_NAME,
                        NEW.inventory_id,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                        COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                        get_site_timestamp(NEW.site_id),
                        'Inventory Adjustment',
                        'Adjustment',
                        NEW.quantity,
                        0.00,
                        v_cost,
                        CURRENT_TIMESTAMP,
                        v_creator,
                        'Inventory positive adjustment: ' || COALESCE(NEW.description, '')
                    );
                    
                ELSE
                    -- Negative adjustment (remove from inventory)
                    -- Create Inventory credit entry (decreasing the asset)
                    RAISE LOG 'Creating Inventory credit entry for negative adjustment of inventory ID: %, amount: %', 
                             NEW.inventory_id, v_cost;
                    
                    INSERT INTO form_ledger_finance (
                        po_id,
                        account_id, 
                        source_id, 
                        source_form,
                        inventory_id,
                        ledger_inventory_id,
                        ledger_lot_id,
                        ledger_serial_id,
                        post_datetime, 
                        transaction_datetime,
                        account_type, 
                        transaction_type,
                        quantity,
                        debit, 
                        credit, 
                        created_on, 
                        created_by,
                        notes
                    ) VALUES (
                        NEW.po_id,
                        v_account_id,
                        NEW.id,
                        TG_TABLE_NAME,
                        NEW.inventory_id,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                        COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                        get_site_timestamp(NEW.site_id),
                        'Inventory',
                        'Adjustment',
                        ABS(NEW.quantity),
                        0.00,
                        v_cost,
                        CURRENT_TIMESTAMP,
                        v_creator,
                        'Inventory negative adjustment: ' || COALESCE(NEW.description, '')
                    );
                    
                    -- Create Inventory Adjustment debit entry (balancing entry)
                    RAISE LOG 'Creating Inventory Adjustment debit entry for negative adjustment of inventory ID: %, amount: %', 
                             NEW.inventory_id, v_cost;
                    
                    INSERT INTO form_ledger_finance (
                        po_id,
                        account_id, 
                        source_id, 
                        source_form,
                        inventory_id,
                        ledger_inventory_id,
                        ledger_lot_id,
                        ledger_serial_id,
                        post_datetime, 
                        transaction_datetime,
                        account_type, 
                        transaction_type,
                        quantity,
                        debit, 
                        credit, 
                        created_on, 
                        created_by,
                        notes
                    ) VALUES (
                        NEW.po_id,
                        v_account_id,
                        NEW.id,
                        TG_TABLE_NAME,
                        NEW.inventory_id,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                        COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                        get_site_timestamp(NEW.site_id),
                        'Inventory Adjustment',
                        'Adjustment',
                        ABS(NEW.quantity),
                        v_cost,
                        0.00,
                        CURRENT_TIMESTAMP,
                        v_creator,
                        'Inventory negative adjustment: ' || COALESCE(NEW.description, '')
                    );
                END IF;
        WHEN 'Transfer' THEN
            IF v_is_positive IS FALSE THEN
                IF NEW.po_id IS NULL THEN
                    -- Negative adjustment (remove from inventory)
                    -- Create Inventory credit entry (decreasing the asset)
                    RAISE LOG 'Creating Inventory credit entry for transfer of inventory ID: %, amount: %', 
                                NEW.inventory_id, v_cost;

                    -- Create a temporary table to store PO allocation
                    CREATE TEMP TABLE IF NOT EXISTS temp_po_allocation (
                        po_id INTEGER,
                        quantity NUMERIC,
                        unit_cost NUMERIC,
                        total_cost NUMERIC
                    ) ON COMMIT DROP;
                    
                    -- Clear the temporary table
                    DELETE FROM temp_po_allocation;
                    
                    -- Calculate how much to allocate from each PO based on FIFO/LIFO
                    INSERT INTO temp_po_allocation (po_id, quantity, unit_cost, total_cost)
                    SELECT * FROM calculate_po_allocation(
                        NEW.inventory_id, 
                        NEW.site_id, 
                        ABS(NEW.quantity)::numeric, 
                        v_cogs_method,
                        TG_TABLE_NAME,
                        p_lot_no,
                        p_serial_no
                    );
                    
                    -- Now create entries for each PO allocation
                    FOR v_po_record IN SELECT * FROM temp_po_allocation WHERE quantity > 0 LOOP
                        -- Credit Inventory at this site for this PO (reduce inventory)
                        INSERT INTO form_ledger_finance (
                            po_id,
                            account_id, 
                            source_id, 
                            source_form,
                            inventory_id,
                            ledger_inventory_id,
                            ledger_lot_id,
                            ledger_serial_id,
                            post_datetime, 
                            transaction_datetime,
                            account_type, 
                            transaction_type,
                            quantity,
                            debit, 
                            credit, 
                            created_on, 
                            created_by,
                            notes
                        ) VALUES (
                            v_po_record.po_id,
                            v_account_id,
                            NEW.id,
                            TG_TABLE_NAME,
                            NEW.inventory_id,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                            COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                            get_site_timestamp(NEW.site_id),
                            'Inventory',
                            'Transfer',
                            v_po_record.quantity,
                            0.00,
                            v_po_record.total_cost,
                            CURRENT_TIMESTAMP,
                            v_creator,
                            'Inventory transfer out: ' || COALESCE(NEW.description, '')
                        );
                        INSERT INTO form_ledger_finance (
                            po_id,
                            account_id, 
                            source_id, 
                            source_form,
                            inventory_id,
                            ledger_inventory_id,
                            ledger_lot_id,
                            ledger_serial_id,
                            post_datetime, 
                            transaction_datetime,
                            account_type, 
                            transaction_type,
                            quantity,
                            debit, 
                            credit, 
                            created_on, 
                            created_by,
                            notes
                        ) VALUES (
                            v_po_record.po_id,
                            v_account_id,
                            NEW.id,
                            TG_TABLE_NAME,
                            NEW.inventory_id,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                            COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                            get_site_timestamp(NEW.site_id),
                            'Purchases Clearing',
                            'Transfer',
                            v_po_record.quantity,
                            v_po_record.total_cost,
                            0.00,
                            CURRENT_TIMESTAMP,
                            v_creator,
                            'Inventory transfer out: ' || COALESCE(NEW.description, '')
                        );
                    END LOOP;
                    
                    -- Drop the temporary table
                    DROP TABLE IF EXISTS temp_po_allocation;
                ELSE
                    -- We have the PO ID, so we can create the entry directly
                    -- Credit Inventory at this site (reduce inventory)
                    INSERT INTO form_ledger_finance (
                        po_id,
                        account_id, 
                        source_id, 
                        source_form,
                        inventory_id,
                        ledger_inventory_id,
                        ledger_lot_id,
                        ledger_serial_id,
                        post_datetime, 
                        transaction_datetime,
                        account_type, 
                        transaction_type,
                        quantity,
                        debit, 
                        credit, 
                        created_on, 
                        created_by,
                        notes
                    ) VALUES (
                        NEW.po_id,
                        v_account_id,
                        NEW.id,
                        TG_TABLE_NAME,
                        NEW.inventory_id,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                        COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                        get_site_timestamp(NEW.site_id),
                        'Inventory',
                        'Transfer',
                        ABS(NEW.quantity),
                        v_cost,
                        0.00,
                        CURRENT_TIMESTAMP,
                        v_creator,
                        'Inventory transfer out: ' || COALESCE(NEW.description, '')
                    );
                    INSERT INTO form_ledger_finance (
                        po_id,
                        account_id, 
                        source_id, 
                        source_form,
                        inventory_id,
                        ledger_inventory_id,
                        ledger_lot_id,
                        ledger_serial_id,
                        post_datetime, 
                        transaction_datetime,
                        account_type, 
                        transaction_type,
                        quantity,
                        debit, 
                        credit, 
                        created_on, 
                        created_by,
                        notes
                    ) VALUES (
                        NEW.po_id,
                        v_account_id,
                        NEW.id,
                        TG_TABLE_NAME,
                        NEW.inventory_id,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                        COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                        get_site_timestamp(NEW.site_id),
                        'Purchases Clearing',
                        'Transfer',
                        ABS(NEW.quantity),
                        0.00,
                        v_cost,
                        CURRENT_TIMESTAMP,
                        v_creator,
                        'Inventory transfer out: ' || COALESCE(NEW.description, '')
                    );
                END IF;
        ELSE
        	IF NEW.po_id IS NULL THEN
            	-- Positive adjustment (add to inventory)
            	-- Create Inventory debit entry (increasing the asset)
            	RAISE LOG 'Creating Inventory debit entry for positive adjustment of inventory ID: %, amount: %', 
                    NEW.inventory_id, v_cost;

                    -- Create a temporary table to store PO allocation
                    CREATE TEMP TABLE IF NOT EXISTS temp_po_allocation(
                        po_id INTEGER,
                        quantity NUMERIC,
                        unit_cost NUMERIC,
                        total_cost NUMERIC
                    ) ON COMMIT DROP;
                    
                    -- Clear the temporary table
                    DELETE FROM temp_po_allocation;
                    
                    -- Calculate how much to allocate from each PO based on FIFO/LIFO
                    INSERT INTO temp_po_allocation (po_id, quantity, unit_cost, total_cost)
                    SELECT * FROM calculate_po_allocation(
                        NEW.inventory_id, 
                        NEW.site_id, 
                        ABS(NEW.quantity)::numeric, 
                        'LIFO', -- Use LIFO here since we just got it in
                        TG_TABLE_NAME,
                        p_lot_no,
                        p_serial_no
                    );
                    
                    -- Now create entries for each PO allocation
                    FOR v_po_record IN SELECT * FROM temp_po_allocation WHERE quantity > 0 LOOP
                        -- Credit Inventory at this site for this PO (reduce inventory)
                        INSERT INTO form_ledger_finance (
                            po_id,
                            account_id, 
                            source_id, 
                            source_form,
                            inventory_id,
                            ledger_inventory_id,
                            ledger_lot_id,
                            ledger_serial_id,
                            post_datetime, 
                            transaction_datetime,
                            account_type, 
                            transaction_type,
                            quantity,
                            debit, 
                            credit, 
                            created_on, 
                            created_by,
                            notes
                        ) VALUES (
                            v_po_record.po_id,
                            v_account_id,
                            NEW.id,
                            TG_TABLE_NAME,
                            NEW.inventory_id,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                            COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                            get_site_timestamp(NEW.site_id),
                            'Inventory',
                            'Transfer',
                            v_po_record.quantity,
                            v_po_record.total_cost,
                            0.00,
                            CURRENT_TIMESTAMP,
                            v_creator,
                            'Inventory transfer in: ' || COALESCE(NEW.description, '')
                        );
                        INSERT INTO form_ledger_finance (
                            po_id,
                            account_id, 
                            source_id, 
                            source_form,
                            inventory_id,
                            ledger_inventory_id,
                            ledger_lot_id,
                            ledger_serial_id,
                            post_datetime, 
                            transaction_datetime,
                            account_type, 
                            transaction_type,
                            quantity,
                            debit, 
                            credit, 
                            created_on, 
                            created_by,
                            notes
                        ) VALUES (
                            v_po_record.po_id,
                            v_account_id,
                            NEW.id,
                            TG_TABLE_NAME,
                            NEW.inventory_id,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                            CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                            COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                            get_site_timestamp(NEW.site_id),
                            'Purchases Clearing',
                            'Transfer',
                            v_po_record.quantity,
                            0.00,
                            v_po_record.total_cost,
                            CURRENT_TIMESTAMP,
                            v_creator,
                            'Inventory transfer in: ' || COALESCE(NEW.description, '')
                        );
                    END LOOP;
                    
                    -- Drop the temporary table
                    DROP TABLE IF EXISTS temp_po_allocation;
                ELSE
                    -- We have the PO ID, so we can create the entry directly
                    -- Credit Inventory at this site (reduce inventory)
                    INSERT INTO form_ledger_finance (
                        po_id,
                        account_id, 
                        source_id, 
                        source_form,
                        inventory_id,
                        ledger_inventory_id,
                        ledger_lot_id,
                        ledger_serial_id,
                        post_datetime, 
                        transaction_datetime,
                        account_type, 
                        transaction_type,
                        quantity,
                        debit, 
                        credit, 
                        created_on, 
                        created_by,
                        notes
                    ) VALUES (
                        NEW.po_id,
                        v_account_id,
                        NEW.id,
                        TG_TABLE_NAME,
                        NEW.inventory_id,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                        COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                        get_site_timestamp(NEW.site_id),
                        'Inventory',
                        'Transfer',
                        ABS(NEW.quantity),
                        0.00,
                        v_cost,
                        CURRENT_TIMESTAMP,
                        v_creator,
                        'Inventory transfer in: ' || COALESCE(NEW.description, '')
                    );
                    INSERT INTO form_ledger_finance (
                        po_id,
                        account_id, 
                        source_id, 
                        source_form,
                        inventory_id,
                        ledger_inventory_id,
                        ledger_lot_id,
                        ledger_serial_id,
                        post_datetime, 
                        transaction_datetime,
                        account_type, 
                        transaction_type,
                        quantity,
                        debit, 
                        credit, 
                        created_on, 
                        created_by,
                        notes
                    ) VALUES (
                        NEW.po_id,
                        v_account_id,
                        NEW.id,
                        TG_TABLE_NAME,
                        NEW.inventory_id,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_inventory' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_lot' THEN NEW.id ELSE NULL END,
                        CASE WHEN TG_TABLE_NAME = 'form_ledger_serial' THEN NEW.id ELSE NULL END,
                        COALESCE(NEW.transaction_date, get_site_timestamp(NEW.site_id)),
                        get_site_timestamp(NEW.site_id),
                        'Purchases Clearing',
                        'Transfer',
                        ABS(NEW.quantity),
                        v_cost,
                        0.00,
                        CURRENT_TIMESTAMP,
                        v_creator,
                        'Inventory transfer in: ' || COALESCE(NEW.description, '')
                    );
                END IF;
        END IF;
       END CASE;
        
        v_success := TRUE;
        RAISE LOG 'Inventory transaction successfully processed for inventory ID: %', NEW.inventory_id;
        
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        -- Log the error
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Transaction failed during inventory transaction processing',
            'TRIGGER',
            current_schema(),
            TG_TABLE_NAME,
            jsonb_build_object(
                'function_name', 'process_inventory_transaction',
                'inventory_id', NEW.inventory_id,
                'transaction_type', NEW.transaction_type,
                'success_flag', v_success
            )
        );
        
        RAISE; -- Re-throw the exception to rollback the transaction
    END;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Create triggers for each inventory table
CREATE OR REPLACE TRIGGER trg_process_inventory_transaction
AFTER INSERT ON form_ledger_inventory
FOR EACH ROW
EXECUTE FUNCTION process_inventory_transaction('inventory');

CREATE OR REPLACE TRIGGER trg_process_lot_transaction
AFTER INSERT ON form_ledger_lot
FOR EACH ROW
EXECUTE FUNCTION process_inventory_transaction('lot');

CREATE OR REPLACE TRIGGER trg_process_serial_transaction
AFTER INSERT ON form_ledger_serial
FOR EACH ROW
EXECUTE FUNCTION process_inventory_transaction('serial');