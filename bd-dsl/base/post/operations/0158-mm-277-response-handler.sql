-- Medical Claim 277 Response Handler
-- Parses 277 (Claim Status) responses and populates form_med_claim_resp_277 and subforms

DROP FUNCTION IF EXISTS parse_277_response;
CREATE OR REPLACE FUNCTION parse_277_response(
    p_response_id INTEGER,
    p_response_json JSONB,
    p_created_by INTEGER,
    p_created_dt TIMESTAMP
) RETURNS VOID AS $$
DECLARE
    v_main_record_id INTEGER;
    v_subform_id INTEGER;
    v_service_line_id INTEGER;
    v_contact_id INTEGER;
    v_status_history_id INTEGER;
    
    v_transaction JSONB;
    v_payer JSONB;
    v_claim_transaction JSONB;
    v_claim_detail JSONB;
    v_patient_detail JSONB;
    v_claim JSONB;
    v_claim_status JSONB;
    v_info_status JSONB;
    v_service_line JSONB;
    v_service_status JSONB;
    v_contact_method JSONB;
    v_detail_info JSONB;

    v_claim_no TEXT;
    v_patient_id INTEGER;
    v_site_id INTEGER;
    v_payer_id INTEGER;
    
    -- Main 277 record fields
    v_control_number TEXT;
    v_trading_partner_claim_number TEXT;
    v_patient_account_number TEXT;
    v_service_date DATE;
    v_status_code TEXT;
    v_status_code_value TEXT;
    v_status_category_code TEXT;
    v_status_category_code_value TEXT;
    v_entity_identifier_code TEXT;
    v_entity_identifier_code_value TEXT;
    v_total_claim_charge_amount NUMERIC;
    v_claim_payment_amount NUMERIC;
    v_status_effective_date DATE;
    v_payer_organization_name TEXT;
    v_payer_identification TEXT;
    v_patient_control_number TEXT;

    -- Additional 277 fields from CSON
    v_claim_transaction_batch_number TEXT;
    v_bill_type_identifier TEXT;
    v_reference_identification TEXT;
    v_service_provider_organization_name TEXT;
    v_service_provider_npi TEXT;
    v_service_begin_date DATE;
    v_service_end_date DATE;
    v_transaction_set_creation_date DATE;
    v_transaction_set_creation_time TEXT;
    v_subscriber_last_name TEXT;
    v_subscriber_first_name TEXT;
    v_subscriber_middle_name TEXT;
    v_subscriber_suffix TEXT;
    v_subscriber_member_id TEXT;
    v_dependent_last_name TEXT;
    v_dependent_first_name TEXT;
    v_dependent_middle_name TEXT;
    v_dependent_suffix TEXT;
    v_payer_centers_for_medicare_and_medicaid_service_plan_id TEXT;
    v_service_provider_status_information_effective_date DATE;
    v_service_provider_health_care_claim_status_category_code TEXT;
    v_service_provider_health_care_claim_status_category_code_value TEXT;
    v_service_provider_status_code TEXT;
    v_service_provider_status_code_value TEXT;
    v_service_provider_entity_identifier_code TEXT;
    v_service_provider_entity_identifier_code_value TEXT;
    v_claim_adjudicated_finalized_date DATE;
    v_claim_remittance_date DATE;
    v_claim_remittance_trace_number TEXT;
    v_clearinghouse_trace_number TEXT;
    v_pharmacy_prescription_number TEXT;
    v_voucher_identifier TEXT;
    v_adjudicated_finalized_date DATE;
    v_remittance_date DATE;
    
    -- Fields from original response log record
    v_raw_report_url TEXT;
    v_s3_filehash TEXT;

    -- Service line fields
    v_line_item_control_number TEXT;
    v_service_line_date DATE;
    v_procedure_code TEXT;
    v_charge_amount NUMERIC;
    v_amount_paid NUMERIC;
    v_submitted_units NUMERIC;
    
    -- Meta fields
    v_meta_submitter_id TEXT;
    v_meta_sender_id TEXT;
    v_meta_biller_id TEXT;
    v_trace_id TEXT;
    v_application_mode TEXT;
    
    v_i INTEGER;
    v_j INTEGER;
    v_k INTEGER;
    v_l INTEGER;
    v_m INTEGER;
    v_n INTEGER;
    v_o INTEGER;
    v_p INTEGER;
    v_q INTEGER;
    
    -- Logging variables
    v_start_time TIMESTAMP;
    v_execution_time INTERVAL;
    v_error_message TEXT;
    v_params JSONB;
    v_result_info JSONB;
    v_claims_processed INTEGER := 0;
BEGIN
    -- Record start time
    v_start_time := clock_timestamp();
    RAISE LOG 'parse_277_response: Start processing response_id=%', p_response_id;
    
    -- Build parameters JSON for logging
    v_params := jsonb_build_object(
        'response_id', p_response_id,
        'response_type', '277',
        'created_by', p_created_by
    );
    
    BEGIN  -- Start exception block
        -- Log function call start
        BEGIN
            PERFORM log_billing_function(
                'parse_277_response'::tracked_function,
                v_params::jsonb,
                NULL::jsonb,
                NULL::text,
                clock_timestamp() - v_start_time
            );
        EXCEPTION WHEN OTHERS THEN
            -- Ignore logging errors
            NULL;
        END;
        
        -- Get additional fields from original response log record
        SELECT  raw_report_url, s3_filehash
        INTO v_raw_report_url, v_s3_filehash
        FROM form_med_claim_resp_log
        WHERE id = p_response_id
            AND deleted IS NOT TRUE
            AND archived IS NOT TRUE;
        RAISE LOG 'parse_277_response: Loaded raw_report_url and s3_filehash for response_id=%', p_response_id;
        
        -- Process each transaction in the response
        FOR v_transaction IN SELECT * FROM jsonb_array_elements(p_response_json->'transactions')
        LOOP
            RAISE LOG 'parse_277_response: Processing transaction';
            
            -- Extract control number from transaction
            v_control_number := v_transaction->>'controlNumber';
            
            -- Process each detail info in the transaction  
            FOR v_detail_info IN SELECT * FROM jsonb_array_elements(v_transaction->'detailInfo')
            LOOP
                -- Link to original claim using patient control number
                v_patient_control_number := v_detail_info->>'patientControlNumber';
                RAISE LOG 'parse_277_response: Processing detailInfo for patient_control_number=%', v_patient_control_number;
                
                SELECT mc.claim_no, mc.patient_id, mc.site_id, mc.payer_id 
                INTO v_claim_no, v_patient_id, v_site_id, v_payer_id
                FROM form_med_claim mc
                INNER JOIN form_med_claim_info mci ON mci.id = mc.id
                WHERE mci.patient_control_number = v_patient_control_number
                    AND mc.deleted IS NOT TRUE
                    AND mc.archived IS NOT TRUE
                    AND COALESCE(mc.void,'No') = 'No'
                    AND mci.deleted IS NOT TRUE
                    AND mci.archived IS NOT TRUE
                ORDER BY mc.id DESC
                LIMIT 1;

                IF v_claim_no IS NULL THEN
                    RAISE LOG 'parse_277_response: No claim found for patient_control_number=%', v_patient_control_number;
                    PERFORM log_billing_function(
                        'mm_277_handler'::tracked_function, 
                        jsonb_build_object('level', 'warning')::jsonb,
                        jsonb_build_object('message', 'Could not find claim for patient control number: ' || v_patient_control_number)::jsonb,
                        NULL::text,
                        interval '0'
                    );
                    CONTINUE;
                END IF;
                
                -- Extract transaction-level fields
                v_reference_identification := v_transaction->>'referenceIdentification';
                BEGIN
                    v_transaction_set_creation_date := (v_transaction->>'transactionSetCreationDate')::DATE;
                EXCEPTION WHEN OTHERS THEN
                    v_transaction_set_creation_date := NULL;
                    RAISE WARNING 'Failed to convert transactionSetCreationDate to date: %, using NULL instead', 
                                 v_transaction->>'transactionSetCreationDate';
                END;
                v_transaction_set_creation_time := v_transaction->>'transactionSetCreationTime'; -- Keep as text
                v_clearinghouse_trace_number := p_response_json->>'referencedTransactionTraceNumber';
                v_pharmacy_prescription_number := p_response_json->>'pharmacyPrescriptionNumber';
                v_voucher_identifier := p_response_json->>'voucherIdentifier';
                
                FOR v_j IN 0..jsonb_array_length(v_transaction->'payers') - 1 LOOP
                    v_payer := v_transaction->'payers'->v_j;
                    v_payer_organization_name := v_payer->>'organizationName';
                    v_payer_identification := v_payer->>'payerIdentification';
                    v_payer_centers_for_medicare_and_medicaid_service_plan_id := v_payer->>'centersForMedicareAndMedicaidServicePlanId';
                    RAISE LOG 'parse_277_response: Processing payer % (%), claim_no=%', v_payer_organization_name, v_payer_identification, v_claim_no;
                    
                    FOR v_k IN 0..jsonb_array_length(v_payer->'claimStatusTransactions') - 1 LOOP
                        v_claim_transaction := v_payer->'claimStatusTransactions'->v_k;
                        v_claim_transaction_batch_number := v_claim_transaction->>'claimTransactionBatchNumber';
                        RAISE LOG 'parse_277_response: Processing claimStatusTransaction batch_number=%', v_claim_transaction_batch_number;
                        
                        FOR v_l IN 0..jsonb_array_length(v_claim_transaction->'claimStatusDetails') - 1 LOOP
                            v_claim_detail := v_claim_transaction->'claimStatusDetails'->v_l;
                            
                            -- Extract service provider information
                            v_service_provider_organization_name := v_claim_detail->'serviceProvider'->>'organizationName';
                            v_service_provider_npi := v_claim_detail->'serviceProvider'->>'npi';
                            RAISE LOG 'parse_277_response: Service provider org=% npi=%', v_service_provider_organization_name, v_service_provider_npi;
                            
                            -- Extract service provider status information (if available)
                            IF v_claim_detail ? 'serviceProviderClaimStatuses' AND 
                               jsonb_array_length(v_claim_detail->'serviceProviderClaimStatuses') > 0 THEN
                                DECLARE
                                    v_sp_status JSONB;
                                BEGIN
                                    v_sp_status := v_claim_detail->'serviceProviderClaimStatuses'->0;
                                    BEGIN
                                        v_service_provider_status_information_effective_date := (v_sp_status->>'statusInformationEffectiveDate')::DATE;
                                    EXCEPTION WHEN OTHERS THEN
                                        v_service_provider_status_information_effective_date := NULL;
                                    END;
                                    
                                    IF v_sp_status ? 'serviceProviderStatuses' AND 
                                       jsonb_array_length(v_sp_status->'serviceProviderStatuses') > 0 THEN
                                        DECLARE
                                            v_sp_status_detail JSONB;
                                        BEGIN
                                            v_sp_status_detail := v_sp_status->'serviceProviderStatuses'->0;
                                            v_service_provider_health_care_claim_status_category_code := v_sp_status_detail->>'healthCareClaimStatusCategoryCode';
                                            v_service_provider_health_care_claim_status_category_code_value := v_sp_status_detail->>'healthCareClaimStatusCategoryCodeValue';
                                            v_service_provider_status_code := v_sp_status_detail->>'statusCode';
                                            v_service_provider_status_code_value := v_sp_status_detail->>'statusCodeValue';
                                            v_service_provider_entity_identifier_code := v_sp_status_detail->>'entityIdentifierCode';
                                            v_service_provider_entity_identifier_code_value := v_sp_status_detail->>'entityIdentifierCodeValue';
                                            RAISE LOG 'parse_277_response: Service provider status: category_code=% code=%', v_service_provider_health_care_claim_status_category_code, v_service_provider_status_code;
                                        END;
                                    END IF;
                                END;
                            END IF;
                            
                            FOR v_m IN 0..jsonb_array_length(v_claim_detail->'patientClaimStatusDetails') - 1 LOOP
                                v_patient_detail := v_claim_detail->'patientClaimStatusDetails'->v_m;
                                
                                -- Extract subscriber information
                                IF v_patient_detail ? 'subscriber' AND jsonb_typeof(v_patient_detail->'subscriber') = 'object' THEN
                                    v_subscriber_last_name := v_patient_detail->'subscriber'->>'lastName';
                                    v_subscriber_first_name := v_patient_detail->'subscriber'->>'firstName';
                                    v_subscriber_middle_name := v_patient_detail->'subscriber'->>'middleName';
                                    v_subscriber_suffix := v_patient_detail->'subscriber'->>'suffix';
                                    v_subscriber_member_id := v_patient_detail->'subscriber'->>'memberId';
                                    RAISE LOG 'parse_277_response: Subscriber: % % (member_id=%)', v_subscriber_first_name, v_subscriber_last_name, v_subscriber_member_id;
                                END IF;
                                
                                -- Extract dependent information
                                IF v_patient_detail ? 'dependent' THEN
                                    v_dependent_last_name := v_patient_detail->'dependent'->>'lastName';
                                    v_dependent_first_name := v_patient_detail->'dependent'->>'firstName';
                                    v_dependent_middle_name := v_patient_detail->'dependent'->>'middleName';
                                    v_dependent_suffix := v_patient_detail->'dependent'->>'suffix';
                                    RAISE LOG 'parse_277_response: Dependent: % %', v_dependent_first_name, v_dependent_last_name;
                                END IF;
                                
                                FOR v_n IN 0..jsonb_array_length(v_patient_detail->'claims') - 1 LOOP
                                    v_claim := v_patient_detail->'claims'->v_n;
                                    v_claim_status := v_claim->'claimStatus';
                                    
                                    -- Extract claim identifiers for finding original claim
                                    v_trading_partner_claim_number := v_claim_status->>'tradingPartnerClaimNumber';
                                    v_patient_account_number := v_claim_status->>'patientAccountNumber';
                                    BEGIN
                                        v_service_date := (v_claim_status->>'claimServiceDate')::DATE;
                                    EXCEPTION WHEN OTHERS THEN
                                        v_service_date := NULL;
                                        RAISE WARNING 'Failed to convert claimServiceDate to date: %, using NULL instead', 
                                                     v_claim_status->>'claimServiceDate';
                                    END;
                                    v_bill_type_identifier := v_claim_status->>'billTypeIdentifier';
                                    BEGIN
                                        v_service_begin_date := (v_claim_status->>'claimServiceBeginDate')::DATE;
                                    EXCEPTION WHEN OTHERS THEN
                                        v_service_begin_date := NULL;
                                    END;
                                    BEGIN
                                        v_service_end_date := (v_claim_status->>'claimServiceEndDate')::DATE;
                                    EXCEPTION WHEN OTHERS THEN
                                        v_service_end_date := NULL;
                                    END;

                                    -- If no matching claim found, log error and continue to next claim
                                    IF v_claim_no IS NULL THEN
                                        -- Log to billing error log
                                        INSERT INTO billing_error_log (
                                            error_message,
                                            error_context,
                                            error_type,
                                            schema_name,
                                            table_name,
                                            additional_details
                                        ) VALUES (
                                            'No matching claim found for trading_partner_claim_number: ' || COALESCE(v_trading_partner_claim_number, 'NULL'),
                                            'Claim lookup failed in parse_277_response',
                                            'DATA_NOT_FOUND',
                                            current_schema(),
                                            'form_med_claim_info',
                                            jsonb_build_object(
                                                'trading_partner_claim_number', v_trading_partner_claim_number,
                                                'patient_account_number', v_patient_account_number,
                                                'service_date', v_service_date,
                                                'response_id', p_response_id
                                            )
                                        );
                                        
                                        RAISE WARNING 'No matching claim found for trading_partner_claim_number: %, skipping claim', 
                                                    v_trading_partner_claim_number;
                                        RAISE LOG 'parse_277_response: No matching claim found for trading_partner_claim_number=%', v_trading_partner_claim_number;
                                        
                                        -- Continue to next claim
                                        CONTINUE;
                                    END IF;
                                    
                                    -- Extract status information from last information status (most recent)
                                IF v_claim_status ? 'informationClaimStatuses' AND 
                                jsonb_array_length(v_claim_status->'informationClaimStatuses') > 0 THEN
                                    
                                    DECLARE
                                        v_info_status_item JSONB;
                                        v_info_status_date DATE;
                                        v_most_recent_date DATE := NULL;
                                        v_most_recent_info_status JSONB := NULL;
                                        v_info_idx INTEGER;
                                    BEGIN
                                        -- Find the information status with the most recent statusInformationEffectiveDate
                                        FOR v_info_idx IN 0..jsonb_array_length(v_claim_status->'informationClaimStatuses') - 1 LOOP
                                            v_info_status_item := v_claim_status->'informationClaimStatuses'->v_info_idx;
                                            
                                            IF v_info_status_item ? 'statusInformationEffectiveDate' THEN
                                                BEGIN
                                                    v_info_status_date := (v_info_status_item->>'statusInformationEffectiveDate')::DATE;
                                                    
                                                    IF v_most_recent_date IS NULL OR v_info_status_date > v_most_recent_date THEN
                                                        v_most_recent_date := v_info_status_date;
                                                        v_most_recent_info_status := v_info_status_item;
                                                    END IF;
                                                EXCEPTION WHEN OTHERS THEN
                                                    -- If date conversion fails, skip this status
                                                    NULL;
                                                END;
                                            END IF;
                                        END LOOP;
                                        
                                        -- If no status with valid date found, use the last one
                                        IF v_most_recent_info_status IS NULL THEN
                                            v_most_recent_info_status := v_claim_status->'informationClaimStatuses'->(jsonb_array_length(v_claim_status->'informationClaimStatuses') - 1);
                                        END IF;
                                        
                                        -- Now extract from the most recent info status
                                        v_info_status := v_most_recent_info_status;
                                        BEGIN
                                            v_total_claim_charge_amount := (v_info_status->>'totalClaimChargeAmount')::NUMERIC;
                                        EXCEPTION WHEN OTHERS THEN
                                            v_total_claim_charge_amount := NULL;
                                            RAISE WARNING 'Failed to convert totalClaimChargeAmount to numeric: %, using NULL instead', 
                                                         v_info_status->>'totalClaimChargeAmount';
                                        END;
                                        BEGIN
                                            v_claim_payment_amount := (v_info_status->>'claimPaymentAmount')::NUMERIC;
                                        EXCEPTION WHEN OTHERS THEN
                                            v_claim_payment_amount := NULL;
                                            RAISE WARNING 'Failed to convert claimPaymentAmount to numeric: %, using NULL instead', 
                                                         v_info_status->>'claimPaymentAmount';
                                        END;
                                        BEGIN
                                            v_status_effective_date := (v_info_status->>'statusInformationEffectiveDate')::DATE;
                                        EXCEPTION WHEN OTHERS THEN
                                            v_status_effective_date := NULL;
                                        END;
                                        BEGIN
                                            v_claim_adjudicated_finalized_date := (v_info_status->>'adjudicatedFinalizedDate')::DATE;
                                        EXCEPTION WHEN OTHERS THEN
                                            v_claim_adjudicated_finalized_date := NULL;
                                        END;
                                        BEGIN
                                            v_claim_remittance_date := (v_info_status->>'remittanceDate')::DATE;
                                        EXCEPTION WHEN OTHERS THEN
                                            v_claim_remittance_date := NULL;
                                        END;
                                        v_claim_remittance_trace_number := v_info_status->>'remittanceTraceNumber';
                                        BEGIN
                                            v_adjudicated_finalized_date := (v_info_status->>'adjudicatedFinalizedDate')::DATE;
                                        EXCEPTION WHEN OTHERS THEN
                                            v_adjudicated_finalized_date := NULL;
                                        END;
                                        BEGIN
                                            v_remittance_date := (v_info_status->>'remittanceDate')::DATE;
                                        EXCEPTION WHEN OTHERS THEN
                                            v_remittance_date := NULL;
                                        END;
                                        
                                        -- Extract information status details (get most recent by date or last in array)
                                        IF v_info_status ? 'informationStatuses' AND 
                                           jsonb_array_length(v_info_status->'informationStatuses') > 0 THEN
                                            
                                            DECLARE
                                                v_status_item JSONB;
                                                v_status_date DATE;
                                                v_most_recent_date DATE := NULL;
                                                v_most_recent_status JSONB := NULL;
                                                v_status_idx INTEGER;
                                            BEGIN
                                                -- Look for the status with the most recent effectiveDate
                                                FOR v_status_idx IN 0..jsonb_array_length(v_info_status->'informationStatuses') - 1 LOOP
                                                    v_status_item := v_info_status->'informationStatuses'->v_status_idx;
                                                    
                                                    -- Check if this status has a date
                                                    IF v_status_item ? 'effectiveDate' THEN
                                                        BEGIN
                                                            v_status_date := (v_status_item->>'effectiveDate')::DATE;
                                                            
                                                            -- If this is the first date found or it's more recent, use it
                                                            IF v_most_recent_date IS NULL OR v_status_date > v_most_recent_date THEN
                                                                v_most_recent_date := v_status_date;
                                                                v_most_recent_status := v_status_item;
                                                            END IF;
                                                        EXCEPTION WHEN OTHERS THEN
                                                            -- Skip this status if date conversion fails
                                                            NULL;
                                                        END;
                                                    END IF;
                                                END LOOP;
                                                
                                                -- If no status with date was found, use the last item in the array
                                                IF v_most_recent_status IS NULL THEN
                                                    v_most_recent_status := v_info_status->'informationStatuses'->(jsonb_array_length(v_info_status->'informationStatuses') - 1);
                                                END IF;
                                                
                                                -- Extract the status details from the selected status
                                                v_status_code := v_most_recent_status->>'statusCode';
                                                v_status_code_value := v_most_recent_status->>'statusCodeValue';
                                                v_status_category_code := v_most_recent_status->>'healthCareClaimStatusCategoryCode';
                                                v_status_category_code_value := v_most_recent_status->>'healthCareClaimStatusCategoryCodeValue';
                                                v_entity_identifier_code := v_most_recent_status->>'entityIdentifierCode';
                                                v_entity_identifier_code_value := v_most_recent_status->>'entityIdentifierCodeValue';
                                                RAISE LOG 'parse_277_response: Most recent status: code=% value=% category_code=%', v_status_code, v_status_code_value, v_status_category_code;
                                            END;
                                        END IF;
                                    END;
                                END IF;
                                    
                                    -- Insert main 277 response record
                                    INSERT INTO form_med_claim_resp_277 (
                                        response_id,
                                        claim_no,
                                        patient_id,
                                        site_id,
                                        payer_id,
                                        control_number,
                                        claim_transaction_batch_number,
                                        trading_partner_claim_number,
                                        bill_type_identifier,
                                        reference_identification,
                                        service_provider_organization_name,
                                        service_provider_npi,
                                        service_begin_date,
                                        service_end_date,
                                        service_date,
                                        transaction_set_creation_date,
                                        transaction_set_creation_time,
                                        subscriber_last_name,
                                        subscriber_first_name,
                                        subscriber_middle_name,
                                        subscriber_suffix,
                                        patient_account_number,
                                        subscriber_member_id,
                                        dependent_last_name,
                                        dependent_first_name,
                                        dependent_middle_name,
                                        dependent_suffix,
                                        payer_organization_name,
                                        payer_identification,
                                        payer_centers_for_medicare_and_medicaid_service_plan_id,
                                        service_provider_status_information_effective_date,
                                        service_provider_health_care_claim_status_category_code,
                                        service_provider_health_care_claim_status_category_code_value,
                                        service_provider_status_code,
                                        service_provider_status_code_value,
                                        service_provider_entity_identifier_code,
                                        service_provider_entity_identifier_code_value,
                                        claim_total_claim_charge_amount,
                                        claim_payment_amount,
                                        claim_adjudicated_finalized_date,
                                        claim_remittance_date,
                                        claim_remittance_trace_number,
                                        clearinghouse_trace_number,
                                        pharmacy_prescription_number,
                                        voucher_identifier,
                                        claim_status_information_effective_date,
                                        adjudicated_finalized_date,
                                        remittance_date,
                                        status_code,
                                        status_code_value,
                                        health_care_claim_status_category_code,
                                        health_care_claim_status_category_code_value,
                                        entity_identifier_code,
                                        entity_identifier_code_value,
                                        response_raw_json,
                                        raw_report_url,
                                        s3_filehash,
                                        created_by,
                                        created_on,
                                        archived,
                                        deleted
                                    ) VALUES (
                                        p_response_id,
                                        v_claim_no,
                                        v_patient_id,
                                        v_site_id,
                                        v_payer_id,
                                        v_control_number,
                                        v_claim_transaction_batch_number,
                                        v_trading_partner_claim_number,
                                        v_bill_type_identifier,
                                        v_reference_identification,
                                        v_service_provider_organization_name,
                                        v_service_provider_npi,
                                        v_service_begin_date,
                                        v_service_end_date,
                                        v_service_date,
                                        v_transaction_set_creation_date,
                                        v_transaction_set_creation_time,
                                        v_subscriber_last_name,
                                        v_subscriber_first_name,
                                        v_subscriber_middle_name,
                                        v_subscriber_suffix,
                                        v_patient_account_number,
                                        v_subscriber_member_id,
                                        v_dependent_last_name,
                                        v_dependent_first_name,
                                        v_dependent_middle_name,
                                        v_dependent_suffix,
                                        v_payer_organization_name,
                                        v_payer_identification,
                                        v_payer_centers_for_medicare_and_medicaid_service_plan_id,
                                        v_service_provider_status_information_effective_date,
                                        v_service_provider_health_care_claim_status_category_code,
                                        v_service_provider_health_care_claim_status_category_code_value,
                                        v_service_provider_status_code,
                                        v_service_provider_status_code_value,
                                        v_service_provider_entity_identifier_code,
                                        v_service_provider_entity_identifier_code_value,
                                        v_total_claim_charge_amount,
                                        v_claim_payment_amount,
                                        v_claim_adjudicated_finalized_date,
                                        v_claim_remittance_date,
                                        v_claim_remittance_trace_number,
                                        v_clearinghouse_trace_number,
                                        v_pharmacy_prescription_number,
                                        v_voucher_identifier,
                                        v_status_effective_date,
                                        v_adjudicated_finalized_date,
                                        v_remittance_date,
                                        v_status_code,
                                        v_status_code_value,
                                        v_status_category_code,
                                        v_status_category_code_value,
                                        v_entity_identifier_code,
                                        v_entity_identifier_code_value,
                                        p_response_json::TEXT,
                                        v_raw_report_url,
                                        v_s3_filehash,
                                        p_created_by,
                                        p_created_dt,
                                        FALSE,
                                        FALSE
                                    ) RETURNING id INTO v_main_record_id;
                                    RAISE LOG 'parse_277_response: Inserted form_med_claim_resp_277 id=% for claim_no=%', v_main_record_id, v_claim_no;
                                    
                                    v_claims_processed := v_claims_processed + 1;
                                    
                                    -- Update the medical claim status based on the 277 response status
                                    DECLARE
                                        v_mapped_status TEXT;
                                        v_final_status TEXT;
                                        v_claim_frequency_code TEXT;
                                    BEGIN
                                        -- Get the claim frequency code for reversal check
                                        SELECT mci.claim_frequency_code 
                                        INTO v_claim_frequency_code
                                        FROM form_med_claim mc
                                        INNER JOIN form_med_claim_info mci ON mci.id = mc.id
                                        WHERE mc.claim_no = v_claim_no
                                        AND mc.deleted IS NOT TRUE
                                        AND mc.archived IS NOT TRUE
                                        AND mci.deleted IS NOT TRUE
                                        AND mci.archived IS NOT TRUE
                                        LIMIT 1;
                                        
                                        -- Map the status using service provider status if available, otherwise use regular status
                                        IF v_service_provider_health_care_claim_status_category_code IS NOT NULL THEN
                                            v_mapped_status := map_277_response_status(v_service_provider_health_care_claim_status_category_code);
                                        ELSIF v_status_category_code IS NOT NULL THEN
                                            v_mapped_status := map_277_response_status(v_status_category_code);
                                        ELSE
                                            v_mapped_status := 'Error'; -- Default if no status found
                                        END IF;
                                        
                                        -- Check for reversal scenario
                                        v_final_status := check_claim_reversal(v_claim_frequency_code, v_mapped_status);
                                        
                                        IF v_final_status IS NULL OR v_mapped_status = 'Error' THEN
                                            INSERT INTO billing_error_log (
                                                error_message,
                                                error_context,
                                                error_type,
                                                schema_name,
                                                table_name,
                                                additional_details
                                            ) VALUES (
                                                'No final status found for claim ' || v_claim_no || ' with status code ' || v_status_code,
                                                'Exception in parse_277_response',
                                                'FUNCTION',
                                                current_schema(),
                                                'form_med_claim_resp_277',
                                                jsonb_build_object(
                                                    'claim_no', v_claim_no,
                                                    'status_code', v_status_code,
                                                    'response_id', p_response_id
                                                )
                                            );
                                            RAISE WARNING 'No final status found for claim % with status code %', v_claim_no, v_status_code;
                                        END IF;

                                        -- Update the medical claim status
                                        UPDATE form_med_claim
                                        SET status = v_final_status,
                                            paid = COALESCE(v_claim_payment_amount::NUMERIC, 0::NUMERIC),
                                            copay = 0.00::NUMERIC,
                                            updated_on = CURRENT_TIMESTAMP
                                        WHERE claim_no = v_claim_no 
                                        AND COALESCE(void, 'No') = 'No'
                                        AND archived IS NOT TRUE
                                        AND deleted IS NOT TRUE;
                                        
                                        UPDATE form_med_claim_supplemental
                                        SET claim_control_number = v_control_number
                                        WHERE claim_number = (
                                            SELECT id::TEXT FROM form_med_claim 
                                            WHERE claim_no = v_claim_no 
                                            AND deleted IS NOT TRUE 
                                            AND archived IS NOT TRUE 
                                            LIMIT 1
                                        )
                                        AND deleted IS NOT TRUE
                                        AND archived IS NOT TRUE;
                                        
                                        UPDATE form_billing_invoice inv
                                        SET total_insurance_paid = COALESCE(v_claim_payment_amount::NUMERIC, 0::NUMERIC),
                                            total_pt_pay = 0.00::NUMERIC
                                        FROM form_med_claim claim
                                        WHERE claim.claim_no = v_claim_no 
                                        AND claim.invoice_no = inv.invoice_no
                                        AND COALESCE(inv.void, 'No') = 'No'
                                        AND COALESCE(inv.zeroed, 'No') = 'No'
                                        AND inv.archived IS NOT TRUE
                                        AND inv.deleted IS NOT TRUE
                                        AND claim.deleted IS NOT TRUE
                                        AND claim.archived IS NOT TRUE;
                                        
                                        RAISE NOTICE 'Updated claim % status to: % (category code: %)', 
                                                    v_claim_no, v_final_status, 
                                                    COALESCE(v_service_provider_health_care_claim_status_category_code, v_status_category_code);
                                        RAISE LOG 'parse_277_response: Updated claim_no=% to status=%', v_claim_no, v_final_status;
                                    END;
                                    
                                    -- Process payer contact information subform (object, not array)
                                    IF v_payer ? 'payerContactInformation' THEN
                                        DECLARE
                                            v_payer_contact_info JSONB;
                                            v_contact_methods JSONB;
                                        BEGIN
                                            v_payer_contact_info := v_payer->'payerContactInformation';
                                            
                                            -- Extract contact name
                                            IF v_payer_contact_info ? 'contactName' THEN
                                                -- Process contactMethods array within the payerContactInformation object
                                                IF v_payer_contact_info ? 'contactMethods' AND 
                                                   jsonb_array_length(v_payer_contact_info->'contactMethods') > 0 THEN
                                                    FOR v_o IN 0..jsonb_array_length(v_payer_contact_info->'contactMethods') - 1 LOOP
                                                        v_contact_method := v_payer_contact_info->'contactMethods'->v_o;
                                                        
                                                        INSERT INTO form_med_claim_resp_py_cont (
                                                            contact_name,
                                                            electronic_data_inter_change_access_number,
                                                            email,
                                                            fax,
                                                            phone,
                                                            phone_extension,
                                                            created_by,
                                                            created_on,
                                                            archived,
                                                            deleted
                                                        ) VALUES (
                                                            v_payer_contact_info->>'contactName',
                                                            v_contact_method->>'electronicDataInterChangeAccessNumber',
                                                            v_contact_method->>'email',
                                                            v_contact_method->>'fax',
                                                            v_contact_method->>'phone',
                                                            v_contact_method->>'phoneExtension',
                                                            p_created_by,
                                                            p_created_dt,
                                                            FALSE,
                                                            FALSE
                                                        ) RETURNING id INTO v_contact_id;
                                                        RAISE LOG 'parse_277_response: Inserted payer contact id=% for main_record_id=%', v_contact_id, v_main_record_id;
                                                        
                                                        -- Link to main record
                                                        INSERT INTO sf_form_med_claim_resp_277_to_med_claim_resp_py_cont (
                                                            form_med_claim_resp_277_fk,
                                                            form_med_claim_resp_py_cont_fk,
                                                            archive,
                                                            delete
                                                        ) VALUES (
                                                            v_main_record_id,
                                                            v_contact_id,
                                                            FALSE,
                                                            FALSE
                                                        );
                                                    END LOOP;
                                                END IF;
                                            END IF;
                                        END;
                                    END IF;
                                    
                                    -- Process claim status history subform (multi: true)
                                    IF v_info_status ? 'informationStatuses' AND 
                                       jsonb_array_length(v_info_status->'informationStatuses') > 0 THEN
                                        FOR v_o IN 0..jsonb_array_length(v_info_status->'informationStatuses') - 1 LOOP
                                            v_service_status := v_info_status->'informationStatuses'->v_o;
                                            
                                            INSERT INTO form_med_claim_resp_277_st_hst (
                                                health_care_claim_status_category_code,
                                                health_care_claim_status_category_code_value,
                                                status_information_effective_date,
                                                status_code,
                                                status_code_value,
                                                entity_identifier_code,
                                                entity_identifier_code_value,
                                                ncpdp_reject_payment_codes,
                                                created_by,
                                                created_on,
                                                archived,
                                                deleted
                                            ) VALUES (
                                                v_service_status->>'healthCareClaimStatusCategoryCode',
                                                v_service_status->>'healthCareClaimStatusCategoryCodeValue',
                                                safe_to_date(v_service_status->>'statusInformationEffectiveDate'),
                                                v_service_status->>'statusCode',
                                                v_service_status->>'statusCodeValue',
                                                v_service_status->>'entityIdentifierCode',
                                                v_service_status->>'entityIdentifierCodeValue',
                                                v_service_status->>'nationalCouncilForPrescriptionDrugProgramsRejectPaymentCodes',
                                                p_created_by,
                                                p_created_dt,
                                                FALSE,
                                                FALSE
                                            ) RETURNING id INTO v_status_history_id;
                                            RAISE LOG 'parse_277_response: Inserted claim status history id=% for main_record_id=%', v_status_history_id, v_main_record_id;
                                            
                                            -- Link to main record
                                            INSERT INTO sf_form_med_claim_resp_277_to_med_claim_resp_277_st_hst (
                                                form_med_claim_resp_277_fk,
                                                form_med_claim_resp_277_st_hst_fk,
                                                archive,
                                                delete
                                            ) VALUES (
                                                v_main_record_id,
                                                v_status_history_id,
                                                FALSE,
                                                FALSE
                                            );
                                        END LOOP;
                                    END IF;
                                    
                                    -- Process service lines subform (multi: true)
                                    IF v_claim ? 'serviceLines' AND 
                                       jsonb_array_length(v_claim->'serviceLines') > 0 THEN
                                        FOR v_o IN 0..jsonb_array_length(v_claim->'serviceLines') - 1 LOOP
                                            v_service_line := v_claim->'serviceLines'->v_o;
                                            
                                            -- Extract service line information
                                            v_line_item_control_number := v_service_line->>'lineItemControlNumber';
                                            BEGIN
                                                v_service_line_date := (v_service_line->>'serviceLineDate')::DATE;
                                            EXCEPTION WHEN OTHERS THEN
                                                v_service_line_date := NULL;
                                            END;
                                            
                                            -- Extract additional date fields
                                            DECLARE
                                                v_begin_service_line_date DATE;
                                                v_end_service_line_date DATE;
                                                v_status_effective_date DATE;
                                                v_inventory_id INTEGER;
                                                v_service_id_qualifier_code TEXT;
                                                v_service_id_qualifier_code_value TEXT;
                                                v_modifier_1 TEXT;
                                                v_modifier_2 TEXT;
                                                v_modifier_3 TEXT;
                                                v_modifier_4 TEXT;
                                                v_sl_revenue_code TEXT;
                                                v_sl_health_care_claim_status_category_code TEXT;
                                                v_sl_health_care_claim_status_category_code_value TEXT;
                                                v_sl_status_code TEXT;
                                                v_sl_status_code_value TEXT;
                                                v_sl_entity_identifier_code TEXT;
                                                v_sl_entity_identifier_code_value TEXT;
                                                v_sl_ncpdp_reject_codes TEXT;
                                            BEGIN
                                                -- Parse additional dates
                                                BEGIN
                                                    v_begin_service_line_date := (v_service_line->>'beginServiceLineDate')::DATE;
                                                EXCEPTION WHEN OTHERS THEN
                                                    v_begin_service_line_date := NULL;
                                                END;
                                                BEGIN
                                                    v_end_service_line_date := (v_service_line->>'endServiceLineDate')::DATE;
                                                EXCEPTION WHEN OTHERS THEN
                                                    v_end_service_line_date := NULL;
                                                END;
                                                
                                                -- Extract service information
                                                v_service_id_qualifier_code := v_service_line->'service'->>'serviceIdQualifierCode';
                                                v_service_id_qualifier_code_value := v_service_line->'service'->>'serviceIdQualifierCodeValue';
                                                v_procedure_code := v_service_line->'service'->>'procedureCode';
                                                v_sl_revenue_code := v_service_line->'service'->>'revenueCode';
                                                
                                                -- Extract procedure modifiers from array
                                                IF v_service_line->'service' ? 'procedureModifiers' AND 
                                                   jsonb_array_length(v_service_line->'service'->'procedureModifiers') > 0 THEN
                                                    v_modifier_1 := v_service_line->'service'->'procedureModifiers'->>0;
                                                    IF jsonb_array_length(v_service_line->'service'->'procedureModifiers') > 1 THEN
                                                        v_modifier_2 := v_service_line->'service'->'procedureModifiers'->>1;
                                                    END IF;
                                                    IF jsonb_array_length(v_service_line->'service'->'procedureModifiers') > 2 THEN
                                                        v_modifier_3 := v_service_line->'service'->'procedureModifiers'->>2;
                                                    END IF;
                                                    IF jsonb_array_length(v_service_line->'service'->'procedureModifiers') > 3 THEN
                                                        v_modifier_4 := v_service_line->'service'->'procedureModifiers'->>3;
                                                    END IF;
                                                END IF;
                                                
                                                -- Extract amounts
                                                BEGIN
                                                    v_charge_amount := (v_service_line->'service'->>'chargeAmount')::NUMERIC;
                                                EXCEPTION WHEN OTHERS THEN
                                                    v_charge_amount := NULL;
                                                END;
                                                BEGIN
                                                    v_amount_paid := (v_service_line->'service'->>'amountPaid')::NUMERIC;
                                                EXCEPTION WHEN OTHERS THEN
                                                    v_amount_paid := NULL;
                                                END;
                                                BEGIN
                                                    v_submitted_units := (v_service_line->'service'->>'submittedUnits')::NUMERIC;
                                                EXCEPTION WHEN OTHERS THEN
                                                    v_submitted_units := NULL;
                                                END;
                                                
                                                -- Extract status information from service claim statuses (first one for effective date)
                                                IF v_service_line ? 'serviceClaimStatuses' AND 
                                                   jsonb_array_length(v_service_line->'serviceClaimStatuses') > 0 THEN
                                                    DECLARE
                                                        v_first_status JSONB;
                                                    BEGIN
                                                        v_first_status := v_service_line->'serviceClaimStatuses'->0;
                                                        BEGIN
                                                            v_status_effective_date := (v_first_status->>'effectiveDate')::DATE;
                                                        EXCEPTION WHEN OTHERS THEN
                                                            v_status_effective_date := NULL;
                                                        END;
                                                        
                                                        -- Get first service status for line-level status fields
                                                        IF v_first_status ? 'serviceStatuses' AND 
                                                           jsonb_array_length(v_first_status->'serviceStatuses') > 0 THEN
                                                            DECLARE
                                                                v_first_service_status JSONB;
                                                            BEGIN
                                                                v_first_service_status := v_first_status->'serviceStatuses'->0;
                                                                v_sl_health_care_claim_status_category_code := v_first_service_status->>'healthCareClaimStatusCategoryCode';
                                                                v_sl_health_care_claim_status_category_code_value := v_first_service_status->>'healthCareClaimStatusCategoryCodeValue';
                                                                v_sl_status_code := v_first_service_status->>'statusCode';
                                                                v_sl_status_code_value := v_first_service_status->>'statusCodeValue';
                                                                v_sl_entity_identifier_code := v_first_service_status->>'entityIdentifierCode';
                                                                v_sl_entity_identifier_code_value := v_first_service_status->>'entityIdentifierCodeValue';
                                                                v_sl_ncpdp_reject_codes := v_first_service_status->>'nationalCouncilForPrescriptionDrugProgramsRejectPaymentCodes';
                                                                RAISE LOG 'parse_277_response: Service line status: code=% value=%', v_sl_status_code, v_sl_status_code_value;
                                                            END;
                                                        END IF;
                                                    END;
                                                END IF;
                                                
                                                -- Look up inventory_id from form_med_claim_sl using claim_no and line_item_control_number
                                                SELECT inventory_id 
                                                INTO v_inventory_id
                                                FROM form_med_claim_sl 
                                                WHERE claim_no = v_claim_no 
                                                    AND provider_control_number = v_line_item_control_number
                                                    AND deleted IS NOT TRUE
                                                    AND archived IS NOT TRUE
                                                LIMIT 1;
                                                
                                                INSERT INTO form_med_claim_resp_277_sl (
                                                    line_item_control_number,
                                                    service_line_date,
                                                    begin_service_line_date,
                                                    end_service_line_date,
                                                    inventory_id,
                                                    service_id_qualifier_code,
                                                    service_id_qualifier_code_value,
                                                    procedure_code,
                                                    modifier_1,
                                                    modifier_2,
                                                    modifier_3,
                                                    modifier_4,
                                                    charge_amount,
                                                    amount_paid,
                                                    revenue_code,
                                                    submitted_units,
                                                    status_effective_date,
                                                    health_care_claim_status_category_code,
                                                    health_care_claim_status_category_code_value,
                                                    status_code,
                                                    status_code_value,
                                                    entity_identifier_code,
                                                    entity_identifier_code_value,
                                                    ncpdp_reject_payment_codes,
                                                    created_by,
                                                    created_on,
                                                    archived,
                                                    deleted
                                                ) VALUES (
                                                    v_line_item_control_number,
                                                    v_service_line_date,
                                                    v_begin_service_line_date,
                                                    v_end_service_line_date,
                                                    v_inventory_id,
                                                    v_service_id_qualifier_code,
                                                    v_service_id_qualifier_code_value,
                                                    v_procedure_code,
                                                    v_modifier_1,
                                                    v_modifier_2,
                                                    v_modifier_3,
                                                    v_modifier_4,
                                                    v_charge_amount,
                                                    v_amount_paid,
                                                    v_sl_revenue_code,
                                                    v_submitted_units,
                                                    v_status_effective_date,
                                                    v_sl_health_care_claim_status_category_code,
                                                    v_sl_health_care_claim_status_category_code_value,
                                                    v_sl_status_code,
                                                    v_sl_status_code_value,
                                                    v_sl_entity_identifier_code,
                                                    v_sl_entity_identifier_code_value,
                                                    v_sl_ncpdp_reject_codes,
                                                    p_created_by,
                                                    p_created_dt,
                                                    FALSE,
                                                    FALSE
                                                ) RETURNING id INTO v_service_line_id;
                                                RAISE LOG 'parse_277_response: Inserted service line id=% for main_record_id=%', v_service_line_id, v_main_record_id;
                                            END;
                                            
                                            -- Link to main record
                                            INSERT INTO sf_form_med_claim_resp_277_to_med_claim_resp_277_sl (
                                                form_med_claim_resp_277_fk,
                                                form_med_claim_resp_277_sl_fk,
                                                archive,
                                                delete
                                            ) VALUES (
                                                v_main_record_id,
                                                v_service_line_id,
                                                FALSE,
                                                FALSE
                                            );
                                            
                                            -- Process service line status history (nested subform)
                                            IF v_service_line ? 'serviceClaimStatuses' AND 
                                               jsonb_array_length(v_service_line->'serviceClaimStatuses') > 0 THEN
                                                FOR v_p IN 0..jsonb_array_length(v_service_line->'serviceClaimStatuses') - 1 LOOP
                                                    IF v_service_line->'serviceClaimStatuses'->v_p ? 'serviceStatuses' AND
                                                       jsonb_array_length(v_service_line->'serviceClaimStatuses'->v_p->'serviceStatuses') > 0 THEN
                                                        FOR v_q IN 0..jsonb_array_length(v_service_line->'serviceClaimStatuses'->v_p->'serviceStatuses') - 1 LOOP
                                                            v_service_status := v_service_line->'serviceClaimStatuses'->v_p->'serviceStatuses'->v_q;
                                                            
                                                            INSERT INTO form_med_claim_resp_277_sl_hst (
                                                                health_care_claim_status_category_code,
                                                                health_care_claim_status_category_code_value,
                                                                status_code,
                                                                status_code_value,
                                                                entity_identifier_code,
                                                                entity_identifier_code_value,
                                                                ncpdp_reject_payment_codes,
                                                                created_by,
                                                                created_on,
                                                                archived,
                                                                deleted
                                                            ) VALUES (
                                                                v_service_status->>'healthCareClaimStatusCategoryCode',
                                                                v_service_status->>'healthCareClaimStatusCategoryCodeValue',
                                                                v_service_status->>'statusCode',
                                                                v_service_status->>'statusCodeValue',
                                                                v_service_status->>'entityIdentifierCode',
                                                                v_service_status->>'entityIdentifierCodeValue',
                                                                v_service_status->>'nationalCouncilForPrescriptionDrugProgramsRejectPaymentCodes',
                                                                p_created_by,
                                                                p_created_dt,
                                                                FALSE,
                                                                FALSE
                                                            ) RETURNING id INTO v_status_history_id;
                                                            RAISE LOG 'parse_277_response: Inserted service line status history id=% for service_line_id=%', v_status_history_id, v_service_line_id;
                                                            
                                                            -- Link to service line record
                                                            INSERT INTO sf_form_med_claim_resp_277_sl_to_med_claim_resp_277_sl_hst (
                                                                form_med_claim_resp_277_sl_fk,
                                                                form_med_claim_resp_277_sl_hst_fk,
                                                                archive,
                                                                delete
                                                            ) VALUES (
                                                                v_service_line_id,
                                                                v_status_history_id,
                                                                FALSE,
                                                                FALSE
                                                            );
                                                        END LOOP;
                                                    END IF;
                                                END LOOP;
                                            END IF;
                                        END LOOP;
                                    END IF;
                                    
                                    RAISE NOTICE 'Processed 277 response for claim_no: %, trading_partner_claim_number: %', 
                                                v_claim_no, v_trading_partner_claim_number;
                                    RAISE LOG 'parse_277_response: Finished processing claim_no=% trading_partner_claim_number=%', v_claim_no, v_trading_partner_claim_number;
                                END LOOP; -- claims
                            END LOOP; -- patientClaimStatusDetails
                        END LOOP; -- claimStatusDetails
                    END LOOP; -- claimStatusTransactions
                END LOOP; -- payers
            END LOOP; -- detailInfo
        END LOOP; -- transactions
        
        -- Process response meta subform (multi: false)
        IF p_response_json ? 'meta' THEN
            v_meta_submitter_id := p_response_json->'meta'->>'submitterId';
            v_meta_sender_id := p_response_json->'meta'->>'senderId';
            v_meta_biller_id := p_response_json->'meta'->>'billerId';
            v_trace_id := p_response_json->'meta'->>'traceId';
            v_application_mode := p_response_json->'meta'->>'applicationMode';
            
            INSERT INTO form_med_claim_resp_ch_meta (
                submitter_id,
                sender_id,
                biller_id,
                trace_id,
                application_mode,
                created_by,
                created_on,
                archived,
                deleted
            ) VALUES (
                v_meta_submitter_id,
                v_meta_sender_id,
                v_meta_biller_id,
                v_trace_id,
                v_application_mode,
                p_created_by,
                p_created_dt,
                FALSE,
                FALSE
            ) RETURNING id INTO v_subform_id;
            RAISE LOG 'parse_277_response: Inserted response meta id=% for main_record_id=%', v_subform_id, v_main_record_id;
            
            -- Link to main record
            INSERT INTO sf_form_med_claim_resp_277_to_med_claim_resp_ch_meta (
                form_med_claim_resp_277_fk,
                form_med_claim_resp_ch_meta_fk,
                archive,
                delete
            ) VALUES (
                v_main_record_id,
                v_subform_id,
                FALSE,
                FALSE
            );
        END IF;
        
        -- Build result info for successful logging
        v_result_info := jsonb_build_object(
            'claims_processed', v_claims_processed,
            'control_number', v_control_number,
            'last_main_record_id', v_main_record_id
        );
        
        -- Log successful completion
        BEGIN
            PERFORM log_billing_function(
                'parse_277_response'::tracked_function,
                v_params,
                v_result_info,
                NULL::text,
                clock_timestamp() - v_start_time
            );
        EXCEPTION WHEN OTHERS THEN
            -- Ignore logging errors
            NULL;
        END;
        
        RAISE NOTICE 'Completed 277 response processing for response_id: %, claims processed: %', p_response_id, v_claims_processed;
        RAISE LOG 'parse_277_response: Completed processing for response_id=%; claims_processed=%', p_response_id, v_claims_processed;
        
    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        v_error_message := SQLERRM;
        
        -- Log to billing error log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            v_error_message,
            'Exception in parse_277_response',
            'FUNCTION',
            current_schema(),
            'form_med_claim_resp_277',
            v_params
        );
        
        -- Log error to function log
        BEGIN
            PERFORM log_billing_function(
                'parse_277_response'::tracked_function,
                v_params::jsonb,
                NULL::jsonb,
                v_error_message::text,
                clock_timestamp() - v_start_time
            );
        EXCEPTION WHEN OTHERS THEN
            -- Ignore logging errors
            NULL;
        END;
        
        RAISE WARNING 'Error parsing 277 response (response_id: %): %', p_response_id, v_error_message;
        RAISE LOG 'parse_277_response: ERROR for response_id=%: %', p_response_id, v_error_message;
        RAISE;
    END;
END;
$$ LANGUAGE plpgsql;