-- Function to process adding unapplied cash
DO $$ BEGIN
  PERFORM drop_all_function_signatures('process_cash_add');
END $$;
CREATE OR REPLACE FUNCTION process_cash_add() RETURNS TRIGGER AS $$
DECLARE
    v_account_id INTEGER;
    v_creator INTEGER;
    v_success BOOLEAN := FALSE;
BEGIN
    -- Log function entry
    RAISE LOG 'Entering process_cash_add for cash_no: %, id: %', NEW.cash_no, NEW.id;
    
    -- Skip if conditions aren't met
    IF (COALESCE(NEW.zeroed, 'No') = 'Yes' OR 
        NEW.archived IS TRUE OR 
        NEW.deleted IS TRUE OR
        NEW.applied_datetime IS NOT NULL) THEN
        RAISE LOG 'Skipping process_cash_add, conditions not met for cash_no: %', 
                 COALESCE(NEW.cash_no, 'NULL');
        RETURN NEW;
    END IF;
    
    -- Check if we're in a closed period
    IF check_closed_period(NEW.post_datetime::timestamp) THEN
        -- Log error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Cannot post cash in a closed accounting period',
            'Error processing cash',
            'TRIGGER',
            current_schema(),
            'billing_cash',
            jsonb_build_object(
                'function_name', 'process_cash_add',
                'cash_no', COALESCE(NEW.cash_no, 'NULL'),
                'post_datetime', NEW.post_datetime
            )
        );
        
        RAISE EXCEPTION 'Cannot post cash in a closed accounting period for cash %', 
                        COALESCE(NEW.cash_no, 'NULL');
    END IF;
    
    BEGIN
        -- Determine who created/updated this record
        v_creator := COALESCE(NEW.updated_by, NEW.created_by);
        RAISE LOG 'Processing cash for cash_no: %, creator: %', 
                 COALESCE(NEW.cash_no, 'NULL'), v_creator;
        
        -- Get account ID based on payer_id or patient_id
        BEGIN
            IF NEW.payer_id IS NOT NULL THEN
                SELECT id INTO v_account_id 
                FROM form_billing_account
                WHERE type = 'Payer'
                AND payer_id = NEW.payer_id
                AND archived IS NOT TRUE
                AND deleted IS NOT TRUE;
            ELSIF NEW.patient_id IS NOT NULL THEN
                SELECT id INTO v_account_id 
                FROM form_billing_account
                WHERE type = 'Patient'
                AND patient_id = NEW.patient_id
                AND archived IS NOT TRUE
                AND deleted IS NOT TRUE;
            END IF;
            
            IF v_account_id IS NULL THEN
                -- Log error to billing_error_log
                INSERT INTO billing_error_log (
                    error_message,
                    error_context,
                    error_type,
                    schema_name,
                    table_name,
                    additional_details
                ) VALUES (
                    'Account not found for cash',
                    'Error processing cash',
                    'TRIGGER',
                    current_schema(),
                    'billing_cash',
                    jsonb_build_object(
                        'function_name', 'process_cash_add',
                        'cash_no', COALESCE(NEW.cash_no, 'NULL'),
                        'patient_id', NEW.patient_id,
                        'payer_id', NEW.payer_id
                    )
                );
                
                RAISE EXCEPTION 'Account not found for cash %', COALESCE(NEW.cash_no, 'NULL');
            END IF;
            
            RAISE LOG 'Retrieved account_id: % for cash: %', 
                     v_account_id, COALESCE(NEW.cash_no, 'NULL');
        EXCEPTION WHEN OTHERS THEN
            -- Log error to billing_error_log
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                SQLERRM,
                'Error retrieving account ID for cash',
                'TRIGGER',
                current_schema(),
                'billing_cash',
                jsonb_build_object(
                    'function_name', 'process_cash_add',
                    'cash_no', COALESCE(NEW.cash_no, 'NULL'),
                    'patient_id', NEW.patient_id,
                    'payer_id', NEW.payer_id
                )
            );
            
            RAISE;
        END;
        
        -- Create Unapplied Cash credit entry
        RAISE LOG 'Creating Unapplied Cash credit entry for amount: %', NEW.amount;
        
        INSERT INTO form_ledger_finance (
            account_id, 
            source_id, 
            source_form, 
            post_datetime, 
            transaction_datetime,
            account_type, 
            transaction_type,
            debit, 
            credit, 
            created_on, 
            created_by,
            notes
        ) VALUES (
            v_account_id,
            NEW.id,
            'billing_cash',
            COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
            get_site_timestamp(NEW.site_id),
            'Unapplied Cash',
            'Posting',
            0.00,
            NEW.amount,
            CURRENT_TIMESTAMP,
            v_creator,
            'Payment Method: ' || NEW.payment_method
        );
        
        -- Create Cash debit entry
        RAISE LOG 'Creating Cash debit entry for amount: %', NEW.amount;
        
        INSERT INTO form_ledger_finance (
            account_id, 
            source_id, 
            source_form, 
            post_datetime, 
            transaction_datetime,
            account_type, 
            transaction_type,
            debit, 
            credit, 
            created_on, 
            created_by,
            notes
        ) VALUES (
            v_account_id,
            NEW.id,
            'billing_cash',
            COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
            get_site_timestamp(NEW.site_id),
            'Cash',
            'Posting',
            NEW.amount,
            0.00,
            CURRENT_TIMESTAMP,
            v_creator,
            'Payment Method: ' || NEW.payment_method
        );
        
        -- Mark cash as applied using an UPDATE statement (since this is an AFTER trigger)
        UPDATE form_billing_cash
        SET applied_datetime = get_site_timestamp(NEW.site_id)
        WHERE id = NEW.id;
        
        v_success := TRUE;
        RAISE LOG 'Cash successfully processed: %, with amount: %', 
                 COALESCE(NEW.cash_no, 'NULL'), NEW.amount;
        RETURN NEW;
        
    EXCEPTION WHEN OTHERS THEN
        -- Log the error
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Transaction failed during cash processing',
            'TRIGGER',
            current_schema(),
            'billing_cash',
            jsonb_build_object(
                'function_name', 'process_cash_add',
                'cash_no', COALESCE(NEW.cash_no, 'NULL'),
                'success_flag', v_success
            )
        );
        
        RAISE; -- Re-throw the exception to rollback the transaction
    END;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Trigger for cash processing - changed to AFTER trigger
CREATE OR REPLACE TRIGGER trg_billing_cash_insert
AFTER INSERT ON form_billing_cash
FOR EACH ROW
EXECUTE FUNCTION process_cash_add();

-- Function to handle zeroing cash
DO $$ BEGIN
  PERFORM drop_all_function_signatures('process_cash_zero');
END $$;
CREATE OR REPLACE FUNCTION process_cash_zero() RETURNS TRIGGER AS $$
DECLARE
    v_ledger_record RECORD;
    v_creator INTEGER;
    v_success BOOLEAN := FALSE;
BEGIN
    -- Log function entry
    RAISE LOG 'Entering process_cash_zero for cash_no: %', NEW.cash_no;
    
    -- Skip if conditions aren't met
    IF (COALESCE(NEW.zeroed,'No') <> 'Yes' OR 
        COALESCE(OLD.zeroed,'No') = 'Yes' OR
        NEW.applied_datetime IS NULL) THEN
        RAISE LOG 'Skipping process_cash_zero, conditions not met for cash_no: %', 
                 NEW.cash_no;
        RETURN NEW;
    END IF;
    
    -- Check if we're in a closed period
    IF check_closed_period(NEW.post_datetime::timestamp) THEN
        -- Log error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Cannot zero cash in a closed accounting period',
            'Error zeroing cash',
            'TRIGGER',
            current_schema(),
            'billing_cash',
            jsonb_build_object(
                'function_name', 'process_cash_zero',
                'cash_no', NEW.cash_no,
                'post_datetime', NEW.post_datetime
            )
        );
        
        RAISE EXCEPTION 'Cannot zero cash in a closed accounting period for cash %', 
                        NEW.cash_no;
    END IF;
    
    BEGIN
        -- Determine who created/updated this record
        v_creator := COALESCE(NEW.updated_by, NEW.created_by);
        RAISE LOG 'Zeroing cash for cash_no: %, by user: %', 
                 NEW.cash_no, v_creator;
        
        -- Check unapplied cash balance for this record
        DECLARE
            v_unapplied_balance NUMERIC;
        BEGIN
            -- Calculate how much unapplied cash is still available for this record
            SELECT 
                SUM(CASE WHEN account_type = 'Unapplied Cash' THEN credit - debit ELSE 0 END)
            INTO v_unapplied_balance
            FROM form_ledger_finance
            WHERE source_id = NEW.id 
            AND source_form = 'billing_cash';
            
            IF v_unapplied_balance <= 0 THEN
                -- Log error - can't zero since all cash has been applied
                INSERT INTO billing_error_log (
                    error_message,
                    error_context,
                    error_type,
                    schema_name,
                    table_name,
                    additional_details
                ) VALUES (
                    'Cannot zero cash with no remaining unapplied balance',
                    'Error zeroing cash',
                    'TRIGGER',
                    current_schema(),
                    'billing_cash',
                    jsonb_build_object(
                        'function_name', 'process_cash_zero',
                        'cash_no', NEW.cash_no,
                        'unapplied_balance', v_unapplied_balance,
                        'amount', OLD.amount
                    )
                );
                
                RAISE EXCEPTION 'Cannot zero cash % - no remaining unapplied balance', 
                               NEW.cash_no;
            END IF;
        END;
        
        -- Process each ledger entry to create reversing entries
        FOR v_ledger_record IN 
            SELECT * FROM form_ledger_finance lf
            WHERE lf.source_id = NEW.id
            AND lf.source_form = 'billing_cash'
            AND lf.reversal_for_id IS NULL
            AND NOT EXISTS (
                SELECT 1 FROM form_ledger_finance lfr
                WHERE lfr.reversal_for_id = lf.id
            )
            ORDER BY id
        LOOP
            RAISE LOG 'Creating reversing entry for ledger ID: % in cash: %', 
                     v_ledger_record.id, NEW.cash_no;
                     
            -- Create reversing entry
            INSERT INTO form_ledger_finance (
                invoice_id, 
                charge_line_id, 
                account_id, 
                source_id, 
                source_form, 
                post_datetime, 
                transaction_datetime,
                account_type, 
                transaction_type,
                reversal_for_id,
                debit, 
                credit, 
                created_on, 
                created_by,
                notes
            ) VALUES (
                v_ledger_record.invoice_id,
                v_ledger_record.charge_line_id,
                v_ledger_record.account_id,
                NEW.id,
                'billing_cash',
                COALESCE(NEW.zeroed_datetime, get_site_timestamp(NEW.site_id)),
                get_site_timestamp(NEW.site_id),
                v_ledger_record.account_type,
                'Posting Reversal',
                v_ledger_record.id,
                v_ledger_record.credit,  -- Swap credit and debit
                v_ledger_record.debit,   -- Swap credit and debit
                CURRENT_TIMESTAMP,
                v_creator,
                'Zeroed cash: ' || NEW.zeroed_reason_id
            );
        END LOOP;
        
        v_success := TRUE;
        RAISE LOG 'Successfully zeroed cash: %', NEW.cash_no;
        
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        -- Log the error
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Transaction failed during cash zeroing',
            'TRIGGER',
            current_schema(),
            'billing_cash',
            jsonb_build_object(
                'function_name', 'process_cash_zero',
                'cash_no', NEW.cash_no,
                'success_flag', v_success
            )
        );
        
        RAISE; -- Re-throw the exception to rollback the transaction
    END;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Trigger for zeroing cash - changed to AFTER trigger
CREATE OR REPLACE TRIGGER trg_billing_cash_zero
AFTER UPDATE ON form_billing_cash
FOR EACH ROW
WHEN (COALESCE(NEW.zeroed, 'No') = 'Yes' AND COALESCE(OLD.zeroed, 'No') IS DISTINCT FROM 'Yes')
EXECUTE FUNCTION process_cash_zero();
    