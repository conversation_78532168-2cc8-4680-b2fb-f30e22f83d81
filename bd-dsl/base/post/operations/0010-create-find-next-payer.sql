
CREATE OR REPLACE FUNCTION get_insurance_with_rank_original(
    p_order_item_payer_ids TEXT,
    p_order_payer_ids TEXT,
    p_rank TEXT
) RETURNS INTEGER AS $$
DECLARE
    array_1 JSONB;
    array_2 JSONB;
    result INTEGER;
BEGIN
    -- Convert input text to JSONB (or default to an empty array if null or empty)
    array_1 := COALESCE(NULLIF(p_order_item_payer_ids, '[]')::JSONB, '[]'::JSONB);
    array_2 := COALESCE(NULLIF(p_order_payer_ids, '[]')::JSONB, '[]'::JSONB);
    
    IF jsonb_array_length(array_1) > 0 THEN
        SELECT INTO result
            (elem->>'id')::INTEGER
        FROM
            jsonb_array_elements(array_1) AS elem
        WHERE
            elem->>'rank' = p_rank;
    ELSIF jsonb_array_length(array_2) > 0 THEN 
        SELECT INTO result
            (elem->>'id')::INTEGER
        FROM
            jsonb_array_elements(array_2) AS elem
        WHERE
            elem->>'rank' = p_rank;
    ELSE 
        RETURN NULL;
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_insurance_with_rank_fast(
    p_order_item_payer_ids TEXT,
    p_order_payer_ids TEXT,
    p_rank TEXT
) RETURNS INTEGER AS $$
DECLARE
    result INTEGER;
BEGIN
    -- Early return if both are empty
    IF (p_order_item_payer_ids IS NULL OR p_order_item_payer_ids = '[]') AND 
       (p_order_payer_ids IS NULL OR p_order_payer_ids = '[]') THEN
        RETURN NULL;
    END IF;
    
    -- Try order_item_payer_ids first
    IF p_order_item_payer_ids IS NOT NULL AND p_order_item_payer_ids != '[]' THEN
        -- Use more efficient string operations instead of JSON parsing when possible
        SELECT (regexp_matches(p_order_item_payer_ids, 
                '"id":"?(\d+)"?[^}]*"rank":"?' || p_rank || '"?', 'g'))[1]::INTEGER 
        INTO result;
        
        IF result IS NOT NULL THEN
            RETURN result;
        END IF;
    END IF;
    
    -- Try order_payer_ids if we didn't find in order_item_payer_ids
    IF p_order_payer_ids IS NOT NULL AND p_order_payer_ids != '[]' THEN
        SELECT (regexp_matches(p_order_payer_ids, 
                '"id":"?(\d+)"?[^}]*"rank":"?' || p_rank || '"?', 'g'))[1]::INTEGER 
        INTO result;
        
        RETURN result;
    END IF;
    
    -- Fall back to original function if the above methods fail
    -- (This is a safety measure, ideally the above methods should work)
    RETURN get_insurance_with_rank_original(p_order_item_payer_ids, p_order_payer_ids, p_rank);
END;
$$ LANGUAGE plpgsql STABLE;


CREATE OR REPLACE FUNCTION get_insurance_with_rank(
    p_order_item_payer_ids TEXT,
    p_order_payer_ids TEXT,
    p_rank TEXT
) RETURNS INTEGER
LANGUAGE SQL
STABLE
AS $$
    SELECT get_insurance_with_rank_fast($1, $2, $3);
$$;

CREATE OR REPLACE FUNCTION find_insurance_rank(
    p_order_item_payer_ids TEXT,
    p_order_payer_ids TEXT,
    p_insurance_id TEXT  -- Changed from INTEGER to TEXT
) RETURNS INTEGER AS $$
DECLARE
    array_1 JSONB;
    array_2 JSONB;
    result INTEGER;
BEGIN
    -- Convert input text to JSONB (or default to an empty array if null or empty)
    array_1 := COALESCE(NULLIF(p_order_item_payer_ids, '[]')::JSONB, '[]'::JSONB);
    array_2 := COALESCE(NULLIF(p_order_payer_ids, '[]')::JSONB, '[]'::JSONB);
    
    IF p_insurance_id IS NULL THEN 
        RETURN NULL;
    ELSIF jsonb_array_length(array_1) > 0 THEN
        SELECT INTO result
            (elem->>'rank')::INTEGER
        FROM
            jsonb_array_elements(array_1) AS elem
        WHERE
            elem->>'id' = p_insurance_id LIMIT 1;  -- Direct text comparison
    ELSIF jsonb_array_length(array_2) > 0 THEN 
        SELECT INTO result
            (elem->>'rank')::INTEGER
        FROM
            jsonb_array_elements(array_2) AS elem
        WHERE
            elem->>'id' = p_insurance_id LIMIT 1;  -- Direct text comparison
    ELSE 
        RETURN NULL;
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_insurance_ids_for_joining(
    p_order_item_payer_ids TEXT,
    p_order_payer_ids TEXT,
    p_rank TEXT DEFAULT '1'
) RETURNS SETOF INTEGER AS $$
DECLARE
    v_ins_id INTEGER;
BEGIN
    -- Try to get the ID using the fast function
    v_ins_id := get_insurance_with_rank_fast(p_order_item_payer_ids, p_order_payer_ids, p_rank);
    
    IF v_ins_id IS NOT NULL THEN
        RETURN NEXT v_ins_id;
    END IF;
    
    RETURN;
END;
$$ LANGUAGE plpgsql STABLE;

CREATE OR REPLACE FUNCTION get_next_insurance_id(
    p_order_rx_id INTEGER,
    p_insurance_id INTEGER DEFAULT NULL,
    p_default_to_selfpay BOOLEAN DEFAULT TRUE
) RETURNS INTEGER AS $$
DECLARE
    v_billing_method TEXT;
    v_patient_id integer;
    v_next_insurance_id integer;
BEGIN
    IF p_order_rx_id IS NULL THEN
        RETURN NULL;
    END IF;

    SELECT COALESCE(coi.billing_method, copi.billing_method, co.billing_method ) as billing_method,
        co.patient_id
    INTO v_billing_method, v_patient_id
    FROM form_careplan_order_rx rx
    LEFT JOIN form_careplan_order_item coi 
        ON coi.rx_no = rx.rx_no 
        AND coi.archived IS NOT TRUE 
        AND coi.deleted IS NOT TRUE
    LEFT JOIN sf_form_careplan_order_to_careplan_order_item sfco 
        ON sfco.form_careplan_order_item_fk = coi.id 
        AND sfco.archive IS NOT TRUE 
        AND sfco.delete IS NOT TRUE 
    LEFT JOIN form_careplan_orderp_item copi 
        ON copi.rx_no = rx.rx_no 
        AND copi.archived IS NOT TRUE 
        AND copi.deleted IS NOT TRUE
    LEFT JOIN sf_form_careplan_order_to_careplan_orderp_item sfcop 
        ON sfcop.form_careplan_orderp_item_fk = copi.id 
        AND sfcop.archive IS NOT TRUE 
        AND sfcop.delete IS NOT TRUE
    LEFT JOIN form_careplan_order co 
        ON co.id = COALESCE(sfco.form_careplan_order_fk, sfcop.form_careplan_order_fk) 
        AND co.archived IS NOT TRUE
        AND co.deleted IS NOT TRUE
        AND COALESCE(co.void, 'No') <> 'Yes'
    WHERE rx.id = p_order_rx_id;

    IF v_billing_method = 'Do Not Bill' THEN
        RETURN NULL;
    END IF;

    -- Fix for the problematic CTE
    WITH RankedPayers AS (
        SELECT 
            get_insurance_with_rank(
                CASE co.order_format 
                    WHEN 'Single Prescription' THEN copi.payer_ids::text
                    ELSE coi.payer_ids::text
                END,
                CASE co.order_format 
                    WHEN 'Single Prescription' THEN NULL::text
                    ELSE co.payer_ids::text
                END,
                (COALESCE(
                    find_insurance_rank(
                         CASE co.order_format 
                            WHEN 'Single Prescription' THEN copi.payer_ids::text
                            ELSE coi.payer_ids::text
                        END,
                        CASE co.order_format 
                            WHEN 'Single Prescription' THEN NULL::text
                            ELSE co.payer_ids::text
                        END,
                        p_insurance_id::TEXT
                    ),
                    0
                ) + 1)::TEXT
            ) AS next_insurance_id
        FROM form_careplan_order_rx rx
        LEFT JOIN form_careplan_order_item coi 
            ON coi.rx_no = rx.rx_no 
            AND coi.archived IS NOT TRUE 
            AND coi.deleted IS NOT TRUE
        LEFT JOIN sf_form_careplan_order_to_careplan_order_item sfco 
            ON sfco.form_careplan_order_item_fk = coi.id 
            AND sfco.archive IS NOT TRUE 
            AND sfco.delete IS NOT TRUE 
        LEFT JOIN form_careplan_orderp_item copi 
            ON copi.rx_no = rx.rx_no 
            AND copi.archived IS NOT TRUE 
            AND copi.deleted IS NOT TRUE
        LEFT JOIN sf_form_careplan_order_to_careplan_orderp_item sfcop 
            ON sfcop.form_careplan_orderp_item_fk = copi.id 
            AND sfcop.archive IS NOT TRUE 
            AND sfcop.delete IS NOT TRUE
        INNER JOIN form_careplan_order co 
            ON co.id = COALESCE(sfco.form_careplan_order_fk, sfcop.form_careplan_order_fk) 
            AND co.archived IS NOT TRUE 
            AND co.deleted IS NOT TRUE 
        WHERE rx.id = p_order_rx_id 
            AND co.therapy_set_dc IS NULL 
            AND co.void IS NULL
    )
    SELECT next_insurance_id INTO v_next_insurance_id
    FROM RankedPayers;

    IF v_next_insurance_id IS NULL AND p_default_to_selfpay THEN
        SELECT id INTO v_next_insurance_id
        FROM form_patient_insurance
        WHERE payer_id = 1;
    END IF;

    RETURN v_next_insurance_id;
END;
$$ LANGUAGE plpgsql STABLE;