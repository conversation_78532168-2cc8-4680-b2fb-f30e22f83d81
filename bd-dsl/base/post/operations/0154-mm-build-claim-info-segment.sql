CREATE OR REPLACE FUNCTION build_mm_claim_info_segment(
  p_site_id integer,
  p_patient_id integer,
  p_insurance_id integer,
  p_payer_id integer,
  p_charge_lines charge_line_with_split[],
  p_parent_claim_no text DEFAULT NULL
) RETURNS mm_claim_info_segment AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_claim_info_segment;
  v_error_message text;
  v_params jsonb;
  v_patient_control_number text := 'CLAIM_NO_PLACEHOLDER';
  v_total_expected numeric := 0;
  v_total_paid numeric := 0;
  v_total_billed numeric := 0;
  v_patient_weight numeric;
  v_patient_gender text;
  v_claim_filing_code text;
  v_place_of_service_code text;
  v_is_cob boolean := FALSE;
  v_parent_payer_id integer;
  v_override_claim_filing_code text;
  v_pregnancy_indicator text;
  v_death_date text;
  
  -- Helper function results
  v_diagnosis_loop mm_dx_info[];
  v_service_facility mm_service_facility_info;
  v_supplemental_info mm_supplemental_info[];
  v_other_info mm_other_info[];
  v_service_lines mm_service_line_info[];
  v_other_subscriber_info mm_cob_info[];
BEGIN

  -- Check for null input parameters
  IF p_site_id IS NULL THEN
    RAISE EXCEPTION 'Site ID cannot be NULL';
  END IF;

  IF p_patient_id IS NULL THEN
    RAISE EXCEPTION 'Patient ID cannot be NULL';
  END IF;

  IF p_insurance_id IS NULL THEN
    RAISE EXCEPTION 'Insurance ID cannot be NULL';
  END IF;

  IF p_payer_id IS NULL THEN
    RAISE EXCEPTION 'Payer ID cannot be NULL';
  END IF;

  IF p_charge_lines IS NULL OR array_length(p_charge_lines, 1) = 0 THEN
    RAISE EXCEPTION 'Charge lines cannot be NULL or empty';
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'site_id', p_site_id,
    'patient_id', p_patient_id,
    'insurance_id', p_insurance_id,
    'payer_id', p_payer_id,
    'parent_claim_no', p_parent_claim_no
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
      'build_mm_claim_info_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM claim information segment for patient ID: %, insurance ID: %, payer ID: %', p_patient_id, p_insurance_id, p_payer_id;

    -- Determine if this is a COB claim
    v_is_cob := (p_parent_claim_no IS NOT NULL);

    -- Get parent payer_id if this is a COB claim
    IF p_parent_claim_no IS NOT NULL THEN
        -- Check if parent is a medical claim
        IF EXISTS(SELECT 1 FROM form_med_claim WHERE claim_no = p_parent_claim_no AND deleted IS NOT TRUE AND archived IS NOT TRUE) THEN
            SELECT pmc.payer_id, mci.patient_control_number
            INTO v_parent_payer_id, v_patient_control_number
            FROM form_med_claim pmc
            LEFT JOIN sf_form_med_claim_to_med_claim_info sf_mci ON sf_mci.form_med_claim_fk = pmc.id
                AND sf_mci.delete IS NOT TRUE 
                AND sf_mci.archive IS NOT TRUE
            LEFT JOIN form_med_claim_info mci ON mci.id = sf_mci.form_med_claim_info_fk
                AND mci.deleted IS NOT TRUE 
                AND mci.archived IS NOT TRUE
            WHERE pmc.claim_no = p_parent_claim_no
              AND pmc.deleted IS NOT TRUE 
              AND pmc.archived IS NOT TRUE;
              
            -- Set claim filing code to '1' for Medicare Secondary Payer
            v_claim_filing_code := '1';
            
            RAISE LOG 'COB scenario: Using patient_control_number % from parent medical claim %', v_patient_control_number, p_parent_claim_no;
              
        -- Check if parent is an NCPDP claim
        ELSIF EXISTS(SELECT 1 FROM form_ncpdp WHERE claim_no = p_parent_claim_no AND deleted IS NOT TRUE AND archived IS NOT TRUE) THEN
            SELECT nc.invoice_no
            INTO v_patient_control_number
            FROM form_ncpdp nc
            WHERE nc.claim_no = p_parent_claim_no
              AND nc.deleted IS NOT TRUE 
              AND nc.archived IS NOT TRUE;
              
            -- Set claim filing code to '1' for Medicare Secondary Payer
            v_claim_filing_code := '1';
            
            RAISE LOG 'COB scenario: Using patient_control_number % from parent NCPDP claim %', v_patient_control_number, p_parent_claim_no;
            
        -- Check if parent is a 1500 claim
        ELSIF EXISTS(SELECT 1 FROM form_med_claim_1500 WHERE id::text = p_parent_claim_no AND deleted IS NOT TRUE AND archived IS NOT TRUE) THEN
            SELECT mc1500.patient_account_no
            INTO v_patient_control_number
            FROM form_med_claim_1500 mc1500
            WHERE mc1500.id::text = p_parent_claim_no
              AND mc1500.deleted IS NOT TRUE 
              AND mc1500.archived IS NOT TRUE;
              
            -- Also get payer_id from 1500 claim
            SELECT mc1500.payer_id
            INTO v_parent_payer_id
            FROM form_med_claim_1500 mc1500
            WHERE mc1500.id::text = p_parent_claim_no
              AND mc1500.deleted IS NOT TRUE 
              AND mc1500.archived IS NOT TRUE;
              
            -- Set claim filing code to '1' for Medicare Secondary Payer
            v_claim_filing_code := '1';
            
            RAISE LOG 'COB scenario: Using patient_control_number % from parent 1500 claim %', v_patient_control_number, p_parent_claim_no;
        END IF;
    END IF;

    -- Check for claim_filing_code override from patient insurance medical conditions
    SELECT pimc.claim_filing_code
    INTO v_override_claim_filing_code
    FROM form_patient_insurance_med_cond pimc
    WHERE pimc.patient_id = p_patient_id
      AND pimc.active_payer_id = p_payer_id
      AND (v_parent_payer_id IS NULL OR pimc.previous_payer_id = v_parent_payer_id)
      AND pimc.deleted IS NOT TRUE 
      AND pimc.archived IS NOT TRUE
      AND pimc.claim_filing_code IS NOT NULL
    ORDER BY pimc.created_on DESC
    LIMIT 1;

    -- Calculate charge totals from charge lines
    SELECT
        COALESCE(SUM(cl.expected), 0),
        COALESCE(SUM(cl.paid), 0),
        COALESCE(SUM(cl.billed), 0)
    INTO 
        v_total_expected,
        v_total_paid,
        v_total_billed
    FROM unnest(p_charge_lines) cl;

    -- Get patient demographics
    WITH patient_weight AS (
      SELECT DISTINCT ON (patient_id)
      patient_id,
      ml.weight
      FROM form_patient_measurement_log ml
      WHERE ml.archived IS NOT TRUE AND ml.deleted IS NOT TRUE
      ORDER BY patient_id, date DESC
    )
    SELECT 
        wt.weight,
        CASE 
            WHEN pt.gender = 'Male' THEN 'M'
            WHEN pt.gender = 'Female' THEN 'F'
            ELSE 'U'
        END,
        -- Check for pregnancy diagnosis if female or unspecified gender
        CASE 
            WHEN (pt.gender = 'Female' OR pt.gender IS NULL) 
                 AND EXISTS (
                     SELECT 1 
                     FROM form_patient_diagnosis pd
                     INNER JOIN form_list_diagnosis dx ON dx.code = pd.dx_id
                     WHERE pd.patient_id = pt.id
                       AND pd.deleted IS NOT TRUE 
                       AND pd.archived IS NOT TRUE
                       AND COALESCE(pd.active, 'No') = 'Yes'
                       AND LOWER(dx.name) ILIKE '%pregnan%'  -- Simple pregnancy check
                 ) THEN 'Y'
            ELSE NULL
        END as pregnancy_indicator,
        CASE
          WHEN pt.death_date IS NOT NULL THEN TO_CHAR(pt.death_date, 'MM/DD/YYYY')::text
          ELSE NULL::text
        END as death_date
    INTO 
        v_patient_weight,
        v_patient_gender,
        v_pregnancy_indicator,
        v_death_date
    FROM form_patient pt
    LEFT JOIN patient_weight wt ON wt.patient_id = pt.id
    WHERE pt.id = p_patient_id
      AND pt.deleted IS NOT TRUE 
      AND pt.archived IS NOT TRUE;

    -- Apply comprehensive payer settings
    DECLARE
        v_payer_settings jsonb;
        v_previous_payer_id integer;
    BEGIN
        -- Get previous payer ID for COB claims
        IF v_is_cob IS TRUE THEN
            SELECT mc.payer_id
            INTO v_previous_payer_id
            FROM form_med_claim mc
            WHERE mc.claim_no = p_parent_claim_no
              AND mc.deleted IS NOT TRUE 
              AND mc.archived IS NOT TRUE;
        END IF;

        -- Apply payer settings
        v_payer_settings := apply_mm_payer_settings(
            p_payer_id, 
            v_previous_payer_id, 
            p_insurance_id, 
            p_charge_lines
        );

        -- Extract settings (don't override claim_filing_code if already set by COB logic)
        IF v_claim_filing_code IS NULL THEN
            v_claim_filing_code := COALESCE(v_override_claim_filing_code, (v_payer_settings->>'claim_filing_indicator_code')::text, 'CI');
        END IF;
        v_place_of_service_code := COALESCE((v_payer_settings->>'default_service_place_id')::text, '12');
        
    END;

    -- Build diagnosis loop
    v_diagnosis_loop := build_mm_claim_dx_loop(p_charge_lines, p_parent_claim_no);

    -- Build service facility segment
    v_service_facility := build_mm_service_facility_segment(p_charge_lines, p_parent_claim_no);

    -- Build supplemental information
    WITH supplemental_result AS (
        SELECT build_mm_supplemental_segment(
            p_patient_id, 
            p_insurance_id, 
            p_payer_id, 
            p_charge_lines, 
            CASE WHEN v_patient_control_number = 'CLAIM_NO_PLACEHOLDER' THEN NULL ELSE v_patient_control_number END, 
            p_parent_claim_no
        ) as supp_info
    )
    SELECT CASE WHEN sr.supp_info IS NOT NULL THEN ARRAY[sr.supp_info] ELSE NULL::mm_supplemental_info[] END
    INTO v_supplemental_info
    FROM supplemental_result sr;

    -- Build other info segment  
    WITH other_info_result AS (
        SELECT build_mm_claim_info_other_segment(p_site_id, p_patient_id, p_insurance_id, p_payer_id, v_total_billed, p_parent_claim_no) as other_info
    )
    SELECT CASE WHEN oir.other_info IS NOT NULL THEN ARRAY[oir.other_info] ELSE NULL::mm_other_info[] END
    INTO v_other_info
    FROM other_info_result oir;

    -- Build service lines
    v_service_lines := build_mm_service_lines_loop(p_insurance_id, p_payer_id, p_charge_lines, v_diagnosis_loop, p_parent_claim_no);

    -- Build COB-specific segments if needed
    IF v_is_cob IS TRUE THEN
        -- Build other subscriber information  
        v_other_subscriber_info := build_mm_other_subscriber_loop(p_insurance_id, p_parent_claim_no);
    END IF;

    -- Assemble the complete claim information segment
    SELECT
        p_site_id::integer,
        p_patient_id::integer,
        p_insurance_id::integer,
        p_payer_id::integer,
        v_patient_weight::numeric,
        v_death_date::text,
        v_patient_control_number::text,
        v_patient_gender::text,
        v_claim_filing_code::text,
        v_place_of_service_code::text,
        'A'::text,  -- plan_participation_code (DefaultPlanParticipateCode)
        v_total_billed::numeric,
        v_total_paid::numeric,
        '1'::text,  -- claim_frequency_code (ORIGINAL_CLAIM)
        'Y'::text,  -- signature_indicator (DefaultProvSignatureIndicator)
        'P'::text,  -- patient_signature_source_code (DefaultPatientSignatureIndicator)
        'Y'::text,  -- release_information_code (DefaultReleaseOfInformationCode)
        NULL::text, -- homebound_indicator
        'Y'::text,  -- benefits_assignment_certification_indicator (DefaultBenefitsAssignmentIndicator)
        v_pregnancy_indicator,
        NULL::text, -- delay_reason_code
        v_diagnosis_loop::mm_dx_info[],
        NULL::mm_repricing_info[],
        CASE WHEN v_service_facility IS NOT NULL THEN ARRAY[v_service_facility] ELSE NULL::mm_service_facility_info[] END,
        CASE WHEN v_other_subscriber_info IS NOT NULL AND array_length(v_other_subscriber_info, 1) > 0 THEN v_other_subscriber_info::mm_cob_info[] ELSE NULL::mm_cob_info[] END,
        CASE WHEN v_service_lines IS NOT NULL AND array_length(v_service_lines, 1) > 0 THEN v_service_lines::mm_service_line_info[] ELSE NULL::mm_service_line_info[] END,
        CASE WHEN v_supplemental_info IS NOT NULL AND array_length(v_supplemental_info, 1) > 0 THEN v_supplemental_info::mm_supplemental_info[] ELSE NULL::mm_supplemental_info[] END,
        CASE WHEN v_supplemental_info IS NOT NULL AND array_length(v_supplemental_info, 1) > 0 THEN 'Yes' ELSE NULL END::text,
        CASE WHEN v_other_info IS NOT NULL AND array_length(v_other_info, 1) > 0 THEN v_other_info::mm_other_info[] ELSE NULL::mm_other_info[] END,
        CASE WHEN v_other_info IS NOT NULL AND array_length(v_other_info, 1) > 0 THEN 'Yes' ELSE NULL END::text
    INTO v_result;

    RAISE LOG 'MM claim information segment build result with % diagnosis codes, % service lines', 
              COALESCE(array_length(v_diagnosis_loop, 1), 0),
              COALESCE(array_length(v_service_lines, 1), 0);

    -- Log success
    PERFORM log_billing_function(
      'build_mm_claim_info_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_claim_info_segment',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'build_mm_claim_info_segment',
        'patient_id', p_patient_id,
        'insurance_id', p_insurance_id,
        'payer_id', p_payer_id,
        'parent_claim_no', p_parent_claim_no
      )
    );

    PERFORM log_billing_function(
      'build_mm_claim_info_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;