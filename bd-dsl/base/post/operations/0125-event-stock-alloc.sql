-- METADATA_START
-- Functions Created: [process_pending_shipment_ledger_event]
-- Functions Called: [jsonb_build_object, process_inv_event, row_to_json] /* Filtered to user-defined only */
-- Views Created: []
-- Views Referenced: []
-- METADATA_END
-- 0157-event-stock-alloc.sql
-- Trigger for Stock Allocation/De-allocation Event (Creates Pending Shipment Ledgers)

-- =============================================================================
-- Stock Allocation/De-allocation Trigger Function
-- =============================================================================

CREATE OR REPLACE FUNCTION process_pending_shipment_ledger_event()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_is_adjustment BOOLEAN;
    v_inv_log       inv_transaction_log;
    v_inv_ledgers   inv_ledger[];
BEGIN
    RAISE LOG 'Trigger process_pending_shipment_ledger_event fired for StkAlloc ID: %, WtPulled ID: %, Stock ID: %, Qty: %',
        NEW.id, NEW.careplan_dt_wt_pulled_id, NEW.inventory_stock_id, NEW.quantity;

    -- Determine if this is an adjustment (negative quantity) or initial pull (positive quantity)
    v_is_adjustment := (NEW.quantity < 0);

    -- Generate the appropriate Inventory Log ('pending_shipment' or 'pending_shipment_adjustment')
    v_inv_log := gen_inv_shipment_transaction_log(
        p_wt_pulled_id  => NEW.careplan_dt_wt_pulled_id::integer,
        p_stock_id      => NEW.inventory_stock_id::integer,
        p_quantity      => NEW.quantity::numeric,
        p_is_adjustment => v_is_adjustment::boolean
    );

    -- Generate the corresponding Inventory Ledger entries
    v_inv_ledgers := gen_inv_ledgers(v_inv_log);

    -- Process the Inventory Event (Insert Log & Ledgers)
    PERFORM process_inv_event(v_inv_log, v_inv_ledgers);

    RAISE LOG 'Successfully processed pending shipment ledger for StkAlloc ID: %', NEW.id;
    RETURN NEW; -- Required for AFTER trigger

EXCEPTION
    WHEN OTHERS THEN
        RAISE LOG 'Error in process_pending_shipment_ledger_event trigger for StkAlloc ID %: %', NEW.id, SQLERRM;
        INSERT INTO inventory_ledger_error_log (
            error_message, error_context, additional_details, error_type, schema_name, table_name
        )
        VALUES (
            SQLERRM,
            'process_pending_shipment_ledger_event trigger',
            jsonb_build_object('stk_alloc_id', NEW.id, 'stk_alloc_details', row_to_json(NEW)),
            'Trigger Error',
            TG_TABLE_SCHEMA,
            TG_TABLE_NAME
        );
        RAISE; -- Re-raise exception
END;
$$;

COMMENT ON FUNCTION process_pending_shipment_ledger_event() IS 'Trigger function called upon insert into form_careplan_dt_wt_pulled_stk_allc. Generates inventory log and ledger entries for pending shipments or adjustments.';

-- =============================================================================
-- Stock Allocation/De-allocation Trigger Definition
-- =============================================================================

DROP TRIGGER IF EXISTS trg_pending_shipment_ledger ON form_careplan_dt_wt_pulled_stk_allc;

CREATE TRIGGER trg_pending_shipment_ledger
AFTER INSERT ON form_careplan_dt_wt_pulled_stk_allc
FOR EACH ROW
EXECUTE FUNCTION process_pending_shipment_ledger_event();

COMMENT ON TRIGGER trg_pending_shipment_ledger ON form_careplan_dt_wt_pulled_stk_allc IS 'Handles the creation of pending shipment inventory ledger entries when stock is allocated or de-allocated.';

-- ----------------------------------------------------------------------------- 