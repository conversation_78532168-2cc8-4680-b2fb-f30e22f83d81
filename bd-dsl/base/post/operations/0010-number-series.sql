BEGIN;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('ACCOUNT_LEDGER', NULL, 5, '000', NULL, NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('ADJUSTMENT_NO', NULL, 5, NULL, NULL, NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('BILLABLE', NULL, 5, 'BILL', NULL, NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('CHARGE', NULL, 8, 'CHG', NULL, NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('CLAIM', NULL, 6, NULL, NULL, NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('CLOSE_PERIOD', NULL, 5, NULL, NULL, NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('DISPENSE', NULL, 6, '100', NULL, NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('INVOICE', NULL, 8, '100', '', NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('MRN', NULL, 4, NULL, NULL, NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('ORDER', NULL, 6, NULL, NULL, NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('PO', NULL, 4, NULL, NULL, NULL, '2024-05-08', NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('POSTING', NULL, 5, NULL, NULL, NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('RECEIPT', NULL, 4, '', '', NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('RX', NULL, 5, '10', NULL, NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('SERVICE_LINE_CONTROL_NUMBER', NULL, 25, NULL, NULL, NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('TESTRX', NULL, 10, '1000', '', NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('TICKET', NULL, 5, '00', NULL, NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('WORKTICKET', NULL, 5, '00', NULL, NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
INSERT INTO form_number_series (series, last_number_used, digit_length, prefix, suffix, last_sequence_number_used, start_date, end_date, active) VALUES ('WRITEOFF_NO', NULL, 5, NULL, NULL, NULL, NULL, NULL, 'Yes') ON CONFLICT (series) DO NOTHING;
COMMIT;
