-- Phase 5: DME Rental Billing Implementation
-- This handles recurring billing for DME equipment rentals

-- Create a view to track active DME rentals that need billing
CREATE OR REPLACE VIEW vw_active_dme_rentals AS
WITH rental_activity AS (
    -- Get the latest activity for each rental item to determine current status
    SELECT DISTINCT ON (rla.serial_no, rla.inventory_id)
        rla.serial_no,
        rla.inventory_id,
        rla.patient_id,
        rla.previous_patient_id,
        rla.localized_datetime,
        rla.ticket_no,
        rla.ticket_item_no,
        -- Determine if currently checked out based on latest activity
        CASE 
            WHEN rla.patient_id IS NOT NULL AND rla.previous_patient_id IS NULL THEN 'checked_out'
            WHEN rla.patient_id IS NULL AND rla.previous_patient_id IS NOT NULL THEN 'checked_in'
            ELSE 'unknown'
        END as activity_type
    FROM form_ledger_rental_log_activity rla
    WHERE rla.deleted IS NOT TRUE
    AND rla.archived IS NOT TRUE
    ORDER BY rla.serial_no, rla.inventory_id, rla.localized_datetime DESC
),
current_rentals AS (
    -- Get currently active rentals from rental log
    -- A rental is active if the latest activity shows equipment is checked out to a patient
    SELECT 
        rl.id as rental_log_id,
        rl.patient_id,
        rl.inventory_id,
        rl.serial_no,
        rl.ticket_no,
        rl.ticket_item_no,
        rl.checked_out_date,
        rl.site_id,
        -- Get delivery ticket info
        dt.delivery_date,
        dt.id as delivery_ticket_id,
        -- Get delivery ticket item info
        dti.rx_id,
        dti.insurance_id as dt_insurance_id,
        dti.rental_type,
        dti.frequency_code,
        dti.day_supply,
        -- Get prescription info
        rx.order_no,
        -- Get order info for rental billable
        co.rental_billable_id as rental_billable_id
    FROM form_inventory_rental_log rl
    INNER JOIN rental_activity ra ON ra.serial_no = rl.serial_no 
        AND ra.inventory_id = rl.inventory_id
        AND ra.activity_type = 'checked_out'
        AND ra.patient_id = rl.patient_id  -- Equipment is currently with this patient
    LEFT JOIN form_careplan_delivery_tick dt ON dt.ticket_no = rl.ticket_no
        AND dt.deleted IS NOT TRUE
        AND dt.archived IS NOT TRUE
    LEFT JOIN form_careplan_dt_item dti ON dti.ticket_no = rl.ticket_no
        AND dti.ticket_item_no = rl.ticket_item_no
        AND dti.deleted IS NOT TRUE
        AND dti.archived IS NOT TRUE
        AND dti.rental_type = 'Rental'
    LEFT JOIN form_careplan_order_rx rx ON rx.id = dti.rx_id
        AND rx.deleted IS NOT TRUE
        AND rx.archived IS NOT TRUE
    LEFT JOIN form_careplan_order co ON co.order_no = rx.order_no
        AND co.deleted IS NOT TRUE
        AND co.archived IS NOT TRUE
    WHERE rl.patient_id IS NOT NULL  -- Equipment is currently assigned to a patient in rental log
    AND rl.deleted IS NOT TRUE
    AND rl.archived IS NOT TRUE
),
rental_insurance AS (
    -- Find appropriate insurance for each rental
    SELECT 
        cr.*,
        COALESCE(
            cr.dt_insurance_id,
            vw.rental_insurance_id,
            pi_dme.id
        ) as insurance_id,
        COALESCE(
            cr.rental_billable_id,
            inv_billable.id,
            cr.inventory_id
        ) as billable_inventory_id
    FROM current_rentals cr
    LEFT JOIN vw_order_raw vw ON vw.rx_id = cr.rx_id
    LEFT JOIN form_patient_insurance pi_dme ON pi_dme.patient_id = cr.patient_id
        AND pi_dme.deleted IS NOT TRUE
        AND pi_dme.archived IS NOT TRUE
        AND COALESCE(pi_dme.active, 'No') = 'Yes'
        AND EXISTS (
            SELECT 1 FROM form_payer py 
            WHERE py.id = pi_dme.payer_id
            AND ((py.billing_method_id IN ('mm', 'cms1500') OR COALESCE(py.bill_mm_and_ncpdp, 'No') = 'Yes'))
            AND COALESCE(py.cover_dme, 'No') = 'Yes'
            AND py.deleted IS NOT TRUE
            AND py.archived IS NOT TRUE
        )
    LEFT JOIN form_inventory inv_rental ON inv_rental.id = cr.inventory_id
        AND inv_rental.deleted IS NOT TRUE
        AND inv_rental.archived IS NOT TRUE
    LEFT JOIN form_inventory inv_billable ON inv_billable.billable_code_id = inv_rental.billable_code_id
        AND inv_billable.type = 'Billable'
        AND inv_billable.deleted IS NOT TRUE
        AND inv_billable.archived IS NOT TRUE
        AND COALESCE(inv_billable.active, 'No') = 'Yes'
),
rental_with_payer_settings AS (
    -- Get payer settings for billing logic
    SELECT 
        ri.*,
        py.no_recurring_billing,
        py.bill_recur_arrears,
        py.span_rental_dates,
        py.type_id as payer_type_id,
        -- Calculate billing start date
        COALESCE(ri.delivery_date, ri.checked_out_date) as billing_start_date,
        -- Get prior auth info
        pa.id as prior_auth_id,
        pa.rental_price_approved,
        py.max_rental_claims as max_rental_claims
    FROM rental_insurance ri
    LEFT JOIN form_patient_insurance pi ON pi.id = ri.insurance_id
        AND pi.deleted IS NOT TRUE
        AND pi.archived IS NOT TRUE
    LEFT JOIN form_payer py ON py.id = pi.payer_id
        AND py.deleted IS NOT TRUE
        AND py.archived IS NOT TRUE
    LEFT JOIN form_patient_prior_auth pa ON pa.patient_id = ri.patient_id
        AND pa.insurance_id = ri.insurance_id
        AND pa.deleted IS NOT TRUE
        AND pa.archived IS NOT TRUE
        AND COALESCE(pa.status_id, '') = '5'  -- Active
        AND pa.pa_type LIKE '%DME%'
        AND COALESCE(pa.rental_coverage_type, '') = 'Rental'
    WHERE ri.insurance_id IS NOT NULL
    AND ri.billable_inventory_id IS NOT NULL
),
patient_status_check AS (
    -- Check if patient is still active
    SELECT 
        rps.*,
        pt.status_id as current_patient_status,
        -- Find when patient went off service (if applicable)
        lpa.localized_datetime as off_service_date
    FROM rental_with_payer_settings rps
    INNER JOIN form_patient pt ON pt.id = rps.patient_id
        AND pt.deleted IS NOT TRUE
        AND pt.archived IS NOT TRUE
    LEFT JOIN form_ledger_patient_activity lpa ON lpa.patient_id = rps.patient_id
        AND lpa.field = 'status_id'
        AND lpa.old_value IN ('1', '3')  -- Was active
        AND lpa.new_value NOT IN ('1', '3')  -- Now inactive
        AND lpa.deleted IS NOT TRUE
        AND lpa.archived IS NOT TRUE
        AND lpa.localized_datetime >= rps.billing_start_date
),
last_billed_period AS (
    -- Get the last billed service period for each rental
    SELECT 
        psc.*,
        -- Get the most recent service period end date
        MAX(lcl.date_of_service_end) as last_service_end_date,
        -- Extract the day of month from billing start date for span_rental_dates logic
        EXTRACT(DAY FROM psc.billing_start_date)::integer as billing_day_of_month
    FROM patient_status_check psc
    LEFT JOIN form_ledger_charge_line lcl ON lcl.patient_id = psc.patient_id
        AND lcl.inventory_id = psc.billable_inventory_id
        AND lcl.rental_id = psc.rental_log_id
        AND lcl.deleted IS NOT TRUE
        AND lcl.archived IS NOT TRUE
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
        AND lcl.date_of_service_end IS NOT NULL
    GROUP BY 
        psc.rental_log_id, psc.patient_id, psc.inventory_id, psc.serial_no,
        psc.ticket_no, psc.ticket_item_no, psc.checked_out_date, psc.site_id,
        psc.delivery_date, psc.delivery_ticket_id, psc.rx_id, psc.dt_insurance_id,
        psc.rental_type, psc.frequency_code, psc.day_supply, psc.order_no,
        psc.rental_billable_id, psc.insurance_id, psc.billable_inventory_id,
        psc.no_recurring_billing, psc.bill_recur_arrears, psc.span_rental_dates,
        psc.payer_type_id, psc.billing_start_date, psc.prior_auth_id, 
        psc.rental_price_approved, psc.max_rental_claims, psc.current_patient_status, 
        psc.off_service_date
),
claims_count AS (
    -- Count existing claims for this rental and check if current period already billed
    SELECT 
        lbp.*,
        COUNT(lcl.id) as existing_claims_count,
        -- Check if we've already billed for the current period
        COUNT(CASE 
            WHEN COALESCE(lbp.span_rental_dates, 'No') = 'Yes' THEN
                -- With span_rental_dates, check if we've billed up to the current expected end date
                CASE WHEN lcl.date_of_service_end >= 
                    CASE 
                        -- Determine the expected end date based on billing cycle
                        WHEN lbp.billing_day_of_month = 1 THEN 
                            -- First of month - ends last day of month
                            DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day'
                        WHEN lbp.billing_day_of_month IN (30, 31) THEN
                            -- 30th/31st - handle short months
                            CASE 
                                WHEN EXTRACT(DAY FROM (DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day')) < lbp.billing_day_of_month THEN
                                    DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day'
                                ELSE
                                    DATE_TRUNC('month', CURRENT_DATE) + (lbp.billing_day_of_month - 1 || ' days')::interval
                            END
                        ELSE 
                            -- Other days - monthly period (e.g., 15th to 14th)
                            DATE_TRUNC('month', CURRENT_DATE) + (lbp.billing_day_of_month - 2 || ' days')::interval
                    END 
                THEN 1 END
            ELSE
                -- Original logic for non-span rentals
                CASE
                    WHEN COALESCE(lbp.bill_recur_arrears, 'No') = 'Yes' AND 
                         DATE_TRUNC('month', lcl.created_on) = DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month') THEN 1
                    WHEN COALESCE(lbp.bill_recur_arrears, 'No') <> 'Yes' AND 
                         DATE_TRUNC('month', lcl.created_on) = DATE_TRUNC('month', CURRENT_DATE) THEN 1
                END
        END) as current_period_billed_count,
        -- Determine max claims allowed
        CASE 
            WHEN lbp.max_rental_claims IS NOT NULL THEN lbp.max_rental_claims
            WHEN lbp.payer_type_id IN ('MCRB', 'MCRD') THEN 13
            ELSE 18
        END as max_claims_allowed
    FROM last_billed_period lbp
    LEFT JOIN form_ledger_charge_line lcl ON lcl.patient_id = lbp.patient_id
        AND lcl.inventory_id = lbp.billable_inventory_id
        AND lcl.deleted IS NOT TRUE
        AND lcl.archived IS NOT TRUE
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
    GROUP BY 
        lbp.rental_log_id, lbp.patient_id, lbp.inventory_id, lbp.serial_no,
        lbp.ticket_no, lbp.ticket_item_no, lbp.checked_out_date, lbp.site_id,
        lbp.delivery_date, lbp.delivery_ticket_id, lbp.rx_id, lbp.dt_insurance_id,
        lbp.rental_type, lbp.frequency_code, lbp.day_supply, lbp.order_no,
        lbp.rental_billable_id, lbp.insurance_id, lbp.billable_inventory_id,
        lbp.no_recurring_billing, lbp.bill_recur_arrears, lbp.span_rental_dates,
        lbp.payer_type_id, lbp.billing_start_date, lbp.prior_auth_id, 
        lbp.rental_price_approved, lbp.max_rental_claims, lbp.current_patient_status, 
        lbp.off_service_date, lbp.last_service_end_date, lbp.billing_day_of_month
)
SELECT 
    cc.rental_log_id,
    cc.patient_id,
    cc.inventory_id,
    cc.billable_inventory_id,
    cc.serial_no,
    cc.ticket_no,
    cc.ticket_item_no,
    cc.site_id,
    cc.insurance_id,
    cc.rx_id,
    cc.frequency_code,
    cc.day_supply,
    cc.billing_start_date,
    cc.no_recurring_billing,
    cc.bill_recur_arrears,
    cc.span_rental_dates,
    cc.payer_type_id,
    cc.prior_auth_id,
    cc.rental_price_approved,
    cc.existing_claims_count,
    cc.max_claims_allowed,
    cc.current_patient_status,
    cc.off_service_date,
    cc.current_period_billed_count,
    cc.last_service_end_date,
    cc.billing_day_of_month,
    -- Determine if rental is eligible for billing
    CASE 
        WHEN COALESCE(cc.no_recurring_billing, 'No') = 'Yes' THEN 'No recurring billing allowed'
        WHEN cc.current_patient_status NOT IN ('1', '3') AND 
             (cc.bill_recur_arrears IS NULL OR cc.bill_recur_arrears <> 'Yes') THEN 'Patient discharged'
        WHEN cc.existing_claims_count >= cc.max_claims_allowed THEN 'Max claims reached'
        WHEN cc.current_period_billed_count > 0 THEN 'Current period already billed'
        WHEN cc.insurance_id IS NULL THEN 'No valid insurance found'
        WHEN cc.billable_inventory_id IS NULL THEN 'No billable inventory found'
        ELSE NULL::text
    END as billing_exclusion_reason,
    -- Calculate next billing date
    CASE 
        WHEN COALESCE(cc.span_rental_dates, 'No') = 'Yes' THEN
            -- With span_rental_dates, calculate based on last billed period
            CASE 
                WHEN cc.last_service_end_date IS NULL THEN 
                    -- First billing - use billing start date
                    cc.billing_start_date
                ELSE 
                    -- Next day after last service end
                    cc.last_service_end_date + INTERVAL '1 day'
            END
        ELSE
            -- Original logic for non-span rentals
            CASE 
                WHEN COALESCE(cc.bill_recur_arrears, 'No') = 'Yes' THEN
                    -- Bill on 1st of next month for current month
                    DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month'
                ELSE
                    -- Bill on 1st of current month for current month (if not already billed)
                    DATE_TRUNC('month', CURRENT_DATE)
            END
    END as next_billing_date,
    -- Calculate billing period end date
    CASE 
        WHEN COALESCE(cc.span_rental_dates, 'No') = 'Yes' THEN
            -- With span_rental_dates, calculate proper end date based on billing cycle
            CASE 
                WHEN cc.last_service_end_date IS NULL THEN
                    -- First billing period
                    CASE 
                        WHEN cc.billing_day_of_month = 1 THEN 
                            -- First of month - ends last day of same month
                            DATE_TRUNC('month', cc.billing_start_date) + INTERVAL '1 month' - INTERVAL '1 day'
                        WHEN cc.billing_day_of_month IN (30, 31) THEN
                            -- 30th/31st - handle short months
                            CASE 
                                WHEN EXTRACT(DAY FROM (DATE_TRUNC('month', cc.billing_start_date) + INTERVAL '1 month' - INTERVAL '1 day')) < cc.billing_day_of_month THEN
                                    DATE_TRUNC('month', cc.billing_start_date) + INTERVAL '1 month' - INTERVAL '1 day'
                                ELSE
                                    DATE_TRUNC('month', cc.billing_start_date) + INTERVAL '1 month' + (cc.billing_day_of_month - 1 || ' days')::interval
                            END
                        ELSE 
                            -- Other days - monthly period (e.g., 15th to 14th of next month)
                            cc.billing_start_date + INTERVAL '1 month' - INTERVAL '1 day'
                    END
                ELSE
                    -- Subsequent billing periods - add one month and adjust
                    CASE 
                        WHEN cc.billing_day_of_month = 1 THEN 
                            -- First of month - ends last day of month
                            DATE_TRUNC('month', cc.last_service_end_date + INTERVAL '1 day') + INTERVAL '1 month' - INTERVAL '1 day'
                        WHEN cc.billing_day_of_month IN (30, 31) THEN
                            -- 30th/31st - handle short months
                            CASE 
                                -- Check if target month has fewer days than billing day
                                WHEN EXTRACT(DAY FROM (DATE_TRUNC('month', cc.last_service_end_date + INTERVAL '1 day') + INTERVAL '2 months' - INTERVAL '1 day')) < cc.billing_day_of_month THEN
                                    -- Use last day of month
                                    DATE_TRUNC('month', cc.last_service_end_date + INTERVAL '1 day') + INTERVAL '2 months' - INTERVAL '1 day'
                                ELSE
                                    -- Use the billing day - 1
                                    DATE_TRUNC('month', cc.last_service_end_date + INTERVAL '1 day') + INTERVAL '1 month' + (cc.billing_day_of_month - 1 || ' days')::interval
                            END
                        ELSE 
                            -- Other days - add one month, minus one day
                            cc.last_service_end_date + INTERVAL '1 month'
                    END
            END
        ELSE
            -- Original logic for non-span rentals
            CASE 
                WHEN COALESCE(cc.bill_recur_arrears, 'No') = 'Yes' THEN
                    -- Previous month end (billing for last month)
                    DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '1 day'
                ELSE
                    -- Current month end (billing for current month)
                    DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day'
            END
    END as billing_period_end_date
FROM claims_count cc
WHERE cc.insurance_id IS NOT NULL
AND cc.billable_inventory_id IS NOT NULL;

-- Create function to generate DME rental charge line
CREATE OR REPLACE FUNCTION create_dme_rental_charge_line(
    p_rental_log_id integer,
    p_service_from_date date,
    p_service_to_date date
) RETURNS charge_line_with_split AS $$
DECLARE
    v_rental_info record;
    v_charge_line charge_line;
    v_result charge_line_with_split;
    v_quantity numeric := 1.0;
    v_days_in_period integer;
    v_pricing_info record;
    v_expected_amount numeric := 0.00;
    v_billed_amount numeric := 0.00;
    v_calc_invoice_split_no text;
BEGIN
    -- Get rental information
    SELECT 
        adr.patient_id,
        adr.inventory_id,
        adr.billable_inventory_id,
        adr.site_id,
        adr.insurance_id,
        adr.frequency_code,
        adr.rental_price_approved,
        adr.ticket_no,
        adr.ticket_item_no,
        adr.rx_id
    INTO v_rental_info
    FROM vw_active_dme_rentals adr
    WHERE adr.rental_log_id = p_rental_log_id;

    IF v_rental_info IS NULL THEN
        RAISE EXCEPTION 'Rental log ID % not found in active rentals', p_rental_log_id;
    END IF;

    -- Calculate days in billing period
    v_days_in_period := (p_service_to_date - p_service_from_date) + 1;

    -- Calculate quantity based on frequency
    IF v_rental_info.frequency_code = '6' THEN -- Daily
        v_quantity := v_days_in_period::numeric;
    ELSIF v_rental_info.frequency_code = '1' THEN -- Weekly
        v_quantity := ROUND(v_days_in_period::numeric / 7.0, 2);
    ELSE -- Monthly (default)
        v_quantity := ROUND(v_days_in_period::numeric / 28.0, 2);
    END IF;

    -- Get pricing information
    SELECT * INTO v_pricing_info
    FROM get_inventory_pricing(
        v_rental_info.billable_inventory_id,
        v_rental_info.insurance_id,
        v_rental_info.site_id,
        v_rental_info.patient_id
    );

    -- Calculate expected and billed amounts
    IF v_rental_info.rental_price_approved IS NOT NULL THEN
        -- Use prior auth approved price
        v_expected_amount := v_rental_info.rental_price_approved * v_quantity;
        v_billed_amount := v_rental_info.rental_price_approved * v_quantity;
    ELSE
        -- Use pricing from get_inventory_pricing
        IF v_rental_info.frequency_code = '6' THEN -- Daily
            v_expected_amount := COALESCE(v_pricing_info.rental_expected_daily_ea, 0) * v_quantity;
            v_billed_amount := COALESCE(v_pricing_info.rental_bill_daily_ea, 0) * v_quantity;
        ELSE -- Weekly and Monthly use monthly rates
            v_expected_amount := COALESCE(v_pricing_info.rental_expected_monthly_ea, 0) * v_quantity;
            v_billed_amount := COALESCE(v_pricing_info.rental_bill_monthly_ea, 0) * v_quantity;
        END IF;
    END IF;

    -- Generate calc_invoice_split_no for grouping rental charges
    v_calc_invoice_split_no := gen_random_uuid();

    -- Create the charge line
    SELECT * INTO v_charge_line
    FROM create_ledger_charge_line(
        p_inventory_id := v_rental_info.billable_inventory_id,
        p_insurance_id := v_rental_info.insurance_id,
        p_site_id := v_rental_info.site_id,
        p_patient_id := v_rental_info.patient_id,
        p_quantity := v_quantity,
        p_is_primary_drug := FALSE,
        p_compound_no := NULL,
        p_order_rx_id := v_rental_info.rx_id,
        p_rental_id := p_rental_log_id,
        p_ticket_no := v_rental_info.ticket_no,
        p_ticket_item_no := v_rental_info.ticket_item_no,
        p_encounter_id := NULL,
        p_is_test := FALSE,
        p_parent_charge_no := NULL
    );

    -- Override pricing amounts if needed
    IF v_expected_amount > 0 THEN
        v_charge_line.expected := v_expected_amount;
        v_charge_line.calc_expected_ea := ROUND(v_expected_amount / v_quantity, 4);
    END IF;

    IF v_billed_amount > 0 THEN
        v_charge_line.billed := v_billed_amount;
        v_charge_line.calc_billed_ea := ROUND(v_billed_amount / v_quantity, 4);
        v_charge_line.gross_amount_due := v_billed_amount;
        v_charge_line.total_balance_due := v_expected_amount;
    END IF;

    -- Set the service dates
    v_charge_line.date_of_service := p_service_from_date;
    v_charge_line.date_of_service_end := p_service_to_date;

    -- Build result with calc_invoice_split_no
    SELECT 
        NULL as id,
        v_calc_invoice_split_no as calc_invoice_split_no,
        v_charge_line.*
    INTO v_result;

    RETURN v_result;

EXCEPTION WHEN OTHERS THEN
    -- Log error
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        SQLERRM,
        'Error in create_dme_rental_charge_line',
        'FUNCTION',
        current_schema(),
        'inventory_rental_log',
        jsonb_build_object(
            'rental_log_id', p_rental_log_id,
            'service_from_date', p_service_from_date,
            'service_to_date', p_service_to_date
        )
    );
    RAISE;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Create view to show rentals due for billing
CREATE OR REPLACE VIEW vw_dme_rentals_due_for_billing AS
SELECT 
    adr.rental_log_id,
    adr.patient_id,
    pt.mrn,
    TRIM(CONCAT(pt.firstname, ' ', pt.lastname)) as patient_name,
    adr.inventory_id,
    inv.name as equipment_name,
    adr.billable_inventory_id,
    bill_inv.name as billable_item_name,
    adr.serial_no,
    adr.site_id,
    st.name as site_name,
    adr.insurance_id,
    py.organization as payer_name,
    adr.frequency_code,
    CASE adr.frequency_code
        WHEN '6' THEN 'Daily Rate'
        WHEN '1' THEN 'Weekly Rate'
        ELSE 'Monthly Rate'
    END as frequency_description,
    adr.billing_start_date,
    adr.next_billing_date,
    adr.billing_period_end_date,
    adr.existing_claims_count,
    adr.max_claims_allowed,
    adr.billing_exclusion_reason,
    adr.span_rental_dates,
    adr.bill_recur_arrears,
    adr.last_service_end_date,
    adr.billing_day_of_month,
    -- Determine if due for billing today
    CASE 
        WHEN adr.billing_exclusion_reason IS NOT NULL THEN FALSE
        WHEN adr.next_billing_date <= CURRENT_DATE THEN TRUE
        ELSE FALSE
    END as due_for_billing,
    -- Calculate service period for next bill
    CASE 
        WHEN COALESCE(adr.span_rental_dates, 'No') = 'Yes' THEN
            -- With span_rental_dates, service period starts from next billing date
            adr.next_billing_date
        ELSE
            -- Original logic
            CASE 
                WHEN COALESCE(adr.bill_recur_arrears, 'No') = 'Yes' THEN
                    -- Billing in arrears - service period is previous month
                    DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '1 month'
                ELSE
                    -- Billing upfront - service period starts from current month
                    DATE_TRUNC('month', CURRENT_DATE)
            END
    END as service_from_date,
    -- Use the calculated billing period end date
    adr.billing_period_end_date as service_to_date
FROM vw_active_dme_rentals adr
INNER JOIN form_patient pt ON pt.id = adr.patient_id
    AND pt.deleted IS NOT TRUE
    AND pt.archived IS NOT TRUE
INNER JOIN form_inventory inv ON inv.id = adr.inventory_id
    AND inv.deleted IS NOT TRUE
    AND inv.archived IS NOT TRUE
INNER JOIN form_inventory bill_inv ON bill_inv.id = adr.billable_inventory_id
    AND bill_inv.deleted IS NOT TRUE
    AND bill_inv.archived IS NOT TRUE
INNER JOIN form_site st ON st.id = adr.site_id
    AND st.deleted IS NOT TRUE
    AND st.archived IS NOT TRUE
INNER JOIN form_patient_insurance pi ON pi.id = adr.insurance_id
    AND pi.deleted IS NOT TRUE
    AND pi.archived IS NOT TRUE
INNER JOIN form_payer py ON py.id = pi.payer_id
    AND py.deleted IS NOT TRUE
    AND py.archived IS NOT TRUE
WHERE adr.billing_exclusion_reason IS NULL
ORDER BY adr.next_billing_date, adr.patient_id;

-- Add helpful comments
COMMENT ON VIEW vw_active_dme_rentals IS 
'Shows all currently active DME rentals with billing information and eligibility status.
When span_rental_dates is enabled for a payer:
- The From Date remains as the rental start/cycle date
- The To Date reflects the proper end of the rental period
- Rentals starting on 1st end on the last day of the month
- Rentals starting on 30th/31st handle short months appropriately
- All other rentals follow monthly periods (e.g., 2nd to 1st, 15th to 14th)';

COMMENT ON VIEW vw_dme_rentals_due_for_billing IS 
'Shows DME rentals that are due for billing based on their frequency and billing settings.
Incorporates span_rental_dates logic to properly calculate service periods based on payer configuration.';

COMMENT ON FUNCTION create_dme_rental_charge_line(integer, date, date) IS 
'Creates a charge line for a DME rental billing period with appropriate pricing and quantity calculations.
The service dates passed to this function should follow the span_rental_dates logic if enabled for the payer.';

SELECT pgboss.create_queue('worker_dme_rental_billing'::text, '{"policy":"standard"}'::json);
INSERT INTO pgboss.schedule (name,cron,data,options,timezone) VALUES ('worker_dme_rental_billing','0 12 * * *', '{"action":"process_dme_rental_billing"}','{}','America/New_York') ON CONFLICT (name) DO UPDATE SET "cron" = EXCLUDED."cron", "timezone" = EXCLUDED."timezone";
