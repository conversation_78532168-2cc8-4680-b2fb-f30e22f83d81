
DO $$ BEGIN
  PERFORM drop_all_function_signatures('set_rx_status');
END $$;
CREATE OR REPLACE FUNCTION set_rx_status() RETURNS TRIGGER AS $$
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Set status based on rx flags
    IF NEW.rx_complete IS NULL THEN
        NEW.status := 'Setup';
    ELSIF COALESCE(NEW.rx_complete, 'No') = 'Yes' THEN
        IF COALESCE(NEW.rx_denied, 'No') = 'Yes' THEN
            NEW.status := 'Denied';
        ELSIF COALESCE(NEW.rx_verified, 'No') = 'Yes' THEN
            NEW.status := 'Verified';
        ELSIF COALESCE(NEW.rx_verified, 'No') = 'No' AND COALESCE(NEW.rx_denied, 'No') = 'No' THEN
            NEW.status := 'Complete';
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE TRIGGER trg_set_rx_status
    BEFORE INSERT OR UPDATE ON form_careplan_order_rx
    FOR EACH ROW
    EXECUTE FUNCTION set_rx_status();
