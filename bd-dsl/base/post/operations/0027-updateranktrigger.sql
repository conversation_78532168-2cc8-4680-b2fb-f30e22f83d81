-- Trigger for patient insurance form
CREATE OR REPLACE TRIGGER trg_update_rank_insurance
BEFORE INSERT OR UPDATE ON form_patient_insurance
FOR EACH ROW
EXECUTE FUNCTION update_patient_rank("form_patient_insurance");

-- Trigger for patient diagnosis form
CREATE OR REPLACE TRIGGER trg_update_rank_diagnosis
BEFORE INSERT OR UPDATE ON form_patient_diagnosis
FOR EACH ROW
EXECUTE FUNCTION update_patient_rank("form_patient_diagnosis");