-- Drop the existing view
DROP VIEW IF EXISTS vw_report_helper;

-- Create the new view
DO $$
DECLARE
    col record;
    query text := 'CREATE VIEW vw_report_helper AS SELECT ';
BEGIN
    -- Columns from form_site (excluding JSON fields and search)
    FOR col IN (SELECT column_name FROM information_schema.columns WHERE table_name = 'form_site' AND column_name NOT IN ('logo', 'search'))
    LOOP
        query := query || format('s.%I AS %I, ', col.column_name, 'site_' || col.column_name);
    END LOOP;

    -- Flatten the logo JSON field from form_site
    query := query || '(s.logo::jsonb)->>''filehash'' AS site_logo_filehash, ';
    query := query || '(s.logo::jsonb)->>''filename'' AS site_logo_filename, ';
    query := query || '(s.logo::jsonb)->>''filesize'' AS site_logo_filesize, ';
    query := query || '(s.logo::jsonb)->>''mimetype'' AS site_logo_mimetype, ';

    -- Columns from form_patient (excluding search)
    FOR col IN (SELECT column_name FROM information_schema.columns WHERE table_name = 'form_patient' AND column_name != 'search')
    LOOP
        query := query || format('p.%I AS %I, ', col.column_name, 'patient_' || col.column_name);
    END LOOP;

    -- Columns from form_physician (excluding search)
    FOR col IN (SELECT column_name FROM information_schema.columns WHERE table_name = 'form_physician' AND column_name != 'search')
    LOOP
        query := query || format('ph.%I AS %I, ', col.column_name, 'physician_' || col.column_name);
    END LOOP;

    -- Columns from form_payer (excluding search)
    FOR col IN (SELECT column_name FROM information_schema.columns WHERE table_name = 'form_payer' AND column_name != 'search')
    LOOP
        query := query || format('py.%I AS %I, ', col.column_name, 'payer_' || col.column_name);
    END LOOP;

    -- Remove the trailing comma and space
    query := rtrim(query, ', ');

    -- Add the FROM and JOIN clauses
    query := query || ' FROM form_site s ' ||
                     'LEFT JOIN form_patient p ON p.site_id = s.id AND p.deleted is not true AND p.archived is not true AND p.status_id != ''5'' ' ||
                     'LEFT JOIN LATERAL (
                         WITH ranked_insurance AS (
                             SELECT pi.*,
                                ROW_NUMBER() OVER (PARTITION BY patient_id ORDER BY pi."rank") as row_num
                             FROM form_patient_insurance pi
                             WHERE pi.patient_id = p.id
                             AND pi.active = ''Yes''
                             AND pi.payer_level = ''Primary''
                             AND pi.deleted is not true AND pi.archived is not true
                         )
                         SELECT *
                         FROM ranked_insurance
                         WHERE row_num = 1
                     ) pi ON true ' ||
                     'LEFT JOIN form_payer py ON py.id = pi.payer_id AND py.deleted is not true AND py.archived is not true ' ||
                     'LEFT JOIN LATERAL (
                         SELECT pp.*
                         FROM form_patient_prescriber pp
                         WHERE pp.patient_id = p.id
                         AND pp.deleted is not true AND pp.archived is not true
                         LIMIT 1
                     ) pp ON true ' ||
                     'LEFT JOIN form_physician ph ON ph.id = pp.physician_id AND ph.deleted is not true AND ph.archived is not true ' ||
                     'WHERE s.deleted is not true AND s.archived is not true';

    -- Execute the query to create the view
    EXECUTE query;
END $$;