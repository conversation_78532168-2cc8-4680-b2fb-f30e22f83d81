-- Core function for NCPDP payment posting
CREATE OR R<PERSON>LACE FUNCTION process_ncpdp_payment_posting_core(p_response_id INTEGER, p_process_reversals BOOLEAN DEFAULT FALSE)
RETURNS VOID AS $$
DECLARE
    v_response RECORD;
    v_invoice_no TEXT;
    v_highest_expected_charge_line_id BIGINT;
    v_charge_record RECORD;
    v_ledger_record RECORD;
    v_proportion NUMERIC;
    v_line_payment_amount NUMERIC;
    v_processed_payment_amount NUMERIC := 0;
    v_total_paid NUMERIC;
    v_account_id INTEGER;
    v_ncpdp_id INTEGER;
    v_site_id INTEGER;
    v_revenue_accepted_posted BOOLEAN := FALSE;
    v_claim_status TEXT;
    v_payer_id INTEGER;
    v_invoice_id INTEGER;
    v_company_auto_cash_posting BOOLEAN := FALSE;
BEGIN
    -- Get the response record
    SELECT * INTO v_response 
    FROM form_ncpdp_response 
    WHERE id = p_response_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'NCPDP response record not found with ID %', p_response_id;
    END IF;
    
    RAISE LOG 'process_ncpdp_payment_posting_core Response ID %, process_reversals: %', p_response_id, p_process_reversals;
    
    IF v_response.claim_no IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'claim_no cannot be null',
            'Validation in process_ncpdp_payment_posting_core function',
            'FUNCTION',
            current_schema(),
            'form_ncpdp_response',
            jsonb_build_object('claim_no', v_response.claim_no)
        );
        RETURN;
    END IF;

    -- Check if auto cash posting is enabled
    SELECT 
        CASE WHEN ncpdp_cash_posting = 'Yes' THEN TRUE ELSE FALSE END 
    INTO v_company_auto_cash_posting
    FROM form_company 
    WHERE id = 1;

    -- Fetch necessary data for payment posting
    SELECT
        bi.invoice_no,
        COALESCE(bi.total_paid, 0),
        CASE WHEN COALESCE(bi.revenue_accepted_posted, 'No') = 'Yes' THEN TRUE ELSE FALSE END,
        claim.id,
        claim.site_id,
        claim.payer_id,
        bi.id,
        CASE
            WHEN stat.transaction_response_status = 'P' THEN 'Payable'
            WHEN (v_response.response_status = 'R' OR stat.transaction_response_status = 'R') THEN 'Rejected'
            WHEN stat.transaction_response_status = 'A' THEN 'Approved'
            WHEN stat.transaction_response_status = 'C' THEN 'Captured'
            WHEN stat.transaction_response_status = 'F' THEN 'PA Deferred'
            WHEN stat.transaction_response_status IN ('Q','D','S') THEN 'Duplicate'
            WHEN stat.transaction_response_status = 'B' THEN 'Benefit'
            ELSE 'Sent'
        END
    INTO
        v_invoice_no,
        v_total_paid,
        v_revenue_accepted_posted,
        v_ncpdp_id,
        v_site_id,
        v_payer_id,
        v_invoice_id,
        v_claim_status
    FROM form_billing_invoice bi
    INNER JOIN form_ncpdp claim ON claim.claim_no = v_response.claim_no
    INNER JOIN sf_form_billing_invoice_to_ncpdp sfbi 
        ON sfbi.form_ncpdp_fk = claim.id AND sfbi.form_billing_invoice_fk = bi.id
        AND sfbi.archive IS NOT TRUE
        AND sfbi.delete IS NOT TRUE
    LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_stat sfstat 
        ON sfstat.form_ncpdp_response_fk = v_response.id
        AND sfstat.archive IS NOT TRUE
        AND sfstat.delete IS NOT TRUE
    LEFT JOIN form_ncpdp_response_stat stat
        ON stat.id = sfstat.form_ncpdp_response_stat_fk 
        AND stat.archived IS NOT TRUE
        AND stat.deleted IS NOT TRUE
    WHERE bi.archived IS NOT TRUE
    AND bi.deleted IS NOT TRUE
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes';

    IF v_invoice_no IS NULL THEN
        RAISE LOG 'No valid invoice found for NCPDP claim response ID %', p_response_id;
        RETURN;
    END IF;

    -- Find the charge line with the highest expected amount for remainder distribution
    SELECT id
    INTO v_highest_expected_charge_line_id
    FROM form_ledger_charge_line
    WHERE invoice_no = v_invoice_no
    AND (archived IS NULL OR archived = FALSE)
    AND (deleted IS NULL OR deleted = FALSE)
    AND COALESCE(void, 'No') <> 'Yes'
    ORDER BY expected DESC
    LIMIT 1;

    -- Get account ID for the payer
    SELECT id INTO v_account_id
    FROM form_billing_account
    WHERE type = 'Payer'
    AND payer_id = v_payer_id
    AND archived IS NOT TRUE
    AND deleted IS NOT TRUE;

    IF v_account_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Account not found for NCPDP payment posting transaction',
            'Error in payment posting',
            'FUNCTION',
            current_schema(),
            'form_ncpdp_response',
            jsonb_build_object(
                'claim_no', v_response.claim_no,
                'payer_id', v_payer_id
            )
        );
        RAISE EXCEPTION 'Account not found for NCPDP payment posting transaction %', v_response.claim_no;
    END IF;

    -- Create automatic cash posting if enabled and invoice is posted to revenue
    IF v_company_auto_cash_posting IS TRUE AND v_revenue_accepted_posted IS TRUE THEN
        IF v_total_paid > 0 AND v_claim_status IN ('Payable', 'Margin') THEN
            -- Process each charge line for payment
            FOR v_charge_record IN 
                SELECT 
                    lcl.*,
                    bi.id as invoice_id
                FROM form_ledger_charge_line lcl
                INNER JOIN form_billing_invoice bi ON bi.invoice_no = lcl.invoice_no
                WHERE lcl.invoice_no = v_invoice_no
                AND lcl.archived IS NOT TRUE
                AND lcl.deleted IS NOT TRUE
                AND COALESCE(lcl.void, 'No') <> 'Yes'
                AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
                AND bi.archived IS NOT TRUE
                AND bi.deleted IS NOT TRUE
                AND COALESCE(bi.void, 'No') <> 'Yes'
                AND COALESCE(bi.zeroed, 'No') <> 'Yes'
                ORDER BY lcl.expected DESC
            LOOP
                -- Calculate proportion for this charge line
                v_proportion := v_charge_record.expected / v_total_paid;
                v_line_payment_amount := ROUND(v_total_paid::numeric * v_proportion, 2);

                -- Handle rounding differences for the last charge line
                IF v_charge_record.id = v_highest_expected_charge_line_id THEN
                    v_line_payment_amount := v_total_paid - v_processed_payment_amount;
                ELSE
                    v_processed_payment_amount := v_processed_payment_amount + v_line_payment_amount;
                END IF;
                
                -- Skip if payment amount is zero
                IF v_line_payment_amount <= 0 THEN
                    CONTINUE;
                END IF;

                INSERT INTO form_ledger_finance (
                    invoice_id, 
                    invoice_no,
                    charge_line_id, 
                    charge_no,
                    site_id,
                    account_id, 
                    source_id, 
                    source_form, 
                    post_datetime, 
                    transaction_datetime,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    account_type, 
                    transaction_type,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    v_invoice_id,
                    v_invoice_no,
                    v_charge_record.id,
                    v_charge_record.charge_no,
                    v_site_id,
                    v_account_id,
                    v_ncpdp_id,
                    'ncpdp',
                    get_site_timestamp(v_site_id, FALSE),
                    get_site_timestamp(v_site_id, FALSE),
                    v_charge_record.inventory_id,
                    NULL,
                    NULL,
                    NULL,
                    'Cash',
                    'Posting',
                    v_line_payment_amount,
                    0.00,
                    CURRENT_TIMESTAMP,
                    v_response.created_by,
                    'Auto-posting payment from NCPDP response for charge_line: ' || v_charge_record.charge_no
                );

                INSERT INTO form_ledger_finance (
                    invoice_id, 
                    invoice_no,
                    charge_line_id,
                    charge_no,
                    site_id,
                    account_id, 
                    source_id, 
                    source_form, 
                    post_datetime, 
                    transaction_datetime,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    account_type, 
                    transaction_type,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    v_invoice_id,
                    v_invoice_no,
                    v_charge_record.id,
                    v_charge_record.charge_no,
                    v_site_id,
                    v_account_id,
                    v_ncpdp_id,
                    'ncpdp',
                    get_site_timestamp(v_site_id, FALSE),
                    get_site_timestamp(v_site_id, FALSE),
                    v_charge_record.inventory_id,
                    NULL,
                    NULL,
                    NULL,
                    'AR',
                    'Posting',
                    0.00,
                    v_line_payment_amount,
                    CURRENT_TIMESTAMP,
                    v_response.created_by,
                    'Auto-posting payment from NCPDP response for charge_line: ' || v_charge_record.charge_no
                );
            END LOOP;
            
            RAISE LOG 'Created automatic payment AR transactions for invoice: % ', 
                    v_invoice_no;
        ELSIF p_process_reversals IS TRUE AND v_claim_status IN ('Rejected', 'Reversed', 'PA Deferred', 'Duplicate') THEN
            -- Need to reverse any ledger entries we may have made against the claim
            BEGIN
                -- Create reversing entries for all finance entries related to this invoice
                FOR v_ledger_record IN 
                    SELECT * FROM form_ledger_finance lf
                    WHERE invoice_id = v_invoice_id
                    AND reversal_for_id IS NULL
                    AND transaction_type IN ('Posting','Adjustment')
                    AND source_form = 'ncpdp'
                    AND NOT EXISTS (
                        SELECT 1 FROM form_ledger_finance lfr
                        WHERE lfr.reversal_for_id = lf.id
                    )
                    ORDER BY id
                LOOP
                    RAISE LOG 'Creating reversing entry for ledger ID: % in invoice: %', 
                            v_ledger_record.id, v_invoice_no;
                    
                    -- Create reversing entry (swap debit and credit)
                    INSERT INTO form_ledger_finance (
                        invoice_id, 
                        invoice_no,
                        charge_line_id, 
                        charge_no,
                        account_id, 
                        source_id, 
                        source_form, 
                        post_datetime,
                        transaction_datetime,
                        inventory_id,
                        ledger_inventory_id,
                        ledger_lot_id,
                        ledger_serial_id,
                        po_id,
                        site_id,
                        account_type, 
                        transaction_type,
                        reversal_for_id,
                        quantity,
                        debit, 
                        credit, 
                        created_on, 
                        created_by,
                        notes
                    ) VALUES (
                        v_ledger_record.invoice_id,
                        v_ledger_record.invoice_no,
                        v_ledger_record.charge_line_id,
                        v_ledger_record.charge_no,
                        v_ledger_record.account_id,
                        v_ncpdp_id,
                        'ncpdp',
                        get_site_timestamp(v_site_id, FALSE),
                        get_site_timestamp(v_site_id, FALSE),
                        v_ledger_record.inventory_id,
                        v_ledger_record.ledger_inventory_id,
                        v_ledger_record.ledger_lot_id,
                        v_ledger_record.ledger_serial_id,
                        v_ledger_record.po_id,
                        v_site_id,
                        v_ledger_record.account_type,
                        'Posting Reversal',
                        v_ledger_record.id,
                        v_ledger_record.quantity,
                        v_ledger_record.credit,  -- Swap credit and debit
                        v_ledger_record.debit,   -- Swap credit and debit
                        CURRENT_TIMESTAMP,
                        v_response.created_by,
                        'Auto-reversing NCPDP posting for charge_line: ' || v_ledger_record.charge_no || ' for claim status: ' || v_claim_status
                    );
                END LOOP;
            END;
        END IF;
    END IF;

EXCEPTION WHEN OTHERS THEN
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        SQLERRM,
        'Transaction failed during NCPDP payment posting',
        'FUNCTION',
        current_schema(),
        'form_ncpdp_response',
        jsonb_build_object(
            'function_name', 'process_ncpdp_payment_posting_core',
            'claim_no', v_response.claim_no,
            'invoice_no', v_invoice_no
        )
    );
    RAISE NOTICE 'Error processing NCPDP payment posting: %', SQLERRM;
    RAISE;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Trigger function that calls the core function
CREATE OR REPLACE FUNCTION process_ncpdp_payment_posting()
RETURNS TRIGGER AS $$
BEGIN
    RAISE LOG 'process_ncpdp_payment_posting Response ID %', NEW.ID;
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);
    
    -- Call the core function with the response ID
    PERFORM process_ncpdp_payment_posting_core(NEW.id, TRUE);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Keep the existing trigger definition
DROP TRIGGER IF EXISTS process_ncpdp_payment_posting_trigger ON form_ncpdp_response;
CREATE CONSTRAINT TRIGGER process_ncpdp_payment_posting_trigger
    AFTER INSERT OR UPDATE ON form_ncpdp_response
    DEFERRABLE INITIALLY DEFERRED
    FOR EACH ROW
    EXECUTE FUNCTION process_ncpdp_payment_posting();
