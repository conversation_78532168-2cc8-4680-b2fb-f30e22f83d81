-- Helper function to convert MM claim record to JSON (similar to ncpdp_record_to_json)
CREATE OR REPLACE FUNCTION mm_record_to_json(
  p_claim_record mm_claim_record
) RETURNS jsonb AS $BODY$
DECLARE
  v_result jsonb;
  v_receiver_json jsonb := NULL;
  v_submitter_json jsonb := NULL;
  v_pay_to_address_json jsonb := NULL;
  v_subscriber_json jsonb := NULL;
  v_dependent_json jsonb := NULL;
  v_claim_information_json jsonb := NULL;
  v_providers_json jsonb := NULL;
BEGIN
  -- Return NULL if input is NULL
  IF p_claim_record IS NULL THEN
    RAISE LOG 'mm_record_to_json called with NULL p_claim_record, returning NULL';
    RETURN NULL;
  END IF;

  RAISE LOG 'mm_record_to_json called for claim_no: %, patient_id: %, insurance_id: %, payer_id: %',
    p_claim_record.claim_no, p_claim_record.patient_id, p_claim_record.insurance_id, p_claim_record.payer_id;

  -- Process receiver segment if not null
  IF p_claim_record.receiver IS NOT NULL THEN
    RAISE LOG 'Processing receiver segment for claim_no: %', p_claim_record.claim_no;
    BEGIN
      SELECT jsonb_agg(
        jsonb_build_object(
          'payer_id', r.payer_id,
          'organization_name', r.organization_name,
          'address', CASE WHEN r.address IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'address1', addr.address1,
                'address2', addr.address2,
                'city', addr.city,
                'state', addr.state,
                'postal_code', addr.postal_code
              )
            ) FROM unnest(r.address) addr)
          ELSE NULL END
        )
      ) INTO v_receiver_json
      FROM unnest(p_claim_record.receiver) r;
      RAISE LOG 'Receiver segment processed for claim_no: %', p_claim_record.claim_no;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in receiver segment: %', SQLERRM;
      v_receiver_json := NULL;
    END;
  ELSE
    RAISE LOG 'No receiver segment present for claim_no: %', p_claim_record.claim_no;
  END IF;

  -- Process submitter segment if not null
  IF p_claim_record.submitter IS NOT NULL THEN
    RAISE LOG 'Processing submitter segment for claim_no: %', p_claim_record.claim_no;
    BEGIN
      SELECT jsonb_agg(
        jsonb_build_object(
          'site_id', s.site_id,
          'organization_name', s.organization_name,
          'contact_information', CASE WHEN s.contact_information IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'name', ci.name,
                'email', ci.email,
                'phone_number', ci.phone_number,
                'fax_number', ci.fax_number
              )
            ) FROM unnest(s.contact_information) ci)
          ELSE NULL END
        )
      ) INTO v_submitter_json
      FROM unnest(p_claim_record.submitter) s;
      RAISE LOG 'Submitter segment processed for claim_no: %', p_claim_record.claim_no;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in submitter segment: %', SQLERRM;
      v_submitter_json := NULL;
    END;
  ELSE
    RAISE LOG 'No submitter segment present for claim_no: %', p_claim_record.claim_no;
  END IF;

  -- Process pay_to_address if not null
  IF p_claim_record.pay_to_address IS NOT NULL THEN
    RAISE LOG 'Processing pay_to_address segment for claim_no: %', p_claim_record.claim_no;
    BEGIN
      SELECT jsonb_agg(
        jsonb_build_object(
          'address1', addr.address1,
          'address2', addr.address2,
          'city', addr.city,
          'state', addr.state,
          'postal_code', addr.postal_code
        )
      ) INTO v_pay_to_address_json
      FROM unnest(p_claim_record.pay_to_address) addr;
      RAISE LOG 'pay_to_address segment processed for claim_no: %', p_claim_record.claim_no;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in pay_to_address segment: %', SQLERRM;
      v_pay_to_address_json := NULL;
    END;
  ELSE
    RAISE LOG 'No pay_to_address segment present for claim_no: %', p_claim_record.claim_no;
  END IF;

  -- Process subscriber segment if not null
  IF p_claim_record.subscriber IS NOT NULL THEN
    RAISE LOG 'Processing subscriber segment for claim_no: %', p_claim_record.claim_no;
    BEGIN
      SELECT jsonb_agg(
        jsonb_build_object(
          'insurance_id', sub.insurance_id,
          'medical_relationship_id', sub.medical_relationship_id,
          'patient_id', sub.patient_id,
          'first_name', sub.first_name,
          'last_name', sub.last_name,
          'middle_name', sub.middle_name,
          'date_of_birth', sub.date_of_birth,
          'gender', sub.gender,
          'member_id', sub.member_id,
          'policy_number', sub.policy_number,
          'group_number', sub.group_number,
          'payment_responsibility_level_code', sub.payment_responsibility_level_code,
          'insurance_type_code', sub.insurance_type_code,
          'address', CASE WHEN sub.address IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'address1', addr.address1,
                'address2', addr.address2,
                'city', addr.city,
                'state', addr.state,
                'postal_code', addr.postal_code
              )
            ) FROM unnest(sub.address) addr)
          ELSE NULL END,
          'contact_information', CASE WHEN sub.contact_information IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'name', ci.name,
                'email', ci.email,
                'phone_number', ci.phone_number
              )
            ) FROM unnest(sub.contact_information) ci)
          ELSE NULL END
        )
      ) INTO v_subscriber_json
      FROM unnest(p_claim_record.subscriber) sub;
      RAISE LOG 'Subscriber segment processed for claim_no: %', p_claim_record.claim_no;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in subscriber segment: %', SQLERRM;
      v_subscriber_json := NULL;
    END;
  ELSE
    RAISE LOG 'No subscriber segment present for claim_no: %', p_claim_record.claim_no;
  END IF;

  -- Process dependent segment if not null
  IF p_claim_record.dependent IS NOT NULL THEN
    RAISE LOG 'Processing dependent segment for claim_no: %', p_claim_record.claim_no;
    BEGIN
      SELECT jsonb_agg(
        jsonb_build_object(
          'relationship_to_subscriber_code', dep.relationship_to_subscriber_code,
          'member_id', dep.member_id,
          'first_name', dep.first_name,
          'last_name', dep.last_name,
          'middle_name', dep.middle_name,
          'date_of_birth', dep.date_of_birth,
          'gender', dep.gender,
          'address', CASE WHEN dep.address IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'address1', addr.address1,
                'address2', addr.address2,
                'city', addr.city,
                'state', addr.state,
                'postal_code', addr.postal_code
              )
            ) FROM unnest(dep.address) addr)
          ELSE NULL END,
          'contact_information', CASE WHEN dep.contact_information IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'name', ci.name,
                'email', ci.email,
                'phone_number', ci.phone_number
              )
            ) FROM unnest(dep.contact_information) ci)
          ELSE NULL END
        )
      ) INTO v_dependent_json
      FROM unnest(p_claim_record.dependent) dep;
      RAISE LOG 'Dependent segment processed for claim_no: %', p_claim_record.claim_no;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in dependent segment: %', SQLERRM;
      v_dependent_json := NULL;
    END;
  ELSE
    RAISE LOG 'No dependent segment present for claim_no: %', p_claim_record.claim_no;
  END IF;

  -- Process claim_information segment (the most complex one)
  IF p_claim_record.claim_information IS NOT NULL THEN
    RAISE LOG 'Processing claim_information segment for claim_no: %', p_claim_record.claim_no;
    BEGIN
      SELECT jsonb_agg(
        jsonb_build_object(
          'site_id', ci.site_id,
          'patient_id', ci.patient_id,
          'insurance_id', ci.insurance_id,
          'payer_id', ci.payer_id,
          'patient_weight', ci.patient_weight,
          'death_date', ci.death_date,
          'patient_control_number', ci.patient_control_number,
          'gender', ci.gender,
          'claim_filing_code', ci.claim_filing_code,
          'place_of_service_code', ci.place_of_service_code,
          'plan_participation_code', ci.plan_participation_code,
          'claim_charge_amount', ci.claim_charge_amount,
          'patient_amount_paid', ci.patient_amount_paid,
          'claim_frequency_code', ci.claim_frequency_code,
          'signature_indicator', ci.signature_indicator,
          'patient_signature_source_code', ci.patient_signature_source_code,
          'release_information_code', ci.release_information_code,
          'homebound_indicator', ci.homebound_indicator,
          'benefits_assignment_certification_indicator', ci.benefits_assignment_certification_indicator,
          'pregnancy_indicator', ci.pregnancy_indicator,
          'delay_reason_code', ci.delay_reason_code,
          'tabif_claim_supplemental_information', ci.tabif_claim_supplemental_information,
          'tabif_claim_info_other', ci.tabif_claim_info_other,
          
          -- Process health_care_code_information (diagnosis info)
          'health_care_code_information', CASE WHEN ci.health_care_code_information IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'patient_id', dx.patient_id,
                'dx_id', dx.dx_id,
                'diagnosis_type_code', dx.diagnosis_type_code,
                'diagnosis_code', dx.diagnosis_code
              )
            ) FROM unnest(ci.health_care_code_information) dx)
          ELSE NULL END,
          -- Process service_facility_location
          'service_facility_location', CASE WHEN ci.service_facility_location IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'infusion_suite_id', sf.infusion_suite_id,
                'npi', sf.npi,
                'organization_name', sf.organization_name,
                'phone_name', sf.phone_name,
                'phone_number', sf.phone_number,
                'address', CASE WHEN sf.address IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'address1', addr.address1,
                      'address2', addr.address2,
                      'city', addr.city,
                      'state', addr.state,
                      'postal_code', addr.postal_code
                    )
                  ) FROM unnest(sf.address) addr)
                ELSE NULL END
              )
            ) FROM unnest(ci.service_facility_location) sf)
          ELSE NULL END,
          
          -- Process other_subscriber_information (COB info) - simplified due to extreme complexity
          'other_subscriber_information', CASE WHEN ci.other_subscriber_information IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'patient_id', cob.patient_id,
                'cob_insurance_id', cob.cob_insurance_id,
                'cob_payer_id', cob.cob_payer_id,
                'payment_responsibility_level_code', cob.payment_responsibility_level_code,
                'individual_relationship_code', cob.individual_relationship_code,
                'insurance_type_code', cob.insurance_type_code,
                'claim_filing_indicator_code', cob.claim_filing_indicator_code,
                'benefits_assignment_certification_indicator', cob.benefits_assignment_certification_indicator,
                'patient_signature_generate_for_patient', cob.patient_signature_generate_for_patient,
                'insurance_group_or_policy_number', cob.insurance_group_or_policy_number,
                'other_insured_group_name', cob.other_insured_group_name,
                'release_of_information_code', cob.release_of_information_code,
                'payer_paid_amount', cob.payer_paid_amount,
                'mcr_reimbursement_rate', cob.mcr_reimbursement_rate,
                'mcr_hcpcs_payable_amount', cob.mcr_hcpcs_payable_amount,
                'mcr_rcodes', CASE 
                  WHEN cob.mcr_rcodes IS NOT NULL THEN 
                    (SELECT jsonb_agg(val) FROM unnest(cob.mcr_rcodes) val)
                  ELSE NULL::text[]
                END,
                'non_covered_charge_amount', cob.non_covered_charge_amount,
                'remaining_patient_liability', cob.remaining_patient_liability,
                'tabif_claim_level_adjustments', cob.tabif_claim_level_adjustments,
                'tabif_providers', cob.tabif_providers,
                'tabif_other_payer_service_facility_location', cob.tabif_other_payer_service_facility_location,
                
                -- Process other_subscriber_name
                'other_subscriber_name', CASE WHEN cob.other_subscriber_name IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'other_insured_qualifier', osn.other_insured_qualifier,
                      'other_insured_identifier_type_code', osn.other_insured_identifier_type_code,
                      'other_insured_identifier', osn.other_insured_identifier,
                      'other_insured_first_name', osn.other_insured_first_name,
                      'other_insured_last_name', osn.other_insured_last_name,
                      'other_insured_middle_name', osn.other_insured_middle_name,
                      'other_insured_name_suffix', osn.other_insured_name_suffix,
                      'other_insured_address', CASE WHEN osn.other_insured_address IS NOT NULL THEN
                        (SELECT jsonb_agg(
                          jsonb_build_object(
                            'address1', addr.address1,
                            'address2', addr.address2,
                            'city', addr.city,
                            'state', addr.state,
                            'postal_code', addr.postal_code
                          )
                        ) FROM unnest(osn.other_insured_address) addr)
                      ELSE NULL END
                    )
                  ) FROM unnest(cob.other_subscriber_name) osn)
                ELSE NULL END,
                
                -- Process other_payer_name
                'other_payer_name', CASE WHEN cob.other_payer_name IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'patient_id', opn.patient_id,
                      'cob_insurance_id', opn.cob_insurance_id,
                      'cob_payer_id', opn.cob_payer_id,
                      'other_payer_organization_name', opn.other_payer_organization_name,
                      'other_payer_identifier_type_code', opn.other_payer_identifier_type_code,
                      'other_payer_identifier', opn.other_payer_identifier,
                      'other_payer_adjudication_or_payment_date', opn.other_payer_adjudication_or_payment_date,
                      'other_payer_claim_adjustment_indicator', opn.other_payer_claim_adjustment_indicator,
                      'pa_id', opn.pa_id,
                      'other_payer_prior_authorization_number', opn.other_payer_prior_authorization_number,
                      'other_payer_claim_control_number', opn.other_payer_claim_control_number,
                      'other_payer_secondary_identifier', CASE WHEN opn.other_payer_secondary_identifier IS NOT NULL THEN
                        (SELECT jsonb_agg(
                          jsonb_build_object(
                            'qualifier', opsi.qualifier,
                            'identifier', opsi.identifier
                          )
                        ) FROM unnest(opn.other_payer_secondary_identifier) opsi)
                      ELSE NULL END,
                      'other_payer_address', CASE WHEN opn.other_payer_address IS NOT NULL THEN
                        (SELECT jsonb_agg(
                          jsonb_build_object(
                            'address1', addr.address1,
                            'address2', addr.address2,
                            'city', addr.city,
                            'state', addr.state,
                            'postal_code', addr.postal_code
                          )
                        ) FROM unnest(opn.other_payer_address) addr)
                      ELSE NULL END
                    )
                  ) FROM unnest(cob.other_payer_name) opn)
                ELSE NULL END,
                
                -- Process claim_level_adjustments
                'claim_level_adjustments', CASE WHEN cob.claim_level_adjustments IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'adjustment_group_code', cla.adjustment_group_code,
                      'adjustment_details', CASE WHEN cla.adjustment_details IS NOT NULL THEN
                        (SELECT jsonb_agg(
                          jsonb_build_object(
                            'adjustment_reason_code', ad.adjustment_reason_code,
                            'adjustment_amount', ad.adjustment_amount,
                            'adjustment_quantity', ad.adjustment_quantity
                          )
                        ) FROM unnest(cla.adjustment_details) ad)
                      ELSE NULL END
                    )
                  ) FROM unnest(cob.claim_level_adjustments) cla)
                ELSE NULL END,
                
                -- Process providers (COB providers loop)
                'providers', CASE WHEN cob.providers IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'tabif_other_payer_billing_provider', prov.tabif_other_payer_billing_provider,
                      'tabif_other_payer_referring_provider', prov.tabif_other_payer_referring_provider,
                      'tabif_other_payer_rendering_provider', prov.tabif_other_payer_rendering_provider,
                      'tabif_other_payer_supervising_provider', prov.tabif_other_payer_supervising_provider,
                      'other_payer_billing_provider', CASE WHEN prov.other_payer_billing_provider IS NOT NULL THEN
                        (SELECT jsonb_agg(
                          jsonb_build_object(
                            'entity_type_qualifier', obp.entity_type_qualifier,
                            'other_payer_billing_provider_identifier', CASE WHEN obp.other_payer_billing_provider_identifier IS NOT NULL THEN
                              (SELECT jsonb_agg(
                                jsonb_build_object(
                                  'qualifier', id_qual.qualifier,
                                  'identifier', id_qual.identifier
                                )
                              ) FROM unnest(obp.other_payer_billing_provider_identifier) id_qual)
                            ELSE NULL END
                          )
                        ) FROM unnest(prov.other_payer_billing_provider) obp)
                      ELSE NULL END,
                      'other_payer_referring_provider', CASE WHEN prov.other_payer_referring_provider IS NOT NULL THEN
                        (SELECT jsonb_agg(
                          jsonb_build_object(
                            'other_payer_referring_provider_identifier', CASE WHEN orp.other_payer_referring_provider_identifier IS NOT NULL THEN
                              (SELECT jsonb_agg(
                                jsonb_build_object(
                                  'qualifier', id_qual.qualifier,
                                  'identifier', id_qual.identifier
                                )
                              ) FROM unnest(orp.other_payer_referring_provider_identifier) id_qual)
                            ELSE NULL END
                          )
                        ) FROM unnest(prov.other_payer_referring_provider) orp)
                      ELSE NULL END,
                      'other_payer_rendering_provider', CASE WHEN prov.other_payer_rendering_provider IS NOT NULL THEN
                        (SELECT jsonb_agg(
                          jsonb_build_object(
                            'entity_type_qualifier', orp.entity_type_qualifier,
                            'other_payer_rendering_provider_secondary_identifier', CASE WHEN orp.other_payer_rendering_provider_secondary_identifier IS NOT NULL THEN
                              (SELECT jsonb_agg(
                                jsonb_build_object(
                                  'qualifier', id_qual.qualifier,
                                  'identifier', id_qual.identifier
                                )
                              ) FROM unnest(orp.other_payer_rendering_provider_secondary_identifier) id_qual)
                            ELSE NULL END
                          )
                        ) FROM unnest(prov.other_payer_rendering_provider) orp)
                      ELSE NULL END,
                      'other_payer_supervising_provider', CASE WHEN prov.other_payer_supervising_provider IS NOT NULL THEN
                        (SELECT jsonb_agg(
                          jsonb_build_object(
                            'other_payer_supervising_provider_identifier', CASE WHEN osp.other_payer_supervising_provider_identifier IS NOT NULL THEN
                              (SELECT jsonb_agg(
                                jsonb_build_object(
                                  'qualifier', id_qual.qualifier,
                                  'identifier', id_qual.identifier
                                )
                              ) FROM unnest(osp.other_payer_supervising_provider_identifier) id_qual)
                            ELSE NULL END
                          )
                        ) FROM unnest(prov.other_payer_supervising_provider) osp)
                      ELSE NULL END
                    )
                  ) FROM unnest(cob.providers) prov)
                ELSE NULL END
              )
            ) FROM unnest(ci.other_subscriber_information) cob)
          ELSE NULL END,
          
          -- Process service_lines (extremely complex)
          'service_lines', CASE WHEN ci.service_lines IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'charge_no', sl.charge_no,
                'lock_sv', sl.lock_sv,
                'patient_id', sl.patient_id,
                'site_id', sl.site_id,
                'payer_id', sl.payer_id,
                'mm_calc_perc_sales_tax', sl.mm_calc_perc_sales_tax,
                'inventory_id', sl.inventory_id,
                'measurement_unit', sl.measurement_unit,
                'service_unit_count', sl.service_unit_count,
                'dx_id_1', sl.dx_id_1,
                'modifier_1', sl.modifier_1,
                'line_item_charge_amount', sl.line_item_charge_amount,
                'assigned_number', sl.assigned_number,
                'provider_control_number', sl.provider_control_number,
                'service_date', sl.service_date,
                'service_date_end', sl.service_date_end,
                'additional_notes', sl.additional_notes,
                'goal_rehab_or_discharge_plans', sl.goal_rehab_or_discharge_plans,
                'third_party_organization_notes', sl.third_party_organization_notes,
                
                -- Process service_line_reference_information
                'service_line_reference_information', CASE WHEN sl.service_line_reference_information IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'patient_id', slri.patient_id,
                      'prior_authorization', CASE WHEN slri.prior_authorization IS NOT NULL THEN
                        (SELECT jsonb_agg(
                          jsonb_build_object(
                            'patient_id', pa.patient_id,
                            'insurance_id', pa.insurance_id,
                            'pa_id', pa.pa_id,
                            'prior_authorization_or_referral_number', pa.prior_authorization_or_referral_number
                          )
                        ) FROM unnest(slri.prior_authorization) pa)
                      ELSE NULL END
                    )
                  ) FROM unnest(sl.service_line_reference_information) slri)
                ELSE NULL END,
                
                -- Process professional_service
                'professional_service', CASE WHEN sl.professional_service IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'patient_id', ps.patient_id,
                      'type', ps.type,
                      'charge_no', ps.charge_no,
                      'inventory_id', ps.inventory_id,
                      'description', ps.description,
                      'line_item_charge_amount', ps.line_item_charge_amount,
                      'measurement_unit', ps.measurement_unit,
                      'service_unit_count', ps.service_unit_count,
                      'place_of_service_code', ps.place_of_service_code,
                      'emergency_indicator', ps.emergency_indicator,
                      'epsdt_indicator', ps.epsdt_indicator,
                      'copay_status_code', ps.copay_status_code,
                      'dx_filter', CASE WHEN ps.dx_filter IS NOT NULL THEN to_jsonb(ps.dx_filter) ELSE NULL END,
                      'dx_id_1', ps.dx_id_1,
                      'dx_id_2', ps.dx_id_2,
                      'dx_id_3', ps.dx_id_3,
                      'dx_id_4', ps.dx_id_4,
                      'procedure_identifier', ps.procedure_identifier,
                      'procedure_code', ps.procedure_code,
                      'modifier_1', ps.modifier_1,
                      'modifier_2', ps.modifier_2,
                      'modifier_3', ps.modifier_3,
                      'modifier_4', ps.modifier_4
                    )
                  ) FROM unnest(sl.professional_service) ps)
                ELSE NULL END,
                
                -- Process drug_identification
                'drug_identification', CASE WHEN sl.drug_identification IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'inventory_id', di.inventory_id,
                      'service_id_qualifier', di.service_id_qualifier,
                      'national_drug_code', di.national_drug_code,
                      'national_drug_unit_count', di.national_drug_unit_count,
                      'measurement_unit_code', di.measurement_unit_code,
                      'link_sequence_number', di.link_sequence_number,
                      'pharmacy_prescription_number', di.pharmacy_prescription_number
                    )
                  ) FROM unnest(sl.drug_identification) di)
                ELSE NULL END,
                
                -- Process durable_medical_equipment_service
                'durable_medical_equipment_service', CASE WHEN sl.durable_medical_equipment_service IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'inventory_id', dme.inventory_id,
                      'days', dme.days,
                      'frequency_code', dme.frequency_code,
                      'rental_price', dme.rental_price,
                      'purchase_price', dme.purchase_price
                    )
                  ) FROM unnest(sl.durable_medical_equipment_service) dme)
                ELSE NULL END,
                
                -- Process durable_medical_equipment_certificate_of_medical_necessity
                'durable_medical_equipment_certificate_of_medical_necessity', CASE WHEN sl.durable_medical_equipment_certificate_of_medical_necessity IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'attachment_transmission_code', dmecmn.attachment_transmission_code
                    )
                  ) FROM unnest(sl.durable_medical_equipment_certificate_of_medical_necessity) dmecmn)
                ELSE NULL END,
                
                -- Process durable_medical_equipment_certification
                'durable_medical_equipment_certification', CASE WHEN sl.durable_medical_equipment_certification IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'send_cert', dmec.send_cert,
                      'certification_type_code', dmec.certification_type_code,
                      'durable_medical_equipment_duration_in_months', dmec.durable_medical_equipment_duration_in_months
                    )
                  ) FROM unnest(sl.durable_medical_equipment_certification) dmec)
                ELSE NULL END,

                -- Process service_line_date_information
                'service_line_date_information', CASE WHEN sl.service_line_date_information IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'prescription_date', sld.prescription_date,
                      'shipped_date', sld.shipped_date
                    )
                  ) FROM unnest(sl.service_line_date_information) sld)
                ELSE NULL END,
                
                -- Process line_adjudication_information
                'line_adjudication_information', CASE WHEN sl.line_adjudication_information IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'other_payer_primary_identifier', lai.other_payer_primary_identifier,
                      'procedure_code', lai.procedure_code,
                      'service_id_qualifier', lai.service_id_qualifier,
                      'procedure_code_description', lai.procedure_code_description,
                      'paid_service_unit_count', lai.paid_service_unit_count,
                      'adjudication_or_payment_date', lai.adjudication_or_payment_date,
                      'service_line_paid_amount', lai.service_line_paid_amount,
                      'remaining_patient_liability', lai.remaining_patient_liability,
                      'modifier_1', lai.modifier_1,
                      'modifier_2', lai.modifier_2,
                      'modifier_3', lai.modifier_3,
                      'modifier_4', lai.modifier_4,
                      'claim_adjustment_information', CASE WHEN lai.claim_adjustment_information IS NOT NULL THEN
                        (SELECT jsonb_agg(
                          jsonb_build_object(
                            'adjustment_group_code', cai.adjustment_group_code,
                            'adjustment_details', CASE WHEN cai.adjustment_details IS NOT NULL THEN
                              (SELECT jsonb_agg(
                                jsonb_build_object(
                                  'adjustment_reason_code', ad.adjustment_reason_code,
                                  'adjustment_amount', ad.adjustment_amount,
                                  'adjustment_quantity', ad.adjustment_quantity
                                )
                              ) FROM unnest(cai.adjustment_details) ad)
                            ELSE NULL END
                          )
                        ) FROM unnest(lai.claim_adjustment_information) cai)
                      ELSE NULL END
                    )
                  ) FROM unnest(sl.line_adjudication_information) lai)
                ELSE NULL END,
                
                -- Process service_line_supplemental_information
                'service_line_supplemental_information', CASE WHEN sl.service_line_supplemental_information IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'document_id', slsi.document_id,
                      'attachment_report_type_code', slsi.attachment_report_type_code,
                      'attachment_transmission_code', slsi.attachment_transmission_code,
                      'attachment_control_number', slsi.attachment_control_number
                    )
                  ) FROM unnest(sl.service_line_supplemental_information) slsi)
                ELSE NULL END,
                
                -- Process file_information
                'file_information', CASE WHEN sl.file_information IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'file', fi.file,
                      'comments', fi.comments
                    )
                  ) FROM unnest(sl.file_information) fi)
                ELSE NULL END
              )
            ) FROM unnest(ci.service_lines) sl)
          ELSE NULL END,
          
          -- Process claim_supplemental_information
          'claim_supplemental_information', CASE WHEN ci.claim_supplemental_information IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'patient_id', csi.patient_id,
                'insurance_id', csi.insurance_id,
                'claim_number', csi.claim_number,
                'pa_id', csi.pa_id,
                'prior_authorization_number', csi.prior_authorization_number,
                'medical_record_number', csi.medical_record_number
              )
            ) FROM unnest(ci.claim_supplemental_information) csi)
          ELSE NULL END,
          
          -- Process claim_info_other
          'claim_info_other', CASE WHEN ci.claim_info_other IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'site_id', cio.site_id,
                'patient_id', cio.patient_id,
                'payer_id', cio.payer_id,
                'mm_send_contract_pricing', cio.mm_send_contract_pricing,
                'tabif_dates', cio.tabif_dates,
                'tabif_contract', cio.tabif_contract,
                
                -- Process claim_date_information
                'claim_date_information', CASE WHEN cio.claim_date_information IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'symptom_date', cdi.symptom_date,
                      'accident_date', cdi.accident_date
                    )
                  ) FROM unnest(cio.claim_date_information) cdi)
                ELSE NULL END,
                
                -- Process claim_contract_information
                'claim_contract_information', CASE WHEN cio.claim_contract_information IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'contract_type_code', cci.contract_type_code,
                      'contract_amount', cci.contract_amount,
                      'contract_percentage', cci.contract_percentage,
                      'contract_code', cci.contract_code,
                      'terms_discount_percentage', cci.terms_discount_percentage,
                      'contract_version_identifier', cci.contract_version_identifier
                    )
                  ) FROM unnest(cio.claim_contract_information) cci)
                ELSE NULL END,
                
                -- Process file_information_list
                'file_information_list', CASE WHEN cio.file_information_list IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'file', fil.file,
                      'comments', fil.comments
                    )
                  ) FROM unnest(cio.file_information_list) fil)
                ELSE NULL END
              )
            ) FROM unnest(ci.claim_info_other) cio)
          ELSE NULL END
        )
      ) INTO v_claim_information_json
      FROM unnest(p_claim_record.claim_information) ci;
      RAISE LOG 'claim_information segment processed for claim_no: %', p_claim_record.claim_no;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in claim_information segment: %', SQLERRM;
      v_claim_information_json := NULL;
    END;
  ELSE
    RAISE LOG 'No claim_information segment present for claim_no: %', p_claim_record.claim_no;
  END IF;

  -- Process providers segment (simplified due to complexity)
  IF p_claim_record.providers IS NOT NULL THEN
    RAISE LOG 'Processing providers segment for claim_no: %', p_claim_record.claim_no;
    BEGIN
      SELECT jsonb_agg(
        jsonb_build_object(
          'patient_id', prov.patient_id,
          'site_id', prov.site_id,
          'tabif_billing', prov.tabif_billing,
          'tabif_referring', prov.tabif_referring,
          'tabif_ordering', prov.tabif_ordering,
          'tabif_rendering', prov.tabif_rendering,
          'tabif_supervising', prov.tabif_supervising,
          
          -- Process billing_provider
          'billing_provider', CASE WHEN prov.billing_provider IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'site_id', bp.site_id,
                'provider_type', bp.provider_type,
                'organization_name', bp.organization_name,
                'npi', bp.npi,
                'employer_identification_number', bp.employer_identification_number,
                'commercial_number', bp.commercial_number,
                'state_license_number', bp.state_license_number,
                'contact_information', CASE WHEN bp.contact_information IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'name', ci.name,
                      'email', ci.email,
                      'phone_number', ci.phone_number,
                      'fax_number', ci.fax_number
                    )
                  ) FROM unnest(bp.contact_information) ci)
                ELSE NULL END,
                'address', CASE WHEN bp.address IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'address1', addr.address1,
                      'address2', addr.address2,
                      'city', addr.city,
                      'state', addr.state,
                      'postal_code', addr.postal_code
                    )
                  ) FROM unnest(bp.address) addr)
                ELSE NULL END
              )
            ) FROM unnest(prov.billing_provider) bp)
          ELSE NULL END,
          
          -- Process referring_provider
          'referring_provider', CASE WHEN prov.referring_provider IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'patient_prescriber_id', rp.patient_prescriber_id,
                'provider_type', rp.provider_type,
                'first_name', rp.first_name,
                'last_name', rp.last_name,
                'middle_name', rp.middle_name,
                'suffix', rp.suffix,
                'npi', rp.npi,
                'state_license_number', rp.state_license_number,
                'taxonomy_id', rp.taxonomy_id,
                'secondary_identifier', CASE WHEN rp.secondary_identifier IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'qualifier', si.qualifier,
                      'identifier', si.identifier
                    )
                  ) FROM unnest(rp.secondary_identifier) si)
                ELSE NULL END
              )
            ) FROM unnest(prov.referring_provider) rp)
          ELSE NULL END,
          
          -- Process ordering_provider
          'ordering_provider', CASE WHEN prov.ordering_provider IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'patient_prescriber_id', op.patient_prescriber_id,
                'provider_type', op.provider_type,
                'first_name', op.first_name,
                'last_name', op.last_name,
                'middle_name', op.middle_name,
                'suffix', op.suffix,
                'npi', op.npi,
                'state_license_number', op.state_license_number,
                'taxonomy_id', op.taxonomy_id,
                'secondary_identifier', CASE WHEN op.secondary_identifier IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'qualifier', si.qualifier,
                      'identifier', si.identifier
                    )
                  ) FROM unnest(op.secondary_identifier) si)
                ELSE NULL END,
                'address', CASE WHEN op.address IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'address1', addr.address1,
                      'address2', addr.address2,
                      'city', addr.city,
                      'state', addr.state,
                      'postal_code', addr.postal_code
                    )
                  ) FROM unnest(op.address) addr)
                ELSE NULL END,
                'contact_information', CASE WHEN op.contact_information IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'name', ci.name,
                      'email', ci.email,
                      'phone_number', ci.phone_number,
                      'fax_number', ci.fax_number
                    )
                  ) FROM unnest(op.contact_information) ci)
                ELSE NULL END
              )
            ) FROM unnest(prov.ordering_provider) op)
          ELSE NULL END,
          
          -- Process rendering_provider
          'rendering_provider', CASE WHEN prov.rendering_provider IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'patient_prescriber_id', rp.patient_prescriber_id,
                'provider_type', rp.provider_type,
                'first_name', rp.first_name,
                'last_name', rp.last_name,
                'middle_name', rp.middle_name,
                'suffix', rp.suffix,
                'npi', rp.npi,
                'state_license_number', rp.state_license_number,
                'taxonomy_id', rp.taxonomy_id,
                'secondary_identifier', CASE WHEN rp.secondary_identifier IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'qualifier', si.qualifier,
                      'identifier', si.identifier
                    )
                  ) FROM unnest(rp.secondary_identifier) si)
                ELSE NULL END
              )
            ) FROM unnest(prov.rendering_provider) rp)
          ELSE NULL END,
          
          -- Process supervising_provider
          'supervising_provider', CASE WHEN prov.supervising_provider IS NOT NULL THEN
            (SELECT jsonb_agg(
              jsonb_build_object(
                'patient_prescriber_id', sp.patient_prescriber_id,
                'provider_type', sp.provider_type,
                'first_name', sp.first_name,
                'last_name', sp.last_name,
                'middle_name', sp.middle_name,
                'suffix', sp.suffix,
                'npi', sp.npi,
                'state_license_number', sp.state_license_number,
                'taxonomy_id', sp.taxonomy_id,
                'secondary_identifier', CASE WHEN sp.secondary_identifier IS NOT NULL THEN
                  (SELECT jsonb_agg(
                    jsonb_build_object(
                      'qualifier', si.qualifier,
                      'identifier', si.identifier
                    )
                  ) FROM unnest(sp.secondary_identifier) si)
                ELSE NULL END
              )
            ) FROM unnest(prov.supervising_provider) sp)
          ELSE NULL END
        )
      ) INTO v_providers_json
      FROM unnest(p_claim_record.providers) prov;
      RAISE LOG 'Providers segment processed for claim_no: %', p_claim_record.claim_no;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in providers segment: %', SQLERRM;
      v_providers_json := NULL;
    END;
  ELSE
    RAISE LOG 'No providers segment present for claim_no: %', p_claim_record.claim_no;
  END IF;

  -- Create the full JSON object with all fields
  RAISE LOG 'Building final JSON object for claim_no: %', p_claim_record.claim_no;
  v_result := jsonb_build_object(
    'parent_claim_no', p_claim_record.parent_claim_no,
    'claim_no', p_claim_record.claim_no,
    'usage_indicator', p_claim_record.usage_indicator,
    'service_date', p_claim_record.service_date,
    'site_id', p_claim_record.site_id,
    'patient_id', p_claim_record.patient_id,
    'insurance_id', p_claim_record.insurance_id,
    'payer_id', p_claim_record.payer_id,
    'organization_name', p_claim_record.organization_name,
    'dependent_required', p_claim_record.dependent_required,
    'control_number', p_claim_record.control_number,
    'status', p_claim_record.status,
    'substatus_id', p_claim_record.substatus_id,
    'trading_partner_service_id', p_claim_record.trading_partner_service_id,
    'trading_partner_name', p_claim_record.trading_partner_name,
    'expected', p_claim_record.expected,
    'billed', p_claim_record.billed,
    
    -- Include processed segment data
    'receiver', v_receiver_json,
    'submitter', v_submitter_json,
    'pay_to_address', v_pay_to_address_json,
    'subscriber', v_subscriber_json,
    'dependent', v_dependent_json,
    'claim_information', v_claim_information_json,
    'providers', v_providers_json
  );

  -- Strip null values for cleaner JSON
  v_result := jsonb_strip_nulls(v_result);

  RAISE LOG 'mm_record_to_json completed for claim_no: %', p_claim_record.claim_no;
  
  RETURN v_result;
END;
$BODY$ LANGUAGE plpgsql IMMUTABLE;
