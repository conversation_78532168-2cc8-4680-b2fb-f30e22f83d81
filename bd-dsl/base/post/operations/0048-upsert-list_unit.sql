INSERT INTO
    form_list_unit (
        uom_mstr_id,
        code,
        name,
        dose_unit,
        base_unit,
        rate_unit,
        unit_components,
        unit_basis,
        unit_component_1,
        unit_component_2,
        unit_component_3,
        patient_param_required,
        active,
        allow_sync,
        auto_name
    )
SELECT
    uom_mstr_id,
    uom_stds_org_abbr,
    uom_mstr_desc,
    CASE
        WHEN dose_ind = 1 THEN 'Yes'
        ELSE 'No'
    END AS dose_unit,
    CASE
        WHEN ratio_ind = 0 THEN 'Yes'
        ELSE 'No'
    END AS base_unit,
    CASE
        WHEN rate_ind = 1 THEN 'Yes'
        ELSE 'No'
    END AS rate_unit,
    ratio_ind + 1 AS unit_components,
    d.uom_type_cd_desc AS unit_basis,
    uom_mstr_component1_id,
    uom_mstr_component2_id,
    uom_mstr_component3_id,
    CASE WHEN f.patient_param_req_cd = 1 THEN 'Weight (Kg)' 
    WHEN f.patient_param_req_cd = 2 THEN 'BSA (m²)'
    WHEN f.patient_param_req_cd = 3 THEN 'Lesions'
    WHEN f.patient_param_req_cd = 4 THEN 'cm² per Lesion'
    WHEN f.patient_param_req_cd = 5 THEN '1.73 m²'
    WHEN f.patient_param_req_cd = 6 THEN 'grams of carbohydrate'
    ELSE NULL END AS patient_param_required,
    'Yes' as active,
    'Yes' AS allow_sync,
    uom_mstr_desc AS auto_name
FROM
    form_list_fdb_unit_master f
    LEFT JOIN form_list_fdb_uom_desc d ON f.uom_type_cd = d.uom_type_cd 
    WHERE f.uom_mstr_desc NOT LIKE '%(Do Not Use)%'
    ON CONFLICT (code) DO
UPDATE
SET
    uom_mstr_id = EXCLUDED.uom_mstr_id,
    name = EXCLUDED.name,
    dose_unit = EXCLUDED.dose_unit,
    base_unit = EXCLUDED.base_unit,
    rate_unit = EXCLUDED.rate_unit,
    unit_components = EXCLUDED.unit_components,
    unit_basis = EXCLUDED.unit_basis,
    unit_component_1 = EXCLUDED.unit_component_1,
    unit_component_2 = EXCLUDED.unit_component_2,
    unit_component_3 = EXCLUDED.unit_component_3,
    patient_param_required = EXCLUDED.patient_param_required,
    auto_name = EXCLUDED.auto_name;