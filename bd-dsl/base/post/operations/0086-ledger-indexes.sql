-- ===================================
-- 8. INDEXES AND OPTIMIZATIONS
-- ===================================

-- Add indexes on form_ledger_finance for better performance
CREATE INDEX IF NOT EXISTS idx_ledger_finance_source ON form_ledger_finance(source_form, source_id);
CREATE INDEX IF NOT EXISTS idx_ledger_finance_invoice_id ON form_ledger_finance(invoice_id);
CREATE INDEX IF NOT EXISTS idx_ledger_finance_charge_line_id ON form_ledger_finance(charge_line_id);
CREATE INDEX IF NOT EXISTS idx_ledger_finance_account_id ON form_ledger_finance(account_id);
CREATE INDEX IF NOT EXISTS idx_ledger_finance_post_datetime ON form_ledger_finance(post_datetime);
CREATE INDEX IF NOT EXISTS idx_ledger_finance_account_type ON form_ledger_finance(account_type);

-- Add indexes on billing_transaction_log
CREATE INDEX IF NOT EXISTS idx_billing_transaction_log_source ON billing_transaction_log(source_form, source_id);
CREATE INDEX IF NOT EXISTS idx_billing_transaction_log_transaction_id ON billing_transaction_log(transaction_id);
CREATE INDEX IF NOT EXISTS idx_billing_transaction_log_status ON billing_transaction_log(status);
CREATE INDEX IF NOT EXISTS idx_billing_transaction_log_start_time ON billing_transaction_log(start_time);

-- Add indexes on billing_error_log
CREATE INDEX IF NOT EXISTS idx_billing_error_log_table_name ON billing_error_log(table_name);
CREATE INDEX IF NOT EXISTS idx_billing_error_log_error_type ON billing_error_log(error_type);

-- Add indexes on form_billing_invoice
CREATE INDEX IF NOT EXISTS idx_billing_invoice_invoice_no ON form_billing_invoice(invoice_no);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_master_invoice_no ON form_billing_invoice(master_invoice_no);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_parent_invoice_no ON form_billing_invoice(parent_invoice_no);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_post_datetime ON form_billing_invoice(post_datetime);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_patient_id ON form_billing_invoice(patient_id);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_payer_id ON form_billing_invoice(payer_id);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_status ON form_billing_invoice(status);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_close_no ON form_billing_invoice(close_no);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_revenue_accepted ON form_billing_invoice(revenue_accepted);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_billing_method_id ON form_billing_invoice(billing_method_id);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_void ON form_billing_invoice(void);

-- Add indexes on form_billing_closing
CREATE INDEX IF NOT EXISTS idx_billing_closing_close_no ON form_billing_closing(close_no);
CREATE INDEX IF NOT EXISTS idx_billing_closing_start_date ON form_billing_closing(start_date);
CREATE INDEX IF NOT EXISTS idx_billing_closing_end_date ON form_billing_closing(end_date);
CREATE INDEX IF NOT EXISTS idx_billing_closing_locked_period ON form_billing_closing(locked_period);
CREATE INDEX IF NOT EXISTS idx_billing_closing_void ON form_billing_closing(void);

-- Add indexes on form_ledger_charge_line
CREATE INDEX IF NOT EXISTS idx_ledger_charge_line_charge_no ON form_ledger_charge_line(charge_no);
CREATE INDEX IF NOT EXISTS idx_ledger_charge_line_invoice_no ON form_ledger_charge_line(invoice_no);
CREATE INDEX IF NOT EXISTS idx_ledger_charge_line_revenue_accepted_posted ON form_ledger_charge_line(revenue_accepted_posted);
CREATE INDEX IF NOT EXISTS idx_ledger_charge_line_void ON form_ledger_charge_line(void);

-- Add combined indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_ledger_finance_period_account 
ON form_ledger_finance(post_datetime, account_type, account_id);

CREATE INDEX IF NOT EXISTS idx_billing_invoice_active 
ON form_billing_invoice(void, deleted, archived, status);
