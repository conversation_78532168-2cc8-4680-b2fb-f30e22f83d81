
CREATE OR REPLACE FUNCTION get_patient_responsibility_data(
    root_claim TEXT,
    p_insurance_id INTEGER,
    p_site_id INTEGER
) RETURNS ncpdp_cob_ptresp[] AS $BODY$
DECLARE
    v_start_time timestamp;
    v_execution_time interval;
    v_error_message text;
    v_params jsonb;
    v_result ncpdp_cob_ptresp[];
    v_is_copay_card boolean := FALSE;
    v_pt_res_codes text[];
BEGIN
    -- Record start time
    v_start_time := clock_timestamp();

    -- Build parameters JSON for logging
    v_params := jsonb_build_object(
        'root_claim', root_claim,
        'insurance_id', p_insurance_id,
        'site_id', p_site_id
    );

    BEGIN  -- Start exception block
        -- Log function call
        PERFORM log_billing_function(
            'get_patient_responsibility_data'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            NULL::text,
            clock_timestamp() - v_start_time
        );

        -- Validate required parameters
        IF root_claim IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Parent Claim # cannot be null',
                'Validating required parameters in get_patient_responsibility_data',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                jsonb_build_object(
                    'function_name', 'get_patient_responsibility_data',
                    'root_claim', root_claim
                )
            );
            RAISE EXCEPTION 'Parent Claim # cannot be null';
        END IF;

        IF p_insurance_id IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Insurance ID cannot be null',
                'Validating required parameters in get_patient_responsibility_data',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                jsonb_build_object(
                    'function_name', 'get_patient_responsibility_data',
                    'root_claim', root_claim
                )
            );
            RAISE EXCEPTION 'Insurance ID cannot be null';
        END IF;

        IF p_site_id IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Site ID cannot be null',
                'Validating required parameters in get_patient_responsibility_data',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                jsonb_build_object(
                    'function_name', 'get_patient_responsibility_data',
                    'root_claim', root_claim
                )
            );
            RAISE EXCEPTION 'Site ID cannot be null';
        END IF;

        -- Check if this is a copay card
        SELECT (pyr.type_id = 'COPAY'), res_codes.codes INTO v_is_copay_card, v_pt_res_codes
        FROM form_payer pyr
        LEFT JOIN LATERAL (
        	SELECT ARRAY_AGG(ecl.code::text) as codes
        	FROM gr_form_payer_ncpdp_pt_res_codes_to_list_ncpdp_ecl_id grecl 
        	INNER JOIN form_list_ncpdp_ecl ecl ON ecl.code = grecl.form_list_ncpdp_ecl_fk
        	WHERE grecl.form_payer_fk = pyr.id AND ecl.field = '351-NP'
        ) res_codes ON true
        INNER JOIN form_patient_insurance pi ON pi.payer_id = pyr.id
        WHERE pi.id = p_insurance_id
        AND pyr.archived IS NOT TRUE
        AND pyr.deleted IS NOT TRUE
        AND pi.archived IS NOT TRUE
        AND pi.deleted IS NOT TRUE;
        
        RAISE LOG 'Is copay card: %, PT Resp Codes: %', v_is_copay_card, v_pt_res_codes;
        
        -- If no specific codes are defined but this is a copay card, default to '06' (Patient Pay Amount)
        IF v_pt_res_codes IS NULL OR array_length(v_pt_res_codes, 1) = 0 THEN
            IF v_is_copay_card THEN
                v_pt_res_codes := ARRAY['06']; -- Default for copay cards
            ELSE
                v_pt_res_codes := ARRAY['01', '02', '04', '05', '06', '07', '08', '10', '11', '12', '13']; -- Include all common codes
            END IF;
        END IF;

        -- Get claim hierarchy and insurance info
        WITH claim_hierarchy AS (
            SELECT * FROM get_claim_hierarchy(root_claim)
        ),
        insurance_info AS (
            SELECT * FROM get_insurance_claim_settings(p_insurance_id, p_site_id, NULL::integer)
        ),
        -- Get all patient responsibility amounts based on the payer's ncpdp_pt_res_codes setting
        all_pt_resp_amounts AS (
            -- Patient paid amount - include if '06' is in the list or for copay cards
            SELECT 
                ch.claim_no::text as claim_no,
                '06'::text AS pt_resp_amt_qualifier,
                ch.pt_pay_amt::numeric AS pt_resp_amt,
                1::integer AS rank,
                ch.depth
            FROM claim_hierarchy ch
            WHERE ch.pt_pay_amt > 0 
                AND ('06' = ANY(v_pt_res_codes) OR v_is_copay_card)

            UNION ALL
            
            -- Only include the following if explicitly in the pt_res_codes array
            
            -- Deductible amount
            SELECT 
                ch.claim_no::text as claim_no,
                '01'::text AS pt_resp_amt_qualifier,
                ch.amt_apld_ded::numeric AS pt_resp_amt,
                0::integer AS rank,
                ch.depth
            FROM claim_hierarchy ch
            WHERE ch.amt_apld_ded > 0 
                AND '01' = ANY(v_pt_res_codes)

            UNION ALL
            
            -- Exceeded benefit maximum
            SELECT 
                ch.claim_no::text as claim_no,
                '04'::text AS pt_resp_amt_qualifier,
                ch.amt_exd_ben_max::numeric AS pt_resp_amt,
                0::integer AS rank,
                ch.depth
            FROM claim_hierarchy ch
            WHERE ch.amt_exd_ben_max > 0 
                AND '04' = ANY(v_pt_res_codes)

            UNION ALL
            
            -- Coinsurance amount
            SELECT 
                ch.claim_no::text as claim_no,
                '07'::text AS pt_resp_amt_qualifier,
                ch.coinsur_amt::numeric AS pt_resp_amt,
                0::integer AS rank,
                ch.depth
            FROM claim_hierarchy ch
            WHERE ch.coinsur_amt > 0 
                AND '07' = ANY(v_pt_res_codes)

            UNION ALL
            
            -- Professional fee
            SELECT 
                ch.claim_no::text as claim_no,
                '13'::text AS pt_resp_amt_qualifier,
                ch.pro_fee_amt::numeric AS pt_resp_amt,
                0::integer AS rank,
                ch.depth
            FROM claim_hierarchy ch
            WHERE ch.pro_fee_amt > 0 
                AND '13' = ANY(v_pt_res_codes)

            UNION ALL
            
            -- Provider selection amount
            SELECT 
                ch.claim_no::text as claim_no,
                '10'::text AS pt_resp_amt_qualifier,
                ch.amt_attr_prov_sel::numeric AS pt_resp_amt,
                0::integer AS rank,
                ch.depth
            FROM claim_hierarchy ch
            WHERE ch.amt_attr_prov_sel > 0 
                AND '10' = ANY(v_pt_res_codes)

            UNION ALL
            
            -- Product selection amount
            SELECT 
                ch.claim_no::text as claim_no,
                '02'::text AS pt_resp_amt_qualifier,
                ch.amt_attr_prod_sel::numeric AS pt_resp_amt,
                0::integer AS rank,
                ch.depth
            FROM claim_hierarchy ch
            WHERE ch.amt_attr_prod_sel > 0 
                AND '02' = ANY(v_pt_res_codes)

            UNION ALL
            
            -- Non-formulary amount
            SELECT 
                ch.claim_no::text as claim_no,
                '08'::text AS pt_resp_amt_qualifier,
                ch.amt_attr_nonformulary::numeric AS pt_resp_amt,
                0::integer AS rank,
                ch.depth
            FROM claim_hierarchy ch
            WHERE ch.amt_attr_nonformulary > 0 
                AND '08' = ANY(v_pt_res_codes)

            UNION ALL
            
            -- Brand non-formulary amount
            SELECT 
                ch.claim_no::text as claim_no,
                '11'::text AS pt_resp_amt_qualifier,
                ch.amt_attr_brd_nonformulary::numeric AS pt_resp_amt,
                0::integer AS rank,
                ch.depth
            FROM claim_hierarchy ch
            WHERE ch.amt_attr_brd_nonformulary > 0 
                AND '11' = ANY(v_pt_res_codes)

            UNION ALL
            
            -- Coverage gap amount
            SELECT 
                ch.claim_no::text as claim_no,
                '12'::text AS pt_resp_amt_qualifier,
                ch.amt_coverage_gap::numeric AS pt_resp_amt,
                0::integer AS rank,
                ch.depth
            FROM claim_hierarchy ch
            WHERE ch.amt_coverage_gap > 0 
                AND '12' = ANY(v_pt_res_codes)
        ),
        -- CTE to gather the filtered row data with column names matching the type
        filtered_resp_data AS (
            SELECT 
                ara.pt_resp_amt_qualifier::text AS pt_resp_amt_qualifier,
                ara.pt_resp_amt::numeric AS pt_resp_amt
            FROM all_pt_resp_amounts ara
            WHERE ara.pt_resp_amt > 0
            ORDER BY ara.rank DESC, ara.depth, ara.pt_resp_amt_qualifier
            LIMIT 25  -- Maximum of 25 responsibility amounts allowed
        )
        -- Create the final result as an array of the custom type
        SELECT ARRAY_AGG(resp::ncpdp_cob_ptresp) INTO v_result
        FROM (
            SELECT pt_resp_amt_qualifier, pt_resp_amt
            FROM filtered_resp_data
        ) resp;

        -- Validate result - if no results but it's a copay card, create a default entry with patient pay amount
        IF (v_result IS NULL OR array_length(v_result, 1) = 0) AND v_is_copay_card THEN
            -- For copay cards, pull the patient pay amount from the primary claim
            WITH claim_hierarchy AS (
                SELECT * FROM get_claim_hierarchy(root_claim)
            ),
            primary_claim AS (
                SELECT pt_pay_amt::numeric as pt_pay_amt
                FROM claim_hierarchy
                WHERE depth = 0
                LIMIT 1
            ),
            default_resp_data AS (
                SELECT 
                    '06'::text AS pt_resp_amt_qualifier,
                    COALESCE(pc.pt_pay_amt, 0)::numeric AS pt_resp_amt
                FROM primary_claim pc
            )
            SELECT ARRAY_AGG(resp::ncpdp_cob_ptresp) INTO v_result
            FROM (
                SELECT pt_resp_amt_qualifier::text as pt_resp_amt_qualifier,
                pt_resp_amt::numeric as pt_resp_amt
                FROM default_resp_data
            ) resp;
        END IF;

        -- Final null check
        IF v_result IS NULL THEN
            v_result := ARRAY[]::ncpdp_cob_ptresp[];
        END IF;

        -- Log successful completion
        PERFORM log_billing_function(
            'get_patient_responsibility_data'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            NULL::text,
            clock_timestamp() - v_start_time
        );

        RETURN v_result;

    EXCEPTION WHEN OTHERS THEN
        -- Log error
        v_error_message := SQLERRM;
        
        -- Log to billing error log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            v_error_message,
            'Exception in get_patient_responsibility_data',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );

        -- Log to NCPDP function log
        PERFORM log_billing_function(
            'get_patient_responsibility_data'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            v_error_message::text,
            clock_timestamp() - v_start_time
        );
        RAISE;
    END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;