CREATE OR REPLACE VIEW vw_dispense_log AS
WITH inventory_items AS (
    SELECT
        li.patient_id,
        li.inventory_id,
        li.site_id,
        li.ticket_item_no,
        SUM(li.quantity) as raw_quantity,
        li.description,
        NULL as lot_no,
        NULL as serial_no,
        NULL as rx_no
    FROM form_ledger_inventory li
    WHERE transaction_type = 'Dispense' 
    AND 
    NOT EXISTS (SELECT 1 FROM form_ledger_serial ls
                WHERE ls.inventory_id = li.inventory_id
                AND ls.ticket_item_no = li.ticket_item_no
                AND ls.transaction_type = 'Dispense')
    AND NOT EXISTS (SELECT 1 FROM form_ledger_lot ll
                WHERE ll.inventory_id = li.inventory_id
                AND ll.ticket_item_no = li.ticket_item_no
                AND ll.transaction_type = 'Dispense')
    GROUP BY li.patient_id, li.inventory_id, li.site_id, li.ticket_item_no, li.description
    HAVING SUM(li.quantity) < 0
),
lot_items AS (
    SELECT
        ll.patient_id,
        ll.inventory_id,
        ll.site_id,
        ll.ticket_item_no,
        SUM(ll.quantity) as raw_quantity,
        ll.description,
        ll.lot_no,
        NULL as serial_no,
        ll.rx_no
    FROM form_ledger_lot ll
    WHERE transaction_type = 'Dispense' AND
    NOT EXISTS (SELECT 1 FROM form_ledger_serial ls
                WHERE ls.inventory_id = ll.inventory_id
                AND ls.ticket_item_no = ll.ticket_item_no
                AND ls.transaction_type = 'Dispense')
    GROUP BY ll.patient_id, ll.inventory_id, ll.site_id, ll.ticket_item_no, ll.description, ll.lot_no, ll.rx_no
    HAVING SUM(ll.quantity) < 0
),
serial_items AS (
    SELECT
        ls.patient_id,
        ls.inventory_id,
        ls.site_id,
        ls.ticket_item_no,
        SUM(ls.quantity) as raw_quantity,
        ls.description,
        ls.lot_no,
        ls.serial_no,
        ls.rx_no
    FROM form_ledger_serial ls
    WHERE transaction_type = 'Dispense' 
    GROUP BY ls.patient_id, ls.inventory_id, ls.site_id, ls.ticket_item_no, ls.description, ls.lot_no, ls.serial_no, ls.rx_no
    HAVING SUM(ls.quantity) < 0
),
combined_dispense_log AS (
    SELECT * FROM inventory_items
    UNION ALL
    SELECT * FROM lot_items
    UNION ALL
    SELECT * FROM serial_items
)
SELECT
   dt.id as query_id,
   'careplan_delivery_tick' as query_form,
   dl.patient_id,
   dl.site_id,
   dl.inventory_id,
   dt.ticket_no as ticket_no,
   pt.lastname as "Last Name",
   pt.firstname as "First Name",
   dl.inventory_id as item,
   inv.auto_name as item_auto_name,
   dl.ticket_item_no,
   ABS(dl.raw_quantity) as raw_quantity,
   format_numeric(ABS(dl.raw_quantity::numeric)::numeric) as quantity,
   dl.description,
   dl.lot_no,
   dl.serial_no,
   dl.rx_no,
   TO_CHAR(dt.delivery_date, 'MM/DD/YYYY') as "Delivery Date",
   st.id as site,
   st.auto_name as site_auto_name,
   CASE 
   WHEN ver IS NOT NULL THEN COALESCE(ver.displayname, CONCAT(ver.firstname, ' ', ver.lastname))
   ELSE NULL
   END as "Verified By",
   CASE 
   WHEN con IS NOT NULL THEN COALESCE(con.displayname, CONCAT(con.firstname, ' ', con.lastname))
   ELSE NULL
   END as "Confirmed By"
   FROM combined_dispense_log dl
   INNER JOIN form_inventory inv ON inv.id = dl.inventory_id
   INNER JOIN form_careplan_dt_item dti ON dti.ticket_item_no = dl.ticket_item_no
   INNER JOIN form_careplan_delivery_tick dt ON dti.ticket_no = dt.ticket_no
   INNER JOIN form_patient pt ON pt.id = dl.patient_id
   INNER JOIN form_site st ON st.id = dl.site_id
   LEFT JOIN form_user ver ON ver.id = COALESCE(dt.tech_verified_by, dt.verified_by)
   LEFT JOIN form_user con ON con.id = dt.confirmed_by;