CREATE OR REPLACE FUNCTION crx_upsert_list_unit_conversion() RETURNS text AS $$
BEGIN
    INSERT INTO form_list_unit_conversion (
        to_unit_id, to_uom_id, from_unit_id, from_unit_basis, from_unit_comp_cnt, 
        from_unit_comp_1_id, from_unit_comp_1_uom_id, from_unit_comp_2_id, from_unit_comp_3_id, component_multiplier, auto_name
    )
    SELECT 
        lu_to.code AS to_unit_id,
        fdb.to_uom_mstr_id AS to_uom_id,
        lu_from.code AS from_unit_id,
        CASE 
            WHEN fdb.to_uom_mstr_id = 482 THEN 'Quantity'
            WHEN fdb.to_uom_mstr_id = 368 THEN 'Weight'
            WHEN fdb.to_uom_mstr_id = 380 THEN 'Volume'
            ELSE NULL 
        END AS from_unit_basis,
        CASE 
            WHEN lu_from.unit_component_3 IS NOT NULL THEN 3
            WHEN lu_from.unit_component_2 IS NOT NULL THEN 2
            WHEN lu_from.unit_component_1 IS NOT NULL THEN 1
            ELSE 0 
        END AS from_unit_comp_cnt,
        uc1.code AS from_unit_comp_1_id,
        uc1.uom_mstr_id AS from_unit_comp_1_uom_id,
        uc2.code AS from_unit_comp_2_id,
        uc3.code AS from_unit_comp_3_id,
        fdb.uom_conversion_factor AS component_multiplier,
        CONCAT(lu_to.auto_name, ' to ', lu_from.auto_name) AS auto_name
    FROM 
        form_list_fdb_unit_conversion fdb
    JOIN 
        form_list_unit lu_to ON lu_to.uom_mstr_id = fdb.to_uom_mstr_id
    JOIN 
        form_list_unit lu_from ON lu_from.uom_mstr_id = fdb.from_uom_mstr_id AND lu_from.dose_unit = 'Yes'
    LEFT JOIN 
        form_list_unit uc1 ON uc1.uom_mstr_id = lu_from.unit_component_1
    LEFT JOIN 
        form_list_unit uc2 ON uc2.uom_mstr_id = lu_from.unit_component_2
    LEFT JOIN 
        form_list_unit uc3 ON uc3.uom_mstr_id = lu_from.unit_component_3
    WHERE 
        fdb.to_uom_mstr_id IN (482, 368, 380)
    ON CONFLICT (to_unit_id, from_unit_id) DO UPDATE SET
        to_unit_id = EXCLUDED.to_unit_id,
        to_uom_id = EXCLUDED.to_uom_id,
        from_unit_id = EXCLUDED.from_unit_id,
        from_unit_basis = EXCLUDED.from_unit_basis,
        from_unit_comp_cnt = EXCLUDED.from_unit_comp_cnt,
        from_unit_comp_1_id = EXCLUDED.from_unit_comp_1_id,
        from_unit_comp_1_uom_id = EXCLUDED.from_unit_comp_1_uom_id,
        from_unit_comp_2_id = EXCLUDED.from_unit_comp_2_id,
        from_unit_comp_3_id = EXCLUDED.from_unit_comp_3_id,
        component_multiplier = EXCLUDED.component_multiplier,
        auto_name = EXCLUDED.auto_name;
    RETURN 'Upsert and updates completed successfully';
END;
$$ LANGUAGE plpgsql;