CREATE OR REPLACE FUNCTION drop_all_function_signatures(func_name TEXT)
RETURNS void AS $$
DECLARE
    func_oid oid;
    drop_sql TEXT;
BEGIN
    FOR func_oid IN
        SELECT p.oid
        FROM pg_proc p
        JOIN pg_namespace n ON n.oid = p.pronamespace
        WHERE p.proname = func_name
          AND n.nspname = current_schema()
    LOOP
        SELECT 'DROP FUNCTION IF EXISTS '
               || n.nspname || '.' || p.proname
               || '(' || pg_get_function_identity_arguments(p.oid) || ') CASCADE'
        INTO drop_sql
        FROM pg_proc p
        JOIN pg_namespace n ON n.oid = p.pronamespace
        WHERE p.oid = func_oid;

        RAISE NOTICE 'Dropping: %', drop_sql;
        EXECUTE drop_sql;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION coalesce_if_empty_json(
    txt1 text,
    txt2 text
)
RETURNS text
LANGUAGE sql
IMMUTABLE
AS $$
  SELECT CASE
           WHEN txt1 IS NOT NULL
                AND txt1 <> '[]'
             THEN txt1
           ELSE txt2
         END
$$;

CREATE OR REPLACE FUNCTION get_column_value(col_name text, table_name text, filter_condition text DEFAULT '')
RETURNS TABLE (value numeric) AS $$
BEGIN
    RETURN QUERY EXECUTE 
        'SELECT ' || quote_ident(col_name) ||
        ' FROM ' || quote_ident(table_name) ||
        CASE WHEN COALESCE(filter_condition, '') <> '' THEN filter_condition ELSE '' END;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION coalesce_if_empty(
    arr1 anyarray,
    arr2 anyarray
)
RETURNS anyarray AS
$$
  SELECT CASE
           WHEN arr1 IS NOT NULL AND cardinality(arr1) > 0
             THEN arr1
           ELSE arr2
         END
$$
LANGUAGE sql IMMUTABLE;

CREATE OR REPLACE FUNCTION format_numeric(val numeric)
RETURNS text AS $$
BEGIN
  RETURN REGEXP_REPLACE(
    TO_CHAR(ROUND(val, 4), 'FM999,999,990.9999'),
    '\.$',
    ''
  );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

CREATE OR REPLACE FUNCTION format_currency(val numeric)
RETURNS text AS $$
DECLARE
  base_text text;
BEGIN
  -- Format to 4 decimals with fixed width, then trim trailing zeros past second decimal
  base_text := TO_CHAR(ROUND(val, 4), 'FM999,999,990.0000');

  -- Trim trailing zeros beyond 2nd decimal
  base_text := REGEXP_REPLACE(base_text, '(\.\d{2})0+$', '\1');  -- e.g. .1200 → .12
  base_text := REGEXP_REPLACE(base_text, '\.$', '');             -- remove trailing dot if value is whole number

  RETURN '$' || base_text;
END;
$$ LANGUAGE plpgsql IMMUTABLE;


CREATE OR REPLACE FUNCTION setup_view_refresh_schedule(
    p_view_name text,
    p_cron_schedule text DEFAULT '*/10 * * * *',
    p_queue_name text DEFAULT 'view_refresh_query'
) RETURNS void AS $BODY$
DECLARE
    v_start_time timestamp;
    v_execution_time interval;
    v_error_message text;
    v_params jsonb;
BEGIN
    -- Record start time
    v_start_time := clock_timestamp();

    -- Build parameters JSON for logging
    v_params := jsonb_build_object(
        'view_name', p_view_name,
        'cron_schedule', p_cron_schedule,
        'queue_name', p_queue_name
    );

    BEGIN

        -- Validate required parameters
        IF p_view_name IS NULL THEN
            RAISE EXCEPTION 'View name cannot be null';
        END IF;

        IF p_cron_schedule IS NULL THEN
            RAISE EXCEPTION 'Cron schedule cannot be null';
        END IF;

        IF p_queue_name IS NULL THEN
            RAISE EXCEPTION 'Queue name cannot be null';
        END IF;

        -- Check if the queue already exists
        IF NOT EXISTS (
            SELECT 1 
            FROM pgboss.queue 
            WHERE name = p_queue_name::text
        ) THEN
            -- Create the queue only if it doesn't exist
            PERFORM pgboss.create_queue(p_queue_name::text, '{"policy":"standard"}'::json);
        END IF;

        -- Insert the schedule only if it doesn't already exist, otherwise update it
        INSERT INTO pgboss.schedule (name, cron, data, options, timezone) 
        VALUES (p_queue_name::text, p_cron_schedule::text, jsonb_build_object('view', p_view_name), '{}', 'UTC') 
        ON CONFLICT (name) 
        DO UPDATE SET cron = EXCLUDED.cron, data = EXCLUDED.data;

    EXCEPTION WHEN OTHERS THEN
        -- Log error
        v_error_message := SQLERRM;
        RAISE LOG 'Error in setup_view_refresh_schedule: %', v_error_message;
        RAISE;
    END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION jsonb_strip_nulls(obj jsonb)
RETURNS jsonb LANGUAGE sql AS $$
  SELECT jsonb_object_agg(key, value)
  FROM jsonb_each(obj)
  WHERE value IS NOT NULL;
$$;

CREATE OR REPLACE FUNCTION validate_subform_array(
  p_json json,
  p_field_name text,
  p_allow_multiple boolean
) RETURNS void AS $BODY$
BEGIN
  -- Check for null array
  IF p_json IS NULL THEN
    RAISE EXCEPTION 'Subform % cannot be null', p_field_name;
  END IF;

  -- For single-item subforms, validate count
  IF NOT p_allow_multiple AND jsonb_array_length(p_json::jsonb) > 1 THEN
    RAISE EXCEPTION 'Subform % cannot have multiple items', p_field_name;
  END IF;
END;
$BODY$ LANGUAGE plpgsql