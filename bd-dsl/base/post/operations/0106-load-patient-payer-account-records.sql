-- First, create accounts for patients without accounts
INSERT INTO form_billing_account (
    patient_id,
    type,
    created_on,
    created_by
)
SELECT 
    p.id AS patient_id,
    'Patient' AS type,
    CURRENT_TIMESTAMP AS created_on,
    1 as created_by
FROM 
    form_patient p
LEFT JOIN 
    form_billing_account ba ON ba.patient_id = p.id AND ba.type = 'Patient'
WHERE 
    ba.id IS NULL;

-- Then, create accounts for payers without accounts
INSERT INTO form_billing_account (
    payer_id,
    type,
    created_on,
    created_by
)
SELECT 
    p.id AS payer_id,
    'Payer' AS type,
    CURRENT_TIMESTAMP AS created_on,
    1 as created_by
FROM 
    form_payer p
LEFT JOIN 
    form_billing_account ba ON ba.payer_id = p.id AND ba.type = 'Payer'
WHERE 
    ba.id IS NULL;

-- Then, create accounts for sites without accounts
INSERT INTO form_billing_account (
    site_id,
    type,
    created_on,
    created_by
)
SELECT 
    s.id AS site_id,
    'Site' AS type,
    CURRENT_TIMESTAMP AS created_on,
    1 as created_by
FROM 
    form_site s
LEFT JOIN 
    form_billing_account ba ON ba.site_id = s.id AND ba.type = 'Site'
WHERE 
    ba.id IS NULL;

-- Create trigger function for patient account creation
DO $$ BEGIN
  PERFORM drop_all_function_signatures('create_patient_account');
END $$;
CREATE OR REPLACE FUNCTION create_patient_account()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO form_billing_account (
        patient_id,
        type,
        created_on,
        created_by
    ) VALUES (
        NEW.id,
        'Patient',
        CURRENT_TIMESTAMP,
        COALESCE(NEW.created_by, 1)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for patient creation
DROP TRIGGER IF EXISTS trg_create_patient_account ON form_patient;
CREATE TRIGGER trg_create_patient_account
AFTER INSERT ON form_patient
FOR EACH ROW
EXECUTE FUNCTION create_patient_account();

-- Create trigger function for payer account creation
DO $$ BEGIN
  PERFORM drop_all_function_signatures('create_payer_account');
END $$;
CREATE OR REPLACE FUNCTION create_payer_account()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO form_billing_account (
        payer_id,
        type,
        created_on,
        created_by
    ) VALUES (
        NEW.id,
        'Payer',
        CURRENT_TIMESTAMP,
        COALESCE(NEW.created_by, 1)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for payer creation
DROP TRIGGER IF EXISTS trg_create_payer_account ON form_payer;
CREATE TRIGGER trg_create_payer_account
AFTER INSERT ON form_payer
FOR EACH ROW
EXECUTE FUNCTION create_payer_account();

-- Create trigger function for site account creation
CREATE OR REPLACE FUNCTION create_site_account()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO form_billing_account (
        site_id,
        type,
        created_on,
        created_by
    ) VALUES (
        NEW.id,
        'Site',
        CURRENT_TIMESTAMP,
        COALESCE(NEW.created_by, 1)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for site creation
DROP TRIGGER IF EXISTS trg_create_site_account ON form_site;
CREATE TRIGGER trg_create_site_account
AFTER INSERT ON form_site
FOR EACH ROW
EXECUTE FUNCTION create_site_account();
