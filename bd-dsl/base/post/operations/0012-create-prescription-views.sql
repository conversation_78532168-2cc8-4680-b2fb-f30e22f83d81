BEGIN;
osite index for the common filter conditions
CREATE INDEX IF NOT EXISTS idx_careplan_dt_item_bill_scanned
ON form_careplan_dt_item(bill, needs_scanned) 
WHERE COALESCE(bill, 'No') = 'Yes' AND COALESCE(needs_scanned, 'No') = 'Yes';

-- Index for rx_invoices joins
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_disp_rx_id
ON form_careplan_order_rx_disp(rx_id);

-- Composite index for form_careplan_order_rx table that will help with vw_rx_order
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_patient_archived_deleted
ON form_careplan_order_rx(patient_id, archived, deleted);

CREATE INDEX IF NOT EXISTS idx_form_document_lookup ON form_document(form_name, assigned_to, direct_attachment, source) 
   WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_form_document_patient_id_form_rx ON form_document(patient_id, form_code, form_name)
   WH<PERSON><PERSON> archived IS NOT TRUE AND deleted IS NOT TRUE 
   AND type_id = 'Prescription';

-- Indexes for form_careplan_order_rx table
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_rx_no ON form_careplan_order_rx(rx_no);
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_patient_id ON form_careplan_order_rx(patient_id);
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_archived_deleted ON form_careplan_order_rx(id) WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

-- Indexes for form_careplan_order_item table
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_item_rx_no ON form_careplan_order_item(rx_no);
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_item_patient_id ON form_careplan_order_item(patient_id);
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_item_status_id ON form_careplan_order_item(status_id) WHERE status_id IN ('1', '5', '4');

-- Indexes for form_careplan_orderp_item table
CREATE INDEX IF NOT EXISTS idx_form_careplan_orderp_item_rx_no ON form_careplan_orderp_item(rx_no);
CREATE INDEX IF NOT EXISTS idx_form_careplan_orderp_item_patient_id ON form_careplan_orderp_item(patient_id);
CREATE INDEX IF NOT EXISTS idx_form_careplan_orderp_item_status_id ON form_careplan_orderp_item(status_id) WHERE status_id IN ('1', '5', '4');

-- Indexes for form_careplan_order table
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_patient_id ON form_careplan_order(patient_id);
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_id_void ON form_careplan_order(id) WHERE COALESCE(void, 'No') <> 'Yes';

-- Indexes for form_patient table
CREATE INDEX IF NOT EXISTS idx_form_patient_status_id ON form_patient(status_id) WHERE status_id IN ('1', '2', '3');

-- Indexes for join tables
CREATE INDEX IF NOT EXISTS idx_sf_form_careplan_order_to_careplan_order_item_fk ON sf_form_careplan_order_to_careplan_order_item(form_careplan_order_item_fk);
CREATE INDEX IF NOT EXISTS idx_sf_form_careplan_order_to_careplan_orderp_item_fk ON sf_form_careplan_order_to_careplan_orderp_item(form_careplan_orderp_item_fk);
CREATE INDEX IF NOT EXISTS idx_sf_form_careplan_order_to_careplan_order_rental_fk ON sf_form_careplan_order_to_careplan_order_rental(form_careplan_order_rental_fk);
CREATE INDEX IF NOT EXISTS idx_sf_form_careplan_order_to_careplan_orders_item_fk ON sf_form_careplan_order_to_careplan_orders_item(form_careplan_orders_item_fk);
-- For invoice queries
CREATE INDEX IF NOT EXISTS idx_form_billing_invoice_master_is_master_invoice ON form_billing_invoice(is_master_invoice);

CREATE INDEX IF NOT EXISTS idx_form_billing_invoice_master_invoice_no ON form_billing_invoice(master_invoice_no);
CREATE INDEX IF NOT EXISTS idx_form_billing_invoice_master_parent_no ON form_billing_invoice(master_invoice_no, parent_invoice_no);

-- For rx_dispense queries
CREATE INDEX IF NOT EXISTS idx_form_careplan_order_rx_disp_rx_no ON form_careplan_order_rx_disp(rx_no);

-- For document retrieval
CREATE INDEX IF NOT EXISTS idx_form_document_patient_id_link ON form_document(patient_id);

-- For delivery tickets
CREATE INDEX IF NOT EXISTS idx_form_careplan_delivery_tick_ticket_no ON form_careplan_delivery_tick(ticket_no);
CREATE INDEX IF NOT EXISTS idx_form_careplan_dt_item_ticket_no ON form_careplan_dt_item(ticket_no);
CREATE INDEX IF NOT EXISTS idx_form_careplan_dt_item_inventory_patient ON form_careplan_dt_item(inventory_id, patient_id, careplan_id);

COMMIT;


CREATE OR REPLACE FUNCTION get_highest_ranked_copay_insurance(
    p_order_item_payer_ids TEXT,
    p_order_payer_ids TEXT,
    p_patient_id INTEGER
) RETURNS INTEGER AS $$
DECLARE
    array_1 JSONB;
    array_2 JSONB;
    v_insurance_id INTEGER;
BEGIN
    -- Convert input text to JSONB (or default to an empty array if null or empty)
    array_1 := COALESCE(NULLIF(p_order_item_payer_ids, '[]')::JSONB, '[]'::JSONB);
    array_2 := COALESCE(NULLIF(p_order_payer_ids, '[]')::JSONB, '[]'::JSONB);
    
    -- First try to find in array_1 (order item payers) with lowest rank (highest priority)
    IF jsonb_array_length(array_1) > 0 THEN
        WITH ranked_ids AS (
            SELECT 
                (elem->>'id')::INTEGER AS insurance_id,
                (elem->>'rank')::INTEGER AS rank
            FROM 
                jsonb_array_elements(array_1) AS elem
            WHERE
                EXISTS (
                    SELECT 1 
                    FROM form_patient_insurance ins 
                    WHERE ins.id = (elem->>'id')::INTEGER
                    AND ins.type_id = 'COPAY'
                    AND ins.archived IS NOT TRUE
                    AND ins.deleted IS NOT TRUE
                    AND COALESCE(ins.active, 'No') = 'Yes'
                )
            ORDER BY 
                (elem->>'rank')::INTEGER ASC
            LIMIT 1
        )
        SELECT insurance_id INTO v_insurance_id FROM ranked_ids;
        
        IF v_insurance_id IS NOT NULL THEN
            RETURN v_insurance_id;
        END IF;
    END IF;
    
    -- Then try array_2 (order payers) with lowest rank
    IF jsonb_array_length(array_2) > 0 THEN
        WITH ranked_ids AS (
            SELECT 
                (elem->>'id')::INTEGER AS insurance_id,
                (elem->>'rank')::INTEGER AS rank
            FROM 
                jsonb_array_elements(array_2) AS elem
            WHERE
                EXISTS (
                    SELECT 1 
                    FROM form_patient_insurance ins 
                    WHERE ins.id = (elem->>'id')::INTEGER
                    AND ins.type_id = 'COPAY'
                    AND ins.archived IS NOT TRUE
                    AND ins.deleted IS NOT TRUE
                    AND COALESCE(ins.active, 'No') = 'Yes'
                )
            ORDER BY 
                (elem->>'rank')::INTEGER ASC
            LIMIT 1
        )
        SELECT insurance_id INTO v_insurance_id FROM ranked_ids;
        
        IF v_insurance_id IS NOT NULL THEN
            RETURN v_insurance_id;
        END IF;
    END IF;
    
    -- Fallback: Look for any active COPAY insurance for the patient
    SELECT id INTO v_insurance_id
    FROM form_patient_insurance
    WHERE patient_id = p_patient_id
    AND type_id = 'COPAY'
    AND archived IS NOT TRUE
    AND deleted IS NOT TRUE
    AND COALESCE(active, 'No') = 'Yes'
    ORDER BY id
    LIMIT 1;
    
    RETURN v_insurance_id;
END;
$$ LANGUAGE plpgsql STABLE;

CREATE OR REPLACE VIEW vw_invoice_claim_response_details AS 
SELECT
bi.id as invoice_id,
bi.invoice_no,
bi.master_invoice_no,
bi.revenue_accepted,
COALESCE(nc.copay::numeric, mc.copay::numeric, pc.copay::numeric, 0::numeric) as copay,
COALESCE(nc.paid::numeric, mc.paid::numeric, pc.paid::numeric, 0::numeric) as paid,
COALESCE(nc.billed::numeric, mc.billed::numeric, pc.billed::numeric, 0::numeric) as billed,
COALESCE(nc.expected::numeric, mc.expected::numeric, pc.expected::numeric, 0::numeric) as expected,
COALESCE(nc.status, mc.status, pc.status)::text as status,
csc.name::text as claim_substatus,
COALESCE(pc.substatus_id, mc.substatus_id, nc.substatus_id)::text as claim_substatus_id,
csc.code::text as claim_substatus_id_auto_name,
nc.auth_flag::text as auth_flag
FROM form_billing_invoice bi
LEFT JOIN sf_form_billing_invoice_to_med_claim_1500 bitpc ON bitpc.form_billing_invoice_fk = bi.id 
  AND bitpc.archive IS NOT TRUE 
  AND bitpc.delete IS NOT TRUE
LEFT JOIN form_med_claim_1500 pc ON pc.id = bitpc.form_med_claim_1500_fk 
  AND COALESCE(pc.void, 'No') <> 'Yes'
  AND pc.archived IS NOT TRUE 
  AND pc.deleted IS NOT TRUE  
LEFT JOIN sf_form_billing_invoice_to_med_claim bitmc ON bitmc.form_billing_invoice_fk = bi.id 
  AND bitmc.archive IS NOT TRUE 
  AND bitmc.delete IS NOT TRUE
LEFT JOIN form_med_claim mc ON mc.id = bitmc.form_med_claim_fk 
  AND COALESCE(mc.void, 'No') <> 'Yes' 
  AND mc.archived IS NOT TRUE 
  AND mc.deleted IS NOT TRUE 
LEFT JOIN sf_form_billing_invoice_to_ncpdp bitnc ON bitnc.form_billing_invoice_fk = bi.id 
  AND bitnc.archive IS NOT TRUE 
  AND bitnc.delete IS NOT TRUE
LEFT JOIN form_ncpdp nc ON nc.id = bitnc.form_ncpdp_fk 
  AND COALESCE(nc.void, 'No') <> 'Yes' 
  AND nc.archived IS NOT TRUE 
  AND nc.deleted IS NOT TRUE 
LEFT JOIN form_list_billing_csstatus csc ON csc.code = COALESCE(pc.substatus_id, mc.substatus_id, nc.substatus_id)
WHERE COALESCE(bi.void, 'No') <> 'Yes'
AND COALESCE(bi.zeroed, 'No') <> 'Yes'
AND bi.archived IS NOT TRUE
AND bi.deleted IS NOT TRUE;

CREATE OR REPLACE VIEW vw_rx_copay_card_invoice_details AS 
SELECT
bi.id as invoice_id,
bi.invoice_no,
bi.master_invoice_no,
bi.total_billed,
COALESCE(nc.copay, mc.copay, pc.copay, 0) as copay,
COALESCE(nc.paid::numeric, mc.paid::numeric, pc.paid::numeric, 0::numeric) as paid,
COALESCE(nc.billed::numeric, mc.billed::numeric, pc.billed::numeric, 0::numeric) as billed,
COALESCE(nc.status, mc.status, pc.status) as status,
csc.name as claim_substatus,
COALESCE(pc.substatus_id, mc.substatus_id, nc.substatus_id) as claim_substatus_id,
csc.code as claim_substatus_id_auto_name
FROM form_billing_invoice bi
INNER JOIN form_payer py ON py.id = bi.payer_id
  AND py.type_id = 'COPAY'
  AND py.archived IS NOT TRUE
  AND py.deleted IS NOT TRUE
LEFT JOIN sf_form_billing_invoice_to_med_claim_1500 bitpc ON bitpc.form_billing_invoice_fk = bi.id 
  AND bitpc.archive IS NOT TRUE
  AND bitpc.delete IS NOT TRUE
LEFT JOIN form_med_claim_1500 pc ON pc.id = bitpc.form_med_claim_1500_fk 
  AND COALESCE(pc.void, 'No') <> 'Yes'
  AND pc.archived IS NOT TRUE
  AND pc.deleted IS NOT TRUE
LEFT JOIN sf_form_billing_invoice_to_med_claim bitmc ON bitmc.form_billing_invoice_fk = bi.id 
  AND bitmc.archive IS NOT TRUE
  AND bitmc.delete IS NOT TRUE
LEFT JOIN form_med_claim mc ON mc.id = bitmc.form_med_claim_fk 
  AND COALESCE(mc.void, 'No') <> 'Yes'
  AND mc.archived IS NOT TRUE
  AND mc.deleted IS NOT TRUE
LEFT JOIN sf_form_billing_invoice_to_ncpdp bitnc ON bitnc.form_billing_invoice_fk = bi.id 
  AND bitnc.archive IS NOT TRUE
  AND bitnc.delete IS NOT TRUE
LEFT JOIN form_ncpdp nc ON nc.id = bitnc.form_ncpdp_fk 
  AND COALESCE(nc.void, 'No') <> 'Yes'
  AND nc.archived IS NOT TRUE
  AND nc.deleted IS NOT TRUE
LEFT JOIN form_list_billing_csstatus csc ON csc.code = COALESCE(pc.substatus_id, mc.substatus_id, nc.substatus_id)
WHERE COALESCE(bi.void, 'No') <> 'Yes'
AND COALESCE(bi.zeroed, 'No') <> 'Yes'
AND bi.archived IS NOT TRUE
AND bi.deleted IS NOT TRUE;

CREATE OR REPLACE VIEW vw_order_raw AS
WITH order_item_data AS (
    SELECT
        coi.id,
        coi.rx_no,
        coi.patient_id,
        coi.status_id,
        coi.therapy_id,
        coi.drug_pa_id,
        coi.start_date,
        coi.stop_date,
        coi.expiration_date,
        coi.inventory_id,
        coi.written_date,
        coi.route_id,
        coi.next_fill_number,
        coi.dose,
        coi.dose_unit_id,
        coi.dose_range_1,
        coi.dose_range_2,
        coi.allowed_variance,
        coi.rx_template_id,
        coi.order_complete,
        coi.type_id,
        coi.billing_method,
        coi.last_event_id,
        coi.last_event_by,
        coi.last_event_datetime,
        coi.is_specialty,
        coi.created_on,
        coi.created_by,
        coi.updated_on,
        coi.updated_by,
        coi.stat_order,
        coi.intake_substatus_id,
        coi.auth_flag,
        coi.bv_flag,
        coi.onhold_reason,
        coi.payer_ids,
        coi.dx_ids,
        coi.code,
        'careplan_order_item' as source_type,
        coi.prescription_provided_in,
        coi.weight,
        coi.bsa,
        coi.lesions,
        coi.therapy_days,
        coi.cm2_per_lesion,
        coi.m2_173,
        coi.grams_of_carbohydrate,
        coi.frequency_id,
        coi.frequency_multiplier,
        coi.frequency_label,
        coi.comments,
        coi.is_erx,
        coi.ss_description,
        coi.physician_order_id,
        coi.formatted_ndc,
        coi.hcpc_code,
        coi.sig,
        coi.manufacturer_id,
        coi.requires_infusion_time,
        coi.requires_therapy_days,
        coi.patient_param_required,
        coi.infuse_length,
        coi.infuse_for,
        coi.infuse_time,
        coi.discontinued_date,
        coi.frequency_weekly,
        coi.frequency_type,
        coi.refills
    FROM form_careplan_order_item coi
    WHERE coi.archived IS NOT TRUE
    AND coi.deleted IS NOT TRUE
),
orderp_item_data AS (
    SELECT
        copi.id,
        copi.rx_no,
        copi.patient_id,
        copi.status_id,
        copi.therapy_id,
        copi.drug_pa_id,
        copi.start_date,
        copi.stop_date,
        copi.expiration_date,
        copi.inventory_id,
        copi.written_date,
        copi.route_id,
        copi.next_fill_number,
        copi.dose,
        copi.dose_unit_id,
        copi.dose_range_1,
        copi.dose_range_2,
        copi.allowed_variance,
        copi.rx_template_id,
        copi.order_complete,
        copi.type_id,
        copi.billing_method,
        copi.last_event_id,
        copi.last_event_by,
        copi.last_event_datetime,
        copi.is_specialty,
        copi.created_on,
        copi.created_by,
        copi.updated_on,
        copi.updated_by,
        copi.stat_order,
        copi.intake_substatus_id,
        copi.auth_flag,
        copi.bv_flag,
        copi.onhold_reason,
        copi.payer_ids,
        copi.dx_ids,
        ord.code,
        'careplan_orderp_item' as source_type,
        copi.prescription_provided_in,
        copi.weight,
        copi.bsa,
        copi.lesions,
        copi.therapy_days,
        copi.cm2_per_lesion,
        copi.m2_173,
        copi.grams_of_carbohydrate,
        copi.frequency_id,
        copi.frequency_multiplier,
        copi.frequency_label,
        copi.comments,
        copi.is_erx,
        copi.ss_description,
        copi.physician_order_id,
        copi.formatted_ndc,
        copi.hcpc_code,
        copi.sig,
        copi.manufacturer_id,
        copi.requires_infusion_time,
        copi.requires_therapy_days,
        copi.patient_param_required,
        copi.infuse_length,
        copi.infuse_for,
        copi.infuse_time,
        copi.discontinued_date,
        copi.frequency_weekly,
        copi.frequency_type,
        copi.refills
    FROM form_careplan_orderp_item copi
	INNER JOIN sf_form_careplan_order_to_careplan_orderp_item sfco
    ON sfco.form_careplan_orderp_item_fk = copi.id 
    AND sfco.archive IS NOT TRUE 
    AND sfco.delete IS NOT TRUE 
    INNER JOIN form_careplan_order ord ON ord.id = sfco.form_careplan_order_fk
    AND ord.archived IS NOT TRUE 
    AND ord.deleted IS NOT TRUE 
    WHERE copi.archived IS NOT TRUE
    AND copi.deleted IS NOT TRUE
),
rx_dispense_data AS (
    SELECT DISTINCT ON (rxd.rx_no)
        rxd.rx_no,
        rxd.master_invoice_no,
        rxd.id as working_dispense_id,
        rxd.fill_number,
        rxd.delivery_ticket_id,
        rxd.status as fill_status,
        rxd.refills,
        rxd.refills_remaining,
        rxd.doses_allowed,
        rxd.doses_remaining
    FROM form_careplan_order_rx_disp rxd
    WHERE rxd.archived IS NOT TRUE
    AND rxd.deleted IS NOT TRUE
    ORDER BY rxd.rx_no, rxd.id DESC
),
rx_invoice_data AS (
    SELECT 
        dispense.working_dispense_id,
        array_agg(DISTINCT bi.invoice_id) as invoice_ids,
        bool_or(COALESCE(bi.revenue_accepted, 'No') <> 'Yes') as has_unaccepted_claims,
        ROUND(MAX(bi.copay::numeric), 2) as copay
    FROM rx_dispense_data dispense
    LEFT JOIN vw_invoice_claim_response_details bi
        ON bi.master_invoice_no = dispense.master_invoice_no
    GROUP BY dispense.working_dispense_id
    LIMIT 1
),
patient_insurance_next AS (
    SELECT DISTINCT 
        rx.id as rx_id,
        get_insurance_with_rank(oi.payer_ids, co.payer_ids, '1') as insurance_id
    FROM form_careplan_order_rx rx
    LEFT JOIN order_item_data oi ON oi.rx_no = rx.rx_no
    LEFT JOIN sf_form_careplan_order_to_careplan_order_item sfco ON sfco.form_careplan_order_item_fk = oi.id
        AND sfco.archive IS NOT TRUE
        AND sfco.delete IS NOT TRUE
    LEFT JOIN form_careplan_order co ON co.id = sfco.form_careplan_order_fk
        AND co.archived IS NOT TRUE
        AND co.deleted IS NOT TRUE
    WHERE rx.archived IS NOT TRUE
    AND rx.deleted IS NOT TRUE
),
patient_copay_insurance AS (
    SELECT DISTINCT
        rx.id as rx_id,
        get_highest_ranked_copay_insurance(oi.payer_ids, co.payer_ids, rx.patient_id) as insurance_id
    FROM form_careplan_order_rx rx
    LEFT JOIN order_item_data oi ON oi.rx_no = rx.rx_no
    LEFT JOIN sf_form_careplan_order_to_careplan_order_item sfco ON sfco.form_careplan_order_item_fk = oi.id
        AND sfco.archive IS NOT TRUE
        AND sfco.delete IS NOT TRUE
    LEFT JOIN form_careplan_order co ON co.id = sfco.form_careplan_order_fk
        AND co.archived IS NOT TRUE
        AND co.deleted IS NOT TRUE
)
SELECT
    cpor.id as rx_id,
    co.id as order_id,
    oi.id as item_id,
    co.patient_id,
    co.careplan_id,
    COALESCE(co.site_id, pt.site_id) as site_id,
    COALESCE(co.team_id, pt.team_id) as team_id,
    co.prescriber_id,
    co.physician_id,
    co.referral_source_id,
    co.territory_id,
    pt.status_id as patient_status_id,
    co.commissioned_sales_rep,
    COALESCE(cpor.therapy_id, oi.therapy_id) as therapy_id,
    oi.drug_pa_id as pa_id,
    cpor.rx_no,
    co.order_no,
    oi.status_id as status_id,
    COALESCE(cpor.start_date, oi.start_date, co.start_date) as start_date,
    COALESCE(oi.stop_date, co.stop_date) as stop_date,
    COALESCE(cpor.expiration_date, oi.expiration_date, co.expiration_date) as expiration_date,
    cpor.template_type,
    cpor.next_fill_date,
    oi.inventory_id,
    cpor.dispense_quantity,
    cpor.day_supply,
    oi.written_date as written_date,
    oi.route_id as route_id,
    rxd.delivery_ticket_id,
    rxd.fill_status,
    rxd.working_dispense_id,
    cpor.rx_complete,
    cpor.rx_verified,
    cpor.rx_denied,
    cpor.is_refill,
    cpor.refill_rx_id,
    oi.order_complete,
    oi.type_id,
    cpor.is_340b,
    COALESCE(oi.billing_method, co.billing_method) as billing_method,
    CASE
        WHEN COALESCE(oi.billing_method, co.billing_method) = 'Do Not Bill' THEN NULL
        ELSE 'Yes'
    END as bill,
    ins.id as insurance_id,
    ins.payer_id,
    ins.billing_method_id,
    ins.type_id as insurance_type_id,
    ins.effective_date,
    ins.bill_for_denial,
    ins.next_insurance_id,
    co.bill_supplies,
    co.supplies_insurance_id,
    co.supply_billable_id,
    co.supplies_pa_id,
    dt.service_from as service_from,
    dt.service_to as service_to,
    co.supply_kit_id,
    oi.last_event_id as last_event_id,
    oi.last_event_by as last_event_by,
    oi.last_event_datetime as last_event_datetime,
    oi.is_specialty as is_specialty,
    cpor.last_through_date,
    COALESCE(cpor.created_on, oi.created_on, co.created_on) as created_on,
    COALESCE(cpor.created_by, oi.created_by, co.created_by) as created_by,
    COALESCE(cpor.updated_on, oi.updated_on, co.updated_on) as updated_on,
    COALESCE(cpor.updated_by, oi.updated_by, co.updated_by) as updated_by,
    co.requires_nursing,
    co.nursing_status,
    co.nursing_agency_id,
    co.infusion_suite_id,
    oi.stat_order as stat_order,
    oi.intake_substatus_id,
    dx.diagnosis,
    dx.patient_dx_id,
    dx.dx_id,
    oi.auth_flag,
    oi.bv_flag,
    oi.onhold_reason,
    doc.document_file_path,
    doc.document_id,
    doc.document_name,
    'careplan_order_item' as item_id_type,
    oi.next_fill_number,
    oi.dose,
    oi.dose_unit_id,
    oi.dose_range_1,
    oi.dose_range_2,
    oi.allowed_variance,
    oi.rx_template_id,
    oi.prescription_provided_in,
    oi.weight,
    oi.bsa,
    oi.lesions,
    oi.therapy_days,
    oi.cm2_per_lesion,
    oi.m2_173,
    oi.grams_of_carbohydrate,
    oi.frequency_id,
    oi.frequency_multiplier,
    oi.frequency_label,
    oi.comments,
    oi.is_erx,
    oi.ss_description,
    oi.physician_order_id,
    oi.formatted_ndc,
    oi.hcpc_code,
    oi.sig,
    oi.manufacturer_id,
    oi.requires_infusion_time,
    oi.requires_therapy_days,
    oi.patient_param_required,
    oi.infuse_length,
    oi.infuse_for,
    oi.infuse_time,
    oi.discontinued_date,
    oi.frequency_weekly,
    oi.frequency_type,
    rxd.master_invoice_no,
    pci.insurance_id as copay_insurance_id,
    COALESCE(oi.payer_ids, co.payer_ids) as payer_ids,
    COALESCE(oi.dx_ids, co.dx_ids) as dx_ids,
    oi.refills,
    oi.drug_pa_id,
    co.rental_pa_id,
    co.rental_insurance_id,
    co.nursing_insurance_id,
    co.rental_billable_id,
    co.nursing_pa_id
FROM order_item_data oi
INNER JOIN sf_form_careplan_order_to_careplan_order_item sfco
    ON sfco.form_careplan_order_item_fk = oi.id 
    AND sfco.archive IS NOT TRUE 
    AND sfco.delete IS NOT TRUE 
INNER JOIN form_patient pt
    ON pt.id = oi.patient_id
    AND pt.archived IS NOT TRUE
    AND pt.deleted IS NOT TRUE
INNER JOIN form_careplan_order co 
    ON co.id = sfco.form_careplan_order_fk
    AND co.archived IS NOT TRUE
    AND co.deleted IS NOT TRUE
    AND COALESCE(co.void, 'No') <> 'Yes'
    AND COALESCE(co.therapy_set_dc, 'No') <> 'Yes'
LEFT JOIN form_careplan_order_rx cpor 
    ON cpor.rx_no = oi.rx_no
    AND cpor.archived IS NOT TRUE
    AND cpor.deleted IS NOT TRUE
LEFT JOIN rx_dispense_data rxd 
    ON rxd.rx_no = cpor.rx_no
LEFT JOIN rx_invoice_data rxi
    ON rxi.working_dispense_id = rxd.working_dispense_id
LEFT JOIN LATERAL (
    SELECT pi.* 
    FROM form_patient_insurance pi
    WHERE pi.id IN (SELECT * FROM get_insurance_ids_for_joining(oi.payer_ids, co.payer_ids, '1'))
      AND pi.archived IS NOT TRUE
      AND pi.deleted IS NOT TRUE
      AND COALESCE(pi.active, 'No') = 'Yes'
      AND pi.patient_id = pt.id
    LIMIT 1
) ins ON true
LEFT JOIN patient_copay_insurance pci ON pci.rx_id = cpor.id
LEFT JOIN LATERAL (
    SELECT
        ptdx.id as patient_dx_id,
        ptdx.dx_id as dx_id,
        dx.name as diagnosis
    FROM jsonb_array_elements(coalesce_if_empty_json(
        coalesce_if_empty_json(
        coalesce_if_empty_json(oi.dx_ids, co.dx_ids), 
        co.dx_ids), 
        '[]')::jsonb) AS elem
    INNER JOIN form_patient_diagnosis ptdx 
        ON ptdx.id = (elem->>'id')::int 
        AND ptdx.archived IS NOT TRUE 
        AND ptdx.deleted IS NOT TRUE 
        AND COALESCE(ptdx.active, 'No') = 'Yes'
    INNER JOIN form_list_diagnosis dx 
        ON dx.code = ptdx.dx_id 
        AND dx.archived IS NOT TRUE 
        AND dx.deleted IS NOT TRUE
    GROUP BY (elem->>'rank')::int, ptdx.id, ptdx.dx_id, dx.name
    ORDER BY (elem->>'rank')::int ASC
    LIMIT 1
) AS dx ON true
LEFT JOIN form_careplan_delivery_tick dt 
    ON dt.id = rxd.delivery_ticket_id
    AND dt.archived IS NOT TRUE
    AND dt.deleted IS NOT TRUE
LEFT JOIN LATERAL (
    SELECT 
        doc.id as document_id,
        doc.name as document_name,
        doc.file_path as document_file_path
    FROM form_document doc
    WHERE doc.form_name = 'careplan_order_item'
    AND doc.form_code = oi.code
    AND doc.archived IS NOT TRUE
    AND doc.deleted IS NOT TRUE
    AND doc.type_id = 'Prescription'
    AND doc.patient_id = oi.patient_id
    ORDER BY doc.created_on DESC
    LIMIT 1
) doc ON TRUE

UNION ALL

SELECT
    cpor.id as rx_id,
    co.id as order_id,
    opi.id as item_id, 
    co.patient_id,
    co.careplan_id,
    COALESCE(co.site_id, pt.site_id) as site_id,
    COALESCE(co.team_id, pt.team_id) as team_id,
    co.prescriber_id,
    co.physician_id,
    co.referral_source_id,
    co.territory_id,
    pt.status_id as patient_status_id,
    co.commissioned_sales_rep,
    COALESCE(cpor.therapy_id, opi.therapy_id) as therapy_id,
    opi.drug_pa_id as pa_id,
    cpor.rx_no,
    co.order_no,
    opi.status_id as status_id,
    COALESCE(cpor.start_date, opi.start_date) as start_date,
    opi.stop_date as stop_date,
    COALESCE(cpor.expiration_date, opi.expiration_date) as expiration_date,
    cpor.template_type,
    cpor.next_fill_date,
    opi.inventory_id,
    cpor.dispense_quantity,
    cpor.day_supply,
    opi.written_date as written_date,
    opi.route_id as route_id,
    rxd.delivery_ticket_id,
    rxd.fill_status,
    rxd.working_dispense_id,
    cpor.rx_complete,
    cpor.rx_verified,
    cpor.rx_denied,
    cpor.is_refill,
    cpor.refill_rx_id,
    opi.order_complete,
    opi.type_id,
    cpor.is_340b,
    opi.billing_method as billing_method,
    CASE
        WHEN COALESCE(opi.billing_method, co.billing_method) = 'Do Not Bill' THEN NULL
        ELSE 'Yes'
    END as bill,
    ins.id as insurance_id,
    ins.payer_id,
    ins.billing_method_id,
    ins.type_id as insurance_type_id,
    ins.effective_date,
    ins.bill_for_denial,
    ins.next_insurance_id,
    co.bill_supplies,
    co.supplies_insurance_id,
    co.supply_billable_id,
    co.supplies_pa_id,
    dt.service_from as service_from,
    dt.service_to as service_to,
    co.supply_kit_id,
    opi.last_event_id as last_event_id,
    opi.last_event_by as last_event_by,
    opi.last_event_datetime as last_event_datetime,
    opi.is_specialty as is_specialty,
    cpor.last_through_date,
    COALESCE(cpor.created_on, opi.created_on) as created_on,
    COALESCE(cpor.created_by, opi.created_by) as created_by,
    COALESCE(cpor.updated_on, opi.updated_on) as updated_on,
    COALESCE(cpor.updated_by, opi.updated_by) as updated_by,
    co.requires_nursing,
    co.nursing_status,
    co.nursing_agency_id,
    co.infusion_suite_id,
    opi.stat_order as stat_order,
    opi.intake_substatus_id,
    dx.diagnosis,
    dx.patient_dx_id,
    dx.dx_id,
    opi.auth_flag,
    opi.bv_flag,
    opi.onhold_reason,
    doc.document_file_path,
    doc.document_id,
    doc.document_name,
    'careplan_orderp_item' as item_id_type,
    opi.next_fill_number,
    opi.dose,
    opi.dose_unit_id,
    opi.dose_range_1,
    opi.dose_range_2,
    opi.allowed_variance,
    opi.rx_template_id,
    opi.prescription_provided_in,
    opi.weight,
    opi.bsa,
    opi.lesions,
    opi.therapy_days,
    opi.cm2_per_lesion,
    opi.m2_173,
    opi.grams_of_carbohydrate,
    opi.frequency_id,
    opi.frequency_multiplier,
    opi.frequency_label,
    opi.comments,
    opi.is_erx,
    opi.ss_description,
    opi.physician_order_id,
    opi.formatted_ndc,
    opi.hcpc_code,
    opi.sig,
    opi.manufacturer_id,
    opi.requires_infusion_time,
    opi.requires_therapy_days,
    opi.patient_param_required,
    opi.infuse_length,
    opi.infuse_for,
    opi.infuse_time,
    opi.discontinued_date,
    opi.frequency_weekly,
    opi.frequency_type,
    rxd.master_invoice_no,
    pci.insurance_id as copay_insurance_id,
    opi.payer_ids,
    opi.dx_ids,
    opi.refills,
    opi.drug_pa_id,
    co.rental_pa_id,
    co.rental_insurance_id,
    co.nursing_insurance_id,
    co.rental_billable_id,
    co.nursing_pa_id
FROM orderp_item_data opi
INNER JOIN sf_form_careplan_order_to_careplan_orderp_item sfco
    ON sfco.form_careplan_orderp_item_fk = opi.id 
    AND sfco.archive IS NOT TRUE 
    AND sfco.delete IS NOT TRUE 
INNER JOIN form_patient pt
    ON pt.id = opi.patient_id
    AND pt.archived IS NOT TRUE
    AND pt.deleted IS NOT TRUE
INNER JOIN form_careplan_order co 
    ON co.id = sfco.form_careplan_order_fk
    AND co.archived IS NOT TRUE
    AND co.deleted IS NOT TRUE
    AND COALESCE(co.void, 'No') <> 'Yes'
LEFT JOIN form_careplan_order_rx cpor 
    ON cpor.rx_no = opi.rx_no
    AND cpor.archived IS NOT TRUE
    AND cpor.deleted IS NOT TRUE
LEFT JOIN rx_dispense_data rxd 
    ON rxd.rx_no = cpor.rx_no
LEFT JOIN rx_invoice_data rxi
    ON rxi.working_dispense_id = rxd.working_dispense_id
LEFT JOIN LATERAL (
    SELECT pi.* 
    FROM form_patient_insurance pi
    WHERE pi.id IN (SELECT * FROM get_insurance_ids_for_joining(opi.payer_ids, co.payer_ids, '1'))
      AND pi.archived IS NOT TRUE
      AND pi.deleted IS NOT TRUE
      AND COALESCE(pi.active, 'No') = 'Yes'
      AND pi.patient_id = pt.id
    LIMIT 1
) ins ON true
LEFT JOIN patient_copay_insurance pci ON pci.rx_id = cpor.id
LEFT JOIN LATERAL (
    SELECT
        ptdx.id as patient_dx_id,
        ptdx.dx_id as dx_id,
        dx.name as diagnosis
    FROM jsonb_array_elements(coalesce_if_empty_json(
        coalesce_if_empty_json(
        coalesce_if_empty_json(opi.dx_ids, co.dx_ids), 
        co.dx_ids), 
        '[]')::jsonb) AS elem
    INNER JOIN form_patient_diagnosis ptdx 
        ON ptdx.id = (elem->>'id')::int 
        AND ptdx.archived IS NOT TRUE 
        AND ptdx.deleted IS NOT TRUE 
        AND COALESCE(ptdx.active, 'No') = 'Yes'
    INNER JOIN form_list_diagnosis dx 
        ON dx.code = ptdx.dx_id 
        AND dx.archived IS NOT TRUE 
        AND dx.deleted IS NOT TRUE
    GROUP BY (elem->>'rank')::int, ptdx.id, ptdx.dx_id, dx.name
    ORDER BY (elem->>'rank')::int ASC
    LIMIT 1
) AS dx ON true
LEFT JOIN form_careplan_delivery_tick dt 
    ON dt.id = rxd.delivery_ticket_id
    AND dt.archived IS NOT TRUE
    AND dt.deleted IS NOT TRUE
LEFT JOIN LATERAL (
    SELECT 
        doc.id as document_id,
        doc.name as document_name,
        doc.file_path as document_file_path
    FROM form_document doc
    WHERE
    doc.form_name = 'careplan_order'
    AND doc.type_id = 'Prescription'
    AND doc.patient_id = co.patient_id
    AND doc.form_code = co.code
    AND doc.archived IS NOT TRUE
    AND doc.deleted IS NOT TRUE
    ORDER BY doc.created_on DESC
    LIMIT 1
) doc ON TRUE;

CREATE OR REPLACE VIEW vw_ord_rx_split_view AS
SELECT 
    ord.patient_id,
    ord.careplan_id,
    ord.rx_no,
    ord.document_file_path as file_path,
    ord.document_id,
    CASE
        WHEN ord.rx_template_id = 'Factor'
        AND ord.prescription_provided_in = 'Range' THEN CONCAT(
            format_numeric(
                ord.dose_range_1 :: numeric
            ),
            '-',
            format_numeric(
                ord.dose_range_2 :: numeric
            ),
            ord.dose_unit_id
        )
        WHEN ord.rx_template_id = 'Factor'
        AND ord.prescription_provided_in = 'Variance' THEN CONCAT(
            format_numeric(
                ord.dose :: numeric
            ),
            ord.dose_unit_id,
            ' ±',
            ord.allowed_variance,
            '%'
        )
        ELSE CONCAT(
            format_numeric(
                ord.dose :: numeric
            ),
            ' ',
            ord.dose_unit_id
        )
    END as dose_str,
    ord.is_erx,
    ord.ss_description,
    ord.physician_order_id,
    ord.inventory_id,
    ord.formatted_ndc,
    ord.hcpc_code,
    ord.manufacturer_id,
    ord.sig,
    ord.requires_infusion_time,
    ord.requires_therapy_days,
    ord.patient_param_required,
    ord.weight,
    ord.bsa,
    ord.lesions,
    ord.therapy_days,
    ord.cm2_per_lesion,
    ord.m2_173,
    ord.grams_of_carbohydrate,
    ord.route_id,
    ord.frequency_id,
    ord.frequency_multiplier,
    ord.frequency_label,
    ord.infuse_length,
    ord.infuse_for,
    ord.infuse_time,
    ord.therapy_id,
    ord.status_id,
    ord.onhold_reason,
    CASE WHEN ord.discontinued_date IS NOT NULL THEN TO_CHAR(ord.discontinued_date, 'MM/DD/YYYY') ELSE NULL END as discontinued_date,
    ord.rx_template_id,
    ord.template_type,
    ord.frequency_type,
    ord.frequency_weekly,
    ord.allowed_variance,
    ord.comments,
    ord.order_id,
    ord.item_id_type,
    ord.item_id,
    ord.insurance_id,
    ord.payer_ids as payer_ids_raw,
    ord.dx_ids as dx_ids_raw,
    ord.refills,
    ord.drug_pa_id
FROM vw_order_raw ord;

-- Drop the view that references vw_invoice_claim_response_details
DROP VIEW IF EXISTS vw_rx_invoices;
CREATE OR REPLACE VIEW vw_rx_invoices AS 
SELECT
    rxd.id as working_dispense_id,
    bi.id as invoice_id,
    COALESCE(iva.copay::numeric, 0::numeric) as copay,
    bi.invoice_no
FROM form_careplan_order_rx_disp rxd
INNER JOIN form_careplan_order_rx rx 
    ON rx.id = rxd.rx_id
    AND rxd.archived IS NOT TRUE
    AND rxd.deleted IS NOT TRUE
INNER JOIN form_billing_invoice bi 
    ON bi.master_invoice_no = rxd.master_invoice_no
    AND bi.archived IS NOT TRUE
    AND bi.deleted IS NOT TRUE
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes'
    AND COALESCE(bi.is_master_invoice, 'No') = 'Yes'
INNER JOIN vw_invoice_claim_response_details iva
    ON iva.invoice_id = bi.id
;

CREATE OR REPLACE VIEW vw_rx_order AS
WITH rx_dispense_data AS (
    SELECT DISTINCT ON (rxd.rx_no)
        rxd.rx_id,
        rxd.rx_no,
        rxd.master_invoice_no,
        rxd.id as working_dispense_id,
        rxd.fill_number,
        rxd.delivery_ticket_id,
        rxd.status as fill_status,
        rxd.refills,
        rxd.refills_remaining,
        rxd.doses_allowed,
        rxd.doses_remaining
    FROM form_careplan_order_rx_disp rxd
    WHERE rxd.archived IS NOT TRUE
    AND rxd.deleted IS NOT TRUE
    ORDER BY rxd.rx_no, rxd.id DESC
),
rx_invoice_data AS (
    SELECT 
        dispense.working_dispense_id,
        array_agg(DISTINCT bi.invoice_id) as invoice_ids,
        bool_or(COALESCE(bi.revenue_accepted, 'No') <> 'Yes') as has_unaccepted_claims,
        ROUND(MAX(COALESCE(bi.copay, 0)::numeric), 2) as copay
    FROM rx_dispense_data dispense
    LEFT JOIN vw_invoice_claim_response_details bi 
        ON bi.master_invoice_no = dispense.master_invoice_no
    GROUP BY dispense.working_dispense_id
),
order_item_data AS (
    SELECT
        coi.id,
        coi.rx_no,
        coi.status_id,
        coi.intake_substatus_id,
        coi.billing_method,
        coi.last_event_id,
        coi.last_event_by,
        coi.last_event_datetime,
        coi.dose,
        coi.dose_unit_id,
        coi.frequency_id,
        coi.type_id,
        coi.stat_order,
        coi.patient_id,
        coi.drug_pa_id,
        coi.written_date,
        coi.route_id,
        coi.therapy_id,
        doc.document_file_path,
        doc.document_id,
        doc.document_name
    FROM form_careplan_order_item coi
    LEFT JOIN LATERAL (
    SELECT 
        doc.id as document_id,
        doc.name as document_name,
        doc.file_path as document_file_path
    FROM form_document doc
    WHERE doc.archived IS NOT TRUE
    AND doc.deleted IS NOT TRUE
    AND doc.type_id = 'Prescription'
    AND (
        (coi.id IS NOT NULL 
        AND doc.form_name = 'careplan_order_item'
        AND doc.form_code = coi.code
        AND doc.patient_id = coi.patient_id)
    )
    ORDER BY doc.created_on DESC
    LIMIT 1
    ) doc ON TRUE
    WHERE coi.archived IS NOT TRUE
    AND coi.deleted IS NOT TRUE
    AND coi.status_id IN ('1', '5', '4')
),
orderp_item_data AS (
    SELECT
        copi.id,
        copi.rx_no,
        copi.status_id,
        copi.intake_substatus_id,
        copi.billing_method,
        copi.last_event_id,
        copi.last_event_by,
        copi.last_event_datetime,
        copi.dose,
        copi.dose_unit_id,
        copi.frequency_id,
        copi.type_id,
        copi.stat_order,
        copi.patient_id,
        copi.drug_pa_id,
        copi.written_date,
        copi.route_id,
        copi.start_date,
        copi.stop_date,
        copi.expiration_date,
        copi.therapy_id,
        doc.document_file_path,
        doc.document_id,
        doc.document_name
    FROM form_careplan_orderp_item copi
	INNER JOIN sf_form_careplan_order_to_careplan_orderp_item sfco
    ON sfco.form_careplan_orderp_item_fk = copi.id 
    AND sfco.archive IS NOT TRUE 
    AND sfco.delete IS NOT TRUE 
    INNER JOIN form_careplan_order ord ON ord.id = sfco.form_careplan_order_fk
    AND ord.archived IS NOT TRUE 
    AND ord.deleted IS NOT TRUE 
    LEFT JOIN LATERAL (
    SELECT 
        doc.id as document_id,
        doc.name as document_name,
        doc.file_path as document_file_path
    FROM form_document doc
    WHERE doc.archived IS NOT TRUE
    AND doc.deleted IS NOT TRUE
    AND doc.type_id = 'Prescription'
    AND (
        (ord.id IS NOT NULL 
        AND doc.form_name = 'careplan_order'
        AND doc.form_code = ord.code
        AND doc.patient_id = ord.patient_id)
    )
    ORDER BY doc.created_on DESC
    LIMIT 1
    ) doc ON TRUE
    WHERE copi.archived IS NOT TRUE
    AND copi.deleted IS NOT TRUE
    AND copi.status_id IN ('1', '5', '4')
    )
SELECT
    rx.id as rx_id,
    co.id as order_id,
    co.patient_id,
    co.careplan_id,
    COALESCE(co.site_id, pt.site_id) as site_id,
    COALESCE(co.team_id, pt.team_id) as team_id,
    co.prescriber_id,
    co.physician_id,
    co.referral_source_id,
    co.territory_id,
    pt.status_id as patient_status_id,
    co.commissioned_sales_rep,
    COALESCE(rx.therapy_id, opi.therapy_id, oi.therapy_id) as therapy_id,
    oi.id as careplan_order_item_id,
    opi.id as careplan_orderp_item_id,
    COALESCE(oi.drug_pa_id, opi.drug_pa_id) as pa_id,
    rx.rx_no,
    rx.order_no,
    COALESCE(oi.status_id, opi.status_id) as status_id,
    COALESCE(oi.intake_substatus_id, opi.intake_substatus_id) as intake_substatus_id,
    COALESCE(rx.start_date, opi.start_date, co.start_date) as start_date,
    COALESCE(opi.stop_date, co.stop_date) as stop_date,
    COALESCE(rx.expiration_date, opi.expiration_date, co.expiration_date) as expiration_date,
    rx.template_type,
    CASE
        WHEN rx.dose_type_id = 'Loading' THEN rx.start_fill_number
        WHEN rx.dose_type_id = 'Maintenance' THEN rx.end_fill_number
        ELSE NULL
    END as start_fill_number,
    COALESCE(rxd.fill_number, rx.next_fill_number) as fill_number,
    rx.next_fill_date,
    rx.inventory_id,
    rx.dispense_quantity,
    CASE
        WHEN rx.template_type IN ('IV', 'TPN', 'Compound') THEN rx.next_delivery_date
        ELSE NULL
    END as next_delivery_date,
    CASE
        WHEN rx.template_type IN ('TPN', 'Compound') THEN rx.comp_dsg_fm_code
        ELSE NULL
    END as comp_dsg_fm_code,
    CASE
        WHEN rx.template_type IN ('TPN', 'Compound') THEN rx.compound_type
        ELSE NULL
    END as compound_type,
    CASE
        WHEN rx.template_type IN ('TPN') THEN '3'
        WHEN rx.template_type IN ('Compound') THEN rx.comp_disp_unit
        ELSE NULL
    END as comp_disp_unit,
    CASE
        WHEN rx.template_type IN ('TPN', 'Compound') THEN rx.doses_to_prep
        ELSE NULL
    END as doses_to_prep,
    CASE
        WHEN rx.template_type IN ('TPN', 'Compound') THEN rx.doses_per_container
        ELSE NULL
    END as doses_per_container,
    CASE
        WHEN rx.template_type IN ('TPN', 'Compound') THEN rx.containers_to_prep
        ELSE NULL
    END as containers_to_prep,
    CASE
        WHEN rx.template_type IN ('TPN', 'Compound') THEN rx.container_id
        ELSE NULL
    END as container_id,
    CASE
        WHEN rx.template_type IN ('TPN', 'Compound') THEN rx.vehicle_id
        ELSE NULL
    END as vehicle_id,
    CASE
        WHEN rx.template_type IN ('TPN', 'Compound') THEN rx.volume_per_dose
        ELSE NULL
    END as volume_per_dose,
    CASE
        WHEN rx.template_type IN ('TPN', 'Compound') THEN rx.overfill
        ELSE NULL
    END as overfill,
    CASE
        WHEN rx.template_type IN ('TPN', 'Compound', 'IV') THEN rx.include_cmpd_instr_wo
        ELSE NULL
    END as include_cmpd_instr_wo,
    CASE
        WHEN rx.template_type IN ('TPN', 'Compound', 'IV') THEN rx.auto_compounding_instructions
        ELSE NULL
    END as auto_compounding_instructions,
    CASE
        WHEN rx.template_type IN ('TPN', 'Compound', 'IV') THEN rx.compounding_instructions
        ELSE NULL
    END as compounding_instructions,
    CASE
        WHEN rx.template_type IN ('TPN', 'Compound', 'IV') THEN rx.med_review_wo
        ELSE NULL
    END as med_review_wo,
    rx.day_supply,
    rx.daw_code,
    rx.rx_origin_id,
    COALESCE(rx.written_date, oi.written_date, opi.written_date) as written_date,
    COALESCE(oi.route_id, opi.route_id) as route_id,
    rx.refill_tracking,
    CASE 
        WHEN rx.refill_tracking = 'Refills' THEN rx.refills
        ELSE NULL
    END as refills,
    CASE 
        WHEN rx.refill_tracking = 'Refills' THEN rx.refills_remaining
        ELSE NULL
    END as refills_remaining,
    CASE
        WHEN rx.refill_tracking = 'Doses' THEN rx.doses_allowed
        ELSE NULL
    END as doses_allowed,
    CASE 
        WHEN rx.refill_tracking = 'Doses' THEN rx.doses_remaining
        ELSE NULL
    END as doses_remaining,
    rxd.delivery_ticket_id,
    rxd.fill_status,
    rxd.working_dispense_id,
    rxi.invoice_ids,
    rx.rx_complete,
    rx.rx_verified,
    rx.rx_denied,
    rx.is_refill,
    rx.refill_rx_id,
    rx.auto_dc,
    oi.id as order_item_id,
    opi.id as orderp_item_id,
    COALESCE(oi.type_id, opi.type_id) as type_id,
    rx.is_340b,
    COALESCE(oi.billing_method, opi.billing_method, co.billing_method) as billing_method,
    CASE
        WHEN COALESCE(oi.billing_method, opi.billing_method, co.billing_method) = 'Do Not Bill' THEN NULL
        ELSE 'Yes'
    END as bill,
    ins.id as insurance_id,
    co.bill_supplies,
    co.supplies_insurance_id,
    co.supply_billable_id,
    co.supplies_pa_id,
    dt.service_from as service_from,
    dt.service_to as service_to,
    co.supply_kit_id,
    COALESCE(oi.last_event_id, opi.last_event_id) as last_event_id,
    COALESCE(oi.last_event_by, opi.last_event_by) as last_event_by,
    COALESCE(oi.last_event_datetime, opi.last_event_datetime) as last_event_datetime,
    rx.is_specialty as is_specialty,
    ins.payer_id as payer_id,
    ins.billing_method_id,
    rx.last_through_date,
    rx.created_on,
    rx.created_by,
    rx.updated_on,
    rx.updated_by,
    co.requires_nursing,
    co.nursing_status,
    co.nursing_agency_id,
    co.infusion_suite_id,
    COALESCE(oi.stat_order, opi.stat_order) as stat_order,
    rxi.copay,
    COALESCE(oi.dose, opi.dose) as dose,
    COALESCE(oi.dose_unit_id, opi.dose_unit_id) as dose_unit_id,
    COALESCE(oi.frequency_id, opi.frequency_id) as frequency_id,
    rxi.has_unaccepted_claims,
    COALESCE(oi.document_file_path, opi.document_file_path) as document_file_path,
    COALESCE(oi.document_id, opi.document_id) as document_id,
    COALESCE(oi.document_name, opi.document_name) as document_name,
    rxd.master_invoice_no,
    rx.archived
FROM form_careplan_order_rx rx
INNER JOIN form_patient pt
    ON pt.id = rx.patient_id
    AND pt.archived IS NOT TRUE
    AND pt.deleted IS NOT TRUE
LEFT JOIN order_item_data oi 
    ON oi.rx_no = rx.rx_no 
LEFT JOIN sf_form_careplan_order_to_careplan_order_item sfco 
    ON sfco.form_careplan_order_item_fk = oi.id 
    AND sfco.archive IS NOT TRUE 
    AND sfco.delete IS NOT TRUE 
LEFT JOIN orderp_item_data opi 
    ON opi.rx_no = rx.rx_no 
LEFT JOIN sf_form_careplan_order_to_careplan_orderp_item sfcop 
    ON sfcop.form_careplan_orderp_item_fk = opi.id 
    AND sfcop.archive IS NOT TRUE 
    AND sfcop.delete IS NOT TRUE
LEFT JOIN LATERAL (SELECT
    pins.id,
    pins.payer_id,
    pins.billing_method_id
    FROM form_patient_insurance pins
    WHERE pins.id = get_next_insurance_id(rx.id::integer,NULL::integer, TRUE::boolean)
    AND pins.archived IS NOT TRUE
    AND pins.deleted IS NOT TRUE
    AND COALESCE(pins.active, 'No') = 'Yes'
    LIMIT 1
) ins ON TRUE
INNER JOIN form_careplan_order co 
    ON co.id = COALESCE(sfco.form_careplan_order_fk, sfcop.form_careplan_order_fk) 
    AND co.archived IS NOT TRUE
    AND co.deleted IS NOT TRUE
    AND COALESCE(co.void, 'No') <> 'Yes'
LEFT JOIN rx_dispense_data rxd 
    ON rxd.rx_no = rx.rx_no
LEFT JOIN rx_invoice_data rxi
    ON rxi.working_dispense_id = rxd.working_dispense_id
LEFT JOIN form_careplan_delivery_tick dt 
    ON dt.id = rxd.delivery_ticket_id
    AND dt.archived IS NOT TRUE
    AND dt.deleted IS NOT TRUE
WHERE COALESCE(oi.status_id, opi.status_id) IN ('1', '5', '4');

CREATE OR REPLACE VIEW vw_rx_rental_items AS
SELECT
    co.site_id::integer as site_id,
    cor.patient_id::integer as patient_id,
    co.careplan_id::integer as careplan_id,
    co.id::integer as order_id,
    cor.inventory_id::integer as inventory_id,
    cor.rental_billable::text as rental_billable,
    cor.rental_type::text as rental_type,
    cor.frequency_code::text as frequency_code,
    cor.max_rental_claims::integer as max_rental_claims,
    cor.rental_pa_id::integer as pa_id,
    cor.last_dispense_delivery_tick_id::integer as last_dispense_delivery_tick_id,
    co.billing_method::text as billing_method,
    cor.serial_no::text as serial_no,
    cor.checked_in::text as checked_in,
    cor.last_billed::date as last_billed,
    cor.last_dispense_date::date as last_dispense_date,
    cor.rental_insurance_id::integer as insurance_id,
    cor.payer_id::integer as payer_id,
    cor.no_recurring_billing::text as no_recurring_billing,
    cor.daily_bill_rental::text as daily_bill_rental,
    cor.send_cert::text as send_cert,
    cor.certification_type_code::text as certification_type_code,
    cor.durable_medical_equipment_duration_in_months::integer as durable_medical_equipment_duration_in_months,
    cor.id::integer as rental_id,
    CASE
        WHEN cor.rental_type = 'Purchase' 
        OR (cor.rental_type = 'Rental' AND cor.rental_billable = 'Yes') THEN
            CASE
                WHEN cor.last_dispense_delivery_tick_id IS NULL THEN TRUE
                WHEN (SELECT COUNT(*) FROM form_ledger_charge_line lgl 
                      WHERE lgl.rental_id = cor.id 
                      AND lgl.archived IS NOT TRUE 
                      AND lgl.deleted IS NOT TRUE 
                      AND COALESCE(lgl.void, 'No') <> 'Yes') >= COALESCE(cor.max_rental_claims, 999) THEN FALSE
                WHEN COALESCE(py.no_recurring_billing, 'No') = 'Yes' THEN FALSE
                WHEN COALESCE(py.bill_recur_arrears, 'No') = 'Yes' THEN 
                CASE
                    WHEN (SELECT COUNT(*) FROM form_ledger_charge_line lgl 
                          WHERE lgl.rental_id = cor.id 
                          AND lgl.archived IS NOT TRUE 
                          AND lgl.deleted IS NOT TRUE 
                          AND COALESCE(lgl.void, 'No') <> 'Yes') = 1
                         AND cor.last_billed >= (CURRENT_DATE - INTERVAL '56 days') THEN TRUE
                    WHEN ((SELECT COUNT(*) FROM form_ledger_charge_line lgl 
                           WHERE lgl.rental_id = cor.id 
                           AND lgl.archived IS NOT TRUE 
                           AND lgl.deleted IS NOT TRUE 
                           AND COALESCE(lgl.void, 'No') <> 'Yes') > 2 
                          OR py.bill_recur_arrears IS NULL) 
                         AND cor.last_billed >= (CURRENT_DATE - INTERVAL '28 days') THEN TRUE
                    ELSE FALSE
                END
                WHEN cor.last_billed >= (CURRENT_DATE - INTERVAL '28 days') THEN TRUE
                ELSE FALSE
            END
        ELSE FALSE
    END as bill_due,
    CASE
        WHEN (SELECT COUNT(*) FROM form_careplan_dt_item dti 
              WHERE dti.rental_id = cor.id 
              AND dti.archived IS NOT TRUE 
              AND dti.deleted IS NOT TRUE) = 0 
             AND cor.serial_no IS NULL THEN TRUE
        ELSE FALSE
    END as dispense_due
FROM form_careplan_order co
INNER JOIN sf_form_careplan_order_to_careplan_order_rental sfro 
    ON sfro.form_careplan_order_fk = co.id
    AND sfro.archive IS NOT TRUE
    AND sfro.delete IS NOT TRUE
INNER JOIN form_careplan_order_rental cor 
    ON cor.id = sfro.form_careplan_order_rental_fk
    AND cor.archived IS NOT TRUE
    AND cor.deleted IS NOT TRUE
LEFT JOIN form_patient_insurance ins 
    ON ins.id = cor.rental_insurance_id
    AND ins.archived IS NOT TRUE
    AND ins.deleted IS NOT TRUE
LEFT JOIN form_payer py 
    ON py.id = ins.payer_id
    AND py.archived IS NOT TRUE
    AND py.deleted IS NOT TRUE

UNION

SELECT
    co.site_id::integer as site_id,
    co.patient_id::integer as patient_id,
    co.careplan_id::integer as careplan_id,
    co.id::integer as order_id,
    cos.inventory_id::integer as inventory_id,
    NULL::text as rental_billable,
    cos.rental_type::text as rental_type,
    cos.frequency_code::text as frequency_code,
    NULL::integer as max_rental_claims,
    cos.supplies_pa_id::integer as pa_id,
    NULL::integer as last_dispense_delivery_tick_id,
    cos.billing_method::text as billing_method,
    NULL::text as serial_no,
    NULL::text as checked_in,
    NULL::date as last_billed,
    NULL::date as last_dispense_date,
    cos.insurance_id::integer as insurance_id,
    py.id::integer as payer_id,
    NULL::text as no_recurring_billing,
    NULL::text as daily_bill_rental,
    NULL::text as send_cert,
    NULL::text as certification_type_code,
    NULL::integer as durable_medical_equipment_duration_in_months,
    cos.id::integer as rental_id,
    NULL::boolean as bill_due,
    NULL::boolean as dispense_due
FROM form_careplan_order co
INNER JOIN sf_form_careplan_order_to_careplan_orders_item sfro 
    ON sfro.form_careplan_orders_item_fk = co.id
    AND sfro.archive IS NOT TRUE
    AND sfro.delete IS NOT TRUE
INNER JOIN form_careplan_orders_item cos 
    ON cos.id = sfro.form_careplan_orders_item_fk
    AND cos.archived IS NOT TRUE
    AND cos.deleted IS NOT TRUE
LEFT JOIN form_patient_insurance ins 
    ON ins.id = cos.insurance_id
    AND ins.archived IS NOT TRUE
    AND ins.deleted IS NOT TRUE
LEFT JOIN form_payer py
    ON py.id = ins.payer_id
    AND py.archived IS NOT TRUE
    AND py.deleted IS NOT TRUE;

CREATE OR REPLACE VIEW vw_rx_supplies_information AS
SELECT
    rx.patient_id,
    rx.careplan_id,
    rx.site_id,
    rx.rx_id,
    cos.inventory_id,
    CASE
        WHEN cos.billing_method IN ('Insurance', 'Self Pay') THEN 'Yes' 
        ELSE NULL
    END as billable,
    cos.part_of_kit,
    cos.one_time_only,
    cos.dispense_quantity::numeric as dispense_quantity,
    cos.insurance_id,
    cos.billing_method,
    ins.payer_id,
    binv.billable_code_id as hcpc_code,
    binv.id as billable_inventory_id,
    COALESCE(co.supplies_pa_id, cos.supplies_pa_id) as pa_id,
    cos.id as supply_order_id,
    COALESCE(cos.day_supply, rx.day_supply) as day_supply,
    cos.frequency_code::text as frequency_code,
    cos.rental_type::text as rental_type
FROM vw_rx_order rx
INNER JOIN form_careplan_order co 
    ON co.id = rx.order_id
    AND co.archived IS NOT TRUE
    AND co.deleted IS NOT TRUE
    AND COALESCE(co.void, 'No') <> 'Yes'
INNER JOIN sf_form_careplan_order_to_careplan_orders_item sfcoi 
    ON sfcoi.form_careplan_orders_item_fk = co.id
    AND sfcoi.archive IS NOT TRUE
    AND sfcoi.delete IS NOT TRUE
INNER JOIN form_careplan_orders_item cos 
    ON cos.id = sfcoi.form_careplan_orders_item_fk
    AND cos.archived IS NOT TRUE
    AND cos.deleted IS NOT TRUE
    AND cos.associated_rx_id = rx.rx_id
    AND cos.status_id IN ('1', '5')
LEFT JOIN form_patient_insurance ins 
    ON ins.id = cos.insurance_id
    AND ins.archived IS NOT TRUE
    AND ins.deleted IS NOT TRUE
LEFT JOIN form_inventory binv
    ON binv.id = co.supply_billable_id
    AND binv.archived IS NOT TRUE
    AND binv.deleted IS NOT TRUE
WHERE COALESCE(cos.one_time_only,'No') <> 'Yes' 
    OR NOT EXISTS (
        SELECT 1 
        FROM form_careplan_dt_item dti
        INNER JOIN form_careplan_delivery_tick dt 
            ON dt.ticket_no = dti.ticket_no
            AND dt.archived IS NOT TRUE
            AND dt.deleted IS NOT TRUE
            AND COALESCE(dt.void, 'No') <> 'Yes'
        WHERE dti.inventory_id = cos.inventory_id 
        AND dti.archived IS NOT TRUE 
        AND dti.deleted IS NOT TRUE 
        AND dti.patient_id = rx.patient_id 
        AND dti.careplan_id = rx.careplan_id
    );

CREATE OR REPLACE VIEW vw_supply_kit_items AS
SELECT
    gfiskis.form_site_fk,
    skt.name,
    skt.billable_id,
    ski.inventory_id,
    ski.dispense_quantity::numeric as dispense_quantity,
    ski.bill,
    ski.part_of_kit,
    ski.one_time_only,
    skt.id as supply_kit_id,
    ski.id as supply_kit_item_id
FROM form_inventory_supply_kit skt 
INNER JOIN sf_form_inventory_supply_kit_to_inventory_sk_item sfski 
    ON sfski.form_inventory_supply_kit_fk = skt.id 
    AND sfski.archive IS NOT TRUE 
    AND sfski.delete IS NOT TRUE
INNER JOIN form_inventory_sk_item ski 
    ON ski.id = sfski.form_inventory_sk_item_fk 
    AND ski.archived IS NOT TRUE 
    AND ski.deleted IS NOT TRUE
INNER JOIN gr_form_inventory_supply_kit_site_id_to_site_id gfiskis 
    ON gfiskis.form_inventory_supply_kit_fk = skt.id
WHERE skt.archived IS NOT TRUE 
AND skt.deleted IS NOT TRUE;

CREATE OR REPLACE VIEW vw_patient_primary_prescriber AS
SELECT
    pt.id as patient_id,
    pp.id as prescriber_id,
    pp.physician_id,
    pp.supervising,
    pp.ordering
FROM form_patient pt
LEFT JOIN LATERAL (
    SELECT
        pp.id,
        pp.physician_id,
        pp.supervising,
        pp.ordering
    FROM form_patient_prescriber pp
    WHERE pp.patient_id = pt.id
    AND pp.archived IS NOT TRUE
    AND pp.deleted IS NOT TRUE
    ORDER BY CASE WHEN COALESCE(pp.is_primary, 'No') = 'Yes' THEN 0 ELSE 1 END ASC
    LIMIT 1
) pp ON true
WHERE pt.archived IS NOT TRUE 
AND pt.deleted IS NOT TRUE;

DROP VIEW IF EXISTS vw_order CASCADE;
CREATE OR REPLACE VIEW vw_order AS
    SELECT 
    CASE 
        WHEN rx_id IS NOT NULL THEN rx_id
        WHEN item_id IS NOT NULL THEN item_id
        ELSE order_id
    END as form_id,
    CASE 
        WHEN rx_id IS NOT NULL THEN 'careplan_order_rx'
        WHEN item_id IS NOT NULL THEN item_id_type
        ELSE 'careplan_order'
    END as form_name,
    ord.*
    FROM vw_order_raw ord
    WHERE ord.status_id IN ('1', '5', '4');

CREATE OR REPLACE VIEW vw_open_delivery_ticket_items AS
SELECT 
	dt.id as delivery_ticket_id,
	dt.patient_id,
	dt.site_id,
	dti.inventory_id,
	dti.rx_id
FROM form_careplan_delivery_tick dt
INNER JOIN form_careplan_dt_item dti ON dt.ticket_no = dt.ticket_no
AND dti.archived IS NOT TRUE 
AND dti.deleted IS NOT TRUE 
WHERE dt.status IN ('delivery_ticket', 'ready_to_fill', 'order_ver', 'pending_conf') 
AND dt.archived IS NOT TRUE 
AND dt.deleted IS NOT TRUE;


CREATE OR REPLACE VIEW vw_ss_messages AS
WITH message_base AS (
  SELECT
    ss.id as form_id,
    ss.message_id::text as message_id,
    ss.related_message_id::text as related_message_id,
    'ss_message' as form_name,
    ss.direction::text as direction,
    string_to_array(trim(both '{}' from ss.status_icons::text), ',')::text[] as status_icons,
    ss.sent_dt::timestamp as received_datettime_raw,
    CASE
      WHEN ss.sent_dt::timestamp IS NOT NULL THEN TO_CHAR(ss.sent_dt::timestamp, 'MM/DD/YYYY HH:MI AM')
      ELSE NULL::text
    END::text as received_datetime,
    ss.message_type::text as message_type,
    ss.patient_id::integer as patient_id,
    CONCAT(ss.patient_last_name::text, ', ', ss.patient_first_name::text) as patient,
    ss.physician_id::integer as prescriber_id,
    CONCAT(ss.prescriber_last_name::text, ', ', ss.prescriber_first_name::text) as provider,
    ss.fdb_id::text as fdb_id,
    ss.description::text as drug,
    CASE
      WHEN ss.message_type = 'NewRx' THEN ss.sig::text
      WHEN ss.message_type = 'RxRenewalRequest' THEN 'Refill Requested'
      WHEN ss.message_type = 'RxRenewalResponse' THEN ss.renewal_status::text
      WHEN ss.message_type = 'RxChangeRequest' THEN sscc.name::text
      WHEN ss.message_type = 'RxChangeResponse' THEN COALESCE(ss.chg_status::text, sscc.name::text || ' Response')
      WHEN ss.message_type = 'CancelRx' THEN 'Cancel Request'
      WHEN ss.message_type = 'CancelRxResponse' THEN ss.cancel_status::text
    END::text as message
  FROM form_ss_message ss
  LEFT JOIN form_list_ss_chg_code sscc
    ON sscc.code::text = ss.chg_type_id::text
    AND sscc.archived IS NOT TRUE
    AND sscc.deleted IS NOT TRUE
  WHERE ss.archived IS NOT TRUE
    AND ss.deleted IS NOT TRUE
),
top_level_messages AS (
  SELECT message_id, direction, form_id
  FROM message_base mb1
  WHERE 
    -- Outbound messages are always top-level
    mb1.direction = 'OUT'
    OR
    -- Inbound messages that aren't related to any other message
    (mb1.direction = 'IN' AND NOT EXISTS (
      SELECT 1 
      FROM message_base mb2 
      WHERE mb2.related_message_id = mb1.message_id
    ))
),
thread_children AS (
  SELECT 
    parent.message_id as parent_message_id,
    json_agg(
      json_build_object(
        'form_id', child.form_id,
        'form_name', child.form_name,
        'direction', child.direction,
        'status_icons', child.status_icons,
        'received_datettime_raw', child.received_datettime_raw,
        'received_datetime', child.received_datetime,
        'message_type', child.message_type,
        'patient_id', child.patient_id,
        'patient', child.patient,
        'prescriber_id', child.prescriber_id,
        'provider', child.provider,
        'fdb_id', child.fdb_id,
        'drug', child.drug,
        'message', child.message
      ) 
      ORDER BY child.received_datettime_raw DESC -- newest first
    ) as children_json
  FROM message_base parent
  INNER JOIN message_base child 
    ON parent.message_id = child.related_message_id
  WHERE 
    -- Only show outbound children for inbound parent messages
    parent.direction = 'IN' AND child.direction = 'OUT'
  GROUP BY parent.message_id
)
SELECT 
  mb.form_id,
  mb.form_name,
  mb.direction,
  mb.status_icons,
  mb.received_datettime_raw,
  mb.received_datetime,
  mb.message_type,
  mb.patient_id,
  mb.patient,
  mb.prescriber_id,
  mb.provider,
  mb.fdb_id,
  mb.drug,
  mb.message,
  COALESCE(tc.children_json, '[]'::json) as children
FROM message_base mb
INNER JOIN top_level_messages tlm 
  ON mb.form_id = tlm.form_id
LEFT JOIN thread_children tc 
  ON mb.message_id = tc.parent_message_id
ORDER BY mb.received_datettime_raw DESC;