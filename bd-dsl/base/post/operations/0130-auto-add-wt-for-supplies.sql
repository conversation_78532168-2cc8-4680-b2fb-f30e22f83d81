CREATE OR REPLACE FUNCTION auto_add_wt_for_supplies_on_ticket()
RETURNS TRIGGER AS $$
BEGIN
    BEGIN
        -- Check if the status is 'ready_to_fill'
        IF NEW.status = 'ready_to_fill' THEN
            -- Insert form_careplan_dt_wt_pulled records for items that need scanning and don't have a pulled record
            INSERT INTO form_careplan_dt_wt_pulled (
                patient_id,
                careplan_id,
                site_id,
                status,
                item_verified,
                item_pulled_from_stock,
                inventory_id,
                dispensed_unit_id,
                ticket_no,
                ticket_item_no,
                dispensed_quantity,
                quantity_needed,
                archived,
                deleted,
                created_by,
                created_on
            )
            SELECT 
                NEW.patient_id,
                NEW.careplan_id,
                NEW.site_id,
                NEW.status,
                'Yes',
                'Yes',
                i.inventory_id,
                'each',
                NEW.ticket_no,
                i.ticket_item_no,
                i.dispense_quantity,
                i.dispense_quantity,
                FALSE,
                FALSE,
                i.created_by,
                i.created_on
            FROM form_careplan_dt_item i
            LEFT JOIN form_careplan_dt_wt_pulled p
                ON i.ticket_no = p.ticket_no
                AND i.ticket_item_no = p.ticket_item_no
                AND p.archived = FALSE
                AND p.deleted = FALSE
                AND COALESCE(p.void, 'No') = 'No'
            WHERE i.ticket_no = NEW.ticket_no
                AND i.archived = FALSE
                AND i.deleted = FALSE
                AND i.needs_scanned IS NULL
                AND p.ticket_no IS NULL;
        END IF;
        
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Error in auto_add_wt_for_supplies_on_ticket: %', SQLERRM;
    END;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger on form_careplan_delivery_tick
CREATE OR REPLACE TRIGGER trigger_auto_add_wt_for_supplies_on_ticket
AFTER UPDATE ON form_careplan_delivery_tick
FOR EACH ROW
EXECUTE FUNCTION auto_add_wt_for_supplies_on_ticket();

CREATE OR REPLACE FUNCTION auto_add_wt_for_supplies_on_item()
RETURNS TRIGGER AS $$
BEGIN
    BEGIN
        RAISE LOG 'auto_add_wt_for_supplies_on_item: dt_item_id: % needs_scanned: %', NEW.id, NEW.needs_scanned;

        -- Check if the item needs scanning and is not archived or deleted
        IF NEW.needs_scanned IS NULL AND NEW.archived IS NOT TRUE AND NEW.deleted IS NOT TRUE THEN
            -- Check if there is an associated delivery ticket with status 'ready_to_fill' or later
            DECLARE ticket_status TEXT;
            BEGIN
                SELECT status INTO ticket_status
                FROM form_careplan_delivery_tick
                WHERE ticket_no = NEW.ticket_no;
                
                RAISE LOG 'auto_add_wt_for_supplies_on_item: ticket_status: %', ticket_status;
                IF ticket_status IN ('ready_to_fill', 'order_ver', 'pending_conf') THEN
                    -- Check if a pulled record already exists
                    DECLARE existing_pulled BOOLEAN;
                    BEGIN
                        RAISE LOG 'auto_add_wt_for_supplies_on_item: checking for existing pulled record';
                        SELECT TRUE INTO existing_pulled
                        FROM form_careplan_dt_wt_pulled p
                        WHERE p.ticket_no = NEW.ticket_no
                            AND p.ticket_item_no = NEW.ticket_item_no
                            AND p.archived = FALSE
                            AND p.deleted = FALSE
                            AND COALESCE(p.void, 'No') = 'No'
                        LIMIT 1;

                        IF NOT FOUND THEN
                            RAISE LOG 'auto_add_wt_for_supplies_on_item: no existing pulled record found';
                            -- Insert a new pulled record if none exists
                            INSERT INTO form_careplan_dt_wt_pulled (
                                patient_id,
                                careplan_id,
                                site_id,
                                status,
                                item_verified,
                                item_pulled_from_stock,
                                inventory_id,
                                dispensed_unit_id,
                                ticket_no,
                                ticket_item_no,
                                dispensed_quantity,
                                quantity_needed,
                                archived,
                                deleted,
                                created_by,
                                created_on
                            )
                            VALUES (
                                NEW.patient_id,
                                NEW.careplan_id,
                                NEW.site_id,
                                NEW.status,
                                'Yes',
                                'Yes',
                                NEW.inventory_id,
                                'each',
                                NEW.ticket_no,
                                NEW.ticket_item_no,
                                NEW.dispense_quantity,
                                NEW.dispense_quantity,
                                FALSE,
                                FALSE,
                                NEW.created_by,
                                NEW.created_on
                            );
                        END IF;
                    END;
                END IF;
            END;
        END IF;
        
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION '%', SQLERRM;
    END;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION auto_void_wt_pulled_on_item_archive()
RETURNS TRIGGER AS $$
BEGIN
    BEGIN
        -- Check if the item is being archived or deleted
        IF NEW.archived IS TRUE OR NEW.deleted IS TRUE THEN
            -- Update related form_careplan_dt_wt_pulled records to set void and archived to true
            UPDATE form_careplan_dt_wt_pulled
            SET void = 'Yes',
                archived = TRUE
            WHERE ticket_no = NEW.ticket_no
                AND ticket_item_no = NEW.ticket_item_no
                AND archived IS NOT TRUE
                AND deleted IS NOT TRUE
                AND COALESCE(void, 'No') = 'No';
        END IF;
        
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Error in auto_void_wt_pulled_on_item_archive: %', SQLERRM;
    END;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger on form_careplan_dt_item for auto adding pulled records
CREATE OR REPLACE TRIGGER trigger_auto_add_wt_for_supplies_on_item
AFTER INSERT OR UPDATE ON form_careplan_dt_item
FOR EACH ROW
EXECUTE FUNCTION auto_add_wt_for_supplies_on_item();

-- Create the trigger on form_careplan_dt_item for voiding pulled records on archive or delete
CREATE OR REPLACE TRIGGER trigger_auto_void_wt_pulled_on_item_archive
AFTER UPDATE ON form_careplan_dt_item
FOR EACH ROW
EXECUTE FUNCTION auto_void_wt_pulled_on_item_archive();