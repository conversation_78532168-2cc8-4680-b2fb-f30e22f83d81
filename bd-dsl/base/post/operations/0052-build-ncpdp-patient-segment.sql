
CREATE OR REPLACE FUNCTION build_patient_ncpdp_segment(
  p_patient_id integer,
  p_insurance_id integer,
  p_previous_payer_id integer,
  p_site_id integer,
  p_transaction_code text DEFAULT 'B1'
) RETURNS ncpdp_patient AS $BODY$
DECLARE
  v_start_time timestamp;
  v_execution_time interval;
  v_result ncpdp_patient;
  v_error_message text;
  v_params jsonb;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'insurance_id', p_insurance_id,
    'patient_id', p_patient_id,
    'site_id', p_site_id,
    'transaction_code', p_transaction_code,
    'previous_payer_id', p_previous_payer_id
  );

  BEGIN  -- Start exception block
    -- Log function call
    PERFORM log_billing_function(
      'build_patient_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );
    -- Validate required parameters
    IF p_patient_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Patient ID cannot be null',
            'Validating parameters in build_patient_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_patient_ncpdp_segment',
                'patient_id', p_patient_id,
                'insurance_id', p_insurance_id,
                'site_id', p_site_id
            )
        );
        RAISE EXCEPTION 'Patient ID cannot be null';
    END IF;

    IF p_insurance_id IS NULL AND p_transaction_code NOT IN ('E1') THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Insurance ID cannot be null',
            'Validating parameters in build_patient_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_patient_ncpdp_segment',
                'patient_id', p_patient_id,
                'insurance_id', p_insurance_id,
                'site_id', p_site_id
            )
        );
        RAISE EXCEPTION 'Insurance ID cannot be null';
    END IF;

    IF p_site_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Site ID cannot be null',
            'Validating parameters in build_patient_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_patient_ncpdp_segment',
                'patient_id', p_patient_id,
                'insurance_id', p_insurance_id,
                'site_id', p_site_id
            )
        );
        RAISE EXCEPTION 'Site ID cannot be null';
    END IF;

    IF p_transaction_code IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Transaction code cannot be null',
            'Validating parameters in build_patient_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_patient_ncpdp_segment',
                'patient_id', p_patient_id,
                'insurance_id', p_insurance_id,
                'site_id', p_site_id
            )
        );
        RAISE EXCEPTION 'Transaction code cannot be null';
    END IF;

    IF p_insurance_id IS NULL THEN
      SELECT 
        p_transaction_code::text AS transaction_code,
        p_patient_id::integer AS patient_id,
        NULL::integer AS insurance_id,
        NULL::text AS pt_rel_code,
        NULL::integer AS payer_type_id,
        NULL::text AS patient_id_qualifier,
        NULL::text AS patient_claim_id,
        TO_CHAR(pt.dob, 'MM/DD/YYYY')::text AS patient_date_of_birth,
        CASE 
          WHEN pt.gender = 'Male' THEN '1'
          WHEN pt.gender = 'Female' THEN '2'
          ELSE '0'
        END::text AS patient_gender_code,
        pt.firstname::text AS patient_first_name,
        pt.lastname::text AS patient_last_name,
        NULL::text AS patient_phone,
        NULL::text AS patient_email_address,
        TRIM(CONCAT(ptpa.street, ' ', ptpa.street2))::text AS patient_street_address,
        ptpa.city::text AS patient_city_address,
        ptpa.state_id::text AS patient_state,
        ptpa.zip::text AS patient_zip,
        NULL::integer AS place_of_service,
        NULL::integer AS patient_residence,
        NULL::text AS pregnancy_indicator
      INTO v_result
      FROM form_patient pt
      LEFT JOIN LATERAL(
        SELECT 
          street,
          street2,
          city,
          state_id,
          zip
        FROM form_patient_address pa 
        WHERE pa.patient_id = pt.id 
          AND pa.archived IS NOT TRUE 
          AND pa.deleted IS NOT TRUE
        ORDER BY 
          CASE
            WHEN address_type = 'Home' THEN 0
            WHEN address_type = 'Shipping' THEN 1 
            ELSE 2
          END 
        LIMIT 1 
      ) ptpa ON TRUE 
      WHERE pt.id = p_patient_id 
        AND pt.archived IS NOT TRUE 
        AND pt.deleted IS NOT TRUE;
    ELSE
        -- Get insurance settings once
      WITH insurance_info AS (
          SELECT * FROM get_insurance_claim_settings(p_insurance_id, p_site_id, p_previous_payer_id)
        )
      SELECT
        p_transaction_code::text AS transaction_code,
        p_patient_id::integer AS patient_id,
        p_insurance_id::integer AS insurance_id,
        ins.pt_rel_code::text AS pt_rel_code,
        ins.type_id::text AS payer_type_id,
        CASE 
          WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3') THEN ins.patient_id_qualifier
          ELSE NULL
        END::text AS patient_id_qualifier,
        CASE 
          WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3') THEN ins.patient_claim_id
          ELSE NULL
        END::text AS patient_claim_id,
        TO_CHAR(pt.dob, 'MM/DD/YYYY')::text AS patient_date_of_birth,
        CASE 
          WHEN pt.gender = 'Male' THEN '1'
          WHEN pt.gender = 'Female' THEN '2'
          ELSE '0'
        END::text AS patient_gender_code,
        pt.firstname::text AS patient_first_name,
        pt.lastname::text AS patient_last_name,
        CASE 
          WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3') THEN COALESCE(pt.phone_cell, pt.phone_home, pt.phone_work)
          ELSE NULL
        END::text AS patient_phone,
        CASE 
          WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3') AND COALESCE(ins.send_pt_email, 'No') = 'Yes' THEN pt.email
          ELSE NULL
        END::text AS patient_email_address,
        TRIM(CONCAT(ptpa.street, ' ', ptpa.street2))::text AS patient_street_address,
        ptpa.city::text AS patient_city_address,
        ptpa.state_id::text AS patient_state,
        ptpa.zip::text AS patient_zip,
        ins.default_service_place_id::text AS place_of_service,
        ins.default_place_of_res_id::text AS patient_residence,
        CASE 
          WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3') THEN 
            CASE
              WHEN (pt.gender = 'Female' OR pt.gender IS NULL) AND dx.id IS NOT NULL THEN '2' 
              WHEN pt.gender = 'Male' THEN '1'
            END
          ELSE NULL
        END::text AS pregnancy_indicator
      INTO v_result
      FROM form_patient pt
      LEFT JOIN form_patient_diagnosis dx ON dx.patient_id = pt.id 
        AND dx.archived IS NOT TRUE 
        AND dx.deleted IS NOT TRUE 
        AND COALESCE(dx.active, 'No') = 'Yes' 
        AND is_pregnancy_diagnosis(dx.dx_id) IS TRUE
      CROSS JOIN insurance_info ins
      LEFT JOIN LATERAL(
        SELECT 
          street,
          street2,
          city,
          state_id,
          zip
        FROM form_patient_address pa 
        WHERE pa.patient_id = pt.id 
          AND pa.archived IS NOT TRUE 
          AND pa.deleted IS NOT TRUE
        ORDER BY 
          CASE
            WHEN address_type = 'Home' THEN 0
            WHEN address_type = 'Shipping' THEN 1 
            ELSE 2
          END 
        LIMIT 1 
      ) ptpa ON TRUE 
      WHERE pt.id = p_patient_id 
        AND pt.archived IS NOT TRUE 
        AND pt.deleted IS NOT TRUE;
    END IF;

    -- Validate we got a result
    IF v_result IS NULL THEN
      INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
      ) VALUES (
        'Failed to build patient segment',
        'Validating result in build_patient_ncpdp_segment',
        'FUNCTION',
        current_schema(),
        'form_ncpdp',
        jsonb_build_object(
          'function_name', 'build_patient_ncpdp_segment',
          'patient_id', p_patient_id
        )
      );
      RAISE EXCEPTION 'Failed to build patient segment for patient_id: %', p_patient_id;
    END IF;

    -- Log successful completion
    PERFORM log_billing_function(
      'build_patient_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    -- Log error
    v_error_message := SQLERRM;
    
    -- Log to billing error log
    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_patient_ncpdp_segment',
      'FUNCTION',
      current_schema(),
      'form_ncpdp',
      jsonb_build_object(
        'function_name', 'build_patient_ncpdp_segment',
        'patient_id', p_patient_id
      )
    );

    -- Log to NCPDP function log
    PERFORM log_billing_function(
      'build_patient_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;