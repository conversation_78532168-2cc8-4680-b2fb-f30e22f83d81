CREATE
OR REPLACE FUNCTION crx_upsert_fdb_drug_brand_group() RETURNS text AS $$ BEGIN
INSERT INTO
    form_list_fdb_brd_grp (
        code,
        name,
        auto_name,
        brand_name_id,
        billing_unit_id,
        storage_id,
        dea_schedule_id
    )
SELECT
    DISTINCT ON (f.medid) f.medid,
    f.med_medid_desc,
    f.med_medid_desc,
    n.ndc,
    CASE
        WHEN n.df = '1' THEN 'each'
        WHEN n.df = '2' THEN 'mL'
        WHEN n.df = '3' THEN 'gram'
    END AS billing_unit_id,
    (
        SELECT
            fdbst.storage_id
        FROM
            form_list_fdb_ndc_attribute ndcattr
            INNER JOIN form_list_fdb_storage_to_clara fdbst ON ndcattr.ndc_attribute_value = fdbst.attribute_val
        WHERE
            ndcattr.ndc = n.ndc
            AND ndcattr.ndc_attribute_type_cd = '55'
            AND ndcattr.ndc_attribute_value IS NOT NULL
        LIMIT
            1
    ) AS storage_id,
    CASE
        WHEN n.dea = '1' THEN 'C48672'
        WHEN n.dea = '2' THEN 'C48675'
        WHEN n.dea = '3' THEN 'C48676'
        WHEN n.dea = '4' THEN 'C48677'
        WHEN n.dea = '5' THEN 'C48679'
    END AS dea_schedule_id
FROM
    form_list_fdb_med_table f
    JOIN form_list_fdb_ndc_to_medid ndc_medid ON f.medid = ndc_medid.medid
    JOIN form_list_fdb_ndc n ON ndc_medid.ndc = n.ndc
WHERE
    n.inpcki <> '1'
    AND n.outpcki = '1' ON CONFLICT (code) DO
UPDATE
SET
    name = EXCLUDED.name,
    auto_name = EXCLUDED.auto_name,
    brand_name_id = EXCLUDED.brand_name_id,
    billing_unit_id = EXCLUDED.billing_unit_id,
    storage_id = EXCLUDED.storage_id,
    dea_schedule_id = EXCLUDED.dea_schedule_id;
RETURN 'Upsert and updates completed successfully';

END;

$$ LANGUAGE plpgsql;