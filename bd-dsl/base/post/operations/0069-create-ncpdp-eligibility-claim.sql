-- Function to sync discontinued status from order items to rx records
DROP FUNCTION build_ncpdp_eligibility_claim;
CREATE OR REPLACE FUNCTION build_ncpdp_eligibility_claim(
  p_patient_id integer,
  p_site_id integer,
  p_insurance_id integer DEFAULT NULL,
  p_payer_id integer DEFAULT NULL
) RETURNS JSONB AS $BODY$
DECLARE
  v_patient_segment json;
  v_pharmacy_id text;
  v_pharmacy_qualifier text;
  v_prescriber_id integer := NULL;
  v_prescriber_segment json := NULL;
  v_insurance_segment json;
  v_pharmacy_segment json := NULL;
  v_e1_prov_required text;
  v_start_time timestamp;
  v_execution_time interval;
  v_result jsonb;
  v_error_message text;
  v_bin_number text;
  v_process_control_number text;
  v_params jsonb;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'insurance_id', p_insurance_id,
    'payer_id', p_payer_id,
    'patient_id', p_patient_id,
    'site_id', p_site_id
  );

  RAISE LOG 'build_ncpdp_eligibility_claim: p_patient_id: % p_site_id: % p_insurance_id: % p_payer_id: %', p_patient_id, p_site_id, p_insurance_id, p_payer_id;

  BEGIN  -- Start exception block
    -- Log function call
    PERFORM log_billing_function(
      'build_ncpdp_eligibility_claim'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    IF p_patient_id IS NULL THEN
      INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
      ) VALUES (
        'Patient ID is required',
        'Validating required parameters in build_ncpdp_eligibility_claim',
        'FUNCTION',
        current_schema(),
        'form_ncpdp',
        v_params
      );
      RAISE EXCEPTION 'Patient ID is required';
    END IF;

    -- Validate required parameters
    IF p_insurance_id IS NULL OR p_payer_id IS NULL OR p_site_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Insurance ID, Payer ID, Site ID, and Prescriber ID are required',
            'Validating required parameters in build_ncpdp_eligibility_claim',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );
        RAISE EXCEPTION 'Site ID is required';
    END IF;

    -- Patient segment - Create JSON directly from patient segment data
    WITH patient_data AS (
      SELECT * FROM build_patient_ncpdp_segment(p_patient_id, p_insurance_id, NULL::integer, p_site_id, 'E1')
    )
    SELECT json_agg(
      json_build_object(
        'transaction_code', p.transaction_code,
        'patient_id', p.patient_id,
        'insurance_id', p.insurance_id,
        'pt_rel_code', p.pt_rel_code,
        'payer_type_id', p.payer_type_id,
        'patient_id_qualifier', p.patient_id_qualifier,
        'patient_claim_id', p.patient_claim_id,
        'patient_date_of_birth', p.patient_date_of_birth,
        'patient_gender_code', p.patient_gender_code,
        'patient_first_name', p.patient_first_name,
        'patient_last_name', p.patient_last_name,
        'patient_phone', p.patient_phone,
        'patient_email_address', p.patient_email_address,
        'patient_street_address', p.patient_street_address,
        'patient_city_address', p.patient_city_address,
        'patient_state', p.patient_state,
        'patient_zip', p.patient_zip,
        'place_of_service', p.place_of_service,
        'patient_residence', p.patient_residence,
        'pregnancy_indicator', p.pregnancy_indicator
      )
    ) INTO v_patient_segment 
    FROM patient_data p;
    
    PERFORM validate_subform_array(v_patient_segment::json, 'segment_patient', false);

    -- Build and validate segments based on transaction code
    SELECT bin, pcn, ncpdp_pharmacy_qualifier_id, ncpdp_pharmacy_id, ncpdp_e1_prov_required 
    INTO v_bin_number, v_process_control_number, v_pharmacy_qualifier, v_pharmacy_id, v_e1_prov_required
    FROM get_insurance_claim_settings(p_insurance_id, p_site_id, NULL::integer);

    IF COALESCE(v_e1_prov_required, 'No') = 'Yes' THEN
      SELECT prescriber_id INTO v_prescriber_id
      FROM vw_patient_primary_prescriber pp
      WHERE pp.patient_id = p_patient_id;

      IF v_prescriber_id IS NULL THEN
          INSERT INTO billing_error_log (
              error_message,
              error_context,
              error_type,
              schema_name,
              table_name,
              additional_details
          ) VALUES (
              'No primary prescriber found for patient but is required for E1 by payer',
              'Validating required parameters in build_ncpdp_eligibility_claim',
              'FUNCTION',
              current_schema(),
              'form_ncpdp',
              v_params
          );
          RAISE EXCEPTION 'No primary prescriber found for patient but is required for E1 by payer';
      END IF;

      -- Prescriber segment - Create JSON directly from prescriber segment data
      WITH prescriber_data AS (
        SELECT * FROM build_prescriber_ncpdp_segment(p_patient_id, v_prescriber_id, p_insurance_id, p_site_id, 'E1', NULL::text)
      )
      SELECT json_agg(
        json_build_object(
          'transaction_code', p.transaction_code,
          'physician_id', p.physician_id,
          'dr_id_qualifier', p.dr_id_qualifier,
          'dr_id', p.dr_id,
          'dr_last_name', p.dr_last_name,
          'dr_first_name', p.dr_first_name,
          'dr_street_address', p.dr_street_address,
          'dr_city', p.dr_city,
          'dr_state', p.dr_state,
          'dr_zip', p.dr_zip,
          'dr_phone', p.dr_phone,
          'primary_physician_id', p.primary_physician_id,
          'pri_dr_id_qualifier', p.pri_dr_id_qualifier,
          'pri_dr_id', p.pri_dr_id,
          'pri_dr_last_name', p.pri_dr_last_name
        )
      ) INTO v_prescriber_segment
      FROM prescriber_data p;
      
      PERFORM validate_subform_array(v_prescriber_segment::json, 'segment_prescriber', false);
    ELSE
      v_prescriber_segment := NULL;
    END IF;

    -- Insurance segment always required - Create JSON directly from insurance segment data
    WITH insurance_data AS (
      SELECT * FROM build_insurance_ncpdp_segment(p_insurance_id, p_site_id, 'E1', NULL::integer)
    )
    SELECT json_agg(
      json_build_object(
        'transaction_code', i.transaction_code,
        'insurance_id', i.insurance_id,
        'payer_type_id', i.payer_type_id,
        'card_holder_id', i.card_holder_id,
        'pt_rel_code', i.pt_rel_code,
        'card_holder_first_name', i.card_holder_first_name,
        'card_holder_last_name', i.card_holder_last_name,
        'plan_id', i.plan_id,
        'group_id', i.group_id,
        'person_code', i.person_code,
        'home_plan', i.home_plan,
        'medigap_id', i.medigap_id,
        'mcd_indicator', i.mcd_indicator,
        'dr_accept_indicator', i.dr_accept_indicator,
        'elig_clar_code', i.elig_clar_code,
        'partd_facility', i.partd_facility,
        'mcd_id_no', i.mcd_id_no
      )
    ) INTO v_insurance_segment
    FROM insurance_data i;
    
    PERFORM validate_subform_array(v_insurance_segment::json, 'segment_insurance', false);

    -- Pharmacy Segment - Create JSON directly from pharmacy segment data
    WITH pharmacy_data AS (
      SELECT * FROM build_pharmacy_ncpdp_segment(p_insurance_id, NULL, p_patient_id, p_site_id)
    )
    SELECT json_agg(
      json_build_object(
        'site_id', p.site_id,
        'provider_id_qualifier', p.provider_id_qualifier,
        'provider_id', p.provider_id
      )
    ) INTO v_pharmacy_segment 
    FROM pharmacy_data p;
    
    PERFORM validate_subform_array(v_pharmacy_segment::json, 'segment_pharmacy', false);

    IF v_pharmacy_qualifier IS NULL OR v_pharmacy_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'No pharmacy qualifier or ID found for insurance',
            'Validating required parameters in build_ncpdp_eligibility_claim',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );
        RAISE EXCEPTION 'No pharmacy qualifier or ID found for insurance';
    END IF;

    -- Build final result
    SELECT jsonb_strip_nulls(jsonb_build_object(
      'site_id', p_site_id,
      'patient_id', p_patient_id,
      'insurance_id', p_insurance_id,
      'payer_id', p_payer_id,
      'software_vendor_id', 'CLARARX',
      'version_number', 'D0',
      'transaction_code', 'E1',
      'bin_number', v_bin_number,
      'svc_prov_id_qualifier', v_pharmacy_qualifier,
      'svc_prov_id', v_pharmacy_id,
      'hide_prescriber_in_e1', CASE WHEN COALESCE(v_e1_prov_required, 'No') = 'Yes' THEN 'Yes' ELSE 'No' END,
      'process_control_number', v_process_control_number,
      'date_of_service', TO_CHAR(get_site_timestamp(p_site_id, TRUE)::date, 'MM/DD/YYYY'),
      'segment_patient', v_patient_segment,
      'segment_prescriber', v_prescriber_segment,
      'segment_pharmacy', v_pharmacy_segment,
      'segment_insurance', v_insurance_segment
    )) INTO v_result;

    -- Log successful completion
    PERFORM log_billing_function(
      'build_ncpdp_eligibility_claim'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;
  EXCEPTION WHEN OTHERS THEN
    -- Log error
    v_error_message := SQLERRM;
    
    -- Log to billing error log
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        v_error_message,
        'Exception in build_ncpdp_eligibility_claim',
        'FUNCTION',
        current_schema(),
        'form_ncpdp',
        v_params
    );

    -- Log to NCPDP function log
    PERFORM log_billing_function(
      'build_ncpdp_eligibility_claim'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

DROP FUNCTION build_ncpdp_cardfinder_claim;
CREATE OR REPLACE FUNCTION build_ncpdp_cardfinder_claim(
  p_site_id integer,
  p_patient_id integer,
  p_first_name text,
  p_last_name text,
  p_date_of_birth date,
  p_gender text,
  p_patient_zip text DEFAULT NULL,
  p_cardholder_no text DEFAULT NULL,
  p_cardfinder_comm_only BOOLEAN DEFAULT FALSE
) RETURNS JSONB AS $BODY$
DECLARE
  v_patient_segment json;
  v_insurance_segment json;
  v_start_time timestamp;
  v_execution_time interval;
  v_result jsonb;
  v_error_message text;
  v_bin_number text;
  v_process_control_number text;
  v_params jsonb;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'first_name', p_first_name,
    'last_name', p_last_name,
    'date_of_birth', p_date_of_birth,
    'gender', p_gender,
    'cardholder_no', p_cardholder_no,
    'cardfinder_comm_only', p_cardfinder_comm_only
  );

  BEGIN  -- Start exception block
    -- Log function call
    PERFORM log_billing_function(
      'build_ncpdp_cardfinder_claim'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    IF p_site_id IS NULL OR p_patient_id IS NULL OR p_first_name IS NULL OR p_last_name IS NULL OR p_date_of_birth IS NULL THEN
      INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
      ) VALUES (
        'Site ID, patient ID, first name, last name, and date of birth are required',
        'Validating required parameters in build_ncpdp_cardfinder_claim',
        'FUNCTION',
        current_schema(),
        'form_ncpdp',
        v_params
      );
      RAISE EXCEPTION 'Site ID, patient ID, first name, last name, and date of birth are required';
    END IF;

    -- Create patient segment JSON directly without using ROW()
    v_patient_segment := json_build_array(
      json_strip_nulls(
        json_build_object(
          'transaction_code', 'E1',
          'patient_id', p_patient_id,
          'patient_date_of_birth', TO_CHAR(p_date_of_birth, 'MM/DD/YYYY'),
          'patient_gender_code', CASE 
            WHEN p_gender = 'Male' THEN '1'
            WHEN p_gender = 'Female' THEN '2'
            ELSE '0'
          END,
          'patient_first_name', p_first_name,
          'patient_last_name', p_last_name,
          'patient_zip', p_patient_zip
        )
      )
    );

    -- Set PCN based on type
    IF p_cardfinder_comm_only THEN
      v_process_control_number := 'COMMCF';
    ELSE
      v_process_control_number := 'CARDFINDER';
    END IF;

    v_bin_number := '610144';

    -- Cardfinder segment - Create JSON directly without using ROW()
    IF p_cardholder_no IS NOT NULL THEN
      v_insurance_segment := json_build_array(
        json_build_object(
          'card_holder_id', p_cardholder_no
        )
      );
    ELSE
      v_insurance_segment := NULL;
    END IF;

    -- Build final result
    SELECT jsonb_strip_nulls(jsonb_build_object(
      'site_id', p_site_id,
      'patient_id', p_patient_id,
      'software_vendor_id', 'CLARARX',
      'version_number', 'D0',
      'transaction_code', 'E1',
      'bin_number', v_bin_number,
      'svc_prov_id_qualifier', '01',
      'svc_prov_id', st.npi,
      'process_control_number', v_process_control_number,
      'date_of_service', TO_CHAR(get_site_timestamp(p_site_id, TRUE)::date, 'MM/DD/YYYY'),
      'segment_patient', v_patient_segment,
      'segment_insurance', v_insurance_segment
    )) INTO v_result
    FROM form_site st
    WHERE st.id = p_site_id;
  
    -- Log successful completion
    PERFORM log_billing_function(
      'build_ncpdp_cardfinder_claim'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;
  EXCEPTION WHEN OTHERS THEN
    -- Log error
    v_error_message := SQLERRM;
    
    -- Log to billing error log
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        v_error_message,
        'Exception in build_ncpdp_cardfinder_claim',
        'FUNCTION',
        current_schema(),
        'form_ncpdp',
        v_params
    );

    -- Log to NCPDP function log
    PERFORM log_billing_function(
      'build_ncpdp_cardfinder_claim'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;
