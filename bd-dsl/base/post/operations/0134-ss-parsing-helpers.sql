-- Surescripts Parsing Helper Functions

CREATE OR REPLACE FUNCTION _parse_ss_header_data(p_header_json JSONB)
RETURNS ss_extracted_header_type LANGUAGE plpgsql AS $$
DECLARE
    v_extracted_header ss_extracted_header_type;
BEGIN
    v_extracted_header.digital_signature_indicator :=
        CASE COALESCE(p_header_json#>>'{DigitalSignature,DigitalSignatureIndicator}', 'false')
            WHEN 'Yes' THEN 'true'
            WHEN 'No' THEN 'false'
            WHEN 'true' THEN 'true'
            WHEN 'false' THEN 'false'
            ELSE 'false'
        END;
    v_extracted_header.to_val := p_header_json#>>'{To}';
    v_extracted_header.from_val := p_header_json#>>'{From}';
    v_extracted_header.sender_software_developer := p_header_json#>>'{SenderSoftware,SenderSoftwareDeveloper}';
    v_extracted_header.sender_software_product := p_header_json#>>'{SenderSoftware,SenderSoftwareProduct}';
    v_extracted_header.sender_software_version := p_header_json#>>'{SenderSoftware,SenderSoftwareVersionRelease}';
    v_extracted_header.order_group_no := p_header_json#>>'{PrescriberOrderGroup,OrderGroupNumber}';
    v_extracted_header.rx_group_no := p_header_json#>>'{RxReferenceOrderGroup,OrderGroupNumber}';
    v_extracted_header.order_group_icnt := (p_header_json#>>'{PrescriberOrderGroup,ItemCountInOrderGroup}')::INTEGER;
    v_extracted_header.rx_group_icnt := (p_header_json#>>'{RxReferenceOrderGroup,ItemCountInOrderGroup}')::INTEGER;
    v_extracted_header.order_group_tcnt := (p_header_json#>>'{PrescriberOrderGroup,TotalCountForOrderGroup}')::INTEGER;
    v_extracted_header.rx_group_tcnt := (p_header_json#>>'{RxReferenceOrderGroup,TotalCountForOrderGroup}')::INTEGER;
    v_extracted_header.order_group_reason_code := p_header_json#>>'{PrescriberOrderGroup,OrderGroupReason}';
    v_extracted_header.rx_group_reason_code := p_header_json#>>'{RxReferenceOrderGroup,OrderGroupReason}';
    v_extracted_header.message_id_header := p_header_json#>>'{MessageID}';
    v_extracted_header.related_message_id := p_header_json#>>'{RelatesToMessageID}';
    v_extracted_header.prescriber_order_number := p_header_json#>>'{PrescriberOrderNumber}';
    v_extracted_header.pharmacy_rx_no := p_header_json#>>'{RxReferenceNumber}';
    v_extracted_header.sent_dt := (p_header_json#>>'{SentTime}')::TIMESTAMP;
    RETURN v_extracted_header;
END;
$$;

CREATE OR REPLACE FUNCTION _parse_ss_patient_data(p_patient_json JSONB, p_full_message_body_json JSONB, p_message_type_name TEXT)
RETURNS ss_extracted_patient_type LANGUAGE plpgsql AS $$
DECLARE
    v_extracted_patient ss_extracted_patient_type;
BEGIN
    IF p_patient_json IS NOT NULL AND jsonb_typeof(p_patient_json) = 'object' THEN
        v_extracted_patient.first_name := p_patient_json#>>'{HumanPatient,Name,FirstName}';
        v_extracted_patient.last_name := p_patient_json#>>'{HumanPatient,Name,LastName}';
        v_extracted_patient.middle_name := p_patient_json#>>'{HumanPatient,Name,MiddleName}';
        IF v_extracted_patient.last_name IS NOT NULL AND v_extracted_patient.first_name IS NOT NULL THEN
            v_extracted_patient.name_display := v_extracted_patient.last_name || ', ' || v_extracted_patient.first_name;
            IF v_extracted_patient.middle_name IS NOT NULL THEN
                 v_extracted_patient.name_display := v_extracted_patient.name_display || ' ' || v_extracted_patient.middle_name;
            END IF;
        END IF;
        v_extracted_patient.dob := (NULLIF(COALESCE(p_patient_json#>>'{HumanPatient,DateOfBirth,Date}', p_patient_json#>>'{HumanPatient,DateOfBirth,DateTime}'),''))::DATE;
        v_extracted_patient.gender := p_patient_json#>>'{HumanPatient,Gender}';
        v_extracted_patient.ssn := p_patient_json#>>'{HumanPatient,Identification,SocialSecurity}';
        v_extracted_patient.home_street_1 := p_patient_json#>>'{HumanPatient,Address,AddressLine1}';
        v_extracted_patient.home_street_2 := p_patient_json#>>'{HumanPatient,Address,AddressLine2}';
        v_extracted_patient.home_city := p_patient_json#>>'{HumanPatient,Address,City}';
        v_extracted_patient.home_state_code := p_patient_json#>>'{HumanPatient,Address,StateProvince}';
        v_extracted_patient.home_zip := p_patient_json#>>'{HumanPatient,Address,PostalCode}';
        v_extracted_patient.phone := p_patient_json#>>'{HumanPatient,CommunicationNumbers,PrimaryTelephone,Number}';
        v_extracted_patient.mrn := p_patient_json#>>'{HumanPatient,Identification,MedicalRecordIdentificationNumberEHR}';
        IF v_extracted_patient.mrn IS NULL THEN -- Fallback for older path
             v_extracted_patient.mrn := p_patient_json#>>'{HumanPatient,Identification,MedicalRecordIdentificationNumber}';
        END IF;
        v_extracted_patient.medicare := p_patient_json#>>'{HumanPatient,Identification,MedicareNumber}';
        v_extracted_patient.medicaid := p_patient_json#>>'{HumanPatient,Identification,MedicaidNumber}';
        v_extracted_patient.rems := p_patient_json#>>'{HumanPatient,Identification,REMSPatientID}';
    END IF;
    v_extracted_patient.nka := jsonb_extract_path_text(p_full_message_body_json, p_message_type_name,'AllergyOrAdverseEvent','NoKnownAllergies');
    RETURN v_extracted_patient;
END;
$$;

CREATE OR REPLACE FUNCTION _parse_ss_prescriber_data(p_prescriber_json JSONB)
RETURNS ss_extracted_prescriber_info_type LANGUAGE plpgsql AS $$
DECLARE
    v_extracted_prescriber ss_extracted_prescriber_info_type;
    v_non_vet_json JSONB;
    v_temp_licenses TEXT[];
    v_loc_json JSONB;
BEGIN
    v_non_vet_json := p_prescriber_json->'NonVeterinarian';
    IF v_non_vet_json IS NOT NULL AND jsonb_typeof(v_non_vet_json) = 'object' THEN
        v_extracted_prescriber.npi := v_non_vet_json#>>'{Identification,NPI}';
        v_extracted_prescriber.dea := v_non_vet_json#>>'{Identification,DEANumber}';
        v_extracted_prescriber.rems := v_non_vet_json#>>'{Identification,REMSHealthcareProviderEnrollmentID}';
        IF v_extracted_prescriber.rems IS NULL THEN -- Fallback for older REMSPrescriberID path
             v_extracted_prescriber.rems := v_non_vet_json#>>'{Identification,REMSPrescriberID}';
        END IF;
        v_extracted_prescriber.state_cs_lic := v_non_vet_json#>>'{Identification,StateControlledSubstanceNumber}';
        v_extracted_prescriber.medicare := v_non_vet_json#>>'{Identification,MedicareNumber}';
        v_extracted_prescriber.medicaid := v_non_vet_json#>>'{Identification,MedicaidNumber}';
        v_extracted_prescriber.state_lic := v_non_vet_json#>>'{Identification,StateLicenseNumber}';
        v_extracted_prescriber.certificate_to_prescribe := v_non_vet_json#>>'{Identification,CertificateToPrescribe}';
        IF v_extracted_prescriber.certificate_to_prescribe IS NULL THEN -- Fallback for XNumber
            v_extracted_prescriber.certificate_to_prescribe := v_non_vet_json#>>'{Identification,CertificateToPrescribeXNumber}';
        END IF;
        v_extracted_prescriber.waiver_2000_id := v_non_vet_json#>>'{Identification,Data2000WaiverID}';
         IF v_extracted_prescriber.waiver_2000_id IS NULL THEN -- Fallback for older DrugAbuseTreatmentAct2000WaiverID path
            v_extracted_prescriber.waiver_2000_id := v_non_vet_json#>>'{Identification,DrugAbuseTreatmentAct2000WaiverID}';
        END IF;
        v_extracted_prescriber.spec_code := v_non_vet_json#>>'{Specialty}';
        v_extracted_prescriber.last_name := v_non_vet_json#>>'{Name,LastName}';
        v_extracted_prescriber.first_name := v_non_vet_json#>>'{Name,FirstName}';
        IF v_extracted_prescriber.last_name IS NOT NULL AND v_extracted_prescriber.first_name IS NOT NULL THEN
            v_extracted_prescriber.name_display := v_extracted_prescriber.last_name || ', ' || v_extracted_prescriber.first_name;
        END IF;
        v_extracted_prescriber.address_1 := v_non_vet_json#>>'{Address,AddressLine1}';
        v_extracted_prescriber.address_2 := v_non_vet_json#>>'{Address,AddressLine2}';
        v_extracted_prescriber.city := v_non_vet_json#>>'{Address,City}';
        v_extracted_prescriber.state_code := v_non_vet_json#>>'{Address,StateProvince}';
        v_extracted_prescriber.zip := v_non_vet_json#>>'{Address,PostalCode}';
        v_extracted_prescriber.phone := v_non_vet_json#>>'{CommunicationNumbers,PrimaryTelephone,Number}';
        v_extracted_prescriber.extension := v_non_vet_json#>>'{CommunicationNumbers,PrimaryTelephone,Extension}';
        v_extracted_prescriber.fax := v_non_vet_json#>>'{CommunicationNumbers,Fax,Number}';
        v_temp_licenses := ARRAY[]::TEXT[];
        IF v_extracted_prescriber.npi IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'NPI'); END IF;
        IF v_extracted_prescriber.dea IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'DEA'); END IF;
        IF v_extracted_prescriber.rems IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'REMS'); END IF;
        IF v_extracted_prescriber.state_cs_lic IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'STATECS'); END IF;
        IF v_extracted_prescriber.state_lic IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'STATE'); END IF;
        IF v_extracted_prescriber.medicare IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'MEDICARE'); END IF;
        IF v_extracted_prescriber.medicaid IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'MEDICAID'); END IF;
        IF v_extracted_prescriber.certificate_to_prescribe IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'CERTIFICATE'); END IF;
        IF v_extracted_prescriber.waiver_2000_id IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'NADEAN'); END IF;
        v_extracted_prescriber.has_license := v_temp_licenses;
        -- Flatten location fields directly here
        v_loc_json := v_non_vet_json->'PracticeLocation';
        IF v_loc_json IS NOT NULL AND jsonb_typeof(v_loc_json) = 'object' THEN
            v_extracted_prescriber.loc_name := v_loc_json#>>'{BusinessName}';
            v_extracted_prescriber.loc_ncpdp_id := v_loc_json#>>'{Identification,NCPDPID}';
            v_extracted_prescriber.loc_dea := v_loc_json#>>'{Identification,DEANumber}';
            v_extracted_prescriber.loc_rems := v_loc_json#>>'{Identification,REMSHealthcareSettingEnrollmentID}';
            v_extracted_prescriber.loc_state_cs_lic := v_loc_json#>>'{Identification,StateControlledSubstanceNumber}';
            v_extracted_prescriber.loc_medicare := v_loc_json#>>'{Identification,MedicareNumber}';
            v_extracted_prescriber.loc_medicaid := v_loc_json#>>'{Identification,MedicaidNumber}';
            v_extracted_prescriber.loc_state_lic := v_loc_json#>>'{Identification,StateLicenseNumber}';
            -- Build loc_has_license array
            v_extracted_prescriber.loc_has_license := ARRAY[]::TEXT[];
            IF v_extracted_prescriber.loc_ncpdp_id IS NOT NULL THEN v_extracted_prescriber.loc_has_license := array_append(v_extracted_prescriber.loc_has_license, 'NCPDPID'); END IF;
            IF v_extracted_prescriber.loc_dea IS NOT NULL THEN v_extracted_prescriber.loc_has_license := array_append(v_extracted_prescriber.loc_has_license, 'DEA'); END IF;
            IF v_extracted_prescriber.loc_rems IS NOT NULL THEN v_extracted_prescriber.loc_has_license := array_append(v_extracted_prescriber.loc_has_license, 'REMS'); END IF;
            IF v_extracted_prescriber.loc_state_cs_lic IS NOT NULL THEN v_extracted_prescriber.loc_has_license := array_append(v_extracted_prescriber.loc_has_license, 'STATECS'); END IF;
            IF v_extracted_prescriber.loc_medicare IS NOT NULL THEN v_extracted_prescriber.loc_has_license := array_append(v_extracted_prescriber.loc_has_license, 'MEDICARE'); END IF;
            IF v_extracted_prescriber.loc_medicaid IS NOT NULL THEN v_extracted_prescriber.loc_has_license := array_append(v_extracted_prescriber.loc_has_license, 'MEDICAID'); END IF;
            IF v_extracted_prescriber.loc_state_lic IS NOT NULL THEN v_extracted_prescriber.loc_has_license := array_append(v_extracted_prescriber.loc_has_license, 'STATE'); END IF;
        END IF;
        -- Agent assignment
        v_extracted_prescriber.agent_first_name := v_non_vet_json#>>'{PrescriberAgent,Name,FirstName}';
        v_extracted_prescriber.agent_last_name := v_non_vet_json#>>'{PrescriberAgent,Name,LastName}';
        IF v_extracted_prescriber.agent_first_name IS NULL AND v_extracted_prescriber.agent_last_name IS NULL THEN -- Fallback for older AgentContact path
            v_extracted_prescriber.agent_first_name := v_non_vet_json#>>'{AgentContact,Name,FirstName}';
            v_extracted_prescriber.agent_last_name := v_non_vet_json#>>'{AgentContact,Name,LastName}';
        END IF;
    END IF;
    RETURN v_extracted_prescriber;
END;
$$;

CREATE OR REPLACE FUNCTION _parse_ss_supervisor_data(p_supervisor_json JSONB)
RETURNS ss_extracted_supervisor_type LANGUAGE plpgsql AS $$
DECLARE
    v_extracted_supervisor ss_extracted_supervisor_type;
    v_non_vet_json JSONB;
    v_temp_licenses TEXT[];
BEGIN
    v_non_vet_json := p_supervisor_json->'NonVeterinarian';
    IF v_non_vet_json IS NOT NULL AND jsonb_typeof(v_non_vet_json) = 'object' THEN
        v_extracted_supervisor.last_name := v_non_vet_json#>>'{Name,LastName}';
        v_extracted_supervisor.first_name := v_non_vet_json#>>'{Name,FirstName}';
        v_extracted_supervisor.npi := v_non_vet_json#>>'{Identification,NPI}';
        v_extracted_supervisor.dea := v_non_vet_json#>>'{Identification,DEANumber}';
        v_extracted_supervisor.rems := v_non_vet_json#>>'{Identification,REMSHealthcareSettingEnrollmentID}'; -- Path in XSD for Supervisor ID
        v_extracted_supervisor.state_cs_lic := v_non_vet_json#>>'{Identification,StateControlledSubstanceNumber}';
        v_extracted_supervisor.medicare := v_non_vet_json#>>'{Identification,MedicareNumber}';
        v_extracted_supervisor.medicaid := v_non_vet_json#>>'{Identification,MedicaidNumber}';
        v_extracted_supervisor.state_lic := v_non_vet_json#>>'{Identification,StateLicenseNumber}';
        v_extracted_supervisor.certificate_to_prescribe := v_non_vet_json#>>'{Identification,CertificateToPrescribe}';
        v_extracted_supervisor.waiver_2000_id := v_non_vet_json#>>'{Identification,Data2000WaiverID}';
        v_extracted_supervisor.spec_code := v_non_vet_json#>>'{Specialty}';
        v_extracted_supervisor.phone := v_non_vet_json#>>'{CommunicationNumbers,PrimaryTelephone,Number}';
        v_extracted_supervisor.extension := v_non_vet_json#>>'{CommunicationNumbers,PrimaryTelephone,Extension}';
        v_extracted_supervisor.fax := v_non_vet_json#>>'{CommunicationNumbers,Fax,Number}';

        v_temp_licenses := ARRAY[]::TEXT[];
        IF v_extracted_supervisor.npi IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'NPI'); END IF;
        IF v_extracted_supervisor.dea IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'DEA'); END IF;
        IF v_extracted_supervisor.rems IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'REMS'); END IF;
        IF v_extracted_supervisor.state_cs_lic IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'STATECS'); END IF;
        IF v_extracted_supervisor.state_lic IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'STATE'); END IF;
        IF v_extracted_supervisor.medicare IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'MEDICARE'); END IF;
        IF v_extracted_supervisor.medicaid IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'MEDICAID'); END IF;
        IF v_extracted_supervisor.certificate_to_prescribe IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'CERTIFICATE'); END IF;
        IF v_extracted_supervisor.waiver_2000_id IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'NADEAN'); END IF;
        v_extracted_supervisor.has_license := v_temp_licenses;
    END IF;
    RETURN v_extracted_supervisor;
END;
$$;

CREATE OR REPLACE FUNCTION _parse_ss_fu_prescriber_data(p_fu_prescriber_json JSONB)
RETURNS ss_extracted_fu_prescriber_type LANGUAGE plpgsql AS $$
DECLARE
    v_extracted_fu_prescriber ss_extracted_fu_prescriber_type;
    v_non_vet_json JSONB;
    v_temp_licenses TEXT[];
BEGIN
    v_non_vet_json := p_fu_prescriber_json->'NonVeterinarian';
    IF v_non_vet_json IS NOT NULL AND jsonb_typeof(v_non_vet_json) = 'object' THEN
        v_extracted_fu_prescriber.last_name := v_non_vet_json#>>'{Name,LastName}';
        v_extracted_fu_prescriber.first_name := v_non_vet_json#>>'{Name,FirstName}';
        v_extracted_fu_prescriber.address_1 := v_non_vet_json#>>'{Address,AddressLine1}';
        v_extracted_fu_prescriber.address_2 := v_non_vet_json#>>'{Address,AddressLine2}';
        v_extracted_fu_prescriber.city := v_non_vet_json#>>'{Address,City}';
        v_extracted_fu_prescriber.state_code := v_non_vet_json#>>'{Address,StateProvince}';
        v_extracted_fu_prescriber.zip := v_non_vet_json#>>'{Address,PostalCode}';
        v_extracted_fu_prescriber.phone := v_non_vet_json#>>'{CommunicationNumbers,PrimaryTelephone,Number}';
        v_extracted_fu_prescriber.extension := v_non_vet_json#>>'{CommunicationNumbers,PrimaryTelephone,Extension}';
        v_extracted_fu_prescriber.fax := v_non_vet_json#>>'{CommunicationNumbers,Fax,Number}';
        v_extracted_fu_prescriber.npi := v_non_vet_json#>>'{Identification,NPI}';
        v_extracted_fu_prescriber.dea := v_non_vet_json#>>'{Identification,DEANumber}';
        v_extracted_fu_prescriber.rems := v_non_vet_json#>>'{Identification,REMSHealthcareSettingEnrollmentID}';
        v_extracted_fu_prescriber.state_cs_lic := v_non_vet_json#>>'{Identification,StateControlledSubstanceNumber}';
        v_extracted_fu_prescriber.medicare := v_non_vet_json#>>'{Identification,MedicareNumber}';
        v_extracted_fu_prescriber.medicaid := v_non_vet_json#>>'{Identification,MedicaidNumber}';
        v_extracted_fu_prescriber.state_lic := v_non_vet_json#>>'{Identification,StateLicenseNumber}';
        v_extracted_fu_prescriber.certificate_to_prescribe := v_non_vet_json#>>'{Identification,CertificateToPrescribe}';
        v_extracted_fu_prescriber.waiver_2000_id := v_non_vet_json#>>'{Identification,Data2000WaiverID}';
        v_extracted_fu_prescriber.spec_code := v_non_vet_json#>>'{Specialty}';

        v_temp_licenses := ARRAY[]::TEXT[];
        IF v_extracted_fu_prescriber.npi IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'NPI'); END IF;
        IF v_extracted_fu_prescriber.dea IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'DEA'); END IF;
        IF v_extracted_fu_prescriber.rems IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'REMS'); END IF;
        IF v_extracted_fu_prescriber.state_cs_lic IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'STATECS'); END IF;
        IF v_extracted_fu_prescriber.state_lic IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'STATE'); END IF;
        IF v_extracted_fu_prescriber.medicare IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'MEDICARE'); END IF;
        IF v_extracted_fu_prescriber.medicaid IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'MEDICAID'); END IF;
        IF v_extracted_fu_prescriber.certificate_to_prescribe IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'CERTIFICATE'); END IF;
        IF v_extracted_fu_prescriber.waiver_2000_id IS NOT NULL THEN v_temp_licenses := array_append(v_temp_licenses, 'NADEAN'); END IF;
        v_extracted_fu_prescriber.has_license := v_temp_licenses;
    END IF;
    RETURN v_extracted_fu_prescriber;
END;
$$;

CREATE OR REPLACE FUNCTION _parse_ss_allergies(p_allergies_container_json JSONB)
RETURNS ss_allergy_type[] LANGUAGE plpgsql AS $$
DECLARE
    v_allergies_array ss_allergy_type[] := ARRAY[]::ss_allergy_type[];
    v_allergy_item_json JSONB;
    v_temp_allergy ss_allergy_type;
    v_allergies_json JSONB;
    v_parsed_allergies JSONB;
BEGIN
    IF p_allergies_container_json IS NULL OR jsonb_typeof(p_allergies_container_json) = 'null' THEN
        RETURN v_allergies_array;
    END IF;

    v_allergies_json := p_allergies_container_json->'Allergies';

    IF v_allergies_json IS NOT NULL AND jsonb_typeof(v_allergies_json) <> 'null' THEN
       -- Convert object to array if necessary
        IF jsonb_typeof(v_allergies_json) = 'object' THEN
            RAISE LOG '_parse_ss_allergies: Input is an object, converting to array.';
            v_parsed_allergies := jsonb_build_array(v_allergies_json);
        ELSE
            v_parsed_allergies := v_allergies_json;
        END IF;

        FOR v_allergy_item_json IN SELECT * FROM jsonb_array_elements(ensure_jsonb_array(v_parsed_allergies))
        LOOP
            v_temp_allergy.source                      := v_allergy_item_json#>>'{SourceOfInformation}';
            v_temp_allergy.effective_date            := (NULLIF(COALESCE(v_allergy_item_json#>>'{EffectiveDate,Date}', v_allergy_item_json#>>'{EffectiveDate,DateTime}'),''))::DATE;
            v_temp_allergy.expiration_date           := (NULLIF(COALESCE(v_allergy_item_json#>>'{ExpirationDate,Date}', v_allergy_item_json#>>'{ExpirationDate,DateTime}'),''))::DATE;
            v_temp_allergy.adverse_event_code_id     := v_allergy_item_json#>>'{AdverseEvent,Code}';
            v_temp_allergy.adverse_event_text        := v_allergy_item_json#>>'{AdverseEvent,Text}';
            v_temp_allergy.drug_product_code         := v_allergy_item_json#>>'{DrugProductCoded,Code}';
            v_temp_allergy.drug_product_qualifier_id := v_allergy_item_json#>>'{DrugProductCoded,Qualifier}';
            v_temp_allergy.drug_product_text         := v_allergy_item_json#>>'{DrugProductCoded,Text}';
            v_temp_allergy.reaction_code_id          := v_allergy_item_json#>>'{ReactionCoded,Code}';
            v_temp_allergy.reaction_text             := v_allergy_item_json#>>'{ReactionCoded,Text}';
            v_temp_allergy.severity_code_id          := v_allergy_item_json#>>'{SeverityCoded,Code}';
            v_temp_allergy.severity_text             := v_allergy_item_json#>>'{SeverityCoded,Text}';
            v_allergies_array := array_append(v_allergies_array, v_temp_allergy);
        END LOOP;
    END IF;
    RETURN v_allergies_array;
END;
$$;

CREATE OR REPLACE FUNCTION _parse_ss_benefits(p_benefits_json JSONB, p_resolved_patient_id INTEGER)
RETURNS ss_benefit_type[] LANGUAGE plpgsql AS $$
DECLARE
    v_benefits_array ss_benefit_type[] := ARRAY[]::ss_benefit_type[];
    v_benefit_item_json JSONB;
    v_temp_benefit ss_benefit_type;
    v_parsed_benefits JSONB;
BEGIN
    IF p_benefits_json IS NULL OR jsonb_typeof(p_benefits_json) = 'null' THEN
        RETURN v_benefits_array;
    END IF;

   -- Convert object to array if necessary
    IF jsonb_typeof(p_benefits_json) = 'object' THEN
        RAISE LOG '_parse_ss_benefits: Input is an object, converting to array.';
        v_parsed_benefits := jsonb_build_array(p_benefits_json);
    ELSE
        v_parsed_benefits := p_benefits_json;
    END IF;

    FOR v_benefit_item_json IN SELECT * FROM jsonb_array_elements(ensure_jsonb_array(v_parsed_benefits))
    LOOP
        v_temp_benefit.payer_type_id          := v_benefit_item_json#>>'{PayerType}';
        v_temp_benefit.payer_level            := v_benefit_item_json#>>'{PayerResponsibilityCode}';
        v_temp_benefit.payer_name             := v_benefit_item_json#>>'{PayerName}';
        v_temp_benefit.pbm_participant_id     := v_benefit_item_json#>>'{PayerIdentification,PayerID}';
        v_temp_benefit.bin                    := v_benefit_item_json#>>'{PayerIdentification,IINNumber}';
        v_temp_benefit.pcn                    := v_benefit_item_json#>>'{PayerIdentification,ProcessorIdentificationNumber}';
        v_temp_benefit.naic_id                := v_benefit_item_json#>>'{PayerIdentification,NAICCode}';
        v_temp_benefit.hp_id                  := v_benefit_item_json#>>'{PayerIdentification,StandardUniqueHealthPlanIdentifier}';
        v_temp_benefit.person_code            := v_benefit_item_json#>>'{PersonCode}';
        v_temp_benefit.group_id               := v_benefit_item_json#>>'{GroupID}';
        v_temp_benefit.group_name             := v_benefit_item_json#>>'{GroupName}';
        v_temp_benefit.pbm_member_id          := v_benefit_item_json#>>'{PBMMemberID}';
        v_temp_benefit.relationship_code      := v_benefit_item_json#>>'{PatientRelationshipCode}';
        v_temp_benefit.cardholder_id          := v_benefit_item_json#>>'{CardholderID}';
        v_temp_benefit.cardholder_first_name  := v_benefit_item_json#>>'{CardHolderName,FirstName}';
        v_temp_benefit.cardholder_last_name   := v_benefit_item_json#>>'{CardHolderName,LastName}';
        v_temp_benefit.payer_phone            := v_benefit_item_json#>>'{CommunicationNumbers,PrimaryTelephone,Number}';
        v_temp_benefit.payer_fax              := v_benefit_item_json#>>'{CommunicationNumbers,Fax,Number}';
        v_temp_benefit.prohibit_renewal_request := CASE (v_benefit_item_json#>>'{ProhibitRenewalRequest}')
                                                        WHEN 'true' THEN 'true'
                                                        WHEN 'Y' THEN 'true'
                                                        WHEN 'false' THEN 'false'
                                                        WHEN 'N' THEN 'false'
                                                        ELSE NULL
                                                    END;

        IF v_temp_benefit.bin IS NOT NULL AND v_temp_benefit.pcn IS NOT NULL THEN
            SELECT p.id INTO v_temp_benefit.payer_id FROM form_payer p
            WHERE p.bin = v_temp_benefit.bin AND p.pcn = v_temp_benefit.pcn AND p.deleted IS NOT TRUE AND p.archived IS NOT TRUE LIMIT 1;
        ELSIF v_temp_benefit.naic_id IS NOT NULL THEN
             SELECT p.id INTO v_temp_benefit.payer_id FROM form_payer p
            WHERE p.naic_id = v_temp_benefit.naic_id AND p.deleted IS NOT TRUE AND p.archived IS NOT TRUE LIMIT 1;
        ELSE
            v_temp_benefit.payer_id := NULL;
        END IF;

        IF p_resolved_patient_id IS NOT NULL AND v_temp_benefit.payer_id IS NOT NULL THEN
            SELECT pi.id INTO v_temp_benefit.insurance_id FROM form_patient_insurance pi
            WHERE pi.patient_id = p_resolved_patient_id AND pi.payer_id = v_temp_benefit.payer_id AND pi.active = 'Yes'
            AND pi.deleted IS NOT TRUE AND pi.archived IS NOT TRUE ORDER BY pi.rank ASC, pi.id DESC LIMIT 1;
        ELSE
            v_temp_benefit.insurance_id := NULL;
        END IF;

        v_benefits_array := array_append(v_benefits_array, v_temp_benefit);
    END LOOP;
    RETURN v_benefits_array;
END;
$$;

CREATE OR REPLACE FUNCTION _parse_ss_observations(p_observations_json JSONB)
RETURNS ss_observation_type[] LANGUAGE plpgsql AS $$
DECLARE
    v_observations_array ss_observation_type[] := ARRAY[]::ss_observation_type[];
    v_observation_item_json JSONB;
    v_temp_observation ss_observation_type;
    v_parsed_observations JSONB;
BEGIN

    IF p_observations_json IS NULL OR jsonb_typeof(p_observations_json) = 'null' THEN
        RETURN v_observations_array;
    END IF;

    -- Convert object to array if necessary
    IF jsonb_typeof(p_observations_json) = 'object' THEN
        RAISE LOG '_parse_ss_observations: Input is an object, converting to array.';
        v_parsed_observations := jsonb_build_array(p_observations_json);
    ELSE
        v_parsed_observations := p_observations_json;
    END IF;

    FOR v_observation_item_json IN SELECT * FROM jsonb_array_elements(ensure_jsonb_array(v_parsed_observations))
    LOOP
        v_temp_observation.type_id           := v_observation_item_json#>>'{VitalSign}';
        v_temp_observation.loinc_version     := v_observation_item_json#>>'{LOINCVersion}';
        v_temp_observation.value             := (NULLIF(v_observation_item_json#>>'{Value}',''))::NUMERIC;
        v_temp_observation.unit_id           := v_observation_item_json#>>'{UnitOfMeasure}';
        v_temp_observation.ucum_version      := v_observation_item_json#>>'{UCUMVersion}';
        v_temp_observation.observation_date  := (NULLIF(COALESCE(v_observation_item_json#>>'{ObservationDate,Date}', v_observation_item_json#>>'{ObservationDate,DateTime}'),''))::DATE;
        v_temp_observation.notes             := p_observations_json#>>'{ObservationNotes}';
        v_observations_array := array_append(v_observations_array, v_temp_observation);
    END LOOP;
    RETURN v_observations_array;
END;
$$;

CREATE OR REPLACE FUNCTION _parse_ss_dues(p_dues_json_array JSONB)
RETURNS ss_due_type[] LANGUAGE plpgsql AS $$
DECLARE
    v_dues_array ss_due_type[] := ARRAY[]::ss_due_type[];
    v_due_item_json JSONB;
    v_temp_due ss_due_type;
    v_parsed_dues JSONB;
BEGIN
    IF p_dues_json_array IS NULL OR jsonb_typeof(p_dues_json_array) = 'null' THEN
        RETURN v_dues_array;
    END IF;

    -- Convert object to array if necessary
    IF jsonb_typeof(p_dues_json_array) = 'object' THEN
        RAISE LOG '_parse_ss_dues: Input is an object, converting to array.';
        v_parsed_dues := jsonb_build_array(p_dues_json_array);
    ELSE
        v_parsed_dues := p_dues_json_array;
    END IF;

    IF jsonb_typeof(v_parsed_dues) <> 'array' THEN
        RAISE NOTICE '_parse_ss_dues: Input is not a JSON array, attempting to process with ensure_jsonb_array. Actual type: %', jsonb_typeof(p_dues_json_array);
    END IF;

    FOR v_due_item_json IN SELECT * FROM jsonb_array_elements(ensure_jsonb_array(v_parsed_dues))
    LOOP
        v_temp_due.service_reason_id      := v_due_item_json#>>'{ServiceReasonCode}';
        v_temp_due.pservice_rsn_id        := v_due_item_json#>>'{ProfessionalServiceCode}';

        IF jsonb_typeof(v_due_item_json#>'{ServiceResultCode}') = 'array' THEN
            SELECT array_agg(elem->>'Code') INTO v_temp_due.service_res_id
            FROM jsonb_array_elements(v_due_item_json#>'{ServiceResultCode}') elem;
        ELSIF jsonb_typeof(v_due_item_json#>'{ServiceResultCode}') = 'object' AND v_due_item_json#>>'{ServiceResultCode,Code}' IS NOT NULL THEN
            v_temp_due.service_res_id := ARRAY[v_due_item_json#>>'{ServiceResultCode,Code}'];
        ELSIF jsonb_typeof(v_due_item_json#>'{ServiceResultCode}') IN ('string', 'number', 'boolean') THEN
            v_temp_due.service_res_id := ARRAY[v_due_item_json#>>'{ServiceResultCode}'];
        ELSE
             v_temp_due.service_res_id := ARRAY[]::TEXT[]; -- Default to empty array
        END IF;

        v_temp_due.coagent_code           := v_due_item_json#>>'{CoAgent,CoAgentCode,Code}';
        v_temp_due.coagent_qualifier_id   := v_due_item_json#>>'{CoAgent,CoAgentCode,Qualifier}';
        v_temp_due.coagent_description    := v_due_item_json#>>'{CoAgent,CoAgentCode,Description}';
        v_temp_due.clinical_significance  := v_due_item_json#>>'{ClinicalSignificanceCode}';
        v_temp_due.ack_reason             := v_due_item_json#>>'{AcknowledgementReason}';
        v_temp_due.compound_no            := (NULLIF(v_due_item_json#>>'{CompoundIngredientItemNumber}',''))::INTEGER;

        IF v_temp_due.coagent_qualifier_id = 'ND' AND v_temp_due.coagent_code IS NOT NULL THEN
            SELECT flfn.code::TEXT INTO v_temp_due.coagent_id FROM form_list_fdb_ndc flfn
            WHERE flfn.ndc = fn_format_ndc(v_temp_due.coagent_code) AND flfn.deleted IS NOT TRUE AND flfn.archived IS NOT TRUE LIMIT 1;
        ELSE
             v_temp_due.coagent_id := NULL;
        END IF;
        v_dues_array := array_append(v_dues_array, v_temp_due);
    END LOOP;
    RETURN v_dues_array;
END;
$$;

CREATE OR REPLACE FUNCTION _parse_ss_compounds(p_compound_ingredients_json_array JSONB)
RETURNS ss_compound_type[] LANGUAGE plpgsql AS $$
DECLARE
    v_compounds_array ss_compound_type[] := ARRAY[]::ss_compound_type[];
    v_compound_item_json JSONB;
    v_temp_compound ss_compound_type;
    v_loop_count INTEGER := 0;
    v_parsed_compound_ingredients JSONB;
BEGIN
    RAISE NOTICE '_parse_ss_compounds received input: %', p_compound_ingredients_json_array;

    IF p_compound_ingredients_json_array IS NULL OR jsonb_typeof(p_compound_ingredients_json_array) = 'null' THEN
        RAISE NOTICE '_parse_ss_compounds: Input is NULL or jsonb null, returning empty array.';
        RETURN v_compounds_array;
    END IF;

    -- Convert object to array if necessary
    IF jsonb_typeof(p_compound_ingredients_json_array) = 'object' THEN
        RAISE LOG 'p_compound_ingredients_json_array: Input is an object, converting to array.';
        v_parsed_compound_ingredients := jsonb_build_array(p_compound_ingredients_json_array);
    ELSE
        v_parsed_compound_ingredients := p_compound_ingredients_json_array;
    END IF;

    IF jsonb_typeof(v_parsed_compound_ingredients) <> 'array' THEN
        RAISE NOTICE '_parse_ss_compounds: Input is not a JSON array, attempting to process with ensure_jsonb_array. Actual type: %', jsonb_typeof(p_compound_ingredients_json_array);
    END IF;

    FOR v_compound_item_json IN SELECT * FROM jsonb_array_elements(ensure_jsonb_array(v_parsed_compound_ingredients))
    LOOP
        v_loop_count := v_loop_count + 1;
        RAISE NOTICE '_parse_ss_compounds: Loop iteration %, item: %', v_loop_count, v_compound_item_json;
        v_temp_compound.qual_id                 := v_compound_item_json#>>'{CompoundIngredient,ItemNumber,Qualifier}'; -- Path updated based on NewRx
        v_temp_compound.code                    := v_compound_item_json#>>'{CompoundIngredient,ItemNumber,Code}';
        v_temp_compound.description             := v_compound_item_json#>>'{CompoundIngredient,CompoundIngredientItemDescription}';
        v_temp_compound.strength                := v_compound_item_json#>>'{CompoundIngredient,Strength,StrengthValue}';
        v_temp_compound.strength_form_id        := v_compound_item_json#>>'{CompoundIngredient,Strength,StrengthForm,Code}';
        v_temp_compound.strength_uom_id         := v_compound_item_json#>>'{CompoundIngredient,Strength,StrengthUnitOfMeasure,Code}';
        v_temp_compound.quantity                := (NULLIF(v_compound_item_json#>>'{Quantity,Value}',''))::NUMERIC;
        v_temp_compound.quantity_qualifier_id   := v_compound_item_json#>>'{Quantity,CodeListQualifier}';
        v_temp_compound.quantity_uom_id         := v_compound_item_json#>>'{Quantity,QuantityUnitOfMeasure,Code}';
        v_temp_compound.dea_schedule_id         := v_compound_item_json#>>'{CompoundIngredient,DEASchedule,Code}';
        v_temp_compound.compound_no             := (NULLIF(v_compound_item_json#>>'{ItemNumber}',''))::INTEGER;

        IF v_temp_compound.qual_id = 'ND' AND v_temp_compound.code IS NOT NULL THEN
            SELECT flfn.id::TEXT INTO v_temp_compound.fdb_id FROM form_list_fdb_ndc flfn
            WHERE flfn.ndc = fn_format_ndc(v_temp_compound.code) AND flfn.deleted IS NOT TRUE AND flfn.archived IS NOT TRUE LIMIT 1;
        ELSE
            v_temp_compound.fdb_id := NULL;
        END IF;
        v_compounds_array := array_append(v_compounds_array, v_temp_compound);
    END LOOP;

    RAISE NOTICE '_parse_ss_compounds completed, loop iterations: %, returning array with % elements', v_loop_count, COALESCE(array_length(v_compounds_array, 1), 0);
    RETURN v_compounds_array;
END;
$$;

CREATE OR REPLACE FUNCTION _parse_ss_diagnoses(p_diagnoses_json_array JSONB)
RETURNS ss_diagnosis_type[] LANGUAGE plpgsql AS $$
DECLARE
    v_diagnoses_array ss_diagnosis_type[] := ARRAY[]::ss_diagnosis_type[];
    v_diagnosis_group_json JSONB; -- Represents one element of the input array
    v_primary_diag_json JSONB;
    v_secondary_diag_json JSONB;
    v_temp_diagnosis ss_diagnosis_type;
    v_parsed_diagnoses JSONB;
BEGIN
    RAISE LOG '_parse_ss_diagnoses received input: %', p_diagnoses_json_array;

    IF p_diagnoses_json_array IS NULL OR jsonb_typeof(p_diagnoses_json_array) = 'null' THEN
        RAISE LOG '_parse_ss_diagnoses: Input is NULL or jsonb null, returning empty array.';
        RETURN v_diagnoses_array;
    END IF;

    -- Convert object to array if necessary
    IF jsonb_typeof(p_diagnoses_json_array) = 'object' THEN
        RAISE LOG '_parse_ss_diagnoses: Input is an object, converting to array.';
        v_parsed_diagnoses := jsonb_build_array(p_diagnoses_json_array);
    ELSE
        v_parsed_diagnoses := p_diagnoses_json_array;
    END IF;

    -- The input p_diagnoses_json_array is expected to be an ARRAY of diagnosis groups.
    -- Each group can have a Primary and/or a Secondary diagnosis.
    IF jsonb_typeof(v_parsed_diagnoses) = 'array' THEN
        RAISE LOG '_parse_ss_diagnoses: Input is an array, processing elements.';

        FOR v_diagnosis_group_json IN SELECT * FROM jsonb_array_elements(v_parsed_diagnoses)
        LOOP
            RAISE LOG '_parse_ss_diagnoses: Processing diagnosis group: %', v_diagnosis_group_json;

            v_primary_diag_json := v_diagnosis_group_json->'Primary';
            v_secondary_diag_json := v_diagnosis_group_json->'Secondary';

            -- Process Primary Diagnosis if it exists
            IF v_primary_diag_json IS NOT NULL AND jsonb_typeof(v_primary_diag_json) = 'object' THEN
                v_temp_diagnosis := NULL; -- Reset for safety
                v_temp_diagnosis.type := 'Primary';
                v_temp_diagnosis.dx_code := v_primary_diag_json#>>'{Code}';
                v_temp_diagnosis.dx_code_qualifier_id := v_primary_diag_json#>>'{Qualifier}';
                v_temp_diagnosis.dx_desc := v_primary_diag_json#>>'{Description}';

                IF v_temp_diagnosis.dx_code IS NOT NULL AND v_temp_diagnosis.dx_code_qualifier_id IS NOT NULL THEN
                    v_temp_diagnosis.dx_id := NULL;
                    IF v_temp_diagnosis.dx_code_qualifier_id = 'ABF' THEN
                        SELECT fld.code INTO v_temp_diagnosis.dx_id
                        FROM form_list_diagnosis fld
                        WHERE fld.code = v_temp_diagnosis.dx_code
                        AND fld.deleted IS NOT TRUE AND fld.archived IS NOT TRUE
                        LIMIT 1;
                    END IF;
                    v_diagnoses_array := array_append(v_diagnoses_array, v_temp_diagnosis);
                ELSE
                    RAISE LOG '_parse_ss_diagnoses: Skipping invalid Primary diagnosis data - missing code or qualifier';
                END IF;
            END IF;

            -- Process Secondary Diagnosis if it exists
            IF v_secondary_diag_json IS NOT NULL AND jsonb_typeof(v_secondary_diag_json) = 'object' THEN
                v_temp_diagnosis := NULL; -- Reset for safety
                v_temp_diagnosis.type := 'Secondary';
                v_temp_diagnosis.dx_code := v_secondary_diag_json#>>'{Code}';
                v_temp_diagnosis.dx_code_qualifier_id := v_secondary_diag_json#>>'{Qualifier}';
                v_temp_diagnosis.dx_desc := v_secondary_diag_json#>>'{Description}';

                RAISE LOG '_parse_ss_diagnoses: Extracted Secondary - Type: %, Code: %, Qualifier: %, Desc: %',
                    v_temp_diagnosis.type, v_temp_diagnosis.dx_code,
                    v_temp_diagnosis.dx_code_qualifier_id, v_temp_diagnosis.dx_desc;

                IF v_temp_diagnosis.dx_code IS NOT NULL AND v_temp_diagnosis.dx_code_qualifier_id IS NOT NULL THEN
                    v_temp_diagnosis.dx_id := NULL;
                    IF v_temp_diagnosis.dx_code_qualifier_id = 'ABF' THEN -- Typically ICD-10
                        SELECT fld.code INTO v_temp_diagnosis.dx_id
                        FROM form_list_diagnosis fld
                        WHERE fld.code = v_temp_diagnosis.dx_code
                        AND fld.deleted IS NOT TRUE AND fld.archived IS NOT TRUE
                        LIMIT 1;
                    END IF;
                    v_diagnoses_array := array_append(v_diagnoses_array, v_temp_diagnosis);
                ELSE
                    RAISE LOG '_parse_ss_diagnoses: Skipping invalid Secondary diagnosis data - missing code or qualifier';
                END IF;
            END IF;

        END LOOP;
    ELSE
        RAISE LOG '_parse_ss_diagnoses: Input was not an array as expected. Type was: %', jsonb_typeof(p_diagnoses_json_array);
    END IF;

    RAISE LOG '_parse_ss_diagnoses completed, returning array with % elements',
        COALESCE(array_length(v_diagnoses_array, 1), 0);
    RETURN v_diagnoses_array;
END;
$$;

CREATE OR REPLACE FUNCTION _parse_ss_codified_notes(p_codified_notes_json_array JSONB)
RETURNS ss_codified_note_type[] LANGUAGE plpgsql AS $$
DECLARE
    v_codified_notes_array ss_codified_note_type[] := ARRAY[]::ss_codified_note_type[];
    v_codified_note_item_json JSONB;
    v_temp_codified_note ss_codified_note_type;
    v_parsed_codified_notes JSONB;
BEGIN
    IF p_codified_notes_json_array IS NULL OR jsonb_typeof(p_codified_notes_json_array) = 'null' THEN
        RETURN v_codified_notes_array;
    END IF;


    -- Convert object to array if necessary
    IF jsonb_typeof(p_codified_notes_json_array) = 'object' THEN
        RAISE LOG '_parse_ss_codified_notes: Input is an object, converting to array.';
        v_parsed_codified_notes := jsonb_build_array(p_codified_notes_json_array);
    ELSE
        v_parsed_codified_notes := p_codified_notes_json_array;
    END IF;

    FOR v_codified_note_item_json IN SELECT * FROM jsonb_array_elements(ensure_jsonb_array(v_parsed_codified_notes))
    LOOP
        v_temp_codified_note.qualifier_id       := v_codified_note_item_json#>>'{Qualifier}';
        v_temp_codified_note.value              := (NULLIF(v_codified_note_item_json#>>'{Value}',''))::INTEGER;
        v_codified_notes_array := array_append(v_codified_notes_array, v_temp_codified_note);
    END LOOP;
    RETURN v_codified_notes_array;
END;
$$;

CREATE OR REPLACE FUNCTION _parse_ss_medication_data(p_message_json JSONB, p_surescripts_message_type TEXT, p_med_path_segment TEXT)
RETURNS ss_extracted_medication_type LANGUAGE plpgsql AS $$
DECLARE
    v_medication_data ss_extracted_medication_type;
    r_subform_item JSONB;
BEGIN
    v_medication_data.description                 := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DrugDescription');
    v_medication_data.product_code_qualifier_id   := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DrugCoded','ProductCode','Qualifier');
    v_medication_data.product_code                := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DrugCoded','ProductCode','Code');
    v_medication_data.drug_db_qualifier_id        := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DrugCoded','DrugDBCode','Qualifier');
    v_medication_data.drug_db_code                := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DrugCoded','DrugDBCode','Code');
    v_medication_data.dea_schedule_id             := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DrugCoded','DEASchedule','Code');
    v_medication_data.strength                    := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DrugCoded','Strength','StrengthValue');
    v_medication_data.strength_form_id            := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DrugCoded','Strength','StrengthForm','Code');
    v_medication_data.strength_uom_id             := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DrugCoded','Strength','StrengthUnitOfMeasure','Code');
    v_medication_data.quantity                    := (jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'Quantity','Value'))::NUMERIC;
    v_medication_data.quantity_qualifier_id       := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'Quantity','CodeListQualifier');
    v_medication_data.quantity_uom_id             := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'Quantity','QuantityUnitOfMeasure','Code');
    v_medication_data.days_supply                 := (jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DaysSupply'))::INTEGER;
    v_medication_data.compound_dosage_form_id     := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'CompoundInformation','FinalCompoundPharmaceuticalDosageForm');
    v_medication_data.written_date                := (COALESCE(jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'WrittenDate','Date'),
                                               jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'WrittenDate','DateTime')))::DATE;
    v_medication_data.daw                         := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'Substitutions');
    v_medication_data.daw_code_reason             := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'ReasonForSubstitutionCodeUsed');
    v_medication_data.refills                     := (jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'NumberOfRefills'))::INTEGER;
    v_medication_data.pa_number                   := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'PriorAuthorization');
    v_medication_data.pa_status_id                := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'PriorAuthorizationStatus');
    v_medication_data.drug_cvg_status_id := CASE
        WHEN jsonb_typeof(jsonb_extract_path(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DrugCoverageStatusCode')) = 'array' THEN
            ARRAY(SELECT jsonb_array_elements_text(jsonb_extract_path(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DrugCoverageStatusCode')))
        ELSE
            ARRAY[jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DrugCoverageStatusCode')]
    END;
    v_medication_data.do_not_fill                 := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DoNotFill');
    v_medication_data.note                        := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'Note');
    v_medication_data.sig                         := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'Sig','SigText');
    v_medication_data.delivery_request            := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DeliveryRequest');
    v_medication_data.delivery_location           := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'DeliveryLocation');
    v_medication_data.flavoring_requested         := COALESCE(jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'FlavoringRequested'), 'N');
    v_medication_data.prescriber_checked_rems     := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'PrescriberCheckedREMS');
    v_medication_data.rems_risk_category          := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'REMSPatientRiskCategory');
    v_medication_data.rems_authorization_number   := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'REMSAuthorizationNumber');
    IF jsonb_typeof(jsonb_extract_path(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'Diagnosis')) = 'array' THEN
        v_medication_data.clinical_info_qualifier     := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'Diagnosis','0','ClinicalInformationQualifier');
    ELSE
        v_medication_data.clinical_info_qualifier     := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,p_med_path_segment,'Diagnosis','ClinicalInformationQualifier');
    END IF;
    v_medication_data.prohibit_renewal_request    := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,'BenefitsCoordination','ProhibitRenewalRequest');

    FOR r_subform_item IN SELECT * FROM jsonb_array_elements(ensure_jsonb_array(jsonb_extract_path(p_message_json, VARIADIC ARRAY['Message','Body',p_surescripts_message_type,p_med_path_segment,'OtherMedicationDate'])))
    LOOP
        IF r_subform_item#>>'{OtherMedicationDateQualifier}' = 'StartDate' THEN
            v_medication_data.start_date := (COALESCE(r_subform_item#>>'{OtherMedicationDate,Date}', r_subform_item#>>'{OtherMedicationDate,DateTime}'))::DATE;
        ELSIF r_subform_item#>>'{OtherMedicationDateQualifier}' = 'ExpirationDate' THEN
            v_medication_data.expiration_date := (COALESCE(r_subform_item#>>'{OtherMedicationDate,Date}', r_subform_item#>>'{OtherMedicationDate,DateTime}'))::DATE;
        ELSIF r_subform_item#>>'{OtherMedicationDateQualifier}' = 'EffectiveDate' THEN
            v_medication_data.effective_date := (COALESCE(r_subform_item#>>'{OtherMedicationDate,Date}', r_subform_item#>>'{OtherMedicationDate,DateTime}'))::DATE;
        END IF;
    END LOOP;

    IF v_medication_data.strength IS NULL THEN -- Fallback for older path if primary path is null
        v_medication_data.strength := jsonb_extract_path_text(p_message_json, 'Message','Body',p_surescripts_message_type,'MedicationPrescribed','Strength','StrengthValue');
    END IF;

    RETURN v_medication_data;
END;
$$;

CREATE OR REPLACE FUNCTION _resolve_ss_ids(
    p_ss_to TEXT,
    p_ss_from TEXT,
    p_ss_direction TEXT,
    p_parsed_patient ss_extracted_patient_type,
    p_parsed_prescriber ss_extracted_prescriber_info_type,
    p_parsed_medication ss_extracted_medication_type,
    p_prescriber_order_number TEXT
)
RETURNS ss_resolved_ids_type LANGUAGE plpgsql AS $$
DECLARE
    v_resolved_ids ss_resolved_ids_type;
BEGIN
    SELECT s.id INTO v_resolved_ids.resolved_site_id FROM form_site s
    WHERE s.ncpdp_id = (CASE WHEN p_ss_direction = 'IN' THEN p_ss_to ELSE p_ss_from END)
    AND s.deleted IS NOT TRUE AND s.archived IS NOT TRUE LIMIT 1;

    IF p_parsed_patient.first_name IS NOT NULL AND p_parsed_patient.last_name IS NOT NULL AND p_parsed_patient.dob IS NOT NULL THEN
        SELECT fp.id INTO v_resolved_ids.resolved_patient_id FROM form_patient fp
        WHERE
            UPPER(fp.firstname) = UPPER(p_parsed_patient.first_name) AND
            UPPER(fp.lastname) = UPPER(p_parsed_patient.last_name) AND
            fp.dob = p_parsed_patient.dob AND
            (fp.ssn IS NULL OR p_parsed_patient.ssn IS NULL OR fp.ssn = p_parsed_patient.ssn) AND
            fp.deleted IS NOT TRUE AND fp.archived IS NOT TRUE
        LIMIT 1;
    END IF;

    IF p_parsed_prescriber.npi IS NOT NULL THEN
        SELECT phy.id INTO v_resolved_ids.resolved_physician_id FROM form_physician phy
        WHERE phy.npi = p_parsed_prescriber.npi AND phy.deleted IS NOT TRUE AND phy.archived IS NOT TRUE
        LIMIT 1;
    END IF;

    IF p_prescriber_order_number IS NOT NULL THEN
        SELECT co.id INTO v_resolved_ids.resolved_pharmacy_order_id FROM form_careplan_order co
        INNER JOIN sf_form_careplan_order_to_careplan_orderp_item sfo ON sfo.form_careplan_order_fk = co.id
        AND sfo.delete IS NOT TRUE
        AND sfo.archive IS NOT TRUE
        INNER JOIN form_careplan_orderp_item opi ON opi.id = sfo.form_careplan_orderp_item_fk
        AND opi.deleted IS NOT TRUE
        AND opi.archived IS NOT TRUE
        WHERE opi.physician_order_id = p_prescriber_order_number
        AND co.deleted IS NOT TRUE AND co.archived IS NOT TRUE -- This was co.deleted IS NOT TRUE AND co.archived IS NOT TRUE. Corrected typo.
        LIMIT 1;
    END IF;

    IF p_parsed_medication.product_code_qualifier_id = 'ND' AND p_parsed_medication.product_code IS NOT NULL THEN
        SELECT flfn.code::TEXT INTO v_resolved_ids.fdb_id FROM form_list_fdb_ndc flfn
        WHERE flfn.ndc = fn_format_ndc(p_parsed_medication.product_code) AND flfn.deleted IS NOT TRUE AND flfn.archived IS NOT TRUE LIMIT 1;
    END IF;

    RETURN v_resolved_ids;
END;
$$;