DO $$ BEGIN
  PERFORM drop_all_function_signatures('get_fdb_ndc_data');
END $$;
CREATE OR REPLACE FUNCTION get_fdb_ndc_data(p_ndc TEXT)
RETURNS TABLE (
    external_id TEXT,
    type TEXT,
    ndc TEXT,
    fdb_id TEXT,
    formatted_ndc TEXT,
    gcn_seqno TEXT,
    medid TEXT,
    manufacturer_id TEXT,
    active TEXT,
    name TEXT,
    dea_schedule_id TEXT,
    therapy_class TEXT,
    hcpc_id TEXT,
    hcpc_code TEXT,
    brand_name_id TEXT,
    generic_name TEXT,
    obsolete_date TEXT,
    cmn_description TEXT,
    hcpc_quantity NUMERIC,
    hcpc_price NUMERIC,
    hcpc_unit TEXT,
    quantity_each NUMERIC,
    available_routes TEXT[],
    route_id TEXT,
    billing_unit_id TEXT,
    metric_unit_each NUMERIC,
    additional_descriptor TEXT,
    dosage_form TEXT,
    strength NUMERIC,
    strength_unit_id TEXT,
    volume NUMERIC,
    volume_unit_id TEXT,
    dosage_form_type TEXT,
    concentration NUMERIC,
    concentration_unit TEXT,
    wac_price NUMERIC,
    avg_acq_cost_brand NUMERIC,
    avg_acq_cost_gen NUMERIC,
    ful NUMERIC,
    awp_price NUMERIC,
    awp_basis TEXT,
    storage_id TEXT,
    quantity_per_case NUMERIC,
    dosing_unit_per_each NUMERIC,
    default_dosing_unit_id TEXT,
    quantity_per_package NUMERIC,
    awp_price_pkg NUMERIC,
    wac_price_pkg NUMERIC,
    asp_price NUMERIC,
    report_unit_id TEXT,
    requires_nursing TEXT
) AS $$
BEGIN
    RETURN QUERY
WITH base_ndc_data AS (
        -- Get primary NDC data in a single query
        SELECT 
            fndc.ndc,
            fndc.lblrid,
            fndc.gcn_seqno,
            fndc.ps,
            fndc.df,
            fndc.ad,
            fndc.ln60,
            fndc.bn,
            fndc.dea,
            fndc.csp,
            fndc.gni,
            fndc.formatted_ndc,
            lm.code as manufacturer_id,
            deasc.code as dea_schedule_id,
            CASE
                when fndc.obsdtec IS NULL THEN 'Yes'
                ELSE NULL
            END as active,
            fndc.obsdtec
        FROM form_list_fdb_ndc fndc
        LEFT JOIN form_list_manufacturer lm ON fndc.lblrid = lm.lblrid
        LEFT JOIN form_list_dea_schedule deasc ON deasc.fdb_map = fndc.dea
        WHERE fndc.ndc = p_ndc
    ),
    dosage_data AS (
        -- Get dosage form data
        SELECT 
            bnd.ndc,
            dosemstr.DOSAGE_FORM_DESC_SHORT as dosage_form,
            CASE 
                WHEN df::integer = 1 THEN 'each'
                WHEN df::integer = 2 THEN 'mL'
                WHEN df::integer = 3 THEN 'gm'
                ELSE NULL
            END as billing_unit_id
        FROM base_ndc_data bnd
        LEFT JOIN form_list_fdb_ndc_dose_form_link ndcdose ON ndcdose.ndc = bnd.ndc
            AND dosage_form_type_id = 1
        LEFT JOIN form_list_fdb_dosage_master dosemstr ON dosemstr.dosage_form_id = ndcdose.dosage_form_id
    ),
    route_data AS (
        -- Get route data
        SELECT 
            bnd.ndc,
            (SELECT 
                 gcrt2
             FROM form_list_fdb_ndc_to_route ndcrt
             JOIN form_list_fdb_route_mstr rtmstr ON ndcrt.clinical_rt_id::numeric = rtmstr.rt_id::numeric
             JOIN form_list_fdb_route_abbrev rtabbrev ON rtabbrev.gcrt = rtmstr.gcrt
             WHERE ndcrt.ndc = bnd.ndc
             LIMIT 1
            ) as route_id,
            ARRAY(
                SELECT DISTINCT gcrt2
                FROM form_list_fdb_ndc_to_route ndcrt
                JOIN form_list_fdb_route_mstr rtmstr ON ndcrt.clinical_rt_id::numeric = rtmstr.rt_id::numeric
                JOIN form_list_fdb_route_abbrev rtabbrev ON rtabbrev.gcrt = rtmstr.gcrt
                WHERE ndcrt.ndc = bnd.ndc
            ) as available_routes
        FROM base_ndc_data bnd
    ),
    generic_data AS (
        -- Get generic name info
        SELECT
            bnd.ndc,
            medid.medid,
            medtbl.med_medid_desc as generic_name
        FROM base_ndc_data bnd
        LEFT JOIN form_list_fdb_ndc_to_medid medid ON medid.ndc = bnd.ndc
        LEFT JOIN form_list_fdb_med_table medtbl ON medtbl.medid = medid.medid
    ),
    therapy_data AS (
        -- Get therapy class
        SELECT 
            bnd.ndc,
            thrpy.etc_name as therapy_class
        FROM base_ndc_data bnd
        LEFT JOIN form_list_fdb_ndc_to_therapy ndcthrpy ON ndcthrpy.ndc = bnd.ndc
        LEFT JOIN form_list_fdb_therapy thrpy ON ndcthrpy.etc_id = thrpy.etc_id
        LIMIT 1
    ),
    medicare_data AS (
        -- Get Medicare data
        SELECT 
            bnd.ndc,
            mcrdesc.mcr_bc as hcpc_id,
            mcrdesc.mcr_bcdesc as cmn_description,
            cmsndccw.bill_units as hcpc_quantity,
            cmsndccw.hcpcs_dosage as hcpc_unit
        FROM base_ndc_data bnd
        LEFT JOIN form_list_fdb_medicare_mstr mcrmstr ON mcrmstr.ndc = bnd.ndc
        LEFT JOIN form_list_fdb_medicare_desc mcrdesc ON mcrdesc.mcr_ref = mcrmstr.mcr_ref
        LEFT JOIN form_list_cms_ndc_hcpc_cw cmsndccw ON cmsndccw.ndc = bnd.ndc
        LIMIT 1
    ),
    medicare_price_data AS (
        -- Get Medicare price data
        SELECT 
            bnd.ndc,
            mcrprc.hcpc_pbp1
        FROM base_ndc_data bnd
        LEFT JOIN form_list_fdb_medicare_price mcrprc ON 
            (mcrprc.hcpc = (SELECT mcd.hcpc_id FROM medicare_data mcd WHERE mcd.ndc = bnd.ndc) OR mcrprc.ndc = bnd.ndc)
            AND mcrprc.hcpc_pbp1 > 0
        ORDER BY mcrprc.hcpc_pbc1 DESC
        LIMIT 1
    ),
    strength_data AS (
        -- Get strength data more efficiently
        SELECT 
            bnd.ndc,
            ingstr.strength,
            unitmstrstr.uom_mstr_abbr as strength_unit_id,
            ingstr.volume,
            unitmstrvol.uom_mstr_abbr as volume_unit_id,
            CASE
                WHEN bnd.df::integer = 2 THEN 
                    CASE
                        WHEN ingstr.volume > 0 THEN ROUND(CAST(ingstr.strength / ingstr.volume as NUMERIC), 4)
                        ELSE COALESCE(bnd.ps::numeric, 0)
                    END
                ELSE NULL
            END as concentration,
            CASE
                WHEN bnd.df::integer = 2 THEN 
                    CASE
                        WHEN ingstr.volume > 0 THEN 
                            CONCAT(unitmstrstr.uom_mstr_abbr, '/', unitmstrvol.uom_mstr_abbr)
                        ELSE 'each/mL'
                    END
                ELSE NULL
            END as concentration_unit
        FROM base_ndc_data bnd
        LEFT JOIN form_list_fdb_gcnseqno_mstr gcnmstr ON gcnmstr.gcn_seqno = bnd.gcn_seqno
        LEFT JOIN LATERAL (
            SELECT s.*
            FROM form_list_fdb_clinc_ingred_strength s
            JOIN form_list_fdb_hic_hiclseqno_link inglnk ON 
                inglnk.hic_seqn = s.hic_seqn AND 
                gcnmstr.hicl_seqno = inglnk.hicl_seqno AND 
                hic_rel_no = 1
            WHERE s.gcn_seqno = bnd.gcn_seqno
            AND s.strength_status_code IN (1, 2)
            AND s.strength_typ_code IN (0, 1, 2, 3)
            ORDER BY
                CASE
                    WHEN s.strength_typ_code IN (1, 3) THEN 0
                    WHEN s.strength_typ_code IN (0) THEN 1
                    ELSE 2
                END
            LIMIT 1
        ) ingstr ON true
        LEFT JOIN form_list_fdb_unit_master unitmstrstr ON unitmstrstr.uom_id = ingstr.strength_uom_id
        LEFT JOIN form_list_fdb_unit_master unitmstrvol ON unitmstrvol.uom_id = ingstr.volume_uom_id
    ),
    price_data AS (
        -- Get price data
        SELECT 
            bnd.ndc,
            (
            	SELECT
                CASE 
		        	WHEN bnd.df IN ('2', '3') THEN bnd.ps::numeric * price::numeric
		        	ELSE price::numeric 
        		END as price
                FROM form_list_fdb_ndc_price prc
                WHERE prc.ndc = bnd.ndc AND prc.price_type = '09'
                AND price > 0.0000

                ORDER BY price_effective_dt DESC
                LIMIT 1
            ) as wac_price,
            (
            	SELECT
                price::numeric as price
                FROM form_list_fdb_ndc_price prc
                WHERE prc.ndc = bnd.ndc AND prc.price_type = '10'
                AND price > 0.0000

                ORDER BY price_effective_dt DESC
                LIMIT 1
            ) as wac_price_pkg,
            (
                SELECT price::numeric
                FROM form_list_fdb_ndc_price prc
                WHERE prc.ndc = bnd.ndc AND prc.price_type = '24'
                AND price > 0.0000

                ORDER BY price_effective_dt DESC
                LIMIT 1
            ) as avg_acq_cost_brand,
            (
                SELECT price::numeric
                FROM form_list_fdb_ndc_price prc
                WHERE prc.ndc = bnd.ndc AND prc.price_type = '25'
                AND price > 0.0000

                ORDER BY price_effective_dt DESC
                LIMIT 1
            ) as avg_acq_cost_gen,
            (
                SELECT price::numeric
                FROM form_list_fdb_ndc_price prc
                WHERE prc.ndc = bnd.ndc AND prc.price_type = '23'
                AND price > 0.0000

                ORDER BY price_effective_dt DESC
                LIMIT 1
            ) as ful,
            (
                SELECT price::numeric
                FROM form_list_fdb_ndc_price prc
                WHERE prc.ndc = bnd.ndc AND prc.price_type = '07'
                AND price > 0.0000

                ORDER BY price_effective_dt DESC
                LIMIT 1
            ) as swp_price,
            (
                SELECT price::numeric
                FROM form_list_fdb_ndc_price prc
                WHERE prc.ndc = bnd.ndc AND prc.price_type = '08'
         		AND price > 0.0000
                ORDER BY price_effective_dt DESC
                LIMIT 1
            ) as swp_price_pkg
        FROM base_ndc_data bnd
    ),
    awp_price_wac AS (
        SELECT 
        CASE 
		   WHEN bnd.df IN ('2', '3') THEN bnd.ps::numeric * CASE WHEN bnd.gni = '1' THEN 1.25 ELSE 1.2 END * price::numeric
		   ELSE price::numeric * CASE WHEN bnd.gni = '1' THEN 1.25 ELSE 1.2 END
        END as price,
    	price_effective_dt,
    	'WAC * 1.2' as awp_basis,
    	bnd.ndc,
        CASE 
        WHEN EXTRACT(YEAR FROM AGE(CURRENT_TIMESTAMP, price_effective_dt))::int > 1 THEN 0 
        ELSE 2
        END as priority,
        '10' as package_code
        FROM base_ndc_data bnd
        
        INNER JOIN form_list_fdb_ndc_price prc ON prc.ndc = bnd.ndc AND prc.price_type = '09'
        WHERE price > 0.0000
        
        ORDER BY price_effective_dt DESC
        LIMIT 1
    ),
    awp_price_swp AS (
        SELECT 
        CASE 
           
		   WHEN bnd.df IN ('2', '3') THEN bnd.ps::numeric * CASE WHEN bnd.gni = '1' THEN 1.25 ELSE 1.2 END * price::numeric
		   ELSE price::numeric 
        END as price,
        price_effective_dt, 
        'SWP' as awp_basis, 
        bnd.ndc, 
        CASE 
        WHEN EXTRACT(YEAR FROM AGE(CURRENT_TIMESTAMP, price_effective_dt))::int > 1 THEN 1 
        ELSE 0 
        END as priority, -- If greater than a year old, use SWP instead of WAC * 1.2
        '08' as package_code
        FROM base_ndc_data bnd
        INNER JOIN form_list_fdb_ndc_price prc ON prc.ndc = bnd.ndc AND prc.price_type = '07'
        WHERE price > 0.0000
        ORDER BY price_effective_dt DESC
        LIMIT 1
    ),
    awp_price_fallthrough AS (
		SELECT * FROM awp_price_wac
    ),
    awp_price AS (
    	SELECT
    	pft.price,
    	pft.awp_basis,
    	pft.ndc,
    	pft.package_code
    	FROM awp_price_fallthrough pft
    	ORDER BY price_effective_dt DESC, priority DESC 
    	LIMIT 1
    ),
    awp_package_price AS (
        SELECT 
        CASE 
		   WHEN bnd.df IN ('2', '3') THEN ROUND(awp.price::numeric * COALESCE(bnd.csp, 1)::numeric,4)::numeric
		   ELSE ROUND(awp.price::numeric * COALESCE(bnd.ps, 1)::numeric,4)::numeric 
        END as price,
        awp.ndc
        FROM base_ndc_data bnd 
        INNER JOIN awp_price awp ON awp.ndc = bnd.ndc
    	LIMIT 1
    ),
    storage_data AS (
        -- Get storage data
        SELECT 
            bnd.ndc,
            (
                SELECT fdbst.storage_id
                FROM form_list_fdb_ndc_attribute ndcattr
                JOIN form_list_fdb_storage_to_clara fdbst ON ndcattr.ndc_attribute_value = fdbst.attribute_val
                WHERE ndcattr.ndc = bnd.ndc
                AND ndc_attribute_type_cd = '55'
                AND ndc_attribute_value IS NOT NULL
                LIMIT 1
            ) as storage_id
        FROM base_ndc_data bnd
    ),
    additional_data AS (
        -- Get alt name data
        SELECT
            bnd.ndc,
            CASE
                WHEN length(tmndc.tm_alt_ndc_desc) > 0 THEN CONCAT(tmndc.tm_alt_ndc_desc, ' ', bnd.formatted_ndc)
                ELSE CONCAT(bnd.ln60, ' ', bnd.formatted_ndc)
            END as name
        FROM base_ndc_data bnd
        LEFT JOIN form_list_fdb_tm_ndc tmndc ON tmndc.ndc = bnd.ndc AND tm_name_type_id = '2'
    ),
    asp_data AS (
        -- Calculate ASP price
        SELECT
            bnd.ndc,
            CASE
                WHEN mpd.hcpc_pbp1 > 0 AND md.hcpc_quantity > 0 THEN 
                    ROUND((mpd.hcpc_pbp1 - (mpd.hcpc_pbp1 * 0.06))::numeric, 2)
                ELSE NULL
            END as asp_price
        FROM base_ndc_data bnd
        JOIN medicare_data md ON md.ndc = bnd.ndc
        JOIN medicare_price_data mpd ON mpd.ndc = bnd.ndc
    ),
    metric_units AS (
    	SELECT 
        COALESCE(bnd.ps::numeric, 0) as metric_unit_each,
        bnd.ndc
	    FROM base_ndc_data bnd
	    LEFT JOIN dosage_data dd ON dd.ndc = bnd.ndc
    )
    -- Final join
    SELECT 
        bnd.ndc::text as external_id,
        'Drug'::text as type,
        bnd.ndc::text,
        bnd.ndc::text as fdb_id,
        bnd.formatted_ndc::text as formatted_ndc,
        bnd.gcn_seqno::text as gcn_seqno,
        gd.medid::text as medid,
        bnd.manufacturer_id::text as manufacturer_id,
        bnd.active::text as active,
        ad.name::text as name,
        bnd.dea_schedule_id::text as dea_schedule_id,
        td.therapy_class::text as therapy_class,
        md.hcpc_id::text as hcpc_id,
        md.hcpc_id::text as hcpc_code,
        bnd.bn::text as brand_name_id,
        gd.generic_name::text as generic_name,
        bnd.obsdtec::text as obsolete_date,
        md.cmn_description::text as cmn_description,
        md.hcpc_quantity::numeric as hcpc_quantity,
        asp.asp_price::numeric as hcpc_price,
        md.hcpc_unit::text as hcpc_unit,
        bnd.ps::numeric as quantity_each,
        rd.available_routes::text[] as available_routes,
        rd.route_id::text as route_id,
        dd.billing_unit_id::text as billing_unit_id,
        mu.metric_unit_each::numeric as metric_unit_each,
        bnd.ad::text as additional_descriptor,
        dd.dosage_form::text as dosage_form,
        sd.strength::numeric as strength,
        sd.strength_unit_id::text as strength_unit_id,
        sd.volume::numeric as volume,
        sd.volume_unit_id::text as volume_unit_id,
        CASE
            WHEN bnd.gni = '0' THEN 'Device'::text
            WHEN dd.billing_unit_id = 'mL' THEN 'Solution'::text
            ELSE 'Tablet/Capsule'::text
        END as dosage_form_type,
        sd.concentration::numeric as concentration,
        sd.concentration_unit::text as concentration_unit,
        pd.wac_price::numeric as wac_price,
        pd.avg_acq_cost_brand::numeric as avg_acq_cost_brand,
        pd.avg_acq_cost_gen::numeric as avg_acq_cost_gen,
        pd.ful::numeric as ful,
        awp.price::numeric as awp_price,
        awp.awp_basis::text as awp_basis,
        st.storage_id::text as storage_id,
        bnd.csp::numeric as quantity_per_case,
        CASE
            WHEN sd.strength_unit_id = '%' THEN bnd.ps::numeric
            ELSE COALESCE(sd.strength::numeric, bnd.ps::numeric)::numeric
        END as dosing_unit_per_each,
        CASE
            WHEN sd.strength_unit_id = '%' THEN dd.billing_unit_id::text
            ELSE COALESCE(sd.strength_unit_id::text, dd.billing_unit_id::text)::text
        END as default_dosing_unit_id,
        CASE 
        	WHEN bnd.df IN ('2', '3') THEN bnd.csp::numeric
        	ELSE bnd.ps::numeric 
        END as quantity_per_package,
        awppkg.price::numeric as awp_price_pkg,
        pd.wac_price_pkg::numeric as wac_price_pkg,
        asp.asp_price::numeric as asp_price,
        CASE
            WHEN sd.strength_unit_id = '%' THEN dd.billing_unit_id::text
            ELSE COALESCE(sd.strength_unit_id::text, dd.billing_unit_id::text)::text
        END as report_unit_id,
        CASE
            WHEN rd.route_id IN ('IV', 'IVP') THEN 'Yes'::text
            ELSE NULL::text
        END as requires_nursing
    FROM base_ndc_data bnd
    LEFT JOIN metric_units mu ON mu.ndc = bnd.ndc
    LEFT JOIN dosage_data dd ON dd.ndc = bnd.ndc
    LEFT JOIN route_data rd ON rd.ndc = bnd.ndc
    LEFT JOIN generic_data gd ON gd.ndc = bnd.ndc
    LEFT JOIN therapy_data td ON td.ndc = bnd.ndc
    LEFT JOIN medicare_data md ON md.ndc = bnd.ndc
    LEFT JOIN strength_data sd ON sd.ndc = bnd.ndc
    LEFT JOIN price_data pd ON pd.ndc = bnd.ndc
    LEFT JOIN storage_data st ON st.ndc = bnd.ndc
    LEFT JOIN additional_data ad ON ad.ndc = bnd.ndc
    LEFT JOIN asp_data asp ON asp.ndc = bnd.ndc
    LEFT JOIN awp_package_price awppkg ON awppkg.ndc = bnd.ndc
    LEFT JOIN awp_price awp ON awp.ndc = bnd.ndc
    WHERE bnd.ndc = p_ndc
    LIMIT 1;
END;
$$ LANGUAGE plpgsql STABLE;

DROP VIEW IF EXISTS inventory_pricing_matrix_view;
CREATE OR REPLACE VIEW inventory_pricing_matrix_view AS
WITH inventory_base AS (
    -- Get base inventory information
    SELECT
        inv.id AS inventory_id,
        inv.hcpc_code,
        inv.type,
        inv.price_code_id,
        inv.awp_price_pkg,
        inv.wac_price_pkg,
        inv.list_price,
        inv.asp_price,
        inv.last_cost_ea,
        inv.add_price1,
        inv.add_price2
    FROM
        form_inventory inv
    WHERE
        inv.archived IS NOT TRUE
        AND inv.deleted IS NOT TRUE
),
price_matrices AS (
    -- Get all price matrices
    SELECT
        ppm.id AS payer_price_matrix_id,
        ppm.name AS matrix_name
    FROM
        form_payer_price_matrix ppm
    WHERE
        ppm.archived IS NOT TRUE
        AND ppm.deleted IS NOT TRUE
),
contracts_with_formulas AS (
    -- Get contracts and their formulas
    SELECT
        pc.id AS contract_id,
        pc.assigned_matrix_id AS payer_price_matrix_id,
        pcf.code_category AS price_code_id,
        pcf.special_price_basis AS special_price_formula_id,
        pcf.expected_price_basis AS expected_price_formula_id,
        pcf.special_price_multiplier::numeric as special_price_multiplier,
        pcf.expected_price_multiplier::numeric as expected_price_multiplier
    FROM
        form_payer_contract pc
    JOIN
        sf_form_payer_contract_to_price_code_formulas spcf
        ON spcf.form_payer_contract_fk = pc.id
    JOIN
        form_price_code_formulas pcf
        ON pcf.id = spcf.form_price_code_formulas_fk
    WHERE
        pc.archived IS NOT TRUE
        AND pc.deleted IS NOT TRUE
),
site_price_codes AS (
    -- Get site price code formulas as fallback
    SELECT
        spc.price_code_id,
        spc.price_formula_id,
        spc.multiplier
    FROM
        form_site_price_code_item spc
    WHERE
        spc.archived IS NOT TRUE
        AND spc.deleted IS NOT TRUE
)
-- Combine all the data
SELECT
    ib.inventory_id,
    ib.hcpc_code,
    ib.type,
    ib.price_code_id,
    ib.awp_price_pkg,
    ib.wac_price_pkg,
    ib.list_price,
    ib.last_cost_ea,
    ib.add_price1,
    ib.add_price2,
    pm.payer_price_matrix_id,
    pm.matrix_name,
    -- Formula information with fallback logic
    COALESCE(
        -- Check if there's a contract formula for this price code
        (SELECT cwf.special_price_formula_id
         FROM contracts_with_formulas cwf
         WHERE cwf.payer_price_matrix_id = pm.payer_price_matrix_id
           AND cwf.price_code_id = ib.price_code_id
         LIMIT 1),
        -- Fall back to site price code formula
        (SELECT spc.price_formula_id
         FROM site_price_codes spc
         WHERE spc.price_code_id = ib.price_code_id
         LIMIT 1)
    ) AS special_price_formula_id,
    
    COALESCE(
        -- Check if there's a contract formula for this price code
        (SELECT cwf.expected_price_formula_id
         FROM contracts_with_formulas cwf
         WHERE cwf.payer_price_matrix_id = pm.payer_price_matrix_id
           AND cwf.price_code_id = ib.price_code_id
         LIMIT 1),
        -- Fall back to site price code formula
        (SELECT spc.price_formula_id
         FROM site_price_codes spc
         WHERE spc.price_code_id = ib.price_code_id
         LIMIT 1)
    ) AS expected_price_formula_id,
    
    COALESCE(
        -- Check if there's a contract formula for this price code
        (SELECT cwf.special_price_multiplier::numeric as special_price_multiplier
         FROM contracts_with_formulas cwf
         WHERE cwf.payer_price_matrix_id = pm.payer_price_matrix_id
           AND cwf.price_code_id = ib.price_code_id
         LIMIT 1),
        -- Fall back to site price code multiplier
        (SELECT spc.multiplier::numeric as multiplier
         FROM site_price_codes spc
         WHERE spc.price_code_id = ib.price_code_id
         LIMIT 1)
    ) AS special_price_multiplier,
    
    COALESCE(
        -- Check if there's a contract formula for this price code
        (SELECT cwf.expected_price_multiplier::numeric as expected_price_multiplier
         FROM contracts_with_formulas cwf
         WHERE cwf.payer_price_matrix_id = pm.payer_price_matrix_id
           AND cwf.price_code_id = ib.price_code_id
         LIMIT 1),
        -- Fall back to site price code multiplier
        (SELECT spc.multiplier::numeric as multiplier
         FROM site_price_codes spc
         WHERE spc.price_code_id = ib.price_code_id
         LIMIT 1)
    ) AS expected_price_multiplier
    
FROM
    inventory_base ib
CROSS JOIN
    price_matrices pm;