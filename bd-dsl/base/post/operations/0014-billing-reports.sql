CREATE OR REPLACE VIEW vw_ticket_item_cogs AS
    SELECT 
        pld.ticket_no,
        pld.ticket_item_no,
        ROUND(SUM(COALESCE(lf.debit, 0) - COALESCE(lf.credit, 0))::numeric, 2) AS amount_raw
    FROM
        form_ledger_finance lf
        INNER JOIN form_ledger_serial ls ON ls.id = lf.ledger_serial_id AND lf.ledger_serial_id IS NOT NULL
        INNER JOIN form_inventory_serial invs ON invs.inventory_id = ls.inventory_id 
        AND invs.serial_no = ls.serial_no 
        AND invs.archived IS NOT TRUE 
        AND invs.deleted IS NOT TRUE
        INNER JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = ls.ticket_item_no 
        AND pld.serial_id = invs.id
        AND COALESCE(pld.void, 'No') <> 'Yes' 
        AND pld.archived IS NOT TRUE
        AND pld.deleted IS NOT TRUE
    WHERE
        lf.account_type = 'COGS'
        AND lf.transaction_type = 'Dispense'
    GROUP BY pld.ticket_no, pld.ticket_item_no

    UNION

    SELECT 
        pld.ticket_no,
        pld.ticket_item_no,
        ROUND(SUM(COALESCE(lf.debit, 0) - COALESCE(lf.credit, 0))::numeric, 2) AS amount_raw
    FROM
        form_ledger_finance lf
        INNER JOIN form_ledger_lot ll ON ll.id = lf.ledger_lot_id AND lf.ledger_lot_id IS NOT NULL
        INNER JOIN form_inventory_lot invl ON invl.id = ll.inventory_id AND invl.lot_no = ll.lot_no
        AND invl.archived IS NOT TRUE
        AND invl.deleted IS NOT TRUE
        INNER JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = ll.ticket_item_no 
        AND pld.lot_id = invl.id
        AND pld.serial_id IS NULL
        AND COALESCE(pld.void, 'No') <> 'Yes' 
        AND pld.archived IS NOT TRUE
        AND pld.deleted IS NOT TRUE
    WHERE
        lf.account_type = 'COGS'
        AND lf.transaction_type = 'Dispense'
    GROUP BY pld.ticket_no, pld.ticket_item_no

    UNION

    SELECT 
        pld.ticket_no,
        pld.ticket_item_no,
        ROUND(SUM(COALESCE(lf.debit, 0) - COALESCE(lf.credit, 0))::numeric, 2) AS amount_raw
    FROM
        form_ledger_finance lf
        INNER JOIN form_ledger_inventory li ON li.id = lf.ledger_inventory_id AND lf.ledger_inventory_id IS NOT NULL 
        INNER JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = li.ticket_item_no 
        AND pld.inventory_id = li.inventory_id 
        AND pld.lot_id IS NULL 
        AND pld.serial_id IS NULL
        AND COALESCE(pld.void, 'No') <> 'Yes' 
        AND pld.archived IS NOT TRUE
        AND pld.deleted IS NOT TRUE
    WHERE
        lf.account_type = 'COGS'
        AND lf.transaction_type = 'Dispense'
    GROUP BY pld.ticket_no, pld.ticket_item_no;

CREATE OR REPLACE VIEW vw_charge_line_cogs AS
    SELECT DISTINCT ON (cl.master_charge_no)
        cl.master_charge_no,
        ROUND(SUM(COALESCE(tic.amount_raw, 0))::numeric, 2) AS amount_raw
    FROM
        form_ledger_charge_line cl
        INNER JOIN vw_ticket_item_cogs tic ON tic.ticket_item_no = cl.ticket_item_no
    WHERE
        cl.master_charge_no = cl.charge_no
    GROUP BY cl.master_charge_no;

CREATE OR REPLACE VIEW vw_invoice_cogs AS
    SELECT DISTINCT ON (bi.master_invoice_no)
        bi.master_invoice_no,
        ROUND(SUM(COALESCE(lf.debit, 0) - COALESCE(lf.credit, 0))::numeric,2) AS amount_raw
    FROM
        form_ledger_finance lf
        INNER JOIN form_ledger_serial ls ON ls.id = lf.ledger_serial_id AND lf.ledger_serial_id IS NOT NULL
        INNER JOIN form_inventory_serial invs ON invs.inventory_id = ls.inventory_id 
        AND invs.serial_no = ls.serial_no 
        AND invs.archived IS NOT TRUE 
        AND invs.deleted IS NOT TRUE
        INNER JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = ls.ticket_item_no 
        AND pld.serial_id = invs.id
        AND COALESCE(pld.void, 'No') <> 'Yes' 
        AND pld.archived IS NOT TRUE
        AND pld.deleted IS NOT TRUE
        INNER JOIN form_ledger_charge_line cl ON cl.ticket_item_no = pld.ticket_item_no 
        AND cl.master_charge_no = cl.charge_no
        INNER JOIN form_billing_invoice bi ON bi.invoice_no = cl.invoice_no 
        AND bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
        AND COALESCE(bi.void, 'No') <> 'Yes'
    WHERE
        lf.account_type = 'COGS'
        AND lf.transaction_type = 'Dispense'
		GROUP BY bi.master_invoice_no
    UNION

    SELECT DISTINCT ON (cl.master_charge_no)
        cl.master_charge_no,
        ROUND(SUM(COALESCE(lf.debit, 0) - COALESCE(lf.credit, 0))::numeric,2) AS amount_raw
    FROM
        form_ledger_finance lf
        INNER JOIN form_ledger_lot ll ON ll.id = lf.ledger_lot_id AND lf.ledger_lot_id IS NOT NULL 
        INNER JOIN form_inventory_lot invl ON invl.id = ll.inventory_id AND invl.lot_no = ll.lot_no
        AND invl.archived IS NOT TRUE
        AND invl.deleted IS NOT TRUE
        INNER JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = ll.ticket_item_no 
        AND pld.lot_id = invl.id
        AND pld.serial_id IS NULL
        AND COALESCE(pld.void, 'No') <> 'Yes' 
        AND pld.archived IS NOT TRUE
        AND pld.deleted IS NOT TRUE
        INNER JOIN form_ledger_charge_line cl ON cl.ticket_item_no = pld.ticket_item_no 
        AND cl.master_charge_no = cl.charge_no
        INNER JOIN form_billing_invoice bi ON bi.invoice_no = cl.invoice_no 
        AND bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
    WHERE
        lf.account_type = 'COGS'
        AND lf.transaction_type = 'Dispense'
		GROUP BY cl.master_charge_no

    UNION

    SELECT DISTINCT ON (cl.master_charge_no)
        cl.master_charge_no,
        ROUND(SUM(COALESCE(lf.debit, 0) - COALESCE(lf.credit, 0))::numeric,2) AS amount_raw
    FROM
        form_ledger_finance lf
        INNER JOIN form_ledger_inventory li ON li.id = lf.ledger_inventory_id AND lf.ledger_inventory_id IS NOT NULL 
        INNER JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = li.ticket_item_no 
        AND pld.inventory_id = li.inventory_id 
        AND pld.lot_id IS NULL 
        AND pld.serial_id IS NULL
        AND COALESCE(pld.void, 'No') <> 'Yes' 
        AND pld.archived IS NOT TRUE
        AND pld.deleted IS NOT TRUE
        INNER JOIN form_ledger_charge_line cl ON cl.ticket_item_no = pld.ticket_item_no 
        AND cl.master_charge_no = cl.charge_no
        INNER JOIN form_billing_invoice bi ON bi.invoice_no = cl.invoice_no 
        AND bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
    WHERE
        lf.account_type = 'COGS'
        AND lf.transaction_type = 'Dispense'
        GROUP BY cl.master_charge_no;

CREATE OR REPLACE VIEW vw_adjustment_report AS
WITH adjustment_transactions AS (
    -- Get all adjustment transactions from ledger_finance
    SELECT 
        lf.id AS ledger_finance_id,
        lf.charge_line_id,
        lf.charge_no,
        lf.account_id,
        lf.inventory_id,
        lf.site_id,
        lf.transaction_type,
        lf.account_type,
        lf.source_id,
        lf.source_form,
        lf.transaction_datetime,
        lf.post_datetime,
        lf.adjustment_reason_id,
        lf.created_by,
        (CASE 
            WHEN lf.account_type IN ('AR', 'Revenue') THEN COALESCE(lf.credit, 0) - COALESCE(lf.debit, 0)
            ELSE COALESCE(lf.debit, 0) - COALESCE(lf.credit, 0)
         END) as amount,
        CASE 
            WHEN lf.account_type = 'AR' THEN 'AR'
            WHEN lf.account_type = 'COGS' THEN 'COGS'
            WHEN lf.account_type = 'Revenue' THEN 'Revenue'
            WHEN lf.account_type = 'Contra-Revenue' THEN 'Contra-Revenue'
            ELSE lf.account_type
        END as adjustment_for
    FROM 
        form_ledger_finance lf
    WHERE 
        lf.transaction_type = 'Adjustment'
        -- Exclude reversals to avoid double-counting
        AND NOT EXISTS (SELECT 1 FROM form_ledger_finance lf2 WHERE lf2.reversal_for_id = lf.id)
        -- Include only financial accounts relevant to adjustments
        AND lf.account_type IN ('AR', 'COGS', 'Revenue', 'Contra-Revenue', 'Inventory')
)
SELECT
    adj.source_form as query_form,
    adj.source_id as query_id,
    lcl.invoice_no,
    lcl.charge_no,
    lcl.patient_id,
    pt.auto_name as patient_id_auto_name,
    lcl.payer_id,
    py.auto_name as payer_id_auto_name,
    lcl.site_id,
    st.auto_name as site_id_auto_name,
    bi.date_of_service,
    TO_CHAR(bi.date_of_service, 'MM/DD/YYYY') as "DOS",
    lcl.inventory_type,
    lcl.charge_quantity,
    lcl.charge_unit,
    bi.billed_datetime,
    TO_CHAR(bi.billed_datetime, 'MM/DD/YYYY') as billed_date,
    COALESCE(adj.amount, 0) as adjusted_amount,
    lcl.expected as expected,
    
    -- Get cost from COGS entries in ledger_finance
    (SELECT COALESCE(SUM(lf2.debit) - SUM(lf2.credit), 0)
        FROM form_ledger_finance lf2
        WHERE lf2.ticket_item_no = lcl.ticket_item_no 
        AND lf2.account_type = 'COGS') as cost,
    
    -- Using ledger finance ID as adjustment_no
    adj.ledger_finance_id as adjustment_no,
    adj.created_by as adjustment_by_id,
     
    COALESCE(cu.displayname,TRIM(CONCAT(cu.firstname, ' ',cu.lastname)))::text as adjustment_by,
    
    -- Get the adjustment reason name
    ar.name as adjustment_reason,
    
    adj.transaction_type as type,
    adj.post_datetime as adjustment_datetime,
    TO_CHAR(adj.post_datetime, 'MM/DD/YYYY') as post_date,
    format_currency(lcl.expected::numeric) as tot_expected,
    format_currency(COALESCE(adj.amount, 0)::numeric) as total_adjusted,
    lcl.ticket_no,
    lcl.rx_no,
    lcl.inventory_id,
    inv.auto_name as inventory_auto_name,
    fdb.id as drug_brand_id,
    fdb.auto_name as drug_brand_auto_name,
    inv.name as inventory_name,
    py.organization as payer_name,
    pt.mrn as "MRN",
    pt.lastname as patient_last_name,
    pt.firstname as patient_first_name,
    pt.lastname || ', ' || pt.firstname as patient_name,
    st."name" as site_name,
    fdb.name as drug_brand,
    CASE 
        WHEN lcl.billing_method_id = 'ncpdp' THEN 'Pharmacy'
        WHEN lcl.billing_method_id = 'mm' THEN 'Major Medical'
        WHEN lcl.billing_method_id = 'cms1500' THEN 'Paper Medical'
        WHEN lcl.billing_method_id = 'generic' THEN 'Generic'
    END as billing_method
FROM
    adjustment_transactions adj
    JOIN form_ledger_charge_line lcl ON adj.charge_line_id = lcl.id
    LEFT JOIN form_billing_invoice bi ON lcl.invoice_no = bi.invoice_no
    LEFT JOIN form_inventory inv ON inv.id = COALESCE(adj.inventory_id, lcl.inventory_id)
    LEFT JOIN form_list_fdb_drug_brand fdb ON fdb.code = inv.brand_name_id
    LEFT JOIN form_payer py ON py.id = lcl.payer_id
    LEFT JOIN form_patient pt ON pt.id = lcl.patient_id
    LEFT JOIN form_site st ON st.id = COALESCE(adj.site_id, lcl.site_id)
    LEFT JOIN form_list_adjustment_reason ar ON ar.code = adj.adjustment_reason_id
    LEFT JOIN form_user cu ON cu.id = adj.created_by
WHERE lcl.archived IS NOT TRUE
    AND lcl.deleted IS NOT TRUE
ORDER BY
    bi.date_of_service DESC, 
    lcl.charge_no;

CREATE OR REPLACE VIEW vw_unapplied_cash_report AS
SELECT
    ucb.ledger_finance_id,
    ucb.account_id,
    ucb.account_type,
    ucb.patient_id as patient,
    pt.auto_name as patient_auto_name,
    ucb.payer_id as payer,
    py.auto_name as payer_auto_name,
    ucb.site_id as site,
    st.auto_name as site_auto_name,
    TO_CHAR(ucb.post_datetime, 'MM/DD/YYYY') as "Post Date",
    ucb.amount_raw,
    format_currency(ucb.amount_raw::numeric) as "Amount",
    ucb.check_no as "Check No",
    ucb.payment_method as "Payment Method",
    ucb.other_payment_method as "Other Payment Method",
    u.displayname as "Posted By",
    CASE
        WHEN ucb.account_type = 'Patient' THEN pt.lastname
        ELSE NULL
    END as "Last Name",
    CASE
        WHEN ucb.account_type = 'Patient' THEN pt.firstname
        ELSE NULL
    END as "First Name",
    CASE
        WHEN ucb.account_type = 'Patient' THEN TRIM(CONCAT(pt.lastname, ', ', pt.firstname))
        ELSE NULL
    END as patient_name,
    CASE
        WHEN ucb.account_type = 'Patient' THEN pt.mrn
        ELSE NULL
    END as "MRN"
FROM
    vw_unapplied_cash_base ucb
LEFT JOIN 
    form_patient pt ON pt.id = ucb.patient_id
LEFT JOIN 
    form_payer py ON py.id = ucb.payer_id
LEFT JOIN 
    form_site st ON st.id = ucb.site_id
LEFT JOIN 
    form_user u ON u.id = ucb.created_by
ORDER BY
    ucb.post_datetime DESC;
    
CREATE OR REPLACE VIEW vw_ar_aging_report AS
WITH aging_buckets AS (
    SELECT
        lf.invoice_id,
        lf.charge_line_id,
        bi.patient_id,
        bi.payer_id as payer,
        bi.site_id as site,
        -- Calculate AR balance for each charge line
        ROUND(SUM(CASE
            WHEN lf.account_type = 'AR'
            THEN (COALESCE(lf.debit, 0)::numeric - COALESCE(lf.credit, 0)::numeric)
            ELSE 0::numeric
        END), 2) AS ar_balance,
        
        -- Calculate aging days based on post date
        CASE
            WHEN bi.billed_datetime IS NOT NULL THEN 
                (CURRENT_DATE - bi.billed_datetime::date)::integer
            ELSE 0
        END AS aging_days,
        
        -- Determine aging bucket
        CASE
            WHEN bi.billed_datetime IS NULL THEN 'Unknown'
            WHEN (CURRENT_DATE - bi.billed_datetime::date) <= 30 THEN '0-30 Days'
            WHEN (CURRENT_DATE - bi.billed_datetime::date) <= 60 THEN '31-60 Days'
            WHEN (CURRENT_DATE - bi.billed_datetime::date) <= 90 THEN '61-90 Days'
            WHEN (CURRENT_DATE - bi.billed_datetime::date) <= 120 THEN '91-120 Days'
            ELSE '120+ Days'
        END AS aging_bucket
    FROM
        form_ledger_finance lf
    JOIN form_billing_invoice bi ON bi.id = lf.invoice_id
    WHERE
        lf.account_type = 'AR'
        AND bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
        AND COALESCE(bi.void, 'No') <> 'Yes'
        AND COALESCE(bi.zeroed, 'No') <> 'Yes'
        AND COALESCE(bi.on_hold, 'No') <> 'Yes'
    GROUP BY
        lf.invoice_id,
        lf.charge_line_id,
        bi.patient_id,
        bi.payer_id,
        bi.site_id,
        bi.billed_datetime
    HAVING
        ROUND(SUM(CASE
            WHEN lf.account_type = 'AR'
            THEN (COALESCE(lf.debit, 0)::numeric - COALESCE(lf.credit, 0)::numeric)
            ELSE 0::numeric
        END), 2) > 0
)
SELECT
    'billing_invoice' as query_form,
    ab.invoice_id as query_id,
    bi.invoice_no,
    pt.id as patient,
    pt.auto_name as patient_auto_name,
    py.id as payer,
    py.auto_name as payer_auto_name,
    st.id as site,
    st.auto_name as site_auto_name,
    TO_CHAR(bi.billed_datetime, 'MM/DD/YYYY') as "Bill Date",
    TO_CHAR(bi.date_of_service, 'MM/DD/YYYY') as "DOS",
    TO_CHAR(bi.post_datetime, 'MM/DD/YYYY') as "Post Date",
    ab.aging_days,
    ab.aging_bucket as "Aging Bucket",
    ab.ar_balance as balance_raw,
    format_currency(ab.ar_balance::numeric) as "Balance",
    pt.lastname as "Last Name",
    pt.firstname as "First Name",
    TRIM(CONCAT(pt.lastname, ', ', pt.firstname)) as patient_name,
    pt.mrn as "MRN",
    CASE
        WHEN iva.status IN ('Rejected', 'Denied', 'Error', 'Rebill Rejected', 'Reversal Rejected') THEN 'Rejected'
        WHEN iva.status IN ('On-hold','PA Deferred', 'Margin', 'Warning', 'Request for Additional Information', 'Awaiting Requested Information') THEN 'On Hold'
        WHEN iva.status IN ('Payable', 'Partially Paid') THEN 'Payable'
        ELSE 'Pending'
    END as claim_status,
    CASE
        WHEN iva.status IN ('Rejected', 'Denied', 'Error', 'Rebill Rejected', 'Reversal Rejected') THEN '#FFD9D9'
        WHEN iva.status IN ('On-hold','PA Deferred', 'Margin', 'Warning', 'Request for Additional Information', 'Awaiting Requested Information') THEN '#F5C198'
        WHEN iva.status IN ('Payable', 'Partially Paid') THEN '#9EDBC7'
        WHEN ab.aging_bucket = '91-120 Days' THEN '#FAE3AF'
        WHEN ab.aging_bucket = '120+ Days' THEN '#FFD9D9'
        ELSE NULL
    END AS __row_color
FROM
    aging_buckets ab
JOIN form_billing_invoice bi ON bi.id = ab.invoice_id
JOIN form_patient pt ON pt.id = ab.patient_id
JOIN form_payer py ON py.id = ab.payer
JOIN form_site st ON st.id = ab.site
LEFT JOIN vw_invoice_claim_response_details iva ON iva.invoice_id = ab.invoice_id
ORDER BY
    ab.aging_days DESC,
    ab.ar_balance DESC;

CREATE OR REPLACE VIEW vw_charge_line_report AS
WITH wt_pulled as (
    SELECT DISTINCT ON (ticket_item_no)
        ticket_item_no,
        array_agg(pld.id) as pulled_ids
    FROM form_careplan_dt_wt_pulled pld
    WHERE pld.archived IS NOT TRUE 
    AND pld.deleted IS NOT TRUE 
    AND COALESCE(pld.void, 'No') <> 'Yes'
    GROUP BY ticket_item_no
),
physician_location as (
    SELECT DISTINCT ON (spl.form_physician_fk)
        spl.form_physician_fk as physician_id,
        pl.city,
        pl.state_id as state,
        stat.auto_name as state_auto_name,
        pl.zip
    FROM sf_form_physician_to_physician_location spl
    INNER JOIN form_physician_location pl
        ON pl.id = spl.form_physician_location_fk
    LEFT JOIN form_list_us_state stat ON stat.code = pl.state_id
    WHERE spl.archive IS NOT TRUE
        AND spl."delete" IS NOT TRUE
        AND pl.archived IS NOT TRUE
        AND pl.deleted IS NOT TRUE
    ORDER BY 
    	spl.form_physician_fk,
        CASE WHEN pl.is_primary = 'Yes' THEN 1 ELSE 0 END,
        pl.created_on DESC
)
SELECT
    lcl.id as charge_line_id,
    bi.id as query_id,
    'billing_invoice' as query_form,
    lcl.invoice_no,
    lcl.claim_no,
    lcl.charge_no,
    lcl.close_no,
    lcl.ticket_no,
    lcl.compound_no,
    lcl.ticket_item_no,
    lcl.parent_charge_no,
    lcl.master_charge_no,
    rx.order_no,
    lcl.encounter_id,

    lcl.inventory_id,
    inv.is_specialty,
    inv.auto_name as inventory_id_auto_name,
    db.code as brand_name,
    db.auto_name as brand_name_auto_name,
    man.code as manufacturer,
    man.auto_name as manufacturer_auto_name,
    lcl.hcpc_code,
    lcl.formatted_ndc as ndc,
    lcl.upc,
    lcl.upin,
    lcl.is_primary_drug,
    lcl.bill_quantity as quantity_raw,
    format_numeric(lcl.bill_quantity::numeric) AS quantity,
    lcl.metric_quantity as metric_quantity_raw,
    CONCAT(format_numeric(lcl.metric_quantity::numeric), ' ', lcl.billing_unit_id) AS metric_quantity,
    lcl.billing_unit_id,
    lcl.fill_number,
    lcl.rx_no,

    lcl.pricing_source,
    lcl.shared_contract_id as shared_contract,
    pc.auto_name as shared_contract_auto_name,
    lcl.patient_id,
    CONCAT(pt.lastname, ', ', pt.firstname) as patient_id_auto_name,
    pt.mrn as "MRN",
    lcl.payer_id,
    py.id as payer,
    py.auto_name as payer_auto_name,
    bi.site_id as site,
    st.auto_name as site_auto_name,
    lcl.revenue_code_id as revenue_code,
    rc.auto_name as revenue_code_auto_name,
    lcl.applied_datetime,
    TO_CHAR(lcl.applied_datetime, 'MM/DD/YYYY') as "Applied Date",
    lcl.post_datetime,
    TO_CHAR(lcl.post_datetime, 'MM/DD/YYYY') as "Post Date",
    bi.date_of_service,
    TO_CHAR(bi.date_of_service, 'MM/DD/YYYY') as "DOS",
    lcl.created_by,
    COALESCE(usr.displayname,TRIM(CONCAT(usr.firstname, ' ',usr.lastname)))::text as created_by_auto_name,
    lcl.created_on,
    lcl.zeroed,

    rx.therapy_id as therapy,
    lt.auto_name as therapy_auto_name,
    co.physician_id as physician,
    TRIM(CONCAT(ph.first, ' ', ph.last, ' ', ph.title)) as physician_auto_name,
    str.id as sales_territory,
    COALESCE(str.territory_name, str.territory_code) as sales_territory_auto_name,
    rep.id as sales_rep,
    COALESCE(rep.displayname,TRIM(CONCAT(rep.firstname, ' ',rep.lastname)))::text as sales_rep_auto_name,
    sa.id as sales_account,
    sa.auto_name as sales_account_auto_name,
    na.id as nursing_agency,
    na.auto_name as nursing_agency_auto_name,
    infus.id as infusion_suite,
    infus.auto_name as infusion_suite_auto_name,
    pt.status_id as patient_status,
    ps.auto_name as patient_status_auto_name,
    dti.report_quantity as report_quantity_raw,
    dti.report_unit_id as report_unit_id_raw,
    CONCAT(format_numeric(COALESCE(dti.report_quantity, 0.0)::numeric), ' ', dti.report_unit_id) AS report_quantity,
    bm.auto_name as billing_method_auto_name,
    bm.code as billing_method,
    lcl.revenue_accepted_posted,
    wt.pulled_ids,
    pl.city as physician_city,
    pl.state as physician_state,
    pl.state_auto_name as physician_state_auto_name,
    pl.zip as physician_zip,
    pyt.code as payer_type,
    pyt.auto_name as payer_type_auto_name,
    lcl.awp_price,
    pt.lastname as "Last Name",
    pt.firstname as "First Name",
    TRIM(CONCAT(pt.lastname, ', ', pt.firstname)) as patient_name,
    inv."name" as item
    FROM form_ledger_charge_line lcl
    INNER JOIN form_inventory inv ON inv.id = lcl.inventory_id
    INNER JOIN form_patient pt ON pt.id = lcl.patient_id
    INNER JOIN form_user usr ON usr.id = lcl.created_by
    INNER JOIN form_billing_invoice bi ON bi.invoice_no = lcl.invoice_no
    LEFT JOIN form_list_fdb_drug_brand db ON db.code = inv.brand_name_id
    LEFT JOIN form_list_manufacturer man ON man.code = inv.manufacturer_id
    LEFT JOIN form_careplan_dt_item dti ON dti.ticket_item_no = lcl.ticket_item_no
    LEFT JOIN form_list_patient_status ps ON ps.code = pt.status_id
    LEFT JOIN form_list_revenue_code rc ON rc.code = lcl.revenue_code_id
    LEFT JOIN form_payer py ON py.id = lcl.payer_id
    LEFT JOIN form_list_payer_type pyt ON pyt.code = bi.type_id
    LEFT JOIN form_site st ON st.id = bi.site_id
    LEFT JOIN form_payer_contract pc ON pc.id = lcl.shared_contract_id
    LEFT JOIN form_careplan_order_rx rx ON rx.rx_no = lcl.rx_no
    LEFT JOIN form_careplan_order co ON co.order_no = rx.order_no
    LEFT JOIN form_physician ph ON ph.id = co.physician_id
    LEFT JOIN form_list_therapy lt ON lt.code = rx.therapy_id
    LEFT JOIN form_sales_territory str ON str.id = co.territory_id
    LEFT JOIN form_user rep ON rep.id = str.commissioned_sales_rep
    LEFT JOIN form_sales_account sa ON sa.id = co.referral_source_id
    LEFT JOIN form_nurse_agency na ON na.id = co.nursing_agency_id
    LEFT JOIN form_infusion_suite infus ON infus.id = co.infusion_suite_id
    LEFT JOIN form_list_billing_method bm ON bm.code = lcl.billing_method_id
    LEFT JOIN wt_pulled wt ON wt.ticket_item_no = lcl.ticket_item_no
    LEFT JOIN physician_location pl ON pl.physician_id = ph.id
    WHERE lcl.archived IS NOT TRUE
    AND lcl.deleted IS NOT TRUE
    AND COALESCE(lcl.void, 'No') <> 'Yes';


CREATE OR REPLACE VIEW vw_revenue_report_charge_line AS
WITH revenue AS (
    SELECT DISTINCT ON (lf.charge_line_id)
        lf.charge_line_id,
        SUM(COALESCE(lf.credit, 0) - COALESCE(lf.debit, 0)) AS amount
    FROM
        form_ledger_finance lf
    WHERE
        lf.account_type = 'Revenue'
    	GROUP BY lf.charge_line_id
),
contra_revenue AS (
    SELECT DISTINCT ON (lf.charge_line_id)
        lf.charge_line_id,
        SUM(COALESCE(lf.debit, 0) - COALESCE(lf.credit, 0)) AS amount
    FROM
        form_ledger_finance lf
    WHERE
        lf.account_type = 'Contra-Revenue'
    	GROUP BY lf.charge_line_id
),
revenue_summary AS (
    SELECT DISTINCT ON (lcl.charge_line_id)
        lcl.charge_line_id,
        ROUND(SUM(re.amount)::numeric,2) AS gross_revenue_amount,
        format_currency(ROUND(SUM(re.amount)::numeric,2)::numeric) as gross_revenue,
        ROUND(SUM(re.amount)::numeric - SUM(COALESCE(cre.amount,0.0)::numeric),2) AS net_revenue_amount,
        format_currency(ROUND(SUM(re.amount)::numeric-SUM(COALESCE(cre.amount,0.0)::numeric),2)::numeric) as net_revenue
    FROM
        vw_charge_line_report lcl
        INNER JOIN revenue re ON re.charge_line_id = lcl.charge_line_id
        LEFT JOIN contra_revenue cre ON cre.charge_line_id = lcl.charge_line_id
    	GROUP BY lcl.charge_line_id
)
SELECT
	lcl.*,
    re.gross_revenue,
    re.gross_revenue_amount,
    re.net_revenue,
    re.net_revenue_amount    
FROM
    vw_charge_line_report lcl
JOIN revenue_summary re ON re.charge_line_id = lcl.charge_line_id
ORDER BY
    lcl.charge_line_id DESC;


CREATE OR REPLACE VIEW vw_master_charge_line_report AS
WITH charge_lines_revenue AS (
    SELECT DISTINCT ON (lcl.master_charge_no)
        lcl.master_charge_no,
        ROUND(SUM(lcl.gross_revenue_amount)::numeric,2) as gross_revenue_amount,
        format_currency(ROUND(SUM(lcl.gross_revenue_amount)::numeric,2)::numeric) as gross_revenue,
        ROUND(SUM(lcl.net_revenue_amount)::numeric,2)::numeric as net_revenue_amount,
        format_currency(ROUND(SUM(lcl.net_revenue_amount)::numeric,2)::numeric) as net_revenue
    FROM vw_revenue_report_charge_line lcl
    GROUP BY lcl.master_charge_no
),
profit_margin AS (
    SELECT 
        lcr.master_charge_no,
	    CASE WHEN lcr.net_revenue_amount > 0 THEN
            ROUND(((lcr.net_revenue_amount::numeric - COALESCE(clc.amount_raw, 0)::numeric) / lcr.net_revenue_amount::numeric * 100)::numeric, 2)
	    ELSE NULL::numeric
        END AS profit_margin_raw,
	    CASE WHEN lcr.net_revenue_amount > 0 THEN
	        CONCAT(format_numeric(ROUND(((lcr.net_revenue_amount::numeric - COALESCE(clc.amount_raw, 0)::numeric) / lcr.net_revenue_amount::numeric * 100)::numeric, 2)::numeric), '%')
	    ELSE NULL::text
	    END AS profit_margin,
	    CASE WHEN lcr.net_revenue_amount > 0 THEN
	        ROUND((lcr.net_revenue_amount::numeric - COALESCE(clc.amount_raw, 0)::numeric)::numeric, 2)
	    ELSE NULL::numeric
	    END AS profit_raw,
	    CASE WHEN lcr.net_revenue_amount > 0 THEN
	        format_currency((lcr.net_revenue_amount::numeric - COALESCE(clc.amount_raw, 0)::numeric)::numeric)
	    ELSE NULL::text
	    END AS profit
    FROM charge_lines_revenue lcr
    LEFT JOIN vw_charge_line_cogs clc ON clc.master_charge_no = lcr.master_charge_no
),
awp_calcs AS (
    SELECT DISTINCT ON (lcl.master_charge_no)
        lcl.master_charge_no,
        COALESCE(lcl.awp_price::numeric * lcl.quantity_raw::numeric, 0.0)::numeric as total_awp_raw,
        format_currency(COALESCE(lcl.awp_price::numeric * lcl.quantity_raw::numeric, 0.0)::numeric) as total_awp,
        CASE 
            WHEN COALESCE(lcl.awp_price::numeric * lcl.quantity_raw::numeric, 0.0) > 0 THEN
                -ROUND((((clr.net_revenue_amount - COALESCE(lcl.awp_price::numeric * lcl.quantity_raw::numeric, 0.0)) / clr.net_revenue_amount * 100.0) * -1), 4)
            ELSE 0.0
        END as total_awp_percentage_raw,
		CASE 
		    WHEN COALESCE(lcl.awp_price::numeric * lcl.quantity_raw::numeric, 0.0) > 0 THEN
		        CONCAT('-', format_numeric(ABS(ROUND((((clr.net_revenue_amount - COALESCE(lcl.awp_price::numeric * lcl.quantity_raw::numeric, 0.0)) / clr.net_revenue_amount * 100.0) * -1), 4))::numeric), '%')
		    ELSE '0.00%'
		END as total_awp_percentage
    FROM vw_charge_line_report lcl
    INNER JOIN charge_lines_revenue clr ON clr.master_charge_no = lcl.master_charge_no
    WHERE lcl.master_charge_no = lcl.charge_no
)
SELECT
    lcl.charge_line_id,
    lcl.query_id,
    lcl.query_form,
    lcl.invoice_no,
    lcl.claim_no,
    lcl.charge_no,
    lcl.close_no,
    lcl.ticket_no,
    lcl.compound_no,
    lcl.ticket_item_no,
    lcl.parent_charge_no,
    lcl.master_charge_no,
    lcl.order_no,
    lcl.encounter_id,

    lcl.inventory_id,
    lcl.inventory_id_auto_name,
    lcl.is_specialty,
    lcl.brand_name,
    lcl.brand_name_auto_name,
    lcl.manufacturer,
    lcl.manufacturer_auto_name,
    lcl.hcpc_code,
    lcl.ndc,
    lcl.upc,
    lcl.upin,
    lcl.is_primary_drug,
    lcl.quantity_raw,
    lcl.quantity,
    lcl.metric_quantity as metric_quantity_raw,
    lcl.metric_quantity,
    lcl.billing_unit_id,
    lcl.fill_number,
    lcl.rx_no,

    lcl.pricing_source,
    lcl.shared_contract,
    lcl.shared_contract_auto_name,
    lcl.patient_id,
    lcl.patient_id_auto_name,
    lcl."MRN",
    lcl.payer,
    lcl.payer_auto_name,
    lcl.site,
    lcl.site_auto_name,
    lcl.revenue_code,
    lcl.revenue_code_auto_name,
    lcl.applied_datetime,
    lcl."Applied Date",
    lcl.post_datetime,
    lcl."Post Date",
    lcl.date_of_service,
    lcl."DOS",
    lcl.created_by,
    lcl.created_by_auto_name,
    lcl.created_on,
    lcl.zeroed,

    lcl.therapy,
    lcl.therapy_auto_name,
    lcl.physician,
    lcl.physician_auto_name,
    lcl.sales_territory,
    lcl.sales_territory_auto_name,
    lcl.sales_rep,
    lcl.sales_rep_auto_name,
    lcl.sales_account,
    lcl.sales_account_auto_name,
    lcl.nursing_agency,
    lcl.nursing_agency_auto_name,
    lcl.infusion_suite,
    lcl.infusion_suite_auto_name,
    lcl.patient_status,
    lcl.patient_status_auto_name,
    lcl.report_quantity_raw,
    lcl.report_unit_id_raw,
    lcl.report_quantity,
    lcl.billing_method_auto_name,
    lcl.billing_method,
    lcl.physician_city,
    lcl.physician_state,
    lcl.physician_state_auto_name,
    lcl.physician_zip,
    lcl.payer_type,
    lcl.payer_type_auto_name,
    clr.gross_revenue,
    clr.gross_revenue_amount,
    clr.net_revenue,
    clr.net_revenue_amount,
    pm.profit_margin,
    pm.profit_margin_raw,
    pm.profit,
    pm.profit_raw,
    ac.total_awp_percentage,
    ac.total_awp_percentage_raw,
    ac.total_awp,
    ac.total_awp_raw
FROM
    vw_charge_line_report lcl
JOIN charge_lines_revenue clr ON clr.master_charge_no = lcl.master_charge_no
JOIN profit_margin pm ON pm.master_charge_no = lcl.master_charge_no
JOIN awp_calcs ac ON ac.master_charge_no = lcl.master_charge_no
WHERE lcl.master_charge_no = lcl.charge_no
ORDER BY
    lcl.charge_line_id DESC;

CREATE OR REPLACE VIEW vw_revenue_invoice_report AS
WITH summed_revenue AS (
    SELECT DISTINCT ON (lcl.invoice_no)
        lcl.invoice_no,
        ROUND(SUM(re.gross_revenue_amount)::numeric,2) as gross_revenue_amount,
        format_currency(ROUND(SUM(re.gross_revenue_amount)::numeric,2)::numeric) as gross_revenue,
        ROUND(SUM(re.net_revenue_amount)::numeric,2) as net_revenue_amount,
        format_currency(ROUND(SUM(re.net_revenue_amount)::numeric,2)::numeric) as net_revenue
    FROM vw_charge_line_report lcl
    INNER JOIN vw_revenue_report_charge_line re ON re.charge_line_id = lcl.charge_line_id
    GROUP BY lcl.invoice_no
)
SELECT
    bi.id as query_id,
    'billing_invoice' as query_form,
    bi.invoice_no,
    bi.claim_no,
    bi.close_no,
    lcl.ticket_no,
    lcl.order_no,
    bi.parent_invoice_no,
    bi.master_invoice_no,

    lcl.inventory_id,
    lcl.inventory_id_auto_name,
    lcl.is_specialty,
    lcl.brand_name,
    lcl.brand_name_auto_name,
    lcl.manufacturer,
    lcl.manufacturer_auto_name,
    lcl.hcpc_code,
    lcl.ndc,
    lcl.upc,
    lcl.upin,
    lcl.quantity_raw,
    lcl.quantity,
    lcl.metric_quantity_raw,
    lcl.metric_quantity,
    lcl.billing_unit_id,
    lcl.fill_number,
    lcl.rx_no,

    lcl.pricing_source,
    lcl.shared_contract,
    lcl.shared_contract_auto_name,
    lcl.patient_id,
    lcl.patient_id_auto_name,
    lcl."MRN",
    lcl.payer,
    lcl.payer_auto_name,
    lcl.payer_type,
    lcl.payer_type_auto_name,
    lcl.site,
    lcl.site_auto_name,
    lcl.revenue_code,
    lcl.revenue_code_auto_name,
    bi.applied_datetime,
    TO_CHAR(bi.applied_datetime, 'MM/DD/YYYY') as "Applied Date",
    bi.post_datetime,
    TO_CHAR(bi.post_datetime, 'MM/DD/YYYY') as "Post Date",
    bi.date_of_service,
    TO_CHAR(bi.date_of_service, 'MM/DD/YYYY') as "DOS",
    bi.billed_datetime,
    TO_CHAR(bi.billed_datetime, 'MM/DD/YYYY') as "Billed Date",
    bi.created_by,
    COALESCE(usr.displayname,TRIM(CONCAT(usr.firstname, ' ',usr.lastname)))::text as created_by_auto_name,
    bi.created_on,
    bi.zeroed,

    lcl.therapy,
    lcl.therapy_auto_name,
    lcl.physician,
    lcl.physician_auto_name,
    lcl.sales_territory,
    lcl.sales_territory_auto_name,
    lcl.sales_rep,
    lcl.sales_rep_auto_name,
    lcl.sales_account,
    lcl.sales_account_auto_name,
    lcl.nursing_agency,
    lcl.nursing_agency_auto_name,
    lcl.infusion_suite,
    lcl.infusion_suite_auto_name,
    lcl.patient_status,
    lcl.patient_status_auto_name,
    lcl.billing_method,
    lcl.billing_method_auto_name,
    lcl.revenue_accepted_posted,
    lcl.physician_city,
    lcl.physician_state,
    lcl.physician_state_auto_name,
    lcl.physician_zip,
    sr.gross_revenue,
    sr.gross_revenue_amount,
    sr.net_revenue,
    sr.net_revenue_amount,
    lcl."Last Name",
    lcl."First Name",
    lcl.patient_name,
    lcl.item
    FROM form_billing_invoice bi
    INNER JOIN summed_revenue sr ON sr.invoice_no = bi.invoice_no
    INNER JOIN vw_invoice_highest_priced_item_info hpi ON hpi.invoice_no = bi.invoice_no
    INNER JOIN vw_charge_line_report lcl ON hpi.charge_line_id = lcl.charge_line_id
    INNER JOIN form_user usr ON usr.id = bi.created_by
    WHERE bi.archived IS NOT TRUE
    AND bi.deleted IS NOT TRUE
    AND COALESCE(bi.void, 'No') <> 'Yes';

CREATE OR REPLACE VIEW vw_master_invoice_report AS
WITH invoices_revenue AS (
    SELECT DISTINCT ON (bi.master_invoice_no)
        bi.master_invoice_no,
        ROUND(SUM(bi.gross_revenue_amount)::numeric,2) as gross_revenue_amount,
        format_currency(ROUND(SUM(bi.gross_revenue_amount)::numeric,2)::numeric) as gross_revenue,
        ROUND(SUM(bi.net_revenue_amount)::numeric,2) as net_revenue_amount,
        format_currency(ROUND(SUM(bi.net_revenue_amount)::numeric,2)::numeric) as net_revenue
    FROM vw_revenue_invoice_report bi
    GROUP BY bi.master_invoice_no
),
profit_margin AS (
    SELECT 
        bir.master_invoice_no,
	    CASE WHEN bir.net_revenue_amount > 0 THEN
            ROUND(((bir.net_revenue_amount::numeric - COALESCE(clc.amount_raw, 0)::numeric) / bir.net_revenue_amount::numeric * 100)::numeric, 2)
	    ELSE NULL::numeric
        END AS profit_margin_raw,
	    CASE WHEN bir.net_revenue_amount > 0 THEN
	        CONCAT(format_numeric(ROUND(((bir.net_revenue_amount::numeric - COALESCE(clc.amount_raw, 0)::numeric) / bir.net_revenue_amount::numeric * 100)::numeric, 2)::numeric), '%')
	    ELSE NULL::text
	    END AS profit_margin,
	    CASE WHEN bir.net_revenue_amount > 0 THEN
	        ROUND((bir.net_revenue_amount::numeric - COALESCE(clc.amount_raw, 0)::numeric)::numeric, 2)
	    ELSE NULL::numeric
	    END AS profit_raw,
	    CASE WHEN bir.net_revenue_amount > 0 THEN
	        format_currency((bir.net_revenue_amount::numeric - COALESCE(clc.amount_raw, 0)::numeric)::numeric)
	    ELSE NULL::text
	    END AS profit
    FROM invoices_revenue bir
    LEFT JOIN vw_invoice_cogs clc ON clc.master_invoice_no = bir.master_invoice_no
),
awp_calcs AS (
    SELECT DISTINCT ON (bir.master_invoice_no)
        bir.master_invoice_no,
        COALESCE(SUM(lcl.awp_price::numeric * lcl.quantity_raw::numeric), 0.0)::numeric as total_awp_raw,
        format_currency(COALESCE(SUM(lcl.awp_price::numeric * lcl.quantity_raw::numeric), 0.0)::numeric) as total_awp,
        CASE 
            WHEN COALESCE(SUM(lcl.awp_price::numeric * lcl.quantity_raw::numeric), 0.0) > 0 THEN
                -ROUND((((bir.net_revenue_amount - COALESCE(SUM(lcl.awp_price::numeric * lcl.quantity_raw::numeric), 0.0)) / bir.net_revenue_amount * 100.0) * -1), 4)
            ELSE 0.0
        END as total_awp_percentage_raw,
		CASE 
		    WHEN COALESCE(SUM(lcl.awp_price::numeric * lcl.quantity_raw::numeric), 0.0) > 0 THEN
		        CONCAT('-', format_numeric(ABS(ROUND((((bir.net_revenue_amount - COALESCE(SUM(lcl.awp_price::numeric * lcl.quantity_raw::numeric), 0.0)) / bir.net_revenue_amount * 100.0) * -1), 4))::numeric), '%')
		    ELSE '0.00%'
		END as total_awp_percentage
    FROM vw_charge_line_report lcl
    INNER JOIN invoices_revenue bir ON CONCAT(bir.master_invoice_no,'-1') = lcl.invoice_no
    GROUP BY bir.master_invoice_no, bir.net_revenue_amount
)
SELECT
    bi.query_id,
    bi.query_form,
    bi.invoice_no,
    bi.claim_no,
    bi.close_no,
    bi.ticket_no,
    bi.order_no,
    bi.parent_invoice_no,
    bi.master_invoice_no,

    bi.inventory_id,
    bi.inventory_id_auto_name,
    bi.is_specialty,
    bi.brand_name,
    bi.brand_name_auto_name,
    bi.manufacturer,
    bi.manufacturer_auto_name,
    bi.hcpc_code,
    bi.ndc,
    bi.upc,
    bi.upin,
    bi.quantity_raw,
    bi.quantity,
    bi.metric_quantity_raw,
    bi.metric_quantity,
    bi.billing_unit_id,
    bi.fill_number,
    bi.rx_no,

    bi.pricing_source,
    bi.shared_contract,
    bi.shared_contract_auto_name,
    bi.patient_id,
    bi.patient_id_auto_name,
    bi."MRN",
    bi.payer,
    bi.payer_auto_name,
    bi.payer_type,
    bi.payer_type_auto_name,
    bi.site,
    bi.site_auto_name,
    bi.revenue_code,
    bi.revenue_code_auto_name,
    bi.applied_datetime,
    bi."Applied Date",
    bi.post_datetime,
    bi."Post Date",
    bi.date_of_service,
    bi."DOS",
    bi.billed_datetime,
    bi."Billed Date",
    bi.created_by,
    bi.created_by_auto_name,
    bi.created_on,
    bi.zeroed,

    bi.therapy,
    bi.therapy_auto_name,
    bi.physician,
    bi.physician_auto_name,
    bi.sales_territory,
    bi.sales_territory_auto_name,
    bi.sales_rep,
    bi.sales_rep_auto_name,
    bi.sales_account,
    bi.sales_account_auto_name,
    bi.nursing_agency,
    bi.nursing_agency_auto_name,
    bi.infusion_suite,
    bi.infusion_suite_auto_name,
    bi.patient_status,
    bi.patient_status_auto_name,
    bi.billing_method,
    bi.billing_method_auto_name,
    bi.revenue_accepted_posted,
    bi.physician_city,
    bi.physician_state,
    bi.physician_state_auto_name,
    bi.physician_zip,
    pm.profit_margin,
    pm.profit_margin_raw,
    pm.profit,
    pm.profit_raw,
    ac.total_awp_percentage,
    ac.total_awp_percentage_raw,
    ac.total_awp,
    ac.total_awp_raw,
    iva.status as "Claim Status",
    iva.claim_substatus_id,
    iva.claim_substatus_id_auto_name,
    iva.claim_substatus_id_auto_name as "Claim Substatus"
FROM vw_revenue_invoice_report bi
LEFT JOIN vw_invoice_claim_response_details iva ON iva.invoice_id = bi.query_id
INNER JOIN invoices_revenue ir ON CONCAT(ir.master_invoice_no,'-1') = bi.invoice_no
INNER JOIN profit_margin pm ON CONCAT(pm.master_invoice_no,'-1') = bi.invoice_no
INNER JOIN awp_calcs ac ON CONCAT(ac.master_invoice_no,'-1') = bi.invoice_no
WHERE bi.invoice_no = CONCAT(bi.master_invoice_no,'-1');

-- Annualized Revenue by Physician
CREATE OR REPLACE VIEW vw_annualized_revenue_by_physician AS
WITH annual_physician_revenue AS (
    SELECT 
        lcl.physician,
        lcl.physician_auto_name,
        EXTRACT(YEAR FROM bi.date_of_service) AS revenue_year,
        ROUND(SUM(re.gross_revenue_amount)::numeric, 2) AS gross_revenue_amount,
        format_currency(ROUND(SUM(re.gross_revenue_amount)::numeric, 2)::numeric) as gross_revenue,
        ROUND(SUM(re.net_revenue_amount)::numeric, 2) AS net_revenue_amount,
        format_currency(ROUND(SUM(re.net_revenue_amount)::numeric, 2)::numeric) as net_revenue
    FROM form_billing_invoice bi
    INNER JOIN vw_charge_line_report lcl ON lcl.invoice_no = bi.invoice_no
    INNER JOIN vw_revenue_report_charge_line re ON re.charge_line_id = lcl.charge_line_id
    WHERE 
        bi.archived IS NOT TRUE 
        AND bi.deleted IS NOT TRUE 
        AND COALESCE(bi.void, 'No') <> 'Yes'
    GROUP BY 
        lcl.physician,
        lcl.physician_auto_name,
        EXTRACT(YEAR FROM bi.date_of_service)
)
SELECT 
    physician,
    physician_auto_name,
    revenue_year,
    gross_revenue_amount,
    gross_revenue,
    net_revenue_amount,
    net_revenue
FROM annual_physician_revenue;

-- Annualized Revenue by Therapy
CREATE OR REPLACE VIEW vw_annualized_revenue_by_therapy AS
WITH annual_therapy_revenue AS (
    SELECT 
        lcl.therapy,
        lcl.therapy_auto_name,
        EXTRACT(YEAR FROM bi.date_of_service) AS revenue_year,
        ROUND(SUM(re.gross_revenue_amount)::numeric, 2) AS gross_revenue_amount,
        format_currency(ROUND(SUM(re.gross_revenue_amount)::numeric, 2)::numeric) as gross_revenue,
        ROUND(SUM(re.net_revenue_amount)::numeric, 2) AS net_revenue_amount,
        format_currency(ROUND(SUM(re.net_revenue_amount)::numeric, 2)::numeric) as net_revenue
    FROM form_billing_invoice bi
    INNER JOIN vw_charge_line_report lcl ON lcl.invoice_no = bi.invoice_no
    INNER JOIN vw_revenue_report_charge_line re ON re.charge_line_id = lcl.charge_line_id
    WHERE 
        bi.archived IS NOT TRUE 
        AND bi.deleted IS NOT TRUE 
        AND COALESCE(bi.void, 'No') <> 'Yes'
    GROUP BY 
        lcl.therapy,
        lcl.therapy_auto_name,
        EXTRACT(YEAR FROM bi.date_of_service)
)
SELECT 
    therapy,
    therapy_auto_name,
    revenue_year,
    gross_revenue_amount,
    gross_revenue,
    net_revenue_amount,
    net_revenue
FROM annual_therapy_revenue;

-- Annualized Revenue by Revenue Code
CREATE OR REPLACE VIEW vw_annualized_revenue_by_revenue_code AS
WITH annual_revenue_code_revenue AS (
    SELECT 
        lcl.revenue_code,
        lcl.revenue_code_auto_name,
        EXTRACT(YEAR FROM bi.date_of_service) AS revenue_year,
        ROUND(SUM(re.gross_revenue_amount)::numeric, 2) AS gross_revenue_amount,
        format_currency(ROUND(SUM(re.gross_revenue_amount)::numeric, 2)::numeric) as gross_revenue,
        ROUND(SUM(re.net_revenue_amount)::numeric, 2) AS net_revenue_amount,
        format_currency(ROUND(SUM(re.net_revenue_amount)::numeric, 2)::numeric) as net_revenue
    FROM form_billing_invoice bi
    INNER JOIN vw_charge_line_report lcl ON lcl.invoice_no = bi.invoice_no
    INNER JOIN vw_revenue_report_charge_line re ON re.charge_line_id = lcl.charge_line_id
    WHERE 
        bi.archived IS NOT TRUE 
        AND bi.deleted IS NOT TRUE 
        AND COALESCE(bi.void, 'No') <> 'Yes'
    GROUP BY 
        lcl.revenue_code,
        lcl.revenue_code_auto_name,
        EXTRACT(YEAR FROM bi.date_of_service)
)
SELECT 
    revenue_code,
    revenue_code_auto_name,
    revenue_year,
    gross_revenue_amount,
    gross_revenue,
    net_revenue_amount,
    net_revenue
FROM annual_revenue_code_revenue;

-- Annualized Revenue by Brand Name
CREATE OR REPLACE VIEW vw_annualized_revenue_by_brand_name AS
WITH annual_brand_revenue AS (
    SELECT 
        lcl.brand_name,
        lcl.brand_name_auto_name,
        EXTRACT(YEAR FROM bi.date_of_service) AS revenue_year,
        ROUND(SUM(re.gross_revenue_amount)::numeric, 2) AS gross_revenue_amount,
        format_currency(ROUND(SUM(re.gross_revenue_amount)::numeric, 2)::numeric) as gross_revenue,
        ROUND(SUM(re.net_revenue_amount)::numeric, 2) AS net_revenue_amount,
        format_currency(ROUND(SUM(re.net_revenue_amount)::numeric, 2)::numeric) as net_revenue
    FROM form_billing_invoice bi
    INNER JOIN vw_charge_line_report lcl ON lcl.invoice_no = bi.invoice_no
    INNER JOIN vw_revenue_report_charge_line re ON re.charge_line_id = lcl.charge_line_id
    WHERE 
        bi.archived IS NOT TRUE 
        AND bi.deleted IS NOT TRUE 
        AND COALESCE(bi.void, 'No') <> 'Yes'
    GROUP BY 
        lcl.brand_name,
        lcl.brand_name_auto_name,
        EXTRACT(YEAR FROM bi.date_of_service)
)
SELECT 
    brand_name,
    brand_name_auto_name,
    revenue_year,
    gross_revenue_amount,
    gross_revenue,
    net_revenue_amount,
    net_revenue
FROM annual_brand_revenue;

-- Annualized Revenue by Payer ID
CREATE OR REPLACE VIEW vw_annualized_revenue_by_payer AS
WITH annual_payer_revenue AS (
    SELECT 
        lcl.payer,
        lcl.payer_auto_name,
        EXTRACT(YEAR FROM bi.date_of_service) AS revenue_year,
        ROUND(SUM(re.gross_revenue_amount)::numeric, 2) AS gross_revenue_amount,
        format_currency(ROUND(SUM(re.gross_revenue_amount)::numeric, 2)::numeric) as gross_revenue,
        ROUND(SUM(re.net_revenue_amount)::numeric, 2) AS net_revenue_amount,
        format_currency(ROUND(SUM(re.net_revenue_amount)::numeric, 2)::numeric) as net_revenue
    FROM form_billing_invoice bi
    INNER JOIN vw_charge_line_report lcl ON lcl.invoice_no = bi.invoice_no
    INNER JOIN vw_revenue_report_charge_line re ON re.charge_line_id = lcl.charge_line_id
    WHERE 
        bi.archived IS NOT TRUE 
        AND bi.deleted IS NOT TRUE 
        AND COALESCE(bi.void, 'No') <> 'Yes'
    GROUP BY 
        lcl.payer,
        lcl.payer_auto_name,
        EXTRACT(YEAR FROM bi.date_of_service)
)
SELECT 
    payer,
    payer_auto_name,
    revenue_year,
    gross_revenue_amount,
    gross_revenue,
    net_revenue_amount,
    net_revenue
FROM annual_payer_revenue;

-- Annualized Revenue by Site
CREATE OR REPLACE VIEW vw_annualized_revenue_by_site AS
WITH annual_site_revenue AS (
    SELECT 
        lcl.site,
        lcl.site_auto_name,
        EXTRACT(YEAR FROM bi.date_of_service) AS revenue_year,
        ROUND(SUM(re.gross_revenue_amount)::numeric, 2) AS gross_revenue_amount,
        format_currency(ROUND(SUM(re.gross_revenue_amount)::numeric, 2)::numeric) as gross_revenue,
        ROUND(SUM(re.net_revenue_amount)::numeric, 2) AS net_revenue_amount,
        format_currency(ROUND(SUM(re.net_revenue_amount)::numeric, 2)::numeric) as net_revenue
    FROM form_billing_invoice bi
    INNER JOIN vw_charge_line_report lcl ON lcl.invoice_no = bi.invoice_no
    INNER JOIN vw_revenue_report_charge_line re ON re.charge_line_id = lcl.charge_line_id
    WHERE 
        bi.archived IS NOT TRUE 
        AND bi.deleted IS NOT TRUE 
        AND COALESCE(bi.void, 'No') <> 'Yes'
    GROUP BY 
        lcl.site,
        lcl.site_auto_name,
        EXTRACT(YEAR FROM bi.date_of_service)
)
SELECT 
    site,
    site_auto_name,
    revenue_year,
    gross_revenue_amount,
    gross_revenue,
    net_revenue_amount,
    net_revenue
FROM annual_site_revenue;

CREATE OR REPLACE VIEW vw_cash_collections_report AS
WITH cash_transactions AS (
    -- Capture all cash-related transactions from the ledger finance table
    SELECT 
        lf.id AS ledger_finance_id,
        lf.account_id,
        lf.source_id,
        lf.source_form,
        lf.invoice_id,
        lf.charge_line_id,
        lf.transaction_type,
        lf.account_type,
        lf.transaction_datetime,
        lf.post_datetime,
        lf.created_by,
        COALESCE(lf.debit, 0)::numeric AS amount_raw,
        CASE 
            WHEN lf.transaction_type LIKE '%Reversal%' THEN 'Reversal'
            ELSE 'Payment'
        END AS payment_type
    FROM 
        form_ledger_finance lf
    WHERE 
        lf.account_type = 'Cash'
        AND lf.debit > 0
)
SELECT
    ct.source_form AS query_form,
    ct.source_id AS query_id,
    ct.ledger_finance_id,
    ba.id AS account_id,
    ba.type AS account_type,
    
    CASE WHEN ba.type = 'Patient' THEN pt.id ELSE NULL END AS patient,
    CASE WHEN ba.type = 'Patient' THEN pt.auto_name ELSE NULL END AS patient_auto_name,
    CASE WHEN ba.type = 'Patient' THEN pt.lastname ELSE NULL END AS "Last Name",
    CASE WHEN ba.type = 'Patient' THEN pt.firstname ELSE NULL END AS "First Name",
    CASE WHEN ba.type = 'Patient' THEN TRIM(CONCAT(pt.lastname, ', ', pt.firstname)) ELSE NULL END AS patient_name,
    CASE WHEN ba.type = 'Patient' THEN pt.mrn ELSE NULL END AS "MRN",
    
    CASE WHEN ba.type = 'Payer' THEN py.id ELSE NULL END AS payer,
    CASE WHEN ba.type = 'Payer' THEN py.auto_name ELSE NULL END AS payer_auto_name,

    COALESCE(bi.site_id, bc.site_id) AS site,
    st.auto_name AS site_auto_name,

    bi.invoice_no AS "Invoice No",
    cl.charge_no AS "Charge No",

    ct.transaction_type AS "Transaction Type",
    ct.payment_type AS "Payment Type",
    ct.post_datetime,
    TO_CHAR(ct.post_datetime, 'MM/DD/YYYY') AS "Post Date",
    ct.transaction_datetime,
    TO_CHAR(ct.transaction_datetime, 'MM/DD/YYYY') AS "Transaction Date",

    CASE 
        WHEN ct.source_form = 'billing_cash' THEN bc.payment_method
        WHEN ct.source_form = 'billing_ar_transaction' THEN bar.payment_method
        ELSE NULL
    END AS "Payment Method",

    CASE 
        WHEN ct.source_form = 'billing_cash' AND bc.payment_method = 'Check' THEN bc.check_no
        WHEN ct.source_form = 'billing_ar_transaction' AND bar.payment_method = 'Check' THEN bar.check_no
        ELSE NULL
    END AS "Check No",

    CASE 
        WHEN ct.source_form = 'billing_cash' AND bc.payment_method = 'Other' THEN bc.other_payment_method
        WHEN ct.source_form = 'billing_ar_transaction' AND bar.payment_method = 'Other' THEN bar.other_payment_method
        ELSE NULL
    END AS "Other Payment Method",

    ct.amount_raw,
    format_currency(ct.amount_raw::numeric) as "Amount",

    COALESCE(u.displayname, TRIM(CONCAT(u.firstname, ' ', u.lastname))) AS "Posted By",
    CASE
        WHEN ct.payment_type = 'Reversal' THEN '#FCB6B6'
        ELSE NULL
    END AS __row_color
FROM 
    cash_transactions ct
JOIN 
    form_billing_account ba ON ba.id = ct.account_id
LEFT JOIN 
    form_patient pt ON pt.id = ba.patient_id AND ba.type = 'Patient'
LEFT JOIN 
    form_payer py ON py.id = ba.payer_id AND ba.type = 'Payer'
LEFT JOIN 
    form_billing_invoice bi ON bi.id = ct.invoice_id
LEFT JOIN 
    form_ledger_charge_line cl ON cl.id = ct.charge_line_id
LEFT JOIN 
    form_billing_cash bc ON bc.id = ct.source_id AND ct.source_form = 'billing_cash'
LEFT JOIN 
    form_billing_ar_transaction bar ON bar.id = ct.source_id AND ct.source_form = 'billing_ar_transaction'
LEFT JOIN 
    form_site st ON st.id = COALESCE(bi.site_id, bc.site_id)
LEFT JOIN 
    form_user u ON u.id = ct.created_by
WHERE 
    ba.archived IS NOT TRUE
    AND ba.deleted IS NOT TRUE
    AND (
        (ba.type = 'Patient' AND pt.archived IS NOT TRUE AND pt.deleted IS NOT TRUE) OR
        (ba.type = 'Payer' AND py.archived IS NOT TRUE AND py.deleted IS NOT TRUE)
    )
ORDER BY
    ct.post_datetime DESC;


CREATE OR REPLACE VIEW vw_master_profit_report AS
WITH invoice_metrics AS (
    -- Calculate key financial metrics for each invoice
    SELECT
        bi.id AS invoice_id,
        bi.invoice_no,
        bi.master_invoice_no,
        bi.patient_id,
        bi.payer_id,
        bi.site_id,
        bi.date_of_service,
        bi.billed_datetime,
        bi.billing_method_id,
        
        -- Revenue calculation from ledger finance
        ROUND(COALESCE(
            (SELECT SUM(lf.credit)::numeric
             FROM form_ledger_finance lf
             WHERE lf.invoice_id = bi.id
             AND lf.account_type = 'Revenue'),
            bi.total_expected
        )::numeric, 2) AS gross_revenue,
        
        -- COGS calculation from ledger finance
        ROUND(COALESCE(
            (SELECT SUM(lf.debit)::numeric
             FROM form_ledger_finance lf
             INNER JOIN form_ledger_charge_line lcl ON lcl.ticket_item_no = lf.ticket_item_no
             WHERE lcl.invoice_no = bi.invoice_no
             AND lf.account_type = 'COGS'),
            bi.total_cost
        )::numeric, 2) AS cost_of_goods,
        
        -- Adjustments calculation from ledger finance
        ROUND(COALESCE(
            (SELECT SUM(
                CASE 
                    WHEN account_type = 'AR' AND transaction_type = 'Adjustment' 
                    THEN credit::numeric - debit::numeric
                    ELSE 0
                END)
             FROM form_ledger_finance lf
             WHERE lf.invoice_id = bi.id),
            0
        )::numeric, 2) AS adjustments,
        
        -- Bad debt calculation from writeoffs marked as bad debt
        ROUND(COALESCE(
            (SELECT SUM(
                CASE 
                    WHEN account_type = 'AR' AND transaction_type = 'Writeoff' AND 
                         EXISTS (SELECT 1 FROM form_list_writeoff_reason wor 
                                WHERE wor.code = lf.writeoff_reason_id AND wor.bad_debt = 'Yes')
                    THEN credit::numeric - debit::numeric
                    ELSE 0
                END)
             FROM form_ledger_finance lf
             WHERE lf.invoice_id = bi.id),
            0
        )::numeric, 2) AS bad_debt
    FROM 
        form_billing_invoice bi
        LEFT JOIN vw_invoice_highest_priced_item_info hpi ON hpi.invoice_no = bi.invoice_no 

    WHERE 
        bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
        AND COALESCE(bi.void, 'No') <> 'Yes'
        AND COALESCE(bi.revenue_accepted_posted, 'No') = 'Yes'
),
-- Roll up metrics to master invoice level
master_invoice_metrics AS (
    SELECT
        im.master_invoice_no,
        SUM(im.gross_revenue) AS gross_revenue,
        SUM(im.cost_of_goods) AS cost_of_goods,
        SUM(im.adjustments) AS adjustments,
        SUM(im.bad_debt) AS bad_debt,
        -- Calculate net revenue (gross revenue minus adjustments)
        SUM(im.gross_revenue - im.adjustments) AS net_revenue
    FROM
        invoice_metrics im
    GROUP BY
        im.master_invoice_no
),
-- Get details from the master invoice only (for display purposes)
master_invoice_details AS (
    SELECT DISTINCT ON (bi.master_invoice_no)
        bi.master_invoice_no,
        bi.id AS invoice_id,
        bi.invoice_no,
        bi.patient_id,
        pt.auto_name as patient_id_auto_name,
        bi.payer_id,
        bi.site_id as site,
        st.auto_name as site_auto_name,
        bi.date_of_service,
        bi.billed_datetime,
        bi.post_datetime,
        hpi.physician_id,
        hpi.physician_last_name,
        hpi.physician_first_name,
        hpi.physician_title,
        hpi.physician_state_id as physician_state,
        hpi.physician_zip,
        hpi.physician_city,
        lcl.sales_territory,
        lcl.sales_territory_auto_name,
        lcl.sales_rep,
        lcl.sales_rep_auto_name,
        lcl.therapy,
        lcl.therapy_auto_name,
        lcl.sales_account,
        lcl.physician,
        lcl.physician_auto_name,
        lcl.infusion_suite,
        lcl.infusion_suite_auto_name,
        lcl.nursing_agency,
        lcl.nursing_agency_auto_name,
        lcl.billing_method,
        lcl.billing_method_auto_name,
        lcl.brand_name,
        lcl.brand_name_auto_name,
        lcl."First Name",
        lcl."Last Name",
        lcl."MRN",
        lcl.patient_name,
        lcl.item,
        lcl.awp_price,
        lcl.payer,
        lcl.payer_auto_name,
        lcl.payer_type,
        lcl.payer_type_auto_name,
        lcl.revenue_code,
        lcl.revenue_code_auto_name
    FROM
        form_billing_invoice bi
        JOIN form_patient pt ON pt.id = bi.patient_id
        JOIN form_site st ON st.id = bi.site_id
        JOIN vw_invoice_highest_priced_item_info hpi ON hpi.invoice_no = bi.invoice_no
        JOIN vw_charge_line_report lcl ON lcl.charge_line_id = hpi.charge_line_id
    WHERE
        bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
        AND COALESCE(bi.void, 'No') <> 'Yes'
        AND bi.invoice_no = CONCAT(bi.master_invoice_no, '-1')  -- Only get the primary invoice
)
SELECT
    'billing_invoice' AS query_form,
    mid.invoice_id AS query_id,
    mid.invoice_no,
    mid.master_invoice_no,
   	mid.physician,
    mid.physician_auto_name,
    mid.patient_id,
    mid.patient_id_auto_name,
    mid.site,
    mid.site_auto_name,
    mid.physician_last_name,
    mid.physician_first_name,
    mid.physician_title,
    mid.physician_state,
    mid.physician_zip,
    mid.physician_city,
    mid.sales_territory,
    mid.sales_territory_auto_name,
    mid.sales_rep,
    mid.sales_rep_auto_name,
    mid.therapy,
    mid.therapy_auto_name,
    mid.sales_account,
    mid.infusion_suite,
    mid.infusion_suite_auto_name,
    mid.nursing_agency,
    mid.nursing_agency_auto_name,
    mid.billing_method,
    mid.billing_method_auto_name,
    mid.brand_name,
    mid.brand_name_auto_name,
    mid."First Name",
    mid."Last Name",
    mid.patient_name,
    mid.item,
    mid.awp_price,
    mid.payer,
    mid.payer_auto_name,
    mid.revenue_code,
    mid.revenue_code_auto_name,
    mid."MRN",
    mid.payer_type,
    mid.payer_type_auto_name,

    -- Date information
    mid.date_of_service,
    TO_CHAR(mid.date_of_service, 'MM/DD/YYYY') AS "DOS",
    mid.billed_datetime,
    TO_CHAR(mid.billed_datetime, 'MM/DD/YYYY') AS "Billed Date",
    mid.post_datetime,
    TO_CHAR(mid.post_datetime, 'MM/DD/YYYY') AS "Post Date",

    -- Financial metrics with formatting (rolled up to master invoice)
    mim.gross_revenue AS gross_revenue_raw,
    format_currency(mim.gross_revenue::numeric) as "Gross Revenue",
    
    -- Net revenue (gross revenue minus adjustments)
    mim.net_revenue AS net_revenue_raw,
    format_currency(mim.net_revenue::numeric) as "Net Revenue",

    mim.cost_of_goods AS cost_of_goods_raw,
    format_currency(mim.cost_of_goods::numeric) as "COGs",

    (mim.gross_revenue - mim.cost_of_goods) AS gross_profit_raw,
    format_currency((mim.gross_revenue - mim.cost_of_goods)::numeric) as "Gross Profit",

    -- Gross profit margin
    CASE 
        WHEN mim.gross_revenue > 0 THEN 
            ROUND(((mim.gross_revenue - mim.cost_of_goods) / mim.gross_revenue * 100)::numeric, 2)
        ELSE NULL
    END AS gross_margin_raw,
    
    CASE 
        WHEN mim.gross_revenue > 0 THEN 
            CONCAT(format_numeric(ROUND(((mim.gross_revenue - mim.cost_of_goods) / mim.gross_revenue * 100)::numeric, 2)::numeric), '%')
        ELSE NULL
    END AS "Gross Margin",

    -- Adjustments
    mim.adjustments AS adjustments_raw,
    format_currency(mim.adjustments::numeric) as "Adjustments",

    -- Bad debt
    mim.bad_debt AS bad_debt_raw,
    format_currency(mim.bad_debt::numeric) as "Bad Debt",

    -- Net profit calculation (Gross profit minus adjustments and bad debt)
    (mim.gross_revenue - mim.cost_of_goods - mim.adjustments - mim.bad_debt) AS net_profit_raw,
    format_currency((mim.gross_revenue - mim.cost_of_goods - mim.adjustments - mim.bad_debt)::numeric) as "Net Profit",

    -- Net profit margin
    CASE 
        WHEN mim.gross_revenue > 0 THEN 
            ROUND(((mim.gross_revenue - mim.cost_of_goods - mim.adjustments - mim.bad_debt) / mim.gross_revenue * 100)::numeric, 2)
        ELSE NULL
    END AS net_margin_raw,

    CASE 
        WHEN mim.gross_revenue > 0 THEN 
            CONCAT(format_numeric(ROUND(((mim.gross_revenue - mim.cost_of_goods - mim.adjustments - mim.bad_debt) / mim.gross_revenue * 100)::numeric, 2)::numeric), '%')
        ELSE NULL
    END AS "Net Margin",
    iva.status as "Claim Status",
    iva.claim_substatus_id,
    iva.claim_substatus_id_auto_name,
    iva.claim_substatus_id_auto_name as "Claim Substatus",
    iva.auth_flag,
    -- Row coloring based on profitability
    CASE 
        WHEN (mim.gross_revenue - mim.cost_of_goods - mim.adjustments - mim.bad_debt) < 0 THEN '#FCB6B6'  -- Negative profit (red)
        WHEN (mim.gross_revenue - mim.cost_of_goods - mim.adjustments - mim.bad_debt) / mim.gross_revenue < 0.15 AND mim.gross_revenue > 0 THEN '#FACAA5'  -- Below 15% margin (orange)
        WHEN (mim.gross_revenue - mim.cost_of_goods - mim.adjustments - mim.bad_debt) / mim.gross_revenue > 0.3 AND mim.gross_revenue > 0 THEN '#9EDBC7'  -- Above 30% margin (green)
        ELSE NULL
    END AS __row_color
FROM 
    master_invoice_details mid
JOIN 
    master_invoice_metrics mim ON mim.master_invoice_no = mid.master_invoice_no
LEFT JOIN vw_invoice_claim_response_details iva ON iva.invoice_id = mid.invoice_id

ORDER BY
    mid.date_of_service DESC;


CREATE OR REPLACE VIEW vw_profit_report AS
SELECT * FROM vw_master_profit_report;

CREATE OR REPLACE VIEW vw_days_sales_outstanding_report AS
WITH 
company_fy_settings AS (
    SELECT 
        COALESCE(EXTRACT(MONTH FROM fiscal_year_start), 1) AS fy_start_month,
        COALESCE(EXTRACT(DAY FROM fiscal_year_start), 1) AS fy_start_day
    FROM form_company 
    WHERE id = 1
),
invoice_aging AS (
    SELECT
        bi.id AS invoice_id,
        bi.invoice_no,
        bi.master_invoice_no,
        bi.invoice_type,
        bi.patient_id,
        bi.payer_id,
        bi.site_id,
        bi.billed_datetime,
        bi.date_of_service,
        
        -- Calculate AR balance for each invoice using ledger_finance
        ROUND(COALESCE(
            (SELECT SUM(CASE WHEN lf.account_type = 'AR' THEN (lf.debit - lf.credit) ELSE 0 END)
             FROM form_ledger_finance lf
             WHERE lf.invoice_id = bi.id),
            0
        )::numeric, 2) AS outstanding_balance,
        
        -- Calculate days since billing
        CASE 
            WHEN bi.billed_datetime IS NOT NULL THEN 
                (CURRENT_DATE - bi.billed_datetime::date)
            ELSE 0
        END AS days_outstanding,
        
        -- Determine aging bucket
        CASE
            WHEN bi.billed_datetime IS NULL THEN 'Unknown'
            WHEN (CURRENT_DATE - bi.billed_datetime::date) <= 30 THEN '0-30 Days'
            WHEN (CURRENT_DATE - bi.billed_datetime::date) <= 60 THEN '31-60 Days'
            WHEN (CURRENT_DATE - bi.billed_datetime::date) <= 90 THEN '61-90 Days'
            WHEN (CURRENT_DATE - bi.billed_datetime::date) <= 120 THEN '91-120 Days'
            ELSE '120+ Days'
        END AS aging_bucket,
        
        -- Get numeric bucket for sorting
        CASE
            WHEN bi.billed_datetime IS NULL THEN 999
            WHEN (CURRENT_DATE - bi.billed_datetime::date) <= 30 THEN 1
            WHEN (CURRENT_DATE - bi.billed_datetime::date) <= 60 THEN 2
            WHEN (CURRENT_DATE - bi.billed_datetime::date) <= 90 THEN 3
            WHEN (CURRENT_DATE - bi.billed_datetime::date) <= 120 THEN 4
            ELSE 5
        END AS bucket_sort_order
    FROM 
        form_billing_invoice bi
    WHERE 
        bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
        AND COALESCE(bi.void, 'No') <> 'Yes'
        AND COALESCE(bi.revenue_accepted_posted, 'No') = 'Yes'
),
payer_dso AS (
    SELECT
        ia.payer_id,
        -- Calculate rolling 90-day revenue for this payer from ledger_finance
        ROUND(COALESCE(
            (SELECT SUM(lf.credit)::numeric
             FROM form_ledger_finance lf
             JOIN form_billing_invoice bi ON bi.id = lf.invoice_id
             WHERE lf.account_type = 'Revenue'
             AND bi.payer_id = ia.payer_id
             AND lf.post_datetime >= (CURRENT_DATE - 90)),
            0
        ), 2) AS rolling_90_day_revenue,
        
        -- Sum of outstanding balances
        SUM(ia.outstanding_balance) AS total_ar_balance,
        
        -- Calculate DSO
        CASE 
            WHEN COALESCE(
                (SELECT SUM(lf.credit)::numeric
                 FROM form_ledger_finance lf
                 JOIN form_billing_invoice bi ON bi.id = lf.invoice_id
                 WHERE lf.account_type = 'Revenue'
                 AND bi.payer_id = ia.payer_id
                 AND lf.post_datetime >= (CURRENT_DATE - 90)),
                0
            ) > 0 THEN
                ROUND((SUM(ia.outstanding_balance) / 
                    NULLIF(COALESCE(
                        (SELECT SUM(lf.credit)::numeric
                         FROM form_ledger_finance lf
                         JOIN form_billing_invoice bi ON bi.id = lf.invoice_id
                         WHERE lf.account_type = 'Revenue'
                         AND bi.payer_id = ia.payer_id
                         AND lf.post_datetime >= (CURRENT_DATE - 90)),
                        0
                    ), 0)) * 90)
            ELSE 0
        END AS days_sales_outstanding
    FROM 
        invoice_aging ia
    GROUP BY
        ia.payer_id
),
site_dso AS (
    SELECT
        ia.site_id,
        -- Sum of outstanding balances
        SUM(ia.outstanding_balance) AS total_ar_balance,
        
        -- Calculate DSO
        CASE 
            WHEN COALESCE(
                (SELECT SUM(lf.credit)::numeric
                 FROM form_ledger_finance lf
                 JOIN form_billing_invoice bi ON bi.id = lf.invoice_id
                 WHERE lf.account_type = 'Revenue'
                 AND bi.site_id = ia.site_id
                 AND lf.post_datetime >= (CURRENT_DATE - 90)),
                0
            ) > 0 THEN
                ROUND((SUM(ia.outstanding_balance)::numeric / 
                    NULLIF(COALESCE(
                        (SELECT SUM(lf.credit)::numeric
                         FROM form_ledger_finance lf
                         JOIN form_billing_invoice bi ON bi.id = lf.invoice_id
                         WHERE lf.account_type = 'Revenue'
                         AND bi.site_id = ia.site_id
                         AND lf.post_datetime >= (CURRENT_DATE - 90)),
                        0
                    ), 0)) * 90)
            ELSE 0
        END AS days_sales_outstanding
    FROM 
        invoice_aging ia
    GROUP BY
        ia.site_id
),
-- Calculate overall DSO
overall_dso AS (
    SELECT
        SUM(ia.outstanding_balance) AS total_ar_balance,
        
        -- Calculate DSO
        CASE 
            WHEN COALESCE(
                (SELECT SUM(lf.credit)::numeric
                 FROM form_ledger_finance lf
                 WHERE lf.account_type = 'Revenue'
                 AND lf.post_datetime >= (CURRENT_DATE - 90)),
                0
            ) > 0 THEN
                ROUND((SUM(ia.outstanding_balance) / 
                    NULLIF(COALESCE(
                        (SELECT SUM(lf.credit)::numeric
                         FROM form_ledger_finance lf
                         WHERE lf.account_type = 'Revenue'
                         AND lf.post_datetime >= (CURRENT_DATE - 90)),
                        0
                    ), 0)) * 90)
            ELSE 0
        END AS days_sales_outstanding
    FROM 
        invoice_aging ia
)
SELECT
    'billing_invoice' AS query_form,
    ia.invoice_id AS query_id,
    ia.invoice_no,
    ia.master_invoice_no,
    ia.invoice_type,
    ia.patient_id as patient,
    pt.auto_name AS patient_auto_name,
    ia.payer_id as payer,
    py.auto_name AS payer_auto_name,
    ia.site_id as site,
    st.auto_name AS site_auto_name,

    -- Customer/patient details
    pt.lastname AS "Last Name",
    pt.firstname AS "First Name",
    TRIM(CONCAT(pt.lastname, ', ', pt.firstname)) AS patient_name,
    pt.mrn AS "MRN",

    -- Date information
    ia.billed_datetime,
    TO_CHAR(ia.billed_datetime, 'MM/DD/YYYY') AS "Billed Date",
    ia.date_of_service,
    TO_CHAR(ia.date_of_service, 'MM/DD/YYYY') AS "DOS",

    -- Aging information
    ia.days_outstanding,
    ia.aging_bucket AS "Aging Bucket",

    -- Financial information
    ia.outstanding_balance AS balance_raw,
    format_currency(ia.outstanding_balance::numeric) as "Outstanding Balance",

    -- DSO metrics
    dso_py.days_sales_outstanding AS payer_dso,
    dso_s.days_sales_outstanding AS site_dso,
    dso_overall.days_sales_outstanding AS overall_dso,

    -- Formatted DSO displays
    CONCAT(dso_py.days_sales_outstanding, ' days') AS "Payer DSO",
    CONCAT(dso_s.days_sales_outstanding, ' days') AS "Site DSO",
    CONCAT(dso_overall.days_sales_outstanding, ' days') AS "Overall DSO",

    -- Check if this invoice's age exceeds the payer's DSO
    CASE 
        WHEN ia.days_outstanding > dso_py.days_sales_outstanding AND dso_py.days_sales_outstanding > 0 THEN 'Yes'
        ELSE 'No'
    END AS "Exceeds Payer DSO",

    -- Color code rows by age
    CASE
        WHEN ia.bucket_sort_order = 5 THEN '#FCB6B6'  -- 120+ days (red)
        WHEN ia.bucket_sort_order = 4 THEN '#FACAA5'  -- 91-120 days (orange)
        WHEN ia.bucket_sort_order = 3 THEN '#FAE3AF'  -- 61-90 days (yellow)
        WHEN ia.days_outstanding > dso_py.days_sales_outstanding AND dso_py.days_sales_outstanding > 0 THEN '#E8CAFF'  -- Exceeds payer DSO (purple)
        ELSE NULL
    END AS __row_color,

    -- Additional aggregation for reporting
    dso_py.total_ar_balance AS payer_ar_balance_raw,
    format_currency(dso_py.total_ar_balance::numeric) as "Payer AR Balance",
    
    dso_py.rolling_90_day_revenue AS payer_revenue_raw,
    format_currency(dso_py.rolling_90_day_revenue::numeric) as "Payer 90 Day Revenue"
FROM 
    invoice_aging ia
JOIN 
    form_patient pt ON pt.id = ia.patient_id
JOIN 
    form_payer py ON py.id = ia.payer_id
JOIN 
    form_site st ON st.id = ia.site_id
LEFT JOIN
    payer_dso dso_py ON dso_py.payer_id = ia.payer_id
LEFT JOIN
    site_dso dso_s ON dso_s.site_id = ia.site_id
CROSS JOIN
    overall_dso dso_overall
WHERE
    ia.outstanding_balance > 0  -- Only show invoices with outstanding balances
ORDER BY
    ia.bucket_sort_order DESC,  -- Oldest buckets first
    ia.outstanding_balance DESC;  -- Highest balances first for each bucket


CREATE OR REPLACE VIEW vw_pending_revenue_report AS
SELECT
    bi.id as invoice_id,
    'billing_invoice' AS query_form,
    bi.id AS query_id,
    bi.invoice_no as "Invoice No",
    TO_CHAR(bi.date_of_service, 'MM/DD/YYYY') as "DOS",
    pt.id AS patient_id,
    pt.auto_name as patient_id_auto_name,
    py.id AS payer,
    py.organization as payer_auto_name,
    bi.site_id as site,
    st."name" as site_auto_name,
    pt.lastname AS "Last Name",
    pt.firstname AS "First Name",
    pt.mrn as "MRN",
    hpi.pri_item,
    hpi.inventory_id,
    hpi.brand_name_id as brand_name,
    fdb.auto_name as brand_name_auto_name,
    hpi.therapy_id as therapy,
    thr.auto_name as therapy_auto_name,
    hpi.physician_id as physician,
    hpi.physician as physician_auto_name,
    hpi.physician_last_name,
    hpi.physician_first_name,
    hpi.physician_title,
    hpi.physician_state_id as physician_state,
    hpi.physician_zip,
    hpi.physician_city,
    bi.billed_datetime,
    bi.master_invoice_no,
    bi.invoice_no,
    bi.created_on,
    bi.post_datetime,
    bi.zeroed,
    bi.void,
    bi.on_hold,
    CASE
      WHEN COALESCE(bi.is_master_invoice, 'No') = 'Yes' THEN 1
      ELSE 2
    END as group_level,
    TO_CHAR(bi.created_on, 'MM/DD/YYYY HH:MI AM') AS "Created Date/Time",
    TO_CHAR(bi.billed_datetime, 'MM/DD/YYYY HH:MI AM') as "Billed Date/Time",
    TO_CHAR(bi.billed_datetime, 'MM/DD/YYYY') as "Billed Date",
    COALESCE(bi.total_paid, 0.0) as paid_raw,
    format_currency(COALESCE(bi.total_paid, 0.0)::numeric) as "Paid",
    COALESCE(bi.total_cost, 0.0) AS cost_raw,
    format_currency(COALESCE(bi.total_cost, 0.0)::numeric) as "Cost",
    COALESCE(bi.total_expected, 0.0) as expected_raw,
    format_currency(COALESCE(bi.total_expected, 0.0)::numeric) as "Expected",
    ROUND(COALESCE(bi.total_expected, 0.0)::numeric - COALESCE(bi.total_cost, 0.0)::numeric, 2) as profit_raw,
    format_currency(ROUND(COALESCE(bi.total_expected, 0.0)::numeric - COALESCE(bi.total_cost, 0.0)::numeric, 2)::numeric) as "Exp Profit",
    CASE
        WHEN COALESCE(bi.total_expected, 0.0) > 0 THEN
            ROUND(((COALESCE(bi.total_expected, 0.0)::numeric - COALESCE(bi.total_cost,0)::numeric) / COALESCE(bi.total_expected, 0.0)::numeric * 100)::numeric, 2)
        ELSE NULL
    END AS profit_margin_raw,
    CASE
        WHEN COALESCE(bi.total_expected, 0.0) > 0 THEN
            CONCAT(format_numeric(ROUND(((COALESCE(bi.total_expected, 0.0)::numeric - COALESCE(bi.total_cost,0)::numeric) / COALESCE(bi.total_expected, 0.0)::numeric * 100)::numeric, 2)::numeric), '%')
        ELSE NULL
    END AS "Exp Profit Margin",
    iva.status as "Claim Status",
    iva.claim_substatus_id,
    iva.claim_substatus_id_auto_name,
    iva.claim_substatus_id_auto_name as "Claim Substatus",
    iva.auth_flag,
    bi.billing_method_id as billing_method,
    bm.auto_name as billing_method_auto_name,
    CASE
        WHEN ROUND(COALESCE(bi.total_expected, 0.0)::numeric - COALESCE(bi.total_cost, 0.0)::numeric, 2) < 0.0 THEN '#BF7171'
        WHEN bi.void = 'Yes' OR bi.zeroed = 'Yes' THEN '#FCB6B6'
        WHEN bi.bill_for_denial = 'Yes' THEN '#FACAA5'
        WHEN iva.status IN ('Rejected', 'Denied', 'Error', 'Rebill Rejected', 'Reversal Rejected') THEN '#FFD9D9'
        WHEN iva.status IN ('On-hold','PA Deferred', 'Margin', 'Warning', 'Request for Additional Information', 'Awaiting Requested Information') THEN '#F5C198'
        WHEN iva.status IN ('Payable', 'Partially Paid') THEN '#9EDBC7'
        ELSE NULL
    END AS __row_color,
    CASE 
        WHEN ROUND(COALESCE(bi.total_expected, 0.0)::numeric - COALESCE(bi.total_cost, 0.0)::numeric, 2) < 0.0 THEN '#FFFFFF'
        ELSE NULL
    END AS __text_color,
    CASE 
        WHEN ROUND(COALESCE(bi.total_expected, 0.0)::numeric - COALESCE(bi.total_cost, 0.0)::numeric, 2) < 0.0 THEN '600'
        WHEN iva.status IN ('Rejected', 'Denied', 'Error', 'Rebill Rejected', 'Reversal Rejected') THEN '600'
        ELSE NULL
    END AS __font_weight
FROM 
    form_billing_invoice bi
INNER JOIN form_patient pt ON pt.id = bi.patient_id
INNER JOIN form_payer py ON py.id = bi.payer_id
INNER JOIN form_site st ON st.id = bi.site_id
INNER JOIN form_list_billing_method bm ON bm.code = bi.billing_method_id
LEFT JOIN vw_invoice_claim_response_details iva ON iva.invoice_id = bi.id
LEFT JOIN vw_invoice_highest_priced_item_info hpi ON hpi.invoice_no = bi.invoice_no
LEFT JOIN form_inventory inv ON inv.id = hpi.inventory_id
LEFT JOIN form_list_therapy thr ON thr.code = hpi.therapy_id
LEFT JOIN form_list_fdb_drug_brand fdb ON fdb.code = inv.brand_name_id

WHERE bi."deleted" IS NOT TRUE 
    AND bi.archived IS NOT TRUE
    AND COALESCE(bi.bill_for_denial, 'No') = 'No'
    AND COALESCE(bi.revenue_accepted_posted, 'No') = 'No';

CREATE OR REPLACE VIEW vw_negative_margin_report AS
WITH invoice_financials AS (
    -- Get financial metrics from vw_invoice_finance_totals
    SELECT 
        bi.id AS invoice_id,
        bi.invoice_no,
        bi.master_invoice_no,
        bi.patient_id,
        bi.payer_id,
        bi.site_id,
        bi.date_of_service,
        bi.billing_method_id,

        -- Get highest priced item to show as primary item
        (SELECT inventory_id FROM form_ledger_charge_line 
         WHERE invoice_no = bi.invoice_no 
         ORDER BY expected DESC LIMIT 1) AS primary_inventory_id,
        
        -- Financial data from the totals view
        ift.total_expected AS revenue,
        ift.total_cost AS cogs,
        ift.total_adjusted AS adjustments,
        
        -- Net revenue after adjustments
        (ift.total_expected - ift.total_adjusted) AS net_revenue,
        
        -- Profit metrics directly from the finance totals view
        ift.total_profit AS profit,
        ift.total_margin AS margin_percentage
    FROM 
        form_billing_invoice bi
    JOIN
        vw_invoice_finance_totals ift ON ift.invoice_id = bi.id
    WHERE 
        bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
        AND COALESCE(bi.void, 'No') <> 'Yes'
        AND COALESCE(bi.revenue_accepted_posted, 'No') = 'Yes'
)
-- Final negative margin report - select only invoices with negative net margin
SELECT
    'billing_invoice' AS query_form,
    if.invoice_id AS query_id,
    if.invoice_no,
    if.master_invoice_no,
    if.patient_id,
    pt.auto_name AS patient_id_auto_name,
    if.payer_id,
    py.auto_name AS payer_id_auto_name,
    if.site_id,
    st.auto_name AS site_id_auto_name,

    -- Customer/patient details
    pt.lastname AS "Last Name",
    pt.firstname AS "First Name",
    TRIM(CONCAT(pt.lastname, ', ', pt.firstname)) AS patient_name,
    pt.mrn AS "MRN",
    py.organization AS "Payer",
    py.type_id AS payer_type_id,
    pyt.auto_name AS payer_type_auto_name,
    st.name AS "Site",
    
    -- Invoice details
    if.date_of_service,
    TO_CHAR(if.date_of_service, 'MM/DD/YYYY') AS "DOS",
    if.billing_method_id,
    bm.auto_name AS billing_method_auto_name,
    
    -- Primary inventory item details
    if.primary_inventory_id AS inventory_id,
    inv.auto_name AS inventory_auto_name,
    inv.name AS inventory_name,
    inv.brand_name_id,
    fdb.auto_name AS brand_name_auto_name,
    
    -- Financial metrics
    if.revenue AS revenue_raw,
    format_currency(if.revenue::numeric) as "Revenue",
    
    if.cogs AS cogs_raw,
    format_currency(if.cogs::numeric) as "COGS",
    
    if.adjustments AS adjustments_raw,
    format_currency(if.adjustments::numeric) as "Adjustments",
    
    if.net_revenue AS net_revenue_raw,
    format_currency(if.net_revenue::numeric) as "Net Revenue",
    
    if.profit AS profit_raw,
    format_currency(if.profit::numeric) as "Profit",
    
    if.margin_percentage AS margin_raw,
    CONCAT(format_numeric(ROUND(if.margin_percentage::numeric, 2)::numeric), '%') AS "Margin",
    
    -- Row coloring by severity
    CASE 
        WHEN if.margin_percentage < -15 THEN '#BF7171'  -- Very negative (dark red)
        WHEN if.margin_percentage < 0 THEN '#FFD9D9'  -- Moderately negative (red)
        WHEN if.margin_percentage < 5 THEN '#FACAA5'    -- Slightly negative (orange)
        ELSE NULL
    END AS __row_color,
    CASE 
        WHEN if.margin_percentage < -15 THEN '#FFFFFF'
        ELSE NULL
    END AS __text_color,
    CASE 
        WHEN if.margin_percentage < 0 THEN '600'
        ELSE NULL
    END AS __font_weight
FROM 
    invoice_financials if
JOIN 
    form_patient pt ON pt.id = if.patient_id
JOIN 
    form_payer py ON py.id = if.payer_id
LEFT JOIN
    form_list_payer_type pyt ON pyt.code = py.type_id
JOIN 
    form_site st ON st.id = if.site_id
LEFT JOIN
    form_list_billing_method bm ON bm.code = if.billing_method_id
LEFT JOIN
    form_inventory inv ON inv.id = if.primary_inventory_id
LEFT JOIN
    form_list_fdb_drug_brand fdb ON fdb.code = inv.brand_name_id
WHERE
    if.margin_percentage < 0  -- Only include items with negative margin
ORDER BY
    if.margin_percentage ASC,  -- Most negative margins first
    if.date_of_service DESC;

/*
CREATE OR REPLACE VIEW vw_inventory_valuation_report AS
WITH company_settings AS (
    -- Get company settings for inventory valuation method
    SELECT 
        COALESCE(inventory_costing_method, 'FIFO') AS costing_method
    FROM 
        form_company 
    WHERE id = 1
),
current_inventory AS ( -- Get all current inventory with proper hierarchy (serial > lot > inventory)
    -- Serial-tracked items (most granular level)
    SELECT
        'serial' AS tracking_level,
        iss.id AS item_id,
        iss.inventory_id,
        iss.site_id,
        iss.serial_no,
        NULL AS lot_no,
        1 AS quantity, -- Each serial number represents 1 item
        iss.status
    FROM 
        form_inventory_serial iss
    WHERE 
        iss.archived IS NOT TRUE
        AND iss.deleted IS NOT TRUE
        AND iss.status = 'In Stock'
    
    UNION ALL
    
    -- Lot-tracked items (only include lots not already counted in serials)
    SELECT
        'lot' AS tracking_level,
        il.id AS item_id,
        il.inventory_id,
        il.site_id,
        NULL AS serial_no,
        il.lot_no,
        il.quantity,
        il.status
    FROM 
        form_inventory_lot il
    WHERE 
        il.archived IS NOT TRUE
        AND il.deleted IS NOT TRUE
        AND il.status = 'In Stock'
        AND il.quantity > 0
        -- Exclude inventory tracked at serial level
        AND NOT EXISTS (
            SELECT 1 
            FROM form_inventory_serial iss
            WHERE iss.inventory_id = il.inventory_id
            AND iss.site_id = il.site_id
            AND iss.status = 'In Stock'
        )
    
    UNION ALL
    
    -- Regular inventory (only include inventory not tracked at serial or lot level)
    SELECT
        'inventory' AS tracking_level,
        ie.id AS item_id,
        ie.inventory_id,
        ie.site_id,
        NULL AS serial_no,
        NULL AS lot_no,
        ie.quantity,
        ie.status
    FROM 
        form_inventory_each ie
    WHERE 
        ie.archived IS NOT TRUE
        AND ie.deleted IS NOT TRUE
        AND ie.status = 'In Stock'
        AND ie.quantity > 0
        -- Exclude inventory tracked at serial level
        AND NOT EXISTS (
            SELECT 1 
            FROM form_inventory_serial iss
            WHERE iss.inventory_id = ie.inventory_id
            AND iss.site_id = ie.site_id
            AND iss.status = 'In Stock'
        )
        -- Exclude inventory tracked at lot level
        AND NOT EXISTS (
            SELECT 1 
            FROM form_inventory_lot il
            WHERE il.inventory_id = ie.inventory_id
            AND il.site_id = ie.site_id
            AND il.status = 'In Stock'
        )
),
-- Calculate value based on purchase history using FIFO/LIFO
inventory_valuation AS (
    SELECT
        ci.inventory_id,
        ci.site_id,
        inv.name AS inventory_name,
        inv.type AS inventory_type,
        COALESCE(inv.is_specialty, 'No') AS is_specialty,
        ci.tracking_level,
        SUM(ci.quantity) AS total_quantity,
        cs.costing_method,
        -- Calculate value based on cost allocation
        SUM(
            CASE
                -- For serial items, get cost from ledger_serial
                WHEN ci.tracking_level = 'serial' THEN
                    (SELECT COALESCE(acquisition_cost_ea, 0)
                     FROM form_ledger_serial ls
                     WHERE ls.inventory_id = ci.inventory_id
                     AND ls.site_id = ci.site_id
                     AND ls.serial_no = ci.serial_no
                     AND ls.transaction_type = 'Purchase'
                     ORDER BY transaction_datetime DESC
                     LIMIT 1)
                     
                -- For lot items, get cost from ledger_lot
                WHEN ci.tracking_level = 'lot' THEN
                    (SELECT COALESCE(acquisition_cost_ea, 0)
                     FROM form_ledger_lot ll
                     WHERE ll.inventory_id = ci.inventory_id
                     AND ll.site_id = ci.site_id
                     AND ll.lot_no = ci.lot_no
                     AND ll.transaction_type = 'Purchase'
                     ORDER BY transaction_datetime DESC
                     LIMIT 1)
                     
                -- For regular inventory, use calculate_po_allocation function
                ELSE 
                    (SELECT SUM(total_cost) / NULLIF(SUM(quantity), 0)
                     FROM calculate_po_allocation(
                        ci.inventory_id, 
                        ci.site_id, 
                        ci.quantity, 
                        cs.costing_method, 
                        'form_ledger_inventory'))
            END * ci.quantity
        ) AS inventory_value,
        -- Get WAC price
        COALESCE(inv.wac_price, 0) AS wac_price,
        -- Get AWP price
        COALESCE(inv.awp_price, 0) AS awp_price
    FROM
        current_inventory ci
    JOIN
        form_inventory inv ON inv.id = ci.inventory_id
    CROSS JOIN
        company_settings cs
    GROUP BY
        ci.inventory_id, 
        ci.site_id, 
        inv.name, 
        inv.type, 
        inv.is_specialty,
        ci.tracking_level,
        cs.costing_method,
        inv.wac_price,
        inv.awp_price
)
SELECT
    'inventory' AS query_form,
    iv.inventory_id AS query_id,
    iv.site_id,
    st.auto_name AS site_id_auto_name,
    st.name AS "Site",
    
    -- Inventory details
    iv.inventory_id as inventory,
    inv.auto_name AS inventory_auto_name,
    iv.inventory_name AS "Inventory Item",
    iv.inventory_type AS "Type",
    fdb.id AS drug_brand,
    fdb.auto_name AS drug_brand_auto_name,
    fdb.name AS "Brand Name",
    man.id AS manufacturer,
    man.auto_name AS manufacturer_auto_name,
    man.name AS "Manufacturer",
    iv.tracking_level AS tracking_level_raw,
    CASE 
        WHEN iv.tracking_level = 'serial' THEN 'Serial'
        WHEN iv.tracking_level = 'lot' THEN 'Lot'
        ELSE 'Each'
    END AS "Tracking Level",
    
    -- Product identifiers
    iv.is_specialty,
    CASE WHEN iv.is_specialty = 'Yes' THEN 'Yes' ELSE 'No' END AS "Specialty",
    inv.hcpc_code AS "HCPC",
    inv.formatted_ndc AS "NDC",
    inv.upc AS "UPC",
    
    -- Quantity and costs
    iv.total_quantity AS quantity_raw,
    TO_CHAR(iv.total_quantity, 'FM999,999,990.00') AS "Quantity",
    
    iv.awp_price AS awp_price_raw,
    TO_CHAR(iv.awp_price, 'FM$999,999,990.00') AS "AWP Unit Price",
    
    iv.wac_price AS wac_price_raw,
    TO_CHAR(iv.wac_price, 'FM$999,999,990.00') AS "WAC Unit Price",
    
    (iv.inventory_value / NULLIF(iv.total_quantity, 0)) AS acquisition_cost_raw,
    TO_CHAR((iv.inventory_value / NULLIF(iv.total_quantity, 0)), 'FM$999,999,990.00') AS "Acquisition Unit Cost",
    
    -- Valuation totals
    (iv.total_quantity * iv.awp_price) AS awp_valuation_raw,
    TO_CHAR((iv.total_quantity * iv.awp_price), 'FM$999,999,990.00') AS "AWP Valuation",
    
    (iv.total_quantity * iv.wac_price) AS wac_valuation_raw,
    TO_CHAR((iv.total_quantity * iv.wac_price), 'FM$999,999,990.00') AS "WAC Valuation",
    
    iv.inventory_value AS acquisition_valuation_raw, 
    TO_CHAR(iv.inventory_value, 'FM$999,999,990.00') AS "Acquisition Valuation",
    
    -- Costing method used
    iv.costing_method,
    
    -- Markup calculations
    CASE 
        WHEN (iv.inventory_value / NULLIF(iv.total_quantity, 0)) > 0 THEN 
            ROUND(((iv.awp_price - (iv.inventory_value / NULLIF(iv.total_quantity, 0))) / 
                  (iv.inventory_value / NULLIF(iv.total_quantity, 0)) * 100)::numeric, 2)
        ELSE NULL
    END AS awp_markup_raw,
    
    CASE 
        WHEN (iv.inventory_value / NULLIF(iv.total_quantity, 0)) > 0 THEN 
            CONCAT(TO_CHAR(ROUND(((iv.awp_price - (iv.inventory_value / NULLIF(iv.total_quantity, 0))) / 
                  (iv.inventory_value / NULLIF(iv.total_quantity, 0)) * 100)::numeric, 2), 'FM990.00'), '%')
        ELSE NULL
    END AS "AWP Markup",
    
    CASE 
        WHEN (iv.inventory_value / NULLIF(iv.total_quantity, 0)) > 0 THEN 
            ROUND(((iv.wac_price - (iv.inventory_value / NULLIF(iv.total_quantity, 0))) / 
                  (iv.inventory_value / NULLIF(iv.total_quantity, 0)) * 100)::numeric, 2)
        ELSE NULL
    END AS wac_markup_raw,
    
    CASE 
        WHEN (iv.inventory_value / NULLIF(iv.total_quantity, 0)) > 0 THEN 
            CONCAT(TO_CHAR(ROUND(((iv.wac_price - (iv.inventory_value / NULLIF(iv.total_quantity, 0))) / 
                  (iv.inventory_value / NULLIF(iv.total_quantity, 0)) * 100)::numeric, 2), 'FM990.00'), '%')
        ELSE NULL
    END AS "WAC Markup"
        
FROM 
    inventory_valuation iv
JOIN 
    form_inventory inv ON inv.id = iv.inventory_id
JOIN 
    form_site st ON st.id = iv.site_id
LEFT JOIN 
    form_list_fdb_drug_brand fdb ON fdb.code = inv.brand_name_id
LEFT JOIN 
    form_list_manufacturer man ON man.code = inv.manufacturer_id
WHERE
    iv.total_quantity > 0  -- Only show items with inventory
ORDER BY
    iv.is_specialty DESC,  -- Specialty items first
    iv.inventory_value DESC;  -- Highest value items next
*/

CREATE OR REPLACE VIEW vw_dispense_log_report AS
WITH company_settings AS (
    -- Get company settings for inventory valuation method
    SELECT 
        COALESCE(cogs_method, 'FIFO') AS costing_method
    FROM 
        form_company 
    WHERE id = 1
),
-- Get all dispense activities from ledger tables with hierarchy (serial > lot > inventory)
dispense_activities AS (
    -- Serial-tracked dispenses (most granular)
    SELECT
        'serial' AS tracking_level,
        ls.id AS ledger_id,
        ls.ticket_item_no,
        ls.inventory_id,
        ls.site_id,
        ls.serial_no,
        NULL AS lot_no,
        ls.transaction_type,
        ls.created_on as transaction_datetime,
        ls.po_id,
        ls.quantity AS quantity_dispensed,
        ls.acquisition_cost_ea,
        (ls.quantity * ls.acquisition_cost_ea) AS total_cost,
        ls.created_by,
        ls.is_340b
    FROM 
        form_ledger_serial ls
    WHERE 
        ls.archived IS NOT TRUE
        AND ls.deleted IS NOT TRUE
        AND ls.transaction_type = 'Dispense'
    
    UNION ALL
    
    -- Lot-tracked dispenses (only include lots not already counted in serials)
    SELECT
        'lot' AS tracking_level,
        ll.id AS ledger_id,
        ll.ticket_item_no,
        ll.inventory_id,
        ll.site_id,
        NULL AS serial_no,
        ll.lot_no,
        ll.transaction_type,
        ll.created_on as transaction_datetime,
        ll.po_id,
        ll.quantity AS quantity_dispensed,
        ll.acquisition_cost_ea,
        (ll.quantity * ll.acquisition_cost_ea) AS total_cost,
        ll.created_by,
        ll.is_340b
    FROM 
        form_ledger_lot ll
    WHERE 
        ll.archived IS NOT TRUE
        AND ll.deleted IS NOT TRUE
        AND ll.transaction_type = 'Dispense'
        -- Exclude dispenses tracked at serial level
        AND NOT EXISTS (
            SELECT 1 
            FROM form_ledger_serial ls
            WHERE ls.ticket_item_no = ll.ticket_item_no
            AND ls.transaction_type = 'Dispense'
        )
    
    UNION ALL
    
    -- Regular inventory dispenses (only include inventory not at serial or lot level)
    SELECT
        'inventory' AS tracking_level,
        li.id AS ledger_id,
        li.ticket_item_no,
        li.inventory_id,
        li.site_id,
        NULL AS serial_no,
        NULL AS lot_no,
        li.transaction_type,
        li.created_on as transaction_datetime,
        li.po_id,
        li.quantity AS quantity_dispensed,
        li.acquisition_cost_ea,
        (li.quantity * li.acquisition_cost_ea) AS total_cost,
        li.created_by,
        NULL as is_340b
    FROM 
        form_ledger_inventory li
    WHERE 
        li.archived IS NOT TRUE
        AND li.deleted IS NOT TRUE
        AND li.transaction_type = 'Dispense'
        -- Exclude dispenses tracked at serial level
        AND NOT EXISTS (
            SELECT 1 
            FROM form_ledger_serial ls
            WHERE ls.ticket_item_no = li.ticket_item_no
            AND ls.transaction_type = 'Dispense'
        )
        -- Exclude dispenses tracked at lot level
        AND NOT EXISTS (
            SELECT 1 
            FROM form_ledger_lot ll
            WHERE ll.ticket_item_no = li.ticket_item_no
            AND ll.transaction_type = 'Dispense'
        )
),
-- Get additional dispense information from delivery tickets and charge lines
dispense_details AS (
    SELECT
        da.ledger_id,
        da.tracking_level,
        da.ticket_item_no,
        da.inventory_id,
        da.site_id,
        da.transaction_datetime,
        da.quantity_dispensed,
        da.acquisition_cost_ea,
        da.total_cost,
        da.created_by,
        pld.ticket_no,
        dt.patient_id,
        dt.id AS delivery_ticket_id,
        lcl.invoice_no,
        lcl.charge_no,
        lcl.id AS charge_line_id,
        bi.payer_id,
        da.is_340b,
        -- Revenue from charge line if available
        COALESCE(lcl.expected, 0) AS expected_revenue,
        -- Get AWP at time of dispense
        COALESCE(lcl.awp_price, inv.awp_price, 0) AS awp_price_at_dispense,
        da.lot_no,
        da.serial_no
    FROM
        dispense_activities da
    LEFT JOIN
        form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = da.ticket_item_no
    LEFT JOIN
        form_careplan_delivery_tick dt ON dt.ticket_no = pld.ticket_no
    LEFT JOIN
        form_ledger_charge_line lcl ON lcl.ticket_item_no = da.ticket_item_no
    LEFT JOIN
        form_billing_invoice bi ON bi.invoice_no = lcl.invoice_no
    LEFT JOIN
        form_inventory inv ON inv.id = da.inventory_id
    WHERE
        pld.archived IS NOT TRUE
        AND pld.deleted IS NOT TRUE
        AND COALESCE(pld.void, 'No') <> 'Yes'
)
SELECT
    dd.ledger_id AS id,
    'careplan_delivery_tick' AS query_form,
    dt.id AS query_id,
    
    -- Related IDs
    dd.ticket_no,
    dd.delivery_ticket_id,
    dt.ticket_no AS delivery_ticket_no,
    dd.invoice_no,
    dd.charge_no,
    dd.charge_line_id,
    
    -- Inventory details
    dd.inventory_id as inventory,
    inv.auto_name AS inventory_auto_name,
    inv.name AS "Item",
    inv.type AS inventory_type,
    fdb.id AS drug_brand_id,
    fdb.auto_name AS drug_brand_id_auto_name,
    fdb.name AS "Brand Name",
    man.code AS manufacturer,
    man.auto_name AS manufacturer_auto_name,
    inv.hcpc_code AS "HCPC",
    inv.formatted_ndc AS "NDC",
    dd.tracking_level AS tracking_level_raw,
    CASE 
        WHEN dd.tracking_level = 'serial' THEN 'Serial'
        WHEN dd.tracking_level = 'lot' THEN 'Lot'
        ELSE 'Each'
    END AS "Tracking Level",
    
    -- Patient and payer details
    dd.patient_id,
    pt.auto_name AS patient_id_auto_name,
    pt.lastname AS "Last Name",
    pt.firstname AS "First Name",
    TRIM(CONCAT(pt.lastname, ', ', pt.firstname)) AS patient_name,
    pt.mrn AS "MRN",
    dd.payer_id as payer,
    py.auto_name as payer_auto_name,
    dd.site_id AS site,
    st.auto_name AS site_auto_name,
    dd.is_340b as "340B?",
    dd.lot_no,
    dd.serial_no,

    -- Dispense details
    dd.transaction_datetime,
    TO_CHAR(dd.transaction_datetime, 'MM/DD/YYYY HH:MI AM') AS "Dispense Date/Time",
    dd.quantity_dispensed AS quantity_raw,
    format_numeric(dd.quantity_dispensed::numeric) AS "Quantity",
    dd.acquisition_cost_ea AS unit_cost_raw,
    format_currency(dd.acquisition_cost_ea::numeric) AS "Unit Cost",
    dd.total_cost AS total_cost_raw,
    format_currency(dd.total_cost::numeric) AS "Total Cost",
    dd.expected_revenue AS revenue_raw,
    format_currency(dd.expected_revenue::numeric) AS "Revenue",
    
    -- Profit calculation
    (dd.expected_revenue - dd.total_cost) AS profit_raw,
    format_currency((dd.expected_revenue - dd.total_cost)::numeric) AS "Profit",
    
    -- Profit margin calculation
    CASE
        WHEN dd.expected_revenue > 0 THEN 
            ROUND(((dd.expected_revenue - dd.total_cost) / dd.expected_revenue * 100)::numeric, 2)
        ELSE NULL
    END AS profit_margin_raw,
    
    CASE
        WHEN dd.expected_revenue > 0 THEN 
            CONCAT(format_numeric(ROUND(((dd.expected_revenue - dd.total_cost) / dd.expected_revenue * 100)::numeric, 2)::numeric), '%')
        ELSE NULL
    END AS "Profit Margin",
    
    -- AWP information
    dd.awp_price_at_dispense AS awp_price_raw,
    format_currency(dd.awp_price_at_dispense::numeric) AS "AWP Unit Price",
    (dd.quantity_dispensed * dd.awp_price_at_dispense) AS awp_valuation_raw,
    format_currency((dd.quantity_dispensed * dd.awp_price_at_dispense)::numeric) AS "AWP Value",
    
    -- AWP discount calculation
    CASE
        WHEN (dd.quantity_dispensed * dd.awp_price_at_dispense) > 0 THEN 
            ROUND(((dd.expected_revenue - (dd.quantity_dispensed * dd.awp_price_at_dispense)) / 
                  (dd.quantity_dispensed * dd.awp_price_at_dispense) * 100)::numeric, 2)
        ELSE NULL
    END AS awp_discount_raw,
    
    CASE
        WHEN (dd.quantity_dispensed * dd.awp_price_at_dispense) > 0 THEN 
            CONCAT(format_numeric(ROUND(((dd.expected_revenue - (dd.quantity_dispensed * dd.awp_price_at_dispense)) / 
                  (dd.quantity_dispensed * dd.awp_price_at_dispense) * 100)::numeric, 2)::numeric), '%')
        ELSE NULL
    END AS "AWP Discount",
    
    -- User who performed dispense
    dd.created_by,
    COALESCE(u.displayname, CONCAT(u.firstname, ' ', u.lastname)) AS "Dispensed By",
    
    -- Row coloring
    CASE
        WHEN dd.expected_revenue < dd.total_cost THEN '#FFD9D9'  -- Red for negative profit
        WHEN dd.expected_revenue = 0 THEN '#FAE3AF'  -- Yellow for zero revenue
        WHEN inv.is_specialty = 'Yes' THEN '#FACAA5'  -- Orange for specialty
        ELSE NULL
    END AS __row_color
FROM
    dispense_details dd
JOIN
    form_inventory inv ON inv.id = dd.inventory_id
LEFT JOIN
    form_careplan_delivery_tick dt ON dt.id = dd.delivery_ticket_id
LEFT JOIN
    form_patient pt ON pt.id = dd.patient_id
LEFT JOIN
    form_payer py ON py.id = dd.payer_id
LEFT JOIN
    form_site st ON st.id = dd.site_id
LEFT JOIN
    form_list_fdb_drug_brand fdb ON fdb.code = inv.brand_name_id
LEFT JOIN
    form_list_manufacturer man ON man.code = inv.manufacturer_id
LEFT JOIN
    form_user u ON u.id = dd.created_by
ORDER BY
    dd.transaction_datetime DESC;

CREATE OR REPLACE VIEW vw_inventory_adjustments_report AS
WITH adjustments_activities AS (

    SELECT
        'inventory' AS tracking_level,
        li.id AS ledger_id,
        li.inventory_id,
        li.site_id,
        li.transaction_type,
        li.created_on as transaction_datetime,
        li.quantity AS quantity_adjusted,
        li.created_by
    FROM 
        form_ledger_inventory li
    WHERE 
        li.archived IS NOT TRUE
        AND li.deleted IS NOT TRUE
        AND li.transaction_type = 'Adjustment'

)
SELECT
    aa.ledger_id AS id,
    NULL AS query_form,
    NULL AS query_id,
    
    -- Inventory details
    aa.inventory_id as inventory,
    inv.auto_name AS inventory_auto_name,
    inv.name AS "Item",
    inv.type AS inventory_type,
    fdb.id AS drug_brand_id,
    fdb.auto_name AS drug_brand_id_auto_name,
    fdb.name AS "Brand Name",
    man.code AS manufacturer,
    man.auto_name AS manufacturer_auto_name,
    inv.hcpc_code AS "HCPC",
    inv.formatted_ndc AS "NDC",
    aa.site_id AS site,
    st.auto_name AS site_auto_name,

    -- Dispense details
    aa.transaction_datetime,
    TO_CHAR(aa.transaction_datetime, 'MM/DD/YYYY HH:MI AM') AS "Dispense Date/Time",
    aa.quantity_adjusted AS quantity_raw,
    format_numeric(aa.quantity_adjusted::numeric) AS "Quantity",

    -- User who performed dispense
    aa.created_by,
    COALESCE(u.displayname, CONCAT(u.firstname, ' ', u.lastname)) AS "Adjusted By",

    -- Row coloring
    CASE
        WHEN aa.quantity_adjusted < 0 THEN '#FFD9D9'  -- Red for negative profit
        ELSE NULL
    END AS __row_color
FROM
    adjustments_activities aa
JOIN
    form_inventory inv ON inv.id = aa.inventory_id
LEFT JOIN
    form_site st ON st.id = aa.site_id
LEFT JOIN
    form_list_fdb_drug_brand fdb ON fdb.code = inv.brand_name_id
LEFT JOIN
    form_list_manufacturer man ON man.code = inv.manufacturer_id
LEFT JOIN
    form_user u ON u.id = aa.created_by
ORDER BY
    aa.transaction_datetime DESC;


CREATE OR REPLACE VIEW vw_inventory_purchases_report AS
WITH purchases_activites AS (
    SELECT
        'serial' AS tracking_level,
        ls.id AS ledger_id,
        ls.ticket_item_no,
        ls.inventory_id,
        ls.site_id,
        ls.serial_no,
        NULL AS lot_no,
        ls.transaction_type,
        ls.created_on as transaction_datetime,
        ls.po_id,
        ls.quantity AS quantity_purchased,
        ls.acquisition_cost_ea,
        (ls.quantity * ls.acquisition_cost_ea) AS total_cost,
        ls.created_by,
        ls.supplier_id
    FROM 
        form_ledger_serial ls
    WHERE 
        ls.archived IS NOT TRUE
        AND ls.deleted IS NOT TRUE
        AND ls.transaction_type = 'Purchase'
    
    UNION ALL
    
    -- Lot-tracked dispenses (only include lots not already counted in serials)
    SELECT
        'lot' AS tracking_level,
        ll.id AS ledger_id,
        ll.ticket_item_no,
        ll.inventory_id,
        ll.site_id,
        NULL AS serial_no,
        ll.lot_no,
        ll.transaction_type,
        ll.created_on as transaction_datetime,
        ll.po_id,
        ll.quantity AS quantity_purchased,
        ll.acquisition_cost_ea,
        (ll.quantity * ll.acquisition_cost_ea) AS total_cost,
        ll.created_by,
        ll.supplier_id
    FROM 
        form_ledger_lot ll
    WHERE 
        ll.archived IS NOT TRUE
        AND ll.deleted IS NOT TRUE
        AND ll.transaction_type = 'Purchase'
        -- Exclude dispenses tracked at serial level
        AND NOT EXISTS (
            SELECT 1 
            FROM form_ledger_serial ls
            WHERE ls.po_id = ll.po_id AND ls.inventory_id = ll.inventory_id
            AND ls.transaction_type = 'Purchase'
        )
    
    UNION ALL
    
    -- Regular inventory dispenses (only include inventory not at serial or lot level)
    SELECT
        'inventory' AS tracking_level,
        li.id AS ledger_id,
        li.ticket_item_no,
        li.inventory_id,
        li.site_id,
        NULL AS serial_no,
        NULL AS lot_no,
        li.transaction_type,
        li.created_on as transaction_datetime,
        li.po_id,
        li.quantity AS quantity_purchased,
        li.acquisition_cost_ea,
        (li.quantity * li.acquisition_cost_ea) AS total_cost,
        li.created_by,
        li.supplier_id
    FROM 
        form_ledger_inventory li
    WHERE 
        li.archived IS NOT TRUE
        AND li.deleted IS NOT TRUE
        AND li.transaction_type = 'Purchase'
        -- Exclude dispenses tracked at serial level
        AND NOT EXISTS (
            SELECT 1 
            FROM form_ledger_serial ls
            WHERE ls.po_id = li.po_id AND ls.inventory_id = li.inventory_id
            AND ls.transaction_type = 'Purchase'
        )
        -- Exclude dispenses tracked at lot level
        AND NOT EXISTS (
            SELECT 1 
            FROM form_ledger_lot ll
            WHERE ll.po_id = li.po_id AND ll.inventory_id = li.inventory_id
            AND ll.transaction_type = 'Purchase'
        )
)
SELECT
    pa.ledger_id AS id,
    'po' AS query_form,
    pa.po_id AS query_id,
    

    -- Inventory details
    pa.inventory_id as inventory,
    inv.auto_name AS inventory_auto_name,
    inv.name AS "Item",
    inv.type AS inventory_type,
    fdb.id AS drug_brand_id,
    fdb.auto_name AS drug_brand_id_auto_name,
    fdb.name AS "Brand Name",
    man.code AS manufacturer,
    man.auto_name AS manufacturer_auto_name,
    inv.hcpc_code AS "HCPC",
    inv.formatted_ndc AS "NDC",
    pa.tracking_level AS tracking_level_raw,
    CASE 
        WHEN pa.tracking_level = 'serial' THEN 'Serial'
        WHEN pa.tracking_level = 'lot' THEN 'Lot'
        ELSE 'Each'
    END AS "Tracking Level",
    
    pa.site_id AS site,
    st.auto_name AS site_auto_name,
    sup.name AS supplier,
    sup.auto_name AS supplier_auto_name,

    -- Dispense details
    po.order_date as order_date_raw,
    TO_CHAR(po.order_date, 'MM/DD/YYYY') AS "Ordered Date",
    por.receipt_date as receipt_date_raw,
    TO_CHAR(por.receipt_date, 'MM/DD/YYYY') AS "Received Date",
    pa.quantity_purchased AS quantity_raw,
    format_numeric(pa.quantity_purchased::numeric) AS "Quantity",
    pa.acquisition_cost_ea AS unit_cost_raw,
    format_currency(pa.acquisition_cost_ea::numeric) AS "Unit Cost",
    pa.total_cost AS total_cost_raw,
    format_currency(pa.total_cost::numeric) as "Total Cost",
    pa.created_by,
    COALESCE(u.displayname, CONCAT(u.firstname, ' ', u.lastname)) AS "Received By"
FROM
    purchases_activites pa
JOIN
    form_inventory inv ON inv.id = pa.inventory_id
LEFT JOIN form_list_supplier sup ON sup.id = pa.supplier_id
INNER JOIN form_po po ON po.id = pa.po_id AND po.archived IS NOT TRUE AND po.deleted IS NOT TRUE
INNER JOIN form_receipt_po por ON por.id = pa.po_id AND COALESCE(por.void, 'No') <> 'Yes'

LEFT JOIN
    form_site st ON st.id = pa.site_id
LEFT JOIN
    form_list_fdb_drug_brand fdb ON fdb.code = inv.brand_name_id
LEFT JOIN
    form_list_manufacturer man ON man.code = inv.manufacturer_id
LEFT JOIN
    form_user u ON u.id = pa.created_by
ORDER BY
    pa.transaction_datetime DESC;

CREATE OR REPLACE VIEW vw_ncpdp_medical_claims_cube_report AS
WITH physician_location as (
    SELECT DISTINCT ON (spl.form_physician_fk)
        spl.form_physician_fk as physician_id,
        pl.city,
        pl.state_id as state,
        stat.auto_name as state_auto_name,
        pl.zip
    FROM sf_form_physician_to_physician_location spl
    INNER JOIN form_physician_location pl
        ON pl.id = spl.form_physician_location_fk
    LEFT JOIN form_list_us_state stat ON stat.code = pl.state_id
    WHERE spl.archive IS NOT TRUE
        AND spl."delete" IS NOT TRUE
        AND pl.archived IS NOT TRUE
        AND pl.deleted IS NOT TRUE
    ORDER BY 
    	spl.form_physician_fk,
        CASE WHEN pl.is_primary = 'Yes' THEN 1 ELSE 0 END,
        pl.created_on DESC
),
patient_address as (
      SELECT 
            pa.patient_id,
            TRIM(CONCAT(pa.street, ' ', pa.street2)) as patient_address,
            pa.city as patient_city,
            pa.state_id as patient_state,
            pa.zip as patient_zip_code
      FROM form_patient_address pa 
      WHERE pa.archived IS NOT TRUE 
        AND pa.deleted IS NOT TRUE
      ORDER BY 
        CASE
          WHEN address_type = 'Home' THEN 0
          WHEN address_type = 'Shipping' THEN 1 
          ELSE 2
        END 
      LIMIT 1 
),
patient_diagnosis as (
    SELECT DISTINCT ON (patient_id)
        patient_id,
        dx.icd_code as patient_primary_icd10
    FROM form_patient_diagnosis ptdx
    INNER JOIN form_list_diagnosis dx ON dx.code = ptdx.dx_id
    WHERE ptdx.archived IS NOT TRUE
        AND ptdx.deleted IS NOT TRUE
        AND active = 'Yes'
        ORDER BY patient_id, rank ASC
)
SELECT
    lcl.id as charge_line_id,
    lcl.id as query_id,
    inv.id as inventory,
    inv.auto_name as inventory_auto_name,
    st.id as site,
    st.auto_name as site_auto_name,
    py.id as payer,
    py.auto_name as payer_auto_name,
    'ledger_charge_line' as query_form,
    pt.mrn as "MRN",
    lcl.invoice_no as invoice_number,
    lcl.claim_no as claim_no,
    inv.name as inventory_item_name,
    py.organization as due_payer,
    pt.lastname as patient_last_name,
    pt.firstname as patient_first_name,
    TO_CHAR(pt.dob, 'MM/DD/YYYY') as date_of_birth_date,
    CASE
        WHEN pt.gender = 'Female' THEN 'F'
        WHEN pt.gender = 'Male' THEN 'M'
        ELSE NULL
    END as patient_gender,
    adr.patient_address as patient_address,
    adr.patient_city as patient_city,
    adr.patient_state as patient_state,
    adr.patient_zip_code as patient_zip_code,
    COALESCE(pt.phone_home, pt.phone_cell, pt.phone_work) as patient_primary_phone,
    COALESCE(pi.policy_number, pi.cardholder_id) as due_patient_insurance_policy_number,
    ph.first as physician_first_name,
    ph.last as physician_last_name,
    ph.npi  as "Physician NPI",
    inv.ndc as "Inventory NDC",
    COALESCE(rxdx.patient_primary_icd10, dx.patient_primary_icd10) as patient_primary_icd10,
    pa.number as authorization_number,
    dt.service_from as "Invoice Start DOS Date",
    inv.hcpc_code as "Lineitem HCPC",
    lcl.billed as billed

    FROM form_ledger_charge_line lcl
    INNER JOIN form_inventory inv ON inv.id = lcl.inventory_id
    INNER JOIN form_patient pt ON pt.id = lcl.patient_id
    INNER JOIN form_billing_invoice bi ON bi.invoice_no = lcl.invoice_no
    INNER JOIN form_patient_insurance pi ON lcl.insurance_id = pi.id
    INNER JOIN form_careplan_delivery_tick dt ON dt.ticket_no = lcl.ticket_no
    LEFT JOIN patient_address adr ON lcl.patient_id = adr.patient_id
    LEFT JOIN form_careplan_order_rx rx ON rx.rx_no = lcl.rx_no
    LEFT JOIN form_careplan_order co ON co.order_no = rx.order_no
    LEFT JOIN form_careplan_order_item oi ON oi.rx_no = rx.rx_no
    LEFT JOIN form_careplan_orderp_item opi ON opi.rx_no = rx.rx_no
    LEFT JOIN form_physician ph ON ph.id = co.physician_id
    LEFT JOIN form_payer py ON py.id = lcl.payer_id
    LEFT JOIN form_site st ON st.id = lcl.site_id
    LEFT JOIN patient_diagnosis dx ON dx.patient_id = lcl.patient_id
    LEFT JOIN form_patient_prior_auth pa ON pa.id = COALESCE(oi.drug_pa_id, opi.drug_pa_id)

    LEFT JOIN LATERAL (
        SELECT
        	(elem->>'rank')::int,
            dx.icd_code as patient_primary_icd10
        FROM jsonb_array_elements(coalesce_if_empty_json(
            coalesce_if_empty_json(
            coalesce_if_empty_json(oi.dx_ids, opi.dx_ids), 
            co.dx_ids), 
            '[]')::jsonb) AS elem
        INNER JOIN form_patient_diagnosis ptdx 
            ON ptdx.id = (elem->>'id')::int 
            AND ptdx.archived IS NOT TRUE 
            AND ptdx.deleted IS NOT TRUE 
            AND COALESCE(ptdx.active, 'No') = 'Yes'
        INNER JOIN form_list_diagnosis dx 
            ON dx.code = ptdx.dx_id 
            AND dx.archived IS NOT TRUE 
            AND dx.deleted IS NOT TRUE
        GROUP BY (elem->>'rank')::int, dx.icd_code
        ORDER BY (elem->>'rank')::int,dx.icd_code ASC
        LIMIT 1
    ) AS rxdx ON true

    WHERE lcl.archived IS NOT TRUE
    AND lcl.deleted IS NOT TRUE
    AND COALESCE(lcl.void, 'No') <> 'Yes'
    AND COALESCE(lcl.zeroed, 'No') <> 'Yes';


CREATE OR REPLACE VIEW vw_master_claims_cube AS
SELECT
    bi.invoice_id,
    bi.query_form,
    bi.query_id,
    bi."Invoice No",
    bi."DOS",
    bi.patient_id,
    bi.patient_id_auto_name,
    bi.payer,
    bi.payer_auto_name,
    bi.site,
    bi.site_auto_name,
    bi."Last Name",
    bi."First Name",
    bi."MRN",
    bi.pri_item as item,
    bi.brand_name,
    bi.brand_name_auto_name,
    bi.therapy,
    bi.therapy_auto_name,
    bi.physician,
    bi.physician_auto_name,
    bi.physician_last_name,
    bi.physician_first_name,
    bi.physician_title,
    bi.physician_state,
    bi.physician_zip,
    bi.physician_city,
    bi.billed_datetime,
    bi.post_datetime,
    bi.cost_raw,
    bi.expected_raw,
    NULL as "Posted Date",
    bi."Billed Date",
    bi."Expected",
    bi."Exp Profit" as "Profit",
    bi."Exp Profit Margin" as "Profit Margin",
    bi."Cost",
    bi."Claim Status",
    bi.claim_substatus_id,
    bi.claim_substatus_id_auto_name,
    bi."Claim Substatus",
    bi.auth_flag
FROM vw_pending_revenue_report bi
WHERE bi.billing_method IN ('ncpdp', 'mm') AND COALESCE(bi.void, 'No') <> 'Yes'

UNION 

SELECT
    bi.query_id as invoice_id,
    bi.query_form,
    bi.query_id,
    bi.invoice_no as "Invoice No",
    bi."DOS",
    bi.patient_id,
    bi.patient_id_auto_name,
    bi.payer,
    bi.payer_auto_name,
    bi.site,
    bi.site_auto_name,
    bi."Last Name",
    bi."First Name",
    bi."MRN",
    bi.item,
    bi.brand_name,
    bi.brand_name_auto_name,
    bi.therapy,
    bi.therapy_auto_name,
    bi.physician,
    bi.physician_auto_name,
    bi.physician_last_name,
    bi.physician_first_name,
    bi.physician_title,
    bi.physician_state,
    bi.physician_zip,
    bi.physician_city,
    bi.billed_datetime,
    bi.post_datetime,
    bi.cost_of_goods_raw as cost_raw,
    bi.net_revenue_raw as expected_raw,
    NULL as "Posted Date",
    bi."Billed Date",
    bi."Net Revenue" as "Expected",
    bi."Net Profit" as "Profit",
    bi."Net Margin" as "Profit Margin",
    bi."COGs" as "Cost",
    bi."Claim Status",
    bi.claim_substatus_id,
    bi.claim_substatus_id_auto_name,
    bi."Claim Substatus",
    bi.auth_flag
FROM vw_master_profit_report bi
WHERE bi.billing_method IN ('ncpdp', 'mm');

CREATE OR REPLACE VIEW vw_initially_denied_claims AS
SELECT
    bi.invoice_no
FROM 
    form_billing_invoice bi
LEFT JOIN 
    sf_form_billing_invoice_to_med_claim bitmc ON bitmc.form_billing_invoice_fk = bi.id
    AND bitmc.archive IS NOT TRUE
    AND bitmc."delete" IS NOT TRUE
LEFT JOIN 
    form_med_claim mc ON mc.id = bitmc.form_med_claim_fk
    AND COALESCE(mc.void, 'No') <> 'Yes'
    AND mc.archived IS NOT TRUE
    AND mc.deleted IS NOT TRUE
LEFT JOIN 
    form_med_claim_resp_835 mc835 ON mc835.claim_no = mc.claim_no
    AND mc835.id = (
        SELECT MIN(id) FROM form_med_claim_resp_835 
        WHERE claim_no = mc.claim_no
    )
LEFT JOIN 
    sf_form_billing_invoice_to_ncpdp bitnc ON bitnc.form_billing_invoice_fk = bi.id
    AND bitnc.archive IS NOT TRUE
    AND bitnc."delete" IS NOT TRUE
LEFT JOIN 
    form_ncpdp nc ON nc.id = bitnc.form_ncpdp_fk
    AND COALESCE(nc.void, 'No') <> 'Yes'
    AND nc.archived IS NOT TRUE
    AND nc.deleted IS NOT TRUE
    AND COALESCE(nc.is_test, 'No') <> 'Yes'
LEFT JOIN 
    form_ncpdp_response ncr ON ncr.claim_no = nc.claim_no
    AND ncr.id = (
        SELECT MIN(id) FROM form_ncpdp_response 
        WHERE claim_no = nc.claim_no
    )
WHERE 
    bi.archived IS NOT TRUE 
    AND bi.deleted IS NOT TRUE
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND CONCAT(bi.master_invoice_no,'-1') = bi.invoice_no
    AND COALESCE(bi.bill_for_denial, 'No') <> 'Yes'
    AND (
        ncr.response_status = 'R' OR 
        ncr.transaction_response_status = 'R' OR
        mc835.claim_status_code IN ('0', '18', '21', '26', '29', '30', '31', '32', '33', '34', '35', '86', '97', '121', 
                                    '400', '401', '402', '473', '474', '475', '476', '477', '478', '479', '480', '481', 
                                    '484', '493', '495', '678', '684', '689', '690', '691', '732', '746', '784', '804', 
                                    '806', '23', '24', '25', '27', '54', '85', '95', '96', '111', '116', '487', '491', 
                                    '494', '496', '499', '667', '677', '679', '681', '770', '771', '802', '803', '81', 
                                    '83', '84', '88', '89', '90', '91', '92', '93', '94', '106', '109', '483', '498', 
                                    '674', '682', '718', '735', '744', '764', '765', '768', '774', '778', '779', '780')
    );

CREATE OR REPLACE VIEW vw_dt_charge_lines_report AS
WITH confirmed_revenue_charge_lines AS (
    -- Get charge lines with confirmed revenue (posted to ledger)
    SELECT
       lcl.id::integer as query_id,
        'ledger_charge_line' as query_form,
        lcl.id::integer AS charge_line_id,
        lcl.inventory_id::integer as item,
        inv.auto_name::text as item_auto_name,
        lcl.patient_id::integer as patient,
        pt.auto_name::text as patient_auto_name,
        lcl.insurance_id::integer as insurance,
        pi.auto_name::text as insurance_auto_name,
        lcl.payer_id::integer as payer,
        py.auto_name::text as payer_auto_name,
        lcl.ticket_no::text as ticket_no,
        lcl.ticket_item_no::text as ticket_item_no,
        clra.remaining_ar_balance::numeric AS raw_expected,
        format_currency(clra.remaining_ar_balance::numeric) AS expected,
        clra.paid_amount::numeric AS raw_paid,
        format_currency(clra.paid_amount::numeric) AS paid,
        clra.pending_copay_balance::numeric as raw_copay,
        format_currency(clra.pending_copay_balance::numeric) AS copay,
        lcl.charge_no::text as charge_no,
        COALESCE(cog.amount_raw::numeric, 0.00::numeric)::numeric AS raw_cost,
        format_currency(COALESCE(cog.amount_raw::numeric, 0.00::numeric)::numeric) AS cost,
        lcl.formatted_ndc::text as "NDC",
        lcl.hcpc_code::text as "HCPC",
        lcl.bill_quantity::numeric as raw_quantity,
        format_numeric(lcl.bill_quantity::numeric) AS quantity,
        lcl.hcpc_quantity::numeric as raw_hcpc_quantity,
        lcl.charge_quantity::numeric as raw_charge_quantity,
        format_numeric(lcl.charge_quantity::numeric) AS charge_quantity,
        lcl.metric_quantity::numeric as raw_metric_quantity,
        format_numeric(lcl.metric_quantity::numeric) AS metric_quantity,
        lcl.billing_unit_id::text as metric_unit,
        lcl.charge_unit::text as charge_unit,
        lcl.invoice_no::text as invoice_no,
        lcl.billed::numeric as raw_billed,
        format_currency(lcl.billed::numeric) as billed
    FROM 
        form_ledger_charge_line lcl
    INNER JOIN form_patient_insurance pi ON pi.id = lcl.insurance_id
    INNER JOIN form_payer py ON py.id = lcl.payer_id
    INNER JOIN form_patient pt ON pt.id = lcl.patient_id
    INNER JOIN form_inventory inv ON inv.id = lcl.inventory_id
    INNER JOIN vw_charge_line_remaining_ar_balance clra ON clra.charge_line_id = lcl.id
    LEFT JOIN
        vw_charge_line_cogs cog ON cog.master_charge_no = lcl.charge_no
    WHERE 
        lcl.archived IS NOT TRUE
        AND lcl.deleted IS NOT TRUE
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
        AND EXISTS (
            SELECT 1
            FROM form_ledger_finance lf
            WHERE lf.charge_line_id = lcl.id
            AND lf.account_type = 'AR'
        )
),
pending_revenue_charge_lines AS (
    -- Get charge lines with confirmed revenue (posted to ledger)
    SELECT
        lcl.id::integer as query_id,
        'ledger_charge_line' as query_form,
        lcl.id::integer AS charge_line_id,
        lcl.inventory_id::integer as item,
        inv.auto_name::text as item_auto_name,
        lcl.patient_id::integer as patient,
        pt.auto_name::text as patient_auto_name,
        lcl.insurance_id::integer as insurance,
        pi.auto_name::text as insurance_auto_name,
        lcl.payer_id::integer as payer,
        py.auto_name::text as payer_auto_name,
        lcl.ticket_no::text as ticket_no,
        lcl.ticket_item_no::text as ticket_item_no,
        clpar.proportional_expected_amount::numeric AS raw_expected,
        format_currency(clpar.proportional_expected_amount::numeric) AS expected,
        clpar.applied_unapplied_cash::numeric AS raw_paid,
        format_currency(clpar.applied_unapplied_cash::numeric) AS paid,
        clpar.pending_copay_balance::numeric as raw_copay,
        format_currency(clpar.pending_copay_balance::numeric) AS copay,
        lcl.charge_no::text as charge_no,
        COALESCE(cog.amount_raw::numeric, lcl.total_cost::numeric, 0.00::numeric)::numeric AS raw_cost,
        format_currency(COALESCE(cog.amount_raw::numeric, 0.00::numeric)::numeric) AS cost,
        lcl.formatted_ndc::text as "NDC",
        lcl.hcpc_code::text as "HCPC",
        lcl.bill_quantity::numeric as raw_quantity,
        format_numeric(lcl.bill_quantity::numeric) AS quantity,
        lcl.hcpc_quantity::numeric as raw_hcpc_quantity,
        lcl.charge_quantity::numeric as raw_charge_quantity,
        format_numeric(lcl.charge_quantity::numeric) AS charge_quantity,
        lcl.metric_quantity::numeric as raw_metric_quantity,
        format_numeric(lcl.metric_quantity::numeric) AS metric_quantity,
        lcl.billing_unit_id::text as metric_unit,
        lcl.charge_unit::text as charge_unit,
        lcl.invoice_no::text as invoice_no,
        lcl.billed::numeric as raw_billed,
        format_currency(lcl.billed::numeric) as billed
    FROM 
        form_ledger_charge_line lcl
    INNER JOIN form_patient_insurance pi ON pi.id = lcl.insurance_id
    INNER JOIN form_payer py ON py.id = lcl.payer_id
    INNER JOIN form_patient pt ON pt.id = lcl.patient_id
    INNER JOIN form_inventory inv ON inv.id = lcl.inventory_id
    INNER JOIN vw_charge_line_pending_ar_balance clpar ON clpar.charge_line_id = lcl.id
    LEFT JOIN
        vw_charge_line_cogs cog ON cog.master_charge_no = lcl.charge_no
    WHERE
        lcl.archived IS NOT TRUE
        AND lcl.deleted IS NOT TRUE
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
        AND NOT EXISTS (
            SELECT 1
            FROM form_ledger_finance lf
            WHERE lf.charge_line_id = lcl.id
            AND lf.account_type = 'AR'
        )
)
SELECT
    *
FROM
    confirmed_revenue_charge_lines
UNION ALL
SELECT
    *
FROM
    pending_revenue_charge_lines;