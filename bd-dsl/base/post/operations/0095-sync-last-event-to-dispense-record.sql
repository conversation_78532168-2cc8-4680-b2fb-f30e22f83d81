-- Function to sync last_event_id from order items to dispense recordsDO $$ BEGIN
DO $$ BEGIN
  PERFORM drop_all_function_signatures('sync_last_event_to_dispense');
END $$;
CREATE OR REPLACE FUNCTION sync_last_event_to_dispense()
RETURNS TRIGGER AS $$
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Only proceed if last_event_id was changed
    IF NEW.last_event_id IS DISTINCT FROM OLD.last_event_id THEN
        -- Update dispense records that match the rx_no
        UPDATE form_careplan_order_rx_disp disp
        SET last_event_id = NEW.last_event_id
        FROM vw_working_prescriptions wpr
        WHERE wpr.rx_no = NEW.rx_no AND disp.id = wpr.working_dispense_id
        AND wpr.archived IS NOT TRUE
        AND wpr.deleted IS NOT TRUE;
    END IF;

    R<PERSON>URN NEW;
EXCEPTION WHEN OTHERS THEN
    -- Log error
    INSERT INTO dispensing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        SQLERRM,
        'Exception in sync_last_event_to_dispense',
        'FUNCTION',
        current_schema(),
        TG_TABLE_NAME,
        jsonb_build_object(
            'function_name', 'sync_last_event_to_dispense',
            'rx_no', NEW.rx_no,
            'last_event_id', NEW.last_event_id
        )
    );
    
    RAISE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER sync_last_event_order_item_trigger
    AFTER UPDATE OF last_event_id ON form_careplan_order_item
    FOR EACH ROW
    EXECUTE FUNCTION sync_last_event_to_dispense();

CREATE OR REPLACE TRIGGER sync_last_event_orderp_item_trigger
    AFTER UPDATE OF last_event_id ON form_careplan_orderp_item
    FOR EACH ROW 
    EXECUTE FUNCTION sync_last_event_to_dispense();
