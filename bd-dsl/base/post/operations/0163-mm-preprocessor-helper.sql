
-- Helper function to format dates to YYYYMMDD format
CREATE OR REPLACE FUNCTION mm_format_date(input_date date)
RET<PERSON>NS text AS $$
BEGIN
    IF input_date IS NULL THEN
        RETURN NULL;
    END IF;
    RETURN TO_CHAR(input_date, 'YYYYMMDD');
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Helper function to clean phone/fax numbers (remove non-numeric characters)
CREATE OR REPLACE FUNCTION mm_clean_phone_number(phone_number text)
RETURNS text AS $$
BEGIN
    IF phone_number IS NULL THEN
        RETURN NULL;
    END IF;
    RETURN REGEXP_REPLACE(phone_number, '[^0-9]', '', 'g');
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Helper function to remove null values from JSON
CREATE OR REPLACE FUNCTION mm_remove_nulls(input_json jsonb)
RETURNS jsonb AS $$
DECLARE
    result jsonb = '{}';
    key text;
    value jsonb;
BEGIN
    FOR key, value IN SELECT * FROM jsonb_each(input_json)
    LOOP
        IF value IS NOT NULL AND value::text != 'null' THEN
            IF jsonb_typeof(value) = 'object' THEN
                value = mm_remove_nulls(value);
                IF value::text != '{}' THEN
                    result = result || jsonb_build_object(key, value);
                END IF;
            ELSIF jsonb_typeof(value) = 'array' THEN
                IF jsonb_array_length(value) > 0 THEN
                    result = result || jsonb_build_object(key, value);
                END IF;
            ELSE
                result = result || jsonb_build_object(key, value);
            END IF;
        END IF;
    END LOOP;
    RETURN result;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Helper function to merge jsonb objects without double escaping
CREATE OR REPLACE FUNCTION mm_jsonb_merge(target jsonb, source jsonb)
RETURNS jsonb AS $$
BEGIN
    RETURN target || source;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Helper function to build address JSON
CREATE OR REPLACE FUNCTION mm_build_address(
    p_address1 text,
    p_address2 text,
    p_city text,
    p_state text,
    p_postal_code text
)
RETURNS jsonb AS $$
DECLARE
    v_address jsonb;
BEGIN
    v_address = jsonb_build_object(
        'address1', p_address1,
        'address2', p_address2,
        'city', p_city,
        'state', p_state,
        'postalCode', p_postal_code
    );
    
    RETURN mm_remove_nulls(v_address);
END;
$$ LANGUAGE plpgsql;

-- Helper function to build contact information JSON
CREATE OR REPLACE FUNCTION mm_build_contact_info(
    p_name text,
    p_phone_number text DEFAULT NULL,
    p_phone_extension text DEFAULT NULL,
    p_fax_number text DEFAULT NULL,
    p_email text DEFAULT NULL
)
RETURNS jsonb AS $$
DECLARE
    v_contact jsonb;
BEGIN
    v_contact = jsonb_build_object(
        'name', p_name,
        'phoneNumber', mm_clean_phone_number(p_phone_number),
        'phoneExtension', mm_clean_phone_number(p_phone_extension),
        'faxNumber', mm_clean_phone_number(p_fax_number),
        'email', p_email
    );
    
    RETURN mm_remove_nulls(v_contact);
END;
$$ LANGUAGE plpgsql;

-- Function to build submitter segment
CREATE OR REPLACE FUNCTION mm_build_submitter(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_submitter jsonb;
    v_contact_info jsonb;
BEGIN
    -- First get contact info separately
    SELECT mm_build_contact_info(
        sc.name,
        sc.phone_number,
        NULL::text, -- phone_extension column doesn't exist
        sc.fax_number,
        sc.email
    ) INTO v_contact_info
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_submitter sf_s ON sf_s.form_med_claim_fk = mc.id AND sf_s.delete IS NOT TRUE AND sf_s.archive IS NOT TRUE
    INNER JOIN form_med_claim_submitter s ON s.id = sf_s.form_med_claim_submitter_fk AND s.deleted IS NOT TRUE AND s.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_submitter_to_med_claim_smt_cont sf_sc ON sf_sc.form_med_claim_submitter_fk = s.id AND sf_sc.delete IS NOT TRUE AND sf_sc.archive IS NOT TRUE
    INNER JOIN form_med_claim_smt_cont sc ON sc.id = sf_sc.form_med_claim_smt_cont_fk AND sc.deleted IS NOT TRUE AND sc.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Build submitter object
    SELECT jsonb_build_object(
        'organizationName', s.organization_name
    ) INTO v_submitter
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_submitter sf_s ON sf_s.form_med_claim_fk = mc.id AND sf_s.delete IS NOT TRUE AND sf_s.archive IS NOT TRUE
    INNER JOIN form_med_claim_submitter s ON s.id = sf_s.form_med_claim_submitter_fk AND s.deleted IS NOT TRUE AND s.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Merge contact info if exists
    IF v_contact_info IS NOT NULL THEN
        v_submitter := v_submitter || jsonb_build_object('contactInformation', v_contact_info);
    END IF;
    
    IF v_submitter IS NULL THEN
        RAISE EXCEPTION 'Submitter information not found for med_claim_id: %', p_med_claim_id;
    END IF;
    
    RETURN mm_remove_nulls(v_submitter);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_submitter',
            'FUNCTION',
            current_schema(),
            'form_med_claim_submitter',
            jsonb_build_object(
                'function_name', 'mm_build_submitter',
                'med_claim_id', p_med_claim_id
            )
        );
        RAISE;
END;
$$ LANGUAGE plpgsql;

-- Function to build receiver segment
CREATE OR REPLACE FUNCTION mm_build_receiver(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_receiver jsonb;
BEGIN
    SELECT jsonb_build_object(
        'organizationName', r.organization_name
    ) INTO v_receiver
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_receiver sf_r ON sf_r.form_med_claim_fk = mc.id AND sf_r.delete IS NOT TRUE AND sf_r.archive IS NOT TRUE
    INNER JOIN form_med_claim_receiver r ON r.id = sf_r.form_med_claim_receiver_fk AND r.deleted IS NOT TRUE AND r.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    IF v_receiver IS NULL THEN
        RAISE EXCEPTION 'Receiver information not found for med_claim_id: %', p_med_claim_id;
    END IF;
    
    RETURN mm_remove_nulls(v_receiver);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_receiver',
            'FUNCTION',
            current_schema(),
            'form_med_claim_receiver',
            jsonb_build_object(
                'function_name', 'mm_build_receiver',
                'med_claim_id', p_med_claim_id
            )
        );
        RAISE;
END;
$$ LANGUAGE plpgsql;

-- Function to build payer address
CREATE OR REPLACE FUNCTION mm_build_payer_address(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_address jsonb;
BEGIN
    SELECT mm_build_address(
        a.address1,
        a.address2,
        a.city,
        a.state,
        a.postal_code
    ) INTO v_address
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_receiver sf_r ON sf_r.form_med_claim_fk = mc.id AND sf_r.delete IS NOT TRUE AND sf_r.archive IS NOT TRUE
    INNER JOIN form_med_claim_receiver r ON r.id = sf_r.form_med_claim_receiver_fk AND r.deleted IS NOT TRUE AND r.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_receiver_to_med_claim_address_pyr sf_a ON sf_a.form_med_claim_receiver_fk = r.id AND sf_a.delete IS NOT TRUE AND sf_a.archive IS NOT TRUE
    INNER JOIN form_med_claim_address_pyr a ON a.id = sf_a.form_med_claim_address_pyr_fk AND a.deleted IS NOT TRUE AND a.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    RETURN v_address;
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Optional payer address not found: ' || SQLERRM,
            'Exception in mm_build_payer_address',
            'FUNCTION',
            current_schema(),
            'form_med_claim_address_pyr',
            jsonb_build_object(
                'function_name', 'mm_build_payer_address',
                'med_claim_id', p_med_claim_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build pay to address
CREATE OR REPLACE FUNCTION mm_build_pay_to_address(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_address jsonb;
BEGIN
    SELECT mm_build_address(
        a.address1,
        a.address2,
        a.city,
        a.state,
        a.postal_code
    ) INTO v_address
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_address_pay sf ON sf.form_med_claim_fk = mc.id AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
    INNER JOIN form_med_claim_address_pay a ON a.id = sf.form_med_claim_address_pay_fk AND a.deleted IS NOT TRUE AND a.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    RETURN v_address;
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Optional pay to address not found: ' || SQLERRM,
            'Exception in mm_build_pay_to_address',
            'FUNCTION',
            current_schema(),
            'form_med_claim_address_pay',
            jsonb_build_object(
                'function_name', 'mm_build_pay_to_address',
                'med_claim_id', p_med_claim_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build billing provider segment
CREATE OR REPLACE FUNCTION mm_build_billing_provider(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_provider jsonb;
    v_address jsonb;
    v_contact jsonb;
BEGIN
    -- Get address
    SELECT mm_build_address(
        ab.address1,
        ab.address2,
        ab.city,
        ab.state,
        ab.postal_code
    ) INTO v_address
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_provs sf_provs ON sf_provs.form_med_claim_fk = mc.id AND sf_provs.delete IS NOT TRUE AND sf_provs.archive IS NOT TRUE
    INNER JOIN form_med_claim_provs provs ON provs.id = sf_provs.form_med_claim_provs_fk AND provs.deleted IS NOT TRUE AND provs.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_provs_to_med_claim_prov_bill sf_pb ON sf_pb.form_med_claim_provs_fk = provs.id AND sf_pb.delete IS NOT TRUE AND sf_pb.archive IS NOT TRUE
    INNER JOIN form_med_claim_prov_bill pb ON pb.id = sf_pb.form_med_claim_prov_bill_fk AND pb.deleted IS NOT TRUE AND pb.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_prov_bill_to_med_claim_address_bprov sf_ab ON sf_ab.form_med_claim_prov_bill_fk = pb.id AND sf_ab.delete IS NOT TRUE AND sf_ab.archive IS NOT TRUE
    INNER JOIN form_med_claim_address_bprov ab ON ab.id = sf_ab.form_med_claim_address_bprov_fk AND ab.deleted IS NOT TRUE AND ab.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Get contact info
    SELECT mm_build_contact_info(
        cb.name,
        cb.phone_number,
        NULL::text, -- phone_extension column doesn't exist
        cb.fax_number,
        cb.email
    ) INTO v_contact
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_provs sf_provs ON sf_provs.form_med_claim_fk = mc.id AND sf_provs.delete IS NOT TRUE AND sf_provs.archive IS NOT TRUE
    INNER JOIN form_med_claim_provs provs ON provs.id = sf_provs.form_med_claim_provs_fk AND provs.deleted IS NOT TRUE AND provs.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_provs_to_med_claim_prov_bill sf_pb ON sf_pb.form_med_claim_provs_fk = provs.id AND sf_pb.delete IS NOT TRUE AND sf_pb.archive IS NOT TRUE
    INNER JOIN form_med_claim_prov_bill pb ON pb.id = sf_pb.form_med_claim_prov_bill_fk AND pb.deleted IS NOT TRUE AND pb.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_prov_bill_to_med_claim_contact_bprov sf_cb ON sf_cb.form_med_claim_prov_bill_fk = pb.id AND sf_cb.delete IS NOT TRUE AND sf_cb.archive IS NOT TRUE
    INNER JOIN form_med_claim_contact_bprov cb ON cb.id = sf_cb.form_med_claim_contact_bprov_fk AND cb.deleted IS NOT TRUE AND cb.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Build main provider object
    SELECT jsonb_build_object(
        'providerType', 'BillingProvider',
        'npi', pb.npi,
        'stateLicenseNumber', pb.state_license_number,
        'organizationName', pb.organization_name
    ) INTO v_provider
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_provs sf_provs ON sf_provs.form_med_claim_fk = mc.id AND sf_provs.delete IS NOT TRUE AND sf_provs.archive IS NOT TRUE
    INNER JOIN form_med_claim_provs provs ON provs.id = sf_provs.form_med_claim_provs_fk AND provs.deleted IS NOT TRUE AND provs.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_provs_to_med_claim_prov_bill sf_pb ON sf_pb.form_med_claim_provs_fk = provs.id AND sf_pb.delete IS NOT TRUE AND sf_pb.archive IS NOT TRUE
    INNER JOIN form_med_claim_prov_bill pb ON pb.id = sf_pb.form_med_claim_prov_bill_fk AND pb.deleted IS NOT TRUE AND pb.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Merge address and contact info
    IF v_address IS NOT NULL THEN
        v_provider := v_provider || jsonb_build_object('address', v_address);
    END IF;
    
    IF v_contact IS NOT NULL THEN
        v_provider := v_provider || jsonb_build_object('contactInformation', v_contact);
    END IF;
    
    IF v_provider IS NULL THEN
        RAISE EXCEPTION 'Billing provider information not found for med_claim_id: %', p_med_claim_id;
    END IF;
    
    RETURN mm_remove_nulls(v_provider);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_billing_provider',
            'FUNCTION',
            current_schema(),
            'form_med_claim_prov_bill',
            jsonb_build_object(
                'function_name', 'mm_build_billing_provider',
                'med_claim_id', p_med_claim_id
            )
        );
        RAISE;
END;
$$ LANGUAGE plpgsql;

-- Function to build subscriber segment
CREATE OR REPLACE FUNCTION mm_build_subscriber(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_subscriber jsonb;
    v_address jsonb;
    v_contact jsonb;
BEGIN
    -- Get address
    SELECT mm_build_address(
        a.address1,
        a.address2,
        a.city,
        a.state,
        a.postal_code
    ) INTO v_address
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_subscriber sf ON sf.form_med_claim_fk = mc.id AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
    INNER JOIN form_med_claim_subscriber s ON s.id = sf.form_med_claim_subscriber_fk AND s.deleted IS NOT TRUE AND s.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_subscriber_to_med_claim_address_sub sfa ON sfa.form_med_claim_subscriber_fk = s.id AND sfa.delete IS NOT TRUE AND sfa.archive IS NOT TRUE
    INNER JOIN form_med_claim_address_sub a ON a.id = sfa.form_med_claim_address_sub_fk AND a.deleted IS NOT TRUE AND a.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Get contact info
    SELECT mm_build_contact_info(
        c.name,
        c.phone_number,
        NULL::text, -- no phone extension in this form
        NULL::text, -- no fax number in this form
        c.email
    ) INTO v_contact
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_subscriber sf ON sf.form_med_claim_fk = mc.id AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
    INNER JOIN form_med_claim_subscriber s ON s.id = sf.form_med_claim_subscriber_fk AND s.deleted IS NOT TRUE AND s.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_subscriber_to_med_claim_sub_cont sfc ON sfc.form_med_claim_subscriber_fk = s.id AND sfc.delete IS NOT TRUE AND sfc.archive IS NOT TRUE
    INNER JOIN form_med_claim_sub_cont c ON c.id = sfc.form_med_claim_sub_cont_fk AND c.deleted IS NOT TRUE AND c.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    LIMIT 1;
    
    -- Build main subscriber object
    SELECT jsonb_build_object(
        'memberId', s.member_id,
        'paymentResponsibilityLevelCode', s.payment_responsibility_level_code,
        'firstName', s.first_name,
        'lastName', s.last_name,
        'middleName', s.middle_name,
        'dateOfBirth', mm_format_date(s.date_of_birth::date),
        'gender', s.gender,
        'insuranceTypeCode', s.insurance_type_code,
        'policyNumber', s.policy_number,
        'groupNumber', s.group_number
    ) INTO v_subscriber
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_subscriber sf ON sf.form_med_claim_fk = mc.id AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
    INNER JOIN form_med_claim_subscriber s ON s.id = sf.form_med_claim_subscriber_fk AND s.deleted IS NOT TRUE AND s.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Merge address and contact info
    IF v_address IS NOT NULL THEN
        v_subscriber := v_subscriber || jsonb_build_object('address', v_address);
    END IF;
    
    IF v_contact IS NOT NULL THEN
        v_subscriber := v_subscriber || jsonb_build_object('contactInformation', v_contact);
    END IF;
    
    IF v_subscriber IS NULL THEN
        RAISE EXCEPTION 'Subscriber information not found for med_claim_id: %', p_med_claim_id;
    END IF;
    
    RETURN mm_remove_nulls(v_subscriber);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_subscriber',
            'FUNCTION',
            current_schema(),
            'form_med_claim_subscriber',
            jsonb_build_object(
                'function_name', 'mm_build_subscriber',
                'med_claim_id', p_med_claim_id
            )
        );
        RAISE;
END;
$$ LANGUAGE plpgsql;

-- Function to build rendering provider segment
CREATE OR REPLACE FUNCTION mm_build_rendering_provider(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_provider jsonb;
    v_address jsonb;
    v_contact jsonb;
BEGIN
    -- Get address
    SELECT mm_build_address(
        ar.address1,
        ar.address2,
        ar.city,
        ar.state,
        ar.postal_code
    ) INTO v_address
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_provs sf_provs ON sf_provs.form_med_claim_fk = mc.id AND sf_provs.delete IS NOT TRUE AND sf_provs.archive IS NOT TRUE
    INNER JOIN form_med_claim_provs provs ON provs.id = sf_provs.form_med_claim_provs_fk AND provs.deleted IS NOT TRUE AND provs.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_provs_to_med_claim_prov_rend sf_pr ON sf_pr.form_med_claim_provs_fk = provs.id AND sf_pr.delete IS NOT TRUE AND sf_pr.archive IS NOT TRUE
    INNER JOIN form_med_claim_prov_rend pr ON pr.id = sf_pr.form_med_claim_prov_rend_fk AND pr.deleted IS NOT TRUE AND pr.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_prov_rend_to_med_claim_address_rend sf_ar ON sf_ar.form_med_claim_prov_rend_fk = pr.id AND sf_ar.delete IS NOT TRUE AND sf_ar.archive IS NOT TRUE
    INNER JOIN form_med_claim_address_rend ar ON ar.id = sf_ar.form_med_claim_address_rend_fk AND ar.deleted IS NOT TRUE AND ar.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Get contact info
    SELECT mm_build_contact_info(
        cr.name,
        cr.phone_number,
        NULL::text, -- phone_extension column doesn't exist
        cr.fax_number,
        cr.email
    ) INTO v_contact
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_provs sf_provs ON sf_provs.form_med_claim_fk = mc.id AND sf_provs.delete IS NOT TRUE AND sf_provs.archive IS NOT TRUE
    INNER JOIN form_med_claim_provs provs ON provs.id = sf_provs.form_med_claim_provs_fk AND provs.deleted IS NOT TRUE AND provs.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_provs_to_med_claim_prov_rend sf_pr ON sf_pr.form_med_claim_provs_fk = provs.id AND sf_pr.delete IS NOT TRUE AND sf_pr.archive IS NOT TRUE
    INNER JOIN form_med_claim_prov_rend pr ON pr.id = sf_pr.form_med_claim_prov_rend_fk AND pr.deleted IS NOT TRUE AND pr.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_prov_rend_to_med_claim_contact_rend sf_cr ON sf_cr.form_med_claim_prov_rend_fk = pr.id  AND sf_cr.delete IS NOT TRUE AND sf_cr.archive IS NOT TRUE
    INNER JOIN form_med_claim_contact_rend cr ON cr.id = sf_cr.form_med_claim_contact_rend_fk AND cr.deleted IS NOT TRUE AND cr.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    LIMIT 1;
    
    -- Build main provider object
    SELECT jsonb_build_object(
        'providerType', 'RenderingProvider',
        'npi', pr.npi,
        'commercialNumber', pr.commercial_number,
        'stateLicenseNumber', pr.state_license_number,
        'organizationName', pr.organization_name
    ) INTO v_provider
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_provs sf_provs ON sf_provs.form_med_claim_fk = mc.id AND sf_provs.delete IS NOT TRUE AND sf_provs.archive IS NOT TRUE
    INNER JOIN form_med_claim_provs provs ON provs.id = sf_provs.form_med_claim_provs_fk AND provs.deleted IS NOT TRUE AND provs.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_provs_to_med_claim_prov_rend sf_pr ON sf_pr.form_med_claim_provs_fk = provs.id AND sf_pr.delete IS NOT TRUE AND sf_pr.archive IS NOT TRUE
    INNER JOIN form_med_claim_prov_rend pr ON pr.id = sf_pr.form_med_claim_prov_rend_fk AND pr.deleted IS NOT TRUE AND pr.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Merge address and contact info
    IF v_address IS NOT NULL THEN
        v_provider := v_provider || jsonb_build_object('address', v_address);
    END IF;
    
    IF v_contact IS NOT NULL THEN
        v_provider := v_provider || jsonb_build_object('contactInformation', v_contact);
    END IF;
    
    RETURN mm_remove_nulls(v_provider);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Optional rendering provider not found: ' || SQLERRM,
            'Exception in mm_build_rendering_provider',
            'FUNCTION',
            current_schema(),
            'form_med_claim_prov_rend',
            jsonb_build_object(
                'function_name', 'mm_build_rendering_provider',
                'med_claim_id', p_med_claim_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build referring provider segment
CREATE OR REPLACE FUNCTION mm_build_referring_provider(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_provider jsonb;
    v_address jsonb;
    v_contact jsonb;
BEGIN
    -- Get address
    SELECT mm_build_address(
        ar.address1,
        ar.address2,
        ar.city,
        ar.state,
        ar.postal_code
    ) INTO v_address
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_provs sf_provs ON sf_provs.form_med_claim_fk = mc.id AND sf_provs.delete IS NOT TRUE AND sf_provs.archive IS NOT TRUE
    INNER JOIN form_med_claim_provs provs ON provs.id = sf_provs.form_med_claim_provs_fk AND provs.deleted IS NOT TRUE AND provs.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_provs_to_med_claim_prov_ref sf_pr ON sf_pr.form_med_claim_provs_fk = provs.id AND sf_pr.delete IS NOT TRUE AND sf_pr.archive IS NOT TRUE
    INNER JOIN form_med_claim_prov_ref pr ON pr.id = sf_pr.form_med_claim_prov_ref_fk AND pr.deleted IS NOT TRUE AND pr.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_prov_ref_to_med_claim_address_rprov sf_ar ON sf_ar.form_med_claim_prov_ref_fk = pr.id AND sf_ar.delete IS NOT TRUE AND sf_ar.archive IS NOT TRUE
    INNER JOIN form_med_claim_address_rprov ar ON ar.id = sf_ar.form_med_claim_address_rprov_fk AND ar.deleted IS NOT TRUE AND ar.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Get contact info
    SELECT mm_build_contact_info(
        cr.name,
        cr.phone_number,
        NULL::text, -- phone_extension column doesn't exist
        cr.fax_number,
        cr.email
    ) INTO v_contact
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_provs sf_provs ON sf_provs.form_med_claim_fk = mc.id AND sf_provs.delete IS NOT TRUE AND sf_provs.archive IS NOT TRUE
    INNER JOIN form_med_claim_provs provs ON provs.id = sf_provs.form_med_claim_provs_fk AND provs.deleted IS NOT TRUE AND provs.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_provs_to_med_claim_prov_ref sf_pr ON sf_pr.form_med_claim_provs_fk = provs.id AND sf_pr.delete IS NOT TRUE AND sf_pr.archive IS NOT TRUE
    INNER JOIN form_med_claim_prov_ref pr ON pr.id = sf_pr.form_med_claim_prov_ref_fk AND pr.deleted IS NOT TRUE AND pr.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_prov_ref_to_med_claim_contact_rprov sf_cr ON sf_cr.form_med_claim_prov_ref_fk = pr.id AND sf_cr.delete IS NOT TRUE AND sf_cr.archive IS NOT TRUE
    INNER JOIN form_med_claim_contact_rprov cr ON cr.id = sf_cr.form_med_claim_contact_rprov_fk AND cr.deleted IS NOT TRUE AND cr.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Build main provider object
    SELECT jsonb_build_object(
        'providerType', 'ReferringProvider',
        'npi', pr.npi,
        'commercialNumber', pr.commercial_number,
        'stateLicenseNumber', pr.state_license_number,
        'taxonomyCode', pr.taxonomy_code,
        'firstName', pr.first_name,
        'lastName', pr.last_name
    ) INTO v_provider
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_provs sf_provs ON sf_provs.form_med_claim_fk = mc.id AND sf_provs.delete IS NOT TRUE AND sf_provs.archive IS NOT TRUE
    INNER JOIN form_med_claim_provs provs ON provs.id = sf_provs.form_med_claim_provs_fk AND provs.deleted IS NOT TRUE AND provs.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_provs_to_med_claim_prov_ref sf_pr ON sf_pr.form_med_claim_provs_fk = provs.id AND sf_pr.delete IS NOT TRUE AND sf_pr.archive IS NOT TRUE
    INNER JOIN form_med_claim_prov_ref pr ON pr.id = sf_pr.form_med_claim_prov_ref_fk AND pr.deleted IS NOT TRUE AND pr.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Merge address and contact info
    IF v_address IS NOT NULL THEN
        v_provider := v_provider || jsonb_build_object('address', v_address);
    END IF;
    
    IF v_contact IS NOT NULL THEN
        v_provider := v_provider || jsonb_build_object('contactInformation', v_contact);
    END IF;
    
    RETURN mm_remove_nulls(v_provider);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Optional referring provider not found: ' || SQLERRM,
            'Exception in mm_build_referring_provider',
            'FUNCTION',
            current_schema(),
            'form_med_claim_prov_ref',
            jsonb_build_object(
                'function_name', 'mm_build_referring_provider',
                'med_claim_id', p_med_claim_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build supervising provider segment
CREATE OR REPLACE FUNCTION mm_build_supervising_provider(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_provider jsonb;
    v_address jsonb;
    v_contact jsonb;
BEGIN
    -- Get address
    SELECT mm_build_address(
        asp.address1,
        asp.address2,
        asp.city,
        asp.state,
        asp.postal_code
    ) INTO v_address
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_provs sf_provs ON sf_provs.form_med_claim_fk = mc.id AND sf_provs.delete IS NOT TRUE AND sf_provs.archive IS NOT TRUE
    INNER JOIN form_med_claim_provs provs ON provs.id = sf_provs.form_med_claim_provs_fk AND provs.deleted IS NOT TRUE AND provs.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_provs_to_med_claim_prov_sup sf_ps ON sf_ps.form_med_claim_provs_fk = provs.id AND sf_ps.delete IS NOT TRUE AND sf_ps.archive IS NOT TRUE
    INNER JOIN form_med_claim_prov_sup ps ON ps.id = sf_ps.form_med_claim_prov_sup_fk AND ps.deleted IS NOT TRUE AND ps.archived IS NOT TRUE
    LEFT JOIN sf_form_med_claim_prov_sup_to_med_claim_address_sprov sf_asp ON sf_asp.form_med_claim_prov_sup_fk = ps.id AND sf_asp.delete IS NOT TRUE AND sf_asp.archive IS NOT TRUE
    LEFT JOIN form_med_claim_address_sprov asp ON asp.id = sf_asp.form_med_claim_address_sprov_fk AND asp.deleted IS NOT TRUE AND asp.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    LIMIT 1;
    
    -- Get contact info
    SELECT mm_build_contact_info(
        cs.name,
        cs.phone_number,
        NULL::text, -- phone_extension column doesn't exist
        cs.fax_number,
        cs.email
    ) INTO v_contact
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_provs sf_provs ON sf_provs.form_med_claim_fk = mc.id AND sf_provs.delete IS NOT TRUE AND sf_provs.archive IS NOT TRUE
    INNER JOIN form_med_claim_provs provs ON provs.id = sf_provs.form_med_claim_provs_fk AND provs.deleted IS NOT TRUE AND provs.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_provs_to_med_claim_prov_sup sf_ps ON sf_ps.form_med_claim_provs_fk = provs.id AND sf_ps.delete IS NOT TRUE AND sf_ps.archive IS NOT TRUE
    INNER JOIN form_med_claim_prov_sup ps ON ps.id = sf_ps.form_med_claim_prov_sup_fk AND ps.deleted IS NOT TRUE AND ps.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_prov_sup_to_med_claim_contact_sprov sf_cs ON sf_cs.form_med_claim_prov_sup_fk = ps.id AND sf_cs.delete IS NOT TRUE AND sf_cs.archive IS NOT TRUE
    INNER JOIN form_med_claim_contact_sprov cs ON cs.id = sf_cs.form_med_claim_contact_sprov_fk AND cs.deleted IS NOT TRUE AND cs.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    LIMIT 1;
    
    -- Build main provider object
    SELECT jsonb_build_object(
        'providerType', 'SupervisingProvider',
        'npi', ps.npi,
        'commercialNumber', ps.commercial_number,
        'stateLicenseNumber', ps.state_license_number,
        'taxonomyCode', ps.taxonomy_code,
        'firstName', ps.first_name,
        'lastName', ps.last_name,
        'organizationName', ps.organization_name
    ) INTO v_provider
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_provs sf_provs ON sf_provs.form_med_claim_fk = mc.id AND sf_provs.delete IS NOT TRUE AND sf_provs.archive IS NOT TRUE
    INNER JOIN form_med_claim_provs provs ON provs.id = sf_provs.form_med_claim_provs_fk AND provs.deleted IS NOT TRUE AND provs.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_provs_to_med_claim_prov_sup sf_ps ON sf_ps.form_med_claim_provs_fk = provs.id AND sf_ps.delete IS NOT TRUE AND sf_ps.archive IS NOT TRUE
    INNER JOIN form_med_claim_prov_sup ps ON ps.id = sf_ps.form_med_claim_prov_sup_fk AND ps.deleted IS NOT TRUE AND ps.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    LIMIT 1;
    
    -- Merge address and contact info
    IF v_address IS NOT NULL THEN
        v_provider := v_provider || jsonb_build_object('address', v_address);
    END IF;
    
    IF v_contact IS NOT NULL THEN
        v_provider := v_provider || jsonb_build_object('contactInformation', v_contact);
    END IF;
    
    RETURN mm_remove_nulls(v_provider);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Optional supervising provider not found: ' || SQLERRM,
            'Exception in mm_build_supervising_provider',
            'FUNCTION',
            current_schema(),
            'form_med_claim_prov_sup',
            jsonb_build_object(
                'function_name', 'mm_build_supervising_provider',
                'med_claim_id', p_med_claim_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build ordering provider segment
CREATE OR REPLACE FUNCTION mm_build_ordering_provider(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_provider jsonb;
    v_address jsonb;
    v_contact jsonb;
BEGIN
    -- Get address
    SELECT mm_build_address(
        ao.address1,
        ao.address2,
        ao.city,
        ao.state,
        ao.postal_code
    ) INTO v_address
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_provs sf_provs ON sf_provs.form_med_claim_fk = mc.id AND sf_provs.delete IS NOT TRUE AND sf_provs.archive IS NOT TRUE
    INNER JOIN form_med_claim_provs provs ON provs.id = sf_provs.form_med_claim_provs_fk AND provs.deleted IS NOT TRUE AND provs.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_provs_to_med_claim_prov_ord sf_po ON sf_po.form_med_claim_provs_fk = provs.id AND sf_po.delete IS NOT TRUE AND sf_po.archive IS NOT TRUE
    INNER JOIN form_med_claim_prov_ord po ON po.id = sf_po.form_med_claim_prov_ord_fk AND po.deleted IS NOT TRUE AND po.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_prov_ord_to_med_claim_address_oprov sf_ao ON sf_ao.form_med_claim_prov_ord_fk = po.id AND sf_ao.delete IS NOT TRUE AND sf_ao.archive IS NOT TRUE
    INNER JOIN form_med_claim_address_oprov ao ON ao.id = sf_ao.form_med_claim_address_oprov_fk AND ao.deleted IS NOT TRUE AND ao.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    LIMIT 1;
    
    -- Get contact info
    SELECT mm_build_contact_info(
        co.name,
        co.phone_number,
        NULL::text, -- phone_extension column doesn't exist
        co.fax_number,
        co.email
    ) INTO v_contact
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_provs sf_provs ON sf_provs.form_med_claim_fk = mc.id AND sf_provs.delete IS NOT TRUE AND sf_provs.archive IS NOT TRUE
    INNER JOIN form_med_claim_provs provs ON provs.id = sf_provs.form_med_claim_provs_fk AND provs.deleted IS NOT TRUE AND provs.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_provs_to_med_claim_prov_ord sf_po ON sf_po.form_med_claim_provs_fk = provs.id AND sf_po.delete IS NOT TRUE AND sf_po.archive IS NOT TRUE
    INNER JOIN form_med_claim_prov_ord po ON po.id = sf_po.form_med_claim_prov_ord_fk AND po.deleted IS NOT TRUE AND po.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_prov_ord_to_med_claim_contact_oprov sf_co ON sf_co.form_med_claim_prov_ord_fk = po.id AND sf_co.delete IS NOT TRUE AND sf_co.archive IS NOT TRUE
    INNER JOIN form_med_claim_contact_oprov co ON co.id = sf_co.form_med_claim_contact_oprov_fk AND co.deleted IS NOT TRUE AND co.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    LIMIT 1;
    
    -- Build main provider object
    SELECT jsonb_build_object(
        'providerType', 'OrderingProvider',
        'npi', po.npi,
        'commercialNumber', po.commercial_number,
        'stateLicenseNumber', po.state_license_number,
        'taxonomyCode', po.taxonomy_code,
        'firstName', po.first_name,
        'lastName', po.last_name
    ) INTO v_provider
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_provs sf_provs ON sf_provs.form_med_claim_fk = mc.id AND sf_provs.delete IS NOT TRUE AND sf_provs.archive IS NOT TRUE
    INNER JOIN form_med_claim_provs provs ON provs.id = sf_provs.form_med_claim_provs_fk AND provs.deleted IS NOT TRUE AND provs.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_provs_to_med_claim_prov_ord sf_po ON sf_po.form_med_claim_provs_fk = provs.id AND sf_po.delete IS NOT TRUE AND sf_po.archive IS NOT TRUE
    INNER JOIN form_med_claim_prov_ord po ON po.id = sf_po.form_med_claim_prov_ord_fk AND po.deleted IS NOT TRUE AND po.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    LIMIT 1;
    
    -- Merge address and contact info
    IF v_address IS NOT NULL THEN
        v_provider := v_provider || jsonb_build_object('address', v_address);
    END IF;
    
    IF v_contact IS NOT NULL THEN
        v_provider := v_provider || jsonb_build_object('contactInformation', v_contact);
    END IF;
    
    RETURN mm_remove_nulls(v_provider);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Optional ordering provider not found: ' || SQLERRM,
            'Exception in mm_build_ordering_provider',
            'FUNCTION',
            current_schema(),
            'form_med_claim_prov_ord',
            jsonb_build_object(
                'function_name', 'mm_build_ordering_provider',
                'med_claim_id', p_med_claim_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build claim level adjustments
CREATE OR REPLACE FUNCTION mm_build_claim_adjustments(p_osub_id integer)
RETURNS jsonb AS $$
DECLARE
    v_adjustments jsonb = '[]'::jsonb;
    v_adj jsonb;
    r_adj record;
BEGIN
    FOR r_adj IN 
        SELECT adj.id, adj.adjustment_group_code
        FROM form_med_claim_osub os
        LEFT JOIN sf_form_med_claim_osub_to_med_claim_adj sf ON sf.form_med_claim_osub_fk = os.id
        LEFT JOIN form_med_claim_adj adj ON adj.id = sf.form_med_claim_adj_fk
        WHERE os.id = p_osub_id
        AND sf.delete IS NOT TRUE
        AND sf.archive IS NOT TRUE
        AND adj.deleted IS NOT TRUE
        AND adj.archived IS NOT TRUE
        ORDER BY adj.id
    LOOP
        SELECT jsonb_build_object(
            'adjustmentGroupCode', r_adj.adjustment_group_code,
            'adjustmentDetails', COALESCE(
                (SELECT jsonb_agg(
                    jsonb_build_object(
                        'adjustmentReasonCode', adjd.adjustment_reason_code,
                        'adjustmentAmount', adjd.adjustment_amount::text,
                        'adjustmentQuantity', adjd.adjustment_quantity::text
                    )
                ) 
                FROM sf_form_med_claim_adj_to_med_claim_adj_dl sf_adjd
                LEFT JOIN form_med_claim_adj_dl adjd ON adjd.id = sf_adjd.form_med_claim_adj_dl_fk
                WHERE sf_adjd.form_med_claim_adj_fk = r_adj.id
                AND sf_adjd.delete IS NOT TRUE
                AND sf_adjd.archive IS NOT TRUE
                AND adjd.deleted IS NOT TRUE
                AND adjd.archived IS NOT TRUE),
                '[]'::jsonb
            )
        ) INTO v_adj;

        v_adjustments := v_adjustments || v_adj;
    END LOOP;

    IF jsonb_array_length(v_adjustments) > 0 THEN
        RETURN v_adjustments;
    ELSE
        RETURN NULL;
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_claim_adjustments',
            'FUNCTION',
            current_schema(),
            'form_med_claim_adj',
            jsonb_build_object(
                'function_name', 'mm_build_claim_adjustments',
                'osub_id', p_osub_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;
