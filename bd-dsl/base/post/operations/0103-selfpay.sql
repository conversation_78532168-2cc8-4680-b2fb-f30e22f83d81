DO $$
DECLARE
    table_exists BO<PERSON>EAN;
    fid INTEGER;
    type_id TEXT;
    is_archived BOOLEAN;
    is_deleted BOOLEAN;
BEGIN
    -- Check if the table exists
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_name = 'form_payer'
    ) INTO table_exists;

    IF NOT table_exists THEN
        RAISE EXCEPTION 'Table form_payer does not exist';
    END IF;

    -- Check for data with id = 1
    SELECT fp.type_id, fp.id, fp.archived, fp.deleted
    INTO type_id, fid, is_archived, is_deleted
    FROM form_payer fp
    WHERE id = 1;

    IF type_id IS NOT NULL AND type_id <> 'SELF' THEN
        RAISE EXCEPTION 'Row with id = 1 exists but type_id is not SELF';
    ELSIF type_id IS NULL AND fid IS NULL THEN
        INSERT INTO form_payer (id, type_id, active, billing_method_id, organization, short_code, is_self_pay) VALUES (1, 'SELF', 'Yes', 'generic', 'Self Pay', 'SELF', 'Yes');
    ELSIF fid IS NOT NULL AND type_id IS NULL THEN
        RAISE EXCEPTION 'Row with id = 1 exists but type_id is NULL';
    ELSIF fid IS NOT NULL AND type_id = 'SELF' AND (is_archived OR is_deleted) THEN
        UPDATE form_payer
        SET archived = false, deleted = false
        WHERE id = 1;
    END IF;
END $$;

DO $$ BEGIN
  PERFORM drop_all_function_signatures('insert_default_self_pay_insurance');
END $$;
CREATE OR REPLACE FUNCTION insert_default_self_pay_insurance()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if patient already has a self-pay insurance record
    IF NOT EXISTS (
        SELECT 1 
        FROM form_patient_insurance 
        WHERE patient_id = NEW.id 
        AND COALESCE(is_self_pay, 'No') = 'Yes'
    ) THEN
        -- Insert default self-pay record
        INSERT INTO form_patient_insurance (
            patient_id,
            rank,
            type_id,
            is_self_pay,
            payer_id,
            -- Fields from form_patient
            mrn,
            ssn,
            external_id,
            -- Default fields
            created_by,
            created_on,
            deleted,
            archived,
            active,
            billing_method_id,
            effective_date
        ) VALUES (
            NEW.id,
            0,
            'SELF',
            'Yes',
            '1',
            -- Copy values from the newly inserted patient record
            NEW.mrn,
            NEW.ssn,
            NEW.external_id,
            -- Default values for other fields
            1,
            CURRENT_TIMESTAMP,
            FALSE,
            FALSE,
            'Yes',
            'generic',
            CURRENT_TIMESTAMP::date
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql VOLATILE;


-- Create trigger that fires after inserting a new patient
CREATE OR REPLACE TRIGGER after_patient_insert
AFTER INSERT ON form_patient
FOR EACH ROW
EXECUTE FUNCTION insert_default_self_pay_insurance();

-- Function to backfill self-pay insurance records for existing patients
DO $$ BEGIN
  PERFORM drop_all_function_signatures('backfill_patient_self_pay_insurance');
END $$;
CREATE OR REPLACE FUNCTION backfill_patient_self_pay_insurance()
RETURNS INTEGER AS $$
DECLARE
    patient_rec RECORD;
    record_count INTEGER := 0;
BEGIN
    -- Find all patients who don't have a self-pay insurance record
    FOR patient_rec IN 
        SELECT p.id, p.mrn, p.ssn, p.external_id
        FROM form_patient p
        WHERE NOT EXISTS (
            SELECT 1 
            FROM form_patient_insurance i 
            WHERE i.patient_id = p.id 
            AND COALESCE(i.is_self_pay, 'No') = 'Yes'
        )
    LOOP
        -- Insert self-pay record for each patient
        INSERT INTO form_patient_insurance (
            patient_id,
            rank,
            type_id,
            is_self_pay,
            payer_id,
            -- Fields from form_patient
            mrn,
            ssn,
            external_id,  
            -- Default fields
            created_by,
            created_on,
            deleted,
            archived,
            active,
            billing_method_id,
            effective_date
        ) VALUES (
            patient_rec.id,
            0,
            'SELF',
            'Yes',
            '1',
            -- Copy values from the patient record
            patient_rec.mrn,
            patient_rec.ssn,
            patient_rec.external_id,
            -- Default values for other fields
            1,
            CURRENT_TIMESTAMP,
            FALSE,
            FALSE,
            'Yes',
            'generic',
            CURRENT_TIMESTAMP::date
        );
        
        record_count := record_count + 1;
    END LOOP;
    
    RETURN record_count;
END;
$$ LANGUAGE plpgsql;

-- Execute the function and log the result
DO $$
DECLARE
    records_created INTEGER;
BEGIN
    records_created := backfill_patient_self_pay_insurance();
    RAISE NOTICE 'Self-pay insurance backfill complete. Created % records.', records_created;
END;
$$;