
DO $$ BEGIN
  PERFORM drop_all_function_signatures('check_and_create_pricing_update');
END $$;
CREATE OR REPLACE FUNCTION check_and_create_pricing_update()
RETURNS void AS $$
DECLARE
    current_date date := CURRENT_DATE;
    last_update_date date;
    update_count integer;
    pricing_update_uuid uuid;
    existing_pending_update uuid;
BEGIN
    -- Check if there are any pending pricing updates from today
    SELECT pricing_update_no INTO existing_pending_update
    FROM form_inventory_pricing_update
    WHERE status = 'Pending' 
    AND DATE(pricing_update_date) = current_date
    LIMIT 1;
    
    -- Get the last completed update date
    SELECT COALESCE(
        (SELECT MAX(pricing_update_date)
         FROM form_inventory_pricing_update 
         WHERE status = 'Completed'),
        (SELECT DATE(created_on) FROM form_company WHERE id = 1)
    ) INTO last_update_date;
    
    -- Check if there are actual pricing changes since last update
    SELECT COUNT(*) INTO update_count 
    FROM (
        -- Check for NDC price changes
        SELECT 1 FROM form_list_fdb_ndc_price 
        WHERE price_effective_dt >= last_update_date
        UNION
        -- Check for Medicare price changes
        SELECT 1 FROM form_list_fdb_medicare_price 
        WHERE hcpc_pbc1 >= last_update_date
        UNION
        -- Check for NDC updates
        SELECT 1 FROM form_list_fdb_ndc 
        WHERE dupdc >= last_update_date OR daddnc >= last_update_date OR obsdtec >= last_update_date
        LIMIT 1
    ) changes;
    
    IF update_count > 0 THEN
        -- If we have a pending update, use it - otherwise create new one
        IF existing_pending_update IS NOT NULL THEN
            pricing_update_uuid := existing_pending_update;
        ELSE
            -- Mark any other 'Pending' updates as 'Expired'
            UPDATE form_inventory_pricing_update
            SET status = 'Expired', action = NULL
            WHERE status = 'Pending';
            
            -- Create new update record
            pricing_update_uuid := gen_random_uuid();
            
            INSERT INTO form_inventory_pricing_update (
                pricing_update_no,
                pricing_update_date,
                last_pricing_update_date,
                status
            ) VALUES (
                pricing_update_uuid,
                current_date,
                last_update_date,
                'Pending'
            );
        END IF;
        
        -- Now process updates for the current record (existing or new)
        
        -- Create inventory pricing update items (only for items not already in the update)
        INSERT INTO form_inventory_pricing_upt_item (
            created_by,
            created_on,
            pricing_update_no,
            inventory_id,
            prev_list_price,
            updated_list_price,
            prev_awp_price,
            updated_awp_price,
            prev_awp_price_pkg,
            updated_awp_price_pkg,
            prev_wac_price,
            updated_wac_price,
            prev_wac_price_pkg,
            updated_wac_price_pkg,
            prev_asp_price,
            updated_asp_price,
            prev_avg_acq_cost_brand,
            updated_avg_acq_cost_brand,
            prev_avg_acq_cost_gen,
            updated_avg_acq_cost_gen,
            prev_ful,
            updated_ful,
            deleted,
            archived
        )
        SELECT
            1,
            CURRENT_TIMESTAMP,
            pricing_update_uuid,
            inv.id,
            inv.list_price::numeric,
            -- Calculate updated list price based on site price code formula
            CASE
                WHEN spc.price_formula_id = 'A' AND spc.multiplier IS NOT NULL THEN ROUND(fdb_data.awp_price::numeric * spc.multiplier::numeric, 4)
                WHEN spc.price_formula_id = 'W' AND spc.multiplier IS NOT NULL THEN ROUND(fdb_data.wac_price::numeric * spc.multiplier::numeric, 4)
                WHEN spc.price_formula_id = 'C' AND spc.multiplier IS NOT NULL THEN ROUND(inv.last_cost_ea::numeric * spc.multiplier::numeric, 4)
                WHEN spc.price_formula_id = 'L' AND spc.multiplier IS NOT NULL THEN ROUND(inv.list_price::numeric * spc.multiplier::numeric, 4)
                WHEN spc.price_formula_id = '1' AND spc.multiplier IS NOT NULL THEN ROUND(inv.add_price1::numeric * spc.multiplier::numeric, 4)
                WHEN spc.price_formula_id = '2' AND spc.multiplier IS NOT NULL THEN ROUND(inv.add_price2::numeric * spc.multiplier::numeric, 4)
                WHEN spc.price_formula_id = 'ASP' AND spc.multiplier IS NOT NULL THEN ROUND(fdb_data.asp_price::numeric * spc.multiplier::numeric, 4)
                ELSE inv.list_price::numeric
            END,
            inv.awp_price,
            fdb_data.awp_price,
            inv.awp_price_pkg,
            fdb_data.awp_price_pkg,
            inv.wac_price,
            fdb_data.wac_price,
            inv.wac_price_pkg,
            fdb_data.wac_price_pkg,
            inv.asp_price,
            fdb_data.asp_price,
            inv.avg_acq_cost_brand,
            fdb_data.avg_acq_cost_brand,
            inv.avg_acq_cost_gen,
            fdb_data.avg_acq_cost_gen,
            inv.ful,
            fdb_data.ful,
            FALSE,
            FALSE
        FROM form_inventory inv
        JOIN form_site_price_code_item spc ON inv.price_code_id = spc.price_code_id
        JOIN LATERAL (
            SELECT * FROM get_fdb_ndc_data(inv.ndc)
        ) fdb_data ON true
        WHERE inv.active = 'Yes'
        AND inv.type IN ('Drug', 'Compound')
        AND (
            COALESCE(inv.awp_price::numeric, 0) != COALESCE(fdb_data.awp_price::numeric, 0) OR
            COALESCE(inv.awp_price_pkg::numeric, 0) != COALESCE(fdb_data.awp_price_pkg::numeric, 0) OR
            COALESCE(inv.wac_price::numeric, 0) != COALESCE(fdb_data.wac_price::numeric, 0) OR
            COALESCE(inv.wac_price_pkg::numeric, 0) != COALESCE(fdb_data.wac_price_pkg::numeric, 0) OR
            COALESCE(inv.asp_price::numeric, 0) != COALESCE(fdb_data.asp_price::numeric, 0) OR
            COALESCE(inv.avg_acq_cost_brand, 0) != COALESCE(fdb_data.avg_acq_cost_brand, 0) OR
            COALESCE(inv.avg_acq_cost_gen::numeric, 0) != COALESCE(fdb_data.avg_acq_cost_gen::numeric, 0) OR
            COALESCE(inv.ful::numeric, 0) != COALESCE(fdb_data.ful::numeric, 0)
        )
        -- Only add items not already in the pending update
        AND NOT EXISTS (
            SELECT 1 FROM form_inventory_pricing_upt_item existing
            WHERE existing.pricing_update_no = pricing_update_uuid
            AND existing.inventory_id = inv.id
        );
        
        -- Create contract pricing update items (only for items not already in the update)
        INSERT INTO form_inventory_pricing_cont_upt_item (
            created_by,
            created_on,
            pricing_update_no,
            contract_id,
            inventory_id,
            price_matrix_item_id,
            prev_expected_price,
            updated_expected_price,
            prev_special_price,
            updated_special_price,
            deleted,
            archived
        )
        SELECT 
            1,
            CURRENT_TIMESTAMP,
            pricing_update_uuid,
            pc.id,
            ppmi.inventory_id,
            ppmi.id,
            ppmi.expected_price,
            CASE
                -- If there's an override formula, use it
                WHEN ppmi.override_expected_formula_id IS NOT NULL THEN
                    CASE 
                        WHEN ppmi.override_expected_formula_id = 'A' AND ppmi.override_expected_price_multiplier IS NOT NULL THEN ROUND(fdb_data.awp_price::numeric * ppmi.override_expected_price_multiplier::numeric, 4)
                        WHEN ppmi.override_expected_formula_id = 'W' AND ppmi.override_expected_price_multiplier IS NOT NULL THEN ROUND(fdb_data.wac_price::numeric * ppmi.override_expected_price_multiplier::numeric, 4)
                        WHEN ppmi.override_expected_formula_id = 'C' AND ppmi.override_expected_price_multiplier IS NOT NULL THEN ROUND(inv.last_cost_ea::numeric * ppmi.override_expected_price_multiplier::numeric, 4)
                        WHEN ppmi.override_expected_formula_id = 'ASP' AND ppmi.override_expected_price_multiplier IS NOT NULL THEN ROUND(fdb_data.asp_price::numeric * ppmi.override_expected_price_multiplier::numeric, 4)
                        WHEN ppmi.override_expected_formula_id = 'L' AND ppmi.override_expected_price_multiplier IS NOT NULL THEN ROUND(
                            -- Calculate list price based on site price code
                            CASE
                                WHEN spc.price_formula_id = 'A' AND spc.multiplier IS NOT NULL THEN ROUND(fdb_data.awp_price::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = 'W' AND spc.multiplier IS NOT NULL THEN ROUND(fdb_data.wac_price::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = 'C' AND spc.multiplier IS NOT NULL THEN ROUND(inv.last_cost_ea::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = '1' AND spc.multiplier IS NOT NULL THEN ROUND(inv.add_price1::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = '2' AND spc.multiplier IS NOT NULL THEN ROUND(inv.add_price2::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = 'ASP' AND spc.multiplier IS NOT NULL THEN ROUND(fdb_data.asp_price::numeric * spc.multiplier::numeric, 4)
                                ELSE inv.list_price::numeric
                            END * ppmi.override_expected_price_multiplier::numeric, 4)
                        WHEN ppmi.override_expected_formula_id = '1' AND ppmi.override_expected_price_multiplier IS NOT NULL THEN ROUND(inv.add_price1::numeric * ppmi.override_expected_price_multiplier::numeric, 4)
                        WHEN ppmi.override_expected_formula_id = '2' AND ppmi.override_expected_price_multiplier IS NOT NULL THEN ROUND(inv.add_price2::numeric * ppmi.override_expected_price_multiplier::numeric, 4)
                        ELSE ppmi.expected_price::numeric
                    END
                -- Otherwise, use the contract formula
                ELSE
                    CASE
                        WHEN pcf.expected_price_basis = 'A' AND pcf.expected_price_multiplier IS NOT NULL THEN ROUND(fdb_data.awp_price::numeric * pcf.expected_price_multiplier::numeric, 4)
                        WHEN pcf.expected_price_basis = 'W' AND pcf.expected_price_multiplier IS NOT NULL THEN ROUND(fdb_data.wac_price::numeric * pcf.expected_price_multiplier::numeric, 4)
                        WHEN pcf.expected_price_basis = 'C' AND pcf.expected_price_multiplier IS NOT NULL THEN ROUND(inv.last_cost_ea::numeric * pcf.expected_price_multiplier::numeric, 4)
                        WHEN pcf.expected_price_basis = 'ASP' AND pcf.expected_price_multiplier IS NOT NULL THEN ROUND(fdb_data.asp_price::numeric * pcf.expected_price_multiplier::numeric, 4)
                        WHEN pcf.expected_price_basis = 'L' AND pcf.expected_price_multiplier IS NOT NULL THEN ROUND(
                            -- Calculate list price based on site price code
                            CASE
                                WHEN spc.price_formula_id = 'A' AND spc.multiplier IS NOT NULL THEN ROUND(fdb_data.awp_price::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = 'W' AND spc.multiplier IS NOT NULL THEN ROUND(fdb_data.wac_price::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = 'C' AND spc.multiplier IS NOT NULL THEN ROUND(inv.last_cost_ea::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = '1' AND spc.multiplier IS NOT NULL THEN ROUND(inv.add_price1::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = '2' AND spc.multiplier IS NOT NULL THEN ROUND(inv.add_price2::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = 'ASP' AND spc.multiplier IS NOT NULL THEN ROUND(fdb_data.asp_price::numeric * spc.multiplier::numeric, 4)
                                ELSE inv.list_price::numeric
                            END * pcf.expected_price_multiplier::numeric, 4)
                        WHEN pcf.expected_price_basis = '1' AND pcf.expected_price_multiplier IS NOT NULL THEN ROUND(inv.add_price1::numeric * pcf.expected_price_multiplier::numeric, 4)
                        WHEN pcf.expected_price_basis = '2' AND pcf.expected_price_multiplier IS NOT NULL THEN ROUND(inv.add_price2::numeric * pcf.expected_price_multiplier::numeric, 4)
                        ELSE ppmi.expected_price::numeric
                    END
            END,
            CASE
                WHEN ppmi.override_special_formula_id IS NOT NULL THEN
                    CASE 
                        WHEN ppmi.override_special_formula_id = 'A' AND ppmi.override_special_price_multiplier IS NOT NULL THEN ROUND(fdb_data.awp_price::numeric * ppmi.override_special_price_multiplier::numeric, 4)
                        WHEN ppmi.override_special_formula_id = 'W' AND ppmi.override_special_price_multiplier IS NOT NULL THEN ROUND(fdb_data.wac_price::numeric * ppmi.override_special_price_multiplier::numeric, 4)
                        WHEN ppmi.override_special_formula_id = 'C' AND ppmi.override_special_price_multiplier IS NOT NULL THEN ROUND(inv.last_cost_ea::numeric * ppmi.override_special_price_multiplier::numeric, 4)
                        WHEN ppmi.override_special_formula_id = 'ASP' AND ppmi.override_special_price_multiplier IS NOT NULL THEN ROUND(fdb_data.asp_price::numeric * ppmi.override_special_price_multiplier::numeric, 4)
                        WHEN ppmi.override_special_formula_id = 'L' AND ppmi.override_special_price_multiplier IS NOT NULL THEN ROUND(
                            -- Calculate list price based on site price code
                            CASE
                                WHEN spc.price_formula_id = 'A' AND spc.multiplier IS NOT NULL THEN ROUND(fdb_data.awp_price::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = 'W' AND spc.multiplier IS NOT NULL THEN ROUND(fdb_data.wac_price::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = 'C' AND spc.multiplier IS NOT NULL THEN ROUND(inv.last_cost_ea::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = '1' AND spc.multiplier IS NOT NULL THEN ROUND(inv.add_price1::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = '2' AND spc.multiplier IS NOT NULL THEN ROUND(inv.add_price2::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = 'ASP' AND spc.multiplier IS NOT NULL THEN ROUND(fdb_data.asp_price::numeric * spc.multiplier::numeric, 4)
                                ELSE inv.list_price::numeric
                            END * ppmi.override_special_price_multiplier::numeric, 4)
                        WHEN ppmi.override_special_formula_id = '1' AND ppmi.override_special_price_multiplier IS NOT NULL THEN ROUND(inv.add_price1::numeric * ppmi.override_special_price_multiplier::numeric, 4)
                        WHEN ppmi.override_special_formula_id = '2' AND ppmi.override_special_price_multiplier IS NOT NULL THEN ROUND(inv.add_price2::numeric * ppmi.override_special_price_multiplier::numeric, 4)
                        ELSE ppmi.special_price::numeric
                    END
                -- Otherwise, use the contract formula
                ELSE
                    CASE
                        WHEN pcf.special_price_basis = 'A' AND pcf.special_price_multiplier IS NOT NULL THEN ROUND(fdb_data.awp_price::numeric * pcf.special_price_multiplier::numeric, 4)
                        WHEN pcf.special_price_basis = 'W' AND pcf.special_price_multiplier IS NOT NULL THEN ROUND(fdb_data.wac_price::numeric * pcf.special_price_multiplier::numeric, 4)
                        WHEN pcf.special_price_basis = 'C' AND pcf.special_price_multiplier IS NOT NULL THEN ROUND(inv.last_cost_ea::numeric * pcf.special_price_multiplier::numeric, 4)
                        WHEN pcf.special_price_basis = 'ASP' AND pcf.special_price_multiplier IS NOT NULL THEN ROUND(fdb_data.asp_price::numeric * pcf.special_price_multiplier::numeric, 4)
                        WHEN pcf.special_price_basis = 'L' AND pcf.special_price_multiplier IS NOT NULL THEN ROUND(
                            -- Calculate list price based on site price code
                            CASE
                                WHEN spc.price_formula_id = 'A' AND spc.multiplier IS NOT NULL THEN ROUND(fdb_data.awp_price::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = 'W' AND spc.multiplier IS NOT NULL THEN ROUND(fdb_data.wac_price::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = 'C' AND spc.multiplier IS NOT NULL THEN ROUND(inv.last_cost_ea::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = '1' AND spc.multiplier IS NOT NULL THEN ROUND(inv.add_price1::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = '2' AND spc.multiplier IS NOT NULL THEN ROUND(inv.add_price2::numeric * spc.multiplier::numeric, 4)
                                WHEN spc.price_formula_id = 'ASP' AND spc.multiplier IS NOT NULL THEN ROUND(fdb_data.asp_price::numeric * spc.multiplier::numeric, 4)
                                ELSE inv.list_price::numeric
                            END * pcf.special_price_multiplier::numeric, 4)
                        WHEN pcf.special_price_basis = '1' AND pcf.special_price_multiplier IS NOT NULL THEN ROUND(inv.add_price1::numeric * pcf.special_price_multiplier::numeric, 4)
                        WHEN pcf.special_price_basis = '2' AND pcf.special_price_multiplier IS NOT NULL THEN ROUND(inv.add_price2::numeric * pcf.special_price_multiplier::numeric, 4)
                        ELSE ppmi.special_price::numeric
                    END
            END,
            FALSE,
            FALSE
        FROM form_inventory inv
        JOIN form_site_price_code_item spc ON inv.price_code_id = spc.price_code_id
        JOIN form_payer_price_matrix_item ppmi ON ppmi.inventory_id = inv.id
        JOIN form_payer_price_matrix ppm ON ppmi.payer_price_matrix_id = ppm.id
        JOIN form_payer_contract pc ON pc.assigned_matrix_id = ppm.id
        LEFT JOIN form_price_code_formulas pcf ON pcf.code_category = ppmi.price_code_id
        LEFT JOIN sf_form_payer_contract_to_price_code_formulas spcf ON spcf.form_payer_contract_fk = pc.id AND spcf.form_price_code_formulas_fk = pcf.id
        JOIN LATERAL (
            SELECT * FROM get_fdb_ndc_data(inv.ndc)
        ) fdb_data ON true
        WHERE inv.active = 'Yes'
        AND inv.type IN ('Drug', 'Compound')
        AND (
            COALESCE(inv.awp_price, 0) != COALESCE(fdb_data.awp_price, 0) OR
            COALESCE(inv.wac_price, 0) != COALESCE(fdb_data.wac_price, 0)
        )
        AND NOT EXISTS (
            SELECT 1 FROM form_inventory_pricing_cont_upt_item existing
            WHERE existing.pricing_update_no = pricing_update_uuid
            AND existing.price_matrix_item_id = ppmi.id
        );
        
        -- Process NDC updates not already in the pending update
        INSERT INTO form_inventory_pricing_upt_ndc (    
            created_by,
            created_on,
            pricing_update_no,
            inventory_id,
            pndc,
            repndc,
            deleted,
            archived
        )
        SELECT 
            1,
            CURRENT_TIMESTAMP,
            pricing_update_uuid,
            inv.id,
            inv.ndc,
            fdb.repndc,
            FALSE,
            FALSE
        FROM form_inventory inv
        JOIN form_list_fdb_ndc fdb ON inv.ndc = fdb.ndc
        WHERE inv.active = 'Yes'
        AND fdb.repndc IS NOT NULL
        AND fdb.dupdc >= last_update_date
        AND fdb.dupdc <= current_date
        -- Only add items not already in the pending update
        AND NOT EXISTS (
            SELECT 1 FROM form_inventory_pricing_upt_ndc existing
            WHERE existing.pricing_update_no = pricing_update_uuid
            AND existing.inventory_id = inv.id
        );
        
        -- Process obsolete drugs not already in the pending update
        INSERT INTO form_inventory_pricing_upt_obs (
            created_by,
            created_on,
            pricing_update_no,
            inventory_id,
            obsdtec,
            deleted,
            archived
        )
        SELECT 
            1,
            CURRENT_TIMESTAMP,
            pricing_update_uuid,
            inv.id,
            fdb.obsdtec,
            FALSE,
            FALSE
        FROM form_inventory inv
        JOIN form_list_fdb_ndc fdb ON inv.ndc = fdb.ndc
        WHERE inv.active = 'Yes'
        AND fdb.obsdtec IS NOT NULL
        AND fdb.dupdc >= last_update_date
        AND fdb.dupdc <= current_date
        -- Only add items not already in the pending update
        AND NOT EXISTS (
            SELECT 1 FROM form_inventory_pricing_upt_obs existing
            WHERE existing.pricing_update_no = pricing_update_uuid
            AND existing.inventory_id = inv.id
        );
    END IF;
END;
$$ LANGUAGE plpgsql;

DO $$ BEGIN
  PERFORM drop_all_function_signatures('apply_pricing_updates');
END $$;

CREATE OR REPLACE FUNCTION apply_pricing_updates(pricing_update_uuid uuid)
RETURNS void AS $$
DECLARE
    total_items integer;
    processed_items integer := 0;
    start_time timestamp;
    error_message text;
BEGIN
    -- Begin transaction
    START TRANSACTION;
    
    -- Update status to Loading and set start time
    UPDATE form_inventory_pricing_update
    SET status = 'Loading',
        started_loading = CURRENT_TIMESTAMP,
        percentage_complete = 0
    WHERE pricing_update_no = pricing_update_uuid;
    
    -- Get start time
    SELECT started_loading INTO start_time
    FROM form_inventory_pricing_update
    WHERE pricing_update_no = pricing_update_uuid;
    
    -- Count total items to process
    SELECT COUNT(*) INTO total_items
    FROM (
        -- Count inventory price updates
        SELECT id FROM form_inventory_pricing_upt_item 
        WHERE pricing_update_no = pricing_update_uuid
        UNION ALL
        -- Count contract price updates
        SELECT id FROM form_inventory_pricing_cont_upt_item 
        WHERE pricing_update_no = pricing_update_uuid
        UNION ALL
        -- Count NDC updates
        SELECT id FROM form_inventory_pricing_upt_ndc 
        WHERE pricing_update_no = pricing_update_uuid
        UNION ALL
        -- Count obsolete drugs
        SELECT id FROM form_inventory_pricing_upt_obs 
        WHERE pricing_update_no = pricing_update_uuid
    ) AS items;
    
    BEGIN
        -- Update inventory pricing
        UPDATE form_inventory inv
        SET 
            list_price = upt.updated_list_price::numeric,
            awp_price = upt.updated_awp_price::numeric,
            awp_price_pkg = upt.updated_awp_price_pkg::numeric,
            wac_price = upt.updated_wac_price::numeric,
            wac_price_pkg = upt.updated_wac_price_pkg::numeric,
            asp_price = upt.updated_asp_price::numeric,
            avg_acq_cost_brand = upt.updated_avg_acq_cost_brand::numeric,
            avg_acq_cost_gen = upt.updated_avg_acq_cost_gen::numeric,
            ful = upt.updated_ful::numeric,
            last_update_at = CURRENT_TIMESTAMP
        FROM form_inventory_pricing_upt_item upt
        WHERE inv.id = upt.inventory_id
        AND upt.pricing_update_no = pricing_update_uuid;
        
        -- Update processed count for inventory updates
        processed_items := processed_items + 
            (SELECT COUNT(*) FROM form_inventory_pricing_upt_item 
            WHERE pricing_update_no = pricing_update_uuid);
        
        -- Update percentage complete after inventory updates
        UPDATE form_inventory_pricing_update
        SET percentage_complete = ROUND((processed_items::numeric / 
                                        CASE WHEN total_items = 0 THEN 1 ELSE total_items::numeric END) * 100)::numeric
        WHERE pricing_update_no = pricing_update_uuid;
        
        -- Update contract pricing
        UPDATE form_payer_price_matrix_item ppmi
        SET 
            expected_price = upt.updated_expected_price::numeric,
            special_price = CASE 
                WHEN EXISTS (
                    SELECT 1 
                    FROM form_payer_contract pc 
                    JOIN form_payer_price_matrix ppm ON pc.assigned_matrix_id = ppm.id
                    WHERE ppm.id = ppmi.payer_price_matrix_id
                    AND pc.contract_type = 'Shared Contract'
                ) THEN upt.updated_special_price::numeric
                ELSE ppmi.special_price
            END
        FROM form_inventory_pricing_cont_upt_item upt
        WHERE ppmi.id = upt.price_matrix_item_id
        AND upt.pricing_update_no = pricing_update_uuid;
        
        -- Update processed count for contract updates
        processed_items := processed_items + 
            (SELECT COUNT(*) FROM form_inventory_pricing_cont_upt_item 
             WHERE pricing_update_no = pricing_update_uuid);
        
        -- Update percentage complete after contract updates
        UPDATE form_inventory_pricing_update
        SET percentage_complete = ROUND((processed_items::numeric / 
                                        CASE WHEN total_items = 0 THEN 1 ELSE total_items::numeric END) * 100)::numeric
        WHERE pricing_update_no = pricing_update_uuid;
        
 
        -- Apply NDC updates to inventory
        UPDATE form_inventory inv
        SET 
            ndc = ndc_update.repndc,
            -- Get formatted NDC from the replacement NDC
            formatted_ndc = (
                SELECT fn.formatted_ndc
                FROM form_list_fdb_ndc fn
                WHERE fn.ndc = ndc_update.repndc
                LIMIT 1
            )
        FROM form_inventory_pricing_upt_ndc ndc_update
        WHERE inv.id = ndc_update.inventory_id
        AND ndc_update.pricing_update_no = pricing_update_uuid;
        
        -- Update processed count for NDC updates
        processed_items := processed_items + 
            (SELECT COUNT(*) FROM form_inventory_pricing_upt_ndc 
            WHERE pricing_update_no = pricing_update_uuid);
        
        -- Update percentage complete after NDC updates
        UPDATE form_inventory_pricing_update
        SET percentage_complete = ROUND((processed_items::numeric / 
                                        CASE WHEN total_items = 0 THEN 1 ELSE total_items::numeric END) * 100)::numeric
        WHERE pricing_update_no = pricing_update_uuid;

        -- Mark obsolete drugs as inactive
        UPDATE form_inventory inv
        SET active = NULL
        FROM form_inventory_pricing_upt_obs obs
        WHERE inv.id = obs.inventory_id
        AND obs.pricing_update_no = pricing_update_uuid;
        
        -- Update processed count for obsolete drugs
        processed_items := processed_items + 
            (SELECT COUNT(*) FROM form_inventory_pricing_upt_obs 
            WHERE pricing_update_no = pricing_update_uuid);
        
        -- Update percentage complete to 100%
        UPDATE form_inventory_pricing_update
        SET 
            percentage_complete = 100::numeric,
            status = 'Completed',
            completed_loading = CURRENT_TIMESTAMP,
            total_loading_time = EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - start_time)) / 60.0::numeric
        WHERE pricing_update_no = pricing_update_uuid;
        
        -- Commit the transaction
        COMMIT;

    EXCEPTION WHEN OTHERS THEN
        -- Rollback the transaction
        ROLLBACK;
        
        GET STACKED DIAGNOSTICS error_message = MESSAGE_TEXT;
        
        -- Update status to Error
        UPDATE form_inventory_pricing_update
        SET 
            status = 'Error',
            action = NULL,
            error_message = error_message
        WHERE pricing_update_no = pricing_update_uuid;
    END;

END;
$$ LANGUAGE plpgsql;

DO $$ BEGIN
  PERFORM drop_all_function_signatures('handle_pricing_update_action');
END $$;

CREATE OR REPLACE FUNCTION handle_pricing_update_action()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if the action was changed to "Load Pricing"
    IF NEW.action = 'Load Pricing' AND 
       (OLD.action IS NULL OR OLD.action != 'Load Pricing') AND 
       NEW.status IN ('Pending', 'Error') THEN
        -- Call apply_pricing_updates function with the pricing_update_no
        PERFORM apply_pricing_updates(NEW.pricing_update_no::uuid);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for pricing update action
CREATE OR REPLACE TRIGGER handle_pricing_update_action_trigger
AFTER UPDATE ON form_inventory_pricing_update
FOR EACH ROW EXECUTE FUNCTION handle_pricing_update_action();

DO $$ BEGIN
  PERFORM drop_all_function_signatures('fdb_data_changes_trigger');
END $$;
CREATE OR REPLACE FUNCTION fdb_data_changes_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Call function to check and create pricing update
    PERFORM check_and_create_pricing_update();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for FDB tables
CREATE OR REPLACE TRIGGER fdb_ndc_price_change_trigger
AFTER INSERT OR UPDATE ON form_list_fdb_ndc_price
FOR EACH STATEMENT EXECUTE FUNCTION fdb_data_changes_trigger();

CREATE OR REPLACE TRIGGER fdb_medicare_price_change_trigger
AFTER INSERT OR UPDATE ON form_list_fdb_medicare_price
FOR EACH STATEMENT EXECUTE FUNCTION fdb_data_changes_trigger();

CREATE OR REPLACE TRIGGER fdb_ndc_change_trigger
AFTER INSERT OR UPDATE ON form_list_fdb_ndc
FOR EACH STATEMENT EXECUTE FUNCTION fdb_data_changes_trigger();

DO $$ BEGIN
  PERFORM drop_all_function_signatures('update_matrix_item_pricing');
END $$;

CREATE OR REPLACE FUNCTION update_matrix_item_pricing()
RETURNS TRIGGER AS $$
DECLARE
    v_matrix_record record;
    v_price_code_record record;
    v_formula_record record;
    v_list_price numeric;
    v_expected_price numeric;
    v_special_price numeric;
    v_expected_formula_id text;
    v_special_formula_id text;
    v_expected_multiplier numeric;
    v_special_multiplier numeric;
BEGIN
    -- Only process active drug or compound items
    IF NEW.active = 'Yes' AND NEW.type IN ('Drug', 'Compound') THEN
        -- Get the site price code for this item
        SELECT * INTO v_price_code_record 
        FROM form_site_price_code_item 
        WHERE price_code_id = NEW.price_code_id
        LIMIT 1;

        -- Calculate list price
        IF v_price_code_record.price_formula_id = 'A' THEN
            v_list_price := ROUND(NEW.awp_price::numeric * v_price_code_record.multiplier::numeric, 4);
        ELSIF v_price_code_record.price_formula_id = 'W' THEN
            v_list_price := ROUND(NEW.wac_price::numeric * v_price_code_record.multiplier::numeric, 4);
        ELSIF v_price_code_record.price_formula_id = 'C' THEN
            v_list_price := ROUND(NEW.last_cost_ea::numeric * v_price_code_record.multiplier::numeric, 4);
        ELSIF v_price_code_record.price_formula_id = 'ASP' THEN
            v_list_price := ROUND(NEW.asp_price::numeric * v_price_code_record.multiplier::numeric, 4);
        ELSIF v_price_code_record.price_formula_id = '1' THEN
            v_list_price := ROUND(NEW.add_price1::numeric * v_price_code_record.multiplier::numeric, 4);
        ELSIF v_price_code_record.price_formula_id = '2' THEN
            v_list_price := ROUND(NEW.add_price2::numeric * v_price_code_record.multiplier::numeric, 4);
        ELSE
            v_list_price := NEW.list_price::numeric;
        END IF;

        NEW.list_price := v_list_price;
        -- Add to each price matrix
        FOR v_matrix_record IN 
        SELECT * FROM form_payer_price_matrix_item ppmi
        WHERE ppmi.inventory_id = NEW.id
        LOOP
            -- For each matrix, check all contracts using this matrix
            FOR v_formula_record IN 
                SELECT pc.id as contract_id, pc.contract_type, pcf.*
                FROM form_payer_contract pc
                LEFT JOIN sf_form_payer_contract_to_price_code_formulas spcf ON spcf.form_payer_contract_fk = pc.id
                LEFT JOIN form_price_code_formulas pcf ON pcf.id = spcf.form_price_code_formulas_fk AND pcf.code_category = NEW.price_code_id
                WHERE pc.assigned_matrix_id = v_matrix_record.payer_price_matrix_id
                LIMIT 1
            LOOP
            
                IF v_matrix_record.override_expected_formula_id IS NOT NULL AND v_matrix_record.override_expected_price_multiplier IS NOT NULL THEN
                    v_expected_formula_id := v_matrix_record.override_expected_formula_id;
                    v_expected_multiplier := v_matrix_record.override_expected_price_multiplier;
                ELSIF v_formula_record.expected_price_basis IS NOT NULL AND v_formula_record.expected_price_multiplier IS NOT NULL THEN
                    v_expected_formula_id := v_formula_record.expected_price_basis;
                    v_expected_multiplier := v_formula_record.expected_price_multiplier;
                ELSE
                    v_expected_formula_id := v_price_code_record.price_formula_id;
                    v_expected_multiplier := v_price_code_record.multiplier;
                END IF;
                IF v_matrix_record.override_special_formula_id IS NOT NULL AND v_matrix_record.override_special_price_multiplier IS NOT NULL THEN
                    v_special_formula_id := v_matrix_record.override_special_formula_id;
                    v_special_multiplier := v_matrix_record.override_special_price_multiplier;
                ELSIF v_formula_record.special_price_basis IS NOT NULL AND v_formula_record.special_price_multiplier IS NOT NULL THEN
                    v_special_formula_id := v_formula_record.special_price_basis;
                    v_special_multiplier := v_formula_record.special_price_multiplier;
                ELSE
                    v_special_formula_id := v_price_code_record.price_formula_id;
                    v_special_multiplier := v_price_code_record.multiplier;
                END IF;

                -- Calculate expected price
                IF v_expected_formula_id = 'A' THEN
                    v_expected_price := ROUND(NEW.awp_price::numeric * v_expected_multiplier::numeric, 4);
                ELSIF v_expected_formula_id = 'W' THEN
                    v_expected_price := ROUND(NEW.wac_price::numeric * v_expected_multiplier::numeric, 4);
                ELSIF v_expected_formula_id = 'C' THEN
                    v_expected_price := ROUND(NEW.last_cost_ea::numeric * v_expected_multiplier::numeric, 4);
                ELSIF v_expected_formula_id = 'ASP' THEN
                    v_expected_price := ROUND(NEW.asp_price::numeric * v_expected_multiplier::numeric, 4);
                ELSIF v_expected_formula_id = 'L' THEN
                    v_expected_price := ROUND(v_list_price::numeric * v_expected_multiplier::numeric, 4);
                ELSIF v_expected_formula_id = '1' THEN
                    v_expected_price := ROUND(NEW.add_price1::numeric * v_expected_multiplier::numeric, 4);
                ELSIF v_expected_formula_id = '2' THEN
                    v_expected_price := ROUND(NEW.add_price2::numeric * v_expected_multiplier::numeric, 4);
                ELSE
                    v_expected_price := v_list_price;
                END IF;
                
                -- Update expected price in the matrix item
                UPDATE form_payer_price_matrix_item
                SET expected_price = v_expected_price
                WHERE id = v_matrix_record.id AND expected_price <> v_expected_price;
                
                IF v_formula_record.contract_type = 'Shared Contract' THEN
                    -- Calculate special price
                    IF v_special_formula_id = 'A' THEN
                        v_special_price := ROUND(NEW.awp_price::numeric * v_special_multiplier::numeric, 4);
                    ELSIF v_special_formula_id = 'W' THEN
                        v_special_price := ROUND(NEW.wac_price::numeric * v_special_multiplier::numeric, 4);
                    ELSIF v_special_formula_id = 'C' THEN
                        v_special_price := ROUND(NEW.last_cost_ea::numeric * v_special_multiplier::numeric, 4);
                    ELSIF v_special_formula_id = 'ASP' THEN
                        v_special_price := ROUND(NEW.asp_price::numeric * v_special_multiplier::numeric, 4);
                    ELSIF v_special_formula_id = 'L' THEN
                        v_special_price := ROUND(v_list_price::numeric * v_special_multiplier::numeric, 4);
                    ELSIF v_special_formula_id = '1' THEN
                        v_special_price := ROUND(NEW.add_price1::numeric * v_special_multiplier::numeric, 4);
                    ELSIF v_special_formula_id = '2' THEN
                        v_special_price := ROUND(NEW.add_price2::numeric * v_special_multiplier::numeric, 4);
                    ELSE
                        v_special_price := v_list_price;
                    END IF;
                    
                    -- Update special price in the matrix item for Shared Contract only
                    UPDATE form_payer_price_matrix_item itm
                    SET special_price = v_special_price
                    WHERE itm.id = v_matrix_record.id AND itm.special_price <> v_special_price;
                END IF;
            END LOOP;
        END LOOP;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for new inventory items
CREATE OR REPLACE TRIGGER add_inventory_to_matrices_trigger
AFTER INSERT OR UPDATE ON form_inventory
FOR EACH ROW EXECUTE FUNCTION update_matrix_item_pricing();

CREATE OR REPLACE VIEW new_ndc_updates AS
SELECT
    fdb.daddnc,
    fdb.dupdc,
    fdb.ln,
    fdb.formatted_ndc,
    fdb.ps,
    CASE 
        WHEN fdb.df = '1' THEN 'each'
        WHEN fdb.df = '2' THEN 'mL'
        WHEN fdb.df = '3' THEN 'gram'
        ELSE 'each'
    END AS billing_unit_id,
    fdb.hcfa_ps,
    fdb.hcfa_unit
FROM form_list_fdb_ndc fdb
ORDER BY fdb.daddnc DESC;
