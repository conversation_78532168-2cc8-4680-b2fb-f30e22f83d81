BEGIN;
-- Indexes for ledger finance table
CREATE INDEX IF NOT EXISTS idx_ledger_finance_account_type ON form_ledger_finance(account_type);
CREATE INDEX IF NOT EXISTS idx_ledger_finance_post_datetime ON form_ledger_finance(post_datetime);
CREATE INDEX IF NOT EXISTS idx_ledger_finance_source_form_id ON form_ledger_finance(source_form, source_id);
CREATE INDEX IF NOT EXISTS idx_ledger_finance_invoice_id ON form_ledger_finance(invoice_id);
CREATE INDEX IF NOT EXISTS idx_ledger_finance_charge_line_id ON form_ledger_finance(charge_line_id);

-- Indexes for billing invoice table
CREATE INDEX IF NOT EXISTS idx_billing_invoice_master_invoice_no ON form_billing_invoice(master_invoice_no, invoice_no);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_void_archived_deleted ON form_billing_invoice(void, archived, deleted);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_revenue_accepted_posted ON form_billing_invoice(revenue_accepted_posted);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_revenue_accepted ON form_billing_invoice(revenue_accepted);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_bill_for_denial ON form_billing_invoice(bill_for_denial);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_created_on ON form_billing_invoice(created_on);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_patient_id ON form_billing_invoice(patient_id);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_payer_id ON form_billing_invoice(payer_id);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_billing_method_id ON form_billing_invoice(billing_method_id);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_type_id ON form_billing_invoice(type_id);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_delivery_ticket_id ON form_billing_invoice(delivery_ticket_id);
CREATE INDEX IF NOT EXISTS idx_billing_invoice_on_hold ON form_billing_invoice(on_hold);

-- Indexes for NCPDP tables
CREATE INDEX IF NOT EXISTS idx_ncpdp_void_archived_deleted ON form_ncpdp(void, archived, deleted);
CREATE INDEX IF NOT EXISTS idx_ncpdp_is_test ON form_ncpdp(is_test);
CREATE INDEX IF NOT EXISTS idx_ncpdp_status ON form_ncpdp(status);
CREATE INDEX IF NOT EXISTS idx_ncpdp_auth_flag ON form_ncpdp(auth_flag);
CREATE INDEX IF NOT EXISTS idx_ncpdp_claim_no ON form_ncpdp(claim_no);
CREATE INDEX IF NOT EXISTS idx_ncpdp_inventory ON form_ncpdp(inventory_id);
CREATE INDEX IF NOT EXISTS idx_ncpdp_date_of_service ON form_ncpdp(date_of_service);

-- Indexes for NCPDP response table
CREATE INDEX IF NOT EXISTS idx_ncpdp_response_claim_no ON form_ncpdp_response(claim_no);
CREATE INDEX IF NOT EXISTS idx_ncpdp_response_response_status ON form_ncpdp_response(response_status);
CREATE INDEX IF NOT EXISTS idx_ncpdp_response_transaction_response_status ON form_ncpdp_response(transaction_response_status);

-- Indexes for med claim tables
CREATE INDEX IF NOT EXISTS idx_med_claim_void_archived_deleted ON form_med_claim(void, archived, deleted);
CREATE INDEX IF NOT EXISTS idx_med_claim_status ON form_med_claim(status);
CREATE INDEX IF NOT EXISTS idx_med_claim_claim_no ON form_med_claim(claim_no);
CREATE INDEX IF NOT EXISTS idx_med_claim_inventory_id ON form_med_claim(inventory_id);
CREATE INDEX IF NOT EXISTS idx_med_claim_service_date ON form_med_claim(service_date);

-- Indexes for med claim 835 table
CREATE INDEX IF NOT EXISTS idx_med_claim_835_claim_no ON form_med_claim_resp_835(claim_no);
CREATE INDEX IF NOT EXISTS idx_med_claim_835_claim_status_code ON form_med_claim_resp_835(claim_status_code);

-- Indexes for writeoff reason table
CREATE INDEX IF NOT EXISTS idx_list_writeoff_reason_bad_debt ON form_list_writeoff_reason(bad_debt);
CREATE INDEX IF NOT EXISTS idx_list_writeoff_reason_code ON form_list_writeoff_reason(code);

-- Indexes for careplan_order_rx_disp table
CREATE INDEX IF NOT EXISTS idx_careplan_order_rx_disp_status ON form_careplan_order_rx_disp(status);
CREATE INDEX IF NOT EXISTS idx_careplan_order_rx_disp_delivery_ticket_id ON form_careplan_order_rx_disp(delivery_ticket_id);

-- Indexes for gerund tables
CREATE INDEX IF NOT EXISTS idx_sf_billing_to_med_claim_invoice_fk ON sf_form_billing_invoice_to_med_claim(form_billing_invoice_fk, archive, delete);
CREATE INDEX IF NOT EXISTS idx_sf_billing_to_med_claim_med_claim_fk ON sf_form_billing_invoice_to_med_claim(form_med_claim_fk);

CREATE INDEX IF NOT EXISTS idx_sf_billing_to_ncpdp_invoice_fk ON sf_form_billing_invoice_to_ncpdp(form_billing_invoice_fk, archive, delete);
CREATE INDEX IF NOT EXISTS idx_sf_billing_to_ncpdp_ncpdp_fk ON sf_form_billing_invoice_to_ncpdp(form_ncpdp_fk);

-- Composite indexes for frequently used query patterns
CREATE INDEX IF NOT EXISTS idx_billing_invoice_master_invoice_void_billfor ON form_billing_invoice(master_invoice_no, void, bill_for_denial) 
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_billing_invoice_pending_revenue ON form_billing_invoice(master_invoice_no, revenue_accepted_posted, revenue_accepted, total_expected) 
WHERE void IS NULL AND archived IS NOT TRUE AND deleted IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_ncpdp_auth_clean_claims ON form_ncpdp(auth_flag, is_test) 
WHERE void IS NULL AND archived IS NOT TRUE AND deleted IS NOT TRUE;

-- Partial indexes for better performance on specific queries
CREATE INDEX IF NOT EXISTS idx_med_claim_denied_status ON form_med_claim(status) 
WHERE status IN ('Rejected', 'Denied', 'Error') AND void IS NULL AND archived IS NOT TRUE AND deleted IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_ncpdp_rejected_status ON form_ncpdp(status) 
WHERE status IN ('Rejected', 'Rebill Rejected') AND void IS NULL AND archived IS NOT TRUE AND deleted IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_med_claim_payable_status ON form_med_claim(status) 
WHERE status IN ('Partially Paid', 'Payable') AND void IS NULL AND archived IS NOT TRUE AND deleted IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_ncpdp_payable_status ON form_ncpdp(status) 
WHERE status = 'Payable' AND void IS NULL AND archived IS NOT TRUE AND deleted IS NOT TRUE;
CREATE INDEX IF NOT EXISTS idx_billing_invoice_created_quarter ON form_billing_invoice(DATE_TRUNC('quarter', created_on));
END;

-- Materialized Views
DO $$
BEGIN
    CREATE MATERIALIZED VIEW IF NOT EXISTS view_mvw_clean_claims_details AS
    SELECT * FROM vw_master_claims_cube where auth_flag = NULL AND "Claim Status" IN ('Payable');

    PERFORM setup_view_refresh_schedule('view_mvw_clean_claims_details', '*/10 * * * *', 'view_refresh_query');

    CREATE MATERIALIZED VIEW IF NOT EXISTS view_mvw_denied_claims_details AS
    SELECT * FROM vw_master_claims_cube WHERE "Claim Status" IN ('Rejected', 'Rebill Rejected', 'Denied', 'Error');
    PERFORM setup_view_refresh_schedule('view_mvw_denied_claims_details', '*/10 * * * *', 'view_refresh_query');

    CREATE MATERIALIZED VIEW IF NOT EXISTS view_mvw_finance_ledger_details AS
    SELECT
        -- Primary identifiers
        lf.id,
        REPLACE(lf.source_form, 'form_', '') AS form_name,
        lf.source_id AS form_id,
        
        -- Patient information  
    lcl.patient_id,
    lcl."MRN",
    lcl."Last Name",
    lcl."First Name",
    lcl.patient_status,
    lcl.patient_status_auto_name,

    -- Payer information 
    lcl.payer_type,
    lcl.payer_type_auto_name,
    lcl.payer,
    lcl.payer_auto_name,
    lcl.shared_contract,
    lcl.shared_contract_auto_name,

    -- Site and inventory information
    lcl.site,
    lcl.site_auto_name,
    lcl.revenue_code,
    lcl.revenue_code_auto_name,
    lcl.quantity_raw,
    lcl.quantity,
    lcl.ndc,
    lcl.hcpc_code,
    lcl.item,
    lcl.brand_name,
    lcl.brand_name_auto_name,
    lcl.manufacturer,
    lcl.manufacturer_auto_name,
    lcl.is_specialty,
    lcl.therapy,
    lcl.therapy_auto_name,
    lcl.inventory_id,
    lcl.inventory_id_auto_name,
    ll.lot_no,
    ls.serial_no,
    
    -- Invoice information
    lcl."DOS",
    lcl."Post Date",
    lcl.sales_rep,
    lcl.sales_rep_auto_name,
    lcl.sales_account,
    lcl.sales_account_auto_name,
    lcl.sales_territory,
    lcl.sales_territory_auto_name,
    lcl.billing_method,
    lcl.billing_method_auto_name,
    
    -- Physician information
    lcl.physician,
    lcl.physician_auto_name,
    lcl.physician_state,
    lcl.physician_zip,
    
    -- Transaction identifiers
    lf.account_id,
    lf.charge_line_id,
    lf.charge_no,
    lf.invoice_id,
    lf.invoice_no AS "Invoice No",
    
    -- Transaction timestamps with formatting
    lf.transaction_datetime,
    lf.post_datetime,
    TO_CHAR(lf.transaction_datetime, 'MM/DD/YYYY HH:MI AM') AS "Transaction Date/Time",
    TO_CHAR(lf.post_datetime, 'MM/DD/YYYY HH:MI AM') AS "Posted Date/Time",
    
    -- Transaction type information
    lf.transaction_type AS source,
    lf.account_type AS "Type",
    
    -- Amount calculation (different formula based on account type)
    CASE
        WHEN lf.account_type IN ('Revenue') THEN COALESCE(lf.credit, 0) - COALESCE(lf.debit, 0)
        ELSE COALESCE(lf.debit, 0) - COALESCE(lf.credit, 0)
    END AS amount_raw,
    
    format_currency(
        CASE
            WHEN lf.account_type IN ('Revenue') THEN COALESCE(lf.credit, 0) - COALESCE(lf.debit, 0)
            ELSE COALESCE(lf.debit, 0) - COALESCE(lf.credit, 0)
        END::numeric
    ) AS "Amount",

    -- Row coloring logic
    CASE
        WHEN lf.transaction_type IN ('Posting', 'Cash Allocation') THEN '#9EDBC7'                          			 -- Green for payments
        WHEN lf.transaction_type IN ('Writeoff', 'Writeoff Reversal') THEN '#FCB6B6'           						 -- Red for writeoffs
        WHEN lf.transaction_type IN ('Adjustment', 'Adjustment Reversal') AND lf.credit::numeric > 0 THEN '#FACAA5'  -- Orange for adjustments with credits
        ELSE NULL
    END AS __row_color,
    
    -- Additional raw fields for reference
    lf.account_type,
    lf.debit,
    lf.credit,
    lf.transaction_type,
    lf.po_id,
    lf.ledger_inventory_id,
    lf.ledger_lot_id,
    lf.ledger_serial_id,
    lf.notes
    FROM
        form_ledger_finance lf
    LEFT JOIN form_ledger_lot ll ON ll.id = lf.ledger_lot_id
    LEFT JOIN form_ledger_serial ls ON ls.id = lf.ledger_serial_id
    INNER JOIN
        vw_charge_line_report lcl ON lcl.charge_line_id = lf.charge_line_id

    WHERE 
    lf.account_type IN ('Revenue', 'AR', 'Cash', 'Cash Allocation');

    PERFORM setup_view_refresh_schedule('view_mvw_finance_ledger_details', '*/10 * * * *', 'view_refresh_query');

    CREATE MATERIALIZED VIEW IF NOT EXISTS view_mvw_initial_denied_claims_details AS
    SELECT * FROM vw_master_claims_cube mcc
    inner join vw_initially_denied_claims dc ON dc.invoice_no = mcc."Invoice No";
    PERFORM setup_view_refresh_schedule('view_mvw_initial_denied_claims_details', '*/10 * * * *', 'view_refresh_query');

    CREATE MATERIALIZED VIEW IF NOT EXISTS view_mvw_pending_medical_claims AS
    WITH claim_counts AS (
        SELECT
            disp.status,
            COUNT(*) AS claim_count
        FROM 
            form_billing_invoice bi
        LEFT JOIN form_careplan_order_rx_disp disp on disp.master_invoice_no = bi.master_invoice_no

        WHERE
            COALESCE(bi.void, 'No') = 'No'
            AND bi.archived IS NOT TRUE
            AND bi."deleted" IS NOT TRUE
            AND COALESCE(bi.on_hold, 'No') = 'No'
            AND COALESCE(bi.revenue_accepted_posted, 'No') = 'No'
            AND ((bi.billing_method_id = 'ncpdp' AND COALESCE(bi.bill_mm_and_ncpdp, 'No') = 'Yes')
                OR (bi.billing_method_id IN ('mm', 'cms1500')))
            AND disp.status IS NOT NULL
        GROUP BY
            disp.status
    ),
    total_count AS (
        SELECT SUM(claim_count) AS total FROM claim_counts
    )
    SELECT
        disp_status.status AS description,
        COALESCE(cc.claim_count, 0) AS count,
        CASE 
            WHEN t.total > 0 THEN 
                ROUND((COALESCE(cc.claim_count, 0)::numeric / t.total), 2)
            ELSE 0
        END AS value,
        CASE disp_status.status
            WHEN 'Order Entry/Setup' THEN '#F5F5F5'
            WHEN 'Claims to Adjudicate' THEN '#E3E5E8'
            WHEN 'Test Claim' THEN '#D7DAE0'
            WHEN 'Rx Verification' THEN '#B6BAC2'
            WHEN 'Ready to Contact' THEN '#B2CEED'
            WHEN 'Ready to Refill' THEN '#8DB1D9'
            WHEN 'Print Labels / Fill Rx' THEN '#769BC4'
            WHEN 'Order Verification' THEN '#5B83B0'
            WHEN 'Delivery Ticket Confirmation' THEN '#50749C'
            WHEN 'Confirmed' THEN '#415D7D'
            ELSE '#CCCCCC'
        END AS color
    FROM 
        (VALUES
            ('Order Entry/Setup'),
            ('Claims to Adjudicate'),
            ('Test Claim'),
            ('Rx Verification'),
            ('Ready to Contact'),
            ('Ready to Refill'),
            ('Print Labels / Fill Rx'),
            ('Order Verification'),
            ('Delivery Ticket Confirmation'),
            ('Confirmed')
        ) AS disp_status(status)
    LEFT JOIN
        claim_counts cc ON cc.status = disp_status.status
    CROSS JOIN
        total_count t;

    PERFORM setup_view_refresh_schedule('view_mvw_pending_medical_claims', '*/10 * * * *', 'view_refresh_query');

    CREATE MATERIALIZED VIEW IF NOT EXISTS view_mvw_pending_revenue_details AS
    SELECT * FROM vw_pending_revenue_report where COALESCE(void,'No') <> 'Yes' ;
    PERFORM setup_view_refresh_schedule('view_mvw_pending_revenue_details', '*/10 * * * *', 'view_refresh_query');

    CREATE MATERIALIZED VIEW IF NOT EXISTS view_mvw_pending_pharmacy_claims AS
    WITH claim_counts AS (
        SELECT
            disp.status,
            COUNT(*) AS claim_count
        FROM 
            form_billing_invoice bi
        LEFT JOIN form_careplan_order_rx_disp disp on disp.master_invoice_no = bi.master_invoice_no

        WHERE
            CONCAT(bi.master_invoice_no,'-1') = bi.invoice_no
            AND COALESCE(bi.void, 'No') = 'No'
            AND bi.archived IS NOT TRUE
            AND bi."deleted" IS NOT TRUE
            AND COALESCE(bi.on_hold, 'No') = 'No'
            AND COALESCE(bi.revenue_accepted_posted, 'No') = 'No'
            AND bi.type_id NOT IN ('CMMED', 'MCRB')
            AND bi.billing_method_id = 'ncpdp'
            AND disp.status IS NOT NULL
        GROUP BY
            disp.status
    ),
    total_count AS (
        SELECT SUM(claim_count) AS total FROM claim_counts
    )
    SELECT
        disp_status.status AS description,
        COALESCE(cc.claim_count, 0) AS count,
        CASE 
            WHEN t.total > 0 THEN 
                ROUND((COALESCE(cc.claim_count, 0)::numeric / t.total), 2)
            ELSE 0
        END AS value,
        CASE disp_status.status
            WHEN 'Order Entry/Setup' THEN '#F5F5F5'
            WHEN 'Claims to Adjudicate' THEN '#E3E5E8'
            WHEN 'Test Claim' THEN '#D7DAE0'
            WHEN 'Rx Verification' THEN '#B6BAC2'
            WHEN 'Ready to Contact' THEN '#F6F5FF'
            WHEN 'Ready to Refill' THEN '#D6D3EB'
            WHEN 'Print Labels / Fill Rx' THEN '#A69FCC'
            WHEN 'Order Verification' THEN '#837BB2'
            WHEN 'Delivery Ticket Confirmation' THEN '#746D9E'
            WHEN 'Confirmed' THEN '#655F8A'
            ELSE '#CCCCCC'
        END AS color
    FROM 
        (VALUES
            ('Order Entry/Setup'),
            ('Claims to Adjudicate'),
            ('Test Claim'),
            ('Rx Verification'),
            ('Ready to Contact'),
            ('Ready to Refill'),
            ('Print Labels / Fill Rx'),
            ('Order Verification'),
            ('Delivery Ticket Confirmation'),
            ('Confirmed')
        ) AS disp_status(status)
    LEFT JOIN
        claim_counts cc ON cc.status = disp_status.status
    CROSS JOIN
        total_count t;
    PERFORM setup_view_refresh_schedule('view_mvw_pending_pharmacy_claims', '*/10 * * * *', 'view_refresh_query');

    CREATE MATERIALIZED VIEW IF NOT EXISTS view_mvw_reworked_claims_details AS
    SELECT * FROM vw_master_claims_cube mcc
    inner join vw_initially_denied_claims dc ON dc.invoice_no = mcc."Invoice No"
    WHERE "Claim Status" IN ('Payable');
    PERFORM setup_view_refresh_schedule('view_mvw_reworked_claims_details', '*/10 * * * *', 'view_refresh_query');

    CREATE MATERIALIZED VIEW IF NOT EXISTS view_mvw_writeoffs_details AS
    SELECT * FROM vw_writeoffs_report;
    PERFORM setup_view_refresh_schedule('view_mvw_writeoffs_details', '*/10 * * * *', 'view_refresh_query');


    -- Indexes for view_mvw_finance_ledger_details
    CREATE INDEX IF NOT EXISTS idx_mvw_finance_ledger_post_datetime ON view_mvw_finance_ledger_details(post_datetime);
    CREATE INDEX IF NOT EXISTS idx_mvw_finance_ledger_txn_datetime ON view_mvw_finance_ledger_details(transaction_datetime);
    CREATE INDEX IF NOT EXISTS idx_mvw_finance_ledger_patient_id ON view_mvw_finance_ledger_details(patient_id);
    CREATE INDEX IF NOT EXISTS idx_mvw_finance_ledger_amount_raw ON view_mvw_finance_ledger_details(amount_raw);

    -- Indexes for view_mvw_pending_revenue_details
    CREATE INDEX IF NOT EXISTS idx_mvw_pending_revenue_patient_id ON view_mvw_pending_revenue_details(patient_id);
    CREATE INDEX IF NOT EXISTS idx_mvw_pending_revenue_billing_method ON view_mvw_pending_revenue_details(billing_method);

    -- Indexes for view_mvw_denied_claims_details
    CREATE INDEX IF NOT EXISTS idx_mvw_denied_claims_claim_status ON view_mvw_denied_claims_details("Claim Status");
    CREATE INDEX IF NOT EXISTS idx_mvw_denied_claims_patient_id ON view_mvw_denied_claims_details(patient_id);

    -- Indexes for view_mvw_initial_denied_claims_details
    CREATE INDEX IF NOT EXISTS idx_mvw_initial_denied_claim_status ON view_mvw_initial_denied_claims_details("Claim Status");
    CREATE INDEX IF NOT EXISTS idx_mvw_initial_denied_patient_id ON view_mvw_initial_denied_claims_details(patient_id);

    -- Indexes for view_mvw_reworked_claims_details
    CREATE INDEX IF NOT EXISTS idx_mvw_reworked_claims_claim_status ON view_mvw_reworked_claims_details("Claim Status");
    CREATE INDEX IF NOT EXISTS idx_mvw_reworked_claims_patient_id ON view_mvw_reworked_claims_details(patient_id);

    -- Indexes for view_mvw_writeoffs_details
    CREATE INDEX IF NOT EXISTS idx_mvw_writeoffs_is_bad_debt ON view_mvw_writeoffs_details(is_bad_debt);
    CREATE INDEX IF NOT EXISTS idx_mvw_writeoffs_created_on ON view_mvw_writeoffs_details("Created On");
    CREATE INDEX IF NOT EXISTS idx_mvw_writeoffs_amount_raw ON view_mvw_writeoffs_details(writeoff_amount_raw);

    -- Indexes for view_mvw_clean_claims_details
    CREATE INDEX IF NOT EXISTS idx_mvw_clean_claims_patient_id ON view_mvw_clean_claims_details(patient_id);

    CREATE INDEX IF NOT EXISTS idx_mvw_writeoffs_bad_debt_date ON view_mvw_writeoffs_details(is_bad_debt, "Created On", writeoff_amount_raw);
END $$;


CREATE OR REPLACE VIEW vw_finance_metrics AS
WITH 
    -- Get fiscal year start month and day from company settings
    company_fy_settings AS (
        SELECT 
            COALESCE(EXTRACT(MONTH FROM fiscal_year_start), 1) AS fy_start_month,
            COALESCE(EXTRACT(DAY FROM fiscal_year_start), 1) AS fy_start_day
        FROM form_company 
        WHERE id = 1
    ),
    
    -- Current fiscal year definition based on company settings
    current_fy AS (
        SELECT 
            -- Calculate when the current fiscal year started
            CASE 
                -- If current date is later in the year than fiscal start date
                WHEN (EXTRACT(MONTH FROM CURRENT_DATE) > fy_start_month) OR 
                     (EXTRACT(MONTH FROM CURRENT_DATE) = fy_start_month AND 
                      EXTRACT(DAY FROM CURRENT_DATE) >= fy_start_day) 
                THEN
                    -- Current fiscal year started this calendar year
                    DATE(EXTRACT(YEAR FROM CURRENT_DATE) || '-' || 
                         fy_start_month || '-' || 
                         fy_start_day)
                ELSE
                    -- Current fiscal year started in previous calendar year
                    DATE((EXTRACT(YEAR FROM CURRENT_DATE) - 1) || '-' || 
                         fy_start_month || '-' || 
                         fy_start_day)
            END AS fy_start_date,
            
            -- Calculate when the current fiscal year ends
            CASE 
                -- If current date is later in the year than fiscal start date
                WHEN (EXTRACT(MONTH FROM CURRENT_DATE) > fy_start_month) OR 
                     (EXTRACT(MONTH FROM CURRENT_DATE) = fy_start_month AND 
                      EXTRACT(DAY FROM CURRENT_DATE) >= fy_start_day) 
                THEN
                    -- Current fiscal year ends next calendar year
                    DATE((EXTRACT(YEAR FROM CURRENT_DATE) + 1) || '-' || 
                         fy_start_month || '-' || 
                         fy_start_day) - INTERVAL '1 day'
                ELSE
                    -- Current fiscal year ends this calendar year
                    DATE(EXTRACT(YEAR FROM CURRENT_DATE) || '-' || 
                         fy_start_month || '-' || 
                         fy_start_day) - INTERVAL '1 day'
            END AS fy_end_date
        FROM company_fy_settings
    ),
    
    -- Previous fiscal year
    previous_fy AS (
        SELECT 
            fy_start_date - INTERVAL '1 year' AS fy_start_date,
            fy_end_date - INTERVAL '1 year' AS fy_end_date
        FROM current_fy
    ),
    
    -- Current YTD (Year-to-Date within fiscal year)
    current_fytd AS (
        SELECT
            current_fy.fy_start_date,
            CURRENT_DATE AS current_date,
            -- Calculate days elapsed in current fiscal year
            (CURRENT_DATE - current_fy.fy_start_date) AS days_elapsed
        FROM current_fy
    ),
    
    -- Previous FYTD with equivalent date range
    previous_fytd AS (
        SELECT
            previous_fy.fy_start_date,
            -- Explicitly convert days to an interval
            (previous_fy.fy_start_date + (cf.days_elapsed * INTERVAL '1 day')) AS equivalent_date
        FROM previous_fy, current_fytd cf
    ),
    
    -- Current month to date
    current_mtd AS (
        SELECT 
            DATE_TRUNC('month', CURRENT_DATE) AS month_start_date,
            CURRENT_DATE AS current_date,
            EXTRACT(DAY FROM CURRENT_DATE) AS days_elapsed
        FROM current_fy
    ),
    
    -- Previous month with same number of days
    previous_mtd AS (
        SELECT 
            DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 year') AS month_start_date,
            -- Use the same interval multiplication approach
            (DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 year') + 
             ((cm.days_elapsed - 1) * INTERVAL '1 day')) AS equivalent_date
        FROM current_mtd cm
    ),
    
    -- Current quarter to date
    current_qtd AS (
        SELECT
            DATE_TRUNC('quarter', CURRENT_DATE) AS quarter_start_date,
            CURRENT_DATE AS current_date,
            (CURRENT_DATE - DATE_TRUNC('quarter', CURRENT_DATE)) AS days_elapsed
        FROM current_fy
    ),
    
    -- Previous quarter with same number of days
    previous_qtd AS (
        SELECT
            DATE_TRUNC('quarter', CURRENT_DATE - INTERVAL '3 months') AS quarter_start_date,
            (DATE_TRUNC('quarter', CURRENT_DATE - INTERVAL '3 months') + 
             (cq.days_elapsed)) AS equivalent_date
        FROM current_qtd cq
    ),
    
    -- Calculate the revenue for current FYTD using ledger_finance
    revenue_fytd_current AS (
        SELECT COALESCE(SUM(lf.credit), 0) AS total
        FROM form_ledger_finance lf
        CROSS JOIN current_fytd
        WHERE lf.account_type = 'Revenue'
        AND lf.post_datetime >= current_fytd.fy_start_date
        AND lf.post_datetime < (current_fytd.current_date + INTERVAL '1 day')
    ),
    
    -- Calculate the revenue for previous FYTD using ledger_finance
    revenue_fytd_previous AS (
        SELECT COALESCE(SUM(lf.credit), 0) AS total
        FROM form_ledger_finance lf
        CROSS JOIN previous_fytd
        WHERE lf.account_type = 'Revenue'
        AND lf.post_datetime >= previous_fytd.fy_start_date
        AND lf.post_datetime < (previous_fytd.equivalent_date + INTERVAL '1 day')
    ),
    
    -- Calculate the revenue for current month to date using ledger_finance
    revenue_mtd_current AS (
        SELECT COALESCE(SUM(lf.credit), 0) AS total
        FROM form_ledger_finance lf
        CROSS JOIN current_mtd
        WHERE lf.account_type = 'Revenue'
        AND lf.post_datetime >= current_mtd.month_start_date
        AND lf.post_datetime < (current_mtd.current_date + INTERVAL '1 day')
    ),
    
    -- Calculate the revenue for same period in previous year's equivalent month
    revenue_mtd_previous AS (
        SELECT COALESCE(SUM(lf.credit), 0) AS total
        FROM form_ledger_finance lf
        CROSS JOIN previous_mtd
        WHERE lf.account_type = 'Revenue'
        AND lf.post_datetime >= previous_mtd.month_start_date
        AND lf.post_datetime < (previous_mtd.equivalent_date + INTERVAL '1 day')
    ),
    
    -- Calculate revenue for current QTD using ledger_finance
    revenue_qtd_current AS (
        SELECT COALESCE(SUM(lf.credit), 0) AS total
        FROM form_ledger_finance lf
        CROSS JOIN current_qtd
        WHERE lf.account_type = 'Revenue'
        AND lf.post_datetime >= current_qtd.quarter_start_date
        AND lf.post_datetime < (current_qtd.current_date + INTERVAL '1 day')
    ),
    
    -- Calculate revenue for previous QTD using ledger_finance
    revenue_qtd_previous AS (
        SELECT COALESCE(SUM(lf.credit), 0) AS total
        FROM form_ledger_finance lf
        CROSS JOIN previous_qtd
        WHERE lf.account_type = 'Revenue'
        AND lf.post_datetime >= previous_qtd.quarter_start_date
        AND lf.post_datetime < (previous_qtd.equivalent_date + INTERVAL '1 day')
    ),
    
    -- Calculate payment collections for current QTD using ledger_finance
    collections_qtd_current AS (
        SELECT COALESCE(SUM(lf.debit), 0) AS total
        FROM form_ledger_finance lf
        CROSS JOIN current_qtd
        WHERE lf.account_type = 'Cash'
        AND lf.post_datetime >= current_qtd.quarter_start_date
        AND lf.post_datetime < (current_qtd.current_date + INTERVAL '1 day')
    ),
    
    -- Calculate payment collections for previous QTD using ledger_finance
    collections_qtd_previous AS (
        SELECT COALESCE(SUM(lf.debit), 0) AS total
        FROM form_ledger_finance lf
        CROSS JOIN previous_qtd
        WHERE lf.account_type = 'Cash'
        AND lf.post_datetime >= previous_qtd.quarter_start_date
        AND lf.post_datetime < (previous_qtd.equivalent_date + INTERVAL '1 day')
    ),
    
    -- Calculate payment collections for current FYTD using ledger_finance
    collections_fytd_current AS (
        SELECT COALESCE(SUM(lf.debit), 0) AS total
        FROM form_ledger_finance lf
        CROSS JOIN current_fytd
        WHERE lf.account_type = 'Cash'
        AND lf.post_datetime >= current_fytd.fy_start_date
        AND lf.post_datetime < (current_fytd.current_date + INTERVAL '1 day')
    ),
    
    -- Calculate payment collections for previous FYTD using ledger_finance
    collections_fytd_previous AS (
        SELECT COALESCE(SUM(lf.debit), 0) AS total
        FROM form_ledger_finance lf
        CROSS JOIN previous_fytd
        WHERE lf.account_type = 'Cash'
        AND lf.post_datetime >= previous_fytd.fy_start_date
        AND lf.post_datetime < (previous_fytd.equivalent_date + INTERVAL '1 day')
    ),
    
    -- Calculate bad debt for current QTD from ledger_finance
    bad_debt_qtd_current AS (
        SELECT COALESCE(SUM(
            CASE WHEN lf.transaction_type = 'Writeoff' THEN
                (COALESCE(lf.credit, 0) - COALESCE(lf.debit, 0))
            ELSE 0 END
        ), 0) AS total
        FROM form_ledger_finance lf
        JOIN form_list_writeoff_reason wor ON wor.code = lf.writeoff_reason_id
        CROSS JOIN current_qtd
        WHERE wor.bad_debt = 'Yes'
        AND lf.post_datetime >= current_qtd.quarter_start_date
        AND lf.post_datetime < (current_qtd.current_date + INTERVAL '1 day')
    ),
    
    -- Calculate bad debt for previous QTD from ledger_finance
    bad_debt_qtd_previous AS (
        SELECT COALESCE(SUM(
            CASE WHEN lf.transaction_type = 'Writeoff' THEN
                (COALESCE(lf.credit, 0) - COALESCE(lf.debit, 0))
            ELSE 0 END
        ), 0) AS total
        FROM form_ledger_finance lf
        JOIN form_list_writeoff_reason wor ON wor.code = lf.writeoff_reason_id
        CROSS JOIN previous_qtd
        WHERE wor.bad_debt = 'Yes'
        AND lf.post_datetime >= previous_qtd.quarter_start_date
        AND lf.post_datetime < (previous_qtd.equivalent_date + INTERVAL '1 day')
    ),
    
    -- Calculate bad debt for current FYTD from ledger_finance
    bad_debt_fytd_current AS (
        SELECT COALESCE(SUM(
            CASE WHEN lf.transaction_type = 'Writeoff' THEN
                (COALESCE(lf.credit, 0) - COALESCE(lf.debit, 0))
            ELSE 0 END
        ), 0) AS total
        FROM form_ledger_finance lf
        JOIN form_list_writeoff_reason wor ON wor.code = lf.writeoff_reason_id
        CROSS JOIN current_fytd
        WHERE wor.bad_debt = 'Yes'
        AND lf.post_datetime >= current_fytd.fy_start_date
        AND lf.post_datetime < (current_fytd.current_date + INTERVAL '1 day')
    ),
    
    -- Calculate bad debt for previous FYTD from ledger_finance
    bad_debt_fytd_previous AS (
        SELECT COALESCE(SUM(
            CASE WHEN lf.transaction_type = 'Writeoff' THEN
                (COALESCE(lf.credit, 0) - COALESCE(lf.debit, 0))
            ELSE 0 END
        ), 0) AS total
        FROM form_ledger_finance lf
        JOIN form_list_writeoff_reason wor ON wor.code = lf.writeoff_reason_id
        CROSS JOIN previous_fytd
        WHERE wor.bad_debt = 'Yes'
        AND lf.post_datetime >= previous_fytd.fy_start_date
        AND lf.post_datetime < (previous_fytd.equivalent_date + INTERVAL '1 day')
    ),
    
    -- Calculate pending revenue
    pending_revenue AS (
        SELECT COALESCE(SUM(bi.total_expected), 0) AS total
        FROM form_billing_invoice bi
        WHERE bi.archived IS NOT TRUE 
        AND bi.deleted IS NOT TRUE
        AND COALESCE(bi.void, 'No') <> 'Yes'
        AND COALESCE(bi.revenue_accepted_posted, 'No') <> 'Yes'
        AND COALESCE(bi.revenue_accepted, 'No') = 'Yes'
    ),
    
    -- Calculate total claims in current quarter
    total_claims_qtd AS (
        SELECT COUNT(*) AS total
        FROM vw_master_claims_cube bi
        CROSS JOIN current_qtd
        WHERE bi.billed_datetime >= current_qtd.quarter_start_date
        AND bi.billed_datetime < (current_qtd.current_date + INTERVAL '1 day')
    ),
    
    -- Calculate total claims in previous quarter
    total_claims_prev_qtd AS (
        SELECT COUNT(*) AS total
        FROM vw_master_claims_cube bi
        CROSS JOIN previous_qtd
        WHERE bi.billed_datetime >= previous_qtd.quarter_start_date
        AND bi.billed_datetime < (previous_qtd.equivalent_date + INTERVAL '1 day')
    ),
    
    -- Calculate final denied claims in current quarter
    final_denied_claims_qtd AS (
        SELECT COUNT(*) AS total
        FROM view_mvw_denied_claims_details bi
        CROSS JOIN current_qtd
        WHERE bi.billed_datetime >= current_qtd.quarter_start_date
        AND bi.billed_datetime < (current_qtd.current_date + INTERVAL '1 day')
    ),
        
    -- Calculate final denied claims in previous quarter
    final_denied_claims_prev_qtd AS (
        SELECT COUNT(*) AS total
        FROM view_mvw_denied_claims_details bi
        CROSS JOIN previous_qtd
        WHERE bi.billed_datetime >= previous_qtd.quarter_start_date
        AND bi.billed_datetime < (previous_qtd.equivalent_date + INTERVAL '1 day')
    ),
    
    -- Calculate initially denied claims in current quarter
    initial_denied_claims_qtd AS (
        SELECT COUNT(*) AS total
        FROM view_mvw_initial_denied_claims_details bi
        CROSS JOIN current_qtd
        WHERE bi.billed_datetime >= current_qtd.quarter_start_date
        AND bi.billed_datetime < (current_qtd.current_date + INTERVAL '1 day')

    ),
    
    -- Calculate initially denied claims in previous quarter
    initial_denied_claims_prev_qtd AS (
        SELECT COUNT(*) AS total
        FROM view_mvw_initial_denied_claims_details bi
        CROSS JOIN previous_qtd
        WHERE bi.billed_datetime >= previous_qtd.quarter_start_date
        AND bi.billed_datetime < (previous_qtd.equivalent_date + INTERVAL '1 day')
    ),
    -- Calculate reworked claims in current quarter
    reworked_claims_qtd AS (
        SELECT COUNT(*) AS total
        FROM view_mvw_reworked_claims_details bi
        CROSS JOIN current_qtd
        WHERE bi.billed_datetime >= current_qtd.quarter_start_date
        AND bi.billed_datetime < (current_qtd.current_date + INTERVAL '1 day')
    ),

    -- Calculate reworked claims in previous quarter
    reworked_claims_prev_qtd AS (
        SELECT COUNT(*) AS total
        FROM view_mvw_reworked_claims_details bi
        CROSS JOIN previous_qtd
        WHERE bi.billed_datetime >= previous_qtd.quarter_start_date
        AND bi.billed_datetime < (previous_qtd.equivalent_date + INTERVAL '1 day')
    ),

    -- Calculate clean claims in current quarter
    clean_claims_qtd AS (
        SELECT COUNT(*) AS total
        FROM view_mvw_clean_claims_details bi
        CROSS JOIN current_qtd
        WHERE bi.billed_datetime >= current_qtd.quarter_start_date
        AND bi.billed_datetime < (current_qtd.current_date + INTERVAL '1 day')
    ),

    -- Calculate clean claims in previous quarter
    clean_claims_prev_qtd AS (
        SELECT COUNT(*) AS total
        FROM view_mvw_clean_claims_details bi
        CROSS JOIN previous_qtd
        WHERE bi.billed_datetime >= previous_qtd.quarter_start_date
        AND bi.billed_datetime < (previous_qtd.equivalent_date + INTERVAL '1 day')
    ),

-- Combine all metrics
metrics_data AS (
    -- Total Revenue FYTD (Fiscal Year to Date)
    SELECT 
        'tot_revenue_fy' AS measure_key,
        'Total Revenue FYTD' AS name,
        'finance_ledger_details' AS details_view_query,
        'card' AS type,
        cur.total AS amount_raw,
        CASE 
            WHEN prev.total > 0 THEN 
                ROUND(((cur.total - prev.total) / prev.total * 100)::numeric, 2)
            ELSE NULL
        END AS previous_amount_perc_raw
    FROM revenue_fytd_current cur
    CROSS JOIN revenue_fytd_previous prev
    
    UNION ALL
    
    -- Revenue MTD compared to same period last year
    SELECT 
        'revenue_mtd' AS measure_key,
        'Revenue MTD' AS name,
        'finance_ledger_details' AS details_view_query,
        'card' AS type,
        cur.total AS amount_raw,
        CASE 
            WHEN prev.total > 0 THEN 
                ROUND(((cur.total - prev.total) / prev.total * 100)::numeric, 2)
            ELSE NULL
        END AS previous_amount_perc_raw
    FROM revenue_mtd_current cur
    CROSS JOIN revenue_mtd_previous prev
    
    UNION ALL
    
    -- Pending Revenue
    SELECT 
        'pending_revenue' AS measure_key,
        'Pending Revenue' AS name,
        'pending_revenue_drill_down' AS details_view_query,
        'card' AS type,
        total AS amount_raw,
        NULL AS previous_amount_perc_raw
    FROM pending_revenue
    
    UNION ALL 
    
    -- Final Denial Rate QTD
    SELECT 
        'denials_rate_qtd_final' AS measure_key,
        'Final Denial Rate QTD' AS name,
        'denied_claims_drill_down' AS details_view_query,
        'card' AS type,
        CASE 
            WHEN tc.total > 0 THEN 
                ROUND((fdc.total::numeric * 100.0 / tc.total)::numeric, 2)
            ELSE 0 
        END AS amount_raw,
        CASE 
            WHEN tc_prev.total > 0 AND fdc_prev.total > 0 THEN 
                ROUND(((fdc_prev.total::numeric * 100.0 / tc_prev.total))::numeric, 2)
            ELSE NULL 
        END AS previous_amount_perc_raw
    FROM total_claims_qtd tc
    CROSS JOIN final_denied_claims_qtd fdc
    CROSS JOIN total_claims_prev_qtd tc_prev
    CROSS JOIN final_denied_claims_prev_qtd fdc_prev
    
    UNION ALL
    
    -- Initial Denial Rate QTD
    SELECT 
        'denials_rate_qtd_initial' AS measure_key,
        'Initial Denial Rate QTD' AS name,
        'initial_claims_denied_drilldown' AS details_view_query,
        'card' AS type,
        CASE 
            WHEN tc.total > 0 THEN 
                ROUND((idc.total::numeric * 100.0 / tc.total)::numeric, 2)
            ELSE 0 
        END AS amount_raw,
        CASE 
            WHEN tc_prev.total > 0 AND idc_prev.total > 0 THEN 
                ROUND(((idc_prev.total::numeric * 100.0 / tc_prev.total))::numeric, 2)
            ELSE NULL 
        END AS previous_amount_perc_raw
    FROM total_claims_qtd tc
    CROSS JOIN initial_denied_claims_qtd idc
    CROSS JOIN total_claims_prev_qtd tc_prev
    CROSS JOIN initial_denied_claims_prev_qtd idc_prev
    
    UNION ALL
    
    -- Reworked Claims Rate QTD
    SELECT 
        'denials_rate_qtd_reworked' AS measure_key,
        'Reworked Claims Rate QTD' AS name,
        'reworked_claims_drilldown' AS details_view_query,
        'card' AS type,
        CASE 
            WHEN idc.total > 0 THEN 
                ROUND((rc.total::numeric * 100.0 / idc.total)::numeric, 2)
            ELSE 0 
        END AS amount_raw,
        CASE 
            WHEN idc_prev.total > 0 AND rc_prev.total > 0 THEN 
                ROUND(((rc_prev.total::numeric * 100.0 / idc_prev.total))::numeric, 2)
            ELSE NULL 
        END AS previous_amount_perc_raw
    FROM initial_denied_claims_qtd idc
    CROSS JOIN reworked_claims_qtd rc
    CROSS JOIN initial_denied_claims_prev_qtd idc_prev
    CROSS JOIN reworked_claims_prev_qtd rc_prev
    
    UNION ALL
    
    -- Clean Claims QTD
    SELECT 
        'clean_claims_qtd' AS measure_key,
        'Clean Claims QTD' AS name,
        'clean_claims_drilldown' AS details_view_query,
        'card' AS type,
        CASE 
            WHEN tc.total > 0 THEN 
                ROUND(((COALESCE(cc.total::numeric,0.0)::numeric * 100.0 / tc.total))::numeric, 2)
            ELSE 0 
        END AS amount_raw,
        CASE
            WHEN tc_prev.total > 0 AND cc_prev.total > 0 THEN 
                ROUND(((cc_prev.total::numeric * 100.0 / tc_prev.total))::numeric, 2)
            ELSE NULL 
        END AS previous_amount_perc_raw
    FROM total_claims_qtd tc
    CROSS JOIN clean_claims_qtd cc
    CROSS JOIN total_claims_prev_qtd tc_prev
    CROSS JOIN clean_claims_prev_qtd cc_prev
    
    UNION ALL
    
    -- Net Collections Ratio QTD
    SELECT 
        'net_collections_qtd' AS measure_key,
        'Net Collections Ratio QTD' AS name,
        'finance_ledger_details' AS details_view_query,
        'card' AS type,
        CASE 
            WHEN rev.total > 0 THEN 
                ROUND((coll.total::numeric * 100.0 / rev.total)::numeric, 2)
            ELSE 0 
        END AS amount_raw,
        CASE 
            WHEN rev_prev.total > 0 AND coll_prev.total > 0 THEN 
                ROUND(((coll_prev.total::numeric * 100.0 / rev_prev.total))::numeric, 2)
            ELSE NULL 
        END AS previous_amount_perc_raw
    FROM revenue_qtd_current rev
    CROSS JOIN collections_qtd_current coll
    CROSS JOIN revenue_qtd_previous rev_prev
    CROSS JOIN collections_qtd_previous coll_prev
    
    UNION ALL
    
    -- Net Collections Ratio FY
    SELECT 
        'net_collections_fy' AS measure_key,
        'Net Collections Ratio FY' AS name,
        'finance_ledger_details' AS details_view_query,
        'card' AS type,
        CASE 
            WHEN rev.total > 0 THEN 
                ROUND((coll.total::numeric * 100.0 / rev.total)::numeric, 2)
            ELSE 0 
        END AS amount_raw,
        CASE 
            WHEN rev_prev.total > 0 AND coll_prev.total > 0 THEN 
                ROUND(((coll_prev.total::numeric * 100.0 / rev_prev.total))::numeric, 2)
            ELSE NULL 
        END AS previous_amount_perc_raw
    FROM revenue_fytd_current rev
    CROSS JOIN collections_fytd_current coll
    CROSS JOIN revenue_fytd_previous rev_prev
    CROSS JOIN collections_fytd_previous coll_prev
    
    UNION ALL
    
    -- Bad Debt QTD
    SELECT 
        'bad_debit_qtd' AS measure_key,
        'Bad Debt QTD' AS name,
        'writeoffs_report' AS details_view_query,
        'card' AS type,
        CASE 
            WHEN rev.total > 0 THEN 
                ROUND((bd.total::numeric * 100.0 / rev.total)::numeric, 2)
            ELSE 0 
        END AS amount_raw,
        CASE 
            WHEN rev_prev.total > 0 AND bd_prev.total > 0 THEN 
                ROUND(((bd_prev.total::numeric * 100.0 / rev_prev.total))::numeric, 2)
            ELSE NULL 
        END AS previous_amount_perc_raw
    FROM revenue_qtd_current rev
    CROSS JOIN bad_debt_qtd_current bd
    CROSS JOIN revenue_qtd_previous rev_prev
    CROSS JOIN bad_debt_qtd_previous bd_prev
    
    UNION ALL
    
    -- Bad Debt FY
    SELECT 
        'bad_debit_fy' AS measure_key,
        'Bad Debt FY' AS name,
        'writeoffs_report' AS details_view_query,
        'card' AS type,
        CASE 
            WHEN rev.total > 0 THEN 
                ROUND((bd.total::numeric * 100.0 / rev.total)::numeric, 2)
            ELSE 0 
        END AS amount_raw,
        CASE 
            WHEN rev_prev.total > 0 AND bd_prev.total > 0 THEN 
                ROUND(((bd_prev.total::numeric * 100.0 / rev_prev.total))::numeric, 2)
            ELSE NULL 
        END AS previous_amount_perc_raw
    FROM revenue_fytd_current rev
    CROSS JOIN bad_debt_fytd_current bd
    CROSS JOIN revenue_fytd_previous rev_prev
    CROSS JOIN bad_debt_fytd_previous bd_prev
    
    UNION ALL
    
    -- Pending Pharmacy Claims (Pie Chart)
    SELECT 
        'pending_pharmacy_claims' AS measure_key,
        'Pending Pharmacy Claims' AS name,
        'pending_revenue_drill_down' AS details_view_query,
        'pie_chart' AS type,
        0 AS amount_raw,
        NULL AS previous_amount_perc_raw
        
    UNION ALL
    
    -- Pending Medical Claims (Pie Chart)
    SELECT 
        'pending_medical_claims' AS measure_key,
        'Pending Medical Claims' AS name,
        'pending_revenue_drill_down' AS details_view_query,
        'pie_chart' AS type,
        0 AS amount_raw,
        NULL AS previous_amount_perc_raw
)
SELECT
    measure_key,
    name,
    details_view_query,
    type,
    amount_raw,
    CASE
        WHEN measure_key LIKE 'denials_rate%' OR 
             measure_key LIKE 'clean_claims%' OR 
             measure_key LIKE 'net_collections%' OR 
             measure_key LIKE 'bad_debit%' THEN 
            CONCAT(format_numeric(amount_raw::numeric), '%')
        WHEN measure_key LIKE 'pending_%claims' THEN
            NULL -- No amount format for pie charts
        ELSE
            format_currency(amount_raw::numeric)
    END AS amount,
    previous_amount_perc_raw,
    CASE 
        WHEN previous_amount_perc_raw IS NOT NULL THEN
            CONCAT(format_numeric(previous_amount_perc_raw::numeric), '%')
        ELSE NULL 
    END AS previous_amount_perc,
    CASE
        WHEN measure_key = 'pending_pharmacy_claims' THEN
            (SELECT jsonb_agg(
                jsonb_build_object(
                    'description', description,
                    'value', value,
                    'color', color
                )
            )
            FROM view_mvw_pending_pharmacy_claims)
        WHEN measure_key = 'pending_medical_claims' THEN
            (SELECT jsonb_agg(
                jsonb_build_object(
                    'description', description,
                    'value', value,
                    'color', color
                )
            )
            FROM view_mvw_pending_medical_claims)
        ELSE NULL
    END AS data
FROM metrics_data
ORDER BY
    CASE measure_key 
        -- Revenue metrics
        WHEN 'tot_revenue_fy' THEN 1
        WHEN 'revenue_mtd' THEN 2
        WHEN 'pending_revenue' THEN 3
        
        -- Denial rate metrics
        WHEN 'denials_rate_qtd_final' THEN 4
        WHEN 'denials_rate_qtd_initial' THEN 5
        WHEN 'denials_rate_qtd_reworked' THEN 6
        WHEN 'clean_claims_qtd' THEN 7
        
        -- Collections and bad debt metrics
        WHEN 'net_collections_qtd' THEN 8
        WHEN 'net_collections_fy' THEN 9
        WHEN 'bad_debit_qtd' THEN 10
        WHEN 'bad_debit_fy' THEN 11
        
        -- Pie charts
        WHEN 'pending_pharmacy_claims' THEN 12
        WHEN 'pending_medical_claims' THEN 13
        
        -- Default
        ELSE 999
    END;

CREATE OR REPLACE VIEW vw_finance_dashboard AS
SELECT
    m.measure_key,
    m.name,
    m.amount,
    m.previous_amount_perc,
    CASE
        -- Revenue metrics (higher is better)
        WHEN m.previous_amount_perc_raw > 0 AND 
             (m.measure_key LIKE 'revenue%' OR m.measure_key = 'tot_revenue_fy') THEN 'positive'
        WHEN m.previous_amount_perc_raw < 0 AND 
             (m.measure_key LIKE 'revenue%' OR m.measure_key = 'tot_revenue_fy') THEN 'negative'
             
        -- Denial rate metrics (lower is better)
        WHEN m.previous_amount_perc_raw > 0 AND 
             (m.measure_key IN ('denials_rate_qtd_final', 'denials_rate_qtd_initial', 'bad_debit_qtd', 'bad_debit_fy')) THEN 'negative'
        WHEN m.previous_amount_perc_raw < 0 AND 
             (m.measure_key IN ('denials_rate_qtd_final', 'denials_rate_qtd_initial', 'bad_debit_qtd', 'bad_debit_fy')) THEN 'positive'
             
        -- Reworked claims and clean claims metrics (higher is better)
        WHEN m.previous_amount_perc_raw > 0 AND 
             (m.measure_key IN ('denials_rate_qtd_reworked', 'clean_claims_qtd', 'net_collections_qtd', 'net_collections_fy')) THEN 'positive'
        WHEN m.previous_amount_perc_raw < 0 AND 
             (m.measure_key IN ('denials_rate_qtd_reworked', 'clean_claims_qtd', 'net_collections_qtd', 'net_collections_fy')) THEN 'negative'
             
        -- Default case
        ELSE 'neutral'
    END AS trend_direction,
    m.details_view_query,
    m.type,
    m.data,
    -- Updated drill-down parameters to use correct views
    CASE
        WHEN m.measure_key = 'tot_revenue_fy' THEN
            jsonb_build_object(
                '1', (SELECT fy_start_date::text FROM (
                    SELECT 
                        -- Calculate fiscal year start date using company settings
                        CASE 
                            WHEN (EXTRACT(MONTH FROM CURRENT_DATE) > COALESCE(EXTRACT(MONTH FROM fiscal_year_start), 1)) OR 
                                 (EXTRACT(MONTH FROM CURRENT_DATE) = COALESCE(EXTRACT(MONTH FROM fiscal_year_start), 1) AND 
                                  EXTRACT(DAY FROM CURRENT_DATE) >= COALESCE(EXTRACT(DAY FROM fiscal_year_start), 1)) 
                            THEN
                                DATE(EXTRACT(YEAR FROM CURRENT_DATE) || '-' || 
                                     COALESCE(EXTRACT(MONTH FROM fiscal_year_start), 1) || '-' || 
                                     COALESCE(EXTRACT(DAY FROM fiscal_year_start), 1))
                            ELSE
                                DATE((EXTRACT(YEAR FROM CURRENT_DATE) - 1) || '-' || 
                                     COALESCE(EXTRACT(MONTH FROM fiscal_year_start), 1) || '-' || 
                                     COALESCE(EXTRACT(DAY FROM fiscal_year_start), 1))
                        END AS fy_start_date
                    FROM form_company 
                    WHERE id = 1
                ) AS fy),
                '2', CURRENT_DATE::text
            )
        WHEN m.measure_key = 'revenue_mtd' THEN
            jsonb_build_object(
                '1', DATE_TRUNC('month', CURRENT_DATE)::text,
                '2', CURRENT_DATE::text
            )
        WHEN m.measure_key = 'pending_revenue' THEN
            jsonb_build_object(
                '1', 'all',
                '2', (SELECT fy_start_date::text FROM (
                    SELECT 
                        -- Calculate fiscal year start date using company settings
                        CASE 
                            WHEN (EXTRACT(MONTH FROM CURRENT_DATE) > COALESCE(EXTRACT(MONTH FROM fiscal_year_start), 1)) OR 
                                 (EXTRACT(MONTH FROM CURRENT_DATE) = COALESCE(EXTRACT(MONTH FROM fiscal_year_start), 1) AND 
                                  EXTRACT(DAY FROM CURRENT_DATE) >= COALESCE(EXTRACT(DAY FROM fiscal_year_start), 1)) 
                            THEN
                                DATE(EXTRACT(YEAR FROM CURRENT_DATE) || '-' || 
                                     COALESCE(EXTRACT(MONTH FROM fiscal_year_start), 1) || '-' || 
                                     COALESCE(EXTRACT(DAY FROM fiscal_year_start), 1))
                            ELSE
                                DATE((EXTRACT(YEAR FROM CURRENT_DATE) - 1) || '-' || 
                                     COALESCE(EXTRACT(MONTH FROM fiscal_year_start), 1) || '-' || 
                                     COALESCE(EXTRACT(DAY FROM fiscal_year_start), 1))
                        END AS fy_start_date
                    FROM form_company 
                    WHERE id = 1
                ) AS fy),
                '3', CURRENT_DATE::text
            )
        WHEN m.measure_key = 'denials_rate_qtd_final' THEN
            jsonb_build_object(
                '1', DATE_TRUNC('quarter', CURRENT_DATE)::text,
                '2', CURRENT_DATE::text,
                '3', 'denied_claims_drill_down' -- Updated view name
            )
        WHEN m.measure_key = 'denials_rate_qtd_initial' THEN
            jsonb_build_object(
                '1', DATE_TRUNC('quarter', CURRENT_DATE)::text,
                '2', CURRENT_DATE::text,
                '3', 'initial_claims_denied_drilldown' -- Updated view name
            )
        WHEN m.measure_key = 'denials_rate_qtd_reworked' THEN
            jsonb_build_object(
                '1', DATE_TRUNC('quarter', CURRENT_DATE)::text,
                '2', CURRENT_DATE::text,
                '3', 'reworked_claims_drilldown' -- Updated view name
            )
        WHEN m.measure_key = 'clean_claims_qtd' THEN
            jsonb_build_object(
                '1', DATE_TRUNC('quarter', CURRENT_DATE)::text,
                '2', CURRENT_DATE::text,
                '3', 'clean_claims_drilldown' -- Updated view name
            )
        WHEN m.measure_key IN ('net_collections_qtd', 'bad_debit_qtd') THEN
            jsonb_build_object(
                '1', DATE_TRUNC('quarter', CURRENT_DATE)::text,
                '2', CURRENT_DATE::text,
                '3', CASE 
                      WHEN m.measure_key = 'bad_debit_qtd' THEN 'writeoffs_report'
                      ELSE 'finance_ledger_details'
                   END -- Updated view names
            )
        WHEN m.measure_key IN ('net_collections_fy', 'bad_debit_fy') THEN
            jsonb_build_object(
                '1', (SELECT fy_start_date::text FROM (
                    SELECT 
                        CASE 
                            WHEN (EXTRACT(MONTH FROM CURRENT_DATE) > COALESCE(EXTRACT(MONTH FROM fiscal_year_start), 1)) OR 
                                 (EXTRACT(MONTH FROM CURRENT_DATE) = COALESCE(EXTRACT(MONTH FROM fiscal_year_start), 1) AND 
                                  EXTRACT(DAY FROM CURRENT_DATE) >= COALESCE(EXTRACT(DAY FROM fiscal_year_start), 1)) 
                            THEN
                                DATE(EXTRACT(YEAR FROM CURRENT_DATE) || '-' || 
                                     COALESCE(EXTRACT(MONTH FROM fiscal_year_start), 1) || '-' || 
                                     COALESCE(EXTRACT(DAY FROM fiscal_year_start), 1))
                            ELSE
                                DATE((EXTRACT(YEAR FROM CURRENT_DATE) - 1) || '-' || 
                                     COALESCE(EXTRACT(MONTH FROM fiscal_year_start), 1) || '-' || 
                                     COALESCE(EXTRACT(DAY FROM fiscal_year_start), 1))
                        END AS fy_start_date
                    FROM form_company 
                    WHERE id = 1
                ) AS fy),
                '2', CURRENT_DATE::text,
                '3', CASE 
                      WHEN m.measure_key = 'bad_debit_fy' THEN 'writeoffs_report'
                      ELSE 'finance_ledger_details'
                   END -- Updated view names
            )
        WHEN m.measure_key LIKE 'pending_pharmacy_claims' THEN
            jsonb_build_object(
                '1', 'pharmacy',
                '2', '1/1/1999',
                '3', '1/1/2099'
            )
       WHEN m.measure_key LIKE 'pending_medical_claims' THEN
            jsonb_build_object(
                '1', 'medical',
                '2', '1/1/1999',
                '3', '1/1/2999'
            )
        ELSE NULL
    END AS drill_down_params
FROM
    vw_finance_metrics m
ORDER BY
    CASE m.measure_key 
        -- Revenue metrics
        WHEN 'tot_revenue_fy' THEN 1
        WHEN 'revenue_mtd' THEN 2
        WHEN 'pending_revenue' THEN 3
        
        -- Denial rate metrics
        WHEN 'denials_rate_qtd_final' THEN 4
        WHEN 'denials_rate_qtd_initial' THEN 5
        WHEN 'denials_rate_qtd_reworked' THEN 6
        WHEN 'clean_claims_qtd' THEN 7
        
        -- Collections and bad debt metrics
        WHEN 'net_collections_qtd' THEN 8
        WHEN 'net_collections_fy' THEN 9
        WHEN 'bad_debit_qtd' THEN 10
        WHEN 'bad_debit_fy' THEN 11
        
        -- Pie charts
        WHEN 'pending_pharmacy_claims' THEN 12
        WHEN 'pending_medical_claims' THEN 13
        
        -- Default
        ELSE 999
    END;