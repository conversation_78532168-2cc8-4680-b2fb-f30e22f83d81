
DO $$ BEGIN
  PERFORM drop_all_function_signatures('handle_ncpdp_compound_ingredients');
END $$;
CREATE OR REPLACE FUNCTION handle_ncpdp_compound_ingredients(
  p_ncpdp_id integer,
  p_charge_line record,
  p_treat_as_compound boolean,
  p_is_real_compound boolean
) RETURNS void AS $$
DECLARE
  v_compound_id integer;
BEGIN
  -- First, attempt to find an existing compound record
  SELECT id INTO v_compound_id
  FROM form_ncpdp_compound
  WHERE charge_no = p_charge_line.charge_no
  LIMIT 1;

  -- Insert or update compound ingredient without using ON CONFLICT
  IF v_compound_id IS NULL THEN
    -- No existing record, do an INSERT
    INSERT INTO form_ncpdp_compound (
      charge_no,
      product_id,
      cmp_ing_qty,
      cmp_ing_cost,
      cmp_ing_cost_basis,
      cmp_id_qualifier,
      cmp_id,
      created_by,
      created_on
    )
    VALUES (
      p_charge_line.charge_no,
      p_charge_line.inventory_id,
      p_charge_line.charge_quantity,
      p_charge_line.billed,
      p_charge_line.cost_basis,
      '03', -- NDC TODO: Need to handle other compound qualifier logic here
      p_charge_line.ndc,
      COALESCE(p_charge_line.updated_by, p_charge_line.created_by),
      COALESCE(p_charge_line.updated_on, p_charge_line.created_on)
    )
    RETURNING id INTO v_compound_id;
  ELSE
    -- Existing record, do an UPDATE
    UPDATE form_ncpdp_compound
    SET
      product_id = p_charge_line.inventory_id,
      cmp_ing_qty = p_charge_line.charge_quantity,
      cmp_ing_cost = p_charge_line.billed,
      cmp_ing_cost_basis = p_charge_line.cost_basis,
      cmp_id_qualifier = '03', -- NDC TODO: Need to handle other compound qualifier logic here
      cmp_id = p_charge_line.ndc,
      updated_by = COALESCE(p_charge_line.updated_by, p_charge_line.created_by),
      updated_on = COALESCE(p_charge_line.updated_on, p_charge_line.created_on),
      archived = FALSE
    WHERE id = v_compound_id;
  END IF;

  -- Check if there's already a link in the gerund table
  IF NOT EXISTS (
    SELECT 1
    FROM sf_form_ncpdp_to_ncpdp_compound
    WHERE form_ncpdp_fk = p_ncpdp_id
    AND form_ncpdp_compound_fk = v_compound_id
  ) THEN
    -- Insert new gerund table link
    INSERT INTO sf_form_ncpdp_to_ncpdp_compound (
      form_ncpdp_fk,
      form_ncpdp_compound_fk,
      archive,
      delete
    )
    VALUES (
      p_ncpdp_id,
      v_compound_id,
      FALSE,
      FALSE
    );
  ELSE
    -- Update existing gerund table link
    UPDATE sf_form_ncpdp_to_ncpdp_compound
    SET archive = FALSE
    WHERE form_ncpdp_fk = p_ncpdp_id
    AND form_ncpdp_compound_fk = v_compound_id;
  END IF;

  -- Handle compound ingredient modifier codes - avoiding CTE for clarity
  INSERT INTO gr_form_ncpdp_compound_cmp_ing_md_code_to_list_ncpdp_ext_ecl_id (
    form_ncpdp_compound_fk,
    form_list_ncpdp_ext_ecl_fk
  )
  SELECT
    v_compound_id,
    grl.form_list_ncpdp_ext_ecl_fk
  FROM gr_form_ledger_charge_line_procm_to_list_ncpdp_ext_ecl_id grl
  WHERE grl.form_ledger_charge_line_fk = p_charge_line.id
  AND NOT EXISTS (
    SELECT 1
    FROM gr_form_ncpdp_compound_cmp_ing_md_code_to_list_ncpdp_ext_ecl_id grnc
    WHERE grnc.form_ncpdp_compound_fk = v_compound_id
    AND grnc.form_list_ncpdp_ext_ecl_fk = grl.form_list_ncpdp_ext_ecl_fk
  );
END;
$$ LANGUAGE plpgsql;

-- Function to update NCPDP claim segment
DO $$ BEGIN
  PERFORM drop_all_function_signatures('update_ncpdp_claim_segment');
END $$;
CREATE OR REPLACE FUNCTION update_ncpdp_claim_segment(
    p_ncpdp_id integer,
    p_charge_line record,
    p_treat_as_compound boolean,
    p_is_real_compound boolean,
    p_qualifier_settings record
) RETURNS void AS $$
BEGIN
    UPDATE form_ncpdp_claim nc
    SET
        quantity_dispensed = p_qualifier_settings.dispense_qty,
        quantity_prescribed = p_qualifier_settings.dispense_qty,
        compound_code = p_qualifier_settings.compound_code,
        prod_svc_id_qualifier = CASE 
            WHEN p_treat_as_compound OR COALESCE(p_charge_line.is_primary_drug_ncpdp, 'No') = 'Yes' OR (NOT p_treat_as_compound AND NOT p_is_real_compound) 
            THEN p_qualifier_settings.prod_svc_id_qualifier
            ELSE prod_svc_id_qualifier
        END,
        prod_svc_id = CASE
            WHEN p_treat_as_compound OR COALESCE(p_charge_line.is_primary_drug_ncpdp, 'No') = 'Yes' OR (NOT p_treat_as_compound AND NOT p_is_real_compound) 
            THEN p_qualifier_settings.prod_svc_id
            ELSE prod_svc_id
        END,
        unit_of_measure = CASE
            WHEN p_treat_as_compound OR COALESCE(p_charge_line.is_primary_drug_ncpdp, 'No') = 'Yes' OR (NOT p_treat_as_compound AND NOT p_is_real_compound) 
            THEN p_qualifier_settings.unit_of_measure
            ELSE unit_of_measure
        END,
        updated_by = COALESCE(p_charge_line.updated_by, p_charge_line.created_by),
        updated_on = COALESCE(p_charge_line.updated_on, p_charge_line.created_on)
    FROM sf_form_ncpdp_to_ncpdp_claim sfnc
    WHERE sfnc.form_ncpdp_fk = p_ncpdp_id 
    AND sfnc.archive IS NOT TRUE
    AND sfnc.delete IS NOT TRUE
    AND sfnc.form_ncpdp_claim_fk = nc.id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Could not find claim segment to update for NCPDP ID: %', p_ncpdp_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Main sync function for real claims (Fixed missing comma in parameter list)
DO $$ BEGIN
  PERFORM drop_all_function_signatures('sync_ledger_to_ncpdp');
END $$;
CREATE OR REPLACE FUNCTION sync_ledger_to_ncpdp()
RETURNS TRIGGER AS $$
DECLARE
    v_ncpdp_id integer;
    v_treat_as_compound boolean;
    v_is_real_compound boolean;
    v_qualifier_settings record;
    v_other_charge_record record;
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    RAISE LOG 'sync_ledger_to_ncpdp_trigger: Syncing ledger lines to claim: %', NEW.invoice_no;

    -- Only proceed if this is an NCPDP claim
    IF NEW.billing_method_id = 'ncpdp' AND NEW.invoice_no IS NOT NULL THEN
    
    
        PERFORM set_config('clara.disable_invoice_trigger', 'on', false);
        PERFORM set_config('clara.prevent_locked_checks', 'on', false);
        IF COALESCE(NEW.is_primary_drug_ncpdp, 'No') = 'Yes' AND COALESCE(OLD.is_primary_drug_ncpdp, 'No') <> 'Yes' THEN
            -- Turn off the primary drug flag for the original charge line
            UPDATE form_ledger_charge_line lcl
            SET is_primary_drug_ncpdp = null::text,
            is_primary_drug = null::text
            WHERE lcl.claim_no = NEW.claim_no
            AND COALESCE(lcl.is_primary_drug_ncpdp, 'No') = 'Yes'
            AND lcl.id <> NEW.id;
        END IF;
        PERFORM set_config('clara.disable_invoice_trigger', 'off', false);
        PERFORM set_config('clara.prevent_locked_checks', 'off', false);

        -- Get compound status
        SELECT * FROM get_ncpdp_compound_status(NEW.invoice_no)
        INTO v_ncpdp_id, v_treat_as_compound, v_is_real_compound;

        IF v_ncpdp_id IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Could not find associated NCPDP claim',
                'Retrieving NCPDP claim in sync_ledger_to_ncpdp',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                jsonb_build_object(
                    'function_name', 'sync_ledger_to_ncpdp',
                    'invoice_no', NEW.invoice_no,
                    'charge_no', NEW.charge_no
                )
            );
            RAISE EXCEPTION 'Could not find associated NCPDP claim for invoice: %', NEW.invoice_no;
        END IF;

        -- Update pricing - Added missing comma
        PERFORM update_ncpdp_pricing(
            v_ncpdp_id, 
            NEW, 
            COALESCE(NEW.is_primary_drug_ncpdp, 'No') = 'Yes' OR (NOT v_treat_as_compound AND NOT v_is_real_compound),
            NEW.invoice_no, 
            NEW.claim_no,
            FALSE
        );

        -- Get insurance qualifier settings
        SELECT * FROM get_ncpdp_insurance_qualifier_settings(NEW, v_treat_as_compound, v_is_real_compound, FALSE)
        INTO v_qualifier_settings;

        -- Update claim segment
        PERFORM update_ncpdp_claim_segment(v_ncpdp_id, NEW, v_treat_as_compound, v_is_real_compound, v_qualifier_settings);

        IF v_treat_as_compound THEN
            UPDATE form_ncpdp ncpdp
            SET comp_disp_unit = '1',
                show_compound = 'Yes'
            WHERE claim_no = NEW.claim_no;
        ELSIF v_is_real_compound THEN
            UPDATE form_ncpdp ncpdp
            SET comp_disp_unit = '2',
                show_compound = 'Yes'
            WHERE claim_no = NEW.claim_no;

            UPDATE form_ncpdp ncpdp
            SET comp_dsg_fm_code = rx.comp_dsg_fm_code
            FROM form_ncpdp_order_rx rx
            WHERE claim_no = NEW.claim_no AND NEW.order_rx_id = rx.id;
        ELSE
            UPDATE form_ncpdp ncpdp
            SET comp_disp_unit = NULL,
                comp_dsg_fm_code = NULL,
                show_compound = NULL
            WHERE claim_no = NEW.claim_no;
        END IF;

        -- Handle compounds if necessary
        IF v_treat_as_compound OR v_is_real_compound THEN
            PERFORM handle_ncpdp_compound_ingredients(v_ncpdp_id, NEW, v_treat_as_compound, v_is_real_compound);
            FOR v_other_charge_record IN 
                SELECT * FROM form_ledger_charge_line
                WHERE invoice_no = NEW.invoice_no
                AND charge_no <> NEW.charge_no
                AND COALESCE(void, 'No') <> 'Yes'
                AND archived IS NOT TRUE
                AND deleted IS NOT TRUE
            LOOP
                PERFORM handle_ncpdp_compound_ingredients(v_ncpdp_id, v_other_charge_record, v_treat_as_compound, v_is_real_compound);
            END LOOP;
        ELSE
            -- Remove any existing compound links
            DELETE FROM sf_form_ncpdp_to_ncpdp_compound
            WHERE form_ncpdp_fk = v_ncpdp_id
            AND archive IS NOT TRUE
            AND delete IS NOT TRUE;

            -- Remove any existing compound ingredients
            DELETE FROM form_ncpdp_compound
            WHERE charge_no = NEW.charge_no 
            OR charge_no IN (
                SELECT charge_no 
                FROM form_ledger_charge_line
                WHERE invoice_no = NEW.invoice_no
                    AND charge_no <> NEW.charge_no
                    AND COALESCE(void, 'No') <> 'Yes'
                    AND archived IS NOT TRUE
                    AND deleted IS NOT TRUE
            )
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Main sync function for test claims
DO $$ BEGIN
  PERFORM drop_all_function_signatures('sync_test_charge_to_ncpdp');
END $$;
CREATE OR REPLACE FUNCTION sync_test_charge_to_ncpdp()
RETURNS TRIGGER AS $$
DECLARE
    v_ncpdp_id integer;
    v_treat_as_compound boolean;
    v_is_real_compound boolean;
    v_old_is_primary_drug_ncpdp text;
    v_qualifier_settings record;
    v_other_charge_record record;
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    RAISE LOG 'sync_test_charge_to_ncpdp_trigger: Syncing test charge lines to claim: %', NEW.claim_no;

    -- Only proceed if this is an NCPDP claim
    IF NEW.billing_method_id = 'ncpdp' AND NEW.claim_no IS NOT NULL THEN
        -- Get compound status

        IF COALESCE(NEW.is_primary_drug_ncpdp, 'No') = 'Yes' AND COALESCE(OLD.is_primary_drug_ncpdp, 'No') <> 'Yes' THEN
            -- Turn off the primary drug flag for the original charge line
            UPDATE form_test_charge_line lcl
            SET is_primary_drug_ncpdp = null
            WHERE lcl.claim_no = NEW.claim_no
            AND COALESCE(lcl.is_primary_drug_ncpdp, 'No') = 'Yes'
            AND lcl.id <> NEW.id;
        END IF;
        SELECT * FROM get_ncpdp_compound_status(NULL, NEW.claim_no, TRUE)
        INTO v_ncpdp_id, v_treat_as_compound, v_is_real_compound;

        IF v_ncpdp_id IS NULL THEN
            RAISE EXCEPTION 'Could not find associated NCPDP claim for test claim: %', NEW.claim_no;
        END IF;

        -- Update pricing
        PERFORM update_ncpdp_pricing(
            v_ncpdp_id, 
            NEW, 
            COALESCE(NEW.is_primary_drug_ncpdp, 'No') = 'Yes' OR (NOT v_treat_as_compound AND NOT v_is_real_compound),
            NULL, 
            NEW.claim_no, 
            TRUE
        );

        -- Get insurance qualifier settings
        SELECT * FROM get_ncpdp_insurance_qualifier_settings(NEW, v_treat_as_compound, v_is_real_compound, TRUE)
        INTO v_qualifier_settings;

        -- Update claim segment
        PERFORM update_ncpdp_claim_segment(v_ncpdp_id, NEW, v_treat_as_compound, v_is_real_compound, v_qualifier_settings);

        IF v_treat_as_compound THEN
            UPDATE form_ncpdp ncpdp
            SET comp_disp_unit = '1',
                show_compound = 'Yes'
            WHERE claim_no = NEW.claim_no;
        ELSIF v_is_real_compound THEN
            UPDATE form_ncpdp ncpdp
            SET comp_disp_unit = '2',
                show_compound = 'Yes'
            WHERE claim_no = NEW.claim_no;

            UPDATE form_ncpdp ncpdp
            SET comp_dsg_fm_code = rx.comp_dsg_fm_code
            FROM form_ncpdp_order_rx rx
            WHERE claim_no = NEW.claim_no AND NEW.order_rx_id = rx.id;
        ELSE
            UPDATE form_ncpdp ncpdp
            SET comp_disp_unit = NULL,
                comp_dsg_fm_code = NULL,
                show_compound = NULL
            WHERE claim_no = NEW.claim_no;
        END IF;

        -- Handle compounds if necessary
        IF v_treat_as_compound OR v_is_real_compound THEN
            PERFORM handle_ncpdp_compound_ingredients(v_ncpdp_id, NEW, v_treat_as_compound, v_is_real_compound);
            FOR v_other_charge_record IN 
                SELECT * FROM form_test_charge_line
                WHERE claim_no = NEW.claim_no
                AND charge_no <> NEW.charge_no
                AND COALESCE(void, 'No') <> 'Yes'
                AND archived IS NOT TRUE
                AND deleted IS NOT TRUE
            LOOP
                PERFORM handle_ncpdp_compound_ingredients(v_ncpdp_id, v_other_charge_record, v_treat_as_compound, v_is_real_compound);
            END LOOP;
        ELSE
            -- Remove any existing compound links
            DELETE FROM sf_form_ncpdp_to_ncpdp_compound
            WHERE form_ncpdp_fk = v_ncpdp_id
            AND archive IS NOT TRUE
            AND delete IS NOT TRUE;

            -- Remove any existing compound ingredients
            DELETE FROM form_ncpdp_compound
            WHERE charge_no = NEW.charge_no 
            OR charge_no IN (
                SELECT charge_no 
                FROM form_test_charge_line
                WHERE claim_no = NEW.claim_no
                    AND charge_no <> NEW.charge_no
                    AND COALESCE(void, 'No') <> 'Yes'
                    AND archived IS NOT TRUE
                    AND deleted IS NOT TRUE
            )
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the triggers
DROP TRIGGER IF EXISTS sync_ledger_to_ncpdp_trigger ON form_ledger_charge_line;
DROP TRIGGER IF EXISTS sync_test_charge_to_ncpdp_trigger ON form_test_charge_line;

CREATE CONSTRAINT TRIGGER sync_ledger_to_ncpdp_trigger
AFTER INSERT OR UPDATE ON form_ledger_charge_line
DEFERRABLE INITIALLY DEFERRED
FOR EACH ROW
WHEN (NEW.billing_method_id = 'ncpdp' AND NEW.invoice_no IS NOT NULL)
EXECUTE FUNCTION sync_ledger_to_ncpdp();

CREATE CONSTRAINT TRIGGER sync_test_charge_to_ncpdp_trigger
AFTER INSERT OR UPDATE ON form_test_charge_line
DEFERRABLE INITIALLY DEFERRED
FOR EACH ROW
WHEN (NEW.billing_method_id = 'ncpdp' AND NEW.claim_no IS NOT NULL)
EXECUTE FUNCTION sync_test_charge_to_ncpdp();