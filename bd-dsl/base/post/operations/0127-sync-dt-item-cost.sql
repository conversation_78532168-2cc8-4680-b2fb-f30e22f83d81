-- Trigger to handle form_ledger_finance entries when account_type = 'COGS'
CREATE OR REPLACE FUNCTION sync_dt_item_cost()
RETURNS TRIGGER AS $$
BEGIN
    BEGIN
        -- Check if the input NEW row is not null
        IF NEW IS NULL THEN
            RAISE EXCEPTION 'New ledger finance entry cannot be null';
        END IF;

        -- Check if account_type is 'COGS'
        IF NEW.account_type = 'COGS' THEN
            -- Update the corresponding form_careplan_dt_item with the cost details
            UPDATE form_careplan_dt_item
            SET total_cost = COALESCE(total_cost, 0) + CAST(NEW.amount AS DECIMAL(10, 2))
            WHERE ticket_no = NEW.ticket_no
            AND ticket_item_no = NEW.ticket_item_no;
        END IF;

        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        -- Raise the error to be handled at a higher level
        RAISE EXCEPTION 'Error in sync_dt_item_cost trigger: %', SQLERRM;
    END;
END;
$$ LANGUAGE plpgsql;

-- Attach the trigger to form_ledger_finance table
CREATE TRIGGER trigger_sync_dt_item_cost
AFTER INSERT OR UPDATE ON form_ledger_finance
FOR EACH ROW
EXECUTE FUNCTION sync_dt_item_cost();
