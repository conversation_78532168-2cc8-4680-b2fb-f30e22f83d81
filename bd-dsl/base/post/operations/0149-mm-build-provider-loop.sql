CREATE OR REPLACE FUNCTION build_mm_provider_loop(
  p_patient_id integer,
  charge_lines charge_line_with_split[]
) RETURNS mm_providers_loop AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_providers_loop;
  v_error_message text;
  v_params jsonb;
  v_site_id integer;
  v_payer_id integer;
  v_physician_id integer;
  v_prescriber_id integer;
  v_physician_count integer;
  v_mm_copy_ordering_md_to_ref boolean;
  v_billing_provider mm_billing_provider_info;
  v_referring_provider mm_dr_provider_info;
  v_ordering_provider mm_dr_provider_info;
  v_rendering_provider mm_rendering_provider_info;
  v_contact_info mm_contact_info;
  v_address_info mm_address_info;
BEGIN
  -- Check for null input parameters
  IF p_patient_id IS NULL THEN
    RAISE EXCEPTION 'Patient ID cannot be NULL';
  END IF;

  IF charge_lines IS NULL OR array_length(charge_lines, 1) = 0 THEN
    RAISE EXCEPTION 'Charge lines cannot be NULL or empty';
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'patient_id', p_patient_id
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
      'build_mm_provider_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM provider loop for patient ID: %', p_patient_id;

    -- Get site_id and payer_id from first charge line
    SELECT 
        cl.site_id,
        cl.payer_id
    INTO 
        v_site_id,
        v_payer_id
    FROM unnest(charge_lines) cl
    WHERE cl.site_id IS NOT NULL AND cl.payer_id IS NOT NULL
    LIMIT 1;

    IF v_site_id IS NULL THEN
        RAISE EXCEPTION 'Site ID not found in charge lines';
    END IF;

    IF v_payer_id IS NULL THEN
        RAISE EXCEPTION 'Payer ID not found in charge lines';
    END IF;

    -- Get unique physician from charge lines
    WITH physician_from_charges AS (
        SELECT DISTINCT vr.physician_id,vr.prescriber_id
        FROM unnest(charge_lines) cl
        INNER JOIN vw_order_raw vr ON vr.rx_no = cl.rx_no
        WHERE vr.physician_id IS NOT NULL
    )
    SELECT 
        physician_id,
        prescriber_id,
        COUNT(*) as physician_count
    INTO 
        v_physician_id,
        v_prescriber_id,
        v_physician_count
    FROM physician_from_charges
    GROUP BY physician_id, prescriber_id
    ORDER BY physician_id
    LIMIT 1;

    -- Check for multiple physicians (should only have one per claim)
    IF v_physician_count > 1 THEN
        RAISE EXCEPTION 'Multiple physicians found on claim - only one physician allowed per claim';
    END IF;

    -- Build billing provider (the pharmacy/site)
    SELECT
        v_site_id::integer,
        'BillingProvider'::text
    INTO 
        v_billing_provider.site_id,
        v_billing_provider.provider_type;
  
    -- Build contact information for billing provider
    SELECT 
        COALESCE(site.mm_contact_name, site.name)::text,
        site.mm_contact_email::text,
        site.phone::text,
        site.fax::text,
        site.name::text,
        site.npi::text,
        CASE 
            WHEN py.mm_send_billing_prov_commercial_number = 'Yes' THEN py.mm_billing_commercial_number 
            ELSE NULL 
        END::text,
        site.state_license::text,
        COALESCE(py.mm_copy_ordering_md_to_ref, 'No') = 'Yes'
    INTO
        v_contact_info.name,
        v_contact_info.email,
        v_contact_info.phone_number,
        v_contact_info.fax_number,
        v_billing_provider.organization_name,
        v_billing_provider.npi,
        v_billing_provider.commercial_number,
        v_billing_provider.state_license_number,
        v_mm_copy_ordering_md_to_ref
    FROM form_site site
    INNER JOIN form_payer py ON py.id = v_payer_id
    AND py.deleted IS NOT TRUE 
    AND py.archived IS NOT TRUE
    WHERE site.id = v_site_id
      AND site.deleted IS NOT TRUE 
      AND site.archived IS NOT TRUE;

    v_billing_provider.contact_information := ARRAY[v_contact_info];

    -- Build address for billing provider
    SELECT 
        site.address1::text,
        site.address2::text,
        site.city::text,
        site.state_id::text,
        site.zip::text
    INTO 
        v_address_info.address1,
        v_address_info.address2,
        v_address_info.city,
        v_address_info.state,
        v_address_info.postal_code
    FROM form_site site
    WHERE site.id = v_site_id
      AND site.deleted IS NOT TRUE 
      AND site.archived IS NOT TRUE;

    v_billing_provider.address := ARRAY[v_address_info];

    -- Build referring/ordering provider (same physician)
    IF v_physician_id IS NOT NULL AND v_mm_copy_ordering_md_to_ref THEN
        -- Get physician information
        SELECT
            p_patient_id::integer,
            v_prescriber_id::integer,
            phys.id::integer,
            'ReferringProvider'::text,
            phys.last::text,
            phys.first::text,
            phys.npi::text,
            NULL::text,
            phys.state_license::text,
            phys.taxonomy_id::text
        INTO 
            v_referring_provider.patient_id,
            v_referring_provider.prescriber_id,
            v_referring_provider.physician_id,
            v_referring_provider.provider_type,
            v_referring_provider.last_name,
            v_referring_provider.first_name,
            v_referring_provider.npi,
            v_referring_provider.commercial_number,
            v_referring_provider.state_license_number,
            v_referring_provider.taxonomy_code
        FROM form_physician phys
        INNER JOIN form_payer py ON py.id = v_payer_id
        AND py.deleted IS NOT TRUE 
        AND py.archived IS NOT TRUE
        WHERE phys.id = v_physician_id
          AND phys.deleted IS NOT TRUE 
          AND phys.archived IS NOT TRUE;

        -- Build physician contact information
        SELECT 
            (phys.first || ' ' || phys.last)::text,
            phys.email::text,
            phys.primary_phone::text,
            phys.primary_fax::text
        INTO 
            v_contact_info.name,
            v_contact_info.email,
            v_contact_info.phone_number,
            v_contact_info.fax_number
        FROM form_physician phys
        WHERE phys.id = v_physician_id
          AND phys.deleted IS NOT TRUE 
          AND phys.archived IS NOT TRUE;

        v_referring_provider.contact_information := ARRAY[v_contact_info];

        -- Build physician address from primary location or first available
        SELECT 
            pl.address1::text,
            pl.address2::text,
            pl.city::text,
            pl.state_id::text,
            pl.zip::text
        INTO 
            v_address_info.address1,
            v_address_info.address2,
            v_address_info.city,
            v_address_info.state,
            v_address_info.postal_code
        FROM form_physician_location pl
        INNER JOIN sf_form_physician_to_physician_location sf ON sf.form_physician_location_fk = pl.id
        WHERE sf.form_physician_fk = v_physician_id
          AND pl.deleted IS NOT TRUE 
          AND pl.archived IS NOT TRUE
          AND sf.delete IS NOT TRUE 
          AND sf.archive IS NOT TRUE
        ORDER BY 
            CASE WHEN pl.is_primary = 'Yes' THEN 0 ELSE 1 END,
            pl.created_on DESC
        LIMIT 1;

        v_referring_provider.address := ARRAY[v_address_info];

        -- Copy referring provider to ordering provider with different type
        v_ordering_provider := v_referring_provider;
        v_ordering_provider.provider_type := 'OrderingProvider';
    END IF;

    -- Build rendering provider (also the pharmacy/site)
    SELECT 
        v_site_id::integer,
        'RenderingProvider'::text
    INTO 
        v_rendering_provider.site_id,
        v_rendering_provider.provider_type;

    -- Reuse contact and address from billing provider
    v_rendering_provider.contact_information := v_billing_provider.contact_information;
    v_rendering_provider.address := v_billing_provider.address;

    -- Get organization name, NPI, and other site details for rendering provider
    SELECT 
        site.name::text,
        site.npi::text,
        site.tax_id::text,
        CASE 
            WHEN py.mm_send_rendering_prov_commercial_number = 'Yes' THEN py.mm_rendering_commercial_number 
            ELSE NULL
        END::text,
        site.state_license::text
    INTO 
        v_rendering_provider.organization_name,
        v_rendering_provider.npi,
        v_rendering_provider.employer_identification_number,
        v_rendering_provider.commercial_number,
        v_rendering_provider.state_license_number
    FROM form_site site
    INNER JOIN form_payer py ON py.id = v_payer_id
        AND py.deleted IS NOT TRUE 
        AND py.archived IS NOT TRUE
    WHERE site.id = v_site_id
      AND site.deleted IS NOT TRUE 
      AND site.archived IS NOT TRUE;

    -- Build the final providers loop result
    SELECT
        p_patient_id::integer,
        v_site_id::integer,
        CASE WHEN v_billing_provider.site_id IS NOT NULL THEN ARRAY[v_billing_provider] ELSE NULL::mm_billing_provider_info[] END,
        CASE WHEN v_billing_provider.site_id IS NOT NULL THEN 'Yes' ELSE NULL END::text,
        CASE WHEN v_referring_provider.physician_id IS NOT NULL THEN ARRAY[v_referring_provider] ELSE NULL::mm_dr_provider_info[] END,
        CASE WHEN v_referring_provider.physician_id IS NOT NULL THEN 'Yes' ELSE NULL END::text,
        CASE WHEN v_ordering_provider.physician_id IS NOT NULL THEN ARRAY[v_ordering_provider] ELSE NULL::mm_dr_provider_info[] END,
        CASE WHEN v_ordering_provider.physician_id IS NOT NULL THEN 'Yes' ELSE NULL END::text,
        CASE WHEN v_rendering_provider.site_id IS NOT NULL THEN ARRAY[v_rendering_provider] ELSE NULL::mm_rendering_provider_info[] END,
        CASE WHEN v_rendering_provider.site_id IS NOT NULL THEN 'Yes' ELSE NULL END::text,
        NULL::mm_dr_provider_info[],  -- No supervising provider for now
        NULL::text  -- No supervising provider tabif
    INTO 
        v_result.patient_id,
        v_result.site_id,
        v_result.billing,
        v_result.tabif_billing,
        v_result.referring,
        v_result.tabif_referring,
        v_result.ordering,
        v_result.tabif_ordering,
        v_result.rendering,
        v_result.tabif_rendering,
        v_result.supervising,
        v_result.tabif_supervising;

    RAISE LOG 'MM provider loop build result: %', v_result;

    -- Log success
    PERFORM log_billing_function(
      'build_mm_provider_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_provider_loop',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'build_mm_provider_loop',
        'patient_id', p_patient_id
      )
    );

    PERFORM log_billing_function(
      'build_mm_provider_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;