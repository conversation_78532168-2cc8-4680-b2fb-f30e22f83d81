CREATE OR REPLACE FUNCTION create_confirmed_dt_chargeline_presets(
  p_careplan_dt_id integer
) RETURNS charge_line_with_split[] AS $$
DECLARE
  v_result charge_line_with_split[];
  v_error_record record;
  v_start_time timestamptz;
  v_site_id integer;
  v_ticket_no text;
  v_patient_id integer;

  v_rx_guid uuid := gen_random_uuid();
  v_supply_guid uuid := gen_random_uuid();
  v_dme_guid uuid := gen_random_uuid();
  v_pa_guid uuid := gen_random_uuid();
  v_medid_guid uuid := gen_random_uuid();
BEGIN
  -- Record start time for performance tracking
  v_start_time := clock_timestamp();
  RAISE LOG 'Starting create_confirmed_dt_chargeline_presets for dt_id: %', p_careplan_dt_id;
  
  -- Check for any errors
  SELECT ticket_no, ticket_item_no, error
  INTO v_error_record
  FROM vw_delivery_ticket_pulled_items dti
  WHERE delivery_ticket_id = p_careplan_dt_id AND dti.bill = 'Yes'
  AND error IS NOT NULL
  LIMIT 1;
  
  -- If error found, log it and raise exception
  IF v_error_record.error IS NOT NULL THEN
    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_record.error,
      'Error creating confirmed delivery ticket charge lines',
      'FUNCTION',
      current_schema(),
      'form_billing_invoice',
      jsonb_build_object(
        'function_name', 'create_confirmed_dt_chargeline_presets',
        'careplan_dt_id', p_careplan_dt_id,
        'ticket_no', v_error_record.ticket_no,
        'ticket_item_no', v_error_record.ticket_item_no
      )
    );
    
    RAISE EXCEPTION 'Error creating charge lines for confirmed delivery ticket % item %: %',
      v_error_record.ticket_no,
      v_error_record.ticket_item_no,
      v_error_record.error;
  END IF;

  -- Get rx_ids, site_id, ticket_no and patient_id once
  SELECT 
    MAX(site_id),
    MAX(ticket_no),
    MAX(patient_id)
  INTO 
    v_site_id,
    v_ticket_no,
    v_patient_id
  FROM vw_delivery_ticket_pulled_items dti
  WHERE delivery_ticket_id = p_careplan_dt_id AND dti.bill = 'Yes';
  
  RAISE LOG 'Collected data about delivery ticket in: %', clock_timestamp() - v_start_time;

  -- Handle original charge lines first
  WITH existing_charge_lines AS (
    SELECT
      lcl.*
    FROM get_existing_charge_lines_pre_confirmation(p_careplan_dt_id) lcl
    WHERE NOT EXISTS (SELECT 1 
    FROM form_ledger_charge_line lcl2
    WHERE lcl2.parent_charge_no = lcl.charge_no AND 
    lcl2.archived IS NOT TRUE AND 
    lcl2.deleted IS NOT TRUE 
    AND COALESCE(lcl2.void, 'No') <> 'Yes'
    AND COALESCE(lcl2.zeroed, 'No') <> 'Yes')
  ),
  charge_lines AS (
    SELECT
      NULL as id,
      calculate_invoice_split_no(
        lcl.payer_id,
        v_rx_guid,
        v_supply_guid,
        v_dme_guid,
        v_pa_guid,
        v_medid_guid,
        lcl.rx_no,
        lcl.inventory_type,
        inv.medid,
        get_prior_auth_id(
          dti.inventory_id::INTEGER,
          dti.rx_id::INTEGER,
          dti.insurance_id::INTEGER
        )
      ) AS calc_invoice_split_no,
      lcl.*
    FROM vw_delivery_ticket_pulled_items dti
    CROSS JOIN LATERAL create_ledger_charge_line(
      dti.inventory_id::INTEGER,
      dti.insurance_id::INTEGER,
      dti.site_id::INTEGER,
      dti.patient_id::INTEGER,
      dti.dispense_quantity::NUMERIC,
      dti.is_primary_drug::BOOLEAN,
      CASE WHEN dti.template_type = 'Compound' 
           THEN 'COMPOUND_NO_PLACEHOLDER_' || dti.rx_id 
           ELSE NULL END,
      dti.rx_id::INTEGER,
      dti.rental_id::INTEGER,
      dti.ticket_no::TEXT,
      dti.ticket_item_no::TEXT,
      NULL::INTEGER,
      FALSE,
      NULL::text
    ) lcl
    INNER JOIN form_inventory inv ON inv.id = dti.inventory_id
    WHERE dti.delivery_ticket_id = p_careplan_dt_id AND dti.bill = 'Yes'
    AND NOT EXISTS (SELECT 1 
      FROM existing_charge_lines ecl
      WHERE ecl.insurance_id = dti.insurance_id 
      AND ecl.rx_id = dti.rx_id
      AND ecl.inventory_id = dti.inventory_id)
  ),
  charge_lines_with_balance AS (
    SELECT 
      ecl.charge_no,
      ecl.inventory_id,
      cpor.rx_no,
      ecl.inventory_type,
      get_next_insurance_id(ecl.rx_id, ecl.insurance_id, FALSE) as next_insurance_id
    FROM existing_charge_lines ecl
    INNER JOIN form_careplan_order_rx cpor ON cpor.id = ecl.rx_id
    WHERE ecl.remaining_balance > 0
    AND NOT EXISTS (SELECT 1 
    FROM form_billing_invoice bi
    INNER JOIN form_ncpdp ncpdp ON ncpdp.invoice_no = bi.invoice_no
    AND ncpdp.archived IS NOT TRUE 
    AND ncpdp.deleted IS NOT TRUE 
    AND ncpdp.status NOT IN ('Captured')
    WHERE bi.invoice_no = ecl.invoice_no
    AND bi.archived IS NULL 
    AND bi.deleted IS NULL 
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes'
    )
  ),
  charge_lines_with_copay AS (
    SELECT
      ecl.charge_no,
      ecl.inventory_id,
      cpor.rx_no,
      ecl.inventory_type,
      pi.id as next_insurance_id
    FROM existing_charge_lines ecl
    INNER JOIN form_patient_insurance pi ON pi.patient_id = ecl.patient_id AND pi.payer_id = 1
    INNER JOIN form_careplan_order_rx cpor ON cpor.id = ecl.rx_id
    WHERE ecl.copay > 0 AND NOT EXISTS (SELECT 1 
    FROM charge_lines_with_balance cob
    INNER JOIN form_patient_insurance pi ON pi.id = cob.next_insurance_id
    AND pi.archived IS NOT TRUE
    AND pi.deleted IS NOT TRUE
    INNER JOIN form_payer py ON py.id = pi.payer_id
    AND py.archived IS NOT TRUE
    AND py.deleted IS NOT TRUE
    AND py.type_id = 'COPAY') -- If we are billing a copay card next, don't bill the patient their copay
  ),
  combined_cob_charge_lines AS (
    SELECT * FROM charge_lines_with_balance WHERE next_insurance_id IS NOT NULL
    UNION
    SELECT * FROM charge_lines_with_copay
  ),
  cob_results AS (
    SELECT 
      NULL as id,
      calculate_invoice_split_no(
        pi.payer_id,
        v_rx_guid,
        v_supply_guid,
        v_dme_guid,
        v_pa_guid,
        v_medid_guid,
        cwb.rx_no,
        cwb.inventory_type,
        inv.medid,
        NULL::integer
      ) AS calc_invoice_split_no,
      lcl.*
    FROM combined_cob_charge_lines cwb
    INNER JOIN form_inventory inv ON inv.id = cwb.inventory_id
    INNER JOIN form_patient_insurance pi ON pi.id = cwb.next_insurance_id
    CROSS JOIN LATERAL create_cob_ledger_charge_line(
      cwb.next_insurance_id,
      cwb.charge_no
    ) lcl
    WHERE lcl IS NOT NULL 
  ),
  unified_charge_lines AS (
    SELECT lcl.*
    FROM charge_lines lcl

    UNION

    SELECT cob.*
    FROM cob_results cob
  )
  SELECT array_agg(ucl) as charge_lines
  FROM unified_charge_lines ucl
  INTO v_result;

  RAISE LOG 'Successfully created delivery ticket charge lines in %ms', extract(milliseconds from clock_timestamp() - v_start_time);

  -- Return the final result as json
  RETURN v_result;

EXCEPTION WHEN OTHERS THEN
  -- Log the error
  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    SQLERRM,
    'Unhandled error in create_confirmed_dt_chargeline_presets',
    'FUNCTION',
    current_schema(),
    'form_billing_invoice',
    jsonb_build_object(
      'function_name', 'create_confirmed_dt_chargeline_presets',
      'careplan_dt_id', p_careplan_dt_id,
      'execution_time_ms', extract(milliseconds from clock_timestamp() - v_start_time)
    )
  );
  RAISE;
END;
$$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION create_cob_chargeline_presets(
  p_invoice_no text,
  p_previous_insurance_id integer DEFAULT NULL,
  p_next_insurance_id integer DEFAULT NULL,
  p_ignore_split boolean DEFAULT true,
  p_charge_nos text[] DEFAULT NULL,
  p_amounts numeric[] DEFAULT NULL
) RETURNS charge_line_with_split[] AS $$  
DECLARE
  v_charge_lines charge_line[];
  v_split_charge_lines charge_line_with_split[];
  v_next_insurance_id integer;
  v_missing_next_payer boolean := false;
  v_start_time timestamptz;
  v_json_array json;
  v_parent_info record;

  v_rx_guid uuid := gen_random_uuid();
  v_supply_guid uuid := gen_random_uuid();
  v_dme_guid uuid := gen_random_uuid();
  v_medid_guid uuid := gen_random_uuid();
BEGIN
  -- Record start time for performance tracking
  v_start_time := clock_timestamp();

  RAISE LOG 'Starting create_cob_chargeline_presets for invoice: %, previous insurance: %, next insurance: %', 
    p_invoice_no, p_previous_insurance_id, p_next_insurance_id;

  IF p_invoice_no IS NULL THEN
    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      'Invoice number is required',
      'Error creating COB charge lines',
      'FUNCTION',
      current_schema(),
      'billing_invoice',
      jsonb_build_object(
        'function_name', 'create_cob_chargeline_presets',
        'p_invoice_no', p_invoice_no,
        'p_previous_insurance_id', p_previous_insurance_id,
        'p_next_insurance_id', p_next_insurance_id
      )
    );
    RAISE EXCEPTION 'Error creating COB charge lines for invoice %: Invoice number is required',
      p_invoice_no;
  END IF;
  
  RAISE LOG 'p_invoice_no: % p_previous_insurance_id: % p_next_insurance_id: %', 
    p_invoice_no, p_previous_insurance_id, p_next_insurance_id;

  -- Get invoice information and next insurance ID if not provided
  SELECT 
    bi.id,
    bi.patient_id,
    rx.id as rx_id,
    COALESCE(p_next_insurance_id, 
      get_next_insurance_id(rx.id, p_previous_insurance_id, TRUE)
    ) as next_insurance_id
  INTO v_parent_info
  FROM form_billing_invoice bi
  INNER JOIN form_ledger_charge_line lcl ON lcl.invoice_no = bi.invoice_no
    AND lcl.archived IS NOT TRUE
    AND lcl.deleted IS NOT TRUE
    AND COALESCE(lcl.void, 'No') <> 'Yes'
    AND lcl.inventory_type = 'Drug'
  INNER JOIN form_careplan_order_rx rx ON rx.rx_no = lcl.rx_no
    AND rx.archived IS NOT TRUE
    AND rx.deleted IS NOT TRUE
  WHERE bi.invoice_no = p_invoice_no
    AND bi.archived IS NOT TRUE
    AND bi.deleted IS NOT TRUE
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes'
  LIMIT 1;

  IF v_parent_info.next_insurance_id IS NULL THEN
    -- Return missing_next_payer flag as single-element array
    RETURN ARRAY[json_build_object('missing_next_payer', true)::json];
  END IF;

  -- Generate COB charge lines for each parent charge line
  WITH parent_charge_lines AS (
    SELECT lcl.*
    FROM form_ledger_charge_line lcl
    WHERE lcl.invoice_no = p_invoice_no
      AND lcl.archived IS NOT TRUE
      AND lcl.deleted IS NOT TRUE
      AND COALESCE(lcl.void, 'No') <> 'Yes'
      AND (p_charge_nos IS NULL OR lcl.charge_no = ANY(p_charge_nos))
  ),
  cob_results AS (
    SELECT 
      NULL::integer AS id,
      calculate_invoice_split_no(
        cob.payer_id,
        v_rx_guid,
        v_supply_guid,
        v_dme_guid,
        NULL::uuid,
        v_medid_guid,
        cob.rx_no,
        cob.inventory_type,
        inv.medid,
        NULL::integer
      )::text AS calc_invoice_split_no,
      cob.site_id::integer AS site_id,
      cob.patient_id::integer AS patient_id,
      cob.insurance_id::integer AS insurance_id,
      cob.payer_id::integer AS payer_id,
      cob.inventory_id::integer AS inventory_id,
      cob.shared_contract_id::integer AS shared_contract_id,
      cob.is_dirty::text AS is_dirty,
      cob.charge_no::text AS charge_no,
      cob.parent_charge_no::text AS parent_charge_no,
      cob.master_charge_no::text AS master_charge_no,
      cob.compound_no::text AS compound_no,
      cob.ticket_no::text AS ticket_no,
      cob.ticket_item_no::text AS ticket_item_no,
      cob.rental_id::integer AS rental_id,
      cob.order_rx_id::integer AS order_rx_id,
      cob.is_primary_drug::text AS is_primary_drug,
      cob.is_primary_drug_ncpdp::text AS is_primary_drug_ncpdp,
      cob.rx_no::text AS rx_no,
      cob.inventory_type_filter::text[] AS inventory_type_filter,
      cob.inventory_type::text AS inventory_type,
      cob.revenue_code_id::text AS revenue_code_id,
      cob.hcpc_code::text AS hcpc_code,
      cob.ndc::text AS ndc,
      cob.formatted_ndc::text AS formatted_ndc,
      cob.gcn_seqno::text AS gcn_seqno,
      cob.charge_quantity::numeric AS charge_quantity,
      cob.charge_quantity_ea::numeric AS charge_quantity_ea,
      cob.hcpc_quantity::numeric AS hcpc_quantity,
      cob.hcpc_unit::text AS hcpc_unit,
      cob.metric_quantity::numeric AS metric_quantity,
      cob.charge_unit::text AS charge_unit,
      cob.bill_quantity::numeric AS bill_quantity,
      cob.metric_unit_each::numeric AS metric_unit_each,
      cob.billing_unit_id::text AS billing_unit_id,
      cob.billing_method_id::text AS billing_method_id,
      cob.pricing_source::text AS pricing_source,
      CASE
        WHEN p_amounts IS NOT NULL THEN p_amounts[array_position(p_charge_nos, pcl.charge_no)]
        ELSE cob.billed
      END::numeric AS billed,
      CASE
        WHEN p_amounts IS NOT NULL THEN
          CASE WHEN pcl.bill_quantity > 0 THEN p_amounts[array_position(p_charge_nos, pcl.charge_no)] / pcl.bill_quantity ELSE 0 END
        ELSE cob.calc_billed_ea
      END::numeric AS calc_billed_ea,
      CASE
        WHEN p_amounts IS NOT NULL THEN p_amounts[array_position(p_charge_nos, pcl.charge_no)]
        ELSE cob.expected
      END::numeric AS expected,
      CASE
        WHEN p_amounts IS NOT NULL THEN
          CASE WHEN pcl.bill_quantity > 0 THEN p_amounts[array_position(p_charge_nos, pcl.charge_no)] / pcl.bill_quantity ELSE 0 END
        ELSE cob.calc_expected_ea
      END::numeric AS calc_expected_ea,
      cob.list_price::numeric AS list_price,
      cob.calc_list_ea::numeric AS calc_list_ea,
      cob.total_cost::numeric AS total_cost,
      cob.gross_amount_due::numeric AS gross_amount_due,
      cob.incv_amt_sub::numeric AS incv_amt_sub,
      cob.copay::numeric AS copay,
      cob.calc_cost_ea::numeric AS calc_cost_ea,
      cob.total_adjusted::numeric AS total_adjusted,
      cob.total_balance_due::numeric AS total_balance_due,
      cob.dispense_fee::numeric AS dispense_fee,
      cob.pt_pd_amt_sub::numeric AS pt_pd_amt_sub,
      cob.encounter_id::integer AS encounter_id,
      cob.description::text AS description,
      cob.upc::text AS upc,
      cob.upin::text AS upin,
      cob.cost_basis::text AS cost_basis,
      cob.awp_price::numeric AS awp_price,
      cob.modifier_1::text AS modifier_1,
      cob.modifier_2::text AS modifier_2,
      cob.modifier_3::text AS modifier_3,
      cob.modifier_4::text AS modifier_4,
      cob.rental_type::text AS rental_type,
      cob.frequency_code::text AS frequency_code,
      cob.fill_number::integer AS fill_number,
      cob.paid::numeric AS paid,
      cob.date_of_service::date AS date_of_service,
      cob.date_of_service_end::date AS date_of_service_end
    FROM parent_charge_lines pcl
    INNER JOIN form_inventory inv ON inv.id = pcl.inventory_id
      AND inv.archived IS NOT TRUE
      AND inv.deleted IS NOT TRUE
    CROSS JOIN LATERAL create_cob_ledger_charge_line(
      v_parent_info.next_insurance_id,
      pcl.charge_no
    ) cob
    WHERE cob IS NOT NULL
  )
  SELECT array_agg(cob)
  INTO v_split_charge_lines
  FROM cob_results cob;

  IF v_split_charge_lines IS NULL THEN
    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      'No COB charge lines generated',
      'Error creating COB charge lines',
      'FUNCTION',
      current_schema(),
      'billing_invoice',
      jsonb_build_object(
        'function_name', 'create_cob_chargeline_presets',
        'p_invoice_no', p_invoice_no,
        'p_previous_insurance_id', p_previous_insurance_id,
        'p_next_insurance_id', p_next_insurance_id,
        'next_insurance_id', v_parent_info.next_insurance_id
      )
    );
    RAISE EXCEPTION 'No COB charge lines generated for invoice %', p_invoice_no;
  END IF;

  RAISE LOG 'Successfully created COB charge lines in %ms', 
    extract(milliseconds from clock_timestamp() - v_start_time);
    
  RETURN v_split_charge_lines;
EXCEPTION WHEN OTHERS THEN
  -- Log error
  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    SQLERRM,
    'Unhandled error in create_cob_chargeline_presets',
    'FUNCTION',
    current_schema(),
    'billing_invoice',
    jsonb_build_object(
      'function_name', 'create_cob_chargeline_presets',
      'p_invoice_no', p_invoice_no,
      'p_previous_insurance_id', p_previous_insurance_id,
      'p_next_insurance_id', p_next_insurance_id,
      'execution_time_ms', extract(milliseconds from clock_timestamp() - v_start_time)
    )
  );
  RAISE;
END;
$$ LANGUAGE plpgsql VOLATILE;


DO $$ BEGIN
  PERFORM drop_all_function_signatures('create_rx_chargeline_presets');
END $$;
CREATE OR REPLACE FUNCTION create_rx_chargeline_presets(
  p_order_rx_id integer,
  p_is_test boolean DEFAULT false
) RETURNS charge_line_with_split[] AS $$
DECLARE
  v_split_charge_lines charge_line_with_split[];
  v_error_record record;

  v_rx_guid uuid := gen_random_uuid();
  v_supply_guid uuid := gen_random_uuid();
  v_dme_guid uuid := gen_random_uuid();
  v_pa_guid uuid := gen_random_uuid();
  v_medid_guid uuid := gen_random_uuid();
BEGIN
  -- Check for any errors
  SELECT rx_no, error
  INTO v_error_record
  FROM vw_rx_fill_items
  WHERE p_order_rx_id = rx_id
  AND error IS NOT NULL
  LIMIT 1;

  IF v_error_record.error IS NOT NULL THEN
    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_record.error,
      'Error creating prescription charge lines',
      'FUNCTION',
      current_schema(),
      'billing_invoice',
      jsonb_build_object(
        'function_name', 'create_rx_chargeline_presets',
        'p_order_rx_id', p_order_rx_id
      )
    );
    
    RAISE EXCEPTION 'Error creating charge lines for prescription %: %',
      v_error_record.rx_no,
      v_error_record.error;
  END IF;

  -- Get charge lines using create_ledger_charge_line
  WITH charge_lines AS (
    SELECT
        NULL AS id,
        calculate_invoice_split_no(
        lcl.payer_id,
        v_rx_guid,
        v_supply_guid,
        v_dme_guid,
        v_pa_guid,
        v_medid_guid,
        lcl.rx_no,
        lcl.inventory_type,
        inv.medid,
        get_prior_auth_id(
          rxfi.inventory_id::INTEGER,
          rxfi.rx_id::INTEGER,
          rxfi.insurance_id::INTEGER
        )) AS calc_invoice_split_no,
      lcl.*
    FROM vw_rx_fill_items rxfi
    INNER JOIN form_inventory inv ON inv.id = rxfi.inventory_id 
    AND rxfi.archived IS NOT TRUE
    AND rxfi.deleted IS NOT TRUE
    CROSS JOIN LATERAL create_ledger_charge_line(
      rxfi.inventory_id::INTEGER,
      rxfi.insurance_id::INTEGER,
      rxfi.site_id::INTEGER,
      rxfi.patient_id::INTEGER,
      rxfi.dispense_quantity::NUMERIC,
      rxfi.is_primary_drug::BOOLEAN,
      CASE
        WHEN rxfi.is_compound THEN 'COMPOUND_NO_PLACEHOLDER_' || rxfi.rx_id
        ELSE NULL
      END,
      rxfi.rx_id::INTEGER,
      NULL::INTEGER,
      NULL::TEXT,
      NULL::TEXT,
      NULL::INTEGER,
      p_is_test::BOOLEAN,
      NULL::text
    ) lcl
    WHERE rxfi.rx_id = p_order_rx_id
  )
  SELECT array_agg(lcl)
  INTO v_split_charge_lines 
  FROM charge_lines lcl;
  
  RETURN v_split_charge_lines;
END;
$$ LANGUAGE plpgsql VOLATILE;