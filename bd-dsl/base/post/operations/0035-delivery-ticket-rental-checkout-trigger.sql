-- Function to handle rental equipment updates
DO $$ BEGIN
  PERFORM drop_all_function_signatures('update_rental_dispense_info');
END $$;
CREATE OR REPLACE FUNCTION update_rental_dispense_info(new_record form_careplan_delivery_tick)
RETURNS VOID AS $$
DECLARE
  v_is_voided boolean;
  v_is_confirmed boolean;
BEGIN
  -- Check if delivery ticket is voided
  v_is_voided := new_record.void = 'Yes' OR new_record.archived IS TRUE OR new_record.deleted IS TRUE;
  v_is_confirmed := new_record.confirmed = 'Yes';
  
  IF v_is_voided THEN
    -- Clear dispense info for rentals on this ticket
    WITH affected_rentals AS (
      SELECT DISTINCT
        dti.rental_id as order_rental_id
      FROM vw_delivery_ticket_pulled_items dti
      WHERE dti.rental_id IS NOT NULL 
      AND dti.delivery_ticket_id = new_record.id
    )
    UPDATE form_careplan_order_rental cor
    SET
      last_dispense_date = NULL,
      last_dispense_delivery_tick_id = NULL,
      serial_no = NULL
    FROM affected_rentals ar
    WHERE cor.id = ar.order_rental_id;
  ELSIF v_is_confirmed THEN
    -- Update rental equipment with dispense info and serial numbers
    WITH rental_info AS (
      SELECT
        dti.rental_id as order_rental_id,
        dt.service_from::date as last_dispense_date,
        dt.id as last_dispense_delivery_tick_id,
        wtp.serial_no
      FROM vw_delivery_ticket_pulled_items dti
      INNER JOIN form_careplan_dt_wt_pulled wtp ON wtp.ticket_item_no = dti.ticket_item_no
      INNER JOIN form_careplan_delivery_tick dt ON dt.ticket_no = dti.ticket_no
      WHERE dti.rental_id IS NOT NULL
      AND wtp.serial_no IS NOT NULL
      AND dt.id = new_record.id
    )
    UPDATE form_careplan_order_rental cor
    SET
      last_dispense_date = ri.last_dispense_date,
      last_dispense_delivery_tick_id = ri.last_dispense_delivery_tick_id,
      serial_no = ri.serial_no
    FROM rental_info ri
    WHERE cor.id = ri.order_rental_id
    AND cor.last_dispense_delivery_tick_id IS NULL;
  END IF;
  
  EXCEPTION WHEN OTHERS THEN
    -- Log error
    INSERT INTO dispensing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      SQLERRM,
      'Exception in update_rental_dispense_info',
      'FUNCTION',
      current_schema(),
      'form_careplan_delivery_tick',
      jsonb_build_object(
        'function_name', 'update_rental_dispense_info'
      )
    );
    RAISE WARNING 'Failed to update rental dispense info: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger function
DO $$ BEGIN
  PERFORM drop_all_function_signatures('trigger_update_rental_dispense');
END $$;
CREATE OR REPLACE FUNCTION trigger_update_rental_dispense()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
  VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);
  
  -- Update rental dispense info, passing the NEW record
  PERFORM update_rental_dispense_info(NEW);
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE OR REPLACE TRIGGER update_rental_dispense_after_delivery_tick_change
AFTER UPDATE
ON form_careplan_delivery_tick
FOR EACH ROW
EXECUTE FUNCTION trigger_update_rental_dispense();