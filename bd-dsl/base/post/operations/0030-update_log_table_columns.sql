-- Drop existing functions with this name

DO $$ BEGIN
  PERFORM drop_all_function_signatures('sync_log_table_column_types');
END $$;
CREATE OR REPLACE FUNCTION sync_log_table_column_types(dry_run boolean DEFAULT false)
RETURNS TABLE (
    form_table text,
    log_table text,
    column_name text,
    old_type text,
    new_type text,
    alter_statement text,
    status text
) AS $$
DECLARE
    current_form_table text;
    current_log_table text;
    current_alter_sql text;
BEGIN
    -- Create temporary table to store results
    CREATE TEMPORARY TABLE IF NOT EXISTS type_changes (
        form_table text,
        log_table text,
        column_name text,
        old_type text,
        new_type text,
        alter_statement text,
        status text
    );

    -- Loop through all form tables
    FOR current_form_table IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name LIKE 'form_%'
    LOOP
        -- Construct corresponding log table name
        current_log_table := 'log_' || substring(current_form_table from 6);
        
        -- Check if log table exists
        IF EXISTS (
            SELECT 1 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = current_log_table
        ) THEN
            -- Insert mismatched columns into results
            INSERT INTO type_changes
            SELECT 
                current_form_table,
                current_log_table,
                form_cols.column_name,
                log_cols.data_type as old_type,
                form_cols.data_type as new_type,
                format('ALTER TABLE %I ALTER COLUMN %I TYPE %s USING %I::%s;',
                    current_log_table,
                    form_cols.column_name,
                    form_cols.data_type,
                    form_cols.column_name,
                    form_cols.data_type
                ) as alter_statement,
                CASE 
                    WHEN dry_run THEN 'PENDING'
                    ELSE 'EXECUTED'
                END as status
            FROM 
                information_schema.columns form_cols
                JOIN information_schema.columns log_cols 
                    ON form_cols.column_name = log_cols.column_name
                    AND log_cols.table_name = current_log_table
            WHERE 
                form_cols.table_name = current_form_table
                AND form_cols.data_type != log_cols.data_type;

            -- Execute ALTER statements if not in dry run mode
            IF NOT dry_run THEN
                FOR current_alter_sql IN 
                    SELECT tc.alter_statement 
                    FROM type_changes tc
                    WHERE tc.log_table = current_log_table
                LOOP
                    EXECUTE current_alter_sql;
                END LOOP;
            END IF;
        END IF;
    END LOOP;

    -- Return results
    RETURN QUERY SELECT * FROM type_changes;
    
    -- Clean up
    DROP TABLE IF EXISTS type_changes;
END;
$$ LANGUAGE plpgsql;