CREATE OR REPLACE FUNCTION build_mm_submitter_segment(
  p_site_id integer
) RETURNS mm_submitter_segment AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_submitter_segment;
  v_error_message text;
  v_params jsonb;
  v_contact_info mm_contact_info;
BEGIN
  -- Check for null input parameters
  IF p_site_id IS NULL THEN
    RAISE EXCEPTION 'Site ID cannot be NULL';
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'site_id', p_site_id
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
      'build_mm_submitter_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM submitter segment for site ID: %', p_site_id;

    -- Build submitter segment from site record
    SELECT 
        p_site_id::integer,
        COALESCE(site.mm_ch_organization_name, site.name)::text
    INTO 
        v_result.site_id,
        v_result.organization_name
    FROM form_site site
    WHERE site.id = p_site_id
      AND site.deleted IS NOT TRUE 
      AND site.archived IS NOT TRUE;

    -- Validate that we found the site record
    IF v_result.organization_name IS NULL THEN
        RAISE EXCEPTION 'Site record not found or missing mm_ch_organization_name for ID: %', p_site_id;
    END IF;

    -- Build contact information array
    SELECT 
        COALESCE(site.mm_contact_name, site.name)::text,
        site.mm_contact_email::text,
        site.phone::text,
        site.fax::text
    INTO 
        v_contact_info.name,
        v_contact_info.email,
        v_contact_info.phone_number,
        v_contact_info.fax_number
    FROM form_site site
    WHERE site.id = p_site_id
      AND site.deleted IS NOT TRUE 
      AND site.archived IS NOT TRUE;

    -- Set contact information array
    v_result.contact_information := ARRAY[v_contact_info];

    RAISE LOG 'MM submitter segment build result: %', v_result;

    -- Log success
    PERFORM log_billing_function(
      'build_mm_submitter_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_submitter_segment',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'build_mm_submitter_segment',
        'site_id', p_site_id
      )
    );

    PERFORM log_billing_function(
      'build_mm_submitter_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;