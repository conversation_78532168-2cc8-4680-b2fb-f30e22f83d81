
DO $$ BEGIN
  PERFORM drop_all_function_signatures('prevent_accepting_dirty_invoice');
END $$;
CREATE OR REPLACE FUNCTION prevent_accepting_dirty_invoice()
RETURNS TRIGGER AS $$
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Disable for now
    RETURN NEW;

    -- Check if trying to set revenue_accepted to 'Yes' when is_dirty is 'Yes'
    IF NEW.revenue_accepted = 'Yes' AND NEW.is_dirty = 'Yes' THEN
        -- Log the error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Cannot accept revenue for invoice while it is marked as dirty',
            'Validation in prevent_accepting_dirty_invoice trigger',
            'TRIGGER',
            current_schema(),
            'form_billing_invoice',
            jsonb_build_object(
                'invoice_id', NEW.id,
                'is_dirty', NEW.is_dirty,
                'revenue_accepted', NEW.revenue_accepted
            )
        );
        
        RAISE EXCEPTION 'Cannot accept revenue for invoice while it is marked as dirty';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE TRIGGER prevent_accepting_dirty_invoice_trigger
BEFORE UPDATE ON form_billing_invoice
FOR EACH ROW
EXECUTE FUNCTION prevent_accepting_dirty_invoice();
