-- Update the ledger charge line invoice status when the invoice status is changed

-- Function to sync charge lines to invoice
DO $$ BEGIN
  PERFORM drop_all_function_signatures('sync_charge_lines_to_invoice');
END $$;
CREATE OR REPLACE FUNCTION sync_charge_lines_to_invoice()
RETURNS TRIGGER AS $$
DECLARE
    invoice_locked BOOLEAN;
    new_total_expected NUMERIC;
    new_total_cost NUMERIC;
    new_total_billed NUMERIC;
    is_financial_change BOOLEAN;
BEGIN
  -- Skip if trigger flag is set
  IF current_setting('clara.disable_invoice_trigger', true) = 'on' THEN
    RAISE LOG 'sync_charge_lines_to_invoice skipped due to flag';
    RETURN NEW;
  END IF;

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Check if this is a change to financial fields (expected, billed)
    is_financial_change := (TG_OP = 'INSERT') OR 
                          (TG_OP = 'UPDATE' AND 
                           (OLD.expected <> NEW.expected OR OLD.billed <> NEW.billed));
    
    -- Check if invoice is locked (revenue_accepted_posted = 'Yes')
    SELECT (COALESCE(revenue_accepted_posted, 'No') = 'Yes') INTO invoice_locked
    FROM form_billing_invoice
    WHERE invoice_no = NEW.invoice_no
      AND archived IS NOT TRUE
      AND deleted IS NOT TRUE
      AND COALESCE(void, 'No') <> 'Yes'
      AND COALESCE(zeroed, 'No') <> 'Yes';
      
    -- Only block financial changes when revenue_accepted_posted = 'Yes'
    IF invoice_locked AND is_financial_change THEN
        IF OLD.expected <> NEW.expected AND COALESCE(NEW.void, 'No') <> 'Yes' AND COALESCE(NEW.zeroed, 'No') <> 'Yes' THEN
            RAISE EXCEPTION 'Cannot update expected amount when revenue_accepted_posted = ''Yes''';
        END IF;
        
        IF OLD.billed <> NEW.billed AND COALESCE(NEW.void, 'No') <> 'Yes' AND COALESCE(NEW.zeroed, 'No') <> 'Yes' THEN
            RAISE EXCEPTION 'Cannot update billed amount when revenue_accepted_posted = ''Yes''';
        END IF;
    END IF;
    
    -- Calculate new totals from all related charge lines and round to 2 decimal places
    SELECT 
        ROUND(COALESCE(SUM(lgl.expected::numeric), 0), 2),
        ROUND(COALESCE(SUM(lgl.total_cost::numeric), 0), 2),
        ROUND(COALESCE(SUM(lgl.billed::numeric), 0), 2)
    INTO 
        new_total_expected,
        new_total_cost,
        new_total_billed
    FROM form_ledger_charge_line lgl
    WHERE invoice_no = NEW.invoice_no
      AND archived IS NOT TRUE
      AND deleted IS NOT TRUE
      AND COALESCE(void, 'No') <> 'Yes'
      AND COALESCE(zeroed, 'No') <> 'Yes';
    
    -- Update the invoice with new totals
    PERFORM set_config('clara.prevent_locked_checks', 'on', true);
    UPDATE form_billing_invoice
    SET total_expected = new_total_expected,
        total_cost = new_total_cost,
        total_billed = new_total_billed
    WHERE invoice_no = NEW.invoice_no
      AND archived IS NOT TRUE
      AND deleted IS NOT TRUE
      AND COALESCE(void, 'No') <> 'Yes'
      AND COALESCE(zeroed, 'No') <> 'Yes'
      AND COALESCE(revenue_accepted_posted, 'No') <> 'Yes'
      AND (total_expected <> new_total_expected OR total_cost <> new_total_cost OR total_billed <> new_total_billed);
    PERFORM set_config('clara.prevent_locked_checks', 'off', true);

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to run after insert or update on charge lines

CREATE OR REPLACE TRIGGER after_charge_line_change
AFTER INSERT OR UPDATE OF expected, total_cost, billed
ON form_ledger_charge_line
FOR EACH ROW
WHEN (NEW.archived IS NOT TRUE AND NEW.deleted IS NOT TRUE AND COALESCE(NEW.void, 'No') <> 'Yes')
EXECUTE FUNCTION sync_charge_lines_to_invoice();

-- Function to handle deleting a charge line
DO $$ BEGIN
  PERFORM drop_all_function_signatures('handle_charge_line_change');
END $$;
CREATE OR REPLACE FUNCTION handle_charge_line_change()
RETURNS TRIGGER AS $$
DECLARE
    invoice_locked BOOLEAN;
    new_total_expected NUMERIC;
    new_total_cost NUMERIC;
    new_total_billed NUMERIC;
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);
  -- Skip if trigger flag is set
  IF current_setting('clara.disable_invoice_trigger', true) = 'on' THEN
    RAISE LOG 'sync_charge_lines_to_invoice skipped due to flag';
    RETURN NEW;
  END IF;

    -- If a charge line is being deleted or marked as archived/deleted/void,
    -- we need to update the invoice totals accordingly
    IF OLD.invoice_no IS NOT NULL THEN
        -- Check if invoice is locked (revenue_accepted_posted = 'Yes')
        SELECT (COALESCE(revenue_accepted_posted, 'No') = 'Yes') INTO invoice_locked
        FROM form_billing_invoice
        WHERE invoice_no = OLD.invoice_no
          AND archived IS NOT TRUE
          AND deleted IS NOT TRUE
          AND COALESCE(void, 'No') <> 'Yes'
          AND COALESCE(zeroed, 'No') <> 'Yes';
          
        IF invoice_locked AND OLD.expected <> NEW.expected THEN
            RAISE EXCEPTION 'Cannot update charge line expected amount when revenue is posted. An adjustment must be made.';
        END IF;
        
        -- Calculate new totals from all related charge lines except the one being modified
        SELECT 
            ROUND(COALESCE(SUM(lgl.expected::numeric), 0), 2),
            ROUND(COALESCE(SUM(lgl.total_cost::numeric), 0), 2),
            ROUND(COALESCE(SUM(lgl.billed::numeric), 0), 2)
        INTO 
            new_total_expected,
            new_total_cost,
            new_total_billed
        FROM form_ledger_charge_line lgl
        WHERE invoice_no = OLD.invoice_no
          AND (id <> OLD.id OR
              (NEW.archived IS TRUE OR 
               NEW.deleted IS TRUE OR 
               COALESCE(NEW.void, 'No') = 'Yes' OR
               COALESCE(NEW.zeroed, 'No') = 'Yes' OR
               NEW.invoice_no IS NULL OR 
               NEW.invoice_no <> OLD.invoice_no))
          AND archived IS NOT TRUE
          AND deleted IS NOT TRUE
          AND COALESCE(void, 'No') <> 'Yes'
          AND COALESCE(zeroed, 'No') <> 'Yes'
          AND COALESCE(revenue_accepted_posted, 'No') <> 'Yes';
        
        PERFORM set_config('clara.prevent_locked_checks', 'on', true);
        -- Update the invoice with new totals
        UPDATE form_billing_invoice
        SET total_expected = new_total_expected,
            total_cost = new_total_cost,
            total_billed = new_total_billed
        WHERE invoice_no = OLD.invoice_no
          AND archived IS NOT TRUE
          AND deleted IS NOT TRUE
          AND COALESCE(void, 'No') <> 'Yes'
          AND COALESCE(zeroed, 'No') <> 'Yes'
          AND COALESCE(revenue_accepted_posted, 'No') <> 'Yes'
          AND (total_expected <> new_total_expected OR total_cost <> new_total_cost OR total_billed <> new_total_billed);
      PERFORM set_config('clara.prevent_locked_checks', 'off', true);

    END IF;
    
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Trigger for when a charge line is deleted or marked as archived/deleted/void
CREATE OR REPLACE TRIGGER after_charge_line_delete_or_deactivate_trigger
AFTER UPDATE OF archived, deleted, void, invoice_no, zeroed
ON form_ledger_charge_line
FOR EACH ROW
WHEN (
    (OLD.archived IS NOT TRUE AND NEW.archived IS TRUE) OR
    (OLD.deleted IS NOT TRUE AND NEW.deleted IS TRUE) OR
    (COALESCE(OLD.void, 'No') <> 'Yes' AND COALESCE(NEW.void, 'No') = 'Yes') OR
    (COALESCE(OLD.zeroed, 'No') <> 'Yes' AND COALESCE(NEW.zeroed, 'No') = 'Yes') OR
    (OLD.invoice_no IS NOT NULL AND (NEW.invoice_no IS NULL OR NEW.invoice_no <> OLD.invoice_no))
)
EXECUTE FUNCTION handle_charge_line_change();