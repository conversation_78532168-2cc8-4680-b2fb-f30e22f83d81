DO $$ BEGIN
  PERFORM drop_all_function_signatures('set_invoice_no');
END $$;
CREATE OR REPLACE FUNCTION set_invoice_no() RETURNS TRIGGER AS $$
DECLARE
    v_record_count INTEGER := 0;
    v_invoice_no TEXT;
    v_original_invoice_no TEXT;
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Log function entry
    RAISE LOG 'Updating invoice no for invoice ID: %', 
             NEW.id;
    
    IF (NEW.master_invoice_no IS NULL OR NEW.master_invoice_no LIKE 'INVOICE_NO_PLACEHOLDER%') THEN
        RAISE EXCEPTION 'Master invoice no is null or has a placeholder value for invoice ID: %', NEW.id;
    END IF;

    v_original_invoice_no := NEW.invoice_no;
    IF (COALESCE(NEW.bill_for_denial, 'No')::text = 'Yes') THEN
        NEW.invoice_no := CONCAT(NEW.invoice_no, '-0');
        NEW.auto_name := CONCAT(NEW.invoice_no, ' DOS: ', TO_CHAR(NEW.date_of_service, 'MM/DD/YYYY'));
        RETURN NEW;
    ELSE
        SELECT COUNT(*) INTO v_record_count
        FROM form_billing_invoice
        WHERE master_invoice_no = NEW.master_invoice_no AND COALESCE(bill_for_denial, 'No') = 'No';
        NEW.invoice_no := CONCAT(NEW.master_invoice_no, '-', v_record_count + 1);
        NEW.is_master_invoice := CASE WHEN v_record_count = 0 THEN 'Yes' ELSE NULL END;
        NEW.auto_name := CONCAT(NEW.invoice_no, ' DOS: ', TO_CHAR(NEW.date_of_service, 'MM/DD/YYYY'));
        RETURN NEW;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DO $$ BEGIN
  PERFORM drop_all_function_signatures('update_invoice_links_after_insert');
END $$;
CREATE OR REPLACE FUNCTION update_invoice_links_after_insert() RETURNS TRIGGER AS $$
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Log function entry
    RAISE LOG 'Updating invoice links for invoice ID: %', NEW.id;
    PERFORM set_config('clara.disable_invoice_trigger', 'on', false);
    PERFORM set_config('clara.prevent_locked_checks', 'on', false);

    UPDATE form_ledger_charge_line lcl
    SET invoice_no = NEW.invoice_no
    WHERE invoice_no = NEW.series_uuid;
    UPDATE form_ncpdp nc
    SET invoice_no = NEW.invoice_no
    WHERE invoice_no = NEW.series_uuid;
    UPDATE form_med_claim mc
    SET invoice_no = NEW.invoice_no
    WHERE invoice_no = NEW.series_uuid;
    UPDATE form_med_claim_1500 mci
    SET invoice_no = NEW.invoice_no
    WHERE invoice_no = NEW.series_uuid;
    PERFORM set_config('clara.disable_invoice_trigger', 'off', false);
    PERFORM set_config('clara.prevent_locked_checks', 'off', false);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER trg_billing_invoice_set_invoice_no
BEFORE INSERT ON form_billing_invoice
FOR EACH ROW
EXECUTE FUNCTION set_invoice_no();

DROP TRIGGER IF EXISTS trg_invoice_link_updates ON form_billing_invoice;
CREATE CONSTRAINT TRIGGER trg_invoice_link_updates
AFTER INSERT ON form_billing_invoice
DEFERRABLE INITIALLY DEFERRED
FOR EACH ROW
EXECUTE FUNCTION update_invoice_links_after_insert();