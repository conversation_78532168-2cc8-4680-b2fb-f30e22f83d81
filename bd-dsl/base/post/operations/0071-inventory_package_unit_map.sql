-- Drop the index if it exists (to avoid conflicts when recreating table)
DROP INDEX IF EXISTS idx_package_units_package;

-- Create the table if it doesn't exist
CREATE TABLE IF NOT EXISTS package_units (
    package text PRIMARY KEY,
    description text,
    unit text
);

-- Create a temporary table for the new data
CREATE TEMPORARY TABLE temp_package_units (
    package text PRIMARY KEY,
    description text,
    unit text
);

-- Insert the mappings into temporary table
INSERT INTO temp_package_units (package, description, unit) VALUES
    ('AER BR.ACT', 'Aerosol, Breath Activated', 'actuation'),
    ('AER REF', 'Aerosol Refill Unit', 'each'),
    ('AER W/ADAP', 'Aerosol with Adapter', 'actuation'),
    ('AMPUL', 'Ampul with Device', 'ampule'),
    ('AMPUL/KIT', 'Ampul', 'kit'),
    ('AMP W/DEV', 'Ampul/Kit', 'kit'),
    ('BAG', 'Bag', 'each'),
    ('BLIST PACK', 'Blister Pack', 'blister'),
    ('BOTTLE', 'Bottle', 'each'),
    ('BOX', 'Box', 'each'),
    ('CAN', 'Can', 'each'),
    ('CANISTER', 'Canister', 'each'),
    ('CARTRIDGE', 'Cartridge', 'cartridge'),
    ('CUP', 'Cup', 'cup'),
    ('DISPENSER', 'Dispenser', 'each'),
    ('DOSE-PACK', 'Dispensing Pack', 'dose pack'),
    ('DROP BTL', 'Drum', 'drop'),
    ('DRUM', 'Drum', 'each'),
    ('FLEX CONT', 'Flexible Container', 'container'),
    ('GLASS CONT', 'Glass Container', 'container'),
    ('JAR', 'Jar', 'each'),
    ('KIT', 'Kit', 'kit'),
    ('KIT-REFILL', 'Kit-Refill', 'kit'),
    ('N/A', '', 'each'),
    ('PACKAGE', 'Package', 'package'),
    ('PACKET', 'Packet', 'packet'),
    ('PF APPLI', 'Applicator, Pre-filled', 'applicator'),
    ('ROLL', 'Roll', 'each'),
    ('SOLN BTL', 'Solution Bottle', 'each'),
    ('SQUEEZ BTL', 'Squeeze Bottle', 'each'),
    ('SYRINGE', 'Syringe', 'syringe'),
    ('TINE', 'Tine', 'each'),
    ('TUBE', 'Tube', 'tube'),
    ('TUBE/JAR', 'Tube/Jar', 'each'),
    ('TUBE/KIT', 'Tube/Kit', 'kit'),
    ('TUBE STICK', 'Tube Stick', 'stick'),
    ('VIAL', 'Vial', 'vial'),
    ('VIAL/KIT', 'Vial/Kit', 'kit');

-- Perform an upsert operation
INSERT INTO package_units (package, description, unit)
SELECT package, description, unit FROM temp_package_units
ON CONFLICT (package) 
DO UPDATE SET 
    description = EXCLUDED.description,
    unit = EXCLUDED.unit;

-- Clean up the temporary table
DROP TABLE temp_package_units;

-- Recreate the index
CREATE INDEX idx_package_units_package ON package_units(package);