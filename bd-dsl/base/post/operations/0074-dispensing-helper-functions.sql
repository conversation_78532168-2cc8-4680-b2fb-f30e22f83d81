DO $$ BEGIN
  PERFORM drop_all_function_signatures('get_filtered_inventory_items');
END $$;
CREATE OR REPLACE FUNCTION get_filtered_inventory_items(p_ticket_no TEXT)
RETURNS TABLE (
 auto_name TEXT,
 type TEXT,
 id INTEGER,
 manufacture TEXT,
 code TEXT,
 quantity_in_stock TEXT
) AS $$
BEGIN
-- Create a temporary table to store the inventory_ids from the delivery ticket
CREATE TEMP TABLE IF NOT EXISTS temp_filtered_items ON COMMIT DROP AS
SELECT
dti.inventory_id,
cdt.site_id
FROM
 form_careplan_dt_item dti
INNER JOIN form_careplan_delivery_tick cdt ON cdt.ticket_no = dti.ticket_no
JOIN
 form_inventory fi ON dti.inventory_id = fi.id
WHERE
dti.ticket_no = p_ticket_no
AND dti.inventory_id IS NOT NULL
AND dti.archived IS NOT TRUE
AND dti.deleted IS NOT TRUE
AND fi.type IN ('Billable', 'Equipment Rental', 'Supply');

RETURN QUERY
-- First part: Non-billable items
SELECT
fi.auto_name::TEXT as auto_name,  -- Explicit cast to TEXT
fi.type::TEXT,
fi.id as id,
mfr.mfg::TEXT as manufacture,
CASE
WHEN fi.formatted_ndc IS NOT NULL AND fi.hcpc_code IS NOT NULL THEN TRIM(CONCAT(fi.hcpc_code, ' ', fi.formatted_ndc))
WHEN fi.formatted_ndc IS NOT NULL THEN fi.formatted_ndc
ELSE fi.hcpc_code
END::TEXT as code,
format_numeric(COALESCE(
SUM(li.quantity),
0
)::NUMERIC) AS quantity_in_stock
FROM
 form_inventory fi
LEFT JOIN form_list_manufacturer mfr ON mfr.code = fi.manufacturer_id
LEFT JOIN form_ledger_inventory li ON (
fi.id = li.inventory_id
AND fi.deleted IS NOT TRUE
AND fi.archived IS NOT TRUE
AND li.deleted IS NOT TRUE
AND li.archived IS NOT TRUE
AND li.site_id = (SELECT site_id FROM temp_filtered_items)
)
WHERE
fi.type IS DISTINCT FROM 'Billable'
AND fi.type IN ('Equipment Rental', 'Supply')
AND fi.id NOT IN (SELECT inventory_id FROM temp_filtered_items)
GROUP BY
fi.auto_name, fi.id, fi.type, mfr.mfg, fi.formatted_ndc, fi.hcpc_code

UNION

-- Second part: Billable items
SELECT
fi.auto_name::TEXT as auto_name,  -- Explicit cast to TEXT
fi.type::TEXT,
fi.id,
NULL::TEXT as manufacture,
fi.hcpc_code::TEXT as code,
NULL::TEXT AS quantity_in_stock
FROM
 form_inventory fi
WHERE
fi.deleted IS NOT TRUE
AND fi.archived IS NOT TRUE
AND fi.type = 'Billable'
AND fi.id IN (SELECT inventory_id FROM temp_filtered_items)
GROUP BY
fi.auto_name, fi.id, fi.type, fi.hcpc_code;

-- Drop the temporary table
DROP TABLE IF EXISTS temp_filtered_items;
END;
$$ LANGUAGE plpgsql;
DO $$ BEGIN
  PERFORM drop_all_function_signatures('get_site_timestamp');
END $$;
CREATE OR REPLACE FUNCTION get_site_timestamp(
    p_site_id INTEGER,
    p_return_date BOOLEAN DEFAULT FALSE,
    p_utc_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
)
RETURNS TIMESTAMP WITH TIME ZONE AS $$
DECLARE
    local_time TIMESTAMP WITH TIME ZONE;
BEGIN
    RAISE LOG 'p_site_id: %', p_site_id;
    -- Convert provided UTC timestamp to the specified timezone
    local_time := p_utc_timestamp AT TIME ZONE (SELECT COALESCE(timezone_id::text, 'America/Los_Angeles') FROM form_site st WHERE st.id = p_site_id);
    
    -- Return as date or timestamp based on the parameter
    IF p_return_date THEN
        RETURN local_time::DATE;
    ELSE
        RETURN local_time;
    END IF;
END;
$$ LANGUAGE plpgsql;

DO $$ BEGIN
  PERFORM drop_all_function_signatures('get_filtered_spkt_items');
END $$;
CREATE OR REPLACE FUNCTION get_filtered_spkt_items(p_ticket_no TEXT, p_supply_kit_id INTEGER)
RETURNS TABLE (
    id INTEGER,
    inventory_id INTEGER,
    inventory_id_auto_name TEXT,
    bill TEXT,
    one_time_only TEXT,
    dispense_quantity_formatted TEXT,
    dispense_quantity NUMERIC,
    part_of_kit TEXT
) AS $$
BEGIN
    RAISE LOG 'p_ticket_no: %', p_ticket_no;
    RAISE LOG 'p_supply_kit_id: %', p_supply_kit_id;
    -- Create a temporary table to store the inventory_ids from the delivery ticket
    CREATE TEMP TABLE IF NOT EXISTS temp_filtered_items ON COMMIT DROP AS
    SELECT 
        dti.inventory_id
    FROM 
        form_careplan_dt_item dti
    JOIN 
        form_inventory fi ON dti.inventory_id = fi.id
    WHERE 
        dti.ticket_no = p_ticket_no
        AND dti.inventory_id IS NOT NULL
        AND dti.archived IS NOT TRUE 
        AND dti.deleted IS NOT TRUE;

    RETURN QUERY
SELECT
        ski.id as id,
        ski.inventory_id,
        inv.auto_name::text as inventory_id_auto_name,
        CASE 
        	WHEN ski.bill::text = 'Yes' THEN '✓' 
        	ELSE NULL 
        END as bill,
        CASE 
            WHEN ski.one_time_only = 'Yes' THEN '✓'
            ELSE NULL::text
        END as one_time_only,
        format_numeric(COALESCE(
            ski.dispense_quantity,
            0
        )::NUMERIC) AS dispense_quantity_formatted,
        ski.dispense_quantity::numeric as dispense_quantity,
        CASE 
            WHEN ski.part_of_kit = 'Yes' THEN '✓' 
            ELSE NULL 
            END as part_of_kit
    FROM
        form_inventory_supply_kit sk
        INNER JOIN sf_form_inventory_supply_kit_to_inventory_sk_item sfski ON sfski.form_inventory_supply_kit_fk = sk.id
        AND sfski."delete" IS NOT TRUE 
        AND sfski.archive IS NOT TRUE
        INNER JOIN form_inventory_sk_item ski ON ski.id = sfski.form_inventory_sk_item_fk
     	INNER JOIN form_inventory inv ON inv.id = ski.inventory_id
    WHERE
        inv.id NOT IN (SELECT tp.inventory_id FROM temp_filtered_items tp)
        AND sk.id = p_supply_kit_id;

    DROP TABLE IF EXISTS temp_filtered_items;
END;
$$ LANGUAGE plpgsql;