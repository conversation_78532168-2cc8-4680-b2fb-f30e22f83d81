
CREATE OR R<PERSON>LACE FUNCTION trigger_update_use_by_date_on_label()
RETURNS TRIGGER AS $$
DECLARE
    v_rx_id integer;
    v_is_primary_drug boolean := FALSE;
    v_min_exp_date DATE;
BEGIN
    /**
     * Updates the expiration date on pharmacy labels based on the lowest expiration date
     * of all pulled items for a delivery ticket item.
     * 
     * @param {UUID} NEW.ticket_no - The delivery ticket number
     * @param {UUID} NEW.ticket_item_no - The delivery ticket item number
     * @returns {trigger} - The trigger result
     * @throws {Exception} - If an error occurs during processing
     */
    
    -- Begin a transaction block for all operations
    BEGIN
        -- Get the rx_id from the delivery ticket item
        SELECT dti.rx_id,is_primary_drug = 'Yes' INTO v_rx_id, v_is_primary_drug
        FROM form_careplan_dt_item dti
        WHERE dti.ticket_no = NEW.ticket_no
        AND dti.ticket_item_no = NEW.ticket_item_no
        AND dti.archived IS NOT TRUE
        AND dti.deleted IS NOT TRUE;
        
        IF v_is_primary_drug IS FALSE THEN
            RAISE LOG 'Not a primary drug, skipping use by date update for ticket_no: % and ticket_item_no: %', NEW.ticket_no, NEW.ticket_item_no;
            RETURN NEW;
        END IF;
        
        -- Check if rx_id is null
        IF v_rx_id IS NULL THEN
            RAISE WARNING 'No rx_id found for ticket_no: % and ticket_item_no: %', NEW.ticket_no, NEW.ticket_item_no;
            RETURN NEW;
        END IF;
        
        -- Get the minimum expiration date for all pulled items for this ticket item
        SELECT MIN(wtp.expiration_date) INTO v_min_exp_date
        FROM form_careplan_dt_wt_pulled wtp
        WHERE wtp.ticket_no = NEW.ticket_no
        AND wtp.ticket_item_no = NEW.ticket_item_no
        AND wtp.archived IS NOT TRUE
        AND wtp.deleted IS NOT TRUE
        AND wtp.expiration_date IS NOT NULL;
        
        -- Check if min_exp_date is null
        IF v_min_exp_date IS NULL THEN
            RAISE LOG 'No expiration dates found for ticket_no: % and ticket_item_no: %', NEW.ticket_no, NEW.ticket_item_no;
            RETURN NEW;
        END IF;
        
        -- Update IV labels
        UPDATE form_careplan_order_lbl_iv oiv
        SET exp_date = v_min_exp_date
        FROM sf_form_careplan_order_rx_to_careplan_order_lbl_iv sfoiv
        WHERE sfoiv.form_careplan_order_rx_fk = v_rx_id
        AND sfoiv.form_careplan_order_lbl_iv_fk = oiv.id 
        AND sfoiv.archive IS NOT TRUE 
        AND sfoiv."delete" IS NOT TRUE;
        
        -- Update Syringe labels
        UPDATE form_careplan_order_lbl_syr syr
        SET exp_date = v_min_exp_date
        FROM sf_form_careplan_order_rx_to_careplan_order_lbl_syr sfsyr
        WHERE sfsyr.form_careplan_order_rx_fk = v_rx_id
        AND sfsyr.form_careplan_order_lbl_syr_fk = syr.id 
        AND sfsyr.archive IS NOT TRUE 
        AND sfsyr."delete" IS NOT TRUE;
        
        -- Update TPN labels
        UPDATE form_careplan_order_lbl_tpn tpn
        SET exp_date = v_min_exp_date
        FROM sf_form_careplan_order_rx_to_careplan_order_lbl_tpn sftpn
        WHERE sftpn.form_careplan_order_rx_fk = v_rx_id
        AND sftpn.form_careplan_order_lbl_tpn_fk = tpn.id 
        AND sftpn.archive IS NOT TRUE 
        AND sftpn."delete" IS NOT TRUE;

    EXCEPTION WHEN OTHERS THEN
        -- Log error
        INSERT INTO dispensing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in trigger_update_use_by_date_on_label',
            'FUNCTION',
            current_schema(),
            'form_careplan_dt_wt_pulled',
            jsonb_build_object(
                'function_name', 'trigger_update_use_by_date_on_label',
                'ticket_no', NEW.ticket_no,
                'ticket_item_no', NEW.ticket_item_no
            )
        );
        
        RAISE WARNING 'Failed to update use by date on labels: %', SQLERRM;
    END;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS update_use_by_date_on_label ON form_careplan_dt_wt_pulled;
CREATE TRIGGER update_use_by_date_on_label
    AFTER INSERT OR UPDATE
    ON form_careplan_dt_wt_pulled
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_use_by_date_on_label();
