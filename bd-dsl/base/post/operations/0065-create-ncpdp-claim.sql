
CREATE OR REPLACE FUNCTION build_ncpdp_claim(
  p_insurance_id integer,
  p_payer_id integer,
  p_patient_id integer,
  p_site_id integer,
  p_prescriber_id integer,
  p_charge_lines charge_line_with_split[],
  p_date_of_service date,
  p_day_supply integer DEFAULT NULL,
  p_previous_payer_id integer DEFAULT NULL,
  p_parent_claim_no text DEFAULT NULL,
  p_rx_no text DEFAULT NULL,
  p_order_item_id integer DEFAULT NULL,
  p_orderp_item_id integer DEFAULT NULL,
  p_transaction_code text DEFAULT 'B1',

  -- Optional Test Claim Parameters
  p_route_id text DEFAULT NULL,
  p_compound_code text DEFAULT NULL,
  p_compound_type text DEFAULT NULL,
  p_comp_dosage_form_code text DEFAULT NULL,
  p_comp_disp_unit text DEFAULT NULL,
  p_is_test BOOLEAN DEFAULT False,
  p_order_no text DEFAULT NULL
) RETURNS ncpdp_record AS $BODY$
DECLARE
  v_patient_segment ncpdp_patient[];
  v_prescriber_segment ncpdp_prescriber[];
  v_insurance_segment ncpdp_insurance[];
  v_claim_segment ncpdp_claim[];
  v_pricing_segment ncpdp_pricing[];
  v_clinical_segment ncpdp_clinical[];
  v_pharmacy_segment ncpdp_pharmacy[];
  v_cob_segment ncpdp_cob[];
  v_dur_segment ncpdp_dur[];
  v_narrative_segment ncpdp_narrative[];
  v_start_time timestamp;
  v_execution_time interval;
  v_result ncpdp_record;
  v_error_message text;
  v_params jsonb;
  v_charge_quantity numeric;
  v_ing_cst_sub numeric;
  v_dispense_fee numeric;
  v_u_and_c_charge numeric;
  v_pt_pd_amt_sub numeric;
  v_gross_amount_due numeric;
  v_treat_as_compound boolean;
  v_is_real_compound boolean;
  v_inventory_id integer;
  v_fill_number integer;
  v_claim_substatus text := NULL;
  v_order_id integer;
  v_rx_order_item_id integer;
  v_rx_orderp_item_id integer;
  v_comp_dsg_fm_code text;
  v_comp_disp_unit text;
  
  -- Variables for payer info
  v_payer_info record;
  v_never_comp_seg text;
  v_software_vendor_id text;
  v_svc_prov_id_qualifier text;
  v_svc_prov_id text;
  v_bin_number text;
  v_process_control_number text;
  v_service_date text;
  v_billed numeric;
  v_expected numeric;
  v_cost numeric;
  
  -- Claims data for COB
  v_parent_claim_data record;
  v_insurance_settings record;
  v_is_cob boolean := FALSE;
  v_parent_pt_pay_amt numeric;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build minimal parameters JSON for logging
  v_params := jsonb_build_object(
    'insurance_id', p_insurance_id,
    'payer_id', p_payer_id,
    'patient_id', p_patient_id,
    'site_id', p_site_id,
    'prescriber_id', p_prescriber_id,
    -- Skip charge_lines to avoid logging large JSON
    'date_of_service', p_date_of_service,
    'transaction_code', p_transaction_code,
    'parent_claim_no', p_parent_claim_no
  );

  BEGIN  -- Start exception block
    -- Log function call
    BEGIN
      PERFORM log_billing_function(
        'build_ncpdp_claim'::tracked_function,
        v_params::jsonb,
        NULL::jsonb,
        NULL::text,
        clock_timestamp() - v_start_time
      );
    EXCEPTION WHEN OTHERS THEN
      -- Ignore logging errors
      NULL;
    END;

    IF p_charge_lines IS NULL OR array_length(p_charge_lines, 1) = 0 THEN
      RAISE EXCEPTION 'Charge lines are required to build a claim';
    END IF;

    -- Validate required parameters
    IF p_insurance_id IS NULL AND p_transaction_code NOT IN ('E1') THEN
        RAISE EXCEPTION 'Insurance ID cannot be null';
    END IF;
    
    IF p_payer_id IS NULL AND p_transaction_code NOT IN ('E1') THEN
        RAISE EXCEPTION 'Payer ID cannot be null';
    END IF;

    IF p_site_id IS NULL THEN
        RAISE EXCEPTION 'Site ID cannot be null';
    END IF;

    IF p_patient_id IS NULL THEN
        RAISE EXCEPTION 'Patient ID cannot be null';
    END IF;

    IF p_prescriber_id IS NULL AND p_transaction_code NOT IN ('E1') THEN
        RAISE EXCEPTION 'Prescriber ID cannot be null';
    END IF;

    IF p_transaction_code IS NULL THEN
        RAISE EXCEPTION 'Transaction code cannot be null';
    END IF;
    
    -- Check if this is a COB claim
    v_is_cob := (p_parent_claim_no IS NOT NULL);
    
    -- Get insurance settings early since we need these for several calculations
    SELECT * INTO v_insurance_settings
    FROM get_insurance_claim_settings(p_insurance_id, p_site_id, NULL::integer);

    -- Get payer information directly from the tables
    SELECT 
      py.software_vendor_id,
      py.bin,
      py.pcn,
      CASE WHEN COALESCE(py.never_comp_seg, 'No') = 'Yes' THEN 'Yes' ELSE NULL END AS never_comp_seg,
      COALESCE(st.npi, '') AS pharmacy_id,
      COALESCE(py.ncpdp_pharmacy_qualifier_id, '01') AS pharmacy_qualifier_id
    INTO v_payer_info
    FROM form_payer py
    JOIN form_site st ON st.id = p_site_id
    WHERE py.id = p_payer_id
      AND py.archived IS NOT TRUE
      AND py.deleted IS NOT TRUE
      AND st.archived IS NOT TRUE
      AND st.deleted IS NOT TRUE;

    -- Store values from the query
    v_software_vendor_id := v_payer_info.software_vendor_id;
    v_bin_number := v_payer_info.bin;
    v_process_control_number := v_payer_info.pcn;
    v_never_comp_seg := v_payer_info.never_comp_seg;
    v_svc_prov_id := v_payer_info.pharmacy_id;
    v_svc_prov_id_qualifier := v_payer_info.pharmacy_qualifier_id;

    -- Format date of service once
    v_service_date := TO_CHAR(
        CASE
            WHEN p_date_of_service IS NOT NULL AND p_date_of_service > get_site_timestamp(p_site_id, TRUE)::date THEN get_site_timestamp(p_site_id, TRUE)::date
            ELSE COALESCE(p_date_of_service, get_site_timestamp(p_site_id, TRUE)::date)
        END,
        'MM/DD/YYYY'
    );

    -- For COB claims, get parent claim data early
    IF v_is_cob THEN
        -- Log that we're processing a COB claim
        RAISE LOG 'Processing COB claim with parent claim number: %', p_parent_claim_no;
        -- Get parent claim data - need this for COB calculation
        SELECT * INTO v_parent_claim_data
        FROM get_primary_ncpdp_claim_info(p_parent_claim_no);

        IF v_parent_claim_data IS NULL THEN
            RAISE LOG 'Warning: No parent claim data found for claim: %', p_parent_claim_no;
        ELSE
            RAISE LOG 'Parent claim data retrieved. pt_pay_amt: %', v_parent_claim_data.pt_pay_amt;
            v_parent_pt_pay_amt := COALESCE(v_parent_claim_data.pt_pay_amt, 0.0)::numeric;
        END IF;
    END IF;

    -- Get Rx information only once if we have an Rx number
    IF p_rx_no IS NOT NULL THEN
        SELECT
            co.id,
            rx.inventory_id,
            rx.order_item_id,
            rx.orderp_item_id,
            rx.comp_dsg_fm_code,
            rx.comp_disp_unit
        INTO
            v_order_id,
            v_inventory_id,
            v_rx_order_item_id,
            v_rx_orderp_item_id,
            v_comp_dsg_fm_code,
            v_comp_disp_unit
        FROM vw_rx_order rx
        JOIN form_careplan_order co ON co.order_no = rx.order_no
        WHERE rx.rx_no = p_rx_no;
    END IF;

    -- Determine compound status efficiently
    WITH compound_analysis AS (
        SELECT 
            COUNT(*) > 1 AND NOT EXISTS (
                SELECT 1 
                FROM unnest(p_charge_lines) cl 
                WHERE cl.compound_no IS NOT NULL
            ) AS treat_as_compound,
            EXISTS (
                SELECT 1 
                FROM unnest(p_charge_lines) cl 
                WHERE cl.compound_no IS NOT NULL
            ) AS is_real_compound
        FROM (SELECT 1) dummy
    )
    SELECT 
        treat_as_compound,
        is_real_compound
    INTO 
        v_treat_as_compound,
        v_is_real_compound
    FROM compound_analysis;

    -- Set inventory ID if not already set
    IF v_inventory_id IS NULL THEN
        IF v_is_real_compound AND p_rx_no IS NOT NULL THEN
            SELECT inventory_id INTO v_inventory_id
            FROM form_careplan_order_rx
            WHERE rx_no = p_rx_no
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE;
        ELSE
            -- If not a real compound, get inventory ID from charge lines
        WITH ranked_charges AS (
            SELECT cl.inventory_id AS inventory_id,
                  ROW_NUMBER() OVER (ORDER BY cl.billed DESC) AS rn
            FROM unnest(p_charge_lines) cl
            WHERE cl.inventory_id IS NOT NULL
        )
        SELECT inventory_id INTO v_inventory_id
        FROM ranked_charges
        WHERE rn = 1;
        END IF;
    END IF;

    IF v_inventory_id IS NULL THEN
        RAISE EXCEPTION 'Inventory ID cannot be null';
    END IF;

    -- Build and validate segments based on transaction code
    IF p_transaction_code IN ('B1', 'B3', 'S1', 'S3', 'E1') THEN
      -- Patient segment - Using correct parameter order from your implementation
      RAISE LOG 'SELECT build_patient_ncpdp_segment(%, %, %, %, %)',
        p_patient_id,
        p_insurance_id,
        p_previous_payer_id,
        p_site_id,
        p_transaction_code;
      BEGIN
        SELECT ARRAY_AGG(row) INTO v_patient_segment
        FROM build_patient_ncpdp_segment(p_patient_id, p_insurance_id, p_previous_payer_id, p_site_id, p_transaction_code) row;
      EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Error building patient segment: %', SQLERRM;
      END;

      -- Prescriber segment
      RAISE LOG 'SELECT build_prescriber_ncpdp_segment(%, %, %, %, %, %)',
        p_patient_id,
        p_prescriber_id,
        p_insurance_id,
        p_site_id,
        p_transaction_code,
        p_parent_claim_no;
      BEGIN
        SELECT ARRAY_AGG(row) INTO v_prescriber_segment
        FROM build_prescriber_ncpdp_segment(p_patient_id, p_prescriber_id, p_insurance_id, p_site_id, p_transaction_code, p_parent_claim_no) row;
      EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Error building prescriber segment: %', SQLERRM;
      END;
    END IF;

    -- Insurance segment
    BEGIN
      SELECT ARRAY_AGG(row) INTO v_insurance_segment
      FROM build_insurance_ncpdp_segment(p_insurance_id, p_site_id, p_transaction_code, p_previous_payer_id) row;
    EXCEPTION WHEN OTHERS THEN
      RAISE EXCEPTION 'Error building insurance segment: %', SQLERRM;
    END;

    IF p_transaction_code IN ('B1', 'B2', 'B3', 'S1', 'S2', 'S3') THEN
      -- Validate compound rules
      IF v_treat_as_compound AND v_is_real_compound THEN
          RAISE EXCEPTION 'Claim cannot be both compound and non-compound treated as compound';
      END IF;

      -- Process Fill # validation
      IF NOT p_is_test THEN
        -- Get first Fill #
        SELECT cl.fill_number INTO v_fill_number 
        FROM unnest(p_charge_lines) cl
        WHERE cl.fill_number IS NOT NULL
        LIMIT 1;
        
        -- Verify all Fill #s match
        IF EXISTS (
          SELECT 1
          FROM unnest(p_charge_lines) cl
          WHERE cl.fill_number IS NOT NULL
          AND cl.fill_number != v_fill_number
        ) THEN
          RAISE EXCEPTION 'All charge lines must have the same Fill #';
        END IF;

        IF v_fill_number IS NULL THEN
          RAISE EXCEPTION 'Fill # cannot be null';
        END IF;
      END IF;
    END IF;

    IF p_transaction_code NOT IN ('E1') THEN
      -- Calculate dispense quantity
      SELECT calculate_ncpdp_charge_lines_dispense_qty(
        v_treat_as_compound, v_is_real_compound, p_charge_lines, p_is_test
      ) INTO v_charge_quantity;
    
      BEGIN
        SELECT ARRAY_AGG(row) INTO v_claim_segment
        FROM build_claim_ncpdp_segment(
          p_insurance_id, p_payer_id, p_patient_id, p_site_id, p_prescriber_id,  
          p_date_of_service, p_day_supply, v_inventory_id, v_charge_quantity, p_previous_payer_id,
          p_parent_claim_no, p_rx_no, COALESCE(p_order_item_id, v_rx_order_item_id), 
          COALESCE(p_orderp_item_id, v_rx_orderp_item_id), v_fill_number, p_transaction_code,
          v_is_real_compound, v_treat_as_compound, p_route_id, p_compound_code, p_compound_type, p_is_test
        ) row;
      EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Error building claim segment: %', SQLERRM;
      END;

      -- Calculate these totals directly from charge lines
      SELECT
        SUM(COALESCE(cl.billed, 0))::numeric AS ing_cst_sub,
        SUM(COALESCE(cl.expected, 0))::numeric AS expected,
        SUM(COALESCE(cl.total_cost, 0))::numeric AS cost,
        SUM(COALESCE(cl.dispense_fee, 0))::numeric AS dispense_fee,
        SUM(COALESCE(cl.list_price, 0))::numeric AS u_and_c_charge,
        SUM(COALESCE(cl.pt_pd_amt_sub, 0))::numeric AS pt_pd_amt_sub,
        SUM(COALESCE(cl.gross_amount_due, 0))::numeric AS gross_amount_due
      INTO
        v_ing_cst_sub,
        v_expected,
        v_cost,
        v_dispense_fee,
        v_u_and_c_charge,
        v_pt_pd_amt_sub,
        v_gross_amount_due
      FROM unnest(p_charge_lines) AS cl;
      
      -- Use default values if null
      v_ing_cst_sub := COALESCE(v_ing_cst_sub, 0);
      v_expected := COALESCE(v_expected, 0);
      v_cost := COALESCE(v_cost, 0);
      v_dispense_fee := COALESCE(v_dispense_fee, 0);
      v_u_and_c_charge := COALESCE(v_u_and_c_charge, 0);
      v_pt_pd_amt_sub := COALESCE(v_pt_pd_amt_sub, 0);
      v_gross_amount_due := COALESCE(v_gross_amount_due, 0);
      
      -- Apply COB specific logic for secondary claims
      IF v_is_cob THEN
        RAISE LOG 'Applying COB rules for ingredient cost: %, gross amount due: %, parent pt pay amt: %',
          v_insurance_settings.ncpdp_sec_claims_ingred_cost,
          v_insurance_settings.ncpdp_sec_claims_gross_amount_due,
          v_parent_pt_pay_amt;
        
        -- For ingredient cost
        IF v_insurance_settings.ncpdp_sec_claims_ingred_cost = 'Send Zeros' THEN
          RAISE LOG 'Setting ing_cst_sub to zero based on insurance settings';
          v_ing_cst_sub := 0.0;
        ELSIF v_insurance_settings.ncpdp_sec_claims_ingred_cost = 'Send Co-pay Amount' AND v_parent_pt_pay_amt > 0 THEN
          RAISE LOG 'Setting ing_cst_sub to parent pt_pay_amt: %', v_parent_pt_pay_amt;
          v_ing_cst_sub := v_parent_pt_pay_amt;
        END IF;
        
        -- For gross amount due
        IF v_insurance_settings.ncpdp_sec_claims_gross_amount_due = 'Send Zeros' THEN
          RAISE LOG 'Setting gross_amount_due to zero based on insurance settings';
          v_gross_amount_due := 0.0;
        ELSIF v_insurance_settings.ncpdp_sec_claims_gross_amount_due = 'Send Co-pay Amount' AND v_parent_pt_pay_amt > 0 THEN
          RAISE LOG 'Setting gross_amount_due to parent pt_pay_amt: %', v_parent_pt_pay_amt;
          v_gross_amount_due := v_parent_pt_pay_amt;
        END IF;
      END IF;
      
      -- Build pricing segment
      BEGIN
        SELECT ARRAY_AGG(row) INTO v_pricing_segment
        FROM build_pricing_ncpdp_segment(
          p_patient_id, 
          p_insurance_id, 
          p_payer_id, 
          p_site_id,
          v_ing_cst_sub,
          v_pt_pd_amt_sub,
          v_dispense_fee,
          v_u_and_c_charge,
          v_gross_amount_due,
          p_previous_payer_id, 
          p_transaction_code, 
          p_is_test, 
          p_parent_claim_no
        ) row;
      EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Error building pricing segment: %', SQLERRM;
      END;
    END IF;

    -- Clinical segment
    IF COALESCE(v_insurance_settings.send_dx_code, 'No') = 'Yes' THEN
    BEGIN
      SELECT ARRAY_AGG(row) INTO v_clinical_segment
      FROM build_clinical_ncpdp_segment(
        p_patient_id, p_insurance_id, p_site_id, p_rx_no, 
        COALESCE(p_order_item_id, v_rx_order_item_id), 
        COALESCE(p_orderp_item_id, v_rx_orderp_item_id), 
        p_previous_payer_id, p_parent_claim_no
      ) row
      WHERE row IS NOT NULL;
    EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Error building clinical segment: %', SQLERRM;
      END;
    END IF;

    -- COB segment - FIX THE AMBIGUOUS COLUMN REFERENCE
    IF p_transaction_code IN ('B1', 'B3', 'S1', 'S3') AND p_parent_claim_no IS NOT NULL THEN
      RAISE LOG 'Building COB segment for parent claim: %', p_parent_claim_no;
      BEGIN
        SELECT ARRAY_AGG(row) INTO v_cob_segment
        FROM build_cob_ncpdp_segment(
          p_patient_id, p_insurance_id, p_site_id, p_transaction_code, p_parent_claim_no
        ) row;
      EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Error building COB segment: %', SQLERRM;
      END;
    END IF;

    -- DUR segment
    IF p_transaction_code IN ('B1', 'B3') AND p_parent_claim_no IS NOT NULL THEN
      BEGIN
        SELECT ARRAY_AGG(row) INTO v_dur_segment
        FROM build_dur_ncpdp_segment(
          p_patient_id, p_parent_claim_no, p_transaction_code
        ) row;
      EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Error building DUR segment: %', SQLERRM;
      END;
    END IF;

    -- Pharmacy Segment
    BEGIN
      SELECT ARRAY_AGG(row) INTO v_pharmacy_segment
      FROM build_pharmacy_ncpdp_segment(
        p_insurance_id, p_previous_payer_id, p_patient_id, p_site_id
      ) row;
    EXCEPTION WHEN OTHERS THEN
      RAISE EXCEPTION 'Error building pharmacy segment: %', SQLERRM;
    END;

    -- Narrative segment
    IF p_transaction_code IN ('B1', 'B3') AND p_parent_claim_no IS NOT NULL THEN
      SELECT ARRAY_AGG((nar.narrative_message::text)::ncpdp_narrative)
      INTO v_narrative_segment
      FROM form_ncpdp pclaim
      INNER JOIN sf_form_ncpdp_to_ncpdp_narrative sfn ON sfn.form_ncpdp_fk = pclaim.id
      AND sfn.delete IS NOT TRUE 
      AND sfn.archive IS NOT TRUE
      INNER JOIN form_ncpdp_narrative nar ON nar.id = sfn.form_ncpdp_narrative_fk
      AND nar.archived IS NOT TRUE
      AND nar.deleted IS NOT TRUE
      WHERE pclaim.claim_no = p_parent_claim_no
      AND pclaim.archived IS NOT TRUE
      AND pclaim.deleted IS NOT TRUE;
      
    ELSE
      v_narrative_segment := NULL;
    END IF;

    -- Get claim substatus
    IF NOT p_is_test THEN
      SELECT
        CASE
          WHEN COALESCE(ins.bill_for_denial, 'No') = 'Yes' THEN '108'
          ELSE '101'
        END
      INTO v_claim_substatus
      FROM form_patient_insurance ins
      INNER JOIN form_payer py ON py.id = ins.payer_id
      AND py.archived IS NOT TRUE
      AND py.deleted IS NOT TRUE
      WHERE ins.id = p_insurance_id
      AND ins.archived IS NOT TRUE
      AND ins.deleted IS NOT TRUE
      LIMIT 1;
    END IF;
    -- Build final result
    SELECT
      CASE WHEN p_is_test THEN 'Yes' ELSE NULL END::text AS is_test,
      'CLAIM_NO_PLACEHOLDER'::text AS claim_no,
      p_parent_claim_no::text AS parent_claim_no,
      v_never_comp_seg::text AS never_comp_seg,
      v_order_id::integer AS order_id,
      p_order_no::text AS order_no,
      COALESCE(p_order_item_id, v_rx_order_item_id)::integer AS order_item_id,
      COALESCE(p_orderp_item_id, v_rx_orderp_item_id)::integer AS orderp_item_id,
      p_rx_no::text AS rx_no,
      COALESCE(p_comp_dosage_form_code, v_comp_dsg_fm_code)::text AS comp_dosage_form_code,
      COALESCE(p_comp_disp_unit, v_comp_disp_unit)::text AS comp_disp_unit,
      v_inventory_id::integer AS inventory_id,
      v_claim_substatus::text AS substatus_id,
      p_site_id::integer AS site_id,
      p_patient_id::integer AS patient_id,
      p_insurance_id::integer AS insurance_id,
      p_payer_id::integer AS payer_id,
      v_software_vendor_id::text AS software_vendor_id,
      'D0'::text AS version_release_number,
      v_svc_prov_id_qualifier::text AS svc_prov_id_qualifier,
      v_svc_prov_id::text AS svc_prov_id,
      p_transaction_code::text AS transaction_code,
      v_bin_number::text AS bin_number,
      v_process_control_number::text AS process_control_number,
      TO_DATE(v_service_date, 'MM/DD/YYYY')::date AS service_date,
      v_patient_segment::ncpdp_patient[] AS segment_patient,
      v_prescriber_segment::ncpdp_prescriber[] AS segment_prescriber,
      v_pharmacy_segment::ncpdp_pharmacy[] AS segment_pharmacy,
      v_insurance_segment::ncpdp_insurance[] AS segment_insurance,
      v_claim_segment::ncpdp_claim[] AS segment_claim,
      v_pricing_segment::ncpdp_pricing[] AS segment_pricing,
      v_clinical_segment::ncpdp_clinical[] AS segment_clinical,
      v_narrative_segment::ncpdp_narrative[] AS segment_narrative,
      v_cob_segment::ncpdp_cob[] AS segment_cob,
      CASE WHEN v_cob_segment IS NOT NULL THEN 'Yes' ELSE NULL END::text AS is_cob,
      v_dur_segment::ncpdp_dur[] AS segment_dur,
      v_ing_cst_sub::numeric AS ing_cst_sub,
      v_expected::numeric AS expected,
      v_cost::numeric AS cost
      INTO v_result;

    -- Log successful completion
    BEGIN
      PERFORM log_billing_function(
        'build_ncpdp_claim'::tracked_function,
        v_params,
        jsonb_build_object(
          'claim_no', 'CLAIM_NO_PLACEHOLDER',
          'insurance_id', p_insurance_id,
          'patient_id', p_patient_id,
          'transaction_code', p_transaction_code,
          'parent_claim_no', p_parent_claim_no,
          'is_cob', v_is_cob
        ),
        NULL::text,
        clock_timestamp() - v_start_time
      );
    EXCEPTION WHEN OTHERS THEN
      -- Ignore logging errors
      NULL;
    END;

    RETURN v_result;
  EXCEPTION WHEN OTHERS THEN
    -- Log error
    v_error_message := SQLERRM;
    
    -- Log to billing error log
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        v_error_message,
        'Exception in build_ncpdp_claim',
        'FUNCTION',
        current_schema(),
        'form_ncpdp',
        v_params
    );

    -- Log to NCPDP function log
    BEGIN
      PERFORM log_billing_function(
        'build_ncpdp_claim'::tracked_function,
        v_params::jsonb,
        NULL::jsonb,
        v_error_message::text,
        clock_timestamp() - v_start_time
      );
    EXCEPTION WHEN OTHERS THEN
      -- Ignore logging errors
      NULL;
    END;
    
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;