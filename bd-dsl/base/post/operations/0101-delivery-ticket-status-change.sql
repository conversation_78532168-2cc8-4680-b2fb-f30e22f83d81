CREATE OR REPLACE FUNCTION update_delivery_ticket_status()
R<PERSON>UR<PERSON> trigger AS $$
DECLARE
    v_has_assoc_invoices boolean;
    v_has_unposted_revenue boolean := FALSE;
    v_has_assoc_charge_lines boolean;
    v_rx_is_specialty boolean;
    v_dt_is_supplies_only boolean := TRUE;
    v_pulled_all_items boolean := FALSE;
    v_user_id integer;
    v_items_pulled_by_pharmacist boolean := FALSE;
    v_ready_to_fill boolean := FALSE;
    v_ready_to_verify boolean := FALSE;
    v_ready_to_confirm boolean := FALSE;
    v_ready_to_bill boolean := FALSE;
    v_is_confirmed boolean := FALSE;
    v_billed boolean := FALSE;
    v_status text;
    v_verified text;
    v_verified_datetime timestamp;
    v_verified_by integer;
    v_pharm_signature text;
    v_confirmed text;
    v_confirmed_datetime timestamp;
    v_confirmed_by integer;
    v_tech_verified text;
    v_tech_verified_datetime timestamp;
    v_tech_verified_by integer;
    v_tech_signature text;
    v_requires_assessment text;
    v_show_verification text;
    v_show_labels text;
    v_archived boolean;
    v_trigger_bypass boolean := FALSE;
BEGIN
    -- Check if the trigger is bypassed
    IF current_setting('careplan_delivery_tick.trigger_bypass', TRUE) = 'true' THEN
        RETURN NEW;
    END IF;

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    IF COALESCE(OLD.void, 'No') = 'Yes' OR OLD.archived IS TRUE THEN
        RAISE EXCEPTION 'Cannot modify a voided ticket';
    END IF;

    SELECT COUNT(*) > 0 INTO v_has_assoc_invoices
    FROM form_billing_invoice bi
    WHERE bi.delivery_ticket_id = NEW.id
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes'
    AND bi.archived IS NOT TRUE
    AND bi.deleted IS NOT TRUE;

    IF v_has_assoc_invoices THEN
        SELECT COUNT(*) > 0 INTO v_has_unposted_revenue
        FROM form_billing_invoice bi
        WHERE bi.delivery_ticket_id = NEW.id
        AND COALESCE(bi.void, 'No') <> 'Yes'
        AND COALESCE(bi.zeroed, 'No') <> 'Yes'
        AND bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
        AND bi.revenue_accepted_posted IS NULL;
    END IF;

    SELECT COUNT(*) > 0 INTO v_has_assoc_charge_lines
    FROM form_ledger_charge_line lgl
    INNER JOIN form_billing_invoice bi ON bi.invoice_no = lgl.invoice_no
    WHERE lgl.ticket_no = NEW.ticket_no
    AND COALESCE(lgl.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes'
    AND lgl.archived IS NOT TRUE
    AND lgl.deleted IS NOT TRUE;

    SELECT CASE 
        WHEN COALESCE(rx.is_specialty, 'No') = 'Yes' THEN TRUE 
        ELSE FALSE
    END INTO v_rx_is_specialty
    FROM vw_delivery_items disp
    INNER JOIN form_careplan_order_rx rx ON rx.id = disp.rx_id
    WHERE disp.delivery_ticket_id = NEW.id
    ORDER BY CASE WHEN COALESCE(rx.is_specialty, 'No') = 'Yes' THEN 1 ELSE 0 END DESC
    LIMIT 1;

    SELECT COUNT(*) = 0 INTO v_dt_is_supplies_only
    FROM vw_delivery_items disp
    WHERE disp.delivery_ticket_id = NEW.id
    AND disp.inventory_type IN ('Drug', 'Compound');

    SELECT 
        COUNT(*) = 0 INTO v_pulled_all_items
    FROM form_careplan_dt_item dt_item
    LEFT JOIN (
        SELECT 
            ticket_no,
            ticket_item_no,
            SUM(dispensed_quantity) as total_dispensed
        FROM form_careplan_dt_wt_pulled wt
        WHERE COALESCE(wt.void, 'No') = 'No'
        AND wt.archived IS NOT TRUE
        AND wt.deleted IS NOT TRUE
        GROUP BY ticket_no, ticket_item_no
    ) pulled ON dt_item.ticket_no = pulled.ticket_no 
            AND dt_item.ticket_item_no = pulled.ticket_item_no
    WHERE dt_item.ticket_no = NEW.ticket_no
    AND dt_item.archived IS NOT TRUE
    AND dt_item.deleted IS NOT TRUE
    AND dt_item.type IN ('Drug', 'Compound', 'Equipment Rental')
    AND (COALESCE(dt_item.dispense_quantity, 0) != COALESCE(pulled.total_dispensed, 0) OR pulled.total_dispensed IS NULL);

    -- Check if record was previously confirmed
    IF COALESCE(OLD.confirmed, 'No') = 'Yes' AND (v_has_assoc_invoices OR v_has_assoc_charge_lines) THEN
        -- Block changes to confirmed status
        IF COALESCE(NEW.confirmed, 'No') IS DISTINCT FROM COALESCE(OLD.confirmed, 'No') THEN
            RAISE EXCEPTION 'Cannot modify confirmed status once delivery ticket is confirmed';
        END IF;
    END IF;

    IF COALESCE(OLD.tech_verified, 'No') <> 'Yes' AND COALESCE(NEW.tech_verified, 'No') = 'Yes' THEN
        SELECT COUNT(*) > 0 INTO v_items_pulled_by_pharmacist
        FROM form_list_rph_labels rph
        WHERE rph.user_id = NEW.tech_verified_by;
        IF v_items_pulled_by_pharmacist THEN 
            v_verified := 'Yes';
            v_verified_datetime := NEW.tech_verified_datetime;
            v_verified_by := NEW.tech_verified_by;
            v_pharm_signature := NEW.tech_signature;
        ELSE
            v_verified := NEW.verified;
            v_verified_datetime := NEW.verified_datetime;
            v_verified_by := NEW.verified_by;
            v_pharm_signature := NEW.pharm_signature;
        END IF;
    ELSIF COALESCE(OLD.tech_verified, 'No') = 'Yes' AND COALESCE(NEW.tech_verified, 'No') <> 'Yes' THEN
        v_verified := NULL;
        v_verified_datetime := NULL;
        v_verified_by := NULL;
        v_pharm_signature := NULL;
    ELSE
        v_verified := NEW.verified;
        v_verified_datetime := NEW.verified_datetime;
        v_verified_by := NEW.verified_by;
        v_pharm_signature := NEW.pharm_signature;
    END IF;

    v_ready_to_fill := COALESCE(NEW.ready_to_fill, 'No') = 'Yes';
    v_ready_to_verify := COALESCE(NEW.tech_verified, 'No') = 'Yes' AND v_ready_to_fill AND v_pulled_all_items;
    v_ready_to_confirm := (COALESCE(NEW.verified, 'No') = 'Yes' AND v_ready_to_verify) OR (v_dt_is_supplies_only AND v_ready_to_verify);
    v_is_confirmed := COALESCE(NEW.confirmed, 'No') = 'Yes' AND v_has_assoc_invoices IS FALSE AND NOT v_has_unposted_revenue;
    v_ready_to_bill := COALESCE(NEW.confirmed, 'No') = 'Yes' AND v_has_unposted_revenue AND v_ready_to_confirm;
    v_billed := COALESCE(NEW.confirmed, 'No') = 'Yes' AND v_has_assoc_invoices AND NOT v_has_unposted_revenue AND v_ready_to_confirm;
    v_requires_assessment := CASE WHEN v_dt_is_supplies_only THEN NULL ELSE 'Yes' END;
    v_show_verification := CASE WHEN v_dt_is_supplies_only OR v_items_pulled_by_pharmacist THEN NULL ELSE 'Yes' END;
    v_show_labels := CASE WHEN v_dt_is_supplies_only THEN NULL ELSE 'Yes' END;

    IF COALESCE(NEW.void, 'No') = 'Yes' THEN
        IF v_has_assoc_invoices THEN
            RAISE EXCEPTION 'Cannot void delivery ticket with associated invoices';
        END IF;
        IF v_has_assoc_charge_lines THEN
            RAISE EXCEPTION 'Cannot void delivery ticket with associated charges lines attached to an invoice';
        END IF;

        v_status := 'voided';
        v_archived := TRUE;
    ELSIF v_billed THEN
        v_status := 'billed';
        v_archived := NEW.archived;
    ELSIF v_ready_to_bill THEN
        v_status := 'ready_to_bill';
        v_archived := NEW.archived;
    ELSIF v_is_confirmed THEN
        v_status := 'confirmed';
        v_archived := NEW.archived;
    ELSIF v_ready_to_confirm THEN
        v_status := 'pending_conf';
        v_confirmed := NULL;
        v_confirmed_datetime := NULL;
        v_confirmed_by := NULL;
        v_archived := NEW.archived;
    ELSIF v_ready_to_verify THEN
        v_status := 'order_ver';
        v_verified := NULL;
        v_verified_datetime := NULL;
        v_verified_by := NULL;
        v_pharm_signature := NULL;
        v_archived := NEW.archived;
    ELSIF v_ready_to_fill THEN
        v_status := 'ready_to_fill';
        v_tech_verified := NULL;
        v_tech_verified_datetime := NULL;
        v_tech_verified_by := NULL;
        v_tech_signature := NULL;
        v_archived := NEW.archived;
    ELSE
        v_status := 'delivery_ticket';
        v_archived := NEW.archived;
    END IF;

    -- Initialize confirmed fields if not set yet
    IF v_confirmed IS NULL AND (v_is_confirmed OR v_ready_to_bill OR v_billed) THEN
        v_confirmed := NEW.confirmed;
        v_confirmed_datetime := NEW.confirmed_datetime;
        v_confirmed_by := NEW.confirmed_by;
    END IF;

    -- Initialize tech verified fields if not set yet
    IF v_tech_verified IS NULL THEN
        v_tech_verified := NEW.tech_verified;
        v_tech_verified_datetime := NEW.tech_verified_datetime;
        v_tech_verified_by := NEW.tech_verified_by;
        v_tech_signature := NEW.tech_signature;
    END IF;

    -- Check if any values have changed before performing an update to avoid trigger loop
    IF v_status IS DISTINCT FROM NEW.status OR
       v_verified IS DISTINCT FROM NEW.verified OR
       v_verified_datetime IS DISTINCT FROM NEW.verified_datetime OR
       v_verified_by IS DISTINCT FROM NEW.verified_by OR
       v_pharm_signature IS DISTINCT FROM NEW.pharm_signature OR
       v_confirmed IS DISTINCT FROM NEW.confirmed OR
       v_confirmed_datetime IS DISTINCT FROM NEW.confirmed_datetime OR
       v_confirmed_by IS DISTINCT FROM NEW.confirmed_by OR
       v_tech_verified IS DISTINCT FROM NEW.tech_verified OR
       v_tech_verified_datetime IS DISTINCT FROM NEW.tech_verified_datetime OR
       v_tech_verified_by IS DISTINCT FROM NEW.tech_verified_by OR
       v_tech_signature IS DISTINCT FROM NEW.tech_signature OR
       v_requires_assessment IS DISTINCT FROM NEW.requires_assessment OR
       v_show_verification IS DISTINCT FROM NEW.show_verification OR
       v_show_labels IS DISTINCT FROM NEW.show_labels OR
       v_archived IS DISTINCT FROM NEW.archived THEN

        -- Set configuration to bypass trigger on our update
        PERFORM set_config('careplan_delivery_tick.trigger_bypass', 'true', FALSE);

        -- Update the record with new values
        UPDATE form_careplan_delivery_tick
        SET status = v_status,
            verified = v_verified,
            verified_datetime = v_verified_datetime,
            verified_by = v_verified_by,
            pharm_signature = v_pharm_signature,
            confirmed = v_confirmed,
            confirmed_datetime = v_confirmed_datetime,
            confirmed_by = v_confirmed_by,
            tech_verified = v_tech_verified,
            tech_verified_datetime = v_tech_verified_datetime,
            tech_verified_by = v_tech_verified_by,
            tech_signature = v_tech_signature,
            requires_assessment = v_requires_assessment,
            show_verification = v_show_verification,
            show_labels = v_show_labels,
            archived = v_archived
        WHERE id = NEW.id AND COALESCE(NEW.void, 'No') <> 'Yes' AND COALESCE(NEW.status, 'delivery_ticket') <> 'voided';

        -- Reset configuration after update
        PERFORM set_config('careplan_delivery_tick.trigger_bypass', 'false', FALSE);
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE TRIGGER update_delivery_ticket_status
AFTER UPDATE ON form_careplan_delivery_tick
FOR EACH ROW
EXECUTE FUNCTION update_delivery_ticket_status();
