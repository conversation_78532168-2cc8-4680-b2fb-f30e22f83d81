CREATE OR REPLACE FUNCTION build_mm_dependent_segment(
  p_patient_id integer,
  p_insurance_id integer
) RETURNS mm_dependent_segment AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_dependent_segment;
  v_error_message text;
  v_params jsonb;
  v_is_patient_subscriber boolean := TRUE;
  v_address_info mm_address_info;
  v_contact_info mm_patient_contact_info;
BEGIN
  -- Check for null input parameters
  IF p_patient_id IS NULL THEN
    RAISE EXCEPTION 'Patient ID cannot be NULL';
  END IF;

  IF p_insurance_id IS NULL THEN
    RAISE EXCEPTION 'Insurance ID cannot be NULL';
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'patient_id', p_patient_id,
    'insurance_id', p_insurance_id
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
      'build_mm_dependent_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM dependent segment for patient ID: %, insurance ID: %', p_patient_id, p_insurance_id;

    -- Check if patient is the subscriber (medical_relationship_id = '18' means self)
    SELECT COALESCE(pi.medical_relationship_id, '18') = '18'
    INTO v_is_patient_subscriber
    FROM form_patient_insurance pi
    WHERE pi.id = p_insurance_id
      AND pi.deleted IS NOT TRUE 
      AND pi.archived IS NOT TRUE;

    -- Only build dependent segment if patient is NOT the subscriber
    IF NOT v_is_patient_subscriber THEN
        -- Get dependent information from patient record
        SELECT 
            pi.medical_relationship_id::text,
            pi.cardholder_id::text,
            pt.firstname::text,
            pt.lastname::text,
            pt.middlename::text,
            TO_CHAR(pt.dob, 'MM/DD/YYYY')::text,
            CASE 
                WHEN pt.gender = 'Male' THEN 'M'
                WHEN pt.gender = 'Female' THEN 'F'
                ELSE 'U'
            END::text
        INTO 
            v_result.relationship_to_subscriber_code,
            v_result.member_id,
            v_result.first_name,
            v_result.last_name,
            v_result.middle_name,
            v_result.date_of_birth,
            v_result.gender
        FROM form_patient_insurance pi
        INNER JOIN form_patient pt ON pt.id = pi.patient_id
        WHERE pi.id = p_insurance_id
          AND pi.deleted IS NOT TRUE 
          AND pi.archived IS NOT TRUE
          AND pt.deleted IS NOT TRUE 
          AND pt.archived IS NOT TRUE;

        -- Build address from patient address, prioritizing Home over Shipping
        WITH pa AS (
            SELECT *
            FROM form_patient_address
            WHERE patient_id = p_patient_id
              AND address_type IN ('Home', 'Shipping')
              AND deleted IS NOT TRUE
              AND archived IS NOT TRUE
            ORDER BY 
                CASE WHEN address_type = 'Home' THEN 1
                     WHEN address_type = 'Shipping' THEN 2
                     ELSE 3 END,
                created_on DESC NULLS LAST
            LIMIT 1
        )
        SELECT 
            pa.street::text,
            pa.street2::text,
            pa.city::text,
            pa.state_id::text,
            pa.zip::text
        INTO 
            v_address_info.address1,
            v_address_info.address2,
            v_address_info.city,
            v_address_info.state,
            v_address_info.postal_code
        FROM pa;

        -- Build contact information from patient
        SELECT 
            (pt.firstname || COALESCE(' ' || pt.middlename, '') || ' ' || pt.lastname)::text,
            pt.email::text,
            COALESCE(
                CASE pt.phone_primary 
                    WHEN 'Cell Phone' THEN pt.phone_cell
                    WHEN 'Home Phone' THEN pt.phone_home
                    WHEN 'Work Phone' THEN pt.phone_work
                    ELSE pt.phone_cell
                END,
                pt.phone_home,
                pt.phone_work
            )::text
        INTO 
            v_contact_info.name,
            v_contact_info.email,
            v_contact_info.phone_number
        FROM form_patient pt
        WHERE pt.id = p_patient_id
          AND pt.deleted IS NOT TRUE 
          AND pt.archived IS NOT TRUE;

        -- Validate that we found the insurance record
        IF v_result.relationship_to_subscriber_code IS NULL THEN
            RAISE EXCEPTION 'Insurance or patient record not found for patient ID: %, insurance ID: %', p_patient_id, p_insurance_id;
        END IF;

        -- Set address and contact arrays
        v_result.address := ARRAY[v_address_info];
        v_result.contact_information := ARRAY[v_contact_info];

    ELSE
        -- Patient is subscriber, return NULL (no dependent segment needed)
        v_result := NULL;
    END IF;

    RAISE LOG 'MM dependent segment build result: %', v_result;

    -- Log success
    PERFORM log_billing_function(
      'build_mm_dependent_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_dependent_segment',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'build_mm_dependent_segment',
        'patient_id', p_patient_id,
        'insurance_id', p_insurance_id
      )
    );

    PERFORM log_billing_function(
      'build_mm_dependent_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;