CREATE OR REPLACE FUNCTION build_mm_1500_claim(
  p_insurance_id integer,
  p_payer_id integer,
  p_patient_id integer,
  p_site_id integer,
  p_date_of_service date,
  p_date_of_service_end date,
  p_charge_lines charge_line_with_split[],
  p_parent_claim_no text DEFAULT NULL,
  p_patient_prescriber_id integer DEFAULT NULL
) RETURNS mm_1500_claim_record AS $BODY$
DECLARE
  v_service_lines mm_1500_service_line_info[];
  v_diagnoses mm_1500_diagnosis_info[];
  v_start_time timestamp;
  v_result mm_1500_claim_record;
  v_error_message text;
  v_params jsonb;
  v_is_cob boolean := FALSE;
  v_claim_no text := 'CLAIM_NO_PLACEHOLDER';
  v_status text := NULL;
  v_expected numeric := 0.0;
  v_billed numeric := 0.0;
  v_paid numeric := 0.0;
  v_substatus_id text;
  v_future_service_date boolean := FALSE;

  -- Segment data variables
  v_patient_segment record;
  v_subscriber_segment record;
  v_referring_provider_segment record;
  v_billing_provider_segment record;
  v_dates_segment record;
  v_cob_segment record;
  
  -- New calculated field variables
  v_patient_account_number text;
  v_referring_provider_qualifier text;
  v_referring_provider_alt_id text;
  v_bill_tax_id text;
  v_bill_alt_provider_id text;
  v_patient_signature_date text;
  v_payer_rec record;
  v_physician_rec record;
  v_site_rec record;
  v_patient_rec record;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'parent_claim_no', p_parent_claim_no,
    'insurance_id', p_insurance_id,
    'payer_id', p_payer_id,
    'patient_id', p_patient_id,
    'site_id', p_site_id,
    'date_of_service', p_date_of_service,
    'date_of_service_end', p_date_of_service_end,
    'patient_prescriber_id', p_patient_prescriber_id,
    'claim_no', 'CLAIM_NO_PLACEHOLDER'
  );

  BEGIN  -- Start exception block
    -- Log function call
    PERFORM log_billing_function(
      'build_mm_1500_claim'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM 1500 claim for patient %, payer %, site %', p_patient_id, p_payer_id, p_site_id;

    -- Validate required parameters
    IF p_charge_lines IS NULL OR array_length(p_charge_lines, 1) = 0 THEN
      RAISE EXCEPTION 'Charge lines are required to build a claim';
    END IF;

    IF p_insurance_id IS NULL THEN
        RAISE EXCEPTION 'Insurance ID cannot be null';
    END IF;
    
    IF p_payer_id IS NULL THEN
        RAISE EXCEPTION 'Payer ID cannot be null';
    END IF;

    IF p_site_id IS NULL THEN
        RAISE EXCEPTION 'Site ID cannot be null';
    END IF;

    IF p_patient_id IS NULL THEN
        RAISE EXCEPTION 'Patient ID cannot be null';
    END IF;

    -- Check maximum service lines constraint (CMS-1500 limit)
    IF array_length(p_charge_lines, 1) > 50 THEN
        RAISE EXCEPTION 'Cannot have more than 50 service lines on CMS-1500, found %', array_length(p_charge_lines, 1);
    END IF;

    -- Check for future service dates if charge lines provided
    IF p_charge_lines IS NOT NULL AND array_length(p_charge_lines, 1) > 0 THEN
        SELECT EXISTS(
            SELECT 1 
            FROM unnest(p_charge_lines) cl
            WHERE cl.date_of_service_end::date > get_site_timestamp(p_site_id)::date
        )::boolean INTO v_future_service_date;

        -- Set substatus for future dates (simplified logic for now)
        IF v_future_service_date THEN
            v_substatus_id := '114'; -- Hold for future date
        ELSE
          v_substatus_id := '101'; -- Ready to Bill
        END IF;
    END IF;

    -- Check if this is a COB claim
    v_is_cob := (p_parent_claim_no IS NOT NULL);

    -- Calculate expected amount from charge lines
    SELECT SUM(cl.expected), SUM(cl.billed)
    INTO v_expected, v_billed
    FROM unnest(p_charge_lines) cl;

    v_expected := COALESCE(v_expected, 0.0);
    v_billed := COALESCE(v_billed, 0.0);

    -- Determine ready to bill status
    -- For primary claims, check if all charge lines are ready

    -- Build diagnosis loop
    v_diagnoses := build_mm_1500_diagnosis_loop(
        p_charge_lines,
        p_parent_claim_no,
        p_payer_id
    )::mm_1500_diagnosis_info[];

    -- Build service lines loop
    v_service_lines := build_mm_1500_charge_lines_loop(
        p_site_id,
        p_patient_id,
        p_payer_id,
        p_charge_lines,
        v_diagnoses,
        p_parent_claim_no,
        p_insurance_id
    )::mm_1500_service_line_info[];

    RAISE NOTICE 'v_service_lines: %', v_service_lines;
    RAISE NOTICE 'v_diagnoses: %', v_diagnoses;
    -- Build patient segment
    SELECT * INTO v_patient_segment
    FROM build_mm_1500_patient_segment(p_patient_id);
    RAISE NOTICE 'v_patient_segment: %', v_patient_segment;

    -- Build subscriber segment
    SELECT * INTO v_subscriber_segment
    FROM build_mm_1500_subscriber_segment(
        p_patient_id,
        p_insurance_id,
        p_charge_lines,
        p_parent_claim_no,
        p_payer_id
    );
    RAISE NOTICE 'v_subscriber_segment: %', v_subscriber_segment;
    -- Build referring provider segment (if patient prescriber provided)
    IF p_patient_prescriber_id IS NOT NULL THEN
        SELECT * INTO v_referring_provider_segment
        FROM build_mm_1500_referring_provider_segment(
            p_site_id,
            p_patient_id,
            p_payer_id,
            p_patient_prescriber_id,
            p_parent_claim_no
        );
    END IF;
    RAISE NOTICE 'v_referring_provider_segment: %', v_referring_provider_segment;
    -- Build billing provider segment
    SELECT * INTO v_billing_provider_segment
    FROM build_mm_1500_billing_provider_segment(p_site_id);

    -- Build dates segment (only for COB claims)
    IF v_is_cob THEN
        SELECT * INTO v_dates_segment
        FROM build_mm_1500_dates_segment(p_parent_claim_no);
    END IF;

    -- Build COB segment (only for COB claims)
    IF v_is_cob THEN
        SELECT * INTO v_cob_segment
        FROM build_mm_1500_cob_loop(p_parent_claim_no);
    ELSE
        -- Initialize COB segment to NULL values for non-COB claims
        SELECT 
            NULL::integer as cob_insurance_id,
            NULL::integer as cob_payer_id,
            NULL::text as cob_organization_name,
            NULL::text as cob_insurance_group_or_policy_number,
            NULL::text as other_payer_claim_control_number,
            NULL::text as cob_first_name,
            NULL::text as cob_last_name,
            NULL::text as cob_middle_name
        INTO v_cob_segment;
    END IF;

    -- Calculate new fields
    -- Get payer record for settings
    SELECT * INTO v_payer_rec
    FROM form_payer p
    WHERE p.id = p_payer_id
      AND p.deleted IS NOT TRUE 
      AND p.archived IS NOT TRUE;

    -- Get site record for tax ID
    SELECT * INTO v_site_rec
    FROM form_site s
    WHERE s.id = p_site_id
      AND s.deleted IS NOT TRUE 
      AND s.archived IS NOT TRUE;

    -- Get patient record for MRN
    SELECT * INTO v_patient_rec
    FROM form_patient pt
    WHERE pt.id = p_patient_id
      AND pt.deleted IS NOT TRUE 
      AND pt.archived IS NOT TRUE;

    -- Calculate patient_account_number (Patient Account Number)
    CASE COALESCE(v_payer_rec.cms_4, 'MRN')
        WHEN 'Patient ID' THEN v_patient_account_number := p_patient_id::text;
        WHEN 'Invoice Number' THEN v_patient_account_number := 'INVOICE_NO_PLACEHOLDER';
        ELSE v_patient_account_number := v_patient_rec.mrn; -- Default to MRN
    END CASE;

    -- Calculate referring_provider_qualifier and referring_provider_alt_id
    IF v_referring_provider_segment.referring_provider_physician_id IS NOT NULL THEN
        -- Default qualifier from payer cms_10 setting
        v_referring_provider_qualifier := COALESCE(v_payer_rec.cms_10, 'State License #');
        
        -- Get physician record for the referring provider
        SELECT * INTO v_physician_rec
        FROM form_physician ph
        WHERE ph.id = v_referring_provider_segment.referring_provider_physician_id
          AND ph.deleted IS NOT TRUE 
          AND ph.archived IS NOT TRUE;
        
        -- Get the appropriate ID based on qualifier
        CASE v_referring_provider_qualifier
            WHEN 'MCD Provider #' THEN v_referring_provider_alt_id := v_physician_rec.mcd;
            WHEN 'DEA #' THEN v_referring_provider_alt_id := v_physician_rec.dea;
            WHEN 'State License #' THEN v_referring_provider_alt_id := v_physician_rec.state_license;
            WHEN 'MCR #' THEN v_referring_provider_alt_id := v_physician_rec.mcr_ptan_id;
            WHEN 'Taxonomy Code' THEN v_referring_provider_alt_id := v_physician_rec.taxonomy_id;
            ELSE v_referring_provider_alt_id := v_physician_rec.state_license; -- Default
        END CASE;
    END IF;

    -- Calculate bill_tax_id
    v_bill_tax_id := COALESCE(v_payer_rec.cms_11, v_site_rec.tax_id);

    -- Calculate bill_alt_provider_id
    IF COALESCE(v_payer_rec.cms_12, 'No') = 'Yes' THEN
        v_bill_alt_provider_id := COALESCE(v_payer_rec.cms_12_qualifier, '') || '^' || COALESCE(v_payer_rec.cms_12_id, '');
    END IF;

    -- Calculate patient_signature_date based on cms_5 setting
    IF COALESCE(v_payer_rec.cms_5, 'No') = 'Yes' THEN
        -- Use earliest start_date from related prescriptions
        DECLARE
            v_earliest_start_date date;
        BEGIN
            SELECT MIN(cor.start_date)
            INTO v_earliest_start_date
            FROM unnest(p_charge_lines) cl
            INNER JOIN form_careplan_order_rx cor ON cor.rx_no = cl.rx_no
              AND cor.deleted IS NOT TRUE 
              AND cor.archived IS NOT TRUE
            WHERE cl.rx_no IS NOT NULL;
            
            IF v_earliest_start_date IS NOT NULL THEN
                v_patient_signature_date := TO_CHAR(v_earliest_start_date, 'MM/DD/YYYY');
            ELSE
                -- Fallback to current date if no prescription start date found
                v_patient_signature_date := TO_CHAR(CURRENT_DATE, 'MM/DD/YYYY');
            END IF;
        END;
    ELSE
        -- Default to current date when cms_5 is not 'Yes'
        v_patient_signature_date := TO_CHAR(CURRENT_DATE, 'MM/DD/YYYY');
    END IF;

    -- Assemble the complete claim record
    SELECT 
        -- Header information
        p_parent_claim_no::text as parent_claim_no,
        v_claim_no::text as claim_no,
        p_patient_id::integer as patient_id,
        v_status::text as status,
        v_substatus_id::text as substatus_id,
        p_site_id::integer as site_id,
        p_insurance_id::integer as insurance_id,
        p_payer_id::integer as payer_id,
        v_subscriber_segment.insurance_type::text as insurance_type,
        NULL::text as claim_resubmission_code,
        NULL::text as control_number,
        NULL::text as claim_codes,
        
        -- Patient information
        v_patient_segment.patient_first_name::text as patient_first_name,
        v_patient_segment.patient_last_name::text as patient_last_name,
        v_patient_segment.patient_middle_name::text as patient_middle_name,
        v_patient_segment.patient_dob::text as patient_dob,
        v_patient_segment.patient_gender::text as patient_gender,
        v_patient_segment.patient_phone_number::text as patient_phone_number,
        v_patient_segment.patient_address1::text as patient_address1,
        v_patient_segment.patient_address2::text as patient_address2,
        v_patient_segment.patient_city::text as patient_city,
        v_patient_segment.patient_state_id::text as patient_state_id,
        v_patient_segment.patient_postal_code::text as patient_postal_code,
        
        -- Subscriber information
        v_subscriber_segment.subscriber_payer_organization_name::text as subscriber_payer_organization_name,
        v_subscriber_segment.subscriber_member_id::text as subscriber_member_id,
        v_subscriber_segment.subscriber_insurance_group_or_policy_number::text as subscriber_insurance_group_or_policy_number,
        v_subscriber_segment.pa_id::integer as pa_id,
        v_subscriber_segment.prior_authorization_number::text as prior_authorization_number,
        v_subscriber_segment.subscriber_payment_responsibility_level_code::text as subscriber_payment_responsibility_level_code,
        v_subscriber_segment.subscriber_relationship_id::text as subscriber_relationship_id,
        v_subscriber_segment.subscriber_first_name::text as subscriber_first_name,
        v_subscriber_segment.subscriber_last_name::text as subscriber_last_name,
        v_subscriber_segment.subscriber_middle_name::text as subscriber_middle_name,
        v_subscriber_segment.subscriber_dob::text as subscriber_dob,
        v_subscriber_segment.subscriber_gender::text as subscriber_gender,
        v_subscriber_segment.subscriber_phone_number::text as subscriber_phone_number,
        v_subscriber_segment.subscriber_address1::text as subscriber_address1,
        v_subscriber_segment.subscriber_address2::text as subscriber_address2,
        v_subscriber_segment.subscriber_city::text as subscriber_city,
        v_subscriber_segment.subscriber_state_id::text as subscriber_state_id,
        v_subscriber_segment.subscriber_postal_code::text as subscriber_postal_code,
        
        -- Referring provider information
        v_referring_provider_segment.referring_provider_id::integer as referring_provider_id,
        v_referring_provider_segment.referring_provider_physician_id::integer as referring_provider_physician_id,
        v_referring_provider_segment.referring_provider_first_name::text as referring_provider_first_name,
        v_referring_provider_segment.referring_provider_last_name::text as referring_provider_last_name,
        v_referring_provider_segment.referring_provider_npi::text as referring_provider_npi,
        v_referring_provider_segment.referring_provider_state_license_number::text as referring_provider_state_license_number,
        v_referring_provider_segment.referring_provider_taxonomy_id::text as referring_provider_taxonomy_id,
        
        -- Billing provider information
        v_billing_provider_segment.bill_organization_name::text as bill_organization_name,
        v_billing_provider_segment.npi::text as npi,
        v_billing_provider_segment.bill_address1::text as bill_address1,
        v_billing_provider_segment.bill_address2::text as bill_address2,
        v_billing_provider_segment.bill_city::text as bill_city,
        v_billing_provider_segment.bill_state_id::text as bill_state_id,
        v_billing_provider_segment.bill_phone::text as bill_phone,
        v_bill_tax_id::text as bill_tax_id,
        v_bill_alt_provider_id::text as bill_alt_provider_id,
        v_patient_account_number::text as patient_account_number,
        v_referring_provider_qualifier::text as referring_provider_qualifier,
        v_referring_provider_alt_id::text as referring_provider_alt_id,
        v_patient_signature_date::text as patient_signature_date,
        
        -- Subforms
        v_diagnoses::mm_1500_diagnosis_info[] as subform_dx,
        v_service_lines::mm_1500_service_line_info[] as subform_sl,

        -- Financial information
        v_billed::numeric as billed,
        v_expected::numeric as expected,
        v_paid::numeric as paid,
        
        -- COB information (only populated for COB claims)
        v_cob_segment.cob_insurance_id::integer as cob_insurance_id,
        v_cob_segment.cob_payer_id::integer as cob_payer_id,
        v_cob_segment.cob_organization_name::text as cob_organization_name,
        v_cob_segment.cob_insurance_group_or_policy_number::text as cob_insurance_group_or_policy_number,
        v_cob_segment.other_payer_claim_control_number::text as other_payer_claim_control_number,
        v_cob_segment.cob_first_name::text as cob_first_name,
        v_cob_segment.cob_last_name::text as cob_last_name,
        v_cob_segment.cob_middle_name::text as cob_middle_name
    INTO v_result;

    RAISE LOG 'MM 1500 claim build completed successfully for claim %', v_claim_no;

    -- Log successful completion
    PERFORM log_billing_function(
      'build_mm_1500_claim'::tracked_function,
      v_params,
      jsonb_build_object(
          'claim_no', v_claim_no,
          'expected_amount', v_expected,
          'service_lines_count', COALESCE(array_length(v_service_lines, 1), 0),
          'diagnosis_count', COALESCE(array_length(v_diagnoses, 1), 0),
          'is_cob', v_is_cob
      ),
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;
    
  EXCEPTION WHEN OTHERS THEN
    -- Log error
    v_error_message := SQLERRM;
    
    -- Log to billing error log
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        v_error_message,
        'Exception in build_mm_1500_claim',
        'FUNCTION',
        current_schema(),
        'med_claim_1500',
        v_params
    );

    -- Log to function log
    PERFORM log_billing_function(
      'build_mm_1500_claim'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;