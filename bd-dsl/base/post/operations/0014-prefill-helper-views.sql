CREATE OR REPLACE VIEW vw_patient_prescriber_address AS
SELECT 
    pp.id,
    p.auto_name,
    pl.address1,
    pl.address2,
    pl.city,
    pl.state_id,
    pl.zip,
    pp.patient_id
FROM form_patient_prescriber pp
INNER JOIN form_physician p ON pp.physician_id = p.id 
AND p.archived IS NOT TRUE 
AND p.deleted IS NOT TRUE
LEFT JOIN LATERAL (
    SELECT
        pl.address1,
        pl.address2,
        pl.city,
        pl.state_id,
        pl.zip
    FROM sf_form_physician_to_physician_location spl
    INNER JOIN form_physician_location pl ON pl.id = spl.form_physician_location_fk
    AND pl.archived IS NOT TRUE 
    AND pl.deleted IS NOT TRUE
    WHERE spl.form_physician_fk = p.id AND spl.archive IS NOT TRUE AND spl."delete" IS NOT TRUE
    ORDER BY CASE WHEN pl.is_primary = 'Yes' THEN 1 ELSE 0 END, pl.created_on DESC
    LIMIT 1
) AS pl ON true
WHERE pp.archived IS NOT TRUE 
AND pp.deleted IS NOT TRUE;
