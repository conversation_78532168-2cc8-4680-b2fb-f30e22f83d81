DO $$ BEGIN
  PERFORM drop_all_function_signatures('process_charge_line_revenue');
END $$;
CREATE OR REPLACE FUNCTION process_charge_line_revenue() RETURNS TRIGGER AS $$
DECLARE
    v_account_id INTEGER;
    v_creator INTEGER;
    v_invoice_id INTEGER;
    v_parent_account_id INTEGER;
    v_parent_invoice_id INTEGER;
    v_is_cob_charge BOOLEAN;
    v_success BOOLEAN := false;
    v_parent_charge_record RECORD;
BEGIN
    -- Log function entry
    RAISE LOG 'Entering process_charge_line_revenue for charge_no: %, id: %', NEW.charge_no, NEW.id;

	IF COALESCE(NEW.revenue_accepted_posted, 'No') <> 'Yes' OR 
	   COALESCE(NEW.void, 'No') = 'Yes' OR 
	   COALESCE(NEW.zeroed, 'No') = 'Yes' OR 
	   COALESCE(NEW.archived, FALSE) = TRUE OR 
	   COALESCE(NEW.deleted, FALSE) = TRUE OR 
	   NEW.applied_datetime IS NOT NULL THEN
	    RAISE LOG 'Skipping process_charge_line_revenue conditions not met for charge_no: %', NEW.charge_no;
	    RETURN NEW;
	END IF;

    -- Check if we're in a closed period
    IF check_closed_period(NEW.post_datetime::timestamp) THEN
        -- Log error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Cannot post revenue in a closed accounting period',
            'Error processing charge line revenue',
            'TRIGGER',
            current_schema(),
            'ledger_charge_line',
            jsonb_build_object(
                'function_name', 'process_charge_line_revenue',
                'invoice_no', NEW.invoice_no,
                'charge_no', NEW.charge_no,
                'post_datetime', NEW.post_datetime
            )
        );
        
        RAISE EXCEPTION 'Cannot post revenue in a closed accounting period for charge_line %', NEW.charge_no;
    END IF;
    
    BEGIN
        -- Determine who created/updated this record
        v_creator := COALESCE(NEW.updated_by, NEW.created_by);
        RAISE LOG 'Processing invoice revenue for charge_no: %, creator: %', NEW.charge_no, v_creator;
        
        v_is_cob_charge := (NEW.parent_charge_no IS NOT NULL);
        SELECT bi.id INTO v_invoice_id
        FROM form_billing_invoice bi
        WHERE bi.invoice_no = NEW.invoice_no;

        RAISE LOG 'Processing revenue for charge_no: %', NEW.charge_no;
        
        -- Get account ID for this charge
        BEGIN
            v_account_id := get_account_id(NEW.patient_id, NEW.payer_id);
            RAISE LOG 'Retrieved account_id: % for charge_no: %', v_account_id, NEW.charge_no;
        EXCEPTION WHEN OTHERS THEN
            -- Log error to billing_error_log
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                SQLERRM,
                'Error retrieving account ID for charge',
                'TRIGGER',
                current_schema(),
                'ledger_charge_line',
                jsonb_build_object(
                    'function_name', 'process_charge_line_revenue',
                    'invoice_no', NEW.invoice_no,
                    'charge_no', NEW.charge_no,
                    'post_datetime', NEW.post_datetime
                )
            );

            RAISE;
        END;

        RAISE LOG 'Processing charge_line: %, expected: %', NEW.charge_no, NEW.expected;
        
        -- Validate the expected amount is not negative
        IF NEW.expected < 0 THEN
            -- Log error to billing_error_log
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Negative expected amount found for charge line',
                'Error processing invoice revenue',
                'TRIGGER',
                current_schema(),
                'billing_invoice',
                jsonb_build_object(
                    'function_name', 'process_charge_line_revenue',
                    'invoice_no', NEW.invoice_no,
                    'charge_no', NEW.charge_no,
                    'expected', NEW.expected
                )
            );
            
            RAISE EXCEPTION 'Negative expected amount found for charge line %', NEW.charge_no;
        END IF;
        
        -- 1. Create AR debit entry (Accounts Receivable)
        RAISE LOG 'Creating AR debit entry for charge_no: %, amount: %', 
                    NEW.charge_no, NEW.expected;
        
        INSERT INTO form_ledger_finance (
            invoice_id, 
            invoice_no,
            charge_line_id, 
            charge_no,
            account_id, 
            source_id, 
            source_form, 
            post_datetime, 
            transaction_datetime,
            inventory_id,
            site_id,
            account_type, 
            transaction_type,
            quantity,
            debit, 
            credit, 
            created_on, 
            created_by,
            notes
        ) VALUES (
            v_invoice_id,
            NEW.invoice_no,
            NEW.id,
            NEW.charge_no,
            v_account_id,
            NEW.id,
            'ledger_charge_line',
            COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
            get_site_timestamp(NEW.site_id),
            NEW.inventory_id,
            NEW.site_id,
            'AR',
            'Posting',
            NEW.bill_quantity,
            NEW.expected,
            0.00,
            CURRENT_TIMESTAMP,
            v_creator,
            'Revenue posting for invoice: ' || NEW.invoice_no || ', charge: ' || NEW.charge_no
        );

        -- 2. Create Revenue credit entry
        RAISE LOG 'Creating Revenue credit entry for charge_line: %, amount: %', 
                    NEW.charge_no, NEW.expected;
        
        INSERT INTO form_ledger_finance (
            invoice_id, 
            invoice_no,
            charge_line_id, 
            charge_no,
            account_id, 
            source_id, 
            source_form, 
            post_datetime, 
            transaction_datetime,
            inventory_id,
            site_id,
            account_type, 
            transaction_type,
            quantity,
            debit, 
            credit, 
            created_on, 
            created_by,
            notes
        ) VALUES (
            v_invoice_id,
            NEW.invoice_no,
            NEW.id,
            NEW.charge_no,
            v_account_id,
            NEW.id,
            'ledger_charge_line',
            COALESCE(NEW.post_datetime,get_site_timestamp(NEW.site_id)),
            get_site_timestamp(NEW.site_id),
            NEW.inventory_id,
            NEW.site_id,
            'Revenue',
            'Posting',
            NEW.bill_quantity,
            0.00,
            NEW.expected,
            CURRENT_TIMESTAMP,
            v_creator,
            'Revenue posting for invoice: ' || NEW.invoice_no || ', charge: ' || NEW.charge_no
        );
        
        -- 3. create adjustment entries to reduce liability on parent/master invoice
        IF v_is_cob_charge THEN

            -- Get master charge info
            SELECT * INTO v_parent_charge_record
            FROM form_ledger_charge_line
            WHERE charge_no = NEW.parent_charge_no
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE
            AND COALESCE(void, 'No') <> 'Yes';

            SELECT bi.id INTO v_parent_invoice_id
            FROM form_billing_invoice bi
            WHERE bi.invoice_no = v_parent_charge_record.invoice_no;
            
            v_parent_account_id := get_account_id(v_parent_charge_record.patient_id, v_parent_charge_record.payer_id);

            INSERT INTO form_ledger_finance (
                invoice_id,
                invoice_no,
                charge_line_id, 
                charge_no,
                account_id, 
                source_id, 
                source_form, 
                post_datetime,
                transaction_datetime,
                inventory_id,
                site_id,
                account_type, 
                transaction_type,
                quantity,
                debit, 
                credit, 
                created_on, 
                created_by,
                notes
            ) VALUES (
                v_parent_invoice_id,
                v_parent_charge_record.invoice_no,
                v_parent_charge_record.id,
                v_parent_charge_record.charge_no,
                v_account_id,
                NEW.id,
                'ledger_charge_line',
                COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
                get_site_timestamp(NEW.site_id),
                NEW.inventory_id,
                NEW.site_id,
                'AR',
                'Adjustment',
                NEW.bill_quantity,
                0.00,
                NEW.expected,
                CURRENT_TIMESTAMP,
                v_creator,
                'Liability reduction for invoice: ' || NEW.invoice_no
                || ', charge: ' || v_parent_charge_record.charge_no 
            );
            
            -- Create Revenue debit entry to offset
            INSERT INTO form_ledger_finance (
                invoice_id, 
                invoice_no,
                charge_line_id, 
                charge_no,
                account_id, 
                source_id, 
                source_form, 
                post_datetime,
                transaction_datetime,
                inventory_id,
                site_id,
                account_type, 
                transaction_type,
                quantity,
                debit, 
                credit, 
                created_on, 
                created_by,
                notes
            ) VALUES (
                v_parent_invoice_id,
                v_parent_charge_record.invoice_no,
                v_parent_charge_record.id,
                v_parent_charge_record.charge_no,
                v_account_id,
                NEW.id,
                'ledger_charge_line',
                COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
                get_site_timestamp(NEW.site_id),
                NEW.inventory_id,
                NEW.site_id,
                'Revenue',
                'Adjustment',
                NEW.bill_quantity,
                NEW.expected,
                0.00,
                CURRENT_TIMESTAMP,
                v_creator,
                    'Liability reduction for invoice: ' || NEW.invoice_no
                || ', charge: ' || v_parent_charge_record.charge_no 
            );
        END IF;

        -- Mark charge line as posted
        NEW.revenue_accepted_posted := 'Yes';
        NEW.post_datetime := COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id));
        NEW.applied_datetime := get_site_timestamp(NEW.site_id);

        v_success := true;
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        -- Log the error
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Transaction failed during invoice revenue posting',
            'TRIGGER',
            current_schema(),
            'ledger_charge_line',
            jsonb_build_object(
                'function_name', 'process_charge_line_revenue',
                'charge_no', NEW.charge_no,
                'success_flag', v_success
            )
        );
        
        RAISE; -- Re-throw the exception to rollback the transaction
    END;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Trigger for charge line revenue posting
CREATE OR REPLACE TRIGGER trg_ledger_charge_line_revenue
BEFORE UPDATE ON form_ledger_charge_line
FOR EACH ROW
WHEN (
    (COALESCE(NEW.revenue_accepted_posted, 'No') = 'Yes' AND COALESCE(OLD.revenue_accepted_posted, 'No') IS DISTINCT FROM 'Yes')
)
EXECUTE FUNCTION process_charge_line_revenue();

CREATE OR REPLACE FUNCTION process_charge_line_zero() RETURNS TRIGGER AS $$
DECLARE
    v_creator INTEGER;
    v_success BOOLEAN := false;
    v_ledger_record RECORD;
BEGIN
    -- Log function entry
    RAISE LOG 'Entering process_charge_line_zero for charge_no: %, id: %', NEW.charge_no, NEW.id;
    
    -- Skip if conditions aren't met
    IF (COALESCE(NEW.zeroed, 'No') <> 'Yes' OR 
        COALESCE(OLD.zeroed, 'No') = 'Yes' OR
        COALESCE(NEW.revenue_accepted_posted, 'No') <> 'Yes' OR 
        (NEW.applied_datetime IS NULL)) THEN
        RAISE LOG 'Skipping process_charge_line_zero, conditions not met for charge_no: %', 
                 COALESCE(NEW.charge_no, 'NULL');
        RETURN NEW;
    END IF;
    
    -- Check if we're in a closed period
    IF check_closed_period(NEW.post_datetime) THEN
        -- Log error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Cannot zero charge line in a closed accounting period',
            'Error zeroing charge line',
            'TRIGGER',
            current_schema(),
            'ledger_charge_line',
            jsonb_build_object(
                'function_name', 'process_charge_line_zero',
                'invoice_no', NEW.invoice_no,
                'charge_no', NEW.charge_no,
                'post_datetime', NEW.post_datetime
            )
        );
        
        RAISE EXCEPTION 'Cannot zero charge line in a closed accounting period for charge %', NEW.charge_no;
    END IF;
    
    -- Lock the charge row exclusively to prevent concurrent modifications
    PERFORM id FROM form_ledger_charge_line 
    WHERE id = NEW.id 
    FOR UPDATE;

    -- Determine who created/updated this record
    v_creator := COALESCE(NEW.updated_by, NEW.created_by);
    RAISE LOG 'Zeroing charge_no: %, by user: %', 
             NEW.charge_no, v_creator;
    
    BEGIN
    EXCEPTION WHEN OTHERS THEN
        -- Log error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Error retrieving account ID for zeroing charge',
            'TRIGGER',
            current_schema(),
            'ledger_charge_line',
            jsonb_build_object(
                'function_name', 'process_charge_line_zero',
                'invoice_no', NEW.invoice_no,
                'patient_id', NEW.patient_id,
                'payer_id', NEW.payer_id,
                'charge_no', NEW.charge_no
            )
        );
        
        RAISE;
    END;
    
    BEGIN
        -- Create reversing entries for all finance entries related to this invoice
        FOR v_ledger_record IN 
            SELECT * FROM form_ledger_finance lf
            WHERE (lf.charge_line_id = NEW.id OR (lf.source_form = 'ledger_charge_line' AND lf.source_id = NEW.id))
            AND lf.reversal_for_id IS NULL
            AND NOT EXISTS (
                SELECT 1 FROM form_ledger_finance lfr
                WHERE lfr.reversal_for_id = lf.id
            )
            ORDER BY id
        LOOP
            RAISE LOG 'Creating reversing entry for ledger ID: % in charge: %', 
                     v_ledger_record.id, NEW.charge_no;
            
            -- Create reversing entry (swap debit and credit)
            INSERT INTO form_ledger_finance (
                invoice_id, 
                invoice_no,
                charge_line_id, 
                charge_no,
                account_id, 
                source_id, 
                source_form, 
                post_datetime,
                transaction_datetime,
                inventory_id,
                site_id,
                account_type, 
                transaction_type,
                reversal_for_id,
                quantity,
                debit, 
                credit, 
                created_on, 
                created_by,
                notes
            ) VALUES (
                v_ledger_record.invoice_id,
                v_ledger_record.invoice_no,
                v_ledger_record.charge_line_id,
                v_ledger_record.charge_no,
                v_ledger_record.account_id,
                NEW.id,
                'ledger_charge_line',
                COALESCE(NEW.zeroed_datetime, get_site_timestamp(NEW.site_id)),
                get_site_timestamp(NEW.site_id),
                v_ledger_record.inventory_id,
                v_ledger_record.site_id,
                v_ledger_record.account_type,
                v_ledger_record.transaction_type || ' Reversal',
                v_ledger_record.id,
                v_ledger_record.quantity,
                v_ledger_record.credit,  -- Swap credit and debit
                v_ledger_record.debit,   -- Swap credit and debit
                CURRENT_TIMESTAMP,
                v_creator,
                'Zero-out reversal for charge: ' || NEW.charge_no ||
                CASE
                    WHEN NEW.zeroed_reason_id IS NOT NULL THEN 
                        ' - Reason: ' || NEW.zeroed_reason_id
                    ELSE ''
                END
            );
        END LOOP;

        -- Create a log entry for this zero-out operation
        INSERT INTO billing_transaction_log (
            transaction_id,
            source_form,
            source_id,
            operation,
            status,
            start_time,
            end_time,
            details
        ) VALUES (
            'TX-ZERO-' || to_char(now(), 'YYYYMMDD-HH24MISS-') || trunc(random() * 10000)::TEXT,
            'ledger_charge_line',
            NEW.id,
            'ZERO',
            'COMPLETED',
            CURRENT_TIMESTAMP - interval '1 second',  -- Just to record a start time
            CURRENT_TIMESTAMP,
            jsonb_build_object(
                'user_id', v_creator,
                'invoice_no', NEW.invoice_no,
                'charge_no', NEW.charge_no,
                'reason', NEW.zeroed_reason_id
            )
        );
        
        -- Set zeroed_datetime if not already set
        NEW.zeroed_datetime := COALESCE(NEW.zeroed_datetime, get_site_timestamp(NEW.site_id));
        v_success := true;
        RAISE LOG 'Successfully zeroed charge: %', NEW.charge_no;
        
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        -- Log the error
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Transaction failed during charge zeroing',
            'TRIGGER',
            current_schema(),
            'ledger_charge_line',
            jsonb_build_object(
                'function_name', 'process_charge_line_zero',
                'invoice_no', NEW.invoice_no,
                'charge_no', NEW.charge_no,
                'success_flag', v_success
            )
        );
        
        RAISE; -- Re-throw the exception to rollback the transaction
    END;
END;
$$ LANGUAGE plpgsql VOLATILE;
-- Trigger for zeroing invoices
CREATE OR REPLACE TRIGGER trg_billing_charge_zero
BEFORE UPDATE ON form_ledger_charge_line
FOR EACH ROW
WHEN (COALESCE(NEW.zeroed, 'No') = 'Yes' AND COALESCE(OLD.zeroed, 'No') IS DISTINCT FROM 'Yes' AND COALESCE(NEW.revenue_accepted_posted, 'No') = 'Yes')
EXECUTE FUNCTION process_charge_line_zero();
