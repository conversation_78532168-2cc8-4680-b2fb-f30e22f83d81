
-- Primary indexes for invoice filtering
CREATE INDEX IF NOT EXISTS idx_billing_invoice_master_invoice_no 
ON form_billing_invoice(master_invoice_no) 
WHERE deleted IS NOT TRUE AND archived IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_billing_invoice_revenue_status 
ON form_billing_invoice(revenue_accepted_posted, void, zeroed) 
WHER<PERSON> deleted IS NOT TRUE AND archived IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_billing_invoice_patient_id 
ON form_billing_invoice(patient_id) 
WHERE deleted IS NOT TRUE AND archived IS NOT TRUE;

CREATE INDEX IF NOT EXISTS idx_billing_invoice_billed_datetime 
ON form_billing_invoice(billed_datetime) 
WHERE deleted IS NOT TRUE AND archived IS NOT TRUE;

-- Composite indexes for main query conditions
DROP INDEX IF EXISTS idx_billing_invoice_master_revenue;
CREATE INDEX IF NOT EXISTS idx_billing_invoice_master_revenue 
ON form_billing_invoice(master_invoice_no, revenue_accepted_posted) 
WHERE deleted IS NOT TRUE AND archived IS NOT TRUE AND COALESCE(void,'No') = 'No' AND COALESCE(zeroed,'No') = 'No';

CREATE INDEX IF NOT EXISTS idx_billing_invoice_invoice_no 
ON form_billing_invoice(invoice_no, master_invoice_no) 
WHERE deleted IS NOT TRUE AND archived IS NOT TRUE;

-- Indexes for ledger finance table
CREATE INDEX IF NOT EXISTS idx_ledger_finance_invoice_id 
ON form_ledger_finance(invoice_id);

CREATE INDEX IF NOT EXISTS idx_ledger_finance_account_type 
ON form_ledger_finance(account_type, transaction_type) 
WHERE account_type IN ('AR', 'COGS');

CREATE INDEX IF NOT EXISTS idx_ledger_finance_invoice_account 
ON form_ledger_finance(invoice_id, account_type);

-- Partial indexes to help with filtering
DROP INDEX IF EXISTS idx_billing_invoice_balance;
CREATE INDEX IF NOT EXISTS idx_billing_invoice_balance 
ON form_billing_invoice(id) 
WHERE COALESCE(revenue_accepted_posted,'No') = 'Yes' AND COALESCE(void,'No') = 'No' AND COALESCE(zeroed,'No') = 'No';

-- Additional index for aging calculations
CREATE INDEX IF NOT EXISTS idx_billing_invoice_aging 
ON form_billing_invoice(id, billed_datetime);

CREATE OR REPLACE VIEW vw_charge_line_finance_totals AS
WITH aggregated_finance AS (
    SELECT
        charge_line_id,
        invoice_id,
        -- AR calculations with reversal handling
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'AR'
            THEN (COALESCE(debit, 0)::numeric - COALESCE(credit, 0)::numeric)
            ELSE 0::numeric
        END), 0::numeric)::numeric, 2) AS ar_balance,
        
        -- Cash payments with reversal handling
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'Cash'
            THEN (COALESCE(debit, 0)::numeric - COALESCE(credit, 0)::numeric)
            ELSE 0::numeric
        END), 0::numeric)::numeric, 2) AS cash_paid,
        
        -- Expected amount (total AR debits)
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'AR'
            THEN COALESCE(debit, 0)::numeric
            ELSE 0::numeric
        END), 0::numeric)::numeric, 2) AS expected_amount,
        
        -- Adjustments and writeoffs with reversal handling
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'AR' AND transaction_type IN ('Adjustment', 'Adjustment Reversal', 'Writeoff', 'Writeoff Reversal')
            THEN (COALESCE(credit, 0)::numeric - COALESCE(debit, 0)::numeric)
            ELSE 0::numeric
        END), 0::numeric), 2) AS net_adjustment,
        
        MAX(transaction_datetime) as last_transaction_datetime
    FROM form_ledger_finance
    WHERE charge_line_id IS NOT NULL
    GROUP BY charge_line_id, invoice_id
),
-- Get COGS directly from ledger_finance for charge_line_id where available
direct_cogs AS (
    SELECT 
        charge_line_id,
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'COGS'
            THEN (COALESCE(debit, 0)::numeric - COALESCE(credit, 0)::numeric)
            ELSE 0::numeric
        END), 0::numeric), 2) AS cogs_amount
    FROM form_ledger_finance
    WHERE charge_line_id IS NOT NULL
    GROUP BY charge_line_id
),
-- For charge lines without direct COGS entries, get COGS from ticket items
ticket_item_cogs AS (
    SELECT
        lcl.id AS charge_line_id,
        ROUND(SUM(COALESCE(
            -- Get COGS from serial items
            (SELECT SUM(lf.debit - lf.credit)::numeric
             FROM form_ledger_finance lf
             JOIN form_ledger_serial ls ON ls.id = lf.ledger_serial_id
             JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = ls.ticket_item_no 
                AND pld.serial_id IS NOT NULL
             WHERE lf.ledger_serial_id IS NOT NULL
               AND lf.account_type = 'COGS'
               AND lf.transaction_type = 'Dispense'
               AND pld.ticket_item_no = lcl.ticket_item_no),
            
            -- Get COGS from lot items
            (SELECT SUM(lf.debit - lf.credit)::numeric
             FROM form_ledger_finance lf
             JOIN form_ledger_lot ll ON ll.id = lf.ledger_lot_id
             JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = ll.ticket_item_no 
                AND pld.lot_id IS NOT NULL 
                AND pld.serial_id IS NULL
             WHERE lf.ledger_lot_id IS NOT NULL
               AND lf.account_type = 'COGS'
               AND lf.transaction_type = 'Dispense'
               AND pld.ticket_item_no = lcl.ticket_item_no),
               
            -- Get COGS from inventory items
            (SELECT SUM(lf.debit - lf.credit)::numeric
             FROM form_ledger_finance lf
             JOIN form_ledger_inventory li ON li.id = lf.ledger_inventory_id
             JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = li.ticket_item_no 
                AND pld.inventory_id = li.inventory_id 
                AND pld.lot_id IS NULL 
                AND pld.serial_id IS NULL
             WHERE lf.ledger_inventory_id IS NOT NULL
               AND lf.account_type = 'COGS'
               AND lf.transaction_type = 'Dispense'
               AND pld.ticket_item_no = lcl.ticket_item_no),
               
            0
        )), 2) AS cogs_amount
    FROM form_ledger_charge_line lcl
    WHERE lcl.ticket_item_no IS NOT NULL
    GROUP BY lcl.id
),
-- Combine direct COGS with ticket item COGS
combined_cogs AS (
    SELECT 
        dc.charge_line_id,
        CASE
            WHEN dc.cogs_amount > 0 THEN dc.cogs_amount
            WHEN tic.cogs_amount > 0 THEN tic.cogs_amount
            ELSE 0
        END AS cogs_amount
    FROM direct_cogs dc
    FULL OUTER JOIN ticket_item_cogs tic ON tic.charge_line_id = dc.charge_line_id
)
SELECT
    af.charge_line_id,
    cl.charge_no,
    cl.invoice_no,
    af.invoice_id,
    cl.patient_id,
    cl.payer_id,
    cl.inventory_id,
    cl.site_id,
    cl.order_rx_id,
    cl.ticket_no,
    inv.name as inventory_name,
    cl.bill_quantity,
    cl.billing_unit_id,
    cl.hcpc_code,
    cl.formatted_ndc,
    cl.upc,
    cl.upin,
    -- Financial calculations
    COALESCE(af.expected_amount, 0) as expected_amount,
    COALESCE(af.cash_paid, 0) as cash_paid,
    COALESCE(af.net_adjustment, 0) as net_adjustment,
    COALESCE(cc.cogs_amount, 0) as cogs_amount,
    COALESCE(af.ar_balance, 0) as ar_balance,
    -- Balance due calculation
    (COALESCE(af.expected_amount, 0)::numeric - COALESCE(af.cash_paid, 0)::numeric - COALESCE(af.net_adjustment, 0)::numeric) as balance_due,
    -- Profit calculation
    (COALESCE(af.expected_amount, 0)::numeric - COALESCE(cc.cogs_amount, 0)::numeric) as profit,
    -- Profit margin calculation
    CASE 
        WHEN COALESCE(af.expected_amount, 0)::numeric > 0 THEN
            ROUND(((COALESCE(af.expected_amount, 0)::numeric - COALESCE(cc.cogs_amount, 0)::numeric) / COALESCE(af.expected_amount, 0)::numeric * 100)::numeric, 2)
        ELSE NULL
    END as profit_margin,
    af.last_transaction_datetime,
    -- Status indicators
    CASE 
        WHEN (COALESCE(af.expected_amount, 0)::numeric - COALESCE(af.cash_paid, 0)::numeric - COALESCE(af.net_adjustment, 0)::numeric) <= 0 THEN 'Paid'
        WHEN COALESCE(af.cash_paid, 0) > 0 THEN 'Partially Paid'
        ELSE 'Unpaid'
    END as payment_status,
    -- Row coloring
    CASE 
        WHEN (COALESCE(af.expected_amount, 0)::numeric - COALESCE(af.cash_paid, 0)::numeric - COALESCE(af.net_adjustment, 0)::numeric) <= 0 THEN '#E1FAF2'
        WHEN COALESCE(af.cash_paid, 0)::numeric > 0 THEN '#FFEAD9'
        ELSE NULL
    END as __row_color
FROM aggregated_finance af
JOIN form_ledger_charge_line cl ON cl.id = af.charge_line_id
JOIN form_inventory inv ON inv.id = cl.inventory_id
LEFT JOIN combined_cogs cc ON cc.charge_line_id = af.charge_line_id
WHERE cl.archived IS NOT TRUE
AND cl.deleted IS NOT TRUE
AND COALESCE(cl.revenue_accepted_posted, 'No') = 'Yes'
AND COALESCE(cl.void, 'No') <> 'Yes'
AND COALESCE(cl.zeroed, 'No') <> 'Yes';

CREATE OR REPLACE VIEW vw_invoice_finance_totals AS
WITH aggregated_finance AS (
    SELECT
        invoice_id,
        -- AR calculations with reversal handling
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'AR'
            THEN (COALESCE(debit, 0)::numeric - COALESCE(credit, 0)::numeric)
            ELSE 0::numeric
        END), 0::numeric)::numeric, 2) AS ar_balance,
        
        -- Cash payments with reversal handling
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'Cash'
            THEN (COALESCE(debit, 0)::numeric - COALESCE(credit, 0)::numeric)
            ELSE 0::numeric
        END), 0::numeric)::numeric, 2) AS cash_paid,
        
        -- Expected amount (total AR debits)
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'AR'
            THEN COALESCE(debit, 0)::numeric
            ELSE 0::numeric
        END), 0::numeric)::numeric, 2) AS expected_amount,
        
        -- Adjustments and writeoffs with reversal handling
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'AR' AND transaction_type IN ('Adjustment', 'Adjustment Reversal', 'Writeoff', 'Writeoff Reversal')
            THEN (COALESCE(credit, 0)::numeric - COALESCE(debit, 0)::numeric)
            ELSE 0::numeric
        END), 0::numeric), 2) AS net_adjustment,
        
        MAX(transaction_datetime) as last_transaction_datetime
    FROM form_ledger_finance
    GROUP BY invoice_id
),
-- Get direct COGS entries for invoice
direct_invoice_cogs AS (
    SELECT 
        invoice_id,
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'COGS'
            THEN (COALESCE(debit, 0)::numeric - COALESCE(credit, 0)::numeric)
            ELSE 0::numeric
        END), 0::numeric), 2) AS cogs_amount
    FROM form_ledger_finance
    WHERE invoice_id IS NOT NULL
    GROUP BY invoice_id
),
-- Get COGS from charge lines for each invoice
charge_line_cogs AS (
    SELECT 
        lcl.invoice_no,
        bi.id AS invoice_id,
        SUM(clf.cogs_amount) AS cogs_amount
    FROM form_ledger_charge_line lcl
    JOIN form_billing_invoice bi ON bi.invoice_no = lcl.invoice_no
    JOIN vw_charge_line_finance_totals clf ON clf.charge_line_id = lcl.id
    WHERE lcl.archived IS NOT TRUE
    AND lcl.deleted IS NOT TRUE
    AND bi.archived IS NOT TRUE
    AND bi.deleted IS NOT TRUE
    GROUP BY lcl.invoice_no, bi.id
),
-- Combine direct invoice COGS with charge line COGS
combined_cogs AS (
    SELECT 
        COALESCE(dic.invoice_id, clc.invoice_id) AS invoice_id,
        CASE
            WHEN dic.cogs_amount > 0 THEN dic.cogs_amount
            WHEN clc.cogs_amount > 0 THEN clc.cogs_amount
            ELSE 0
        END AS cogs_amount
    FROM direct_invoice_cogs dic
    FULL OUTER JOIN charge_line_cogs clc ON clc.invoice_id = dic.invoice_id
)
SELECT
    bi.id as invoice_id,
    bi.invoice_no,
    ROUND(COALESCE(bi.total_billed::numeric, 0::numeric), 2)::numeric as total_billed,
    ROUND(COALESCE(cc.cogs_amount, 0::numeric), 2)::numeric as total_cost,
    ROUND(COALESCE(af.expected_amount, 0::numeric), 2)::numeric as total_expected,
    ROUND(COALESCE(af.cash_paid, 0::numeric), 2)::numeric as total_paid,
    ROUND(COALESCE(af.net_adjustment, 0::numeric), 2)::numeric as total_adjusted,
    ROUND((
        COALESCE(af.expected_amount, 0::numeric) -
        COALESCE(af.cash_paid, 0::numeric) -
        COALESCE(af.net_adjustment, 0::numeric)
    ), 2)::numeric as total_balance_due,
    ROUND((
        COALESCE(af.expected_amount, 0::numeric) -
        COALESCE(cc.cogs_amount, 0::numeric)
    ), 2)::numeric as total_profit,
    CASE
        WHEN COALESCE(af.expected_amount, 0::numeric) <= 0 THEN 0.0::numeric
        ELSE ROUND((
            (COALESCE(af.expected_amount, 0::numeric) -
            COALESCE(cc.cogs_amount, 0::numeric)) /
            COALESCE(af.expected_amount, 0::numeric) * 100
        ), 2)::numeric
    END as total_margin,
    af.last_transaction_datetime
FROM form_billing_invoice bi
LEFT JOIN aggregated_finance af ON af.invoice_id = bi.id
LEFT JOIN combined_cogs cc ON cc.invoice_id = bi.id
WHERE bi.deleted IS NOT TRUE
    AND bi.archived IS NOT TRUE
    AND COALESCE(bi.revenue_accepted_posted, 'No') = 'Yes'
    AND COALESCE(bi.bill_for_denial, 'No') <> 'Yes'
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes';

CREATE OR REPLACE VIEW vw_base_invoice_details AS
SELECT
	bi.id as form_id,
	'billing_invoice' as form_name,
    bi.id as invoice_id,
    bi.auto_name as invoice_id_auto_name,
    bi.master_invoice_no,
    bi.invoice_no,
    bi.patient_id,
    pt.auto_name as patient_id_auto_name,
    bi.payer_id,
    py.auto_name as payer_id_auto_name,
    bi.site_id,
    st.auto_name as site_id_auto_name,
    bi.revenue_accepted_posted,
    bi.delivery_ticket_id,
    bi.zeroed,
    bi.void,
    bi.post_datetime,
    bi.billing_method_id,
    bmd.auto_name as billing_method_id_auto_name,
    hpi.inventory_id,
    inv.auto_name as inventory_id_auto_name,
    inv.brand_name_id,
    db.auto_name as brand_name_id_auto_name,
    hpi.therapy_id,
    tp.auto_name as therapy_id_auto_name,
    bi.close_no,
    bi.on_hold,
    bi.followup_date,
    bi.collector_id,
    bi.billed_datetime,
    tm.auto_name as collector_id_auto_name,
    bi.date_of_service as dos_start,
    bi.date_of_service_end as dos_end,
    null::text as post_date,
    cs.status as claim_status,
    cs.claim_substatus_id,
    cs.claim_substatus_id_auto_name,
    CASE
        WHEN cs.status IN ('Rejected', 'Denied', 'Error', 'Reversal Rejected', 'Rebill Rejected') THEN '#FFE5E5'
        WHEN cs.status IN ('On-hold', 'Margin', 'Warning', 'Request for Additional Information', 'Awaiting Requested Information', 'PA Deferred') THEN '#FFEAD9'
        WHEN cs.status IN ('Payable', 'Partially Paid', 'Captured') THEN '#E1FAF2'
        WHEN COALESCE(bi.void,'No') = 'Yes' OR COALESCE(bi.zeroed,'No') = 'Yes' THEN '#FFE5E5'
        WHEN COALESCE(bi.on_hold,'No') = 'Yes' THEN '#FFEAD9'
        ELSE NULL
    END AS __row_color,
 
    -- Finance metrics
    COALESCE(bi.total_billed, 0) as total_billed,
    COALESCE(it.total_cost, 0) as total_cost,
    CASE
      WHEN COALESCE(bi.revenue_accepted_posted, 'No') = 'Yes' THEN 
        COALESCE(it.total_expected, 0)
      ELSE 
        COALESCE(bi.total_expected, 0)
    END as total_expected,
    COALESCE(it.total_paid, 0) as total_paid,
    COALESCE(it.total_adjusted, 0) as total_adjusted,
    CASE
      WHEN COALESCE(bi.revenue_accepted_posted, 'No') = 'Yes' THEN 
        COALESCE(it.total_expected, 0) - COALESCE(it.total_paid, 0) - COALESCE(it.total_adjusted, 0)
      ELSE 
        COALESCE(bi.total_balance_due, 0)
    END as total_balance_due,
    CASE
        WHEN COALESCE(bi.revenue_accepted_posted, 'No') = 'Yes' AND COALESCE(it.total_expected, 0) > 0 THEN
            (COALESCE(it.total_expected, 0) - COALESCE(it.total_cost, 0))
        ELSE NULL
    END as total_profit,
    CASE
        WHEN COALESCE(bi.revenue_accepted_posted, 'No') = 'Yes' AND COALESCE(it.total_expected, 0) > 0 THEN
            CONCAT(ROUND(((COALESCE(it.total_expected, 0)::numeric - COALESCE(it.total_cost, 0)::numeric) / COALESCE(it.total_expected, 0)::numeric * 100), 2), '%')
        ELSE NULL
    END as total_margin,
    it.last_transaction_datetime
FROM
    form_billing_invoice bi
    LEFT JOIN form_payer py ON py.id = bi.payer_id
    LEFT JOIN form_site st ON st.id = bi.site_id
    LEFT JOIN form_patient pt ON pt.id = bi.patient_id
    LEFT JOIN form_list_billing_method bmd ON bmd.code = bi.billing_method_id
    LEFT JOIN vw_invoice_highest_priced_item_info hpi ON hpi.invoice_no = bi.invoice_no
    LEFT JOIN form_inventory inv ON inv.id = hpi.inventory_id
    LEFT JOIN form_list_fdb_drug_brand db ON db.code = inv.brand_name_id
    LEFT JOIN form_list_therapy tp ON tp.code = hpi.therapy_id
    LEFT JOIN form_wf_queue_team tm ON tm.code = bi.collector_id
    LEFT JOIN vw_invoice_claim_response_details cs ON cs.invoice_id = bi.id
	INNER JOIN vw_invoice_finance_totals it ON it.invoice_id = bi.id
WHERE
    bi.deleted IS NOT TRUE
    AND bi.archived IS NOT TRUE
    AND COALESCE(bi.bill_for_denial, 'No') <> 'Yes'
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes';

CREATE OR REPLACE VIEW vw_ar_master_invoice_group AS
WITH patient_balances AS (
    SELECT
        patient_id,
        SUM(COALESCE(lf.balance_raw, 0)) as total_balance
    FROM form_billing_invoice bi
    JOIN LATERAL (
        SELECT
            (COALESCE(SUM(debit) FILTER (WHERE account_type = 'AR'), 0) - 
             COALESCE(SUM(credit) FILTER (WHERE account_type = 'AR'), 0))::numeric AS balance_raw
        FROM form_ledger_finance flf
        WHERE flf.invoice_id = bi.id
    ) lf ON true
    WHERE 
        bi.deleted IS NOT TRUE
        AND bi.archived IS NOT TRUE
        AND COALESCE(bi.void, 'No') <> 'Yes'
        AND COALESCE(bi.zeroed, 'No') <> 'Yes'
        AND COALESCE(bi.revenue_accepted_posted, 'No') = 'Yes'
    GROUP BY bi.patient_id
),
invoice_balances AS (
    SELECT 
        master_invoice_no,
        SUM(total_balance_due) as group_balance
    FROM vw_base_invoice_details
    WHERE revenue_accepted_posted = 'Yes'
    GROUP BY master_invoice_no
    HAVING SUM(total_balance_due) > 0
),
master_invoice_data AS (
    SELECT DISTINCT ON (bi.master_invoice_no)
        NULL::integer AS form_id,
        NULL::text AS form_name,
        MIN(big.followup_date) AS followup_date,
        NULL::INTEGER AS invoice_id,
        NULL::text AS invoice_id_auto_name,
        bi.master_invoice_no,
        bi.patient_id,
        bi.patient_id_auto_name,
        bi.payer_id,
        bi.master_invoice_no AS invoice_no,
        bi.claim_status,
        bi.claim_substatus_id,
        bi.claim_substatus_id_auto_name,
        bi.payer_id_auto_name,
        bi.site_id,
        bi.site_id_auto_name,
        bi.revenue_accepted_posted,
        bi.delivery_ticket_id,
        bi.zeroed,
        bi.void,
        bi.post_datetime,
        bi.billing_method_id,
        bi.billing_method_id_auto_name,
        bi.inventory_id,
        bi.inventory_id_auto_name,
        bi.brand_name_id,
        bi.brand_name_id_auto_name,
        bi.therapy_id,
        bi.therapy_id_auto_name,
        bi.close_no,
        bi.on_hold,
        bi.collector_id,
        bi.collector_id_auto_name,
        bi.dos_start,
        bi.dos_end,
        TO_CHAR(bi.post_datetime, 'MM/DD/YYYY') as post_date,
        ARRAY[bi.master_invoice_no] as path,
        COALESCE(pb.total_balance, 0) AS total_patient_balance,
        SUM(big.total_expected) AS total_expected,
        SUM(big.total_cost) AS total_cost,
        SUM(big.total_paid) AS total_paid,
        SUM(big.total_adjusted) AS total_adjusted,
        SUM(big.total_balance_due) AS total_balance_due,
        SUM(COALESCE(big.total_profit, 0)) AS total_profit,
        (CURRENT_DATE - bi.billed_datetime::date) AS ar_aging_days,
        get_ar_aging_bucket((CURRENT_DATE - bi.billed_datetime::date)) AS ar_aging_bucket,
        CASE
            WHEN MIN(big.followup_date) IS NOT NULL THEN (CURRENT_DATE - MIN(big.followup_date))
            ELSE NULL::integer
        END AS followup_days,
        CASE
            WHEN SUM(big.total_expected) > 0 THEN
                CONCAT(
                    ROUND(
                        (SUM(COALESCE(big.total_profit::numeric, 0)) / SUM(big.total_expected::numeric) * 100)::numeric, 
                        2
                    ), 
                    '%'
                )
            ELSE NULL
        END AS total_margin,
        MAX(big.last_transaction_datetime) AS group_last_transaction_datetime,
        CASE 
            WHEN MAX(CASE WHEN big.__row_color = '#FFE5E5' THEN 1 ELSE 0 END) = 1 THEN '#FFE5E5'
            WHEN MAX(CASE WHEN big.__row_color = '#FFEAD9' THEN 1 ELSE 0 END) = 1 THEN '#FFEAD9'
            WHEN MAX(CASE WHEN big.__row_color = '#E1FAF2' THEN 1 ELSE 0 END) = 1 THEN '#E1FAF2'
        END AS __row_color,
    ARRAY['view_billing_notes', 'view_delivery_tickets'] as available_actions
    FROM vw_base_invoice_details big
    INNER JOIN vw_base_invoice_details bi ON bi.invoice_no = big.master_invoice_no
    LEFT JOIN patient_balances pb ON pb.patient_id = bi.patient_id
    INNER JOIN invoice_balances ib ON ib.master_invoice_no = bi.master_invoice_no
    WHERE big.revenue_accepted_posted = 'Yes'
    GROUP BY 
        bi.master_invoice_no,
        bi.patient_id,
        bi.patient_id_auto_name,
        bi.payer_id,
        bi.claim_status,
        bi.claim_substatus_id,
        bi.claim_substatus_id_auto_name,
        bi.payer_id_auto_name,
        bi.site_id,
        bi.site_id_auto_name,
        bi.revenue_accepted_posted,
        bi.delivery_ticket_id,
        bi.zeroed,
        bi.void,
        bi.post_datetime,
        bi.billing_method_id,
        bi.billing_method_id_auto_name,
        bi.inventory_id,
        bi.inventory_id_auto_name,
        bi.brand_name_id,
        bi.brand_name_id_auto_name,
        bi.therapy_id,
        bi.therapy_id_auto_name,
        bi.close_no,
        bi.on_hold,
        bi.collector_id,
        bi.collector_id_auto_name,
        bi.dos_start,
        bi.dos_end,
        bi.billed_datetime,
        pb.total_balance
),
detail_invoice_data AS (
    SELECT
        bi.form_id,
        bi.form_name,
        bi.followup_date,
        bi.invoice_id,
        bi.invoice_id_auto_name,
        bi.master_invoice_no,
        bi.patient_id,
        bi.patient_id_auto_name,
        bi.payer_id,
        bi.invoice_no,
        bi.claim_status,
        bi.claim_substatus_id,
        bi.claim_substatus_id_auto_name,
        bi.payer_id_auto_name,
        bi.site_id,
        bi.site_id_auto_name,
        bi.revenue_accepted_posted,
        bi.delivery_ticket_id,
        bi.zeroed,
        bi.void,
        bi.post_datetime,
        TO_CHAR(bi.post_datetime, 'MM/DD/YYYY') as post_date,
        bi.billing_method_id,
        bi.billing_method_id_auto_name,
        bi.inventory_id,
        bi.inventory_id_auto_name,
        bi.brand_name_id,
        bi.brand_name_id_auto_name,
        bi.therapy_id,
        bi.therapy_id_auto_name,
        bi.close_no,
        bi.on_hold,
        bi.collector_id,
        bi.collector_id_auto_name,
        bi.dos_start,
        bi.dos_end,
        ARRAY[bi.master_invoice_no, bi.master_invoice_no || '_details'] as path,
        COALESCE(pb.total_balance, 0) AS total_patient_balance,
        bi.total_expected,
        bi.total_cost,
        bi.total_paid,
        bi.total_adjusted,
        bi.total_balance_due,
        bi.total_profit,
        (CURRENT_DATE - bi.billed_datetime::date) AS ar_aging_days,
        get_ar_aging_bucket((CURRENT_DATE - bi.billed_datetime::date)) AS ar_aging_bucket,
        CASE
            WHEN bi.followup_date IS NOT NULL THEN (CURRENT_DATE - bi.followup_date)
            ELSE NULL::integer
        END AS followup_days,
        CASE
            WHEN bi.total_profit IS NOT NULL AND bi.total_expected > 0 THEN
                CONCAT(
                    ROUND(
                        (COALESCE(bi.total_profit::numeric, 0) / bi.total_expected::numeric * 100)::numeric, 
                        2
                    ), 
                    '%'
                )
            ELSE NULL
        END AS total_margin,
        bi.last_transaction_datetime AS group_last_transaction_datetime,
        bi.__row_color,
        get_invoice_actions(bi.invoice_id) as available_actions
    FROM vw_base_invoice_details bi
    LEFT JOIN patient_balances pb ON pb.patient_id = bi.patient_id
    INNER JOIN invoice_balances ib ON ib.master_invoice_no = bi.master_invoice_no
    WHERE COALESCE(bi.revenue_accepted_posted,'No') = 'Yes'
)

-- Combine the master and detail data
SELECT 
    form_id,
    form_name,
    followup_date,
    invoice_id,
    invoice_id_auto_name,
    master_invoice_no,
    patient_id,
    patient_id_auto_name,
    payer_id,
    invoice_no,
    claim_status,
    claim_substatus_id,
    claim_substatus_id_auto_name,
    payer_id_auto_name,
    site_id,
    site_id_auto_name,
    revenue_accepted_posted,
    delivery_ticket_id,
    zeroed,
    void,
    post_datetime,
    billing_method_id,
    billing_method_id_auto_name,
    inventory_id,
    inventory_id_auto_name,
    brand_name_id,
    brand_name_id_auto_name,
    therapy_id,
    therapy_id_auto_name,
    close_no,
    on_hold,
    collector_id,
    collector_id_auto_name,
    dos_start,
    dos_end,
    post_date,
    path,
    total_patient_balance,
    total_expected,
    total_cost,
    total_paid,
    total_adjusted,
    total_balance_due,
    total_profit,
    ar_aging_days,
    ar_aging_bucket,
    followup_days,
    total_margin,
    group_last_transaction_datetime,
    __row_color,
    available_actions
FROM master_invoice_data

UNION ALL

SELECT 
    form_id,
    form_name,
    followup_date,
    invoice_id,
    invoice_id_auto_name,
    master_invoice_no,
    patient_id,
    patient_id_auto_name,
    payer_id,
    invoice_no,
    claim_status,
    claim_substatus_id,
    claim_substatus_id_auto_name,
    payer_id_auto_name,
    site_id,
    site_id_auto_name,
    revenue_accepted_posted,
    delivery_ticket_id,
    zeroed,
    void,
    post_datetime,
    billing_method_id,
    billing_method_id_auto_name,
    inventory_id,
    inventory_id_auto_name,
    brand_name_id,
    brand_name_id_auto_name,
    therapy_id,
    therapy_id_auto_name,
    close_no,
    on_hold,
    collector_id,
    collector_id_auto_name,
    dos_start,
    dos_end,
    post_date,
    path,
    total_patient_balance,
    total_expected,
    total_cost,
    total_paid,
    total_adjusted,
    total_balance_due,
    total_profit,
    ar_aging_days,
    ar_aging_bucket,
    followup_days,
    total_margin,
    group_last_transaction_datetime,
    __row_color,
    available_actions
FROM detail_invoice_data
ORDER BY master_invoice_no, path;

CREATE OR REPLACE VIEW vw_charge_line_ncpdp AS
SELECT 
	lgl.charge_no,
	lgl.invoice_no,
	lgl.bill_quantity,
	lgl.gross_amount_due,
	lgl.billed,
	lgl.inventory_id,
	inv.auto_name as inventory_id_auto_name,
	lgl.formatted_ndc as ndc,
	ct.ar_balance as total_balance,
	ct.cash_paid as total_paid,
	ct.expected_amount as expected,
	ct.net_adjustment as total_adjusted,
	ct.cogs_amount as total_cost,
	ct.last_transaction_datetime,
    ROUND((
        COALESCE(ct.expected_amount, 0::numeric) - 
        COALESCE(ct.cogs_amount, 0::numeric)
    ), 2)::numeric as total_profit
FROM form_ledger_charge_line lgl 
INNER JOIN form_inventory inv ON inv.id = lgl.inventory_id
LEFT JOIN vw_charge_line_finance_totals ct ON ct.charge_line_id = lgl.id
WHERE lgl.archived IS NOT TRUE 
AND lgl.deleted IS NOT TRUE 
AND COALESCE(lgl.zeroed, 'No') <> 'Yes'
AND COALESCE(lgl.void, 'No') <> 'Yes';

CREATE OR REPLACE VIEW vw_charge_line_mm AS
SELECT 
	lgl.charge_no,
	lgl.invoice_no,
	lgl.bill_quantity,
	lgl.gross_amount_due,
	lgl.billed,
	lgl.inventory_id,
	inv.auto_name as inventory_id_auto_name,
	lgl.hcpc_code as hcpc_code,
	ct.ar_balance as total_balance,
	ct.cash_paid as total_paid,
	ct.expected_amount as expected,
	ct.net_adjustment as total_adjusted,
	ct.cogs_amount as total_cost,
	ct.last_transaction_datetime,
    ROUND((
        COALESCE(ct.expected_amount, 0::numeric) - 
        COALESCE(ct.cogs_amount, 0::numeric)
    ), 2)::numeric as total_profit
FROM form_ledger_charge_line lgl 
INNER JOIN form_inventory inv ON inv.id = lgl.inventory_id
LEFT JOIN vw_charge_line_finance_totals ct ON ct.charge_line_id = lgl.id
WHERE lgl.archived IS NOT TRUE 
AND lgl.deleted IS NOT TRUE 
AND COALESCE(lgl.zeroed, 'No') <> 'Yes'
AND COALESCE(lgl.void, 'No') <> 'Yes'
AND lgl.billing_method_id IN ('mm', 'cm1500');

CREATE OR REPLACE VIEW vw_charge_line_generic AS
SELECT 
	lgl.charge_no,
	lgl.invoice_no,
	lgl.bill_quantity,
	lgl.gross_amount_due,
	lgl.billed,
	lgl.inventory_id,
	inv.auto_name as inventory_id_auto_name,
	lgl.description,
	ct.ar_balance as total_balance,
	ct.cash_paid as total_paid,
	ct.expected_amount as expected,
	ct.net_adjustment as total_adjusted,
	ct.cogs_amount as total_cost,
	ct.last_transaction_datetime,
    ROUND((
        COALESCE(ct.expected_amount, 0::numeric) - 
        COALESCE(ct.cogs_amount, 0::numeric)
    ), 2)::numeric as total_profit
FROM form_ledger_charge_line lgl 
INNER JOIN form_inventory inv ON inv.id = lgl.inventory_id
LEFT JOIN vw_charge_line_finance_totals ct ON ct.charge_line_id = lgl.id
WHERE lgl.archived IS NOT TRUE 
AND lgl.deleted IS NOT TRUE 
AND COALESCE(lgl.zeroed, 'No') <> 'Yes'
AND COALESCE(lgl.void, 'No') <> 'Yes'
AND lgl.billing_method_id IN ('generic');

DO $$ BEGIN
  PERFORM drop_all_function_signatures('get_charge_lines_for_invoice');
END $$;
CREATE OR REPLACE FUNCTION get_charge_lines_for_invoice(p_invoice_id integer)
RETURNS TABLE (
    charge_no text,
    invoice_no text,
    bill_quantity numeric,
    gross_amount_due numeric,
    billed numeric,
    inventory_id integer,
    inventory_id_auto_name text,
    description text,
    hcpc_code text,
    ndc text,
    total_balance numeric,
    total_paid numeric,
    expected numeric,
    total_adjusted numeric,
    total_cost numeric,
    last_transaction_datetime timestamp,
    total_profit numeric,
    available_actions text[]
) AS $$
DECLARE
    v_billing_method_id text;
    v_invoice_no text;
    v_invoice_post_datetime timestamp;
BEGIN
    -- Get the billing method, invoice_no, and post_datetime for the specified invoice
    SELECT bi.billing_method_id, bi.invoice_no, bi.post_datetime
    INTO v_billing_method_id, v_invoice_no, v_invoice_post_datetime
    FROM form_billing_invoice bi
    WHERE id = p_invoice_id
    AND archived IS NOT TRUE
    AND deleted IS NOT TRUE;
    
    -- Return data based on the billing method
    IF v_billing_method_id = 'ncpdp' THEN
        RETURN QUERY
        SELECT 
            cl.charge_no::text,
            cl.invoice_no::text,
            cl.bill_quantity::numeric,
            cl.gross_amount_due::numeric,
            cl.billed::numeric,
            cl.inventory_id::integer,
            cl.inventory_id_auto_name::text,
            cl.inventory_id_auto_name::text AS description,
            lcl.hcpc_code::text,
            cl.ndc::text,
            cl.total_balance::numeric,
            cl.total_paid::numeric,
            cl.expected::numeric,
            cl.total_adjusted::numeric,
            cl.total_cost::numeric,
            cl.last_transaction_datetime::timestamp,
            cl.total_profit::numeric,
            -- Generate available actions
            CASE
                WHEN NOT check_closed_period(v_invoice_post_datetime::timestamp)
                     AND lcl.void IS DISTINCT FROM 'Yes'
                     AND lcl.zeroed IS DISTINCT FROM 'Yes'
                THEN ARRAY['ar_transaction', 'zero']
                ELSE NULL::text[]
            END AS available_actions
        FROM vw_charge_line_ncpdp cl
        JOIN form_ledger_charge_line lcl ON lcl.charge_no = cl.charge_no
        WHERE cl.invoice_no = v_invoice_no;
        
    ELSIF v_billing_method_id IN ('mm', 'cms1500') THEN
        RETURN QUERY
        SELECT 
            cl.charge_no::text,
            cl.invoice_no::text,
            cl.bill_quantity::numeric,
            cl.gross_amount_due::numeric,
            cl.billed::numeric,
            cl.inventory_id::integer,
            cl.inventory_id_auto_name::text AS description,
            lcl.hcpc_code::text,
            cl.ndc::text,
            cl.total_balance::numeric,
            cl.total_paid::numeric,
            cl.expected::numeric,
            cl.total_adjusted::numeric,
            cl.total_cost::numeric,
            cl.last_transaction_datetime::timestamp,
            cl.total_profit::numeric,
            -- Generate available actions
            CASE
                WHEN NOT check_closed_period(v_invoice_post_datetime::timestamp)
                     AND lcl.void IS DISTINCT FROM 'Yes'
                     AND lcl.zeroed IS DISTINCT FROM 'Yes'
                THEN ARRAY['ar_transaction', 'zero']
                ELSE NULL::text[]
            END AS available_actions
        FROM vw_charge_line_mm cl
        JOIN form_ledger_charge_line lcl ON lcl.charge_no = cl.charge_no
        WHERE cl.invoice_no = v_invoice_no;
        
    ELSIF v_billing_method_id = 'generic' THEN
        RETURN QUERY
        SELECT 
            cl.charge_no::text,
            cl.invoice_no::text,
            cl.bill_quantity::numeric,
            cl.gross_amount_due::numeric,
            cl.billed::numeric,
            cl.inventory_id::integer,
            cl.inventory_id_auto_name::text,
            cl.description::text,
            lcl.hcpc_code::text,
            cl.ndc::text,
            cl.total_balance::numeric,
            cl.total_paid::numeric,
            cl.expected::numeric,
            cl.total_adjusted::numeric,
            cl.total_cost::numeric,
            cl.last_transaction_datetime::timestamp,
            cl.total_profit::numeric,
            -- Generate available actions
            CASE
                WHEN NOT check_closed_period(v_invoice_post_datetime::timestamp)
                     AND lcl.void IS DISTINCT FROM 'Yes'
                     AND lcl.zeroed IS DISTINCT FROM 'Yes'
                THEN ARRAY['ar_transaction', 'zero']
                ELSE NULL::text[]
            END AS available_actions
        FROM vw_charge_line_generic cl
        JOIN form_ledger_charge_line lcl ON lcl.charge_no = cl.charge_no
        WHERE cl.invoice_no = v_invoice_no;
        
    ELSE
        -- Default fallback that tries to find the charge lines in any of the views
        -- First try NCPDP
        RETURN QUERY
        SELECT 
            cl.charge_no::text,
            cl.invoice_no::text,
            cl.bill_quantity::numeric,
            cl.gross_amount_due::numeric,
            cl.billed::numeric,
            cl.inventory_id::integer,
            cl.inventory_id_auto_name::text AS description,
            lcl.hcpc_code::text,
            cl.ndc::text,
            cl.total_balance::numeric,
            cl.total_paid::numeric,
            cl.expected::numeric,
            cl.total_adjusted::numeric,
            cl.total_cost::numeric,
            cl.last_transaction_datetime::timestamp,
            cl.total_profit::numeric,
            -- Generate available actions
            CASE
                WHEN NOT check_closed_period(v_invoice_post_datetime::timestamp)
                     AND lcl.void IS DISTINCT FROM 'Yes'
                     AND lcl.zeroed IS DISTINCT FROM 'Yes'
                THEN ARRAY['ar_transaction', 'zero']
                ELSE NULL::text[]
            END AS available_actions
        FROM vw_charge_line_ncpdp cl
        JOIN form_ledger_charge_line lcl ON lcl.charge_no = cl.charge_no
        WHERE cl.invoice_no = v_invoice_no;
        
        IF NOT FOUND THEN
            RETURN QUERY
            SELECT 
                cl.charge_no::text,
                cl.invoice_no::text,
                cl.bill_quantity::numeric,
                cl.gross_amount_due::numeric,
                cl.billed::numeric,
                cl.inventory_id::integer,
                cl.inventory_id_auto_name::text,
                cl.inventory_id_auto_name::text AS description,
                lcl.hcpc_code::text,
                cl.ndc::text,
                cl.total_balance::numeric,
                cl.total_paid::numeric,
                cl.expected::numeric,
                cl.total_adjusted::numeric,
                cl.total_cost::numeric,
                cl.last_transaction_datetime::timestamp,
                cl.total_profit::numeric,
                -- Generate available actions
                CASE
                    WHEN NOT check_closed_period(v_invoice_post_datetime::timestamp)
                         AND lcl.void IS DISTINCT FROM 'Yes'
                         AND lcl.zeroed IS DISTINCT FROM 'Yes'
                    THEN ARRAY['ar_transaction', 'zero']
                    ELSE NULL::text[]
                END AS available_actions
            FROM vw_charge_line_mm cl
            JOIN form_ledger_charge_line lcl ON lcl.charge_no = cl.charge_no
            WHERE cl.invoice_no = v_invoice_no;
            
            IF NOT FOUND THEN
                RETURN QUERY
                SELECT 
                    cl.charge_no::text,
                    cl.invoice_no::text,
                    cl.bill_quantity::numeric,
                    cl.gross_amount_due::numeric,
                    cl.billed::numeric,
                    cl.inventory_id::integer,
                    cl.inventory_id_auto_name::text,
	                cl.description::text,
	                lcl.hcpc_code::text,
	                cl.ndc::text,
                    cl.total_balance::numeric,
                    cl.total_paid::numeric,
                    cl.expected::numeric,
                    cl.total_adjusted::numeric,
                    cl.total_cost::numeric,
                    cl.last_transaction_datetime::timestamp,
                    cl.total_profit::numeric,
                    -- Generate available actions
                    CASE
                        WHEN NOT check_closed_period(v_invoice_post_datetime::timestamp)
                             AND lcl.void IS DISTINCT FROM 'Yes'
                             AND lcl.zeroed IS DISTINCT FROM 'Yes'
                        THEN ARRAY['ar_transaction', 'zero']
                        ELSE NULL::text[]
                    END AS available_actions
                FROM vw_charge_line_generic cl
                JOIN form_ledger_charge_line lcl ON lcl.charge_no = cl.charge_no
                WHERE cl.invoice_no = v_invoice_no;
            END IF;
        END IF;
    END IF;
    
    RETURN;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE VIEW vw_charge_line_transactions AS
WITH reversal_entries AS (
    -- Identify all reversal entries and their corresponding original transactions
    SELECT 
        lf_rev.id AS reversal_id,
        lf_rev.reversal_for_id AS original_id,
        lf_rev.transaction_datetime AS reversal_datetime,
        lf_rev.transaction_type AS reversal_type,
        lf_rev.credit AS reversal_credit,
        lf_rev.debit AS reversal_debit,
        lf_orig.transaction_type AS original_type,
        lf_orig.credit AS original_credit,
        lf_orig.debit AS original_debit
    FROM form_ledger_finance lf_rev
    JOIN form_ledger_finance lf_orig ON lf_rev.reversal_for_id = lf_orig.id
    WHERE lf_rev.reversal_for_id IS NOT NULL
),
consolidated_transactions AS (
    -- Get all AR and COGS transactions for charge lines excluding reversed transactions
    SELECT
        lf.id,
        lf.charge_line_id,
        lf.invoice_id,
        lf.invoice_no,
        lf.transaction_datetime,
        lf.created_by,
        -- For transactions that have reversals, show the transaction type with '(Reversed)' appended
        CASE 
            WHEN re.original_id IS NOT NULL THEN lf.transaction_type || ' (Reversed)'
            ELSE lf.transaction_type
        END AS transaction_type,
        lf.account_type,
        lf.debit,
        lf.credit,
        lf.notes,
        lf.reversal_for_id,
        lf.post_datetime,
        -- Flag if this transaction has been reversed
        CASE WHEN re.original_id IS NOT NULL THEN TRUE ELSE FALSE END AS is_reversed,
        -- For normal transactions, show the amount
        -- For reversed transactions, the amount is zeroed out (we'll show the reversal instead)
        CASE 
            WHEN re.original_id IS NOT NULL THEN 0
            ELSE COALESCE(lf.debit, 0)::numeric - COALESCE(lf.credit, 0)::numeric
        END AS amount
    FROM form_ledger_finance lf
    LEFT JOIN reversal_entries re ON lf.id = re.original_id
    WHERE 
        lf.charge_line_id IS NOT NULL
        AND lf.account_type IN ('AR', 'COGS')
        AND lf.transaction_type NOT IN ('Posting')
        AND lf.reversal_for_id IS NULL -- Exclude reversal entries themselves
    
    UNION ALL
    
    -- Add reversal transactions (consolidated with original transaction info)
    SELECT
        re.reversal_id AS id,
        lf.charge_line_id,
        lf.invoice_id,
        lf.invoice_no,
        re.reversal_datetime AS transaction_datetime,
        lf.created_by,
        CASE 
            WHEN re.reversal_type LIKE '%Reversal' THEN re.reversal_type
            ELSE re.reversal_type || ' Reversal'
        END AS transaction_type,
        lf.account_type,
        re.reversal_debit AS debit,
        re.reversal_credit AS credit,
        lf.notes || ' (Reversal)' AS notes,
        re.original_id AS reversal_for_id,
        lf.post_datetime,
        TRUE AS is_reversed,
        -- Show the net effect of the reversal (opposite of the original transaction)
        CASE
            WHEN lf.account_type IN ('AR', 'Revenue') THEN -1 * (COALESCE(re.original_debit, 0)::numeric - COALESCE(re.original_credit, 0)::numeric)
            ELSE -1 * (COALESCE(re.original_credit, 0)::numeric - COALESCE(re.original_debit, 0)::numeric) 
        END AS amount
    FROM reversal_entries re
    JOIN form_ledger_finance lf ON lf.id = re.original_id
)
SELECT
    'form_ledger_finance' AS form_name,
    ct.id AS form_id,
    ct.charge_line_id,
    ct.invoice_id,
    ct.invoice_no,
    ct.transaction_datetime,
    TO_CHAR(ct.transaction_datetime, 'MM/DD/YYYY HH:MM AM') as transaction_date_time,
    COALESCE(u.displayname, u.firstname || ' ' || u.lastname) AS by,
    ct.created_by,
    ct.transaction_type,
    ct.account_type,
    CASE
        WHEN ct.debit > 0 THEN ct.debit
        ELSE NULL
    END AS debit,
    CASE
        WHEN ct.credit > 0 THEN ct.credit
        ELSE NULL
    END AS credit,
    ct.amount,
    format_currency(
        CASE
            WHEN ct.amount >= 0 THEN ct.amount
            ELSE -ct.amount
        END
    ) AS formatted_amount,
    CASE 
        WHEN ct.amount > 0 THEN 'Debit'
        WHEN ct.amount < 0 THEN 'Credit'
        ELSE NULL
    END AS entry_type,
    ct.notes,
    ct.reversal_for_id,
    lcl.charge_no,
    -- Row coloring
    CASE
        -- Reversals get red highlighting
        WHEN ct.is_reversed THEN '#FFE5E5'
        -- Credit adjustments to AR get orange highlighting
        WHEN ct.account_type = 'AR' AND ct.transaction_type IN ('Adjustment', 'Writeoff') AND ct.amount < 0 THEN '#FFEAD9'
        -- Payments get green highlighting
        WHEN ct.account_type = 'AR' AND ct.transaction_type = 'Payment' AND ct.amount < 0 THEN '#E1FAF2'
        -- Default is no highlighting
        ELSE NULL
    END AS __row_color,
    ct.is_reversed,
    -- Format the transaction datetime for display
    TO_CHAR(ct.transaction_datetime, 'MM/DD/YYYY HH:MI AM') AS formatted_datetime,
    -- Available actions
    CASE
        WHEN ct.reversal_for_id IS NULL 
             AND NOT check_closed_period(COALESCE(ct.post_datetime, ct.transaction_datetime)::timestamp)
             AND NOT ct.is_reversed
        THEN ARRAY['reverse']
        ELSE NULL
    END AS available_actions
FROM consolidated_transactions ct
LEFT JOIN form_user u ON ct.created_by = u.id
LEFT JOIN form_ledger_charge_line lcl ON ct.charge_line_id = lcl.id
ORDER BY ct.transaction_datetime DESC;

-- Supporting index for better performance
CREATE INDEX IF NOT EXISTS idx_ledger_finance_charge_transaction 
ON form_ledger_finance(charge_line_id, account_type, transaction_type)
WHERE charge_line_id IS NOT NULL;