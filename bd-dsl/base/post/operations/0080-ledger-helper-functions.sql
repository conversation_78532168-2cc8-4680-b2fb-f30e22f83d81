DO $$ BEGIN
  PERFORM drop_all_function_signatures('process_po_price_adjustment');
END $$;
CREATE OR REPLACE FUNCTION process_po_price_adjustment(
    p_po_id INTEGER,
    p_inventory_id INTEGER,
    p_site_id INTEGER,
    p_old_price NUMERIC,
    p_new_price NUMERIC,
    p_quantity NUMERIC,
    p_adjustment_reason_id TEXT,
    p_notes TEXT,
    p_user_id INTEGER
) RETURNS BOOLEAN AS $$
DECLARE
    v_account_id INTEGER;
    v_adjustment_amount NUMERIC;
    v_success BOOLEAN := FALSE;
BEGIN
    -- Get site account ID
    SELECT id INTO v_account_id 
    FROM form_billing_account
    WHERE type = 'Site'
    AND site_id = p_site_id
    AND archived IS NOT TRUE
    AND deleted IS NOT TRUE
    LIMIT 1;
    
    IF v_account_id IS NULL THEN
        -- Log error
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Site account not found for PO price adjustment',
            'Error adjusting PO price',
            'FUNCTION',
            current_schema(),
            'ledger_inventory',
            jsonb_build_object(
                'function_name', 'process_po_price_adjustment',
                'po_id', p_po_id,
                'site_id', p_site_id,
                'inventory_id', p_inventory_id
            )
        );
        
        RETURN FALSE;
    END IF;
    
    -- Calculate adjustment amount (total adjustment for the quantity)
    v_adjustment_amount := (p_new_price - p_old_price) * p_quantity;
    
    BEGIN
        -- If new price is higher than old price (increase cost)
        IF p_new_price > p_old_price THEN
            -- Debit Inventory (increase asset value)
            INSERT INTO form_ledger_finance (
                po_id,
                account_id,
                inventory_id,
                site_id,
                source_id,
                source_form,
                post_datetime,
                transaction_datetime,
                account_type,
                transaction_type,
                adjustment_reason_id,
                quantity,
                debit,
                credit,
                created_on,
                created_by,
                notes
            ) VALUES (
                p_po_id,
                v_account_id,
                p_inventory_id,
                p_site_id,
                p_po_id,
                'po',
                get_site_timestamp(p_site_id),
                get_site_timestamp(p_site_id),
                'Inventory',
                'Adjustment',
                p_adjustment_reason_id,
                p_quantity,
                ABS(v_adjustment_amount),
                0.00,
                CURRENT_TIMESTAMP,
                p_user_id,
                COALESCE(p_notes, 'PO price adjustment')
            );
            
            -- Credit Purchases Clearing (increase liability)
            INSERT INTO form_ledger_finance (
                po_id,
                account_id,
                inventory_id,
                site_id,
                source_id,
                source_form,
                post_datetime,
                transaction_datetime,
                account_type,
                transaction_type,
                adjustment_reason_id,
                quantity,
                debit,
                credit,
                created_on,
                created_by,
                notes
            ) VALUES (
                p_po_id,
                v_account_id,
                p_inventory_id,
                p_site_id,
                p_po_id,
                'po',
                get_site_timestamp(p_site_id),
                get_site_timestamp(p_site_id),
                'Purchases Clearing',
                'Adjustment',
                p_adjustment_reason_id,
                p_quantity,
                0.00,
                ABS(v_adjustment_amount),
                CURRENT_TIMESTAMP,
                p_user_id,
                COALESCE(p_notes, 'PO price adjustment')
            );
        ELSE
            -- Credit Inventory (decrease asset value)
            INSERT INTO form_ledger_finance (
                po_id,
                account_id,
                inventory_id,
                site_id,
                source_id,
                source_form,
                post_datetime,
                transaction_datetime,
                account_type,
                transaction_type,
                adjustment_reason_id,
                quantity,
                debit,
                credit,
                created_on,
                created_by,
                notes
            ) VALUES (
                p_po_id,
                v_account_id,
                p_inventory_id,
                p_site_id,
                p_po_id,
                'po',
                get_site_timestamp(p_site_id),
                get_site_timestamp(p_site_id),
                'Inventory',
                'Adjustment',
                p_adjustment_reason_id,
                p_quantity,
                0.00,
                ABS(v_adjustment_amount),
                CURRENT_TIMESTAMP,
                p_user_id,
                COALESCE(p_notes, 'PO price adjustment')
            );
            
            -- Debit Purchases Clearing (decrease liability)
            INSERT INTO form_ledger_finance (
                po_id,
                account_id,
                inventory_id,
                site_id,
                source_id,
                source_form,
                post_datetime,
                transaction_datetime,
                account_type,
                transaction_type,
                adjustment_reason_id,
                quantity,
                debit,
                credit,
                created_on,
                created_by,
                notes
            ) VALUES (
                p_po_id,
                v_account_id,
                p_inventory_id,
                p_site_id,
                p_po_id,
                'po',
                get_site_timestamp(p_site_id),
                get_site_timestamp(p_site_id),
                'Purchases Clearing',
                'Adjustment',
                p_adjustment_reason_id,
                p_quantity,
                ABS(v_adjustment_amount),
                0.00,
                CURRENT_TIMESTAMP,
                p_user_id,
                COALESCE(p_notes, 'PO price adjustment')
            );
        END IF;
        
        -- Update acquisition costs in inventory tables
        -- Note: This would update the acquisition_cost in the appropriate inventory tables
        -- Implementation depends on your specific requirements
        
        v_success := TRUE;
        RETURN TRUE;
        
    EXCEPTION WHEN OTHERS THEN
        -- Log the error
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Transaction failed during PO price adjustment',
            'FUNCTION',
            current_schema(),
            'ledger_inventory',
            jsonb_build_object(
                'function_name', 'process_po_price_adjustment',
                'po_id', p_po_id,
                'site_id', p_site_id,
                'inventory_id', p_inventory_id,
                'old_price', p_old_price,
                'new_price', p_new_price,
                'quantity', p_quantity,
                'success_flag', v_success
            )
        );
        
        RETURN FALSE;
    END;
END;
$$ LANGUAGE plpgsql VOLATILE;