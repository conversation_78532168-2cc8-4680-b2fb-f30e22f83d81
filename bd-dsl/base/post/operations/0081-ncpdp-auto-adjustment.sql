-- Auto Adjustment Trigger for NCPD<PERSON> Claims
-- This trigger handles automatic adjustments for NCPDP claims after the invoice is updated.
-- It uses views to calculate remaining balances and applies adjustments only for posted revenue invoices.
CREATE OR REPLACE FUNCTION process_ncpdp_auto_adjustment()
RETURNS TRIGGER AS $$
DECLARE
    v_adjustment_amount NUMERIC := 0;
    v_adjustment_type TEXT;
    v_invoice_no TEXT;
    v_highest_expected_charge_line_id BIGINT;
    v_charge_record RECORD;
    v_proportion NUMERIC;
    v_line_adjustment_amount NUMERIC := 0;
    v_line_expected_amount NUMERIC;
    v_processed_adjustment_amount NUMERIC := 0;
    v_processed_expected_amount NUMERIC := 0;
    v_total_expected NUMERIC;
    v_total_paid NUMERIC;
    v_account_id INTEGER;
    v_ncpdp_id INTEGER;
    v_site_id INTEGER;
    v_add_auto_adjust_expected BOOLEAN := FALSE;
    v_revenue_accepted_posted BOOLEAN := FALSE;
    v_claim_status TEXT;
    v_payer_id INTEGER;
BEGIN
    RAISE LOG 'process_ncpdp_auto_adjustment Response ID %', NEW.ID;
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    IF NEW.claim_no IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'claim_no cannot be null',
            'Validation in process_ncpdp_auto_adjustment trigger',
            'TRIGGER',
            current_schema(),
            'form_ncpdp_response',
            jsonb_build_object('claim_no', NEW.claim_no)
        );
        RETURN NEW;
    END IF;

    -- Fetch necessary data for adjustment
    SELECT
        bi.invoice_no,
        COALESCE(bi.total_expected, 0),
        COALESCE(bi.total_paid, 0),
        CASE WHEN COALESCE(bi.revenue_accepted_posted, 'No') = 'Yes' THEN TRUE ELSE FALSE END,
        claim.id,
        claim.site_id,
        claim.payer_id,
        CASE WHEN COALESCE(py.auto_adjust_expected, 'No') = 'Yes' THEN TRUE ELSE FALSE END,
        CASE
            WHEN stat.transaction_response_status = 'P' THEN 'Payable'
            WHEN (NEW.response_status = 'R' OR stat.transaction_response_status = 'R') THEN 'Rejected'
            WHEN stat.transaction_response_status = 'A' THEN 'Approved'
            WHEN stat.transaction_response_status = 'C' THEN 'Captured'
            WHEN stat.transaction_response_status = 'F' THEN 'PA Deferred'
            WHEN stat.transaction_response_status IN ('Q','D','S') THEN 'Duplicate'
            WHEN stat.transaction_response_status = 'B' THEN 'Benefit'
            ELSE 'Sent'
        END
    INTO
        v_invoice_no,
        v_total_expected,
        v_total_paid,
        v_revenue_accepted_posted,
        v_ncpdp_id,
        v_site_id,
        v_payer_id,
        v_add_auto_adjust_expected,
        v_claim_status
    FROM form_billing_invoice bi
    INNER JOIN form_ncpdp claim ON claim.claim_no = NEW.claim_no
    INNER JOIN sf_form_billing_invoice_to_ncpdp sfbi 
        ON sfbi.form_ncpdp_fk = claim.id AND sfbi.form_billing_invoice_fk = bi.id
        AND sfbi.archive IS NOT TRUE
        AND sfbi.delete IS NOT TRUE
    LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_stat sfstat 
        ON sfstat.form_ncpdp_response_fk = NEW.id
        AND sfstat.archive IS NOT TRUE
        AND sfstat.delete IS NOT TRUE
    LEFT JOIN form_ncpdp_response_stat stat
        ON stat.id = sfstat.form_ncpdp_response_stat_fk 
        AND stat.archived IS NOT TRUE
        AND stat.deleted IS NOT TRUE
    LEFT JOIN form_payer py
        ON claim.payer_id = py.id
        AND py.archived IS NOT TRUE
        AND py.deleted IS NOT TRUE
    WHERE bi.archived IS NOT TRUE
    AND bi.deleted IS NOT TRUE
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes';

    IF v_invoice_no IS NULL THEN
        RAISE LOG 'No valid invoice found for NCPDP claim response ID %', NEW.ID;
        RETURN NEW;
    END IF;

    -- Calculate adjustment amount
    v_adjustment_amount := ABS(v_total_expected - v_total_paid);

    -- Find the charge line with the highest expected amount for remainder distribution
    SELECT id
    INTO v_highest_expected_charge_line_id
    FROM form_ledger_charge_line
    WHERE invoice_no = v_invoice_no
    AND (archived IS NULL OR archived = FALSE)
    AND (deleted IS NULL OR deleted = FALSE)
    AND COALESCE(void, 'No') <> 'Yes'
    AND COALESCE(zeroed, 'No') <> 'Yes'
    ORDER BY expected DESC
    LIMIT 1;

    -- Get account ID for the payer
    SELECT id INTO v_account_id
    FROM form_billing_account
    WHERE type = 'Payer'
    AND payer_id = v_payer_id
    AND archived IS NOT TRUE
    AND deleted IS NOT TRUE;

    IF v_account_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Account not found for NCPDP adjustment transaction',
            'Error in auto adjustment',
            'TRIGGER',
            current_schema(),
            'form_ncpdp_response',
            jsonb_build_object(
                'claim_no', NEW.claim_no,
                'payer_id', v_payer_id
            )
        );
        RETURN NEW;
    END IF;

    -- Create adjustment records if needed (for discrepancies between expected and paid)
    IF v_adjustment_amount > 0 AND v_add_auto_adjust_expected IS TRUE AND v_claim_status NOT IN ('Margin') AND v_revenue_accepted_posted IS TRUE THEN
        -- Determine if this is a Credit or Debit based on difference
        IF v_total_expected > v_total_paid THEN
            -- Expected is higher than paid, so this is a Credit (write-off)
            v_adjustment_type := 'Credit';
        ELSE
            -- Expected is lower than paid, so this is a Debit (increase)
            v_adjustment_type := 'Debit';
        END IF;
        
        -- Reset processing variables
        v_processed_adjustment_amount := 0;
        v_processed_expected_amount := 0;
        
        -- Process each charge line for adjustments
        FOR v_charge_record IN
            SELECT 
                lcl.*,
                bi.id as invoice_id
            FROM form_ledger_charge_line lcl
            INNER JOIN form_billing_invoice bi ON bi.invoice_no = lcl.invoice_no
            AND bi.archived IS NOT TRUE
            AND bi.deleted IS NOT TRUE
            AND COALESCE(bi.void, 'No') <> 'Yes'
            AND COALESCE(bi.zeroed, 'No') <> 'Yes'
            WHERE lcl.invoice_no = v_invoice_no
            AND lcl.archived IS NOT TRUE
            AND lcl.deleted IS NOT TRUE
            AND COALESCE(lcl.void, 'No') <> 'Yes'
            AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
            ORDER BY lcl.expected DESC
        LOOP
            IF v_charge_record.id <> v_highest_expected_charge_line_id THEN
                v_proportion := v_charge_record.expected / v_total_expected;
                v_line_expected_amount := ROUND(v_total_expected::numeric * v_proportion, 2);
                v_processed_expected_amount := v_processed_expected_amount + v_line_expected_amount;
                v_line_adjustment_amount := ROUND(v_adjustment_amount::numeric * v_proportion, 2);
            ELSE
                v_line_expected_amount := ROUND(v_total_expected::numeric - v_processed_expected_amount::numeric, 2);
                v_line_adjustment_amount := ROUND(v_adjustment_amount::numeric - v_processed_adjustment_amount::numeric, 2);
            END IF;
            v_processed_adjustment_amount := v_processed_adjustment_amount + v_line_adjustment_amount;

            -- Skip if adjustment amount is zero
            IF v_line_adjustment_amount <= 0 THEN
                CONTINUE;
            END IF;
            -- Create AR transaction record for adjustment
            RAISE LOG 'Creating NCPDP auto-adjustment charge_line: %, amount: %', 
                        v_charge_record.charge_no, v_line_adjustment_amount;
            
            INSERT INTO form_ledger_finance (
                invoice_id, 
                invoice_no,
                site_id,
                charge_line_id, 
                charge_no,
                account_id, 
                source_id, 
                source_form, 
                post_datetime, 
                transaction_datetime,
                inventory_id,
                ledger_inventory_id,
                ledger_lot_id,
                ledger_serial_id,
                account_type, 
                transaction_type,
                debit, 
                credit, 
                created_on, 
                created_by,
                notes
            ) VALUES (
                v_charge_record.invoice_id,
                v_charge_record.invoice_no,
                v_charge_record.site_id,
                v_charge_record.id,
                v_charge_record.charge_no,
                v_account_id,
                v_ncpdp_id,
                'ncpdp',
                get_site_timestamp(v_charge_record.site_id, FALSE),
                get_site_timestamp(v_charge_record.site_id, FALSE),
                v_charge_record.inventory_id,
                NULL,
                NULL,
                NULL,
                'AR',
                'Adjustment',
                CASE WHEN v_adjustment_type = 'Debit' THEN v_line_adjustment_amount ELSE 0.00 END,
                CASE WHEN v_adjustment_type = 'Credit' THEN v_line_adjustment_amount ELSE 0.00 END,
                CURRENT_TIMESTAMP,
                NEW.created_by,
                'NCPDP auto-adjustment for charge_line: ' || v_charge_record.charge_no
            );

            INSERT INTO form_ledger_finance (
                invoice_id, 
                invoice_no,
                site_id,
                charge_line_id, 
                charge_no,
                account_id, 
                source_id, 
                source_form, 
                post_datetime, 
                transaction_datetime,
                inventory_id,
                ledger_inventory_id,
                ledger_lot_id,
                ledger_serial_id,
                account_type, 
                transaction_type,
                debit, 
                credit, 
                created_on, 
                created_by,
                notes
            ) VALUES (
                v_charge_record.invoice_id,
                v_charge_record.invoice_no,
                v_charge_record.site_id,
                v_charge_record.id,
                v_charge_record.charge_no,
                v_account_id,
                v_ncpdp_id,
                'ncpdp',
                get_site_timestamp(v_charge_record.site_id, FALSE),
                get_site_timestamp(v_charge_record.site_id, FALSE),
                v_charge_record.inventory_id,
                NULL,
                NULL,
                NULL,
                CASE WHEN v_adjustment_type = 'Debit' THEN 'Contra-Revenue' ELSE 'Revenue' END,
                'Adjustment',
                CASE WHEN v_adjustment_type = 'Credit' THEN v_line_adjustment_amount ELSE 0.00 END,
                CASE WHEN v_adjustment_type = 'Debit' THEN v_line_adjustment_amount ELSE 0.00 END,
                CURRENT_TIMESTAMP,
                NEW.created_by,
                'NCPDP auto-adjustment for charge_line: ' || v_charge_record.charge_no
            );
        END LOOP;
        
        RAISE LOG 'Created automatic adjustment AR transactions for invoice: %', v_invoice_no;
    END IF;
    RETURN NEW;

EXCEPTION WHEN OTHERS THEN
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        SQLERRM,
        'Transaction failed during NCPDP auto adjustment',
        'TRIGGER',
        current_schema(),
        'form_ncpdp_response',
        jsonb_build_object(
            'function_name', 'process_ncpdp_auto_adjustment',
            'claim_no', NEW.claim_no,
            'invoice_no', v_invoice_no
        )
    );
    RAISE NOTICE 'Error processing NCPDP auto adjustment: %', SQLERRM;
    RAISE;
END;
$$ LANGUAGE plpgsql VOLATILE;

DROP TRIGGER IF EXISTS process_ncpdp_auto_adjustment_trigger ON form_ncpdp_response;
CREATE CONSTRAINT TRIGGER process_ncpdp_auto_adjustment_trigger
    AFTER INSERT ON form_ncpdp_response
    DEFERRABLE INITIALLY DEFERRED
    FOR EACH ROW
    EXECUTE FUNCTION process_ncpdp_auto_adjustment(); 