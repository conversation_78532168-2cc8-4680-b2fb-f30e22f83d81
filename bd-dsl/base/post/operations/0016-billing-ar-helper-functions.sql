
-- Function to check if date falls within a closed period


CREATE OR R<PERSON>LACE FUNCTION check_closed_period(post_date timestamp) RETURNS BOOLEAN AS $$
DECLARE
    is_closed BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 FROM form_billing_closing
        WHERE post_date BETWEEN start_date AND end_date
        AND locked_period = 'Yes'
        AND void IS NULL
        AND deleted IS NOT TRUE
        AND archived IS NOT TRUE
    ) INTO is_closed;
    
    RETURN is_closed;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION calculate_close_of_month_totals(
    p_start_date date, 
    p_end_date date
)
RETURNS JSON AS $$
DECLARE
    result JSON;
    v_total_ar numeric(15,2);
    v_total_ap numeric(15,2);
    v_total_credits numeric(15,2);
    v_total_debits numeric(15,2);
BEGIN
    -- Calculate total AR (Accounts Receivable)
    SELECT ROUND(COALESCE(SUM(
        CASE 
            WHEN account_type = 'AR' AND debit::numeric > 0 THEN debit::numeric
            WHEN account_type = 'AR' AND credit::numeric > 0 THEN -credit::numeric
            ELSE 0 
        END
    ), 0.00)::numeric,2) INTO v_total_ar
    FROM form_ledger_finance
    WHERE post_datetime::date BETWEEN p_start_date AND p_end_date;

    -- Calculate total AP (Accounts Payable)
    SELECT ROUND(COALESCE(SUM(
        CASE 
            WHEN account_type = 'AP' AND credit::numeric > 0 THEN credit::numeric
            WHEN account_type = 'AP' AND debit::numeric > 0 THEN -debit::numeric
            ELSE 0 
        END
    ), 0.00)::numeric,2) INTO v_total_ap
    FROM form_ledger_finance
    WHERE post_datetime::date BETWEEN p_start_date AND p_end_date;

    -- Calculate total credits across all account types
    SELECT ROUND(COALESCE(SUM(credit::numeric), 0.00)::numeric,2) INTO v_total_credits
    FROM form_ledger_finance
    WHERE post_datetime::date BETWEEN p_start_date AND p_end_date;

    -- Calculate total debits across all account types
    SELECT ROUND(COALESCE(SUM(debit::numeric), 0.00)::numeric,2) INTO v_total_debits
    FROM form_ledger_finance
    WHERE post_datetime::date BETWEEN p_start_date AND p_end_date;

    result := jsonb_build_object(
        'total_ar', v_total_ar,
        'total_ap', v_total_ap,
        'total_credits', v_total_credits,
        'total_debits', v_total_debits
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION get_invoice_actions(
    p_invoice_id integer
) 
RETURNS text[] AS $$
DECLARE
    v_invoice_no text;
    v_revenue_accepted_posted text;
    v_delivery_ticket_id integer;
    v_zeroed text;
    v_void text;
    v_post_datetime timestamp;
    v_billing_method_id text;
    v_actions text[] := '{}';
    v_period_closed boolean;
    v_has_unvoided_children boolean;
    v_has_unzeroed_children boolean;
    v_balance numeric;
    v_can_be_adjudicated boolean DEFAULT FALSE;
    v_is_reversable boolean DEFAULT FALSE;
    v_is_rebillable boolean DEFAULT FALSE;
    v_ncpdp_status text;
    v_mm_status text;
    v_close_no text;
    v_total_balance_due numeric;
BEGIN

    SELECT 
    bi.invoice_no,
    bi.revenue_accepted_posted,
    bi.delivery_ticket_id,
    bi.zeroed, 
    bi.void, 
    bi.post_datetime, 
    bi.billing_method_id, 
    it.total_balance_due,
    bi.close_no,
    nc.status as ncpdp_status,
    mm.status as mm_status  
    INTO v_invoice_no, v_revenue_accepted_posted, v_delivery_ticket_id, v_zeroed, v_void, v_post_datetime, v_billing_method_id, v_total_balance_due, v_close_no, v_ncpdp_status, v_mm_status
    FROM form_billing_invoice bi
    INNER JOIN vw_invoice_finance_totals it ON it.invoice_id = bi.id
    LEFT JOIN sf_form_billing_invoice_to_ncpdp sfnc ON sfnc.form_billing_invoice_fk = bi.id
    LEFT JOIN form_ncpdp nc ON nc.id = sfnc.form_ncpdp_fk
    LEFT JOIN sf_form_billing_invoice_to_med_claim sfmm ON sfmm.form_billing_invoice_fk = bi.id
    LEFT JOIN form_med_claim mm ON mm.id = sfmm.form_med_claim_fk
    WHERE bi.id = p_invoice_id
    AND bi.archived IS NOT TRUE
    AND bi.deleted IS NOT TRUE;

    -- Check if period is closed
    v_period_closed := check_closed_period(v_post_datetime::timestamp);
    
    -- Check for unvoided/unzeroed children
    SELECT EXISTS (
        SELECT 1 
        FROM form_billing_invoice 
        WHERE parent_invoice_no = v_invoice_no 
        AND COALESCE(void, 'No') = 'No'
    ) INTO v_has_unvoided_children;
    
    SELECT EXISTS (
        SELECT 1 
        FROM form_billing_invoice 
        WHERE parent_invoice_no = v_invoice_no 
        AND COALESCE(zeroed, 'No') = 'No'
    ) INTO v_has_unzeroed_children;

    -- Determine if rebillable, reversable, or can be adjudicated
    -- Using similar logic to the views you provided
    IF v_billing_method_id = 'ncpdp' THEN
        v_is_rebillable := v_ncpdp_status IN ('Payable', 'Margin', 'Captured', 'Reversal Rejected', 'Rebill Rejected', 'PA Deferred', 'Duplicate');
        v_is_reversable := v_ncpdp_status IN ('Payable', 'Margin', 'Captured', 'Reversal Rejected', 'Rebill Rejected', 'PA Deferred', 'Duplicate');
    ELSIF v_billing_method_id = 'mm' THEN
        v_is_rebillable := v_mm_status IN ('Sent', 'Processing', 'Forwarded', 'Received', 'Accepted', 'Revised', 'Warning', 'On-hold', 'Awaiting Requested Information', 'Request for Additional Information', 'Under Review', 'Partially Paid', 'Payable');
        v_is_reversable := v_mm_status IN ('Sent', 'Processing', 'Forwarded', 'Received', 'Accepted', 'Revised', 'Warning', 'On-hold', 'Awaiting Requested Information', 'Request for Additional Information', 'Under Review', 'Partially Paid', 'Payable');
    END IF;
    
    v_can_be_adjudicated := v_ncpdp_status IS NULL AND v_billing_method_id IN ('ncpdp', 'mm');

    -- Always included actions
    v_actions := array_append(v_actions, 'view_billing_notes');
    v_actions := array_append(v_actions, 'view_payer');
    
    -- Conditional actions
    IF v_delivery_ticket_id IS NOT NULL THEN
        v_actions := array_append(v_actions, 'view_delivery_tickets');
    END IF;
    
    IF COALESCE(v_revenue_accepted_posted, 'No') = 'Yes' AND COALESCE(v_zeroed, 'No') <> 'Yes' AND NOT v_period_closed AND NOT v_has_unzeroed_children THEN
        v_actions := array_append(v_actions, 'zero_balance');
    END IF;

    IF COALESCE(v_zeroed, 'No') <> 'Yes' AND COALESCE(v_void, 'No') <> 'Yes' AND v_total_balance_due > 0 THEN
        v_actions := array_append(v_actions, 'generate_cob');
    END IF;
    
    IF COALESCE(v_revenue_accepted_posted, 'No') <> 'Yes' AND COALESCE(v_void, 'No') <> 'Yes' AND NOT v_has_unvoided_children THEN
        v_actions := array_append(v_actions, 'void_invoice');
    END IF;
    
    IF COALESCE(v_revenue_accepted_posted, 'No') = 'Yes' THEN
        v_actions := array_append(v_actions, 'view_transactions');
    END IF;
    
    IF v_billing_method_id = 'ncpdp' AND v_ncpdp_status IS NOT NULL THEN
        v_actions := array_append(v_actions, 'view_ncpdp_response');
    END IF;
    
    IF NOT v_period_closed THEN
        v_actions := array_append(v_actions, 'change_claim_status');
    END IF;
    
    IF v_can_be_adjudicated AND v_close_no IS NULL THEN
        v_actions := array_append(v_actions, 'adjudicate_claim');
    END IF;
    
    IF v_is_reversable AND v_close_no IS NULL THEN
        v_actions := array_append(v_actions, 'reverse_claim');
    END IF;
    
    IF v_is_rebillable AND v_close_no IS NULL THEN
        v_actions := array_append(v_actions, 'rebill_claim');
    END IF;
    
    RETURN v_actions;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_ar_aging_days(
    p_invoice_id integer,
    p_reference_date date DEFAULT CURRENT_DATE,
    p_aging_method text DEFAULT 'billed_date'  -- Options: 'billed_date', 'dos', 'posted_date'
) RETURNS integer AS $$
DECLARE
    v_reference_date date;
    v_aging_days integer;
BEGIN
    -- Get the appropriate reference date based on aging method
    IF p_aging_method = 'billed_date' THEN
        SELECT billed_datetime::date INTO v_reference_date
        FROM form_billing_invoice
        WHERE id = p_invoice_id;
    ELSIF p_aging_method = 'dos' THEN
        SELECT date_of_service INTO v_reference_date
        FROM form_billing_invoice
        WHERE id = p_invoice_id;
    ELSIF p_aging_method = 'posted_date' THEN
        SELECT post_datetime::date INTO v_reference_date
        FROM form_billing_invoice
        WHERE id = p_invoice_id;
    ELSE
        -- Default to billed date if invalid method provided
        SELECT billed_datetime::date INTO v_reference_date
        FROM form_billing_invoice
        WHERE id = p_invoice_id;
    END IF;
    
    -- If no valid date found, return NULL
    IF v_reference_date IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Calculate the aging days
    v_aging_days := p_reference_date - v_reference_date;
    
    RETURN v_aging_days;
END;
$$ LANGUAGE plpgsql;

DO $$ BEGIN
  PERFORM drop_all_function_signatures('get_ar_aging_bucket');
END $$;
CREATE OR REPLACE FUNCTION get_ar_aging_bucket(p_aging_days integer)
RETURNS text AS $$
BEGIN
    RETURN CASE
        WHEN p_aging_days IS NULL THEN 'Unknown'
        WHEN p_aging_days <= 30 THEN '0-30 Days'
        WHEN p_aging_days <= 60 THEN '31-60 Days'
        WHEN p_aging_days <= 90 THEN '61-90 Days'
        WHEN p_aging_days <= 120 THEN '91-120 Days'
        ELSE '120+ Days'
    END;
END;
$$ LANGUAGE plpgsql;
