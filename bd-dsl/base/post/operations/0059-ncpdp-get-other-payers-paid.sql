
CREATE OR REPLACE FUNCTION get_other_payer_paid_data(root_claim TEXT)
RETURNS ncpdp_cob_opaid[] AS $BODY$
DECLARE
    v_start_time timestamp;
    v_execution_time interval;
    v_error_message text;
    v_params jsonb;
    v_result ncpdp_cob_opaid[];
BEGIN
    -- Record start time
    v_start_time := clock_timestamp();

    -- Build parameters JSON for logging
    v_params := jsonb_build_object(
        'root_claim', root_claim
    );

    BEGIN  -- Start exception block
        -- Log function call
        PERFORM log_billing_function(
            'get_other_payer_paid_data'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            NULL::text,
            clock_timestamp() - v_start_time
        );

        -- Validate required parameters
        IF root_claim IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Root Claim # cannot be null',
                'Validating required parameters in get_other_payer_paid_data',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                jsonb_build_object(
                    'function_name', 'get_other_payer_paid_data',
                    'root_claim', root_claim
                )
            );
            RAISE EXCEPTION 'Root Claim # cannot be null';
        END IF;

        -- Get claim hierarchy first
        WITH claim_hierarchy AS (
            SELECT * FROM get_claim_hierarchy(root_claim)
        ),
        -- Get all paid amounts, focusing on primary claim (depth = 0)
        all_paid_amounts AS (
            -- Ingredient cost + dispensing fee
            SELECT 
                ch.claim_no::text as claim_no,
                '07'::text AS other_amt_qualifier,
                (ch.ing_cst_paid + ch.disp_fee_paid)::numeric AS other_amt_paid,
                0::integer AS rank
            FROM claim_hierarchy ch
            WHERE (ch.ing_cst_paid > 0 OR ch.disp_fee_paid > 0)
                AND ch.depth = 0  -- Focus on primary claim
            
            UNION ALL
            
            -- Professional service fee paid
            SELECT 
                ch.claim_no::text as claim_no,
                '11'::text AS other_amt_qualifier,
                ch.prof_svc_paid::numeric AS other_amt_paid,
                0::integer AS rank
            FROM claim_hierarchy ch
            WHERE ch.prof_svc_paid > 0
                AND ch.depth = 0  -- Focus on primary claim
            
            UNION ALL
            
            -- Incentive amount paid
            SELECT 
                ch.claim_no::text as claim_no,
                '05'::text AS other_amt_qualifier,
                ch.incv_amt_paid::numeric AS other_amt_paid,
                1::integer AS rank
            FROM claim_hierarchy ch
            WHERE ch.incv_amt_paid > 0
                AND ch.depth = 0  -- Focus on primary claim
            
            -- If there's no data from specific fields, add at least the total paid amount
            UNION ALL
            
            SELECT
                ch.claim_no::text as claim_no,
                '07'::text AS other_amt_qualifier,
                ch.total_paid::numeric AS other_amt_paid,
                3::integer AS rank
            FROM claim_hierarchy ch
            WHERE ch.total_paid > 0
                AND ch.depth = 0  -- Focus on primary claim
                AND NOT EXISTS (
                    SELECT 1 
                    FROM claim_hierarchy ch2 
                    WHERE ch2.depth = 0 
                    AND (ch2.ing_cst_paid > 0 OR ch2.disp_fee_paid > 0)
                )
            
            UNION ALL
            
            -- Other amount paid types
            SELECT 
                ch.claim_no,
                (unnested.amt_details).other_amt_qualifier::text AS other_amt_qualifier,
                (unnested.amt_details).other_amt_paid::numeric AS other_amt_paid,
                2 AS rank
            FROM claim_hierarchy ch
            CROSS JOIN LATERAL (
                SELECT unnest(COALESCE(ch.oth_amt_paid, '{}'::oth_amt_type[])) AS amt_details
            ) AS unnested
            WHERE (unnested.amt_details).other_amt_paid > 0
                AND ch.depth = 0  -- Focus on primary claim
        )
        -- Create the final result as an array of the composite type
        SELECT ARRAY_AGG(resp::ncpdp_cob_opaid) INTO v_result
        FROM (
            SELECT 
                other_amt_qualifier::text AS paid_qualifier,
                other_amt_paid::numeric AS other_payer_amount_paid
            FROM all_paid_amounts
            WHERE other_amt_paid > 0
            ORDER BY rank, other_amt_qualifier
            LIMIT 9  -- Maximum of 9 other payment amounts allowed
        ) resp;

        -- Validate result - if empty but we have a root claim, create a default entry
        IF (v_result IS NULL OR array_length(v_result, 1) IS NULL) THEN
            -- Get total paid from primary claim
            WITH claim_hierarchy AS (
                SELECT * FROM get_claim_hierarchy(root_claim)
            ),
            primary_claim AS (
                SELECT total_paid::numeric, 
                pt_pay_amt::numeric
                FROM claim_hierarchy
                WHERE depth = 0
                LIMIT 1
            )
            SELECT ARRAY_AGG(resp::ncpdp_cob_opaid) INTO v_result
            FROM (
                SELECT 
                    '07'::text AS paid_qualifier,
                    GREATEST(COALESCE(pc.total_paid, 0), COALESCE(pc.pt_pay_amt, 0))::numeric AS other_payer_amount_paid
                FROM primary_claim pc
                WHERE GREATEST(COALESCE(pc.total_paid, 0), COALESCE(pc.pt_pay_amt, 0)) > 0
            ) resp;
        END IF;

        -- Final null check
        IF v_result IS NULL THEN
            v_result := ARRAY[]::ncpdp_cob_opaid[];
        END IF;

        -- Log successful completion
        PERFORM log_billing_function(
            'get_other_payer_paid_data'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            NULL::text,
            clock_timestamp() - v_start_time
        );

        RETURN v_result;

    EXCEPTION WHEN OTHERS THEN
        -- Log error
        v_error_message := SQLERRM;
        
        -- Log to billing error log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            v_error_message,
            'Exception in get_other_payer_paid_data',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );

        -- Log to NCPDP function log
        PERFORM log_billing_function(
            'get_other_payer_paid_data'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            v_error_message::text,
            clock_timestamp() - v_start_time
        );
        RAISE;
    END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;