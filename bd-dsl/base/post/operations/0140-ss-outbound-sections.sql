CREATE OR REPLACE FUNCTION remove_null_values_from_jsonb(p_jsonb JSONB)
RETURNS JSONB AS $$
DECLARE
    v_key TEXT;
    v_value JSONB;
    v_result_obj JSONB := '{}'::JSONB;
    v_result_arr JSONB := '[]'::JSONB;
    v_temp_val JSONB;
BEGIN
    IF p_jsonb IS NULL THEN
        RETURN NULL;
    END IF;

    IF jsonb_typeof(p_jsonb) = 'object' THEN
        FOR v_key, v_value IN SELECT key, value FROM jsonb_each(p_jsonb) LOOP
            IF v_value IS NOT NULL AND v_value::TEXT <> 'null' THEN
                v_temp_val := remove_null_values_from_jsonb(v_value);
                IF v_temp_val IS NOT NULL AND (jsonb_typeof(v_temp_val) <> 'object' OR v_temp_val <> '{}'::JSONB) AND (jsonb_typeof(v_temp_val) <> 'array' OR v_temp_val <> '[]'::JSONB) THEN
                    v_result_obj := v_result_obj || jsonb_build_object(v_key, v_temp_val);
                END IF;
            END IF;
        END LOOP;
        IF v_result_obj = '{}'::JSONB AND p_jsonb <> '{}'::JSONB THEN 
             RETURN NULL;
        END IF;
        RETURN v_result_obj;
    ELSIF jsonb_typeof(p_jsonb) = 'array' THEN
        FOR v_temp_val IN SELECT value FROM jsonb_array_elements(p_jsonb) LOOP
            IF v_temp_val IS NOT NULL AND v_temp_val::TEXT <> 'null' THEN
                DECLARE
                    v_processed_element JSONB;
                BEGIN
                    v_processed_element := remove_null_values_from_jsonb(v_temp_val);
                    IF v_processed_element IS NOT NULL AND (jsonb_typeof(v_processed_element) <> 'object' OR v_processed_element <> '{}'::JSONB) AND (jsonb_typeof(v_processed_element) <> 'array' OR v_processed_element <> '[]'::JSONB) THEN
                        v_result_arr := v_result_arr || v_processed_element;
                    END IF;
                END;
            END IF;
        END LOOP;
         IF v_result_arr = '[]'::JSONB AND p_jsonb <> '[]'::JSONB THEN
            RETURN NULL; 
        END IF;
        RETURN v_result_arr;
    ELSE
        RETURN p_jsonb; 
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE STRICT;

CREATE OR REPLACE FUNCTION _build_outbound_header(
    p_ss_message_id INTEGER, 
    p_site_id INTEGER, 
    p_order_group_reason_code TEXT, 
    p_rx_group_reason_code TEXT
) RETURNS JSONB LANGUAGE plpgsql AS $$
DECLARE
    v_header_payload JSONB;
    v_ss_message_record form_ss_message;
    v_site_record form_site;
BEGIN
    RAISE LOG 'Starting _build_outbound_header for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_outbound_header', p_ss_message_id;
    END IF;

    IF p_site_id IS NOT NULL THEN
        SELECT * INTO v_site_record FROM form_site WHERE id = p_site_id;
        IF NOT FOUND THEN
            RAISE WARNING 'form_site with id % not found in _build_outbound_header, using sent_from from form_ss_message.', p_site_id;
        END IF;
    END IF;
    
    v_header_payload := remove_null_values_from_jsonb(jsonb_build_object(
        'DigitalSignatureIndicator', CASE v_ss_message_record.digital_signature_indicator
                                       WHEN 'true' THEN TRUE
                                       WHEN 'false' THEN FALSE
                                       ELSE NULL
                                   END,
        'To', v_ss_message_record.send_to,
        'From', v_ss_message_record.sent_from,
        'MessageID', v_ss_message_record.message_id,
        'RelatesToMessageID', v_ss_message_record.related_message_id,
        'SentTime', TO_CHAR(NOW() AT TIME ZONE 'UTC', 'YYYYMMDDHH24MISS'), -- Changed format
        'SenderSoftware', CASE 
                              WHEN v_ss_message_record.sender_software_developer IS NOT NULL OR 
                                   v_ss_message_record.sender_software_product IS NOT NULL OR 
                                   v_ss_message_record.sender_software_version IS NOT NULL THEN
                                  remove_null_values_from_jsonb(jsonb_build_object(
                                      'SenderSoftwareDeveloper', v_ss_message_record.sender_software_developer,
                                      'SenderSoftwareProduct', v_ss_message_record.sender_software_product,
                                      'SenderSoftwareVersionRelease', v_ss_message_record.sender_software_version
                                  ))
                              ELSE NULL 
                          END,
        'PrescriberOrderGroup', CASE 
                                   WHEN v_ss_message_record.order_group_no IS NOT NULL THEN
                                       remove_null_values_from_jsonb(jsonb_build_object(
                                           'OrderGroupNumber', v_ss_message_record.order_group_no,
                                           'ItemCountInOrderGroup', v_ss_message_record.order_group_icnt,
                                           'TotalCountForOrderGroup', v_ss_message_record.order_group_tcnt,
                                           'OrderGroupReason', p_order_group_reason_code
                                       ))
                                   ELSE NULL
                               END,
        'RxReferenceOrderGroup', CASE 
                                    WHEN v_ss_message_record.rx_group_no IS NOT NULL THEN 
                                        remove_null_values_from_jsonb(jsonb_build_object(
                                            'OrderGroupNumber', v_ss_message_record.rx_group_no,
                                            'ItemCountInOrderGroup', v_ss_message_record.rx_group_icnt,
                                            'TotalCountForOrderGroup', v_ss_message_record.rx_group_tcnt,
                                            'OrderGroupReason', p_rx_group_reason_code
                                        ))
                                    ELSE NULL
                                END,
        'PrescriberOrderNumber', v_ss_message_record.physician_order_id, 
        'RxReferenceNumber', v_ss_message_record.pharmacy_rx_no 
    ));

    RAISE LOG 'Finished _build_outbound_header for ss_message_id: %. Payload: %', p_ss_message_id, v_header_payload;
    RETURN v_header_payload;
END;
$$;

CREATE OR REPLACE FUNCTION _build_outbound_patient(
    p_ss_message_id INTEGER
) RETURNS JSONB LANGUAGE plpgsql AS $$
DECLARE
    v_patient_payload JSONB;
    v_ss_message_record form_ss_message;
BEGIN
    RAISE LOG 'Starting _build_outbound_patient for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_outbound_patient', p_ss_message_id;
    END IF;

    IF v_ss_message_record.message_type IN ('NewRx', 'RxRenewalRequest', 'RxChangeRequest', 'CancelRx') THEN
        v_patient_payload := remove_null_values_from_jsonb(jsonb_build_object(
            'HumanPatient', remove_null_values_from_jsonb(jsonb_build_object(
                'Name', remove_null_values_from_jsonb(jsonb_build_object(
                    'FirstName', v_ss_message_record.patient_first_name,
                    'LastName', v_ss_message_record.patient_last_name,
                    'MiddleName', v_ss_message_record.patient_middle_name
                )),
                'DateOfBirth', CASE WHEN v_ss_message_record.patient_dob IS NOT NULL THEN jsonb_build_object('Date', TO_CHAR(v_ss_message_record.patient_dob, 'YYYYMMDD')) ELSE NULL END, -- Changed format
                'Gender', v_ss_message_record.patient_gender, 
                'Address', CASE WHEN v_ss_message_record.patient_home_street_1 IS NOT NULL OR v_ss_message_record.patient_home_city IS NOT NULL OR v_ss_message_record.patient_home_zip IS NOT NULL THEN 
                               remove_null_values_from_jsonb(jsonb_build_object(
                                   'AddressLine1', v_ss_message_record.patient_home_street_1,
                                   'AddressLine2', v_ss_message_record.patient_home_street_2,
                                   'City', v_ss_message_record.patient_home_city,
                                   'StateProvince', v_ss_message_record.patient_home_state, 
                                   'PostalCode', v_ss_message_record.patient_home_zip 
                               ))
                           ELSE NULL 
                       END,
                'CommunicationNumbers', CASE WHEN v_ss_message_record.patient_phone IS NOT NULL THEN 
                                           jsonb_build_object('PrimaryTelephone', jsonb_build_object('Number', regexp_replace(v_ss_message_record.patient_phone, '[^0-9]', '', 'g')))
                                      ELSE NULL 
                                      END,
                'Identification', remove_null_values_from_jsonb(jsonb_build_object(
                    'MedicalRecordIdentificationNumber', v_ss_message_record.patient_mrn, 
                    'MedicareNumber', v_ss_message_record.patient_medicare,
                    'MedicaidNumber', v_ss_message_record.patient_medicaid,
                    'SocialSecurity', v_ss_message_record.patient_ssn,
                    'REMSPatientID', v_ss_message_record.patient_rems
                ))
            ))
        ));
    ELSE
        v_patient_payload := NULL;
    END IF;

    RAISE LOG 'Finished _build_outbound_patient for ss_message_id: %. Payload: %', p_ss_message_id, v_patient_payload;
    RETURN v_patient_payload;
END;
$$;

CREATE OR REPLACE FUNCTION _build_outbound_pharmacy(
    p_site_id INTEGER 
) RETURNS JSONB LANGUAGE plpgsql AS $$ 
DECLARE
    v_pharmacy_payload JSONB;
    v_site_record form_site; 
BEGIN
    RAISE LOG 'Starting _build_outbound_pharmacy for site_id: %', p_site_id;

    IF p_site_id IS NULL THEN
        RAISE LOG 'Site ID is NULL in _build_outbound_pharmacy, returning NULL';
        RETURN NULL;
    END IF;

    SELECT * INTO v_site_record FROM form_site WHERE id = p_site_id;
    IF NOT FOUND THEN
        RAISE WARNING 'form_site with id % not found in _build_outbound_pharmacy. Returning NULL.', p_site_id;
        RETURN NULL;
    END IF;

    v_pharmacy_payload := remove_null_values_from_jsonb(jsonb_build_object(
        'BusinessName', v_site_record.name,
        'Identification', remove_null_values_from_jsonb(jsonb_build_object(
            'NPI', v_site_record.npi,
            'NCPDPID', v_site_record.ncpdp_id 
        )),
        'Address', CASE WHEN v_site_record.address1 IS NOT NULL OR v_site_record.city IS NOT NULL OR v_site_record.zip IS NOT NULL THEN 
                       remove_null_values_from_jsonb(jsonb_build_object(
                           'AddressLine1', v_site_record.address1,
                           'AddressLine2', v_site_record.address2,
                           'City', v_site_record.city,
                           'StateProvince', v_site_record.state_id, 
                           'PostalCode', v_site_record.zip
                       ))
                   ELSE NULL 
                   END,
        'CommunicationNumbers', CASE WHEN v_site_record.phone IS NOT NULL OR v_site_record.fax IS NOT NULL THEN
                                   remove_null_values_from_jsonb(jsonb_build_object(
                                       'PrimaryTelephone', CASE WHEN v_site_record.phone IS NOT NULL THEN jsonb_build_object('Number', regexp_replace(v_site_record.phone, '[^0-9]', '', 'g')) ELSE NULL END,
                                       'Fax', CASE WHEN v_site_record.fax IS NOT NULL THEN jsonb_build_object('Number', regexp_replace(v_site_record.fax, '[^0-9]', '', 'g')) ELSE NULL END
                                   ))
                               ELSE NULL 
                               END
    ));

    RAISE LOG 'Finished _build_outbound_pharmacy for site_id: %. Payload: %', p_site_id, v_pharmacy_payload;
    RETURN v_pharmacy_payload;
END;
$$;

CREATE OR REPLACE FUNCTION _build_outbound_prescriber(
    p_ss_message_id INTEGER,
    p_prescriber_spec_actual_code TEXT
) RETURNS JSONB LANGUAGE plpgsql AS $$
DECLARE
    v_prescriber_payload JSONB;
    v_ss_message_record form_ss_message;
BEGIN
    RAISE LOG 'Starting _build_outbound_prescriber for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_outbound_prescriber', p_ss_message_id;
    END IF;

    IF v_ss_message_record.message_type IN ('NewRx', 'RxRenewalRequest', 'RxChangeRequest', 'CancelRx') THEN
        v_prescriber_payload := remove_null_values_from_jsonb(jsonb_build_object(
            'NonVeterinarian', remove_null_values_from_jsonb(jsonb_build_object(
                'Name', remove_null_values_from_jsonb(jsonb_build_object(
                    'LastName', v_ss_message_record.prescriber_last_name,
                    'FirstName', v_ss_message_record.prescriber_first_name
                )),
                'Identification', remove_null_values_from_jsonb(jsonb_build_object(
                    'NPI', v_ss_message_record.prescriber_npi,
                    'DEANumber', v_ss_message_record.prescriber_dea,
                    'REMSHealthcareProviderEnrollmentID', v_ss_message_record.prescriber_rems, 
                    'StateControlSubstanceNumber', v_ss_message_record.prescriber_state_cs_lic,
                    'MedicareNumber', v_ss_message_record.prescriber_medicare,
                    'MedicaidNumber', v_ss_message_record.prescriber_medicaid,
                    'StateLicenseNumber', v_ss_message_record.prescriber_state_lic,
                    'CertificateToPrescribe', v_ss_message_record.prescriber_certificate_to_prescribe, 
                    'Data2000WaiverID', v_ss_message_record.prescriber_2000waiver_id 
                )),
                'Specialty', p_prescriber_spec_actual_code, 
                'Address', CASE WHEN v_ss_message_record.prescriber_address_1 IS NOT NULL OR v_ss_message_record.prescriber_city IS NOT NULL OR v_ss_message_record.prescriber_zip IS NOT NULL THEN 
                               remove_null_values_from_jsonb(jsonb_build_object(
                                   'AddressLine1', v_ss_message_record.prescriber_address_1,
                                   'AddressLine2', v_ss_message_record.prescriber_address_2,
                                   'City', v_ss_message_record.prescriber_city,
                                   'StateProvince', v_ss_message_record.prescriber_state,
                                   'PostalCode', v_ss_message_record.prescriber_zip
                               ))
                           ELSE NULL 
                           END,
                'CommunicationNumbers', CASE WHEN v_ss_message_record.prescriber_phone IS NOT NULL OR v_ss_message_record.prescriber_fax IS NOT NULL THEN
                                           remove_null_values_from_jsonb(jsonb_build_object(
                                               'PrimaryTelephone', CASE WHEN v_ss_message_record.prescriber_phone IS NOT NULL THEN remove_null_values_from_jsonb(jsonb_build_object('Number', regexp_replace(v_ss_message_record.prescriber_phone, '[^0-9]', '', 'g'), 'Extension', v_ss_message_record.prescriber_extension)) ELSE NULL END,
                                               'Fax', CASE WHEN v_ss_message_record.prescriber_fax IS NOT NULL THEN jsonb_build_object('Number', regexp_replace(v_ss_message_record.prescriber_fax, '[^0-9]', '', 'g')) ELSE NULL END
                                           ))
                                       ELSE NULL 
                                       END,
                'PracticeLocation', CASE WHEN v_ss_message_record.prescriber_loc_name IS NOT NULL OR v_ss_message_record.prescriber_loc_ncpdp_id IS NOT NULL OR v_ss_message_record.prescriber_loc_dea IS NOT NULL OR 
                                             v_ss_message_record.prescriber_loc_rems IS NOT NULL OR v_ss_message_record.prescriber_loc_state_cs_lic IS NOT NULL OR v_ss_message_record.prescriber_loc_medicare IS NOT NULL OR
                                             v_ss_message_record.prescriber_loc_medicaid IS NOT NULL OR v_ss_message_record.prescriber_loc_state_lic IS NOT NULL THEN
                                        remove_null_values_from_jsonb(jsonb_build_object(
                                            'BusinessName', v_ss_message_record.prescriber_loc_name,
                                            'Identification', remove_null_values_from_jsonb(jsonb_build_object(
                                                'NCPDPID', v_ss_message_record.prescriber_loc_ncpdp_id,
                                                'DEANumber', v_ss_message_record.prescriber_loc_dea,
                                                'REMSHealthcareSettingEnrollmentID', v_ss_message_record.prescriber_loc_rems,
                                                'StateControlSubstanceNumber', v_ss_message_record.prescriber_loc_state_cs_lic,
                                                'MedicareNumber', v_ss_message_record.prescriber_loc_medicare,
                                                'MedicaidNumber', v_ss_message_record.prescriber_loc_medicaid,
                                                'StateLicenseNumber', v_ss_message_record.prescriber_loc_state_lic
                                            ))
                                        ))
                                    ELSE NULL 
                                    END,
                'PrescriberAgent', CASE WHEN v_ss_message_record.prescriber_agent_first_name IS NOT NULL OR v_ss_message_record.prescriber_agent_last_name IS NOT NULL THEN
                                      remove_null_values_from_jsonb(jsonb_build_object('Name', remove_null_values_from_jsonb(jsonb_build_object(
                                          'FirstName', v_ss_message_record.prescriber_agent_first_name,
                                          'LastName', v_ss_message_record.prescriber_agent_last_name
                                      ))))
                                  ELSE NULL 
                                  END
            )))
        );
    ELSE
        v_prescriber_payload := NULL;
    END IF;
    
    RAISE LOG 'Finished _build_outbound_prescriber for ss_message_id: %. Payload: %', p_ss_message_id, v_prescriber_payload;
    RETURN v_prescriber_payload;
END;
$$;

CREATE OR REPLACE FUNCTION _build_outbound_supervisor(
    p_ss_message_id INTEGER,
    p_supervisor_spec_actual_code TEXT
) RETURNS JSONB LANGUAGE plpgsql AS $$
DECLARE
    v_supervisor_payload JSONB;
    v_ss_message_record form_ss_message;
BEGIN
    RAISE LOG 'Starting _build_outbound_supervisor for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_outbound_supervisor', p_ss_message_id;
    END IF;

    IF v_ss_message_record.message_type IN ('NewRx', 'RxRenewalRequest', 'RxChangeRequest') AND 
       (v_ss_message_record.supervisor_first_name IS NOT NULL OR v_ss_message_record.supervisor_last_name IS NOT NULL) THEN
        v_supervisor_payload := remove_null_values_from_jsonb(jsonb_build_object(
            'NonVeterinarian', remove_null_values_from_jsonb(jsonb_build_object(
                'Name', remove_null_values_from_jsonb(jsonb_build_object(
                    'LastName', v_ss_message_record.supervisor_last_name,
                    'FirstName', v_ss_message_record.supervisor_first_name
                )),
                'Identification', remove_null_values_from_jsonb(jsonb_build_object(
                    'NPI', v_ss_message_record.supervisor_npi,
                    'DEANumber', v_ss_message_record.supervisor_dea,
                    'REMSHealthcareSettingEnrollmentID', v_ss_message_record.supervisor_rems, 
                    'StateControlSubstanceNumber', v_ss_message_record.supervisor_state_cs_lic,
                    'MedicareNumber', v_ss_message_record.supervisor_medicare,
                    'MedicaidNumber', v_ss_message_record.supervisor_medicaid,
                    'StateLicenseNumber', v_ss_message_record.supervisor_state_lic,
                    'CertificateToPrescribe', v_ss_message_record.supervisor_certificate_to_prescribe,
                    'Data2000WaiverID', v_ss_message_record.supervisor_2000waiver_id
                )),
                'Specialty', p_supervisor_spec_actual_code,
                'CommunicationNumbers', CASE WHEN v_ss_message_record.supervisor_phone IS NOT NULL OR v_ss_message_record.supervisor_fax IS NOT NULL THEN
                                           remove_null_values_from_jsonb(jsonb_build_object(
                                               'PrimaryTelephone', CASE WHEN v_ss_message_record.supervisor_phone IS NOT NULL THEN remove_null_values_from_jsonb(jsonb_build_object('Number', regexp_replace(v_ss_message_record.supervisor_phone, '[^0-9]', '', 'g'), 'Extension', v_ss_message_record.supervisor_extension)) ELSE NULL END,
                                               'Fax', CASE WHEN v_ss_message_record.supervisor_fax IS NOT NULL THEN jsonb_build_object('Number', regexp_replace(v_ss_message_record.supervisor_fax, '[^0-9]', '', 'g')) ELSE NULL END
                                           ))
                                       ELSE NULL 
                                       END
            )))
        );
    ELSE
        v_supervisor_payload := NULL;
    END IF;

    RAISE LOG 'Finished _build_outbound_supervisor for ss_message_id: %. Payload: %', p_ss_message_id, v_supervisor_payload;
    RETURN v_supervisor_payload;
END;
$$;

CREATE OR REPLACE FUNCTION _build_outbound_fu_prescriber(
    p_ss_message_id INTEGER,
    p_fu_prescriber_spec_actual_code TEXT
) RETURNS JSONB LANGUAGE plpgsql AS $$
DECLARE
    v_fu_prescriber_payload JSONB;
    v_ss_message_record form_ss_message;
BEGIN
    RAISE LOG 'Starting _build_outbound_fu_prescriber for ss_message_id: %', p_ss_message_id;
    
    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_outbound_fu_prescriber', p_ss_message_id;
    END IF;

    IF v_ss_message_record.message_type IN ('NewRx', 'RxRenewalRequest', 'RxChangeRequest') AND 
       (v_ss_message_record.fu_prescriber_first_name IS NOT NULL OR v_ss_message_record.fu_prescriber_last_name IS NOT NULL) THEN
        v_fu_prescriber_payload := remove_null_values_from_jsonb(jsonb_build_object(
            'NonVeterinarian', remove_null_values_from_jsonb(jsonb_build_object(
                 'Name', remove_null_values_from_jsonb(jsonb_build_object(
                    'LastName', v_ss_message_record.fu_prescriber_last_name,
                    'FirstName', v_ss_message_record.fu_prescriber_first_name
                )),
                'Identification', remove_null_values_from_jsonb(jsonb_build_object(
                    'NPI', v_ss_message_record.fu_prescriber_npi,
                    'DEANumber', v_ss_message_record.fu_prescriber_dea,
                    'REMSHealthcareSettingEnrollmentID', v_ss_message_record.fu_prescriber_rems, 
                    'StateControlSubstanceNumber', v_ss_message_record.fu_prescriber_state_cs_lic,
                    'MedicareNumber', v_ss_message_record.fu_prescriber_medicare,
                    'MedicaidNumber', v_ss_message_record.fu_prescriber_medicaid,
                    'StateLicenseNumber', v_ss_message_record.fu_prescriber_state_lic,
                    'CertificateToPrescribe', v_ss_message_record.fu_prescriber_certificate_to_prescribe,
                    'Data2000WaiverID', v_ss_message_record.fu_prescriber_2000waiver_id
                )),
                'Specialty', p_fu_prescriber_spec_actual_code,
                'Address', CASE WHEN v_ss_message_record.fu_prescriber_address_1 IS NOT NULL OR v_ss_message_record.fu_prescriber_city IS NOT NULL OR v_ss_message_record.fu_prescriber_zip IS NOT NULL THEN 
                               remove_null_values_from_jsonb(jsonb_build_object(
                                   'AddressLine1', v_ss_message_record.fu_prescriber_address_1,
                                   'AddressLine2', v_ss_message_record.fu_prescriber_address_2,
                                   'City', v_ss_message_record.fu_prescriber_city,
                                   'StateProvince', v_ss_message_record.fu_prescriber_state,
                                   'PostalCode', v_ss_message_record.fu_prescriber_zip
                               ))
                           ELSE NULL 
                           END,
                'CommunicationNumbers', CASE WHEN v_ss_message_record.fu_prescriber_phone IS NOT NULL OR v_ss_message_record.fu_prescriber_fax IS NOT NULL THEN
                                           remove_null_values_from_jsonb(jsonb_build_object(
                                               'PrimaryTelephone', CASE WHEN v_ss_message_record.fu_prescriber_phone IS NOT NULL THEN remove_null_values_from_jsonb(jsonb_build_object('Number', regexp_replace(v_ss_message_record.fu_prescriber_phone, '[^0-9]', '', 'g'), 'Extension', v_ss_message_record.fu_prescriber_extension)) ELSE NULL END,
                                               'Fax', CASE WHEN v_ss_message_record.fu_prescriber_fax IS NOT NULL THEN jsonb_build_object('Number', regexp_replace(v_ss_message_record.fu_prescriber_fax, '[^0-9]', '', 'g')) ELSE NULL END
                                           ))
                                       ELSE NULL 
                                       END
            )))
        );
    ELSE
        v_fu_prescriber_payload := NULL;
    END IF;

    RAISE LOG 'Finished _build_outbound_fu_prescriber for ss_message_id: %. Payload: %', p_ss_message_id, v_fu_prescriber_payload;
    RETURN v_fu_prescriber_payload;
END;
$$;

CREATE OR REPLACE FUNCTION _build_outbound_allergies_section(
    p_ss_message_id INTEGER 
) RETURNS JSONB LANGUAGE plpgsql AS $$
DECLARE
    v_allergies_payload JSONB;
    v_allergies_array JSONB;
    v_temp_allergies_obj JSONB := '{}'::JSONB;
    v_ss_message_record form_ss_message;
BEGIN
    RAISE LOG 'Starting _build_outbound_allergies_section for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_outbound_allergies_section', p_ss_message_id;
    END IF;

    IF v_ss_message_record.message_type <> 'CancelRxResponse' THEN 
        IF v_ss_message_record.id IS NOT NULL THEN 
            SELECT jsonb_agg( remove_null_values_from_jsonb(jsonb_build_object() 
                || CASE WHEN alg.source IS NOT NULL THEN jsonb_build_object('SourceOfInformation', alg.source) ELSE '{}'::JSONB END 
                || jsonb_build_object('AdverseEvent', remove_null_values_from_jsonb(jsonb_build_object()
                    || CASE WHEN alg.adverse_event_code_id IS NOT NULL THEN jsonb_build_object('Code', alg.adverse_event_code_id) ELSE '{}'::JSONB END 
                    || CASE WHEN alg.adverse_event_text IS NOT NULL THEN jsonb_build_object('Text', alg.adverse_event_text) ELSE '{}'::JSONB END
                ))
                || jsonb_build_object('DrugProductCoded', remove_null_values_from_jsonb(jsonb_build_object()
                    || CASE WHEN alg.drug_product_code IS NOT NULL THEN jsonb_build_object('Code', alg.drug_product_code) ELSE '{}'::JSONB END
                    || CASE WHEN alg.drug_product_qualifier_id IS NOT NULL THEN jsonb_build_object('Qualifier', alg.drug_product_qualifier_id) ELSE '{}'::JSONB END 
                    || CASE WHEN alg.drug_product_text IS NOT NULL THEN jsonb_build_object('Text', alg.drug_product_text) ELSE '{}'::JSONB END
                ))
                || jsonb_build_object('ReactionCoded', remove_null_values_from_jsonb(jsonb_build_object()
                    || CASE WHEN alg.reaction_code_id IS NOT NULL THEN jsonb_build_object('Code', alg.reaction_code_id) ELSE '{}'::JSONB END 
                    || CASE WHEN alg.reaction_text IS NOT NULL THEN jsonb_build_object('Text', alg.reaction_text) ELSE '{}'::JSONB END
                ))
                || jsonb_build_object('SeverityCoded', remove_null_values_from_jsonb(jsonb_build_object()
                    || CASE WHEN alg.severity_code_id IS NOT NULL THEN jsonb_build_object('Code', alg.severity_code_id) ELSE '{}'::JSONB END 
                    || CASE WHEN alg.severity_text IS NOT NULL THEN jsonb_build_object('Text', alg.severity_text) ELSE '{}'::JSONB END
                ))
                || CASE WHEN alg.effective_date IS NOT NULL THEN jsonb_build_object('EffectiveDate', jsonb_build_object('Date', TO_CHAR(alg.effective_date, 'YYYY-MM-DD'))) ELSE '{}'::JSONB END
                || CASE WHEN alg.expiration_date IS NOT NULL THEN jsonb_build_object('ExpirationDate', jsonb_build_object('Date', TO_CHAR(alg.expiration_date, 'YYYY-MM-DD'))) ELSE '{}'::JSONB END
            ))
            INTO v_allergies_array
            FROM form_ss_allergy alg
            JOIN sf_form_ss_message_to_ss_allergy sf ON alg.id = sf.form_ss_allergy_fk
            WHERE sf.form_ss_message_fk = v_ss_message_record.id AND alg.deleted IS NOT TRUE AND alg.archived IS NOT TRUE AND sf.archive IS NOT TRUE AND sf.delete IS NOT TRUE;

            IF v_allergies_array IS NOT NULL AND jsonb_array_length(v_allergies_array) > 0 THEN
                v_temp_allergies_obj := v_temp_allergies_obj || jsonb_build_object('Allergies', v_allergies_array);
            END IF;
        END IF;

        IF v_ss_message_record.nka IS NOT NULL THEN 
             v_temp_allergies_obj := v_temp_allergies_obj || jsonb_build_object('NoKnownAllergies', v_ss_message_record.nka);
        END IF;
        
        IF v_temp_allergies_obj <> '{}'::JSONB THEN
            v_allergies_payload := jsonb_build_object('AllergyOrAdverseEvent', v_temp_allergies_obj);
        ELSE
            v_allergies_payload := NULL;
        END IF;

    ELSE
        v_allergies_payload := NULL;
    END IF;

    RAISE LOG 'Finished _build_outbound_allergies_section for ss_message_id: %. Payload: %', p_ss_message_id, v_allergies_payload;
    RETURN v_allergies_payload;
END;
$$;

CREATE OR REPLACE FUNCTION _build_outbound_benefits_array(
    p_ss_message_id INTEGER
) RETURNS JSONB LANGUAGE plpgsql AS $$
DECLARE
    v_benefits_payload JSONB;
    v_ss_message_record form_ss_message;
BEGIN
    RAISE LOG 'Starting _build_outbound_benefits_array for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_outbound_benefits_array', p_ss_message_id;
    END IF;

    IF v_ss_message_record.message_type <> 'CancelRx' AND v_ss_message_record.message_type <> 'CancelRxResponse' THEN
        IF v_ss_message_record.id IS NOT NULL THEN 
            SELECT jsonb_agg( remove_null_values_from_jsonb(jsonb_build_object() 
                || CASE WHEN ben.payer_type_id IS NOT NULL THEN jsonb_build_object('PayerType', ben.payer_type_id) ELSE '{}'::JSONB END 
                || CASE WHEN ben.payer_level IS NOT NULL THEN jsonb_build_object('PayerResponsibilityCode', ben.payer_level) ELSE '{}'::JSONB END
                || CASE WHEN ben.payer_name IS NOT NULL THEN jsonb_build_object('PayerName', ben.payer_name) ELSE '{}'::JSONB END
                || jsonb_build_object('PayerIdentification', remove_null_values_from_jsonb(jsonb_build_object()
                    || CASE WHEN ben.pbm_participant_id IS NOT NULL THEN jsonb_build_object('PayerID', ben.pbm_participant_id) ELSE '{}'::JSONB END
                    || CASE WHEN ben.bin IS NOT NULL THEN jsonb_build_object('IINNumber', ben.bin) ELSE '{}'::JSONB END
                    || CASE WHEN ben.pcn IS NOT NULL THEN jsonb_build_object('ProcessorIdentificationNumber', ben.pcn) ELSE '{}'::JSONB END
                    || CASE WHEN ben.naic_id IS NOT NULL THEN jsonb_build_object('NAICCode', ben.naic_id) ELSE '{}'::JSONB END
                    || CASE WHEN ben.hp_id IS NOT NULL THEN jsonb_build_object('StandardUniqueHealthPlanIdentifier', ben.hp_id) ELSE '{}'::JSONB END
                ))
                || CASE WHEN ben.person_code IS NOT NULL THEN jsonb_build_object('PersonCode', ben.person_code) ELSE '{}'::JSONB END
                || CASE WHEN ben.group_id IS NOT NULL THEN jsonb_build_object('GroupID', ben.group_id) ELSE '{}'::JSONB END
                || CASE WHEN ben.group_name IS NOT NULL THEN jsonb_build_object('GroupName', ben.group_name) ELSE '{}'::JSONB END
                || CASE WHEN ben.pbm_member_id IS NOT NULL THEN jsonb_build_object('PBMMemberID', ben.pbm_member_id) ELSE '{}'::JSONB END
                || CASE WHEN ben.relationship_code IS NOT NULL THEN jsonb_build_object('PatientRelationshipCode', ben.relationship_code) ELSE '{}'::JSONB END 
                || CASE WHEN ben.cardholder_id IS NOT NULL THEN jsonb_build_object('CardholderID', ben.cardholder_id) ELSE '{}'::JSONB END
                || jsonb_build_object('CardHolderName', remove_null_values_from_jsonb(jsonb_build_object()
                    || CASE WHEN ben.cardholder_first_name IS NOT NULL THEN jsonb_build_object('FirstName', ben.cardholder_first_name) ELSE '{}'::JSONB END
                    || CASE WHEN ben.cardholder_last_name IS NOT NULL THEN jsonb_build_object('LastName', ben.cardholder_last_name) ELSE '{}'::JSONB END
                ))
                || jsonb_build_object('CommunicationNumbers', remove_null_values_from_jsonb(jsonb_build_object()
                    || CASE WHEN ben.payer_phone IS NOT NULL THEN jsonb_build_object('PrimaryTelephone', jsonb_build_object('Number', ben.payer_phone)) ELSE '{}'::JSONB END
                    || CASE WHEN ben.payer_fax IS NOT NULL THEN jsonb_build_object('Fax', jsonb_build_object('Number', ben.payer_fax)) ELSE '{}'::JSONB END
                ))
                || CASE 
                       WHEN ben.prohibit_renewal_request = 'true' THEN jsonb_build_object('ProhibitRenewalRequest', TRUE)
                       WHEN ben.prohibit_renewal_request = 'false' THEN jsonb_build_object('ProhibitRenewalRequest', FALSE)
                       ELSE '{}'::JSONB 
                   END
            ))
            INTO v_benefits_payload 
            FROM form_ss_benefit ben
            JOIN sf_form_ss_message_to_ss_benefit sf ON ben.id = sf.form_ss_benefit_fk
            WHERE sf.form_ss_message_fk = v_ss_message_record.id AND ben.deleted IS NOT TRUE AND ben.archived IS NOT TRUE AND sf.archive IS NOT TRUE AND sf.delete IS NOT TRUE;
        ELSE
            v_benefits_payload := NULL;
        END IF;
    ELSE
        v_benefits_payload := NULL;
    END IF;

    RAISE LOG 'Finished _build_outbound_benefits_array for ss_message_id: %. Payload: %', p_ss_message_id, v_benefits_payload;
    RETURN v_benefits_payload; 
END;
$$;

CREATE OR REPLACE FUNCTION _build_medication_other_dates_array(
    p_ss_message_id INTEGER
) RETURNS JSONB LANGUAGE plpgsql AS $$
DECLARE
    v_arr JSONB;
    v_ss_message_record form_ss_message;
BEGIN
    RAISE LOG 'Starting _build_medication_other_dates_array for ss_message_id: %', p_ss_message_id;
    
    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_medication_other_dates_array', p_ss_message_id;
    END IF;

    SELECT COALESCE(jsonb_agg( remove_null_values_from_jsonb(jsonb_build_object()
        || jsonb_build_object('OtherMedicationDateQualifier', dt.qual)
        || jsonb_build_object('OtherMedicationDate', jsonb_build_object('Date', TO_CHAR(dt.dt_val, 'YYYYMMDD'))) -- Changed format
    )), '[]'::JSONB)
    INTO v_arr
    FROM (
        SELECT v_ss_message_record.start_date AS dt_val, 'StartDate' AS qual WHERE v_ss_message_record.start_date IS NOT NULL
        UNION ALL
        SELECT v_ss_message_record.expiration_date AS dt_val, 'ExpirationDate' AS qual WHERE v_ss_message_record.expiration_date IS NOT NULL
        UNION ALL
        SELECT v_ss_message_record.effective_date AS dt_val, 'EffectiveDate' AS qual WHERE v_ss_message_record.effective_date IS NOT NULL
    ) dt;
    RAISE LOG 'Finished _build_medication_other_dates_array for ss_message_id: %. Payload: %', p_ss_message_id, v_arr;
    RETURN CASE WHEN jsonb_array_length(v_arr) > 0 THEN v_arr ELSE NULL END;
END;
$$;


CREATE OR REPLACE FUNCTION build_compound_ingredient_jsonb(p_compound_ingredient_id INTEGER, p_ss_message_id INTEGER)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    v_ingredient_jsonb JSONB;
    v_ingredient_dues_json_array JSONB;
    v_compound_record form_ss_compound;
BEGIN
    SELECT * INTO v_compound_record FROM form_ss_compound WHERE id = p_compound_ingredient_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_compound with id % not found in build_compound_ingredient_jsonb', p_compound_ingredient_id;
    END IF;

    -- Resolve codes for compound ingredient
    v_ingredient_jsonb := remove_null_values_from_jsonb(jsonb_build_object(
        'ItemNumber', remove_null_values_from_jsonb(jsonb_build_object(
            'Qualifier', v_compound_record.qual_id, 
            'Code', v_compound_record.code
        )),
        'CompoundIngredientItemDescription', v_compound_record.description,
        'Strength', CASE WHEN v_compound_record.strength IS NOT NULL OR v_compound_record.strength_form_id IS NOT NULL OR v_compound_record.strength_uom_id IS NOT NULL THEN
                       remove_null_values_from_jsonb(jsonb_build_object(
                           'StrengthValue', v_compound_record.strength,
                           'StrengthForm', CASE WHEN v_compound_record.strength_form_id IS NOT NULL THEN jsonb_build_object('Code', v_compound_record.strength_form_id) ELSE NULL END, 
                           'StrengthUnitOfMeasure', CASE WHEN v_compound_record.strength_uom_id IS NOT NULL THEN jsonb_build_object('Code', v_compound_record.strength_uom_id) ELSE NULL END 
                       ))
                   ELSE NULL 
                   END,
        'Quantity', remove_null_values_from_jsonb(jsonb_build_object(
            'Value', v_compound_record.quantity,
            'CodeListQualifier', v_compound_record.quantity_qualifier_id, 
            'QuantityUnitOfMeasure', CASE WHEN v_compound_record.quantity_uom_id IS NOT NULL THEN jsonb_build_object('Code', v_compound_record.quantity_uom_id) ELSE NULL END 
        )),
        'DeaSchedule', CASE WHEN v_compound_record.dea_schedule_id IS NOT NULL THEN jsonb_build_object('Code', v_compound_record.dea_schedule_id) ELSE NULL END 
    ));

    -- Fetch and build DUEs for this specific compound ingredient
    SELECT jsonb_agg(remove_null_values_from_jsonb(jsonb_build_object()
        || CASE WHEN d.service_reason_id IS NOT NULL THEN jsonb_build_object('ServiceReasonCode', d.service_reason_id) ELSE '{}'::JSONB END
        || CASE WHEN d.pservice_rsn_id IS NOT NULL THEN jsonb_build_object('ProfessionalServiceCode', d.pservice_rsn_id) ELSE '{}'::JSONB END
        || CASE WHEN src.codes IS NOT NULL AND array_length(src.codes,1) > 0 THEN jsonb_build_object('ServiceResultCode', to_jsonb(src.codes)) ELSE '{}'::JSONB END
        || jsonb_build_object('CoAgent', remove_null_values_from_jsonb(jsonb_build_object()
            || jsonb_build_object('CoAgentCode', remove_null_values_from_jsonb(jsonb_build_object()
                || CASE WHEN d.coagent_code IS NOT NULL THEN jsonb_build_object('Code', d.coagent_code) ELSE '{}'::JSONB END
                || CASE WHEN d.coagent_qualifier_id IS NOT NULL THEN jsonb_build_object('Qualifier', d.coagent_qualifier_id) ELSE '{}'::JSONB END
                || CASE WHEN d.coagent_description IS NOT NULL THEN jsonb_build_object('Description', d.coagent_description) ELSE '{}'::JSONB END
            ))
        ))
        || CASE WHEN d.clinical_significance IS NOT NULL THEN jsonb_build_object('ClinicalSignificanceCode', d.clinical_significance) ELSE '{}'::JSONB END
        || CASE WHEN d.ack_reason IS NOT NULL THEN jsonb_build_object('AcknowledgementReason', d.ack_reason) ELSE '{}'::JSONB END
    ))
    INTO v_ingredient_dues_json_array
    FROM form_ss_due d
    JOIN sf_form_ss_message_to_ss_due sf ON d.id = sf.form_ss_due_fk -- Assumes DUEs for compounds are also linked to the main message
    LEFT JOIN LATERAL (
        SELECT array_agg(lsrc.code ORDER BY lsrc.code) as codes
        FROM gr_form_ss_due_service_res_id_to_list_ss_service_result_code_id gr 
        JOIN form_list_ss_service_result_code lsrc ON lsrc.code = gr.form_list_ss_service_result_code_fk
        WHERE gr.form_ss_due_fk = d.id AND lsrc.deleted IS NOT TRUE AND lsrc.archived IS NOT TRUE
    ) src ON TRUE
    WHERE sf.form_ss_message_fk = p_ss_message_id -- Link to the main message
      AND d.compound_no = v_compound_record.compound_no -- Filter for the current compound ingredient
      AND d.deleted IS NOT TRUE AND d.archived IS NOT TRUE 
      AND sf.archive IS NOT TRUE AND sf.delete IS NOT TRUE;

    IF v_ingredient_dues_json_array IS NOT NULL AND jsonb_array_length(v_ingredient_dues_json_array) > 0 THEN
        v_ingredient_jsonb := v_ingredient_jsonb || jsonb_build_object('DrugUseEvaluation', v_ingredient_dues_json_array);
    ELSE
        -- Always include DrugUseEvaluation as an array, even if empty
        v_ingredient_jsonb := v_ingredient_jsonb || jsonb_build_object('DrugUseEvaluation', '[]'::jsonb);
    END IF;

    RETURN v_ingredient_jsonb;
END;
$$;

CREATE OR REPLACE FUNCTION _build_medication_compounds_json(
    p_ss_message_id INTEGER,
    p_compound_dosage_form_actual_code TEXT
) RETURNS JSONB LANGUAGE plpgsql AS $$
DECLARE
    v_compounds_json JSONB;
    v_ss_message_record form_ss_message;
    v_compound_ingredient_ids INTEGER[];
BEGIN
    RAISE LOG 'Starting _build_medication_compounds_json for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_medication_compounds_json', p_ss_message_id;
    END IF;

    SELECT array_agg(fc.id) 
    INTO v_compound_ingredient_ids
    FROM form_ss_compound fc
    JOIN sf_form_ss_message_to_ss_compound sf ON fc.id = sf.form_ss_compound_fk
    WHERE sf.form_ss_message_fk = p_ss_message_id AND fc.deleted IS NOT TRUE AND fc.archived IS NOT TRUE AND sf.archive IS NOT TRUE AND sf.delete IS NOT TRUE;

    IF v_compound_ingredient_ids IS NOT NULL AND array_length(v_compound_ingredient_ids, 1) > 0 THEN
         SELECT jsonb_build_object(
            'FinalCompoundPharmaceuticalDosageForm', p_compound_dosage_form_actual_code, 
            'CompoundIngredientsLotNotUsed', jsonb_agg(build_compound_ingredient_jsonb(compound_id, p_ss_message_id))
        ) INTO v_compounds_json 
        FROM unnest(v_compound_ingredient_ids) as compound_id; 
    ELSE
        v_compounds_json := NULL;
    END IF;
    RAISE LOG 'Finished _build_medication_compounds_json for ss_message_id: %. Payload: %', p_ss_message_id, v_compounds_json;
    RETURN v_compounds_json;
END;
$$;

CREATE OR REPLACE FUNCTION _build_medication_dues_array(
    p_ss_message_id INTEGER
) RETURNS JSONB LANGUAGE plpgsql AS $$
DECLARE
    v_dues_arr JSONB;
BEGIN
    RAISE LOG 'Starting _build_medication_dues_array for ss_message_id: %', p_ss_message_id;

    SELECT jsonb_agg(
        remove_null_values_from_jsonb(jsonb_build_object()
            || CASE WHEN d.service_reason_id IS NOT NULL THEN jsonb_build_object('ServiceReasonCode', d.service_reason_id) ELSE '{}'::JSONB END
            || CASE WHEN d.pservice_rsn_id IS NOT NULL THEN jsonb_build_object('ProfessionalServiceCode', d.pservice_rsn_id) ELSE '{}'::JSONB END
            || CASE WHEN src.codes IS NOT NULL AND array_length(src.codes,1) > 0 THEN jsonb_build_object('ServiceResultCode', to_jsonb(src.codes)) ELSE '{}'::JSONB END
            || jsonb_build_object('CoAgent', remove_null_values_from_jsonb(jsonb_build_object()
                || jsonb_build_object('CoAgentCode', remove_null_values_from_jsonb(jsonb_build_object()
                    || CASE WHEN d.coagent_code IS NOT NULL THEN jsonb_build_object('Code', d.coagent_code) ELSE '{}'::JSONB END
                    || CASE WHEN d.coagent_qualifier_id IS NOT NULL THEN jsonb_build_object('Qualifier', d.coagent_qualifier_id) ELSE '{}'::JSONB END 
                    || CASE WHEN d.coagent_description IS NOT NULL THEN jsonb_build_object('Description', d.coagent_description) ELSE '{}'::JSONB END
                ))
            ))
            || CASE WHEN d.clinical_significance IS NOT NULL THEN jsonb_build_object('ClinicalSignificanceCode', d.clinical_significance) ELSE '{}'::JSONB END 
            || CASE WHEN d.ack_reason IS NOT NULL THEN jsonb_build_object('AcknowledgementReason', d.ack_reason) ELSE '{}'::JSONB END
        ))
    INTO v_dues_arr 
    FROM form_ss_due d
    JOIN sf_form_ss_message_to_ss_due sf ON d.id = sf.form_ss_due_fk
    LEFT JOIN LATERAL (
        SELECT array_agg(lsrc.code ORDER BY lsrc.code) as codes -- Ensure consistent order
        FROM gr_form_ss_due_service_res_id_to_list_ss_service_result_code_id gr 
        JOIN form_list_ss_service_result_code lsrc ON lsrc.code = gr.form_list_ss_service_result_code_fk -- Using user's corrected join condition
        WHERE gr.form_ss_due_fk = d.id AND lsrc.deleted IS NOT TRUE AND lsrc.archived IS NOT TRUE
    ) src ON TRUE
    WHERE sf.form_ss_message_fk = p_ss_message_id 
      AND d.deleted IS NOT TRUE AND d.archived IS NOT TRUE 
      AND sf.archive IS NOT TRUE AND sf.delete IS NOT TRUE  -- Corrected sf conditions
      AND d.compound_no IS NULL;

    RAISE LOG 'Finished _build_medication_dues_array for ss_message_id: %. Payload: %', p_ss_message_id, v_dues_arr;
    RETURN CASE WHEN v_dues_arr IS NOT NULL AND jsonb_array_length(v_dues_arr) > 0 THEN v_dues_arr ELSE NULL END;
END;
$$;

CREATE OR REPLACE FUNCTION _build_medication_cnote_array(
    p_ss_message_id INTEGER
) RETURNS JSONB LANGUAGE plpgsql AS $$ 
DECLARE
    v_cnote_arr JSONB;
    v_ss_message_record form_ss_message;
BEGIN
    RAISE LOG 'Starting _build_medication_cnote_array for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_medication_cnote_array', p_ss_message_id;
    END IF;

    IF v_ss_message_record.id IS NOT NULL THEN 
        SELECT jsonb_agg(remove_null_values_from_jsonb(jsonb_build_object()
            || CASE WHEN cn.qualifier_id IS NOT NULL THEN jsonb_build_object('Qualifier', cn.qualifier_id) ELSE '{}'::JSONB END 
            || CASE WHEN cn.value IS NOT NULL THEN jsonb_build_object('Value', cn.value) ELSE '{}'::JSONB END
        ))
        INTO v_cnote_arr
        FROM form_ss_codified_note cn
        JOIN sf_form_ss_message_to_ss_codified_note sf ON cn.id = sf.form_ss_codified_note_fk
        WHERE sf.form_ss_message_fk = v_ss_message_record.id AND cn.deleted IS NOT TRUE AND cn.archived IS NOT TRUE AND sf.archive IS NOT TRUE AND sf.delete IS NOT TRUE;
    ELSE
        v_cnote_arr := NULL;
    END IF;
    RAISE LOG 'Finished _build_medication_cnote_array for ss_message_id: %. Payload: %', p_ss_message_id, v_cnote_arr;
    RETURN CASE WHEN v_cnote_arr IS NOT NULL AND jsonb_array_length(v_cnote_arr) > 0 THEN v_cnote_arr ELSE NULL END;
END;
$$;

CREATE OR REPLACE FUNCTION _build_medication_diagnoses_json(
    p_ss_message_id INTEGER
) RETURNS JSONB LANGUAGE plpgsql AS $$ 
DECLARE
    v_diagnoses_obj JSONB := '{}'::JSONB;
    item RECORD;
    v_ss_message_record form_ss_message;
BEGIN
    RAISE LOG 'Starting _build_medication_diagnoses_json for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_medication_diagnoses_json', p_ss_message_id;
    END IF;

    IF v_ss_message_record.id IS NOT NULL THEN
        FOR item IN 
            SELECT 
                dx.type, 
                dx.dx_code, 
                dx.dx_code_qualifier_id as dx_qualifier_actual_code, 
                dx.dx_desc 
            FROM form_ss_diagnosis dx
            JOIN sf_form_ss_message_to_ss_diagnosis sf ON dx.id = sf.form_ss_diagnosis_fk
            WHERE sf.form_ss_message_fk = v_ss_message_record.id AND dx.deleted IS NOT TRUE AND dx.archived IS NOT TRUE AND sf.archive IS NOT TRUE AND sf.delete IS NOT TRUE
        LOOP
            v_diagnoses_obj := v_diagnoses_obj || jsonb_build_object(item.type, remove_null_values_from_jsonb(jsonb_build_object(
                'Code', item.dx_code,
                'Qualifier', item.dx_qualifier_actual_code,
                'Description', item.dx_desc
            )));
        END LOOP;
    END IF;
    IF v_ss_message_record.clinical_info_qualifier IS NOT NULL THEN
        v_diagnoses_obj := jsonb_build_object('ClinicalInformationQualifier', v_ss_message_record.clinical_info_qualifier) || v_diagnoses_obj ;
    END IF;
    
    RAISE LOG 'Finished _build_medication_diagnoses_json for ss_message_id: %. Payload: %', p_ss_message_id, v_diagnoses_obj;
    RETURN CASE WHEN v_diagnoses_obj = '{}'::JSONB THEN NULL ELSE v_diagnoses_obj END;
END;
$$;

CREATE OR REPLACE FUNCTION _build_outbound_main_medication_object(
    p_ss_message_id INTEGER,
    p_other_dates_json_array JSONB,
    p_compounds_json JSONB, 
    p_dues_json_array JSONB,
    p_cnote_json_array JSONB,
    p_diagnoses_json JSONB,
    p_product_code_qualifier_actual_code TEXT,
    p_dea_schedule_actual_code TEXT,
    p_strength_form_actual_code TEXT,
    p_strength_uom_actual_code TEXT,
    p_quantity_qualifier_actual_code TEXT,
    p_quantity_uom_actual_code TEXT,
    p_pa_status_actual_code TEXT
) RETURNS JSONB LANGUAGE plpgsql AS $$
DECLARE
    v_med_obj JSONB;
    v_ss_message_record form_ss_message;
    v_drug_cvg_status_actual_codes TEXT[];
BEGIN
    RAISE LOG 'Starting _build_outbound_main_medication_object for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_outbound_main_medication_object', p_ss_message_id;
    END IF;

    SELECT array_agg(gr.form_list_ss_cvg_status_fk) 
    INTO v_drug_cvg_status_actual_codes
    FROM gr_form_ss_message_drug_cvg_status_id_to_list_ss_cvg_status_id gr
    WHERE gr.form_ss_message_fk = p_ss_message_id;

    v_med_obj := remove_null_values_from_jsonb(jsonb_build_object(
        'DrugDescription', v_ss_message_record.description,
        'DrugCoded', remove_null_values_from_jsonb(jsonb_build_object(
            'ProductCode', remove_null_values_from_jsonb(jsonb_build_object(
                'Qualifier', p_product_code_qualifier_actual_code,
                'Code', v_ss_message_record.product_code
            )),
            'DrugDBCode', CASE WHEN v_ss_message_record.drug_db_qualifier_id IS NOT NULL OR v_ss_message_record.drug_db_code IS NOT NULL THEN 
                              remove_null_values_from_jsonb(jsonb_build_object(
                                  'Qualifier', v_ss_message_record.drug_db_qualifier_id, 
                                  'Code', v_ss_message_record.drug_db_code
                              ))
                          ELSE NULL 
                          END,
            'DEASchedule', CASE WHEN p_dea_schedule_actual_code IS NOT NULL THEN jsonb_build_object('Code', p_dea_schedule_actual_code) ELSE NULL END
        )),
        'Strength', CASE WHEN v_ss_message_record.strength IS NOT NULL OR p_strength_form_actual_code IS NOT NULL OR p_strength_uom_actual_code IS NOT NULL THEN
                        remove_null_values_from_jsonb(jsonb_build_object(
                            'StrengthValue', v_ss_message_record.strength,
                            'StrengthForm', CASE WHEN p_strength_form_actual_code IS NOT NULL THEN jsonb_build_object('Code', p_strength_form_actual_code) ELSE NULL END,
                            'StrengthUnitOfMeasure', CASE WHEN p_strength_uom_actual_code IS NOT NULL THEN jsonb_build_object('Code', p_strength_uom_actual_code) ELSE NULL END
                        ))
                    ELSE NULL 
                    END,
        'Quantity', remove_null_values_from_jsonb(jsonb_build_object(
            'Value', v_ss_message_record.quantity,
            'CodeListQualifier', p_quantity_qualifier_actual_code,
            'QuantityUnitOfMeasure', CASE WHEN p_quantity_uom_actual_code IS NOT NULL THEN jsonb_build_object('Code', p_quantity_uom_actual_code) ELSE NULL END
        )),
        'DaysSupply', v_ss_message_record.days_supply,
        'WrittenDate', CASE WHEN v_ss_message_record.written_date IS NOT NULL THEN jsonb_build_object('Date', TO_CHAR(v_ss_message_record.written_date, 'YYYYMMDD')) ELSE NULL END, -- Changed format
        'Substitutions', v_ss_message_record.daw, 
        'ReasonForSubstitutionCodeUsed', v_ss_message_record.daw_code_reason,
        'NumberOfRefills', v_ss_message_record.refills,
        'PriorAuthorization', v_ss_message_record.pa_number,
        'PriorAuthorizationStatus', p_pa_status_actual_code,
        'DrugCoverageStatusCode', CASE WHEN v_drug_cvg_status_actual_codes IS NOT NULL AND array_length(v_drug_cvg_status_actual_codes, 1) > 0 THEN to_jsonb(v_drug_cvg_status_actual_codes) ELSE NULL END,
        'DoNotFill', v_ss_message_record.do_not_fill, 
        'Note', v_ss_message_record.note,
        'Sig', CASE WHEN v_ss_message_record.sig IS NOT NULL THEN jsonb_build_object('SigText', v_ss_message_record.sig) ELSE NULL END,
        'DeliveryRequest', v_ss_message_record.delivery_request, 
        'DeliveryLocation', v_ss_message_record.delivery_location, 
        'FlavoringRequested', CASE v_ss_message_record.flavoring_requested WHEN 'Y' THEN 'Y' ELSE NULL END, 
        'PrescriberCheckedREMS', v_ss_message_record.prescriber_checked_rems, 
        'REMSPatientRiskCategory', v_ss_message_record.rems_risk_category, 
        'REMSAuthorizationNumber', v_ss_message_record.rems_authorization_number,
        'OtherMedicationDate', p_other_dates_json_array,
        'CompoundInformation', p_compounds_json, 
        'DrugUseEvaluation', p_dues_json_array, 
        'PatientCodifiedNote', p_cnote_json_array,
        'Diagnosis', p_diagnoses_json
    ));
    RAISE LOG 'Finished _build_outbound_main_medication_object for ss_message_id: %. Payload: %', p_ss_message_id, v_med_obj;
    RETURN v_med_obj;
END;
$$;

CREATE OR REPLACE FUNCTION _build_outbound_medication_dispensed_object(
    p_ss_message_id INTEGER,
    p_compounds_json JSONB, 
    p_dues_json_array JSONB,
    p_diagnoses_json JSONB,
    p_other_dates_json_array JSONB
) RETURNS JSONB LANGUAGE plpgsql AS $$
DECLARE
    v_med_disp_obj JSONB;
    v_ss_message_record form_ss_message;
    v_disp_prod_code_qual_actual_code TEXT;
    v_disp_dea_sched_actual_code TEXT;
    v_disp_qty_qual_actual_code TEXT;
    v_disp_qty_uom_actual_code TEXT;
    v_disp_pa_status_actual_code TEXT;
    v_disp_drug_cvg_stat_actual_codes TEXT[]; 
BEGIN
    RAISE LOG 'Starting _build_outbound_medication_dispensed_object for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_outbound_medication_dispensed_object', p_ss_message_id;
    END IF;

    v_disp_prod_code_qual_actual_code := v_ss_message_record.disp_product_code_qualifier_id; 
    v_disp_dea_sched_actual_code := v_ss_message_record.disp_dea_schedule_id; 
    v_disp_qty_qual_actual_code := v_ss_message_record.disp_quantity_qualifier_id; 
    v_disp_qty_uom_actual_code := v_ss_message_record.disp_quantity_uom_id; 
    v_disp_pa_status_actual_code := v_ss_message_record.disp_pa_status_id; 
    
    SELECT array_agg(gr.form_list_ss_cvg_status_fk) 
    INTO v_disp_drug_cvg_stat_actual_codes
    FROM gr_form_ss_message_disp_drug_cvg_ids_to_list_ss_cvg_status_id gr
    WHERE gr.form_ss_message_fk = p_ss_message_id;

    v_med_disp_obj := remove_null_values_from_jsonb(jsonb_build_object(
        'DrugDescription', v_ss_message_record.disp_description,
        'DrugCoded', remove_null_values_from_jsonb(jsonb_build_object(
            'ProductCode', remove_null_values_from_jsonb(jsonb_build_object(
                'Qualifier', v_disp_prod_code_qual_actual_code,
                'Code', v_ss_message_record.disp_product_code
            )),
            'DEASchedule', CASE WHEN v_disp_dea_sched_actual_code IS NOT NULL THEN jsonb_build_object('Code', v_disp_dea_sched_actual_code) ELSE NULL END
        )),
        'Quantity', remove_null_values_from_jsonb(jsonb_build_object(
            'Value', v_ss_message_record.disp_quantity,
            'CodeListQualifier', v_disp_qty_qual_actual_code,
            'QuantityUnitOfMeasure', CASE WHEN v_disp_qty_uom_actual_code IS NOT NULL THEN jsonb_build_object('Code', v_disp_qty_uom_actual_code) ELSE NULL END
        )),
        'Sig', CASE WHEN v_ss_message_record.disp_sig IS NOT NULL THEN jsonb_build_object('SigText', v_ss_message_record.disp_sig) ELSE NULL END,
        'Substitutions', v_ss_message_record.disp_daw, 
        'LastFillDate', CASE WHEN v_ss_message_record.disp_last_disp_date IS NOT NULL THEN jsonb_build_object('Date', TO_CHAR(v_ss_message_record.disp_last_disp_date, 'YYYYMMDD')) ELSE NULL END, -- Changed format
        'Note', v_ss_message_record.disp_note,
        'PharmacyRequestedRefills', v_ss_message_record.request_refills,
        'RefillsRemaining', v_ss_message_record.refills_remaining,
        'PriorAuthorizationStatus', v_disp_pa_status_actual_code,
        'PriorAuthorization', v_ss_message_record.disp_pa_number,
        'DrugCoverageStatusCode', CASE WHEN v_disp_drug_cvg_stat_actual_codes IS NOT NULL AND array_length(v_disp_drug_cvg_stat_actual_codes, 1) > 0 THEN to_jsonb(v_disp_drug_cvg_stat_actual_codes) ELSE NULL END,
        'CompoundInformation', p_compounds_json,
        'DrugUseEvaluation', p_dues_json_array,
        'Diagnosis', p_diagnoses_json, 
        'OtherMedicationDate', p_other_dates_json_array
    ));
    RAISE LOG 'Finished _build_outbound_medication_dispensed_object for ss_message_id: %. Payload: %', p_ss_message_id, v_med_disp_obj;
    RETURN v_med_disp_obj;
END;
$$;

CREATE OR REPLACE FUNCTION _build_outbound_medication_section(
    p_ss_message_id INTEGER,
    p_medication_type_path TEXT, 
    p_other_dates_json_array JSONB,
    p_compounds_json JSONB, 
    p_dues_json_array JSONB,
    p_cnote_json_array JSONB,
    p_diagnoses_json JSONB,
    p_product_code_qualifier_actual_code TEXT,
    p_dea_schedule_actual_code TEXT,
    p_strength_form_actual_code TEXT,
    p_strength_uom_actual_code TEXT,
    p_quantity_qualifier_actual_code TEXT,
    p_quantity_uom_actual_code TEXT,
    p_pa_status_actual_code TEXT
) RETURNS JSONB LANGUAGE plpgsql AS $$ 
DECLARE
    v_med_section JSONB := '{}'::JSONB;
    v_main_med_obj JSONB;
    v_disp_med_obj JSONB;
    v_ss_message_record form_ss_message;
BEGIN
    RAISE LOG 'Starting _build_outbound_medication_section for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_outbound_medication_section', p_ss_message_id;
    END IF;

    IF p_medication_type_path IS NOT NULL THEN 
        v_main_med_obj := _build_outbound_main_medication_object(
            p_ss_message_id,
            p_other_dates_json_array,
            p_compounds_json,
            p_dues_json_array,
            p_cnote_json_array,
            p_diagnoses_json,
            p_product_code_qualifier_actual_code,
            p_dea_schedule_actual_code,
            p_strength_form_actual_code,
            p_strength_uom_actual_code,
            p_quantity_qualifier_actual_code,
            p_quantity_uom_actual_code,
            p_pa_status_actual_code
        );
        IF v_main_med_obj IS NOT NULL THEN
            v_med_section := v_med_section || jsonb_build_object(p_medication_type_path, v_main_med_obj);
        END IF;
    END IF;

    IF v_ss_message_record.message_type = 'RxRenewalRequest' THEN
        v_disp_med_obj := _build_outbound_medication_dispensed_object(
            p_ss_message_id,
            p_compounds_json,
            p_dues_json_array,
            p_diagnoses_json,
            p_other_dates_json_array
        );
        IF v_disp_med_obj IS NOT NULL THEN
            v_med_section := v_med_section || jsonb_build_object('MedicationDispensed', v_disp_med_obj);
        END IF;
    END IF;

    RAISE LOG 'Finished _build_outbound_medication_section for ss_message_id: %. Payload: %', p_ss_message_id, v_med_section;
    RETURN CASE WHEN v_med_section = '{}'::JSONB THEN NULL ELSE v_med_section END;
END;
$$;

CREATE OR REPLACE FUNCTION ensure_jsonb_array(p_input JSONB)
RETURNS JSONB LANGUAGE plpgsql IMMUTABLE AS $$
BEGIN
    IF p_input IS NULL OR jsonb_typeof(p_input) = 'null' THEN RETURN '[]'::JSONB; END IF;
    IF jsonb_typeof(p_input) = 'array' THEN RETURN p_input; END IF;
    IF jsonb_typeof(p_input) = 'object' THEN RETURN jsonb_build_array(p_input); END IF;
    RETURN '[]'::JSONB; -- Should not happen for valid Surescripts array paths
END;
$$;

CREATE OR REPLACE FUNCTION _build_rx_change_request_specific_body(
    p_ss_message_id INTEGER,
    p_chg_type_actual_code TEXT,
    p_chg_type_sc_actual_codes TEXT[]
) RETURNS JSONB LANGUAGE plpgsql AS $$ 
DECLARE
    v_chg_req_body JSONB := '{}'::JSONB;
    v_chg_med_json_array JSONB;
    v_ss_message_record form_ss_message;
    v_chg_med_ids INTEGER[];
    v_med_req_dues_array JSONB; -- For DUEs specific to MedicationRequested
BEGIN
    RAISE LOG 'Starting _build_rx_change_request_specific_body for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_rx_change_request_specific_body', p_ss_message_id;
    END IF;

    -- Fetch DUEs linked to the main form_ss_message, to be included in the first MedicationRequested
    v_med_req_dues_array := _build_medication_dues_array(p_ss_message_id);

    SELECT array_agg(fcm.id) 
    INTO v_chg_med_ids
    FROM form_ss_chg_med fcm
    JOIN sf_form_ss_message_to_ss_chg_med sf ON fcm.id = sf.form_ss_chg_med_fk
    WHERE sf.form_ss_message_fk = p_ss_message_id AND fcm.deleted IS NOT TRUE AND fcm.archived IS NOT TRUE AND sf.archive IS NOT TRUE AND sf.delete IS NOT TRUE;

    IF v_chg_med_ids IS NOT NULL AND array_length(v_chg_med_ids, 1) > 0 THEN
        SELECT jsonb_agg(remove_null_values_from_jsonb(jsonb_build_object(
            'DrugDescription', chg_med_item.description,
            'DrugCoded', remove_null_values_from_jsonb(jsonb_build_object(
                'ProductCode', remove_null_values_from_jsonb(jsonb_build_object(
                    'Qualifier', chg_med_item.pc_qualifier_id, 
                    'Code', chg_med_item.product_code
                )),
                'DEASchedule', CASE WHEN chg_med_item.dea_schedule_id IS NOT NULL THEN jsonb_build_object('Code', chg_med_item.dea_schedule_id) ELSE NULL END
            )),
            'Quantity', remove_null_values_from_jsonb(jsonb_build_object(
                'Value', chg_med_item.quantity,
                'CodeListQualifier', chg_med_item.qty_qualifier_id, 
                'QuantityUnitOfMeasure', CASE WHEN chg_med_item.quantity_uom_id IS NOT NULL THEN jsonb_build_object('Code', chg_med_item.quantity_uom_id) ELSE NULL END
            )),
            'DaysSupply', chg_med_item.day_supply,
            'NumberOfRefills', chg_med_item.refills,
            'Sig', CASE WHEN chg_med_item.sig IS NOT NULL THEN jsonb_build_object('SigText', chg_med_item.sig) ELSE NULL END,
            'Substitutions', chg_med_item.daw, 
            'Note', chg_med_item.note,
            -- Add DrugUseEvaluation only to the first MedicationRequested item if DUEs exist
            'DrugUseEvaluation', CASE WHEN chg_med_item.chg_med_item_idx = 1 AND v_med_req_dues_array IS NOT NULL AND jsonb_array_length(v_med_req_dues_array) > 0 THEN v_med_req_dues_array ELSE NULL END
        )))
        INTO v_chg_med_json_array
        FROM (
            SELECT fcm_inner.*, row_number() OVER (ORDER BY fcm_inner.id) as chg_med_item_idx
            FROM form_ss_chg_med fcm_inner
            WHERE fcm_inner.id = ANY(v_chg_med_ids)
        ) chg_med_item;
    END IF;

    v_chg_req_body := remove_null_values_from_jsonb(jsonb_build_object(
        'MessageRequestCode', p_chg_type_actual_code,
        'ChangeReasonText', v_ss_message_record.chg_reason,
        'MessageRequestSubCode', CASE WHEN p_chg_type_sc_actual_codes IS NOT NULL AND array_length(p_chg_type_sc_actual_codes, 1) > 0 THEN to_jsonb(p_chg_type_sc_actual_codes) ELSE NULL END,
        'MedicationRequested', v_chg_med_json_array
    ));
    RAISE LOG 'Finished _build_rx_change_request_specific_body for ss_message_id: %. Payload: %', p_ss_message_id, v_chg_req_body;
    RETURN v_chg_req_body;
END;
$$;

CREATE OR REPLACE FUNCTION _build_cancel_rx_response_specific_body(
    p_ss_message_id INTEGER
) RETURNS JSONB LANGUAGE plpgsql AS $$ 
DECLARE
    v_cancel_resp_body JSONB := '{}'::JSONB;
    v_ss_message_record form_ss_message;
    v_actual_denial_reason_code TEXT; -- To store the resolved code if cancel_denial_reason_code is an FK
BEGIN
    RAISE LOG 'Starting _build_cancel_rx_response_specific_body for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_cancel_rx_response_specific_body', p_ss_message_id;
    END IF;

    IF v_ss_message_record.cancel_status = 'Approved' THEN
        v_cancel_resp_body := jsonb_build_object('Approved', 
            CASE 
                WHEN v_ss_message_record.cancel_note IS NOT NULL THEN remove_null_values_from_jsonb(jsonb_build_object('Note', v_ss_message_record.cancel_note)) 
                ELSE '{}'::JSONB -- Ensure an empty object if note is null, so 'Approved' key is present
            END
        );
    ELSEIF v_ss_message_record.cancel_status = 'Denied' THEN
        DECLARE
            v_denied_details JSONB := '{}'::JSONB;
        BEGIN
            v_actual_denial_reason_code := v_ss_message_record.cancel_denied_reason_code; 

            IF v_actual_denial_reason_code IS NOT NULL THEN 
               v_denied_details := v_denied_details || jsonb_build_object('ReasonCode', v_actual_denial_reason_code); -- Surescripts expects a single code here, not an array for <ReasonCode>
            END IF;
            IF v_ss_message_record.cancel_denied_reason IS NOT NULL THEN 
                v_denied_details := v_denied_details || jsonb_build_object('DenialReason', v_ss_message_record.cancel_denied_reason);
            END IF;
            v_cancel_resp_body := jsonb_build_object('Denied', remove_null_values_from_jsonb(v_denied_details));
        END;
    END IF;
    RAISE LOG 'Finished _build_cancel_rx_response_specific_body for ss_message_id: %. Payload: %', p_ss_message_id, v_cancel_resp_body;
    RETURN jsonb_build_object('Response', v_cancel_resp_body);
END;
$$;

CREATE OR REPLACE FUNCTION _build_outbound_message_body_content(
    p_ss_message_id INTEGER,
    p_patient JSONB,
    p_pharmacy JSONB,
    p_prescriber JSONB,
    p_supervisor JSONB,
    p_fu_prescriber JSONB,
    p_allergies_section JSONB, 
    p_benefits_array JSONB,    
    p_medication_section JSONB,
    p_observations_payload JSONB -- Changed parameter name for clarity
) RETURNS JSONB LANGUAGE plpgsql AS $$ 
DECLARE
    v_body_content JSONB := '{}'::JSONB;
    v_ss_message_record form_ss_message; -- To fetch the record
BEGIN
    RAISE LOG 'Starting _build_outbound_message_body_content for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with id % not found in _build_outbound_message_body_content', p_ss_message_id;
    END IF;

    IF p_patient IS NOT NULL AND NOT (p_patient ? 'error') THEN v_body_content := v_body_content || jsonb_build_object('Patient', p_patient); END IF;
    IF p_pharmacy IS NOT NULL AND NOT (p_pharmacy ? 'error') THEN v_body_content := v_body_content || jsonb_build_object('Pharmacy', p_pharmacy); END IF;
    IF p_prescriber IS NOT NULL AND NOT (p_prescriber ? 'error') THEN v_body_content := v_body_content || jsonb_build_object('Prescriber', p_prescriber); END IF;
    IF p_supervisor IS NOT NULL AND NOT (p_supervisor ? 'error') THEN v_body_content := v_body_content || jsonb_build_object('Supervisor', p_supervisor); END IF;
    IF p_fu_prescriber IS NOT NULL AND NOT (p_fu_prescriber ? 'error') THEN v_body_content := v_body_content || jsonb_build_object('FollowUpPrescriber', p_fu_prescriber); END IF;
    
    IF p_allergies_section IS NOT NULL AND NOT (p_allergies_section ? 'error') THEN 
        v_body_content := v_body_content || p_allergies_section; 
    END IF;
    
    IF p_observations_payload IS NOT NULL AND NOT (p_observations_payload ? 'error') THEN -- Use the new parameter name
        v_body_content := v_body_content || p_observations_payload; 
    END IF;

    IF p_benefits_array IS NOT NULL AND NOT (p_benefits_array ? 'error') AND jsonb_array_length(COALESCE(p_benefits_array, '[]'::jsonb)) > 0 THEN 
        v_body_content := v_body_content || jsonb_build_object('BenefitsCoordination', p_benefits_array); 
    ELSIF v_ss_message_record.prohibit_renewal_request IS NOT NULL AND (p_benefits_array IS NULL OR jsonb_array_length(COALESCE(p_benefits_array, '[]'::jsonb)) = 0) THEN
         DECLARE v_prohibit_val BOOLEAN; 
         BEGIN
             SELECT CASE v_ss_message_record.prohibit_renewal_request
                        WHEN 'true' THEN TRUE
                        WHEN 'false' THEN FALSE
                        ELSE NULL 
                    END INTO v_prohibit_val;
             IF v_prohibit_val IS NOT NULL THEN
                  v_body_content := v_body_content || jsonb_build_object('BenefitsCoordination', jsonb_build_object('ProhibitRenewalRequest', v_prohibit_val));
             END IF;
         END;
    END IF;

    IF p_medication_section IS NOT NULL AND NOT (p_medication_section ? 'error') AND p_medication_section <> '{}'::JSONB THEN
        v_body_content := v_body_content || p_medication_section;
    END IF;

    RAISE LOG 'Finished _build_outbound_message_body_content for ss_message_id: %. Payload: %', p_ss_message_id, v_body_content;
    RETURN v_body_content;
END;
$$;

CREATE OR REPLACE FUNCTION _build_outbound_observations_array(
    p_ss_message_id INTEGER
) RETURNS JSONB LANGUAGE plpgsql AS $$
DECLARE
    v_observations_payload JSONB;
    v_measurements_array JSONB;
BEGIN
    RAISE LOG 'Starting _build_outbound_observations_array for ss_message_id: %', p_ss_message_id;

    SELECT jsonb_agg(remove_null_values_from_jsonb(jsonb_build_object(
        'VitalSign', obs.type_id,
        'LOINCVersion', obs.loinc_version,
        'Value', obs.value,
        'UnitOfMeasure', obs.unit_id,
        'UCUMVersion', obs.ucum_version,
        'ObservationDate', CASE WHEN obs.observation_date IS NOT NULL THEN jsonb_build_object('Date', TO_CHAR(obs.observation_date, 'YYYYMMDD')) ELSE NULL END, -- Changed format
        'ObservationNotes', obs.notes
    )))
    INTO v_measurements_array
    FROM form_ss_observation obs
    JOIN sf_form_ss_message_to_ss_observation sf ON obs.id = sf.form_ss_observation_fk
    WHERE sf.form_ss_message_fk = p_ss_message_id AND obs.deleted IS NOT TRUE AND obs.archived IS NOT TRUE AND sf.archive IS NOT TRUE AND sf.delete IS NOT TRUE;

    IF v_measurements_array IS NOT NULL AND jsonb_array_length(v_measurements_array) > 0 THEN
        v_observations_payload := jsonb_build_object('Observation', jsonb_build_object('Measurement', v_measurements_array));
    ELSE
        v_observations_payload := NULL;
    END IF;

    RAISE LOG 'Finished _build_outbound_observations_array for ss_message_id: %. Payload: %', p_ss_message_id, v_observations_payload;
    RETURN v_observations_payload;
END;
$$;

