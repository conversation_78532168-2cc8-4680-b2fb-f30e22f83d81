CREATE OR REPLACE FUNCTION ncpdp_to_mm_cob(
  p_parent_claim_no text
) RETURNS mm_cob_info[] AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_cob_info[];
  v_error_message text;
  v_params jsonb;
  v_patient_id integer;
  v_cob_info mm_cob_info;
  v_ncpdp_claim record;
  v_ncpdp_insurance record;
  v_ncpdp_patient record;
  v_payer_info record;
  v_site_info record;
  v_physician_info record;
  v_claim_response record;
  v_reject_codes text[];
  v_adjustment_mapping record;
  v_claim_level_adjustments mm_claim_level_adjustment_info[];
  v_adjustment_info mm_claim_level_adjustment_info;
  v_adjustment_groups mm_adjustment_detail_info[];
  v_adjustment_group mm_adjustment_detail_info;
  v_adjustment_details mm_adjustment_detail_info[];
  v_adjustment_detail mm_adjustment_detail_info;
  -- Variables for building complex nested structures
  v_other_sub_info mm_other_sub_info;
  v_other_payer_info mm_other_payer_info;
  v_address_info mm_address_info;
  v_id_qualifier mm_id_qualifier;
  v_other_billing_provider mm_other_billing_provider_info;
  v_other_referring_provider mm_other_referring_provider_info;
  v_other_rendering_provider mm_other_rendering_provider_info;
  v_cob_providers mm_cob_providers_loop;
  v_ncpdp_providers mm_cob_providers_loop[];
BEGIN

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'parent_claim_no', p_parent_claim_no
  );

  BEGIN

    -- Log function call
    PERFORM log_billing_function(
      'ncpdp_to_mm_cob'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    IF p_parent_claim_no IS NULL THEN
        RAISE EXCEPTION 'Parent claim no is required';
    END IF;

    RAISE LOG 'Building MM NCPDP to COB loop for parent claim no: %', p_parent_claim_no;

    -- Verify the parent claim exists in NCPDP
    SELECT 
        nc.patient_id,
        nc.insurance_id,
        nc.payer_id,
        nc.site_id,
        nc.billed
    INTO v_ncpdp_claim
    FROM form_ncpdp nc
    WHERE nc.claim_no = p_parent_claim_no
      AND nc.deleted IS NOT TRUE 
      AND nc.archived IS NOT TRUE;

    IF v_ncpdp_claim.patient_id IS NULL THEN
        RAISE EXCEPTION 'Parent NCPDP claim not found: %', p_parent_claim_no;
    END IF;

    -- Get NCPDP insurance information using subform
    SELECT 
        ni.group_id,
        ni.card_holder_id,
        ni.card_holder_first_name,
        ni.card_holder_last_name,
        ni.pt_rel_code
    INTO v_ncpdp_insurance
    FROM form_ncpdp nc
    INNER JOIN sf_form_ncpdp_to_ncpdp_insurance sf_ins ON sf_ins.form_ncpdp_fk = nc.id
    INNER JOIN form_ncpdp_insurance ni ON ni.id = sf_ins.form_ncpdp_insurance_fk
    WHERE nc.claim_no = p_parent_claim_no
      AND nc.deleted IS NOT TRUE 
      AND nc.archived IS NOT TRUE
      AND sf_ins.delete IS NOT TRUE 
      AND sf_ins.archive IS NOT TRUE
      AND ni.deleted IS NOT TRUE 
      AND ni.archived IS NOT TRUE
    LIMIT 1;

    -- Get NCPDP patient information using subform
    SELECT 
        np.patient_first_name,
        np.patient_last_name,
        np.patient_street_address,
        np.patient_city_address,
        np.patient_state,
        np.patient_zip
    INTO v_ncpdp_patient
    FROM form_ncpdp nc
    INNER JOIN sf_form_ncpdp_to_ncpdp_patient sf_pat ON sf_pat.form_ncpdp_fk = nc.id
    INNER JOIN form_ncpdp_patient np ON np.id = sf_pat.form_ncpdp_patient_fk
    WHERE nc.claim_no = p_parent_claim_no
      AND nc.deleted IS NOT TRUE 
      AND nc.archived IS NOT TRUE
      AND sf_pat.delete IS NOT TRUE 
      AND sf_pat.archive IS NOT TRUE
      AND np.deleted IS NOT TRUE 
      AND np.archived IS NOT TRUE
    LIMIT 1;

    -- Get payer information
    SELECT 
        py.organization::text,
        py.type_id::text,
        py.bin::text,
        py.address1::text,
        py.address2::text,
        py.city::text,
        py.state_id::text,
        py.zip::text,
        py.mm_sec_claims_pr_code::text,
        py.mm_sec_claim_filing_indicator_code::text,
        py.type_id::text,
        py.mm_sec_send_identifier::text,
        py.mm_sec_id_qualifier::text,
        py.mm_sec_id::text
    INTO v_payer_info
    FROM form_payer py
    WHERE py.id = v_ncpdp_claim.payer_id
      AND py.deleted IS NOT TRUE 
      AND py.archived IS NOT TRUE;

    -- Get site information
    SELECT 
        s.npi::text
    INTO v_site_info
    FROM form_site s
    WHERE s.id = v_ncpdp_claim.site_id
      AND s.deleted IS NOT TRUE 
      AND s.archived IS NOT TRUE;

    -- Get physician information from prescriber
    SELECT 
        ph.npi::text
    INTO v_physician_info
    FROM form_ncpdp nc
    INNER JOIN sf_form_ncpdp_to_ncpdp_prescriber sf_presc ON sf_presc.form_ncpdp_fk = nc.id
    INNER JOIN form_ncpdp_prescriber npr ON npr.id = sf_presc.form_ncpdp_prescriber_fk
    INNER JOIN form_physician ph ON ph.id = npr.physician_id
    WHERE nc.claim_no = p_parent_claim_no
      AND nc.deleted IS NOT TRUE 
      AND nc.archived IS NOT TRUE
      AND sf_presc.delete IS NOT TRUE 
      AND sf_presc.archive IS NOT TRUE
      AND npr.deleted IS NOT TRUE 
      AND npr.archived IS NOT TRUE
      AND ph.deleted IS NOT TRUE 
      AND ph.archived IS NOT TRUE
    LIMIT 1;

    -- Get claim response information
    SELECT 
        vcr.adjudication_date::text,
        vcr.total_paid::numeric,
        vcr.pt_pay_amt::numeric
    INTO v_claim_response
    FROM vw_ncpdp_claim_response vcr
    WHERE vcr.claim_no = p_parent_claim_no;

    -- Get rejection codes from parent claim
    SELECT get_claim_reject_codes(p_parent_claim_no) INTO v_reject_codes;

    -- Build claim level adjustments if we have rejection codes
    v_claim_level_adjustments := NULL::mm_claim_level_adjustment_info[];
    
    IF v_reject_codes IS NOT NULL AND array_length(v_reject_codes, 1) > 0 THEN
        -- Get the first mapping with lowest priority rank
        SELECT 
            nm.mm_adj_reason_code::text as mm_adj_reason_code,
            nm.mm_adj_group_code::text as mm_adj_group_code
        INTO v_adjustment_mapping
        FROM form_list_ncpdp_to_med_claim_adj_code nm
        WHERE nm.ncpdp_reject_code = ANY(v_reject_codes)
          AND nm.deleted IS NOT TRUE 
          AND nm.archived IS NOT TRUE
        ORDER BY nm.priority_rank::integer ASC
        LIMIT 1;

        IF v_adjustment_mapping.mm_adj_reason_code IS NOT NULL THEN
            -- Build adjustment detail
            v_adjustment_detail.adjustment_reason_code := v_adjustment_mapping.mm_adj_reason_code;
            v_adjustment_detail.adjustment_amount := v_ncpdp_claim.billed;
            v_adjustment_detail.adjustment_quantity := NULL::numeric;

            v_adjustment_details := ARRAY[v_adjustment_detail];

            -- Build adjustment info
            v_adjustment_info.adjustment_group_code := v_adjustment_mapping.mm_adj_group_code;
            v_adjustment_info.adjustment_details := v_adjustment_details;

            v_claim_level_adjustments := ARRAY[v_adjustment_info];
        END IF;
    END IF;

    -- Build the COB info
    v_cob_info.patient_id := v_ncpdp_claim.patient_id::integer;
    v_cob_info.cob_insurance_id := v_ncpdp_claim.insurance_id::integer;
    v_cob_info.cob_payer_id := v_ncpdp_claim.payer_id::integer;
    v_cob_info.cob_payer_type_id := v_payer_info.type_id::text;
    v_cob_info.mcr_reimbursement_rate := NULL::numeric;
    v_cob_info.mcr_hcpcs_payable_amount := NULL::numeric;
    v_cob_info.mcr_rcodes := NULL::text[];

    -- payment_responsibility_level_code
    v_cob_info.payment_responsibility_level_code := COALESCE(
        v_payer_info.mm_sec_claims_pr_code,
        CASE 
            WHEN EXISTS (
                SELECT 1 FROM form_patient_insurance pi2 
                WHERE pi2.patient_id = v_ncpdp_claim.patient_id 
                  AND pi2.payer_level = 'P'
                  AND pi2.deleted IS NOT TRUE 
                  AND pi2.archived IS NOT TRUE
            ) THEN 'S'  -- Secondary if primary exists
            ELSE 'P'    -- Primary otherwise
        END
    )::text;
    -- individual_relationship_code - map NCPDP pt_rel_code to medical
    v_cob_info.individual_relationship_code := CASE COALESCE(v_ncpdp_insurance.pt_rel_code, '0')
        WHEN '0' THEN '21'  -- Not Specified → Unknown
        WHEN '1' THEN '18'  -- Cardholder → Self
        WHEN '2' THEN '01'  -- Spouse → Spouse
        WHEN '3' THEN '19'  -- Child → Child
        WHEN '4' THEN 'G8'  -- Other → Other Relationship
        ELSE '21'           -- Default to Unknown
    END::text;
    v_cob_info.insurance_type_code := NULL::text;
    -- claim_filing_indicator_code
    v_cob_info.claim_filing_indicator_code := COALESCE(v_payer_info.mm_sec_claim_filing_indicator_code, 'CI')::text;
    v_cob_info.benefits_assignment_certification_indicator := 'Yes'::text;
    v_cob_info.patient_signature_generate_for_patient := 'Yes'::text;
    -- insurance_group_or_policy_number
    v_cob_info.insurance_group_or_policy_number := COALESCE(v_ncpdp_insurance.group_id, v_ncpdp_insurance.card_holder_id)::text;
    -- other_insured_group_name
    v_cob_info.other_insured_group_name := CASE 
        WHEN v_ncpdp_insurance.group_id IS NOT NULL THEN v_ncpdp_insurance.card_holder_id
        ELSE NULL
    END::text;
    v_cob_info.release_of_information_code := 'Y'::text;
    
    -- Build other_insured_address
    v_address_info.address1 := v_ncpdp_patient.patient_street_address::text;
    v_address_info.address2 := NULL::text;
    v_address_info.city := v_ncpdp_patient.patient_city_address::text;
    v_address_info.state := v_ncpdp_patient.patient_state::text;
    v_address_info.postal_code := v_ncpdp_patient.patient_zip::text;
    
    -- Build other_subscriber_name
    v_other_sub_info.other_insured_qualifier := '1'::text;
    v_other_sub_info.other_insured_identifier_type_code := 'MI'::text;
    v_other_sub_info.other_insured_identifier := v_ncpdp_insurance.card_holder_id::text;
    v_other_sub_info.other_insured_first_name := COALESCE(v_ncpdp_insurance.card_holder_first_name, v_ncpdp_patient.patient_first_name)::text;
    v_other_sub_info.other_insured_last_name := COALESCE(v_ncpdp_insurance.card_holder_last_name, v_ncpdp_patient.patient_last_name)::text;
    v_other_sub_info.other_insured_middle_name := NULL::text;
    v_other_sub_info.other_insured_name_suffix := NULL::text;
    v_other_sub_info.other_insured_address := ARRAY[v_address_info];
    
    v_cob_info.other_subscriber_name := ARRAY[v_other_sub_info];
    
    -- Build other_payer_address
    v_address_info.address1 := v_payer_info.address1::text;
    v_address_info.address2 := v_payer_info.address2::text;
    v_address_info.city := v_payer_info.city::text;
    v_address_info.state := v_payer_info.state_id::text;
    v_address_info.postal_code := v_payer_info.zip::text;
    
    -- Build other_payer_name
    v_other_payer_info.patient_id := v_ncpdp_claim.patient_id::integer;
    v_other_payer_info.cob_insurance_id := v_ncpdp_claim.insurance_id::integer;
    v_other_payer_info.cob_payer_id := v_ncpdp_claim.payer_id::integer;
    v_other_payer_info.other_payer_organization_name := v_payer_info.organization::text;
    -- other_payer_identifier_type_code
    v_other_payer_info.other_payer_identifier_type_code := CASE 
        WHEN v_payer_info.type_id IN ('MCRB', 'MCRD') THEN 'XV'
        ELSE 'PI'
    END::text;
    v_other_payer_info.other_payer_identifier := v_payer_info.bin::text;
                v_other_payer_info.other_payer_adjudication_or_payment_date := v_claim_response.adjudication_date::text;
            v_other_payer_info.other_payer_claim_adjustment_indicator := CASE WHEN v_claim_level_adjustments IS NOT NULL AND array_length(v_claim_level_adjustments, 1) > 0 THEN 'Y' ELSE NULL END::text;
    -- Get PA info from NCPDP claim subform
    v_other_payer_info.pa_id := (
        SELECT ncl.pa_id
        FROM form_ncpdp nc2
        INNER JOIN sf_form_ncpdp_to_ncpdp_claim sf_claim ON sf_claim.form_ncpdp_fk = nc2.id
        INNER JOIN form_ncpdp_claim ncl ON ncl.id = sf_claim.form_ncpdp_claim_fk
        WHERE nc2.claim_no = p_parent_claim_no
          AND nc2.deleted IS NOT TRUE 
          AND nc2.archived IS NOT TRUE
          AND sf_claim.delete IS NOT TRUE 
          AND sf_claim.archive IS NOT TRUE
          AND ncl.deleted IS NOT TRUE 
          AND ncl.archived IS NOT TRUE
        LIMIT 1
    )::integer;
    -- other_payer_prior_authorization_number
    v_other_payer_info.other_payer_prior_authorization_number := (
        SELECT ncl.pa_no_submitted
        FROM form_ncpdp nc2
        INNER JOIN sf_form_ncpdp_to_ncpdp_claim sf_claim ON sf_claim.form_ncpdp_fk = nc2.id
        INNER JOIN form_ncpdp_claim ncl ON ncl.id = sf_claim.form_ncpdp_claim_fk
        WHERE nc2.claim_no = p_parent_claim_no
          AND nc2.deleted IS NOT TRUE 
          AND nc2.archived IS NOT TRUE
          AND sf_claim.delete IS NOT TRUE 
          AND sf_claim.archive IS NOT TRUE
          AND ncl.deleted IS NOT TRUE 
          AND ncl.archived IS NOT TRUE
        LIMIT 1
    )::text;
    v_other_payer_info.other_payer_claim_control_number := NULL::text;
    v_other_payer_info.other_payer_secondary_identifier := CASE 
    WHEN v_payer_info.mm_sec_send_identifier IS NOT NULL AND v_payer_info.mm_sec_id IS NOT NULL AND v_payer_info.mm_sec_id_qualifier IS NOT NULL THEN ARRAY[
        (
            v_payer_info.mm_sec_id_qualifier::text,
            v_payer_info.mm_sec_id::text
        )::mm_other_payer_secondary_identifier
    ] ELSE NULL::mm_other_payer_secondary_identifier[] END;
    v_other_payer_info.other_payer_address := ARRAY[v_address_info];
    
    v_cob_info.other_payer_name := ARRAY[v_other_payer_info];
    
    v_cob_info.payer_paid_amount := COALESCE(v_claim_response.total_paid, 0::numeric)::numeric;
    -- non_covered_charge_amount
    v_cob_info.non_covered_charge_amount := (v_ncpdp_claim.billed::numeric - COALESCE(v_claim_response.total_paid, 0::numeric))::numeric;
    -- remaining_patient_liability
    v_cob_info.remaining_patient_liability := CASE 
        WHEN COALESCE(v_claim_response.total_paid, 0::numeric) > 0 
        THEN COALESCE(v_claim_response.pt_pay_amt, 0::numeric)
        ELSE v_ncpdp_claim.billed::numeric
    END::numeric;
    v_cob_info.claim_level_adjustments := v_claim_level_adjustments;
    -- tabif_claim_level_adjustments
    v_cob_info.tabif_claim_level_adjustments := CASE 
        WHEN v_claim_level_adjustments IS NOT NULL AND array_length(v_claim_level_adjustments, 1) > 0 
        THEN 'Yes'::text 
        ELSE NULL::text
    END::text;
    
    -- Build providers structure using helper function
    -- For NCPDP claims, build providers manually since we don't have a medical claim structure
    -- Build billing provider
    v_id_qualifier.qualifier := 'NF'::text;
    v_id_qualifier.identifier := v_site_info.npi::text;
    v_other_billing_provider.entity_type_qualifier := '2'::text;
    v_other_billing_provider.other_payer_billing_provider_identifier := ARRAY[v_id_qualifier];
    
    -- Build referring provider
    v_id_qualifier.qualifier := 'NF'::text;
    v_id_qualifier.identifier := v_physician_info.npi::text;
    v_other_referring_provider.other_payer_referring_provider_identifier := ARRAY[v_id_qualifier];
    
    -- Build rendering provider
    v_id_qualifier.qualifier := 'NF'::text;
    v_id_qualifier.identifier := v_site_info.npi::text;
    v_other_rendering_provider.entity_type_qualifier := '2'::text;
    v_other_rendering_provider.other_payer_rendering_provider_identifier := ARRAY[v_id_qualifier];
    
    -- Build providers loop
    v_cob_providers.other_payer_billing_provider := ARRAY[v_other_billing_provider];
    v_cob_providers.tabif_other_payer_billing_provider := 'Yes'::text;
    v_cob_providers.other_payer_referring_provider := ARRAY[v_other_referring_provider];
    v_cob_providers.tabif_other_payer_referring_provider := 'Yes'::text;
    v_cob_providers.other_payer_rendering_provider := ARRAY[v_other_rendering_provider];
    v_cob_providers.tabif_other_payer_rendering_provider := 'Yes'::text;
    v_cob_providers.other_payer_supervising_provider := NULL::mm_other_supervising_provider_info[];
    v_cob_providers.tabif_other_payer_supervising_provider := NULL::text;
    
    v_ncpdp_providers := ARRAY[v_cob_providers];
    
    v_cob_info.providers := v_ncpdp_providers;
    v_cob_info.other_payer_service_facility_location := NULL::mm_other_service_facility_info[];
    v_cob_info.tabif_other_payer_service_facility_location := NULL::text;

    v_result := ARRAY[v_cob_info];

    RAISE LOG 'MM NCPDP to COB loop build result: % COB found', 
              COALESCE(array_length(v_result, 1), 0);

    -- Log success
    PERFORM log_billing_function(
      'ncpdp_to_mm_cob'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in ncpdp_to_mm_cob',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'ncpdp_to_mm_cob',
        'parent_claim_no', p_parent_claim_no
      )
    );

    PERFORM log_billing_function(
      'ncpdp_to_mm_cob'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;


-- COB Providers Helper Function
CREATE OR REPLACE FUNCTION build_mm_cob_providers(
  p_claim_no text
) RETURNS mm_cob_providers_loop[] AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_cob_providers_loop[];
  v_error_message text;
  v_providers_info mm_cob_providers_loop;
  v_billing_provider mm_other_billing_provider_info[];
  v_referring_provider mm_other_referring_provider_info[];
  v_rendering_provider mm_other_rendering_provider_info[];
  v_supervising_provider mm_other_supervising_provider_info[];
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  RAISE LOG 'build_mm_cob_providers: Starting with claim_no=%', p_claim_no;

  -- Initialize result
  v_result := ARRAY[]::mm_cob_providers_loop[];

  -- Build billing provider information
  WITH billing_provider_data AS (
    SELECT DISTINCT
      s.npi::text as billing_npi,
      s.organization_name::text as billing_org_name
    FROM form_med_claim mc
    INNER JOIN form_site s ON s.id = mc.site_id
      AND s.deleted IS NOT TRUE 
      AND s.archived IS NOT TRUE
    WHERE mc.claim_no = p_claim_no
      AND mc.deleted IS NOT TRUE 
      AND mc.archived IS NOT TRUE
      AND s.npi IS NOT NULL
  )
  SELECT array_agg(
    (
      '2'::text,  -- entity_type_qualifier (Non-person entity)
      ARRAY[(
        'NF'::text,  -- qualifier (NPI)
        bp.billing_npi::text  -- identifier
      )::mm_id_qualifier]
    )::mm_other_billing_provider_info
  )
  INTO v_billing_provider
  FROM billing_provider_data bp;

  -- Build referring provider information
  WITH referring_provider_data AS (
    SELECT DISTINCT
      ph.npi::text as referring_npi,
      ph.first_name::text as referring_first_name,
      ph.last_name::text as referring_last_name
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_info sf_info ON sf_info.form_med_claim_fk = mc.id
      AND sf_info.delete IS NOT TRUE 
      AND sf_info.archive IS NOT TRUE
    INNER JOIN form_med_claim_info ci ON ci.id = sf_info.form_med_claim_info_fk
      AND ci.deleted IS NOT TRUE 
      AND ci.archived IS NOT TRUE
    INNER JOIN form_physician ph ON ph.id = ci.referring_physician_id
      AND ph.deleted IS NOT TRUE 
      AND ph.archived IS NOT TRUE
    WHERE mc.claim_no = p_claim_no
      AND mc.deleted IS NOT TRUE 
      AND mc.archived IS NOT TRUE
      AND ph.npi IS NOT NULL
  )
  SELECT array_agg(
    (
      ARRAY[(
        'NF'::text,  -- qualifier (NPI)
        rp.referring_npi::text  -- identifier
      )::mm_id_qualifier]
    )::mm_other_referring_provider_info
  )
  INTO v_referring_provider
  FROM referring_provider_data rp;

  -- Build rendering provider information (same as billing for most cases)
  WITH rendering_provider_data AS (
    SELECT DISTINCT
      s.npi::text as rendering_npi,
      s.organization_name::text as rendering_org_name
    FROM form_med_claim mc
    INNER JOIN form_site s ON s.id = mc.site_id
      AND s.deleted IS NOT TRUE 
      AND s.archived IS NOT TRUE
    WHERE mc.claim_no = p_claim_no
      AND mc.deleted IS NOT TRUE 
      AND mc.archived IS NOT TRUE
      AND s.npi IS NOT NULL
  )
  SELECT array_agg(
    (
      '2'::text,  -- entity_type_qualifier (Non-person entity)
      ARRAY[(
        'NF'::text,  -- qualifier (NPI)
        rp.rendering_npi::text  -- identifier
      )::mm_id_qualifier]
    )::mm_other_rendering_provider_info
  )
  INTO v_rendering_provider
  FROM rendering_provider_data rp;

  -- Supervising provider is typically not used for COB, set to NULL
  v_supervising_provider := NULL::mm_other_supervising_provider_info[];

  -- Build the complete providers structure if we have any providers
  IF v_billing_provider IS NOT NULL OR v_referring_provider IS NOT NULL OR v_rendering_provider IS NOT NULL THEN
    SELECT 
      v_billing_provider,
      CASE WHEN v_billing_provider IS NOT NULL THEN 'Yes' ELSE NULL END::text,
      v_referring_provider,
      CASE WHEN v_referring_provider IS NOT NULL THEN 'Yes' ELSE NULL END::text,
      v_rendering_provider,
      CASE WHEN v_rendering_provider IS NOT NULL THEN 'Yes' ELSE NULL END::text,
      v_supervising_provider,
      CASE WHEN v_supervising_provider IS NOT NULL THEN 'Yes' ELSE NULL END::text
    INTO 
      v_providers_info.other_payer_billing_provider,
      v_providers_info.tabif_other_payer_billing_provider,
      v_providers_info.other_payer_referring_provider,
      v_providers_info.tabif_other_payer_referring_provider,
      v_providers_info.other_payer_rendering_provider,
      v_providers_info.tabif_other_payer_rendering_provider,
      v_providers_info.other_payer_supervising_provider,
      v_providers_info.tabif_other_payer_supervising_provider;

    v_result := ARRAY[v_providers_info];
  END IF;

  RAISE LOG 'build_mm_cob_providers: Built providers for claim_no=%, found % provider groups', 
    p_claim_no, COALESCE(array_length(v_result, 1), 0);

  RETURN v_result;

EXCEPTION WHEN OTHERS THEN
  v_error_message := SQLERRM;
  
  RAISE LOG 'build_mm_cob_providers: ERROR - % for claim_no=%', v_error_message, p_claim_no;

  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    v_error_message,
    'Exception in build_mm_cob_providers',
    'FUNCTION',
    current_schema(),
    'med_claim',
    jsonb_build_object(
      'function_name', 'build_mm_cob_providers',
      'claim_no', p_claim_no,
      'execution_time_ms', EXTRACT(EPOCH FROM (clock_timestamp() - v_start_time)) * 1000
    )
  );

  RAISE;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

-- COB Amount Calculations Helper Function
CREATE OR REPLACE FUNCTION calculate_mm_cob_amounts(
  p_claim_no text
) RETURNS jsonb AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result jsonb;
  v_error_message text;
  v_response_type text;
  v_payer_paid_amount numeric := 0;
  v_non_covered_amount numeric := 0;
  v_mcr_covered_amount numeric := 0;
  v_patient_liability numeric := 0;
  v_payment_date text;
  v_mcr_reimbursement_rate numeric := 0;
  v_mcr_remark_codes text[] := '{}';
BEGIN

  -- Record start time
  v_start_time := clock_timestamp();

  RAISE LOG 'calculate_mm_cob_amounts: Starting with claim_no=%', p_claim_no;

  -- Determine response type and calculate amounts
  SELECT 
    CASE 
      WHEN EXISTS (
        SELECT 1 FROM form_med_claim_resp_835 r835 
        WHERE r835.claim_no = p_claim_no 
        AND r835.deleted IS NOT TRUE
        AND r835.archived IS NOT TRUE
      ) THEN '835'::text
      WHEN EXISTS (
        SELECT 1 FROM form_med_claim_resp_277 r277 
        WHERE r277.claim_no = p_claim_no 
        AND r277.deleted IS NOT TRUE
        AND r277.archived IS NOT TRUE
      ) THEN '277'::text
      ELSE 'NONE'::text
    END
  INTO v_response_type;

  -- Calculate amounts from 835 responses
  IF v_response_type = '835' THEN
    -- Get payment date from 835 responses
    SELECT CASE WHEN batch.check_issue_or_eft_effective_date IS NOT NULL THEN TO_CHAR(batch.check_issue_or_eft_effective_date::date, 'MM/DD/YYYY')::text ELSE NULL::text END,
    COALESCE(r835.mcr_hcpcs_payable_amount::numeric, 0::numeric)::numeric,
    COALESCE(r835.mcr_reimbursement_rate::numeric, 0::numeric)::numeric,
    COALESCE(rmk_cd.rmk_cd::text[], '{}'::text[])
    INTO v_payment_date, v_mcr_covered_amount, v_mcr_reimbursement_rate, v_mcr_remark_codes
    FROM form_med_claim_resp_835 r835
    INNER JOIN form_med_claim_resp_835_batch batch 
      ON batch.response_id = r835.response_id
      AND batch.deleted IS NOT TRUE 
      AND batch.archived IS NOT TRUE
    LEFT JOIN LATERAL (
      SELECT ARRAY_AGG(DISTINCT ecl.code) AS rmk_cd
      FROM gr_form_med_claim_resp_835_rmk_cd_to_list_med_claim_ecl_id gr
      INNER JOIN list_med_claim_ecl ecl
        ON ecl.id = gr.form_list_med_claim_ecl_fk
        AND ecl.deleted IS NOT TRUE 
        AND ecl.archived IS NOT TRUE
      WHERE gr.form_med_claim_resp_835_fk = r835.id
        AND gr.deleted IS NOT TRUE 
        AND gr.archived IS NOT TRUE
    ) AS rmk_cd ON TRUE
    WHERE r835.claim_no = p_claim_no
      AND r835.deleted IS NOT TRUE
      AND r835.archived IS NOT TRUE
      AND batch.check_issue_or_eft_effective_date IS NOT NULL
    ORDER BY batch.check_issue_or_eft_effective_date::timestamp DESC
    LIMIT 1;

    -- Get paid amount from claim payment information
    SELECT COALESCE(SUM(pmt.claim_payment_amount::numeric), 0::numeric)
    INTO v_payer_paid_amount
    FROM form_med_claim_resp_835 r835
    INNER JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_pmt sf_pmt 
      ON sf_pmt.form_med_claim_resp_835_fk = r835.id
      AND sf_pmt.delete IS NOT TRUE 
      AND sf_pmt.archive IS NOT TRUE
    INNER JOIN form_med_claim_resp_835_pmt pmt 
      ON pmt.id = sf_pmt.form_med_claim_resp_835_pmt_fk
      AND pmt.deleted IS NOT TRUE 
      AND pmt.archived IS NOT TRUE
    WHERE r835.claim_no = p_claim_no
      AND r835.deleted IS NOT TRUE
      AND r835.archived IS NOT TRUE;

    -- Calculate patient liability from 835 responses
    SELECT COALESCE(SUM(pmt.patient_responsibility_amount::numeric), 0::numeric)
    INTO v_patient_liability
    FROM form_med_claim_resp_835 r835
    INNER JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_pmt sf_pmt 
      ON sf_pmt.form_med_claim_resp_835_fk = r835.id
      AND sf_pmt.delete IS NOT TRUE 
      AND sf_pmt.archive IS NOT TRUE
    INNER JOIN form_med_claim_resp_835_pmt pmt 
      ON pmt.id = sf_pmt.form_med_claim_resp_835_pmt_fk
      AND pmt.deleted IS NOT TRUE 
      AND pmt.archived IS NOT TRUE
    WHERE r835.claim_no = p_claim_no
      AND r835.deleted IS NOT TRUE
      AND r835.archived IS NOT TRUE;

    -- Calculate non-covered amount from adjustments with reason code '96' ONLY
    WITH claim_adjustments AS (
      SELECT COALESCE(SUM(COALESCE(adj.adjustment_amount::numeric, 0::numeric)), 0::numeric) as claim_adj_96
      FROM form_med_claim_resp_835 r835
      INNER JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_pmt sf_pmt 
        ON sf_pmt.form_med_claim_resp_835_fk = r835.id
        AND sf_pmt.delete IS NOT TRUE 
        AND sf_pmt.archive IS NOT TRUE
      INNER JOIN form_med_claim_resp_835_pmt pmt 
        ON pmt.id = sf_pmt.form_med_claim_resp_835_pmt_fk
        AND pmt.deleted IS NOT TRUE 
        AND pmt.archived IS NOT TRUE
      INNER JOIN sf_form_med_claim_resp_835_pmt_to_med_claim_resp_835_adj sf_adj
        ON sf_adj.form_med_claim_resp_835_pmt_fk = pmt.id
        AND sf_adj.delete IS NOT TRUE 
        AND sf_adj.archive IS NOT TRUE
      INNER JOIN form_med_claim_resp_835_adj adj 
        ON adj.id = sf_adj.form_med_claim_resp_835_adj_fk
        AND adj.deleted IS NOT TRUE
        AND adj.archived IS NOT TRUE
      WHERE r835.claim_no = p_claim_no
        AND r835.deleted IS NOT TRUE
        AND r835.archived IS NOT TRUE
        AND (adj.claim_adjustment_group_code = 'CO' AND adj.adjustment_reason_code = '96')
    ),
    service_line_adjustments AS (
      SELECT COALESCE(SUM(COALESCE(adj.adjustment_amount::numeric, 0::numeric)), 0::numeric) as sl_adj_96
      FROM form_med_claim_resp_835 r835
      INNER JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_sl sf_sl
        ON sf_sl.form_med_claim_resp_835_fk = r835.id
        AND sf_sl.delete IS NOT TRUE 
        AND sf_sl.archive IS NOT TRUE
      INNER JOIN form_med_claim_resp_835_sl sl 
        ON sl.id = sf_sl.form_med_claim_resp_835_sl_fk
        AND sl.deleted IS NOT TRUE 
        AND sl.archived IS NOT TRUE
      INNER JOIN sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj sf_sl_adj
        ON sf_sl_adj.form_med_claim_resp_835_sl_fk = sl.id
        AND sf_sl_adj.delete IS NOT TRUE 
        AND sf_sl_adj.archive IS NOT TRUE
      INNER JOIN form_med_claim_resp_835_sl_adj adj 
        ON adj.id = sf_sl_adj.form_med_claim_resp_835_sl_adj_fk
        AND adj.deleted IS NOT TRUE
        AND adj.archived IS NOT TRUE
      WHERE r835.claim_no = p_claim_no
        AND r835.deleted IS NOT TRUE
        AND r835.archived IS NOT TRUE
        AND (adj.claim_adjustment_group_code = 'CO' AND adj.adjustment_reason_code = '96')
    )
    SELECT 
      CASE 
        WHEN (ca.claim_adj_96::numeric + sla.sl_adj_96::numeric) > 0 THEN (ca.claim_adj_96::numeric + sla.sl_adj_96::numeric)
        ELSE NULL::numeric  -- Set to NULL if no '96' adjustments found
      END
    INTO v_non_covered_amount
    FROM claim_adjustments ca, service_line_adjustments sla;
  END IF;

  -- Build result JSON
  v_result := jsonb_build_object(
    'response_type', v_response_type,
    'payer_paid_amount', v_payer_paid_amount,
    'non_covered_amount', v_non_covered_amount,
    'patient_liability', v_patient_liability,
    'payment_date', v_payment_date,
    'mcr_covered_amount', v_mcr_covered_amount,
    'mcr_reimbursement_rate', v_mcr_reimbursement_rate,
    'mcr_remark_codes', v_mcr_remark_codes
  );

  RAISE LOG 'calculate_mm_cob_amounts: Completed for claim_no=%, amounts=%', p_claim_no, v_result;

  RETURN v_result;

EXCEPTION WHEN OTHERS THEN
  v_error_message := SQLERRM;
  
  RAISE LOG 'calculate_mm_cob_amounts: ERROR - % for claim_no=%', v_error_message, p_claim_no;

  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    v_error_message,
    'Exception in calculate_mm_cob_amounts',
    'FUNCTION',
    current_schema(),
    'med_claim',
    jsonb_build_object(
      'function_name', 'calculate_mm_cob_amounts',
      'claim_no', p_claim_no,
      'execution_time_ms', EXTRACT(EPOCH FROM (clock_timestamp() - v_start_time)) * 1000
    )
  );

  RAISE;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;


-- Payment Responsibility Code Helper Function
CREATE OR REPLACE FUNCTION get_mm_payment_responsibility_code(
  p_patient_id integer,
  p_payer_id integer,
  p_payer_sequence integer,
  p_parent_payer_id integer
) RETURNS text AS $BODY$
DECLARE
  v_med_cond_override text;
  v_payer_override text;
  v_payment_responsibility_code text;
BEGIN
  RAISE LOG 'get_mm_payment_responsibility_code: Starting with patient_id=%, payer_id=%, sequence=%', 
    p_patient_id, p_payer_id, p_payer_sequence;

  -- Check for patient insurance medical condition override
  SELECT pimc.payment_responsibility_level_code
  INTO v_med_cond_override
  FROM form_patient_insurance_med_cond pimc
  WHERE pimc.patient_id = p_patient_id
    AND pimc.active_payer_id = p_payer_id
    AND pimc.previous_payer_id = p_parent_payer_id
    AND pimc.deleted IS NOT TRUE 
    AND pimc.archived IS NOT TRUE
    AND pimc.payment_responsibility_level_code IS NOT NULL
  ORDER BY pimc.created_on DESC
  LIMIT 1;

  -- Check for payer override
  SELECT payer.mm_sec_claims_pr_code
  INTO v_payer_override
  FROM form_payer payer
  WHERE payer.id = p_payer_id
    AND payer.deleted IS NOT TRUE 
    AND payer.archived IS NOT TRUE
    AND payer.mm_sec_claims_pr_code IS NOT NULL;

  -- Apply overrides in priority order, or fall back to sequence-based calculation
  v_payment_responsibility_code := COALESCE(
    v_med_cond_override,  -- Highest priority: patient insurance medical condition
    v_payer_override,     -- Second priority: payer setting
    -- Default: sequence-based calculation
    CASE 
      WHEN p_payer_sequence = 1 THEN 'P'  -- Primary
      WHEN p_payer_sequence = 2 THEN 'S'  -- Secondary  
      WHEN p_payer_sequence = 3 THEN 'T'  -- Tertiary
      WHEN p_payer_sequence = 4 THEN 'A'  -- Payer Responsibility Four
      WHEN p_payer_sequence = 5 THEN 'B'  -- Payer Responsibility Five
      WHEN p_payer_sequence = 6 THEN 'C'  -- Payer Responsibility Six
      WHEN p_payer_sequence = 7 THEN 'D'  -- Payer Responsibility Seven
      WHEN p_payer_sequence = 8 THEN 'E'  -- Payer Responsibility Eight
      WHEN p_payer_sequence = 9 THEN 'F'  -- Payer Responsibility Nine
      WHEN p_payer_sequence = 10 THEN 'G'  -- Payer Responsibility Ten
      WHEN p_payer_sequence = 11 THEN 'H'  -- Payer Responsibility Eleven
      ELSE 'U'  -- Unknown
    END
  );

  RAISE LOG 'get_mm_payment_responsibility_code: Result for patient_id=%, payer_id=%: med_cond=%, payer=%, sequence=%, final=%', 
    p_patient_id, p_payer_id, v_med_cond_override, v_payer_override, p_payer_sequence, v_payment_responsibility_code;

  RETURN v_payment_responsibility_code;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION build_mm_other_subscriber_loop(
  p_insurance_id integer,
  p_parent_claim_no text
) RETURNS mm_cob_info[] AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_cob_info[];
  v_error_message text;
  v_params jsonb;
  v_cob_info mm_cob_info;
  v_claim_hierarchy text[];
  v_current_claim_no text;
  v_claim_chain_rec record;
  v_cob_count integer := 0;
  v_payer_id integer;
BEGIN
  -- Check for null input parameters
  IF p_insurance_id IS NULL THEN
    RAISE EXCEPTION 'Insurance ID cannot be NULL';
  END IF;

  IF p_parent_claim_no IS NULL THEN
    RAISE EXCEPTION 'Parent claim no cannot be NULL';
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  v_params := jsonb_build_object(
    'insurance_id', p_insurance_id,
    'parent_claim_no', p_parent_claim_no
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
      'build_mm_other_subscriber_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM other subscriber loop for insurance ID: %, parent claim no: %', p_insurance_id, p_parent_claim_no;

    -- Initialize result array
    v_result := ARRAY[]::mm_cob_info[];

    -- Build the complete claim hierarchy starting from the top-level claim
    -- First, find the top-level claim by following parent_claim_no backwards
    v_current_claim_no := p_parent_claim_no;
    
    -- Follow the chain backwards to find the root claim
    WHILE EXISTS (
        SELECT 1 FROM form_med_claim mc 
        WHERE mc.claim_no = v_current_claim_no
          AND mc.parent_claim_no IS NOT NULL
          AND mc.deleted IS NOT TRUE 
          AND mc.archived IS NOT TRUE
    ) LOOP
        SELECT mc.parent_claim_no
        INTO v_current_claim_no
        FROM form_med_claim mc
        WHERE mc.claim_no = v_current_claim_no
          AND mc.deleted IS NOT TRUE 
          AND mc.archived IS NOT TRUE;
    END LOOP;

    SELECT pi.payer_id
    INTO v_payer_id
    FROM form_patient_insurance pi
    WHERE pi.id = p_insurance_id
      AND pi.deleted IS NOT TRUE 
      AND pi.archived IS NOT TRUE;

    -- Now build the hierarchy starting from the root claim
    v_claim_hierarchy := ARRAY[v_current_claim_no];
    
    -- Follow the chain forward to build complete hierarchy
    WHILE EXISTS (
        SELECT 1 FROM form_med_claim mc 
        WHERE mc.parent_claim_no = v_current_claim_no
          AND mc.deleted IS NOT TRUE 
          AND mc.archived IS NOT TRUE
    ) LOOP
        SELECT mc.claim_no
        INTO v_current_claim_no
        FROM form_med_claim mc
        WHERE mc.parent_claim_no = v_current_claim_no
          AND mc.deleted IS NOT TRUE 
          AND mc.archived IS NOT TRUE
        LIMIT 1;
        
        v_claim_hierarchy := v_claim_hierarchy || v_current_claim_no;
    END LOOP;

    RAISE LOG 'build_mm_other_subscriber_loop: Built claim hierarchy: %', v_claim_hierarchy;

        -- Process each claim in the hierarchy in chronological order
    -- Handle both NCPDP and medical claims as we encounter them
    FOR v_current_claim_no IN SELECT unnest(v_claim_hierarchy) LOOP
        -- Check if we've reached the maximum number of COB entries (10)
        IF v_cob_count >= 10 THEN
            RAISE LOG 'build_mm_other_subscriber_loop: Reached maximum of 10 COB entries, stopping processing';
            EXIT;
        END IF;

        -- Check if this is an NCPDP claim
        IF EXISTS(
            SELECT 1 FROM form_ncpdp nc
            WHERE nc.claim_no = v_current_claim_no
              AND nc.deleted IS NOT TRUE 
              AND nc.archived IS NOT TRUE
        ) THEN
            -- Process NCPDP claim using specialized function
            DECLARE
                v_ncpdp_cob_result mm_cob_info[];
            BEGIN
                RAISE LOG 'build_mm_other_subscriber_loop: Processing NCPDP claim %', v_current_claim_no;
                
                -- Get COB info from NCPDP claim
                SELECT ncpdp_to_mm_cob(v_current_claim_no) INTO v_ncpdp_cob_result;
                
                -- Add NCPDP COB results to our main result array
                IF v_ncpdp_cob_result IS NOT NULL AND array_length(v_ncpdp_cob_result, 1) > 0 THEN
                    v_result := v_result || v_ncpdp_cob_result;
                    v_cob_count := v_cob_count + array_length(v_ncpdp_cob_result, 1);
                    
                    RAISE LOG 'build_mm_other_subscriber_loop: Added % NCPDP COB entries, total count now %/10', 
                        array_length(v_ncpdp_cob_result, 1), v_cob_count;
                END IF;
            END;
        -- Check if this is a 1500 claim
        ELSIF EXISTS(
            SELECT 1 FROM form_med_claim_1500 mc1500
            WHERE mc1500.claim_no::text = v_current_claim_no
              AND mc1500.deleted IS NOT TRUE 
              AND mc1500.archived IS NOT TRUE
        ) THEN
            -- Process 1500 claim
            FOR v_claim_chain_rec IN
                WITH claim_1500 AS (
                    SELECT 
                        mc1500.claim_no::text as claim_no,
                        mc1500.patient_id::integer,
                        mc1500.insurance_id::integer as insurance_id,
                        mc1500.payer_id::integer,
                        COALESCE(mc1500.billed, 0)::numeric as billed,
                        mc1500.pa_id::integer as pa_id,
                        mc1500.prior_authorization_number::text as pa_no_submitted,
                        'cms1500'::text as billing_method_id,
                        CASE pi.payer_level
                            WHEN 'P' THEN 1
                            WHEN 'S' THEN 2
                            WHEN 'T' THEN 3
                            WHEN 'Q' THEN 4
                            ELSE 1
                        END::integer as payer_sequence,
                        -- Get subscriber information from 1500 claim
                        CASE 
                            WHEN mc1500.subscriber_relationship_id = '18' THEN '18'  -- Self
                            WHEN mc1500.subscriber_relationship_id = '01' THEN '01'  -- Spouse
                            WHEN mc1500.subscriber_relationship_id = '19' THEN '19'  -- Child
                            ELSE 'G8'  -- Other Relationship
                        END::text as medical_relationship_id,
                        mc1500.subscriber_member_id::text as member_id,
                        mc1500.subscriber_insurance_group_or_policy_number::text as policy_number,
                        NULL::text as group_number,
                        NULL::text as insurance_type_code,
                        mc1500.subscriber_first_name::text as first_name,
                        mc1500.subscriber_last_name::text as last_name,
                        mc1500.subscriber_middle_name::text as middle_name,
                        NULL::text as suffix,
                        TO_CHAR(mc1500.subscriber_dob::date, 'MM/DD/YYYY')::text as date_of_birth,
                        mc1500.subscriber_gender::text as gender,
                        -- Get subscriber address
                        mc1500.subscriber_address1::text as sub_address1,
                        mc1500.subscriber_address2::text as sub_address2,
                        mc1500.subscriber_city::text as sub_city,
                        mc1500.subscriber_state_id::text as sub_state,
                        mc1500.subscriber_postal_code::text as sub_postal_code,
                        -- Get subscriber contact
                        mc1500.subscriber_phone_number::text as contact_phone,
                        NULL::text as contact_name,
                        NULL::text as contact_email,
                        -- Get payer information
                        payer.organization::text as payer_organization,
                        payer.address1::text as payer_address1,
                        payer.address2::text as payer_address2,
                        payer.city::text as payer_city,
                        payer.state_id::text as payer_state,
                        payer.zip::text as payer_zip,
                        payer.type_id::text as payer_type_id,
                        payer.mm_sec_send_identifier::text as payer_mm_sec_send_identifier,
                        payer.mm_sec_id_qualifier::text as payer_mm_sec_id_qualifier,
                        payer.mm_sec_id::text as payer_mm_sec_id
                    FROM form_med_claim_1500 mc1500
                    INNER JOIN form_patient_insurance pi ON pi.patient_id = mc1500.patient_id
                        AND pi.payer_id = mc1500.payer_id
                        AND pi.deleted IS NOT TRUE 
                        AND pi.archived IS NOT TRUE
                    INNER JOIN form_payer payer ON payer.id = mc1500.payer_id
                        AND payer.deleted IS NOT TRUE 
                        AND payer.archived IS NOT TRUE
                    WHERE mc1500.claim_no::text = v_current_claim_no
                      AND mc1500.deleted IS NOT TRUE 
                      AND mc1500.archived IS NOT TRUE
                )
                SELECT * FROM claim_1500
            LOOP
                -- Same processing logic as medical claims
                -- Check if we've reached the maximum number of COB entries (10)
                IF v_cob_count >= 10 THEN
                    RAISE LOG 'build_mm_other_subscriber_loop: Reached maximum of 10 COB entries, stopping processing';
                    EXIT;
                END IF;

                RAISE LOG 'build_mm_other_subscriber_loop: Processing 1500 claim % (payer_id=%, sequence=%)', 
                    v_claim_chain_rec.claim_no, v_claim_chain_rec.payer_id, v_claim_chain_rec.payer_sequence;

                -- Build COB info for this claim using same logic as medical claims
                DECLARE
                    v_other_sub_info mm_other_sub_info[];
                    v_other_payer_info mm_other_payer_info[];
                    v_claim_adjustments mm_claim_level_adjustment_info[];
                    v_payment_date text;
                    v_payer_paid_amount numeric := 0;
                    v_non_covered_amount numeric := 0;
                    v_patient_liability numeric := 0;
                    v_payment_responsibility_code text;
                    v_claim_adjustment_indicator text;
                    v_final_insurance_type_code text;
                    v_final_claim_filing_code text;
                    v_med_cond_claim_filing_override text;
                    v_payer_claim_filing_override text;
                    v_cob_providers mm_cob_providers_loop[];
                BEGIN
                    -- Build other subscriber information
                    WITH other_subscriber AS (
                        SELECT 
                            '1'::text as other_insured_qualifier,  -- Person entity
                            'MI'::text as other_insured_identifier_type_code,
                            v_claim_chain_rec.member_id::text as other_insured_identifier,
                            v_claim_chain_rec.first_name::text as other_insured_first_name,
                            v_claim_chain_rec.last_name::text as other_insured_last_name,
                            v_claim_chain_rec.middle_name::text as other_insured_middle_name,
                            v_claim_chain_rec.suffix::text as other_insured_name_suffix
                    )
                    SELECT array_agg(
                        (
                            os.other_insured_qualifier,
                            os.other_insured_identifier_type_code,
                            os.other_insured_identifier,
                            os.other_insured_first_name,
                            os.other_insured_last_name,
                            os.other_insured_middle_name,
                            os.other_insured_name_suffix,
                            CASE WHEN os.other_insured_first_name IS NOT NULL THEN 
                                ARRAY[(
                                    v_claim_chain_rec.sub_address1,
                                    v_claim_chain_rec.sub_address2,
                                    v_claim_chain_rec.sub_city,
                                    v_claim_chain_rec.sub_state,
                                    v_claim_chain_rec.sub_postal_code
                                )::mm_address_info]
                            ELSE NULL::mm_address_info[]
                            END
                        )::mm_other_sub_info
                    )
                    INTO v_other_sub_info
                    FROM other_subscriber os;

                    -- Since 1500 claims don't have payment info, use defaults
                    v_payment_date := NULL;
                    v_payer_paid_amount := 0;
                    v_non_covered_amount := 0;
                    v_patient_liability := 0;
                    v_claim_adjustments := NULL;

                    -- Determine payment responsibility level code using helper function
                    SELECT get_mm_payment_responsibility_code(
                        v_claim_chain_rec.patient_id,
                        v_payer_id,
                        v_claim_chain_rec.payer_sequence,
                        v_claim_chain_rec.payer_id
                    ) INTO v_payment_responsibility_code;

                    -- Build other payer information
                    WITH other_payer AS (
                        SELECT 
                            v_claim_chain_rec.patient_id::integer as patient_id,
                            v_claim_chain_rec.insurance_id::integer as cob_insurance_id,
                            v_claim_chain_rec.payer_id::integer as cob_payer_id,
                            v_claim_chain_rec.payer_organization::text as other_payer_organization_name,
                            CASE 
                                WHEN v_claim_chain_rec.payer_type_id IN ('MCRB', 'MCRD') THEN 'XV'::text
                                ELSE 'PI'::text
                            END as other_payer_identifier_type_code,
                            v_claim_chain_rec.payer_id::text as other_payer_identifier,
                            v_payment_date::text as other_payer_adjudication_or_payment_date,
                            NULL::text as other_payer_claim_adjustment_indicator,
                            v_claim_chain_rec.pa_id::integer as pa_id,
                            v_claim_chain_rec.pa_no_submitted::text as other_payer_prior_authorization_number,
                            NULL::text as other_payer_claim_control_number  -- 1500 claims don't have control numbers
                    )
                    SELECT array_agg(
                        (
                            op.patient_id::integer,
                            op.cob_insurance_id::integer,
                            op.cob_payer_id::integer,
                            op.other_payer_organization_name::text,
                            op.other_payer_identifier_type_code::text,
                            op.other_payer_identifier::text,
                            op.other_payer_adjudication_or_payment_date::text,
                            op.other_payer_claim_adjustment_indicator::text,
                            op.pa_id::integer,
                            op.other_payer_prior_authorization_number::text,
                            op.other_payer_claim_control_number::text,
                            CASE 
                                WHEN v_claim_chain_rec.payer_mm_sec_send_identifier IS NOT NULL AND v_claim_chain_rec.payer_mm_sec_id IS NOT NULL AND v_claim_chain_rec.payer_mm_sec_id_qualifier IS NOT NULL THEN ARRAY[
                                    (
                                        v_claim_chain_rec.payer_mm_sec_id_qualifier::text,
                                        v_claim_chain_rec.payer_mm_sec_id::text
                                    )::mm_other_payer_secondary_identifier
                                ] ELSE NULL::mm_other_payer_secondary_identifier[] END,
                            -- other_payer_address
                            ARRAY[(
                                v_claim_chain_rec.payer_address1::text,
                                v_claim_chain_rec.payer_address2::text,
                                v_claim_chain_rec.payer_city::text,
                                v_claim_chain_rec.payer_state::text,
                                v_claim_chain_rec.payer_zip::text
                            )::mm_address_info]
                        )::mm_other_payer_info
                    )
                    INTO v_other_payer_info
                    FROM other_payer op;

                    -- Build the complete COB information for this claim
                    -- Get insurance type code and claim filing code with overrides
                    -- Use insurance type code from claim chain record
                    v_final_insurance_type_code := v_claim_chain_rec.insurance_type_code;
                    
                    -- Check for patient insurance medical condition claim filing code override
                    SELECT pimc.claim_filing_code
                    INTO v_med_cond_claim_filing_override
                    FROM form_patient_insurance_med_cond pimc
                    WHERE pimc.patient_id = v_claim_chain_rec.patient_id
                      AND pimc.active_payer_id = v_payer_id
                      AND pimc.previous_payer_id = v_claim_chain_rec.payer_id
                      AND pimc.deleted IS NOT TRUE 
                      AND pimc.archived IS NOT TRUE
                      AND pimc.claim_filing_code IS NOT NULL
                    ORDER BY pimc.created_on DESC
                    LIMIT 1;

                    -- Get claim filing code from payer settings
                    SELECT payer.mm_sec_claim_filing_indicator_code
                    INTO v_payer_claim_filing_override
                    FROM form_payer payer
                    WHERE payer.id = v_claim_chain_rec.payer_id
                      AND payer.deleted IS NOT TRUE 
                      AND payer.archived IS NOT TRUE
                      AND payer.mm_sec_claim_filing_indicator_code IS NOT NULL;

                    -- Apply overrides in priority order, or fall back to default
                    -- Priority: 1) patient_insurance_med_cond override, 2) payer override, 3) default 'CI'
                    v_final_claim_filing_code := COALESCE(
                        v_med_cond_claim_filing_override,  -- Highest priority: patient insurance medical condition
                        v_payer_claim_filing_override,     -- Second priority: payer setting
                        'CI'                               -- Default: Commercial Insurance
                    );

                    RAISE LOG 'build_mm_other_subscriber_loop: Claim filing code for 1500 claim % (payer_id=%): med_cond_override=%, payer_override=%, final=%', 
                        v_claim_chain_rec.claim_no, v_claim_chain_rec.payer_id, v_med_cond_claim_filing_override, v_payer_claim_filing_override, v_final_claim_filing_code;

                    -- Build COB providers for this claim (1500 claims don't have provider records in same structure)
                    v_cob_providers := NULL;  -- 1500 claims handle providers differently

                    -- Build the final COB information for this claim
                    SELECT 
                        v_claim_chain_rec.patient_id::integer,
                        v_claim_chain_rec.insurance_id::integer,
                        v_claim_chain_rec.payer_id::integer,
                        v_payment_responsibility_code::text,
                        v_claim_chain_rec.medical_relationship_id::text,
                        v_final_insurance_type_code::text,
                        v_final_claim_filing_code::text,
                        'Y'::text,   -- benefits_assignment_certification_indicator
                        'P'::text, -- patient_signature_generate_for_patient
                        v_claim_chain_rec.policy_number::text,
                        v_claim_chain_rec.group_number::text,
                        'Y'::text,   -- release_of_information_code
                        v_other_sub_info,
                        v_other_payer_info,
                        v_payer_paid_amount::numeric,
                        v_non_covered_amount::numeric,
                        v_patient_liability::numeric,
                        v_claim_adjustments,
                        CASE WHEN v_claim_adjustments IS NOT NULL AND array_length(v_claim_adjustments, 1) > 0 THEN 'Yes' ELSE NULL END::text,  -- tabif_claim_level_adjustments
                        v_cob_providers,  -- providers from parent claim
                        CASE WHEN v_cob_providers IS NOT NULL AND array_length(v_cob_providers, 1) > 0 THEN 'Yes' ELSE NULL END::text,  -- tabif_providers
                        NULL::mm_other_service_facility_info[],  -- other_payer_service_facility_location (not implementing for now)
                        NULL::text   -- tabif_other_payer_service_facility_location
                    INTO 
                        v_cob_info.patient_id,
                        v_cob_info.cob_insurance_id,
                        v_cob_info.cob_payer_id,
                        v_cob_info.payment_responsibility_level_code,
                        v_cob_info.individual_relationship_code,
                        v_cob_info.insurance_type_code,
                        v_cob_info.claim_filing_indicator_code,
                        v_cob_info.benefits_assignment_certification_indicator,
                        v_cob_info.patient_signature_generate_for_patient,
                        v_cob_info.insurance_group_or_policy_number,
                        v_cob_info.other_insured_group_name,
                        v_cob_info.release_of_information_code,
                        v_cob_info.other_subscriber_name,
                        v_cob_info.other_payer_name,
                        v_cob_info.payer_paid_amount,
                        v_cob_info.non_covered_charge_amount,
                        v_cob_info.remaining_patient_liability,
                        v_cob_info.claim_level_adjustments,
                        v_cob_info.tabif_claim_level_adjustments,
                        v_cob_info.providers,
                        v_cob_info.tabif_providers,
                        v_cob_info.other_payer_service_facility_location,
                        v_cob_info.tabif_other_payer_service_facility_location;

                    -- Add this COB info to the result array
                    v_result := v_result || v_cob_info;
                    
                    -- Increment COB count
                    v_cob_count := v_cob_count + 1;

                    RAISE LOG 'build_mm_other_subscriber_loop: Added COB entry %/10 for 1500 claim % (payer_id=%)', 
                        v_cob_count, v_claim_chain_rec.claim_no, v_claim_chain_rec.payer_id;
                END;
            END LOOP; -- End 1500 claim processing loop
        ELSE
            -- Process medical claim using existing logic
            FOR v_claim_chain_rec IN
                WITH claim_chain AS (
                    SELECT 
                        mc.claim_no::text,
                        mc.patient_id::integer,
                        mc.insurance_id::integer,
                        mc.payer_id::integer,
                        mc.billed::numeric,
                        mc.status::text,
                        NULL::integer as pa_id,  -- PA info retrieved later from supplemental
                        NULL::text as pa_no_submitted,  -- PA info retrieved later from supplemental
                        pi.billing_method_id::text,
                        CASE pi.payer_level
                            WHEN 'P' THEN 1
                            WHEN 'S' THEN 2
                            WHEN 'T' THEN 3
                            WHEN 'Q' THEN 4
                            ELSE 1
                        END::integer as payer_sequence,
                        -- Get subscriber information
                        sub.medical_relationship_id::text,
                        sub.member_id::text,
                        sub.policy_number::text,
                        sub.group_number::text,
                        sub.insurance_type_code::text,
                        sub.first_name::text,
                        sub.last_name::text,
                        sub.middle_name::text,
                        sub.suffix::text,
                        sub.date_of_birth::text,
                        sub.gender::text,
                        -- Get subscriber address
                        addr.address1::text as sub_address1,
                        addr.address2::text as sub_address2,
                        addr.city::text as sub_city,
                        addr.state::text as sub_state,
                        addr.postal_code::text as sub_postal_code,
                        -- Get subscriber contact
                        cont.name::text as contact_name,
                        cont.email::text as contact_email,
                        cont.phone_number::text as contact_phone,
                        -- Get payer information
                        COALESCE(payer.mm_payer_name, payer.organization)::text as payer_organization,
                        payer.address1::text as payer_address1,
                        payer.address2::text as payer_address2,
                        payer.city::text as payer_city,
                        payer.state_id::text as payer_state,
                        payer.zip::text as payer_zip,
                        payer.type_id::text as payer_type_id,
                        payer.mm_sec_send_identifier::text as payer_mm_sec_send_identifier,
                        payer.mm_sec_id_qualifier::text as payer_mm_sec_id_qualifier,
                        payer.mm_sec_id::text as payer_mm_sec_id
                    FROM form_med_claim mc
                    INNER JOIN form_patient_insurance pi ON pi.id = mc.insurance_id
                        AND pi.deleted IS NOT TRUE 
                        AND pi.archived IS NOT TRUE
                    INNER JOIN form_payer payer ON payer.id = mc.payer_id
                        AND payer.deleted IS NOT TRUE 
                        AND payer.archived IS NOT TRUE
                    LEFT JOIN sf_form_med_claim_to_med_claim_subscriber sf_sub ON sf_sub.form_med_claim_fk = mc.id
                        AND sf_sub.delete IS NOT TRUE 
                        AND sf_sub.archive IS NOT TRUE
                    LEFT JOIN form_med_claim_subscriber sub ON sub.id = sf_sub.form_med_claim_subscriber_fk
                        AND sub.deleted IS NOT TRUE 
                        AND sub.archived IS NOT TRUE
                    LEFT JOIN sf_form_med_claim_subscriber_to_med_claim_address_sub sf_addr ON sf_addr.form_med_claim_subscriber_fk = sub.id
                        AND sf_addr.delete IS NOT TRUE 
                        AND sf_addr.archive IS NOT TRUE
                    LEFT JOIN form_med_claim_address_sub addr ON addr.id = sf_addr.form_med_claim_address_sub_fk
                        AND addr.deleted IS NOT TRUE 
                        AND addr.archived IS NOT TRUE
                    LEFT JOIN sf_form_med_claim_subscriber_to_med_claim_sub_cont sf_cont ON sf_cont.form_med_claim_subscriber_fk = sub.id
                        AND sf_cont.delete IS NOT TRUE 
                        AND sf_cont.archive IS NOT TRUE
                    LEFT JOIN form_med_claim_sub_cont cont ON cont.id = sf_cont.form_med_claim_sub_cont_fk
                        AND cont.deleted IS NOT TRUE 
                        AND cont.archived IS NOT TRUE
                    WHERE mc.claim_no = v_current_claim_no
                      AND mc.deleted IS NOT TRUE 
                      AND mc.archived IS NOT TRUE
                )
                SELECT * FROM claim_chain
            LOOP
        -- Check if we've reached the maximum number of COB entries (10)
        IF v_cob_count >= 10 THEN
            RAISE LOG 'build_mm_other_subscriber_loop: Reached maximum of 10 COB entries, stopping processing';
            EXIT;
        END IF;

        -- Get PA info from medical claim supplemental if not already populated
        IF v_claim_chain_rec.pa_id IS NULL AND v_claim_chain_rec.pa_no_submitted IS NULL THEN
            SELECT mcs.pa_id::integer, mcs.prior_authorization_number::text
            INTO v_claim_chain_rec.pa_id, v_claim_chain_rec.pa_no_submitted
            FROM form_med_claim mc
            INNER JOIN sf_form_med_claim_to_med_claim_info sf_mci
                ON sf_mci.form_med_claim_fk = mc.id 
                AND sf_mci.delete IS NOT TRUE 
                AND sf_mci.archive IS NOT TRUE
            INNER JOIN form_med_claim_info mci 
                ON mci.id = sf_mci.form_med_claim_info_fk
                AND mci.deleted IS NOT TRUE
                AND mci.archived IS NOT TRUE
            INNER JOIN sf_form_med_claim_info_to_med_claim_supplemental sf_mcs
                ON sf_mcs.form_med_claim_info_fk = mci.id 
                AND sf_mcs.delete IS NOT TRUE 
                AND sf_mcs.archive IS NOT TRUE
            INNER JOIN form_med_claim_supplemental mcs 
                ON mcs.id = sf_mcs.form_med_claim_supplemental_fk
                AND mcs.deleted IS NOT TRUE
                AND mcs.archived IS NOT TRUE
            WHERE mc.claim_no = v_claim_chain_rec.claim_no
              AND mc.deleted IS NOT TRUE 
              AND mc.archived IS NOT TRUE
              AND (mcs.pa_id IS NOT NULL OR mcs.prior_authorization_number IS NOT NULL)
            LIMIT 1;
            
            IF v_claim_chain_rec.pa_id IS NOT NULL OR v_claim_chain_rec.pa_no_submitted IS NOT NULL THEN
                RAISE LOG 'build_mm_other_subscriber_loop: Found PA info from medical claim supplemental: pa_id=%, pa_no=%', 
                    v_claim_chain_rec.pa_id, v_claim_chain_rec.pa_no_submitted;
            END IF;
        END IF;

        RAISE LOG 'build_mm_other_subscriber_loop: Processing claim % (payer_id=%, sequence=%)', 
            v_claim_chain_rec.claim_no, v_claim_chain_rec.payer_id, v_claim_chain_rec.payer_sequence;

        -- Build COB info for this claim
        DECLARE
            v_other_sub_info mm_other_sub_info[];
            v_other_payer_info mm_other_payer_info[];
            v_claim_adjustments mm_claim_level_adjustment_info[];
            v_payment_date text;
            v_payer_paid_amount numeric := 0;
            v_non_covered_amount numeric := 0;
            v_patient_liability numeric := 0;
            v_mcr_covered_amount numeric := 0;
            v_mcr_reimbursement_rate numeric := 0;
            v_mcr_remark_codes text[] := '{}';
            v_payment_responsibility_code text;
            v_claim_adjustment_indicator text;
        BEGIN
            -- Build other subscriber information
            WITH other_subscriber AS (
                SELECT 
                    '1'::text as other_insured_qualifier,  -- Person entity
                    'MI'::text as other_insured_identifier_type_code,
                    v_claim_chain_rec.member_id::text as other_insured_identifier,
                    v_claim_chain_rec.first_name::text as other_insured_first_name,
                    v_claim_chain_rec.last_name::text as other_insured_last_name,
                    v_claim_chain_rec.middle_name::text as other_insured_middle_name,
                    v_claim_chain_rec.suffix::text as other_insured_name_suffix
            )
            SELECT array_agg(
                (
                    os.other_insured_qualifier,
                    os.other_insured_identifier_type_code,
                    os.other_insured_identifier,
                    os.other_insured_first_name,
                    os.other_insured_last_name,
                    os.other_insured_middle_name,
                    os.other_insured_name_suffix,
                    CASE WHEN os.other_insured_first_name IS NOT NULL THEN 
                        ARRAY[(
                            v_claim_chain_rec.sub_address1,
                            v_claim_chain_rec.sub_address2,
                            v_claim_chain_rec.sub_city,
                            v_claim_chain_rec.sub_state,
                            v_claim_chain_rec.sub_postal_code
                        )::mm_address_info]
                    ELSE NULL::mm_address_info[]
                    END
                )::mm_other_sub_info
            )
            INTO v_other_sub_info
            FROM other_subscriber os;

            -- Get payment date from 835 responses for this claim
            SELECT TO_CHAR(batch.check_issue_or_eft_effective_date::date, 'MM/DD/YYYY')::text
            INTO v_payment_date
            FROM form_med_claim_resp_835 r835
            INNER JOIN form_med_claim_resp_835_batch batch 
                ON batch.response_id = r835.response_id
                AND batch.deleted IS NOT TRUE 
                AND batch.archived IS NOT TRUE
            WHERE r835.claim_no = v_claim_chain_rec.claim_no
              AND r835.deleted IS NOT TRUE
              AND r835.archived IS NOT TRUE
              AND batch.check_issue_or_eft_effective_date IS NOT NULL
            ORDER BY batch.check_issue_or_eft_effective_date::timestamp DESC
            LIMIT 1;

            -- Calculate COB amounts using helper function
            DECLARE
                v_cob_amounts jsonb;
            BEGIN
                SELECT calculate_mm_cob_amounts(v_claim_chain_rec.claim_no) INTO v_cob_amounts;
                
                v_payer_paid_amount := (v_cob_amounts->>'payer_paid_amount')::numeric;
                v_non_covered_amount := CASE 
                    WHEN v_cob_amounts->>'non_covered_amount' = 'null' THEN NULL::numeric
                    ELSE (v_cob_amounts->>'non_covered_amount')::numeric
                END;
                v_mcr_covered_amount := (v_cob_amounts->>'mcr_covered_amount')::numeric;
                v_mcr_reimbursement_rate := (v_cob_amounts->>'mcr_reimbursement_rate')::numeric;
                v_mcr_remark_codes := (v_cob_amounts->>'mcr_remark_codes')::text[];
                v_patient_liability := (v_cob_amounts->>'patient_liability')::numeric;
                v_payment_date := v_cob_amounts->>'payment_date';

                -- Build claim level adjustments if we have responses
                IF (v_cob_amounts->>'response_type')::text = '835' THEN
                    WITH claim_adjustments_result AS (
                        SELECT build_mm_claim_level_adjustments(v_claim_chain_rec.claim_no) as claim_adjustments
                    )
                    SELECT CASE WHEN car.claim_adjustments IS NOT NULL AND array_length(car.claim_adjustments, 1) > 0 
                               THEN car.claim_adjustments 
                               ELSE NULL::mm_claim_level_adjustment_info[] 
                           END
                    INTO v_claim_adjustments
                    FROM claim_adjustments_result car;
                END IF;
            END;

            -- Determine payment responsibility level code using helper function
            SELECT get_mm_payment_responsibility_code(
                v_claim_chain_rec.patient_id,
                v_claim_chain_rec.payer_id,
                v_claim_chain_rec.payer_sequence
            ) INTO v_payment_responsibility_code;

            -- Determine claim adjustment indicator before the WITH clause
            v_claim_adjustment_indicator := CASE 
                WHEN v_claim_adjustments IS NOT NULL AND array_length(v_claim_adjustments, 1) > 0 
                THEN 'Y' 
                ELSE NULL 
            END::text;

            -- Build other payer information
            WITH other_payer AS (
                SELECT 
                    v_claim_chain_rec.patient_id::integer as patient_id,
                    v_claim_chain_rec.insurance_id::integer as cob_insurance_id,
                    v_claim_chain_rec.payer_id::integer as cob_payer_id,
                    v_claim_chain_rec.payer_organization::text as other_payer_organization_name,
                    CASE 
                        WHEN v_claim_chain_rec.payer_type_id IN ('MCRB', 'MCRD') THEN 'XV'::text
                        ELSE 'PI'::text
                    END as other_payer_identifier_type_code,
                    v_claim_chain_rec.payer_id::text as other_payer_identifier,
                    v_payment_date::text as other_payer_adjudication_or_payment_date,
                    v_claim_adjustment_indicator::text as other_payer_claim_adjustment_indicator,
                    v_claim_chain_rec.pa_id::integer as pa_id,
                    v_claim_chain_rec.pa_no_submitted::text as other_payer_prior_authorization_number,
                    CASE 
                        WHEN v_claim_chain_rec.billing_method_id = 'ncpdp' THEN NULL::text
                        ELSE COALESCE(
                            -- Try to get from 835 payment response first (more common)
                            (SELECT pmt.payer_claim_control_number::text
                             FROM form_med_claim_resp_835 r835
                             INNER JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_pmt sf_pmt 
                                 ON sf_pmt.form_med_claim_resp_835_fk = r835.id
                                 AND sf_pmt.delete IS NOT TRUE 
                                 AND sf_pmt.archive IS NOT TRUE
                             INNER JOIN form_med_claim_resp_835_pmt pmt 
                                 ON pmt.id = sf_pmt.form_med_claim_resp_835_pmt_fk
                                 AND pmt.deleted IS NOT TRUE 
                                 AND pmt.archived IS NOT TRUE
                             WHERE r835.claim_no = v_claim_chain_rec.claim_no
                               AND r835.deleted IS NOT TRUE
                               AND r835.archived IS NOT TRUE
                               AND pmt.payer_claim_control_number IS NOT NULL
                             ORDER BY r835.created_on DESC
                             LIMIT 1),
                            -- Try to get from 277 response as fallback
                            (SELECT r277.trading_partner_claim_number::text
                             FROM form_med_claim_resp_277 r277
                             WHERE r277.claim_no = v_claim_chain_rec.claim_no
                               AND r277.deleted IS NOT TRUE 
                               AND r277.archived IS NOT TRUE
                               AND r277.trading_partner_claim_number IS NOT NULL
                             ORDER BY r277.created_on DESC
                             LIMIT 1),
                            NULL::text
                        )
                    END as other_payer_claim_control_number
            )
            SELECT array_agg(
                (
                    op.patient_id::integer,
                    op.cob_insurance_id::integer,
                    op.cob_payer_id::integer,
                    op.other_payer_organization_name::text,
                    op.other_payer_identifier_type_code::text,
                    op.other_payer_identifier::text,
                    op.other_payer_adjudication_or_payment_date::text,
                    op.other_payer_claim_adjustment_indicator::text,
                    op.pa_id::integer,
                    op.other_payer_prior_authorization_number::text,
                    op.other_payer_claim_control_number::text,
                    CASE 
                        WHEN v_claim_chain_rec.payer_mm_sec_send_identifier IS NOT NULL AND v_claim_chain_rec.payer_mm_sec_id IS NOT NULL AND v_claim_chain_rec.payer_mm_sec_id_qualifier IS NOT NULL THEN ARRAY[
                            (
                                v_claim_chain_rec.payer_mm_sec_id_qualifier::text,
                                v_claim_chain_rec.payer_mm_sec_id::text
                            )::mm_other_payer_secondary_identifier
                        ]
                        ELSE NULL::mm_other_payer_secondary_identifier[]
                    END,
                    -- other_payer_address
                    ARRAY[(
                        v_claim_chain_rec.payer_address1::text,
                        v_claim_chain_rec.payer_address2::text,
                        v_claim_chain_rec.payer_city::text,
                        v_claim_chain_rec.payer_state::text,
                        v_claim_chain_rec.payer_zip::text
                    )::mm_address_info]
                )::mm_other_payer_info
            )
            INTO v_other_payer_info
            FROM other_payer op;

            -- Build the complete COB information for this claim
            -- Get insurance type code and claim filing code with overrides
            DECLARE
                v_final_insurance_type_code text;
                v_final_claim_filing_code text;
                v_med_cond_claim_filing_override text;
                v_payer_claim_filing_override text;
            BEGIN
                -- Use insurance type code from claim chain record
                v_final_insurance_type_code := v_claim_chain_rec.insurance_type_code;
                
                -- Check for patient insurance medical condition claim filing code override
                SELECT pimc.claim_filing_code
                INTO v_med_cond_claim_filing_override
                FROM form_patient_insurance_med_cond pimc
                WHERE pimc.patient_id = v_claim_chain_rec.patient_id
                  AND pimc.active_payer_id = v_payer_id
                  AND pimc.previous_payer_id = v_claim_chain_rec.payer_id
                  AND pimc.deleted IS NOT TRUE 
                  AND pimc.archived IS NOT TRUE
                  AND pimc.claim_filing_code IS NOT NULL
                ORDER BY pimc.created_on DESC
                LIMIT 1;

                -- Get claim filing code from payer settings
                SELECT payer.mm_sec_claim_filing_indicator_code
                INTO v_payer_claim_filing_override
                FROM form_payer payer
                WHERE payer.id = v_claim_chain_rec.payer_id
                  AND payer.deleted IS NOT TRUE 
                  AND payer.archived IS NOT TRUE
                  AND payer.mm_sec_claim_filing_indicator_code IS NOT NULL;

                -- Apply overrides in priority order, or fall back to default
                -- Priority: 1) patient_insurance_med_cond override, 2) payer override, 3) default 'CI'
                v_final_claim_filing_code := COALESCE(
                    v_med_cond_claim_filing_override,  -- Highest priority: patient insurance medical condition
                    v_payer_claim_filing_override,     -- Second priority: payer setting
                    'CI'                               -- Default: Commercial Insurance
                );

                RAISE LOG 'build_mm_other_subscriber_loop: Claim filing code for claim % (payer_id=%): med_cond_override=%, payer_override=%, final=%', 
                    v_claim_chain_rec.claim_no, v_claim_chain_rec.payer_id, v_med_cond_claim_filing_override, v_payer_claim_filing_override, v_final_claim_filing_code;
            END;

            -- Build COB providers for this claim
            DECLARE
                v_cob_providers mm_cob_providers_loop[];
            BEGIN
                SELECT build_mm_cob_providers(v_claim_chain_rec.claim_no) INTO v_cob_providers;
            END;

            -- Build the final COB information for this claim
            SELECT 
                v_claim_chain_rec.patient_id::integer,
                v_claim_chain_rec.insurance_id::integer,
                v_claim_chain_rec.payer_id::integer,
                v_claim_chain_rec.payer_type_id::text,
                CASE WHEN v_claim_chain_rec.payer_type_id = 'MCRB' THEN v_mcr_covered_amount::numeric ELSE NULL::numeric END,
                CASE WHEN v_claim_chain_rec.payer_type_id = 'MCRB' THEN v_mcr_reimbursement_rate::numeric ELSE NULL::numeric END,
                CASE WHEN v_claim_chain_rec.payer_type_id = 'MCRB' THEN v_mcr_remark_codes::text[] ELSE NULL::text[] END,
                v_payment_responsibility_code::text,
                v_claim_chain_rec.medical_relationship_id::text,
                v_final_insurance_type_code::text,
                v_final_claim_filing_code::text,
                'Y'::text,   -- benefits_assignment_certification_indicator
                'P'::text, -- patient_signature_generate_for_patient
                v_claim_chain_rec.policy_number::text,
                'Y'::text,   -- release_of_information_code
                v_other_sub_info,
                v_other_payer_info,
                v_payer_paid_amount::numeric,
                v_non_covered_amount::numeric,
                v_patient_liability::numeric,
                v_claim_adjustments,
                CASE WHEN v_claim_adjustments IS NOT NULL AND array_length(v_claim_adjustments, 1) > 0 THEN 'Yes' ELSE NULL END::text,  -- tabif_claim_level_adjustments
                v_cob_providers,  -- providers from parent claim
                CASE WHEN v_cob_providers IS NOT NULL AND array_length(v_cob_providers, 1) > 0 THEN 'Yes' ELSE NULL END::text,  -- tabif_providers
                NULL::mm_other_service_facility_info[],  -- other_payer_service_facility_location (not implementing for now)
                NULL::text   -- tabif_other_payer_service_facility_location
            INTO 
                v_cob_info.patient_id,
                v_cob_info.cob_insurance_id,
                v_cob_info.cob_payer_id,
                v_cob_info.cob_payer_type_id,
                v_cob_info.mcr_reimbursement_rate,
                v_cob_info.mcr_hcpcs_payable_amount,
                v_cob_info.mcr_rcodes,
                v_cob_info.payment_responsibility_level_code,
                v_cob_info.individual_relationship_code,
                v_cob_info.insurance_type_code,
                v_cob_info.claim_filing_indicator_code,
                v_cob_info.benefits_assignment_certification_indicator,
                v_cob_info.patient_signature_generate_for_patient,
                v_cob_info.insurance_group_or_policy_number,
                v_cob_info.other_insured_group_name,
                v_cob_info.release_of_information_code,
                v_cob_info.other_subscriber_name,
                v_cob_info.other_payer_name,
                v_cob_info.payer_paid_amount,
                v_cob_info.non_covered_charge_amount,
                v_cob_info.remaining_patient_liability,
                v_cob_info.claim_level_adjustments,
                v_cob_info.tabif_claim_level_adjustments,
                v_cob_info.providers,
                v_cob_info.tabif_providers,
                v_cob_info.other_payer_service_facility_location,
                v_cob_info.tabif_other_payer_service_facility_location;

            -- Add this COB info to the result array
            v_result := v_result || v_cob_info;
            
            -- Increment COB count
            v_cob_count := v_cob_count + 1;

            RAISE LOG 'build_mm_other_subscriber_loop: Added COB entry %/10 for claim % (payer_id=%, paid_amount=%)', 
                v_cob_count, v_claim_chain_rec.claim_no, v_claim_chain_rec.payer_id, v_payer_paid_amount;
        END;
            END LOOP; -- End medical claim processing loop
        END IF; -- End NCPDP vs medical claim check
    END LOOP; -- End claim hierarchy loop

    RAISE LOG 'MM other subscriber loop build result: % COB entries', COALESCE(array_length(v_result, 1), 0);

    -- Log success
    PERFORM log_billing_function(
      'build_mm_other_subscriber_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_other_subscriber_loop',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'build_mm_other_subscriber_loop',
        'insurance_id', p_insurance_id,
        'parent_claim_no', p_parent_claim_no
      )
    );

    PERFORM log_billing_function(
      'build_mm_other_subscriber_loop'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION build_mm_sl_cob_adjustments(
  p_charge_line charge_line_with_split,
  p_parent_claim_no text,
  p_payer_id integer
) RETURNS mm_sl_adjustment_info[] AS $BODY$
DECLARE
  v_result mm_sl_adjustment_info[];
  v_adjustment_info mm_sl_adjustment_info;
  v_adjustment_groups mm_sl_adj_group_info[];
  v_error_message text;
  v_start_time timestamp;
  v_payer_rec record;
  v_master_claim_no text;
  v_claim_hierarchy text[];
  v_current_claim_no text;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  RAISE LOG 'build_mm_sl_cob_adjustments: Starting with rx_no=%, parent_claim_no=%, charge_no=%', 
    p_charge_line.rx_no, p_parent_claim_no, p_charge_line.charge_no;

  -- Initialize result array
  v_result := ARRAY[]::mm_sl_adjustment_info[];

  -- First, get the top-level claim by finding the master charge line
  -- Get the top-level claim from the master charge line
  SELECT mc.claim_no
  INTO v_master_claim_no
  FROM form_ledger_charge_line lcl
  INNER JOIN form_billing_invoice bi ON bi.invoice_no = lcl.invoice_no
      AND bi.deleted IS NOT TRUE 
      AND bi.archived IS NOT TRUE
  INNER JOIN form_med_claim mc ON mc.claim_no = bi.claim_no
      AND mc.deleted IS NOT TRUE 
      AND mc.archived IS NOT TRUE
  WHERE lcl.charge_no = p_charge_line.master_charge_no
    AND lcl.deleted IS NOT TRUE 
    AND lcl.archived IS NOT TRUE;

  IF v_master_claim_no IS NULL THEN
      RAISE LOG 'build_mm_sl_cob_adjustments: No master claim found for charge line master_charge_no: %', p_charge_line.master_charge_no;
      RETURN v_result;
  END IF;

  -- Build the claim hierarchy starting from the master claim
  v_claim_hierarchy := ARRAY[v_master_claim_no];
  v_current_claim_no := v_master_claim_no;
  
  -- Follow the parent_claim_no chain to get all claims in hierarchy
  WHILE EXISTS (
    SELECT 1 FROM form_med_claim mc 
    WHERE mc.parent_claim_no = v_current_claim_no
      AND mc.deleted IS NOT TRUE 
      AND mc.archived IS NOT TRUE
  ) LOOP
    SELECT mc.claim_no
    INTO v_current_claim_no
    FROM form_med_claim mc
    WHERE mc.parent_claim_no = v_current_claim_no
      AND mc.deleted IS NOT TRUE 
      AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    v_claim_hierarchy := v_claim_hierarchy || v_current_claim_no;
  END LOOP;

  RAISE LOG 'build_mm_sl_cob_adjustments: Built claim hierarchy: %', v_claim_hierarchy;

  -- Get all 835 responses for claims in the claim hierarchy that have service line adjustments
  FOR v_payer_rec IN
    WITH claim_chain AS (
      -- Get all claims in the hierarchy
      SELECT mc.claim_no, mc.insurance_id, mc.payer_id
      FROM form_med_claim mc
      WHERE mc.claim_no = ANY(v_claim_hierarchy)
        AND mc.deleted IS NOT TRUE 
        AND mc.archived IS NOT TRUE
      ORDER BY mc.id ASC
    ),
    service_line_data AS (
      -- Get service lines that match our control number and have adjustments
      SELECT DISTINCT
        cc.claim_no::text as claim_no,
        r835.id::integer as response_id,
        cc.insurance_id::integer as insurance_id,
        cc.payer_id::integer as payer_id,
        batch.check_issue_or_eft_effective_date::date as check_issue_or_eft_effective_date,
        sl.id::integer as service_line_id,
        sl.submitted_procedure_code_description::text as submitted_procedure_code_description,
        sl.product_or_service_id_qualifier::text as product_or_service_id_qualifier,
        sl.adjudicated_procedure_code::text as adjudicated_procedure_code,
        sl.units_of_service_paid_count::numeric as units_of_service_paid_count,
        sl.line_item_provider_payment_amount::numeric as line_item_provider_payment_amount,
        sl.line_item_charge_amount::numeric as line_item_charge_amount,
        sl.adjudicated_procedure_modifier_1::text as adjudicated_procedure_modifier_1,
        sl.adjudicated_procedure_modifier_2::text as adjudicated_procedure_modifier_2,
        sl.adjudicated_procedure_modifier_3::text as adjudicated_procedure_modifier_3,
        sl.adjudicated_procedure_modifier_4::text as adjudicated_procedure_modifier_4
      FROM claim_chain cc
      INNER JOIN form_med_claim_resp_835 r835 ON r835.claim_no = cc.claim_no
        AND r835.deleted IS NOT TRUE 
        AND r835.archived IS NOT TRUE
      INNER JOIN form_med_claim_resp_835_batch batch 
        ON batch.response_id = r835.response_id
        AND batch.deleted IS NOT TRUE 
        AND batch.archived IS NOT TRUE
      INNER JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_sl sf_sl
          ON sf_sl.form_med_claim_resp_835_fk = r835.id
          AND sf_sl.delete IS NOT TRUE 
          AND sf_sl.archive IS NOT TRUE
      INNER JOIN form_med_claim_resp_835_sl sl 
          ON sl.id = sf_sl.form_med_claim_resp_835_sl_fk
          AND sl.deleted IS NOT TRUE
          AND sl.archived IS NOT TRUE
      WHERE sl.line_item_control_number = p_charge_line.master_charge_no::text
        -- Only include service lines that have adjustments
        AND EXISTS (
          SELECT 1 
          FROM sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj sf_sl_adj
          INNER JOIN form_med_claim_resp_835_sl_adj sladj 
              ON sladj.id = sf_sl_adj.form_med_claim_resp_835_sl_adj_fk
              AND sladj.deleted IS NOT TRUE
              AND sladj.archived IS NOT TRUE
          WHERE sf_sl_adj.form_med_claim_resp_835_sl_fk = sl.id
            AND sf_sl_adj.delete IS NOT TRUE 
            AND sf_sl_adj.archive IS NOT TRUE
        )
    )
    -- Final selection with payer organization name
    SELECT DISTINCT
      sld.claim_no::text as claim_no,
      sld.service_line_id::integer as service_line_id,
      sld.submitted_procedure_code_description::text as submitted_procedure_code_description,
      sld.product_or_service_id_qualifier::text as product_or_service_id_qualifier,
      sld.adjudicated_procedure_code::text as adjudicated_procedure_code,
      sld.units_of_service_paid_count::numeric as units_of_service_paid_count,
      sld.line_item_provider_payment_amount::numeric as line_item_provider_payment_amount,
      sld.line_item_charge_amount::numeric as line_item_charge_amount,
      sld.adjudicated_procedure_modifier_1::text as adjudicated_procedure_modifier_1,
      sld.adjudicated_procedure_modifier_2::text as adjudicated_procedure_modifier_2,
      sld.adjudicated_procedure_modifier_3::text as adjudicated_procedure_modifier_3,
      sld.adjudicated_procedure_modifier_4::text as adjudicated_procedure_modifier_4,
      sld.check_issue_or_eft_effective_date::date as check_issue_or_eft_effective_date,
      -- Get the payer organization name using the same logic as other_subscriber_loop
      COALESCE(pimc.sec_payer_id, py.mm_sec_payer_id, py.organization)::text as other_payer_organization_name
    FROM service_line_data sld
    INNER JOIN form_patient_insurance pi ON pi.id = sld.insurance_id
        AND pi.deleted IS NOT TRUE
        AND pi.archived IS NOT TRUE
    INNER JOIN form_payer py ON py.id = sld.payer_id
        AND py.deleted IS NOT TRUE 
        AND py.archived IS NOT TRUE
    LEFT JOIN form_patient_insurance_med_cond pimc ON pimc.patient_id = pi.patient_id
        AND pimc.active_payer_id = p_payer_id
        AND pimc.previous_payer_id = sld.payer_id
        AND pimc.deleted IS NOT TRUE 
        AND pimc.archived IS NOT TRUE
  LOOP
    RAISE LOG 'build_mm_sl_cob_adjustments: Processing payer with claim_no=%, organization_name=%', 
      v_payer_rec.claim_no, v_payer_rec.other_payer_organization_name;

    -- Build adjustment groups for this payer
    DECLARE
      v_current_group_code text;
      v_current_group_details mm_sl_adj_detail_info[];
      v_group_adjustment_count integer;
      v_adj_group mm_sl_adj_group_info;
      v_adj_detail mm_sl_adj_detail_info;
      v_service_line_adj record;
      v_patient_liability numeric;
    BEGIN
      -- Initialize adjustment groups array for this payer
      v_adjustment_groups := ARRAY[]::mm_sl_adj_group_info[];
      v_current_group_code := NULL;
      v_current_group_details := ARRAY[]::mm_sl_adj_detail_info[];
      v_group_adjustment_count := 0;
      
      -- Get service line adjustments for this specific service line, grouped by adjustment group code
      FOR v_service_line_adj IN
        SELECT 
          sladj.claim_adjustment_group_code::text as claim_adjustment_group_code,
          sladj.adjustment_reason_code::text as adjustment_reason_code,
          sladj.adjustment_amount::numeric as adjustment_amount,
          sladj.adjustment_quantity::numeric as adjustment_quantity
        FROM sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj sf_sl_adj
        INNER JOIN form_med_claim_resp_835_sl_adj sladj 
            ON sladj.id = sf_sl_adj.form_med_claim_resp_835_sl_adj_fk
            AND sladj.deleted IS NOT TRUE
            AND sladj.archived IS NOT TRUE
        WHERE sf_sl_adj.form_med_claim_resp_835_sl_fk = v_payer_rec.service_line_id
          AND sf_sl_adj.delete IS NOT TRUE 
          AND sf_sl_adj.archive IS NOT TRUE
          AND (sladj.claim_adjustment_group_code = 'PR' 
               OR (sladj.claim_adjustment_group_code = 'CO' AND sladj.adjustment_reason_code = '96'))
        ORDER BY sladj.claim_adjustment_group_code, sladj.adjustment_amount DESC
      LOOP
        -- Check if we're starting a new group
        IF v_current_group_code IS NULL OR v_current_group_code != v_service_line_adj.claim_adjustment_group_code THEN
          -- If we have a previous group with adjustments, add it to the result
          IF v_current_group_code IS NOT NULL AND array_length(v_current_group_details, 1) > 0 THEN
            SELECT
              v_current_group_code::text,
              v_current_group_details
            INTO v_adj_group;
            
            v_adjustment_groups := v_adjustment_groups || v_adj_group;
            
            RAISE LOG 'build_mm_sl_cob_adjustments: Added adjustment group % for claim_no=% with % adjustments', 
              v_current_group_code, v_payer_rec.claim_no, array_length(v_current_group_details, 1);
          END IF;
          
          -- Start new group
          v_current_group_code := v_service_line_adj.claim_adjustment_group_code;
          v_current_group_details := ARRAY[]::mm_sl_adj_detail_info[];
          v_group_adjustment_count := 0;
        END IF;

        -- Add adjustment to current group if we haven't exceeded the limit
        IF v_group_adjustment_count < 6 THEN -- Max 6 adjustments per group
          -- Build adjustment detail
          SELECT 
            v_service_line_adj.adjustment_reason_code::text,
            v_service_line_adj.adjustment_amount::numeric,
            COALESCE(v_service_line_adj.adjustment_quantity::numeric, 1::numeric)
          INTO v_adj_detail;

          v_current_group_details := v_current_group_details || v_adj_detail;
          v_group_adjustment_count := v_group_adjustment_count + 1;

          RAISE LOG 'build_mm_sl_cob_adjustments: Added service line adjustment group=%, reason_code=%, amount=%, quantity=%', 
            v_service_line_adj.claim_adjustment_group_code, v_service_line_adj.adjustment_reason_code, 
            v_service_line_adj.adjustment_amount, v_service_line_adj.adjustment_quantity;
        END IF;
      END LOOP;

      -- Add the final group if it has adjustments
      IF v_current_group_code IS NOT NULL AND array_length(v_current_group_details, 1) > 0 THEN
        SELECT
          v_current_group_code::text,
          v_current_group_details
        INTO v_adj_group;

        v_adjustment_groups := v_adjustment_groups || v_adj_group;

        RAISE LOG 'build_mm_sl_cob_adjustments: Added final adjustment group % for claim_no=% with % adjustments', 
          v_current_group_code, v_payer_rec.claim_no, array_length(v_current_group_details, 1);
      END IF;

      -- Calculate patient liability by summing PR adjustments, or use charge - payment if no PR adjustments
      WITH pr_adjustments AS (
          SELECT COALESCE(SUM(sladj.adjustment_amount::numeric), 0::numeric) as pr_total
          FROM sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj sf_sl_adj
          INNER JOIN form_med_claim_resp_835_sl_adj sladj 
              ON sladj.id = sf_sl_adj.form_med_claim_resp_835_sl_adj_fk
              AND sladj.deleted IS NOT TRUE
              AND sladj.archived IS NOT TRUE
          WHERE sf_sl_adj.form_med_claim_resp_835_sl_fk = v_payer_rec.service_line_id
            AND sf_sl_adj.delete IS NOT TRUE 
            AND sf_sl_adj.archive IS NOT TRUE
            AND sladj.claim_adjustment_group_code = 'PR'
      ),
      service_line_amounts AS (
          SELECT 
              v_payer_rec.line_item_charge_amount::numeric as charge_amount,
              v_payer_rec.line_item_provider_payment_amount::numeric as payment_amount
      )
      SELECT 
          CASE 
              WHEN pr.pr_total > 0 THEN pr.pr_total
              ELSE COALESCE(sla.charge_amount::numeric - sla.payment_amount::numeric, 0::numeric)
          END
      INTO v_patient_liability
             FROM pr_adjustments pr, service_line_amounts sla;

    -- Build the service line adjustment info for this payer
    SELECT 
      v_payer_rec.other_payer_organization_name::text, -- other_payer_primary_identifier
      v_payer_rec.submitted_procedure_code_description::text, -- procedure_code
      COALESCE(v_payer_rec.product_or_service_id_qualifier, 'HC')::text, -- service_id_qualifier
      v_payer_rec.adjudicated_procedure_code::text, -- procedure_code_description
      COALESCE(v_payer_rec.units_of_service_paid_count::numeric, 0), -- paid_service_unit_count
      to_char(v_payer_rec.check_issue_or_eft_effective_date, 'MM/DD/YYYY')::text, -- adjudication_or_payment_date
      COALESCE(v_payer_rec.line_item_provider_payment_amount::numeric, 0), -- service_line_paid_amount
      v_patient_liability::numeric, -- remaining_patient_liability
      v_payer_rec.adjudicated_procedure_modifier_1::text, -- modifier_1
      v_payer_rec.adjudicated_procedure_modifier_2::text, -- modifier_2
      v_payer_rec.adjudicated_procedure_modifier_3::text, -- modifier_3
      v_payer_rec.adjudicated_procedure_modifier_4::text, -- modifier_4
      COALESCE(v_adjustment_groups, ARRAY[]::mm_sl_adj_group_info[]) -- claim_adjustment_information
    INTO 
      v_adjustment_info.other_payer_primary_identifier,
      v_adjustment_info.procedure_code,
      v_adjustment_info.service_id_qualifier,
      v_adjustment_info.procedure_code_description,
      v_adjustment_info.paid_service_unit_count,
      v_adjustment_info.adjudication_or_payment_date,
      v_adjustment_info.service_line_paid_amount,
      v_adjustment_info.remaining_patient_liability,
      v_adjustment_info.modifier_1,
      v_adjustment_info.modifier_2,
      v_adjustment_info.modifier_3,
      v_adjustment_info.modifier_4,
      v_adjustment_info.claim_adjustment_information;

    -- Add this payer's adjustment info to the result array
    v_result := v_result || v_adjustment_info;

    RAISE LOG 'build_mm_sl_cob_adjustments: Built adjustment info for payer=%, service_line_paid_amount=%', 
      v_payer_rec.other_payer_organization_name, v_adjustment_info.service_line_paid_amount;
    END;
  END LOOP;

  RAISE LOG 'build_mm_sl_cob_adjustments: Completed with % payer adjustment records for rx_no=%', 
    array_length(v_result, 1), p_charge_line.rx_no;

  RETURN v_result;

EXCEPTION WHEN OTHERS THEN
  v_error_message := SQLERRM;
  
  RAISE LOG 'build_mm_sl_cob_adjustments: ERROR - % for rx_no=%, parent_claim_no=%', 
    v_error_message, p_charge_line.rx_no, p_parent_claim_no;

  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    v_error_message,
    'Exception in build_mm_sl_cob_adjustments',
    'FUNCTION',
    current_schema(),
    'med_claim',
    jsonb_build_object(
      'function_name', 'build_mm_sl_cob_adjustments',
      'rx_no', p_charge_line.rx_no,
      'parent_claim_no', p_parent_claim_no,
      'charge_no', p_charge_line.charge_no,
      'execution_time_ms', EXTRACT(EPOCH FROM (clock_timestamp() - v_start_time)) * 1000
    )
  );

  RAISE;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

