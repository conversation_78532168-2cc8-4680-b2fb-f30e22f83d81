DO $$ BEGIN
  PERFORM drop_all_function_signatures('handle_invoice_archive_void');
END $$;
CREATE OR REPLACE FUNCTION handle_invoice_archive_void()
RETURNS TRIGGER AS $$
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);
    PERFORM set_config('clara.disable_invoice_trigger', 'on', false);
    PERFORM set_config('clara.prevent_locked_checks', 'on', false);

    -- Check if either archived is true or void is 'Yes'
    IF (NEW.archived = true OR COALESCE(NEW.void, 'No') = 'Yes') THEN
        -- Case 1: When parent_invoice_no is not null
        IF NEW.parent_invoice_no IS NOT NULL OR NEW.delivery_ticket_id IS NULL THEN
            -- If void , update void-related fields
            IF COALESCE(NEW.void, 'No') = 'Yes' THEN
                UPDATE form_ledger_charge_line
                SET 
                    void = NEW.void,
                    void_reason_id = NEW.void_reason_id,
                    voided_datetime = NEW.voided_datetime
                WHERE invoice_no = NEW.invoice_no
                AND COALESCE(void, 'No') <> 'Yes'
                AND COALESCE(zeroed, 'No') <> 'Yes'
                AND COALESCE(locked, 'No') <> 'Yes';
            -- If archived = true, set archived on related entries
            ELSE
                UPDATE form_ledger_charge_line
                SET archived = true
                WHERE invoice_no = NEW.invoice_no
                AND COALESCE(locked, 'No') <> 'Yes'
                AND COALESCE(void, 'No') <> 'Yes'
                AND COALESCE(zeroed, 'No') <> 'Yes';
            END IF;
        -- Case 2: When parent_invoice_no is null
        ELSE
            -- Clear invoice_no from related entries
            UPDATE form_ledger_charge_line
            SET invoice_no = NULL,
                invoice_status = NULL
            WHERE invoice_no = NEW.invoice_no
                AND COALESCE(locked, 'No') <> 'Yes'
                AND COALESCE(void, 'No') <> 'Yes'
                AND COALESCE(zeroed, 'No') <> 'Yes';
        END IF;
    END IF;
    PERFORM set_config('clara.disable_invoice_trigger', 'off', false);
    PERFORM set_config('clara.prevent_locked_checks', 'off', false);

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE OR REPLACE TRIGGER trg_invoice_archive_void
AFTER UPDATE ON form_billing_invoice
FOR EACH ROW
WHEN (
    (NEW.archived = true AND OLD.archived = false) OR 
    (COALESCE(NEW.void, 'No') = 'Yes' AND COALESCE(OLD.void, 'No') = 'No')
)
EXECUTE FUNCTION handle_invoice_archive_void();