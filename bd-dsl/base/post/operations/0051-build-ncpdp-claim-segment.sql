
CREATE OR REPLACE FUNCTION build_claim_ncpdp_segment(
  p_insurance_id integer,
  p_payer_id integer,
  p_patient_id integer,
  p_site_id integer,
  p_prescriber_id integer,
  p_date_of_service date,
  p_day_supply integer,
  p_inventory_id integer,
  p_charge_quantity numeric,
  p_previous_payer_id integer DEFAULT NULL,
  p_parent_claim_no text DEFAULT NULL,
  p_rx_no text DEFAULT NULL,
  p_order_item_id integer DEFAULT NULL,
  p_orderp_item_id integer DEFAULT NULL,
  p_fill_number integer DEFAULT 1,
  p_transaction_code text DEFAULT 'B1',
  p_is_cmpd_claim BOOLEAN DEFAULT False,
  p_is_noncmpd_treated_as_cmpd BOOLEAN DEFAULT False,

  -- Optional Test Claim Parameters
  p_route_id text DEFAULT NULL,
  p_compound_code text DEFAULT NULL,
  p_compound_type text DEFAULT NULL,
  p_is_test BOOLEAN DEFAULT False
) RETURNS ncpdp_claim
AS $BODY$
DECLARE
  v_start_time timestamp;
  v_execution_time interval;
  v_result ncpdp_claim;
  v_error_message text;
  v_params jsonb;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'insurance_id', p_insurance_id,
    'patient_id', p_patient_id,
    'site_id', p_site_id,
    'prescriber_id', p_prescriber_id,
    'date_of_service', p_date_of_service,
    'day_supply', p_day_supply,
    'previous_payer_id', p_previous_payer_id,
    'parent_claim_no', p_parent_claim_no,
    'rx_no', p_rx_no,
    'order_item_id', p_order_item_id,
    'orderp_item_id', p_orderp_item_id,
    'fill_number', p_fill_number,
    'transaction_code', p_transaction_code,
    'is_cmpd_claim', p_is_cmpd_claim,
    'is_noncmpd_treated_as_cmpd', p_is_noncmpd_treated_as_cmpd,
    'inventory_id', p_inventory_id,
    'charge_quantity', p_charge_quantity,
    'route_id', p_route_id,
    'compound_code', p_compound_code,
    'compound_type', p_compound_type,
    'is_test', p_is_test
  );

  BEGIN  -- Start exception block
    -- Log function call
    PERFORM log_billing_function(
      'build_claim_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );
    -- Validate required parameters
    IF p_patient_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Patient ID cannot be null',
            'Validating parameters in build_claim_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_claim_ncpdp_segment',
                'patient_id', p_patient_id,
                'inventory_id', p_inventory_id,
                'site_id', p_site_id
            )
        );
        RAISE EXCEPTION 'Patient ID cannot be null';
    END IF;
    IF p_inventory_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Inventory ID cannot be null',
            'Validating parameters in build_claim_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_claim_ncpdp_segment',
                'patient_id', p_patient_id,
                'inventory_id', p_inventory_id,
                'site_id', p_site_id
            )
        );
        RAISE EXCEPTION 'Inventory ID cannot be null';
    END IF;
    IF p_site_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Site ID cannot be null',
            'Validating parameters in build_claim_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_claim_ncpdp_segment',
                'patient_id', p_patient_id,
                'inventory_id', p_inventory_id,
                'site_id', p_site_id
            )
        );
        RAISE EXCEPTION 'Site ID cannot be null';
    END IF;
    -- Validate business rules
    IF p_is_cmpd_claim AND p_is_noncmpd_treated_as_cmpd THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Claim cannot be both compound and non-compound treated as compound',
            'Validating business rules in build_claim_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_claim_ncpdp_segment',
                'is_cmpd_claim', p_is_cmpd_claim,
                'is_noncmpd_treated_as_cmpd', p_is_noncmpd_treated_as_cmpd
            )
        );
        RAISE EXCEPTION 'Claim cannot be both compound and non-compound treated as compound';
    END IF;
    IF p_transaction_code IN ('B2', 'S2') AND p_parent_claim_no IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Parent Claim # required for reversal transaction',
            'Validating business rules in build_claim_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_claim_ncpdp_segment',
                'transaction_code', p_transaction_code,
                'parent_claim_no', p_parent_claim_no
            )
        );
        RAISE EXCEPTION 'Parent Claim # required for reversal transaction';
    END IF;

    -- Get insurance settings once
    WITH insurance_info AS (
      SELECT * FROM get_insurance_claim_settings(p_insurance_id, p_site_id, p_previous_payer_id)
    ),
    parent_claim_data AS (
      SELECT clm.*,
      grsc.sub_clar_code,
      ncr.occ,
      grpm.pmc
      FROM form_ncpdp pclaim --TODO: Handle COB scenario for non-NCPDP parent claims
      LEFT JOIN sf_form_ncpdp_to_ncpdp_claim sfcl ON sfcl.form_ncpdp_fk = pclaim.id 
      AND sfcl.archive IS NOT TRUE 
      AND sfcl.delete IS NOT TRUE
      LEFT JOIN form_ncpdp_claim clm ON clm.id = sfcl.form_ncpdp_claim_fk 
      AND clm.archived IS NOT TRUE 
      AND clm.deleted IS NOT TRUE
      LEFT JOIN LATERAL (
        SELECT
          array_agg(grpm.form_list_ncpdp_ext_ecl_fk) as pmc
        FROM gr_form_ncpdp_claim_proc_mod_code_to_list_ncpdp_ext_ecl_id grpm
        WHERE grpm.form_ncpdp_claim_fk = clm.id
        LIMIT 4
      ) grpm ON TRUE
      LEFT JOIN LATERAL (
        SELECT
            CASE
                WHEN stat.transaction_response_status IN ('Q', 'C') THEN '2' -- Duplicate Captured, Captured
                WHEN stat.transaction_response_status IN ('F', 'R') THEN '3' -- PA Deferred
                WHEN stat.transaction_response_status IN ('D', 'P') THEN -- Duplicate Paid, Paid
                  CASE
                    WHEN prc.ing_cst_paid::numeric = COALESCE(ncr.ing_cst_sub,0)::numeric
                    AND prc.pt_pay_amt > 0 THEN '8'
                    ELSE '2'
                  END
            ELSE NULL
          END as occ
          FROM form_ncpdp_response ncr
          CROSS JOIN insurance_info ins
          LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_prc sfprc 
              ON sfprc.form_ncpdp_response_fk = ncr.id 
              AND sfprc.archive IS NOT TRUE
              AND sfprc.delete IS NOT TRUE
          LEFT JOIN form_ncpdp_response_prc prc 
              ON prc.id = sfprc.form_ncpdp_response_prc_fk 
              AND prc.archived IS NOT TRUE
              AND prc.deleted IS NOT TRUE
          INNER JOIN sf_form_ncpdp_response_to_ncpdp_response_stat sfstat 
              ON sfstat.form_ncpdp_response_fk = ncr.id
              AND sfstat.archive IS NOT TRUE
              AND sfstat.delete IS NOT TRUE
          INNER JOIN form_ncpdp_response_stat stat
              ON stat.id = sfstat.form_ncpdp_response_stat_fk 
              AND stat.archived IS NOT TRUE
              AND stat.deleted IS NOT TRUE
          WHERE ncr.claim_no = p_parent_claim_no
          AND ncr.archived IS NOT TRUE
          AND ncr.deleted IS NOT TRUE
          AND ncr.response_status = 'A'
          ORDER BY ncr.id DESC
          LIMIT 1
        ) ncr ON TRUE

      LEFT JOIN LATERAL(
        SELECT
          array_agg(grsc.form_list_ncpdp_ecl_fk) as sub_clar_code
        FROM gr_form_ncpdp_claim_sub_clar_code_to_list_ncpdp_ecl_id grsc
        WHERE grsc.form_ncpdp_claim_fk = clm.id
      ) grsc ON TRUE
      WHERE pclaim.claim_no = p_parent_claim_no
      AND pclaim.archived IS NOT TRUE
      AND pclaim.deleted IS NOT TRUE
      AND COALESCE(pclaim.void, 'No') <> 'Yes'
    ),
    order_info AS (
      SELECT 
        co.id as order_id,
        rx.doses_to_prep,
        rx.start_date,
        rx.compound_type as compound_type,
        rx.sched_rx_no,
        COALESCE(coi.drug_pa_id, opi.drug_pa_id) as drug_pa_id,
        rx.rx_origin_id as rx_origin_id,
        COALESCE(coi.route_id, opi.route_id) as route_id,
        rx.daw_code as daw_code,
        rx.written_date,
        rx.refill_tracking as refill_tracking,
        rx.refills as refills,
        rx.doses_allowed as doses_allowed,
        CASE 
          WHEN COALESCE(rx.is_340b, 'No') = 'Yes' THEN TRUE
          ELSE FALSE
        END as is_340b
      FROM form_careplan_order_rx rx
      INNER JOIN form_careplan_order co ON co.order_no = rx.order_no 
        AND co.archived IS NOT TRUE
        AND co.deleted IS NOT TRUE
      LEFT JOIN form_careplan_order_item coi ON coi.id = p_order_item_id 
        AND coi.archived IS NOT TRUE 
        AND coi.deleted IS NOT TRUE 
      LEFT JOIN form_careplan_orderp_item opi ON opi.id = p_orderp_item_id 
        AND opi.archived IS NOT TRUE 
        AND opi.deleted IS NOT TRUE
      WHERE rx.rx_no = p_rx_no 
        AND rx.archived IS NOT TRUE 
        AND rx.deleted IS NOT TRUE
    )
    SELECT
      CASE WHEN p_is_test IS TRUE THEN 'Yes'::text ELSE NULL::text END AS is_test,
      p_transaction_code::text AS transaction_code,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.rx_svc_no_ref_qualifier::text
        WHEN p_transaction_code IN ('B1', 'B3') THEN '1'::text
        ELSE '2'::text
      END AS rx_svc_no_ref_qualifier,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.rx_svc_no::text
        ELSE COALESCE(p_rx_no, lpad(floor(random() * ********** + **********)::text, 12, '00'))::text
      END AS rx_svc_no,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.fill_number::integer
        ELSE COALESCE(p_fill_number, 1)::integer
      END AS fill_number,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.sched_rx_no::text
        ELSE oi.sched_rx_no::text
      END AS sched_rx_no,
      pt.id::integer AS patient_id,
      oi.order_id::integer AS order_id,
      p_insurance_id::integer AS insurance_id,
      p_payer_id::integer AS payer_id,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.product_id::integer
        WHEN p_transaction_code IN ('B1', 'B3') THEN inv.id::integer
        ELSE NULL::integer
      END AS product_id,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.service_id::integer
        WHEN p_transaction_code IN ('S1', 'S3') THEN inv.id::integer
        ELSE NULL::integer
      END AS service_id,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.prod_svc_id_qualifier::text
        WHEN p_transaction_code IN ('B1', 'B3') THEN 
          CASE 
            WHEN p_is_noncmpd_treated_as_cmpd THEN '00'::text
            WHEN p_is_cmpd_claim THEN
              CASE 
                WHEN ins.compound_qualifier_id = '09' AND LENGTH(inv.hcpc_code) > 0 THEN ins.compound_qualifier_id::text
                WHEN ins.compound_qualifier_id = '32' AND LENGTH(inv.gcn_seqno) > 0 THEN ins.compound_qualifier_id::text
                WHEN ins.compound_qualifier_id IN ('36','03') AND LENGTH(inv.ndc) > 0 THEN ins.compound_qualifier_id::text
                ELSE COALESCE(ins.compound_qualifier_id, '00')::text
              END
            ELSE 
              CASE 
                WHEN ins.noncompound_drug_qualifier_id = '00' THEN ins.noncompound_drug_qualifier_id::text
                WHEN ins.noncompound_drug_qualifier_id = '09' AND LENGTH(inv.hcpc_code) > 0 THEN ins.noncompound_drug_qualifier_id::text
                WHEN ins.noncompound_drug_qualifier_id = '32' AND LENGTH(inv.gcn_seqno) > 0 THEN ins.noncompound_drug_qualifier_id::text
                WHEN ins.noncompound_drug_qualifier_id IN ('36','03') AND LENGTH(inv.ndc) > 0 THEN ins.noncompound_drug_qualifier_id::text
                WHEN ins.noncompound_drug_qualifier_id = '01' AND LENGTH(inv.upc) > 0 THEN ins.noncompound_drug_qualifier_id::text
                WHEN ins.noncompound_drug_qualifier_id = '09' AND LENGTH(inv.hcpc_code) > 0 THEN ins.noncompound_drug_qualifier_id::text
                WHEN ins.noncompound_drug_qualifier_id = '34' AND LENGTH(inv.upin) > 0 THEN ins.noncompound_drug_qualifier_id::text
                ELSE '03'::text
              END
          END
        ELSE '09'::text
      END AS prod_svc_id_qualifier,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.prod_svc_id::text
        WHEN p_transaction_code IN ('B1', 'B3') THEN
          CASE
            WHEN p_is_noncmpd_treated_as_cmpd THEN '00000000000'::text
            WHEN p_is_cmpd_claim THEN
              CASE 
                WHEN ins.compound_qualifier_id = '00' THEN NULL::text
                WHEN ins.compound_qualifier_id = '09' AND LENGTH(inv.hcpc_code) > 0 THEN inv.hcpc_code::text
                WHEN ins.compound_qualifier_id = '32' AND LENGTH(inv.gcn_seqno) > 0 THEN inv.gcn_seqno::text
                WHEN ins.compound_qualifier_id IN ('36','03') AND LENGTH(inv.ndc) > 0 THEN inv.ndc::text
                ELSE '00000000000'::text
              END
            ELSE
              CASE
                WHEN ins.noncompound_drug_qualifier_id = '00' THEN NULL::text
                WHEN ins.noncompound_drug_qualifier_id = '09' AND LENGTH(inv.hcpc_code) > 0 THEN inv.hcpc_code::text
                WHEN ins.noncompound_drug_qualifier_id = '32' AND LENGTH(inv.gcn_seqno) > 0 THEN inv.gcn_seqno::text
                WHEN ins.noncompound_drug_qualifier_id IN ('36','03') AND LENGTH(inv.ndc) > 0 THEN inv.ndc::text
                WHEN ins.noncompound_drug_qualifier_id = '03' AND LENGTH(inv.ndc) > 0 THEN inv.ndc::text
                WHEN ins.noncompound_drug_qualifier_id = '01' AND LENGTH(inv.upc) > 0 THEN inv.upc::text
                WHEN ins.noncompound_drug_qualifier_id = '09' AND LENGTH(inv.hcpc_code) > 0 THEN inv.hcpc_code::text
                WHEN ins.noncompound_drug_qualifier_id = '34' AND LENGTH(inv.upin) > 0 THEN inv.upin::text
                ELSE inv.ndc::text
              END
          END
        ELSE
          inv.hcpc_code::text
      END AS prod_svc_id,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.quantity_dispensed::numeric
        ELSE COALESCE(p_charge_quantity, 1)::numeric
      END AS quantity_dispensed,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.unit_of_measure::text
        WHEN p_is_noncmpd_treated_as_cmpd THEN
          CASE
            WHEN ins.send_uom = 'Y' THEN 'EA'::text
            ELSE NULL::text
          END
        WHEN ins.send_uom = 'Y' AND p_transaction_code IN ('B1', 'B3') THEN   
          CASE
            WHEN p_is_noncmpd_treated_as_cmpd THEN 'EA'::text
            WHEN inv.billing_unit_id = 'each' THEN 'EA'::text
            WHEN inv.billing_unit_id = 'gram' THEN 'GM'::text
            WHEN inv.billing_unit_id = 'mL' THEN 'ML'::text
            ELSE 'EA'::text
          END
        ELSE NULL::text
      END AS unit_of_measure,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.day_supply::integer
        ELSE p_day_supply::integer
      END AS day_supply,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.number_of_refills_authorized::integer
        WHEN oi.refill_tracking = 'Refills' THEN 
          CASE 
            WHEN COALESCE(oi.refills, 0) < (p_fill_number - 1) THEN (p_fill_number - 1)::integer
            ELSE COALESCE(oi.refills, 0)::integer
          END
        WHEN oi.doses_to_prep > 0 AND oi.doses_allowed > 0 THEN 
          CASE 
            WHEN CEIL(oi.doses_allowed / oi.doses_to_prep) < (p_fill_number - 1) THEN (p_fill_number - 1)::integer
            ELSE CEIL(oi.doses_allowed / oi.doses_to_prep)::integer
          END
        ELSE 0::integer
      END AS number_of_refills_authorized,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.daw_code::text
        WHEN p_transaction_code IN ('B1', 'B3') THEN COALESCE(oi.daw_code, '0')::text
        ELSE NULL::text
      END AS daw_code,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN TO_CHAR(cd.date_rx_written, 'MM/DD/YYYY')::text
        ELSE TO_CHAR(COALESCE(oi.written_date, oi.start_date, p_date_of_service, CURRENT_DATE), 'MM/DD/YYYY')::text
      END AS date_rx_written,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.quantity_prescribed::numeric
        ELSE COALESCE(p_charge_quantity, 1)::numeric
      END AS quantity_prescribed,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.admin_route::text
        WHEN p_transaction_code IN ('B1', 'B3') THEN rt.snomed_code::text
        ELSE NULL::text
      END AS admin_route,
      pa.id::integer AS prior_auth_id,
      CASE
        WHEN pa.id IS NOT NULL AND LENGTH(pa.number) > 0 THEN ins.default_pa_type_id::text
        ELSE NULL::text
      END AS prior_auth_type,
      pa.number::text AS prior_auth_number,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.compound_code::text
        WHEN p_transaction_code IN ('B1', 'B3') THEN 
          CASE
            WHEN p_is_cmpd_claim THEN COALESCE(p_compound_code,'2')::text
            WHEN p_is_noncmpd_treated_as_cmpd THEN '0'::text
            ELSE '1'::text
          END
        ELSE '1'::text
      END AS compound_code,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.compound_type::text
        WHEN LENGTH(p_compound_type) > 0 THEN p_compound_type::text
        WHEN LENGTH(oi.compound_type) > 0 AND inv.type = 'Compound' THEN oi.compound_type::text
        ELSE NULL::text
      END AS compound_type,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.rx_origin_code::text
        WHEN p_transaction_code IN ('B1', 'B3') THEN COALESCE(oi.rx_origin_id, ins.default_origin_id, '4')::text
        ELSE NULL::text
      END AS rx_origin_code,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.sub_clar_code::text[]
        WHEN inv.type = 'Compound' AND COALESCE(ins.compound_sub_clarification_code, 'No') = 'Yes' AND oi.is_340b IS TRUE THEN ARRAY['8', '20']::text[]
        WHEN inv.type = 'Compound' AND COALESCE(ins.compound_sub_clarification_code, 'No') = 'Yes' THEN ARRAY['8']::text[]
        WHEN oi.is_340b IS TRUE THEN ARRAY['20']::text[]
        ELSE NULL::text[]
      END AS sub_clar_code,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN
          CASE
            WHEN LENGTH(ins.ncpdp_default_other_coverage_code) > 0 THEN ins.ncpdp_default_other_coverage_code::text
            WHEN cd.occ IS NOT NULL THEN cd.occ::text
            WHEN ins.type_id = 'COPAY' THEN '8'::text
            WHEN ins.type_id = 'PAP' THEN '99'::text
            WHEN ins.type_id = 'FOUND' THEN '99'::text
            WHEN is_medicare_part_d(p_payer_id) THEN '2'::text
            ELSE '2'::text
          END
        ELSE NULL::text
      END AS other_coverage_code,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN 'Yes'::text
        ELSE NULL::text
      END AS is_parent_claim,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.sp_pk_indicator::text
        WHEN p_transaction_code IN ('B1', 'B3') THEN COALESCE(inv.sp_pk_indicator, '0')::text
        ELSE NULL::text
      END AS sp_pk_indicator,
      ins.default_los_id::text AS default_los_id,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.pt_assign_indicator::text
        ELSE 'Y'::text
      END AS pt_assign_indicator,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.pharmacy_service_type::text
        ELSE COALESCE(ins.default_service_id, '08')::text
      END AS pharmacy_service_type,
      CASE
        WHEN p_parent_claim_no IS NOT NULL THEN cd.pmc::text[]
        ELSE NULL::text[]
      END AS pmc
    INTO v_result
    FROM form_patient pt
    INNER JOIN form_inventory inv ON inv.id = p_inventory_id 
      AND inv.archived IS NOT TRUE 
      AND inv.deleted IS NOT TRUE
    LEFT JOIN order_info oi ON TRUE
    LEFT JOIN form_list_route rt ON rt.code = COALESCE(p_route_id, oi.route_id) 
      AND rt.archived IS NOT TRUE 
      AND rt.deleted IS NOT TRUE
    LEFT JOIN form_patient_prior_auth pa ON pa.id = oi.drug_pa_id
      AND pa.archived IS NOT TRUE
      AND pa.deleted IS NOT TRUE
    CROSS JOIN insurance_info ins
    LEFT JOIN parent_claim_data cd ON TRUE
    WHERE pt.id = p_patient_id
      AND pt.archived IS NOT TRUE 
      AND pt.deleted IS NOT TRUE;
    -- Validate we got a result
    IF v_result IS NULL THEN
      INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
      ) VALUES (
        'Failed to build claim segment',
        'Validating result in build_claim_ncpdp_segment',
        'FUNCTION',
        current_schema(),
        'form_ncpdp',
        jsonb_build_object(
          'function_name', 'build_claim_ncpdp_segment',
          'patient_id', p_patient_id,
          'inventory_id', p_inventory_id,
          'site_id', p_site_id
        )
      );
      RAISE EXCEPTION 'Failed to build claim segment for patient_id: %', p_patient_id;
    END IF;
    -- Log successful completion
    PERFORM log_billing_function(
      'build_claim_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );
    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    -- Log error
    v_error_message := SQLERRM;
    
    -- Log to billing error log
    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_claim_ncpdp_segment',
      'FUNCTION',
      current_schema(),
      'form_ncpdp',
      jsonb_build_object(
        'function_name', 'build_claim_ncpdp_segment',
        'patient_id', p_patient_id,
        'inventory_id', p_inventory_id,
        'site_id', p_site_id
      )
    );

    -- Log to NCPDP function log
    PERFORM log_billing_function(
      'build_claim_ncpdp_segment'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;