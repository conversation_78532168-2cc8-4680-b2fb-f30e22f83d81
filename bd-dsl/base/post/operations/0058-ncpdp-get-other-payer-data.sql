
CREATE OR REPLACE FUNCTION get_other_payer_data(
    root_claim TEXT,
    p_patient_id INTEGER,
    p_insurance_id INTEGER
) RETURNS ncpdp_cob_opayer[] AS $BODY$
DECLARE
    v_start_time timestamp;
    v_execution_time interval;
    v_error_message text;
    v_params jsonb;
    v_result ncpdp_cob_opayer[];
BEGIN
    -- Record start time
    v_start_time := clock_timestamp();

    -- Build parameters JSON for logging
    v_params := jsonb_build_object(
        'root_claim', root_claim,
        'patient_id', p_patient_id,
        'insurance_id', p_insurance_id
    );

    BEGIN  -- Start exception block
        -- Log function call
        PERFORM log_billing_function(
            'get_other_payer_data'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            NULL::text,
            clock_timestamp() - v_start_time
        );

        -- Validate required parameters
        IF root_claim IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Root Claim # cannot be null',
                'Validating required parameters in get_other_payer_data',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                jsonb_build_object(
                    'function_name', 'get_other_payer_data',
                    'root_claim', root_claim
                )
            );
            RAISE EXCEPTION 'Root Claim # cannot be null';
        END IF;

        IF p_patient_id IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Patient ID cannot be null',
                'Validating required parameters in get_other_payer_data',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                jsonb_build_object(
                    'function_name', 'get_other_payer_data',
                    'root_claim', root_claim
                )
            );
            RAISE EXCEPTION 'Patient ID cannot be null';
        END IF;
        
        IF p_insurance_id IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Insurance ID cannot be null',
                'Validating required parameters in get_other_payer_data',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                jsonb_build_object(
                    'function_name', 'get_other_payer_data',
                    'root_claim', root_claim
                )
            );
            RAISE EXCEPTION 'Insurance ID cannot be null';
        END IF;
 
        -- Get claim hierarchy first
        WITH claim_hierarchy AS (
            SELECT * FROM get_claim_hierarchy(root_claim)
        ),
        -- Extract primary payer info
        primary_payer_info AS (
            SELECT 
                ch.insurance_id::integer as insurance_id,
                ch.payer_id::integer as payer_id,
                ch.other_coverage_type::text as other_coverage_type,
                ch.internal_control_number::text as internal_control_number,
                ch.other_id_qualifier::text as other_id_qualifier,
                ch.other_id::text as other_id,
                ch.other_date::text as other_date,
                CASE WHEN py.type_id = 'COPAY' THEN TRUE ELSE FALSE END as is_copay_card
            FROM claim_hierarchy ch
            INNER JOIN form_payer py ON py.id = ch.payer_id
            WHERE ch.depth = 0  -- Primary claim
            LIMIT 1
        ),
        -- Format other payer data - ensure we have at least the primary payer
        other_payers AS (
            SELECT 
                pp.insurance_id::integer as insurance_id,
                pp.payer_id::integer as payer_id,
                -- For copay cards, set the appropriate other_coverage_type
                CASE 
                    WHEN pp.is_copay_card THEN '99'::text  -- Other
                    ELSE COALESCE(pp.other_coverage_type, '01')::text  -- Default to Primary
                END as other_coverage_type,
                pp.internal_control_number::text as internal_control_number,
                pp.other_id_qualifier::text as other_id_qualifier,
                pp.other_id::text as other_id,
                pp.other_date::text as other_date
            FROM primary_payer_info pp
        ),
        final_other_payers AS (
            SELECT 
                p_patient_id::integer AS patient_id,
                p_insurance_id::integer AS pinsurance_id,
                py.insurance_id::integer AS insurance_id,
                py.payer_id::integer AS payer_id,
                py.other_coverage_type::text AS other_coverage_type,
                py.internal_control_number::text AS internal_control_number,
                py.other_id_qualifier::text AS other_id_qualifier,
                py.other_id::text AS other_id,
                py.other_date::text AS other_date
            FROM other_payers py
        )
        -- Create the final result as an array of the composite type
        SELECT ARRAY_AGG(resp::ncpdp_cob_opayer) INTO v_result
        FROM final_other_payers resp;

        -- If no results but we have a root claim, create a default entry
        IF (v_result IS NULL OR array_length(v_result, 1) = 0) AND root_claim IS NOT NULL THEN
            -- Find primary claim information
            WITH primary_claim AS (
                SELECT 
                    f.insurance_id::integer as insurance_id,
                    f.payer_id::integer as payer_id,
                    f.claim_no::text as claim_no,
                    p.bin::text as bin,
                    p.pcn::text as pcn,
                    CASE WHEN p.type_id = 'COPAY' THEN TRUE ELSE FALSE END as is_copay_card
                FROM form_ncpdp f
                JOIN form_payer p ON p.id = f.payer_id
                WHERE f.claim_no = root_claim
                  AND f.archived IS NOT TRUE
                  AND f.deleted IS NOT TRUE
                  AND p.archived IS NOT TRUE
                  AND p.deleted IS NOT TRUE
                LIMIT 1
            ),
            final_other_payers AS (
                SELECT 
                    p_patient_id::integer as patient_id,
                    p_insurance_id::integer as pinsurance_id,
                    pc.insurance_id::integer as insurance_id,
                    pc.payer_id::integer as payer_id,
                    CASE WHEN pc.is_copay_card THEN '99'::text ELSE '01'::text END as other_coverage_type,
                    pc.claim_no::text as internal_control_number,
                    '03'::text as other_id_qualifier,
                    pc.bin::text as other_id,
                    CURRENT_DATE::text as other_date
                FROM primary_claim pc
            )
            SELECT ARRAY_AGG(resp::ncpdp_cob_opayer) INTO v_result
            FROM final_other_payers resp;
        END IF;

        -- Log successful completion
        PERFORM log_billing_function(
            'get_other_payer_data'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            NULL::text,
            clock_timestamp() - v_start_time
        );

        RETURN v_result;

    EXCEPTION WHEN OTHERS THEN
        -- Log error
        v_error_message := SQLERRM;
        
        -- Log to billing error log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            v_error_message,
            'Exception in get_other_payer_data',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );

        -- Log to NCPDP function log
        PERFORM log_billing_function(
            'get_other_payer_data'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            v_error_message::text,
            clock_timestamp() - v_start_time
        );
        RAISE;
    END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;