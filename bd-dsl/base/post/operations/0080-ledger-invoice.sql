
-- Apply the transaction control trigger to all financial tables
CREATE OR R<PERSON>LACE FUNCTION ensure_transaction_integrity() RETURNS TRIGGER AS $$
DECLARE
    v_transaction_id TEXT;
    v_start_time TIMESTAMP;
    v_error_encountered BOOLEAN := FALSE;
    v_error_message TEXT;
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Generate a transaction identifier
    v_transaction_id := 'TX-' || to_char(now(), 'YYYYMMDD-HH24MISS-') || trunc(random() * 10000)::TEXT;
    v_start_time := clock_timestamp();
    
    -- Log the transaction start
    INSERT INTO billing_transaction_log (
        transaction_id,
        source_form,
        source_id,
        operation,
        status,
        start_time,
        details
    ) VALUES (
        v_transaction_id,
        TG_TABLE_NAME,
        NEW.id,
        TG_OP,
        'STARTED',
        v_start_time,
        jsonb_build_object(
            'user_id', COALESCE(NEW.updated_by, NEW.created_by),
            'table', TG_TABLE_NAME,
            'operation', TG_OP
        )
    );
    
    -- Execute the operation inside a transaction block
    BEGIN
        -- For updates to financial records, check if it's allowed
        IF TG_OP = 'UPDATE' AND TG_TABLE_NAME IN ('form_billing_invoice', 'form_careplan_delivery_tick',
                                                  'form_ledger_charge_line', 'form_billing_closing') THEN
            -- If record is already marked as void, prevent further modifications
            IF COALESCE(OLD.void, 'No') = 'Yes' AND NEW.id = OLD.id THEN
                -- Log the error
                INSERT INTO billing_error_log (
                    error_message,
                    error_context,
                    error_type,
                    schema_name,
                    table_name,
                    additional_details
                ) VALUES (
                    'Cannot modify a voided record',
                    'Transaction control',
                    'SYSTEM',
                    current_schema(),
                    TG_TABLE_NAME,
                    jsonb_build_object(
                        'record_id', NEW.id,
                        'transaction_id', v_transaction_id
                    )
                );
                
                RAISE EXCEPTION 'Cannot modify a voided record (ID: %)', OLD.id;
            END IF;
            
            IF OLD.close_no IS NOT NULL THEN
                -- First check the table condition separately
                IF TG_TABLE_NAME = 'form_billing_closing' THEN
                    -- Only check locked_period for this specific table
                    IF COALESCE(NEW.locked_period, 'No') = 'Yes' THEN
                        -- Skip the closed period check for locked periods
                        NULL; -- Do nothing
                    ELSIF check_closed_period(COALESCE(NEW.post_datetime, OLD.post_datetime)) THEN
                        -- Handle closed period error (same as your original code)
                        INSERT INTO billing_error_log (
                            error_message,
                            error_context,
                            error_type,
                            schema_name,
                            table_name,
                            additional_details
                        ) VALUES (
                            'Cannot modify a record in a closed period',
                            'Transaction control',
                            'SYSTEM',
                            current_schema(),
                            TG_TABLE_NAME,
                            jsonb_build_object(
                                'record_id', NEW.id,
                                'close_no', OLD.close_no,
                                'transaction_id', v_transaction_id
                            )
                        );
                        
                        RAISE EXCEPTION 'Cannot modify a record (ID: %) in a closed period (Close #: %)', 
                                        OLD.id, OLD.close_no;
                    END IF;
                ELSE
                    -- For all other tables, just do the closed period check
                    IF check_closed_period(COALESCE(NEW.post_datetime, OLD.post_datetime)) THEN
                        -- Handle closed period error (same code as above)
                        INSERT INTO billing_error_log (
                            error_message,
                            error_context,
                            error_type,
                            schema_name,
                            table_name,
                            additional_details
                        ) VALUES (
                            'Cannot modify a record in a closed period',
                            'Transaction control',
                            'SYSTEM',
                            current_schema(),
                            TG_TABLE_NAME,
                            jsonb_build_object(
                                'record_id', NEW.id,
                                'close_no', OLD.close_no,
                                'transaction_id', v_transaction_id
                            )
                        );
                        
                        RAISE EXCEPTION 'Cannot modify a record (ID: %) in a closed period (Close #: %)', 
                                        OLD.id, OLD.close_no;
                    END IF;
                END IF;
            ELSIF TG_OP = 'UPDATE' AND TG_TABLE_NAME IN ('form_billing_ar_transaction', 'form_billing_cash') THEN
                IF check_closed_period(COALESCE(NEW.post_datetime, OLD.post_datetime)) THEN
                    -- Handle closed period error (same code as above)
                    INSERT INTO billing_error_log (
                        error_message,
                        error_context,
                        error_type,
                        schema_name,
                        table_name,
                        additional_details
                    ) VALUES (
                        'Cannot modify a record in a closed period',
                        'Transaction control',
                        'SYSTEM',
                        current_schema(),
                        TG_TABLE_NAME,
                        jsonb_build_object(
                            'record_id', NEW.id,
                            'close_no', OLD.close_no,
                            'transaction_id', v_transaction_id
                        )
                    );
                    
                    RAISE EXCEPTION 'Cannot modify a record (ID: %) in a closed period (Close #: %)', 
                                    OLD.id, OLD.close_no;
                    END IF;

                IF COALESCE(OLD.zeroed, 'No') = 'Yes' THEN
                        INSERT INTO billing_error_log (
                            error_message,
                            error_context,
                            error_type,
                            schema_name,
                            table_name,
                            additional_details
                        ) VALUES (
                            'Cannot modify a zeroed record',
                            'Transaction control',
                            'SYSTEM',
                            current_schema(),
                            TG_TABLE_NAME,
                            jsonb_build_object(
                                'record_id', NEW.id,
                                'table', TG_TABLE_NAME
                            )
                        );
                    RAISE EXCEPTION 'Cannot modify a zeroed record';
                ELSIF (COALESCE(OLD.zeroed, 'No') IS NULL AND COALESCE(NEW.zeroed, 'No') IS NULL) THEN
                    INSERT INTO billing_error_log (
                        error_message,
                        error_context,
                        error_type,
                        schema_name,
                        table_name,
                        additional_details
                    ) VALUES (
                        'Cannot modify a financed record unless it is zeroed',
                        'Transaction control',
                        'SYSTEM',
                        current_schema(),
                        TG_TABLE_NAME,
                        jsonb_build_object(
                            'record_id', NEW.id,
                            'table', TG_TABLE_NAME
                        )
                    );
                    RAISE EXCEPTION 'Cannot modify a financed record unless it is zeroed';
                END IF;
                RETURN NEW;
            ELSE

                IF check_closed_period(NEW.post_datetime) THEN
                        -- Handle closed period error (same code as above)
                        INSERT INTO billing_error_log (
                            error_message,
                            error_context,
                            error_type,
                            schema_name,
                            table_name,
                            additional_details
                        ) VALUES (
                            'Cannot modify a record in a closed period',
                            'Transaction control',
                            'SYSTEM',
                            current_schema(),
                            TG_TABLE_NAME,
                            jsonb_build_object(
                                'record_id', NEW.id,
                                'close_no', OLD.close_no,
                                'transaction_id', v_transaction_id
                            )
                        );
                        
                        RAISE EXCEPTION 'Cannot modify a record (ID: %) in a closed period (Close #: %)', 
                                        OLD.id, OLD.close_no;
                    END IF;

            END IF;
        END IF;
        
        -- Let the original operation proceed
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        v_error_encountered := TRUE;
        v_error_message := SQLERRM;
        
        -- Log the error
        INSERT INTO billing_transaction_log (
            transaction_id,
            source_form,
            source_id,
            operation,
            status,
            start_time,
            end_time,
            error_message,
            details
        ) VALUES (
            v_transaction_id,
            TG_TABLE_NAME,
            NEW.id,
            TG_OP,
            'FAILED',
            v_start_time,
            clock_timestamp(),
            v_error_message,
            jsonb_build_object(
                'user_id', COALESCE(NEW.updated_by, NEW.created_by),
                'table', TG_TABLE_NAME,
                'operation', TG_OP,
                'error', v_error_message
            )
        );
        
        -- Re-raise the exception
        RAISE;
    END;
    
    -- If no error occurred, log successful completion
    IF NOT v_error_encountered THEN
        UPDATE billing_transaction_log
        SET status = 'COMPLETED',
            end_time = clock_timestamp()
        WHERE transaction_id = v_transaction_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE TRIGGER trg_ensure_transaction_integrity_invoice
BEFORE INSERT OR UPDATE ON form_billing_invoice
FOR EACH ROW EXECUTE FUNCTION ensure_transaction_integrity();

CREATE OR REPLACE TRIGGER trg_ensure_transaction_integrity_ar_transaction
BEFORE INSERT OR UPDATE ON form_billing_ar_transaction
FOR EACH ROW EXECUTE FUNCTION ensure_transaction_integrity();

CREATE OR REPLACE TRIGGER trg_ensure_transaction_integrity_billing_cash
BEFORE INSERT OR UPDATE ON form_billing_cash
FOR EACH ROW EXECUTE FUNCTION ensure_transaction_integrity();

CREATE OR REPLACE TRIGGER trg_ensure_transaction_integrity_closing
BEFORE INSERT OR UPDATE ON form_billing_closing
FOR EACH ROW EXECUTE FUNCTION ensure_transaction_integrity();

-- ===================================
-- BILLING INVOICE TRIGGERS
-- ===================================
-- Addition to process_invoice_revenue function to handle automatic cash posting
-- for previously processed NCPDP claims

CREATE OR REPLACE FUNCTION process_invoice_revenue() RETURNS TRIGGER AS $$
DECLARE
    v_charge_record RECORD;
    v_creator INTEGER;
    v_success BOOLEAN := false;
    v_last_response_id INTEGER;
BEGIN
    -- Log function entry
    RAISE LOG 'Entering process_invoice_revenue for invoice_no: %, id: %', NEW.invoice_no, NEW.id;
    
    -- Skip if conditions aren't met
    IF (COALESCE(NEW.revenue_accepted, 'No') <> 'Yes' OR 
        COALESCE(NEW.void, 'No') = 'Yes' OR 
        COALESCE(NEW.zeroed, 'No') = 'Yes' OR 
        COALESCE(NEW.archived, FALSE) = TRUE OR 
        COALESCE(NEW.deleted, FALSE) = TRUE OR 
        COALESCE(NEW.delivery_ticket_id, 0) = 0 OR
        COALESCE(NEW.revenue_accepted_posted, 'No') = 'Yes') THEN
        RAISE LOG 'Skipping process_invoice_revenue, conditions not met for invoice_no: %', NEW.invoice_no;
        RETURN NEW;
    END IF;
    
    -- Check if we're in a closed period
    IF check_closed_period(NEW.post_datetime::timestamp) THEN
        -- Log error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Cannot post revenue in a closed accounting period',
            'Error processing invoice revenue',
            'TRIGGER',
            current_schema(),
            'billing_invoice',
            jsonb_build_object(
                'function_name', 'process_invoice_revenue',
                'invoice_no', NEW.invoice_no,
                'post_datetime', NEW.post_datetime
            )
        );
        
        RAISE EXCEPTION 'Cannot post revenue in a closed accounting period for invoice %', NEW.invoice_no;
    END IF;
    
    -- Determine who created/updated this record
    v_creator := COALESCE(NEW.updated_by, NEW.created_by);
    RAISE LOG 'Processing invoice revenue for invoice_no: %, creator: %', NEW.invoice_no, v_creator;
    
    BEGIN
        -- Process each charge line
        RAISE LOG 'Processing charge lines for invoice_no: %', NEW.invoice_no;
        PERFORM set_config('clara.disable_invoice_trigger', 'on', false);
        PERFORM set_config('clara.prevent_locked_checks', 'on', false);

        FOR v_charge_record IN 
            SELECT cl.id, cl.expected, cl.charge_no
            FROM form_ledger_charge_line cl
            WHERE cl.invoice_no = NEW.invoice_no
            AND COALESCE(cl.void, 'No') <> 'Yes'
            AND COALESCE(cl.zeroed, 'No') <> 'Yes'
            AND cl.archived IS NOT TRUE
            AND cl.deleted IS NOT TRUE
            FOR UPDATE
        LOOP
            RAISE LOG 'Processing charge_line: %, expected: %', v_charge_record.charge_no, v_charge_record.expected;
            
            -- Update the charge line to mark as posted
            UPDATE form_ledger_charge_line
            SET
                revenue_accepted_posted = 'Yes',
                post_datetime = COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id))
            WHERE id = v_charge_record.id;
        END LOOP;

        IF NEW.billing_method_id = 'ncpdp' THEN
            SELECT resp.id
            INTO v_last_response_id
            FROM form_ncpdp_response resp
            INNER JOIN form_ncpdp ncpdp ON ncpdp.claim_no = resp.claim_no AND
            ncpdp.invoice_no = NEW.invoice_no
            AND ncpdp.archived IS NOT TRUE
            AND ncpdp.deleted IS NOT TRUE
            ORDER BY resp.id DESC
            LIMIT 1;

            IF v_last_response_id IS NOT NULL THEN
                PERFORM process_ncpdp_payment_posting_core(v_last_response_id, FALSE);
            END IF;
        END IF;

        PERFORM set_config('clara.disable_invoice_trigger', 'off', false);
        PERFORM set_config('clara.prevent_locked_checks', 'off', false);

        -- Mark invoice as posted
        NEW.revenue_accepted_posted := 'Yes';
        NEW.post_datetime := COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id));
        NEW.applied_datetime := get_site_timestamp(NEW.site_id);

        v_success := true;

    EXCEPTION WHEN OTHERS THEN
        -- Log the error
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Transaction failed during invoice revenue posting',
            'TRIGGER',
            current_schema(),
            'billing_invoice',
            jsonb_build_object(
                'function_name', 'process_invoice_revenue',
                'invoice_no', NEW.invoice_no,
                'success_flag', v_success
            )
        );
        
        RAISE; -- Re-throw the exception to rollback the transaction
    END;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Trigger for invoice revenue posting
CREATE OR REPLACE TRIGGER trg_billing_invoice_revenue
BEFORE UPDATE ON form_billing_invoice
FOR EACH ROW
WHEN (
    (COALESCE(NEW.revenue_accepted, 'No') = 'Yes' AND COALESCE(OLD.revenue_accepted, 'No') IS NULL) OR
    (NEW.delivery_ticket_id IS NOT NULL AND OLD.delivery_ticket_id IS NULL)
)
EXECUTE FUNCTION process_invoice_revenue();
-- Function to get account_id based on patient or payer

CREATE OR REPLACE FUNCTION get_account_id(p_patient_id INTEGER, p_payer_id INTEGER) RETURNS INTEGER AS $$
DECLARE
    v_account_id INTEGER;
BEGIN

    IF p_payer_id = 1 THEN
        SELECT id INTO v_account_id FROM form_billing_account
        WHERE type = 'Patient'
        AND patient_id = p_patient_id
        AND archived IS NOT TRUE
        AND deleted IS NOT TRUE
        LIMIT 1;
    ELSE
        SELECT id INTO v_account_id FROM form_billing_account
        WHERE type = 'Payer'
        AND payer_id = p_payer_id
        AND archived IS NOT TRUE
        AND deleted IS NOT TRUE
        LIMIT 1;
    END IF;
    
    IF v_account_id IS NULL THEN
        RAISE EXCEPTION 'No account found for % with ID %', p_type, COALESCE(p_patient_id, p_payer_id);
    END IF;
    
    RETURN v_account_id;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- ===================================
-- 2. TRANSACTION CONTROL
-- ===================================

-- Function to verify balanced ledger entries at transaction commit
CREATE OR REPLACE FUNCTION verify_balanced_ledger_entries() RETURNS TRIGGER AS $$
DECLARE
    debit_sum DECIMAL;
    credit_sum DECIMAL;
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Check for each source_id/source_form combination
    SELECT SUM(debit), SUM(credit) 
    INTO debit_sum, credit_sum
    FROM form_ledger_finance
    WHERE source_id = NEW.source_id AND source_form = NEW.source_form;
    
    IF ROUND(debit_sum::numeric, 2) != ROUND(credit_sum::numeric, 2) THEN
        -- Log error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Unbalanced ledger entries detected',
            'Ledger balance verification',
            'CONSTRAINT',
            current_schema(),
            'form_ledger_finance',
            jsonb_build_object(
                'source_form', NEW.source_form,
                'source_id', NEW.source_id,
                'debit_sum', debit_sum,
                'credit_sum', credit_sum
            )
        );
        
        RAISE EXCEPTION 'Unbalanced ledger entries: debit=%, credit=% for %/ID=%', 
                        debit_sum, credit_sum, NEW.source_form, NEW.source_id;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Constraint trigger to verify balanced entries at transaction commit
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.triggers
        WHERE trigger_name = 'trg_verify_ledger_balance'
        AND event_object_table = 'form_ledger_finance'
    ) THEN
        CREATE CONSTRAINT TRIGGER trg_verify_ledger_balance
        AFTER INSERT OR UPDATE ON form_ledger_finance
        DEFERRABLE INITIALLY DEFERRED
        FOR EACH ROW EXECUTE FUNCTION verify_balanced_ledger_entries();
    END IF;
END $$;

-- Function to wrap all financial operations in proper transaction control
CREATE OR REPLACE FUNCTION ensure_transaction_integrity() RETURNS TRIGGER AS $$
DECLARE
    v_transaction_id TEXT;
    v_start_time TIMESTAMP;
    v_error_encountered BOOLEAN := FALSE;
    v_error_message TEXT;
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Generate a transaction identifier
    v_transaction_id := 'TX-' || to_char(now(), 'YYYYMMDD-HH24MISS-') || trunc(random() * 10000)::TEXT;
    v_start_time := clock_timestamp();
    
    -- Log the transaction start
    INSERT INTO billing_transaction_log (
        transaction_id,
        source_form,
        source_id,
        operation,
        status,
        start_time,
        details
    ) VALUES (
        v_transaction_id,
        TG_TABLE_NAME,
        NEW.id,
        TG_OP,
        'STARTED',
        v_start_time,
        jsonb_build_object(
            'user_id', COALESCE(NEW.updated_by, NEW.created_by),
            'table', TG_TABLE_NAME,
            'operation', TG_OP
        )
    );
    
    -- Execute the operation inside a transaction block
    BEGIN
        -- For updates to financial records, check if it's allowed
        IF TG_OP = 'UPDATE' AND TG_TABLE_NAME IN ('form_billing_invoice', 'form_careplan_delivery_tick',
                                                  'form_ledger_charge_line', 'form_billing_closing') THEN
            -- If record is already marked as void, prevent further modifications
            IF OLD.void = 'Yes' AND NEW.id = OLD.id THEN
                -- Log the error
                INSERT INTO billing_error_log (
                    error_message,
                    error_context,
                    error_type,
                    schema_name,
                    table_name,
                    additional_details
                ) VALUES (
                    'Cannot modify a voided record',
                    'Transaction control',
                    'SYSTEM',
                    current_schema(),
                    TG_TABLE_NAME,
                    jsonb_build_object(
                        'record_id', NEW.id,
                        'transaction_id', v_transaction_id
                    )
                );
                
                RAISE EXCEPTION 'Cannot modify a voided record (ID: %)', OLD.id;
            END IF;
            
            IF OLD.close_no IS NOT NULL THEN
                -- First check the table condition separately
                IF TG_TABLE_NAME = 'form_billing_closing' THEN
                    -- Only check locked_period for this specific table
                    IF COALESCE(NEW.locked_period, 'No') = 'Yes' THEN
                        -- Skip the closed period check for locked periods
                        NULL; -- Do nothing
                    ELSIF check_closed_period(COALESCE(NEW.post_datetime, OLD.post_datetime)) THEN
                        -- Handle closed period error (same as your original code)
                        INSERT INTO billing_error_log (
                            error_message,
                            error_context,
                            error_type,
                            schema_name,
                            table_name,
                            additional_details
                        ) VALUES (
                            'Cannot modify a record in a closed period',
                            'Transaction control',
                            'SYSTEM',
                            current_schema(),
                            TG_TABLE_NAME,
                            jsonb_build_object(
                                'record_id', NEW.id,
                                'close_no', OLD.close_no,
                                'transaction_id', v_transaction_id
                            )
                        );
                        
                        RAISE EXCEPTION 'Cannot modify a record (ID: %) in a closed period (Close #: %)', 
                                        OLD.id, OLD.close_no;
                    END IF;
                ELSE
                    -- For all other tables, just do the closed period check
                    IF check_closed_period(COALESCE(NEW.post_datetime, OLD.post_datetime)) THEN
                        -- Handle closed period error (same code as above)
                        INSERT INTO billing_error_log (
                            error_message,
                            error_context,
                            error_type,
                            schema_name,
                            table_name,
                            additional_details
                        ) VALUES (
                            'Cannot modify a record in a closed period',
                            'Transaction control',
                            'SYSTEM',
                            current_schema(),
                            TG_TABLE_NAME,
                            jsonb_build_object(
                                'record_id', NEW.id,
                                'close_no', OLD.close_no,
                                'transaction_id', v_transaction_id
                            )
                        );
                        
                        RAISE EXCEPTION 'Cannot modify a record (ID: %) in a closed period (Close #: %)', 
                                        OLD.id, OLD.close_no;
                    END IF;
                END IF;
            ELSIF TG_OP = 'UPDATE' AND TG_TABLE_NAME IN ('form_billing_ar_transaction', 'form_billing_cash') THEN
                IF check_closed_period(COALESCE(NEW.post_datetime, OLD.post_datetime)) THEN
                    -- Handle closed period error (same code as above)
                    INSERT INTO billing_error_log (
                        error_message,
                        error_context,
                        error_type,
                        schema_name,
                        table_name,
                        additional_details
                    ) VALUES (
                        'Cannot modify a record in a closed period',
                        'Transaction control',
                        'SYSTEM',
                        current_schema(),
                        TG_TABLE_NAME,
                        jsonb_build_object(
                            'record_id', NEW.id,
                            'close_no', OLD.close_no,
                            'transaction_id', v_transaction_id
                        )
                    );
                    
                    RAISE EXCEPTION 'Cannot modify a record (ID: %) in a closed period (Close #: %)', 
                                    OLD.id, OLD.close_no;
                    END IF;

                IF COALESCE(OLD.zeroed, 'No') = 'Yes' THEN
                        INSERT INTO billing_error_log (
                            error_message,
                            error_context,
                            error_type,
                            schema_name,
                            table_name,
                            additional_details
                        ) VALUES (
                            'Cannot modify a zeroed record',
                            'Transaction control',
                            'SYSTEM',
                            current_schema(),
                            TG_TABLE_NAME,
                            jsonb_build_object(
                                'record_id', NEW.id,
                                'table', TG_TABLE_NAME
                            )
                        );
                    RAISE EXCEPTION 'Cannot modify a zeroed record';
                ELSIF (COALESCE(OLD.zeroed, 'No') IS NULL AND COALESCE(NEW.zeroed, 'No') IS NULL) THEN
                    INSERT INTO billing_error_log (
                        error_message,
                        error_context,
                        error_type,
                        schema_name,
                        table_name,
                        additional_details
                    ) VALUES (
                        'Cannot modify a financed record unless it is zeroed',
                        'Transaction control',
                        'SYSTEM',
                        current_schema(),
                        TG_TABLE_NAME,
                        jsonb_build_object(
                            'record_id', NEW.id,
                            'table', TG_TABLE_NAME
                        )
                    );
                    RAISE EXCEPTION 'Cannot modify a financed record unless it is zeroed';
                END IF;
                RETURN NEW;
            ELSE

                IF check_closed_period(NEW.post_datetime) THEN
                        -- Handle closed period error (same code as above)
                        INSERT INTO billing_error_log (
                            error_message,
                            error_context,
                            error_type,
                            schema_name,
                            table_name,
                            additional_details
                        ) VALUES (
                            'Cannot modify a record in a closed period',
                            'Transaction control',
                            'SYSTEM',
                            current_schema(),
                            TG_TABLE_NAME,
                            jsonb_build_object(
                                'record_id', NEW.id,
                                'close_no', OLD.close_no,
                                'transaction_id', v_transaction_id
                            )
                        );
                        
                        RAISE EXCEPTION 'Cannot modify a record (ID: %) in a closed period (Close #: %)', 
                                        OLD.id, OLD.close_no;
                    END IF;

            END IF;
        END IF;
        
        -- Let the original operation proceed
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        v_error_encountered := TRUE;
        v_error_message := SQLERRM;
        
        -- Log the error
        INSERT INTO billing_transaction_log (
            transaction_id,
            source_form,
            source_id,
            operation,
            status,
            start_time,
            end_time,
            error_message,
            details
        ) VALUES (
            v_transaction_id,
            TG_TABLE_NAME,
            NEW.id,
            TG_OP,
            'FAILED',
            v_start_time,
            clock_timestamp(),
            v_error_message,
            jsonb_build_object(
                'user_id', COALESCE(NEW.updated_by, NEW.created_by),
                'table', TG_TABLE_NAME,
                'operation', TG_OP,
                'error', v_error_message
            )
        );
        
        -- Re-raise the exception
        RAISE;
    END;
    
    -- If no error occurred, log successful completion
    IF NOT v_error_encountered THEN
        UPDATE billing_transaction_log
        SET status = 'COMPLETED',
            end_time = clock_timestamp()
        WHERE transaction_id = v_transaction_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION process_invoice_zero() RETURNS TRIGGER AS $$
DECLARE
    v_creator INTEGER;
    v_success BOOLEAN := false;
    v_has_open_children BOOLEAN := false;
    v_ledger_record RECORD;
    v_chargeline_record RECORD;
    v_account_id INTEGER;

    v_rx_guid uuid := gen_random_uuid();
    v_supply_guid uuid := gen_random_uuid();
    v_dme_guid uuid := gen_random_uuid();
    v_pa_guid uuid := gen_random_uuid();
    v_medid_guid uuid := gen_random_uuid();
    v_old_charge_no uuid;
BEGIN
    -- Log function entry
    RAISE LOG 'Entering process_invoice_zero for invoice_no: %, id: %', NEW.invoice_no, NEW.id;
    
    -- Skip if conditions aren't met
    IF (COALESCE(NEW.zeroed, 'No') <> 'Yes' OR 
        COALESCE(OLD.zeroed, 'No') = 'Yes' OR
        COALESCE(NEW.revenue_accepted_posted, 'No') <> 'Yes') THEN
        RAISE LOG 'Skipping process_invoice_zero, conditions not met for invoice_no: %', 
                 COALESCE(NEW.invoice_no, 'NULL');
        RETURN NEW;
    END IF;
    
    -- Check if we're in a closed period
    IF check_closed_period(NEW.post_datetime) THEN
        -- Log error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Cannot zero invoice in a closed accounting period',
            'Error zeroing invoice',
            'TRIGGER',
            current_schema(),
            'billing_invoice',
            jsonb_build_object(
                'function_name', 'process_invoice_zero',
                'invoice_no', NEW.invoice_no,
                'post_datetime', NEW.post_datetime
            )
        );
        
        RAISE EXCEPTION 'Cannot zero invoice in a closed accounting period for invoice %', NEW.invoice_no;
    END IF;
    
    -- Lock the invoice row exclusively to prevent concurrent modifications
    PERFORM id FROM form_billing_invoice 
    WHERE id = NEW.id 
    FOR UPDATE;
    
    SELECT EXISTS (
        SELECT 1 
        FROM form_billing_invoice bi
        WHERE bi.parent_invoice_no = NEW.invoice_no
        AND COALESCE(bi.void, 'No') <> 'Yes'
        AND COALESCE(bi.zeroed, 'No') <> 'Yes'
    ) INTO v_has_open_children;
 
    -- Check if this invoice has non-voided, non-zeroed children (for master invoices only)
    IF v_has_open_children THEN
        -- Log error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Cannot zero master invoice with active children',
            'Error zeroing invoice',
            'TRIGGER',
            current_schema(),
            'billing_invoice',
            jsonb_build_object(
                'function_name', 'process_invoice_zero',
                'invoice_no', NEW.invoice_no,
                'master_invoice_no', NEW.master_invoice_no
            )
        );
        
        RAISE EXCEPTION 'Cannot zero master invoice % while it has active child invoices. Zero or void them first.', NEW.invoice_no;
    END IF;
    
    -- Determine who created/updated this record
    v_creator := COALESCE(NEW.updated_by, NEW.created_by);
    RAISE LOG 'Zeroing invoice_no: %, by user: %', 
             NEW.invoice_no, v_creator;
    BEGIN
        -- Get appropriate account ID
        IF NEW.payer_id = 1 THEN
            -- Patient account
            SELECT id INTO v_account_id 
            FROM form_billing_account
            WHERE type = 'Patient'
            AND patient_id = NEW.patient_id
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE
            LIMIT 1;
        ELSE
            -- Payer account
            SELECT id INTO v_account_id 
            FROM form_billing_account
            WHERE type = 'Payer'
            AND payer_id = NEW.payer_id
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE
            LIMIT 1;
        END IF;
        
        -- Check if account_id exists
        IF v_account_id IS NULL THEN
            -- Log error
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Account not found for invoice',
                'Error zeroing invoice',
                'TRIGGER',
                current_schema(),
                'billing_invoice',
                jsonb_build_object(
                    'function_name', 'process_invoice_zero',
                    'invoice_no', NEW.invoice_no,
                    'patient_id', NEW.patient_id,
                    'payer_id', NEW.payer_id
                )
            );
            
            RAISE EXCEPTION 'Account not found for invoice %', NEW.invoice_no;
        END IF;
        

        -- Check each charge line to ensure it has ledger entries
        FOR v_ledger_record IN 
            SELECT * FROM form_ledger_finance lf
            WHERE invoice_no = NEW.invoice_no
            AND source_form = 'billing_invoice'
            AND reversal_for_id IS NULL
            AND NOT EXISTS (
                SELECT 1 FROM form_ledger_finance lfr
                WHERE lfr.reversal_for_id = lf.id
            )
            ORDER BY id
        LOOP
            RAISE LOG 'Creating reversing entry for ledger ID: % in invoice: % for charge: %', 
                     v_ledger_record.id, NEW.invoice_no, v_ledger_record.charge_no;
                     
            -- Create reversing entry
            INSERT INTO form_ledger_finance (
                invoice_id, 
                invoice_no,
                site_id,
                charge_line_id, 
                charge_no,
                account_id, 
                source_id, 
                source_form, 
                post_datetime,
                transaction_datetime,
                inventory_id,
                ledger_inventory_id,
                ledger_lot_id,
                ledger_serial_id,
                account_type, 
                transaction_type,
                reversal_for_id,
                debit, 
                credit, 
                created_on, 
                created_by,
                notes
            ) VALUES (
                v_ledger_record.invoice_id,
                v_ledger_record.invoice_no,
                v_ledger_record.site_id,
                v_ledger_record.charge_line_id,
                v_ledger_record.charge_no,
                v_ledger_record.account_id,
                NEW.id,
                'billing_invoice',
                COALESCE(NEW.zeroed_datetime, get_site_timestamp(NEW.site_id)),
                get_site_timestamp(NEW.site_id),
                v_ledger_record.inventory_id,
                v_ledger_record.ledger_inventory_id,
                v_ledger_record.ledger_lot_id,
                v_ledger_record.ledger_serial_id,
                v_ledger_record.account_type,
                CASE 
                    WHEN v_ledger_record.transaction_type = 'Payment' THEN 'Payment Reversal'
                    WHEN v_ledger_record.transaction_type = 'Adjustment' THEN 'Adjustment Reversal'
                    WHEN v_ledger_record.transaction_type = 'Writeoff' THEN 'Writeoff Reversal'
                    WHEN v_ledger_record.transaction_type = 'Cash Allocation' THEN 'Cash Allocation Reversal'
                    ELSE v_ledger_record.transaction_type || ' Reversal'
                END,
                v_ledger_record.id,
                v_ledger_record.credit,  -- Swap credit and debit
                v_ledger_record.debit,   -- Swap credit and debit
                CURRENT_TIMESTAMP,
                v_creator,
                'Zeroed invoice: ' || COALESCE(NEW.zeroed_reason_id, 'No reason provided')
            );

        END LOOP;

        PERFORM set_config('clara.disable_invoice_trigger', 'on', false);
        PERFORM set_config('clara.prevent_locked_checks', 'on', false);

        IF NEW.zeroed_next_insurance IS NOT NULL THEN
            FOR v_chargeline_record IN
                SELECT 
                lcl.id,
                calculate_invoice_split_no(
                    lcl.payer_id,
                    v_rx_guid,
                    v_supply_guid,
                    v_dme_guid,
                    v_pa_guid,
                    v_medid_guid,
                    lcl.rx_no,
                    lcl.inventory_type,
                    inv.medid,
                    get_prior_auth_id(
                    lcl.inventory_id::INTEGER,
                    lcl.order_rx_id::INTEGER,
                    NEW.zeroed_next_insurance::INTEGER
                    )
                ) AS calc_invoice_split_no,
                lcl.patient_id,
                lcl.order_rx_id,
                lcl.fill_number,
                lcl.rental_id,
                lcl.charge_no,
                lcl.parent_charge_no,
                lcl.master_charge_no,
                lcl.encounter_id,
                lcl.ticket_no,
                lcl.ticket_item_no,
                lcl.show_cost,
                lcl.compound_no,
                lcl.is_primary_drug,
                lcl.is_primary_drug_ncpdp,
                lcl.site_id,
                lcl.rx_no,
                lcl.inventory_type_filter,
                lcl.inventory_id,
                lcl.description,
                lcl.inventory_type,
                lclnew.hcpc_code,
                lcl.gcn_seqno,
                lcl.ndc,
                lcl.formatted_ndc,
                lcl.upc,
                lcl.upin,
                lcl.bill_quantity,
                lcl.metric_unit_each,
                lclnew.charge_quantity_ea,
                lclnew.charge_quantity,
                lclnew.charge_unit,
                lcl.hcpc_quantity,
                lcl.hcpc_unit,
                lcl.metric_quantity,
                lcl.billing_unit_id,
                lcl.rental_type,
                lcl.frequency_code,
                lclnew.insurance_id,
                lclnew.payer_id,
                lclnew.billing_method_id,
                lclnew.pricing_source,
                lclnew.shared_contract_id,
                lclnew.modifier_1,
                lclnew.modifier_2,
                lclnew.modifier_3,
                lclnew.modifier_4,
                lclnew.billed,
                lclnew.calc_billed_ea,
                CASE
                    WHEN lcl.master_charge_no = lcl.charge_no THEN lclnew.dispense_fee
                    ELSE 0.00
                END AS dispense_fee,
                lclnew.incv_amt_sub,
                lclnew.gross_amount_due,
                lclnew.cost_basis,
                lclnew.pt_pd_amt_sub,
                lclnew.expected,
                lclnew.awp_price,
                lclnew.calc_expected_ea,
                lclnew.list_price,
                lclnew.calc_list_ea,
                lcl.total_cost,
                lcl.calc_cost_ea,
                lclnew.copay,
                lclnew.total_balance_due,
                lclnew.revenue_code_id,
                lcl.date_of_service::date as date_of_service,
                lcl.date_of_service_end::date as date_of_service_end
                FROM form_ledger_charge_line lcl
                INNER JOIN form_inventory inv ON inv.id = lcl.inventory_id
                AND inv.archived IS NOT TRUE
                AND inv.deleted IS NOT TRUE
                CROSS JOIN LATERAL create_ledger_charge_line(
                lcl.inventory_id::INTEGER,
                NEW.zeroed_next_insurance::INTEGER,
                lcl.site_id::INTEGER,
                lcl.patient_id::INTEGER,
                lcl.bill_quantity::NUMERIC,
                CASE 
                    WHEN lcl.is_primary_drug = 'Yes' THEN TRUE 
                    ELSE FALSE 
                END::BOOLEAN,
                lcl.compound_no::text,
                lcl.order_rx_id::INTEGER,
                lcl.rental_id::INTEGER,
                lcl.ticket_no::TEXT,
                lcl.ticket_item_no::TEXT,
                lcl.encounter_id::INTEGER,
                FALSE::BOOLEAN,
                lcl.parent_charge_no::text
                ) lclnew
                WHERE lcl.invoice_no = NEW.invoice_no
                AND COALESCE(lcl.void, 'No') <> 'Yes'
                AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
                AND lcl.archived IS NOT TRUE
                AND lcl.deleted IS NOT TRUE
            LOOP

                v_old_charge_no := gen_random_uuid();
                UPDATE form_ledger_charge_line lcl
                SET
                    charge_no = v_old_charge_no,
                    carry_over_charge_no = lcl.charge_no,
                    invoice_status = NULL,
                    revenue_accepted_posted = NULL,
                    zeroed = 'Yes',
                    zeroed_datetime = COALESCE(NEW.zeroed_datetime, get_site_timestamp(NEW.site_id)),
                    zeroed_by = v_creator,
                    zeroed_reason_id = NEW.zeroed_reason_id,
                    expected = 0.00,
                    paid = 0.00,
                    billed = 0.00,
                    gross_amount_due = 0.00,
                    total_adjusted = 0.00,
                    total_balance_due = 0.00,
                    flat_tax_amt = 0.00,
                    sales_tax = 0.00
                WHERE lcl.id = v_chargeline_record.id;

                INSERT INTO form_ledger_charge_line (
                    patient_id,
                    order_rx_id,
                    calc_invoice_split_no,
                    fill_number,
                    rental_id,
                    charge_no,
                    parent_charge_no,
                    master_charge_no,
                    encounter_id,
                    ticket_no,
                    ticket_item_no,
                    show_cost,
                    compound_no,
                    is_primary_drug,
                    is_primary_drug_ncpdp,
                    site_id,
                    rx_no,
                    inventory_type_filter,
                    inventory_id,
                    description,
                    inventory_type,
                    hcpc_code,
                    gcn_seqno,
                    ndc,
                    formatted_ndc,
                    upc,
                    upin,
                    bill_quantity,
                    metric_unit_each,
                    charge_quantity_ea,
                    charge_quantity,
                    charge_unit,
                    hcpc_quantity,
                    hcpc_unit,
                    metric_quantity,
                    billing_unit_id,
                    rental_type,
                    frequency_code,
                    insurance_id,
                    payer_id,
                    billing_method_id,
                    pricing_source,
                    shared_contract_id,
                    modifier_1,
                    modifier_2,
                    modifier_3,
                    modifier_4,
                    billed,
                    calc_billed_ea,
                    dispense_fee,
                    incv_amt_sub,
                    gross_amount_due,
                    cost_basis,
                    pt_pd_amt_sub,
                    expected,
                    awp_price,
                    calc_expected_ea,
                    list_price,
                    calc_list_ea,
                    total_cost,
                    calc_cost_ea,
                    copay,
                    total_balance_due,
                    revenue_code_id,
                    archived,
                    deleted,
                    created_by,
                    created_on,
                    date_of_service,
                    date_of_service_end
                ) VALUES (
                    v_chargeline_record.patient_id,
                    v_chargeline_record.order_rx_id,
                    v_chargeline_record.calc_invoice_split_no,
                    v_chargeline_record.fill_number,
                    v_chargeline_record.rental_id,
                    v_chargeline_record.charge_no,
                    v_chargeline_record.parent_charge_no,
                    v_chargeline_record.master_charge_no,
                    v_chargeline_record.encounter_id,
                    v_chargeline_record.ticket_no,
                    v_chargeline_record.ticket_item_no,
                    v_chargeline_record.show_cost,
                    v_chargeline_record.compound_no,
                    v_chargeline_record.is_primary_drug,
                    v_chargeline_record.is_primary_drug_ncpdp,
                    v_chargeline_record.site_id,
                    v_chargeline_record.rx_no,
                    v_chargeline_record.inventory_type_filter,
                    v_chargeline_record.inventory_id,
                    v_chargeline_record.description,
                    v_chargeline_record.inventory_type,
                    v_chargeline_record.hcpc_code,
                    v_chargeline_record.gcn_seqno,
                    v_chargeline_record.ndc,
                    v_chargeline_record.formatted_ndc,
                    v_chargeline_record.upc,
                    v_chargeline_record.upin,
                    v_chargeline_record.bill_quantity,
                    v_chargeline_record.metric_unit_each,
                    v_chargeline_record.charge_quantity_ea,
                    v_chargeline_record.charge_quantity,
                    v_chargeline_record.charge_unit,
                    v_chargeline_record.hcpc_quantity,
                    v_chargeline_record.hcpc_unit,
                    v_chargeline_record.metric_quantity,
                    v_chargeline_record.billing_unit_id,
                    v_chargeline_record.rental_type,
                    v_chargeline_record.frequency_code,
                    v_chargeline_record.insurance_id,
                    v_chargeline_record.payer_id,
                    v_chargeline_record.billing_method_id,
                    v_chargeline_record.pricing_source,
                    v_chargeline_record.shared_contract_id,
                    v_chargeline_record.modifier_1,
                    v_chargeline_record.modifier_2,
                    v_chargeline_record.modifier_3,
                    v_chargeline_record.modifier_4,
                    v_chargeline_record.billed,
                    v_chargeline_record.calc_billed_ea,
                    v_chargeline_record.dispense_fee,
                    v_chargeline_record.incv_amt_sub,
                    v_chargeline_record.gross_amount_due,
                    v_chargeline_record.cost_basis,
                    v_chargeline_record.pt_pd_amt_sub,
                    v_chargeline_record.expected,
                    v_chargeline_record.awp_price,
                    v_chargeline_record.calc_expected_ea,
                    v_chargeline_record.list_price,
                    v_chargeline_record.calc_list_ea,
                    v_chargeline_record.total_cost,
                    v_chargeline_record.calc_cost_ea,
                    v_chargeline_record.copay,
                    v_chargeline_record.total_balance_due,
                    v_chargeline_record.revenue_code_id,
                    FALSE,
                    FALSE,
                    v_creator,
                    get_site_timestamp(NEW.site_id),
                    v_chargeline_record.date_of_service,
                    v_chargeline_record.date_of_service_end
                );
            END LOOP;

            -- Insert into billing_error_log to record the action
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Successfully carried over charges to new insurance',
                'Zero invoice with carry over',
                'INFO',
                current_schema(),
                'billing_invoice',
                jsonb_build_object(
                    'function_name', 'process_invoice_zero',
                    'invoice_no', NEW.invoice_no,
                    'zeroed_next_insurance', NEW.zeroed_next_insurance
                )
            );
        ELSE
            -- Unlink from current invoice, master charges still have to go somewhere
            UPDATE form_ledger_charge_line lcl
            SET
                invoice_no = NULL,
                invoice_status = NULL,
                revenue_accepted_posted = NULL
            WHERE lcl.invoice_no = NEW.invoice_no
            AND COALESCE(lcl.void, 'No') <> 'Yes'
            AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
            AND lcl.archived IS NOT TRUE
            AND lcl.deleted IS NOT TRUE
            AND lcl.master_charge_no = lcl.charge_no;

            -- Zero out the charge lines if COB, otherwise, unlink from the invoice
            UPDATE form_ledger_charge_line lcl
            SET 
                zeroed = 'Yes',
                zeroed_datetime = COALESCE(NEW.zeroed_datetime, get_site_timestamp(NEW.site_id)),
                zeroed_by = v_creator,
                zeroed_reason_id = NEW.zeroed_reason_id,
                expected = 0.00,
                paid = 0.00,
                billed = 0.00,
                gross_amount_due = 0.00,
                total_adjusted = 0.00,
                total_balance_due = 0.00,
                flat_tax_amt = 0.00,
                sales_tax = 0.00
            WHERE lcl.invoice_no = NEW.invoice_no
            AND COALESCE(lcl.void, 'No') <> 'Yes'
            AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
            AND lcl.archived IS NOT TRUE
            AND lcl.deleted IS NOT TRUE
            AND lcl.master_charge_no <> lcl.charge_no;
        END IF;

        -- Set zeroed_datetime if not already set
        NEW.zeroed_datetime := COALESCE(NEW.zeroed_datetime, get_site_timestamp(NEW.site_id));
        -- Reset allocated_unapplied_cash since we've reversed it
        NEW.allocated_unapplied_cash := 0;
        NEW.total_expected := 0;
        NEW.total_paid := 0;
        NEW.total_pt_pay := 0;
        NEW.total_billed := 0;
        NEW.total_tax := 0;
        NEW.total_cost := 0;

        -- Touch the delivery ticket so the status updates
        UPDATE form_careplan_delivery_tick dt
        SET created_by = dt.created_by
        WHERE dt.id = NEW.delivery_ticket_id
        AND COALESCE(dt.void, 'No') <> 'Yes';

        PERFORM set_config('clara.disable_invoice_trigger', 'off', false);
        PERFORM set_config('clara.prevent_locked_checks', 'off', false);

        v_success := true;
        RAISE LOG 'Successfully zeroed invoice: %', NEW.invoice_no;
        
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        -- Log the error
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Transaction failed during invoice zeroing',
            'TRIGGER',
            current_schema(),
            'billing_invoice',
            jsonb_build_object(
                'function_name', 'process_invoice_zero',
                'invoice_no', NEW.invoice_no,
                'success_flag', v_success
            )
        );
        
        RAISE; -- Re-throw the exception to rollback the transaction
    END;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Trigger for zeroing invoices
CREATE OR REPLACE TRIGGER trg_billing_invoice_zero
BEFORE UPDATE ON form_billing_invoice
FOR EACH ROW
WHEN (COALESCE(NEW.zeroed, 'No') = 'Yes' AND COALESCE(OLD.zeroed, 'No') IS DISTINCT FROM 'Yes' AND COALESCE(NEW.revenue_accepted_posted, 'No') = 'Yes')
EXECUTE FUNCTION process_invoice_zero();
