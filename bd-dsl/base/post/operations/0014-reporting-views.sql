
DO $$ 
BEGIN
    -- Create type for reject codes if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'generic_invoice_charge_line_type') THEN
        CREATE TYPE generic_invoice_charge_line_type AS (
            description text,
            quantity text,
            unit_price text,
            total_price text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'generic_delivery_ticket_charge_line_type') THEN
        CREATE TYPE generic_delivery_ticket_charge_line_type AS (
            description text,
            quantity text,
            copay text
        );
    END IF;

END $$;

CREATE OR REPLACE VIEW vw_generic_invoice AS 
SELECT
    bi.site_id,
    bi.patient_id,
    bi.id as invoice_id,
    bi.patient_name,
    bi.patient_address,
    bi.patient_city,
    bi.patient_state,
    bi.patient_zip,
    bi.responsible_party,
    bi.send_bill_to,
    bi.billing_city,
    bi.billing_state,
    bi.billing_zip,
    bi.policy_no,
    bi.billing_group_no,
    bi.prior_auth_no,
    bi.insured_name,
    bi.insured_address,
    bi.insured_city,
    bi.insured_state,
    bi.insured_zip,
    bi.due_days,
    bi.services_provided,
    bi.free_text,
    bi.terms,
    format_currency(COALESCE(bi.total_billed, 0.00)::numeric) as total_billed,
    format_currency(COALESCE(bi.total_expected, 0.00)::numeric) as total_expected,
    format_currency(COALESCE(bi.total_balance_due, 0.00)::numeric) as total_balance_due,
    format_currency(COALESCE(bi.total_adjusted, 0.00)::numeric) as total_adjusted,
    lcl.charge_lines
FROM form_billing_invoice bi
LEFT JOIN LATERAL (
    SELECT 
        ARRAY(
            SELECT 
                (
                    lcl.description::text,
                    format_numeric(COALESCE(lcl.bill_quantity, 1)::numeric),
                    format_currency(COALESCE(lcl.calc_billed_ea, 0.00)::numeric),
                    format_currency(COALESCE(lcl.billed, 0.00)::numeric)
                )::generic_invoice_charge_line_type
            FROM form_ledger_charge_line lcl
            WHERE lcl.invoice_no = bi.invoice_no
            AND lcl.archived IS NOT TRUE
            AND lcl.deleted IS NOT TRUE
            AND COALESCE(lcl.void, 'No') <> 'Yes'
        ) as charge_lines
) lcl ON true
WHERE bi.payer_id = 1
AND bi.archived IS NOT TRUE
AND bi.deleted IS NOT TRUE
AND COALESCE(bi.void, 'No') <> 'Yes'
AND COALESCE(bi.zeroed, 'No') <> 'Yes';


CREATE OR REPLACE VIEW vw_patient_copay AS 
SELECT
    dt.site_id,
    dt.patient_id,
    dt.id as delivery_ticket_id,
    dt.ticket_no,
    dt.delivery_date as fill_by_date,
    cdl.ship_method_id as delivery_method,
    pt.mrn,
    TRIM(CONCAT(pt.firstname, ' ', pt.lastname)) as patient_name,
    cdl.ship_delivery_instructions as delivery_notes,
    cdl.ship_to,
    TRIM(CONCAT(cdl.ship_street, ' ', cdl.ship_street2)) as address_line1,
    TRIM(CONCAT(cdl.ship_city, ' ', cdl.ship_state_id, ' ', cdl.ship_zip)) as address_line2,
    dtism.charge_lines,
    format_currency(COALESCE(dtism.total_copay, 0.00)::numeric) as total_copay
    FROM form_careplan_delivery_tick dt
    INNER JOIN form_patient pt
        ON pt.id = dt.patient_id
        AND pt.archived IS NOT TRUE
        AND pt.deleted IS NOT TRUE
    INNER JOIN sf_form_careplan_delivery_tick_to_careplan_delivery_log sfdtl
        ON sfdtl.form_careplan_delivery_tick_fk = dt.id
        AND sfdtl.archive IS NOT TRUE
        AND sfdtl.delete IS NOT TRUE
    INNER JOIN form_careplan_delivery_log cdl
        ON cdl.id = sfdtl.form_careplan_delivery_log_fk
        AND cdl.archived IS NOT TRUE
        AND cdl.deleted IS NOT TRUE
    LEFT JOIN lateral (
        SELECT
            ROUND(SUM(COALESCE(dti.copay::numeric, 0.00)::numeric), 2) as total_copay,
            ARRAY_AGG(
                ROW(
                    CONCAT('Rx #:', rx.rx_no, ' ', inv.name::text),
                    format_numeric(COALESCE(dti.dispense_quantity, 1)::numeric),
                    format_currency(COALESCE(dti.copay, 0.00)::numeric)
                )::generic_delivery_ticket_charge_line_type
            ) as charge_lines
        FROM vw_delivery_ticket_pulled_items dti 
        INNER JOIN form_inventory inv ON inv.id = dti.inventory_id
        INNER JOIN form_careplan_order_rx rx ON rx.id = dti.rx_id
        WHERE dti.copay::numeric > 0.0 AND dti.bill = 'Yes'
    ) dtism on true;
