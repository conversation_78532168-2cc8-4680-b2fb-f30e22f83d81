CREATE OR R<PERSON>LACE FUNCTION build_mm_pay_to_segment(
  p_site_id integer
) RETURNS mm_address_info AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_address_info;
  v_error_message text;
  v_params jsonb;
BEGIN
  -- Check for null input parameters
  IF p_site_id IS NULL THEN
    RAISE EXCEPTION 'Site ID cannot be NULL';
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'site_id', p_site_id
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
      'build_mm_pay_to_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM pay to segment for site ID: %', p_site_id;

    -- Build pay-to address from site record
    -- Use billing address if available, otherwise use regular address
    SELECT 
        CASE 
            WHEN site.bill_address1 IS NOT NULL THEN site.bill_address1
            ELSE site.address1
        END::text,
        CASE 
            WHEN site.bill_address1 IS NOT NULL THEN site.bill_address2
            ELSE site.address2
        END::text,
        CASE 
            WHEN site.bill_address1 IS NOT NULL THEN site.bill_city
            ELSE site.city
        END::text,
        CASE 
            WHEN site.bill_address1 IS NOT NULL THEN site.bill_state_id
            ELSE site.state_id
        END::text,
        CASE
            WHEN site.bill_address1 IS NOT NULL THEN site.bill_zip
            ELSE site.zip
        END::text
    INTO 
        v_result.address1,
        v_result.address2,
        v_result.city,
        v_result.state,
        v_result.postal_code
    FROM form_site site
    WHERE site.id = p_site_id
      AND site.deleted IS NOT TRUE 
      AND site.archived IS NOT TRUE;

    -- Validate that we found the site record
    IF v_result.address1 IS NULL THEN
        RAISE EXCEPTION 'Site record not found or missing address information for ID: %', p_site_id;
    END IF;

    RAISE LOG 'MM pay to segment build result: %', v_result;

    -- Log success
    PERFORM log_billing_function(
      'build_mm_pay_to_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_pay_to_segment',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'build_mm_pay_to_segment',
        'site_id', p_site_id
      )
    );

    PERFORM log_billing_function(
      'build_mm_pay_to_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;