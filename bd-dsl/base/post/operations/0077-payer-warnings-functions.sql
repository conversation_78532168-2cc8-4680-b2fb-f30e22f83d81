DO $$ BEGIN
  PERFORM drop_all_function_signatures('check_payer_warnings');
END $$;
CREATE OR REPLACE FUNCTION check_payer_warnings(
    p_ticket_no TEXT
) RETURNS JSONB AS $$
DECLARE
    v_warnings TEXT[] := '{}';
    v_block BOOLEAN := FALSE;
    v_error_message TEXT;
    v_params JSONB;
BEGIN

    -- Set parameters for potential error logging
    v_params := jsonb_build_object(
        'function_name', 'check_payer_warnings',
        'input', p_input
    );

    -- Collect all warnings and block status
    WITH DeliveryItems AS (
        SELECT
            rxo.next_fill_number as fill_number,
            ins.id as insurance_id,
            pi.not_covered,
            pi.limits,
            pi.refills_limit,
            pi.unit_limit,
            pi.limit_freq,
            pi.auth_required,
            pi.billable,
            pi.max_rental_claims,
            pi.daily_bill_rental,
            pi.no_recurring_billing,
            pi.required_doc_ids,
            pc.name as shared_contract_name,
            item.frequency_code as frequency_code,
            (item.dispense_quantity)::NUMERIC as dispense_quantity,
            (item.day_supply)::INTEGER as day_supply,
            item.type as type,
            py.id as payer_id,
            inv.id as inventory_id,
            CASE
                WHEN inv.type IN ('Drug', 'Compound') THEN rxo.pa_id
                WHEN inv.type = 'Equipment Rental' THEN cor.rental_pa_id
                WHEN inv.type = 'Supply' THEN co.supplies_pa_id
                WHEN inv.type = 'Billable' AND COALESCE(inv.nursing_related, 'No') = 'Yes' THEN co.nursing_pa_id
                ELSE NULL
            END as pa_id,
            inv.name as inventory_name,
            py.organization as payer_name,
            pt.id as patient_id,
            co.id as order_id
        FROM form_careplan_dt_item item
        INNER JOIN form_patient_insurance ins ON ins.id = item.insurance_id
        INNER JOIN form_payer py ON py.id = ins.payer_id AND py.archived IS NOT TRUE AND py.deleted IS NOT TRUE
        INNER JOIN form_site st ON st.id = item.site_id AND st.archived IS NOT TRUE AND st.deleted IS NOT TRUE
        INNER JOIN form_inventory inv ON inv.id = item.inventory_id AND inv.archived IS NOT TRUE AND inv.deleted IS NOT TRUE
        INNER JOIN form_patient pt ON pt.id = item.patient_id AND pt.archived IS NOT TRUE AND pt.deleted IS NOT TRUE
        INNER JOIN form_careplan_order_rx cpor ON cpor.id = item.rx_id AND cpor.archived IS NOT TRUE AND cpor.deleted IS NOT TRUE
        INNER JOIN form_careplan_order co ON cpor.order_no = co.order_no AND co.archived IS NOT TRUE AND co.deleted IS NOT TRUE
        INNER JOIN vw_rx_order rxo ON rxo.rx_id = cpor.id
        LEFT JOIN form_careplan_order_rental cor ON cor.id = item.rental_id AND cor.archived IS NOT TRUE AND cor.deleted IS NOT TRUE
        INNER JOIN get_inventory_pricing(inv.id, ins.id, st.id, pt.id) pi ON TRUE
        LEFT JOIN vw_payer_contract pc ON pc.payer_id = py.id AND pc.site_id = st.id
        WHERE item.ticket_no = p_ticket_no
        AND item.archived IS NOT TRUE
        AND item.deleted IS NOT TRUE
    ),
    UnitChecks AS (
        SELECT
            di.*,
            CASE di.limit_freq
                WHEN 'Day' THEN di.dispense_quantity / COALESCE(di.day_supply, 1)
                WHEN 'Week' THEN di.dispense_quantity / CEIL(COALESCE(di.day_supply, 1) / 7)
                WHEN 'Month' THEN di.dispense_quantity / CEIL(COALESCE(di.day_supply, 1) / 28)
                WHEN 'Quarter' THEN (
                    di.dispense_quantity + COALESCE(
                        get_charged_units_by_payer(
                            di.patient_id, 
                            di.payer_id, 
                            di.inventory_id,
                            TO_CHAR(CURRENT_DATE - INTERVAL '90 days', 'MM/DD/YYYY')
                        ), 0
                    )
                )
                WHEN 'Bi-Annual' THEN (
                    di.dispense_quantity + COALESCE(
                        get_charged_units_by_payer(
                            di.patient_id, 
                            di.payer_id, 
                            di.inventory_id,
                            TO_CHAR(CURRENT_DATE - INTERVAL '182 days', 'MM/DD/YYYY')
                        ), 0
                    )
                )
                WHEN 'Annual' THEN (
                    di.dispense_quantity + COALESCE(
                        get_charged_units_by_payer(
                            di.patient_id, 
                            di.payer_id, 
                            di.inventory_id,
                            TO_CHAR(CURRENT_DATE - INTERVAL '365 days', 'MM/DD/YYYY')
                        ), 0
                    )
                )
            END as calculated_units
        FROM DeliveryItems di
        WHERE di.limits IN ('Units', 'Fills')
    ),
    AllWarnings AS (
        SELECT
            CASE
                WHEN di.not_covered = 'Block' THEN TRUE
                ELSE FALSE
            END as block,
            CASE
                WHEN di.not_covered = 'Block' THEN di.inventory_name || ' is blocked by ' || di.payer_name || ' on ' || di.shared_contract_name || ' contract for ' || di.payer_name || '.'
                ELSE di.inventory_name || ' is not covered by ' || di.payer_name || ' on ' || di.shared_contract_name || ' contract for ' || di.payer_name || '.'
            END as warning
        FROM DeliveryItems di
        WHERE di.not_covered IN ('Yes', 'Block')

        UNION

        SELECT
            FALSE as block,
            CASE
                WHEN uc.limits = 'Units' AND uc.calculated_units > uc.unit_limit THEN
                    uc.inventory_name || ' has a unit limit of ' || uc.unit_limit || ' per ' || uc.limit_freq ||
                    ' on ' || uc.shared_contract_name || ' contract for ' || uc.payer_name || '.'
                WHEN uc.limits = 'Fills' AND uc.fill_number > uc.refills_limit THEN
                    uc.inventory_name || ' has a fill limit of ' || uc.refills_limit || 
                    ' on ' || uc.shared_contract_name || ' contract for ' || uc.payer_name || '.'
                ELSE NULL
            END as warning
        FROM UnitChecks uc
        WHERE 
            (uc.limits = 'Units' AND uc.calculated_units > uc.unit_limit)
            OR (uc.limits = 'Fills' AND uc.fill_number > uc.refills_limit)
        
        UNION
        
        SELECT
            CASE
                WHEN di.auth_required = 'Block' AND di.pa_id IS NULL THEN TRUE
                ELSE FALSE
            END as block,
            'Patient has no authorization assigned to ' || di.inventory_name ||
            ' but is set to require authorization on ' || di.shared_contract_name ||
            ' contract for ' || di.payer_name || '.' as warning
        FROM DeliveryItems di
        WHERE di.auth_required IN ('Yes', 'Block') AND di.pa_id IS NULL

        UNION

        SELECT
            FALSE as block,
            di.inventory_name || ' is set as not billable by ' || di.payer_name ||
            ' on ' || di.shared_contract_name || ' contract for ' || di.payer_name || '.' as warning
        FROM DeliveryItems di
        WHERE di.billable = 'No'

        UNION

        SELECT
            FALSE as block,
            di.inventory_name || ' is missing required document ' || lrd.name || ' for ' || di.payer_name ||
            ' on ' || di.shared_contract_name || ' contract for ' || di.payer_name || '.' as warning
        FROM DeliveryItems di
        INNER JOIN form_list_required_doc lrd 
            ON lrd.code = ANY(di.required_doc_ids)
            AND lrd.code NOT IN 
            (SELECT ldt.rdc_id FROM form_document doc
            INNER JOIN form_list_doc_type ldt
                ON ldt.code = doc.type_id
                AND ldt.archive IS NOT TRUE
                AND ldt.deleted IS NOT TRUE
            WHERE di.order_id = doc.order_id 
            AND doc.archive IS NOT TRUE 
            AND doc.deleted IS NOT TRUE)
        WHERE di.required_doc_ids IS NOT NULL
    )
    SELECT 
        array_agg(warning) FILTER (WHERE warning IS NOT NULL),
        bool_or(block)
    INTO v_warnings, v_block
    FROM AllWarnings;

    -- Return the JSON result
    RETURN jsonb_build_object(
        'block', CASE WHEN v_block THEN 'Yes' ELSE 'No' END,
        'warnings', COALESCE(pretty_jsonb(to_jsonb(v_warnings)), '[]'::jsonb)
    );

EXCEPTION WHEN OTHERS THEN
    -- Log error
    v_error_message := SQLERRM;
    
    -- Log to billing error log
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        v_error_message,
        'Exception in check_payer_warnings',
        'FUNCTION',
        current_schema(),
        'form_delivery_ticket',
        v_params
    );
    
    -- Re-raise the exception
    RAISE;
END;
$$ LANGUAGE plpgsql VOLATILE;