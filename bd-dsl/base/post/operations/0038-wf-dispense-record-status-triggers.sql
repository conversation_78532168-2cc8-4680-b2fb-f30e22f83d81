DO $$ BEGIN
  PERFORM drop_all_function_signatures('insert_dispense_record');
END $$;
CREATE OR REPLACE FUNCTION insert_dispense_record(
    v_rx_id INTEGER,
    v_created_by INTEGER,
    v_status VARCHAR
)
RETURNS VOID AS
$$
BEGIN
    
    -- This function can be called from triggers or other functions
    -- For example, from update_disp_record() when a delivery ticket is confirmed
    -- Insert into the dispense record table for next fills if no record exists
    INSERT INTO form_careplan_order_rx_disp (
        is_specialty,
        is_ncpdp,
        patient_id,
        site_id,
        careplan_id,
        rx_no,
        rx_id,
        order_no,
        template_type,
        next_fill_date,
        next_delivery_date,
        last_through_date,
        fill_number,
        status,
        last_event_id,
        refill_tracking,
        refills,
        refills_remaining,
        doses_allowed,
        doses_remaining,
        archived,
        deleted,
        created_on,
        created_by
    )
    SELECT
        rx.is_specialty::text as is_specialty,
        CASE WHEN rx.billing_method_id = 'ncpdp' THEN 'Yes'::text ELSE NULL::text END,
        rx.patient_id,
        rx.site_id,
        rx.careplan_id,
        rx.rx_no,
        rx.rx_id,
        rx.order_no,
        rx.template_type,
        rx.next_fill_date::date as next_fill_date,
        rx.next_delivery_date::date as next_delivery_date,
        rx.last_through_date::date as last_through_date,
        rx.fill_number+1,
        v_status,
        rx.last_event_id::text as last_event_id,
        COALESCE(rx.refill_tracking, 'Refills'),
        rx.refills::integer as refills,
        rx.refills_remaining::integer as refills_remaining,
        rx.doses_allowed::integer as doses_allowed,
        rx.doses_remaining::integer as doses_remaining,
        FALSE,
        FALSE,
        CURRENT_TIMESTAMP,
        v_created_by
    FROM vw_rx_order rx
    WHERE rx.rx_id = v_rx_id
    AND NOT EXISTS (
        SELECT 1
        FROM form_careplan_order_rx_disp existing
        WHERE existing.rx_no = rx.rx_no
        AND existing.fill_number = rx.fill_number+1
        AND existing.archived IS NOT TRUE
        AND existing.deleted IS NOT TRUE
    );
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_disp_record()
RETURNS TRIGGER AS $$
DECLARE
    v_tech_verified boolean;
    v_ready_to_fill boolean;
    v_dt_confirmed boolean;
    v_pharm_verified boolean;
    v_revenue_accepted boolean;
    v_billing_method_id text;
    v_delivery_ticket_id integer;
    v_rx_complete boolean;
    v_rx_verified boolean;
    v_rx_denied boolean;
    v_is_refill boolean;
    v_working_disp_id integer;
    v_working_disp_ids integer[];
    v_fill_number integer;
    v_template_type text;
    v_next_fill_date date;
    v_next_delivery_date date;
    v_last_through_date date;
    v_refill_tracking text;
    v_refills integer;
    v_refills_remaining integer;
    v_doses_allowed integer;
    v_doses_remaining integer;
    v_copay numeric;
    v_params jsonb;
    v_rx_id integer;
    v_final_invoice_count integer;
    v_test_claim_id integer;
    v_require_test_claim boolean;
    v_billing_method text;
    v_related_rxs integer[];
    v_related_invoices integer[];
    v_dt_voided boolean;
    v_rx_is_specialty boolean;
    v_needs_claim boolean := FALSE;
    v_delivery_ticket_status text;
    v_rx_verification_complete boolean := FALSE;
    v_disp_record_status text;
    v_new_disp_record_status text;
    v_dt_is_rx_record boolean := FALSE;
    v_is_cob boolean := FALSE;
    v_is_bill_for_denial boolean := FALSE;
    v_copay_paid_by_card boolean := FALSE;
    v_needs_adjudicated boolean := FALSE;
	v_setup_complete boolean := FALSE;
    v_rx_on_file boolean := FALSE;
    v_master_invoice_no text;
    v_cob_payer_id integer;
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- You can use conditionals based on the table name
    IF TG_TABLE_NAME = 'form_billing_invoice' THEN
        -- Check invoice type based on master_invoice_no relationship
        v_is_cob := (COALESCE(NEW.bill_for_denial,'No') = 'No' AND COALESCE(NEW.is_master_invoice,'No') = 'No');
        
        IF NEW.zeroed = 'Yes' OR NEW.void = 'Yes' AND v_is_cob IS FALSE THEN
            v_master_invoice_no = NULL;
        ELSE
            v_master_invoice_no := NEW.master_invoice_no;
        END IF;

        IF NEW.delivery_ticket_id IS NOT NULL THEN
            RETURN NEW;
        END IF;

        -- Bill for denial: master_invoice_no IS NULL
        v_is_bill_for_denial := (COALESCE(NEW.bill_for_denial,'No') = 'Yes');

        -- If this is a COB invoice, check if it's a copay card
        IF v_is_cob THEN
            SELECT payer_id INTO v_cob_payer_id
            FROM form_patient_insurance 
            WHERE id = NEW.insurance_id;
            
            -- Check if this is a copay card by checking payer type
            SELECT EXISTS (
                SELECT 1 FROM form_payer 
                WHERE id = v_cob_payer_id 
                AND type_id = 'COPAY'
            ) INTO v_copay_paid_by_card;

        END IF;

        -- Get related RXs from the charge lines
        SELECT
            ARRAY_AGG(DISTINCT lcl.order_rx_id)
        INTO
            v_related_rxs
        FROM form_ledger_charge_line lcl
        WHERE lcl.invoice_no = NEW.invoice_no;

    ELSIF TG_TABLE_NAME = 'form_careplan_delivery_tick' THEN
        RAISE LOG 'Invoice #: % Patient ID: %, Careplan ID: %', NEW.ticket_no, NEW.patient_id, NEW.careplan_id;

        v_dt_voided := NEW.archived IS TRUE OR NEW.void = 'Yes';
        v_dt_confirmed := COALESCE(NEW.confirmed, 'No') = 'Yes';
        -- Get working dispense IDs and rx IDs
        SELECT
            ARRAY_AGG(DISTINCT dti.rx_id)
        INTO
            v_related_rxs
        FROM vw_delivery_items dti
        WHERE dti.delivery_ticket_id = NEW.id
        AND dti.is_primary_drug IS TRUE
        AND NEW.status NOT IN ('Void') 
        AND COALESCE(NEW.void,'No') <> 'Yes' 
        AND NEW.archived IS NOT TRUE 
        AND NEW.deleted IS NOT TRUE;

        RAISE LOG 'v_related_rxs: %', v_related_rxs;
        -- Handle different scenarios based on ticket status
        IF v_dt_voided AND v_dt_confirmed THEN
            RAISE LOG 'v_dt_voided AND v_dt_confirmed';
            -- If delivery ticket is voided after confirmation, set the status to void on the previous dispense record
            SELECT
                ARRAY_AGG(DISTINCT wprx.working_dispense_id)
            INTO
                v_working_disp_ids
            FROM vw_working_prescriptions wprx
            WHERE wprx.delivery_ticket_id = NEW.id;

            UPDATE form_careplan_order_rx_disp rxd
            SET
                status = 'Void'
            WHERE rxd.delivery_ticket_id = NEW.id
            AND (v_working_disp_ids IS NULL OR rxd.id <> ALL(v_working_disp_ids));
            
        ELSIF v_dt_voided THEN
            RAISE LOG 'v_dt_voided';
            -- Get the working dispense IDs if we haven't already
            IF v_working_disp_ids IS NULL THEN
                SELECT
                    ARRAY_AGG(DISTINCT wprx.working_dispense_id)
                INTO
                    v_working_disp_ids
                FROM vw_working_prescriptions wprx
                WHERE wprx.delivery_ticket_id = NEW.id;
            END IF;
            
            UPDATE form_careplan_order_rx_disp rxd
            SET
                delivery_ticket_id = NULL
            WHERE (rxd.delivery_ticket_id = NEW.id AND (rxd.id = ANY(v_working_disp_ids)));
        ELSE
            RAISE LOG 'new.id: %', NEW.id;
            UPDATE form_careplan_order_rx_disp rxd
            SET
                delivery_ticket_id = NEW.id
            WHERE rxd.rx_id = ANY(v_related_rxs) AND 
            (rxd.delivery_ticket_id IS NULL OR rxd.delivery_ticket_id < NEW.id);
        END IF;

    ELSIF TG_TABLE_NAME = 'form_careplan_order_rx' THEN
        v_related_rxs := ARRAY[NEW.id];
        v_dt_is_rx_record := TRUE;
    ELSE
        RAISE EXCEPTION 'update_disp_record: Invalid table name: %', TG_TABLE_NAME;
        RETURN NEW;
    END IF;

    RAISE LOG 'Found % related rxs for table % with ID %', v_related_rxs, TG_TABLE_NAME, NEW.id;
    IF v_related_rxs IS NULL THEN
        RAISE EXCEPTION 'Unable to find related rxs on table % with ID %', TG_TABLE_NAME, NEW.id;
    END IF;

    FOREACH v_rx_id IN ARRAY v_related_rxs
    LOOP
        BEGIN
            v_params := jsonb_build_object('rx_id', v_rx_id);
        
            -- For COB invoices with copay cards, handle them specially
            IF v_is_cob AND v_copay_paid_by_card THEN
                
                PERFORM set_config('clara.prevent_locked_checks', 'on', false);
                -- Find the master invoice and update the patient responsibility as paid
                UPDATE form_billing_invoice bi
                SET
                    total_pt_pay = 0 -- Set patient responsibility to zero since copay card paid it
                WHERE COALESCE(bi.is_master_invoice,'No') = 'Yes'
                AND bi.archived IS NOT TRUE
                AND bi.deleted IS NOT TRUE
                AND bi.master_invoice_no = v_master_invoice_no
                AND COALESCE(bi.void, 'No') <> 'Yes'
                AND COALESCE(bi.zeroed, 'No') <> 'Yes'
                AND COALESCE(bi.revenue_accepted_posted, 'No') <> 'Yes';
                PERFORM set_config('clara.prevent_locked_checks', 'off', false);

                -- Create a record of the copay card payment
                INSERT INTO billing_error_log (
                    error_message,
                    error_context,
                    error_type,
                    schema_name,
                    table_name,
                    additional_details
                ) VALUES (
                    'Copay card payment recorded',
                    'Informational log about copay card payment',
                    'INFO',
                    current_schema(),
                    'form_billing_invoice',
                    jsonb_build_object(
                        'rx_id', v_rx_id
                    )
                );
            END IF;

            SELECT
                CASE
                    WHEN rx.is_specialty = 'Yes' THEN TRUE 
                    ELSE FALSE 
                END,
                rxd.id,
                ins.billing_method_id,
                dt.id,
                CASE WHEN COALESCE(dt.ready_to_fill, 'No') = 'Yes' THEN TRUE ELSE FALSE END,
                CASE WHEN COALESCE(dt.confirmed, 'No') = 'Yes' THEN TRUE ELSE FALSE END,
                CASE WHEN COALESCE(dt.verified, 'No') = 'Yes' THEN TRUE ELSE FALSE END,
                CASE WHEN COALESCE(dt.tech_verified, 'No') = 'Yes' THEN TRUE ELSE FALSE END,
                CASE WHEN COALESCE(rx.rx_complete, 'No') = 'Yes' THEN TRUE ELSE FALSE END,
                CASE WHEN COALESCE(rx.rx_verified, 'No') = 'Yes' THEN TRUE ELSE FALSE END,
                CASE WHEN COALESCE(rx.rx_denied, 'No') = 'Yes' THEN TRUE ELSE FALSE END,
                CASE WHEN fill_count.fill_count > 1 THEN TRUE ELSE FALSE END,
                rxd.fill_number::integer,
                rx.template_type::text,
                rx.next_fill_date::date,
                rx.next_delivery_date::date,
                rx.last_through_date::date,
                rx.refill_tracking::text,
                rx.refills::integer,
                rx.refills_remaining::integer,
                rx.doses_allowed::integer,
                rx.doses_remaining::integer,
                -- If we have a copay card payment, use it; otherwise use the invoice copay
                CASE
                    WHEN copay_paid_by_card IS TRUE THEN 0.00 -- Copay is paid by card
                    ELSE COALESCE(ROUND(bi.total_pt_pay::numeric, 2), 0.00)
                END::numeric,
                CASE WHEN COALESCE(bi.revenue_accepted, 'No') = 'Yes' THEN TRUE ELSE FALSE END,
                bi.master_invoice_no::text,
                COALESCE(bi.invoice_count, 0)::integer,
                CASE
                    WHEN COALESCE(cp.require_test_claim, 'No') = 'Yes' THEN TRUE
                    ELSE FALSE
                END,
                rxd.test_claim_id::integer,
                COALESCE(copi.billing_method, coi.billing_method, ord.billing_method),
                CASE WHEN rx.rx_on_file = 'Yes' THEN TRUE ELSE FALSE END
            INTO 
                v_rx_is_specialty,
                v_working_disp_id,
                v_billing_method_id,
                v_delivery_ticket_id,
                v_ready_to_fill,
                v_dt_confirmed,
                v_pharm_verified,
                v_tech_verified,
                v_rx_complete,
                v_rx_verified,
                v_rx_denied,
                v_is_refill,
                v_fill_number,
                v_template_type,
                v_next_fill_date,
                v_next_delivery_date,
                v_last_through_date,
                v_refill_tracking,
                v_refills,
                v_refills_remaining,
                v_doses_allowed,
                v_doses_remaining,
                v_copay,
                v_revenue_accepted,
                v_master_invoice_no,
                v_final_invoice_count,
                v_require_test_claim,
                v_test_claim_id,
                v_billing_method,
                v_rx_on_file
            FROM form_careplan_order_rx_disp rxd
            INNER JOIN form_careplan_order_rx rx ON rx.id = rxd.rx_id
            INNER JOIN form_company cp ON cp.id = 1
            LEFT JOIN form_careplan_order_item coi
                ON coi.rx_no = rx.rx_no
                AND coi.archived IS NOT TRUE
                AND coi.deleted IS NOT TRUE
            LEFT JOIN form_careplan_orderp_item copi 
                ON copi.rx_no = rx.rx_no 
                AND copi.archived IS NOT TRUE 
                AND copi.deleted IS NOT TRUE
            LEFT JOIN form_patient_insurance ins ON (ins.id = get_next_insurance_id(rx.id::integer, NULL::integer, FALSE::boolean)
                AND ins.archived IS NOT TRUE
                AND ins.deleted IS NOT TRUE)
            LEFT JOIN form_careplan_order ord
                ON ord.order_no = rx.order_no
                AND ord.archived IS NOT TRUE
                AND ord.deleted IS NOT TRUE
            LEFT JOIN LATERAL (SELECT COUNT(*) as fill_count FROM form_careplan_order_rx_disp disp
            	WHERE rx_id = rxd.rx_id AND status IN ('Confirmed')) fill_count on true
            LEFT JOIN LATERAL (SELECT 
                MAX(bi.master_invoice_no) as master_invoice_no,
                MAX(iva.copay) as total_pt_pay,
                COALESCE(bool_and(bi.revenue_accepted = 'Yes'), FALSE) as revenue_accepted,
                COALESCE(bool_and(ica.invoice_no IS NOT NULL), FALSE) as copay_paid_by_card,
                COUNT(DISTINCT bi.id) as invoice_count
            FROM form_ledger_charge_line lcl
            INNER JOIN form_billing_invoice bi ON bi.invoice_no = lcl.invoice_no 
            AND bi.archived IS NOT TRUE
            AND bi.deleted IS NOT TRUE
            AND COALESCE(bi.void, 'No') <> 'Yes'
            AND COALESCE(bi.zeroed, 'No') <> 'Yes'
            AND (bi.delivery_ticket_id IS NULL OR bi.delivery_ticket_id = rxd.delivery_ticket_id)
            LEFT JOIN vw_invoice_claim_response_details iva ON iva.invoice_id = bi.id
            LEFT JOIN vw_rx_copay_card_invoice_details ica ON ica.master_invoice_no = bi.master_invoice_no
            WHERE rxd.rx_no = lcl.rx_no
            AND lcl.archived IS NOT TRUE
            AND lcl.deleted IS NOT TRUE
            AND COALESCE(lcl.void, 'No') <> 'Yes'
            AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
            GROUP BY bi.invoice_no
            ) bi ON true
            LEFT JOIN form_careplan_delivery_tick dt ON dt.id = rxd.delivery_ticket_id
            AND dt.archived IS NOT TRUE
            AND dt.deleted IS NOT TRUE
            AND COALESCE(dt.void, 'No') <> 'Yes'
            WHERE rxd.rx_id = v_rx_id
            ORDER BY rxd.id DESC
            LIMIT 1;

            v_setup_complete := v_rx_complete IS TRUE AND v_rx_denied IS NOT TRUE AND ((v_billing_method = 'Insurance' AND v_billing_method_id IS NOT NULL) OR (v_billing_method <> 'Insurance'));
            v_rx_verification_complete := v_setup_complete IS TRUE AND v_rx_verified IS TRUE;
            v_needs_claim := (v_billing_method_id = 'ncpdp' AND v_final_invoice_count = 0);

            v_needs_adjudicated := v_revenue_accepted IS FALSE AND v_needs_claim IS FALSE;
            RAISE LOG 'v_is_refill:% v_delivery_ticket_id:% v_dt_confirmed:% v_needs_claim:% v_billing_method_id:% v_pharm_verified:% v_tech_verified:% v_ready_to_fill:% v_rx_complete:% v_rx_denied:% v_needs_adjudicated:% v_rx_verification_complete:% v_needs_claim:% v_revenue_accepted:% v_billing_method_id:% v_final_invoice_count:%', v_is_refill, v_delivery_ticket_id, v_dt_confirmed, v_needs_claim, v_billing_method_id, v_pharm_verified, v_tech_verified, v_ready_to_fill, v_rx_complete, v_rx_denied, v_needs_adjudicated, v_rx_verification_complete, v_needs_claim, v_revenue_accepted, v_billing_method_id, v_final_invoice_count;
            v_delivery_ticket_status := CASE
                WHEN v_dt_confirmed IS TRUE THEN 'Confirmed'
                WHEN v_pharm_verified IS TRUE THEN 'Delivery Ticket Confirmation'
                WHEN v_tech_verified IS TRUE THEN 'Order Verification'
                WHEN v_ready_to_fill IS TRUE THEN 'Print Labels / Fill Rx'
                ELSE 'Ready to Contact'
            END;
            v_disp_record_status := CASE
                WHEN v_setup_complete IS FALSE THEN 'Order Entry/Setup'
                WHEN v_rx_verification_complete IS FALSE AND (v_needs_claim IS TRUE AND v_rx_on_file IS FALSE) THEN 'Order Entry/Setup'
                WHEN v_needs_adjudicated IS TRUE THEN 'Claims to Adjudicate'
                WHEN v_rx_verification_complete IS FALSE THEN 'Rx Verification'
                WHEN v_require_test_claim IS TRUE AND v_test_claim_id IS NULL THEN 'Test Claim'
                WHEN v_is_refill IS TRUE AND v_needs_claim IS TRUE THEN 'Ready to Refill'
                WHEN v_needs_claim IS TRUE THEN 'Order Entry/Setup' -- Should never get here
                WHEN v_delivery_ticket_id IS NULL THEN 'Ready to Contact'
                ELSE v_delivery_ticket_status
            END;
            RAISE LOG 'v_disp_record_status:% v_working_disp_id:% v_rx_id:% v_delivery_ticket_id:%', v_disp_record_status, v_working_disp_id, v_rx_id, v_delivery_ticket_id;
            IF v_working_disp_id IS NULL THEN
                IF TG_TABLE_NAME <> 'form_careplan_order_rx' THEN
                    RAISE LOG 'TG_TABLE_NAME: %', TG_TABLE_NAME;
                    INSERT INTO dispensing_error_log (
                        error_message,
                        error_context,
                        error_type,
                        schema_name,
                        table_name,
                        additional_details
                    ) VALUES (
                        'Missing dispense record',
                        'Exception in update_disp_record',
                        'FUNCTION',
                        current_schema(),
                        'form_careplan_order_rx_disp',
                        v_params
                    );
                    RAISE EXCEPTION 'Dispense record not found for rx_id: %', v_rx_id;
                ELSE
                    PERFORM insert_dispense_record(v_rx_id, COALESCE(NEW.updated_by,NEW.created_by), v_disp_record_status);
                END IF;
            END IF;

            UPDATE form_careplan_order_rx_disp rxd
            SET
                master_invoice_no = v_master_invoice_no,
                is_specialty = CASE WHEN v_rx_is_specialty THEN 'Yes'::text ELSE NULL::text END,
                is_ncpdp = CASE WHEN v_billing_method_id = 'ncpdp' THEN 'Yes'::text ELSE NULL::text END,
                status = v_disp_record_status,
                delivery_ticket_id = v_delivery_ticket_id,
                fill_number = CASE 
                    WHEN v_dt_confirmed AND v_delivery_ticket_id IS NOT NULL THEN rxd.fill_number
                    ELSE v_fill_number
                END,
                template_type = v_template_type,
                next_fill_date = CASE
                    WHEN v_dt_confirmed AND v_delivery_ticket_id IS NOT NULL  THEN rxd.next_fill_date
                    ELSE v_next_fill_date
                END,
                next_delivery_date = CASE 
                    WHEN v_dt_confirmed AND v_delivery_ticket_id IS NOT NULL THEN rxd.next_delivery_date
                    ELSE v_next_delivery_date
                END,
                last_through_date = CASE 
                    WHEN v_dt_confirmed AND v_delivery_ticket_id IS NOT NULL THEN rxd.last_through_date
                    ELSE v_last_through_date
                END,
                refill_tracking = v_refill_tracking,
                refills = v_refills,
                refills_remaining = CASE
                    WHEN v_dt_confirmed AND v_delivery_ticket_id IS NOT NULL THEN rxd.refills_remaining
                    ELSE v_refills_remaining
                END,
                doses_allowed = v_doses_allowed,
                doses_remaining = CASE
                    WHEN v_dt_confirmed AND v_delivery_ticket_id IS NOT NULL THEN rxd.doses_remaining
                    ELSE v_doses_remaining
                END,
                copay = v_copay
            WHERE rxd.id = v_working_disp_id;

            -- If the delivery ticket was already created, check if the drug item is on it to update the copay
            UPDATE form_careplan_dt_item dti
            SET
                copay = v_copay
            FROM form_careplan_delivery_tick dt
            WHERE dti.ticket_no = dt.ticket_no
            AND dt.id = v_delivery_ticket_id
            AND dti.archived IS NOT TRUE
            AND dti.deleted IS NOT TRUE
            AND COALESCE(dti.is_primary_drug, 'No') = 'Yes'
            AND dti.rx_id = v_rx_id;

            IF v_dt_is_rx_record IS TRUE THEN
                UPDATE form_careplan_order_rx rx
                SET
                    status = CASE 
                        WHEN v_rx_verified THEN 'Verified'
                        WHEN v_rx_denied THEN 'Denied'
                        WHEN v_rx_complete THEN 'Complete'
                        ELSE 'Setup'
                    END
                WHERE rx.id = v_rx_id;
            END IF;

            -- For bill for denial invoices, we need to handle them specially
            IF v_is_bill_for_denial AND TG_TABLE_NAME = 'form_billing_invoice' THEN
                -- Check if the next insurance ID is available
                IF NEW.next_insurance_id IS NOT NULL THEN
                    -- Note: This approach logs the fact that we have a bill for denial scenario
                    -- that requires a second invoice for the next payer in line
                    INSERT INTO billing_error_log (
                        error_message,
                        error_context,
                        error_type,
                        schema_name,
                        table_name,
                        additional_details
                    ) VALUES (
                        'Bill for denial invoice identified',
                        'Preparing for subsequent payer billing',
                        'INFO',
                        current_schema(),
                        'form_billing_invoice',
                        jsonb_build_object(
                            'invoice_no', NEW.invoice_no,
                            'rx_id', v_rx_id,
                            'next_insurance_id', NEW.next_insurance_id
                        )
                    );
                END IF;
            END IF;

            IF v_dt_confirmed IS TRUE AND v_delivery_ticket_id IS NOT NULL THEN

                v_new_disp_record_status := CASE
                            WHEN v_billing_method_id = 'ncpdp' THEN 'Ready to Refill'
                            ELSE 'Ready to Contact'
                END;
                PERFORM insert_dispense_record(v_rx_id, COALESCE(NEW.updated_by,NEW.created_by), v_new_disp_record_status);
            END IF;
        END;
    END LOOP;
    RETURN NEW;

    EXCEPTION WHEN OTHERS THEN
    -- Log error
    INSERT INTO dispensing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        SQLERRM,
        'Exception in update_disp_record',
        'FUNCTION',
        current_schema(),
        'form_careplan_order_rx_disp',
        jsonb_build_object(
            'function_name', 'update_disp_record',
            'rx_id', v_rx_id
        )
    );
    RAISE WARNING 'Failed to update dispense log status: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- For INSERT operations
CREATE OR REPLACE TRIGGER review_dt_insert_changes
    AFTER INSERT ON form_careplan_delivery_tick
    FOR EACH ROW
    EXECUTE FUNCTION update_disp_record();

DROP TRIGGER IF EXISTS review_dt_update_changes ON form_careplan_delivery_tick;
CREATE CONSTRAINT TRIGGER review_dt_update_changes
    AFTER UPDATE ON form_careplan_delivery_tick
    DEFERRABLE INITIALLY DEFERRED
    FOR EACH ROW
    EXECUTE FUNCTION update_disp_record();

CREATE OR REPLACE TRIGGER review_invoice_insert_changes
    AFTER INSERT ON form_billing_invoice
    FOR EACH ROW
    EXECUTE FUNCTION update_disp_record();

DROP TRIGGER IF EXISTS review_invoice_update_changes ON form_billing_invoice;

CREATE CONSTRAINT TRIGGER review_invoice_update_changes
    AFTER UPDATE ON form_billing_invoice
    DEFERRABLE INITIALLY DEFERRED
    FOR EACH ROW
    EXECUTE FUNCTION update_disp_record();

DROP TRIGGER IF EXISTS review_rx_update_changes ON form_careplan_order_rx;

CREATE CONSTRAINT TRIGGER review_rx_update_changes
    AFTER UPDATE OF rx_verified, rx_denied, rx_complete, is_specialty ON form_careplan_order_rx
    DEFERRABLE INITIALLY DEFERRED
    FOR EACH ROW
    EXECUTE FUNCTION update_disp_record();
