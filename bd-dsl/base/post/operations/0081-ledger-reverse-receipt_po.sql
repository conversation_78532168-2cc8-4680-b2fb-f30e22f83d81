DO $$ BEGIN
  PERFORM drop_all_function_signatures('trigger_receipt_po_void');
END $$;
CREATE OR REPLACE FUNCTION trigger_receipt_po_void()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if the record is being updated to void status
    IF COALESCE(NEW.void, 'No') = 'Yes' AND (COALESCE(OLD.void, 'No') IS NULL OR COALESCE(OLD.void, 'No') = 'No') THEN
        -- Create reverse entries in ledger_inventory
        INSERT INTO form_ledger_inventory (
            receipt_no,
            receipt_date,
            source_form,
            source_id,
            source_no,
            transaction_type,
            transaction_date,
            po_id,
            site_id,
            patient_id,
            careplan_id,
            inventory_id,
            order_id,
            order_no,
            order_item_id,
            physician_order_id,
            rx_no,
            ticket_no,
            ticket_item_no,
            acquisition_cost,
            acquisition_cost_ea,
            supplier_id,
            quantity,
            description,
            raw_barcode
        )
        SELECT 
            lei.receipt_no,
            NEW.receipt_date,            -- Use the current void date
            'receipt_po',                -- Source form is receipt_po
            NEW.id,                      -- Source ID is the receipt_po ID
            NEW.receipt_no,              -- Source number is receipt number
            'Void',                      -- Transaction type is Void
            NEW.voided_datetime::date,   -- Transaction date is void date
            lei.po_id,
            lei.site_id,
            lei.patient_id,
            lei.careplan_id,
            lei.inventory_id,
            lei.order_id,
            lei.order_no,
            lei.order_item_id,
            lei.physician_order_id,
            lei.rx_no,
            lei.ticket_no,
            lei.ticket_item_no,
            lei.acquisition_cost,
            lei.acquisition_cost_ea,
            lei.supplier_id,
            -1 * lei.quantity,           -- Negative quantity to reverse
            'Void of receipt ' || NEW.receipt_no,
            lei.raw_barcode
        FROM form_ledger_inventory lei
        WHERE lei.po_id = NEW.po_id
        AND lei.transaction_type = 'Purchase';

        -- Create reverse entries in ledger_lot
        INSERT INTO form_ledger_lot (
            ledger_id,
            source_form,
            source_id,
            source_no,
            transaction_type,
            transaction_date,
            po_id,
            supplier_id,
            site_id,
            patient_id,
            careplan_id,
            order_id,
            order_no,
            order_item_id,
            physician_order_id,
            rx_no,
            ticket_no,
            ticket_item_no,
            inventory_id,
            acquisition_cost,
            acquisition_cost_ea,
            lot_no,
            quantity,
            description,
            is_340b,
            raw_barcode
        )
        SELECT 
            ll.ledger_id,
            'receipt_po',                -- Source form is receipt_po
            NEW.id,                      -- Source ID is the receipt_po ID
            NEW.receipt_no,              -- Source number is receipt number
            'Void',                      -- Transaction type is Void
            NEW.voided_datetime::date,   -- Transaction date is void date
            ll.po_id,
            ll.supplier_id,
            ll.site_id,
            ll.patient_id,
            ll.careplan_id,
            ll.order_id,
            ll.order_no,
            ll.order_item_id,
            ll.physician_order_id,
            ll.rx_no,
            ll.ticket_no,
            ll.ticket_item_no,
            ll.inventory_id,
            ll.acquisition_cost,
            ll.acquisition_cost_ea,
            ll.lot_no,
            -1 * ll.quantity,            -- Negative quantity to reverse
            'Void of receipt ' || NEW.receipt_no,
            ll.is_340b,
            ll.raw_barcode
        FROM form_ledger_lot ll
        WHERE ll.po_id = NEW.po_id
        AND ll.transaction_type = 'Purchase';

        -- Create reverse entries in ledger_serial
        INSERT INTO form_ledger_serial (
            ledger_id,
            source_form,
            source_id,
            source_no,
            transaction_type,
            transaction_date,
            po_id,
            supplier_id,
            site_id,
            patient_id,
            order_id,
            order_no,
            order_item_id,
            physician_order_id,
            rx_no,
            ticket_no,
            ticket_item_no,
            careplan_id,
            inventory_id,
            lot_no,
            serial_no,
            acquisition_cost,
            acquisition_cost_ea,
            quantity,
            description,
            is_340b,
            raw_barcode
        )
        SELECT 
            ls.ledger_id,
            'receipt_po',                -- Source form is receipt_po
            NEW.id,                      -- Source ID is the receipt_po ID
            NEW.receipt_no,              -- Source number is receipt number
            'Void',                      -- Transaction type is Void
            NEW.voided_datetime::date,   -- Transaction date is void date
            ls.po_id,
            ls.supplier_id,
            ls.site_id,
            ls.patient_id,
            ls.order_id,
            ls.order_no,
            ls.order_item_id,
            ls.physician_order_id,
            ls.rx_no,
            ls.ticket_no,
            ls.ticket_item_no,
            ls.careplan_id,
            ls.inventory_id,
            ls.lot_no,
            ls.serial_no,
            ls.acquisition_cost,
            ls.acquisition_cost_ea,
            -1 * ls.quantity,            -- Negative quantity to reverse
            'Void of receipt ' || NEW.receipt_no,
            ls.is_340b,
            ls.raw_barcode
        FROM form_ledger_serial ls
        WHERE ls.po_id = NEW.po_id
        AND ls.transaction_type = 'Purchase';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create or replace the trigger on the form_receipt_po table
DROP TRIGGER IF EXISTS receipt_po_void_trigger ON form_receipt_po;

CREATE TRIGGER receipt_po_void_trigger
AFTER UPDATE ON form_receipt_po
FOR EACH ROW
EXECUTE FUNCTION trigger_receipt_po_void();