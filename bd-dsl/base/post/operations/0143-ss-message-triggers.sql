-- Triggers for form_ss_message

-- Function to update canceled_message_id on related messages
CREATE OR REPLACE FUNCTION trigfunc_update_canceled_message_on_thread()
RETURNS TRIGGER AS $$
DECLARE
    v_thread_physician_order_id TEXT;
    v_thread_pharmacy_rx_no TEXT;
    v_thread_site_id INTEGER;
BEGIN
    IF NEW.message_type = 'CancelRx' THEN
        RAISE LOG 'CancelRx trigger fired for ss_message_id: %', NEW.id;

        -- Determine the thread identifiers
        IF NEW.related_message_id IS NOT NULL THEN
            SELECT fsm.physician_order_id, fsm.pharmacy_rx_no, fsm.site_id
            INTO v_thread_physician_order_id, v_thread_pharmacy_rx_no, v_thread_site_id
            FROM form_ss_message fsm
            WHERE fsm.message_id = NEW.related_message_id
            ORDER BY fsm.created_on DESC LIMIT 1;
        ELSE
            v_thread_physician_order_id := NEW.physician_order_id;
            v_thread_pharmacy_rx_no := NEW.pharmacy_rx_no;
            v_thread_site_id := NEW.site_id;
        END IF;

        RAISE LOG 'Updating canceled_message_id for thread PON: %, RxNo: %, Site: %', 
                  COALESCE(v_thread_physician_order_id, 'NULL'), 
                  COALESCE(v_thread_pharmacy_rx_no, 'NULL'), 
                  COALESCE(v_thread_site_id::TEXT, 'NULL');

        IF v_thread_physician_order_id IS NOT NULL THEN
            UPDATE form_ss_message
            SET canceled_message_id = NEW.id
            WHERE physician_order_id = v_thread_physician_order_id
              AND id != NEW.id -- Don't update trigger record
              AND deleted IS NOT TRUE AND archived IS NOT TRUE
              AND canceled_message_id IS DISTINCT FROM NEW.id;
        ELSIF v_thread_pharmacy_rx_no IS NOT NULL AND v_thread_site_id IS NOT NULL THEN
            UPDATE form_ss_message
            SET canceled_message_id = NEW.id
            WHERE pharmacy_rx_no = v_thread_pharmacy_rx_no
              AND site_id = v_thread_site_id
              AND id != NEW.id -- Don't update trigger record
              AND deleted IS NOT TRUE AND archived IS NOT TRUE
              AND canceled_message_id IS DISTINCT FROM NEW.id;
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER trigger_update_canceled_message
AFTER INSERT ON form_ss_message
FOR EACH ROW EXECUTE FUNCTION trigfunc_update_canceled_message_on_thread();

-- Function to update warning for has_renewal
CREATE OR REPLACE FUNCTION trigfunc_update_renewal_warning_on_thread()
RETURNS TRIGGER AS $$
DECLARE
    v_original_request_physician_order_id TEXT;
    v_original_request_pharmacy_rx_no TEXT;
    v_original_request_site_id INTEGER;
BEGIN
    BEGIN
        IF (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') AND 
            NEW.message_type = 'RxRenewalResponse' AND 
            NEW.renewal_status IN ('Approved', 'ApprovedWithChanges', 'Replace') AND
            NEW.related_message_id IS NOT NULL
        THEN
            RAISE LOG 'Renewal Warning trigger fired for ss_message_id: %', NEW.id;

            SELECT fsm.physician_order_id, fsm.pharmacy_rx_no, fsm.site_id
            INTO v_original_request_physician_order_id, v_original_request_pharmacy_rx_no, v_original_request_site_id
            FROM form_ss_message fsm
            WHERE fsm.message_id = NEW.related_message_id
              AND fsm.message_type = 'RxRenewalRequest' -- Ensure it's the original request
              AND fsm.deleted IS NOT TRUE AND fsm.archived IS NOT TRUE
            ORDER BY fsm.created_on DESC LIMIT 1;

            IF FOUND THEN
                RAISE LOG 'Original request found. PON: %, RxNo: %, Site: %', 
                          COALESCE(v_original_request_physician_order_id, 'NULL'), 
                          COALESCE(v_original_request_pharmacy_rx_no, 'NULL'), 
                          COALESCE(v_original_request_site_id::TEXT, 'NULL');

                IF v_original_request_physician_order_id IS NOT NULL THEN
                    UPDATE form_ss_message
                    SET warning = 'has_renewal'
                    WHERE physician_order_id = v_original_request_physician_order_id
                      AND created_on < NEW.created_on
                      AND id != NEW.id -- Don't update trigger record
                      AND deleted IS NOT TRUE AND archived IS NOT TRUE
                      AND warning IS DISTINCT FROM 'has_renewal';
                ELSIF v_original_request_pharmacy_rx_no IS NOT NULL AND v_original_request_site_id IS NOT NULL THEN
                    UPDATE form_ss_message
                    SET warning = 'has_renewal'
                    WHERE pharmacy_rx_no = v_original_request_pharmacy_rx_no
                      AND site_id = v_original_request_site_id
                      AND created_on < NEW.created_on
                      AND id != NEW.id -- Don't update trigger record
                      AND deleted IS NOT TRUE AND archived IS NOT TRUE
                      AND warning IS DISTINCT FROM 'has_renewal';
                END IF;
            ELSE
                RAISE LOG 'Original RxRenewalRequest not found for related_message_id: %', NEW.related_message_id;
            END IF;
        END IF;
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        RAISE LOG 'Error in trigfunc_update_renewal_warning_on_thread: % %', SQLERRM, SQLSTATE;
        RETURN NEW;
    END;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER  trigger_update_renewal_warning
AFTER INSERT OR UPDATE ON form_ss_message
FOR EACH ROW EXECUTE FUNCTION trigfunc_update_renewal_warning_on_thread();

-- Function and Trigger for is_active_message (initial simplified version)
CREATE OR REPLACE FUNCTION trigfunc_set_is_active_message()
RETURNS TRIGGER AS $$
DECLARE
    v_is_active TEXT := 'No';
    v_thread_pon TEXT;
    v_thread_rx_no TEXT;
    v_thread_site_id INTEGER;
    v_target_of_cancel_id INTEGER;
    v_is_denied_cancel_response BOOLEAN := FALSE;
    v_is_active_cancel_for_thread BOOLEAN := FALSE;
    v_original_request_message_id INTEGER := NULL;
    v_is_active_message TEXT := NULL;
BEGIN
    RAISE LOG 'is_active_message trigger fired for ss_message_id: %, Type: %, OP: %', NEW.id, NEW.message_type, TG_OP;

    -- Determine thread identifiers
    IF NEW.physician_order_id IS NOT NULL THEN
        v_thread_pon := NEW.physician_order_id;
    ELSIF NEW.pharmacy_rx_no IS NOT NULL AND NEW.site_id IS NOT NULL THEN
        v_thread_rx_no := NEW.pharmacy_rx_no;
        v_thread_site_id := NEW.site_id;
    ELSIF NEW.related_message_id IS NOT NULL THEN
        SELECT fsm.physician_order_id, fsm.pharmacy_rx_no, fsm.site_id
        INTO v_thread_pon, v_thread_rx_no, v_thread_site_id
        FROM form_ss_message fsm WHERE fsm.message_id = NEW.related_message_id
        ORDER BY fsm.created_on DESC LIMIT 1;
    END IF;

    -- Check for an active (non-denied) CancelRx on the thread
    IF v_thread_pon IS NOT NULL THEN
        SELECT TRUE INTO v_is_active_cancel_for_thread
        FROM form_ss_message cm
        WHERE cm.message_type = 'CancelRx' AND cm.physician_order_id = v_thread_pon
          AND cm.deleted IS NOT TRUE AND cm.archived IS NOT TRUE
          AND NOT EXISTS (
              SELECT 1 FROM form_ss_message crr 
              WHERE crr.related_message_id = cm.message_id AND crr.message_type = 'CancelRxResponse' AND crr.cancel_status = 'Denied'
                AND crr.deleted IS NOT TRUE AND crr.archived IS NOT TRUE
          )
        LIMIT 1;
    ELSIF v_thread_rx_no IS NOT NULL AND v_thread_site_id IS NOT NULL THEN
         SELECT TRUE INTO v_is_active_cancel_for_thread
        FROM form_ss_message cm
        WHERE cm.message_type = 'CancelRx' AND cm.pharmacy_rx_no = v_thread_rx_no AND cm.site_id = v_thread_site_id
          AND cm.deleted IS NOT TRUE AND cm.archived IS NOT TRUE
          AND NOT EXISTS (
              SELECT 1 FROM form_ss_message crr 
              WHERE crr.related_message_id = cm.message_id AND crr.message_type = 'CancelRxResponse' AND crr.cancel_status = 'Denied'
                AND crr.deleted IS NOT TRUE AND crr.archived IS NOT TRUE
          )
        LIMIT 1;
    END IF;
    v_is_active_cancel_for_thread := COALESCE(v_is_active_cancel_for_thread, FALSE);

    IF NEW.message_type = 'CancelRxResponse' AND NEW.cancel_status = 'Denied' THEN
        v_is_denied_cancel_response := TRUE;
        -- Find the message the original CancelRx was targeting
        SELECT orig_req.related_message_id -- This is message_id of the NewRx/RefillResponse that was targeted
        INTO v_original_request_message_id
        FROM form_ss_message orig_req WHERE orig_req.message_id = NEW.related_message_id AND orig_req.message_type = 'CancelRx';

        IF v_original_request_message_id IS NOT NULL THEN
             SELECT id INTO v_target_of_cancel_id 
             FROM form_ss_message 
             WHERE message_id = v_original_request_message_id 
             ORDER BY created_on DESC LIMIT 1; -- Assuming this gets the target
        END IF;
    END IF;

    IF v_is_active_cancel_for_thread AND NOT v_is_denied_cancel_response THEN
        -- If there's an active cancel, all messages in the thread are generally inactive for further Rx actions
        -- (except for the CancelRx itself which might be actioned with a response)
        -- The NEW record (if not the CancelRx itself or a denying response) becomes inactive.
        IF v_thread_pon IS NOT NULL THEN
            UPDATE form_ss_message 
            SET is_active_message = NULL
            WHERE physician_order_id = v_thread_pon 
              AND id != NEW.id -- Don't update trigger record
              AND deleted IS NOT TRUE AND archived IS NOT TRUE 
              AND is_active_message IS NOT NULL;
        ELSIF v_thread_rx_no IS NOT NULL AND v_thread_site_id IS NOT NULL THEN
            UPDATE form_ss_message
            SET is_active_message = NULL
            WHERE pharmacy_rx_no = v_thread_rx_no 
              AND site_id = v_thread_site_id 
              AND id != NEW.id -- Don't update trigger record
              AND deleted IS NOT TRUE AND archived IS NOT TRUE 
              AND is_active_message IS NOT NULL;
        END IF;
        -- The NEW record itself will be 'No' due to default unless it's the denying CancelRxResponse handled below.

    ELSIF v_is_denied_cancel_response AND v_target_of_cancel_id IS NOT NULL THEN
        -- Denied cancel response: reactivate the target of the cancel
        IF v_thread_pon IS NOT NULL THEN
            UPDATE form_ss_message 
            SET is_active_message = NULL
            WHERE physician_order_id = v_thread_pon 
              AND id != NEW.id -- Don't update trigger record
              AND id <> v_target_of_cancel_id 
              AND deleted IS NOT TRUE AND archived IS NOT TRUE 
              AND is_active_message IS NOT NULL;
        ELSIF v_thread_rx_no IS NOT NULL AND v_thread_site_id IS NOT NULL THEN
            UPDATE form_ss_message 
            SET is_active_message = NULL
            WHERE pharmacy_rx_no = v_thread_rx_no 
              AND site_id = v_thread_site_id 
              AND id != NEW.id -- Don't update trigger record
              AND id <> v_target_of_cancel_id 
              AND deleted IS NOT TRUE AND archived IS NOT TRUE 
              AND is_active_message IS NOT NULL;
        END IF;
        UPDATE form_ss_message 
        SET is_active_message = 'Yes' 
        WHERE id = v_target_of_cancel_id 
          AND id != NEW.id -- Don't update trigger record
          AND is_active_message IS NULL;
        IF NEW.id = v_target_of_cancel_id THEN NEW.is_active_message := 'Yes'; 
        END IF; -- If current op is on the target itself

    ELSE -- Not an active cancel thread, or it's a denied cancel (handled above)
        -- Deactivate other messages in the same thread
        IF v_thread_pon IS NOT NULL THEN
            UPDATE form_ss_message 
            SET is_active_message = NULL
            WHERE physician_order_id = v_thread_pon 
              AND id != NEW.id -- Don't update trigger record
              AND deleted IS NOT TRUE AND archived IS NOT TRUE 
              AND is_active_message IS NOT NULL;
        ELSIF v_thread_rx_no IS NOT NULL AND v_thread_site_id IS NOT NULL THEN
            UPDATE form_ss_message 
            SET is_active_message = NULL
            WHERE pharmacy_rx_no = v_thread_rx_no 
              AND site_id = v_thread_site_id 
              AND id != NEW.id -- Don't update trigger record
              AND deleted IS NOT TRUE AND archived IS NOT TRUE 
              AND is_active_message IS NOT NULL;
        END IF;

        -- Set current message to active based on its type and status
        IF NEW.message_type = 'NewRx' THEN
            v_is_active_message := 'Yes';
        ELSIF NEW.message_type = 'RxRenewalResponse' AND NEW.renewal_status IN ('Approved', 'ApprovedWithChanges', 'Replace') THEN
            v_is_active_message := 'Yes';
        ELSIF NEW.message_type = 'RxChangeResponse' AND (NEW.chg_status IN ('Approved', 'ApprovedWithChanges') OR NEW.chg_type_id IN ('U', 'S', 'P')) THEN
            v_is_active_message := 'Yes';
        -- Other cases (like pending requests, denied responses not CancelRx) will default to 'No' unless set above
        END IF;
    END IF;

    IF NEW.is_active_message IS DISTINCT FROM v_is_active_message THEN
        NEW.is_active_message := v_is_active_message;
    END IF;
    RAISE LOG 'is_active_message for % set to %', NEW.id, v_is_active_message;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER trigger_set_is_active_message
BEFORE INSERT OR UPDATE ON form_ss_message
FOR EACH ROW
WHEN (NEW.direction = 'IN')
EXECUTE FUNCTION trigfunc_set_is_active_message();

-- Function to update status_icons on original request when a response comes in
CREATE OR REPLACE FUNCTION trigfunc_update_request_status_icons_on_response()
RETURNS TRIGGER AS $$
DECLARE
    v_original_request_id INTEGER;
    v_current_icons TEXT[];
BEGIN
    IF (TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND NEW.message_status <> OLD.message_status)) AND 
        NEW.message_type IN ('RxChangeResponse', 'RxRenewalResponse') AND 
        NEW.related_message_id IS NOT NULL 
    THEN
        RAISE LOG 'Response Status Icon trigger fired for ss_message_id: %, Type: %', NEW.id, NEW.message_type;

        SELECT id, status_icons INTO v_original_request_id, v_current_icons
        FROM form_ss_message
        WHERE message_id = NEW.related_message_id
          AND deleted IS NOT TRUE AND archived IS NOT TRUE
        ORDER BY created_on DESC LIMIT 1;

        IF v_original_request_id IS NOT NULL THEN
            v_current_icons := COALESCE(v_current_icons, ARRAY[]::TEXT[]);
            v_current_icons := array_remove(v_current_icons, 'pending');
            v_current_icons := array_remove(v_current_icons, 'new');

            IF NEW.message_type = 'RxChangeResponse' THEN
                IF NEW.chg_status IN ('Approved', 'ApprovedWithChanges') THEN
                    v_current_icons := array_append(v_current_icons, 'approved');
                ELSIF NEW.chg_status = 'Denied' THEN
                    v_current_icons := array_append(v_current_icons, 'denied');
                END IF;
                IF NEW.chg_type_id IN ('P', 'U', 'S') THEN
                    v_current_icons := array_append(v_current_icons, 'info');
                END IF;
            ELSIF NEW.message_type = 'RxRenewalResponse' THEN
                IF NEW.renewal_status IN ('Approved', 'ApprovedWithChanges', 'Replace') THEN
                    v_current_icons := array_append(v_current_icons, 'approved');
                ELSIF NEW.renewal_status = 'Denied' THEN
                    v_current_icons := array_append(v_current_icons, 'denied');
                END IF;
            END IF;
            
            v_current_icons := ARRAY(SELECT DISTINCT unnest(v_current_icons));

            UPDATE form_ss_message 
            SET status_icons = v_current_icons
            WHERE id = v_original_request_id
              AND id != NEW.id
              AND status_icons IS DISTINCT FROM v_current_icons;
            RAISE LOG 'Updated status_icons on original request ID % to %', v_original_request_id, v_current_icons;
        ELSE
            RAISE LOG 'Original request message not found for related_message_id: %', NEW.related_message_id;
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER trigger_update_request_status_icons
AFTER INSERT OR UPDATE ON form_ss_message
FOR EACH ROW EXECUTE FUNCTION trigfunc_update_request_status_icons_on_response();

-- Function to update status_icons when a message is reviewed
CREATE OR REPLACE FUNCTION trigfunc_update_status_icons_on_review()
RETURNS TRIGGER AS $$
DECLARE
    v_current_icons TEXT[];
BEGIN
    BEGIN
        IF TG_OP = 'UPDATE' AND (
            (NEW.reviewed_by IS NOT NULL AND OLD.reviewed_by IS NULL) OR 
            (NEW.reviewed_on IS NOT NULL AND OLD.reviewed_on IS NULL)
        ) THEN
            RAISE LOG 'Review Status Icon trigger fired for ss_message_id: %', NEW.id;
            
            v_current_icons := COALESCE(NEW.status_icons, ARRAY[]::TEXT[]); -- Directly use/default the TEXT[]
            v_current_icons := array_remove(v_current_icons, 'new');
            v_current_icons := ARRAY(SELECT DISTINCT unnest(v_current_icons));
            
            NEW.status_icons := v_current_icons; -- Assign TEXT[] directly
            RAISE LOG 'Updated status_icons on message ID % to %', NEW.id, NEW.status_icons;
        END IF;
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        RAISE LOG 'Error in trigfunc_update_status_icons_on_review - SQLSTATE: %, SQLERRM: %, Message ID: %, Old Status Icons: %, New Status Icons: %',
            SQLSTATE, SQLERRM, NEW.id, OLD.status_icons, NEW.status_icons;
        RETURN NEW;
    END;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER trigger_update_status_icons_on_review
BEFORE UPDATE ON form_ss_message -- BEFORE to modify NEW directly
FOR EACH ROW EXECUTE FUNCTION trigfunc_update_status_icons_on_review();

-- Function and Trigger to sync pharmacy_order_id and pharmacy_rx_no to form_ss_message from form_careplan_orderp_item
CREATE OR REPLACE FUNCTION trigfunc_sync_ss_message_order_links()
RETURNS TRIGGER AS $$
DECLARE
    v_careplan_order_id INTEGER;
BEGIN
    IF NEW.physician_order_id IS NOT NULL THEN
        RAISE LOG 'Sync SS Message Order Links trigger fired for orderp_item_id: %, physician_order_id: %', NEW.id, NEW.physician_order_id;

        -- Fetch Parent form_careplan_order.id
        SELECT sf.form_careplan_order_fk INTO v_careplan_order_id
        FROM sf_form_careplan_order_to_careplan_orderp_item sf
        WHERE sf.form_careplan_orderp_item_fk = NEW.id
          AND sf.archive IS NOT TRUE AND sf.delete IS NOT TRUE
        LIMIT 1;

        IF v_careplan_order_id IS NULL THEN
            RAISE WARNING 'Could not find parent form_careplan_order for orderp_item_id: % via sf_table. Cannot sync ss_message links.', NEW.id;
            RETURN NEW;
        END IF;

        -- Handle archived or deleted status of form_careplan_orderp_item
        IF NEW.archived IS TRUE OR NEW.deleted IS TRUE THEN
            RAISE LOG 'Orderp_item_id: % is archived/deleted. Clearing pharmacy_order_id and pharmacy_rx_no on related ss_messages for PON: %', NEW.id, NEW.physician_order_id;
            UPDATE form_ss_message
            SET pharmacy_order_id = NULL, pharmacy_rx_no = NULL
            WHERE physician_order_id = NEW.physician_order_id
              AND deleted IS NOT TRUE AND archived IS NOT TRUE -- Only update non-deleted/archived ss_messages
              AND (pharmacy_order_id IS NOT NULL OR pharmacy_rx_no IS NOT NULL);
            RETURN NEW; -- Exit after clearing
        END IF;

        -- Handle pharmacy_order_id update (always set if not archived/deleted and parent order found)
        RAISE LOG 'Updating pharmacy_order_id to % on ss_messages for PON: %', v_careplan_order_id, NEW.physician_order_id;
        UPDATE form_ss_message
        SET pharmacy_order_id = v_careplan_order_id
        WHERE physician_order_id = NEW.physician_order_id
          AND deleted IS NOT TRUE AND archived IS NOT TRUE
          AND pharmacy_order_id IS DISTINCT FROM v_careplan_order_id;

        -- Handle pharmacy_rx_no update/clear
        IF TG_OP = 'INSERT' THEN
            IF NEW.rx_no IS NOT NULL THEN
                RAISE LOG 'Setting pharmacy_rx_no to % on ss_messages for PON: % (INSERT)', NEW.rx_no, NEW.physician_order_id;
                UPDATE form_ss_message
                SET pharmacy_rx_no = NEW.rx_no
                WHERE physician_order_id = NEW.physician_order_id
                  AND deleted IS NOT TRUE AND archived IS NOT TRUE
                  AND pharmacy_rx_no IS DISTINCT FROM NEW.rx_no;
            END IF;
        ELSIF TG_OP = 'UPDATE' THEN
            IF NEW.rx_no IS DISTINCT FROM OLD.rx_no THEN
                RAISE LOG 'Updating pharmacy_rx_no from % to % on ss_messages for PON: % (UPDATE)', OLD.rx_no, NEW.rx_no, NEW.physician_order_id;
                UPDATE form_ss_message
                SET pharmacy_rx_no = NEW.rx_no -- This will set to NULL if NEW.rx_no is NULL
                WHERE physician_order_id = NEW.physician_order_id
                  AND deleted IS NOT TRUE AND archived IS NOT TRUE
                  AND pharmacy_rx_no IS DISTINCT FROM NEW.rx_no;
            END IF;
        END IF;

    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER trigger_sync_ss_message_order_links
AFTER INSERT OR UPDATE ON form_careplan_orderp_item
FOR EACH ROW EXECUTE FUNCTION trigfunc_sync_ss_message_order_links();

CREATE OR REPLACE FUNCTION trigfunc_sync_ss_benefit_insurance()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.payer_id IS NOT NULL AND NEW.active ='Yes' THEN
        UPDATE form_ss_benefit
        SET insurance_id = NEW.id
        WHERE patient_id = NEW.patient_id
          AND payer_id = NEW.payer_id
          AND insurance_id IS NULL;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER trigger_sync_ss_benefit_insurance
AFTER INSERT OR UPDATE ON form_patient_insurance
FOR EACH ROW EXECUTE FUNCTION trigfunc_sync_ss_benefit_insurance();

-- Function and Trigger to validate CancelRxResponse before saving
CREATE OR REPLACE FUNCTION trigfunc_validate_cancel_rx_response()
RETURNS TRIGGER AS $$
DECLARE
    v_had_dispense BOOLEAN := FALSE;
    v_original_request_message_id TEXT;
    v_original_pharmacy_rx_no TEXT;
    v_original_site_id INTEGER;
    v_dispensed_count INTEGER;
BEGIN
    IF NEW.message_type = 'CancelRxResponse' THEN
        RAISE LOG 'Validating CancelRxResponse for ss_message_id: %', NEW.id;

        -- Determine if the original prescription had any dispenses
        -- First, find the original CancelRx request this response is for
        SELECT fsm.related_message_id INTO v_original_request_message_id
        FROM form_ss_message fsm 
        WHERE fsm.message_id = NEW.related_message_id AND fsm.message_type = 'CancelRx' -- This assumes NEW.related_message_id is the ID of the CancelRx request
        ORDER BY fsm.created_on DESC LIMIT 1;

        IF v_original_request_message_id IS NOT NULL THEN
            -- Now find the NewRx or RenewalResponse that the CancelRx was targeting
            SELECT fsm.pharmacy_rx_no, fsm.site_id 
            INTO v_original_pharmacy_rx_no, v_original_site_id
            FROM form_ss_message fsm
            WHERE fsm.message_id = v_original_request_message_id -- The message_id of the prescription record that was being canceled
            ORDER BY fsm.created_on DESC LIMIT 1;
        ELSIF NEW.pharmacy_rx_no IS NOT NULL AND NEW.site_id IS NOT NULL THEN 
            -- Fallback if direct related message isn't found or isn't CancelRx, try using current message context if it has rx_no
            v_original_pharmacy_rx_no := NEW.pharmacy_rx_no;
            v_original_site_id := NEW.site_id;
        END IF;

        IF v_original_pharmacy_rx_no IS NOT NULL AND v_original_site_id IS NOT NULL THEN
            SELECT COUNT(DISTINCT drd.delivery_ticket_id) INTO v_dispensed_count
            FROM form_careplan_order_rx_disp drd
            WHERE drd.rx_no = v_original_pharmacy_rx_no AND drd.site_id = v_original_site_id
              AND drd.deleted IS NOT TRUE AND drd.archived IS NOT TRUE AND drd.status = 'Confirmed';
            v_had_dispense := COALESCE(v_dispensed_count, 0) > 0;
            RAISE LOG 'CancelRxResponse check: had_dispense = % for RxNo %', v_had_dispense, v_original_pharmacy_rx_no;
        ELSE
            RAISE LOG 'CancelRxResponse check: Could not determine original pharmacy_rx_no to check for dispenses.';
        END IF;

        IF NEW.cancel_status = 'Approved' AND v_had_dispense THEN
            IF NEW.cancel_note IS NULL OR TRIM(NEW.cancel_note) = '' THEN
                RAISE EXCEPTION 'Approval note is required for CancelRxResponse if original Rx was dispensed.'
                USING ERRCODE = 'P0002', DETAIL = 'Original prescription had dispenses; an approval note is mandatory.';
            END IF;
        ELSIF NEW.cancel_status = 'Denied' AND v_had_dispense THEN
            IF (NEW.cancel_denied_reason_code IS NULL OR array_length(NEW.cancel_denied_reason_code, 1) IS NULL OR array_length(NEW.cancel_denied_reason_code, 1) = 0) AND 
               (NEW.cancel_denied_reason IS NULL OR TRIM(NEW.cancel_denied_reason) = '') THEN
                RAISE EXCEPTION 'Denial reason code or text is required for CancelRxResponse if original Rx was dispensed.'
                USING ERRCODE = 'P0003', DETAIL = 'Original prescription had dispenses; a denial reason code or text is mandatory.';
            END IF;
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER trigger_validate_cancel_rx_response
BEFORE INSERT OR UPDATE ON form_ss_message
FOR EACH ROW EXECUTE FUNCTION trigfunc_validate_cancel_rx_response();
