-- First revoke all permissions and then grant only insert
<PERSON><PERSON><PERSON><PERSON> ALL ON form_ledger_billing_activity FROM PUBLIC;
GRANT INSERT ON form_ledger_billing_activity TO PUBLIC;

-- CREATE OR REPLACE TRIGGER to prevent updates and deletes
DO $$ BEGIN
  PERFORM drop_all_function_signatures('prevent_billing_ledger_modifications');
END $$;
CREATE OR REPLACE FUNCTION prevent_billing_ledger_modifications()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);
    RAISE EXCEPTION 'Modifications to billing activity log are not allowed';
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER prevent_billing_ledger_modifications_trigger
    BEFORE UPDATE OR DELETE ON form_ledger_billing_activity
    FOR EACH ROW
    EXECUTE FUNCTION prevent_billing_ledger_modifications();

-- Helper function to get the localized datetime based on user timezone
DO $$ BEGIN
  PERFORM drop_all_function_signatures('get_user_localized_datetime');
END $$;
CREATE OR REPLACE FUNCTION get_user_localized_datetime(user_id integer)
RETURNS timestamp WITH TIME ZONE AS $$
DECLARE
    user_timezone text;
    ts timestamp with time zone;
BEGIN

    -- Get user's timezone, default to 'America/Chicago' if not set
    SELECT COALESCE(timezone_id::text, 'America/Chicago') INTO user_timezone 
    FROM form_user 
    WHERE id = user_id;
    
    IF user_timezone IS NULL THEN
        RAISE EXCEPTION 'Invalid timezone configuration for user %', user_id;
    END IF;

    -- Get current timestamp and explicitly set timezone
    SELECT NOW() AT TIME ZONE 'UTC' AT TIME ZONE user_timezone INTO ts;
    
    RETURN ts;
END;
$$ LANGUAGE plpgsql;

-- Helper function to get status name from form_list_billing_csstatus
DO $$ BEGIN
  PERFORM drop_all_function_signatures('get_substatus_name');
END $$;
CREATE OR REPLACE FUNCTION get_substatus_name(status_code text)
RETURNS text AS $$
BEGIN
    RETURN (SELECT name FROM form_list_billing_csstatus WHERE code = status_code);
END;
$$ LANGUAGE plpgsql;

-- Main trigger function for billing_invoice changes
DO $$ BEGIN
  PERFORM drop_all_function_signatures('log_billing_invoice_changes');
END $$;
CREATE OR REPLACE FUNCTION log_billing_invoice_changes()
RETURNS TRIGGER AS $$
DECLARE
    user_id integer;
    localized_ts timestamp with time zone;
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Determine the user ID from the context
    user_id := COALESCE(NEW.updated_by, NEW.created_by);
    
    -- Get localized timestamp
    localized_ts := get_user_localized_datetime(user_id);

    -- Handle revenue_accepted changes
    IF (TG_OP = 'INSERT' AND NEW.revenue_accepted IS NOT NULL) OR
       (TG_OP = 'UPDATE' AND COALESCE(OLD.revenue_accepted, '') != COALESCE(NEW.revenue_accepted, '')) THEN
        INSERT INTO form_ledger_billing_activity (
            localized_datetime, user_id, patient_id, site_id, 
            invoice_no, form, form_id, field, 
            old_value, new_value, description
        ) VALUES (
            localized_ts, user_id, NEW.patient_id, NEW.site_id,
            NEW.invoice_no, 'billing_invoice', NEW.id, 'revenue_accepted',
            COALESCE(OLD.revenue_accepted::text, ''),
            COALESCE(NEW.revenue_accepted::text, ''),
            'Revenue accepted status changed'
        );
    END IF;

    -- Handle status changes
    IF (TG_OP = 'INSERT' AND NEW.status IS NOT NULL) OR
       (TG_OP = 'UPDATE' AND COALESCE(OLD.status, '') != COALESCE(NEW.status, '')) THEN
        INSERT INTO form_ledger_billing_activity (
            localized_datetime, user_id, patient_id, site_id, 
            invoice_no, form, form_id, field, 
            old_value, new_value, description
        ) VALUES (
            localized_ts, user_id, NEW.patient_id, NEW.site_id,
            NEW.invoice_no, 'billing_invoice', NEW.id, 'status',
            COALESCE(OLD.status::text, ''),
            COALESCE(NEW.status::text, ''),
            'Invoice status changed'
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for NCPDP status changes
DO $$ BEGIN
  PERFORM drop_all_function_signatures('log_ncpdp_status_changes');
END $$;
CREATE OR REPLACE FUNCTION log_ncpdp_status_changes()
RETURNS TRIGGER AS $$
DECLARE
    user_id integer;
    localized_ts timestamp with time zone;
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Determine the user ID from the context
    user_id := COALESCE(NEW.updated_by, NEW.created_by);
    
    -- Get localized timestamp
    localized_ts := get_user_localized_datetime(user_id);

    -- Handle status changes
    IF (TG_OP = 'INSERT' AND NEW.status IS NOT NULL) OR
       (TG_OP = 'UPDATE' AND COALESCE(OLD.status, '') != COALESCE(NEW.status, '')) THEN
        INSERT INTO form_ledger_billing_activity (
            localized_datetime, user_id, patient_id, site_id,
            claim_no, form, form_id, field,
            old_value, new_value, description
        ) VALUES (
            localized_ts, user_id, NEW.patient_id, NEW.site_id,
            NEW.claim_no, 'ncpdp', NEW.id, 'status',
            COALESCE(OLD.status::text, ''),
            COALESCE(NEW.status::text, ''),
            'NCPDP claim status changed'
        );
    END IF;

    IF (TG_OP = 'INSERT' AND NEW.substatus_id IS NOT NULL) OR
       (TG_OP = 'UPDATE' AND COALESCE(OLD.substatus_id, '') != COALESCE(NEW.substatus_id, '')) THEN
        INSERT INTO form_ledger_billing_activity (
            localized_datetime, user_id, patient_id, site_id, 
            claim_no, form, form_id, field, 
            old_value, new_value, description
        ) VALUES (
            localized_ts, user_id, NEW.patient_id, NEW.site_id,
            NEW.claim_no, 'ncpdp', NEW.id, 'substatus_id',
            COALESCE(get_substatus_name(OLD.substatus_id), ''),
            COALESCE(get_substatus_name(NEW.substatus_id), ''),
            'Claim substatus changed'
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for med_claim status changes
DO $$ BEGIN
  PERFORM drop_all_function_signatures('log_med_claim_status_changes');
END $$;
CREATE OR REPLACE FUNCTION log_med_claim_status_changes()
RETURNS TRIGGER AS $$
DECLARE
    user_id integer;
    localized_ts timestamp with time zone;
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Determine the user ID from the context
    user_id := COALESCE(NEW.updated_by, NEW.created_by);
    
    -- Get localized timestamp
    localized_ts := get_user_localized_datetime(user_id);

    -- Handle status changes
    IF (TG_OP = 'INSERT' AND NEW.status IS NOT NULL) OR
       (TG_OP = 'UPDATE' AND COALESCE(OLD.status, '') != COALESCE(NEW.status, '')) THEN
        INSERT INTO form_ledger_billing_activity (
            localized_datetime, user_id, patient_id, site_id,
            claim_no, form, form_id, field,
            old_value, new_value, description
        ) VALUES (
            localized_ts, user_id, NEW.patient_id, NEW.site_id,
            NEW.claim_no, 'med_claim', NEW.id, 'status',
            COALESCE(OLD.status::text, ''),
            COALESCE(NEW.status::text, ''),
            'Medical claim status changed'
        );
    END IF;

    IF (TG_OP = 'INSERT' AND NEW.substatus_id IS NOT NULL) OR
       (TG_OP = 'UPDATE' AND COALESCE(OLD.substatus_id, '') != COALESCE(NEW.substatus_id, '')) THEN
        INSERT INTO form_ledger_billing_activity (
            localized_datetime, user_id, patient_id, site_id, 
            claim_no, form, form_id, field, 
            old_value, new_value, description
        ) VALUES (
            localized_ts, user_id, NEW.patient_id, NEW.site_id,
            NEW.claim_no, 'med_claim', NEW.id, 'substatus_id',
            COALESCE(get_substatus_name(OLD.substatus_id), ''),
            COALESCE(get_substatus_name(NEW.substatus_id), ''),
            'Claim substatus changed'
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for med_claim_1500 status changes
DO $$ BEGIN
  PERFORM drop_all_function_signatures('log_med_claim_1500_status_changes');
END $$;
CREATE OR REPLACE FUNCTION log_med_claim_1500_status_changes()
RETURNS TRIGGER AS $$
DECLARE
    user_id integer;
    localized_ts timestamp with time zone;
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Determine the user ID from the context
    user_id := COALESCE(NEW.updated_by, NEW.created_by);
    
    -- Get localized timestamp
    localized_ts := get_user_localized_datetime(user_id);

    -- Handle status changes
    IF (TG_OP = 'INSERT' AND NEW.status IS NOT NULL) OR
       (TG_OP = 'UPDATE' AND COALESCE(OLD.status, '') != COALESCE(NEW.status, '')) THEN
        INSERT INTO form_ledger_billing_activity (
            localized_datetime, user_id, patient_id, site_id,
            claim_no, form, form_id, field,
            old_value, new_value, description
        ) VALUES (
            localized_ts, user_id, NEW.patient_id, NEW.site_id,
            NEW.claim_no, 'med_claim_1500', NEW.id, 'status',
            COALESCE(OLD.status::text, ''),
            COALESCE(NEW.status::text, ''),
            'CMS-1500 claim status changed'
        );
    END IF;


    -- Handle substatus_id changes
    IF (TG_OP = 'INSERT' AND NEW.substatus_id IS NOT NULL) OR
       (TG_OP = 'UPDATE' AND COALESCE(OLD.substatus_id, '') != COALESCE(NEW.substatus_id, '')) THEN
        INSERT INTO form_ledger_billing_activity (
            localized_datetime, user_id, patient_id, site_id, 
            claim_no, form, form_id, field, 
            old_value, new_value, description
        ) VALUES (
            localized_ts, user_id, NEW.patient_id, NEW.site_id,
            NEW.claim_no, 'med_claim_1500', NEW.id, 'substatus_id',
            COALESCE(get_substatus_name(OLD.substatus_id), ''),
            COALESCE(get_substatus_name(NEW.substatus_id), ''),
            'Claim substatus changed'
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the triggers
CREATE OR REPLACE TRIGGER billing_invoice_activity_trigger
    AFTER INSERT OR UPDATE ON form_billing_invoice
    FOR EACH ROW
    EXECUTE FUNCTION log_billing_invoice_changes();

CREATE OR REPLACE TRIGGER ncpdp_activity_trigger
    AFTER INSERT OR UPDATE ON form_ncpdp
    FOR EACH ROW
    EXECUTE FUNCTION log_ncpdp_status_changes();

CREATE OR REPLACE TRIGGER med_claim_activity_trigger
    AFTER INSERT OR UPDATE ON form_med_claim
    FOR EACH ROW
    EXECUTE FUNCTION log_med_claim_status_changes();

CREATE OR REPLACE TRIGGER med_claim_1500_activity_trigger
    AFTER INSERT OR UPDATE ON form_med_claim_1500
    FOR EACH ROW
    EXECUTE FUNCTION log_med_claim_1500_status_changes();
