

CREATE OR REPLACE FUNCTION get_prior_auth_id(
    p_inventory_id INTEGER, 
    p_rx_id INTEGER, 
    p_insurance_id INTEGER
)
RETURNS INTEGER AS $$
DECLARE
    v_patient_id INTEGER;
    v_result INTEGER;
    v_inventory_type TEXT;
    v_drug_pa_id INTEGER;
    v_supplies_pa_id INTEGER;
    v_rental_pa_id INTEGER;
    v_nursing_pa_id INTEGER;
    v_order_id INTEGER;
BEGIN
    SELECT type INTO v_inventory_type
    FROM form_inventory
    WHERE id = p_inventory_id
    AND archived IS NOT TRUE
    AND deleted IS NOT TRUE;
    
    SELECT patient_id INTO v_patient_id
    FROM form_careplan_order_rx
    WHERE id = p_rx_id
    AND archived IS NOT TRUE
    AND deleted IS NOT TRUE;
    
    SELECT 
        order_id,
        drug_pa_id,
        supplies_pa_id
    INTO 
        v_order_id,
        v_drug_pa_id,
        v_supplies_pa_id
    FROM vw_order_raw
    WHERE rx_id = p_rx_id
    LIMIT 1;
    
    -- Get rental PA ID if applicable
    IF v_inventory_type = 'Equipment Rental' THEN
        SELECT pa_id INTO v_rental_pa_id
        FROM vw_rx_rental_items
        WHERE order_id = v_order_id AND inventory_id = p_inventory_id
        LIMIT 1;
    END IF;
  
    CASE 
        WHEN v_inventory_type = 'Equipment Rental' THEN
            v_result := v_rental_pa_id;
        WHEN v_inventory_type = 'Supply' THEN
            v_result := v_supplies_pa_id;
        WHEN v_inventory_type = 'Drug' THEN
            v_result := v_drug_pa_id;
        ELSE
        	v_result := NULL::integer;
    END CASE;

    IF v_result IS NOT NULL THEN
        SELECT pa.id INTO v_result
        FROM form_patient_prior_auth pa
        WHERE pa.id = v_result
        AND pa.insurance_id = p_insurance_id
        AND pa.archived IS NOT TRUE
        AND pa.deleted IS NOT TRUE
        AND pa.status_id = '5';
    END IF;

    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql STABLE;

DROP FUNCTION IF EXISTS get_payer_split_settings;
CREATE OR REPLACE FUNCTION get_payer_split_settings(p_payer_id integer)
RETURNS SETOF payer_split_settings AS $$
BEGIN
    IF p_payer_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Payer ID cannot be null',
            'Fetching payer ticket split settings',
            'FUNCTION',
            current_schema(),
            'get_payer_split_settings',
            jsonb_build_object(
                'p_payer_id', p_payer_id
            )
        );
        RAISE EXCEPTION 'Payer ID cannot be null';
    END IF;

    -- Use RETURN QUERY to properly return the result set
    RETURN QUERY
    SELECT
        CASE
            WHEN py.never_comp_seg = 'Yes' AND py.billing_method_id = 'ncpdp' THEN TRUE
            ELSE FALSE
        END as split_all,
        CASE
            WHEN py.billing_method_id = 'ncpdp' AND py.auto_split_noncompound = 'Yes' THEN TRUE
            ELSE FALSE
        END as split_by_medid,
        CASE
            WHEN COALESCE(py.split_items::jsonb ? 'By Prescription', false) = TRUE OR py.billing_method_id = 'ncpdp' THEN TRUE
            ELSE FALSE
        END as split_by_rx,
        CASE
            WHEN COALESCE(py.split_items::jsonb ? 'Supplies', false)= TRUE THEN TRUE
            ELSE FALSE
        END as split_by_supplies,
        CASE
            WHEN COALESCE(py.split_items::jsonb ? 'DME', false) = TRUE THEN TRUE
            ELSE FALSE
        END as split_by_dme,
        CASE
            WHEN COALESCE(py.split_pa::jsonb ? 'Yes', false) = TRUE THEN TRUE
            ELSE FALSE
        END as split_by_pa
    FROM form_payer py
    WHERE py.id = p_payer_id
    AND py.archived IS NOT TRUE
    AND py.deleted IS NOT TRUE;
    
    -- Return default values if no row was found
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT
            FALSE as split_all,
            FALSE as split_by_medid,
            FALSE as split_by_rx,
            FALSE as split_by_supplies,
            FALSE as split_by_dme,
            FALSE as split_by_pa;
    END IF;
END;
$$ LANGUAGE plpgsql VOLATILE;

DROP FUNCTION calculate_invoice_split_no;
CREATE OR REPLACE FUNCTION calculate_invoice_split_no(
  p_payer_id integer,
  p_rx_guid uuid,
  p_supply_guid uuid,
  p_dme_guid uuid,
  p_pa_guid uuid,
  p_medid_guid uuid,
  p_rx_no text DEFAULT NULL,
  p_inventory_type text DEFAULT NULL,
  p_medid text DEFAULT NULL,
  p_pa_id integer DEFAULT NULL
) RETURNS text AS $$
DECLARE
  v_payer_split_settings record;
  v_split_uuid uuid;
BEGIN
  -- Get payer split settings
  SELECT * INTO v_payer_split_settings
  FROM get_payer_split_settings(p_payer_id)
  LIMIT 1;
  
  -- Determine the split UUID based on settings
  IF v_payer_split_settings.split_all THEN
    -- Individual - generate a new UUID for each charge line
    v_split_uuid := gen_random_uuid();
  ELSIF v_payer_split_settings.split_by_medid AND p_medid IS NOT NULL THEN
    -- Group by medication ID - use the medid GUID
    v_split_uuid := p_medid_guid;
  ELSIF v_payer_split_settings.split_by_rx AND p_rx_no IS NOT NULL THEN
    -- Group by prescription number - use the RX GUID
    v_split_uuid := p_rx_guid;
  ELSIF v_payer_split_settings.split_by_supplies AND p_inventory_type = 'Supply' THEN
    -- Group supplies together - use the supplies GUID
    v_split_uuid := p_supply_guid;
  ELSIF v_payer_split_settings.split_by_dme AND p_inventory_type = 'Equipment Rental' THEN
    -- Group DME together - use the DME GUID
    v_split_uuid := p_dme_guid;
  ELSIF v_payer_split_settings.split_by_pa AND p_pa_id IS NOT NULL THEN
    -- Group by PA number - use the PA GUID
    v_split_uuid := p_pa_guid;
  ELSE
    -- Default grouping - create a new UUID for the default group
    v_split_uuid := gen_random_uuid();
  END IF;
  
  RETURN v_split_uuid::text;
END;
$$ LANGUAGE plpgsql STABLE;

DO $$ BEGIN
  PERFORM drop_all_function_signatures('generate_split_invoice_charge_lines');
END $$;
CREATE OR REPLACE FUNCTION generate_split_invoice_charge_line(
  p_charge_nos text[],
  p_amounts numeric[],
  p_next_insurance_id integer,
  p_split_no text
) RETURNS charge_line_with_split[] AS $$
DECLARE
  v_split_charge_lines charge_line_with_split[];
  v_error_record record;
  v_rx_guid uuid := gen_random_uuid();
  v_supply_guid uuid := gen_random_uuid();
  v_dme_guid uuid := gen_random_uuid();
  v_pa_guid uuid := gen_random_uuid();
  v_medid_guid uuid := gen_random_uuid();
BEGIN

  IF p_next_insurance_id IS NULL THEN
    -- Log error to billing_error_log
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        'Next insurance id is missing',
        'Error generating split invoice charge lines',
        'FUNCTION',
        current_schema(),
        'billing_invoice',
        jsonb_build_object(
            'function_name', 'generate_split_invoice_charge_line',
            'p_charge_nos', p_charge_nos,
            'p_amounts', p_amounts,
            'p_next_insurance_id', p_next_insurance_id,
            'p_split_no', p_split_no
        )
    );
    
    RAISE EXCEPTION 'Next insurance id is missing';
  END IF;

    BEGIN
      WITH charge_lines AS (
        SELECT 
          lcl.id,
          calculate_invoice_split_no(
              lcl.payer_id,
              v_rx_guid,
              v_supply_guid,
              v_dme_guid,
              v_pa_guid,
              v_medid_guid,
              lcl.rx_no,
              lcl.inventory_type,
              inv.medid,
              get_prior_auth_id(
              lcl.inventory_id::INTEGER,
              lcl.order_rx_id::INTEGER,
              p_next_insurance_id::INTEGER
              )
          ) AS calc_invoice_split_no,
          lcl.patient_id,
          lcl.order_rx_id,
          lcl.fill_number,
          lcl.rental_id,
          lcl.charge_no,
          lcl.parent_charge_no,
          lcl.master_charge_no,
          lcl.encounter_id,
          lcl.ticket_no,
          lcl.ticket_item_no,
          lcl.show_cost,
          lcl.compound_no,
          lcl.is_primary_drug,
          lcl.is_primary_drug_ncpdp,
          lcl.site_id,
          lcl.rx_no,
          lcl.inventory_type_filter,
          lcl.inventory_id,
          lcl.description,
          lcl.inventory_type,
          lclnew.hcpc_code,
          lcl.gcn_seqno,
          lcl.ndc,
          lcl.formatted_ndc,
          lcl.upc,
          lcl.upin,
          lcl.bill_quantity,
          lcl.metric_unit_each,
          lclnew.charge_quantity_ea,
          lclnew.charge_quantity,
          lclnew.charge_unit,
          lcl.hcpc_quantity,
          lcl.hcpc_unit,
          lcl.metric_quantity,
          lcl.billing_unit_id,
          lcl.rental_type,
          lcl.frequency_code,
          lclnew.insurance_id,
          lclnew.payer_id,
          lclnew.billing_method_id,
          lclnew.pricing_source,
          lclnew.shared_contract_id,
          lclnew.modifier_1,
          lclnew.modifier_2,
          lclnew.modifier_3,
          lclnew.modifier_4,
          lclnew.billed,
          lclnew.calc_billed_ea,
          CASE
              WHEN lcl.master_charge_no = lcl.charge_no THEN lclnew.dispense_fee
              ELSE 0.00
          END AS dispense_fee,
          lclnew.incv_amt_sub,
          lclnew.gross_amount_due,
          lclnew.cost_basis,
          lclnew.pt_pd_amt_sub,
          lclnew.expected,
          lclnew.awp_price,
          lclnew.calc_expected_ea,
          lclnew.list_price,
          lclnew.calc_list_ea,
          lcl.total_cost,
          lcl.calc_cost_ea,
          lclnew.copay,
          lclnew.total_balance_due,
          lclnew.revenue_code_id
          FROM form_ledger_charge_line lcl
          INNER JOIN form_inventory inv ON inv.id = lcl.inventory_id
          AND inv.archived IS NOT TRUE
          AND inv.deleted IS NOT TRUE
          CROSS JOIN LATERAL create_ledger_charge_line(
          lcl.inventory_id::INTEGER,
          p_next_insurance_id::INTEGER,
          lcl.site_id::INTEGER,
          lcl.patient_id::INTEGER,
          lcl.bill_quantity::NUMERIC,
          CASE 
              WHEN lcl.is_primary_drug = 'Yes' THEN TRUE 
              ELSE FALSE 
          END::BOOLEAN,
          lcl.compound_no::text,
          lcl.order_rx_id::INTEGER,
          lcl.rental_id::INTEGER,
          lcl.ticket_no::TEXT,
          lcl.ticket_item_no::TEXT,
          lcl.encounter_id::INTEGER,
          FALSE::BOOLEAN,
          lcl.parent_charge_no::text
          ) lclnew
          WHERE lcl.charge_no = ANY(p_charge_nos)
      )
      SELECT array_agg(lcl)
      INTO v_split_charge_lines
      FROM charge_lines lcl;
  
    RETURN v_split_charge_lines;

  EXCEPTION WHEN OTHERS THEN
        -- Log the error
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Transaction failed generating split invoice charge lines',
            'FUNCTION',
            current_schema(),
            'billing_invoice',
            jsonb_build_object(
                'function_name', 'generate_split_invoice_charge_line',
                'p_charge_nos', p_charge_nos,
                'p_amounts', p_amounts,
                'p_next_insurance_id', p_next_insurance_id,
                'p_split_no', p_split_no
            )
        );
        
        RAISE; -- Re-throw the exception to rollback the transaction
    END;
END;
$$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION jsonb_camel_to_snake(data jsonb)
RETURNS jsonb AS $$
DECLARE
    result jsonb := '{}';
    key text;
    value jsonb;
    snake_key text;
BEGIN
    -- Handle null input
    IF data IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Handle non-object types (arrays, primitives)
    IF jsonb_typeof(data) != 'object' THEN
        IF jsonb_typeof(data) = 'array' THEN
            -- Recursively process array elements
            SELECT jsonb_agg(jsonb_camel_to_snake(elem))
            INTO result
            FROM jsonb_array_elements(data) AS elem;
            RETURN result;
        ELSE
            -- Return primitive values as-is
            RETURN data;
        END IF;
    END IF;
    
    -- Process object keys
    FOR key, value IN SELECT * FROM jsonb_each(data)
    LOOP
        -- Convert camelCase to snake_case
        snake_key := regexp_replace(key, '([a-z])([A-Z])', '\1_\2', 'g');
        snake_key := lower(snake_key);
        
        -- Recursively process nested objects/arrays
        result := result || jsonb_build_object(snake_key, jsonb_camel_to_snake(value));
    END LOOP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

CREATE OR REPLACE FUNCTION jsonb_snake_to_camel(data jsonb)
RETURNS jsonb AS $$
DECLARE
    result jsonb := '{}';
    key text;
    value jsonb;
    camel_key text;
BEGIN
    -- Handle null input
    IF data IS NULL THEN
        RETURN NULL;
    END IF;

    -- Handle non-object types (arrays, primitives)
    IF jsonb_typeof(data) != 'object' THEN
        IF jsonb_typeof(data) = 'array' THEN
            -- Recursively process array elements
            SELECT jsonb_agg(jsonb_snake_to_camel(elem))
            INTO result
            FROM jsonb_array_elements(data) AS elem;
            RETURN result;
        ELSE
            -- Return primitive values as-is
            RETURN data;
        END IF;
    END IF;

    -- Process object keys
    FOR key, value IN SELECT * FROM jsonb_each(data)
    LOOP
        -- Convert snake_case to camelCase
        -- Split on underscores, capitalize first letter of each word except the first
        SELECT string_agg(
            CASE 
                WHEN row_number() OVER () = 1 THEN lower(word)
                ELSE initcap(word)
            END, 
            ''
        )
        INTO camel_key
        FROM (
            SELECT unnest(string_to_array(key, '_')) AS word
        ) words;
        
        -- Recursively process nested objects/arrays
        result := result || jsonb_build_object(camel_key, jsonb_snake_to_camel(value));
    END LOOP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql IMMUTABLE;