
CREATE OR REPLACE FUNCTION get_benefits_data(root_claim TEXT)
RETURNS ncpdp_cob_benefit[] AS $BODY$
DECLARE
    v_start_time timestamp;
    v_execution_time interval;
    v_error_message text;
    v_params jsonb;
    v_result ncpdp_cob_benefit[];
    v_payer_id int;
BEGIN
    v_start_time := clock_timestamp();

    v_params := jsonb_build_object(
        'root_claim', root_claim
    );

    BEGIN
        PERFORM log_billing_function(
        'get_benefits_data'::tracked_function,
        v_params::jsonb,
        NULL::jsonb,
        NULL::text,
        clock_timestamp() - v_start_time
        );

        IF root_claim IS NULL THEN
            INSERT INTO billing_error_log (
                error_message, error_context, error_type, schema_name, table_name, additional_details
            ) VALUES (
                'Parent Claim # cannot be null',
                'Validating required parameters in get_benefits_data',
                'FUNCTION',
                current_schema(), 'form_ncpdp',
                v_params
            );
            RAISE EXCEPTION 'Parent Claim # cannot be null';
        END IF;

        -- Get payer_id from primary claim
        SELECT payer_id INTO v_payer_id
        FROM get_claim_hierarchy(root_claim)
        WHERE depth = 0
        LIMIT 1;

        -- Get any explicitly provided benefit data from primary claim
        WITH claim_hierarchy AS (
            SELECT * FROM get_claim_hierarchy(root_claim)
        ),
        benefit_amounts AS (
            SELECT 
                ch.claim_no::text as claim_no,
                ch.depth,
                (unnested.bft).benefit_stage_qualifier::text as benefit_stage_qualifier,
                (unnested.bft).benefit_stage_amount::numeric as benefit_stage_amount
            FROM claim_hierarchy ch
            CROSS JOIN LATERAL (
                SELECT unnest(COALESCE(ch.bft_amt, '{}'::bft_amt_type[])) AS bft
            ) AS unnested
            WHERE ch.ignore_benefits IS NOT TRUE
                AND ch.depth = 0
        )
        SELECT array_agg((ba.benefit_stage_qualifier, ba.benefit_stage_amount)::ncpdp_cob_benefit) INTO v_result
        FROM (
            SELECT 
                benefit_stage_qualifier::text as benefit_stage_qualifier,
                benefit_stage_amount::numeric as benefit_stage_amount
            FROM benefit_amounts
            WHERE benefit_stage_amount > 0
            ORDER BY benefit_stage_qualifier
            LIMIT 4
        ) ba;

        -- Fallback: infer benefit stages from payment values if none explicitly set
        IF (v_result IS NULL OR array_length(v_result, 1) IS NULL) THEN
            WITH claim_hierarchy AS (
                SELECT * FROM get_claim_hierarchy(root_claim)
            ),
            primary_claim AS (
                SELECT 
                    total_paid::numeric as total_paid,
                    pt_pay_amt::numeric as pt_pay_amt,
                    amt_apld_ded::numeric as amt_apld_ded,
                    coinsur_amt::numeric as coinsur_amt,
                    ing_cst_paid::numeric as ing_cst_paid,
                    ing_cst_sub::numeric as ing_cst_sub
                FROM claim_hierarchy
                WHERE depth = 0
                LIMIT 1
            )
            SELECT array_agg(sub) INTO v_result
            FROM (
                SELECT
                    CASE
                        WHEN is_medicare_part_d(v_payer_id) THEN
                            CASE
                                WHEN pc.pt_pay_amt > 0 AND pc.ing_cst_paid = 0 THEN '01'::text
                                WHEN pc.pt_pay_amt > 0 AND pc.ing_cst_paid > 0 AND 
                                     (pc.pt_pay_amt / NULLIF(pc.ing_cst_sub, 0)) BETWEEN 0.2 AND 0.35 THEN '03'::text
                                WHEN pc.pt_pay_amt = 0 AND pc.ing_cst_paid = pc.ing_cst_sub THEN '04'::text
                                ELSE '02'::text
                            END
                        ELSE '02'::text
                    END::text as benefit_stage_qualifier,
                    CASE
                        WHEN is_medicare_part_d(v_payer_id) THEN
                            CASE
                                WHEN pc.pt_pay_amt > 0 AND pc.ing_cst_paid = 0 THEN pc.pt_pay_amt::numeric
                                WHEN pc.pt_pay_amt > 0 AND pc.ing_cst_paid > 0 AND 
                                     (pc.pt_pay_amt / NULLIF(pc.ing_cst_sub, 0)) BETWEEN 0.2 AND 0.35 THEN pc.pt_pay_amt::numeric
                                WHEN pc.pt_pay_amt = 0 AND pc.ing_cst_paid = pc.ing_cst_sub THEN pc.total_paid::numeric
                                ELSE pc.total_paid::numeric
                            END
                        ELSE pc.total_paid::numeric
                    END::numeric as benefit_stage_amount
                FROM primary_claim pc
                WHERE (pc.total_paid > 0 OR pc.pt_pay_amt > 0)
            ) sub;
        END IF;

        PERFORM log_billing_function(
            'get_benefits_data'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            NULL::text,
            clock_timestamp() - v_start_time
        );

        RETURN v_result;

    EXCEPTION WHEN OTHERS THEN
        v_error_message := SQLERRM;

        INSERT INTO billing_error_log (
            error_message, error_context, error_type, schema_name, table_name, additional_details
        ) VALUES (
            v_error_message,
            'Exception in get_benefits_data',
            'FUNCTION',
            current_schema(), 'form_ncpdp',
            v_params
        );

        PERFORM log_billing_function(
            'get_benefits_data'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            v_error_message::text,
            clock_timestamp() - v_start_time
        );

        RAISE;
    END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;
