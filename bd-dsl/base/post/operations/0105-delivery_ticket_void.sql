
CREATE OR <PERSON><PERSON>LACE FUNCTION delivery_ticket_void()
RETURNS TRIGGER AS $$
BEGIN
    IF COALESCE(NEW.void, 'No') = 'No' THEN
        RETURN NEW;
    END IF;

    IF OLD.void IS DISTINCT FROM 'Yes' THEN
        NEW.status := 'voided';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trg_delivery_ticket_void ON form_careplan_delivery_tick;
CREATE TRIGGER trg_delivery_ticket_void
    BEFORE UPDATE ON form_careplan_delivery_tick
    FOR EACH ROW
    EXECUTE FUNCTION delivery_ticket_void();

CREATE OR REPLACE FUNCTION delivery_ticket_item_void()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'voided' THEN
        IF OLD.status IS DISTINCT FROM 'voided' THEN
            UPDATE form_careplan_dt_wt_pulled wtp
            SET void = 'Yes'
            WHERE wtp.archived IS NOT TRUE
                AND wtp.deleted IS NOT TRUE
                AND wtp.ticket_item_no = NEW.ticket_item_no
                AND wtp.void IS DISTINCT FROM 'Yes';
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trg_delivery_ticket_item_void ON form_careplan_dt_item;
CREATE TRIGGER trg_delivery_ticket_item_void
    AFTER UPDATE ON form_careplan_dt_item
    FOR EACH ROW
    EXECUTE FUNCTION delivery_ticket_item_void();
