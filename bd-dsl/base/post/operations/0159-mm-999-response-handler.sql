-- Medical Claim 999 Response Handler
-- Parses 999 (Acknowledgment) responses and populates form_med_claim_resp and subforms

DROP FUNCTION IF EXISTS parse_999_response;
CREATE OR REPLACE FUNCTION parse_999_response(
    p_response_id INTEGER,
    p_response_json JSONB,
    p_created_by INTEGER,
    p_created_dt TIMESTAMP
) RETURNS VOID AS $$
DECLARE
    v_main_record_id INTEGER;
    v_subform_id INTEGER;
    
    v_claim_no INTEGER;
    v_patient_id INTEGER;
    v_site_id INTEGER;
    v_payer_id INTEGER;
    
    -- Main 999 record fields
    v_control_number TEXT;
    v_customer_claim_number TEXT;
    v_patient_control_number TEXT;
    v_correlation_id TEXT;
    v_submitter_id TEXT;
    v_time_of_response TIMESTAMP;
    v_claim_type TEXT;
    v_payer_id_response TEXT;
    v_format_version TEXT;
    v_rhclaim_number TEXT;
    v_status TEXT;
    v_edit_status TEXT;
    
    -- Fields from original response log record
    v_request_raw_x12 TEXT;
    v_response_raw_x12 TEXT;
    v_raw_report_url TEXT;
    v_s3_filehash TEXT;
    
    -- Meta fields
    v_meta_submitter_id TEXT;
    v_meta_sender_id TEXT;
    v_meta_biller_id TEXT;
    v_trace_id TEXT;
    v_application_mode TEXT;
    
    -- Array elements for processing
    v_payer JSONB;
    v_edit_response JSONB;
    v_error JSONB;
    v_failure JSONB;
    
    v_i INTEGER;
    
    -- Logging variables
    v_start_time TIMESTAMP;
    v_execution_time INTERVAL;
    v_error_message TEXT;
    v_params JSONB;
    v_result_info JSONB;
    v_claims_processed INTEGER := 0;
BEGIN
    -- Record start time
    v_start_time := clock_timestamp();
    RAISE LOG 'parse_999_response: Start processing response_id=%, created_by=%', p_response_id, p_created_by;
    
    -- Build parameters JSON for logging
    v_params := jsonb_build_object(
        'response_id', p_response_id,
        'response_type', '999',
        'created_by', p_created_by
    );
    
    BEGIN  -- Start exception block
        -- Log function call start
        BEGIN
            PERFORM log_billing_function(
                'parse_999_response'::tracked_function,
                v_params::jsonb,
                NULL::jsonb,
                NULL::text,
                clock_timestamp() - v_start_time
            );
        EXCEPTION WHEN OTHERS THEN
            -- Ignore logging errors
            NULL;
        END;
        
        -- Get additional fields from original response log record
        RAISE LOG 'parse_999_response: Fetching response log record for response_id=%', p_response_id;
        SELECT request_raw_x12, response_raw_x12, raw_report_url, s3_filehash
        INTO v_request_raw_x12, v_response_raw_x12, v_raw_report_url, v_s3_filehash
        FROM form_med_claim_resp_log
        WHERE id = p_response_id
            AND deleted IS NOT TRUE
            AND archived IS NOT TRUE;
        
        -- Extract main claim reference data
        v_control_number := p_response_json->>'controlNumber';
        v_customer_claim_number := p_response_json->'claimReference'->>'customerClaimNumber';
        v_patient_control_number := p_response_json->'claimReference'->>'patientControlNumber';
        v_correlation_id := p_response_json->'claimReference'->>'correlationId';
        v_submitter_id := p_response_json->'claimReference'->>'submitterId';
        RAISE LOG 'parse_999_response: Extracted controlNumber=%, customer_claim_number=%, patient_control_number=%', v_control_number, v_customer_claim_number, v_patient_control_number;
        
        BEGIN
            v_time_of_response := (p_response_json->'claimReference'->>'timeOfResponse')::TIMESTAMP;
        EXCEPTION WHEN OTHERS THEN
            v_time_of_response := NULL;
            RAISE WARNING 'Failed to convert timeOfResponse to timestamp: %, using NULL instead', 
                         p_response_json->'claimReference'->>'timeOfResponse';
        END;
        
        v_claim_type := p_response_json->'claimReference'->>'claimType';
        v_payer_id_response := p_response_json->'claimReference'->>'payerID';
        v_format_version := p_response_json->'claimReference'->>'formatVersion';
        v_rhclaim_number := p_response_json->'claimReference'->>'rhclaimNumber';
        v_status := p_response_json->>'status';
        v_edit_status := p_response_json->>'editStatus';
        
        -- Find the original claim using customer claim number or patient control number
        RAISE LOG 'parse_999_response: Looking up original claim for customer_claim_number=% or patient_control_number=%', v_customer_claim_number, v_patient_control_number;
        -- First try form_med_claim_info for patient_control_number match
        SELECT mc.claim_no, mc.patient_id, mc.site_id, mc.payer_id 
        INTO v_claim_no, v_patient_id, v_site_id, v_payer_id
        FROM form_med_claim mc
        JOIN sf_form_med_claim_to_med_claim_info sf_ci ON sf_ci.form_med_claim_fk = mc.id
            AND sf_ci.delete IS NOT TRUE
            AND sf_ci.archive IS NOT TRUE
        JOIN form_med_claim_info mci ON mci.id = sf_ci.form_med_claim_info_fk
            AND mci.deleted IS NOT TRUE
            AND mci.archived IS NOT TRUE
        WHERE (mci.patient_control_number = v_customer_claim_number OR 
               mci.patient_control_number = v_patient_control_number)
            AND mc.deleted IS NOT TRUE
            AND mc.archived IS NOT TRUE
        LIMIT 1;
        
        -- If no matching claim found, log error but continue processing
        IF v_claim_no IS NULL THEN
            -- Log to billing error log
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'No matching claim found for customer_claim_number: ' || COALESCE(v_customer_claim_number, 'NULL') ||
                ' or patient_control_number: ' || COALESCE(v_patient_control_number, 'NULL'),
                'Claim lookup failed in parse_999_response',
                'DATA_NOT_FOUND',
                current_schema(),
                'form_med_claim_info',
                jsonb_build_object(
                    'customer_claim_number', v_customer_claim_number,
                    'patient_control_number', v_patient_control_number,
                    'response_id', p_response_id
                )
            );
            
            RAISE WARNING 'No matching claim found for customer_claim_number: % or patient_control_number: %, skipping claim linking', 
                        v_customer_claim_number, v_patient_control_number;
            RAISE LOG 'parse_999_response: No matching claim found for response_id=%', p_response_id;
        ELSE
            RAISE LOG 'parse_999_response: Found claim_no=% for response_id=%', v_claim_no, p_response_id;
        END IF;
        
        -- Insert main 999 response record
        RAISE LOG 'parse_999_response: Inserting main 999 response record for response_id=%', p_response_id;
        INSERT INTO form_med_claim_resp (
            response_id,
            claim_no,
            patient_id,
            control_number,
            trading_partner_service_id,
            status,
            edit_status,
            response_type,
            request_raw_x12,
            request_raw_json,
            response_raw_x12,
            response_raw_json,
            created_by,
            created_on,
            archived,
            deleted
        ) VALUES (
            p_response_id,
            v_claim_no,
            v_patient_id,
            v_control_number,
            p_response_json->>'tradingPartnerServiceId',
            v_status,
            v_edit_status,
            'Submission',
            v_request_raw_x12,
            NULL,
            v_response_raw_x12,
            p_response_json::TEXT,
            p_created_by,
            p_created_dt,
            FALSE,
            FALSE
        ) RETURNING id INTO v_main_record_id;
        RAISE LOG 'parse_999_response: Inserted main record id=% for response_id=%', v_main_record_id, p_response_id;
        
        v_claims_processed := 1;
        
        -- Process claim reference subform (multi: false)
        IF p_response_json ? 'claimReference' THEN
            RAISE LOG 'parse_999_response: Processing claim reference subform for response_id=%', p_response_id;
            INSERT INTO form_med_claim_resp_ref (
                time_of_response,
                claim_type,
                format_version,
                rhclaim_number,
                correlation_id,
                submitter_id,
                payer_id,
                customer_claim_number,
                patient_control_number,
                created_by,
                created_on,
                archived,
                deleted
            ) VALUES (
                v_time_of_response,
                v_claim_type,
                v_format_version,
                v_rhclaim_number,
                v_correlation_id,
                v_submitter_id,
                v_payer_id_response,
                v_customer_claim_number,
                v_patient_control_number,
                p_created_by,
                p_created_dt,
                FALSE,
                FALSE
            ) RETURNING id INTO v_subform_id;
            RAISE LOG 'parse_999_response: Inserted claim reference subform id=% for main_record_id=%', v_subform_id, v_main_record_id;
            
            -- Link to main record
            INSERT INTO sf_form_med_claim_resp_to_med_claim_resp_ref (
                form_med_claim_resp_fk,
                form_med_claim_resp_ref_fk,
                archive,
                delete
            ) VALUES (
                v_main_record_id,
                v_subform_id,
                FALSE,
                FALSE
            );
        END IF;
        
        -- Process payer information (multi: false)
        IF p_response_json ? 'payer' THEN
            v_payer := p_response_json->'payer';
            RAISE LOG 'parse_999_response: Processing payer subform for response_id=%', p_response_id;
            INSERT INTO form_med_claim_resp_ch_pyr (
                payer_name,
                payer_id,
                created_by,
                created_on,
                archived,
                deleted
            ) VALUES (
                v_payer->>'payerName',
                v_payer->>'payerID',
                1,
                CURRENT_TIMESTAMP,
                FALSE,
                FALSE
            ) RETURNING id INTO v_subform_id;
            RAISE LOG 'parse_999_response: Inserted payer subform id=% for main_record_id=%', v_subform_id, v_main_record_id;
            
            -- Link to main record
            INSERT INTO sf_form_med_claim_resp_to_med_claim_resp_ch_pyr (
                form_med_claim_resp_fk,
                form_med_claim_resp_ch_pyr_fk,
                archive,
                delete
            ) VALUES (
                v_main_record_id,
                v_subform_id,
                FALSE,
                FALSE
            );
        END IF;
        
        -- Process errors subform (multi: true)
        IF p_response_json ? 'errors' AND 
           jsonb_array_length(p_response_json->'errors') > 0 THEN
            RAISE LOG 'parse_999_response: Processing % error subforms for response_id=%', jsonb_array_length(p_response_json->'errors'), p_response_id;
            FOR v_i IN 0..jsonb_array_length(p_response_json->'errors') - 1 LOOP
                v_error := p_response_json->'errors'->v_i;
                
                INSERT INTO form_med_claim_resp_err (
                    field,
                    value,
                    code,
                    description,
                    location,
                    created_by,
                    created_on,
                    archived,
                    deleted
                ) VALUES (
                    v_error->>'field',
                    v_error->>'value',
                    v_error->>'code',
                    v_error->>'description',
                    v_error->>'location',
                    1,
                    CURRENT_TIMESTAMP,
                    FALSE,
                    FALSE
                ) RETURNING id INTO v_subform_id;
                RAISE LOG 'parse_999_response: Inserted error subform id=% for main_record_id=%', v_subform_id, v_main_record_id;
                
                -- Link to main record
                INSERT INTO sf_form_med_claim_resp_to_med_claim_resp_err (
                    form_med_claim_resp_fk,
                    form_med_claim_resp_err_fk,
                    archive,
                    delete
                ) VALUES (
                    v_main_record_id,
                    v_subform_id,
                    FALSE,
                    FALSE
                );
            END LOOP;
        END IF;
        
        -- Process edit responses subform (multi: true)
        IF p_response_json ? 'editResponses' AND 
           jsonb_array_length(p_response_json->'editResponses') > 0 THEN
            RAISE LOG 'parse_999_response: Processing % editResponses subforms for response_id=%', jsonb_array_length(p_response_json->'editResponses'), p_response_id;
            FOR v_i IN 0..jsonb_array_length(p_response_json->'editResponses') - 1 LOOP
                v_edit_response := p_response_json->'editResponses'->v_i;
                
                INSERT INTO form_med_claim_resp_edit (
                    qualifier_code,
                    error_description,
                    field_index,
                    edit_name,
                    edit_activity,
                    reference_id,
                    claim_core_path,
                    allow_override,
                    element,
                    segment,
                    loop,
                    bad_data,
                    phase_id,
                    created_by,
                    created_on,
                    archived,
                    deleted
                ) VALUES (
                    v_edit_response->>'qualifierCode',
                    v_edit_response->>'errorDescription',
                    v_edit_response->>'fieldIndex',
                    v_edit_response->>'editName',
                    v_edit_response->>'editActivity',
                    v_edit_response->>'referenceID',
                    v_edit_response->>'claimCorePath',
                    v_edit_response->>'allowOverride',
                    v_edit_response->>'element',
                    v_edit_response->>'segment',
                    v_edit_response->>'loop',
                    v_edit_response->>'badData',
                    v_edit_response->>'phaseID',
                    1,
                    CURRENT_TIMESTAMP,
                    FALSE,
                    FALSE
                ) RETURNING id INTO v_subform_id;
                RAISE LOG 'parse_999_response: Inserted editResponse subform id=% for main_record_id=%', v_subform_id, v_main_record_id;
                
                -- Link to main record
                INSERT INTO sf_form_med_claim_resp_to_med_claim_resp_edit (
                    form_med_claim_resp_fk,
                    form_med_claim_resp_edit_fk,
                    archive,
                    delete
                ) VALUES (
                    v_main_record_id,
                    v_subform_id,
                    FALSE,
                    FALSE
                );
            END LOOP;
        END IF;
        
        -- Process failure information (multi: false)
        IF p_response_json ? 'failure' THEN
            v_failure := p_response_json->'failure';
            RAISE LOG 'parse_999_response: Processing failure subform for response_id=%', p_response_id;
            INSERT INTO form_med_claim_resp_fail (
                description,
                code,
                created_by,
                created_on,
                archived,
                deleted
            ) VALUES (
                v_failure->>'description',
                v_failure->>'code',
                1,
                CURRENT_TIMESTAMP,
                FALSE,
                FALSE
            ) RETURNING id INTO v_subform_id;
            RAISE LOG 'parse_999_response: Inserted failure subform id=% for main_record_id=%', v_subform_id, v_main_record_id;
            
            -- Link to main record
            INSERT INTO sf_form_med_claim_resp_to_med_claim_resp_fail (
                form_med_claim_resp_fk,
                form_med_claim_resp_fail_fk,
                archive,
                delete
            ) VALUES (
                v_main_record_id,
                v_subform_id,
                FALSE,
                FALSE
            );
        END IF;
        
        -- Process response meta subform (multi: false)
        IF p_response_json ? 'meta' THEN
            v_meta_submitter_id := p_response_json->'meta'->>'submitterId';
            v_meta_sender_id := p_response_json->'meta'->>'senderId';
            v_meta_biller_id := p_response_json->'meta'->>'billerId';
            v_trace_id := p_response_json->'meta'->>'traceId';
            v_application_mode := p_response_json->'meta'->>'applicationMode';
            RAISE LOG 'parse_999_response: Processing meta subform for response_id=%', p_response_id;
            INSERT INTO form_med_claim_resp_ch_meta (
                submitter_id,
                sender_id,
                biller_id,
                trace_id,
                application_mode,
                created_by,
                created_on,
                archived,
                deleted
            ) VALUES (
                v_meta_submitter_id,
                v_meta_sender_id,
                v_meta_biller_id,
                v_trace_id,
                v_application_mode,
                1,
                CURRENT_TIMESTAMP,
                FALSE,
                FALSE
            ) RETURNING id INTO v_subform_id;
            RAISE LOG 'parse_999_response: Inserted meta subform id=% for main_record_id=%', v_subform_id, v_main_record_id;
            
            -- Link to main record
            INSERT INTO sf_form_med_claim_resp_to_med_claim_resp_ch_meta (
                form_med_claim_resp_fk,
                form_med_claim_resp_ch_meta_fk,
                archive,
                delete
            ) VALUES (
                v_main_record_id,
                v_subform_id,
                FALSE,
                FALSE
            );
        END IF;
        
        -- Build result info for successful logging
        v_result_info := jsonb_build_object(
            'claims_processed', v_claims_processed,
            'control_number', v_control_number,
            'main_record_id', v_main_record_id,
            'status', v_status
        );
        
        -- Log successful completion
        BEGIN
            PERFORM log_billing_function(
                'parse_999_response'::tracked_function,
                v_params,
                v_result_info,
                NULL::text,
                clock_timestamp() - v_start_time
            );
        EXCEPTION WHEN OTHERS THEN
            -- Ignore logging errors
            NULL;
        END;
        
        RAISE NOTICE 'Completed 999 response processing for response_id: %, status: %', p_response_id, v_status;
        RAISE LOG 'parse_999_response: Completed processing for response_id=%, status=%, main_record_id=%', p_response_id, v_status, v_main_record_id;
        
    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        v_error_message := SQLERRM;
        
        -- Log to billing error log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            v_error_message,
            'Exception in parse_999_response',
            'FUNCTION',
            current_schema(),
            'form_med_claim_resp',
            v_params
        );
        
        -- Log error to function log
        BEGIN
            PERFORM log_billing_function(
                'parse_999_response'::tracked_function,
                v_params::jsonb,
                NULL::jsonb,
                v_error_message::text,
                clock_timestamp() - v_start_time
            );
        EXCEPTION WHEN OTHERS THEN
            -- Ignore logging errors
            NULL;
        END;
        
        RAISE WARNING 'Error parsing 999 response (response_id: %): %', p_response_id, v_error_message;
        RAISE LOG 'parse_999_response: ERROR for response_id=%: %', p_response_id, v_error_message;
        RAISE;
    END;
END;
$$ LANGUAGE plpgsql;
