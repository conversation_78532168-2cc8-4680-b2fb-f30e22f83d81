CREATE OR REPLACE FUNCTION get_insurance_claim_settings(
  p_insurance_id integer,
  p_site_id integer,
  p_previous_payer_id integer
) RETURNS TABLE (
	insurance_id INTEGER,
	payer_id INTEGER,
	patient_id INTEGER,
	type_id text,
	mrn text,
	bin text,
	pcn text,
	group_number text,
	person_code text,
	cardholder_id text,
	medicaid_number text,
	medicare_number text,
	pt_rel_code text,
	medicaid_agency_number text,
	medigap_id text,
	plan_name text,
	patient_id_qualifier text,
	patient_claim_id text,
	home_plan text,
	beneficiary_fname text,
	beneficiary_lname text,
	beneficiary_mname text,
	beneficiary_gender text,
	beneficiary_dob text,
	ncpdp_pharmacy_qualifier_id text,
	ncpdp_pharmacy_id text,
	prescriber_no_field_id text,
	pcp_no_field_id text,
	default_service_place_id text,
	software_vendor_id text,
	default_dispense_fee numeric,
	base_ing_cost_on_id text,
	compound_qualifier_id text,
	compound_sub_clarification_code text,
	noncompound_drug_qualifier_id text,
	service_qualifier_id text,
	never_comp_seg text,
	auto_split_noncompound text,
	send_dx_code text,
	send_dx_period text,
	transmit_grp_no_reversal text,
	transmit_card_id_reversal text,
	swap_uc_and_gross text,
	send_uom text,
	send_pharmacist_npi text,
	send_blank_person_code text,
	send_pt_email text,
	ncpdp_e1_prov_required text,
	ncpdp_sec_claims_ingred_cost text,
	ncpdp_sec_claims_gross_amount_due text,
	ncpdp_send_primary_payer_amt_paid text,
	ncpdp_default_other_coverage_code text,
	ncpdp_send_sec_charge text,
	ncpdp_pt_paid_amount_dx text,
	default_coverage integer,
	req_auth text,
	req_auth_for text,
	discarded_meds text,
	split_items text,
	split_pa text, 
	req_signed_cmn text, 
	min_accept_margin numeric,
	auto_adjust_expected text,
	auto_transfer_write_off text,
	auto_transfer_margin numeric,
	auto_transfer_adj_id text,
	default_pa_type_id text,
	default_los_id text,
	default_service_id text,
	default_origin_id text,
	default_place_of_res_id text,
	ncpdp_pt_res_codes text[],
	ncpdp_sec_send_other_payer text,
	ncpdp_send_benefits_stage text,
	mm_claim_filing_indicator_code text,
	mm_payer_id text,
	mm_payer_name text,
	mm_hold_claims_dos_end text,
	mm_send_billing_prov_commercial_number text,
	mm_billing_commercial_number text,
	mm_send_rendering_prov_commercial_number text,
	mm_rendering_commercial_number text,
	mm_submit_ndc_rx_info text,
	mm_sos_only text,
	mm_default_service_place_id text,
	mm_send_contract_pricing text,
	mm_calc_perc_sales_tax text,
	mm_copy_ordering_md_to_ref text,
	mm_send_upin_supplies text,
	mm_response_type text,
	mm_sec_payer_id text,
	mm_sec_send_identifier text,
	mm_sec_id_qualifier text,
	mm_sec_id text,
	mm_sec_claims_pr_code text,
	mm_sec_claim_filing_indicator_code text,
	contract_type_code text,
	contract_code text,
	contract_version_identifier text,
	contract_percentage numeric,
	terms_discount_percentage numeric,
	pverify_payer_id integer,
	daily_bill_rental text,
	no_recurring_billing text,
	max_rental_claims integer,
	bill_recur_arrears text,
	span_rental_dates text,
	days_timely_filing text,
	cms_1 text,
	cms_2 text,
	cms_3 text,
	cms_4 text,
	cms_5 text,
	cms_6 text,
	cms_7 text,
	cms_8 text,
	cms_9 text,
	cms_10 text,
	cms_11 text,
	cms_12 text,
	cms_12_qualifier text,
	cms_12_id text,
	cms_13 text,
	cms_14 text,
	billing_method_id text,
	organization text
) AS $BODY$
BEGIN
	-- Validate insurance ID
	IF NOT EXISTS (
		SELECT 1 FROM form_patient_insurance 
		WHERE id = p_insurance_id 
		AND archived IS NOT TRUE 
		AND deleted IS NOT TRUE 
		AND COALESCE(active, 'No') = 'Yes'
	) THEN
		INSERT INTO billing_error_log (
			error_message,
			error_context,
			error_type, 
			schema_name,
			table_name,
			additional_details
		) VALUES (
			'Insurance ID not found or inactive',
			'Exception in get_ncpdp_insurance_settings',
			'FUNCTION',
			current_schema(),
			'form_patient_insurance',
			jsonb_build_object(
				'function_name', 'get_ncpdp_insurance_settings',
				'insurance_id', p_insurance_id
			)
		);
		RAISE EXCEPTION 'Insurance ID % not found or inactive', p_insurance_id;
	END IF;

	-- Return the results
	RETURN QUERY
	SELECT
		pi.id::integer as insurance_id,
		pi.payer_id::integer as payer_id,
		pt.id::integer as patient_id,
		COALESCE(pi.type_id, py.type_id)::text as type_id,
		pt.mrn::text as mrn,
		COALESCE(pi.bin, py.bin)::text as bin,
		COALESCE(pi.pcn, py.pcn)::text as pcn,
		pi.group_number::text as group_number,
		CASE
			WHEN pi.type_id = 'CMPBM' AND COALESCE(py.send_blank_person_code, 'No') = 'Yes' THEN NULL::text
			ELSE COALESCE(cobcond.person_code, pi.person_code)::text
		END as person_code,
		CASE
			WHEN pi.type_id = 'MCRD' THEN COALESCE(pi.cardholder_id, pi.medicare_number)::text 
			WHEN pi.type_id = 'MEDI' THEN COALESCE(pi.cardholder_id, pi.medicaid_number)::text
			WHEN pi.type_id = 'COPAY' THEN COALESCE(pi.card_no, pi.cardholder_id)::text
		ELSE pi.cardholder_id::text END as cardholder_id,
		CASE
			WHEN pi.type_id = 'MEDI' THEN COALESCE(pi.medicaid_number, pi.cardholder_id)::text
		ELSE NULL::text END as medicaid_number,
		CASE
			WHEN pi.type_id = 'MCRD' THEN COALESCE(pi.medicare_number, pi.cardholder_id)::text 
		ELSE NULL::text END as medicare_number,
		CASE 
			WHEN pi.type_id = 'MEDI' THEN COALESCE(pi.medicaid_relationship_id, '1')::text
			ELSE COALESCE(pi.pharmacy_relationship_id, '1')::text
		END as pt_rel_code,
		CASE 
			WHEN pi.type_id = 'MEDI' THEN pi.medicaid_agency_number::text 
			ELSE NULL::text 
		END as medicaid_agency_number,
		CASE 
			WHEN pi.type_id = 'MCRD' THEN pi.medigap_id::text 
			ELSE NULL::text
		END as medigap_id,
		CASE
			WHEN pi.type_id = 'CMPBM' THEN COALESCE(cobcond.plan_name, pi.plan_name)::text
			WHEN cobcond.plan_name IS NOT NULL AND length(cobcond.plan_name) > 0 THEN cobcond.plan_name::text
			ELSE NULL::text
		END as plan_name,
		COALESCE(pi.patient_id_qualifier, 'EA')::text as patient_id_qualifier,
		CASE 
			WHEN COALESCE(pi.patient_id_qualifier, 'EA') = 'EA' THEN pt.mrn::text
			WHEN pi.patient_id_qualifier = '01' THEN COALESCE(pi.patient_claim_id, pt.ssn)::text
			WHEN pi.patient_id_qualifier = '10' THEN pi.employer_id::text
			ELSE pi.patient_claim_id::text
		END as patient_claim_id,
		pi.home_plan,
		CASE 
			WHEN COALESCE(pi.pharmacy_relationship_id, '1') <> '1' THEN pi.beneficiary_fname::text 
			ELSE NULL::text 
		END as beneficiary_fname,
		CASE 
			WHEN COALESCE(pi.pharmacy_relationship_id, '1') <> '1' THEN pi.beneficiary_lname::text 
			ELSE NULL::text 
		END as beneficiary_lname,
		CASE 
			WHEN COALESCE(pi.pharmacy_relationship_id, '1') <> '1' THEN pi.beneficiary_mname::text 
			ELSE NULL::text 
		END as beneficiary_mname,
		CASE 
			WHEN COALESCE(pi.pharmacy_relationship_id, '1') <> '1' THEN pi.beneficiary_gender::text 
			ELSE NULL::text 
		END as beneficiary_gender,
		CASE 
			WHEN COALESCE(pi.pharmacy_relationship_id, '1') <> '1' THEN pi.beneficiary_dob::text 
			ELSE NULL::text 
		END as beneficiary_dob,
		COALESCE(py.ncpdp_pharmacy_qualifier_id, '01')::text as ncpdp_pharmacy_qualifier_id,
		CASE
			WHEN py.ncpdp_pharmacy_qualifier_id = '01' THEN CASE WHEN COALESCE(py.ncpdp_pharmacy_pull_id, 'No') = 'Yes' THEN COALESCE(st.npi, py.ncpdp_pharmacy_id)::text ELSE COALESCE(py.ncpdp_pharmacy_id, st.npi)::text END
			WHEN py.ncpdp_pharmacy_qualifier_id = '03' THEN CASE WHEN COALESCE(py.ncpdp_pharmacy_pull_id, 'No') = 'Yes' THEN COALESCE(st.bcbs_id, py.ncpdp_pharmacy_id)::text ELSE COALESCE(py.ncpdp_pharmacy_id, st.bcbs_id)::text END 
			WHEN py.ncpdp_pharmacy_qualifier_id = '04' THEN CASE WHEN COALESCE(py.ncpdp_pharmacy_pull_id, 'No') = 'Yes' THEN COALESCE(st.mcr_ptan_id, py.ncpdp_pharmacy_id)::text ELSE COALESCE(py.ncpdp_pharmacy_id, st.mcr_ptan_id)::text END  
			WHEN py.ncpdp_pharmacy_qualifier_id = '08' THEN CASE WHEN COALESCE(py.ncpdp_pharmacy_pull_id, 'No') = 'Yes' THEN COALESCE(st.state_license, py.ncpdp_pharmacy_id)::text ELSE COALESCE(py.ncpdp_pharmacy_id, st.state_license)::text END 
			WHEN py.ncpdp_pharmacy_qualifier_id = '11' THEN CASE WHEN COALESCE(py.ncpdp_pharmacy_pull_id, 'No') = 'Yes' THEN COALESCE(st.tax_id, py.ncpdp_pharmacy_id)::text ELSE COALESCE(py.ncpdp_pharmacy_id, st.tax_id)::text END
			WHEN py.ncpdp_pharmacy_qualifier_id = '12' THEN CASE WHEN COALESCE(py.ncpdp_pharmacy_pull_id, 'No') = 'Yes' THEN COALESCE(st.dea_id, py.ncpdp_pharmacy_id)::text ELSE COALESCE(py.ncpdp_pharmacy_id, st.dea_id)::text END
			WHEN py.ncpdp_pharmacy_qualifier_id = '14' THEN COALESCE(py.ncpdp_pharmacy_id, st.npi)::text
			WHEN py.ncpdp_pharmacy_qualifier_id = '99' THEN COALESCE(py.ncpdp_pharmacy_id, st.npi)::text
			ELSE st.npi::text
		END as ncpdp_pharmacy_id,
		COALESCE(py.prescriber_no_field_id, '01')::text as prescriber_no_field_id,
		COALESCE(py.pcp_no_field_id, '01')::text as pcp_no_field_id,
		COALESCE(py.default_service_place_id, '12')::text as default_service_place_id,
		py.software_vendor_id::text as software_vendor_id,
		py.default_dispense_fee::numeric as default_dispense_fee,
		COALESCE(py.base_ing_cost_on_id, '00')::text as base_ing_cost_on_id,
		py.compound_qualifier_id::text as compound_qualifier_id,
		py.compound_sub_clarification_code::text as compound_sub_clarification_code,
		COALESCE(py.noncompound_drug_qualifier_id, '03')::text as noncompound_drug_qualifier_id,
		COALESCE(py.service_qualifier_id, '08')::text as service_qualifier_id,
		py.never_comp_seg::text as never_comp_seg,
		py.auto_split_noncompound::text as auto_split_noncompound,
		py.send_dx_code::text as send_dx_code,
		py.send_dx_period::text as send_dx_period,
		py.transmit_grp_no_reversal::text as transmit_grp_no_reversal,
		py.transmit_card_id_reversal::text as transmit_card_id_reversal,
		py.swap_uc_and_gross::text as swap_uc_and_gross,
		py.send_uom::text as send_uom,
		py.send_pharmacist_npi::text as send_pharmacist_npi,
		py.send_blank_person_code::text as send_blank_person_code,
		py.send_pt_email::text as send_pt_email,
		py.ncpdp_e1_prov_required::text as ncpdp_e1_prov_required,
		CASE 
			WHEN py.type_id IN ('COUPON', 'COPAY') THEN COALESCE(py.ncpdp_sec_claims_ingred_cost, 'Send Primary Claim Amount')::text
			ELSE COALESCE(py.ncpdp_sec_claims_ingred_cost, 'Send Primary Claim Amount')::text
		END as ncpdp_sec_claims_ingred_cost,
		CASE
			WHEN py.type_id IN ('COUPON', 'COPAY') THEN COALESCE(py.ncpdp_sec_claims_gross_amount_due, 'Send Co-pay Amount')::text
			ELSE COALESCE(py.ncpdp_sec_claims_gross_amount_due, 'Send Primary Claim Amount')::text
		END as ncpdp_sec_claims_gross_amount_due,
		py.ncpdp_send_primary_payer_amt_paid::text as ncpdp_send_primary_payer_amt_paid,
		py.ncpdp_default_other_coverage_code::text as ncpdp_default_other_coverage_code,
		py.ncpdp_send_sec_charge::text as ncpdp_send_sec_charge,
		py.ncpdp_pt_paid_amount_dx::text as ncpdp_pt_paid_amount_dx,
		py.default_coverage::integer as default_coverage,
		py.req_auth::text as req_auth,
		py.req_auth_for::text as req_auth_for,
		py.discarded_meds::text as discarded_meds,
		py.split_items::text as split_items,
		py.split_pa::text as split_pa,
		py.req_signed_cmn::text as req_signed_cmn,
		py.min_accept_margin::numeric as min_accept_margin,
		py.auto_adjust_expected::text as auto_adjust_expected,
		py.auto_transfer_write_off::text as auto_transfer_write_off,
		py.auto_transfer_margin::numeric as auto_transfer_margin,
		py.auto_transfer_adj_id::text as auto_transfer_adj_id,
		COALESCE(py.default_pa_type_id, '1')::text as default_pa_type_id,
		py.default_los_id::text as default_los_id,
		COALESCE(py.default_service_id, '1')::text as default_service_id,
		COALESCE(py.default_origin_id, '4')::text as default_origin_id,
		COALESCE(py.default_place_of_res_id, '0')::text as default_place_of_res_id,
		ptres.ncpdp_pt_res_codes::text[] as ncpdp_pt_res_codes,
		py.ncpdp_sec_send_other_payer::text as ncpdp_sec_send_other_payer,
		py.ncpdp_send_benefits_stage::text as ncpdp_send_benefits_stage,
		py.mm_claim_filing_indicator_code::text as mm_claim_filing_indicator_code,
		py.mm_payer_id::text as mm_payer_id,
		py.mm_payer_name::text as mm_payer_name,
		py.mm_hold_claims_dos_end::text as mm_hold_claims_dos_end,
		py.mm_send_billing_prov_commercial_number::text as mm_send_billing_prov_commercial_number,
		py.mm_billing_commercial_number::text as mm_billing_commercial_number,
		py.mm_send_rendering_prov_commercial_number::text as mm_send_rendering_prov_commercial_number,
		py.mm_rendering_commercial_number::text as mm_rendering_commercial_number,
		py.mm_submit_ndc_rx_info::text as mm_submit_ndc_rx_info,
		py.mm_sos_only::text as mm_sos_only,
		COALESCE(py.mm_default_service_place_id::text, '12')::text as mm_default_service_place_id,
		py.mm_send_contract_pricing::text as mm_send_contract_pricing,
		py.mm_calc_perc_sales_tax::text as mm_calc_perc_sales_tax,
		py.mm_copy_ordering_md_to_ref::text as mm_copy_ordering_md_to_ref,
		py.mm_send_upin_supplies::text as mm_send_upin_supplies,
		COALESCE(py.mm_response_type::text, 'Electronic')::text as mm_response_type,
		py.mm_sec_payer_id::text as mm_sec_payer_id,
		py.mm_sec_send_identifier::text as mm_sec_send_identifier,
		py.mm_sec_id_qualifier::text as mm_sec_id_qualifier,
		py.mm_sec_id::text as mm_sec_id,
		py.mm_sec_claims_pr_code::text as mm_sec_claims_pr_code,
		py.mm_sec_claim_filing_indicator_code::text as mm_sec_claim_filing_indicator_code,
		pc.contract_type_code::text as contract_type_code,
		pc.contract_code::text as contract_code,
		pc.contract_version_identifier::text as contract_version_identifier,
		pc.contract_percentage::numeric as contract_percentage,
		pc.terms_discount_percentage::numeric as terms_discount_percentage,
		py.pverify_payer_id::integer as pverify_payer_id,
		py.daily_bill_rental::text as daily_bill_rental,
		py.no_recurring_billing::text as no_recurring_billing,
		COALESCE(py.max_rental_claims, 18)::integer as max_rental_claims,
		py.bill_recur_arrears::text as bill_recur_arrears,
		py.span_rental_dates::text as span_rental_dates,
		py.days_timely_filing::text as days_timely_filing,
		py.cms_1::text as cms_1,
		py.cms_2::text as cms_2,
		py.cms_3::text as cms_3,
		py.cms_4::text as cms_4,
		py.cms_5::text as cms_5,
		py.cms_6::text as cms_6,
		py.cms_7::text as cms_7,
		py.cms_8::text as cms_8,
		py.cms_9::text as cms_9,
		py.cms_10::text as cms_10,
		py.cms_11::text as cms_11,
		py.cms_12::text::text as cms_12,
		py.cms_12_qualifier::text as cms_12_qualifier,
		py.cms_12_id::text as cms_12_id,
		py.cms_13::text as cms_13,
		py.cms_14::text as cms_14,
		py.billing_method_id::text as billing_method_id,
		py.organization::text as organization
	FROM form_patient_insurance pi
	LEFT JOIN LATERAL(
		SELECT 
			*
		FROM form_patient_insurance_ncpdp_cond cond
		INNER JOIN sf_form_patient_insurance_to_patient_insurance_ncpdp_cond sfcond 
		    ON sfcond.form_patient_insurance_ncpdp_cond_fk = cond.id 
		    AND sfcond.delete IS NOT TRUE 
		    AND sfcond.archive IS NOT TRUE
		WHERE sfcond.form_patient_insurance_fk = p_insurance_id 
		    AND cond.archived IS NOT TRUE 
		    AND cond.deleted IS NOT TRUE 
		    AND cond.previous_payer_id = p_previous_payer_id
	) cobcond ON TRUE 

	LEFT JOIN LATERAL (
		SELECT
			array_agg(lrcl.code::text) as ncpdp_pt_res_codes
		FROM gr_form_payer_ncpdp_pt_res_codes_to_list_ncpdp_ecl_id lgrrc
		INNER JOIN form_list_ncpdp_ecl lrcl 
		    ON lrcl.code = lgrrc.form_list_ncpdp_ecl_fk 
			AND lrcl.field = '351-NP'
		    AND lrcl.archived IS NOT TRUE 
		    AND lrcl.deleted IS NOT TRUE
	) ptres ON TRUE

	INNER JOIN form_site st ON st.id = p_site_id
	INNER JOIN form_payer py ON py.id = pi.payer_id 
	    AND py.archived IS NOT TRUE 
	    AND py.deleted IS NOT TRUE 
	    AND COALESCE(py.active, 'No') = 'Yes'
	LEFT JOIN form_payer_contract pc ON pc.id = py.assigned_contract_id
		AND pc.archived IS NOT TRUE 
		AND pc.deleted IS NOT TRUE
	INNER JOIN form_patient pt ON pt.id = pi.patient_id 
	    AND pt.archived IS NOT TRUE 
	    AND pt.deleted IS NOT TRUE
	WHERE pi.id = p_insurance_id 
	    AND pi.archived IS NOT TRUE 
	    AND pi.deleted IS NOT TRUE 
	    AND COALESCE(pi.active, 'No') = 'Yes';
END;
$BODY$ LANGUAGE plpgsql VOLATILE;
