-- Function to process AR transactions at the charge line level
DO $$ BEGIN
  PERFORM drop_all_function_signatures('process_ar_transaction');
END $$;
CREATE OR REPLACE FUNCTION process_ar_transaction() RETURNS TRIGGER AS $$
DECLARE
    v_account_id INTEGER;
    v_site_account_id INTEGER;
    v_charge_record RECORD;
    v_invoice_record RECORD;
    v_remaining_balance NUMERIC;
    v_creator INTEGER;
    v_success BOOLEAN := FALSE;
BEGIN
    -- Log function entry
    RAISE LOG 'Entering process_ar_transaction for transaction_no: %, id: %', 
             NEW.transaction_no, NEW.id;
    
    -- Skip if conditions aren't met
    IF (COALESCE(NEW.zeroed, 'No') = 'Yes' OR 
        NEW.archived IS TRUE OR 
        NEW.deleted IS TRUE OR
        NEW.applied_datetime IS NOT NULL) THEN
        RAISE LOG 'Skipping process_ar_transaction, conditions not met for transaction_no: %', 
                 COALESCE(NEW.transaction_no, 'NULL');
        RETURN NEW;
    END IF;
    
    -- Check if we're in a closed period
    IF check_closed_period(NEW.post_datetime::timestamp) THEN
        -- Log error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Cannot post transaction in a closed accounting period',
            'Error processing AR transaction',
            'TRIGGER',
            current_schema(),
            'billing_ar_transaction',
            jsonb_build_object(
                'function_name', 'process_ar_transaction',
                'transaction_no', COALESCE(NEW.transaction_no, 'NULL'),
                'post_datetime', NEW.post_datetime
            )
        );
        
        RAISE EXCEPTION 'Cannot post transaction in a closed accounting period for transaction %', 
                        COALESCE(NEW.transaction_no, 'NULL');
    END IF;
    
    BEGIN
        -- Determine who created/updated this record
        v_creator := COALESCE(NEW.updated_by, NEW.created_by);
        RAISE LOG 'Processing AR transaction for transaction_no: %, creator: %', 
                 COALESCE(NEW.transaction_no, 'NULL'), v_creator;
        
        -- Get the charge line record
        SELECT cl.*, bi.id AS invoice_id, bi.invoice_type
        INTO v_charge_record
        FROM form_ledger_charge_line cl
        JOIN form_billing_invoice bi ON cl.invoice_no = bi.invoice_no
        WHERE cl.charge_no = NEW.charge_no
        AND cl.archived IS NOT TRUE
        AND cl.deleted IS NOT TRUE
        AND COALESCE(cl.void, 'No') <> 'Yes'
        AND COALESCE(cl.zeroed, 'No') <> 'Yes'
        AND bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
        AND COALESCE(bi.void, 'No') <> 'Yes'
        AND COALESCE(bi.zeroed, 'No') <> 'Yes';
        
        IF v_charge_record IS NULL THEN
            -- Log error to billing_error_log
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Charge line not found for AR transaction',
                'Error processing AR transaction',
                'TRIGGER',
                current_schema(),
                'billing_ar_transaction',
                jsonb_build_object(
                    'function_name', 'process_ar_transaction',
                    'transaction_no', COALESCE(NEW.transaction_no, 'NULL'),
                    'charge_no', NEW.charge_no
                )
            );
            
            RAISE EXCEPTION 'Charge line with charge_no % not found for transaction %', 
                           NEW.charge_no, COALESCE(NEW.transaction_no, 'NULL');
        END IF;
        
        -- Get account ID based on charge line invoice type
        BEGIN
            IF v_charge_record.invoice_type = 'Patient' THEN
                SELECT id INTO v_account_id 
                FROM form_billing_account
                WHERE type = 'Patient'
                AND patient_id = NEW.patient_id
                AND archived IS NOT TRUE
                AND deleted IS NOT TRUE;
            ELSE
                SELECT id INTO v_account_id 
                FROM form_billing_account
                WHERE type = 'Payer'
                AND payer_id = NEW.payer_id
                AND archived IS NOT TRUE
                AND deleted IS NOT TRUE;
            END IF;
            
            IF v_account_id IS NULL THEN
                -- Log error to billing_error_log
                INSERT INTO billing_error_log (
                    error_message,
                    error_context,
                    error_type,
                    schema_name,
                    table_name,
                    additional_details
                ) VALUES (
                    'Account not found for AR transaction',
                    'Error processing AR transaction',
                    'TRIGGER',
                    current_schema(),
                    'billing_ar_transaction',
                    jsonb_build_object(
                        'function_name', 'process_ar_transaction',
                        'transaction_no', COALESCE(NEW.transaction_no, 'NULL'),
                        'patient_id', NEW.patient_id,
                        'payer_id', NEW.payer_id
                    )
                );
                
                RAISE EXCEPTION 'Account not found for transaction %', COALESCE(NEW.transaction_no, 'NULL');
            END IF;
            
            RAISE LOG 'Retrieved account_id: % for transaction: %', 
                     v_account_id, COALESCE(NEW.transaction_no, 'NULL');
        EXCEPTION WHEN OTHERS THEN
            -- Log error to billing_error_log
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                SQLERRM,
                'Error retrieving account ID for transaction',
                'TRIGGER',
                current_schema(),
                'billing_ar_transaction',
                jsonb_build_object(
                    'function_name', 'process_ar_transaction',
                    'transaction_no', COALESCE(NEW.transaction_no, 'NULL'),
                    'patient_id', NEW.patient_id,
                    'payer_id', NEW.payer_id
                )
            );
            
            RAISE;
        END;

    -- Process based on transaction type and adjustment_for
    CASE NEW.transaction_type
    -- Cash Payment - transaction_type should be 'Posting', account_types are 'Cash' and 'AR'
    WHEN 'Payment' THEN
        -- Create Cash debit entry
        RAISE LOG 'Creating Cash debit entry for charge_line: %, amount: %', 
                 NEW.charge_no, NEW.amount;
        
        INSERT INTO form_ledger_finance (
            invoice_id, 
            invoice_no,
            charge_line_id, 
            charge_no,
            site_id,
            account_id, 
            source_id, 
            source_form, 
            post_datetime, 
            transaction_datetime,
            inventory_id,
            ledger_inventory_id,
            ledger_lot_id,
            ledger_serial_id,
            account_type, 
            transaction_type,
            debit, 
            credit, 
            created_on, 
            created_by,
            notes
        ) VALUES (
            v_charge_record.invoice_id,
            v_charge_record.invoice_no,
            v_charge_record.id,
            v_charge_record.charge_no,
            v_charge_record.site_id,
            v_account_id,
            NEW.id,
            'billing_ar_transaction',
            COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
            get_site_timestamp(NEW.site_id),
            v_charge_record.inventory_id,
            NULL,
            NULL,
            NULL,
            'Cash',
            'Posting',
            NEW.amount,
            0.00,
            CURRENT_TIMESTAMP,
            v_creator,
            'Payment posting for charge_line: ' || NEW.charge_no
        );
        
        -- Create AR credit entry
        RAISE LOG 'Creating AR credit entry for charge_line: %, amount: %', 
                 NEW.charge_no, NEW.amount;
        
        INSERT INTO form_ledger_finance (
            invoice_id, 
            invoice_no,
            charge_line_id,
            charge_no,
            site_id,
            account_id, 
            source_id, 
            source_form, 
            post_datetime, 
            transaction_datetime,
            inventory_id,
            ledger_inventory_id,
            ledger_lot_id,
            ledger_serial_id,
            account_type, 
            transaction_type,
            debit, 
            credit, 
            created_on, 
            created_by,
            notes
        ) VALUES (
            v_charge_record.invoice_id,
            v_charge_record.invoice_no,
            v_charge_record.id,
            v_charge_record.charge_no,
            v_charge_record.site_id,
            v_account_id,
            NEW.id,
            'billing_ar_transaction',
            COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
            get_site_timestamp(NEW.site_id),
            v_charge_record.inventory_id,
            NULL,
            NULL,
            NULL,
            'AR',
            'Posting',
            0.00,
            NEW.amount,
            CURRENT_TIMESTAMP,
            v_creator,
            'Payment posting for charge_line: ' || NEW.charge_no
        );
        
    -- Cash Allocation - transaction_type should be 'Cash Allocation', account_types are 'Unapplied Cash' and 'AR'
    WHEN 'Cash Allocation' THEN
    -- Step 1: Reverse the original cash entries
    -- Debit Unapplied Cash (reducing the liability)
        RAISE LOG 'Reversing Unapplied Cash for cash allocation, amount: %', 
                NEW.amount;
        
        INSERT INTO form_ledger_finance (
            invoice_id, 
            invoice_no,
            charge_line_id,
            charge_no,
            site_id,
            account_id, 
            source_id, 
            source_form, 
            post_datetime, 
            transaction_datetime,
            inventory_id,
            ledger_inventory_id,
            ledger_lot_id,
            ledger_serial_id,
            account_type, 
            transaction_type,
            debit, 
            credit, 
            created_on, 
            created_by,
            notes
        ) VALUES (
            v_charge_record.invoice_id,
            v_charge_record.invoice_no,
            v_charge_record.id,
            v_charge_record.charge_no,
            v_charge_record.site_id,
            v_account_id,
            NEW.id,
            'billing_ar_transaction',
            COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
            get_site_timestamp(NEW.site_id),
            v_charge_record.inventory_id,
            NULL,
            NULL,
            NULL,
            'Unapplied Cash',
            'Cash Allocation Reversal',
            NEW.amount,
            0.00,
            CURRENT_TIMESTAMP,
            v_creator,
            'Reversing unapplied cash for cash allocation transaction'
        );
        
        -- Credit Cash (reducing the asset from unapplied)
        RAISE LOG 'Reversing Cash for cash allocation, amount: %', 
                NEW.amount;
        
        INSERT INTO form_ledger_finance (
            invoice_id, 
            invoice_no,
            charge_line_id,
            charge_no,
            site_id,
            account_id, 
            source_id, 
            source_form, 
            post_datetime, 
            transaction_datetime,
            inventory_id,
            ledger_inventory_id,
            ledger_lot_id,
            ledger_serial_id,
            account_type, 
            transaction_type,
            debit, 
            credit, 
            created_on, 
            created_by,
            notes
        ) VALUES (
            v_charge_record.invoice_id,
            v_charge_record.invoice_no,
            v_charge_record.id,
            v_charge_record.charge_no,
            v_charge_record.site_id,
            v_account_id,
            NEW.id,
            'billing_ar_transaction',
            COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
            get_site_timestamp(NEW.site_id),
            v_charge_record.inventory_id,
            NULL,
            NULL,
            NULL,
            'Cash',
            'Cash Allocation Reversal',
            0.00,
            NEW.amount,
            CURRENT_TIMESTAMP,
            v_creator,
            'Reversing unapplied cash for cash allocation transaction'
        );
        
        -- Step 2: Create new entries for allocation to the specific charge line
        -- Debit Cash (increasing the asset, but now tied to this charge line)
        RAISE LOG 'Creating Cash debit entry for charge_line: %, amount: %', 
                NEW.charge_no, NEW.amount;
        
        INSERT INTO form_ledger_finance (
            invoice_id, 
            invoice_no,
            charge_line_id,
            charge_no,
            site_id,
            account_id, 
            source_id, 
            source_form, 
            post_datetime, 
            transaction_datetime,
            inventory_id,
            ledger_inventory_id,
            ledger_lot_id,
            ledger_serial_id,
            account_type, 
            transaction_type,
            debit, 
            credit, 
            created_on, 
            created_by,
            notes
        ) VALUES (
            v_charge_record.invoice_id,
            v_charge_record.invoice_no,
            v_charge_record.id,
            v_charge_record.charge_no,
            v_charge_record.site_id,
            v_account_id,
            NEW.id,
            'billing_ar_transaction',
            COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
            get_site_timestamp(NEW.site_id),
            v_charge_record.inventory_id,
            NULL,
            NULL,
            NULL,
            'Cash',
            'Cash Allocation',
            NEW.amount,
            0.00,
            CURRENT_TIMESTAMP,
            v_creator,
            'Cash allocation for charge_line: ' || NEW.charge_no
        );
        
        -- Credit AR (reducing the receivable)
        RAISE LOG 'Creating AR credit entry for charge_line: %, amount: %', 
                NEW.charge_no, NEW.amount;
        
        INSERT INTO form_ledger_finance (
            invoice_id, 
            invoice_no,
            charge_line_id, 
            charge_no,
            site_id,
            account_id, 
            source_id, 
            source_form, 
            post_datetime, 
            transaction_datetime,
            inventory_id,
            ledger_inventory_id,
            ledger_lot_id,
            ledger_serial_id,
            account_type, 
            transaction_type,
            debit, 
            credit, 
            created_on, 
            created_by,
            notes
        ) VALUES (
            v_charge_record.invoice_id,
            v_charge_record.invoice_no,
            v_charge_record.id,
            v_charge_record.charge_no,
            v_charge_record.site_id,
            v_account_id,
            NEW.id,
            'billing_ar_transaction',
            COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
            get_site_timestamp(NEW.site_id),
            v_charge_record.inventory_id,
            NULL,
            NULL,
            NULL,
            'AR',
            'Cash Allocation',
            0.00,
            NEW.amount,
            CURRENT_TIMESTAMP,
            v_creator,
            'Cash allocation for charge_line: ' || NEW.charge_no
        );

    -- Adjustment - transaction_type should be 'Adjustment'
    WHEN 'Adjustment' THEN
        -- Determine whether this is an AR or COGS adjustment
        IF NEW.adjustment_for = 'COGS' THEN
                
            SELECT 
                id
            INTO v_site_account_id
            FROM form_site_account
            WHERE site_id = v_charge_record.site_id
            AND account_type = 'Site';

            -- This is a COGS adjustment, handle COGS and Inventory only
            IF NEW.adjustment_type = 'Credit' THEN
                -- Credit COGS adjustment (COGS credit, Inventory debit)
                -- Create COGS credit entry
                RAISE LOG 'Creating COGS credit entry for charge_line: %, amount: %', 
                         NEW.charge_no, NEW.amount;

                INSERT INTO form_ledger_finance (   
                    invoice_id, 
                    invoice_no,
                    charge_line_id, 
                    charge_no,
                    site_id,
                    account_id, 
                    source_id, 
                    source_form, 
                    post_datetime, 
                    transaction_datetime,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    account_type, 
                    transaction_type,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    v_charge_record.invoice_id,
                    v_charge_record.invoice_no,
                    v_charge_record.id,
                    v_charge_record.charge_no,
                    v_charge_record.site_id,
                    v_site_account_id,
                    NEW.id,
                    'billing_ar_transaction',
                    COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
                    get_site_timestamp(NEW.site_id),
                    v_charge_record.inventory_id,
                    NULL,
                    NULL,
                    NULL,
                    'COGS',
                    'Adjustment',
                    0.00,
                    NEW.amount,
                    CURRENT_TIMESTAMP,
                    v_creator,
                    'COGS adjustment for charge_line: ' || NEW.charge_no
                );
                
                -- Create Inventory debit entry
                RAISE LOG 'Creating Inventory debit entry for charge_line: %, amount: %', 
                         NEW.charge_no, NEW.amount;
                
                INSERT INTO form_ledger_finance (
                    invoice_id, 
                    invoice_no,
                    charge_line_id, 
                    charge_no,
                    site_id,
                    account_id, 
                    source_id, 
                    source_form, 
                    post_datetime, 
                    transaction_datetime,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    account_type, 
                    transaction_type,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    v_charge_record.invoice_id,
                    v_charge_record.invoice_no,
                    v_charge_record.id,
                    v_charge_record.charge_no,
                    v_charge_record.site_id,
                    v_site_account_id,
                    NEW.id,
                    'billing_ar_transaction',
                    COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
                    get_site_timestamp(NEW.site_id),
                    v_charge_record.inventory_id,
                    NULL,
                    NULL,
                    NULL,
                    'Inventory',
                    'Adjustment',
                    NEW.amount,
                    0.00,
                    CURRENT_TIMESTAMP,
                    v_creator,
                    'Inventory adjustment for charge_line: ' || NEW.charge_no
                );
                
            ELSIF NEW.adjustment_type = 'Debit' THEN
                -- Debit COGS adjustment (COGS debit, Inventory credit)
                -- Create COGS debit entry
                RAISE LOG 'Creating COGS debit entry for charge_line: %, amount: %', 
                         NEW.charge_no, NEW.amount;
                
                INSERT INTO form_ledger_finance (
                    invoice_id, 
                    invoice_no,
                    charge_line_id, 
                    charge_no,
                    site_id,
                    account_id, 
                    source_id, 
                    source_form, 
                    post_datetime, 
                    transaction_datetime,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    account_type, 
                    transaction_type,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    v_charge_record.invoice_id,
                    v_charge_record.invoice_no,
                    v_charge_record.id,
                    v_charge_record.charge_no,
                    v_charge_record.site_id,
                    v_site_account_id,
                    NEW.id,
                    'billing_ar_transaction',
                    COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
                    get_site_timestamp(NEW.site_id),
                    v_charge_record.inventory_id,
                    NULL,
                    NULL,
                    NULL,
                    'COGS',
                    'Adjustment',
                    NEW.amount,
                    0.00,
                    CURRENT_TIMESTAMP,
                    v_creator,
                    'COGS adjustment for charge_line: ' || NEW.charge_no
                );
                
                -- Create Inventory credit entry
                RAISE LOG 'Creating Inventory credit entry for charge_line: %, amount: %', 
                         NEW.charge_no, NEW.amount;
                
                INSERT INTO form_ledger_finance (
                    invoice_id, 
                    invoice_no,
                    charge_line_id, 
                    charge_no,
                    site_id,
                    account_id, 
                    source_id, 
                    source_form, 
                    post_datetime, 
                    transaction_datetime,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    account_type, 
                    transaction_type,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    v_charge_record.invoice_id,
                    v_charge_record.invoice_no,
                    v_charge_record.charge_no,
                    v_charge_record.site_id,
                    v_site_account_id,
                    NEW.id,
                    'billing_ar_transaction',
                    COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
                    get_site_timestamp(NEW.site_id),
                    v_charge_record.inventory_id,
                    NULL,
                    NULL,
                    NULL,
                    'Inventory',
                    'Adjustment',
                    0.00,
                    NEW.amount,
                    CURRENT_TIMESTAMP,
                    v_creator,
                    'Inventory adjustment for charge_line: ' || NEW.charge_no
                );
            END IF;
            
        ELSE
            -- This is an AR adjustment (default), handle AR and Revenue
            IF NEW.adjustment_type = 'Credit' THEN
                -- Credit Adjustment (credit AR, debit Revenue)
                -- Create AR credit entry
                RAISE LOG 'Creating AR credit entry for charge_line: %, amount: %', 
                         NEW.charge_no, NEW.amount;
                
                INSERT INTO form_ledger_finance (
                    invoice_id, 
                    invoice_no,
                    charge_line_id, 
                    charge_no,
                    site_id,
                    account_id, 
                    source_id, 
                    source_form, 
                    post_datetime, 
                    transaction_datetime,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    account_type, 
                    transaction_type,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    v_charge_record.invoice_id,
                    v_charge_record.invoice_no,
                    v_charge_record.id,
                    v_charge_record.charge_no,
                    v_charge_record.site_id,
                    v_account_id,
                    NEW.id,
                    'billing_ar_transaction',
                    COALESCE(NEW.post_datetime,get_site_timestamp(NEW.site_id)),
                    get_site_timestamp(NEW.site_id),
                    v_charge_record.inventory_id,
                    NULL,
                    NULL,
                    NULL,
                    'AR',
                    'Adjustment',
                    0.00,
                    NEW.amount,
                    CURRENT_TIMESTAMP,
                    v_creator,
                    'AR adjustment for charge_line: ' || NEW.charge_no
                );
                
                -- Create Revenue debit entry
                RAISE LOG 'Creating Revenue debit entry for charge_line: %, amount: %', 
                         NEW.charge_no, NEW.amount;
                
                INSERT INTO form_ledger_finance (
                    invoice_id,
                    invoice_no,
                    charge_line_id,  
                    charge_no,
                    site_id,
                    account_id, 
                    source_id, 
                    source_form, 
                    post_datetime, 
                    transaction_datetime,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    account_type, 
                    transaction_type,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    v_charge_record.invoice_id,
                    v_charge_record.invoice_no,
                    v_charge_record.id,
                    v_charge_record.charge_no,
                    v_charge_record.site_id,
                    v_account_id,
                    NEW.id,
                    'billing_ar_transaction',
                    COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
                    get_site_timestamp(NEW.site_id),
                    v_charge_record.inventory_id,
                    NULL,
                    NULL,
                    NULL,
                    'Revenue',
                    'Adjustment',
                    NEW.amount,
                    0.00,
                    CURRENT_TIMESTAMP,
                    v_creator,
                    'AR adjustment for charge_line: ' || NEW.charge_no
                );

            ELSIF NEW.adjustment_type = 'Debit' THEN
                -- Debit Adjustment (debit AR, credit Revenue)
                -- Create AR debit entry
                RAISE LOG 'Creating AR debit entry for charge_line: %, amount: %', 
                         NEW.charge_no, NEW.amount;
                
                INSERT INTO form_ledger_finance (
                    invoice_id, 
                    invoice_no,
                    site_id,
                    charge_line_id, 
                    charge_no,
                    account_id, 
                    source_id, 
                    source_form, 
                    post_datetime, 
                    transaction_datetime,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    account_type, 
                    transaction_type,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    v_charge_record.invoice_id,
                    v_charge_record.invoice_no,
                    v_charge_record.site_id,
                    v_charge_record.id,
                    v_charge_record.charge_no,
                    v_account_id,
                    NEW.id,
                    'billing_ar_transaction',
                    COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
                    get_site_timestamp(NEW.site_id),
                    v_charge_record.inventory_id,
                    NULL,
                    NULL,
                    NULL,
                    'AR',
                    'Adjustment',
                    NEW.amount,
                    0.00,
                    CURRENT_TIMESTAMP,
                    v_creator,
                    'AR adjustment for charge_line: ' || NEW.charge_no
                );
                
                -- Create Revenue credit entry
                RAISE LOG 'Creating Revenue credit entry for charge_line: %, amount: %', 
                         NEW.charge_no, NEW.amount;
                
                INSERT INTO form_ledger_finance (
                    invoice_id, 
                    invoice_no,
                    site_id,
                    charge_line_id, 
                    charge_no,
                    account_id, 
                    source_id, 
                    source_form, 
                    post_datetime, 
                    transaction_datetime,
                    inventory_id,
                    ledger_inventory_id,
                    ledger_lot_id,
                    ledger_serial_id,
                    account_type, 
                    transaction_type,
                    debit, 
                    credit, 
                    created_on, 
                    created_by,
                    notes
                ) VALUES (
                    v_charge_record.invoice_id,
                    v_charge_record.invoice_no,
                    v_charge_record.site_id,
                    v_charge_record.id,
                    v_charge_record.charge_no,
                    v_account_id,
                    NEW.id,
                    'billing_ar_transaction',
                    COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
                    get_site_timestamp(NEW.site_id),
                    v_charge_record.inventory_id,
                    NULL,
                    NULL,
                    NULL,
                    'Revenue',
                    'Adjustment',
                    0.00,
                    NEW.amount,
                    CURRENT_TIMESTAMP,
                    v_creator,
                    'AR adjustment for charge_line: ' || NEW.charge_no
                );
                
            ELSE
                -- Invalid adjustment type
                RAISE EXCEPTION 'Invalid adjustment type: %', NEW.adjustment_type;
            END IF;
        END IF;
    
    -- Writeoff - transaction_type should be 'Writeoff', account_types are 'AR' and 'Revenue'
    WHEN 'Writeoff' THEN
        -- Create AR credit entry
        RAISE LOG 'Creating AR credit entry for charge_line: %, amount: %', 
                 NEW.charge_no, NEW.amount;
        
        -- Verify the AR transaction doesn't exceed the remaining balance on the charge line
        IF NEW.transaction_type = 'Payment' OR NEW.transaction_type = 'Writeoff' THEN
            -- Calculate remaining balance
            SELECT 
                COALESCE(SUM(CASE WHEN lf.account_type = 'AR' THEN lf.debit - lf.credit ELSE 0 END), 0) 
            INTO v_remaining_balance
            FROM form_ledger_finance lf
            WHERE lf.charge_line_id = v_charge_record.id;
            
            -- Check if transaction exceeds remaining balance
            IF NEW.amount > v_remaining_balance THEN
                -- Log error
                INSERT INTO billing_error_log (
                    error_message,
                    error_context,
                    error_type,
                    schema_name,
                    table_name,
                    additional_details
                ) VALUES (
                    'Transaction amount exceeds remaining balance',
                    'Error processing AR transaction',
                    'TRIGGER',
                    current_schema(),
                    'billing_ar_transaction',
                    jsonb_build_object(
                        'function_name', 'process_ar_transaction',
                        'charge_no', NEW.charge_no,
                        'amount', NEW.amount,
                        'remaining_balance', v_remaining_balance
                    )
                );
                
                RAISE EXCEPTION 'Transaction amount (%) exceeds remaining balance (%) for charge %', 
                                NEW.amount, v_remaining_balance, NEW.charge_no;
            END IF;
        END IF;

        INSERT INTO form_ledger_finance (
            invoice_id, 
            invoice_no,
            site_id,
            charge_line_id, 
            charge_no,
            account_id, 
            source_id, 
            source_form, 
            post_datetime, 
            transaction_datetime,
            inventory_id,
            ledger_inventory_id,
            ledger_lot_id,
            ledger_serial_id,
            account_type, 
            transaction_type,
            debit, 
            credit, 
            created_on, 
            created_by,
            notes
        ) VALUES (
            v_charge_record.invoice_id,
            v_charge_record.invoice_no,
            v_charge_record.site_id,
            v_charge_record.id,
            v_charge_record.charge_no,
            v_account_id,
            NEW.id,
            'billing_ar_transaction',
            COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
            get_site_timestamp(NEW.site_id),
            v_charge_record.inventory_id,
            NULL,
            NULL,
            NULL,
            'AR',
            'Writeoff',
            0.00,
            NEW.amount,
            CURRENT_TIMESTAMP,
            v_creator,
            'Writeoff for charge_line: ' || NEW.charge_no
        );
        
        -- Create Revenue debit entry
        RAISE LOG 'Creating Revenue debit entry for charge_line: %, amount: %', 
                 NEW.charge_no, NEW.amount;
        
        INSERT INTO form_ledger_finance (
            invoice_id, 
            invoice_no,
            site_id,
            charge_line_id, 
            charge_no,
            account_id, 
            source_id, 
            source_form, 
            post_datetime, 
            transaction_datetime,
            inventory_id,
            ledger_inventory_id,
            ledger_lot_id,
            ledger_serial_id,
            account_type, 
            transaction_type,
            debit, 
            credit, 
            created_on, 
            created_by,
            notes
        ) VALUES (
            v_charge_record.invoice_id,
            v_charge_record.invoice_no,
            v_charge_record.site_id,
            v_charge_record.id,
            v_charge_record.charge_no,
            v_account_id,
            NEW.id,
            'billing_ar_transaction',
            COALESCE(NEW.post_datetime, get_site_timestamp(NEW.site_id)),
            get_site_timestamp(NEW.site_id),
            v_charge_record.inventory_id,
            NULL,
            NULL,
            NULL,
            'Revenue',
            'Writeoff',
            NEW.amount,
            0.00,
            CURRENT_TIMESTAMP,
            v_creator,
            'Writeoff for charge_line: ' || NEW.charge_no
        );

        ELSE
            -- Invalid transaction type
                RAISE EXCEPTION 'Invalid transaction type: %', NEW.transaction_type;
        END CASE;
        -- Mark transaction as applied
        NEW.applied_datetime := get_site_timestamp(NEW.site_id);

        v_success := TRUE;
        RAISE LOG 'AR transaction successfully processed: %, with amount: %', 
                 COALESCE(NEW.transaction_no, 'NULL'), NEW.amount;
        
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        -- Log the error
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Transaction failed during AR transaction processing',
            'TRIGGER',
            current_schema(),
            'billing_ar_transaction',
            jsonb_build_object(
                'function_name', 'process_ar_transaction',
                'transaction_no', COALESCE(NEW.transaction_no, 'NULL'),
                'success_flag', v_success
            )
        );
        
        RAISE; -- Re-throw the exception to rollback the transaction
    END;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Trigger for AR transaction processing
CREATE OR REPLACE TRIGGER trg_billing_ar_transaction_insert
BEFORE INSERT ON form_billing_ar_transaction
FOR EACH ROW
EXECUTE FUNCTION process_ar_transaction();

-- Function to handle zeroing an AR transaction
DO $$ BEGIN
  PERFORM drop_all_function_signatures('process_ar_transaction_zero');
END $$;
CREATE OR REPLACE FUNCTION process_ar_transaction_zero() RETURNS TRIGGER AS $$
DECLARE
    v_charge_record RECORD;
    v_invoice_record RECORD;
    v_ledger_record RECORD;
    v_account_id INTEGER;
    v_creator INTEGER;
    v_success BOOLEAN := FALSE;
    v_ledger_entry_ids INTEGER[];
BEGIN
    -- Log function entry
    RAISE LOG 'Entering process_ar_transaction_zero for transaction_no: %', 
             NEW.transaction_no;
    
    -- Skip if conditions aren't met
    IF (COALESCE(NEW.zeroed, 'No') <> 'Yes' OR 
        COALESCE(OLD.zeroed, 'No') = 'Yes' OR
        COALESCE(NEW.applied_datetime, NULL) IS NULL) THEN
        RAISE LOG 'Skipping process_ar_transaction_zero, conditions not met for transaction_no: %', 
                 NEW.transaction_no;
        RETURN NEW;
    END IF;
    
    -- Check if we're in a closed period
    IF check_closed_period(NEW.post_datetime::timestamp) THEN
        -- Log error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Cannot zero AR transaction in a closed accounting period',
            'Error zeroing AR transaction',
            'TRIGGER',
            current_schema(),
            'billing_ar_transaction',
            jsonb_build_object(
                'function_name', 'process_ar_transaction_zero',
                'transaction_no', NEW.transaction_no,
                'post_datetime', NEW.post_datetime
            )
        );
        
        RAISE EXCEPTION 'Cannot zero AR transaction in a closed accounting period for transaction %', 
                        NEW.transaction_no;
    END IF;
    
    BEGIN
        -- Determine who created/updated this record
        v_creator := COALESCE(NEW.updated_by, NEW.created_by);
        RAISE LOG 'Zeroing AR transaction: %, by user: %', 
                 NEW.transaction_no, v_creator;
        
        -- Get the charge line record to update it
        SELECT cl.*, bi.id AS invoice_id, bi.invoice_type
        INTO v_charge_record
        FROM form_ledger_charge_line cl
        JOIN form_billing_invoice bi ON cl.invoice_no = bi.invoice_no
        WHERE cl.charge_no = NEW.charge_no
        AND cl.archived IS NOT TRUE
        AND cl.deleted IS NOT TRUE
        AND bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE;
        
        IF v_charge_record IS NULL THEN
            -- Log warning but continue - we'll still create reversing entries
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Charge line not found for AR transaction',
                'Warning: Zeroing will create reversing entries only',
                'WARNING',
                current_schema(),
                'billing_ar_transaction',
                jsonb_build_object(
                    'function_name', 'process_ar_transaction_zero',
                    'transaction_no', NEW.transaction_no,
                    'charge_no', NEW.charge_no
                )
            );
        END IF;
        
        -- Get account ID for creating reversing entries
        BEGIN
            IF NEW.patient_id IS NOT NULL THEN
                SELECT id INTO v_account_id 
                FROM form_billing_account
                WHERE type = 'Patient'
                AND patient_id = NEW.patient_id
                AND archived IS NOT TRUE
                AND deleted IS NOT TRUE;
            ELSE
                SELECT id INTO v_account_id 
                FROM form_billing_account
                WHERE type = 'Payer'
                AND payer_id = NEW.payer_id
                AND archived IS NOT TRUE
                AND deleted IS NOT TRUE;
            END IF;
            
            IF v_account_id IS NULL THEN
                -- Log error to billing_error_log
                INSERT INTO billing_error_log (
                    error_message,
                    error_context,
                    error_type,
                    schema_name,
                    table_name,
                    additional_details
                ) VALUES (
                    'Account not found for AR transaction',
                    'Error zeroing AR transaction',
                    'TRIGGER',
                    current_schema(),
                    'billing_ar_transaction',
                    jsonb_build_object(
                        'function_name', 'process_ar_transaction_zero',
                        'transaction_no', NEW.transaction_no,
                        'patient_id', NEW.patient_id,
                        'payer_id', NEW.payer_id
                    )
                );
                
                RAISE EXCEPTION 'Account not found for transaction %', NEW.transaction_no;
            END IF;
        EXCEPTION WHEN OTHERS THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                SQLERRM,
                'Error retrieving account ID for transaction',
                'TRIGGER',
                current_schema(),
                'billing_ar_transaction',
                jsonb_build_object(
                    'function_name', 'process_ar_transaction_zero',
                    'transaction_no', NEW.transaction_no,
                    'patient_id', NEW.patient_id,
                    'payer_id', NEW.payer_id
                )
            );
            
            RAISE;
        END;
        
        -- Get all ledger entries to reverse
        FOR v_ledger_record IN 
            SELECT * FROM form_ledger_finance lf
            WHERE source_id = NEW.id
            AND source_form = 'billing_ar_transaction'
            AND reversal_for_id IS NULL
            AND NOT EXISTS (
                SELECT 1 FROM form_ledger_finance lfr
                WHERE lfr.reversal_for_id = lf.id
            )
            ORDER BY id
        LOOP
            RAISE LOG 'Creating reversing entry for ledger ID: % in transaction: %', 
                     v_ledger_record.id, NEW.transaction_no;
                     
            -- Create reversing entry
            INSERT INTO form_ledger_finance (
                invoice_id, 
                invoice_no,
                site_id,
                charge_line_id, 
                charge_no,
                account_id, 
                source_id, 
                source_form, 
                post_datetime,
                transaction_datetime,
                inventory_id,
                ledger_inventory_id,
                ledger_lot_id,
                ledger_serial_id,
                account_type, 
                transaction_type,
                reversal_for_id,
                debit, 
                credit, 
                created_on, 
                created_by,
                notes
            ) VALUES (
                v_ledger_record.invoice_id,
                v_ledger_record.invoice_no,
                v_ledger_record.site_id,
                v_ledger_record.charge_line_id,
                v_ledger_record.charge_no,
                v_ledger_record.account_id,
                NEW.id,
                'billing_ar_transaction',
                COALESCE(NEW.zeroed_datetime, get_site_timestamp(NEW.site_id)),
                get_site_timestamp(NEW.site_id),
                v_ledger_record.inventory_id,
                v_ledger_record.ledger_inventory_id,
                v_ledger_record.ledger_lot_id,
                v_ledger_record.ledger_serial_id,
                v_ledger_record.account_type,
                CASE 
                    WHEN v_ledger_record.transaction_type = 'Payment' THEN 'Payment Reversal'
                    WHEN v_ledger_record.transaction_type = 'Adjustment' THEN 'Adjustment Reversal'
                    WHEN v_ledger_record.transaction_type = 'Writeoff' THEN 'Writeoff Reversal'
                    WHEN v_ledger_record.transaction_type = 'Cash Allocation' THEN 'Cash Allocation Reversal'
                    ELSE v_ledger_record.transaction_type || ' Reversal'
                END,
                v_ledger_record.id,
                v_ledger_record.credit,  -- Swap credit and debit
                v_ledger_record.debit,   -- Swap credit and debit
                CURRENT_TIMESTAMP,
                v_creator,
                'Zeroed AR transaction: ' || COALESCE(NEW.zeroed_reason_id, 'No reason provided')
            );
            
            -- Store ID of this ledger entry for potential future reversals
            v_ledger_entry_ids := array_append(v_ledger_entry_ids, currval(pg_get_serial_sequence('form_ledger_finance', 'id')));
        END LOOP;

        NEW.zeroed_datetime := COALESCE(NEW.zeroed_datetime, get_site_timestamp(NEW.site_id));
        v_success := TRUE;
        RAISE LOG 'Successfully zeroed AR transaction: %', NEW.transaction_no;
        
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        -- Log the error
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Transaction failed during AR transaction zeroing',
            'TRIGGER',
            current_schema(),
            'billing_ar_transaction',
            jsonb_build_object(
                'function_name', 'process_ar_transaction_zero',
                'transaction_no', NEW.transaction_no,
                'success_flag', v_success
            )
        );
        
        RAISE; -- Re-throw the exception to rollback the transaction
    END;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Trigger for zeroing AR transactions
CREATE OR REPLACE TRIGGER trg_billing_ar_transaction_zero
BEFORE UPDATE ON form_billing_ar_transaction
FOR EACH ROW
WHEN (COALESCE(NEW.zeroed, 'No') = 'Yes' AND COALESCE(OLD.zeroed, 'No') IS DISTINCT FROM 'Yes')
EXECUTE FUNCTION process_ar_transaction_zero();