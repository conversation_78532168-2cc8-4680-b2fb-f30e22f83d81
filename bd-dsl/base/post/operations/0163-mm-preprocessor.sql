-- 0163-mm-preprocessor.sql
-- This file contains the preprocessor functions to build JSON blob for medical claims submission

-- Helper function to build other payer provider arrays
CREATE OR REPLACE FUNCTION mm_build_other_payer_providers(p_osub_id integer)
RETURNS jsonb AS $$
DECLARE
    v_providers jsonb;
    v_billing_providers jsonb = '[]'::jsonb;
    v_referring_providers jsonb = '[]'::jsonb;
    v_rendering_providers jsonb = '[]'::jsonb;
    v_supervising_providers jsonb = '[]'::jsonb;
    r_prov record;
BEGIN
    -- Get all provider records for this other subscriber
    FOR r_prov IN 
        SELECT oprovs.id
        FROM form_med_claim_osub os
        INNER JOIN sf_form_med_claim_osub_to_med_claim_oprovs sf ON sf.form_med_claim_osub_fk = os.id AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
        INNER JOIN form_med_claim_oprovs oprovs ON oprovs.id = sf.form_med_claim_oprovs_fk AND oprovs.deleted IS NOT TRUE AND oprovs.archived IS NOT TRUE
        WHERE os.id = p_osub_id
        ORDER BY oprovs.id
    LOOP
        -- Build billing provider array
        SELECT jsonb_agg(
            jsonb_build_object(
                'entityTypeQualifier', bill.entity_type_qualifier,
                'otherPayerBillingProviderIdentifier', COALESCE(
                    (SELECT jsonb_agg(
                        jsonb_build_object(
                            'qualifier', bi.qualifier,
                            'identifier', bi.identifier
                        ) ORDER BY bi.id
                    )
                    FROM sf_form_med_claim_oprov_bill_to_med_claim_obill_id sf_bi
                    INNER JOIN form_med_claim_obill_id bi ON bi.id = sf_bi.form_med_claim_obill_id_fk AND bi.deleted IS NOT TRUE AND bi.archived IS NOT TRUE
                    WHERE sf_bi.form_med_claim_oprov_bill_fk = bill.id
                    AND sf_bi.delete IS NOT TRUE
                    AND sf_bi.archive IS NOT TRUE),
                    '[]'::jsonb
                )
            )
        ) INTO v_billing_providers
        FROM sf_form_med_claim_oprovs_to_med_claim_oprov_bill sf_bill
        INNER JOIN form_med_claim_oprov_bill bill ON bill.id = sf_bill.form_med_claim_oprov_bill_fk AND bill.deleted IS NOT TRUE AND bill.archived IS NOT TRUE
        WHERE sf_bill.form_med_claim_oprovs_fk = r_prov.id
        AND sf_bill.delete IS NOT TRUE
        AND sf_bill.archive IS NOT TRUE;
        
        -- Build referring provider array
        SELECT jsonb_agg(
            jsonb_build_object(
                'otherPayerReferringProviderIdentifier', COALESCE(
                    (SELECT jsonb_agg(
                        jsonb_build_object(
                            'qualifier', ri.qualifier,
                            'identifier', ri.identifier
                        ) ORDER BY ri.id
                    )
                    FROM sf_form_med_claim_oprov_ref_to_med_claim_oref_id sf_ri
                    INNER JOIN form_med_claim_oref_id ri ON ri.id = sf_ri.form_med_claim_oref_id_fk AND ri.deleted IS NOT TRUE AND ri.archived IS NOT TRUE
                    WHERE sf_ri.form_med_claim_oprov_ref_fk = ref.id
                    AND sf_ri.delete IS NOT TRUE
                    AND sf_ri.archive IS NOT TRUE),
                    '[]'::jsonb
                )
            )
        ) INTO v_referring_providers
        FROM sf_form_med_claim_oprovs_to_med_claim_oprov_ref sf_ref
        INNER JOIN form_med_claim_oprov_ref ref ON ref.id = sf_ref.form_med_claim_oprov_ref_fk AND ref.deleted IS NOT TRUE AND ref.archived IS NOT TRUE
        WHERE sf_ref.form_med_claim_oprovs_fk = r_prov.id
        AND sf_ref.delete IS NOT TRUE
        AND sf_ref.archive IS NOT TRUE;

        -- Build rendering provider array
        SELECT jsonb_agg(
            jsonb_build_object(
                'entityTypeQualifier', rend.entity_type_qualifier,
                'otherPayerRenderingProviderIdentifier', COALESCE(
                    (SELECT jsonb_agg(
                        jsonb_build_object(
                            'qualifier', rendi.qualifier,
                            'identifier', rendi.identifier
                        ) ORDER BY rendi.id
                    )
                    FROM sf_form_med_claim_oprov_rend_to_med_claim_orend_id sf_rendi
                    INNER JOIN form_med_claim_orend_id rendi ON rendi.id = sf_rendi.form_med_claim_orend_id_fk AND rendi.deleted IS NOT TRUE AND rendi.archived IS NOT TRUE
                    WHERE sf_rendi.form_med_claim_oprov_rend_fk = rend.id
                    AND sf_rendi.delete IS NOT TRUE
                    AND sf_rendi.archive IS NOT TRUE),
                    '[]'::jsonb
                )
            )
        ) INTO v_rendering_providers
        FROM sf_form_med_claim_oprovs_to_med_claim_oprov_rend sf_rend
        INNER JOIN form_med_claim_oprov_rend rend ON rend.id = sf_rend.form_med_claim_oprov_rend_fk AND rend.deleted IS NOT TRUE AND rend.archived IS NOT TRUE
        WHERE sf_rend.form_med_claim_oprovs_fk = r_prov.id
        AND sf_rend.delete IS NOT TRUE
        AND sf_rend.archive IS NOT TRUE;
        
        -- Build supervising provider array
        SELECT jsonb_agg(
            jsonb_build_object(
                'otherPayerSupervisingProviderIdentifier', COALESCE(
                    (SELECT jsonb_agg(
                        jsonb_build_object(
                            'qualifier', si.qualifier,
                            'identifier', si.identifier
                        ) ORDER BY si.id
                    )
                    FROM sf_form_med_claim_oprov_sup_to_med_claim_osup_id sf_si
                    INNER JOIN form_med_claim_osup_id si ON si.id = sf_si.form_med_claim_osup_id_fk AND si.deleted IS NOT TRUE AND si.archived IS NOT TRUE
                    WHERE sf_si.form_med_claim_oprov_sup_fk = sup.id
                    AND sf_si.delete IS NOT TRUE
                    AND sf_si.archive IS NOT TRUE),
                    '[]'::jsonb
                )
            )
        ) INTO v_supervising_providers
        FROM sf_form_med_claim_oprovs_to_med_claim_oprov_sup sf_sup
        INNER JOIN form_med_claim_oprov_sup sup ON sup.id = sf_sup.form_med_claim_oprov_sup_fk AND sup.deleted IS NOT TRUE AND sup.archived IS NOT TRUE
        WHERE sf_sup.form_med_claim_oprovs_fk = r_prov.id
        AND sf_sup.delete IS NOT TRUE
        AND sf_sup.archive IS NOT TRUE;
    END LOOP;

    -- Return provider arrays
    RETURN jsonb_build_object(
        'otherPayerBillingProvider', COALESCE(v_billing_providers, '[]'::jsonb),
        'otherPayerReferringProvider', COALESCE(v_referring_providers, '[]'::jsonb),
        'otherPayerRenderingProvider', COALESCE(v_rendering_providers, '[]'::jsonb),
        'otherPayerSupervisingProvider', COALESCE(v_supervising_providers, '[]'::jsonb)
    );
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_other_payer_providers',
            'FUNCTION',
            current_schema(),
            'form_med_claim_oprov_bill',
            jsonb_build_object(
                'function_name', 'mm_build_other_payer_providers',
                'osub_id', p_osub_id
            )
        );
        RETURN jsonb_build_object(
            'otherPayerBillingProvider', '[]'::jsonb,
            'otherPayerReferringProvider', '[]'::jsonb,
            'otherPayerRenderingProvider', '[]'::jsonb,
            'otherPayerSupervisingProvider', '[]'::jsonb
        );
END;
$$ LANGUAGE plpgsql;

-- Function to build other subscriber information
CREATE OR REPLACE FUNCTION mm_build_other_subscriber(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_osub_array jsonb = '[]'::jsonb;
    v_osub jsonb;
    v_base_osub jsonb;
    v_adjustments jsonb;
    v_providers jsonb;
    v_osub_name jsonb;
    v_other_payer_name jsonb;
    v_osub_address jsonb;
    v_opayer_address jsonb;
    r_osub record;
BEGIN
    FOR r_osub IN 
        SELECT os.id
        FROM form_med_claim mc
        INNER JOIN sf_form_med_claim_to_med_claim_osub sf ON sf.form_med_claim_fk = mc.id AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
        INNER JOIN form_med_claim_osub os ON os.id = sf.form_med_claim_osub_fk AND os.deleted IS NOT TRUE AND os.archived IS NOT TRUE
        WHERE mc.id = p_med_claim_id AND os.deleted IS NOT TRUE AND os.archived IS NOT TRUE
        ORDER BY os.id
    LOOP
        -- Get claim level adjustments
        v_adjustments := mm_build_claim_adjustments(r_osub.id);
        
        -- Get other payer providers
        v_providers := mm_build_other_payer_providers(r_osub.id);
        
        -- Build other subscriber name with address
        SELECT jsonb_build_object(
            'otherInsuredQualifier', osn.other_insured_qualifier,
            'otherInsuredLastName', osn.other_insured_last_name,
            'otherInsuredFirstName', osn.other_insured_first_name,
            'otherInsuredMiddleName', osn.other_insured_middle_name,
            'otherInsuredIdentifierTypeCode', osn.other_insured_identifier_type_code,
            'otherInsuredIdentifier', osn.other_insured_identifier
        ) INTO v_osub_name
        FROM form_med_claim_osub os
        INNER JOIN sf_form_med_claim_osub_to_med_claim_osub_nm sf_osn ON sf_osn.form_med_claim_osub_fk = os.id AND sf_osn.delete IS NOT TRUE AND sf_osn.archive IS NOT TRUE
        INNER JOIN form_med_claim_osub_nm osn ON osn.id = sf_osn.form_med_claim_osub_nm_fk AND osn.deleted IS NOT TRUE AND osn.archived IS NOT TRUE
        WHERE os.id = r_osub.id AND os.deleted IS NOT TRUE AND os.archived IS NOT TRUE
        LIMIT 1;
        
        -- Get other subscriber address
        SELECT mm_build_address(
            a.address1,
            a.address2,
            a.city,
            a.state,
            a.postal_code
        ) INTO v_osub_address
        FROM form_med_claim_osub os
        INNER JOIN sf_form_med_claim_osub_to_med_claim_address_osub sf_a ON sf_a.form_med_claim_osub_fk = os.id AND sf_a.delete IS NOT TRUE AND sf_a.archive IS NOT TRUE
        INNER JOIN form_med_claim_address_osub a ON a.id = sf_a.form_med_claim_address_osub_fk AND a.deleted IS NOT TRUE AND a.archived IS NOT TRUE
        WHERE os.id = r_osub.id AND os.deleted IS NOT TRUE AND os.archived IS NOT TRUE
        LIMIT 1;
        
        -- Add address to osub_name if exists
        IF v_osub_address IS NOT NULL THEN
            v_osub_name := v_osub_name || jsonb_build_object('otherInsuredAddress', v_osub_address);
        END IF;
        
        -- Build other payer name
        SELECT jsonb_build_object(
            'otherPayerOrganizationName', op.other_payer_organization_name,
            'otherPayerIdentifierTypeCode', op.other_payer_identifier_type_code,
            'otherPayerIdentifier', op.other_payer_identifier,
            'otherPayerAdjudicationOrPaymentDate', mm_format_date(op.other_payer_adjudication_or_payment_date::date),
            'otherPayerClaimAdjustmentIndicator', op.other_payer_claim_adjustment_indicator,
            'otherPayerPriorAuthorizationNumber', op.other_payer_prior_authorization_number,
            'otherPayerClaimControlNumber', op.other_payer_claim_control_number,
            'otherPayerSecondaryIdentifier', COALESCE(
                (SELECT jsonb_agg(
                    jsonb_build_object(
                        'qualifier', opi.qualifier,
                        'identifier', opi.identifier
                    ) ORDER BY opi.id
                )
                FROM sf_form_med_claim_opayer_to_med_claim_opayer_id sf_opi
                LEFT JOIN form_med_claim_opayer_id opi ON opi.id = sf_opi.form_med_claim_opayer_id_fk
                WHERE sf_opi.form_med_claim_opayer_fk = op.id
                AND sf_opi.delete IS NOT TRUE
                AND sf_opi.archive IS NOT TRUE
                AND opi.deleted IS NOT TRUE
                AND opi.archived IS NOT TRUE),
                '[]'::jsonb
            )
        ) INTO v_other_payer_name
        FROM form_med_claim_osub os
        INNER JOIN sf_form_med_claim_osub_to_med_claim_opayer sf_op ON sf_op.form_med_claim_osub_fk = os.id AND sf_op.delete IS NOT TRUE AND sf_op.archive IS NOT TRUE
        INNER JOIN form_med_claim_opayer op ON op.id = sf_op.form_med_claim_opayer_fk AND op.deleted IS NOT TRUE AND op.archived IS NOT TRUE
        WHERE os.id = r_osub.id AND os.deleted IS NOT TRUE AND os.archived IS NOT TRUE
        LIMIT 1;
        
        -- Get other payer address
        SELECT mm_build_address(
            ap.address1,
            ap.address2,
            ap.city,
            ap.state,
            ap.postal_code
        ) INTO v_opayer_address
        FROM form_med_claim_osub os
        INNER JOIN sf_form_med_claim_osub_to_med_claim_opayer sf_op ON sf_op.form_med_claim_osub_fk = os.id AND sf_op.delete IS NOT TRUE AND sf_op.archive IS NOT TRUE
        INNER JOIN form_med_claim_opayer op ON op.id = sf_op.form_med_claim_opayer_fk AND op.deleted IS NOT TRUE AND op.archived IS NOT TRUE
        INNER JOIN sf_form_med_claim_opayer_to_med_claim_address_opay sf_ap ON sf_ap.form_med_claim_opayer_fk = op.id AND sf_ap.delete IS NOT TRUE AND sf_ap.archive IS NOT TRUE
        INNER JOIN form_med_claim_address_opay ap ON ap.id = sf_ap.form_med_claim_address_opay_fk AND ap.deleted IS NOT TRUE AND ap.archived IS NOT TRUE
        WHERE os.id = r_osub.id
        AND os.deleted IS NOT TRUE AND os.archived IS NOT TRUE
        LIMIT 1;
        
        -- Add address to other_payer_name if exists
        IF v_opayer_address IS NOT NULL THEN
            v_other_payer_name := v_other_payer_name || jsonb_build_object('otherPayerAddress', v_opayer_address);
        END IF;
        
        -- Build base other subscriber object
        SELECT jsonb_build_object(
            'paymentResponsibilityLevelCode', os.payment_responsibility_level_code,
            'individualRelationshipCode', os.individual_relationship_code,
            'claimFilingIndicatorCode', os.claim_filing_indicator_code,
            'insuranceTypeCode', os.insurance_type_code,
            'benefitsAssignmentCertificationIndicator', os.benefits_assignment_certification_indicator,
            'releaseOfInformationCode', os.release_of_information_code,
            'patientSignatureGeneratedForPatient', os.patient_signature_generate_for_patient,
            'insuranceGroupOrPolicyNumber', os.insurance_group_or_policy_number,
            'otherInsuredGroupName', os.other_insured_group_name,
            'payerPaidAmount', os.payer_paid_amount::text,
            'nonCoveredChargeAmount', os.non_covered_charge_amount::text,
            'remainingPatientLiability', os.remaining_patient_liability::text,
            'medicareOutpatientAdjudication', CASE 
                WHEN os.mcr_reimbursement_rate IS NOT NULL OR os.mcr_hcpcs_payable_amount IS NOT NULL OR EXISTS(
                    SELECT 1 FROM gr_form_med_claim_osub_mcr_rcodes_to_list_med_claim_ecl_id gr 
                    WHERE gr.form_med_claim_osub_fk = os.id
                ) THEN
                    jsonb_build_object(
                        'reimbursementRate', os.mcr_reimbursement_rate::text,
                        'hcpcsPayableAmount', os.mcr_hcpcs_payable_amount::text,
                        'claimPaymentRemarkCode', COALESCE(
                            (SELECT jsonb_agg(gr.form_list_med_claim_ecl_fk ORDER BY gr.id)
                             FROM gr_form_med_claim_osub_mcr_rcodes_to_list_med_claim_ecl_id gr
                             WHERE gr.form_med_claim_osub_fk = os.id),
                            '[]'::jsonb
                        )
                    )
                ELSE NULL
            END
        ) INTO v_base_osub
        FROM form_med_claim_osub os
        WHERE os.id = r_osub.id
        LIMIT 1;
        
        -- Merge all components
        v_osub := v_base_osub;
        
        IF v_osub_name IS NOT NULL THEN
            v_osub := v_osub || jsonb_build_object('otherSubscriberName', v_osub_name);
        END IF;
        
        IF v_other_payer_name IS NOT NULL THEN
            v_osub := v_osub || jsonb_build_object('otherPayerName', v_other_payer_name);
        END IF;
        
        IF v_adjustments IS NOT NULL THEN
            v_osub := v_osub || jsonb_build_object('claimLevelAdjustments', v_adjustments);
        END IF;
        
        IF v_providers IS NOT NULL THEN
            v_osub := v_osub || v_providers;
        END IF;
        
        v_osub_array := v_osub_array || mm_remove_nulls(v_osub);
    END LOOP;
    
    IF jsonb_array_length(v_osub_array) > 0 THEN
        RETURN v_osub_array;
    ELSE
        RETURN NULL;
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_other_subscriber',
            'FUNCTION',
            current_schema(),
            'form_med_claim_osub',
            jsonb_build_object(
                'function_name', 'mm_build_other_subscriber',
                'med_claim_id', p_med_claim_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build dependent segment (if exists)
CREATE OR REPLACE FUNCTION mm_build_dependent(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_dependent jsonb;
    v_address jsonb;
    v_contact jsonb;
BEGIN
    -- Get address
    SELECT mm_build_address(
        a.address1,
        a.address2,
        a.city,
        a.state,
        a.postal_code
    ) INTO v_address
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_dep sf_d ON sf_d.form_med_claim_fk = mc.id AND sf_d.delete IS NOT TRUE AND sf_d.archive IS NOT TRUE
    INNER JOIN form_med_claim_dep d ON d.id = sf_d.form_med_claim_dep_fk AND d.deleted IS NOT TRUE AND d.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_dep_to_med_claim_address_dep sf_a ON sf_a.form_med_claim_dep_fk = d.id AND sf_a.delete IS NOT TRUE AND sf_a.archive IS NOT TRUE
    INNER JOIN form_med_claim_address_dep a ON a.id = sf_a.form_med_claim_address_dep_fk AND a.deleted IS NOT TRUE AND a.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    AND d.deleted IS NOT TRUE AND d.archived IS NOT TRUE
    LIMIT 1;
    
    -- Get contact info
    SELECT mm_build_contact_info(
        c.name,
        c.phone_number,
        NULL::text, -- no phone extension in this form
        NULL::text, -- no fax number in this form
        c.email
    ) INTO v_contact
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_dep sf_d ON sf_d.form_med_claim_fk = mc.id AND sf_d.delete IS NOT TRUE AND sf_d.archive IS NOT TRUE
    INNER JOIN form_med_claim_dep d ON d.id = sf_d.form_med_claim_dep_fk AND d.deleted IS NOT TRUE AND d.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_dep_to_med_claim_dep_cont sf_c ON sf_c.form_med_claim_dep_fk = d.id AND sf_c.delete IS NOT TRUE AND sf_c.archive IS NOT TRUE
    INNER JOIN form_med_claim_dep_cont c ON c.id = sf_c.form_med_claim_dep_cont_fk AND c.deleted IS NOT TRUE AND c.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Build main dependent object
    SELECT jsonb_build_object(
        'memberId', d.member_id,
        'firstName', d.first_name,
        'lastName', d.last_name,
        'middleName', d.middle_name,
        'suffix', d.suffix,
        'dateOfBirth', mm_format_date(d.date_of_birth::date),
        'gender', d.gender,
        'relationshipToSubscriberCode', d.relationship_to_subscriber_code
    ) INTO v_dependent
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_dep sf_d ON sf_d.form_med_claim_fk = mc.id AND sf_d.delete IS NOT TRUE AND sf_d.archive IS NOT TRUE
    INNER JOIN form_med_claim_dep d ON d.id = sf_d.form_med_claim_dep_fk AND d.deleted IS NOT TRUE AND d.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Merge address and contact info
    IF v_address IS NOT NULL THEN
        v_dependent := v_dependent || jsonb_build_object('address', v_address);
    END IF;
    
    IF v_contact IS NOT NULL THEN
        v_dependent := v_dependent || jsonb_build_object('contactInformation', v_contact);
    END IF;
    
    RETURN mm_remove_nulls(v_dependent);
EXCEPTION
    WHEN OTHERS THEN
        -- Dependent is optional, so just log warning
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Dependent not found (optional): ' || SQLERRM,
            'Exception in mm_build_dependent',
            'FUNCTION',
            current_schema(),
            'form_med_claim_dep',
            jsonb_build_object(
                'function_name', 'mm_build_dependent',
                'med_claim_id', p_med_claim_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build diagnosis codes array
CREATE OR REPLACE FUNCTION mm_build_diagnosis_codes(p_med_claim_info_id integer)
RETURNS jsonb AS $$
DECLARE
    v_diagnoses jsonb;
BEGIN
    SELECT jsonb_agg(
        jsonb_build_object(
            'diagnosisCode', dx.diagnosis_code,
            'diagnosisTypeCode', dx.diagnosis_type_code
        ) ORDER BY dx.id
    ) INTO v_diagnoses
    FROM form_med_claim_info mci
    INNER JOIN sf_form_med_claim_info_to_med_claim_dx sf ON sf.form_med_claim_info_fk = mci.id AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
    INNER JOIN form_med_claim_dx dx ON dx.id = sf.form_med_claim_dx_fk AND dx.deleted IS NOT TRUE AND dx.archived IS NOT TRUE
    WHERE mci.id = p_med_claim_info_id
    AND mci.deleted IS NOT TRUE AND mci.archived IS NOT TRUE;
    
    RETURN COALESCE(v_diagnoses, '[]'::jsonb);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_diagnosis_codes',
            'FUNCTION',
            current_schema(),
            'form_med_claim_dx',
            jsonb_build_object(
                'function_name', 'mm_build_diagnosis_codes',
                'med_claim_info_id', p_med_claim_info_id
            )
        );
        RETURN '[]'::jsonb;
END;
$$ LANGUAGE plpgsql;

-- Function to build condition information array (up to 2 objects, each with up to 12 condition codes)
CREATE OR REPLACE FUNCTION mm_build_condition_codes(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_condition_info jsonb;
    v_med_claim_info_id integer;
BEGIN
    -- First get the med_claim_info_id
    SELECT mci.id INTO v_med_claim_info_id
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_info sf ON sf.form_med_claim_fk = mc.id AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
    INNER JOIN form_med_claim_info mci ON mci.id = sf.form_med_claim_info_fk AND mci.deleted IS NOT TRUE AND mci.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Build condition information as an array of up to 2 objects, each with up to 12 condition codes
    SELECT jsonb_agg(
        jsonb_build_object(
            'conditionCodes', cond_codes.codes
        ) ORDER BY cond_codes.cond_id
    ) INTO v_condition_info
    FROM (
        SELECT 
            cc.id as cond_id,
            COALESCE(
                jsonb_agg(gr.form_list_nubc_code_fk ORDER BY gr.id),
                '[]'::jsonb
            ) as codes
        FROM form_med_claim_info mci
        INNER JOIN sf_form_med_claim_info_to_med_claim_info_other sf_mio ON sf_mio.form_med_claim_info_fk = mci.id AND sf_mio.delete IS NOT TRUE AND sf_mio.archive IS NOT TRUE
        INNER JOIN form_med_claim_info_other mio ON mio.id = sf_mio.form_med_claim_info_other_fk AND mio.deleted IS NOT TRUE AND mio.archived IS NOT TRUE
        INNER JOIN sf_form_med_claim_info_other_to_med_claim_cond sf_cc ON sf_cc.form_med_claim_info_other_fk = mio.id AND sf_cc.delete IS NOT TRUE AND sf_cc.archive IS NOT TRUE
        INNER JOIN form_med_claim_cond cc ON cc.id = sf_cc.form_med_claim_cond_fk AND cc.deleted IS NOT TRUE AND cc.archived IS NOT TRUE
        INNER JOIN gr_form_med_claim_cond_condition_codes_to_list_nubc_code_id gr ON gr.form_med_claim_cond_fk = cc.id
        WHERE mci.id = v_med_claim_info_id
        AND mci.deleted IS NOT TRUE AND mci.archived IS NOT TRUE
        GROUP BY cc.id
    ) cond_codes
    WHERE jsonb_array_length(cond_codes.codes) > 0;
    
    RETURN COALESCE(v_condition_info, '[]'::jsonb);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_condition_codes',
            'FUNCTION',
            current_schema(),
            'form_med_claim_cond',
            jsonb_build_object(
                'function_name', 'mm_build_condition_codes',
                'med_claim_id', p_med_claim_id
            )
        );
        RETURN '[]'::jsonb;
END;
$$ LANGUAGE plpgsql;

-- Function to build claim file information
CREATE OR REPLACE FUNCTION mm_build_claim_file_information(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_file_info jsonb;
    v_med_claim_info_id integer;
BEGIN
    -- First get the med_claim_info_id
    SELECT mci.id INTO v_med_claim_info_id
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_info sf ON sf.form_med_claim_fk = mc.id AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
    INNER JOIN form_med_claim_info mci ON mci.id = sf.form_med_claim_info_fk AND mci.deleted IS NOT TRUE AND mci.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Build claim file information array, extracting only the filename from fi.file JSON string
    SELECT jsonb_agg(
        CASE 
            WHEN fi.file IS NOT NULL AND fi.file <> '' 
            THEN COALESCE(jsonb_extract_path_text(fi.file::jsonb, 'filename'), NULL)
            ELSE NULL
        END
        ORDER BY fi.id
    ) INTO v_file_info
    FROM form_med_claim_info mci
    INNER JOIN sf_form_med_claim_info_to_med_claim_info_other sf_mio ON sf_mio.form_med_claim_info_fk = mci.id AND sf_mio.delete IS NOT TRUE AND sf_mio.archive IS NOT TRUE
    INNER JOIN form_med_claim_info_other mio ON mio.id = sf_mio.form_med_claim_info_other_fk AND mio.deleted IS NOT TRUE AND mio.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_info_other_to_med_claim_file sf_file ON sf_file.form_med_claim_info_other_fk = mio.id AND sf_file.delete IS NOT TRUE AND sf_file.archive IS NOT TRUE
    INNER JOIN form_med_claim_file fi ON fi.id = sf_file.form_med_claim_file_fk AND fi.deleted IS NOT TRUE AND fi.archived IS NOT TRUE
    WHERE mci.id = v_med_claim_info_id
    AND mci.deleted IS NOT TRUE AND mci.archived IS NOT TRUE;
    
    RETURN COALESCE(v_file_info, '[]'::jsonb);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_claim_file_information',
            'FUNCTION',
            current_schema(),
            'form_med_claim_file',
            jsonb_build_object(
                'function_name', 'mm_build_claim_file_information',
                'med_claim_id', p_med_claim_id
            )
        );
        RETURN '[]'::jsonb;
END;
$$ LANGUAGE plpgsql;

-- Function to build claim information segment
CREATE OR REPLACE FUNCTION mm_build_claim_information(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_claim_info jsonb;
    v_med_claim_info_id integer;
    v_diagnoses jsonb;
    v_condition_info jsonb;
    v_claim_dates jsonb;
    v_file_info jsonb;
BEGIN
    -- Get med_claim_info_id
    SELECT mci.id INTO v_med_claim_info_id
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_info sf ON sf.form_med_claim_fk = mc.id AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
    INNER JOIN form_med_claim_info mci ON mci.id = sf.form_med_claim_info_fk AND mci.deleted IS NOT TRUE AND mci.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    IF v_med_claim_info_id IS NULL THEN
        RAISE EXCEPTION 'Claim information not found for med_claim_id: %', p_med_claim_id;
    END IF;
    
    -- Get diagnoses
    v_diagnoses = mm_build_diagnosis_codes(v_med_claim_info_id);
    
    -- Get condition codes
    v_condition_info = mm_build_condition_codes(p_med_claim_id);
    
    -- Build claim dates
    SELECT jsonb_build_object(
        'symptomDate', mm_format_date(cd.symptom_date::date),
        'initialTreatmentDate', mm_format_date(cd.initial_treatment_date::date),
        'lastSeenDate', mm_format_date(cd.last_seen_date::date),
        'acuteManifestationDate', mm_format_date(cd.acute_manifestation_date::date),
        'accidentDate', mm_format_date(cd.accident_date::date),
        'lastMenstrualPeriod', mm_format_date(cd.last_menstrual_period_date::date),
        'disabilityBeginDate', mm_format_date(cd.disability_begin_date::date),
        'disabilityEndDate', mm_format_date(cd.disability_end_date::date),
        'lastWorkedDate', mm_format_date(cd.last_worked_date::date),
        'authorizedReturnToWorkDate', mm_format_date(cd.authorized_return_to_work_date::date),
        'firstContactDate', mm_format_date(cd.first_contact_date::date),
        'assumedAndRelinquishedCareBeginDate', mm_format_date(cd.assumed_and_relinquished_care_begin_date::date),
        'assumedAndRelinquishedCareEndDate', mm_format_date(cd.assumed_and_relinquished_care_end_date::date),
        'admissionDate', mm_format_date(cd.admission_date::date),
        'dischargeDate', mm_format_date(cd.discharge_date::date),
        'repricerReceivedDate', mm_format_date(cd.repricer_received_date::date)
    ) INTO v_claim_dates
    FROM form_med_claim_info mci
    INNER JOIN sf_form_med_claim_info_to_med_claim_info_other sf_mio ON sf_mio.form_med_claim_info_fk = mci.id AND sf_mio.delete IS NOT TRUE AND sf_mio.archive IS NOT TRUE
    INNER JOIN form_med_claim_info_other mio ON mio.id = sf_mio.form_med_claim_info_other_fk AND mio.deleted IS NOT TRUE AND mio.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_info_other_to_med_claim_dates sf_cd ON sf_cd.form_med_claim_info_other_fk = mio.id AND sf_cd.delete IS NOT TRUE AND sf_cd.archive IS NOT TRUE
    INNER JOIN form_med_claim_dates cd ON cd.id = sf_cd.form_med_claim_dates_fk AND cd.deleted IS NOT TRUE AND cd.archived IS NOT TRUE
    WHERE mci.id = v_med_claim_info_id
    AND mci.deleted IS NOT TRUE AND mci.archived IS NOT TRUE
    LIMIT 1;
    
    -- Build main claim information
    SELECT jsonb_build_object(
        'patientControlNumber', mci.patient_control_number,
        'claimChargeAmount', mci.claim_charge_amount::text,
        'patientAmountPaid', CASE 
            WHEN mci.patient_amount_paid IS NOT NULL 
            THEN mci.patient_amount_paid::text 
            ELSE NULL 
        END,
        'placeOfServiceCode', mci.place_of_service_code,
        'claimFrequencyCode', mci.claim_frequency_code,
        'releaseOfInformationCode', mci.release_information_code,
        'patientSignatureSourceCode', mci.patient_signature_source_code,
        'specialProgramCode', mci.special_program_code,
        'delayReasonCode', mci.delay_reason_code,
        'claimFilingCode', mci.claim_filing_code,
        'planParticipationCode', mci.plan_participation_code,
        'benefitsAssignmentCertificationIndicator', mci.benefits_assignment_certification_indicator,
        'providerOrSupplierSignatureIndicator', COALESCE(mci.signature_indicator, 'Y'),
        'homeboundIndicator', mci.homebound_indicator,
        'pregnancyIndicator', mci.pregnancy_indicator,
        'deathDate', mm_format_date(mci.death_date::date),
        'patientWeight', CASE 
            WHEN mci.patient_weight IS NOT NULL 
            THEN mci.patient_weight::text 
            ELSE NULL 
        END,
        'serviceLines', '[]'::jsonb -- Will be filled later
    ) INTO v_claim_info
    FROM form_med_claim_info mci
    WHERE mci.id = v_med_claim_info_id;
    
    -- Merge JSONB objects to avoid double-escaping
    IF v_diagnoses IS NOT NULL THEN
        v_claim_info := v_claim_info || jsonb_build_object('healthCareCodeInformation', v_diagnoses);
    END IF;
    
    IF v_condition_info IS NOT NULL THEN
        v_claim_info := v_claim_info || jsonb_build_object('conditionInformation', v_condition_info);
    END IF;
    
    IF v_claim_dates IS NOT NULL THEN
        v_claim_info := v_claim_info || jsonb_build_object('claimDateInformation', mm_remove_nulls(v_claim_dates));
    END IF;
    
    -- Add claim note
    v_claim_info := v_claim_info || jsonb_build_object('claimNote', mm_build_claim_note(p_med_claim_id));
    
    -- Add supplemental information
    v_claim_info := v_claim_info || jsonb_build_object('claimSupplementalInformation', mm_build_claim_supplemental(p_med_claim_id));
    
    -- Add contract information
    v_claim_info := v_claim_info || jsonb_build_object('claimContractInformation', mm_build_claim_contract(p_med_claim_id));
    
    -- Add file information
    v_file_info := mm_build_claim_file_information(p_med_claim_id);
    IF v_file_info IS NOT NULL AND jsonb_array_length(v_file_info) > 0 THEN
        v_claim_info := v_claim_info || jsonb_build_object('fileInformationList', v_file_info);
    END IF;
    
    -- Add other subscriber information
    v_claim_info := v_claim_info || jsonb_build_object('otherSubscriberInformation', mm_build_other_subscriber(p_med_claim_id));
    
    RETURN mm_remove_nulls(v_claim_info);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_claim_information',
            'FUNCTION',
            current_schema(),
            'form_med_claim_info',
            jsonb_build_object(
                'function_name', 'mm_build_claim_information',
                'med_claim_id', p_med_claim_id
            )
        );
        RAISE;
END;
$$ LANGUAGE plpgsql;

-- Function to build claim note information
CREATE OR REPLACE FUNCTION mm_build_claim_note(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_claim_note jsonb;
    v_med_claim_info_id integer;
BEGIN
    -- First get the med_claim_info_id
    SELECT mci.id INTO v_med_claim_info_id
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_info sf ON sf.form_med_claim_fk = mc.id AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
    INNER JOIN form_med_claim_info mci ON mci.id = sf.form_med_claim_info_fk AND mci.deleted IS NOT TRUE AND mci.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Build claim note
    SELECT jsonb_build_object(
        'additionalInformation', cn.additional_information,
        'certificationNarrative', cn.certification_narrative,
        'goalRehabOrDischargePlans', cn.goal_rehab_or_discharge_plans,
        'diagnosisDescription', cn.diagnosis_description,
        'thirdPartOrgNotes', cn.third_part_org_notes
    ) INTO v_claim_note
    FROM form_med_claim_info mci
    INNER JOIN sf_form_med_claim_info_to_med_claim_note sf_cn ON sf_cn.form_med_claim_info_fk = mci.id AND sf_cn.delete IS NOT TRUE AND sf_cn.archive IS NOT TRUE
    INNER JOIN form_med_claim_note cn ON cn.id = sf_cn.form_med_claim_note_fk AND cn.deleted IS NOT TRUE AND cn.archived IS NOT TRUE
    WHERE mci.id = v_med_claim_info_id
    AND mci.deleted IS NOT TRUE AND mci.archived IS NOT TRUE
    LIMIT 1;
    
    RETURN mm_remove_nulls(v_claim_note);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_claim_note',
            'FUNCTION',
            current_schema(),
            'form_med_claim_note',
            jsonb_build_object(
                'function_name', 'mm_build_claim_note',
                'med_claim_id', p_med_claim_id
            )
        );
        RAISE;
END;
$$ LANGUAGE plpgsql;

-- Function to build claim supplemental information
CREATE OR REPLACE FUNCTION mm_build_claim_supplemental(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_supplemental jsonb;
    v_med_claim_info_id integer;
BEGIN
    -- First get the med_claim_info_id
    SELECT mci.id INTO v_med_claim_info_id
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_info sf ON sf.form_med_claim_fk = mc.id AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
    INNER JOIN form_med_claim_info mci ON mci.id = sf.form_med_claim_info_fk AND mci.deleted IS NOT TRUE AND mci.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Build claim supplemental information
    SELECT jsonb_build_object(
        'claimNumber', cs.claim_number,
        'claimControlNumber', cs.claim_control_number,
        'medicareCrossoverReferenceId', cs.medicare_crossover_reference_id,
        'priorAuthorizationNumber', cs.prior_authorization_number,
        'medicalRecordNumber', cs.medical_record_number,
        'reportInformation', COALESCE(
            (SELECT jsonb_agg(
                jsonb_build_object(
                    'attachmentReportTypeCode', r.attachment_report_type_code,
                    'attachmentTransmissionCode', r.attachment_transmission_code,
                    'attachmentControlNumber', r.attachment_control_number
                ) ORDER BY r.id
            )
            FROM sf_form_med_claim_supplemental_to_med_claim_report sf_r
            LEFT JOIN form_med_claim_report r ON r.id = sf_r.form_med_claim_report_fk
            WHERE sf_r.form_med_claim_supplemental_fk = cs.id
            AND sf_r.delete IS NOT TRUE
            AND sf_r.archive IS NOT TRUE
            AND r.deleted IS NOT TRUE
            AND r.archived IS NOT TRUE),
            '[]'::jsonb
        )
    ) INTO v_supplemental
    FROM form_med_claim_info mci
    INNER JOIN sf_form_med_claim_info_to_med_claim_supplemental sf_cs ON sf_cs.form_med_claim_info_fk = mci.id AND sf_cs.delete IS NOT TRUE AND sf_cs.archive IS NOT TRUE
    INNER JOIN form_med_claim_supplemental cs ON cs.id = sf_cs.form_med_claim_supplemental_fk AND cs.deleted IS NOT TRUE AND cs.archived IS NOT TRUE
    WHERE mci.id = v_med_claim_info_id
    AND mci.deleted IS NOT TRUE AND mci.archived IS NOT TRUE
    LIMIT 1;
    
    RETURN mm_remove_nulls(v_supplemental);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_claim_supplemental',
            'FUNCTION',
            current_schema(),
            'form_med_claim_supplemental',
            jsonb_build_object(
                'function_name', 'mm_build_claim_supplemental',
                'med_claim_id', p_med_claim_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build claim contract information
CREATE OR REPLACE FUNCTION mm_build_claim_contract(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_contract jsonb;
    v_med_claim_info_id integer;
BEGIN
    -- First get the med_claim_info_id
    SELECT mci.id INTO v_med_claim_info_id
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_info sf ON sf.form_med_claim_fk = mc.id AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
    INNER JOIN form_med_claim_info mci ON mci.id = sf.form_med_claim_info_fk AND mci.deleted IS NOT TRUE AND mci.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Build claim contract information
    SELECT jsonb_build_object(
        'contractTypeCode', cc.contract_type_code,
        'contractCode', cc.contract_code,
        'contractVersionIdentifier', cc.contract_version_identifier,
        'contractAmount', CASE 
            WHEN cc.contract_amount IS NOT NULL 
            THEN cc.contract_amount::text 
            ELSE NULL 
        END,
        'contractPercentage', CASE 
            WHEN cc.contract_percentage IS NOT NULL 
            THEN cc.contract_percentage::text 
            ELSE NULL 
        END,
        'termsDiscountPercentage', CASE 
            WHEN cc.terms_discount_percentage IS NOT NULL 
            THEN cc.terms_discount_percentage::text 
            ELSE NULL 
        END
    ) INTO v_contract
    FROM form_med_claim_info mci
    INNER JOIN sf_form_med_claim_info_to_med_claim_info_other sf_mio ON sf_mio.form_med_claim_info_fk = mci.id AND sf_mio.delete IS NOT TRUE AND sf_mio.archive IS NOT TRUE
    INNER JOIN form_med_claim_info_other mio ON mio.id = sf_mio.form_med_claim_info_other_fk AND mio.deleted IS NOT TRUE AND mio.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_info_other_to_med_claim_contract sf_cc ON sf_cc.form_med_claim_info_other_fk = mio.id AND sf_cc.delete IS NOT TRUE AND sf_cc.archive IS NOT TRUE
    INNER JOIN form_med_claim_contract cc ON cc.id = sf_cc.form_med_claim_contract_fk AND cc.deleted IS NOT TRUE AND cc.archived IS NOT TRUE
    WHERE mci.id = v_med_claim_info_id
    AND mci.deleted IS NOT TRUE AND mci.archived IS NOT TRUE
    LIMIT 1;
    
    RETURN mm_remove_nulls(v_contract);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_claim_contract',
            'FUNCTION',
            current_schema(),
            'form_med_claim_contract',
            jsonb_build_object(
                'function_name', 'mm_build_claim_contract',
                'med_claim_id', p_med_claim_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build professional service segment for a service line
CREATE OR REPLACE FUNCTION mm_build_professional_service(p_service_line_id integer)
RETURNS jsonb AS $$
DECLARE
    v_service jsonb;
    v_modifiers jsonb;
BEGIN
    -- Build modifiers array from modifier_1, modifier_2, modifier_3, modifier_4
    SELECT jsonb_build_array(
        NULLIF(sv.modifier_1, ''),
        NULLIF(sv.modifier_2, ''),
        NULLIF(sv.modifier_3, ''),
        NULLIF(sv.modifier_4, '')
    ) - 'null' INTO v_modifiers
    FROM form_med_claim_sl sl
    INNER JOIN sf_form_med_claim_sl_to_med_claim_sv sf_sv ON sf_sv.form_med_claim_sl_fk = sl.id AND sf_sv.delete IS NOT TRUE AND sf_sv.archive IS NOT TRUE
    INNER JOIN form_med_claim_sv sv ON sv.id = sf_sv.form_med_claim_sv_fk AND sv.deleted IS NOT TRUE AND sv.archived IS NOT TRUE
    WHERE sl.id = p_service_line_id
    AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE
    LIMIT 1;
    
    SELECT jsonb_build_object(
        'procedureIdentifier', sv.procedure_identifier,
        'procedureCode', sv.procedure_code,
        'description', sv.description,
        'procedureModifiers', CASE 
            WHEN jsonb_array_length(v_modifiers) > 0 THEN v_modifiers
            ELSE NULL
        END,
        'lineItemChargeAmount', sv.line_item_charge_amount::text,
        'measurementUnit', sv.measurement_unit,
        'serviceUnitCount', sv.service_unit_count::text,
        'placeOfServiceCode', sv.place_of_service_code,
        'compositeDiagnosisCodePointers', jsonb_build_object(
            'diagnosisCodePointers', '[]'::jsonb -- Will be filled by calling function
        ),
        'emergencyIndicator', sv.emergency_indicator,
        'epsdtIndicator', sv.epsdt_indicator,
        'copayStatusCode', sv.copay_status_code
    ) INTO v_service
    FROM form_med_claim_sl sl
    INNER JOIN sf_form_med_claim_sl_to_med_claim_sv sf_sv ON sf_sv.form_med_claim_sl_fk = sl.id AND sf_sv.delete IS NOT TRUE AND sf_sv.archive IS NOT TRUE
    INNER JOIN form_med_claim_sv sv ON sv.id = sf_sv.form_med_claim_sv_fk AND sv.deleted IS NOT TRUE AND sv.archived IS NOT TRUE
    WHERE sl.id = p_service_line_id
    AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE
    LIMIT 1;
    
    RETURN mm_remove_nulls(v_service);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_professional_service',
            'FUNCTION',
            current_schema(),
            'form_med_claim_sl',
            jsonb_build_object(
                'function_name', 'mm_build_professional_service',
                'service_line_id', p_service_line_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build DME service segment for a service line
CREATE OR REPLACE FUNCTION mm_build_dme_service(p_service_line_id integer)
RETURNS jsonb AS $$
DECLARE
    v_service jsonb;
    v_rental_price text;
    v_purchase_price text;
BEGIN
    -- Note: The form_med_claim_dme table is missing several columns that are typically needed
    -- for DME services (procedure_code, line_item_charge_amount, service_unit_count, measurement_unit)
    -- Building with available fields only
    SELECT jsonb_build_object(
        'days', dme.days::text,
        'rentalPrice', dme.rental_price::text,
        'purchasePrice', dme.purchase_price::text,
        'frequencyCode', dme.frequency_code
    ) INTO v_service
    FROM form_med_claim_sl sl
    INNER JOIN sf_form_med_claim_sl_to_med_claim_dme sf_dme ON sf_dme.form_med_claim_sl_fk = sl.id AND sf_dme.delete IS NOT TRUE AND sf_dme.archive IS NOT TRUE
    INNER JOIN form_med_claim_dme dme ON dme.id = sf_dme.form_med_claim_dme_fk AND dme.deleted IS NOT TRUE AND dme.archived IS NOT TRUE
    WHERE sl.id = p_service_line_id
    AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE
    LIMIT 1;
    
    RETURN mm_remove_nulls(v_service);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_dme_service',
            'FUNCTION',
            current_schema(),
            'form_med_claim_dme',
            jsonb_build_object(
                'function_name', 'mm_build_dme_service',
                'service_line_id', p_service_line_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build DME certification segment
CREATE OR REPLACE FUNCTION mm_build_dme_certification(p_service_line_id integer)
RETURNS jsonb AS $$
DECLARE
    v_cert jsonb;
BEGIN
    -- Check if send_cert is 'Yes' to include certification
    SELECT CASE 
        WHEN cert.send_cert = 'Yes' THEN
            jsonb_build_object(
                'certificationTypeCode', cert.certification_type_code,
                'durableMedicalEquipmentDurationInMonths', cert.durable_medical_equipment_duration_in_months::text
            )
        ELSE NULL
    END INTO v_cert
    FROM form_med_claim_sl sl
    INNER JOIN sf_form_med_claim_sl_to_med_claim_dme_cert sf_cert ON sf_cert.form_med_claim_sl_fk = sl.id AND sf_cert.delete IS NOT TRUE AND sf_cert.archive IS NOT TRUE
    INNER JOIN form_med_claim_dme_cert cert ON cert.id = sf_cert.form_med_claim_dme_cert_fk AND cert.deleted IS NOT TRUE AND cert.archived IS NOT TRUE
    WHERE sl.id = p_service_line_id
    AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE
    LIMIT 1;
    
    RETURN mm_remove_nulls(v_cert);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_dme_certification',
            'FUNCTION',
            current_schema(),
            'form_med_claim_dme_cert',
            jsonb_build_object(
                'function_name', 'mm_build_dme_certification',
                'service_line_id', p_service_line_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build condition indicator DME segment
CREATE OR REPLACE FUNCTION mm_build_condition_indicator_dme(p_service_line_id integer)
RETURNS jsonb AS $$
DECLARE
    v_condition jsonb;
BEGIN
    SELECT CASE 
        WHEN dc.certification_condition_indicator IS NOT NULL THEN
            jsonb_build_object(
                'certificationConditionIndicator', dc.certification_condition_indicator,
                'conditionIndicator', dc.condition_indicator,
                'conditionIndicatorCode', dc.condition_indicator_code
            )
        ELSE NULL
    END INTO v_condition
    FROM form_med_claim_sl sl
    INNER JOIN sf_form_med_claim_sl_to_med_claim_dme_cond sf_dc ON sf_dc.form_med_claim_sl_fk = sl.id AND sf_dc.delete IS NOT TRUE AND sf_dc.archive IS NOT TRUE
    INNER JOIN form_med_claim_dme_cond dc ON dc.id = sf_dc.form_med_claim_dme_cond_fk AND dc.deleted IS NOT TRUE AND dc.archived IS NOT TRUE
    WHERE sl.id = p_service_line_id
    AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE
    LIMIT 1;
    
    RETURN mm_remove_nulls(v_condition);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_condition_indicator_dme',
            'FUNCTION',
            current_schema(),
            'form_med_claim_dme_cond',
            jsonb_build_object(
                'function_name', 'mm_build_condition_indicator_dme',
                'service_line_id', p_service_line_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build drug identification segment
CREATE OR REPLACE FUNCTION mm_build_drug_identification(p_service_line_id integer)
RETURNS jsonb AS $$
DECLARE
    v_drug jsonb;
BEGIN
    SELECT jsonb_build_object(
        'serviceIdQualifier', di.service_id_qualifier,
        'nationalDrugCode', di.national_drug_code,
        'nationalDrugUnitCount', di.national_drug_unit_count::text,
        'measurementUnitCode', di.measurement_unit_code,
        'linkSequenceNumber', di.link_sequence_number,
        'pharmacyPrescriptionNumber', di.pharmacy_prescription_number
    ) INTO v_drug
    FROM form_med_claim_sl sl
    INNER JOIN sf_form_med_claim_sl_to_med_claim_sl_di sf_di ON sf_di.form_med_claim_sl_fk = sl.id AND sf_di.delete IS NOT TRUE AND sf_di.archive IS NOT TRUE
    INNER JOIN form_med_claim_sl_di di ON di.id = sf_di.form_med_claim_sl_di_fk AND di.deleted IS NOT TRUE AND di.archived IS NOT TRUE
    WHERE sl.id = p_service_line_id
    AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE
    LIMIT 1;
    
    RETURN mm_remove_nulls(v_drug);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_drug_identification',
            'FUNCTION',
            current_schema(),
            'form_med_claim_sl_di',
            jsonb_build_object(
                'function_name', 'mm_build_drug_identification',
                'service_line_id', p_service_line_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to get diagnosis code pointers for a service line
CREATE OR REPLACE FUNCTION mm_get_diagnosis_pointers(
    p_service_line_id integer,
    p_med_claim_info_id integer
)
RETURNS jsonb AS $$
DECLARE
    v_pointers jsonb;
    v_dx_id_array integer[];
    v_all_dx_ids integer[];
    v_pointer_array integer[];
    i integer;
    j integer;
BEGIN
    -- Get all diagnosis IDs in order
    SELECT array_agg(dx.id ORDER BY dx.id)
    INTO v_all_dx_ids
    FROM form_med_claim_info mci
    INNER JOIN sf_form_med_claim_info_to_med_claim_dx sf ON sf.form_med_claim_info_fk = mci.id AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
    INNER JOIN form_med_claim_dx dx ON dx.id = sf.form_med_claim_dx_fk AND dx.deleted IS NOT TRUE AND dx.archived IS NOT TRUE
    WHERE mci.id = p_med_claim_info_id
    AND mci.deleted IS NOT TRUE AND mci.archived IS NOT TRUE;
    
    -- Get the diagnosis IDs linked to this service line (optimized)
    SELECT array_agg(DISTINCT dx_id) INTO v_dx_id_array
    FROM (
        SELECT unnest(ARRAY[sv.dx_id_1, sv.dx_id_2, sv.dx_id_3, sv.dx_id_4]) AS dx_id
        FROM form_med_claim_sl sl
        INNER JOIN sf_form_med_claim_sl_to_med_claim_sv sf_sv 
            ON sf_sv.form_med_claim_sl_fk = sl.id 
            AND sf_sv.delete IS NOT TRUE 
            AND sf_sv.archive IS NOT TRUE
        INNER JOIN form_med_claim_sv sv 
            ON sv.id = sf_sv.form_med_claim_sv_fk 
            AND sv.deleted IS NOT TRUE 
            AND sv.archived IS NOT TRUE
        WHERE sl.id = p_service_line_id 
        AND sl.deleted IS NOT TRUE 
        AND sl.archived IS NOT TRUE
    ) AS dx_codes
    WHERE dx_id IS NOT NULL;

    -- Convert diagnosis IDs to index positions (1-based)
    IF v_dx_id_array IS NOT NULL AND array_length(v_dx_id_array, 1) > 0 THEN
        v_pointer_array := ARRAY[]::integer[];
        FOR i IN 1..array_length(v_dx_id_array, 1) LOOP
            -- Find position of diagnosis ID in all diagnoses array
            FOR j IN 1..array_length(v_all_dx_ids, 1) LOOP
                IF v_all_dx_ids[j] = v_dx_id_array[i] THEN
                    v_pointer_array := array_append(v_pointer_array, j);
                    EXIT;
                END IF;
            END LOOP;
        END LOOP;
        
        -- Convert to JSON array of strings
        SELECT jsonb_agg(pointer::text ORDER BY pointer)
        INTO v_pointers
        FROM unnest(v_pointer_array) AS pointer;
    END IF;
    
    RETURN COALESCE(v_pointers, '[]'::jsonb);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_get_diagnosis_pointers',
            'FUNCTION',
            current_schema(),
            'form_med_claim_sv',
            jsonb_build_object(
                'function_name', 'mm_get_diagnosis_pointers',
                'service_line_id', p_service_line_id,
                'med_claim_info_id', p_med_claim_info_id
            )
        );
        RETURN '[]'::jsonb;
END;
$$ LANGUAGE plpgsql;

-- Function to build service line dates
CREATE OR REPLACE FUNCTION mm_build_service_line_dates(p_service_line_id integer)
RETURNS jsonb AS $$
DECLARE
    v_dates jsonb;
BEGIN
    SELECT jsonb_build_object(
        'prescriptionDate', mm_format_date(dt.prescription_date::date),
        'certificationRevisionOrRecertificationDate', mm_format_date(dt.certification_revision_or_recertification_date::date),
        'beginTherapyDate', mm_format_date(dt.begin_therapy_date::date),
        'lastCertificationDate', mm_format_date(dt.last_certification_date::date),
        'treatmentOrTherapyDate', mm_format_date(dt.treatment_or_therapy_date::date),
        'hemoglobinTestDate', mm_format_date(dt.hemoglobin_test_date::date),
        'serumCreatineTestDate', mm_format_date(dt.serum_creatine_test_date::date),
        'shippedDate', mm_format_date(dt.shipped_date::date),
        'initialTreatmentDate', mm_format_date(dt.initial_treatment_date::date)
    ) INTO v_dates
    FROM form_med_claim_sl sl
    LEFT JOIN sf_form_med_claim_sl_to_med_claim_sl_dt sf_dt ON sf_dt.form_med_claim_sl_fk = sl.id
    LEFT JOIN form_med_claim_sl_dt dt ON dt.id = sf_dt.form_med_claim_sl_dt_fk
    WHERE sl.id = p_service_line_id
    AND sl.deleted IS NOT TRUE
    AND sl.archived IS NOT TRUE
    LIMIT 1;
    
    -- Only return if there are actual date values
    IF v_dates IS NOT NULL AND v_dates::text != '{}'::jsonb::text THEN
        RETURN mm_remove_nulls(v_dates);
    ELSE
        RETURN NULL;
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_service_line_dates',
            'FUNCTION',
            current_schema(),
            'form_med_claim_sl_dt',
            jsonb_build_object(
                'function_name', 'mm_build_service_line_dates',
                'service_line_id', p_service_line_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build service line form identification
CREATE OR REPLACE FUNCTION mm_build_service_line_form_identification(p_service_line_id integer)
RETURNS jsonb AS $$
DECLARE
    v_form_identification jsonb;
BEGIN
    -- Build form identification array
    SELECT jsonb_agg(
        jsonb_build_object(
            'formTypeCode', fi.form_type_code,
            'formIdentifier', fi.form_identifier
        ) ORDER BY fi.id
    ) INTO v_form_identification
    FROM sf_form_med_claim_sl_to_med_claim_sl_fi sf_fi
    INNER JOIN form_med_claim_sl_fi fi ON fi.id = sf_fi.form_med_claim_sl_fi_fk AND fi.deleted IS NOT TRUE AND fi.archived IS NOT TRUE
    WHERE sf_fi.form_med_claim_sl_fk = p_service_line_id
    AND sf_fi.delete IS NOT TRUE
    AND sf_fi.archive IS NOT TRUE;
    
    RETURN COALESCE(v_form_identification, '[]'::jsonb);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_service_line_form_identification',
            'FUNCTION',
            current_schema(),
            'form_med_claim_sl_fi',
            jsonb_build_object(
                'function_name', 'mm_build_service_line_form_identification',
                'service_line_id', p_service_line_id
            )
        );
        RETURN '[]'::jsonb;
END;
$$ LANGUAGE plpgsql;

-- Function to build service line supplemental information  
CREATE OR REPLACE FUNCTION mm_build_service_line_supplemental(p_service_line_id integer)
RETURNS jsonb AS $$
DECLARE
    v_supplemental jsonb;
BEGIN
    -- Build service line supplemental information array
    SELECT jsonb_agg(
        jsonb_build_object(
            'attachmentReportTypeCode', sup.attachment_report_type_code,
            'attachmentTransmissionCode', sup.attachment_transmission_code,
            'attachmentControlNumber', sup.attachment_control_number
        ) ORDER BY sup.id
    ) INTO v_supplemental
    FROM sf_form_med_claim_sl_to_med_claim_sl_sup sf_sup
    INNER JOIN form_med_claim_sl_sup sup ON sup.id = sf_sup.form_med_claim_sl_sup_fk AND sup.deleted IS NOT TRUE AND sup.archived IS NOT TRUE
    WHERE sf_sup.form_med_claim_sl_fk = p_service_line_id
    AND sf_sup.delete IS NOT TRUE
    AND sf_sup.archive IS NOT TRUE;
    
    RETURN COALESCE(v_supplemental, '[]'::jsonb);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_service_line_supplemental',
            'FUNCTION',
            current_schema(),
            'form_med_claim_sl_sup',
            jsonb_build_object(
                'function_name', 'mm_build_service_line_supplemental',
                'service_line_id', p_service_line_id
            )
        );
        RETURN '[]'::jsonb;
END;
$$ LANGUAGE plpgsql;

-- Function to build service line file information
CREATE OR REPLACE FUNCTION mm_build_service_line_file_information(p_service_line_id integer)
RETURNS jsonb AS $$
DECLARE
    v_file_info jsonb;
BEGIN
    -- Build service line file information array, extracting only the filename from fi.file JSON string
    SELECT jsonb_agg(
        CASE 
            WHEN fi.file IS NOT NULL AND fi.file <> '' 
            THEN COALESCE(jsonb_extract_path_text(fi.file::jsonb, 'filename'), NULL)
            ELSE NULL
        END
        ORDER BY fi.id
    ) INTO v_file_info
    FROM sf_form_med_claim_sl_to_med_claim_sl_file sf_file
    INNER JOIN form_med_claim_sl_file fi ON fi.id = sf_file.form_med_claim_sl_file_fk AND fi.deleted IS NOT TRUE AND fi.archived IS NOT TRUE
    WHERE sf_file.form_med_claim_sl_fk = p_service_line_id
    AND sf_file.delete IS NOT TRUE
    AND sf_file.archive IS NOT TRUE;
    
    RETURN COALESCE(v_file_info, '[]'::jsonb);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_service_line_file_information',
            'FUNCTION',
            current_schema(),
            'form_med_claim_sl_file',
            jsonb_build_object(
                'function_name', 'mm_build_service_line_file_information',
                'service_line_id', p_service_line_id
            )
        );
        RETURN '[]'::jsonb;
END;
$$ LANGUAGE plpgsql;

-- Function to build DME certificate of medical necessity
CREATE OR REPLACE FUNCTION mm_build_dme_certificate_medical_necessity(p_service_line_id integer)
RETURNS jsonb AS $$
DECLARE
    v_dme_cmn jsonb;
BEGIN
    -- Build DME certificate of medical necessity
    SELECT jsonb_build_object(
        'attachmentTransmissionCode', cmn.attachment_transmission_code
    ) INTO v_dme_cmn
    FROM sf_form_med_claim_sl_to_med_claim_dme_cmn sf_cmn
    INNER JOIN form_med_claim_dme_cmn cmn ON cmn.id = sf_cmn.form_med_claim_dme_cmn_fk AND cmn.deleted IS NOT TRUE AND cmn.archived IS NOT TRUE
    WHERE sf_cmn.form_med_claim_sl_fk = p_service_line_id
    AND sf_cmn.delete IS NOT TRUE
    AND sf_cmn.archive IS NOT TRUE
    LIMIT 1;
    
    RETURN mm_remove_nulls(v_dme_cmn);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_dme_certificate_medical_necessity',
            'FUNCTION',
            current_schema(),
            'form_med_claim_dme_cmn',
            jsonb_build_object(
                'function_name', 'mm_build_dme_certificate_medical_necessity',
                'service_line_id', p_service_line_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build service line reference information
CREATE OR REPLACE FUNCTION mm_build_service_line_reference_info(p_service_line_id integer)
RETURNS jsonb AS $$
DECLARE
    v_ref_info jsonb;
    v_ref_id integer;
    v_referral_numbers jsonb;
    v_prior_auth_array jsonb;
BEGIN
    -- Get the reference info id
    SELECT ref.id INTO v_ref_id
    FROM form_med_claim_sl sl
    INNER JOIN sf_form_med_claim_sl_to_med_claim_sl_ref sf_ref ON sf_ref.form_med_claim_sl_fk = sl.id AND sf_ref.delete IS NOT TRUE AND sf_ref.archive IS NOT TRUE
    INNER JOIN form_med_claim_sl_ref ref ON ref.id = sf_ref.form_med_claim_sl_ref_fk AND ref.deleted IS NOT TRUE AND ref.archived IS NOT TRUE
    WHERE sl.id = p_service_line_id
    AND sl.deleted IS NOT TRUE
    AND sl.archived IS NOT TRUE
    LIMIT 1;
    
    IF v_ref_id IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Build referral numbers array
    SELECT COALESCE(jsonb_agg(rn.value ORDER BY rn.id), '[]'::jsonb) INTO v_referral_numbers
    FROM form_med_claim_sl_ref ref
    INNER JOIN sf_form_med_claim_sl_ref_to_med_claim_sl_ref_rn sf_rn ON sf_rn.form_med_claim_sl_ref_fk = ref.id AND sf_rn.delete IS NOT TRUE AND sf_rn.archive IS NOT TRUE
    INNER JOIN form_med_claim_sl_ref_rn rn ON rn.id = sf_rn.form_med_claim_sl_ref_rn_fk AND rn.deleted IS NOT TRUE AND rn.archived IS NOT TRUE
    WHERE ref.id = v_ref_id
    AND ref.deleted IS NOT TRUE
    AND ref.archived IS NOT TRUE;
    
    -- Build prior authorization array
    SELECT COALESCE(
        jsonb_agg(
            jsonb_build_object(
                'priorAuthorizationOrReferralNumber', pa.prior_authorization_or_referral_number
            ) ORDER BY pa.id
        ),
        '[]'::jsonb
    ) INTO v_prior_auth_array
    FROM form_med_claim_sl_ref ref
    INNER JOIN sf_form_med_claim_sl_ref_to_med_claim_sl_ref_pa sf_pa ON sf_pa.form_med_claim_sl_ref_fk = ref.id AND sf_pa.delete IS NOT TRUE AND sf_pa.archive IS NOT TRUE
    INNER JOIN form_med_claim_sl_ref_pa pa ON pa.id = sf_pa.form_med_claim_sl_ref_pa_fk AND pa.deleted IS NOT TRUE AND pa.archived IS NOT TRUE
    WHERE ref.id = v_ref_id
    AND ref.deleted IS NOT TRUE
    AND ref.archived IS NOT TRUE;

    -- Add arrays only if they have content
    IF jsonb_array_length(v_referral_numbers) > 0 THEN
        v_ref_info := v_ref_info || jsonb_build_object('referralNumber', v_referral_numbers);
    END IF;
    
    IF jsonb_array_length(v_prior_auth_array) > 0 THEN
        v_ref_info := v_ref_info || jsonb_build_object('priorAuthorization', v_prior_auth_array);
    END IF;
    
    RETURN mm_remove_nulls(v_ref_info);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_service_line_reference_info',
            'FUNCTION',
            current_schema(),
            'form_med_claim_sl_ref',
            jsonb_build_object(
                'function_name', 'mm_build_service_line_reference_info',
                'service_line_id', p_service_line_id
            )
        );
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to build a single service line
CREATE OR REPLACE FUNCTION mm_build_service_line(
    p_service_line_id integer,
    p_med_claim_info_id integer,
    p_med_claim_id integer
)
RETURNS jsonb AS $$
DECLARE
    v_service_line jsonb;
    v_professional_service jsonb;
    v_dme_service jsonb;
    v_drug_id jsonb;
    v_sl_dates jsonb;
    v_dme_cert jsonb;
    v_dme_condition jsonb;
    v_diagnosis_pointers jsonb;
    v_report_info jsonb;
    v_line_adjudication jsonb;
    v_form_identification jsonb;
    v_sl_supplemental jsonb;
    v_file_information jsonb;
    v_dme_cmn jsonb;
    v_sl_ref_info jsonb;
    v_has_sv boolean;
    v_has_dme boolean;
    -- Provider variables for service line
    v_sl_rendering jsonb;
    v_sl_referring jsonb;
    v_sl_supervising jsonb;
    v_sl_ordering jsonb;
BEGIN
    -- Check if we have professional service or DME service
    SELECT EXISTS(
        SELECT 1 
        FROM form_med_claim_sl sl
        INNER JOIN sf_form_med_claim_sl_to_med_claim_sv sf_sv ON sf_sv.form_med_claim_sl_fk = sl.id
        INNER JOIN form_med_claim_sv sv ON sv.id = sf_sv.form_med_claim_sv_fk AND sv.deleted IS NOT TRUE AND sv.archived IS NOT TRUE
        WHERE sl.id = p_service_line_id AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE
    )
    INTO v_has_sv;
    
    SELECT EXISTS(
        SELECT 1 
        FROM form_med_claim_sl sl
        INNER JOIN sf_form_med_claim_sl_to_med_claim_dme sf_dme ON sf_dme.form_med_claim_sl_fk = sl.id AND sf_dme.delete IS NOT TRUE AND sf_dme.archive IS NOT TRUE
        INNER JOIN form_med_claim_dme dme ON dme.id = sf_dme.form_med_claim_dme_fk AND dme.deleted IS NOT TRUE AND dme.archived IS NOT TRUE
        WHERE sl.id = p_service_line_id AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE
    )
    INTO v_has_dme;
    
    -- Build professional service if exists
    IF v_has_sv THEN
        v_professional_service := mm_build_professional_service(p_service_line_id);
        -- Get diagnosis pointers
        v_diagnosis_pointers := mm_get_diagnosis_pointers(p_service_line_id, p_med_claim_info_id);
        -- Update professional service with diagnosis pointers using correct nested structure
        v_professional_service := jsonb_set(
            v_professional_service, 
            '{compositeDiagnosisCodePointers,diagnosisCodePointers}', 
            v_diagnosis_pointers
        );
    END IF;
    
    -- Build DME service if exists
    IF v_has_dme THEN
        v_dme_service := mm_build_dme_service(p_service_line_id);
        v_dme_cert := mm_build_dme_certification(p_service_line_id);
        v_dme_condition := mm_build_condition_indicator_dme(p_service_line_id);
        v_dme_cmn := mm_build_dme_certificate_medical_necessity(p_service_line_id);
    END IF;
    
    -- Build drug identification
    v_drug_id := mm_build_drug_identification(p_service_line_id);
    
    -- Build service line reference information
    v_sl_ref_info := mm_build_service_line_reference_info(p_service_line_id);
    
    -- Build service line dates
    v_sl_dates := mm_build_service_line_dates(p_service_line_id);
    
    -- Build service line supplemental information (aggregate multiple reports)
    SELECT CASE 
        WHEN COUNT(r.id) > 0 THEN
            jsonb_agg(
                jsonb_build_object(
                    'attachmentReportTypeCode', r.attachment_report_type_code,
                    'attachmentTransmissionCode', r.attachment_transmission_code,
                    'attachmentControlNumber', r.attachment_control_number
                ) ORDER BY r.id
            ) FILTER (WHERE r.attachment_transmission_code IS NOT NULL AND r.attachment_transmission_code != '')
        ELSE NULL
    END INTO v_report_info
    FROM form_med_claim_sl sl
    INNER JOIN sf_form_med_claim_sl_to_med_claim mc_sf ON mc_sf.form_med_claim_sl_fk = sl.id
    INNER JOIN form_med_claim mc ON mc.id = mc_sf.form_med_claim_fk AND mc.deleted IS NOT TRUE AND mc.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_to_med_claim_report sf_r ON sf_r.form_med_claim_fk = mc.id AND sf_r.delete IS NOT TRUE AND sf_r.archive IS NOT TRUE
    INNER JOIN form_med_claim_report r ON r.id = sf_r.form_med_claim_report_fk AND r.deleted IS NOT TRUE AND r.archived IS NOT TRUE
    WHERE sl.id = p_service_line_id
    AND sl.deleted IS NOT TRUE
    AND sl.archived IS NOT TRUE;
    
    -- Build line adjudication information (aggregate multiple adjudications)
    SELECT jsonb_agg(
        jsonb_build_object(
            'otherPayerPrimaryIdentifier', adj.other_payer_primary_identifier,
            'serviceLinePaidAmount', adj.service_line_paid_amount::text,
            'serviceIdQualifier', adj.service_id_qualifier,
            'procedureCode', adj.procedure_code,
            'procedureCodeDescription', adj.procedure_code_description,
            'paidServiceUnitCount', adj.paid_service_unit_count::text,
            'procedureModifier', COALESCE(
                (SELECT jsonb_agg(modifier ORDER BY modifier_order) 
                 FROM (
                     SELECT adj.modifier_1 as modifier, 1 as modifier_order WHERE adj.modifier_1 IS NOT NULL
                     UNION ALL
                     SELECT adj.modifier_2 as modifier, 2 as modifier_order WHERE adj.modifier_2 IS NOT NULL
                     UNION ALL
                     SELECT adj.modifier_3 as modifier, 3 as modifier_order WHERE adj.modifier_3 IS NOT NULL
                     UNION ALL
                     SELECT adj.modifier_4 as modifier, 4 as modifier_order WHERE adj.modifier_4 IS NOT NULL
                 ) modifiers
                ),
                '[]'::jsonb
            ),
            'adjudicationOrPaymentDate', mm_format_date(adj.adjudication_or_payment_date),
            'remainingPatientLiability', CASE 
                WHEN adj.remaining_patient_liability IS NOT NULL 
                THEN adj.remaining_patient_liability::text 
                ELSE NULL 
            END,
            'claimAdjustmentInformation', COALESCE(
                (SELECT jsonb_agg(
                    jsonb_build_object(
                        'adjustmentGroupCode', cas.adjustment_group_code,
                        'adjustmentDetails', COALESCE(
                            (SELECT jsonb_agg(
                                jsonb_build_object(
                                    'adjustmentReasonCode', dl.adjustment_reason_code,
                                    'adjustmentAmount', dl.adjustment_amount::text,
                                    'adjustmentQuantity', CASE 
                                        WHEN dl.adjustment_quantity IS NOT NULL 
                                        THEN dl.adjustment_quantity::text 
                                        ELSE NULL 
                                    END
                                ) ORDER BY dl.id
                            )
                            FROM sf_form_med_claim_adj_sl_to_med_claim_adj_sl_dl sf_dl
                            INNER JOIN form_med_claim_adj_sl_dl dl ON dl.id = sf_dl.form_med_claim_adj_sl_dl_fk AND dl.deleted IS NOT TRUE AND dl.archived IS NOT TRUE
                            WHERE sf_dl.form_med_claim_adj_sl_fk = cas.id
                            AND sf_dl.delete IS NOT TRUE
                            AND sf_dl.archive IS NOT TRUE),
                            '[]'::jsonb
                        )
                    ) ORDER BY cas.id
                )
                FROM sf_form_med_claim_sl_adj_to_med_claim_adj_sl sf_cas
                INNER JOIN form_med_claim_adj_sl cas ON cas.id = sf_cas.form_med_claim_adj_sl_fk AND cas.deleted IS NOT TRUE AND cas.archived IS NOT TRUE
                WHERE sf_cas.form_med_claim_sl_adj_fk = adj.id
                AND sf_cas.delete IS NOT TRUE
                AND sf_cas.archive IS NOT TRUE),
                '[]'::jsonb
            )
        ) ORDER BY adj.id
    ) INTO v_line_adjudication
    FROM sf_form_med_claim_sl_to_med_claim_sl_adj sf_adj
    INNER JOIN form_med_claim_sl_adj adj ON adj.id = sf_adj.form_med_claim_sl_adj_fk AND adj.deleted IS NOT TRUE AND adj.archived IS NOT TRUE
    WHERE sf_adj.form_med_claim_sl_fk = p_service_line_id
    AND sf_adj.delete IS NOT TRUE
    AND sf_adj.archive IS NOT TRUE;
    
    -- Build additional service line components
    v_form_identification := mm_build_service_line_form_identification(p_service_line_id);
    v_sl_supplemental := mm_build_service_line_supplemental(p_service_line_id);
    v_file_information := mm_build_service_line_file_information(p_service_line_id);
    
    -- Get claim-level providers and copy to service line level
    v_sl_rendering := mm_build_rendering_provider(p_med_claim_id);
    v_sl_referring := mm_build_referring_provider(p_med_claim_id);
    v_sl_supervising := mm_build_supervising_provider(p_med_claim_id);
    v_sl_ordering := mm_build_ordering_provider(p_med_claim_id);
    
    -- Build main service line using JSONB merge to avoid double-escaping
    SELECT jsonb_build_object(
        'assignedNumber', sl.assigned_number::text,
        'serviceDate', mm_format_date(sl.service_date::date),
        'serviceDateEnd', CASE 
            WHEN sl.service_date_end IS NOT NULL THEN mm_format_date(sl.service_date_end::date)
            ELSE NULL
        END,
        'providerControlNumber', sl.provider_control_number,
        'additionalNotes', sl.additional_notes,
        'goalRehabOrDischargePlans', sl.goal_rehab_or_discharge_plans,
        'thirdPartyOrganizationNotes', sl.third_party_organization_notes
    ) INTO v_service_line
    FROM form_med_claim_sl sl
    WHERE sl.id = p_service_line_id AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE;
    
    -- Merge JSONB objects to avoid double-escaping
    IF v_professional_service IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('professionalService', v_professional_service);
    END IF;
    
    IF v_dme_service IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('durableMedicalEquipmentService', v_dme_service);
    END IF;
    
    IF v_report_info IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('serviceLineSupplementalInformation', v_report_info);
    END IF;
    
    IF v_dme_cert IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('durableMedicalEquipmentCertification', v_dme_cert);
    END IF;
    
    IF v_dme_condition IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('conditionIndicatorDurableMedicalEquipment', v_dme_condition);
    END IF;
    
    IF v_sl_dates IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('serviceLineDateInformation', v_sl_dates);
    END IF;
    
    IF v_drug_id IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('drugIdentification', v_drug_id);
    END IF;
    
    IF v_sl_ref_info IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('serviceLineReferenceInformation', v_sl_ref_info);
    END IF;
    
    IF v_line_adjudication IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('lineAdjudicationInformation', v_line_adjudication);
    END IF;
    
    IF v_form_identification IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('formIdentification', v_form_identification);
    END IF;
    
    IF v_sl_supplemental IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('claimSupplementalInformation', v_sl_supplemental);
    END IF;
    
    IF v_file_information IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('fileInformation', v_file_information);
    END IF;
    
    IF v_dme_cmn IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('durableMedicalEquipmentCertificateOfMedicalNecessity', v_dme_cmn);
    END IF;
    
    -- Add claim-level providers to service line
    IF v_sl_rendering IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('renderingProvider', v_sl_rendering);
    END IF;
    
    IF v_sl_referring IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('referringProvider', v_sl_referring);
    END IF;
    
    IF v_sl_supervising IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('supervisingProvider', v_sl_supervising);
    END IF;
    
    IF v_sl_ordering IS NOT NULL THEN
        v_service_line := v_service_line || jsonb_build_object('orderingProvider', v_sl_ordering);
    END IF;
    
    RETURN mm_remove_nulls(v_service_line);
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_service_line',
            'FUNCTION',
            current_schema(),
            'form_med_claim_sl',
            jsonb_build_object(
                'function_name', 'mm_build_service_line',
                'service_line_id', p_service_line_id,
                'med_claim_info_id', p_med_claim_info_id,
                'med_claim_id', p_med_claim_id
            )
        );
        RAISE;
END;
$$ LANGUAGE plpgsql;

-- Function to build all service lines
CREATE OR REPLACE FUNCTION mm_build_service_lines(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_service_lines jsonb = '[]'::jsonb;
    v_service_line jsonb;
    v_med_claim_info_id integer;
    r_sl record;
    v_line_number integer := 1;
BEGIN
    -- Get med_claim_info_id
    SELECT mci.id INTO v_med_claim_info_id
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_info sf ON sf.form_med_claim_fk = mc.id AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
    INNER JOIN form_med_claim_info mci ON mci.id = sf.form_med_claim_info_fk AND mci.deleted IS NOT TRUE AND mci.archived IS NOT TRUE
    WHERE mc.id = p_med_claim_id
    AND mc.deleted IS NOT TRUE
    AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Loop through all service lines
    FOR r_sl IN 
        SELECT sl.id
        FROM form_med_claim mc
        INNER JOIN sf_form_med_claim_to_med_claim_sl sf_sl ON sf_sl.form_med_claim_fk = mc.id AND sf_sl.delete IS NOT TRUE AND sf_sl.archive IS NOT TRUE
        INNER JOIN form_med_claim_sl sl ON sl.id = sf_sl.form_med_claim_sl_fk AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE
        WHERE mc.id = p_med_claim_id
        AND mc.deleted IS NOT TRUE
        AND mc.archived IS NOT TRUE
        ORDER BY sl.id
    LOOP
        v_service_line := mm_build_service_line(r_sl.id, v_med_claim_info_id, p_med_claim_id);
        v_service_lines := v_service_lines || v_service_line;
        v_line_number := v_line_number + 1;
    END LOOP;
    
    RETURN v_service_lines;
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_service_lines',
            'FUNCTION',
            current_schema(),
            'form_med_claim_sl',
            jsonb_build_object(
                'function_name', 'mm_build_service_lines',
                'med_claim_id', p_med_claim_id
            )
        );
        RETURN '[]'::jsonb;
END;
$$ LANGUAGE plpgsql;

-- Main function to build the complete claim JSON
CREATE OR REPLACE FUNCTION mm_build_claim_json(p_med_claim_id integer)
RETURNS jsonb AS $$
DECLARE
    v_result jsonb;
    v_claim_info jsonb;
    v_service_lines jsonb;
    v_dependent jsonb;
    v_pay_to_address jsonb;
    v_payer_address jsonb;
    v_rendering jsonb;
    v_referring jsonb;
    v_supervising jsonb;
    v_ordering jsonb;
BEGIN
    -- Build service lines first
    v_service_lines := mm_build_service_lines(p_med_claim_id);
    
    -- Build claim information and add service lines
    v_claim_info := mm_build_claim_information(p_med_claim_id);
    v_claim_info := jsonb_set(v_claim_info, '{serviceLines}', v_service_lines);
    
    -- Build the complete JSON step by step to avoid double escaping
    SELECT jsonb_build_object(
        'controlNumber', mc.control_number,
        'tradingPartnerServiceId', mc.trading_partner_service_id,
        'tradingPartnerName', mc.trading_partner_name,
        'usageIndicator', COALESCE(mc.usage_indicator, 'P')
    ) INTO v_result
    FROM form_med_claim mc
    WHERE mc.id = p_med_claim_id;
    
    -- Add each required component by merging JSONB objects
    v_result := v_result || jsonb_build_object('submitter', mm_build_submitter(p_med_claim_id));
    v_result := v_result || jsonb_build_object('receiver', mm_build_receiver(p_med_claim_id));
    v_result := v_result || jsonb_build_object('billing', mm_build_billing_provider(p_med_claim_id));
    v_result := v_result || jsonb_build_object('subscriber', mm_build_subscriber(p_med_claim_id));
    
    -- Add optional components only if they exist
    v_dependent := mm_build_dependent(p_med_claim_id);
    IF v_dependent IS NOT NULL THEN
        v_result := v_result || jsonb_build_object('dependent', v_dependent);
    END IF;
    
    
    v_pay_to_address := mm_build_pay_to_address(p_med_claim_id);
    IF v_pay_to_address IS NOT NULL THEN
        v_result := v_result || jsonb_build_object('payToAddress', v_pay_to_address);
    END IF;
    
    v_payer_address := mm_build_payer_address(p_med_claim_id);
    IF v_payer_address IS NOT NULL THEN
        v_result := v_result || jsonb_build_object('payerAddress', v_payer_address);
    END IF;
    
    v_rendering := mm_build_rendering_provider(p_med_claim_id);
    IF v_rendering IS NOT NULL THEN
        v_result := v_result || jsonb_build_object('rendering', v_rendering);
    END IF;
    
    v_referring := mm_build_referring_provider(p_med_claim_id);
    IF v_referring IS NOT NULL THEN
        v_result := v_result || jsonb_build_object('referring', v_referring);
    END IF;
    
    v_supervising := mm_build_supervising_provider(p_med_claim_id);
    IF v_supervising IS NOT NULL THEN
        v_result := v_result || jsonb_build_object('supervising', v_supervising);
    END IF;
    
    v_ordering := mm_build_ordering_provider(p_med_claim_id);
    IF v_ordering IS NOT NULL THEN
        v_result := v_result || jsonb_build_object('ordering', v_ordering);
    END IF;
    
    -- Add claim information
    v_result := v_result || jsonb_build_object('claimInformation', v_claim_info);
    
    -- Remove any remaining nulls recursively
    v_result := mm_remove_nulls(v_result);
    
    -- Log successful build
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        'Successfully built claim JSON',
        'Success in mm_build_claim_json',
        'SUCCESS',
        current_schema(),
        'form_med_claim',
        jsonb_build_object(
            'function_name', 'mm_build_claim_json',
            'med_claim_id', p_med_claim_id,
            'json_size', length(v_result::text)
        )
    );
    
    RETURN v_result;
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in mm_build_claim_json',
            'FUNCTION',
            current_schema(),
            'form_med_claim',
            jsonb_build_object(
                'function_name', 'mm_build_claim_json',
                'med_claim_id', p_med_claim_id,
                'error_detail', SQLERRM,
                'error_hint', SQLSTATE
            )
        );
        RAISE;
END;
$$ LANGUAGE plpgsql;
