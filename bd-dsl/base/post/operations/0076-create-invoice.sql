CREATE OR REPLACE FUNCTION create_ready_to_bill_invoice(
    p_patient_id integer,
    p_site_id integer,
    p_insurance_id integer,
    p_existing_charge_lines integer[],
    p_user_id integer
) RETURNS json AS $$
DECLARE
    v_patient_insurance record;
    v_payer_id integer;
    v_billing_method_id text;
    v_error_text text;
    v_start_time timestamptz;
    v_existing_charge record;
    v_parent_invoice_no text;
    v_previous_payer_id integer;
    v_charge_lines charge_line_with_split[] := '{}'::charge_line_with_split[];
    v_date_of_service_start date;
    v_date_of_service_end date;
    v_new_invoice insurance_invoice;
    v_results json;
BEGIN
    -- Record start time for performance tracking
    v_start_time := clock_timestamp();
    
    -- Get patient insurance info
    SELECT pi.*, py.id AS payer_id, py.billing_method_id
    INTO v_patient_insurance
    FROM form_patient_insurance pi
    INNER JOIN form_payer py ON py.id = pi.payer_id
        AND py.archived IS NOT TRUE
        AND py.deleted IS NOT TRUE
    WHERE pi.id = p_insurance_id
        AND pi.archived IS NOT TRUE
        AND pi.deleted IS NOT TRUE
        AND pi.patient_id = p_patient_id;

    -- Validate patient insurance exists
    IF v_patient_insurance.id IS NULL THEN
        RAISE EXCEPTION 'Invalid patient insurance ID: %', p_insurance_id;
    END IF;

    -- Store billing method and payer ID for later use
    v_billing_method_id := v_patient_insurance.billing_method_id;
    v_payer_id := v_patient_insurance.payer_id;

    -- Process existing charge lines
    IF p_existing_charge_lines IS NULL OR array_length(p_existing_charge_lines, 1) = 0 THEN
        RAISE EXCEPTION 'No charge lines provided';
    ELSE
        -- Get parent invoice and previous payer info from the first charge line
        SELECT 
            plcl.invoice_no::text AS parent_invoice_no,
            plcl.payer_id::integer AS previous_payer_id,
            COALESCE(dt.service_from::date,lcl.date_of_service::date) AS date_of_service_start,
            COALESCE(dt.service_to::date,lcl.date_of_service_end::date) AS date_of_service_end
        INTO 
            v_parent_invoice_no,
            v_previous_payer_id,
            v_date_of_service_start,
            v_date_of_service_end
        FROM form_ledger_charge_line lcl
        LEFT JOIN form_careplan_delivery_tick dt ON dt.ticket_no = lcl.ticket_no
        LEFT JOIN form_ledger_charge_line plcl ON lcl.parent_charge_no = plcl.charge_no
        WHERE lcl.id = p_existing_charge_lines[1]
            AND lcl.archived IS NOT TRUE
            AND lcl.deleted IS NOT TRUE
            AND COALESCE(lcl.void, 'No') <> 'Yes'
            AND COALESCE(lcl.zeroed, 'No') <> 'Yes';

        -- Use the service date from the parent invoice or default to current date
        v_date_of_service_start := COALESCE(v_date_of_service_start, CURRENT_DATE);
        v_date_of_service_end := COALESCE(v_date_of_service_end, NULL);

        WITH existing_charge_lines AS (
          SELECT lcl.*
          FROM form_ledger_charge_line lcl
          WHERE lcl.id = ANY(p_existing_charge_lines)
          AND lcl.archived IS NOT TRUE
          AND lcl.deleted IS NOT TRUE
          AND COALESCE(lcl.void, 'No') <> 'Yes'
          AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
        ),
        same_payer_lines AS (
          SELECT
            lcl.id::integer as id,
            lcl.calc_invoice_split_no::text as calc_invoice_split_no,
            lcl.site_id::integer as site_id,
            lcl.patient_id::integer as patient_id,
            lcl.insurance_id::integer as insurance_id,
            lcl.payer_id::integer as payer_id,
            lcl.inventory_id::integer as inventory_id,
            lcl.shared_contract_id::integer as shared_contract_id,
            lcl.is_dirty::text as is_dirty,
            lcl.charge_no::text as charge_no,
            lcl.parent_charge_no::text as parent_charge_no,
            lcl.master_charge_no::text as master_charge_no,
            lcl.compound_no::text as compound_no,
            lcl.ticket_no::text as ticket_no,
            lcl.ticket_item_no::text as ticket_item_no,
            lcl.rental_id::integer as rental_id,
            lcl.order_rx_id::integer as order_rx_id,
            lcl.is_primary_drug::text as is_primary_drug,
            lcl.is_primary_drug_ncpdp::text as is_primary_drug_ncpdp,
            lcl.rx_no::text as rx_no,
            lcl.inventory_type_filter::text[] as inventory_type_filter,
            lcl.inventory_type::text as inventory_type,
            lcl.revenue_code_id::text as revenue_code_id,
            lcl.hcpc_code::text as hcpc_code,
            lcl.ndc::text as ndc,
            lcl.formatted_ndc::text as formatted_ndc,
            lcl.gcn_seqno::text as gcn_seqno,
            lcl.charge_quantity::numeric as charge_quantity,
            lcl.charge_quantity_ea::numeric as charge_quantity_ea,
            lcl.hcpc_quantity::numeric as hcpc_quantity,
            lcl.hcpc_unit::text as hcpc_unit,
            lcl.metric_quantity::numeric as metric_quantity,
            lcl.charge_unit::text as charge_unit,
            lcl.bill_quantity::numeric as bill_quantity,
            lcl.metric_unit_each::numeric as metric_unit_each,
            lcl.billing_unit_id::text as billing_unit_id,
            lcl.billing_method_id::text as billing_method_id,
            lcl.pricing_source::text as pricing_source,
            lcl.billed::numeric as billed,
            lcl.calc_billed_ea::numeric as calc_billed_ea,
            lcl.expected::numeric as expected,
            lcl.calc_expected_ea::numeric as calc_expected_ea,
            lcl.list_price::numeric as list_price,
            lcl.calc_list_ea::numeric as calc_list_ea,
            lcl.total_cost::numeric as total_cost,
            lcl.gross_amount_due::numeric as gross_amount_due,
            lcl.incv_amt_sub::numeric as incv_amt_sub,
            lcl.copay::numeric as copay,
            lcl.calc_cost_ea::numeric as calc_cost_ea,
            lcl.total_adjusted::numeric as total_adjusted,
            lcl.total_balance_due::numeric as total_balance_due,
            lcl.dispense_fee::numeric as dispense_fee,
            lcl.pt_pd_amt_sub::numeric as pt_pd_amt_sub,
            lcl.encounter_id::integer as encounter_id,
            lcl.description::text as description,
            lcl.upc::text as upc,
            lcl.upin::text as upin,
            lcl.cost_basis::text as cost_basis,
            lcl.awp_price::numeric as awp_price,
            lcl.modifier_1::text as modifier_1,
            lcl.modifier_2::text as modifier_2,
            lcl.modifier_3::text as modifier_3,
            lcl.modifier_4::text as modifier_4,
            lcl.rental_type::text as rental_type,
            lcl.frequency_code::text as frequency_code,
            lcl.fill_number::integer as fill_number,
            lcl.paid::numeric as paid,
            lcl.date_of_service::date as date_of_service,
            lcl.date_of_service_end::date as date_of_service_end
          FROM existing_charge_lines lcl
          WHERE lcl.payer_id = v_payer_id
        ),
        lines_to_convert AS (
          SELECT lcl.*
          FROM existing_charge_lines lcl
          WHERE lcl.id NOT IN (SELECT id FROM same_payer_lines)
        ),
        converted_cob_lines AS (
          SELECT
            lcl.id::integer as id,
            lcl.calc_invoice_split_no::text as calc_invoice_split_no,
            lcl.site_id::integer as site_id,
            lcl.patient_id::integer as patient_id,
            lcl.insurance_id::integer as insurance_id,
            lcl.payer_id::integer as payer_id,
            lcl.inventory_id::integer as inventory_id,
            lcl.shared_contract_id::integer as shared_contract_id,
            lcl2.is_dirty::text as is_dirty,
            lcl.charge_no::text as charge_no,
            lcl.parent_charge_no::text as parent_charge_no,
            lcl.master_charge_no::text as master_charge_no,
            lcl.compound_no::text as compound_no,
            lcl.ticket_no::text as ticket_no,
            lcl.ticket_item_no::text as ticket_item_no,
            lcl.rental_id::integer as rental_id,
            lcl.order_rx_id::integer as order_rx_id,
            lcl.is_primary_drug::text as is_primary_drug,
            lcl.is_primary_drug_ncpdp::text as is_primary_drug_ncpdp,
            lcl.rx_no::text as rx_no,
            lcl.inventory_type_filter::text[] as inventory_type_filter,
            lcl.inventory_type::text as inventory_type,
            lcl.revenue_code_id::text as revenue_code_id,
            lcl2.hcpc_code::text as hcpc_code,
            lcl.ndc::text as ndc,
            lcl.formatted_ndc::text as formatted_ndc,
            lcl.gcn_seqno::text as gcn_seqno,
            lcl2.charge_quantity::numeric as charge_quantity,
            lcl2.charge_quantity_ea::numeric as charge_quantity_ea,
            lcl2.hcpc_quantity::numeric as hcpc_quantity,
            lcl2.hcpc_unit::text as hcpc_unit,
            lcl.metric_quantity::numeric as metric_quantity,
            lcl2.charge_unit::text as charge_unit,
            lcl.bill_quantity::numeric as bill_quantity,
            lcl.metric_unit_each::numeric as metric_unit_each,
            lcl2.billing_unit_id::text as billing_unit_id,
            lcl2.billing_method_id::text as billing_method_id,
            lcl2.pricing_source::text as pricing_source,
            lcl2.billed::numeric as billed,
            lcl2.calc_billed_ea::numeric as calc_billed_ea,
            lcl2.expected::numeric as expected,
            lcl2.calc_expected_ea::numeric as calc_expected_ea,
            lcl2.list_price::numeric as list_price,
            lcl2.calc_list_ea::numeric as calc_list_ea,
            lcl2.total_cost::numeric as total_cost,
            lcl2.gross_amount_due::numeric as gross_amount_due,
            lcl.incv_amt_sub::numeric as incv_amt_sub,
            lcl.copay::numeric as copay,
            lcl2.calc_cost_ea::numeric as calc_cost_ea,
            lcl2.total_adjusted::numeric as total_adjusted,
            lcl2.total_balance_due::numeric as total_balance_due,
            lcl2.dispense_fee::numeric as dispense_fee,
            lcl2.pt_pd_amt_sub::numeric as pt_pd_amt_sub,
            lcl.encounter_id::integer as encounter_id,
            lcl.description::text as description,
            lcl.upc::text as upc,
            lcl.upin::text as upin,
            lcl2.cost_basis::text as cost_basis,
            lcl.awp_price::numeric as awp_price,
            lcl2.modifier_1::text as modifier_1,
            lcl2.modifier_2::text as modifier_2,
            lcl2.modifier_3::text as modifier_3,
            lcl2.modifier_4::text as modifier_4,
            lcl.rental_type::text as rental_type,
            lcl.frequency_code::text as frequency_code,
            lcl.fill_number::integer as fill_number,
            0.00::numeric as paid,
            lcl.date_of_service::date as date_of_service,
            lcl.date_of_service_end::date as date_of_service_end
          FROM lines_to_convert lcl
          CROSS JOIN create_cob_ledger_charge_line(
            p_insurance_id,
            lcl.parent_charge_no
          ) lcl2
          WHERE lcl.parent_charge_no IS NOT NULL
        ),
        convert_noncob_lines AS (
          SELECT
            lcl.id::integer as id,
            lcl.calc_invoice_split_no::text as calc_invoice_split_no,
            lcl.site_id::integer as site_id,
            lcl.patient_id::integer as patient_id,
            lcl.insurance_id::integer as insurance_id,
            lcl.payer_id::integer as payer_id,
            lcl.inventory_id::integer as inventory_id,
            lcl.shared_contract_id::integer as shared_contract_id,
            lcl2.is_dirty::text as is_dirty,
            lcl.charge_no::text as charge_no,
            lcl.parent_charge_no::text as parent_charge_no,
            lcl.master_charge_no::text as master_charge_no,
            lcl.compound_no::text as compound_no,
            lcl.ticket_no::text as ticket_no,
            lcl.ticket_item_no::text as ticket_item_no,
            lcl.rental_id::integer as rental_id,
            lcl.order_rx_id::integer as order_rx_id,
            lcl.is_primary_drug::text as is_primary_drug,
            lcl.is_primary_drug_ncpdp::text as is_primary_drug_ncpdp,
            lcl.rx_no::text as rx_no,
            lcl.inventory_type_filter::text[] as inventory_type_filter,
            lcl.inventory_type::text as inventory_type,
            lcl.revenue_code_id::text as revenue_code_id,
            lcl2.hcpc_code::text as hcpc_code,
            lcl.ndc::text as ndc,
            lcl.formatted_ndc::text as formatted_ndc,
            lcl.gcn_seqno::text as gcn_seqno,
            lcl2.charge_quantity::numeric as charge_quantity,
            lcl2.charge_quantity_ea::numeric as charge_quantity_ea,
            lcl2.hcpc_quantity::numeric as hcpc_quantity,
            lcl2.hcpc_unit::text as hcpc_unit,
            lcl.metric_quantity::numeric as metric_quantity,
            lcl2.charge_unit::text as charge_unit,
            lcl.bill_quantity::numeric as bill_quantity,
            lcl.metric_unit_each::numeric as metric_unit_each,
            lcl2.billing_unit_id::text as billing_unit_id,
            lcl2.billing_method_id::text as billing_method_id,
            lcl2.pricing_source::text as pricing_source,
            lcl2.billed::numeric as billed,
            lcl2.calc_billed_ea::numeric as calc_billed_ea,
            lcl2.expected::numeric as expected,
            lcl2.calc_expected_ea::numeric as calc_expected_ea,
            lcl2.list_price::numeric as list_price,
            lcl2.calc_list_ea::numeric as calc_list_ea,
            lcl2.total_cost::numeric as total_cost,
            lcl2.gross_amount_due::numeric as gross_amount_due,
            lcl.incv_amt_sub::numeric as incv_amt_sub,
            lcl.copay::numeric as copay,
            lcl2.calc_cost_ea::numeric as calc_cost_ea,
            lcl2.total_adjusted::numeric as total_adjusted,
            lcl2.total_balance_due::numeric as total_balance_due,
            lcl2.dispense_fee::numeric as dispense_fee,
            lcl2.pt_pd_amt_sub::numeric as pt_pd_amt_sub,
            lcl.encounter_id::integer as encounter_id,
            lcl.description::text as description,
            lcl.upc::text as upc,
            lcl.upin::text as upin,
            lcl2.cost_basis::text as cost_basis,
            lcl.awp_price::numeric as awp_price,
            lcl2.modifier_1::text as modifier_1,
            lcl2.modifier_2::text as modifier_2,
            lcl2.modifier_3::text as modifier_3,
            lcl2.modifier_4::text as modifier_4,
            lcl.rental_type::text as rental_type,
            lcl.frequency_code::text as frequency_code,
            lcl.fill_number::integer as fill_number,
            0.00::numeric as paid,
            lcl.date_of_service::date as date_of_service,
            lcl.date_of_service_end::date as date_of_service_end
          FROM lines_to_convert lcl
          CROSS JOIN create_ledger_charge_line(
            lcl.inventory_id::INTEGER,
            p_insurance_id::INTEGER,
            lcl.site_id::INTEGER,
            lcl.patient_id::INTEGER,
            lcl.bill_quantity::NUMERIC,
            lcl.is_primary_drug::BOOLEAN,
            lcl.compound_no::text,
            lcl.order_rx_id::INTEGER,
            lcl.rental_id::INTEGER,
            lcl.ticket_no::text,
            lcl.ticket_item_no::text,
            lcl.encounter_id::INTEGER,
            FALSE,
            NULL::text
          ) lcl2 
          WHERE lcl.parent_charge_no IS NULL
        ),
        combined_charge_lines AS (
          SELECT * FROM converted_cob_lines
          UNION ALL
          SELECT * FROM convert_noncob_lines
          UNION ALL
          SELECT * FROM same_payer_lines
        )
        SELECT array_agg((lcl.id::integer,
            lcl.calc_invoice_split_no,
            lcl.site_id,
            lcl.patient_id,
            lcl.insurance_id,
            lcl.payer_id,
            lcl.inventory_id,
            lcl.shared_contract_id,
            lcl.is_dirty,
            lcl.charge_no,
            lcl.parent_charge_no,
            lcl.master_charge_no,
            lcl.compound_no,
            lcl.ticket_no,
            lcl.ticket_item_no,
            lcl.rental_id,
            lcl.order_rx_id,
            lcl.is_primary_drug,
            lcl.is_primary_drug_ncpdp,
            lcl.rx_no,
            lcl.inventory_type_filter,
            lcl.inventory_type,
            lcl.revenue_code_id,
            lcl.hcpc_code,
            lcl.ndc,
            lcl.formatted_ndc,
            lcl.gcn_seqno,
            lcl.charge_quantity,
            lcl.charge_quantity_ea,
            lcl.hcpc_quantity,
            lcl.hcpc_unit,
            lcl.metric_quantity,
            lcl.charge_unit,
            lcl.bill_quantity,
            lcl.metric_unit_each,
            lcl.billing_unit_id,
            lcl.billing_method_id,
            lcl.pricing_source,
            lcl.billed,
            lcl.calc_billed_ea,
            lcl.expected,
            lcl.calc_expected_ea,
            lcl.list_price,
            lcl.calc_list_ea,
            lcl.total_cost,
            lcl.gross_amount_due,
            lcl.incv_amt_sub,
            lcl.copay,
            lcl.calc_cost_ea,
            lcl.total_adjusted,
            lcl.total_balance_due,
            lcl.dispense_fee,
            lcl.pt_pd_amt_sub,
            lcl.encounter_id,
            lcl.description,
            lcl.upc,
            lcl.upin,
            lcl.cost_basis,
            lcl.awp_price,
            lcl.modifier_1,
            lcl.modifier_2,
            lcl.modifier_3,
            lcl.modifier_4,
            lcl.rental_type,
            lcl.frequency_code,
            lcl.fill_number,
            lcl.paid,
            lcl.date_of_service,
            lcl.date_of_service_end)::charge_line_with_split)::charge_line_with_split[] 
        FROM combined_charge_lines lcl
        INTO v_charge_lines;

        -- Create the invoice using create_insurance_invoice
        -- This function uses the charge lines to build the invoice
        v_results := create_insurance_invoice_json(
            p_insurance_id,
            v_payer_id,
            p_patient_id,
            p_site_id,
            v_charge_lines,
            v_date_of_service_start,
            v_date_of_service_end,
            v_previous_payer_id,
            v_parent_invoice_no
        );
    END IF;

    IF v_results IS NULL THEN
        RAISE EXCEPTION 'Failed to build ready to bill invoice';
    END IF;

    RETURN v_results;

	EXCEPTION WHEN OTHERS THEN
    -- Log error
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        SQLERRM,
        'Error in create_ready_to_bill_invoice',
        'FUNCTION',
        current_schema(),
        'billing_invoice',
        jsonb_build_object(
            'patient_id', p_patient_id,
            'site_id', p_site_id,
            'insurance_id', p_insurance_id,
            'existing_charge_lines', p_existing_charge_lines,
            'execution_time_ms', extract(milliseconds from clock_timestamp() - v_start_time)
        )
    );
    
    -- Return error information
    RAISE EXCEPTION 'Error in create_ready_to_bill_invoice: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION create_rx_fill_invoice(
  p_insurance_id integer,
  p_payer_id integer,
  p_patient_id integer,
  p_site_id integer,
  p_rx_id integer
) RETURNS json AS $BODY$
DECLARE
  v_charge_lines charge_line_with_split[];
  v_start_time timestamp;
  v_params jsonb;
  v_service_from date;
  v_service_to date;
  v_error_message text;
  v_results json;
BEGIN
  -- Record start time for performance tracking
  v_start_time := clock_timestamp();
  
  -- Build minimal parameters JSON for logging - exclude potentially large JSON objects
  v_params := jsonb_build_object(
    'insurance_id', p_insurance_id,
    'payer_id', p_payer_id,
    'patient_id', p_patient_id,
    'site_id', p_site_id,
    'rx_id', p_rx_id
  );
  
  IF p_rx_id IS NULL THEN
    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      'RX ID is required',
      'Validating parameters in create_rx_fill_invoice',
      'FUNCTION',
      current_schema(),
      'form_billing_invoice',
      v_params
    );
    RAISE EXCEPTION 'RX ID is required to build an invoice';
  END IF;
  
  -- Get service dates
  SELECT service_from, service_to
  FROM calc_delivery_ticket_service_dates(p_rx_id)
  INTO v_service_from, v_service_to;
  
  SELECT create_rx_chargeline_presets(p_rx_id, FALSE) INTO v_charge_lines;
  
  -- Create the invoice
  SELECT create_insurance_invoice_json(
    p_insurance_id,
    p_payer_id,
    p_patient_id,
    p_site_id,
    v_charge_lines,
    v_service_from,
    v_service_to,
    NULL::integer,
    NULL::text
  ) INTO v_results;
  
  IF v_results IS NULL THEN
    RAISE EXCEPTION 'Failed to build rx fill invoice';
  END IF;
  
  RAISE NOTICE 'Rx fill invoice built successfully';

  
  RETURN v_results;
  
EXCEPTION WHEN OTHERS THEN
  -- Get error message
  v_error_message := SQLERRM;
  
  -- Log error
  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    v_error_message,
    'Exception in create_rx_fill_invoice',
    'FUNCTION',
    current_schema(),
    'form_billing_invoice',
    v_params
  );
  
  -- Log to NCPDP function log if available
  BEGIN
    PERFORM log_billing_function(
      'create_rx_fill_invoice'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
  EXCEPTION WHEN OTHERS THEN
    -- If log_billing_function fails, we don't want to mask the original error
    NULL;
  END;
  
  -- Re-raise the original exception
  RAISE;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION create_cob_invoice(
  p_parent_invoice_no text,
  p_parent_insurance_id integer,
  p_parent_payer_id integer,
  p_insurance_id integer,
  p_payer_id integer,
  p_patient_id integer,
  p_site_id integer
) RETURNS json AS $BODY$
DECLARE
  v_charge_lines charge_line_with_split[];
  v_start_time timestamp;
  v_params jsonb;
  v_invoice insurance_invoice;
  v_service_from date;
  v_service_to date;
  v_error_message text;
  v_results json;
BEGIN
  -- Record start time for performance tracking
  v_start_time := clock_timestamp();
  
  -- Build minimal parameters JSON for logging - exclude potentially large JSON objects
  v_params := jsonb_build_object(
    'insurance_id', p_insurance_id,
    'payer_id', p_payer_id,
    'patient_id', p_patient_id,
    'site_id', p_site_id,
    'p_parent_invoice_no', p_parent_invoice_no,
    'p_parent_insurance_id', p_parent_insurance_id,
    'p_parent_payer_id', p_parent_payer_id
  );
  
  IF p_parent_invoice_no IS NULL
     OR p_parent_insurance_id IS NULL
     OR p_parent_payer_id IS NULL
     OR p_insurance_id IS NULL
     OR p_payer_id IS NULL
     OR p_patient_id IS NULL
     OR p_site_id IS NULL
  THEN
    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      'Parent invoice number, insurance ID, payer ID, patient ID, and site ID are required',
      'Validating parameters in create_cob_invoice',
      'FUNCTION',
      current_schema(),
      'form_billing_invoice',
      v_params
    );
    RAISE EXCEPTION 'Parent invoice number, insurance ID, payer ID, patient ID, and site ID are required';
  END IF;
  
  -- Get service dates
  SELECT date_of_service::date, date_of_service_end::date
  FROM form_billing_invoice
  WHERE invoice_no = p_parent_invoice_no
  INTO v_service_from, v_service_to;
  
  SELECT create_cob_chargeline_presets(p_parent_invoice_no, p_parent_insurance_id, p_insurance_id, TRUE) INTO v_charge_lines;
  
  -- Create the invoice
  SELECT create_insurance_invoice_json(
    p_insurance_id,
    p_payer_id,
    p_patient_id,
    p_site_id,
    v_charge_lines,
    v_service_from,
    v_service_to,
    p_parent_payer_id::integer,
    p_parent_invoice_no::text
  ) INTO v_results;
  
  IF v_results IS NULL THEN
    RAISE EXCEPTION 'Failed to build COB invoice';
  END IF;
  
  RAISE NOTICE 'COB invoice built successfully';

  RETURN v_results;
  
EXCEPTION WHEN OTHERS THEN
  -- Get error message
  v_error_message := SQLERRM;
  
  -- Log error
  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    v_error_message,
    'Exception in create_cob_invoice',
    'FUNCTION',
    current_schema(),
    'form_billing_invoice',
    v_params
  );
  
  -- Log to NCPDP function log if available
  BEGIN
    PERFORM log_billing_function(
      'create_cob_invoice'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
  EXCEPTION WHEN OTHERS THEN
    -- If log_billing_function fails, we don't want to mask the original error
    NULL;
  END;
  
  -- Re-raise the original exception
  RAISE;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;