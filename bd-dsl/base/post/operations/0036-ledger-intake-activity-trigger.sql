-- Helper function to get the localized datetime based on user timezone (reusing existing function)
-- First revoke all permissions and then grant only insert

-- CREATE OR REPLACE TRIGGER to prevent updates and deletes
DO $$ BEGIN
  PERFORM drop_all_function_signatures('get_user_localized_datetime');
END $$;
CREATE OR REPLACE FUNCTION get_user_localized_datetime(user_id integer)
RETURNS timestamp WITH TIME ZONE AS $$
DECLARE
    user_timezone text;
BEGIN
    -- Get user's timezone, default to 'America/Chicago' if not set
    SELECT COALESCE(timezone_id::text, 'America/Chicago') INTO user_timezone 
    FROM form_user 
    WHERE id = user_id;
    
    -- Return current timestamp in user's timezone
    RETURN (NOW() AT TIME ZONE user_timezone);
END;
$$ LANGUAGE plpgsql;

-- Main trigger function for careplan_order changes
DO $$ BEGIN
  PERFORM drop_all_function_signatures('log_careplan_order_changes');
END $$;
CREATE OR REPLACE FUNCTION log_careplan_order_changes()
RETURNS TRIGGER AS $$
DECLARE
    user_id integer;
    localized_ts timestamp with time zone;
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Determine the user ID from the context
    user_id := COALESCE(NEW.updated_by, NEW.created_by);
    
    -- Get localized timestamp
    localized_ts := get_user_localized_datetime(user_id);

    -- Handle nursing_status changes
    IF (TG_OP = 'INSERT' AND NEW.nursing_status IS NOT NULL) OR
       (TG_OP = 'UPDATE' AND COALESCE(OLD.nursing_status, '') != COALESCE(NEW.nursing_status, '')) THEN
        INSERT INTO form_ledger_intake_activity (
            localized_datetime, user_id, patient_id, site_id,
            form, form_id, field,
            old_value, new_value, description
        ) VALUES (
            localized_ts, user_id, NEW.patient_id, NEW.site_id,
            'careplan_order', NEW.id, 'nursing_status',
            COALESCE((SELECT name FROM form_list_order_nursing_status WHERE code = OLD.nursing_status), ''),
            COALESCE((SELECT name FROM form_list_order_nursing_status WHERE code = NEW.nursing_status), ''),
            'Nursing status changed'
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for patient_prior_auth status changes
DO $$ BEGIN
  PERFORM drop_all_function_signatures('log_prior_auth_changes');
END $$;
CREATE OR REPLACE FUNCTION log_prior_auth_changes()
RETURNS TRIGGER AS $$
DECLARE
    user_id integer;
    localized_ts timestamp with time zone;
    v_patient_rec RECORD;
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Determine the user ID from the context
    user_id := COALESCE(NEW.updated_by, NEW.created_by);
    
    -- Get localized timestamp
    localized_ts := get_user_localized_datetime(user_id);

    SELECT * INTO v_patient_rec 
    FROM form_patient 
    WHERE id = NEW.patient_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'patient record not found for prior auth record %', NEW.id;
    END IF;
    -- Handle status_id changes
    IF (TG_OP = 'INSERT' AND NEW.status_id IS NOT NULL) OR
       (TG_OP = 'UPDATE' AND COALESCE(OLD.status_id, '') != COALESCE(NEW.status_id, '')) THEN
        INSERT INTO form_ledger_intake_activity (
            localized_datetime, user_id, patient_id, site_id,
            form, form_id, field,
            old_value, new_value, description
        ) VALUES (
            localized_ts, user_id, NEW.patient_id, v_patient_rec.site_id,
            'patient_prior_auth', NEW.id, 'status_id',
            COALESCE((SELECT name FROM form_list_pa_status WHERE code = OLD.status_id), ''),
            COALESCE((SELECT name FROM form_list_pa_status WHERE code = NEW.status_id), ''),
            'Prior authorization status changed'
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for patient_assistance status changes
DO $$ BEGIN
  PERFORM drop_all_function_signatures('log_patient_assistance_changes');
END $$;
CREATE OR REPLACE FUNCTION log_patient_assistance_changes()
RETURNS TRIGGER AS $$
DECLARE
    user_id integer;
    localized_ts timestamp with time zone;
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Determine the user ID from the context
    user_id := COALESCE(NEW.updated_by, NEW.created_by);
    
    -- Get localized timestamp
    localized_ts := get_user_localized_datetime(user_id);

    -- Handle assistance_status changes
    IF (TG_OP = 'INSERT' AND NEW.assistance_status IS NOT NULL) OR
       (TG_OP = 'UPDATE' AND COALESCE(OLD.assistance_status, '') != COALESCE(NEW.assistance_status, '')) THEN
        INSERT INTO form_ledger_intake_activity (
            localized_datetime, user_id, patient_id, site_id,
            form, form_id, field,
            old_value, new_value, description
        ) VALUES (
            localized_ts, user_id, NEW.patient_id, NEW.site_id,
            'patient_assistance', NEW.id, 'assistance_status',
            COALESCE(OLD.assistance_status::text, ''),
            COALESCE(NEW.assistance_status::text, ''),
            'Patient assistance status changed'
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the triggers
CREATE OR REPLACE TRIGGER careplan_order_activity_trigger
    AFTER INSERT OR UPDATE ON form_careplan_order
    FOR EACH ROW
    EXECUTE FUNCTION log_careplan_order_changes();

CREATE OR REPLACE TRIGGER patient_prior_auth_activity_trigger
    AFTER INSERT OR UPDATE ON form_patient_prior_auth
    FOR EACH ROW
    EXECUTE FUNCTION log_prior_auth_changes();

CREATE OR REPLACE TRIGGER patient_assistance_activity_trigger
    AFTER INSERT OR UPDATE ON form_patient_assistance
    FOR EACH ROW
    EXECUTE FUNCTION log_patient_assistance_changes();

-- First revoke all permissions and then grant only insert (like billing activity)
REVOKE ALL ON form_ledger_intake_activity FROM PUBLIC;
GRANT INSERT ON form_ledger_intake_activity TO PUBLIC;

-- Prevent updates and deletes on the activity log
DO $$ BEGIN
  PERFORM drop_all_function_signatures('prevent_intake_ledger_modifications');
END $$;
CREATE OR REPLACE FUNCTION prevent_intake_ledger_modifications()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    RAISE EXCEPTION 'Modifications to intake activity log are not allowed';
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER prevent_intake_ledger_modifications_trigger
    BEFORE UPDATE OR DELETE ON form_ledger_intake_activity
    FOR EACH ROW
    EXECUTE FUNCTION prevent_intake_ledger_modifications();