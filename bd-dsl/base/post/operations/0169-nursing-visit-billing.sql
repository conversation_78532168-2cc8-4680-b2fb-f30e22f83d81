-- Helper function to generate charge line data for nursing visits (returns J<PERSON>N for Node.js processing)
CREATE OR REPLACE FUNCTION create_nursing_visit_charge_line_json(
    p_encounter_id integer
) RETURNS jsonb AS $$
DECLARE
    v_encounter record;
    v_delivery_ticket record;
    v_insurance_id integer := NULL;
    v_inventory_id integer := NULL;
    v_quantity numeric := 1.0;
    v_charge_line charge_line;
    v_calc_invoice_split_no text := NULL;
    v_result jsonb;
    v_error_text text := NULL;
BEGIN
    -- Get encounter details
    SELECT 
        enc.id,
        enc.patient_id,
        enc.delivery_ticket_id,
        enc.time_total,
        enc.visit_number,
        enc.approved,
        enc.cc_signature,
        enc.site_id
    INTO v_encounter
    FROM form_encounter enc
    WHERE enc.id = p_encounter_id
    AND enc.deleted IS NOT TRUE
    AND enc.archived IS NOT TRUE;

    IF v_encounter IS NULL THEN
        v_error_text := 'Encounter not found or is deleted/archived';
        RAISE EXCEPTION '%', v_error_text;
    END IF;

    -- Validate encounter is approved
    IF COALESCE(v_encounter.approved, 'No') <> 'Yes' OR v_encounter.cc_signature IS NULL THEN
        v_error_text := 'Encounter must be approved with cc_signature before generating charge line';
        RAISE EXCEPTION '%', v_error_text;
    END IF;

    -- Check if charge line already exists for this encounter
    IF EXISTS (
        SELECT 1 
        FROM form_ledger_charge_line lcl
        WHERE lcl.encounter_id = p_encounter_id
        AND lcl.deleted IS NOT TRUE
        AND lcl.archived IS NOT TRUE
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
    ) THEN
        v_error_text := 'Charge line already exists for encounter ID ' || p_encounter_id::text;
        RAISE EXCEPTION '%', v_error_text;
    END IF;

    -- Get delivery ticket information if available
    IF v_encounter.delivery_ticket_id IS NOT NULL THEN
        SELECT 
            dt.ticket_no,
            dt.patient_id,
            dt.site_id
        INTO v_delivery_ticket
        FROM form_careplan_delivery_tick dt
        WHERE dt.id = v_encounter.delivery_ticket_id
        AND dt.deleted IS NOT TRUE
        AND dt.archived IS NOT TRUE;
    END IF;

    -- Method 1: Try to find insurance from delivery ticket prescriptions
    IF v_delivery_ticket.ticket_no IS NOT NULL THEN
        SELECT DISTINCT 
            vw.nursing_insurance_id
        INTO v_insurance_id
        FROM form_careplan_dt_item dti
        INNER JOIN form_careplan_order_rx rx ON rx.id = dti.rx_id
            AND rx.deleted IS NOT TRUE
            AND rx.archived IS NOT TRUE
        INNER JOIN vw_order_raw vw ON vw.rx_no = rx.rx_no
        WHERE dti.ticket_no = v_delivery_ticket.ticket_no
        AND dti.deleted IS NOT TRUE
        AND dti.archived IS NOT TRUE
        AND vw.nursing_insurance_id IS NOT NULL
        LIMIT 1;
    END IF;

    -- Method 2: If no insurance found, try patient insurance for medical billing
    IF v_insurance_id IS NULL THEN
        SELECT 
            pi.id
        INTO v_insurance_id
        FROM form_patient_insurance pi
        INNER JOIN form_payer py ON py.id = pi.payer_id
            AND py.deleted IS NOT TRUE
            AND py.archived IS NOT TRUE
        WHERE pi.patient_id = v_encounter.patient_id
        AND pi.deleted IS NOT TRUE
        AND pi.archived IS NOT TRUE
        AND COALESCE(pi.active, 'No') = 'Yes'
        AND ((py.billing_method_id IN ('mm', 'cms1500') OR COALESCE(py.bill_mm_and_ncpdp, 'No') = 'Yes'))
        ORDER BY pi.rank DESC
        LIMIT 1;
    END IF;

    -- If still no insurance found, log error and exit
    IF v_insurance_id IS NULL THEN
        v_error_text := 'No valid insurance found for nursing visit billing';
        
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            v_error_text,
            'Error in create_nursing_visit_charge_line_json',
            'FUNCTION',
            current_schema(),
            'encounter',
            jsonb_build_object(
                'encounter_id', p_encounter_id,
                'patient_id', v_encounter.patient_id,
                'delivery_ticket_id', v_encounter.delivery_ticket_id
            )
        );
        
        RAISE EXCEPTION '%', v_error_text;
    END IF;

    -- Find appropriate inventory item for nursing visit
    -- First try to find from prior authorization
    SELECT 
        inv.id,
        CASE
            WHEN v_encounter.visit_number = 1 THEN
                ROUND(v_encounter.time_total::numeric / COALESCE(NULLIF(inv.nursing_hours::numeric, 0::numeric), 1::numeric), 2)
            ELSE
                ROUND(v_encounter.time_total::numeric / COALESCE(NULLIF(inv.nursing_hours::numeric, 0::numeric), 1::numeric), 2)
        END as calculated_quantity
    INTO v_inventory_id, v_quantity
    FROM form_patient_prior_auth pa
    INNER JOIN gr_form_patient_prior_auth_nc_id_to_list_billing_code_id gr_pa 
        ON gr_pa.form_patient_prior_auth_fk = pa.id
        AND gr_pa.deleted IS NOT TRUE
        AND gr_pa.archived IS NOT TRUE
    INNER JOIN form_list_billing_code bc 
        ON bc.id = gr_pa.list_billing_code_id_fk
        AND bc.deleted IS NOT TRUE
        AND bc.archived IS NOT TRUE
    INNER JOIN form_inventory inv 
        ON inv.billable_code_id = bc.id
        AND inv.deleted IS NOT TRUE
        AND inv.archived IS NOT TRUE
        AND COALESCE(inv.active, 'No') = 'Yes'
        AND inv.type = 'Billable'
    WHERE pa.patient_id = v_encounter.patient_id
    AND pa.deleted IS NOT TRUE
    AND pa.archived IS NOT TRUE
    AND COALESCE(pa.status_id, '') = '5'  -- Active
    AND pa.pa_type LIKE '%Nursing%'
    AND (
        (pa.initial_visit = 'Yes' AND v_encounter.visit_number = 1) OR
        (pa.initial_visit IS NULL AND v_encounter.visit_number > 1)
    )
    ORDER BY 
        CASE WHEN pa.initial_visit = 'Yes' AND v_encounter.visit_number = 1 THEN 1
             WHEN pa.initial_visit IS NULL AND v_encounter.visit_number > 1 THEN 2
             ELSE 3 END,
        inv.nursing_hours ASC
    LIMIT 1;

    -- If no inventory found from prior auth, we cannot proceed
    IF v_inventory_id IS NULL OR v_quantity IS NULL OR v_quantity <= 0 THEN
        v_error_text := 'No appropriate nursing visit inventory item found or invalid quantity calculated';
        
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            v_error_text,
            'Error in create_nursing_visit_charge_line_json',
            'FUNCTION',
            current_schema(),
            'encounter',
            jsonb_build_object(
                'encounter_id', p_encounter_id,
                'patient_id', v_encounter.patient_id,
                'insurance_id', v_insurance_id,
                'inventory_id', v_inventory_id,
                'calculated_quantity', v_quantity,
                'time_total', v_encounter.time_total,
                'visit_number', v_encounter.visit_number
            )
        );
        
        RAISE EXCEPTION '%', v_error_text;
    END IF;

    -- Check for existing pending nursing charges for same patient/payer to group on same invoice
    SELECT 
        lcl.calc_invoice_split_no
    INTO v_calc_invoice_split_no
    FROM form_ledger_charge_line lcl
    INNER JOIN form_patient_insurance pi ON pi.id = lcl.insurance_id
    WHERE lcl.patient_id = v_encounter.patient_id
    AND pi.payer_id = (SELECT payer_id FROM form_patient_insurance WHERE id = v_insurance_id)
    AND lcl.encounter_id IS NOT NULL  -- This is a nursing visit charge
    AND lcl.invoice_no IS NULL  -- Not yet invoiced
    AND lcl.deleted IS NOT TRUE
    AND lcl.archived IS NOT TRUE
    AND COALESCE(lcl.void, 'No') <> 'Yes'
    AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
    AND lcl.calc_invoice_split_no IS NOT NULL
    ORDER BY lcl.created_on DESC
    LIMIT 1;

    -- If no existing split found, generate a new one
    IF v_calc_invoice_split_no IS NULL THEN
        v_calc_invoice_split_no := gen_random_uuid()::text;
    END IF;

    -- Create the charge line using the existing function
    SELECT * INTO v_charge_line
    FROM create_ledger_charge_line(
        p_inventory_id := v_inventory_id,
        p_insurance_id := v_insurance_id,
        p_site_id := COALESCE(v_encounter.site_id, v_delivery_ticket.site_id),
        p_patient_id := v_encounter.patient_id,
        p_quantity := v_quantity,
        p_is_primary_drug := FALSE,  -- Nursing visits are not drugs
        p_compound_no := NULL,
        p_order_rx_id := NULL,
        p_rental_id := NULL,
        p_ticket_no := v_delivery_ticket.ticket_no,
        p_ticket_item_no := NULL,
        p_encounter_id := p_encounter_id,
        p_is_test := FALSE,
        p_parent_charge_no := NULL
    );

    -- Calculate nursing cost based on agency rates and encounter details
    DECLARE
        v_nursing_cost_data record;
        v_calculated_cost numeric := 0.00;
        v_calculated_cost_ea numeric := 0.00;
    BEGIN
        -- Get agency and encounter cost data
        SELECT 
            agency.rate::numeric as rate,
            agency.mileage_rate::numeric as mileage_rate,
            agency.overtime::text as overtime,
            agency.overtime_rate::numeric as overtime_rate,
            agency.overtime_threshold::numeric as overtime_threshold,
            enc.time_total::numeric as time_total,
            enc.total_mileage::numeric as total_mileage
        INTO v_nursing_cost_data
        FROM form_encounter enc
        LEFT JOIN form_patient_ancillary agency ON agency.id = enc.agency_id
            AND agency.deleted IS NOT TRUE
            AND agency.archived IS NOT TRUE
        WHERE enc.id = p_encounter_id;

        IF v_nursing_cost_data IS NOT NULL AND v_nursing_cost_data.time_total::numeric > 0::numeric THEN
            DECLARE
                v_agency_rate numeric := COALESCE(v_nursing_cost_data.rate::numeric, 0.00::numeric);
                v_mileage_rate numeric := COALESCE(v_nursing_cost_data.mileage_rate::numeric, 0.00::numeric);
                v_bill_overtime boolean := COALESCE(v_nursing_cost_data.overtime, 'No') = 'Yes';
                v_overtime_rate numeric := CASE WHEN v_bill_overtime THEN COALESCE(v_nursing_cost_data.overtime_rate::numeric, 0.00::numeric) ELSE 0.00::numeric END;
                v_overtime_threshold numeric := CASE WHEN v_bill_overtime THEN COALESCE(v_nursing_cost_data.overtime_threshold::numeric, 0.00::numeric) ELSE 0.00::numeric END;
                v_overtime_hours numeric := CASE 
                    WHEN v_bill_overtime AND v_nursing_cost_data.time_total::numeric - v_overtime_threshold > 0::numeric 
                    THEN v_nursing_cost_data.time_total::numeric - v_overtime_threshold 
                    ELSE 0.00 
                END;
                v_overtime_cost numeric := CASE 
                    WHEN v_bill_overtime AND v_nursing_cost_data.time_total::numeric - v_overtime_threshold > 0::numeric
                    THEN (v_nursing_cost_data.time_total::numeric - v_overtime_threshold) * v_overtime_rate 
                    ELSE 0.00 
                END;
                v_normal_hours numeric := v_nursing_cost_data.time_total::numeric - v_overtime_hours;
                v_normal_cost numeric := v_normal_hours * v_agency_rate;
                v_mileage_cost numeric := COALESCE(v_nursing_cost_data.total_mileage::numeric, 0.00::numeric) * v_mileage_rate::numeric;
                v_total_cost numeric := v_normal_cost + v_overtime_cost + v_mileage_cost;
            BEGIN
                v_calculated_cost := ROUND(v_total_cost::numeric, 2);
                v_calculated_cost_ea := ROUND(v_total_cost::numeric / COALESCE(NULLIF(v_quantity::numeric, 0::numeric), 1::numeric), 4);
            END;
        END IF;
    END;

    -- Convert charge_line to JSON with calc_invoice_split_no
    v_result := jsonb_build_object(
        'calc_invoice_split_no', v_calc_invoice_split_no,
        'site_id', v_charge_line.site_id,
        'patient_id', v_charge_line.patient_id,
        'insurance_id', v_charge_line.insurance_id,
        'payer_id', v_charge_line.payer_id,
        'inventory_id', v_charge_line.inventory_id,
        'shared_contract_id', v_charge_line.shared_contract_id,
        'is_dirty', v_charge_line.is_dirty,
        'parent_charge_no', v_charge_line.parent_charge_no,
        'master_charge_no', v_charge_line.master_charge_no,
        'compound_no', v_charge_line.compound_no,
        'ticket_no', v_charge_line.ticket_no,
        'ticket_item_no', v_charge_line.ticket_item_no,
        'rental_id', v_charge_line.rental_id,
        'order_rx_id', v_charge_line.order_rx_id,
        'is_primary_drug', v_charge_line.is_primary_drug,
        'is_primary_drug_ncpdp', v_charge_line.is_primary_drug_ncpdp,
        'rx_no', v_charge_line.rx_no,
        'inventory_type_filter', v_charge_line.inventory_type_filter,
        'inventory_type', v_charge_line.inventory_type,
        'revenue_code_id', v_charge_line.revenue_code_id,
        'hcpc_code', v_charge_line.hcpc_code,
        'ndc', v_charge_line.ndc,
        'formatted_ndc', v_charge_line.formatted_ndc,
        'gcn_seqno', v_charge_line.gcn_seqno,
        'charge_quantity', v_charge_line.charge_quantity,
        'charge_quantity_ea', v_charge_line.charge_quantity_ea,
        'hcpc_quantity', v_charge_line.hcpc_quantity,
        'hcpc_unit', v_charge_line.hcpc_unit,
        'metric_quantity', v_charge_line.metric_quantity,
        'charge_unit', v_charge_line.charge_unit,
        'bill_quantity', v_charge_line.bill_quantity,
        'metric_unit_each', v_charge_line.metric_unit_each,
        'billing_unit_id', v_charge_line.billing_unit_id,
        'billing_method_id', v_charge_line.billing_method_id,
        'pricing_source', v_charge_line.pricing_source,
        'billed', v_charge_line.billed,
        'calc_billed_ea', v_charge_line.calc_billed_ea,
        'expected', v_charge_line.expected,
        'calc_expected_ea', v_charge_line.calc_expected_ea,
        'list_price', v_charge_line.list_price,
        'calc_list_ea', v_charge_line.calc_list_ea,
        'total_cost', v_calculated_cost,
        'gross_amount_due', v_charge_line.gross_amount_due,
        'incv_amt_sub', v_charge_line.incv_amt_sub,
        'copay', v_charge_line.copay,
        'calc_cost_ea', v_calculated_cost_ea,
        'total_adjusted', v_charge_line.total_adjusted,
        'total_balance_due', v_charge_line.total_balance_due,
        'dispense_fee', v_charge_line.dispense_fee,
        'pt_pd_amt_sub', v_charge_line.pt_pd_amt_sub,
        'encounter_id', v_charge_line.encounter_id,
        'description', v_charge_line.description,
        'upc', v_charge_line.upc,
        'upin', v_charge_line.upin,
        'cost_basis', v_charge_line.cost_basis,
        'awp_price', v_charge_line.awp_price,
        'modifier_1', v_charge_line.modifier_1,
        'modifier_2', v_charge_line.modifier_2,
        'modifier_3', v_charge_line.modifier_3,
        'modifier_4', v_charge_line.modifier_4,
        'rental_type', v_charge_line.rental_type,
        'frequency_code', v_charge_line.frequency_code,
        'fill_number', v_charge_line.fill_number,
        'paid', v_charge_line.paid
    );

    RETURN v_result;

EXCEPTION WHEN OTHERS THEN
    -- Log any unexpected errors
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        SQLERRM,
        'Unexpected error in create_nursing_visit_charge_line_json',
        'FUNCTION',
        current_schema(),
        'encounter',
        jsonb_build_object(
            'encounter_id', p_encounter_id,
            'patient_id', COALESCE(v_encounter.patient_id, NULL),
            'insurance_id', v_insurance_id,
            'inventory_id', v_inventory_id
        )
    );
    RAISE;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Add helpful comment
COMMENT ON FUNCTION create_nursing_visit_charge_line_json(integer) IS 
'Generates charge line data as JSON for a nursing visit based on an approved encounter form. 
Finds appropriate insurance and inventory items based on prior authorization and visit type.
Groups nursing charges for the same patient/payer onto the same invoice using calc_invoice_split_no.
Returns JSON data for Node.js to process and insert with proper charge_no generation.'; 