DO $$ BEGIN
  PERFORM drop_all_function_signatures('fn_update_dt_status_when_billed');
END $$;
CREATE OR REPLACE FUNCTION fn_update_dt_status_when_billed() 
RETURNS TRIGGER AS $$
BEGIN

  INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
  VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);
  -- Only proceed if delivery_ticket_id is set
  IF NEW.delivery_ticket_id IS NOT NULL THEN
    -- Update delivery ticket status to 'billed' if not voided
    UPDATE form_careplan_delivery_tick
    SET status = 'billed'
    WHERE id = NEW.delivery_ticket_id
    AND COALESCE(void, 'No') = 'No' AND COALESCE(confirmed, 'No') = 'Yes';
  END IF;

  RETURN NEW;
EXCEPTION WHEN OTHERS THEN
  -- Log error
  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    SQLERRM,
    'Error updating delivery ticket status when billed',
    'TRIGGER',
    current_schema(),
    'form_careplan_delivery_tick',
    jsonb_build_object(
      'delivery_ticket_id', NEW.delivery_ticket_id
    )
  );
  RAISE;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE TRIGGER trg_update_dt_status_when_billed
  AFTER INSERT OR UPDATE OF delivery_ticket_id
  ON form_billing_invoice
  FOR EACH ROW
  EXECUTE FUNCTION fn_update_dt_status_when_billed();
