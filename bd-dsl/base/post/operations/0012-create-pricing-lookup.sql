-- Create indices on NDC fields for all relevant tables

CREATE OR REPLACE FUNCTION get_inventory_pricing(
  p_inventory_id integer,
  p_insurance_id integer,
  p_site_id integer,
  p_patient_id integer
) RETURNS TABLE (
  cost_ea numeric,
  daily_rental_cost numeric,
  monthly_rental_cost numeric,
  charge_quantity_ea numeric,
  hcpc_code text,
  hcpc_quantity numeric,
  hcpc_unit text,
  metric_quantity numeric,
  charge_unit text,
  gcn_seqno text,
  upc text,
  upin text,
  dispense_fee numeric,
  billing_method_id text,
  payer_id int,
  modifier_1 text,
  modifier_2 text,
  modifier_3 text,
  modifier_4 text,
  limits text,
  refills_limit int,
  unit_limit int,
  limit_freq text,
  not_covered text,
  auth_required text,
  req_auth_for text,
  max_rental_claims int,
  no_recurring_billing text,
  daily_bill_rental text,
  required_doc_ids text[],
  ndc text,
  formatted_ndc text,
  inventory_type text,
  metric_unit_each numeric,
  billing_unit_id text,
  shared_contract_id integer,
  cost_basis text,
  revenue_code_id text,
  list_ea numeric,
  daily_rental_list_price numeric,
  monthly_rental_list_price numeric,
  bill_ea numeric,
  expected_copay_ea numeric,
  rental_bill_daily_ea numeric,
  rental_bill_monthly_ea numeric,
  expected_ea numeric,
  rental_expected_daily_ea numeric,
  rental_expected_monthly_ea numeric,
  special_price_price_basis text,
  expected_price_price_basis text,
  pricing_source text,
  billable text,
  error text,
  awp_price numeric,
  description text
) AS $$
DECLARE
    error_text text;
    effective_payer_id integer;
    patient_state_code text;
    patient_zip_code text;
    medicare_rural boolean := false;
    state_pricing_column text;
BEGIN
    -- Get effective payer ID in a single query
    SELECT 
        CASE 
            WHEN i.billing_method_id IN ('cms1500', 'mm') 
            AND COALESCE(i.bill_for_denial,'No') = 'Yes'
            AND i.next_insurance_id IS NOT NULL 
            THEN next_i.payer_id::int
            ELSE i.payer_id::int
        END INTO effective_payer_id
    FROM form_patient_insurance i
    LEFT JOIN form_patient_insurance next_i 
        ON next_i.id = i.next_insurance_id 
        AND next_i.archived IS NOT TRUE 
        AND next_i.deleted IS NOT TRUE
    WHERE i.id = p_insurance_id
        AND i.archived IS NOT TRUE 
        AND i.deleted IS NOT TRUE;
    
    -- Get patient location data
    SELECT 
        state_id, zip INTO patient_state_code, patient_zip_code
    FROM form_patient_address
    WHERE patient_id = p_patient_id
        AND address_type IN ('Shipping', 'Home')
    ORDER BY 
        CASE address_type 
            WHEN 'Shipping' THEN 1 
            WHEN 'Home' THEN 2 
        END
    LIMIT 1;
    
    -- Check if patient is in a rural zip code
    IF patient_zip_code IS NOT NULL THEN
        SELECT EXISTS (
            SELECT 1 
            FROM form_list_cms_dme_rural r
            WHERE r.dmeposruralzipcode = patient_zip_code
        ) INTO medicare_rural;
    END IF;
    
    -- Create state column name
    IF patient_state_code IS NOT NULL THEN
        IF medicare_rural THEN
            state_pricing_column := LOWER(patient_state_code) || '_r';
        ELSE
            state_pricing_column := LOWER(patient_state_code) || '_nr';
        END IF;
    ELSE
        state_pricing_column := 'ny_nr'; -- Default to New York non-rural if no state
    END IF;
    
    -- Build pricing data in a single query
    RETURN QUERY
    WITH inventory_price_info AS (
        -- Get inventory basic information and pricing
        SELECT 
            inv.id as inventory_id,
            inv.price_code_id,
            COALESCE(inv.awp_price, 0.00)::numeric as awp_price,
            CASE 
                WHEN inv.type = 'Equipment Rental' THEN COALESCE(inv.purchase_list_price, 0.00)::numeric
                ELSE COALESCE(inv.list_price, 0.00)::numeric
            END as list_ea,
            COALESCE(inv.asp_price, 0.00)::numeric as asp_price,
            COALESCE(inv.wac_price, 0.00)::numeric as wac_price,
            COALESCE(inv.last_cost_ea, 0.00)::numeric as cost_ea,
            COALESCE(inv.add_price1, 0.00)::numeric as add_price1,
            COALESCE(inv.add_price2, 0.00)::numeric as add_price2,
            COALESCE(inv.purchase_list_price, 0.00)::numeric as device_sale_price_expected,
            COALESCE(inv.purchase_list_price, 0.00)::numeric as device_sale_price_special,
            COALESCE(inv.daily_rental_list_price, 0.00)::numeric as daily_rental_list_price,
            COALESCE(inv.daily_rental_list_price, 0.00)::numeric as daily_rental_price_expected,
            COALESCE(inv.daily_rental_list_price, 0.00)::numeric as daily_rental_price_special,
            COALESCE(inv.monthly_rental_list_price, 0.00)::numeric as monthly_rental_list_price,
            COALESCE(inv.monthly_rental_list_price, 0.00)::numeric as monthly_rental_price_expected,
            COALESCE(inv.monthly_rental_list_price, 0.00)::numeric as monthly_rental_price_special,
            COALESCE(inv.monthly_rental_cost, 0.00)::numeric as monthly_rental_cost,
            COALESCE(inv.daily_rental_cost, 0.00)::numeric as daily_rental_cost,
            COALESCE(inv.purchase_cost, 0.00)::numeric as purchase_cost,
            inv.name::text as description,
            CASE 
                WHEN inv.type = 'Equipment Rental' THEN COALESCE(inv.hcpc_code, 'E1399')::text
                WHEN inv.type = 'Supplies' THEN COALESCE(inv.hcpc_code, 'A9999')::text
                WHEN inv.type IN ('Drug', 'Compound') THEN COALESCE(inv.hcpc_code, 'J3490')::text 
                ELSE COALESCE(inv.hcpc_code, '')::text
            END as hcpc_code,
            inv.hcpc_quantity::numeric,
            COALESCE(inv.hcpc_unit, '')::text as hcpc_unit,
            inv.hcpc_modifier::text,
            inv.metric_unit_each::numeric,
            inv.quantity_each::numeric as metric_quantity,
            COALESCE(inv.billing_unit_id, 'each')::text as billing_unit_id,
            inv.gcn_seqno::text as gcn_seqno,
            inv.type::text as inventory_type,
            CASE 
                WHEN inv.type = 'Drug' THEN COALESCE(inv.revenue_code_id, '01')::text 
                WHEN inv.type = 'Supply' THEN COALESCE(inv.revenue_code_id, '02')::text
                WHEN inv.type = 'Equipment Rental' THEN COALESCE(inv.revenue_code_id, '03')::text
                WHEN inv.type = 'Billable' THEN COALESCE(inv.revenue_code_id, '04')::text
                ELSE COALESCE(inv.revenue_code_id, '00')::text
            END as revenue_code_id,
            COALESCE(inv.upc, '')::text as upc,
            COALESCE(inv.upin, '')::text as upin,
            COALESCE(inv.ndc, '99999999911')::text as ndc,
            COALESCE(inv.formatted_ndc, '99999-9999-11')::text as formatted_ndc,
            CASE 
                WHEN inv.type IN ('Drug','Compound') THEN 'Yes'::text 
                WHEN inv.type = 'Supply' THEN COALESCE(inv.supply_billable, 'No')::text
                WHEN inv.type = 'Equipment Rental' THEN COALESCE(inv.hcpc_billable, 'No')::text
                WHEN inv.type = 'Billable' THEN 'Yes'::text
                ELSE 'No'::text
            END as billable,
            COALESCE(inv.name, inv.auto_name)::text as name
        FROM form_inventory inv
        WHERE inv.id = p_inventory_id
            AND inv.archived IS NOT TRUE
            AND inv.deleted IS NOT TRUE
    ),
    medicare_dme_pricing AS (
        -- Get Medicare pricing data for DME items only if payer is Medicare type
        SELECT 
            ip.hcpc_code::text as hcpc_code,
            dme.rental_price::numeric as rental_price,
            dme.purchase_price::numeric as purchase_price
        FROM inventory_price_info ip
        JOIN form_payer p ON p.id = effective_payer_id AND p.type_id = 'MCRB'
        LEFT JOIN LATERAL (
            SELECT 
                d.hcpcs,
                d.mod,
                -- Use COALESCE to handle case when column doesn't exist
                    get_column_value(state_pricing_column, 'form_list_cms_dmepos', 
                        ' WHERE hcpcs = ' || quote_literal(ip.hcpc_code) || ' AND mod = ''RR'' LIMIT 1') as rental_price,
                    get_column_value(state_pricing_column, 'form_list_cms_dmepos', 
                        ' WHERE hcpcs = ' || quote_literal(ip.hcpc_code) || ' AND (mod IS NULL OR mod = ''NU'') LIMIT 1')
             as purchase_price
            FROM form_list_cms_dmepos d 
            WHERE d.hcpcs = ip.hcpc_code
            LIMIT 1
        ) dme ON true
    ),
    payer_data AS (
        -- Get payer settings in a single query for better performance
        SELECT 
            p.id::int as payer_id,
            p.billing_method_id::text,
            COALESCE(p.payer_bill_in_hcpc, 'No')::text as payer_bill_in_hcpc,
            p.default_dispense_fee::numeric,
            CASE
                WHEN (ip.inventory_type = 'Equipment Rental' AND COALESCE(p.cover_dme, 'No') = 'No') OR
                     (ip.inventory_type = 'Supply' AND COALESCE(p.cover_supplies, 'No') = 'No')
                THEN 'Yes'::text
                ELSE 'No'::text
            END as not_covered,
            CASE 
                WHEN p.req_auth = 'Always' THEN 
                    CASE
                        WHEN ip.inventory_type IN ('Drug', 'Compound') AND COALESCE(p.req_auth_for::jsonb ? 'Medication', false) THEN 'Yes'
                        WHEN ip.inventory_type = 'Supply' AND COALESCE(p.req_auth_for::jsonb ? 'Supplies', false) THEN 'Yes'
                        WHEN ip.inventory_type = 'Equipment Rental' AND COALESCE(p.req_auth_for::jsonb ? 'DME', false) THEN 'Yes'
                        WHEN p.req_auth_for::jsonb = '[]'::jsonb OR p.req_auth_for IS NULL THEN 'Yes'
                        ELSE 'No'::text
                    END
                WHEN p.req_auth = 'Never' THEN 'No'::text
                WHEN p.req_auth IS NOT NULL THEN p.req_auth::text
                ELSE 'No'::text
            END as auth_required,
            p.req_auth_for::text,
            CASE
                WHEN p.billing_method_id = 'ncpdp' THEN p.base_ing_cost_on_id::text
                ELSE '00'::text
            END as cost_basis,
            p.max_rental_claims::int,
            p.no_recurring_billing::text,
            p.daily_bill_rental::text,
            -- Calculate the charge quantity based on HCPC or metric quantity
            CASE
                WHEN (p.billing_method_id in ('mm', 'cms1500') OR COALESCE(p.payer_bill_in_hcpc,'No') = 'Yes') AND 
                     ip.hcpc_code IS NOT NULL AND ip.hcpc_quantity > 0 
                THEN ip.hcpc_quantity::numeric
                ELSE 
                    CASE 
                        WHEN ip.inventory_type IN ('Drug', 'Compound') THEN COALESCE(ip.metric_unit_each::numeric, 1)::numeric
                        ELSE 1::numeric
                    END
            END as charge_quantity_ea,
            -- Calculate the charge unit
            CASE 
                WHEN (p.billing_method_id in ('mm', 'cms1500') OR COALESCE(p.payer_bill_in_hcpc,'No') = 'Yes') AND 
                     ip.hcpc_code IS NOT NULL AND ip.hcpc_quantity > 0
                THEN ip.hcpc_unit::text
                ELSE TRIM(format_numeric(ROUND(ip.metric_unit_each::numeric, 3)::numeric)) || ' ' || ip.billing_unit_id::text 
            END as charge_unit
        FROM inventory_price_info ip
        JOIN form_payer p ON p.id = effective_payer_id
        LIMIT 1
    ),
    contract_data AS (
        -- Get contract data for pricing rules
        SELECT 
            pc.id::int as contract_id,
            pc.contract_type::text,
            ppmi.id::int as price_matrix_item_id,
            ppmi.price_code_id::text,
            ppmi.expected_price::numeric,
            ppmi.special_price::numeric,
            ppmi.override_expected_formula_id::text,
            ppmi.override_expected_price_multiplier::numeric,
            ppmi.override_special_formula_id::text,
            ppmi.override_special_price_multiplier::numeric,
            filtered_spcf.expected_price_basis::text,
            filtered_spcf.expected_price_multiplier::numeric,
            filtered_spcf.special_price_basis::text,
            filtered_spcf.special_price_multiplier::numeric,
            -- Get metadata for any required documents
            (
                SELECT array_agg(rd.code::text)
                FROM form_list_required_doc rd
                JOIN gr_form_payer_price_matrix_item_rdc_ids_to_list_required_doc_id grrd 
                    ON grrd.form_payer_price_matrix_item_fk = ppmi.id 
                    AND grrd.form_list_required_doc_fk = rd.code
                WHERE rd.archived IS NOT TRUE AND rd.deleted IS NOT TRUE
            ) as required_doc_ids,
            COALESCE(ppmi.billable, ip.billable)::text as billable,
            COALESCE(ppmi.not_covered, pd.not_covered)::text as not_covered,
            COALESCE(ppmi.auth_required, pd.auth_required)::text as auth_required,
            COALESCE(ppmi.modifier_1, ip.hcpc_modifier)::text as modifier_1,
            ppmi.modifier_2::text,
            ppmi.modifier_3::text,
            ppmi.modifier_4::text,
            ppmi.limits::text,
            ppmi.refills_limit::int,
            ppmi.unit_limit::int,
            ppmi.limit_freq::text,
            COALESCE(ppmi.device_sale_price_special, 0.00)::numeric as device_sale_price_special,
            COALESCE(ppmi.device_sale_price_expected, 0.00)::numeric as device_sale_price_expected,
            COALESCE(ppmi.daily_rental_price_special, 0.00)::numeric as daily_rental_price_special,
            COALESCE(ppmi.daily_rental_price_expected, 0.00)::numeric as daily_rental_price_expected,
            COALESCE(ppmi.monthly_rental_price_special, 0.00)::numeric as monthly_rental_price_special,
            COALESCE(ppmi.monthly_rental_price_expected, 0.00)::numeric as monthly_rental_price_expected
        FROM inventory_price_info ip
        JOIN payer_data pd ON true
        LEFT JOIN form_payer py ON py.id = pd.payer_id
        LEFT JOIN form_payer_contract pc ON pc.id = py.assigned_contract_id 
            AND pc.archived IS NOT TRUE 
            AND pc.deleted IS NOT TRUE
        LEFT JOIN form_payer_price_matrix ppm ON ppm.id = pc.assigned_matrix_id 
            AND ppm.deleted IS NOT TRUE 
            AND ppm.archived IS NOT TRUE
        LEFT JOIN form_payer_price_matrix_item ppmi ON ppmi.inventory_id = ip.inventory_id 
            AND ppmi.payer_price_matrix_id = ppm.id 
            AND ppmi.deleted IS NOT TRUE 
            AND ppmi.archived IS NOT TRUE
        LEFT JOIN LATERAL (
        SELECT pcf2.*
        FROM sf_form_payer_contract_to_price_code_formulas spcf
        JOIN form_price_code_formulas pcf2 ON pcf2.id = spcf.form_price_code_formulas_fk
            WHERE pcf2.code_category = ip.price_code_id AND spcf.form_payer_contract_fk = pc.id
            AND spcf.archive IS NOT TRUE 
            AND spcf."delete" IS NOT TRUE 
        ) filtered_spcf on true 
        LIMIT 1
    ),
    site_price_code_data AS (
        -- Get site price code data for pricing rules (fallback if no contract)
        SELECT
            spc.price_code_id::text as price_code_id,
            spc.price_formula_id::text as price_formula_id,
            spc.multiplier::numeric as multiplier
        FROM form_site_price_code_item spc
        JOIN inventory_price_info ip ON ip.price_code_id = spc.price_code_id
        LIMIT 1
    ),
    medicare_pricing AS (
        -- Calculate Medicare prices if applicable
        SELECT
            CASE
                WHEN ip.inventory_type = 'Equipment Rental' THEN 'part_b_dme'::text
                ELSE 'part_b_asp'::text
            END as pricing_source,
            CASE
                WHEN ip.inventory_type != 'Equipment Rental' AND 
                     COALESCE(cms.co_insurance_per::numeric, 0) > 0.0 AND 
                     ip.asp_price > 0 AND ip.hcpc_quantity > 0 
                THEN ROUND((ip.asp_price::numeric / ip.hcpc_quantity::numeric) * 
                          (COALESCE(cms.co_insurance_per::numeric, 0)/100.0)::numeric, 2)::numeric
                ELSE NULL::numeric
            END as expected_copay_ea,
            CASE
                WHEN ip.inventory_type = 'Equipment Rental' THEN 
                    CASE 
                        WHEN mpd.purchase_price IS NOT NULL THEN mpd.purchase_price::numeric
                        WHEN ip.asp_price::numeric > 0 THEN ip.asp_price::numeric
                        ELSE NULL::numeric
                    END
                WHEN ip.asp_price > 0 AND ip.hcpc_quantity > 0 THEN
                    ROUND(ip.asp_price::numeric / GREATEST(ip.hcpc_quantity::numeric, 1), 2)::numeric
                ELSE 0.00::numeric
            END as expected_ea,
            CASE
                WHEN ip.inventory_type = 'Equipment Rental' THEN 
                    CASE 
                        WHEN mpd.rental_price IS NOT NULL THEN 
                            ROUND(COALESCE(mpd.rental_price::numeric, 0) / 30, 2)::numeric
                        ELSE NULL::numeric
                    END
                ELSE NULL::numeric
            END as daily_rental_price_expected,
            CASE
                WHEN ip.inventory_type = 'Equipment Rental' THEN
                    CASE
                        WHEN mpd.rental_price IS NOT NULL THEN COALESCE(mpd.rental_price::numeric, 0.00)::numeric
                        ELSE 0.00::numeric
                    END
                ELSE NULL::numeric
            END as monthly_rental_price_expected
        FROM inventory_price_info ip
        LEFT JOIN medicare_dme_pricing mpd ON mpd.hcpc_code = ip.hcpc_code
        LEFT JOIN form_list_cms_ndc_hcpc cms ON cms.hcpc = ip.hcpc_code
        LEFT JOIN payer_data pd ON pd.payer_id = effective_payer_id
        WHERE EXISTS (SELECT 1 FROM form_payer p WHERE p.id = effective_payer_id AND p.type_id = 'MCRB')
        LIMIT 1
    ),
    final_pricing AS (
        -- Finalize pricing data
        SELECT
            -- If contract pricing exists, use it; otherwise fall back to site pricing
            COALESCE(cd.contract_id, NULL)::int as contract_id,
            pd.payer_id::int,
            pd.billing_method_id::text,
            COALESCE(cd.billable, ip.billable)::text as billable,
            ip.cost_ea::numeric,
            ip.daily_rental_cost::numeric,
            ip.monthly_rental_cost::numeric,
            pd.charge_quantity_ea::numeric,
            ip.hcpc_code::text as hcpc_code,
            ip.hcpc_quantity::numeric,
            ip.hcpc_unit::text,
            ip.metric_quantity::numeric,
            pd.charge_unit::text,
            ip.gcn_seqno::text,
            ip.upc::text,
            ip.upin::text,
            pd.default_dispense_fee::numeric as dispense_fee,
            pd.cost_basis::text,
            cd.contract_id::int as shared_contract_id,
            COALESCE(cd.modifier_1, ip.hcpc_modifier)::text as modifier_1,
            cd.modifier_2::text,
            cd.modifier_3::text,
            cd.modifier_4::text,
            cd.limits::text,
            cd.refills_limit::int,
            cd.unit_limit::int,
            cd.limit_freq::text,
            COALESCE(cd.not_covered, pd.not_covered)::text as not_covered,
            COALESCE(cd.auth_required, pd.auth_required)::text as auth_required,
            pd.req_auth_for::text,
            pd.max_rental_claims::int,
            pd.no_recurring_billing::text,
            pd.daily_bill_rental::text,
            cd.required_doc_ids::text[],
            ip.ndc::text,
            ip.formatted_ndc::text,
            ip.inventory_type::text,
            ip.metric_unit_each::numeric,
            ip.billing_unit_id::text,
            ip.revenue_code_id::text,
            ip.list_ea::numeric,
            ip.daily_rental_list_price::numeric,
            ip.monthly_rental_list_price::numeric,
            ip.name::text as description,
            -- Add rental pricing
            CASE
                WHEN ip.inventory_type = 'Equipment Rental' THEN
                    CASE
                        WHEN mp.pricing_source = 'part_b_dme' AND mp.daily_rental_price_expected IS NOT NULL THEN mp.daily_rental_price_expected
                        WHEN cd.contract_id IS NOT NULL THEN 
                            COALESCE(cd.daily_rental_price_expected, ip.daily_rental_price_expected)
                        ELSE ip.daily_rental_price_expected
                    END
                ELSE NULL
            END as rental_expected_daily_ea,
            
            CASE 
                WHEN ip.inventory_type = 'Equipment Rental' THEN
                    CASE
                        WHEN mp.pricing_source = 'part_b_dme' AND mp.monthly_rental_price_expected IS NOT NULL THEN mp.monthly_rental_price_expected
                        WHEN cd.contract_id IS NOT NULL THEN 
                            COALESCE(cd.monthly_rental_price_expected, ip.monthly_rental_price_expected)
                        ELSE ip.monthly_rental_price_expected
                    END
                ELSE NULL
            END as rental_expected_monthly_ea,
            
            CASE
                WHEN ip.inventory_type = 'Equipment Rental' THEN
                    CASE
                        WHEN mp.pricing_source = 'part_b_dme' AND mp.daily_rental_price_expected IS NOT NULL THEN mp.daily_rental_price_expected
                        WHEN cd.contract_id IS NOT NULL THEN
                            CASE
                                WHEN cd.contract_type = 'Contract' THEN 
                                    COALESCE(cd.daily_rental_price_expected, ip.daily_rental_price_expected)
                                WHEN cd.contract_type = 'Shared Contract' THEN
                                    COALESCE(cd.daily_rental_price_special, ip.daily_rental_price_special)
                                ELSE ip.daily_rental_price_special
                            END
                        ELSE ip.daily_rental_price_special
                    END
                ELSE NULL
            END as rental_bill_daily_ea,
            
            CASE 
                WHEN ip.inventory_type = 'Equipment Rental' THEN
                    CASE
                        WHEN mp.pricing_source = 'part_b_dme' AND mp.monthly_rental_price_expected IS NOT NULL THEN mp.monthly_rental_price_expected
                        WHEN cd.contract_id IS NOT NULL THEN
                            CASE
                                WHEN cd.contract_type = 'Contract' THEN 
                                    COALESCE(cd.monthly_rental_price_expected, ip.monthly_rental_price_expected)
                                WHEN cd.contract_type = 'Shared Contract' THEN
                                    COALESCE(cd.monthly_rental_price_special, ip.monthly_rental_price_special)
                                ELSE ip.monthly_rental_price_special
                            END
                        ELSE ip.monthly_rental_price_special
                    END
                ELSE NULL
            END as rental_bill_monthly_ea,
            
            -- Calculate expected price
            CASE
                WHEN mp.expected_ea IS NOT NULL THEN mp.expected_ea
                WHEN cd.billable = '$0'::text THEN 0.00::numeric
                WHEN cd.contract_id IS NOT NULL THEN
                    CASE
                        WHEN cd.override_expected_formula_id IS NOT NULL AND cd.override_expected_price_multiplier IS NOT NULL AND cd.override_expected_price_multiplier > 0 THEN
                            -- Use override formula if present
                            CASE
                                WHEN cd.override_expected_formula_id = 'A' THEN 
                                    ROUND(COALESCE(ip.awp_price, 0)::numeric * COALESCE(cd.override_expected_price_multiplier, 1)::numeric, 4)
                                WHEN cd.override_expected_formula_id = 'W' THEN 
                                    ROUND(COALESCE(ip.wac_price, 0)::numeric * COALESCE(cd.override_expected_price_multiplier, 1)::numeric, 4)
                                WHEN cd.override_expected_formula_id = 'C' THEN 
                                    ROUND(COALESCE(ip.cost_ea, 0)::numeric * COALESCE(cd.override_expected_price_multiplier, 1)::numeric, 4)
                                WHEN cd.override_expected_formula_id = 'ASP' THEN 
                                    ROUND(COALESCE(ip.asp_price, 0)::numeric * COALESCE(cd.override_expected_price_multiplier, 1)::numeric, 4)
                                WHEN cd.override_expected_formula_id = 'L' THEN 
                                    ROUND(COALESCE(ip.list_ea, 0)::numeric * COALESCE(cd.override_expected_price_multiplier, 1)::numeric, 4)
                                WHEN cd.override_expected_formula_id = '1' THEN 
                                    ROUND(COALESCE(ip.add_price1, 0)::numeric * COALESCE(cd.override_expected_price_multiplier, 1)::numeric, 4)
                                WHEN cd.override_expected_formula_id = '2' THEN 
                                    ROUND(COALESCE(ip.add_price2, 0)::numeric * COALESCE(cd.override_expected_price_multiplier, 1)::numeric, 4)
                                WHEN cd.override_expected_formula_id = 'M' THEN 
                                    0.00
                                ELSE ip.list_ea
                            END
                        WHEN cd.expected_price_basis IS NOT NULL AND cd.expected_price_multiplier IS NOT NULL AND cd.expected_price_multiplier > 0 THEN
                            -- Use contract formula if no override
                            CASE
                                WHEN cd.expected_price_basis = 'A' THEN 
                                    ROUND(COALESCE(ip.awp_price, 0)::numeric * COALESCE(cd.expected_price_multiplier, 1)::numeric, 4)
                                WHEN cd.expected_price_basis = 'W' THEN 
                                    ROUND(COALESCE(ip.wac_price, 0)::numeric * COALESCE(cd.expected_price_multiplier, 1)::numeric, 4)
                                WHEN cd.expected_price_basis = 'C' THEN 
                                    ROUND(COALESCE(ip.cost_ea, 0)::numeric * COALESCE(cd.expected_price_multiplier, 1)::numeric, 4)
WHEN cd.expected_price_basis = 'ASP' THEN 
                                    ROUND(COALESCE(ip.asp_price, 0)::numeric * COALESCE(cd.expected_price_multiplier, 1)::numeric, 4)
                                WHEN cd.expected_price_basis = 'L' THEN 
                                    ROUND(COALESCE(ip.list_ea, 0)::numeric * COALESCE(cd.expected_price_multiplier, 1)::numeric, 4)
                                WHEN cd.expected_price_basis = '1' THEN 
                                    ROUND(COALESCE(ip.add_price1, 0)::numeric * COALESCE(cd.expected_price_multiplier, 1)::numeric, 4)
                                WHEN cd.expected_price_basis = '2' THEN 
                                    ROUND(COALESCE(ip.add_price2, 0)::numeric * COALESCE(cd.expected_price_multiplier, 1)::numeric, 4)
                                WHEN cd.expected_price_basis = 'M' THEN 
                                    0.00
                                ELSE ip.list_ea
                            END
                        ELSE ip.list_ea
                    END
                ELSE
                    -- Fall back to site pricing code
                    CASE
                        WHEN spcd.price_formula_id = 'A' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            ROUND(ip.awp_price::numeric * (SELECT spcd.multiplier FROM site_price_code_data spcd WHERE spcd.price_formula_id = 'A' LIMIT 1)::numeric, 4)
                        WHEN spcd.price_formula_id = 'W' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            ROUND(ip.wac_price::numeric * (SELECT spcd.multiplier FROM site_price_code_data spcd WHERE spcd.price_formula_id = 'W' LIMIT 1)::numeric, 4)
                        WHEN spcd.price_formula_id = 'C' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            ROUND(ip.cost_ea::numeric * (SELECT spcd.multiplier FROM site_price_code_data spcd WHERE spcd.price_formula_id = 'C' LIMIT 1)::numeric, 4)
                        WHEN spcd.price_formula_id = 'ASP' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            ROUND(ip.asp_price::numeric * (SELECT spcd.multiplier FROM site_price_code_data spcd WHERE spcd.price_formula_id = 'ASP' LIMIT 1)::numeric, 4)
                        WHEN spcd.price_formula_id = 'L' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            ROUND(ip.list_ea::numeric * (SELECT spcd.multiplier FROM site_price_code_data spcd WHERE spcd.price_formula_id = 'L' LIMIT 1)::numeric, 4)
                        WHEN spcd.price_formula_id = '1' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            ROUND(ip.add_price1::numeric * (SELECT spcd.multiplier FROM site_price_code_data spcd WHERE spcd.price_formula_id = '1' LIMIT 1)::numeric, 4)
                        WHEN spcd.price_formula_id = 'M' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            0.00
                        WHEN spcd.price_formula_id = '2' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            ROUND(ip.add_price2::numeric * (SELECT spcd.multiplier FROM site_price_code_data spcd WHERE spcd.price_formula_id = '2' LIMIT 1)::numeric, 4)
                        ELSE ip.list_ea
                    END
            END as expected_ea,

            -- Calculate special price with similar approach
            CASE
                WHEN mp.expected_ea IS NOT NULL THEN mp.expected_ea
                WHEN cd.billable = '$0'::text THEN 0.00::numeric
                WHEN cd.contract_id IS NOT NULL THEN
                    CASE
                        WHEN cd.contract_type = 'Contract' THEN 
                            -- For Contract type, use the same logic as expected_ea
                            CASE 
                                WHEN cd.override_expected_formula_id IS NOT NULL AND cd.override_expected_price_multiplier IS NOT NULL AND cd.override_expected_price_multiplier > 0 THEN
                                    CASE
                                        WHEN cd.override_expected_formula_id = 'A' THEN 
                                            ROUND(COALESCE(ip.awp_price, 0)::numeric * COALESCE(cd.override_expected_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.override_expected_formula_id = 'W' THEN 
                                            ROUND(COALESCE(ip.wac_price, 0)::numeric * COALESCE(cd.override_expected_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.override_expected_formula_id = 'C' THEN 
                                            ROUND(COALESCE(ip.cost_ea, 0)::numeric * COALESCE(cd.override_expected_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.override_expected_formula_id = 'ASP' THEN 
                                            ROUND(COALESCE(ip.asp_price, 0)::numeric * COALESCE(cd.override_expected_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.override_expected_formula_id = 'L' THEN 
                                            ROUND(COALESCE(ip.list_ea, 0)::numeric * COALESCE(cd.override_expected_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.override_expected_formula_id = '1' THEN 
                                            ROUND(COALESCE(ip.add_price1, 0)::numeric * COALESCE(cd.override_expected_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.override_expected_formula_id = '2' THEN 
                                            ROUND(COALESCE(ip.add_price2, 0)::numeric * COALESCE(cd.override_expected_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.override_expected_formula_id = 'M' THEN 
                                            0.00
                                        ELSE ip.list_ea
                                    END
                                WHEN cd.expected_price_basis IS NOT NULL AND cd.expected_price_multiplier IS NOT NULL AND cd.expected_price_multiplier > 0 THEN
                                    -- Use contract formula with expected price basis
                                    CASE
                                        WHEN cd.expected_price_basis = 'A' THEN 
                                            ROUND(COALESCE(ip.awp_price, 0)::numeric * COALESCE(cd.expected_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.expected_price_basis = 'W' THEN 
                                            ROUND(COALESCE(ip.wac_price, 0)::numeric * COALESCE(cd.expected_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.expected_price_basis = 'C' THEN 
                                            ROUND(COALESCE(ip.cost_ea, 0)::numeric * COALESCE(cd.expected_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.expected_price_basis = 'ASP' THEN 
                                            ROUND(COALESCE(ip.asp_price, 0)::numeric * COALESCE(cd.expected_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.expected_price_basis = 'L' THEN 
                                            ROUND(COALESCE(ip.list_ea, 0)::numeric * COALESCE(cd.expected_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.expected_price_basis = '1' THEN 
                                            ROUND(COALESCE(ip.add_price1, 0)::numeric * COALESCE(cd.expected_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.expected_price_basis = '2' THEN 
                                            ROUND(COALESCE(ip.add_price2, 0)::numeric * COALESCE(cd.expected_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.expected_price_basis = 'M' THEN 
                                            0.00
                                        ELSE ip.list_ea
                                    END
                                ELSE ip.list_ea
                            END
                        ELSE -- For Shared Contract, use special price logic
                            CASE 
                                WHEN cd.override_special_formula_id IS NOT NULL AND cd.override_special_price_multiplier IS NOT NULL AND cd.override_special_price_multiplier > 0 THEN
                                    -- Use override formula with special price
                                    CASE
                                        WHEN cd.override_special_formula_id = 'A' THEN 
                                            ROUND(COALESCE(ip.awp_price, 0)::numeric * COALESCE(cd.override_special_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.override_special_formula_id = 'W' THEN 
                                            ROUND(COALESCE(ip.wac_price, 0)::numeric * COALESCE(cd.override_special_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.override_special_formula_id = 'C' THEN 
                                            ROUND(COALESCE(ip.cost_ea, 0)::numeric * COALESCE(cd.override_special_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.override_special_formula_id = 'ASP' THEN 
                                            ROUND(COALESCE(ip.asp_price, 0)::numeric * COALESCE(cd.override_special_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.override_special_formula_id = 'L' THEN 
                                            ROUND(COALESCE(ip.list_ea, 0)::numeric * COALESCE(cd.override_special_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.override_special_formula_id = '1' THEN 
                                            ROUND(COALESCE(ip.add_price1, 0)::numeric * COALESCE(cd.override_special_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.override_special_formula_id = '2' THEN 
                                            ROUND(COALESCE(ip.add_price2, 0)::numeric * COALESCE(cd.override_special_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.override_special_formula_id = 'M' THEN 
                                            0.00
                                        ELSE ip.list_ea
                                    END
                                WHEN cd.special_price_basis IS NOT NULL AND cd.special_price_multiplier IS NOT NULL AND cd.special_price_multiplier > 0 THEN
                                    -- Use contract formula with special price basis
                                    CASE
                                        WHEN cd.special_price_basis = 'A' THEN 
                                            ROUND(COALESCE(ip.awp_price, 0)::numeric * COALESCE(cd.special_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.special_price_basis = 'W' THEN 
                                            ROUND(COALESCE(ip.wac_price, 0)::numeric * COALESCE(cd.special_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.special_price_basis = 'C' THEN 
                                            ROUND(COALESCE(ip.cost_ea, 0)::numeric * COALESCE(cd.special_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.special_price_basis = 'ASP' THEN 
                                            ROUND(COALESCE(ip.asp_price, 0)::numeric * COALESCE(cd.special_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.special_price_basis = 'L' THEN 
                                            ROUND(COALESCE(ip.list_ea, 0)::numeric * COALESCE(cd.special_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.special_price_basis = '1' THEN 
                                            ROUND(COALESCE(ip.add_price1, 0)::numeric * COALESCE(cd.special_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.special_price_basis = '2' THEN 
                                            ROUND(COALESCE(ip.add_price2, 0)::numeric * COALESCE(cd.special_price_multiplier, 1)::numeric, 4)
                                        WHEN cd.special_price_basis = 'M' THEN 
                                            0.00
                                        ELSE ip.list_ea
                                    END
                                ELSE ip.list_ea
                            END
                    END
                ELSE
                    -- Fall back to site pricing code (same approach as expected_ea)
                    CASE
                        WHEN spcd.price_formula_id = 'A' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            ROUND(ip.awp_price::numeric * (SELECT spcd.multiplier FROM site_price_code_data spcd WHERE spcd.price_formula_id = 'A' LIMIT 1)::numeric, 4)
                        WHEN spcd.price_formula_id = 'W' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            ROUND(ip.wac_price::numeric * (SELECT spcd.multiplier FROM site_price_code_data spcd WHERE spcd.price_formula_id = 'W' LIMIT 1)::numeric, 4)
                        WHEN spcd.price_formula_id = 'C' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            ROUND(ip.cost_ea::numeric * (SELECT spcd.multiplier FROM site_price_code_data spcd WHERE spcd.price_formula_id = 'C' LIMIT 1)::numeric, 4)
                        WHEN spcd.price_formula_id = 'ASP' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            ROUND(ip.asp_price::numeric * (SELECT spcd.multiplier FROM site_price_code_data spcd WHERE spcd.price_formula_id = 'ASP' LIMIT 1)::numeric, 4)
                        WHEN spcd.price_formula_id = 'L' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            ROUND(ip.list_ea::numeric * (SELECT spcd.multiplier FROM site_price_code_data spcd WHERE spcd.price_formula_id = 'L' LIMIT 1)::numeric, 4)
                        WHEN spcd.price_formula_id = '1' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            ROUND(ip.add_price1::numeric * (SELECT spcd.multiplier FROM site_price_code_data spcd WHERE spcd.price_formula_id = '1' LIMIT 1)::numeric, 4)
                        WHEN spcd.price_formula_id = '2' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            ROUND(ip.add_price2::numeric * (SELECT spcd.multiplier FROM site_price_code_data spcd WHERE spcd.price_formula_id = '2' LIMIT 1)::numeric, 4)
                        WHEN spcd.price_formula_id = 'M' AND spcd.multiplier IS NOT NULL AND spcd.multiplier > 0 THEN 
                            0.00
                        ELSE ip.list_ea
                    END
            END as bill_ea,
            
            -- Pass through source and basis information
            CASE
                WHEN cd.contract_type = 'Shared Contract' AND cd.override_special_formula_id IS NOT NULL AND cd.override_special_price_multiplier IS NOT NULL AND cd.override_special_price_multiplier > 0 THEN
                    cd.override_special_formula_id::text
                WHEN cd.contract_type = 'Shared Contract' AND cd.special_price_basis IS NOT NULL AND cd.special_price_multiplier IS NOT NULL AND cd.special_price_multiplier > 0 THEN
                    cd.special_price_basis::text
                WHEN cd.contract_type = 'Contract' AND cd.override_expected_formula_id IS NOT NULL AND cd.override_expected_price_multiplier IS NOT NULL AND cd.override_expected_price_multiplier > 0 THEN
                    cd.override_expected_formula_id::text
                WHEN  cd.contract_type = 'Contract' AND cd.expected_price_basis IS NOT NULL AND cd.expected_price_multiplier IS NOT NULL AND cd.expected_price_multiplier > 0 THEN
                    cd.expected_price_basis::text
                ELSE
                    spcd.price_formula_id::text
            END as special_price_price_basis,
            CASE
                WHEN cd.override_expected_formula_id IS NOT NULL AND cd.override_expected_price_multiplier IS NOT NULL AND cd.override_expected_price_multiplier > 0 THEN
                    cd.override_expected_formula_id::text
                WHEN cd.expected_price_basis IS NOT NULL AND cd.expected_price_multiplier IS NOT NULL AND cd.expected_price_multiplier > 0 THEN
                    cd.expected_price_basis::text
                ELSE
                    spcd.price_formula_id::text
            END as expected_price_price_basis,
            CASE
                WHEN cd.contract_id IS NOT NULL THEN
                    CASE 
                        WHEN cd.contract_type = 'Contract' THEN 'payer_contract'::text
                        WHEN cd.contract_type = 'Shared Contract' THEN 'shared_contract'::text
                        ELSE 'contract'::text
                    END
                ELSE 
                    CASE
                        WHEN mp.pricing_source IS NOT NULL THEN mp.pricing_source
                        ELSE 'list'::text
                    END
            END as pricing_source,
            CASE
                WHEN mp.expected_copay_ea IS NOT NULL THEN mp.expected_copay_ea
                ELSE 0
            END as expected_copay_ea,
            ip.awp_price
        FROM inventory_price_info ip
        LEFT JOIN site_price_code_data spcd ON true
        LEFT JOIN medicare_pricing mp ON true
        LEFT JOIN payer_data pd ON true
        LEFT JOIN contract_data cd ON true
    )
    
    -- Final select statement to return from the function
    SELECT 
        fp.cost_ea,
        fp.daily_rental_cost,
        fp.monthly_rental_cost,
        fp.charge_quantity_ea,
        fp.hcpc_code,
        fp.hcpc_quantity,
        fp.hcpc_unit,
        fp.metric_quantity,
        fp.charge_unit,
        fp.gcn_seqno,
        fp.upc,
        fp.upin,
        fp.dispense_fee,
        fp.billing_method_id,
        fp.payer_id,
        fp.modifier_1,
        fp.modifier_2,
        fp.modifier_3,
        fp.modifier_4,
        fp.limits,
        fp.refills_limit,
        fp.unit_limit,
        fp.limit_freq,
        fp.not_covered,
        fp.auth_required,
        fp.req_auth_for,
        fp.max_rental_claims,
        fp.no_recurring_billing,
        fp.daily_bill_rental,
        fp.required_doc_ids,
        fp.ndc,
        fp.formatted_ndc,
        fp.inventory_type,
        fp.metric_unit_each,
        fp.billing_unit_id,
        fp.shared_contract_id,
        fp.cost_basis,
        fp.revenue_code_id,
        fp.list_ea,
        fp.daily_rental_list_price,
        fp.monthly_rental_list_price,
        fp.bill_ea,
        fp.expected_copay_ea,
        fp.rental_bill_daily_ea,
        fp.rental_bill_monthly_ea,
        fp.expected_ea,
        fp.rental_expected_daily_ea,
        fp.rental_expected_monthly_ea,
        fp.special_price_price_basis,
        fp.expected_price_price_basis,
        fp.pricing_source,
        fp.billable,
        NULL::text as error,
        fp.awp_price,
        fp.description
    FROM final_pricing fp;

EXCEPTION WHEN OTHERS THEN
    -- Improved error handling
    GET STACKED DIAGNOSTICS error_text = MESSAGE_TEXT;
    
    -- Log error with more context
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        error_text,
        'Error fetching inventory pricing',
        'FUNCTION',
        current_schema(),
        'form_inventory',
        jsonb_build_object(
            'function_name', 'get_inventory_pricing',
            'inventory_id', p_inventory_id,
            'insurance_id', p_insurance_id,
            'site_id', p_site_id,
            'patient_id', p_patient_id
        )
    );
    
    -- Return the error record directly
    RETURN QUERY
    SELECT 
        NULL::numeric as cost_ea,
        NULL::numeric as daily_rental_cost,
        NULL::numeric as monthly_rental_cost,
        NULL::numeric as charge_quantity_ea,
        NULL::text as hcpc_code,
        NULL::numeric as hcpc_quantity,
        NULL::text as hcpc_unit,
        NULL::numeric as metric_quantity,
        NULL::text as charge_unit,
        NULL::text as gcn_seqno,
        NULL::text as upc,
        NULL::text as upin,
        NULL::numeric as dispense_fee,
        NULL::text as billing_method_id,
        NULL::int as payer_id,
        NULL::text as modifier_1,
        NULL::text as modifier_2,
        NULL::text as modifier_3,
        NULL::text as modifier_4,
        NULL::text as limits,
        NULL::int as refills_limit,
        NULL::int as unit_limit,
        NULL::text as limit_freq,
        NULL::text as not_covered,
        NULL::text as auth_required,
        NULL::text as req_auth_for,
        NULL::int as max_rental_claims,
        NULL::text as no_recurring_billing,
        NULL::text as daily_bill_rental,
        NULL::text[] as required_doc_ids,
        NULL::text as ndc,
        NULL::text as formatted_ndc,
        NULL::text as inventory_type,
        NULL::numeric as metric_unit_each,
        NULL::text as billing_unit_id,
        NULL::integer as shared_contract_id,
        NULL::text as cost_basis,
        NULL::text as revenue_code_id,
        NULL::numeric as list_ea,
        NULL::numeric as daily_rental_list_price,
        NULL::numeric as monthly_rental_list_price,
        NULL::numeric as bill_ea,
        NULL::numeric as expected_copay_ea,
        NULL::numeric as rental_bill_daily_ea,
        NULL::numeric as rental_bill_monthly_ea,
        NULL::numeric as expected_ea,
        NULL::numeric as rental_expected_daily_ea,
        NULL::numeric as rental_expected_monthly_ea,
        NULL::text as special_price_price_basis,
        NULL::text as expected_price_price_basis,
        NULL::text as pricing_source,
        NULL::text as billable,
        error_text as error,
        NULL::numeric as awp_price,
        NULL::text as description;
END;
$$ LANGUAGE plpgsql VOLATILE;