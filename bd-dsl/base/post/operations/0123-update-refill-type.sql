
CREATE OR <PERSON><PERSON>LACE FUNCTION update_refill_type_and_template_type()
RETURNS TRIGGER AS $$
/**
 * Updates refill_tracking and template_type when they are null
 * 
 * @returns {trigger} The modified NEW record
 */
BEGIN
    -- Check if input parameters are not null
    IF NEW IS NULL THEN
        RAISE EXCEPTION 'NEW record cannot be null';
    END IF;
    
    -- Wrap in try-catch block to handle errors
    BEGIN
        -- Set refill_tracking if it's null
        IF NEW.refill_tracking IS NULL OR NEW.template_type IS NULL THEN
            SELECT 
                rxt.refill_tracking,
                rxt.template_type
            INTO NEW.refill_tracking, NEW.template_type
            FROM form_list_rx_template rxt
            WHERE rxt.code = NEW.rx_template_id;
        END IF;

        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Error in update_refill_type_and_template_type: %', SQLERRM;
    END;
END;
$$ LANGUAGE plpgsql;

-- Create trigger on form_careplan_order_rx
CREATE OR REPLACE TRIGGER trg_update_refill_type_and_template_type
BEFORE INSERT OR UPDATE ON form_careplan_order_rx
FOR EACH ROW
EXECUTE FUNCTION update_refill_type_and_template_type();
