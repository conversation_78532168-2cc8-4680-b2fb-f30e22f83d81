-- Function to sync discontinued status from order items to rx records
DO $$ BEGIN
  PERFORM drop_all_function_signatures('calc_report_quantity_function');
END $$;
CREATE OR REPLACE FUNCTION calc_report_quantity_function()
RETURNS TRIGGER AS $$
DECLARE
    v_report_quantity NUMERIC;
    v_report_quantity_ea NUMERIC;
    v_report_unit_id TEXT;
    v_error_context TEXT;
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Only process for drugs and when necessary conditions are met
    IF NEW.type IN ('Drug') AND NEW.archived IS NOT TRUE AND NEW.deleted IS NOT TRUE AND NEW.dispense_quantity IS NOT NULL THEN
        BEGIN
            -- Capture error context for potential logging
            v_error_context := format('Processing care plan item %s with inventory ID %s', 
                                      NEW.id, COALESCE(NEW.inventory_id::TEXT, 'NULL'));

            -- Use a single CTE to calculate conversion factors
            WITH inventory_info AS (
                SELECT
                    inv.id AS inventory_id,
                    COALESCE(
                        CASE 
                            WHEN inv.report_unit_id = 'each' THEN 1
                            ELSE COALESCE(
                                bunits.conversion_factor, 
                                volunits.conversion_factor, 
                                0
                            )
                        END, 
                        0
                    ) AS report_quantity_ea,
                    COALESCE(
                        CASE 
                            WHEN inv.report_unit_id = 'each' THEN NEW.dispense_quantity
                            ELSE COALESCE(
                                bunits.conversion_factor, 
                                volunits.conversion_factor, 
                                0
                            ) * NEW.dispense_quantity
                        END, 
                        0
                    ) AS report_quantity,
                    inv.report_unit_id
                FROM form_inventory inv
                LEFT JOIN LATERAL (
                    SELECT 
                        COALESCE(inv.metric_unit_each::NUMERIC, 1) * uom_conversion_factor::NUMERIC AS conversion_factor
                    FROM form_list_fdb_unit_conversion
                    INNER JOIN form_list_unit uni1 ON uni1.code = inv.billing_unit_id
                    INNER JOIN form_list_unit uni2 ON uni2.code = inv.report_unit_id
                    WHERE uni1.uom_mstr_id = from_uom_mstr_id
                    AND uni2.uom_mstr_id = to_uom_mstr_id
                    AND inv.metric_unit_each::NUMERIC > 0
                    
                    UNION
                    
                    SELECT 
                        COALESCE(inv.metric_unit_each::NUMERIC, 1) AS conversion_factor
                    FROM form_list_unit uni1
                    INNER JOIN form_list_unit uni2 ON uni2.code = inv.report_unit_id
                    WHERE uni1.uom_mstr_id = uni2.uom_mstr_id
                    AND uni1.code = inv.billing_unit_id
                    AND inv.metric_unit_each::NUMERIC > 0
                ) AS bunits ON TRUE
                LEFT JOIN LATERAL (
                    SELECT 
                        COALESCE(inv.volume::NUMERIC, 1) * uom_conversion_factor::NUMERIC AS conversion_factor
                    FROM form_list_fdb_unit_conversion
                    INNER JOIN form_list_unit uni1 ON uni1.code = inv.volume_unit_id
                    INNER JOIN form_list_unit uni2 ON uni2.code = inv.report_unit_id
                    WHERE uni1.uom_mstr_id = from_uom_mstr_id
                    AND uni2.uom_mstr_id = to_uom_mstr_id
                    AND inv.dosage_form_type = 'Solution'
                    AND inv.volume_unit_id IS NOT NULL
                    AND inv.volume::NUMERIC > 0
                    
                    UNION
                    
                    SELECT 
                        COALESCE(inv.volume::NUMERIC, 1) AS conversion_factor
                    FROM form_list_unit uni1
                    INNER JOIN form_list_unit uni2 ON uni2.code = inv.report_unit_id
                    WHERE uni1.uom_mstr_id = uni2.uom_mstr_id
                    AND uni1.code = inv.volume_unit_id
                    AND inv.dosage_form_type = 'Solution'
                    AND inv.volume_unit_id IS NOT NULL
                    AND inv.volume::NUMERIC > 0
                ) AS volunits ON TRUE
                WHERE NEW.inventory_id = inv.id
                AND inv.report_unit_id IS NOT NULL
            )
            -- Directly assign values to the NEW record
            SELECT 
                report_quantity,
                report_quantity_ea,
                report_unit_id
            INTO 
                v_report_quantity, 
                v_report_quantity_ea, 
                v_report_unit_id
            FROM inventory_info
            LIMIT 1;

            -- Update the NEW record
            NEW.report_quantity := v_report_quantity;
            NEW.report_quantity_ea := v_report_quantity_ea;
            NEW.report_unit_id := v_report_unit_id;

        EXCEPTION WHEN OTHERS THEN
            -- Error logging
            RAISE NOTICE 'Error in report quantity calculation: %', SQLERRM;
            RAISE NOTICE 'Error context: %', v_error_context;
            
            INSERT INTO billing_error_log (
                error_message, 
                error_context, 
                error_type, 
                schema_name, 
                table_name,
                additional_details
            ) VALUES (
                SQLERRM, 
                v_error_context,
                TG_OP, 
                TG_TABLE_SCHEMA, 
                TG_TABLE_NAME,
                jsonb_build_object(
                    'trigger_name', 'calc_report_quantity_functions',
                    'new_record_id', NEW.id,
                    'inventory_id', NEW.inventory_id
                )
            );
            
            -- Re-raise the exception to prevent invalid data insertion
            RAISE;
        END;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Recreate the trigger
CREATE OR REPLACE TRIGGER calc_report_quantity_trigger
BEFORE INSERT OR UPDATE ON form_careplan_dt_item
FOR EACH ROW
EXECUTE FUNCTION calc_report_quantity_function();