
CREATE OR REPLACE FUNCTION calc_rental_service_end_date(service_from DATE, service_to DATE, payer_id INTEGER) 
RETURNS DATE AS $$
DECLARE
    v_service_end_date DATE;
BEGIN
  SELECT
    CASE
        WHEN py.span_rental_dates = 'Yes' THEN
            CASE
                WHEN service_from IS NOT NULL THEN
                    CASE 
                        WHEN EXTRACT(DAY FROM service_from) = 1 THEN 
                            (DATE_TRUNC('MONTH', service_from) + INTERVAL '1 MONTH' - INTERVAL '1 day')::DATE
                        WHEN EXTRACT(DAY FROM service_from) IN (30,31) THEN
                            (DATE_TRUNC('MONTH', service_from + INTERVAL '1 MONTH') - INTERVAL '1 day')::DATE 
                        ELSE
                            (service_from + INTERVAL '1 MONTH' - INTERVAL '1 day')::DATE
                    END
                ELSE COALESCE(service_to, CURRENT_DATE)
            END
        ELSE service_from
    END as service_end_date
  INTO v_service_end_date
  FROM form_payer py
  WHERE py.id = payer_id
  AND py.archived IS NOT TRUE
  AND py.deleted IS NOT TRUE;

  RETURN v_service_end_date;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_primary_claim(p_claim_no TEXT)
RETURNS TEXT AS $$
DECLARE
    primary_claim TEXT;
BEGIN
    WITH RECURSIVE claim_hierarchy AS (
        -- Base case: start with the given claim
        SELECT 
            claim_no, 
            parent_claim_no,
            0 AS depth
        FROM form_ncpdp
        WHERE claim_no = p_claim_no
        
        UNION ALL
        
        -- Recursive case: follow parent links upward
        SELECT 
            f.claim_no, 
            f.parent_claim_no,
            ch.depth + 1 AS depth
        FROM form_ncpdp f
        JOIN claim_hierarchy ch ON f.claim_no = ch.parent_claim_no
        WHERE ch.depth < 100  -- Safeguard against infinite recursion
    )
    SELECT claim_no INTO primary_claim
    FROM claim_hierarchy
    WHERE parent_claim_no IS NULL
    LIMIT 1;
    
    RETURN primary_claim;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_last_claim_response(p_claim_no TEXT) 
RETURNS TABLE (
  response_id integer,
  response_date text,
  transaction_response_status text,
  ing_cst_paid numeric,
  amt_ing_cost_contracted numeric,
  total_paid numeric,
  incv_amt_paid numeric,
  prof_svc_paid numeric,
  disp_fee_paid numeric,
  amt_disp_fee_contracted numeric,
  disp_fee_basis text,
  rem_basis text,
  amt_coverage_gap numeric,
  pro_fee_amt numeric,
  pt_pay_amt numeric,
  opayer_amt_rec numeric,
  est_gen_savings numeric,
  amt_attr_prod_sel numeric,
  amt_attr_prov_sel numeric,
  amt_attr_brd_nonformulary numeric,
  amt_attr_nonformulary numeric,
  amt_exd_ben_max numeric,
  spd_acct_remaining numeric,
  rem_ben_amt numeric,
  hlth_pln_asst numeric,
  copay_basis text,
  amt_copay numeric,
  amt_apld_ded numeric,
  rem_ded_amt numeric,
  acc_ded_amt numeric,
  coinsur_basis text,
  coinsur_amt numeric,
  reject_codes reject_code_type[],
  oth_amt_paid oth_amt_type[],
  bft_amt bft_amt_type[]
) AS $$
BEGIN
    RETURN QUERY
    WITH last_response AS (
    SELECT 
        res.id,
        res.created_on,
        res.claim_no
      FROM form_ncpdp_response res
      WHERE res.claim_no = p_claim_no 
      AND res.archived IS NOT TRUE 
      AND res.deleted IS NOT TRUE
      ORDER BY res.id DESC 
      LIMIT 1
    ),
    reject_codes_rows AS (
      SELECT
        ecl.code::text AS reject_code,
        COALESCE(trk.rank, 1)::integer AS reject_rank
      FROM last_response resp
      INNER JOIN sf_form_ncpdp_response_to_ncpdp_response_stat sfrstat ON sfrstat.form_ncpdp_response_fk = resp.id AND sfrstat.archive IS NOT TRUE AND sfrstat.delete IS NOT TRUE 
      INNER JOIN form_ncpdp_response_stat rstat ON rstat.id = sfrstat.form_ncpdp_response_stat_fk AND rstat.archived IS NOT TRUE AND rstat.deleted IS NOT TRUE 
      LEFT JOIN sf_form_ncpdp_response_stat_to_ncpdp_response_stat_rj sfrj ON sfrj.form_ncpdp_response_stat_fk = rstat.id AND sfrj.archive IS NOT TRUE AND sfrj.delete IS NOT TRUE 
      LEFT JOIN form_ncpdp_response_stat_rj rj ON rj.id = sfrj.form_ncpdp_response_stat_rj_fk AND rj.archived IS NOT TRUE AND rj.deleted IS NOT TRUE
      LEFT JOIN gr_form_ncpdp_response_stat_rj_reject_code_to_list_ncpdp_ecl_id grrj ON grrj.form_ncpdp_response_stat_rj_fk = rj.id
      LEFT JOIN form_list_ncpdp_ecl ecl ON ecl.code = grrj.form_list_ncpdp_ecl_fk AND ecl.field = '511-FB' AND ecl.archived IS NOT TRUE AND ecl.deleted IS NOT TRUE
      LEFT JOIN form_list_ncpdp_resp_cob_trk trk ON trk.code = ecl.code AND trk.archived IS NOT TRUE AND trk.deleted IS NOT TRUE AND trk.active = 'Yes'
    ),
    reject_codes_cte AS (
      SELECT
        ARRAY_AGG(rejr::reject_code_type) AS reject_codes
      FROM reject_codes_rows rejr
    ),
    oth_amt_paid_rows AS (
      SELECT 
        rprc_oth.oth_amt_qualifier::text AS other_amt_qualifier,
        rprc_oth.oth_amt_paid::numeric AS other_amt_paid
      FROM last_response resp
      INNER JOIN sf_form_ncpdp_response_to_ncpdp_response_prc sfrprc ON sfrprc.form_ncpdp_response_fk = resp.id AND sfrprc.archive IS NOT TRUE AND sfrprc.delete IS NOT TRUE 
      INNER JOIN form_ncpdp_response_prc rprc ON rprc.id = sfrprc.form_ncpdp_response_prc_fk AND rprc.archived IS NOT TRUE AND rprc.deleted IS NOT TRUE 
      LEFT JOIN sf_form_ncpdp_response_prc_to_ncpdp_response_prc_oth sfroth ON sfroth.form_ncpdp_response_prc_fk = rprc.id AND sfroth.archive IS NOT TRUE AND sfroth.delete IS NOT TRUE 
      LEFT JOIN form_ncpdp_response_prc_oth rprc_oth ON rprc_oth.id = sfroth.form_ncpdp_response_prc_oth_fk AND rprc_oth.archived IS NOT TRUE AND rprc_oth.deleted IS NOT TRUE 
    ),
    oth_amt_paid_cte AS (
      SELECT 
        ARRAY_AGG(othpd::oth_amt_type) AS oth_amt_paid
      FROM oth_amt_paid_rows othpd
    ),
    bft_amt_rows AS (
      SELECT 
        rprc_bft.benefit_stage_qualifier::text AS benefit_stage_qualifier,
        rprc_bft.benefit_stage_amount::numeric AS benefit_stage_amount
      FROM last_response resp
      INNER JOIN sf_form_ncpdp_response_to_ncpdp_response_prc sfrprc ON sfrprc.form_ncpdp_response_fk = resp.id AND sfrprc.archive IS NOT TRUE AND sfrprc.delete IS NOT TRUE 
      INNER JOIN form_ncpdp_response_prc rprc ON rprc.id = sfrprc.form_ncpdp_response_prc_fk AND rprc.archived IS NOT TRUE AND rprc.deleted IS NOT TRUE 
      LEFT JOIN sf_form_ncpdp_response_prc_to_ncpdp_response_prc_bft sfrprc_bft ON sfrprc_bft.form_ncpdp_response_prc_fk = rprc.id AND sfrprc_bft.archive IS NOT TRUE AND sfrprc_bft.delete IS NOT TRUE 
      LEFT JOIN form_ncpdp_response_prc_bft rprc_bft ON rprc_bft.id = sfrprc_bft.form_ncpdp_response_prc_bft_fk AND rprc_bft.archived IS NOT TRUE AND rprc_bft.deleted IS NOT TRUE 
    ),
    bft_amt_cte AS (
      SELECT 
        ARRAY_AGG(bftrow::bft_amt_type) AS bft_amt
      FROM bft_amt_rows bftrow
    )
    SELECT
      resp.id as response_id,
      TO_CHAR(get_site_timestamp(ncpdp.site_id, TRUE, resp.created_on), 'MM/DD/YYYY') as response_date,
      rstat.transaction_response_status::text as transaction_response_status,
      rprc.ing_cst_paid::numeric as ing_cst_paid,
      rprc.amt_ing_cost_contracted::numeric as amt_ing_cost_contracted,
      rprc.total_paid::numeric as total_paid,
      rprc.incv_amt_paid::numeric as incv_amt_paid,
      rprc.prof_svc_paid::numeric as prof_svc_paid,
      rprc.disp_fee_paid::numeric as disp_fee_paid,
      rprc.amt_disp_fee_contracted::numeric as amt_disp_fee_contracted,
      rprc.disp_fee_basis::text as disp_fee_basis,
      rprc.rem_basis::text as rem_basis,
      rprc.amt_coverage_gap::numeric as amt_coverage_gap,
      rprc.pro_fee_amt::numeric as pro_fee_amt,
      rprc.pt_pay_amt::numeric as pt_pay_amt,
      rprc.opayer_amt_rec::numeric as opayer_amt_rec,
      rprc.est_gen_savings::numeric as est_gen_savings,
      rprc.amt_attr_prod_sel::numeric as amt_attr_prod_sel,
      rprc.amt_attr_prov_sel::numeric as amt_attr_prov_sel,
      rprc.amt_attr_brd_nonformulary::numeric as amt_attr_brd_nonformulary,
      rprc.amt_attr_nonformulary::numeric as amt_attr_nonformulary,
      rprc.amt_exd_ben_max::numeric as amt_exd_ben_max,
      rprc.spd_acct_remaining::numeric as spd_acct_remaining,
      rprc.rem_ben_amt::numeric as rem_ben_amt,
      rprc.hlth_pln_asst::numeric as hlth_pln_asst,
      rprc.copay_basis::text as copay_basis,
      rprc.amt_copay::numeric as amt_copay,
      rprc.amt_apld_ded::numeric as amt_apld_ded,
      rprc.rem_ded_amt::numeric as rem_ded_amt,
      rprc.acc_ded_amt::numeric as acc_ded_amt,
      rprc.coinsur_basis::text as coinsur_basis,
      rprc.coinsur_amt::numeric as coinsur_amt,
      COALESCE(rej.reject_codes, NULL::reject_code_type[]) as reject_codes,
      COALESCE(oth.oth_amt_paid, NULL::oth_amt_type[]) as oth_amt_paid,
      COALESCE(bft.bft_amt, NULL::bft_amt_type[]) as bft_amt
    FROM last_response resp
    INNER JOIN form_ncpdp ncpdp ON ncpdp.claim_no = resp.claim_no AND ncpdp.archived IS NOT TRUE AND ncpdp.deleted IS NOT TRUE
    LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_prc sfrprc ON sfrprc.form_ncpdp_response_fk = resp.id AND sfrprc.archive IS NOT TRUE AND sfrprc.delete IS NOT TRUE 
    LEFT JOIN form_ncpdp_response_prc rprc ON rprc.id = sfrprc.form_ncpdp_response_prc_fk AND rprc.archived IS NOT TRUE AND rprc.deleted IS NOT TRUE 
    LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_stat sfrstat ON sfrstat.form_ncpdp_response_fk = resp.id AND sfrstat.archive IS NOT TRUE AND sfrstat.delete IS NOT TRUE 
    LEFT JOIN form_ncpdp_response_stat rstat ON rstat.id = sfrstat.form_ncpdp_response_stat_fk AND rstat.archived IS NOT TRUE AND rstat.deleted IS NOT TRUE 
    LEFT JOIN reject_codes_cte rej ON true
    LEFT JOIN oth_amt_paid_cte oth ON true
    LEFT JOIN bft_amt_cte bft ON true;
END;
$$ LANGUAGE plpgsql STABLE;

CREATE OR REPLACE FUNCTION get_primary_ncpdp_claim_info(p_claim_no TEXT) 
RETURNS TABLE (
  response_id integer,
  primary_claim_id integer,
  transaction_response_status text,
  ing_cst_paid numeric,
  pt_pay_amt numeric,
  ing_cst_sub numeric,
  gross_amount_due numeric,
  u_and_c_charge numeric,
  disp_fee_sub numeric,
  cost_basis text,
  subform_oclaim ncpdp_opaid[]
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    lres.response_id::integer as response_id,
    pclaim.id::integer as primary_claim_id,
    lres.transaction_response_status::text as transaction_response_status,
    lres.ing_cst_paid::numeric as ing_cst_paid,
    lres.pt_pay_amt::numeric as pt_pay_amt,
    prc.ing_cst_sub::numeric as ing_cst_sub,
    prc.gross_amount_due::numeric as gross_amount_due,
    prc.u_and_c_charge::numeric as u_and_c_charge,
    prc.disp_fee_sub::numeric as disp_fee_sub,
    prc.cost_basis::text as cost_basis,
    oamts.other_payers::ncpdp_opaid[] as subform_oclaim
  FROM form_ncpdp pclaim
  INNER JOIN sf_form_ncpdp_to_ncpdp_pricing sfprc ON sfprc.form_ncpdp_fk = pclaim.id AND sfprc.delete IS NOT TRUE AND sfprc.archive IS NOT TRUE 
  INNER JOIN form_ncpdp_pricing prc ON prc.id = sfprc.form_ncpdp_pricing_fk AND prc.archived IS NOT TRUE AND prc.deleted IS NOT TRUE
  LEFT JOIN get_last_claim_response(pclaim.claim_no) lres ON true
  LEFT JOIN LATERAL (
      WITH other_payers AS (
        SELECT osub.o_amt_sub_qualifier::text as o_amt_sub_qualifier,
        osub.o_amt_sub::numeric as o_sub_sub
        FROM sf_form_ncpdp_pricing_to_ncpdp_pricing_osub sfosub
        INNER JOIN form_ncpdp_pricing_osub osub ON osub.id = sfosub.form_ncpdp_pricing_osub_fk AND osub.deleted IS NOT TRUE AND osub.archived IS NOT TRUE
        WHERE sfosub.form_ncpdp_pricing_fk = prc.id AND sfosub.delete IS NOT TRUE AND sfosub.archive IS NOT TRUE
      )
      SELECT ARRAY_AGG(other_payers::ncpdp_opaid) as other_payers
      FROM other_payers
  ) oamts ON true
  WHERE p_claim_no IS NOT NULL AND pclaim.claim_no = get_primary_claim(p_claim_no) AND COALESCE(pclaim.is_test, 'No') <> 'Yes' AND pclaim.archived IS NOT TRUE AND pclaim.deleted IS NOT TRUE AND COALESCE(pclaim.void, 'No') <> 'Yes'
  AND pclaim.status IN ('Pending', 'Payable', 'Captured');
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION calc_rental_charge_quantity(
    frequency_code text,
    service_from date,
    service_to date
) RETURNS TABLE (
    charge_quantity integer
) AS $$
DECLARE
    v_service_days integer;
BEGIN
    SELECT (COALESCE(service_to, CURRENT_DATE + INTERVAL '1 day' * 28) - COALESCE(service_from, CURRENT_DATE) + 1)::integer INTO v_service_days;
        -- Return the results
        RETURN QUERY 
        SELECT CASE
          WHEN frequency_code = '1' THEN CEIL(v_service_days / 7)::integer
          WHEN frequency_code = '2' THEN CEIL(v_service_days / 28)::integer
          WHEN frequency_code = '3' THEN v_service_days::integer
          ELSE NULL::integer
        END as charge_quantity;
END;
$$ LANGUAGE plpgsql STABLE;

CREATE OR REPLACE FUNCTION get_charged_units_by_payer(
    p_patient_id INTEGER,
    p_payer_id INTEGER,
    p_inventory_id INTEGER,
    p_start_date_period text
) RETURNS INTEGER AS $$
BEGIN
    IF p_patient_id IS NULL OR p_payer_id IS NULL OR p_inventory_id IS NULL OR p_start_date_period IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Required parameters cannot be null',
            'Validating parameters in get_charged_units_by_payer',
            'FUNCTION',
            current_schema(),
            'form_payer',
            jsonb_build_object(
                'function_name', 'get_charged_units_by_payer',
                'patient_id', p_patient_id,
                'payer_id', p_payer_id,
                'inventory_id', p_inventory_id,
                'start_date_period', p_start_date_period
            )
        );
        RAISE EXCEPTION 'Required parameters cannot be null: patient_id=%, payer_id=%, inventory_id=%, start_date_period=%', 
            p_patient_id, p_payer_id, p_inventory_id, p_start_date_period;
    END IF;
    RETURN (
        WITH ChargeData AS (
            SELECT SUM(lgl.bill_quantity) as bill_quantity
            FROM form_ledger_charge_line lgl
            INNER JOIN form_billing_invoice bi ON bi.invoice_no = lgl.invoice_no 
            AND bi.archived IS NOT TRUE
            AND bi.deleted IS NOT TRUE
            AND bi.status NOT IN ('Voided', '$0')
            AND COALESCE(bi.void,'No') <> 'Yes'
            AND COALESCE(bi.zeroed, 'No') <> 'Yes'
            WHERE lgl.payer_id = p_payer_id
            AND lgl.archived IS NOT TRUE
            AND COALESCE(lgl.void,'No') <> 'Yes'
            AND lgl.payer_id = p_payer_id
            AND lgl.patient_id = p_patient_id
            AND lgl.inventory_id = p_inventory_id
            AND bi.date_of_service >= TO_DATE(p_start_date_period, 'MM/DD/YYYY')
        )
        SELECT cd.bill_quantity as bill_quantity
        FROM ChargeData cd
        LIMIT 1
    );

END;
$$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION get_existing_charge_lines_pre_confirmation(
    p_delivery_ticket_id integer
) RETURNS TABLE (
    charge_line_id integer,
    inventory_id integer,
    insurance_id integer,
    payer_id integer,
    bill_quantity numeric,
    expected numeric,
    copay numeric,
    master_charge_no text,
    parent_charge_no text,
    charge_no text,
    invoice_no text,
    invoice_id integer,
    remaining_balance numeric,
    paid_amount numeric,
    rx_id integer,
    inventory_type text,
    patient_id integer
) AS $$
BEGIN
    RETURN QUERY
    -- Get charge lines with balances and payments
    SELECT 
        lcl.id as charge_line_id,
        lcl.inventory_id::integer as inventory_id,
        lcl.insurance_id::integer as insurance_id,
        lcl.payer_id::integer as payer_id,
        lcl.bill_quantity::numeric as bill_quantity,
        lcl.expected::numeric as expected,
        lcl.copay::numeric as copay,
        lcl.master_charge_no::text as master_charge_no,
        lcl.parent_charge_no::text as parent_charge_no,
        lcl.charge_no::text as charge_no,
        lcl.invoice_no::text as invoice_no,
        bi.id::integer as invoice_id,
        clparb.pending_ar_balance::numeric as remaining_balance,
        clparb.applied_unapplied_cash::numeric as paid_amount,
        lcl.order_rx_id::integer as rx_id,
        lcl.inventory_type::text as inventory_type,
        lcl.patient_id::integer as patient
    FROM form_ledger_charge_line lcl
    INNER JOIN vw_charge_line_pending_ar_balance clparb ON clparb.charge_no = lcl.charge_no
    INNER JOIN form_careplan_delivery_tick dt ON dt.id = p_delivery_ticket_id 
    AND dt.archived IS NOT TRUE AND dt.deleted IS NOT TRUE
    AND COALESCE(dt.void, 'No') <> 'Yes'
    JOIN LATERAL (
        SELECT DISTINCT ON (dti.rx_id)
            dti.rx_id
        FROM form_careplan_dt_item dti 
        WHERE dti.ticket_no = dt.ticket_no
        AND dti.archived IS NOT TRUE
        AND dti.deleted IS NOT TRUE
        AND dti.inventory_id = lcl.inventory_id
        AND dti.rx_id = lcl.order_rx_id
    ) dti ON true
    INNER JOIN form_billing_invoice bi ON bi.invoice_no = lcl.invoice_no
    WHERE lcl.archived IS NOT TRUE
    AND lcl.deleted IS NOT TRUE
    AND COALESCE(lcl.void, 'No') <> 'Yes'
    AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
    AND bi.archived IS NOT TRUE
    AND bi.deleted IS NOT TRUE
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes'
    AND lcl.ticket_no IS NULL
    AND lcl.ticket_item_no IS NULL
    AND lcl.order_rx_id = dti.rx_id
    -- Order by master charge, then parent charge to ensure proper COB chain
    ORDER BY 
        COALESCE(lcl.master_charge_no, lcl.charge_no),
        COALESCE(lcl.parent_charge_no, lcl.charge_no),
        lcl.charge_no;
END;
$$ LANGUAGE plpgsql STABLE;