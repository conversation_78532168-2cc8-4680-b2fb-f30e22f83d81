DO $$ BEGIN
  PERFORM drop_all_function_signatures('link_supply_orders_to_rx');
END $$;
CREATE OR REPLACE FUNCTION link_supply_orders_to_rx()
RETURNS TRIGGER AS $$
BEGIN
    BEGIN
        -- Check if NEW.id and NEW.rx_no are not null
        IF NEW.id IS NULL OR NEW.rx_no IS NULL THEN
            RAISE EXCEPTION 'New RX ID or RX Number cannot be null';
        END IF;

        -- Update form_careplan_orders_item to link supply orders to the new RX
        UPDATE form_careplan_orders_item
        SET associated_rx_id = NEW.id,
            rx_no = NEW.rx_no
        WHERE patient_id = NEW.patient_id
        AND status_id = '1'
        AND associated_rx_id IS NULL;
        
    EXCEPTION WHEN OTHERS THEN
        -- Raise an error to be caught by the calling application
        RAISE EXCEPTION 'Error linking supply orders to RX: %', SQLERRM;
    END;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger on form_careplan_order_rx insert
CREATE TRIGGER trigger_link_supply_orders_to_rx
AFTER INSERT ON form_careplan_order_rx
FOR EACH ROW
EXECUTE FUNCTION link_supply_orders_to_rx();
