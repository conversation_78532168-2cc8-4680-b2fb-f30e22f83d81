
DO $$ BEGIN
  PERFORM drop_all_function_signatures('build_ncpdp_response_summary');
END $$;
CREATE OR REPLACE FUNCTION build_ncpdp_response_summary(v_response_id int, v_created_on timestamp, v_created_by int, v_site_id int) RETURNS VOID AS $$
DECLARE
    p_uuid TEXT;
BEGIN

    RAISE LOG 'build_ncpdp_response_summary Response ID %', v_response_id;

    IF v_response_id IS NULL THEN
        -- Log the error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'response_id cannot be null',
            'Validation in build_ncpdp_response_summary function',
            'FUNCTION',
            current_schema(),
            'form_ncpdp_response',
            jsonb_build_object(
                'response_id', v_response_id
            )
        );
        RAISE EXCEPTION 'response_id cannot be null';
    END IF;

    p_uuid := gen_random_uuid();
    INSERT INTO form_ncpdp_response_summary (
        created_on,
        created_by,
        response_uuid,
        response_id,
        claim_no,
        invoice_no,
        patient_id,
        inventory_id,
        site_id,
        svc_prov_id,
        rx_no,
        date_of_service,
        day_supply,
        total_cost,
        reimbursement,
        total_profit,
        margin,
        total_billed,
        total_paid,
        total_discount,
        total_paid_percentage,
        formatted_ndc,
        quantity_dispensed,
        response_datetime,
        claim_status,
        group_id,
        network_reimbursement_id,
        patient_first_name,
        patient_last_name,
        patient_date_of_birth,
        transaction_response_status,
        authorization_number,
        rx_svc_no_ref_qualifier,
        rx_svc_no,
        transaction_ref_no,
        message,
        pt_pay_amt,
        ing_cst_paid,
        disp_fee_paid,
        total_paid_amount,
        total_awp,
        total_awp_percentage,
        request_json_data,
        response_json_data,
        request_d0_raw,
        response_d0_raw,
        archived,
        deleted
    )
    SELECT
        v_created_on,
        v_created_by,
        p_uuid,
        v_response_id,
        resp.claim_no,
        resp.invoice_no,
        resp.patient_id,
        resp.inventory_id,
        v_site_id,
        resp.svc_prov_id,
        resp.rx_no,
        resp.date_of_service,
        resp.day_supply,
        resp.total_cost,
        resp.reimbursement,
        resp.total_profit,
        resp.margin,
        resp.total_billed,
        resp.total_paid,
        resp.total_discount,
        resp.total_paid_percentage,
        resp.formatted_ndc,
        resp.quantity_dispensed,
        get_site_timestamp(v_site_id, FALSE),
        resp.claim_status,
        resp.group_id,
        resp.network_reimbursement_id,
        resp.patient_first_name,
        resp.patient_last_name,
        resp.patient_date_of_birth,
        resp.transaction_response_status,
        resp.authorization_number,
        resp.rx_svc_no_ref_qualifier,
        resp.rx_svc_no,
        resp.transaction_ref_no,
        resp.message,
        resp.pt_pay_amt,
        resp.ing_cst_paid,
        resp.disp_fee_paid,
        resp.total_paid_amount,
        resp.total_awp,
        resp.total_awp_percentage,
        resp.request_json_data,
        resp.response_json_data,
        resp.request_d0_raw,
        resp.response_d0_raw,
        FALSE,
        FALSE
    FROM vw_ncpdp_response_summary resp
    WHERE resp.response_id = v_response_id;

    INSERT INTO form_ncpdp_response_summary_li (
        created_on,
        created_by,
        response_uuid,
        type,
        billed_amount,
        paid_amount,
        discount_amount,
        paid_percentage,
        archived,
        deleted
    )
    SELECT
        v_created_on,
        v_created_by,
        p_uuid,
        type,
        billed_amount,
        paid_amount,
        discount_amount,
        paid_percentage,
        FALSE,
        FALSE
    FROM vw_ncpdp_response_summary_lines resp
    WHERE resp.response_id = v_response_id;

    INSERT INTO form_ncpdp_response_summary_msg (
        created_on,
        created_by,
        response_uuid,
        add_msg,
        add_msg_qualifier,
        archived,
        deleted
    )
    SELECT
        v_created_on,
        v_created_by,
        p_uuid,
        msg.add_msg,
        msg.add_msg_qualifier,
        FALSE,
        FALSE
        FROM vw_ncpdp_response_summary_msg msg
        WHERE msg.response_id = v_response_id;

    INSERT INTO form_ncpdp_response_summary_rejmsg (
        created_on,
        created_by,
        response_uuid,
        reject_code,
        archived,
        deleted
    )
    SELECT
        v_created_on,
            v_created_by,
            p_uuid,
            rej.reject_code,
            FALSE,
            FALSE
        FROM vw_ncpdp_response_summary_rejmsg rej
        WHERE rej.response_id = v_response_id;

    -- If any of the inserts fail, the entire transaction will be rolled back
    EXCEPTION WHEN OTHERS THEN
        -- Log the error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in build_ncpdp_response_summary function',
            'FUNCTION',
            current_schema(),
            'form_ncpdp_response',
            jsonb_build_object(
                'response_id', v_response_id
            )
        );

        RAISE NOTICE 'Error creating ncpdp response summary: %', SQLERRM;
        RAISE;
    END;

$$ LANGUAGE plpgsql VOLATILE;


DO $$ BEGIN
  PERFORM drop_all_function_signatures('build_ncpdp_response_elig_summary');
END $$;
CREATE OR REPLACE FUNCTION build_ncpdp_response_elig_summary(v_response_id int, v_created_on timestamp, v_created_by int, v_site_id int) RETURNS VOID AS $$
DECLARE
    p_uuid TEXT;
BEGIN

    RAISE LOG 'build_ncpdp_response_elig_summary Response ID %', v_response_id;

    IF v_response_id IS NULL THEN
        -- Log the error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'response_id cannot be null',
            'Validation in build_ncpdp_response_elig_summary function',
            'FUNCTION',
            current_schema(),
            'form_ncpdp_response',
            jsonb_build_object(
                'response_id', v_response_id
            )
        );
        RAISE EXCEPTION 'response_id cannot be null';
    END IF;

    p_uuid := gen_random_uuid();
    INSERT INTO form_ncpdp_response_elig_summary (
        created_on,
        created_by,
        response_uuid,
        response_id,
        claim_no,
        patient_id,
        svc_prov_id,
        response_datetime,
        claim_status,
        group_id,
        plan_id,
        card_holder_id,
        mcd_id_no,
        mcd_agcy_no,
        network_reimbursement_id,
        medi_part_d_cov_code,
        cms_low_income_sharing,
        contract_number,
        formulary_id,
        benefit_id,
        medi_part_d_eff_date,
        medi_part_d_trm_date,
        patient_first_name,
        patient_last_name,
        patient_date_of_birth,
        transaction_response_status,
        authorization_number,
        message,
        request_json_data,
        response_json_data,
        request_d0_raw,
        response_d0_raw,
        archived,
        deleted
    )
    SELECT
        v_created_on,
        v_created_by,
        p_uuid,
        v_response_id,
        resp.claim_no,
        resp.patient_id,
        resp.svc_prov_id,
        get_site_timestamp(v_site_id, FALSE),
        resp.claim_status,
        resp.group_id,
        resp.plan_id,
        resp.card_holder_id,
        resp.mcd_id_no,
        resp.mcd_agcy_no,
        resp.network_reimbursement_id,
        resp.medi_part_d_cov_code,
        resp.cms_low_income_sharing,
        resp.contract_number,
        resp.formulary_id,
        resp.benefit_id,
        resp.medi_part_d_eff_date,
        resp.medi_part_d_trm_date,
        resp.patient_first_name,
        resp.patient_last_name,
        resp.patient_date_of_birth,
        resp.transaction_response_status,
        resp.authorization_number,
        resp.message,
        resp.request_json_data,
        resp.response_json_data,
        resp.request_d0_raw,
        resp.response_d0_raw,
        FALSE,
        FALSE
    FROM vw_ncpdp_response_elig_summary resp
    WHERE resp.response_id = v_response_id;

    INSERT INTO form_ncpdp_response_summary_msg (
        created_on,
        created_by,
        response_uuid,
        add_msg,
        add_msg_qualifier,
        archived,
        deleted
    )
    SELECT
        v_created_on,
        v_created_by,
        p_uuid,
        msg.add_msg,
        msg.add_msg_qualifier,
        FALSE,
        FALSE
        FROM vw_ncpdp_response_summary_msg msg
        WHERE msg.response_id = v_response_id;

    INSERT INTO form_ncpdp_response_summary_rejmsg (
        created_on,
        created_by,
        response_uuid,
        reject_code,
        archived,
        deleted
    )
    SELECT
        v_created_on,
            v_created_by,
            p_uuid,
            rej.reject_code,
            FALSE,
            FALSE
        FROM vw_ncpdp_response_summary_rejmsg rej
        WHERE rej.response_id = v_response_id;

    -- If any of the inserts fail, the entire transaction will be rolled back
    EXCEPTION WHEN OTHERS THEN
        -- Log the error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in build_ncpdp_response_elig_summary function',
            'FUNCTION',
            current_schema(),
            'form_ncpdp_response',
            jsonb_build_object(
                'response_id', v_response_id
            )
        );

        RAISE NOTICE 'Error creating ncpdp response elig summary: %', SQLERRM;
        RAISE;
    END;

$$ LANGUAGE plpgsql VOLATILE;


DO $$ BEGIN
  PERFORM drop_all_function_signatures('build_ncpdp_response_cf_summary');
END $$;
CREATE OR REPLACE FUNCTION build_ncpdp_response_cf_summary(v_response_id int, v_created_on timestamp, v_created_by int, v_site_id int) RETURNS VOID AS $$
DECLARE
    p_uuid TEXT;
BEGIN

    RAISE LOG 'build_ncpdp_response_cf_summary Response ID %', v_response_id;

    IF v_response_id IS NULL THEN
        -- Log the error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'response_id cannot be null',
            'Validation in build_ncpdp_response_cf_summary function',
            'FUNCTION',
            current_schema(),
            'form_ncpdp_response',
            jsonb_build_object(
                'response_id', v_response_id
            )
        );
        RAISE EXCEPTION 'response_id cannot be null';
    END IF;

    p_uuid := gen_random_uuid();
    INSERT INTO form_ncpdp_response_cf_summary (
        created_on,
        created_by,
        response_uuid,
        response_id,
        patient_id,
        response_datetime,
        response_status,
        patient_first_name,
        patient_last_name,
        patient_date_of_birth,
        transaction_response_status,
        message,
        request_json_data,
        response_json_data,
        request_d0_raw,
        response_d0_raw,
        archived,
        deleted
    )
    SELECT
        v_created_on,
        v_created_by,
        p_uuid,
        v_response_id,
        resp.patient_id,
        get_site_timestamp(v_site_id, FALSE),
        resp.response_status,
        resp.patient_first_name,
        resp.patient_last_name,
        resp.patient_date_of_birth,
        resp.transaction_response_status,
        resp.message,
        resp.request_json_data,
        resp.response_json_data,
        resp.request_d0_raw,
        resp.response_d0_raw,
        FALSE,
        FALSE
    FROM vw_ncpdp_response_cf_summary resp
    WHERE resp.response_id = v_response_id;

    INSERT INTO form_ncpdp_response_cf_payer (
        created_on,
        created_by,
        response_uuid,
        insurance_id,
        patient_id,
        payer_id,
        other_coverage_type,
        other_id_qualifier,
        other_id,
        other_pcn,
        other_group_no,
        other_person_code,
        other_help_phone,
        other_rel_code,
        ben_eff_date,
        ben_trm_date,
        other_cardholder_id,
        archived,
        deleted
    )
    SELECT
        v_created_on,
        v_created_by,
        p_uuid,
        resp.insurance_id,
        resp.patient_id,
        resp.payer_id,
        resp.other_coverage_type,
        resp.other_id_qualifier,
        resp.other_id,
        resp.other_pcn,
        resp.other_group_no,
        resp.other_person_code,
        resp.other_help_phone,
        resp.other_rel_code,
        resp.ben_eff_date,
        resp.ben_trm_date,
        resp.other_cardholder_id,
        FALSE,
        FALSE
    FROM vw_ncpdp_response_cf_payer resp
    WHERE resp.response_id = v_response_id;

    INSERT INTO form_ncpdp_response_summary_msg (
        created_on,
        created_by,
        response_uuid,
        add_msg,
        add_msg_qualifier,
        archived,
        deleted
    )
    SELECT
        v_created_on,
        v_created_by,
        p_uuid,
        msg.add_msg,
        msg.add_msg_qualifier,
        FALSE,
        FALSE
        FROM vw_ncpdp_response_summary_msg msg
        WHERE msg.response_id = v_response_id;

    INSERT INTO form_ncpdp_response_summary_rejmsg (
        created_on,
        created_by,
        response_uuid,
        reject_code,
        archived,
        deleted
    )
    SELECT
        v_created_on,
            v_created_by,
            p_uuid,
            rej.reject_code,
            FALSE,
            FALSE
        FROM vw_ncpdp_response_summary_rejmsg rej
        WHERE rej.response_id = v_response_id;

    -- If any of the inserts fail, the entire transaction will be rolled back
    EXCEPTION WHEN OTHERS THEN
        -- Log the error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in build_ncpdp_response_cf_summary function',
            'FUNCTION',
            current_schema(),
            'form_ncpdp_response',
            jsonb_build_object(
                'response_id', v_response_id
            )
        );

        RAISE NOTICE 'Error creating ncpdp response cf summary: %', SQLERRM;
        RAISE;
    END;

$$ LANGUAGE plpgsql VOLATILE;

DO $$ BEGIN
  PERFORM drop_all_function_signatures('process_ncpdp_claim_response');
END $$;
CREATE OR REPLACE FUNCTION process_ncpdp_claim_response()
RETURNS TRIGGER AS $$
DECLARE
    v_adjustment_amount NUMERIC := 0;
    v_invoice_no TEXT;
    v_bill_for_denial BOOLEAN := FALSE;
    v_total_cost NUMERIC;
    v_total_expected NUMERIC;
    v_updated_expected_amount NUMERIC;
    v_total_paid NUMERIC;
    v_total_pt_pay NUMERIC;
    v_total_allocated_cash NUMERIC;
    v_claim_status TEXT;
    v_claim_substatus_id TEXT;
    v_bin_number TEXT;
    v_is_test BOOLEAN;
    v_add_auto_adjust_expected BOOLEAN := FALSE;
    v_is_specialty BOOLEAN;
    v_working_ticket_id INTEGER;
    v_ncpdp_id INTEGER;
    v_is_open_claim BOOLEAN;
    v_requires_auth BOOLEAN := FALSE;
    v_company_auto_cash_posting BOOLEAN := FALSE;
    v_revenue_accepted_posted BOOLEAN := FALSE;
    v_charges_editable BOOLEAN := FALSE;
    v_patient_id INTEGER;
    v_payer_id INTEGER;
    v_insurance_id INTEGER;
    v_site_id INTEGER;
    v_self_insurance_id INTEGER;
    v_copay_insurance_id INTEGER;
    v_copay_payer_id INTEGER;
    v_total_unallocated_cash NUMERIC;
    v_total_balance_due NUMERIC;
    v_total_allocated_cash_used NUMERIC;
    v_confirmed_revenue BOOLEAN := FALSE;
BEGIN

    RAISE LOG 'process_ncpdp_claim_response Response ID %', NEW.ID;
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    IF NEW.claim_no IS NULL THEN
        -- Log the error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'claim_no cannot be null',
            'Validation in process_claim_response trigger',
            'TRIGGER',
            current_schema(),
            'form_ncpdp_response',
            jsonb_build_object(
                'claim_no', NEW.claim_no
            )
        );
        RETURN NEW;
        RAISE EXCEPTION 'claim_no cannot be null';
    END IF;

    -- Check if auto cash posting is enabled
    SELECT 
        CASE WHEN ncpdp_cash_posting = 'Yes' THEN TRUE ELSE FALSE END 
    INTO v_company_auto_cash_posting
    FROM form_company 
    WHERE id = 1;

    SELECT
        COALESCE(bi.total_cost, 0),
        COALESCE(bi.total_expected, 0),
        COALESCE(bi.total_paid, 0),
        bi.invoice_no,
        FALSE,
        COALESCE(bi.bill_for_denial, 'No') = 'Yes',
        CASE 
            WHEN bi.delivery_ticket_id IS NOT NULL THEN TRUE 
            ELSE FALSE 
        END
    INTO v_total_cost, v_total_expected, v_total_paid, v_invoice_no, v_revenue_accepted_posted, v_bill_for_denial, v_confirmed_revenue
    FROM form_billing_invoice bi
    INNER JOIN form_ncpdp ncpdp ON ncpdp.claim_no = NEW.claim_no
    INNER JOIN sf_form_billing_invoice_to_ncpdp sfbi 
        ON sfbi.form_ncpdp_fk = ncpdp.id AND sfbi.form_billing_invoice_fk = bi.id
        AND sfbi.archive IS NOT TRUE
        AND sfbi.delete IS NOT TRUE
    WHERE bi.archived IS NOT TRUE
    AND bi.deleted IS NOT TRUE
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes'
    AND COALESCE(bi.revenue_accepted_posted, 'No') = 'No';

    IF v_invoice_no IS NULL THEN
        SELECT
            COALESCE(bif.total_cost, 0),
            COALESCE(bif.total_expected, 0),
            COALESCE(bif.total_paid, 0),
            bi.invoice_no,
            TRUE,
            COALESCE(bi.bill_for_denial, 'No') = 'Yes',
            TRUE
        INTO v_total_cost, v_total_expected, v_total_paid, v_invoice_no, v_revenue_accepted_posted, v_bill_for_denial, v_confirmed_revenue
        FROM form_billing_invoice bi
        INNER JOIN form_ncpdp ncpdp ON ncpdp.claim_no = NEW.claim_no
        INNER JOIN sf_form_billing_invoice_to_ncpdp sfbi 
            ON sfbi.form_ncpdp_fk = ncpdp.id AND sfbi.form_billing_invoice_fk = bi.id
            AND sfbi.archive IS NOT TRUE
            AND sfbi.delete IS NOT TRUE
        INNER JOIN vw_invoice_finance_totals bif ON bif.invoice_id = bi.id
        WHERE bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
        AND COALESCE(bi.void, 'No') <> 'Yes'
        AND COALESCE(bi.zeroed, 'No') <> 'Yes'
        AND COALESCE(bi.revenue_accepted_posted, 'No') = 'Yes';
    END IF;

    IF v_invoice_no IS NULL THEN
        SELECT
            COALESCE(ncpdp.cost, 0),
            COALESCE(ncpdp.expected, 0),
            COALESCE(ncpdp.paid, 0),
            NULL::text,
            FALSE
        INTO v_total_cost, v_total_expected, v_total_paid, v_invoice_no, v_revenue_accepted_posted
        FROM form_ncpdp ncpdp
        WHERE ncpdp.archived IS NOT TRUE
        AND ncpdp.deleted IS NOT TRUE
        AND ncpdp.claim_no = NEW.claim_no
        AND COALESCE(ncpdp.is_test, 'No') = 'Yes';
    END IF;

    -- Get invoice_no, adjustment amount and payer threshold
    -- Also get master_invoice_no for COB scenarios
    SELECT DISTINCT
        claim.patient_id,
        claim.payer_id,
        claim.site_id,
        CASE
            WHEN NEW.transaction_code IN ('E1') THEN NULL::numeric
            WHEN stat.transaction_response_status = 'P' THEN -- Paid
                CASE
                    WHEN py.min_accept_margin IS NOT NULL THEN
                        CASE
                            WHEN (COALESCE(claim.cost::numeric, 0.0) + (COALESCE(claim.cost::numeric, 0.0) * (1 + py.min_accept_margin::numeric / 100))) < COALESCE(rprc.total_paid::numeric, 0.0) THEN v_total_expected::numeric
                            WHEN py.auto_adjust_expected = 'Yes' AND v_revenue_accepted_posted IS FALSE THEN COALESCE(rprc.total_paid::numeric, 0.0)::numeric
                            ELSE v_total_expected::numeric
                        END
                    ELSE v_total_expected::numeric
                END
            ELSE v_total_expected::numeric
        END,
        CASE
            WHEN NEW.transaction_code IN ('E1') THEN NULL::numeric
            WHEN msg.add_msg LIKE 'CLAIM NOT FOUND%' AND NEW.transaction_code IN ('B2', 'S2') THEN 0.0::numeric
            WHEN (NEW.response_status = 'R' OR stat.transaction_response_status = 'R') AND NEW.transaction_code IN ('B2', 'S2', 'B3', 'S3') THEN v_total_paid::numeric
            WHEN (NEW.response_status = 'R' OR stat.transaction_response_status = 'R') THEN 0.0::numeric
            WHEN stat.transaction_response_status IN ('Q', 'F') THEN 0.0::numeric -- Duplicate of Captured, PA Deferred
            ELSE COALESCE(rprc.total_paid::numeric, 0.0)::numeric
        END,
        CASE
            WHEN msg.add_msg LIKE 'CLAIM NOT FOUND%' AND NEW.transaction_code IN ('B2', 'S2') THEN 0.0::numeric
            WHEN stat.transaction_response_status = 'A' AND NEW.transaction_code IN ('B2', 'S2') THEN 0.0::numeric
            WHEN NEW.transaction_code IN ('E1') THEN NULL::numeric
            WHEN (NEW.response_status = 'R' OR stat.transaction_response_status = 'R') AND NEW.transaction_code IN ('B2', 'S2', 'B3', 'S3') THEN COALESCE(claim.copay::numeric, 0.0)::numeric
            WHEN (NEW.response_status = 'R' OR stat.transaction_response_status = 'R') THEN 0.0::numeric
            WHEN stat.transaction_response_status IN ('Q','F') THEN 0.0::numeric -- Duplicate of Captured,  PA Deferred
            ELSE COALESCE(rprc.pt_pay_amt::numeric, 0.0)::numeric
        END,
        CASE
            WHEN stat.transaction_response_status IN ('P', 'A', 'C') AND v_confirmed_revenue IS FALSE THEN '106' -- Unconfirmed
            WHEN stat.transaction_response_status IN ('P', 'A', 'C') AND claim.status IS NULL THEN '103' -- Sent
            WHEN stat.transaction_response_status IN ('F') AND claim.status IS NULL THEN '109' -- On hold for authorization
            WHEN NEW.transaction_code IN ('B2', 'S2') THEN '101' -- Ready (reversed)
            ELSE claim.substatus_id::text
        END,
        CASE
            WHEN msg.add_msg LIKE 'CLAIM NOT FOUND%' AND NEW.transaction_code IN ('B2', 'S2') THEN 'Reversed'::text
            WHEN (NEW.response_status = 'R' OR stat.transaction_response_status = 'R') AND NEW.transaction_code IN ('B2', 'S2') THEN 'Reversal Rejected'::text
            WHEN (NEW.response_status = 'R' OR stat.transaction_response_status = 'R') AND NEW.transaction_code IN ('B3', 'S3') THEN 'Rebill Rejected'::text
            WHEN (NEW.response_status = 'R' OR stat.transaction_response_status = 'R') THEN 'Rejected'::text
            WHEN stat.transaction_response_status = 'B' THEN 'Benefit'::text
            WHEN stat.transaction_response_status = 'A' AND NEW.transaction_code IN ('B2', 'S2') THEN 'Reversed'::text
            WHEN stat.transaction_response_status = 'A' THEN 'Approved'::text
            WHEN stat.transaction_response_status = 'C' THEN 'Captured'::text
            WHEN stat.transaction_response_status = 'F' THEN 'PA Deferred'::text
            WHEN stat.transaction_response_status IN ('Q','D','S') THEN 
                CASE WHEN NEW.transaction_code IN ('B2', 'S2') THEN 'Reversed'::text
                     ELSE 'Duplicate'::text
                END
            WHEN stat.transaction_response_status = 'P' THEN
                CASE
                    WHEN py.min_accept_margin IS NOT NULL THEN
                        CASE
                            WHEN (COALESCE(claim.cost::numeric, 0.0) + (COALESCE(claim.cost::numeric, 0.0) * (1 + py.min_accept_margin::numeric / 100))) < COALESCE(rprc.total_paid::numeric, 0.0) THEN 'Margin'::text
                            ELSE 'Payable'::text
                        END
                    ELSE 'Payable'::text
                END
            ELSE NULL::text
        END,
        CASE
            WHEN COALESCE(claim.is_test, 'No') = 'Yes' THEN TRUE::boolean
            ELSE FALSE::boolean
        END,
        CASE
            WHEN COALESCE(rx.is_specialty, 'No') = 'Yes' THEN TRUE::boolean
            ELSE FALSE::boolean
        END,
        rx.working_dispense_id,
        claim.id,
        CASE
            WHEN stat.transaction_response_status IN ('C', 'F') THEN TRUE::boolean
            ELSE FALSE::boolean
        END,
        CASE WHEN stat.transaction_response_status = 'F' THEN TRUE::boolean ELSE FALSE::boolean END,
        CASE WHEN COALESCE(py.auto_adjust_expected, 'No') = 'Yes' THEN TRUE::boolean ELSE FALSE::boolean END,
        claim.bin_number
    INTO
        v_patient_id,
        v_payer_id,
        v_site_id,
        v_updated_expected_amount,
        v_total_paid,
        v_total_pt_pay,
        v_claim_substatus_id,
        v_claim_status,
        v_is_test,
        v_is_specialty,
        v_working_ticket_id,
        v_ncpdp_id,
        v_is_open_claim,
        v_requires_auth,
        v_add_auto_adjust_expected,
        v_bin_number
    FROM form_ncpdp claim
    LEFT JOIN sf_form_ncpdp_to_ncpdp_pricing sfprc 
        ON sfprc.form_ncpdp_fk = claim.id
        AND sfprc.archive IS NOT TRUE
        AND sfprc.delete IS NOT TRUE
    LEFT JOIN vw_rx_order rx
        ON rx.rx_no = claim.rx_no AND claim.rx_no IS NOT NULL
    LEFT JOIN form_ncpdp_pricing prc 
        ON prc.id = sfprc.form_ncpdp_pricing_fk 
        AND prc.archived IS NOT TRUE 
        AND prc.deleted IS NOT TRUE
    LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_prc sfrprc 
        ON sfrprc.form_ncpdp_response_fk = NEW.id
        AND sfrprc.archive IS NOT TRUE
        AND sfrprc.delete IS NOT TRUE
    LEFT JOIN form_ncpdp_response_prc rprc 
        ON rprc.id = sfrprc.form_ncpdp_response_prc_fk 
        AND rprc.archived IS NOT TRUE 
        AND rprc.deleted IS NOT TRUE
    LEFT JOIN sf_form_ncpdp_response_to_ncpdp_response_stat sfstat 
        ON sfstat.form_ncpdp_response_fk = NEW.id
        AND sfstat.archive IS NOT TRUE
        AND sfstat.delete IS NOT TRUE
    LEFT JOIN form_ncpdp_response_stat stat
        ON stat.id = sfstat.form_ncpdp_response_stat_fk 
        AND stat.archived IS NOT TRUE
        AND stat.deleted IS NOT TRUE
    LEFT JOIN form_payer py
        ON claim.payer_id = py.id
        AND py.archived IS NOT TRUE
        AND py.deleted IS NOT TRUE
    LEFT JOIN vw_ncpdp_response_summary_msg msg ON msg.response_id = NEW.id
    WHERE claim.claim_no = NEW.claim_no
        AND claim.archived IS NOT TRUE 
        AND claim.deleted IS NOT TRUE
        AND COALESCE(claim.void, 'No') <> 'Yes';

    IF v_requires_auth IS FALSE THEN
        SELECT 
            bool_or(rej.reject_code = '75')
        INTO
            v_requires_auth
        FROM vw_ncpdp_response_summary_rejmsg rej
            WHERE rej.response_id = NEW.id;
    END IF;

    -- Get the pricing components with a separate query to avoid join complexity
    SELECT 
        COALESCE(prc.ing_cst_sub, 0.0),
        COALESCE(prc.disp_fee_sub, 0.0),
        COALESCE(prc.pro_svc_fee_sub, 0.0),
        COALESCE(prc.incv_amt_sub, 0.0),
        COALESCE(prc.pt_pd_amt_sub, 0.0),
        COALESCE(prc.u_and_c_charge, 0.0),
        COALESCE(prc.gross_amount_due, 0.0)
    INTO
        NEW.ing_cst_sub,
        NEW.disp_fee_sub,
        NEW.pro_svc_fee_sub,
        NEW.incv_amt_sub,
        NEW.pt_pd_amt_sub,
        NEW.u_and_c_charge,
        NEW.gross_amount_due
    FROM form_ncpdp claim
    INNER JOIN sf_form_ncpdp_to_ncpdp_pricing sfprc 
        ON sfprc.form_ncpdp_fk = claim.id
        AND sfprc.archive IS NOT TRUE
        AND sfprc.delete IS NOT TRUE
    LEFT JOIN form_ncpdp_pricing prc 
        ON prc.id = sfprc.form_ncpdp_pricing_fk 
        AND prc.archived IS NOT TRUE 
        AND prc.deleted IS NOT TRUE
    WHERE claim.claim_no = NEW.claim_no
    AND COALESCE(claim.void, 'No') <> 'Yes'
    LIMIT 1;

    UPDATE form_ncpdp
    SET
        -- We want to store this for historical purposes to track clean claims
        auth_flag = CASE 
            WHEN form_ncpdp.auth_flag IS NULL AND v_requires_auth IS TRUE THEN 'Yes'::text
            ELSE NULL::text
        END,
        substatus_id = v_claim_substatus_id,
        status = v_claim_status,
        paid = v_total_paid,
        copay = v_total_pt_pay,
        expected = v_total_expected,
        request_d0_raw = NEW.request_d0_raw,
        response_d0_raw = NEW.response_d0_raw,
        request_json_data = NEW.request_json_data::text,
        response_json_data = NEW.response_json_data::text
    WHERE form_ncpdp.claim_no = NEW.claim_no
    AND (form_ncpdp.archived IS NULL OR form_ncpdp.archived = FALSE)
    AND (form_ncpdp.deleted IS NULL OR form_ncpdp.deleted = FALSE)
    AND COALESCE(form_ncpdp.void, 'No') <> 'Yes';

    IF v_is_test IS TRUE AND v_is_specialty IS FALSE AND v_working_ticket_id IS NOT NULL AND v_claim_status IN ('Payable', 'Margin') THEN
        UPDATE form_careplan_order_rx_disp disp
        SET
            status = 'Ready to Contact',
            test_claim_id = v_ncpdp_id
        WHERE id = v_working_ticket_id
        AND status = 'Test Claim'
        AND archived IS NOT TRUE
        AND deleted IS NOT TRUE;
    END IF;

    IF NEW.transaction_code = 'E1' THEN
        IF v_bin_number = '610144' THEN
            PERFORM build_ncpdp_response_cf_summary(NEW.id, NEW.created_on, NEW.created_by, v_site_id);
        ELSE
            PERFORM build_ncpdp_response_elig_summary(NEW.id, NEW.created_on, NEW.created_by, v_site_id);
        END IF;
        RETURN NEW;

    ELSE
        PERFORM build_ncpdp_response_summary(NEW.id, NEW.created_on, NEW.created_by, v_site_id);
    END IF;

    IF v_is_test IS TRUE THEN
        RAISE LOG 'Test Claim, Returning...';
        RETURN NEW;
    END IF;

    SELECT COALESCE(unapplied_cash_balance,0.0)
    INTO v_total_unallocated_cash
    FROM vw_unapplied_cash_balance ub
    WHERE (ub.patient_id = v_patient_id);

    v_total_balance_due := v_total_expected - v_total_paid;
    v_total_allocated_cash_used := CASE WHEN v_total_unallocated_cash > v_total_balance_due THEN v_total_balance_due ELSE v_total_unallocated_cash END;

    v_charges_editable := CASE WHEN COALESCE(v_claim_status, 'Sent') IN ('Sent', 'Rejected', 'Reversed', 'Benefit', 'Approved', 'PA Deferred', 'Duplicate') THEN TRUE ELSE FALSE END;
    PERFORM set_config('clara.prevent_locked_checks', 'on', false);

    UPDATE form_billing_invoice bi
    SET
        auth_flag = CASE WHEN v_requires_auth IS TRUE THEN 'Yes'::text ELSE NULL::text END,
        total_insurance_paid = v_total_paid,
        total_paid = v_total_paid,
        total_pt_pay = v_total_pt_pay,
        total_expected = CASE 
            WHEN v_add_auto_adjust_expected AND v_total_paid > 0 
            THEN v_total_paid 
            ELSE v_total_expected 
        END,
        revenue_accepted = CASE WHEN v_confirmed_revenue AND v_total_paid > 0 THEN 'Yes'::text ELSE bi.revenue_accepted::text END,
        total_balance_due = ROUND(COALESCE(v_total_balance_due, 0.0), 2),
        unapplied_cash_available = CASE WHEN COALESCE(v_total_unallocated_cash, 0.0) > 0 THEN 'Yes'::text ELSE NULL::text END,
        available_unapplied_cash = ROUND(COALESCE(v_total_unallocated_cash, 0.0), 2),
        allocated_unapplied_cash = ROUND(COALESCE(v_total_allocated_cash_used, 0.0), 2),
        is_dirty = NULL,
        ncpdp_open_paid = CASE WHEN v_is_open_claim IS TRUE THEN 'Yes'::text ELSE NULL::text END
    FROM form_ncpdp claim
    INNER JOIN sf_form_billing_invoice_to_ncpdp sfbi 
        ON sfbi.form_ncpdp_fk = claim.id
        AND sfbi.archive IS NOT TRUE
        AND sfbi.delete IS NOT TRUE
    INNER JOIN form_payer py
        ON claim.payer_id = py.id
        AND py.archived IS NOT TRUE
        AND py.deleted IS NOT TRUE
    WHERE claim.claim_no = NEW.claim_no
        AND bi.id = sfbi.form_billing_invoice_fk
        AND bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
        AND v_revenue_accepted_posted IS FALSE
        AND COALESCE(bi.void, 'No') <> 'Yes'
        AND COALESCE(claim.void, 'No') <> 'Yes'
        AND close_no IS NULL
        AND COALESCE(revenue_accepted_posted, 'No') = 'No';
    PERFORM set_config('clara.prevent_locked_checks', 'off', false);

    IF v_bill_for_denial IS TRUE THEN
        RETURN NEW;
    END IF;

    RETURN NEW;

    EXCEPTION WHEN OTHERS THEN
        -- Log the error
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Transaction failed during NCPDP claim response processing',
            'TRIGGER',
            current_schema(),
            'form_ncpdp_response',
            jsonb_build_object(
                'function_name', 'process_ncpdp_claim_response',
                'claim_no', NEW.claim_no,
                'invoice_no', v_invoice_no
            )
        );

        RAISE NOTICE 'Error processing NCPDP claim response: %', SQLERRM;
        RAISE;
    END;
$$ LANGUAGE plpgsql VOLATILE;

DROP TRIGGER IF EXISTS process_ncpdp_claim_response_trigger ON form_ncpdp_response;
CREATE CONSTRAINT TRIGGER process_ncpdp_claim_response_trigger
    AFTER INSERT OR UPDATE ON form_ncpdp_response
    DEFERRABLE INITIALLY DEFERRED
    FOR EACH ROW
    EXECUTE FUNCTION process_ncpdp_claim_response();