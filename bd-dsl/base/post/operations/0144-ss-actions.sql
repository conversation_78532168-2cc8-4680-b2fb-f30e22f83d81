CREATE OR REPLACE FUNCTION _check_refill_action_eligibility(p_ss_message_id INTEGER) 
RETURNS BOOLEAN LANGUAGE plpgsql AS $$
DECLARE
    p_ss_message_rec RECORD;
    v_disp_count_refill INTEGER := 0;
    v_calc_refills_rem_refill INTEGER;
    v_too_soon_refills_refill BOOLEAN := FALSE;
    v_too_soon_date_refill BOOLEAN := FALSE;
    v_eff_exp_date_refill DATE;
    v_refill_check_ok BOOLEAN;
    REFILLS_LEFT_ALLOWED INTEGER := 2; -- Copied from main function, consider making these shared constants if used elsewhere
    REFILLS_MAX_DAYS_OUT INTEGER := 60;
BEGIN
    SELECT * INTO p_ss_message_rec FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        RAISE WARNING 'Refill check: ss_message_id % not found.', p_ss_message_id;
        RETURN FALSE;
    END IF;

    v_refill_check_ok := 
        COALESCE(p_ss_message_rec.prohibit_renewal_request, 'false') <> 'true' AND
        p_ss_message_rec.patient_id IS NOT NULL AND
        p_ss_message_rec.pharmacy_rx_no IS NOT NULL AND 
        (p_ss_message_rec.expiration_date IS NULL OR p_ss_message_rec.expiration_date >= CURRENT_DATE) AND
        (p_ss_message_rec.start_date IS NULL OR p_ss_message_rec.start_date <= CURRENT_DATE) AND
        (p_ss_message_rec.effective_date IS NULL OR p_ss_message_rec.effective_date <= CURRENT_DATE);

    IF v_refill_check_ok THEN
        IF p_ss_message_rec.pharmacy_rx_no IS NOT NULL AND p_ss_message_rec.site_id IS NOT NULL THEN
            SELECT COUNT(DISTINCT drd.delivery_ticket_id) INTO v_disp_count_refill
            FROM form_careplan_order_rx_disp drd
            WHERE drd.rx_no = p_ss_message_rec.pharmacy_rx_no AND drd.site_id = p_ss_message_rec.site_id
                AND drd.deleted IS NOT TRUE AND drd.archived IS NOT TRUE AND drd.status = 'Confirmed';
        END IF;
        v_calc_refills_rem_refill := GREATEST(0, COALESCE(p_ss_message_rec.refills::INTEGER, 0) - GREATEST(0, v_disp_count_refill - 1));
        IF v_calc_refills_rem_refill > REFILLS_LEFT_ALLOWED THEN 
            v_too_soon_refills_refill := TRUE; 
        END IF;

        IF p_ss_message_rec.expiration_date IS NOT NULL THEN 
            v_eff_exp_date_refill := p_ss_message_rec.expiration_date;
        ELSIF p_ss_message_rec.written_date IS NOT NULL THEN 
            v_eff_exp_date_refill := p_ss_message_rec.written_date + INTERVAL '1 year';
        ELSE 
            v_too_soon_date_refill := FALSE; -- Cannot check date if no expiration or written date, so not too soon by this criteria
        END IF;

        IF v_eff_exp_date_refill IS NOT NULL THEN -- This check is now after v_eff_exp_date_refill might be set
            IF (v_eff_exp_date_refill - CURRENT_DATE) > REFILLS_MAX_DAYS_OUT THEN 
                v_too_soon_date_refill := TRUE; 
            END IF;
        END IF;

        v_refill_check_ok := v_refill_check_ok AND NOT v_too_soon_refills_refill AND NOT v_too_soon_date_refill;
    END IF;
    RETURN v_refill_check_ok;
END;
$$;

CREATE OR REPLACE FUNCTION _get_prefill_ss_allergy_array(p_original_ss_message_id INTEGER)
RETURNS ss_allergy_type[] LANGUAGE plpgsql AS $$
DECLARE
    v_result_array ss_allergy_type[];
BEGIN
    RAISE LOG 'Building prefill allergy array for original_ss_message_id: %', p_original_ss_message_id;
    WITH allergy_data AS (
        SELECT
            sa.source::TEXT AS source,
            sa.effective_date::DATE AS effective_date,
            sa.expiration_date::DATE AS expiration_date,
            sa.adverse_event_code_id::TEXT AS adverse_event_code_id,
            sa.adverse_event_text::TEXT AS adverse_event_text,
            sa.drug_product_code::TEXT AS drug_product_code,
            sa.drug_product_qualifier_id::TEXT AS drug_product_qualifier_id,
            sa.drug_product_text::TEXT AS drug_product_text,
            sa.reaction_code_id::TEXT AS reaction_code_id,
            sa.reaction_text::TEXT AS reaction_text,
            sa.severity_code_id::TEXT AS severity_code_id,
            sa.severity_text::TEXT AS severity_text
        FROM form_ss_allergy sa
        JOIN sf_form_ss_message_to_ss_allergy sfs ON sa.id = sfs.form_ss_allergy_fk
        WHERE sfs.form_ss_message_fk = p_original_ss_message_id
        AND sa.deleted IS NOT TRUE AND sa.archived IS NOT TRUE
        AND sfs.archive IS FALSE AND sfs.delete IS FALSE
    )
    SELECT array_agg(
        (
            ad.source,
            ad.effective_date,
            ad.expiration_date,
            ad.adverse_event_code_id,
            ad.adverse_event_text,
            ad.drug_product_code,
            ad.drug_product_qualifier_id,
            ad.drug_product_text,
            ad.reaction_code_id,
            ad.reaction_text,
            ad.severity_code_id,
            ad.severity_text
        )::ss_allergy_type
    )
    INTO v_result_array
    FROM allergy_data ad;

    RAISE LOG 'Prefill allergy array for original_ss_message_id: % is: %', p_original_ss_message_id, v_result_array;
    RETURN v_result_array; -- Returns NULL if no matching allergies found due to array_agg behavior with no rows
END;
$$;

CREATE OR REPLACE FUNCTION _get_prefill_ss_benefit_array(p_original_ss_message_id INTEGER)
RETURNS ss_benefit_type[] LANGUAGE plpgsql AS $$
DECLARE
    v_result_array ss_benefit_type[];
BEGIN
    RAISE LOG 'Building prefill benefit array for original_ss_message_id: %', p_original_ss_message_id;
    WITH benefit_data AS (
        SELECT
            sb.insurance_id::INTEGER AS insurance_id,
            sb.payer_id::INTEGER AS payer_id,
            sb.payer_type_id::TEXT AS payer_type_id_code,
            sb.payer_level::TEXT AS payer_level,
            sb.payer_name::TEXT AS payer_name,
            sb.pbm_participant_id::TEXT AS pbm_participant_id,
            sb.bin::TEXT AS bin,
            sb.pcn::TEXT AS pcn,
            sb.naic_id::TEXT AS naic_id,
            sb.hp_id::TEXT AS hp_id,
            sb.person_code::TEXT AS person_code,
            sb.group_id::TEXT AS group_id,
            sb.group_name::TEXT AS group_name,
            sb.pbm_member_id::TEXT AS pbm_member_id,
            sb.relationship_code::TEXT AS relationship_code_val,
            sb.cardholder_id::TEXT AS cardholder_id,
            sb.cardholder_first_name::TEXT AS cardholder_first_name,
            sb.cardholder_last_name::TEXT AS cardholder_last_name,
            sb.payer_phone::TEXT AS payer_phone,
            sb.payer_fax::TEXT AS payer_fax,
            sb.prohibit_renewal_request::TEXT AS prohibit_renewal_request
        FROM form_ss_benefit sb
        JOIN sf_form_ss_message_to_ss_benefit sfs ON sb.id = sfs.form_ss_benefit_fk
        WHERE sfs.form_ss_message_fk = p_original_ss_message_id
        AND sb.deleted IS NOT TRUE AND sb.archived IS NOT TRUE
        AND sfs.archive IS FALSE AND sfs.delete IS FALSE
    )
    SELECT array_agg(
        (
            bd.insurance_id,
            bd.payer_id,
            bd.payer_type_id_code,
            bd.payer_level,
            bd.payer_name,
            bd.pbm_participant_id,
            bd.bin,
            bd.pcn,
            bd.naic_id,
            bd.hp_id,
            bd.person_code,
            bd.group_id,
            bd.group_name,
            bd.pbm_member_id,
            bd.relationship_code_val,
            bd.cardholder_id,
            bd.cardholder_first_name,
            bd.cardholder_last_name,
            bd.payer_phone,
            bd.payer_fax,
            bd.prohibit_renewal_request
        )::ss_benefit_type
    )
    INTO v_result_array
    FROM benefit_data bd;

    RAISE LOG 'Prefill benefit array for original_ss_message_id: % is: %', p_original_ss_message_id, v_result_array;
    RETURN v_result_array;
END;
$$;

CREATE OR REPLACE FUNCTION _get_prefill_ss_due_array(p_original_ss_message_id INTEGER)
RETURNS ss_due_type[] LANGUAGE plpgsql AS $$
DECLARE
    v_result_array ss_due_type[];
BEGIN
    RAISE LOG 'Building prefill DUE array for original_ss_message_id: %', p_original_ss_message_id;
    WITH due_data AS (
        SELECT
            sd.compound_no::INTEGER AS compound_no,
            sd.coagent_id::TEXT AS coagent_id,
            sd.service_reason_id::TEXT AS service_reason_id_code,
            sd.pservice_rsn_id::TEXT AS pservice_rsn_id_code,
            (SELECT COALESCE(array_agg(lsrc.code), ARRAY[]::TEXT[]) 
             FROM gr_form_ss_due_service_res_id_to_list_ss_service_result_code_id gr
             JOIN form_list_ss_service_result_code lsrc ON gr.form_list_ss_service_result_code_fk = lsrc.code
             WHERE gr.form_ss_due_fk = sd.id AND lsrc.deleted IS NOT TRUE AND lsrc.archived IS FALSE
            ) AS service_res_id_codes,
            sd.coagent_code::TEXT AS coagent_code,
            sd.coagent_qualifier_id::TEXT AS coagent_qualifier_id_code,
            sd.coagent_description::TEXT AS coagent_description,
            sd.clinical_significance::TEXT AS clinical_significance_code,
            sd.ack_reason::TEXT AS ack_reason
        FROM form_ss_due sd
        JOIN sf_form_ss_message_to_ss_due sfs ON sd.id = sfs.form_ss_due_fk
        WHERE sfs.form_ss_message_fk = p_original_ss_message_id
        AND sd.deleted IS NOT TRUE AND sd.archived IS NOT TRUE
        AND sfs.archive IS FALSE AND sfs.delete IS FALSE
    )
    SELECT array_agg(
        (
            dd.compound_no,
            dd.coagent_id,
            dd.service_reason_id_code,
            dd.pservice_rsn_id_code,
            dd.service_res_id_codes,
            dd.coagent_code,
            dd.coagent_qualifier_id_code,
            dd.coagent_description,
            dd.clinical_significance_code,
            dd.ack_reason
        )::ss_due_type
    )
    INTO v_result_array
    FROM due_data dd;

    RAISE LOG 'Prefill DUE array for original_ss_message_id: % is: %', p_original_ss_message_id, v_result_array;
    RETURN v_result_array;
END;
$$;

CREATE OR REPLACE FUNCTION _get_prefill_ss_compound_array(p_original_ss_message_id INTEGER)
RETURNS ss_compound_type[] LANGUAGE plpgsql AS $$
DECLARE
    v_result_array ss_compound_type[];
BEGIN
    RAISE LOG 'Building prefill compound array for original_ss_message_id: %', p_original_ss_message_id;
    WITH compound_data AS (
        SELECT
            sc.fdb_id::TEXT AS fdb_id,
            sc.compound_no::INTEGER AS compound_no,
            sc.qual_id::TEXT AS qual_id_code,
            sc.code::TEXT AS code,
            sc.description::TEXT AS description,
            sc.strength::TEXT AS strength,
            sc.strength_form_id::TEXT AS strength_form_id_code,
            sc.strength_uom_id::TEXT AS strength_uom_id_code,
            sc.quantity::NUMERIC AS quantity,
            sc.quantity_qualifier_id::TEXT AS quantity_qualifier_id_code,
            sc.quantity_uom_id::TEXT AS quantity_uom_id_code,
            sc.dea_schedule_id::TEXT AS dea_schedule_id_code
        FROM form_ss_compound sc
        JOIN sf_form_ss_message_to_ss_compound sfs ON sc.id = sfs.form_ss_compound_fk
        WHERE sfs.form_ss_message_fk = p_original_ss_message_id
        AND sc.deleted IS NOT TRUE AND sc.archived IS NOT TRUE
        AND sfs.archive IS FALSE AND sfs.delete IS FALSE
    )
    SELECT array_agg(
        (
            cd.fdb_id,
            cd.compound_no,
            cd.qual_id_code,
            cd.code,
            cd.description,
            cd.strength,
            cd.strength_form_id_code,
            cd.strength_uom_id_code,
            cd.quantity,
            cd.quantity_qualifier_id_code,
            cd.quantity_uom_id_code,
            cd.dea_schedule_id_code
        )::ss_compound_type
    )
    INTO v_result_array
    FROM compound_data cd;

    RAISE LOG 'Prefill compound array for original_ss_message_id: % is: %', p_original_ss_message_id, v_result_array;
    RETURN v_result_array;
END;
$$;

CREATE OR REPLACE FUNCTION _get_prefill_ss_diagnosis_array(p_original_ss_message_id INTEGER)
RETURNS ss_diagnosis_type[] LANGUAGE plpgsql AS $$
DECLARE
    v_result_array ss_diagnosis_type[];
BEGIN
    RAISE LOG 'Building prefill diagnosis array for original_ss_message_id: %', p_original_ss_message_id;
    WITH diagnosis_data AS (
        SELECT
            sd.dx_id::TEXT AS dx_id, -- dx_id is already the code as per form_ss_diagnosis structure
            sd.type::TEXT AS type,
            sd.dx_code::TEXT AS dx_code,
            sd.dx_code_qualifier_id::TEXT AS dx_code_qualifier_id_code,
            sd.dx_desc::TEXT AS dx_desc
        FROM form_ss_diagnosis sd
        JOIN sf_form_ss_message_to_ss_diagnosis sfs ON sd.id = sfs.form_ss_diagnosis_fk
        WHERE sfs.form_ss_message_fk = p_original_ss_message_id
        AND sd.deleted IS NOT TRUE AND sd.archived IS NOT TRUE
        AND sfs.archive IS FALSE AND sfs.delete IS FALSE
    )
    SELECT array_agg(
        (
            dd.dx_id,
            dd.type,
            dd.dx_code,
            dd.dx_code_qualifier_id_code,
            dd.dx_desc
        )::ss_diagnosis_type
    )
    INTO v_result_array
    FROM diagnosis_data dd;

    RAISE LOG 'Prefill diagnosis array for original_ss_message_id: % is: %', p_original_ss_message_id, v_result_array;
    RETURN v_result_array;
END;
$$;

CREATE OR REPLACE FUNCTION _get_prefill_ss_codified_note_array(p_original_ss_message_id INTEGER)
RETURNS ss_codified_note_type[] LANGUAGE plpgsql AS $$
DECLARE
    v_result_array ss_codified_note_type[];
BEGIN
    RAISE LOG 'Building prefill codified_note array for original_ss_message_id: %', p_original_ss_message_id;
    WITH codified_note_data AS (
        SELECT
            scn.qualifier_id::TEXT AS qualifier_id_code,
            scn.value::INTEGER AS value
        FROM form_ss_codified_note scn
        JOIN sf_form_ss_message_to_ss_codified_note sfs ON scn.id = sfs.form_ss_codified_note_fk
        WHERE sfs.form_ss_message_fk = p_original_ss_message_id
        AND scn.deleted IS NOT TRUE AND scn.archived IS NOT TRUE
        AND sfs.archive IS FALSE AND sfs.delete IS FALSE
    )
    SELECT array_agg(
        (
            cnd.qualifier_id_code,
            cnd.value
        )::ss_codified_note_type
    )
    INTO v_result_array
    FROM codified_note_data cnd;

    RAISE LOG 'Prefill codified_note array for original_ss_message_id: % is: %', p_original_ss_message_id, v_result_array;
    RETURN v_result_array;
END;
$$;

CREATE OR REPLACE FUNCTION get_ss_response_prefill(
    p_ss_message_id INTEGER, 
    p_action_type TEXT -- e.g., 'refill', 'pa', 'change', 'approve_cancel', 'deny_cancel', 'request_provider_info', 'followup'
)
RETURNS JSONB LANGUAGE plpgsql AS $$ 
DECLARE
    v_prefill_payload JSONB := '{}'::JSONB;
    ssrec RECORD;
    v_original_message_type TEXT;
    v_response_message_type TEXT;
    v_error_text TEXT;
    v_error_context TEXT;
    v_sqlstate TEXT;
	v_message_text TEXT;
	v_pg_exception_context TEXT;

    -- Helper variables for conditional logic based on SSResponseCopySections
    is_cancel_action BOOLEAN;
    is_change_action BOOLEAN; -- General change, not specific sub-types like PA or Clarify

    v_show_options_array TEXT[] := ARRAY[]::TEXT[];

    -- For building *_has_license arrays using jsonb_agg
    v_prescriber_licenses_jsonb JSONB;
    v_prescriber_loc_licenses_jsonb JSONB;
    v_supervisor_licenses_jsonb JSONB;
    v_fu_prescriber_licenses_jsonb JSONB;

    -- For building *_has_license arrays
    v_prescriber_location_payload JSONB; -- To check if location object exists

BEGIN
    RAISE LOG 'Generating prefill for ss_message_id: %, action: %', p_ss_message_id, p_action_type;
    
    SELECT * INTO ssrec FROM form_ss_message WHERE id = p_ss_message_id AND deleted IS NOT TRUE AND archived IS NOT TRUE;
    IF NOT FOUND THEN
        RAISE WARNING 'Original message ID % not found for prefill generation.', p_ss_message_id;
        RETURN jsonb_build_object('error', 'Original message not found');
    END IF;

    is_cancel_action := p_action_type = ANY(ARRAY['approve_cancel', 'deny_cancel']);
    is_change_action := p_action_type = ANY(ARRAY['request_change', 'request_clarification', 'request_pa', 'request_provider_info']); -- Add other change-like actions if needed

    v_prefill_payload := v_prefill_payload || jsonb_build_object(
        'related_message_id', ssrec.message_id, 
        'direction', 'OUT',
        'send_to', ssrec.sent_from, 
        'sent_from', ssrec.send_to,
        'priority_flag', ssrec.priority_flag, -- Roll forward priority (assuming priority_flag stores the code like 'S' or 'X')
        'pharmacy_order_id', ssrec.pharmacy_order_id -- Roll forward link if present
    );

    -- Conditionally roll forward Header fields (excluding those managed above or always set for new messages)
    IF NOT is_cancel_action THEN -- Most header fields are context for non-cancel responses
        v_prefill_payload := v_prefill_payload || jsonb_build_object(
            'order_group_no', ssrec.order_group_no,
            'order_group_icnt', ssrec.order_group_icnt,
            'order_group_tcnt', ssrec.order_group_tcnt,
            'order_group_reason', ssrec.order_group_reason, -- Assuming this is the FK to the list table
            'rx_group_no', ssrec.rx_group_no,
            'rx_group_icnt', ssrec.rx_group_icnt,
            'rx_group_tcnt', ssrec.rx_group_tcnt,
            'rx_group_reason', ssrec.rx_group_reason, -- Assuming this is the FK
            'physician_order_id', ssrec.physician_order_id,
            'pharmacy_rx_no', ssrec.pharmacy_rx_no
        );
    END IF;
    -- Note: message_status, processed, followup_count, show_last_action are set by default/later logic for new messages

    -- Patient Info (always roll forward as per SSResponseCopySections.Patient having no ignore_actions)
    v_prefill_payload := v_prefill_payload || jsonb_build_object(
        'patient_id', ssrec.patient_id,
        'patient_first_name', ssrec.patient_first_name,
        'patient_last_name', ssrec.patient_last_name,
        'patient_middle_name', ssrec.patient_middle_name,
        'patient_dob', ssrec.patient_dob,
        'patient_gender', ssrec.patient_gender,
        'patient_ssn', ssrec.patient_ssn,
        'patient_home_street_1', ssrec.patient_home_street_1,
        'patient_home_street_2', ssrec.patient_home_street_2,
        'patient_home_city', ssrec.patient_home_city,
        'patient_home_state', ssrec.patient_home_state,
        'patient_home_zip', ssrec.patient_home_zip,
        'patient_phone', ssrec.patient_phone,
        'patient_mrn', ssrec.patient_mrn,
        'patient_medicare', ssrec.patient_medicare,
        'patient_medicaid', ssrec.patient_medicaid,
        'patient_rems', CASE WHEN NOT is_cancel_action THEN ssrec.patient_rems ELSE NULL END, -- REMS section ignored for cancel
        'nka', ssrec.nka
    );

    -- Prescriber Info (base section always rolled forward)
    v_prefill_payload := v_prefill_payload || jsonb_build_object(
        'physician_id', ssrec.physician_id,
        'prescriber_last_name', ssrec.prescriber_last_name,
        'prescriber_first_name', ssrec.prescriber_first_name,
        'prescriber_npi', ssrec.prescriber_npi,
        'prescriber_dea', ssrec.prescriber_dea,
        'prescriber_rems', CASE WHEN NOT is_cancel_action THEN ssrec.prescriber_rems ELSE NULL END, -- REMS section ignored for cancel
        'prescriber_state_cs_lic', ssrec.prescriber_state_cs_lic,
        'prescriber_medicare', ssrec.prescriber_medicare,
        'prescriber_medicaid', ssrec.prescriber_medicaid,
        'prescriber_state_lic', ssrec.prescriber_state_lic,
        'prescriber_certificate_to_prescribe', ssrec.prescriber_certificate_to_prescribe,
        'prescriber_2000waiver_id', ssrec.prescriber_2000waiver_id,
        'prescriber_spec_id', ssrec.prescriber_spec_id,
        'prescriber_address_1', ssrec.prescriber_address_1,
        'prescriber_address_2', ssrec.prescriber_address_2,
        'prescriber_city', ssrec.prescriber_city,
        'prescriber_state', ssrec.prescriber_state,
        'prescriber_zip', ssrec.prescriber_zip,
        'prescriber_phone', ssrec.prescriber_phone,
        'prescriber_extension', ssrec.prescriber_extension,
        'prescriber_fax', ssrec.prescriber_fax,
        'prescriber_agent_first_name', ssrec.prescriber_agent_first_name, -- Assuming Agent is part of Prescriber section
        'prescriber_agent_last_name', ssrec.prescriber_agent_last_name
    );

    -- Practice Location (ignored for cancel actions)
    IF NOT is_cancel_action THEN
        v_prefill_payload := v_prefill_payload || jsonb_build_object(
            'prescriber_loc_name', ssrec.prescriber_loc_name,
            'prescriber_loc_ncpdp_id', ssrec.prescriber_loc_ncpdp_id,
            'prescriber_loc_dea', ssrec.prescriber_loc_dea,
            'prescriber_loc_rems', ssrec.prescriber_loc_rems,
            'prescriber_loc_state_cs_lic', ssrec.prescriber_loc_state_cs_lic,
            'prescriber_loc_medicare', ssrec.prescriber_loc_medicare,
            'prescriber_loc_medicaid', ssrec.prescriber_loc_medicaid,
            'prescriber_loc_state_lic', ssrec.prescriber_loc_state_lic
        );
    END IF;

    -- Supervisor (ignored for cancel actions)
    IF NOT is_cancel_action THEN
        v_prefill_payload := v_prefill_payload || jsonb_build_object(
            'supervisor_last_name', ssrec.supervisor_last_name,
            'supervisor_first_name', ssrec.supervisor_first_name,
            'supervisor_npi', ssrec.supervisor_npi,
            'supervisor_dea', ssrec.supervisor_dea,
            'supervisor_rems', ssrec.supervisor_rems,
            'supervisor_state_cs_lic', ssrec.supervisor_state_cs_lic,
            'supervisor_medicare', ssrec.supervisor_medicare,
            'supervisor_medicaid', ssrec.supervisor_medicaid,
            'supervisor_state_lic', ssrec.supervisor_state_lic,
            'supervisor_certificate_to_prescribe', ssrec.supervisor_certificate_to_prescribe,
            'supervisor_2000waiver_id', ssrec.supervisor_2000waiver_id,
            'supervisor_spec_id', ssrec.supervisor_spec_id,
            'supervisor_phone', ssrec.supervisor_phone,
            'supervisor_extension', ssrec.supervisor_extension,
            'supervisor_fax', ssrec.supervisor_fax
        );
        
        SELECT jsonb_agg(license_type)
        INTO v_supervisor_licenses_jsonb
        FROM (
            SELECT 'NPI' AS license_type WHERE ssrec.supervisor_npi IS NOT NULL
            UNION ALL
            SELECT 'DEA' WHERE ssrec.supervisor_dea IS NOT NULL
            UNION ALL
            SELECT 'REMS' WHERE ssrec.supervisor_rems IS NOT NULL
            UNION ALL
            SELECT 'STATECS' WHERE ssrec.supervisor_state_cs_lic IS NOT NULL
            UNION ALL
            SELECT 'STATE' WHERE ssrec.supervisor_state_lic IS NOT NULL
            UNION ALL
            SELECT 'MEDICARE' WHERE ssrec.supervisor_medicare IS NOT NULL
            UNION ALL
            SELECT 'MEDICAID' WHERE ssrec.supervisor_medicaid IS NOT NULL
            UNION ALL
            SELECT 'CERTIFICATE' WHERE ssrec.supervisor_certificate_to_prescribe IS NOT NULL
            UNION ALL
            SELECT 'NADEAN' WHERE ssrec.supervisor_2000waiver_id IS NOT NULL
        ) AS licenses;
        v_prefill_payload := v_prefill_payload || jsonb_build_object('supervisor_has_license', COALESCE(v_supervisor_licenses_jsonb, '[]'::jsonb));
    END IF;

    -- Follow-up Prescriber (ignored for cancel actions)
    IF NOT is_cancel_action THEN
        v_prefill_payload := v_prefill_payload || jsonb_build_object(
            'fu_prescriber_last_name', ssrec.fu_prescriber_last_name,
            'fu_prescriber_first_name', ssrec.fu_prescriber_first_name,
            'fu_prescriber_address_1', ssrec.fu_prescriber_address_1,
            'fu_prescriber_address_2', ssrec.fu_prescriber_address_2,
            'fu_prescriber_city', ssrec.fu_prescriber_city,
            'fu_prescriber_state', ssrec.fu_prescriber_state,
            'fu_prescriber_zip', ssrec.fu_prescriber_zip,
            'fu_prescriber_phone', ssrec.fu_prescriber_phone,
            'fu_prescriber_extension', ssrec.fu_prescriber_extension,
            'fu_prescriber_fax', ssrec.fu_prescriber_fax,
            'fu_prescriber_npi', ssrec.fu_prescriber_npi,
            'fu_prescriber_dea', ssrec.fu_prescriber_dea,
            'fu_prescriber_rems', ssrec.fu_prescriber_rems,
            'fu_prescriber_state_cs_lic', ssrec.fu_prescriber_state_cs_lic,
            'fu_prescriber_medicare', ssrec.fu_prescriber_medicare,
            'fu_prescriber_medicaid', ssrec.fu_prescriber_medicaid,
            'fu_prescriber_state_lic', ssrec.fu_prescriber_state_lic,
            'fu_prescriber_certificate_to_prescribe', ssrec.fu_prescriber_certificate_to_prescribe,
            'fu_prescriber_2000waiver_id', ssrec.fu_prescriber_2000waiver_id,
            'fu_prescriber_spec_id', ssrec.fu_prescriber_spec_id
        );

        SELECT jsonb_agg(license_type)
        INTO v_fu_prescriber_licenses_jsonb
        FROM (
            SELECT 'NPI' AS license_type WHERE ssrec.fu_prescriber_npi IS NOT NULL
            UNION ALL
            SELECT 'DEA' WHERE ssrec.fu_prescriber_dea IS NOT NULL
            UNION ALL
            SELECT 'REMS' WHERE ssrec.fu_prescriber_rems IS NOT NULL
            UNION ALL
            SELECT 'STATECS' WHERE ssrec.fu_prescriber_state_cs_lic IS NOT NULL
            UNION ALL
            SELECT 'STATE' WHERE ssrec.fu_prescriber_state_lic IS NOT NULL
            UNION ALL
            SELECT 'MEDICARE' WHERE ssrec.fu_prescriber_medicare IS NOT NULL
            UNION ALL
            SELECT 'MEDICAID' WHERE ssrec.fu_prescriber_medicaid IS NOT NULL
            UNION ALL
            SELECT 'CERTIFICATE' WHERE ssrec.fu_prescriber_certificate_to_prescribe IS NOT NULL
            UNION ALL
            SELECT 'NADEAN' WHERE ssrec.fu_prescriber_2000waiver_id IS NOT NULL
        ) AS licenses;
        v_prefill_payload := v_prefill_payload || jsonb_build_object('fu_prescriber_has_license', COALESCE(v_fu_prescriber_licenses_jsonb, '[]'::jsonb));

        -- Build prescriber_has_license (for main prescriber)
        SELECT jsonb_agg(license_type)
        INTO v_prescriber_licenses_jsonb
        FROM (
            SELECT 'NPI' AS license_type WHERE ssrec.prescriber_npi IS NOT NULL
            UNION ALL
            SELECT 'DEA' WHERE ssrec.prescriber_dea IS NOT NULL
            UNION ALL
            SELECT 'REMS' WHERE ssrec.prescriber_rems IS NOT NULL
            UNION ALL
            SELECT 'STATECS' WHERE ssrec.prescriber_state_cs_lic IS NOT NULL
            UNION ALL
            SELECT 'STATE' WHERE ssrec.prescriber_state_lic IS NOT NULL
            UNION ALL
            SELECT 'MEDICARE' WHERE ssrec.prescriber_medicare IS NOT NULL
            UNION ALL
            SELECT 'MEDICAID' WHERE ssrec.prescriber_medicaid IS NOT NULL
            UNION ALL
            SELECT 'CERTIFICATE' WHERE ssrec.prescriber_certificate_to_prescribe IS NOT NULL
            UNION ALL
            SELECT 'NADEAN' WHERE ssrec.prescriber_2000waiver_id IS NOT NULL
        ) AS licenses;
        v_prefill_payload := v_prefill_payload || jsonb_build_object('prescriber_has_license', COALESCE(v_prescriber_licenses_jsonb, '[]'::jsonb));

        -- Build prescriber_loc_has_license
        v_prescriber_location_payload := v_prefill_payload->'prescriber_loc_name'; -- Check if location was prefilled by name
        IF v_prescriber_location_payload IS NOT NULL AND jsonb_typeof(v_prescriber_location_payload) <> 'null' AND NOT is_cancel_action THEN
            SELECT jsonb_agg(license_type)
            INTO v_prescriber_loc_licenses_jsonb
            FROM (
                SELECT 'NCPDPID' AS license_type WHERE ssrec.prescriber_loc_ncpdp_id IS NOT NULL
                UNION ALL
                SELECT 'DEA' WHERE ssrec.prescriber_loc_dea IS NOT NULL
                UNION ALL
                SELECT 'REMS' WHERE ssrec.prescriber_loc_rems IS NOT NULL -- Assuming field name from ssrec, was prescriber_loc_rems
                UNION ALL
                SELECT 'STATECS' WHERE ssrec.prescriber_loc_state_cs_lic IS NOT NULL
                UNION ALL
                SELECT 'MEDICARE' WHERE ssrec.prescriber_loc_medicare IS NOT NULL
                UNION ALL
                SELECT 'MEDICAID' WHERE ssrec.prescriber_loc_medicaid IS NOT NULL
                UNION ALL
                SELECT 'STATE' WHERE ssrec.prescriber_loc_state_lic IS NOT NULL
            ) AS licenses;
            v_prefill_payload := v_prefill_payload || jsonb_build_object('prescriber_loc_has_license', COALESCE(v_prescriber_loc_licenses_jsonb, '[]'::jsonb));
        ELSE
            v_prefill_payload := v_prefill_payload || jsonb_build_object('prescriber_loc_has_license', '[]'::jsonb); -- Ensure it's an empty array if no loc
        END IF;
    END IF;
    
    -- Medication Prescribed (rolled forward unless action-specific logic below removes it or parts of it)
    IF NOT is_cancel_action THEN -- Most medication details are not relevant for a CancelRxResponse prefill
        v_prefill_payload := v_prefill_payload || jsonb_build_object(
            'description', ssrec.description,
            'product_code_qualifier_id', ssrec.product_code_qualifier_id, 
            'product_code', ssrec.product_code,
            'drug_db_qualifier_id', ssrec.drug_db_qualifier_id,
            'drug_db_code', ssrec.drug_db_code,
            'dea_schedule_id', ssrec.dea_schedule_id,
            'strength', ssrec.strength,
            'strength_form_id', ssrec.strength_form_id,
            'strength_uom_id', ssrec.strength_uom_id,
            'quantity', ssrec.quantity,
            'quantity_qualifier_id', ssrec.quantity_qualifier_id,
            'quantity_uom_id', ssrec.quantity_uom_id,
            'day_supply', ssrec.day_supply,
            'sig', ssrec.sig,
            'daw', ssrec.daw,
            'daw_code_reason', ssrec.daw_code_reason,
            'refills', ssrec.refills,
            'written_date', ssrec.written_date,
            'effective_date', ssrec.effective_date,
            'expiration_date', ssrec.expiration_date,
            'start_date', ssrec.start_date,
            'note', ssrec.note,
            'fdb_id', ssrec.fdb_id,
            'prohibit_renewal_request', ssrec.prohibit_renewal_request, -- Top level flag for Benefits section in XML
            'clinical_info_qualifier', CASE WHEN NOT is_cancel_action THEN ssrec.clinical_info_qualifier ELSE NULL END,
            'delivery_request', ssrec.delivery_request,
            'delivery_location', ssrec.delivery_location,
            'flavoring_requested', ssrec.flavoring_requested,
            'prescriber_checked_rems', CASE WHEN NOT is_cancel_action THEN ssrec.prescriber_checked_rems ELSE NULL END,
            'rems_risk_category', CASE WHEN NOT is_cancel_action THEN ssrec.rems_risk_category ELSE NULL END,
            'rems_authorization_number', CASE WHEN NOT is_cancel_action THEN ssrec.rems_authorization_number ELSE NULL END,
            'pa_number', ssrec.pa_number,
            'pa_status_id', ssrec.pa_status_id,
            'drug_cvg_status_id', ssrec.drug_cvg_status_id,
            'do_not_fill', ssrec.do_not_fill,
            'compound_dosage_form_id', ssrec.compound_dosage_form_id
        );
    ELSE -- For cancel actions, only a minimal set of medication details are usually needed for context if any
        v_prefill_payload := v_prefill_payload || jsonb_build_object(
            'description', ssrec.description,
            'quantity', ssrec.quantity,
            'written_date', ssrec.written_date,
            'sig', ssrec.sig
        );
    END IF;

    -- Example: Allergies are ignored for Cancel actions according to SSResponseCopySections["Allergies"].ignore_actions
    IF NOT is_cancel_action THEN 
        v_prefill_payload := v_prefill_payload || jsonb_build_object(
            'subform_allergy', _get_prefill_ss_allergy_array(ssrec.id),
            'subform_benefit', _get_prefill_ss_benefit_array(ssrec.id),
            'subform_due', _get_prefill_ss_due_array(ssrec.id),
            'subform_compound', _get_prefill_ss_compound_array(ssrec.id),
            'subform_diagnosis', _get_prefill_ss_diagnosis_array(ssrec.id),
            'subform_cnote', _get_prefill_ss_codified_note_array(ssrec.id)
        );
    END IF;
    
    v_original_message_type := ssrec.message_type;

    -- Determine Response Message Type & specific prefill logic based on p_action_type
    CASE p_action_type
        WHEN 'refill' THEN 
            v_response_message_type := 'RxRenewalRequest';
            DECLARE 
                v_dispensed_count INTEGER := 0;
                v_last_disp_date DATE := NULL;
                v_actual_refills_remaining INTEGER;
                v_dispense_quantity NUMERIC := 0;
                v_inventory_id INTEGER := NULL;
                v_inventory_ndc TEXT := NULL;
                v_dea_schedule_id TEXT := NULL;
                v_inventory_description TEXT := NULL;
                v_required_auth BOOLEAN := FALSE;
                v_pa_number TEXT := NULL;
                v_pa_status_id TEXT := NULL;
            BEGIN
                IF ssrec.pharmacy_rx_no IS NOT NULL THEN
                    SELECT COUNT(distinct delivery_ticket_id),MAX(next_fill_date)  INTO v_dispensed_count, v_last_disp_date
                    FROM form_careplan_order_rx_disp disp
                    WHERE disp.rx_no = ssrec.pharmacy_rx_no
                      AND disp.deleted IS NOT TRUE AND disp.archived IS NOT TRUE
                      AND disp.status IN ('Confirmed'); -- Count actual dispenses

                    v_actual_refills_remaining := GREATEST(0, COALESCE(ssrec.refills::integer, 0) - GREATEST(0, v_dispensed_count - 1));

                    SELECT 
                        (drug_pa_id IS NOT NULL) as auth_required, 
                        pap.number as pa_number,
                        CASE
                            WHEN pap.status_id = '5' THEN 'A'
                            WHEN pap.status_id = '6' THEN 'D'
                            WHEN pap.id IS NULL THEN 'N'
                        ELSE NULL::text
                        END as pa_status_id
                    INTO v_required_auth, v_pa_number, v_pa_status_id
                    FROM form_careplan_orderp_item opi 
                    LEFT JOIN form_patient_prior_auth pap ON pap.id = opi.drug_pa_id
                    WHERE opi.rx_no = ssrec.pharmacy_rx_no
                    AND opi.deleted IS NOT TRUE AND opi.archived IS NOT TRUE;

                    SELECT SUM(quantity::numeric * -1),MAX(inventory_id) INTO v_dispense_quantity, v_inventory_id
                    FROM form_ledger_inventory lli
                    WHERE lli.rx_no = ssrec.pharmacy_rx_no;
                    SELECT ndc,dea_schedule_id,CASE WHEN LENGTH(name) > 105 THEN SUBSTRING(name, 1, 105) ELSE name END INTO v_inventory_ndc, v_dea_schedule_id, v_inventory_description
                    FROM form_inventory fi
                    WHERE fi.id = v_inventory_id;
                ELSE
                    v_actual_refills_remaining := COALESCE(ssrec.refills::integer, 0); 
                END IF;
                -- Medication Dispensed section (specific to RxRenewalRequest)
                v_prefill_payload := v_prefill_payload || jsonb_build_object(
                    'disp_description', v_inventory_description, 
                    'disp_product_code_qualifier_id', 'ND',
                    'disp_product_code', v_inventory_ndc,
                    'disp_sig', ssrec.sig,
                    'disp_pa_status_id', v_pa_status_id,
                    'disp_pa_number', v_pa_number,
                    'disp_quantity', COALESCE(v_dispense_quantity::integer, 0),
                    'disp_dea_schedule_id', v_dea_schedule_id,
                    'disp_quantity_qualifier_id', '87',
                    'disp_quantity_uom_id', 'C64933',
                    'disp_daw', ssrec.daw,
                    'disp_last_disp_date', v_last_disp_date::date, 
                    'refills_remaining', v_actual_refills_remaining::integer, 
                    'request_refills', ssrec.request_refills::integer, 
                    'disp_drug_cvg_ids', CASE WHEN v_required_auth THEN jsonb_build_array('PA') ELSE jsonb_build_array('UN') END
                );
            END;
        WHEN 'request_pa' THEN
            v_response_message_type := 'RxChangeRequest';
            v_prefill_payload := v_prefill_payload || jsonb_build_object(
                'chg_type_id', 'P', 
                'lock_chg_type', 'Yes'
            );

        WHEN 'request_clarification' THEN
            v_response_message_type := 'RxChangeRequest';
            v_prefill_payload := v_prefill_payload || jsonb_build_object(
                'chg_type_id', 'S', 
                'lock_chg_type', 'Yes'
            );

        WHEN 'request_change' THEN
            v_response_message_type := 'RxChangeRequest';
            -- User will define chg_type_id and subform_chg_med on client.
            -- Most context (medication, patient, prescriber, subforms) already rolled forward if not is_cancel_action.

        WHEN 'approve_cancel' THEN
            v_response_message_type := 'CancelRxResponse';
            DECLARE 
                v_dispensed_count INTEGER := 0;
                v_had_dispense BOOLEAN := FALSE;
            BEGIN
                 IF ssrec.pharmacy_rx_no IS NOT NULL THEN
                     SELECT COUNT(distinct delivery_ticket_id)  INTO v_dispensed_count
                     FROM form_careplan_order_rx_disp disp
                     WHERE disp.rx_no = ssrec.pharmacy_rx_no
                       AND disp.deleted IS NOT TRUE AND disp.archived IS NOT TRUE
                       AND disp.status IN ('Confirmed'); 
                     v_had_dispense := v_dispensed_count > 0;
                 END IF;
                 v_prefill_payload := v_prefill_payload || jsonb_build_object(
                    'cancel_status', 'Approved',
                    'cancel_approved_requires_note', CASE WHEN v_had_dispense THEN 'Yes' ELSE 'No' END
                );
            END;

        WHEN 'deny_cancel' THEN
            v_response_message_type := 'CancelRxResponse';
            v_prefill_payload := v_prefill_payload || jsonb_build_object(
                'cancel_status', 'Denied'
            );

        WHEN 'request_provider_info' THEN 
            v_response_message_type := 'RxChangeRequest';
            v_prefill_payload := v_prefill_payload || jsonb_build_object(
                'chg_type_id', 'U', 
                'lock_chg_type', 'Yes'
            );

        WHEN 'followup' THEN
            RAISE EXCEPTION 'Follow up not processed through SQL function';
        ELSE
            RAISE WARNING 'Unsupported action_type % for prefill generation.', p_action_type;
            RETURN jsonb_build_object('error', 'Unsupported action type for prefill.');
    END CASE;
    v_prefill_payload := v_prefill_payload || jsonb_build_object('message_type', v_response_message_type);
    -- Populate show_options based on the content of v_prefill_payload and action type
    IF NOT is_cancel_action THEN
        IF jsonb_typeof(v_prefill_payload->'subform_observation') = 'array' AND jsonb_array_length(v_prefill_payload->'subform_observation') > 0 THEN
            v_show_options_array := array_append(v_show_options_array, 'Observations');
        END IF;
        IF jsonb_typeof(v_prefill_payload->'subform_benefit') = 'array' AND jsonb_array_length(v_prefill_payload->'subform_benefit') > 0 THEN
            v_show_options_array := array_append(v_show_options_array, 'Benefits');
        END IF;
        IF jsonb_typeof(v_prefill_payload->'subform_allergy') = 'array' AND jsonb_array_length(v_prefill_payload->'subform_allergy') > 0 THEN
            v_show_options_array := array_append(v_show_options_array, 'Allergies');
        END IF;
        IF v_prefill_payload->>'supervisor_last_name' IS NOT NULL THEN
            v_show_options_array := array_append(v_show_options_array, 'Supervisor');
        END IF;
        IF v_prefill_payload->>'prescriber_agent_last_name' IS NOT NULL THEN
             v_show_options_array := array_append(v_show_options_array, 'Agent');
        END IF;
        IF v_prefill_payload->>'fu_prescriber_last_name' IS NOT NULL THEN
            v_show_options_array := array_append(v_show_options_array, 'Follow-up Prescriber');
        END IF;
        IF jsonb_typeof(v_prefill_payload->'subform_due') = 'array' AND jsonb_array_length(v_prefill_payload->'subform_due') > 0 THEN
            v_show_options_array := array_append(v_show_options_array, 'DUE');
        END IF;
        IF jsonb_typeof(v_prefill_payload->'subform_cnote') = 'array' AND jsonb_array_length(v_prefill_payload->'subform_cnote') > 0 THEN
            v_show_options_array := array_append(v_show_options_array, 'Codified Notes');
        END IF;
        IF v_prefill_payload->>'patient_rems' IS NOT NULL OR 
           v_prefill_payload->>'prescriber_checked_rems' IS NOT NULL OR 
           v_prefill_payload->>'rems_risk_category' IS NOT NULL OR 
           v_prefill_payload->>'rems_authorization_number' IS NOT NULL THEN
            v_show_options_array := array_append(v_show_options_array, 'REMS');
        END IF;
        IF v_prefill_payload->>'prescriber_loc_name' IS NOT NULL THEN
            v_show_options_array := array_append(v_show_options_array, 'Practice Location');
        END IF;
        IF v_prefill_payload->>'order_group_no' IS NOT NULL THEN 
            v_show_options_array := array_append(v_show_options_array, 'Order Group');
        END IF;
        IF v_prefill_payload->>'rx_group_no' IS NOT NULL THEN
            v_show_options_array := array_append(v_show_options_array, 'RX Group');
        END IF;
    END IF;

    IF v_response_message_type = 'RxChangeRequest' AND p_action_type IN ('request_pa', 'request_clarification', 'request_change', 'request_provider_info') THEN
         v_show_options_array := array_append(v_show_options_array, 'Change Request');
         IF p_action_type = 'request_change' THEN 
             v_show_options_array := array_append(v_show_options_array, 'Change Medication');
         END IF;
    END IF;
    
    IF v_response_message_type = 'CancelRxResponse' THEN
        v_show_options_array := array_append(v_show_options_array, 'Cancel Response');
    END IF;
    
    IF v_response_message_type = 'RxRenewalRequest' THEN 
        v_show_options_array := array_append(v_show_options_array, 'Medication Dispensed');
        v_show_options_array := array_append(v_show_options_array, 'Renewal Request');
    END IF;

    v_prefill_payload := v_prefill_payload || jsonb_build_object('show_options', ARRAY(SELECT DISTINCT unnest(v_show_options_array)));
    v_prefill_payload := v_prefill_payload || jsonb_build_object('message_status', 'Pending', 'processed', 'No', 'status_icons', ARRAY['pending']::TEXT[]);

    RAISE LOG 'Generated prefill for ss_message_id: %, action: %. Payload: %', p_ss_message_id, p_action_type, v_prefill_payload;
    RETURN remove_null_values_from_jsonb(v_prefill_payload);

EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS v_sqlstate = RETURNED_SQLSTATE, v_message_text = MESSAGE_TEXT, v_pg_exception_context = PG_EXCEPTION_CONTEXT;
    PERFORM _log_surescripts_error(p_ss_message_id, SQLSTATE, v_message_text, v_pg_exception_context, 'get_surescripts_response_prefill', jsonb_build_object('action_type', p_action_type));
    RAISE WARNING 'Error in get_surescripts_response_prefill for ID % (Action: %): % ', p_ss_message_id, p_action_type, v_error_text || ' ' || v_pg_exception_context;
    RETURN jsonb_build_object('error', 'Exception in get_surescripts_response_prefill: ' || v_message_text, 'sqlstate', SQLSTATE);
END;
$$;

CREATE OR REPLACE FUNCTION get_ss_message_available_actions(p_ss_message_id INTEGER, p_service_levels TEXT[])
RETURNS TEXT[] LANGUAGE plpgsql AS $$
DECLARE
    ssrec RECORD;
    actions TEXT[] := ARRAY[]::TEXT[];
    v_error_text TEXT;
	v_error_context TEXT;
    v_has_pending_requests BOOLEAN := FALSE;
    v_active_cancel_msg_id INTEGER;
    v_is_superseded BOOLEAN := FALSE;
    v_pa_check_ok BOOLEAN := FALSE;
    v_pa_check_ok_chg BOOLEAN := FALSE;
    v_pa_check_ok_renew BOOLEAN := FALSE;
    v_avail_prefill BOOLEAN := FALSE;
    F_SSActionPA TEXT := 'request_pa';
    F_SSActionInfo TEXT := 'request_provider_info';
    F_SSActionRefill TEXT := 'refill';
    F_SSActionFollowup TEXT := 'followup';
    F_SSActionResend TEXT := 'resend';
    F_SSActionCreate TEXT := 'create';
    F_SSDefaultsCancelRxActions TEXT[] := ARRAY['approve_cancel', 'deny_cancel'];
    F_SSDefaultsNewRxActions TEXT[] := ARRAY['request_change', 'request_clarification', 'request_provider_info'];
    MIN_HOURS_FOR_FU INTEGER := 48; 
    REFILLS_LEFT_ALLOWED INTEGER := 2; 
    REFILLS_MAX_DAYS_OUT INTEGER := 60; 

BEGIN
    RAISE LOG 'Calculating actions for ss_message_id: %', p_ss_message_id;

    SELECT * INTO ssrec FROM form_ss_message WHERE id = p_ss_message_id AND deleted IS NOT TRUE AND archived IS NOT TRUE;
    IF NOT FOUND THEN
        RAISE WARNING 'Message ID % not found for action calculation.', p_ss_message_id;
        RETURN ARRAY[]::TEXT[];
    END IF;

    -- 1. Global Cancellation Check (using new canceled_message_id field)
    IF ssrec.canceled_message_id IS NOT NULL THEN
        IF ssrec.message_type = 'CancelRx' AND ssrec.id = ssrec.canceled_message_id THEN -- Current message IS the active cancel
            -- Check if this CancelRx has already been actioned (Approved/Denied) and service level includes Cancel
            IF NOT EXISTS (
                SELECT 1 FROM form_ss_message crr_any
                WHERE crr_any.related_message_id = ssrec.message_id
                  AND crr_any.message_type = 'CancelRxResponse'
                  AND crr_any.deleted IS NOT TRUE AND crr_any.archived IS NOT TRUE
            ) AND 'Cancel' = ANY(p_service_levels) THEN
                actions := F_SSDefaultsCancelRxActions;
            END IF;
        END IF; -- Else, ssrec is not the active cancel, but part of a canceled thread, so no actions.
        RAISE LOG 'Actions for % (CancelRx thread active, canceled_message_id: %): %', p_ss_message_id, ssrec.canceled_message_id, actions;
        RETURN actions;
    END IF;

    -- 3. Handle Pending RxRenewalRequest, RxChangeRequest, or CancelRxResponse
    IF ssrec.message_type IN ('RxRenewalRequest', 'RxChangeRequest', 'CancelRxResponse') THEN
        -- Check if resend is allowed
        IF ssrec.message_type IN ('RxRenewalRequest', 'RxChangeRequest', 'CancelRxResponse') AND 
           ssrec.message_status IN ('Pending', 'Error') AND 
           ssrec.direction = 'OUT' THEN
            actions := array_append(actions, F_SSActionResend);
        END IF;
        
        -- Check if follow-up is allowed (as a fallback or in addition to resend)
        IF ssrec.message_status IN ('Pending', 'Sent', 'Verified') AND ssrec.response_message_id IS NULL THEN -- This is a pending request
            DECLARE v_can_followup_this_request BOOLEAN;
            BEGIN
                v_can_followup_this_request := (ssrec.expiration_date IS NULL OR ssrec.expiration_date >= CURRENT_DATE);
                IF COALESCE(ssrec.followup_count::INTEGER, 0) >= 1 AND ssrec.last_followup_dt IS NOT NULL THEN
                     v_can_followup_this_request := v_can_followup_this_request AND (EXTRACT(EPOCH FROM (NOW() - ssrec.last_followup_dt))/3600) >= MIN_HOURS_FOR_FU;
                ELSIF ssrec.sent_dt IS NOT NULL THEN 
                     -- Use sent_dt if available for more accurate timing
                     v_can_followup_this_request := v_can_followup_this_request AND (EXTRACT(EPOCH FROM (NOW() - ssrec.sent_dt))/3600) >= MIN_HOURS_FOR_FU;
                ELSIF ssrec.created_on IS NOT NULL THEN 
                     v_can_followup_this_request := v_can_followup_this_request AND (EXTRACT(EPOCH FROM (NOW() - ssrec.created_on))/3600) >= MIN_HOURS_FOR_FU;
                ELSE
                     v_can_followup_this_request := FALSE; 
                END IF;
                IF v_can_followup_this_request THEN actions := array_append(actions, F_SSActionFollowup); END IF;
            END;
        END IF;
        
        -- If no actions available for these request types, return empty
        IF array_length(actions, 1) IS NULL OR array_length(actions, 1) = 0 THEN
            actions := ARRAY[]::TEXT[];
        END IF;
        
        RAISE LOG 'Actions for % (Pending Request check): %', p_ss_message_id, actions;
        RETURN actions;
    END IF;

    -- 4. Handle Actionable Messages (NewRx, RxRenewalResponse, RxChangeResponse) 
    --    Only if this message IS the active one. We'll filter conflicting actions later.
    IF ssrec.is_active_message = 'Yes' THEN
        CASE ssrec.message_type
            WHEN 'NewRx' THEN
                -- Check if this NewRx has been superseded by an approved Change or Renewal Response
                SELECT TRUE INTO v_is_superseded
                FROM form_ss_message resp
                WHERE resp.related_message_id = ssrec.message_id
                  AND resp.deleted IS NOT TRUE AND resp.archived IS NOT TRUE
                  AND (
                      (resp.message_type = 'RxChangeResponse' AND resp.chg_status IN ('Approved', 'ApprovedWithChanges'))
                   OR (resp.message_type = 'RxRenewalResponse' AND resp.renewal_status IN ('Approved', 'ApprovedWithChanges', 'Replace'))
                  )
                LIMIT 1;

                IF NOT COALESCE(v_is_superseded, FALSE) THEN
                    IF 'Change' = ANY(p_service_levels) THEN
                        actions := actions || F_SSDefaultsNewRxActions;
                    END IF;
                    
                    v_pa_check_ok := ssrec.pa_number IS NULL; 
                    IF v_pa_check_ok THEN actions := array_append(actions, F_SSActionPA); END IF;

                    IF _check_refill_action_eligibility(ssrec.id) AND 'Refill' = ANY(p_service_levels) THEN 
                        actions := array_append(actions, F_SSActionRefill); 
                    END IF;
                END IF;

            WHEN 'RxRenewalResponse' THEN
                IF ssrec.renewal_status NOT IN ('Denied', 'Replace') THEN
                    IF 'Change' = ANY(p_service_levels) THEN
                        actions := actions || F_SSDefaultsNewRxActions;
                    END IF;
                    
                    v_pa_check_ok_renew := ssrec.pa_number IS NULL;
                    IF v_pa_check_ok_renew THEN actions := array_append(actions, F_SSActionPA); END IF;

                    IF _check_refill_action_eligibility(ssrec.id) AND 'Refill' = ANY(p_service_levels) THEN 
                        actions := array_append(actions, F_SSActionRefill); 
                    END IF;
                END IF;

            WHEN 'RxChangeResponse' THEN
                IF ssrec.chg_status IN ('Approved', 'ApprovedWithChanges') THEN
                    IF 'Change' = ANY(p_service_levels) THEN
                        actions := actions || F_SSDefaultsNewRxActions;
                    END IF;
                    
                    v_pa_check_ok_chg := ssrec.pa_number IS NULL;
                    IF v_pa_check_ok_chg THEN actions := array_append(actions, F_SSActionPA); END IF;

                    IF _check_refill_action_eligibility(ssrec.id) AND 'Refill' = ANY(p_service_levels) THEN 
                        actions := array_append(actions, F_SSActionRefill); 
                    END IF;
                END IF; 
        ELSE
            -- No actions for other authoritative types like already responded-to requests.
        END CASE;
    ELSE
        -- If not active message for the thread, it should have no actions.
        RAISE LOG 'Message ID % is not the active message for the thread. No actions.', p_ss_message_id;
        actions := ARRAY[]::TEXT[];
    END IF; 

    -- Common Filters (applied if actions list is not empty from above logic)
    IF array_length(actions, 1) > 0 THEN
        -- Check for all pending messages in the thread (not just one)
        DECLARE
            v_pending_rec RECORD;
        BEGIN
            -- Find all pending requests that are part of this message thread
            FOR v_pending_rec IN 
                SELECT DISTINCT pm.message_type, pm.chg_type_id
                FROM form_ss_message pm
                WHERE pm.message_type IN ('RxChangeRequest', 'RxRenewalRequest')
                AND pm.message_status IN ('Pending', 'Sent', 'Verified') -- Consider these as pending
                AND pm.response_message_id IS NULL -- No response yet
                AND pm.deleted IS NOT TRUE 
                AND pm.archived IS NOT TRUE
                AND (
                    -- Same thread: either same pharmacy_order_id or related messages
                    (pm.pharmacy_order_id IS NOT NULL AND pm.pharmacy_order_id = ssrec.pharmacy_order_id)
                    OR pm.id IN (
                        -- Messages that reference the same original prescription
                        SELECT sm.id FROM form_ss_message sm
                        WHERE sm.related_message_id = ssrec.message_id
                        OR sm.message_id = ssrec.related_message_id
                        OR (ssrec.related_message_id IS NOT NULL AND sm.related_message_id = ssrec.related_message_id)
                    )
                )
            LOOP
                IF v_pending_rec.message_type = 'RxChangeRequest' THEN
                    -- Remove actions that conflict with the specific type of change request
                    CASE v_pending_rec.chg_type_id
                        WHEN 'P' THEN 
                            -- PA request pending, remove PA action
                            actions := array_remove(actions, F_SSActionPA);
                        WHEN 'U' THEN 
                            -- Provider info request pending, remove provider info action
                            actions := array_remove(actions, F_SSActionInfo);
                        WHEN 'S' THEN
                            -- Clarification request pending, remove clarification action
                            actions := array_remove(actions, 'request_clarification');
                        ELSE
                            -- General change pending, remove change-related actions
                            actions := array_remove(actions, 'request_change');
                    END CASE;
                    
                    -- For any RxChangeRequest, also remove refill since changes might affect the prescription
                    actions := array_remove(actions, F_SSActionRefill);
                    
                ELSIF v_pending_rec.message_type = 'RxRenewalRequest' THEN
                    -- Renewal request pending, remove refill and change actions that would duplicate
                    actions := array_remove(actions, F_SSActionRefill);
                    actions := array_remove(actions, 'request_change');
                    actions := array_remove(actions, 'request_clarification');
                END IF;
            END LOOP;
        END;
    END IF;
    RAISE LOG 'Actions after common filters for %: %', p_ss_message_id, actions;

    IF ssrec.direction = 'IN' AND ssrec.message_type IN ('NewRx', 'RxRenewalResponse', 'RxChangeResponse') THEN
        SELECT EXISTS (
            SELECT 1 
            FROM vw_ss_available_prefills 
            WHERE ss_message_id = ssrec.id
        ) INTO v_avail_prefill;
        IF v_avail_prefill THEN
            actions := array_append(actions, F_SSActionCreate);
        END IF;
    END IF;

    RAISE LOG 'Final actions for ss_message_id: %: %', p_ss_message_id, actions;
    RETURN actions;

EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS v_error_text = MESSAGE_TEXT, v_error_context = PG_EXCEPTION_CONTEXT;
    PERFORM _log_surescripts_error(p_ss_message_id, SQLSTATE, v_error_text, v_error_context, 'get_surescripts_message_available_actions');
    RAISE WARNING 'Error in get_surescripts_message_available_actions for ID %: % ', p_ss_message_id, v_error_text || ' ' || v_error_context;
    RETURN ARRAY[]::TEXT[];
END;
$$;

CREATE OR REPLACE FUNCTION prepare_surescripts_followup(
    p_original_ss_message_id INTEGER,
    p_followup_message_id_header TEXT, -- The MessageID of the new followup message being sent
    p_followup_message_status TEXT,    -- The status from the gateway after sending (e.g., 'Sent', 'Verified', 'Error')
    p_error_code_id TEXT DEFAULT NULL,
    p_error_desc_code_id TEXT DEFAULT NULL,
    p_error_description TEXT DEFAULT NULL
)
RETURNS INTEGER LANGUAGE plpgsql AS $$
DECLARE
    v_new_followup_count INTEGER;
    v_ss_followup_id INTEGER;
    v_user_id INTEGER;
BEGIN
    -- Get current user ID (assuming a function or variable like current_setting('app.current_user_id') exists, or default to system/admin)
    BEGIN
        v_user_id := current_setting('app.current_user_id')::INTEGER;
    EXCEPTION WHEN OTHERS THEN
        v_user_id := 1; -- Default to admin/system if not set
    END;

    UPDATE form_ss_message
    SET followup_count = COALESCE(followup_count, 0) + 1,
        last_followup_dt = CURRENT_TIMESTAMP
    WHERE id = p_original_ss_message_id
    RETURNING followup_count INTO v_new_followup_count;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Original Surescripts message ID % not found to prepare followup.', p_original_ss_message_id;
    END IF;

    INSERT INTO form_ss_followup (
        message_id,
        message_status,
        error_code_id,
        error_desc_code_id,
        error_description,
        value, 
        sent_dt,
        created_on,
        created_by,
        archived,
        deleted
    ) VALUES (
        p_followup_message_id_header, 
        p_followup_message_status,
        p_error_code_id,
        p_error_desc_code_id,
        p_error_description,
        v_new_followup_count, 
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP,
        v_user_id,
        FALSE,
        FALSE
    ) RETURNING id INTO v_ss_followup_id;

    INSERT INTO sf_form_ss_message_to_ss_followup (
        form_ss_message_fk,
        form_ss_followup_fk,
        created_on,
        created_by,
        archive,
        delete
    ) VALUES (
        p_original_ss_message_id,
        v_ss_followup_id,
        CURRENT_TIMESTAMP,
        v_user_id,
        FALSE,
        FALSE
    );

    RAISE LOG 'Prepared followup for original_ss_message_id: %, new followup_count: %, created form_ss_followup_id: %', 
              p_original_ss_message_id, v_new_followup_count, v_ss_followup_id;
              
    RETURN v_ss_followup_id;
END;
$$;