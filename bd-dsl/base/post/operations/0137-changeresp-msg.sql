CREATE OR REPLACE FUNCTION _parse_and_insert_rxchangeresponse(
    p_ss_log_id INTEGER, 
    p_message_json JSONB, 
    p_surescripts_message_type TEXT, 
    p_header_data JSONB, 
    p_patient_data JSONB, 
    p_prescriber_data JSONB,
    p_priority_flag TEXT
)
RETURNS TABLE (ss_message_id INTEGER, error_message TEXT) LANGUAGE plpgsql AS $$ 
DECLARE
    -- Parsed data holders from helper functions
    v_parsed_header ss_extracted_header_type;
    v_parsed_patient ss_extracted_patient_type;
    v_parsed_prescriber ss_extracted_prescriber_info_type;
    v_parsed_supervisor ss_extracted_supervisor_type;
    v_parsed_fu_prescriber ss_extracted_fu_prescriber_type;
    v_parsed_medication ss_extracted_medication_type;
    v_resolved_ids ss_resolved_ids_type;

    v_main_params ss_message_main_params_type;

    -- Change Response specific fields (will be populated into v_main_params directly later)
    v_ss_chg_type_id TEXT;
    v_ss_chg_type_sc_id TEXT[];
    v_ss_chg_status TEXT;
    v_ss_chg_dr_code_id TEXT[];
    v_ss_chg_denied_reason TEXT;
    v_ss_chg_approved_note TEXT;
    v_ss_chg_vr_cd_id TEXT[];
    v_ss_chg_validated_note TEXT;
    v_final_chg_resp_status TEXT; -- To determine icon

    -- Utility
    v_new_ss_message_id INTEGER;
    v_error_text TEXT;
    v_error_context TEXT;
    v_status_icons TEXT[];
    v_original_message_record_id INTEGER;

    v_show_options TEXT[] := ARRAY[]::TEXT[];
    v_med_path_segment TEXT; -- Determined based on response type (ApprovedWithChanges implies MedicationPrescribed)

    v_allergies ss_allergy_type[] := ARRAY[]::ss_allergy_type[];
    v_benefits ss_benefit_type[] := ARRAY[]::ss_benefit_type[];
    v_observations ss_observation_type[] := ARRAY[]::ss_observation_type[];
    v_dues ss_due_type[] := ARRAY[]::ss_due_type[];
    v_compounds ss_compound_type[] := ARRAY[]::ss_compound_type[];
    v_diagnoses ss_diagnosis_type[] := ARRAY[]::ss_diagnosis_type[];
    v_codified_notes ss_codified_note_type[] := ARRAY[]::ss_codified_note_type[];

BEGIN
    RAISE LOG 'Helper: Parsing RxChangeResponse for ss_log_id: % with MessageType: %', p_ss_log_id, p_surescripts_message_type;
    v_main_params.ss_direction := 'IN';
    
    -- Call helper functions to parse main sections
    v_parsed_header := _parse_ss_header_data(p_header_data);
    v_main_params.ss_digital_signature_indicator := v_parsed_header.digital_signature_indicator;
    v_main_params.ss_direction := 'IN'; -- Explicit for inbound
    v_main_params.ss_sent_dt := v_parsed_header.sent_dt;
    v_main_params.ss_to := v_parsed_header.to_val;
    v_main_params.ss_from := v_parsed_header.from_val;
    v_main_params.ss_message_id_header := v_parsed_header.message_id_header;
    v_main_params.ss_related_message_id := v_parsed_header.related_message_id;
    v_main_params.ss_prescriber_order_number := v_parsed_header.prescriber_order_number;
    v_main_params.ss_pharmacy_rx_no := v_parsed_header.pharmacy_rx_no;
    v_main_params.sender_software_developer := v_parsed_header.sender_software_developer;
    v_main_params.sender_software_product := v_parsed_header.sender_software_product;
    v_main_params.sender_software_version := v_parsed_header.sender_software_version;
    v_main_params.order_group_no := v_parsed_header.order_group_no;
    v_main_params.rx_group_no := v_parsed_header.rx_group_no;
    v_main_params.order_group_icnt := v_parsed_header.order_group_icnt;
    v_main_params.rx_group_icnt := v_parsed_header.rx_group_icnt;
    v_main_params.order_group_tcnt := v_parsed_header.order_group_tcnt;
    v_main_params.rx_group_tcnt := v_parsed_header.rx_group_tcnt;
    v_main_params.order_group_reason_code := v_parsed_header.order_group_reason_code;
    v_main_params.rx_group_reason_code := v_parsed_header.rx_group_reason_code;

    v_parsed_patient := _parse_ss_patient_data(p_patient_data, p_message_json->'Message'->'Body', p_surescripts_message_type);
    v_main_params.ss_patient_name_display := v_parsed_patient.name_display;
    v_main_params.ss_patient_first_name    := v_parsed_patient.first_name;
    v_main_params.ss_patient_last_name     := v_parsed_patient.last_name;
    v_main_params.ss_patient_middle_name   := v_parsed_patient.middle_name;
    v_main_params.ss_patient_dob           := v_parsed_patient.dob;
    v_main_params.ss_patient_gender        := v_parsed_patient.gender;
    v_main_params.ss_patient_ssn           := v_parsed_patient.ssn;
    v_main_params.ss_patient_mrn           := v_parsed_patient.mrn;
    v_main_params.ss_patient_home_street_1 := v_parsed_patient.home_street_1;
    v_main_params.ss_patient_home_street_2 := v_parsed_patient.home_street_2;
    v_main_params.ss_patient_home_city     := v_parsed_patient.home_city;
    v_main_params.ss_patient_home_state    := v_parsed_patient.home_state_code;
    v_main_params.ss_patient_home_zip      := v_parsed_patient.home_zip;
    v_main_params.ss_patient_phone         := v_parsed_patient.phone;
    v_main_params.ss_nka                   := v_parsed_patient.nka; 

    v_parsed_prescriber := _parse_ss_prescriber_data(p_prescriber_data);
    v_main_params.ss_prescriber_name_display := v_parsed_prescriber.name_display;
    v_main_params.ss_prescriber_npi := v_parsed_prescriber.npi;
    v_main_params.ss_prescriber_dea := v_parsed_prescriber.dea;
    v_main_params.ss_prescriber_rems := v_parsed_prescriber.rems;
    v_main_params.ss_prescriber_state_cs_lic := v_parsed_prescriber.state_cs_lic;
    v_main_params.ss_prescriber_medicare := v_parsed_prescriber.medicare;
    v_main_params.ss_prescriber_medicaid := v_parsed_prescriber.medicaid;
    v_main_params.ss_prescriber_state_lic := v_parsed_prescriber.state_lic;
    v_main_params.ss_prescriber_certificate_to_prescribe := v_parsed_prescriber.certificate_to_prescribe;
    v_main_params.ss_prescriber_2000waiver_id := v_parsed_prescriber.waiver_2000_id;
    v_main_params.ss_prescriber_spec_id := v_parsed_prescriber.spec_code;
    v_main_params.ss_prescriber_has_license := v_parsed_prescriber.has_license;
    v_main_params.ss_prescriber_last_name := v_parsed_prescriber.last_name;
    v_main_params.ss_prescriber_first_name := v_parsed_prescriber.first_name;
    v_main_params.ss_prescriber_address_1 := v_parsed_prescriber.address_1;
    v_main_params.ss_prescriber_address_2 := v_parsed_prescriber.address_2;
    v_main_params.ss_prescriber_city := v_parsed_prescriber.city;
    v_main_params.ss_prescriber_state := v_parsed_prescriber.state_code;
    v_main_params.ss_prescriber_zip := v_parsed_prescriber.zip;
    v_main_params.ss_prescriber_phone := v_parsed_prescriber.phone;
    v_main_params.ss_prescriber_extension := v_parsed_prescriber.extension;
    v_main_params.ss_prescriber_fax := v_parsed_prescriber.fax;
    v_main_params.ss_prescriber_loc_name := v_parsed_prescriber.loc_name;
    v_main_params.ss_prescriber_loc_ncpdp_id := v_parsed_prescriber.loc_ncpdp_id;
    v_main_params.ss_prescriber_loc_dea := v_parsed_prescriber.loc_dea;
    v_main_params.ss_prescriber_loc_rems := v_parsed_prescriber.loc_rems;
    v_main_params.ss_prescriber_loc_state_cs_lic := v_parsed_prescriber.loc_state_cs_lic;
    v_main_params.ss_prescriber_loc_medicare := v_parsed_prescriber.loc_medicare;
    v_main_params.ss_prescriber_loc_medicaid := v_parsed_prescriber.loc_medicaid;
    v_main_params.ss_prescriber_loc_state_lic := v_parsed_prescriber.loc_state_lic;
    v_main_params.ss_prescriber_loc_has_license := v_parsed_prescriber.loc_has_license;
    v_main_params.ss_prescriber_agent_first_name := v_parsed_prescriber.agent_first_name;
    v_main_params.ss_prescriber_agent_last_name := v_parsed_prescriber.agent_last_name;

    v_parsed_supervisor := _parse_ss_supervisor_data(p_message_json#>ARRAY['Message','Body',p_surescripts_message_type,'Supervisor']);
    v_main_params.ss_supervisor_name_display := CASE 
        WHEN v_parsed_supervisor.last_name IS NOT NULL AND v_parsed_supervisor.first_name IS NOT NULL THEN
            v_parsed_supervisor.last_name || ', ' || v_parsed_supervisor.first_name
        WHEN v_parsed_supervisor.last_name IS NOT NULL THEN v_parsed_supervisor.last_name
        WHEN v_parsed_supervisor.first_name IS NOT NULL THEN v_parsed_supervisor.first_name
        ELSE NULL
    END;
    v_main_params.ss_supervisor_last_name := v_parsed_supervisor.last_name;
    v_main_params.ss_supervisor_first_name := v_parsed_supervisor.first_name;
    v_main_params.ss_supervisor_npi := v_parsed_supervisor.npi;
    v_main_params.ss_supervisor_dea := v_parsed_supervisor.dea;
    v_main_params.ss_supervisor_rems := v_parsed_supervisor.rems;
    v_main_params.ss_supervisor_state_cs_lic := v_parsed_supervisor.state_cs_lic;
    v_main_params.ss_supervisor_medicare := v_parsed_supervisor.medicare;
    v_main_params.ss_supervisor_medicaid := v_parsed_supervisor.medicaid;
    v_main_params.ss_supervisor_state_lic := v_parsed_supervisor.state_lic;
    v_main_params.ss_supervisor_certificate_to_prescribe := v_parsed_supervisor.certificate_to_prescribe;
    v_main_params.ss_supervisor_2000waiver_id := v_parsed_supervisor.waiver_2000_id;
    v_main_params.ss_supervisor_spec_id := v_parsed_supervisor.spec_code; -- Map from spec_code
    v_main_params.ss_supervisor_phone := v_parsed_supervisor.phone;
    v_main_params.ss_supervisor_extension := v_parsed_supervisor.extension;
    v_main_params.ss_supervisor_fax := v_parsed_supervisor.fax;
    v_main_params.ss_supervisor_has_license := v_parsed_supervisor.has_license;

    v_parsed_fu_prescriber := _parse_ss_fu_prescriber_data(p_message_json#>ARRAY['Message','Body',p_surescripts_message_type,'FollowUpPrescriber']);
    v_main_params.ss_fu_prescriber_name_display := CASE 
        WHEN v_parsed_fu_prescriber.last_name IS NOT NULL AND v_parsed_fu_prescriber.first_name IS NOT NULL THEN
            v_parsed_fu_prescriber.last_name || ', ' || v_parsed_fu_prescriber.first_name
        WHEN v_parsed_fu_prescriber.last_name IS NOT NULL THEN v_parsed_fu_prescriber.last_name
        WHEN v_parsed_fu_prescriber.first_name IS NOT NULL THEN v_parsed_fu_prescriber.first_name
        ELSE NULL
    END;
    v_main_params.ss_fu_prescriber_last_name := v_parsed_fu_prescriber.last_name;
    v_main_params.ss_fu_prescriber_first_name := v_parsed_fu_prescriber.first_name;
    v_main_params.ss_fu_prescriber_address_1 := v_parsed_fu_prescriber.address_1;
    v_main_params.ss_fu_prescriber_address_2 := v_parsed_fu_prescriber.address_2;
    v_main_params.ss_fu_prescriber_city := v_parsed_fu_prescriber.city;
    v_main_params.ss_fu_prescriber_state := v_parsed_fu_prescriber.state_code; -- Map from state_code
    v_main_params.ss_fu_prescriber_zip := v_parsed_fu_prescriber.zip;
    v_main_params.ss_fu_prescriber_phone := v_parsed_fu_prescriber.phone;
    v_main_params.ss_fu_prescriber_extension := v_parsed_fu_prescriber.extension;
    v_main_params.ss_fu_prescriber_fax := v_parsed_fu_prescriber.fax;
    v_main_params.ss_fu_prescriber_npi := v_parsed_fu_prescriber.npi;
    v_main_params.ss_fu_prescriber_dea := v_parsed_fu_prescriber.dea;
    v_main_params.ss_fu_prescriber_rems := v_parsed_fu_prescriber.rems;
    v_main_params.ss_fu_prescriber_state_cs_lic := v_parsed_fu_prescriber.state_cs_lic;
    v_main_params.ss_fu_prescriber_medicare := v_parsed_fu_prescriber.medicare;
    v_main_params.ss_fu_prescriber_medicaid := v_parsed_fu_prescriber.medicaid;
    v_main_params.ss_fu_prescriber_state_lic := v_parsed_fu_prescriber.state_lic;
    v_main_params.ss_fu_prescriber_certificate_to_prescribe := v_parsed_fu_prescriber.certificate_to_prescribe;
    v_main_params.ss_fu_prescriber_2000waiver_id := v_parsed_fu_prescriber.waiver_2000_id;
    v_main_params.ss_fu_prescriber_spec_id := v_parsed_fu_prescriber.spec_code; -- Map from spec_code
    v_main_params.ss_fu_prescriber_has_license := v_parsed_fu_prescriber.has_license;

    -- Initialize status icons
    v_status_icons := ARRAY['new']::TEXT[];
    IF p_priority_flag = 'X' THEN
        v_status_icons := array_append(v_status_icons, 'high_priority');
    END IF;

    -- Extract ChangeResponse data and determine medication path
    DECLARE
        v_chg_resp_item JSONB;
    BEGIN
        v_chg_resp_item := p_message_json#>ARRAY['Message','Body',p_surescripts_message_type];
        v_ss_chg_type_id := v_chg_resp_item#>>'{MessageRequestCode}';

        IF jsonb_typeof(v_chg_resp_item#>'{MessageRequestSubCode}') = 'array' THEN
            SELECT array_agg(elem->>'Code') INTO v_ss_chg_type_sc_id
            FROM jsonb_array_elements(v_chg_resp_item#>'{MessageRequestSubCode}') elem;
        ELSIF v_chg_resp_item#>>'{MessageRequestSubCode,Code}' IS NOT NULL THEN
            v_ss_chg_type_sc_id := ARRAY[v_chg_resp_item#>>'{MessageRequestSubCode,Code}'];
        ELSE
            v_ss_chg_type_sc_id := NULL;
        END IF;

        IF v_chg_resp_item#>'{MedicationResponse}' IS NOT NULL THEN
            v_med_path_segment := 'MedicationResponse';
        ELSE
            v_med_path_segment := 'MedicationPrescribed';
        END IF;

        IF v_chg_resp_item#>'{Response,Approved}' IS NOT NULL THEN
            v_ss_chg_status := 'Approved';
            v_ss_chg_approved_note := v_chg_resp_item#>>'{Response,Approved,Note}';
        ELSIF v_chg_resp_item#>'{Response,Denied}' IS NOT NULL THEN
            v_ss_chg_status := 'Denied';
            IF jsonb_typeof(v_chg_resp_item#>'{Response,Denied,ReasonCode}') = 'array' THEN
                SELECT array_agg(elem->>'Code') INTO v_ss_chg_dr_code_id
                FROM jsonb_array_elements(v_chg_resp_item#>'{Response,Denied,ReasonCode}') elem;
            ELSIF v_chg_resp_item#>>'{Response,Denied,ReasonCode}' IS NOT NULL THEN 
                 v_ss_chg_dr_code_id := ARRAY[v_chg_resp_item#>>'{Response,Denied,ReasonCode}'];
            ELSE
                v_ss_chg_dr_code_id := NULL::TEXT[];
            END IF;
            v_ss_chg_denied_reason := v_chg_resp_item#>>'{Response,Denied,DenialReason}';
        ELSIF v_chg_resp_item#>'{Response,ApprovedWithChanges}' IS NOT NULL THEN
            v_ss_chg_status := 'ApprovedWithChanges';
            v_ss_chg_approved_note := v_chg_resp_item#>>'{Response,ApprovedWithChanges,Note}';
        ELSIF v_chg_resp_item#>'{Response,Validated}' IS NOT NULL THEN
            v_ss_chg_status := 'Validated';
            IF jsonb_typeof(v_chg_resp_item#>'{Response,Validated,ReasonCode}') = 'array' THEN
                SELECT array_agg(elem->>'Code') INTO v_ss_chg_vr_cd_id
                FROM jsonb_array_elements(v_chg_resp_item#>'{Response,Validated,ReasonCode}') elem;
            ELSIF v_chg_resp_item#>>'{Response,Validated,ReasonCode}' IS NOT NULL THEN
                v_ss_chg_vr_cd_id := ARRAY[v_chg_resp_item#>>'{Response,Validated,ReasonCode}'];
            ELSE 
                v_ss_chg_vr_cd_id := NULL::TEXT[];
            END IF;
            v_ss_chg_validated_note := v_chg_resp_item#>>'{Response,Validated,Note}';
        END IF;

        IF v_ss_chg_status IS NULL THEN
            RAISE EXCEPTION 'Invalid or unsupported RxChangeResponse type. Status could not be determined.'
            USING ERRCODE = 'P0001', DETAIL = 'Surescripts Change Response type is not one of Approved, Denied, ApprovedWithChanges, or Validated.';
        END IF;

        v_final_chg_resp_status := v_ss_chg_status; -- For icon logic
    END;

    -- Add specific icon based on change response status
    IF v_final_chg_resp_status = 'Approved' OR v_final_chg_resp_status = 'ApprovedWithChanges' THEN
        v_status_icons := array_append(v_status_icons, 'approved');
    ELSIF v_final_chg_resp_status = 'Denied' THEN
        v_status_icons := array_append(v_status_icons, 'denied');
    ELSIF v_final_chg_resp_status = 'Validated' THEN
        v_status_icons := array_append(v_status_icons, 'info');
    END IF;

    -- Parse Medication Data using helper function
    v_parsed_medication := _parse_ss_medication_data(p_message_json, p_surescripts_message_type, v_med_path_segment);
    v_main_params.description := v_parsed_medication.description;
    v_main_params.product_code_qualifier_id := v_parsed_medication.product_code_qualifier_id;
    v_main_params.product_code := v_parsed_medication.product_code;
    v_main_params.fdb_id := v_resolved_ids.fdb_id; -- From resolved IDs
    v_main_params.drug_db_qualifier_id := v_parsed_medication.drug_db_qualifier_id;
    v_main_params.drug_db_code := v_parsed_medication.drug_db_code;
    v_main_params.dea_schedule_id := v_parsed_medication.dea_schedule_id;
    v_main_params.strength := v_parsed_medication.strength;
    v_main_params.strength_form_id := v_parsed_medication.strength_form_id;
    v_main_params.strength_uom_id := v_parsed_medication.strength_uom_id;
    v_main_params.quantity := v_parsed_medication.quantity;
    v_main_params.quantity_qualifier_id := v_parsed_medication.quantity_qualifier_id;
    v_main_params.quantity_uom_id := v_parsed_medication.quantity_uom_id;
    v_main_params.days_supply := v_parsed_medication.days_supply;
    v_main_params.compound_dosage_form_id := v_parsed_medication.compound_dosage_form_id;
    v_main_params.written_date := v_parsed_medication.written_date;
    v_main_params.start_date := v_parsed_medication.start_date;
    v_main_params.expiration_date := v_parsed_medication.expiration_date;
    v_main_params.effective_date := v_parsed_medication.effective_date;
    v_main_params.daw := v_parsed_medication.daw;
    v_main_params.daw_code_reason := v_parsed_medication.daw_code_reason;
    v_main_params.refills := v_parsed_medication.refills;
    v_main_params.pa_number := v_parsed_medication.pa_number;
    v_main_params.pa_status_id := v_parsed_medication.pa_status_id;
    v_main_params.drug_cvg_status_id := v_parsed_medication.drug_cvg_status_id;
    v_main_params.do_not_fill := v_parsed_medication.do_not_fill;
    v_main_params.note := v_parsed_medication.note;
    v_main_params.sig := v_parsed_medication.sig;
    v_main_params.delivery_request := v_parsed_medication.delivery_request;
    v_main_params.delivery_location := v_parsed_medication.delivery_location;
    v_main_params.flavoring_requested := v_parsed_medication.flavoring_requested;
    v_main_params.prescriber_checked_rems := v_parsed_medication.prescriber_checked_rems;
    v_main_params.rems_risk_category := v_parsed_medication.rems_risk_category;
    v_main_params.rems_authorization_number := v_parsed_medication.rems_authorization_number;
    v_main_params.clinical_info_qualifier := v_parsed_medication.clinical_info_qualifier;
    v_main_params.prohibit_renewal_request := v_parsed_medication.prohibit_renewal_request;

    -- Resolve IDs using helper
    v_resolved_ids := _resolve_ss_ids(
        p_ss_to := v_main_params.ss_to,
        p_ss_from := v_main_params.ss_from,
        p_ss_direction := v_main_params.ss_direction,
        p_parsed_patient := v_parsed_patient,
        p_parsed_prescriber := v_parsed_prescriber,
        p_parsed_medication := v_parsed_medication, -- Pass the fully parsed medication object
        p_prescriber_order_number := v_main_params.ss_prescriber_order_number
    );
    v_main_params.resolved_site_id := v_resolved_ids.resolved_site_id;
    v_main_params.resolved_patient_id := v_resolved_ids.resolved_patient_id;
    v_main_params.resolved_physician_id := v_resolved_ids.resolved_physician_id;
    v_main_params.resolved_pharmacy_order_id := v_resolved_ids.resolved_pharmacy_order_id;
    v_main_params.fdb_id := v_resolved_ids.fdb_id; -- From resolved IDs helper

    -- Check if ProhibitRenewalRequest needs to be copied from original message
    IF v_main_params.ss_related_message_id IS NOT NULL AND v_main_params.prohibit_renewal_request IS NULL THEN
        DECLARE
            v_original_prohibit_renewal TEXT;
        BEGIN
            SELECT orig_msg.prohibit_renewal_request INTO v_original_prohibit_renewal
            FROM form_ss_message orig_msg
            WHERE orig_msg.message_id = v_main_params.ss_related_message_id
            AND orig_msg.deleted IS NOT TRUE AND orig_msg.archived IS NOT TRUE
            ORDER BY orig_msg.created_on DESC LIMIT 1;
            
            IF v_original_prohibit_renewal IS NOT NULL THEN
                v_main_params.prohibit_renewal_request := v_original_prohibit_renewal;
                RAISE LOG 'Copied forward ProhibitRenewalRequest from original message: %', v_original_prohibit_renewal;
            END IF;
        END;
    END IF;

    -- Populate remaining v_main_params
    v_main_params.ss_priority_flag := p_priority_flag;
    v_main_params.ss_message_type := p_surescripts_message_type;
    v_main_params.status_icons := v_status_icons;
    v_main_params.processed_dt := CURRENT_TIMESTAMP;
    v_main_params.ss_renewal_status := NULL::TEXT; -- Not a renewal response
    v_main_params.ss_renewal_denial_reason_code := NULL::TEXT[]; -- Not a renewal response
    v_main_params.ss_renewal_denied_reason := NULL; -- Not a renewal response
    v_main_params.ss_renewal_note := NULL; -- Not a renewal response
    v_main_params.ss_cancel_status := NULL::TEXT; -- Not a cancel response
    v_main_params.ss_chg_type_id := v_ss_chg_type_id; 
    v_main_params.ss_chg_type_sc_id := v_ss_chg_type_sc_id; -- This should be the original request's subcodes
    v_main_params.ss_chg_sc_aw_chg_id := v_ss_chg_type_sc_id;
    v_main_params.ss_chg_status := v_ss_chg_status;
    v_main_params.ss_chg_dr_code_id := v_ss_chg_dr_code_id;
    v_main_params.ss_chg_denied_reason := v_ss_chg_denied_reason;
    v_main_params.ss_chg_approved_note := v_ss_chg_approved_note;
    v_main_params.ss_chg_vr_cd_id := v_ss_chg_vr_cd_id;
    v_main_params.ss_chg_validated_note := v_ss_chg_validated_note;

    -- Parse subforms (allergies, observations, etc.)
    v_observations := _parse_ss_observations(p_message_json#>ARRAY['Message','Body',p_surescripts_message_type,'Measurement']);
    v_allergies := _parse_ss_allergies(p_message_json#>ARRAY['Message','Body',p_surescripts_message_type,'AllergyOrAdverseEvent']);
    v_diagnoses := _parse_ss_diagnoses(p_message_json#>ARRAY['Message','Body',p_surescripts_message_type, v_med_path_segment, 'Diagnosis']);
    v_codified_notes := _parse_ss_codified_notes(p_message_json#>ARRAY['Message','Body',p_surescripts_message_type, v_med_path_segment,'PatientCodifiedNote']);
    v_dues := _parse_ss_dues(p_message_json#>ARRAY['Message','Body',p_surescripts_message_type,v_med_path_segment,'DrugUseEvaluation']);
    IF jsonb_path_exists(p_message_json, ('$.Message.Body.'||p_surescripts_message_type||'."'||v_med_path_segment||'".CompoundInformation.CompoundIngredientsLotNotUsed')::jsonpath) THEN
        v_compounds := _parse_ss_compounds(p_message_json#>ARRAY['Message','Body',p_surescripts_message_type,v_med_path_segment,'CompoundInformation','CompoundIngredientsLotNotUsed']);
    ELSIF jsonb_path_exists(p_message_json, ('$.Message.Body.'||p_surescripts_message_type||'."'||v_med_path_segment||'".CompoundInformation.CompoundIngredients')::jsonpath) THEN
        v_compounds := _parse_ss_compounds(p_message_json#>ARRAY['Message','Body',p_surescripts_message_type,v_med_path_segment,'CompoundInformation','CompoundIngredients']);
    END IF;
    v_benefits := _parse_ss_benefits(p_message_json#>ARRAY['Message','Body',p_surescripts_message_type,'BenefitsCoordination'], v_main_params.resolved_patient_id);

    -- Logic for show_options
    IF v_ss_chg_status IS NOT NULL THEN v_show_options := array_append(v_show_options, 'Change Response'); END IF;
    IF array_length(v_observations, 1) > 0 THEN v_show_options := array_append(v_show_options, 'Observations'); END IF;
    IF array_length(v_benefits, 1) > 0 THEN v_show_options := array_append(v_show_options, 'Benefits'); END IF;
    IF array_length(v_allergies, 1) > 0 THEN v_show_options := array_append(v_show_options, 'Allergies'); END IF;
    IF v_main_params.ss_supervisor_last_name IS NOT NULL THEN v_show_options := array_append(v_show_options, 'Supervisor'); END IF;
    IF v_main_params.ss_prescriber_agent_last_name IS NOT NULL THEN v_show_options := array_append(v_show_options, 'Agent'); END IF;
    IF v_main_params.ss_fu_prescriber_last_name IS NOT NULL THEN v_show_options := array_append(v_show_options, 'Follow-up Prescriber'); END IF;
    IF array_length(v_dues, 1) > 0 THEN v_show_options := array_append(v_show_options, 'DUE'); END IF;
    IF array_length(v_codified_notes, 1) > 0 THEN v_show_options := array_append(v_show_options, 'Codified Notes'); END IF;
    IF v_parsed_patient.rems IS NOT NULL OR v_main_params.prescriber_checked_rems IS NOT NULL OR v_main_params.rems_risk_category IS NOT NULL OR v_main_params.rems_authorization_number IS NOT NULL THEN
        v_show_options := array_append(v_show_options, 'REMS');
    END IF;
    IF v_parsed_prescriber.loc_name IS NOT NULL THEN
        v_show_options := array_append(v_show_options, 'Practice Location');
    END IF;
    IF v_main_params.order_group_no IS NOT NULL THEN v_show_options := array_append(v_show_options, 'Order Group'); END IF;
    IF v_main_params.rx_group_no IS NOT NULL THEN v_show_options := array_append(v_show_options, 'RX Group'); END IF;
    v_main_params.show_options := v_show_options;

    RAISE LOG 'Determined show_options: % for RxChangeResponse (Log ID: %)', array_to_string(v_show_options, ', '), p_ss_log_id;
    
    v_new_ss_message_id := _insert_ss_message_and_subforms(
        p_main_params := v_main_params,
        p_allergies := v_allergies,
        p_benefits := v_benefits,
        p_observations := v_observations,
        p_dues := v_dues,
        p_compounds := v_compounds,
        p_diagnoses := v_diagnoses,
        p_codified_notes := v_codified_notes,
        p_ss_log_id := p_ss_log_id
    );

    IF v_new_ss_message_id IS NOT NULL AND v_main_params.ss_related_message_id IS NOT NULL THEN
        SELECT id INTO v_original_message_record_id FROM form_ss_message
        WHERE message_id = v_main_params.ss_related_message_id AND deleted IS NOT TRUE AND archived IS NOT TRUE
        ORDER BY created_on DESC LIMIT 1;

        IF v_original_message_record_id IS NOT NULL THEN
            UPDATE form_ss_message
            SET response_message_id = v_main_params.ss_message_id_header,
                show_last_action = 'Yes',
                last_action = 'Change Response (' || COALESCE(v_final_chg_resp_status, 'Status N/A') || ') received on ' || TO_CHAR(CURRENT_TIMESTAMP, 'MM/DD/YYYY HH:MI AM'),
                processed = 'Yes',
                processed_dt = CURRENT_TIMESTAMP,
                status_icons = '{' || array_to_string(array_remove(array_append(string_to_array(REPLACE(REPLACE(COALESCE(status_icons, '{}'), '{', ''), '}', ''), ','), 'replied'), 'new'), ',') || '}'
            WHERE id = v_original_message_record_id;
            RAISE LOG 'Updated original message ID % for RxChangeResponse %', v_original_message_record_id, v_main_params.ss_message_id_header;
        ELSE
            RAISE WARNING 'Original message with ID % not found to update for RxChangeResponse %', v_main_params.ss_related_message_id, v_main_params.ss_message_id_header;
        END IF;
    END IF;

    RAISE LOG 'RxChangeResponse successfully parsed and inserted for ss_log_id: %. New form_ss_message_id: % ', p_ss_log_id, v_new_ss_message_id;
    RETURN QUERY SELECT v_new_ss_message_id, NULL::TEXT;
EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_error_text = MESSAGE_TEXT, v_error_context = PG_EXCEPTION_CONTEXT;
        RAISE LOG 'Error in _parse_and_insert_rxchangeresponse for ss_log_id: %. Error: %, Context: %', p_ss_log_id, v_error_text, v_error_context;
        INSERT INTO ss_error_log (
            error_message, error_context, error_type,
            table_name, table_id, application_name, additional_details
        ) VALUES (
            v_error_text, v_error_context, 'RXCHANGERESPONSE_PARSE_EXCEPTION',
            'form_ss_log', p_ss_log_id, 'SURESCRIPTS_INBOUND_PARSER', p_message_json
        );
        RETURN QUERY SELECT NULL::INTEGER, ('Error processing RxChangeResponse message (Log ID: ' || p_ss_log_id || '): ' || v_error_text)::TEXT;
END; $$;
