
CREATE INDEX IF NOT EXISTS idx_list_fdb_ndc_ndc ON form_list_fdb_ndc(ndc);
CREATE INDEX IF NOT EXISTS idx_list_fdb_ndc_price_ndc ON form_list_fdb_ndc_price(ndc, price_type, price_effective_dt);
CREATE INDEX IF NOT EXISTS idx_list_fdb_ndc_to_medid_ndc ON form_list_fdb_ndc_to_medid(ndc);
CREATE INDEX IF NOT EXISTS idx_list_fdb_ndc_to_therapy_ndc ON form_list_fdb_ndc_to_therapy(ndc);
CREATE INDEX IF NOT EXISTS idx_list_fdb_medicare_mstr_ndc ON form_list_fdb_medicare_mstr(ndc);
CREATE INDEX IF NOT EXISTS idx_list_cms_ndc_hcpc_cw_ndc ON form_list_cms_ndc_hcpc_cw(ndc);
CREATE INDEX IF NOT EXISTS idx_list_fdb_ndc_dose_form_link_ndc ON form_list_fdb_ndc_dose_form_link(ndc);
CREATE INDEX IF NOT EXISTS idx_list_fdb_ndc_to_route_ndc ON form_list_fdb_ndc_to_route(ndc);
CREATE INDEX IF NOT EXISTS idx_list_fdb_ndc_attribute_ndc ON form_list_fdb_ndc_attribute(ndc);
CREATE INDEX IF NOT EXISTS idx_list_fdb_tm_ndc_ndc ON form_list_fdb_tm_ndc(ndc);

-- For GCN sequence number queries
CREATE INDEX IF NOT EXISTS idx_list_fdb_ndc_gcn_seqno ON form_list_fdb_ndc(gcn_seqno);
CREATE INDEX IF NOT EXISTS idx_list_fdb_clinc_ingred_strength_gcn_seqno ON form_list_fdb_clinc_ingred_strength(gcn_seqno);

-- For HCPC price lookups
CREATE INDEX IF NOT EXISTS idx_list_fdb_medicare_price_hcpc ON form_list_fdb_medicare_price(hcpc);
CREATE INDEX IF NOT EXISTS idx_list_fdb_medicare_price_ndc ON form_list_fdb_medicare_price(ndc);

CREATE INDEX IF NOT EXISTS idx_inventory_id ON form_inventory(id);
CREATE INDEX IF NOT EXISTS idx_inventory_ndc ON form_inventory(ndc);
CREATE INDEX IF NOT EXISTS idx_inventory_price_code ON form_inventory(price_code_id);

-- Patient and insurance indexes
CREATE INDEX IF NOT EXISTS idx_patient_insurance_patient_id ON form_patient_insurance(patient_id);
CREATE INDEX IF NOT EXISTS idx_patient_address_patient_id ON form_patient_address(patient_id);

-- Contract and pricing indexes
CREATE INDEX IF NOT EXISTS idx_payer_contract_matrix_id ON form_payer_contract(assigned_matrix_id);
CREATE INDEX IF NOT EXISTS idx_price_matrix_item_inventory ON form_payer_price_matrix_item(inventory_id);
CREATE INDEX IF NOT EXISTS idx_price_matrix_item_matrix ON form_payer_price_matrix_item(payer_price_matrix_id);
CREATE INDEX IF NOT EXISTS idx_price_formulas_category ON form_price_code_formulas(code_category);

-- Supplementary indexes
CREATE INDEX IF NOT EXISTS idx_cms_dme_rural_zip ON form_list_cms_dme_rural(dmeposruralzipcode);
CREATE INDEX IF NOT EXISTS idx_cms_dmepos_hcpcs ON form_list_cms_dmepos(hcpcs, mod);

CREATE INDEX IF NOT EXISTS idx_inv_price_code ON form_inventory(price_code_id);
CREATE INDEX IF NOT EXISTS idx_contract_matrix_id ON form_payer_contract(assigned_matrix_id);
CREATE INDEX IF NOT EXISTS idx_pcf_code_category ON form_price_code_formulas(code_category);
CREATE INDEX IF NOT EXISTS idx_site_price_code ON form_site_price_code_item(price_code_id);
