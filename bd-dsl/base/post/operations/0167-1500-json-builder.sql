-- Helper function to convert CMS-1500 claim record to JSON (similar to ncpdp_record_to_json)
CREATE OR REPLACE FUNCTION mm_1500_record_to_json(
  p_claim_record mm_1500_claim_record
) RETURNS jsonb AS $BODY$
DECLARE
  v_result jsonb;
  v_diagnosis_json jsonb := NULL;
  v_service_lines_json jsonb := NULL;
BEGIN
  -- Return NULL if input is NULL
  IF p_claim_record IS NULL THEN
    RETURN NULL;
  END IF;
  
  -- Process diagnosis subform if not null
  IF p_claim_record.subform_dx IS NOT NULL THEN
    BEGIN
      WITH diagnosis_fields AS (
        SELECT 
          ROW_NUMBER() OVER () as dx_row,
          dx.patient_id,
          dx.dx_id,
          dx.diagnosis_code
        FROM unnest(p_claim_record.subform_dx) dx
      )
      SELECT jsonb_agg(
        jsonb_build_object(
          'patient_id', patient_id,
          'dx_id', dx_id,
          'diagnosis_code', diagnosis_code
        )
      ) INTO v_diagnosis_json
      FROM diagnosis_fields;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in diagnosis subform: %', SQLERRM;
      -- Simpler approach for diagnosis subform
      BEGIN
        SELECT jsonb_agg(
          jsonb_build_object(
            'patient_id', COALESCE(dx.patient_id, 0),
            'dx_id', COALESCE(dx.dx_id, 0),
            'diagnosis_code', COALESCE(dx.diagnosis_code, '')
          )
        ) INTO v_diagnosis_json
        FROM unnest(p_claim_record.subform_dx) dx;
        
        IF v_diagnosis_json IS NULL THEN
          v_diagnosis_json := jsonb_build_array(
            jsonb_build_object(
              'error', 'Could not parse diagnosis subform'
            )
          );
        END IF;
      EXCEPTION WHEN OTHERS THEN
        v_diagnosis_json := NULL;
      END;
    END;
  END IF;

  -- Process service lines subform if not null
  IF p_claim_record.subform_sl IS NOT NULL THEN
    BEGIN
      WITH service_line_fields AS (
        SELECT 
          ROW_NUMBER() OVER () as sl_row,
          sl.rx_no,
          sl.charge_no,
          sl.patient_id,
          sl.service_date,
          sl.service_date_end,
          sl.inventory_id,
          sl.type,
          sl.line_item_charge_amount,
          sl.line_item_paid_amount,
          sl.measurement_unit,
          sl.service_unit_count,
          sl.place_of_service_code,
          sl.modifier_1,
          sl.modifier_2,
          sl.modifier_3,
          sl.modifier_4,
          sl.dx_filter,
          sl.dx_id_1,
          sl.dx_id_2,
          sl.dx_id_3,
          sl.dx_id_4,
          sl.procedure_code,
          sl.supplemental_info
        FROM unnest(p_claim_record.subform_sl) sl
      )
      SELECT jsonb_agg(
        jsonb_build_object(
          'rx_no', rx_no,
          'charge_no', charge_no,
          'patient_id', patient_id,
          'service_date', service_date,
          'service_date_end', service_date_end,
          'inventory_id', inventory_id,
          'type', type,
          'line_item_charge_amount', line_item_charge_amount,
          'line_item_paid_amount', line_item_paid_amount,
          'measurement_unit', measurement_unit,
          'service_unit_count', service_unit_count,
          'place_of_service_code', place_of_service_code,
          'modifier_1', modifier_1,
          'modifier_2', modifier_2,
          'modifier_3', modifier_3,
          'modifier_4', modifier_4,
          'dx_filter', CASE WHEN dx_filter IS NOT NULL THEN to_jsonb(dx_filter) ELSE NULL END,
          'dx_id_1', dx_id_1,
          'dx_id_2', dx_id_2,
          'dx_id_3', dx_id_3,
          'dx_id_4', dx_id_4,
          'procedure_code', procedure_code,
          'supplemental_info', supplemental_info
        )
      ) INTO v_service_lines_json
      FROM service_line_fields;
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Exception in service lines subform: %', SQLERRM;
      -- Simpler approach for service lines subform
      BEGIN
        SELECT jsonb_agg(
          jsonb_build_object(
            'rx_no', COALESCE(sl.rx_no, ''),
            'charge_no', COALESCE(sl.charge_no, ''),
            'patient_id', COALESCE(sl.patient_id, 0),
            'service_date', COALESCE(sl.service_date, ''),
            'inventory_id', COALESCE(sl.inventory_id, 0),
            'line_item_charge_amount', COALESCE(sl.line_item_charge_amount, 0),
            'procedure_code', COALESCE(sl.procedure_code, '')
          )
        ) INTO v_service_lines_json
        FROM unnest(p_claim_record.subform_sl) sl;
        
        IF v_service_lines_json IS NULL THEN
          v_service_lines_json := jsonb_build_array(
            jsonb_build_object(
              'error', 'Could not parse service lines subform'
            )
          );
        END IF;
      EXCEPTION WHEN OTHERS THEN
        v_service_lines_json := NULL;
      END;
    END;
  END IF;

  -- Create the full JSON object with all fields by building up sections
  -- Patient section
  v_result := jsonb_build_object(
    'parent_claim_no', p_claim_record.parent_claim_no,
    'claim_no', p_claim_record.claim_no,
    'patient_id', p_claim_record.patient_id,
    'status', p_claim_record.status,
    'substatus_id', p_claim_record.substatus_id,
    'site_id', p_claim_record.site_id,
    'insurance_id', p_claim_record.insurance_id,
    'payer_id', p_claim_record.payer_id,
    'insurance_type', p_claim_record.insurance_type,
    'claim_resubmission_code', p_claim_record.claim_resubmission_code,
    'control_number', p_claim_record.control_number,
    'claim_codes', p_claim_record.claim_codes
  );

  -- Add patient information
  v_result := v_result || jsonb_build_object(
    'patient_first_name', p_claim_record.patient_first_name,
    'patient_last_name', p_claim_record.patient_last_name,
    'patient_middle_name', p_claim_record.patient_middle_name,
    'patient_dob', p_claim_record.patient_dob,
    'patient_gender', p_claim_record.patient_gender,
    'patient_phone_number', p_claim_record.patient_phone_number,
    'patient_address1', p_claim_record.patient_address1,
    'patient_address2', p_claim_record.patient_address2,
    'patient_city', p_claim_record.patient_city,
    'patient_state_id', p_claim_record.patient_state_id,
    'patient_postal_code', p_claim_record.patient_postal_code,
    'patient_account_number', p_claim_record.patient_account_number,
    'patient_signature_date', p_claim_record.patient_signature_date
  );

  -- Add subscriber information
  v_result := v_result || jsonb_build_object(
    'subscriber_payer_organization_name', p_claim_record.subscriber_payer_organization_name,
    'subscriber_member_id', p_claim_record.subscriber_member_id,
    'subscriber_insurance_group_or_policy_number', p_claim_record.subscriber_insurance_group_or_policy_number,
    'pa_id', p_claim_record.pa_id,
    'prior_authorization_number', p_claim_record.prior_authorization_number,
    'subscriber_payment_responsibility_level_code', p_claim_record.subscriber_payment_responsibility_level_code,
    'subscriber_relationship_id', p_claim_record.subscriber_relationship_id,
    'subscriber_first_name', p_claim_record.subscriber_first_name,
    'subscriber_last_name', p_claim_record.subscriber_last_name,
    'subscriber_middle_name', p_claim_record.subscriber_middle_name,
    'subscriber_dob', p_claim_record.subscriber_dob,
    'subscriber_gender', p_claim_record.subscriber_gender,
    'subscriber_phone_number', p_claim_record.subscriber_phone_number,
    'subscriber_address1', p_claim_record.subscriber_address1,
    'subscriber_address2', p_claim_record.subscriber_address2,
    'subscriber_city', p_claim_record.subscriber_city,
    'subscriber_state_id', p_claim_record.subscriber_state_id,
    'subscriber_postal_code', p_claim_record.subscriber_postal_code
  );

  -- Add referring provider information
  v_result := v_result || jsonb_build_object(
    'referring_provider_id', p_claim_record.referring_provider_id,
    'referring_provider_physician_id', p_claim_record.referring_provider_physician_id,
    'referring_provider_first_name', p_claim_record.referring_provider_first_name,
    'referring_provider_last_name', p_claim_record.referring_provider_last_name,
    'referring_provider_npi', p_claim_record.referring_provider_npi,
    'referring_provider_state_license_number', p_claim_record.referring_provider_state_license_number,
    'referring_provider_taxonomy_id', p_claim_record.referring_provider_taxonomy_id,
    'referring_provider_qualifier', p_claim_record.referring_provider_qualifier,
    'referring_provider_alt_id', p_claim_record.referring_provider_alt_id
  );

  -- Add billing provider information
  v_result := v_result || jsonb_build_object(
    'bill_organization_name', p_claim_record.bill_organization_name,
    'npi', p_claim_record.npi,
    'bill_address1', p_claim_record.bill_address1,
    'bill_address2', p_claim_record.bill_address2,
    'bill_city', p_claim_record.bill_city,
    'bill_state_id', p_claim_record.bill_state_id,
    'bill_phone', p_claim_record.bill_phone,
    'bill_tax_id', p_claim_record.bill_tax_id,
    'bill_alt_provider_id', p_claim_record.bill_alt_provider_id
  );

  -- Add financial and COB information
  v_result := v_result || jsonb_build_object(
    'billed', p_claim_record.billed,
    'expected', p_claim_record.expected,
    'paid', p_claim_record.paid,
    'cob_insurance_id', p_claim_record.cob_insurance_id,
    'cob_payer_id', p_claim_record.cob_payer_id,
    'cob_organization_name', p_claim_record.cob_organization_name,
    'cob_insurance_group_or_policy_number', p_claim_record.cob_insurance_group_or_policy_number,
    'other_payer_claim_control_number', p_claim_record.other_payer_claim_control_number,
    'cob_first_name', p_claim_record.cob_first_name,
    'cob_last_name', p_claim_record.cob_last_name,
    'cob_middle_name', p_claim_record.cob_middle_name
  );

  -- Add subform data
  v_result := v_result || jsonb_build_object(
    'subform_dx', v_diagnosis_json,
    'subform_sl', v_service_lines_json
  );

  -- Strip null values for cleaner JSON
  v_result := jsonb_strip_nulls(v_result);
  
  RETURN v_result;
END;
$BODY$ LANGUAGE plpgsql IMMUTABLE;
