-- Trigger to sync status from form_careplan_delivery_tick to form_careplan_dt_item and form_careplan_dt_wt_pulled
CREATE OR REPLACE FUNCTION sync_dt_status()
RETURNS TRIGGER AS $$
BEGIN
    BEGIN
        -- Check if the status is not null
        IF NEW.status IS NOT NULL THEN
            -- Update status in form_careplan_dt_item where ticket_no matches
            UPDATE form_careplan_dt_item dti
            SET status = NEW.status
            WHERE ticket_no = NEW.ticket_no
            AND dti.archived IS NOT TRUE
            AND dti.deleted IS NOT TRUE;

            -- Update status in form_careplan_dt_wt_pulled where ticket_no matches
            UPDATE form_careplan_dt_wt_pulled wtp
            SET status = NEW.status, void = CASE WHEN NEW.status = 'voided' THEN 'Yes' ELSE NULL::text END
            WHERE ticket_no = NEW.ticket_no
            AND COALESCE(wtp.void,'No') <> 'Yes'
            AND wtp.archived IS NOT TRUE
            AND wtp.deleted IS NOT TRUE;
        END IF;
    EXCEPTION WHEN OTHERS THEN
        -- Raise an error if something goes wrong during the update
        RAISE EXCEPTION 'Error syncing status to dt_item and dt_wt_pulled: %', SQLERRM;
    END;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger on form_careplan_delivery_tick for status updates
CREATE OR REPLACE TRIGGER trigger_sync_dt_status
AFTER UPDATE OF status ON form_careplan_delivery_tick
FOR EACH ROW
EXECUTE FUNCTION sync_dt_status();
