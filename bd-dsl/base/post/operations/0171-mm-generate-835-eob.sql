-- EOB Site Data Query
-- This would be called with endpoint: /eob_site_data?id={835_response_id}
CREATE OR REPLACE FUNCTION eob_site_data(p_id integer)
RETURNS TABLE (
    site_name text,
    site_logo jsonb,
    site_address1 text,
    site_address2 text,
    site_city text,
    site_state text,
    site_zip text
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT
        s.name::text AS site_name,
        s.logo::jsonb AS site_logo,
        s.address1::text AS site_address1,
        s.address2::text AS site_address2,
        s.city::text AS site_city,
        s.state_id::text AS site_state,
        s.zip::text AS site_zip
    FROM form_med_claim_resp_835 resp
    JOIN form_site s ON s.id = resp.site_id
    WHERE resp.id = p_id
        AND resp.deleted IS NOT TRUE 
        AND resp.archived IS NOT TRUE
        AND s.deleted IS NOT TRUE 
        AND s.archived IS NOT TRUE
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- EOB Header Data Query (Updated)
-- This would be called with endpoint: /eob_header_data?id={835_response_id}
CREATE OR REPLACE FUNCTION eob_header_data(p_id integer)
RETURNS TABLE (
    payer_name text,
    payer_organization text,
    production_date date,
    total_charge_amount numeric,
    total_paid_amount numeric,
    total_pt_pay numeric,
    patient_first_name text,
    patient_last_name text,
    patient_member_id text,
    patient_mrn text,
    ordering_provider_name text
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT
        batch.payer_name::text,
        py.organization::text AS payer_organization,
        TO_CHAR(batch.production_date, 'MM/DD/YYYY') as production_date,
        resp.total_charge_amount::numeric,
        resp.total_paid_amount::numeric,
        resp.total_pt_pay::numeric,
        resp.patient_first_name::text,
        resp.patient_last_name::text,
        resp.patient_member_id::text,
        pt.mrn::text AS patient_mrn,
        prov.last || ', ' || prov.first || COALESCE(' ' || prov.title, '')::text AS ordering_provider_name
    FROM form_med_claim_resp_835 resp
    JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
    LEFT JOIN form_patient pt ON pt.id = resp.patient_id
    LEFT JOIN form_payer py ON py.id = resp.payer_id
    LEFT JOIN form_physician prov ON prov.id = (
        SELECT prov_ord.physician_id
        FROM form_med_claim mcl
        JOIN sf_form_med_claim_to_med_claim_provs sf_prov ON sf_prov.form_med_claim_fk = mcl.id
        JOIN form_med_claim_provs provs ON provs.id = sf_prov.form_med_claim_provs_fk
        JOIN sf_form_med_claim_provs_to_med_claim_prov_ord sf_ord ON sf_ord.form_med_claim_provs_fk = provs.id
        JOIN form_med_claim_prov_ord prov_ord ON prov_ord.id = sf_ord.form_med_claim_prov_ord_fk
        WHERE mcl.claim_no = resp.claim_no
          AND mcl.deleted IS NOT TRUE 
          AND mcl.archived IS NOT TRUE
          AND sf_prov.delete IS NOT TRUE 
          AND sf_prov.archive IS NOT TRUE
          AND provs.deleted IS NOT TRUE 
          AND provs.archived IS NOT TRUE
          AND sf_ord.delete IS NOT TRUE 
          AND sf_ord.archive IS NOT TRUE
          AND prov_ord.deleted IS NOT TRUE 
          AND prov_ord.archived IS NOT TRUE
        LIMIT 1
    )
    WHERE resp.id = p_id
        AND resp.deleted IS NOT TRUE 
        AND resp.archived IS NOT TRUE
        AND batch.deleted IS NOT TRUE 
        AND batch.archived IS NOT TRUE
        AND (pt.deleted IS NOT TRUE OR pt.deleted IS NULL)
        AND (pt.archived IS NOT TRUE OR pt.archived IS NULL)
        AND (py.deleted IS NOT TRUE OR py.deleted IS NULL)
        AND (py.archived IS NOT TRUE OR py.archived IS NULL)
        AND (prov.deleted IS NOT TRUE OR prov.deleted IS NULL)
        AND (prov.archived IS NOT TRUE OR prov.archived IS NULL)
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- EOB Claims Data Query (Updated)
-- This would be called with endpoint: /eob_claims_data?id={835_response_id}
CREATE OR REPLACE FUNCTION eob_claims_data(p_id integer)
RETURNS TABLE (
    claim_id integer,
    patient_control_number text,
    patient_first_name text,
    patient_last_name text,
    patient_member_id text,
    ordering_provider_name text,
    service_lines jsonb,
    claim_adjustments jsonb
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        resp.id::integer AS claim_id,
        resp.patient_control_number::text,
        resp.patient_first_name::text,
        resp.patient_last_name::text,
        resp.patient_member_id::text,
        prov.last || ', ' || prov.first || COALESCE(' ' || prov.title, '')::text AS ordering_provider_name,
        (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'service_date', TO_CHAR(sl.service_date, 'MM/DD/YYYY'),
                    'line_item_control_number', sl.line_item_control_number,
                    'submitted_procedure_code_description', sl.submitted_procedure_code_description,
                    'line_item_charge_amount', sl.line_item_charge_amount,
                    'allowed_actual', sl.allowed_actual,
                    'line_item_provider_payment_amount', sl.line_item_provider_payment_amount,
                    'deduction_amount', sl.deduction_amount,
                    'pt_copay', (
                        SELECT COALESCE(SUM(sa.adjustment_amount), 0)
                        FROM form_med_claim_resp_835_sl_adj sa
                        JOIN sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj sfa
                            ON sfa.form_med_claim_resp_835_sl_adj_fk = sa.id
                        WHERE sfa.form_med_claim_resp_835_sl_fk = sl.id
                            AND sfa.delete IS NOT TRUE
                            AND sfa.archive IS NOT TRUE
                            AND sa.deleted IS NOT TRUE
                            AND sa.archived IS NOT TRUE
                            AND sa.claim_adjustment_group_code = 'PR'
                    ),
                    'service_adjustments', (
                        SELECT jsonb_agg(
                            jsonb_build_object(
                                'line_item_control_number', sl.line_item_control_number,
                                'claim_adjustment_group_code', sa.claim_adjustment_group_code,
                                'claim_adjustment_group_code_value', sa.claim_adjustment_group_code_value,
                                'adjustment_reason_code', sa.adjustment_reason_code,
                                'adjustment_reason_code_value', ecl.name,
                                'adjustment_amount', sa.adjustment_amount,
                                'adjustment_quantity', sa.adjustment_quantity
                            ) ORDER BY sa.id
                        )
                        FROM form_med_claim_resp_835_sl_adj sa
                        JOIN sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj sfa
                            ON sfa.form_med_claim_resp_835_sl_adj_fk = sa.id
                        LEFT JOIN form_list_med_claim_ecl ecl
                            ON ecl.code = sa.adjustment_reason_code
                            AND ecl.field = 'CARC'
                            AND ecl.deleted IS NOT TRUE
                            AND ecl.archived IS NOT TRUE
                        WHERE sfa.form_med_claim_resp_835_sl_fk = sl.id
                            AND sfa.delete IS NOT TRUE
                            AND sfa.archive IS NOT TRUE
                            AND sa.deleted IS NOT TRUE
                            AND sa.archived IS NOT TRUE
                    )
                ) ORDER BY sl.service_date, sl.line_item_control_number
            )
            FROM form_med_claim_resp_835_sl sl
            JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_sl sf 
                ON sf.form_med_claim_resp_835_sl_fk = sl.id
            WHERE sf.form_med_claim_resp_835_fk = resp.id
                AND sf.delete IS NOT TRUE
                AND sf.archive IS NOT TRUE
                AND sl.deleted IS NOT TRUE
                AND sl.archived IS NOT TRUE
        ) AS service_lines,
        (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'claim_adjustment_group_code', ca.claim_adjustment_group_code,
                    'claim_adjustment_group_code_value', ca.claim_adjustment_group_code_value,
                    'adjustment_reason_code', ca.adjustment_reason_code,
                    'adjustment_reason_code_value', ecl.name,
                    'adjustment_amount', ca.adjustment_amount,
                    'adjustment_quantity', ca.adjustment_quantity
                ) ORDER BY ca.id
            )
            FROM form_med_claim_resp_835_adj ca
            JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_adj sfc
                ON sfc.form_med_claim_resp_835_adj_fk = ca.id
            LEFT JOIN form_list_med_claim_ecl ecl
                ON ecl.code = ca.adjustment_reason_code
                AND ecl.field = 'CARC'
                AND ecl.deleted IS NOT TRUE
                AND ecl.archived IS NOT TRUE
            WHERE sfc.form_med_claim_resp_835_fk = resp.id
                AND sfc.delete IS NOT TRUE
                AND sfc.archive IS NOT TRUE
                AND ca.deleted IS NOT TRUE
                AND ca.archived IS NOT TRUE
        ) AS claim_adjustments
    FROM form_med_claim_resp_835 resp
    LEFT JOIN form_physician prov ON prov.id = (
        SELECT prov_ord.physician_id
        FROM form_med_claim mcl
        JOIN sf_form_med_claim_to_med_claim_provs sf_prov ON sf_prov.form_med_claim_fk = mcl.id
        JOIN form_med_claim_provs provs ON provs.id = sf_prov.form_med_claim_provs_fk
        JOIN sf_form_med_claim_provs_to_med_claim_prov_ord sf_ord ON sf_ord.form_med_claim_provs_fk = provs.id
        JOIN form_med_claim_prov_ord prov_ord ON prov_ord.id = sf_ord.form_med_claim_prov_ord_fk
        WHERE mcl.claim_no = resp.claim_no
          AND mcl.deleted IS NOT TRUE 
          AND mcl.archived IS NOT TRUE
          AND sf_prov.delete IS NOT TRUE 
          AND sf_prov.archive IS NOT TRUE
          AND provs.deleted IS NOT TRUE 
          AND provs.archived IS NOT TRUE
          AND sf_ord.delete IS NOT TRUE 
          AND sf_ord.archive IS NOT TRUE
          AND prov_ord.deleted IS NOT TRUE 
          AND prov_ord.archived IS NOT TRUE
        LIMIT 1
    )
    WHERE resp.id = p_id
        AND resp.deleted IS NOT TRUE 
        AND resp.archived IS NOT TRUE
        AND (prov.deleted IS NOT TRUE OR prov.deleted IS NULL)
        AND (prov.archived IS NOT TRUE OR prov.archived IS NULL);
END;
$$ LANGUAGE plpgsql;

-- Alternative: Single comprehensive query for all EOB data
CREATE OR REPLACE FUNCTION eob_full_data(p_id integer)
RETURNS TABLE (
    -- Header fields
    payer_name text,
    payer_address_1 text,
    payer_address_2 text,
    payer_city text,
    payer_state text,
    payer_zip text,
    payer_identification_number text,
    production_date date,
    check_or_eft_trace_number text,
    total_actual_provider_payment_amount numeric,
    
    -- Claim level fields
    claim_id integer,
    patient_control_number text,
    patient_first_name text,
    patient_last_name text,
    patient_middle_name text,
    patient_member_id text,
    rendering_provider_name text,
    rendering_provider_npi text,
    claim_status_code text,
    total_charge_amount numeric,
    total_paid_amount numeric,
    total_adjusted_amount numeric,
    total_pt_pay numeric,
    
    -- Service line details (as JSON array)
    service_lines jsonb,
    
    -- Claim adjustments (as JSON array)
    claim_adjustments jsonb,
    
    -- Medicare specific fields
    mcr_reimbursement_rate numeric,
    mcr_hcpcs_payable_amount numeric,
    rmk_cd text[]
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        -- Header fields from batch
        batch.payer_name::text,
        batch.payer_address_1::text,
        batch.payer_address_2::text,
        batch.payer_city::text,
        batch.payer_state::text,
        batch.payer_zip::text,
        batch.payer_identification_number::text,
        batch.production_date::date,
        batch.check_or_eft_trace_number::text,
        batch.total_actual_provider_payment_amount::numeric,
        
        -- Claim level fields
        resp.id::integer AS claim_id,
        resp.patient_control_number::text,
        resp.patient_first_name::text,
        resp.patient_last_name::text,
        resp.patient_middle_name::text,
        resp.patient_member_id::text,
        resp.rendering_provider_name::text,
        resp.rendering_provider_npi::text,
        resp.claim_status_code::text,
        resp.total_charge_amount::numeric,
        resp.total_paid_amount::numeric,
        resp.total_adjusted_amount::numeric,
        resp.total_pt_pay::numeric,
        
        -- Service lines as JSON
        (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'line_item_control_number', sl.line_item_control_number,
                    'service_date', TO_CHAR(sl.service_date, 'MM/DD/YYYY'),
                    'service_start_date', sl.service_start_date,
                    'service_end_date', sl.service_end_date,
                    'adjudicated_procedure_code', sl.adjudicated_procedure_code,
                    'adjudicated_procedure_modifier_1', sl.adjudicated_procedure_modifier_1,
                    'adjudicated_procedure_modifier_2', sl.adjudicated_procedure_modifier_2,
                    'adjudicated_procedure_modifier_3', sl.adjudicated_procedure_modifier_3,
                    'adjudicated_procedure_modifier_4', sl.adjudicated_procedure_modifier_4,
                    'submitted_procedure_code', sl.submitted_procedure_code,
                    'submitted_procedure_code_description', sl.submitted_procedure_code_description,
                    'line_item_charge_amount', sl.line_item_charge_amount,
                    'line_item_provider_payment_amount', sl.line_item_provider_payment_amount,
                    'allowed_actual', sl.allowed_actual,
                    'deduction_amount', sl.deduction_amount,
                    'revenue_code', sl.revenue_code,
                    'units_of_service_paid_count', sl.units_of_service_paid_count,
                    'original_units_of_service_count', sl.original_units_of_service_count,
                    'rmk_cd', sl.rmk_cd,
                    'pt_copay', (
                        SELECT COALESCE(SUM(sa.adjustment_amount), 0)
                        FROM form_med_claim_resp_835_sl_adj sa
                        JOIN sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj sfa
                            ON sfa.form_med_claim_resp_835_sl_adj_fk = sa.id
                        WHERE sfa.form_med_claim_resp_835_sl_fk = sl.id
                            AND sfa.delete IS NOT TRUE
                            AND sfa.archive IS NOT TRUE
                            AND sa.deleted IS NOT TRUE
                            AND sa.archived IS NOT TRUE
                            AND sa.claim_adjustment_group_code = 'PR'
                    ),
                    'service_adjustments', (
                        SELECT jsonb_agg(
                            jsonb_build_object(
                                'claim_adjustment_group_code', sa.claim_adjustment_group_code,
                                'claim_adjustment_group_code_value', sa.claim_adjustment_group_code_value,
                                'adjustment_reason_code', sa.adjustment_reason_code,
                                'adjustment_reason_code_value', ecl.name,
                                'adjustment_amount', sa.adjustment_amount,
                                'adjustment_quantity', sa.adjustment_quantity
                            ) ORDER BY sa.id
                        )
                        FROM form_med_claim_resp_835_sl_adj sa
                        JOIN sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj sfa
                            ON sfa.form_med_claim_resp_835_sl_adj_fk = sa.id
                        LEFT JOIN form_list_med_claim_ecl ecl
                            ON ecl.code = sa.adjustment_reason_code
                            AND ecl.field = 'CARC'
                            AND ecl.deleted IS NOT TRUE
                            AND ecl.archived IS NOT TRUE
                        WHERE sfa.form_med_claim_resp_835_sl_fk = sl.id
                            AND sfa.delete IS NOT TRUE
                            AND sfa.archive IS NOT TRUE
                            AND sa.deleted IS NOT TRUE
                            AND sa.archived IS NOT TRUE
                    )
                ) ORDER BY sl.service_date, sl.line_item_control_number
            )
            FROM form_med_claim_resp_835_sl sl
            JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_sl sf 
                ON sf.form_med_claim_resp_835_sl_fk = sl.id
            WHERE sf.form_med_claim_resp_835_fk = resp.id
                AND sf.delete IS NOT TRUE
                AND sf.archive IS NOT TRUE
                AND sl.deleted IS NOT TRUE
                AND sl.archived IS NOT TRUE
        ) AS service_lines,
        
        -- Claim adjustments as JSON
        (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'claim_adjustment_group_code', ca.claim_adjustment_group_code,
                    'claim_adjustment_group_code_value', ca.claim_adjustment_group_code_value,
                    'adjustment_reason_code', ca.adjustment_reason_code,
                    'adjustment_reason_code_value', ecl.name,
                    'adjustment_amount', ca.adjustment_amount,
                    'adjustment_quantity', ca.adjustment_quantity
                ) ORDER BY ca.id
            )
            FROM form_med_claim_resp_835_adj ca
            JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_adj sfc
                ON sfc.form_med_claim_resp_835_adj_fk = ca.id
            LEFT JOIN form_list_med_claim_ecl ecl
                ON ecl.code = ca.adjustment_reason_code
                AND ecl.field = 'CARC'
                AND ecl.deleted IS NOT TRUE
                AND ecl.archived IS NOT TRUE
            WHERE sfc.form_med_claim_resp_835_fk = resp.id
                AND sfc.delete IS NOT TRUE
                AND sfc.archive IS NOT TRUE
                AND ca.deleted IS NOT TRUE
                AND ca.archived IS NOT TRUE
        ) AS claim_adjustments,
        
        -- Medicare specific fields
        resp.mcr_reimbursement_rate::numeric,
        resp.mcr_hcpcs_payable_amount::numeric,
        resp.rmk_cd::text[]
        
    FROM form_med_claim_resp_835 resp
    JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
    WHERE resp.id = p_id
        AND resp.deleted IS NOT TRUE 
        AND resp.archived IS NOT TRUE
        AND batch.deleted IS NOT TRUE 
        AND batch.archived IS NOT TRUE;
END;
$$ LANGUAGE plpgsql;

-- Query to get all claims in a batch for multi-claim EOBs
CREATE OR REPLACE FUNCTION eob_batch_claims(p_batch_id integer)
RETURNS TABLE (
    claim_id integer,
    patient_control_number text,
    patient_first_name text,
    patient_last_name text,
    patient_member_id text,
    rendering_provider_name text,
    claim_status_code text,
    total_charge_amount numeric,
    total_paid_amount numeric,
    total_pt_pay numeric
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        resp.id::integer AS claim_id,
        resp.patient_control_number::text,
        resp.patient_first_name::text,
        resp.patient_last_name::text,
        resp.patient_member_id::text,
        resp.rendering_provider_name::text,
        resp.claim_status_code::text,
        resp.total_charge_amount::numeric,
        resp.total_paid_amount::numeric,
        resp.total_pt_pay::numeric
    FROM form_med_claim_resp_835 resp
    JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
    WHERE batch.id = p_batch_id
        AND batch.deleted IS NOT TRUE 
        AND batch.archived IS NOT TRUE
        AND resp.deleted IS NOT TRUE 
        AND resp.archived IS NOT TRUE
    ORDER BY resp.patient_last_name, resp.patient_first_name, resp.id;
END;
$$ LANGUAGE plpgsql;

-- New function: EOB Header Data by Batch ID
-- This accepts a batch ID and returns header data for the first claim in the batch
CREATE OR REPLACE FUNCTION eob_header_data_by_batch(p_batch_id integer)
RETURNS TABLE (
    payer_name text,
    payer_organization text,
    production_date date,
    total_charge_amount numeric,
    total_paid_amount numeric,
    total_pt_pay numeric,
    patient_first_name text,
    patient_last_name text,
    patient_member_id text,
    patient_mrn text,
    ordering_provider_name text
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT
        batch.payer_name::text,
        py.organization::text AS payer_organization,
        batch.production_date::date,
        resp.total_charge_amount::numeric,
        resp.total_paid_amount::numeric,
        resp.total_pt_pay::numeric,
        resp.patient_first_name::text,
        resp.patient_last_name::text,
        resp.patient_member_id::text,
        pt.mrn::text AS patient_mrn,
        prov.last || ', ' || prov.first || COALESCE(' ' || prov.title, '')::text AS ordering_provider_name
    FROM form_med_claim_resp_835_batch batch
    JOIN form_med_claim_resp_835 resp ON resp.response_id = batch.response_id
    LEFT JOIN form_patient pt ON pt.id = resp.patient_id
    LEFT JOIN form_payer py ON py.id = resp.payer_id
    LEFT JOIN form_physician prov ON prov.id = (
        SELECT prov_ord.physician_id
        FROM form_med_claim mcl
        JOIN sf_form_med_claim_to_med_claim_provs sf_prov ON sf_prov.form_med_claim_fk = mcl.id
        JOIN form_med_claim_provs provs ON provs.id = sf_prov.form_med_claim_provs_fk
        JOIN sf_form_med_claim_provs_to_med_claim_prov_ord sf_ord ON sf_ord.form_med_claim_provs_fk = provs.id
        JOIN form_med_claim_prov_ord prov_ord ON prov_ord.id = sf_ord.form_med_claim_prov_ord_fk
        WHERE mcl.claim_no = resp.claim_no
          AND mcl.deleted IS NOT TRUE 
          AND mcl.archived IS NOT TRUE
          AND sf_prov.delete IS NOT TRUE 
          AND sf_prov.archive IS NOT TRUE
          AND provs.deleted IS NOT TRUE 
          AND provs.archived IS NOT TRUE
          AND sf_ord.delete IS NOT TRUE 
          AND sf_ord.archive IS NOT TRUE
          AND prov_ord.deleted IS NOT TRUE 
          AND prov_ord.archived IS NOT TRUE
        LIMIT 1
    )
    WHERE batch.id = p_batch_id
        AND batch.deleted IS NOT TRUE 
        AND batch.archived IS NOT TRUE
        AND resp.deleted IS NOT TRUE 
        AND resp.archived IS NOT TRUE
        AND (pt.deleted IS NOT TRUE OR pt.deleted IS NULL)
        AND (pt.archived IS NOT TRUE OR pt.archived IS NULL)
        AND (py.deleted IS NOT TRUE OR py.deleted IS NULL)
        AND (py.archived IS NOT TRUE OR py.archived IS NULL)
        AND (prov.deleted IS NOT TRUE OR prov.deleted IS NULL)
        AND (prov.archived IS NOT TRUE OR prov.archived IS NULL)
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;