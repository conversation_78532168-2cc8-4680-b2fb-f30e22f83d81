-- bd-dsl/base/post/operations/0146-ss-outbound-validation-rules.sql
-- Contains functions for validating outbound Surescripts messages before sending.

-- Helper function to check if a Surescripts message thread is canceled
CREATE OR REPLACE FUNCTION fn_is_surescripts_thread_canceled(p_ss_message_id INTEGER)
RETURNS BOOLEAN LANGUAGE plpgsql AS $$
DECLARE
    v_canceled_id INTEGER;
BEGIN
    WITH original_message AS (
        SELECT physician_order_id
        FROM form_ss_message
        WHERE id = p_ss_message_id
    )
    SELECT ssrec.id INTO v_canceled_id
    FROM form_ss_message ssrec
    INNER JOIN original_message ON ssrec.physician_order_id = original_message.physician_order_id
    WHERE ssrec.message_type = 'CancelRx'
      AND ssrec.deleted IS NOT TRUE
      AND ssrec.archived IS NOT TRUE
    LIMIT 1;

    IF v_canceled_id IS NOT NULL THEN
        RETURN TRUE;
    END IF;
    RETURN FALSE;
END;
$$;

-- Helper function to calculate refills remaining
CREATE OR REPLACE FUNCTION fn_get_refills_remaining(
    p_pharmacy_rx_no TEXT,
    p_site_id INTEGER,
    p_total_authorized_refills INTEGER
)
RETURNS INTEGER LANGUAGE plpgsql AS $$
DECLARE
    v_dispensed_count INTEGER := 0;
BEGIN
    IF p_pharmacy_rx_no IS NOT NULL AND p_site_id IS NOT NULL THEN
        SELECT COUNT(DISTINCT drd.delivery_ticket_id) INTO v_dispensed_count
        FROM form_careplan_order_rx_disp drd
        WHERE drd.rx_no = p_pharmacy_rx_no AND drd.site_id = p_site_id
          AND drd.deleted IS NOT TRUE AND drd.archived IS NOT TRUE AND drd.status = 'Confirmed';
    END IF;
    RETURN GREATEST(0, COALESCE(p_total_authorized_refills, 0) - GREATEST(0, v_dispensed_count - 1));
END;
$$;

-- Helper function to check if followup is allowed (time/count limits)
CREATE OR REPLACE FUNCTION fn_check_followup_eligibility(p_original_ss_message_id INTEGER)
RETURNS TEXT[] LANGUAGE plpgsql AS $$
DECLARE
    ssrec form_ss_message;
    v_errors TEXT[] := ARRAY[]::TEXT[];
    MIN_HOURS_FOR_FU INTEGER := 48; -- Could be a configuration setting
    MAX_FOLLOWUPS INTEGER := 3; -- Example limit, adjust as needed
    v_last_event_time TIMESTAMP;
    v_hours_since_last_event NUMERIC;
BEGIN
    SELECT * INTO ssrec FROM form_ss_message WHERE id = p_original_ss_message_id;
    IF NOT FOUND THEN
        v_errors := array_append(v_errors, 'Original message not found for followup eligibility check.');
        RETURN v_errors;
    END IF;

    IF COALESCE(ssrec.followup_count, 0) >= MAX_FOLLOWUPS THEN
        v_errors := array_append(v_errors, 'Maximum number of follow-ups (' || MAX_FOLLOWUPS || ') already sent.');
    END IF;

    IF COALESCE(ssrec.followup_count, 0) > 0 AND ssrec.last_followup_dt IS NOT NULL THEN
        v_last_event_time := ssrec.last_followup_dt;
    ELSE
        v_last_event_time := ssrec.created_on;
    END IF;

    IF v_last_event_time IS NOT NULL THEN
        v_hours_since_last_event := EXTRACT(EPOCH FROM (NOW() AT TIME ZONE 'UTC' - v_last_event_time AT TIME ZONE 'UTC'))/3600;
        IF v_hours_since_last_event < MIN_HOURS_FOR_FU THEN
            v_errors := array_append(v_errors, 'Please wait at least ' || MIN_HOURS_FOR_FU || ' hours since the last event to send a follow-up.');
        END IF;
    ELSE
         v_errors := array_append(v_errors, 'Cannot determine time of last event for follow-up eligibility.');
    END IF;
    
    RETURN v_errors;
END;
$$;

CREATE OR REPLACE FUNCTION fn_validate_ss_outbound_message_rules(
    p_ss_message_id INTEGER,
    p_action_type TEXT, 
    p_recipient_service_levels TEXT[] 
)
RETURNS TEXT[] LANGUAGE plpgsql AS $$
DECLARE
    v_errors TEXT[] := ARRAY[]::TEXT[];
    ssrec RECORD;
    v_site_record form_site;
    REFILLS_LEFT_ALLOWED INTEGER := 2; 
    REFILLS_MAX_DAYS_OUT INTEGER := 60;
    MAX_DEA_REFILLS INTEGER := 5;
    MAX_DS_3_5 INTEGER := 90;
    MAX_DS_OTHER INTEGER := 30;
BEGIN
    SELECT * INTO ssrec FROM form_ss_message WHERE id = p_ss_message_id;
    IF NOT FOUND THEN
        v_errors := array_append(v_errors, 'Original Surescripts message not found');
        RETURN v_errors;
    END IF;

    RAISE LOG 'Validating outbound message rules for message ID: %, action type: %, recipient service levels: %', p_ss_message_id, p_action_type, p_recipient_service_levels;
    IF ssrec.site_id IS NOT NULL THEN
        SELECT * INTO v_site_record FROM form_site WHERE id = ssrec.site_id;
    END IF;

    IF fn_is_surescripts_thread_canceled(p_ss_message_id) AND p_action_type NOT IN ('approve_cancel', 'deny_cancel') THEN
        v_errors := array_append(v_errors, 'Prescription thread is canceled');
    END IF;

    IF ssrec.expiration_date IS NOT NULL AND ssrec.expiration_date < CURRENT_DATE AND p_action_type <> 'approve_cancel' AND p_action_type <> 'deny_cancel' THEN
        v_errors := array_append(v_errors, 'Prescription has expired');
    END IF;

    IF p_action_type = 'refill' THEN 
        IF COALESCE(ssrec.prohibit_renewal_request, 'false') = 'true' THEN
            v_errors := array_append(v_errors, 'Prescription is prohibited for renewal');
        END IF;
        
        IF NOT (p_recipient_service_levels @> ARRAY['REFILL'] OR p_recipient_service_levels @> ARRAY['Refill']) THEN 
            v_errors := array_append(v_errors, 'Recipient does not support renewal/refill service level');
        END IF;

        -- Check for any pending requests in the thread
        DECLARE
            v_pending_count INTEGER;
            v_pending_type TEXT;
        BEGIN
            SELECT COUNT(*), MAX(fsm_pending.message_type) INTO v_pending_count, v_pending_type
            FROM form_ss_message fsm_pending
            WHERE fsm_pending.message_type IN ('RxChangeRequest', 'RxRenewalRequest', 'CancelRx')
              AND fsm_pending.message_status IN ('Pending', 'Sent', 'Verified')
              AND fsm_pending.response_message_id IS NULL
              AND fsm_pending.deleted IS NOT TRUE 
              AND fsm_pending.archived IS NOT TRUE
              AND (
                  -- Same thread: either same pharmacy_order_id or related messages
                  (fsm_pending.pharmacy_order_id IS NOT NULL AND fsm_pending.pharmacy_order_id = ssrec.pharmacy_order_id)
                  OR fsm_pending.related_message_id = ssrec.message_id
                  OR fsm_pending.message_id = ssrec.related_message_id
                  OR (ssrec.related_message_id IS NOT NULL AND fsm_pending.related_message_id = ssrec.related_message_id)
              );

            IF v_pending_count > 0 THEN
                IF v_pending_type = 'CancelRx' THEN
                    v_errors := array_append(v_errors, 'Cannot send refill request - a cancellation is pending for this prescription');
                ELSIF v_pending_type = 'RxRenewalRequest' THEN
                    v_errors := array_append(v_errors, 'A renewal request is already pending for this prescription');
                ELSIF v_pending_type = 'RxChangeRequest' THEN
                    v_errors := array_append(v_errors, 'Cannot send refill request - a change request is pending for this prescription');
                END IF;
            END IF;
        END;

        IF ssrec.dea_schedule_id IS NOT NULL AND ssrec.dea_schedule_id <> '' THEN
            IF NOT (p_recipient_service_levels @> ARRAY['CS'] OR p_recipient_service_levels @> ARRAY['ControlledSubstance']) THEN
                v_errors := array_append(v_errors, 'Recipient does not support controlled substance service level');
            END IF;
            IF ssrec.dea_schedule_id = 'C48675' THEN 
                 v_errors := array_append(v_errors, 'Refill is not allowed for Schedule II controlled substances');
            END IF;
            IF ssrec.dea_schedule_id IN ('C48676', 'C48677') AND COALESCE(ssrec.request_refills, 0) > MAX_DEA_REFILLS THEN 
                 v_errors := array_append(v_errors, 'Refills for Schedule III-IV controlled substance is limited to ' || MAX_DEA_REFILLS || ' refills');
            END IF;
        END IF;

        IF ssrec.start_date IS NOT NULL AND CURRENT_DATE < ssrec.start_date THEN
            v_errors := array_append(v_errors, 'Cannot request a refill before the prescription start date');
        END IF;
        IF ssrec.effective_date IS NOT NULL AND CURRENT_DATE < ssrec.effective_date THEN
             v_errors := array_append(v_errors, 'Cannot request a refill before the prescription effective date');
        END IF;

        IF ssrec.pharmacy_rx_no IS NULL OR ssrec.pharmacy_rx_no = '' THEN
            v_errors := array_append(v_errors, 'Original pharmacy RX number is missing for refill request');
        END IF;

        IF ssrec.disp_last_disp_date IS NULL THEN 
            v_errors := array_append(v_errors, 'Last dispense date is missing for refill request');
        END IF;

        DECLARE
            v_refills_rem INTEGER;
            v_eff_exp_date DATE;
        BEGIN
            v_refills_rem := fn_get_refills_remaining(ssrec.pharmacy_rx_no, ssrec.site_id, ssrec.refills);
            IF v_refills_rem > REFILLS_LEFT_ALLOWED THEN
                v_errors := array_append(v_errors, 'Requested refill is too soon based on refills remaining (' || v_refills_rem || ' > ' || REFILLS_LEFT_ALLOWED || ')');
            END IF;

            IF ssrec.expiration_date IS NOT NULL THEN 
                v_eff_exp_date := ssrec.expiration_date;
            ELSIF ssrec.written_date IS NOT NULL THEN 
                v_eff_exp_date := ssrec.written_date + INTERVAL '1 year';
            END IF;

            IF v_eff_exp_date IS NOT NULL AND (v_eff_exp_date - CURRENT_DATE) > REFILLS_MAX_DAYS_OUT THEN 
                v_errors := array_append(v_errors, 'Requested refill is too soon. More than ' || REFILLS_MAX_DAYS_OUT || ' days out from prescription expiration date');
            END IF;
        END;
        
        IF ssrec.disp_description IS NULL OR ssrec.disp_description = '' THEN
             v_errors := array_append(v_errors, 'Dispensed medication description is missing for refill request');
        END IF;
        
        DECLARE v_dx_count INTEGER;
        BEGIN
            SELECT COUNT(*) INTO v_dx_count FROM sf_form_ss_message_to_ss_diagnosis WHERE form_ss_message_fk = ssrec.id;
            IF v_dx_count = 0 THEN
                v_errors := array_append(v_errors, 'Diagnosis is missing for refill request');
            END IF;
        END;
        
        IF ssrec.refills_remaining IS NULL THEN 
             v_errors := array_append(v_errors, 'Original refills remaining information is missing');
        END IF;
        IF ssrec.written_date IS NULL THEN
             v_errors := array_append(v_errors, 'Original written date is missing');
        END IF;

    ELSIF p_action_type = 'change' OR p_action_type = 'request_pa' OR p_action_type = 'request_clarification' OR p_action_type = 'request_provider_info' THEN 
        RAISE LOG 'Validating change prescription service level for recipient: %', p_recipient_service_levels;
        IF NOT (p_recipient_service_levels @> ARRAY['CHANGE'] OR p_recipient_service_levels @> ARRAY['Change']) AND p_action_type <> 'request_pa' THEN
            v_errors := array_append(v_errors, 'Recipient does not support change prescription service level');
        END IF;

        IF p_action_type = 'request_pa' AND NOT (p_recipient_service_levels @> ARRAY['CHANGE'] OR p_recipient_service_levels @> ARRAY['Change']) AND ssrec.prescriber_fax IS NULL THEN
            v_errors := array_append(v_errors, 'Prescriber fax number is required for PA request since Change service level is not supported');
        END IF;

        IF ssrec.dea_schedule_id IS NOT NULL AND ssrec.dea_schedule_id <> '' AND 
           ssrec.chg_type_id NOT IN ('P', 'U') THEN 
            v_errors := array_append(v_errors, 'Change request type ' || COALESCE(ssrec.chg_type_id, '') || ' is not valid for controlled substances (original medication)');
        END IF;
        DECLARE
            v_pending_similar_change_exists BOOLEAN := FALSE;
            v_pending_count INTEGER;
            v_pending_type TEXT;
        BEGIN
            -- First check for any pending requests in the thread
            SELECT COUNT(*), MAX(fsm_pending.message_type) INTO v_pending_count, v_pending_type
            FROM form_ss_message fsm_pending
            WHERE fsm_pending.message_type IN ('RxChangeRequest', 'RxRenewalRequest', 'CancelRx')
              AND fsm_pending.message_status IN ('Pending', 'Sent', 'Verified')
              AND fsm_pending.response_message_id IS NULL
              AND fsm_pending.deleted IS NOT TRUE 
              AND fsm_pending.archived IS NOT TRUE
              AND (
                  -- Same thread: either same pharmacy_order_id or related messages
                  (fsm_pending.pharmacy_order_id IS NOT NULL AND fsm_pending.pharmacy_order_id = ssrec.pharmacy_order_id)
                  OR fsm_pending.related_message_id = ssrec.message_id
                  OR fsm_pending.message_id = ssrec.related_message_id
                  OR (ssrec.related_message_id IS NOT NULL AND fsm_pending.related_message_id = ssrec.related_message_id)
              );

            IF v_pending_count > 0 THEN
                IF v_pending_type = 'CancelRx' THEN
                    v_errors := array_append(v_errors, 'Cannot send change request - a cancellation is pending for this prescription');
                ELSIF v_pending_type = 'RxRenewalRequest' THEN
                    v_errors := array_append(v_errors, 'Cannot send change request - a renewal request is already pending for this prescription');
                ELSE
                    -- Check for similar change request types
                    SELECT COUNT(*) INTO v_pending_count 
                    FROM form_ss_message fsm_pending
                    WHERE fsm_pending.message_type = 'RxChangeRequest'
                      AND fsm_pending.message_status IN ('Pending', 'Sent', 'Verified')
                      AND fsm_pending.response_message_id IS NULL
                      AND fsm_pending.id <> ssrec.id
                      AND fsm_pending.deleted IS NOT TRUE 
                      AND fsm_pending.archived IS NOT TRUE
                      AND (
                          -- Same thread: either same pharmacy_order_id or related messages
                          (fsm_pending.pharmacy_order_id IS NOT NULL AND fsm_pending.pharmacy_order_id = ssrec.pharmacy_order_id)
                          OR fsm_pending.related_message_id = ssrec.message_id
                          OR fsm_pending.message_id = ssrec.related_message_id
                          OR (ssrec.related_message_id IS NOT NULL AND fsm_pending.related_message_id = ssrec.related_message_id)
                      )
                      AND (
                            (ssrec.chg_type_id IN ('S','G','T','D','OS') AND fsm_pending.chg_type_id IN ('S','G','T','D','OS')) OR
                            (ssrec.chg_type_id = 'P' AND fsm_pending.chg_type_id = 'P') OR
                            (ssrec.chg_type_id = 'U' AND fsm_pending.chg_type_id = 'U')
                      );

                    IF v_pending_count > 0 THEN
                        v_errors := array_append(v_errors, 'A similar change request is already pending for this prescription');
                    END IF;
                END IF;
            END IF;
        END;

        IF ssrec.chg_type_id = 'U' THEN 
            DECLARE v_subcodes TEXT[];
            BEGIN
                SELECT array_agg(gr.form_list_ss_chg_subcode_fk) INTO v_subcodes 
                FROM gr_form_ss_message_chg_type_sc_id_to_list_ss_chg_subcode_id gr 
                WHERE gr.form_ss_message_fk = ssrec.id;

                IF v_subcodes @> ARRAY['H'] AND v_subcodes @> ARRAY['J'] THEN
                     v_errors := array_append(v_errors, 'Change request subcodes H (Benefit Plan) and J (REMS) cannot be used together for Provider Info request');
                END IF;
            END;
        END IF;

        IF (p_action_type = 'request_pa' OR ssrec.chg_type_id = 'P') THEN 
            IF ssrec.pa_status_id = 'A' THEN 
                v_errors := array_append(v_errors, 'Prior Authorization is already approved');
            ELSIF ssrec.pa_status_id = 'D' THEN 
                v_errors := array_append(v_errors, 'Prior Authorization is already denied');
            ELSIF ssrec.pa_status_id = 'N' THEN 
                v_errors := array_append(v_errors, 'Prior Authorization is not required for this prescription');
            ELSIF ssrec.pa_number IS NOT NULL AND ssrec.pa_number <> '' THEN
                 v_errors := array_append(v_errors, 'Prior Authorization number already exists on the prescription');
            END IF;
        END IF;
        
        DECLARE med_req_rec RECORD;
        BEGIN
            FOR med_req_rec IN 
                SELECT chg_med.dea_schedule_id, chg_med.day_supply 
                FROM form_ss_chg_med chg_med
                JOIN sf_form_ss_message_to_ss_chg_med sf_chg ON chg_med.id = sf_chg.form_ss_chg_med_fk
                WHERE sf_chg.form_ss_message_fk = ssrec.id AND chg_med.deleted IS NOT TRUE AND chg_med.archived IS NOT TRUE
            LOOP
                IF med_req_rec.dea_schedule_id IS NOT NULL AND med_req_rec.dea_schedule_id <> '' THEN
                    IF NOT (p_recipient_service_levels @> ARRAY['CS'] OR p_recipient_service_levels @> ARRAY['ControlledSubstance']) AND 
                       ssrec.chg_type_id NOT IN ('P', 'U') THEN
                        v_errors := array_append(v_errors, 'Recipient does not support controlled substance service level for one or more requested medication changes');
                    END IF;
                    IF med_req_rec.day_supply IS NOT NULL THEN
                        IF med_req_rec.dea_schedule_id IN ('C48675', 'C48679') AND med_req_rec.day_supply > MAX_DS_3_5 THEN 
                             v_errors := array_append(v_errors, 'Day supply for a requested CS (Schedule II/V) exceeds ' || MAX_DS_3_5 || ' days');
                        ELSIF med_req_rec.dea_schedule_id NOT IN ('C48675', 'C48679') AND med_req_rec.day_supply > MAX_DS_OTHER THEN
                             v_errors := array_append(v_errors, 'Day supply for a requested CS (Schedule III/IV/Other) exceeds ' || MAX_DS_OTHER || ' days');
                        END IF;
                    END IF;
                END IF;
            END LOOP;
        END;

    ELSIF p_action_type = 'followup' THEN
        v_errors := v_errors || fn_check_followup_eligibility(p_ss_message_id);

    ELSIF p_action_type = 'approve_cancel' OR p_action_type = 'deny_cancel' THEN
        DECLARE 
            v_had_dispense BOOLEAN := FALSE;
            v_cancel_request_related_msg_id TEXT;
            v_original_pharmacy_rx_no TEXT;
            v_original_site_id INTEGER;
            v_dispensed_count INTEGER;
        BEGIN
            SELECT fsm_cancel.related_message_id INTO v_cancel_request_related_msg_id
            FROM form_ss_message fsm_cancel 
            WHERE fsm_cancel.message_id = ssrec.related_message_id 
              AND fsm_cancel.message_type = 'CancelRx'
            ORDER BY fsm_cancel.created_on DESC LIMIT 1;

            IF v_cancel_request_related_msg_id IS NOT NULL THEN
                SELECT orig_req.pharmacy_rx_no, orig_req.site_id 
                INTO v_original_pharmacy_rx_no, v_original_site_id
                FROM form_ss_message orig_req
                WHERE orig_req.message_id = v_cancel_request_related_msg_id
                ORDER BY orig_req.created_on DESC LIMIT 1;
            END IF;

            IF v_original_pharmacy_rx_no IS NOT NULL AND v_original_site_id IS NOT NULL THEN
                SELECT COUNT(DISTINCT drd.delivery_ticket_id) INTO v_dispensed_count
                FROM form_careplan_order_rx_disp drd
                WHERE drd.rx_no = v_original_pharmacy_rx_no AND drd.site_id = v_original_site_id
                  AND drd.deleted IS NOT TRUE AND drd.archived IS NOT TRUE AND drd.status = 'Confirmed';
                v_had_dispense := COALESCE(v_dispensed_count, 0) > 0;
            END IF;

            IF ssrec.cancel_status = 'Approved' AND v_had_dispense THEN
                IF ssrec.cancel_note IS NULL OR TRIM(ssrec.cancel_note) = '' THEN
                    v_errors := array_append(v_errors, 'Approval note is required for CancelRxResponse if original Rx was dispensed');
                END IF;
            ELSIF ssrec.cancel_status = 'Denied' AND v_had_dispense THEN
                IF (ssrec.cancel_denied_reason_code IS NULL OR array_length(ssrec.cancel_denied_reason_code, 1) IS NULL OR array_length(ssrec.cancel_denied_reason_code, 1) = 0) AND 
                   (ssrec.cancel_denied_reason IS NULL OR TRIM(ssrec.cancel_denied_reason) = '') THEN
                    v_errors := array_append(v_errors, 'Denial reason code or text is required for CancelRxResponse if original Rx was dispensed');
                END IF;
            END IF;
        END;
    END IF;

    RETURN v_errors;
END;
$$; 