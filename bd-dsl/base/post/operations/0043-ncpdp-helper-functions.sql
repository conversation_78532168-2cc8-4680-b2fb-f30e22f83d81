
DO $$ BEGIN
  PERFORM drop_all_function_signatures('calculate_ncpdp_dispense_qty');
END $$;
CREATE OR REPLACE FUNCTION calculate_ncpdp_dispense_qty(
    v_treat_as_compound boolean,
    v_is_real_compound boolean,
    p_invoice_no text,
    p_claim_no text,
    p_is_test boolean
) RETURNS numeric 
LANGUAGE plpgsql AS $$
DECLARE
    v_total_amount numeric;
BEGIN
    RAISE LOG 'calculate_ncpdp_dispense_qty: v_treat_as_compound: %, v_is_real_compound: %, invoice/claim: %/%, is_test: %', 
        v_treat_as_compound, v_is_real_compound, p_invoice_no, p_claim_no, p_is_test;

    BEGIN
        IF p_is_test THEN
            IF v_treat_as_compound THEN
                SELECT COUNT(*)
                INTO v_total_amount
                FROM form_test_charge_line
                WHERE claim_no = p_claim_no
                    AND archived IS NOT TRUE
                    AND deleted IS NOT TRUE
                    AND COALESCE(void, 'No') <> 'Yes';
            ELSIF v_is_real_compound THEN
                -- TODO: Need to handle compound base billing units here
                SELECT SUM(charge_quantity::numeric)
                INTO v_total_amount
                FROM form_test_charge_line
                WHERE claim_no = p_claim_no
                    AND archived IS NOT TRUE
                    AND deleted IS NOT TRUE
                    AND COALESCE(void, 'No') <> 'Yes';
            ELSE
                SELECT SUM(charge_quantity::numeric)
                INTO v_total_amount
                FROM form_test_charge_line
                WHERE claim_no = p_claim_no
                    AND archived IS NOT TRUE
                    AND deleted IS NOT TRUE
                    AND COALESCE(void, 'No') <> 'Yes';
            END IF;
        ELSE
            IF v_treat_as_compound THEN
                SELECT COUNT(*)
                INTO v_total_amount
                FROM form_ledger_charge_line
                WHERE invoice_no = p_invoice_no
                    AND archived IS NOT TRUE
                    AND deleted IS NOT TRUE
                    AND COALESCE(void, 'No') <> 'Yes';
            ELSIF v_is_real_compound THEN
                -- TODO: Need to handle compound base billing units here
                SELECT SUM(charge_quantity::numeric)
                INTO v_total_amount
                FROM form_ledger_charge_line
                WHERE invoice_no = p_invoice_no
                    AND archived IS NOT TRUE
                    AND deleted IS NOT TRUE
                    AND COALESCE(void, 'No') <> 'Yes';
            ELSE
                SELECT SUM(charge_quantity::numeric)
                INTO v_total_amount
                FROM form_ledger_charge_line
                WHERE invoice_no = p_invoice_no
                    AND archived IS NOT TRUE
                    AND deleted IS NOT TRUE
                    AND COALESCE(void, 'No') <> 'Yes';
            END IF;
        END IF;

        RETURN COALESCE(v_total_amount, 0);
        
    EXCEPTION WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Calculating dispense quantity',
            'FUNCTION',
            current_schema(),
            CASE WHEN p_is_test THEN 'form_test_charge_line' ELSE 'form_ledger_charge_line' END,
            jsonb_build_object(
                'function_name', 'calculate_ncpdp_dispense_qty',
                'invoice_no', p_invoice_no,
                'claim_no', p_claim_no,
                'is_test', p_is_test,
                'treat_as_compound', v_treat_as_compound,
                'is_real_compound', v_is_real_compound
            )
        );
        RAISE;
    END;
END;
$$;

DO $$ BEGIN
  PERFORM drop_all_function_signatures('calculate_ncpdp_charge_lines_dispense_qty');
END $$;
CREATE OR REPLACE FUNCTION calculate_ncpdp_charge_lines_dispense_qty(
  v_treat_as_compound boolean,
  v_is_real_compound boolean,
  p_charge_lines charge_line_with_split[],
  p_is_test boolean
) RETURNS numeric
LANGUAGE plpgsql AS $$
DECLARE
  v_total_amount numeric;
BEGIN
  -- Check for null input parameters
  IF p_charge_lines IS NULL THEN
    RAISE EXCEPTION 'Charge lines parameter cannot be null';
  END IF;
  
  IF v_treat_as_compound IS NULL THEN
    RAISE EXCEPTION 'Treat as compound parameter cannot be null';
  END IF;
  
  IF v_is_real_compound IS NULL THEN
    RAISE EXCEPTION 'Is real compound parameter cannot be null';
  END IF;
  
  IF p_is_test IS NULL THEN
    RAISE EXCEPTION 'Is test parameter cannot be null';
  END IF;

  BEGIN
    RAISE LOG 'calculate_ncpdp_charge_lines_dispense_qty: v_treat_as_compound: %, v_is_real_compound: %, charge_lines: %, is_test: %',
      v_treat_as_compound, v_is_real_compound, array_length(p_charge_lines, 1), p_is_test;

    IF p_is_test THEN
      IF v_treat_as_compound THEN
        -- Get the count of elements in the array
        SELECT array_length(p_charge_lines, 1)::numeric
        INTO v_total_amount;
      ELSIF v_is_real_compound THEN
        -- TODO: Need to handle compound base billing units here
        SELECT SUM(COALESCE(cl.charge_quantity, 0)::numeric)
        INTO v_total_amount
        FROM unnest(p_charge_lines) cl;
      ELSE
        SELECT SUM(COALESCE(cl.charge_quantity, 0)::numeric)
        INTO v_total_amount
        FROM unnest(p_charge_lines) cl;
      END IF;
    ELSE
      IF v_treat_as_compound THEN
        -- Get the count of elements in the array
        SELECT array_length(p_charge_lines, 1)::numeric
        INTO v_total_amount;
      ELSIF v_is_real_compound THEN
        -- TODO: Need to handle compound base billing units here
        SELECT SUM(COALESCE(cl.charge_quantity, 0)::numeric)
        INTO v_total_amount
        FROM unnest(p_charge_lines) cl;
      ELSE
        SELECT SUM(COALESCE(cl.charge_quantity, 0)::numeric)
        INTO v_total_amount
        FROM unnest(p_charge_lines) cl;
      END IF;
    END IF;

    RETURN COALESCE(v_total_amount, 0);
  EXCEPTION
    WHEN OTHERS THEN
      INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
      ) VALUES (
        SQLERRM,
        'Calculating dispense quantity',
        'FUNCTION',
        current_schema(),
        'form_ledger_charge_line',
        jsonb_build_object(
          'function_name', 'calculate_ncpdp_charge_lines_dispense_qty',
          'charge_lines_count', array_length(p_charge_lines, 1),
          'is_test', p_is_test,
          'treat_as_compound', v_treat_as_compound,
          'is_real_compound', v_is_real_compound
        )
      );
      RAISE;
  END;
END;
$$;

DO $$ BEGIN
  PERFORM drop_all_function_signatures('get_ncpdp_insurance_qualifier_settings');
END $$;
CREATE OR REPLACE FUNCTION get_ncpdp_insurance_qualifier_settings(
    p_charge_line record,
    p_treat_as_compound boolean,
    p_is_real_compound boolean,
    p_is_test boolean DEFAULT FALSE
) RETURNS TABLE (
    prod_svc_id_qualifier text,
    prod_svc_id text,
    unit_of_measure text,
    compound_code text,
    dispense_qty numeric
)
LANGUAGE plpgsql AS $$
DECLARE
    v_invoice_no text;
    v_claim_no text;
BEGIN
    RAISE LOG 'get_ncpdp_insurance_qualifier_settings: charge_no: %, treat_as_compound: %, is_real_compound: %',
        p_charge_line.charge_no, p_treat_as_compound, p_is_real_compound;

    BEGIN

    v_invoice_no := NULL;
    v_claim_no := NULL;

    -- Invoice number
    IF p_is_test IS FALSE THEN
        v_invoice_no := p_charge_line.invoice_no;
    END IF;

    -- Claim #
    v_claim_no := p_charge_line.claim_no;


        RETURN QUERY 
        WITH insurance_info AS (
            SELECT ins.*,
                inv.billing_unit_id
            FROM get_insurance_claim_settings(
                p_charge_line.insurance_id, 
                p_charge_line.site_id, 
                NULL::integer
            ) ins
            INNER JOIN form_inventory inv 
                ON inv.id = p_charge_line.inventory_id
        )
        SELECT
            -- Product Service ID Qualifier
            CASE
                WHEN p_treat_as_compound AND NOT p_is_real_compound THEN '00'
                WHEN p_is_real_compound THEN
                    CASE 
                        WHEN ins.compound_qualifier_id = '09' AND LENGTH(p_charge_line.hcpc_code) > 0 THEN ins.compound_qualifier_id
                        WHEN ins.compound_qualifier_id = '32' AND LENGTH(p_charge_line.gcn_seqno) > 0 THEN ins.compound_qualifier_id
                        WHEN ins.compound_qualifier_id IN ('36','03') AND LENGTH(p_charge_line.ndc) > 0 THEN ins.compound_qualifier_id
                        ELSE COALESCE(ins.compound_qualifier_id, '00')
                    END
                ELSE 
                    CASE 
                        WHEN ins.noncompound_drug_qualifier_id = '00' THEN ins.noncompound_drug_qualifier_id
                        WHEN ins.noncompound_drug_qualifier_id = '09' AND LENGTH(p_charge_line.hcpc_code) > 0 THEN ins.noncompound_drug_qualifier_id
                        WHEN ins.noncompound_drug_qualifier_id = '32' AND LENGTH(p_charge_line.gcn_seqno) > 0 THEN ins.noncompound_drug_qualifier_id
                        WHEN ins.noncompound_drug_qualifier_id IN ('36','03') AND LENGTH(p_charge_line.ndc) > 0 THEN ins.noncompound_drug_qualifier_id
                        ELSE '03'
                    END
            END AS prod_svc_id_qualifier,

            -- Product Service ID
            CASE
                WHEN p_treat_as_compound AND NOT p_is_real_compound THEN '00000000000'
                WHEN p_is_real_compound THEN
                    CASE
                        WHEN ins.compound_qualifier_id = '00' THEN NULL
                        WHEN ins.compound_qualifier_id = '09' AND LENGTH(p_charge_line.hcpc_code) > 0 THEN p_charge_line.hcpc_code
                        WHEN ins.compound_qualifier_id = '32' AND LENGTH(p_charge_line.gcn_seqno) > 0 THEN p_charge_line.gcn_seqno
                        WHEN ins.compound_qualifier_id IN ('36','03') AND LENGTH(p_charge_line.ndc) > 0 THEN p_charge_line.ndc
                        ELSE '00000000000'
                    END
                ELSE
                    CASE
                        WHEN ins.noncompound_drug_qualifier_id = '00' THEN NULL
                        WHEN ins.noncompound_drug_qualifier_id = '09' AND LENGTH(p_charge_line.hcpc_code) > 0 THEN p_charge_line.hcpc_code
                        WHEN ins.noncompound_drug_qualifier_id = '32' AND LENGTH(p_charge_line.gcn_seqno) > 0 THEN p_charge_line.gcn_seqno
                        WHEN ins.noncompound_drug_qualifier_id IN ('36','03') AND LENGTH(p_charge_line.ndc) > 0 THEN p_charge_line.ndc
                        ELSE p_charge_line.ndc
                    END
            END AS prod_svc_id,

            -- Unit of Measure
            CASE
                WHEN p_treat_as_compound THEN
                    CASE
                        WHEN ins.send_uom = 'Y' THEN 'EA'
                        ELSE NULL
                    END
                WHEN ins.send_uom = 'Y' THEN   
                    CASE
                        WHEN ins.billing_unit_id = 'each' THEN 'EA'
                        WHEN ins.billing_unit_id = 'gram' THEN 'GM'
                        WHEN ins.billing_unit_id = 'mL' THEN 'ML'
                        ELSE 'EA'
                    END
                ELSE NULL
            END AS unit_of_measure,

            -- Compound Code
            CASE
                WHEN p_treat_as_compound AND NOT p_is_real_compound THEN '1'
                WHEN p_is_real_compound THEN '2'
                ELSE '1'
            END AS compound_code,
            -- Dispense Quantity
            calculate_ncpdp_dispense_qty(
                p_treat_as_compound, 
                p_is_real_compound, 
                v_invoice_no, 
                v_claim_no, 
                p_is_test
            ) AS dispense_qty
        FROM insurance_info ins;

    EXCEPTION WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Getting insurance qualifier settings',
            'FUNCTION',
            current_schema(),
            'form_inventory',
            jsonb_build_object(
                'function_name', 'get_ncpdp_insurance_qualifier_settings',
                'charge_no', p_charge_line.charge_no,
                'treat_as_compound', p_treat_as_compound,
                'is_real_compound', p_is_real_compound
            )
        );
        RAISE;
    END;
END;
$$;

DO $$ BEGIN
  PERFORM drop_all_function_signatures('get_ncpdp_compound_status');
END $$;
CREATE OR REPLACE FUNCTION get_ncpdp_compound_status(
    p_invoice_no text DEFAULT NULL::text,
    p_claim_no text DEFAULT NULL::text,
    p_is_test boolean DEFAULT false
) RETURNS TABLE(
    ncpdp_id integer,
    should_treat_as_compound boolean,
    is_real_compound boolean
) 
LANGUAGE plpgsql AS $$
DECLARE
    v_result record;
BEGIN
    RAISE LOG 'get_ncpdp_compound_status: invoice/claim: %/%, is_test: %', 
        p_invoice_no, p_claim_no, p_is_test;

    BEGIN
        -- Validate input parameters
        IF p_invoice_no IS NULL AND p_claim_no IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Invoice or Claim # is required',
                'Validating required fields in get_ncpdp_compound_status',
                'FUNCTION',
                current_schema(),
                'form_ledger_charge_line',
                jsonb_build_object(
                    'function_name', 'get_ncpdp_compound_status',
                    'invoice_no', p_invoice_no,
                    'claim_no', p_claim_no,
                    'is_test', p_is_test
                )
            );
            RAISE EXCEPTION 'Invoice or Claim # is required';
        END IF;

        -- Handle test claims
        IF p_is_test THEN
            WITH charge_line_counts AS (
                SELECT
                    COUNT(*) as total_lines,
                    bool_or(compound_no IS NOT NULL) as has_compound,
                    bool_or(COALESCE(is_primary_drug_ncpdp, 'No') = 'Yes') as has_primary_drug
                FROM form_test_charge_line
                WHERE claim_no = p_claim_no
                    AND archived IS NOT TRUE
                    AND deleted IS NOT TRUE
                    AND COALESCE(void, 'No') <> 'Yes'
            )
            SELECT
                n.id,
                CASE
                    WHEN clc.has_compound THEN TRUE
                    WHEN clc.total_lines > 1 THEN TRUE
                    ELSE FALSE
                END as should_treat_as_compound,
                clc.has_compound as is_real_compound
            INTO v_result
            FROM form_ncpdp n
            CROSS JOIN charge_line_counts clc
            WHERE n.claim_no = p_claim_no
                AND COALESCE(n.is_test, 'No') = 'Yes'
                AND n.archived IS NOT TRUE
                AND n.deleted IS NOT TRUE
                AND COALESCE(n.void, 'No') <> 'Yes';

        -- Handle real claims
        ELSE
            WITH charge_line_counts AS (
                SELECT
                    COUNT(*) as total_lines,
                    bool_or(compound_no IS NOT NULL) as has_compound,
                    bool_or(COALESCE(is_primary_drug_ncpdp, 'No') = 'Yes') as has_primary_drug
                FROM form_ledger_charge_line
                WHERE ((claim_no = p_claim_no AND p_claim_no IS NOT NULL) 
                    OR (invoice_no = p_invoice_no AND p_invoice_no IS NOT NULL))
                    AND archived IS NOT TRUE
                    AND deleted IS NOT TRUE
                    AND COALESCE(void, 'No') <> 'Yes'
            )
            SELECT DISTINCT
                n.id,
                CASE
                    WHEN clc.has_compound THEN TRUE
                    WHEN clc.total_lines > 1 THEN TRUE
                    ELSE FALSE
                END as should_treat_as_compound,
                clc.has_compound as is_real_compound
            INTO v_result
            FROM form_ncpdp n
            CROSS JOIN charge_line_counts clc
            WHERE ((claim_no = p_claim_no AND p_claim_no IS NOT NULL) 
                OR (invoice_no = p_invoice_no AND p_invoice_no IS NOT NULL))
                AND n.archived IS NOT TRUE
                AND n.deleted IS NOT TRUE
                AND COALESCE(n.void, 'No') <> 'Yes';
        END IF;

        -- Return results
        RETURN QUERY 
        SELECT 
            v_result.id,
            v_result.should_treat_as_compound,
            v_result.is_real_compound;

    EXCEPTION WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Getting compound status',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'get_ncpdp_compound_status',
                'invoice_no', p_invoice_no,
                'claim_no', p_claim_no,
                'is_test', p_is_test
            )
        );
        RAISE;
    END;
END;
$$;

DO $$ BEGIN
  PERFORM drop_all_function_signatures('calculate_ncpdp_ingredient_cost');
END $$;
CREATE OR REPLACE FUNCTION calculate_ncpdp_ingredient_cost(
 p_invoice_no text DEFAULT NULL,
 p_claim_no text DEFAULT NULL,
 p_is_test boolean DEFAULT FALSE)
RETURNS numeric AS $$
DECLARE
    v_total_cost numeric;
BEGIN
    RAISE LOG 'calculate_ncpdp_ingredient_cost: invoice/claim: %/%, is_test: %', p_invoice_no, p_claim_no, p_is_test;

    IF p_invoice_no IS NULL AND p_claim_no IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Invoice or Claim # is required',
            'Validating required fields in calculate_ncpdp_ingredient_cost',
            'FUNCTION',
            current_schema(),
            'form_ledger_charge_line',
            jsonb_build_object(
                'function_name', 'calculate_ncpdp_ingredient_cost',
                'invoice_no', p_invoice_no,
                'claim_no', p_claim_no,
                'is_test', p_is_test
            )
        );
        RAISE EXCEPTION 'Invoice or Claim # is required';
    END IF;
    
    BEGIN
        IF p_is_test IS TRUE THEN
            SELECT SUM(billed::numeric)
            INTO v_total_cost
            FROM form_test_charge_line
            WHERE claim_no = p_claim_no
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE
            AND COALESCE(void, 'No') <> 'Yes';
        ELSE
            SELECT SUM(billed::numeric)
            INTO v_total_cost
            FROM form_ledger_charge_line
            WHERE (invoice_no = p_invoice_no OR claim_no = p_claim_no)
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE
            AND COALESCE(void, 'No') <> 'Yes';
        END IF;

        RETURN COALESCE(v_total_cost, 0);
    EXCEPTION WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Calculating ingredient cost',
            'FUNCTION',
            current_schema(),
            CASE WHEN p_is_test THEN 'form_test_charge_line' ELSE 'form_ledger_charge_line' END,
            jsonb_build_object(
                'function_name', 'calculate_ncpdp_ingredient_cost',
                'invoice_no', p_invoice_no,
                'claim_no', p_claim_no,
                'is_test', p_is_test
            )
        );
        RAISE;
    END;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Function to update NCPDP pricing
DO $$ BEGIN
  PERFORM drop_all_function_signatures('update_ncpdp_pricing');
END $$;
CREATE OR REPLACE FUNCTION update_ncpdp_pricing(
    p_ncpdp_id integer,
    p_charge_line record,
    p_is_primary boolean,
    p_invoice_no text DEFAULT NULL,
    p_claim_no text DEFAULT NULL,
    p_is_test boolean DEFAULT FALSE
) RETURNS void AS $$
BEGIN
    RAISE LOG 'update_ncpdp_pricing: ncpdp_id: %, invoice/claim: %/%, is_test: %, is_primary: %', 
        p_ncpdp_id, p_invoice_no, p_claim_no, p_is_test, p_is_primary;

    BEGIN
        IF p_is_primary THEN
            UPDATE form_ncpdp_pricing np
            SET
                disp_fee_sub = p_charge_line.dispense_fee,
                incv_amt_sub = p_charge_line.incv_amt_sub,
                cost_basis = p_charge_line.cost_basis,
                pt_pd_amt_sub = p_charge_line.pt_pd_amt_sub,
                ing_cst_sub = calculate_ncpdp_ingredient_cost(p_invoice_no, p_claim_no, p_is_test),
                gross_amount_due = calculate_ncpdp_gross_amount(p_invoice_no, p_claim_no, p_is_test),
                u_and_c_charge = calculate_ncpdp_u_and_c_charge(p_invoice_no, p_claim_no, p_is_test),
                updated_by = COALESCE(p_charge_line.updated_by, p_charge_line.created_by),
                updated_on = COALESCE(p_charge_line.updated_on, p_charge_line.created_on)
            FROM sf_form_ncpdp_to_ncpdp_pricing sfnp
            WHERE sfnp.form_ncpdp_fk = p_ncpdp_id
            AND sfnp.form_ncpdp_pricing_fk = np.id
            AND sfnp.archive IS NOT TRUE
            AND sfnp.delete IS NOT TRUE;
        ELSE
            UPDATE form_ncpdp_pricing np
            SET
                ing_cst_sub = calculate_ncpdp_ingredient_cost(p_invoice_no, p_claim_no, p_is_test),
                gross_amount_due = calculate_ncpdp_gross_amount(p_invoice_no, p_claim_no, p_is_test),
                u_and_c_charge = calculate_ncpdp_u_and_c_charge(p_invoice_no, p_claim_no, p_is_test)
            FROM sf_form_ncpdp_to_ncpdp_pricing sfnp
            WHERE sfnp.form_ncpdp_fk = p_ncpdp_id
            AND sfnp.form_ncpdp_pricing_fk = np.id
            AND sfnp.archive IS NOT TRUE
            AND sfnp.delete IS NOT TRUE;
        END IF;

        IF NOT FOUND THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Could not find pricing segment to update',
                'Updating pricing in update_ncpdp_pricing',
                'FUNCTION',
                current_schema(),
                'form_ncpdp_pricing',
                jsonb_build_object(
                    'function_name', 'update_ncpdp_pricing',
                    'invoice_no', p_invoice_no,
                    'claim_no', p_claim_no,
                    'ncpdp_id', p_ncpdp_id,
                    'is_test', p_is_test,
                    'is_primary', p_is_primary
                )
            );
            RAISE EXCEPTION 'Could not find pricing segment to update for NCPDP ID: %', p_ncpdp_id;
        END IF;
    EXCEPTION WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Updating NCPDP pricing',
            'FUNCTION',
            current_schema(),
            'form_ncpdp_pricing',
            jsonb_build_object(
                'function_name', 'update_ncpdp_pricing',
                'invoice_no', p_invoice_no,
                'claim_no', p_claim_no,
                'ncpdp_id', p_ncpdp_id,
                'is_test', p_is_test,
                'is_primary', p_is_primary
            )
        );
        RAISE;
    END;
END;
$$ LANGUAGE plpgsql VOLATILE;


-- Helper function to calculate U&C charge for both real and test claims
DO $$ BEGIN
  PERFORM drop_all_function_signatures('calculate_ncpdp_u_and_c_charge');
END $$;
CREATE OR REPLACE FUNCTION calculate_ncpdp_u_and_c_charge(
    p_invoice_no text DEFAULT NULL,
    p_claim_no text DEFAULT NULL,
    p_is_test boolean DEFAULT FALSE
)
RETURNS numeric AS $$
DECLARE
    v_total_u_and_c numeric;
BEGIN
    RAISE LOG 'calculate_ncpdp_u_and_c_charge: invoice/claim: %/%, is_test: %', p_invoice_no, p_claim_no, p_is_test;

    IF p_invoice_no IS NULL AND p_claim_no IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Invoice or Claim # is required',
            'Validating required fields in calculate_ncpdp_u_and_c_charge',
            'FUNCTION',
            current_schema(),
            'form_ledger_charge_line',
            jsonb_build_object(
                'function_name', 'calculate_ncpdp_u_and_c_charge',
                'invoice_no', p_invoice_no,
                'claim_no', p_claim_no,
                'is_test', p_is_test
            )
        );
        RAISE EXCEPTION 'Invoice or Claim # is required';
    END IF;
    
    BEGIN
        IF p_is_test THEN
            SELECT SUM(list_price::numeric)
            INTO v_total_u_and_c
            FROM form_test_charge_line
            WHERE claim_no = p_claim_no
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE
            AND COALESCE(void, 'No') <> 'Yes';
        ELSE
            SELECT SUM(list_price::numeric)
            INTO v_total_u_and_c
            FROM form_ledger_charge_line
            WHERE (invoice_no = p_invoice_no OR claim_no = p_claim_no)
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE
            AND COALESCE(void, 'No') <> 'Yes';
        END IF;

        RETURN COALESCE(v_total_u_and_c, 0);
    EXCEPTION WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Calculating U&C charge',
            'FUNCTION',
            current_schema(),
            CASE WHEN p_is_test THEN 'form_test_charge_line' ELSE 'form_ledger_charge_line' END,
            jsonb_build_object(
                'function_name', 'calculate_ncpdp_u_and_c_charge',
                'invoice_no', p_invoice_no,
                'claim_no', p_claim_no,
                'is_test', p_is_test
            )
        );
        RAISE;
    END;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Helper function to calculate gross amount due for both real and test claims
DO $$ BEGIN
  PERFORM drop_all_function_signatures('calculate_ncpdp_gross_amount');
END $$;
CREATE OR REPLACE FUNCTION calculate_ncpdp_gross_amount(
    p_invoice_no text DEFAULT NULL,
    p_claim_no text DEFAULT NULL,
    p_is_test boolean DEFAULT FALSE
)
RETURNS numeric AS $$
DECLARE
    v_total_amount numeric;
BEGIN
    RAISE LOG 'calculate_ncpdp_gross_amount: invoice/claim: %/%, is_test: %', p_invoice_no, p_claim_no, p_is_test;

    IF p_invoice_no IS NULL AND p_claim_no IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Invoice or Claim # is required',
            'Validating required fields in calculate_ncpdp_gross_amount',
            'FUNCTION',
            current_schema(),
            'form_ledger_charge_line',
            jsonb_build_object(
                'function_name', 'calculate_ncpdp_gross_amount',
                'invoice_no', p_invoice_no,
                'claim_no', p_claim_no,
                'is_test', p_is_test
            )
        );
        RAISE EXCEPTION 'Invoice or Claim # is required';
    END IF;

    BEGIN
        IF p_is_test THEN
            SELECT SUM(gross_amount_due::numeric)
            INTO v_total_amount
            FROM form_test_charge_line
            WHERE claim_no = p_claim_no
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE
            AND COALESCE(void, 'No') <> 'Yes';
        ELSE
            SELECT SUM(gross_amount_due::numeric)
            INTO v_total_amount
            FROM form_ledger_charge_line
            WHERE (invoice_no = p_invoice_no OR claim_no = p_claim_no)
            AND archived IS NOT TRUE
            AND deleted IS NOT TRUE
            AND COALESCE(void, 'No') <> 'Yes';
        END IF;

        RETURN COALESCE(v_total_amount, 0);
    EXCEPTION WHEN OTHERS THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Calculating gross amount',
            'FUNCTION',
            current_schema(),
            CASE WHEN p_is_test THEN 'form_test_charge_line' ELSE 'form_ledger_charge_line' END,
            jsonb_build_object(
                'function_name', 'calculate_ncpdp_gross_amount',
                'invoice_no', p_invoice_no,
                'claim_no', p_claim_no,
                'is_test', p_is_test
            )
        );
        RAISE;
    END;
END;
$$ LANGUAGE plpgsql VOLATILE;
