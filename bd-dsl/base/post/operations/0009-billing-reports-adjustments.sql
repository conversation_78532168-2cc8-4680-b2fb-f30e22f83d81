-- METADATA_START
-- Functions Created: []
-- Functions Called: [format_currency_short] /* Filtered to user-defined only */
-- Views Created: [vw_adjustment_charge_line_base, vw_ar_charge_line_adjustment_report, vw_ar_invoice_adjustment_report]
-- Views Referenced: [vw_charge_line_report_base, vw_invoice_report_base, vw_ledger_finance_report_base]
-- METADATA_END

DROP VIEW IF EXISTS vw_adjustment_charge_line_base CASCADE;
CREATE OR REPLACE VIEW vw_adjustment_charge_line_base AS
SELECT
    lf.charge_no::text as charge_no,
    lf.invoice_no::text as invoice_no,
    lf.ticket_no::text as ticket_no,
    lf.ticket_item_no::text as ticket_item_no,
    lf.transaction_type::text as transaction_type,
    lf.transaction_type_auto_name::text as transaction_type_auto_name,
    lf.transaction_subtype::text as transaction_subtype,
    lf.transaction_subtype_auto_name::text as transaction_subtype_auto_name,
    lf.gl_account::text as gl_account,
    lf.gl_account_auto_name::text as gl_account_auto_name,
    lf.source_id::integer as source_id,
    lf.source_form::text as source_form,
    lf.transaction_datetime::timestamp as transaction_datetime,
    TO_CHAR(lf.transaction_datetime, 'MM/DD/YYYY')::text as transaction_date,
    lf.post_datetime::timestamp as post_datetime,
    TO_CHAR(lf.post_datetime, 'MM/DD/YYYY')::text as post_date,
    lf.adjustment_no::text as adjustment_no,
    lf.transaction_by::integer as transaction_by,
    lf.transaction_by_auto_name::text as transaction_by_auto_name,
    lf.amount_raw::numeric as adjusted_amount_raw,
    CASE
        WHEN ft.transaction_type::text = 'charge_adjustment'::text THEN 'Charge Adjustment'::text
        WHEN ft.transaction_type::text = 'charge_adjustment_reversal'::text THEN 'Charge Adjustment Reversal'::text
        WHEN ft.transaction_type::text = 'cogs_adjustment'::text THEN 'COGS Adjustment'::text
        WHEN ft.transaction_type::text = 'cogs_adjustment_reversal'::text THEN 'COGS Adjustment Reversal'::text
    END::text as adjustment_type,
    ba.adjustment_comment::text as adjustment_comment
FROM
    vw_ledger_finance_report_base lf
    INNER JOIN form_ledger_fin_transaction_log ft ON ft.id = lf.transaction_id
    LEFT JOIN form_billing_adjustment ba ON ba.adjustment_no = lf.adjustment_no
WHERE
    lf.gl_account IN ('2000', '1300') -- AR Account
    AND lf.transaction_type IN ('CHGADJ', 'CDTMEM', 'BADDEB', 'COGADJ'); -- Adjustment Types

DROP VIEW IF EXISTS vw_ar_charge_line_adjustment_report CASCADE;
CREATE OR REPLACE VIEW vw_ar_charge_line_adjustment_report AS
SELECT
    adj.source_form::text as query_form,
    adj.source_id::integer as query_id,
    adj.transaction_type::text as transaction_type,
    adj.transaction_type_auto_name::text as transaction_type_auto_name,
    adj.transaction_subtype::text as transaction_subtype,
    adj.transaction_subtype_auto_name::text as transaction_subtype_auto_name,
    adj.gl_account::text as gl_account,
    adj.gl_account_auto_name::text as gl_account_auto_name,
    adj.source_id::integer as source_id,
    adj.source_form::text as source_form,
    adj.transaction_datetime::timestamp as transaction_datetime,
    adj.transaction_date::text as transaction_date,
    adj.post_datetime::timestamp as post_datetime,
    adj.post_date::text as post_date,
    adj.adjustment_no::text as adjustment_no,
    adj.transaction_by::integer as transaction_by,
    adj.transaction_by_auto_name::text as transaction_by_auto_name,
    adj.adjusted_amount_raw::numeric as adjusted_amount_raw,
    format_currency_short(adj.adjusted_amount_raw) as adjusted_amount,
    adj.adjustment_type::text as adjustment_type,
    lcl.invoice_no::text as invoice_no,
    lcl.claim_no::text as claim_no,
    lcl.charge_no::text as charge_no,
    lcl.close_no::text as close_no,
    lcl.ticket_no::text as ticket_no,
    lcl.compound_no::text as compound_no,
    lcl.ticket_item_no::text as ticket_item_no,
    lcl.parent_charge_no::text as parent_charge_no,
    lcl.master_charge_no::text as master_charge_no,
    lcl.encounter::integer as encounter,
    lcl.billing_unit::text as billing_unit,
    lcl.patient::integer as patient,
    lcl.patient_auto_name::text as patient_auto_name,
    lcl.insurance::integer as insurance,
    lcl.is_specialty::text as is_specialty,
    lcl.item::integer as item,
    lcl.item_auto_name::text as item_auto_name,
    lcl.brand_name::text as brand_name,
    lcl.brand_name_auto_name::text as brand_name_auto_name,
    lcl.manufacturer::text as manufacturer,
    lcl.manufacturer_auto_name::text as manufacturer_auto_name,
    lcl.hcpc_code::text as hcpc_code,
    lcl.ndc_raw::text as ndc_raw,
    lcl.ndc::text as ndc,
    lcl.upc::text as upc,
    lcl.is_primary_drug::text as is_primary_drug,
    lcl.quantity_raw::numeric as quantity_raw,
    lcl.quantity::text as quantity,
    lcl.metric_quantity_raw::numeric as metric_quantity_raw,
    lcl.metric_quantity::text as metric_quantity,
    lcl.fill_number::integer as fill_number,
    lcl.rx_no::text as rx_no,
    lcl.created_on::timestamp as created_on,
    lcl.pricing_source_raw::text as pricing_source_raw,
    lcl.pricing_source::text as pricing_source,
    lcl.shared_contract::text as shared_contract,
    lcl.shared_contract_auto_name::text as shared_contract_auto_name,
    lcl."MRN"::text as "MRN",
    lcl.payer::integer as payer,
    lcl.payer_auto_name::text as payer_auto_name,
    lcl.site::integer as site,
    lcl.site_auto_name::text as site_auto_name,
    lcl.revenue_code::text as revenue_code,
    lcl.revenue_code_auto_name::text as revenue_code_auto_name,
    lcl.date_of_service::date as date_of_service,
    lcl."DOS"::text as "DOS",
    lcl.date_of_service_end::date as date_of_service_end,
    lcl."DOS End"::text as "DOS End",
    lcl.created_date::timestamp as created_date,
    lcl.therapy::text as therapy,
    lcl.therapy_auto_name::text as therapy_auto_name,
    lcl.physician::integer as physician,
    lcl.physician_auto_name::text as physician_auto_name,
    lcl.sales_territory::integer as sales_territory,
    lcl.sales_territory_auto_name::text as sales_territory_auto_name,
    lcl.sales_rep::integer as sales_rep,
    lcl.sales_rep_auto_name::text as sales_rep_auto_name,
    lcl.sales_account::integer as sales_account,
    lcl.sales_account_auto_name::text as sales_account_auto_name,
    lcl.nursing_agency::integer as nursing_agency,
    lcl.nursing_agency_auto_name::text as nursing_agency_auto_name,
    lcl.infusion_suite::integer as infusion_suite,
    lcl.infusion_suite_auto_name::text as infusion_suite_auto_name,
    lcl.patient_status::text as patient_status,
    lcl.patient_status_auto_name::text as patient_status_auto_name,
    lcl.report_quantity_raw::numeric as report_quantity_raw,
    lcl.report_unit_id_raw::text as report_unit_id_raw,
    lcl.report_quantity::text as report_quantity,
    lcl.billing_method::text as billing_method,
    lcl.billing_method_auto_name::text as billing_method_auto_name,
    lcl.revenue_accepted_posted::text as revenue_accepted_posted,
    lcl.physician_city::text as physician_city,
    lcl.physician_state::text as physician_state,
    lcl.physician_state_auto_name::text as physician_state_auto_name,
    lcl.physician_zip::text as physician_zip,
    lcl.payer_type::text as payer_type,
    lcl.payer_type_auto_name::text as payer_type_auto_name,
    lcl.awp_price_raw::numeric as awp_price_raw,
    lcl.awp_price::text as awp_price,
    lcl.last_name::text as last_name,
    lcl.first_name::text as first_name,
    lcl.patient_name::text as patient_name,
    adj.adjustment_comment::text as adjustment_comment
FROM
    vw_adjustment_charge_line_base adj
    INNER JOIN vw_charge_line_report_base lcl ON lcl.charge_no = adj.charge_no AND
    lcl.invoice_no = adj.invoice_no
WHERE 
    lcl.revenue_accepted_posted = 'Yes'
ORDER BY
    adj.transaction_date DESC, 
    adj.charge_no;

DROP VIEW IF EXISTS vw_ar_invoice_adjustment_report CASCADE;
CREATE OR REPLACE VIEW vw_ar_invoice_adjustment_report AS
WITH invoice_adjustments AS (
    SELECT DISTINCT ON (adj.invoice_no)
        adj.invoice_no,
        SUM(adj.adjusted_amount_raw) as adjusted_amount_raw
    FROM
        vw_adjustment_charge_line_base adj
    GROUP BY
        adj.invoice_no
)
SELECT
    'billing_invoice'::text as query_form,
    bi.invoice_id::integer as query_id,
    bi.prior_auth_required::text as prior_auth_required,
    bi.date_of_service::date as date_of_service,
    bi."DOS"::text as "DOS",
    bi.date_of_service_end::date as date_of_service_end,
    bi."DOS End"::text as "DOS End",
    bi.followup_dt::date as followup_dt,
    bi.followup_date::text as followup_date,
    bi.confirmed_dt::timestamp as confirmed_dt,
    bi.confirmed_date::text as confirmed_date,
    bi.created_date::timestamp as created_date,
    bi.payer::integer as payer,
    bi.payer_auto_name::text as payer_auto_name,
    bi.therapy::text as therapy,
    bi.therapy_auto_name::text as therapy_auto_name,
    bi.physician::integer as physician,
    bi.physician_auto_name::text as physician_auto_name,
    bi.sales_territory::integer as sales_territory,
    bi.sales_territory_auto_name::text as sales_territory_auto_name,
    bi.sales_rep::integer as sales_rep,
    bi.sales_rep_auto_name::text as sales_rep_auto_name,
    bi.sales_account::integer as sales_account,
    bi.sales_account_auto_name::text as sales_account_auto_name,
    bi.nursing_agency::integer as nursing_agency,
    bi.nursing_agency_auto_name::text as nursing_agency_auto_name,
    bi.infusion_suite::integer as infusion_suite,
    bi.infusion_suite_auto_name::text as infusion_suite_auto_name,
    bi.patient_status::text as patient_status,
    bi.patient_status_auto_name::text as patient_status_auto_name,
    bi.report_quantity_raw::numeric as report_quantity_raw,
    bi.report_unit_id_raw::text as report_unit_id_raw,
    bi.report_quantity::text as report_quantity,
    bi.billing_method::text as billing_method,
    bi.billing_method_auto_name::text as billing_method_auto_name,
    bi.physician_city::text as physician_city,
    bi.physician_state::text as physician_state,
    bi.physician_state_auto_name::text as physician_state_auto_name,
    bi.physician_zip::text as physician_zip,
    bi.payer_type::text as payer_type,
    bi.payer_type_auto_name::text as payer_type_auto_name,
    bi.awp_price_raw::numeric as awp_price_raw,
    bi.awp_price::text as awp_price,
    bi.last_name::text as last_name,
    bi.first_name::text as first_name,
    bi.patient_name::text as patient_name,
    bi.is_specialty::text as is_specialty,
    bi.item::integer as item,
    bi.item_auto_name::text as item_auto_name,
    bi.brand_name::text as brand_name,
    bi.brand_name_auto_name::text as brand_name_auto_name,
    bi.manufacturer::text as manufacturer,
    bi.manufacturer_auto_name::text as manufacturer_auto_name,
    bi.fill_number::integer as fill_number,
    bi.rx_no::text as rx_no,
    bi.revenue_code::text as revenue_code,
    bi.revenue_code_auto_name::text as revenue_code_auto_name,
    bi.collector_auto_name::text as collector_auto_name,
    bi.collector::text as collector,
    bi.confirmed_by::integer as confirmed_by,
    bi.confirmed_by_auto_name::text as confirmed_by_auto_name,

    ia.adjusted_amount_raw::numeric as adjusted_amount_raw,
    format_currency_short(ia.adjusted_amount_raw) as adjusted_amount
FROM
    vw_invoice_report_base bi
    INNER JOIN invoice_adjustments ia ON ia.invoice_no = bi.invoice_no
WHERE 
    bi.revenue_accepted_posted = 'Yes'
ORDER BY
    bi.post_datetime DESC, 
    bi.invoice_no;
