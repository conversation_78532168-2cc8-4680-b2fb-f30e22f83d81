
-- Create trigger function
DO $$ BEGIN
  PERFORM drop_all_function_signatures('link_rx_and_invoice');
END $$;
CREATE OR REPLACE FUNCTION link_rx_and_invoice()
RETURNS TRIGGER AS $$
DECLARE
    v_start_time timestamp;
    v_params jsonb;
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Record start time for logging
    v_start_time := clock_timestamp();

    -- Build parameters JSON for logging
    v_params := jsonb_build_object(
        'invoice_id', NEW.id,
        'invoice_no', NEW.invoice_no
    );

    BEGIN
        -- Log function call
        PERFORM log_billing_function(
            'link_rx_and_invoice'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            NULL::text,
            clock_timestamp() - v_start_time
        );

        -- Validate input
        IF NEW.id IS NULL THEN
            RAISE EXCEPTION 'Invoice ID cannot be null';
        END IF;

        IF NEW.invoice_no IS NULL THEN
            RAISE EXCEPTION 'Invoice number cannot be null';
        END IF;

        IF COALESCE(NEW.void, 'No') <> 'Yes' THEN
            RETURN NEW;
        END IF;

        -- Delete any existing links that are no longer valid
        DELETE FROM gr_form_billing_invoice_rx_id_to_careplan_order_rx_id
        WHERE form_billing_invoice_fk = NEW.id
        AND form_careplan_order_rx_fk NOT IN (
            SELECT DISTINCT fcor.id
            FROM form_ledger_charge_line flcl
            JOIN form_careplan_order_rx fcor ON flcl.order_rx_id = fcor.id 
            AND fcor.archived IS NOT TRUE AND fcor.deleted IS NOT TRUE
            WHERE flcl.invoice_no = NEW.invoice_no 
            AND flcl.archived IS NOT TRUE 
            AND flcl.deleted IS NOT TRUE
            AND COALESCE(flcl.void, 'No') <> 'Yes'
        );

        -- Insert new links for any missing associations
        INSERT INTO gr_form_billing_invoice_rx_id_to_careplan_order_rx_id 
            (form_billing_invoice_fk, form_careplan_order_rx_fk)
        SELECT DISTINCT 
            NEW.id,
            fcor.id
        FROM form_ledger_charge_line flcl
        JOIN form_careplan_order_rx fcor ON flcl.order_rx_id = fcor.id
        WHERE flcl.invoice_no = NEW.invoice_no
        AND flcl.archived IS NOT TRUE
        AND flcl.deleted IS NOT TRUE
        AND COALESCE(flcl.void, 'No') <> 'Yes'
        AND NOT EXISTS (
            SELECT 1 
            FROM gr_form_billing_invoice_rx_id_to_careplan_order_rx_id g
            WHERE g.form_billing_invoice_fk = NEW.id
            AND g.form_careplan_order_rx_fk = fcor.id
            AND fcor.archived IS NOT TRUE AND fcor.deleted IS NOT TRUE
        );

        RETURN NEW;

    EXCEPTION WHEN OTHERS THEN
        -- Log error and re-raise
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Error in link_rx_and_invoice trigger',
            'TRIGGER',
            current_schema(),
            'form_billing_invoice',
            jsonb_build_object(
                'function_name', 'link_rx_and_invoice',
                'invoice_id', NEW.id,
                'invoice_no', NEW.invoice_no
            )
        );
        RAISE;
    END;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Create trigger
CREATE OR REPLACE TRIGGER link_rx_and_invoice_update_trigger
AFTER UPDATE ON form_billing_invoice
FOR EACH ROW
WHEN (COALESCE(OLD.void,'No') <> 'Yes' AND COALESCE(OLD.zeroed,'No') <> 'Yes')
EXECUTE FUNCTION link_rx_and_invoice();

-- Create trigger for INSERT (no conditional based on OLD values)
CREATE OR REPLACE TRIGGER link_rx_and_invoice_insert_trigger
AFTER INSERT ON form_billing_invoice
FOR EACH ROW
EXECUTE FUNCTION link_rx_and_invoice();