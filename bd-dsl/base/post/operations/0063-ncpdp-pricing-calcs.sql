
CREATE OR REPLACE FUNCTION get_ncpdp_pricing_calculations(
    p_insurance_id integer,
    p_site_id integer,
    p_patient_id integer,
    p_charge_lines charge_line_with_split[],
    p_parent_claim_no text DEFAULT NULL
) RETURNS TABLE (
    u_and_c_charge numeric,
    pt_pd_amt_sub numeric,
    ing_cst_sub numeric,
    dispense_fee numeric,
    gross_amount_due numeric,
    cost numeric,
    expected numeric
) AS $BODY$
DECLARE
    v_start_time timestamp;
    v_execution_time interval;
    v_error_message text;
    v_params jsonb;
BEGIN
    -- Record start time
    v_start_time := clock_timestamp();

    -- Build parameters JSON for logging
    v_params := jsonb_build_object(
        'insurance_id', p_insurance_id,
        'site_id', p_site_id,
        'patient_id', p_patient_id,
        'charge_lines', p_charge_lines,
        'parent_claim_no', p_parent_claim_no
    );

    BEGIN  -- Start exception block
        -- Log function call
        PERFORM log_billing_function(
            'get_ncpdp_pricing_calculations'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            NULL::text,
            clock_timestamp() - v_start_time
        );

        IF p_charge_lines IS NULL OR array_length(p_charge_lines, 1) IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Charge lines are required',
                'Validating required parameters in get_ncpdp_pricing_calculations',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                v_params
            );
            RAISE EXCEPTION 'Charge lines are required to calculate NCPDP pricing';
        END IF;

        IF p_insurance_id IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Insurance ID cannot be null',
                'Validating required parameters in get_ncpdp_pricing_calculations',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                v_params
            );
            RAISE EXCEPTION 'Insurance ID cannot be null';
        END IF;

        IF p_site_id IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Site ID cannot be null',
                'Validating required parameters in get_ncpdp_pricing_calculations',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                v_params
            );
            RAISE EXCEPTION 'Site ID cannot be null';
        END IF;

        IF p_patient_id IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Patient ID cannot be null',
                'Validating required parameters in get_ncpdp_pricing_calculations',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                v_params
            );
            RAISE EXCEPTION 'Patient ID cannot be null';
        END IF;

        RETURN QUERY
        WITH insurance_info AS (
            SELECT * FROM get_insurance_claim_settings(p_insurance_id, p_site_id, NULL::integer)
        ),
        claim_data AS (
            SELECT * FROM get_primary_ncpdp_claim_info(p_parent_claim_no)
        ),
        charge_line_sums AS (
            SELECT
                ROUND(SUM((cl).list_price::numeric), 2)::numeric as u_and_c_charge,
                ROUND(SUM((cl).billed::numeric + COALESCE((cl).dispense_fee::numeric, 0.0)), 2)::numeric as gross_amount_due,
                ROUND(SUM((cl).billed::numeric), 2)::numeric as ing_cst_sub,
                ROUND(SUM((cl).dispense_fee::numeric), 2)::numeric as dispense_fee,
                ROUND(SUM((cl).total_cost::numeric), 2)::numeric as cost,
                ROUND(SUM((cl).expected::numeric), 2)::numeric as expected
            FROM unnest(p_charge_lines) AS cl
        )
        SELECT
            COALESCE(
                cd.u_and_c_charge::numeric,
                lgs.u_and_c_charge::numeric,
                0.00::numeric
            )::numeric as u_and_c_charge,
            COALESCE(SUM(cd.pt_pay_amt::numeric), 0)::numeric as pt_pd_amt_sub,
            COALESCE(
                CASE
                    WHEN p_parent_claim_no IS NOT NULL THEN
                        CASE
                            WHEN ins.ncpdp_sec_claims_ingred_cost = 'Send Zeros' THEN 0.0::numeric
                            WHEN ins.ncpdp_sec_claims_ingred_cost = 'Send Co-pay Amount' AND cd.pt_pay_amt::numeric > 0 THEN cd.pt_pay_amt::numeric
                            ELSE NULL::numeric
                        END
                    ELSE NULL
                END,
                cd.ing_cst_sub::numeric,
                lgs.ing_cst_sub::numeric,
                0.00::numeric
            )::numeric as ing_cst_sub,
            CASE WHEN COALESCE(cd.disp_fee_sub, lgs.dispense_fee) > 0 THEN COALESCE(
                cd.disp_fee_sub::numeric,
                lgs.dispense_fee::numeric
            )::numeric ELSE 0.00::numeric END as dispense_fee,
            COALESCE(
                CASE
                    WHEN p_parent_claim_no IS NOT NULL THEN
                        CASE
                            WHEN ins.ncpdp_sec_claims_gross_amount_due = 'Send Zeros' THEN 0.0
                            WHEN ins.ncpdp_sec_claims_gross_amount_due = 'Send Co-pay Amount' AND cd.pt_pay_amt::numeric > 0 THEN cd.pt_pay_amt
                            ELSE NULL
                        END
                    ELSE NULL
                END,
                cd.gross_amount_due::numeric,
                lgs.gross_amount_due::numeric,
                0.00::numeric
            )::numeric as gross_amount_due,
            COALESCE(
                cd.cost::numeric,
                lgs.cost::numeric,
                0.00::numeric
            )::numeric as cost,
            COALESCE(
                cd.expected::numeric,
                lgs.expected::numeric,
                0.00::numeric
            )::numeric as expected
        FROM inventory_data inv
        CROSS JOIN insurance_info ins
        LEFT JOIN charge_line_sums lgs ON true
        LEFT JOIN claim_data cd ON true
        GROUP BY
            cd.u_and_c_charge,
            lgs.u_and_c_charge,
            cd.ing_cst_sub,
            lgs.ing_cst_sub,
            cd.disp_fee_sub,
            lgs.dispense_fee,
            cd.gross_amount_due,
            lgs.gross_amount_due,
            cd.pt_pay_amt,
            ins.ncpdp_sec_claims_ingred_cost,
            ins.ncpdp_sec_claims_gross_amount_due,
            lgs.cost,
            lgs.expected;

        -- Log successful completion
        PERFORM log_billing_function(
            'get_ncpdp_pricing_calculations'::tracked_function,
            v_params::jsonb,
            NULL::jsonb, -- Not logging result due to complexity
            NULL::text,
            clock_timestamp() - v_start_time
        );

    EXCEPTION WHEN OTHERS THEN
        -- Log error
        v_error_message := SQLERRM;
        
        -- Log to billing error log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            v_error_message,
            'Exception in get_ncpdp_pricing_calculations',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );

        -- Log to NCPDP function log
        PERFORM log_billing_function(
            'get_ncpdp_pricing_calculations'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            v_error_message::text,
            clock_timestamp() - v_start_time
        );
        RAISE;
    END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;