
CREATE OR REPLACE FUNCTION build_insurance_ncpdp_segment(
  p_insurance_id integer,
  p_site_id integer,
  p_transaction_code text,
  p_previous_payer_id integer DEFAULT NULL
) RETURNS ncpdp_insurance AS $BODY$
DECLARE
  v_start_time timestamp;
  v_execution_time interval;
  v_result ncpdp_insurance;
  v_error_message text;
  v_params jsonb;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'insurance_id', p_insurance_id,
    'site_id', p_site_id,
    'transaction_code', p_transaction_code,
    'previous_payer_id', p_previous_payer_id
  );

  BEGIN  -- Start exception block
    -- Log function call
    PERFORM log_billing_function(
      'build_insurance_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    -- Validate required parameters
    IF p_insurance_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Insurance ID cannot be null',
            'Validating required fields in build_insurance_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_insurance_ncpdp_segment',
                'transaction_code', p_transaction_code
            )
        );
        RAISE EXCEPTION 'Insurance ID cannot be null';
    END IF;

    IF p_site_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Site ID cannot be null',
            'Validating required fields in build_insurance_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_insurance_ncpdp_segment',
                'transaction_code', p_transaction_code
            )
        );
        RAISE EXCEPTION 'Site ID cannot be null';
    END IF;

    IF p_transaction_code IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Transaction code cannot be null',
            'Validating required fields in build_insurance_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_insurance_ncpdp_segment',
                'transaction_code', p_transaction_code
            )
        );
        RAISE EXCEPTION 'Transaction code cannot be null';
    END IF;
    -- Get insurance settings once
    WITH insurance_info AS (
      SELECT * FROM get_insurance_claim_settings(p_insurance_id, p_site_id, p_previous_payer_id)
    )
    SELECT
      p_transaction_code::text AS transaction_code,
      p_insurance_id::integer AS insurance_id,
      ins.type_id::text AS payer_type_id,
      CASE
        WHEN p_transaction_code IN ('B2', 'S2') THEN
          CASE
             WHEN COALESCE(ins.transmit_card_id_reversal, 'No') = 'Yes' THEN ins.cardholder_id
             ELSE NULL
          END
        ELSE ins.cardholder_id
      END::text AS card_holder_id,
      CASE
        WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3', 'E1') THEN COALESCE(pi.pharmacy_relationship_id, '1')
        ELSE NULL
      END::text AS pt_rel_code,
      pt.firstname::text AS card_holder_first_name,
      pt.lastname::text AS card_holder_last_name,
      CASE
        WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3') THEN ins.plan_name
        ELSE NULL
      END::text AS plan_id,
      CASE
        WHEN p_transaction_code IN ('B2', 'S2') THEN
          CASE
             WHEN COALESCE(ins.transmit_grp_no_reversal, 'No') = 'Yes' THEN ins.group_number
             ELSE NULL
          END
        ELSE ins.group_number
      END::text AS group_id,
      CASE
        WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3', 'E1') THEN ins.person_code
        ELSE NULL
      END::text AS person_code,
      ins.home_plan::text AS home_plan,
      CASE
        WHEN p_transaction_code NOT IN ('E1') THEN ins.medigap_id
        ELSE NULL
      END::text AS medigap_id,
      CASE 
        WHEN ins.type_id = 'MEDI' AND p_transaction_code IN ('B1', 'B3', 'S1', 'S3') THEN ptpa.state_id
        ELSE NULL
      END::text AS mcd_indicator,
      CASE
        WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3') THEN 'Y'
        ELSE NULL
      END::text AS dr_accept_indicator,
      CASE
        WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3') THEN '0'
        ELSE NULL
      END::text AS elig_clar_code,
      CASE
        WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3') THEN 'N'
        ELSE NULL
      END::text AS partd_facility,
      CASE 
        WHEN p_transaction_code NOT IN ('E1') THEN ins.medicaid_number
        ELSE NULL
      END::text AS mcd_id_no
    INTO v_result
    FROM form_patient_insurance pi 
    INNER JOIN form_patient pt ON pt.id = pi.patient_id 
      AND pt.archived IS NOT TRUE 
      AND pt.deleted IS NOT TRUE
    INNER JOIN form_payer py ON py.id = pi.payer_id 
      AND py.archived IS NOT TRUE 
      AND py.deleted IS NOT TRUE
    CROSS JOIN insurance_info ins
    LEFT JOIN LATERAL(
      SELECT 
        state_id
      FROM form_patient_address pa 
      WHERE pa.patient_id = pt.id 
        AND pa.archived IS NOT TRUE 
        AND pa.deleted IS NOT TRUE
      ORDER BY 
        CASE
          WHEN address_type = 'Home' THEN 0
          WHEN address_type = 'Shipping' THEN 1 
          ELSE 2
        END 
      LIMIT 1 
    ) ptpa ON TRUE 
    WHERE pi.id = p_insurance_id 
      AND pi.archived IS NOT TRUE 
      AND pi.deleted IS NOT TRUE;

    -- Validate we got a result
    IF v_result IS NULL THEN
      INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
      ) VALUES (
        'No insurance data found',
        'Validating result in build_insurance_ncpdp_segment',
        'FUNCTION',
        current_schema(),
        'form_ncpdp',
        jsonb_build_object(
          'function_name', 'build_insurance_ncpdp_segment',
          'insurance_id', p_insurance_id
        )
      );
      RAISE EXCEPTION 'No insurance data found for insurance_id: %', p_insurance_id;
    END IF;

    -- Log successful completion
    PERFORM log_billing_function(
      'build_insurance_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    -- Log error
    v_error_message := SQLERRM;
    
    -- Log to billing error log
    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_insurance_ncpdp_segment',
      'FUNCTION',
      current_schema(),
      'form_ncpdp',
      jsonb_build_object(
        'function_name', 'build_insurance_ncpdp_segment',
        'insurance_id', p_insurance_id
      )
    );

    -- Log to NCPDP function log
    PERFORM log_billing_function(
      'build_insurance_ncpdp_segment'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;