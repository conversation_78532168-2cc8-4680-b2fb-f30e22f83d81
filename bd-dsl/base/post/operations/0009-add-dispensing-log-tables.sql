CREATE TABLE IF NOT EXISTS dispensing_error_log (
    id BIGSERIAL PRIMARY KEY,
    error_message TEXT NOT NULL,
    error_context TEXT,
    occurred_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    error_type TEXT,
    schema_name TEXT,
    table_name TEXT,
    user_name TEXT DEFAULT CURRENT_USER,
    application_name TEXT,
    client_address INET,
    additional_details JSONB
);

-- Optional: Create an index for faster querying
CREATE INDEX IF NOT EXISTS idx_dispensing_error_log_occurred_at ON dispensing_error_log(occurred_at);
CREATE INDEX IF NOT EXISTS idx_dispensing_error_log_error_type ON dispensing_error_log(error_type);

DO $$ BEGIN
  PERFORM drop_all_function_signatures('truncate_dispensing_error_log');
END $$;
CREATE OR REPLACE FUNCTION truncate_dispensing_error_log()
RETURNS TRIGGER AS $$
DECLARE
    v_row_count INTEGER;
BEGIN
    -- Count total rows in the log table
    SELECT COUNT(*) INTO v_row_count FROM dispensing_error_log;

    -- If rows exceed 1000, delete the oldest rows
    IF v_row_count > 1000 THEN
        DELETE FROM dispensing_error_log
        WHERE id IN (   
            SELECT id 
            FROM dispensing_error_log
            ORDER BY occurred_at ASC
            LIMIT (v_row_count - 100)
        );
    END IF;
    
    RETURN NULL;  -- Trigger returns NULL for after insert triggers
END;
$$ LANGUAGE plpgsql;

DO $$ BEGIN
  PERFORM drop_all_function_signatures('truncate_trigger_log');
END $$;
CREATE OR REPLACE FUNCTION truncate_trigger_log()
RETURNS TRIGGER AS $$
DECLARE
    v_row_count INTEGER;
BEGIN
    -- Count total rows in the log table
    SELECT COUNT(*) INTO v_row_count FROM trigger_log;

    -- If rows exceed 1000, delete the oldest rows
    IF v_row_count > 1000 THEN
        DELETE FROM trigger_log
        WHERE id IN (   
            SELECT id 
            FROM trigger_log
            ORDER BY occurred_at ASC
            LIMIT (v_row_count - 100)
        );
    END IF;
    
    RETURN NULL;  -- Trigger returns NULL for after insert triggers
END;
$$ LANGUAGE plpgsql;

CREATE TABLE IF NOT EXISTS trigger_log (
    id BIGSERIAL PRIMARY KEY,
    trigger_name TEXT NOT NULL,
    trigger_table TEXT,
    trigger_type TEXT,
    occurred_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    user_name TEXT DEFAULT CURRENT_USER
);

CREATE INDEX IF NOT EXISTS idx_trigger_log_occurred_at ON trigger_log(occurred_at);
CREATE INDEX IF NOT EXISTS idx_trigger_log_trigger_type ON trigger_log(trigger_type);

-- Create a new trigger
CREATE OR REPLACE TRIGGER maintain_dispensing_error_log_size
AFTER INSERT ON dispensing_error_log
FOR EACH STATEMENT
EXECUTE FUNCTION truncate_dispensing_error_log();

CREATE OR REPLACE TRIGGER maintain_trigger_log_size
AFTER INSERT ON trigger_log
FOR EACH STATEMENT
EXECUTE FUNCTION truncate_trigger_log();
