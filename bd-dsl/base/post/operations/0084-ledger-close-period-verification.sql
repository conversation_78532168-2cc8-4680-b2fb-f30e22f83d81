-- Period Closing Balance Verification
-- This trigger ensures that debits = credits before a period can be closed

-- Function to verify period balance before locking
DO $$ BEGIN
  PERFORM drop_all_function_signatures('verify_period_balance');
END $$;
CREATE OR REPLACE FUNCTION verify_period_balance() RETURNS TRIGGER AS $$
DECLARE
    v_debit_sum DECIMAL;
    v_credit_sum DECIMAL;
    v_imbalance DECIMAL;
    v_account_imbalances RECORD;
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Skip if not locking the period or if voided/deleted/archived
    IF NEW.locked_period <> 'Yes' OR 
       NEW.void IS NOT NULL OR 
       NEW.deleted IS TRUE OR 
       NEW.archived IS TRUE THEN
        RETURN NEW;
    END IF;
    
    -- Log function entry
    RAISE LOG 'Verifying period balance for closing period: % to %', 
             NEW.start_date, NEW.end_date;
    
    -- Check overall balance for the period
    SELECT COALESCE(SUM(debit), 0) as total_debit, COALESCE(SUM(credit), 0) as total_credit
    INTO v_debit_sum, v_credit_sum
    FROM form_ledger_finance
    WHERE post_datetime BETWEEN NEW.start_date AND NEW.end_date;
    
    -- Calculate imbalance with 2 decimal precision
    v_imbalance := ROUND((v_debit_sum - v_credit_sum)::numeric, 2);
    
    IF v_imbalance <> 0 THEN
        -- Log error to billing_error_log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Period cannot be closed due to unbalanced ledger',
            'Error verifying period balance',
            'TRIGGER',
            current_schema(),
            'billing_closing',
            jsonb_build_object(
                'function_name', 'verify_period_balance',
                'close_no', NEW.close_no,
                'start_date', NEW.start_date,
                'end_date', NEW.end_date,
                'total_debit', v_debit_sum,
                'total_credit', v_credit_sum,
                'imbalance', v_imbalance
            )
        );
        
        RAISE EXCEPTION 'Period cannot be closed: ledger is unbalanced. Total debits: %, total credits: %, imbalance: %', 
                        v_debit_sum, v_credit_sum, v_imbalance;
    END IF;
    
    -- Check balance by account_id (more detailed check)
    FOR v_account_imbalances IN
        SELECT 
            account_id,
            SUM(debit) as total_debit,
            SUM(credit) as total_credit,
            ROUND((SUM(debit) - SUM(credit))::numeric, 2) as imbalance
        FROM form_ledger_finance
        WHERE post_datetime BETWEEN NEW.start_date AND NEW.end_date
        GROUP BY account_id
        HAVING ROUND((SUM(debit) - SUM(credit))::numeric, 2) <> 0
    LOOP
        -- Log warning for account imbalance
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Account imbalance detected during period closing',
            'Warning: Account level imbalance',
            'WARNING',
            current_schema(),
            'billing_closing',
            jsonb_build_object(
                'function_name', 'verify_period_balance',
                'close_no', NEW.close_no,
                'account_id', v_account_imbalances.account_id,
                'total_debit', v_account_imbalances.total_debit,
                'total_credit', v_account_imbalances.total_credit,
                'imbalance', v_account_imbalances.imbalance
            )
        );
        
        RAISE WARNING 'Account % is unbalanced: debits %, credits %, imbalance %', 
                      v_account_imbalances.account_id,
                      v_account_imbalances.total_debit,
                      v_account_imbalances.total_credit,
                      v_account_imbalances.imbalance;
    END LOOP;
    
    -- Calculate summary totals for the period
    SELECT 
        COALESCE(SUM(CASE WHEN account_type = 'AR' THEN debit - credit ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN account_type = 'AR' THEN 0 ELSE debit - credit END), 0)
    INTO 
        NEW.tot_ar,
        NEW.tot_ap
    FROM form_ledger_finance
    WHERE post_datetime BETWEEN NEW.start_date AND NEW.end_date;
    
    -- Calculate additional financial metrics
    SELECT 
        COALESCE(SUM(CASE WHEN account_type = 'Revenue' THEN credit - debit ELSE 0 END), 0)
    INTO 
        NEW.tot_cost
    FROM form_ledger_finance
    WHERE post_datetime BETWEEN NEW.start_date AND NEW.end_date;
    
    -- Calculate total credits/debits
    SELECT 
        COALESCE(SUM(credit), 0),
        COALESCE(SUM(debit), 0)
    INTO 
        NEW.tot_credits,
        NEW.tot_debits
    FROM form_ledger_finance
    WHERE post_datetime BETWEEN NEW.start_date AND NEW.end_date;
    
    RAISE LOG 'Period balance verification completed successfully';
    RETURN NEW;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Trigger to verify period balance before closing
CREATE OR REPLACE TRIGGER trg_billing_closing_verify_balance
BEFORE INSERT OR UPDATE ON form_billing_closing
FOR EACH ROW
WHEN (COALESCE(NEW.locked_period, 'No') = 'Yes')
EXECUTE FUNCTION verify_period_balance();

DO $$ BEGIN
  PERFORM drop_all_function_signatures('update_records_closing_status');
END $$;
CREATE OR REPLACE FUNCTION update_records_closing_status() RETURNS TRIGGER AS $$
DECLARE
    v_record_count INTEGER := 0;
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Skip if inappropriate conditions
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    END IF;
    
    -- Log function entry
    RAISE LOG 'Updating closing status for period: % to %', 
             NEW.start_date, NEW.end_date;
             
    -- If this is a locked period that is not voided/deleted/archived
    IF (COALESCE(NEW.locked_period,No) = 'Yes' AND 
        NEW.void IS NULL AND 
        NEW.deleted IS NOT TRUE AND 
        NEW.archived IS NOT TRUE) THEN
        
        RAISE LOG 'Setting close_no % on records from % to %', 
                 NEW.close_no, NEW.start_date, NEW.end_date;
        PERFORM set_config('clara.prevent_locked_checks', 'on', false);

        -- Update billing_invoice records
        UPDATE form_billing_invoice
        SET close_no = NEW.close_no
        WHERE post_datetime BETWEEN NEW.start_date AND NEW.end_date
        AND (close_no IS NULL OR close_no <> NEW.close_no);
        GET DIAGNOSTICS v_record_count = ROW_COUNT;
        RAISE LOG 'Updated % invoice records', v_record_count;
        PERFORM set_config('clara.prevent_locked_checks', 'off', false);

        -- Store count of associated records in JSON format
        WITH record_counts AS (
            SELECT 
                (SELECT COUNT(*) FROM form_billing_invoice 
                 WHERE post_datetime BETWEEN NEW.start_date AND NEW.end_date) AS invoice_count,
                (SELECT COUNT(*) FROM form_billing_adjustment 
                 WHERE post_datetime BETWEEN NEW.start_date AND NEW.end_date) AS adjustment_count,
                (SELECT COUNT(*) FROM form_billing_writeoff 
                 WHERE post_datetime BETWEEN NEW.start_date AND NEW.end_date) AS writeoff_count,
                (SELECT COUNT(*) FROM form_billing_posting 
                 WHERE post_datetime BETWEEN NEW.start_date AND NEW.end_date) AS posting_count
        )
        UPDATE form_billing_closing
        SET associated_records = jsonb_build_object(
            'invoices', invoice_count,
            'adjustments', adjustment_count,
            'writeoffs', writeoff_count,
            'postings', posting_count
        )::json
        FROM record_counts
        WHERE id = NEW.id;
        
    -- If unlocking/voiding/deleting the period
    ELSIF (COALESCE(NEW.locked_period, 'No') = 'No' OR
           NEW.void IS NOT NULL OR
           NEW.deleted IS TRUE OR
           NEW.archived IS TRUE) THEN
        
        RAISE LOG 'Removing close_no from records with close_no: %', NEW.close_no;
        PERFORM set_config('clara.prevent_locked_checks', 'on', false);

        -- Update billing_invoice records
        
        UPDATE form_billing_invoice
        SET close_no = NULL
        WHERE close_no = OLD.close_no;
        GET DIAGNOSTICS v_record_count = ROW_COUNT;
        RAISE LOG 'Cleared close_no from % invoice records', v_record_count;
        PERFORM set_config('clara.prevent_locked_checks', 'off', false);

        -- Clear associated records count
        UPDATE form_billing_closing
        SET associated_records = NULL
        WHERE id = NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update records with close_no
CREATE OR REPLACE TRIGGER trg_billing_closing_update_records
AFTER INSERT OR UPDATE ON form_billing_closing
FOR EACH ROW
EXECUTE FUNCTION update_records_closing_status();