CREATE OR REPLACE FUNCTION build_mm_receiver_segment(
  p_payer_id integer,
  p_patient_id integer DEFAULT NULL,
  p_insurance_id integer DEFAULT NULL,
  p_parent_claim_no text DEFAULT NULL
) RETURNS mm_receiver_segment AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_receiver_segment;
  v_error_message text;
  v_params jsonb;
  v_parent_payer_id integer;
  v_sec_payer_id text;
  v_address_info mm_address_info[];
  v_payer_record record;
BEGIN
  -- Check for null input parameters
  IF p_payer_id IS NULL THEN
    RAISE EXCEPTION 'Payer ID cannot be NULL';
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'payer_id', p_payer_id,
    'patient_id', p_patient_id,
    'insurance_id', p_insurance_id,
    'parent_claim_no', p_parent_claim_no
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
        'build_mm_receiver_segment'::tracked_function,
        v_params,
        NULL::jsonb,
        NULL::text,
        clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM receiver segment for payer ID: %, patient ID: %, parent claim: %', p_payer_id, p_patient_id, p_parent_claim_no;

    -- Get parent payer_id if this is a COB claim
    IF p_parent_claim_no IS NOT NULL THEN
        SELECT pmc.payer_id
        INTO v_parent_payer_id
        FROM form_med_claim pmc
        WHERE pmc.claim_no = p_parent_claim_no
          AND pmc.deleted IS NOT TRUE 
          AND pmc.archived IS NOT TRUE;
    END IF;

    -- Check for sec_payer_id override from patient insurance medical conditions
    IF p_patient_id IS NOT NULL AND p_insurance_id IS NOT NULL THEN
        SELECT pimc.sec_payer_id
        INTO v_sec_payer_id
        FROM form_patient_insurance_med_cond pimc
        WHERE pimc.patient_id = p_patient_id
          AND pimc.active_payer_id = p_payer_id
          AND (v_parent_payer_id IS NULL OR pimc.previous_payer_id = v_parent_payer_id)
          AND pimc.deleted IS NOT TRUE 
          AND pimc.archived IS NOT TRUE
          AND pimc.sec_payer_id IS NOT NULL
        ORDER BY pimc.created_on DESC
        LIMIT 1;
    END IF;

    -- Get payer record
    SELECT 
        payer.id,
        payer.organization,
        payer.mm_payer_name,
        payer.baddress1,
        payer.baddress2,
        payer.bcity,
        payer.bstate_id,
        payer.bzip,
        payer.address1,
        payer.address2,
        payer.city,
        payer.state_id,
        payer.zip
    INTO v_payer_record
    FROM form_payer payer
    WHERE payer.id = p_payer_id
      AND payer.deleted IS NOT TRUE 
      AND payer.archived IS NOT TRUE;

    -- Validate that we found the payer record
    IF v_payer_record.id IS NULL THEN
        RAISE EXCEPTION 'Payer record not found for ID: %', p_payer_id;
    END IF;

    -- Build address array using billing address first, then regular address as fallback
    IF v_payer_record.baddress1 IS NOT NULL OR v_payer_record.bcity IS NOT NULL THEN
        DECLARE
            v_addr mm_address_info;
        BEGIN
            v_addr.address1 := v_payer_record.baddress1;
            v_addr.address2 := v_payer_record.baddress2;
            v_addr.city := v_payer_record.bcity;
            v_addr.state := v_payer_record.bstate_id;
            v_addr.postal_code := v_payer_record.bzip;
            v_address_info := ARRAY[v_addr];
        END;
    ELSIF v_payer_record.address1 IS NOT NULL OR v_payer_record.city IS NOT NULL THEN
        DECLARE
            v_addr mm_address_info;
        BEGIN
            v_addr.address1 := v_payer_record.address1;
            v_addr.address2 := v_payer_record.address2;
            v_addr.city := v_payer_record.city;
            v_addr.state := v_payer_record.state_id;
            v_addr.postal_code := v_payer_record.zip;
            v_address_info := ARRAY[v_addr];
        END;
    ELSE
        v_address_info := NULL;
    END IF;

    -- Build the receiver segment using direct field assignment
    v_result.payer_id := p_payer_id::integer;
    v_result.organization_name := CASE 
        WHEN v_sec_payer_id IS NOT NULL THEN v_sec_payer_id
        ELSE COALESCE(v_payer_record.mm_payer_name, v_payer_record.organization)
    END::text;
    v_result.address := v_address_info;

    RAISE LOG 'MM receiver segment build result: payer_id=%, org_name=%, address_count=%', 
        v_result.payer_id, v_result.organization_name, 
        CASE WHEN v_result.address IS NULL THEN 0 ELSE array_length(v_result.address, 1) END;

    -- Log success
    PERFORM log_billing_function(
        'build_mm_receiver_segment'::tracked_function,
        v_params,
        NULL::jsonb,
        NULL::text,
        clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_receiver_segment',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'build_mm_receiver_segment',
        'payer_id', p_payer_id,
        'patient_id', p_patient_id,
        'insurance_id', p_insurance_id,
        'parent_claim_no', p_parent_claim_no,
        'parent_payer_id', v_parent_payer_id,
        'sec_payer_id', v_sec_payer_id
      )
    );

    PERFORM log_billing_function(
      'build_mm_receiver_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;