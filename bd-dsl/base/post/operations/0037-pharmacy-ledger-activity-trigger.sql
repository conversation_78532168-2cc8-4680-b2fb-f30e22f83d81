-- Main trigger function for prescription verification changes
DO $$ BEGIN
  PERFORM drop_all_function_signatures('log_pharmacy_order_changes');
END $$;
CREATE OR REPLACE FUNCTION log_pharmacy_order_changes()
    RETURNS TRIGGER
    AS $$
DECLARE
    user_id integer;
    localized_ts timestamp with time zone;
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Skip if this is not the right table
    IF TG_TABLE_NAME != 'form_careplan_order_rx' THEN
        RETURN NEW;
    END IF;
    -- Determine the user ID from the context
    user_id := COALESCE(NEW.updated_by, NEW.created_by);
    -- Get localized timestamp
    localized_ts := get_user_localized_datetime(user_id);
    -- Handle rx_verified changes (including inserts and unverifying)
    IF (TG_OP = 'INSERT' AND COALESCE(NEW.rx_verified, 'No') = 'Yes') OR (TG_OP = 'UPDATE' AND COALESCE(OLD.rx_verified, '') != COALESCE(NEW.rx_verified, '')) THEN
        INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, rx_no, form, form_id, field, old_value, new_value, description)
            VALUES (localized_ts, CASE WHEN COALESCE(NEW.rx_verified, 'No') = 'Yes' THEN
                    NEW.verified_by
                ELSE
                    NEW.updated_by
                END, NEW.patient_id, NEW.site_id, NEW.rx_no, 'careplan_order_rx', NEW.id, 'rx_verified', CASE WHEN TG_OP = 'INSERT' THEN
                    '' ELSE COALESCE(OLD.rx_verified, '')
                END, COALESCE(NEW.rx_verified, ''), CASE WHEN NEW.rx_verified = 'Yes' THEN
                    'Prescription verified'
                ELSE
                    'Prescription unverified'
                END);
    END IF;
    -- Handle rx_denied changes
    IF (TG_OP = 'INSERT' AND COALESCE(NEW.rx_denied,'No') = 'Yes') OR (TG_OP = 'UPDATE' AND COALESCE(OLD.rx_denied, '') != 'Yes' AND COALESCE(NEW.rx_denied, 'No') = 'Yes') THEN
        INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, rx_no, form, form_id, field, old_value, new_value, description)
            VALUES (localized_ts, NEW.denied_by, NEW.patient_id, NEW.site_id, NEW.rx_no, 'careplan_order_rx', NEW.id, 'rx_denied', COALESCE(OLD.rx_denied, ''), NEW.rx_denied, 'Prescription denied');
    END IF;
    -- Handle rx_complete changes (including unset/null/empty cases)
    IF TG_OP = 'UPDATE' THEN
        -- Log any change to rx_complete, including to/from NULL or empty string
        IF (OLD.rx_complete IS DISTINCT FROM NEW.rx_complete) OR (OLD.rx_complete != NEW.rx_complete) THEN
            INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, rx_no, form, form_id, field, old_value, new_value, description)
                VALUES (localized_ts, CASE WHEN COALESCE(NEW.rx_complete, 'No') = 'Yes' THEN
                        NEW.completed_by
                    ELSE
                        NEW.updated_by
                    END, NEW.patient_id, NEW.site_id, NEW.rx_no, 'careplan_order_rx', NEW.id, 'rx_complete', COALESCE(OLD.rx_complete, ''), COALESCE(NEW.rx_complete, ''), CASE WHEN NEW.rx_complete = 'Yes' THEN
                        'Prescription setup completed'
                    WHEN NEW.rx_complete IS NULL
                        OR NEW.rx_complete = '' THEN
                        'Prescription setup uncompleted'
                    ELSE
                        'Prescription setup status changed'
                    END);
        END IF;
    END IF;
    -- Handle next_fill_date changes
    IF (TG_OP = 'INSERT' AND NEW.next_fill_date IS NOT NULL) OR (TG_OP = 'UPDATE' AND COALESCE(OLD.next_fill_date::text, '') != COALESCE(NEW.next_fill_date::text, '')) THEN
        INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, rx_no, form, form_id, field, old_value, new_value, description)
            VALUES (localized_ts, user_id, NEW.patient_id, NEW.site_id, NEW.rx_no, 'careplan_order_rx', NEW.id, 'next_fill_date', COALESCE(OLD.next_fill_date::text, ''), COALESCE(NEW.next_fill_date::text, ''), 'Next fill date changed');
    END IF;
    -- Handle next_delivery_date changes
    IF (TG_OP = 'INSERT' AND NEW.next_delivery_date IS NOT NULL) OR (TG_OP = 'UPDATE' AND COALESCE(OLD.next_delivery_date::text, '') != COALESCE(NEW.next_delivery_date::text, '')) THEN
        INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, rx_no, form, form_id, field, old_value, new_value, description)
            VALUES (localized_ts, user_id, NEW.patient_id, NEW.site_id, NEW.rx_no, 'careplan_order_rx', NEW.id, 'next_delivery_date', COALESCE(OLD.next_delivery_date::text, ''), COALESCE(NEW.next_delivery_date::text, ''), 'Next delivery date changed');
    END IF;
    RETURN NEW;
END;
$$
LANGUAGE plpgsql;

-- Trigger function for careplan_orderp_item changes
DO $$ BEGIN
  PERFORM drop_all_function_signatures('log_pharmacy_orderp_item_changes');
END $$;
CREATE OR REPLACE FUNCTION log_pharmacy_orderp_item_changes()
    RETURNS TRIGGER
    AS $$
DECLARE
    user_id integer;
    localized_ts timestamp with time zone;
    v_site_id integer;
    v_order_no text;
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Determine the user ID from the context
    user_id := COALESCE(NEW.updated_by, NEW.created_by);
    -- Get localized timestamp
    localized_ts := get_user_localized_datetime(user_id);
    -- Get site_id and order_no from parent order
    SELECT
        co.site_id,
        co.order_no INTO v_site_id,
        v_order_no
    FROM
        form_careplan_orderp_item coi
        INNER JOIN sf_form_careplan_order_to_careplan_orderp_item sflink ON sflink.form_careplan_orderp_item_fk = coi.id
        INNER JOIN form_careplan_order co ON co.id = sflink.form_careplan_order_fk
    WHERE
        coi.id = NEW.id
        AND (sflink.archive IS NULL
            OR sflink.archive = FALSE)
        AND (sflink.delete IS NULL
            OR sflink.delete = FALSE)
        AND (coi.archived IS NULL
            OR coi.archived = FALSE)
        AND (coi.deleted IS NULL
            OR coi.deleted = FALSE);
    -- Handle order_complete changes
    IF TG_OP = 'UPDATE' THEN
        IF (OLD.order_complete IS DISTINCT FROM NEW.order_complete) OR (COALESCE(OLD.order_complete, '') != COALESCE(NEW.order_complete, '')) THEN
            INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, order_no, rx_no, form, form_id, field, old_value, new_value, description)
                VALUES (localized_ts, CASE WHEN COALESCE(NEW.order_complete, 'No') = 'Yes' THEN
                        NEW.completed_by
                    ELSE
                        NEW.updated_by
                    END, NEW.patient_id, v_site_id, v_order_no, NEW.rx_no, 'careplan_orderp_item', NEW.id, 'order_complete', COALESCE(OLD.order_complete, ''), COALESCE(NEW.order_complete, ''), CASE WHEN NEW.order_complete = 'Yes' THEN
                        'Order completion status set to Yes'
                    WHEN NEW.order_complete = 'No' THEN
                        'Order completion status set to No'
                    WHEN NEW.order_complete IS NULL THEN
                        'Order completion status cleared'
                    ELSE
                        'Order completion status changed'
                    END);
        END IF;
    END IF;
    -- Handle status_id changes
    IF (TG_OP = 'INSERT' AND NEW.status_id IS NOT NULL) OR (TG_OP = 'UPDATE' AND COALESCE(OLD.status_id, '') != COALESCE(NEW.status_id, '')) THEN
        INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, order_no, rx_no, form, form_id, field, old_value, new_value, description)
            VALUES (localized_ts, user_id, NEW.patient_id, v_site_id, v_order_no, NEW.rx_no, 'careplan_orderp_item', NEW.id, 'status_id', COALESCE(OLD.status_id, ''), COALESCE(NEW.status_id, ''), 'Order status changed');
    END IF;
    -- Handle intake_substatus_id changes
    IF (TG_OP = 'INSERT' AND NEW.intake_substatus_id IS NOT NULL) OR (TG_OP = 'UPDATE' AND COALESCE(OLD.intake_substatus_id, '') != COALESCE(NEW.intake_substatus_id, '')) THEN
        INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, order_no, rx_no, form, form_id, field, old_value, new_value, description)
            VALUES (localized_ts, user_id, NEW.patient_id, v_site_id, v_order_no, NEW.rx_no, 'careplan_orderp_item', NEW.id, 'intake_substatus_id', COALESCE(OLD.intake_substatus_id, ''), COALESCE(NEW.intake_substatus_id, ''), 'Intake substatus changed');
    END IF;
    -- Handle auth_flag changes
    IF (TG_OP = 'INSERT' AND NEW.auth_flag IS NOT NULL) OR (TG_OP = 'UPDATE' AND COALESCE(OLD.auth_flag, '') != COALESCE(NEW.auth_flag, '')) THEN
        INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, order_no, rx_no, form, form_id, field, old_value, new_value, description)
            VALUES (localized_ts, user_id, NEW.patient_id, v_site_id, v_order_no, NEW.rx_no, 'careplan_orderp_item', NEW.id, 'auth_flag', COALESCE(OLD.auth_flag, ''), COALESCE(NEW.auth_flag, ''), 'Requires authorization status changed');
    END IF;
    -- Handle bv_flag changes
    IF (TG_OP = 'INSERT' AND NEW.bv_flag IS NOT NULL) OR (TG_OP = 'UPDATE' AND COALESCE(OLD.bv_flag, '') != COALESCE(NEW.bv_flag, '')) THEN
        INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, order_no, rx_no, form, form_id, field, old_value, new_value, description)
            VALUES (localized_ts, user_id, NEW.patient_id, v_site_id, v_order_no, NEW.rx_no, 'careplan_orderp_item', NEW.id, 'bv_flag', COALESCE(OLD.bv_flag, ''), COALESCE(NEW.bv_flag, ''), 'Requires billing verification status changed');
    END IF;
    RETURN NEW;
END;
$$
LANGUAGE plpgsql;

-- Trigger function for careplan_delivery_ticket changes
DO $$ BEGIN
  PERFORM drop_all_function_signatures('log_pharmacy_delivery_ticket_changes');
END $$;
CREATE OR REPLACE FUNCTION log_pharmacy_delivery_ticket_changes()
    RETURNS TRIGGER
    AS $$
DECLARE
    user_id integer;
    localized_ts timestamp with time zone;
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Determine the user ID from the context
    user_id := COALESCE(NEW.updated_by, NEW.created_by);
    -- Get localized timestamp
    localized_ts := get_user_localized_datetime(user_id);
    -- Handle status changes
    IF (TG_OP = 'INSERT' AND NEW.status IS NOT NULL) OR (TG_OP = 'UPDATE' AND COALESCE(OLD.status, '') != COALESCE(NEW.status, '')) THEN
        INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, form, form_id, field, old_value, new_value, description)
            VALUES (localized_ts, user_id, NEW.patient_id, NEW.site_id, 'careplan_delivery_tick', NEW.id, 'status', COALESCE(OLD.status, ''), COALESCE(NEW.status, ''), 'Delivery ticket status changed');
    END IF;
    -- Handle void changes
    IF (TG_OP = 'INSERT' AND NEW.void IS NOT NULL) OR (TG_OP = 'UPDATE' AND COALESCE(OLD.void, '') != COALESCE(NEW.void, '')) THEN
        INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, form, form_id, field, old_value, new_value, description)
            VALUES (localized_ts, user_id, NEW.patient_id, NEW.site_id, 'careplan_delivery_tick', NEW.id, 'void', COALESCE(OLD.void, ''), COALESCE(NEW.void, ''), 'Delivery ticket void status changed');
    END IF;
    -- Handle tech verified changes
    IF (TG_OP = 'INSERT' AND NEW.tech_verified IS NOT NULL) OR (TG_OP = 'UPDATE' AND COALESCE(OLD.tech_verified, '') != COALESCE(NEW.tech_verified, '')) THEN
        INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, form, form_id, field, old_value, new_value, description)
            VALUES (localized_ts, user_id, NEW.patient_id, NEW.site_id, 'careplan_delivery_tick', NEW.id, 'tech_verified', COALESCE(OLD.tech_verified, ''), COALESCE(NEW.tech_verified, ''), 'Delivery ticket tech verification status changed');
    END IF;
    -- Handle verified changes
    IF (TG_OP = 'INSERT' AND NEW.verified IS NOT NULL) OR (TG_OP = 'UPDATE' AND COALESCE(OLD.verified, '') != COALESCE(NEW.verified, '')) THEN
        INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, form, form_id, field, old_value, new_value, description)
            VALUES (localized_ts, user_id, NEW.patient_id, NEW.site_id, 'careplan_delivery_tick', NEW.id, 'verified', COALESCE(OLD.verified, ''), COALESCE(NEW.verified, ''), 'Delivery ticket verification status changed');
    END IF;
    -- Handle confirmed changes
    IF (TG_OP = 'INSERT' AND NEW.confirmed IS NOT NULL) OR (TG_OP = 'UPDATE' AND COALESCE(OLD.confirmed, '') != COALESCE(NEW.confirmed, '')) THEN
        INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, form, form_id, field, old_value, new_value, description)
            VALUES (localized_ts, user_id, NEW.patient_id, NEW.site_id, 'careplan_delivery_tick', NEW.id, 'confirmed', COALESCE(OLD.confirmed, ''), COALESCE(NEW.confirmed, ''), 'Delivery ticket confirmation status changed');
    END IF;
    IF (TG_OP = 'INSERT' AND NEW.ready_to_fill IS NOT NULL) OR (TG_OP = 'UPDATE' AND COALESCE(OLD.ready_to_fill, '') != COALESCE(NEW.ready_to_fill, '')) THEN
        INSERT INTO form_ledger_pharmacy_activity(localized_datetime, user_id, patient_id, site_id, form, form_id, field, old_value, new_value, description)
            VALUES (localized_ts, user_id, NEW.patient_id, NEW.site_id, 'careplan_delivery_tick', NEW.id, 'ready_to_fill', COALESCE(OLD.ready_to_fill, ''), COALESCE(NEW.ready_to_fill, ''), 'Delivery ticket ready to fill status changed');
    END IF;
    RETURN NEW;
END;
$$
LANGUAGE plpgsql;

-- Create the triggers
CREATE OR REPLACE TRIGGER pharmacy_prescription_activity_trigger
    AFTER INSERT OR UPDATE ON form_careplan_order_rx
    FOR EACH ROW
    EXECUTE FUNCTION log_pharmacy_order_changes();

CREATE OR REPLACE TRIGGER pharmacy_order_activity_trigger
    AFTER INSERT OR UPDATE ON form_careplan_order
    FOR EACH ROW
    EXECUTE FUNCTION log_pharmacy_order_changes();

CREATE OR REPLACE TRIGGER pharmacy_orderp_item_activity_trigger
    AFTER INSERT OR UPDATE ON form_careplan_orderp_item
    FOR EACH ROW
    EXECUTE FUNCTION log_pharmacy_orderp_item_changes();

CREATE OR REPLACE TRIGGER pharmacy_delivery_ticket_activity_trigger
    AFTER INSERT OR UPDATE ON form_careplan_delivery_tick
    FOR EACH ROW
    EXECUTE FUNCTION log_pharmacy_delivery_ticket_changes();

-- First revoke all permissions and then grant only insert
REVOKE ALL ON form_ledger_pharmacy_activity FROM PUBLIC;

GRANT INSERT ON form_ledger_pharmacy_activity TO PUBLIC;

-- Prevent updates and deletes on the activity log
DO $$ BEGIN
  PERFORM drop_all_function_signatures('prevent_pharmacy_ledger_modifications');
END $$;
CREATE OR REPLACE FUNCTION prevent_pharmacy_ledger_modifications()
    RETURNS TRIGGER
    AS $$
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    RAISE EXCEPTION 'Modifications to pharmacy activity log are not allowed';
END;
$$
LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER prevent_pharmacy_ledger_modifications_trigger
    BEFORE UPDATE OR DELETE ON form_ledger_pharmacy_activity
    FOR EACH ROW
    EXECUTE FUNCTION prevent_pharmacy_ledger_modifications();
