

-- Helper function to process batch-level information
CREATE OR REPLACE FUNCTION process_835_batch_info(
    p_response_id INTEGER,
    p_response_json JSONB
) RETURNS INTEGER AS $$
DECLARE
    v_batch_record_id INTEGER;
    v_control_number TEXT;
    v_production_date DATE;
    v_total_payment_amount NUMERIC;
    v_credit_debit_flag_code TEXT;
    v_payment_method_code TEXT;
    v_payment_format_code TEXT;
    v_reassociation_trace_number TEXT;
    v_financial_info JSONB;
    v_first_transaction JSONB;
    v_payer_info JSONB;
    v_raw_report_url TEXT;
    v_s3_filehash TEXT;
    v_payer_name TEXT;
    v_payer_id INTEGER;
    v_payer_identification TEXT;
BEGIN
    -- Extract batch-level information
    IF p_response_json ? 'controlNumber' THEN
        v_control_number := p_response_json->>'controlNumber';
    END IF;
    
    IF p_response_json ? 'productionDate' THEN
        v_production_date := safe_to_date(p_response_json->>'productionDate');
    END IF;

    -- Extract financial information
    v_financial_info := CASE 
        WHEN p_response_json ? 'transactions' AND 
             jsonb_array_length(p_response_json->'transactions') > 0 AND
             p_response_json->'transactions'->0 ? 'financialInformation'
        THEN p_response_json->'transactions'->0->'financialInformation'
        ELSE '{}'::jsonb
    END;
    
    IF v_financial_info ? 'totalActualProviderPaymentAmount' THEN
        v_total_payment_amount := safe_to_numeric(v_financial_info->>'totalActualProviderPaymentAmount');
    END IF;
    
    IF v_financial_info ? 'creditOrDebitFlagCode' THEN
        v_credit_debit_flag_code := v_financial_info->>'creditOrDebitFlagCode';
    END IF;
    
    IF v_financial_info ? 'paymentMethodCode' THEN
        v_payment_method_code := v_financial_info->>'paymentMethodCode';
    END IF;
    
    IF v_financial_info ? 'paymentFormatCode' THEN
        v_payment_format_code := v_financial_info->>'paymentFormatCode';
    END IF;
    
    -- Extract reassociation trace number
    IF p_response_json ? 'transactions' AND 
       jsonb_array_length(p_response_json->'transactions') > 0 AND
       p_response_json->'transactions'->0 ? 'paymentAndRemitReassociationDetails' AND
       p_response_json->'transactions'->0->'paymentAndRemitReassociationDetails' ? 'checkOrEFTTraceNumber' THEN
        v_reassociation_trace_number := p_response_json->'transactions'->0->'paymentAndRemitReassociationDetails'->>'checkOrEFTTraceNumber';
    END IF;
    
    -- Extract first transaction and payer info
    v_first_transaction := CASE 
        WHEN p_response_json ? 'transactions' AND 
             jsonb_array_length(p_response_json->'transactions') > 0 
        THEN p_response_json->'transactions'->0
        ELSE '{}'::jsonb
    END;
    
    v_payer_info := CASE 
        WHEN v_first_transaction ? 'payer' 
        THEN v_first_transaction->'payer'
        ELSE '{}'::jsonb
    END;
    
    IF v_payer_info ? 'name' THEN
        v_payer_name := v_payer_info->>'name';
    END IF;

    IF v_payer_info ? 'payerIdentificationNumber' THEN
        v_payer_identification := v_payer_info->>'payerIdentificationNumber';
    END IF;
    
    -- Try to find payer_id based on payer identification
    IF v_payer_identification IS NOT NULL THEN
        SELECT id INTO v_payer_id
        FROM form_payer
        WHERE mm_payer_name = v_payer_name OR mm_payer_name = v_payer_identification OR mm_payer_id = v_payer_identification
        AND COALESCE(deleted, FALSE) = FALSE
        AND COALESCE(archived, FALSE) = FALSE
        LIMIT 1;
    END IF;

    -- Get raw_report_url and s3_filehash from form_med_claim_resp_log
    SELECT 
        raw_report_url,
        s3_filehash
    INTO v_raw_report_url, v_s3_filehash
    FROM form_med_claim_resp_log
    WHERE id = p_response_id
    AND deleted IS NOT TRUE
    AND archived IS NOT TRUE;

    -- Create the batch record
    INSERT INTO form_med_claim_resp_835_batch (
        response_id,
        payer_id,
        control_number,
        production_date,
        check_or_eft_trace_number,
        total_actual_provider_payment_amount,
        credit_or_debit_flag_code,
        payment_method_code,
        payment_format_code,
        sender_dfi_id_number_qualifier,
        sender_dfi_identifier,
        sender_account_number_qualifier,
        sender_account_number,
        receiver_dfi_id_number_qualifier,
        receiver_dfi_identification_number,
        receiver_account_number_qualifier,
        receiver_account_number,
        check_issue_or_eft_effective_date,
        payer_name,
        payer_identification_number,
        payer_cms_plan_id,
        payer_address_1,
        payer_address_2,
        payer_city,
        payer_state,
        payer_zip,
        payer_contact_name,
        technical_contact_name,
        payee_name,
        payee_npi,
        payee_tax_id,
        payee_ncpdp_number,
        remittance_delivery_method_name,
        remittance_delivery_method_email,
        remittance_delivery_method_ftp,
        remittance_delivery_method_on_line,
        total_payment_amount_processed,
        claims_processed,
        response_raw_json,
        raw_report_url,
        s3_filehash,
        created_by,
        created_on,
        archived,
        deleted
    ) VALUES (
        p_response_id,
        v_payer_id,
        v_control_number,
        v_production_date,
        v_reassociation_trace_number,
        v_total_payment_amount,
        v_credit_debit_flag_code,
        v_payment_method_code,
        v_payment_format_code,
        CASE WHEN v_financial_info ? 'senderDfiIdNumberQualifier' THEN v_financial_info->>'senderDfiIdNumberQualifier' END,
        CASE WHEN v_financial_info ? 'senderAccountDetails' AND v_financial_info->'senderAccountDetails' ? 'senderDfiIdentifier' 
             THEN v_financial_info->'senderAccountDetails'->>'senderDfiIdentifier' END,
        CASE WHEN v_financial_info ? 'senderAccountDetails' AND v_financial_info->'senderAccountDetails' ? 'senderAccountNumberQualifier' 
             THEN v_financial_info->'senderAccountDetails'->>'senderAccountNumberQualifier' END,
        CASE WHEN v_financial_info ? 'senderAccountDetails' AND v_financial_info->'senderAccountDetails' ? 'senderAccountNumber' 
             THEN v_financial_info->'senderAccountDetails'->>'senderAccountNumber' END,
        CASE WHEN v_financial_info ? 'receiverAccountDetails' AND v_financial_info->'receiverAccountDetails' ? 'receiverDfiIdNumberQualifier' 
             THEN v_financial_info->'receiverAccountDetails'->>'receiverDfiIdNumberQualifier' END,
        CASE WHEN v_financial_info ? 'receiverAccountDetails' AND v_financial_info->'receiverAccountDetails' ? 'receiverDfiIdentificationNumber' 
             THEN v_financial_info->'receiverAccountDetails'->>'receiverDfiIdentificationNumber' END,
        CASE WHEN v_financial_info ? 'receiverAccountDetails' AND v_financial_info->'receiverAccountDetails' ? 'receiverAccountNumberQualifier' 
             THEN v_financial_info->'receiverAccountDetails'->>'receiverAccountNumberQualifier' END,
        CASE WHEN v_financial_info ? 'receiverAccountDetails' AND v_financial_info->'receiverAccountDetails' ? 'receiverAccountNumber' 
             THEN v_financial_info->'receiverAccountDetails'->>'receiverAccountNumber' END,
        CASE WHEN v_financial_info ? 'checkIssueOrEFTEffectiveDate' 
             THEN safe_to_date(v_financial_info->>'checkIssueOrEFTEffectiveDate') END,
        CASE WHEN v_payer_info ? 'name' THEN v_payer_info->>'name' END,
        CASE WHEN v_payer_info ? 'payerIdentificationNumber' THEN v_payer_info->>'payerIdentificationNumber' END,
        CASE WHEN v_payer_info ? 'centersForMedicareAndMedicaidServicesPlanId' THEN v_payer_info->>'centersForMedicareAndMedicaidServicesPlanId' END,
        CASE WHEN v_payer_info ? 'address' AND v_payer_info->'address' ? 'address1' THEN v_payer_info->'address'->>'address1' END,
        CASE WHEN v_payer_info ? 'address' AND v_payer_info->'address' ? 'address2' THEN v_payer_info->'address'->>'address2' END,
        CASE WHEN v_payer_info ? 'address' AND v_payer_info->'address' ? 'city' THEN v_payer_info->'address'->>'city' END,
        CASE WHEN v_payer_info ? 'address' AND v_payer_info->'address' ? 'state' THEN v_payer_info->'address'->>'state' END,
        CASE WHEN v_payer_info ? 'address' AND v_payer_info->'address' ? 'postalCode' THEN v_payer_info->'address'->>'postalCode' END,
        CASE WHEN v_payer_info ? 'businessContactInformation' AND v_payer_info->'businessContactInformation' ? 'contactName' 
             THEN v_payer_info->'businessContactInformation'->>'contactName' END,
        CASE WHEN v_payer_info ? 'technicalContactInformation' AND v_payer_info->'technicalContactInformation' ? 'contactName' 
             THEN v_payer_info->'technicalContactInformation'->>'contactName' END,
        CASE WHEN v_first_transaction ? 'payee' AND v_first_transaction->'payee' ? 'name' 
             THEN v_first_transaction->'payee'->>'name' END,
        CASE WHEN v_first_transaction ? 'payee' AND v_first_transaction->'payee' ? 'npi' 
             THEN v_first_transaction->'payee'->>'npi' END,
        CASE WHEN v_first_transaction ? 'payee' AND v_first_transaction->'payee' ? 'taxId' 
             THEN v_first_transaction->'payee'->>'taxId' END,
        CASE WHEN v_first_transaction ? 'payee' AND v_first_transaction->'payee' ? 'nationalCouncilForPrescriptionDrugProgramsPharmacyNumber' 
             THEN v_first_transaction->'payee'->>'nationalCouncilForPrescriptionDrugProgramsPharmacyNumber' END,
        CASE WHEN v_first_transaction ? 'payee' AND v_first_transaction->'payee' ? 'remittanceDeliveryMethod' AND 
                  v_first_transaction->'payee'->'remittanceDeliveryMethod' ? 'name' 
             THEN v_first_transaction->'payee'->'remittanceDeliveryMethod'->>'name' END,
        CASE WHEN v_first_transaction ? 'payee' AND v_first_transaction->'payee' ? 'remittanceDeliveryMethod' AND 
                  v_first_transaction->'payee'->'remittanceDeliveryMethod' ? 'email' 
             THEN v_first_transaction->'payee'->'remittanceDeliveryMethod'->>'email' END,
        CASE WHEN v_first_transaction ? 'payee' AND v_first_transaction->'payee' ? 'remittanceDeliveryMethod' AND 
                  v_first_transaction->'payee'->'remittanceDeliveryMethod' ? 'ftp' 
             THEN v_first_transaction->'payee'->'remittanceDeliveryMethod'->>'ftp' END,
        CASE WHEN v_first_transaction ? 'payee' AND v_first_transaction->'payee' ? 'remittanceDeliveryMethod' AND 
                  v_first_transaction->'payee'->'remittanceDeliveryMethod' ? 'onLine' 
             THEN v_first_transaction->'payee'->'remittanceDeliveryMethod'->>'onLine' END,
        0.00, -- total_payment_amount_processed (will be updated)
        0, -- claims_processed (will be updated)
        p_response_json::TEXT,
        v_raw_report_url,
        v_s3_filehash,
        1, -- created_by
        CURRENT_TIMESTAMP,
        FALSE, -- archived
        FALSE -- deleted
    ) RETURNING id INTO v_batch_record_id;

    RETURN v_batch_record_id;
END;
$$ LANGUAGE plpgsql;

-- Helper function to process batch-level contacts
CREATE OR REPLACE FUNCTION process_835_batch_contacts(
    p_batch_record_id INTEGER,
    p_payer_info JSONB
) RETURNS VOID AS $$
DECLARE
    v_i INTEGER;
    v_contact JSONB;
    v_subform_id INTEGER;
BEGIN
    -- Process batch-level payer business contacts
    IF p_payer_info ? 'businessContactInformation' AND 
       p_payer_info->'businessContactInformation' ? 'contactMethods' AND
       jsonb_typeof(p_payer_info->'businessContactInformation'->'contactMethods') = 'array' AND
       jsonb_array_length(p_payer_info->'businessContactInformation'->'contactMethods') > 0 THEN
        FOR v_i IN 0..jsonb_array_length(p_payer_info->'businessContactInformation'->'contactMethods') - 1 LOOP
            v_contact := p_payer_info->'businessContactInformation'->'contactMethods'->v_i;
            
            INSERT INTO form_med_claim_resp_835_pcnt (
                email,
                fax,
                phone,
                phone_extension,
                created_by,
                created_on,
                archived,
                deleted
            ) VALUES (
                CASE WHEN v_contact ? 'email' THEN v_contact->>'email' END,
                CASE WHEN v_contact ? 'fax' THEN v_contact->>'fax' END,
                CASE WHEN v_contact ? 'phone' THEN v_contact->>'phone' END,
                CASE WHEN v_contact ? 'phoneExtension' THEN v_contact->>'phoneExtension' END,
                1,
                CURRENT_TIMESTAMP,
                FALSE,
                FALSE
            ) RETURNING id INTO v_subform_id;
            
            -- Link to batch record
            INSERT INTO sf_form_med_claim_resp_835_batch_to_med_claim_resp_835_pcnt (
                form_med_claim_resp_835_batch_fk,
                form_med_claim_resp_835_pcnt_fk,
                archive,
                delete
            ) VALUES (
                p_batch_record_id,
                v_subform_id,
                FALSE,
                FALSE
            );
        END LOOP;
    END IF;
    
    -- Process batch-level technical contacts
    IF p_payer_info ? 'technicalContactInformation' AND 
       jsonb_typeof(p_payer_info->'technicalContactInformation') = 'array' AND
       jsonb_array_length(p_payer_info->'technicalContactInformation') > 0 THEN
        FOR v_i IN 0..jsonb_array_length(p_payer_info->'technicalContactInformation') - 1 LOOP
            DECLARE
                v_tech_contact JSONB;
                v_j INTEGER;
            BEGIN
                v_tech_contact := p_payer_info->'technicalContactInformation'->v_i;
                
                -- Process contact methods within this technical contact
                IF v_tech_contact ? 'contactMethods' AND 
                   jsonb_typeof(v_tech_contact->'contactMethods') = 'array' AND
                   jsonb_array_length(v_tech_contact->'contactMethods') > 0 THEN
                    FOR v_j IN 0..jsonb_array_length(v_tech_contact->'contactMethods') - 1 LOOP
                        v_contact := v_tech_contact->'contactMethods'->v_j;
                        
                        INSERT INTO form_med_claim_resp_835_btcm (
                            url,
                            email,
                            fax,
                            phone,
                            phone_extension,
                            created_by,
                            created_on,
                            archived,
                            deleted
                        ) VALUES (
                            CASE WHEN v_contact ? 'url' THEN v_contact->>'url' END,
                            CASE WHEN v_contact ? 'email' THEN v_contact->>'email' END,
                            CASE WHEN v_contact ? 'fax' THEN v_contact->>'fax' END,
                            CASE WHEN v_contact ? 'phone' THEN v_contact->>'phone' END,
                            CASE WHEN v_contact ? 'phoneExtension' THEN v_contact->>'phoneExtension' END,
                            1,
                            CURRENT_TIMESTAMP,
                            FALSE,
                            FALSE
                        ) RETURNING id INTO v_subform_id;
                        
                        -- Link to batch record
                        INSERT INTO sf_form_med_claim_resp_835_batch_to_med_claim_resp_835_btcm (
                            form_med_claim_resp_835_batch_fk,
                            form_med_claim_resp_835_btcm_fk,
                            archive,
                            delete
                        ) VALUES (
                            p_batch_record_id,
                            v_subform_id,
                            FALSE,
                            FALSE
                        );
                    END LOOP;
                END IF;
            END;
        END LOOP;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Helper function to process provider payment summaries
CREATE OR REPLACE FUNCTION process_835_provider_payment_summary(
    p_batch_record_id INTEGER,
    p_response_json JSONB
) RETURNS VOID AS $$
DECLARE
    v_i INTEGER;
    v_j INTEGER;
    v_transaction JSONB;
    v_detail_info JSONB;
    v_subform_id INTEGER;
    v_provider_summary JSONB;
    v_provider_supplemental JSONB;
    v_assigned_number TEXT;
    v_summary_provider_identifier TEXT;
    v_facility_type_code TEXT;
    v_summary_fiscal_period_date DATE;
    v_total_claim_count INTEGER;
    v_total_claim_charge_amount NUMERIC;
    v_total_hcpcs_reported_charge_amount NUMERIC;
    v_total_hcpcs_payable_amount NUMERIC;
    v_total_patient_reimbursement_amount NUMERIC;
BEGIN
    -- Process batch-level provider payment summary information
    IF p_response_json ? 'transactions' AND 
       jsonb_typeof(p_response_json->'transactions') = 'array' AND
       jsonb_array_length(p_response_json->'transactions') > 0 THEN
        FOR v_i IN 0..jsonb_array_length(p_response_json->'transactions') - 1 LOOP
            v_transaction := p_response_json->'transactions'->v_i;
            
            IF v_transaction ? 'detailInfo' AND 
               jsonb_typeof(v_transaction->'detailInfo') = 'array' AND
               jsonb_array_length(v_transaction->'detailInfo') > 0 THEN
                FOR v_j IN 0..jsonb_array_length(v_transaction->'detailInfo') - 1 LOOP
                    v_detail_info := v_transaction->'detailInfo'->v_j;
                    
                    -- Check if this detailInfo has provider summary information
                    IF v_detail_info ? 'providerSummaryInformation' OR 
                       v_detail_info ? 'providerSupplementalSummaryInformation' THEN
                        v_provider_summary := v_detail_info->'providerSummaryInformation';
                        v_provider_supplemental := v_detail_info->'providerSupplementalSummaryInformation';
                        
                        v_assigned_number := v_detail_info->>'assignedNumber';
                        v_summary_provider_identifier := v_provider_summary->>'providerIdentifier';
                        v_facility_type_code := v_provider_supplemental->>'facilityTypeCode';
                        v_summary_fiscal_period_date := safe_to_date(v_provider_supplemental->>'fiscalPeriodDate');
                        v_total_claim_count := safe_to_numeric(v_provider_supplemental->>'totalClaimCount')::INTEGER;
                        v_total_claim_charge_amount := safe_to_numeric(v_provider_supplemental->>'totalClaimChargeAmount');
                        v_total_hcpcs_reported_charge_amount := safe_to_numeric(v_provider_supplemental->>'totalHCPCSReportedChargeAmount');
                        v_total_hcpcs_payable_amount := safe_to_numeric(v_provider_supplemental->>'totalHCPCSPayableAmount');
                        v_total_patient_reimbursement_amount := safe_to_numeric(v_provider_supplemental->>'totalPatientReimbursementAmount');
                        
                        INSERT INTO form_med_claim_resp_835_bpmt (
                            assigned_number,
                            provider_identifier,
                            facility_type_code,
                            fiscal_period_date,
                            total_claim_count,
                            total_claim_charge_amount,
                            total_hcpcs_reported_charge_amount,
                            total_hcpcs_payable_amount,
                            total_patient_reimbursement_amount,
                            created_by,
                            created_on,
                            archived,
                            deleted
                        ) VALUES (
                            v_assigned_number,
                            v_summary_provider_identifier,
                            v_facility_type_code,
                            v_summary_fiscal_period_date,
                            v_total_claim_count,
                            v_total_claim_charge_amount,
                            v_total_hcpcs_reported_charge_amount,
                            v_total_hcpcs_payable_amount,
                            v_total_patient_reimbursement_amount,
                            1,
                            CURRENT_TIMESTAMP,
                            FALSE,
                            FALSE
                        ) RETURNING id INTO v_subform_id;
                        
                        -- Link to batch record
                        INSERT INTO sf_form_med_claim_resp_835_batch_to_med_claim_resp_835_bpmt (
                            form_med_claim_resp_835_batch_fk,
                            form_med_claim_resp_835_bpmt_fk,
                            archive,
                            delete
                        ) VALUES (
                            p_batch_record_id,
                            v_subform_id,
                            FALSE,
                            FALSE
                        );
                    END IF;
                END LOOP;
            END IF;
        END LOOP;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Helper function to process claim adjustments
CREATE OR REPLACE FUNCTION process_835_claim_adjustments(
    p_main_record_id INTEGER,
    p_payment_info JSONB
) RETURNS VOID AS $$
DECLARE
    v_l INTEGER;
    v_adjustment JSONB;
    v_subform_id INTEGER;
BEGIN
    IF p_payment_info ? 'claimAdjustments' AND 
       jsonb_typeof(p_payment_info->'claimAdjustments') = 'array' AND
       jsonb_array_length(p_payment_info->'claimAdjustments') > 0 THEN
        FOR v_l IN 0..jsonb_array_length(p_payment_info->'claimAdjustments') - 1 LOOP
            v_adjustment := p_payment_info->'claimAdjustments'->v_l;
            
            INSERT INTO form_med_claim_resp_835_adj (
                claim_adjustment_group_code,
                claim_adjustment_group_code_value,
                adjustment_reason_code,
                adjustment_amount,
                adjustment_quantity,
                created_by,
                created_on,
                archived,
                deleted
            ) VALUES (
                CASE WHEN v_adjustment ? 'claimAdjustmentGroupCode' THEN v_adjustment->>'claimAdjustmentGroupCode' END,
                CASE WHEN v_adjustment ? 'claimAdjustmentGroupCodeValue' THEN v_adjustment->>'claimAdjustmentGroupCodeValue' END,
                CASE WHEN v_adjustment ? 'adjustmentReasonCode' THEN v_adjustment->>'adjustmentReasonCode' END,
                CASE WHEN v_adjustment ? 'adjustmentAmount' 
                     THEN safe_to_numeric(v_adjustment->>'adjustmentAmount') END,
                CASE WHEN v_adjustment ? 'adjustmentQuantity' 
                     THEN safe_to_numeric(v_adjustment->>'adjustmentQuantity') END,
                1,
                CURRENT_TIMESTAMP,
                FALSE,
                FALSE
            ) RETURNING id INTO v_subform_id;
            
            -- Link to main record
            INSERT INTO sf_form_med_claim_resp_835_to_med_claim_resp_835_adj (
                form_med_claim_resp_835_fk,
                form_med_claim_resp_835_adj_fk,
                archive,
                delete
            ) VALUES (
                p_main_record_id,
                v_subform_id,
                FALSE,
                FALSE
            );
        END LOOP;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Helper function to process service lines
CREATE OR REPLACE FUNCTION process_835_service_lines(
    p_main_record_id INTEGER,
    p_payment_info JSONB
) RETURNS VOID AS $$
DECLARE
    v_l INTEGER;
    v_service_line JSONB;
    v_service_line_id INTEGER;
    v_service_payment_info JSONB;
    v_service_identification JSONB;
    v_rendering_provider_info JSONB;
    v_service_supplemental_amounts JSONB;
    v_rmk_codes TEXT[];
    v_rmk_code TEXT;
    v_rmk_idx INTEGER;
BEGIN
    IF p_payment_info ? 'serviceLines' AND 
       jsonb_typeof(p_payment_info->'serviceLines') = 'array' AND
       jsonb_array_length(p_payment_info->'serviceLines') > 0 THEN
        FOR v_l IN 0..jsonb_array_length(p_payment_info->'serviceLines') - 1 LOOP
            v_service_line := p_payment_info->'serviceLines'->v_l;
            
            -- Extract nested sections
            v_service_payment_info := CASE 
                WHEN v_service_line ? 'servicePaymentInformation' 
                THEN v_service_line->'servicePaymentInformation'
                ELSE '{}'::jsonb
            END;
            
            v_service_identification := CASE 
                WHEN v_service_line ? 'serviceIdentification' 
                THEN v_service_line->'serviceIdentification'
                ELSE '{}'::jsonb
            END;
            
            v_rendering_provider_info := CASE 
                WHEN v_service_line ? 'renderingProviderInformation' 
                THEN v_service_line->'renderingProviderInformation'
                ELSE '{}'::jsonb
            END;
            
            v_service_supplemental_amounts := CASE 
                WHEN v_service_line ? 'serviceSupplementalAmounts' 
                THEN v_service_line->'serviceSupplementalAmounts'
                ELSE '{}'::jsonb
            END;
            
            INSERT INTO form_med_claim_resp_835_sl (
                line_item_control_number,
                service_date,
                service_start_date,
                service_end_date,
                product_or_service_id_qualifier,
                product_or_service_id_qualifier_value,
                adjudicated_procedure_code,
                adjudicated_procedure_modifier_1,
                adjudicated_procedure_modifier_2,
                adjudicated_procedure_modifier_3,
                adjudicated_procedure_modifier_4,
                line_item_charge_amount,
                line_item_provider_payment_amount,
                revenue_code,
                units_of_service_paid_count,
                original_units_of_service_count,
                submitted_procedure_code_description,
                submitted_procedure_code,
                authorization_number,
                prior_authorization_number,
                location_number,
                pre_determination_number,
                rendering_provider_npi,
                rendering_provider_ncpdp,
                allowed_actual,
                deduction_amount,
                created_by,
                created_on,
                archived,
                deleted
            ) VALUES (
                CASE WHEN v_service_line ? 'lineItemControlNumber' THEN v_service_line->>'lineItemControlNumber' END,
                CASE WHEN v_service_line ? 'serviceDate' 
                     THEN safe_to_date(v_service_line->>'serviceDate') END,
                CASE WHEN v_service_line ? 'serviceStartDate' 
                     THEN safe_to_date(v_service_line->>'serviceStartDate') END,
                CASE WHEN v_service_line ? 'serviceEndDate' 
                     THEN safe_to_date(v_service_line->>'serviceEndDate') END,
                CASE WHEN v_service_payment_info ? 'productOrServiceIDQualifier' 
                     THEN v_service_payment_info->>'productOrServiceIDQualifier' END,
                CASE WHEN v_service_payment_info ? 'productOrServiceIDQualifierValue' 
                     THEN v_service_payment_info->>'productOrServiceIDQualifierValue' END,
                CASE WHEN v_service_payment_info ? 'adjudicatedProcedureCode' 
                     THEN v_service_payment_info->>'adjudicatedProcedureCode' END,
                CASE WHEN v_service_payment_info ? 'adjudicatedProcedureModifierCodes' AND 
                          jsonb_typeof(v_service_payment_info->'adjudicatedProcedureModifierCodes') = 'array' AND
                          jsonb_array_length(v_service_payment_info->'adjudicatedProcedureModifierCodes') >= 1
                     THEN v_service_payment_info->'adjudicatedProcedureModifierCodes'->>0 END,
                CASE WHEN v_service_payment_info ? 'adjudicatedProcedureModifierCodes' AND 
                          jsonb_typeof(v_service_payment_info->'adjudicatedProcedureModifierCodes') = 'array' AND
                          jsonb_array_length(v_service_payment_info->'adjudicatedProcedureModifierCodes') >= 2
                     THEN v_service_payment_info->'adjudicatedProcedureModifierCodes'->>1 END,
                CASE WHEN v_service_payment_info ? 'adjudicatedProcedureModifierCodes' AND 
                          jsonb_typeof(v_service_payment_info->'adjudicatedProcedureModifierCodes') = 'array' AND
                          jsonb_array_length(v_service_payment_info->'adjudicatedProcedureModifierCodes') >= 3
                     THEN v_service_payment_info->'adjudicatedProcedureModifierCodes'->>2 END,
                CASE WHEN v_service_payment_info ? 'adjudicatedProcedureModifierCodes' AND 
                          jsonb_typeof(v_service_payment_info->'adjudicatedProcedureModifierCodes') = 'array' AND
                          jsonb_array_length(v_service_payment_info->'adjudicatedProcedureModifierCodes') >= 4
                     THEN v_service_payment_info->'adjudicatedProcedureModifierCodes'->>3 END,
                CASE WHEN v_service_payment_info ? 'lineItemChargeAmount' 
                     THEN safe_to_numeric(v_service_payment_info->>'lineItemChargeAmount') END,
                CASE WHEN v_service_payment_info ? 'lineItemProviderPaymentAmount' 
                     THEN safe_to_numeric(v_service_payment_info->>'lineItemProviderPaymentAmount') END,
                CASE WHEN v_service_payment_info ? 'nationalUniformBillingCommitteeRevenueCode' 
                     THEN v_service_payment_info->>'nationalUniformBillingCommitteeRevenueCode' END,
                CASE WHEN v_service_payment_info ? 'unitsOfServicePaidCount' 
                     THEN safe_to_numeric(v_service_payment_info->>'unitsOfServicePaidCount') END,
                CASE WHEN v_service_payment_info ? 'originalUnitsOfServiceCount' 
                     THEN safe_to_numeric(v_service_payment_info->>'originalUnitsOfServiceCount') END,
                CASE WHEN v_service_payment_info ? 'submittedProcedureCodeDescription' 
                     THEN v_service_payment_info->>'submittedProcedureCodeDescription' END,
                CASE WHEN v_service_payment_info ? 'submittedAdjudicatedProcedureCode' 
                     THEN v_service_payment_info->>'submittedAdjudicatedProcedureCode' END,
                CASE WHEN v_service_identification ? 'authorizationNumber' 
                     THEN v_service_identification->>'authorizationNumber' END,
                CASE WHEN v_service_identification ? 'priorAuthorizationNumber' 
                     THEN v_service_identification->>'priorAuthorizationNumber' END,
                CASE WHEN v_service_identification ? 'locationNumber' 
                     THEN v_service_identification->>'locationNumber' END,
                CASE WHEN v_service_identification ? 'preDeterminationOfBenefitsNumber' 
                     THEN v_service_identification->>'preDeterminationOfBenefitsNumber' END,
                CASE WHEN v_rendering_provider_info ? 'npi' 
                     THEN v_rendering_provider_info->>'npi' END,
                CASE WHEN v_rendering_provider_info ? 'nationalCouncilForPrescriptionDrugProgramPharmacyNumber' 
                     THEN v_rendering_provider_info->>'nationalCouncilForPrescriptionDrugProgramPharmacyNumber' END,
                CASE WHEN v_service_supplemental_amounts ? 'allowedActual' 
                     THEN safe_to_numeric(v_service_supplemental_amounts->>'allowedActual') END,
                CASE WHEN v_service_supplemental_amounts ? 'deductionAmount' 
                     THEN safe_to_numeric(v_service_supplemental_amounts->>'deductionAmount') END,
                1,
                CURRENT_TIMESTAMP,
                FALSE,
                FALSE
            ) RETURNING id INTO v_service_line_id;
            
            -- Link to main record
            INSERT INTO sf_form_med_claim_resp_835_to_med_claim_resp_835_sl (
                form_med_claim_resp_835_fk,
                form_med_claim_resp_835_sl_fk,
                archive,
                delete
            ) VALUES (
                p_main_record_id,
                v_service_line_id,
                FALSE,
                FALSE
            );
            
            -- Process health care remark codes (rmk_cd) - multi-field
            IF v_service_line ? 'healthCareCheckRemarkCodes' AND 
               jsonb_typeof(v_service_line->'healthCareCheckRemarkCodes') = 'array' AND
               jsonb_array_length(v_service_line->'healthCareCheckRemarkCodes') > 0 THEN
                v_rmk_codes := ARRAY[]::TEXT[];
                FOR v_rmk_idx IN 0..jsonb_array_length(v_service_line->'healthCareCheckRemarkCodes') - 1 LOOP
                    v_rmk_code := v_service_line->'healthCareCheckRemarkCodes'->>v_rmk_idx;
                    IF v_rmk_code IS NOT NULL AND v_rmk_code != '' THEN
                        -- Insert into gerund table for rmk_cd multi-field
                        INSERT INTO gr_form_med_claim_resp_835_sl_rmk_cd_to_list_med_claim_ecl_id (
                            form_med_claim_resp_835_sl_fk,
                            form_list_med_claim_ecl_fk
                        ) VALUES (
                            v_service_line_id,
                            v_rmk_code
                        );
                    END IF;
                END LOOP;
            END IF;
            
            -- Process service adjustments subform
            IF v_service_line ? 'serviceAdjustments' AND 
               jsonb_typeof(v_service_line->'serviceAdjustments') = 'array' AND
               jsonb_array_length(v_service_line->'serviceAdjustments') > 0 THEN
                DECLARE
                    v_adj_idx INTEGER;
                    v_adjustment JSONB;
                    v_adj_subform_id INTEGER;
                BEGIN
                    FOR v_adj_idx IN 0..jsonb_array_length(v_service_line->'serviceAdjustments') - 1 LOOP
                        v_adjustment := v_service_line->'serviceAdjustments'->v_adj_idx;
                        
                        INSERT INTO form_med_claim_resp_835_sl_adj (
                            claim_adjustment_group_code,
                            claim_adjustment_group_code_value,
                            adjustment_reason_code,
                            adjustment_amount,
                            adjustment_quantity,
                            created_by,
                            created_on,
                            archived,
                            deleted
                        ) VALUES (
                            CASE WHEN v_adjustment ? 'claimAdjustmentGroupCode' THEN v_adjustment->>'claimAdjustmentGroupCode' END,
                            CASE WHEN v_adjustment ? 'claimAdjustmentGroupCodeValue' THEN v_adjustment->>'claimAdjustmentGroupCodeValue' END,
                            CASE WHEN v_adjustment ? 'adjustmentReasonCode' THEN v_adjustment->>'adjustmentReasonCode' END,
                            CASE WHEN v_adjustment ? 'adjustmentAmount' 
                                 THEN safe_to_numeric(v_adjustment->>'adjustmentAmount') END,
                            CASE WHEN v_adjustment ? 'adjustmentQuantity' 
                                 THEN safe_to_numeric(v_adjustment->>'adjustmentQuantity') END,
                            1,
                            CURRENT_TIMESTAMP,
                            FALSE,
                            FALSE
                        ) RETURNING id INTO v_adj_subform_id;
                        
                        -- Link service adjustment to service line
                        INSERT INTO sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj (
                            form_med_claim_resp_835_sl_fk,
                            form_med_claim_resp_835_sl_adj_fk,
                            archive,
                            delete
                        ) VALUES (
                            v_service_line_id,
                            v_adj_subform_id,
                            FALSE,
                            FALSE
                        );
                    END LOOP;
                END;
            END IF;
        END LOOP;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Helper function to process individual claim
DROP FUNCTION IF EXISTS process_835_individual_claim(INTEGER, JSONB, INTEGER);
CREATE OR REPLACE FUNCTION process_835_individual_claim(
    p_response_id INTEGER,
    p_payment_info JSONB,
    p_batch_record_id INTEGER
) RETURNS TABLE(payer_id INTEGER, payment_amount NUMERIC, success_flag BOOLEAN) AS $$
DECLARE
    v_claim_info JSONB;
    v_claim_no TEXT;
    v_patient_id INTEGER;
    v_site_id INTEGER;
    v_payer_id INTEGER;
    v_status_code TEXT;
    v_claim_status TEXT;
    v_patient_control_number TEXT;
    v_payer_claim_control_number TEXT;
    v_main_record_id INTEGER;
    
    -- Medicare outpatient adjudication fields
    v_outpatient_adjudication JSONB;
    v_mcr_reimbursement_rate NUMERIC;
    v_mcr_hcpcs_payable_amount NUMERIC;
    v_rmk_codes TEXT[];
    v_rmk_code TEXT;
    v_rmk_idx INTEGER;
    
    -- Claim totals
    v_total_charge_amount NUMERIC;
    v_total_paid_amount NUMERIC;
    v_total_patient_responsibility_amount NUMERIC;
    v_total_claim_adjustment_amount NUMERIC;
    
    -- Result record
    
    v_adjustment JSONB;
    v_l INTEGER;
    v_claim_info_id INTEGER;
BEGIN
    -- Extract claim identifiers
    IF p_payment_info ? 'claimPaymentInfo' THEN
        v_claim_info := p_payment_info->'claimPaymentInfo';
        
        IF v_claim_info ? 'payerClaimControlNumber' THEN
            v_payer_claim_control_number := v_claim_info->>'payerClaimControlNumber';
        END IF;
        
        IF v_claim_info ? 'patientControlNumber' THEN
            v_patient_control_number := v_claim_info->>'patientControlNumber';
        END IF;
    END IF;
    
    -- Find the original claim
    SELECT mci.id INTO v_claim_info_id
    FROM form_med_claim_info mci
    LEFT JOIN form_med_claim_supplemental mcs ON mci.id::TEXT = mcs.claim_number
    WHERE (
        (v_patient_control_number IS NOT NULL AND mci.patient_control_number = v_patient_control_number) OR
        (v_payer_claim_control_number IS NOT NULL AND mcs.claim_control_number = v_payer_claim_control_number)
    )
    AND COALESCE(mci.deleted, FALSE) = FALSE
    AND COALESCE(mci.archived, FALSE) = FALSE
    AND COALESCE(mcs.deleted, FALSE) = FALSE
    AND COALESCE(mcs.archived, FALSE) = FALSE
    ORDER BY mci.created_on DESC
    LIMIT 1;
    
    -- Get the actual claim_no (UUID) from the claim_info_id
    IF v_claim_info_id IS NOT NULL THEN
        SELECT mc.claim_no INTO v_claim_no
        FROM form_med_claim mc
        WHERE mc.id = v_claim_info_id
        AND COALESCE(mc.deleted, FALSE) = FALSE
        AND COALESCE(mc.archived, FALSE) = FALSE;
    END IF;
    
    IF v_claim_no IS NULL THEN
        -- Log error with more detail
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'No matching claim found for patient_control_number: ' || COALESCE(v_patient_control_number, 'NULL') || 
            ', payer_claim_control_number: ' || COALESCE(v_payer_claim_control_number, 'NULL'),
            'Claim lookup failed in process_835_individual_claim',
            'DATA_NOT_FOUND',
            current_schema(),
            'form_med_claim_info',
            jsonb_build_object(
                'patient_control_number', v_patient_control_number,
                'payer_claim_control_number', v_payer_claim_control_number,
                'response_id', p_response_id,
                'batch_record_id', p_batch_record_id
            )
        );
        
        RETURN QUERY SELECT NULL::INTEGER, NULL::NUMERIC, FALSE;
        RETURN;
    END IF;

    -- Get patient, site, and payer from the claim
    SELECT mc.patient_id, mc.site_id, mc.payer_id 
    INTO v_patient_id, v_site_id, v_payer_id
    FROM form_med_claim mc
    WHERE mc.claim_no = v_claim_no
    AND COALESCE(mc.deleted, FALSE) = FALSE
    AND COALESCE(mc.archived, FALSE) = FALSE;
    v_status_code := CASE WHEN v_claim_info ? 'claimStatusCode' THEN v_claim_info->>'claimStatusCode' ELSE NULL::TEXT END;

    -- Extract Medicare outpatient adjudication information
    v_mcr_reimbursement_rate := NULL;
    v_mcr_hcpcs_payable_amount := NULL;
    v_rmk_codes := ARRAY[]::TEXT[];
    
    IF p_payment_info ? 'outpatientAdjudication' THEN
        v_outpatient_adjudication := p_payment_info->'outpatientAdjudication';
        
        -- Extract reimbursement rate (MOA01)
        IF v_outpatient_adjudication ? 'reimbursementRate' THEN
            v_mcr_reimbursement_rate := safe_to_numeric(v_outpatient_adjudication->>'reimbursementRate');
        END IF;
        
        -- Extract HCPCS payable amount (MOA02)
        IF v_outpatient_adjudication ? 'claimHCPCSPayableAmount' THEN
            v_mcr_hcpcs_payable_amount := safe_to_numeric(v_outpatient_adjudication->>'claimHCPCSPayableAmount');
        END IF;
        
        -- Extract remark codes (MOA03-MOA07)
        FOR v_rmk_idx IN 1..5 LOOP
            v_rmk_code := NULL;
            
            IF v_outpatient_adjudication ? ('claimPaymentRemarkCode' || v_rmk_idx) THEN
                v_rmk_code := v_outpatient_adjudication->>('claimPaymentRemarkCode' || v_rmk_idx);
                
                IF v_rmk_code IS NOT NULL AND v_rmk_code != '' THEN
                    v_rmk_codes := array_append(v_rmk_codes, v_rmk_code);
                END IF;
            END IF;
        END LOOP;
    END IF;
    
    -- Calculate claim totals
    v_total_paid_amount := 0.00;
    v_total_patient_responsibility_amount := 0.00;
    v_total_claim_adjustment_amount := 0.00;
    
    IF p_payment_info ? 'claimPaymentInfo' THEN
        v_claim_info := p_payment_info->'claimPaymentInfo';
        
        -- Get total claim charge amount
        IF v_claim_info ? 'totalClaimChargeAmount' THEN
            v_total_charge_amount := safe_to_numeric(v_claim_info->>'totalClaimChargeAmount');
            IF v_total_charge_amount IS NULL THEN
                v_total_charge_amount := 0.00;
            END IF;
        END IF;
        
        -- Get total claim payment amount
        IF v_claim_info ? 'claimPaymentAmount' THEN
            v_total_paid_amount := safe_to_numeric(v_claim_info->>'claimPaymentAmount');
            IF v_total_paid_amount IS NULL THEN
                v_total_paid_amount := 0.00;
            END IF;
        END IF;
        
        -- Get patient responsibility amount
        IF v_claim_info ? 'patientResponsibilityAmount' THEN
            v_total_patient_responsibility_amount := safe_to_numeric(v_claim_info->>'patientResponsibilityAmount');
            IF v_total_patient_responsibility_amount IS NULL THEN
                v_total_patient_responsibility_amount := 0.00;
            END IF;
        END IF;
        
        -- Calculate total adjustments
        IF p_payment_info ? 'claimAdjustments' AND 
           jsonb_typeof(p_payment_info->'claimAdjustments') = 'array' THEN
            FOR v_l IN 0..jsonb_array_length(p_payment_info->'claimAdjustments') - 1 LOOP
                v_adjustment := p_payment_info->'claimAdjustments'->v_l;
                IF v_adjustment ? 'adjustmentAmount' THEN
                    v_total_claim_adjustment_amount := v_total_claim_adjustment_amount + 
                        COALESCE(safe_to_numeric(v_adjustment->>'adjustmentAmount'), 0.00);
                END IF;
            END LOOP;
        END IF;
    END IF;
    
    -- Insert main claim record
    INSERT INTO form_med_claim_resp_835 (
        claim_no,
        patient_id,
        site_id,
        payer_id,
        response_id,
        patient_control_number,
        claim_status_code,
        claim_statement_period_start,
        claim_statement_period_end,
        coverage_expiration_date,
        claim_received_date,
        group_number,
        authorization_number,
        prior_authorization_number,
        patient_last_name,
        patient_first_name,
        patient_middle_name,
        patient_suffix,
        patient_member_id,
        subscriber_last_name,
        subscriber_first_name,
        subscriber_member_id,
        rendering_provider_name,
        rendering_provider_npi,
        claim_contact_name,
        total_charge_amount,
        total_paid_amount,
        total_adjusted_amount,
        total_pt_pay,
        mcr_reimbursement_rate,
        mcr_hcpcs_payable_amount,
        reviewed,
        reviewed_by,
        notes,
        created_by,
        created_on,
        archived,
        deleted
    ) VALUES (
        v_claim_no,
        v_patient_id,
        v_site_id,
        v_payer_id,
        p_response_id,
        v_patient_control_number,
        v_status_code,
        CASE WHEN p_payment_info ? 'claimStatementPeriodStart' 
             THEN safe_to_date(p_payment_info->>'claimStatementPeriodStart') END,
        CASE WHEN p_payment_info ? 'claimStatementPeriodEnd' 
             THEN safe_to_date(p_payment_info->>'claimStatementPeriodEnd') END,
        CASE WHEN p_payment_info ? 'coverageExpirationDate' 
             THEN safe_to_date(p_payment_info->>'coverageExpirationDate') END,
        CASE WHEN p_payment_info ? 'claimReceivedDate' 
             THEN safe_to_date(p_payment_info->>'claimReceivedDate') END,
        CASE WHEN p_payment_info ? 'otherClaimRelatedIdentification' AND 
                  p_payment_info->'otherClaimRelatedIdentification' ? 'groupNumber' 
             THEN p_payment_info->'otherClaimRelatedIdentification'->>'groupNumber' END,
        CASE WHEN p_payment_info ? 'otherClaimRelatedIdentification' AND 
                  p_payment_info->'otherClaimRelatedIdentification' ? 'authorizationNumber' 
             THEN p_payment_info->'otherClaimRelatedIdentification'->>'authorizationNumber' END,
        CASE WHEN p_payment_info ? 'otherClaimRelatedIdentification' AND 
                  p_payment_info->'otherClaimRelatedIdentification' ? 'priorAuthorizationNumber' 
             THEN p_payment_info->'otherClaimRelatedIdentification'->>'priorAuthorizationNumber' END,
        CASE WHEN p_payment_info ? 'patientName' AND p_payment_info->'patientName' ? 'lastName' 
             THEN p_payment_info->'patientName'->>'lastName' END,
        CASE WHEN p_payment_info ? 'patientName' AND p_payment_info->'patientName' ? 'firstName' 
             THEN p_payment_info->'patientName'->>'firstName' END,
        CASE WHEN p_payment_info ? 'patientName' AND p_payment_info->'patientName' ? 'middleName' 
             THEN p_payment_info->'patientName'->>'middleName' END,
        CASE WHEN p_payment_info ? 'patientName' AND p_payment_info->'patientName' ? 'suffix' 
             THEN p_payment_info->'patientName'->>'suffix' END,
        CASE WHEN p_payment_info ? 'patientName' AND p_payment_info->'patientName' ? 'memberId' 
             THEN p_payment_info->'patientName'->>'memberId' END,
        CASE WHEN p_payment_info ? 'subscriber' AND p_payment_info->'subscriber' ? 'lastName' 
             THEN p_payment_info->'subscriber'->>'lastName' END,
        CASE WHEN p_payment_info ? 'subscriber' AND p_payment_info->'subscriber' ? 'firstName' 
             THEN p_payment_info->'subscriber'->>'firstName' END,
        CASE WHEN p_payment_info ? 'subscriber' AND p_payment_info->'subscriber' ? 'memberId' 
             THEN p_payment_info->'subscriber'->>'memberId' END,
        CASE WHEN p_payment_info ? 'renderingProvider' AND p_payment_info->'renderingProvider' ? 'organizationName' 
             THEN p_payment_info->'renderingProvider'->>'organizationName' END,
        CASE WHEN p_payment_info ? 'renderingProvider' AND p_payment_info->'renderingProvider' ? 'npi' 
             THEN p_payment_info->'renderingProvider'->>'npi' END,
        CASE WHEN p_payment_info ? 'claimContactInformation' AND p_payment_info->'claimContactInformation' ? 'contactName' 
             THEN p_payment_info->'claimContactInformation'->>'contactName' END,
        v_total_charge_amount,
        v_total_paid_amount,
        v_total_claim_adjustment_amount,
        v_total_patient_responsibility_amount,
        v_mcr_reimbursement_rate,
        v_mcr_hcpcs_payable_amount,
        FALSE, -- reviewed
        NULL, -- reviewed_by
        NULL, -- notes
        1, -- created_by
        CURRENT_TIMESTAMP,
        FALSE, -- archived
        FALSE -- deleted
    ) RETURNING id INTO v_main_record_id;
    
    v_claim_status := map_835_claim_status(v_status_code);

    -- Only log error if status is NULL (unrecognized status code)
    -- 'Error' is a valid status for denied claims (status code 4)
    IF v_claim_status IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'No final status found for claim ' || v_claim_no || ' with status code ' || v_status_code || ' and response id ' || p_response_id,
            'Exception in process_835_individual_claim',
            'FUNCTION',
            current_schema(),
            'form_med_claim_resp_835',
            jsonb_build_object(
                'claim_no', v_claim_no,
                'status_code', v_status_code,
                'response_id', p_response_id
            )
        );
        RAISE WARNING 'No final status found for claim % with status code % and response id %', v_claim_no, v_status_code, p_response_id;
    END IF;

    -- Update the medical claim status
    UPDATE form_med_claim
    SET status = v_claim_status,
        paid = COALESCE(v_total_paid_amount::NUMERIC, 0::NUMERIC),
        copay = COALESCE(v_total_patient_responsibility_amount::NUMERIC, 0::NUMERIC),
        updated_on = CURRENT_TIMESTAMP
    WHERE claim_no = v_claim_no
    AND COALESCE(void, 'No') = 'No'
    AND archived IS NOT TRUE
    AND deleted IS NOT TRUE;
    
    UPDATE form_billing_invoice inv
    SET total_insurance_paid = COALESCE(v_total_paid_amount::NUMERIC, 0::NUMERIC),
        total_pt_pay = COALESCE(v_total_patient_responsibility_amount::NUMERIC, 0::NUMERIC)
    FROM form_med_claim claim
    WHERE claim.claim_no = v_claim_no 
    AND claim.invoice_no = inv.invoice_no
    AND COALESCE(inv.void, 'No') = 'No'
    AND COALESCE(inv.zeroed, 'No') = 'No'
    AND inv.archived IS NOT TRUE
    AND inv.deleted IS NOT TRUE
    AND claim.deleted IS NOT TRUE
    AND claim.archived IS NOT TRUE;

    -- Insert remark codes
    IF v_rmk_codes IS NOT NULL AND array_length(v_rmk_codes, 1) > 0 THEN
        FOR v_rmk_idx IN 1..array_length(v_rmk_codes, 1) LOOP
            v_rmk_code := v_rmk_codes[v_rmk_idx];
            
            INSERT INTO gr_form_med_claim_resp_835_rmk_cd_to_list_med_claim_ecl_id (
                form_med_claim_resp_835_fk,
                form_list_med_claim_ecl_fk
            ) VALUES (
                v_main_record_id,
                v_rmk_code
            );
        END LOOP;
    END IF;
    
    -- Process adjustments
    PERFORM process_835_claim_adjustments(v_main_record_id, p_payment_info);
    
    -- Process service lines
    PERFORM process_835_service_lines(v_main_record_id, p_payment_info);
    
    -- Return the record with claim ID, payment amount, and success flag
    RETURN QUERY SELECT v_payer_id, v_total_paid_amount, TRUE;
    RETURN;
END;
$$ LANGUAGE plpgsql;

-- Main parse_835_response function (simplified)
CREATE OR REPLACE FUNCTION parse_835_response(
    p_response_id INTEGER,
    p_response_json JSONB,
    p_created_by INTEGER,
    p_created_dt TIMESTAMP
) RETURNS VOID AS $$
DECLARE
    v_batch_record_id INTEGER;
    v_transaction JSONB;
    v_detail_info JSONB;
    v_payment_info JSONB;
    v_first_transaction JSONB;
    v_payer_info JSONB;
    
    v_i INTEGER;
    v_j INTEGER;
    v_k INTEGER;
    
    v_claims_processed INTEGER := 0;
    v_total_payment_amount_processed NUMERIC := 0.00;
    v_batch_payer_id INTEGER;
    
    v_result_payer_id INTEGER;
    v_result_payment_amount NUMERIC;
    v_result_success_flag BOOLEAN;
    
    -- Error handling
    v_error_message TEXT;
    v_error_detail TEXT;
    v_params JSONB;
BEGIN
    BEGIN
        -- Validate input JSON
        IF p_response_json IS NULL OR jsonb_typeof(p_response_json) != 'object' THEN
            RAISE EXCEPTION 'Invalid or empty response JSON for 835 response';
        END IF;
        
        RAISE LOG 'Starting parse_835_response for response_id: %', p_response_id;
        
        -- Build parameters for logging
        v_params := jsonb_build_object(
            'response_id', p_response_id,
            'response_type', '835'
        );
        
        -- Process batch information
        v_batch_record_id := process_835_batch_info(p_response_id, p_response_json);
        RAISE LOG 'Created batch record with id: %', v_batch_record_id;
        
        -- Get first transaction and payer info for contacts
        v_first_transaction := CASE 
            WHEN p_response_json ? 'transactions' AND 
                 jsonb_array_length(p_response_json->'transactions') > 0 
            THEN p_response_json->'transactions'->0
            ELSE '{}'::jsonb
        END;
        
        v_payer_info := CASE 
            WHEN v_first_transaction ? 'payer' 
            THEN v_first_transaction->'payer'
            ELSE '{}'::jsonb
        END;
        
        -- Process batch contacts
        PERFORM process_835_batch_contacts(v_batch_record_id, v_payer_info);
        
        -- Process provider payment summaries
        PERFORM process_835_provider_payment_summary(v_batch_record_id, p_response_json);
        
        -- Process transactions (individual claims)
        IF p_response_json ? 'transactions' AND 
           jsonb_typeof(p_response_json->'transactions') = 'array' AND
           jsonb_array_length(p_response_json->'transactions') > 0 THEN
            RAISE LOG '835 response contains % transactions', jsonb_array_length(p_response_json->'transactions');
            FOR v_i IN 0..jsonb_array_length(p_response_json->'transactions') - 1 LOOP
                v_transaction := p_response_json->'transactions'->v_i;
                RAISE LOG 'Processing transaction % of %', v_i+1, jsonb_array_length(p_response_json->'transactions');
                
                -- Process detail info
                IF v_transaction ? 'detailInfo' AND 
                   jsonb_typeof(v_transaction->'detailInfo') = 'array' AND
                   jsonb_array_length(v_transaction->'detailInfo') > 0 THEN
                    RAISE LOG 'Transaction % has % detailInfo records', v_i+1, jsonb_array_length(v_transaction->'detailInfo');
                    FOR v_j IN 0..jsonb_array_length(v_transaction->'detailInfo') - 1 LOOP
                        v_detail_info := v_transaction->'detailInfo'->v_j;
                        
                        -- Process payment info
                        IF v_detail_info ? 'paymentInfo' AND 
                           jsonb_typeof(v_detail_info->'paymentInfo') = 'array' AND
                           jsonb_array_length(v_detail_info->'paymentInfo') > 0 THEN
                            RAISE LOG 'DetailInfo % has % paymentInfo records', v_j+1, jsonb_array_length(v_detail_info->'paymentInfo');
                            FOR v_k IN 0..jsonb_array_length(v_detail_info->'paymentInfo') - 1 LOOP
                                v_payment_info := v_detail_info->'paymentInfo'->v_k;
                                
                                -- Process individual claim
                                SELECT payer_id, payment_amount, success_flag
                                INTO v_result_payer_id, v_result_payment_amount, v_result_success_flag
                                FROM process_835_individual_claim(
                                    p_response_id,
                                    v_payment_info,
                                    v_batch_record_id
                                );
                                
                                -- Check if claim was processed successfully
                                IF v_result_success_flag THEN
                                    v_claims_processed := v_claims_processed + 1;
                                    v_total_payment_amount_processed := v_total_payment_amount_processed + 
                                        COALESCE(v_result_payment_amount, 0.00);
                                    
                                    -- Update batch payer_id from first claim if not set
                                    IF v_batch_payer_id IS NULL AND v_result_payer_id IS NOT NULL AND v_claims_processed = 1 THEN
                                        UPDATE form_med_claim_resp_835_batch
                                        SET payer_id = v_result_payer_id
                                        WHERE id = v_batch_record_id;
                                        v_batch_payer_id := v_result_payer_id;
                                        RAISE LOG 'Set batch payer_id to % for batch id: %', v_result_payer_id, v_batch_record_id;
                                    END IF;
                                ELSE
                                    RAISE WARNING 'No claim found for payment info at indices [%,%,%]', v_i, v_j, v_k;
                                END IF;
                            END LOOP; -- End paymentInfo loop
                        END IF;
                    END LOOP; -- End detailInfo loop
                END IF;
                -- Process provider adjustments for this transaction
                IF v_transaction ? 'providerAdjustments' AND 
                   jsonb_typeof(v_transaction->'providerAdjustments') = 'array' AND
                   jsonb_array_length(v_transaction->'providerAdjustments') > 0 THEN
                    DECLARE
                        v_prov_adj_idx INTEGER;
                        v_provider_adj JSONB;
                        v_adj_idx INTEGER;
                        v_adjustment JSONB;
                        v_prov_adj_subform_id INTEGER;
                    BEGIN
                        FOR v_prov_adj_idx IN 0..jsonb_array_length(v_transaction->'providerAdjustments') - 1 LOOP
                            v_provider_adj := v_transaction->'providerAdjustments'->v_prov_adj_idx;
                            
                            -- Process each adjustment within this provider adjustment
                            IF v_provider_adj ? 'adjustments' AND 
                               jsonb_typeof(v_provider_adj->'adjustments') = 'array' AND
                               jsonb_array_length(v_provider_adj->'adjustments') > 0 THEN
                                FOR v_adj_idx IN 0..jsonb_array_length(v_provider_adj->'adjustments') - 1 LOOP
                                    v_adjustment := v_provider_adj->'adjustments'->v_adj_idx;
                                    
                                    INSERT INTO form_med_claim_resp_835_prov_adj (
                                        provider_identifier,
                                        fiscal_period_date,
                                        adjustment_reason_code,
                                        adjustment_reason_code_value,
                                        provider_adjustment_identifier,
                                        provider_adjustment_amount,
                                        created_by,
                                        created_on,
                                        archived,
                                        deleted
                                    ) VALUES (
                                        CASE WHEN v_provider_adj ? 'providerIdentifier' THEN v_provider_adj->>'providerIdentifier' END,
                                        CASE WHEN v_provider_adj ? 'fiscalPeriodDate' THEN safe_to_date(v_provider_adj->>'fiscalPeriodDate') END,
                                        CASE WHEN v_adjustment ? 'adjustmentReasonCode' THEN v_adjustment->>'adjustmentReasonCode' END,
                                        CASE WHEN v_adjustment ? 'adjustmentReasonCodeValue' THEN v_adjustment->>'adjustmentReasonCodeValue' END,
                                        CASE WHEN v_adjustment ? 'providerAdjustmentIdentifier' THEN v_adjustment->>'providerAdjustmentIdentifier' END,
                                        CASE WHEN v_adjustment ? 'providerAdjustmentAmount' THEN safe_to_numeric(v_adjustment->>'providerAdjustmentAmount') END,
                                        1,
                                        CURRENT_TIMESTAMP,
                                        FALSE,
                                        FALSE
                                    ) RETURNING id INTO v_prov_adj_subform_id;
                                    
                                    -- Link to batch record
                                    INSERT INTO sf_form_med_claim_resp_835_batch_to_med_claim_resp_835_prov_adj (
                                        form_med_claim_resp_835_batch_fk,
                                        form_med_claim_resp_835_prov_adj_fk,
                                        archive,
                                        delete
                                    ) VALUES (
                                        v_batch_record_id,
                                        v_prov_adj_subform_id,
                                        FALSE,
                                        FALSE
                                    );
                                END LOOP;
                            END IF;
                        END LOOP;
                    END;
                END IF;
            END LOOP; -- End transactions loop
        END IF;
        
        -- Update batch record with totals
        UPDATE form_med_claim_resp_835_batch
        SET total_payment_amount_processed = v_total_payment_amount_processed,
            claims_processed = v_claims_processed
        WHERE id = v_batch_record_id;
        
        RAISE LOG 'Updated batch record id: % with claims_processed: %, total_payment_amount_processed: %', 
                  v_batch_record_id, v_claims_processed, v_total_payment_amount_processed;
        
        -- Log success
        BEGIN
            PERFORM log_billing_function(
                'parse_835_response',
                'info',
                'Successfully parsed 835 response',
                v_params || jsonb_build_object(
                    'batch_record_id', v_batch_record_id,
                    'claims_processed', v_claims_processed,
                    'total_payment_amount', v_total_payment_amount_processed
                )
            );
        EXCEPTION WHEN OTHERS THEN
            -- Ignore logging errors
        END;
        
        RAISE LOG 'parse_835_response completed successfully for response_id: % (batch_id: %, claims_processed: %, total_payment_amount: %)', 
                  p_response_id, v_batch_record_id, v_claims_processed, v_total_payment_amount_processed;
        
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS 
            v_error_message = MESSAGE_TEXT,
            v_error_detail = PG_EXCEPTION_DETAIL;
        
        -- Log error
        BEGIN
            PERFORM log_billing_function(
                'parse_835_response',
                'error',
                'Failed to parse 835 response: ' || v_error_message,
                v_params || jsonb_build_object(
                    'error_detail', v_error_detail,
                    'error_hint', SQLERRM
                )
            );
        EXCEPTION WHEN OTHERS THEN
            -- Ignore logging errors
        END;
        
        RAISE LOG 'parse_835_response failed for response_id: %: %', p_response_id, v_error_message;
        RAISE EXCEPTION 'Failed to parse 835 response: %', v_error_message;
    END;
END;
$$ LANGUAGE plpgsql;