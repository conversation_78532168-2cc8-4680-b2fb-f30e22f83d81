CREATE OR R<PERSON>LACE FUNCTION process_inv_transaction()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_existing_stock  RECORD;
    v_origin_stock_id INTEGER;
    v_origin_site_id  INTEGER;
    v_calculated_qty  NUMERIC;
    v_po_receipt_details RECORD;
    v_inv_details      RECORD;
BEGIN
    RAISE LOG 'Trigger process_inv_transaction fired for inv log ID: %, Type: %', NEW.id, NEW.transaction_type;

    IF NEW.transaction_type IN (
        'purchase', 'adjustment', 'pending_shipment', 'pending_shipment_adjustment',
        'transfer_initiated', 'transfer_completed', 'transfer_canceled',
        'purchase_void', 'pending_shipment_void'
    ) THEN
        RAISE LOG 'Log type % affects stock. Processing stock update for Inv ID: %, Site ID: %, PO ID: %, Lot: %, Serial: %',
            NEW.transaction_type, NEW.inventory_id, NEW.site_id, NEW.po_id, NEW.lot_no, NEW.serial_no;

        -- Directly calculate the stock quantity from the ledger (Re-applying this change)
        SELECT COALESCE(SUM(COALESCE(li.debit, 0) - COALESCE(li.credit, 0)), 0)::numeric INTO v_calculated_qty
        FROM form_ledger_inventory li
        WHERE li.inventory_id = NEW.inventory_id::integer
          AND li.site_id = NEW.site_id::integer
          AND COALESCE(li.po_id, 0) = COALESCE(NEW.po_id, 0)::integer
          AND COALESCE(li.lot_no, '') = COALESCE(NEW.lot_no, '')::text
          AND COALESCE(li.serial_no, '') = COALESCE(NEW.serial_no, '')::text;

        RAISE LOG 'Directly calculated quantity for stock item: %', v_calculated_qty;

        -- Fetch details needed for insert/update only if we might need to insert/update
        -- (Defer fetching until after checking if an existing record needs modification)
        SELECT * INTO v_existing_stock FROM form_inventory_stock invs
        WHERE invs.inventory_id = NEW.inventory_id::integer
          AND invs.site_id = NEW.site_id::integer
          AND COALESCE(invs.po_id, 0) = COALESCE(NEW.po_id, 0)::integer
          AND COALESCE(invs.lot_no, '') = COALESCE(NEW.lot_no, '')::text
          AND COALESCE(invs.serial_no, '') = COALESCE(NEW.serial_no, '')::text
          AND invs.deleted IS NOT TRUE AND invs.archived IS NOT TRUE
        FOR UPDATE;

        IF FOUND THEN
            -- Update Existing Stock Record if quantity differs
            IF v_existing_stock.quantity::numeric <> v_calculated_qty::numeric THEN
                RAISE LOG 'Found existing stock record ID: %. Updating quantity from % to: %', v_existing_stock.id, v_existing_stock.quantity, v_calculated_qty;
                UPDATE form_inventory_stock
                SET
                    quantity              = v_calculated_qty::numeric,            -- quantity
                    -- Update cost only if it changed in the log (primarily for adjustments)
                    acquisition_cost_ea   = COALESCE(NEW.acquisition_cost_ea, v_existing_stock.acquisition_cost_ea)::numeric,
                    updated_by            = NEW.transaction_by::integer,         -- updated_by
                    updated_on            = CURRENT_TIMESTAMP::timestamp         -- updated_on
                WHERE id = v_existing_stock.id;
            ELSE
                 RAISE LOG 'Found existing stock record ID: %. Quantity matches calculated value (%). No update needed.', v_existing_stock.id, v_calculated_qty;
            END IF;
        ELSE
            -- Insert New Stock Record (only if calculated quantity > 0)
            IF v_calculated_qty > 0 THEN
                RAISE LOG 'No existing stock record found. Inserting new record with Qty: %', v_calculated_qty;
                -- Fetch details needed for insert
                IF NEW.po_id IS NOT NULL THEN
                    SELECT pr.supplier_id, COALESCE(pr.updated_on, pr.created_on) as acq_dt, pr.is_340b
                    INTO v_po_receipt_details
                    FROM form_po_receipt pr WHERE pr.id = NEW.po_id::integer;
                END IF;
                SELECT inv.manufacturer_id INTO v_inv_details FROM form_inventory inv WHERE inv.id = NEW.inventory_id::integer;

                INSERT INTO form_inventory_stock (
                    inventory_id, site_id, po_id, supplier_id, manufacturer_id,
                    acquisition_datetime, acquisition_cost_ea, quantity,
                    lot_no, serial_no, is_340b, -- expiration_date (Not available directly here)
                    created_on, created_by, updated_on, updated_by
                )
                VALUES (
                    NEW.inventory_id::integer,
                    NEW.site_id::integer,
                    NEW.po_id::integer,
                    v_po_receipt_details.supplier_id::integer,
                    v_inv_details.manufacturer_id::text,
                    v_po_receipt_details.acq_dt::timestamp,
                    NEW.acquisition_cost_ea::numeric,
                    v_calculated_qty::numeric,
                    NEW.lot_no::text,
                    NEW.serial_no::text,
                    v_po_receipt_details.is_340b::text,
                    CURRENT_TIMESTAMP::timestamp,
                    NEW.transaction_by::integer,
                    CURRENT_TIMESTAMP::timestamp,
                    NEW.transaction_by::integer
                ) RETURNING * INTO v_existing_stock;
                RAISE LOG 'Inserted new stock record ID: %', v_existing_stock.id;
            ELSE
                 RAISE LOG 'Calculated stock quantity is 0 or less (%). Not inserting new stock record for Site ID %', v_calculated_qty, NEW.site_id;
            END IF;
        END IF;

        -- Handle Transfer Field Clearing/Setting (Logic remains largely the same)
        -- ... (existing transfer logic using v_existing_stock if needed) ...

    ELSE
      RAISE LOG 'Log type % does not affect stock quantity/transfer. Skipping stock update.', NEW.transaction_type;
    END IF;

    RETURN NULL;

EXCEPTION
    WHEN OTHERS THEN
        RAISE LOG 'Error in process_inv_transaction trigger: % - Log ID: %', SQLERRM, NEW.id;
        INSERT INTO inventory_ledger_error_log (
            error_message, error_context, additional_details, error_type, schema_name, table_name
        )
        VALUES (
            SQLERRM,
            'process_inv_transaction trigger',
            jsonb_build_object('inv_log_id', NEW.id, 'inv_log_details', row_to_json(NEW)),
            'Trigger Error',
            TG_TABLE_SCHEMA,
            TG_TABLE_NAME
        );
        RAISE;
END;
$$; 