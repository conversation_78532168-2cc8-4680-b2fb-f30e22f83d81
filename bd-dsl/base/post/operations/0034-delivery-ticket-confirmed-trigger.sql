-- Create the index
DROP INDEX IF EXISTS idx_delivery_tick_confirm;
CREATE INDEX idx_delivery_tick_confirm ON form_careplan_delivery_tick(id) 
WHERE COALESCE(confirmed, 'No') = 'Yes' AND archived IS NOT TRUE AND deleted IS NOT TRUE AND COALESCE(void, 'No') = 'No';

-- Create the trigger function
CREATE OR REPLACE FUNCTION trigger_dt_confirmed()
RETURNS TRIGGER AS $$
DECLARE
    v_is_confirmed boolean;
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    RAISE LOG 'Delivery ticket confirmed trigger % Old confirmed: % New confirmed: %', NEW.id, OLD.confirmed, NEW.confirmed;

    -- Only proceed if confirmed has changed to 'Yes'
    IF COALESCE(OLD.confirmed,'No') = 'No' AND COALESCE(NEW.confirmed,'No') = 'Yes' THEN
        -- Check if delivery ticket is voided
        IF COALESCE(NEW.void, 'No') = 'Yes' THEN
            RETURN NEW;
        END IF;

        -- Begin a transaction block for all operations
        BEGIN
            -- Create a temporary table to hold rx_info results
            CREATE TEMP TABLE tmp_rx_info AS
            SELECT DISTINCT
                rx.patient_id,
                rx.site_id,
                rx.careplan_id,
                rx.rx_no,
                rx.rx_id as order_rx_id,
                rx.careplan_order_item_id as order_item_id,
                rx.careplan_orderp_item_id as orderp_item_id,
                rx.fill_number+1 as next_fill_number,
                CASE 
                    WHEN rx.refills IS NOT NULL THEN 
                        GREATEST(COALESCE(rx.refills, 1) - (1 + COALESCE(rxf.fills, 0)), 0)
                    ELSE NULL
                END as refills_remaining,
                CASE 
                    WHEN rx.doses_allowed IS NOT NULL THEN 
                        GREATEST(COALESCE(rx.doses_allowed, 1) - (COALESCE(dti.doses_prepared, 0) + COALESCE(rxf.doses_dispensed, 0)), 0)
                    ELSE NULL
                END as doses_remaining,
                COALESCE(dti.last_through_date - COALESCE(cp.refill_days_out, 7)) as next_fill_date,
                COALESCE(dti.last_through_date - COALESCE(cp.refill_days_out, 7) + COALESCE(cp.next_delivery_day, 6)) as next_delivery_date,
                dt.service_from as last_dispense_date,
                dti.last_through_date as last_through_date,
                NEW.id as last_dispense_delivery_tick_id,
                rx.last_event_id,
                rx.refill_tracking,
                rx.working_dispense_id
            FROM vw_rx_order rx
            LEFT JOIN vw_rx_past_fills rxf ON rxf.rx_id = rx.rx_id
            INNER JOIN form_company cp ON cp.id = 1
            INNER JOIN form_careplan_delivery_tick dt 
                ON dt.id = NEW.id
                AND dt.archived IS NOT TRUE
                AND dt.deleted IS NOT TRUE
                AND COALESCE(dt.void, 'No') <> 'Yes'
            INNER JOIN form_careplan_dt_item dti ON dti.ticket_no = dt.ticket_no
                AND dti.patient_id = dt.patient_id
                AND dti.careplan_id = dt.careplan_id
                AND dti.rx_id = rx.rx_id
                AND dti.archived IS NOT TRUE
                AND dti.deleted IS NOT TRUE
                AND dti.type = 'Drug'
                AND COALESCE(dti.is_primary_drug, 'No') = 'Yes'
            WHERE dti.rx_id = rx.rx_id;

            RAISE LOG 'Updating form_careplan_order_rx with refills remaining';

            -- Update form_careplan_order_rx
            UPDATE form_careplan_order_rx rx
            SET
                next_fill_date = ri.next_fill_date,
                next_delivery_date = ri.next_delivery_date,
                last_dispense_date = ri.last_dispense_date,
                last_through_date = ri.last_through_date,
                last_dispense_delivery_tick_id = ri.last_dispense_delivery_tick_id,
                next_fill_number = ri.next_fill_number,
                refills_remaining = ri.refills_remaining,
                doses_remaining = ri.doses_remaining
            FROM tmp_rx_info ri
            WHERE rx.id = ri.order_rx_id;

            -- Update NCPDP claim
            UPDATE form_ncpdp np
            SET substatus_id = '101'
            FROM form_billing_invoice bi
            WHERE bi.delivery_ticket_id = NEW.id AND np.invoice_no = bi.invoice_no AND np.substatus_id IS NULL;

            -- Update medical claim
            UPDATE form_med_claim mc
            SET substatus_id = '101'
            FROM form_billing_invoice bi
            WHERE bi.delivery_ticket_id = NEW.id AND mc.invoice_no = bi.invoice_no AND mc.substatus_id IS NULL;

            -- Update label: IV
            UPDATE form_careplan_order_lbl_iv oiv
            SET line3_date = ri.next_fill_date
            FROM sf_form_careplan_order_rx_to_careplan_order_lbl_iv sfoiv
            INNER JOIN tmp_rx_info ri ON sfoiv.form_careplan_order_rx_fk = ri.order_rx_id
            WHERE sfoiv.form_careplan_order_lbl_iv_fk = oiv.id
            AND sfoiv.archive IS NOT TRUE
            AND sfoiv.delete IS NOT TRUE;

            -- Update label: Syringe
            UPDATE form_careplan_order_lbl_syr syr
            SET line3_date = ri.next_fill_date
            FROM sf_form_careplan_order_rx_to_careplan_order_lbl_syr sfsyr
            INNER JOIN tmp_rx_info ri ON sfsyr.form_careplan_order_rx_fk = ri.order_rx_id
            WHERE sfsyr.form_careplan_order_lbl_syr_fk = syr.id
            AND sfsyr.archive IS NOT TRUE
            AND sfsyr.delete IS NOT TRUE;

            -- Update label: TPN
            UPDATE form_careplan_order_lbl_tpn tpn
            SET line3_date = ri.next_fill_date
            FROM sf_form_careplan_order_rx_to_careplan_order_lbl_tpn sftpn
            INNER JOIN tmp_rx_info ri ON sftpn.form_careplan_order_rx_fk = ri.order_rx_id
            WHERE sftpn.form_careplan_order_lbl_tpn_fk = tpn.id
            AND sftpn.archive IS NOT TRUE
            AND sftpn.delete IS NOT TRUE;

            -- Update order items
            UPDATE form_careplan_order_item oi
            SET
                next_fill_number = ri.next_fill_number,
                status_id = '5'
            FROM tmp_rx_info ri
            WHERE oi.id = ri.order_item_id AND oi.status_id = '1'
            AND ri.order_item_id IS NOT NULL;

            -- Update order pending items
            UPDATE form_careplan_orderp_item op
            SET
                next_fill_number = ri.next_fill_number,
                status_id = '5'
            FROM tmp_rx_info ri
            WHERE op.id = ri.orderp_item_id AND op.status_id = '1'
            AND ri.orderp_item_id IS NOT NULL;

            UPDATE form_careplan_orders_item osi
            SET status_id = CASE WHEN osi.one_time_only = 'Yes' THEN '2'
            ELSE '5' END 
            FROM tmp_rx_info ri 
            WHERE osi.associated_rx_id = ri.order_rx_id
            AND osi.status_id IN ('1', '5')
            AND ri.order_rx_id IS NOT NULL;

            -- Clean up (optional in function, automatic in DO blocks)
            DROP TABLE IF EXISTS tmp_rx_info;

            -- Add progress note 
            INSERT INTO form_patient_note (
                patient_id,
                careplan_id,
                created_by,
                created_on,
                note_type,
                subject,
                note
            ) VALUES (
                NEW.patient_id,
                NEW.careplan_id,
                NEW.created_by,
                NEW.created_on,
                'Pharmacy',
                'Delivery Ticket Confirmed',
                'Delivery ticket ' || NEW.ticket_no || ' has been confirmed. Service from: ' || TO_CHAR(NEW.service_from, 'MM/DD/YYYY') || ' to ' || TO_CHAR(NEW.service_to, 'MM/DD/YYYY') || ' with a delivery date of: ' || TO_CHAR(NEW.delivery_date, 'MM/DD/YYYY') || '.'
            );
        EXCEPTION WHEN OTHERS THEN
            -- Log error
            INSERT INTO dispensing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                SQLERRM,
                'Exception in trigger_dt_confirmed',
                'FUNCTION',
                current_schema(),
                'form_careplan_delivery_tick',
                jsonb_build_object(
                    'function_name', 'trigger_dt_confirmed'
                )
            );
            
            RAISE WARNING 'Failed to update dispense records: %', SQLERRM;
        END;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE OR REPLACE TRIGGER update_dt_confirmed
    BEFORE UPDATE
    ON form_careplan_delivery_tick
    FOR EACH ROW
    EXECUTE FUNCTION trigger_dt_confirmed();