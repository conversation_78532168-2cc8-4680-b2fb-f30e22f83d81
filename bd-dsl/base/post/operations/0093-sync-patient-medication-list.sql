-- Function to sync order records with patient medication records
DO $$ BEGIN
  PERFORM drop_all_function_signatures('sync_order_to_medication');
END $$;
CREATE OR REPLACE FUNCTION sync_order_to_medication()
RETURNS TRIGGER AS $$
DECLARE
    inventory_record RECORD;
    med_record RECORD;
    new_status TEXT;
    frequency_label TEXT;
    v_prescriber_id INTEGER;
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Only process records with rx_no
    RAISE LOG 'Processing record with rx_no %', NEW.rx_no;
    IF (NEW.rx_no IS NULL OR NEW.rx_no = '') THEN
        RETURN NEW;
    END IF;

    -- Get inventory record for medication details
    RAISE LOG 'Getting inventory record for inventory_id %', NEW.inventory_id;
    SELECT * INTO inventory_record
    FROM form_inventory
    WHERE id = NEW.inventory_id;
    
    IF NOT FOUND THEN
        RAISE WARNING 'No inventory record found for inventory_id %', NEW.inventory_id;
        RETURN NEW;
    END IF;

    IF (TG_TABLE_NAME = 'form_careplan_orderp_item') THEN
        SELECT ord.prescriber_id INTO v_prescriber_id
        FROM form_careplan_order ord
        INNER JOIN sf_form_careplan_order_to_careplan_orderp_item sfo ON sfo.form_careplan_orderp_item_fk = NEW.id AND sfo.form_careplan_order_fk = ord.id
        WHERE sfo.archive IS NOT TRUE AND sfo.delete IS NOT TRUE;
    ELSE
        SELECT ord.prescriber_id INTO v_prescriber_id
        FROM form_careplan_order ord
        INNER JOIN sf_form_careplan_order_to_careplan_order_item sfo ON sfo.form_careplan_order_item_fk = NEW.id AND sfo.form_careplan_order_fk = ord.id
        WHERE sfo.archive IS NOT TRUE AND sfo.delete IS NOT TRUE;
    END IF;

    -- Get frequency label
    SELECT COALESCE(label_string, name) INTO frequency_label
    FROM form_list_frequency
    WHERE code = NEW.frequency_id;

    -- Determine appropriate status
    IF NEW.status_id = '5' THEN -- Active order
        new_status := 'Active';
    ELSE
        new_status := 'Discontinued';
    END IF;

    RAISE LOG 'New Status: % Rx No: % Patient ID: %', new_status, NEW.rx_no, NEW.patient_id;
    -- Check if medication record already exists
    SELECT * INTO med_record
    FROM form_patient_medication
    WHERE rx_no = NEW.rx_no AND patient_id = NEW.patient_id;

    RAISE LOG 'New RXNO: % FOUND: % Med Record ID: %', NEW.rx_no, FOUND, med_record.id;
    IF FOUND THEN
        -- Update existing record
        RAISE LOG 'Updating medication record for %', NEW.rx_no;
        UPDATE form_patient_medication
        SET 
            status = new_status,
            fdb_id = inventory_record.fdb_id,
            medication_dose = TRIM(CONCAT(NEW.dose, ' ', NEW.dose_unit_id)),
            medication_frequency = frequency_label,
            start_date = NEW.start_date,
            end_date = CASE WHEN new_status = 'Discontinued' THEN CURRENT_DATE ELSE NULL END,
            discontinued_reason = CASE
                WHEN new_status = 'Discontinued' AND NEW.status_id = '2' THEN 'Discontinued'
                WHEN new_status = 'Discontinued' AND NEW.status_id = '3' THEN 'Prior Auth Denied'
                WHEN new_status = 'Discontinued' AND NEW.status_id = '4' THEN 'On Hold'
                ELSE NULL
            END,
            updated_on = CURRENT_TIMESTAMP,
            updated_by = COALESCE(NEW.updated_by, NEW.created_by)
        WHERE id = med_record.id;
    ELSE
        -- Create new record if order is active
        IF new_status = 'Active' THEN
            RAISE LOG 'Creating new medication record for %', NEW.rx_no;
            INSERT INTO form_patient_medication (
                patient_id,
                rx_no,
                fdb_id,
                medication_dose,
                medication_frequency,
                prescribed_by,
                prescribed_by_id,
                reported_by,
                status,
                start_date,
                created_on,
                created_by,
                type_id
            ) VALUES (
                NEW.patient_id,
                NEW.rx_no,
                inventory_record.fdb_id,
                TRIM(CONCAT(NEW.dose, ' ', NEW.dose_unit_id)),
                frequency_label,
                'Physician',
                v_prescriber_id,
                'Physician',
                new_status,
                NEW.start_date,
                CURRENT_TIMESTAMP,
                COALESCE(NEW.updated_by, NEW.created_by),
                NEW.type_id
            );
        END IF;
    END IF;

    RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    -- Log error and continue
    RAISE WARNING 'Error in sync_order_to_medication: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER trig_sync_orderp_item_to_medication
    AFTER INSERT OR UPDATE OF rx_no, status_id, dose, dose_unit_id, frequency_id, start_date, next_fill_number
    ON form_careplan_orderp_item
    FOR EACH ROW
    EXECUTE FUNCTION sync_order_to_medication();

CREATE OR REPLACE TRIGGER trig_sync_order_item_to_medication
    AFTER INSERT OR UPDATE OF rx_no, status_id, dose, dose_unit_id, frequency_id, start_date, next_fill_number
    ON form_careplan_order_item
    FOR EACH ROW
    EXECUTE FUNCTION sync_order_to_medication();