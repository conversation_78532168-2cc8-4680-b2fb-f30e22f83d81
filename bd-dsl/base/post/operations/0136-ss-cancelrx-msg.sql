CREATE OR REPLACE FUNCTION _parse_and_insert_cancelrx(
    p_ss_log_id INTEGER,
    p_message_json JSONB,
    p_surescripts_message_type TEXT,
    p_header_data JSONB,
    p_patient_data JSONB,
    p_prescriber_data JSONB,
    p_priority_flag TEXT
)
RETURNS TABLE (ss_message_id INTEGER, error_message TEXT) LANGUAGE plpgsql AS $$
DECLARE
    -- Parsed data holders from helper functions
    v_parsed_header ss_extracted_header_type;
    v_parsed_patient ss_extracted_patient_type;
    v_parsed_prescriber ss_extracted_prescriber_info_type;
    v_parsed_medication ss_extracted_medication_type;
    -- No supervisor or fu_prescriber for CancelRx generally
    v_compounds ss_compound_type[] := ARRAY[]::ss_compound_type[];
    v_diagnoses ss_diagnosis_type[] := ARRAY[]::ss_diagnosis_type[];

    v_main_params ss_message_main_params_type;

    -- Declarations for MedicationPrescribed fields (minimal set for CancelRx)
    v_med_path_segment TEXT := 'MedicationPrescribed';

    -- Declarations for resolved IDs (some might come from helpers now)
    v_resolved_ids ss_resolved_ids_type; -- Using the composite type

    -- Other utility variables
    v_status_icons TEXT[];
    v_new_ss_message_id INTEGER;
    v_error_text TEXT;
    v_error_context TEXT;
    v_show_options TEXT[];
BEGIN
    RAISE LOG 'Helper: Parsing CancelRx for ss_log_id: % with MessageType: %', p_ss_log_id, p_surescripts_message_type;

    -- Call helper functions to parse main sections
    v_parsed_header := _parse_ss_header_data(p_header_data);
    v_main_params.ss_digital_signature_indicator := v_parsed_header.digital_signature_indicator;
    v_main_params.ss_direction := 'IN'; -- Explicit for inbound
    v_main_params.ss_sent_dt := v_parsed_header.sent_dt;
    v_main_params.ss_to := v_parsed_header.to_val;
    v_main_params.ss_from := v_parsed_header.from_val;
    v_main_params.ss_message_id_header := v_parsed_header.message_id_header;
    v_main_params.ss_related_message_id := v_parsed_header.related_message_id;
    v_main_params.ss_prescriber_order_number := v_parsed_header.prescriber_order_number;
    v_main_params.ss_pharmacy_rx_no := v_parsed_header.pharmacy_rx_no;
    v_main_params.sender_software_developer := v_parsed_header.sender_software_developer;
    v_main_params.sender_software_product := v_parsed_header.sender_software_product;
    v_main_params.sender_software_version := v_parsed_header.sender_software_version;
    v_main_params.order_group_no := v_parsed_header.order_group_no;
    v_main_params.rx_group_no := v_parsed_header.rx_group_no;
    v_main_params.order_group_icnt := v_parsed_header.order_group_icnt;
    v_main_params.rx_group_icnt := v_parsed_header.rx_group_icnt;
    v_main_params.order_group_tcnt := v_parsed_header.order_group_tcnt;
    v_main_params.rx_group_tcnt := v_parsed_header.rx_group_tcnt;
    v_main_params.order_group_reason_code := v_parsed_header.order_group_reason_code;
    v_main_params.rx_group_reason_code := v_parsed_header.rx_group_reason_code;

    v_parsed_patient := _parse_ss_patient_data(p_patient_data, p_message_json->'Message'->'Body', p_surescripts_message_type);
    v_main_params.ss_patient_name_display := v_parsed_patient.name_display;
    v_main_params.ss_patient_first_name    := v_parsed_patient.first_name;
    v_main_params.ss_patient_last_name     := v_parsed_patient.last_name;
    v_main_params.ss_patient_middle_name   := v_parsed_patient.middle_name;
    v_main_params.ss_patient_dob           := v_parsed_patient.dob;
    v_main_params.ss_patient_gender        := v_parsed_patient.gender;
    v_main_params.ss_patient_ssn           := v_parsed_patient.ssn;
    v_main_params.ss_patient_mrn           := v_parsed_patient.mrn;
    v_main_params.ss_patient_home_street_1 := v_parsed_patient.home_street_1;
    v_main_params.ss_patient_home_street_2 := v_parsed_patient.home_street_2;
    v_main_params.ss_patient_home_city     := v_parsed_patient.home_city;
    v_main_params.ss_patient_home_state    := v_parsed_patient.home_state_code;
    v_main_params.ss_patient_home_zip      := v_parsed_patient.home_zip;
    v_main_params.ss_patient_phone         := v_parsed_patient.phone;
    v_main_params.ss_nka                   := v_parsed_patient.nka; 

    v_parsed_prescriber := _parse_ss_prescriber_data(p_prescriber_data);
    v_main_params.ss_prescriber_name_display := v_parsed_prescriber.name_display;
    v_main_params.ss_prescriber_npi := v_parsed_prescriber.npi;
    v_main_params.ss_prescriber_dea := v_parsed_prescriber.dea;
    v_main_params.ss_prescriber_rems := v_parsed_prescriber.rems;
    v_main_params.ss_prescriber_state_cs_lic := v_parsed_prescriber.state_cs_lic;
    v_main_params.ss_prescriber_medicare := v_parsed_prescriber.medicare;
    v_main_params.ss_prescriber_medicaid := v_parsed_prescriber.medicaid;
    v_main_params.ss_prescriber_state_lic := v_parsed_prescriber.state_lic;
    v_main_params.ss_prescriber_certificate_to_prescribe := v_parsed_prescriber.certificate_to_prescribe;
    v_main_params.ss_prescriber_2000waiver_id := v_parsed_prescriber.waiver_2000_id;
    v_main_params.ss_prescriber_spec_id := v_parsed_prescriber.spec_code;
    v_main_params.ss_prescriber_has_license := v_parsed_prescriber.has_license;
    v_main_params.ss_prescriber_last_name := v_parsed_prescriber.last_name;
    v_main_params.ss_prescriber_first_name := v_parsed_prescriber.first_name;
    v_main_params.ss_prescriber_address_1 := v_parsed_prescriber.address_1;
    v_main_params.ss_prescriber_address_2 := v_parsed_prescriber.address_2;
    v_main_params.ss_prescriber_city := v_parsed_prescriber.city;
    v_main_params.ss_prescriber_state := v_parsed_prescriber.state_code;
    v_main_params.ss_prescriber_zip := v_parsed_prescriber.zip;
    v_main_params.ss_prescriber_phone := v_parsed_prescriber.phone;
    v_main_params.ss_prescriber_extension := v_parsed_prescriber.extension;
    v_main_params.ss_prescriber_fax := v_parsed_prescriber.fax;
    v_main_params.ss_prescriber_loc_name := v_parsed_prescriber.loc_name;
    v_main_params.ss_prescriber_loc_ncpdp_id := v_parsed_prescriber.loc_ncpdp_id;
    v_main_params.ss_prescriber_loc_dea := v_parsed_prescriber.loc_dea;
    v_main_params.ss_prescriber_loc_rems := v_parsed_prescriber.loc_rems;
    v_main_params.ss_prescriber_loc_state_cs_lic := v_parsed_prescriber.loc_state_cs_lic;
    v_main_params.ss_prescriber_loc_medicare := v_parsed_prescriber.loc_medicare;
    v_main_params.ss_prescriber_loc_medicaid := v_parsed_prescriber.loc_medicaid;
    v_main_params.ss_prescriber_loc_state_lic := v_parsed_prescriber.loc_state_lic;
    v_main_params.ss_prescriber_loc_has_license := v_parsed_prescriber.loc_has_license;
    v_main_params.ss_prescriber_agent_first_name := v_parsed_prescriber.agent_first_name;
    v_main_params.ss_prescriber_agent_last_name := v_parsed_prescriber.agent_last_name;

    v_compounds := _parse_ss_compounds(p_message_json#>ARRAY['Message','Body',p_surescripts_message_type,v_med_path_segment,'CompoundInformation','CompoundIngredientsLotNotUsed']);
    RAISE NOTICE 'Parsed compounds: %', v_compounds;
    v_diagnoses := _parse_ss_diagnoses(p_message_json#>ARRAY['Message','Body',p_surescripts_message_type,v_med_path_segment,'Diagnosis']);
    RAISE NOTICE 'Parsed diagnoses: %', v_diagnoses;
    -- Initialize status icons
    v_status_icons := ARRAY['new', 'canceled']::TEXT[];
    IF p_priority_flag = 'X' THEN
        v_status_icons := array_append(v_status_icons, 'high_priority');
    END IF;

    -- Resolve IDs using helper, passing NULL for medication as it's not fully parsed into ss_extracted_medication_type for CancelRx
    v_resolved_ids := _resolve_ss_ids(
        p_ss_to := v_main_params.ss_to,
        p_ss_from := v_main_params.ss_from,
        p_ss_direction := v_main_params.ss_direction,
        p_parsed_patient := v_parsed_patient,
        p_parsed_prescriber := v_parsed_prescriber,
        p_parsed_medication := NULL, -- CancelRx MedicationRequested is minimal
        p_prescriber_order_number := v_main_params.ss_prescriber_order_number
    );

    v_main_params.resolved_site_id := v_resolved_ids.resolved_site_id;
    v_main_params.resolved_patient_id := v_resolved_ids.resolved_patient_id;
    v_main_params.resolved_physician_id := v_resolved_ids.resolved_physician_id;

    -- Special handling for resolved_pharmacy_order_id in CancelRx
    IF v_main_params.ss_related_message_id IS NOT NULL THEN
        SELECT orig_msg.pharmacy_order_id INTO v_main_params.resolved_pharmacy_order_id
        FROM form_ss_message orig_msg
        WHERE orig_msg.message_id = v_main_params.ss_related_message_id AND orig_msg.deleted IS NOT TRUE AND orig_msg.archived IS NOT TRUE LIMIT 1;
    ELSIF v_main_params.ss_prescriber_order_number IS NOT NULL AND v_main_params.resolved_site_id IS NOT NULL AND v_main_params.resolved_patient_id IS NOT NULL THEN
        SELECT co.id INTO v_main_params.resolved_pharmacy_order_id FROM form_careplan_order co
        JOIN form_ss_message ssm ON co.id = ssm.pharmacy_order_id
        WHERE ssm.physician_order_id = v_main_params.ss_prescriber_order_number
          AND co.site_id = v_main_params.resolved_site_id
          AND co.patient_id = v_main_params.resolved_patient_id
          AND co.deleted IS NOT TRUE AND co.archived IS NOT TRUE
        ORDER BY co.created_on DESC LIMIT 1;
    END IF;
    RAISE LOG 'Resolved pharmacy_order_id (order to cancel): % for CancelRx (Log ID: %)', v_main_params.resolved_pharmacy_order_id, p_ss_log_id;

    -- Populate remaining v_main_params fields
    v_main_params.ss_priority_flag := p_priority_flag;
    v_main_params.ss_message_type := p_surescripts_message_type;

    -- Medication info specific to CancelRx (minimal set)
    v_parsed_medication := _parse_ss_medication_data(p_message_json, p_surescripts_message_type, v_med_path_segment);
    v_main_params.description := v_parsed_medication.description;
    v_main_params.product_code_qualifier_id := v_parsed_medication.product_code_qualifier_id;
    v_main_params.product_code := v_parsed_medication.product_code;
    v_main_params.fdb_id := v_resolved_ids.fdb_id; -- From resolved IDs
    v_main_params.drug_db_qualifier_id := v_parsed_medication.drug_db_qualifier_id;
    v_main_params.drug_db_code := v_parsed_medication.drug_db_code;
    v_main_params.dea_schedule_id := v_parsed_medication.dea_schedule_id;
    v_main_params.strength := v_parsed_medication.strength;
    v_main_params.strength_form_id := v_parsed_medication.strength_form_id;
    v_main_params.strength_uom_id := v_parsed_medication.strength_uom_id;
    v_main_params.quantity := v_parsed_medication.quantity;
    v_main_params.quantity_qualifier_id := v_parsed_medication.quantity_qualifier_id;
    v_main_params.quantity_uom_id := v_parsed_medication.quantity_uom_id;
    v_main_params.days_supply := v_parsed_medication.days_supply;
    v_main_params.compound_dosage_form_id := v_parsed_medication.compound_dosage_form_id;
    v_main_params.written_date := v_parsed_medication.written_date;
    v_main_params.start_date := v_parsed_medication.start_date;
    v_main_params.expiration_date := v_parsed_medication.expiration_date;
    v_main_params.effective_date := v_parsed_medication.effective_date;
    v_main_params.daw := v_parsed_medication.daw;
    v_main_params.daw_code_reason := v_parsed_medication.daw_code_reason;
    v_main_params.refills := v_parsed_medication.refills;
    v_main_params.pa_number := v_parsed_medication.pa_number;
    v_main_params.pa_status_id := v_parsed_medication.pa_status_id;
    v_main_params.drug_cvg_status_id := v_parsed_medication.drug_cvg_status_id;
    v_main_params.do_not_fill := v_parsed_medication.do_not_fill;
    v_main_params.note := v_parsed_medication.note;
    v_main_params.sig := v_parsed_medication.sig;
    v_main_params.delivery_request := v_parsed_medication.delivery_request;
    v_main_params.delivery_location := v_parsed_medication.delivery_location;
    v_main_params.flavoring_requested := v_parsed_medication.flavoring_requested;
    v_main_params.prescriber_checked_rems := v_parsed_medication.prescriber_checked_rems;
    v_main_params.rems_risk_category := v_parsed_medication.rems_risk_category;
    v_main_params.rems_authorization_number := v_parsed_medication.rems_authorization_number;
    v_main_params.clinical_info_qualifier := v_parsed_medication.clinical_info_qualifier;
    v_main_params.prohibit_renewal_request := v_parsed_medication.prohibit_renewal_request;

    IF v_main_params.ss_prescriber_agent_last_name IS NOT NULL THEN v_show_options := array_append(v_show_options, 'Agent'); END IF;
    IF v_parsed_patient.rems IS NOT NULL OR v_main_params.prescriber_checked_rems IS NOT NULL OR v_main_params.rems_risk_category IS NOT NULL OR v_main_params.rems_authorization_number IS NOT NULL THEN
        v_show_options := array_append(v_show_options, 'REMS');
    END IF;
    IF v_parsed_prescriber.loc_name IS NOT NULL THEN
        v_show_options := array_append(v_show_options, 'Practice Location');
    END IF;
    IF v_main_params.order_group_no IS NOT NULL THEN v_show_options := array_append(v_show_options, 'Order Group'); END IF;
    IF v_main_params.rx_group_no IS NOT NULL THEN v_show_options := array_append(v_show_options, 'RX Group'); END IF;

    -- Status info
    v_main_params.status_icons := v_status_icons;
    v_main_params.show_options := v_show_options;
    v_main_params.processed_dt := CURRENT_TIMESTAMP;
    v_main_params.ss_cancel_status := NULL;

    -- Explicitly NULL out fields not relevant to CancelRx from ss_message_main_params_type
    v_main_params.ss_renewal_status := NULL;
    v_main_params.ss_renewal_denial_reason_code := NULL;
    v_main_params.ss_renewal_denied_reason := NULL;
    v_main_params.ss_renewal_note := NULL;
    v_main_params.ss_chg_type_id := NULL;
    v_main_params.ss_chg_type_sc_id := NULL;
    v_main_params.ss_chg_sc_aw_chg_id := NULL;
    v_main_params.ss_chg_status := NULL;
    v_main_params.ss_chg_dr_code_id := NULL;
    v_main_params.ss_chg_denied_reason := NULL;
    v_main_params.ss_chg_approved_note := NULL;
    v_main_params.ss_chg_vr_cd_id := NULL;
    v_main_params.ss_chg_validated_note := NULL;

    -- Call the insertion function (passing empty arrays for subforms not in CancelRx)
    v_new_ss_message_id := _insert_ss_message_and_subforms(
        p_main_params := v_main_params,
        p_allergies := ARRAY[]::ss_allergy_type[],
        p_benefits := ARRAY[]::ss_benefit_type[],
        p_observations := ARRAY[]::ss_observation_type[],
        p_dues := ARRAY[]::ss_due_type[],
        p_compounds := v_compounds,
        p_diagnoses := v_diagnoses,
        p_codified_notes := ARRAY[]::ss_codified_note_type[],
        p_ss_log_id := p_ss_log_id
    );

    -- Update original message logic
    IF v_new_ss_message_id IS NOT NULL THEN
        IF v_main_params.resolved_pharmacy_order_id IS NOT NULL THEN
            DECLARE
                v_original_prescriber_order_number_from_cancel TEXT;
                v_original_newrx_id INTEGER;
            BEGIN
                SELECT ssm.physician_order_id INTO v_original_prescriber_order_number_from_cancel
                FROM form_ss_message ssm
                WHERE ssm.message_id = v_main_params.ss_related_message_id AND ssm.deleted IS NOT TRUE AND ssm.archived IS NOT TRUE
                LIMIT 1;

                IF v_original_prescriber_order_number_from_cancel IS NOT NULL THEN
                    UPDATE form_careplan_orderp_item opi
                    SET status_id = '2' -- Discontinued
                    WHERE opi.physician_order_id = v_original_prescriber_order_number_from_cancel
                      AND opi.id IN (SELECT sfo.form_careplan_orderp_item_fk FROM sf_form_careplan_order_to_careplan_orderp_item sfo WHERE sfo.form_careplan_order_fk = v_main_params.resolved_pharmacy_order_id);

                    RAISE LOG 'Attempted to mark careplan_orderp_item as discontinued for PON % related to pharmacy_order_id % due to CancelRx message ID %.',
                              v_original_prescriber_order_number_from_cancel, v_main_params.resolved_pharmacy_order_id, v_main_params.ss_message_id_header;
                END IF;

                SELECT id INTO v_original_newrx_id FROM form_ss_message
                WHERE message_id = v_main_params.ss_related_message_id
                      AND deleted IS NOT TRUE AND archived IS NOT TRUE
                ORDER BY created_on DESC LIMIT 1;

                IF v_original_newrx_id IS NOT NULL THEN
                    UPDATE form_ss_message
                    SET status_icons = array_remove(array_append(COALESCE(status_icons, ARRAY[]::TEXT[]), 'canceled'), 'new'),
                        processed = 'Yes',
                        processed_dt = CURRENT_TIMESTAMP,
                        last_action = 'Prescription canceled by physician via CancelRx message ' || v_main_params.ss_message_id_header || ' on ' || TO_CHAR(CURRENT_TIMESTAMP, 'MM/DD/YYYY HH:MI AM'),
                        show_last_action = 'Yes',
                        canceled_message_id = v_new_ss_message_id,
                        is_active_message = 'No'
                    WHERE id = v_original_newrx_id;
                    RAISE LOG 'Updated original ss_message ID % due to CancelRx %.', v_original_newrx_id, v_main_params.ss_message_id_header;
                ELSE
                    RAISE WARNING 'CancelRx: Original NewRx message with MessageID % not found to update.', v_main_params.ss_related_message_id;
                END IF;
            END;
        ELSIF v_main_params.ss_related_message_id IS NOT NULL THEN
            DECLARE v_original_related_msg_id INTEGER;
            BEGIN
                SELECT id INTO v_original_related_msg_id FROM form_ss_message
                WHERE message_id = v_main_params.ss_related_message_id AND deleted IS NOT TRUE AND archived IS NOT TRUE
                ORDER BY created_on DESC LIMIT 1;

                IF v_original_related_msg_id IS NOT NULL THEN
                    UPDATE form_ss_message
                    SET status_icons = array_remove(array_append(COALESCE(status_icons, ARRAY[]::TEXT[]), 'canceled'), 'new'),
                        processed = 'Yes',
                        processed_dt = CURRENT_TIMESTAMP,
                        last_action = 'Cancellation processed via CancelRx message ' || v_main_params.ss_message_id_header || ' on ' || TO_CHAR(CURRENT_TIMESTAMP, 'MM/DD/YYYY HH:MI AM'),
                        show_last_action = 'Yes',
                        canceled_message_id = v_new_ss_message_id,
                        is_active_message = 'No'
                    WHERE id = v_original_related_msg_id;
                    RAISE LOG 'Updated status_icons on related message ID % due to CancelRx message ID %.', v_original_related_msg_id, v_main_params.ss_message_id_header;
                END IF;
            END;
        ELSE
            RAISE WARNING 'CancelRx message ID % (Log ID: %) could not identify a specific order or prior message to update as canceled.', v_main_params.ss_message_id_header, p_ss_log_id;
        END IF;
    END IF;

    RAISE LOG 'CancelRx successfully parsed and inserted for ss_log_id: %. New form_ss_message_id: % ', p_ss_log_id, v_new_ss_message_id;
    RETURN QUERY SELECT v_new_ss_message_id, NULL::TEXT;
EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_error_text = MESSAGE_TEXT, v_error_context = PG_EXCEPTION_CONTEXT;
        RAISE LOG 'Error in _parse_and_insert_cancelrx for ss_log_id: %. Error: %, Context: %', p_ss_log_id, v_error_text, v_error_context;
        INSERT INTO ss_error_log (
            error_message, error_context, error_type,
            table_name, table_id, application_name, additional_details
        ) VALUES (
            v_error_text, v_error_context, 'CANCELRX_PARSE_EXCEPTION',
            'form_ss_log', p_ss_log_id, 'SURESCRIPTS_INBOUND_PARSER', p_message_json
        );
        RETURN QUERY SELECT NULL::INTEGER, ('Error processing CancelRx message (Log ID: ' || p_ss_log_id || '): ' || v_error_text)::TEXT;
END; $$;
