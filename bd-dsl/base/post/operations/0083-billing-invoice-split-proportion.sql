-- Split Proportion Trigger for Billing Invoice
-- This trigger handles the proportional distribution of total_expected, paid, and copay
-- on charge lines for invoices without posted revenue using vw_charge_line_pending_ar_balance view.

CREATE OR REPLACE FUNCTION process_billing_invoice_split_proportion()
RETURNS TRIGGER AS $$
DECLARE
    v_charge_record RECORD;
    v_total_expected NUMERIC := 0;
    v_total_paid NUMERIC := 0;
    v_total_pt_pay NUMERIC := 0;
	v_line_copay_amount NUMERIC := 0;
    v_line_balance_amount NUMERIC := 0;
	v_line_expected_amount NUMERIC := 0;
	v_line_paid_amount NUMERIC := 0;
    v_revenue_accepted_posted BOOLEAN := FALSE;
    v_charges_editable BOOLEAN := FALSE;
BEGIN
    RAISE LOG 'process_billing_invoice_split_proportion Invoice ID %', NEW.id;
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Skip if trigger flag is set
    IF current_setting('clara.disable_invoice_trigger', true) = 'on' THEN
        RAISE LOG 'process_billing_invoice_split_proportion skipped due to flag';
        RETURN NEW;
    END IF;

    -- Skip if invoice is void, zeroed, archived, deleted, or revenue is already posted
    IF COALESCE(NEW.void, 'No') = 'Yes' OR
       COALESCE(NEW.zeroed, 'No') = 'Yes' OR
       COALESCE(NEW.archived, FALSE) = TRUE OR
       COALESCE(NEW.deleted, FALSE) = TRUE OR
       COALESCE(NEW.revenue_accepted_posted, 'No') = 'Yes' THEN
        RAISE LOG 'Skipping split proportion for invoice ID %, conditions not met', NEW.id;
        RETURN NEW;
    END IF;

    -- Fetch invoice totals
    SELECT
        COALESCE(NEW.total_expected, 0),
        COALESCE(NEW.total_paid, 0),
        COALESCE(NEW.total_pt_pay, 0),
        CASE WHEN COALESCE(NEW.revenue_accepted_posted, 'No') = 'Yes' THEN TRUE ELSE FALSE END
    INTO
        v_total_expected,
        v_total_paid,
        v_total_pt_pay,
        v_revenue_accepted_posted
    FROM form_billing_invoice
    WHERE id = NEW.id;

    -- Determine if charges are editable based on claim status (if available)
    SELECT
        CASE
            WHEN ncpdp.status IN ('Sent', 'Rejected', 'Reversed', 'Benefit', 'Approved', 'PA Deferred', 'Duplicate') OR ncpdp.status IS NULL THEN TRUE
            ELSE FALSE
        END
    INTO v_charges_editable
    FROM form_ncpdp ncpdp
    INNER JOIN sf_form_billing_invoice_to_ncpdp sfbi
        ON sfbi.form_ncpdp_fk = ncpdp.id
        AND sfbi.form_billing_invoice_fk = NEW.id
        AND sfbi.archive IS NOT TRUE
        AND sfbi.delete IS NOT TRUE
    WHERE ncpdp.archived IS NOT TRUE
    AND ncpdp.deleted IS NOT TRUE
    AND COALESCE(ncpdp.void, 'No') <> 'Yes'
    LIMIT 1;

    -- If no NCPDP claim status is found, default to editable for non-posted revenue
    IF v_charges_editable IS NULL THEN
        v_charges_editable := TRUE;
    END IF;

    -- Distribute proportions across charge lines if revenue is not posted
    IF v_revenue_accepted_posted IS FALSE THEN
        PERFORM set_config('clara.disable_invoice_trigger', 'on', false);
        PERFORM set_config('clara.prevent_locked_checks', 'on', false);
        FOR v_charge_record IN
            SELECT
                cl.id,
                cl.charge_no,
                cl.bill_quantity,
                COALESCE(arb.proportional_expected_amount, 0)::numeric AS expected_proportion,
                (COALESCE(arb.proportional_expected_amount,0) - COALESCE(arb.pending_ar_balance, 0))::numeric AS paid_proportion,
                COALESCE(arb.pending_copay_balance, 0)::numeric AS copay_proportion,
                COALESCE(arb.pending_ar_balance, 0)::numeric AS balance_portion
            FROM form_ledger_charge_line cl
            LEFT JOIN vw_charge_line_pending_ar_balance arb
                ON arb.charge_line_id = cl.id
            WHERE cl.invoice_no = NEW.invoice_no
            AND (cl.archived IS NULL OR cl.archived = FALSE)
            AND (cl.deleted IS NULL OR cl.deleted = FALSE)
            AND COALESCE(cl.void, 'No') <> 'Yes'
            AND COALESCE(cl.zeroed, 'No') <> 'Yes'
            ORDER BY cl.expected DESC
        LOOP
            -- Calculate proportional expected amount using view proportion
            v_line_expected_amount := ROUND(v_charge_record.expected_proportion, 2);

            -- Calculate proportional paid amount using view proportion
            v_line_paid_amount := ROUND(v_charge_record.paid_proportion, 2);

            -- Calculate proportional copay amount using view proportion
            v_line_copay_amount := ROUND(v_charge_record.copay_proportion, 2);

            -- Calculate proportional balance amount using view proportion
            v_line_balance_amount := ROUND(v_charge_record.balance_portion, 2);

            -- Update charge line with proportional values
            UPDATE form_ledger_charge_line
            SET
                invoice_status = NEW.status,
                expected = v_line_expected_amount,
                calc_expected_ea = CASE
                    WHEN v_charge_record.bill_quantity > 0 THEN ROUND(v_line_expected_amount::numeric / v_charge_record.bill_quantity::numeric, 4)::numeric
                    ELSE 0::numeric
                END,
                paid = v_line_paid_amount,
                copay = v_line_copay_amount,
                total_balance_due = v_line_balance_amount,
                locked = CASE WHEN v_charges_editable IS TRUE THEN NULL::text ELSE 'Yes'::text END,
                locked_by = CASE WHEN v_charges_editable IS TRUE THEN NULL::integer ELSE NEW.updated_by END,
                locked_datetime = CASE WHEN v_charges_editable IS TRUE THEN NULL::timestamp ELSE CURRENT_TIMESTAMP END
            WHERE id = v_charge_record.id
            AND COALESCE(void, 'No') <> 'Yes'
            AND COALESCE(zeroed, 'No') <> 'Yes'
            AND (COALESCE(locked, 'No') = 'No' OR v_charges_editable IS TRUE);

            RAISE LOG 'Updated charge line % with expected: %, paid: %, copay: %', 
                      v_charge_record.charge_no, v_line_expected_amount, v_line_paid_amount, v_line_copay_amount;
        END LOOP;
        PERFORM set_config('clara.disable_invoice_trigger', 'off', false);
        PERFORM set_config('clara.prevent_locked_checks', 'off', false);
        RAISE LOG 'Completed proportional split for invoice: % using vw_charge_line_pending_ar_balance', NEW.invoice_no;
    END IF;

    RETURN NEW;

EXCEPTION WHEN OTHERS THEN
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        SQLERRM,
        'Transaction failed during billing invoice split proportion',
        'TRIGGER',
        current_schema(),
        'form_billing_invoice',
        jsonb_build_object(
            'function_name', 'process_billing_invoice_split_proportion',
            'invoice_no', NEW.invoice_no
        )
    );
    RAISE NOTICE 'Error processing billing invoice split proportion: %', SQLERRM;
    RAISE;
END;
$$ LANGUAGE plpgsql VOLATILE;

DROP TRIGGER IF EXISTS process_billing_invoice_split_proportion_trigger ON form_billing_invoice;
CREATE CONSTRAINT TRIGGER process_billing_invoice_split_proportion_trigger
    AFTER INSERT OR UPDATE ON form_billing_invoice
    DEFERRABLE INITIALLY DEFERRED
    FOR EACH ROW
    WHEN (COALESCE(NEW.revenue_accepted_posted, 'No') <> 'Yes')
    EXECUTE FUNCTION process_billing_invoice_split_proportion(); 