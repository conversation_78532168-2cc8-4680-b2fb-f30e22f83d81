
CREATE OR REPLACE VIEW vw_patient_invoice_information AS 
SELECT
	COALESCE(last_invoice.patient_name, CONCAT(pt.firstname, ' ', pt.lastname)) as patient_name,
	COALESCE(last_invoice.patient_address, TRIM(CONCAT(ptpa.street,' ',ptpa.street2))) as patient_address,
	COALESCE(last_invoice.patient_city, ptpa.city) as patient_city,
	COALESCE(last_invoice.patient_state, ptpa.state_id) as patient_state,
	COALESCE(last_invoice.patient_zip, ptpa.zip) as patient_zip,
	COALESCE(last_invoice.responsible_party, CONCAT(pt.firstname, ' ', pt.lastname)) as responsible_party,
	COALESCE(last_invoice.send_bill_to, CONCAT(ptpa.street,' ',ptpa.street2)) as send_bill_to,
	COALESCE(last_invoice.billing_city, ptpa.city) as billing_city,
	COALESCE(last_invoice.billing_state, ptpa.state_id) as billing_state,
	COALESCE(last_invoice.billing_zip, ptpa.zip) as billing_zip,
	COALESCE(last_invoice.policy_no, pi.policy_number) as policy_no,
	COALESCE(last_invoice.billing_group_no, pi.group_number) as billing_group_no,
	last_invoice.prior_auth_no as prior_auth_no,
	COALESCE(last_invoice.insured_name, CONCAT(pt.firstname, ' ', pt.lastname)) as insured_name,
	COALESCE(last_invoice.insured_address, CONCAT(ptpa.street,' ',ptpa.street2)) as insured_address,
	COALESCE(last_invoice.insured_city, ptpa.city) as insured_city,
	COALESCE(last_invoice.insured_state, ptpa.state_id) as insured_state,
	COALESCE(last_invoice.insured_zip, ptpa.zip) as insured_zip,
	COALESCE(last_invoice.due_days, cp.default_due_days) as due_days,
	COALESCE(last_invoice.services_provided, cp.default_services_provided) as services_provided,
	COALESCE(last_invoice.free_text, cp.default_free_text) as free_text,
	COALESCE(last_invoice.terms, cp.default_terms) as terms,
	pt.id as patient_id,
    pi.id as insurance_id,
    py.id as payer_id
	FROM form_patient pt
    INNER JOIN form_patient_insurance pi ON pi.patient_id = pt.id 
    AND pi.archived IS NOT TRUE 
    AND pi.deleted IS NOT TRUE
    AND pi.active = 'Yes'
    INNER JOIN form_payer py ON py.id = pi.payer_id 
    AND py.archived IS NOT TRUE 
    AND py.deleted IS NOT TRUE
    AND py.billing_method_id = 'generic'
    INNER JOIN form_company cp ON cp.id = 1
    LEFT JOIN LATERAL(
        SELECT 
            street,
            street2,
            city,
            state_id,
            zip
        FROM form_patient_address pa 
        WHERE pa.patient_id = pt.id 
            AND pa.archived IS NOT TRUE 
            AND pa.deleted IS NOT TRUE
        ORDER BY 
            CASE
            WHEN address_type = 'Home' THEN 0
            WHEN address_type = 'Shipping' THEN 1 
            ELSE 2
            END 
        LIMIT 1 
    ) ptpa ON TRUE
    LEFT JOIN LATERAL (
        SELECT responsible_party,
        send_bill_to,
        patient_name,
        patient_address,
        patient_city,
        patient_state,
        patient_zip,
        billing_city,
        billing_state,
        billing_zip,
        policy_no,
        billing_group_no,
        prior_auth_no,
        insured_name,
        insured_address,
        insured_city,
        insured_state,
        insured_zip,
        due_days,
        services_provided,
        free_text,
        terms
        FROM form_billing_invoice bi
        WHERE bi.patient_id = pt.id
        AND bi.payer_id = 1
        ORDER BY bi.created_on DESC
        LIMIT 1
    ) last_invoice ON TRUE;

CREATE OR REPLACE VIEW vw_unapplied_cash_base AS
WITH unapplied_cash AS (
    SELECT DISTINCT ON (lf.account_id)
        lf.id AS ledger_finance_id,
        lf.account_id,
        lf.source_id,
        lf.source_form,
        lf.transaction_datetime,
        lf.post_datetime,
        lf.created_by,
        ROUND(COALESCE(lf.credit, 0)::numeric - COALESCE(lf.debit, 0)::numeric, 2) AS amount_raw
    FROM
        form_ledger_finance lf
    WHERE
        lf.account_type = 'Unapplied Cash'
    ORDER BY
        lf.account_id, lf.transaction_datetime DESC
)
SELECT
    uc.ledger_finance_id,
    uc.account_id,
    ba.id as billing_account_id,
    ba.type as account_type,
    CASE
        WHEN ba.type = 'Patient' THEN pt.id
        ELSE NULL
    END AS patient_id,
    CASE
        WHEN ba.type = 'Payer' THEN py.id
        ELSE NULL
    END AS payer_id,
    CASE
        WHEN bc.site_id IS NOT NULL THEN bc.site_id
        ELSE NULL
    END AS site_id,
    uc.post_datetime,
    uc.amount_raw,
    bc.check_no,
    bc.payment_method,
    CASE
        WHEN bc.payment_method = 'Other' THEN bc.other_payment_method
        ELSE NULL
    END as other_payment_method,
    uc.created_by
FROM
    unapplied_cash uc
JOIN 
    form_billing_account ba ON ba.id = uc.account_id
LEFT JOIN 
    form_patient pt ON pt.id = ba.patient_id AND ba.type = 'Patient'
LEFT JOIN 
    form_payer py ON py.id = ba.payer_id AND ba.type = 'Payer'
LEFT JOIN 
    form_billing_cash bc ON bc.id = uc.source_id AND uc.source_form = 'billing_cash'
WHERE
    ba.archived IS NOT TRUE
    AND ba.deleted IS NOT TRUE
    AND (
        (ba.type = 'Patient' AND pt.archived IS NOT TRUE AND pt.deleted IS NOT TRUE) OR
        (ba.type = 'Payer' AND py.archived IS NOT TRUE AND py.deleted IS NOT TRUE)
    );

CREATE OR REPLACE VIEW vw_unapplied_cash_balance AS 
SELECT 
    account.account_id,
    ROUND(COALESCE(SUM(
        account.amount_raw
    ), 0), 2) AS unapplied_cash_balance,
	account.account_type,
	account.payer_id,
	account.patient_id
	FROM 
    vw_unapplied_cash_base account
GROUP BY 
    account.account_id, account.account_type, account.payer_id, account.patient_id;

CREATE OR REPLACE VIEW vw_invoice_highest_priced_item_info AS
SELECT DISTINCT ON (lgl.invoice_no)
  lgl.invoice_no,
  invi.name AS pri_item,
  invi.id AS inventory_id,
  invi.brand_name_id,
  cor.therapy_id,
  lt.name AS therapy,
  ph.id AS physician_id,
  ph.last AS physician_last_name,
  ph.first AS physician_first_name,
  ph.title AS physician_title,
  TRIM(CONCAT(ph.last, ', ', ph.first, ' ', ph.title)) AS physician,
  pl.state_id AS physician_state_id,
  pl.zip AS physician_zip,
  pl.city AS physician_city,
  lgl.id AS charge_line_id
FROM form_ledger_charge_line lgl
INNER JOIN form_inventory invi ON invi.id = lgl.inventory_id
LEFT JOIN form_careplan_order_rx cor ON cor.id = lgl.order_rx_id
LEFT JOIN form_list_therapy lt ON lt.code = cor.therapy_id
LEFT JOIN form_physician ph ON ph.id = cor.physician_id
LEFT JOIN LATERAL (
  SELECT
    pl.city,
    pl.state_id,
    pl.zip
  FROM sf_form_physician_to_physician_location spl
  INNER JOIN form_physician_location pl
    ON pl.id = spl.form_physician_location_fk
  WHERE spl.form_physician_fk = ph.id
    AND spl.archive IS NOT TRUE
    AND spl."delete" IS NOT TRUE
    AND pl.archived IS NOT TRUE
    AND pl.deleted IS NOT TRUE
  ORDER BY 
    CASE WHEN pl.is_primary = 'Yes' THEN 1 ELSE 0 END,
    pl.created_on DESC
  LIMIT 1
) AS pl ON true
WHERE lgl.archived IS NOT TRUE
  AND lgl.deleted IS NOT TRUE
  AND COALESCE(lgl.void, 'No') <> 'Yes'
  AND COALESCE(lgl.zeroed, 'No') <> 'Yes'
ORDER BY lgl.invoice_no, lgl.expected DESC;

CREATE OR REPLACE VIEW vw_ledger_finance_metrics AS
WITH cogs_lookup AS (
    -- Get COGS for each invoice/charge_line combination
    SELECT 
        invoice_id,
        charge_line_id,
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'COGS'
            THEN (COALESCE(debit, 0)::numeric - COALESCE(credit, 0)::numeric)
            ELSE 0::numeric
        END), 0), 2) AS cogs_amount
    FROM form_ledger_finance
    WHERE account_type = 'COGS'
    GROUP BY invoice_id, charge_line_id
),
ticket_item_cogs AS (
    -- Get COGS from ticket items
    SELECT
        lcl.id AS charge_line_id,
        lcl.invoice_no,
        bi.id AS invoice_id,
        ROUND(COALESCE(
            -- Serial items
            (SELECT SUM(lf.debit - lf.credit)::numeric
             FROM form_ledger_finance lf
             JOIN form_ledger_serial ls ON ls.id = lf.ledger_serial_id
             JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = ls.ticket_item_no 
                AND pld.serial_id IS NOT NULL
             WHERE lf.ledger_serial_id IS NOT NULL
               AND lf.account_type = 'COGS'
               AND lf.transaction_type = 'Dispense'
               AND pld.ticket_item_no = lcl.ticket_item_no),
            
            -- Lot items
            (SELECT SUM(lf.debit - lf.credit)::numeric
             FROM form_ledger_finance lf
             JOIN form_ledger_lot ll ON ll.id = lf.ledger_lot_id
             JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = ll.ticket_item_no 
                AND pld.lot_id IS NOT NULL 
                AND pld.serial_id IS NULL
             WHERE lf.ledger_lot_id IS NOT NULL
               AND lf.account_type = 'COGS'
               AND lf.transaction_type = 'Dispense'
               AND pld.ticket_item_no = lcl.ticket_item_no),
               
            -- Inventory items
            (SELECT SUM(lf.debit - lf.credit)::numeric
             FROM form_ledger_finance lf
             JOIN form_ledger_inventory li ON li.id = lf.ledger_inventory_id
             JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = li.ticket_item_no 
                AND pld.inventory_id = li.inventory_id 
                AND pld.lot_id IS NULL 
                AND pld.serial_id IS NULL
             WHERE lf.ledger_inventory_id IS NOT NULL
               AND lf.account_type = 'COGS'
               AND lf.transaction_type = 'Dispense'
               AND pld.ticket_item_no = lcl.ticket_item_no),
               
            0
        ), 2) AS cogs_amount
    FROM form_ledger_charge_line lcl
    JOIN form_billing_invoice bi ON bi.invoice_no = lcl.invoice_no
    WHERE lcl.ticket_item_no IS NOT NULL
      AND lcl.archived IS NOT TRUE
      AND lcl.deleted IS NOT TRUE
),
combined_cogs AS (
    -- Combine direct and ticket item COGS
    SELECT 
        COALESCE(cl.invoice_id, tic.invoice_id) AS invoice_id,
        COALESCE(cl.charge_line_id, tic.charge_line_id) AS charge_line_id,
        CASE
            WHEN cl.cogs_amount > 0 THEN cl.cogs_amount
            WHEN tic.cogs_amount > 0 THEN tic.cogs_amount
            ELSE 0
        END AS cogs_amount
    FROM cogs_lookup cl
    FULL OUTER JOIN ticket_item_cogs tic 
        ON tic.charge_line_id = cl.charge_line_id 
        AND tic.invoice_id = cl.invoice_id
)
SELECT
    lf.id,
    REPLACE(lf.source_form, 'form_', '') AS form_name,
    lf.source_id AS form_id,
    COALESCE(pt.id, pt_cl.id) AS patient_id,
    COALESCE(pt.auto_name, pt_cl.auto_name) as patient_id_auto_name,
    COALESCE(py.id, py_cl.id) AS payer_id,
    COALESCE(py.auto_name, py_cl.auto_name) as payer_id_auto_name,
    COALESCE(inv.site_id, lf.site_id) AS site_id,
    COALESCE(pt.lastname, pt_cl.lastname) AS "Last Name",
    COALESCE(pt.firstname, pt_cl.firstname) AS "First Name",
    COALESCE(pt.mrn, pt_cl.mrn) as "MRN",
    COALESCE(py.organization, py_cl.organization) as "Payer",
    hpi.pri_item as "Pri Item",
    lf.account_id,
    lf.charge_line_id,
    lf.charge_no,
    lf.invoice_id,
    lf.invoice_no as "Invoice No",
    lf.transaction_datetime,
    lf.post_datetime,
    lf.transaction_type AS source,
    hpi.inventory_id,
    hpi.brand_name_id,
    TO_CHAR(lf.transaction_datetime, 'MM/DD/YYYY HH:MI AM') AS "Transaction Date/Time",
    TO_CHAR(lf.post_datetime, 'MM/DD/YYYY HH:MI AM') AS "Posted Date/Time",
    lf.account_type AS "Type",
    CASE
        WHEN lf.account_type IN ('Revenue', 'Unapplied Cash') THEN COALESCE(lf.credit, 0) - COALESCE(lf.debit, 0)
        ELSE COALESCE(lf.debit, 0) - COALESCE(lf.credit, 0)
    END AS amount_raw,
    format_currency(
        CASE
            WHEN lf.account_type IN ('Revenue', 'Unapplied Cash') THEN COALESCE(lf.credit, 0) - COALESCE(lf.debit, 0)
            ELSE COALESCE(lf.debit, 0) - COALESCE(lf.credit, 0)
        END::numeric
    ) AS "Amount",
    CASE
        WHEN lf.account_type = 'AR' THEN COALESCE(lf.debit, 0)
        ELSE COALESCE(lf.debit, 0)
    END AS expected_raw,
    format_currency(
        CASE
            WHEN lf.account_type = 'AR' THEN COALESCE(lf.debit, 0)
            ELSE COALESCE(lf.debit, 0)
        END::numeric
    ) AS "Expected",
    COALESCE(cc.cogs_amount, 0) AS cost_raw,
    format_currency(
        COALESCE(cc.cogs_amount, 0)::numeric
    ) AS "Cost",
    CASE
        WHEN lf.source_form = 'billing_invoice' AND 
             COALESCE(cl.expected, CASE WHEN lf.account_type = 'Revenue' THEN COALESCE(lf.credit, 0)::numeric - COALESCE(lf.debit, 0)::numeric ELSE 0::numeric END) > 0 THEN
            ROUND(((COALESCE(cl.expected, CASE WHEN lf.account_type = 'Revenue' THEN COALESCE(lf.credit, 0)::numeric - COALESCE(lf.debit, 0)::numeric ELSE 0::numeric END) - 
                   COALESCE(cc.cogs_amount, 0)) / 
                   NULLIF(COALESCE(cl.expected, CASE WHEN lf.account_type = 'Revenue' THEN COALESCE(lf.credit, 0)::numeric - COALESCE(lf.debit, 0)::numeric ELSE 0::numeric END), 0)::numeric * 100)::numeric, 2)
        ELSE NULL
    END AS profit_margin_raw,
    CASE
        WHEN lf.source_form = 'billing_invoice' AND 
             COALESCE(cl.expected, CASE WHEN lf.account_type = 'Revenue' THEN COALESCE(lf.credit, 0)::numeric - COALESCE(lf.debit, 0)::numeric ELSE 0::numeric END) > 0 THEN
            CONCAT(format_numeric(ROUND(((COALESCE(cl.expected, CASE WHEN lf.account_type = 'Revenue' THEN COALESCE(lf.credit, 0)::numeric - COALESCE(lf.debit, 0)::numeric ELSE 0::numeric END) - 
                                  COALESCE(cc.cogs_amount, 0)) / 
                                  NULLIF(COALESCE(cl.expected, CASE WHEN lf.account_type = 'Revenue' THEN COALESCE(lf.credit, 0)::numeric - COALESCE(lf.debit, 0)::numeric ELSE 0::numeric END), 0)::numeric * 100)::numeric, 2)::numeric), '%')
        ELSE NULL
    END AS "Profit Margin",
    CASE
        WHEN lf.source_form = 'billing_invoice' THEN
            ROUND((COALESCE(cl.expected, CASE WHEN lf.account_type = 'Revenue' THEN COALESCE(lf.credit, 0)::numeric - COALESCE(lf.debit, 0)::numeric ELSE 0::numeric END) - 
                  COALESCE(cc.cogs_amount, 0))::numeric, 2)
        ELSE NULL
    END AS profit_raw,
    CASE
        WHEN lf.source_form = 'billing_invoice' THEN
            format_currency((COALESCE(cl.expected, CASE WHEN lf.account_type = 'Revenue' THEN COALESCE(lf.credit, 0)::numeric - COALESCE(lf.debit, 0)::numeric ELSE 0::numeric END) - 
                    COALESCE(cc.cogs_amount, 0))::numeric)
        ELSE NULL
    END AS "Profit",
    CASE
        WHEN inv.zeroed = 'Yes' THEN '#FCB6B6'
        WHEN lf.transaction_type = 'Writeoff' THEN '#FCB6B6'
        WHEN lf.transaction_type = 'Adjustment' AND lf.credit::numeric > 0 THEN '#FACAA5'
        ELSE NULL
    END AS __row_color,
    lf.account_type,
    lf.debit,
    lf.credit,
    lf.transaction_type,
    lf.quantity,
    lf.po_id,
    lf.ledger_inventory_id,
    lf.ledger_lot_id,
    lf.ledger_serial_id,
    lf.notes
FROM
    form_ledger_finance lf
LEFT JOIN
    form_ledger_charge_line cl ON cl.id = lf.charge_line_id
LEFT JOIN
    combined_cogs cc ON cc.charge_line_id = lf.charge_line_id AND cc.invoice_id = lf.invoice_id
LEFT JOIN
    form_billing_invoice inv ON (inv.id = lf.invoice_id)
LEFT JOIN 
    form_patient pt ON pt.id = inv.patient_id
LEFT JOIN 
    form_payer py ON py.id = inv.payer_id
LEFT JOIN
    form_patient pt_cl ON pt_cl.id = cl.patient_id
LEFT JOIN
    form_patient_insurance pi_cl ON pi_cl.id = cl.insurance_id
LEFT JOIN
    form_payer py_cl ON py_cl.id = pi_cl.payer_id
LEFT JOIN vw_invoice_highest_priced_item_info hpi ON hpi.invoice_no = lf.invoice_no;

CREATE INDEX IF NOT EXISTS idx_charge_line_invoice_expected_desc
ON form_ledger_charge_line (invoice_no, expected DESC)
WHERE archived IS NOT TRUE AND deleted IS NOT TRUE;

CREATE OR REPLACE VIEW vw_master_charge_line_drill_down AS
WITH charge_line_finance AS (
    -- Get finance data for each charge line
    SELECT
        charge_line_id,
        lcl.ticket_item_no,
        -- Expected amount (total AR debits)
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'AR'
            THEN COALESCE(debit, 0)::numeric
            ELSE 0::numeric
        END), 0::numeric)::numeric, 2) AS expected_amount,
        
        -- Cash payments with reversal handling
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'Cash'
            THEN (COALESCE(debit, 0)::numeric - COALESCE(credit, 0)::numeric)
            ELSE 0::numeric
        END), 0::numeric)::numeric, 2) AS cash_paid,
        
        -- Adjustments and writeoffs with reversal handling
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'AR' AND transaction_type IN ('Adjustment', 'Adjustment Reversal', 'Writeoff', 'Writeoff Reversal')
            THEN (COALESCE(credit, 0)::numeric - COALESCE(debit, 0)::numeric)
            ELSE 0::numeric
        END), 0::numeric), 2) AS net_adjustment,
        
        -- Balance calculation
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'AR'
            THEN (COALESCE(debit, 0)::numeric - COALESCE(credit, 0)::numeric)
            ELSE 0::numeric
        END), 0::numeric)::numeric, 2) AS balance_due,
        
        MAX(transaction_datetime) as last_transaction_datetime
    FROM form_ledger_finance lf
    JOIN form_ledger_charge_line lcl ON lcl.id = lf.charge_line_id
    WHERE charge_line_id IS NOT NULL
    GROUP BY charge_line_id, lcl.ticket_item_no
),
-- Get COGS directly from ledger_finance for charge_line_id where available
direct_cogs AS (
    SELECT 
        charge_line_id,
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'COGS'
            THEN (COALESCE(debit, 0)::numeric - COALESCE(credit, 0)::numeric)
            ELSE 0::numeric
        END), 0::numeric), 2) AS cogs_amount
    FROM form_ledger_finance
    WHERE charge_line_id IS NOT NULL
    GROUP BY charge_line_id
),
-- For charge lines without direct COGS entries, get COGS from ticket items
ticket_item_cogs AS (
    SELECT
        clf.charge_line_id,
        ROUND(COALESCE(
            -- Get COGS from serial items
            (SELECT SUM(lf.debit - lf.credit)::numeric
             FROM form_ledger_finance lf
             JOIN form_ledger_serial ls ON ls.id = lf.ledger_serial_id
             JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = ls.ticket_item_no 
                AND pld.serial_id IS NOT NULL
             WHERE lf.ledger_serial_id IS NOT NULL
               AND lf.account_type = 'COGS'
               AND lf.transaction_type = 'Dispense'
               AND pld.ticket_item_no = clf.ticket_item_no),
            
            -- Get COGS from lot items
            (SELECT SUM(lf.debit - lf.credit)::numeric
             FROM form_ledger_finance lf
             JOIN form_ledger_lot ll ON ll.id = lf.ledger_lot_id
             JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = ll.ticket_item_no 
                AND pld.lot_id IS NOT NULL 
                AND pld.serial_id IS NULL
             WHERE lf.ledger_lot_id IS NOT NULL
               AND lf.account_type = 'COGS'
               AND lf.transaction_type = 'Dispense'
               AND pld.ticket_item_no = clf.ticket_item_no),
               
            -- Get COGS from inventory items
            (SELECT SUM(lf.debit - lf.credit)::numeric
             FROM form_ledger_finance lf
             JOIN form_ledger_inventory li ON li.id = lf.ledger_inventory_id
             JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = li.ticket_item_no 
                AND pld.inventory_id = li.inventory_id 
                AND pld.lot_id IS NULL 
                AND pld.serial_id IS NULL
             WHERE lf.ledger_inventory_id IS NOT NULL
               AND lf.account_type = 'COGS'
               AND lf.transaction_type = 'Dispense'
               AND pld.ticket_item_no = clf.ticket_item_no),
               
            0
        ), 2) AS cogs_amount
    FROM charge_line_finance clf
    WHERE clf.ticket_item_no IS NOT NULL
),
-- Combine direct COGS with ticket item COGS
combined_cogs AS (
    SELECT 
        COALESCE(dc.charge_line_id, tic.charge_line_id) AS charge_line_id,
        CASE
            WHEN dc.cogs_amount > 0 THEN dc.cogs_amount
            WHEN tic.cogs_amount > 0 THEN tic.cogs_amount
            ELSE 0
        END AS cogs_amount
    FROM direct_cogs dc
    FULL OUTER JOIN ticket_item_cogs tic ON tic.charge_line_id = dc.charge_line_id
)
SELECT
    lcl.id,
    lcl.insurance_id,
    lcl.payer_id,
    lcl.invoice_no,
    lcl.pricing_source as pricing_source_raw,
    CASE
        WHEN lcl.pricing_source = 'part_b_asp' THEN 'MCR Part B ASP'
        WHEN lcl.pricing_source = 'part_b_dme' THEN 'MCR Part B DMEPOS'
        WHEN lcl.pricing_source = 'assigned_matrix' THEN 'Shared Contract Rate'
        WHEN lcl.pricing_source = 'matrix' THEN 'Payer Contracted Rate'
        WHEN lcl.pricing_source = 'list' THEN 'List Price'
        WHEN lcl.pricing_source = 'cob_override' THEN 'COB Amount'
        ELSE NULL
    END as pricing_source,
    
    -- Quantity fields
    lcl.charge_quantity as charge_quantity_raw,
    format_numeric(lcl.charge_quantity::numeric) AS "Charge Quantity",
    lcl.charge_unit,
    COALESCE(lcl.bill_quantity, 0.0) as bill_quantity_raw,
    format_numeric(COALESCE(lcl.bill_quantity, 0.0)::numeric) AS "Bill Quantity",
    lcl.hcpc_quantity,
    lcl.hcpc_unit,
    lcl.metric_quantity,
    -- Contract fields
    lcl.shared_contract_id,
    pc.name as "Shared Contract",
    
    -- Financial fields
    COALESCE(clf.expected_amount, lcl.expected, 0.0) as expected_raw,
    format_currency(COALESCE(clf.expected_amount, lcl.expected, 0.0)::numeric) AS "Expected",
    
    lcl.billed as billed_raw,
    format_currency(lcl.billed::numeric) AS "Billed",
    
    COALESCE(cc.cogs_amount, 0.0) as cost_raw,
    format_currency(COALESCE(cc.cogs_amount, 0.0)::numeric) AS "Cost",
    
    COALESCE(clf.cash_paid, 0.0) as paid_raw,
    format_currency(COALESCE(clf.cash_paid, 0.0)::numeric) AS "Paid",
    
    COALESCE(clf.net_adjustment, 0.0) as adjustment_raw,
    format_currency(COALESCE(clf.net_adjustment, 0.0)::numeric) AS "Adjustments",
    
    COALESCE(clf.balance_due, 0.0) as balance_due_raw,
    format_currency(COALESCE(clf.balance_due, 0.0)::numeric) AS "Balance",
    
    -- Profit calculations
    (COALESCE(clf.expected_amount, lcl.expected, 0.0) - COALESCE(cc.cogs_amount, 0.0)) as profit_raw,
    format_currency((COALESCE(clf.expected_amount, lcl.expected, 0.0) - COALESCE(cc.cogs_amount, 0.0))::numeric) AS "Profit",
    
    CASE 
        WHEN COALESCE(clf.expected_amount, lcl.expected, 0.0) > 0 THEN
            ROUND(((COALESCE(clf.expected_amount, lcl.expected, 0.0)::numeric - COALESCE(cc.cogs_amount, 0.0)::numeric) / 
                  COALESCE(clf.expected_amount, lcl.expected, 0.0)::numeric * 100)::numeric, 2)
        ELSE NULL
    END as margin_raw,
    
    CASE 
        WHEN COALESCE(clf.expected_amount, lcl.expected, 0.0) > 0 THEN
            CONCAT(format_numeric(ROUND(((COALESCE(clf.expected_amount, lcl.expected, 0.0)::numeric - COALESCE(cc.cogs_amount, 0.0)::numeric) / 
                  COALESCE(clf.expected_amount, lcl.expected, 0.0)::numeric * 100)::numeric, 2)::numeric), '%')
        ELSE NULL
    END AS "Margin",
    
    -- Other financial fields
    COALESCE(lcl.pt_pd_amt_sub, 0.0) as pt_pd_amt_sub_raw,
    format_currency(COALESCE(lcl.pt_pd_amt_sub, 0.0)::numeric) AS "Pt/PD Amount Sub",
    
    COALESCE(lcl.list_price, 0.0) as list_price_raw,
    format_currency(COALESCE(lcl.list_price, 0.0)::numeric) AS "List Price",
    
    COALESCE(lcl.copay, 0.0) as copay_raw,
    format_currency(COALESCE(lcl.copay, 0.0)::numeric) AS "Copay",
    
    -- Reference fields
    lcl.patient_id,
    lcl.order_rx_id,
    lcl.claim_no,
    lcl.calc_invoice_split_no,
    lcl.fill_number,
    lcl.close_no,
    lcl.encounter_id,
    lcl.ticket_no,
    lcl.ticket_item_no,
    lcl.compound_no,
    lcl.rx_no,
    lcl.inventory_id,
    inv.name as "Item",
    lcl.inventory_type,
    lcl.hcpc_code,
    lcl.formatted_ndc as ndc,
    lcl.revenue_code_id,
    rc.name as revenue_code,
    
    -- Status indicators
    CASE 
        WHEN COALESCE(clf.balance_due, 0.0) <= 0 THEN 'Paid'
        WHEN COALESCE(clf.cash_paid, 0.0) > 0 THEN 'Partially Paid'
        ELSE 'Unpaid'
    END as payment_status,
    
    -- Row coloring
    CASE 
        WHEN COALESCE(clf.balance_due, 0.0) <= 0 THEN '#E1FAF2'
        WHEN COALESCE(clf.cash_paid, 0.0) > 0 THEN '#FFEAD9'
        ELSE NULL
    END as __row_color,
    
    -- Transaction datetime
    clf.last_transaction_datetime,
    lcl.charge_no
FROM form_ledger_charge_line lcl
LEFT JOIN charge_line_finance clf ON clf.charge_line_id = lcl.id
LEFT JOIN combined_cogs cc ON cc.charge_line_id = lcl.id
LEFT JOIN form_list_revenue_code rc ON rc.code = lcl.revenue_code_id
LEFT JOIN form_payer_contract pc ON pc.id = lcl.shared_contract_id
INNER JOIN form_inventory inv ON inv.id = lcl.inventory_id
WHERE lcl.archived IS NOT TRUE
AND lcl.deleted IS NOT TRUE
AND COALESCE(lcl.void, 'No') <> 'Yes'
AND COALESCE(lcl.zeroed, 'No') <> 'Yes';

-- Create index to improve performance
CREATE INDEX IF NOT EXISTS idx_ledger_finance_charge_line_account 
ON form_ledger_finance(charge_line_id, account_type)
WHERE charge_line_id IS NOT NULL;


CREATE OR REPLACE VIEW vw_ncpdp_claim_response AS
WITH latest_response AS (
  SELECT DISTINCT ON (resp.claim_no)
    resp.*
  FROM form_ncpdp_response resp
  WHERE resp.archived IS NOT TRUE
    AND resp.deleted IS NOT TRUE
  ORDER BY resp.claim_no, resp.id DESC
),
latest_stat AS (
  SELECT DISTINCT ON (sf.form_ncpdp_response_fk)
    sf.form_ncpdp_response_fk,
    stat.transaction_response_status
  FROM sf_form_ncpdp_response_to_ncpdp_response_stat sf
  JOIN form_ncpdp_response_stat stat ON stat.id = sf.form_ncpdp_response_stat_fk
  WHERE sf.archive IS NOT TRUE
    AND sf.delete IS NOT TRUE
    AND stat.archived IS NOT TRUE
    AND stat.deleted IS NOT TRUE
  ORDER BY sf.form_ncpdp_response_fk, stat.id DESC
),
latest_prc AS (
  SELECT DISTINCT ON (sf.form_ncpdp_response_fk)
    sf.form_ncpdp_response_fk,
    rprc.*
  FROM sf_form_ncpdp_response_to_ncpdp_response_prc sf
  JOIN form_ncpdp_response_prc rprc ON rprc.id = sf.form_ncpdp_response_prc_fk
  WHERE sf.archive IS NOT TRUE
    AND sf.delete IS NOT TRUE
    AND rprc.archived IS NOT TRUE
    AND rprc.deleted IS NOT TRUE
  ORDER BY sf.form_ncpdp_response_fk, rprc.id DESC
)
SELECT
  claim.patient_id::integer as patient_id,
  claim.payer_id::integer as payer_id,
  claim.site_id::integer as site_id,
  claim.id::integer AS ncpdp_id,
  claim.claim_no::text as claim_no,
  claim.invoice_no::text as invoice_no,
  claim.transaction_code::text as transaction_code,
  bi.master_invoice_no::text as master_invoice_no,
  stat.transaction_response_status::text as transaction_response_status,
  resp.response_status::text as response_status,
  prc.ing_cst_sub::numeric as ing_cst_sub,
  prc.disp_fee_sub::numeric as disp_fee_sub,
  prc.pro_svc_fee_sub::numeric as pro_svc_fee_sub,
  prc.incv_amt_sub::numeric as incv_amt_sub,
  prc.pt_pd_amt_sub::numeric as pt_pd_amt_sub,
  prc.u_and_c_charge::numeric as u_and_c_charge,
  prc.gross_amount_due::numeric as gross_amount_due,
  prc.cost_basis::numeric as cost_basis,
  rprc.total_paid::numeric as total_paid,
  rprc.pt_pay_amt::numeric as pt_pay_amt,
  rprc.ing_cst_paid::numeric as ing_cst_paid,
  rprc.disp_fee_paid::numeric as disp_fee_paid,
  rprc.amt_disp_fee_contracted::numeric as amt_disp_fee_contracted,
  rprc.incv_amt_paid::numeric as incv_amt_paid,
  rprc.pro_fee_amt::numeric as pro_fee_amt,
  rprc.amt_coverage_gap::numeric as amt_coverage_gap,
  rprc.coinsur_amt::numeric as coinsur_amt,
  rprc.acc_ded_amt::numeric as acc_ded_amt,
  rprc.rem_ded_amt::numeric as rem_ded_amt,
  rprc.amt_apld_ded::numeric as amt_apld_ded,
  rprc.amt_copay::numeric as amt_copay,
  rprc.hlth_pln_asst::numeric as hlth_pln_asst,
  rprc.est_gen_savings::numeric as est_gen_savings,
  rprc.rem_ben_amt::numeric as rem_ben_amt,
  rprc.amt_exd_ben_max::numeric as amt_exd_ben_max,
  rprc.spd_acct_remaining::numeric as spd_acct_remaining,
  rprc.amt_attr_nonformulary::numeric as amt_attr_nonformulary,
  rprc.amt_attr_brd_nonformulary::numeric as amt_attr_brd_nonformulary,
  rprc.amt_attr_prov_sel::numeric as amt_attr_prov_sel,
  rprc.amt_attr_prod_sel::numeric as amt_attr_prod_sel,
  COALESCE(prc.gross_amount_due, 0.0)::numeric - COALESCE(rprc.total_paid, 0.0)::numeric AS total_balance,
  resp.created_on::date as adjudication_date
FROM form_ncpdp claim
JOIN sf_form_ncpdp_to_ncpdp_pricing sfprc 
  ON sfprc.form_ncpdp_fk = claim.id
  AND sfprc.archive IS NOT TRUE
  AND sfprc.delete IS NOT TRUE
JOIN form_ncpdp_pricing prc 
  ON prc.id = sfprc.form_ncpdp_pricing_fk 
  AND prc.archived IS NOT TRUE 
  AND prc.deleted IS NOT TRUE
JOIN form_billing_invoice bi ON bi.invoice_no = claim.invoice_no
    AND bi.archived IS NOT TRUE
    AND bi.deleted IS NOT TRUE
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes'
LEFT JOIN latest_response resp
  ON resp.claim_no = claim.claim_no
LEFT JOIN latest_stat stat
  ON stat.form_ncpdp_response_fk = resp.id
LEFT JOIN latest_prc rprc
  ON rprc.form_ncpdp_response_fk = resp.id
WHERE claim.archived IS NOT TRUE 
  AND claim.deleted IS NOT TRUE
  AND COALESCE(claim.void, 'No') <> 'Yes';


CREATE OR REPLACE VIEW vw_mm_claim_response AS
WITH latest_response_835 AS (
  SELECT DISTINCT ON (resp.claim_no)
    resp.*
  FROM form_med_claim_resp_835 resp
  WHERE resp.archived IS NOT TRUE
    AND resp.deleted IS NOT TRUE
  ORDER BY resp.claim_no, resp.id DESC
),
latest_response_277 AS (
  SELECT DISTINCT ON (resp.claim_no)
    resp.claim_no::text as claim_no,
    resp.control_number::text as control_number,
    resp.status::text as status,
    resp.status_information_effective_date::date as status_information_effective_date,
    resp.health_care_claim_status_category_code::date as health_care_claim_status_category_code,
    resp.health_care_claim_status_category_code_value::text as health_care_claim_status_category_code_value,
    resp.status_code::text as status_code,
    resp.status_code_value::text as status_code_value,
    ts.control_number::text as control_number,
    ts.reference_identification::text as reference_identification,
    ts.transaction_set_creation_date::date as transaction_set_creation_date,
    ts.transaction_set_creation_time::time as transaction_set_creation_time,
    ts.payers::jsonb as payers,
    ts.meta::jsonb as meta,
    ts.response_id::text as response_id,
  FROM form_med_claim_resp_277 resp
  LEFT JOIN sf_form_med_claim_resp_277_to_med_claim_resp_ts sfts ON sfts.form_med_claim_resp_277_fk = resp.id
  AND sfts.archive IS NOT TRUE
  AND sfts.delete IS NOT TRUE
  LEFT JOIN form_med_claim_resp_ts ts ON ts.id = sfts.form_med_claim_resp_ts_fk
  AND ts.archived IS NOT TRUE
  AND ts.deleted IS NOT TRUE
  WHERE resp.archived IS NOT TRUE
    AND resp.deleted IS NOT TRUE
  ORDER BY resp.claim_no, resp.status_information_effective_date DESC
),
latest_stat AS (
  SELECT DISTINCT ON (sf.form_ncpdp_response_fk)
    sf.form_ncpdp_response_fk,
    stat.transaction_response_status
  FROM sf_form_ncpdp_response_to_ncpdp_response_stat sf
  JOIN form_ncpdp_response_stat stat ON stat.id = sf.form_ncpdp_response_stat_fk
  WHERE sf.archive IS NOT TRUE
    AND sf.delete IS NOT TRUE
    AND stat.archived IS NOT TRUE
    AND stat.deleted IS NOT TRUE
  ORDER BY sf.form_ncpdp_response_fk, stat.id DESC
),
latest_prc AS (
  SELECT DISTINCT ON (sf.form_ncpdp_response_fk)
    sf.form_ncpdp_response_fk,
    rprc.*
  FROM sf_form_ncpdp_response_to_ncpdp_response_prc sf
  JOIN form_ncpdp_response_prc rprc ON rprc.id = sf.form_ncpdp_response_prc_fk
  WHERE sf.archive IS NOT TRUE
    AND sf.delete IS NOT TRUE
    AND rprc.archived IS NOT TRUE
    AND rprc.deleted IS NOT TRUE
  ORDER BY sf.form_ncpdp_response_fk, rprc.id DESC
)
SELECT
  claim.patient_id::integer as patient_id,
  claim.payer_id::integer as payer_id,
  claim.site_id::integer as site_id,
  claim.id::integer AS ncpdp_id,
  claim.claim_no::text as claim_no,
  claim.invoice_no::text as invoice_no,
  claim.transaction_code::text as transaction_code,
  bi.master_invoice_no::text as master_invoice_no,
  stat.transaction_response_status::text as transaction_response_status,
  resp.response_status::text as response_status,
  prc.ing_cst_sub::numeric as ing_cst_sub,
  prc.disp_fee_sub::numeric as disp_fee_sub,
  prc.pro_svc_fee_sub::numeric as pro_svc_fee_sub,
  prc.incv_amt_sub::numeric as incv_amt_sub,
  prc.pt_pd_amt_sub::numeric as pt_pd_amt_sub,
  prc.u_and_c_charge::numeric as u_and_c_charge,
  prc.gross_amount_due::numeric as gross_amount_due,
  prc.cost_basis::numeric as cost_basis,
  rprc.total_paid::numeric as total_paid,
  rprc.pt_pay_amt::numeric as pt_pay_amt,
  rprc.ing_cst_paid::numeric as ing_cst_paid,
  rprc.disp_fee_paid::numeric as disp_fee_paid,
  rprc.amt_disp_fee_contracted::numeric as amt_disp_fee_contracted,
  rprc.incv_amt_paid::numeric as incv_amt_paid,
  rprc.pro_fee_amt::numeric as pro_fee_amt,
  rprc.amt_coverage_gap::numeric as amt_coverage_gap,
  rprc.coinsur_amt::numeric as coinsur_amt,
  rprc.acc_ded_amt::numeric as acc_ded_amt,
  rprc.rem_ded_amt::numeric as rem_ded_amt,
  rprc.amt_apld_ded::numeric as amt_apld_ded,
  rprc.amt_copay::numeric as amt_copay,
  rprc.hlth_pln_asst::numeric as hlth_pln_asst,
  rprc.est_gen_savings::numeric as est_gen_savings,
  rprc.rem_ben_amt::numeric as rem_ben_amt,
  rprc.amt_exd_ben_max::numeric as amt_exd_ben_max,
  rprc.spd_acct_remaining::numeric as spd_acct_remaining,
  rprc.amt_attr_nonformulary::numeric as amt_attr_nonformulary,
  rprc.amt_attr_brd_nonformulary::numeric as amt_attr_brd_nonformulary,
  rprc.amt_attr_prov_sel::numeric as amt_attr_prov_sel,
  rprc.amt_attr_prod_sel::numeric as amt_attr_prod_sel,
  COALESCE(prc.gross_amount_due, 0.0)::numeric - COALESCE(rprc.total_paid, 0.0)::numeric AS total_balance,
  resp.created_on::date as adjudication_date
FROM form_ncpdp claim
JOIN sf_form_ncpdp_to_ncpdp_pricing sfprc 
  ON sfprc.form_ncpdp_fk = claim.id
  AND sfprc.archive IS NOT TRUE
  AND sfprc.delete IS NOT TRUE
JOIN form_ncpdp_pricing prc 
  ON prc.id = sfprc.form_ncpdp_pricing_fk 
  AND prc.archived IS NOT TRUE 
  AND prc.deleted IS NOT TRUE
JOIN form_billing_invoice bi ON bi.invoice_no = claim.invoice_no
    AND bi.archived IS NOT TRUE
    AND bi.deleted IS NOT TRUE
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes'
LEFT JOIN latest_response resp
  ON resp.claim_no = claim.claim_no
LEFT JOIN latest_stat stat
  ON stat.form_ncpdp_response_fk = resp.id
LEFT JOIN latest_prc rprc
  ON rprc.form_ncpdp_response_fk = resp.id
WHERE claim.archived IS NOT TRUE 
  AND claim.deleted IS NOT TRUE
  AND COALESCE(claim.void, 'No') <> 'Yes';

CREATE OR REPLACE VIEW vw_master_pending_invoice_drill_down AS
SELECT
    bi.id,
    'billing_invoice' AS form_name,
    bi.id AS form_id,
    bi.invoice_no as "Invoice No",
    pt.id AS patient_id,
    py.id AS payer_id,
    bi.site_id,
    st."name" as "Site",
    pt.lastname AS "Last Name",
    pt.firstname AS "First Name",
    pt.mrn as "MRN",
    py.organization as "Payer",
    hpi.pri_item,
    hpi.inventory_id,
    hpi.brand_name_id,
    hpi.therapy_id,
    hpi.therapy as "Therapy",
    hpi.physician,
    hpi.physician_id,
    hpi.physician_last_name,
    hpi.physician_first_name,
    hpi.physician_title,
    hpi.physician_state_id,
    hpi.physician_zip,
    hpi.physician_city,
    bi.billed_datetime,
    bi.master_invoice_no,
    bi.invoice_no,
    bi.created_on,
    bi.post_datetime,
    bi.zeroed,
    bi.void,
    bi.on_hold,
    CASE
      WHEN COALESCE(bi.is_master_invoice, 'No') = 'Yes' THEN 1
      ELSE 2
    END as group_level,
    TO_CHAR(bi.created_on, 'MM/DD/YYYY HH:MI AM') AS "Created Date/Time",
    TO_CHAR(bi.post_datetime, 'MM/DD/YYYY HH:MI AM') AS "Posted Date/Time",
    TO_CHAR(bi.billed_datetime, 'MM/DD/YYYY HH:MI AM') as "Billed Date/Time",
    COALESCE(bi.total_paid, 0.0) as paid_raw,
    format_currency(
        COALESCE(bi.total_paid, 0.0)::numeric
    ) AS "Paid",
    COALESCE(bi.total_cost, 0.0) AS cost_raw,
    format_currency(
        COALESCE(bi.total_cost, 0.0)::numeric
    ) AS "Cost",
    COALESCE(bi.total_expected, 0.0) as expected_raw,
    format_currency(
        COALESCE(bi.total_expected, 0.0)::numeric
    ) AS "Expected",
    ROUND(COALESCE(bi.total_expected, 0.0)::numeric - COALESCE(bi.total_cost, 0.0)::numeric, 2) as profit_raw,
    format_currency(
        ROUND(COALESCE(bi.total_expected, 0.0)::numeric - COALESCE(bi.total_cost, 0.0)::numeric, 2)::numeric
    ) AS "Exp Profit",
    CASE
        WHEN COALESCE(bi.total_expected, 0.0) > 0 THEN
            ROUND(((COALESCE(bi.total_expected, 0.0)::numeric - COALESCE(bi.total_cost,0)::numeric) / COALESCE(bi.total_expected, 0.0)::numeric * 100)::numeric, 2)
        ELSE NULL
    END AS profit_margin_raw,
    CASE
        WHEN COALESCE(bi.total_expected, 0.0) > 0 THEN
            CONCAT(format_numeric(ROUND(((COALESCE(bi.total_expected, 0.0)::numeric - COALESCE(bi.total_cost,0)::numeric) / COALESCE(bi.total_expected, 0.0)::numeric * 100)::numeric, 2)::numeric), '%')
        ELSE NULL
    END AS "Exp Profit Margin",
    iva.claim_substatus_id,
    iva.claim_substatus_id_auto_name,
    iva.claim_substatus_id_auto_name as "Claim Substatus",
    CASE
        WHEN bi.void = 'Yes' OR bi.zeroed = 'Yes' THEN '#FCB6B6'
        WHEN bi.bill_for_denial = 'Yes' THEN '#FACAA5'
        ELSE NULL
    END AS __row_color
FROM 
    form_billing_invoice bi
INNER JOIN form_patient pt ON pt.id = bi.patient_id
INNER JOIN form_payer py ON py.id = bi.payer_id
INNER JOIN form_site st ON st.id = bi.site_id
LEFT JOIN vw_invoice_claim_response_details iva ON iva.invoice_id = bi.id
LEFT JOIN vw_invoice_highest_priced_item_info hpi ON hpi.invoice_no = bi.invoice_no
WHERE bi."deleted" IS NOT TRUE 
    AND bi.archived IS NOT TRUE
    AND COALESCE(bi.bill_for_denial, 'No') = 'No'
    AND COALESCE(bi.revenue_accepted_posted, 'No') = 'No';

CREATE OR REPLACE VIEW vw_master_pending_cash_drill_down AS
SELECT
    cp.id,
    cp.patient_id,
    cp.payer_id,
    cp.zeroed,
    cp.zeroed_reason_id,
    cp.post_datetime,
    'billing_cash' AS form_name,
    cp.id AS form_id,
    CASE WHEN cp.payment_method = 'Other' THEN cp.other_payment_method ELSE cp.payment_method END as "Payment Method",
    cp.cash_no as "Cash #",
    cp.check_no as "Check #",
    format_currency(
        ROUND(COALESCE(cp.amount, 0.0)::numeric, 2)::numeric
    ) AS "Amount",
    TO_CHAR(cp.post_datetime, 'MM/DD/YYYY HH:MI AM') AS "Posted Date/Time",
    TO_CHAR(cp.applied_datetime, 'MM/DD/YYYY HH:MI AM') AS "Applied Date/Time",
    cp.amount as amount_raw,
    CASE
        WHEN cp.zeroed = 'Yes' THEN '#FCB6B6'
        ELSE NULL
    END AS __row_color
FROM 
    form_billing_cash cp
LEFT JOIN form_patient pt ON pt.id = cp.patient_id
LEFT JOIN form_payer py ON py.id = cp.payer_id
WHERE cp."deleted" IS NOT TRUE 
    AND cp.archived IS NOT TRUE;

CREATE OR REPLACE VIEW vw_master_invoice_drill_down AS
SELECT
    bi.id,
    'billing_invoice' AS form_name,
    bi.id AS form_id,
    bi.invoice_no as "Invoice No",
    pt.id AS patient_id,
    py.id AS payer_id,
    bi.site_id,
    st."name" as "Site",
    pt.lastname AS "Last Name",
    pt.firstname AS "First Name",
    pt.mrn as "MRN",
    py.organization as "Payer",
    hpi.pri_item,
    hpi.inventory_id,
    hpi.brand_name_id,
    hpi.therapy_id,
    hpi.therapy as "Therapy",
    hpi.physician,
    hpi.physician_id,
    hpi.physician_last_name,
    hpi.physician_first_name,
    hpi.physician_title,
    hpi.physician_state_id,
    hpi.physician_zip,
    hpi.physician_city,
    bi.billed_datetime,
    bi.master_invoice_no,
    bi.invoice_no,
    bi.created_on,
    bi.post_datetime,
    CASE
      WHEN COALESCE(bi.is_master_invoice, 'No') = 'Yes' THEN 1
      ELSE 2
    END as group_level,
    lf.last_transaction_datetime,
    TO_CHAR(bi.created_on, 'MM/DD/YYYY HH:MI AM') AS "Created Date/Time",
    TO_CHAR(bi.post_datetime, 'MM/DD/YYYY HH:MI AM') AS "Posted Date/Time",
    TO_CHAR(bi.billed_datetime, 'MM/DD/YYYY HH:MI AM') as "Billed Date/Time",
    TO_CHAR(lf.last_transaction_datetime, 'MM/DD/YYYY HH:MI AM') as "Last Transaction Date/Time",
    COALESCE(lf.paid_raw, 0.0) as paid_raw,
    format_currency(
        COALESCE(lf.paid_raw, 0.0)::numeric
    ) AS "Paid",
    COALESCE(lf.cogs_raw, 0.0) AS cost_raw,
    format_currency(
        COALESCE(lf.cogs_raw, 0.0)::numeric
    ) AS "Cost",
    COALESCE(lf.expected_raw, 0.0) as expected_raw,
    format_currency(
        COALESCE(lf.expected_raw, 0.0)::numeric
    ) as "Expected",
    COALESCE(lf.net_adjustment_raw, 0.0) as adjustment_raw,
    format_currency(
        COALESCE(lf.net_adjustment_raw, 0.0)::numeric
    ) AS "Adjustments",
    COALESCE(lf.balance_raw, 0.0) as balance_raw,
    format_currency(
        COALESCE(lf.balance_raw, 0.0)::numeric
    ) as "Balance",
    CASE
        WHEN COALESCE(lf.expected_raw, 0.0) <= 0 OR py.id = 1 THEN NULL
        ELSE ROUND(((COALESCE(lf.expected_raw, 0.0)::numeric - COALESCE(lf.cogs_raw, 0)::numeric) / COALESCE(lf.expected_raw, 0.0)::numeric * 100)::numeric, 2)
    END AS profit_margin_raw,
    CASE
        WHEN COALESCE(lf.expected_raw, 0.0) <= 0 OR py.id = 1 THEN NULL
        ELSE CONCAT(format_numeric(ROUND(((COALESCE(lf.expected_raw, 0.0)::numeric - COALESCE(lf.cogs_raw, 0)::numeric) / COALESCE(lf.expected_raw, 0.0)::numeric * 100)::numeric, 2)::numeric), '%')
    END AS "Profit Margin",
    CASE
        WHEN COALESCE(lf.expected_raw, 0.0) <= 0 OR py.id = 1 THEN NULL
        ELSE ROUND((COALESCE(lf.expected_raw, 0.0)::numeric - COALESCE(lf.cogs_raw, 0)::numeric)::numeric, 2)
    END AS profit_raw,
    CASE
        WHEN COALESCE(lf.expected_raw, 0.0) <= 0 OR py.id = 1 THEN NULL
        ELSE format_currency((COALESCE(lf.expected_raw, 0.0)::numeric - COALESCE(lf.cogs_raw, 0)::numeric)::numeric)
    END AS "Profit",
    bi.on_hold,
    bi.zeroed,
    iva.status as claim_status,
    iva.claim_substatus_id,
    iva.claim_substatus_id_auto_name as "Claim Substatus",
    -- Status indicator
    CASE
        WHEN COALESCE(lf.balance_raw, 0.0) <= 0 THEN 'Paid'
        WHEN COALESCE(lf.paid_raw, 0.0) > 0 THEN 'Partially Paid'
        ELSE 'Unpaid'
    END as payment_status,
    CASE
        WHEN iva.status IN ('Rejected', 'Denied', 'Error', 'Rebill Rejected', 'Reversal Rejected') THEN '#FFD9D9'
        WHEN iva.status IN ('On-hold','PA Deferred', 'Margin', 'Warning', 'Request for Additional Information', 'Awaiting Requested Information') THEN '#F5C198'
        WHEN iva.status IN ('Payable', 'Partially Paid') THEN '#9EDBC7'
        WHEN bi.zeroed = 'Yes' THEN '#FCB6B6'
        WHEN bi.on_hold = 'Yes' THEN '#F5C198'
        ELSE NULL
    END AS __row_color
FROM 
    form_billing_invoice bi
INNER JOIN form_patient pt ON pt.id = bi.patient_id
INNER JOIN form_payer py ON py.id = bi.payer_id
INNER JOIN form_site st ON st.id = bi.site_id
LEFT JOIN vw_invoice_claim_response_details iva ON iva.invoice_id = bi.id
LEFT JOIN vw_invoice_highest_priced_item_info hpi ON hpi.invoice_no = bi.invoice_no
JOIN LATERAL (
SELECT 
  (COALESCE(SUM(debit) FILTER (WHERE account_type = 'AR'), 0) - 
   COALESCE(SUM(credit) FILTER (WHERE account_type = 'AR'), 0)) AS balance_raw,
  (COALESCE(SUM(debit) FILTER (WHERE account_type = 'Cash'), 0) - 
   COALESCE(SUM(credit) FILTER (WHERE account_type = 'Cash'), 0)) AS paid_raw,
  COALESCE(SUM(debit) FILTER (WHERE account_type = 'AR'), 0) AS expected_raw,
    (COALESCE(SUM(credit) - SUM(debit) FILTER (WHERE account_type = 'AR' AND transaction_type IN ('Adjustment', 'Adjustment Reversal', 'Writeoff', 'Writeoff Reversal')), 0)) AS net_adjustment_raw,
  (COALESCE(SUM(debit) FILTER (WHERE account_type = 'COGS'), 0) - 
   COALESCE(SUM(credit) FILTER (WHERE account_type = 'COGS'), 0)) AS cogs_raw,
  MAX(transaction_datetime) AS last_transaction_datetime
FROM form_ledger_finance flf
WHERE flf.invoice_id = bi.id
) lf on true
WHERE bi."deleted" IS NOT TRUE 
    AND bi.archived IS NOT TRUE
    AND COALESCE(bi.bill_for_denial, 'No') = 'No'
    AND COALESCE(bi.revenue_accepted_posted, 'No') = 'Yes';

-- Create supporting index
CREATE INDEX IF NOT EXISTS idx_ledger_finance_invoice_account 
ON form_ledger_finance(invoice_id, account_type)
WHERE invoice_id IS NOT NULL;

CREATE OR REPLACE VIEW vw_rx_disp_invoices AS
WITH invoice_finance AS (
    -- Get finance data for each invoice
    SELECT 
        invoice_id,
        -- AR balance 
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'AR'
            THEN (COALESCE(debit, 0)::numeric - COALESCE(credit, 0)::numeric)
            ELSE 0::numeric
        END), 0), 2) AS balance_raw,
        
        -- Cash payments 
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'Cash'
            THEN (COALESCE(debit, 0)::numeric - COALESCE(credit, 0)::numeric)
            ELSE 0::numeric
        END), 0), 2) AS paid_raw,
        
        -- Expected amount (AR debits)
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'AR'
            THEN COALESCE(debit, 0)::numeric
            ELSE 0::numeric
        END), 0), 2) AS expected_raw,
        
        -- Adjustments and writeoffs
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'AR' AND transaction_type IN ('Adjustment', 'Adjustment Reversal', 'Writeoff', 'Writeoff Reversal')
            THEN (COALESCE(credit, 0)::numeric - COALESCE(debit, 0)::numeric)
            ELSE 0::numeric
        END), 0), 2) AS net_adjustment_raw,
        
        -- Direct COGS from invoice
        ROUND(COALESCE(SUM(CASE
            WHEN account_type = 'COGS'
            THEN (COALESCE(debit, 0)::numeric - COALESCE(credit, 0)::numeric)
            ELSE 0::numeric
        END), 0), 2) AS direct_cogs_raw,
        
        MAX(transaction_datetime) AS last_transaction_datetime
    FROM form_ledger_finance
    WHERE invoice_id IS NOT NULL
    GROUP BY invoice_id
),
-- Get COGS from ticket items for each invoice
invoice_ticket_cogs AS (
    SELECT
        bi.id AS invoice_id,
        ROUND(SUM(COALESCE(
            -- Serial items COGS
            (SELECT SUM(lf.debit - lf.credit)::numeric
             FROM form_ledger_finance lf
             JOIN form_ledger_serial ls ON ls.id = lf.ledger_serial_id
             JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = ls.ticket_item_no 
                AND pld.serial_id IS NOT NULL
             JOIN form_ledger_charge_line lcl ON lcl.ticket_item_no = pld.ticket_item_no
             WHERE lf.ledger_serial_id IS NOT NULL
               AND lf.account_type = 'COGS'
               AND lf.transaction_type = 'Dispense'
               AND lcl.invoice_no = bi.invoice_no),
            
            -- Lot items COGS
            (SELECT SUM(lf.debit - lf.credit)::numeric
             FROM form_ledger_finance lf
             JOIN form_ledger_lot ll ON ll.id = lf.ledger_lot_id
             JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = ll.ticket_item_no 
                AND pld.lot_id IS NOT NULL 
                AND pld.serial_id IS NULL
             JOIN form_ledger_charge_line lcl ON lcl.ticket_item_no = pld.ticket_item_no
             WHERE lf.ledger_lot_id IS NOT NULL
               AND lf.account_type = 'COGS'
               AND lf.transaction_type = 'Dispense'
               AND lcl.invoice_no = bi.invoice_no),
               
            -- Inventory items COGS
            (SELECT SUM(lf.debit - lf.credit)::numeric
             FROM form_ledger_finance lf
             JOIN form_ledger_inventory li ON li.id = lf.ledger_inventory_id
             JOIN form_careplan_dt_wt_pulled pld ON pld.ticket_item_no = li.ticket_item_no 
                AND pld.inventory_id = li.inventory_id 
                AND pld.lot_id IS NULL 
                AND pld.serial_id IS NULL
             JOIN form_ledger_charge_line lcl ON lcl.ticket_item_no = pld.ticket_item_no
             WHERE lf.ledger_inventory_id IS NOT NULL
               AND lf.account_type = 'COGS'
               AND lf.transaction_type = 'Dispense'
               AND lcl.invoice_no = bi.invoice_no),
            
            0
        )), 2) AS ticket_cogs_raw
    FROM form_billing_invoice bi
    WHERE bi.archived IS NOT TRUE
      AND bi.deleted IS NOT TRUE
    GROUP BY bi.id
),
-- Combine all vw_charge_line_report sources
combined_invoice_cogs AS (
    SELECT
        COALESCE(inf.invoice_id, itc.invoice_id) AS invoice_id,
        CASE
            WHEN inf.direct_cogs_raw > 0 THEN inf.direct_cogs_raw
            WHEN itc.ticket_cogs_raw > 0 THEN itc.ticket_cogs_raw
            ELSE 0
        END AS cogs_raw
    FROM invoice_finance inf
    FULL OUTER JOIN invoice_ticket_cogs itc ON itc.invoice_id = inf.invoice_id
)
SELECT
    bi.id,
    'billing_invoice' AS query_form,
    bi.id AS query_id,
    bi.patient_id,
    grrx.form_careplan_order_rx_fk as rx_id,
    bi.invoice_no as "Invoice No",
    py.id AS payer_id,
    py.auto_name as payer_id_auto_name,
    bi.site_id,
    st.auto_name as site_id_auto_name,
    hpi.inventory_id,
    inv.auto_name as inventory_id_auto_name,
    st."name" as "Site",
    py.organization as "Payer",
    bi.master_invoice_no,
    bi.created_on,
    TO_CHAR(bi.created_on, 'MM/DD/YYYY HH:MI AM') AS "Created Date/Time",
    TO_CHAR(bi.post_datetime, 'MM/DD/YYYY HH:MI AM') AS "Posted Date/Time",
    TO_CHAR(bi.billed_datetime, 'MM/DD/YYYY HH:MI AM') as "Billed Date/Time",
    COALESCE(inf.paid_raw, 0.0) as paid_raw,
    format_currency(
        COALESCE(inf.paid_raw, 0.0)::numeric
    ) AS paid,
    COALESCE(cic.cogs_raw, 0.0) AS cost_raw,
    format_currency(
        COALESCE(cic.cogs_raw, 0.0)::numeric
    ) AS cost,
    COALESCE(inf.expected_raw, 0.0) as expected_raw,
    format_currency(
        COALESCE(inf.expected_raw, 0.0)::numeric
    ) as expected,
    COALESCE(inf.net_adjustment_raw, 0.0) as adjustment_raw,
    format_currency(
        COALESCE(inf.net_adjustment_raw, 0.0)::numeric
    ) AS adjustments,
    COALESCE(inf.balance_raw, 0.0) as balance_raw,
    format_currency(
        COALESCE(inf.balance_raw, 0.0)::numeric
    ) as balance,
    CASE
        WHEN COALESCE(inf.expected_raw, 0.0) > 0 AND py.id != 1 THEN
            ROUND(((COALESCE(inf.expected_raw, 0.0)::numeric - COALESCE(cic.cogs_raw, 0.0)::numeric) / 
                   COALESCE(inf.expected_raw, 0.0)::numeric * 100)::numeric, 2)
        ELSE NULL
    END AS profit_margin_raw,
    CASE
        WHEN COALESCE(inf.expected_raw, 0.0) > 0 AND py.id != 1 THEN
            CONCAT(format_numeric(ROUND(((COALESCE(inf.expected_raw, 0.0)::numeric - COALESCE(cic.cogs_raw, 0.0)::numeric) / 
                   COALESCE(inf.expected_raw, 0.0)::numeric * 100)::numeric, 2)::numeric), '%')
        ELSE NULL
    END AS profit_margin,
    CASE
        WHEN COALESCE(inf.expected_raw, 0.0) > 0 AND py.id != 1 THEN
            ROUND((COALESCE(inf.expected_raw, 0.0)::numeric - COALESCE(cic.cogs_raw, 0.0)::numeric)::numeric, 2)
        ELSE NULL
    END AS profit_raw,
    CASE
        WHEN COALESCE(inf.expected_raw, 0.0) > 0 AND py.id != 1 THEN
            format_currency((COALESCE(inf.expected_raw, 0.0)::numeric - COALESCE(cic.cogs_raw, 0.0)::numeric)::numeric)
        ELSE NULL
    END AS profit,
    iva.status as claim_status,
    iva.claim_substatus_id,
    iva.claim_substatus_id_auto_name,
    iva.claim_substatus_id_auto_name as "Claim Substatus",
    -- Status indicator
    CASE
        WHEN COALESCE(inf.balance_raw, 0.0) <= 0 THEN 'Paid'
        WHEN COALESCE(inf.paid_raw, 0.0) > 0 THEN 'Partially Paid'
        ELSE 'Unpaid'
    END as payment_status,
    CASE
        WHEN iva.status IN ('Rejected', 'Denied', 'Error', 'Rebill Rejected', 'Reversal Rejected') THEN '#FFD9D9'
        WHEN iva.status IN ('On-hold','PA Deferred', 'Margin', 'Warning', 'Request for Additional Information', 'Awaiting Requested Information') THEN '#F5C198'
        WHEN iva.status IN ('Payable', 'Partially Paid') THEN '#9EDBC7'
        WHEN bi.zeroed = 'Yes' THEN '#FCB6B6'
        WHEN bi.on_hold = 'Yes' THEN '#F5C198'
        ELSE NULL
    END AS __row_color
FROM 
    form_billing_invoice bi
INNER JOIN gr_form_billing_invoice_rx_id_to_careplan_order_rx_id grrx 
    ON grrx.form_billing_invoice_fk = bi.id 
INNER JOIN form_payer py ON py.id = bi.payer_id
INNER JOIN form_site st ON st.id = bi.site_id
LEFT JOIN invoice_finance inf ON inf.invoice_id = bi.id
LEFT JOIN combined_invoice_cogs cic ON cic.invoice_id = bi.id
LEFT JOIN vw_invoice_claim_response_details iva ON iva.invoice_id = bi.id
LEFT JOIN vw_invoice_highest_priced_item_info hpi ON hpi.invoice_no = bi.invoice_no
LEFT JOIN form_inventory inv ON inv.id = hpi.inventory_id
WHERE bi."deleted" IS NOT TRUE 
    AND bi.archived IS NOT TRUE
    AND COALESCE(bi.bill_for_denial, 'No') = 'No'
    AND COALESCE(bi.revenue_accepted_posted, 'No') = 'Yes';

CREATE OR REPLACE VIEW vw_pending_revenue_drill_down AS
SELECT
    *
    FROM vw_master_pending_invoice_drill_down bi
WHERE COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.zeroed, 'No') <> 'Yes';


CREATE OR REPLACE VIEW vw_insurance_open_invoice_drill_down AS
SELECT * FROM vw_master_invoice_drill_down
WHERE payer_id <> 1 AND balance_raw > 0 AND COALESCE(on_hold, 'No') <> 'Yes';

CREATE OR REPLACE VIEW vw_patient_open_invoice_drill_down AS
SELECT * FROM vw_master_invoice_drill_down
WHERE payer_id = 1 AND balance_raw > 0 AND COALESCE(on_hold, 'No') <> 'Yes';

CREATE OR REPLACE VIEW vw_combined_open_invoice_drill_down AS
SELECT * FROM vw_master_invoice_drill_down
WHERE COALESCE(on_hold, 'No') <> 'Yes' AND balance_raw > 0;

CREATE OR REPLACE VIEW vw_invoice_onhold_revenue AS
SELECT * FROM vw_master_invoice_drill_down
WHERE COALESCE(on_hold, 'No') = 'Yes';

CREATE OR REPLACE VIEW vw_patient_pending_revenue AS
SELECT * FROM vw_pending_revenue_drill_down
WHERE COALESCE(on_hold, 'No') <> 'Yes';

CREATE OR REPLACE VIEW vw_ar_manager_drill_down AS
SELECT
    bi.id,
    'billing_invoice' AS form_name,
    bi.id AS form_id,
    pt.id AS patient_id,
    py.id AS payer_id,
    bi.site_id,
    st."name" as site,
    pt.lastname AS last_name,
    pt.firstname AS first_name,
    TRIM(CONCAT(pt.lastname, ', ', pt.firstname)) AS patient_name,
    pt.mrn as mrn,
    py.organization as payer,
    hpi.pri_item,
    hpi.inventory_id,
    hpi.brand_name_id,
    hpi.therapy_id,
    hpi.therapy as therapy,
    hpi.physician,
    hpi.physician_id,
    hpi.physician_last_name,
    hpi.physician_first_name,
    hpi.physician_title,
    hpi.physician_state_id,
    hpi.physician_zip,
    hpi.physician_city,
    bi.billed_datetime,
    bi.master_invoice_no,
    bi.invoice_no,
    bi.created_on,
    bi.post_datetime,
    lf.last_transaction_datetime,
    TO_CHAR(bi.created_on, 'MM/DD/YYYY HH:MI AM') AS created_datetime_str,
    TO_CHAR(bi.post_datetime, 'MM/DD/YYYY HH:MI AM') AS posted_datetime_str,
    TO_CHAR(bi.billed_datetime, 'MM/DD/YYYY HH:MI AM') as billed_datetime_str,
    TO_CHAR(bi.billed_datetime, 'MM/DD/YYYY') as billed_date,
    TO_CHAR(lf.last_transaction_datetime, 'MM/DD/YYYY HH:MI AM') as last_transaction_datetime_str,
    COALESCE(lf.paid_raw, 0.0) as paid_raw,
    format_currency(
        COALESCE(lf.paid_raw, 0.0)::numeric
    ) AS paid,
    COALESCE(bi.total_cost, 0.0) AS cost_raw,
    format_currency(
        COALESCE(bi.total_cost, 0.0)::numeric
    ) AS cost,
    COALESCE(lf.billed_raw, 0.0) as expected_raw,
    format_currency(
        COALESCE(lf.billed_raw, 0.0)::numeric
    ) as expected,
    format_currency(
        COALESCE(bi.total_billed, 0.0)::numeric
    ) as billed,
    COALESCE(lf.balance_raw, 0.0) as balance_raw,
    format_currency(
        COALESCE(lf.balance_raw, 0.0)::numeric
    ) as balance,
    CASE
        WHEN COALESCE(lf.billed_raw, 0.0) <= 0 OR py.id = 1 THEN NULL
        ELSE ROUND(((COALESCE(lf.billed_raw, 0.0)::numeric - COALESCE(bi.total_cost,0)::numeric) / COALESCE(lf.billed_raw, 0.0)::numeric * 100)::numeric, 2)
    END AS profit_margin_raw,
    CASE
        WHEN COALESCE(lf.billed_raw, 0.0) <= 0 OR py.id = 1 THEN NULL
        ELSE CONCAT(format_numeric(ROUND(((COALESCE(lf.billed_raw, 0.0)::numeric - COALESCE(bi.total_cost,0)::numeric) / COALESCE(lf.billed_raw, 0.0)::numeric * 100)::numeric, 2)::numeric), '%')
    END AS profit_margin,
    CASE
        WHEN COALESCE(lf.billed_raw, 0.0) <= 0 OR py.id = 1 THEN NULL
        ELSE ROUND((COALESCE(lf.billed_raw, 0.0)::numeric - COALESCE(bi.total_cost,0)::numeric)::numeric, 2)
    END AS profit_raw,
    CASE
        WHEN COALESCE(lf.billed_raw, 0.0) <= 0 OR py.id = 1 THEN NULL
        ELSE format_currency((COALESCE(lf.billed_raw, 0.0)::numeric - COALESCE(bi.total_cost,0)::numeric)::numeric)
    END AS profit,
    bi.on_hold,
    bi.zeroed,
    iva.status as claim_status,
    iva.claim_substatus_id,
    iva.claim_substatus as claim_substatus,
    CASE
        WHEN bi.bill_for_denial = 'Yes' THEN '#F5C198'
        WHEN iva.status IN ('Rejected', 'Denied', 'Error', 'Rebill Rejected', 'Reversal Rejected') THEN '#FFD9D9'
        WHEN iva.status IN ('On-hold','PA Deferred', 'Margin', 'Warning', 'Request for Additional Information', 'Awaiting Requested Information') THEN '#F5C198'
        WHEN iva.status IN ('Payable', 'Partially Paid') THEN '#9EDBC7'
        WHEN bi.zeroed = 'Yes' THEN '#FCB6B6'
        WHEN bi.on_hold = 'Yes' THEN '#F5C198'
        ELSE NULL
    END AS __row_color,
    CASE 
      WHEN bi.followup_date IS NOT NULL THEN TO_CHAR(bi.followup_date, 'MM/DD/YYYY')
      ELSE NULL
    END AS followup_date,
    CASE
      WHEN bi.date_of_service IS NOT NULL THEN TO_CHAR(bi.date_of_service, 'MM/DD/YYYY')
      ELSE NULL
    END AS dos_start,
    CASE
      WHEN bi.date_of_service_end IS NOT NULL THEN TO_CHAR(bi.date_of_service_end, 'MM/DD/YYYY')
      ELSE NULL
    END AS dos_end
FROM
    form_billing_invoice bi
INNER JOIN form_patient pt ON pt.id = bi.patient_id
INNER JOIN form_payer py ON py.id = bi.payer_id
INNER JOIN form_site st ON st.id = bi.site_id
LEFT JOIN vw_invoice_claim_response_details iva ON iva.invoice_id = bi.id
LEFT JOIN vw_invoice_highest_priced_item_info hpi ON hpi.invoice_no = bi.invoice_no
JOIN LATERAL (
SELECT 
  (COALESCE(SUM(debit) FILTER (WHERE account_type = 'AR'), 0) - 
   COALESCE(SUM(credit) FILTER (WHERE account_type = 'AR'), 0)) AS balance_raw,
  (COALESCE(SUM(debit) FILTER (WHERE account_type = 'Cash'), 0) - 
   COALESCE(SUM(credit) FILTER (WHERE account_type = 'Cash'), 0)) AS paid_raw,
  COALESCE(SUM(debit) FILTER (WHERE account_type = 'AR'), 0) AS billed_raw,
  MAX(transaction_datetime) AS last_transaction_datetime
FROM vw_ledger_finance_metrics vm
) lf on true
WHERE bi."deleted" IS NOT TRUE 
    AND bi.archived IS NOT TRUE
    AND COALESCE(bi.bill_for_denial, 'No') = 'No'
    AND COALESCE(bi.void, 'No') <> 'Yes'
    AND COALESCE(bi.revenue_accepted_posted, 'No') = 'Yes';

CREATE OR REPLACE VIEW vw_pending_medical_claims AS
SELECT
        DISTINCT invoice_no
    FROM 
        form_billing_invoice bi
    WHERE
        COALESCE(bi.void, 'No') = 'No'
        AND bi.archived IS NOT TRUE
        AND bi."deleted" IS NOT TRUE
        AND COALESCE(bi.on_hold, 'No') = 'No'
        AND COALESCE(bi.revenue_accepted_posted, 'No') = 'No'
        AND ((bi.billing_method_id = 'ncpdp' AND COALESCE(bi.bill_mm_and_ncpdp, 'No') = 'Yes')
             OR bi.billing_method_id IN ('mm', 'cms1500'));

CREATE OR REPLACE VIEW vw_pending_pharmacy_claims AS
SELECT
        DISTINCT invoice_no
    FROM 
        form_billing_invoice bi
    WHERE
        COALESCE(bi.void, 'No') = 'No'
        AND bi.archived IS NOT TRUE
        AND bi."deleted" IS NOT TRUE
        AND COALESCE(bi.on_hold, 'No') = 'No'
        AND COALESCE(bi.revenue_accepted_posted, 'No') = 'No'
        AND bi.type_id NOT IN ('CMMED', 'MCRB')
        AND bi.billing_method_id = 'ncpdp';

CREATE OR REPLACE VIEW vw_invoice_remaining_ar_balance AS
WITH invoice_ar_transactions AS (
    -- Get all AR transactions from ledger_finance grouped by invoice
    SELECT 
        lf.invoice_id,
        bi.invoice_no,
        bi.master_invoice_no,
        bi.patient_id,
        bi.payer_id,
        bi.total_expected,
        COALESCE(cresp.copay, bi.total_pt_pay, 0) as copay_amount,
        ROUND(SUM(CASE
            WHEN lf.account_type = 'AR' AND lf.credit > 0
            THEN COALESCE(lf.credit, 0)::numeric
            ELSE 0::numeric
        END), 2) AS paid_amount,
        -- Calculate AR balance (debits - credits)
        ROUND(SUM(CASE
            WHEN lf.account_type = 'AR'
            THEN (COALESCE(lf.debit, 0)::numeric - COALESCE(lf.credit, 0)::numeric)
            ELSE 0::numeric
        END), 2) AS ar_balance
    FROM
        form_ledger_finance lf
    JOIN 
        form_billing_invoice bi ON bi.id = lf.invoice_id
    LEFT JOIN 
        vw_invoice_claim_response_details cresp ON cresp.invoice_no = bi.invoice_no
    WHERE
        lf.account_type = 'AR'
        AND bi.archived IS NOT TRUE
        AND bi.deleted IS NOT TRUE
    GROUP BY 
        lf.invoice_id, bi.invoice_no, bi.master_invoice_no, bi.patient_id, bi.payer_id, 
        bi.total_expected, bi.total_pt_pay, cresp.copay
),
unapplied_cash_by_account AS (
    -- Get all unapplied cash records for patients only
    SELECT
        patient_id,
        SUM(amount_raw)::numeric AS total_unapplied_cash
    FROM
        vw_unapplied_cash_base
    WHERE
        patient_id IS NOT NULL
    GROUP BY
        patient_id
),
invoice_with_unapplied AS (
    -- Join with unapplied cash data
    SELECT
        iat.*,
        COALESCE(uca.total_unapplied_cash, 0)::numeric as total_unapplied_cash,
        -- Calculate how much of the copay amount can be covered by unapplied cash
        LEAST(COALESCE(uca.total_unapplied_cash, 0), iat.copay_amount)::numeric as applied_unapplied_cash,
        -- Calculate remaining balance after considering applied unapplied cash
        GREATEST(0, iat.ar_balance - LEAST(COALESCE(uca.total_unapplied_cash, 0), iat.copay_amount))::numeric as remaining_balance_after_copay_paid
    FROM
        invoice_ar_transactions iat
    LEFT JOIN
        unapplied_cash_by_account uca ON uca.patient_id = iat.patient_id
)
SELECT
    iwu.invoice_id::integer as invoice_id,
    iwu.invoice_no::text as invoice_no,
    iwu.master_invoice_no::text as master_invoice_no,
    iwu.ar_balance::numeric as remaining_ar_balance,
    iwu.total_unapplied_cash::numeric as total_unapplied_cash,
    iwu.applied_unapplied_cash::numeric as applied_unapplied_cash,
    iwu.remaining_balance_after_copay_paid::numeric as remaining_balance_after_copay_paid,
    iwu.paid_amount::numeric as paid_amount,
    iwu.copay_amount::numeric as copay_amount
FROM
    invoice_with_unapplied iwu;

CREATE OR REPLACE VIEW vw_charge_line_remaining_ar_balance AS
WITH charge_line_ar_transactions AS (
    -- Get all AR transactions from ledger_finance grouped by charge line
    SELECT 
        lf.charge_line_id,
        lcl.charge_no,
        lcl.master_charge_no,
        lcl.invoice_no,
        lcl.copay,
        lcl.expected,
        ROUND(SUM(CASE
            WHEN lf.account_type = 'AR' AND lf.credit > 0
            THEN COALESCE(lf.credit, 0)::numeric
            ELSE 0::numeric
        END), 2) AS paid_amount,
        ROUND(SUM(CASE
            WHEN lf.account_type = 'AR'
            THEN (COALESCE(lf.debit, 0)::numeric - COALESCE(lf.credit, 0)::numeric)
            ELSE 0::numeric
        END), 2) AS ar_balance
    FROM 
        form_ledger_finance lf
    JOIN 
        form_ledger_charge_line lcl ON lcl.id = lf.charge_line_id
    WHERE 
        lf.account_type = 'AR'
        AND lcl.archived IS NOT TRUE
        AND lcl.deleted IS NOT TRUE
    GROUP BY 
        lf.charge_line_id, lcl.charge_no, lcl.master_charge_no, lcl.invoice_no, lcl.copay, lcl.expected
),
highest_expected_charge_lines AS (
    -- Identify the highest expected amount charge line for each invoice
    SELECT DISTINCT ON (lcl.invoice_no)
        lcl.invoice_no::text,
        lcl.id::integer AS highest_charge_line_id
    FROM
        form_ledger_charge_line lcl
    WHERE
        lcl.archived IS NOT TRUE
        AND lcl.deleted IS NOT TRUE
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
    ORDER BY
        lcl.invoice_no,
        lcl.expected DESC
),
invoice_unapplied_cash AS (
    -- Get invoice-level unapplied cash data
    SELECT
        iar.invoice_no,
        iar.copay_amount,
        iar.total_unapplied_cash,
        iar.applied_unapplied_cash
    FROM
        vw_invoice_remaining_ar_balance iar
),
charge_line_proportion AS (
    SELECT
        clat.charge_line_id,
        clat.charge_no,
        clat.master_charge_no,
        clat.invoice_no,
        clat.copay,
        clat.expected,
        clat.paid_amount,
        clat.ar_balance,
        CASE
            WHEN COALESCE(bi.total_expected, 0) > 0 THEN
                clat.expected::numeric / bi.total_expected::numeric
            ELSE 0::numeric
        END AS proportion,
        CASE
            WHEN hecl.highest_charge_line_id = clat.charge_line_id THEN TRUE
            ELSE FALSE
        END AS is_highest_expected,
        bi.total_expected as invoice_total_expected
    FROM charge_line_ar_transactions clat
    JOIN form_billing_invoice bi ON bi.invoice_no = clat.invoice_no
    LEFT JOIN highest_expected_charge_lines hecl ON hecl.invoice_no = clat.invoice_no
),
non_highest_totals AS (
    -- Calculate totals for non-highest charge lines
    SELECT
        clp.invoice_no,
        SUM(clp.proportion::numeric * iuc.copay_amount::numeric) as total_non_highest_copay,
        SUM(clp.proportion::numeric * COALESCE(iuc.applied_unapplied_cash, 0)::numeric) as total_non_highest_cash
    FROM charge_line_proportion clp
    JOIN invoice_unapplied_cash iuc ON iuc.invoice_no = clp.invoice_no
    WHERE NOT clp.is_highest_expected
    GROUP BY clp.invoice_no
),
charge_lines_proportional_amounts AS (
    SELECT
        clp.*,
        ROUND(CASE
            WHEN clp.is_highest_expected THEN
                GREATEST(0::numeric, (iuc.copay_amount::numeric - COALESCE(nht.total_non_highest_copay, 0)::numeric)::numeric)
            ELSE
                ROUND((clp.proportion::numeric * iuc.copay_amount::numeric)::numeric, 2)::numeric
        END, 2)::numeric as pending_copay_balance,
        ROUND(CASE
            WHEN clp.is_highest_expected THEN
                GREATEST(0::numeric, (iuc.applied_unapplied_cash::numeric - COALESCE(nht.total_non_highest_cash, 0)::numeric)::numeric)
            ELSE
                ROUND((clp.proportion::numeric * iuc.applied_unapplied_cash::numeric)::numeric, 2)::numeric
        END, 2)::numeric as applied_unapplied_cash
    FROM charge_line_proportion clp
    LEFT JOIN non_highest_totals nht ON nht.invoice_no = clp.invoice_no
    LEFT JOIN invoice_unapplied_cash iuc ON iuc.invoice_no = clp.invoice_no
)
SELECT
    clp.charge_line_id::integer as charge_line_id,
    clp.charge_no::text as charge_no,
    clp.master_charge_no::text as master_charge_no,
    clp.invoice_no::text as invoice_no,
    clp.ar_balance::numeric as remaining_ar_balance,
    clp.paid_amount::numeric as paid_amount,
    clp.pending_copay_balance::numeric as pending_copay_balance,
    clp.applied_unapplied_cash::numeric as applied_unapplied_cash
FROM
    charge_lines_proportional_amounts clp;

CREATE OR REPLACE VIEW vw_charge_line_pending_ar_balance AS
WITH highest_expected_charge_lines AS (
    -- Identify the highest expected amount charge line for each invoice
    SELECT DISTINCT ON (lcl.invoice_no)
        lcl.invoice_no::text,
        lcl.id::integer AS highest_charge_line_id
    FROM
        form_ledger_charge_line lcl
    WHERE
        lcl.archived IS NOT TRUE
        AND lcl.deleted IS NOT TRUE
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
    ORDER BY
        lcl.invoice_no,
        lcl.expected DESC
),
charge_line_proportions AS (
    -- Calculate proportion of each charge line relative to its invoice total
    SELECT
        lcl.invoice_no::text,
        lcl.id::integer AS charge_line_id,
        lcl.charge_no::text,
        lcl.master_charge_no::text,
        lcl.expected::numeric AS charge_expected,
        ipar.expected_amount::numeric AS invoice_expected,
        ipar.pending_ar_balance::numeric AS invoice_remaining_balance,
        ipar.copay_amount::numeric as invoice_copay_amount,
        ipar.applied_unapplied_cash::numeric as invoice_applied_cash,
        CASE
            WHEN COALESCE(ipar.expected_amount, 0)::numeric > 0 THEN
                lcl.expected::numeric / ipar.expected_amount::numeric
            ELSE 0::numeric
        END AS proportion,
        CASE
            WHEN hec.highest_charge_line_id = lcl.id THEN TRUE
            ELSE FALSE
        END AS is_highest_expected
    FROM
        form_ledger_charge_line lcl
        INNER JOIN form_billing_invoice bi ON bi.invoice_no = lcl.invoice_no
        INNER JOIN vw_invoice_pending_ar_balance ipar ON ipar.invoice_no = lcl.invoice_no
        LEFT JOIN highest_expected_charge_lines hec ON hec.invoice_no = lcl.invoice_no
    WHERE
        lcl.archived IS NOT TRUE
        AND lcl.deleted IS NOT TRUE
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
),
non_highest_totals AS (
    -- Calculate totals for non-highest charge lines
    SELECT
        invoice_no,
        SUM(proportion * invoice_remaining_balance) as total_non_highest_ar,
        SUM(proportion * invoice_applied_cash) as total_non_highest_cash,
        SUM(proportion * invoice_copay_amount) as total_non_highest_copay,
        SUM(proportion * invoice_expected) as total_non_highest_expected
    FROM charge_line_proportions
    WHERE NOT is_highest_expected
    GROUP BY invoice_no
)
SELECT
    cp.charge_line_id::integer as charge_line_id,
    lcl.charge_no::text as charge_no,
    lcl.master_charge_no::text as master_charge_no,
    cp.invoice_no::text as invoice_no,
    cp.charge_expected::numeric as expected_amount,
    -- Calculate expected amount as a proportion of invoice expected
    ROUND(CASE
        WHEN cp.is_highest_expected THEN
            GREATEST(0::numeric, (cp.invoice_expected - COALESCE(nht.total_non_highest_expected,0))::numeric)
        ELSE
            ROUND((cp.proportion * cp.invoice_expected)::numeric, 2)::numeric
    END, 2)::numeric as proportional_expected_amount,
    -- Calculate pending AR balance
    ROUND(CASE
        WHEN cp.is_highest_expected THEN
            GREATEST(0::numeric, (cp.invoice_remaining_balance - COALESCE(nht.total_non_highest_ar,0))::numeric)
        ELSE
            ROUND((cp.proportion * cp.invoice_remaining_balance)::numeric, 2)::numeric
    END, 2)::numeric as pending_ar_balance,
    -- Calculate applied unapplied cash amount
    ROUND(CASE
        WHEN cp.is_highest_expected THEN
            GREATEST(0::numeric, (cp.invoice_applied_cash - COALESCE(nht.total_non_highest_cash,0))::numeric)
        ELSE
            ROUND((cp.proportion * cp.invoice_applied_cash)::numeric, 2)::numeric
    END, 2)::numeric as applied_unapplied_cash,
    -- Calculate pending copay balance
    ROUND(CASE
        WHEN cp.is_highest_expected THEN
            GREATEST(0::numeric, (cp.invoice_copay_amount - COALESCE(nht.total_non_highest_copay,0))::numeric)
        ELSE
            ROUND((cp.proportion * cp.invoice_copay_amount)::numeric, 2)::numeric
    END, 2)::numeric as pending_copay_balance
FROM
    charge_line_proportions cp
    INNER JOIN form_ledger_charge_line lcl ON lcl.id = cp.charge_line_id
    LEFT JOIN non_highest_totals nht ON nht.invoice_no = cp.invoice_no;
