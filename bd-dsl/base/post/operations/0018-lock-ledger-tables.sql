

DO $$ BEGIN
  PERFORM drop_all_function_signatures('prevent_update_function');
END $$;
CREATE OR REPLACE FUNCTION prevent_update_function()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);
    RAISE EXCEPTION 'Updates are not allowed on this table';
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER prevent_update_form_ledger_finance
BEFORE UPDATE ON form_ledger_finance
FOR EACH ROW
EXECUTE FUNCTION prevent_update_function();

CREATE OR REPLACE TRIGGER prevent_update_form_ledger_event
BEFORE UPDATE ON form_ledger_event
FOR EACH ROW
EXECUTE FUNCTION prevent_update_function();

CREATE OR REPLACE TRIGGER prevent_update_form_ledger_inventory
BEFORE UPDATE ON form_ledger_inventory
FOR EACH ROW
EXECUTE FUNCTION prevent_update_function();

CREATE OR REPLACE TRIGGER prevent_update_form_ledger_lot
BEFORE UPDATE ON form_ledger_lot
FOR EACH ROW
EXECUTE FUNCTION prevent_update_function();

CREATE OR REPLACE TRIGGER prevent_update_form_ledger_serial
BEFORE UPDATE ON form_ledger_serial
FOR EACH ROW
EXECUTE FUNCTION prevent_update_function();

CREATE OR REPLACE TRIGGER prevent_update_ledger_enc_lock_history
BEFORE UPDATE ON form_ledger_enc_lock_history
FOR EACH ROW
EXECUTE FUNCTION prevent_update_function();

CREATE OR REPLACE TRIGGER prevent_update_form_billing_account
BEFORE UPDATE ON form_billing_account
FOR EACH ROW
EXECUTE FUNCTION prevent_update_function();