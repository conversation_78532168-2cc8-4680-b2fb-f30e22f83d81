CREATE OR REPLACE FUNCTION sync_dt_item_quantity_needed_on_pulled()
RETURNS TRIGGER AS $$
DECLARE
    v_old_quantity numeric := 0;
    v_new_quantity numeric := 0;
    v_calculated_quantity_needed numeric;
BEGIN
    BEGIN
        -- Calculate the current quantity_needed from form_careplan_dt_item
        SELECT i.quantity_needed INTO v_old_quantity
        FROM form_careplan_dt_item i
        WHERE i.ticket_no = NEW.ticket_no
            AND i.ticket_item_no = NEW.ticket_item_no
            AND i.archived IS NOT TRUE
            AND i.deleted IS NOT TRUE;

        RAISE LOG 'v_old_quantity: %', v_old_quantity;

        -- If record is being voided, add back the dispensed_quantity
        IF (TG_OP = 'UPDATE' AND NEW.void = 'Yes' AND OLD.void <> 'Yes') THEN
            v_new_quantity := v_old_quantity + NEW.dispensed_quantity;
        ELSE
            -- Calculate the new quantity_needed based on dispensed_quantity in form_careplan_dt_wt_pulled
            SELECT (i.dispense_quantity - COALESCE((
                SELECT SUM(p.dispensed_quantity)
                FROM form_careplan_dt_wt_pulled p
                WHERE p.ticket_no = i.ticket_no
                    AND p.ticket_item_no = i.ticket_item_no
                    AND p.archived IS NOT TRUE
                    AND p.deleted IS NOT TRUE
                    AND COALESCE(p.void, 'No') <> 'Yes'
            ), 0)::NUMERIC) INTO v_new_quantity
            FROM form_careplan_dt_item i
            WHERE i.ticket_no = NEW.ticket_no
                AND i.ticket_item_no = NEW.ticket_item_no
                AND i.archived IS NOT TRUE
                AND i.deleted IS NOT TRUE;
        END IF;

        RAISE LOG 'v_new_quantity: %', v_new_quantity;
        -- Only update if the calculated quantity_needed has changed
        IF (v_old_quantity IS DISTINCT FROM v_new_quantity) THEN
            UPDATE form_careplan_dt_item i
            SET quantity_needed = v_new_quantity
            WHERE i.ticket_no = NEW.ticket_no
                AND i.ticket_item_no = NEW.ticket_item_no
                AND i.archived IS NOT TRUE
                AND i.deleted IS NOT TRUE;
        END IF;

        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Error in sync_dt_item_quantity_needed_on_pulled: %', SQLERRM;
    END;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER trigger_sync_dt_item_quantity_needed_on_pulled
AFTER INSERT OR UPDATE ON form_careplan_dt_wt_pulled
FOR EACH ROW
EXECUTE FUNCTION sync_dt_item_quantity_needed_on_pulled();
