DO $$ BEGIN
  PERFORM drop_all_function_signatures('build_pricing_ncpdp_segment');
END $$;
CREATE OR REPLACE FUNCTION build_pricing_ncpdp_segment(
  p_patient_id integer,
  p_insurance_id integer,
  p_payer_id integer,
  p_site_id integer,
  p_ing_cst_sub numeric,
  p_pt_pd_amt_sub numeric,
  p_dispense_fee numeric,
  p_u_and_c_charge numeric,
  p_gross_amount_due numeric,
  p_previous_payer_id integer DEFAULT NULL,
  p_transaction_code text DEFAULT 'B1',
  p_is_test BOOLEAN DEFAULT False,
  p_parent_claim_no text DEFAULT NULL
) RETURNS ncpdp_pricing
AS $BODY$
DECLARE
  v_start_time timestamp;
  v_execution_time interval;
  v_result ncpdp_pricing;
  v_error_message text;
  v_params jsonb;
  v_is_test text;
  v_parent_claim_no text;
  v_transaction_code text;
  v_payer_id integer;
  v_ing_cst_sub numeric;
  v_disp_fee_sub numeric;
  v_pro_svc_fee_sub numeric;
  v_pt_pd_amt_sub numeric;
  v_u_and_c_charge numeric;
  v_incv_amt_sub numeric;
  v_flat_tax_amt numeric;
  v_gross_amount_due numeric;
  v_cost_basis text;
  v_subform_oclaim ncpdp_opaid[];
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'patient_id', p_patient_id,
    'insurance_id', p_insurance_id,
    'payer_id', p_payer_id,
    'site_id', p_site_id,
    'transaction_code', p_transaction_code,
    'parent_claim_no', p_parent_claim_no,
    'is_test', p_is_test
  );

  BEGIN  -- Start exception block
    -- Log function call
    PERFORM log_billing_function(
      'build_pricing_ncpdp_segment'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );
    -- Validate required parameters
    IF p_patient_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Patient ID cannot be null',
            'Validating required parameters in build_pricing_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );
        RAISE EXCEPTION 'Patient ID cannot be null';
    END IF;

    IF p_insurance_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Insurance ID cannot be null',
            'Validating required parameters in build_pricing_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );
        RAISE EXCEPTION 'Insurance ID cannot be null';
    END IF;

    IF p_payer_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Payer ID cannot be null',
            'Validating required parameters in build_pricing_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );
        RAISE EXCEPTION 'Payer ID cannot be null';
    END IF;

    IF p_site_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Site ID cannot be null',
            'Validating required parameters in build_pricing_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );
        RAISE EXCEPTION 'Site ID cannot be null';
    END IF;

    -- Additional numeric validation
    IF p_ing_cst_sub < 0 THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Ingredient cost cannot be negative',
            'Validating numeric parameters in build_pricing_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );
        RAISE EXCEPTION 'Ingredient cost cannot be negative';
    END IF;

    IF p_dispense_fee < 0 THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Dispense fee cannot be negative',
            'Validating numeric parameters in build_pricing_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );
        RAISE EXCEPTION 'Dispense fee cannot be negative';
    END IF;

    IF p_u_and_c_charge < 0 THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'U&C charge cannot be negative',
            'Validating numeric parameters in build_pricing_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );
        RAISE EXCEPTION 'U&C charge cannot be negative';
    END IF;

    IF p_gross_amount_due < 0 THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Gross amount due cannot be negative',
            'Validating numeric parameters in build_pricing_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );
        RAISE EXCEPTION 'Gross amount due cannot be negative';
    END IF;

    -- Get insurance settings and claim data once
    WITH insurance_info AS (
      SELECT * FROM get_insurance_claim_settings(p_insurance_id, p_site_id, p_previous_payer_id)
    ),
    claim_data AS (
      SELECT * FROM get_primary_ncpdp_claim_info(p_parent_claim_no)
    )
    -- Calculate each field individually
    SELECT
    CASE WHEN p_is_test THEN 'Yes'::text ELSE NULL::text END as is_test,
    p_parent_claim_no::text as parent_claim_no,
    p_transaction_code::text as transaction_code,
    p_payer_id::integer as payer_id,
    CASE 
        WHEN p_transaction_code NOT IN ('B1', 'B3') THEN NULL::numeric
        WHEN COALESCE(ins.swap_uc_and_gross, 'No') = 'Yes' AND p_gross_amount_due > p_u_and_c_charge 
        THEN ROUND(COALESCE(p_u_and_c_charge, 0.00)::numeric - COALESCE(p_dispense_fee, 0.00)::numeric, 2)
        ELSE COALESCE(p_ing_cst_sub, 0.00)::numeric
    END as ing_cst_sub,
    CASE 
        WHEN p_transaction_code IN ('B1', 'B3') AND p_dispense_fee IS NOT NULL 
            AND COALESCE(p_dispense_fee, 0.00) > 0 
        THEN ROUND(p_dispense_fee::numeric, 2)
        ELSE 0.00::numeric
    END as disp_fee_sub,
    CASE 
        WHEN p_transaction_code IN ('S1', 'S2') THEN ROUND(COALESCE(p_ing_cst_sub, 0.00)::numeric, 2)
        ELSE NULL::numeric
    END as pro_svc_fee_sub,
    CASE
        WHEN p_transaction_code NOT IN ('B1', 'B3', 'S1', 'S3') THEN NULL::numeric
        WHEN COALESCE(ins.ncpdp_pt_paid_amount_dx, 'No') = 'Yes' AND p_parent_claim_no IS NOT NULL 
        THEN ROUND(COALESCE(p_pt_pd_amt_sub, 0.00)::numeric, 2)
        ELSE 0.00::numeric
    END as pt_pd_amt_sub,
    CASE
        WHEN p_transaction_code NOT IN ('B1', 'B3', 'S1', 'S3') THEN NULL::numeric
        WHEN p_parent_claim_no IS NOT NULL THEN COALESCE(p_u_and_c_charge, 0.00)::numeric
        WHEN COALESCE(ins.swap_uc_and_gross, 'No') = 'Yes' AND p_gross_amount_due > p_u_and_c_charge 
        THEN ROUND(COALESCE(p_gross_amount_due, 0.00)::numeric, 2)
        ELSE COALESCE(p_u_and_c_charge, 0.00)::numeric
    END as u_and_c_charge,
    CASE
        WHEN p_transaction_code IN ('B1', 'B3') THEN 0.00::numeric
        ELSE NULL::numeric
    END as incv_amt_sub,
    CASE
        WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3') THEN 0.00::numeric
        ELSE NULL::numeric
    END as flat_tax_amt,
    CASE
        WHEN p_parent_claim_no IS NOT NULL THEN ROUND(COALESCE(p_gross_amount_due, 0.00)::numeric, 2)
        WHEN COALESCE(ins.swap_uc_and_gross, 'No') = 'Yes' AND p_gross_amount_due > p_u_and_c_charge 
        THEN ROUND(COALESCE(p_u_and_c_charge, 0.00)::numeric, 2)
        ELSE ROUND(COALESCE(p_gross_amount_due, 0.00)::numeric, 2)
    END as gross_amount_due,
    CASE
        WHEN p_transaction_code IN ('B1', 'B3') THEN COALESCE(cd.cost_basis, ins.base_ing_cost_on_id, '00')::text
        ELSE NULL::text
    END as cost_basis,
    CASE
        WHEN COALESCE(ins.ncpdp_send_sec_charge, 'No') = 'Yes' THEN cd.subform_oclaim
        ELSE NULL::ncpdp_opaid[]
    END as subform_oclaim
    FROM form_patient pt
    CROSS JOIN insurance_info ins
    LEFT JOIN claim_data cd ON true
    WHERE pt.id = p_patient_id 
    AND pt.archived IS NOT TRUE 
    AND pt.deleted IS NOT TRUE
    INTO v_result;

    RAISE LOG 'Pricing Segment: %', v_result;
    -- Validate we got a result
    IF v_result IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Failed to build pricing segment',
            'Validating result in build_pricing_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_pricing_ncpdp_segment',
                'patient_id', p_patient_id
            )
        );
        RAISE EXCEPTION 'Failed to build pricing segment for patient_id: %', p_patient_id;
    END IF;

    -- Log successful completion
    PERFORM log_billing_function(
        'build_pricing_ncpdp_segment'::tracked_function,
        v_params::jsonb,
        NULL::jsonb,
        NULL::text,
        clock_timestamp() - v_start_time
    );

    RETURN v_result;

    EXCEPTION WHEN OTHERS THEN
        -- Log error
        v_error_message := SQLERRM;
        
        -- Log to billing error log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            v_error_message,
            'Exception in build_pricing_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );

        -- Log to NCPDP function log
        PERFORM log_billing_function(
            'build_pricing_ncpdp_segment'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            v_error_message::text,
            clock_timestamp() - v_start_time
        );
        RAISE;
    END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;