CREATE OR REPLACE FUNCTION build_mm_subscriber_segment(
  p_patient_id integer,
  p_payer_id integer,
  p_insurance_id integer,
  p_parent_claim_no text DEFAULT NULL
) RETURNS mm_subscriber_segment AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result mm_subscriber_segment;
  v_error_message text;
  v_params jsonb;
  v_payment_responsibility_code text := 'P';
  v_is_patient_subscriber boolean := TRUE;
  v_address_info mm_address_info;
  v_contact_info mm_patient_contact_info;
  v_order_id integer;
  v_insurance_dep_id integer;
  v_parent_payer_id integer;
  v_override_payment_resp_code text;
BEGIN
  -- Check for null input parameters
  IF p_patient_id IS NULL THEN
    RAISE EXCEPTION 'Patient ID cannot be NULL';
  END IF;

  IF p_payer_id IS NULL THEN
    RAISE EXCEPTION 'Payer ID cannot be NULL';
  END IF;

  IF p_insurance_id IS NULL THEN
    RAISE EXCEPTION 'Insurance ID cannot be NULL';
  END IF;

  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'patient_id', p_patient_id,
    'payer_id', p_payer_id,
    'insurance_id', p_insurance_id,
    'parent_claim_no', p_parent_claim_no
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
      'build_mm_subscriber_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Building MM subscriber segment for patient ID: %, payer ID: %, insurance ID: %, parent claim no: %', p_patient_id, p_payer_id, p_insurance_id, p_parent_claim_no;

    -- Get parent payer_id if this is a COB claim
    IF p_parent_claim_no IS NOT NULL THEN
        SELECT pmc.payer_id
        INTO v_parent_payer_id
        FROM form_med_claim pmc
        WHERE pmc.claim_no = p_parent_claim_no
          AND pmc.deleted IS NOT TRUE 
          AND pmc.archived IS NOT TRUE;
    END IF;

    -- Check for payment_responsibility_level_code override from patient insurance medical conditions
    SELECT pimc.payment_responsibility_level_code
    INTO v_override_payment_resp_code
    FROM form_patient_insurance_med_cond pimc
    WHERE pimc.patient_id = p_patient_id
      AND pimc.active_payer_id = p_payer_id
      AND (v_parent_payer_id IS NULL OR pimc.previous_payer_id = v_parent_payer_id)
      AND pimc.deleted IS NOT TRUE 
      AND pimc.archived IS NOT TRUE
      AND pimc.payment_responsibility_level_code IS NOT NULL
    ORDER BY pimc.created_on DESC
    LIMIT 1;

    -- Determine payment responsibility level code
    IF v_override_payment_resp_code IS NOT NULL THEN
        v_payment_responsibility_code := v_override_payment_resp_code;
    ELSIF p_parent_claim_no IS NOT NULL THEN
        -- First try to get from payer's configured secondary claims code
        SELECT payer.mm_sec_claims_pr_code INTO v_payment_responsibility_code
        FROM form_payer payer
        WHERE payer.id = p_payer_id
          AND payer.deleted IS NOT TRUE 
          AND payer.archived IS NOT TRUE
          AND payer.mm_sec_claims_pr_code IS NOT NULL;

        -- If not set, calculate based on parent claim chain count
        IF v_payment_responsibility_code IS NULL THEN
            WITH RECURSIVE claim_chain AS (
                -- Base case: start with the current claim
                SELECT claim_no, parent_invoice_no, 1 as level
                FROM form_billing_invoice
                WHERE claim_no = p_parent_claim_no
                  AND archived IS NOT TRUE 
                  AND deleted IS NOT TRUE
                
                UNION ALL
                
                -- Recursive case: follow parent chain
                SELECT bi.claim_no, bi.parent_invoice_no, cc.level + 1
                FROM form_billing_invoice bi
                INNER JOIN claim_chain cc ON bi.claim_no = cc.parent_invoice_no
                WHERE bi.archived IS NOT TRUE 
                  AND bi.deleted IS NOT TRUE
                  AND cc.level < 10 -- prevent infinite recursion
            )
            SELECT 
                CASE 
                    WHEN MAX(level) = 1 THEN 'S' 
                    WHEN MAX(level) = 2 THEN 'T'  
                    WHEN MAX(level) = 3 THEN 'A' 
                    WHEN MAX(level) = 4 THEN 'B' 
                    WHEN MAX(level) = 5 THEN 'C'
                    WHEN MAX(level) = 6 THEN 'D'
                    WHEN MAX(level) = 7 THEN 'E'
                    WHEN MAX(level) = 8 THEN 'F'
                    WHEN MAX(level) = 9 THEN 'G'
                    WHEN MAX(level) = 10 THEN 'H'
                    ELSE 'U'  -- Other
                END
            INTO v_payment_responsibility_code
            FROM claim_chain;
        END IF;
    END IF;

    -- Default to Primary if still not set
    v_payment_responsibility_code := COALESCE(v_payment_responsibility_code, 'P');

    -- Check if patient is the subscriber (medical_relationship_id = '18' means self)
    SELECT COALESCE(pi.medical_relationship_id, '18') = '18'
    INTO v_is_patient_subscriber
    FROM form_patient_insurance pi
    WHERE pi.id = p_insurance_id
      AND pi.deleted IS NOT TRUE 
      AND pi.archived IS NOT TRUE;

    -- Get basic subscriber information
    IF v_is_patient_subscriber THEN
        -- Patient is subscriber - use patient information
        SELECT 
            p_insurance_id::integer,
            COALESCE(pi.medical_relationship_id, '18')::text,
            p_patient_id::integer,
            pt.firstname::text,
            pt.lastname::text,
            pt.middlename::text,
            TO_CHAR(pt.dob, 'MM/DD/YYYY')::text,
            CASE 
                WHEN pt.gender = 'Male' THEN 'M'
                WHEN pt.gender = 'Female' THEN 'F'
                ELSE 'U'
            END::text,
            pi.cardholder_id::text,
            pi.policy_number::text,
            pi.group_name::text,
            pi.group_number::text,
            v_payment_responsibility_code::text,
            pi.insurance_type_code::text
        INTO 
            v_result.insurance_id,
            v_result.medical_relationship_id,
            v_result.patient_id,
            v_result.first_name,
            v_result.last_name,
            v_result.middle_name,
            v_result.date_of_birth,
            v_result.gender,
            v_result.member_id,
            v_result.policy_number,
            v_result.group_number,
            v_result.payment_responsibility_level_code,
            v_result.insurance_type_code
        FROM form_patient_insurance pi
        INNER JOIN form_patient pt ON pt.id = pi.patient_id
        WHERE pi.id = p_insurance_id
          AND pi.deleted IS NOT TRUE 
          AND pi.archived IS NOT TRUE
          AND pt.deleted IS NOT TRUE 
          AND pt.archived IS NOT TRUE;

        -- Build address from patient home address
        -- Get Home or Shipping address, with Home as top priority
        SELECT 
            pa.street::text,
            pa.street2::text,
            pa.city::text,
            pa.state_id::text,
            pa.zip::text
        INTO 
            v_address_info.address1,
            v_address_info.address2,
            v_address_info.city,
            v_address_info.state,
            v_address_info.postal_code
        FROM form_patient_address pa
        WHERE pa.patient_id = p_patient_id
          AND pa.address_type IN ('Home', 'Shipping')
          AND pa.deleted IS NOT TRUE 
          AND pa.archived IS NOT TRUE
        ORDER BY 
            CASE pa.address_type 
                WHEN 'Home' THEN 1
                WHEN 'Shipping' THEN 2
                ELSE 3
            END,
            pa.created_on DESC
        LIMIT 1;

        -- Build contact information from patient
        SELECT 
            (pt.firstname || COALESCE(' ' || pt.middlename, '') || ' ' || pt.lastname)::text,
            pt.email::text,
            COALESCE(
                CASE pt.phone_primary 
                    WHEN 'Cell Phone' THEN pt.phone_cell
                    WHEN 'Home Phone' THEN pt.phone_home
                    WHEN 'Work Phone' THEN pt.phone_work
                    ELSE COALESCE(pt.phone_cell, pt.phone_home, pt.phone_work)
                END,
                pt.phone_home,
                pt.phone_work
            )::text
        INTO 
            v_contact_info.name,
            v_contact_info.email,
            v_contact_info.phone_number
        FROM form_patient pt
        WHERE pt.id = p_patient_id
          AND pt.deleted IS NOT TRUE 
          AND pt.archived IS NOT TRUE;

    ELSE
        -- Patient is dependent - use beneficiary information from insurance record
        SELECT 
            p_insurance_id::integer,
            pi.medical_relationship_id::text,
            p_patient_id::integer,
            pi.beneficiary_fname::text,
            pi.beneficiary_lname::text,
            pi.beneficiary_mname::text,
            TO_CHAR(pi.beneficiary_dob, 'MM/DD/YYYY')::text,
            CASE 
                WHEN pi.beneficiary_gender = 'M' THEN 'M'
                WHEN pi.beneficiary_gender = 'F' THEN 'F'
                ELSE 'U'
            END::text,
            pi.cardholder_id::text,
            pi.policy_number::text,
            pi.group_name::text,
            pi.group_number::text,
            v_payment_responsibility_code::text,
            pi.insurance_type_code::text
        INTO 
            v_result.insurance_id,
            v_result.medical_relationship_id,
            v_result.patient_id,
            v_result.first_name,
            v_result.last_name,
            v_result.middle_name,
            v_result.date_of_birth,
            v_result.gender,
            v_result.member_id,
            v_result.policy_number,
            v_result.group_number,
            v_result.payment_responsibility_level_code,
            v_result.insurance_type_code
        FROM form_patient_insurance pi
        WHERE pi.id = p_insurance_id
          AND pi.deleted IS NOT TRUE 
          AND pi.archived IS NOT TRUE;

        -- Build address from beneficiary address if available, otherwise patient address
        WITH pa AS (
            SELECT *
            FROM form_patient_address
            WHERE patient_id = (
                SELECT patient_id FROM form_patient_insurance WHERE id = p_insurance_id
            )
              AND address_type IN ('Home', 'Shipping')
              AND deleted IS NOT TRUE
              AND archived IS NOT TRUE
            ORDER BY 
                CASE WHEN address_type = 'Home' THEN 1
                     WHEN address_type = 'Shipping' THEN 2
                     ELSE 3 END,
                created_on DESC NULLS LAST
            LIMIT 1
        )
        SELECT 
            COALESCE(pi.beneficiary_address1, pa.street)::text,
            COALESCE(pi.beneficiary_address2, pa.street2)::text,
            COALESCE(pi.beneficiary_city, pa.city)::text,
            COALESCE(pi.beneficiary_state_id, pa.state_id)::text,
            COALESCE(pi.beneficiary_postal_code, pa.zip)::text
        INTO 
            v_address_info.address1,
            v_address_info.address2,
            v_address_info.city,
            v_address_info.state,
            v_address_info.postal_code
        FROM form_patient_insurance pi
        LEFT JOIN pa ON TRUE
        WHERE pi.id = p_insurance_id
          AND pi.deleted IS NOT TRUE 
          AND pi.archived IS NOT TRUE;

        -- Build contact information from beneficiary or insurance contact
        SELECT 
            COALESCE(
                CASE 
                    WHEN pi.beneficiary_fname IS NOT NULL THEN 
                        (pi.beneficiary_fname || COALESCE(' ' || pi.beneficiary_mname, '') || ' ' || pi.beneficiary_lname)
                    ELSE pi.contact_name
                END,
                (pt.firstname || COALESCE(' ' || pt.middlename, '') || ' ' || pt.lastname)
            )::text,
            COALESCE(pi.contact_email, pt.email)::text,
            COALESCE(pi.contact_phone, pt.phone_cell, pt.phone_home, pt.phone_work)::text
        INTO 
            v_contact_info.name,
            v_contact_info.email,
            v_contact_info.phone_number
        FROM form_patient_insurance pi
        INNER JOIN form_patient pt ON pt.id = pi.patient_id
        WHERE pi.id = p_insurance_id
          AND pi.deleted IS NOT TRUE 
          AND pi.archived IS NOT TRUE
          AND pt.deleted IS NOT TRUE 
          AND pt.archived IS NOT TRUE;
    END IF;

    -- Validate that we found the insurance record
    IF v_result.insurance_id IS NULL THEN
        RAISE EXCEPTION 'Insurance record not found for ID: %', p_insurance_id;
    END IF;

    -- Set address and contact arrays
    v_result.address := ARRAY[v_address_info];
    v_result.contact_information := ARRAY[v_contact_info];

    RAISE LOG 'MM subscriber segment build result: %', v_result;

    -- Log success
    PERFORM log_billing_function(
      'build_mm_subscriber_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_mm_subscriber_segment',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'build_mm_subscriber_segment',
        'patient_id', p_patient_id,
        'payer_id', p_payer_id,
        'insurance_id', p_insurance_id,
        'parent_claim_no', p_parent_claim_no
      )
    );

    PERFORM log_billing_function(
      'build_mm_subscriber_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;