CREATE OR REPLACE FUNCTION create_insurance_invoice_json(
  p_insurance_id integer,
  p_payer_id integer,
  p_patient_id integer,
  p_site_id integer,
  p_charge_lines charge_line_with_split[],
  p_date_of_service_start date,
  p_date_of_service_end date DEFAULT NULL,
  p_previous_payer_id integer DEFAULT NULL,
  p_parent_invoice_no text DEFAULT NULL
) RETURNS json AS $BODY$
DECLARE
    v_start_time timestamp;
    v_params jsonb;
    v_rx_no text;
    v_rx_id integer[];
    v_billing_method_id text;
    v_prescriber_id integer;
    v_day_supply integer;
    v_total_billed numeric;
    v_total_expected numeric;
    v_total_cost numeric;
    v_total_pt_pay numeric;
    v_total_balance_due numeric;
    v_type_id text;
    v_bill_for_denial text;
    v_confirmed text;
    v_next_insurance_id integer;
    v_rx_count integer;
    v_ncpdp_claim ncpdp_record;
    v_ncpdp_json json := NULL;
    v_mm_1500_json jsonb := NULL;
    v_mm_json jsonb := NULL;
    v_parent_claim_no text DEFAULT NULL;
    v_order_item_id integer DEFAULT NULL;
    v_orderp_item_id integer DEFAULT NULL;
    v_master_invoice_no text DEFAULT NULL;
    v_delivery_ticket_id integer DEFAULT NULL;
    v_ticket_no text DEFAULT NULL;
    v_error_message text;
    v_account_id integer;
    v_insurance_type text;
    v_invoice_placeholder text;
    v_result json;
    v_patient_invoice_record record;
BEGIN
  -- Same code as create_insurance_invoice up to the point where it builds the final record
  -- Record start time for performance tracking
  v_start_time := clock_timestamp();

  -- Build minimal parameters JSON for logging
  v_params := jsonb_build_object(
      'insurance_id', p_insurance_id,
      'payer_id', p_payer_id,
      'patient_id', p_patient_id,
      'site_id', p_site_id,
      'charge_lines_count', CASE WHEN p_charge_lines IS NULL THEN 0 ELSE array_length(p_charge_lines, 1) END,
      'date_of_service_start', p_date_of_service_start,
      'previous_payer_id', p_previous_payer_id,
      'parent_invoice_no', p_parent_invoice_no,
      'date_of_service_end', p_date_of_service_end
  );

  -- Validate parameters
    IF p_site_id IS NULL OR p_insurance_id IS NULL OR p_payer_id IS NULL OR p_patient_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Site ID, Insurance ID, Payer ID, and Patient ID are required',
            'Validating parameters in create_insurance_invoice',
            'FUNCTION',
            current_schema(),
            'form_billing_invoice',
            v_params
        );
        RAISE EXCEPTION 'Site ID, Insurance ID, Payer ID, and Patient ID are required';
    END IF;

    IF p_date_of_service_start IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Date of Service Start is required',
            'Validating parameters in create_insurance_invoice',
            'FUNCTION',
            current_schema(),
            'form_billing_invoice',
            v_params
        );
        RAISE EXCEPTION 'Date of Service Start is required';
    END IF;

    IF p_charge_lines IS NULL OR array_length(p_charge_lines, 1) = 0 THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Charge Lines are required',
            'Validating parameters in create_insurance_invoice',
            'FUNCTION',
            current_schema(),
            'form_billing_invoice',
            v_params
        );
        RAISE EXCEPTION 'Charge Lines are required to build an invoice';
    END IF;

  v_insurance_type = CASE WHEN p_payer_id = 1 THEN 'Patient' ELSE 'Payer' END;

  -- Get payer and insurance information once
  WITH payer_info AS (
    SELECT 
      py.billing_method_id,
      py.type_id,
      ins.bill_for_denial,
      ins.next_insurance_id
    FROM form_patient_insurance ins
    INNER JOIN form_payer py ON py.id = ins.payer_id
    WHERE ins.id = p_insurance_id
      AND ins.archived IS NOT TRUE
      AND ins.deleted IS NOT TRUE
      AND py.archived IS NOT TRUE
      AND py.deleted IS NOT TRUE
  )
  SELECT
    billing_method_id::text as billing_method_id,
    type_id::text as type_id,
    bill_for_denial::text as bill_for_denial,
    next_insurance_id
  INTO
    v_billing_method_id,
    v_type_id,
    v_bill_for_denial,
    v_next_insurance_id
  FROM payer_info;

  v_account_id := get_account_id(p_patient_id, p_payer_id);
  IF v_account_id IS NULL THEN
    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      'Patient account not found',
      'Validating parameters in create_insurance_invoice',
      'FUNCTION',
      current_schema(),
      'form_billing_invoice',
      v_params
    );
    RAISE EXCEPTION 'Payer account not found';
  END IF;

  -- Process charge line data
  WITH charge_analysis AS (
      SELECT
          MAX(lcl.ticket_no) as ticket_no,
          MAX(lcl.rx_no) as rx_no,
          COUNT(DISTINCT lcl.rx_no) as rx_count,
          SUM((lcl.billed)::numeric) as total_billed,
          SUM((lcl.expected)::numeric) as total_expected,
          SUM((lcl.total_cost)::numeric) as total_cost,
          SUM((lcl.copay)::numeric) as total_pt_pay,
          array_agg(DISTINCT (lcl.order_rx_id)::integer) FILTER (WHERE lcl.order_rx_id IS NOT NULL) as rx_id
      FROM unnest(p_charge_lines) as lcl
  )
  SELECT
      ticket_no,
      rx_no,
      rx_count,
      total_billed,
      total_expected,
      total_cost,
      total_pt_pay,
      total_expected AS total_balance_due,
      rx_id
  INTO
      v_ticket_no,
      v_rx_no,
      v_rx_count,
      v_total_billed,
      v_total_expected,
      v_total_cost,
      v_total_pt_pay,
      v_total_balance_due,
      v_rx_id
  FROM charge_analysis;

  -- Get the Delivery Ticket ID if ticket number is available
  IF v_ticket_no IS NOT NULL THEN
    SELECT id 
    INTO v_delivery_ticket_id 
    FROM form_careplan_delivery_tick
    WHERE ticket_no = v_ticket_no
      AND archived IS NOT TRUE
      AND deleted IS NOT TRUE;
  END IF;

  -- Validate RX IDs
    IF v_rx_id IS NULL OR array_length(v_rx_id, 1) = 0 THEN
      INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
      ) VALUES (
        'No order_rx_id found in charge lines',
        'Validating charge lines in create_insurance_invoice',
        'FUNCTION',
        current_schema(),
        'form_billing_invoice',
        v_params
      );
      RAISE EXCEPTION 'No prescriptions found in charge lines';
    END IF;

  -- Get parent invoice information if needed
  IF p_parent_invoice_no IS NOT NULL THEN
    WITH parent_invoice_data AS (
      SELECT 
        bi.master_invoice_no::text as master_invoice_no,
        COALESCE(pc.claim_no, mc.claim_no, nc.claim_no)::text AS parent_claim_no
      FROM form_billing_invoice bi
      LEFT JOIN sf_form_billing_invoice_to_med_claim_1500 bitpc ON bitpc.form_billing_invoice_fk = bi.id 
        AND bitpc.archive IS NOT TRUE 
        AND bitpc.delete IS NOT TRUE
      LEFT JOIN form_med_claim_1500 pc ON pc.id = bitpc.form_med_claim_1500_fk 
        AND COALESCE(pc.void, 'No') <> 'Yes'
        AND pc.archived IS NOT TRUE
        AND pc.deleted IS NOT TRUE
        AND bi.billing_method_id = 'cms1500'
      LEFT JOIN sf_form_billing_invoice_to_med_claim bitmc ON bitmc.form_billing_invoice_fk = bi.id 
        AND bitmc.archive IS NOT TRUE
        AND bitmc.delete IS NOT TRUE
      LEFT JOIN form_med_claim mc ON mc.id = bitmc.form_med_claim_fk 
        AND COALESCE(mc.void, 'No') <> 'Yes' 
        AND mc.archived IS NOT TRUE 
        AND mc.deleted IS NOT TRUE 
        AND bi.billing_method_id = 'mm'
      LEFT JOIN sf_form_billing_invoice_to_ncpdp bitnc ON bitnc.form_billing_invoice_fk = bi.id 
        AND bitnc.archive IS NOT TRUE
        AND bitnc.delete IS NOT TRUE
      LEFT JOIN form_ncpdp nc ON nc.id = bitnc.form_ncpdp_fk 
        AND COALESCE(nc.void, 'No') <> 'Yes' 
        AND nc.archived IS NOT TRUE
        AND nc.deleted IS NOT TRUE
        AND bi.billing_method_id = 'ncpdp'
      WHERE bi.invoice_no = p_parent_invoice_no
    )
    SELECT 
      master_invoice_no,
      parent_claim_no
    INTO 
      v_master_invoice_no,
      v_parent_claim_no
    FROM parent_invoice_data;

    -- Validate parent Claim #
      IF v_parent_claim_no IS NULL THEN
        INSERT INTO billing_error_log (
          error_message,
          error_context,
          error_type,
          schema_name,
          table_name,
          additional_details
        ) VALUES (
          'Parent invoice Claim # not found',
          'Validating parameters in create_insurance_invoice',
          'FUNCTION',
          current_schema(),
          'form_billing_invoice',
          v_params
        );
        RAISE EXCEPTION 'Parent invoice Claim # not found';
      END IF;
  END IF;

  RAISE LOG 'Billing Method:%', v_billing_method_id;
  -- Process NCPDP data if applicable
  IF v_billing_method_id = 'ncpdp' THEN
    -- Multiple prescriptions check
      IF v_rx_count > 1 THEN
        INSERT INTO billing_error_log (
          error_message,
          error_context, 
          error_type,
          schema_name,
          table_name,
          additional_details
        ) VALUES (
          'Multiple different prescriptions found in charge lines',
          'Validating prescriptions in create_insurance_invoice',
          'FUNCTION',
          current_schema(),
          'form_billing_invoice',
          v_params
        );
        RAISE EXCEPTION 'All charge lines must have the same prescription';
      END IF;
    -- Get prescription information
    WITH rx_data AS (
      SELECT 
        rx.prescriber_id,
        rx.order_item_id AS coi_id,
        rx.orderp_item_id AS cpi_id,
        rx.day_supply
      FROM vw_rx_order rx 
      WHERE rx.rx_no = v_rx_no
    )
    SELECT 
      prescriber_id,
      coi_id,
      cpi_id,
      day_supply
    INTO 
      v_prescriber_id,
      v_order_item_id,
      v_orderp_item_id,
      v_day_supply
    FROM rx_data;

    -- Validate prescription data
    IF v_prescriber_id IS NULL THEN
      INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
      ) VALUES (
        'Prescriber not found',
        'Validating prescriptions in create_insurance_invoice',
        'FUNCTION',
        current_schema(),
        'form_billing_invoice',
        v_params
      );
      RAISE EXCEPTION 'Prescriber not found on referral';
    END IF;

    IF v_order_item_id IS NULL AND v_orderp_item_id IS NULL THEN
      INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
      ) VALUES (
        'Order record not found',
        'Validating prescriptions in create_insurance_invoice',
        'FUNCTION',
        current_schema(),
        'form_billing_invoice',
        v_params
      );
      RAISE EXCEPTION 'Order record not found';
    END IF;

    IF v_day_supply IS NULL OR v_day_supply <= 0 THEN
      INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
      ) VALUES (
        'Day supply is required and not found on prescription',
        'Validating prescriptions in create_insurance_invoice',
        'FUNCTION',
        current_schema(),
        'form_billing_invoice',
        v_params
      );
      RAISE EXCEPTION 'Day supply is required and not found on prescription';
    END IF;

    -- Build the NCPDP claim
    v_ncpdp_claim := build_ncpdp_claim(
      p_insurance_id,
      p_payer_id,
      p_patient_id,
      p_site_id,
      v_prescriber_id,
      p_charge_lines,
      p_date_of_service_start,
      v_day_supply,
      p_previous_payer_id,
      v_parent_claim_no,
      v_rx_no,
      v_order_item_id,
      v_orderp_item_id,
      'B1'::text,
      NULL::text,
      NULL::text,
      NULL::text,
      NULL::text,
      NULL::text,
      FALSE,
      NULL::text
    );

    v_ncpdp_json := ncpdp_record_to_json(v_ncpdp_claim);
  ELSIF v_billing_method_id = 'cms1500' THEN
    -- Build the CMS-1500 claim
    -- For CMS-1500, we need a prescriber (patient_prescriber_id)
    -- Get the prescriber from the first charge line's rx_no
    DECLARE
        v_patient_prescriber_id integer;
    BEGIN
        -- Get prescriber from prescription
        SELECT pp.id
        INTO v_patient_prescriber_id
        FROM vw_rx_order rx
        INNER JOIN form_patient_prescriber pp ON pp.id = rx.prescriber_id
          AND pp.patient_id = rx.patient_id
          AND pp.deleted IS NOT TRUE 
          AND pp.archived IS NOT TRUE
        WHERE rx.rx_no = v_rx_no
        ORDER BY pp.created_on DESC
        LIMIT 1;

        IF v_patient_prescriber_id IS NULL THEN
          INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
          ) VALUES (
            'Patient prescriber not found for CMS-1500 claim',
            'Building CMS-1500 claim in create_insurance_invoice_json',
            'FUNCTION',
            current_schema(),
            'form_billing_invoice',
            v_params
          );
          RAISE EXCEPTION 'Patient prescriber not found for CMS-1500 claim';
        END IF;

        -- Build the CMS-1500 claim JSON
        v_mm_1500_json := mm_1500_record_to_json(
          build_mm_1500_claim(
            p_insurance_id,
            p_payer_id,
            p_patient_id,
            p_site_id,
            p_date_of_service_start,
            p_date_of_service_end,
            p_charge_lines,
            v_parent_claim_no,
            v_patient_prescriber_id
          )
        );
    END;
  ELSIF v_billing_method_id = 'mm' THEN
    -- Build the electronic medical claim
    v_mm_json := mm_record_to_json(
      build_mm_claim(
        p_insurance_id,
        p_payer_id,
        p_patient_id,
        p_site_id,
        p_date_of_service_start,
        p_charge_lines,
        p_date_of_service_end,
        v_parent_claim_no
      )
    );
  END IF;

  -- Get delivery ticket confirmation status
  v_confirmed := 'No'; -- Default value
  IF v_delivery_ticket_id IS NOT NULL THEN
    SELECT 
      COALESCE(dt.confirmed, 'No')::text AS confirmed
    INTO 
      v_confirmed
    FROM form_careplan_delivery_tick dt
    WHERE dt.id = v_delivery_ticket_id
      AND dt.archived IS NOT TRUE
      AND dt.deleted IS NOT TRUE
      AND COALESCE(dt.void, 'No') != 'Yes';
  END IF;

  v_invoice_placeholder := gen_random_uuid();
  
  IF v_billing_method_id = 'generic' THEN

    SELECT * FROM vw_patient_invoice_information
    INTO v_patient_invoice_record
    WHERE patient_id = p_patient_id
    AND payer_id = p_payer_id;

    v_result := json_build_object(
      'invoice_record', json_build_object(
      'series_uuid', 'INVOICE_NO_PLACEHOLDER_' || v_invoice_placeholder,
      'invoice_no', 'INVOICE_NO_PLACEHOLDER_' || v_invoice_placeholder,
      'master_invoice_no', CASE 
        WHEN COALESCE(v_bill_for_denial, 'No') = 'Yes' THEN NULL 
        ELSE COALESCE(v_master_invoice_no, 'INVOICE_NO_PLACEHOLDER_' || v_invoice_placeholder) 
      END,
      'parent_invoice_no', p_parent_invoice_no,
      'site_id', p_site_id,
      'rx_id', v_rx_id,
      'delivery_ticket_id', v_delivery_ticket_id,
      'patient_id', p_patient_id,
      'insurance_id', p_insurance_id,
      'payer_id', p_payer_id,
      'next_insurance_id', CASE 
        WHEN COALESCE(v_bill_for_denial, 'No') = 'Yes' THEN v_next_insurance_id
        ELSE NULL
      END,
      'on_hold', NULL,
      'type_id', v_type_id,
      'status', CASE 
        WHEN COALESCE(v_confirmed, 'No') = 'Yes' THEN 'Confirmed' 
        ELSE 'Open'
      END,
      'invoice_type', v_insurance_type,
      'account_id', v_account_id,
      'billed_datetime', get_site_timestamp(p_site_id),
      'billing_method_id', v_billing_method_id,
      'date_of_service', p_date_of_service_start,
      'date_of_service_end', p_date_of_service_end,
      'total_billed', v_total_billed,
      'total_expected', v_total_expected,
      'total_cost', v_total_cost,
      'total_balance_due', v_total_balance_due,
      'due_days', v_patient_invoice_record.due_days,
      'services_provided', v_patient_invoice_record.services_provided,
      'terms', v_patient_invoice_record.terms,
      'free_text', v_patient_invoice_record.free_text,
      'responsible_party', v_patient_invoice_record.responsible_party,
      'send_bill_to', v_patient_invoice_record.send_bill_to,
      'billing_city', v_patient_invoice_record.billing_city,
      'billing_state', v_patient_invoice_record.billing_state,
      'billing_zip', v_patient_invoice_record.billing_zip,
      'policy_no', v_patient_invoice_record.policy_no,
      'billing_group_no', v_patient_invoice_record.billing_group_no,
      'patient_name', v_patient_invoice_record.patient_name,
      'patient_address', v_patient_invoice_record.patient_address,
      'patient_city', v_patient_invoice_record.patient_city,
      'patient_state', v_patient_invoice_record.patient_state,
      'patient_zip', v_patient_invoice_record.patient_zip,
      'insured_name', v_patient_invoice_record.insured_name,
      'insured_address', v_patient_invoice_record.insured_address,
      'insured_city', v_patient_invoice_record.insured_city,
      'insured_state', v_patient_invoice_record.insured_state,
      'insured_zip', v_patient_invoice_record.insured_zip
    ),
    'charge_lines', array_to_json(p_charge_lines)
  );

  ELSE
    v_result := json_build_object(
      'invoice_record', json_build_object(
      'is_dirty', CASE WHEN v_billing_method_id IN ('ncpdp', 'mm', 'cms1500') THEN 'Yes' ELSE NULL::text END,
      'series_uuid', 'INVOICE_NO_PLACEHOLDER_' || v_invoice_placeholder,
      'invoice_no', 'INVOICE_NO_PLACEHOLDER_' || v_invoice_placeholder,
      'master_invoice_no', CASE 
        WHEN COALESCE(v_bill_for_denial, 'No') = 'Yes' THEN NULL::text
        ELSE COALESCE(v_master_invoice_no, 'INVOICE_NO_PLACEHOLDER_' || v_invoice_placeholder) 
      END,
      'parent_invoice_no', p_parent_invoice_no,
      'site_id', p_site_id,
      'rx_id', v_rx_id,
      'delivery_ticket_id', v_delivery_ticket_id,
      'patient_id', p_patient_id,
      'insurance_id', p_insurance_id,
      'payer_id', p_payer_id,
      'next_insurance_id', CASE 
        WHEN COALESCE(v_bill_for_denial, 'No') = 'Yes' THEN v_next_insurance_id
        ELSE NULL
      END,
      'on_hold', NULL,
      'type_id', v_type_id,
      'status', CASE 
        WHEN COALESCE(v_confirmed, 'No') = 'Yes' THEN 'Confirmed' 
        ELSE 'Open'
      END,
      'invoice_type', v_insurance_type,
      'account_id', v_account_id,
      'bill_for_denial', v_bill_for_denial,
      'billed_datetime', get_site_timestamp(p_site_id),
      'billing_method_id', v_billing_method_id,
      'date_of_service', p_date_of_service_start,
      'date_of_service_end', p_date_of_service_end,
      'subform_pharmacy', CASE 
        WHEN v_billing_method_id = 'ncpdp' AND v_ncpdp_json IS NOT NULL THEN 
          json_build_array(v_ncpdp_json::json)
      END,
      'subform_medical', CASE 
        WHEN v_billing_method_id = 'mm' AND v_mm_json IS NOT NULL THEN 
          json_build_array(v_mm_json::json)
      END,
      'subform_1500', CASE  
        WHEN v_billing_method_id = 'cms1500' AND v_mm_1500_json IS NOT NULL THEN 
          json_build_array(v_mm_1500_json::json)
      END,
      'total_billed', v_total_billed,
      'total_expected', v_total_expected,
      'total_cost', v_total_cost,
      'total_pt_pay', v_total_pt_pay,
      'total_balance_due', v_total_balance_due
    ),
    'charge_lines', array_to_json(p_charge_lines)
  );

  END IF;
  -- Build JSON directly instead of creating a record - THIS IS THE KEY CHANGE
  
  v_result := json_strip_nulls(v_result);
  
  -- Log success with minimal info
  BEGIN
    PERFORM log_billing_function(
      'create_insurance_invoice_json'::tracked_function,
      v_params,
      jsonb_build_object(
        'invoice_no', 'INVOICE_NO_PLACEHOLDER_' || v_invoice_placeholder,
        'billing_method_id', v_billing_method_id,
        'status', CASE WHEN COALESCE(v_confirmed, 'No') = 'Yes' THEN 'Confirmed' ELSE 'Open' END,
        'parent_claim_no', v_parent_claim_no
      ),
      NULL::text,
      clock_timestamp() - v_start_time
    );
  EXCEPTION WHEN OTHERS THEN
    -- Ignore logging errors
    NULL;
  END;
  
  RETURN v_result;
  
EXCEPTION WHEN OTHERS THEN
  -- Get error message
  v_error_message := SQLERRM;
  
  -- Log error
  INSERT INTO billing_error_log (
    error_message,
    error_context,
    error_type,
    schema_name,
    table_name,
    additional_details
  ) VALUES (
    v_error_message,
    'Exception in create_insurance_invoice_json',
    'FUNCTION',
    current_schema(),
    'form_billing_invoice',
    v_params
  );

  -- Log to function log if available
  BEGIN
    PERFORM log_billing_function(
      'create_insurance_invoice_json'::tracked_function,
      v_params::jsonb,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
  EXCEPTION WHEN OTHERS THEN
    -- If log_billing_function fails, don't mask the original error
    NULL;
  END;

  -- Re-raise the original exception
  RAISE;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;
