-- Update the build_cob_ncpdp_segment function to ensure COB data is always included for secondary claims

CREATE OR REPLACE FUNCTION build_cob_ncpdp_segment(
    p_patient_id integer,
    p_insurance_id integer,
    p_site_id integer,
    p_transaction_code text,
    p_parent_claim_no text
) RETURNS ncpdp_cob AS $BODY$
DECLARE
    v_start_time timestamp;
    v_execution_time interval;
    v_result ncpdp_cob;
    v_error_message text;
    v_params jsonb;
    v_root_claim text;
    v_type_id text;
    v_is_copay_card boolean := FALSE;
    v_send_primary_payer_amt_paid text;
    v_opayer_data ncpdp_cob_opayer[];
    v_reject_codes text[];
    v_paid_data ncpdp_cob_opaid[];
    v_resp_data ncpdp_cob_ptresp[];
    v_benefit_data ncpdp_cob_benefit[];
BEGIN
    -- Record start time
    v_start_time := clock_timestamp();

    -- Build parameters JSON for logging
    v_params := jsonb_build_object(
        'patient_id', p_patient_id,
        'insurance_id', p_insurance_id,
        'site_id', p_site_id,
        'transaction_code', p_transaction_code,
        'parent_claim_no', p_parent_claim_no
    );

    BEGIN  -- Start exception block
        -- Log function call
        PERFORM log_billing_function(
            'build_cob_ncpdp_segment'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            NULL::text,
            clock_timestamp() - v_start_time
        );

        -- Validate required parameters
        IF p_patient_id IS NULL THEN
            RAISE EXCEPTION 'Patient ID cannot be null';
        END IF;

        IF p_insurance_id IS NULL THEN
            RAISE EXCEPTION 'Insurance ID cannot be null';
        END IF;

        IF p_site_id IS NULL THEN
            RAISE EXCEPTION 'Site ID cannot be null';
        END IF;

        IF p_parent_claim_no IS NULL THEN
            RAISE EXCEPTION 'Parent Claim # cannot be null for COB segment';
        END IF;

        -- Get root claim
        SELECT get_primary_claim(p_parent_claim_no) INTO v_root_claim;
        
        RAISE LOG 'Building COB segment for parent claim: % and root claim: %', p_parent_claim_no, v_root_claim;
        
        -- Check if this is a copay card
        SELECT (py.type_id = 'COPAY'), py.type_id INTO v_is_copay_card, v_type_id
        FROM form_payer py
        INNER JOIN form_patient_insurance pi ON pi.payer_id = py.id
        WHERE pi.id = p_insurance_id
        AND py.archived IS NOT TRUE
        AND py.deleted IS NOT TRUE
        AND pi.archived IS NOT TRUE
        AND pi.deleted IS NOT TRUE;
        
        RAISE LOG 'Is copay card: %', v_is_copay_card;
        
        -- Get insurance settings once
        WITH insurance_info AS (
            SELECT * FROM get_insurance_claim_settings(p_insurance_id, p_site_id, NULL::integer)
        ),
        -- Use explicit CTEs to avoid ambiguous columns
        other_payer_data AS (
            SELECT get_other_payer_data(v_root_claim, p_patient_id, p_insurance_id)::ncpdp_cob_opayer[] as op_data
        ),
        claim_reject_codes AS (
            SELECT get_claim_reject_codes(v_root_claim)::text[] as rc_data
        ),
        other_payer_paid_data AS (
            SELECT get_other_payer_paid_data(v_root_claim)::ncpdp_cob_opaid[] as pd_data
        ),
        patient_responsibility_data AS (
            SELECT get_patient_responsibility_data(v_root_claim, p_insurance_id, p_site_id)::ncpdp_cob_ptresp[] as pr_data
        ),
        benefits_data AS (
            SELECT get_benefits_data(v_root_claim)::ncpdp_cob_benefit[] as bd_data
        )
        SELECT 
            CASE
                WHEN v_is_copay_card OR COALESCE(ins.ncpdp_sec_send_other_payer, 'No') = 'Yes' 
                THEN (SELECT op_data FROM other_payer_data)
                ELSE NULL::ncpdp_cob_opayer[]
            END AS opayer_data,
            (SELECT rc_data FROM claim_reject_codes)::text[] AS reject_codes,
            CASE
                WHEN COALESCE(v_type_id, 'OTHER') <> 'PAP' AND (v_is_copay_card OR COALESCE(ins.ncpdp_send_primary_payer_amt_paid, 'No') = 'Yes') 
                THEN (SELECT pd_data FROM other_payer_paid_data)::ncpdp_cob_opaid[]
                ELSE NULL::ncpdp_cob_opaid[]
            END AS paid_data,
            CASE 
                WHEN v_type_id = 'PAP' THEN NULL::ncpdp_cob_ptresp[] 
                ELSE (SELECT pr_data FROM patient_responsibility_data)::ncpdp_cob_ptresp[]
            END AS resp_data,
            CASE 
                WHEN ins.ncpdp_send_benefits_stage = 'Yes' THEN (SELECT bd_data FROM benefits_data)::ncpdp_cob_benefit[] 
                ELSE NULL::ncpdp_cob_benefit[]
            END AS benefit_data,
            ins.ncpdp_send_primary_payer_amt_paid::text as send_primary_payer_amt_paid
        INTO v_opayer_data, v_reject_codes, v_paid_data, v_resp_data, v_benefit_data,v_send_primary_payer_amt_paid
        FROM form_patient pt
        CROSS JOIN insurance_info ins
        WHERE pt.id = p_patient_id 
            AND pt.archived IS NOT TRUE 
            AND pt.deleted IS NOT TRUE;

        -- Build the result using the defined composite type
        SELECT
            p_patient_id::integer AS patient_id,
            p_transaction_code::text AS transaction_code,
            p_insurance_id::integer AS pinsurance_id,
            v_opayer_data::ncpdp_cob_opayer[] AS subform_opayer,
            v_reject_codes::text[] AS other_reject_code,
            v_paid_data::ncpdp_cob_opaid[] AS subform_paid,
            v_resp_data::ncpdp_cob_ptresp[] AS subform_resp,
            v_benefit_data::ncpdp_cob_benefit[] AS subform_benefit,
            -- Calculate paid_total
            CASE
                WHEN v_type_id = 'PAP' THEN NULL::numeric
                WHEN v_is_copay_card OR COALESCE(v_send_primary_payer_amt_paid, 'No') = 'Yes' THEN 
                    COALESCE((
                        SELECT SUM(COALESCE(elem.other_payer_amount_paid, 0)::numeric)::numeric
                        FROM unnest(v_paid_data) elem
                    ), 0)::numeric
                ELSE NULL::numeric
            END::numeric AS paid_total,
            -- Calculate pt_responsability_total
            CASE 
                WHEN v_type_id = 'PAP' THEN NULL::numeric
                ELSE COALESCE((
                    SELECT SUM(COALESCE(elem.pt_resp_amt, 0)::numeric)::numeric
                    FROM unnest(v_resp_data) elem
                ), 0)::numeric
            END::numeric AS pt_responsability_total
        INTO v_result;

        -- Validate we got a result
        IF v_result IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Failed to build COB segment',
                'Validating result in build_cob_ncpdp_segment',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                jsonb_build_object(
                    'function_name', 'build_cob_ncpdp_segment',
                    'patient_id', p_patient_id
                )
            );
            RAISE EXCEPTION 'Failed to build COB segment for patient_id: %', p_patient_id;
        END IF;

        -- Log successful completion
        PERFORM log_billing_function(
            'build_cob_ncpdp_segment'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            NULL::text,
            clock_timestamp() - v_start_time
        );

        RETURN v_result;

    EXCEPTION WHEN OTHERS THEN
        -- Log error
        v_error_message := SQLERRM;
        
        -- Log to billing error log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            v_error_message,
            'Exception in build_cob_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            v_params
        );

        -- Log to NCPDP function log
        PERFORM log_billing_function(
            'build_cob_ncpdp_segment'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            v_error_message::text,
            clock_timestamp() - v_start_time
        );
        RAISE;
    END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;