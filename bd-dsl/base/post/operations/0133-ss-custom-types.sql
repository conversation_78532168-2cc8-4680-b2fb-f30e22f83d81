-- bd-dsl/base/post/operations/0132-ss-custom-types.sql

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ss_allergy_type') THEN
        CREATE TYPE ss_allergy_type AS (
            -- direction TEXT, -- This will likely be part of the main ss_message
            source TEXT, -- Body.<MessageType>.AllergyOrAdverseEvent.Allergies.SourceOfInformation (P, C)
            effective_date DATE, -- Body.<MessageType>.AllergyOrAdverseEvent.Allergies.EffectiveDate.Date
            expiration_date DATE, -- Body.<MessageType>.AllergyOrAdverseEvent.Allergies.ExpirationDate.Date
            adverse_event_code_id TEXT, -- Body.<MessageType>.AllergyOrAdverseEvent.Allergies.AdverseEvent.Code (SNOMED)
            adverse_event_text TEXT, -- Body.<MessageType>.AllergyOrAdverseEvent.Allergies.AdverseEvent.Text
            drug_product_code TEXT, -- Body.<MessageType>.AllergyOrAdverseEvent.Allergies.DrugProductCoded.Code
            drug_product_qualifier_id TEXT, -- Body.<MessageType>.AllergyOrAdverseEvent.Allergies.DrugProductCoded.Qualifier
            drug_product_text TEXT, -- Body.<MessageType>.AllergyOrAdverseEvent.Allergies.DrugProductCoded.Text
            reaction_code_id TEXT, -- Body.<MessageType>.AllergyOrAdverseEvent.Allergies.ReactionCoded.Code (SNOMED)
            reaction_text TEXT, -- Body.<MessageType>.AllergyOrAdverseEvent.Allergies.ReactionCoded.Text
            severity_code_id TEXT, -- Body.<MessageType>.AllergyOrAdverseEvent.Allergies.SeverityCoded.Code (SNOMED)
            severity_text TEXT -- Body.<MessageType>.AllergyOrAdverseEvent.Allergies.SeverityCoded.Text
            -- NoKnownAllergies (nka) is a top-level field in ss_message
        );
    END IF;
END $$;

-- Appended type for ss_benefit
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ss_benefit_type') THEN
        DROP TYPE ss_benefit_type CASCADE;
    END IF;
    CREATE TYPE ss_benefit_type AS (
        insurance_id INTEGER,   -- Internal link, to be resolved/mapped
        payer_id INTEGER,       -- Internal link, to be resolved/mapped
        payer_type_id TEXT,     -- Body.<MessageType>.BenefitsCoordination.PayerType
        payer_level TEXT,       -- Body.<MessageType>.BenefitsCoordination.PayerResponsibilityCode
        payer_name TEXT,        -- Body.<MessageType>.BenefitsCoordination.PayerName
        pbm_participant_id TEXT,-- Body.<MessageType>.BenefitsCoordination.PayerIdentification.PayerID
        bin TEXT,               -- Body.<MessageType>.BenefitsCoordination.PayerIdentification.IINNumber
        pcn TEXT,               -- Body.<MessageType>.BenefitsCoordination.PayerIdentification.ProcessorIdentificationNumber
        naic_id TEXT,           -- Body.<MessageType>.BenefitsCoordination.PayerIdentification.NAICCode
        hp_id TEXT,             -- Body.<MessageType>.BenefitsCoordination.PayerIdentification.StandardUniqueHealthPlanIdentifier
        person_code TEXT,       -- Body.<MessageType>.BenefitsCoordination.PersonCode
        group_id TEXT,          -- Body.<MessageType>.BenefitsCoordination.GroupID
        group_name TEXT,        -- Body.<MessageType>.BenefitsCoordination.GroupName
        pbm_member_id TEXT,     -- Body.<MessageType>.BenefitsCoordination.PBMMemberID
        relationship_code TEXT, -- Body.<MessageType>.BenefitsCoordination.PatientRelationshipCode
        cardholder_id TEXT,     -- Body.<MessageType>.BenefitsCoordination.CardholderID
        cardholder_first_name TEXT, -- Body.<MessageType>.BenefitsCoordination.CardHolderName.FirstName
        cardholder_last_name TEXT,  -- Body.<MessageType>.BenefitsCoordination.CardHolderName.LastName
        payer_phone TEXT,       -- Body.<MessageType>.BenefitsCoordination.CommunicationNumbers.PrimaryTelephone.Number
        payer_fax TEXT,         -- Body.<MessageType>.BenefitsCoordination.CommunicationNumbers.Fax.Number
        prohibit_renewal_request TEXT -- Body.<MessageType>.BenefitsCoordination.ProhibitRenewalRequest (boolean in XSD, text in CSON)
    );
END $$;

-- Appended type for ss_observation
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ss_observation_type') THEN
        CREATE TYPE ss_observation_type AS (
            type_id TEXT,         -- Measurement.VitalSign (LOINC code)
            loinc_version TEXT,   -- Measurement.LOINCVersion
            value NUMERIC,        -- Measurement.Value
            unit_id TEXT,         -- Measurement.UnitOfMeasure (UCUM code)
            ucum_version TEXT,    -- Measurement.UCUMVersion
            observation_date DATE,-- Measurement.ObservationDate.Date
            notes TEXT            -- ObservationNotes
        );
    END IF;
END $$;

-- Appended type for ss_due
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ss_due_type') THEN
        DROP TYPE ss_due_type CASCADE;
    END IF;
    CREATE TYPE ss_due_type AS (
        -- direction TEXT, -- Part of main ss_message
        compound_no INTEGER,        
        coagent_id TEXT,            
        service_reason_id TEXT, 
        pservice_rsn_id TEXT,   
        service_res_id TEXT[],    
        coagent_code TEXT,      
        coagent_qualifier_id TEXT, 
        coagent_description TEXT, 
        clinical_significance TEXT, 
        ack_reason TEXT         
    );
END $$;

-- Appended type for ss_compound
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ss_compound_type') THEN
        DROP TYPE ss_compound_type CASCADE;
    END IF;
    CREATE TYPE ss_compound_type AS (
        fdb_id TEXT,                
        compound_no INTEGER,    
        qual_id TEXT,           
        code TEXT,              
        description TEXT,       
        strength TEXT,          
        strength_form_id TEXT,  
        strength_uom_id TEXT,   
        quantity NUMERIC,       
        quantity_qualifier_id TEXT, 
        quantity_uom_id TEXT,   
        dea_schedule_id TEXT    
    );
END $$;

-- Appended type for ss_diagnosis
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ss_diagnosis_type') THEN
        DROP TYPE ss_diagnosis_type CASCADE;
    END IF;
    CREATE TYPE ss_diagnosis_type AS (
        dx_id TEXT,             
        type TEXT,              
        dx_code TEXT,           
        dx_code_qualifier_id TEXT, 
        dx_desc TEXT            
    );
END $$;

-- Appended type for ss_codified_note
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ss_codified_note_type') THEN
        CREATE TYPE ss_codified_note_type AS (
            qualifier_id TEXT, -- Qualifier
            value INTEGER      -- Value
        );
    END IF;
END $$;

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ss_message_main_params_type') THEN
        DROP TYPE ss_message_main_params_type CASCADE;
    END IF;
    CREATE TYPE ss_message_main_params_type AS (
        resolved_site_id INTEGER,
        resolved_patient_id INTEGER,
        resolved_physician_id INTEGER,
        resolved_pharmacy_order_id INTEGER,
        ss_digital_signature_indicator TEXT,
        ss_direction TEXT,
        ss_sent_dt TIMESTAMP,
        ss_priority_flag TEXT,
        ss_to TEXT,
        ss_from TEXT,
        ss_message_id_header TEXT,
        ss_related_message_id TEXT,
        ss_prescriber_order_number TEXT,
        ss_pharmacy_rx_no TEXT,
        ss_message_type TEXT,
        sender_software_developer TEXT,
        sender_software_product TEXT,
        sender_software_version TEXT,
        order_group_no TEXT,
        rx_group_no TEXT,
        order_group_icnt INTEGER,
        rx_group_icnt INTEGER,
        order_group_tcnt INTEGER,
        rx_group_tcnt INTEGER,
        order_group_reason_code TEXT,
        rx_group_reason_code TEXT,
        ss_patient_name_display TEXT,
        ss_patient_first_name TEXT,
        ss_patient_last_name TEXT,
        ss_patient_middle_name TEXT,
        ss_patient_dob DATE,
        ss_patient_gender TEXT,
        ss_patient_ssn TEXT,
        ss_patient_home_street_1 TEXT,
        ss_patient_home_street_2 TEXT,
        ss_patient_home_city TEXT,
        ss_patient_home_state TEXT,
        ss_patient_home_zip TEXT,
        ss_patient_phone TEXT,
        ss_patient_mrn TEXT,
        ss_patient_medicare TEXT,
        ss_patient_medicaid TEXT,
        ss_patient_rems TEXT,
        ss_nka TEXT,
        ss_prescriber_name_display TEXT,
        ss_prescriber_npi TEXT,
        ss_prescriber_dea TEXT,
        ss_prescriber_rems TEXT,
        ss_prescriber_state_cs_lic TEXT,
        ss_prescriber_medicare TEXT,
        ss_prescriber_medicaid TEXT,
        ss_prescriber_state_lic TEXT,
        ss_prescriber_certificate_to_prescribe TEXT,
        ss_prescriber_2000waiver_id TEXT,
        ss_prescriber_spec_id TEXT,
        ss_prescriber_last_name TEXT,
        ss_prescriber_first_name TEXT,
        ss_prescriber_address_1 TEXT,
        ss_prescriber_address_2 TEXT,
        ss_prescriber_city TEXT,
        ss_prescriber_state TEXT,
        ss_prescriber_zip TEXT,
        ss_prescriber_phone TEXT,
        ss_prescriber_extension TEXT,
        ss_prescriber_fax TEXT,
        ss_prescriber_loc_name TEXT,
        ss_prescriber_loc_ncpdp_id TEXT,
        ss_prescriber_loc_dea TEXT,
        ss_prescriber_loc_rems TEXT,
        ss_prescriber_loc_state_cs_lic TEXT,
        ss_prescriber_loc_medicare TEXT,
        ss_prescriber_loc_medicaid TEXT,
        ss_prescriber_loc_state_lic TEXT,
        ss_prescriber_agent_first_name TEXT,
        ss_prescriber_agent_last_name TEXT,
        ss_prescriber_has_license TEXT[],
        ss_prescriber_loc_has_license TEXT[],
        ss_fu_prescriber_name_display TEXT,
        ss_fu_prescriber_last_name TEXT,
        ss_fu_prescriber_first_name TEXT,
        ss_fu_prescriber_address_1 TEXT,
        ss_fu_prescriber_address_2 TEXT,
        ss_fu_prescriber_city TEXT,
        ss_fu_prescriber_state TEXT,
        ss_fu_prescriber_zip TEXT,
        ss_fu_prescriber_phone TEXT,
        ss_fu_prescriber_extension TEXT,
        ss_fu_prescriber_fax TEXT,
        ss_fu_prescriber_npi TEXT,
        ss_fu_prescriber_dea TEXT,
        ss_fu_prescriber_rems TEXT,
        ss_fu_prescriber_state_cs_lic TEXT,
        ss_fu_prescriber_medicare TEXT,
        ss_fu_prescriber_medicaid TEXT,
        ss_fu_prescriber_state_lic TEXT,
        ss_fu_prescriber_certificate_to_prescribe TEXT,
        ss_fu_prescriber_2000waiver_id TEXT,
        ss_fu_prescriber_spec_id TEXT,
        ss_fu_prescriber_has_license TEXT[],
        ss_supervisor_name_display TEXT,
        ss_supervisor_last_name TEXT,
        ss_supervisor_first_name TEXT,
        ss_supervisor_npi TEXT,
        ss_supervisor_dea TEXT,
        ss_supervisor_rems TEXT,
        ss_supervisor_state_cs_lic TEXT,
        ss_supervisor_medicare TEXT,
        ss_supervisor_medicaid TEXT,
        ss_supervisor_state_lic TEXT,
        ss_supervisor_certificate_to_prescribe TEXT,
        ss_supervisor_2000waiver_id TEXT,
        ss_supervisor_spec_id TEXT,
        ss_supervisor_phone TEXT,
        ss_supervisor_extension TEXT,
        ss_supervisor_fax TEXT,
        ss_supervisor_has_license TEXT[],
        description TEXT,
        product_code_qualifier_id TEXT,
        product_code TEXT,
        fdb_id TEXT,
        drug_db_qualifier_id TEXT,
        drug_db_code TEXT,
        dea_schedule_id TEXT,
        strength TEXT,
        strength_form_id TEXT,
        strength_uom_id TEXT,
        quantity NUMERIC,
        quantity_qualifier_id TEXT,
        quantity_uom_id TEXT,
        days_supply INTEGER,
        compound_dosage_form_id TEXT,
        written_date DATE,
        start_date DATE,
        expiration_date DATE,
        effective_date DATE,
        daw TEXT,
        daw_code_reason TEXT,
        refills INTEGER,
        pa_number TEXT,
        pa_status_id TEXT,
        drug_cvg_status_id TEXT[],
        do_not_fill TEXT,
        note TEXT,
        sig TEXT,
        delivery_request TEXT,
        delivery_location TEXT,
        flavoring_requested TEXT,
        prescriber_checked_rems TEXT,
        rems_risk_category TEXT,
        rems_authorization_number TEXT,
        clinical_info_qualifier TEXT,
        prohibit_renewal_request TEXT,
        status_icons TEXT[],
        show_options TEXT[],
        processed_dt TIMESTAMP,
        ss_renewal_status TEXT,
        ss_renewal_denial_reason_code TEXT[],
        ss_renewal_denied_reason TEXT,
        ss_renewal_note TEXT,
        ss_cancel_status TEXT,
        ss_chg_type_id TEXT,
        ss_chg_type_sc_id TEXT[],
        ss_chg_sc_aw_chg_id TEXT[],
        ss_chg_status TEXT,                    
        ss_chg_dr_code_id TEXT[],                  
        ss_chg_denied_reason TEXT,                 
        ss_chg_approved_note TEXT,                 
        ss_chg_vr_cd_id TEXT[],                  
        ss_chg_validated_note TEXT
    );
END$$;

-- Surescripts Extracted Data Types for Parsing Helpers
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ss_extracted_header_type') THEN
        CREATE TYPE ss_extracted_header_type AS (
            digital_signature_indicator TEXT,
            to_val TEXT,
            from_val TEXT,
            sender_software_developer TEXT,
            sender_software_product TEXT,
            sender_software_version TEXT,
            order_group_no TEXT,
            rx_group_no TEXT,
            order_group_icnt INTEGER,
            rx_group_icnt INTEGER,
            order_group_tcnt INTEGER,
            rx_group_tcnt INTEGER,
            order_group_reason_code TEXT,
            rx_group_reason_code TEXT,
            message_id_header TEXT,     -- Renamed from ss_message_id_header
            related_message_id TEXT,    -- Renamed from ss_related_message_id
            prescriber_order_number TEXT, -- Renamed from ss_prescriber_order_number
            pharmacy_rx_no TEXT,        -- Renamed from ss_pharmacy_rx_no
            sent_dt TIMESTAMP
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ss_extracted_patient_type') THEN
        CREATE TYPE ss_extracted_patient_type AS (
            first_name TEXT,
            last_name TEXT,
            middle_name TEXT,
            name_display TEXT,
            dob DATE,
            gender TEXT,
            ssn TEXT,
            home_street_1 TEXT,
            home_street_2 TEXT,
            home_city TEXT,
            home_state_code TEXT,     -- Using _code to avoid conflict with potential _id FKs later
            home_zip TEXT,
            phone TEXT,
            mrn TEXT,
            medicare TEXT,
            medicaid TEXT,
            rems TEXT,
            nka TEXT
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ss_extracted_prescriber_info_type') THEN
        CREATE TYPE ss_extracted_prescriber_info_type AS (
            npi TEXT,               -- Prescriber NPI
            dea TEXT,               -- Prescriber DEA number
            rems TEXT,              -- Prescriber REMS ID
            state_cs_lic TEXT,      -- Prescriber state controlled substance license
            medicare TEXT,          -- Prescriber Medicare number
            medicaid TEXT,          -- Prescriber Medicaid number
            state_lic TEXT,         -- Prescriber state license number
            certificate_to_prescribe TEXT, -- Prescriber certificate to prescribe
            waiver_2000_id TEXT,    -- Prescriber 2000 waiver ID (NADEAN, X-Number)
            spec_code TEXT,         -- Prescriber specialty code
            has_license TEXT[],     -- Array of license types the prescriber has (NPI, DEA, etc.)
            name_display TEXT,      -- Formatted name for display (LastName, FirstName)
            last_name TEXT,
            first_name TEXT,
            address_1 TEXT,
            address_2 TEXT,
            city TEXT,
            state_code TEXT,
            zip TEXT,
            phone TEXT,
            extension TEXT,
            fax TEXT,
            loc_name TEXT,          -- Practice Location Name
            loc_ncpdp_id TEXT,      -- Practice Location NCPDP ID
            loc_dea TEXT,           -- Practice Location DEA
            loc_rems TEXT,          -- Practice Location REMS ID
            loc_state_cs_lic TEXT,  -- Practice Location State CS License
            loc_medicare TEXT,      -- Practice Location Medicare
            loc_medicaid TEXT,      -- Practice Location Medicaid
            loc_state_lic TEXT,     -- Practice Location State License
            loc_has_license TEXT[],  -- Array of license types for the location
            agent_first_name TEXT,    -- Agent First Name
            agent_last_name TEXT     -- Agent Last Name
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ss_extracted_supervisor_type') THEN
        CREATE TYPE ss_extracted_supervisor_type AS (
            last_name TEXT,
            first_name TEXT,
            npi TEXT,
            dea TEXT,
            rems TEXT,
            state_cs_lic TEXT,
            medicare TEXT,
            medicaid TEXT,
            state_lic TEXT,
            certificate_to_prescribe TEXT,
            waiver_2000_id TEXT,
            spec_code TEXT,
            phone TEXT,
            extension TEXT,
            fax TEXT,
            has_license TEXT[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ss_extracted_fu_prescriber_type') THEN
        CREATE TYPE ss_extracted_fu_prescriber_type AS (
            last_name TEXT,
            first_name TEXT,
            address_1 TEXT,
            address_2 TEXT,
            city TEXT,
            state_code TEXT,
            zip TEXT,
            phone TEXT,
            extension TEXT,
            fax TEXT,
            npi TEXT,
            dea TEXT,
            rems TEXT,
            state_cs_lic TEXT,
            medicare TEXT,
            medicaid TEXT,
            state_lic TEXT,
            certificate_to_prescribe TEXT,
            waiver_2000_id TEXT,
            spec_code TEXT,
            has_license TEXT[]
        );
    END IF;

END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ss_extracted_medication_type') THEN
        CREATE TYPE ss_extracted_medication_type AS (
            description TEXT,
            product_code_qualifier_id TEXT,
            product_code TEXT,
            drug_db_qualifier_id TEXT,
            drug_db_code TEXT,
            dea_schedule_id TEXT,
            strength TEXT,
            strength_form_id TEXT,
            strength_uom_id TEXT,
            quantity NUMERIC,
            quantity_qualifier_id TEXT,
            quantity_uom_id TEXT,
            days_supply INTEGER,
            compound_dosage_form_id TEXT,
            written_date DATE,
            start_date DATE,
            expiration_date DATE,
            effective_date DATE,
            daw TEXT,
            daw_code_reason TEXT,
            refills INTEGER,
            pa_number TEXT,
            pa_status_id TEXT,
            drug_cvg_status_id TEXT[],
            do_not_fill TEXT,
            note TEXT,
            sig TEXT,
            delivery_request TEXT,
            delivery_location TEXT,
            flavoring_requested TEXT,
            prescriber_checked_rems TEXT,
            rems_risk_category TEXT,
            rems_authorization_number TEXT,
            clinical_info_qualifier TEXT,
            prohibit_renewal_request TEXT
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ss_resolved_ids_type') THEN
        CREATE TYPE ss_resolved_ids_type AS (
            resolved_site_id INTEGER,
            resolved_patient_id INTEGER,
            resolved_physician_id INTEGER,
            resolved_pharmacy_order_id INTEGER,
            fdb_id TEXT -- For the main medication
        );
    END IF;
END $$;
