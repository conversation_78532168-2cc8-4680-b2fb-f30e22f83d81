DO $$ BEGIN
  PERFORM drop_all_function_signatures('get_ncpdp_billing_response');
END $$;
-- Function to generate NCPDP billing response report
CREATE OR REPLACE FUNCTION get_ncpdp_billing_response(p_response_id integer DEFAULT NULL, p_claim_no text DEFAULT NULL)
RETURNS TABLE (
    patient_name text,
    date_printed text,
    pharmacy_identifier text,
    invoice_number text,
    prescription_number text,
    date_filled text,
    days_supply integer,
    cost numeric,
    reimbursement numeric,
    profit numeric,
    margin numeric,
    billed_amount numeric,
    paid_amount numeric,
    fee numeric,
    discount numeric,
    discount_percentage numeric,
    drug_name text,
    ndc text,
    quantity_metric numeric,
    response_received_datetime text,
    claim_status text,
    group_id text,
    network_reimbursement_id text,
    patient_first_name text,
    patient_last_name text,
    authorization_number text,
    patient_pay_amount numeric,
    ingredient_cost_paid numeric,
    dispensing_fee_paid numeric,
    total_amount_paid numeric,
    cost_basis text,
    awp_price numeric,
    awp_plus_20 numeric,
    additional_message_count text,
    additional_message_qualifier text,
    additional_message text
) AS $$
BEGIN
    RETURN QUERY 
    SELECT 
        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,patient,patient_first_name}'), '') || ' ' ||
        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,patient,patient_last_name}'), '') as patient_name,
        TO_CHAR(CURRENT_DATE, 'MM/DD/YYYY') as date_printed,
        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,pharmacy,provider_id}'), 'PHARMACY_ID') as pharmacy_identifier,
        r.claim_no as invoice_number,
        COALESCE((r.response_json_data::jsonb#>>'{response_claim,0,rx_svc_no}'), '') as prescription_number,
        COALESCE(r.response_json_data::jsonb->>'date_of_service', TO_CHAR(CURRENT_DATE, 'MM/DD/YYYY')) as date_filled,
        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,claim,day_supply}')::integer, 0) as days_supply,
        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,pricing,ing_cst_sub}')::numeric, 0) as cost,
        CASE 
            WHEN COALESCE(r.response_json_data::jsonb#>>'{response_stat,0,transaction_response_status}', '') = 'R' THEN 0
            ELSE COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,total_paid}')::numeric, 0) - 
                 COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,pt_pay_amt}')::numeric, 0)
        END as reimbursement,
        CASE 
            WHEN COALESCE(r.response_json_data::jsonb#>>'{response_stat,0,transaction_response_status}', '') = 'R' 
                OR r.response_status != 'A' 
                OR COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,total_paid}')::numeric, 0) = 0 THEN 0
            ELSE COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,total_paid}')::numeric, 0) + 
                 COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,pt_pay_amt}')::numeric, 0) - 
                 COALESCE((r.response_json_data::jsonb#>>'{request_json_data,pricing,ing_cst_sub}')::numeric, 0)
        END as profit,
        CASE 
            WHEN COALESCE(r.response_json_data::jsonb#>>'{response_stat,0,transaction_response_status}', '') = 'R' 
                OR r.response_status != 'A'
                OR COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,total_paid}')::numeric, 0) = 0 THEN 0
            ELSE ROUND(((COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,total_paid}')::numeric, 0) + 
                        COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,pt_pay_amt}')::numeric, 0) - 
                        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,pricing,ing_cst_sub}')::numeric, 0)) / 
                       NULLIF(COALESCE((r.response_json_data::jsonb#>>'{request_json_data,pricing,ing_cst_sub}')::numeric, 0), 0) * 100), 2)
        END as margin,
        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,pricing,gross_amount_due}')::numeric, 0) as billed_amount,
        COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,total_paid}')::numeric, 0) as paid_amount,
        COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,disp_fee_paid}')::numeric, 0) as fee,
        CASE 
            WHEN COALESCE(r.response_json_data::jsonb#>>'{response_stat,0,transaction_response_status}', '') = 'R' 
                OR r.response_status != 'A'
                OR COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,total_paid}')::numeric, 0) = 0 THEN 0
            ELSE COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,total_paid}')::numeric, 0) + 
                 COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,pt_pay_amt}')::numeric, 0) - 
                 COALESCE((r.response_json_data::jsonb#>>'{request_json_data,pricing,gross_amount_due}')::numeric, 0)
        END as discount,
        CASE 
            WHEN COALESCE(r.response_json_data::jsonb#>>'{response_stat,0,transaction_response_status}', '') = 'R' 
                OR r.response_status != 'A'
                OR COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,total_paid}')::numeric, 0) = 0 THEN 0
            ELSE ROUND(((COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,total_paid}')::numeric, 0) + 
                        COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,pt_pay_amt}')::numeric, 0) - 
                        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,pricing,gross_amount_due}')::numeric, 0)) /
                      NULLIF(COALESCE((r.response_json_data::jsonb#>>'{request_json_data,pricing,gross_amount_due}')::numeric, 0), 0) * 100), 2)
        END as discount_percentage,
        COALESCE(i.name::text, '') as drug_name,
        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,claim,prod_svc_id}'), '') as ndc,
        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,claim,quantity_dispensed}')::numeric/1000, 0) as quantity_metric,
        TO_CHAR(CURRENT_TIMESTAMP, 'MM/DD/YYYY HH24:MI:SS AM') as response_received_datetime,
        CASE 
            WHEN r.response_status = 'A' AND COALESCE(r.response_json_data::jsonb#>>'{response_stat,0,transaction_response_status}', '') = 'R' THEN 'Rejected'
            WHEN r.response_status = 'A' AND 
                 (r.transaction_response_status = 'P' OR 
                  COALESCE(r.response_json_data::jsonb#>>'{response_stat,0,transaction_response_status}', '') = 'C') THEN 'Payable'
            ELSE 'Not Payable'
        END as claim_status,
        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,insurance,group_id}'), '') as group_id,
        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,pharmacy,provider_id_qualifier}'), '') || ':' ||
        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,pharmacy,provider_id}'), '') as network_reimbursement_id,
        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,patient,patient_first_name}'), '') as patient_first_name,
        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,patient,patient_last_name}'), '') as patient_last_name,
        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,insurance,card_holder_id}'), '') as authorization_number,
        COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,pt_pay_amt}')::numeric, 0) as patient_pay_amount,
        COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,ing_cst_paid}')::numeric, 0) as ingredient_cost_paid,
        COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,disp_fee_paid}')::numeric, 0) as dispensing_fee_paid,
        COALESCE((r.response_json_data::jsonb#>>'{response_pricing,0,total_paid}')::numeric, 0) as total_amount_paid,
        COALESCE((r.response_json_data::jsonb#>>'{request_json_data,pricing,cost_basis}'), '') as cost_basis,
        COALESCE(i.awp_price, 0) as awp_price,
        COALESCE(i.awp_price * 1.2, 0) as awp_plus_20,
        COALESCE((r.response_json_data::jsonb#>>'{response_stat,0,subform_msg,0,add_msg_count}'), '') as additional_message_count,
        COALESCE((r.response_json_data::jsonb#>>'{response_stat,0,subform_msg,0,add_msg_qualifier}'), '') as additional_message_qualifier,
        COALESCE((r.response_json_data::jsonb#>>'{response_stat,0,subform_msg,0,add_msg}'), '') as additional_message
    FROM form_ncpdp_response r
    LEFT JOIN LATERAL (
        SELECT 
            fi.fdb_id, 
            fi.name::text as name, 
            fi.awp_price 
        FROM form_inventory fi
        WHERE fi.fdb_id = (r.response_json_data::jsonb#>>'{request_json_data,claim,prod_svc_id}')
        LIMIT 1
    ) i ON true
    WHERE (p_response_id IS NULL OR r.id = p_response_id)
    AND (p_claim_no IS NULL OR r.claim_no = p_claim_no);
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION get_ncpdp_billing_response(integer, text) IS 
'Generates a billing response report for a specific NCPDP response.
Parameters:
  p_response_id - The ID of the NCPDP response record (optional if claim_no provided)
  p_claim_no - The claim number to look up (optional if response_id provided)
Returns a table with billing response details formatted for reporting.
At least one parameter must be provided.
Includes AWP price check and cost basis information.';