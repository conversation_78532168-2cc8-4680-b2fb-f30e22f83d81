DROP TRIGGER IF EXISTS sec_rule_trigger ON form_sec_rule;
DROP FUNCTION IF EXISTS sec_rule_trigger_function;


CREATE OR REPLACE FUNCTION crx_sec_rule_notify() RETURNS TRIGGER AS $$
BEGIN
    NOTIFY sec_rule_trigger_channel, '{"tableName": "form_sec_rule", "eventType": "insert_or_update"}';
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE TRIGGER crx_sec_rule_trigger AFTER
INSERT
OR
UPDATE ON form_sec_rule
FOR EACH ROW EXECUTE FUNCTION crx_sec_rule_notify();