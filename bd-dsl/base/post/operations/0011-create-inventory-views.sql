CREATE OR REPLACE VIEW vw_inventory_on_hand AS
SELECT
    inv.lot_tracking,
    inv.serial_tracking,
    li.site_id,
    li.inventory_id,
    binloc.bin AS bin_location,
    binloc.min_quantity,
    binloc.max_quantity,
    COALESCE(SUM(li.quantity), 0::numeric) AS quantity_on_hand
FROM
    form_ledger_inventory li
    INNER JOIN form_site st
        ON li.site_id = st.id
        AND st.deleted IS NOT TRUE
        AND st.archived IS NOT TRUE
    INNER JOIN form_inventory inv
        ON inv.id = li.inventory_id
        AND inv.deleted IS NOT TRUE
        AND inv.archived IS NOT TRUE
    LEFT JOIN form_inventory_bin binloc
        ON binloc.site_id = st.id
        AND binloc.inventory_id = li.inventory_id
        AND binloc.archived IS NOT TRUE
        AND binloc.deleted IS NOT TRUE
WHERE
    li.deleted IS NOT TRUE
    AND li.archived IS NOT TRUE
GROUP BY
    inv.lot_tracking,
    inv.serial_tracking,
    li.site_id,
    li.inventory_id,
    binloc.bin,
    binloc.min_quantity,
    binloc.max_quantity
ORDER BY
    li.inventory_id,
    li.site_id;


DROP VIEW IF EXISTS vw_site_contract_prices CASCADE;
CREATE OR REPLACE VIEW vw_site_contract_prices AS
WITH inventory_data AS (
    SELECT 
        inv.id AS inventory_id,
        inv.ndc,
        inv.hcpc_code,
        inv.type AS inventory_type,
        inv.price_code_id,
        inv.list_price::numeric,
        inv.awp_price::numeric,
        inv.wac_price::numeric,
        inv.asp_price::numeric,
        inv.last_cost_ea::numeric,
        inv.add_price1::numeric,
        inv.add_price2::numeric
    FROM form_inventory inv
    WHERE inv.archived IS NOT TRUE
    AND inv.deleted IS NOT TRUE
    AND inv.active = 'Yes'
),
contract_data AS (
    SELECT 
        pc.id AS contract_id,
        pc.contract_type,
        pc.name AS contract_name,
        ppm.id AS matrix_id,
        ppm.name AS matrix_name,
        ppmi.id AS matrix_item_id,
        ppmi.inventory_id,
        ppmi.price_code_id,
        COALESCE(ppmi.override_expected_formula_id, filtered_spcf.expected_price_basis) AS expected_formula_id,
        COALESCE(ppmi.override_expected_price_multiplier, filtered_spcf.expected_price_multiplier) AS expected_multiplier,
        COALESCE(ppmi.override_special_formula_id, filtered_spcf.special_price_basis) AS special_formula_id,
        COALESCE(ppmi.override_special_price_multiplier, filtered_spcf.special_price_multiplier) AS special_multiplier,
        ppmi.billable AS matrix_billable
    FROM form_payer_contract pc
    JOIN form_payer_price_matrix ppm ON ppm.id = pc.assigned_matrix_id
    JOIN form_payer_price_matrix_item ppmi ON ppmi.payer_price_matrix_id = ppm.id
    JOIN form_inventory inv ON inv.id = ppmi.inventory_id
	LEFT JOIN LATERAL (
	  SELECT pcf2.*
	  FROM sf_form_payer_contract_to_price_code_formulas spcf
	  JOIN form_price_code_formulas pcf2 ON pcf2.id = spcf.form_price_code_formulas_fk
		  WHERE pcf2.code_category = inv.price_code_id AND spcf.form_payer_contract_fk = pc.id
		  AND spcf.archive IS NOT TRUE 
		  AND spcf."delete" IS NOT TRUE 
	) filtered_spcf on true 
    WHERE pc.archived IS NOT TRUE
    AND pc.deleted IS NOT TRUE
    AND ppm.archived IS NOT TRUE
    AND ppm.deleted IS NOT TRUE
    AND ppmi.archived IS NOT TRUE
    AND ppmi.deleted IS NOT TRUE
),
site_price_data AS (
    SELECT 
        spc.price_code_id,
        spc.price_formula_id,
        spc.multiplier
    FROM form_site_price_code_item spc
    WHERE spc.archived IS NOT TRUE
    AND spc.deleted IS NOT TRUE
),
assigned_sites_data AS (
    SELECT
        form_payer_contract_fk as contract_id,
        st.id as site_id,
        st.id as site,
        st.auto_name as site_auto_name
    FROM form_site st
    INNER JOIN gr_form_payer_contract_site_id_to_site_id grst ON grst.form_site_fk = st.id
),
assigned_payer_data AS (
    SELECT
        pc.id AS contract_id,
        py.id AS payer_id,
        py.id AS payer,
        py.auto_name as payer_auto_name
    FROM form_payer py
    INNER JOIN form_payer_contract pc ON pc.id = py.assigned_contract_id 
    AND pc.archived IS NOT TRUE
    AND pc.deleted IS NOT TRUE
    WHERE py.archived IS NOT TRUE
    AND py.deleted IS NOT TRUE
    AND COALESCE(py.active, 'No') = 'Yes'
),
formula_data AS (
    SELECT 
        code AS formula_id,
        name AS formula_name
    FROM form_list_price_basis
    WHERE archived IS NOT TRUE
    AND deleted IS NOT TRUE
),
formulas_and_pricing AS (
    SELECT
        cd.contract_id as query_id,
        'payer_contract' as query_form,
        cd.contract_id,
        cd.contract_type,
        cd.contract_name,
        apd.payer,
        apd.payer_auto_name,
        apd.payer_id,
        asd.site,
        asd.site_auto_name,
        asd.site_id,
        cd.matrix_name,
        cd.matrix_item_id,
        cd.matrix_id,
        id.inventory_id,
        id.ndc,
        id.hcpc_code,
        id.inventory_type,
        id.price_code_id,
        cd.matrix_billable AS billable,
        -- Formula for expected price
        CASE
            WHEN cd.expected_formula_id IS NOT NULL THEN
                CASE
                    WHEN fd_expected.formula_id = 'M' THEN fd_expected.formula_name
                    ELSE CONCAT(fd_expected.formula_name, ' * ', format_numeric(cd.expected_multiplier::numeric))
                END
            ELSE
                CASE
                    WHEN spd.price_formula_id IS NOT NULL THEN
                        CASE
                            WHEN fd_site.formula_id = 'M' THEN fd_site.formula_name
                            ELSE CONCAT(fd_site.formula_name, ' * ', format_numeric(spd.multiplier::numeric))
                        END
                    ELSE 'Default List Price'
                END
        END AS formula_expected,
        -- Formula for special price
        CASE
            WHEN cd.contract_type = 'Shared Contract' AND cd.special_formula_id IS NOT NULL THEN
                CASE 
                    WHEN fd_special.formula_id = 'M' THEN fd_special.formula_name
                    ELSE CONCAT(fd_special.formula_name, ' * ', format_numeric(cd.special_multiplier::numeric))
                END
            WHEN cd.contract_type = 'Shared Contract' AND cd.special_formula_id IS NULL THEN
                CASE
                    WHEN spd.price_formula_id IS NOT NULL THEN
                        CASE
                            WHEN fd_site.formula_id = 'M' THEN fd_site.formula_name
                            ELSE CONCAT(fd_site.formula_name, ' * ', format_numeric(spd.multiplier::numeric))
                        END
                    ELSE 'Default List Price'
                END
            ELSE
                CASE
                    WHEN fd_expected.formula_id = 'M' THEN fd_expected.formula_name
                    ELSE CONCAT(fd_expected.formula_name, ' * ', format_numeric(cd.expected_multiplier::numeric))
                END
        END AS formula_special,
        -- Calculate expected price
        CASE
            WHEN cd.expected_formula_id = 'A' THEN ROUND(id.awp_price * cd.expected_multiplier::numeric, 4)
            WHEN cd.expected_formula_id = 'W' THEN ROUND(id.wac_price * cd.expected_multiplier::numeric, 4)
            WHEN cd.expected_formula_id = 'C' THEN ROUND(id.last_cost_ea * cd.expected_multiplier::numeric, 4)
            WHEN cd.expected_formula_id = 'ASP' THEN ROUND(id.asp_price * cd.expected_multiplier::numeric, 4)
            WHEN cd.expected_formula_id = 'L' THEN ROUND(id.list_price * cd.expected_multiplier::numeric, 4)
            WHEN cd.expected_formula_id = '1' THEN ROUND(id.add_price1 * cd.expected_multiplier::numeric, 4)
            WHEN cd.expected_formula_id = '2' THEN ROUND(id.add_price2 * cd.expected_multiplier::numeric, 4)
            ELSE
                -- Fallback to site pricing if no contract formula
                CASE
                    WHEN spd.price_formula_id = 'A' THEN ROUND(id.awp_price * spd.multiplier::numeric, 4)
                    WHEN spd.price_formula_id = 'W' THEN ROUND(id.wac_price * spd.multiplier::numeric, 4)
                    WHEN spd.price_formula_id = 'C' THEN ROUND(id.last_cost_ea * spd.multiplier::numeric, 4)
                    WHEN spd.price_formula_id = 'ASP' THEN ROUND(id.asp_price * spd.multiplier::numeric, 4)
                    WHEN spd.price_formula_id = 'L' THEN ROUND(id.list_price * spd.multiplier::numeric, 4)
                    WHEN spd.price_formula_id = '1' THEN ROUND(id.add_price1 * spd.multiplier::numeric, 4)
                    WHEN spd.price_formula_id = '2' THEN ROUND(id.add_price2 * spd.multiplier::numeric, 4)
                    ELSE id.list_price
                END
        END AS expected_price,
        -- Calculate special price (only for Shared Contract)
        CASE
            WHEN cd.contract_type = 'Shared Contract' THEN
                CASE
                    WHEN cd.special_formula_id = 'A' THEN ROUND(id.awp_price * cd.special_multiplier::numeric, 4)
                    WHEN cd.special_formula_id = 'W' THEN ROUND(id.wac_price * cd.special_multiplier::numeric, 4)
                    WHEN cd.special_formula_id = 'C' THEN ROUND(id.last_cost_ea * cd.special_multiplier::numeric, 4)
                    WHEN cd.special_formula_id = 'ASP' THEN ROUND(id.asp_price * cd.special_multiplier::numeric, 4)
                    WHEN cd.special_formula_id = 'L' THEN ROUND(id.list_price * cd.special_multiplier::numeric, 4)
                    WHEN cd.special_formula_id = '1' THEN ROUND(id.add_price1 * cd.special_multiplier::numeric, 4)
                    WHEN cd.special_formula_id = '2' THEN ROUND(id.add_price2 * cd.special_multiplier::numeric, 4)
                    ELSE
                        -- Fallback to site pricing if no contract formula
                        CASE
                            WHEN spd.price_formula_id = 'A' THEN ROUND(id.awp_price * spd.multiplier::numeric, 4)
                            WHEN spd.price_formula_id = 'W' THEN ROUND(id.wac_price * spd.multiplier::numeric, 4)
                            WHEN spd.price_formula_id = 'C' THEN ROUND(id.last_cost_ea * spd.multiplier::numeric, 4)
                            WHEN spd.price_formula_id = 'ASP' THEN ROUND(id.asp_price * spd.multiplier::numeric, 4)
                            WHEN spd.price_formula_id = 'L' THEN ROUND(id.list_price * spd.multiplier::numeric, 4)
                            WHEN spd.price_formula_id = '1' THEN ROUND(id.add_price1 * spd.multiplier::numeric, 4)
                            WHEN spd.price_formula_id = '2' THEN ROUND(id.add_price2 * spd.multiplier::numeric, 4)
                            ELSE id.list_price
                        END
                END
            ELSE
                -- For normal Contract, use expected price logic
                CASE
                    WHEN cd.expected_formula_id = 'A' THEN ROUND(id.awp_price * cd.expected_multiplier::numeric, 4)
                    WHEN cd.expected_formula_id = 'W' THEN ROUND(id.wac_price * cd.expected_multiplier::numeric, 4)
                    WHEN cd.expected_formula_id = 'C' THEN ROUND(id.last_cost_ea * cd.expected_multiplier::numeric, 4)
                    WHEN cd.expected_formula_id = 'ASP' THEN ROUND(id.asp_price * cd.expected_multiplier::numeric, 4)
                    WHEN cd.expected_formula_id = 'L' THEN ROUND(id.list_price * cd.expected_multiplier::numeric, 4)
                    WHEN cd.expected_formula_id = '1' THEN ROUND(id.add_price1 * cd.expected_multiplier::numeric, 4)
                    WHEN cd.expected_formula_id = '2' THEN ROUND(id.add_price2 * cd.expected_multiplier::numeric, 4)
                    ELSE
                        -- Fallback to site pricing if no contract formula
                        CASE
                            WHEN spd.price_formula_id = 'A' THEN ROUND(id.awp_price * spd.multiplier::numeric, 4)
                            WHEN spd.price_formula_id = 'W' THEN ROUND(id.wac_price * spd.multiplier::numeric, 4)
                            WHEN spd.price_formula_id = 'C' THEN ROUND(id.last_cost_ea * spd.multiplier::numeric, 4)
                            WHEN spd.price_formula_id = 'ASP' THEN ROUND(id.asp_price * spd.multiplier::numeric, 4)
                            WHEN spd.price_formula_id = 'L' THEN ROUND(id.list_price * spd.multiplier::numeric, 4)
                            WHEN spd.price_formula_id = '1' THEN ROUND(id.add_price1 * spd.multiplier::numeric, 4)
                            WHEN spd.price_formula_id = '2' THEN ROUND(id.add_price2 * spd.multiplier::numeric, 4)
                            ELSE id.list_price
                        END
                END
        END AS special_price
    FROM contract_data cd
    JOIN inventory_data id ON id.inventory_id = cd.inventory_id
    JOIN assigned_payer_data apd ON apd.contract_id = cd.contract_id
    JOIN assigned_sites_data asd ON asd.contract_id = cd.contract_id
    JOIN site_price_data spd ON spd.price_code_id = cd.price_code_id
    LEFT JOIN formula_data fd_expected ON fd_expected.formula_id = cd.expected_formula_id
    LEFT JOIN formula_data fd_special ON fd_special.formula_id = cd.special_formula_id
    LEFT JOIN formula_data fd_site ON fd_site.formula_id = spd.price_formula_id
)
SELECT
    prc.query_id,
    prc.query_form,
    prc.contract_id,
    prc.contract_type,
    prc.contract_name,
    prc.payer,
    prc.payer_auto_name,
    prc.payer_id,
    prc.site,
    prc.site_auto_name,
    prc.site_id,
    prc.matrix_name,
    prc.matrix_item_id,
    prc.matrix_id,
    prc.inventory_id,
    prc.ndc,
    prc.hcpc_code,
    prc.inventory_type,
    prc.price_code_id,
    prc.billable,
    prc.formula_expected,
    prc.formula_special,
    format_currency(prc.expected_price::numeric) AS expected_price,
    format_currency(prc.special_price::numeric) AS special_price
FROM formulas_and_pricing prc;
