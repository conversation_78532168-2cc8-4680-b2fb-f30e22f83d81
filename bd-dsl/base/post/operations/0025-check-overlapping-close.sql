DO $$ BEGIN
  PERFORM drop_all_function_signatures('check_closing_dates');
END $$;
CREATE OR REPLACE FUNCTION check_closing_dates()
RETURNS TRIGGER AS $$
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);
    -- Check for overlapping dates with other non-voided closing periods
    IF EXISTS (
        SELECT 1
        FROM form_billing_closing
        WHERE COALESCE(void,'No') = 'No'
        AND id != COALESCE(NEW.id, -1)  -- Exclude current record for updates
        AND (
            -- New period starts during existing period
            (NEW.start_date BETWEEN start_date AND end_date)
            OR 
            -- New period ends during existing period
            (NEW.end_date BETWEEN start_date AND end_date)
            OR
            -- New period completely contains existing period
            (NEW.start_date <= start_date AND NEW.end_date >= end_date)
        )
    ) THEN
        RAISE EXCEPTION 'Closing period dates cannot overlap with existing periods';
    END IF;

    -- Check that start_date is before or equal to end_date
    IF NEW.start_date > NEW.end_date THEN
        RAISE EXCEPTION 'Start date must be before or equal to end date';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER closing_dates_check
    BEFORE INSERT OR UPDATE ON form_billing_closing
    FOR EACH ROW
    EXECUTE FUNCTION check_closing_dates();