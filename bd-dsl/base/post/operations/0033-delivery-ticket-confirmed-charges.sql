CREATE OR REPLACE FUNCTION generate_confirmed_dt_presets(
    p_careplan_dt_id integer,
    p_user_id integer
) RETURNS json AS $$
DECLARE
    v_all_charge_lines charge_line_with_split[];
    v_subform_charges charge_line_with_split[];
    v_subform_exceptions dt_exception[];
    v_existing_lgl_ids integer[];
    v_start_time timestamp;
    v_params jsonb;
    v_patient_id integer;
    v_careplan_id integer;
    v_error_message text;
    v_result json;
    v_charge_lines_json json;
    v_exceptions_json json;
BEGIN
    v_start_time := clock_timestamp();
    
    -- Build parameters JSON for logging
    v_params := jsonb_build_object('careplan_dt_id', p_careplan_dt_id);

    -- Log function call
    PERFORM log_delivery_ticket_confirmation(
        'generate_confirmed_dt_presets'::tracked_function,
        v_params,
        NULL::jsonb,
        NULL::text,
        clock_timestamp() - v_start_time
    );

    -- Validate input parameters
    IF p_careplan_dt_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Delivery Ticket ID cannot be null',
            'Validating required fields in generate_confirmed_dt_presets',
            'FUNCTION',
            current_schema(),
            'form_careplan_delivery_tick',
            jsonb_build_object(
                'function_name', 'generate_confirmed_dt_presets',
                'careplan_dt_id', p_careplan_dt_id
            )
        );
        RAISE EXCEPTION 'Delivery Ticket ID cannot be null';
    END IF;

    -- Get delivery ticket information
    SELECT dt.patient_id, dt.careplan_id INTO v_patient_id, v_careplan_id
    FROM form_careplan_delivery_tick dt
    WHERE dt.id = p_careplan_dt_id
    AND dt.archived IS NOT TRUE
    AND dt.deleted IS NOT TRUE;


    -- Get existing charge line IDs for this delivery ticket
    SELECT array_agg(charge_line_id) INTO v_existing_lgl_ids FROM get_existing_charge_lines_pre_confirmation(p_careplan_dt_id);

    RAISE LOG 'generate_confirmed_dt_presets:v_existing_lgl_ids: %', v_existing_lgl_ids;
    -- Fetch all charge lines for the delivery ticket
    SELECT create_confirmed_dt_chargeline_presets(p_careplan_dt_id) INTO v_all_charge_lines;
    
    RAISE LOG 'generate_confirmed_dt_presets:v_all_charge_lines: %', v_all_charge_lines;

    -- Process and generate results
    WITH processed_data AS (
        SELECT
            conf.patient_id,
            conf.careplan_id,
            conf.lgl_fltr,
            conf.subform_charges,
            conf.subform_exceptions
        FROM process_charge_line_data(
            p_careplan_dt_id, 
            p_user_id, 
            v_all_charge_lines, 
            v_existing_lgl_ids,
            v_patient_id,
            v_careplan_id
        ) conf
    )
    SELECT 
        pd.patient_id,
        pd.careplan_id,
        pd.lgl_fltr,
        pd.subform_charges,
        pd.subform_exceptions
    INTO
        v_patient_id,
        v_careplan_id,
        v_existing_lgl_ids,
        v_subform_charges,
        v_subform_exceptions
    FROM processed_data pd;

    -- Convert charge lines to JSON
    SELECT json_strip_nulls(array_to_json(v_subform_charges)) INTO v_charge_lines_json;

    -- Convert exceptions to JSON
    WITH exception_data AS (
        SELECT 
            description,
            dt_exception_id,
            exception_by,
            exception_datetime,
            inventory_id,
            billed_inventory_id,
            invoice_id,
            dispense_quantity,
            bill_quantity,
            expected,
            copay,
            bill_insurance_id,
            expected_insurance_id
        FROM unnest(v_subform_exceptions) ex
    )
    SELECT json_agg(json_strip_nulls(json_build_object(
        'description', ed.description,
        'dt_exception_id', ed.dt_exception_id,
        'exception_by', ed.exception_by,
        'exception_datetime', ed.exception_datetime,
        'inventory_id', ed.inventory_id,
        'billed_inventory_id', ed.billed_inventory_id,
        'invoice_id', ed.invoice_id,
        'dispense_quantity', ed.dispense_quantity,
        'bill_quantity', ed.bill_quantity,
        'expected', ed.expected,
        'copay', ed.copay,
        'bill_insurance_id', ed.bill_insurance_id,
        'expected_insurance_id', ed.expected_insurance_id
    )))
    INTO v_exceptions_json
    FROM exception_data ed;

    RAISE LOG 'generate_confirmed_dt_presets:v_results: patient_id=%, careplan_id=%, lgl_fltr=%, subform_charges=%, subform_exceptions=%', 
        v_patient_id, v_careplan_id, v_existing_lgl_ids, v_charge_lines_json, v_exceptions_json;
    
    -- Log successful completion
    PERFORM log_delivery_ticket_confirmation(
        'generate_confirmed_dt_presets'::tracked_function,
        v_params,
        jsonb_build_object(
            'patient_id', v_patient_id,
            'careplan_id', v_careplan_id,
            'lgl_fltr', v_existing_lgl_ids,
            'subform_exceptions', v_subform_exceptions
        ),
        NULL::text,
        clock_timestamp() - v_start_time
    );

    -- Build final JSON result
    v_result := json_strip_nulls(json_build_object(
        'patient_id', v_patient_id,
        'careplan_id', v_careplan_id,
        'lgl_fltr', v_existing_lgl_ids,
        'subform_charges', v_charge_lines_json,
        'subform_exceptions', v_exceptions_json
    ));

    -- Return the final result as JSON
    RETURN v_result;

EXCEPTION WHEN OTHERS THEN
    -- Log error
    v_error_message := SQLERRM;
    
    -- Log to billing error log
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        v_error_message,
        'Validating required fields in generate_confirmed_dt_presets',
        'FUNCTION',
        current_schema(),
        'form_careplan_delivery_tick',
        jsonb_build_object(
            'function_name', 'generate_confirmed_dt_presets',
            'careplan_dt_id', p_careplan_dt_id
        )
    );
    -- Log to NCPDP function log
    PERFORM log_delivery_ticket_confirmation(
        'generate_confirmed_dt_presets'::tracked_function,
        v_params,
        NULL::jsonb,
        v_error_message,
        clock_timestamp() - v_start_time
    );
    RAISE;
END;
$$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE FUNCTION process_charge_line_data(
    p_careplan_dt_id integer,
    p_user_id integer,
    p_all_charge_lines charge_line_with_split[],
    p_existing_lgl_ids integer[],
    p_patient_id integer,
    p_careplan_id integer
) RETURNS TABLE (
    patient_id integer,
    careplan_id integer,
    lgl_fltr integer[],
    subform_charges charge_line_with_split[],
    subform_exceptions dt_exception[]
) AS $$
DECLARE
    v_combined_charge_lines charge_line_with_split[] := '{}'::charge_line_with_split[];
    v_charge_line charge_line_with_split;
    v_start_time timestamptz;
    v_delivery_ticket RECORD;
    v_next_insurance_id INTEGER;
    v_next_payer_record RECORD;
    v_existing_charge RECORD;
    v_balance_info RECORD;
    v_exceptions dt_exception[];
    v_charged_inventory_ids integer[] DEFAULT '{}'::integer[];

  v_rx_guid uuid := gen_random_uuid();
  v_supply_guid uuid := gen_random_uuid();
  v_dme_guid uuid := gen_random_uuid();
  v_pa_guid uuid := gen_random_uuid();
  v_medid_guid uuid := gen_random_uuid();
BEGIN
    -- Record start time for performance tracking
    v_start_time := clock_timestamp();
    RAISE LOG 'Starting process_charge_line_data for dt_id: % at %', p_careplan_dt_id, v_start_time;

    -- Get delivery ticket info
    SELECT dt.* INTO v_delivery_ticket
    FROM form_careplan_delivery_tick dt 
    WHERE dt.id = p_careplan_dt_id
    AND dt.archived IS NOT TRUE
    AND dt.deleted IS NOT TRUE
    AND COALESCE(dt.void, 'No') <> 'Yes';

    IF v_delivery_ticket.id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Invalid or voided delivery ticket',
            'Processing charge line data',
            'FUNCTION',
            current_schema(),
            'form_careplan_delivery_tick',
            jsonb_build_object(
                'delivery_ticket_id', p_careplan_dt_id,
                'patient_id', p_patient_id,
                'careplan_id', p_careplan_id
            )
        );
        RAISE EXCEPTION 'Invalid or voided delivery ticket: %', p_careplan_dt_id;
    END IF;

    -- Get inventory IDs that have been charged (only if there are existing IDs)
    IF p_existing_lgl_ids IS NOT NULL AND array_length(p_existing_lgl_ids, 1) > 0 THEN
        SELECT array_agg(inventory_id) INTO v_charged_inventory_ids
        FROM form_ledger_charge_line
        WHERE id = ANY(p_existing_lgl_ids)
        AND payer_id <> 1;
    END IF;
    RAISE LOG 'process_charge_line_data:v_charged_inventory_ids: %', v_charged_inventory_ids;

    -- Check new charge lines for duplicates
    IF p_all_charge_lines IS NOT NULL AND array_length(p_all_charge_lines, 1) > 0 THEN
        FOR v_charge_line IN 
            SELECT *
            FROM unnest(p_all_charge_lines)
        LOOP
            -- Check if this charge line matches an existing one
            IF NOT EXISTS (
                SELECT 1 
                FROM form_ledger_charge_line lcl
                WHERE lcl.id = ANY(p_existing_lgl_ids)
                AND lcl.inventory_id = v_charge_line.inventory_id
                AND lcl.insurance_id = v_charge_line.insurance_id
                AND lcl.bill_quantity = v_charge_line.bill_quantity
                AND lcl.archived IS NOT TRUE
                AND lcl.deleted IS NOT TRUE
                AND COALESCE(lcl.void, 'No') <> 'Yes'
                AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
            ) THEN
                -- Add non-duplicate charge line to results
                v_combined_charge_lines := array_append(v_combined_charge_lines, v_charge_line);
            END IF;
        END LOOP;
    END IF;

    -- Get exceptions using original parameters
    v_exceptions := get_all_exceptions(
        p_careplan_dt_id,
        p_user_id,
        v_combined_charge_lines,
        p_existing_lgl_ids,
        v_charged_inventory_ids
    );

    RAISE LOG 'Completed process_charge_line_data in %ms', extract(milliseconds from clock_timestamp() - v_start_time);
    
    -- Return results as a table
    RETURN QUERY
    SELECT 
        p_patient_id::integer AS patient_id,
        p_careplan_id::integer AS careplan_id,
        p_existing_lgl_ids::integer[] AS lgl_fltr,
        v_combined_charge_lines as subform_charges,
        v_exceptions as subform_exceptions;

EXCEPTION WHEN OTHERS THEN
    -- Log error
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        SQLERRM,
        'Error in process_charge_line_data',
        'FUNCTION',
        current_schema(),
        'form_ledger_charge_line',
        jsonb_build_object(
            'delivery_dt_id', p_careplan_dt_id,
            'patient_id', p_patient_id,
            'careplan_id', p_careplan_id,
            'existing_lgl_ids', p_existing_lgl_ids,
            'execution_time_ms', extract(milliseconds from clock_timestamp() - v_start_time)
        )
    );
    
    RAISE;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Get delivery ticket items

DO $$ BEGIN
  PERFORM drop_all_function_signatures('get_delivery_ticket_items');
END $$;
CREATE OR REPLACE FUNCTION get_delivery_ticket_items(
    p_careplan_dt_id integer
) RETURNS TABLE (
    rx_id integer,
    inventory_id integer,
    dispense_quantity numeric,
    insurance_id integer,
    inventory_type text
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        dti.rx_id::integer,
        dti.inventory_id::integer,
        dti.dispense_quantity::numeric,
        dti.insurance_id::integer,
        dti.inventory_type::text
    FROM vw_delivery_ticket_pulled_items dti
    WHERE dti.delivery_ticket_id = p_careplan_dt_id AND dti.bill = 'Yes';
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_all_exceptions(
    p_careplan_dt_id integer,
    p_user_id integer,
    p_all_charge_lines charge_line_with_split[],
    p_existing_lgl_ids integer[],
    p_charged_inventory_ids integer[]
) RETURNS dt_exception[] AS $$
DECLARE
    v_exceptions dt_exception[] := '{}'::dt_exception[];
    v_dt_ticket_no text;
    v_temp_exceptions dt_exception[];
BEGIN
    -- Get delivery ticket number
    SELECT ticket_no INTO v_dt_ticket_no 
    FROM form_careplan_delivery_tick 
    WHERE id = p_careplan_dt_id
    AND archived IS NOT TRUE
    AND deleted IS NOT TRUE;

    -- Process exceptions in smaller batches
    
    -- 1. Quantity mismatches
    WITH new_charge_lines AS (
        -- Extract new charge lines to be created
        SELECT 
            cl.inventory_id,
            cl.insurance_id,
            cl.payer_id,
            cl.bill_quantity,
            cl.expected,
            cl.order_rx_id,
            cl.inventory_type,
            cl.copay
        FROM unnest(p_all_charge_lines) AS cl
        WHERE cl.payer_id <> 1
    ),
    existing_charge_lines AS (
        -- Find existing charge lines associated with this DT
        SELECT
            lcl.id,
            lcl.order_rx_id,
            lcl.inventory_id,
            lcl.insurance_id,
            lcl.bill_quantity,
            lcl.inventory_type,
            lcl.copay,
            lcl.payer_id,
            lcl.expected,
            bi.id as invoice_id
        FROM form_ledger_charge_line lcl
        INNER JOIN form_billing_invoice bi ON bi.invoice_no = lcl.invoice_no
        WHERE lcl.id = ANY(p_existing_lgl_ids) 
        AND lcl.payer_id <> 1
        AND lcl.archived IS NOT TRUE
        AND lcl.deleted IS NOT TRUE
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
    ),
    qty_exceptions AS (
        SELECT
            CONCAT(inv.name, ' expected quantity ', TO_CHAR(COALESCE(ncl.bill_quantity, 0), '999,999,999.00'), ', but found quantity ', TO_CHAR(COALESCE(ecl.bill_quantity, 0), '999,999,999.00')) as description,
            'QTYMIS' as dt_exception_id,
            p_user_id as exception_by,
            TO_CHAR(CURRENT_TIMESTAMP, 'MM/DD/YYYY HH:mm a') as exception_datetime,
            ncl.inventory_id as inventory_id,
            ecl.inventory_id as billed_inventory_id,
            ecl.invoice_id as invoice_id,
            ncl.bill_quantity::numeric as dispense_quantity,
            ecl.bill_quantity::numeric as bill_quantity,
            ncl.expected::numeric as expected,
            ncl.copay::numeric as copay,
            ecl.insurance_id as bill_insurance_id,
            ncl.insurance_id as expected_insurance_id
        FROM new_charge_lines ncl
        INNER JOIN existing_charge_lines ecl ON 
            ncl.order_rx_id = ecl.order_rx_id AND
            ncl.inventory_id = ecl.inventory_id AND
            ecl.insurance_id = ncl.insurance_id
        INNER JOIN form_inventory inv ON inv.id = ncl.inventory_id
        WHERE ecl.bill_quantity != ncl.bill_quantity
    )
    SELECT array_agg((
        description,
        dt_exception_id,
        exception_by,
        exception_datetime,
        inventory_id,
        billed_inventory_id,
        invoice_id,
        dispense_quantity,
        bill_quantity,
        expected,
        copay,
        bill_insurance_id,
        expected_insurance_id
    )::dt_exception)
    INTO v_temp_exceptions
    FROM qty_exceptions;
    
    -- Add to main exceptions array
    IF v_temp_exceptions IS NOT NULL THEN
        v_exceptions := v_exceptions || v_temp_exceptions;
    END IF;

    -- 2. Bill status mismatches
    WITH dt_items AS (
        SELECT 
            item.rx_id::integer AS rx_id,
            item.inventory_id::integer AS inventory_id
        FROM get_delivery_ticket_items(p_careplan_dt_id) item
    ),
    new_charge_lines AS (
        -- Extract new charge lines to be created
        SELECT 
            cl.inventory_id,
            cl.insurance_id,
            cl.payer_id,
            cl.bill_quantity,
            cl.expected,
            cl.order_rx_id,
            cl.inventory_type,
            cl.copay
        FROM unnest(p_all_charge_lines) AS cl
    ),
    existing_charge_lines AS (
        -- Find existing charge lines
        SELECT
            lcl.id,
            lcl.order_rx_id,
            lcl.inventory_id,
            lcl.insurance_id,
            lcl.bill_quantity,
            lcl.inventory_type,
            lcl.copay,
            lcl.payer_id,
            lcl.expected,
            bi.id as invoice_id
        FROM form_ledger_charge_line lcl
        INNER JOIN form_billing_invoice bi ON bi.invoice_no = lcl.invoice_no
        WHERE lcl.id = ANY(p_existing_lgl_ids) AND lcl.payer_id <> 1
        AND lcl.archived IS NOT TRUE
        AND lcl.deleted IS NOT TRUE
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
    ),
    bill_exceptions AS (
        -- Bill status mismatches
        SELECT 
            CONCAT(inv.name, ' charged but was not found to be billable on ticket') as description,
            'BILLMIS' as dt_exception_id,
            p_user_id as exception_by,
            TO_CHAR(CURRENT_TIMESTAMP, 'MM/DD/YYYY HH:mm a') as exception_datetime,
            ecl.inventory_id as inventory_id,
            ecl.inventory_id as billed_inventory_id,
            ecl.invoice_id as invoice_id,
            ecl.bill_quantity::numeric as dispense_quantity,
            ecl.bill_quantity::numeric as bill_quantity,
            ecl.expected::numeric as expected,
            ecl.copay::numeric as copay,
            ecl.insurance_id as bill_insurance_id,
            ecl.insurance_id as expected_insurance_id
        FROM existing_charge_lines ecl
        INNER JOIN form_inventory inv ON inv.id = ecl.inventory_id
        WHERE NOT EXISTS (
            SELECT 1 
            FROM dt_items itm
            WHERE itm.rx_id = ecl.order_rx_id
            AND itm.inventory_id = ecl.inventory_id
        )
    )
    SELECT array_agg((
        description,
        dt_exception_id,
        exception_by,
        exception_datetime,
        inventory_id,
        billed_inventory_id,
        invoice_id,
        dispense_quantity,
        bill_quantity,
        expected,
        copay,
        bill_insurance_id,
        expected_insurance_id
    )::dt_exception)
    INTO v_temp_exceptions
    FROM bill_exceptions;
    
    -- Add to main exceptions array
    IF v_temp_exceptions IS NOT NULL THEN
        v_exceptions := v_exceptions || v_temp_exceptions;
    END IF;

    -- 3. Optimize the payer/inventory mismatch queries by materializing common data
    -- Create a temporary table for DT items to improve join performance
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_dt_items (
        rx_id integer,
        inventory_id integer,
        dispense_quantity numeric,
        insurance_id integer,
        inventory_type text,
        ticket_no text
    ) ON COMMIT DROP;
    
    -- Clear previous data
    TRUNCATE temp_dt_items;
    
    -- Populate with current delivery ticket items
    INSERT INTO temp_dt_items
    SELECT 
        dti.rx_id,
        dti.inventory_id,
        dti.dispense_quantity,
        dti.insurance_id,
        dti.inventory_type,
        dti.ticket_no
    FROM vw_delivery_ticket_pulled_items dti
    WHERE dti.delivery_ticket_id = p_careplan_dt_id 
    AND dti.bill = 'Yes';
    
    -- Create index to speed up joins
    CREATE INDEX IF NOT EXISTS temp_dt_items_rx_id_idx ON temp_dt_items(rx_id);
    CREATE INDEX IF NOT EXISTS temp_dt_items_inventory_id_idx ON temp_dt_items(inventory_id);
    
    -- Get Payer mismatches (optimized)
    WITH rx_data AS (
        -- Get only the RX data we need
        SELECT DISTINCT rx.rx_id, rx.working_dispense_id, rx.rx_no
        FROM vw_rx_order rx
        WHERE rx.rx_id IN (SELECT rx_id FROM temp_dt_items)
    ),
    invoice_data AS (
        -- Get invoice data for just these RXs
        SELECT rxinv.*
        FROM vw_rx_invoices rxinv
        INNER JOIN rx_data rx ON rx.working_dispense_id = rxinv.working_dispense_id
    ),
    charge_line_data AS (
        -- Only get charge lines for these invoices that match our existing ids
        SELECT lcl.*, inv.name as inventory_name
        FROM form_ledger_charge_line lcl
        INNER JOIN invoice_data rxinv ON rxinv.invoice_no = lcl.invoice_no
        INNER JOIN form_inventory inv ON inv.id = lcl.inventory_id
        WHERE lcl.id = ANY(p_existing_lgl_ids)
        AND lcl.archived IS NOT TRUE
        AND lcl.deleted IS NOT TRUE
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
        AND inv.archived IS NOT TRUE
        AND inv.deleted IS NOT TRUE
    ),
    payer_info AS (
        -- Get payer info for just the insurances we need
        SELECT 
            pi.id as insurance_id,
            p.id as payer_id,
            p.organization
        FROM form_patient_insurance pi
        INNER JOIN form_payer p ON p.id = pi.payer_id
        WHERE (pi.id IN (SELECT DISTINCT insurance_id FROM temp_dt_items)
               OR pi.id IN (SELECT DISTINCT insurance_id FROM charge_line_data))
        AND pi.archived IS NOT TRUE
        AND pi.deleted IS NOT TRUE
        AND p.archived IS NOT TRUE
        AND p.deleted IS NOT TRUE
    ),
    payer_mismatches AS (
        SELECT 
            'Expected payer ' || p2.organization || ', but found payer ' || p1.organization as description,
            'PYRWRG' as dt_exception_id,
            p_user_id as exception_by,
            TO_CHAR(CURRENT_TIMESTAMP, 'MM/DD/YYYY HH:mm a') as exception_datetime,
            lcl.inventory_id as inventory_id,
            lcl.inventory_id as billed_inventory_id,
            rxinv.invoice_id as invoice_id,
            dti.dispense_quantity::numeric as dispense_quantity,
            lcl.bill_quantity::numeric as bill_quantity,
            lcl.expected::numeric as expected,
            COALESCE(dti.dispense_quantity, lcl.copay, 0)::numeric as copay,
            lcl.insurance_id as bill_insurance_id,
            dti.insurance_id as expected_insurance_id
        FROM temp_dt_items dti
        INNER JOIN rx_data rx ON rx.rx_id = dti.rx_id
        INNER JOIN invoice_data rxinv ON rxinv.working_dispense_id = rx.working_dispense_id
        INNER JOIN charge_line_data lcl ON lcl.order_rx_id = dti.rx_id
            AND lcl.inventory_id = dti.inventory_id
            AND lcl.insurance_id != dti.insurance_id
        INNER JOIN payer_info p1 ON p1.insurance_id = lcl.insurance_id
        INNER JOIN payer_info p2 ON p2.insurance_id = dti.insurance_id
    )
    SELECT array_agg((
        description,
        dt_exception_id,
        exception_by,
        exception_datetime,
        inventory_id,
        billed_inventory_id,
        invoice_id,
        dispense_quantity,
        bill_quantity,
        expected,
        copay,
        bill_insurance_id,
        expected_insurance_id
    )::dt_exception)
    INTO v_temp_exceptions
    FROM payer_mismatches;
    
    -- Add to main exceptions array
    IF v_temp_exceptions IS NOT NULL THEN
        v_exceptions := v_exceptions || v_temp_exceptions;
    END IF;

    -- 4. Inventory mismatches (using the temporary table)
    WITH charge_line_data AS (
        -- Only get charge lines for these invoices that match our existing ids
        SELECT lcl.*, inv.name as inventory_name
        FROM form_ledger_charge_line lcl
        INNER JOIN form_billing_invoice bi ON bi.invoice_no = lcl.invoice_no
        INNER JOIN form_inventory inv ON inv.id = lcl.inventory_id
        WHERE lcl.id = ANY(p_existing_lgl_ids)
        AND lcl.archived IS NOT TRUE
        AND lcl.deleted IS NOT TRUE
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
        AND inv.archived IS NOT TRUE
        AND inv.deleted IS NOT TRUE
    ),
    rx_data AS (
        -- Get only the RX data we need
        SELECT DISTINCT rx.rx_id, rx.working_dispense_id 
        FROM vw_rx_order rx
        WHERE rx.rx_id IN (SELECT rx_id FROM temp_dt_items)
    ),
    invoice_data AS (
        -- Get invoice data for just these RXs
        SELECT rxinv.*
        FROM vw_rx_invoices rxinv
        INNER JOIN rx_data rx ON rx.working_dispense_id = rxinv.working_dispense_id
    ),
    inventory_mismatches AS (
        SELECT 
            'Drug on claim ' || lcl.inventory_name || ' does not match any drug on work ticket' as description,
            'INVMIS' as dt_exception_id,
            p_user_id as exception_by,
            TO_CHAR(CURRENT_TIMESTAMP, 'MM/DD/YYYY HH:mm a') as exception_datetime,
            dti.inventory_id as inventory_id,
            lcl.inventory_id as billed_inventory_id,
            inv.invoice_id as invoice_id,
            dti.dispense_quantity::numeric as dispense_quantity,
            lcl.bill_quantity::numeric as bill_quantity,
            lcl.expected::numeric as expected,
            lcl.copay::numeric as copay,
            lcl.insurance_id as bill_insurance_id,
            dti.insurance_id as expected_insurance_id
        FROM temp_dt_items dti
        INNER JOIN rx_data rx ON rx.rx_id = dti.rx_id
        INNER JOIN invoice_data inv ON inv.working_dispense_id = rx.working_dispense_id
        INNER JOIN charge_line_data lcl ON lcl.invoice_no = inv.invoice_no
            AND lcl.order_rx_id = dti.rx_id
        WHERE dti.inventory_type IN ('Drug', 'Compound')
        AND NOT EXISTS (
            SELECT 1
            FROM temp_dt_items wtp
            WHERE wtp.inventory_id = lcl.inventory_id 
            AND wtp.ticket_no = dti.ticket_no
        )
    )
    SELECT array_agg((
        description,
        dt_exception_id,
        exception_by,
        exception_datetime,
        inventory_id,
        billed_inventory_id,
        invoice_id,
        dispense_quantity,
        bill_quantity,
        expected,
        copay,
        bill_insurance_id,
        expected_insurance_id
    )::dt_exception)
    INTO v_temp_exceptions
    FROM inventory_mismatches;
    
    -- Add to main exceptions array
    IF v_temp_exceptions IS NOT NULL THEN
        v_exceptions := v_exceptions || v_temp_exceptions;
    END IF;

    -- Clean up temporary tables
    DROP TABLE IF EXISTS temp_dt_items;

    RETURN v_exceptions;
END;
$$ LANGUAGE plpgsql;