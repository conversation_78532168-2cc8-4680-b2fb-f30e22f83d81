-- Trigger for form_careplan_orderp_item to sync status_id with matching prescription record
DO $$ BEGIN
  PERFORM drop_all_function_signatures('sync_orderp_status_to_rx');
END $$;
CREATE OR REPLACE FUNCTION sync_orderp_status_to_rx()
RETURNS TRIGGER AS $$
BEGIN
        -- Check if status_id is being updated and not null
        IF NEW.status_id IS NOT NULL AND (OLD.status_id IS DISTINCT FROM NEW.status_id) THEN
            UPDATE form_careplan_order_rx rx 
            SET status_id = NEW.status_id
            WHERE rx.rx_no = NEW.rx_no
            AND rx.rx_no IS NOT NULL;

            UPDATE form_careplan_order_rx_disp disp
            SET status = NULL
            WHERE disp.rx_no = NEW.rx_no
            AND NEW.status_id NOT IN ('1', '5');
        END IF;
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Error in sync_orderp_status_to_rx: %', SQLERRM;
    END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_sync_orderp_status
AFTER UPDATE ON form_careplan_orderp_item
FOR EACH ROW
EXECUTE FUNCTION sync_orderp_status_to_rx();

DO $$ BEGIN
  PERFORM drop_all_function_signatures('sync_order_status_to_rx');
END $$;
CREATE OR REPLACE FUNCTION sync_order_status_to_rx()
RETURNS TRIGGER AS $$
BEGIN
        -- Check if status_id is being updated and not null
        IF NEW.status_id IS NOT NULL AND (OLD.status_id IS DISTINCT FROM NEW.status_id) THEN
            UPDATE form_careplan_order_rx rx
            SET status_id = NEW.status_id
            WHERE rx.rx_no = NEW.rx_no
            AND rx.rx_no IS NOT NULL;

            UPDATE form_careplan_order_rx_disp disp
            SET status = NULL
            WHERE disp.rx_no = NEW.rx_no
            AND NEW.status_id NOT IN ('1', '5');

        END IF;
        RETURN NEW;
    EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Error in sync_order_status_to_rx: %', SQLERRM;
    END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_sync_order_status
AFTER UPDATE ON form_careplan_order_item
FOR EACH ROW
EXECUTE FUNCTION sync_order_status_to_rx();
