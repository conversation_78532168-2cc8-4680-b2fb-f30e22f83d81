-- Medical Claim Response Triggers
-- Main trigger that processes medical claim responses and calls appropriate parsing functions

-- Every day at 7am EST, change the status of claims that are on hold to 'Ready to bill'. 
SELECT pgboss.create_queue('worker_claim_status_sync'::text, '{"policy":"standard"}'::json);
INSERT INTO pgboss.schedule (name,cron,data,options,timezone) VALUES ('worker_claim_status_sync','0 7 * * *', '{"action":"process_claim_status_sync"}','{}','America/New_York') ON CONFLICT (name) DO UPDATE SET "cron" = EXCLUDED."cron", "timezone" = EXCLUDED."timezone";

-- Corrected trigger function for medical claim response processing

CREATE OR REPLACE FUNCTION process_medical_claim_response() 
RETURNS TRIGGER AS $$
DECLARE
    v_response_json JSONB;
    v_error_message TEXT;
    v_response_type TEXT;
    v_start_time TIMESTAMP;
    v_params JSONB;
BEGIN
    -- Record start time
    v_start_time := clock_timestamp();
    
    -- Build parameters for logging
    v_params := jsonb_build_object(
        'response_id', NEW.id,
        'response_type', NEW.response_type
    );
    
    BEGIN
        -- Log trigger start
        BEGIN
            PERFORM log_billing_function(
                'process_medical_claim_response'::tracked_function,
                v_params::jsonb,
                NULL::jsonb,
                NULL::text,
                clock_timestamp() - v_start_time
            );
        EXCEPTION WHEN OTHERS THEN
            -- Ignore logging errors
            NULL;
        END;
        
        -- Validate required fields
        IF NEW.response_raw_json IS NULL OR NEW.response_raw_json = '' THEN
            RAISE WARNING 'Missing response_raw_json for response_id: %', NEW.id;
            RETURN NEW;
        END IF;
        
        IF NEW.response_type IS NULL OR NEW.response_type = '' THEN
            RAISE WARNING 'Missing response_type for response_id: %', NEW.id;
            RETURN NEW;
        END IF;
        
        -- Convert TEXT response to JSONB with error handling
        BEGIN
            v_response_json := NEW.response_raw_json::JSONB;
        EXCEPTION WHEN OTHERS THEN
            v_error_message := 'Invalid JSON in response_raw_json: ' || SQLERRM;
            
            -- Log to billing error log
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                v_error_message,
                'JSON conversion failed in process_medical_claim_response',
                'DATA_FORMAT',
                current_schema(),
                'form_med_claim_resp_log',
                jsonb_build_object(
                    'response_id', NEW.id,
                    'response_type', NEW.response_type,
                    'raw_length', length(NEW.response_raw_json)
                )
            );
            
            RAISE WARNING 'Invalid JSON for response_id: %, error: %', NEW.id, SQLERRM;
            RETURN NEW;
        END;
        
        -- Normalize response type (case insensitive, trim whitespace, remove 'f' prefix)
        v_response_type := upper(trim(NEW.response_type));
        
        -- Remove 'f' prefix if present (e.g., 'f277' -> '277')
        IF v_response_type LIKE 'F%' THEN
            v_response_type := substring(v_response_type from 2);
        END IF;
        
        -- Route to appropriate parser based on response type
        CASE v_response_type
            WHEN '277' THEN
                PERFORM parse_277_response(
                    NEW.id,
                    v_response_json,
                    COALESCE(NEW.created_by::INTEGER, 1),
                    COALESCE(NEW.created_on, CURRENT_TIMESTAMP)::TIMESTAMP
                );
                
            WHEN '835' THEN
                PERFORM parse_835_response(
                    NEW.id,
                    v_response_json,
                    COALESCE(NEW.created_by::INTEGER, 1),
                    COALESCE(NEW.created_on, CURRENT_TIMESTAMP)::TIMESTAMP
                );
                
            WHEN '999' THEN
                PERFORM parse_999_response(
                    NEW.id,
                    v_response_json,
                    COALESCE(NEW.created_by::INTEGER, 1),
                    COALESCE(NEW.created_on, CURRENT_TIMESTAMP)::TIMESTAMP
                );
            WHEN '997' THEN
                -- 997 Functional Acknowledgment
                -- Log that we received it but don't process yet
                INSERT INTO billing_error_log (
                    error_message,
                    error_context,
                    error_type,
                    schema_name,
                    table_name,
                    additional_details
                ) VALUES (
                    '997 response type not yet implemented',
                    'Received 997 response in process_medical_claim_response',
                    'NOT_IMPLEMENTED',
                    current_schema(),
                    'form_med_claim_resp_log',
                    v_params
                );
                
                RAISE WARNING '997 response processing not yet implemented for response_id: %', NEW.id;
                
            ELSE
                v_error_message := 'Unknown response_type: ' || NEW.response_type;
                
                -- Log to billing error log
                INSERT INTO billing_error_log (
                    error_message,
                    error_context,
                    error_type,
                    schema_name,
                    table_name,
                    additional_details
                ) VALUES (
                    v_error_message,
                    'Unknown response type in process_medical_claim_response',
                    'CONFIGURATION',
                    current_schema(),
                    'form_med_claim_resp_log',
                    v_params
                );
                
                RAISE WARNING 'Unknown response_type for response_id: %, type: %', NEW.id, NEW.response_type;
        END CASE;
        
        -- Log successful completion
        BEGIN
            PERFORM log_billing_function(
                'process_medical_claim_response'::tracked_function,
                v_params,
                jsonb_build_object('success', true, 'response_type', v_response_type),
                NULL::text,
                clock_timestamp() - v_start_time
            );
        EXCEPTION WHEN OTHERS THEN
            -- Ignore logging errors
            NULL;
        END;
        
    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        v_error_message := SQLERRM;
        
        -- Log to billing error log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            v_error_message,
            'Exception in process_medical_claim_response trigger',
            'TRIGGER',
            current_schema(),
            'form_med_claim_resp_log',
            v_params || jsonb_build_object(
                'error_detail', SQLSTATE,
                'error_hint', SQLERRM
            )
        );
        
        -- Log error to function log
        BEGIN
            PERFORM log_billing_function(
                'process_medical_claim_response'::tracked_function,
                v_params::jsonb,
                NULL::jsonb,
                v_error_message::text,
                clock_timestamp() - v_start_time
            );
        EXCEPTION WHEN OTHERS THEN
            -- Ignore logging errors
            NULL;
        END;
        
        RAISE WARNING 'Error processing medical claim response (response_id: %, type: %): %', 
                     NEW.id, NEW.response_type, v_error_message;
        
        -- Don't re-raise the exception - let the insert succeed even if parsing fails
        -- This prevents losing the response data
    END;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Ensure the trigger exists
DROP TRIGGER IF EXISTS trg_process_medical_claim_response ON form_med_claim_resp_log;
CREATE TRIGGER trg_process_medical_claim_response
    AFTER INSERT ON form_med_claim_resp_log
    FOR EACH ROW
    EXECUTE FUNCTION process_medical_claim_response();

-- Add comment to describe the trigger
COMMENT ON TRIGGER trg_process_medical_claim_response ON form_med_claim_resp_log IS 
'Trigger that automatically processes medical claim responses (277, 835, 999) when inserted into the log table';

-- Add function comments
COMMENT ON FUNCTION process_medical_claim_response() IS 
'Main function that routes medical claim responses to appropriate parsing functions based on response_type';

-- 1. Trigger to set other_payer_claim_adjustment_indicator when adjustments exist
CREATE OR REPLACE FUNCTION update_other_payer_adjustment_indicator()
RETURNS TRIGGER AS $$
DECLARE
    v_adj_id INTEGER;
BEGIN
    -- Get the adjustment ID based on which table triggered this
    IF TG_TABLE_NAME = 'form_med_claim_adj' THEN
        -- Direct trigger from adjustment table
        v_adj_id := NEW.id;
    ELSIF TG_TABLE_NAME = 'form_med_claim_adj_dl' THEN
        -- Trigger from adjustment details - need to find parent adjustment
        SELECT sf_dl.form_med_claim_adj_fk INTO v_adj_id
        FROM sf_form_med_claim_adj_to_med_claim_adj_dl sf_dl
        WHERE sf_dl.form_med_claim_adj_dl_fk = NEW.id
        AND sf_dl.delete IS NOT TRUE
        AND sf_dl.archive IS NOT TRUE
        LIMIT 1;
    END IF;

    -- If we couldn't find an adjustment ID, exit
    IF v_adj_id IS NULL THEN
        RETURN NEW;
    END IF;

    -- Update the corresponding other payer's adjustment indicator
    -- We need to traverse: med_claim_adj -> med_claim_osub -> med_claim_opayer
    -- An adjustment exists if it has adjustment details and is not archived
    UPDATE form_med_claim_opayer opay
    SET other_payer_claim_adjustment_indicator = 
        CASE 
            WHEN EXISTS (
                SELECT 1 
                FROM form_med_claim_adj adj
                JOIN sf_form_med_claim_osub_to_med_claim_adj sf_adj 
                    ON sf_adj.form_med_claim_adj_fk = adj.id 
                    AND sf_adj.delete IS NOT TRUE 
                    AND sf_adj.archive IS NOT TRUE
                JOIN form_med_claim_osub osub 
                    ON osub.id = sf_adj.form_med_claim_osub_fk 
                    AND osub.deleted IS NOT TRUE 
                    AND osub.archived IS NOT TRUE
                -- Check if adjustment has details
                JOIN sf_form_med_claim_adj_to_med_claim_adj_dl sf_dl
                    ON sf_dl.form_med_claim_adj_fk = adj.id
                    AND sf_dl.delete IS NOT TRUE
                    AND sf_dl.archive IS NOT TRUE
                JOIN form_med_claim_adj_dl dl
                    ON dl.id = sf_dl.form_med_claim_adj_dl_fk
                    AND dl.deleted IS NOT TRUE
                    AND dl.archived IS NOT TRUE
                WHERE osub.cob_payer_id = opay.cob_payer_id
                AND adj.deleted IS NOT TRUE 
                AND adj.archived IS NOT TRUE
            ) THEN 'Y' 
            ELSE NULL::text
        END,
        updated_on = CURRENT_TIMESTAMP,
        updated_by = 1 -- system user
    WHERE opay.id IN (
        -- Find all opayer records related to the same osub that has this adjustment
        SELECT DISTINCT opay2.id
        FROM form_med_claim_osub osub2
        JOIN sf_form_med_claim_osub_to_med_claim_adj sf_adj2 
            ON sf_adj2.form_med_claim_osub_fk = osub2.id
            AND sf_adj2.form_med_claim_adj_fk = v_adj_id
            AND sf_adj2.delete IS NOT TRUE 
            AND sf_adj2.archive IS NOT TRUE
        JOIN sf_form_med_claim_osub_to_med_claim_opayer sf_opay2
            ON sf_opay2.form_med_claim_osub_fk = osub2.id
            AND sf_opay2.delete IS NOT TRUE 
            AND sf_opay2.archive IS NOT TRUE
        JOIN form_med_claim_opayer opay2
            ON opay2.id = sf_opay2.form_med_claim_opayer_fk
            AND opay2.deleted IS NOT TRUE 
            AND opay2.archived IS NOT TRUE
        WHERE opay2.cob_payer_id = osub2.cob_payer_id
        AND osub2.deleted IS NOT TRUE 
        AND osub2.archived IS NOT TRUE
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- CREATE TRIGGERs for adjustment indicator
-- Trigger on the main adjustment form
DROP TRIGGER IF EXISTS tr_update_adjustment_indicator ON form_med_claim_adj;

CREATE TRIGGER tr_update_adjustment_indicator
    AFTER INSERT OR UPDATE ON form_med_claim_adj
    FOR EACH ROW
    EXECUTE FUNCTION update_other_payer_adjustment_indicator();

-- Also trigger on adjustment details since that's where the actual data is
-- Note: The INSERT trigger on form_med_claim_adj_dl won't find the parent adjustment
-- because the linking record in sf_form_med_claim_adj_to_med_claim_adj_dl is created 
-- after the detail insert. The indicator will be properly set on UPDATE operations 
-- or when the adjustment detail records are modified.
DROP TRIGGER IF EXISTS tr_update_adjustment_indicator_details ON form_med_claim_adj_dl;

CREATE TRIGGER tr_update_adjustment_indicator_details
    AFTER INSERT OR UPDATE ON form_med_claim_adj_dl
    FOR EACH ROW
    EXECUTE FUNCTION update_other_payer_adjustment_indicator();

-- 3. Trigger to sync service dates for rental charge lines
CREATE OR REPLACE FUNCTION sync_rental_service_dates()
RETURNS TRIGGER AS $$
DECLARE
    v_claim_no TEXT;
    v_payer_id INTEGER;
    v_sos_only BOOLEAN;
    v_days INTEGER;
    v_dme_id INTEGER;
BEGIN
    -- Only process if billing_method_id is 'mm' (electronic medical)
    IF NEW.billing_method_id != 'mm' THEN
        RETURN NEW;
    END IF;
    
    -- Only process rental charge lines (rental_type is set OR rental_id is set)
    IF NEW.rental_type IS NULL AND NEW.rental_id IS NULL THEN
        RETURN NEW;
    END IF;
    
    -- Only process if service dates changed
    IF TG_OP = 'UPDATE' AND 
       OLD.date_of_service IS NOT DISTINCT FROM NEW.date_of_service AND
       OLD.date_of_service_end IS NOT DISTINCT FROM NEW.date_of_service_end THEN
        RETURN NEW;
    END IF;
    
    -- Calculate days between dates (inclusive)
    IF NEW.date_of_service IS NOT NULL AND NEW.date_of_service_end IS NOT NULL THEN
        v_days := (NEW.date_of_service_end - NEW.date_of_service)::INTEGER + 1;
    ELSE
        v_days := 1;
    END IF;
    
    -- Get claim number and payer_id for electronic medical claim
    SELECT mc.claim_no, mc.payer_id INTO v_claim_no, v_payer_id
    FROM form_med_claim mc
    WHERE mc.invoice_no = NEW.invoice_no
    AND mc.deleted IS NOT TRUE
    AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    IF v_claim_no IS NOT NULL AND v_payer_id IS NOT NULL THEN
        -- Check payer's mm_sos_only setting
        SELECT COALESCE(p.mm_sos_only, 'No') = 'Yes' INTO v_sos_only
        FROM form_payer p
        WHERE p.id = v_payer_id
        AND p.deleted IS NOT TRUE
        AND p.archived IS NOT TRUE;
        
        -- Update service line dates
        UPDATE form_med_claim_sl
        SET service_date = NEW.date_of_service,
            service_date_end = CASE 
                WHEN v_sos_only THEN NULL  -- Don't set end date if SOS only
                ELSE COALESCE(NEW.date_of_service_end, NEW.date_of_service)
            END,
            updated_on = NEW.updated_on,
            updated_by = NEW.updated_by
        WHERE claim_no = v_claim_no
        AND charge_no = NEW.charge_no
        AND deleted IS NOT TRUE
        AND archived IS NOT TRUE;
        
        -- Update DME days if this is a rental
        UPDATE form_med_claim_dme dme
        SET days = v_days,
            updated_on = NEW.updated_on,
            updated_by = NEW.updated_by
        WHERE dme.charge_no = NEW.charge_no
        AND dme.deleted IS NOT TRUE
        AND dme.archived IS NOT TRUE
        AND EXISTS (
            SELECT 1
            FROM form_med_claim_sl sl
            INNER JOIN sf_form_med_claim_sl_to_med_claim_dme sf_dme
                ON sf_dme.form_med_claim_sl_fk = sl.id
                AND sf_dme.form_med_claim_dme_fk = dme.id
                AND sf_dme.delete IS NOT TRUE
                AND sf_dme.archive IS NOT TRUE
            WHERE sl.claim_no = v_claim_no
            AND sl.charge_no = NEW.charge_no
            AND sl.deleted IS NOT TRUE
            AND sl.archived IS NOT TRUE
        );
        
        -- Update claim header dates if this affects the range
        UPDATE form_med_claim mc
        SET service_date = (
                SELECT MIN(sl.service_date)
                FROM form_med_claim_sl sl
                WHERE sl.claim_no = mc.claim_no
                AND sl.deleted IS NOT TRUE
                AND sl.archived IS NOT TRUE
            ),
            updated_on = NEW.updated_on,
            updated_by = NEW.updated_by
        WHERE mc.claim_no = v_claim_no
        AND mc.deleted IS NOT TRUE
        AND mc.archived IS NOT TRUE;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Update trigger to include both rental_type and rental_id
DROP TRIGGER IF EXISTS tr_sync_rental_service_dates ON form_ledger_charge_line;

CREATE TRIGGER tr_sync_rental_service_dates
    AFTER UPDATE OF date_of_service, date_of_service_end ON form_ledger_charge_line
    FOR EACH ROW
    WHEN (NEW.rental_type IS NOT NULL OR NEW.rental_id IS NOT NULL)
    EXECUTE FUNCTION sync_rental_service_dates();

-- 4. Trigger to keep diagnoses in sync between service lines and diagnosis entries
CREATE OR REPLACE FUNCTION sync_service_line_diagnoses()
RETURNS TRIGGER AS $$
DECLARE
    v_claim_info_id INTEGER;
    v_dx_exists BOOLEAN;
    v_dx_id INTEGER;
    v_dx_code TEXT;
    v_patient_id INTEGER;
    v_dx_fields TEXT[] := ARRAY['dx_id_1', 'dx_id_2', 'dx_id_3', 'dx_id_4'];
    v_field TEXT;
    v_old_dx_id INTEGER;
    v_new_dx_id INTEGER;
    v_dx_used BOOLEAN;
    v_old_dx_type_code TEXT;
    v_new_dx_type_code TEXT;
    v_has_primary BOOLEAN;
BEGIN
    -- Get the claim info ID this service line belongs to
    SELECT mci.id, mc.patient_id INTO v_claim_info_id, v_patient_id
    FROM form_med_claim_sv sv
    INNER JOIN sf_form_med_claim_sl_to_med_claim_sv sf_sv 
        ON sf_sv.form_med_claim_sv_fk = sv.id
        AND sf_sv.delete IS NOT TRUE
        AND sf_sv.archive IS NOT TRUE
    INNER JOIN form_med_claim_sl sl 
        ON sl.id = sf_sv.form_med_claim_sl_fk
        AND sl.deleted IS NOT TRUE
        AND sl.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_info_to_med_claim_sl sf_isl
        ON sf_isl.form_med_claim_sl_fk = sl.id
        AND sf_isl.delete IS NOT TRUE
        AND sf_isl.archive IS NOT TRUE
    INNER JOIN form_med_claim_info mci
        ON mci.id = sf_isl.form_med_claim_info_fk
        AND mci.deleted IS NOT TRUE
        AND mci.archived IS NOT TRUE
    INNER JOIN sf_form_med_claim_to_med_claim_info sf_ci
        ON sf_ci.form_med_claim_info_fk = mci.id
        AND sf_ci.delete IS NOT TRUE
        AND sf_ci.archive IS NOT TRUE
    INNER JOIN form_med_claim mc
        ON mc.id = sf_ci.form_med_claim_fk
        AND mc.deleted IS NOT TRUE
        AND mc.archived IS NOT TRUE
    WHERE sv.id = NEW.id
    LIMIT 1;
    
    IF v_claim_info_id IS NULL THEN
        RETURN NEW;
    END IF;

    -- Check each diagnosis field
    FOREACH v_field IN ARRAY v_dx_fields LOOP
        -- Get old and new values dynamically
        EXECUTE format('SELECT ($1).%I::INTEGER', v_field) INTO v_old_dx_id USING OLD;
        EXECUTE format('SELECT ($1).%I::INTEGER', v_field) INTO v_new_dx_id USING NEW;
        
        -- Skip if no change
        IF v_old_dx_id IS NOT DISTINCT FROM v_new_dx_id THEN
            CONTINUE;
        END IF;
        
        -- If old diagnosis removed, check if it's still used and if it's primary
        IF v_old_dx_id IS NOT NULL AND v_new_dx_id IS DISTINCT FROM v_old_dx_id THEN
            -- Get the diagnosis type code of the old diagnosis
            SELECT dx.diagnosis_type_code INTO v_old_dx_type_code
            FROM form_med_claim_dx dx
            INNER JOIN sf_form_med_claim_info_to_med_claim_dx sf_dx
                ON sf_dx.form_med_claim_dx_fk = dx.id
                AND sf_dx.form_med_claim_info_fk = v_claim_info_id
                AND sf_dx.delete IS NOT TRUE
                AND sf_dx.archive IS NOT TRUE
            WHERE dx.dx_id = v_old_dx_id
            AND dx.deleted IS NOT TRUE
            AND dx.archived IS NOT TRUE
            LIMIT 1;
            
            -- Check if any other service line still uses this diagnosis
            SELECT EXISTS(
                SELECT 1
                FROM form_med_claim_sv sv2
                INNER JOIN sf_form_med_claim_sl_to_med_claim_sv sf_sv2
                    ON sf_sv2.form_med_claim_sv_fk = sv2.id
                    AND sf_sv2.delete IS NOT TRUE
                    AND sf_sv2.archive IS NOT TRUE
                INNER JOIN form_med_claim_sl sl2
                    ON sl2.id = sf_sv2.form_med_claim_sl_fk
                    AND sl2.deleted IS NOT TRUE
                    AND sl2.archived IS NOT TRUE
                INNER JOIN sf_form_med_claim_info_to_med_claim_sl sf_isl2
                    ON sf_isl2.form_med_claim_sl_fk = sl2.id
                    AND sf_isl2.delete IS NOT TRUE
                    AND sf_isl2.archive IS NOT TRUE
                WHERE sf_isl2.form_med_claim_info_fk = v_claim_info_id
                AND sv2.id != NEW.id
                AND sv2.deleted IS NOT TRUE
                AND sv2.archived IS NOT TRUE
                AND (sv2.dx_id_1 = v_old_dx_id OR 
                     sv2.dx_id_2 = v_old_dx_id OR 
                     sv2.dx_id_3 = v_old_dx_id OR 
                     sv2.dx_id_4 = v_old_dx_id)
            ) INTO v_dx_used;
            
            IF NOT v_dx_used THEN
                -- If this is the primary diagnosis (ABK), remember that
                IF v_old_dx_type_code = 'ABK' THEN
                    v_new_dx_type_code := 'ABK'; -- The new diagnosis should become primary
                ELSE
                    v_new_dx_type_code := 'ABF'; -- Secondary diagnosis
                END IF;
                
                -- Archive the diagnosis and its linking record
                UPDATE form_med_claim_dx
                SET archived = TRUE,
                    updated_on = CURRENT_TIMESTAMP,
                    updated_by = 1
                WHERE dx_id = v_old_dx_id
                AND id IN (
                    SELECT dx.id
                    FROM form_med_claim_dx dx
                    INNER JOIN sf_form_med_claim_info_to_med_claim_dx sf_dx
                        ON sf_dx.form_med_claim_dx_fk = dx.id
                        AND sf_dx.form_med_claim_info_fk = v_claim_info_id
                        AND sf_dx.delete IS NOT TRUE
                    WHERE dx.dx_id = v_old_dx_id
                    AND dx.deleted IS NOT TRUE
                );
                
                UPDATE sf_form_med_claim_info_to_med_claim_dx
                SET archive = TRUE
                WHERE form_med_claim_info_fk = v_claim_info_id
                AND form_med_claim_dx_fk IN (
                    SELECT dx.id
                    FROM form_med_claim_dx dx
                    WHERE dx.dx_id = v_old_dx_id
                    AND dx.archived IS TRUE
                );
            END IF;
        ELSE
            -- Default to secondary if not replacing a primary
            v_new_dx_type_code := 'ABF';
        END IF;
        
        -- If new diagnosis added, ensure it exists in med_claim_dx
        IF v_new_dx_id IS NOT NULL THEN
            -- Check if this diagnosis already exists for the claim
            SELECT EXISTS(
                SELECT 1
                FROM form_med_claim_dx dx
                INNER JOIN sf_form_med_claim_info_to_med_claim_dx sf_dx
                    ON sf_dx.form_med_claim_dx_fk = dx.id
                    AND sf_dx.form_med_claim_info_fk = v_claim_info_id
                    AND sf_dx.delete IS NOT TRUE
                    AND sf_dx.archive IS NOT TRUE
                WHERE dx.dx_id = v_new_dx_id
                AND dx.deleted IS NOT TRUE
                AND dx.archived IS NOT TRUE
            ) INTO v_dx_exists;
            
            IF NOT v_dx_exists THEN
                -- Get diagnosis code from patient diagnosis
                SELECT pd.dx_id  -- dx_id already contains the diagnosis code
                INTO v_dx_code
                FROM form_patient_diagnosis pd
                WHERE pd.id = v_new_dx_id
                AND pd.deleted IS NOT TRUE
                AND pd.archived IS NOT TRUE;
                
                IF v_dx_code IS NOT NULL THEN
                    -- Check if we still have a primary diagnosis
                    SELECT EXISTS(
                        SELECT 1
                        FROM form_med_claim_dx dx
                        INNER JOIN sf_form_med_claim_info_to_med_claim_dx sf_dx
                            ON sf_dx.form_med_claim_dx_fk = dx.id
                            AND sf_dx.form_med_claim_info_fk = v_claim_info_id
                            AND sf_dx.delete IS NOT TRUE
                            AND sf_dx.archive IS NOT TRUE
                        WHERE dx.diagnosis_type_code = 'ABK'
                        AND dx.deleted IS NOT TRUE
                        AND dx.archived IS NOT TRUE
                    ) INTO v_has_primary;
                    
                    -- If no primary exists and we didn't already decide to make this primary, 
                    -- make this the primary
                    IF NOT v_has_primary AND v_new_dx_type_code != 'ABK' THEN
                        v_new_dx_type_code := 'ABK';
                    END IF;
                    
                    -- Insert new diagnosis
                    INSERT INTO form_med_claim_dx (
                        patient_id,
                        dx_id,
                        diagnosis_code,
                        diagnosis_type_code,
                        created_on,
                        created_by
                    )
                    VALUES (
                        v_patient_id,
                        v_new_dx_id,
                        v_dx_code,
                        v_new_dx_type_code,
                        CURRENT_TIMESTAMP,
                        1
                    )
                    RETURNING id INTO v_dx_id;
                    
                    -- Link to claim info
                    INSERT INTO sf_form_med_claim_info_to_med_claim_dx (
                        form_med_claim_info_fk,
                        form_med_claim_dx_fk,
                        delete,
                        archive
                    )
                    VALUES (
                        v_claim_info_id,
                        v_dx_id,
                        FALSE,
                        FALSE
                    );
                END IF;
            END IF;
        END IF;
    END LOOP;
    
    -- After all changes, ensure we still have a primary diagnosis if we have any diagnoses
    -- This handles edge cases where multiple diagnoses might be changed at once
    IF NOT EXISTS (
        SELECT 1
        FROM form_med_claim_dx dx
        INNER JOIN sf_form_med_claim_info_to_med_claim_dx sf_dx
            ON sf_dx.form_med_claim_dx_fk = dx.id
            AND sf_dx.form_med_claim_info_fk = v_claim_info_id
            AND sf_dx.delete IS NOT TRUE
            AND sf_dx.archive IS NOT TRUE
        WHERE dx.diagnosis_type_code = 'ABK'
        AND dx.deleted IS NOT TRUE
        AND dx.archived IS NOT TRUE
    ) THEN
        -- Find the first non-archived diagnosis and make it primary
        UPDATE form_med_claim_dx
        SET diagnosis_type_code = 'ABK',
            updated_on = CURRENT_TIMESTAMP,
            updated_by = 1
        WHERE id = (
            SELECT dx.id
            FROM form_med_claim_dx dx
            INNER JOIN sf_form_med_claim_info_to_med_claim_dx sf_dx
                ON sf_dx.form_med_claim_dx_fk = dx.id
                AND sf_dx.form_med_claim_info_fk = v_claim_info_id
                AND sf_dx.delete IS NOT TRUE
                AND sf_dx.archive IS NOT TRUE
            WHERE dx.deleted IS NOT TRUE
            AND dx.archived IS NOT TRUE
            ORDER BY dx.id
            LIMIT 1
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- CREATE TRIGGER for diagnosis sync
DROP TRIGGER IF EXISTS tr_sync_service_line_diagnoses ON form_med_claim_sv;

CREATE TRIGGER tr_sync_service_line_diagnoses
    AFTER UPDATE OF dx_id_1, dx_id_2, dx_id_3, dx_id_4 ON form_med_claim_sv
    FOR EACH ROW
    EXECUTE FUNCTION sync_service_line_diagnoses();

-- 6. Trigger to handle new charge lines being added to claims
CREATE OR REPLACE FUNCTION add_charge_line_to_claim()
RETURNS TRIGGER AS $$
DECLARE
    v_claim_no TEXT;
    v_claim_type TEXT;
    v_claim_id INTEGER;
    v_service_line_id INTEGER;
    v_line_number INTEGER;
    v_claim_info_id INTEGER;
    v_total_billed NUMERIC;
    v_total_expected NUMERIC;
    v_charge_line_array charge_line_with_split[];
    v_service_lines mm_service_line_info[];
    v_service_line mm_service_line_info;
    v_diagnosis_codes mm_dx_info[];
    v_insurance_id INTEGER;
    v_payer_id INTEGER;
    v_professional_service_id INTEGER;
    v_drug_id_id INTEGER;
    v_dme_service_id INTEGER;
    v_sl_dates_id INTEGER;
    v_sl_ref_id INTEGER;
    v_sl_ref_pa_id INTEGER;
BEGIN
    -- Only process if billing_method_id is 'mm' (electronic medical)
    IF NEW.billing_method_id != 'mm' THEN
        RETURN NEW;
    END IF;
    
    -- Only process if invoice_no is set and not voided
    IF NEW.invoice_no IS NULL OR COALESCE(NEW.void, 'No') = 'Yes' THEN
        RETURN NEW;
    END IF;
    
    -- Check for electronic medical claim
    SELECT mc.id, mc.claim_no, mc.payer_id
    INTO v_claim_id, v_claim_no, v_payer_id
    FROM form_med_claim mc
    WHERE mc.invoice_no = NEW.invoice_no
    AND mc.deleted IS NOT TRUE
    AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    -- Exit if no claim found
    IF v_claim_id IS NULL THEN
        RETURN NEW;
    END IF;
    
    -- Get insurance_id from the charge line
    v_insurance_id := NEW.insurance_id;
    
    -- Build charge_line_with_split array for the new charge line using CTE
    WITH charge_line_data AS (
        SELECT 
            NEW.id::integer as id,
            NEW.calc_invoice_split_no::text as calc_invoice_split_no,
            NEW.site_id::integer as site_id,
            NEW.patient_id::integer as patient_id,
            NEW.insurance_id::integer as insurance_id,
            v_payer_id::integer as payer_id,
            NEW.inventory_id::integer as inventory_id,
            NEW.shared_contract_id::integer as shared_contract_id,
            NEW.is_dirty::text as is_dirty,
            NEW.charge_no::text as charge_no,
            NEW.parent_charge_no::text as parent_charge_no,
            NEW.master_charge_no::text as master_charge_no,
            NEW.compound_no::text as compound_no,
            NEW.ticket_no::text as ticket_no,
            NEW.ticket_item_no::text as ticket_item_no,
            NEW.rental_id::integer as rental_id,
            NEW.order_rx_id::integer as order_rx_id,
            NEW.is_primary_drug::text as is_primary_drug,
            NEW.is_primary_drug_ncpdp::text as is_primary_drug_ncpdp,
            NEW.rx_no::text as rx_no,
            NEW.inventory_type_filter::text[] as inventory_type_filter,
            NEW.inventory_type::text as inventory_type,
            NEW.revenue_code_id::text as revenue_code_id,
            NEW.hcpc_code::text as hcpc_code,
            NEW.ndc::text as ndc,
            NEW.formatted_ndc::text as formatted_ndc,
            NEW.gcn_seqno::text as gcn_seqno,
            NEW.charge_quantity::numeric as charge_quantity,
            NEW.charge_quantity_ea::numeric as charge_quantity_ea,
            NEW.hcpc_quantity::numeric as hcpc_quantity,
            NEW.hcpc_unit::text as hcpc_unit,
            NEW.metric_quantity::numeric as metric_quantity,
            NEW.charge_unit::text as charge_unit,
            NEW.bill_quantity::numeric as bill_quantity,
            NEW.metric_unit_each::numeric as metric_unit_each,
            NEW.billing_unit_id::text as billing_unit_id,
            NEW.billing_method_id::text as billing_method_id,
            NEW.pricing_source::text as pricing_source,
            NEW.billed::numeric as billed,
            NEW.calc_billed_ea::numeric as calc_billed_ea,
            NEW.expected::numeric as expected,
            NEW.calc_expected_ea::numeric as calc_expected_ea,
            NEW.list_price::numeric as list_price,
            NEW.calc_list_ea::numeric as calc_list_ea,
            NEW.total_cost::numeric as total_cost,
            NEW.gross_amount_due::numeric as gross_amount_due,
            NEW.incv_amt_sub::numeric as incv_amt_sub,
            NEW.copay::numeric as copay,
            NEW.calc_cost_ea::numeric as calc_cost_ea,
            NEW.total_adjusted::numeric as total_adjusted,
            NEW.total_balance_due::numeric as total_balance_due,
            NEW.dispense_fee::numeric as dispense_fee,
            NEW.pt_pd_amt_sub::numeric as pt_pd_amt_sub,
            NEW.encounter_id::integer as encounter_id,
            NEW.description::text as description,
            NEW.upc::text as upc,
            NEW.upin::text as upin,
            NEW.cost_basis::text as cost_basis,
            NEW.awp_price::numeric as awp_price,
            NEW.modifier_1::text as modifier_1,
            NEW.modifier_2::text as modifier_2,
            NEW.modifier_3::text as modifier_3,
            NEW.modifier_4::text as modifier_4,
            NEW.rental_type::text as rental_type,
            NEW.frequency_code::text as frequency_code,
            NEW.fill_number::integer as fill_number,
            NEW.paid::numeric as paid,
            NEW.date_of_service::date as date_of_service,
            NEW.date_of_service_end::date as date_of_service_end
    )
    SELECT array_agg(
        (
            id,
            calc_invoice_split_no,
            site_id,
            patient_id,
            insurance_id,
            payer_id,
            inventory_id,
            shared_contract_id,
            is_dirty,
            charge_no,
            parent_charge_no,
            master_charge_no,
            compound_no,
            ticket_no,
            ticket_item_no,
            rental_id,
            order_rx_id,
            is_primary_drug,
            is_primary_drug_ncpdp,
            rx_no,
            inventory_type_filter,
            inventory_type,
            revenue_code_id,
            hcpc_code,
            ndc,
            formatted_ndc,
            gcn_seqno,
            charge_quantity,
            charge_quantity_ea,
            hcpc_quantity,
            hcpc_unit,
            metric_quantity,
            charge_unit,
            bill_quantity,
            metric_unit_each,
            billing_unit_id,
            billing_method_id,
            pricing_source,
            billed,
            calc_billed_ea,
            expected,
            calc_expected_ea,
            list_price,
            calc_list_ea,
            total_cost,
            gross_amount_due,
            incv_amt_sub,
            copay,
            calc_cost_ea,
            total_adjusted,
            total_balance_due,
            dispense_fee,
            pt_pd_amt_sub,
            encounter_id,
            description,
            upc,
            upin,
            cost_basis,
            awp_price,
            modifier_1,
            modifier_2,
            modifier_3,
            modifier_4,
            rental_type,
            frequency_code,
            fill_number,
            paid,
            date_of_service,
            date_of_service_end
        )::charge_line_with_split
    ) INTO v_charge_line_array
    FROM charge_line_data;
    
    -- Build diagnosis codes using the same logic as build_mm_claim_dx_loop
    v_diagnosis_codes := build_mm_claim_dx_loop(v_charge_line_array);
    
    -- Build service lines using the helper function
    v_service_lines := build_mm_service_lines_loop(
        v_insurance_id,
        v_payer_id,
        v_charge_line_array,
        v_diagnosis_codes,
        NULL -- not a COB claim
    );
    
    -- Process each service line (should only be one in this case)
    FOREACH v_service_line IN ARRAY v_service_lines LOOP
        -- Check if service line already exists
        SELECT sl.id INTO v_service_line_id
        FROM form_med_claim_sl sl
        WHERE sl.claim_no = v_claim_no
        AND sl.charge_no = NEW.charge_no
        AND sl.deleted IS NOT TRUE
        LIMIT 1;
        
        IF v_service_line_id IS NULL THEN
            -- Get next line number
            SELECT COALESCE(MAX(sl.assigned_number::INTEGER), 0) + 1 INTO v_line_number
            FROM form_med_claim_sl sl
            WHERE sl.claim_no = v_claim_no
            AND sl.deleted IS NOT TRUE
            AND sl.archived IS NOT TRUE;
            
            -- Get the claim info ID to link service line properly
            SELECT mci.id INTO v_claim_info_id
            FROM form_med_claim mc
            INNER JOIN sf_form_med_claim_to_med_claim_info sf_ci 
                ON sf_ci.form_med_claim_fk = mc.id
                AND sf_ci.delete IS NOT TRUE
                AND sf_ci.archive IS NOT TRUE
            INNER JOIN form_med_claim_info mci 
                ON mci.id = sf_ci.form_med_claim_info_fk
                AND mci.deleted IS NOT TRUE
                AND mci.archived IS NOT TRUE
            WHERE mc.id = v_claim_id
            LIMIT 1;
            
            -- Create main service line
            INSERT INTO form_med_claim_sl (
                claim_no,
                charge_no,
                patient_id,
                site_id,
                payer_id,
                inventory_id,
                measurement_unit,
                service_unit_count,
                dx_id_1,
                modifier_1,
                line_item_charge_amount,
                assigned_number,
                provider_control_number,
                service_date,
                service_date_end,
                created_on,
                created_by
            )
            VALUES (
                v_claim_no,
                v_service_line.charge_no,
                v_service_line.patient_id,
                v_service_line.site_id,
                v_service_line.payer_id,
                v_service_line.inventory_id,
                v_service_line.measurement_unit,
                v_service_line.service_unit_count,
                v_service_line.dx_id_1,
                v_service_line.modifier_1,
                v_service_line.line_item_charge_amount,
                v_line_number,
                v_service_line.provider_control_number,
                v_service_line.service_date::date,
                v_service_line.service_date_end::date,
                NEW.created_on,
                NEW.created_by
            )
            RETURNING id INTO v_service_line_id;
            
            -- Create linking record to claim info (not directly to claim)
            INSERT INTO sf_form_med_claim_info_to_med_claim_sl (
                form_med_claim_info_fk,
                form_med_claim_sl_fk,
                delete,
                archive
            )
            VALUES (
                v_claim_info_id,
                v_service_line_id,
                FALSE,
                FALSE
            );
            
            -- Create professional service (form_med_claim_sv)
            IF v_service_line.professional_service IS NOT NULL AND array_length(v_service_line.professional_service, 1) > 0 THEN
                INSERT INTO form_med_claim_sv (
                    patient_id,
                    type,
                    charge_no,
                    inventory_id,
                    description,
                    line_item_charge_amount,
                    measurement_unit,
                    service_unit_count,
                    place_of_service_code,
                    emergency_indicator,
                    epsdt_indicator,
                    copay_status_code,
                    dx_filter,
                    dx_id_1,
                    dx_id_2,
                    dx_id_3,
                    dx_id_4,
                    procedure_identifier,
                    procedure_code,
                    modifier_1,
                    modifier_2,
                    modifier_3,
                    modifier_4,
                    created_on,
                    created_by
                )
                VALUES (
                    (v_service_line.professional_service[1]).patient_id,
                    (v_service_line.professional_service[1]).type,
                    (v_service_line.professional_service[1]).charge_no,
                    (v_service_line.professional_service[1]).inventory_id,
                    (v_service_line.professional_service[1]).description,
                    (v_service_line.professional_service[1]).line_item_charge_amount,
                    (v_service_line.professional_service[1]).measurement_unit,
                    (v_service_line.professional_service[1]).service_unit_count,
                    (v_service_line.professional_service[1]).place_of_service_code,
                    (v_service_line.professional_service[1]).emergency_indicator,
                    (v_service_line.professional_service[1]).epsdt_indicator,
                    (v_service_line.professional_service[1]).copay_status_code,
                    (v_service_line.professional_service[1]).dx_filter,
                    (v_service_line.professional_service[1]).dx_id_1,
                    (v_service_line.professional_service[1]).dx_id_2,
                    (v_service_line.professional_service[1]).dx_id_3,
                    (v_service_line.professional_service[1]).dx_id_4,
                    (v_service_line.professional_service[1]).procedure_identifier,
                    (v_service_line.professional_service[1]).procedure_code,
                    (v_service_line.professional_service[1]).modifier_1,
                    (v_service_line.professional_service[1]).modifier_2,
                    (v_service_line.professional_service[1]).modifier_3,
                    (v_service_line.professional_service[1]).modifier_4,
                    NEW.created_on,
                    NEW.created_by
                )
                RETURNING id INTO v_professional_service_id;
                
                -- Link professional service to service line
                INSERT INTO sf_form_med_claim_sl_to_med_claim_sv (
                    form_med_claim_sl_fk,
                    form_med_claim_sv_fk,
                    delete,
                    archive
                )
                VALUES (
                    v_service_line_id,
                    v_professional_service_id,
                    FALSE,
                    FALSE
                );
            END IF;
            
            -- Create drug identification subform if applicable
            IF v_service_line.drug_identification IS NOT NULL AND array_length(v_service_line.drug_identification, 1) > 0 THEN
                INSERT INTO form_med_claim_sl_di (
                    charge_no,
                    inventory_id,
                    service_id_qualifier,
                    national_drug_code,
                    national_drug_unit_count,
                    measurement_unit_code,
                    link_sequence_number,
                    pharmacy_prescription_number,
                    created_on,
                    created_by
                )
                VALUES (
                    (v_service_line.drug_identification[1]).charge_no,
                    (v_service_line.drug_identification[1]).inventory_id,
                    (v_service_line.drug_identification[1]).service_id_qualifier,
                    (v_service_line.drug_identification[1]).national_drug_code,
                    (v_service_line.drug_identification[1]).national_drug_unit_count,
                    (v_service_line.drug_identification[1]).measurement_unit_code,
                    (v_service_line.drug_identification[1]).link_sequence_number,
                    (v_service_line.drug_identification[1]).pharmacy_prescription_number,
                    NEW.created_on,
                    NEW.created_by
                )
                RETURNING id INTO v_drug_id_id;
                
                -- Link drug identification to service line
                INSERT INTO sf_form_med_claim_sl_to_med_claim_sl_di (
                    form_med_claim_sl_fk,
                    form_med_claim_sl_di_fk,
                    delete,
                    archive
                )
                VALUES (
                    v_service_line_id,
                    v_drug_id_id,
                    FALSE,
                    FALSE
                );
            END IF;
            
            -- Create DME service subform if applicable
            IF v_service_line.durable_medical_equipment_service IS NOT NULL AND array_length(v_service_line.durable_medical_equipment_service, 1) > 0 THEN
                INSERT INTO form_med_claim_dme (
                    charge_no,
                    inventory_id,
                    days,
                    frequency_code,
                    rental_price,
                    purchase_price,
                    created_on,
                    created_by
                )
                VALUES (
                    (v_service_line.durable_medical_equipment_service[1]).charge_no,
                    (v_service_line.durable_medical_equipment_service[1]).inventory_id,
                    (v_service_line.durable_medical_equipment_service[1]).days,
                    (v_service_line.durable_medical_equipment_service[1]).frequency_code,
                    (v_service_line.durable_medical_equipment_service[1]).rental_price,
                    (v_service_line.durable_medical_equipment_service[1]).purchase_price,
                    NEW.created_on,
                    NEW.created_by
                )
                RETURNING id INTO v_dme_service_id;
                
                -- Link DME service to service line
                INSERT INTO sf_form_med_claim_sl_to_med_claim_dme (
                    form_med_claim_sl_fk,
                    form_med_claim_dme_fk,
                    delete,
                    archive
                )
                VALUES (
                    v_service_line_id,
                    v_dme_service_id,
                    FALSE,
                    FALSE
                );
                
                -- Also handle DME certification if present
                IF v_service_line.durable_medical_equipment_certification IS NOT NULL AND 
                   array_length(v_service_line.durable_medical_equipment_certification, 1) > 0 THEN
                    DECLARE
                        v_dme_cert_id INTEGER;
                    BEGIN
                        -- Insert DME certification record
                        INSERT INTO form_med_claim_dme_cert (
                            send_cert,
                            certification_type_code,
                            durable_medical_equipment_duration_in_months,
                            created_on,
                            created_by
                        )
                        VALUES (
                            (v_service_line.durable_medical_equipment_certification[1]).send_cert,
                            (v_service_line.durable_medical_equipment_certification[1]).certification_type_code,
                            (v_service_line.durable_medical_equipment_certification[1]).durable_medical_equipment_duration_in_months,
                            NEW.created_on,
                            NEW.created_by
                        )
                        RETURNING id INTO v_dme_cert_id;
                        
                        -- Link DME certification to service line
                        INSERT INTO sf_form_med_claim_sl_to_med_claim_dme_cert (
                            form_med_claim_sl_fk,
                            form_med_claim_dme_cert_fk,
                            delete,
                            archive
                        )
                        VALUES (
                            v_service_line_id,
                            v_dme_cert_id,
                            FALSE,
                            FALSE
                        );
                    END;
                END IF;
                
                -- Handle DME CMN (Certificate of Medical Necessity) if present
                IF v_service_line.durable_medical_equipment_certificate_of_medical_necessity IS NOT NULL AND 
                   array_length(v_service_line.durable_medical_equipment_certificate_of_medical_necessity, 1) > 0 THEN
                    DECLARE
                        v_dme_cmn_id INTEGER;
                    BEGIN
                        -- Insert DME CMN record
                        INSERT INTO form_med_claim_dme_cmn (
                            attachment_transmission_code,
                            created_on,
                            created_by
                        )
                        VALUES (
                            (v_service_line.durable_medical_equipment_certificate_of_medical_necessity[1]).attachment_transmission_code,
                            NEW.created_on,
                            NEW.created_by
                        )
                        RETURNING id INTO v_dme_cmn_id;
                        
                        -- Link DME CMN to service line
                        INSERT INTO sf_form_med_claim_sl_to_med_claim_dme_cmn (
                            form_med_claim_sl_fk,
                            form_med_claim_dme_cmn_fk,
                            delete,
                            archive
                        )
                        VALUES (
                            v_service_line_id,
                            v_dme_cmn_id,
                            FALSE,
                            FALSE
                        );
                    END;
                END IF;
            END IF;
            
            -- Create service line dates subform if present
            IF v_service_line.service_line_date_information IS NOT NULL AND 
               array_length(v_service_line.service_line_date_information, 1) > 0 THEN
                INSERT INTO form_med_claim_sl_dt (
                    prescription_date,
                    shipped_date,
                    created_on,
                    created_by
                )
                VALUES (
                    (v_service_line.service_line_date_information[1]).prescription_date,
                    (v_service_line.service_line_date_information[1]).shipped_date,
                    NEW.created_on,
                    NEW.created_by
                )
                RETURNING id INTO v_sl_dates_id;
                
                -- Link dates to service line
                INSERT INTO sf_form_med_claim_sl_to_med_claim_sl_dt (
                    form_med_claim_sl_fk,
                    form_med_claim_sl_dt_fk,
                    delete,
                    archive
                )
                VALUES (
                    v_service_line_id,
                    v_sl_dates_id,
                    FALSE,
                    FALSE
                );
            END IF;
            
            -- Handle service line reference information (PA, etc.)
            IF v_service_line.service_line_reference_information IS NOT NULL AND 
               array_length(v_service_line.service_line_reference_information, 1) > 0 THEN
                DECLARE
                    v_ref_info mm_service_line_reference_info;
                BEGIN
                    FOREACH v_ref_info IN ARRAY v_service_line.service_line_reference_information LOOP
                        -- Create reference record
                        INSERT INTO form_med_claim_sl_ref (
                            patient_id,
                            created_on,
                            created_by
                        )
                        VALUES (
                            v_service_line.patient_id,
                            NEW.created_on,
                            NEW.created_by
                        )
                        RETURNING id INTO v_sl_ref_id;
                        
                        -- Link reference to service line
                        INSERT INTO sf_form_med_claim_sl_to_med_claim_sl_ref (
                            form_med_claim_sl_fk,
                            form_med_claim_sl_ref_fk,
                            delete,
                            archive
                        )
                        VALUES (
                            v_service_line_id,
                            v_sl_ref_id,
                            FALSE,
                            FALSE
                        );
                        
                        -- Create PA reference if present
                        IF v_ref_info.prior_authorization IS NOT NULL AND 
                           array_length(v_ref_info.prior_authorization, 1) > 0 THEN
                            INSERT INTO form_med_claim_sl_ref_pa (
                                patient_id,
                                insurance_id,
                                pa_id,
                                prior_authorization_or_referral_number,
                                created_on,
                                created_by
                            )
                            VALUES (
                                (v_ref_info.prior_authorization[1]).patient_id,
                                (v_ref_info.prior_authorization[1]).insurance_id,
                                (v_ref_info.prior_authorization[1]).pa_id,
                                (v_ref_info.prior_authorization[1]).prior_authorization_or_referral_number,
                                NEW.created_on,
                                NEW.created_by
                            )
                            RETURNING id INTO v_sl_ref_pa_id;
                            
                            -- Link PA to reference
                            INSERT INTO sf_form_med_claim_sl_ref_to_med_claim_sl_ref_pa (
                                form_med_claim_sl_ref_fk,
                                form_med_claim_sl_ref_pa_fk,
                                delete,
                                archive
                            )
                            VALUES (
                                v_sl_ref_id,
                                v_sl_ref_pa_id,
                                FALSE,
                                FALSE
                            );
                        END IF;
                    END LOOP;
                END;
            END IF;
        END IF;
    END LOOP;
    
    -- Update claim totals
    -- First get the claim info ID
    SELECT mci.id INTO v_claim_info_id
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_info sf_ci 
        ON sf_ci.form_med_claim_fk = mc.id
        AND sf_ci.delete IS NOT TRUE
        AND sf_ci.archive IS NOT TRUE
    INNER JOIN form_med_claim_info mci 
        ON mci.id = sf_ci.form_med_claim_info_fk
        AND mci.deleted IS NOT TRUE
        AND mci.archived IS NOT TRUE
    WHERE mc.id = v_claim_id
    LIMIT 1;
    
    -- Calculate new totals from all charge lines
    SELECT 
        SUM(lcl.billed),
        SUM(lcl.expected)
    INTO v_total_billed, v_total_expected
    FROM form_ledger_charge_line lcl
    WHERE lcl.invoice_no = NEW.invoice_no
    AND lcl.deleted IS NOT TRUE
    AND lcl.archived IS NOT TRUE
    AND COALESCE(lcl.void, 'No') <> 'Yes'
    AND COALESCE(lcl.zeroed, 'No') <> 'Yes';
    
    -- Update claim info with new totals
    UPDATE form_med_claim_info
    SET claim_charge_amount = v_total_billed,
        updated_on = NEW.updated_on,
        updated_by = NEW.updated_by
    WHERE id = v_claim_info_id;
    
    -- Update claim header
    UPDATE form_med_claim
    SET billed = COALESCE(v_total_billed, 0),
        expected = COALESCE(v_total_expected, 0),
        updated_on = NEW.updated_on,
        updated_by = NEW.updated_by
    WHERE id = v_claim_id;
    
    -- Update form_med_claim_contract if it exists
    UPDATE form_med_claim_contract mcc
    SET contract_amount = COALESCE(v_total_billed, 0),
        updated_on = NEW.updated_on,
        updated_by = NEW.updated_by
    WHERE mcc.deleted IS NOT TRUE
    AND mcc.archived IS NOT TRUE
    AND mcc.contract_type_code IS NOT NULL
    AND EXISTS (
        SELECT 1 
        FROM form_med_claim mc
        INNER JOIN sf_form_med_claim_to_med_claim_info sf_ci
            ON sf_ci.form_med_claim_fk = mc.id
            AND sf_ci.delete IS NOT TRUE
            AND sf_ci.archive IS NOT TRUE
        INNER JOIN form_med_claim_info mci
            ON mci.id = sf_ci.form_med_claim_info_fk
            AND mci.deleted IS NOT TRUE
            AND mci.archived IS NOT TRUE
        INNER JOIN sf_form_med_claim_info_to_med_claim_info_other sf_io
            ON sf_io.form_med_claim_info_fk = mci.id
            AND sf_io.delete IS NOT TRUE
            AND sf_io.archive IS NOT TRUE
        INNER JOIN form_med_claim_info_other mcio
            ON mcio.id = sf_io.form_med_claim_info_other_fk
            AND mcio.deleted IS NOT TRUE
            AND mcio.archived IS NOT TRUE
        INNER JOIN sf_form_med_claim_info_other_to_med_claim_contract sf_cc
            ON sf_cc.form_med_claim_info_other_fk = mcio.id
            AND sf_cc.form_med_claim_contract_fk = mcc.id
            AND sf_cc.delete IS NOT TRUE
            AND sf_cc.archive IS NOT TRUE
        WHERE mc.id = v_claim_id
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. Trigger to sync various fields from charge lines to service lines
CREATE OR REPLACE FUNCTION sync_charge_line_fields_to_claim()
RETURNS TRIGGER AS $$
DECLARE
    v_claim_no TEXT;
    v_claim_id INTEGER;
    v_payer_id INTEGER;
    v_insurance_id INTEGER;
    v_charge_line_array charge_line_with_split[];
    v_service_lines mm_service_line_info[];
    v_service_line mm_service_line_info;
    v_total_billed NUMERIC;
    v_total_expected NUMERIC;
    v_claim_info_id INTEGER;
    v_professional_service_id INTEGER;
    v_drug_id_id INTEGER;
    v_contract_id INTEGER;
    v_days INTEGER;
BEGIN
    -- Only process if billing_method_id is 'mm' (electronic medical)
    IF NEW.billing_method_id != 'mm' THEN
        RETURN NEW;
    END IF;

        -- Only process if invoice_no is set and not voided
    IF NEW.invoice_no IS NULL OR COALESCE(NEW.void, 'No') = 'Yes' OR COALESCE(NEW.zeroed, 'No') = 'Yes' THEN
        RETURN NEW;
    END IF;

    -- Only process if relevant fields changed
    IF TG_OP = 'UPDATE' AND 
       OLD.hcpc_code IS NOT DISTINCT FROM NEW.hcpc_code AND
       OLD.bill_quantity IS NOT DISTINCT FROM NEW.bill_quantity AND
       OLD.billed IS NOT DISTINCT FROM NEW.billed AND
       OLD.ndc IS NOT DISTINCT FROM NEW.ndc AND
       OLD.frequency_code IS NOT DISTINCT FROM NEW.frequency_code AND
       OLD.modifier_1 IS NOT DISTINCT FROM NEW.modifier_1 AND
       OLD.modifier_2 IS NOT DISTINCT FROM NEW.modifier_2 AND
       OLD.modifier_3 IS NOT DISTINCT FROM NEW.modifier_3 AND
       OLD.modifier_4 IS NOT DISTINCT FROM NEW.modifier_4 AND
       OLD.upin IS NOT DISTINCT FROM NEW.upin AND
       OLD.charge_quantity IS NOT DISTINCT FROM NEW.charge_quantity AND
       OLD.metric_quantity IS NOT DISTINCT FROM NEW.metric_quantity AND
       OLD.description IS NOT DISTINCT FROM NEW.description AND
       OLD.inventory_id IS NOT DISTINCT FROM NEW.inventory_id AND
       OLD.expected IS NOT DISTINCT FROM NEW.expected THEN
        RETURN NEW;
    END IF;
    
    -- Get claim information for electronic medical claim
    SELECT mc.claim_no, mc.id, mc.payer_id
    INTO v_claim_no, v_claim_id, v_payer_id
    FROM form_med_claim mc
    WHERE mc.invoice_no = NEW.invoice_no
    AND mc.deleted IS NOT TRUE
    AND mc.archived IS NOT TRUE
    LIMIT 1;
    
    IF v_claim_id IS NULL THEN
        RETURN NEW;
    END IF;
    
    -- Get insurance_id
    v_insurance_id := NEW.insurance_id;
    
    -- Build charge_line_with_split array for the updated charge line
    WITH charge_line_data AS (
        SELECT 
            NEW.id::integer as id,
            NEW.calc_invoice_split_no::text as calc_invoice_split_no,
            NEW.site_id::integer as site_id,
            NEW.patient_id::integer as patient_id,
            NEW.insurance_id::integer as insurance_id,
            v_payer_id::integer as payer_id,
            NEW.inventory_id::integer as inventory_id,
            NEW.shared_contract_id::integer as shared_contract_id,
            NEW.is_dirty::text as is_dirty,
            NEW.charge_no::text as charge_no,
            NEW.parent_charge_no::text as parent_charge_no,
            NEW.master_charge_no::text as master_charge_no,
            NEW.compound_no::text as compound_no,
            NEW.ticket_no::text as ticket_no,
            NEW.ticket_item_no::text as ticket_item_no,
            NEW.rental_id::integer as rental_id,
            NEW.order_rx_id::integer as order_rx_id,
            NEW.is_primary_drug::text as is_primary_drug,
            NEW.is_primary_drug_ncpdp::text as is_primary_drug_ncpdp,
            NEW.rx_no::text as rx_no,
            NEW.inventory_type_filter::text[] as inventory_type_filter,
            NEW.inventory_type::text as inventory_type,
            NEW.revenue_code_id::text as revenue_code_id,
            NEW.hcpc_code::text as hcpc_code,
            NEW.ndc::text as ndc,
            NEW.formatted_ndc::text as formatted_ndc,
            NEW.gcn_seqno::text as gcn_seqno,
            NEW.charge_quantity::numeric as charge_quantity,
            NEW.charge_quantity_ea::numeric as charge_quantity_ea,
            NEW.hcpc_quantity::numeric as hcpc_quantity,
            NEW.hcpc_unit::text as hcpc_unit,
            NEW.metric_quantity::numeric as metric_quantity,
            NEW.charge_unit::text as charge_unit,
            NEW.bill_quantity::numeric as bill_quantity,
            NEW.metric_unit_each::numeric as metric_unit_each,
            NEW.billing_unit_id::text as billing_unit_id,
            NEW.billing_method_id::text as billing_method_id,
            NEW.pricing_source::text as pricing_source,
            NEW.billed::numeric as billed,
            NEW.calc_billed_ea::numeric as calc_billed_ea,
            NEW.expected::numeric as expected,
            NEW.calc_expected_ea::numeric as calc_expected_ea,
            NEW.list_price::numeric as list_price,
            NEW.calc_list_ea::numeric as calc_list_ea,
            NEW.total_cost::numeric as total_cost,
            NEW.gross_amount_due::numeric as gross_amount_due,
            NEW.incv_amt_sub::numeric as incv_amt_sub,
            NEW.copay::numeric as copay,
            NEW.calc_cost_ea::numeric as calc_cost_ea,
            NEW.total_adjusted::numeric as total_adjusted,
            NEW.total_balance_due::numeric as total_balance_due,
            NEW.dispense_fee::numeric as dispense_fee,
            NEW.pt_pd_amt_sub::numeric as pt_pd_amt_sub,
            NEW.encounter_id::integer as encounter_id,
            NEW.description::text as description,
            NEW.upc::text as upc,
            NEW.upin::text as upin,
            NEW.cost_basis::text as cost_basis,
            NEW.awp_price::numeric as awp_price,
            NEW.modifier_1::text as modifier_1,
            NEW.modifier_2::text as modifier_2,
            NEW.modifier_3::text as modifier_3,
            NEW.modifier_4::text as modifier_4,
            NEW.rental_type::text as rental_type,
            NEW.frequency_code::text as frequency_code,
            NEW.fill_number::integer as fill_number,
            NEW.paid::numeric as paid,
            NEW.date_of_service::date as date_of_service,
            NEW.date_of_service_end::date as date_of_service_end
    )
    SELECT array_agg(
        (
            id,
            calc_invoice_split_no,
            site_id,
            patient_id,
            insurance_id,
            payer_id,
            inventory_id,
            shared_contract_id,
            is_dirty,
            charge_no,
            parent_charge_no,
            master_charge_no,
            compound_no,
            ticket_no,
            ticket_item_no,
            rental_id,
            order_rx_id,
            is_primary_drug,
            is_primary_drug_ncpdp,
            rx_no,
            inventory_type_filter,
            inventory_type,
            revenue_code_id,
            hcpc_code,
            ndc,
            formatted_ndc,
            gcn_seqno,
            charge_quantity,
            charge_quantity_ea,
            hcpc_quantity,
            hcpc_unit,
            metric_quantity,
            charge_unit,
            bill_quantity,
            metric_unit_each,
            billing_unit_id,
            billing_method_id,
            pricing_source,
            billed,
            calc_billed_ea,
            expected,
            calc_expected_ea,
            list_price,
            calc_list_ea,
            total_cost,
            gross_amount_due,
            incv_amt_sub,
            copay,
            calc_cost_ea,
            total_adjusted,
            total_balance_due,
            dispense_fee,
            pt_pd_amt_sub,
            encounter_id,
            description,
            upc,
            upin,
            cost_basis,
            awp_price,
            modifier_1,
            modifier_2,
            modifier_3,
            modifier_4,
            rental_type,
            frequency_code,
            fill_number,
            paid,
            date_of_service,
            date_of_service_end
        )::charge_line_with_split
    ) INTO v_charge_line_array
    FROM charge_line_data;
    
    -- Use electronic medical claim helper functions
    DECLARE
        v_diagnosis_codes mm_dx_info[];
    BEGIN
        -- Get existing diagnosis codes from the claim
        v_diagnosis_codes := build_mm_claim_dx_loop(v_charge_line_array, v_claim_no);
        
        -- Build service lines using the helper function
        v_service_lines := build_mm_service_lines_loop(
            v_insurance_id,
            v_payer_id,
            v_charge_line_array,
            v_diagnosis_codes,
            NULL -- not a COB claim
        );
        
        -- Update the service line with new values
        IF array_length(v_service_lines, 1) > 0 THEN
            v_service_line := v_service_lines[1];
            
            -- Update main service line fields including inventory_id
            UPDATE form_med_claim_sl
            SET 
                inventory_id = NEW.inventory_id,
                measurement_unit = v_service_line.measurement_unit,
                service_unit_count = v_service_line.service_unit_count,
                line_item_charge_amount = v_service_line.line_item_charge_amount,
                service_date = v_service_line.service_date::date,
                service_date_end = v_service_line.service_date_end::date,
                modifier_1 = v_service_line.modifier_1,
                updated_on = NEW.updated_on,
                updated_by = NEW.updated_by
            WHERE claim_no = v_claim_no
            AND charge_no = NEW.charge_no
            AND deleted IS NOT TRUE
            AND archived IS NOT TRUE;
            
            -- Update professional service subform if it exists
            IF v_service_line.professional_service IS NOT NULL AND 
               array_length(v_service_line.professional_service, 1) > 0 THEN
                UPDATE form_med_claim_sv sv
                SET 
                    inventory_id = NEW.inventory_id,
                    line_item_charge_amount = (v_service_line.professional_service[1]).line_item_charge_amount,
                    measurement_unit = (v_service_line.professional_service[1]).measurement_unit,
                    service_unit_count = (v_service_line.professional_service[1]).service_unit_count,
                    procedure_identifier = (v_service_line.professional_service[1]).procedure_identifier,
                    procedure_code = (v_service_line.professional_service[1]).procedure_code,
                    modifier_1 = (v_service_line.professional_service[1]).modifier_1,
                    modifier_2 = (v_service_line.professional_service[1]).modifier_2,
                    modifier_3 = (v_service_line.professional_service[1]).modifier_3,
                    modifier_4 = (v_service_line.professional_service[1]).modifier_4,
                    description = CASE 
                        WHEN OLD.description IS DISTINCT FROM NEW.description 
                        THEN (v_service_line.professional_service[1]).description
                        ELSE sv.description
                    END,
                    updated_on = NEW.updated_on,
                    updated_by = NEW.updated_by
                WHERE sv.charge_no = NEW.charge_no
                AND sv.deleted IS NOT TRUE
                AND sv.archived IS NOT TRUE
                AND EXISTS (
                    SELECT 1 
                    FROM form_med_claim_sl sl
                    INNER JOIN sf_form_med_claim_sl_to_med_claim_sv sf_sv
                        ON sf_sv.form_med_claim_sl_fk = sl.id
                        AND sf_sv.form_med_claim_sv_fk = sv.id
                        AND sf_sv.delete IS NOT TRUE
                        AND sf_sv.archive IS NOT TRUE
                    WHERE sl.claim_no = v_claim_no
                    AND sl.charge_no = NEW.charge_no
                    AND sl.deleted IS NOT TRUE
                    AND sl.archived IS NOT TRUE
                );
            END IF;
            
            -- Update drug identification subform if NDC or metric_quantity changed
            IF (OLD.ndc IS DISTINCT FROM NEW.ndc OR 
                OLD.metric_quantity IS DISTINCT FROM NEW.metric_quantity OR
                OLD.billing_unit_id IS DISTINCT FROM NEW.billing_unit_id OR
                OLD.inventory_id IS DISTINCT FROM NEW.inventory_id) AND
               v_service_line.drug_identification IS NOT NULL AND 
               array_length(v_service_line.drug_identification, 1) > 0 THEN
                UPDATE form_med_claim_sl_di di
                SET 
                    inventory_id = NEW.inventory_id,
                    national_drug_code = (v_service_line.drug_identification[1]).national_drug_code,
                    national_drug_unit_count = (v_service_line.drug_identification[1]).national_drug_unit_count,
                    measurement_unit_code = (v_service_line.drug_identification[1]).measurement_unit_code,
                    updated_on = NEW.updated_on,
                    updated_by = NEW.updated_by
                WHERE di.charge_no = NEW.charge_no
                AND di.deleted IS NOT TRUE
                AND di.archived IS NOT TRUE
                AND EXISTS (
                    SELECT 1 
                    FROM form_med_claim_sl sl
                    INNER JOIN sf_form_med_claim_sl_to_med_claim_sl_di sf_di
                        ON sf_di.form_med_claim_sl_fk = sl.id
                        AND sf_di.form_med_claim_sl_di_fk = di.id
                        AND sf_di.delete IS NOT TRUE
                        AND sf_di.archive IS NOT TRUE
                    WHERE sl.claim_no = v_claim_no
                    AND sl.charge_no = NEW.charge_no
                    AND sl.deleted IS NOT TRUE
                    AND sl.archived IS NOT TRUE
                );
            END IF;
            
            -- Update DME service if frequency_code or billed changed
            IF (OLD.frequency_code IS DISTINCT FROM NEW.frequency_code OR 
                OLD.billed IS DISTINCT FROM NEW.billed OR
                OLD.inventory_id IS DISTINCT FROM NEW.inventory_id) AND
               v_service_line.durable_medical_equipment_service IS NOT NULL AND 
               array_length(v_service_line.durable_medical_equipment_service, 1) > 0 THEN
               
                -- Calculate days if dates changed
                IF NEW.date_of_service IS NOT NULL AND NEW.date_of_service_end IS NOT NULL THEN
                    v_days := (NEW.date_of_service_end - NEW.date_of_service)::INTEGER + 1;
                ELSE
                    v_days := NULL::INTEGER;
                END IF;
                
                UPDATE form_med_claim_dme dme
                SET 
                    inventory_id = NEW.inventory_id,
                    frequency_code = (v_service_line.durable_medical_equipment_service[1]).frequency_code,
                    rental_price = CASE 
                        WHEN OLD.billed::NUMERIC IS DISTINCT FROM NEW.billed::NUMERIC AND dme.frequency_code IS NOT NULL
                        THEN COALESCE(NEW.billed, 0::NUMERIC)::NUMERIC
                        ELSE dme.rental_price::NUMERIC
                    END,
                    days = COALESCE(v_days, dme.days),
                    updated_on = NEW.updated_on,
                    updated_by = NEW.updated_by
                WHERE dme.charge_no = NEW.charge_no
                AND dme.deleted IS NOT TRUE
                AND dme.archived IS NOT TRUE
                AND EXISTS (
                    SELECT 1 
                    FROM form_med_claim_sl sl
                    INNER JOIN sf_form_med_claim_sl_to_med_claim_dme sf_dme
                        ON sf_dme.form_med_claim_sl_fk = sl.id
                        AND sf_dme.form_med_claim_dme_fk = dme.id
                        AND sf_dme.delete IS NOT TRUE
                        AND sf_dme.archive IS NOT TRUE
                    WHERE sl.claim_no = v_claim_no
                    AND sl.charge_no = NEW.charge_no
                    AND sl.deleted IS NOT TRUE
                    AND sl.archived IS NOT TRUE
                );
            END IF;
            
            -- Update reference information if UPIN changed (based on payer settings)
            -- Check if payer setting mm_send_upin_supplies is 'Yes' and this is a Supply
            IF OLD.upin IS DISTINCT FROM NEW.upin AND NEW.inventory_type IN ('Supply', 'Supplies') THEN
                DECLARE
                    v_send_upin_supplies TEXT;
                BEGIN
                    -- Get payer setting for UPIN on supplies
                    SELECT COALESCE(p.mm_send_upin_supplies, 'No')
                    INTO v_send_upin_supplies
                    FROM form_payer p
                    WHERE p.id = v_payer_id
                    AND p.deleted IS NOT TRUE
                    AND p.archived IS NOT TRUE;
                    
                    -- If setting is enabled and we have UPIN, update procedure identifier and code
                    IF v_send_upin_supplies = 'Yes' AND NEW.upin IS NOT NULL THEN
                        UPDATE form_med_claim_sv sv
                        SET 
                            procedure_identifier = 'ER',
                            procedure_code = NEW.upin,
                            updated_on = NEW.updated_on,
                            updated_by = NEW.updated_by
                        WHERE sv.charge_no = NEW.charge_no
                        AND sv.deleted IS NOT TRUE
                        AND sv.archived IS NOT TRUE
                        AND EXISTS (
                            SELECT 1 
                            FROM form_med_claim_sl sl
                            INNER JOIN sf_form_med_claim_sl_to_med_claim_sv sf_sv
                                ON sf_sv.form_med_claim_sl_fk = sl.id
                                AND sf_sv.form_med_claim_sv_fk = sv.id
                                AND sf_sv.delete IS NOT TRUE
                                AND sf_sv.archive IS NOT TRUE
                            WHERE sl.claim_no = v_claim_no
                            AND sl.charge_no = NEW.charge_no
                            AND sl.deleted IS NOT TRUE
                            AND sl.archived IS NOT TRUE
                        );
                    END IF;
                END;
            END IF;
        END IF;
    END;
    
    -- Update claim totals
    SELECT 
        SUM(COALESCE(lcl.billed, 0::NUMERIC)::NUMERIC),
        SUM(COALESCE(lcl.expected, 0::NUMERIC)::NUMERIC)
    INTO v_total_billed, v_total_expected
    FROM form_ledger_charge_line lcl
    WHERE lcl.invoice_no = NEW.invoice_no
    AND lcl.deleted IS NOT TRUE
    AND lcl.archived IS NOT TRUE
    AND COALESCE(lcl.void, 'No') <> 'Yes'
    AND COALESCE(lcl.zeroed, 'No') <> 'Yes';
    
    -- Update claim header
    UPDATE form_med_claim
    SET billed = COALESCE(v_total_billed, 0::NUMERIC)::NUMERIC,
        expected = COALESCE(v_total_expected, 0::NUMERIC)::NUMERIC,
        updated_on = NEW.updated_on,
        updated_by = NEW.updated_by
    WHERE id = v_claim_id;
    
    -- Get claim info ID and update total
    SELECT mci.id INTO v_claim_info_id
    FROM form_med_claim mc
    INNER JOIN sf_form_med_claim_to_med_claim_info sf_ci 
        ON sf_ci.form_med_claim_fk = mc.id
        AND sf_ci.delete IS NOT TRUE
        AND sf_ci.archive IS NOT TRUE
    INNER JOIN form_med_claim_info mci 
        ON mci.id = sf_ci.form_med_claim_info_fk
        AND mci.deleted IS NOT TRUE
        AND mci.archived IS NOT TRUE
    WHERE mc.id = v_claim_id
    LIMIT 1;
    
    IF v_claim_info_id IS NOT NULL THEN
        UPDATE form_med_claim_info
        SET claim_charge_amount = v_total_billed,
            updated_on = NEW.updated_on,
            updated_by = NEW.updated_by
        WHERE id = v_claim_info_id;
    END IF;
    
    -- Update form_med_claim_contract if it exists
    UPDATE form_med_claim_contract mcc
    SET contract_amount = COALESCE(v_total_billed, 0::NUMERIC)::NUMERIC,
        updated_on = NEW.updated_on,
        updated_by = NEW.updated_by
    WHERE mcc.deleted IS NOT TRUE
    AND mcc.archived IS NOT TRUE
    AND mcc.contract_type_code IS NOT NULL
    AND EXISTS (
        SELECT 1 
        FROM form_med_claim mc
        INNER JOIN sf_form_med_claim_to_med_claim_info sf_ci
            ON sf_ci.form_med_claim_fk = mc.id
            AND sf_ci.delete IS NOT TRUE
            AND sf_ci.archive IS NOT TRUE
        INNER JOIN form_med_claim_info mci
            ON mci.id = sf_ci.form_med_claim_info_fk
            AND mci.deleted IS NOT TRUE
            AND mci.archived IS NOT TRUE
        INNER JOIN sf_form_med_claim_info_to_med_claim_info_other sf_io
            ON sf_io.form_med_claim_info_fk = mci.id
            AND sf_io.delete IS NOT TRUE
            AND sf_io.archive IS NOT TRUE
        INNER JOIN form_med_claim_info_other mcio
            ON mcio.id = sf_io.form_med_claim_info_other_fk
            AND mcio.deleted IS NOT TRUE
            AND mcio.archived IS NOT TRUE
        INNER JOIN sf_form_med_claim_info_other_to_med_claim_contract sf_cc
            ON sf_cc.form_med_claim_info_other_fk = mcio.id
            AND sf_cc.form_med_claim_contract_fk = mcc.id
            AND sf_cc.delete IS NOT TRUE
            AND sf_cc.archive IS NOT TRUE
        WHERE mc.id = v_claim_id
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- CREATE TRIGGER for syncing charge line fields
DROP TRIGGER IF EXISTS tr_sync_charge_fields_to_claim ON form_ledger_charge_line;

CREATE TRIGGER tr_sync_charge_fields_to_claim
    AFTER UPDATE OF hcpc_code, bill_quantity, billed, ndc, frequency_code, 
                    modifier_1, modifier_2, modifier_3, modifier_4, upin, 
                    charge_quantity, metric_quantity, description, 
                    inventory_id, expected
    ON form_ledger_charge_line
    FOR EACH ROW
    EXECUTE FUNCTION sync_charge_line_fields_to_claim();

-- 8. Trigger to handle voided charge lines
CREATE OR REPLACE FUNCTION handle_voided_charge_line()
RETURNS TRIGGER AS $$
DECLARE
    v_claim_no TEXT;
    v_claim_id INTEGER;
    v_claim_info_id INTEGER;
    v_total_billed NUMERIC;
    v_total_expected NUMERIC;
    v_service_line_id INTEGER;
    v_old_assigned_number INTEGER;
    v_is_voided BOOLEAN;
    v_was_voided BOOLEAN;
BEGIN
    -- Only process if billing_method_id is 'mm' (electronic medical)
    IF NEW.billing_method_id != 'mm' THEN
        RETURN NEW;
    END IF;
    
    -- Check if the charge line is now voided (archived, void, or zeroed)
    v_is_voided := (NEW.archived IS TRUE) OR 
                   (COALESCE(NEW.void, 'No') = 'Yes') OR 
                   (COALESCE(NEW.zeroed, 'No') = 'Yes');
    
    -- Check if it was previously voided
    v_was_voided := (OLD.archived IS TRUE) OR 
                    (COALESCE(OLD.void, 'No') = 'Yes') OR 
                    (COALESCE(OLD.zeroed, 'No') = 'Yes');
    
    -- Only process if void status changed
    IF v_is_voided = v_was_voided THEN
        RETURN NEW;
    END IF;
    
    -- Only process if we have an invoice number
    IF NEW.invoice_no IS NULL THEN
        RETURN NEW;
    END IF;

    -- Check for electronic medical claim
    SELECT mc.id, mc.claim_no INTO v_claim_id, v_claim_no
    FROM form_med_claim mc
    WHERE mc.invoice_no = NEW.invoice_no
    AND mc.deleted IS NOT TRUE
    AND mc.archived IS NOT TRUE
    LIMIT 1;

    IF v_claim_id IS NOT NULL THEN
        -- Get the service line ID
        SELECT sl.id, sl.assigned_number 
        INTO v_service_line_id, v_old_assigned_number
        FROM form_med_claim_sl sl
        WHERE sl.claim_no = v_claim_no
        AND sl.charge_no = NEW.charge_no
        AND sl.deleted IS NOT TRUE
        LIMIT 1;
        
        IF v_service_line_id IS NOT NULL THEN
            IF v_is_voided THEN
                -- Archive the service line
                UPDATE form_med_claim_sl
                SET archived = TRUE,
                    updated_on = NEW.updated_on,
                    updated_by = NEW.updated_by
                WHERE id = v_service_line_id;
                
                -- Archive the linking record
                UPDATE sf_form_med_claim_info_to_med_claim_sl
                SET archive = TRUE
                WHERE form_med_claim_sl_fk = v_service_line_id;
                
                -- Update assigned numbers for remaining service lines
                UPDATE form_med_claim_sl
                SET assigned_number = assigned_number - 1,
                    updated_on = NEW.updated_on,
                    updated_by = NEW.updated_by
                WHERE claim_no = v_claim_no
                AND assigned_number > v_old_assigned_number
                AND deleted IS NOT TRUE
                AND archived IS NOT TRUE;
            ELSE
                -- Un-voiding: restore the service line
                UPDATE form_med_claim_sl
                SET archived = FALSE,
                    updated_on = NEW.updated_on,
                    updated_by = NEW.updated_by
                WHERE id = v_service_line_id;
                
                -- Restore the linking record
                UPDATE sf_form_med_claim_info_to_med_claim_sl
                SET archive = FALSE
                WHERE form_med_claim_sl_fk = v_service_line_id;
                
                -- Reassign line numbers
                WITH renumbered AS (
                    SELECT 
                        id,
                        ROW_NUMBER() OVER (ORDER BY charge_no) as new_number
                    FROM form_med_claim_sl
                    WHERE claim_no = v_claim_no
                    AND deleted IS NOT TRUE
                    AND archived IS NOT TRUE
                )
                UPDATE form_med_claim_sl sl
                SET assigned_number = r.new_number,
                    updated_on = NEW.updated_on,
                    updated_by = NEW.updated_by
                FROM renumbered r
                WHERE sl.id = r.id;
            END IF;
        END IF;

        -- Calculate new totals from all non-voided charge lines
        SELECT 
            SUM(COALESCE(lcl.billed, 0::NUMERIC)::NUMERIC),
            SUM(COALESCE(lcl.expected, 0::NUMERIC)::NUMERIC)
        INTO v_total_billed, v_total_expected
        FROM form_ledger_charge_line lcl
        WHERE lcl.invoice_no = NEW.invoice_no
        AND lcl.deleted IS NOT TRUE
        AND lcl.archived IS NOT TRUE
        AND COALESCE(lcl.void, 'No') <> 'Yes'
        AND COALESCE(lcl.zeroed, 'No') <> 'Yes';
        
        -- Update claim header totals
        UPDATE form_med_claim
        SET billed = COALESCE(v_total_billed, 0::NUMERIC)::NUMERIC,
            expected = COALESCE(v_total_expected, 0::NUMERIC)::NUMERIC,
            updated_on = NEW.updated_on,
            updated_by = NEW.updated_by
        WHERE id = v_claim_id;
        
        -- Get claim info ID and update claim_charge_amount
        SELECT mci.id INTO v_claim_info_id
        FROM form_med_claim mc
        INNER JOIN sf_form_med_claim_to_med_claim_info sf_ci 
            ON sf_ci.form_med_claim_fk = mc.id
            AND sf_ci.delete IS NOT TRUE
            AND sf_ci.archive IS NOT TRUE
        INNER JOIN form_med_claim_info mci 
            ON mci.id = sf_ci.form_med_claim_info_fk
            AND mci.deleted IS NOT TRUE
            AND mci.archived IS NOT TRUE
        WHERE mc.id = v_claim_id
        LIMIT 1;
        
        IF v_claim_info_id IS NOT NULL THEN
            UPDATE form_med_claim_info
            SET claim_charge_amount = COALESCE(v_total_billed, 0::NUMERIC)::NUMERIC,
                updated_on = NEW.updated_on,
                updated_by = NEW.updated_by
            WHERE id = v_claim_info_id;
        END IF;
        
        -- Update form_med_claim_contract if it exists
        UPDATE form_med_claim_contract mcc
        SET contract_amount = COALESCE(v_total_billed, 0::NUMERIC)::NUMERIC,
            updated_on = NEW.updated_on,
            updated_by = NEW.updated_by
        WHERE mcc.deleted IS NOT TRUE
        AND mcc.archived IS NOT TRUE
        AND mcc.contract_type_code IS NOT NULL
        AND EXISTS (
            SELECT 1 
            FROM form_med_claim mc
            INNER JOIN sf_form_med_claim_to_med_claim_info sf_ci
                ON sf_ci.form_med_claim_fk = mc.id
                AND sf_ci.delete IS NOT TRUE
                AND sf_ci.archive IS NOT TRUE
            INNER JOIN form_med_claim_info mci
                ON mci.id = sf_ci.form_med_claim_info_fk
                AND mci.deleted IS NOT TRUE
                AND mci.archived IS NOT TRUE
            INNER JOIN sf_form_med_claim_info_to_med_claim_info_other sf_io
                ON sf_io.form_med_claim_info_fk = mci.id
                AND sf_io.delete IS NOT TRUE
                AND sf_io.archive IS NOT TRUE
            INNER JOIN form_med_claim_info_other mcio
                ON mcio.id = sf_io.form_med_claim_info_other_fk
                AND mcio.deleted IS NOT TRUE
                AND mcio.archived IS NOT TRUE
            INNER JOIN sf_form_med_claim_info_other_to_med_claim_contract sf_cc
                ON sf_cc.form_med_claim_info_other_fk = mcio.id
                AND sf_cc.form_med_claim_contract_fk = mcc.id
                AND sf_cc.delete IS NOT TRUE
                AND sf_cc.archive IS NOT TRUE
            WHERE mc.id = v_claim_id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- CREATE TRIGGER for handling voided charge lines
DROP TRIGGER IF EXISTS tr_handle_voided_charge_line ON form_ledger_charge_line;

CREATE TRIGGER tr_handle_voided_charge_line
    AFTER UPDATE OF archived, void, zeroed ON form_ledger_charge_line
    FOR EACH ROW
    EXECUTE FUNCTION handle_voided_charge_line();

-- Add comments
COMMENT ON FUNCTION update_other_payer_adjustment_indicator() IS 
'Updates the other_payer_claim_adjustment_indicator on claims when adjustments are added/modified/deleted';

COMMENT ON FUNCTION sync_rental_service_dates() IS 
'Updates service dates on claims when rental charge line dates change and calculates DME rental days';

COMMENT ON FUNCTION sync_service_line_diagnoses() IS 
'Keeps diagnoses in sync between med_claim_sv entries and med_claim_dx entries';

COMMENT ON FUNCTION add_charge_line_to_claim() IS 
'Automatically adds new charge lines to existing claims when appropriate';

COMMENT ON FUNCTION sync_charge_line_fields_to_claim() IS 
'Syncs various fields (HCPC, quantities, NDC, modifiers, etc.) from charge lines to service lines';

COMMENT ON FUNCTION handle_voided_charge_line() IS 
'Archives service lines when charge lines are voided and voids claim if no lines remain';

-- CREATE TRIGGER for adding charge lines to claims
DROP TRIGGER IF EXISTS tr_add_charge_line_to_claim ON form_ledger_charge_line;

CREATE TRIGGER tr_add_charge_line_to_claim
    AFTER INSERT ON form_ledger_charge_line
    FOR EACH ROW
    WHEN (NEW.billing_method_id = 'mm')
    EXECUTE FUNCTION add_charge_line_to_claim();
