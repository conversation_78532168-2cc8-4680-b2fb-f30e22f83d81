
DO $$ 
BEGIN
    -- Create type for reject codes if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_receiver_segment') THEN
        CREATE TYPE mm_receiver_segment AS (
            payer_id integer,
            organization_name text,
            address mm_address_info[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_contact_info') THEN
        CREATE TYPE mm_contact_info AS (
            name text,
            email text,
            phone_number text,
            fax_number text
        );
    END IF;

    -- Create type for other amount paid if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_submitter_segment') THEN
        CREATE TYPE mm_submitter_segment AS (
            site_id integer,
            organization_name text,
            contact_information mm_contact_info[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_patient_contact_info') THEN
        CREATE TYPE mm_patient_contact_info AS (
            name text,
            email text,
            phone_number text
        );
    END IF;

    -- Create type for benefit amount if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_address_info') THEN
        CREATE TYPE mm_address_info AS (
            address1 text,
            address2 text,
            city text,
            state text,
            postal_code text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_subscriber_segment') THEN
        CREATE TYPE mm_subscriber_segment AS (
            insurance_id integer,
            medical_relationship_id text,
            patient_id integer,
            first_name text,
            last_name text,
            middle_name text,
            date_of_birth text,
            gender text,
            member_id text,
            policy_number text,
            group_number text,
            payment_responsibility_level_code text,
            insurance_type_code text,
            address mm_address_info[],
            contact_information mm_patient_contact_info[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_billing_provider_info') THEN
        CREATE TYPE mm_billing_provider_info AS (
            site_id integer,
            provider_type text,
            organization_name text,
            npi text,
            commercial_number text,
            state_license_number text,
            contact_information mm_contact_info[],
            address mm_address_info[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_dr_provider_info') THEN
        CREATE TYPE mm_dr_provider_info AS (
            patient_id integer,
            prescriber_id integer,
            physician_id integer,
            provider_type text,
            last_name text,
            first_name text,
            contact_information mm_contact_info[],
            address mm_address_info[],
            npi text,
            commercial_number text,
            state_license_number text,
            taxonomy_code text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_rendering_provider_info') THEN
        CREATE TYPE mm_rendering_provider_info AS (
            site_id integer,
            provider_type text,
            contact_information mm_contact_info[],
            address mm_address_info[],
            organization_name text,
            npi text,
            employer_identification_number text,
            commercial_number text,
            state_license_number text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_providers_loop') THEN
        CREATE TYPE mm_providers_loop AS (
            patient_id integer,
            site_id integer,
            billing mm_billing_provider_info[],
            tabif_billing text,
            referring mm_dr_provider_info[],
            tabif_referring text,
            ordering mm_dr_provider_info[],
            tabif_ordering text,
            rendering mm_rendering_provider_info[],
            tabif_rendering text,
            supervising mm_dr_provider_info[],
            tabif_supervising text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_dependent_segment') THEN
        CREATE TYPE mm_dependent_segment AS (
            relationship_to_subscriber_code text,
            member_id text,
            first_name text,
            last_name text,
            middle_name text,
            date_of_birth text,
            gender text,
            address mm_address_info[],
            contact_information mm_patient_contact_info[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_dx_info') THEN
        CREATE TYPE mm_dx_info AS (
            patient_id integer,
            dx_id integer,
            diagnosis_type_code text,
            diagnosis_code text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_service_facility_info') THEN
        CREATE TYPE mm_service_facility_info AS (
            infusion_suite_id integer,
            npi text,
            organization_name text,
            phone_name text,
            phone_number text,
            address mm_address_info[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_med_claim_report_info') THEN
        CREATE TYPE mm_med_claim_report_info AS (
            document_id integer,
            attachment_report_type_code text,
            attachment_transmission_code text,
            attachment_control_number text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_supplemental_info') THEN
        CREATE TYPE mm_supplemental_info AS (
            patient_id integer,
            insurance_id integer,
            claim_number text,
            pa_id integer,
            prior_authorization_number text,
            medical_record_number text,
            med_claim_report mm_med_claim_report_info[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_claim_date_info') THEN
        CREATE TYPE mm_claim_date_info AS (
            symptom_date text,
            accident_date text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_contract_info') THEN
        CREATE TYPE mm_contract_info AS (
            contract_amount numeric,
            contract_type_code text,
            contract_code text,
            contract_version_identifier text,
            contract_percentage numeric,
            terms_discount_percentage numeric
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_file_info') THEN
        CREATE TYPE mm_file_info AS (
            file text,
            comments text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_other_info') THEN
        CREATE TYPE mm_other_info AS (
            site_id integer,
            patient_id integer,
            payer_id integer,
            mm_send_contract_pricing text,
            claim_date_information mm_claim_date_info[],
            tabif_claim_date_information text,
            claim_contract_information mm_contract_info[],
            tabif_claim_contract_information text,
            file_information_list mm_file_info[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_sl_repricing_info') THEN
        CREATE TYPE mm_sl_repricing_info AS (
            pricing_methodology_code text,
            repriced_allowed_amount numeric,
            repriced_saving_amount numeric,
            repricing_organization_identifier text,
            repricing_per_diem_or_flat_rate_amount numeric,
            reject_reason_code text,
            policy_compliance_code text,
            exception_code text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_repricing_info') THEN
        CREATE TYPE mm_repricing_info AS (
            repricing_organization_identifier text,
            pricing_methodology_code text,
            exception_code text,
            repriced_allowed_amount numeric,
            repriced_saving_amount numeric,
            repricing_per_diem_or_flat_rate_amount numeric,
            repriced_approved_ambulatory_patient_group_code text,
            repriced_approved_ambulatory_patient_group_amount numeric,
            reject_reason_code text,
            policy_compliance_code text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_adjustment_detail_info') THEN
        CREATE TYPE mm_adjustment_detail_info AS (
            adjustment_reason_code text,
            adjustment_amount numeric,
            adjustment_quantity numeric
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_claim_level_adjustment_info') THEN
        CREATE TYPE mm_claim_level_adjustment_info AS (
            adjustment_group_code text,
            adjustment_details mm_adjustment_detail_info[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_other_payer_secondary_identifier') THEN
        CREATE TYPE mm_other_payer_secondary_identifier AS (
            qualifier text,
            identifier text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_other_payer_info') THEN
        CREATE TYPE mm_other_payer_info AS (
            patient_id integer,
            cob_insurance_id integer,
            cob_payer_id integer,
            other_payer_organization_name text,
            other_payer_identifier_type_code text,
            other_payer_identifier text,
            other_payer_adjudication_or_payment_date text,
            other_payer_claim_adjustment_indicator text,
            pa_id integer,
            other_payer_prior_authorization_number text,
            other_payer_claim_control_number text,
            other_payer_secondary_identifier mm_other_payer_secondary_identifier[],
            other_payer_address mm_address_info[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_other_sub_info') THEN
        CREATE TYPE mm_other_sub_info AS (
            other_insured_qualifier text,
            other_insured_identifier_type_code text,
            other_insured_identifier text,
            other_insured_first_name text,
            other_insured_last_name text,
            other_insured_middle_name text,
            other_insured_name_suffix text,
            other_insured_address mm_address_info[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_id_qualifier') THEN
        CREATE TYPE mm_id_qualifier AS (
            qualifier text,
            identifier text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_other_billing_provider_info') THEN
        CREATE TYPE mm_other_billing_provider_info AS (
            entity_type_qualifier text,
            other_payer_billing_provider_identifier mm_id_qualifier[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_other_rendering_provider_info') THEN
        CREATE TYPE mm_other_rendering_provider_info AS (
            entity_type_qualifier text,
            other_payer_rendering_provider_secondary_identifier mm_id_qualifier[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_other_supervising_provider_info') THEN
        CREATE TYPE mm_other_supervising_provider_info AS (
            other_payer_supervising_provider_identifier mm_id_qualifier[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_other_referring_provider_info') THEN
        CREATE TYPE mm_other_referring_provider_info AS (
            other_payer_referring_provider_identifier mm_id_qualifier[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_cob_providers_loop') THEN
        CREATE TYPE mm_cob_providers_loop AS (
            other_payer_billing_provider mm_other_billing_provider_info[],
            tabif_other_payer_billing_provider text,
            other_payer_referring_provider mm_other_referring_provider_info[],
            tabif_other_payer_referring_provider text,
            other_payer_rendering_provider mm_other_rendering_provider_info[],
            tabif_other_payer_rendering_provider text,
            other_payer_supervising_provider mm_other_supervising_provider_info[],
            tabif_other_payer_supervising_provider text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_other_service_facility_info') THEN
        CREATE TYPE mm_other_service_facility_info AS (
            other_payer_service_facility_location_secondary_identifier mm_id_qualifier[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_cob_info') THEN
        CREATE TYPE mm_cob_info AS (
            patient_id integer,
            cob_insurance_id integer,
            cob_payer_id integer,
            cob_payer_type_id text,
            payment_responsibility_level_code text,
            individual_relationship_code text,
            insurance_type_code text,
            claim_filing_indicator_code text,
            benefits_assignment_certification_indicator text,
            patient_signature_generate_for_patient text,
            insurance_group_or_policy_number text,
            other_insured_group_name text,
            release_of_information_code text,
            other_subscriber_name mm_other_sub_info[],
            other_payer_name mm_other_payer_info[],
            payer_paid_amount numeric,
            non_covered_charge_amount numeric,
            remaining_patient_liability numeric,
            mcr_reimbursement_rate numeric,
            mcr_hcpcs_payable_amount numeric,
            mcr_rcodes text[],
            claim_level_adjustments mm_claim_level_adjustment_info[],
            tabif_claim_level_adjustments text,
            providers mm_cob_providers_loop[],
            tabif_providers text,
            other_payer_service_facility_location mm_other_service_facility_info[],
            tabif_other_payer_service_facility_location text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_service_line_reference_info_pa') THEN
        CREATE TYPE mm_service_line_reference_info_pa AS (
            patient_id integer,
            insurance_id integer,
            pa_id integer,
            prior_authorization_or_referral_number text
            );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_service_line_reference_info') THEN
        CREATE TYPE mm_service_line_reference_info AS (
            patient_id integer,
            prior_authorization mm_service_line_reference_info_pa[]
            );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_professional_service_info') THEN
        CREATE TYPE mm_professional_service_info AS (
            patient_id integer,
            type text,
            charge_no text,
            inventory_id integer,
            description text,
            line_item_charge_amount numeric,
            measurement_unit text,
            service_unit_count numeric,
            place_of_service_code text,
            emergency_indicator text,
            epsdt_indicator text,
            copay_status_code text,
            dx_filter integer[],
            dx_id_1 integer,
            dx_id_2 integer,
            dx_id_3 integer,
            dx_id_4 integer,
            procedure_identifier text,
            procedure_code text,
            modifier_1 text,
            modifier_2 text,
            modifier_3 text,
            modifier_4 text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_drug_identification_info') THEN
        CREATE TYPE mm_drug_identification_info AS (
            charge_no text,
            inventory_id integer,
            service_id_qualifier text,
            national_drug_code text,
            national_drug_unit_count numeric,
            measurement_unit_code text,
            link_sequence_number integer,
            pharmacy_prescription_number text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_dme_info') THEN
        CREATE TYPE mm_dme_info AS (
            charge_no text,
            inventory_id integer,
            days integer,
            frequency_code text,
            rental_price numeric,
            purchase_price numeric
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_dme_cmn_info') THEN
        CREATE TYPE mm_dme_cmn_info AS (
            attachment_transmission_code text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_dme_cert_info') THEN
        CREATE TYPE mm_dme_cert_info AS (
            send_cert text,
            certification_type_code text,
            durable_medical_equipment_duration_in_months numeric
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_dme_condition_info') THEN
        CREATE TYPE mm_dme_condition_info AS (
            certification_condition_indicator text,
            condition_indicator text,
            condition_indicator_code text
            );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_service_line_date_info') THEN
        CREATE TYPE mm_service_line_date_info AS (
            prescription_date text,
            shipped_date text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_sl_adj_detail_info') THEN
        CREATE TYPE mm_sl_adj_detail_info AS (
            adjustment_reason_code text,
            adjustment_amount numeric,
            adjustment_quantity numeric
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_sl_adj_group_info') THEN
        CREATE TYPE mm_sl_adj_group_info AS (
            adjustment_group_code text,
            adjustment_details mm_sl_adj_detail_info[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_sl_adjustment_info') THEN
        CREATE TYPE mm_sl_adjustment_info AS (
            other_payer_primary_identifier text,
            procedure_code text,
            service_id_qualifier text,
            procedure_code_description text,
            paid_service_unit_count numeric,
            adjudication_or_payment_date text,
            service_line_paid_amount numeric,
            remaining_patient_liability numeric,
            modifier_1 text,
            modifier_2 text,
            modifier_3 text,
            modifier_4 text,
            claim_adjustment_information mm_sl_adj_group_info[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_sl_supplemental_info') THEN
        CREATE TYPE mm_sl_supplemental_info AS (
            document_id integer,
            attachment_report_type_code text,
            attachment_transmission_code text,
            attachment_control_number text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_service_line_info') THEN
        CREATE TYPE mm_service_line_info AS (
            claim_no text,
            charge_no text,
            lock_sv text,
            patient_id integer,
            site_id integer,
            payer_id integer,
            mm_calc_perc_sales_tax text,
            inventory_id integer,
            measurement_unit text,
            service_unit_count numeric,
            dx_id_1 integer, 
            modifier_1 text,
            line_item_charge_amount numeric,
            assigned_number integer,
            provider_control_number text,
            service_date text,
            service_date_end text,
            service_line_reference_information mm_service_line_reference_info[],
            professional_service mm_professional_service_info[],
            drug_identification mm_drug_identification_info[],
            durable_medical_equipment_service mm_dme_info[],
            durable_medical_equipment_certificate_of_medical_necessity mm_dme_cmn_info[],
            durable_medical_equipment_certification mm_dme_cert_info[],
            condition_indicator_durable_medical_equipment mm_dme_condition_info[],
            service_line_date_information mm_service_line_date_info[],
            additional_notes text,
            goal_rehab_or_discharge_plans text,
            third_party_organization_notes text,
            line_pricing_repricing_information mm_sl_repricing_info[],
            line_adjudication_information mm_sl_adjustment_info[],
            service_line_supplemental_information mm_sl_supplemental_info[],
            file_information mm_file_info[]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_claim_info_segment') THEN
        CREATE TYPE mm_claim_info_segment AS (
            site_id integer,
            patient_id integer,
            insurance_id integer,
            payer_id integer,
            patient_weight numeric,
            death_date text,
            patient_control_number text,
            gender text,
            claim_filing_code text,
            place_of_service_code text,
            plan_participation_code text,
            claim_charge_amount numeric,
            patient_amount_paid numeric,
            claim_frequency_code text,
            signature_indicator text,
            patient_signature_source_code text,
            release_information_code text,
            homebound_indicator text,
            benefits_assignment_certification_indicator text,
            pregnancy_indicator text,
            delay_reason_code text,
            health_care_code_information mm_dx_info[],
            claim_pricing_repricing_information mm_repricing_info[],
            service_facility_location mm_service_facility_info[],
            other_subscriber_information mm_cob_info[],
            service_lines mm_service_line_info[],
            claim_supplemental_information mm_supplemental_info[],
            tabif_claim_supplemental_information text,
            claim_info_other mm_other_info[],
            tabif_claim_info_other text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_claim_record') THEN
        CREATE TYPE mm_claim_record AS (
            uuid text,
            parent_claim_no text,
            claim_no text,
            usage_indicator text,
            service_date integer,
            site_id integer,
            patient_id integer,
            insurance_id integer,
            payer_id integer,
            organization_name text,
            dependent_required text,
            control_number text,
            status text,
            substatus_id text,
            trading_partner_service_id text,
            trading_partner_name text,
            expected numeric,
            billed numeric,
            receiver mm_receiver_segment[],
            submitter mm_submitter_segment[],
            pay_to_address mm_address_info[],
            subscriber mm_subscriber_segment[],
            dependent mm_dependent_segment[],
            claim_information mm_claim_info_segment[],
            providers mm_providers_loop[]
        );
    END IF;

    -- Helper types for function parameters to replace RECORD types
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_parent_claim_info') THEN
        CREATE TYPE mm_parent_claim_info AS (
            patient_id integer,
            insurance_id integer,
            payer_id integer,
            billed numeric,
            status text,
            pa_id integer,
            pa_no_submitted text,
            billing_method_id text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_parent_insurance_info') THEN
        CREATE TYPE mm_parent_insurance_info AS (
            patient_id integer,
            payer_id integer,
            medical_relationship_id text,
            cardholder_id text,
            policy_number text,
            group_name text,
            group_number text,
            insurance_type_code text,
            beneficiary_fname text,
            beneficiary_lname text,
            beneficiary_mname text,
            beneficiary_dob text,
            beneficiary_gender text,
            beneficiary_address1 text,
            beneficiary_address2 text,
            beneficiary_city text,
            beneficiary_state_id text,
            beneficiary_postal_code text,
            contact_name text,
            contact_email text,
            contact_phone text,
            payer_organization text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_payer_address_info') THEN
        CREATE TYPE mm_payer_address_info AS (
            address1 text,
            address2 text,
            city text,
            state_id text,
            zip text
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mm_inventory_info') THEN
        CREATE TYPE mm_inventory_info AS (
            id integer,
            description text,
            ndc text,
            type text,
            rental_type text,
            frequency_code text,
            upin text
        );
    END IF;

END $$;
