DO $$ BEGIN
  PERFORM drop_all_function_signatures('gen_delivery_ticket_item_by_type');
END $$;
CREATE OR REPLACE FUNCTION gen_delivery_ticket_item_by_type(
    p_site_id integer,
    p_patient_id integer,
    p_careplan_id integer,
    p_inventory_id integer,
    p_rx_id integer,
    p_ticket_no text,
    p_part_of_kit text DEFAULT NULL,
    p_rental_type text DEFAULT NULL,
    p_frequency_code text DEFAULT NULL,
    p_day_supply integer DEFAULT NULL
) RETURNS json AS $BODY$
DECLARE
    v_params jsonb;
    v_inventory_active boolean;
    v_inventory_name text;
    v_result json;
    v_result_jsonb jsonb;
    v_insurance_id integer;
    v_billing_method text;
    v_order_id integer;
    v_type text;
    v_supply_billable text;
    v_hcpc_code text;
    v_ndc text;
    v_formatted_ndc text;
    v_ticket_status text;
    v_fetched_inventory_id integer;
    v_base_json jsonb;
BEGIN
    -- Build parameters JSON for logging
    v_params := json_build_object(
        'p_site_id', p_site_id,
        'p_patient_id', p_patient_id,
        'p_careplan_id', p_careplan_id,
        'p_inventory_id', p_inventory_id,
        'p_rx_id', p_rx_id,
        'p_ticket_no', p_ticket_no,
        'p_part_of_kit', p_part_of_kit
    );

    SELECT COALESCE(status::text, 'delivery_ticket') INTO v_ticket_status
    FROM form_careplan_delivery_tick
    WHERE ticket_no = p_ticket_no;

    -- Validate required parameters
    IF p_site_id IS NULL 
    OR p_patient_id IS NULL OR p_careplan_id IS NULL 
    OR p_inventory_id IS NULL OR p_ticket_no IS NULL THEN
        INSERT INTO dispensing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Missing required parameters for delivery ticket item preset generation',
            'Exception in gen_delivery_ticket_item_by_type',
            'FUNCTION',
            current_schema(),
            'form_careplan_delivery_tick',
            v_params
        );
        RAISE EXCEPTION 'Missing required parameters for delivery ticket item preset generation';
    END IF;

    -- Get insurance ID directly from vw_order_raw if rx_id is provided
    -- This is only for insurance lookup purposes
    IF p_rx_id IS NOT NULL THEN
        SELECT insurance_id INTO v_insurance_id
        FROM vw_order_raw
        WHERE rx_id = p_rx_id;
    END IF;
    
    -- If we couldn't find insurance_id, try with patient's insurance
    IF v_insurance_id IS NULL THEN
        SELECT id INTO v_insurance_id
        FROM form_patient_insurance
        WHERE patient_id = p_patient_id
        AND COALESCE(active, 'No') = 'Yes'
        AND archived IS NOT TRUE
        AND deleted IS NOT TRUE
        ORDER BY id DESC
        LIMIT 1;
    END IF;
    
    RAISE NOTICE 'Insurance ID: %, RX ID: %', v_insurance_id, p_rx_id;

    -- Check if inventory is active
    SELECT COALESCE(inv.active, 'No') = 'Yes',
           inv.name,
           inv.type,
           inv.hcpc_code,
           inv.ndc,
           inv.formatted_ndc
    INTO v_inventory_active,
         v_inventory_name,
         v_type,
         v_hcpc_code,
         v_ndc,
         v_formatted_ndc
    FROM form_inventory inv
    WHERE inv.id = p_inventory_id AND inv.archived IS NOT TRUE AND inv.deleted IS NOT TRUE;

    IF NOT v_inventory_active THEN
        INSERT INTO dispensing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Inventory item % is not active. Cannot dispense.',
            'Exception in gen_delivery_ticket_item_by_type',
            'FUNCTION',
            current_schema(),
            'form_inventory',
            v_params
        );  
        RAISE EXCEPTION 'Inventory item % is not active. Cannot dispense.', v_inventory_name;
    END IF;

    -- Start with base json common to all item types
    v_base_json := jsonb_build_object(
        'patient_id', p_patient_id,
        'careplan_id', p_careplan_id,
        'site_id', p_site_id,
        'status', v_ticket_status,
        'ticket_no', p_ticket_no,
        'ticket_item_no', gen_random_uuid()::text,
        'inventory_id', p_inventory_id,
        'type', v_type,
        'print', 'Yes',
        'rx_id', p_rx_id,
        'insurance_id', v_insurance_id,
        'hcpc_code', v_hcpc_code,
        'ndc', v_ndc,
        'formatted_ndc', v_formatted_ndc,
        'accept_assignment', 'Yes'
    );

    IF v_type IN ('Drug', 'Compound') THEN
    INSERT INTO dispensing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Cannot add Drugs or Compounds outside of a prescription',
            'Exception in gen_delivery_ticket_item_by_type',
            'FUNCTION',
            current_schema(),
            'form_careplan_delivery_tick',
            v_params
        );
        RAISE EXCEPTION 'Cannot add Drugs or Compounds outside of a prescription';
    END IF;

    -- Generate the preset data based on type
    IF v_type = 'Billable' THEN
        -- Handle billable items directly without pricing function
        SELECT
            jsonb_strip_nulls(v_base_json || jsonb_build_object(
                -- Billable-specific fields
                'needs_scanned', NULL,
                'bill', 'Yes',

                -- Billing fields
                'billing_method', 'Insurance',

                -- Pricing fields
                'billing_method_id', prc.billing_method_id,
                'expected_ea', COALESCE(prc.expected_ea, 0),
                'list_ea', COALESCE(prc.list_ea, 0),
                'billed_ea', COALESCE(prc.bill_ea, 0),
                'dispense_unit', 'each',
                'dispense_quantity', 1
            ))
        INTO v_result_jsonb
        FROM form_inventory inv
        LEFT JOIN get_inventory_pricing(p_inventory_id, v_insurance_id, p_site_id, p_patient_id) prc ON TRUE
        WHERE inv.id = p_inventory_id;
    ELSIF v_type = 'Equipment Rental' THEN
        -- Handle Equipment Rental items without using rx data
        SELECT
            jsonb_strip_nulls(v_base_json || jsonb_build_object(
                -- Rental-specific fields
                'needs_scanned', 'Yes',
                'hcpc_code', NULLIF(prc.hcpc_code, ''),
                'ndc', prc.ndc,
                'formatted_ndc', prc.formatted_ndc,
                
                -- Rental specific settings
                'rental_type', p_rental_type,
                'frequency_code', p_frequency_code,
                'day_supply', p_day_supply,

                -- Common quantity and billing fields
                'allow_dispense_quantity_update', 'Yes',
                'dispense_quantity', 1,
                'dispense_unit', 'each',
                'quantity_needed', 1,

                -- Billing fields
                'billing_method', CASE
                    WHEN COALESCE(prc.billable, 'No') = 'Yes' THEN
                        CASE 
                            WHEN prc.billing_method_id = 'ncpdp' THEN 'Insurance'
                            WHEN prc.billing_method_id IN ('cms1500', 'mm') THEN 'Insurance'
                            ELSE 'Self Pay'
                        END
                    ELSE 'Do Not Bill'
                END,
                'accept_assignment', CASE
                    WHEN COALESCE(prc.billable, 'No') = 'Yes' THEN 'Yes'
                    ELSE NULL
                END,

                -- Pricing fields
                'expected_ea', CASE 
                WHEN p_rental_type = 'Rental' THEN
                    CASE 
                    	WHEN p_frequency_code = '6' THEN COALESCE(prc.rental_expected_daily_ea, 0)::numeric
                    	WHEN p_frequency_code = '1' THEN (COALESCE(prc.rental_expected_monthly_ea, 0)::numeric/7)::numeric
                    	ELSE COALESCE(prc.rental_bill_monthly_ea, 0)::numeric
                    END
                ELSE COALESCE(prc.expected_ea, 0)
                END,
                'list_ea', CASE 
                WHEN p_rental_type = 'Rental' THEN
                    CASE 
                    	WHEN p_frequency_code = '6' THEN COALESCE(prc.daily_rental_list_price, 0)::numeric
                    	WHEN p_frequency_code = '1' THEN (COALESCE(prc.monthly_rental_list_price, 0)::numeric/7)::numeric
                    	ELSE COALESCE(prc.monthly_rental_list_price, 0)::numeric
                    END
                ELSE COALESCE(prc.list_ea, 0)
                END,
                'billed_ea', CASE 
                WHEN p_rental_type = 'Rental' THEN
                    CASE 
                    	WHEN p_frequency_code = '6' THEN COALESCE(prc.rental_bill_daily_ea, 0)::numeric
                     	WHEN p_frequency_code = '1' THEN (COALESCE(prc.rental_bill_monthly_ea, 0)::numeric/7)::numeric
                    	ELSE COALESCE(prc.rental_bill_monthly_ea, 0)::numeric
                    END
                ELSE COALESCE(prc.bill_ea, 0)
                END
            ))
        INTO v_result_jsonb
        FROM form_inventory inv
        LEFT JOIN get_inventory_pricing(p_inventory_id, v_insurance_id, p_site_id, p_patient_id) prc ON TRUE
        WHERE inv.id = p_inventory_id AND inv.archived IS NOT TRUE AND inv.deleted IS NOT TRUE AND inv.active = 'Yes';
    ELSIF v_type = 'Supply' THEN
        -- Handle supply items without using rx data
        SELECT
            jsonb_strip_nulls(v_base_json || jsonb_build_object(
                -- Supply-specific fields
                'supply_billable', CASE WHEN COALESCE(prc.billable, 'No') = 'Yes' THEN 'Yes' ELSE NULL END,
                'part_of_kit', p_part_of_kit,
                
                -- Common quantity and billing fields
                'allow_dispense_quantity_update', 'Yes',
                'dispense_quantity', 1,
                'dispense_unit', 'each',
                'quantity_needed', 1,

                -- Billing fields
                'billing_method', CASE
                    WHEN COALESCE(prc.billable, 'No') = 'Yes' THEN
                        CASE 
                            WHEN prc.billing_method_id = 'ncpdp' THEN 'Insurance'
                            WHEN prc.billing_method_id IN ('cms1500', 'mm') THEN 'Insurance'
                            ELSE 'Self Pay'
                        END
                    ELSE 'Do Not Bill'
                END,
                'accept_assignment', CASE
                    WHEN COALESCE(prc.billable, 'No') = 'Yes' THEN 'Yes'
                    ELSE NULL
                END,

                -- Pricing fields
                'expected_ea', COALESCE(prc.cost_ea, 0),
                'list_ea', COALESCE(prc.cost_ea, 0),
                'billed_ea', COALESCE(prc.cost_ea, 0)
            ))
        INTO v_result_jsonb
        FROM form_inventory inv
        LEFT JOIN get_inventory_pricing(p_inventory_id, v_insurance_id, p_site_id, p_patient_id) prc ON TRUE
        WHERE inv.id = p_inventory_id AND inv.archived IS NOT TRUE AND inv.deleted IS NOT TRUE AND COALESCE(inv.active, 'No') = 'Yes';
    END IF;

    -- Convert the JSONB result to JSON for return
    v_result := v_result_jsonb::json;
    RETURN v_result;

    EXCEPTION WHEN OTHERS THEN
        -- Log error
        INSERT INTO dispensing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in gen_delivery_ticket_item_by_type',
            'FUNCTION',
            current_schema(),
            'form_careplan_delivery_tick',
            v_params
        );
        
    RAISE WARNING 'Failed to generate delivery ticket item preset: %', SQLERRM;
    RETURN NULL;
END;
$BODY$ LANGUAGE plpgsql;