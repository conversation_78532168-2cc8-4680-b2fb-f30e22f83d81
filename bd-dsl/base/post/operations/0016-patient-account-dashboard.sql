CREATE OR REPLACE VIEW vw_patient_account_dashboard AS
WITH patient_balance AS (
    SELECT
        patient_id,
        SUM(balance_raw) AS amount_raw
    FROM
        vw_patient_open_invoice_drill_down
    GROUP BY
        patient_id
),
insurance_balance AS (
    SELECT
        patient_id,
        SUM(balance_raw) AS amount_raw
    FROM
        vw_insurance_open_invoice_drill_down
    GROUP BY
        patient_id
),
total_balance AS (
    SELECT
        patient_id,
        SUM(balance_raw) AS amount_raw
    FROM
        vw_combined_open_invoice_drill_down
    GROUP BY
        patient_id
),
held_revenue AS (
    SELECT
        patient_id,
        SUM(expected_raw) AS amount_raw
    FROM
        vw_invoice_onhold_revenue
    GROUP BY
        patient_id
),
unbilled_revenue AS (
    SELECT
        patient_id,
        SUM(expected_raw) AS amount_raw
    FROM
        vw_patient_pending_revenue
    GROUP BY
        patient_id
),
total_revenue AS (
    SELECT 
        patient_id,
        SUM(
            CASE 
                WHEN account_type = 'Revenue' THEN COALESCE(credit, 0) - COALESCE(debit, 0)
                ELSE 0
            END
        ) AS amount_raw
    FROM 
        vw_ledger_finance_metrics 
    GROUP BY 
        patient_id
),
cash_balance AS (
    SELECT 
        act.patient_id,
        SUM(
            CASE 
                WHEN fm.account_type = 'Unapplied Cash' THEN COALESCE(fm.credit, 0) - COALESCE(fm.debit, 0)
                ELSE 0
            END
        ) AS amount_raw
    FROM 
        vw_ledger_finance_metrics fm
    INNER JOIN form_billing_account act ON act.id = fm.account_id
    GROUP BY
        act.patient_id
)
-- Combine all metrics into one view
SELECT
    p.id AS patient_id,
    p.firstname,
    p.lastname,
    p.mrn,
    
    -- Patient Balance
    COALESCE(pb.amount_raw::numeric, 0::numeric) AS patient_balance_raw,
    format_currency(COALESCE(pb.amount_raw::numeric, 0::numeric)) AS patient_balance,
    
    -- Insurance Balance
    COALESCE(ib.amount_raw::numeric, 0::numeric) AS insurance_balance_raw,
    format_currency(COALESCE(ib.amount_raw::numeric, 0::numeric)) AS insurance_balance,
    
    -- Total Balance
    COALESCE(tb.amount_raw::numeric, 0::numeric) AS total_balance_raw,
    format_currency(COALESCE(tb.amount_raw::numeric, 0::numeric)) AS total_balance,
    
    -- Held Revenue
    COALESCE(hr.amount_raw::numeric, 0::numeric) AS held_revenue_raw,
    format_currency(COALESCE(hr.amount_raw::numeric, 0::numeric)) AS held_revenue,
    
    -- Unbilled Revenue
    COALESCE(ur.amount_raw::numeric, 0::numeric) AS unbilled_revenue_raw,
    format_currency(COALESCE(ur.amount_raw::numeric, 0::numeric)) AS unbilled_revenue,
    
    -- Total Revenue
    COALESCE(tr.amount_raw::numeric, 0::numeric) AS total_revenue_raw,
    format_currency(COALESCE(tr.amount_raw::numeric, 0::numeric)) AS total_revenue,

    -- Cash Balance
    COALESCE(cb.amount_raw::numeric, 0::numeric) AS cash_balance_raw,
    format_currency(COALESCE(cb.amount_raw::numeric, 0::numeric)) AS cash_balance
FROM
    form_patient p
LEFT JOIN
    patient_balance pb ON pb.patient_id = p.id
LEFT JOIN
    insurance_balance ib ON ib.patient_id = p.id
LEFT JOIN
    total_balance tb ON tb.patient_id = p.id
LEFT JOIN
    held_revenue hr ON hr.patient_id = p.id
LEFT JOIN
    unbilled_revenue ur ON ur.patient_id = p.id
LEFT JOIN
    total_revenue tr ON tr.patient_id = p.id
LEFT JOIN
    cash_balance cb ON cb.patient_id = p.id;

-- Patient Dashboard API Query
-- Create a function to return the formatted dashboard data for a specific patient
-- Update the get_patient_dashboard function to use the new ledger_finance model

CREATE OR REPLACE FUNCTION get_patient_dashboard(p_patient_id INTEGER)
RETURNS JSONB AS $$
DECLARE
    dashboard_data RECORD;
    result_json JSONB;
BEGIN
    -- Get dashboard data for the patient
    SELECT * INTO dashboard_data 
    FROM vw_patient_account_dashboard
    WHERE patient_id = p_patient_id;
    
    -- If no data found, return null
    IF NOT FOUND THEN
        RETURN NULL;
    END IF;
    
    -- Format the result as JSON with appropriate drill-down queries
    result_json := jsonb_build_object(
        'dashboard_metrics', jsonb_build_array(
            -- Patient Balance
            jsonb_build_object(
                'measure_key', 'patient_balance',
                'patient_id', dashboard_data.patient_id,
                'name', 'Patient Balance',
                'amount_raw', dashboard_data.patient_balance_raw,
                'amount', dashboard_data.patient_balance,
                'type', 'card',
                'details_view_query', 'patient_open_invoice_drill_down', -- Updated query name
                'drill_down_params', jsonb_build_object(
                    'patient_id', dashboard_data.patient_id::text
                )
            ),
            
            -- Insurance Balance
            jsonb_build_object(
                'measure_key', 'insurance_balance',
                'patient_id', dashboard_data.patient_id,
                'name', 'Insurance Balance',
                'amount_raw', dashboard_data.insurance_balance_raw,
                'amount', dashboard_data.insurance_balance,
                'type', 'card',
                'details_view_query', 'insurance_open_invoice_drill_down', -- Updated query name
                'drill_down_params', jsonb_build_object(
                    'patient_id', dashboard_data.patient_id::text
                )
            ),
            
            -- Total Balance
            jsonb_build_object(
                'measure_key', 'total_balance',
                'patient_id', dashboard_data.patient_id,
                'name', 'Total Balance',
                'amount_raw', dashboard_data.total_balance_raw,
                'amount', dashboard_data.total_balance,
                'type', 'card',
                'details_view_query', 'combined_open_invoice_drill_down', -- Updated query name
                'drill_down_params', jsonb_build_object(
                    'patient_id', dashboard_data.patient_id::text
                )
            ),
            
            -- Held Revenue
            jsonb_build_object(
                'measure_key', 'held_revenue',
                'patient_id', dashboard_data.patient_id,
                'name', 'Held Revenue',
                'amount_raw', dashboard_data.held_revenue_raw,
                'amount', dashboard_data.held_revenue,
                'type', 'card',
                'details_view_query', 'invoice_onhold_revenue', -- Updated query name
                'drill_down_params', jsonb_build_object(
                    'patient_id', dashboard_data.patient_id::text
                )
            ),
            
            -- Unbilled Revenue
            jsonb_build_object(
                'measure_key', 'unbilled_revenue',
                'patient_id', dashboard_data.patient_id,
                'name', 'Unbilled Revenue',
                'amount_raw', dashboard_data.unbilled_revenue_raw,
                'amount', dashboard_data.unbilled_revenue,
                'type', 'card',
                'details_view_query', 'patient_pending_revenue', -- Updated query name
                'drill_down_params', jsonb_build_object(
                    'patient_id', dashboard_data.patient_id::text
                )
            ),

            -- Cash Balance
            jsonb_build_object(
                'measure_key', 'cash_balance',
                'patient_id', dashboard_data.patient_id,
                'name', 'Cash Balance',
                'amount_raw', dashboard_data.cash_balance_raw,
                'amount', dashboard_data.cash_balance,
                'type', 'card',
                'details_view_query', 'unapplied_cash_report', -- Updated query name
                'drill_down_params', jsonb_build_object(
                    'patient_id', dashboard_data.patient_id::text
                )
            )
        ),
        
        -- Include total revenue as a separate field for display elsewhere
        'total_revenue', dashboard_data.total_revenue
    );
    
    RETURN result_json;
END;
$$ LANGUAGE plpgsql;
