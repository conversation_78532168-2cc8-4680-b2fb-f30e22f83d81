DO $$ BEGIN
  PERFORM drop_all_function_signatures('get_delivery_ticket_assessment_presets');
END $$;
CREATE OR REPLACE FUNCTION get_delivery_ticket_assessment_presets(
  p_delivery_ticket_id integer
) RETURNS JSONB AS $BODY$
DECLARE
  v_presets jsonb;
  v_assessment_form text;
  v_assessment_id integer;
  v_form_id integer;
  v_form_name text;
  v_results jsonb;
  v_related_rxs integer[];
  -- Add these variables to store array lengths
  v_clinical_count integer;
  v_therapy_count integer;
  v_disease_count integer;
  v_brand_count integer;
BEGIN

    RAISE LOG 'get_delivery_ticket_assessment_presets: p_delivery_ticket_id: %', p_delivery_ticket_id;

    IF p_delivery_ticket_id IS NULL THEN
        INSERT INTO dispensing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Missing delivery ticket ID',
            'Exception in get_delivery_ticket_assessment_presets',
            'FUNCTION',
            current_schema(),
            'form_careplan_delivery_tick',
            jsonb_build_object(
                'function_name', 'get_delivery_ticket_assessment_presets',
                'delivery_tick_id', p_delivery_ticket_id
            )
        );
        RAISE EXCEPTION 'Delivery ticket ID is required to generate assessment presets';
    END IF;

    SELECT
        ARRAY_AGG(DISTINCT grdt.form_careplan_order_rx_fk)
    INTO
        v_related_rxs
    FROM gr_form_careplan_delivery_tick_rx_id_to_careplan_order_rx_id grdt
    WHERE grdt.form_careplan_delivery_tick_fk = p_delivery_ticket_id;

    SELECT
        CASE
            WHEN sfas.form_assessment_fk IS NOT NULL THEN 'assessment'
            WHEN sfon.form_ongoing_fk IS NOT NULL THEN 'ongoing'
            WHEN rxdisp.fill_number > 1 OR rx.is_refill = 'Yes' THEN 'ongoing'
            ELSE 'assessment'
        END as form_name,
        COALESCE(sfas.form_assessment_fk, sfon.form_ongoing_fk) as form_id
    INTO v_assessment_form, v_assessment_id
    FROM form_careplan_delivery_tick dt
    INNER JOIN form_careplan_order_rx_disp rxdisp ON rxdisp.delivery_ticket_id = dt.id AND rxdisp.deleted IS NOT TRUE AND rxdisp.archived IS NOT TRUE
    INNER JOIN form_careplan_order_rx rx ON rx.id = rxdisp.rx_id
    LEFT JOIN sf_form_careplan_delivery_tick_to_ongoing sfon ON sfon.form_careplan_delivery_tick_fk = dt.id AND sfon.delete IS NOT TRUE AND sfon.archive IS NOT TRUE
    LEFT JOIN sf_form_careplan_delivery_tick_to_assessment sfas ON sfas.form_careplan_delivery_tick_fk = dt.id AND sfas.delete IS NOT TRUE AND sfas.archive IS NOT TRUE
    WHERE dt.id = p_delivery_ticket_id
    ORDER BY CASE
        WHEN sfas.form_assessment_fk IS NOT NULL OR sfon.form_ongoing_fk IS NOT NULL THEN 1
        WHEN rxdisp.fill_number = 1 AND COALESCE(rx.is_refill, 'No') = 'No' THEN 2 
        ELSE 3 
    END ASC
    LIMIT 1;

    RAISE LOG 'get_delivery_ticket_assessment_presets: v_assessment_form: %, v_assessment_id: %', v_assessment_form, v_assessment_id;
    v_presets := generate_assessment_presets(p_delivery_ticket_id, 'careplan_delivery_tick'::text, TRUE, TRUE, TRUE, TRUE, TRUE);
    v_presets := jsonb_set(v_presets, '{delivery_ticket_id}', to_jsonb(p_delivery_ticket_id));
    RAISE LOG 'get_delivery_ticket_assessment_presets: v_presets: %', v_presets;

    IF v_assessment_id IS NOT NULL THEN

        -- Get existing values from form
        WITH form_data AS (
            SELECT 
                clinical_1, clinical_2, clinical_3, clinical_4, clinical_5,
                therapy_1, therapy_2, therapy_3, therapy_4, therapy_5,
                disease_1, disease_2, disease_3, disease_4, disease_5,
                brand_1, brand_2, brand_3, brand_4, brand_5
            FROM (
                SELECT format('form_%s', v_assessment_form)::regclass::text as form_table
            ) ft,
            LATERAL (
                SELECT clinical_1, clinical_2, clinical_3, clinical_4, clinical_5,
                therapy_1, therapy_2, therapy_3, therapy_4, therapy_5,
                disease_1, disease_2, disease_3, disease_4, disease_5,
                brand_1, brand_2, brand_3, brand_4, brand_5 FROM form_assessment WHERE id = v_assessment_id AND v_assessment_form = 'assessment'
                UNION ALL
                SELECT clinical_1, clinical_2, clinical_3, clinical_4, clinical_5,
                therapy_1, therapy_2, therapy_3, therapy_4, therapy_5,
                disease_1, disease_2, disease_3, disease_4, disease_5,
                brand_1, brand_2, brand_3, brand_4, brand_5 FROM form_ongoing WHERE id = v_assessment_id AND v_assessment_form = 'ongoing'
            ) f
        ),
        new_assignments AS (
            SELECT
                (SELECT array_agg(val) FROM (
                    SELECT DISTINCT jsonb_array_elements_text(v_presets->'clinical') as val
                    EXCEPT
                    SELECT unnest(ARRAY[clinical_1, clinical_2, clinical_3, clinical_4, clinical_5])
                    FROM form_data
                    WHERE clinical_1 IS NOT NULL
                    LIMIT 5
                ) t) as new_clinical,
                
                (SELECT array_agg(val) FROM (
                    SELECT DISTINCT jsonb_array_elements_text(v_presets->'therapy') as val
                    EXCEPT
                    SELECT unnest(ARRAY[therapy_1, therapy_2, therapy_3, therapy_4, therapy_5])
                    FROM form_data
                    WHERE therapy_1 IS NOT NULL
                    LIMIT 5
                ) t) as new_therapy,
                
                (SELECT array_agg(val) FROM (
                    SELECT DISTINCT jsonb_array_elements_text(v_presets->'disease') as val
                    EXCEPT
                    SELECT unnest(ARRAY[disease_1, disease_2, disease_3, disease_4, disease_5])
                    FROM form_data
                    WHERE disease_1 IS NOT NULL
                    LIMIT 5
                ) t) as new_disease,
                
                (SELECT array_agg(val) FROM (
                    SELECT DISTINCT jsonb_array_elements_text(v_presets->'brand') as val
                    EXCEPT
                    SELECT unnest(ARRAY[brand_1, brand_2, brand_3, brand_4, brand_5])
                    FROM form_data
                    WHERE brand_1 IS NOT NULL
                    LIMIT 5
                ) t) as new_brand
            FROM form_data
        ),
        array_counts AS (
            SELECT
                array_length(new_clinical, 1) as clinical_count,
                array_length(new_therapy, 1) as therapy_count,
                array_length(new_disease, 1) as disease_count,
                array_length(new_brand, 1) as brand_count
            FROM new_assignments
        )
        SELECT 
            json_strip_nulls(json_build_object(
                'clinical_1', CASE WHEN clinical_1 IS NULL AND new_clinical[1] IS NOT NULL THEN new_clinical[1] ELSE clinical_1 END,
                'clinical_2', CASE WHEN clinical_2 IS NULL AND new_clinical[1] IS NOT NULL THEN new_clinical[1] 
                                  WHEN clinical_2 IS NULL AND new_clinical[2] IS NOT NULL THEN new_clinical[2] ELSE clinical_2 END,
                'clinical_3', CASE WHEN clinical_3 IS NULL AND new_clinical[1] IS NOT NULL THEN new_clinical[1]
                                  WHEN clinical_3 IS NULL AND new_clinical[2] IS NOT NULL THEN new_clinical[2]
                                  WHEN clinical_3 IS NULL AND new_clinical[3] IS NOT NULL THEN new_clinical[3] ELSE clinical_3 END,
                'clinical_4', CASE WHEN clinical_4 IS NULL AND new_clinical[1] IS NOT NULL THEN new_clinical[1]
                                  WHEN clinical_4 IS NULL AND new_clinical[2] IS NOT NULL THEN new_clinical[2]
                                  WHEN clinical_4 IS NULL AND new_clinical[3] IS NOT NULL THEN new_clinical[3]
                                  WHEN clinical_4 IS NULL AND new_clinical[4] IS NOT NULL THEN new_clinical[4] ELSE clinical_4 END,
                'clinical_5', CASE WHEN clinical_5 IS NULL AND new_clinical[1] IS NOT NULL THEN new_clinical[1]
                                  WHEN clinical_5 IS NULL AND new_clinical[2] IS NOT NULL THEN new_clinical[2]
                                  WHEN clinical_5 IS NULL AND new_clinical[3] IS NOT NULL THEN new_clinical[3]
                                  WHEN clinical_5 IS NULL AND new_clinical[4] IS NOT NULL THEN new_clinical[4]
                                  WHEN clinical_5 IS NULL AND new_clinical[5] IS NOT NULL THEN new_clinical[5] ELSE clinical_5 END,
                
                'therapy_1', CASE WHEN therapy_1 IS NULL AND new_therapy[1] IS NOT NULL THEN new_therapy[1] ELSE therapy_1 END,
                'therapy_2', CASE WHEN therapy_2 IS NULL AND new_therapy[1] IS NOT NULL THEN new_therapy[1]
                                 WHEN therapy_2 IS NULL AND new_therapy[2] IS NOT NULL THEN new_therapy[2] ELSE therapy_2 END,
                'therapy_3', CASE WHEN therapy_3 IS NULL AND new_therapy[1] IS NOT NULL THEN new_therapy[1]
                                 WHEN therapy_3 IS NULL AND new_therapy[2] IS NOT NULL THEN new_therapy[2]
                                 WHEN therapy_3 IS NULL AND new_therapy[3] IS NOT NULL THEN new_therapy[3] ELSE therapy_3 END,
                'therapy_4', CASE WHEN therapy_4 IS NULL AND new_therapy[1] IS NOT NULL THEN new_therapy[1]
                                 WHEN therapy_4 IS NULL AND new_therapy[2] IS NOT NULL THEN new_therapy[2]
                                 WHEN therapy_4 IS NULL AND new_therapy[3] IS NOT NULL THEN new_therapy[3]
                                 WHEN therapy_4 IS NULL AND new_therapy[4] IS NOT NULL THEN new_therapy[4] ELSE therapy_4 END,
                'therapy_5', CASE WHEN therapy_5 IS NULL AND new_therapy[1] IS NOT NULL THEN new_therapy[1]
                                 WHEN therapy_5 IS NULL AND new_therapy[2] IS NOT NULL THEN new_therapy[2]
                                 WHEN therapy_5 IS NULL AND new_therapy[3] IS NOT NULL THEN new_therapy[3]
                                 WHEN therapy_5 IS NULL AND new_therapy[4] IS NOT NULL THEN new_therapy[4]
                                 WHEN therapy_5 IS NULL AND new_therapy[5] IS NOT NULL THEN new_therapy[5] ELSE therapy_5 END,

                'disease_1', CASE WHEN disease_1 IS NULL AND new_disease[1] IS NOT NULL THEN new_disease[1] ELSE disease_1 END,
                'disease_2', CASE WHEN disease_2 IS NULL AND new_disease[1] IS NOT NULL THEN new_disease[1]
                                 WHEN disease_2 IS NULL AND new_disease[2] IS NOT NULL THEN new_disease[2] ELSE disease_2 END,
                'disease_3', CASE WHEN disease_3 IS NULL AND new_disease[1] IS NOT NULL THEN new_disease[1]
                                 WHEN disease_3 IS NULL AND new_disease[2] IS NOT NULL THEN new_disease[2]
                                 WHEN disease_3 IS NULL AND new_disease[3] IS NOT NULL THEN new_disease[3] ELSE disease_3 END,
                'disease_4', CASE WHEN disease_4 IS NULL AND new_disease[1] IS NOT NULL THEN new_disease[1]
                                 WHEN disease_4 IS NULL AND new_disease[2] IS NOT NULL THEN new_disease[2]
                                 WHEN disease_4 IS NULL AND new_disease[3] IS NOT NULL THEN new_disease[3]
                                 WHEN disease_4 IS NULL AND new_disease[4] IS NOT NULL THEN new_disease[4] ELSE disease_4 END,
                'disease_5', CASE WHEN disease_5 IS NULL AND new_disease[1] IS NOT NULL THEN new_disease[1]
                                 WHEN disease_5 IS NULL AND new_disease[2] IS NOT NULL THEN new_disease[2]
                                 WHEN disease_5 IS NULL AND new_disease[3] IS NOT NULL THEN new_disease[3]
                                 WHEN disease_5 IS NULL AND new_disease[4] IS NOT NULL THEN new_disease[4]
                                 WHEN disease_5 IS NULL AND new_disease[5] IS NOT NULL THEN new_disease[5] ELSE disease_5 END,

                'brand_1', CASE WHEN brand_1 IS NULL AND new_brand[1] IS NOT NULL THEN new_brand[1] ELSE brand_1 END,
                'brand_2', CASE WHEN brand_2 IS NULL AND new_brand[1] IS NOT NULL THEN new_brand[1]
                               WHEN brand_2 IS NULL AND new_brand[2] IS NOT NULL THEN new_brand[2] ELSE brand_2 END,
                'brand_3', CASE WHEN brand_3 IS NULL AND new_brand[1] IS NOT NULL THEN new_brand[1]
                               WHEN brand_3 IS NULL AND new_brand[2] IS NOT NULL THEN new_brand[2]
                               WHEN brand_3 IS NULL AND new_brand[3] IS NOT NULL THEN new_brand[3] ELSE brand_3 END,
                'brand_4', CASE WHEN brand_4 IS NULL AND new_brand[1] IS NOT NULL THEN new_brand[1]
                               WHEN brand_4 IS NULL AND new_brand[2] IS NOT NULL THEN new_brand[2]
                               WHEN brand_4 IS NULL AND new_brand[3] IS NOT NULL THEN new_brand[3]
                               WHEN brand_4 IS NULL AND new_brand[4] IS NOT NULL THEN new_brand[4] ELSE brand_4 END,
                'brand_5', CASE WHEN brand_5 IS NULL AND new_brand[1] IS NOT NULL THEN new_brand[1]
                               WHEN brand_5 IS NULL AND new_brand[2] IS NOT NULL THEN new_brand[2]
                               WHEN brand_5 IS NULL AND new_brand[3] IS NOT NULL THEN new_brand[3]
                               WHEN brand_5 IS NULL AND new_brand[4] IS NOT NULL THEN new_brand[4]
                               WHEN brand_5 IS NULL AND new_brand[5] IS NOT NULL THEN new_brand[5] ELSE brand_5 END
            )),
            ac.clinical_count,
            ac.therapy_count,
            ac.disease_count,
            ac.brand_count
        INTO v_presets, v_clinical_count, v_therapy_count, v_disease_count, v_brand_count
        FROM form_data, new_assignments, array_counts ac;
        
        -- Now we can safely check the counts outside the CTE
        IF v_clinical_count > 5 THEN
            RAISE WARNING 'More than 5 clinical values found';
        END IF;
        IF v_therapy_count > 5 THEN
            RAISE WARNING 'More than 5 therapy values found';
        END IF;
        IF v_disease_count > 5 THEN
            RAISE WARNING 'More than 5 disease values found';
        END IF;
        IF v_brand_count > 5 THEN
            RAISE WARNING 'More than 5 brand values found';
        END IF;
    END IF;

    SELECT row_to_json(t)
    INTO v_results 
    FROM (
        SELECT 
            v_presets as preset,
            v_assessment_form as form,
            v_assessment_id as record
    ) t;

    RETURN v_results;

    EXCEPTION WHEN OTHERS THEN
        -- Log error
        INSERT INTO dispensing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            SQLERRM,
            'Exception in get_delivery_ticket_assessment_presets',
            'FUNCTION',
            current_schema(),
            'form_careplan_delivery_tick',
            jsonb_build_object(
                'function_name', 'get_delivery_ticket_assessment_presets',
                'delivery_tick_id', p_delivery_ticket_id
            )
        );
        
    RAISE WARNING 'Failed to generate delivery ticket assessment presets: %', SQLERRM;
    RETURN NULL;
END;
$BODY$ LANGUAGE plpgsql;