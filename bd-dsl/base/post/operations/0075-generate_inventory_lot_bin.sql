
-- Deploy
DO $$ BEGIN
  PERFORM drop_all_function_signatures('update_inventory_bin');
END $$;
CREATE OR REPLACE FUNCTION update_inventory_bin()
RETURNS TRIGGER AS $$
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Only proceed if site_id is not null
    IF NEW.site_id IS NOT NULL THEN
        -- Insert a new record into inventory_bin if it doesn't exist
        INSERT INTO form_inventory_bin (inventory_id, site_id)
        VALUES (NEW.inventory_id, NEW.site_id)
        ON CONFLICT (inventory_id, site_id) DO NOTHING;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger

CREATE OR REPLACE TRIGGER trg_inventory_lot_after_insert_or_update
    AFTER INSERT OR UPDATE
    ON form_inventory_lot
    FOR EACH ROW
    EXECUTE FUNCTION update_inventory_bin(); 