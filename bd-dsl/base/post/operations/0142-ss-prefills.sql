DROP VIEW IF EXISTS vw_ss_patient_prefill CASCADE;
CREATE OR REPLACE VIEW vw_ss_patient_prefill AS
SELECT
    ss.id as ss_message_id,
    ss.site_id as site_id,
    ss.patient_first_name as firstname,
    ss.patient_last_name as lastname,
    ss.patient_middle_name as middlename,
    ss.physician_id as referrer_id,
    TO_CHAR(ss.patient_dob, 'MM/DD/YYYY') as dob,
    'N/A' as code_status,
    CASE
        WHEN ss.patient_gender = 'M' THEN 'Male'
        WHEN ss.patient_gender = 'F' THEN 'Female'
        ELSE NULL::text
    END as gender,
    ss.patient_ssn as ssn,
    CASE
        WHEN ss.patient_rems IS NOT NULL THEN '[{external_id:"' || ss.patient_mrn || '", comment:"Physician EMR"}, {external_id:"' || ss.patient_rems || '", comment:"REMS ID Authorization Number | ' || ss.rems_authorization_number || '"}]'
        ELSE '[{external_id:"' || ss.patient_mrn || '", comment:"Physician <PERSON>"}]'
    END as external_ids,
    '1' as status_id
FROM form_ss_message ss
WHERE ss.patient_id IS NULL AND ss.patient_first_name IS NOT NULL AND ss.patient_last_name IS NOT NULL AND ss.archived IS NOT TRUE AND ss.deleted IS NOT TRUE AND ss.message_type IN ('NewRx', 'RxRenewalResponse', 'RxChangeResponse');

DROP VIEW IF EXISTS vw_ss_address_prefill;
CREATE OR REPLACE VIEW vw_ss_address_prefill AS
SELECT
    ss.id as ss_message_id,
    ss.patient_id as patient_id,
    ss.patient_first_name as firstname,
    ss.patient_last_name as lastname,
    'Home' as address_type,
    'Yes' as ship_sameas,
    ss.patient_first_name || ' ' || ss.patient_last_name as ship_to,
    ss.patient_home_street_1 as street,
    ss.patient_home_street_2 as street2,
    ss.patient_home_city as city,
    ss.patient_home_state as state_id,
    ss.patient_home_zip as zip
FROM form_ss_message ss
WHERE ss.patient_id IS NOT NULL AND ss.message_type IN ('NewRx', 'RxRenewalResponse', 'RxChangeResponse')
AND ss.patient_home_street_1 IS NOT NULL
AND ss.archived IS NOT TRUE AND ss.deleted IS NOT TRUE;

DROP VIEW IF EXISTS vw_ss_patient_prescriber_prefill;
CREATE OR REPLACE VIEW vw_ss_patient_prescriber_prefill AS
SELECT
    ss.id as ss_message_id,
    ss.patient_id as patient_id,
    ss.physician_id as prescriber_id,
    'Yes' as is_primary,
    'Imported from Surescripts message' as notes
FROM form_ss_message ss
LEFT JOIN form_patient_prescriber pp ON pp.patient_id = ss.patient_id AND pp.physician_id = ss.physician_id
WHERE ss.patient_id IS NOT NULL AND ss.physician_id IS NOT NULL AND ss.message_type IN ('NewRx', 'RxRenewalResponse', 'RxChangeResponse')
AND pp.id IS NULL AND ss.archived IS NOT TRUE AND ss.deleted IS NOT TRUE;

DROP VIEW IF EXISTS vw_ss_patient_diagnosis_prefill;
CREATE OR REPLACE VIEW vw_ss_patient_diagnosis_prefill AS
SELECT
    ss.id as ss_message_id,
    ss.patient_id as patient_id,
    COALESCE(sd.dx_id, sd.dx_code) as diagnosis_code, -- Use dx_code as fallback if dx_id is NULL
    CASE WHEN sd.type = 'Primary' THEN 1 ELSE 2 END as rank
FROM form_ss_message ss
INNER JOIN sf_form_ss_message_to_ss_diagnosis sfd ON sfd.form_ss_message_fk = ss.id
AND sfd.archive IS NOT TRUE AND sfd.delete IS NOT TRUE
INNER JOIN form_ss_diagnosis sd ON sfd.form_ss_diagnosis_fk = sd.id
AND sd.archived IS NOT TRUE AND sd.deleted IS NOT TRUE
-- Only include diagnoses with either dx_id or dx_code
WHERE (sd.dx_id IS NOT NULL OR sd.dx_code IS NOT NULL)
AND ss.patient_id IS NOT NULL
AND ss.message_type IN ('NewRx', 'RxRenewalResponse', 'RxChangeResponse')
AND ss.archived IS NOT TRUE AND ss.deleted IS NOT TRUE;

DROP VIEW IF EXISTS vw_ss_patient_insurance_prefill;
CREATE OR REPLACE VIEW vw_ss_patient_insurance_prefill AS
SELECT
    ss.id as ss_message_id,
    pt.id as patient_id,
    py.id as payer_id,
    pt.mrn as mrn,
    pt.ssn as ssn,
    py.billing_method_id as billing_method_id,
    py.type_id as type_id,
    py.send_blank_person_code as send_blank_person_code,
    py.bin as bin,
    py.pcn as pcn,
    py.is_self_pay as is_self_pay,
    py.always_bill_for_denial as bill_for_denial,
    py.pap_program_id as pap_program_id,
    py.cap_program_id as cap_program_id,
    py.other_program_id as other_program_id,
    CASE
        WHEN sb.payer_level = 'P' THEN 'Primary'
        WHEN sb.payer_level = 'S' THEN 'Secondary'
        WHEN sb.payer_level = 'T' THEN 'Tertiary'
        WHEN sb.payer_level = 'U' THEN 'Other'
        WHEN sb.payer_level = 'PP' THEN 'Other'
        ELSE NULL::text
    END as payer_level,
    'Yes' as active,
    'EA' as patient_id_qualifier,
    pt.mrn as patient_claim_id,
    CASE
        WHEN py.billing_method_id IN ('mm', 'cms1500') OR COALESCE(py.bill_mm_and_ncpdp, 'No') = 'Yes' THEN
            CASE
                WHEN sb.relationship_code = '1' THEN '18'
                WHEN sb.relationship_code = '2' THEN '01'
                WHEN sb.relationship_code = '3' THEN '19'
                WHEN sb.relationship_code = '4' THEN 'G8'
                ELSE NULL::text
            END
        ELSE NULL::text
    END as medical_relationship_id,
    CASE
        WHEN py.billing_method_id IN ('ncpdp') OR COALESCE(py.bill_mm_and_ncpdp, 'No') = 'Yes' THEN sb.relationship_code
        ELSE NULL::text
    END as pharmacy_relationship_id,
    COALESCE(sb.cardholder_id, sb.pbm_member_id) as cardholder_id,
    sb.person_code as person_code,
    sb.group_id as group_number,
    sb.group_name as group_name,
    sb.cardholder_first_name as beneficiary_fname,
    sb.cardholder_last_name as beneficiary_lname,
    sb.payer_phone as insurance_contact_phone,
    ss.patient_medicare as medicare_number,
    ss.patient_medicaid as medicaid_number
FROM form_ss_message ss
INNER JOIN form_patient pt ON pt.id = ss.patient_id
INNER JOIN sf_form_ss_message_to_ss_benefit sfb ON sfb.form_ss_message_fk = ss.id
AND sfb.archive IS NOT TRUE AND sfb.delete IS NOT TRUE
INNER JOIN form_ss_benefit sb ON sfb.form_ss_benefit_fk = sb.id
AND sb.archived IS NOT TRUE AND sb.deleted IS NOT TRUE
LEFT JOIN LATERAL (
    SELECT id FROM form_payer py
    WHERE py.id = sb.payer_id
    UNION ALL
    SELECT id FROM form_payer py
    WHERE sb.bin = py.bin AND sb.pcn = py.pcn AND NOT EXISTS (
        SELECT 1 FROM form_payer py2 WHERE py2.id = sb.payer_id
    )
    UNION ALL
    SELECT id FROM form_payer py
    WHERE py.naic_id::text = sb.naic_id::text AND NOT EXISTS (
        SELECT 1 FROM form_payer py2 WHERE py2.id = sb.payer_id
        UNION ALL
        SELECT 1 FROM form_payer py2 WHERE py2.bin = sb.bin AND py2.pcn = sb.pcn
    )
    UNION ALL
    SELECT id FROM form_payer py
    WHERE py.hp_id::text = sb.hp_id::text AND NOT EXISTS (
        SELECT 1 FROM form_payer py2 WHERE py2.id = sb.payer_id
        UNION ALL
        SELECT 1 FROM form_payer py2 WHERE py2.bin = sb.bin AND py2.pcn = sb.pcn
        UNION ALL
        SELECT 1 FROM form_payer py2 WHERE py2.naic_id::text = sb.naic_id::text
    )
    LIMIT 1
) matched_payer(id) ON true
INNER JOIN form_payer py ON py.id = matched_payer.id
LEFT JOIN form_patient_insurance pi ON pi.patient_id = ss.patient_id AND pi.payer_id = py.id
WHERE ss.patient_id IS NOT NULL AND ss.message_type IN ('NewRx', 'RxRenewalResponse', 'RxChangeResponse')
AND sb.insurance_id IS NULL AND pi.id IS NULL
AND ss.archived IS NOT TRUE AND ss.deleted IS NOT TRUE;

DROP VIEW IF EXISTS vw_ss_patient_order_prefill;
CREATE OR REPLACE VIEW vw_ss_patient_order_prefill AS
SELECT
    ss.id as ss_message_id,
    ss.site_id as site_id,
    pt.id as patient_id,
    cp.careplan_id as careplan_id,
    'Single Prescription' as order_format,
    pt.team_id as team_id,
    pt.referral_source_id as referral_source_id,
    pp.id as prescriber_id,
    ss.physician_id as physician_id,
    st.id as territory_id,
    st.commissioned_sales_rep
FROM form_ss_message ss
INNER JOIN form_patient pt ON pt.id = ss.patient_id
INNER JOIN form_physician ph ON ph.id = ss.physician_id
LEFT JOIN LATERAL (SELECT
    cp.id as careplan_id
    FROM form_careplan cp
    WHERE cp.patient_id = ss.patient_id AND cp.archived IS NOT TRUE AND cp.deleted IS NOT TRUE
    LIMIT 1
) cp ON true
INNER JOIN form_patient_prescriber pp ON pp.patient_id = ss.patient_id AND pp.physician_id = ss.physician_id
LEFT JOIN form_sales_territory st ON st.id = ph.territory_id
WHERE ss.message_type IN ('NewRx', 'RxRenewalResponse', 'RxChangeResponse')
AND ss.archived IS NOT TRUE AND ss.deleted IS NOT TRUE;

DROP VIEW IF EXISTS vw_ss_patient_orderp_item_prefill;
CREATE OR REPLACE VIEW vw_ss_patient_orderp_item_prefill AS
SELECT
    ss.id as ss_message_id,
    ss.site_id as site_id,
    pt.id as patient_id,
    cp.careplan_id as careplan_id,
    'Yes' as is_erx,
    ss.description as ss_description,
    ss.physician_order_id as physician_order_id,
    inv.manufacturer_id as manufacturer_id,
    ss.sig as sig,
    ss.refills as refills,
    ss.day_supply as ss_day_supply,
    ss.daw as ss_daw,
    ss.daw_code_reason as ss_daw_code_reason,
    ss.quantity as ss_quantity,
    ss.quantity_qualifier_id as ss_quantity_qualifier_id,
    ss.compound_dosage_form_id as ss_compound_dosage_form_id,
    inv.id as inventory_id,
    inv.formatted_ndc as formatted_ndc,
    inv.hcpc_code as hcpc_code,
    ss.strength as dose,
    ssuom.unit_id as dose_unit_id,
    inv.route_id as route_id,
    ss.written_date as written_date,
    COALESCE(ss.expiration_date, ss.written_date + INTERVAL '365 days') as expiration_date,
    ss.start_date as start_date,
    COALESCE(ss.expiration_date, ss.written_date + INTERVAL '365 days')  as stop_date,
    1 as next_fill_number,
    'Insurance' as billing_method,
    NULL as auth_flag,
    'Yes' as bv_flag,
    payer_info.pa_id as drug_pa_id,
    payer_info.payer_jsonb_ids as payer_ids,
    ptdx.dx_jsonb_ids as dx_ids,
    CASE
        WHEN ss.do_not_fill IS NOT NULL THEN '4'
        ELSE '1'
    END as status_id,
    'A' as intake_substatus_id,
    CASE
        WHEN ss.do_not_fill = 'Y' THEN 'Do Not Fill per Surescripts Message'
        WHEN ss.do_not_fill = 'H' THEN 'Hold per Surescripts Message'
        WHEN ss.do_not_fill = 'E' THEN 'Do Not Fill - Emergency Oral Called-In per Surescripts Message'
        ELSE NULL::text
    END as onhold_reason,
    inv.therapy_id as therapy_id,
    'Primary' as type_id,
    CASE
        WHEN ss.quantity_qualifier_id = 'CF' THEN 'Compound'
        ELSE inv.rx_template_id
    END as rx_template_id,
    rxt.template_type as template_type,
    CASE
        WHEN ss.message_type = 'RxRenewalResponse' THEN 'Yes'
        ELSE NULL::text
    END as is_refill,
    ogss.refill_rx_id,
    CASE
        WHEN ss.priority_flag = 'X' THEN 'Yes'
        ELSE NULL::text
    END as stat_order,
    ss.note as comments
FROM form_ss_message ss
INNER JOIN form_patient pt ON pt.id = ss.patient_id
LEFT JOIN LATERAL (SELECT
    cp.id as careplan_id
    FROM form_careplan cp
    WHERE cp.patient_id = ss.patient_id AND cp.archived IS NOT TRUE AND cp.deleted IS NOT TRUE
    LIMIT 1
) cp ON true
LEFT JOIN form_inventory inv ON inv.fdb_id = ss.fdb_id
LEFT JOIN form_list_ncpdp_strength_unit_msr ssuom ON ssuom.code = ss.strength_uom_id
LEFT JOIN form_list_rx_template rxt ON rxt.code = inv.rx_template_id
LEFT JOIN LATERAL (
    SELECT
        rx.id as refill_rx_id
    FROM form_ss_message ogss
    INNER JOIN form_careplan_order_rx rx ON rx.rx_no = ogss.pharmacy_rx_no
    WHERE ogss.message_id = ss.related_message_id AND ss.message_type = 'RxRenewalResponse'
) ogss ON true
LEFT JOIN LATERAL (
    SELECT
        jsonb_agg(jsonb_build_object(
            'id', sb.insurance_id,
            'rank', CASE
                        WHEN sb.payer_level = 'P' THEN '1'
                        WHEN sb.payer_level = 'S' THEN '2'
                        WHEN sb.payer_level = 'T' THEN '3'
                        ELSE '4'
                    END
        )) as payer_jsonb_ids,
        first_value(ppa.id) OVER (
            PARTITION BY ss.id -- This PARTITION BY might be problematic in a LATERAL join context if ss.id isn't unique enough for first_value desired logic
            ORDER BY
                CASE
                    WHEN sb.payer_level = 'P' THEN 1
                    WHEN sb.payer_level = 'S' THEN 2
                    WHEN sb.payer_level = 'T' THEN 3
                    ELSE 4
                END
        ) as pa_id
    FROM form_ss_benefit sb
    INNER JOIN sf_form_ss_message_to_ss_benefit sfb ON sfb.form_ss_message_fk = ss.id
    LEFT JOIN form_patient_insurance pi ON pi.patient_id = ss.patient_id AND pi.payer_id = sb.insurance_id
    AND pi.archived IS NOT TRUE AND pi.deleted IS NOT TRUE
    LEFT JOIN form_patient_prior_auth ppa ON ppa.patient_id = ss.patient_id AND pi.id = ppa.insurance_id AND ppa.number = ss.pa_number
    WHERE sfb.form_ss_benefit_fk = sb.id AND sfb.archive IS NOT TRUE AND sfb.delete IS NOT TRUE
    AND sb.archived IS NOT TRUE AND sb.deleted IS NOT TRUE
    GROUP BY ppa.id, sb.payer_level
) payer_info ON true
LEFT JOIN LATERAL (
    SELECT
        jsonb_agg(jsonb_build_object(
            'id', pd.id,
            'rank', CASE WHEN sd.type = 'Primary' THEN '1' ELSE '2' END
        )) as dx_jsonb_ids
    FROM sf_form_ss_message_to_ss_diagnosis sfd
    INNER JOIN form_ss_diagnosis sd ON sfd.form_ss_diagnosis_fk = sd.id
    AND sd.archived IS NOT TRUE AND sd.deleted IS NOT TRUE
    LEFT JOIN form_patient_diagnosis pd ON pd.patient_id = ss.patient_id
    AND (pd.dx_id = sd.dx_id OR (sd.dx_id IS NULL AND pd.dx_id = sd.dx_code))
    AND pd.archived IS NOT TRUE AND pd.deleted IS NOT TRUE
    WHERE sfd.form_ss_message_fk = ss.id
    AND sfd.archive IS NOT TRUE AND sfd.delete IS NOT TRUE
    AND (sd.dx_id IS NOT NULL OR sd.dx_code IS NOT NULL)
) ptdx ON true
WHERE ss.patient_id IS NOT NULL AND ss.message_type IN ('NewRx', 'RxRenewalResponse', 'RxChangeResponse')
AND ss.archived IS NOT TRUE AND ss.deleted IS NOT TRUE;

DROP VIEW IF EXISTS vw_ss_patient_order_rx_cmp_prefill;
CREATE OR REPLACE VIEW vw_ss_patient_order_rx_cmp_prefill AS
SELECT
    ss.id as ss_message_id,
    opi.id::integer as orderp_item_id,
    inv.id::integer as inventory_id,
    ssc.quantity::numeric as quantity,
    ssc.quantity_qualifier_id::text as quantity_qualifier_id,
    ssc.quantity_uom_id::text as quantity_uom_id
FROM form_ss_compound ssc
INNER JOIN sf_form_ss_message_to_ss_compound sfc ON sfc.form_ss_compound_fk = ssc.id
AND sfc.archive IS NOT TRUE AND sfc.delete IS NOT TRUE
INNER JOIN form_ss_message ss ON ss.id = sfc.form_ss_message_fk
INNER JOIN form_careplan_orderp_item opi ON opi.ss_message_id = ss.id
AND opi.archived IS NOT TRUE AND opi.deleted IS NOT TRUE
INNER JOIN form_inventory inv ON inv.fdb_id = ssc.fdb_id AND inv.active ='Yes'
AND inv.archived IS NOT TRUE AND inv.deleted IS NOT TRUE
WHERE opi.physician_order_id IS NOT NULL AND opi.archived IS NOT TRUE AND opi.deleted IS NOT TRUE;

DROP VIEW IF EXISTS vw_ss_available_prefills;
CREATE OR REPLACE VIEW vw_ss_available_prefills AS
SELECT
    ss_message_id,
    'Patient Record'::text as type,
    'patient'::text as form_name,
    json_build_object(
        'site_id', site_id,
        'firstname', firstname,
        'lastname', lastname,
        'middlename', middlename,
        'referrer_id', referrer_id,
        'dob', dob,
        'gender', gender,
        'ssn', ssn,
        'external_ids', external_ids
    ) as prefill_data
FROM vw_ss_patient_prefill
UNION ALL
SELECT
    ss_message_id,
    'Patient Address'::text as type,
    'patient_address'::text as form_name,
    json_build_object(
        'patient_id', patient_id,
        'firstname', firstname,
        'lastname', lastname,
        'address_type', address_type,
        'ship_sameas', ship_sameas,
        'ship_to', ship_to,
        'street', street,
        'street2', street2,
        'city', city,
        'state_id', state_id,
        'zip', zip
    ) as prefill_data
FROM vw_ss_address_prefill
UNION ALL
SELECT
    ss_message_id,
    'Patient Prescriber'::text as type,
    'patient_prescriber'::text as form_name,
    json_build_object(
        'patient_id', patient_id,
        'prescriber_id', prescriber_id,
        'is_primary', is_primary,
        'notes', notes
    ) as prefill_data
FROM vw_ss_patient_prescriber_prefill
UNION ALL
SELECT
    ss_message_id,
    'Patient Diagnosis'::text as type,
    'patient_diagnosis'::text as form_name,
    json_build_object(
        'patient_id', patient_id,
        'diagnosis_code', diagnosis_code,
        'rank', rank
    ) as prefill_data
FROM vw_ss_patient_diagnosis_prefill
UNION ALL
SELECT
    ss_message_id,
    'Patient Insurance'::text as type,
    'patient_insurance'::text as form_name,
    json_build_object(
        'patient_id', patient_id,
        'payer_id', payer_id,
        'mrn', mrn,
        'ssn', ssn,
        'billing_method_id', billing_method_id,
        'type_id', type_id,
        'send_blank_person_code', send_blank_person_code,
        'bin', bin,
        'pcn', pcn,
        'is_self_pay', is_self_pay,
        'bill_for_denial', bill_for_denial,
        'pap_program_id', pap_program_id,
        'cap_program_id', cap_program_id,
        'other_program_id', other_program_id,
        'payer_level', payer_level,
        'active', active,
        'patient_id_qualifier', patient_id_qualifier,
        'patient_claim_id', patient_claim_id,
        'medical_relationship_id', medical_relationship_id,
        'pharmacy_relationship_id', pharmacy_relationship_id,
        'cardholder_id', cardholder_id,
        'person_code', person_code,
        'group_number', group_number,
        'group_name', group_name,
        'beneficiary_fname', beneficiary_fname,
        'beneficiary_lname', beneficiary_lname,
        'insurance_contact_phone', insurance_contact_phone,
        'medicare_number', medicare_number,
        'medicaid_number', medicaid_number
    ) as prefill_data
FROM vw_ss_patient_insurance_prefill
UNION ALL
SELECT
    po.ss_message_id,
    'Order'::text as type,
    'careplan_order'::text as form_name,
    json_build_object(
        'patient_id', po.patient_id,
        'careplan_id', po.careplan_id,
        'site_id', po.site_id,
        'order_format', po.order_format,
        'team_id', po.team_id,
        'referral_source_id', po.referral_source_id,
        'prescriber_id', po.prescriber_id,
        'physician_id', po.physician_id,
        'territory_id', po.territory_id,
        'commissioned_sales_rep', po.commissioned_sales_rep,
        'subform_single_order', (
            SELECT json_agg(
                json_build_object(
                    'ss_message_id', opi.ss_message_id,
                    'site_id', opi.site_id,
                    'patient_id', opi.patient_id,
                    'careplan_id', opi.careplan_id,
                    'is_erx', opi.is_erx,
                    'ss_description', opi.ss_description,
                    'physician_order_id', opi.physician_order_id,
                    'manufacturer_id', opi.manufacturer_id,
                    'sig', opi.sig,
                    'refills', opi.refills,
                    'ss_day_supply', opi.ss_day_supply,
                    'ss_daw', opi.ss_daw,
                    'ss_daw_code_reason', opi.ss_daw_code_reason,
                    'ss_quantity', opi.ss_quantity,
                    'ss_quantity_qualifier_id', opi.ss_quantity_qualifier_id,
                    'ss_compound_dosage_form_id', opi.ss_compound_dosage_form_id,
                    'inventory_id', opi.inventory_id,
                    'formatted_ndc', opi.formatted_ndc,
                    'hcpc_code', opi.hcpc_code,
                    'dose', opi.dose,
                    'dose_unit_id', opi.dose_unit_id,
                    'route_id', opi.route_id,
                    'written_date', opi.written_date,
                    'expiration_date', opi.expiration_date,
                    'start_date', opi.start_date,
                    'stop_date', opi.stop_date,
                    'next_fill_number', opi.next_fill_number,
                    'billing_method', opi.billing_method,
                    'auth_flag', opi.auth_flag,
                    'bv_flag', opi.bv_flag,
                    'drug_pa_id', opi.drug_pa_id,
                    'payer_ids', opi.payer_ids,
                    'dx_ids', opi.dx_ids,
                    'onhold_reason', opi.onhold_reason,
                    'therapy_id', opi.therapy_id,
                    'type_id', opi.type_id,
                    'rx_template_id', opi.rx_template_id,
                    'template_type', opi.template_type,
                    'is_refill', opi.is_refill,
                    'refill_rx_id', opi.refill_rx_id,
                    'stat_order', opi.stat_order,
                    'comments', opi.comments
                )
            )
            FROM vw_ss_patient_orderp_item_prefill opi
            WHERE opi.ss_message_id = po.ss_message_id
        )
    ) as prefill_data
FROM vw_ss_patient_order_prefill po
