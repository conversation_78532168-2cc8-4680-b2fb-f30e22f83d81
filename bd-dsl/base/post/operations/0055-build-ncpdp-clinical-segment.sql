
CREATE OR REPLACE FUNCTION build_clinical_ncpdp_segment(
  p_patient_id integer,
  p_insurance_id integer,
  p_site_id integer,
  p_rx_no text DEFAULT NULL,
  p_order_item_id integer DEFAULT NULL,
  p_orderp_item_id integer DEFAULT NULL,
  p_previous_payer_id integer DEFAULT NULL,
  p_parent_claim_no text DEFAULT NULL
) RETURNS ncpdp_clinical AS $BODY$
DECLARE
  v_start_time timestamp;
  v_execution_time interval;
  v_result ncpdp_clinical;
  v_error_message text;
  v_send_dx_code boolean;
  v_params jsonb;
  v_patient_id integer;
  v_diagnosis_data ncpdp_dx[];
  v_measurement_data ncpdp_measurement[];
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'patient_id', p_patient_id,
    'insurance_id', p_insurance_id,
    'site_id', p_site_id,
    'rx_no', p_rx_no,
    'order_item_id', p_order_item_id,
    'orderp_item_id', p_orderp_item_id,
    'parent_claim_no', p_parent_claim_no
  );

  BEGIN  -- Start exception block
    -- Log function call
    PERFORM log_billing_function(
      'build_clinical_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    -- Validate required parameters
    IF p_patient_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Patient ID is required',
            'Validating required fields in build_clinical_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_clinical_ncpdp_segment',
                'patient_id', p_patient_id,
                'insurance_id', p_insurance_id,
                'site_id', p_site_id
            )
        );
        RAISE EXCEPTION 'Patient ID cannot be null';
    END IF;

    IF p_insurance_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Insurance ID is required',
            'Validating required fields in build_clinical_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_clinical_ncpdp_segment',
                'patient_id', p_patient_id,
                'insurance_id', p_insurance_id,
                'site_id', p_site_id
            )
        );
        RAISE EXCEPTION 'Insurance ID cannot be null';
    END IF;

    IF p_site_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Site ID is required',
            'Validating required fields in build_clinical_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_clinical_ncpdp_segment',
                'patient_id', p_patient_id,
                'insurance_id', p_insurance_id,
                'site_id', p_site_id
            )
        );
        RAISE EXCEPTION 'Site ID cannot be null';
    END IF;

    -- Get insurance settings and claim data once
    WITH insurance_info AS (
      SELECT * FROM get_insurance_claim_settings(p_insurance_id, p_site_id, p_previous_payer_id)
    ),
    parent_claim_dx AS (
      SELECT 
          ncpdpdx.patient_id AS patient_id,
          ncpdpdx.dx_id AS dx_id,
          '02' AS dx_code_qualifier,
          CASE
            WHEN COALESCE(ins.send_dx_period, 'No') = 'Yes' THEN dx.icd_code
            ELSE dx.code
          END AS dx_code
      FROM form_ncpdp pclaim
      INNER JOIN sf_form_ncpdp_to_ncpdp_clinical sfcl ON sfcl.form_ncpdp_fk = pclaim.id 
        AND sfcl.delete IS NOT TRUE 
        AND sfcl.archive IS NOT TRUE
      INNER JOIN form_ncpdp_clinical ncpdc ON ncpdc.id = sfcl.form_ncpdp_clinical_fk 
        AND ncpdc.archived IS NOT TRUE 
        AND ncpdc.deleted IS NOT TRUE
      CROSS JOIN insurance_info ins
      LEFT JOIN sf_form_ncpdp_clinical_to_ncpdp_clinical_dx sfdx ON sfdx.form_ncpdp_clinical_fk = ncpdc.id 
        AND sfdx.archive IS NOT TRUE 
        AND sfdx.delete IS NOT TRUE
      LEFT JOIN form_ncpdp_clinical_dx ncpdpdx ON ncpdpdx.id = sfdx.form_ncpdp_clinical_dx_fk 
        AND ncpdpdx.archived IS NOT TRUE 
        AND ncpdpdx.deleted IS NOT TRUE
      LEFT JOIN form_patient_diagnosis pdx ON pdx.id = ncpdpdx.dx_id 
        AND pdx.archived IS NOT TRUE 
        AND pdx.deleted IS NOT TRUE 
      LEFT JOIN form_list_diagnosis dx ON dx.code = pdx.dx_id 
        AND dx.archived IS NOT TRUE 
        AND dx.deleted IS NOT TRUE
      WHERE pclaim.claim_no = p_parent_claim_no 
        AND p_parent_claim_no IS NOT NULL
    ),
    parent_claim_measurement AS (
      SELECT 
        ncpdp.patient_id AS patient_id,
        msr.measurement_date AS measurement_date,
        msr.measurement_time AS measurement_time,
        msr.measurement_dimension AS measurement_dimension,
        msr.measurement_unit AS measurement_unit,
        msr.measurement_value AS measurement_value
      FROM form_ncpdp pclaim
      INNER JOIN sf_form_ncpdp_to_ncpdp_clinical sfcl ON sfcl.form_ncpdp_fk = pclaim.id 
        AND sfcl.delete IS NOT TRUE 
        AND sfcl.archive IS NOT TRUE
      INNER JOIN form_ncpdp_clinical ncpdc ON ncpdc.id = sfcl.form_ncpdp_clinical_fk 
        AND ncpdc.archived IS NOT TRUE 
        AND ncpdc.deleted IS NOT TRUE
      LEFT JOIN sf_form_ncpdp_clinical_to_ncpdp_clinical_measure sfmsr ON sfmsr.form_ncpdp_clinical_fk = ncpdc.id 
        AND sfmsr.archive IS NOT TRUE 
        AND sfmsr.delete IS NOT TRUE
      LEFT JOIN form_ncpdp_clinical_measure msr ON msr.id = sfmsr.form_ncpdp_clinical_measure_fk 
        AND msr.archived IS NOT TRUE 
        AND msr.deleted IS NOT TRUE
      LEFT JOIN form_ncpdp_clinical ncpdp ON ncpdp.id = sfcl.form_ncpdp_clinical_fk
      WHERE pclaim.claim_no = p_parent_claim_no 
        AND p_parent_claim_no IS NOT NULL
    ),
    diagnosis_info AS (
      SELECT 
          ptdx.patient_id AS patient_id,
          ptdx.id AS dx_id,
          '02' AS dx_code_qualifier,
          CASE 
            WHEN COALESCE(ins.send_dx_period, 'No') = 'Yes' THEN dx.icd_code
            ELSE dx.code
          END AS dx_code
      FROM form_patient pt 
      LEFT JOIN form_careplan_order_rx rx ON rx.rx_no = p_rx_no 
        AND rx.rx_no IS NOT NULL 
        AND rx.deleted IS NOT TRUE 
        AND rx.archived IS NOT TRUE
      LEFT JOIN form_careplan_order co ON co.order_no = rx.order_no 
        AND co.archived IS NOT TRUE 
        AND co.deleted IS NOT TRUE
      LEFT JOIN form_careplan_order_item coi ON coi.id = p_order_item_id 
        AND coi.archived IS NOT TRUE 
        AND coi.deleted IS NOT TRUE 
        AND coi.id IS NOT NULL
      LEFT JOIN form_careplan_orderp_item opi ON opi.id = p_orderp_item_id 
        AND opi.archived IS NOT TRUE 
        AND opi.deleted IS NOT TRUE 
        AND opi.id IS NOT NULL
      CROSS JOIN insurance_info ins
      LEFT JOIN jsonb_array_elements(coalesce_if_empty_json(
          coalesce_if_empty_json(
            coalesce_if_empty_json(coi.dx_ids, opi.dx_ids), 
            co.dx_ids),
          '[]')::jsonb) AS elem ON true
      LEFT JOIN form_patient_diagnosis ptdx ON ptdx.id = (elem->>'id')::int 
        AND ptdx.archived IS NOT TRUE 
        AND ptdx.deleted IS NOT TRUE 
        AND COALESCE(ptdx.active, 'No') = 'Yes'
      LEFT JOIN form_list_diagnosis dx ON dx.code = ptdx.dx_id 
        AND dx.archived IS NOT TRUE 
        AND dx.deleted IS NOT TRUE
      WHERE pt.id = p_patient_id 
        AND pt.archived IS NOT TRUE 
        AND pt.deleted IS NOT TRUE 
        AND ptdx.id IS NOT NULL
      LIMIT 5
    ),
    fallback_diagnosis_info AS (
      SELECT 
          ptdx.patient_id AS patient_id,
          ptdx.id AS dx_id,
          '02' AS dx_code_qualifier,
          CASE 
            WHEN COALESCE(ins.send_dx_period, 'No') = 'Yes' THEN dx.icd_code
            ELSE dx.code
          END AS dx_code
      FROM form_patient pt
      CROSS JOIN insurance_info ins
      LEFT JOIN form_patient_diagnosis ptdx ON ptdx.patient_id = p_patient_id 
        AND ptdx.archived IS NOT TRUE 
        AND ptdx.deleted IS NOT TRUE 
        AND COALESCE(ptdx.active, 'No') = 'Yes'
      LEFT JOIN form_list_diagnosis dx ON dx.code = ptdx.dx_id 
        AND dx.archived IS NOT TRUE 
        AND dx.deleted IS NOT TRUE
      WHERE pt.id = p_patient_id 
        AND pt.archived IS NOT TRUE 
        AND pt.deleted IS NOT TRUE 
        AND ptdx.id IS NOT NULL
      LIMIT 5
    ),
    parent_diagnosis_data AS (
      SELECT ARRAY_AGG(resp::ncpdp_dx) AS subform_diagnosis
      FROM parent_claim_dx resp
    ),
    parent_measurement_data AS (
      SELECT ARRAY_AGG(resp::ncpdp_measurement) AS subform_measurement
      FROM parent_claim_measurement resp
    ),
    diagnosis_data AS (
      SELECT ARRAY_AGG(resp::ncpdp_dx) AS subform_diagnosis
      FROM diagnosis_info resp
    ),
    fallback_diagnosis_data AS (
      SELECT ARRAY_AGG(resp::ncpdp_dx) AS subform_diagnosis
      FROM fallback_diagnosis_info resp
    )
    SELECT
      p_patient_id AS patient_id,
      CASE
        WHEN p_parent_claim_no IS NOT NULL AND array_length(pdd.subform_diagnosis, 1) > 0 THEN 
          pdd.subform_diagnosis
        ELSE 
          COALESCE(dd.subform_diagnosis, fdd.subform_diagnosis, ARRAY[]::ncpdp_dx[])
      END AS diagnosis_data,
      CASE 
        WHEN p_parent_claim_no IS NOT NULL AND array_length(pmd.subform_measurement, 1) > 0 THEN 
          pmd.subform_measurement
        ELSE ARRAY[]::ncpdp_measurement[]
      END AS measurement_data,
      COALESCE(ins.send_dx_code, 'No') = 'Yes' AS send_dx_code
    INTO v_patient_id, v_diagnosis_data, v_measurement_data, v_send_dx_code
    FROM form_patient pt
    LEFT JOIN parent_diagnosis_data pdd ON true
    LEFT JOIN parent_measurement_data pmd ON true
    LEFT JOIN diagnosis_data dd ON true
    LEFT JOIN fallback_diagnosis_data fdd ON true
    CROSS JOIN insurance_info ins
    WHERE pt.id = p_patient_id
      AND pt.archived IS NOT TRUE 
      AND pt.deleted IS NOT TRUE;

    -- Validate we got a result
    IF v_send_dx_code IS FALSE THEN
      RETURN NULL;
    END IF;

    v_result.patient_id := v_patient_id;
    v_result.subform_diagnosis := v_diagnosis_data;
    v_result.subform_measurement := v_measurement_data;

    IF v_patient_id IS NULL AND v_diagnosis_data IS NULL AND v_measurement_data IS NULL THEN
      INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
      ) VALUES (
        'Failed to build clinical segment',
        'Validating result in build_clinical_ncpdp_segment',
        'FUNCTION',
        current_schema(),
        'form_ncpdp',
        jsonb_build_object(
          'function_name', 'build_clinical_ncpdp_segment',
          'patient_id', p_patient_id
        )
      );
      RAISE EXCEPTION 'Failed to build clinical segment for patient_id: %', p_patient_id;
    END IF;

    -- Validate diagnosis codes if required
    IF v_diagnosis_data IS NOT NULL THEN
      IF array_length(v_diagnosis_data, 1) > 5 THEN
        INSERT INTO billing_error_log (
          error_message,
          error_context,
          error_type,
          schema_name,
          table_name,
          additional_details
        ) VALUES (
          'Maximum of 5 diagnosis codes allowed',
          'Validating diagnosis codes in build_clinical_ncpdp_segment',
          'FUNCTION',
          current_schema(),
          'form_ncpdp',
          jsonb_build_object(
            'function_name', 'build_clinical_ncpdp_segment',
            'patient_id', p_patient_id
          )
        );
        RAISE EXCEPTION 'Maximum of 5 diagnosis codes allowed';
      END IF;
    END IF;

    -- Log successful completion
    PERFORM log_billing_function(
      'build_clinical_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    -- Log error
    v_error_message := SQLERRM;
    
    -- Log to billing error log
    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_clinical_ncpdp_segment',
      'FUNCTION',
      current_schema(),
      'form_ncpdp',
      jsonb_build_object(
        'function_name', 'build_clinical_ncpdp_segment',
        'patient_id', p_patient_id
      )
    );

    PERFORM log_billing_function(
      'build_clinical_ncpdp_segment'::tracked_function,
      v_params,
      NULL,
      v_error_message,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;