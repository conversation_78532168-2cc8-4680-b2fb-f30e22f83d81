DO $$ BEGIN
  PERFORM drop_all_function_signatures('sync_invoice_cost');
END $$;
CREATE OR REPLACE FUNCTION sync_invoice_cost()
RETURNS TRIGGER AS $$
DECLARE
    invoice_total NUMERIC;
BEGIN
    -- Log the trigger execution
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Only proceed if we have an invoice number and total_cost changed
    IF (NEW.invoice_no IS NULL OR OLD.total_cost = NEW.total_cost) THEN
        RETURN NEW;
    END IF;

    -- Calculate new invoice total by summing all charge line costs
    SELECT ROUND(COALESCE(SUM(total_cost::numeric), 0), 2) INTO invoice_total
    FROM form_ledger_charge_line
    WHERE invoice_no = NEW.invoice_no
    AND archived IS NOT TRUE
    AND deleted IS NOT TRUE
    AND COALESCE(void, 'No') <> 'Yes';

    PERFORM set_config('clara.prevent_locked_checks', 'on', false);
    -- Update the invoice total cost
    UPDATE form_billing_invoice
    SET total_cost = invoice_total
    WHERE invoice_no = NEW.invoice_no
    AND archived IS NOT TRUE
    AND deleted IS NOT TRUE
    AND COALESCE(void, 'No') <> 'Yes'
    AND COALESCE(zeroed, 'No') <> 'Yes';
    PERFORM set_config('clara.prevent_locked_checks', 'off', false);

    RETURN NEW;

EXCEPTION WHEN OTHERS THEN
    -- Log error and re-raise
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        SQLERRM,
        'Error syncing invoice cost',
        'TRIGGER',
        current_schema(),
        'form_ledger_charge_line',
        jsonb_build_object(
            'function_name', 'sync_invoice_cost',
            'invoice_no', NEW.invoice_no,
            'total_cost', NEW.total_cost
        )
    );
    RAISE;
END;
$$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE TRIGGER trg_sync_invoice_cost
    AFTER UPDATE OF total_cost ON form_ledger_charge_line
    FOR EACH ROW
    EXECUTE FUNCTION sync_invoice_cost();
