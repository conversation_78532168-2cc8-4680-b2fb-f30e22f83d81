-- Main function to generate presets based on input type
DO $$ BEGIN
  PERFORM drop_all_function_signatures('generate_assessment_presets');
END $$;
CREATE OR REPLACE FUNCTION generate_assessment_presets(
    p_source_form_id INTEGER,
    p_source_form TEXT, -- 'careplan_delivery_tick', 'careplan_order', or 'careplan_order_rx'
    p_process_brand BOOLEAN DEFAULT TRUE,
    p_process_route BOOLEAN DEFAULT TRUE,
    p_process_disease BOOLEAN DEFAULT TRUE,
    p_process_therapy BOOLEAN DEFAULT TRUE,
    p_process_clinical BOOLEAN DEFAULT TRUE
) RETURNS JSONB AS $$
DECLARE
    v_patient_id INTEGER;
    v_careplan_id INTEGER;
    v_order_id INTEGER;
    v_related_rxs integer[];
    v_result JSONB := '{}'::JSONB;
    v_therapy_presets JSONB := '{}'::JSONB;
    v_disease_presets JSONB := '{}'::JSONB;
    v_brand_presets JSONB := '{}'::JSONB;
    v_route_preset JSONB := '{}'::JSONB;
    v_clinical_presets JSONB := '{}'::JSONB;
BEGIN
    RAISE LOG 'generate_assessment_presets: source_form: %, source_form_id: %', p_source_form, p_source_form_id;

    -- Get patient_id, careplan_id and potentially order_id directly from the view
    IF p_source_form = 'careplan_delivery_tick' THEN
        SELECT
            ARRAY_AGG(DISTINCT dti.rx_id)
        INTO
            v_related_rxs
        FROM vw_delivery_items dti
        WHERE dti.delivery_ticket_id = p_source_form_id;

        SELECT patient_id, careplan_id INTO v_patient_id, v_careplan_id
        FROM form_careplan_delivery_tick dt
        WHERE id = p_source_form_id
        LIMIT 1;

    ELSIF p_source_form = 'careplan_order' THEN
        SELECT patient_id, careplan_id INTO v_patient_id, v_careplan_id
        FROM vw_careplan_order_wf_presets
        WHERE order_id = p_source_form_id
        LIMIT 1;
        
        v_order_id := p_source_form_id;
    ELSIF p_source_form = 'careplan_order_rx' THEN
        SELECT patient_id, careplan_id, order_id INTO v_patient_id, v_careplan_id, v_order_id
        FROM vw_careplan_order_wf_presets
        WHERE rx_id = p_source_form_id
        LIMIT 1;
    END IF;
    
    -- Base result with patient and careplan IDs
    v_result := jsonb_build_object(
        'patient_id', v_patient_id,
        'careplan_id', v_careplan_id
    );
    
    IF v_order_id IS NOT NULL THEN
        v_result := v_result || jsonb_build_object('order_id', v_order_id);
    END IF;
    
    IF p_process_therapy THEN
        -- Generate therapy presets
        v_therapy_presets := generate_therapy_presets(p_source_form_id, p_source_form, v_related_rxs);
        v_result := v_result || v_therapy_presets;
    END IF;

    IF p_process_disease THEN
        -- Generate disease presets
        v_disease_presets := generate_disease_presets(p_source_form_id, p_source_form, v_related_rxs);
        v_result := v_result || v_disease_presets;
    END IF;
    
    IF p_process_brand THEN
        -- Generate brand presets
        v_brand_presets := generate_brand_presets(p_source_form_id, p_source_form, v_related_rxs);
        v_result := v_result || v_brand_presets;
    END IF;

    IF p_process_route THEN
        -- Generate route preset
        v_route_preset := generate_route_preset(p_source_form_id, p_source_form, v_related_rxs);
        v_result := v_result || v_route_preset;
    END IF;
    
    IF p_process_clinical THEN
        -- Generate clinical assessment presets
        v_clinical_presets := generate_clinical_presets(p_source_form_id, p_source_form, v_patient_id, v_related_rxs);
        v_result := v_result || v_clinical_presets;
    END IF;

    RAISE LOG 'generate_assessment_presets: v_result: %', v_result;

    RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- Function to generate clinical assessment presets
DO $$ BEGIN
  PERFORM drop_all_function_signatures('generate_clinical_presets');
END $$;
CREATE OR REPLACE FUNCTION generate_clinical_presets(
    p_source_form_id INTEGER,
    p_source_form TEXT, -- 'careplan_delivery_tick', 'careplan_order', or 'careplan_order_rx'
    p_patient_id INTEGER,
    p_related_rxs integer[]
) RETURNS JSONB AS $$
DECLARE
    v_result JSONB := '{}'::JSONB;
    v_clinical_rules RECORD;
    v_presets_record RECORD;
    v_assessment_codes TEXT[] := '{}'::TEXT[];
    v_current_date TIMESTAMP;
BEGIN
    -- Get current date for cadence calculations
    v_current_date := NOW();

    RAISE LOG 'generate_clinical_presets: p_source_form: %, p_source_form_id: %, p_patient_id: %', p_source_form, p_source_form_id, p_patient_id;
    
    -- Get all active clinical assessment rules
    FOR v_clinical_rules IN (
        SELECT * FROM form_list_clinical_asmt_rule
        WHERE COALESCE(active, 'No') = 'Yes'
    ) LOOP
        -- Check if any preset record matches this rule
        FOR v_presets_record IN (
            SELECT * FROM vw_careplan_order_wf_presets
            WHERE (
                (p_source_form = 'careplan_delivery_tick' AND rx_id = ANY(p_related_rxs)) OR
                (p_source_form = 'careplan_order' AND order_id = p_source_form_id) OR
                (p_source_form = 'careplan_order_rx' AND rx_id = p_source_form_id)
            )
        ) LOOP
            -- Check therapy match
            IF v_clinical_rules.therapy_id IS NOT NULL AND 
               (v_presets_record.therapy_id IS NULL OR v_presets_record.therapy_id != v_clinical_rules.therapy_id) THEN
                CONTINUE;
            END IF;
            
            -- Check diagnosis match
            IF v_clinical_rules.dx_id IS NOT NULL AND 
               (v_presets_record.dx_id IS NULL OR v_presets_record.dx_id != v_clinical_rules.dx_id) THEN
                CONTINUE;
            END IF;
            
            -- Check route match
            IF v_clinical_rules.route_id IS NOT NULL AND 
               (v_presets_record.route_id IS NULL OR v_presets_record.route_id != v_clinical_rules.route_id) THEN
                CONTINUE;
            END IF;
            
            -- Check brand match
            IF v_clinical_rules.brand_name_id IS NOT NULL AND 
               (v_presets_record.brand_name_id IS NULL OR v_presets_record.brand_name_id != v_clinical_rules.brand_name_id) THEN
                CONTINUE;
            END IF;
            
            -- Check if a clinical assessment form exists and if cadence days have passed
            DECLARE
                v_form_exists BOOLEAN := FALSE;
                v_last_assessment TIMESTAMP;
                v_form_table TEXT := 'form_clinical_' || v_clinical_rules.clinical_assmt_id;
                v_query TEXT;
            BEGIN
                -- Check if the form table exists
                IF EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_name = v_form_table
                ) THEN
                    -- Query the most recent assessment
                    v_query := 'SELECT created_on FROM ' || v_form_table || 
                               ' WHERE patient_id = $1 ORDER BY created_on DESC LIMIT 1';
                    
                    EXECUTE v_query INTO v_last_assessment USING p_patient_id;
                    
                    IF v_last_assessment IS NOT NULL THEN
                        v_form_exists := TRUE;
                        
                        -- If cadence_days is 0, don't include this assessment
                        IF v_clinical_rules.cadence_days = 0 THEN
                            CONTINUE;
                        END IF;
                        
                        -- Check if enough days have passed since the last assessment
                        IF (v_current_date - v_last_assessment) < (v_clinical_rules.cadence_days * INTERVAL '1 day') THEN
                            CONTINUE;
                        END IF;
                    END IF;
                END IF;
                
                -- If we reach here, either the form doesn't exist or enough days have passed
                v_assessment_codes := array_append(v_assessment_codes, v_clinical_rules.clinical_assmt_id);
                EXIT; -- Exit the presets_record loop once we find a match
            END;
        END LOOP;
    END LOOP;
    
    -- Remove duplicates and limit to 5
    v_assessment_codes := ARRAY(
        SELECT DISTINCT unnest 
        FROM unnest(v_assessment_codes) 
        ORDER BY unnest 
        LIMIT 5
    );
    
    -- Build the result JSON with clinical_1 through clinical_5
    -- Check if v_assessment_codes is not empty before using array_length
    IF v_assessment_codes IS NOT NULL AND array_length(v_assessment_codes, 1) IS NOT NULL THEN
        FOR i IN 1..array_length(v_assessment_codes, 1) LOOP
            v_result := v_result || jsonb_build_object('clinical_' || i, v_assessment_codes[i]);
        END LOOP;
    END IF;
    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- Function to generate therapy presets
DO $$ BEGIN
  PERFORM drop_all_function_signatures('generate_therapy_presets');
END $$;
CREATE OR REPLACE FUNCTION generate_therapy_presets(
    p_source_form_id INTEGER,
    p_source_form TEXT, -- 'careplan_delivery_tick', 'careplan_order', or 'careplan_order_rx'
    p_related_rxs integer[]
) RETURNS JSONB AS $$
DECLARE
    v_result JSONB := '{}'::JSONB;
    v_presets_record RECORD;
    v_therapy_ids TEXT[];
    v_therapy_presets TEXT[] := '{}'::TEXT[];
    v_therapy_rec RECORD;
    v_found_match BOOLEAN;
    v_therapy_id text;
    v_rule_rec RECORD;
BEGIN
    RAISE LOG 'generate_therapy_presets: p_source_form: %, p_source_form_id: %, p_related_rxs: %', p_source_form, p_source_form_id, p_related_rxs;

    -- Get therapy IDs from the view based on source type
    IF p_source_form = 'careplan_delivery_tick' THEN
        SELECT array_agg(DISTINCT therapy_id) INTO v_therapy_ids
        FROM vw_careplan_order_wf_presets
        WHERE rx_id = ANY(p_related_rxs)
        AND therapy_id IS NOT NULL;
    ELSIF p_source_form = 'careplan_order' THEN
        SELECT array_agg(DISTINCT therapy_id) INTO v_therapy_ids
        FROM vw_careplan_order_wf_presets
        WHERE order_id = p_source_form_id
        AND therapy_id IS NOT NULL;
    ELSIF p_source_form = 'careplan_order_rx' THEN
        SELECT array_agg(DISTINCT therapy_id) INTO v_therapy_ids
        FROM vw_careplan_order_wf_presets
        WHERE rx_id = p_source_form_id
        AND therapy_id IS NOT NULL;
    END IF;

    -- If no therapy IDs found, return empty result
    IF v_therapy_ids IS NULL OR array_length(v_therapy_ids, 1) = 0 THEN
        RETURN v_result;
    END IF;

    -- Process each therapy ID
    FOREACH v_therapy_id IN ARRAY v_therapy_ids
    LOOP
        -- Get therapy record
        SELECT * INTO v_therapy_rec 
        FROM form_list_therapy 
        WHERE code = v_therapy_id;

        -- Skip if therapy record not found
        IF v_therapy_rec.code IS NULL THEN
            CONTINUE;
        END IF;

        -- Check if therapy has associated rules via the gerund table
        v_found_match := FALSE;

        -- For each therapy rule through the gerund table
        FOR v_rule_rec IN (
            SELECT r.*
            FROM sf_form_list_therapy_to_list_therapy_asmt_rule g
            JOIN form_list_therapy_asmt_rule r ON g.form_list_therapy_asmt_rule_fk = r.id
            WHERE g.form_list_therapy_fk = v_therapy_rec.id
            AND g.delete IS NOT TRUE
            AND g.archive IS NOT TRUE
        ) LOOP
            -- Check if any preset record matches this rule
            FOR v_presets_record IN (
                SELECT * FROM vw_careplan_order_wf_presets 
                WHERE therapy_id = v_therapy_id
                AND (
                    (p_source_form = 'careplan_delivery_tick' AND rx_id = ANY(p_related_rxs)) OR
                    (p_source_form = 'careplan_order' AND order_id = p_source_form_id) OR
                    (p_source_form = 'careplan_order_rx' AND rx_id = p_source_form_id)
                )
            ) LOOP
                -- Check diagnosis match if rule has dx_id
                IF v_rule_rec.dx_id IS NOT NULL AND 
                   (v_presets_record.dx_id IS NULL OR v_presets_record.dx_id != v_rule_rec.dx_id) THEN
                    CONTINUE;
                END IF;

                -- Check route match if rule has route_id
                IF v_rule_rec.route_id IS NOT NULL AND 
                   (v_presets_record.route_id IS NULL OR v_presets_record.route_id != v_rule_rec.route_id) THEN
                    CONTINUE;
                END IF;

                -- Check brand match if rule has brand_name_id
                IF v_rule_rec.brand_name_id IS NOT NULL AND 
                   (v_presets_record.brand_name_id IS NULL OR v_presets_record.brand_name_id != v_rule_rec.brand_name_id) THEN
                    CONTINUE;
                END IF;

                -- All checks passed, this rule matches
                v_found_match := TRUE;
                v_therapy_presets := array_append(v_therapy_presets, v_rule_rec.code);
                EXIT; -- Exit the presets_record loop
            END LOOP;

            IF v_found_match THEN
                EXIT; -- Exit the rule loop
            END IF;
        END LOOP;

        -- If no rule matched, use the original therapy code
        IF NOT v_found_match THEN
            v_therapy_presets := array_append(v_therapy_presets, v_therapy_rec.code);
        END IF;
    END LOOP;

    -- Remove duplicates and get top 5 unique therapy presets
    v_therapy_presets := ARRAY(
        SELECT DISTINCT unnest 
        FROM unnest(v_therapy_presets) 
        ORDER BY unnest 
        LIMIT 5
    );

    -- Build the result JSON with therapy_1 through therapy_5
    -- Add null check before using array_length
    IF v_therapy_presets IS NOT NULL AND array_length(v_therapy_presets, 1) IS NOT NULL THEN
        FOR i IN 1..array_length(v_therapy_presets, 1) LOOP
            v_result := v_result || jsonb_build_object('therapy_' || i, v_therapy_presets[i]);
        END LOOP;
    END IF;

    RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- Function to generate disease presets
DO $$ BEGIN
  PERFORM drop_all_function_signatures('generate_disease_presets');
END $$;
CREATE OR REPLACE FUNCTION generate_disease_presets(
    p_source_form_id INTEGER,
    p_source_form TEXT, -- 'careplan_delivery_tick', 'careplan_order', or 'careplan_order_rx'
    p_related_rxs integer[]
) RETURNS JSONB AS $$
DECLARE
    v_result JSONB := '{}'::JSONB;
    v_dx_codes TEXT[];
    v_dx_ids integer[];
    v_assessment_codes TEXT[];
BEGIN
    RAISE LOG 'generate_disease_presets: p_source_form: %, p_source_form_id: %', p_source_form, p_source_form_id;
    
    -- Get dx_codes from the view based on source type
    IF p_source_form = 'careplan_delivery_tick' THEN
        SELECT array_agg(DISTINCT dx_id) INTO v_dx_codes
        FROM vw_careplan_order_wf_presets
        WHERE rx_id = ANY(p_related_rxs)
        AND dx_id IS NOT NULL;
    ELSIF p_source_form = 'careplan_order' THEN
        SELECT array_agg(DISTINCT dx_id) INTO v_dx_codes
        FROM vw_careplan_order_wf_presets
        WHERE order_id = p_source_form_id
        AND dx_id IS NOT NULL;
    ELSIF p_source_form = 'careplan_order_rx' THEN
        SELECT array_agg(DISTINCT dx_id) INTO v_dx_codes
        FROM vw_careplan_order_wf_presets
        WHERE rx_id = p_source_form_id
        AND dx_id IS NOT NULL;
    END IF;
    
    -- If no dx codes found, return empty result
    IF v_dx_codes IS NULL OR array_length(v_dx_codes, 1) = 0 THEN
        RETURN v_result;
    END IF;
    
    FOR i IN 1..array_length(v_dx_codes, 1) LOOP
        v_result := v_result || jsonb_build_object('dx_' || i, v_dx_codes[i]);
    END LOOP;

    -- Get assessment codes mapped to these diagnosis codes
    SELECT array_agg(DISTINCT dam.assessment_code::text) INTO v_assessment_codes
    FROM form_list_dx_asmt_map dam
    INNER JOIN gr_form_list_dx_asmt_map_dx_id_to_list_diagnosis_id sfdx
    ON sfdx.form_list_dx_asmt_map_fk = dam.id
    INNER JOIN form_list_diagnosis dx ON dx.code = sfdx.form_list_diagnosis_fk
    WHERE dx.code::text = ANY(v_dx_codes)
    AND COALESCE(dam.active, 'No') = 'Yes'
    GROUP BY dam.assessment_code, dam.id
    ORDER BY dam.id
    LIMIT 5;
    
    -- If no assessment codes found, return empty result
    IF v_assessment_codes IS NULL OR array_length(v_assessment_codes, 1) = 0 THEN
        RETURN v_result;
    END IF;
    
    -- Build the result JSON with disease_1 through disease_5
    -- Add null check before using array_length
    IF v_assessment_codes IS NOT NULL AND array_length(v_assessment_codes, 1) IS NOT NULL THEN
        FOR i IN 1..array_length(v_assessment_codes, 1) LOOP
            v_result := v_result || jsonb_build_object('disease_' || i, v_assessment_codes[i]);
        END LOOP;
    END IF;
    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- Function to generate brand presets
DO $$ BEGIN
  PERFORM drop_all_function_signatures('generate_brand_presets');
END $$;
CREATE OR REPLACE FUNCTION generate_brand_presets(
    p_source_form_id INTEGER,
    p_source_form TEXT, -- 'careplan_delivery_tick', 'careplan_order', or 'careplan_order_rx'
    p_related_rxs integer[]
) RETURNS JSONB AS $$
DECLARE
    v_result JSONB := '{}'::JSONB;
    v_brand_ids TEXT[];
    v_assessment_codes TEXT[];
BEGIN
    RAISE LOG 'generate_brand_presets: p_source_form: %, p_source_form_id: %', p_source_form, p_source_form_id;
    -- Get brand_ids from the view based on source type
    IF p_source_form = 'careplan_delivery_tick' THEN
        SELECT array_agg(DISTINCT brand_name_id) INTO v_brand_ids
        FROM vw_careplan_order_wf_presets
        WHERE rx_id = ANY(p_related_rxs)
        AND brand_name_id IS NOT NULL;
    ELSIF p_source_form = 'careplan_order' THEN
        SELECT array_agg(DISTINCT brand_name_id) INTO v_brand_ids
        FROM vw_careplan_order_wf_presets
        WHERE order_id = p_source_form_id
        AND brand_name_id IS NOT NULL;
    ELSIF p_source_form = 'careplan_order_rx' THEN
        SELECT array_agg(DISTINCT brand_name_id) INTO v_brand_ids
        FROM vw_careplan_order_wf_presets
        WHERE rx_id = p_source_form_id
        AND brand_name_id IS NOT NULL;
    END IF;
    
    -- If no brand IDs found, return empty result
    IF v_brand_ids IS NULL OR array_length(v_brand_ids, 1) = 0 THEN
        RETURN v_result;
    END IF;
    
    -- Get assessment codes mapped to these brand IDs
    SELECT array_agg(DISTINCT bam.assessment_code) INTO v_assessment_codes
    FROM form_list_brand_asmt_map bam
    INNER JOIN gr_form_list_brand_asmt_map_bname_id_to_list_fdb_drug_brand_id sfbn
    ON sfbn.form_list_brand_asmt_map_fk = bam.id
    INNER JOIN form_list_fdb_drug_brand b ON b.code = sfbn.form_list_fdb_drug_brand_fk
    WHERE b.code = ANY(v_brand_ids) AND COALESCE(bam.active, 'No') = 'Yes'
    GROUP BY bam.id
    ORDER BY bam.id
    LIMIT 5;

    -- If no assessment codes found, return empty result
    IF v_assessment_codes IS NULL OR array_length(v_assessment_codes, 1) = 0 THEN
        RETURN v_result;
    END IF;
    
    -- Build the result JSON with brand_1 through brand_5
    -- Add null check before using array_length
    IF v_assessment_codes IS NOT NULL AND array_length(v_assessment_codes, 1) IS NOT NULL THEN
        FOR i IN 1..array_length(v_assessment_codes, 1) LOOP
            v_result := v_result || jsonb_build_object('brand_' || i, v_assessment_codes[i]);
        END LOOP;
    END IF;
    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- Function to generate route preset
DO $$ BEGIN
  PERFORM drop_all_function_signatures('generate_route_preset');
END $$;
CREATE OR REPLACE FUNCTION generate_route_preset(
    p_source_form_id INTEGER,
    p_source_form TEXT, -- 'careplan_delivery_tick', 'careplan_order', or 'careplan_order_rx'
    p_related_rxs integer[]
) RETURNS JSONB AS $$
DECLARE
    v_result JSONB := '{}'::JSONB;
    v_route_id TEXT;
BEGIN
    RAISE LOG 'generate_route_preset: p_source_form: %, p_source_form_id: %', p_source_form, p_source_form_id;
    
    -- Get primary route_id from the view based on source type
    IF p_source_form = 'careplan_delivery_tick' THEN
        -- For delivery ticket, get first non-null route_id
        SELECT route_id INTO v_route_id
        FROM vw_careplan_order_wf_presets
        WHERE rx_id = ANY(p_related_rxs)
        AND route_id IS NOT NULL
        LIMIT 1;
    ELSIF p_source_form = 'careplan_order' THEN
        -- For order, get first non-null route_id
        SELECT route_id INTO v_route_id
        FROM vw_careplan_order_wf_presets
        WHERE order_id = p_source_form_id
        AND route_id IS NOT NULL
        LIMIT 1;
    ELSIF p_source_form = 'careplan_order_rx' THEN
        -- For rx, get route_id directly
        SELECT route_id INTO v_route_id
        FROM vw_careplan_order_wf_presets
        WHERE rx_id = p_source_form_id
        AND route_id IS NOT NULL
        LIMIT 1;
    END IF;

    -- If route_id found, add it to the result
    IF v_route_id IS NOT NULL THEN
        v_result := jsonb_build_object('route_id', v_route_id);
    END IF;
    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- Simple test function to validate the preset generation
DO $$ BEGIN
  PERFORM drop_all_function_signatures('test_generate_presets');
END $$;
CREATE OR REPLACE FUNCTION test_generate_presets(
    p_source_form_id INTEGER,
    p_source_form TEXT -- 'careplan_delivery_tick', 'careplan_order', or 'careplan_order_rx'
) RETURNS TEXT AS $$
DECLARE
    v_presets JSONB;
BEGIN
    RAISE LOG 'test_generate_presets: p_source_form: %, p_source_form_id: %', p_source_form, p_source_form_id;
    v_presets := generate_assessment_presets(p_source_form_id, p_source_form);
    RAISE LOG 'test_generate_presets: v_presets: %', v_presets;
    RETURN jsonb_pretty(v_presets);
END;
$$ LANGUAGE plpgsql;