
CREATE OR REPLACE FUNCTION update_charge_line_status_on_invoice_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if the invoice is voided or zeroed
    IF COALESCE(NEW.void, 'No') = 'Yes' OR COALESCE(NEW.zeroed, 'No') = 'Yes' THEN
        -- Update charge lines linked through delivery ticket to match void/zeroed status
        UPDATE form_ledger_charge_line lcl
        SET 
            void = CASE WHEN COALESCE(NEW.void, 'No') = 'Yes' THEN 'Yes' ELSE lcl.void END,
            zeroed = CASE WHEN COALESCE(NEW.zeroed, 'No') = 'Yes' THEN 'Yes' ELSE lcl.zeroed END
        FROM form_careplan_delivery_tick dt
        WHERE dt.id = NEW.delivery_ticket_id
        AND lcl.ticket_no = dt.ticket_no
        AND lcl.archived IS NOT TRUE
        AND lcl.deleted IS NOT TRUE;
    END IF;

    -- Update delivery ticket status based on charge line conditions
    UPDATE form_careplan_delivery_tick dt
    SET status = 
        CASE 
            WHEN EXISTS (
                SELECT 1 
                FROM form_ledger_charge_line lcl2
                WHERE lcl2.ticket_no = dt.ticket_no
                AND lcl2.invoice_no IS NOT NULL
                AND COALESCE(lcl2.void, 'No') <> 'Yes'
                AND COALESCE(lcl2.zeroed, 'No') <> 'Yes'
                AND lcl2.archived IS NOT TRUE
                AND lcl2.deleted IS NOT TRUE
            ) THEN 'billed'
            ELSE 'ready_to_bill'
        END
    FROM form_billing_invoice bi
    WHERE bi.delivery_ticket_id = dt.id
    AND bi.id = NEW.id
    AND dt.archived IS NOT TRUE
    AND dt.deleted IS NOT TRUE;

    RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    -- Log error
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        SQLERRM,
        'Exception in update_charge_line_status_on_invoice_change',
        'FUNCTION',
        current_schema(),
        'form_billing_invoice',
        jsonb_build_object(
            'function_name', 'update_charge_line_status_on_invoice_change',
            'invoice_id', NEW.id
        )
    );
    RAISE WARNING 'Failed to update charge line status on invoice change: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger for form_billing_invoice
DROP TRIGGER IF EXISTS trg_update_charge_line_status ON form_billing_invoice;
CREATE TRIGGER trg_update_charge_line_status
    AFTER UPDATE ON form_billing_invoice
    FOR EACH ROW
    WHEN (OLD.void IS DISTINCT FROM NEW.void OR OLD.zeroed IS DISTINCT FROM NEW.zeroed)
    EXECUTE FUNCTION update_charge_line_status_on_invoice_change();

CREATE OR REPLACE FUNCTION update_delivery_ticket_status_on_charge_line_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Update delivery ticket status based on charge line conditions
    UPDATE form_careplan_delivery_tick dt
    SET status = 
        CASE 
            WHEN EXISTS (
                SELECT 1 
                FROM form_ledger_charge_line lcl
                WHERE lcl.ticket_no = dt.ticket_no
                AND lcl.invoice_no IS NOT NULL
                AND COALESCE(lcl.void, 'No') <> 'Yes'
                AND COALESCE(lcl.zeroed, 'No') <> 'Yes'
                AND lcl.archived IS NOT TRUE
                AND lcl.deleted IS NOT TRUE
            ) THEN 'billed'
            ELSE 'ready_to_bill'
        END
    WHERE dt.ticket_no = NEW.ticket_no
    AND COALESCE(dt.void, 'No') <> 'Yes'
    AND dt.archived IS NOT TRUE
    AND dt.deleted IS NOT TRUE;

    RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    -- Log error
    INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
    ) VALUES (
        SQLERRM,
        'Exception in update_delivery_ticket_status_on_charge_line_change',
        'FUNCTION',
        current_schema(),
        'form_ledger_charge_line',
        jsonb_build_object(
            'function_name', 'update_delivery_ticket_status_on_charge_line_change',
            'charge_line_id', NEW.id
        )
    );
    RAISE WARNING 'Failed to update delivery ticket status on charge line change: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger for form_ledger_charge_line
DROP TRIGGER IF EXISTS trg_update_delivery_ticket_status ON form_ledger_charge_line;
CREATE TRIGGER trg_update_delivery_ticket_status
    AFTER UPDATE ON form_ledger_charge_line
    FOR EACH ROW
    WHEN (OLD.void IS DISTINCT FROM NEW.void OR OLD.zeroed IS DISTINCT FROM NEW.zeroed)
    EXECUTE FUNCTION update_delivery_ticket_status_on_charge_line_change();
