
CREATE OR REPLACE FUNCTION get_claim_hierarchy(root_claim TEXT)
RETURNS TABLE (
    insurance_id integer,
    payer_id integer,
    claim_no text,
    internal_control_number text,
    other_id_qualifier text,
    other_id text,
    other_coverage_type text,
    payer_coverage_depth integer,
    ignore_benefits boolean,
    depth integer,
    status text,
    created_date timestamp,
    ing_cst_sub numeric,
    gross_amount_due numeric,
    disp_fee_sub numeric,
    pro_svc_fee_sub numeric,
    u_and_c_charge numeric,
    pt_pd_amt_sub numeric,
    incv_amt_sub numeric,
    other_date text,
    ing_cst_paid numeric,
    total_paid numeric,
    pt_pay_amt numeric,
    incv_amt_paid numeric,
    disp_fee_paid numeric,
    prof_svc_paid numeric,
    amt_apld_ded numeric,
    amt_exd_ben_max numeric,
    coinsur_amt numeric,
    pro_fee_amt numeric,
    amt_attr_prov_sel numeric,
    amt_attr_prod_sel numeric,
    amt_attr_nonformulary numeric,
    amt_attr_brd_nonformulary numeric,
    amt_coverage_gap numeric,
    transaction_response_status text,
    reject_codes reject_code_type[],
    oth_amt_paid oth_amt_type[],
    bft_amt bft_amt_type[]
) AS $BODY$
DECLARE
    v_start_time timestamp;
    v_execution_time interval;
    v_error_message text;
    v_params jsonb;
    found_cycles integer;
    cycle_detail text;
BEGIN
    -- Record start time
    v_start_time := clock_timestamp();

    -- Build parameters JSON for logging
    v_params := jsonb_build_object(
        'root_claim', root_claim
    );

    BEGIN  -- Start exception block
        -- Log function call
        PERFORM log_billing_function(
            'get_claim_hierarchy'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            NULL::text,
            clock_timestamp() - v_start_time
        );

        -- Validate input
        IF root_claim IS NULL THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Root Claim # cannot be null',
                'Validating required fields in get_claim_hierarchy',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                jsonb_build_object(
                    'function_name', 'get_claim_hierarchy',
                    'root_claim', root_claim
                )
            );
            RAISE EXCEPTION 'Root Claim # cannot be null';
        END IF;

        -- Check for circular references
        WITH RECURSIVE cycle_check AS (
            -- Base case: start with root claim
            SELECT 
                ncpdp.claim_no::text as claim_no,
                ncpdp.parent_claim_no::text as parent_claim_no,
                ARRAY[ncpdp.claim_no::text] as path,
                0 as depth
            FROM form_ncpdp ncpdp
            WHERE ncpdp.claim_no = root_claim
            
            UNION ALL
            
            -- Recursive case: follow parent links
            SELECT 
                f.claim_no::text as claim_no,
                f.parent_claim_no::text as parent_claim_no,
                c.path || f.claim_no::text,  -- Add current claim to path
                c.depth + 1
            FROM form_ncpdp f
            INNER JOIN cycle_check c ON f.claim_no = c.parent_claim_no
            WHERE NOT f.claim_no = ANY(c.path)  -- Stop if we've seen this claim before
                AND c.depth < 50  -- Safety limit
        )
        -- Check if any claim appears twice in a path
        SELECT count(*) INTO strict found_cycles
        FROM (
            SELECT ck.claim_no, count(*) 
            FROM cycle_check ck
            GROUP BY ck.claim_no 
            HAVING count(*) > 1
        ) dupes;

        IF found_cycles > 0 THEN
            -- Get the cyclic path for error message
            WITH cycle_path AS (
                SELECT string_agg(ck.claim_no, ' -> ') as cycle
                FROM (
                    SELECT ck.claim_no 
                    FROM cycle_check ck
                    GROUP BY ck.claim_no 
                    HAVING count(*) > 1
                    ORDER BY min(ck.depth)
                ) cycles
            )
            SELECT cycle INTO strict cycle_detail FROM cycle_path;
            
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Circular reference detected in claim hierarchy',
                'Validating claim hierarchy in build_cob_claims_hierarchy',
                'FUNCTION',
                current_schema(),
                'form_ncpdp',
                jsonb_build_object(
                    'function_name', 'build_cob_claims_hierarchy',
                    'cycle_detail', cycle_detail
                )
            );
            RAISE EXCEPTION 'Circular reference detected in claim hierarchy. Cycle: %', cycle_detail;
        END IF;

        -- Main recursive query to get claim hierarchy
        RETURN QUERY
        WITH RECURSIVE claim_hierarchy AS (
            -- Base case: start with primary claim
            SELECT
                f.insurance_id::integer as insurance_id,
                f.payer_id::integer as payer_id,
                f.claim_no::text as claim_no,
                f.claim_no::text as internal_control_number,
                CASE 
                    WHEN p.billing_method_id = 'ncpdp' THEN '03'
                    ELSE '10' -- Payer Name
                END::text as other_id_qualifier,
                CASE 
                    WHEN p.billing_method_id = 'ncpdp' THEN COALESCE(i.bin, p.bin)
                    ELSE p.organization
                END::text as other_id,
                CASE 
                    WHEN p.type_id = 'COUPON' THEN '98'
                    WHEN p.type_id = 'COPAY' THEN '99'
                    WHEN p.type_id = 'PAP' THEN '99'
                    ELSE '01'
                END::text as other_coverage_type,
                CASE 
                    WHEN p.type_id IN ('COUPON', 'COPAY', 'PAP') THEN 1
                    ELSE 2
                END::integer as payer_coverage_depth,
                CASE
                    WHEN p.type_id IN ('COUPON', 'COPAY', 'PAP') THEN TRUE
                    ELSE FALSE
                END::boolean as ignore_benefits,
                0::integer as depth,
                f.status::text as status,
                f.created_on::timestamp as created_date,
                prc.ing_cst_sub::NUMERIC,
                prc.gross_amount_due::NUMERIC,
                prc.disp_fee_sub::NUMERIC,
                prc.pro_svc_fee_sub::NUMERIC,
                prc.u_and_c_charge::NUMERIC,
                prc.pt_pd_amt_sub::NUMERIC,
                prc.incv_amt_sub::NUMERIC,
                COALESCE(lres.response_date, TO_CHAR(f.created_on, 'MM/DD/YYYY')),
                COALESCE(lres.ing_cst_paid, 0.0)::NUMERIC,
                COALESCE(lres.total_paid, 0.0)::NUMERIC,
                COALESCE(lres.pt_pay_amt, 0.0)::NUMERIC,
                COALESCE(lres.incv_amt_paid, 0.0)::NUMERIC,
                COALESCE(lres.disp_fee_paid, 0.0)::NUMERIC,
                COALESCE(lres.prof_svc_paid, 0.0)::NUMERIC,
                COALESCE(lres.amt_apld_ded, 0.0)::NUMERIC,
                COALESCE(lres.amt_exd_ben_max, 0.0)::NUMERIC,
                COALESCE(lres.coinsur_amt, 0.0)::NUMERIC,
                COALESCE(lres.pro_fee_amt, 0.0)::NUMERIC,
                COALESCE(lres.amt_attr_prov_sel, 0.0)::NUMERIC,
                COALESCE(lres.amt_attr_prod_sel, 0.0)::NUMERIC,
                COALESCE(lres.amt_attr_nonformulary, 0.0)::NUMERIC,
                COALESCE(lres.amt_attr_brd_nonformulary, 0.0)::NUMERIC,
                COALESCE(lres.amt_coverage_gap, 0.0)::NUMERIC,
                lres.transaction_response_status::text,
                lres.reject_codes::reject_code_type[],
                lres.oth_amt_paid::oth_amt_type[],
                lres.bft_amt::bft_amt_type[]
            FROM form_ncpdp f
            INNER JOIN sf_form_ncpdp_to_ncpdp_pricing sfprc ON sfprc.form_ncpdp_fk = f.id 
                AND sfprc.delete IS NOT TRUE 
                AND sfprc.archive IS NOT TRUE 
            INNER JOIN form_ncpdp_pricing prc ON prc.id = sfprc.form_ncpdp_pricing_fk 
                AND prc.archived IS NOT TRUE 
                AND prc.deleted IS NOT TRUE
            INNER JOIN form_payer p ON p.id = f.payer_id
            INNER JOIN form_patient_insurance i ON i.id = f.insurance_id
            LEFT JOIN get_last_claim_response(f.claim_no) lres ON true
            WHERE f.claim_no = root_claim

            UNION ALL

            -- Recursive case: follow parent claim links
            SELECT
                f.insurance_id::integer as insurance_id,
                f.payer_id::integer as payer_id,
                f.claim_no::text as claim_no,
                f.claim_no::text as internal_control_number,
                CASE 
                    WHEN p.billing_method_id = 'ncpdp' THEN '03'
                    ELSE '10'
                END::text as other_id_qualifier,
                CASE
                    WHEN p.billing_method_id = 'ncpdp' THEN COALESCE(i.bin, p.bin)
                    ELSE p.organization::text
                END,
                CASE
                    WHEN p.type_id = 'COUPON' THEN '98'
                    WHEN p.type_id = 'COPAY' THEN '99'
                    WHEN p.type_id = 'PAP' THEN '99'
                    ELSE LPAD((ch.payer_coverage_depth)::text, 2, '0')
                END::text as other_coverage_type,
                CASE
                    WHEN p.type_id IN ('COUPON', 'COPAY', 'PAP') THEN ch.payer_coverage_depth
                    WHEN ch.payer_coverage_depth > 9 THEN 99
                    ELSE ch.payer_coverage_depth + 1
                END::integer as payer_coverage_depth,
                CASE
                    WHEN p.type_id IN ('COUPON', 'COPAY', 'PAP') THEN TRUE
                    ELSE FALSE
                END::boolean as ignore_benefits,
                ch.depth + 1::integer as depth,
                f.status::text as status,
                f.created_on::timestamp as created_date,
                prc.ing_cst_sub::NUMERIC,
                prc.gross_amount_due::NUMERIC,
                prc.disp_fee_sub::NUMERIC,
                prc.pro_svc_fee_sub::NUMERIC,
                prc.u_and_c_charge::NUMERIC,
                prc.pt_pd_amt_sub::NUMERIC,
                prc.incv_amt_sub::NUMERIC,
                COALESCE(lres.response_date, TO_CHAR(f.created_on, 'MM/DD/YYYY')),
                COALESCE(lres.ing_cst_paid, 0.0)::NUMERIC,
                COALESCE(lres.total_paid, 0.0)::NUMERIC,
                COALESCE(lres.pt_pay_amt, 0.0)::NUMERIC,
                COALESCE(lres.incv_amt_paid, 0.0)::NUMERIC,
                COALESCE(lres.disp_fee_paid, 0.0)::NUMERIC,
                COALESCE(lres.prof_svc_paid, 0.0)::NUMERIC,
                COALESCE(lres.amt_apld_ded, 0.0)::NUMERIC,
                COALESCE(lres.amt_exd_ben_max, 0.0)::NUMERIC,
                COALESCE(lres.coinsur_amt, 0.0)::NUMERIC,
                COALESCE(lres.pro_fee_amt, 0.0)::NUMERIC,
                COALESCE(lres.amt_attr_prov_sel, 0.0)::NUMERIC,
                COALESCE(lres.amt_attr_prod_sel, 0.0)::NUMERIC,
                COALESCE(lres.amt_attr_nonformulary, 0.0)::NUMERIC,
                COALESCE(lres.amt_attr_brd_nonformulary, 0.0)::NUMERIC,
                COALESCE(lres.amt_coverage_gap, 0.0)::NUMERIC,
                lres.transaction_response_status::text,
                lres.reject_codes::reject_code_type[],
                lres.oth_amt_paid::oth_amt_type[],
                lres.bft_amt::bft_amt_type[]
            FROM form_ncpdp f
            INNER JOIN sf_form_ncpdp_to_ncpdp_pricing sfprc ON sfprc.form_ncpdp_fk = f.id 
                AND sfprc.delete IS NOT TRUE 
                AND sfprc.archive IS NOT TRUE 
            INNER JOIN form_ncpdp_pricing prc ON prc.id = sfprc.form_ncpdp_pricing_fk 
                AND prc.archived IS NOT TRUE 
                AND prc.deleted IS NOT TRUE
            JOIN claim_hierarchy ch ON f.parent_claim_no = ch.claim_no
            INNER JOIN form_payer p ON p.id = f.payer_id
            INNER JOIN form_patient_insurance i ON i.id = f.insurance_id
            LEFT JOIN get_last_claim_response(f.claim_no) lres ON true
            WHERE ch.depth < 50 -- Safeguard against infinite recursion
        )
        SELECT * FROM claim_hierarchy;

        -- Log successful completion
        PERFORM log_billing_function(
            'get_claim_hierarchy'::tracked_function,
            v_params,
            NULL::jsonb, -- No result logged due to potentially large result set
            NULL::text,
            clock_timestamp() - v_start_time
        );
    EXCEPTION WHEN OTHERS THEN
        -- Log error
        v_error_message := SQLERRM;
        
        -- Log to billing error log
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            v_error_message,
            'Exception in get_claim_hierarchy',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'get_claim_hierarchy',
                'root_claim', root_claim
            )
        );

        -- Log to NCPDP function log
        PERFORM log_billing_function(
            'get_claim_hierarchy'::tracked_function,
            v_params::jsonb,
            NULL::jsonb,
            v_error_message::text,
            clock_timestamp() - v_start_time
        );
        RAISE;
    END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;