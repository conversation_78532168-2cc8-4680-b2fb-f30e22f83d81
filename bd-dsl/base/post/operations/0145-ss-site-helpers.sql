CREATE OR R<PERSON>LACE FUNCTION sanitize_surescripts_string(p_input_text TEXT)
RETURNS TEXT LANGUAGE plpgsql IMMUTABLE AS $$
BEGIN
    IF p_input_text IS NULL THEN
        RETURN NULL;
    END IF;
    -- Remove characters not in the range of space (ASCII 32) to tilde (ASCII 126)
    -- Also replace multiple spaces with a single space
    RETURN regexp_replace(regexp_replace(p_input_text, '[^\x20-\x7E]', '', 'g'), '\s+', ' ', 'g');
END;
$$;

CREATE OR REPLACE FUNCTION sanitize_surescripts_text_array(p_input_array TEXT[])
RETURNS TEXT[] LANGUAGE plpgsql IMMUTABLE AS $$
DECLARE
    v_sanitized_array TEXT[];
    v_element TEXT;
BEGIN
    IF p_input_array IS NULL THEN
        RETURN NULL;
    END IF;
    FOREACH v_element IN ARRAY p_input_array
    LOOP
        v_sanitized_array := array_append(v_sanitized_array, sanitize_surescripts_string(v_element));
    END LOOP;
    RETURN v_sanitized_array;
END;
$$;

CREATE OR REPLACE FUNCTION get_formatted_site_hours(p_site_id INTEGER)
RETURNS TEXT LANGUAGE plpgsql AS $$
DECLARE
    v_hours_string TEXT := '';
    v_site_hour_record RECORD;
BEGIN
    FOR v_site_hour_record IN
        SELECT sh.day, sh.start_time, sh.close_time
        FROM form_site_hours sh
        JOIN sf_form_site_to_site_hours sf ON sh.id = sf.form_site_hours_fk
        WHERE sf.form_site_fk = p_site_id
          AND sh.deleted IS NOT TRUE AND sh.archived IS NOT TRUE
          AND sf.archive IS NOT TRUE AND sf.delete IS NOT TRUE
        ORDER BY sh.day -- Ensure consistent order if needed, though Surescripts might not care
    LOOP
        v_hours_string := v_hours_string || v_site_hour_record.day || ' ' || v_site_hour_record.start_time || '-' || v_site_hour_record.close_time || ' ';
    END LOOP;

    RETURN TRIM(v_hours_string);
END;
$$;

DROP VIEW IF EXISTS vw_surescripts_directory_site_info CASCADE;
CREATE OR REPLACE VIEW vw_surescripts_directory_site_info AS
SELECT
    s.id::INTEGER as original_site_id,
    sssl.service_levels::TEXT[] as organization_servicelevels,
    sanitize_surescripts_string(s.ncpdp_id::text) as organization_ncpdp_id,
    sanitize_surescripts_string(s.npi::text) as organization_npi,
    sanitize_surescripts_string(s.dea_id::text) as organization_dea_number,
    sanitize_surescripts_string(s.ss_organization_id::text) as organization_id,
    sanitize_surescripts_string(s.name::text) as organization_name,
    sanitize_surescripts_string(TRIM(s.address1::text || ' ' || COALESCE(s.address2::text, ''))) as organization_address, -- Added COALESCE for address2
    sanitize_surescripts_string(s.city::text) as organization_city,
    sanitize_surescripts_string(s.state_id::text) as organization_state,
    sanitize_surescripts_string(s.zip::text) as organization_zipcode,
    sanitize_surescripts_string(s.phone::text) as organization_phone,
    sanitize_surescripts_string(s.fax::text) as organization_fax,
    -- ss_organization_type is used to build organization_specialties, keep it if direct use cases exist or for clarity
    'Pharmacy'::text as organization_type_raw, 
    -- ss_organization_specialty (original array from DB) is used to build organization_specialties
    sanitize_surescripts_text_array(
        CASE 
            WHEN s.ss_organization_specialty IS NOT NULL THEN string_to_array(trim(both '{}' from s.ss_organization_specialty), ',')::TEXT[] 
            ELSE ARRAY[]::TEXT[] 
        END
    ) as organization_specialty_array_raw,
    -- New combined field for Surescripts Directory payload
    array_remove(
        array_cat(
            CASE WHEN s.ss_organization_type IS NOT NULL AND TRIM(s.ss_organization_type) <> '' 
                 THEN ARRAY[sanitize_surescripts_string(s.ss_organization_type::text)] 
                 ELSE ARRAY[]::TEXT[] 
            END,
            CASE 
                WHEN s.ss_organization_specialty IS NOT NULL AND TRIM(s.ss_organization_specialty) <> '{}' AND TRIM(s.ss_organization_specialty) <> '' 
                THEN sanitize_surescripts_text_array(string_to_array(trim(both '{}' from s.ss_organization_specialty), ',')::TEXT[])
                ELSE ARRAY[]::TEXT[]
            END
        ),
        NULL -- Remove any NULL elements that might have resulted if sanitize_surescripts_string returned NULL
    ) as organization_specialties -- This is the field the JS code should use directly
FROM
    form_site s
    LEFT JOIN LATERAL (
        SELECT ARRAY_AGG(grsl.form_list_ss_service_level_fk) as service_levels FROM gr_form_site_ss_service_level_to_list_ss_service_level_id grsl
        WHERE grsl.form_site_fk = s.id
    ) sssl ON TRUE 
WHERE s.deleted IS NOT TRUE AND s.archived IS NOT TRUE;