
CREATE OR REPLACE FUNCTION build_prescriber_ncpdp_segment(
  p_patient_id integer,
  p_prescriber_id integer,
  p_insurance_id integer,
  p_site_id integer,
  p_transaction_code text DEFAULT 'B1',
  p_parent_claim_no text DEFAULT NULL
) RETURNS ncpdp_prescriber AS $$
DECLARE
  v_start_time timestamp;
  v_execution_time interval;
  v_result ncpdp_prescriber;
  v_error_message text;
  v_params jsonb;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  RAISE LOG 'p_prescriber_id: % p_patient_id: % p_insurance_id: % p_site_id: % p_transaction_code: % p_parent_claim_no: %', p_prescriber_id, p_patient_id, p_insurance_id, p_site_id, p_transaction_code, p_parent_claim_no;
  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'patient_id', p_patient_id,
    'prescriber_id', p_prescriber_id,
    'insurance_id', p_insurance_id,
    'site_id', p_site_id,
    'transaction_code', p_transaction_code,
    'parent_claim_no', p_parent_claim_no
  );

  BEGIN  -- Start exception block
    -- Log function call
    PERFORM log_billing_function(
      'build_prescriber_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );
    -- Validate required parameters
    IF p_patient_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Patient ID cannot be null',
            'Validating parameters in build_prescriber_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_prescriber_ncpdp_segment',
                'patient_id', p_patient_id,
                'prescriber_id', p_prescriber_id,
                'insurance_id', p_insurance_id,
                'site_id', p_site_id
            )
        );
        RAISE EXCEPTION 'Patient ID cannot be null';
    END IF;

    IF p_prescriber_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Prescriber ID cannot be null',
            'Validating parameters in build_prescriber_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_prescriber_ncpdp_segment',
                'patient_id', p_patient_id,
                'prescriber_id', p_prescriber_id,
                'insurance_id', p_insurance_id,
                'site_id', p_site_id
            )
        );
        RAISE EXCEPTION 'Prescriber ID cannot be null';
    END IF;

    IF p_insurance_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Insurance ID cannot be null',
            'Validating parameters in build_prescriber_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_prescriber_ncpdp_segment',
                'patient_id', p_patient_id,
                'prescriber_id', p_prescriber_id,
                'insurance_id', p_insurance_id,
                'site_id', p_site_id
            )
        );
        RAISE EXCEPTION 'Insurance ID cannot be null';
    END IF;

    IF p_site_id IS NULL THEN
        INSERT INTO billing_error_log (
            error_message,
            error_context,
            error_type,
            schema_name,
            table_name,
            additional_details
        ) VALUES (
            'Site ID cannot be null',
            'Validating parameters in build_prescriber_ncpdp_segment',
            'FUNCTION',
            current_schema(),
            'form_ncpdp',
            jsonb_build_object(
                'function_name', 'build_prescriber_ncpdp_segment',
                'patient_id', p_patient_id,
                'prescriber_id', p_prescriber_id,
                'insurance_id', p_insurance_id,
                'site_id', p_site_id
            )
        );
        RAISE EXCEPTION 'Site ID cannot be null';
    END IF;

    -- Get insurance settings and parent claim data once
    WITH insurance_info AS (
      SELECT * FROM get_insurance_claim_settings(p_insurance_id, p_site_id, NULL::integer)
    ),
    parent_claim_data AS (
      SELECT dr.*
      FROM form_ncpdp pclaim
      LEFT JOIN sf_form_ncpdp_to_ncpdp_prescriber sfdr ON sfdr.form_ncpdp_fk = pclaim.id 
      AND sfdr.archive IS NOT TRUE 
      AND sfdr.delete IS NOT TRUE
      LEFT JOIN form_ncpdp_prescriber dr ON dr.id = sfdr.form_ncpdp_prescriber_fk 
      AND dr.archived IS NOT TRUE 
      AND dr.deleted IS NOT TRUE
      WHERE pclaim.claim_no = p_parent_claim_no AND p_parent_claim_no IS NOT NULL
      AND pclaim.archived IS NOT TRUE
      AND pclaim.deleted IS NOT TRUE
    ),
    prescriber_info AS (
      SELECT 
        p_transaction_code::text AS transaction_code,
        ph.id::integer AS physician_id,
        CASE 
          WHEN ins.prescriber_no_field_id = '04' AND length(ph.mcr_ptan_id) > 0 THEN ins.prescriber_no_field_id::text
          WHEN ins.prescriber_no_field_id = '05' AND length(ph.mcd) > 0 THEN ins.prescriber_no_field_id::text
          WHEN ins.prescriber_no_field_id = '08' AND length(ph.state_license) > 0 THEN ins.prescriber_no_field_id::text
          WHEN ins.prescriber_no_field_id = '12' AND length(ph.dea) > 0 THEN ins.prescriber_no_field_id::text
          WHEN ins.prescriber_no_field_id = '13' AND length(ph.state_cs_license) > 0 THEN ins.prescriber_no_field_id::text
          ELSE COALESCE(ins.prescriber_no_field_id, '01')::text
        END AS dr_id_qualifier,
        CASE 
          WHEN ins.prescriber_no_field_id = '04' AND length(ph.mcr_ptan_id) > 0 THEN ph.mcr_ptan_id::text
          WHEN ins.prescriber_no_field_id = '05' AND length(ph.mcd) > 0 THEN ph.mcd::text
          WHEN ins.prescriber_no_field_id = '08' AND length(ph.state_license) > 0 THEN ph.state_license::text
          WHEN ins.prescriber_no_field_id = '12' AND length(ph.dea) > 0 THEN ph.dea::text
          WHEN ins.prescriber_no_field_id = '13' AND length(ph.state_cs_license) > 0 THEN ph.state_cs_license::text
          ELSE ph.npi::text
        END AS dr_id,
        CASE
          WHEN p_parent_claim_no IS NOT NULL THEN cd.dr_last_name::text
          ELSE ph.last::text
        END AS dr_last_name,
        CASE
          WHEN p_parent_claim_no IS NOT NULL THEN cd.dr_first_name::text
          ELSE ph.first::text
        END AS dr_first_name,
        CASE
          WHEN p_parent_claim_no IS NOT NULL THEN cd.dr_street_address::text
          WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3') THEN phl.address1::text
          ELSE NULL::text
        END AS dr_street_address,
        CASE 
          WHEN p_parent_claim_no IS NOT NULL THEN cd.dr_city::text
          WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3') THEN phl.city::text
          ELSE NULL::text
        END AS dr_city,
        CASE
          WHEN p_parent_claim_no IS NOT NULL THEN cd.dr_state::text
          WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3') THEN phl.state_id::text
          ELSE NULL::text
        END AS dr_state, 
        CASE
          WHEN p_parent_claim_no IS NOT NULL THEN cd.dr_zip::text
          WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3') THEN phl.zip::text
          ELSE NULL::text
        END AS dr_zip, 
        CASE 
          WHEN p_parent_claim_no IS NOT NULL THEN cd.dr_phone::text
          WHEN p_transaction_code IN ('B1', 'B3', 'S1', 'S3') THEN COALESCE(ph.primary_phone, phl.phone_number)::text
          ELSE NULL::text
        END AS dr_phone
      FROM form_patient_prescriber pp
      CROSS JOIN insurance_info ins
      LEFT JOIN parent_claim_data cd ON true
      INNER JOIN form_physician ph ON ph.id = COALESCE(cd.physician_id, pp.physician_id) 
        AND ph.archived IS NOT TRUE 
        AND ph.deleted IS NOT TRUE
      LEFT JOIN LATERAL (
        SELECT
          pl.address1,
          pl.city,
          pl.state_id,
          pl.zip,
          pl.phone_number
        FROM form_physician_location pl
        INNER JOIN sf_form_physician_to_physician_location sfpl ON sfpl.form_physician_fk = ph.id 
          AND pl.id = sfpl.form_physician_location_fk 
          AND sfpl.delete IS NOT TRUE 
          AND sfpl.archive IS NOT TRUE
        ORDER BY pl.is_primary NULLS LAST
        LIMIT 1
      ) phl ON true 
      WHERE pp.id = p_prescriber_id
    ),
    pcp_info AS (
      SELECT 
        CASE 
          WHEN p_parent_claim_no IS NOT NULL THEN cd.primary_physician_id::integer
          ELSE ph.id::integer
        END AS primary_physician_id,
        CASE 
          WHEN ins.pcp_no_field_id = '04' AND length(ph.mcr_ptan_id) > 0 THEN ins.pcp_no_field_id::text
          WHEN ins.pcp_no_field_id = '05' AND length(ph.mcd) > 0 THEN ins.pcp_no_field_id::text
          WHEN ins.pcp_no_field_id = '08' AND length(ph.state_license) > 0 THEN ins.pcp_no_field_id::text
          WHEN ins.pcp_no_field_id = '12' AND length(ph.dea) > 0 THEN ins.pcp_no_field_id::text
          WHEN ins.pcp_no_field_id = '13' AND length(ph.state_cs_license) > 0 THEN ins.pcp_no_field_id::text
          ELSE COALESCE(ins.pcp_no_field_id, '01')::text
        END AS pri_dr_id_qualifier,
        CASE 
          WHEN ins.pcp_no_field_id = '04' AND length(ph.mcr_ptan_id) > 0 THEN ph.mcr_ptan_id::text
          WHEN ins.pcp_no_field_id = '05' AND length(ph.mcd) > 0 THEN ph.mcd::text
          WHEN ins.pcp_no_field_id = '08' AND length(ph.state_license) > 0 THEN ph.state_license::text
          WHEN ins.pcp_no_field_id = '12' AND length(ph.dea) > 0 THEN ph.dea::text
          WHEN ins.pcp_no_field_id = '13' AND length(ph.state_cs_license) > 0 THEN ph.state_cs_license::text
          ELSE ph.npi::text
        END AS pri_dr_id,
        CASE
          WHEN p_parent_claim_no IS NOT NULL THEN cd.pri_dr_last_name::text
          ELSE ph.last::text
        END AS pri_dr_last_name
      FROM form_patient pt 
      LEFT JOIN LATERAL (
        SELECT
          pcppp.id
        FROM form_patient_prescriber pcppp
        WHERE COALESCE(pcppp.is_primary, 'No') = 'Yes'
          AND pcppp.patient_id = pt.id
          AND pcppp.archived IS NOT TRUE 
          AND pcppp.deleted IS NOT TRUE
        LIMIT 1
      ) pcp ON TRUE
      INNER JOIN form_patient_prescriber pp ON pp.id = COALESCE(pcp.id, p_prescriber_id) 
        AND pp.archived IS NOT TRUE 
        AND pp.deleted IS NOT TRUE 
        AND pp.patient_id = pt.id
      LEFT JOIN parent_claim_data cd ON true
      INNER JOIN form_physician ph ON ph.id = COALESCE(cd.primary_physician_id, pp.physician_id) 
        AND ph.archived IS NOT TRUE 
        AND ph.deleted IS NOT TRUE
      CROSS JOIN insurance_info ins
      WHERE pt.id = p_patient_id
    )
    -- Combine prescriber and PCP info into the final result
    SELECT
      prei.transaction_code::text AS transaction_code,
      prei.physician_id::integer AS physician_id,
      prei.dr_id_qualifier::text AS dr_id_qualifier,
      prei.dr_id::text AS dr_id,
      prei.dr_last_name::text AS dr_last_name,
      prei.dr_first_name::text AS dr_first_name,
      prei.dr_street_address::text AS dr_street_address,
      prei.dr_city::text AS dr_city,
      prei.dr_state::text AS dr_state,
      prei.dr_zip::text AS dr_zip,
      prei.dr_phone::text AS dr_phone,
      pcpi.primary_physician_id::integer AS primary_physician_id,
      pcpi.pri_dr_id_qualifier::text AS pri_dr_id_qualifier,
      pcpi.pri_dr_id::text AS pri_dr_id,
      pcpi.pri_dr_last_name::text AS pri_dr_last_name
    INTO v_result
    FROM prescriber_info prei 
    CROSS JOIN pcp_info pcpi;
    
    -- Validate we got a result
    IF v_result IS NULL THEN
      INSERT INTO billing_error_log (
        error_message,
        error_context,
        error_type,
        schema_name,
        table_name,
        additional_details
      ) VALUES (
        'Failed to build prescriber segment',
        'Validating result in build_prescriber_ncpdp_segment',
        'FUNCTION',
        current_schema(),
        'form_ncpdp',
        jsonb_build_object(
          'function_name', 'build_prescriber_ncpdp_segment',
          'prescriber_id', p_prescriber_id
        )
      );
      RAISE EXCEPTION 'Failed to build prescriber segment for prescriber_id: %', p_prescriber_id;
    END IF;

    -- Log successful completion
    PERFORM log_billing_function(
      'build_prescriber_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    -- Log error
    v_error_message := SQLERRM;
    
    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in build_prescriber_ncpdp_segment',
      'FUNCTION',
      current_schema(),
      'form_ncpdp',
      jsonb_build_object(
        'function_name', 'build_prescriber_ncpdp_segment',
        'prescriber_id', p_prescriber_id,
        'patient_id', p_patient_id,
        'insurance_id', p_insurance_id,
        'site_id', p_site_id
      )
    );

    PERFORM log_billing_function(
      'build_prescriber_ncpdp_segment'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$$ LANGUAGE plpgsql VOLATILE;