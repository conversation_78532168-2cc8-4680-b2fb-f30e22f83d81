CREATE TABLE IF NOT EXISTS ss_error_log (
    id BIGSERIAL PRIMARY KEY,
    error_message TEXT NOT NULL,
    error_context TEXT,
    occurred_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    error_type TEXT,
    schema_name TEXT,
    table_name TEXT,
    table_id INTEGER,
    application_name TEXT,
    additional_details JSONB,
    surescripts_error_code TEXT,
    surescripts_desc_code TEXT
); 

CREATE OR REPLACE FUNCTION _log_surescripts_error(
    p_ss_message_id INTEGER,
    p_sqlstate TEXT,
    p_message_text TEXT,
    p_pg_exception_context TEXT,
    p_function_name TEXT,
    p_additional_details JSONB DEFAULT NULL
)
RETURNS VOID LANGUAGE plpgsql AS $$
BEGIN
    INSERT INTO ss_error_log (
        error_message, error_context, error_type, 
        table_name, table_id, application_name, additional_details,
        surescripts_error_code, surescripts_desc_code
    ) VALUES (
        p_message_text,
        'SQLSTATE: ' || p_sqlstate || E'\nCONTEXT: ' || p_pg_exception_context || E'\nFUNCTION: ' || p_function_name,
        'OUTBOUND_PAYLOAD_GENERATION_ERROR',
        'form_ss_message',
        p_ss_message_id,
        'SURESCRIPTS_OUTBOUND_GENERATOR',
        p_additional_details,
        NULL, NULL 
    );
END;
$$;


-- Create a GIN index on the message_json column after casting to jsonb
-- This will help speed up queries that filter or extract data from the JSON structure.
CREATE INDEX IF NOT EXISTS idx_gin_form_ss_log_message_jsonb ON form_ss_log USING gin ((message_json::jsonb)); 

CREATE OR REPLACE FUNCTION fn_format_ndc(p_ndc TEXT)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    v_cleaned_ndc TEXT;
    v_part1 TEXT;
    v_part2 TEXT;
    v_part3 TEXT;
    v_formatted_ndc TEXT;
BEGIN
    IF p_ndc IS NULL OR TRIM(p_ndc) = '' THEN
        RETURN NULL;
    END IF;
    -- Remove all non-numeric characters
    v_cleaned_ndc := REGEXP_REPLACE(p_ndc, '[^0-9]', '', 'g');

    -- Check length for common formats
    IF LENGTH(v_cleaned_ndc) = 11 THEN
        -- Already 11 digits, assume it's correctly formatted without hyphens
        v_formatted_ndc := v_cleaned_ndc;
    ELSIF LENGTH(v_cleaned_ndc) = 10 THEN
        -- Attempt to convert 10-digit to 11-digit by trying common hyphenation patterns
        -- Most common is 5-4-1 needing padding in 3rd segment, or 4-4-2 needing padding in 1st
        -- A common practice is to pad the shortest segment.
        -- Example: 1234-5678-90 -> 1234-5678-090 (Incorrect for standard 5-4-2, but this is just an example of 10 to 11)
        -- Surescripts usually expects NDCs in 11-digit format (5-4-2 segments, no hyphens).
        -- If 10 digits are received, it's often assumed to be 4-4-2 (needs padding in 1st segment) or 5-3-2 (padding in 2nd).
        -- For simplicity, a common approach is to pad the first segment (labeler) if it's 4 digits,
        -- or pad the second segment (product) if it's 3 digits.
        -- This example will assume if 10 digits, it's likely a 4-4-2 that needs a leading zero on the first part.
        -- Or a 5-3-2 that needs a leading zero on the second part.
        -- The most reliable way is to know the original hyphenated format if it was 10-digit.
        -- Given just 10 digits, this is a common heuristic:
        -- Try 5-4-2 by padding middle (if original was 5-3-2): LPAD(SUBSTRING(v_cleaned_ndc, 6, 3), 4, '0')
        -- Try 5-4-2 by padding first (if original was 4-4-2): LPAD(SUBSTRING(v_cleaned_ndc, 1, 4), 5, '0')
        -- For now, let's try a common padding for 10-digit NDCs to make them 11-digit (5-4-2 structure):
        -- If the original was 1234-1234-12, it becomes 01234-1234-12.
        -- If the original was 12345-123-12, it becomes 12345-0123-12.
        -- If the original was 12345-1234-1, it becomes 12345-1234-01.
        -- This is complex to do generically without knowing the original segments.
        -- A common system default is to pad the product code (middle part).
        -- Let's assume for a 10-digit unhyphenated string, it's missing a leading zero in the product segment (e.g. 5-3-2 -> 5-4-2)
        -- or labeler segment (e.g. 4-4-2 -> 5-4-2).
        -- The FDA standard is 5-4-2. If 10 digits are presented as XXXXX-XXX-XX, then pad middle. If XXXX-XXXX-XX, pad first.
        -- Without hyphens, it's ambiguous.
        -- A simple approach if you mostly get 10-digit NDCs that were 4-4-2 originally:
        -- v_formatted_ndc := '0' || v_cleaned_ndc; -- This is often too simplistic.

        -- More robust: Try to infer based on common 10-digit patterns that become 11-digit (5-4-2)
        -- Pattern 1: Original 4-4-2 (e.g., 1234-5678-90) -> 01234-5678-90 -> 01234567890
        -- Pattern 2: Original 5-3-2 (e.g., 12345-678-90) -> 12345-0678-90 -> 12345067890
        -- Pattern 3: Original 5-4-1 (e.g., 12345-6789-0) -> 12345-6789-00 -> 12345678900

        -- For simplicity, let's assume the most common transformation is to pad to 11 digits.
        -- The most common is that the product code (middle part) is 3 digits instead of 4, requiring a leading zero there.
        -- Or the labeler code (first part) is 4 digits instead of 5.
        -- This heuristic is NOT foolproof. True NDC conversion requires knowledge of original segments or a database lookup.
        -- Let's assume the 10-digit is often a 4-4-2 and needs padding on the first segment
        v_formatted_ndc := LPAD(v_cleaned_ndc, 11, '0'); -- Simple left padding to 11. This is a common fallback.

    ELSIF LENGTH(v_cleaned_ndc) = 9 THEN -- e.g. 4-3-2
        v_formatted_ndc := LPAD(v_cleaned_ndc, 11, '0'); -- Heuristic
    ELSIF LENGTH(v_cleaned_ndc) = 8 THEN -- e.g. 4-3-1
        v_formatted_ndc := LPAD(v_cleaned_ndc, 11, '0'); -- Heuristic
    ELSE
        -- If not 10 or 11 digits after cleaning, it might be an issue or a different system.
        -- For this function, we'll return it as is if not fitting common patterns for padding.
        -- Or, you might want to return NULL or raise an error.
        RAISE WARNING 'NDC fn_format_ndc received unexpected length after cleaning: % for original %', LENGTH(v_cleaned_ndc), p_ndc;
        v_formatted_ndc := v_cleaned_ndc; -- or NULL
    END IF;

    RETURN v_formatted_ndc;
END;
$$;

-- Helper function shells for Status, Verify, Error
CREATE OR REPLACE FUNCTION _handle_status_response(p_message_json JSONB, p_related_message_id TEXT, p_ss_log_id INTEGER)
RETURNS TABLE (ss_message_id INTEGER, error_message TEXT) LANGUAGE plpgsql AS $$ 
DECLARE
    v_status_code TEXT;
    v_status_desc_code TEXT;
    v_status_description TEXT;
    v_target_ss_message_id INTEGER;
    v_current_direction TEXT;
    v_current_message_status TEXT; 
    v_new_message_status TEXT;     
    v_error_text TEXT;
    v_error_context TEXT;
BEGIN 
    RAISE LOG 'ENTERING _handle_status_response for log ID: %, RelatedID: %', p_ss_log_id, p_related_message_id;

    v_status_code        := p_message_json#>>'{Message,Body,Status,Code}';
    v_status_desc_code   := p_message_json#>>'{Message,Body,Status,DescriptionCode}';
    v_status_description := p_message_json#>>'{Message,Body,Status,Description}';

    RAISE LOG '_handle_status_response - Status Details - Code: %, DescCode: %, Desc: %', v_status_code, v_status_desc_code, v_status_description;

    IF p_related_message_id IS NOT NULL THEN
        SELECT msg.id, msg.direction, msg.message_status 
        INTO v_target_ss_message_id, v_current_direction, v_current_message_status
        FROM form_ss_message msg 
        WHERE msg.message_id = p_related_message_id 
        AND msg.deleted IS NOT TRUE AND msg.archived IS NOT TRUE
        ORDER BY msg.created_on DESC LIMIT 1;

        IF v_target_ss_message_id IS NOT NULL THEN
            RAISE LOG '_handle_status_response - Found related form_ss_message ID: % for status update. Current status: %', v_target_ss_message_id, v_current_message_status;
            v_new_message_status := v_current_message_status; -- Default to current status

            DECLARE
                v_set_surescripts_error_code TEXT := NULL;
                v_set_surescripts_error_desc_code TEXT := NULL;
                v_set_error_description TEXT := NULL;
            BEGIN
                IF v_status_code >= '600' THEN
                    v_set_surescripts_error_code := v_status_code;
                    v_set_surescripts_error_desc_code := v_status_desc_code;
                    v_set_error_description := v_status_description;
                END IF;

                CASE v_status_code
                    WHEN '010' THEN 
                        v_new_message_status := 'Verified';
                    WHEN '000', '001', '003', '005' THEN
                        IF v_current_direction = 'OUT' THEN
                            v_new_message_status := 'Sent';
                        END IF;
                        IF v_status_code = '002' THEN
                             RAISE LOG 'Status code 002 (No more messages) received for related_id %. No status change on original message.', p_related_message_id;
                             v_new_message_status := v_current_message_status; 
                        END IF;
                         IF v_status_code = '005' THEN
                             RAISE LOG 'Status code 005 (Password Expiring) received for related_id %. No status change on original message.', p_related_message_id;
                             v_new_message_status := v_current_message_status; 
                        END IF;
                    WHEN '600' THEN v_new_message_status := 'Comms Error';
                    WHEN '601' THEN v_new_message_status := 'Receiver Processing Error';
                    WHEN '602' THEN v_new_message_status := 'Receiver System Error';
                    WHEN '700' THEN v_new_message_status := 'Configuration Error';
                    WHEN '900' THEN v_new_message_status := 'Rejected';
                    ELSE
                        RAISE WARNING 'Unhandled Surescripts Status Code: % for related_id: %', v_status_code, p_related_message_id;
                END CASE;

                UPDATE form_ss_message
                SET 
                    processed = 'Yes',
                    processed_dt = CURRENT_TIMESTAMP,
                    message_status = v_new_message_status, 
                    error_code_id = NULL, -- Set old FK to NULL
                    error_desc_code_id = NULL, -- Set old FK to NULL
                    surescripts_error_code = v_set_surescripts_error_code, -- Store raw text code
                    surescripts_error_desc_code = v_set_surescripts_error_desc_code, -- Store raw text desc code
                    error_description = COALESCE(v_set_error_description, error_description) -- Keep original if new is null, or use existing form_ss_message.error_description
                WHERE id = v_target_ss_message_id;
            END; 
            
            RAISE LOG 'Updated form_ss_message ID: % with status info. New status: %', v_target_ss_message_id, v_new_message_status;
            RETURN QUERY SELECT NULL::INTEGER, NULL::TEXT;
        ELSE
            RAISE WARNING 'Status received for unknown related message ID: %', p_related_message_id;
            INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details, surescripts_error_code, surescripts_desc_code)
            VALUES ('Status for unknown related message', 'RelatedMessageID: ' || p_related_message_id, 'RELATED_MESSAGE_NOT_FOUND', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_STATUS_HANDLER', p_message_json, v_status_code, v_status_desc_code);
            RETURN QUERY SELECT NULL::INTEGER, ('Could not find the original message (ID: ' || p_related_message_id || ') to apply this status update.')::TEXT;
        END IF;
    ELSE
        RAISE WARNING 'Status message received without a RelatesToMessageID. ss_log_id: %', p_ss_log_id;
        INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details, surescripts_error_code, surescripts_desc_code)
        VALUES ('Status message without RelatesToMessageID', 'Message: ' || p_message_json::text, 'MISSING_RELATED_ID', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_STATUS_HANDLER', p_message_json, v_status_code, v_status_desc_code);
        RETURN QUERY SELECT NULL::INTEGER, 'Status message received without a related message ID; cannot process.'::TEXT;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_error_text = MESSAGE_TEXT, v_error_context = PG_EXCEPTION_CONTEXT;
        RAISE LOG 'Error in _handle_status_response for ss_log_id: %. Error: %, Context: %', p_ss_log_id, v_error_text, v_error_context;
        INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details, surescripts_error_code, surescripts_desc_code)
        VALUES (v_error_text, v_error_context, 'STATUS_HANDLER_EXCEPTION', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_STATUS_HANDLER', p_message_json, v_status_code, v_status_desc_code);
        RETURN QUERY SELECT NULL::INTEGER, ('Error processing Status message (Log ID: ' || p_ss_log_id || '): ' || v_error_text)::TEXT;
END; $$;

CREATE OR REPLACE FUNCTION _handle_verify_response(p_message_json JSONB, p_related_message_id TEXT, p_ss_log_id INTEGER)
RETURNS TABLE (ss_message_id INTEGER, error_message TEXT) LANGUAGE plpgsql AS $$ 
DECLARE
    v_target_ss_message_id INTEGER;
    v_error_text TEXT;
    v_error_context TEXT;
    v_verify_description TEXT; 
    v_new_ss_message_id INTEGER;
BEGIN 
    RAISE LOG 'Handling Verify response for related msg: % (ss_log_id: %)', p_related_message_id, p_ss_log_id;

    v_verify_description := p_message_json#>>'{Message,Body,Verify,Description}';
    IF v_verify_description IS NOT NULL THEN
        RAISE LOG 'Verify Description: %', v_verify_description;
    END IF;

    IF p_related_message_id IS NOT NULL THEN
        SELECT msg.id INTO v_target_ss_message_id
        FROM form_ss_message msg 
        WHERE msg.message_id = p_related_message_id 
        AND msg.deleted IS NOT TRUE AND msg.archived IS NOT TRUE
        ORDER BY msg.created_on DESC LIMIT 1;

        IF v_target_ss_message_id IS NOT NULL THEN
            RAISE LOG 'Found related form_ss_message ID: % for Verify update.', v_target_ss_message_id;
            UPDATE form_ss_message
            SET 
                processed = 'Yes',
                processed_dt = CURRENT_TIMESTAMP,
                message_status = 'Verified',
                error_code_id = NULL, -- Clear previous errors
                error_desc_code_id = NULL,
                error_description = COALESCE(v_verify_description, error_description) -- Update if Verify provides a description
            WHERE id = v_target_ss_message_id;
            
            RAISE LOG 'Updated form_ss_message ID: % to Verified.', v_target_ss_message_id;
            RETURN QUERY SELECT NULL::INTEGER, NULL::TEXT; -- Success
        ELSE
            RAISE WARNING 'Verify received for unknown related message ID: %', p_related_message_id;
            INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details, surescripts_error_code, surescripts_desc_code)
            VALUES ('Verify for unknown related message', 'RelatedMessageID: ' || p_related_message_id, 'RELATED_MESSAGE_NOT_FOUND', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_VERIFY_HANDLER', p_message_json, NULL, NULL);
            RETURN QUERY SELECT NULL::INTEGER, ('Could not find the original message (ID: ' || p_related_message_id || ') to apply this verification.')::TEXT;
        END IF;
    ELSE
        RAISE WARNING 'Verify message received without a RelatesToMessageID. ss_log_id: %', p_ss_log_id;
        INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details, surescripts_error_code, surescripts_desc_code)
        VALUES ('Verify message without RelatesToMessageID', 'Message: ' || p_message_json::text, 'MISSING_RELATED_ID', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_VERIFY_HANDLER', p_message_json, NULL, NULL);
        RETURN QUERY SELECT NULL::INTEGER, 'Verify message received without a related message ID; cannot process.'::TEXT;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_error_text = MESSAGE_TEXT, v_error_context = PG_EXCEPTION_CONTEXT;
        RAISE LOG 'Error in _handle_verify_response for ss_log_id: %. Error: %, Context: %', p_ss_log_id, v_error_text, v_error_context;
        INSERT INTO ss_error_log (error_message, error_context, error_type, table_name, table_id, application_name, additional_details, surescripts_error_code, surescripts_desc_code)
        VALUES (v_error_text, v_error_context, 'VERIFY_HANDLER_EXCEPTION', 'form_ss_log', p_ss_log_id, 'SURESCRIPTS_VERIFY_HANDLER', p_message_json, NULL, NULL);
        RETURN QUERY SELECT NULL::INTEGER, ('Error processing Verify message (Log ID: ' || p_ss_log_id || '): ' || v_error_text)::TEXT;
END; $$;

CREATE OR REPLACE FUNCTION handle_ss_error_response(p_message_json JSONB, p_related_message_id TEXT, p_ss_log_id INTEGER)
RETURNS TABLE (ss_message_id INTEGER, error_message TEXT, error_json_response JSONB) LANGUAGE plpgsql AS $$
DECLARE
    v_surescripts_error_code TEXT;    -- From Message.Body.Error.Code
    v_surescripts_desc_code TEXT;     -- From Message.Body.Error.DescriptionCode
    v_surescripts_description TEXT;   -- From Message.Body.Error.Description
    v_target_ss_message_id INTEGER;
    v_user_facing_error_message TEXT;
    v_error_text TEXT;
    v_error_context TEXT;
    v_original_header JSONB;
BEGIN
    RAISE LOG 'Handling Error response. Related msg ID: % (ss_log_id: %)', COALESCE(p_related_message_id, 'N/A'), p_ss_log_id;

    v_surescripts_error_code   := p_message_json#>>'{Message,Body,Error,Code}';
    v_surescripts_desc_code    := p_message_json#>>'{Message,Body,Error,DescriptionCode}';
    v_surescripts_description  := p_message_json#>>'{Message,Body,Error,Description}';

    RAISE LOG 'Surescripts Error Details - Code: %, DescCode: %, Desc: %', v_surescripts_error_code, v_surescripts_desc_code, v_surescripts_description;

    INSERT INTO ss_error_log (
        error_message, error_context, error_type, 
        table_name, table_id, application_name, additional_details, 
        surescripts_error_code, surescripts_desc_code
    ) VALUES (
        COALESCE(v_surescripts_description, 'Surescripts Error received without description'), 
        'RelatedMessageID: ' || COALESCE(p_related_message_id, 'N/A') || '; FullMessage: ' || p_message_json::TEXT, 
        'SURESCRIPTS_ERROR_RESPONSE', 
        'form_ss_log', 
        p_ss_log_id, 
        'SURESCRIPTS_ERROR_HANDLER', 
        p_message_json, 
        v_surescripts_error_code, 
        v_surescripts_desc_code
    );

    IF p_related_message_id IS NOT NULL THEN
        SELECT msg.id INTO v_target_ss_message_id
        FROM form_ss_message msg 
        WHERE msg.message_id = p_related_message_id 
        AND msg.deleted IS NOT TRUE AND msg.archived IS NOT TRUE
        ORDER BY msg.created_on DESC LIMIT 1;

        IF v_target_ss_message_id IS NOT NULL THEN
            RAISE LOG 'Found related form_ss_message ID: % for Error update.', v_target_ss_message_id;
            UPDATE form_ss_message
            SET 
                processed = 'Yes',
                processed_dt = CURRENT_TIMESTAMP,
                message_status = 'Error',
                error_code_id = NULL, -- Set old FK to NULL, not using list table
                error_desc_code_id = NULL, -- Set old FK to NULL, not using list table
                surescripts_error_code = v_surescripts_error_code, -- Store raw text code
                surescripts_error_desc_code = v_surescripts_desc_code, -- Store raw text desc code
                error_description = COALESCE(v_surescripts_description, 'Surescripts reported an unspecified error.') -- Use correct variable
            WHERE id = v_target_ss_message_id;
            
            RAISE LOG 'Updated form_ss_message ID: % with Error info.', v_target_ss_message_id;
        ELSE
            RAISE WARNING 'Error response received for unknown related message ID: %', p_related_message_id;
            -- Error already logged to ss_error_log above
        END IF;
    ELSE
        RAISE WARNING 'Error message received without a RelatesToMessageID. ss_log_id: %', p_ss_log_id;
        -- Error already logged to ss_error_log above
    END IF;

    -- Determine the user-facing error message
    v_user_facing_error_message := COALESCE(v_surescripts_description, 'Surescripts reported an error. Code: ' || COALESCE(v_surescripts_error_code, 'N/A') || ', DescCode: ' || COALESCE(v_surescripts_desc_code, 'N/A'));

    RETURN QUERY SELECT NULL::INTEGER, v_user_facing_error_message, NULL::JSONB; -- No JSON error to send back when successfully processing their error.

EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_error_text = MESSAGE_TEXT, v_error_context = PG_EXCEPTION_CONTEXT;
        RAISE LOG 'Critical Error in handle_ss_error_response for ss_log_id: %. Error: %, Context: %', p_ss_log_id, v_error_text, v_error_context;
        DECLARE
            v_json_error_payload JSONB;
        BEGIN
            -- Attempt to get the header of the message that caused the error processing
            BEGIN
                v_original_header := p_message_json->'Message'->'Header';
            EXCEPTION WHEN OTHERS THEN
                v_original_header := NULL; -- Safety net if p_message_json is malformed
            END;

            INSERT INTO ss_error_log (
                error_message, error_context, error_type,
                table_name, table_id, application_name, additional_details
            ) VALUES (
                'CRITICAL: ' || v_error_text, v_error_context, 'ERROR_HANDLER_EXCEPTION',
                'form_ss_log', p_ss_log_id, 'SURESCRIPTS_ERROR_HANDLER', p_message_json
            );

            v_json_error_payload := jsonb_build_object(
                'Message', jsonb_build_object(
                    'Header', jsonb_build_object(
                        'RelatesToMessageID', v_original_header->>'MessageID', -- Error in processing their error message
                        'To', v_original_header->>'From',
                        'From', v_original_header->>'To',
                        'SentTime', TO_CHAR(NOW() AT TIME ZONE 'UTC', 'YYYYMMDDHH24MISS'),
                        'PrescriberOrderNumber', v_original_header->>'PrescriberOrderNumber'
                    ),
                    'Body', jsonb_build_object(
                        'Error', jsonb_build_object(
                            'Code', 'T500', -- Generic "unable to process"
                            'DescriptionCode', '99', -- Other
                            'Description', 'Internal server error processing received Surescripts Error message.'
                        )
                    )
                )
            );
            IF v_original_header IS NULL THEN -- If we couldn't even get the header, make a very generic error
                 v_json_error_payload := jsonb_build_object(
                    'Message', jsonb_build_object(
                        'Header', jsonb_build_object(
                            'SentTime', TO_CHAR(NOW() AT TIME ZONE 'UTC', 'YYYYMMDDHH24MISS')
                        ),
                        'Body', jsonb_build_object(
                            'Error', jsonb_build_object(
                                'Code', 'T500',
                                'DescriptionCode', '99',
                                'Description', 'Critical internal server error processing malformed Surescripts Error message.'
                            )
                        )
                    )
                );
            END IF;

            RETURN QUERY SELECT NULL::INTEGER,
                                ('Critical error processing Surescripts Error message (Log ID: ' || p_ss_log_id || '): ' || v_error_text)::TEXT,
                                v_json_error_payload;
        END;
END; $$;


DROP FUNCTION IF EXISTS _insert_ss_message_and_subforms(ss_message_main_params_type, ss_allergy_type[], ss_benefit_type[], ss_observation_type[], ss_due_type[], ss_compound_type[], ss_diagnosis_type[], ss_codified_note_type[], INTEGER) CASCADE;

CREATE OR REPLACE FUNCTION _insert_ss_message_and_subforms(
    p_main_params ss_message_main_params_type, 
    p_allergies ss_allergy_type[] DEFAULT ARRAY[]::ss_allergy_type[],
    p_benefits ss_benefit_type[] DEFAULT ARRAY[]::ss_benefit_type[],
    p_observations ss_observation_type[] DEFAULT ARRAY[]::ss_observation_type[],
    p_dues ss_due_type[] DEFAULT ARRAY[]::ss_due_type[],
    p_compounds ss_compound_type[] DEFAULT ARRAY[]::ss_compound_type[],
    p_diagnoses ss_diagnosis_type[] DEFAULT ARRAY[]::ss_diagnosis_type[],
    p_codified_notes ss_codified_note_type[] DEFAULT ARRAY[]::ss_codified_note_type[],
    p_ss_log_id INTEGER DEFAULT NULL::INTEGER
)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_new_ss_message_id INTEGER;
    v_subform_item RECORD;
    v_allergy_id INTEGER;
    v_benefit_id INTEGER;
    v_observation_id INTEGER;
    v_due_id INTEGER;
    v_compound_id INTEGER;
    v_diagnosis_id INTEGER;
    v_codified_note_id INTEGER;
    v_error_text TEXT;
    v_error_context TEXT;
    v_created_by_user_id INTEGER := 1;

BEGIN
    RAISE LOG 'Inserting form_ss_message for type: %, ss_log_id: %', p_main_params.ss_message_type, p_ss_log_id;
    RAISE NOTICE 'DEBUG: p_main_params before INSERT: %', row_to_json(p_main_params);
    RAISE NOTICE 'DIAGNOSTIC _insert_ss_message_and_subforms received p_main_params.ss_fu_prescriber_npi: %', p_main_params.ss_fu_prescriber_npi;
    RAISE NOTICE 'DIAGNOSTIC _insert_ss_message_and_subforms received p_main_params.ss_fu_prescriber_spec_id: %', p_main_params.ss_fu_prescriber_spec_id;
    RAISE NOTICE 'DEBUG _insert_ss_message_and_subforms: p_main_params.strength before INSERT: %', p_main_params.strength;
    RAISE NOTICE 'DEBUG: p_main_params.ss_patient_first_name = %, ss_patient_last_name = %', p_main_params.ss_patient_first_name, p_main_params.ss_patient_last_name;

    INSERT INTO form_ss_message (
        digital_signature_indicator,
        direction,
        sent_dt,
        priority_flag,
        processed,
        message_status,
        send_to,       
        sent_from,     
        message_id,    
        related_message_id, 
        physician_order_id,
        pharmacy_rx_no,     
        message_type,       
        sender_software_developer,
        sender_software_product,
        sender_software_version,
        order_group_no,
        rx_group_no,
        order_group_icnt,
        rx_group_icnt,
        order_group_tcnt,
        rx_group_tcnt,
        order_group_reason,
        rx_group_reason,
        patient_id,    
        physician_id,  
        pharmacy_order_id, 
        site_id,       
        patient_name_display,
        patient_first_name,
        patient_last_name,
        patient_middle_name,
        patient_dob,
        patient_gender,
        patient_ssn,
        patient_home_street_1,
        patient_home_street_2,
        patient_home_city,
        patient_home_state,
        patient_home_zip,
        patient_phone,
        patient_mrn,
        patient_medicare,
        patient_medicaid,
        patient_rems,
        nka, 
        prescriber_name_display,
        prescriber_npi,
        prescriber_dea,
        prescriber_rems,
        prescriber_state_cs_lic,
        prescriber_medicare,
        prescriber_medicaid,
        prescriber_state_lic,
        prescriber_certificate_to_prescribe,
        prescriber_2000waiver_id,
        prescriber_spec_id, 
        prescriber_last_name,
        prescriber_first_name,
        prescriber_address_1,
        prescriber_address_2,
        prescriber_city,
        prescriber_state,
        prescriber_zip,
        prescriber_phone,
        prescriber_extension,
        prescriber_fax,
        prescriber_loc_name,
        prescriber_loc_ncpdp_id,
        prescriber_loc_dea,
        prescriber_loc_rems,
        prescriber_loc_state_cs_lic,
        prescriber_loc_medicare,
        prescriber_loc_medicaid,
        prescriber_loc_state_lic,
        prescriber_agent_first_name,
        prescriber_agent_last_name,
        prescriber_has_license,
        fu_prescriber_last_name,
        fu_prescriber_first_name,
        fu_prescriber_address_1,
        fu_prescriber_address_2,
        fu_prescriber_city,
        fu_prescriber_state,
        fu_prescriber_zip,
        fu_prescriber_phone,
        fu_prescriber_extension,
        fu_prescriber_fax,
        fu_prescriber_npi,
        fu_prescriber_dea,
        fu_prescriber_rems,
        fu_prescriber_state_cs_lic,
        fu_prescriber_medicare,
        fu_prescriber_medicaid,
        fu_prescriber_state_lic,
        fu_prescriber_certificate_to_prescribe,
        fu_prescriber_2000waiver_id,
        fu_prescriber_spec_id, 
        fu_prescriber_has_license,
        supervisor_last_name,
        supervisor_first_name,
        supervisor_npi,
        supervisor_dea,
        supervisor_rems,
        supervisor_state_cs_lic,
        supervisor_medicare,
        supervisor_medicaid,
        supervisor_state_lic,
        supervisor_certificate_to_prescribe,
        supervisor_2000waiver_id,
        supervisor_spec_id, 
        supervisor_phone,
        supervisor_extension,
        supervisor_fax,
        supervisor_has_license,
        description,
        product_code_qualifier_id, 
        product_code,
        fdb_id,
        drug_db_qualifier_id, 
        drug_db_code,
        dea_schedule_id, 
        strength,
        strength_form_id, 
        strength_uom_id,  
        quantity,
        quantity_qualifier_id, 
        quantity_uom_id,     
        days_supply,
        compound_dosage_form_id, 
        written_date,
        start_date,
        expiration_date,
        effective_date,
        daw,
        daw_code_reason,
        refills,
        pa_number,
        pa_status_id, 
        do_not_fill,
        note,
        sig,
        delivery_request,
        delivery_location,
        flavoring_requested,
        prescriber_checked_rems,
        rems_risk_category,
        rems_authorization_number,
        clinical_info_qualifier,
        prohibit_renewal_request,
        status_icons,
        show_options,
        processed_dt,
        renewal_status, 
        renewal_denial_reason_code, 
        renewal_denied_reason, 
        renewal_note, 
        cancel_status, 
        chg_type_id,
        chg_status,
        chg_denied_reason,
        chg_approved_note,
        chg_validated_note,
        created_on,
        created_by,
        archived,
        deleted
    ) VALUES (
        p_main_params.ss_digital_signature_indicator,
        'IN',
        p_main_params.ss_sent_dt,
        p_main_params.ss_priority_flag, 
        'Yes',
        'Verified', 
        p_main_params.ss_to,
        p_main_params.ss_from,
        p_main_params.ss_message_id_header,
        p_main_params.ss_related_message_id,
        p_main_params.ss_prescriber_order_number,
        p_main_params.ss_pharmacy_rx_no,
        p_main_params.ss_message_type,
        p_main_params.sender_software_developer,
        p_main_params.sender_software_product,
        p_main_params.sender_software_version,
        p_main_params.order_group_no,
        p_main_params.rx_group_no,
        p_main_params.order_group_icnt,
        p_main_params.rx_group_icnt,
        p_main_params.order_group_tcnt,
        p_main_params.rx_group_tcnt,
        p_main_params.order_group_reason_code,
        p_main_params.rx_group_reason_code,
        p_main_params.resolved_patient_id,
        p_main_params.resolved_physician_id,
        p_main_params.resolved_pharmacy_order_id,
        p_main_params.resolved_site_id,
        p_main_params.ss_patient_name_display,
        p_main_params.ss_patient_first_name,
        p_main_params.ss_patient_last_name,
        p_main_params.ss_patient_middle_name,
        p_main_params.ss_patient_dob,
        p_main_params.ss_patient_gender,
        p_main_params.ss_patient_ssn,
        p_main_params.ss_patient_home_street_1,
        p_main_params.ss_patient_home_street_2,
        p_main_params.ss_patient_home_city,
        p_main_params.ss_patient_home_state,
        p_main_params.ss_patient_home_zip,
        p_main_params.ss_patient_phone,
        p_main_params.ss_patient_mrn,
        p_main_params.ss_patient_medicare,
        p_main_params.ss_patient_medicaid,
        p_main_params.ss_patient_rems,
        p_main_params.ss_nka,
        p_main_params.ss_prescriber_name_display,
        p_main_params.ss_prescriber_npi,
        p_main_params.ss_prescriber_dea,
        p_main_params.ss_prescriber_rems, 
        p_main_params.ss_prescriber_state_cs_lic,
        p_main_params.ss_prescriber_medicare,
        p_main_params.ss_prescriber_medicaid,
        p_main_params.ss_prescriber_state_lic,
        p_main_params.ss_prescriber_certificate_to_prescribe,
        p_main_params.ss_prescriber_2000waiver_id,
        p_main_params.ss_prescriber_spec_id, 
        p_main_params.ss_prescriber_last_name,
        p_main_params.ss_prescriber_first_name,
        p_main_params.ss_prescriber_address_1,
        p_main_params.ss_prescriber_address_2,
        p_main_params.ss_prescriber_city,
        p_main_params.ss_prescriber_state,
        p_main_params.ss_prescriber_zip,
        p_main_params.ss_prescriber_phone,
        p_main_params.ss_prescriber_extension,
        p_main_params.ss_prescriber_fax,
        p_main_params.ss_prescriber_loc_name,
        p_main_params.ss_prescriber_loc_ncpdp_id,
        p_main_params.ss_prescriber_loc_dea,
        p_main_params.ss_prescriber_loc_rems,
        p_main_params.ss_prescriber_loc_state_cs_lic,
        p_main_params.ss_prescriber_loc_medicare,
        p_main_params.ss_prescriber_loc_medicaid,
        p_main_params.ss_prescriber_loc_state_lic,
        p_main_params.ss_prescriber_agent_first_name,
        p_main_params.ss_prescriber_agent_last_name,
        CASE WHEN p_main_params.ss_prescriber_has_license IS NULL THEN NULL ELSE '{' || array_to_string(p_main_params.ss_prescriber_has_license, ',') || '}' END,
        p_main_params.ss_fu_prescriber_last_name,
        p_main_params.ss_fu_prescriber_first_name,
        p_main_params.ss_fu_prescriber_address_1,
        p_main_params.ss_fu_prescriber_address_2,
        p_main_params.ss_fu_prescriber_city,
        p_main_params.ss_fu_prescriber_state,
        p_main_params.ss_fu_prescriber_zip,
        p_main_params.ss_fu_prescriber_phone,
        p_main_params.ss_fu_prescriber_extension,
        p_main_params.ss_fu_prescriber_fax,
        p_main_params.ss_fu_prescriber_npi,
        p_main_params.ss_fu_prescriber_dea,
        p_main_params.ss_fu_prescriber_rems,
        p_main_params.ss_fu_prescriber_state_cs_lic,
        p_main_params.ss_fu_prescriber_medicare,
        p_main_params.ss_fu_prescriber_medicaid,
        p_main_params.ss_fu_prescriber_state_lic,
        p_main_params.ss_fu_prescriber_certificate_to_prescribe,
        p_main_params.ss_fu_prescriber_2000waiver_id,
        p_main_params.ss_fu_prescriber_spec_id, 
        CASE WHEN p_main_params.ss_fu_prescriber_has_license IS NULL THEN NULL ELSE '{' || array_to_string(p_main_params.ss_fu_prescriber_has_license, ',') || '}' END,
        p_main_params.ss_supervisor_last_name,
        p_main_params.ss_supervisor_first_name,
        p_main_params.ss_supervisor_npi,
        p_main_params.ss_supervisor_dea,
        p_main_params.ss_supervisor_rems,
        p_main_params.ss_supervisor_state_cs_lic,
        p_main_params.ss_supervisor_medicare,
        p_main_params.ss_supervisor_medicaid,
        p_main_params.ss_supervisor_state_lic,
        p_main_params.ss_supervisor_certificate_to_prescribe,
        p_main_params.ss_supervisor_2000waiver_id,
        p_main_params.ss_supervisor_spec_id, 
        p_main_params.ss_supervisor_phone,
        p_main_params.ss_supervisor_extension,
        p_main_params.ss_supervisor_fax,
        CASE WHEN p_main_params.ss_supervisor_has_license IS NULL THEN NULL ELSE '{' || array_to_string(p_main_params.ss_supervisor_has_license, ',') || '}' END,
        p_main_params.description,
        p_main_params.product_code_qualifier_id, 
        p_main_params.product_code,
        p_main_params.fdb_id,
        p_main_params.drug_db_qualifier_id, 
        p_main_params.drug_db_code,
        p_main_params.dea_schedule_id, 
        p_main_params.strength,
        p_main_params.strength_form_id, 
        p_main_params.strength_uom_id,  
        p_main_params.quantity,
        p_main_params.quantity_qualifier_id, 
        p_main_params.quantity_uom_id,     
        p_main_params.days_supply,
        p_main_params.compound_dosage_form_id, 
        p_main_params.written_date,
        p_main_params.start_date,
        p_main_params.expiration_date,
        p_main_params.effective_date,
        p_main_params.daw,
        p_main_params.daw_code_reason,
        p_main_params.refills,
        p_main_params.pa_number,
        p_main_params.pa_status_id, 
        p_main_params.do_not_fill,
        p_main_params.note,
        p_main_params.sig,
        p_main_params.delivery_request,
        p_main_params.delivery_location,
        p_main_params.flavoring_requested,
        p_main_params.prescriber_checked_rems,
        p_main_params.rems_risk_category,
        p_main_params.rems_authorization_number,
        p_main_params.clinical_info_qualifier,
        p_main_params.prohibit_renewal_request,
        p_main_params.status_icons,
        p_main_params.show_options,
        p_main_params.processed_dt,
        p_main_params.ss_renewal_status,
        p_main_params.ss_renewal_denial_reason_code,
        p_main_params.ss_renewal_denied_reason,
        p_main_params.ss_renewal_note,
        p_main_params.ss_cancel_status,
        p_main_params.ss_chg_type_id,
        p_main_params.ss_chg_status,
        p_main_params.ss_chg_denied_reason,
        p_main_params.ss_chg_approved_note,
        p_main_params.ss_chg_validated_note,
        CURRENT_TIMESTAMP,
        v_created_by_user_id,
        FALSE,
        FALSE
    ) RETURNING id INTO v_new_ss_message_id;

    RAISE LOG 'Inserted form_ss_message with ID: % for ss_log_id: %', v_new_ss_message_id, p_ss_log_id;
    RAISE NOTICE 'DEBUG: Inserted form_ss_message row: %', row_to_json((SELECT t FROM (SELECT * FROM form_ss_message WHERE id = v_new_ss_message_id LIMIT 1) t));

    -- Insert Allergies
    RAISE NOTICE 'DEBUG _insert_ss_message_and_subforms: Allergies array length: %', array_length(p_allergies, 1);
    IF array_length(p_allergies, 1) > 0 THEN
        RAISE LOG 'Inserting % allergies for ss_message_id: %', array_length(p_allergies, 1), v_new_ss_message_id;
        BEGIN
            FOR v_subform_item IN SELECT * FROM unnest(p_allergies)
            LOOP
                RAISE NOTICE 'DEBUG _insert_ss_message_and_subforms: Processing allergy item: %', v_subform_item;
                INSERT INTO form_ss_allergy (
                    direction,
                    source,
                    effective_date,
                    expiration_date,
                    adverse_event_code_id,
                    adverse_event_text,
                    drug_product_code,
                    drug_product_qualifier_id,
                    drug_product_text,
                    reaction_code_id,
                    reaction_text,
                    severity_code_id,
                    severity_text,
                    created_on,
                    created_by,
                    archived,
                    deleted
                ) VALUES (
                    'IN',
                    v_subform_item.source,
                    v_subform_item.effective_date,
                    v_subform_item.expiration_date,
                    v_subform_item.adverse_event_code_id,
                    v_subform_item.adverse_event_text,
                    v_subform_item.drug_product_code,
                    v_subform_item.drug_product_qualifier_id,
                    v_subform_item.drug_product_text,
                    v_subform_item.reaction_code_id,
                    v_subform_item.reaction_text,
                    v_subform_item.severity_code_id,
                    v_subform_item.severity_text,
                    CURRENT_TIMESTAMP,
                    v_created_by_user_id,
                    FALSE,
                    FALSE
                ) RETURNING id INTO v_allergy_id;

                INSERT INTO sf_form_ss_message_to_ss_allergy (
                    form_ss_message_fk,
                    form_ss_allergy_fk,
                    created_on,
                    created_by,
                    archive,
                    delete
                ) VALUES (
                    v_new_ss_message_id,
                    v_allergy_id,
                    CURRENT_TIMESTAMP,
                    v_created_by_user_id,
                    FALSE,
                    FALSE
                );
            END LOOP;
        END;
    END IF;

    -- Insert Benefits
    RAISE NOTICE 'DEBUG _insert_ss_message_and_subforms: Benefits array length: %', array_length(p_benefits, 1);
    IF array_length(p_benefits, 1) > 0 THEN
        RAISE LOG 'Inserting % benefits for ss_message_id: %', array_length(p_benefits, 1), v_new_ss_message_id;
        BEGIN
            FOR v_subform_item IN SELECT * FROM unnest(p_benefits)
            LOOP
                RAISE NOTICE 'DEBUG _insert_ss_message_and_subforms: Processing benefit item: %', v_subform_item;
                INSERT INTO form_ss_benefit (
                    direction,
                    patient_id,
                    payer_type_id,
                    payer_level,
                    payer_name,
                    pbm_participant_id,
                    bin,
                    pcn,
                    naic_id,
                    hp_id,
                    person_code,
                    group_id,
                    group_name,
                    pbm_member_id,
                    relationship_code,
                    cardholder_id,
                    cardholder_first_name,
                    cardholder_last_name,
                    payer_phone,
                    payer_fax,
                    prohibit_renewal_request,
                    insurance_id,
                    payer_id,
                    created_on,
                    created_by,
                    archived,
                    deleted
                ) VALUES (
                    'IN',
                    p_main_params.resolved_patient_id,
                    v_subform_item.payer_type_id,
                    v_subform_item.payer_level,
                    v_subform_item.payer_name,
                    v_subform_item.pbm_participant_id,
                    v_subform_item.bin,
                    v_subform_item.pcn,
                    v_subform_item.naic_id,
                    v_subform_item.hp_id,
                    v_subform_item.person_code,
                    v_subform_item.group_id,
                    v_subform_item.group_name,
                    v_subform_item.pbm_member_id,
                    v_subform_item.relationship_code,
                    v_subform_item.cardholder_id,
                    v_subform_item.cardholder_first_name,
                    v_subform_item.cardholder_last_name,
                    v_subform_item.payer_phone,
                    v_subform_item.payer_fax,
                    v_subform_item.prohibit_renewal_request,
                    v_subform_item.insurance_id,
                    v_subform_item.payer_id,
                    CURRENT_TIMESTAMP,
                    v_created_by_user_id,
                    FALSE,
                    FALSE
                ) RETURNING id INTO v_benefit_id;

                INSERT INTO sf_form_ss_message_to_ss_benefit (
                    form_ss_message_fk,
                    form_ss_benefit_fk,
                    created_on,
                    created_by,
                    archive,
                    delete
                ) VALUES (
                    v_new_ss_message_id,
                    v_benefit_id,
                    CURRENT_TIMESTAMP,
                    v_created_by_user_id,
                    FALSE,
                    FALSE
                );
            END LOOP;
        END;
    END IF;

    -- Insert Observations
    RAISE LOG 'DEBUG _insert_ss_message_and_subforms: Observations array length: %', array_length(p_observations, 1);
    IF array_length(p_observations, 1) > 0 THEN
        RAISE LOG 'Inserting % observations for ss_message_id: %', array_length(p_observations, 1), v_new_ss_message_id;
        BEGIN
            FOR v_subform_item IN SELECT * FROM unnest(p_observations)
            LOOP
                RAISE NOTICE 'DEBUG _insert_ss_message_and_subforms: Processing observation item: %', v_subform_item;
                BEGIN
                    RAISE NOTICE 'Attempting to insert observation with type_id: %', v_subform_item.type_id; -- Using v_subform_item directly
                    INSERT INTO form_ss_observation (
                        type_id,
                        loinc_version,
                        value,
                        unit_id,
                        ucum_version,
                        observation_date,
                        notes,
                        created_on,
                        created_by,
                        archived,
                        deleted
                    ) VALUES (
                        v_subform_item.type_id, 
                        v_subform_item.loinc_version,
                        v_subform_item.value,
                        v_subform_item.unit_id,
                        v_subform_item.ucum_version,
                        v_subform_item.observation_date,
                        v_subform_item.notes,
                        CURRENT_TIMESTAMP,
                        v_created_by_user_id, -- Assuming v_created_by_user_id is 1 or valid
                        FALSE,
                        FALSE
                    ) RETURNING id INTO v_observation_id;

                    RAISE NOTICE 'DEBUG: Inserted form_ss_observation with id: %', v_observation_id;

                    IF v_observation_id IS NOT NULL THEN
                        INSERT INTO sf_form_ss_message_to_ss_observation (
                            form_ss_message_fk,
                            form_ss_observation_fk,
                            created_on,
                            created_by,
                            archive,
                            delete
                        ) VALUES (
                            v_new_ss_message_id,
                            v_observation_id,
                            CURRENT_TIMESTAMP,
                            v_created_by_user_id,
                            FALSE,
                            FALSE
                        );
                    ELSE
                        RAISE WARNING 'DEBUG: v_observation_id was NULL after INSERT for item: %', v_subform_item;
                    END IF;
                EXCEPTION
                    WHEN OTHERS THEN
                        GET STACKED DIAGNOSTICS v_error_text = MESSAGE_TEXT, v_error_context = PG_EXCEPTION_CONTEXT;
                        RAISE WARNING 'DEBUG: Error inserting observation/sf link: %, Context: %, Item: %', v_error_text, v_error_context, v_subform_item;
                END;
            END LOOP;
        END;
    END IF;

    -- Insert Compounds
    RAISE NOTICE 'DEBUG _insert_ss_message_and_subforms: Compounds array length: %', array_length(p_compounds, 1);
    IF array_length(p_compounds, 1) > 0 THEN
        RAISE LOG 'Inserting % compounds for ss_message_id: %', array_length(p_compounds, 1), v_new_ss_message_id;
        BEGIN
            FOR v_subform_item IN SELECT * FROM unnest(p_compounds)
            LOOP
                RAISE NOTICE 'DEBUG _insert_ss_message_and_subforms: Processing compound item: %', v_subform_item;
                INSERT INTO form_ss_compound (
                    fdb_id,
                    compound_no,
                    qual_id,
                    code,
                    description,
                    strength,
                    strength_form_id,
                    strength_uom_id,
                    quantity,
                    quantity_qualifier_id,
                    quantity_uom_id,
                    dea_schedule_id,
                    created_on,
                    created_by,
                    archived,
                    deleted
                ) VALUES (
                    v_subform_item.fdb_id,
                    v_subform_item.compound_no,
                    v_subform_item.qual_id,
                    v_subform_item.code,
                    v_subform_item.description,
                    v_subform_item.strength,
                    v_subform_item.strength_form_id,
                    v_subform_item.strength_uom_id,
                    v_subform_item.quantity,
                    v_subform_item.quantity_qualifier_id,
                    v_subform_item.quantity_uom_id,
                    v_subform_item.dea_schedule_id,
                    CURRENT_TIMESTAMP,
                    v_created_by_user_id,
                    FALSE,
                    FALSE
                ) RETURNING id INTO v_compound_id;
                INSERT INTO sf_form_ss_message_to_ss_compound (
                    form_ss_message_fk,
                    form_ss_compound_fk,
                    created_on,
                    created_by,
                    archive,
                    delete
                ) VALUES (
                    v_new_ss_message_id,
                    v_compound_id,
                    CURRENT_TIMESTAMP,
                    v_created_by_user_id,
                    FALSE,
                    FALSE
                );
            END LOOP;
        END;
    END IF;

    -- Insert Diagnoses
    RAISE NOTICE 'DEBUG _insert_ss_message_and_subforms: Diagnoses array length: %', array_length(p_diagnoses, 1);
    IF array_length(p_diagnoses, 1) > 0 THEN
        RAISE LOG 'Inserting % diagnoses for ss_message_id: %', array_length(p_diagnoses, 1), v_new_ss_message_id;
        BEGIN
            FOR v_subform_item IN SELECT * FROM unnest(p_diagnoses)
            LOOP
                RAISE NOTICE 'DEBUG _insert_ss_message_and_subforms: Processing diagnosis item: %', v_subform_item;
                DECLARE
                    diag_dx_id TEXT := v_subform_item.dx_id;
                    diag_type TEXT := v_subform_item.type;
                    diag_dx_code TEXT := v_subform_item.dx_code;
                    diag_dx_code_qualifier_id TEXT := v_subform_item.dx_code_qualifier_id;
                    diag_dx_desc TEXT := v_subform_item.dx_desc;
                BEGIN
                    RAISE NOTICE 'Attempting to insert diagnosis with type: %, dx_code: %', diag_type, diag_dx_code;
                    INSERT INTO form_ss_diagnosis (
                        dx_id, type, dx_code, dx_code_qualifier_id, dx_desc,
                        created_on, created_by, archived, deleted
                    ) VALUES (
                        diag_dx_id,
                        diag_type,
                        diag_dx_code,
                        diag_dx_code_qualifier_id, 
                        diag_dx_desc,
                        CURRENT_TIMESTAMP,
                        v_created_by_user_id,
                        FALSE,
                        FALSE
                    ) RETURNING id INTO v_diagnosis_id;

                    RAISE NOTICE 'DEBUG: Inserted form_ss_diagnosis with id: %', v_diagnosis_id;

                    IF v_diagnosis_id IS NOT NULL THEN
                        INSERT INTO sf_form_ss_message_to_ss_diagnosis (
                            form_ss_message_fk,
                            form_ss_diagnosis_fk,
                            created_on,
                            created_by,
                            archive,
                            delete
                        ) VALUES (
                            v_new_ss_message_id,
                            v_diagnosis_id,
                            CURRENT_TIMESTAMP,
                            v_created_by_user_id,
                            FALSE,
                            FALSE
                        );
                    ELSE
                        RAISE WARNING 'DEBUG: v_diagnosis_id was NULL after INSERT for item: %', v_subform_item;
                    END IF;
                EXCEPTION
                    WHEN OTHERS THEN
                        GET STACKED DIAGNOSTICS v_error_text = MESSAGE_TEXT, v_error_context = PG_EXCEPTION_CONTEXT;
                        RAISE WARNING 'DEBUG: Error inserting diagnosis/sf link: %, Context: %, Item: %', v_error_text, v_error_context, v_subform_item;
                END;
            END LOOP;
        END;
    END IF;
    
    -- Insert Codified Notes
    RAISE NOTICE 'DEBUG _insert_ss_message_and_subforms: Codified Notes array length: %', array_length(p_codified_notes, 1);
    IF array_length(p_codified_notes, 1) > 0 THEN
        RAISE LOG 'Inserting % codified notes for ss_message_id: %', array_length(p_codified_notes, 1), v_new_ss_message_id;
        BEGIN
            FOR v_subform_item IN SELECT * FROM unnest(p_codified_notes)
            LOOP
                RAISE NOTICE 'DEBUG _insert_ss_message_and_subforms: Processing codified_note item: %', v_subform_item;
                INSERT INTO form_ss_codified_note (
                    qualifier_id, 
                    value,
                    created_on,
                    created_by,
                    archived,
                    deleted
                ) VALUES (
                    v_subform_item.qualifier_id,
                    v_subform_item.value,
                    CURRENT_TIMESTAMP,
                    v_created_by_user_id,
                    FALSE,
                    FALSE
                ) RETURNING id INTO v_codified_note_id;

                INSERT INTO sf_form_ss_message_to_ss_codified_note (
                    form_ss_message_fk,
                    form_ss_codified_note_fk,
                    created_on,
                    created_by,
                    archive,
                    delete
                ) VALUES (
                    v_new_ss_message_id,
                    v_codified_note_id,
                    CURRENT_TIMESTAMP, 
                    v_created_by_user_id, 
                    FALSE, 
                    FALSE
                );
            END LOOP;
        END;
    END IF;

    -- Insert DUEs (Drug Use Evaluation)
    RAISE NOTICE 'DEBUG _insert_ss_message_and_subforms: DUEs array length: %', array_length(p_dues, 1);
    IF array_length(p_dues, 1) > 0 THEN
        RAISE LOG 'Inserting % DUEs for ss_message_id: %', array_length(p_dues, 1), v_new_ss_message_id;
        BEGIN
            FOR v_subform_item IN SELECT * FROM unnest(p_dues)
            LOOP
                RAISE NOTICE 'DEBUG _insert_ss_message_and_subforms: Processing DUE item: %', v_subform_item;
                INSERT INTO form_ss_due (
                    direction,
                    compound_no,
                    coagent_id,
                    service_reason_id,
                    pservice_rsn_id,
                    coagent_code,
                    coagent_qualifier_id,
                    coagent_description,
                    clinical_significance,
                    ack_reason,
                    service_res_id,
                    created_on,
                    created_by,
                    archived,
                    deleted
                ) VALUES (
                    'IN',
                    v_subform_item.compound_no,
                    v_subform_item.coagent_id,
                    v_subform_item.service_reason_id,
                    v_subform_item.pservice_rsn_id,
                    v_subform_item.coagent_code,
                    v_subform_item.coagent_qualifier_id,
                    v_subform_item.coagent_description,
                    v_subform_item.clinical_significance,
                    v_subform_item.ack_reason,
                    v_subform_item.service_res_id,
                    CURRENT_TIMESTAMP,
                    v_created_by_user_id,
                    FALSE,
                    FALSE
                ) RETURNING id INTO v_due_id;

                INSERT INTO sf_form_ss_message_to_ss_due (
                    form_ss_message_fk,
                    form_ss_due_fk,
                    created_on,
                    created_by,
                    archive,
                    delete
                ) VALUES (
                    v_new_ss_message_id,
                    v_due_id,
                    CURRENT_TIMESTAMP,
                    v_created_by_user_id,
                    FALSE,
                    FALSE
                );

                DECLARE
                    v_res_code_item TEXT;
                    v_list_res_code_fk INTEGER;
                BEGIN
                    IF v_subform_item.service_res_id IS NOT NULL THEN
                        FOREACH v_res_code_item IN ARRAY v_subform_item.service_res_id
                        LOOP
                            INSERT INTO gr_form_ss_due_service_res_id_to_list_ss_service_result_code_id (form_ss_due_fk, form_list_ss_service_result_code_fk)
                            VALUES (v_due_id, v_res_code_item);
                        END LOOP;
                    END IF;
                END;
            END LOOP;
        END;
    END IF;

    -- Insert drug_cvg_status_id into GR table
    IF p_main_params.drug_cvg_status_id IS NOT NULL AND array_length(p_main_params.drug_cvg_status_id, 1) > 0 THEN
        RAISE LOG 'Inserting % drug_cvg_status_id links for ss_message_id: %', array_length(p_main_params.drug_cvg_status_id, 1), v_new_ss_message_id;
        DECLARE
            v_cvg_code TEXT;
            v_list_cvg_fk INTEGER;
        BEGIN
            FOREACH v_cvg_code IN ARRAY p_main_params.drug_cvg_status_id
            LOOP
                INSERT INTO gr_form_ss_message_drug_cvg_status_id_to_list_ss_cvg_status_id (form_ss_message_fk, form_list_ss_cvg_status_fk)
                VALUES (v_new_ss_message_id, v_cvg_code);
            END LOOP;
        END;
    END IF;

    -- Insert chg_type_sc_id into GR table
    IF p_main_params.ss_chg_type_sc_id IS NOT NULL AND array_length(p_main_params.ss_chg_type_sc_id, 1) > 0 THEN
        RAISE LOG 'Inserting % chg_type_sc_id links for ss_message_id: %', array_length(p_main_params.ss_chg_type_sc_id, 1), v_new_ss_message_id;
        DECLARE
            v_chg_sc_code TEXT;
            v_list_chg_sc_fk INTEGER;
        BEGIN
            FOREACH v_chg_sc_code IN ARRAY p_main_params.ss_chg_type_sc_id
            LOOP
                INSERT INTO gr_form_ss_message_chg_type_sc_id_to_list_ss_chg_subcode_id (form_ss_message_fk, form_list_ss_chg_subcode_fk)
                VALUES (v_new_ss_message_id, v_chg_sc_code);
            END LOOP;
        END;
    END IF;

    -- Insert chg_sc_aw_chg_id into GR table
    IF p_main_params.ss_chg_sc_aw_chg_id IS NOT NULL AND array_length(p_main_params.ss_chg_sc_aw_chg_id, 1) > 0 THEN
        RAISE LOG 'Inserting % chg_sc_aw_chg_id links for ss_message_id: %', array_length(p_main_params.ss_chg_sc_aw_chg_id, 1), v_new_ss_message_id;
        DECLARE
            v_chg_sc_aw_code TEXT;
            v_list_chg_sc_aw_fk INTEGER;
        BEGIN
            FOREACH v_chg_sc_aw_code IN ARRAY p_main_params.ss_chg_sc_aw_chg_id
            LOOP
                INSERT INTO gr_form_ss_message_chg_allowed_chg_id_to_list_ss_chg_code_id (form_ss_message_fk, form_list_ss_chg_subcode_fk)
                VALUES (v_new_ss_message_id, v_chg_sc_aw_code);
            END LOOP;
        END;
    END IF;

    -- Insert ss_chg_dr_code_id into GR table
    IF p_main_params.ss_chg_dr_code_id IS NOT NULL AND array_length(p_main_params.ss_chg_dr_code_id, 1) > 0 THEN
        RAISE LOG 'Inserting % ss_chg_dr_code_id links for ss_message_id: %', array_length(p_main_params.ss_chg_dr_code_id, 1), v_new_ss_message_id;
        DECLARE
            v_denial_reason_code TEXT;
        BEGIN
            FOREACH v_denial_reason_code IN ARRAY p_main_params.ss_chg_dr_code_id
            LOOP
                INSERT INTO gr_form_ss_message_chg_dr_code_id_to_list_ss_denial_reason_id (form_ss_message_fk, form_list_ss_denial_reason_fk)
                VALUES (v_new_ss_message_id, v_denial_reason_code);
            END LOOP;
        END;
    END IF;

    -- Insert ss_chg_vr_cd_id into GR table
    IF p_main_params.ss_chg_vr_cd_id IS NOT NULL AND array_length(p_main_params.ss_chg_vr_cd_id, 1) > 0 THEN
        RAISE LOG 'Inserting % ss_chg_vr_cd_id links for ss_message_id: %', array_length(p_main_params.ss_chg_vr_cd_id, 1), v_new_ss_message_id;
        DECLARE
            v_validation_reason_code TEXT;
        BEGIN
            FOREACH v_validation_reason_code IN ARRAY p_main_params.ss_chg_vr_cd_id
            LOOP
                INSERT INTO gr_form_ss_message_chg_vr_cd_id_to_list_ss_chg_valid_reason_id (form_ss_message_fk, form_list_ss_chg_valid_reason_fk)
                VALUES (v_new_ss_message_id, v_validation_reason_code);
            END LOOP;
        END;
    END IF;

    RETURN v_new_ss_message_id;

EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_error_text = MESSAGE_TEXT, v_error_context = PG_EXCEPTION_CONTEXT;
        RAISE LOG 'Error in _insert_ss_message_and_subforms for ss_log_id: %. Error: %, Context: %', p_ss_log_id, v_error_text, v_error_context;
        BEGIN
            INSERT INTO ss_error_log (
                error_message, error_context, error_type,
                table_name, table_id, application_name, additional_details
            ) VALUES (
                v_error_text, v_error_context, 'INSERT_SUBFORM_EXCEPTION',
                'form_ss_log', p_ss_log_id, 'SURESCRIPTS_INBOUND_PARSER', NULL
            );
        EXCEPTION WHEN OTHERS THEN RAISE WARNING 'Failed to write to ss_error_log during _insert_ss_message_and_subforms exception: % ', SQLERRM; END;
        RAISE;
END;
$$;

CREATE OR REPLACE FUNCTION ensure_jsonb_array(p_input JSONB)
RETURNS JSONB LANGUAGE plpgsql IMMUTABLE AS $$
BEGIN
    IF p_input IS NULL OR jsonb_typeof(p_input) = 'null' THEN 
        RETURN '[]'::JSONB; 
    END IF;
    IF jsonb_typeof(p_input) = 'array' THEN 
        RETURN p_input; 
    END IF;
    IF jsonb_typeof(p_input) = 'object' THEN 
        RETURN jsonb_build_array(p_input); 
    END IF;
    -- This case should ideally not be reached if p_input is a valid JSONB structure part 
    -- that is expected to be an array or an object that can be wrapped in an array.
    -- However, returning empty array for unexpected primitive types found at array paths.
    RAISE WARNING 'ensure_jsonb_array received unexpected JSONB type: %', jsonb_typeof(p_input);
    RETURN '[]'::JSONB; 
END;
$$;
