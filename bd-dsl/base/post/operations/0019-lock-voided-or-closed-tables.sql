
CREATE OR REPLACE FUNCTION prevent_update_conditionally_function()
RETURNS TRIGGER AS $$
DECLARE
    v_old_invoice_status TEXT;
    v_new_invoice_status TEXT;
BEGIN
  -- Skip if trigger flag is set
  IF current_setting('clara.prevent_locked_checks', true) = 'on' THEN
    RAISE LOG 'prevent_locked_checks skipped due to flag';
    RETURN NEW;
  END IF;

  IF TG_TABLE_NAME = 'form_billing_invoice' THEN
    v_old_invoice_status := OLD.status;
    v_new_invoice_status := NEW.status;
  ELSE
    v_old_invoice_status := OLD.invoice_status;
    v_new_invoice_status := NEW.invoice_status;
  END IF;

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);
    -- Allow changing void from 'Yes' to NULL (unvoiding)
    -- Block updates if:
    -- 1. close_no exists in both OLD and NEW (no changes to closed records)
    -- 2. locked is 'Yes' in OLD and NEW is not changing it to NULL
    -- 3. void is 'Yes' in OLD and NEW is not changing it to NULL

    IF (
        (OLD.close_no IS NOT NULL AND NEW.close_no IS NOT NULL) OR 
        (COALESCE(OLD.locked, 'No') = 'Yes' AND NEW.locked IS NOT NULL AND v_old_invoice_status = v_new_invoice_status) OR 
        (COALESCE(OLD.zeroed, 'No') = 'Yes') OR
        (COALESCE(OLD.void, 'No') = 'Yes' AND NEW.void IS NOT NULL)
    ) THEN
        RAISE EXCEPTION 'Updates are not allowed on records where close_no is set, locked is Yes, or void is Yes (except when unvoiding/unlocking) Table Name: % Trigger Name: %', TG_TABLE_NAME, TG_NAME;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to the relevant tables
CREATE OR REPLACE TRIGGER prevent_update_form_ledger_charge_line
BEFORE UPDATE ON form_ledger_charge_line
FOR EACH ROW
EXECUTE FUNCTION prevent_update_conditionally_function();

CREATE OR REPLACE TRIGGER prevent_update_form_billing_invoice
BEFORE UPDATE ON form_billing_invoice
FOR EACH ROW
EXECUTE FUNCTION prevent_update_conditionally_function();