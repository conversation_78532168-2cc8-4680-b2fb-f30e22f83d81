-- Function for form_billing_invoice
-- Function to sync discontinued status from order items to rx records
DO $$ BEGIN
  PERFORM drop_all_function_signatures('prevent_invoice_status_change');
END $$;
CREATE OR REPLACE FUNCTION prevent_invoice_status_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Skip if trigger flag is set
    IF current_setting('clara.prevent_locked_checks', true) = 'on' THEN
        RAISE LOG 'prevent_locked_checks skipped due to flag';
        RETURN NEW;
    END IF;
    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    -- Prevent changing status fields if they were already set
    IF TG_OP = 'UPDATE' THEN
        IF COALESCE(OLD.void, 'No') = 'Yes' AND COALESCE(NEW.void, 'No') != COALESCE(OLD.void, 'No') THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context, 
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Cannot change void status once set to Yes',
                'Attempting to update void status',
                TG_OP,
                TG_TABLE_SCHEMA,
                TG_TABLE_NAME,
                jsonb_build_object(
                    'trigger_name', 'prevent_invoice_status_change',
                    'old_void', OLD.void,
                    'new_void', NEW.void
                )
            );
            RAISE EXCEPTION 'Cannot change void status once set to Yes';
        END IF;

        IF COALESCE(OLD.zeroed, 'No') = 'Yes' AND COALESCE(NEW.zeroed, 'No') != COALESCE(OLD.zeroed, 'No') THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Cannot change zeroed status once set to Yes',
                'Attempting to update zeroed status',
                TG_OP,
                TG_TABLE_SCHEMA,
                TG_TABLE_NAME,
                jsonb_build_object(
                    'trigger_name', 'prevent_invoice_status_change',
                    'old_zeroed', OLD.zeroed,
                    'new_zeroed', NEW.zeroed
                )
            );
            RAISE EXCEPTION 'Cannot change zeroed status once set to Yes';
        END IF;

        IF OLD.archived = TRUE AND NEW.archived != OLD.archived THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Cannot change archived status once set to TRUE',
                'Attempting to update archived status',
                TG_OP,
                TG_TABLE_SCHEMA,
                TG_TABLE_NAME,
                jsonb_build_object(
                    'trigger_name', 'prevent_invoice_status_change',
                    'old_archived', OLD.archived,
                    'new_archived', NEW.archived
                )
            );
            RAISE EXCEPTION 'Cannot change archived status once set to TRUE';
        END IF;

        IF OLD.deleted = TRUE AND NEW.deleted != OLD.deleted THEN
            INSERT INTO billing_error_log (
                error_message,
                error_context,
                error_type,
                schema_name,
                table_name,
                additional_details
            ) VALUES (
                'Cannot change deleted status once set to TRUE',
                'Attempting to update deleted status',
                TG_OP,
                TG_TABLE_SCHEMA,
                TG_TABLE_NAME,
                jsonb_build_object(
                    'trigger_name', 'prevent_invoice_status_change',
                    'old_deleted', OLD.deleted,
                    'new_deleted', NEW.deleted
                )
            );
            RAISE EXCEPTION 'Cannot change deleted status once set to TRUE';
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE TRIGGER trg_billing_invoice_status
    BEFORE INSERT OR UPDATE ON form_billing_invoice
    FOR EACH ROW
    EXECUTE FUNCTION prevent_invoice_status_change();
-- Function to sync discontinued status from order items to rx records

CREATE OR REPLACE FUNCTION prevent_ledger_status_change()
RETURNS TRIGGER AS $$
BEGIN

    INSERT INTO trigger_log (trigger_name, trigger_table, trigger_type)
    VALUES (TG_NAME, TG_TABLE_NAME, TG_OP);

    IF TG_OP = 'UPDATE' THEN
        BEGIN
            IF COALESCE(OLD.void, 'No') = 'Yes' AND COALESCE(NEW.void, 'No') != COALESCE(OLD.void, 'No') THEN
                INSERT INTO billing_error_log (
                    error_message,
                    error_context,
                    error_type,
                    schema_name,
                    table_name,
                    additional_details
                ) VALUES (
                    'Cannot change void status once set to Yes',
                    'Attempting to update void status',
                    TG_OP,
                    TG_TABLE_SCHEMA,
                    TG_TABLE_NAME,
                    jsonb_build_object(
                        'trigger_name', 'prevent_ledger_status_change',
                        'old_void', OLD.void,
                        'new_void', NEW.void
                    )
                );
                RAISE EXCEPTION 'Cannot change void status once set to Yes';
            END IF;

            IF OLD.archived = TRUE AND NEW.archived != OLD.archived THEN
                INSERT INTO billing_error_log (
                    error_message,
                    error_context,
                    error_type,
                    schema_name,
                    table_name,
                    additional_details
                ) VALUES (
                    'Cannot change archived status once set to TRUE',
                    'Attempting to update archived status',
                    TG_OP,
                    TG_TABLE_SCHEMA,
                    TG_TABLE_NAME,
                    jsonb_build_object(
                        'trigger_name', 'prevent_ledger_status_change',
                        'old_archived', OLD.archived,
                        'new_archived', NEW.archived
                    )
                );
                RAISE EXCEPTION 'Cannot change archived status once set to TRUE';
            END IF;

            IF COALESCE(OLD.zeroed, 'No') = 'Yes' AND COALESCE(NEW.zeroed, 'No') != COALESCE(OLD.zeroed, 'No') THEN
                INSERT INTO billing_error_log (
                    error_message,
                    error_context,
                    error_type,
                    schema_name,
                    table_name,
                    additional_details
                ) VALUES (
                    'Cannot change zeroed status once set to Yes',
                    'Attempting to update zeroed status',
                    TG_OP,
                    TG_TABLE_SCHEMA,
                    TG_TABLE_NAME,
                        jsonb_build_object(
                            'trigger_name', 'prevent_ledger_status_change',
                            'old_zeroed', OLD.zeroed,
                            'new_zeroed', NEW.zeroed
                        )
                );
                RAISE EXCEPTION 'Cannot change zeroed status once set to Yes';
            END IF;

            IF OLD.deleted = TRUE AND NEW.deleted != OLD.deleted THEN
                INSERT INTO billing_error_log (
                    error_message,
                    error_context,
                    error_type,
                    schema_name,
                    table_name,
                    additional_details
                ) VALUES (
                    'Cannot change deleted status once set to TRUE',
                    'Attempting to update deleted status',
                    TG_OP,
                    TG_TABLE_SCHEMA,
                    TG_TABLE_NAME,
                    jsonb_build_object(
                        'trigger_name', 'prevent_ledger_status_change',
                        'old_deleted', OLD.deleted,
                        'new_deleted', NEW.deleted
                    )
                );
                RAISE EXCEPTION 'Cannot change deleted status once set to TRUE';
            END IF;
        END;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql VOLATILE;

CREATE OR REPLACE TRIGGER trg_ledger_charge_status
    BEFORE INSERT OR UPDATE ON form_ledger_charge_line
    FOR EACH ROW
    EXECUTE FUNCTION prevent_ledger_status_change();