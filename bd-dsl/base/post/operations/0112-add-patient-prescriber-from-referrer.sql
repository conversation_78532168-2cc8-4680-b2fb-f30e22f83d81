DO $$ BEGIN
  PERFORM drop_all_function_signatures('update_patient_prescriber_from_referrer');
END $$;
CREATE OR REPLACE FUNCTION update_patient_prescriber_from_referrer()
RETURNS TRIGGER AS $$
BEGIN
	-- Check if referrer_id is not null
	IF NEW.referrer_id IS NOT NULL THEN
		-- Try to insert a new record into form_patient_prescriber
		BEGIN
			-- Insert only if this physician is not already a prescriber for this patient
			INSERT INTO form_patient_prescriber (
				patient_id,
				physician_id,
				created_on,
				created_by,
				ordering,
				is_primary
			)
			SELECT
				NEW.id,
				NEW.referrer_id,
				NOW(),
				COALESCE(NEW.created_by, 1),
				'Yes',
				'Yes'
			WHERE NOT EXISTS (
				SELECT 1 FROM form_patient_prescriber
				WHERE patient_id = NEW.id AND physician_id = NEW.referrer_id
			);
			
		EXCEPTION WHEN OTHERS THEN
			-- Log error but don't fail the transaction
			RAISE NOTICE 'Failed to insert patient prescriber: %', SQLERRM;
		END;
		END IF;
	
	RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the trigger if it already exists
DROP TRIGGER IF EXISTS trg_update_patient_prescriber_from_referrer ON form_patient;

-- Create the trigger
CREATE TRIGGER trg_update_patient_prescriber_from_referrer
AFTER INSERT OR UPDATE OF referrer_id ON form_patient
FOR EACH ROW
EXECUTE FUNCTION update_patient_prescriber_from_referrer();
