-- ERA Report Query Functions
-- These functions support the ERA (835) report generation

-- Query 1: ERA Site Information
CREATE OR REPLACE FUNCTION era_site_info(p_batch_id INTEGER)
RETURNS TABLE (
    site_name TEXT,
    site_logo JSONB,
    site_address1 TEXT,
    site_address2 TEXT,
    site_city TEXT,
    site_state TEXT,
    site_zip TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT
        s.name::TEXT AS site_name,
        s.logo::JSONB AS site_logo,
        s.address1::TEXT AS site_address1,
        s.address2::TEXT AS site_address2,
        s.city::TEXT AS site_city,
        s.state_id::TEXT AS site_state,
        s.zip::TEXT AS site_zip
    FROM form_med_claim_resp_835_batch batch
    JOIN form_med_claim_resp_835 resp ON resp.response_id = batch.response_id
    JOIN form_site s ON s.id = resp.site_id
    WHERE batch.id = p_batch_id
        AND batch.deleted IS NOT TRUE 
        AND batch.archived IS NOT TRUE
        AND resp.deleted IS NOT TRUE 
        AND resp.archived IS NOT TRUE
        AND s.deleted IS NOT TRUE 
        AND s.archived IS NOT TRUE
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Query 2: ERA Batch Information
CREATE OR REPLACE FUNCTION era_batch_info(p_batch_id INTEGER)
RETURNS TABLE (
    payer_name TEXT,
    payer_identification_number TEXT,
    payer_address_1 TEXT,
    payer_address_2 TEXT,
    payer_city TEXT,
    payer_state TEXT,
    payer_zip TEXT,
    production_date TEXT,
    check_or_eft_trace_number TEXT,
    payment_method TEXT,
    total_actual_provider_payment_amount NUMERIC,
    payee_name TEXT,
    payee_npi TEXT,
    payee_tax_id TEXT,
    payee_ncpdp_number TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        batch.payer_name::TEXT,
        batch.payer_identification_number::TEXT,
        batch.payer_address_1::TEXT,
        batch.payer_address_2::TEXT,
        batch.payer_city::TEXT,
        batch.payer_state::TEXT,
        batch.payer_zip::TEXT,
        TO_CHAR(batch.production_date, 'MM/DD/YYYY') as production_date,
        batch.check_or_eft_trace_number::TEXT,
        CASE batch.payment_method_code
            WHEN 'CHK' THEN 'Check'
            WHEN 'ACH' THEN 'ACH Transfer'
            WHEN 'NON' THEN 'Non-Payment'
            WHEN 'FWT' THEN 'Wire Transfer'
            ELSE batch.payment_method_code
        END::TEXT AS payment_method,
        batch.total_actual_provider_payment_amount::NUMERIC,
        batch.payee_name::TEXT,
        batch.payee_npi::TEXT,
        batch.payee_tax_id::TEXT,
        batch.payee_ncpdp_number::TEXT
    FROM form_med_claim_resp_835_batch batch
    WHERE batch.id = p_batch_id
        AND batch.deleted IS NOT TRUE 
        AND batch.archived IS NOT TRUE;
END;
$$ LANGUAGE plpgsql;

-- Query 3: ERA Patient Groups (grouped by patient)
CREATE OR REPLACE FUNCTION era_patient_groups(p_batch_id INTEGER)
RETURNS TABLE (
    patient_id INTEGER,
    patient_first_name TEXT,
    patient_last_name TEXT,
    patient_mrn TEXT,
    claims JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pt.id::INTEGER AS patient_id,
        resp.patient_first_name::TEXT,
        resp.patient_last_name::TEXT,
        pt.mrn::TEXT AS patient_mrn,
        jsonb_agg(
            jsonb_build_object(
                'claim_id', resp.id,
                'claim_no', resp.claim_no,
                'patient_member_id', resp.patient_member_id,
                'claim_status', CASE 
                    WHEN resp.claim_status_code = '1' THEN 'Processed as Primary'
                    WHEN resp.claim_status_code = '2' THEN 'Processed as Secondary'
                    WHEN resp.claim_status_code = '3' THEN 'Processed as Tertiary'
                    WHEN resp.claim_status_code = '4' THEN 'Denied'
                    WHEN resp.claim_status_code = '19' THEN 'Processed as Primary, Forwarded to Additional Payer(s)'
                    WHEN resp.claim_status_code = '20' THEN 'Processed as Secondary, Forwarded to Additional Payer(s)'
                    WHEN resp.claim_status_code = '21' THEN 'Processed as Tertiary, Forwarded to Additional Payer(s)'
                    WHEN resp.claim_status_code = '22' THEN 'Reversal of Previous Payment'
                    WHEN resp.claim_status_code = '23' THEN 'Not Our Claim, Forwarded to Additional Payer(s)'
                    ELSE 'Unknown Status (' || resp.claim_status_code || ')'
                END,
                'service_lines', (
                    SELECT jsonb_agg(
                        jsonb_build_object(
                            'service_start_date', TO_CHAR(sl.service_start_date, 'MM/DD/YYYY'),
                            'service_end_date', TO_CHAR(sl.service_end_date, 'MM/DD/YYYY'),
                            'product_or_service_id_qualifier_value', sl.product_or_service_id_qualifier_value,
                            'adjudicated_procedure_code', sl.adjudicated_procedure_code,
                            'submitted_procedure_code_description', sl.submitted_procedure_code_description,
                            'line_item_charge_amount', sl.line_item_charge_amount,
                            'line_item_expected_amount', lcl.expected_amount,
                            'adjustments_text', (
                                SELECT string_agg(
                                    sa.claim_adjustment_group_code || ' ' || 
                                    sa.adjustment_reason_code || ' $' || 
                                    TO_CHAR(sa.adjustment_amount::NUMERIC, 'FM999,999.00'),
                                    E'\n'
                                    ORDER BY sa.claim_adjustment_group_code, sa.adjustment_reason_code
                                )
                                FROM form_med_claim_resp_835_sl_adj sa
                                JOIN sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj sfa
                                    ON sfa.form_med_claim_resp_835_sl_adj_fk = sa.id
                                WHERE sfa.form_med_claim_resp_835_sl_fk = sl.id
                                    AND sfa.delete IS NOT TRUE
                                    AND sfa.archive IS NOT TRUE
                                    AND sa.deleted IS NOT TRUE
                                    AND sa.archived IS NOT TRUE
                            ),
                            'total_adjustments', (
                                SELECT COALESCE(SUM(sa.adjustment_amount), 0)
                                FROM form_med_claim_resp_835_sl_adj sa
                                JOIN sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj sfa
                                    ON sfa.form_med_claim_resp_835_sl_adj_fk = sa.id
                                WHERE sfa.form_med_claim_resp_835_sl_fk = sl.id
                                    AND sfa.delete IS NOT TRUE
                                    AND sfa.archive IS NOT TRUE
                                    AND sa.deleted IS NOT TRUE
                                    AND sa.archived IS NOT TRUE
                            ),
                            'line_item_provider_payment_amount', sl.line_item_provider_payment_amount
                        ) ORDER BY sl.service_date, sl.line_item_control_number
                    )
                    FROM form_med_claim_resp_835_sl sl
                    JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_sl sf 
                        ON sf.form_med_claim_resp_835_sl_fk = sl.id
                    LEFT JOIN sf_form_med_claim_to_med_claim_info mci ON mci.form_med_claim_fk = mcl.id
                    LEFT JOIN sf_form_med_claim_info_to_med_claim_sl sf_sl ON sf_sl.form_med_claim_info_fk = mci.id
                    LEFT JOIN form_med_claim_sl csl ON csl.provider_control_number = sl.line_item_control_number AND sf_sl.form_med_claim_sl_fk = csl.id
                    LEFT JOIN form_ledger_charge_line lcl ON lcl.charge_no = csl.charge_no
                    WHERE sf.form_med_claim_resp_835_fk = resp.id
                        AND sf.delete IS NOT TRUE
                        AND sf.archive IS NOT TRUE
                        AND sl.deleted IS NOT TRUE
                        AND sl.archived IS NOT TRUE
                ),
                'claim_adjustments', (
                    SELECT jsonb_agg(
                        jsonb_build_object(
                            'claim_adjustment_group_code_value', ca.claim_adjustment_group_code_value,
                            'adjustment_reason_code', ca.adjustment_reason_code,
                            'adjustment_reason_code_value', ecl.name,
                            'adjustment_amount', ca.adjustment_amount
                        ) ORDER BY ca.claim_adjustment_group_code, ca.adjustment_reason_code
                    )
                    FROM form_med_claim_resp_835_adj ca
                    JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_adj sfc
                        ON sfc.form_med_claim_resp_835_adj_fk = ca.id
                    LEFT JOIN form_list_med_claim_ecl ecl
                        ON ecl.code = ca.adjustment_reason_code
                        AND ecl.field = 'CARC'
                        AND ecl.deleted IS NOT TRUE
                        AND ecl.archived IS NOT TRUE
                    WHERE sfc.form_med_claim_resp_835_fk = resp.id
                        AND sfc.delete IS NOT TRUE
                        AND sfc.archive IS NOT TRUE
                        AND ca.deleted IS NOT TRUE
                        AND ca.archived IS NOT TRUE
                )
            ) ORDER BY resp.claim_no
        ) AS claims
    FROM form_med_claim_resp_835 resp
    JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
    LEFT JOIN form_med_claim mcl ON mcl.claim_no = resp.claim_no
    LEFT JOIN form_patient pt ON pt.id = resp.patient_id
    WHERE batch.id = p_batch_id
        AND batch.deleted IS NOT TRUE 
        AND batch.archived IS NOT TRUE
        AND resp.deleted IS NOT TRUE 
        AND resp.archived IS NOT TRUE
        AND (pt.deleted IS NOT TRUE OR pt.deleted IS NULL)
        AND (pt.archived IS NOT TRUE OR pt.archived IS NULL)
    GROUP BY pt.id, resp.patient_first_name, resp.patient_last_name, pt.mrn
    ORDER BY resp.patient_last_name, resp.patient_first_name;
END;
$$ LANGUAGE plpgsql;

-- Query 4: ERA Adjustment Keys (unique adjustment codes)
CREATE OR REPLACE FUNCTION era_adjustment_keys(p_batch_id INTEGER)
RETURNS TABLE (
    adjustment_type TEXT,
    adjustment_key TEXT,
    adjustment_description TEXT
) AS $$
BEGIN
    RETURN QUERY
    -- Get unique group codes
    SELECT DISTINCT
        'Group'::TEXT AS adjustment_type,
        adj.claim_adjustment_group_code::TEXT AS adjustment_key,
        adj.claim_adjustment_group_code_value::TEXT AS adjustment_description
    FROM (
        -- Claim level adjustments
        SELECT DISTINCT ca.claim_adjustment_group_code, ca.claim_adjustment_group_code_value
        FROM form_med_claim_resp_835_adj ca
        JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_adj sfc
            ON sfc.form_med_claim_resp_835_adj_fk = ca.id
        JOIN form_med_claim_resp_835 resp ON resp.id = sfc.form_med_claim_resp_835_fk
        JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
        WHERE batch.id = p_batch_id
            AND batch.deleted IS NOT TRUE AND batch.archived IS NOT TRUE
            AND resp.deleted IS NOT TRUE AND resp.archived IS NOT TRUE
            AND ca.deleted IS NOT TRUE AND ca.archived IS NOT TRUE
            AND sfc.delete IS NOT TRUE AND sfc.archive IS NOT TRUE
        
        UNION
        
        -- Service line adjustments
        SELECT DISTINCT sa.claim_adjustment_group_code, sa.claim_adjustment_group_code_value
        FROM form_med_claim_resp_835_sl_adj sa
        JOIN sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj sfa
            ON sfa.form_med_claim_resp_835_sl_adj_fk = sa.id
        JOIN form_med_claim_resp_835_sl sl ON sl.id = sfa.form_med_claim_resp_835_sl_fk
        JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_sl sf 
            ON sf.form_med_claim_resp_835_sl_fk = sl.id
        JOIN form_med_claim_resp_835 resp ON resp.id = sf.form_med_claim_resp_835_fk
        JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
        WHERE batch.id = p_batch_id
            AND batch.deleted IS NOT TRUE AND batch.archived IS NOT TRUE
            AND resp.deleted IS NOT TRUE AND resp.archived IS NOT TRUE
            AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE
            AND sa.deleted IS NOT TRUE AND sa.archived IS NOT TRUE
            AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
            AND sfa.delete IS NOT TRUE AND sfa.archive IS NOT TRUE
    ) adj
    
    UNION ALL
    
    -- Get unique reason codes
    SELECT DISTINCT
        'CARC'::TEXT AS adjustment_type,
        adj.adjustment_reason_code::TEXT AS adjustment_key,
        COALESCE(ecl.name, 'Unknown Reason Code')::TEXT AS adjustment_description
    FROM (
        -- Claim level adjustments
        SELECT DISTINCT ca.adjustment_reason_code
        FROM form_med_claim_resp_835_adj ca
        JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_adj sfc
            ON sfc.form_med_claim_resp_835_adj_fk = ca.id
        JOIN form_med_claim_resp_835 resp ON resp.id = sfc.form_med_claim_resp_835_fk
        JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
        WHERE batch.id = p_batch_id
            AND batch.deleted IS NOT TRUE AND batch.archived IS NOT TRUE
            AND resp.deleted IS NOT TRUE AND resp.archived IS NOT TRUE
            AND ca.deleted IS NOT TRUE AND ca.archived IS NOT TRUE
            AND sfc.delete IS NOT TRUE AND sfc.archive IS NOT TRUE
        
        UNION
        
        -- Service line adjustments
        SELECT DISTINCT sa.adjustment_reason_code
        FROM form_med_claim_resp_835_sl_adj sa
        JOIN sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj sfa
            ON sfa.form_med_claim_resp_835_sl_adj_fk = sa.id
        JOIN form_med_claim_resp_835_sl sl ON sl.id = sfa.form_med_claim_resp_835_sl_fk
        JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_sl sf 
            ON sf.form_med_claim_resp_835_sl_fk = sl.id
        JOIN form_med_claim_resp_835 resp ON resp.id = sf.form_med_claim_resp_835_fk
        JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
        WHERE batch.id = p_batch_id
            AND batch.deleted IS NOT TRUE AND batch.archived IS NOT TRUE
            AND resp.deleted IS NOT TRUE AND resp.archived IS NOT TRUE
            AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE
            AND sa.deleted IS NOT TRUE AND sa.archived IS NOT TRUE
            AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
            AND sfa.delete IS NOT TRUE AND sfa.archive IS NOT TRUE
    ) adj
    LEFT JOIN form_list_med_claim_ecl ecl
        ON ecl.code = adj.adjustment_reason_code
        AND ecl.field = 'CARC'
        AND ecl.deleted IS NOT TRUE
        AND ecl.archived IS NOT TRUE
    
    UNION ALL
    
    -- Get unique remark codes
    SELECT DISTINCT
        'RARC'::TEXT AS adjustment_type,
        rmk.rmk_cd::TEXT AS adjustment_key,
        COALESCE(ecl.name, 'Unknown Remark Code')::TEXT AS adjustment_description
    FROM (
        -- Claim level remark codes
        SELECT DISTINCT gr.form_list_med_claim_ecl_fk AS rmk_cd
        FROM form_med_claim_resp_835 resp
        JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
        JOIN gr_form_med_claim_resp_835_rmk_cd_to_list_med_claim_ecl_id gr
            ON gr.form_med_claim_resp_835_fk = resp.id
        WHERE batch.id = p_batch_id
            AND batch.deleted IS NOT TRUE AND batch.archived IS NOT TRUE
            AND resp.deleted IS NOT TRUE AND resp.archived IS NOT TRUE
        
        UNION
        
        -- Service line remark codes
        SELECT DISTINCT gr.form_list_med_claim_ecl_fk AS rmk_cd
        FROM form_med_claim_resp_835_sl sl
        JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_sl sf 
            ON sf.form_med_claim_resp_835_sl_fk = sl.id
        JOIN form_med_claim_resp_835 resp ON resp.id = sf.form_med_claim_resp_835_fk
        JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
        JOIN gr_form_med_claim_resp_835_sl_rmk_cd_to_list_med_claim_ecl_id gr
            ON gr.form_med_claim_resp_835_sl_fk = sl.id
        WHERE batch.id = p_batch_id
            AND batch.deleted IS NOT TRUE AND batch.archived IS NOT TRUE
            AND resp.deleted IS NOT TRUE AND resp.archived IS NOT TRUE
            AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE
            AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
    ) rmk
    LEFT JOIN form_list_med_claim_ecl ecl
        ON ecl.code = rmk.rmk_cd
        AND ecl.field = 'RARC'
        AND ecl.deleted IS NOT TRUE
        AND ecl.archived IS NOT TRUE
    
    ORDER BY adjustment_type, adjustment_key;
END;
$$ LANGUAGE plpgsql;

-- Query 4: ERA Adjustment Keys (unique adjustment codes)
CREATE OR REPLACE FUNCTION era_adjustment_keys(p_batch_id INTEGER)
RETURNS TABLE (
    adjustment_type TEXT,
    adjustment_key TEXT,
    adjustment_description TEXT
) AS $$
BEGIN
    RETURN QUERY
    -- Get unique group codes
    SELECT DISTINCT
        'Group'::TEXT AS adjustment_type,
        adj.claim_adjustment_group_code::TEXT AS adjustment_key,
        adj.claim_adjustment_group_code_value::TEXT AS adjustment_description
    FROM (
        -- Claim level adjustments
        SELECT DISTINCT ca.claim_adjustment_group_code, ca.claim_adjustment_group_code_value
        FROM form_med_claim_resp_835_adj ca
        JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_adj sfc
            ON sfc.form_med_claim_resp_835_adj_fk = ca.id
        JOIN form_med_claim_resp_835 resp ON resp.id = sfc.form_med_claim_resp_835_fk
        JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
        WHERE batch.id = p_batch_id
            AND batch.deleted IS NOT TRUE AND batch.archived IS NOT TRUE
            AND resp.deleted IS NOT TRUE AND resp.archived IS NOT TRUE
            AND ca.deleted IS NOT TRUE AND ca.archived IS NOT TRUE
            AND sfc.delete IS NOT TRUE AND sfc.archive IS NOT TRUE
        
        UNION
        
        -- Service line adjustments
        SELECT DISTINCT sa.claim_adjustment_group_code, sa.claim_adjustment_group_code_value
        FROM form_med_claim_resp_835_sl_adj sa
        JOIN sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj sfa
            ON sfa.form_med_claim_resp_835_sl_adj_fk = sa.id
        JOIN form_med_claim_resp_835_sl sl ON sl.id = sfa.form_med_claim_resp_835_sl_fk
        JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_sl sf 
            ON sf.form_med_claim_resp_835_sl_fk = sl.id
        JOIN form_med_claim_resp_835 resp ON resp.id = sf.form_med_claim_resp_835_fk
        JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
        WHERE batch.id = p_batch_id
            AND batch.deleted IS NOT TRUE AND batch.archived IS NOT TRUE
            AND resp.deleted IS NOT TRUE AND resp.archived IS NOT TRUE
            AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE
            AND sa.deleted IS NOT TRUE AND sa.archived IS NOT TRUE
            AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
            AND sfa.delete IS NOT TRUE AND sfa.archive IS NOT TRUE
    ) adj
    
    UNION ALL
    
    -- Get unique reason codes
    SELECT DISTINCT
        'CARC'::TEXT AS adjustment_type,
        adj.adjustment_reason_code::TEXT AS adjustment_key,
        COALESCE(ecl.name, 'Unknown Reason Code')::TEXT AS adjustment_description
    FROM (
        -- Claim level adjustments
        SELECT DISTINCT ca.adjustment_reason_code
        FROM form_med_claim_resp_835_adj ca
        JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_adj sfc
            ON sfc.form_med_claim_resp_835_adj_fk = ca.id
        JOIN form_med_claim_resp_835 resp ON resp.id = sfc.form_med_claim_resp_835_fk
        JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
        WHERE batch.id = p_batch_id
            AND batch.deleted IS NOT TRUE AND batch.archived IS NOT TRUE
            AND resp.deleted IS NOT TRUE AND resp.archived IS NOT TRUE
            AND ca.deleted IS NOT TRUE AND ca.archived IS NOT TRUE
            AND sfc.delete IS NOT TRUE AND sfc.archive IS NOT TRUE
        
        UNION
        
        -- Service line adjustments
        SELECT DISTINCT sa.adjustment_reason_code
        FROM form_med_claim_resp_835_sl_adj sa
        JOIN sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj sfa
            ON sfa.form_med_claim_resp_835_sl_adj_fk = sa.id
        JOIN form_med_claim_resp_835_sl sl ON sl.id = sfa.form_med_claim_resp_835_sl_fk
        JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_sl sf 
            ON sf.form_med_claim_resp_835_sl_fk = sl.id
        JOIN form_med_claim_resp_835 resp ON resp.id = sf.form_med_claim_resp_835_fk
        JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
        WHERE batch.id = p_batch_id
            AND batch.deleted IS NOT TRUE AND batch.archived IS NOT TRUE
            AND resp.deleted IS NOT TRUE AND resp.archived IS NOT TRUE
            AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE
            AND sa.deleted IS NOT TRUE AND sa.archived IS NOT TRUE
            AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
            AND sfa.delete IS NOT TRUE AND sfa.archive IS NOT TRUE
    ) adj
    LEFT JOIN form_list_med_claim_ecl ecl
        ON ecl.code = adj.adjustment_reason_code
        AND ecl.field = 'CARC'
        AND ecl.deleted IS NOT TRUE
        AND ecl.archived IS NOT TRUE
    
    UNION ALL
    
    -- Get unique remark codes
    SELECT DISTINCT
        'RARC'::TEXT AS adjustment_type,
        rmk.rmk_cd::TEXT AS adjustment_key,
        COALESCE(ecl.name, 'Unknown Remark Code')::TEXT AS adjustment_description
    FROM (
        -- Claim level remark codes
        SELECT DISTINCT gr.form_list_med_claim_ecl_fk AS rmk_cd
        FROM form_med_claim_resp_835 resp
        JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
        JOIN gr_form_med_claim_resp_835_rmk_cd_to_list_med_claim_ecl_id gr
            ON gr.form_med_claim_resp_835_fk = resp.id
        WHERE batch.id = p_batch_id
            AND batch.deleted IS NOT TRUE AND batch.archived IS NOT TRUE
            AND resp.deleted IS NOT TRUE AND resp.archived IS NOT TRUE
        
        UNION
        
        -- Service line remark codes
        SELECT DISTINCT gr.form_list_med_claim_ecl_fk AS rmk_cd
        FROM form_med_claim_resp_835_sl sl
        JOIN sf_form_med_claim_resp_835_to_med_claim_resp_835_sl sf 
            ON sf.form_med_claim_resp_835_sl_fk = sl.id
        JOIN form_med_claim_resp_835 resp ON resp.id = sf.form_med_claim_resp_835_fk
        JOIN form_med_claim_resp_835_batch batch ON batch.response_id = resp.response_id
        JOIN gr_form_med_claim_resp_835_sl_rmk_cd_to_list_med_claim_ecl_id gr
            ON gr.form_med_claim_resp_835_sl_fk = sl.id
        WHERE batch.id = p_batch_id
            AND batch.deleted IS NOT TRUE AND batch.archived IS NOT TRUE
            AND resp.deleted IS NOT TRUE AND resp.archived IS NOT TRUE
            AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE
            AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE
    ) rmk
    LEFT JOIN form_list_med_claim_ecl ecl
        ON ecl.code = rmk.rmk_cd
        AND ecl.field = 'RARC'
        AND ecl.deleted IS NOT TRUE
        AND ecl.archived IS NOT TRUE
    
    ORDER BY adjustment_type, adjustment_key;
END;
$$ LANGUAGE plpgsql;