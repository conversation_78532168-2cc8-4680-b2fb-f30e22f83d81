CREATE OR REPLACE FUNCTION generate_ss_outbound_payload(p_ss_message_id INTEGER, p_is_followup BOOLEAN DEFAULT FALSE)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    v_ss_message_record form_ss_message; -- Changed from RECORD to specific type
    v_site_record form_site; -- Changed from RECORD to specific type
    v_payload JSONB;
    v_message_body JSONB := '{}'::JSONB;
    v_header JSONB;
    v_patient JSONB;
    v_pharmacy JSONB;
    v_prescriber JSONB;
    v_supervisor JSONB;
    v_fu_prescriber JSONB;
    v_medication_section JSONB := '{}'::JSONB; 

    v_medication_type_path TEXT; 
    v_message_type_path TEXT; 

    v_allergies_section_payload JSONB; -- Renamed from v_allergies_json_array
    v_benefits_array_payload JSONB;    -- Renamed from v_benefits_json_array
    v_compounds_json JSONB;          -- Renamed from v_compounds_json_array
    v_dues_json_array JSONB;
    v_diagnoses_json JSONB;          -- Renamed from v_diagnoses_json_array
    v_cnote_json_array JSONB;
    v_observations_json_array JSONB;
    v_other_dates_json_array JSONB;

    item RECORD;
    v_temp_jsonb JSONB;

    v_order_group_reason_actual_code TEXT;
    v_rx_group_reason_actual_code TEXT;
    v_prescriber_spec_actual_code TEXT;
    v_supervisor_spec_actual_code TEXT;
    v_fu_prescriber_spec_actual_code TEXT;

    v_product_code_qualifier_actual_code TEXT;
    v_dea_schedule_actual_code TEXT;
    v_strength_form_actual_code TEXT;
    v_strength_uom_actual_code TEXT;
    v_quantity_qualifier_actual_code TEXT;
    v_quantity_uom_actual_code TEXT;
    v_pa_status_actual_code TEXT;
    v_compound_dosage_form_actual_code TEXT;

    v_sqlstate TEXT;
    v_message_text TEXT;
    v_pg_exception_context TEXT;

    v_chg_type_actual_code TEXT;
    v_chg_type_sc_actual_codes TEXT[];

BEGIN
    RAISE LOG 'Starting generate_ss_outbound_payload for ss_message_id: %', p_ss_message_id;

    SELECT * INTO v_ss_message_record
    FROM form_ss_message fsm
    WHERE fsm.id = p_ss_message_id
    AND fsm.deleted IS NOT TRUE AND fsm.archived IS NOT TRUE;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'form_ss_message with ID % not found', p_ss_message_id;
    END IF;

    IF v_ss_message_record.site_id IS NOT NULL THEN
        SELECT fs.* INTO v_site_record
        FROM form_site fs
        WHERE fs.id = v_ss_message_record.site_id
        AND fs.deleted IS NOT TRUE AND fs.archived IS NOT TRUE;
         IF NOT FOUND THEN
            RAISE WARNING 'Sender form_site with id % not found for outbound ss_message_id %, but message has site_id set.', v_ss_message_record.site_id, p_ss_message_id;
        END IF;
    ELSIF v_ss_message_record.direction = 'OUT' AND v_ss_message_record.sent_from IS NOT NULL THEN
         SELECT fs.* INTO v_site_record 
         FROM form_site fs 
         WHERE fs.ncpdp_id = v_ss_message_record.sent_from OR fs.ss_organization_id = v_ss_message_record.sent_from LIMIT 1;
         IF NOT FOUND THEN
            RAISE WARNING 'Sender form_site with ncpdp_id/ss_organization_id % not found for outbound ss_message_id %, but message has sent_from set.', v_ss_message_record.sent_from, p_ss_message_id;
        END IF;
    ELSE
        RAISE WARNING 'Sender site_id IS NULL and sent_from is also NULL/not applicable for form_ss_message ID % ', p_ss_message_id;
    END IF;
    -- Resolve codes based on FKs in v_ss_message_record
    v_order_group_reason_actual_code := v_ss_message_record.order_group_reason;
    v_rx_group_reason_actual_code := v_ss_message_record.rx_group_reason;
    v_prescriber_spec_actual_code := v_ss_message_record.prescriber_spec_id;
    v_supervisor_spec_actual_code := v_ss_message_record.supervisor_spec_id;
    v_fu_prescriber_spec_actual_code := v_ss_message_record.fu_prescriber_spec_id;
    v_product_code_qualifier_actual_code := v_ss_message_record.product_code_qualifier_id;
    v_dea_schedule_actual_code := v_ss_message_record.dea_schedule_id;
    v_strength_form_actual_code := v_ss_message_record.strength_form_id;
    v_strength_uom_actual_code := v_ss_message_record.strength_uom_id;
    v_quantity_qualifier_actual_code := v_ss_message_record.quantity_qualifier_id;
    v_quantity_uom_actual_code := v_ss_message_record.quantity_uom_id;
    v_pa_status_actual_code := v_ss_message_record.pa_status_id;
    v_compound_dosage_form_actual_code := v_ss_message_record.compound_dosage_form_id;
    v_chg_type_actual_code := v_ss_message_record.chg_type_id;
    SELECT array_agg(lsc.code) INTO v_chg_type_sc_actual_codes 
    FROM form_list_ss_chg_subcode lsc
    JOIN gr_form_ss_message_chg_type_sc_id_to_list_ss_chg_subcode_id gr ON lsc.code = gr.form_list_ss_chg_subcode_fk 
    WHERE gr.form_ss_message_fk = p_ss_message_id;
    v_message_type_path := v_ss_message_record.message_type;
    CASE v_ss_message_record.message_type
        WHEN 'CancelRxResponse' THEN v_medication_type_path := NULL;
        WHEN 'RxRenewalRequest' THEN v_medication_type_path := 'MedicationPrescribed';
        WHEN 'RxChangeRequest' THEN v_medication_type_path := 'MedicationPrescribed'; -- Has MedicationPrescribed (original) and MedicationRequested (changes)
        WHEN 'CancelRx' THEN v_medication_type_path := 'MedicationPrescribed'; -- Or MedicationResponse based on Surescripts guide for specific use case
        WHEN 'RxRenewalResponse' THEN v_medication_type_path := 'MedicationResponse';
        WHEN 'RxChangeResponse' THEN v_medication_type_path := 'MedicationPrescribed'; -- Typically echoes original or includes new if approved w/changes
        ELSE v_medication_type_path := 'MedicationPrescribed';
    END CASE;

    ------------------------------------------------------------------------------------
    -- 1. Build Header
    ------------------------------------------------------------------------------------
    BEGIN
        v_header := _build_outbound_header(p_ss_message_id, v_site_record.id, v_order_group_reason_actual_code, v_rx_group_reason_actual_code);
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_sqlstate = RETURNED_SQLSTATE, v_message_text = MESSAGE_TEXT, v_pg_exception_context = PG_EXCEPTION_CONTEXT;
        PERFORM _log_surescripts_error(p_ss_message_id, v_sqlstate, v_message_text, v_pg_exception_context, '_build_outbound_header');
        v_header := jsonb_build_object('error', '_build_outbound_header failed: ' || v_message_text);
        RAISE; 
    END;

    BEGIN
        v_patient := _build_outbound_patient(p_ss_message_id);
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_sqlstate = RETURNED_SQLSTATE, v_message_text = MESSAGE_TEXT, v_pg_exception_context = PG_EXCEPTION_CONTEXT;
        PERFORM _log_surescripts_error(p_ss_message_id, v_sqlstate, v_message_text, v_pg_exception_context, '_build_outbound_patient');
        v_patient := jsonb_build_object('error', '_build_outbound_patient failed: ' || v_message_text);
        RAISE;
    END;

    BEGIN
        IF v_site_record.id IS NOT NULL THEN
            v_pharmacy := _build_outbound_pharmacy(v_site_record.id);
        ELSE
            RAISE WARNING 'Sender site record not found for % message %', v_ss_message_record.message_type, p_ss_message_id;
            v_pharmacy := NULL;
        END IF;
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_sqlstate = RETURNED_SQLSTATE, v_message_text = MESSAGE_TEXT, v_pg_exception_context = PG_EXCEPTION_CONTEXT;
        PERFORM _log_surescripts_error(p_ss_message_id, v_sqlstate, v_message_text, v_pg_exception_context, '_build_outbound_pharmacy (logic in main)');
        v_pharmacy := jsonb_build_object('error', '_build_outbound_pharmacy logic failed: ' || v_message_text);
        RAISE;
    END;

    BEGIN
        v_prescriber := _build_outbound_prescriber(p_ss_message_id, v_prescriber_spec_actual_code);
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_sqlstate = RETURNED_SQLSTATE, v_message_text = MESSAGE_TEXT, v_pg_exception_context = PG_EXCEPTION_CONTEXT;
        PERFORM _log_surescripts_error(p_ss_message_id, v_sqlstate, v_message_text, v_pg_exception_context, '_build_outbound_prescriber');
        v_prescriber := jsonb_build_object('error', '_build_outbound_prescriber failed: ' || v_message_text);
        RAISE;
    END;

    BEGIN
        v_supervisor := _build_outbound_supervisor(p_ss_message_id, v_supervisor_spec_actual_code);
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_sqlstate = RETURNED_SQLSTATE, v_message_text = MESSAGE_TEXT, v_pg_exception_context = PG_EXCEPTION_CONTEXT;
        PERFORM _log_surescripts_error(p_ss_message_id, v_sqlstate, v_message_text, v_pg_exception_context, '_build_outbound_supervisor');
        v_supervisor := jsonb_build_object('error', '_build_outbound_supervisor failed: ' || v_message_text);
        RAISE;
    END;

    BEGIN
        v_fu_prescriber := _build_outbound_fu_prescriber(p_ss_message_id, v_fu_prescriber_spec_actual_code);
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_sqlstate = RETURNED_SQLSTATE, v_message_text = MESSAGE_TEXT, v_pg_exception_context = PG_EXCEPTION_CONTEXT;
        PERFORM _log_surescripts_error(p_ss_message_id, v_sqlstate, v_message_text, v_pg_exception_context, '_build_outbound_fu_prescriber');
        v_fu_prescriber := jsonb_build_object('error', '_build_outbound_fu_prescriber failed: ' || v_message_text);
        RAISE;
    END;

    BEGIN
        v_allergies_section_payload := _build_outbound_allergies_section(p_ss_message_id); 
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_sqlstate = RETURNED_SQLSTATE, v_message_text = MESSAGE_TEXT, v_pg_exception_context = PG_EXCEPTION_CONTEXT;
        PERFORM _log_surescripts_error(p_ss_message_id, v_sqlstate, v_message_text, v_pg_exception_context, '_build_outbound_allergies_section');
        v_allergies_section_payload := jsonb_build_object('error', '_build_outbound_allergies_section failed: ' || v_message_text);
        RAISE;
    END;

    BEGIN
        v_benefits_array_payload := _build_outbound_benefits_array(p_ss_message_id);
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_sqlstate = RETURNED_SQLSTATE, v_message_text = MESSAGE_TEXT, v_pg_exception_context = PG_EXCEPTION_CONTEXT;
        PERFORM _log_surescripts_error(p_ss_message_id, v_sqlstate, v_message_text, v_pg_exception_context, '_build_outbound_benefits_array');
        v_benefits_array_payload := jsonb_build_object('error', '_build_outbound_benefits_array failed: ' || v_message_text);
        RAISE;
    END;

    BEGIN
        v_observations_json_array := _build_outbound_observations_array(p_ss_message_id);
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_sqlstate = RETURNED_SQLSTATE, v_message_text = MESSAGE_TEXT, v_pg_exception_context = PG_EXCEPTION_CONTEXT;
        PERFORM _log_surescripts_error(p_ss_message_id, v_sqlstate, v_message_text, v_pg_exception_context, '_build_outbound_observations_array');
        v_observations_json_array := jsonb_build_object('error', '_build_outbound_observations_array failed: ' || v_message_text);
        RAISE;
    END;

    BEGIN
        v_other_dates_json_array := _build_medication_other_dates_array(p_ss_message_id);
        v_compounds_json   := _build_medication_compounds_json(p_ss_message_id, v_compound_dosage_form_actual_code);
        v_dues_json_array        := _build_medication_dues_array(p_ss_message_id);
        v_cnote_json_array       := _build_medication_cnote_array(p_ss_message_id);
        v_diagnoses_json   := _build_medication_diagnoses_json(p_ss_message_id);
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_sqlstate = RETURNED_SQLSTATE, v_message_text = MESSAGE_TEXT, v_pg_exception_context = PG_EXCEPTION_CONTEXT;
        PERFORM _log_surescripts_error(p_ss_message_id, v_sqlstate, v_message_text, v_pg_exception_context, 'build_medication_sub_arrays');
        RAISE;
    END;

    BEGIN
        v_medication_section := _build_outbound_medication_section(
            p_ss_message_id, 
            v_medication_type_path, 
            v_other_dates_json_array, 
            v_compounds_json, 
            v_dues_json_array, 
            v_cnote_json_array, 
            v_diagnoses_json, 
            v_product_code_qualifier_actual_code,
            v_dea_schedule_actual_code,
            v_strength_form_actual_code,
            v_strength_uom_actual_code,
            v_quantity_qualifier_actual_code,
            v_quantity_uom_actual_code,
            v_pa_status_actual_code
        );
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_sqlstate = RETURNED_SQLSTATE, v_message_text = MESSAGE_TEXT, v_pg_exception_context = PG_EXCEPTION_CONTEXT;
        PERFORM _log_surescripts_error(p_ss_message_id, v_sqlstate, v_message_text, v_pg_exception_context, '_build_outbound_medication_section');
        v_medication_section := jsonb_build_object('error', '_build_outbound_medication_section failed: ' || v_message_text);
        RAISE;
    END;

    DECLARE
        v_body_content_built JSONB;
    BEGIN
        v_body_content_built := _build_outbound_message_body_content(
            p_ss_message_id,
            v_patient,
            v_pharmacy,
            v_prescriber,
            v_supervisor,
            v_fu_prescriber,
            v_allergies_section_payload, 
            v_benefits_array_payload,
            v_medication_section,
            v_observations_json_array
        );

        IF v_ss_message_record.message_type = 'CancelRxResponse' THEN
            v_message_body := _build_cancel_rx_response_specific_body(p_ss_message_id);
        ELSIF v_ss_message_record.message_type = 'RxChangeRequest' THEN
            DECLARE
                v_chg_req_specific_part JSONB;
            BEGIN
                v_chg_req_specific_part := _build_rx_change_request_specific_body(
                    p_ss_message_id => p_ss_message_id,
                    p_chg_type_actual_code => v_chg_type_actual_code, 
                    p_chg_type_sc_actual_codes => v_chg_type_sc_actual_codes
                );
                v_message_body := v_body_content_built || v_chg_req_specific_part;
            END;
        ELSIF v_ss_message_record.message_type = 'RxRenewalResponse' THEN
            DECLARE 
                v_renewal_response_block JSONB := '{}'::JSONB;
            BEGIN
                IF v_ss_message_record.renewal_status = 'Approved' THEN
                     v_renewal_response_block := jsonb_build_object('Approved', COALESCE(remove_null_values_from_jsonb(jsonb_build_object('Note', v_ss_message_record.renewal_note)), '{}'::jsonb) );
                ELSIF v_ss_message_record.renewal_status = 'ApprovedWithChanges' THEN
                     v_renewal_response_block := jsonb_build_object('ApprovedWithChanges', COALESCE(remove_null_values_from_jsonb(jsonb_build_object('Note', v_ss_message_record.renewal_note)), '{}'::jsonb) );
                ELSIF v_ss_message_record.renewal_status = 'Denied' THEN
                    DECLARE v_denial_reason_actual_codes TEXT[];
                    BEGIN
                        SELECT array_agg(code) INTO v_denial_reason_actual_codes FROM form_list_ss_renewal_denial_reason WHERE id = ANY(v_ss_message_record.renewal_denial_reason_code::INT[]);
                        v_renewal_response_block := jsonb_build_object('Denied', remove_null_values_from_jsonb(jsonb_build_object(
                            'ReasonCode', CASE WHEN v_denial_reason_actual_codes IS NOT NULL THEN to_jsonb(v_denial_reason_actual_codes) ELSE NULL END,
                            'DenialReason', v_ss_message_record.renewal_denied_reason
                        )));
                    END;
                ELSIF v_ss_message_record.renewal_status = 'Replace' THEN
                    v_renewal_response_block := jsonb_build_object('Replace', COALESCE(remove_null_values_from_jsonb(jsonb_build_object('Note', v_ss_message_record.renewal_note)), '{}'::jsonb) );
                END IF;
                IF v_renewal_response_block <> '{}'::JSONB THEN
                    v_message_body := v_body_content_built || jsonb_build_object('Response', v_renewal_response_block);
                END IF;
            END;
        ELSIF v_ss_message_record.message_type = 'RxChangeResponse' THEN
            DECLARE 
                v_chg_response_block JSONB := '{}'::JSONB;
                v_response_details JSONB := '{}'::JSONB;
                v_chg_dr_code_actual_codes TEXT[];
                v_chg_vr_cd_actual_codes TEXT[];
            BEGIN
                IF v_ss_message_record.chg_status = 'Approved' THEN
                    IF v_ss_message_record.chg_approved_note IS NOT NULL THEN 
                        v_response_details := v_response_details || jsonb_build_object('Note', v_ss_message_record.chg_approved_note);
                    END IF;
                    v_chg_response_block := jsonb_build_object('Approved', v_response_details);
                ELSIF v_ss_message_record.chg_status = 'Denied' THEN
                    SELECT array_agg(flsdr.code) INTO v_chg_dr_code_actual_codes 
                    FROM form_list_ss_denial_reason flsdr
                    JOIN gr_form_ss_message_chg_dr_code_id_to_list_ss_denial_reason_id gr ON flsdr.id = gr.form_list_ss_denial_reason_fk 
                    WHERE gr.form_ss_message_fk = p_ss_message_id;
                    IF v_chg_dr_code_actual_codes IS NOT NULL AND array_length(v_chg_dr_code_actual_codes, 1) > 0 THEN
                        v_response_details := v_response_details || jsonb_build_object('ReasonCode', to_jsonb(v_chg_dr_code_actual_codes));
                    END IF;
                    IF v_ss_message_record.chg_denied_reason IS NOT NULL THEN
                        v_response_details := v_response_details || jsonb_build_object('DenialReason', v_ss_message_record.chg_denied_reason);
                    END IF;
                    v_chg_response_block := jsonb_build_object('Denied', v_response_details);
                ELSIF v_ss_message_record.chg_status = 'ApprovedWithChanges' THEN
                     IF v_ss_message_record.chg_approved_note IS NOT NULL THEN 
                        v_response_details := v_response_details || jsonb_build_object('Note', v_ss_message_record.chg_approved_note);
                    END IF;
                    v_chg_response_block := jsonb_build_object('ApprovedWithChanges', v_response_details);           
                ELSIF v_ss_message_record.chg_status = 'Validated' THEN
                    SELECT array_agg(gr.form_list_ss_chg_valid_reason_fk) INTO v_chg_vr_cd_actual_codes FROM gr_form_ss_message_chg_vr_cd_id_to_list_ss_chg_valid_reason_id gr WHERE gr.form_ss_message_fk = p_ss_message_id;
                    IF v_chg_vr_cd_actual_codes IS NOT NULL AND array_length(v_chg_vr_cd_actual_codes, 1) > 0 THEN
                        v_response_details := v_response_details || jsonb_build_object('ReasonCode', to_jsonb(v_chg_vr_cd_actual_codes));
                    END IF;
                    IF v_ss_message_record.chg_validated_note IS NOT NULL THEN
                        v_response_details := v_response_details || jsonb_build_object('Note', v_ss_message_record.chg_validated_note);
                    END IF;
                    v_chg_response_block := jsonb_build_object('Validated', v_response_details);
                END IF;

                IF v_chg_response_block <> '{}'::JSONB THEN
                     v_message_body := v_body_content_built || jsonb_build_object('Response', v_chg_response_block);
                END IF;
                IF v_ss_message_record.chg_type_id IS NOT NULL THEN 
                    v_message_body := v_message_body || jsonb_build_object('MessageRequestCode', v_chg_type_actual_code);
                END IF;
                IF v_chg_type_sc_actual_codes IS NOT NULL AND array_length(v_chg_type_sc_actual_codes,1) > 0 THEN
                     v_message_body := v_message_body || jsonb_build_object('MessageRequestSubCode', to_jsonb(v_chg_type_sc_actual_codes));
                END IF;
            END;
        ELSE
            v_message_body := v_body_content_built; 
            IF v_ss_message_record.priority_flag IS NOT NULL AND v_message_body IS NOT NULL AND jsonb_typeof(v_message_body) = 'object' THEN 
                v_message_body := jsonb_set(v_message_body, ARRAY['UrgencyIndicatorCode'], to_jsonb(v_ss_message_record.priority_flag));
            END IF;
            IF p_is_followup AND v_ss_message_record.message_type <> 'CancelRxResponse' AND v_message_body IS NOT NULL AND jsonb_typeof(v_message_body) = 'object' THEN 
                v_message_body := jsonb_set(v_message_body, ARRAY['FollowUpRequest'], to_jsonb(COALESCE(v_ss_message_record.followup_count,0) + 1));
            END IF;
        END IF;

    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_sqlstate = RETURNED_SQLSTATE, v_message_text = MESSAGE_TEXT, v_pg_exception_context = PG_EXCEPTION_CONTEXT;
        PERFORM _log_surescripts_error(
            p_ss_message_id, 
            v_sqlstate, 
            v_message_text, 
            v_pg_exception_context,
            '_build_outbound_message_body_content_assembly', 
            jsonb_build_object('current_payload_attempt', v_message_body)
        );
        v_message_body := jsonb_build_object('error', 'Body content assembly failed: ' || v_message_text);
        RAISE;
    END;

    v_payload := jsonb_build_object(
        'Message', jsonb_build_object(
            'Header', COALESCE(v_header, jsonb_build_object('error', 'Header not generated')),
            'Body', jsonb_build_object(
                v_message_type_path, COALESCE(v_message_body, jsonb_build_object('error', 'Body content for ' || v_message_type_path || ' not generated'))
            )
        )
    );
    
    RAISE LOG 'Finished generate_surescripts_outbound_payload for ss_message_id: %. Final Payload (pre-null removal): %', p_ss_message_id, v_payload;
    RETURN remove_null_values_from_jsonb(v_payload);

EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS 
        v_sqlstate = RETURNED_SQLSTATE, 
        v_message_text = MESSAGE_TEXT, 
        v_pg_exception_context = PG_EXCEPTION_CONTEXT;

    PERFORM _log_surescripts_error(
        p_ss_message_id, 
        v_sqlstate, 
        v_message_text, 
        v_pg_exception_context,
        'generate_ss_outbound_payload (main_exception)', 
        jsonb_build_object('current_payload_attempt', v_payload)
    );
    RAISE WARNING 'Error in generate_ss_outbound_payload for ID %: %, SQLSTATE: %, CONTEXT: %', p_ss_message_id, v_message_text, v_sqlstate, v_pg_exception_context;
    RETURN jsonb_build_object('error', 'Main exception in generate_ss_outbound_payload: ' || v_message_text, 'sqlstate', v_sqlstate);
END;
$$;