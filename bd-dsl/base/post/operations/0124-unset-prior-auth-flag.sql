DO $$ BEGIN
  PERFORM drop_all_function_signatures('unset_auth_flag_when_pa_set');
END $$;
CREATE OR REPLACE FUNCTION unset_auth_flag_when_pa_set()
RETURNS TRIGGER AS $$
BEGIN
    -- Only proceed if drug_pa_id has changed
    IF (TG_OP = 'INSERT') OR (TG_OP = 'UPDATE' AND OLD.drug_pa_id IS DISTINCT FROM NEW.drug_pa_id) THEN
        -- If drug_pa_id is set and auth_flag is also set, unset auth_flag
        IF NEW.drug_pa_id IS NOT NULL AND NEW.auth_flag = 'Yes' THEN
            NEW.auth_flag := NULL;
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for form_careplan_orderp_item
DROP TRIGGER IF EXISTS trg_unset_auth_flag_orderp_item ON form_careplan_orderp_item;
CREATE TRIGGER trg_unset_auth_flag_orderp_item
BEFORE INSERT OR UPDATE ON form_careplan_orderp_item
FOR EACH ROW
EXECUTE FUNCTION unset_auth_flag_when_pa_set();

-- Create trigger for form_careplan_order_item
DROP TRIGGER IF EXISTS trg_unset_auth_flag_order_item ON form_careplan_order_item;
CREATE TRIGGER trg_unset_auth_flag_order_item
BEFORE INSERT OR UPDATE ON form_careplan_order_item
FOR EACH ROW
EXECUTE FUNCTION unset_auth_flag_when_pa_set();
