-- Medical Claim Response Status Mapping Functions
-- Maps response codes to claim statuses based on JavaScript receiver.js logic

-- Map 277 response status codes (health_care_claim_status_category_code) to claim status
CREATE OR REPLACE FUNCTION map_277_response_status(p_category_code TEXT) RETURNS TEXT AS $$
BEGIN
    RETURN CASE p_category_code
        -- Status Report codes
        WHEN 'A0' THEN 'Forwarded'
        WHEN 'A1' THEN 'Received'
        WHEN 'A2' THEN 'Accepted'
        WHEN 'A3' THEN 'Rejected'
        WHEN 'A4' THEN 'Not Found'
        WHEN 'A5' THEN 'Processing'
        WHEN 'A6' THEN 'Rejected'
        WHEN 'A7' THEN 'Rejected'
        WHEN 'A8' THEN 'Rejected'
        WHEN 'DR01' THEN 'Received'
        WHEN 'DR02' THEN 'Accepted'
        WHEN 'DR03' THEN 'Rejected'
        WHEN 'DR04' THEN 'Not Found'
        WHEN 'DR05' THEN 'Rejected'
        WHEN 'DR06' THEN 'Rejected'
        WHEN 'DR07' THEN 'Rejected'
        WHEN 'DR08' THEN 'Warning'
        WHEN 'P0' THEN 'Partially Paid'
        WHEN 'P1' THEN 'Processing'
        WHEN 'P2' THEN 'Under Review'
        WHEN 'P3' THEN 'Awaiting Requested Information'
        WHEN 'P4' THEN 'Awaiting Requested Information'
        WHEN 'P5' THEN 'On-hold'
        WHEN 'F0' THEN 'Paid'
        WHEN 'F1' THEN 'Paid'
        WHEN 'F2' THEN 'Denied'
        WHEN 'F3' THEN 'Revised'
        WHEN 'F3F' THEN 'Forwarded'
        WHEN 'F3N' THEN 'Revised'
        WHEN 'F4' THEN 'Denied'
        WHEN 'R0' THEN 'Request for Additional Information'
        WHEN 'R1' THEN 'Request for Additional Information'
        WHEN 'R3' THEN 'Request for Additional Information'
        WHEN 'R4' THEN 'Request for Additional Information'
        WHEN 'R5' THEN 'Request for Additional Information'
        WHEN 'R6' THEN 'Request for Additional Information'
        WHEN 'R7' THEN 'Request for Additional Information'
        WHEN 'R8' THEN 'Request for Additional Information'
        WHEN 'R9' THEN 'Request for Additional Information'
        WHEN 'R10' THEN 'Request for Additional Information'
        WHEN 'R11' THEN 'Request for Additional Information'
        WHEN 'R12' THEN 'Request for Additional Information'
        WHEN 'R13' THEN 'Request for Additional Information'
        WHEN 'R14' THEN 'Request for Additional Information'
        WHEN 'R15' THEN 'Request for Additional Information'
        WHEN 'R16' THEN 'Request for Additional Information'
        WHEN 'R17' THEN 'Request for Additional Information'
        WHEN 'E0' THEN 'Error'
        WHEN 'E1' THEN 'Error'
        WHEN 'E2' THEN 'Payer Not Responding, Try Again Later'
        WHEN 'E3' THEN 'Rejected'
        WHEN 'E4' THEN 'Rejected'
        WHEN 'D0' THEN 'Not Found'
        ELSE 'Error' -- Default to Error if unknown code
    END;
END;
$$ LANGUAGE plpgsql;

-- Map 835 claim status code to claim status
CREATE OR REPLACE FUNCTION map_835_claim_status(p_status_code TEXT) RETURNS TEXT AS $$
BEGIN
    RETURN CASE p_status_code
        WHEN '0' THEN 'Error'
        WHEN '3' THEN 'Payable'
        WHEN '6' THEN 'Partially Paid'
        WHEN '12' THEN 'Partially Paid'
        WHEN '15' THEN 'Partially Paid'
        WHEN '16' THEN 'Forwarded'
        WHEN '17' THEN 'Forwarded'
        WHEN '18' THEN 'Error'
        WHEN '19' THEN 'Received'
        WHEN '20' THEN 'Accepted'
        WHEN '21' THEN 'Error'
        WHEN '23' THEN 'Rejected'
        WHEN '24' THEN 'Rejected'
        WHEN '25' THEN 'Rejected'
        WHEN '26' THEN 'Error'
        WHEN '27' THEN 'Rejected'
        WHEN '29' THEN 'Error'
        WHEN '30' THEN 'Error'
        WHEN '31' THEN 'Error'
        WHEN '32' THEN 'Error'
        WHEN '33' THEN 'Error'
        WHEN '34' THEN 'Error'
        WHEN '35' THEN 'Error'
        WHEN '37' THEN 'On-hold'
        WHEN '38' THEN 'Processing'
        WHEN '39' THEN 'On-hold'
        WHEN '40' THEN 'Under Review'
        WHEN '41' THEN 'Processing'
        WHEN '42' THEN 'On-hold'
        WHEN '44' THEN 'Under Review'
        WHEN '45' THEN 'Under Review'
        WHEN '46' THEN 'Under Review'
        WHEN '47' THEN 'Partially Paid'
        WHEN '49' THEN 'Under Review'
        WHEN '50' THEN 'Under Review'
        WHEN '51' THEN 'Under Review'
        WHEN '52' THEN 'Under Review'
        WHEN '53' THEN 'Under Review'
        WHEN '54' THEN 'Rejected'
        WHEN '55' THEN 'Under Review'
        WHEN '56' THEN 'Under Review'
        WHEN '64' THEN 'Processing'
        WHEN '65' THEN 'Payable'
        WHEN '66' THEN 'Payable'
        WHEN '72' THEN 'Partially Paid'
        WHEN '73' THEN 'Payable'
        WHEN '78' THEN 'Processing'
        WHEN '81' THEN 'Denied'
        WHEN '83' THEN 'Denied'
        WHEN '84' THEN 'Denied'
        WHEN '85' THEN 'Rejected'
        WHEN '86' THEN 'Error'
        WHEN '88' THEN 'Denied'
        WHEN '89' THEN 'Denied'
        WHEN '90' THEN 'Denied'
        WHEN '91' THEN 'Denied'
        WHEN '92' THEN 'Denied'
        WHEN '93' THEN 'Denied'
        WHEN '94' THEN 'Denied'
        WHEN '95' THEN 'Rejected'
        WHEN '96' THEN 'Rejected'
        WHEN '97' THEN 'Error'
        WHEN '98' THEN 'Partially Paid'
        WHEN '99' THEN 'Under Review'
        WHEN '100' THEN 'Partially Paid'
        WHEN '101' THEN 'Partially Paid'
        WHEN '102' THEN 'Partially Paid'
        WHEN '103' THEN 'Partially Paid'
        WHEN '104' THEN 'Partially Paid'
        WHEN '105' THEN 'Partially Paid'
        WHEN '106' THEN 'Denied'
        WHEN '107' THEN 'Partially Paid'
        WHEN '109' THEN 'Denied'
        WHEN '111' THEN 'Rejected'
        WHEN '114' THEN 'Forwarded'
        WHEN '116' THEN 'Rejected'
        WHEN '121' THEN 'Error'
        WHEN '400' THEN 'Error'
        WHEN '401' THEN 'Error'
        WHEN '402' THEN 'Error'
        WHEN '450' THEN 'On-hold'
        WHEN '473' THEN 'Error'
        WHEN '474' THEN 'Error'
        WHEN '475' THEN 'Error'
        WHEN '476' THEN 'Error'
        WHEN '477' THEN 'Error'
        WHEN '478' THEN 'Error'
        WHEN '479' THEN 'Error'
        WHEN '480' THEN 'Error'
        WHEN '481' THEN 'Error'
        WHEN '483' THEN 'Denied'
        WHEN '484' THEN 'Error'
        WHEN '487' THEN 'Rejected'
        WHEN '491' THEN 'Rejected'
        WHEN '493' THEN 'Error'
        WHEN '494' THEN 'Rejected'
        WHEN '495' THEN 'Error'
        WHEN '496' THEN 'Rejected'
        WHEN '497' THEN 'Partially Paid'
        WHEN '498' THEN 'Denied'
        WHEN '499' THEN 'Rejected'
        WHEN '506' THEN 'Forwarded'
        WHEN '515' THEN 'Under Review'
        WHEN '530' THEN 'Processing'
        WHEN '535' THEN 'Processing'
        WHEN '541' THEN 'Processing'
        WHEN '554' THEN 'Payable'
        WHEN '591' THEN 'Payable'
        WHEN '592' THEN 'Partially Paid'
        WHEN '606' THEN 'Payable'
        WHEN '607' THEN 'Payable'
        WHEN '666' THEN 'Processing'
        WHEN '667' THEN 'Rejected'
        WHEN '674' THEN 'Denied'
        WHEN '676' THEN 'Under Review'
        WHEN '677' THEN 'Rejected'
        WHEN '678' THEN 'Error'
        WHEN '679' THEN 'Rejected'
        WHEN '681' THEN 'Rejected'
        WHEN '682' THEN 'Denied'
        WHEN '683' THEN 'On-hold'
        WHEN '684' THEN 'Error'
        WHEN '685' THEN 'Processing'
        WHEN '686' THEN 'Reversed'
        WHEN '687' THEN 'Processing'
        WHEN '689' THEN 'Error'
        WHEN '690' THEN 'Error'
        WHEN '691' THEN 'Error'
        WHEN '692' THEN 'Processing'
        WHEN '717' THEN 'Processing'
        WHEN '718' THEN 'Denied'
        WHEN '732' THEN 'Error'
        WHEN '734' THEN 'Under Review'
        WHEN '735' THEN 'Denied'
        WHEN '736' THEN 'On-hold'
        WHEN '744' THEN 'Denied'
        WHEN '746' THEN 'Error'
        WHEN '764' THEN 'Denied'
        WHEN '765' THEN 'Denied'
        WHEN '766' THEN 'On-hold'
        WHEN '768' THEN 'Denied'
        WHEN '770' THEN 'Rejected'
        WHEN '771' THEN 'Rejected'
        WHEN '774' THEN 'Denied'
        WHEN '777' THEN 'Processing'
        WHEN '778' THEN 'Denied'
        WHEN '779' THEN 'Denied'
        WHEN '780' THEN 'Denied'
        WHEN '781' THEN 'Under Review'
        WHEN '783' THEN 'Processing'
        WHEN '784' THEN 'Error'
        WHEN '786' THEN 'Forwarded'
        WHEN '788' THEN 'Forwarded'
        WHEN '789' THEN 'Forwarded'
        WHEN '790' THEN 'Forwarded'
        WHEN '791' THEN 'Forwarded'
        WHEN '792' THEN 'Forwarded'
        WHEN '793' THEN 'Forwarded'
        WHEN '798' THEN 'Under Review'
        WHEN '800' THEN 'Sent'
        WHEN '801' THEN 'Accepted'
        WHEN '802' THEN 'Rejected'
        WHEN '803' THEN 'Rejected'
        WHEN '804' THEN 'Error'
        WHEN '806' THEN 'Error'
        ELSE NULL -- Return NULL if unknown code
    END;
END;
$$ LANGUAGE plpgsql;

-- Determine status for 835 based on payment amounts
CREATE OR REPLACE FUNCTION determine_835_payment_status(
    p_mapped_status TEXT,
    p_paid_amount NUMERIC,
    p_expected_amount NUMERIC
) RETURNS TEXT AS $$
BEGIN
    -- If we already have a mapped status from the claim status code, use it
    IF p_mapped_status IS NOT NULL THEN
        RETURN p_mapped_status;
    END IF;
    
    -- Otherwise determine based on payment amounts
    IF p_paid_amount > 0 THEN
        IF p_paid_amount >= p_expected_amount THEN
            RETURN 'Paid';
        ELSE
            RETURN 'Partially Paid';
        END IF;
    ELSE
        RETURN 'Request for Additional Information';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Check if claim is pending reversal and should be marked as reversed
CREATE OR REPLACE FUNCTION check_claim_reversal(
    p_claim_frequency_code TEXT,
    p_current_status TEXT
) RETURNS TEXT AS $$
BEGIN
    -- Claim frequency code '8' is VOID_CANCEL_OF_PRIOR_CLAIM
    IF p_claim_frequency_code = '8' AND p_current_status = 'Accepted' THEN
        RETURN 'Reversed';
    ELSE
        RETURN p_current_status;
    END IF;
END;
$$ LANGUAGE plpgsql; 