-- SQL Generation started: 2025-05-20T19:22:27.215Z
-- Table: list_fdb_alrgn_xsense_hist
-- Total records: 154

-- Initial configuration
BEGIN;
SET session_replication_role = replica;
SET maintenance_work_mem = '1GB';
SET synchronous_commit = off;
SET work_mem = '2GB';
SET temp_buffers = '1GB';
SET statement_timeout = 0;

-- Clean table
TRUNCATE TABLE form_list_fdb_alrgn_xsense_hist;
COMMIT;

-- Beginning data import in batches of 50000 records
-- Each batch will have its own transaction
BEGIN;
INSERT INTO form_list_fdb_alrgn_xsense_hist (id, code, repl_dam_alrgn_xsense, prev_dam_alrgn_xsense, dam_alrgn_xsense_repl_eff_dt, auto_name, created_on, created_by) VALUES
(1, '85-1096', 85, 1096, 20140102, '85 1096 20140102', '2025-05-20 15:22:27', 1),
(2, '152-246', 152, 246, 20040723, '152 246 20040723', '2025-05-20 15:22:27', 1),
(3, '172-55', 172, 55, 20071023, '172 55 20071023', '2025-05-20 15:22:27', 1),
(4, '198-241', 198, 241, 20041115, '198 241 20041115', '2025-05-20 15:22:27', 1),
(5, '220-297', 220, 297, 20130515, '220 297 20130515', '2025-05-20 15:22:27', 1),
(6, '236-147', 236, 147, 20040723, '236 147 20040723', '2025-05-20 15:22:27', 1),
(7, '413-65', 413, 65, 20040723, '413 65 20040723', '2025-05-20 15:22:27', 1),
(8, '415-65', 415, 65, 20040723, '415 65 20040723', '2025-05-20 15:22:27', 1),
(9, '418-65', 418, 65, 20040723, '418 65 20040723', '2025-05-20 15:22:27', 1),
(10, '420-65', 420, 65, 20040723, '420 65 20040723', '2025-05-20 15:22:27', 1),
(11, '421-65', 421, 65, 20040723, '421 65 20040723', '2025-05-20 15:22:27', 1),
(12, '426-265', 426, 265, 20040723, '426 265 20040723', '2025-05-20 15:22:27', 1),
(13, '526-304', 526, 304, 20040723, '526 304 20040723', '2025-05-20 15:22:27', 1),
(14, '538-213', 538, 213, 20050411, '538 213 20050411', '2025-05-20 15:22:27', 1),
(15, '540-1585', 540, 1585, 20230104, '540 1585 20230104', '2025-05-20 15:22:27', 1),
(16, '568-70', 568, 70, 20040723, '568 70 20040723', '2025-05-20 15:22:27', 1),
(17, '569-70', 569, 70, 20040723, '569 70 20040723', '2025-05-20 15:22:27', 1),
(18, '570-70', 570, 70, 20040723, '570 70 20040723', '2025-05-20 15:22:27', 1),
(19, '571-70', 571, 70, 20040723, '571 70 20040723', '2025-05-20 15:22:27', 1),
(20, '573-265', 573, 265, 20040723, '573 265 20040723', '2025-05-20 15:22:27', 1),
(21, '574-265', 574, 265, 20040723, '574 265 20040723', '2025-05-20 15:22:27', 1),
(22, '575-265', 575, 265, 20040723, '575 265 20040723', '2025-05-20 15:22:27', 1),
(23, '579-858', 579, 858, 20101102, '579 858 20101102', '2025-05-20 15:22:27', 1),
(24, '582-138', 582, 138, 20040723, '582 138 20040723', '2025-05-20 15:22:27', 1),
(25, '599-117', 599, 117, 20040723, '599 117 20040723', '2025-05-20 15:22:27', 1),
(26, '600-117', 600, 117, 20040723, '600 117 20040723', '2025-05-20 15:22:27', 1),
(27, '601-117', 601, 117, 20040723, '601 117 20040723', '2025-05-20 15:22:27', 1),
(28, '634-241', 634, 241, 20041115, '634 241 20041115', '2025-05-20 15:22:27', 1),
(29, '635-241', 635, 241, 20041115, '635 241 20041115', '2025-05-20 15:22:27', 1),
(30, '636-241', 636, 241, 20041115, '636 241 20041115', '2025-05-20 15:22:27', 1),
(31, '637-241', 637, 241, 20041115, '637 241 20041115', '2025-05-20 15:22:27', 1),
(32, '668-1061', 668, 1061, 20100922, '668 1061 20100922', '2025-05-20 15:22:27', 1),
(33, '688-687', 688, 687, 20130515, '688 687 20130515', '2025-05-20 15:22:27', 1),
(34, '835-830', 835, 830, 20050818, '835 830 20050818', '2025-05-20 15:22:27', 1),
(35, '882-234', 882, 234, 20051005, '882 234 20051005', '2025-05-20 15:22:27', 1),
(36, '883-234', 883, 234, 20051005, '883 234 20051005', '2025-05-20 15:22:27', 1),
(37, '885-348', 885, 348, 20051010, '885 348 20051010', '2025-05-20 15:22:27', 1),
(38, '886-348', 886, 348, 20051010, '886 348 20051010', '2025-05-20 15:22:27', 1),
(39, '982-377', 982, 377, 20060315, '982 377 20060315', '2025-05-20 15:22:27', 1),
(40, '983-377', 983, 377, 20060315, '983 377 20060315', '2025-05-20 15:22:27', 1),
(41, '1090-75', 1090, 75, 20060927, '1090 75 20060927', '2025-05-20 15:22:27', 1),
(42, '1091-75', 1091, 75, 20060927, '1091 75 20060927', '2025-05-20 15:22:27', 1),
(43, '1092-75', 1092, 75, 20060927, '1092 75 20060927', '2025-05-20 15:22:27', 1),
(44, '1115-989', 1115, 989, 20211108, '1115 989 20211108', '2025-05-20 15:22:27', 1),
(45, '1115-1001', 1115, 1001, 20211108, '1115 1001 20211108', '2025-05-20 15:22:27', 1),
(46, '1115-1040', 1115, 1040, 20211108, '1115 1040 20211108', '2025-05-20 15:22:27', 1),
(47, '1115-1075', 1115, 1075, 20211108, '1115 1075 20211108', '2025-05-20 15:22:27', 1),
(48, '1115-1150', 1115, 1150, 20211108, '1115 1150 20211108', '2025-05-20 15:22:27', 1),
(49, '1115-1323', 1115, 1323, 20211108, '1115 1323 20211108', '2025-05-20 15:22:27', 1),
(50, '1115-1332', 1115, 1332, 20211108, '1115 1332 20211108', '2025-05-20 15:22:27', 1),
(51, '1115-1356', 1115, 1356, 20211108, '1115 1356 20211108', '2025-05-20 15:22:27', 1),
(52, '1152-129', 1152, 129, 20070412, '1152 129 20070412', '2025-05-20 15:22:27', 1),
(53, '1152-130', 1152, 130, 20070412, '1152 130 20070412', '2025-05-20 15:22:27', 1),
(54, '1172-311', 1172, 311, 20071024, '1172 311 20071024', '2025-05-20 15:22:27', 1),
(55, '1172-682', 1172, 682, 20071024, '1172 682 20071024', '2025-05-20 15:22:27', 1),
(56, '1172-975', 1172, 975, 20071024, '1172 975 20071024', '2025-05-20 15:22:27', 1),
(57, '1175-66', 1175, 66, 20071107, '1175 66 20071107', '2025-05-20 15:22:27', 1),
(58, '1176-66', 1176, 66, 20071107, '1176 66 20071107', '2025-05-20 15:22:27', 1),
(59, '1177-66', 1177, 66, 20071107, '1177 66 20071107', '2025-05-20 15:22:27', 1),
(60, '1178-66', 1178, 66, 20071107, '1178 66 20071107', '2025-05-20 15:22:27', 1),
(61, '1179-66', 1179, 66, 20071107, '1179 66 20071107', '2025-05-20 15:22:27', 1),
(62, '1305-225', 1305, 225, 20100825, '1305 225 20100825', '2025-05-20 15:22:27', 1),
(63, '1306-225', 1306, 225, 20100825, '1306 225 20100825', '2025-05-20 15:22:27', 1),
(64, '1307-225', 1307, 225, 20100825, '1307 225 20100825', '2025-05-20 15:22:27', 1),
(65, '1308-225', 1308, 225, 20100825, '1308 225 20100825', '2025-05-20 15:22:27', 1),
(66, '1408-1397', 1408, 1397, 20121029, '1408 1397 20121029', '2025-05-20 15:22:27', 1),
(67, '1617-182', 1617, 182, 20210421, '1617 182 20210421', '2025-05-20 15:22:27', 1),
(68, '1618-182', 1618, 182, 20210421, '1618 182 20210421', '2025-05-20 15:22:27', 1),
(69, '1621-1095', 1621, 1095, 20210723, '1621 1095 20210723', '2025-05-20 15:22:27', 1),
(70, '1622-1095', 1622, 1095, 20210723, '1622 1095 20210723', '2025-05-20 15:22:27', 1),
(71, '1639-41', 1639, 41, 20230124, '1639 41 20230124', '2025-05-20 15:22:27', 1),
(72, '1640-41', 1640, 41, 20230124, '1640 41 20230124', '2025-05-20 15:22:27', 1),
(73, '1642-1', 1642, 1, 20230405, '1642 1 20230405', '2025-05-20 15:22:27', 1),
(74, '1643-1', 1643, 1, 20230405, '1643 1 20230405', '2025-05-20 15:22:27', 1),
(75, '1644-1', 1644, 1, 20230405, '1644 1 20230405', '2025-05-20 15:22:27', 1),
(76, '1645-1', 1645, 1, 20230405, '1645 1 20230405', '2025-05-20 15:22:27', 1),
(77, '1646-1', 1646, 1, 20230405, '1646 1 20230405', '2025-05-20 15:22:27', 1),
(78, '1647-1', 1647, 1, 20230405, '1647 1 20230405', '2025-05-20 15:22:27', 1),
(79, '1648-1', 1648, 1, 20230405, '1648 1 20230405', '2025-05-20 15:22:27', 1),
(80, '1649-1', 1649, 1, 20230405, '1649 1 20230405', '2025-05-20 15:22:27', 1),
(81, '1650-1', 1650, 1, 20230405, '1650 1 20230405', '2025-05-20 15:22:27', 1),
(82, '1651-1', 1651, 1, 20230405, '1651 1 20230405', '2025-05-20 15:22:27', 1),
(83, '1652-1', 1652, 1, 20230405, '1652 1 20230405', '2025-05-20 15:22:27', 1),
(84, '1653-1', 1653, 1, 20230405, '1653 1 20230405', '2025-05-20 15:22:27', 1),
(85, '1654-1', 1654, 1, 20230405, '1654 1 20230405', '2025-05-20 15:22:27', 1),
(86, '1655-1', 1655, 1, 20230405, '1655 1 20230405', '2025-05-20 15:22:27', 1),
(87, '1656-1', 1656, 1, 20230405, '1656 1 20230405', '2025-05-20 15:22:27', 1),
(88, '1657-1', 1657, 1, 20230405, '1657 1 20230405', '2025-05-20 15:22:27', 1),
(89, '1658-1', 1658, 1, 20230405, '1658 1 20230405', '2025-05-20 15:22:27', 1),
(90, '1659-1', 1659, 1, 20230405, '1659 1 20230405', '2025-05-20 15:22:27', 1),
(91, '1660-1', 1660, 1, 20230405, '1660 1 20230405', '2025-05-20 15:22:27', 1),
(92, '1661-1', 1661, 1, 20230405, '1661 1 20230405', '2025-05-20 15:22:27', 1),
(93, '1662-1', 1662, 1, 20230405, '1662 1 20230405', '2025-05-20 15:22:27', 1),
(94, '1663-1', 1663, 1, 20230405, '1663 1 20230405', '2025-05-20 15:22:27', 1),
(95, '1664-1', 1664, 1, 20230405, '1664 1 20230405', '2025-05-20 15:22:27', 1),
(96, '1665-1', 1665, 1, 20230405, '1665 1 20230405', '2025-05-20 15:22:27', 1),
(97, '1666-1', 1666, 1, 20230405, '1666 1 20230405', '2025-05-20 15:22:27', 1),
(98, '1667-1', 1667, 1, 20230405, '1667 1 20230405', '2025-05-20 15:22:27', 1),
(99, '1668-1', 1668, 1, 20230405, '1668 1 20230405', '2025-05-20 15:22:27', 1),
(100, '1669-1', 1669, 1, 20230405, '1669 1 20230405', '2025-05-20 15:22:27', 1),
(101, '1670-1', 1670, 1, 20230405, '1670 1 20230405', '2025-05-20 15:22:27', 1),
(102, '1671-1', 1671, 1, 20230405, '1671 1 20230405', '2025-05-20 15:22:27', 1),
(103, '1672-1', 1672, 1, 20230405, '1672 1 20230405', '2025-05-20 15:22:27', 1),
(104, '1673-1', 1673, 1, 20230405, '1673 1 20230405', '2025-05-20 15:22:27', 1),
(105, '1674-1', 1674, 1, 20230405, '1674 1 20230405', '2025-05-20 15:22:27', 1),
(106, '1675-1', 1675, 1, 20230405, '1675 1 20230405', '2025-05-20 15:22:27', 1),
(107, '1676-1', 1676, 1, 20230405, '1676 1 20230405', '2025-05-20 15:22:27', 1),
(108, '1677-1', 1677, 1, 20230405, '1677 1 20230405', '2025-05-20 15:22:27', 1),
(109, '1678-1', 1678, 1, 20230405, '1678 1 20230405', '2025-05-20 15:22:27', 1),
(110, '1679-1', 1679, 1, 20230405, '1679 1 20230405', '2025-05-20 15:22:27', 1),
(111, '1680-1', 1680, 1, 20230405, '1680 1 20230405', '2025-05-20 15:22:27', 1),
(112, '1681-1', 1681, 1, 20230405, '1681 1 20230405', '2025-05-20 15:22:27', 1),
(113, '1682-1', 1682, 1, 20230405, '1682 1 20230405', '2025-05-20 15:22:27', 1),
(114, '1683-1', 1683, 1, 20230405, '1683 1 20230405', '2025-05-20 15:22:27', 1),
(115, '1684-1', 1684, 1, 20230405, '1684 1 20230405', '2025-05-20 15:22:27', 1),
(116, '1685-1', 1685, 1, 20230405, '1685 1 20230405', '2025-05-20 15:22:27', 1),
(117, '1686-1', 1686, 1, 20230405, '1686 1 20230405', '2025-05-20 15:22:27', 1),
(118, '1687-1', 1687, 1, 20230405, '1687 1 20230405', '2025-05-20 15:22:27', 1),
(119, '1688-1', 1688, 1, 20230405, '1688 1 20230405', '2025-05-20 15:22:27', 1),
(120, '1689-1', 1689, 1, 20230405, '1689 1 20230405', '2025-05-20 15:22:27', 1),
(121, '1690-1', 1690, 1, 20230405, '1690 1 20230405', '2025-05-20 15:22:27', 1),
(122, '1691-1', 1691, 1, 20230405, '1691 1 20230405', '2025-05-20 15:22:27', 1),
(123, '1692-1', 1692, 1, 20230405, '1692 1 20230405', '2025-05-20 15:22:27', 1),
(124, '1693-1', 1693, 1, 20230405, '1693 1 20230405', '2025-05-20 15:22:27', 1),
(125, '1694-1', 1694, 1, 20230405, '1694 1 20230405', '2025-05-20 15:22:27', 1),
(126, '1695-1', 1695, 1, 20230405, '1695 1 20230405', '2025-05-20 15:22:27', 1),
(127, '1696-1', 1696, 1, 20230405, '1696 1 20230405', '2025-05-20 15:22:27', 1),
(128, '1697-1', 1697, 1, 20230405, '1697 1 20230405', '2025-05-20 15:22:27', 1),
(129, '1698-1', 1698, 1, 20230405, '1698 1 20230405', '2025-05-20 15:22:27', 1),
(130, '1699-1', 1699, 1, 20230405, '1699 1 20230405', '2025-05-20 15:22:27', 1),
(131, '1700-1', 1700, 1, 20230405, '1700 1 20230405', '2025-05-20 15:22:27', 1),
(132, '1701-1', 1701, 1, 20230405, '1701 1 20230405', '2025-05-20 15:22:27', 1),
(133, '1702-1', 1702, 1, 20230405, '1702 1 20230405', '2025-05-20 15:22:27', 1),
(134, '1703-1', 1703, 1, 20230405, '1703 1 20230405', '2025-05-20 15:22:27', 1),
(135, '1704-1', 1704, 1, 20230405, '1704 1 20230405', '2025-05-20 15:22:27', 1),
(136, '1705-722', 1705, 722, 20230615, '1705 722 20230615', '2025-05-20 15:22:27', 1),
(137, '1705-1209', 1705, 1209, 20230615, '1705 1209 20230615', '2025-05-20 15:22:27', 1),
(138, '1705-1401', 1705, 1401, 20230615, '1705 1401 20230615', '2025-05-20 15:22:27', 1),
(139, '1705-1440', 1705, 1440, 20230615, '1705 1440 20230615', '2025-05-20 15:22:27', 1),
(140, '1705-1464', 1705, 1464, 20230615, '1705 1464 20230615', '2025-05-20 15:22:27', 1),
(141, '1705-1537', 1705, 1537, 20230615, '1705 1537 20230615', '2025-05-20 15:22:27', 1),
(142, '1745-1', 1745, 1, 20230405, '1745 1 20230405', '2025-05-20 15:22:27', 1),
(143, '1746-1', 1746, 1, 20230405, '1746 1 20230405', '2025-05-20 15:22:27', 1),
(144, '1747-1', 1747, 1, 20230405, '1747 1 20230405', '2025-05-20 15:22:27', 1),
(145, '1748-1', 1748, 1, 20230405, '1748 1 20230405', '2025-05-20 15:22:27', 1),
(146, '1749-1', 1749, 1, 20230405, '1749 1 20230405', '2025-05-20 15:22:27', 1),
(147, '1750-1', 1750, 1, 20230405, '1750 1 20230405', '2025-05-20 15:22:27', 1),
(148, '1751-1', 1751, 1, 20230405, '1751 1 20230405', '2025-05-20 15:22:27', 1),
(149, '1752-1', 1752, 1, 20230405, '1752 1 20230405', '2025-05-20 15:22:27', 1),
(150, '1753-1', 1753, 1, 20230405, '1753 1 20230405', '2025-05-20 15:22:27', 1),
(151, '1754-1', 1754, 1, 20230405, '1754 1 20230405', '2025-05-20 15:22:27', 1),
(152, '1755-1', 1755, 1, 20230405, '1755 1 20230405', '2025-05-20 15:22:27', 1),
(153, '1756-1', 1756, 1, 20230405, '1756 1 20230405', '2025-05-20 15:22:27', 1),
(154, '1757-1', 1757, 1, 20230405, '1757 1 20230405', '2025-05-20 15:22:27', 1)
;
COMMIT;

-- Import complete: 2025-05-20T19:22:28.232Z
-- Total records processed: 154
-- Duration: 1.02 seconds

-- Reset and update sequence to next available value
BEGIN;
ALTER SEQUENCE form_list_fdb_alrgn_xsense_hist_id_seq RESTART WITH 155;
SET session_replication_role = DEFAULT;
COMMIT;
