-- SQL Generation started: 2025-05-20T19:21:51.892Z
-- Table: list_fdb_route_abbrev
-- Total records: 36

-- Initial configuration
BEGIN;
SET session_replication_role = replica;
SET maintenance_work_mem = '1GB';
SET synchronous_commit = off;
SET work_mem = '2GB';
SET temp_buffers = '1GB';
SET statement_timeout = 0;

-- Clean table
TRUNCATE TABLE form_list_fdb_route_abbrev;
COMMIT;

-- Beginning data import in batches of 50000 records
-- Each batch will have its own transaction
BEGIN;
INSERT INTO form_list_fdb_route_abbrev (id, code, gcrt, rt, gcrt2, gcrt_desc, systemic, auto_name, created_on, created_by) VALUES
(1, NULL, 'A', 'INTRAVEN', 'IV', 'INTRAVENOUS', 'S', 'INTRAVEN', '2025-05-20 15:21:52', 1),
(2, NULL, 'B', 'BUCCAL', 'BC', 'BUCCAL', 'S', 'BUCCAL', '2025-05-20 15:21:52', 1),
(3, NULL, 'C', 'INTRAMUSC', 'IM', 'INTRAMUSCULAR', 'S', 'INTRAMUSC', '2025-05-20 15:21:52', 1),
(4, NULL, 'D', 'DENTAL', 'DT', 'DENTAL', 'N', 'DENTAL', '2025-05-20 15:21:52', 1),
(5, NULL, 'E', 'EPIDURAL', 'EP', 'EPIDURAL', 'N', 'EPIDURAL', '2025-05-20 15:21:52', 1),
(6, NULL, 'F', 'PERFUSION', 'PF', 'PERFUSION', 'N', 'PERFUSION', '2025-05-20 15:21:52', 1),
(7, NULL, 'G', 'SUBCUT', 'SQ', 'SUBCUTANEOUS', 'S', 'SUBCUT', '2025-05-20 15:21:52', 1),
(8, NULL, 'H', 'INHALATION', 'IH', 'INHALATION', 'S', 'INHALATION', '2025-05-20 15:21:52', 1),
(9, NULL, 'I', 'INTRACAVER', 'IC', 'INTRACAVERNOSAL', 'N', 'INTRACAVER', '2025-05-20 15:21:52', 1),
(10, NULL, 'J', 'INTRAARTER', 'IA', 'INTRAARTERIAL', 'S', 'INTRAARTER', '2025-05-20 15:21:52', 1),
(11, NULL, 'K', 'INTRAARTIC', 'IU', 'INTRAARTICULAR', 'S', 'INTRAARTIC', '2025-05-20 15:21:52', 1),
(12, NULL, 'L', 'TRANSLING', 'TL', 'TRANSLINGUAL', 'S', 'TRANSLING', '2025-05-20 15:21:52', 1),
(13, NULL, 'M', 'MISCELL', 'MC', 'MISCELLANEOUS', 'O', 'MISCELL', '2025-05-20 15:21:52', 1),
(14, NULL, 'N', 'IMPLANT', 'IL', 'IMPLANTATION', 'S', 'IMPLANT', '2025-05-20 15:21:52', 1),
(15, NULL, 'O', 'INTRATHEC', 'IT', 'INTRATHECAL', 'O', 'INTRATHEC', '2025-05-20 15:21:52', 1),
(16, NULL, 'P', 'INTRAPERIT', 'IP', 'INTRAPERITONEAL', 'N', 'INTRAPERIT', '2025-05-20 15:21:52', 1),
(17, NULL, 'Q', 'INTRAVESIC', 'IS', 'INTRAVESICAL', 'S', 'INTRAVESIC', '2025-05-20 15:21:52', 1),
(18, NULL, 'R', 'IRRIGATION', 'IR', 'IRRIGATION', 'N', 'IRRIGATION', '2025-05-20 15:21:52', 1),
(19, NULL, 'S', 'SUBLINGUAL', 'SL', 'SUBLINGUAL', 'S', 'SUBLINGUAL', '2025-05-20 15:21:52', 1),
(20, NULL, 'T', 'TRANSDERM', 'TD', 'TRANSDERMAL', 'S', 'TRANSDERM', '2025-05-20 15:21:52', 1),
(21, NULL, 'U', 'URETHRAL', 'UR', 'URETHRAL', 'N', 'URETHRAL', '2025-05-20 15:21:52', 1),
(22, NULL, 'V', 'VAGINAL', 'VG', 'VAGINAL', 'S', 'VAGINAL', '2025-05-20 15:21:52', 1),
(23, NULL, 'W', 'INTRAOCULR', 'IO', 'INTRAOCULAR', 'S', 'INTRAOCULR', '2025-05-20 15:21:52', 1),
(24, NULL, 'X', 'INTRAPLEUR', 'IX', 'INTRAPLEURAL', 'O', 'INTRAPLEUR', '2025-05-20 15:21:52', 1),
(25, NULL, 'Y', 'IN VITRO', 'IN', 'IN VITRO', 'O', 'IN VITRO', '2025-05-20 15:21:52', 1),
(26, NULL, 'Z', 'INTRAUTERI', 'IY', 'INTRAUTERINE', 'S', 'INTRAUTERI', '2025-05-20 15:21:52', 1),
(27, NULL, '0', 'HEMODIALYS', 'HE', 'HEMODIALYSIS', 'O', 'HEMODIALYS', '2025-05-20 15:21:52', 1),
(28, NULL, '1', 'ORAL', 'PO', 'ORAL', 'S', 'ORAL', '2025-05-20 15:21:52', 1),
(29, NULL, '2', 'INJECTION', 'IJ', 'INJECTION', 'S', 'INJECTION', '2025-05-20 15:21:52', 1),
(30, NULL, '3', 'RECTAL', 'RC', 'RECTAL', 'S', 'RECTAL', '2025-05-20 15:21:52', 1),
(31, NULL, '4', 'MUCOUS MEM', 'MM', 'MUCOUS MEMBRANE', 'S', 'MUCOUS MEM', '2025-05-20 15:21:52', 1),
(32, NULL, '5', 'TOPICAL', 'TP', 'TOPICAL', 'N', 'TOPICAL', '2025-05-20 15:21:52', 1),
(33, NULL, '6', 'OPHTHALMIC', 'OP', 'OPHTHALMIC (EYE)', 'N', 'OPHTHALMIC', '2025-05-20 15:21:52', 1),
(34, NULL, '7', 'NASAL', 'NS', 'NASAL', 'S', 'NASAL', '2025-05-20 15:21:52', 1),
(35, NULL, '8', 'OTIC (EAR)', 'OT', 'OTIC (EAR)', 'N', 'OTIC (EAR)', '2025-05-20 15:21:52', 1),
(36, NULL, '9', 'INTRADERM', 'ID', 'INTRADERMAL', 'O', 'INTRADERM', '2025-05-20 15:21:52', 1)
;
COMMIT;

-- Import complete: 2025-05-20T19:21:52.913Z
-- Total records processed: 36
-- Duration: 1.02 seconds

-- Reset and update sequence to next available value
BEGIN;
ALTER SEQUENCE form_list_fdb_route_abbrev_id_seq RESTART WITH 37;
SET session_replication_role = DEFAULT;
COMMIT;
