-- SQL Generation started: 2025-05-20T19:22:36.294Z
-- Table: list_fdb_med_concept_typ_desc
-- Total records: 4

-- Initial configuration
BEGIN;
SET session_replication_role = replica;
SET maintenance_work_mem = '1GB';
SET synchronous_commit = off;
SET work_mem = '2GB';
SET temp_buffers = '1GB';
SET statement_timeout = 0;

-- Clean table
TRUNCATE TABLE form_list_fdb_med_concept_typ_desc;
COMMIT;

-- Beginning data import in batches of 50000 records
-- Each batch will have its own transaction
BEGIN;
INSERT INTO form_list_fdb_med_concept_typ_desc (id, code, med_concept_id_typ, med_concept_id_typ_desc, auto_name, created_on, created_by) VALUES
(1, NULL, 1, 'Medication Name', '1 Medication Name', '2025-05-20 15:22:36', 1),
(2, NULL, 2, 'Routed Medication', '2 Routed Medication', '2025-05-20 15:22:36', 1),
(3, NULL, 3, 'Medication', '3 Medication', '2025-05-20 15:22:36', 1),
(4, NULL, 7, 'Routed Dosage Form Medication', '7 Routed Dosage Form Medication', '2025-05-20 15:22:36', 1)
;
COMMIT;

-- Import complete: 2025-05-20T19:22:37.322Z
-- Total records processed: 4
-- Duration: 1.03 seconds

-- Reset and update sequence to next available value
BEGIN;
ALTER SEQUENCE form_list_fdb_med_concept_typ_desc_id_seq RESTART WITH 5;
SET session_replication_role = DEFAULT;
COMMIT;
