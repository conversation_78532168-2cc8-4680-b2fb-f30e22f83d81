-- SQL Generation started: 2025-05-20T19:22:14.581Z
-- Table: list_fdb_ndc_attribute_val
-- Total records: 83

-- Initial configuration
BEGIN;
SET session_replication_role = replica;
SET maintenance_work_mem = '1GB';
SET synchronous_commit = off;
SET work_mem = '2GB';
SET temp_buffers = '1GB';
SET statement_timeout = 0;

-- Clean table
TRUNCATE TABLE form_list_fdb_ndc_attribute_val;
COMMIT;

-- Beginning data import in batches of 50000 records
-- Each batch will have its own transaction
BEGIN;
INSERT INTO form_list_fdb_ndc_attribute_val (id, code, ndc_attribute_type_cd, ndc_attribute_value, ndc_attribute_value_dsc, auto_name, created_on, created_by) VALUES
(1, NULL, 1, '1', 'Below Median', '1 1 Below Median', '2025-05-20 15:22:14', 1),
(2, NULL, 1, '2', 'Equals Median', '1 2 Equals Median', '2025-05-20 15:22:14', 1),
(3, NULL, 1, '3', 'Above Median', '1 3 Above Median', '2025-05-20 15:22:14', 1),
(4, NULL, 33, '1', 'Inner NDC', '33 1 Inner NDC', '2025-05-20 15:22:14', 1),
(5, NULL, 33, '2', 'First Generic', '33 2 First Generic', '2025-05-20 15:22:14', 1),
(6, NULL, 33, '3', 'Sourced from SPL', '33 3 Sourced from SPL', '2025-05-20 15:22:14', 1),
(7, NULL, 33, '4', 'Vaccine', '33 4 Vaccine', '2025-05-20 15:22:14', 1),
(8, NULL, 33, '5', 'Sample', '33 5 Sample', '2025-05-20 15:22:14', 1),
(9, NULL, 33, '6', 'Outsourcing Facility', '33 6 Outsourcing Facility', '2025-05-20 15:22:14', 1),
(10, NULL, 33, '7', 'No Price Available', '33 7 No Price Available', '2025-05-20 15:22:14', 1),
(11, NULL, 35, '1', 'Current Price Reflects DIR', '35 1 Current Price Reflects DIR', '2025-05-20 15:22:14', 1),
(12, NULL, 35, '2', 'Current Price Reflects SWP', '35 2 Current Price Reflects SWP', '2025-05-20 15:22:14', 1),
(13, NULL, 35, '3', 'Current Price Reflects WAC', '35 3 Current Price Reflects WAC', '2025-05-20 15:22:14', 1),
(14, NULL, 36, '1', 'Current Price Reflects DIR', '36 1 Current Price Reflects DIR', '2025-05-20 15:22:14', 1),
(15, NULL, 36, '2', 'Current Price Reflects SWP', '36 2 Current Price Reflects SWP', '2025-05-20 15:22:14', 1),
(16, NULL, 36, '3', 'Current Price Reflects WAC', '36 3 Current Price Reflects WAC', '2025-05-20 15:22:14', 1),
(17, NULL, 37, '1', 'SPL', '37 1 SPL', '2025-05-20 15:22:14', 1),
(18, NULL, 37, '2', 'Old FDA NDC Dir', '37 2 Old FDA NDC Dir', '2025-05-20 15:22:14', 1),
(19, NULL, 37, '3', 'Old FDA NDC Dir-not OBC verified', '37 3 Old FDA NDC Dir-not OBC verified', '2025-05-20 15:22:14', 1),
(20, NULL, 38, '1', 'SPL', '38 1 SPL', '2025-05-20 15:22:14', 1),
(21, NULL, 38, '2', 'Old FDA NDC Dir', '38 2 Old FDA NDC Dir', '2025-05-20 15:22:14', 1),
(22, NULL, 38, '3', 'Old FDA NDC Dir-not OBC verified', '38 3 Old FDA NDC Dir-not OBC verified', '2025-05-20 15:22:14', 1),
(23, NULL, 39, '3', 'Old FDA NDC Dir-not OBC verified', '39 3 Old FDA NDC Dir-not OBC verified', '2025-05-20 15:22:14', 1),
(24, NULL, 40, '1', 'SPL', '40 1 SPL', '2025-05-20 15:22:14', 1),
(25, NULL, 40, '3', 'Old FDA NDC Dir-not OBC verified', '40 3 Old FDA NDC Dir-not OBC verified', '2025-05-20 15:22:14', 1),
(26, NULL, 50, '101', 'Unable to contact mfg/mfg no longer in business', '50 101 Unable to contact mfg/mfg no longer in business', '2025-05-20 15:22:14', 1),
(27, NULL, 50, '2', 'Safety, efficacy or compliance-related issue', '50 2 Safety, efficacy or compliance-related issue', '2025-05-20 15:22:14', 1),
(28, NULL, 51, '1', 'Preservative Free', '51 1 Preservative Free', '2025-05-20 15:22:14', 1),
(29, NULL, 51, '2', 'Not Applicable', '51 2 Not Applicable', '2025-05-20 15:22:14', 1),
(30, NULL, 52, '1', 'Sugar Free (NOT IN USE)', '52 1 Sugar Free (NOT IN USE)', '2025-05-20 15:22:14', 1),
(31, NULL, 52, '2', 'Contains Sugar (NOT IN USE)', '52 2 Contains Sugar (NOT IN USE)', '2025-05-20 15:22:14', 1),
(32, NULL, 52, '3', 'Undetermined (NOT IN USE)', '52 3 Undetermined (NOT IN USE)', '2025-05-20 15:22:14', 1),
(33, NULL, 53, '1', 'Latex Free (NOT IN USE)', '53 1 Latex Free (NOT IN USE)', '2025-05-20 15:22:14', 1),
(34, NULL, 53, '2', 'Contains Latex (NOT IN USE)', '53 2 Contains Latex (NOT IN USE)', '2025-05-20 15:22:14', 1),
(35, NULL, 53, '3', 'Undetermined (NOT IN USE)', '53 3 Undetermined (NOT IN USE)', '2025-05-20 15:22:14', 1),
(36, NULL, 54, '1', 'Ethyl Alcohol (Ethanol) Free (NOT IN USE)', '54 1 Ethyl Alcohol (Ethanol) Free (NOT IN USE)', '2025-05-20 15:22:14', 1),
(37, NULL, 54, '2', 'Contains Ethyl Alcohol (Ethanol) (NOT IN USE)', '54 2 Contains Ethyl Alcohol (Ethanol) (NOT IN USE)', '2025-05-20 15:22:14', 1),
(38, NULL, 54, '3', 'Undetermined (NOT IN USE)', '54 3 Undetermined (NOT IN USE)', '2025-05-20 15:22:14', 1),
(39, NULL, 55, '1', 'Store at room temperature upon receipt from MFG', '55 1 Store at room temperature upon receipt from MFG', '2025-05-20 15:22:14', 1),
(40, NULL, 55, '2', 'Refrigerate upon receipt from manufacturer', '55 2 Refrigerate upon receipt from manufacturer', '2025-05-20 15:22:14', 1),
(41, NULL, 55, '3', 'Freeze upon receipt from manufacturer', '55 3 Freeze upon receipt from manufacturer', '2025-05-20 15:22:14', 1),
(42, NULL, 55, '4', 'Undetermined', '55 4 Undetermined', '2025-05-20 15:22:14', 1),
(43, NULL, 58, '01', 'Abbreviated New Drug Application (ANDA)', '58 01 Abbreviated New Drug Application (ANDA)', '2025-05-20 15:22:14', 1),
(44, NULL, 58, '02', 'Biological License Application (BLA)', '58 02 Biological License Application (BLA)', '2025-05-20 15:22:14', 1),
(45, NULL, 58, '03', 'New Drug Application (NDA)', '58 03 New Drug Application (NDA)', '2025-05-20 15:22:14', 1),
(46, NULL, 58, '04', 'NDA Authorized Generic', '58 04 NDA Authorized Generic', '2025-05-20 15:22:14', 1),
(47, NULL, 58, '05', 'DESI 5 - LTE/IRS drug for all indications', '58 05 DESI 5 - LTE/IRS drug for all indications', '2025-05-20 15:22:14', 1),
(48, NULL, 58, '06', 'DESI 6 - LTE/IRS drug withdrawn from market', '58 06 DESI 6 - LTE/IRS drug withdrawn from market', '2025-05-20 15:22:14', 1),
(49, NULL, 58, '07', 'Prescription Pre-Natal Vitamin or Fluoride', '58 07 Prescription Pre-Natal Vitamin or Fluoride', '2025-05-20 15:22:14', 1),
(50, NULL, 58, '08', 'Prescription Dietary Supplement/Vitamin/Mineral', '58 08 Prescription Dietary Supplement/Vitamin/Mineral', '2025-05-20 15:22:14', 1),
(51, NULL, 58, '09', 'OTC Monograph Tentative', '58 09 OTC Monograph Tentative', '2025-05-20 15:22:14', 1),
(52, NULL, 58, '10', 'OTC Monograph Final', '58 10 OTC Monograph Final', '2025-05-20 15:22:14', 1),
(53, NULL, 58, '11', 'Unapproved Drug - Drug Shortage', '58 11 Unapproved Drug - Drug Shortage', '2025-05-20 15:22:14', 1),
(54, NULL, 58, '12', 'Unapproved Drug - Per 1927 (k)(2)(A)(ii)', '58 12 Unapproved Drug - Per 1927 (k)(2)(A)(ii)', '2025-05-20 15:22:14', 1),
(55, NULL, 58, '13', 'Unapproved Drug - Per 1927 (k)(2)(A)(iii)', '58 13 Unapproved Drug - Per 1927 (k)(2)(A)(iii)', '2025-05-20 15:22:14', 1),
(56, NULL, 61, '1', 'Opioid: Morphine Equivalent Dose is available', '61 1 Opioid: Morphine Equivalent Dose is available', '2025-05-20 15:22:14', 1),
(57, NULL, 61, '2', 'Opioid: Morphine Equivalent Dose is not available', '61 2 Opioid: Morphine Equivalent Dose is not available', '2025-05-20 15:22:14', 1),
(58, NULL, 62, '1', 'Designated As Prescription', '62 1 Designated As Prescription', '2025-05-20 15:22:14', 1),
(59, NULL, 62, '10', 'Not Otherwise Classified Non-Drug, Non-Device', '62 10 Not Otherwise Classified Non-Drug, Non-Device', '2025-05-20 15:22:14', 1),
(60, NULL, 62, '2', 'Prenatal Dietary Supplement', '62 2 Prenatal Dietary Supplement', '2025-05-20 15:22:14', 1),
(61, NULL, 62, '3', 'Pediatric Dietary Supplement', '62 3 Pediatric Dietary Supplement', '2025-05-20 15:22:14', 1),
(62, NULL, 62, '4', 'Dietary Supplement', '62 4 Dietary Supplement', '2025-05-20 15:22:14', 1),
(63, NULL, 62, '5', 'Medical Food', '62 5 Medical Food', '2025-05-20 15:22:14', 1),
(64, NULL, 62, '6', 'Non-Drug Compounding Ingredient', '62 6 Non-Drug Compounding Ingredient', '2025-05-20 15:22:14', 1),
(65, NULL, 62, '7', 'Non-Drug Nutritional/Electrolyte, Enteral Product', '62 7 Non-Drug Nutritional/Electrolyte, Enteral Product', '2025-05-20 15:22:14', 1),
(66, NULL, 62, '8', 'Non-Device Supply', '62 8 Non-Device Supply', '2025-05-20 15:22:14', 1),
(67, NULL, 62, '9', 'Non-Drug, Non-Device Topical Product', '62 9 Non-Drug, Non-Device Topical Product', '2025-05-20 15:22:14', 1),
(68, NULL, 63, '1', 'Table 1 - Hazardous Drug with MSHI or Carcinogen', '63 1 Table 1 - Hazardous Drug with MSHI or Carcinogen', '2025-05-20 15:22:14', 1),
(69, NULL, 63, '2', 'Table 2 - Hazardous Drug w/o MSHI, Noncarcinogenic', '63 2 Table 2 - Hazardous Drug w/o MSHI, Noncarcinogenic', '2025-05-20 15:22:14', 1),
(70, NULL, 63, '3', 'No longer in use.', '63 3 No longer in use.', '2025-05-20 15:22:14', 1),
(71, NULL, 63, '4', 'Drugs Deleted from the NIOSH Hazardous Drug List', '63 4 Drugs Deleted from the NIOSH Hazardous Drug List', '2025-05-20 15:22:14', 1),
(72, NULL, 64, '1', 'Single Use', '64 1 Single Use', '2025-05-20 15:22:14', 1),
(73, NULL, 64, '2', 'Multi Use', '64 2 Multi Use', '2025-05-20 15:22:14', 1),
(74, NULL, 64, '3', 'Undetermined - Manufacturer did not specify', '64 3 Undetermined - Manufacturer did not specify', '2025-05-20 15:22:14', 1),
(75, NULL, 64, '4', 'Undetermined - Not Reviewed', '64 4 Undetermined - Not Reviewed', '2025-05-20 15:22:14', 1),
(76, NULL, 83, '1', 'Reference Biologic - 351(a)', '83 1 Reference Biologic - 351(a)', '2025-05-20 15:22:14', 1),
(77, NULL, 83, '2', 'Biosimilar - 351(k)', '83 2 Biosimilar - 351(k)', '2025-05-20 15:22:14', 1),
(78, NULL, 83, '3', 'Interchangeable - 351(k)', '83 3 Interchangeable - 351(k)', '2025-05-20 15:22:14', 1),
(79, NULL, 85, '1', 'compound kits', '85 1 compound kits', '2025-05-20 15:22:14', 1),
(80, NULL, 89, '1', 'On weekly rebate files', '89 1 On weekly rebate files', '2025-05-20 15:22:14', 1),
(81, NULL, 89, '2', 'On the latest quarterly rebate file', '89 2 On the latest quarterly rebate file', '2025-05-20 15:22:14', 1),
(82, NULL, 89, '3', 'On weekly and the latest quarterly rebate file', '89 3 On weekly and the latest quarterly rebate file', '2025-05-20 15:22:14', 1),
(83, NULL, 89, '4', 'Not on the latest quarterly rebate file', '89 4 Not on the latest quarterly rebate file', '2025-05-20 15:22:14', 1)
;
COMMIT;

-- Import complete: 2025-05-20T19:22:15.601Z
-- Total records processed: 83
-- Duration: 1.02 seconds

-- Reset and update sequence to next available value
BEGIN;
ALTER SEQUENCE form_list_fdb_ndc_attribute_val_id_seq RESTART WITH 84;
SET session_replication_role = DEFAULT;
COMMIT;
