-- SQL Generation started: 2025-05-20T19:21:47.902Z
-- Table: list_fdb_gen_df_mstr_link
-- Total records: 667

-- Initial configuration
BEGIN;
SET session_replication_role = replica;
SET maintenance_work_mem = '1GB';
SET synchronous_commit = off;
SET work_mem = '2GB';
SET temp_buffers = '1GB';
SET statement_timeout = 0;

-- Clean table
TRUNCATE TABLE form_list_fdb_gen_df_mstr_link;
COMMIT;

-- Beginning data import in batches of 50000 records
-- Each batch will have its own transaction
BEGIN;
INSERT INTO form_list_fdb_gen_df_mstr_link (id, code, dosage_form_id, gcdf, preferred_dosage_form_ind, auto_name, created_on, created_by) VALUES
(1, '1-QJ', 1, 'QJ', 1, '1 - QJ', '2025-05-20 15:21:48', 1),
(2, '2-PS', 2, 'PS', 1, '2 - PS', '2025-05-20 15:21:48', 1),
(3, '3-AA', 3, 'AA', 1, '3 - AA', '2025-05-20 15:21:48', 1),
(4, '3-AB', 3, 'AB', 1, '3 - AB', '2025-05-20 15:21:48', 1),
(5, '3-AC', 3, 'AC', 1, '3 - AC', '2025-05-20 15:21:48', 1),
(6, '3-AD', 3, 'AD', 0, '3 - AD', '2025-05-20 15:21:48', 1),
(7, '3-AG', 3, 'AG', 0, '3 - AG', '2025-05-20 15:21:48', 1),
(8, '3-AH', 3, 'AH', 0, '3 - AH', '2025-05-20 15:21:48', 1),
(9, '3-AJ', 3, 'AJ', 0, '3 - AJ', '2025-05-20 15:21:48', 1),
(10, '4-AM', 4, 'AM', 1, '4 - AM', '2025-05-20 15:21:48', 1),
(11, '5-AK', 5, 'AK', 1, '5 - AK', '2025-05-20 15:21:48', 1),
(12, '5-AP', 5, 'AP', 1, '5 - AP', '2025-05-20 15:21:48', 1),
(13, '5-AZ', 5, 'AZ', 1, '5 - AZ', '2025-05-20 15:21:48', 1),
(14, '6-AY', 6, 'AY', 1, '6 - AY', '2025-05-20 15:21:48', 1),
(15, '6-BH', 6, 'BH', 1, '6 - BH', '2025-05-20 15:21:48', 1),
(16, '7-AD', 7, 'AD', 1, '7 - AD', '2025-05-20 15:21:48', 1),
(17, '7-AE', 7, 'AE', 1, '7 - AE', '2025-05-20 15:21:48', 1),
(18, '7-AG', 7, 'AG', 1, '7 - AG', '2025-05-20 15:21:48', 1),
(19, '8-AH', 8, 'AH', 1, '8 - AH', '2025-05-20 15:21:48', 1),
(20, '8-AI', 8, 'AI', 1, '8 - AI', '2025-05-20 15:21:48', 1),
(21, '8-AJ', 8, 'AJ', 1, '8 - AJ', '2025-05-20 15:21:48', 1),
(22, '9-BT', 9, 'BT', 1, '9 - BT', '2025-05-20 15:21:48', 1),
(23, '10-AQ', 10, 'AQ', 1, '10 - AQ', '2025-05-20 15:21:48', 1),
(24, '10-AS', 10, 'AS', 1, '10 - AS', '2025-05-20 15:21:48', 1),
(25, '11-AO', 11, 'AO', 1, '11 - AO', '2025-05-20 15:21:48', 1),
(26, '11-VC', 11, 'VC', 1, '11 - VC', '2025-05-20 15:21:48', 1),
(27, '12-AW', 12, 'AW', 1, '12 - AW', '2025-05-20 15:21:48', 1),
(28, '15-AT', 15, 'AT', 1, '15 - AT', '2025-05-20 15:21:48', 1),
(29, '15-BN', 15, 'BN', 1, '15 - BN', '2025-05-20 15:21:48', 1),
(30, '15-DO', 15, 'DO', 1, '15 - DO', '2025-05-20 15:21:48', 1),
(31, '16-HE', 16, 'HE', 1, '16 - HE', '2025-05-20 15:21:48', 1),
(32, '17-HH', 17, 'HH', 1, '17 - HH', '2025-05-20 15:21:48', 1),
(33, '17-HR', 17, 'HR', 1, '17 - HR', '2025-05-20 15:21:48', 1),
(34, '18-AL', 18, 'AL', 1, '18 - AL', '2025-05-20 15:21:48', 1),
(35, '18-BF', 18, 'BF', 1, '18 - BF', '2025-05-20 15:21:48', 1),
(36, '19-HG', 19, 'HG', 1, '19 - HG', '2025-05-20 15:21:48', 1),
(37, '20-BG', 20, 'BG', 1, '20 - BG', '2025-05-20 15:21:48', 1),
(38, '21-BI', 21, 'BI', 1, '21 - BI', '2025-05-20 15:21:48', 1),
(39, '22-YE', 22, 'YE', 1, '22 - YE', '2025-05-20 15:21:48', 1),
(40, '23-EB', 23, 'EB', 1, '23 - EB', '2025-05-20 15:21:48', 1),
(41, '23-GB', 23, 'GB', 1, '23 - GB', '2025-05-20 15:21:48', 1),
(42, '24-BA', 24, 'BA', 1, '24 - BA', '2025-05-20 15:21:48', 1),
(43, '24-BB', 24, 'BB', 1, '24 - BB', '2025-05-20 15:21:48', 1),
(44, '24-BC', 24, 'BC', 1, '24 - BC', '2025-05-20 15:21:48', 1),
(45, '25-JE', 25, 'JE', 1, '25 - JE', '2025-05-20 15:21:48', 1),
(46, '26-YZ', 26, 'YZ', 1, '26 - YZ', '2025-05-20 15:21:48', 1),
(47, '27-WC', 27, 'WC', 1, '27 - WC', '2025-05-20 15:21:48', 1),
(48, '27-ZC', 27, 'ZC', 1, '27 - ZC', '2025-05-20 15:21:48', 1),
(49, '28-HA', 28, 'HA', 1, '28 - HA', '2025-05-20 15:21:48', 1),
(50, '28-HB', 28, 'HB', 1, '28 - HB', '2025-05-20 15:21:48', 1),
(51, '29-ZB', 29, 'ZB', 1, '29 - ZB', '2025-05-20 15:21:48', 1),
(52, '30-YB', 30, 'YB', 1, '30 - YB', '2025-05-20 15:21:48', 1),
(53, '31-FC', 31, 'FC', 1, '31 - FC', '2025-05-20 15:21:48', 1),
(54, '31-HL', 31, 'HL', 1, '31 - HL', '2025-05-20 15:21:48', 1),
(55, '31-WU', 31, 'WU', 1, '31 - WU', '2025-05-20 15:21:48', 1),
(56, '32-EC', 32, 'EC', 1, '32 - EC', '2025-05-20 15:21:48', 1),
(57, '33-CX', 33, 'CX', 1, '33 - CX', '2025-05-20 15:21:48', 1),
(58, '34-CU', 34, 'CU', 1, '34 - CU', '2025-05-20 15:21:48', 1),
(59, '35-CA', 35, 'CA', 1, '35 - CA', '2025-05-20 15:21:48', 1),
(60, '36-CM', 36, 'CM', 1, '36 - CM', '2025-05-20 15:21:48', 1),
(61, '37-CI', 37, 'CI', 1, '37 - CI', '2025-05-20 15:21:48', 1),
(62, '38-CJ', 38, 'CJ', 1, '38 - CJ', '2025-05-20 15:21:48', 1),
(63, '39-CG', 39, 'CG', 1, '39 - CG', '2025-05-20 15:21:48', 1),
(64, '40-CH', 40, 'CH', 1, '40 - CH', '2025-05-20 15:21:48', 1),
(65, '41-CN', 41, 'CN', 1, '41 - CN', '2025-05-20 15:21:48', 1),
(66, '42-CO', 42, 'CO', 1, '42 - CO', '2025-05-20 15:21:48', 1),
(67, '43-CP', 43, 'CP', 1, '43 - CP', '2025-05-20 15:21:48', 1),
(68, '45-CT', 45, 'CT', 1, '45 - CT', '2025-05-20 15:21:48', 1),
(69, '46-BO', 46, 'BO', 1, '46 - BO', '2025-05-20 15:21:48', 1),
(70, '47-CE', 47, 'CE', 1, '47 - CE', '2025-05-20 15:21:48', 1),
(71, '47-CF', 47, 'CF', 1, '47 - CF', '2025-05-20 15:21:48', 1),
(72, '49-UG', 49, 'UG', 1, '49 - UG', '2025-05-20 15:21:48', 1),
(73, '50-CS', 50, 'CS', 1, '50 - CS', '2025-05-20 15:21:48', 1),
(74, '51-CB', 51, 'CB', 1, '51 - CB', '2025-05-20 15:21:48', 1),
(75, '52-CC', 52, 'CC', 1, '52 - CC', '2025-05-20 15:21:48', 1),
(76, '52-CR', 52, 'CR', 1, '52 - CR', '2025-05-20 15:21:48', 1),
(77, '53-CQ', 53, 'CQ', 1, '53 - CQ', '2025-05-20 15:21:48', 1),
(78, '54-CK', 54, 'CK', 1, '54 - CK', '2025-05-20 15:21:48', 1),
(79, '55-CD', 55, 'CD', 1, '55 - CD', '2025-05-20 15:21:48', 1),
(80, '56-ZE', 56, 'ZE', 1, '56 - ZE', '2025-05-20 15:21:48', 1),
(81, '57-HI', 57, 'HI', 1, '57 - HI', '2025-05-20 15:21:48', 1),
(82, '57-HJ', 57, 'HJ', 1, '57 - HJ', '2025-05-20 15:21:48', 1),
(83, '57-WZ', 57, 'WZ', 1, '57 - WZ', '2025-05-20 15:21:48', 1),
(84, '58-BZ', 58, 'BZ', 1, '58 - BZ', '2025-05-20 15:21:48', 1),
(85, '59-EI', 59, 'EI', 1, '59 - EI', '2025-05-20 15:21:48', 1),
(86, '60-PL', 60, 'PL', 1, '60 - PL', '2025-05-20 15:21:48', 1),
(87, '60-PN', 60, 'PN', 1, '60 - PN', '2025-05-20 15:21:48', 1),
(88, '61-OY', 61, 'OY', 1, '61 - OY', '2025-05-20 15:21:48', 1),
(89, '62-BX', 62, 'BX', 1, '62 - BX', '2025-05-20 15:21:48', 1),
(90, '63-BW', 63, 'BW', 1, '63 - BW', '2025-05-20 15:21:48', 1),
(91, '64-ZU', 64, 'ZU', 1, '64 - ZU', '2025-05-20 15:21:48', 1),
(92, '65-ZO', 65, 'ZO', 1, '65 - ZO', '2025-05-20 15:21:48', 1),
(93, '66-ZV', 66, 'ZV', 1, '66 - ZV', '2025-05-20 15:21:48', 1),
(94, '67-ZN', 67, 'ZN', 1, '67 - ZN', '2025-05-20 15:21:48', 1),
(95, '68-ZI', 68, 'ZI', 1, '68 - ZI', '2025-05-20 15:21:48', 1),
(96, '69-4A', 69, '4A', 1, '69 - 4A', '2025-05-20 15:21:48', 1),
(97, '70-ZS', 70, 'ZS', 1, '70 - ZS', '2025-05-20 15:21:48', 1),
(98, '71-BY', 71, 'BY', 1, '71 - BY', '2025-05-20 15:21:48', 1),
(99, '72-ZQ', 72, 'ZQ', 1, '72 - ZQ', '2025-05-20 15:21:48', 1),
(100, '73-ZH', 73, 'ZH', 1, '73 - ZH', '2025-05-20 15:21:48', 1),
(101, '74-ZF', 74, 'ZF', 1, '74 - ZF', '2025-05-20 15:21:48', 1),
(102, '75-ZJ', 75, 'ZJ', 1, '75 - ZJ', '2025-05-20 15:21:48', 1),
(103, '76-1A', 76, '1A', 1, '76 - 1A', '2025-05-20 15:21:48', 1),
(104, '77-ZG', 77, 'ZG', 1, '77 - ZG', '2025-05-20 15:21:48', 1),
(105, '78-ZK', 78, 'ZK', 1, '78 - ZK', '2025-05-20 15:21:48', 1),
(106, '79-BV', 79, 'BV', 1, '79 - BV', '2025-05-20 15:21:48', 1),
(107, '80-ZR', 80, 'ZR', 1, '80 - ZR', '2025-05-20 15:21:48', 1),
(108, '81-DE', 81, 'DE', 1, '81 - DE', '2025-05-20 15:21:48', 1),
(109, '82-ZD', 82, 'ZD', 1, '82 - ZD', '2025-05-20 15:21:48', 1),
(110, '82-ZL', 82, 'ZL', 1, '82 - ZL', '2025-05-20 15:21:48', 1),
(111, '82-ZM', 82, 'ZM', 1, '82 - ZM', '2025-05-20 15:21:48', 1),
(112, '83-SY', 83, 'SY', 1, '83 - SY', '2025-05-20 15:21:48', 1),
(113, '84-CW', 84, 'CW', 1, '84 - CW', '2025-05-20 15:21:48', 1),
(114, '85-EF', 85, 'EF', 1, '85 - EF', '2025-05-20 15:21:48', 1),
(115, '86-EM', 86, 'EM', 1, '86 - EM', '2025-05-20 15:21:48', 1),
(116, '87-CV', 87, 'CV', 1, '87 - CV', '2025-05-20 15:21:48', 1),
(117, '88-KA', 88, 'KA', 1, '88 - KA', '2025-05-20 15:21:48', 1),
(118, '88-KM', 88, 'KM', 1, '88 - KM', '2025-05-20 15:21:48', 1),
(119, '89-KV', 89, 'KV', 1, '89 - KV', '2025-05-20 15:21:48', 1),
(120, '90-KW', 90, 'KW', 1, '90 - KW', '2025-05-20 15:21:48', 1),
(121, '91-IW', 91, 'IW', 1, '91 - IW', '2025-05-20 15:21:48', 1),
(122, '91-KH', 91, 'KH', 1, '91 - KH', '2025-05-20 15:21:48', 1),
(123, '91-KS', 91, 'KS', 1, '91 - KS', '2025-05-20 15:21:48', 1),
(124, '92-BR', 92, 'BR', 1, '92 - BR', '2025-05-20 15:21:48', 1),
(125, '93-KB', 93, 'KB', 1, '93 - KB', '2025-05-20 15:21:48', 1),
(126, '94-RR', 94, 'RR', 1, '94 - RR', '2025-05-20 15:21:48', 1),
(127, '95-PC', 95, 'PC', 1, '95 - PC', '2025-05-20 15:21:48', 1),
(128, '96-FR', 96, 'FR', 1, '96 - FR', '2025-05-20 15:21:48', 1),
(129, '97-YD', 97, 'YD', 1, '97 - YD', '2025-05-20 15:21:48', 1),
(130, '98-TD', 98, 'TD', 1, '98 - TD', '2025-05-20 15:21:48', 1),
(131, '99-GK', 99, 'GK', 1, '99 - GK', '2025-05-20 15:21:48', 1),
(132, '100-SD', 100, 'SD', 1, '100 - SD', '2025-05-20 15:21:48', 1),
(133, '101-DB', 101, 'DB', 1, '101 - DB', '2025-05-20 15:21:48', 1),
(134, '102-DW', 102, 'DW', 1, '102 - DW', '2025-05-20 15:21:48', 1),
(135, '103-DA', 103, 'DA', 1, '103 - DA', '2025-05-20 15:21:48', 1),
(136, '104-DV', 104, 'DV', 1, '104 - DV', '2025-05-20 15:21:48', 1),
(137, '105-DP', 105, 'DP', 1, '105 - DP', '2025-05-20 15:21:48', 1),
(138, '105-NS', 105, 'NS', 1, '105 - NS', '2025-05-20 15:21:48', 1),
(139, '106-SO', 106, 'SO', 1, '106 - SO', '2025-05-20 15:21:48', 1),
(140, '106-VD', 106, 'VD', 1, '106 - VD', '2025-05-20 15:21:48', 1),
(141, '107-DJ', 107, 'DJ', 1, '107 - DJ', '2025-05-20 15:21:48', 1),
(142, '107-GR', 107, 'GR', 1, '107 - GR', '2025-05-20 15:21:48', 1),
(143, '108-RO', 108, 'RO', 1, '108 - RO', '2025-05-20 15:21:48', 1),
(144, '109-DI', 109, 'DI', 1, '109 - DI', '2025-05-20 15:21:48', 1),
(145, '110-RI', 110, 'RI', 1, '110 - RI', '2025-05-20 15:21:48', 1),
(146, '111-RH', 111, 'RH', 1, '111 - RH', '2025-05-20 15:21:48', 1),
(147, '111-RT', 111, 'RT', 1, '111 - RT', '2025-05-20 15:21:48', 1),
(148, '112-RP', 112, 'RP', 1, '112 - RP', '2025-05-20 15:21:48', 1),
(149, '113-PH', 113, 'PH', 1, '113 - PH', '2025-05-20 15:21:48', 1),
(150, '114-KJ', 114, 'KJ', 1, '114 - KJ', '2025-05-20 15:21:48', 1),
(151, '115-EA', 115, 'EA', 1, '115 - EA', '2025-05-20 15:21:48', 1),
(152, '116-PO', 116, 'PO', 1, '116 - PO', '2025-05-20 15:21:48', 1),
(153, '117-EV', 117, 'EV', 1, '117 - EV', '2025-05-20 15:21:48', 1),
(154, '118-EU', 118, 'EU', 1, '118 - EU', '2025-05-20 15:21:48', 1),
(155, '119-ES', 119, 'ES', 1, '119 - ES', '2025-05-20 15:21:48', 1),
(156, '120-EQ', 120, 'EQ', 1, '120 - EQ', '2025-05-20 15:21:48', 1),
(157, '121-SE', 121, 'SE', 1, '121 - SE', '2025-05-20 15:21:48', 1),
(158, '122-RB', 122, 'RB', 1, '122 - RB', '2025-05-20 15:21:48', 1),
(159, '122-SU', 122, 'SU', 1, '122 - SU', '2025-05-20 15:21:48', 1),
(160, '123-EO', 123, 'EO', 1, '123 - EO', '2025-05-20 15:21:48', 1),
(161, '124-JN', 124, 'JN', 1, '124 - JN', '2025-05-20 15:21:48', 1),
(162, '125-RK', 125, 'RK', 1, '125 - RK', '2025-05-20 15:21:48', 1),
(163, '126-RS', 126, 'RS', 1, '126 - RS', '2025-05-20 15:21:48', 1),
(164, '126-SF', 126, 'SF', 1, '126 - SF', '2025-05-20 15:21:48', 1),
(165, '126-SG', 126, 'SG', 1, '126 - SG', '2025-05-20 15:21:48', 1),
(166, '127-SH', 127, 'SH', 1, '127 - SH', '2025-05-20 15:21:48', 1),
(167, '128-FI', 128, 'FI', 1, '128 - FI', '2025-05-20 15:21:48', 1),
(168, '129-ME', 129, 'ME', 1, '129 - ME', '2025-05-20 15:21:48', 1),
(169, '130-KO', 130, 'KO', 1, '130 - KO', '2025-05-20 15:21:48', 1),
(170, '131-PF', 131, 'PF', 1, '131 - PF', '2025-05-20 15:21:48', 1),
(171, '132-FA', 132, 'FA', 1, '132 - FA', '2025-05-20 15:21:48', 1),
(172, '133-FB', 133, 'FB', 1, '133 - FB', '2025-05-20 15:21:48', 1),
(173, '134-SB', 134, 'SB', 1, '134 - SB', '2025-05-20 15:21:48', 1),
(174, '135-AF', 135, 'AF', 1, '135 - AF', '2025-05-20 15:21:48', 1),
(175, '135-AV', 135, 'AV', 1, '135 - AV', '2025-05-20 15:21:48', 1),
(176, '136-GA', 136, 'GA', 1, '136 - GA', '2025-05-20 15:21:48', 1),
(177, '136-GE', 136, 'GE', 1, '136 - GE', '2025-05-20 15:21:48', 1),
(178, '137-JC', 137, 'JC', 1, '137 - JC', '2025-05-20 15:21:48', 1),
(179, '137-JF', 137, 'JF', 1, '137 - JF', '2025-05-20 15:21:48', 1),
(180, '137-JG', 137, 'JG', 1, '137 - JG', '2025-05-20 15:21:48', 1),
(181, '138-JR', 138, 'JR', 1, '138 - JR', '2025-05-20 15:21:48', 1),
(182, '139-GL', 139, 'GL', 1, '139 - GL', '2025-05-20 15:21:48', 1),
(183, '139-JU', 139, 'JU', 1, '139 - JU', '2025-05-20 15:21:48', 1),
(184, '140-JV', 140, 'JV', 1, '140 - JV', '2025-05-20 15:21:48', 1),
(185, '140-JX', 140, 'JX', 1, '140 - JX', '2025-05-20 15:21:48', 1),
(186, '141-GP', 141, 'GP', 1, '141 - GP', '2025-05-20 15:21:48', 1),
(187, '141-KZ', 141, 'KZ', 1, '141 - KZ', '2025-05-20 15:21:48', 1),
(188, '142-JO', 142, 'JO', 1, '142 - JO', '2025-05-20 15:21:48', 1),
(189, '142-JP', 142, 'JP', 1, '142 - JP', '2025-05-20 15:21:48', 1),
(190, '142-KN', 142, 'KN', 1, '142 - KN', '2025-05-20 15:21:48', 1),
(191, '144-KK', 144, 'KK', 1, '144 - KK', '2025-05-20 15:21:48', 1),
(192, '145-DH', 145, 'DH', 1, '145 - DH', '2025-05-20 15:21:48', 1),
(193, '145-KD', 145, 'KD', 1, '145 - KD', '2025-05-20 15:21:48', 1),
(194, '145-KR', 145, 'KR', 1, '145 - KR', '2025-05-20 15:21:48', 1),
(195, '146-JS', 146, 'JS', 1, '146 - JS', '2025-05-20 15:21:48', 1),
(196, '147-JI', 147, 'JI', 1, '147 - JI', '2025-05-20 15:21:48', 1),
(197, '148-JK', 148, 'JK', 1, '148 - JK', '2025-05-20 15:21:48', 1),
(198, '149-JQ', 149, 'JQ', 1, '149 - JQ', '2025-05-20 15:21:48', 1),
(199, '150-SV', 150, 'SV', 1, '150 - SV', '2025-05-20 15:21:48', 1),
(200, '151-6Q', 151, '6Q', 1, '151 - 6Q', '2025-05-20 15:21:48', 1),
(201, '153-TG', 153, 'TG', 1, '153 - TG', '2025-05-20 15:21:48', 1),
(202, '153-TK', 153, 'TK', 1, '153 - TK', '2025-05-20 15:21:48', 1),
(203, '154-BJ', 154, 'BJ', 1, '154 - BJ', '2025-05-20 15:21:48', 1),
(204, '154-DY', 154, 'DY', 1, '154 - DY', '2025-05-20 15:21:48', 1),
(205, '154-FK', 154, 'FK', 1, '154 - FK', '2025-05-20 15:21:48', 1),
(206, '155-IA', 155, 'IA', 1, '155 - IA', '2025-05-20 15:21:48', 1),
(207, '156-YQ', 156, 'YQ', 1, '156 - YQ', '2025-05-20 15:21:48', 1),
(208, '157-GH', 157, 'GH', 1, '157 - GH', '2025-05-20 15:21:48', 1),
(209, '157-GI', 157, 'GI', 1, '157 - GI', '2025-05-20 15:21:48', 1),
(210, '157-GJ', 157, 'GJ', 1, '157 - GJ', '2025-05-20 15:21:48', 1),
(211, '158-GZ', 158, 'GZ', 1, '158 - GZ', '2025-05-20 15:21:48', 1),
(212, '159-QB', 159, 'QB', 1, '159 - QB', '2025-05-20 15:21:48', 1),
(213, '160-QL', 160, 'QL', 1, '160 - QL', '2025-05-20 15:21:48', 1),
(214, '161-IO', 161, 'IO', 1, '161 - IO', '2025-05-20 15:21:48', 1),
(215, '161-IP', 161, 'IP', 1, '161 - IP', '2025-05-20 15:21:48', 1),
(216, '162-YI', 162, 'YI', 1, '162 - YI', '2025-05-20 15:21:48', 1),
(217, '163-YP', 163, 'YP', 1, '163 - YP', '2025-05-20 15:21:48', 1),
(218, '164-YU', 164, 'YU', 1, '164 - YU', '2025-05-20 15:21:48', 1),
(219, '165-YX', 165, 'YX', 1, '165 - YX', '2025-05-20 15:21:48', 1),
(220, '166-JB', 166, 'JB', 1, '166 - JB', '2025-05-20 15:21:48', 1),
(221, '166-JD', 166, 'JD', 1, '166 - JD', '2025-05-20 15:21:48', 1),
(222, '167-II', 167, 'II', 1, '167 - II', '2025-05-20 15:21:48', 1),
(223, '167-JA', 167, 'JA', 1, '167 - JA', '2025-05-20 15:21:48', 1),
(224, '168-JT', 168, 'JT', 1, '168 - JT', '2025-05-20 15:21:48', 1),
(225, '169-JW', 169, 'JW', 1, '169 - JW', '2025-05-20 15:21:48', 1),
(226, '170-YK', 170, 'YK', 1, '170 - YK', '2025-05-20 15:21:48', 1),
(227, '171-BM', 171, 'BM', 1, '171 - BM', '2025-05-20 15:21:48', 1),
(228, '172-BU', 172, 'BU', 1, '172 - BU', '2025-05-20 15:21:48', 1),
(229, '173-OR', 173, 'OR', 1, '173 - OR', '2025-05-20 15:21:48', 1),
(230, '174-OP', 174, 'OP', 1, '174 - OP', '2025-05-20 15:21:48', 1),
(231, '175-OT', 175, 'OT', 1, '175 - OT', '2025-05-20 15:21:48', 1),
(232, '176-BS', 176, 'BS', 1, '176 - BS', '2025-05-20 15:21:48', 1),
(233, '177-OS', 177, 'OS', 1, '177 - OS', '2025-05-20 15:21:48', 1),
(234, '178-OG', 178, 'OG', 1, '178 - OG', '2025-05-20 15:21:48', 1),
(235, '179-YY', 179, 'YY', 1, '179 - YY', '2025-05-20 15:21:48', 1),
(236, '180-PB', 180, 'PB', 1, '180 - PB', '2025-05-20 15:21:48', 1),
(237, '181-YF', 181, 'YF', 1, '181 - YF', '2025-05-20 15:21:48', 1),
(238, '182-SI', 182, 'SI', 1, '182 - SI', '2025-05-20 15:21:48', 1),
(239, '183-LL', 183, 'LL', 1, '183 - LL', '2025-05-20 15:21:48', 1),
(240, '183-RL', 183, 'RL', 1, '183 - RL', '2025-05-20 15:21:48', 1),
(241, '183-SL', 183, 'SL', 1, '183 - SL', '2025-05-20 15:21:48', 1),
(242, '184-LI', 184, 'LI', 1, '184 - LI', '2025-05-20 15:21:48', 1),
(243, '185-SK', 185, 'SK', 1, '185 - SK', '2025-05-20 15:21:48', 1),
(244, '185-SZ', 185, 'SZ', 1, '185 - SZ', '2025-05-20 15:21:48', 1),
(245, '186-JZ', 186, 'JZ', 1, '186 - JZ', '2025-05-20 15:21:48', 1),
(246, '187-LO', 187, 'LO', 1, '187 - LO', '2025-05-20 15:21:48', 1),
(247, '188-KE', 188, 'KE', 1, '188 - KE', '2025-05-20 15:21:48', 1),
(248, '188-LE', 188, 'LE', 1, '188 - LE', '2025-05-20 15:21:48', 1),
(249, '189-LP', 189, 'LP', 1, '189 - LP', '2025-05-20 15:21:48', 1),
(250, '189-TL', 189, 'TL', 1, '189 - TL', '2025-05-20 15:21:48', 1),
(251, '190-LP', 190, 'LP', 0, '190 - LP', '2025-05-20 15:21:48', 1),
(252, '191-LX', 191, 'LX', 1, '191 - LX', '2025-05-20 15:21:48', 1),
(253, '192-KL', 192, 'KL', 1, '192 - KL', '2025-05-20 15:21:48', 1),
(254, '193-PM', 193, 'PM', 1, '193 - PM', '2025-05-20 15:21:48', 1),
(255, '194-KU', 194, 'KU', 1, '194 - KU', '2025-05-20 15:21:48', 1),
(256, '195-ZA', 195, 'ZA', 1, '195 - ZA', '2025-05-20 15:21:48', 1),
(257, '196-GM', 196, 'GM', 1, '196 - GM', '2025-05-20 15:21:48', 1),
(258, '196-GN', 196, 'GN', 1, '196 - GN', '2025-05-20 15:21:48', 1),
(259, '196-MI', 196, 'MI', 1, '196 - MI', '2025-05-20 15:21:48', 1),
(260, '197-SM', 197, 'SM', 1, '197 - SM', '2025-05-20 15:21:48', 1),
(261, '198-MA', 198, 'MA', 1, '198 - MA', '2025-05-20 15:21:48', 1),
(262, '199-FH', 199, 'FH', 1, '199 - FH', '2025-05-20 15:21:48', 1),
(263, '200-YH', 200, 'YH', 1, '200 - YH', '2025-05-20 15:21:48', 1),
(264, '201-YA', 201, 'YA', 1, '201 - YA', '2025-05-20 15:21:48', 1),
(265, '202-IL', 202, 'IL', 1, '202 - IL', '2025-05-20 15:21:48', 1),
(266, '203-OI', 203, 'OI', 1, '203 - OI', '2025-05-20 15:21:48', 1),
(267, '203-SQ', 203, 'SQ', 1, '203 - SQ', '2025-05-20 15:21:48', 1),
(268, '204-IZ', 204, 'IZ', 1, '204 - IZ', '2025-05-20 15:21:48', 1),
(269, '205-4P', 205, '4P', 1, '205 - 4P', '2025-05-20 15:21:48', 1),
(270, '206-OA', 206, 'OA', 1, '206 - OA', '2025-05-20 15:21:48', 1),
(271, '206-OB', 206, 'OB', 1, '206 - OB', '2025-05-20 15:21:48', 1),
(272, '206-OC', 206, 'OC', 1, '206 - OC', '2025-05-20 15:21:48', 1),
(273, '207-OV', 207, 'OV', 1, '207 - OV', '2025-05-20 15:21:48', 1),
(274, '208-OW', 208, 'OW', 1, '208 - OW', '2025-05-20 15:21:48', 1),
(275, '209-KG', 209, 'KG', 1, '209 - KG', '2025-05-20 15:21:48', 1),
(276, '209-LA', 209, 'LA', 1, '209 - LA', '2025-05-20 15:21:48', 1),
(277, '210-ZP', 210, 'ZP', 1, '210 - ZP', '2025-05-20 15:21:48', 1),
(278, '211-8J', 211, '8J', 1, '211 - 8J', '2025-05-20 15:21:48', 1),
(279, '211-PP', 211, 'PP', 1, '211 - PP', '2025-05-20 15:21:48', 1),
(280, '212-JM', 212, 'JM', 1, '212 - JM', '2025-05-20 15:21:48', 1),
(281, '213-YM', 213, 'YM', 1, '213 - YM', '2025-05-20 15:21:48', 1),
(282, '214-ET', 214, 'ET', 1, '214 - ET', '2025-05-20 15:21:48', 1),
(283, '215-KP', 215, 'KP', 1, '215 - KP', '2025-05-20 15:21:48', 1),
(284, '215-KQ', 215, 'KQ', 1, '215 - KQ', '2025-05-20 15:21:48', 1),
(285, '216-QM', 216, 'QM', 1, '216 - QM', '2025-05-20 15:21:48', 1),
(286, '217-QN', 217, 'QN', 1, '217 - QN', '2025-05-20 15:21:48', 1),
(287, '218-QS', 218, 'QS', 1, '218 - QS', '2025-05-20 15:21:48', 1),
(288, '219-PV', 219, 'PV', 1, '219 - PV', '2025-05-20 15:21:48', 1),
(289, '220-PR', 220, 'PR', 1, '220 - PR', '2025-05-20 15:21:48', 1),
(290, '221-QH', 221, 'QH', 1, '221 - QH', '2025-05-20 15:21:48', 1),
(291, '222-PQ', 222, 'PQ', 1, '222 - PQ', '2025-05-20 15:21:48', 1),
(292, '223-PK', 223, 'PK', 1, '223 - PK', '2025-05-20 15:21:48', 1),
(293, '224-QG', 224, 'QG', 1, '224 - QG', '2025-05-20 15:21:48', 1),
(294, '225-RM', 225, 'RM', 1, '225 - RM', '2025-05-20 15:21:48', 1),
(295, '225-TP', 225, 'TP', 1, '225 - TP', '2025-05-20 15:21:48', 1),
(296, '226-IJ', 226, 'IJ', 1, '226 - IJ', '2025-05-20 15:21:48', 1),
(297, '226-IN', 226, 'IN', 1, '226 - IN', '2025-05-20 15:21:48', 1),
(298, '227-IK', 227, 'IK', 1, '227 - IK', '2025-05-20 15:21:48', 1),
(299, '228-IE', 228, 'IE', 1, '228 - IE', '2025-05-20 15:21:48', 1),
(300, '229-IC', 229, 'IC', 1, '229 - IC', '2025-05-20 15:21:48', 1),
(301, '230-TX', 230, 'TX', 1, '230 - TX', '2025-05-20 15:21:48', 1),
(302, '231-HC', 231, 'HC', 1, '231 - HC', '2025-05-20 15:21:48', 1),
(303, '231-HD', 231, 'HD', 1, '231 - HD', '2025-05-20 15:21:48', 1),
(304, '232-EJ', 232, 'EJ', 1, '232 - EJ', '2025-05-20 15:21:48', 1),
(305, '233-GX', 233, 'GX', 1, '233 - GX', '2025-05-20 15:21:48', 1),
(306, '234-CY', 234, 'CY', 1, '234 - CY', '2025-05-20 15:21:48', 1),
(307, '235-HU', 235, 'HU', 1, '235 - HU', '2025-05-20 15:21:48', 1),
(308, '235-HZ', 235, 'HZ', 1, '235 - HZ', '2025-05-20 15:21:48', 1),
(309, '236-DD', 236, 'DD', 1, '236 - DD', '2025-05-20 15:21:48', 1),
(310, '237-EK', 237, 'EK', 1, '237 - EK', '2025-05-20 15:21:48', 1),
(311, '238-PA', 238, 'PA', 1, '238 - PA', '2025-05-20 15:21:48', 1),
(312, '238-PU', 238, 'PU', 1, '238 - PU', '2025-05-20 15:21:48', 1),
(313, '239-GV', 239, 'GV', 1, '239 - GV', '2025-05-20 15:21:48', 1),
(314, '239-JL', 239, 'JL', 1, '239 - JL', '2025-05-20 15:21:48', 1),
(315, '240-IQ', 240, 'IQ', 1, '240 - IQ', '2025-05-20 15:21:48', 1),
(316, '242-PE', 242, 'PE', 1, '242 - PE', '2025-05-20 15:21:48', 1),
(317, '243-IR', 243, 'IR', 1, '243 - IR', '2025-05-20 15:21:48', 1),
(318, '244-EZ', 244, 'EZ', 1, '244 - EZ', '2025-05-20 15:21:48', 1),
(319, '245-JH', 245, 'JH', 1, '245 - JH', '2025-05-20 15:21:48', 1),
(320, '245-JJ', 245, 'JJ', 1, '245 - JJ', '2025-05-20 15:21:48', 1),
(321, '246-QV', 246, 'QV', 1, '246 - QV', '2025-05-20 15:21:48', 1),
(322, '247-RC', 247, 'RC', 1, '247 - RC', '2025-05-20 15:21:48', 1),
(323, '247-SS', 247, 'SS', 1, '247 - SS', '2025-05-20 15:21:48', 1),
(324, '248-RE', 248, 'RE', 1, '248 - RE', '2025-05-20 15:21:48', 1),
(325, '249-FS', 249, 'FS', 1, '249 - FS', '2025-05-20 15:21:48', 1),
(326, '250-HT', 250, 'HT', 1, '250 - HT', '2025-05-20 15:21:48', 1),
(327, '251-EE', 251, 'EE', 1, '251 - EE', '2025-05-20 15:21:48', 1),
(328, '252-ED', 252, 'ED', 1, '252 - ED', '2025-05-20 15:21:48', 1),
(329, '252-EP', 252, 'EP', 1, '252 - EP', '2025-05-20 15:21:48', 1),
(330, '252-ER', 252, 'ER', 1, '252 - ER', '2025-05-20 15:21:48', 1),
(331, '253-EY', 253, 'EY', 1, '253 - EY', '2025-05-20 15:21:48', 1),
(332, '254-RA', 254, 'RA', 1, '254 - RA', '2025-05-20 15:21:48', 1),
(333, '255-KI', 255, 'KI', 1, '255 - KI', '2025-05-20 15:21:48', 1),
(334, '255-MP', 255, 'MP', 1, '255 - MP', '2025-05-20 15:21:48', 1),
(335, '255-QT', 255, 'QT', 1, '255 - QT', '2025-05-20 15:21:48', 1),
(336, '256-KF', 256, 'KF', 1, '256 - KF', '2025-05-20 15:21:48', 1),
(337, '257-IY', 257, 'IY', 1, '257 - IY', '2025-05-20 15:21:48', 1),
(338, '257-JY', 257, 'JY', 1, '257 - JY', '2025-05-20 15:21:48', 1),
(339, '258-RJ', 258, 'RJ', 1, '258 - RJ', '2025-05-20 15:21:48', 1),
(340, '259-HY', 259, 'HY', 1, '259 - HY', '2025-05-20 15:21:48', 1),
(341, '260-HM', 260, 'HM', 1, '260 - HM', '2025-05-20 15:21:48', 1),
(342, '261-HN', 261, 'HN', 1, '261 - HN', '2025-05-20 15:21:48', 1),
(343, '261-HP', 261, 'HP', 1, '261 - HP', '2025-05-20 15:21:48', 1),
(344, '262-SW', 262, 'SW', 1, '262 - SW', '2025-05-20 15:21:48', 1),
(345, '263-SA', 263, 'SA', 1, '263 - SA', '2025-05-20 15:21:48', 1),
(346, '264-SJ', 264, 'SJ', 1, '264 - SJ', '2025-05-20 15:21:48', 1),
(347, '265-HK', 265, 'HK', 1, '265 - HK', '2025-05-20 15:21:48', 1),
(348, '266-IB', 266, 'IB', 1, '266 - IB', '2025-05-20 15:21:48', 1),
(349, '266-IH', 266, 'IH', 1, '266 - IH', '2025-05-20 15:21:48', 1),
(350, '267-QP', 267, 'QP', 1, '267 - QP', '2025-05-20 15:21:48', 1),
(351, '268-NZ', 268, 'NZ', 1, '268 - NZ', '2025-05-20 15:21:48', 1),
(352, '268-PI', 268, 'PI', 1, '268 - PI', '2025-05-20 15:21:48', 1),
(353, '269-GD', 269, 'GD', 1, '269 - GD', '2025-05-20 15:21:48', 1),
(354, '270-SP', 270, 'SP', 1, '270 - SP', '2025-05-20 15:21:48', 1),
(355, '271-YV', 271, 'YV', 1, '271 - YV', '2025-05-20 15:21:48', 1),
(356, '272-GS', 272, 'GS', 1, '272 - GS', '2025-05-20 15:21:48', 1),
(357, '272-KX', 272, 'KX', 1, '272 - KX', '2025-05-20 15:21:48', 1),
(358, '273-BK', 273, 'BK', 1, '273 - BK', '2025-05-20 15:21:48', 1),
(359, '273-BQ', 273, 'BQ', 1, '273 - BQ', '2025-05-20 15:21:48', 1),
(360, '273-EW', 273, 'EW', 1, '273 - EW', '2025-05-20 15:21:48', 1),
(361, '274-AU', 274, 'AU', 1, '274 - AU', '2025-05-20 15:21:48', 1),
(362, '274-AX', 274, 'AX', 1, '274 - AX', '2025-05-20 15:21:48', 1),
(363, '274-BD', 274, 'BD', 1, '274 - BD', '2025-05-20 15:21:48', 1),
(364, '275-AR', 275, 'AR', 1, '275 - AR', '2025-05-20 15:21:48', 1),
(365, '276-EG', 276, 'EG', 1, '276 - EG', '2025-05-20 15:21:48', 1),
(366, '276-EH', 276, 'EH', 1, '276 - EH', '2025-05-20 15:21:48', 1),
(367, '277-YR', 277, 'YR', 1, '277 - YR', '2025-05-20 15:21:48', 1),
(368, '278-QA', 278, 'QA', 1, '278 - QA', '2025-05-20 15:21:48', 1),
(369, '279-QD', 279, 'QD', 1, '279 - QD', '2025-05-20 15:21:48', 1),
(370, '280-QC', 280, 'QC', 1, '280 - QC', '2025-05-20 15:21:48', 1),
(371, '281-BL', 281, 'BL', 1, '281 - BL', '2025-05-20 15:21:48', 1),
(372, '283-KY', 283, 'KY', 1, '283 - KY', '2025-05-20 15:21:48', 1),
(373, '284-PZ', 284, 'PZ', 1, '284 - PZ', '2025-05-20 15:21:48', 1),
(374, '284-QO', 284, 'QO', 1, '284 - QO', '2025-05-20 15:21:48', 1),
(375, '285-RN', 285, 'RN', 1, '285 - RN', '2025-05-20 15:21:48', 1),
(376, '286-QI', 286, 'QI', 1, '286 - QI', '2025-05-20 15:21:48', 1),
(377, '286-XD', 286, 'XD', 1, '286 - XD', '2025-05-20 15:21:48', 1),
(378, '287-DS', 287, 'DS', 1, '287 - DS', '2025-05-20 15:21:48', 1),
(379, '288-SN', 288, 'SN', 1, '288 - SN', '2025-05-20 15:21:48', 1),
(380, '288-WM', 288, 'WM', 1, '288 - WM', '2025-05-20 15:21:48', 1),
(381, '289-PJ', 289, 'PJ', 1, '289 - PJ', '2025-05-20 15:21:48', 1),
(382, '290-PY', 290, 'PY', 1, '290 - PY', '2025-05-20 15:21:48', 1),
(383, '291-RG', 291, 'RG', 1, '291 - RG', '2025-05-20 15:21:48', 1),
(384, '292-SC', 292, 'SC', 1, '292 - SC', '2025-05-20 15:21:48', 1),
(385, '293-PD', 293, 'PD', 1, '293 - PD', '2025-05-20 15:21:48', 1),
(386, '293-QF', 293, 'QF', 1, '293 - QF', '2025-05-20 15:21:48', 1),
(387, '293-QX', 293, 'QX', 1, '293 - QX', '2025-05-20 15:21:48', 1),
(388, '294-KC', 294, 'KC', 1, '294 - KC', '2025-05-20 15:21:48', 1),
(389, '294-SR', 294, 'SR', 1, '294 - SR', '2025-05-20 15:21:48', 1),
(390, '295-YS', 295, 'YS', 1, '295 - YS', '2025-05-20 15:21:48', 1),
(391, '296-EL', 296, 'EL', 1, '296 - EL', '2025-05-20 15:21:48', 1),
(392, '296-VV', 296, 'VV', 1, '296 - VV', '2025-05-20 15:21:48', 1),
(393, '297-YW', 297, 'YW', 1, '297 - YW', '2025-05-20 15:21:48', 1),
(394, '298-GY', 298, 'GY', 1, '298 - GY', '2025-05-20 15:21:48', 1),
(395, '299-HW', 299, 'HW', 1, '299 - HW', '2025-05-20 15:21:48', 1),
(396, '300-YJ', 300, 'YJ', 1, '300 - YJ', '2025-05-20 15:21:48', 1),
(397, '302-YL', 302, 'YL', 1, '302 - YL', '2025-05-20 15:21:48', 1),
(398, '303-HO', 303, 'HO', 1, '303 - HO', '2025-05-20 15:21:48', 1),
(399, '304-YC', 304, 'YC', 1, '304 - YC', '2025-05-20 15:21:48', 1),
(400, '305-RF', 305, 'RF', 1, '305 - RF', '2025-05-20 15:21:48', 1),
(401, '305-ST', 305, 'ST', 1, '305 - ST', '2025-05-20 15:21:48', 1),
(402, '306-GT', 306, 'GT', 1, '306 - GT', '2025-05-20 15:21:48', 1),
(403, '307-DM', 307, 'DM', 1, '307 - DM', '2025-05-20 15:21:48', 1),
(404, '308-TA', 308, 'TA', 1, '308 - TA', '2025-05-20 15:21:48', 1),
(405, '308-WQ', 308, 'WQ', 0, '308 - WQ', '2025-05-20 15:21:48', 1),
(406, '309-UQ', 309, 'UQ', 1, '309 - UQ', '2025-05-20 15:21:48', 1),
(407, '310-UR', 310, 'UR', 1, '310 - UR', '2025-05-20 15:21:48', 1),
(408, '311-UZ', 311, 'UZ', 1, '311 - UZ', '2025-05-20 15:21:48', 1),
(409, '312-4U', 312, '4U', 1, '312 - 4U', '2025-05-20 15:21:48', 1),
(410, '313-UT', 313, 'UT', 1, '313 - UT', '2025-05-20 15:21:48', 1),
(411, '314-UH', 314, 'UH', 1, '314 - UH', '2025-05-20 15:21:48', 1),
(412, '315-UB', 315, 'UB', 1, '315 - UB', '2025-05-20 15:21:48', 1),
(413, '316-UO', 316, 'UO', 1, '316 - UO', '2025-05-20 15:21:48', 1),
(414, '317-UP', 317, 'UP', 1, '317 - UP', '2025-05-20 15:21:48', 1),
(415, '318-TZ', 318, 'TZ', 1, '318 - TZ', '2025-05-20 15:21:48', 1),
(416, '319-UK', 319, 'UK', 1, '319 - UK', '2025-05-20 15:21:48', 1),
(417, '320-TQ', 320, 'TQ', 1, '320 - TQ', '2025-05-20 15:21:48', 1),
(418, '321-TS', 321, 'TS', 1, '321 - TS', '2025-05-20 15:21:48', 1),
(419, '322-TM', 322, 'TM', 1, '322 - TM', '2025-05-20 15:21:48', 1),
(420, '323-EX', 323, 'EX', 1, '323 - EX', '2025-05-20 15:21:48', 1),
(421, '323-TI', 323, 'TI', 1, '323 - TI', '2025-05-20 15:21:48', 1),
(422, '324-UF', 324, 'UF', 1, '324 - UF', '2025-05-20 15:21:48', 1),
(423, '325-UV', 325, 'UV', 1, '325 - UV', '2025-05-20 15:21:48', 1),
(424, '326-UJ', 326, 'UJ', 1, '326 - UJ', '2025-05-20 15:21:48', 1),
(425, '327-US', 327, 'US', 1, '327 - US', '2025-05-20 15:21:48', 1),
(426, '328-TV', 328, 'TV', 1, '328 - TV', '2025-05-20 15:21:48', 1),
(427, '329-TY', 329, 'TY', 1, '329 - TY', '2025-05-20 15:21:48', 1),
(428, '330-TC', 330, 'TC', 1, '330 - TC', '2025-05-20 15:21:48', 1),
(429, '331-TE', 331, 'TE', 1, '331 - TE', '2025-05-20 15:21:48', 1),
(430, '332-UM', 332, 'UM', 1, '332 - UM', '2025-05-20 15:21:48', 1),
(431, '333-TJ', 333, 'TJ', 1, '333 - TJ', '2025-05-20 15:21:48', 1),
(432, '334-UD', 334, 'UD', 1, '334 - UD', '2025-05-20 15:21:48', 1),
(433, '335-TO', 335, 'TO', 1, '335 - TO', '2025-05-20 15:21:48', 1),
(434, '336-TF', 336, 'TF', 1, '336 - TF', '2025-05-20 15:21:48', 1),
(435, '337-UE', 337, 'UE', 1, '337 - UE', '2025-05-20 15:21:48', 1),
(436, '339-TN', 339, 'TN', 1, '339 - TN', '2025-05-20 15:21:48', 1),
(437, '340-TH', 340, 'TH', 1, '340 - TH', '2025-05-20 15:21:48', 1),
(438, '341-TR', 341, 'TR', 1, '341 - TR', '2025-05-20 15:21:48', 1),
(439, '342-UL', 342, 'UL', 1, '342 - UL', '2025-05-20 15:21:48', 1),
(440, '343-UA', 343, 'UA', 1, '343 - UA', '2025-05-20 15:21:48', 1),
(441, '344-TB', 344, 'TB', 1, '344 - TB', '2025-05-20 15:21:48', 1),
(442, '345-TU', 345, 'TU', 1, '345 - TU', '2025-05-20 15:21:48', 1),
(443, '346-YN', 346, 'YN', 1, '346 - YN', '2025-05-20 15:21:48', 1),
(444, '347-EN', 347, 'EN', 1, '347 - EN', '2025-05-20 15:21:48', 1),
(445, '348-YT', 348, 'YT', 1, '348 - YT', '2025-05-20 15:21:48', 1),
(446, '349-WB', 349, 'WB', 1, '349 - WB', '2025-05-20 15:21:48', 1),
(447, '350-PW', 350, 'PW', 1, '350 - PW', '2025-05-20 15:21:48', 1),
(448, '350-PX', 350, 'PX', 1, '350 - PX', '2025-05-20 15:21:48', 1),
(449, '351-SX', 351, 'SX', 1, '351 - SX', '2025-05-20 15:21:48', 1),
(450, '352-HF', 352, 'HF', 1, '352 - HF', '2025-05-20 15:21:48', 1),
(451, '353-KT', 353, 'KT', 1, '353 - KT', '2025-05-20 15:21:48', 1),
(452, '354-YO', 354, 'YO', 1, '354 - YO', '2025-05-20 15:21:48', 1),
(453, '355-ZT', 355, 'ZT', 1, '355 - ZT', '2025-05-20 15:21:48', 1),
(454, '356-TT', 356, 'TT', 1, '356 - TT', '2025-05-20 15:21:48', 1),
(455, '357-UN', 357, 'UN', 1, '357 - UN', '2025-05-20 15:21:48', 1),
(456, '358-HS', 358, 'HS', 1, '358 - HS', '2025-05-20 15:21:48', 1),
(457, '358-HV', 358, 'HV', 1, '358 - HV', '2025-05-20 15:21:48', 1),
(458, '358-NV', 358, 'NV', 1, '358 - NV', '2025-05-20 15:21:48', 1),
(459, '359-AN', 359, 'AN', 1, '359 - AN', '2025-05-20 15:21:48', 1),
(460, '359-BE', 359, 'BE', 1, '359 - BE', '2025-05-20 15:21:48', 1),
(461, '360-IF', 360, 'IF', 1, '360 - IF', '2025-05-20 15:21:48', 1),
(462, '360-IG', 360, 'IG', 1, '360 - IG', '2025-05-20 15:21:48', 1),
(463, '362-TW', 362, 'TW', 1, '362 - TW', '2025-05-20 15:21:48', 1),
(464, '363-WA', 363, 'WA', 1, '363 - WA', '2025-05-20 15:21:48', 1),
(465, '364-WI', 364, 'WI', 1, '364 - WI', '2025-05-20 15:21:48', 1),
(466, '384-PG', 384, 'PG', 1, '384 - PG', '2025-05-20 15:21:48', 1),
(467, '394-HJ', 394, 'HJ', 0, '394 - HJ', '2025-05-20 15:21:48', 1),
(468, '394-HQ', 394, 'HQ', 1, '394 - HQ', '2025-05-20 15:21:48', 1),
(469, '394-HX', 394, 'HX', 1, '394 - HX', '2025-05-20 15:21:48', 1),
(470, '394-WZ', 394, 'WZ', 0, '394 - WZ', '2025-05-20 15:21:48', 1),
(471, '417-ID', 417, 'ID', 1, '417 - ID', '2025-05-20 15:21:48', 1),
(472, '423-DF', 423, 'DF', 1, '423 - DF', '2025-05-20 15:21:48', 1),
(473, '425-DX', 425, 'DX', 1, '425 - DX', '2025-05-20 15:21:48', 1),
(474, '426-ZW', 426, 'ZW', 1, '426 - ZW', '2025-05-20 15:21:48', 1),
(475, '427-DK', 427, 'DK', 1, '427 - DK', '2025-05-20 15:21:48', 1),
(476, '428-DL', 428, 'DL', 1, '428 - DL', '2025-05-20 15:21:48', 1),
(477, '429-DN', 429, 'DN', 1, '429 - DN', '2025-05-20 15:21:48', 1),
(478, '430-PT', 430, 'PT', 1, '430 - PT', '2025-05-20 15:21:48', 1),
(479, '431-UU', 431, 'UU', 1, '431 - UU', '2025-05-20 15:21:48', 1),
(480, '432-OD', 432, 'OD', 1, '432 - OD', '2025-05-20 15:21:48', 1),
(481, '433-DR', 433, 'DR', 1, '433 - DR', '2025-05-20 15:21:48', 1),
(482, '434-RU', 434, 'RU', 1, '434 - RU', '2025-05-20 15:21:48', 1),
(483, '434-RV', 434, 'RV', 1, '434 - RV', '2025-05-20 15:21:48', 1),
(484, '435-DT', 435, 'DT', 1, '435 - DT', '2025-05-20 15:21:48', 1),
(485, '436-DQ', 436, 'DQ', 1, '436 - DQ', '2025-05-20 15:21:48', 1),
(486, '437-DU', 437, 'DU', 1, '437 - DU', '2025-05-20 15:21:48', 1),
(487, '438-DZ', 438, 'DZ', 1, '438 - DZ', '2025-05-20 15:21:48', 1),
(488, '439-RQ', 439, 'RQ', 1, '439 - RQ', '2025-05-20 15:21:48', 1),
(489, '440-IX', 440, 'IX', 1, '440 - IX', '2025-05-20 15:21:48', 1),
(490, '441-RZ', 441, 'RZ', 1, '441 - RZ', '2025-05-20 15:21:48', 1),
(491, '442-FD', 442, 'FD', 1, '442 - FD', '2025-05-20 15:21:48', 1),
(492, '444-DG', 444, 'DG', 1, '444 - DG', '2025-05-20 15:21:48', 1),
(493, '445-FE', 445, 'FE', 1, '445 - FE', '2025-05-20 15:21:48', 1),
(494, '446-FF', 446, 'FF', 1, '446 - FF', '2025-05-20 15:21:48', 1),
(495, '447-FG', 447, 'FG', 1, '447 - FG', '2025-05-20 15:21:48', 1),
(496, '448-FJ', 448, 'FJ', 1, '448 - FJ', '2025-05-20 15:21:48', 1),
(497, '449-OE', 449, 'OE', 1, '449 - OE', '2025-05-20 15:21:48', 1),
(498, '450-GU', 450, 'GU', 1, '450 - GU', '2025-05-20 15:21:48', 1),
(499, '451-GW', 451, 'GW', 1, '451 - GW', '2025-05-20 15:21:48', 1),
(500, '452-FL', 452, 'FL', 1, '452 - FL', '2025-05-20 15:21:48', 1),
(501, '453-FM', 453, 'FM', 1, '453 - FM', '2025-05-20 15:21:48', 1),
(502, '454-FN', 454, 'FN', 1, '454 - FN', '2025-05-20 15:21:48', 1),
(503, '455-UW', 455, 'UW', 1, '455 - UW', '2025-05-20 15:21:48', 1),
(504, '458-UX', 458, 'UX', 1, '458 - UX', '2025-05-20 15:21:48', 1),
(505, '459-FO', 459, 'FO', 1, '459 - FO', '2025-05-20 15:21:48', 1),
(506, '460-VS', 460, 'VS', 1, '460 - VS', '2025-05-20 15:21:48', 1),
(507, '461-FP', 461, 'FP', 1, '461 - FP', '2025-05-20 15:21:48', 1),
(508, '462-GF', 462, 'GF', 1, '462 - GF', '2025-05-20 15:21:48', 1),
(509, '463-FQ', 463, 'FQ', 1, '463 - FQ', '2025-05-20 15:21:48', 1),
(510, '464-FT', 464, 'FT', 1, '464 - FT', '2025-05-20 15:21:48', 1),
(511, '466-QZ', 466, 'QZ', 1, '466 - QZ', '2025-05-20 15:21:48', 1),
(512, '467-FU', 467, 'FU', 1, '467 - FU', '2025-05-20 15:21:48', 1),
(513, '468-OH', 468, 'OH', 1, '468 - OH', '2025-05-20 15:21:48', 1),
(514, '469-FV', 469, 'FV', 1, '469 - FV', '2025-05-20 15:21:48', 1),
(515, '470-FW', 470, 'FW', 1, '470 - FW', '2025-05-20 15:21:48', 1),
(516, '471-IS', 471, 'IS', 1, '471 - IS', '2025-05-20 15:21:48', 1),
(517, '471-IT', 471, 'IT', 1, '471 - IT', '2025-05-20 15:21:48', 1),
(518, '472-QE', 472, 'QE', 1, '472 - QE', '2025-05-20 15:21:48', 1),
(519, '472-VR', 472, 'VR', 1, '472 - VR', '2025-05-20 15:21:48', 1),
(520, '474-VL', 474, 'VL', 1, '474 - VL', '2025-05-20 15:21:48', 1),
(521, '475-FX', 475, 'FX', 1, '475 - FX', '2025-05-20 15:21:48', 1),
(522, '475-WS', 475, 'WS', 1, '475 - WS', '2025-05-20 15:21:48', 1),
(523, '476-FY', 476, 'FY', 1, '476 - FY', '2025-05-20 15:21:48', 1),
(524, '476-VG', 476, 'VG', 1, '476 - VG', '2025-05-20 15:21:48', 1),
(525, '479-ZX', 479, 'ZX', 1, '479 - ZX', '2025-05-20 15:21:48', 1),
(526, '481-GQ', 481, 'GQ', 1, '481 - GQ', '2025-05-20 15:21:48', 1),
(527, '483-GC', 483, 'GC', 1, '483 - GC', '2025-05-20 15:21:48', 1),
(528, '484-FZ', 484, 'FZ', 1, '484 - FZ', '2025-05-20 15:21:48', 1),
(529, '485-OJ', 485, 'OJ', 1, '485 - OJ', '2025-05-20 15:21:48', 1),
(530, '487-ZY', 487, 'ZY', 1, '487 - ZY', '2025-05-20 15:21:48', 1),
(531, '488-ZZ', 488, 'ZZ', 1, '488 - ZZ', '2025-05-20 15:21:48', 1),
(532, '489-GG', 489, 'GG', 1, '489 - GG', '2025-05-20 15:21:48', 1),
(533, '490-GO', 490, 'GO', 1, '490 - GO', '2025-05-20 15:21:48', 1),
(534, '492-UY', 492, 'UY', 1, '492 - UY', '2025-05-20 15:21:48', 1),
(535, '493-LB', 493, 'LB', 1, '493 - LB', '2025-05-20 15:21:48', 1),
(536, '494-LC', 494, 'LC', 1, '494 - LC', '2025-05-20 15:21:48', 1),
(537, '495-LD', 495, 'LD', 1, '495 - LD', '2025-05-20 15:21:48', 1),
(538, '495-LF', 495, 'LF', 1, '495 - LF', '2025-05-20 15:21:48', 1),
(539, '496-LG', 496, 'LG', 1, '496 - LG', '2025-05-20 15:21:48', 1),
(540, '496-VI', 496, 'VI', 1, '496 - VI', '2025-05-20 15:21:48', 1),
(541, '497-IU', 497, 'IU', 1, '497 - IU', '2025-05-20 15:21:48', 1),
(542, '498-IM', 498, 'IM', 1, '498 - IM', '2025-05-20 15:21:48', 1),
(543, '499-OL', 499, 'OL', 1, '499 - OL', '2025-05-20 15:21:48', 1),
(544, '500-LH', 500, 'LH', 1, '500 - LH', '2025-05-20 15:21:48', 1),
(545, '501-LJ', 501, 'LJ', 1, '501 - LJ', '2025-05-20 15:21:48', 1),
(546, '502-LM', 502, 'LM', 1, '502 - LM', '2025-05-20 15:21:48', 1),
(547, '503-LQ', 503, 'LQ', 1, '503 - LQ', '2025-05-20 15:21:48', 1),
(548, '505-LK', 505, 'LK', 1, '505 - LK', '2025-05-20 15:21:48', 1),
(549, '506-LR', 506, 'LR', 1, '506 - LR', '2025-05-20 15:21:48', 1),
(550, '508-XY', 508, 'XY', 1, '508 - XY', '2025-05-20 15:21:48', 1),
(551, '509-WP', 509, 'WP', 1, '509 - WP', '2025-05-20 15:21:48', 1),
(552, '510-VB', 510, 'VB', 1, '510 - VB', '2025-05-20 15:21:48', 1),
(553, '511-LN', 511, 'LN', 1, '511 - LN', '2025-05-20 15:21:48', 1),
(554, '512-LS', 512, 'LS', 1, '512 - LS', '2025-05-20 15:21:48', 1),
(555, '513-MC', 513, 'MC', 1, '513 - MC', '2025-05-20 15:21:48', 1),
(556, '514-OQ', 514, 'OQ', 1, '514 - OQ', '2025-05-20 15:21:48', 1),
(557, '515-NE', 515, 'NE', 1, '515 - NE', '2025-05-20 15:21:48', 1),
(558, '516-OO', 516, 'OO', 1, '516 - OO', '2025-05-20 15:21:48', 1),
(559, '517-LU', 517, 'LU', 1, '517 - LU', '2025-05-20 15:21:48', 1),
(560, '518-LT', 518, 'LT', 1, '518 - LT', '2025-05-20 15:21:48', 1),
(561, '519-LV', 519, 'LV', 1, '519 - LV', '2025-05-20 15:21:48', 1),
(562, '519-LW', 519, 'LW', 1, '519 - LW', '2025-05-20 15:21:48', 1),
(563, '520-IV', 520, 'IV', 1, '520 - IV', '2025-05-20 15:21:48', 1),
(564, '521-LY', 521, 'LY', 1, '521 - LY', '2025-05-20 15:21:48', 1),
(565, '522-LZ', 522, 'LZ', 1, '522 - LZ', '2025-05-20 15:21:48', 1),
(566, '523-MB', 523, 'MB', 1, '523 - MB', '2025-05-20 15:21:48', 1),
(567, '524-VP', 524, 'VP', 1, '524 - VP', '2025-05-20 15:21:48', 1),
(568, '524-VQ', 524, 'VQ', 1, '524 - VQ', '2025-05-20 15:21:48', 1),
(569, '525-MD', 525, 'MD', 1, '525 - MD', '2025-05-20 15:21:48', 1),
(570, '526-MF', 526, 'MF', 1, '526 - MF', '2025-05-20 15:21:48', 1),
(571, '527-MG', 527, 'MG', 1, '527 - MG', '2025-05-20 15:21:48', 1),
(572, '528-XW', 528, 'XW', 1, '528 - XW', '2025-05-20 15:21:48', 1),
(573, '529-MH', 529, 'MH', 1, '529 - MH', '2025-05-20 15:21:48', 1),
(574, '530-VU', 530, 'VU', 1, '530 - VU', '2025-05-20 15:21:48', 1),
(575, '531-MJ', 531, 'MJ', 1, '531 - MJ', '2025-05-20 15:21:48', 1),
(576, '532-MK', 532, 'MK', 1, '532 - MK', '2025-05-20 15:21:48', 1),
(577, '533-ON', 533, 'ON', 1, '533 - ON', '2025-05-20 15:21:48', 1),
(578, '534-ML', 534, 'ML', 1, '534 - ML', '2025-05-20 15:21:48', 1),
(579, '535-MM', 535, 'MM', 1, '535 - MM', '2025-05-20 15:21:48', 1),
(580, '536-MN', 536, 'MN', 1, '536 - MN', '2025-05-20 15:21:48', 1),
(581, '537-MO', 537, 'MO', 1, '537 - MO', '2025-05-20 15:21:48', 1),
(582, '539-MQ', 539, 'MQ', 1, '539 - MQ', '2025-05-20 15:21:48', 1),
(583, '541-MZ', 541, 'MZ', 1, '541 - MZ', '2025-05-20 15:21:48', 1),
(584, '542-NP', 542, 'NP', 1, '542 - NP', '2025-05-20 15:21:48', 1),
(585, '543-MR', 543, 'MR', 1, '543 - MR', '2025-05-20 15:21:48', 1),
(586, '545-MS', 545, 'MS', 1, '545 - MS', '2025-05-20 15:21:48', 1),
(587, '546-MT', 546, 'MT', 1, '546 - MT', '2025-05-20 15:21:48', 1),
(588, '547-MU', 547, 'MU', 1, '547 - MU', '2025-05-20 15:21:48', 1),
(589, '548-MV', 548, 'MV', 1, '548 - MV', '2025-05-20 15:21:48', 1),
(590, '549-MW', 549, 'MW', 1, '549 - MW', '2025-05-20 15:21:48', 1),
(591, '550-MX', 550, 'MX', 1, '550 - MX', '2025-05-20 15:21:48', 1),
(592, '551-MY', 551, 'MY', 1, '551 - MY', '2025-05-20 15:21:48', 1),
(593, '552-NA', 552, 'NA', 1, '552 - NA', '2025-05-20 15:21:48', 1),
(594, '553-NB', 553, 'NB', 1, '553 - NB', '2025-05-20 15:21:48', 1),
(595, '554-NC', 554, 'NC', 1, '554 - NC', '2025-05-20 15:21:48', 1),
(596, '556-ND', 556, 'ND', 1, '556 - ND', '2025-05-20 15:21:48', 1),
(597, '557-NF', 557, 'NF', 1, '557 - NF', '2025-05-20 15:21:48', 1),
(598, '558-NG', 558, 'NG', 1, '558 - NG', '2025-05-20 15:21:48', 1),
(599, '559-NH', 559, 'NH', 1, '559 - NH', '2025-05-20 15:21:48', 1),
(600, '560-NI', 560, 'NI', 1, '560 - NI', '2025-05-20 15:21:48', 1),
(601, '561-NJ', 561, 'NJ', 1, '561 - NJ', '2025-05-20 15:21:48', 1),
(602, '562-NK', 562, 'NK', 1, '562 - NK', '2025-05-20 15:21:48', 1),
(603, '563-NL', 563, 'NL', 1, '563 - NL', '2025-05-20 15:21:48', 1),
(604, '564-NM', 564, 'NM', 1, '564 - NM', '2025-05-20 15:21:48', 1),
(605, '565-NN', 565, 'NN', 1, '565 - NN', '2025-05-20 15:21:48', 1),
(606, '566-NO', 566, 'NO', 1, '566 - NO', '2025-05-20 15:21:48', 1),
(607, '567-NQ', 567, 'NQ', 1, '567 - NQ', '2025-05-20 15:21:48', 1),
(608, '568-NR', 568, 'NR', 1, '568 - NR', '2025-05-20 15:21:48', 1),
(609, '569-NT', 569, 'NT', 1, '569 - NT', '2025-05-20 15:21:48', 1),
(610, '570-NU', 570, 'NU', 1, '570 - NU', '2025-05-20 15:21:48', 1),
(611, '571-NW', 571, 'NW', 1, '571 - NW', '2025-05-20 15:21:48', 1),
(612, '572-NX', 572, 'NX', 1, '572 - NX', '2025-05-20 15:21:48', 1),
(613, '573-NY', 573, 'NY', 1, '573 - NY', '2025-05-20 15:21:48', 1),
(614, '574-OK', 574, 'OK', 1, '574 - OK', '2025-05-20 15:21:48', 1),
(615, '575-OM', 575, 'OM', 1, '575 - OM', '2025-05-20 15:21:48', 1),
(616, '576-OU', 576, 'OU', 1, '576 - OU', '2025-05-20 15:21:48', 1),
(617, '577-OX', 577, 'OX', 1, '577 - OX', '2025-05-20 15:21:48', 1),
(618, '578-OZ', 578, 'OZ', 1, '578 - OZ', '2025-05-20 15:21:48', 1),
(619, '579-QQ', 579, 'QQ', 1, '579 - QQ', '2025-05-20 15:21:48', 1),
(620, '580-QR', 580, 'QR', 1, '580 - QR', '2025-05-20 15:21:48', 1),
(621, '581-QU', 581, 'QU', 1, '581 - QU', '2025-05-20 15:21:48', 1),
(622, '582-QW', 582, 'QW', 1, '582 - QW', '2025-05-20 15:21:48', 1),
(623, '583-QY', 583, 'QY', 1, '583 - QY', '2025-05-20 15:21:48', 1),
(624, '584-RW', 584, 'RW', 1, '584 - RW', '2025-05-20 15:21:48', 1),
(625, '584-XB', 584, 'XB', 1, '584 - XB', '2025-05-20 15:21:48', 1),
(626, '585-6R', 585, '6R', 1, '585 - 6R', '2025-05-20 15:21:48', 1),
(627, '586-RY', 586, 'RY', 1, '586 - RY', '2025-05-20 15:21:48', 1),
(628, '587-RX', 587, 'RX', 1, '587 - RX', '2025-05-20 15:21:48', 1),
(629, '589-VE', 589, 'VE', 1, '589 - VE', '2025-05-20 15:21:48', 1),
(630, '590-VF', 590, 'VF', 1, '590 - VF', '2025-05-20 15:21:48', 1),
(631, '591-VH', 591, 'VH', 1, '591 - VH', '2025-05-20 15:21:48', 1),
(632, '592-VJ', 592, 'VJ', 1, '592 - VJ', '2025-05-20 15:21:48', 1),
(633, '593-VK', 593, 'VK', 1, '593 - VK', '2025-05-20 15:21:48', 1),
(634, '594-VM', 594, 'VM', 1, '594 - VM', '2025-05-20 15:21:48', 1),
(635, '595-VN', 595, 'VN', 1, '595 - VN', '2025-05-20 15:21:48', 1),
(636, '596-VO', 596, 'VO', 1, '596 - VO', '2025-05-20 15:21:48', 1),
(637, '597-VT', 597, 'VT', 1, '597 - VT', '2025-05-20 15:21:48', 1),
(638, '598-VW', 598, 'VW', 1, '598 - VW', '2025-05-20 15:21:48', 1),
(639, '599-VX', 599, 'VX', 1, '599 - VX', '2025-05-20 15:21:48', 1),
(640, '600-VY', 600, 'VY', 1, '600 - VY', '2025-05-20 15:21:48', 1),
(641, '601-VZ', 601, 'VZ', 1, '601 - VZ', '2025-05-20 15:21:48', 1),
(642, '602-WD', 602, 'WD', 1, '602 - WD', '2025-05-20 15:21:48', 1),
(643, '603-WE', 603, 'WE', 1, '603 - WE', '2025-05-20 15:21:48', 1),
(644, '604-WF', 604, 'WF', 1, '604 - WF', '2025-05-20 15:21:48', 1),
(645, '605-WG', 605, 'WG', 1, '605 - WG', '2025-05-20 15:21:48', 1),
(646, '606-WJ', 606, 'WJ', 1, '606 - WJ', '2025-05-20 15:21:48', 1),
(647, '607-WK', 607, 'WK', 1, '607 - WK', '2025-05-20 15:21:48', 1),
(648, '608-WL', 608, 'WL', 1, '608 - WL', '2025-05-20 15:21:48', 1),
(649, '609-WN', 609, 'WN', 1, '609 - WN', '2025-05-20 15:21:48', 1),
(650, '610-WO', 610, 'WO', 1, '610 - WO', '2025-05-20 15:21:48', 1),
(651, '611-WQ', 611, 'WQ', 1, '611 - WQ', '2025-05-20 15:21:48', 1),
(652, '612-WR', 612, 'WR', 1, '612 - WR', '2025-05-20 15:21:48', 1),
(653, '613-WT', 613, 'WT', 1, '613 - WT', '2025-05-20 15:21:48', 1),
(654, '614-WV', 614, 'WV', 1, '614 - WV', '2025-05-20 15:21:48', 1),
(655, '615-WW', 615, 'WW', 1, '615 - WW', '2025-05-20 15:21:48', 1),
(656, '616-WX', 616, 'WX', 1, '616 - WX', '2025-05-20 15:21:48', 1),
(657, '617-WY', 617, 'WY', 1, '617 - WY', '2025-05-20 15:21:48', 1),
(658, '618-XA', 618, 'XA', 1, '618 - XA', '2025-05-20 15:21:48', 1),
(659, '620-XC', 620, 'XC', 1, '620 - XC', '2025-05-20 15:21:48', 1),
(660, '621-XE', 621, 'XE', 1, '621 - XE', '2025-05-20 15:21:48', 1),
(661, '622-XF', 622, 'XF', 1, '622 - XF', '2025-05-20 15:21:48', 1),
(662, '623-XG', 623, 'XG', 1, '623 - XG', '2025-05-20 15:21:48', 1),
(663, '624-XH', 624, 'XH', 1, '624 - XH', '2025-05-20 15:21:48', 1),
(664, '625-XI', 625, 'XI', 1, '625 - XI', '2025-05-20 15:21:48', 1),
(665, '627-4S', 627, '4S', 1, '627 - 4S', '2025-05-20 15:21:48', 1),
(666, '628-XJ', 628, 'XJ', 1, '628 - XJ', '2025-05-20 15:21:48', 1),
(667, '629-XK', 629, 'XK', 1, '629 - XK', '2025-05-20 15:21:48', 1)
;
COMMIT;

-- Import complete: 2025-05-20T19:21:48.920Z
-- Total records processed: 667
-- Duration: 1.02 seconds

-- Reset and update sequence to next available value
BEGIN;
ALTER SEQUENCE form_list_fdb_gen_df_mstr_link_id_seq RESTART WITH 668;
SET session_replication_role = DEFAULT;
COMMIT;
