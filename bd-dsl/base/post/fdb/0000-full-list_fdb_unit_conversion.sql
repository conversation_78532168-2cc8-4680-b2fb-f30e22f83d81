-- SQL Generation started: 2025-05-20T19:22:17.725Z
-- Table: list_fdb_unit_conversion
-- Total records: 1033

-- Initial configuration
BEGIN;
SET session_replication_role = replica;
SET maintenance_work_mem = '1GB';
SET synchronous_commit = off;
SET work_mem = '2GB';
SET temp_buffers = '1GB';
SET statement_timeout = 0;

-- Clean table
TRUNCATE TABLE form_list_fdb_unit_conversion;
COMMIT;

-- Beginning data import in batches of 50000 records
-- Each batch will have its own transaction
BEGIN;
INSERT INTO form_list_fdb_unit_conversion (id, code, from_uom_mstr_id, to_uom_mstr_id, uom_conversion_factor, auto_name, created_on, created_by) VALUES
(1, '-', 112, 188, 1, '112 -> 188', '2025-05-20 15:22:17', 1),
(2, '-', 131, 376, 1, '131 -> 376', '2025-05-20 15:22:17', 1),
(3, '-', 131, 482, 1, '131 -> 482', '2025-05-20 15:22:17', 1),
(4, '-', 132, 376, 1, '132 -> 376', '2025-05-20 15:22:17', 1),
(5, '-', 132, 482, 1, '132 -> 482', '2025-05-20 15:22:17', 1),
(6, '-', 133, 135, 0.142857, '133 -> 135', '2025-05-20 15:22:17', 1),
(7, '-', 133, 141, 0.033333, '133 -> 141', '2025-05-20 15:22:17', 1),
(8, '-', 133, 143, 0.00274, '133 -> 143', '2025-05-20 15:22:17', 1),
(9, '-', 133, 405, 1440, '133 -> 405', '2025-05-20 15:22:17', 1),
(10, '-', 133, 410, 24, '133 -> 410', '2025-05-20 15:22:17', 1),
(11, '-', 133, 412, 86400, '133 -> 412', '2025-05-20 15:22:17', 1),
(12, '-', 135, 133, 7, '135 -> 133', '2025-05-20 15:22:17', 1),
(13, '-', 135, 405, 10080, '135 -> 405', '2025-05-20 15:22:17', 1),
(14, '-', 135, 410, 168, '135 -> 410', '2025-05-20 15:22:17', 1),
(15, '-', 135, 412, 604800, '135 -> 412', '2025-05-20 15:22:17', 1),
(16, '-', 138, 380, 5, '138 -> 380', '2025-05-20 15:22:17', 1),
(17, '-', 138, 926, 1, '138 -> 926', '2025-05-20 15:22:17', 1),
(18, '-', 140, 378, 1, '140 -> 378', '2025-05-20 15:22:17', 1),
(19, '-', 140, 482, 1, '140 -> 482', '2025-05-20 15:22:17', 1),
(20, '-', 140, 928, 1, '140 -> 928', '2025-05-20 15:22:17', 1),
(21, '-', 141, 133, 30, '141 -> 133', '2025-05-20 15:22:17', 1),
(22, '-', 141, 405, 43200, '141 -> 405', '2025-05-20 15:22:17', 1),
(23, '-', 141, 410, 720, '141 -> 410', '2025-05-20 15:22:17', 1),
(24, '-', 143, 133, 365, '143 -> 133', '2025-05-20 15:22:17', 1),
(25, '-', 143, 135, 52, '143 -> 135', '2025-05-20 15:22:17', 1),
(26, '-', 143, 141, 12, '143 -> 141', '2025-05-20 15:22:17', 1),
(27, '-', 143, 410, 8760, '143 -> 410', '2025-05-20 15:22:17', 1),
(28, '-', 144, 380, 15, '144 -> 380', '2025-05-20 15:22:17', 1),
(29, '-', 144, 927, 1, '144 -> 927', '2025-05-20 15:22:17', 1),
(30, '-', 148, 376, 1, '148 -> 376', '2025-05-20 15:22:17', 1),
(31, '-', 148, 482, 1, '148 -> 482', '2025-05-20 15:22:17', 1),
(32, '-', 149, 482, 1, '149 -> 482', '2025-05-20 15:22:17', 1),
(33, '-', 153, 482, 1, '153 -> 482', '2025-05-20 15:22:17', 1),
(34, '-', 154, 482, 1, '154 -> 482', '2025-05-20 15:22:17', 1),
(35, '-', 155, 482, 1, '155 -> 482', '2025-05-20 15:22:17', 1),
(36, '-', 156, 482, 1, '156 -> 482', '2025-05-20 15:22:17', 1),
(37, '-', 157, 482, 1, '157 -> 482', '2025-05-20 15:22:17', 1),
(38, '-', 160, 482, 1, '160 -> 482', '2025-05-20 15:22:17', 1),
(39, '-', 163, 168, 0.454545, '163 -> 168', '2025-05-20 15:22:17', 1),
(40, '-', 163, 367, 454540, '163 -> 367', '2025-05-20 15:22:17', 1),
(41, '-', 163, 368, 454.54, '163 -> 368', '2025-05-20 15:22:17', 1),
(42, '-', 164, 165, 1547.6356, '164 -> 165', '2025-05-20 15:22:17', 1),
(43, '-', 164, 186, 10000, '164 -> 186', '2025-05-20 15:22:17', 1),
(44, '-', 164, 831, 0.578035, '164 -> 831', '2025-05-20 15:22:17', 1),
(45, '-', 165, 164, 0.000646, '165 -> 164', '2025-05-20 15:22:17', 1),
(46, '-', 167, 466, 1000, '167 -> 466', '2025-05-20 15:22:17', 1),
(47, '-', 168, 367, 1000000, '168 -> 367', '2025-05-20 15:22:17', 1),
(48, '-', 168, 368, 1000, '168 -> 368', '2025-05-20 15:22:17', 1),
(49, '-', 169, 428, 0.001, '169 -> 428', '2025-05-20 15:22:17', 1),
(50, '-', 169, 459, 0.000001, '169 -> 459', '2025-05-20 15:22:17', 1),
(51, '-', 170, 380, 1000, '170 -> 380', '2025-05-20 15:22:17', 1),
(52, '-', 173, 471, 1000, '173 -> 471', '2025-05-20 15:22:17', 1),
(53, '-', 175, 476, 0.000001, '175 -> 476', '2025-05-20 15:22:17', 1),
(54, '-', 175, 478, 0.024, '175 -> 478', '2025-05-20 15:22:17', 1),
(55, '-', 180, 482, 1, '180 -> 482', '2025-05-20 15:22:17', 1),
(56, '-', 185, 482, 1, '185 -> 482', '2025-05-20 15:22:17', 1),
(57, '-', 186, 164, 0.0001, '186 -> 164', '2025-05-20 15:22:17', 1),
(58, '-', 188, 112, 1, '188 -> 112', '2025-05-20 15:22:17', 1),
(59, '-', 188, 440, 1, '188 -> 440', '2025-05-20 15:22:17', 1),
(60, '-', 191, 350, 1000, '191 -> 350', '2025-05-20 15:22:17', 1),
(61, '-', 191, 751, 0.1, '191 -> 751', '2025-05-20 15:22:17', 1),
(62, '-', 191, 753, 0.1, '191 -> 753', '2025-05-20 15:22:17', 1),
(63, '-', 191, 754, 100, '191 -> 754', '2025-05-20 15:22:17', 1),
(64, '-', 192, 482, 1, '192 -> 482', '2025-05-20 15:22:17', 1),
(65, '-', 192, 683, 1, '192 -> 683', '2025-05-20 15:22:17', 1),
(66, '-', 195, 356, 60, '195 -> 356', '2025-05-20 15:22:17', 1),
(67, '-', 195, 361, 1440, '195 -> 361', '2025-05-20 15:22:17', 1),
(68, '-', 195, 778, 1.440092, '195 -> 778', '2025-05-20 15:22:17', 1),
(69, '-', 195, 779, 0.06, '195 -> 779', '2025-05-20 15:22:17', 1),
(70, '-', 195, 853, 0.001, '195 -> 853', '2025-05-20 15:22:17', 1),
(71, '-', 199, 464, 24, '199 -> 464', '2025-05-20 15:22:17', 1),
(72, '-', 199, 470, 0.024, '199 -> 470', '2025-05-20 15:22:17', 1),
(73, '-', 199, 471, 0.001, '199 -> 471', '2025-05-20 15:22:17', 1),
(74, '-', 200, 374, 1, '200 -> 374', '2025-05-20 15:22:17', 1),
(75, '-', 200, 482, 1, '200 -> 482', '2025-05-20 15:22:17', 1),
(76, '-', 201, 769, 1000, '201 -> 769', '2025-05-20 15:22:17', 1),
(77, '-', 201, 804, 0.001, '201 -> 804', '2025-05-20 15:22:17', 1),
(78, '-', 201, 813, 1000000, '201 -> 813', '2025-05-20 15:22:17', 1),
(79, '-', 341, 346, 1.44, '341 -> 346', '2025-05-20 15:22:17', 1),
(80, '-', 341, 347, 0.001, '341 -> 347', '2025-05-20 15:22:17', 1),
(81, '-', 341, 355, 0.00006, '341 -> 355', '2025-05-20 15:22:17', 1),
(82, '-', 341, 358, 0.00144, '341 -> 358', '2025-05-20 15:22:17', 1),
(83, '-', 341, 404, 0.000001, '341 -> 404', '2025-05-20 15:22:17', 1),
(84, '-', 341, 413, 0.06, '341 -> 413', '2025-05-20 15:22:17', 1),
(85, '-', 342, 483, 1, '342 -> 483', '2025-05-20 15:22:17', 1),
(86, '-', 343, 482, 1, '343 -> 482', '2025-05-20 15:22:17', 1),
(87, '-', 344, 361, 0.05, '344 -> 361', '2025-05-20 15:22:17', 1),
(88, '-', 345, 364, 0.000694, '345 -> 364', '2025-05-20 15:22:17', 1),
(89, '-', 345, 393, 0.001, '345 -> 393', '2025-05-20 15:22:17', 1),
(90, '-', 345, 396, 0.000042, '345 -> 396', '2025-05-20 15:22:17', 1),
(91, '-', 345, 397, 0.000001, '345 -> 397', '2025-05-20 15:22:17', 1),
(92, '-', 345, 398, 0.000001, '345 -> 398', '2025-05-20 15:22:17', 1),
(93, '-', 345, 408, 41.666667, '345 -> 408', '2025-05-20 15:22:17', 1),
(94, '-', 345, 409, 0.041667, '345 -> 409', '2025-05-20 15:22:17', 1),
(95, '-', 345, 415, 0.694444, '345 -> 415', '2025-05-20 15:22:17', 1),
(96, '-', 345, 416, 1, '345 -> 416', '2025-05-20 15:22:17', 1),
(97, '-', 346, 341, 0.694444, '346 -> 341', '2025-05-20 15:22:17', 1),
(98, '-', 346, 347, 0.000694, '346 -> 347', '2025-05-20 15:22:17', 1),
(99, '-', 346, 355, 0.000042, '346 -> 355', '2025-05-20 15:22:17', 1),
(100, '-', 346, 358, 0.001, '346 -> 358', '2025-05-20 15:22:17', 1),
(101, '-', 346, 390, 0.000001, '346 -> 390', '2025-05-20 15:22:17', 1),
(102, '-', 346, 404, 0.000001, '346 -> 404', '2025-05-20 15:22:17', 1),
(103, '-', 346, 413, 0.041667, '346 -> 413', '2025-05-20 15:22:17', 1),
(104, '-', 347, 341, 1000, '347 -> 341', '2025-05-20 15:22:17', 1),
(105, '-', 347, 346, 1440, '347 -> 346', '2025-05-20 15:22:17', 1),
(106, '-', 347, 355, 0.06, '347 -> 355', '2025-05-20 15:22:17', 1),
(107, '-', 347, 358, 1.44, '347 -> 358', '2025-05-20 15:22:17', 1),
(108, '-', 347, 390, 0.00144, '347 -> 390', '2025-05-20 15:22:17', 1),
(109, '-', 347, 404, 0.001, '347 -> 404', '2025-05-20 15:22:17', 1),
(110, '-', 347, 413, 60, '347 -> 413', '2025-05-20 15:22:17', 1),
(111, '-', 348, 385, 24, '348 -> 385', '2025-05-20 15:22:17', 1),
(112, '-', 350, 191, 0.001, '350 -> 191', '2025-05-20 15:22:17', 1),
(113, '-', 350, 751, 0.0001, '350 -> 751', '2025-05-20 15:22:17', 1),
(114, '-', 350, 753, 0.0001, '350 -> 753', '2025-05-20 15:22:17', 1),
(115, '-', 350, 754, 0.1, '350 -> 754', '2025-05-20 15:22:17', 1),
(116, '-', 350, 852, 0.001, '350 -> 852', '2025-05-20 15:22:17', 1),
(117, '-', 351, 381, 1, '351 -> 381', '2025-05-20 15:22:17', 1),
(118, '-', 351, 483, 1, '351 -> 483', '2025-05-20 15:22:17', 1),
(119, '-', 351, 895, 1, '351 -> 895', '2025-05-20 15:22:17', 1),
(120, '-', 351, 897, 1, '351 -> 897', '2025-05-20 15:22:17', 1),
(121, '-', 352, 349, 2.54, '352 -> 349', '2025-05-20 15:22:17', 1),
(122, '-', 353, 483, 1, '353 -> 483', '2025-05-20 15:22:17', 1),
(123, '-', 354, 483, 1, '354 -> 483', '2025-05-20 15:22:17', 1),
(124, '-', 355, 341, 16666.666667, '355 -> 341', '2025-05-20 15:22:17', 1),
(125, '-', 355, 346, 24000, '355 -> 346', '2025-05-20 15:22:17', 1),
(126, '-', 355, 347, 16.666667, '355 -> 347', '2025-05-20 15:22:17', 1),
(127, '-', 355, 358, 24, '355 -> 358', '2025-05-20 15:22:17', 1),
(128, '-', 355, 390, 0.024, '355 -> 390', '2025-05-20 15:22:17', 1),
(129, '-', 355, 404, 0.016667, '355 -> 404', '2025-05-20 15:22:17', 1),
(130, '-', 355, 413, 1000, '355 -> 413', '2025-05-20 15:22:17', 1),
(131, '-', 355, 490, 0.001, '355 -> 490', '2025-05-20 15:22:17', 1),
(132, '-', 356, 195, 0.016667, '356 -> 195', '2025-05-20 15:22:17', 1),
(133, '-', 356, 361, 24, '356 -> 361', '2025-05-20 15:22:17', 1),
(134, '-', 356, 778, 0.024038, '356 -> 778', '2025-05-20 15:22:17', 1),
(135, '-', 356, 779, 0.001, '356 -> 779', '2025-05-20 15:22:17', 1),
(136, '-', 356, 853, 0.000017, '356 -> 853', '2025-05-20 15:22:17', 1),
(137, '-', 357, 371, 0.001, '357 -> 371', '2025-05-20 15:22:17', 1),
(138, '-', 357, 388, 0.000001, '357 -> 388', '2025-05-20 15:22:17', 1),
(139, '-', 357, 417, 0.001, '357 -> 417', '2025-05-20 15:22:17', 1),
(140, '-', 357, 420, 1000, '357 -> 420', '2025-05-20 15:22:17', 1),
(141, '-', 358, 341, 694.444444, '358 -> 341', '2025-05-20 15:22:17', 1),
(142, '-', 358, 346, 1000, '358 -> 346', '2025-05-20 15:22:17', 1),
(143, '-', 358, 347, 0.694444, '358 -> 347', '2025-05-20 15:22:17', 1),
(144, '-', 358, 355, 0.041667, '358 -> 355', '2025-05-20 15:22:17', 1),
(145, '-', 358, 390, 0.001, '358 -> 390', '2025-05-20 15:22:17', 1),
(146, '-', 358, 404, 0.000694, '358 -> 404', '2025-05-20 15:22:17', 1),
(147, '-', 358, 413, 41.666667, '358 -> 413', '2025-05-20 15:22:17', 1),
(148, '-', 358, 490, 0.000042, '358 -> 490', '2025-05-20 15:22:17', 1),
(149, '-', 359, 360, 1, '359 -> 360', '2025-05-20 15:22:17', 1),
(150, '-', 359, 483, 1, '359 -> 483', '2025-05-20 15:22:17', 1),
(151, '-', 359, 946, 1, '359 -> 946', '2025-05-20 15:22:17', 1),
(152, '-', 359, 947, 1, '359 -> 947', '2025-05-20 15:22:17', 1),
(153, '-', 359, 964, 1, '359 -> 964', '2025-05-20 15:22:17', 1),
(154, '-', 360, 359, 1, '360 -> 359', '2025-05-20 15:22:17', 1),
(155, '-', 360, 483, 1, '360 -> 483', '2025-05-20 15:22:17', 1),
(156, '-', 360, 947, 1, '360 -> 947', '2025-05-20 15:22:17', 1),
(157, '-', 360, 964, 1, '360 -> 964', '2025-05-20 15:22:17', 1),
(158, '-', 361, 195, 0.000694, '361 -> 195', '2025-05-20 15:22:17', 1),
(159, '-', 361, 344, 20, '361 -> 344', '2025-05-20 15:22:17', 1),
(160, '-', 361, 356, 0.041667, '361 -> 356', '2025-05-20 15:22:17', 1),
(161, '-', 361, 778, 0.001, '361 -> 778', '2025-05-20 15:22:17', 1),
(162, '-', 361, 779, 0.000042, '361 -> 779', '2025-05-20 15:22:17', 1),
(163, '-', 361, 853, 0.000001, '361 -> 853', '2025-05-20 15:22:17', 1),
(164, '-', 361, 966, 0.066667, '361 -> 966', '2025-05-20 15:22:17', 1),
(165, '-', 361, 967, 0.066667, '361 -> 967', '2025-05-20 15:22:17', 1),
(166, '-', 361, 983, 0.2, '361 -> 983', '2025-05-20 15:22:17', 1),
(167, '-', 362, 345, 24000000, '362 -> 345', '2025-05-20 15:22:17', 1),
(168, '-', 362, 364, 16666.666667, '362 -> 364', '2025-05-20 15:22:17', 1),
(169, '-', 362, 393, 24000, '362 -> 393', '2025-05-20 15:22:17', 1),
(170, '-', 362, 396, 1000, '362 -> 396', '2025-05-20 15:22:17', 1),
(171, '-', 362, 397, 24, '362 -> 397', '2025-05-20 15:22:17', 1),
(172, '-', 362, 398, 16.666667, '362 -> 398', '2025-05-20 15:22:17', 1),
(173, '-', 362, 403, 0.016667, '362 -> 403', '2025-05-20 15:22:17', 1),
(174, '-', 362, 409, 1000000, '362 -> 409', '2025-05-20 15:22:17', 1),
(175, '-', 364, 345, 1440, '364 -> 345', '2025-05-20 15:22:17', 1),
(176, '-', 364, 362, 0.00006, '364 -> 362', '2025-05-20 15:22:17', 1),
(177, '-', 364, 393, 1.44, '364 -> 393', '2025-05-20 15:22:17', 1),
(178, '-', 364, 396, 0.06, '364 -> 396', '2025-05-20 15:22:17', 1),
(179, '-', 364, 397, 0.00144, '364 -> 397', '2025-05-20 15:22:17', 1),
(180, '-', 364, 398, 0.001, '364 -> 398', '2025-05-20 15:22:17', 1),
(181, '-', 364, 403, 0.000001, '364 -> 403', '2025-05-20 15:22:17', 1),
(182, '-', 364, 408, 60000, '364 -> 408', '2025-05-20 15:22:17', 1),
(183, '-', 364, 409, 60, '364 -> 409', '2025-05-20 15:22:17', 1),
(184, '-', 364, 415, 1000, '364 -> 415', '2025-05-20 15:22:17', 1),
(185, '-', 365, 483, 1, '365 -> 483', '2025-05-20 15:22:17', 1),
(186, '-', 366, 482, 1, '366 -> 482', '2025-05-20 15:22:17', 1),
(187, '-', 366, 683, 1, '366 -> 683', '2025-05-20 15:22:17', 1),
(188, '-', 367, 168, 0.000001, '367 -> 168', '2025-05-20 15:22:17', 1),
(189, '-', 367, 368, 0.001, '367 -> 368', '2025-05-20 15:22:17', 1),
(190, '-', 367, 372, 1000, '367 -> 372', '2025-05-20 15:22:17', 1),
(191, '-', 367, 424, 1000000, '367 -> 424', '2025-05-20 15:22:17', 1),
(192, '-', 368, 168, 0.001, '368 -> 168', '2025-05-20 15:22:17', 1),
(193, '-', 368, 367, 1000, '368 -> 367', '2025-05-20 15:22:17', 1),
(194, '-', 368, 372, 1000000, '368 -> 372', '2025-05-20 15:22:17', 1),
(195, '-', 369, 748, 1000, '369 -> 748', '2025-05-20 15:22:17', 1),
(196, '-', 369, 809, 0.001, '369 -> 809', '2025-05-20 15:22:17', 1),
(197, '-', 370, 380, 0.05, '370 -> 380', '2025-05-20 15:22:17', 1),
(198, '-', 371, 357, 1000, '371 -> 357', '2025-05-20 15:22:17', 1),
(199, '-', 371, 388, 0.001, '371 -> 388', '2025-05-20 15:22:17', 1),
(200, '-', 371, 400, 0.000001, '371 -> 400', '2025-05-20 15:22:17', 1),
(201, '-', 371, 417, 1, '371 -> 417', '2025-05-20 15:22:17', 1),
(202, '-', 371, 420, 1000000, '371 -> 420', '2025-05-20 15:22:17', 1),
(203, '-', 372, 367, 0.001, '372 -> 367', '2025-05-20 15:22:17', 1),
(204, '-', 372, 368, 0.000001, '372 -> 368', '2025-05-20 15:22:17', 1),
(205, '-', 372, 424, 1000, '372 -> 424', '2025-05-20 15:22:17', 1),
(206, '-', 373, 375, 0.393701, '373 -> 375', '2025-05-20 15:22:17', 1),
(207, '-', 373, 704, 0.01, '373 -> 704', '2025-05-20 15:22:17', 1),
(208, '-', 373, 802, 0.032808, '373 -> 802', '2025-05-20 15:22:17', 1),
(209, '-', 373, 816, 10, '373 -> 816', '2025-05-20 15:22:17', 1),
(210, '-', 373, 820, 0.010936, '373 -> 820', '2025-05-20 15:22:17', 1),
(211, '-', 374, 482, 1, '374 -> 482', '2025-05-20 15:22:17', 1),
(212, '-', 375, 373, 2.54, '375 -> 373', '2025-05-20 15:22:17', 1),
(213, '-', 375, 704, 0.0254, '375 -> 704', '2025-05-20 15:22:17', 1),
(214, '-', 375, 802, 0.083333, '375 -> 802', '2025-05-20 15:22:17', 1),
(215, '-', 375, 816, 25.4, '375 -> 816', '2025-05-20 15:22:17', 1),
(216, '-', 375, 820, 0.027777, '375 -> 820', '2025-05-20 15:22:17', 1),
(217, '-', 376, 131, 1, '376 -> 131', '2025-05-20 15:22:17', 1),
(218, '-', 376, 132, 1, '376 -> 132', '2025-05-20 15:22:17', 1),
(219, '-', 376, 148, 1, '376 -> 148', '2025-05-20 15:22:17', 1),
(220, '-', 376, 482, 1, '376 -> 482', '2025-05-20 15:22:17', 1),
(221, '-', 377, 482, 1, '377 -> 482', '2025-05-20 15:22:17', 1),
(222, '-', 378, 140, 1, '378 -> 140', '2025-05-20 15:22:17', 1),
(223, '-', 378, 407, 1, '378 -> 407', '2025-05-20 15:22:17', 1),
(224, '-', 378, 482, 1, '378 -> 482', '2025-05-20 15:22:17', 1),
(225, '-', 378, 928, 1, '378 -> 928', '2025-05-20 15:22:17', 1),
(226, '-', 378, 929, 1, '378 -> 929', '2025-05-20 15:22:17', 1),
(227, '-', 379, 407, 1, '379 -> 407', '2025-05-20 15:22:17', 1),
(228, '-', 379, 482, 1, '379 -> 482', '2025-05-20 15:22:17', 1),
(229, '-', 380, 138, 0.2, '380 -> 138', '2025-05-20 15:22:17', 1),
(230, '-', 380, 144, 0.066667, '380 -> 144', '2025-05-20 15:22:17', 1),
(231, '-', 380, 170, 0.001, '380 -> 170', '2025-05-20 15:22:17', 1),
(232, '-', 380, 370, 20, '380 -> 370', '2025-05-20 15:22:17', 1),
(233, '-', 380, 745, 1000, '380 -> 745', '2025-05-20 15:22:17', 1),
(234, '-', 381, 351, 1, '381 -> 351', '2025-05-20 15:22:17', 1),
(235, '-', 381, 374, 1, '381 -> 374', '2025-05-20 15:22:17', 1),
(236, '-', 381, 483, 1, '381 -> 483', '2025-05-20 15:22:17', 1),
(237, '-', 381, 952, 1, '381 -> 952', '2025-05-20 15:22:17', 1),
(238, '-', 382, 483, 1, '382 -> 483', '2025-05-20 15:22:17', 1),
(239, '-', 383, 523, 0.155, '383 -> 523', '2025-05-20 15:22:17', 1),
(240, '-', 385, 348, 0.041667, '385 -> 348', '2025-05-20 15:22:17', 1),
(241, '-', 386, 482, 1, '386 -> 482', '2025-05-20 15:22:17', 1),
(242, '-', 387, 401, 24, '387 -> 401', '2025-05-20 15:22:17', 1),
(243, '-', 388, 357, 1000000, '388 -> 357', '2025-05-20 15:22:17', 1),
(244, '-', 388, 371, 1000, '388 -> 371', '2025-05-20 15:22:17', 1),
(245, '-', 388, 400, 0.001, '388 -> 400', '2025-05-20 15:22:17', 1),
(246, '-', 388, 417, 1000, '388 -> 417', '2025-05-20 15:22:17', 1),
(247, '-', 389, 374, 1, '389 -> 374', '2025-05-20 15:22:17', 1),
(248, '-', 389, 482, 1, '389 -> 482', '2025-05-20 15:22:17', 1),
(249, '-', 389, 930, 1, '389 -> 930', '2025-05-20 15:22:17', 1),
(250, '-', 390, 346, 1000000, '390 -> 346', '2025-05-20 15:22:17', 1),
(251, '-', 390, 347, 694.40002, '390 -> 347', '2025-05-20 15:22:17', 1),
(252, '-', 390, 355, 41.666667, '390 -> 355', '2025-05-20 15:22:17', 1),
(253, '-', 390, 358, 1000, '390 -> 358', '2025-05-20 15:22:17', 1),
(254, '-', 390, 404, 0.694444, '390 -> 404', '2025-05-20 15:22:17', 1),
(255, '-', 390, 413, 41666.666667, '390 -> 413', '2025-05-20 15:22:17', 1),
(256, '-', 391, 482, 1, '391 -> 482', '2025-05-20 15:22:17', 1),
(257, '-', 392, 483, 1, '392 -> 483', '2025-05-20 15:22:17', 1),
(258, '-', 393, 345, 1000, '393 -> 345', '2025-05-20 15:22:17', 1),
(259, '-', 393, 362, 0.000042, '393 -> 362', '2025-05-20 15:22:17', 1),
(260, '-', 393, 364, 0.694444, '393 -> 364', '2025-05-20 15:22:17', 1),
(261, '-', 393, 396, 0.041667, '393 -> 396', '2025-05-20 15:22:17', 1),
(262, '-', 393, 397, 0.001, '393 -> 397', '2025-05-20 15:22:17', 1),
(263, '-', 393, 398, 0.000694, '393 -> 398', '2025-05-20 15:22:17', 1),
(264, '-', 393, 403, 0.000001, '393 -> 403', '2025-05-20 15:22:17', 1),
(265, '-', 393, 408, 41666.666667, '393 -> 408', '2025-05-20 15:22:17', 1),
(266, '-', 393, 409, 41.666667, '393 -> 409', '2025-05-20 15:22:17', 1),
(267, '-', 393, 415, 694.444444, '393 -> 415', '2025-05-20 15:22:17', 1),
(268, '-', 393, 416, 1000, '393 -> 416', '2025-05-20 15:22:17', 1),
(269, '-', 394, 483, 1, '394 -> 483', '2025-05-20 15:22:17', 1),
(270, '-', 395, 483, 1, '395 -> 483', '2025-05-20 15:22:17', 1),
(271, '-', 396, 345, 24000, '396 -> 345', '2025-05-20 15:22:17', 1),
(272, '-', 396, 362, 0.001, '396 -> 362', '2025-05-20 15:22:17', 1),
(273, '-', 396, 364, 16.666667, '396 -> 364', '2025-05-20 15:22:17', 1),
(274, '-', 396, 393, 24, '396 -> 393', '2025-05-20 15:22:17', 1),
(275, '-', 396, 397, 0.024, '396 -> 397', '2025-05-20 15:22:17', 1),
(276, '-', 396, 398, 0.016667, '396 -> 398', '2025-05-20 15:22:17', 1),
(277, '-', 396, 403, 0.000017, '396 -> 403', '2025-05-20 15:22:17', 1),
(278, '-', 396, 408, 1000000, '396 -> 408', '2025-05-20 15:22:17', 1),
(279, '-', 396, 409, 1000, '396 -> 409', '2025-05-20 15:22:17', 1),
(280, '-', 396, 415, 16666.666667, '396 -> 415', '2025-05-20 15:22:17', 1),
(281, '-', 397, 345, 1000000, '397 -> 345', '2025-05-20 15:22:17', 1),
(282, '-', 397, 362, 0.041667, '397 -> 362', '2025-05-20 15:22:17', 1),
(283, '-', 397, 364, 694.444444, '397 -> 364', '2025-05-20 15:22:17', 1),
(284, '-', 397, 393, 1000, '397 -> 393', '2025-05-20 15:22:17', 1),
(285, '-', 397, 396, 41.666667, '397 -> 396', '2025-05-20 15:22:17', 1),
(286, '-', 397, 398, 0.694444, '397 -> 398', '2025-05-20 15:22:17', 1),
(287, '-', 397, 403, 0.000694, '397 -> 403', '2025-05-20 15:22:17', 1),
(288, '-', 397, 408, 41666667, '397 -> 408', '2025-05-20 15:22:17', 1),
(289, '-', 397, 409, 41666.666667, '397 -> 409', '2025-05-20 15:22:17', 1),
(290, '-', 397, 416, 1000000, '397 -> 416', '2025-05-20 15:22:17', 1),
(291, '-', 398, 345, 1440000, '398 -> 345', '2025-05-20 15:22:17', 1),
(292, '-', 398, 362, 0.06, '398 -> 362', '2025-05-20 15:22:17', 1),
(293, '-', 398, 364, 1000, '398 -> 364', '2025-05-20 15:22:17', 1),
(294, '-', 398, 393, 1440, '398 -> 393', '2025-05-20 15:22:17', 1),
(295, '-', 398, 396, 60, '398 -> 396', '2025-05-20 15:22:17', 1),
(296, '-', 398, 397, 1.44, '398 -> 397', '2025-05-20 15:22:17', 1),
(297, '-', 398, 403, 0.001, '398 -> 403', '2025-05-20 15:22:17', 1),
(298, '-', 398, 408, 60000000, '398 -> 408', '2025-05-20 15:22:17', 1),
(299, '-', 398, 409, 60000, '398 -> 409', '2025-05-20 15:22:17', 1),
(300, '-', 398, 415, 1000000, '398 -> 415', '2025-05-20 15:22:17', 1),
(301, '-', 399, 482, 1, '399 -> 482', '2025-05-20 15:22:17', 1),
(302, '-', 400, 371, 1000000, '400 -> 371', '2025-05-20 15:22:17', 1),
(303, '-', 400, 388, 1000, '400 -> 388', '2025-05-20 15:22:17', 1),
(304, '-', 400, 417, 1000000, '400 -> 417', '2025-05-20 15:22:17', 1),
(305, '-', 401, 387, 0.041667, '401 -> 387', '2025-05-20 15:22:17', 1),
(306, '-', 402, 482, 1, '402 -> 482', '2025-05-20 15:22:17', 1),
(307, '-', 403, 345, 1440000000, '403 -> 345', '2025-05-20 15:22:17', 1),
(308, '-', 403, 362, 60, '403 -> 362', '2025-05-20 15:22:17', 1),
(309, '-', 403, 364, 1000000, '403 -> 364', '2025-05-20 15:22:17', 1),
(310, '-', 403, 393, 1440000, '403 -> 393', '2025-05-20 15:22:17', 1),
(311, '-', 403, 396, 60000, '403 -> 396', '2025-05-20 15:22:17', 1),
(312, '-', 403, 397, 1440, '403 -> 397', '2025-05-20 15:22:17', 1),
(313, '-', 403, 398, 1000, '403 -> 398', '2025-05-20 15:22:17', 1),
(314, '-', 403, 409, 60000000, '403 -> 409', '2025-05-20 15:22:17', 1),
(315, '-', 404, 341, 1000000, '404 -> 341', '2025-05-20 15:22:17', 1),
(316, '-', 404, 346, 1440000, '404 -> 346', '2025-05-20 15:22:17', 1),
(317, '-', 404, 347, 1000, '404 -> 347', '2025-05-20 15:22:17', 1),
(318, '-', 404, 355, 60, '404 -> 355', '2025-05-20 15:22:17', 1),
(319, '-', 404, 358, 1440, '404 -> 358', '2025-05-20 15:22:17', 1),
(320, '-', 404, 390, 1.44, '404 -> 390', '2025-05-20 15:22:17', 1),
(321, '-', 404, 413, 60000, '404 -> 413', '2025-05-20 15:22:17', 1),
(322, '-', 404, 490, 0.06, '404 -> 490', '2025-05-20 15:22:17', 1),
(323, '-', 405, 133, 0.000694, '405 -> 133', '2025-05-20 15:22:17', 1),
(324, '-', 405, 135, 0.000099, '405 -> 135', '2025-05-20 15:22:17', 1),
(325, '-', 405, 141, 0.000023, '405 -> 141', '2025-05-20 15:22:17', 1),
(326, '-', 405, 410, 0.016667, '405 -> 410', '2025-05-20 15:22:17', 1),
(327, '-', 405, 412, 60, '405 -> 412', '2025-05-20 15:22:17', 1),
(328, '-', 406, 814, 0.001, '406 -> 814', '2025-05-20 15:22:17', 1),
(329, '-', 407, 378, 1, '407 -> 378', '2025-05-20 15:22:17', 1),
(330, '-', 407, 379, 1, '407 -> 379', '2025-05-20 15:22:17', 1),
(331, '-', 407, 482, 1, '407 -> 482', '2025-05-20 15:22:17', 1),
(332, '-', 407, 928, 1, '407 -> 928', '2025-05-20 15:22:17', 1),
(333, '-', 407, 929, 1, '407 -> 929', '2025-05-20 15:22:17', 1),
(334, '-', 408, 345, 0.024, '408 -> 345', '2025-05-20 15:22:17', 1),
(335, '-', 408, 364, 0.000017, '408 -> 364', '2025-05-20 15:22:17', 1),
(336, '-', 408, 393, 0.000024, '408 -> 393', '2025-05-20 15:22:17', 1),
(337, '-', 408, 396, 0.000001, '408 -> 396', '2025-05-20 15:22:17', 1),
(338, '-', 408, 409, 0.001, '408 -> 409', '2025-05-20 15:22:17', 1),
(339, '-', 408, 415, 0.016667, '408 -> 415', '2025-05-20 15:22:17', 1),
(340, '-', 409, 362, 0.000001, '409 -> 362', '2025-05-20 15:22:17', 1),
(341, '-', 409, 364, 0.016667, '409 -> 364', '2025-05-20 15:22:17', 1),
(342, '-', 409, 393, 0.024, '409 -> 393', '2025-05-20 15:22:17', 1),
(343, '-', 409, 396, 0.001, '409 -> 396', '2025-05-20 15:22:17', 1),
(344, '-', 409, 397, 0.000024, '409 -> 397', '2025-05-20 15:22:17', 1),
(345, '-', 409, 398, 0.000017, '409 -> 398', '2025-05-20 15:22:17', 1),
(346, '-', 409, 408, 1000, '409 -> 408', '2025-05-20 15:22:17', 1),
(347, '-', 409, 415, 16.666667, '409 -> 415', '2025-05-20 15:22:17', 1),
(348, '-', 409, 416, 24, '409 -> 416', '2025-05-20 15:22:17', 1),
(349, '-', 410, 133, 0.041667, '410 -> 133', '2025-05-20 15:22:17', 1),
(350, '-', 410, 135, 0.005952, '410 -> 135', '2025-05-20 15:22:17', 1),
(351, '-', 410, 143, 0.000114, '410 -> 143', '2025-05-20 15:22:17', 1),
(352, '-', 410, 405, 60, '410 -> 405', '2025-05-20 15:22:17', 1),
(353, '-', 410, 412, 3600, '410 -> 412', '2025-05-20 15:22:17', 1),
(354, '-', 411, 483, 1, '411 -> 483', '2025-05-20 15:22:17', 1),
(355, '-', 411, 661, 1, '411 -> 661', '2025-05-20 15:22:17', 1),
(356, '-', 412, 133, 0.000012, '412 -> 133', '2025-05-20 15:22:17', 1),
(357, '-', 412, 135, 0.000002, '412 -> 135', '2025-05-20 15:22:17', 1),
(358, '-', 412, 405, 0.016667, '412 -> 405', '2025-05-20 15:22:17', 1),
(359, '-', 412, 410, 0.000278, '412 -> 410', '2025-05-20 15:22:17', 1),
(360, '-', 413, 341, 16.666667, '413 -> 341', '2025-05-20 15:22:17', 1),
(361, '-', 413, 346, 24, '413 -> 346', '2025-05-20 15:22:17', 1),
(362, '-', 413, 347, 0.016667, '413 -> 347', '2025-05-20 15:22:17', 1),
(363, '-', 413, 355, 0.001, '413 -> 355', '2025-05-20 15:22:17', 1),
(364, '-', 413, 358, 0.024, '413 -> 358', '2025-05-20 15:22:17', 1),
(365, '-', 413, 390, 0.000024, '413 -> 390', '2025-05-20 15:22:17', 1),
(366, '-', 413, 404, 0.000017, '413 -> 404', '2025-05-20 15:22:17', 1),
(367, '-', 414, 185, 1, '414 -> 185', '2025-05-20 15:22:17', 1),
(368, '-', 414, 482, 1, '414 -> 482', '2025-05-20 15:22:17', 1),
(369, '-', 415, 345, 1.44, '415 -> 345', '2025-05-20 15:22:17', 1),
(370, '-', 415, 364, 0.001, '415 -> 364', '2025-05-20 15:22:17', 1),
(371, '-', 415, 393, 0.00144, '415 -> 393', '2025-05-20 15:22:17', 1),
(372, '-', 415, 396, 0.00006, '415 -> 396', '2025-05-20 15:22:17', 1),
(373, '-', 415, 398, 0.000001, '415 -> 398', '2025-05-20 15:22:17', 1),
(374, '-', 415, 408, 60, '415 -> 408', '2025-05-20 15:22:17', 1),
(375, '-', 415, 409, 0.06, '415 -> 409', '2025-05-20 15:22:17', 1),
(376, '-', 416, 345, 1, '416 -> 345', '2025-05-20 15:22:17', 1),
(377, '-', 416, 393, 0.001, '416 -> 393', '2025-05-20 15:22:17', 1),
(378, '-', 416, 397, 0.000001, '416 -> 397', '2025-05-20 15:22:17', 1),
(379, '-', 417, 371, 1, '417 -> 371', '2025-05-20 15:22:17', 1),
(380, '-', 418, 483, 1, '418 -> 483', '2025-05-20 15:22:17', 1),
(381, '-', 419, 482, 1, '419 -> 482', '2025-05-20 15:22:17', 1),
(382, '-', 420, 357, 0.001, '420 -> 357', '2025-05-20 15:22:17', 1),
(383, '-', 420, 371, 0.000001, '420 -> 371', '2025-05-20 15:22:17', 1),
(384, '-', 421, 483, 1, '421 -> 483', '2025-05-20 15:22:17', 1),
(385, '-', 422, 482, 1, '422 -> 482', '2025-05-20 15:22:17', 1),
(386, '-', 423, 483, 1, '423 -> 483', '2025-05-20 15:22:17', 1),
(387, '-', 424, 367, 0.000001, '424 -> 367', '2025-05-20 15:22:17', 1),
(388, '-', 424, 372, 0.001, '424 -> 372', '2025-05-20 15:22:17', 1),
(389, '-', 425, 482, 1, '425 -> 482', '2025-05-20 15:22:17', 1),
(390, '-', 427, 429, 0.06, '427 -> 429', '2025-05-20 15:22:17', 1),
(391, '-', 427, 437, 0.00144, '427 -> 437', '2025-05-20 15:22:17', 1),
(392, '-', 427, 438, 1440, '427 -> 438', '2025-05-20 15:22:17', 1),
(393, '-', 427, 448, 1000, '427 -> 448', '2025-05-20 15:22:17', 1),
(394, '-', 427, 449, 1.44, '427 -> 449', '2025-05-20 15:22:17', 1),
(395, '-', 427, 454, 0.001, '427 -> 454', '2025-05-20 15:22:17', 1),
(396, '-', 427, 462, 60, '427 -> 462', '2025-05-20 15:22:17', 1),
(397, '-', 428, 169, 1000, '428 -> 169', '2025-05-20 15:22:17', 1),
(398, '-', 428, 445, 0.000001, '428 -> 445', '2025-05-20 15:22:17', 1),
(399, '-', 428, 459, 0.001, '428 -> 459', '2025-05-20 15:22:17', 1),
(400, '-', 429, 427, 16.666667, '429 -> 427', '2025-05-20 15:22:17', 1),
(401, '-', 429, 437, 0.024, '429 -> 437', '2025-05-20 15:22:17', 1),
(402, '-', 429, 438, 24000, '429 -> 438', '2025-05-20 15:22:17', 1),
(403, '-', 429, 448, 16666.666667, '429 -> 448', '2025-05-20 15:22:17', 1),
(404, '-', 429, 449, 24, '429 -> 449', '2025-05-20 15:22:17', 1),
(405, '-', 429, 454, 0.016667, '429 -> 454', '2025-05-20 15:22:17', 1),
(406, '-', 429, 462, 1000, '429 -> 462', '2025-05-20 15:22:17', 1),
(407, '-', 430, 435, 0.000001, '430 -> 435', '2025-05-20 15:22:17', 1),
(408, '-', 430, 443, 1000, '430 -> 443', '2025-05-20 15:22:17', 1),
(409, '-', 430, 451, 0.001, '430 -> 451', '2025-05-20 15:22:17', 1),
(410, '-', 431, 432, 0.000017, '431 -> 432', '2025-05-20 15:22:17', 1),
(411, '-', 431, 433, 0.024, '431 -> 433', '2025-05-20 15:22:17', 1),
(412, '-', 431, 434, 0.001, '431 -> 434', '2025-05-20 15:22:17', 1),
(413, '-', 431, 450, 0.000001, '431 -> 450', '2025-05-20 15:22:17', 1),
(414, '-', 431, 457, 0.000024, '431 -> 457', '2025-05-20 15:22:17', 1),
(415, '-', 432, 431, 60000, '432 -> 431', '2025-05-20 15:22:17', 1),
(416, '-', 432, 433, 1440, '432 -> 433', '2025-05-20 15:22:17', 1),
(417, '-', 432, 434, 60, '432 -> 434', '2025-05-20 15:22:17', 1),
(418, '-', 432, 446, 0.00144, '432 -> 446', '2025-05-20 15:22:17', 1),
(419, '-', 432, 450, 0.06, '432 -> 450', '2025-05-20 15:22:17', 1),
(420, '-', 432, 455, 0.000001, '432 -> 455', '2025-05-20 15:22:17', 1),
(421, '-', 432, 457, 1.44, '432 -> 457', '2025-05-20 15:22:17', 1),
(422, '-', 432, 458, 0.001, '432 -> 458', '2025-05-20 15:22:17', 1),
(423, '-', 433, 431, 41.666667, '433 -> 431', '2025-05-20 15:22:17', 1),
(424, '-', 433, 432, 0.000694, '433 -> 432', '2025-05-20 15:22:17', 1),
(425, '-', 433, 434, 0.041667, '433 -> 434', '2025-05-20 15:22:17', 1),
(426, '-', 433, 446, 0.000001, '433 -> 446', '2025-05-20 15:22:17', 1),
(427, '-', 433, 450, 0.000042, '433 -> 450', '2025-05-20 15:22:17', 1),
(428, '-', 433, 457, 0.001, '433 -> 457', '2025-05-20 15:22:17', 1),
(429, '-', 433, 458, 0.000001, '433 -> 458', '2025-05-20 15:22:17', 1),
(430, '-', 434, 431, 1000, '434 -> 431', '2025-05-20 15:22:17', 1),
(431, '-', 434, 432, 0.016667, '434 -> 432', '2025-05-20 15:22:17', 1),
(432, '-', 434, 433, 24, '434 -> 433', '2025-05-20 15:22:17', 1),
(433, '-', 434, 446, 0.000024, '434 -> 446', '2025-05-20 15:22:17', 1),
(434, '-', 434, 450, 0.001, '434 -> 450', '2025-05-20 15:22:17', 1),
(435, '-', 434, 452, 0.000001, '434 -> 452', '2025-05-20 15:22:17', 1),
(436, '-', 434, 457, 0.024, '434 -> 457', '2025-05-20 15:22:17', 1),
(437, '-', 434, 458, 0.000017, '434 -> 458', '2025-05-20 15:22:17', 1),
(438, '-', 435, 430, 1000000, '435 -> 430', '2025-05-20 15:22:17', 1),
(439, '-', 435, 451, 1000, '435 -> 451', '2025-05-20 15:22:17', 1),
(440, '-', 436, 746, 1000, '436 -> 746', '2025-05-20 15:22:17', 1),
(441, '-', 436, 860, 20, '436 -> 860', '2025-05-20 15:22:17', 1),
(442, '-', 437, 427, 694.40002, '437 -> 427', '2025-05-20 15:22:17', 1),
(443, '-', 437, 429, 41.666667, '437 -> 429', '2025-05-20 15:22:17', 1),
(444, '-', 437, 438, 1000000, '437 -> 438', '2025-05-20 15:22:17', 1),
(445, '-', 437, 448, 694444, '437 -> 448', '2025-05-20 15:22:17', 1),
(446, '-', 437, 449, 1000, '437 -> 449', '2025-05-20 15:22:17', 1),
(447, '-', 437, 454, 0.694444, '437 -> 454', '2025-05-20 15:22:17', 1),
(448, '-', 438, 427, 0.000694, '438 -> 427', '2025-05-20 15:22:17', 1),
(449, '-', 438, 429, 0.000042, '438 -> 429', '2025-05-20 15:22:17', 1),
(450, '-', 438, 437, 0.000001, '438 -> 437', '2025-05-20 15:22:17', 1),
(451, '-', 438, 448, 0.694444, '438 -> 448', '2025-05-20 15:22:17', 1),
(452, '-', 438, 449, 0.001, '438 -> 449', '2025-05-20 15:22:17', 1),
(453, '-', 438, 454, 0.000001, '438 -> 454', '2025-05-20 15:22:17', 1),
(454, '-', 438, 462, 0.041667, '438 -> 462', '2025-05-20 15:22:17', 1),
(455, '-', 440, 188, 1, '440 -> 188', '2025-05-20 15:22:17', 1),
(456, '-', 441, 486, 1, '441 -> 486', '2025-05-20 15:22:17', 1),
(457, '-', 443, 430, 0.001, '443 -> 430', '2025-05-20 15:22:17', 1),
(458, '-', 443, 451, 0.000001, '443 -> 451', '2025-05-20 15:22:17', 1),
(459, '-', 445, 428, 1000000, '445 -> 428', '2025-05-20 15:22:17', 1),
(460, '-', 445, 459, 1000, '445 -> 459', '2025-05-20 15:22:17', 1),
(461, '-', 446, 431, 41666667, '446 -> 431', '2025-05-20 15:22:17', 1),
(462, '-', 446, 432, 694.444444, '446 -> 432', '2025-05-20 15:22:17', 1),
(463, '-', 446, 433, 1000000, '446 -> 433', '2025-05-20 15:22:17', 1),
(464, '-', 446, 434, 41666.666667, '446 -> 434', '2025-05-20 15:22:17', 1),
(465, '-', 446, 450, 41.666667, '446 -> 450', '2025-05-20 15:22:17', 1),
(466, '-', 446, 452, 0.041667, '446 -> 452', '2025-05-20 15:22:17', 1),
(467, '-', 446, 455, 0.000694, '446 -> 455', '2025-05-20 15:22:17', 1),
(468, '-', 446, 457, 1000, '446 -> 457', '2025-05-20 15:22:17', 1),
(469, '-', 446, 458, 0.694444, '446 -> 458', '2025-05-20 15:22:17', 1),
(470, '-', 448, 427, 0.001, '448 -> 427', '2025-05-20 15:22:17', 1),
(471, '-', 448, 429, 0.00006, '448 -> 429', '2025-05-20 15:22:17', 1),
(472, '-', 448, 437, 0.000001, '448 -> 437', '2025-05-20 15:22:17', 1),
(473, '-', 448, 438, 1.44, '448 -> 438', '2025-05-20 15:22:17', 1),
(474, '-', 448, 449, 0.00144, '448 -> 449', '2025-05-20 15:22:17', 1),
(475, '-', 448, 454, 0.000001, '448 -> 454', '2025-05-20 15:22:17', 1),
(476, '-', 448, 462, 0.06, '448 -> 462', '2025-05-20 15:22:17', 1),
(477, '-', 449, 427, 0.694444, '449 -> 427', '2025-05-20 15:22:17', 1),
(478, '-', 449, 429, 0.041667, '449 -> 429', '2025-05-20 15:22:17', 1),
(479, '-', 449, 437, 0.001, '449 -> 437', '2025-05-20 15:22:17', 1),
(480, '-', 449, 438, 1000, '449 -> 438', '2025-05-20 15:22:17', 1),
(481, '-', 449, 448, 694.444444, '449 -> 448', '2025-05-20 15:22:17', 1),
(482, '-', 449, 454, 0.000694, '449 -> 454', '2025-05-20 15:22:17', 1),
(483, '-', 449, 462, 41.666667, '449 -> 462', '2025-05-20 15:22:17', 1),
(484, '-', 450, 431, 1000000, '450 -> 431', '2025-05-20 15:22:17', 1),
(485, '-', 450, 432, 16.666667, '450 -> 432', '2025-05-20 15:22:17', 1),
(486, '-', 450, 433, 24000, '450 -> 433', '2025-05-20 15:22:17', 1),
(487, '-', 450, 434, 1000, '450 -> 434', '2025-05-20 15:22:17', 1),
(488, '-', 450, 446, 0.024, '450 -> 446', '2025-05-20 15:22:17', 1),
(489, '-', 450, 452, 0.001, '450 -> 452', '2025-05-20 15:22:17', 1),
(490, '-', 450, 455, 0.000017, '450 -> 455', '2025-05-20 15:22:17', 1),
(491, '-', 450, 457, 24, '450 -> 457', '2025-05-20 15:22:17', 1),
(492, '-', 450, 458, 0.016667, '450 -> 458', '2025-05-20 15:22:17', 1),
(493, '-', 451, 430, 1000, '451 -> 430', '2025-05-20 15:22:17', 1),
(494, '-', 451, 435, 0.001, '451 -> 435', '2025-05-20 15:22:17', 1),
(495, '-', 451, 443, 1000000, '451 -> 443', '2025-05-20 15:22:17', 1),
(496, '-', 452, 431, 1000000000, '452 -> 431', '2025-05-20 15:22:17', 1),
(497, '-', 452, 432, 16666.666667, '452 -> 432', '2025-05-20 15:22:17', 1),
(498, '-', 452, 433, 24000000, '452 -> 433', '2025-05-20 15:22:17', 1),
(499, '-', 452, 434, 1000000, '452 -> 434', '2025-05-20 15:22:17', 1),
(500, '-', 452, 446, 24, '452 -> 446', '2025-05-20 15:22:17', 1),
(501, '-', 452, 450, 1000, '452 -> 450', '2025-05-20 15:22:17', 1),
(502, '-', 452, 455, 0.016667, '452 -> 455', '2025-05-20 15:22:17', 1),
(503, '-', 452, 457, 24000, '452 -> 457', '2025-05-20 15:22:17', 1),
(504, '-', 452, 458, 16.666667, '452 -> 458', '2025-05-20 15:22:17', 1),
(505, '-', 454, 427, 1000, '454 -> 427', '2025-05-20 15:22:17', 1),
(506, '-', 454, 429, 60, '454 -> 429', '2025-05-20 15:22:17', 1),
(507, '-', 454, 437, 1.44, '454 -> 437', '2025-05-20 15:22:17', 1),
(508, '-', 454, 438, 1440000, '454 -> 438', '2025-05-20 15:22:17', 1),
(509, '-', 454, 448, 1000000, '454 -> 448', '2025-05-20 15:22:17', 1),
(510, '-', 454, 449, 1440, '454 -> 449', '2025-05-20 15:22:17', 1),
(511, '-', 454, 462, 60000, '454 -> 462', '2025-05-20 15:22:17', 1),
(512, '-', 454, 491, 0.06, '454 -> 491', '2025-05-20 15:22:17', 1),
(513, '-', 455, 432, 1000000, '455 -> 432', '2025-05-20 15:22:17', 1),
(514, '-', 455, 433, 1440000000, '455 -> 433', '2025-05-20 15:22:17', 1),
(515, '-', 455, 434, 60000000, '455 -> 434', '2025-05-20 15:22:17', 1),
(516, '-', 455, 446, 1440, '455 -> 446', '2025-05-20 15:22:17', 1),
(517, '-', 455, 450, 60000, '455 -> 450', '2025-05-20 15:22:17', 1),
(518, '-', 455, 452, 60, '455 -> 452', '2025-05-20 15:22:17', 1),
(519, '-', 455, 457, 1440000, '455 -> 457', '2025-05-20 15:22:17', 1),
(520, '-', 455, 458, 1000, '455 -> 458', '2025-05-20 15:22:17', 1),
(521, '-', 456, 859, 20, '456 -> 859', '2025-05-20 15:22:17', 1),
(522, '-', 457, 431, 41666.666667, '457 -> 431', '2025-05-20 15:22:17', 1),
(523, '-', 457, 432, 0.694444, '457 -> 432', '2025-05-20 15:22:17', 1),
(524, '-', 457, 433, 1000, '457 -> 433', '2025-05-20 15:22:17', 1),
(525, '-', 457, 434, 41.666667, '457 -> 434', '2025-05-20 15:22:17', 1),
(526, '-', 457, 446, 0.001, '457 -> 446', '2025-05-20 15:22:17', 1),
(527, '-', 457, 450, 0.041667, '457 -> 450', '2025-05-20 15:22:17', 1),
(528, '-', 457, 452, 0.000042, '457 -> 452', '2025-05-20 15:22:17', 1),
(529, '-', 457, 455, 0.000001, '457 -> 455', '2025-05-20 15:22:17', 1),
(530, '-', 457, 458, 0.000694, '457 -> 458', '2025-05-20 15:22:17', 1),
(531, '-', 458, 431, 60000000, '458 -> 431', '2025-05-20 15:22:17', 1),
(532, '-', 458, 432, 1000, '458 -> 432', '2025-05-20 15:22:17', 1),
(533, '-', 458, 433, 1440000, '458 -> 433', '2025-05-20 15:22:17', 1),
(534, '-', 458, 434, 60000, '458 -> 434', '2025-05-20 15:22:17', 1),
(535, '-', 458, 446, 1.44, '458 -> 446', '2025-05-20 15:22:17', 1),
(536, '-', 458, 450, 60, '458 -> 450', '2025-05-20 15:22:17', 1),
(537, '-', 458, 452, 0.06, '458 -> 452', '2025-05-20 15:22:17', 1),
(538, '-', 458, 455, 0.001, '458 -> 455', '2025-05-20 15:22:17', 1),
(539, '-', 458, 457, 1440, '458 -> 457', '2025-05-20 15:22:17', 1),
(540, '-', 459, 428, 1000, '459 -> 428', '2025-05-20 15:22:17', 1),
(541, '-', 459, 445, 0.001, '459 -> 445', '2025-05-20 15:22:17', 1),
(542, '-', 462, 427, 0.016667, '462 -> 427', '2025-05-20 15:22:17', 1),
(543, '-', 462, 429, 0.001, '462 -> 429', '2025-05-20 15:22:17', 1),
(544, '-', 462, 437, 0.000024, '462 -> 437', '2025-05-20 15:22:17', 1),
(545, '-', 462, 438, 24, '462 -> 438', '2025-05-20 15:22:17', 1),
(546, '-', 462, 448, 16.666667, '462 -> 448', '2025-05-20 15:22:17', 1),
(547, '-', 462, 449, 0.024, '462 -> 449', '2025-05-20 15:22:17', 1),
(548, '-', 462, 454, 0.000017, '462 -> 454', '2025-05-20 15:22:17', 1),
(549, '-', 464, 467, 0.000001, '464 -> 467', '2025-05-20 15:22:17', 1),
(550, '-', 464, 470, 0.001, '464 -> 470', '2025-05-20 15:22:17', 1),
(551, '-', 464, 471, 0.000042, '464 -> 471', '2025-05-20 15:22:17', 1),
(552, '-', 465, 468, 0.000001, '465 -> 468', '2025-05-20 15:22:17', 1),
(553, '-', 465, 478, 0.001, '465 -> 478', '2025-05-20 15:22:17', 1),
(554, '-', 466, 167, 0.001, '466 -> 167', '2025-05-20 15:22:17', 1),
(555, '-', 466, 479, 1000, '466 -> 479', '2025-05-20 15:22:17', 1),
(556, '-', 467, 464, 1000000, '467 -> 464', '2025-05-20 15:22:17', 1),
(557, '-', 467, 470, 1000, '467 -> 470', '2025-05-20 15:22:17', 1),
(558, '-', 467, 471, 41.666667, '467 -> 471', '2025-05-20 15:22:17', 1),
(559, '-', 468, 465, 1000000, '468 -> 465', '2025-05-20 15:22:17', 1),
(560, '-', 468, 478, 1000, '468 -> 478', '2025-05-20 15:22:17', 1),
(561, '-', 469, 472, 0.001, '469 -> 472', '2025-05-20 15:22:17', 1),
(562, '-', 469, 474, 1000000, '469 -> 474', '2025-05-20 15:22:17', 1),
(563, '-', 469, 477, 0.000001, '469 -> 477', '2025-05-20 15:22:17', 1),
(564, '-', 470, 464, 1000, '470 -> 464', '2025-05-20 15:22:17', 1),
(565, '-', 470, 467, 0.001, '470 -> 467', '2025-05-20 15:22:17', 1),
(566, '-', 470, 471, 0.041667, '470 -> 471', '2025-05-20 15:22:17', 1),
(567, '-', 471, 173, 0.001, '471 -> 173', '2025-05-20 15:22:17', 1),
(568, '-', 471, 199, 1000, '471 -> 199', '2025-05-20 15:22:17', 1),
(569, '-', 471, 464, 24000, '471 -> 464', '2025-05-20 15:22:17', 1),
(570, '-', 471, 467, 0.024, '471 -> 467', '2025-05-20 15:22:17', 1),
(571, '-', 471, 470, 24, '471 -> 470', '2025-05-20 15:22:17', 1),
(572, '-', 472, 469, 1000, '472 -> 469', '2025-05-20 15:22:17', 1),
(573, '-', 472, 477, 0.001, '472 -> 477', '2025-05-20 15:22:17', 1),
(574, '-', 474, 469, 0.000001, '474 -> 469', '2025-05-20 15:22:17', 1),
(575, '-', 476, 175, 1000000, '476 -> 175', '2025-05-20 15:22:17', 1),
(576, '-', 477, 469, 1000000, '477 -> 469', '2025-05-20 15:22:17', 1),
(577, '-', 477, 472, 1000, '477 -> 472', '2025-05-20 15:22:17', 1),
(578, '-', 478, 465, 1000, '478 -> 465', '2025-05-20 15:22:17', 1),
(579, '-', 478, 468, 0.001, '478 -> 468', '2025-05-20 15:22:17', 1),
(580, '-', 479, 466, 0.001, '479 -> 466', '2025-05-20 15:22:17', 1),
(581, '-', 482, 131, 1, '482 -> 131', '2025-05-20 15:22:17', 1),
(582, '-', 482, 132, 1, '482 -> 132', '2025-05-20 15:22:17', 1),
(583, '-', 482, 140, 1, '482 -> 140', '2025-05-20 15:22:17', 1),
(584, '-', 482, 148, 1, '482 -> 148', '2025-05-20 15:22:17', 1),
(585, '-', 482, 149, 1, '482 -> 149', '2025-05-20 15:22:17', 1),
(586, '-', 482, 154, 1, '482 -> 154', '2025-05-20 15:22:17', 1),
(587, '-', 482, 155, 1, '482 -> 155', '2025-05-20 15:22:17', 1),
(588, '-', 482, 156, 1, '482 -> 156', '2025-05-20 15:22:17', 1),
(589, '-', 482, 185, 1, '482 -> 185', '2025-05-20 15:22:17', 1),
(590, '-', 482, 192, 1, '482 -> 192', '2025-05-20 15:22:17', 1),
(591, '-', 482, 200, 1, '482 -> 200', '2025-05-20 15:22:17', 1),
(592, '-', 482, 366, 1, '482 -> 366', '2025-05-20 15:22:17', 1),
(593, '-', 482, 376, 1, '482 -> 376', '2025-05-20 15:22:17', 1),
(594, '-', 482, 378, 1, '482 -> 378', '2025-05-20 15:22:17', 1),
(595, '-', 482, 386, 1, '482 -> 386', '2025-05-20 15:22:17', 1),
(596, '-', 482, 389, 1, '482 -> 389', '2025-05-20 15:22:17', 1),
(597, '-', 482, 391, 1, '482 -> 391', '2025-05-20 15:22:17', 1),
(598, '-', 482, 399, 1, '482 -> 399', '2025-05-20 15:22:17', 1),
(599, '-', 482, 402, 1, '482 -> 402', '2025-05-20 15:22:17', 1),
(600, '-', 482, 407, 1, '482 -> 407', '2025-05-20 15:22:17', 1),
(601, '-', 482, 419, 1, '482 -> 419', '2025-05-20 15:22:17', 1),
(602, '-', 482, 422, 1, '482 -> 422', '2025-05-20 15:22:17', 1),
(603, '-', 482, 425, 1, '482 -> 425', '2025-05-20 15:22:17', 1),
(604, '-', 482, 505, 1, '482 -> 505', '2025-05-20 15:22:17', 1),
(605, '-', 482, 561, 1, '482 -> 561', '2025-05-20 15:22:17', 1),
(606, '-', 482, 581, 1, '482 -> 581', '2025-05-20 15:22:17', 1),
(607, '-', 482, 583, 1, '482 -> 583', '2025-05-20 15:22:17', 1),
(608, '-', 482, 683, 1, '482 -> 683', '2025-05-20 15:22:17', 1),
(609, '-', 482, 750, 1, '482 -> 750', '2025-05-20 15:22:17', 1),
(610, '-', 482, 785, 1, '482 -> 785', '2025-05-20 15:22:17', 1),
(611, '-', 482, 896, 1, '482 -> 896', '2025-05-20 15:22:17', 1),
(612, '-', 482, 918, 1, '482 -> 918', '2025-05-20 15:22:17', 1),
(613, '-', 482, 919, 1, '482 -> 919', '2025-05-20 15:22:17', 1),
(614, '-', 482, 920, 1, '482 -> 920', '2025-05-20 15:22:17', 1),
(615, '-', 482, 921, 1, '482 -> 921', '2025-05-20 15:22:17', 1),
(616, '-', 482, 922, 1, '482 -> 922', '2025-05-20 15:22:17', 1),
(617, '-', 482, 923, 1, '482 -> 923', '2025-05-20 15:22:17', 1),
(618, '-', 482, 924, 1, '482 -> 924', '2025-05-20 15:22:17', 1),
(619, '-', 482, 925, 1, '482 -> 925', '2025-05-20 15:22:17', 1),
(620, '-', 482, 928, 1, '482 -> 928', '2025-05-20 15:22:17', 1),
(621, '-', 482, 929, 1, '482 -> 929', '2025-05-20 15:22:17', 1),
(622, '-', 482, 930, 1, '482 -> 930', '2025-05-20 15:22:17', 1),
(623, '-', 482, 931, 1, '482 -> 931', '2025-05-20 15:22:17', 1),
(624, '-', 482, 932, 1, '482 -> 932', '2025-05-20 15:22:17', 1),
(625, '-', 482, 933, 1, '482 -> 933', '2025-05-20 15:22:17', 1),
(626, '-', 482, 934, 1, '482 -> 934', '2025-05-20 15:22:17', 1),
(627, '-', 482, 935, 1, '482 -> 935', '2025-05-20 15:22:17', 1),
(628, '-', 482, 936, 1, '482 -> 936', '2025-05-20 15:22:17', 1),
(629, '-', 482, 937, 1, '482 -> 937', '2025-05-20 15:22:17', 1),
(630, '-', 482, 938, 1, '482 -> 938', '2025-05-20 15:22:17', 1),
(631, '-', 482, 939, 1, '482 -> 939', '2025-05-20 15:22:17', 1),
(632, '-', 482, 940, 1, '482 -> 940', '2025-05-20 15:22:17', 1),
(633, '-', 482, 941, 1, '482 -> 941', '2025-05-20 15:22:17', 1),
(634, '-', 482, 943, 1, '482 -> 943', '2025-05-20 15:22:17', 1),
(635, '-', 482, 989, 1, '482 -> 989', '2025-05-20 15:22:17', 1),
(636, '-', 482, 990, 1, '482 -> 990', '2025-05-20 15:22:17', 1),
(637, '-', 482, 991, 1, '482 -> 991', '2025-05-20 15:22:17', 1),
(638, '-', 482, 992, 1, '482 -> 992', '2025-05-20 15:22:17', 1),
(639, '-', 482, 993, 1, '482 -> 993', '2025-05-20 15:22:17', 1),
(640, '-', 482, 994, 1, '482 -> 994', '2025-05-20 15:22:17', 1),
(641, '-', 482, 995, 1, '482 -> 995', '2025-05-20 15:22:17', 1),
(642, '-', 482, 996, 1, '482 -> 996', '2025-05-20 15:22:17', 1),
(643, '-', 482, 997, 1, '482 -> 997', '2025-05-20 15:22:17', 1),
(644, '-', 482, 998, 1, '482 -> 998', '2025-05-20 15:22:17', 1),
(645, '-', 482, 999, 1, '482 -> 999', '2025-05-20 15:22:17', 1),
(646, '-', 482, 1000, 1, '482 -> 1000', '2025-05-20 15:22:17', 1),
(647, '-', 482, 1001, 1, '482 -> 1001', '2025-05-20 15:22:17', 1),
(648, '-', 482, 1002, 1, '482 -> 1002', '2025-05-20 15:22:17', 1),
(649, '-', 482, 1003, 1, '482 -> 1003', '2025-05-20 15:22:17', 1),
(650, '-', 483, 353, 1, '483 -> 353', '2025-05-20 15:22:17', 1),
(651, '-', 483, 354, 1, '483 -> 354', '2025-05-20 15:22:17', 1),
(652, '-', 483, 360, 1, '483 -> 360', '2025-05-20 15:22:17', 1),
(653, '-', 483, 421, 1, '483 -> 421', '2025-05-20 15:22:17', 1),
(654, '-', 483, 485, 0.041666, '483 -> 485', '2025-05-20 15:22:17', 1),
(655, '-', 483, 786, 1, '483 -> 786', '2025-05-20 15:22:17', 1),
(656, '-', 483, 845, 1, '483 -> 845', '2025-05-20 15:22:17', 1),
(657, '-', 483, 846, 1, '483 -> 846', '2025-05-20 15:22:17', 1),
(658, '-', 483, 850, 1, '483 -> 850', '2025-05-20 15:22:17', 1),
(659, '-', 483, 946, 1, '483 -> 946', '2025-05-20 15:22:17', 1),
(660, '-', 483, 947, 1, '483 -> 947', '2025-05-20 15:22:17', 1),
(661, '-', 483, 948, 1, '483 -> 948', '2025-05-20 15:22:17', 1),
(662, '-', 483, 949, 1, '483 -> 949', '2025-05-20 15:22:17', 1),
(663, '-', 483, 950, 1, '483 -> 950', '2025-05-20 15:22:17', 1),
(664, '-', 483, 951, 1, '483 -> 951', '2025-05-20 15:22:17', 1),
(665, '-', 483, 952, 1, '483 -> 952', '2025-05-20 15:22:17', 1),
(666, '-', 483, 953, 1, '483 -> 953', '2025-05-20 15:22:17', 1),
(667, '-', 483, 954, 1, '483 -> 954', '2025-05-20 15:22:17', 1),
(668, '-', 483, 955, 1, '483 -> 955', '2025-05-20 15:22:17', 1),
(669, '-', 483, 956, 1, '483 -> 956', '2025-05-20 15:22:17', 1),
(670, '-', 483, 957, 1, '483 -> 957', '2025-05-20 15:22:17', 1),
(671, '-', 483, 958, 1, '483 -> 958', '2025-05-20 15:22:17', 1),
(672, '-', 483, 959, 1, '483 -> 959', '2025-05-20 15:22:17', 1),
(673, '-', 483, 960, 1, '483 -> 960', '2025-05-20 15:22:17', 1),
(674, '-', 483, 961, 1, '483 -> 961', '2025-05-20 15:22:17', 1),
(675, '-', 483, 962, 1, '483 -> 962', '2025-05-20 15:22:17', 1),
(676, '-', 483, 963, 1, '483 -> 963', '2025-05-20 15:22:17', 1),
(677, '-', 483, 964, 1, '483 -> 964', '2025-05-20 15:22:17', 1),
(678, '-', 483, 965, 1, '483 -> 965', '2025-05-20 15:22:17', 1),
(679, '-', 483, 981, 1, '483 -> 981', '2025-05-20 15:22:17', 1),
(680, '-', 483, 1004, 1, '483 -> 1004', '2025-05-20 15:22:17', 1),
(681, '-', 483, 1005, 1, '483 -> 1005', '2025-05-20 15:22:17', 1),
(682, '-', 483, 1006, 1, '483 -> 1006', '2025-05-20 15:22:17', 1),
(683, '-', 483, 1007, 1, '483 -> 1007', '2025-05-20 15:22:17', 1),
(684, '-', 483, 1008, 1, '483 -> 1008', '2025-05-20 15:22:17', 1),
(685, '-', 483, 1009, 1, '483 -> 1009', '2025-05-20 15:22:17', 1),
(686, '-', 483, 1010, 1, '483 -> 1010', '2025-05-20 15:22:17', 1),
(687, '-', 483, 1011, 1, '483 -> 1011', '2025-05-20 15:22:17', 1),
(688, '-', 483, 1012, 1, '483 -> 1012', '2025-05-20 15:22:17', 1),
(689, '-', 483, 1013, 1, '483 -> 1013', '2025-05-20 15:22:17', 1),
(690, '-', 483, 1014, 1, '483 -> 1014', '2025-05-20 15:22:17', 1),
(691, '-', 483, 1015, 1, '483 -> 1015', '2025-05-20 15:22:17', 1),
(692, '-', 483, 1016, 1, '483 -> 1016', '2025-05-20 15:22:17', 1),
(693, '-', 483, 1017, 1, '483 -> 1017', '2025-05-20 15:22:17', 1),
(694, '-', 483, 1018, 1, '483 -> 1018', '2025-05-20 15:22:17', 1),
(695, '-', 485, 483, 24, '485 -> 483', '2025-05-20 15:22:17', 1),
(696, '-', 485, 851, 1, '485 -> 851', '2025-05-20 15:22:17', 1),
(697, '-', 490, 355, 1000, '490 -> 355', '2025-05-20 15:22:17', 1),
(698, '-', 490, 358, 24000, '490 -> 358', '2025-05-20 15:22:17', 1),
(699, '-', 490, 390, 24, '490 -> 390', '2025-05-20 15:22:17', 1),
(700, '-', 490, 404, 16.666667, '490 -> 404', '2025-05-20 15:22:17', 1),
(701, '-', 491, 429, 1000, '491 -> 429', '2025-05-20 15:22:17', 1),
(702, '-', 491, 437, 24, '491 -> 437', '2025-05-20 15:22:17', 1),
(703, '-', 491, 449, 24000, '491 -> 449', '2025-05-20 15:22:17', 1),
(704, '-', 491, 454, 16.666667, '491 -> 454', '2025-05-20 15:22:17', 1),
(705, '-', 494, 367, 1, '494 -> 367', '2025-05-20 15:22:17', 1),
(706, '-', 494, 368, 0.001, '494 -> 368', '2025-05-20 15:22:17', 1),
(707, '-', 505, 482, 1, '505 -> 482', '2025-05-20 15:22:17', 1),
(708, '-', 506, 483, 1, '506 -> 483', '2025-05-20 15:22:17', 1),
(709, '-', 523, 383, 6.4516, '523 -> 383', '2025-05-20 15:22:17', 1),
(710, '-', 561, 482, 1, '561 -> 482', '2025-05-20 15:22:17', 1),
(711, '-', 581, 482, 1, '581 -> 482', '2025-05-20 15:22:17', 1),
(712, '-', 582, 483, 1, '582 -> 483', '2025-05-20 15:22:17', 1),
(713, '-', 583, 482, 1, '583 -> 482', '2025-05-20 15:22:17', 1),
(714, '-', 584, 483, 1, '584 -> 483', '2025-05-20 15:22:17', 1),
(715, '-', 612, 483, 1, '612 -> 483', '2025-05-20 15:22:17', 1),
(716, '-', 661, 483, 1, '661 -> 483', '2025-05-20 15:22:17', 1),
(717, '-', 683, 192, 1, '683 -> 192', '2025-05-20 15:22:17', 1),
(718, '-', 683, 366, 1, '683 -> 366', '2025-05-20 15:22:17', 1),
(719, '-', 683, 482, 1, '683 -> 482', '2025-05-20 15:22:17', 1),
(720, '-', 684, 798, 1000000, '684 -> 798', '2025-05-20 15:22:17', 1),
(721, '-', 684, 916, 0.001, '684 -> 916', '2025-05-20 15:22:17', 1),
(722, '-', 704, 373, 100, '704 -> 373', '2025-05-20 15:22:17', 1),
(723, '-', 704, 375, 39.370078, '704 -> 375', '2025-05-20 15:22:17', 1),
(724, '-', 704, 802, 3.2808, '704 -> 802', '2025-05-20 15:22:17', 1),
(725, '-', 704, 816, 1000, '704 -> 816', '2025-05-20 15:22:17', 1),
(726, '-', 704, 820, 1.0936, '704 -> 820', '2025-05-20 15:22:17', 1),
(727, '-', 745, 380, 0.001, '745 -> 380', '2025-05-20 15:22:17', 1),
(728, '-', 746, 436, 0.001, '746 -> 436', '2025-05-20 15:22:17', 1),
(729, '-', 748, 369, 0.001, '748 -> 369', '2025-05-20 15:22:17', 1),
(730, '-', 748, 809, 0.000001, '748 -> 809', '2025-05-20 15:22:17', 1),
(731, '-', 749, 483, 1, '749 -> 483', '2025-05-20 15:22:17', 1),
(732, '-', 750, 482, 1, '750 -> 482', '2025-05-20 15:22:17', 1),
(733, '-', 751, 191, 10, '751 -> 191', '2025-05-20 15:22:17', 1),
(734, '-', 751, 350, 10000, '751 -> 350', '2025-05-20 15:22:17', 1),
(735, '-', 751, 753, 1, '751 -> 753', '2025-05-20 15:22:17', 1),
(736, '-', 751, 754, 1000, '751 -> 754', '2025-05-20 15:22:17', 1),
(737, '-', 752, 170, 0.1, '752 -> 170', '2025-05-20 15:22:17', 1),
(738, '-', 752, 380, 100, '752 -> 380', '2025-05-20 15:22:17', 1),
(739, '-', 753, 191, 10, '753 -> 191', '2025-05-20 15:22:17', 1),
(740, '-', 753, 350, 10000, '753 -> 350', '2025-05-20 15:22:17', 1),
(741, '-', 753, 751, 1, '753 -> 751', '2025-05-20 15:22:17', 1),
(742, '-', 753, 754, 1000, '753 -> 754', '2025-05-20 15:22:17', 1),
(743, '-', 754, 191, 0.01, '754 -> 191', '2025-05-20 15:22:17', 1),
(744, '-', 754, 350, 10, '754 -> 350', '2025-05-20 15:22:17', 1),
(745, '-', 754, 751, 0.001, '754 -> 751', '2025-05-20 15:22:17', 1),
(746, '-', 754, 753, 0.001, '754 -> 753', '2025-05-20 15:22:17', 1),
(747, '-', 755, 168, 0.1, '755 -> 168', '2025-05-20 15:22:17', 1),
(748, '-', 755, 367, 100000, '755 -> 367', '2025-05-20 15:22:17', 1),
(749, '-', 755, 368, 100, '755 -> 368', '2025-05-20 15:22:17', 1),
(750, '-', 756, 757, 1000, '756 -> 757', '2025-05-20 15:22:17', 1),
(751, '-', 756, 758, 1, '756 -> 758', '2025-05-20 15:22:17', 1),
(752, '-', 757, 756, 0.001, '757 -> 756', '2025-05-20 15:22:17', 1),
(753, '-', 757, 758, 0.001, '757 -> 758', '2025-05-20 15:22:17', 1),
(754, '-', 758, 756, 1, '758 -> 756', '2025-05-20 15:22:17', 1),
(755, '-', 758, 757, 1000, '758 -> 757', '2025-05-20 15:22:17', 1),
(756, '-', 769, 201, 0.001, '769 -> 201', '2025-05-20 15:22:17', 1),
(757, '-', 769, 804, 0.000001, '769 -> 804', '2025-05-20 15:22:17', 1),
(758, '-', 770, 380, 3850, '770 -> 380', '2025-05-20 15:22:17', 1),
(759, '-', 773, 869, 1, '773 -> 869', '2025-05-20 15:22:17', 1),
(760, '-', 773, 873, 1, '773 -> 873', '2025-05-20 15:22:17', 1),
(761, '-', 773, 874, 1, '773 -> 874', '2025-05-20 15:22:17', 1),
(762, '-', 774, 870, 1, '774 -> 870', '2025-05-20 15:22:17', 1),
(763, '-', 774, 875, 1, '774 -> 875', '2025-05-20 15:22:17', 1),
(764, '-', 774, 876, 1, '774 -> 876', '2025-05-20 15:22:17', 1),
(765, '-', 778, 195, 0.6944, '778 -> 195', '2025-05-20 15:22:17', 1),
(766, '-', 778, 356, 41.6, '778 -> 356', '2025-05-20 15:22:17', 1),
(767, '-', 778, 361, 1000, '778 -> 361', '2025-05-20 15:22:17', 1),
(768, '-', 778, 779, 0.041667, '778 -> 779', '2025-05-20 15:22:17', 1),
(769, '-', 778, 853, 0.000694, '778 -> 853', '2025-05-20 15:22:17', 1),
(770, '-', 779, 195, 16.666667, '779 -> 195', '2025-05-20 15:22:17', 1),
(771, '-', 779, 356, 1000, '779 -> 356', '2025-05-20 15:22:17', 1),
(772, '-', 779, 361, 24000, '779 -> 361', '2025-05-20 15:22:17', 1),
(773, '-', 779, 778, 24, '779 -> 778', '2025-05-20 15:22:17', 1),
(774, '-', 779, 853, 0.016667, '779 -> 853', '2025-05-20 15:22:17', 1),
(775, '-', 784, 847, 1000000, '784 -> 847', '2025-05-20 15:22:17', 1),
(776, '-', 784, 915, 0.001, '784 -> 915', '2025-05-20 15:22:17', 1),
(777, '-', 785, 482, 1, '785 -> 482', '2025-05-20 15:22:17', 1),
(778, '-', 786, 483, 1, '786 -> 483', '2025-05-20 15:22:17', 1),
(779, '-', 787, 483, 1, '787 -> 483', '2025-05-20 15:22:17', 1),
(780, '-', 788, 486, 1, '788 -> 486', '2025-05-20 15:22:17', 1),
(781, '-', 789, 188, 1, '789 -> 188', '2025-05-20 15:22:17', 1),
(782, '-', 790, 188, 1, '790 -> 188', '2025-05-20 15:22:17', 1),
(783, '-', 791, 486, 1, '791 -> 486', '2025-05-20 15:22:17', 1),
(784, '-', 792, 486, 1, '792 -> 486', '2025-05-20 15:22:17', 1),
(785, '-', 793, 188, 1, '793 -> 188', '2025-05-20 15:22:17', 1),
(786, '-', 796, 751, 1, '796 -> 751', '2025-05-20 15:22:17', 1),
(787, '-', 796, 758, 1, '796 -> 758', '2025-05-20 15:22:17', 1),
(788, '-', 798, 684, 0.000001, '798 -> 684', '2025-05-20 15:22:17', 1),
(789, '-', 798, 898, 0.000001, '798 -> 898', '2025-05-20 15:22:17', 1),
(790, '-', 801, 380, 0.061612, '801 -> 380', '2025-05-20 15:22:17', 1),
(791, '-', 801, 745, 61.61152, '801 -> 745', '2025-05-20 15:22:17', 1),
(792, '-', 802, 373, 30.48, '802 -> 373', '2025-05-20 15:22:17', 1),
(793, '-', 802, 375, 12, '802 -> 375', '2025-05-20 15:22:17', 1),
(794, '-', 802, 704, 0.3048, '802 -> 704', '2025-05-20 15:22:17', 1),
(795, '-', 802, 816, 304.8, '802 -> 816', '2025-05-20 15:22:17', 1),
(796, '-', 802, 820, 0.333333, '802 -> 820', '2025-05-20 15:22:17', 1),
(797, '-', 804, 201, 1000, '804 -> 201', '2025-05-20 15:22:17', 1),
(798, '-', 804, 769, 1000000, '804 -> 769', '2025-05-20 15:22:17', 1),
(799, '-', 806, 371, 1, '806 -> 371', '2025-05-20 15:22:17', 1),
(800, '-', 807, 810, 1000000000, '807 -> 810', '2025-05-20 15:22:17', 1),
(801, '-', 807, 811, 1000000, '807 -> 811', '2025-05-20 15:22:17', 1),
(802, '-', 807, 812, 1000, '807 -> 812', '2025-05-20 15:22:17', 1),
(803, '-', 808, 371, 1, '808 -> 371', '2025-05-20 15:22:17', 1),
(804, '-', 809, 369, 1000, '809 -> 369', '2025-05-20 15:22:17', 1),
(805, '-', 809, 748, 1000000, '809 -> 748', '2025-05-20 15:22:17', 1),
(806, '-', 810, 811, 0.001, '810 -> 811', '2025-05-20 15:22:17', 1),
(807, '-', 810, 812, 0.000001, '810 -> 812', '2025-05-20 15:22:17', 1),
(808, '-', 811, 807, 0.000001, '811 -> 807', '2025-05-20 15:22:17', 1),
(809, '-', 811, 810, 1000, '811 -> 810', '2025-05-20 15:22:17', 1),
(810, '-', 811, 812, 0.001, '811 -> 812', '2025-05-20 15:22:17', 1),
(811, '-', 812, 807, 0.001, '812 -> 807', '2025-05-20 15:22:17', 1),
(812, '-', 812, 810, 1000000, '812 -> 810', '2025-05-20 15:22:17', 1),
(813, '-', 812, 811, 1000, '812 -> 811', '2025-05-20 15:22:17', 1),
(814, '-', 813, 201, 0.000001, '813 -> 201', '2025-05-20 15:22:17', 1),
(815, '-', 814, 406, 1000, '814 -> 406', '2025-05-20 15:22:17', 1),
(816, '-', 815, 371, 1, '815 -> 371', '2025-05-20 15:22:17', 1),
(817, '-', 816, 373, 0.1, '816 -> 373', '2025-05-20 15:22:17', 1),
(818, '-', 816, 375, 0.03937, '816 -> 375', '2025-05-20 15:22:17', 1),
(819, '-', 816, 704, 0.001, '816 -> 704', '2025-05-20 15:22:17', 1),
(820, '-', 819, 163, 0.0625, '819 -> 163', '2025-05-20 15:22:17', 1),
(821, '-', 819, 168, 0.02835, '819 -> 168', '2025-05-20 15:22:17', 1),
(822, '-', 819, 367, 28349.523125, '819 -> 367', '2025-05-20 15:22:17', 1),
(823, '-', 819, 368, 28.349523, '819 -> 368', '2025-05-20 15:22:17', 1),
(824, '-', 820, 373, 91.44, '820 -> 373', '2025-05-20 15:22:17', 1),
(825, '-', 820, 375, 36, '820 -> 375', '2025-05-20 15:22:17', 1),
(826, '-', 820, 704, 0.914402, '820 -> 704', '2025-05-20 15:22:17', 1),
(827, '-', 820, 802, 3, '820 -> 802', '2025-05-20 15:22:17', 1),
(828, '-', 820, 816, 914.402, '820 -> 816', '2025-05-20 15:22:17', 1),
(829, '-', 823, 371, 1, '823 -> 371', '2025-05-20 15:22:17', 1),
(830, '-', 825, 371, 1, '825 -> 371', '2025-05-20 15:22:17', 1),
(831, '-', 827, 371, 1, '827 -> 371', '2025-05-20 15:22:17', 1),
(832, '-', 831, 164, 1.73, '831 -> 164', '2025-05-20 15:22:17', 1),
(833, '-', 834, 186, 1, '834 -> 186', '2025-05-20 15:22:17', 1),
(834, '-', 836, 368, 0.001, '836 -> 368', '2025-05-20 15:22:17', 1),
(835, '-', 836, 372, 1000, '836 -> 372', '2025-05-20 15:22:17', 1),
(836, '-', 839, 346, 0.001, '839 -> 346', '2025-05-20 15:22:17', 1),
(837, '-', 840, 438, 0.001, '840 -> 438', '2025-05-20 15:22:17', 1),
(838, '-', 845, 353, 1, '845 -> 353', '2025-05-20 15:22:17', 1),
(839, '-', 845, 483, 1, '845 -> 483', '2025-05-20 15:22:17', 1),
(840, '-', 846, 483, 1, '846 -> 483', '2025-05-20 15:22:17', 1),
(841, '-', 847, 784, 0.000001, '847 -> 784', '2025-05-20 15:22:17', 1),
(842, '-', 848, 431, 60, '848 -> 431', '2025-05-20 15:22:17', 1),
(843, '-', 848, 432, 0.001, '848 -> 432', '2025-05-20 15:22:17', 1),
(844, '-', 849, 482, 1, '849 -> 482', '2025-05-20 15:22:17', 1),
(845, '-', 850, 483, 1, '850 -> 483', '2025-05-20 15:22:17', 1),
(846, '-', 851, 485, 1, '851 -> 485', '2025-05-20 15:22:17', 1),
(847, '-', 852, 350, 1000, '852 -> 350', '2025-05-20 15:22:17', 1),
(848, '-', 853, 195, 1000, '853 -> 195', '2025-05-20 15:22:17', 1),
(849, '-', 853, 356, 60000, '853 -> 356', '2025-05-20 15:22:17', 1),
(850, '-', 853, 361, 1440000, '853 -> 361', '2025-05-20 15:22:17', 1),
(851, '-', 853, 778, 1440, '853 -> 778', '2025-05-20 15:22:17', 1),
(852, '-', 853, 779, 60, '853 -> 779', '2025-05-20 15:22:17', 1),
(853, '-', 858, 807, 0.001, '858 -> 807', '2025-05-20 15:22:17', 1),
(854, '-', 858, 810, 1000000, '858 -> 810', '2025-05-20 15:22:17', 1),
(855, '-', 858, 811, 1000, '858 -> 811', '2025-05-20 15:22:17', 1),
(856, '-', 859, 456, 0.05, '859 -> 456', '2025-05-20 15:22:17', 1),
(857, '-', 860, 436, 0.05, '860 -> 436', '2025-05-20 15:22:17', 1),
(858, '-', 861, 164, 0.0001, '861 -> 164', '2025-05-20 15:22:17', 1),
(859, '-', 863, 810, 1000, '863 -> 810', '2025-05-20 15:22:17', 1),
(860, '-', 863, 812, 0.001, '863 -> 812', '2025-05-20 15:22:17', 1),
(861, '-', 864, 810, 1000, '864 -> 810', '2025-05-20 15:22:17', 1),
(862, '-', 864, 812, 0.001, '864 -> 812', '2025-05-20 15:22:17', 1),
(863, '-', 867, 371, 1, '867 -> 371', '2025-05-20 15:22:17', 1),
(864, '-', 867, 883, 0.000001, '867 -> 883', '2025-05-20 15:22:17', 1),
(865, '-', 869, 773, 1, '869 -> 773', '2025-05-20 15:22:17', 1),
(866, '-', 870, 774, 1, '870 -> 774', '2025-05-20 15:22:17', 1),
(867, '-', 870, 875, 1, '870 -> 875', '2025-05-20 15:22:17', 1),
(868, '-', 870, 876, 1, '870 -> 876', '2025-05-20 15:22:17', 1),
(869, '-', 871, 486, 1, '871 -> 486', '2025-05-20 15:22:17', 1),
(870, '-', 872, 353, 1, '872 -> 353', '2025-05-20 15:22:17', 1),
(871, '-', 872, 486, 1, '872 -> 486', '2025-05-20 15:22:17', 1),
(872, '-', 873, 376, 1, '873 -> 376', '2025-05-20 15:22:17', 1),
(873, '-', 873, 773, 1, '873 -> 773', '2025-05-20 15:22:17', 1),
(874, '-', 874, 376, 1, '874 -> 376', '2025-05-20 15:22:17', 1),
(875, '-', 874, 773, 1, '874 -> 773', '2025-05-20 15:22:17', 1),
(876, '-', 875, 774, 1, '875 -> 774', '2025-05-20 15:22:17', 1),
(877, '-', 875, 870, 1, '875 -> 870', '2025-05-20 15:22:17', 1),
(878, '-', 876, 774, 1, '876 -> 774', '2025-05-20 15:22:17', 1),
(879, '-', 876, 870, 1, '876 -> 870', '2025-05-20 15:22:17', 1),
(880, '-', 877, 188, 1, '877 -> 188', '2025-05-20 15:22:17', 1),
(881, '-', 877, 440, 1, '877 -> 440', '2025-05-20 15:22:17', 1),
(882, '-', 883, 371, 1000000, '883 -> 371', '2025-05-20 15:22:17', 1),
(883, '-', 883, 867, 1000000, '883 -> 867', '2025-05-20 15:22:17', 1),
(884, '-', 885, 899, 0.000001, '885 -> 899', '2025-05-20 15:22:17', 1),
(885, '-', 886, 345, 1, '886 -> 345', '2025-05-20 15:22:17', 1),
(886, '-', 886, 397, 0.000001, '886 -> 397', '2025-05-20 15:22:17', 1),
(887, '-', 887, 345, 1000000, '887 -> 345', '2025-05-20 15:22:17', 1),
(888, '-', 887, 886, 1000000, '887 -> 886', '2025-05-20 15:22:17', 1),
(889, '-', 888, 482, 1, '888 -> 482', '2025-05-20 15:22:17', 1),
(890, '-', 889, 482, 1, '889 -> 482', '2025-05-20 15:22:17', 1),
(891, '-', 890, 482, 1, '890 -> 482', '2025-05-20 15:22:17', 1),
(892, '-', 891, 161, 1, '891 -> 161', '2025-05-20 15:22:17', 1),
(893, '-', 892, 810, 1000000000, '892 -> 810', '2025-05-20 15:22:17', 1),
(894, '-', 892, 811, 1000000, '892 -> 811', '2025-05-20 15:22:17', 1),
(895, '-', 892, 812, 1000, '892 -> 812', '2025-05-20 15:22:17', 1),
(896, '-', 895, 351, 1, '895 -> 351', '2025-05-20 15:22:17', 1),
(897, '-', 895, 374, 1, '895 -> 374', '2025-05-20 15:22:17', 1),
(898, '-', 895, 482, 1, '895 -> 482', '2025-05-20 15:22:17', 1),
(899, '-', 896, 374, 1, '896 -> 374', '2025-05-20 15:22:17', 1),
(900, '-', 896, 482, 1, '896 -> 482', '2025-05-20 15:22:17', 1),
(901, '-', 897, 351, 1, '897 -> 351', '2025-05-20 15:22:17', 1),
(902, '-', 897, 374, 1, '897 -> 374', '2025-05-20 15:22:17', 1),
(903, '-', 897, 482, 1, '897 -> 482', '2025-05-20 15:22:17', 1),
(904, '-', 898, 798, 1000000, '898 -> 798', '2025-05-20 15:22:17', 1),
(905, '-', 899, 885, 1000000, '899 -> 885', '2025-05-20 15:22:17', 1),
(906, '-', 901, 807, 0.001, '901 -> 807', '2025-05-20 15:22:17', 1),
(907, '-', 901, 810, 1000000, '901 -> 810', '2025-05-20 15:22:17', 1),
(908, '-', 901, 811, 1000, '901 -> 811', '2025-05-20 15:22:17', 1),
(909, '-', 905, 798, 100000000, '905 -> 798', '2025-05-20 15:22:17', 1),
(910, '-', 906, 885, 100000000, '906 -> 885', '2025-05-20 15:22:17', 1),
(911, '-', 914, 483, 1, '914 -> 483', '2025-05-20 15:22:17', 1),
(912, '-', 915, 784, 1000, '915 -> 784', '2025-05-20 15:22:17', 1),
(913, '-', 915, 847, 1000000000, '915 -> 847', '2025-05-20 15:22:17', 1),
(914, '-', 916, 684, 1000, '916 -> 684', '2025-05-20 15:22:17', 1),
(915, '-', 916, 798, 1000000000, '916 -> 798', '2025-05-20 15:22:17', 1),
(916, '-', 918, 482, 1, '918 -> 482', '2025-05-20 15:22:17', 1),
(917, '-', 919, 482, 1, '919 -> 482', '2025-05-20 15:22:17', 1),
(918, '-', 920, 482, 1, '920 -> 482', '2025-05-20 15:22:17', 1),
(919, '-', 921, 482, 1, '921 -> 482', '2025-05-20 15:22:17', 1),
(920, '-', 922, 482, 1, '922 -> 482', '2025-05-20 15:22:17', 1),
(921, '-', 923, 482, 1, '923 -> 482', '2025-05-20 15:22:17', 1),
(922, '-', 924, 482, 1, '924 -> 482', '2025-05-20 15:22:17', 1),
(923, '-', 925, 482, 1, '925 -> 482', '2025-05-20 15:22:17', 1),
(924, '-', 926, 138, 1, '926 -> 138', '2025-05-20 15:22:17', 1),
(925, '-', 926, 380, 5, '926 -> 380', '2025-05-20 15:22:17', 1),
(926, '-', 927, 144, 1, '927 -> 144', '2025-05-20 15:22:17', 1),
(927, '-', 927, 380, 15, '927 -> 380', '2025-05-20 15:22:17', 1),
(928, '-', 928, 140, 1, '928 -> 140', '2025-05-20 15:22:17', 1),
(929, '-', 928, 378, 1, '928 -> 378', '2025-05-20 15:22:17', 1),
(930, '-', 928, 407, 1, '928 -> 407', '2025-05-20 15:22:17', 1),
(931, '-', 928, 482, 1, '928 -> 482', '2025-05-20 15:22:17', 1),
(932, '-', 928, 929, 1, '928 -> 929', '2025-05-20 15:22:17', 1),
(933, '-', 929, 378, 1, '929 -> 378', '2025-05-20 15:22:17', 1),
(934, '-', 929, 407, 1, '929 -> 407', '2025-05-20 15:22:17', 1),
(935, '-', 929, 482, 1, '929 -> 482', '2025-05-20 15:22:17', 1),
(936, '-', 929, 928, 1, '929 -> 928', '2025-05-20 15:22:17', 1),
(937, '-', 930, 389, 1, '930 -> 389', '2025-05-20 15:22:17', 1),
(938, '-', 930, 482, 1, '930 -> 482', '2025-05-20 15:22:17', 1),
(939, '-', 931, 482, 1, '931 -> 482', '2025-05-20 15:22:17', 1),
(940, '-', 932, 482, 1, '932 -> 482', '2025-05-20 15:22:17', 1),
(941, '-', 933, 482, 1, '933 -> 482', '2025-05-20 15:22:17', 1),
(942, '-', 934, 482, 1, '934 -> 482', '2025-05-20 15:22:17', 1),
(943, '-', 935, 377, 1, '935 -> 377', '2025-05-20 15:22:17', 1),
(944, '-', 935, 482, 1, '935 -> 482', '2025-05-20 15:22:17', 1),
(945, '-', 936, 482, 1, '936 -> 482', '2025-05-20 15:22:17', 1),
(946, '-', 937, 482, 1, '937 -> 482', '2025-05-20 15:22:17', 1),
(947, '-', 938, 482, 1, '938 -> 482', '2025-05-20 15:22:17', 1),
(948, '-', 939, 482, 1, '939 -> 482', '2025-05-20 15:22:17', 1),
(949, '-', 940, 482, 1, '940 -> 482', '2025-05-20 15:22:17', 1),
(950, '-', 941, 482, 1, '941 -> 482', '2025-05-20 15:22:17', 1),
(951, '-', 943, 482, 1, '943 -> 482', '2025-05-20 15:22:17', 1),
(952, '-', 944, 367, 1, '944 -> 367', '2025-05-20 15:22:17', 1),
(953, '-', 944, 368, 0.001, '944 -> 368', '2025-05-20 15:22:17', 1),
(954, '-', 945, 358, 1, '945 -> 358', '2025-05-20 15:22:17', 1),
(955, '-', 945, 390, 0.001, '945 -> 390', '2025-05-20 15:22:17', 1),
(956, '-', 946, 359, 1, '946 -> 359', '2025-05-20 15:22:17', 1),
(957, '-', 946, 483, 1, '946 -> 483', '2025-05-20 15:22:17', 1),
(958, '-', 946, 947, 1, '946 -> 947', '2025-05-20 15:22:17', 1),
(959, '-', 947, 359, 1, '947 -> 359', '2025-05-20 15:22:17', 1),
(960, '-', 947, 360, 1, '947 -> 360', '2025-05-20 15:22:17', 1),
(961, '-', 947, 483, 1, '947 -> 483', '2025-05-20 15:22:17', 1),
(962, '-', 947, 946, 1, '947 -> 946', '2025-05-20 15:22:17', 1),
(963, '-', 947, 964, 1, '947 -> 964', '2025-05-20 15:22:17', 1),
(964, '-', 948, 483, 1, '948 -> 483', '2025-05-20 15:22:17', 1),
(965, '-', 949, 483, 1, '949 -> 483', '2025-05-20 15:22:17', 1),
(966, '-', 950, 483, 1, '950 -> 483', '2025-05-20 15:22:17', 1),
(967, '-', 951, 483, 1, '951 -> 483', '2025-05-20 15:22:17', 1),
(968, '-', 952, 381, 1, '952 -> 381', '2025-05-20 15:22:17', 1),
(969, '-', 952, 483, 1, '952 -> 483', '2025-05-20 15:22:17', 1),
(970, '-', 953, 483, 1, '953 -> 483', '2025-05-20 15:22:17', 1),
(971, '-', 954, 483, 1, '954 -> 483', '2025-05-20 15:22:17', 1),
(972, '-', 955, 483, 1, '955 -> 483', '2025-05-20 15:22:17', 1),
(973, '-', 956, 483, 1, '956 -> 483', '2025-05-20 15:22:17', 1),
(974, '-', 957, 483, 1, '957 -> 483', '2025-05-20 15:22:17', 1),
(975, '-', 958, 483, 1, '958 -> 483', '2025-05-20 15:22:17', 1),
(976, '-', 959, 483, 1, '959 -> 483', '2025-05-20 15:22:17', 1),
(977, '-', 960, 483, 1, '960 -> 483', '2025-05-20 15:22:17', 1),
(978, '-', 961, 483, 1, '961 -> 483', '2025-05-20 15:22:17', 1),
(979, '-', 962, 483, 1, '962 -> 483', '2025-05-20 15:22:17', 1),
(980, '-', 963, 483, 1, '963 -> 483', '2025-05-20 15:22:17', 1),
(981, '-', 964, 359, 1, '964 -> 359', '2025-05-20 15:22:17', 1),
(982, '-', 964, 360, 1, '964 -> 360', '2025-05-20 15:22:17', 1),
(983, '-', 964, 483, 1, '964 -> 483', '2025-05-20 15:22:17', 1),
(984, '-', 964, 947, 1, '964 -> 947', '2025-05-20 15:22:17', 1),
(985, '-', 965, 483, 1, '965 -> 483', '2025-05-20 15:22:17', 1),
(986, '-', 966, 361, 15, '966 -> 361', '2025-05-20 15:22:17', 1),
(987, '-', 966, 967, 1, '966 -> 967', '2025-05-20 15:22:17', 1),
(988, '-', 967, 361, 15, '967 -> 361', '2025-05-20 15:22:17', 1),
(989, '-', 967, 966, 1, '967 -> 966', '2025-05-20 15:22:17', 1),
(990, '-', 968, 798, 1000000, '968 -> 798', '2025-05-20 15:22:17', 1),
(991, '-', 969, 885, 1000000, '969 -> 885', '2025-05-20 15:22:17', 1),
(992, '-', 973, 867, 100000000, '973 -> 867', '2025-05-20 15:22:17', 1),
(993, '-', 974, 867, 1000000000, '974 -> 867', '2025-05-20 15:22:17', 1),
(994, '-', 979, 975, 1000000, '979 -> 975', '2025-05-20 15:22:17', 1),
(995, '-', 980, 976, 1000000, '980 -> 976', '2025-05-20 15:22:17', 1),
(996, '-', 981, 483, 1, '981 -> 483', '2025-05-20 15:22:17', 1),
(997, '-', 982, 358, 28349.523125, '982 -> 358', '2025-05-20 15:22:17', 1),
(998, '-', 982, 390, 28.349523, '982 -> 390', '2025-05-20 15:22:17', 1),
(999, '-', 983, 361, 5, '983 -> 361', '2025-05-20 15:22:17', 1),
(1000, '-', 984, 798, 1000000000, '984 -> 798', '2025-05-20 15:22:17', 1),
(1001, '-', 988, 798, 1000000000, '988 -> 798', '2025-05-20 15:22:17', 1),
(1002, '-', 989, 482, 1, '989 -> 482', '2025-05-20 15:22:17', 1),
(1003, '-', 990, 482, 1, '990 -> 482', '2025-05-20 15:22:17', 1),
(1004, '-', 991, 482, 1, '991 -> 482', '2025-05-20 15:22:17', 1),
(1005, '-', 992, 482, 1, '992 -> 482', '2025-05-20 15:22:17', 1),
(1006, '-', 993, 482, 1, '993 -> 482', '2025-05-20 15:22:17', 1),
(1007, '-', 994, 482, 1, '994 -> 482', '2025-05-20 15:22:17', 1),
(1008, '-', 995, 482, 1, '995 -> 482', '2025-05-20 15:22:17', 1),
(1009, '-', 996, 482, 1, '996 -> 482', '2025-05-20 15:22:17', 1),
(1010, '-', 997, 482, 1, '997 -> 482', '2025-05-20 15:22:17', 1),
(1011, '-', 998, 482, 1, '998 -> 482', '2025-05-20 15:22:17', 1),
(1012, '-', 999, 482, 1, '999 -> 482', '2025-05-20 15:22:17', 1),
(1013, '-', 1000, 482, 1, '1000 -> 482', '2025-05-20 15:22:17', 1),
(1014, '-', 1001, 482, 1, '1001 -> 482', '2025-05-20 15:22:17', 1),
(1015, '-', 1002, 482, 1, '1002 -> 482', '2025-05-20 15:22:17', 1),
(1016, '-', 1003, 482, 1, '1003 -> 482', '2025-05-20 15:22:17', 1),
(1017, '-', 1004, 483, 1, '1004 -> 483', '2025-05-20 15:22:17', 1),
(1018, '-', 1005, 483, 1, '1005 -> 483', '2025-05-20 15:22:17', 1),
(1019, '-', 1006, 483, 1, '1006 -> 483', '2025-05-20 15:22:17', 1),
(1020, '-', 1007, 483, 1, '1007 -> 483', '2025-05-20 15:22:17', 1),
(1021, '-', 1008, 483, 1, '1008 -> 483', '2025-05-20 15:22:17', 1),
(1022, '-', 1009, 483, 1, '1009 -> 483', '2025-05-20 15:22:17', 1),
(1023, '-', 1010, 483, 1, '1010 -> 483', '2025-05-20 15:22:17', 1),
(1024, '-', 1011, 483, 1, '1011 -> 483', '2025-05-20 15:22:17', 1),
(1025, '-', 1012, 483, 1, '1012 -> 483', '2025-05-20 15:22:17', 1),
(1026, '-', 1013, 483, 1, '1013 -> 483', '2025-05-20 15:22:17', 1),
(1027, '-', 1014, 483, 1, '1014 -> 483', '2025-05-20 15:22:17', 1),
(1028, '-', 1015, 483, 1, '1015 -> 483', '2025-05-20 15:22:17', 1),
(1029, '-', 1016, 483, 1, '1016 -> 483', '2025-05-20 15:22:17', 1),
(1030, '-', 1017, 483, 1, '1017 -> 483', '2025-05-20 15:22:17', 1),
(1031, '-', 1018, 483, 1, '1018 -> 483', '2025-05-20 15:22:17', 1),
(1032, '-', 1019, 798, 1000000, '1019 -> 798', '2025-05-20 15:22:17', 1),
(1033, '-', 1020, 885, 1000000, '1020 -> 885', '2025-05-20 15:22:17', 1)
;
COMMIT;

-- Import complete: 2025-05-20T19:22:18.233Z
-- Total records processed: 1033
-- Duration: 0.51 seconds

-- Reset and update sequence to next available value
BEGIN;
ALTER SEQUENCE form_list_fdb_unit_conversion_id_seq RESTART WITH 1034;
SET session_replication_role = DEFAULT;
COMMIT;
