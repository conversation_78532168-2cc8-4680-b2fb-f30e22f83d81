-- SQL Generation started: 2025-05-20T19:22:43.208Z
-- Table: list_fdb_clin_effect
-- Total records: 14

-- Initial configuration
BEGIN;
SET session_replication_role = replica;
SET maintenance_work_mem = '1GB';
SET synchronous_commit = off;
SET work_mem = '2GB';
SET temp_buffers = '1GB';
SET statement_timeout = 0;

-- Clean table
TRUNCATE TABLE form_list_fdb_clin_effect;
COMMIT;

-- Beginning data import in batches of 50000 records
-- Each batch will have its own transaction
BEGIN;
INSERT INTO form_list_fdb_clin_effect (id, code, adi_efftc, adi_efftxt, auto_name, created_on, created_by) VALUES
(1, NULL, 'ADD', 'Additive side effects from both drugs', 'ADD Additive side effects from both drugs', '2025-05-20 15:22:43', 1),
(2, NULL, 'ARF', 'Adverse reaction of the former drug', 'ARF Adverse reaction of the former drug', '2025-05-20 15:22:43', 1),
(3, NULL, 'ARL', 'Adverse reaction of the latter drug', 'ARL Adverse reaction of the latter drug', '2025-05-20 15:22:43', 1),
(4, NULL, 'AVD', 'Avoid concurrent use when possible', 'AVD Avoid concurrent use when possible', '2025-05-20 15:22:43', 1),
(5, NULL, 'CEE', 'Conflicting evidence exists in medical literature', 'CEE Conflicting evidence exists in medical literature', '2025-05-20 15:22:43', 1),
(6, NULL, 'CIS', 'Contraindicated in some patients', 'CIS Contraindicated in some patients', '2025-05-20 15:22:43', 1),
(7, NULL, 'DEF', 'Decreased effect of the former drug', 'DEF Decreased effect of the former drug', '2025-05-20 15:22:43', 1),
(8, NULL, 'DEL', 'Decreased effect of the latter drug', 'DEL Decreased effect of the latter drug', '2025-05-20 15:22:43', 1),
(9, NULL, 'INF', 'Increased effect of the former drug', 'INF Increased effect of the former drug', '2025-05-20 15:22:43', 1),
(10, NULL, 'INL', 'Increased effect of the latter drug', 'INL Increased effect of the latter drug', '2025-05-20 15:22:43', 1),
(11, NULL, 'LBC', 'Labeling conflicts between countries or products', 'LBC Labeling conflicts between countries or products', '2025-05-20 15:22:43', 1),
(12, NULL, 'MAR', 'Adverse reaction with both drugs', 'MAR Adverse reaction with both drugs', '2025-05-20 15:22:43', 1),
(13, NULL, 'MXF', 'Mixed effects of the former drug', 'MXF Mixed effects of the former drug', '2025-05-20 15:22:43', 1),
(14, NULL, 'MXL', 'Mixed effects of the latter drug', 'MXL Mixed effects of the latter drug', '2025-05-20 15:22:43', 1)
;
COMMIT;

-- Import complete: 2025-05-20T19:22:44.232Z
-- Total records processed: 14
-- Duration: 1.03 seconds

-- Reset and update sequence to next available value
BEGIN;
ALTER SEQUENCE form_list_fdb_clin_effect_id_seq RESTART WITH 15;
SET session_replication_role = DEFAULT;
COMMIT;
