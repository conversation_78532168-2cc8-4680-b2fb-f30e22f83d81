-- SQL Generation started: 2025-05-20T19:21:48.939Z
-- Table: list_fdb_price_qty_uom
-- Total records: 48

-- Initial configuration
BEGIN;
SET session_replication_role = replica;
SET maintenance_work_mem = '1GB';
SET synchronous_commit = off;
SET work_mem = '2GB';
SET temp_buffers = '1GB';
SET statement_timeout = 0;

-- Clean table
TRUNCATE TABLE form_list_fdb_price_qty_uom;
COMMIT;

-- Beginning data import in batches of 50000 records
-- Each batch will have its own transaction
BEGIN;
INSERT INTO form_list_fdb_price_qty_uom (id, code, price_uom_id, price_uom_desc, auto_name, created_on, created_by) VALUES
(1, NULL, 1, 'Aerosol, Breath Activated', '1 - Aerosol, Breath Activated', '2025-05-20 15:21:49', 1),
(2, NULL, 2, 'Aerosol, Refill Unit', '2 - Aerosol, Refill Unit', '2025-05-20 15:21:49', 1),
(3, NULL, 3, 'Aerosol With Adapter', '3 - Aerosol With Adapter', '2025-05-20 15:21:49', 1),
(4, NULL, 4, 'Ampul With Device', '4 - Ampul With Device', '2025-05-20 15:21:49', 1),
(5, NULL, 5, 'Ampul', '5 - Ampul', '2025-05-20 15:21:49', 1),
(6, NULL, 6, 'Ampul/Kit', '6 - Ampul/Kit', '2025-05-20 15:21:49', 1),
(7, NULL, 7, 'Bag', '7 - Bag', '2025-05-20 15:21:49', 1),
(8, NULL, 8, 'Blister Pack', '8 - Blister Pack', '2025-05-20 15:21:49', 1),
(9, NULL, 9, 'Bottle', '9 - Bottle', '2025-05-20 15:21:49', 1),
(10, NULL, 10, 'Box', '10 - Box', '2025-05-20 15:21:49', 1),
(11, NULL, 11, 'Can', '11 - Can', '2025-05-20 15:21:49', 1),
(12, NULL, 12, 'Canister', '12 - Canister', '2025-05-20 15:21:49', 1),
(13, NULL, 13, 'Canister, Refill Unit', '13 - Canister, Refill Unit', '2025-05-20 15:21:49', 1),
(14, NULL, 14, 'Cartridge', '14 - Cartridge', '2025-05-20 15:21:49', 1),
(15, NULL, 15, 'Cup, Unit Dose', '15 - Cup, Unit Dose', '2025-05-20 15:21:49', 1),
(16, NULL, 16, 'Dispenser', '16 - Dispenser', '2025-05-20 15:21:49', 1),
(17, NULL, 17, 'Dispensing Pack', '17 - Dispensing Pack', '2025-05-20 15:21:49', 1),
(18, NULL, 18, 'Dropper Bottle', '18 - Dropper Bottle', '2025-05-20 15:21:49', 1),
(19, NULL, 19, 'Drum', '19 - Drum', '2025-05-20 15:21:49', 1),
(20, NULL, 20, 'Flexible Container', '20 - Flexible Container', '2025-05-20 15:21:49', 1),
(21, NULL, 21, 'Glass Container', '21 - Glass Container', '2025-05-20 15:21:49', 1),
(22, NULL, 22, 'Jar', '22 - Jar', '2025-05-20 15:21:49', 1),
(23, NULL, 23, 'Kit', '23 - Kit', '2025-05-20 15:21:49', 1),
(24, NULL, 24, 'Kit-Refill', '24 - Kit-Refill', '2025-05-20 15:21:49', 1),
(25, NULL, 25, 'Not Applicable', '25 - Not Applicable', '2025-05-20 15:21:49', 1),
(26, NULL, 26, 'Package', '26 - Package', '2025-05-20 15:21:49', 1),
(27, NULL, 27, 'Packet', '27 - Packet', '2025-05-20 15:21:49', 1),
(28, NULL, 28, 'Applicator, Pre-Filled', '28 - Applicator, Pre-Filled', '2025-05-20 15:21:49', 1),
(29, NULL, 29, 'Roll', '29 - Roll', '2025-05-20 15:21:49', 1),
(30, NULL, 30, 'Solution Bottle', '30 - Solution Bottle', '2025-05-20 15:21:49', 1),
(31, NULL, 31, 'Squeeze Bottle', '31 - Squeeze Bottle', '2025-05-20 15:21:49', 1),
(32, NULL, 32, 'Syringe', '32 - Syringe', '2025-05-20 15:21:49', 1),
(33, NULL, 33, 'Tine', '33 - Tine', '2025-05-20 15:21:49', 1),
(34, NULL, 34, 'Tube', '34 - Tube', '2025-05-20 15:21:49', 1),
(35, NULL, 35, 'Tube Stick', '35 - Tube Stick', '2025-05-20 15:21:49', 1),
(36, NULL, 36, 'Tube/Jar', '36 - Tube/Jar', '2025-05-20 15:21:49', 1),
(37, NULL, 37, 'Tube/Kit', '37 - Tube/Kit', '2025-05-20 15:21:49', 1),
(38, NULL, 38, 'Vial', '38 - Vial', '2025-05-20 15:21:49', 1),
(39, NULL, 39, 'Vial/Kit', '39 - Vial/Kit', '2025-05-20 15:21:49', 1),
(40, NULL, 40, 'Each', '40 - Each', '2025-05-20 15:21:49', 1),
(41, NULL, 41, 'Milliliter', '41 - Milliliter', '2025-05-20 15:21:49', 1),
(42, NULL, 42, 'Gram', '42 - Gram', '2025-05-20 15:21:49', 1),
(43, NULL, 43, 'Not Available', '43 - Not Available', '2025-05-20 15:21:49', 1),
(44, NULL, 44, 'Capsule', '44 - Capsule', '2025-05-20 15:21:49', 1),
(45, NULL, 45, 'Suppository', '45 - Suppository', '2025-05-20 15:21:49', 1),
(46, NULL, 46, 'Tablet', '46 - Tablet', '2025-05-20 15:21:49', 1),
(47, NULL, 47, 'Transdermal Patch', '47 - Transdermal Patch', '2025-05-20 15:21:49', 1),
(48, NULL, 48, 'Injectable Anti-Hemophilic Factor', '48 - Injectable Anti-Hemophilic Factor', '2025-05-20 15:21:49', 1)
;
COMMIT;

-- Import complete: 2025-05-20T19:21:49.960Z
-- Total records processed: 48
-- Duration: 1.02 seconds

-- Reset and update sequence to next available value
BEGIN;
ALTER SEQUENCE form_list_fdb_price_qty_uom_id_seq RESTART WITH 49;
SET session_replication_role = DEFAULT;
COMMIT;
