-- SQL Generation started: 2025-05-20T19:22:41.613Z
-- Table: list_fdb_sever_level
-- Total records: 9

-- Initial configuration
BEGIN;
SET session_replication_role = replica;
SET maintenance_work_mem = '1GB';
SET synchronous_commit = off;
SET work_mem = '2GB';
SET temp_buffers = '1GB';
SET statement_timeout = 0;

-- Clean table
TRUNCATE TABLE form_list_fdb_sever_level;
COMMIT;

-- Beginning data import in batches of 50000 records
-- Each batch will have its own transaction
BEGIN;
INSERT INTO form_list_fdb_sever_level (id, code, ddi_sl, ddi_slsn, ddi_sltxt, auto_name, created_on, created_by) VALUES
(1, '1-1', '1', 1, 'Contraindicated Drug Combination: This drug combination is', '1 1 Contraindicated Drug Combination: This drug combination is', '2025-05-20 15:22:41', 1),
(2, '1-2', '1', 2, 'contraindicated and generally should not be dispensed or administered', '1 2 contraindicated and generally should not be dispensed or administered', '2025-05-20 15:22:41', 1),
(3, '1-3', '1', 3, 'to the same patient.', '1 3 to the same patient.', '2025-05-20 15:22:41', 1),
(4, '2-1', '2', 1, 'Severe Interaction: Action is required to reduce the risk of severe', '2 1 Severe Interaction: Action is required to reduce the risk of severe', '2025-05-20 15:22:41', 1),
(5, '2-2', '2', 2, 'adverse interaction.', '2 2 adverse interaction.', '2025-05-20 15:22:41', 1),
(6, '3-1', '3', 1, 'Moderate Interaction: Assess the risk to the patient and take action', '3 1 Moderate Interaction: Assess the risk to the patient and take action', '2025-05-20 15:22:41', 1),
(7, '3-2', '3', 2, 'as needed.', '3 2 as needed.', '2025-05-20 15:22:41', 1),
(8, '9-1', '9', 1, 'Undetermined Severity - Alternative Therapy Interaction: Assess the', '9 1 Undetermined Severity - Alternative Therapy Interaction: Assess the', '2025-05-20 15:22:41', 1),
(9, '9-2', '9', 2, 'risk to the patient and take action as needed.', '9 2 risk to the patient and take action as needed.', '2025-05-20 15:22:41', 1)
;
COMMIT;

-- Import complete: 2025-05-20T19:22:42.635Z
-- Total records processed: 9
-- Duration: 1.02 seconds

-- Reset and update sequence to next available value
BEGIN;
ALTER SEQUENCE form_list_fdb_sever_level_id_seq RESTART WITH 10;
SET session_replication_role = DEFAULT;
COMMIT;
