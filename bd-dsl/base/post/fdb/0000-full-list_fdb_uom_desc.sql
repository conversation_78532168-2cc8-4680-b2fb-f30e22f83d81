-- SQL Generation started: 2025-05-20T19:22:20.098Z
-- Table: list_fdb_uom_desc
-- Total records: 13

-- Initial configuration
BEGIN;
SET session_replication_role = replica;
SET maintenance_work_mem = '1GB';
SET synchronous_commit = off;
SET work_mem = '2GB';
SET temp_buffers = '1GB';
SET statement_timeout = 0;

-- Clean table
TRUNCATE TABLE form_list_fdb_uom_desc;
COMMIT;

-- Beginning data import in batches of 50000 records
-- Each batch will have its own transaction
BEGIN;
INSERT INTO form_list_fdb_uom_desc (id, code, uom_type_cd, uom_type_cd_desc, auto_name, created_on, created_by) VALUES
(1, NULL, 1, 'Weight', 'Weight', '2025-05-20 15:22:20', 1),
(2, NULL, 2, 'Volume', 'Volume', '2025-05-20 15:22:20', 1),
(3, NULL, 3, 'Time', 'Time', '2025-05-20 15:22:20', 1),
(4, NULL, 4, 'Quantity', 'Quantity', '2025-05-20 15:22:20', 1),
(5, NULL, 5, 'Length', 'Length', '2025-05-20 15:22:20', 1),
(6, NULL, 6, 'Area', 'Area', '2025-05-20 15:22:20', 1),
(7, NULL, 7, 'Concentration', 'Concentration', '2025-05-20 15:22:20', 1),
(8, NULL, 8, 'Radiation', 'Radiation', '2025-05-20 15:22:20', 1),
(9, NULL, 9, 'Energy', 'Energy', '2025-05-20 15:22:20', 1),
(10, NULL, 10, 'Pressure', 'Pressure', '2025-05-20 15:22:20', 1),
(11, NULL, 11, 'Protective Factor', 'Protective Factor', '2025-05-20 15:22:20', 1),
(12, NULL, 12, 'Viscosity', 'Viscosity', '2025-05-20 15:22:20', 1),
(13, NULL, 13, 'Fluorescence', 'Fluorescence', '2025-05-20 15:22:20', 1)
;
COMMIT;

-- Import complete: 2025-05-20T19:22:21.121Z
-- Total records processed: 13
-- Duration: 1.02 seconds

-- Reset and update sequence to next available value
BEGIN;
ALTER SEQUENCE form_list_fdb_uom_desc_id_seq RESTART WITH 14;
SET session_replication_role = DEFAULT;
COMMIT;
