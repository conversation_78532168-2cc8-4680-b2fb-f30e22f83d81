-- SQL Generation started: 2025-05-20T19:22:18.247Z
-- Table: list_fdb_ingred_strength
-- Total records: 74

-- Initial configuration
BEGIN;
SET session_replication_role = replica;
SET maintenance_work_mem = '1GB';
SET synchronous_commit = off;
SET work_mem = '2GB';
SET temp_buffers = '1GB';
SET statement_timeout = 0;

-- Clean table
TRUNCATE TABLE form_list_fdb_ingred_strength;
COMMIT;

-- Beginning data import in batches of 50000 records
-- Each batch will have its own transaction
BEGIN;
INSERT INTO form_list_fdb_ingred_strength (id, code, uom_id, uom_desc, uom_abbr, uom_preferred_desc, auto_name, created_on, created_by) VALUES
(1, '1', 1, 'milligram', 'mg', 'mg', 'milligram - mg', '2025-05-20 15:22:18', 1),
(2, '2', 2, 'milliliter', 'mL', 'mL', 'milliliter - mL', '2025-05-20 15:22:18', 1),
(3, '3', 3, 'gram', 'g', 'gram', 'gram - g', '2025-05-20 15:22:18', 1),
(4, '4', 4, 'microgram', 'mcg', 'mcg', 'microgram - mcg', '2025-05-20 15:22:18', 1),
(5, '6', 6, 'liter', 'L', 'L', 'liter - L', '2025-05-20 15:22:18', 1),
(6, '7', 7, 'Percent', '%', '%', 'Percent - %', '2025-05-20 15:22:18', 1),
(7, '8', 8, 'millimole', 'mmol', 'mmol', 'millimole - mmol', '2025-05-20 15:22:18', 1),
(8, '9', 9, 'milliequivalent', 'mEq', 'mEq', 'milliequivalent - mEq', '2025-05-20 15:22:18', 1),
(9, '10', 10, 'unit', 'unit', 'unit', 'unit - unit', '2025-05-20 15:22:18', 1),
(10, '21', 21, 'milliosmole', 'mOsm', 'mOsm', 'milliosmole - mOsm', '2025-05-20 15:22:18', 1),
(11, '22', 22, 'millicurie', 'mCi', 'mCi', 'millicurie - mCi', '2025-05-20 15:22:18', 1),
(12, '23', 23, 'milligrams, phenytoin equivalents', 'mg PE', 'mg PE', 'milligrams, phenytoin equivalents - mg PE', '2025-05-20 15:22:18', 1),
(13, '24', 24, 'hour', 'hr', 'hour', 'hour - hr', '2025-05-20 15:22:18', 1),
(14, '25', 25, 'cell', 'cell', 'cell', 'cell - cell', '2025-05-20 15:22:18', 1),
(15, '26', 26, 'Lf unit', 'Lf unit', 'Lf unit', 'Lf unit - Lf unit', '2025-05-20 15:22:18', 1),
(16, '27', 27, 'kilogram', 'kg', 'kg', 'kilogram - kg', '2025-05-20 15:22:18', 1),
(17, '28', 28, 'french', 'Fr', 'Fr', 'french - Fr', '2025-05-20 15:22:18', 1),
(18, '29', 29, 'minim', 'minim', 'minim', 'minim - minim', '2025-05-20 15:22:18', 1),
(19, '30', 30, 'scoop', 'scoop', 'scoop', 'scoop - scoop', '2025-05-20 15:22:18', 1),
(20, '31', 31, 'centimeter', 'cm', 'cm', 'centimeter - cm', '2025-05-20 15:22:18', 1),
(21, '33', 33, 'dose', 'dose', 'dose', 'dose - dose', '2025-05-20 15:22:18', 1),
(22, '34', 34, 'drop', 'drop', 'drop', 'drop - drop', '2025-05-20 15:22:18', 1),
(23, '35', 35, 'foot', 'foot', 'foot', 'foot - foot', '2025-05-20 15:22:18', 1),
(24, '36', 36, 'bead', 'bead', 'bead', 'bead - bead', '2025-05-20 15:22:18', 1),
(25, '37', 37, 'curie', 'curie', 'curie', 'curie - curie', '2025-05-20 15:22:18', 1),
(26, '38', 38, 'markers', 'markers', 'markers', 'markers - markers', '2025-05-20 15:22:18', 1),
(27, '39', 39, 'square cm', 'cm2', 'cm2', 'square cm - cm2', '2025-05-20 15:22:18', 1),
(28, '40', 40, 'tuberculin unit', 'tub unit', 'tub. unit', 'tuberculin unit - tub unit', '2025-05-20 15:22:18', 1),
(29, '41', 41, 'gigabecquerel', 'GBq', 'GBq', 'gigabecquerel - GBq', '2025-05-20 15:22:18', 1),
(30, '42', 42, 'Kyowa unit', 'KY unit', 'KY unit', 'Kyowa unit - KY unit', '2025-05-20 15:22:18', 1),
(31, '43', 43, 'mole', 'mol', 'mol', 'mole - mol', '2025-05-20 15:22:18', 1),
(32, '44', 44, 'becquerel', 'Bq', 'Bq', 'becquerel - Bq', '2025-05-20 15:22:18', 1),
(33, '45', 45, 'kilobecquerel', 'kBq', 'kBq', 'kilobecquerel - kBq', '2025-05-20 15:22:18', 1),
(34, '46', 46, 'megabecquerel', 'MBq', 'MBq', 'megabecquerel - MBq', '2025-05-20 15:22:18', 1),
(35, '47', 47, 'nanocurie', 'nCi', 'nCi', 'nanocurie - nCi', '2025-05-20 15:22:18', 1),
(36, '48', 48, 'equivalent', 'Eq', 'Eq', 'equivalent - Eq', '2025-05-20 15:22:18', 1),
(37, '49', 49, 'Percent Weight in Volume', '% (w/v)', '% (w/v)', 'Percent Weight in Volume - % (w/v)', '2025-05-20 15:22:18', 1),
(38, '50', 50, 'Percent Weight in Weight', '% (w/w)', '% (w/w)', 'Percent Weight in Weight - % (w/w)', '2025-05-20 15:22:18', 1),
(39, '51', 51, 'inch', '"', 'inch', 'inch - "', '2025-05-20 15:22:18', 1),
(40, '52', 52, 'pollen unit', 'pn unit', 'pollen unit', 'pollen unit - pn unit', '2025-05-20 15:22:18', 1),
(41, '53', 53, 'nanogram', 'ng', 'nanogram', 'nanogram - ng', '2025-05-20 15:22:18', 1),
(42, '54', 54, 'millimeter', 'mm', 'mm', 'millimeter - mm', '2025-05-20 15:22:18', 1),
(43, '55', 55, 'minute', 'min', 'minute', 'minute - min', '2025-05-20 15:22:18', 1),
(44, '56', 56, 'second', 'sec', 'second', 'second - sec', '2025-05-20 15:22:18', 1),
(45, '57', 57, 'square meter', 'm2', 'm2', 'square meter - m2', '2025-05-20 15:22:18', 1),
(46, '58', 58, 'gauge', 'gauge', 'gauge', 'gauge - gauge', '2025-05-20 15:22:18', 1),
(47, '59', 59, 'Sun Protection Factor', 'SPF', 'SPF', 'Sun Protection Factor - SPF', '2025-05-20 15:22:18', 1),
(48, '60', 60, 'microliter', 'microliter', 'microliter', 'microliter - microliter', '2025-05-20 15:22:18', 1),
(49, '61', 61, 'spray', 'spray', 'spray', 'spray - spray', '2025-05-20 15:22:18', 1),
(50, '62', 62, 'ounce', 'oz', 'ounce', 'ounce - oz', '2025-05-20 15:22:18', 1),
(51, '63', 63, 'yard', 'yd', 'yard', 'yard - yd', '2025-05-20 15:22:18', 1),
(52, '64', 64, 'square inch', 'in2', 'square inch', 'square inch - in2', '2025-05-20 15:22:18', 1),
(53, '65', 65, 'micromole', 'mcmol', 'micromole', 'micromole - mcmol', '2025-05-20 15:22:18', 1),
(54, '66', 66, 'millipascal seconds', 'mPas', 'mPas', 'millipascal seconds - mPas', '2025-05-20 15:22:18', 1),
(55, '67', 67, 'centistokes', 'cSt', 'centistokes', 'centistokes - cSt', '2025-05-20 15:22:18', 1),
(56, '68', 68, 'bioequivalent allergy unit (BAU)', 'BAU', 'BA unit', 'bioequivalent allergy unit (BAU) - BAU', '2025-05-20 15:22:18', 1),
(57, '69', 69, 'Parts per million', 'PPM', 'PPM', 'Parts per million - PPM', '2025-05-20 15:22:18', 1),
(58, '70', 70, 'ELISA unit', 'ELISA unit', 'ELISA unit', 'ELISA unit - ELISA unit', '2025-05-20 15:22:18', 1),
(59, '71', 71, 'TCID50', 'TCID50', 'TCID50', 'TCID50 - TCID50', '2025-05-20 15:22:18', 1),
(60, '72', 72, 'Colony Forming Unit', 'CFU', 'CFU', 'Colony Forming Unit - CFU', '2025-05-20 15:22:18', 1),
(61, '73', 73, 'cup', 'cp', 'cup', 'cup - cp', '2025-05-20 15:22:18', 1),
(62, '74', 74, 'kilocalories', 'kcal', 'kcal', 'kilocalories - kcal', '2025-05-20 15:22:18', 1),
(63, '75', 75, 'histamine equivalent in prick testing', 'HEP', 'HEP', 'histamine equivalent in prick testing - HEP', '2025-05-20 15:22:18', 1),
(64, '76', 76, 'index of reactivity (IR)', 'IR', 'IR', 'index of reactivity (IR) - IR', '2025-05-20 15:22:18', 1),
(65, '77', 77, 'square millimeter', 'mm2', 'mm2', 'square millimeter - mm2', '2025-05-20 15:22:18', 1),
(66, '78', 78, 'Amb a 1 unit', 'Amb a 1-U', 'Amb a 1 unit', 'Amb a 1 unit - Amb a 1-U', '2025-05-20 15:22:18', 1),
(67, '79', 79, 'genome copies', 'gc', 'gc', 'genome copies - gc', '2025-05-20 15:22:18', 1),
(68, '80', 80, 'day', 'day', 'day', 'day - day', '2025-05-20 15:22:18', 1),
(69, '81', 81, 'plaque forming unit', 'PFU', 'PFU', 'plaque forming unit - PFU', '2025-05-20 15:22:18', 1),
(70, '82', 82, 'standardized quality-house dust mite', 'SQ-HDM', 'SQ-HDM', 'standardized quality-house dust mite - SQ-HDM', '2025-05-20 15:22:18', 1),
(71, '83', 83, 'vector genomes', 'vg', 'vg', 'vector genomes - vg', '2025-05-20 15:22:18', 1),
(72, '84', 84, 'week', 'week', 'week', 'week - week', '2025-05-20 15:22:18', 1),
(73, '85', 85, 'micrograms dietary folate equivalents', 'mcg DFE', 'mcg DFE', 'micrograms dietary folate equivalents - mcg DFE', '2025-05-20 15:22:18', 1),
(74, '86', 86, 'standardized quality-Betula verrucosa', 'SQ-Bet', 'SQ-Bet', 'standardized quality-Betula verrucosa - SQ-Bet', '2025-05-20 15:22:18', 1)
;
COMMIT;

-- Import complete: 2025-05-20T19:22:19.270Z
-- Total records processed: 74
-- Duration: 1.03 seconds

-- Reset and update sequence to next available value
BEGIN;
ALTER SEQUENCE form_list_fdb_ingred_strength_id_seq RESTART WITH 75;
SET session_replication_role = DEFAULT;
COMMIT;
