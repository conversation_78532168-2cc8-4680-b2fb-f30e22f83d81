CREATE OR R<PERSON>LACE FUNCTION assert_equals(expected TEXT, actual TEXT, field_name TEXT)
RETURNS VOID AS $$
BEGIN
    IF expected IS DISTINCT FROM actual THEN
        RAISE EXCEPTION 'Assertion failed for %: Expected "%", but got "%"', field_name, expected, actual;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION assert_not_null(actual TEXT, field_name TEXT)
RETURNS VOID AS $$
BEGIN
    IF actual IS NULL THEN
        RAISE EXCEPTION 'Assertion failed for %: Expected not NULL, but got NULL', field_name;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION assert_null(actual TEXT, field_name TEXT)
RETURNS VOID AS $$
BEGIN
    IF actual IS NOT NULL THEN
        RAISE EXCEPTION 'Assertion failed for %: Expected NULL, but got "%"', field_name, actual;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION assert_array_length_equals(actual ANYARRAY, expected_length INTEGER, field_name TEXT)
RETURNS VOID AS $$
DECLARE
    actual_length INTEGER;
BEGIN
    actual_length := array_length(actual, 1);
    IF actual_length IS DISTINCT FROM expected_length THEN
        RAISE EXCEPTION 'Assertion failed for % array length: Expected %, but got %', field_name, expected_length, actual_length;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION assert_text_array_equals(expected TEXT[], actual TEXT[], field_name TEXT)
RETURNS VOID AS $$
BEGIN
    IF NOT (expected <@ actual AND expected @> actual) THEN
        RAISE EXCEPTION 'Assertion failed for %: Expected "%", but got "%"', field_name, array_to_string(expected, ', '), array_to_string(actual, ', ');
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION assert_json_equals(expected JSONB, actual JSONB, message TEXT DEFAULT '')
RETURNS VOID AS $$
DECLARE
    diffs JSONB := '[]'::jsonb;
    queue JSONB[] := ARRAY[jsonb_build_object(
        'path', '',
        'expected', expected,
        'actual', actual
    )];
    q_idx INT := 1;
    current JSONB;
    cur_path TEXT;
    cur_expected JSONB;
    cur_actual JSONB;
    i INT;
    arr_len INT;
    key TEXT;
    keys TEXT[];
BEGIN
    WHILE q_idx <= array_length(queue, 1) LOOP
        current := queue[q_idx];
        q_idx := q_idx + 1;

        cur_path := current->>'path';
        cur_expected := current->'expected';
        cur_actual := current->'actual';

        -- Handle objects (but only if at least one side is an object)
        IF jsonb_typeof(cur_expected) = 'object' OR jsonb_typeof(cur_actual) = 'object' THEN
            -- Gather all unique keys in both objects (protect against scalars)
            keys := ARRAY(
                SELECT DISTINCT k.key
                FROM (
                    SELECT all_keys.key FROM (
                        SELECT unnest(
                            CASE WHEN jsonb_typeof(cur_expected) = 'object'
                                 THEN ARRAY(SELECT jsonb_object_keys(cur_expected))
                                 ELSE ARRAY[]::text[] END
                        ) AS key
                        UNION
                        SELECT unnest(
                            CASE WHEN jsonb_typeof(cur_actual) = 'object'
                                 THEN ARRAY(SELECT jsonb_object_keys(cur_actual))
                                 ELSE ARRAY[]::text[] END
                        ) AS key
                    ) all_keys
                ) k
            );
            FOREACH key IN ARRAY keys LOOP
                queue := array_append(queue, jsonb_build_object(
                    'path', CASE WHEN cur_path = '' THEN key ELSE cur_path || '.' || key END,
                    'expected', cur_expected -> key,
                    'actual', cur_actual -> key
                ));
            END LOOP;

        -- Handle arrays
        ELSIF jsonb_typeof(cur_expected) = 'array' OR jsonb_typeof(cur_actual) = 'array' THEN
            arr_len := GREATEST(
                CASE WHEN jsonb_typeof(cur_expected) = 'array' THEN COALESCE(jsonb_array_length(cur_expected), 0) ELSE 0 END,
                CASE WHEN jsonb_typeof(cur_actual) = 'array' THEN COALESCE(jsonb_array_length(cur_actual), 0) ELSE 0 END
            );
            FOR i IN 0..(arr_len - 1) LOOP
                queue := array_append(queue, jsonb_build_object(
                    'path', CASE WHEN cur_path = '' THEN i::text ELSE cur_path || '.' || i::text END,
                    'expected', cur_expected -> i,
                    'actual', cur_actual -> i
                ));
            END LOOP;

        -- Leaf value comparison
        ELSE
            IF cur_expected IS DISTINCT FROM cur_actual THEN
                diffs := diffs || jsonb_build_array(jsonb_build_object(
                    'path', cur_path,
                    'expected', cur_expected,
                    'actual', cur_actual
                ));
            END IF;
        END IF;
    END LOOP;

    IF jsonb_array_length(diffs) > 0 THEN
        RAISE EXCEPTION 'JSON Assertion Failed %: %', message, jsonb_pretty(diffs);
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION assert_json_exists(p_test_name text, p_json json, p_path text)
RETURNS VOID AS $$
BEGIN
    IF p_json #>> string_to_array(p_path, '.') IS NULL THEN
        RAISE EXCEPTION 'ASSERTION FAILED: % - Path % not found in JSON', p_test_name, p_path;
    ELSE
        RAISE NOTICE 'ASSERTION PASSED: %', p_test_name;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION assert_numeric_equals(p_test_name text, p_expected numeric, p_actual numeric)
RETURNS VOID AS $$
BEGIN
    IF p_expected IS DISTINCT FROM p_actual THEN
        RAISE EXCEPTION 'ASSERTION FAILED: % - Expected: %, Actual: %', p_test_name, p_expected, p_actual;
    ELSE
        RAISE NOTICE 'ASSERTION PASSED: %', p_test_name;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create additional assert functions
CREATE OR REPLACE FUNCTION assert_record_exists(
    p_table_name TEXT,
    p_where_clause TEXT,
    p_test_name TEXT
) RETURNS VOID AS $$
DECLARE
    v_count INTEGER;
    v_sql TEXT;
BEGIN
    v_sql := 'SELECT COUNT(*) FROM ' || p_table_name || ' WHERE ' || p_where_clause;
    EXECUTE v_sql INTO v_count;
    
    IF v_count = 0 THEN
        RAISE EXCEPTION 'ASSERTION FAILED: % - No record found in % where %', 
                        p_test_name, p_table_name, p_where_clause;
    ELSE
        RAISE NOTICE 'ASSERTION PASSED: % - Found % record(s)', p_test_name, v_count;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION assert_record_count(
    p_table_name TEXT,
    p_where_clause TEXT,
    p_expected_count INTEGER,
    p_test_name TEXT
) RETURNS VOID AS $$
DECLARE
    v_count INTEGER;
    v_sql TEXT;
BEGIN
    v_sql := 'SELECT COUNT(*) FROM ' || p_table_name || ' WHERE ' || p_where_clause;
    EXECUTE v_sql INTO v_count;
    
    IF v_count != p_expected_count THEN
        RAISE EXCEPTION 'ASSERTION FAILED: % - Expected % records in % where %, but found %', 
                        p_test_name, p_expected_count, p_table_name, p_where_clause, v_count;
    ELSE
        RAISE NOTICE 'ASSERTION PASSED: % - Found expected % record(s)', p_test_name, v_count;
    END IF;
END;
$$ LANGUAGE plpgsql;