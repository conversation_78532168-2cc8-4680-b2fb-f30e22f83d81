-- Test Setup for Medical Claim Response Processing
-- This file creates test data and helper functions

-- Create test schema if needed
DO $$
BEGIN
    -- Ensure we have necessary extensions
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
EXCEPTION WHEN OTHERS THEN
    -- Extension might already exist
    NULL;
END $$;

CREATE OR REPLACE FUNCTION create_test_insurance(p_patient_id INTEGER, p_payer_id INTEGER, p_member_no TEXT) RETURNS INTEGER AS $func$
DECLARE
    v_insurance_id INTEGER;
    v_patient_insurance_id INTEGER;
BEGIN
   
    -- Create patient insurance link
    INSERT INTO form_patient_insurance (
        patient_id,
        payer_id,
        payer_level,
        rank,
        cardholder_id,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        p_patient_id,
        p_payer_id,
        'Primary',
        1,
        p_member_no,
        CURRENT_TIMESTAMP,
        1,
        FALSE,
        FALSE
    ) RETURNING id INTO v_patient_insurance_id;
    
    RETURN v_patient_insurance_id;
END;
$func$ LANGUAGE plpgsql;

-- Helper function to create test inventory with optional NDC
CREATE OR REPLACE FUNCTION create_test_inventory(
    p_hcpc_code TEXT, 
    p_description TEXT,
    p_ndc TEXT DEFAULT NULL
) RETURNS INTEGER AS $func$
DECLARE
    v_inventory_id INTEGER;
BEGIN
    INSERT INTO form_inventory (
        hcpc_code,
        description,
        ndc,
        type,
        active,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        p_hcpc_code,
        p_description,
        p_ndc,
        'Drug',
        'Yes',
        CURRENT_TIMESTAMP,
        1,
        FALSE,
        FALSE
    ) RETURNING id INTO v_inventory_id;
    
    RETURN v_inventory_id;
END;
$func$ LANGUAGE plpgsql;

-- Helper function to create test patient
CREATE OR REPLACE FUNCTION create_test_patient(
    p_patient_name TEXT DEFAULT 'Test Patient'
) RETURNS INTEGER AS $$
DECLARE
    v_patient_id INTEGER;
BEGIN
    INSERT INTO form_patient (
        mrn,
        firstname,
        lastname,
        created_by,
        created_on,
        archived,
        deleted
    ) VALUES (
        'TEST-' || TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'),
        split_part(p_patient_name, ' ', 1),
        split_part(p_patient_name, ' ', 2),
        1,
        CURRENT_TIMESTAMP,
        FALSE,
        FALSE
    ) RETURNING id INTO v_patient_id;
    
    RETURN v_patient_id;
END;
$$ LANGUAGE plpgsql;

-- Helper function to create test site
CREATE OR REPLACE FUNCTION create_test_site(
    p_site_name TEXT DEFAULT 'Test Site'
) RETURNS INTEGER AS $$
DECLARE
    v_site_id INTEGER;
BEGIN
    INSERT INTO form_site (
        name,
        created_by,
        created_on,
        archived,
        deleted
    ) VALUES (
        p_site_name,
        1,
        CURRENT_TIMESTAMP,
        FALSE,
        FALSE
    ) RETURNING id INTO v_site_id;
    
    RETURN v_site_id;
END;
$$ LANGUAGE plpgsql;

-- Helper function to create test payer
CREATE OR REPLACE FUNCTION create_test_payer(
    p_payer_name TEXT DEFAULT 'Test Payer',
    p_payer_id_text TEXT DEFAULT 'TEST123'
) RETURNS INTEGER AS $$
DECLARE
    v_payer_id INTEGER;
BEGIN
    INSERT INTO form_payer (
        organization,
        mm_payer_name,
        created_by,
        created_on,
        archived,
        deleted
    ) VALUES (
        p_payer_name,
        p_payer_id_text,
        1,
        CURRENT_TIMESTAMP,
        FALSE,
        FALSE
    ) RETURNING id INTO v_payer_id;
    
    RETURN v_payer_id;
END;
$$ LANGUAGE plpgsql;

-- Helper function to create test invoice
CREATE OR REPLACE FUNCTION create_test_invoice(
    p_patient_id INTEGER,
    p_site_id INTEGER,
    p_invoice_no TEXT DEFAULT NULL
) RETURNS TEXT AS $$
DECLARE
    v_invoice_no TEXT;
BEGIN
    v_invoice_no := COALESCE(p_invoice_no, 'INV-' || TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'));
    RAISE NOTICE 'Creating invoice with invoice_no: %', v_invoice_no;
    INSERT INTO form_billing_invoice (
        invoice_no,
        master_invoice_no,
        patient_id,
        site_id,
        total_billed,
        total_insurance_paid,
        total_pt_pay,
        status,
        billing_method_id,
        date_of_service,
        billed_datetime,
        insurance_id,
        payer_id,
        void,
        zeroed,
        created_by,
        created_on,
        archived,
        deleted
    ) VALUES (
        v_invoice_no,
        v_invoice_no,
        p_patient_id,
        p_site_id,
        1000.00,
        0.00,
        0.00,
        'Open',
        'mm',
        CURRENT_DATE,
        CURRENT_TIMESTAMP,
        1, -- Dummy insurance_id
        1, -- Dummy payer_id
        'No',
        'No',
        1,
        CURRENT_TIMESTAMP,
        FALSE,
        FALSE
    );
    
    RETURN v_invoice_no;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION create_test_diagnosis(p_patient_id INTEGER, p_code TEXT, p_description TEXT) RETURNS INTEGER AS $func$
DECLARE
    v_dx_id INTEGER;
    v_patient_dx_id INTEGER;
BEGIN
    -- Create or get diagnosis from list
    INSERT INTO form_list_diagnosis (
        code,
        name,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        p_code,
        p_description,
        CURRENT_TIMESTAMP,
        1,
        FALSE,
        FALSE
    ) ON CONFLICT (code) WHERE deleted IS NOT TRUE 
    DO UPDATE SET name = EXCLUDED.name
    RETURNING id INTO v_dx_id;
    
    -- Create patient diagnosis
    INSERT INTO form_patient_diagnosis (
        patient_id,
        dx_id,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        p_patient_id,
        v_dx_id,
        CURRENT_TIMESTAMP,
        1,
        FALSE,
        FALSE
    ) RETURNING id INTO v_patient_dx_id;
    
    RETURN v_patient_dx_id;
END;
$func$ LANGUAGE plpgsql;

-- Helper function to create test medical claim
CREATE OR REPLACE FUNCTION create_test_medical_claim(
    p_patient_id INTEGER,
    p_site_id INTEGER,
    p_payer_id INTEGER,
    p_invoice_no TEXT,
    p_patient_control_number TEXT DEFAULT NULL,
    p_trading_partner_claim_number TEXT DEFAULT NULL
) RETURNS TEXT AS $$
DECLARE
    v_claim_id INTEGER;
    v_claim_no_uuid TEXT;
    v_patient_control_number TEXT;
    v_trading_partner_claim_number TEXT;
BEGIN
    -- Generate unique control numbers if not provided
    v_patient_control_number := COALESCE(p_patient_control_number, 'PCN-' || TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'));
    v_trading_partner_claim_number := COALESCE(p_trading_partner_claim_number, 'TCN-' || TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'));
    
    -- Generate UUID for claim_no
    v_claim_no_uuid := uuid_generate_v4()::TEXT;
    
    -- Insert into form_med_claim
    INSERT INTO form_med_claim (
        claim_no,
        patient_id,
        site_id,
        payer_id,
        invoice_no,
        status,
        paid,
        copay,
        void,
        created_by,
        created_on,
        archived,
        deleted
    ) VALUES (
        v_claim_no_uuid,
        p_patient_id,
        p_site_id,
        p_payer_id,
        p_invoice_no,
        'Submitted',
        0.00,
        0.00,
        'No',
        1,
        CURRENT_TIMESTAMP,
        FALSE,
        FALSE
    ) RETURNING id INTO v_claim_id;
    
    -- Insert into form_med_claim_info
    INSERT INTO form_med_claim_info (
        id,
        patient_control_number,
        claim_frequency_code,
        created_by,
        created_on,
        archived,
        deleted
    ) VALUES (
        v_claim_id,
        v_patient_control_number,
        '1', -- Original claim
        1,
        CURRENT_TIMESTAMP,
        FALSE,
        FALSE
    );
    
    -- Create linking record between claim and claim info
    INSERT INTO sf_form_med_claim_to_med_claim_info (
        form_med_claim_fk,
        form_med_claim_info_fk,
        delete,
        archive
    ) VALUES (
        v_claim_id,
        v_claim_id,  -- claim_info.id is same as claim.id in this case
        FALSE,
        FALSE
    );
    
    -- Insert into form_med_claim_supplemental
    INSERT INTO form_med_claim_supplemental (
        claim_number,
        claim_control_number,
        created_by,
        created_on,
        archived,
        deleted
    ) VALUES (
        v_claim_id::TEXT,
        v_trading_partner_claim_number, -- Store TCN here instead
        1,
        CURRENT_TIMESTAMP,
        FALSE,
        FALSE
    );
    
    RETURN v_claim_no_uuid;
END;
$$ LANGUAGE plpgsql;

-- Helper function to create test service line
CREATE OR REPLACE FUNCTION create_test_service_line(
    p_claim_no TEXT,
    p_line_item_control_number TEXT,
    p_procedure_code TEXT DEFAULT '99213',
    p_charge_amount NUMERIC DEFAULT 100.00,
    p_units NUMERIC DEFAULT 1
) RETURNS INTEGER AS $$
DECLARE
    v_service_line_id INTEGER;
    v_professional_service_id INTEGER;
BEGIN
    -- Insert into form_med_claim_sl
    INSERT INTO form_med_claim_sl (
        claim_no,
        provider_control_number,
        line_item_charge_amount,
        service_unit_count,
        measurement_unit,
        service_date,
        assigned_number,
        created_by,
        created_on,
        archived,
        deleted
    ) VALUES (
        p_claim_no,
        p_line_item_control_number,
        p_charge_amount,
        p_units,
        'UN', -- Units
        CURRENT_DATE,
        1, -- Default line number
        1,
        CURRENT_TIMESTAMP,
        FALSE,
        FALSE
    ) RETURNING id INTO v_service_line_id;
    
    -- Insert into form_med_claim_sv (professional service subform)
    INSERT INTO form_med_claim_sv (
        procedure_code,
        created_by,
        created_on,
        archived,
        deleted
    ) VALUES (
        p_procedure_code,
        1,
        CURRENT_TIMESTAMP,
        FALSE,
        FALSE
    ) RETURNING id INTO v_professional_service_id;
    
    -- Link the professional service to the service line
    INSERT INTO sf_form_med_claim_sl_to_med_claim_sv (
        form_med_claim_sl_fk,
        form_med_claim_sv_fk,
        archive,
        delete
    ) VALUES (
        v_service_line_id,
        v_professional_service_id,
        FALSE,
        FALSE
    );
    
    RETURN v_service_line_id;
END;
$$ LANGUAGE plpgsql;

-- Helper function to insert response log entry
CREATE OR REPLACE FUNCTION insert_test_response_log(
    p_response_type TEXT,
    p_response_json JSONB
) RETURNS INTEGER AS $$
DECLARE
    v_response_id INTEGER;
BEGIN
    INSERT INTO form_med_claim_resp_log (
        response_type,
        response_raw_json,
        created_by,
        created_on,
        archived,
        deleted
    ) VALUES (
        p_response_type,
        p_response_json::TEXT,
        1,
        CURRENT_TIMESTAMP,
        FALSE,
        FALSE
    ) RETURNING id INTO v_response_id;
    
    RETURN v_response_id;
END;
$$ LANGUAGE plpgsql;

-- Helper assertion functions for testing
DROP FUNCTION IF EXISTS assert_not_null(TEXT, TEXT);
DROP FUNCTION IF EXISTS assert_equals(TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS assert_numeric_equals(TEXT, NUMERIC, NUMERIC);
DROP FUNCTION IF EXISTS assert_null(TEXT, TEXT);
DROP FUNCTION IF EXISTS assert_record_exists(TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS assert_record_count(TEXT, TEXT, INTEGER, TEXT);

CREATE OR REPLACE FUNCTION assert_not_null(
    p_value TEXT,
    p_description TEXT
) RETURNS VOID AS $$
BEGIN
    IF p_value IS NULL THEN
        RAISE EXCEPTION 'Assertion failed: % should not be null', p_description;
    END IF;
    RAISE NOTICE '✅ %: %', p_description, p_value;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION assert_equals(
    p_expected TEXT,
    p_actual TEXT,
    p_description TEXT
) RETURNS VOID AS $$
BEGIN
    IF p_expected IS DISTINCT FROM p_actual THEN
        RAISE EXCEPTION 'Assertion failed: % - Expected: %, Actual: %', p_description, p_expected, p_actual;
    END IF;
    RAISE NOTICE '✅ %: %', p_description, p_actual;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION assert_numeric_equals(
    p_description TEXT,
    p_expected NUMERIC,
    p_actual NUMERIC
) RETURNS VOID AS $$
BEGIN
    IF p_expected IS DISTINCT FROM p_actual THEN
        RAISE EXCEPTION 'Assertion failed: % - Expected: %, Actual: %', p_description, p_expected, p_actual;
    END IF;
    RAISE NOTICE '✅ %: %', p_description, p_actual;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION assert_null(
    p_value TEXT,
    p_description TEXT
) RETURNS VOID AS $$
BEGIN
    IF p_value IS NOT NULL THEN
        RAISE EXCEPTION 'Assertion failed: % should be null but was: %', p_description, p_value;
    END IF;
    RAISE NOTICE '✅ %: NULL (as expected)', p_description;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION assert_record_exists(
    p_table_expression TEXT,
    p_where_clause TEXT,
    p_description TEXT
) RETURNS VOID AS $$
DECLARE
    v_count INTEGER;
    v_sql TEXT;
BEGIN
    v_sql := format('SELECT COUNT(*) FROM %s WHERE %s', p_table_expression, p_where_clause);
    EXECUTE v_sql INTO v_count;
    
    IF v_count = 0 THEN
        RAISE EXCEPTION 'Assertion failed: % - No records found', p_description;
    END IF;
    RAISE NOTICE '✅ %: % record(s) found', p_description, v_count;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION assert_record_count(
    p_table_expression TEXT,
    p_where_clause TEXT,
    p_expected_count INTEGER,
    p_description TEXT
) RETURNS VOID AS $$
DECLARE
    v_count INTEGER;
    v_sql TEXT;
BEGIN
    v_sql := format('SELECT COUNT(*) FROM %s WHERE %s', p_table_expression, p_where_clause);
    EXECUTE v_sql INTO v_count;
    
    IF v_count != p_expected_count THEN
        RAISE EXCEPTION 'Assertion failed: % - Expected: % records, Found: %', p_description, p_expected_count, v_count;
    END IF;
    RAISE NOTICE '✅ %: % record(s) found', p_description, v_count;
END;
$$ LANGUAGE plpgsql;

-- Helper function to cleanup test data
CREATE OR REPLACE FUNCTION cleanup_test_data(
    p_test_prefix TEXT DEFAULT 'TEST-'
) RETURNS VOID AS $$
BEGIN
    -- Step 1: Delete 999 response data
    DELETE FROM sf_form_med_claim_resp_to_med_claim_resp_ref
    WHERE form_med_claim_resp_fk IN (
        SELECT id FROM form_med_claim_resp 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_resp_to_med_claim_resp_err
    WHERE form_med_claim_resp_fk IN (
        SELECT id FROM form_med_claim_resp 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_resp_to_med_claim_resp_edit
    WHERE form_med_claim_resp_fk IN (
        SELECT id FROM form_med_claim_resp 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_resp_to_med_claim_resp_fail
    WHERE form_med_claim_resp_fk IN (
        SELECT id FROM form_med_claim_resp 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_resp_to_med_claim_resp_ch_pyr
    WHERE form_med_claim_resp_fk IN (
        SELECT id FROM form_med_claim_resp 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_resp_to_med_claim_resp_ch_meta
    WHERE form_med_claim_resp_fk IN (
        SELECT id FROM form_med_claim_resp 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM form_med_claim_resp_ref WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_err WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_edit WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_fail WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_ch_pyr WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_ch_meta WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    
    -- Step 2: Delete 277 response data
    DELETE FROM sf_form_med_claim_resp_277_to_med_claim_resp_py_cont
    WHERE form_med_claim_resp_277_fk IN (
        SELECT id FROM form_med_claim_resp_277 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_resp_277_to_med_claim_resp_277_st_hst
    WHERE form_med_claim_resp_277_fk IN (
        SELECT id FROM form_med_claim_resp_277 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_resp_277_sl_to_med_claim_resp_277_sl_hst
    WHERE form_med_claim_resp_277_sl_fk IN (
        SELECT id FROM form_med_claim_resp_277_sl 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_resp_277_to_med_claim_resp_277_sl
    WHERE form_med_claim_resp_277_fk IN (
        SELECT id FROM form_med_claim_resp_277 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM form_med_claim_resp_277_sl_hst WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_277_sl WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_277_st_hst WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_py_cont WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_277 WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    
    -- Step 3: Delete 835 response data (in correct order)
    -- First delete all linking records
    DELETE FROM sf_form_med_claim_resp_835_sl_to_med_claim_resp_835_sl_adj
    WHERE form_med_claim_resp_835_sl_fk IN (
        SELECT id FROM form_med_claim_resp_835_sl 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM gr_form_med_claim_resp_835_sl_rmk_cd_to_list_med_claim_ecl_id
    WHERE form_med_claim_resp_835_sl_fk IN (
        SELECT id FROM form_med_claim_resp_835_sl 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_resp_835_to_med_claim_resp_835_sl
    WHERE form_med_claim_resp_835_fk IN (
        SELECT id FROM form_med_claim_resp_835 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_resp_835_to_med_claim_resp_835_adj
    WHERE form_med_claim_resp_835_fk IN (
        SELECT id FROM form_med_claim_resp_835 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM gr_form_med_claim_resp_835_rmk_cd_to_list_med_claim_ecl_id
    WHERE form_med_claim_resp_835_fk IN (
        SELECT id FROM form_med_claim_resp_835 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    -- Delete batch linking records
    DELETE FROM sf_form_med_claim_resp_835_batch_to_med_claim_resp_835_pcnt
    WHERE form_med_claim_resp_835_batch_fk IN (
        SELECT id FROM form_med_claim_resp_835_batch 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_resp_835_batch_to_med_claim_resp_835_prov_adj
    WHERE form_med_claim_resp_835_batch_fk IN (
        SELECT id FROM form_med_claim_resp_835_batch 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_resp_835_batch_to_med_claim_resp_835_btcm
    WHERE form_med_claim_resp_835_batch_fk IN (
        SELECT id FROM form_med_claim_resp_835_batch 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_resp_835_batch_to_med_claim_resp_835_bpmt
    WHERE form_med_claim_resp_835_batch_fk IN (
        SELECT id FROM form_med_claim_resp_835_batch 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_resp_835_batch_to_med_claim_resp_ch_meta
    WHERE form_med_claim_resp_835_batch_fk IN (
        SELECT id FROM form_med_claim_resp_835_batch 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    -- Now delete the actual records
    DELETE FROM form_med_claim_resp_835_sl_adj WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_835_sl WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_835_adj WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_835 WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_835_prov_adj WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_835_bpmt WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_835_btcm WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_835_pcnt WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_resp_835_batch WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    
    DELETE FROM form_med_claim_resp_log WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    
    -- Step 4: Delete medical claim subforms and linking records
    -- Delete service line linking records first
    DELETE FROM sf_form_med_claim_sl_to_med_claim_sv
    WHERE form_med_claim_sl_fk IN (
        SELECT sl.id FROM form_med_claim_sl sl
        WHERE sl.claim_no IN (
            SELECT claim_no FROM form_med_claim 
            WHERE created_by = 1 AND created_on >= CURRENT_DATE
        )
    );
    
    DELETE FROM sf_form_med_claim_sl_to_med_claim_dme
    WHERE form_med_claim_sl_fk IN (
        SELECT sl.id FROM form_med_claim_sl sl
        WHERE sl.claim_no IN (
            SELECT claim_no FROM form_med_claim 
            WHERE created_by = 1 AND created_on >= CURRENT_DATE
        )
    );
    
    DELETE FROM sf_form_med_claim_sl_to_med_claim_dme_cert
    WHERE form_med_claim_sl_fk IN (
        SELECT sl.id FROM form_med_claim_sl sl
        WHERE sl.claim_no IN (
            SELECT claim_no FROM form_med_claim 
            WHERE created_by = 1 AND created_on >= CURRENT_DATE
        )
    );
    
    DELETE FROM sf_form_med_claim_sl_to_med_claim_dme_cmn
    WHERE form_med_claim_sl_fk IN (
        SELECT sl.id FROM form_med_claim_sl sl
        WHERE sl.claim_no IN (
            SELECT claim_no FROM form_med_claim 
            WHERE created_by = 1 AND created_on >= CURRENT_DATE
        )
    );
    
    DELETE FROM sf_form_med_claim_sl_to_med_claim_sl_di
    WHERE form_med_claim_sl_fk IN (
        SELECT sl.id FROM form_med_claim_sl sl
        WHERE sl.claim_no IN (
            SELECT claim_no FROM form_med_claim 
            WHERE created_by = 1 AND created_on >= CURRENT_DATE
        )
    );
    
    DELETE FROM sf_form_med_claim_sl_to_med_claim_sl_dt
    WHERE form_med_claim_sl_fk IN (
        SELECT sl.id FROM form_med_claim_sl sl
        WHERE sl.claim_no IN (
            SELECT claim_no FROM form_med_claim 
            WHERE created_by = 1 AND created_on >= CURRENT_DATE
        )
    );
    
    DELETE FROM sf_form_med_claim_sl_to_med_claim_sl_ref
    WHERE form_med_claim_sl_fk IN (
        SELECT sl.id FROM form_med_claim_sl sl
        WHERE sl.claim_no IN (
            SELECT claim_no FROM form_med_claim 
            WHERE created_by = 1 AND created_on >= CURRENT_DATE
        )
    );
    
    -- Delete service line reference linking records
    DELETE FROM sf_form_med_claim_sl_ref_to_med_claim_sl_ref_pa
    WHERE form_med_claim_sl_ref_fk IN (
        SELECT id FROM form_med_claim_sl_ref
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    -- Delete adjustment linking records
    DELETE FROM sf_form_med_claim_adj_to_med_claim_adj_dl
    WHERE form_med_claim_adj_fk IN (
        SELECT id FROM form_med_claim_adj
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_osub_to_med_claim_adj
    WHERE form_med_claim_adj_fk IN (
        SELECT id FROM form_med_claim_adj
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_osub_to_med_claim_opayer
    WHERE form_med_claim_osub_fk IN (
        SELECT id FROM form_med_claim_osub
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    -- Delete gerund tables for claim
    DELETE FROM gr_form_med_claim_order_item_id_to_careplan_order_rx_id
    WHERE form_med_claim_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM gr_form_med_claim_rental_id_to_careplan_order_rental_id
    WHERE form_med_claim_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    -- Delete all linking records for form_med_claim
    DELETE FROM sf_form_med_claim_to_med_claim_address_pay
    WHERE form_med_claim_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_to_med_claim_dep
    WHERE form_med_claim_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_to_med_claim_info
    WHERE form_med_claim_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_to_med_claim_provs
    WHERE form_med_claim_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_to_med_claim_receiver
    WHERE form_med_claim_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_to_med_claim_submitter
    WHERE form_med_claim_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_to_med_claim_subscriber
    WHERE form_med_claim_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    -- Delete related professional services
    DELETE FROM form_med_claim_sv WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_dme_cert WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_dme_cmn WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    
    DELETE FROM form_med_claim_supplemental
    WHERE claim_number IN (
        SELECT id::TEXT FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM form_med_claim_sl
    WHERE claim_no IN (
        SELECT claim_no FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    -- Delete all linking records for form_med_claim_info
    DELETE FROM sf_form_med_claim_info_to_med_claim_dx
    WHERE form_med_claim_info_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_info_to_med_claim_facility
    WHERE form_med_claim_info_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_info_to_med_claim_info_other
    WHERE form_med_claim_info_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_info_to_med_claim_note
    WHERE form_med_claim_info_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_info_to_med_claim_osub
    WHERE form_med_claim_info_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_info_to_med_claim_reprice
    WHERE form_med_claim_info_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_info_to_med_claim_sl
    WHERE form_med_claim_info_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM sf_form_med_claim_info_to_med_claim_supplemental
    WHERE form_med_claim_info_fk IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM form_med_claim_info
    WHERE id IN (
        SELECT id FROM form_med_claim 
        WHERE created_by = 1 AND created_on >= CURRENT_DATE
    );
    
    DELETE FROM form_med_claim WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    
    -- Delete test invoices
    DELETE FROM form_billing_invoice 
    WHERE created_by = 1 
    AND created_on >= CURRENT_DATE
    AND invoice_no LIKE 'INV-%';
    
    -- Delete test entities
    DELETE FROM form_patient 
    WHERE (created_by = 1 
    AND created_on >= CURRENT_DATE
    AND firstname = 'Test')
    OR mrn LIKE 'TEST-%';
    
    DELETE FROM form_site 
    WHERE (created_by = 1 
    AND created_on >= CURRENT_DATE
    AND name LIKE 'Test%')
    OR name LIKE 'Test %';
    
    DELETE FROM form_payer 
    WHERE (created_by = 1 
    AND created_on >= CURRENT_DATE
    AND organization LIKE 'Test%')
    OR organization LIKE 'Test %';

    -- Delete additional medical claim related records
    DELETE FROM form_med_claim_adj_dl WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_adj WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_opayer WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_osub WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_sl_ref_pa WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_sl_ref WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_sl_dt WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_dme WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_sl_di WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_dx WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_contract WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_1500_dx WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_1500_sl WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_med_claim_1500 WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_patient WHERE mrn LIKE 'TEST-%';

    -- Delete additional related records
    DELETE FROM form_ledger_charge_line WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_patient_insurance WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_patient_diagnosis WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_list_diagnosis WHERE created_by = 1 AND created_on >= CURRENT_DATE;
    DELETE FROM form_inventory WHERE hcpc_code LIKE 'TEST-%' OR (created_by = 1 AND created_on >= CURRENT_DATE);
END;
$$ LANGUAGE plpgsql;
