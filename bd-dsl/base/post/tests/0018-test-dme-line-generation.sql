-- 0018-test-dme-line-generation.sql
-- Test DME (Durable Medical Equipment) charge line generation
-- Tests create_dme_rental_charge_line function and edge cases

DO $$
DECLARE
    -- Test data IDs
    v_test_site_id uuid;
    v_test_company_id uuid;
    v_test_payer_medicare_id uuid;
    v_test_payer_commercial_id uuid;
    v_test_payer_no_recurring_id uuid;
    v_test_patient_active_id uuid;
    v_test_patient_inactive_id uuid;
    v_test_patient_ins_medicare_id uuid;
    v_test_patient_ins_commercial_id uuid;
    v_test_patient_ins_no_recurring_id uuid;
    v_test_prescriber_id uuid;
    v_test_user_id uuid;
    v_test_inventory_cpap_id uuid;
    v_test_inventory_wheelchair_id uuid;
    v_test_inventory_oxygen_id uuid;
    v_test_serial_cpap_id uuid;
    v_test_serial_wheelchair_id uuid;
    v_test_serial_oxygen_id uuid;
    v_test_rental_cpap_id integer;
    v_test_rental_wheelchair_id integer;
    v_test_rental_oxygen_id integer;
    v_test_rental_inactive_id integer;
    v_test_rental_no_insurance_id integer;
    v_test_rental_no_recurring_id integer;
    
    -- Result variables
    v_charge_line_result charge_line_with_split;
    v_charge_line_record record;
    v_rental_view_record record;
    v_exclusion_reason text;
    
    -- Assertion helper
    v_assertion_failed boolean := false;
    
    -- Helper procedure for assertions
    PROCEDURE assert_equals(p_test_name text, p_expected text, p_actual text) IS
    BEGIN
        IF p_expected IS DISTINCT FROM p_actual THEN
            RAISE NOTICE 'ASSERTION FAILED: % - Expected: %, Actual: %', p_test_name, p_expected, p_actual;
            v_assertion_failed := true;
        ELSE
            RAISE NOTICE 'ASSERTION PASSED: %', p_test_name;
        END IF;
    END;
    
    PROCEDURE assert_not_null(p_test_name text, p_value text) IS
    BEGIN
        IF p_value IS NULL THEN
            RAISE NOTICE 'ASSERTION FAILED: % - Value is NULL', p_test_name;
            v_assertion_failed := true;
        ELSE
            RAISE NOTICE 'ASSERTION PASSED: %', p_test_name;
        END IF;
    END;
    
    PROCEDURE assert_null(p_test_name text, p_value text) IS
    BEGIN
        IF p_value IS NOT NULL THEN
            RAISE NOTICE 'ASSERTION FAILED: % - Expected NULL, got: %', p_test_name, p_value;
            v_assertion_failed := true;
        ELSE
            RAISE NOTICE 'ASSERTION PASSED: %', p_test_name;
        END IF;
    END;
    
    PROCEDURE assert_numeric_equals(p_test_name text, p_expected numeric, p_actual numeric) IS
    BEGIN
        IF p_expected IS DISTINCT FROM p_actual THEN
            RAISE NOTICE 'ASSERTION FAILED: % - Expected: %, Actual: %', p_test_name, p_expected, p_actual;
            v_assertion_failed := true;
        ELSE
            RAISE NOTICE 'ASSERTION PASSED: %', p_test_name;
        END IF;
    END;

BEGIN
    RAISE NOTICE 'Starting DME Charge Line Generation Test Suite';
    
    -- Create temporary billing error log table if it doesn't exist
    CREATE TEMP TABLE IF NOT EXISTS billing_error_log (
        id serial PRIMARY KEY,
        error_message text,
        error_context text,
        error_type text,
        schema_name text,
        table_name text,
        additional_details jsonb,
        created_at timestamp DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Generate test UUIDs
    v_test_site_id := gen_random_uuid();
    v_test_company_id := gen_random_uuid();
    v_test_payer_medicare_id := gen_random_uuid();
    v_test_payer_commercial_id := gen_random_uuid();
    v_test_payer_no_recurring_id := gen_random_uuid();
    v_test_patient_active_id := gen_random_uuid();
    v_test_patient_inactive_id := gen_random_uuid();
    v_test_patient_ins_medicare_id := gen_random_uuid();
    v_test_patient_ins_commercial_id := gen_random_uuid();
    v_test_patient_ins_no_recurring_id := gen_random_uuid();
    v_test_prescriber_id := gen_random_uuid();
    v_test_user_id := gen_random_uuid();
    v_test_inventory_cpap_id := gen_random_uuid();
    v_test_inventory_wheelchair_id := gen_random_uuid();
    v_test_inventory_oxygen_id := gen_random_uuid();
    v_test_serial_cpap_id := gen_random_uuid();
    v_test_serial_wheelchair_id := gen_random_uuid();
    v_test_serial_oxygen_id := gen_random_uuid();
    v_test_rental_cpap_id := gen_random_uuid();
    v_test_rental_wheelchair_id := gen_random_uuid();
    v_test_rental_oxygen_id := gen_random_uuid();
    v_test_rental_inactive_id := gen_random_uuid();
    v_test_rental_no_insurance_id := gen_random_uuid();
    v_test_rental_no_recurring_id := gen_random_uuid();
    
    -- Create test company
    INSERT INTO form_company (
        id, name, phone, fax, address1, city, state, zip, ein, created, modified
    ) VALUES (
        v_test_company_id, 'DME Test Company', '**********', '**********',
        '100 Equipment Blvd', 'DME City', 'OH', '44101', '33-4455667',
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- Create test site
    INSERT INTO form_site (
        id, name, company_id, npi, address1, city, state, zip, phone, fax,
        created, modified
    ) VALUES (
        v_test_site_id, 'DME Test Site', v_test_company_id, '**********',
        '200 Supply St', 'Supply City', 'OH', '44102', '**********', '**********',
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- Create test prescriber
    INSERT INTO form_physician (
        id, active, prefix, first_name, last_name, suffix, full_name,
        npi, dea, license, phone, fax, created, modified
    ) VALUES (
        v_test_prescriber_id, true, 'Dr.', 'DME', 'Prescriber', 'MD', 'Dr. DME Prescriber MD',
        '**********', '*********', 'OH123456', '**********', '**********',
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- Create payers with different DME settings
    -- Medicare payer (13 claim limit)
    INSERT INTO form_payer (
        id, active, name, insurance_type_id, payer_type_id, billing_method_id,
        cover_dme, max_rental_claims, bill_recur_upfront, bill_recur_arrears,
        no_recurring_billing, span_rental_dates, dme_frequency_code,
        address1, city, state, zip, created, modified
    ) VALUES (
        v_test_payer_medicare_id, true, 'Medicare DME', 'Medicare', 'MCRB', 'mm',
        'Yes', NULL, 'No', 'Yes', 'No', 'No', '1',
        '100 Medicare Way', 'Medicare City', 'DC', '20001',
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- Commercial payer (custom 24 claim limit)
    INSERT INTO form_payer (
        id, active, name, insurance_type_id, payer_type_id, billing_method_id,
        cover_dme, max_rental_claims, bill_recur_upfront, bill_recur_arrears,
        no_recurring_billing, span_rental_dates, dme_frequency_code,
        address1, city, state, zip, created, modified
    ) VALUES (
        v_test_payer_commercial_id, true, 'Commercial DME', 'Primary', 'Primary', 'mm',
        'Yes', 24, 'Yes', 'No', 'No', 'Yes', '1',
        '200 Insurance Ave', 'Insurance City', 'OH', '44103',
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- Payer with no recurring billing allowed
    INSERT INTO form_payer (
        id, active, name, insurance_type_id, payer_type_id, billing_method_id,
        cover_dme, no_recurring_billing, address1, city, state, zip,
        created, modified
    ) VALUES (
        v_test_payer_no_recurring_id, true, 'No Recurring DME', 'Primary', 'Primary', 'mm',
        'Yes', 'Yes', '300 One Time St', 'Single City', 'OH', '44104',
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- Create test patients
    -- Active patient
    INSERT INTO form_patient (
        id, active, status_id, chart_id, first_name, last_name,
        birth_date, gender, address1, city, state, zip,
        phone, site_id, created, modified
    ) VALUES (
        v_test_patient_active_id, true, '1', 'DME00001', 'Active', 'Patient',
        '1945-08-20', 'M', '400 Patient Way', 'Patient City', 'OH', '44105',
        '**********', v_test_site_id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- Inactive/discharged patient
    INSERT INTO form_patient (
        id, active, status_id, chart_id, first_name, last_name,
        birth_date, gender, address1, city, state, zip,
        phone, site_id, created, modified
    ) VALUES (
        v_test_patient_inactive_id, true, '2', 'DME00002', 'Inactive', 'Patient',
        '1950-03-15', 'F', '500 Former St', 'Past City', 'OH', '44106',
        '**********', v_test_site_id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- Create patient insurances
    -- Medicare insurance for active patient
    INSERT INTO form_patient_insurance (
        id, active, patient_id, payer_id, member_id, group_id,
        plan_name, coverage_order, relationship_id, effective_date,
        termination_date, created, modified
    ) VALUES (
        v_test_patient_ins_medicare_id, true, v_test_patient_active_id,
        v_test_payer_medicare_id, 'MCR123456789', 'MCRGRP',
        'Medicare Part B', 1, 'Self', '2020-01-01', NULL,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- Commercial insurance for inactive patient
    INSERT INTO form_patient_insurance (
        id, active, patient_id, payer_id, member_id, group_id,
        plan_name, coverage_order, relationship_id, effective_date,
        termination_date, created, modified
    ) VALUES (
        v_test_patient_ins_commercial_id, true, v_test_patient_inactive_id,
        v_test_payer_commercial_id, 'COM987654321', 'COMGRP',
        'PPO Plan', 1, 'Self', '2020-01-01', NULL,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- No recurring insurance for active patient
    INSERT INTO form_patient_insurance (
        id, active, patient_id, payer_id, member_id, group_id,
        plan_name, coverage_order, relationship_id, effective_date,
        termination_date, created, modified
    ) VALUES (
        v_test_patient_ins_no_recurring_id, true, v_test_patient_active_id,
        v_test_payer_no_recurring_id, 'NOR111222333', 'NORGRP',
        'One Time Plan', 2, 'Self', '2020-01-01', NULL,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- Create test user
    INSERT INTO form_user (
        id, username, active, first_name, last_name, created, modified
    ) VALUES (
        v_test_user_id, 'dmetestuser', true, 'DME', 'Tester',
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- Create DME inventory items
    INSERT INTO form_inventory (
        id, active, name, type, hcpc, billing_code_1, billing_code_2,
        rental_price, purchase_price, quantity_on_hand, unit_display,
        created, modified
    ) VALUES
    (v_test_inventory_cpap_id, true, 'CPAP Machine', 'Equipment Rental', 'E0601',
     'E0601', 'E0601-RR', 125.00, 1500.00, 50, 'EA',
     CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    (v_test_inventory_wheelchair_id, true, 'Manual Wheelchair', 'Equipment Rental', 'K0001',
     'K0001', 'K0001-RR', 80.00, 800.00, 25, 'EA',
     CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    (v_test_inventory_oxygen_id, true, 'Oxygen Concentrator', 'Equipment Rental', 'E1390',
     'E1390', 'E1390-RR', 200.00, 2500.00, 30, 'EA',
     CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
    
    -- Create serial numbers for equipment
    INSERT INTO form_inventory_serial (
        id, inventory_id, serial_no, status, created, modified
    ) VALUES
    (v_test_serial_cpap_id, v_test_inventory_cpap_id, 'CPAP-001', 'Available',
     CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    (v_test_serial_wheelchair_id, v_test_inventory_wheelchair_id, 'WC-001', 'Available',
     CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    (v_test_serial_oxygen_id, v_test_inventory_oxygen_id, 'O2-001', 'Available',
     CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
    
    -- First we need to create rental activity records for the DME rentals
    -- This simulates the equipment being checked out to patients
    
    -- Create rental activity for CPAP (Medicare patient)
    INSERT INTO form_ledger_rental_log_activity (
        serial_no, inventory_id, patient_id, previous_patient_id,
        localized_datetime, ticket_no, ticket_item_no,
        created, modified
    ) VALUES (
        'CPAP-001', v_test_inventory_cpap_id, v_test_patient_active_id, NULL,
        CURRENT_DATE - INTERVAL '60 days', 'TEST-CPAP-001', 1,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- Create rental log entry for CPAP
    INSERT INTO form_inventory_rental_log (
        patient_id, serial_no, inventory_id, ticket_no, ticket_item_no,
        checked_out_date, site_id, created, modified
    ) VALUES (
        v_test_patient_active_id, 'CPAP-001', v_test_inventory_cpap_id,
        'TEST-CPAP-001', 1, CURRENT_DATE - INTERVAL '60 days',
        v_test_site_id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    ) RETURNING id INTO v_test_rental_cpap_id;
    
    -- Create rental activity for wheelchair (inactive patient)
    INSERT INTO form_ledger_rental_log_activity (
        serial_no, inventory_id, patient_id, previous_patient_id,
        localized_datetime, ticket_no, ticket_item_no,
        created, modified
    ) VALUES (
        'WC-001', v_test_inventory_wheelchair_id, v_test_patient_inactive_id, NULL,
        CURRENT_DATE - INTERVAL '90 days', 'TEST-WC-001', 1,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- Create rental log entry for wheelchair
    INSERT INTO form_inventory_rental_log (
        patient_id, serial_no, inventory_id, ticket_no, ticket_item_no,
        checked_out_date, site_id, created, modified
    ) VALUES (
        v_test_patient_inactive_id, 'WC-001', v_test_inventory_wheelchair_id,
        'TEST-WC-001', 1, CURRENT_DATE - INTERVAL '90 days',
        v_test_site_id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    ) RETURNING id INTO v_test_rental_wheelchair_id;
    
    -- Create rental activity for oxygen (no recurring billing payer)
    INSERT INTO form_ledger_rental_log_activity (
        serial_no, inventory_id, patient_id, previous_patient_id,
        localized_datetime, ticket_no, ticket_item_no,
        created, modified
    ) VALUES (
        'O2-001', v_test_inventory_oxygen_id, v_test_patient_active_id, NULL,
        CURRENT_DATE - INTERVAL '30 days', 'TEST-O2-001', 1,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- Create rental log entry for oxygen
    INSERT INTO form_inventory_rental_log (
        patient_id, serial_no, inventory_id, ticket_no, ticket_item_no,
        checked_out_date, site_id, created, modified
    ) VALUES (
        v_test_patient_active_id, 'O2-001', v_test_inventory_oxygen_id,
        'TEST-O2-001', 1, CURRENT_DATE - INTERVAL '30 days',
        v_test_site_id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    ) RETURNING id INTO v_test_rental_no_recurring_id;
    
    -- TEST 1: Create charge line for active rental
    RAISE NOTICE '';
    RAISE NOTICE '=== TEST 1: Active DME Rental Charge Line ===';
    
    -- Check the view to see if rental is due for billing
    SELECT * INTO v_rental_view_record
    FROM vw_dme_rentals_due_for_billing
    WHERE rental_log_id = v_test_rental_cpap_id;
    
    IF v_rental_view_record IS NOT NULL THEN
        RAISE NOTICE 'Rental found in due for billing view';
        
        -- Create charge line for CPAP rental using correct parameters
        v_charge_line_result := create_dme_rental_charge_line(
            p_rental_log_id := v_test_rental_cpap_id,
            p_service_from_date := v_rental_view_record.service_from_date,
            p_service_to_date := v_rental_view_record.service_to_date
        );
        
        assert_not_null('Active rental - charge line created', v_charge_line_result.calc_invoice_split_no);
    ELSE
        RAISE NOTICE 'ASSERTION FAILED: Rental not found in due for billing view';
        v_assertion_failed := true;
    END IF;
    
    -- Verify charge line details from the result
    IF v_charge_line_result IS NOT NULL THEN
        assert_equals('Active rental - patient ID', v_test_patient_active_id::text, v_charge_line_result.patient_id::text);
        assert_not_null('Active rental - insurance ID', v_charge_line_result.insurance_id::text);
        assert_equals('Active rental - billing code', 'E0601', v_charge_line_result.hcpc_code);
        assert_equals('Active rental - modifier', 'RR', v_charge_line_result.modifier_1);
        assert_equals('Active rental - rental type', 'Rental', v_charge_line_result.rental_type);
        assert_numeric_equals('Active rental - quantity', 1, v_charge_line_result.charge_quantity);
        assert_not_null('Active rental - charge amount', v_charge_line_result.billed::text);
        assert_equals('Active rental - frequency code', '1', v_charge_line_result.frequency_code);
    END IF;
    
    -- TEST 2: Test patient not active edge case
    RAISE NOTICE '';
    RAISE NOTICE '=== TEST 2: Inactive Patient DME Rental ===';
    
    -- Check if inactive patient rental appears in due for billing view
    SELECT * INTO v_rental_view_record
    FROM vw_dme_rentals_due_for_billing
    WHERE rental_log_id = v_test_rental_wheelchair_id;
    
    -- Should be excluded because patient is inactive and payer doesn't have bill_recur_arrears
    IF v_rental_view_record.rental_id IS NULL THEN
        RAISE NOTICE 'ASSERTION PASSED: Inactive patient rental excluded from billing';
    ELSE
        -- Check exclusion reason
        assert_equals('Inactive patient - exclusion reason', 'Patient discharged', v_rental_view_record.exclusion_reason);
    END IF;
    
    -- Update payer to allow arrears billing for discharged patients
    UPDATE form_payer 
    SET bill_recur_arrears = 'Yes' 
    WHERE id = v_test_payer_commercial_id;
    
    -- Now check if it's billable
    SELECT * INTO v_rental_view_record
    FROM vw_dme_rentals_due_for_billing
    WHERE rental_log_id = v_test_rental_wheelchair_id;
    
    IF v_rental_view_record IS NOT NULL AND v_rental_view_record.billing_exclusion_reason IS NULL THEN
        v_charge_line_result := create_dme_rental_charge_line(
            p_rental_log_id := v_test_rental_wheelchair_id,
            p_service_from_date := v_rental_view_record.service_from_date,
            p_service_to_date := v_rental_view_record.service_to_date
        );
        assert_not_null('Inactive patient with arrears - charge line created', v_charge_line_result.calc_invoice_split_no);
    ELSE
        RAISE NOTICE 'Inactive patient rental still excluded after payer update';
    END IF;
    
    -- TEST 3: Test no recurring billing edge case
    RAISE NOTICE '';
    RAISE NOTICE '=== TEST 3: No Recurring Billing Payer ===';
    
    -- Check if rental with no recurring billing payer appears in view
    SELECT billing_exclusion_reason INTO v_exclusion_reason
    FROM vw_dme_rentals_due_for_billing
    WHERE rental_log_id = v_test_rental_no_recurring_id;
    
    -- Should have an exclusion reason
    IF v_exclusion_reason IS NOT NULL THEN
        RAISE NOTICE 'No recurring billing rental properly excluded: %', v_exclusion_reason;
    ELSE
        -- If it appears without exclusion, try to create charge line
        SELECT * INTO v_rental_view_record
        FROM vw_dme_rentals_due_for_billing
        WHERE rental_log_id = v_test_rental_no_recurring_id;
        
        IF v_rental_view_record IS NOT NULL THEN
            BEGIN
                v_charge_line_result := create_dme_rental_charge_line(
                    p_rental_log_id := v_test_rental_no_recurring_id,
                    p_service_from_date := v_rental_view_record.service_from_date,
                    p_service_to_date := v_rental_view_record.service_to_date
                );
                RAISE NOTICE 'ASSERTION FAILED: Charge line created for no recurring billing payer';
                v_assertion_failed := true;
            EXCEPTION WHEN OTHERS THEN
                RAISE NOTICE 'ASSERTION PASSED: Function properly rejected no recurring billing';
            END;
        END IF;
    END IF;
    
    -- The exclusion reason text might vary, so just check that it exists
    assert_not_null('No recurring billing - has exclusion reason', v_exclusion_reason);
    
    -- TEST 4: Test maximum claims reached
    RAISE NOTICE '';
    RAISE NOTICE '=== TEST 4: Maximum Claims Reached ===';
    
    -- Create 12 existing charge lines for Medicare rental (limit is 13)
    FOR i IN 1..12 LOOP
        INSERT INTO form_ledger_charge_line (
            patient_id, insurance_id, patient_insurance_id, rental_id,
            inventory_id, billing_code, modifier_1, date_of_service,
            date_of_service_end, quantity, billed, rental_type, frequency_code,
            created_by_id, created, modified
        ) VALUES (
            v_test_patient_active_id, v_test_patient_ins_medicare_id,
            v_test_patient_ins_medicare_id, v_test_rental_cpap_id,
            v_test_inventory_cpap_id, 'E0601', 'RR',
            CURRENT_DATE - INTERVAL '1 month' * (13 - i),
            CURRENT_DATE - INTERVAL '1 month' * (13 - i) + INTERVAL '29 days',
            1, 125.00, 'Rental', '1', v_test_user_id,
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        );
    END LOOP;
    
    -- Check if rental still appears in view (should show max claims reached)
    SELECT billing_exclusion_reason INTO v_exclusion_reason
    FROM vw_dme_rentals_due_for_billing
    WHERE rental_log_id = v_test_rental_cpap_id;
    
    IF v_exclusion_reason IS NOT NULL AND v_exclusion_reason LIKE '%Max claims%' THEN
        RAISE NOTICE 'ASSERTION PASSED: Max claims reached detected';
    ELSE
        -- May still allow one more claim until 13
        SELECT * INTO v_rental_view_record
        FROM vw_dme_rentals_due_for_billing
        WHERE rental_log_id = v_test_rental_cpap_id;
        
        IF v_rental_view_record IS NOT NULL AND v_rental_view_record.billing_exclusion_reason IS NULL THEN
            -- Try to create 13th charge line
            v_charge_line_result := create_dme_rental_charge_line(
                p_rental_log_id := v_test_rental_cpap_id,
                p_service_from_date := v_rental_view_record.service_from_date,
                p_service_to_date := v_rental_view_record.service_to_date
            );
            assert_not_null('13th claim for Medicare - created', v_charge_line_result.calc_invoice_split_no);
            
            -- Now check again - should be excluded
            SELECT billing_exclusion_reason INTO v_exclusion_reason
            FROM vw_dme_rentals_due_for_billing
            WHERE rental_log_id = v_test_rental_cpap_id;
            
            assert_not_null('After 13th claim - exclusion reason exists', v_exclusion_reason);
        END IF;
    END IF;
    
    -- TEST 5: Test span rental dates billing
    RAISE NOTICE '';
    RAISE NOTICE '=== TEST 5: Span Rental Dates Billing ===';
    
    -- Create a new rental with span rental dates payer
    DECLARE
        v_test_rental_span_id integer;
        v_test_serial_span_id uuid := gen_random_uuid();
    BEGIN
        -- Add serial number
        INSERT INTO form_inventory_serial (
            id, inventory_id, serial_no, status, created, modified
        ) VALUES (
            v_test_serial_span_id, v_test_inventory_cpap_id, 'CPAP-002', 'Available',
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        );
        
        -- Create rental activity
        INSERT INTO form_ledger_rental_log_activity (
            serial_no, inventory_id, patient_id, previous_patient_id,
            localized_datetime, ticket_no, ticket_item_no,
            created, modified
        ) VALUES (
            'CPAP-002', v_test_inventory_cpap_id, v_test_patient_inactive_id, NULL,
            CURRENT_DATE - INTERVAL '15 days', 'TEST-SPAN-001', 1,
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        );
        
        -- Create rental starting mid-month
        INSERT INTO form_inventory_rental_log (
            patient_id, serial_no, inventory_id, ticket_no, ticket_item_no,
            checked_out_date, site_id, created, modified
        ) VALUES (
            v_test_patient_inactive_id, 'CPAP-002', v_test_inventory_cpap_id,
            'TEST-SPAN-001', 1, CURRENT_DATE - INTERVAL '15 days',
            v_test_site_id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        ) RETURNING id INTO v_test_rental_span_id;
        
        -- Check if rental appears in view with span dates
        SELECT * INTO v_rental_view_record
        FROM vw_dme_rentals_due_for_billing
        WHERE rental_log_id = v_test_rental_span_id;
        
        IF v_rental_view_record IS NOT NULL AND v_rental_view_record.billing_exclusion_reason IS NULL THEN
            -- Create charge line with span dates
            v_charge_line_result := create_dme_rental_charge_line(
                p_rental_log_id := v_test_rental_span_id,
                p_service_from_date := v_rental_view_record.service_from_date,
                p_service_to_date := v_rental_view_record.service_to_date
            );
            
            assert_not_null('Span rental dates - charge line created', v_charge_line_result.calc_invoice_split_no);
            
            -- Should span from rental start to end of billing period
            assert_not_null('Span rental - date_of_service', v_charge_line_result.date_of_service::text);
            assert_not_null('Span rental - date_of_service_end', v_charge_line_result.date_of_service_end::text);
        ELSE
            RAISE NOTICE 'Span rental not found in billing view or has exclusion';
        END IF;
        
        -- Clean up
        DELETE FROM form_ledger_rental_log_activity 
        WHERE serial_no = 'CPAP-002' AND inventory_id = v_test_inventory_cpap_id;
        DELETE FROM form_inventory_rental_log WHERE id = v_test_rental_span_id;
        DELETE FROM form_inventory_serial WHERE id = v_test_serial_span_id;
    END;
    
    -- TEST 6: Test ready to bill view
    RAISE NOTICE '';
    RAISE NOTICE '=== TEST 6: Ready to Bill View ===';
    
    -- Check if rentals are properly tracked in the view
    DECLARE
        v_rental_count integer;
    BEGIN
        SELECT COUNT(*) INTO v_rental_count
        FROM vw_dme_rentals_due_for_billing
        WHERE rental_log_id IN (v_test_rental_cpap_id, v_test_rental_wheelchair_id, v_test_rental_no_recurring_id);
        
        IF v_rental_count > 0 THEN
            RAISE NOTICE 'ASSERTION PASSED: Test rentals found in DME billing view (count: %)', v_rental_count;
        ELSE
            RAISE NOTICE 'WARNING: No test rentals found in DME billing view';
        END IF;
    END;
    
    -- Clean up test data
    RAISE NOTICE '';
    RAISE NOTICE 'Cleaning up test data...';
    
    -- Delete charge lines
    DELETE FROM form_ledger_charge_line 
    WHERE rental_id IN (v_test_rental_cpap_id, v_test_rental_wheelchair_id, v_test_rental_no_recurring_id);
    
    -- Delete rental activity
    DELETE FROM form_ledger_rental_log_activity
    WHERE serial_no IN ('CPAP-001', 'WC-001', 'O2-001')
      AND inventory_id IN (v_test_inventory_cpap_id, v_test_inventory_wheelchair_id, v_test_inventory_oxygen_id);
    
    -- Delete rental logs
    DELETE FROM form_inventory_rental_log 
    WHERE id IN (v_test_rental_cpap_id, v_test_rental_wheelchair_id, v_test_rental_no_recurring_id);
    
    -- Delete serial numbers
    DELETE FROM form_inventory_serial 
    WHERE id IN (v_test_serial_cpap_id, v_test_serial_wheelchair_id, v_test_serial_oxygen_id);
    
    -- Delete inventory
    DELETE FROM form_inventory 
    WHERE id IN (v_test_inventory_cpap_id, v_test_inventory_wheelchair_id, v_test_inventory_oxygen_id);
    
    -- Delete user
    DELETE FROM form_user WHERE id = v_test_user_id;
    
    -- Delete patient insurance
    DELETE FROM form_patient_insurance 
    WHERE id IN (v_test_patient_ins_medicare_id, v_test_patient_ins_commercial_id, v_test_patient_ins_no_recurring_id);
    
    -- Delete patients
    DELETE FROM form_patient WHERE id IN (v_test_patient_active_id, v_test_patient_inactive_id);
    
    -- Delete payers
    DELETE FROM form_payer 
    WHERE id IN (v_test_payer_medicare_id, v_test_payer_commercial_id, v_test_payer_no_recurring_id);
    
    -- Delete prescriber
    DELETE FROM form_physician WHERE id = v_test_prescriber_id;
    
    -- Delete site
    DELETE FROM form_site WHERE id = v_test_site_id;
    
    -- Delete company
    DELETE FROM form_company WHERE id = v_test_company_id;
    
    -- Final result
    IF v_assertion_failed THEN
        RAISE EXCEPTION 'One or more assertions failed. See NOTICE output for details.';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE 'All DME charge line generation tests passed successfully!';
    END IF;
    
END $$;