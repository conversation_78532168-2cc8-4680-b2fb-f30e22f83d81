-- 0000-ss-prescriber-test.sql
-- This file tests the parsing of prescriber fields from SureScripts messages
-- It inserts a test message with complete prescriber data and validates the parsing

-- Create a test message with comprehensive prescriber data
DO $$
DECLARE
    json_template TEXT := '{
      "?xml": "",
      "Message": {
        "Header": {
          "To": 111111,
          "From": 1260306616002,
          "MessageID": "%s",
          "SentTime": "2023-05-01T13:42:39.7Z",
          "SenderSoftware": { "SenderSoftwareDeveloper": "Surescripts", "SenderSoftwareProduct": "Certification Testing", "SenderSoftwareVersionRelease": "20170715" },
          "PrescriberOrderNumber": "PRESCRIBER_TEST_ORDER"
        },
        "Body": {
          "NewRx": {
            "Patient": { "HumanPatient": { "Name": { "LastName": "TestPatient", "FirstName": "<PERSON>" }, "Gender": "M", "DateOfBirth": { "Date": "1980-01-01" } } },
            "Prescriber": {
              "NonVeterinarian": {
                "Identification": {
                  "StateLicenseNumber": "SL12345",
                  "StateControlledSubstanceNumber": "CS12345",
                  "MedicareNumber": "MC12345",
                  "MedicaidNumber": "MD12345",
                  "DEANumber": "DE12345",
                  "NPI": "**********",
                  "CertificateToPrescribe": "CP12345",
                  "Data2000WaiverID": "DW12345"
                },
                "Specialty": "363L00000X",
                "PracticeLocation": {
                  "BusinessName": "Test Practice",
                  "Identification": {
                    "NCPDPID": "PL12345",
                    "DEANumber": "PL-DEA12345",
                    "REMSHealthcareSettingEnrollmentID": "PL-REMS12345",
                    "StateControlledSubstanceNumber": "PL-CS12345",
                    "MedicareNumber": "PL-MC12345",
                    "MedicaidNumber": "PL-MD12345",
                    "StateLicenseNumber": "PL-SL12345"
                  }
                },
                "Name": { "LastName": "TestDoctor", "FirstName": "Jane", "MiddleName": "M", "Suffix": "MD" },
                "FormerName": { "LastName": "OldName", "FirstName": "Jane", "MiddleName": "M" },
                "Address": { "AddressLine1": "123 Main St", "AddressLine2": "Suite 100", "City": "Testville", "StateProvince": "CA", "PostalCode": "12345", "CountryCode": "US" },
                "CommunicationNumbers": {
                  "PrimaryTelephone": { "Number": "**********", "Extension": "123" },
                  "ElectronicMail": "<EMAIL>",
                  "Fax": { "Number": "**********" },
                  "HomeTelephone": { "Number": "**********", "SupportsSMS": "Y" }
                },
                "PrescriberAgent": {
                  "Name": { "LastName": "AgentLast", "FirstName": "AgentFirst" }
                }
              }
            },
            "Supervisor": {
              "NonVeterinarian": {
                "Identification": { "NPI": "**********" },
                "Name": { "LastName": "SupervisorLast", "FirstName": "SupervisorFirst", "MiddleName": "S", "Suffix": "PhD" },
                "Address": { "AddressLine1": "456 Supervisor St", "AddressLine2": "Suite 200", "City": "Supervisorville", "StateProvince": "CA", "PostalCode": "54321", "CountryCode": "US" },
                "CommunicationNumbers": { "PrimaryTelephone": { "Number": "**********", "Extension": "456", "SupportsSMS": "N" } }
              }
            },
            "FollowUpPrescriber": {
              "NonVeterinarian": {
                "Identification": { "NPI": "**********" },
                "Name": { "LastName": "FollowUpLast", "FirstName": "FollowUpFirst" },
                "Address": { "AddressLine1": "789 FollowUp St", "City": "FollowUpville", "StateProvince": "CA", "PostalCode": "67890", "CountryCode": "US" },
                "CommunicationNumbers": { "PrimaryTelephone": { "Number": "**********" } }
              }
            },
            "MedicationPrescribed": {
              "DrugDescription": "Test Medication 10mg",
              "Quantity": { "Value": 30, "CodeListQualifier": "38", "QuantityUnitOfMeasure": { "Code": "C48480" } },
              "WrittenDate": { "Date": "2023-05-01" },
              "Substitutions": "0",
              "NumberOfRefills": 3,
              "Sig": { "SigText": "Take 1 tablet daily" }
            }
          }
        }
      }
    }';
    json_content_final TEXT;
    message_id_value TEXT;
    message_id_unique_suffix TEXT := gen_random_uuid()::text;
    log_id INTEGER;
    ss_message_id INTEGER;
BEGIN
    message_id_value := 'PRESCRIBER_TEST_' || message_id_unique_suffix;
    json_content_final := format(json_template, message_id_value);

    -- Insert the test message into form_ss_log
    INSERT INTO form_ss_log (message_json, direction, message_type, message_id, created_by, updated_by)
    VALUES (json_content_final::jsonb, 'IN', 'NewRx', 'PRESCRIBER_TEST_' || message_id_unique_suffix, 1, 1)
    RETURNING id INTO log_id;

    RAISE NOTICE 'Inserted test message into form_ss_log with ID: %', log_id;

    -- Process the message
    PERFORM parse_ss_inbound(log_id);

    -- Get the resulting ss_message_id
    SELECT id INTO ss_message_id FROM form_ss_message WHERE message_id = 'PRESCRIBER_TEST_' || message_id_unique_suffix;

    -- Validate the prescriber fields
    IF ss_message_id IS NULL THEN
        RAISE EXCEPTION 'Failed to process the test message - no form_ss_message record created';
    ELSE
        RAISE NOTICE 'Successfully processed test message - form_ss_message ID: %', ss_message_id;

        -- Validate prescriber fields
        PERFORM assert_equals('**********', (SELECT prescriber_npi FROM form_ss_message WHERE id = ss_message_id), 'Prescriber NPI');
        PERFORM assert_equals('DE12345', (SELECT prescriber_dea FROM form_ss_message WHERE id = ss_message_id), 'Prescriber DEA');
        PERFORM assert_equals('SL12345', (SELECT prescriber_state_lic FROM form_ss_message WHERE id = ss_message_id), 'Prescriber State License');
        PERFORM assert_equals('CS12345', (SELECT prescriber_state_cs_lic FROM form_ss_message WHERE id = ss_message_id), 'Prescriber State CS License');
        PERFORM assert_equals('MC12345', (SELECT prescriber_medicare FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Medicare');
        PERFORM assert_equals('MD12345', (SELECT prescriber_medicaid FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Medicaid');
        PERFORM assert_equals('CP12345', (SELECT prescriber_certificate_to_prescribe FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Certificate');
        PERFORM assert_equals('DW12345', (SELECT prescriber_2000waiver_id FROM form_ss_message WHERE id = ss_message_id), 'Prescriber 2000 Waiver ID');
        PERFORM assert_equals('363L00000X', (SELECT prescriber_spec_id FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Specialty');
        PERFORM assert_equals('TestDoctor', (SELECT prescriber_last_name FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Last Name');
        PERFORM assert_equals('Jane', (SELECT prescriber_first_name FROM form_ss_message WHERE id = ss_message_id), 'Prescriber First Name');
        PERFORM assert_equals('123 Main St', (SELECT prescriber_address_1 FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Address 1');
        PERFORM assert_equals('Suite 100', (SELECT prescriber_address_2 FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Address 2');
        PERFORM assert_equals('Testville', (SELECT prescriber_city FROM form_ss_message WHERE id = ss_message_id), 'Prescriber City');
        PERFORM assert_equals('CA', (SELECT prescriber_state FROM form_ss_message WHERE id = ss_message_id), 'Prescriber State');
        PERFORM assert_equals('12345', (SELECT prescriber_zip FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Zip');
        PERFORM assert_equals('**********', (SELECT prescriber_phone FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Phone');
        PERFORM assert_equals('123', (SELECT prescriber_extension FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Extension');
        PERFORM assert_equals('**********', (SELECT prescriber_fax FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Fax');

        -- Validate practice location fields
        PERFORM assert_equals('Test Practice', (SELECT prescriber_loc_name FROM form_ss_message WHERE id = ss_message_id), 'Practice Location Name');
        PERFORM assert_equals('PL12345', (SELECT prescriber_loc_ncpdp_id FROM form_ss_message WHERE id = ss_message_id), 'Practice Location NCPDP ID');
        PERFORM assert_equals('PL-DEA12345', (SELECT prescriber_loc_dea FROM form_ss_message WHERE id = ss_message_id), 'Practice Location DEA');
        PERFORM assert_equals('PL-REMS12345', (SELECT prescriber_loc_rems FROM form_ss_message WHERE id = ss_message_id), 'Practice Location REMS');
        PERFORM assert_equals('PL-CS12345', (SELECT prescriber_loc_state_cs_lic FROM form_ss_message WHERE id = ss_message_id), 'Practice Location State CS License');
        PERFORM assert_equals('PL-MC12345', (SELECT prescriber_loc_medicare FROM form_ss_message WHERE id = ss_message_id), 'Practice Location Medicare');
        PERFORM assert_equals('PL-MD12345', (SELECT prescriber_loc_medicaid FROM form_ss_message WHERE id = ss_message_id), 'Practice Location Medicaid');
        PERFORM assert_equals('PL-SL12345', (SELECT prescriber_loc_state_lic FROM form_ss_message WHERE id = ss_message_id), 'Practice Location State License');

        -- Validate agent fields
        PERFORM assert_equals('AgentFirst', (SELECT prescriber_agent_first_name FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Agent First Name');
        PERFORM assert_equals('AgentLast', (SELECT prescriber_agent_last_name FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Agent Last Name');

        -- Validate supervisor fields
        PERFORM assert_equals('SupervisorFirst', (SELECT supervisor_first_name FROM form_ss_message WHERE id = ss_message_id), 'Supervisor First Name');
        PERFORM assert_equals('SupervisorLast', (SELECT supervisor_last_name FROM form_ss_message WHERE id = ss_message_id), 'Supervisor Last Name');
        PERFORM assert_equals('**********', (SELECT supervisor_npi FROM form_ss_message WHERE id = ss_message_id), 'Supervisor NPI');

        -- Validate follow-up prescriber fields
        PERFORM assert_equals('FollowUpFirst', (SELECT fu_prescriber_first_name FROM form_ss_message WHERE id = ss_message_id), 'Follow-up Prescriber First Name');
        PERFORM assert_equals('FollowUpLast', (SELECT fu_prescriber_last_name FROM form_ss_message WHERE id = ss_message_id), 'Follow-up Prescriber Last Name');
        PERFORM assert_equals('**********', (SELECT fu_prescriber_npi FROM form_ss_message WHERE id = ss_message_id), 'Follow-up Prescriber NPI');

        RAISE NOTICE 'All prescriber field validations passed!';
    END IF;

    -- Clean up the test data
    DELETE FROM form_ss_message WHERE id = ss_message_id;
    DELETE FROM form_ss_log WHERE id = log_id;

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error during test: %', SQLERRM;
        -- Clean up any test data that might have been created
        DELETE FROM form_ss_message WHERE message_id = 'PRESCRIBER_TEST_' || message_id_unique_suffix;
        DELETE FROM form_ss_log WHERE id = log_id;
        RAISE;
END $$;

-- Helper function for assertions
CREATE OR REPLACE FUNCTION assert_equals(expected TEXT, actual TEXT, field_name TEXT)
RETURNS VOID AS $$
BEGIN
    IF expected IS DISTINCT FROM actual THEN
        RAISE EXCEPTION 'Assertion failed for %: expected "%" but got "%"', field_name, expected, actual;
    END IF;
END;
$$ LANGUAGE plpgsql;
