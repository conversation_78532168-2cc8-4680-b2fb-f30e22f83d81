-- Test CMS-1500 Paper Claim Triggers
-- This file tests all triggers for CMS-1500 paper claims (billing_method_id = 'cms1500')

BEGIN;

-- Set up test data
DO $$
DECLARE
    v_patient_id INTEGER;
    v_site_id INTEGER;
    v_payer_id INTEGER;
    v_insurance_id INTEGER;
    v_invoice_no TEXT;
    v_claim_no TEXT;
    v_claim_id INTEGER;
    v_service_line_id INTEGER;
    v_charge_line_id INTEGER;
    v_dx_id_1 INTEGER;
    v_dx_id_2 INTEGER;
    v_dx_id_3 INTEGER;
    v_inventory_id INTEGER;
    v_count INTEGER;
    v_total_billed NUMERIC;
    v_dx_count INTEGER;
    v_archived BOOLEAN;
BEGIN
    -- Clean up any existing test data
    PERFORM cleanup_test_data();
    
    RAISE NOTICE '=== Starting CMS-1500 Trigger Tests ===';
    
    -- Create test entities
    v_patient_id := create_test_patient('Test Patient CMS');
    v_site_id := create_test_site('Test Site CMS');
    v_payer_id := create_test_payer('Test Payer CMS', 'TESTPAYERCMS');
    
    -- Update payer with CMS-1500 specific settings
    UPDATE form_payer
    SET cms_14 = 'Yes'  -- Use formatted diagnosis codes
    WHERE id = v_payer_id;
    
    v_insurance_id := create_test_insurance(v_patient_id, v_payer_id, 'CMS123456');
    RAISE NOTICE 'create_test_insurance returned: %', v_insurance_id;
    
    -- Ensure the insurance record is active
    UPDATE form_patient_insurance 
    SET active = 'Yes'
    WHERE id = v_insurance_id;
    
    -- Verify the insurance record exists and is active
    SELECT COUNT(*) INTO v_count
    FROM form_patient_insurance 
    WHERE id = v_insurance_id 
      AND deleted IS NOT TRUE 
      AND archived IS NOT TRUE
      AND active = 'Yes';
    RAISE NOTICE 'Active insurance records found: %', v_count;
    
    v_inventory_id := create_test_inventory('99213', 'Office Visit Level 3');
    
    -- Create test diagnoses with ICD codes (or use existing ones)
    INSERT INTO form_list_diagnosis (
        code,
        icd_code,
        name,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES 
    ('E119', 'E11.9', 'Type 2 diabetes mellitus without complications', CURRENT_TIMESTAMP, 1, FALSE, FALSE),
    ('I10', 'I10', 'Essential hypertension', CURRENT_TIMESTAMP, 1, FALSE, FALSE),
    ('M7981', 'M79.81', 'Nontraumatic hematoma of soft tissue', CURRENT_TIMESTAMP, 1, FALSE, FALSE)
    ON CONFLICT (code) DO NOTHING;
    
    -- Create patient diagnoses individually to get proper IDs
    INSERT INTO form_patient_diagnosis (
        patient_id,
        dx_id,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        v_patient_id, 
        (SELECT id FROM form_list_diagnosis WHERE code = 'E119' LIMIT 1), 
        CURRENT_TIMESTAMP, 
        1, 
        FALSE, 
        FALSE
    ) RETURNING id INTO v_dx_id_1;
    
    INSERT INTO form_patient_diagnosis (
        patient_id,
        dx_id,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        v_patient_id, 
        (SELECT id FROM form_list_diagnosis WHERE code = 'I10' LIMIT 1), 
        CURRENT_TIMESTAMP, 
        1, 
        FALSE, 
        FALSE
    ) RETURNING id INTO v_dx_id_2;
    
    INSERT INTO form_patient_diagnosis (
        patient_id,
        dx_id,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        v_patient_id, 
        (SELECT id FROM form_list_diagnosis WHERE code = 'M7981' LIMIT 1), 
        CURRENT_TIMESTAMP, 
        1, 
        FALSE, 
        FALSE
    ) RETURNING id INTO v_dx_id_3;
    
    -- Create invoice
    v_invoice_no := 'INV-CMS-' || TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS');
    INSERT INTO form_billing_invoice (
        invoice_no,
        master_invoice_no,
        patient_id,
        site_id,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        v_invoice_no,
        v_invoice_no,
        v_patient_id,
        v_site_id,
        CURRENT_TIMESTAMP,
        1,
        FALSE,
        FALSE
    );
    
    -- Create CMS-1500 claim
    v_claim_no := 'CMS-' || TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS');
    INSERT INTO form_med_claim_1500 (
        claim_no,
        invoice_no,
        patient_id,
        site_id,
        payer_id,
        insurance_id,
        billed,
        expected,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        v_claim_no,
        v_invoice_no,
        v_patient_id,
        v_site_id,
        v_payer_id,
        v_insurance_id,
        0::NUMERIC,
        0::NUMERIC,
        CURRENT_TIMESTAMP,
        1,
        FALSE,
        FALSE
    ) RETURNING id INTO v_claim_id;
    
    -- Test 1: Rental Service Dates Sync for CMS-1500
    RAISE NOTICE 'Test 1: Testing sync_rental_service_dates_cms1500 trigger';
    
    -- Create a rental charge line
    INSERT INTO form_ledger_charge_line (
        invoice_no,
        charge_no,
        patient_id,
        site_id,
        insurance_id,
        inventory_id,
        billing_method_id,
        rental_type,
        date_of_service,
        date_of_service_end,
        hcpc_code,
        bill_quantity,
        billed,
        expected,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        v_invoice_no,
        'CMS-RENTAL-001',
        v_patient_id,
        v_site_id,
        v_insurance_id,
        v_inventory_id,
        'cms1500',
        'Monthly',
        '2024-03-01'::DATE,
        '2024-03-31'::DATE,
        'E0431',
        31::NUMERIC,
        650.00::NUMERIC,
        650.00::NUMERIC,
        CURRENT_TIMESTAMP,
        1,
        FALSE,
        FALSE
    ) RETURNING id INTO v_charge_line_id;
    
    -- Debug: Check if insurance ID is valid
    RAISE NOTICE 'Created insurance ID: %', v_insurance_id;
    RAISE NOTICE 'Created claim ID: %', v_claim_id;
    
    -- Wait for trigger to create CMS-1500 service line
    PERFORM pg_sleep(0.1);
    
    -- Get the created service line ID
    SELECT sl.id INTO v_service_line_id
    FROM form_med_claim_1500_sl sl
    INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_sl sf_sl ON sf_sl.form_med_claim_1500_sl_fk = sl.id
    WHERE sf_sl.form_med_claim_1500_fk = v_claim_id
    AND sl.patient_id = v_patient_id
    AND sl.deleted IS NOT TRUE
    AND sl.archived IS NOT TRUE
    AND sf_sl.delete IS NOT TRUE
    AND sf_sl.archive IS NOT TRUE
    LIMIT 1;
    
    -- Update rental dates to trigger sync
    UPDATE form_ledger_charge_line
    SET date_of_service = '2024-04-01'::DATE,
        date_of_service_end = '2024-04-30'::DATE,
        updated_on = CURRENT_TIMESTAMP,
        updated_by = 1
    WHERE id = v_charge_line_id;
    
    -- Verify CMS-1500 service line dates were updated
    PERFORM assert_equals('2024-04-01',
        (SELECT service_date::TEXT FROM form_med_claim_1500_sl WHERE id = v_service_line_id),
        'CMS-1500 service line start date');
    
    PERFORM assert_equals('2024-04-30',
        (SELECT service_date_end::TEXT FROM form_med_claim_1500_sl WHERE id = v_service_line_id),
        'CMS-1500 service line end date');
    
    -- Test 2: CMS-1500 Service Line Diagnoses Sync
    RAISE NOTICE 'Test 2: Testing sync_cms1500_service_line_diagnoses trigger';
    
    -- Update service line with diagnoses
    UPDATE form_med_claim_1500_sl
    SET dx_id_1 = v_dx_id_1,
        dx_id_2 = v_dx_id_2,
        updated_on = CURRENT_TIMESTAMP,
        updated_by = 1
    WHERE id = v_service_line_id;
    
    -- Verify diagnoses were created in med_claim_1500_dx
    SELECT COUNT(*) INTO v_dx_count
    FROM form_med_claim_1500_dx dx
    INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_dx sf_dx ON sf_dx.form_med_claim_1500_dx_fk = dx.id
    WHERE sf_dx.form_med_claim_1500_fk = v_claim_id
    AND dx.deleted IS NOT TRUE
    AND dx.archived IS NOT TRUE
    AND sf_dx.delete IS NOT TRUE
    AND sf_dx.archive IS NOT TRUE;
    
    PERFORM assert_numeric_equals('CMS-1500 diagnosis count', 2::NUMERIC, v_dx_count::NUMERIC);
    
    -- Verify formatted diagnosis codes were used (due to cms_14 = 'Yes')
    PERFORM assert_equals('E11.9',
        (SELECT diagnosis_code FROM form_med_claim_1500_dx WHERE patient_id = v_patient_id AND dx_id = v_dx_id_1 AND deleted IS NOT TRUE LIMIT 1),
        'CMS-1500 formatted diagnosis code');
    
    -- Test 3: Add Charge Line to CMS-1500 Claim
    RAISE NOTICE 'Test 3: Testing add_charge_line_to_cms1500_claim trigger';
    
    -- Insert a new charge line
    INSERT INTO form_ledger_charge_line (
        invoice_no,
        charge_no,
        patient_id,
        site_id,
        insurance_id,
        inventory_id,
        billing_method_id,
        date_of_service,
        hcpc_code,
        bill_quantity,
        billed,
        expected,
        modifier_1,
        modifier_2,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        v_invoice_no,
        'CMS-NEW-001',
        v_patient_id,
        v_site_id,
        v_insurance_id,
        v_inventory_id,
        'cms1500',
        '2024-04-15'::DATE,
        '99214',
        1::NUMERIC,
        250.00::NUMERIC,
        250.00::NUMERIC,
        '25',
        'GT',
        CURRENT_TIMESTAMP,
        1,
        FALSE,
        FALSE
    );
    
    -- Verify service line was created
    SELECT COUNT(*) INTO v_count
    FROM form_med_claim_1500_sl sl
    INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_sl sf_sl ON sf_sl.form_med_claim_1500_sl_fk = sl.id
    WHERE sf_sl.form_med_claim_1500_fk = v_claim_id
    AND sl.patient_id = v_patient_id
    AND sl.deleted IS NOT TRUE
    AND sl.archived IS NOT TRUE
    AND sf_sl.delete IS NOT TRUE
    AND sf_sl.archive IS NOT TRUE;
    
    PERFORM assert_numeric_equals('Total CMS-1500 service lines', 2::NUMERIC, v_count::NUMERIC);
    
    -- Verify CMS-1500 claim totals were updated
    SELECT billed INTO v_total_billed
    FROM form_med_claim_1500
    WHERE id = v_claim_id;
    
    PERFORM assert_numeric_equals('CMS-1500 claim total', 900.00::NUMERIC, v_total_billed::NUMERIC);
    
    -- Test 4: Sync Charge Line Fields to CMS-1500 Claim
    RAISE NOTICE 'Test 4: Testing sync_charge_line_fields_to_cms1500_claim trigger';
    
    -- Update charge line fields
    UPDATE form_ledger_charge_line
    SET hcpc_code = '99215',
        bill_quantity = 2::NUMERIC,
        billed = 500.00::NUMERIC,
        modifier_1 = '59',
        modifier_2 = 'GQ',
        modifier_3 = 'XE',
        modifier_4 = 'XS',
        updated_on = CURRENT_TIMESTAMP,
        updated_by = 1
    WHERE charge_no = 'CMS-NEW-001'
    AND invoice_no = v_invoice_no;
    
    -- Verify CMS-1500 service line was updated
    -- Check procedure code
    PERFORM assert_equals('99215',
        (SELECT procedure_code FROM form_med_claim_1500_sl WHERE charge_no = 'CMS-NEW-001' AND deleted IS NOT TRUE AND archived IS NOT TRUE LIMIT 1),
        'Updated CMS-1500 procedure code');
    
    -- Check service unit count
    PERFORM assert_numeric_equals('Updated CMS-1500 service unit count',
        2::NUMERIC,
        (SELECT service_unit_count FROM form_med_claim_1500_sl WHERE charge_no = 'CMS-NEW-001' AND deleted IS NOT TRUE AND archived IS NOT TRUE LIMIT 1)::NUMERIC);
    
    -- Check line item charge amount
    PERFORM assert_numeric_equals('Updated CMS-1500 line item charge amount',
        500.00::NUMERIC,
        (SELECT line_item_charge_amount FROM form_med_claim_1500_sl WHERE charge_no = 'CMS-NEW-001' AND deleted IS NOT TRUE AND archived IS NOT TRUE LIMIT 1)::NUMERIC);
    
    -- Check modifiers
    PERFORM assert_equals('59',
        (SELECT modifier_1 FROM form_med_claim_1500_sl WHERE charge_no = 'CMS-NEW-001' AND deleted IS NOT TRUE AND archived IS NOT TRUE LIMIT 1),
        'Updated CMS-1500 modifier 1');
    
    PERFORM assert_equals('GQ',
        (SELECT modifier_2 FROM form_med_claim_1500_sl WHERE charge_no = 'CMS-NEW-001' AND deleted IS NOT TRUE AND archived IS NOT TRUE LIMIT 1),
        'Updated CMS-1500 modifier 2');
    
    PERFORM assert_equals('XE',
        (SELECT modifier_3 FROM form_med_claim_1500_sl WHERE charge_no = 'CMS-NEW-001' AND deleted IS NOT TRUE AND archived IS NOT TRUE LIMIT 1),
        'Updated CMS-1500 modifier 3');
    
    PERFORM assert_equals('XS',
        (SELECT modifier_4 FROM form_med_claim_1500_sl WHERE charge_no = 'CMS-NEW-001' AND deleted IS NOT TRUE AND archived IS NOT TRUE LIMIT 1),
        'Updated CMS-1500 modifier 4');
    
    -- Verify claim totals were updated
    SELECT billed INTO v_total_billed
    FROM form_med_claim_1500
    WHERE id = v_claim_id;
    
    PERFORM assert_numeric_equals('Updated CMS-1500 claim total', 1150.00::NUMERIC, v_total_billed::NUMERIC);
    
    -- Test 5: Handle Voided Charge Line for CMS-1500
    RAISE NOTICE 'Test 5: Testing handle_voided_charge_line_cms1500 trigger';
    
    -- Get count of active service lines before voiding
    SELECT COUNT(*) INTO v_count
    FROM form_med_claim_1500_sl sl
    INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_sl sf_sl ON sf_sl.form_med_claim_1500_sl_fk = sl.id
    WHERE sf_sl.form_med_claim_1500_fk = v_claim_id
    AND sl.deleted IS NOT TRUE
    AND sl.archived IS NOT TRUE
    AND sf_sl.delete IS NOT TRUE
    AND sf_sl.archive IS NOT TRUE;
    
    RAISE NOTICE 'Active CMS-1500 service lines before void: %', v_count;
    
    -- Archive a charge line (similar to void/zero)
    UPDATE form_ledger_charge_line
    SET archived = TRUE,
        updated_on = CURRENT_TIMESTAMP,
        updated_by = 1
    WHERE charge_no = 'CMS-NEW-001'
    AND invoice_no = v_invoice_no;
    
    -- Verify service line was archived
    SELECT sl.archived INTO v_archived
    FROM form_med_claim_1500_sl sl
    WHERE sl.charge_no = 'CMS-NEW-001'
    LIMIT 1;
    
    PERFORM assert_equals('true',
        v_archived::TEXT,
        'CMS-1500 service line archived after archiving charge line');
    
    -- Verify CMS-1500 claim totals were updated
    SELECT billed INTO v_total_billed
    FROM form_med_claim_1500
    WHERE id = v_claim_id;
    
    PERFORM assert_numeric_equals('CMS-1500 claim total after archive', 650.00::NUMERIC, v_total_billed::NUMERIC);
    
    -- Test 6: Multiple diagnoses and removal
    RAISE NOTICE 'Test 6: Testing diagnosis removal and management';
    
    -- Create a new service line with multiple diagnoses
    INSERT INTO form_ledger_charge_line (
        invoice_no,
        charge_no,
        patient_id,
        site_id,
        insurance_id,
        inventory_id,
        billing_method_id,
        date_of_service,
        hcpc_code,
        bill_quantity,
        billed,
        expected,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        v_invoice_no,
        'CMS-DX-001',
        v_patient_id,
        v_site_id,
        v_insurance_id,
        v_inventory_id,
        'cms1500',
        '2024-04-20'::DATE,
        '99213',
        1::NUMERIC,
        150.00::NUMERIC,
        150.00::NUMERIC,
        CURRENT_TIMESTAMP,
        1,
        FALSE,
        FALSE
    );
    
    -- Wait for service line to be created
    PERFORM pg_sleep(0.1);
    
    -- Get the new service line ID
    SELECT sl.id INTO v_service_line_id
    FROM form_med_claim_1500_sl sl
    INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_sl sf_sl ON sf_sl.form_med_claim_1500_sl_fk = sl.id
    WHERE sf_sl.form_med_claim_1500_fk = v_claim_id
    AND sl.patient_id = v_patient_id
    AND sl.deleted IS NOT TRUE
    AND sl.archived IS NOT TRUE
    AND sf_sl.delete IS NOT TRUE
    AND sf_sl.archive IS NOT TRUE
    ORDER BY sl.created_on DESC
    LIMIT 1;
    
    -- Add all three diagnoses
    UPDATE form_med_claim_1500_sl
    SET dx_id_1 = v_dx_id_1,
        dx_id_2 = v_dx_id_2,
        dx_id_3 = v_dx_id_3,
        updated_on = CURRENT_TIMESTAMP,
        updated_by = 1
    WHERE id = v_service_line_id;
    
    -- Verify all diagnoses were added
    SELECT COUNT(*) INTO v_dx_count
    FROM form_med_claim_1500_dx dx
    INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_dx sf_dx ON sf_dx.form_med_claim_1500_dx_fk = dx.id
    WHERE sf_dx.form_med_claim_1500_fk = v_claim_id
    AND dx.deleted IS NOT TRUE
    AND dx.archived IS NOT TRUE
    AND sf_dx.delete IS NOT TRUE
    AND sf_dx.archive IS NOT TRUE;
    
    RAISE NOTICE 'Total diagnoses after adding three: %', v_dx_count;
    
    -- Remove one diagnosis that's not used by other service lines
    UPDATE form_med_claim_1500_sl
    SET dx_id_3 = NULL,
        updated_on = CURRENT_TIMESTAMP,
        updated_by = 1
    WHERE id = v_service_line_id;
    
    -- Verify the diagnosis was archived
    SELECT archived INTO v_archived
    FROM form_med_claim_1500_dx
    WHERE dx_id = v_dx_id_3
    AND patient_id = v_patient_id;
    
    RAISE NOTICE 'Diagnosis 3 archived status: %', v_archived;
    
    -- Test 7: Supplemental info handling
    RAISE NOTICE 'Test 7: Testing supplemental info field in CMS-1500';
    
    -- Create charge line with NDC code (which goes to supplemental info)
    INSERT INTO form_ledger_charge_line (
        invoice_no,
        charge_no,
        patient_id,
        site_id,
        insurance_id,
        inventory_id,
        billing_method_id,
        date_of_service,
        hcpc_code,
        ndc,
        metric_quantity,
        billing_unit_id,
        bill_quantity,
        billed,
        expected,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        v_invoice_no,
        'CMS-NDC-001',
        v_patient_id,
        v_site_id,
        v_insurance_id,
        v_inventory_id,
        'cms1500',
        '2024-04-25'::DATE,
        'J1100',
        '12345678901',
        10::NUMERIC,
        'UN',
        1::NUMERIC,
        75.00::NUMERIC,
        75.00::NUMERIC,
        CURRENT_TIMESTAMP,
        1,
        FALSE,
        FALSE
    );
    
    -- Wait for trigger to process
    PERFORM pg_sleep(0.1);
    
    -- Verify service line was created for NDC charge
    SELECT COUNT(*) INTO v_count
    FROM form_med_claim_1500_sl sl
    INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_sl sf_sl ON sf_sl.form_med_claim_1500_sl_fk = sl.id
    WHERE sf_sl.form_med_claim_1500_fk = v_claim_id
    AND sl.patient_id = v_patient_id
    AND sl.deleted IS NOT TRUE
    AND sl.archived IS NOT TRUE
    AND sf_sl.delete IS NOT TRUE
    AND sf_sl.archive IS NOT TRUE;
    
    PERFORM assert_numeric_equals('Total CMS-1500 service lines (including NDC)', 3::NUMERIC, v_count::NUMERIC);
    
    -- Test 8: Archived Charge Lines
    RAISE NOTICE 'Test 8: Testing archived charge lines for CMS-1500';
    
    -- Create a new charge line
    INSERT INTO form_ledger_charge_line (
        invoice_no,
        charge_no,
        patient_id,
        site_id,
        insurance_id,
        inventory_id,
        billing_method_id,
        date_of_service,
        hcpc_code,
        bill_quantity,
        billed,
        expected,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        v_invoice_no,
        'CMS-ARCH-001',
        v_patient_id,
        v_site_id,
        v_insurance_id,
        v_inventory_id,
        'cms1500',
        '2024-04-30'::DATE,
        '99212',
        1::NUMERIC,
        100.00::NUMERIC,
        100.00::NUMERIC,
        CURRENT_TIMESTAMP,
        1,
        FALSE,
        FALSE
    );
    
    -- Wait for trigger to process
    PERFORM pg_sleep(0.1);
    
    -- Archive the charge line
    UPDATE form_ledger_charge_line
    SET archived = TRUE,
        updated_on = CURRENT_TIMESTAMP,
        updated_by = 1
    WHERE charge_no = 'CMS-ARCH-001'
    AND invoice_no = v_invoice_no;
    
    -- Verify service line was archived
    SELECT sl.archived INTO v_archived
    FROM form_med_claim_1500_sl sl
    WHERE sl.patient_id = v_patient_id
    ORDER BY sl.created_on DESC
    LIMIT 1;
    
    PERFORM assert_equals('true',
        v_archived::TEXT,
        'CMS-1500 service line archived when charge line archived');
    
    -- Test 9: Service line without diagnoses
    RAISE NOTICE 'Test 9: Testing service line without diagnoses';
    
    -- Create a charge line without setting diagnoses
    INSERT INTO form_ledger_charge_line (
        invoice_no,
        charge_no,
        patient_id,
        site_id,
        insurance_id,
        inventory_id,
        billing_method_id,
        date_of_service,
        hcpc_code,
        bill_quantity,
        billed,
        expected,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        v_invoice_no,
        'CMS-NODX-001',
        v_patient_id,
        v_site_id,
        v_insurance_id,
        v_inventory_id,
        'cms1500',
        '2024-05-01'::DATE,
        '99211',
        1::NUMERIC,
        75.00::NUMERIC,
        75.00::NUMERIC,
        CURRENT_TIMESTAMP,
        1,
        FALSE,
        FALSE
    );
    
    -- Wait for trigger to process
    PERFORM pg_sleep(0.1);
    
    -- Verify service line was created without diagnoses
    DECLARE
        v_sl_dx_id_1 INTEGER;
        v_sl_dx_id_2 INTEGER;
        v_sl_dx_id_3 INTEGER;
        v_sl_dx_id_4 INTEGER;
    BEGIN
        SELECT sl.dx_id_1, sl.dx_id_2, sl.dx_id_3, sl.dx_id_4
        FROM form_med_claim_1500_sl sl
        WHERE sl.patient_id = v_patient_id
        ORDER BY sl.created_on DESC
        LIMIT 1
        INTO v_sl_dx_id_1, v_sl_dx_id_2, v_sl_dx_id_3, v_sl_dx_id_4;
        
        PERFORM assert_null(v_sl_dx_id_1::TEXT, 'Service line dx_id_1 should be null');
        PERFORM assert_null(v_sl_dx_id_2::TEXT, 'Service line dx_id_2 should be null');
        PERFORM assert_null(v_sl_dx_id_3::TEXT, 'Service line dx_id_3 should be null');
        PERFORM assert_null(v_sl_dx_id_4::TEXT, 'Service line dx_id_4 should be null');
    END;
    
    -- Test 10: Diagnosis Pointer Updates  
    RAISE NOTICE 'Test 10: Testing diagnosis pointer updates';
    
    -- Get the service line ID for CMS-NODX-001
    SELECT sl.id INTO v_service_line_id
    FROM form_med_claim_1500_sl sl
    WHERE sl.patient_id = v_patient_id
    ORDER BY sl.created_on DESC
    LIMIT 1;
    
    -- Update service line with diagnosis pointers
    UPDATE form_med_claim_1500_sl
    SET dx_id_1 = v_dx_id_1,
        dx_id_2 = v_dx_id_2,
        updated_on = CURRENT_TIMESTAMP,
        updated_by = 1
    WHERE id = v_service_line_id;
    
    -- Verify the diagnosis pointers were set
    PERFORM assert_numeric_equals('CMS-1500 diagnosis pointer 1',
        v_dx_id_1::NUMERIC,
        (SELECT dx_id_1 FROM form_med_claim_1500_sl WHERE id = v_service_line_id)::NUMERIC);
    
    PERFORM assert_numeric_equals('CMS-1500 diagnosis pointer 2',
        v_dx_id_2::NUMERIC,
        (SELECT dx_id_2 FROM form_med_claim_1500_sl WHERE id = v_service_line_id)::NUMERIC);
    
    -- Test 11: Testing modifiers on existing service line
    RAISE NOTICE 'Test 11: Testing modifier updates on existing service line';
    
    -- Update charge line to remove some modifiers
    UPDATE form_ledger_charge_line
    SET modifier_3 = NULL,
        modifier_4 = NULL,
        updated_on = CURRENT_TIMESTAMP,
        updated_by = 1
    WHERE charge_no = 'CMS-NEW-001'
    AND invoice_no = v_invoice_no;
    
    -- Verify modifiers were cleared
    PERFORM assert_null(
        (SELECT modifier_3 FROM form_med_claim_1500_sl WHERE patient_id = v_patient_id AND deleted IS NOT TRUE AND archived IS NOT TRUE ORDER BY created_on DESC LIMIT 1)::TEXT,
        'CMS-1500 modifier 3 should be cleared');
    
    PERFORM assert_null(
        (SELECT modifier_4 FROM form_med_claim_1500_sl WHERE patient_id = v_patient_id AND deleted IS NOT TRUE AND archived IS NOT TRUE ORDER BY created_on DESC LIMIT 1)::TEXT,
        'CMS-1500 modifier 4 should be cleared');
    
    -- Test 12: Place of Service Code
    RAISE NOTICE 'Test 12: Testing place of service code handling';
    
    -- Create a charge line to test place of service
    INSERT INTO form_ledger_charge_line (
        invoice_no,
        charge_no,
        patient_id,
        site_id,
        insurance_id,
        inventory_id,
        billing_method_id,
        date_of_service,
        hcpc_code,
        bill_quantity,
        billed,
        expected,
        created_on,
        created_by,
        deleted,
        archived
    ) VALUES (
        v_invoice_no,
        'CMS-POS-001',
        v_patient_id,
        v_site_id,
        v_insurance_id,
        v_inventory_id,
        'cms1500',
        '2024-05-05'::DATE,
        '99213',
        1::NUMERIC,
        125.00::NUMERIC,
        125.00::NUMERIC,
        CURRENT_TIMESTAMP,
        1,
        FALSE,
        FALSE
    );
    
    -- Wait for trigger to process
    PERFORM pg_sleep(0.1);
    
    -- Verify place of service was set (should default based on build_mm_1500_charge_lines_loop logic)
    SELECT place_of_service_code
    FROM form_med_claim_1500_sl sl
    WHERE sl.patient_id = v_patient_id
    ORDER BY sl.created_on DESC
    LIMIT 1
    INTO v_count; -- reusing v_count for place_of_service_code
    
    PERFORM assert_not_null(v_count::TEXT, 'CMS-1500 place of service code should be set');
    
    RAISE NOTICE '=== All CMS-1500 Trigger Tests Passed ===';
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Test failed: %', SQLERRM;
    RAISE;
END $$;

ROLLBACK;