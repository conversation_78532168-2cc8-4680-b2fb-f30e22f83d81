

DO $$
DECLARE
    -- Variable declarations
    v_message_json JSONB;
    v_header_json JSONB;
    v_patient_json JSONB;
    v_prescriber_json JSONB;
    v_supervisor_json JSONB;
    v_pharmacy_json JSONB;
    v_response_json JSONB;
    v_medication_json JSONB;
    v_observations_json JSONB;
    v_allergy_json JSONB;
    v_followup_prescriber_json JSONB;

    v_ss_message_id INTEGER;
    v_temp_text TEXT;
    v_temp_int INTEGER;
    v_temp_date DATE;
    v_temp_text_array TEXT[];
    error_message TEXT;
    message_id_value TEXT := 'test-renewalres-max-pop-' || floor(random()***********)::TEXT;
    v_diag_record RECORD;
    v_compound_record RECORD;
    v_obs_record RECORD;
    v_due_record RECORD;
    v_allergy_record RECORD;
    compound_ingredients JSONB;
    diagnosis_count INTEGER;
BEGIN
    -- Build the JSON directly using jsonb_build_object
    
    -- 1. Build the Header section
    v_header_json := jsonb_build_object(
        'To', 111111,
        'From', 6997083822001,
        'MessageID', message_id_value,
        'RelatesToMessageID', '!',
        'SentTime', '2019-01-01T16:28:56.7Z',
        'SenderSoftware', jsonb_build_object(
            'SenderSoftwareDeveloper', 'Surescripts',
            'SenderSoftwareProduct', 'Certification Testing',
            'SenderSoftwareVersionRelease', 20170715
        ),
        'RxReferenceNumber', '!',
        'PrescriberOrderNumber', 'CORE RENEWAL-2 MAX-POP TEST CASE #2'
    );
    
    -- 2. Build the Patient section
    v_patient_json := jsonb_build_object(
        'HumanPatient', jsonb_build_object(
            'Name', jsonb_build_object(
                'LastName', 'Usumacintacoatzacoalcosniltepecvera',
                'FirstName', 'Juancarlosguadalupepaploapan',
                'MiddleName', 'Franciscolisandroculiacan',
                'Suffix', 'Junior'
            ),
            'Gender', 'M',
            'DateOfBirth', jsonb_build_object(
                'Date', '2004-06-21'
            ),
            'Address', jsonb_build_object(
                'AddressLine1', '27732 West Alameda Potholeladen Street',
                'AddressLine2', 'Apt 425-B',
                'City', 'Rancho Cucamonga',
                'StateProvince', 'CA',
                'PostalCode', 917011515,
                'CountryCode', 'US'
            ),
            'CommunicationNumbers', jsonb_build_object(
                'PrimaryTelephone', jsonb_build_object(
                    'Number', **********,
                    'SupportsSMS', 'Y'
                ),
                'ElectronicMail', '<EMAIL>',
                'HomeTelephone', jsonb_build_object(
                    'Number', 7075576314
                ),
                'WorkTelephone', jsonb_build_object(
                    'Number', **********,
                    'Extension', 45422142,
                    'SupportsSMS', 'N'
                ),
                'OtherTelephone', jsonb_build_object(
                    'Number', **********
                )
            )
        )
    );
    
    -- 3. Build the Pharmacy section
    v_pharmacy_json := jsonb_build_object(
        'Identification', jsonb_build_object(
            'NCPDPID', 2455142,
            'StateLicenseNumber', '796597%PH12%82R',
            'MedicareNumber', 886112,
            'MedicaidNumber', 886112,
            'DEANumber', '*********',
            'NPI', **********
        ),
        'BusinessName', 'Medi-Blue Rapid Clinic (000)',
        'Address', jsonb_build_object(
            'AddressLine1', '2165-B1 Northpoint Parkway',
            'City', 'Santa Rosa',
            'StateProvince', 'CA',
            'PostalCode', 95407,
            'CountryCode', 'US'
        ),
        'CommunicationNumbers', jsonb_build_object(
            'PrimaryTelephone', jsonb_build_object(
                'Number', **********
            ),
            'Fax', jsonb_build_object(
                'Number', **********
            )
        )
    );
    
    -- 4. Build the Prescriber section
    v_prescriber_json := jsonb_build_object(
        'NonVeterinarian', jsonb_build_object(
            'Identification', jsonb_build_object(
                'StateLicenseNumber', '784577%1142%14',
                'MedicaidNumber', 654745,
                'UPIN', 0,
                'DEANumber', '*********',
                'HIN', 0,
                'NPI', 222222,
                'CertificateToPrescribe', 'CTP.CA.1142%14'
            ),
            'Specialty', '363L00000X',
            'PracticeLocation', jsonb_build_object(
                'BusinessName', 'MediStar of California'
            ),
            'Name', jsonb_build_object(
                'LastName', 'Thomas',
                'FirstName', 'Walden',
                'MiddleName', 'Macnair',
                'Suffix', 'NP'
            ),
            'FormerName', jsonb_build_object(
                'LastName', 'Macnair',
                'FirstName', 'Walden',
                'MiddleName', 'Robert'
            ),
            'Address', jsonb_build_object(
                'AddressLine1', '1425 Mendocino Ave',
                'AddressLine2', 'Suite 12-A',
                'City', 'Santa Rosa',
                'StateProvince', 'CA',
                'PostalCode', 95401,
                'CountryCode', 'US'
            ),
            'CommunicationNumbers', jsonb_build_object(
                'PrimaryTelephone', jsonb_build_object(
                    'Number', **********,
                    'Extension', 4221
                ),
                'ElectronicMail', '<EMAIL>',
                'Fax', jsonb_build_object(
                    'Number', **********
                ),
                'HomeTelephone', jsonb_build_object(
                    'Number', **********,
                    'SupportsSMS', 'Y'
                )
            )
        )
    );
    
    -- 5. Build the Supervisor section
    v_supervisor_json := jsonb_build_object(
        'NonVeterinarian', jsonb_build_object(
            'Identification', jsonb_build_object(
                'NPI', **********
            ),
            'Name', jsonb_build_object(
                'LastName', 'Smith',
                'FirstName', 'John',
                'MiddleName', 'S',
                'Suffix', 'Jr',
                'Prefix', 'Mr'
            ),
            'CommunicationNumbers', jsonb_build_object(
                'PrimaryTelephone', jsonb_build_object(
                    'Number', **********
                )
            )
        )
    );
    
    -- 6. Build the Response section
    v_response_json := jsonb_build_object(
        'ApprovedWithChanges', jsonb_build_object(
            'Note', '1 refill approved by prescriber'
        )
    );
    
    -- 7. Build the Observations section
    v_observations_json := jsonb_build_object(
        'Measurement', jsonb_build_array(
            jsonb_build_object(
                'VitalSign', '8302-2',
                'LOINCVersion', 2.42,
                'Value', 59,
                'UnitOfMeasure', '[in_i]',
                'UCUMVersion', 2.1,
                'ObservationDate', jsonb_build_object(
                    'Date', '2018-01-20'
                )
            ),
            jsonb_build_object(
                'VitalSign', '29463-7',
                'LOINCVersion', 2.42,
                'Value', 120,
                'UnitOfMeasure', '[lb_av]',
                'UCUMVersion', 2.1,
                'ObservationDate', jsonb_build_object(
                    'Date', '2018-01-23'
                )
            )
        )
    );
    
    -- 8. Build the Allergy section
    v_allergy_json := jsonb_build_object(
        'Allergies', jsonb_build_object(
            'SourceOfInformation', 'P',
            'AdverseEvent', jsonb_build_object(
                'Text', 'Allergy to drug',
                'Code', 419511003
            ),
            'DrugProductCoded', jsonb_build_object(
                'Code', 904272561,
                'Qualifier', 'ND',
                'Text', 'Sulfamethoxazole and trimethoprim'
            ),
            'ReactionCoded', jsonb_build_object(
                'Text', 'Hives',
                'Code', 247472004
            ),
            'SeverityCoded', jsonb_build_object(
                'Text', 'Moderate',
                'Code', 6736007
            )
        )
    );

    -- 9. Build the FollowUpPrescriber section
    v_followup_prescriber_json := jsonb_build_object(
        'NonVeterinarian', jsonb_build_object(
            'Identification', jsonb_build_object(
                'StateLicenseNumber', '784577%1147%24',
                'MedicaidNumber', 658345,
                'DEANumber', '*********',
                'NPI', 222222,
                'CertificateToPrescribe', 'CTP.CA.1147%24'
            ),
            'Name', jsonb_build_object(
                'LastName', 'Pimpernell',
                'FirstName', 'Marguerite',
                'MiddleName', 'Anne',
                'Suffix', 'MD',
                'Prefix', 'DR'
            ),
            'CommunicationNumbers', jsonb_build_object(
                'PrimaryTelephone', jsonb_build_object(
                    'Number', **********,
                    'Extension', 4221
                ),
                'ElectronicMail', '<EMAIL>',
                'Fax', jsonb_build_object(
                    'Number', **********
                )
            )
        )
    );

    compound_ingredients := jsonb_build_array(
        jsonb_build_object(
            'CompoundIngredient', jsonb_build_object(
                'CompoundIngredientItemDescription', 'Diphenhydramine 12.5 mg/5 mL',
                'Strength', jsonb_build_object(
                    'StrengthValue', 12.5,
                    'StrengthForm', jsonb_build_object(
                        'Code', 'C42986'
                    ),
                    'StrengthUnitOfMeasure', jsonb_build_object(
                        'Code', 'C91131'
                    )
                )
            ),
            'Quantity', jsonb_build_object(
                'Value', 300,
                'CodeListQualifier', 38,
                'QuantityUnitOfMeasure', jsonb_build_object(
                    'Code', 'C28254'
                )
            )
        ),
        jsonb_build_object(
            'CompoundIngredient', jsonb_build_object(
                'CompoundIngredientItemDescription', 'Viscous lidocaine 2%',
                'Strength', jsonb_build_object(
                    'StrengthValue', 2,
                    'StrengthForm', jsonb_build_object(
                        'Code', 'C42986'
                    ),
                    'StrengthUnitOfMeasure', jsonb_build_object(
                        'Code', 'C25613'
                    )
                )
            ),
            'Quantity', jsonb_build_object(
                'Value', 300,
                'CodeListQualifier', 38,
                'QuantityUnitOfMeasure', jsonb_build_object(
                    'Code', 'C28254'
                )
            )
        ),
        jsonb_build_object(
            'CompoundIngredient', jsonb_build_object(
                'CompoundIngredientItemDescription', 'Maalox oral suspension',
                'Strength', jsonb_build_object(
                    'StrengthValue', '200/200/20',
                    'StrengthForm', jsonb_build_object(
                        'Code', 'C68992'
                    ),
                    'StrengthUnitOfMeasure', jsonb_build_object(
                        'Code', 'C91131'
                    )
                )
            ),
            'Quantity', jsonb_build_object(
                'Value', 300,
                'CodeListQualifier', 38,
                'QuantityUnitOfMeasure', jsonb_build_object(
                    'Code', 'C28254'
                )
            )
        )
    );
    v_medication_json := jsonb_build_object(
        'DrugDescription', 'Magic Mouthwash Diphenhydramine 12.5 mg/5 mL, Viscous lidocaine 2%, Maalox 1 part',
        'Quantity', jsonb_build_object(
            'Value', 900,
            'CodeListQualifier', 'CF',
            'QuantityUnitOfMeasure', jsonb_build_object(
                'Code', 'C28254'
            )
        ),
        'DaysSupply', 30,
        'WrittenDate', jsonb_build_object(
            'Date', '2019-01-01'
        ),
        'Substitutions', 0,
        'NumberOfRefills', 1,
        'Diagnosis', jsonb_build_object(
            'ClinicalInformationQualifier', 2,
            'Primary', jsonb_build_object(
                'Code', 'K1233',
                'Qualifier', 'ABF',
                'Description', 'Oral mucositis (ulcerative) due to radiation'
            ),
            'Secondary', jsonb_build_object(
                'Code', 'Z510',
                'Qualifier', 'ABF',
                'Description', 'Encounter for antineoplastic radiation therapy'
            )
        ),
        'Note', 'Patient requested peppermint flavoring if possible. Please provide appropriate documentation to patient for how to use this product, including not swallowing solution. A child-resistant package also requested.',
        'Sig', jsonb_build_object(
            'SigText', 'Swish and spit 15 mL orally for 1 minute every 12 hours',
            'CodeSystem', jsonb_build_object(
                'SNOMEDVersion', 20170131,
                'FMTVersion', '19.12e'
            ),
            'Instruction', jsonb_build_object(
                'DoseAdministration', jsonb_build_object(
                    'DoseDeliveryMethod', jsonb_build_object(
                        'Text', 'Swish',
                        'Qualifier', 'SNOMED',
                        'Code', 421805007
                    ),
                    'Dosage', jsonb_build_object(
                        'DoseQuantity', 15,
                        'DoseUnitOfMeasure', jsonb_build_object(
                            'Text', 'mL',
                            'Qualifier', 'DoseUnitOfMeasure',
                            'Code', 'C28254'
                        )
                    ),
                    'RouteOfAdministration', jsonb_build_object(
                        'Text', 'oral route',
                        'Qualifier', 'SNOMED',
                        'Code', 26643006
                    )
                ),
                'TimingAndDuration', jsonb_build_object(
                    'Interval', jsonb_build_object(
                        'IntervalNumericValue', 12,
                        'IntervalUnits', jsonb_build_object(
                            'Text', 'hours',
                            'Qualifier', 'SNOMED',
                            'Code', 307467005
                        )
                    ),
                    'TimingClarifyingFreeText', 'for 1 minute every 12 hours'
                )
            )
        ),
        'RxFillIndicator', 'Not Dispensed',
        'CompoundInformation', jsonb_build_object(
            'FinalCompoundPharmaceuticalDosageForm', 'C68996',
            'CompoundIngredientsLotNotUsed', compound_ingredients
        )
    );
    
    -- 11. Assemble the complete message JSON
    v_message_json := jsonb_build_object(
            '?xml', '',
            'Message', jsonb_build_object(
                'Header', v_header_json,
                'Body', jsonb_build_object(
                    'RxRenewalResponse', jsonb_build_object(
                        'Response', v_response_json,
                        'AllergyOrAdverseEvent', v_allergy_json,
                        'Patient', v_patient_json,
                        'Pharmacy', v_pharmacy_json,
                        'Prescriber', v_prescriber_json,
                        'Observation', v_observations_json,
                        'Supervisor', v_supervisor_json,
                        'MedicationResponse', v_medication_json,
                        'FollowUpPrescriber', v_followup_prescriber_json
                    )
                )
            )
    );

    -- Set the message_id in the JSON to the generated value
    v_message_json := jsonb_set(v_message_json, '{json_data,Message,Header,MessageID}', to_jsonb(message_id_value));

    -- Extract sub-objects for processing
    v_header_json := v_message_json->'Message'->'Header';
    v_patient_json := v_message_json->'Message'->'Body'->'RxRenewalResponse'->'Patient';
    v_prescriber_json := v_message_json->'Message'->'Body'->'RxRenewalResponse'->'Prescriber';
    v_supervisor_json := v_message_json->'Message'->'Body'->'RxRenewalResponse'->'Supervisor';
    v_pharmacy_json := v_message_json->'Message'->'Body'->'RxRenewalResponse'->'Pharmacy';
    v_response_json := v_message_json->'Message'->'Body'->'RxRenewalResponse'->'Response';

    -- Process the message
    SELECT tp.ss_message_id, tp.error_message INTO v_ss_message_id, error_message
    FROM _parse_and_insert_rxrenewalresponse(
        p_ss_log_id := 99999996, -- Fixed log ID for testing
        p_message_json := v_message_json::JSONB,
        p_surescripts_message_type := 'RxRenewalResponse'::TEXT,
        p_header_data := v_header_json::JSONB,
        p_patient_data := v_patient_json::JSONB,
        p_prescriber_data := v_prescriber_json::JSONB,
        p_priority_flag := NULL::TEXT -- Added to match function definition
    ) tp;

    IF error_message IS NOT NULL THEN
        RAISE EXCEPTION 'Parser error: %', error_message;
    END IF;

    RAISE NOTICE 'Processed RxRenewalResponse message, ss_message_id: % using Cert_RenewalRES-2.json', v_ss_message_id;
    -- Assertions for form_ss_message
    PERFORM assert_not_null(v_ss_message_id::TEXT,'ss_message_id');

    -- Header fields
    PERFORM assert_equals('111111',(SELECT send_to FROM form_ss_message WHERE id = v_ss_message_id),'send_to');
    PERFORM assert_equals('6997083822001',(SELECT sent_from FROM form_ss_message WHERE id = v_ss_message_id),'sent_from');
    PERFORM assert_equals(message_id_value,(SELECT message_id FROM form_ss_message WHERE id = v_ss_message_id),'message_id');
    PERFORM assert_equals('!',(SELECT related_message_id FROM form_ss_message WHERE id = v_ss_message_id),'related_message_id');
    PERFORM assert_equals('2019-01-01 16:28:56.7',(SELECT sent_dt::TEXT FROM form_ss_message WHERE id = v_ss_message_id),'sent_dt');
    PERFORM assert_equals('Surescripts',(SELECT sender_software_developer FROM form_ss_message WHERE id = v_ss_message_id),'sender_software_developer');
    PERFORM assert_equals('Certification Testing',(SELECT sender_software_product FROM form_ss_message WHERE id = v_ss_message_id),'sender_software_product');
    PERFORM assert_equals('20170715',(SELECT sender_software_version FROM form_ss_message WHERE id = v_ss_message_id),'sender_software_version');
    PERFORM assert_equals('CORE RENEWAL-2 MAX-POP TEST CASE #2',(SELECT physician_order_id FROM form_ss_message WHERE id = v_ss_message_id),'physician_order_id');
    PERFORM assert_equals('!',(SELECT pharmacy_rx_no FROM form_ss_message WHERE id = v_ss_message_id),'pharmacy_rx_no');
    PERFORM assert_equals('RxRenewalResponse',(SELECT message_type FROM form_ss_message WHERE id = v_ss_message_id),'message_type');
    PERFORM assert_equals('IN',(SELECT direction FROM form_ss_message WHERE id = v_ss_message_id),'direction');

    -- RxRenewalResponse specific fields
    PERFORM assert_equals('ApprovedWithChanges',(SELECT renewal_status FROM form_ss_message WHERE id = v_ss_message_id),'renewal_status (ApprovedWithChanges)');
    PERFORM assert_equals('1 refill approved by prescriber',(SELECT renewal_note FROM form_ss_message WHERE id = v_ss_message_id),'renewal_note');

    -- Patient fields
    PERFORM assert_equals('Usumacintacoatzacoalcosniltepecvera, Juancarlosguadalupepaploapan Franciscolisandroculiacan',(SELECT patient_name_display FROM form_ss_message WHERE id = v_ss_message_id),'patient_name_display');
    PERFORM assert_equals('Juancarlosguadalupepaploapan',(SELECT patient_first_name FROM form_ss_message WHERE id = v_ss_message_id),'patient_first_name');
    PERFORM assert_equals('Usumacintacoatzacoalcosniltepecvera',(SELECT patient_last_name FROM form_ss_message WHERE id = v_ss_message_id),'patient_last_name');
    PERFORM assert_equals('2004-06-21',(SELECT patient_dob::TEXT FROM form_ss_message WHERE id = v_ss_message_id),'patient_dob');
    PERFORM assert_equals('M',(SELECT patient_gender FROM form_ss_message WHERE id = v_ss_message_id),'patient_gender');

    -- Prescriber fields
    PERFORM assert_equals('Thomas, Walden',(SELECT prescriber_name_display FROM form_ss_message WHERE id = v_ss_message_id),'prescriber_name_display');
    PERFORM assert_equals('222222',(SELECT prescriber_npi FROM form_ss_message WHERE id = v_ss_message_id),'prescriber_npi');
    PERFORM assert_equals('*********',(SELECT prescriber_dea FROM form_ss_message WHERE id = v_ss_message_id),'prescriber_dea');

    -- Medication fields
    PERFORM assert_equals('Magic Mouthwash Diphenhydramine 12.5 mg/5 mL, Viscous lidocaine 2%, Maalox 1 part',(SELECT description FROM form_ss_message WHERE id = v_ss_message_id),'description');
    PERFORM assert_equals('900',(SELECT quantity::TEXT FROM form_ss_message WHERE id = v_ss_message_id),'quantity');
    PERFORM assert_equals('C28254',(SELECT quantity_uom_id FROM form_ss_message WHERE id = v_ss_message_id),'quantity_uom_id');
    PERFORM assert_equals('30',(SELECT days_supply::TEXT FROM form_ss_message WHERE id = v_ss_message_id),'days_supply');
    PERFORM assert_equals('2019-01-01',(SELECT written_date::TEXT FROM form_ss_message WHERE id = v_ss_message_id),'written_date');
    PERFORM assert_equals('1',(SELECT refills::TEXT FROM form_ss_message WHERE id = v_ss_message_id),'refills');
    PERFORM assert_equals('Patient requested peppermint flavoring if possible. Please provide appropriate documentation to patient for how to use this product, including not swallowing solution. A child-resistant package also requested.',(SELECT note FROM form_ss_message WHERE id = v_ss_message_id),'note'); -- This is the MedicationPrescribed.Note not Response.Note
    PERFORM assert_equals('Swish and spit 15 mL orally for 1 minute every 12 hours',(SELECT sig FROM form_ss_message WHERE id = v_ss_message_id),'sig');

    -- Status Icons and Show Options
    SELECT status_icons INTO v_temp_text_array FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_text_array_equals(ARRAY['new', 'approved'], v_temp_text_array, 'status_icons');
    
    SELECT show_options INTO v_temp_text_array FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_text_array_equals(ARRAY['Renewal Response', 'Supervisor', 'Follow-up Prescriber', 'Supervisor', 'Follow-up Prescriber', 'Practice Location'], v_temp_text_array, 'show_options');

    -- Subform Counts
    PERFORM assert_equals('2',(SELECT COUNT(*)::TEXT FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = v_ss_message_id),'form_ss_diagnosis count');
    PERFORM assert_equals('0',(SELECT COUNT(*)::TEXT FROM form_ss_due d JOIN sf_form_ss_message_to_ss_due sf ON d.id = sf.form_ss_due_fk WHERE sf.form_ss_message_fk = v_ss_message_id),'form_ss_due count');

    -- Assertions for Supervisor Information (present in RenewalResponse)
    PERFORM assert_equals('**********',(SELECT supervisor_npi::text FROM form_ss_message WHERE id = v_ss_message_id),'supervisor_npi');
    PERFORM assert_equals('Smith',(SELECT supervisor_last_name::text FROM form_ss_message WHERE id = v_ss_message_id),'supervisor_last_name');
    PERFORM assert_equals('John',(SELECT supervisor_first_name::text FROM form_ss_message WHERE id = v_ss_message_id),'supervisor_first_name');
    PERFORM assert_equals('**********',(SELECT supervisor_phone::text FROM form_ss_message WHERE id = v_ss_message_id),'supervisor_phone');

    -- Assertions for Pharmacy Information
    PERFORM assert_equals('1',(SELECT refills::TEXT FROM form_ss_message WHERE id = v_ss_message_id),'refills');
    PERFORM assert_equals('Patient requested peppermint flavoring if possible. Please provide appropriate documentation to patient for how to use this product, including not swallowing solution. A child-resistant package also requested.',(SELECT note FROM form_ss_message WHERE id = v_ss_message_id),'note');
    PERFORM assert_equals('Swish and spit 15 mL orally for 1 minute every 12 hours',(SELECT sig FROM form_ss_message WHERE id = v_ss_message_id),'sig');
    PERFORM assert_equals('2',(SELECT clinical_info_qualifier FROM form_ss_message WHERE id = v_ss_message_id),'clinical_info_qualifier');

    -- Detailed Diagnosis Checks
    FOR v_diag_record IN SELECT type, dx_code, dx_code_qualifier_id, dx_desc FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = v_ss_message_id ORDER BY type
    LOOP
        IF v_diag_record.type = 'Primary' THEN
            PERFORM assert_equals('K1233',v_diag_record.dx_code,'Primary Diagnosis dx_code');
            PERFORM assert_equals('ABF',v_diag_record.dx_code_qualifier_id,'Primary Diagnosis dx_code_qualifier_id');
            PERFORM assert_equals('Oral mucositis (ulcerative) due to radiation',v_diag_record.dx_desc,'Primary Diagnosis dx_desc');
        ELSIF v_diag_record.type = 'Secondary' THEN
            PERFORM assert_equals('Z510',v_diag_record.dx_code,'Secondary Diagnosis dx_code');
            PERFORM assert_equals('ABF',v_diag_record.dx_code_qualifier_id,'Secondary Diagnosis dx_code_qualifier_id');
            PERFORM assert_equals('Encounter for antineoplastic radiation therapy',v_diag_record.dx_desc,'Secondary Diagnosis dx_desc');
        END IF;
    END LOOP;
    RAISE NOTICE 'All max-pop RenewalResponse validations passed!';

END;
$$; 