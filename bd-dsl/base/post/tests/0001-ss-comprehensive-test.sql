-- 0000-ss-comprehensive-test.sql
-- This file tests the parsing of all SureScripts message types with complete data
-- It inserts test messages for each type and validates the parsing

-- Helper function for assertions
CREATE OR REPLACE FUNCTION assert_equals(expected TEXT, actual TEXT, field_name TEXT) 
RETURNS VOID AS $$
BEGIN
    IF expected IS DISTINCT FROM actual THEN
        RAISE EXCEPTION 'Assertion failed for %: expected "%" but got "%"', field_name, expected, actual;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Test NewRx message type
DO $$
DECLARE
    json_template TEXT := '{ 
      "?xml": "", 
      "Message": { 
        "Header": { 
          "To": 111111, 
          "From": 1260306616002, 
          "MessageID": "%s", 
          "SentTime": "2023-05-01T13:42:39.7Z", 
          "SenderSoftware": { "SenderSoftwareDeveloper": "Surescripts", "SenderSoftwareProduct": "Certification Testing", "SenderSoftwareVersionRelease": "20170715" }, 
          "PrescriberOrderNumber": "COMPREHENSIVE_TEST_NEWRX" 
        }, 
        "Body": { 
          "NewRx": { 
            "Patient": { 
              "HumanPatient": { 
                "Name": { "LastName": "CompTest", "FirstName": "Patient", "MiddleName": "Middle" }, 
                "Gender": "M", 
                "DateOfBirth": { "Date": "1980-01-01" },
                "Address": { 
                  "AddressLine1": "123 Patient St", 
                  "AddressLine2": "Apt 456", 
                  "City": "Patientville", 
                  "StateProvince": "CA", 
                  "PostalCode": "12345", 
                  "CountryCode": "US" 
                },
                "Identification": {
                  "SocialSecurity": "***********",
                  "MedicalRecordIdentificationNumber": "MRN12345",
                  "MedicareNumber": "MEDICARE12345",
                  "MedicaidNumber": "MEDICAID12345"
                }
              } 
            }, 
            "Prescriber": { 
              "NonVeterinarian": { 
                "Identification": { 
                  "StateLicenseNumber": "SL12345", 
                  "StateControlledSubstanceNumber": "CS12345",
                  "MedicareNumber": "MC12345", 
                  "MedicaidNumber": "MD12345", 
                  "DEANumber": "DE12345", 
                  "NPI": "**********", 
                  "CertificateToPrescribe": "CP12345",
                  "Data2000WaiverID": "DW12345",
                  "REMSHealthcareSettingEnrollmentID": "REMS12345"
                }, 
                "Specialty": "363L00000X", 
                "PracticeLocation": { 
                  "BusinessName": "Comprehensive Test Practice",
                  "Identification": {
                    "NCPDPID": "PL12345",
                    "DEANumber": "PL-DEA12345",
                    "REMSHealthcareSettingEnrollmentID": "PL-REMS12345",
                    "StateControlledSubstanceNumber": "PL-CS12345",
                    "MedicareNumber": "PL-MC12345",
                    "MedicaidNumber": "PL-MD12345",
                    "StateLicenseNumber": "PL-SL12345"
                  }
                }, 
                "Name": { "LastName": "CompDoctor", "FirstName": "Test", "MiddleName": "M", "Suffix": "MD" }, 
                "Address": { 
                  "AddressLine1": "123 Doctor St", 
                  "AddressLine2": "Suite 100", 
                  "City": "Doctorville", 
                  "StateProvince": "CA", 
                  "PostalCode": "54321", 
                  "CountryCode": "US" 
                }, 
                "CommunicationNumbers": { 
                  "PrimaryTelephone": { "Number": "**********", "Extension": "123" }, 
                  "ElectronicMail": "<EMAIL>", 
                  "Fax": { "Number": "**********" }
                },
                "PrescriberAgent": {
                  "Name": { "LastName": "AgentLast", "FirstName": "AgentFirst" }
                }
              } 
            }, 
            "Supervisor": {
              "NonVeterinarian": {
                "Identification": { "NPI": "**********" },
                "Name": { "LastName": "SupervisorLast", "FirstName": "SupervisorFirst" }
              }
            },
            "MedicationPrescribed": { 
              "DrugDescription": "Comprehensive Test Med 20mg", 
              "DrugCoded": {
                "ProductCode": { "Code": "**********1", "Qualifier": "ND" },
                "DrugDBCode": { "Code": "123456", "Qualifier": "SCD" }
              },
              "Quantity": { "Value": 30, "CodeListQualifier": "38", "QuantityUnitOfMeasure": { "Code": "C48480" } }, 
              "DaysSupply": 30,
              "WrittenDate": { "Date": "2023-05-01" }, 
              "Substitutions": "0", 
              "NumberOfRefills": 3,
              "Diagnosis": {
                "ClinicalInformationQualifier": "1",
                "Primary": { "Code": "J45.909", "Qualifier": "ABF", "Description": "Unspecified asthma, uncomplicated" },
                "Secondary": { "Code": "R05", "Qualifier": "ABF", "Description": "Cough" }
              },
              "Sig": { "SigText": "Take 1 tablet daily" } 
            },
            "Pharmacy": {
              "Identification": { "NCPDPID": "1234567", "NPI": "**********" },
              "BusinessName": "Test Pharmacy",
              "Address": { 
                "AddressLine1": "789 Pharmacy Rd", 
                "City": "Pharmville", 
                "StateProvince": "CA", 
                "PostalCode": "98765", 
                "CountryCode": "US" 
              },
              "CommunicationNumbers": { 
                "PrimaryTelephone": { "Number": "**********" }, 
                "Fax": { "Number": "**********" } 
              }
            },
            "BenefitsCoordination": {
              "PayerIdentification": { "PayerID": "PAYER123", "IINNumber": "123456" },
              "PayerName": "Test Insurance",
              "CardholderID": "CARD123456",
              "GroupID": "GROUP123",
              "GroupName": "Test Group",
              "PBMMemberID": "PBM123456"
            }
          } 
        } 
      }
    }';
    json_content_final TEXT;
    message_id_value TEXT;
    message_id_unique_suffix TEXT := gen_random_uuid()::text;
    log_id INTEGER;
    ss_message_id INTEGER;
BEGIN
    message_id_value := 'COMPREHENSIVE_NEWRX_' || message_id_unique_suffix;
    json_content_final := format(json_template, message_id_value);
    
    -- Insert the test message into form_ss_log
    INSERT INTO form_ss_log (message_json, direction, message_type, message_id, created_by, updated_by)
    VALUES (json_content_final::jsonb, 'IN', 'NewRx', 'COMPREHENSIVE_NEWRX_' || message_id_unique_suffix, 1, 1)
    RETURNING id INTO log_id;
    
    RAISE NOTICE 'Inserted comprehensive NewRx test message into form_ss_log with ID: %', log_id;
    
    -- Process the message
    PERFORM parse_ss_inbound(log_id);
    
    -- Get the resulting ss_message_id
    SELECT id INTO ss_message_id FROM form_ss_message WHERE message_id = 'COMPREHENSIVE_NEWRX_' || message_id_unique_suffix;
    
    -- Validate the message was processed
    IF ss_message_id IS NULL THEN
        RAISE EXCEPTION 'Failed to process the comprehensive NewRx test message - no form_ss_message record created';
    ELSE
        RAISE NOTICE 'Successfully processed comprehensive NewRx test message - form_ss_message ID: %', ss_message_id;
        
        -- Validate key fields
        PERFORM assert_equals('NewRx', (SELECT message_type FROM form_ss_message WHERE id = ss_message_id), 'Message Type');
        PERFORM assert_equals('CompTest', (SELECT patient_last_name FROM form_ss_message WHERE id = ss_message_id), 'Patient Last Name');
        PERFORM assert_equals('Patient', (SELECT patient_first_name FROM form_ss_message WHERE id = ss_message_id), 'Patient First Name');
        PERFORM assert_equals('CompDoctor', (SELECT prescriber_last_name FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Last Name');
        PERFORM assert_equals('**********', (SELECT prescriber_npi FROM form_ss_message WHERE id = ss_message_id), 'Prescriber NPI');
        PERFORM assert_equals('REMS12345', (SELECT prescriber_rems FROM form_ss_message WHERE id = ss_message_id), 'Prescriber REMS');
        PERFORM assert_equals('Comprehensive Test Med 20mg', (SELECT description FROM form_ss_message WHERE id = ss_message_id), 'Medication Description');
        
        -- Check if diagnoses were parsed correctly
        DECLARE
            diagnosis_count INTEGER;
        BEGIN
            SELECT COUNT(*) INTO diagnosis_count 
            FROM form_ss_diagnosis d
            JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk
            WHERE sf.form_ss_message_fk = ss_message_id;
            
            IF diagnosis_count = 2 THEN
                RAISE NOTICE 'Successfully parsed 2 diagnoses';
            ELSE
                RAISE EXCEPTION 'Expected 2 diagnoses but found %', diagnosis_count;
            END IF;
        END;
        
        RAISE NOTICE 'All comprehensive NewRx validations passed!';
    END IF;
    
    -- Clean up the test data (COMMENTED OUT FOR DEBUGGING)
    -- DELETE FROM sf_form_ss_message_to_ss_diagnosis WHERE form_ss_message_fk = ss_message_id;
    -- DELETE FROM form_ss_diagnosis WHERE id IN (
    --     SELECT form_ss_diagnosis_fk FROM sf_form_ss_message_to_ss_diagnosis WHERE form_ss_message_fk = ss_message_id
    -- );
    -- DELETE FROM form_ss_message WHERE id = ss_message_id;
    -- DELETE FROM form_ss_log WHERE id = log_id;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error during comprehensive NewRx test: %', SQLERRM;
        -- Clean up any test data that might have been created (COMMENTED OUT FOR DEBUGGING)
        -- DELETE FROM sf_form_ss_message_to_ss_diagnosis WHERE form_ss_message_fk = ss_message_id;
        -- DELETE FROM form_ss_diagnosis WHERE id IN (
        --     SELECT form_ss_diagnosis_fk FROM sf_form_ss_message_to_ss_diagnosis WHERE form_ss_message_fk = ss_message_id
        -- );
        -- DELETE FROM form_ss_message WHERE message_id = 'COMPREHENSIVE_NEWRX_' || message_id_unique_suffix;
        -- DELETE FROM form_ss_log WHERE id = log_id;
        RAISE;
END $$;
