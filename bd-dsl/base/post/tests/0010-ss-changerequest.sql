DO $$
DECLARE
    v_outbound_ss_message_id INTEGER;
    v_generated_payload JSONB;
    v_expected_payload JSONB;
    v_site_id_from INTEGER; -- Pharmacy sending the ChgReq
    v_site_id_to INTEGER;   -- Prescriber system/PBM receiving ChgReq (used for To field)
    v_chg_med_id INTEGER;
    v_due_id INTEGER;
    v_allergy_id INTEGER;
    v_benefit_id INTEGER;
    v_observation_id INTEGER;
    v_diagnosis_id INTEGER;

BEGIN
    -- 0. Get site IDs 
    SELECT id INTO v_site_id_from FROM form_site WHERE ncpdp_id = '6301875277001' LIMIT 1; -- <PERSON><PERSON><PERSON><PERSON> Pharmacy from Cert_ChangeREQ-5
    IF v_site_id_from IS NULL THEN RAISE EXCEPTION 'Sender Pharmacy site 1655458 not found.'; END IF;
    -- 1. Setup: Create the form_ss_message record for the outbound RxChangeRequest.
    -- Based on surescripts/test_scripts/outbound/Cert_ChangeREQ-5.xml
    INSERT INTO form_ss_message (
        direction,
        message_type,
        message_id,
        send_to,
        sent_from, -- sent_from here is our pharmacy NCPDPID
        physician_order_id,
        pharmacy_rx_no, -- RxReferenceNumber is pharmacy_rx_no
        chg_type_id, -- MessageRequestCode
        patient_last_name,
        patient_first_name,
        patient_gender,
        patient_dob,
        patient_home_street_1,
        patient_home_city,
        patient_home_state,
        patient_home_zip,
        patient_phone,
        prescriber_last_name,
        prescriber_first_name,
        prescriber_npi,
        prescriber_state_lic,
        prescriber_medicaid,
        prescriber_dea,
        prescriber_address_1,
        prescriber_city,
        prescriber_state,
        prescriber_zip,
        prescriber_phone,
        prescriber_fax,
        prescriber_loc_name,
        description,
        product_code,
        product_code_qualifier_id,
        drug_db_code,
        drug_db_qualifier_id,
        quantity,
        quantity_uom_id,
        written_date,
        daw,
        refills,
        sig,
        note,
        clinical_info_qualifier,
        created_by,
        updated_by,
        processed,
        site_id 
    )
    VALUES (
        'OUT',                                      -- direction
        'RxChangeRequest',                          -- message_type
        'OUT_CHGREQ5_' || gen_random_uuid()::text,  -- message_id
        '111111',                                   -- send_to
        '6301875277001',                            -- sent_from
        'PON from NewRx',                           -- physician_order_id
        'RxReference # for Change-5',               -- pharmacy_rx_no
        'D',                                        -- chg_type_id (MessageRequestCode)
        'Delaplaine',                               -- patient_last_name
        'Zachary',                                  -- patient_first_name
        'M',                                        -- patient_gender
        '2010-12-01',                               -- patient_dob
        '901 Sauvblanc Blvd',                       -- patient_home_street_1
        'Petaluma',                                 -- patient_home_city
        'CA',                                       -- patient_home_state
        '94952',                                    -- patient_home_zip
        '**********',                               -- patient_phone
        'Thomas',                                   -- prescriber_last_name
        'Walden',                                   -- prescriber_first_name
        '222222',                                   -- prescriber_npi
        '784577%1142%14',                           -- prescriber_state_lic
        '654745',                                   -- prescriber_medicaid
        '*********',                                -- prescriber_dea
        '2165-B1 Northpoint Parkway',               -- prescriber_address_1
        'Santa Rosa',                               -- prescriber_city
        'CA',                                       -- prescriber_state
        '95407',                                    -- prescriber_zip
        '**********',                               -- prescriber_phone
        '**********',                               -- prescriber_fax
        'Medi-Blue Rapid Clinic',                   -- prescriber_loc_name
        'Tobramycin and Dexamethasone Ophthalmic Suspension, USP 0.3%/0.1%', -- description
        '24208029510',                              -- product_code
        'ND',                                       -- product_code_qualifier_id
        '309683',                                   -- drug_db_code
        'SCD',                                      -- drug_db_qualifier_id
        10,                                         -- quantity
        'C28254',                                   -- quantity_uom_id
        '2019-01-01',                               -- written_date
        '0',                                        -- daw
        1,                                          -- refills
        'Instill 1 drop into affected eye every 6 hours for 7 days', -- sig
        NULL,                                       -- note
        '1',                                        -- clinical_info_qualifier
        1,                                          -- created_by
        1,                                          -- updated_by
        'No',                                       -- processed
        v_site_id_from                              -- site_id
    ) RETURNING id INTO v_outbound_ss_message_id;

    -- Insert primary diagnosis for MedicationPrescribed
    INSERT INTO form_ss_diagnosis (type, dx_code, dx_code_qualifier_id, dx_desc, created_by, updated_by)
    VALUES ('Primary', 'H10502', 'ABF', 'Unspecified blepharoconjunctivitis, left eye', 1,1) RETURNING id INTO v_diagnosis_id;
    INSERT INTO sf_form_ss_message_to_ss_diagnosis (form_ss_message_fk, form_ss_diagnosis_fk) VALUES (v_outbound_ss_message_id, v_diagnosis_id);

    -- Insert Observation data (Cert_ChangeREQ-5 has two observations)
    INSERT INTO form_ss_observation (type_id, loinc_version, value, unit_id, ucum_version, observation_date, created_by, updated_by)
    VALUES ('8302-2', '2.42', 51, '[in_i]', '2.1', '2018-01-26', 1,1) RETURNING id INTO v_observation_id;
    INSERT INTO sf_form_ss_message_to_ss_observation (form_ss_message_fk, form_ss_observation_fk) VALUES (v_outbound_ss_message_id, v_observation_id);
    
    INSERT INTO form_ss_observation (type_id, loinc_version, value, unit_id, ucum_version, observation_date, created_by, updated_by)
    VALUES ('29463-7', '2.42', 62, '[lb_av]', '2.1', '2018-01-26', 1,1) RETURNING id INTO v_observation_id;
    INSERT INTO sf_form_ss_message_to_ss_observation (form_ss_message_fk, form_ss_observation_fk) VALUES (v_outbound_ss_message_id, v_observation_id);

    -- Insert BenefitsCoordination Data (Cert_ChangeREQ-5 has one)
    INSERT INTO form_ss_benefit (payer_name, cardholder_id, group_id, group_name, pbm_participant_id, bin, pcn, created_by, updated_by)
    VALUES ('PBMF', '8351ZD', 'JW92983', 'JW MID-CA#7', 'T00000000021633', '444444', '555555', 1, 1) RETURNING id INTO v_benefit_id;
    INSERT INTO sf_form_ss_message_to_ss_benefit(form_ss_message_fk, form_ss_benefit_fk) VALUES (v_outbound_ss_message_id, v_benefit_id);

    -- Insert MedicationRequested (form_ss_chg_med)
    INSERT INTO form_ss_chg_med (
        description, product_code, pc_qualifier_id, 
        quantity, qty_qualifier_id, quantity_uom_id,  daw, refills, sig, note,
        created_by, updated_by
    ) VALUES (
        'Tobramycin and Dexamethasone Ophthalmic Suspension', '24208029510', 'ND', 
        10, '38', 'C28254', '0', 1, 'Instill 1 drop into affected eye every 4 hours for 7 days', NULL,
        1, 1
    ) RETURNING id INTO v_chg_med_id;
    INSERT INTO sf_form_ss_message_to_ss_chg_med (form_ss_message_fk, form_ss_chg_med_fk) VALUES (v_outbound_ss_message_id, v_chg_med_id);

    -- Insert DUE for the MedicationRequested (linked to form_ss_chg_med)
    INSERT INTO form_ss_due (
        service_reason_id, pservice_rsn_id, clinical_significance, ack_reason,created_by, updated_by
    ) VALUES (
        'SR', 
        'SC',
        '3', 
        'Suggest to increase frequency of distilling as patient eyelid seriously inflamed',
        1,1
    ) RETURNING id INTO v_due_id;
    -- Link DUE ServiceResultCode
    INSERT INTO gr_form_ss_due_service_res_id_to_list_ss_service_result_code_id (form_ss_due_fk, form_list_ss_service_result_code_fk)
    VALUES (v_due_id, (SELECT code FROM form_list_ss_service_result_code WHERE code = '2A'));

    INSERT INTO sf_form_ss_message_to_ss_due (form_ss_message_fk, form_ss_due_fk)
    VALUES (v_outbound_ss_message_id, v_due_id);

    RAISE NOTICE 'Outbound RxChangeRequest form_ss_message_id to be tested: %', v_outbound_ss_message_id;

    -- 3. Execution: Call the main payload generation function
    SELECT generate_ss_outbound_payload(v_outbound_ss_message_id) INTO v_generated_payload;

    RAISE NOTICE 'Generated Payload: %', jsonb_pretty(v_generated_payload);

    -- 4. Define Expected Payload (based on Cert_ChangeREQ-5.xml)
    v_expected_payload := jsonb_strip_nulls(jsonb_build_object(
        'Message', jsonb_build_object(
            'Header', jsonb_build_object(
                'To', '111111', -- Prescriber system
                'From', '6301875277001', -- Pharmacy
                'MessageID', (v_generated_payload->'Message'->'Header'->>'MessageID'),
                'SentTime', (v_generated_payload->'Message'->'Header'->>'SentTime'),
                'RxReferenceNumber', 'RxReference # for Change-5',
                'PrescriberOrderNumber', 'PON from NewRx' -- This was in the DB record
            ),
            'Body', jsonb_build_object(
                'RxChangeRequest', jsonb_build_object(
                    'MessageRequestCode', 'D',
                    'BenefitsCoordination', jsonb_build_array(jsonb_build_object(
                        'PayerIdentification', jsonb_build_object(
                            'ProcessorIdentificationNumber', '555555',
                            'IINNumber', '444444',
                            'PayerID', 'T00000000021633'
                         ),
                        'PayerName', 'PBMF',
                        'CardholderID', '8351ZD',
                        'GroupID', 'JW92983',
                        'GroupName', 'JW MID-CA#7'
                    )),
                    'Patient', jsonb_build_object(
                        'HumanPatient', jsonb_build_object(
                            'Name', jsonb_build_object(
                                'LastName', 'Delaplaine',
                                'FirstName', 'Zachary'
                            ),
                            'Gender', 'M',
                            'DateOfBirth', jsonb_build_object('Date', '20101201'),
                            'Address', jsonb_build_object(
                                'AddressLine1', '901 Sauvblanc Blvd',
                                'City', 'Petaluma',
                                'StateProvince', 'CA',
                                'PostalCode', '94952'
                            ),
                            'CommunicationNumbers', jsonb_build_object(
                                'PrimaryTelephone', jsonb_build_object('Number', '**********')
                            )
                        )
                    ),
                    'Pharmacy', jsonb_build_object(
                        'Identification', jsonb_build_object(
                            'NCPDPID', '6301875277001',
                            'NPI', '**********'
                        ),
                        'BusinessName', 'Hawaii Specialty Pharmacy',
                        'Address', jsonb_build_object(
                            'AddressLine1', '1150 South King Street',
                            'AddressLine2', 'Suite 1006',
                            'City', 'Honolulu',
                            'StateProvince', 'HI',
                            'PostalCode', '96814'
                        ),
                        'CommunicationNumbers', jsonb_build_object(
                            'PrimaryTelephone', jsonb_build_object('Number', '**********'),
                            'Fax', jsonb_build_object('Number', '**********')
                        )
                    ),
                    'Prescriber', jsonb_build_object(
                        'NonVeterinarian', jsonb_build_object(
                            'Identification', jsonb_build_object(
                                'StateLicenseNumber', '784577%1142%14',
                                'MedicaidNumber', '654745',
                                'DEANumber', '*********',
                                'NPI', '222222'
                            ),
                            'PracticeLocation', jsonb_build_object('BusinessName', 'Medi-Blue Rapid Clinic'),
                            'Name', jsonb_build_object(
                                'LastName', 'Thomas',
                                'FirstName', 'Walden'
                            ),
                            'Address', jsonb_build_object(
                                'AddressLine1', '2165-B1 Northpoint Parkway',
                                'City', 'Santa Rosa',
                                'StateProvince', 'CA',
                                'PostalCode', '95407'
                            ),
                            'CommunicationNumbers', jsonb_build_object(
                                'PrimaryTelephone', jsonb_build_object('Number', '**********'),
                                'Fax', jsonb_build_object('Number', '**********')
                            )
                        )
                    ),
                    'Observation', jsonb_build_object(
                        'Measurement', jsonb_build_array(
                            jsonb_build_object(
                                'VitalSign', '8302-2',
                                'LOINCVersion', '2.42',
                                'Value', 51,
                                'UnitOfMeasure', '[in_i]',
                                'UCUMVersion', '2.1',
                                'ObservationDate', jsonb_build_object('Date', '20180126')
                            ),
                            jsonb_build_object(
                                'VitalSign', '29463-7',
                                'LOINCVersion', '2.42',
                                'Value', 62,
                                'UnitOfMeasure', '[lb_av]',
                                'UCUMVersion', '2.1',
                                'ObservationDate', jsonb_build_object('Date', '20180126')
                            )
                        )
                    ),
                    'MedicationPrescribed', jsonb_build_object(
                        'Diagnosis', jsonb_build_object(
                            'ClinicalInformationQualifier', '1',
                            'Primary', jsonb_build_object(
                                'Code', 'H10502',
                                'Description', 'Unspecified blepharoconjunctivitis, left eye',
                                'Qualifier', 'ABF'
                            )
                        ),
                        'DrugCoded', jsonb_build_object(
                            'DrugDBCode', jsonb_build_object(
                                'Code', '309683',
                                'Qualifier', 'SCD'
                            ),
                            'ProductCode', jsonb_build_object(
                                'Code', '24208029510',
                                'Qualifier', 'ND'
                            )
                        ),
                        'DrugDescription', 'Tobramycin and Dexamethasone Ophthalmic Suspension, USP 0.3%/0.1%',
                        'DrugUseEvaluation', jsonb_build_array(
                            jsonb_build_object(
                                'AcknowledgementReason', 'Suggest to increase frequency of distilling as patient eyelid seriously inflamed',
                                'ClinicalSignificanceCode', '3',
                                'ProfessionalServiceCode', 'SC',
                                'ServiceReasonCode', 'SR',
                                'ServiceResultCode', jsonb_build_array('2A')
                            )
                        ),
                        'NumberOfRefills', 1,
                        'Quantity', jsonb_build_object(
                            'QuantityUnitOfMeasure', jsonb_build_object('Code', 'C28254'),
                            'Value', 10
                        ),
                        'Sig', jsonb_build_object('SigText', 'Instill 1 drop into affected eye every 6 hours for 7 days'),
                        'Substitutions', '0',
                        'WrittenDate', jsonb_build_object('Date', '20190101')
                    ),
                    'MedicationRequested', jsonb_build_array(jsonb_build_object(
                        'DrugDescription', 'Tobramycin and Dexamethasone Ophthalmic Suspension',
                        'DrugCoded', jsonb_build_object(
                            'ProductCode', jsonb_build_object('Code', '24208029510', 'Qualifier', 'ND')
                        ),
                        'Quantity', jsonb_build_object(
                            'Value', 10,
                            'CodeListQualifier', '38',
                            'QuantityUnitOfMeasure', jsonb_build_object('Code', 'C28254')
                        ),
                        'Substitutions', '0',
                        'NumberOfRefills', 1,
                        'DrugUseEvaluation', jsonb_build_array(jsonb_build_object(
                            'ServiceReasonCode', 'SR',
                            'ProfessionalServiceCode', 'SC',
                            'ServiceResultCode', jsonb_build_array('2A'),
                            'ClinicalSignificanceCode', '3',
                            'AcknowledgementReason', 'Suggest to increase frequency of distilling as patient eyelid seriously inflamed'
                        )),
                        'Sig', jsonb_build_object('SigText', 'Instill 1 drop into affected eye every 4 hours for 7 days')
                    ))
                )
            )
        ))
    );

    PERFORM assert_json_equals(v_expected_payload, v_generated_payload, 'Full RxChangeRequest Payload for Cert_ChangeREQ-5');

    RAISE NOTICE 'Test 0010-ss-changerequest.sql (Cert_ChangeREQ-5 Scenario) PASSED!';

END $$; 