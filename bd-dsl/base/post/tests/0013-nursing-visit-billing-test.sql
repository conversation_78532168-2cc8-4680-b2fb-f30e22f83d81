-- Simple test for nursing visit billing functionality
-- This will test the create_nursing_visit_charge_line function

-- Test 1: Check if function exists and can be called
SELECT 'Testing nursing visit billing function...' as test_info;

-- Test 2: Try to find an existing encounter to test with
SELECT 
    'Looking for existing encounters...' as test_info,
    COUNT(*) as encounter_count
FROM form_encounter 
WHERE deleted IS NOT TRUE 
AND archived IS NOT TRUE;

-- Test 3: Show recent encounters with approval status
SELECT 
    'Recent encounters:' as info,
    id,
    patient_id,
    approved,
    cc_signature IS NOT NULL as has_signature,
    time_total,
    visit_number,
    created_on
FROM form_encounter 
WHERE deleted IS NOT TRUE 
AND archived IS NOT TRUE
ORDER BY created_on DESC
LIMIT 5;

-- Test 4: Check if we have any nursing-related inventory items
SELECT 
    'Nursing inventory items:' as info,
    COUNT(*) as nursing_inventory_count
FROM form_inventory inv
WHERE inv.deleted IS NOT TRUE
AND inv.archived IS NOT TRUE
AND COALESCE(inv.active, 'No') = 'Yes'
AND inv.type = 'Billable'
AND (inv.description ILIKE '%nursing%' OR inv.nursing_hours IS NOT NULL);

-- Test 5: Check for patients with medical insurance
SELECT 
    'Patients with medical insurance:' as info,
    COUNT(*) as patient_count
FROM form_patient_insurance pi
INNER JOIN form_payer py ON py.id = pi.payer_id
    AND py.deleted IS NOT TRUE
    AND py.archived IS NOT TRUE
    AND py.billing_method_id IN ('mm', 'cms1500')
WHERE pi.deleted IS NOT TRUE
AND pi.archived IS NOT TRUE
AND COALESCE(pi.active, 'No') = 'Yes'
LIMIT 5;

-- Test 6: Check billing error log for any recent nursing-related errors
SELECT 
    'Recent nursing billing errors:' as info,
    error_message,
    created_on
FROM billing_error_log
WHERE (error_context LIKE '%nursing%' 
   OR additional_details::text LIKE '%encounter%'
   OR table_name = 'encounter')
AND created_on > NOW() - INTERVAL '24 hours'
ORDER BY created_on DESC
LIMIT 3;

SELECT 'Nursing visit billing function is deployed and ready for testing!' as result; 