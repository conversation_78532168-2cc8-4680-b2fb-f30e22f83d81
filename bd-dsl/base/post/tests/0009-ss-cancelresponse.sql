DO $$
DECLARE
    v_original_message_id TEXT;
    v_outbound_ss_message_id INTEGER;
    v_generated_payload JSONB;
    v_expected_payload JSONB;
    v_log_id INTEGER;
    v_site_id INTEGER;
    v_related_prescriber_order_number TEXT;
BEGIN
    -- 0. Get a site_id for the 'From' pharmacy in the CancelRxResponse header
    SELECT id INTO v_site_id FROM form_site WHERE ncpdp_id = '6301875277001' LIMIT 1; -- Using From from ss-cancelrxresponseTC3.xml
    IF v_site_id IS NULL THEN
        RAISE EXCEPTION 'Failed to find site with NCPDPID 6301875277001. Please ensure test data is populated.';
    END IF;

    -- 1. Setup: Create an initial message that the CancelRxResponse will relate to.
    -- This would typically be an inbound CancelRx request, but for this test,
    -- we only need its message_id to be referenced in RelatesToMessageID.
    -- The PrescriberOrderNumber from this original message might also be used.
    v_original_message_id := 'CANCEL_REQ_ID_TC3_' || gen_random_uuid()::text;
    v_related_prescriber_order_number := 'CANCELRX TEST CASE #3a';

    -- Minimal insert into form_ss_log just to have a record, not fully parsing it here.
    INSERT INTO form_ss_log (message_json, direction, message_type, message_id, created_by, updated_by)
    VALUES ('{}'::jsonb, 'IN', 'CancelRx', v_original_message_id, 1, 1) RETURNING id INTO v_log_id;

    -- Minimal insert into form_ss_message for the original request. The PrescriberOrderNumber is important.
    INSERT INTO form_ss_message (message_id, direction, message_type, physician_order_id, created_by, updated_by, processed, site_id)
    VALUES (v_original_message_id, 'IN', 'CancelRx', v_related_prescriber_order_number, 1, 1, 'Yes', v_site_id); 

    -- 2. Setup: Create the form_ss_message record for the outbound CancelRxResponse we want to generate.
    -- This data should mimic what our system would prepare before calling the payload builder.
    -- Based on surescripts/test_scripts/outbound/ss-cancelrxresponseTC3.xml
    INSERT INTO form_ss_message (
        direction, message_type, related_message_id, 
        send_to, sent_from, message_id, 
        physician_order_id, 
        cancel_status, cancel_note, 
        created_by, updated_by, processed, site_id, -- site_id here represents the sender (our pharmacy)
        sender_software_developer, sender_software_product, sender_software_version
    )
    VALUES (
        'OUT', 'CancelRxResponse', v_original_message_id, 
        '111111', '6301875277001', 'OUT_CANRES_TC3_' || gen_random_uuid()::text, 
        v_related_prescriber_order_number, 
        'Approved', '(explaining dispensing occurred)', 
        1, 1, 'No', v_site_id,
        'Surescripts', 'Certification Testing', '20170715' -- Mimicking header from example
    ) RETURNING id INTO v_outbound_ss_message_id;

    RAISE NOTICE 'Setup complete. Original CancelRx (simulated) form_ss_log_id: %, Original form_ss_message_id: % (SureScripts MessageID: %)', v_log_id, (SELECT id FROM form_ss_message WHERE message_id = v_original_message_id), v_original_message_id;
    RAISE NOTICE 'Outbound CancelRxResponse form_ss_message_id to be tested: %', v_outbound_ss_message_id;

    -- 3. Execution: Call the main payload generation function
    SELECT generate_ss_outbound_payload(v_outbound_ss_message_id) INTO v_generated_payload;

    RAISE NOTICE 'Generated Payload: %', jsonb_pretty(v_generated_payload);

    -- 4. Define Expected Payload (based on ss-cancelrxresponseTC3.xml)
    v_expected_payload := jsonb_strip_nulls(jsonb_build_object(
        'Message', jsonb_build_object(
            'Header', jsonb_build_object(
                'To', '111111',
                'From', '6301875277001',
                'MessageID', (SELECT message_id FROM form_ss_message WHERE id = v_outbound_ss_message_id), 
                'RelatesToMessageID', v_original_message_id,
                'SentTime', (v_generated_payload->'Message'->'Header'->>'SentTime'), 
                'SenderSoftware', jsonb_build_object(
                    'SenderSoftwareDeveloper', 'Surescripts',
                    'SenderSoftwareProduct', 'Certification Testing',
                    'SenderSoftwareVersionRelease', '20170715'
                ),
                'PrescriberOrderNumber', v_related_prescriber_order_number
            ),
            'Body', jsonb_build_object(
                'CancelRxResponse', jsonb_build_object(
                    'Response', jsonb_build_object(
                        'Approved', jsonb_build_object(
                            'Note', '(explaining dispensing occurred)'
                        )
                    )
                )
            )
        ))
    );

    -- 5. Assertions 
    PERFORM assert_json_equals(v_expected_payload, v_generated_payload, 'Full CancelRxResponse Payload');

    RAISE NOTICE 'Test 0009-ss-cancelresponse.sql (Approved Scenario) PASSED!';

END $$; 