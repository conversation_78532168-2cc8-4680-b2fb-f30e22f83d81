-- Test 999 Response Processing
-- This file tests the parse_999_response function

BEGIN;

-- Set up test data
DO $$
DECLARE
    v_patient_id INTEGER;
    v_site_id INTEGER;
    v_payer_id INTEGER;
    v_invoice_no TEXT;
    v_claim_no TEXT;
    v_response_id INTEGER;
    v_response_json JSONB;
    v_main_record_id INTEGER;
    v_subform_id INTEGER;
    v_error_count INTEGER;
    v_edit_response_count INTEGER;
BEGIN
    -- Clean up any existing test data
    PERFORM cleanup_test_data();
    
    RAISE NOTICE '=== Starting 999 Response Tests ===';
    
    -- Create test entities
    v_patient_id := create_test_patient('<PERSON>');
    v_site_id := create_test_site('Test Pharmacy 999');
    v_payer_id := create_test_payer('Aetna', 'AETNA');
    v_invoice_no := create_test_invoice(v_patient_id, v_site_id);
    
    -- Create test claim with specific control numbers
    v_claim_no := create_test_medical_claim(
        v_patient_id,
        v_site_id,
        v_payer_id,
        v_invoice_no,
        'PCN-999-001',  -- patient_control_number
        'TCN-999-001'   -- trading_partner_claim_number
    );
    
    -- Test 1: Successful 999 Response (Accepted)
    RAISE NOTICE 'Test 1: Successful 999 Response (Accepted)';
    
    v_response_json := '{
        "status": "SUCCESS",
        "controlNumber": "000000001",
        "tradingPartnerServiceId": "9496",
        "claimReference": {
            "correlationId": "200715R999898",
            "submitterId": "999898",
            "customerClaimNumber": "PCN-999-001",
            "patientControlNumber": "PCN-999-001",
            "timeOfResponse": "2024-01-20T12:44:17.994-05:00",
            "claimType": "PRO",
            "payerID": "AETNA",
            "formatVersion": "5010",
            "rhclaimNumber": "RH123456"
        },
        "editStatus": "ACCEPTED",
        "payer": {
            "payerName": "Aetna",
            "payerID": "AETNA"
        },
        "meta": {
            "submitterId": "999898",
            "senderId": "SENDER123",
            "billerId": "BILLER456",
            "traceId": "TRACE-999-001",
            "applicationMode": "production"
        }
    }'::jsonb;
    
    -- Insert response log (trigger will process it with parse_999_response)
    v_response_id := insert_test_response_log('999', v_response_json);
    
    -- Allow parse_999_response to process
    PERFORM pg_sleep(0.5);
    
    -- Get the main record ID that was created by parse_999_response
    SELECT id INTO v_main_record_id
    FROM form_med_claim_resp
    WHERE response_id = v_response_id;
    
    PERFORM assert_not_null(v_main_record_id::TEXT, 'Main 999 record ID');
    
    -- Test main record fields
    PERFORM assert_equals('000000001',
        (SELECT control_number FROM form_med_claim_resp WHERE id = v_main_record_id),
        'Control number');
    
    PERFORM assert_equals('9496',
        (SELECT trading_partner_service_id FROM form_med_claim_resp WHERE id = v_main_record_id),
        'Trading partner service ID');
    
    PERFORM assert_equals('SUCCESS',
        (SELECT status FROM form_med_claim_resp WHERE id = v_main_record_id),
        'Status');
    
    PERFORM assert_equals('ACCEPTED',
        (SELECT edit_status FROM form_med_claim_resp WHERE id = v_main_record_id),
        'Edit status');
    
    -- Verify claim_reference subform was created
    PERFORM assert_record_exists(
        'form_med_claim_resp_ref ref JOIN sf_form_med_claim_resp_to_med_claim_resp_ref sf ON sf.form_med_claim_resp_ref_fk = ref.id',
        'sf.form_med_claim_resp_fk = ' || v_main_record_id || ' AND ref.deleted IS NOT TRUE AND ref.archived IS NOT TRUE AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE',
        'Claim reference subform'
    );
    
    -- Now check fields in the claim_reference subform
    PERFORM assert_equals('PCN-999-001',
        (SELECT ref.customer_claim_number 
         FROM form_med_claim_resp_ref ref
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ref sf ON sf.form_med_claim_resp_ref_fk = ref.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND ref.deleted IS NOT TRUE AND ref.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Customer claim number');
    
    PERFORM assert_equals('PCN-999-001',
        (SELECT ref.patient_control_number 
         FROM form_med_claim_resp_ref ref
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ref sf ON sf.form_med_claim_resp_ref_fk = ref.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND ref.deleted IS NOT TRUE AND ref.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Patient control number');
    
    PERFORM assert_equals('200715R999898',
        (SELECT ref.correlation_id 
         FROM form_med_claim_resp_ref ref
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ref sf ON sf.form_med_claim_resp_ref_fk = ref.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND ref.deleted IS NOT TRUE AND ref.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Correlation ID');
    
    PERFORM assert_equals('999898',
        (SELECT ref.submitter_id 
         FROM form_med_claim_resp_ref ref
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ref sf ON sf.form_med_claim_resp_ref_fk = ref.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND ref.deleted IS NOT TRUE AND ref.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Submitter ID');
    
    PERFORM assert_equals('2024-01-20 12:44:17.994',
        (SELECT ref.time_of_response::TEXT 
         FROM form_med_claim_resp_ref ref
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ref sf ON sf.form_med_claim_resp_ref_fk = ref.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND ref.deleted IS NOT TRUE AND ref.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Time of response');
    
    PERFORM assert_equals('PRO',
        (SELECT ref.claim_type 
         FROM form_med_claim_resp_ref ref
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ref sf ON sf.form_med_claim_resp_ref_fk = ref.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND ref.deleted IS NOT TRUE AND ref.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Claim type');
    
    PERFORM assert_equals('AETNA',
        (SELECT ref.payer_id 
         FROM form_med_claim_resp_ref ref
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ref sf ON sf.form_med_claim_resp_ref_fk = ref.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND ref.deleted IS NOT TRUE AND ref.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Payer ID response');
    
    PERFORM assert_equals('5010',
        (SELECT ref.format_version 
         FROM form_med_claim_resp_ref ref
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ref sf ON sf.form_med_claim_resp_ref_fk = ref.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND ref.deleted IS NOT TRUE AND ref.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Format version');
    
    PERFORM assert_equals('RH123456',
        (SELECT ref.rhclaim_number 
         FROM form_med_claim_resp_ref ref
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ref sf ON sf.form_med_claim_resp_ref_fk = ref.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND ref.deleted IS NOT TRUE AND ref.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'RH claim number');
    
    -- Verify payer subform
    PERFORM assert_record_exists(
        'form_med_claim_resp_ch_pyr pyr JOIN sf_form_med_claim_resp_to_med_claim_resp_ch_pyr sf ON sf.form_med_claim_resp_ch_pyr_fk = pyr.id',
        'sf.form_med_claim_resp_fk = ' || v_main_record_id || ' AND pyr.deleted IS NOT TRUE AND pyr.archived IS NOT TRUE AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE',
        'Payer subform'
    );
    
    -- Verify payer details
    PERFORM assert_equals('Aetna',
        (SELECT pyr.payer_name 
         FROM form_med_claim_resp_ch_pyr pyr
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ch_pyr sf ON sf.form_med_claim_resp_ch_pyr_fk = pyr.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND pyr.deleted IS NOT TRUE AND pyr.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Payer name');
    
    PERFORM assert_equals('AETNA',
        (SELECT pyr.payer_id 
         FROM form_med_claim_resp_ch_pyr pyr
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ch_pyr sf ON sf.form_med_claim_resp_ch_pyr_fk = pyr.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND pyr.deleted IS NOT TRUE AND pyr.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Payer ID');
    
    -- Verify meta subform
    PERFORM assert_record_exists(
        'form_med_claim_resp_ch_meta meta JOIN sf_form_med_claim_resp_to_med_claim_resp_ch_meta sf ON sf.form_med_claim_resp_ch_meta_fk = meta.id',
        'sf.form_med_claim_resp_fk = ' || v_main_record_id || ' AND meta.deleted IS NOT TRUE AND meta.archived IS NOT TRUE AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE',
        'Meta subform'
    );
    
    -- Verify meta details
    PERFORM assert_equals('999898',
        (SELECT meta.submitter_id 
         FROM form_med_claim_resp_ch_meta meta
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ch_meta sf ON sf.form_med_claim_resp_ch_meta_fk = meta.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND meta.deleted IS NOT TRUE AND meta.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Meta submitter ID');
    
    PERFORM assert_equals('SENDER123',
        (SELECT meta.sender_id 
         FROM form_med_claim_resp_ch_meta meta
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ch_meta sf ON sf.form_med_claim_resp_ch_meta_fk = meta.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND meta.deleted IS NOT TRUE AND meta.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Meta sender ID');
    
    PERFORM assert_equals('BILLER456',
        (SELECT meta.biller_id 
         FROM form_med_claim_resp_ch_meta meta
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ch_meta sf ON sf.form_med_claim_resp_ch_meta_fk = meta.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND meta.deleted IS NOT TRUE AND meta.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Meta biller ID');
    
    PERFORM assert_equals('TRACE-999-001',
        (SELECT meta.trace_id 
         FROM form_med_claim_resp_ch_meta meta
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ch_meta sf ON sf.form_med_claim_resp_ch_meta_fk = meta.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND meta.deleted IS NOT TRUE AND meta.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Meta trace ID');
    
    PERFORM assert_equals('production',
        (SELECT meta.application_mode 
         FROM form_med_claim_resp_ch_meta meta
         JOIN sf_form_med_claim_resp_to_med_claim_resp_ch_meta sf ON sf.form_med_claim_resp_ch_meta_fk = meta.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND meta.deleted IS NOT TRUE AND meta.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Meta application mode');
    
    -- Test 2: 999 Response with Errors
    RAISE NOTICE 'Test 2: 999 Response with Errors';
    
    v_claim_no := create_test_medical_claim(
        v_patient_id,
        v_site_id,
        v_payer_id,
        v_invoice_no,
        'PCN-999-002',
        'TCN-999-002'
    );
    
    v_response_json := '{
        "status": "ERROR",
        "controlNumber": "000000002",
        "tradingPartnerServiceId": "9496",
        "claimReference": {
            "correlationId": "200715R999899",
            "submitterId": "999898",
            "customerClaimNumber": "PCN-999-002",
            "patientControlNumber": "PCN-999-002",
            "timeOfResponse": "2024-01-20T13:00:00.000-05:00",
            "claimType": "PRO",
            "payerID": "AETNA",
            "formatVersion": "5010"
        },
        "editStatus": "REJECTED",
        "errors": [{
            "field": "claimInformation.serviceLines[0].serviceDate",
            "value": "20240132",
            "code": "ERR001",
            "description": "Invalid date format",
            "followupAction": "Correct the service date",
            "location": "serviceDate"
        }, {
            "field": "billing.npi",
            "value": "",
            "code": "ERR002",
            "description": "NPI is required",
            "followupAction": "Add NPI",
            "location": "NM109"
        }],
        "payer": {
            "payerName": "Aetna",
            "payerID": "AETNA"
        },
        "meta": {
            "submitterId": "999898",
            "senderId": "SENDER123",
            "billerId": "BILLER456",
            "traceId": "TRACE-999-002",
            "applicationMode": "production"
        }
    }'::jsonb;
    
    v_response_id := insert_test_response_log('999', v_response_json);
    PERFORM pg_sleep(0.5);
    
    -- Get the main record ID
    SELECT id INTO v_main_record_id
    FROM form_med_claim_resp
    WHERE response_id = v_response_id;
    
    PERFORM assert_not_null(v_main_record_id::TEXT, 'Main 999 record ID for error response');
    
    -- Verify status
    PERFORM assert_equals('ERROR',
        (SELECT status FROM form_med_claim_resp WHERE id = v_main_record_id),
        'Error response status');
    
    PERFORM assert_equals('REJECTED',
        (SELECT edit_status FROM form_med_claim_resp WHERE id = v_main_record_id),
        'Error response edit status');
    
    -- Verify errors were created
    SELECT COUNT(*) INTO v_error_count
    FROM form_med_claim_resp_err err
    JOIN sf_form_med_claim_resp_to_med_claim_resp_err sf
        ON sf.form_med_claim_resp_err_fk = err.id
    WHERE sf.form_med_claim_resp_fk = v_main_record_id
    AND err.deleted IS NOT TRUE
    AND err.archived IS NOT TRUE
    AND sf.delete IS NOT TRUE
    AND sf.archive IS NOT TRUE;
    
    PERFORM assert_numeric_equals('Error count', 2::NUMERIC, v_error_count::NUMERIC);
    
    -- Verify first error details
    PERFORM assert_equals('claimInformation.serviceLines[0].serviceDate',
        (SELECT err.field 
         FROM form_med_claim_resp_err err
         JOIN sf_form_med_claim_resp_to_med_claim_resp_err sf ON sf.form_med_claim_resp_err_fk = err.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND err.code = 'ERR001'
         AND err.deleted IS NOT TRUE AND err.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First error field');
    
    PERFORM assert_equals('20240132',
        (SELECT err.value 
         FROM form_med_claim_resp_err err
         JOIN sf_form_med_claim_resp_to_med_claim_resp_err sf ON sf.form_med_claim_resp_err_fk = err.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND err.code = 'ERR001'
         AND err.deleted IS NOT TRUE AND err.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First error value');
    
    PERFORM assert_equals('Invalid date format',
        (SELECT err.description 
         FROM form_med_claim_resp_err err
         JOIN sf_form_med_claim_resp_to_med_claim_resp_err sf ON sf.form_med_claim_resp_err_fk = err.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND err.code = 'ERR001'
         AND err.deleted IS NOT TRUE AND err.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First error description');
    
    PERFORM assert_equals('serviceDate',
        (SELECT err.location 
         FROM form_med_claim_resp_err err
         JOIN sf_form_med_claim_resp_to_med_claim_resp_err sf ON sf.form_med_claim_resp_err_fk = err.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND err.code = 'ERR001'
         AND err.deleted IS NOT TRUE AND err.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First error location');
    
    -- Test 3: 999 Response with Edit Responses
    RAISE NOTICE 'Test 3: 999 Response with Edit Responses';
    
    v_claim_no := create_test_medical_claim(
        v_patient_id,
        v_site_id,
        v_payer_id,
        v_invoice_no,
        'PCN-999-003',
        'TCN-999-003'
    );
    
    v_response_json := '{
        "status": "WARNING",
        "controlNumber": "000000003",
        "tradingPartnerServiceId": "9496",
        "claimReference": {
            "correlationId": "200715R999900",
            "submitterId": "999898",
            "customerClaimNumber": "PCN-999-003",
            "patientControlNumber": "PCN-999-003",
            "timeOfResponse": "2024-01-20T14:00:00.000-05:00",
            "claimType": "PRO",
            "payerID": "AETNA",
            "formatVersion": "5010"
        },
        "editStatus": "ACCEPTED_WITH_EDITS",
        "editResponses": [{
            "qualifierCode": "M20",
            "errorDescription": "Missing diagnosis code for this procedure",
            "fieldIndex": "1",
            "editName": "DiagnosisRequired",
            "editActivity": "ADD",
            "referenceID": "REF001",
            "claimCorePath": "claimInformation.serviceLines[0]",
            "allowOverride": "Y",
            "element": "SV107",
            "segment": "SV1",
            "loop": "2400",
            "badData": "",
            "phaseID": "1"
        }, {
            "qualifierCode": "M25",
            "errorDescription": "Modifier required for bilateral procedure",
            "fieldIndex": "2",
            "editName": "ModifierRequired",
            "editActivity": "ADD",
            "referenceID": "REF002",
            "claimCorePath": "claimInformation.serviceLines[1]",
            "allowOverride": "N",
            "element": "SV101-03",
            "segment": "SV1",
            "loop": "2400",
            "badData": "99213",
            "phaseID": "1"
        }],
        "payer": {
            "payerName": "Aetna",
            "payerID": "AETNA"
        },
        "meta": {
            "submitterId": "999898",
            "senderId": "SENDER123",
            "billerId": "BILLER456",
            "traceId": "TRACE-999-003",
            "applicationMode": "production"
        }
    }'::jsonb;
    
    v_response_id := insert_test_response_log('999', v_response_json);
    PERFORM pg_sleep(0.5);
    
    -- Get the main record ID
    SELECT id INTO v_main_record_id
    FROM form_med_claim_resp
    WHERE response_id = v_response_id;
    
    PERFORM assert_not_null(v_main_record_id::TEXT, 'Main 999 record ID for edit response');
    
    -- Verify status
    PERFORM assert_equals('WARNING',
        (SELECT status FROM form_med_claim_resp WHERE id = v_main_record_id),
        'Edit response status');
    
    PERFORM assert_equals('ACCEPTED_WITH_EDITS',
        (SELECT edit_status FROM form_med_claim_resp WHERE id = v_main_record_id),
        'Edit response edit status');
    
    -- Verify edit responses were created
    SELECT COUNT(*) INTO v_edit_response_count
    FROM form_med_claim_resp_edit edit
    JOIN sf_form_med_claim_resp_to_med_claim_resp_edit sf
        ON sf.form_med_claim_resp_edit_fk = edit.id
    WHERE sf.form_med_claim_resp_fk = v_main_record_id
    AND edit.deleted IS NOT TRUE
    AND edit.archived IS NOT TRUE
    AND sf.delete IS NOT TRUE
    AND sf.archive IS NOT TRUE;
    
    PERFORM assert_numeric_equals('Edit response count', 2::NUMERIC, v_edit_response_count::NUMERIC);
    
    -- Verify first edit response details
    PERFORM assert_equals('M20',
        (SELECT edit.qualifier_code 
         FROM form_med_claim_resp_edit edit
         JOIN sf_form_med_claim_resp_to_med_claim_resp_edit sf ON sf.form_med_claim_resp_edit_fk = edit.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND edit.reference_id = 'REF001'
         AND edit.deleted IS NOT TRUE AND edit.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First edit qualifier code');
    
    PERFORM assert_equals('Missing diagnosis code for this procedure',
        (SELECT edit.error_description 
         FROM form_med_claim_resp_edit edit
         JOIN sf_form_med_claim_resp_to_med_claim_resp_edit sf ON sf.form_med_claim_resp_edit_fk = edit.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND edit.reference_id = 'REF001'
         AND edit.deleted IS NOT TRUE AND edit.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First edit error description');
    
    PERFORM assert_equals('1',
        (SELECT edit.field_index 
         FROM form_med_claim_resp_edit edit
         JOIN sf_form_med_claim_resp_to_med_claim_resp_edit sf ON sf.form_med_claim_resp_edit_fk = edit.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND edit.reference_id = 'REF001'
         AND edit.deleted IS NOT TRUE AND edit.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First edit field index');
    
    PERFORM assert_equals('DiagnosisRequired',
        (SELECT edit.edit_name 
         FROM form_med_claim_resp_edit edit
         JOIN sf_form_med_claim_resp_to_med_claim_resp_edit sf ON sf.form_med_claim_resp_edit_fk = edit.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND edit.reference_id = 'REF001'
         AND edit.deleted IS NOT TRUE AND edit.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First edit name');
    
    PERFORM assert_equals('ADD',
        (SELECT edit.edit_activity 
         FROM form_med_claim_resp_edit edit
         JOIN sf_form_med_claim_resp_to_med_claim_resp_edit sf ON sf.form_med_claim_resp_edit_fk = edit.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND edit.reference_id = 'REF001'
         AND edit.deleted IS NOT TRUE AND edit.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First edit activity');
    
    PERFORM assert_equals('claimInformation.serviceLines[0]',
        (SELECT edit.claim_core_path 
         FROM form_med_claim_resp_edit edit
         JOIN sf_form_med_claim_resp_to_med_claim_resp_edit sf ON sf.form_med_claim_resp_edit_fk = edit.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND edit.reference_id = 'REF001'
         AND edit.deleted IS NOT TRUE AND edit.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First edit claim core path');
    
    PERFORM assert_equals('Y',
        (SELECT edit.allow_override 
         FROM form_med_claim_resp_edit edit
         JOIN sf_form_med_claim_resp_to_med_claim_resp_edit sf ON sf.form_med_claim_resp_edit_fk = edit.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND edit.reference_id = 'REF001'
         AND edit.deleted IS NOT TRUE AND edit.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First edit allow override');
    
    PERFORM assert_equals('SV107',
        (SELECT edit.element 
         FROM form_med_claim_resp_edit edit
         JOIN sf_form_med_claim_resp_to_med_claim_resp_edit sf ON sf.form_med_claim_resp_edit_fk = edit.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND edit.reference_id = 'REF001'
         AND edit.deleted IS NOT TRUE AND edit.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First edit element');
    
    PERFORM assert_equals('SV1',
        (SELECT edit.segment 
         FROM form_med_claim_resp_edit edit
         JOIN sf_form_med_claim_resp_to_med_claim_resp_edit sf ON sf.form_med_claim_resp_edit_fk = edit.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND edit.reference_id = 'REF001'
         AND edit.deleted IS NOT TRUE AND edit.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First edit segment');
    
    PERFORM assert_equals('2400',
        (SELECT edit.loop 
         FROM form_med_claim_resp_edit edit
         JOIN sf_form_med_claim_resp_to_med_claim_resp_edit sf ON sf.form_med_claim_resp_edit_fk = edit.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND edit.reference_id = 'REF001'
         AND edit.deleted IS NOT TRUE AND edit.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First edit loop');
    
    PERFORM assert_equals('',
        (SELECT edit.bad_data 
         FROM form_med_claim_resp_edit edit
         JOIN sf_form_med_claim_resp_to_med_claim_resp_edit sf ON sf.form_med_claim_resp_edit_fk = edit.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND edit.reference_id = 'REF001'
         AND edit.deleted IS NOT TRUE AND edit.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First edit bad data');
    
    PERFORM assert_equals('1',
        (SELECT edit.phase_id 
         FROM form_med_claim_resp_edit edit
         JOIN sf_form_med_claim_resp_to_med_claim_resp_edit sf ON sf.form_med_claim_resp_edit_fk = edit.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND edit.reference_id = 'REF001'
         AND edit.deleted IS NOT TRUE AND edit.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'First edit phase ID');
    
    -- Test 4: 999 Response with Failure
    RAISE NOTICE 'Test 4: 999 Response with Failure';
    
    v_claim_no := create_test_medical_claim(
        v_patient_id,
        v_site_id,
        v_payer_id,
        v_invoice_no,
        'PCN-999-004',
        'TCN-999-004'
    );
    
    v_response_json := '{
        "status": "FAILURE",
        "controlNumber": "000000004",
        "tradingPartnerServiceId": "9496",
        "claimReference": {
            "correlationId": "200715R999901",
            "submitterId": "999898",
            "customerClaimNumber": "PCN-999-004",
            "patientControlNumber": "PCN-999-004",
            "timeOfResponse": "2024-01-20T15:00:00.000-05:00",
            "claimType": "PRO",
            "payerID": "AETNA",
            "formatVersion": "5010"
        },
        "editStatus": "FAILED",
        "failure": {
            "description": "System error occurred during claim processing",
            "code": "SYS001"
        },
        "payer": {
            "payerName": "Aetna",
            "payerID": "AETNA"
        },
        "meta": {
            "submitterId": "999898",
            "senderId": "SENDER123",
            "billerId": "BILLER456",
            "traceId": "TRACE-999-004",
            "applicationMode": "production"
        }
    }'::jsonb;
    
    v_response_id := insert_test_response_log('999', v_response_json);
    PERFORM pg_sleep(0.5);
    
    -- Get the main record ID
    SELECT id INTO v_main_record_id
    FROM form_med_claim_resp
    WHERE response_id = v_response_id;
    
    PERFORM assert_not_null(v_main_record_id::TEXT, 'Main 999 record ID for failure response');
    
    -- Verify status
    PERFORM assert_equals('FAILURE',
        (SELECT status FROM form_med_claim_resp WHERE id = v_main_record_id),
        'Failure response status');
    
    PERFORM assert_equals('FAILED',
        (SELECT edit_status FROM form_med_claim_resp WHERE id = v_main_record_id),
        'Failure response edit status');
    
    -- Verify failure subform was created
    PERFORM assert_record_exists(
        'form_med_claim_resp_fail fail JOIN sf_form_med_claim_resp_to_med_claim_resp_fail sf ON sf.form_med_claim_resp_fail_fk = fail.id',
        'sf.form_med_claim_resp_fk = ' || v_main_record_id || ' AND fail.deleted IS NOT TRUE AND fail.archived IS NOT TRUE AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE',
        'Failure subform'
    );
    
    -- Verify failure details
    PERFORM assert_equals('System error occurred during claim processing',
        (SELECT fail.description 
         FROM form_med_claim_resp_fail fail
         JOIN sf_form_med_claim_resp_to_med_claim_resp_fail sf ON sf.form_med_claim_resp_fail_fk = fail.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND fail.deleted IS NOT TRUE AND fail.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Failure description');
    
    PERFORM assert_equals('SYS001',
        (SELECT fail.code 
         FROM form_med_claim_resp_fail fail
         JOIN sf_form_med_claim_resp_to_med_claim_resp_fail sf ON sf.form_med_claim_resp_fail_fk = fail.id
         WHERE sf.form_med_claim_resp_fk = v_main_record_id
         AND fail.deleted IS NOT TRUE AND fail.archived IS NOT TRUE
         AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE),
        'Failure code');
    
    -- Test 5: 999 Response with missing claim (should log error but not fail)
    RAISE NOTICE 'Test 5: 999 Response with missing claim';
    
    v_response_json := '{
        "status": "SUCCESS",
        "controlNumber": "000000005",
        "tradingPartnerServiceId": "9496",
        "claimReference": {
            "correlationId": "200715R999902",
            "submitterId": "999898",
            "customerClaimNumber": "PCN-DOES-NOT-EXIST",
            "patientControlNumber": "PCN-DOES-NOT-EXIST",
            "timeOfResponse": "2024-01-20T16:00:00.000-05:00",
            "claimType": "PRO",
            "payerID": "AETNA",
            "formatVersion": "5010"
        },
        "editStatus": "ACCEPTED",
        "payer": {
            "payerName": "Aetna",
            "payerID": "AETNA"
        },
        "meta": {
            "submitterId": "999898",
            "senderId": "SENDER123",
            "billerId": "BILLER456",
            "traceId": "TRACE-999-005",
            "applicationMode": "production"
        }
    }'::jsonb;
    
    v_response_id := insert_test_response_log('999', v_response_json);
    PERFORM pg_sleep(0.5);
    
    -- Verify record was created even without matching claim
    SELECT id INTO v_main_record_id
    FROM form_med_claim_resp
    WHERE response_id = v_response_id;
    
    PERFORM assert_not_null(v_main_record_id::TEXT, 'Main 999 record ID for missing claim');
    
    -- Verify claim_no is NULL since no matching claim was found
    PERFORM assert_null(
        (SELECT claim_no FROM form_med_claim_resp WHERE id = v_main_record_id)::TEXT,
        'Claim number should be NULL for missing claim');
    
    -- Test 6: 999 Response with complete data (all subforms)
    RAISE NOTICE 'Test 6: 999 Response with complete data';
    
    v_claim_no := create_test_medical_claim(
        v_patient_id,
        v_site_id,
        v_payer_id,
        v_invoice_no,
        'PCN-999-006',
        'TCN-999-006'
    );
    
    v_response_json := '{
        "status": "WARNING",
        "controlNumber": "000000006",
        "tradingPartnerServiceId": "9496",
        "claimReference": {
            "correlationId": "200715R999903",
            "submitterId": "999898",
            "customerClaimNumber": "PCN-999-006",
            "patientControlNumber": "PCN-999-006",
            "timeOfResponse": "2024-01-20T17:00:00.000-05:00",
            "claimType": "PRO",
            "payerID": "AETNA",
            "formatVersion": "5010",
            "rhclaimNumber": "RH789012"
        },
        "editStatus": "ACCEPTED_WITH_WARNINGS",
        "errors": [{
            "field": "claimInformation.placeOfServiceCode",
            "value": "99",
            "code": "WARN001",
            "description": "Unusual place of service code",
            "followupAction": "Verify place of service",
            "location": "CLM05-01"
        }],
        "editResponses": [{
            "qualifierCode": "N100",
            "errorDescription": "National drug code may be required",
            "fieldIndex": "1",
            "editName": "NDCCheck",
            "editActivity": "INFO",
            "referenceID": "REF003",
            "claimCorePath": "claimInformation.serviceLines[0].drugIdentification",
            "allowOverride": "Y",
            "element": "LIN03",
            "segment": "LIN",
            "loop": "2410",
            "badData": "",
            "phaseID": "2"
        }],
        "payer": {
            "payerName": "Aetna",
            "payerID": "AETNA"
        },
        "meta": {
            "submitterId": "999898",
            "senderId": "SENDER123",
            "billerId": "BILLER456",
            "traceId": "TRACE-999-006",
            "applicationMode": "production"
        }
    }'::jsonb;
    
    v_response_id := insert_test_response_log('999', v_response_json);
    PERFORM pg_sleep(0.5);
    
    -- Get the main record ID
    SELECT id INTO v_main_record_id
    FROM form_med_claim_resp
    WHERE response_id = v_response_id;
    
    PERFORM assert_not_null(v_main_record_id::TEXT, 'Main 999 record ID for complete response');
    
    -- Verify all subforms were created
    PERFORM assert_record_exists(
        'form_med_claim_resp_ch_pyr pyr JOIN sf_form_med_claim_resp_to_med_claim_resp_ch_pyr sf ON sf.form_med_claim_resp_ch_pyr_fk = pyr.id',
        'sf.form_med_claim_resp_fk = ' || v_main_record_id || ' AND pyr.deleted IS NOT TRUE AND pyr.archived IS NOT TRUE AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE',
        'Payer subform for complete response'
    );
    
    PERFORM assert_record_exists(
        'form_med_claim_resp_ch_meta meta JOIN sf_form_med_claim_resp_to_med_claim_resp_ch_meta sf ON sf.form_med_claim_resp_ch_meta_fk = meta.id',
        'sf.form_med_claim_resp_fk = ' || v_main_record_id || ' AND meta.deleted IS NOT TRUE AND meta.archived IS NOT TRUE AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE',
        'Meta subform for complete response'
    );
    
    PERFORM assert_record_exists(
        'form_med_claim_resp_err err JOIN sf_form_med_claim_resp_to_med_claim_resp_err sf ON sf.form_med_claim_resp_err_fk = err.id',
        'sf.form_med_claim_resp_fk = ' || v_main_record_id || ' AND err.deleted IS NOT TRUE AND err.archived IS NOT TRUE AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE',
        'Error subform for complete response'
    );
    
    PERFORM assert_record_exists(
        'form_med_claim_resp_edit edit JOIN sf_form_med_claim_resp_to_med_claim_resp_edit sf ON sf.form_med_claim_resp_edit_fk = edit.id',
        'sf.form_med_claim_resp_fk = ' || v_main_record_id || ' AND edit.deleted IS NOT TRUE AND edit.archived IS NOT TRUE AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE',
        'Edit response subform for complete response'
    );
    
    -- Verify counts
    SELECT COUNT(*) INTO v_error_count
    FROM form_med_claim_resp_err err
    JOIN sf_form_med_claim_resp_to_med_claim_resp_err sf
        ON sf.form_med_claim_resp_err_fk = err.id
    WHERE sf.form_med_claim_resp_fk = v_main_record_id
    AND err.deleted IS NOT TRUE
    AND err.archived IS NOT TRUE
    AND sf.delete IS NOT TRUE
    AND sf.archive IS NOT TRUE;
    
    PERFORM assert_numeric_equals('Error count for complete response', 1::NUMERIC, v_error_count::NUMERIC);
    
    SELECT COUNT(*) INTO v_edit_response_count
    FROM form_med_claim_resp_edit edit
    JOIN sf_form_med_claim_resp_to_med_claim_resp_edit sf
        ON sf.form_med_claim_resp_edit_fk = edit.id
    WHERE sf.form_med_claim_resp_fk = v_main_record_id
    AND edit.deleted IS NOT TRUE
    AND edit.archived IS NOT TRUE
    AND sf.delete IS NOT TRUE
    AND sf.archive IS NOT TRUE;
    
    PERFORM assert_numeric_equals('Edit response count for complete response', 1::NUMERIC, v_edit_response_count::NUMERIC);
    
    RAISE NOTICE '=== All 999 Response Tests Passed ===';
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Test failed: %', SQLERRM;
    RAISE;
END $$;

ROLLBACK; 