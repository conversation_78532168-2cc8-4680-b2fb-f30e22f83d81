DO $$
DECLARE
    -- Variable declarations
    v_message_json JSONB;
    v_header_json JSONB;
    v_patient_json JSONB;
    v_prescriber_json JSONB;
    v_pharmacy_json JSONB;
    v_response_json JSONB;
    v_medication_json JSONB;
    v_observations_json JSONB;

    v_ss_message_id INTEGER;
    v_temp_text TEXT;
    v_temp_int INTEGER;
    v_temp_date DATE;
    v_temp_text_array TEXT[];
    error_message TEXT;
    message_id_value TEXT := 'test-changeres-max-pop-' || floor(random()***********)::TEXT;
    v_diag_record RECORD;
    v_compound_record RECORD;
    v_obs_record RECORD;
    v_due_record RECORD;
    v_allergy_record RECORD;
BEGIN
    -- Build the JSON directly using jsonb_build_object
    
    -- 1. Build the Header section
    v_header_json := jsonb_build_object(
        'To', 111111,
        'From', 6301875277001,
        'MessageID', message_id_value,
        'RelatesToMessageID', 'echo from NEWRX',
        'SentTime', '2019-01-01T13:42:39.7Z',
        'SenderSoftware', jsonb_build_object(
            'SenderSoftwareDeveloper', 'Surescripts',
            'SenderSoftwareProduct', 'Certification Testing',
            'SenderSoftwareVersionRelease', 20170715
        ),
        'PrescriberOrderNumber', 'ECHO FROM NEWRX',
        'DigitalSignature', jsonb_build_object(
            'DigitalSignatureIndicator', true
        )
    );
    
    -- 2. Build the Patient section
    v_patient_json := jsonb_build_object(
        'HumanPatient', jsonb_build_object(
            'Name', jsonb_build_object(
                'LastName', 'Delaplaine',
                'FirstName', 'Angelyne'
            ),
            'Gender', 'F',
            'DateOfBirth', jsonb_build_object(
                'Date', '2012-09-01'
            ),
            'Address', jsonb_build_object(
                'AddressLine1', '901 Sauvblanc Blvd',
                'City', 'Petaluma',
                'StateProvince', 'VA',
                'PostalCode', 94952,
                'CountryCode', 'US'
            )
        )
    );
    
    -- 3. Build the Pharmacy section
    v_pharmacy_json := jsonb_build_object(
        'Identification', jsonb_build_object(
            'NCPDPID', 1655458,
            'StateLicenseNumber', '784577%PH47%24R',
            'MedicareNumber', 755449,
            'MedicaidNumber', 755449,
            'DEANumber', '*********',
            'NPI', **********
        ),
        'BusinessName', 'Shollenberger Pharmacy',
        'Address', jsonb_build_object(
            'AddressLine1', '2002 S. McDowell Blvd Ext',
            'City', 'Petaluma',
            'StateProvince', 'CA',
            'PostalCode', 94954,
            'CountryCode', 'US'
        ),
        'CommunicationNumbers', jsonb_build_object(
            'PrimaryTelephone', jsonb_build_object(
                'Number', **********
            ),
            'Fax', jsonb_build_object(
                'Number', **********
            )
        )
    );
    
    -- 4. Build the Prescriber section
    v_prescriber_json := jsonb_build_object(
        'NonVeterinarian', jsonb_build_object(
            'Identification', jsonb_build_object(
                'StateLicenseNumber', '784577%1147%24',
                'MedicaidNumber', 658345,
                'DEANumber', '*********',
                'NPI', 222222,
                'CertificateToPrescribe', 'CTP.CA.1147%24'
            ),
            'Specialty', '363L00000X',
            'PracticeLocation', jsonb_build_object(
                'BusinessName', 'MediStar of California'
            ),
            'Name', jsonb_build_object(
                'LastName', 'Pimpernel',
                'FirstName', 'Marguerite',
                'MiddleName', 'Anne',
                'Suffix', 'MD',
                'Prefix', 'DR'
            ),
            'Address', jsonb_build_object(
                'AddressLine1', '1425 Mendocino Ave',
                'AddressLine2', 'Suite 12-A',
                'City', 'Santa Rosa',
                'StateProvince', 'CA',
                'PostalCode', 95401,
                'CountryCode', 'US'
            ),
            'CommunicationNumbers', jsonb_build_object(
                'PrimaryTelephone', jsonb_build_object(
                    'Number', **********,
                    'Extension', 4221
                ),
                'ElectronicMail', '<EMAIL>',
                'Fax', jsonb_build_object(
                    'Number', 7079442121
                )
            )
        )
    );
    
    -- 5. Build the Response section
    v_response_json := jsonb_build_object(
        'ApprovedWithChanges', jsonb_build_object(
            'Note', 'Therapy change approved'
        )
    );
    
    -- 6. Build the Observations section
    v_observations_json := jsonb_build_array(
            jsonb_build_object(
                'VitalSign', '8302-2',
                'LOINCVersion', 2.42,
                'Value', 46,
                'UnitOfMeasure', '[in_i]',
                'UCUMVersion', 2.1,
                'ObservationDate', jsonb_build_object(
                    'Date', '2019-01-01'
                )
            ),
            jsonb_build_object(
                'VitalSign', '29463-7',
                'LOINCVersion', 2.42,
                'Value', 48,
                'UnitOfMeasure', '[lb_av]',
                'UCUMVersion', 2.1,
                'ObservationDate', jsonb_build_object(
                    'Date', '2019-01-01'
                )
            )
    );
    
    -- 7. Build the MedicationPrescribed section
    v_medication_json := jsonb_build_object(
        'DrugDescription', 'Vimpat 50 mg tablet',
        'DrugCoded', jsonb_build_object(
            'ProductCode', jsonb_build_object('Code', 131247735, 'Qualifier', 'ND'),
            'Strength', jsonb_build_object(
                'StrengthValue', 50,
                'StrengthUnitOfMeasure', jsonb_build_object('Code', 'C42576')
            ),
            'DrugDBCode', jsonb_build_object('Code', 810002, 'Qualifier', 'SBD'),
            'DEASchedule', jsonb_build_object('Code', 'C48679')
        ),
        'Quantity', jsonb_build_object(
            'Value', 100,
            'CodeListQualifier', 38,
            'QuantityUnitOfMeasure', jsonb_build_object('Code', 'C48542')
        ),
        'WrittenDate', jsonb_build_object('Date', '2019-01-01'),
        'Substitutions', 0,
        'NumberOfRefills', 0,
        'Diagnosis', jsonb_build_object(
            'ClinicalInformationQualifier', 1,
            'Primary', jsonb_build_object(
                'Code', 'G4000',
                'Qualifier', 'ABF',
                'Description', 'Localization-related (focal) (partial) idiopathic epilepsy and epileptic syndromes with seizures of localized onset, not intractable'
            )
        ),
        'Note', 'Suggested change to tablet as solution is not recommended for pediatric patient.',
        'Sig', jsonb_build_object(
            'SigText', 'Take 0.5 tablet by mouth twice daily for 1 week, then take 1 tablet by mouth twice daily for 1 week, then take 1.5 tablets by mouth twice daily for 1 week, then take 2 tablets by mouth twice daily for 2 weeks. Discard remaining tablets.',
            'CodeSystem', jsonb_build_object('SNOMEDVersion', 20170131, 'FMTVersion', '16.03d'),
            'Instruction', jsonb_build_array(
                jsonb_build_object(
                    'DoseAdministration', jsonb_build_object(
                        'DoseDeliveryMethod', jsonb_build_object('Text', 'Take', 'Qualifier', 'SNOMED', 'Code', 419652001),
                        'Dosage', jsonb_build_object('DoseQuantity', 0.5, 'DoseUnitOfMeasure', jsonb_build_object('Text', 'tablet', 'Qualifier', 'DoseUnitOfMeasure', 'Code', 'C48542')),
                        'RouteOfAdministration', jsonb_build_object('Text', 'oral route', 'Qualifier', 'SNOMED', 'Code', 26643006)
                    ),
                    'TimingAndDuration', jsonb_build_array(
                        jsonb_build_object('Frequency', jsonb_build_object('FrequencyNumericValue', 2, 'FrequencyUnits', jsonb_build_object('Text', 'day', 'Qualifier', 'SNOMED', 'Code', 258703001))),
                        jsonb_build_object('Duration', jsonb_build_object('DurationNumericValue', 1, 'DurationText', jsonb_build_object('Text', 'week', 'Qualifier', 'SNOMED', 'Code', 258705008)))
                    )
                ),
                jsonb_build_object(
                    'DoseAdministration', jsonb_build_object(
                        'DoseDeliveryMethod', jsonb_build_object('Text', 'Take', 'Qualifier', 'SNOMED', 'Code', 419652001),
                        'Dosage', jsonb_build_object('DoseQuantity', 1, 'DoseUnitOfMeasure', jsonb_build_object('Text', 'tablet', 'Qualifier', 'DoseUnitOfMeasure', 'Code', 'C48542')),
                        'RouteOfAdministration', jsonb_build_object('Text', 'oral route', 'Qualifier', 'SNOMED', 'Code', 26643006)
                    ),
                    'TimingAndDuration', jsonb_build_array(
                        jsonb_build_object('Frequency', jsonb_build_object('FrequencyNumericValue', 2, 'FrequencyUnits', jsonb_build_object('Text', 'day', 'Qualifier', 'SNOMED', 'Code', 258703001))),
                        jsonb_build_object('Duration', jsonb_build_object('DurationNumericValue', 1, 'DurationText', jsonb_build_object('Text', 'week', 'Qualifier', 'SNOMED', 'Code', 258705008)))
                    )
                ),
                jsonb_build_object(
                    'DoseAdministration', jsonb_build_object(
                        'DoseDeliveryMethod', jsonb_build_object('Text', 'Take', 'Qualifier', 'SNOMED', 'Code', 419652001),
                        'Dosage', jsonb_build_object('DoseQuantity', 1.5, 'DoseUnitOfMeasure', jsonb_build_object('Text', 'tablet', 'Qualifier', 'DoseUnitOfMeasure', 'Code', 'C48542')),
                        'RouteOfAdministration', jsonb_build_object('Text', 'oral route', 'Qualifier', 'SNOMED', 'Code', 26643006)
                    ),
                    'TimingAndDuration', jsonb_build_array(
                        jsonb_build_object('Frequency', jsonb_build_object('FrequencyNumericValue', 2, 'FrequencyUnits', jsonb_build_object('Text', 'day', 'Qualifier', 'SNOMED', 'Code', 258703001))),
                        jsonb_build_object('Duration', jsonb_build_object('DurationNumericValue', 1, 'DurationText', jsonb_build_object('Text', 'week', 'Qualifier', 'SNOMED', 'Code', 258705008)))
                    )
                ),
                jsonb_build_object(
                    'DoseAdministration', jsonb_build_object(
                        'DoseDeliveryMethod', jsonb_build_object('Text', 'Take', 'Qualifier', 'SNOMED', 'Code', 419652001),
                        'Dosage', jsonb_build_object('DoseQuantity', 2, 'DoseUnitOfMeasure', jsonb_build_object('Text', 'tablet', 'Qualifier', 'DoseUnitOfMeasure', 'Code', 'C48542')),
                        'RouteOfAdministration', jsonb_build_object('Text', 'oral route', 'Qualifier', 'SNOMED', 'Code', 26643006)
                    ),
                    'TimingAndDuration', jsonb_build_array(
                        jsonb_build_object('Frequency', jsonb_build_object('FrequencyNumericValue', 2, 'FrequencyUnits', jsonb_build_object('Text', 'day', 'Qualifier', 'SNOMED', 'Code', 258703001))),
                        jsonb_build_object('Duration', jsonb_build_object('DurationNumericValue', 2, 'DurationText', jsonb_build_object('Text', 'week', 'Qualifier', 'SNOMED', 'Code', 258705008)))
                    )
                )
            ),
            'MultipleInstructionModifier', jsonb_build_array('THEN', 'THEN', 'THEN'),
            'ClarifyingFreeText', 'Discard remaining tablets.'
        ),
        'RxFillIndicator', 'Cancel All Fill Statuses'
    );
    
    -- 8. Assemble the complete message JSON
    v_message_json := jsonb_build_object(
            '?xml', '',
            'Message', jsonb_build_object(
                'Header', v_header_json,
                'Body', jsonb_build_object(
                    'RxChangeResponse', jsonb_build_object(
                        'MessageRequestCode', 'T',
                        'Response', v_response_json,
                        'Patient', v_patient_json,
                        'Pharmacy', v_pharmacy_json,
                        'Prescriber', v_prescriber_json,
                        'Measurement', v_observations_json,
                        'MedicationPrescribed', v_medication_json
                    )
                )
            )
    );

    -- Process the message
    SELECT tp.ss_message_id, tp.error_message INTO v_ss_message_id, error_message
    FROM _parse_and_insert_rxchangeresponse(
        p_ss_log_id := 99999997, -- Fixed log ID for testing
        p_message_json := v_message_json::JSONB,
        p_surescripts_message_type := 'RxChangeResponse',
        p_header_data := v_header_json::JSONB,
        p_patient_data := v_patient_json::JSONB,
        p_prescriber_data := v_prescriber_json::JSONB,
        p_priority_flag := NULL::TEXT 
    ) tp;

    IF error_message IS NOT NULL THEN
        RAISE EXCEPTION 'Parser error: %', error_message;
    END IF;

    RAISE NOTICE 'Processed RxChangeResponse message, ss_message_id: % using Cert_ChangeRES-6.json', v_ss_message_id;

    -- Assertions for form_ss_message
    PERFORM assert_not_null(v_ss_message_id::TEXT, 'ss_message_id');

    -- Header fields
    PERFORM assert_equals('111111', (SELECT send_to FROM form_ss_message WHERE id = v_ss_message_id), 'send_to');
    PERFORM assert_equals('6301875277001', (SELECT sent_from FROM form_ss_message WHERE id = v_ss_message_id), 'sent_from');
    PERFORM assert_equals(message_id_value, (SELECT message_id FROM form_ss_message WHERE id = v_ss_message_id), 'message_id (internal unique ID)');
    PERFORM assert_equals('echo from NEWRX', (SELECT related_message_id FROM form_ss_message WHERE id = v_ss_message_id), 'related_message_id');
    PERFORM assert_equals('2019-01-01 13:42:39.7', (SELECT sent_dt::TEXT FROM form_ss_message WHERE id = v_ss_message_id), 'sent_dt');
    PERFORM assert_equals('Surescripts', (SELECT sender_software_developer FROM form_ss_message WHERE id = v_ss_message_id), 'sender_software_developer');
    PERFORM assert_equals('Certification Testing', (SELECT sender_software_product FROM form_ss_message WHERE id = v_ss_message_id), 'sender_software_product');
    PERFORM assert_equals('20170715', (SELECT sender_software_version FROM form_ss_message WHERE id = v_ss_message_id), 'sender_software_version');
    PERFORM assert_equals('ECHO FROM NEWRX', (SELECT physician_order_id FROM form_ss_message WHERE id = v_ss_message_id), 'physician_order_id (ss_prescriber_order_number)');
    PERFORM assert_null((SELECT pharmacy_rx_no FROM form_ss_message WHERE id = v_ss_message_id), 'pharmacy_rx_no (ss_pharmacy_rx_no)');
    PERFORM assert_equals('true', (SELECT digital_signature_indicator FROM form_ss_message WHERE id = v_ss_message_id), 'digital_signature_indicator');

    -- Message Type & Direction
    PERFORM assert_equals('RxChangeResponse', (SELECT message_type FROM form_ss_message WHERE id = v_ss_message_id), 'message_type');
    PERFORM assert_equals('IN', (SELECT direction FROM form_ss_message WHERE id = v_ss_message_id), 'direction');

    -- RxChangeResponse specific fields
    PERFORM assert_equals('T', (SELECT chg_type_id FROM form_ss_message WHERE id = v_ss_message_id), 'chg_type_id (MessageRequestCode)');
    PERFORM assert_equals('ApprovedWithChanges', (SELECT chg_status FROM form_ss_message WHERE id = v_ss_message_id), 'ss_chg_status (ApprovedWithChanges)');
    PERFORM assert_equals('Therapy change approved', (SELECT chg_approved_note FROM form_ss_message WHERE id = v_ss_message_id), 'ss_chg_approved_note');

    -- Patient fields
    PERFORM assert_equals('Delaplaine, Angelyne', (SELECT patient_name_display FROM form_ss_message WHERE id = v_ss_message_id), 'patient_name_display');
    PERFORM assert_equals('Angelyne', (SELECT patient_first_name FROM form_ss_message WHERE id = v_ss_message_id), 'patient_first_name');
    PERFORM assert_equals('Delaplaine', (SELECT patient_last_name FROM form_ss_message WHERE id = v_ss_message_id), 'patient_last_name');
    PERFORM assert_equals('2012-09-01', (SELECT patient_dob::TEXT FROM form_ss_message WHERE id = v_ss_message_id), 'patient_dob');
    PERFORM assert_equals('F', (SELECT patient_gender FROM form_ss_message WHERE id = v_ss_message_id), 'patient_gender');

    -- Prescriber fields
    PERFORM assert_equals('Pimpernel, Marguerite', (SELECT prescriber_name_display FROM form_ss_message WHERE id = v_ss_message_id), 'prescriber_name_display');
    PERFORM assert_equals('222222', (SELECT prescriber_npi FROM form_ss_message WHERE id = v_ss_message_id), 'prescriber_npi');
    PERFORM assert_equals('*********', (SELECT prescriber_dea FROM form_ss_message WHERE id = v_ss_message_id), 'prescriber_dea');

    -- Medication fields
    PERFORM assert_equals('Vimpat 50 mg tablet', (SELECT description FROM form_ss_message WHERE id = v_ss_message_id), 'description');
    PERFORM assert_equals('ND', (SELECT product_code_qualifier_id FROM form_ss_message WHERE id = v_ss_message_id), 'product_code_qualifier_id');
    PERFORM assert_equals('131247735', (SELECT product_code FROM form_ss_message WHERE id = v_ss_message_id), 'product_code');
    PERFORM assert_equals('50', (SELECT strength FROM form_ss_message WHERE id = v_ss_message_id), 'strength');
    PERFORM assert_equals('C42576', (SELECT strength_uom_id FROM form_ss_message WHERE id = v_ss_message_id), 'strength_uom_id');
    PERFORM assert_equals('810002', (SELECT drug_db_code FROM form_ss_message WHERE id = v_ss_message_id), 'drug_db_code');
    PERFORM assert_equals('SBD', (SELECT drug_db_qualifier_id FROM form_ss_message WHERE id = v_ss_message_id), 'drug_db_qualifier_id');
    PERFORM assert_equals('C48679', (SELECT dea_schedule_id FROM form_ss_message WHERE id = v_ss_message_id), 'dea_schedule_id');
    PERFORM assert_equals('100', (SELECT quantity::TEXT FROM form_ss_message WHERE id = v_ss_message_id), 'quantity');
    PERFORM assert_equals('C48542', (SELECT quantity_uom_id FROM form_ss_message WHERE id = v_ss_message_id), 'quantity_uom_id');
    PERFORM assert_equals('2019-01-01', (SELECT written_date::TEXT FROM form_ss_message WHERE id = v_ss_message_id), 'written_date');
    PERFORM assert_equals('0', (SELECT refills::TEXT FROM form_ss_message WHERE id = v_ss_message_id), 'refills');
    PERFORM assert_equals('Suggested change to tablet as solution is not recommended for pediatric patient.', (SELECT note FROM form_ss_message WHERE id = v_ss_message_id), 'note');
    PERFORM assert_equals('Take 0.5 tablet by mouth twice daily for 1 week, then take 1 tablet by mouth twice daily for 1 week, then take 1.5 tablets by mouth twice daily for 1 week, then take 2 tablets by mouth twice daily for 2 weeks. Discard remaining tablets.', (SELECT sig FROM form_ss_message WHERE id = v_ss_message_id), 'sig');
    PERFORM assert_equals('1', (SELECT clinical_info_qualifier FROM form_ss_message WHERE id = v_ss_message_id), 'clinical_info_qualifier');

    -- Status Icons and Show Options
    SELECT status_icons INTO v_temp_text_array FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_text_array_equals(ARRAY['new', 'approved'], v_temp_text_array, 'status_icons');
    
    SELECT show_options INTO v_temp_text_array FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_text_array_equals(ARRAY['Change Response', 'Observations', 'Practice Location'], v_temp_text_array, 'show_options');

    -- Subform Counts
    PERFORM assert_equals('1', (SELECT COUNT(*)::TEXT FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = v_ss_message_id), 'form_ss_diagnosis count');
    PERFORM assert_equals('0', (SELECT COUNT(*)::TEXT FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = v_ss_message_id), 'form_ss_compound count');
    PERFORM assert_equals('2', (SELECT COUNT(*)::TEXT FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = v_ss_message_id), 'form_ss_observation count');

    -- Detailed Diagnosis Checks
    FOR v_diag_record IN SELECT type, dx_code, dx_code_qualifier_id, dx_desc FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = v_ss_message_id ORDER BY type
    LOOP
        IF v_diag_record.type = 'Primary' THEN
            PERFORM assert_equals('G4000', v_diag_record.dx_code, 'Primary Diagnosis dx_code');
            PERFORM assert_equals('ABF', v_diag_record.dx_code_qualifier_id, 'Primary Diagnosis dx_code_qualifier_id');
            PERFORM assert_equals('Localization-related (focal) (partial) idiopathic epilepsy and epileptic syndromes with seizures of localized onset, not intractable', v_diag_record.dx_desc, 'Primary Diagnosis dx_desc');
        END IF;
    END LOOP;

    -- Detailed Observation Checks (order by VitalSign as a proxy for order in JSON)
    FOR v_obs_record IN SELECT type_id, value::TEXT, unit_id, observation_date::TEXT FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = v_ss_message_id ORDER BY type_id
    LOOP
        IF v_obs_record.type_id = '29463-7' THEN -- Weight
            PERFORM assert_equals('48', v_obs_record.value, 'Observation Weight value');
            PERFORM assert_equals('[lb_av]', v_obs_record.unit_id, 'Observation Weight unit_id');
            PERFORM assert_equals('2019-01-01', v_obs_record.observation_date, 'Observation Weight date');
        ELSIF v_obs_record.type_id = '8302-2' THEN -- Height
            PERFORM assert_equals('46', v_obs_record.value, 'Observation Height value');
            PERFORM assert_equals('[in_i]', v_obs_record.unit_id, 'Observation Height unit_id');
            PERFORM assert_equals('2019-01-01', v_obs_record.observation_date, 'Observation Height date');
        END IF;
    END LOOP;

    RAISE NOTICE 'All RxChangeResponse assertions passed for Cert_ChangeRES-6.json!';

END;
$$; 