-- 0016-test-1500-claim-generation.sql
-- Test CMS-1500 paper claim generation for medical claims
-- Tests build_mm_1500_claim and mm_1500_record_to_json functions

DO $$
DECLARE
    -- Test data IDs
    v_test_site_id integer;
    v_test_company_id integer := 1; -- Always use existing company record
    v_test_payer_id integer;
    v_test_patient_id integer;
    v_test_patient_ins_id integer;
    v_test_prescriber_id integer;
    v_test_supervisor_id integer;
    v_test_patient_prescriber_id integer;
    v_test_patient_supervisor_id integer;
    v_test_user_id integer;
    v_test_inventory_drug_id integer;
    v_test_inventory_rental_id integer;
    v_test_inventory_billable_id integer;
    v_test_inventory_supply_id integer;
    v_test_careplan_id integer;
    v_test_careplan_order_id integer;
    v_test_careplan_order_rx_id integer;
    
    -- Charge line IDs
    v_test_charge_line_drug_id integer;
    v_test_charge_line_rental_id integer;
    v_test_charge_line_billable_id integer;
    v_test_charge_line_supply_id integer;
    
    -- Result records
    v_1500_claim_record mm_1500_claim_record;
    v_1500_claim_json json;
    
    -- Assertion helper
    v_test_name text;
    v_assertion_failed boolean := false;

BEGIN
    RAISE NOTICE 'Starting CMS-1500 Claim Generation Test Suite';
    
    -- Use existing company record (ID = 1)
    
    -- Create test site with all CMS-1500 relevant settings
    INSERT INTO form_site (
        name, company_id, npi, address1, address2, city, state_id, zip,
        phone, fax, tax_id
    ) VALUES (
        'CMS1500 Test Site', v_test_company_id, '**********',
        '1500 Claim Ave', 'Suite 100', 'Billing City', 'TX', '75002',
        '**********', '**********', '99-8765432'
    ) RETURNING id INTO v_test_site_id;
    
    -- Create test physicians without physician_group_id
    
    -- Create test prescriber with all ID types for CMS-1500
    INSERT INTO form_physician (
        first, last, npi, dea, state_license,
        primary_phone, primary_fax
    ) VALUES (
        'John', 'Prescriber', '**********', 'DEA123456', 'LIC123456',
        '**********', '**********'
    ) RETURNING id INTO v_test_prescriber_id;
    
    -- Create test supervisor with different qualifiers
    INSERT INTO form_physician (
        first, last, npi, dea, state_license,
        primary_phone, primary_fax
    ) VALUES (
        'Jane', 'Supervisor', '**********', 'DEA234567', 'LIC234567',
        '**********', '**********'
    ) RETURNING id INTO v_test_supervisor_id;
    
    -- Create payer with CMS-1500 settings
    INSERT INTO form_payer (
        organization, address1, address2, city, state_id, zip, phone,
        type_id, billing_method_id, active
    ) VALUES (
        'CMS1500 Test Payer', '999 Insurance Blvd', 'Floor 5',
        'Insurance City', 'TX', '75004', '**********',
        'primary', 'cms1500', 'Yes'
    ) RETURNING id INTO v_test_payer_id;
    
    -- Create test patient with complete demographics
    INSERT INTO form_patient (
        external_id, firstname, middlename, lastname,
        dob, gender, ssn, phone_home, email, site_id
    ) VALUES (
        'CMS1500-00001', 'Robert', 'James', 'Patient',
        '1955-03-25', 'M', '987654321', '**********', '<EMAIL>',
        v_test_site_id
    ) RETURNING id INTO v_test_patient_id;
    
    -- Create patient address
    INSERT INTO form_patient_address (
        patient_id, street, street2, city, state_id, zip
    ) VALUES (
        v_test_patient_id, '321 Patient Parkway', 'Apt 4D',
        'Patient Town', 'TX', '75005'
    );
    
    -- Create patient insurance with complete subscriber information
    INSERT INTO form_patient_insurance (
        patient_id, payer_id, bin, group_number, policy_number, active,
        billing_method_id, type_id, cardholder_id
    ) VALUES (
        v_test_patient_id, v_test_payer_id, 'CMS1500MB123456',
        'GHP001', 'PPO Gold Plan', 'Yes', 'cms1500', 'primary', 'CMS1500-MEMBER-001'
    ) RETURNING id INTO v_test_patient_ins_id;
    
    -- Debug: Check if insurance record was created
    RAISE NOTICE 'Created patient insurance ID: %', v_test_patient_ins_id;
    
    -- Create test user
    INSERT INTO form_user (
        username, firstname, lastname
    ) VALUES (
        'cms1500testuser', 'CMS1500', 'Tester'
    ) RETURNING id INTO v_test_user_id;
    
    -- Create patient-prescriber relationships
    INSERT INTO form_patient_prescriber (
        patient_id, physician_id
    ) VALUES (
        v_test_patient_id, v_test_prescriber_id
    ) RETURNING id INTO v_test_patient_prescriber_id;
    
    INSERT INTO form_patient_prescriber (
        patient_id, physician_id
    ) VALUES (
        v_test_patient_id, v_test_supervisor_id
    ) RETURNING id INTO v_test_patient_supervisor_id;
    
    -- Create inventory items with NDC codes and descriptions
    INSERT INTO form_inventory (
        name, type, hcpc_id, ndc, billing_unit_id
    ) VALUES
    ('Test Injectable Drug', 'Drug', 'J3420', '12345-6789-01', 'mg')
    RETURNING id INTO v_test_inventory_drug_id;
    
    INSERT INTO form_inventory (
        name, type, hcpc_id, billing_unit_id
    ) VALUES
    ('Test DME Equipment', 'Equipment Rental', 'E0601', 'EA')
    RETURNING id INTO v_test_inventory_rental_id;
    
    INSERT INTO form_inventory (
        name, type, hcpc_id, billing_unit_id
    ) VALUES
    ('Test Nursing Service', 'Billable', 'G0299', 'UN')
    RETURNING id INTO v_test_inventory_billable_id;
    
    INSERT INTO form_inventory (
        name, type, hcpc_id, ndc, billing_unit_id
    ) VALUES
    ('Test Medical Supply', 'Supply', 'A4206', '98765-4321-01', 'BX')
    RETURNING id INTO v_test_inventory_supply_id;
    
    -- Create careplan
    INSERT INTO form_careplan (
        patient_id, therapy_start, therapy_end
    ) VALUES (
        v_test_patient_id, CURRENT_DATE - INTERVAL '60 days', CURRENT_DATE + INTERVAL '305 days'
    ) RETURNING id INTO v_test_careplan_id;
    
    -- Create careplan order
    INSERT INTO form_careplan_order (
        patient_id, site_id, prescriber_id, order_no
    ) VALUES (
        v_test_patient_id, v_test_site_id, v_test_prescriber_id, 'CMS1500-ORDER-001'
    ) RETURNING id INTO v_test_careplan_order_id;
    
    -- Create careplan order rx
    INSERT INTO form_careplan_order_rx (
        patient_id, order_no, rx_no, inventory_id, site_id
    ) VALUES (
        v_test_patient_id, 'CMS1500-ORDER-001', 'CMS1500-RX-001',
        v_test_inventory_drug_id, v_test_site_id
    ) RETURNING id INTO v_test_careplan_order_rx_id;
    
    -- Create charge lines
    INSERT INTO form_ledger_charge_line (
        charge_no, site_id, patient_id, insurance_id, payer_id,
        inventory_id, date_of_service, date_of_service_end,
        bill_quantity, billed, billing_method_id, modifier_1
    ) VALUES
    ('CMS1500-DRUG-001', v_test_site_id, v_test_patient_id, v_test_patient_ins_id,
     v_test_payer_id, v_test_inventory_drug_id, CURRENT_DATE - INTERVAL '30 days',
     CURRENT_DATE - INTERVAL '30 days', 250, 375.00, 'cms1500', 'JW')
    RETURNING id INTO v_test_charge_line_drug_id;
    
    INSERT INTO form_ledger_charge_line (
        charge_no, site_id, patient_id, insurance_id, payer_id,
        inventory_id, date_of_service, date_of_service_end,
        bill_quantity, billed, billing_method_id, modifier_1, rental_type
    ) VALUES
    ('CMS1500-RENTAL-001', v_test_site_id, v_test_patient_id, v_test_patient_ins_id,
     v_test_payer_id, v_test_inventory_rental_id, CURRENT_DATE - INTERVAL '30 days',
     CURRENT_DATE - INTERVAL '1 day', 1, 450.00, 'cms1500', 'RR', 'Rental')
    RETURNING id INTO v_test_charge_line_rental_id;
    
    INSERT INTO form_ledger_charge_line (
        charge_no, site_id, patient_id, insurance_id, payer_id,
        inventory_id, date_of_service, date_of_service_end,
        bill_quantity, billed, billing_method_id, modifier_1
    ) VALUES
    ('CMS1500-BILLABLE-001', v_test_site_id, v_test_patient_id, v_test_patient_ins_id,
     v_test_payer_id, v_test_inventory_billable_id, CURRENT_DATE - INTERVAL '14 days',
     CURRENT_DATE - INTERVAL '14 days', 2, 200.00, 'cms1500', 'HQ')
    RETURNING id INTO v_test_charge_line_billable_id;
    
    INSERT INTO form_ledger_charge_line (
        charge_no, site_id, patient_id, insurance_id, payer_id,
        inventory_id, date_of_service, date_of_service_end,
        bill_quantity, billed, billing_method_id
    ) VALUES
    ('CMS1500-SUPPLY-001', v_test_site_id, v_test_patient_id, v_test_patient_ins_id,
     v_test_payer_id, v_test_inventory_supply_id, CURRENT_DATE - INTERVAL '7 days',
     CURRENT_DATE - INTERVAL '7 days', 5, 125.00, 'cms1500')
    RETURNING id INTO v_test_charge_line_supply_id;
    
    -- TEST 1: Build CMS-1500 claim with all charge lines
    RAISE NOTICE '';
    RAISE NOTICE '=== TEST 1: Build CMS-1500 Claim ===';
    
    -- First need to get charge lines in the proper format
    DECLARE
        v_charge_lines charge_line_with_split[];
    BEGIN
        -- Get charge lines data
        SELECT array_agg(
            ROW(
                lcl.id, lcl.calc_invoice_split_no, lcl.site_id, lcl.patient_id,
                lcl.insurance_id, lcl.payer_id, lcl.inventory_id, lcl.shared_contract_id,
                lcl.is_dirty, lcl.charge_no, lcl.parent_charge_no, lcl.master_charge_no,
                lcl.compound_no, lcl.ticket_no, lcl.ticket_item_no, 
                lcl.rental_id, lcl.order_rx_id, lcl.is_primary_drug, lcl.is_primary_drug_ncpdp, 
                lcl.rx_no, lcl.inventory_type_filter, lcl.inventory_type, lcl.revenue_code_id,
                lcl.hcpc_code, lcl.ndc, lcl.formatted_ndc, lcl.gcn_seqno,
                lcl.charge_quantity, lcl.charge_quantity_ea, lcl.hcpc_quantity,
                lcl.hcpc_unit, lcl.metric_quantity, lcl.charge_unit,
                lcl.bill_quantity, lcl.metric_unit_each, lcl.billing_unit_id,
                lcl.billing_method_id, lcl.pricing_source, lcl.billed,
                lcl.calc_billed_ea, lcl.expected, lcl.calc_expected_ea,
                lcl.list_price, lcl.calc_list_ea, lcl.total_cost,
                lcl.gross_amount_due, lcl.incv_amt_sub, lcl.copay,
                lcl.calc_cost_ea, lcl.total_adjusted, lcl.total_balance_due, 
                lcl.dispense_fee, lcl.pt_pd_amt_sub, lcl.encounter_id, 
                lcl.description, lcl.upc, lcl.upin, lcl.cost_basis, lcl.awp_price,
                lcl.modifier_1, lcl.modifier_2, lcl.modifier_3, lcl.modifier_4,
                lcl.rental_type, lcl.frequency_code, lcl.fill_number,
                lcl.paid, lcl.date_of_service, lcl.date_of_service_end
            )::charge_line_with_split
        ) INTO v_charge_lines
        FROM form_ledger_charge_line lcl
        WHERE lcl.id IN (
            v_test_charge_line_drug_id,
            v_test_charge_line_rental_id,
            v_test_charge_line_billable_id,
            v_test_charge_line_supply_id
        );
        
        -- Debug: Check if insurance record still exists before function call
        RAISE NOTICE 'About to call build_mm_1500_claim with insurance ID: %', v_test_patient_ins_id;
        PERFORM 1 FROM form_patient_insurance WHERE id = v_test_patient_ins_id;
        IF NOT FOUND THEN
            RAISE EXCEPTION 'Insurance record % does not exist before function call!', v_test_patient_ins_id;
        END IF;
        RAISE NOTICE 'Insurance record verified to exist';
        
        -- Call the function with proper parameters
        v_1500_claim_record := build_mm_1500_claim(
            p_insurance_id := v_test_patient_ins_id,
            p_payer_id := v_test_payer_id,
            p_patient_id := v_test_patient_id,
            p_site_id := v_test_site_id,
            p_date_of_service := (CURRENT_DATE - INTERVAL '30 days')::date,
            p_date_of_service_end := (CURRENT_DATE - INTERVAL '1 day')::date,
            p_charge_lines := v_charge_lines,
            p_parent_claim_no := NULL::text,
            p_patient_prescriber_id := v_test_patient_prescriber_id
        );
    END;
    
    -- Assert patient information
    RAISE NOTICE 'Patient first name: "%"', v_1500_claim_record.patient_first_name;
    RAISE NOTICE 'Patient last name: "%"', v_1500_claim_record.patient_last_name;
    RAISE NOTICE 'Billing provider name: "%"', v_1500_claim_record.bill_organization_name;
    RAISE NOTICE 'Total billed amount: "%"', v_1500_claim_record.expected;
    
    -- TEST 2: Convert to JSON
    RAISE NOTICE '';
    RAISE NOTICE '=== TEST 2: Convert CMS-1500 to JSON ===';
    
    v_1500_claim_json := mm_1500_record_to_json(v_1500_claim_record);
    
    -- Assert JSON structure - main claim fields
    PERFORM assert_json_exists('JSON - claim_no', v_1500_claim_json, 'claim_no');
    PERFORM assert_json_exists('JSON - patient_first_name', v_1500_claim_json, 'patient_first_name');
    PERFORM assert_json_exists('JSON - patient_last_name', v_1500_claim_json, 'patient_last_name');
    
    -- Clean up test data
    RAISE NOTICE '';
    RAISE NOTICE 'Cleaning up test data...';
    
    DELETE FROM form_ledger_charge_line WHERE id IN (
        v_test_charge_line_drug_id, v_test_charge_line_rental_id,
        v_test_charge_line_billable_id, v_test_charge_line_supply_id
    );
    DELETE FROM form_careplan_order_rx WHERE id = v_test_careplan_order_rx_id;
    DELETE FROM form_careplan_order WHERE id = v_test_careplan_order_id;
    DELETE FROM form_careplan WHERE id = v_test_careplan_id;
    DELETE FROM form_inventory WHERE id IN (
        v_test_inventory_drug_id, v_test_inventory_rental_id,
        v_test_inventory_billable_id, v_test_inventory_supply_id
    );
    DELETE FROM form_user WHERE id = v_test_user_id;
    DELETE FROM form_patient_insurance WHERE id = v_test_patient_ins_id;
    DELETE FROM form_patient_address WHERE patient_id = v_test_patient_id;
    DELETE FROM form_patient WHERE id = v_test_patient_id;
    DELETE FROM form_payer WHERE id = v_test_payer_id;
    DELETE FROM form_patient_prescriber WHERE id IN (v_test_patient_prescriber_id, v_test_patient_supervisor_id);
    DELETE FROM form_physician WHERE id IN (v_test_prescriber_id, v_test_supervisor_id);
    DELETE FROM form_site WHERE id = v_test_site_id;
    -- Note: Company record is not deleted as it's the system's only company record
    
    -- Final result
    IF v_assertion_failed THEN
        RAISE EXCEPTION 'One or more assertions failed. See NOTICE output for details.';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE 'All CMS-1500 claim generation tests passed successfully!';
    END IF;
    
END $$;