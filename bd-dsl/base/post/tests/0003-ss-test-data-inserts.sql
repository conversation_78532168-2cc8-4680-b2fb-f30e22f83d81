-- 0000-ss-test-data-inserts.sql
-- This file is for manual execution to populate form_ss_log with test data.
-- Ensure single quotes within the JSON string literals are properly escaped (doubled).

-- Data from: surescripts/test_scripts/inbound_json/Cert_NewRx-1_2023v1.json
--DO $$
--DECLARE
--    json_template TEXT := '{ 
--      "?xml": "", 
--      "Message": { 
--        "Header": { 
--          "To": 111111, 
--          "From": 1260306616002, 
--          "MessageID": "%s", 
--          "SentTime": "2022-11-01T13:42:39.7Z", 
--          "SenderSoftware": { "SenderSoftwareDeveloper": "Surescripts", "SenderSoftwareProduct": "Certification Testing", "SenderSoftwareVersionRelease": "20170715" }, 
--          "PrescriberOrderNumber": "CORE NEWRX 1 for-S" 
--        }, 
--        "Body": { 
--          "NewRx": { 
--            "BenefitsCoordination": { "PayerIdentification": { "PayerID": "T00000000021633", "ProcessorIdentificationNumber": "555555", "MutuallyDefined": "21456", "IINNumber": "444444" }, "PayerName": "PBMF", "CardholderID": "8351ZD", "GroupID": "JW92983", "GroupName": "JW MID-CA#7", "PBMMemberID": "PBM-ZZ-T92831 8351%%ZD" }, 
--            "Patient": { "HumanPatient": { "Name": { "LastName": "Delaplaine", "FirstName": "Zachary" }, "Gender": "M", "DateOfBirth": { "Date": "2010-12-01" }, "Address": { "AddressLine1": "901 Sauvblanc Blvd", "City": "Petaluma", "StateProvince": "CA", "PostalCode": "94952", "CountryCode": "US" } } }, 
--            "Pharmacy": { "Identification": { "NCPDPID": "1655458", "StateLicenseNumber": "784577%%PH47%%24R", "MedicareNumber": "755449", "MedicaidNumber": "755449", "DEANumber": "*********", "NPI": "**********" }, "BusinessName": "Shollenberger Pharmacy", "Address": { "AddressLine1": "2002 S. McDowell Blvd Ext", "City": "Petaluma", "StateProvince": "CA", "PostalCode": "94954", "CountryCode": "US" }, "CommunicationNumbers": { "PrimaryTelephone": { "Number": "**********" }, "Fax": { "Number": "**********" } } }, 
--            "Prescriber": { "NonVeterinarian": { "Identification": { "StateLicenseNumber": "784577%%1142%%14", "MedicareNumber": "0", "MedicaidNumber": "654745", "DEANumber": "*********", "NPI": "222222", "CertificateToPrescribe": "CTP.CA.1142%%14" }, "Specialty": "363L00000X", "PracticeLocation": { "BusinessName": "Medi-Blue Rapid Clinic" }, "Name": { "LastName": "Thomas", "FirstName": "Walden", "MiddleName": "Macnair", "Suffix": "NP" }, "FormerName": { "LastName": "Macnair", "FirstName": "Walden", "MiddleName": "Robert" }, "Address": { "AddressLine1": "2165-B1 Northpoint Parkway", "City": "Santa Rosa", "StateProvince": "CA", "PostalCode": "95407", "CountryCode": "US" }, "CommunicationNumbers": { "PrimaryTelephone": { "Number": "**********" }, "ElectronicMail": "<EMAIL>", "Fax": { "Number": "**********" }, "HomeTelephone": { "Number": "**********", "SupportsSMS": "Y" } } } }, 
--            "Observation": { "Measurement": [ { "VitalSign": "8302-2", "LOINCVersion": "2.42", "Value": 51, "UnitOfMeasure": "[in_i]", "UCUMVersion": "2.1", "ObservationDate": { "Date": "2022-10-20" } }, { "VitalSign": "29463-7", "LOINCVersion": "2.42", "Value": 62, "UnitOfMeasure": "[lb_av]", "UCUMVersion": "2.1", "ObservationDate": { "Date": "2023-01-12" } } ] }, 
--            "MedicationPrescribed": { "DrugDescription": "amoxicillin 200 mg/5mL oral suspension", "DrugCoded": { "ProductCode": { "Code": "00093416076", "Qualifier": "ND" }, "DrugDBCode": { "Code": "313850", "Qualifier": "SCD" } }, "Quantity": { "Value": 52.555, "CodeListQualifier": "38", "QuantityUnitOfMeasure": { "Code": "C28254" } }, "WrittenDate": { "Date": "2022-10-01" }, "Substitutions": "0", "NumberOfRefills": 0, "Diagnosis": { "ClinicalInformationQualifier": "1", "Primary": { "Code": "H6690", "Qualifier": "ABF", "Description": "Acute otitis media, unspecified, unspecified ear" } }, "Sig": { "SigText": "Take 2.5 mL by mouth three times a day for 7 days. Discard remainder." } } 
--          } 
--        } 
--      }
--    }';
--    json_content_final TEXT;
--    message_id_value TEXT;
--    message_id_unique_suffix TEXT := gen_random_uuid()::text;
--    log_id INTEGER;
--BEGIN
--    message_id_value := '0c9c3d8e48e74196b69c69320be2fb37_' || message_id_unique_suffix;
--    json_content_final := format(json_template, message_id_value);
--    INSERT INTO form_ss_log (message_json, direction, message_type, message_id, created_by, updated_by)
--    VALUES (json_content_final::jsonb, 'IN', 'NewRx', 'Cert_NewRx-1_Test_' || message_id_unique_suffix, 1, 1)
--    RETURNING id INTO log_id;
--    RAISE NOTICE 'Inserted Cert_NewRx-1_2023v1.json into form_ss_log with ID: %', log_id;
--END $$;

-- Data from: surescripts/test_scripts/inbound_json/Cert_NEWRX-2_2019v2.json
DO $$
DECLARE
    json_base_content TEXT := '{ 
      "?xml": "", 
      "Message": { 
        "Header": { 
          "To": 111111, 
          "From": 6301875277001, 
          "MessageID": "PLACEHOLDER_MESSAGE_ID", 
          "SentTime": "2019-01-01T13:42:39.7Z", 
          "SenderSoftware": { "SenderSoftwareDeveloper": "Surescripts", "SenderSoftwareProduct": "Certification Testing", "SenderSoftwareVersionRelease": "20170715" }, 
          "TestMessage": 1, 
          "TertiaryIdentifier": "A1B", 
          "PrescriberOrderNumber": "CORE NEWRX 2 MAX-POP TEST CASE #2a", 
          "PrescriberOrderGroup": { "OrderGroupNumber": "CORE NEWRX 2 MAX-POP TESTCASE GRP", "ItemCountInOrderGroup": 2, "TotalCountForOrderGroup": 2, "OrderGroupReason": "NewRx" } 
        }, 
        "Body": { 
          "NewRx": { 
            "UrgencyIndicatorCode": "X",
            "AllergyOrAdverseEvent": {
              "Allergies": {
                "SourceOfInformation": "P",
                "EffectiveDate": {"Date": "1985-12-31"},
                "ExpirationDate": {"Date": "2018-01-01"},
                "AdverseEvent": {"Text": "This field is edited and the length is setup to now be a full 80 characters long", "Code": "419511003"},
                "DrugProductCoded": {"Code": "12345678911", "Qualifier": "ND", "Text": "This field is edited and the length is setup to now be a full 80 characters long"},
                "ReactionCoded": {"Text": "This field is edited and the length is setup to now be a full 80 characters long", "Code": "247472004"},
                "SeverityCoded": {"Text": "This field is edited and the length is setup to now be a full 80 characters long", "Code": "6736007"}
              }
            },
            "BenefitsCoordination": [
              {
                "PayerIdentification": {"PayerID": "T00000000023152", "ProcessorIdentificationNumber": "555555", "MutuallyDefined": "000021456", "IINNumber": "444444"},
                "PayerName": "This field is edited and the length is setup to be 70 characters long!",
                "CardholderID": "This field is ~35~ characters long!",
                "CardHolderName": {"LastName": "This field is ~35~ characters long!", "FirstName": "This field is ~35~ characters long!", "MiddleName": "This field is ~35~ characters long!", "Suffix": "THIS IS 10", "Prefix": "THIS IS 10"},
                "GroupID": "This field is ~35~ characters long!",
                "PayerResponsibilityCode": "PP",
                "PatientRelationship": "4",
                "PersonCode": "1",
                "GroupName": "This field is edited and the length is setup to be 70 characters long!",
                "Address": {"AddressLine1": "This field is ~40~ characters long here!", "AddressLine2": "This field is ~40~ characters long here!", "City": "This field is ~35~ characters long!", "StateProvince": "CA", "PostalCode": "544421221", "CountryCode": "US"},
                "CommunicationNumbers": {
                  "PrimaryTelephone": {"Number": "**********", "Extension": "45554745", "SupportsSMS": "Y"},
                  "Beeper": {"Number": "**********", "Extension": "32321242", "SupportsSMS": "N"},
                  "ElectronicMail": "<EMAIL>",
                  "Fax": {"Number": "**********", "Extension": "64646454", "SupportsSMS": "N"},
                  "HomeTelephone": {"Number": "**********", "Extension": "64457457", "SupportsSMS": "N"},
                  "WorkTelephone": [
                    {"Number": "**********", "Extension": "32114514", "SupportsSMS": "N"},
                    {"Number": "2123324734", "Extension": "32114521", "SupportsSMS": "N"}
                  ],
                  "OtherTelephone": [
                    {"Number": "4574547995", "Extension": "32566226", "SupportsSMS": "N"},
                    {"Number": "**********", "Extension": "32566986", "SupportsSMS": "N"}
                  ],
                  "DirectAddress": "This field is established to test the length ability for a hundred (100) character long string test!----This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test them all properly!"
                },
                "PBMMemberID": "This field is edited and the length is setup to now be a full 80 characters long",
                "ResponsibleParty": {"LastName": "This field is ~35~ characters long!", "FirstName": "This field is ~35~ characters long!", "MiddleName": "This field is ~35~ characters long!", "Suffix": "THIS IS 10", "Prefix": "THIS IS 10"}
              },
              {"PayerIdentification": {"PayerID": "WENO", "ProcessorIdentificationNumber": "555555", "IINNumber": "444444"}, "PayerName": "CASH CARD", "GroupID": "BSURE11", "PayerType": "L"},
              {"PayerIdentification": {"PayerID": "7417234", "ProcessorIdentificationNumber": "555555", "IINNumber": "444444"}, "PayerName": "Apply Patient Savings", "GroupID": "2388", "PayerType": "M"}
            ],
            "Patient": {
              "HumanPatient": {
                "Name": {"LastName": "This field is ~35~ characters long!", "FirstName": "This field is ~35~ characters long!", "MiddleName": "This field is ~35~ characters long!", "Suffix": "THIS IS 10", "Prefix": "THIS IS 10"},
                "Gender": "U",
                "DateOfBirth": {"Date": "2002-04-01"},
                "Address": {"AddressLine1": "This field is ~40~ characters long here!", "AddressLine2": "This field is ~40~ characters long here!", "City": "This field is ~35~ characters long!", "StateProvince": "CA", "PostalCode": "544421221", "CountryCode": "US"},
                "CommunicationNumbers": {
                  "PrimaryTelephone": {"Number": "**********", "Extension": "45554745", "SupportsSMS": "Y"},
                  "Beeper": {"Number": "**********", "Extension": "32321242", "SupportsSMS": "N"},
                  "ElectronicMail": "<EMAIL>",
                  "Fax": {"Number": "**********", "Extension": "64646454", "SupportsSMS": "N"},
                  "HomeTelephone": {"Number": "**********", "Extension": "64457457", "SupportsSMS": "N"},
                  "WorkTelephone": [
                    {"Number": "**********", "Extension": "32114514", "SupportsSMS": "N"},
                    {"Number": "2123324734", "Extension": "32114521", "SupportsSMS": "N"}
                  ],
                  "OtherTelephone": [
                    {"Number": "4574547995", "Extension": "32566226", "SupportsSMS": "N"},
                    {"Number": "**********", "Extension": "32566986", "SupportsSMS": "N"}
                  ],
                  "DirectAddress": "This field is established to test the length ability for a hundred (100) character long string test!----This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test them all properly!"
                }
              }
            },
            "Pharmacy": {
              "Identification": {"NCPDPID": "2455142", "StateLicenseNumber": "796597%PH12%82R", "MedicareNumber": "886112", "MedicaidNumber": "886112", "DEANumber": "*********", "NPI": "**********"},
              "BusinessName": "Medi-Blue Rapid Clinic (000)",
              "Address": {"AddressLine1": "2165-B1 Northpoint Parkway", "City": "Santa Rosa", "StateProvince": "CA", "PostalCode": "95407", "CountryCode": "US"},
              "CommunicationNumbers": {"PrimaryTelephone": {"Number": "**********"}, "Fax": {"Number": "**********"}}
            },
            "Prescriber": {
              "NonVeterinarian": {
                "Identification": {"StateLicenseNumber": "784577%1142%14", "MedicaidNumber": "654745", "UPIN": "0", "DEANumber": "*********", "HIN": "0", "NPI": "222222", "CertificateToPrescribe": "CTP.CA.1142%14"},
                "Specialty": "363L00000X",
                "PracticeLocation": {"BusinessName": "MediStar of California"},
                "Name": {"LastName": "Thomas", "FirstName": "Walden", "MiddleName": "Macnair", "Suffix": "NP"},
                "FormerName": {"LastName": "Macnair", "FirstName": "Walden", "MiddleName": "Robert"},
                "Address": {"AddressLine1": "1425 Mendocino Ave", "AddressLine2": "Suite 12-A", "City": "Santa Rosa", "StateProvince": "CA", "PostalCode": "95401", "CountryCode": "US"},
                "CommunicationNumbers": {"PrimaryTelephone": {"Number": "**********", "Extension": "4221"}, "ElectronicMail": "<EMAIL>", "Fax": {"Number": "**********"}, "HomeTelephone": {"Number": "**********", "SupportsSMS": "Y"}}
              }
            },
            "Observation": {"Measurement": [
              {"VitalSign": "8302-2", "LOINCVersion": "2.42", "Value": 59, "UnitOfMeasure": "[in_i]", "UCUMVersion": "2.1", "ObservationDate": {"Date": "2018-11-20"}},
              {"VitalSign": "29463-7", "LOINCVersion": "2.42", "Value": 120, "UnitOfMeasure": "[lb_av]", "UCUMVersion": "2.1", "ObservationDate": {"Date": "2018-11-23"}}
            ]},
            "MedicationPrescribed": {
              "DrugDescription": "Magic Mouthwash Diphenhydramine 12.5 mg/5 mL, Viscous lidocaine 2%, Maalox 1 part",
              "Quantity": {"Value": 900, "CodeListQualifier": "CF", "QuantityUnitOfMeasure": {"Code": "C28254"}},
              "DaysSupply": 30,
              "WrittenDate": {"Date": "2019-01-01"},
              "Substitutions": "0",
              "NumberOfRefills": 1,
              "Diagnosis": {"ClinicalInformationQualifier": "1", "Primary": {"Code": "K1233", "Qualifier": "ABF", "Description": "Oral mucositis (ulcerative) due to radiation"}, "Secondary": {"Code": "Z510", "Qualifier": "ABF", "Description": "Encounter for antineoplastic radiation therapy"}},
              "PriorAuthorization": "Q22759CB9475YBV2985BV2B2C43VV54",
              "Note": "Patient requested peppermint flavoring if possible. Please provide appropriate documentation to patient for how to use this product, including not swallowing solution. A child-resistant package also requested.",
              "DrugCoverageStatusCode": "UN",
              "Sig": {"SigText": "Swish and spit 15 mL orally for 1 minute every 12 hours", "CodeSystem": {"SNOMEDVersion": "20170131", "FMTVersion": "19.12e"}, "Instruction": {"DoseAdministration": {"DoseDeliveryMethod": {"Text": "Swish", "Qualifier": "SNOMED", "Code": "421805007"}, "Dosage": {"DoseQuantity": 15, "DoseUnitOfMeasure": {"Text": "mL", "Qualifier": "DoseUnitOfMeasure", "Code": "C28254"}}, "RouteOfAdministration": {"Text": "oral route", "Qualifier": "SNOMED", "Code": "26643006"}}, "TimingAndDuration": {"Interval": {"IntervalNumericValue": 12, "IntervalUnits": {"Text": "hours", "Qualifier": "SNOMED", "Code": "307467005"}}, "TimingClarifyingFreeText": "for 1 minute every 12 hours"}}},
              "RxFillIndicator": "Dispensed And Partially Dispensed",
              "DeliveryRequest": "FIRST FILL DELIVERY",
              "DeliveryLocation": "CONTACT PATIENT FOR DELIVERY",
              "FlavoringRequested": "Y",
              "CompoundInformation": {
                "FinalCompoundPharmaceuticalDosageForm": "C68996",
                "CompoundIngredientsLotNotUsed": [
                  {"CompoundIngredient": {"CompoundIngredientItemDescription": "Diphenhydramine 12.5 mg/5 mL", "Strength": {"StrengthValue": 12.5, "StrengthForm": {"Code": "C42986"}, "StrengthUnitOfMeasure": {"Code": "C91131"}}}, "Quantity": {"Value": 300, "CodeListQualifier": "38", "QuantityUnitOfMeasure": {"Code": "C28254"}}},
                  {"CompoundIngredient": {"CompoundIngredientItemDescription": "Viscous lidocaine 2%", "Strength": {"StrengthValue": 2, "StrengthForm": {"Code": "C42986"}, "StrengthUnitOfMeasure": {"Code": "C25613"}}}, "Quantity": {"Value": 300, "CodeListQualifier": "38", "QuantityUnitOfMeasure": {"Code": "C28254"}}},
                  {"CompoundIngredient": {"CompoundIngredientItemDescription": "Maalox oral suspension", "Strength": {"StrengthValue": "200/200/20", "StrengthForm": {"Code": "C68992"}, "StrengthUnitOfMeasure": {"Code": "C91131"}}}, "Quantity": {"Value": 300, "CodeListQualifier": "38", "QuantityUnitOfMeasure": {"Code": "C28254"}}}
                ]
              }
            },
            "Supervisor": {
              "NonVeterinarian": {
                "Identification": {"NPI": "**********"},
                "Name": {"LastName": "Martinson-McPherson", "FirstName": "Julianne", "MiddleName": "Annabelle", "Suffix": "PhD"},
                "Address": {"AddressLine1": "1425 Mendocino Ave", "AddressLine2": "Suite 12-A", "City": "Santa Rosa", "StateProvince": "CA", "PostalCode": "95401", "CountryCode": "US"},
                "CommunicationNumbers": {"PrimaryTelephone": {"Number": "**********", "Extension": "455142", "SupportsSMS": "N"}}
              }
            },
            "ProhibitRenewalRequest": "false"
          } 
        } 
      }
    }';
    json_content_final JSONB;
    message_id_value TEXT;
    message_id_unique_suffix TEXT := gen_random_uuid()::text;
    log_id INTEGER;
BEGIN
    message_id_value := '0c9c3d8e48e74196b69c69320be2fb37_v2_' || message_id_unique_suffix;
    -- Replace placeholder in the JSON template with the unique MessageID
    json_content_final := jsonb_set(json_base_content::jsonb, '{Message,Header,MessageID}', to_jsonb(message_id_value::text));
    
    INSERT INTO form_ss_log (message_json, direction, message_type, message_id, created_by, updated_by)
    VALUES (json_content_final, 'IN', 'NewRx', 'Cert_NEWRX-2_2019v2_Test_' || message_id_unique_suffix, 1, 1)
    RETURNING id INTO log_id;
    RAISE NOTICE 'Inserted Cert_NEWRX-2_2019v2.json into form_ss_log with ID: %', log_id;
END $$;

-- Example for an Error message (ss-error.xml equivalent)
DO $$
DECLARE
    json_template TEXT := '{ 
      "?xml": "", 
      "Message": { 
        "Header": { 
          "To": "8677465095001", 
          "From": "2455142", 
          "MessageID": "%s", 
          "RelatesToMessageID": "fd11cc6b93e8487aa2a8b3ac4fadca2b", 
          "SentTime": "2019-05-02T12:16:46.1102564Z", 
          "SenderSoftware": { "SenderSoftwareDeveloper": "Surescripts", "SenderSoftwareProduct": "ErxMessageManager", "SenderSoftwareVersionRelease": "1.8.1" }, 
          "PrescriberOrderNumber": "60ac3c294c63428188ea1ffa3d59aff6" 
        }, 
        "Body": { 
          "Error": { "Code": "602", "Description": "Test Error Description from Log" } 
        } 
      }
    }';
    json_content_final TEXT;
    message_id_value TEXT;
    message_id_unique_suffix TEXT := gen_random_uuid()::text;
    log_id INTEGER;
BEGIN
    message_id_value := '2a66f97827a8482eb8ab0d8b8320019f_' || message_id_unique_suffix;
    json_content_final := format(json_template, message_id_value);
    INSERT INTO form_ss_log (message_json, direction, message_type, message_id, created_by, updated_by)
    VALUES (json_content_final::jsonb, 'IN', 'Error', 'TestErrorMsg_' || message_id_unique_suffix, 1, 1)
    RETURNING id INTO log_id;
    RAISE NOTICE 'Inserted Test Error Message into form_ss_log with ID: %', log_id;
END $$;

-- Example for a Status message (ss-status-010.json equivalent)
DO $$
DECLARE
    json_template TEXT := '{ 
      "?xml": "", 
      "Message": { 
        "Header": { 
          "To": "111111", 
          "From": "222222", 
          "MessageID": "%s", 
          "RelatesToMessageID": "A25_test_target", 
          "SentTime": "2010-10-01T08:20:26", 
          "SenderSoftware": { "SenderSoftwareDeveloper": "SWITCH123", "SenderSoftwareProduct": "ROUTEA", "SenderSoftwareVersionRelease": "55" } 
        }, 
        "Body": { 
          "Status": { "Code": "010" } 
        } 
      }
    }';
    json_content_final TEXT;
    message_id_value TEXT;
    message_id_unique_suffix TEXT := gen_random_uuid()::text;
    log_id INTEGER;
BEGIN
    message_id_value := '66666684_test_010_' || message_id_unique_suffix;
    json_content_final := format(json_template, message_id_value);
    INSERT INTO form_ss_log (message_json, direction, message_type, message_id, created_by, updated_by)
    VALUES (json_content_final::jsonb, 'IN', 'Status', 'TestStatus010Msg_' || message_id_unique_suffix, 1, 1)
    RETURNING id INTO log_id;
    RAISE NOTICE 'Inserted Test Status 010 Message into form_ss_log with ID: %', log_id;
END $$;

-- Example for a Verify message (ss-verify.json equivalent)
DO $$
DECLARE
    json_template TEXT := '{ 
      "?xml": "", 
      "Message": { 
        "Header": { 
          "To": "8677465095001", 
          "From": "2455142", 
          "MessageID": "%s", 
          "RelatesToMessageID": "638fbf7c84e242b69e6c46657d64819c_test_target", 
          "SentTime": "2019-05-02T12:07:49.9461329Z", 
          "SenderSoftware": { "SenderSoftwareDeveloper": "Surescripts", "SenderSoftwareProduct": "ErxMessageManager", "SenderSoftwareVersionRelease": "1.8.1" }, 
          "PrescriberOrderNumber": "e68ceab0803c4c78aa828509038e38e9" 
        }, 
        "Body": { 
          "Verify": { "VerifyStatus": { "Code": "010", "Description": "Verified Test Data", "PrescriptionDeliveryMethod": "1" } } 
        } 
      }
    }';
    json_content_final TEXT;
    message_id_value TEXT;
    message_id_unique_suffix TEXT := gen_random_uuid()::text;
    log_id INTEGER;
BEGIN
    message_id_value := '3ac952304b174ca08282d81e9c287d1d_' || message_id_unique_suffix;
    json_content_final := format(json_template, message_id_value);
    INSERT INTO form_ss_log (message_json, direction, message_type, message_id, created_by, updated_by)
    VALUES (json_content_final::jsonb, 'IN', 'Verify', 'TestVerifyMsg_' || message_id_unique_suffix, 1, 1)
    RETURNING id INTO log_id;
    RAISE NOTICE 'Inserted Test Verify Message into form_ss_log with ID: %', log_id;
END $$;

-- Data from: nes/__mocks__/surescripts/inbound/new/QA-NEWRX-Max-Pop.xml
DO $$
DECLARE
    json_template TEXT := '{ 
      "?xml": "", 
      "Message": %s
    }';
    json_content_final TEXT;
    message_id_value TEXT;
    message_id_unique_suffix TEXT := gen_random_uuid()::text;
    log_id INTEGER;
BEGIN
    message_id_value := 'QA-NEWRX-Max-Pop_' || message_id_unique_suffix;
    json_content_final := format(json_template, '{
      "Header": {
        "To": 1122999,
        "From": 6301875277001,
        "MessageID": "%s",
        "SentTime": "2018-05-01T13:42:39.7Z",
        "SenderSoftware": {
          "SenderSoftwareDeveloper": "Surescripts and Maximally-Populated",
          "SenderSoftwareProduct": "Surescripts and Maximally-Populated",
          "SenderSoftwareVersionRelease": "This field is set to be fifty (50) characters long"
        },
        "PrescriberOrderNumber": "4987T6H43RKS8746RGSB3O86RS93S9879rq",
        "PrescriberOrderGroup": {
          "OrderGroupNumber": "DNSDOIUEWY937Y49879274H3SHQ98Y33XX2",
          "ItemCountInOrderGroup": 11,
          "TotalCountForOrderGroup": 12,
          "OrderGroupReason": "MultipleProductsPrescribed"
        }
      },
      "Body": {
        "NewRx": {
          "UrgencyIndicatorCode": "X",
          "AllergyOrAdverseEvent": {
            "Allergies": {
              "SourceOfInformation": "P",
              "EffectiveDate": {"Date": "1985-12-31"},
              "ExpirationDate": {"Date": "2018-01-01"},
              "AdverseEvent": {"Text": "This field is edited and the length is setup to now be a full 80 characters long", "Code": "419511003"},
              "DrugProductCoded": {"Code": "12345678911", "Qualifier": "ND", "Text": "This field is edited and the length is setup to now be a full 80 characters long"},
              "ReactionCoded": {"Text": "This field is edited and the length is setup to now be a full 80 characters long", "Code": "247472004"},
              "SeverityCoded": {"Text": "This field is edited and the length is setup to now be a full 80 characters long", "Code": "6736007"}
            }
          },
          "BenefitsCoordination": [
            {
              "PayerIdentification": {"PayerID": "T00000000023152", "ProcessorIdentificationNumber": "555555", "MutuallyDefined": "000021456", "IINNumber": "444444"},
              "PayerName": "This field is edited and the length is setup to be 70 characters long!",
              "CardholderID": "This field is ~35~ characters long!",
              "CardHolderName": {"LastName": "This field is ~35~ characters long!", "FirstName": "This field is ~35~ characters long!", "MiddleName": "This field is ~35~ characters long!", "Suffix": "THIS IS 10", "Prefix": "THIS IS 10"},
              "GroupID": "This field is ~35~ characters long!",
              "PayerResponsibilityCode": "PP",
              "PatientRelationship": "4",
              "PersonCode": "1",
              "GroupName": "This field is edited and the length is setup to be 70 characters long!",
              "Address": {"AddressLine1": "This field is ~40~ characters long here!", "AddressLine2": "This field is ~40~ characters long here!", "City": "This field is ~35~ characters long!", "StateProvince": "CA", "PostalCode": "544421221", "CountryCode": "US"},
              "CommunicationNumbers": {
                "PrimaryTelephone": {"Number": "**********", "Extension": "45554745", "SupportsSMS": "Y"},
                "Beeper": {"Number": "**********", "Extension": "32321242", "SupportsSMS": "N"},
                "ElectronicMail": "<EMAIL>",
                "Fax": {"Number": "**********", "Extension": "64646454", "SupportsSMS": "N"},
                "HomeTelephone": {"Number": "**********", "Extension": "64457457", "SupportsSMS": "N"},
                "WorkTelephone": [
                  {"Number": "**********", "Extension": "32114514", "SupportsSMS": "N"},
                  {"Number": "2123324734", "Extension": "32114521", "SupportsSMS": "N"}
                ],
                "OtherTelephone": [
                  {"Number": "4574547995", "Extension": "32566226", "SupportsSMS": "N"},
                  {"Number": "**********", "Extension": "32566986", "SupportsSMS": "N"}
                ],
                "DirectAddress": "This field is established to test the length ability for a hundred (100) character long string test!----This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test them all properly!"
              },
              "PBMMemberID": "This field is edited and the length is setup to now be a full 80 characters long",
              "ResponsibleParty": {"LastName": "This field is ~35~ characters long!", "FirstName": "This field is ~35~ characters long!", "MiddleName": "This field is ~35~ characters long!", "Suffix": "THIS IS 10", "Prefix": "THIS IS 10"}
            },
            {"PayerIdentification": {"PayerID": "WENO", "ProcessorIdentificationNumber": "555555", "IINNumber": "444444"}, "PayerName": "CASH CARD", "GroupID": "BSURE11", "PayerType": "L"},
            {"PayerIdentification": {"PayerID": "7417234", "ProcessorIdentificationNumber": "555555", "IINNumber": "444444"}, "PayerName": "Apply Patient Savings", "GroupID": "2388", "PayerType": "M"}
          ],
          "Patient": {
            "HumanPatient": {
              "Name": {"LastName": "This field is ~35~ characters long!", "FirstName": "This field is ~35~ characters long!", "MiddleName": "This field is ~35~ characters long!", "Suffix": "THIS IS 10", "Prefix": "THIS IS 10"},
              "Gender": "U",
              "DateOfBirth": {"Date": "2002-04-01"},
              "Address": {"AddressLine1": "This field is ~40~ characters long here!", "AddressLine2": "This field is ~40~ characters long here!", "City": "This field is ~35~ characters long!", "StateProvince": "CA", "PostalCode": "544421221", "CountryCode": "US"},
              "CommunicationNumbers": {
                "PrimaryTelephone": {"Number": "**********", "Extension": "45554745", "SupportsSMS": "Y"},
                "Beeper": {"Number": "**********", "Extension": "32321242", "SupportsSMS": "N"},
                "ElectronicMail": "<EMAIL>",
                "Fax": {"Number": "**********", "Extension": "64646454", "SupportsSMS": "N"},
                "HomeTelephone": {"Number": "**********", "Extension": "64457457", "SupportsSMS": "N"},
                "WorkTelephone": [
                  {"Number": "**********", "Extension": "32114514", "SupportsSMS": "N"},
                  {"Number": "2123324734", "Extension": "32114521", "SupportsSMS": "N"}
                ],
                "OtherTelephone": [
                  {"Number": "4574547995", "Extension": "32566226", "SupportsSMS": "N"},
                  {"Number": "**********", "Extension": "32566986", "SupportsSMS": "N"}
                ],
                "DirectAddress": "This field is established to test the length ability for a hundred (100) character long string test!----This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test them all properly!"
              }
            }
          },
          "Pharmacy": {
            "Identification": {"NCPDPID": "2455142", "StateLicenseNumber": "796597%PH12%82R", "MedicareNumber": "886112", "MedicaidNumber": "886112", "DEANumber": "*********", "NPI": "**********"},
            "BusinessName": "Medi-Blue Rapid Clinic (000)",
            "Address": {"AddressLine1": "2165-B1 Northpoint Parkway", "City": "Santa Rosa", "StateProvince": "CA", "PostalCode": "95407", "CountryCode": "US"},
            "CommunicationNumbers": {"PrimaryTelephone": {"Number": "**********"}, "Fax": {"Number": "**********"}}
          },
          "Prescriber": {
            "NonVeterinarian": {
              "Identification": {"StateLicenseNumber": "784577%1142%14", "MedicaidNumber": "654745", "UPIN": "0", "DEANumber": "*********", "HIN": "0", "NPI": "222222", "CertificateToPrescribe": "CTP.CA.1142%14"},
              "Specialty": "363L00000X",
              "PracticeLocation": {"BusinessName": "MediStar of California"},
              "Name": {"LastName": "Thomas", "FirstName": "Walden", "MiddleName": "Macnair", "Suffix": "NP"},
              "FormerName": {"LastName": "Macnair", "FirstName": "Walden", "MiddleName": "Robert"},
              "Address": {"AddressLine1": "1425 Mendocino Ave", "AddressLine2": "Suite 12-A", "City": "Santa Rosa", "StateProvince": "CA", "PostalCode": "95401", "CountryCode": "US"},
              "CommunicationNumbers": {"PrimaryTelephone": {"Number": "**********", "Extension": "4221"}, "ElectronicMail": "<EMAIL>", "Fax": {"Number": "**********"}, "HomeTelephone": {"Number": "**********", "SupportsSMS": "Y"}}
            }
          },
          "Observation": {"Measurement": [
            {"VitalSign": "8302-2", "LOINCVersion": "2.42", "Value": 59, "UnitOfMeasure": "[in_i]", "UCUMVersion": "2.1", "ObservationDate": {"Date": "2018-11-20"}},
            {"VitalSign": "29463-7", "LOINCVersion": "2.42", "Value": 120, "UnitOfMeasure": "[lb_av]", "UCUMVersion": "2.1", "ObservationDate": {"Date": "2018-11-23"}}
          ]},
          "MedicationPrescribed": {
            "DrugDescription": "Magic Mouthwash Diphenhydramine 12.5 mg/5 mL, Viscous lidocaine 2%, Maalox 1 part",
            "Quantity": {"Value": 900, "CodeListQualifier": "CF", "QuantityUnitOfMeasure": {"Code": "C28254"}},
            "DaysSupply": 30,
            "WrittenDate": {"Date": "2019-01-01"},
            "Substitutions": "0",
            "NumberOfRefills": 1,
            "Diagnosis": {"ClinicalInformationQualifier": "1", "Primary": {"Code": "K1233", "Qualifier": "ABF", "Description": "Oral mucositis (ulcerative) due to radiation"}, "Secondary": {"Code": "Z510", "Qualifier": "ABF", "Description": "Encounter for antineoplastic radiation therapy"}},
            "PriorAuthorization": "Q22759CB9475YBV2985BV2B2C43VV54",
            "Note": "Patient requested peppermint flavoring if possible. Please provide appropriate documentation to patient for how to use this product, including not swallowing solution. A child-resistant package also requested.",
            "DrugCoverageStatusCode": "UN",
            "Sig": {"SigText": "Swish and spit 15 mL orally for 1 minute every 12 hours", "CodeSystem": {"SNOMEDVersion": "20170131", "FMTVersion": "19.12e"}, "Instruction": {"DoseAdministration": {"DoseDeliveryMethod": {"Text": "Swish", "Qualifier": "SNOMED", "Code": "421805007"}, "Dosage": {"DoseQuantity": 15, "DoseUnitOfMeasure": {"Text": "mL", "Qualifier": "DoseUnitOfMeasure", "Code": "C28254"}}, "RouteOfAdministration": {"Text": "oral route", "Qualifier": "SNOMED", "Code": "26643006"}}, "TimingAndDuration": {"Interval": {"IntervalNumericValue": 12, "IntervalUnits": {"Text": "hours", "Qualifier": "SNOMED", "Code": "307467005"}}, "TimingClarifyingFreeText": "for 1 minute every 12 hours"}}},
            "RxFillIndicator": "Dispensed And Partially Dispensed",
            "DeliveryRequest": "FIRST FILL DELIVERY",
            "DeliveryLocation": "CONTACT PATIENT FOR DELIVERY",
            "FlavoringRequested": "Y",
            "CompoundInformation": {
              "FinalCompoundPharmaceuticalDosageForm": "C68996",
              "CompoundIngredientsLotNotUsed": [
                {"CompoundIngredient": {"CompoundIngredientItemDescription": "Diphenhydramine 12.5 mg/5 mL", "Strength": {"StrengthValue": 12.5, "StrengthForm": {"Code": "C42986"}, "StrengthUnitOfMeasure": {"Code": "C91131"}}}, "Quantity": {"Value": 300, "CodeListQualifier": "38", "QuantityUnitOfMeasure": {"Code": "C28254"}}},
                {"CompoundIngredient": {"CompoundIngredientItemDescription": "Viscous lidocaine 2%", "Strength": {"StrengthValue": 2, "StrengthForm": {"Code": "C42986"}, "StrengthUnitOfMeasure": {"Code": "C25613"}}}, "Quantity": {"Value": 300, "CodeListQualifier": "38", "QuantityUnitOfMeasure": {"Code": "C28254"}}},
                {"CompoundIngredient": {"CompoundIngredientItemDescription": "Maalox oral suspension", "Strength": {"StrengthValue": "200/200/20", "StrengthForm": {"Code": "C68992"}, "StrengthUnitOfMeasure": {"Code": "C91131"}}}, "Quantity": {"Value": 300, "CodeListQualifier": "38", "QuantityUnitOfMeasure": {"Code": "C28254"}}}
              ]
            }
          },
          "Supervisor": {
            "NonVeterinarian": {
              "Identification": {"NPI": "**********"},
              "Name": {"LastName": "Martinson-McPherson", "FirstName": "Julianne", "MiddleName": "Annabelle", "Suffix": "PhD"},
              "Address": {"AddressLine1": "1425 Mendocino Ave", "AddressLine2": "Suite 12-A", "City": "Santa Rosa", "StateProvince": "CA", "PostalCode": "95401", "CountryCode": "US"},
              "CommunicationNumbers": {"PrimaryTelephone": {"Number": "**********", "Extension": "455142", "SupportsSMS": "N"}}
            }
          },
          "ProhibitRenewalRequest": "false"
        }
      }
    }');
    INSERT INTO form_ss_log (message_json, direction, message_type, message_id, created_by, updated_by)
    VALUES (json_content_final::jsonb, 'IN', 'NewRx', 'QA-NEWRX-Max-Pop_Test_' || message_id_unique_suffix, 1, 1)
    RETURNING id INTO log_id;
    RAISE NOTICE 'Inserted QA-NEWRX-Max-Pop.xml into form_ss_log with ID: %', log_id;
END $$;
