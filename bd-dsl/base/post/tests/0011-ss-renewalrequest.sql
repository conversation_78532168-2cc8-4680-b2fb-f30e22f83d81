DO $$
DECLARE
    v_outbound_ss_message_id INTEGER;
    v_generated_payload JSONB;
    v_expected_payload JSONB;
    v_site_id_from INTEGER; -- Pharmacy sending the RenewalReq (form_site.ncpdp_id = 8654545 in XML From)
    v_site_id_to INTEGER;   -- Prescriber system receiving RenewalReq (form_site.ncpdp_id = 6997083822001 in XML To)
    v_benefit_id INTEGER;
    v_observation_id INTEGER;
    v_diagnosis_id_presc INTEGER;
    v_due_id INTEGER;

BEGIN
    -- 0. Get site IDs 
    SELECT id INTO v_site_id_from FROM form_site WHERE ncpdp_id = '6301875277001' LIMIT 1; -- <PERSON><PERSON><PERSON><PERSON> Pharmacy from Cert_ChangeREQ-5
    IF v_site_id_from IS NULL THEN RAISE EXCEPTION 'Sender Pharmacy site 1655458 not found.'; END IF;

    -- 1. Setup: Create the form_ss_message record for the outbound RxRenewalRequest.
    -- Based on surescripts/test_scripts/outbound/Cert_RenewalREQ-2_2023v1.xml
    INSERT INTO form_ss_message (
        direction,                                  -- direction
        message_type,                              -- message_type
        message_id,                                -- message_id
        send_to,                                   -- send_to
        sent_from,                                 -- sent_from
        related_message_id,                        -- related_message_id
        physician_order_id,                        -- physician_order_id
        pharmacy_rx_no,                            -- pharmacy_rx_no
        patient_last_name,                         -- patient_last_name
        patient_first_name,                        -- patient_first_name
        patient_middle_name,                       -- patient_middle_name
        patient_gender,                            -- patient_gender
        patient_dob,                               -- patient_dob
        patient_home_street_1,                     -- patient_home_street_1
        patient_home_street_2,                     -- patient_home_street_2
        patient_home_city,                         -- patient_home_city
        patient_home_state,                        -- patient_home_state
        patient_home_zip,                          -- patient_home_zip
        patient_phone,                             -- patient_phone
        prescriber_last_name,                      -- prescriber_last_name
        prescriber_first_name,                     -- prescriber_first_name
        prescriber_npi,                            -- prescriber_npi
        prescriber_state_lic,                      -- prescriber_state_lic
        prescriber_medicaid,                       -- prescriber_medicaid
        prescriber_dea,                            -- prescriber_dea
        prescriber_certificate_to_prescribe,       -- prescriber_certificate_to_prescribe
        prescriber_spec_id,                        -- prescriber_spec_id
        prescriber_loc_name,                       -- prescriber_loc_name
        prescriber_address_1,                      -- prescriber_address_1
        prescriber_address_2,                      -- prescriber_address_2
        prescriber_city,                           -- prescriber_city
        prescriber_state,                          -- prescriber_state
        prescriber_zip,                            -- prescriber_zip
        prescriber_phone,                          -- prescriber_phone
        prescriber_extension,                      -- prescriber_extension
        prescriber_fax,                            -- prescriber_fax
        description,                               -- description
        product_code,                              -- product_code
        product_code_qualifier_id,                 -- product_code_qualifier_id
        drug_db_qualifier_id,                      -- drug_db_qualifier_id
        quantity,                                  -- quantity
        quantity_uom_id,                           -- quantity_uom_id
        days_supply,                               -- days_supply
        written_date,                              -- written_date
        daw,                                       -- daw
        refills,                                   -- refills
        note,                                      -- note
        sig,                                       -- sig
        flavoring_requested,                       -- flavoring_requested
        pa_number,                                 -- pa_number
        disp_description,                          -- disp_description
        disp_product_code,                         -- disp_product_code
        disp_product_code_qualifier_id,            -- disp_product_code_qualifier_id
        disp_quantity,                             -- disp_quantity
        disp_quantity_uom_id,                      -- disp_quantity_uom_id
        disp_last_disp_date,                       -- disp_last_disp_date
        disp_daw,                                  -- disp_daw
        disp_pa_number,                            -- disp_pa_number
        disp_note,                                 -- disp_note
        disp_sig,                                  -- disp_sig
        request_refills,                           -- request_refills
        created_by,                                -- created_by
        updated_by,                                -- updated_by
        processed,                                 -- processed
        site_id                                    -- site_id
    )
    VALUES (
        'OUT',                                                                     -- direction
        'RxRenewalRequest',                                                        -- message_type
        'OUT_RENREQ2_2023v1_' || gen_random_uuid()::text,                          -- message_id
        '111111',                                                                  -- send_to
        '6301875277001',                                                           -- sent_from
        'ECHO FROM NEWRX',                                                         -- related_message_id
        '!',                                                                       -- physician_order_id
        'RxRef# CORE RxRENEWAL REQUEST TEST',                                      -- pharmacy_rx_no
        'Usumacintacoatzacoalcosniltepecvera',                                     -- patient_last_name
        'Juancarlosguadalupepaploapan',                                            -- patient_first_name
        'Franciscolisandroculiacan',                                               -- patient_middle_name
        'M',                                                                       -- patient_gender
        '2004-06-21',                                                              -- patient_dob
        '27732 West Alameda Potholeladen Street',                                  -- patient_home_street_1
        'Apt 425-B',                                                               -- patient_home_street_2
        'Rancho Cucamonga',                                                        -- patient_home_city
        'CA',                                                                      -- patient_home_state
        '917011515',                                                               -- patient_home_zip
        '**********',                                                              -- patient_phone
        'Thomas',                                                                  -- prescriber_last_name
        'Walden',                                                                  -- prescriber_first_name
        '222222',                                                                  -- prescriber_npi
        '784577%1142%14',                                                          -- prescriber_state_lic
        '654745',                                                                  -- prescriber_medicaid
        '*********',                                                               -- prescriber_dea
        'CTP.CA.1142%14',                                                          -- prescriber_certificate_to_prescribe
        '363L00000X',                                                              -- prescriber_spec_id
        'MediStar of California',                                                  -- prescriber_loc_name
        '1425 Mendocino Ave',                                                      -- prescriber_address_1
        'Suite 12-A',                                                              -- prescriber_address_2
        'Santa Rosa',                                                              -- prescriber_city
        'CA',                                                                      -- prescriber_state
        '95401',                                                                   -- prescriber_zip
        '7079442142',                                                              -- prescriber_phone
        '4221',                                                                    -- prescriber_extension
        '7079442121',                                                              -- prescriber_fax
        'fluoxetine 20 mg/5 mL (4 mg/mL) oral solution',                           -- description
        '00121072104',                                                             -- product_code
        'ND',                                                                      -- product_code_qualifier_id
        'SCD',                                                                     -- drug_db_qualifier_id
        150,                                                                       -- quantity
        'C28254',                                                                  -- quantity_uom_id
        30,                                                                        -- days_supply
        '2019-01-01',                                                              -- written_date
        '0',                                                                       -- daw
        1,                                                                         -- refills
        'Patient requested peppermint flavoring if possible.',                     -- note
        'Take 5 mL by mouth daily',                                                -- sig
        'Y',                                                                       -- flavoring_requested
        'Q22759CB9475YBV2985BV2B2C43VV54',                                        -- pa_number
        'fluoxetine 20 mg/5 mL (4 mg/mL) oral solution',                           -- disp_description
        '00121072104',                                                             -- disp_product_code
        'ND',                                                                      -- disp_product_code_qualifier_id
        150,                                                                       -- disp_quantity
        'C28254',                                                                  -- disp_quantity_uom_id
        '2019-01-01',                                                              -- disp_last_disp_date
        '0',                                                                       -- disp_daw
        'Q22759CB9475YBV2985BV2B2C43VV54',                                        -- disp_pa_number
        'Patient requested peppermint flavoring if possible.',                     -- disp_note
        'Take 5 mL by mouth daily',                                                -- disp_sig
        2,                                                                         -- request_refills
        1,                                                                         -- created_by
        1,                                                                         -- updated_by
        'No',                                                                      -- processed
        v_site_id_from                                                             -- site_id
    ) RETURNING id INTO v_outbound_ss_message_id;

    -- Insert drug_cvg_status_id for MedicationPrescribed
    INSERT INTO gr_form_ss_message_drug_cvg_status_id_to_list_ss_cvg_status_id (form_ss_message_fk, form_list_ss_cvg_status_fk)
    VALUES (v_outbound_ss_message_id, 'UN');

    -- Insert disp_drug_cvg_status_id for MedicationDispensed
    INSERT INTO gr_form_ss_message_disp_drug_cvg_ids_to_list_ss_cvg_status_id (form_ss_message_fk, form_list_ss_cvg_status_fk)
    VALUES (v_outbound_ss_message_id, 'UN');

    -- Insert BenefitsCoordination (Cert_RenewalREQ-2_2023v1.xml has 2)
    INSERT INTO form_ss_benefit (pbm_participant_id, pcn, bin, payer_name, cardholder_id, cardholder_first_name, cardholder_last_name, group_id, group_name, payer_type_id, created_by, updated_by)
    VALUES ('T00000000022649', '555555', '444444', 'PHARMACEUTICAL LITE FOR MEDICARE AND MEDICAID SERVICES (PLS) PBM/PAYER', 'HEREISACARDHOLDERIDTESTINGMAXLENGTH', 'Juancarlosguadalupepaploapan', 'Usumacintacoatzacoalcosniltepecvera', 'THISGROUPIDISATTHEMAXIMUMLENGTHOF35', 'HEREISAREALLYLONGANDOVERDONEGROUPNAMETOTESTLONG,BUTNOTMAX,SUPPORT', NULL, 1,1) RETURNING id INTO v_benefit_id;
    INSERT INTO sf_form_ss_message_to_ss_benefit(form_ss_message_fk, form_ss_benefit_fk) VALUES (v_outbound_ss_message_id, v_benefit_id);
    
    INSERT INTO form_ss_benefit (pbm_participant_id, pcn, bin, payer_name, group_id, payer_type_id, created_by, updated_by)
    VALUES ('7417234', '555555', '444444', 'Apply Patient Savings', '2388', 'M', 1,1) RETURNING id INTO v_benefit_id;
    INSERT INTO sf_form_ss_message_to_ss_benefit(form_ss_message_fk, form_ss_benefit_fk) VALUES (v_outbound_ss_message_id, v_benefit_id);

    -- Insert Observation data (Cert_RenewalREQ-2_2023v1.xml has two observations)
    INSERT INTO form_ss_observation (type_id, loinc_version, value, unit_id, ucum_version, observation_date, created_by, updated_by)
    VALUES ('8302-2', '2.42', 150, '[cm]', '2.1', '2022-10-20', 1,1) RETURNING id INTO v_observation_id;
    INSERT INTO sf_form_ss_message_to_ss_observation (form_ss_message_fk, form_ss_observation_fk) VALUES (v_outbound_ss_message_id, v_observation_id);
    
    INSERT INTO form_ss_observation (type_id, loinc_version, value, unit_id, ucum_version, observation_date, created_by, updated_by)
    VALUES ('29463-7', '2.42', 54.4, '[kg]', '2.1', '2022-10-23', 1,1) RETURNING id INTO v_observation_id;
    INSERT INTO sf_form_ss_message_to_ss_observation (form_ss_message_fk, form_ss_observation_fk) VALUES (v_outbound_ss_message_id, v_observation_id);

    -- Insert Diagnosis for MedicationPrescribed (from XML MedicationPrescribed/Diagnosis - which is the same as MedicationDispensed/Diagnosis in this XML)
    -- This set of diagnoses will be linked to both MedicationPrescribed and MedicationDispensed in the payload by the generator logic based on clinical_info_qualifier_id and disp_clinical_info_qualifier_id
    INSERT INTO form_ss_diagnosis (type, dx_code, dx_code_qualifier_id, dx_desc, created_by, updated_by)
    VALUES ('Primary', 'K1233', 'ABF', 'Oral mucositis (ulcerative) due to radiation', 1,1) RETURNING id INTO v_diagnosis_id_presc;
    INSERT INTO sf_form_ss_message_to_ss_diagnosis (form_ss_message_fk, form_ss_diagnosis_fk) VALUES (v_outbound_ss_message_id, v_diagnosis_id_presc);
    
    INSERT INTO form_ss_diagnosis (type, dx_code, dx_code_qualifier_id, dx_desc, created_by, updated_by)
    VALUES ('Secondary', 'Z510', 'ABF', 'Encounter for antineoplastic radiation therapy', 1,1) RETURNING id INTO v_diagnosis_id_presc;
    INSERT INTO sf_form_ss_message_to_ss_diagnosis (form_ss_message_fk, form_ss_diagnosis_fk) VALUES (v_outbound_ss_message_id, v_diagnosis_id_presc);

    -- Insert DUE for MedicationDispensed (from XML MedicationDispensed/DrugUseEvaluation)
    INSERT INTO form_ss_due (service_reason_id, pservice_rsn_id, clinical_significance, created_by, updated_by)
    VALUES (
        'DL', 
        '00', 
        '3', 
        1,1
    ) RETURNING id INTO v_due_id;
    -- Link DUE ServiceResultCode (XML has 00)
    INSERT INTO gr_form_ss_due_service_res_id_to_list_ss_service_result_code_id (form_ss_due_fk, form_list_ss_service_result_code_fk)
    VALUES (v_due_id, '00');
    -- Link DUE to form_ss_message (implicitly for MedicationDispensed in RxRenewalRequest)
    INSERT INTO sf_form_ss_message_to_ss_due(form_ss_message_fk, form_ss_due_fk) VALUES (v_outbound_ss_message_id, v_due_id);


    RAISE NOTICE 'Outbound RxRenewalRequest form_ss_message_id to be tested: %', v_outbound_ss_message_id;

    -- 3. Execution: Call the main payload generation function
    SELECT generate_ss_outbound_payload(v_outbound_ss_message_id) INTO v_generated_payload;

    RAISE NOTICE 'Generated Payload: %', jsonb_pretty(v_generated_payload);

    -- 4. Define Expected Payload (based on Cert_RenewalREQ-2_2023v1.xml)
    v_expected_payload := jsonb_strip_nulls(jsonb_build_object(
        'Message', jsonb_build_object(
            'Header', jsonb_build_object(
                'To', '111111',
                'From', '6301875277001',
                'MessageID', (v_generated_payload->'Message'->'Header'->>'MessageID'),
                'SentTime', (v_generated_payload->'Message'->'Header'->>'SentTime'),
                'MessageID', (SELECT message_id FROM form_ss_message WHERE id = v_outbound_ss_message_id),
                'RelatesToMessageID', 'ECHO FROM NEWRX',
                'RxReferenceNumber', 'RxRef# CORE RxRENEWAL REQUEST TEST',
                'PrescriberOrderNumber', '!'
            ),
            'Body', jsonb_build_object(
                'RxRenewalRequest', jsonb_build_object(
                    'BenefitsCoordination', jsonb_build_array(
                        jsonb_build_object(
                            'PayerIdentification', jsonb_build_object(
                                'PayerID', 'T00000000022649',
                                'ProcessorIdentificationNumber', '555555',
                                'IINNumber', '444444'
                            ),
                            'PayerName', 'PHARMACEUTICAL LITE FOR MEDICARE AND MEDICAID SERVICES (PLS) PBM/PAYER',
                            'CardholderID', 'HEREISACARDHOLDERIDTESTINGMAXLENGTH',
                            'CardHolderName', jsonb_build_object(
                                'LastName', 'Usumacintacoatzacoalcosniltepecvera',
                                'FirstName', 'Juancarlosguadalupepaploapan'
                            ),
                            'GroupID', 'THISGROUPIDISATTHEMAXIMUMLENGTHOF35',
                            'GroupName', 'HEREISAREALLYLONGANDOVERDONEGROUPNAMETOTESTLONG,BUTNOTMAX,SUPPORT'
                        ),
                        jsonb_build_object(
                            'PayerIdentification', jsonb_build_object(
                                'PayerID', '7417234',
                                'ProcessorIdentificationNumber', '555555',
                                'IINNumber', '444444'
                            ),
                            'PayerName', 'Apply Patient Savings',
                            'GroupID', '2388',
                            'PayerType', 'M'
                        )
                    ),
                    'Patient', jsonb_build_object(
                        'HumanPatient', jsonb_build_object(
                            'Name', jsonb_build_object(
                                'LastName', 'Usumacintacoatzacoalcosniltepecvera',
                                'FirstName', 'Juancarlosguadalupepaploapan',
                                'MiddleName', 'Franciscolisandroculiacan'
                            ),
                            'Gender', 'M',
                            'DateOfBirth', jsonb_build_object('Date', '20040621'), 
                            'Address', jsonb_build_object(
                                'AddressLine1', '27732 West Alameda Potholeladen Street',
                                'AddressLine2', 'Apt 425-B',
                                'City', 'Rancho Cucamonga',
                                'StateProvince', 'CA',
                                'PostalCode', '917011515'
                            ),
                            'CommunicationNumbers', jsonb_build_object(
                                'PrimaryTelephone', jsonb_build_object('Number', '**********')
                            )
                        )
                    ),
                    'Pharmacy', jsonb_build_object(
                        'Identification', jsonb_build_object(
                            'NCPDPID', '6301875277001',
                            'NPI', '**********'
                        ),
                        'BusinessName', 'Hawaii Specialty Pharmacy',
                        'Address', jsonb_build_object(
                            'AddressLine1', '1150 South King Street',
                            'AddressLine2', 'Suite 1006',
                            'City', 'Honolulu',
                            'StateProvince', 'HI',
                            'PostalCode', '96814'
                        ),
                        'CommunicationNumbers', jsonb_build_object(
                            'PrimaryTelephone', jsonb_build_object('Number', '**********'),
                            'Fax', jsonb_build_object('Number', '**********')
                        )
                    ),
                    'Prescriber', jsonb_build_object(
                        'NonVeterinarian', jsonb_build_object(
                            'Identification', jsonb_build_object(
                                'StateLicenseNumber', '784577%1142%14',
                                'MedicaidNumber', '654745',
                                'DEANumber', '*********',
                                'NPI', '222222',
                                'CertificateToPrescribe', 'CTP.CA.1142%14'
                            ),
                            'Specialty', '363L00000X',
                            'PracticeLocation', jsonb_build_object('BusinessName', 'MediStar of California'),
                            'Name', jsonb_build_object(
                                'LastName', 'Thomas',
                                'FirstName', 'Walden'
                            ),
                            'Address', jsonb_build_object(
                                'AddressLine1', '1425 Mendocino Ave',
                                'AddressLine2', 'Suite 12-A',
                                'City', 'Santa Rosa',
                                'StateProvince', 'CA',
                                'PostalCode', '95401'
                            ),
                            'CommunicationNumbers', jsonb_build_object(
                                'PrimaryTelephone', jsonb_build_object('Number', '7079442142', 'Extension', '4221'),
                                'Fax', jsonb_build_object('Number', '7079442121')
                            )
                        )
                    ),
                    'Observation', jsonb_build_object(
                        'Measurement', jsonb_build_array(
                            jsonb_build_object(
                                'VitalSign', '8302-2',
                                'LOINCVersion', '2.42',
                                'Value', 150,
                                'UnitOfMeasure', '[cm]',
                                'UCUMVersion', '2.1',
                                'ObservationDate', jsonb_build_object('Date', '20221020')
                            ),
                            jsonb_build_object(
                                'VitalSign', '29463-7',
                                'LOINCVersion', '2.42',
                                'Value', 54.4,
                                'UnitOfMeasure', '[kg]',
                                'UCUMVersion', '2.1',
                                'ObservationDate', jsonb_build_object('Date', '20221023')
                            )
                        )
                    ),
                    'MedicationDispensed', jsonb_build_object(
                        'DrugDescription', 'fluoxetine 20 mg/5 mL (4 mg/mL) oral solution',
                        'DrugCoded', jsonb_build_object(
                            'ProductCode', jsonb_build_object('Code', '00121072104', 'Qualifier', 'ND')
                        ),
                        'Quantity', jsonb_build_object(
                            'Value', 150,
                            'QuantityUnitOfMeasure', jsonb_build_object('Code', 'C28254')
                        ),
                        'LastFillDate', jsonb_build_object('Date', '20190101'), 
                        'Substitutions', '0',
                        'Diagnosis', jsonb_build_object( 
                            'Primary', jsonb_build_object(
                                'Code', 'K1233',
                                'Qualifier', 'ABF',
                                'Description', 'Oral mucositis (ulcerative) due to radiation'
                            ),
                            'Secondary', jsonb_build_object(
                                'Code', 'Z510',
                                'Qualifier', 'ABF',
                                'Description', 'Encounter for antineoplastic radiation therapy'
                            )
                        ),
                        'DrugUseEvaluation', jsonb_build_array(jsonb_build_object(
                            'ServiceReasonCode', 'DL',
                            'ProfessionalServiceCode', '00',
                            'ClinicalSignificanceCode', '3',
                            'ServiceResultCode', jsonb_build_array('00')
                        )),
                        'PriorAuthorization', 'Q22759CB9475YBV2985BV2B2C43VV54',
                        'Note', 'Patient requested peppermint flavoring if possible.',
                        'Sig', jsonb_build_object('SigText', 'Take 5 mL by mouth daily'),
                        'PharmacyRequestedRefills', 2
                    ),
                    'MedicationPrescribed', jsonb_build_object(
                        'DrugDescription', 'fluoxetine 20 mg/5 mL (4 mg/mL) oral solution',
                        'DrugCoded', jsonb_build_object(
                            'ProductCode', jsonb_build_object('Code', '00121072104', 'Qualifier', 'ND'),
                            'DrugDBCode', jsonb_build_object('Qualifier', 'SCD')
                        ),
                        'Quantity', jsonb_build_object(
                            'Value', 150,
                            'QuantityUnitOfMeasure', jsonb_build_object('Code', 'C28254')
                        ),
                        'DaysSupply', 30,
                        'WrittenDate', jsonb_build_object('Date', '20190101'), 
                        'Substitutions', '0',
                        'NumberOfRefills', 1,
                        'Diagnosis', jsonb_build_object(
                            'Primary', jsonb_build_object(
                                'Code', 'K1233',
                                'Qualifier', 'ABF',
                                'Description', 'Oral mucositis (ulcerative) due to radiation'
                            ),
                            'Secondary', jsonb_build_object(
                                'Code', 'Z510',
                                'Qualifier', 'ABF',
                                'Description', 'Encounter for antineoplastic radiation therapy'
                            )
                        ),
                        'DrugCoverageStatusCode', jsonb_build_array('UN'),
                        'DrugUseEvaluation', jsonb_build_array(jsonb_build_object(
                            'ServiceReasonCode', 'DL',
                            'ProfessionalServiceCode', '00',
                            'ClinicalSignificanceCode', '3',
                            'ServiceResultCode', jsonb_build_array('00')
                        )),
                        'PriorAuthorization', 'Q22759CB9475YBV2985BV2B2C43VV54',
                        'Note', 'Patient requested peppermint flavoring if possible.',
                        'Sig', jsonb_build_object('SigText', 'Take 5 mL by mouth daily'),
                        'FlavoringRequested', 'Y'
                    )
                )
            )
        )
    ));
    PERFORM assert_json_equals(v_expected_payload, v_generated_payload, 'Full RxRenewalRequest Payload for Cert_RenewalREQ-2_2023v1');

    RAISE NOTICE 'Test 0011-ss-renewalrequest.sql (Cert_RenewalREQ-2_2023v1 Scenario) PASSED!';

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Test 0011-ss-renewalrequest.sql FAILED: %', SQLERRM;
        RAISE; 
END $$; 