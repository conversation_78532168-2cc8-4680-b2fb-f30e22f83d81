# Medical Claims JSON Schema Mapping Documentation

## Overview
This document describes how the PostgreSQL medical claims generation functions produce JSON output that matches the OpenAPI Claims_V3.json schema.

## Key Functions

### 1. `build_mm_claim()`
- **Purpose**: Builds a medical claim record from charge lines
- **Input**: insurance_id, payer_id, patient_id, site_id, date_of_service, charge_lines[]
- **Output**: mm_claim_record (composite type)

### 2. `mm_record_to_json()`
- **Purpose**: Converts mm_claim_record to JSON format
- **Input**: mm_claim_record
- **Output**: jsonb matching internal structure

### 3. `mm_build_claim_json()`
- **Purpose**: Builds JSON from saved medical claims in database
- **Input**: med_claim_id
- **Output**: jsonb matching Claims_V3.json schema

## JSON Structure Mapping

### Top Level Fields (Claims_V3.json → Database)

| OpenAPI Field | Database Field | Source Function | Required |
|--------------|----------------|-----------------|----------|
| controlNumber | control_number | mm_build_claim_json | Yes |
| tradingPartnerServiceId | trading_partner_service_id | mm_build_claim_json | No |
| tradingPartnerName | trading_partner_name | mm_build_claim_json | No |
| usageIndicator | usage_indicator | mm_build_claim_json | No |
| submitter | (object) | mm_build_submitter | Yes |
| receiver | (object) | mm_build_receiver | Yes |
| billing | (object) | mm_build_billing_provider | Yes |
| subscriber | (object) | mm_build_subscriber | Yes |
| dependent | (object) | mm_build_dependent | No |
| claimInformation | (object) | mm_build_claim_information | Yes |

### Submitter Object

| OpenAPI Field | Database Field | Table |
|--------------|----------------|-------|
| organizationName | organization_name | form_site |
| contactInformation.name | name | form_site_contact |
| contactInformation.phoneNumber | phone_number | form_site_contact |

### Receiver Object

| OpenAPI Field | Database Field | Table |
|--------------|----------------|-------|
| organizationName | organization | form_payer |

### Billing Provider Object

| OpenAPI Field | Database Field | Table |
|--------------|----------------|-------|
| providerType | 'BillingProvider' | (static) |
| npi | npi | form_site/form_physician |
| organizationName | mm_ch_organization_name | form_site |
| address.address1 | address1 | form_site |
| address.city | city | form_site |
| address.state | state_id | form_site |
| address.postalCode | zip | form_site |

### Subscriber Object

| OpenAPI Field | Database Field | Table |
|--------------|----------------|-------|
| paymentResponsibilityLevelCode | 'P' | (static for primary) |
| firstName | firstname | form_patient |
| lastName | lastname | form_patient |
| gender | gender | form_patient |
| dateOfBirth | dob | form_patient |
| memberId | cardholder_id | form_patient_insurance |
| policyNumber | policy_number | form_patient_insurance |

### Claim Information Object

| OpenAPI Field | Database Field | Table |
|--------------|----------------|-------|
| claimFilingCode | claim_filing_code | form_med_claim_info |
| patientControlNumber | patient_control_number | form_med_claim_info |
| claimChargeAmount | claim_charge_amount | form_med_claim_info |
| placeOfServiceCode | place_of_service_code | form_med_claim_info |
| claimFrequencyCode | claim_frequency_code | form_med_claim_info |
| signatureIndicator | signature_indicator | form_med_claim_info |
| planParticipationCode | plan_participation_code | form_med_claim_info |
| benefitsAssignmentCertificationIndicator | benefits_assignment_certification_indicator | form_med_claim_info |
| releaseInformationCode | release_information_code | form_med_claim_info |
| healthCareCodeInformation | (array) | form_med_claim_dx |
| serviceLines | (array) | form_med_claim_sl |

### Service Line Object

| OpenAPI Field | Database Field | Table |
|--------------|----------------|-------|
| assignedNumber | assigned_number | form_med_claim_sl |
| serviceDate | service_date | form_med_claim_sl |
| professionalService | (object) | form_med_claim_sv |

### Professional Service Object

| OpenAPI Field | Database Field | Table |
|--------------|----------------|-------|
| procedureIdentifier | procedure_identifier | form_med_claim_sv |
| procedureCode | procedure_code | form_med_claim_sv |
| lineItemChargeAmount | line_item_charge_amount | form_med_claim_sv |
| measurementUnit | measurement_unit | form_med_claim_sv |
| serviceUnitCount | service_unit_count | form_med_claim_sv |
| compositeDiagnosisCodePointers | (object) | form_med_claim_sv_dx |

## Field Transformations

### Date Fields
- Database format: `YYYY-MM-DD` or timestamp
- JSON format: `YYYYMMDD` (no separators)
- Transformation: `mm_format_date()` function

### Amount Fields
- Database format: `numeric`
- JSON format: `string` with decimal places
- Transformation: Cast to `::text`

### Code Fields
- Database: Stored as foreign keys or codes
- JSON: Mapped to allowed enum values per schema
- Example: `claim_filing_code` must be one of: 'CI', 'BL', 'MA', etc.

## Schema Validation Points

### Required Fields Validation
The following fields are required by Claims_V3.json:

1. **Top Level**:
   - controlNumber
   - submitter
   - receiver  
   - billing
   - subscriber
   - claimInformation

2. **Claim Information**:
   - claimFilingCode
   - patientControlNumber
   - claimChargeAmount
   - placeOfServiceCode
   - claimFrequencyCode
   - signatureIndicator
   - planParticipationCode
   - benefitsAssignmentCertificationIndicator
   - releaseInformationCode
   - serviceLines (array, min 1, max 50)

3. **Service Lines**:
   - serviceDate
   - professionalService

4. **Professional Service**:
   - procedureIdentifier
   - procedureCode
   - lineItemChargeAmount
   - measurementUnit
   - serviceUnitCount
   - compositeDiagnosisCodePointers

## Test Results

The comprehensive test (`0015-test-mm-claim-generation.sql`) validates:

1. **build_mm_claim()** - Successfully builds claim records
2. **mm_record_to_json()** - Converts to JSON format
3. **mm_build_claim_json()** - Generates schema-compliant JSON

### Sample Valid JSON Output Structure

```json
{
  "controlNumber": "TEST_CONTROL_123",
  "tradingPartnerServiceId": "TESTPAYER001",
  "tradingPartnerName": "TEST_PAYER_MM",
  "usageIndicator": "P",
  "submitter": {
    "organizationName": "TEST_SITE_MM",
    "contactInformation": {
      "name": "Site Contact",
      "phoneNumber": "**********"
    }
  },
  "receiver": {
    "organizationName": "TEST_PAYER_MM"
  },
  "billing": {
    "providerType": "BillingProvider",
    "npi": "TEST1234567890",
    "organizationName": "TEST MM ORG",
    "address": {
      "address1": "123 Test St",
      "city": "Test City",
      "state": "TX",
      "postalCode": "12345"
    }
  },
  "subscriber": {
    "paymentResponsibilityLevelCode": "P",
    "firstName": "TEST",
    "lastName": "TEST_MM_CLAIM",
    "gender": "M",
    "dateOfBirth": "19800101",
    "memberId": "TEST_CARD_456",
    "policyNumber": "TEST_POLICY_123"
  },
  "claimInformation": {
    "claimFilingCode": "CI",
    "patientControlNumber": "TEST_PCN_123",
    "claimChargeAmount": "1500.00",
    "placeOfServiceCode": "11",
    "claimFrequencyCode": "1",
    "signatureIndicator": "Y",
    "planParticipationCode": "A",
    "benefitsAssignmentCertificationIndicator": "Y",
    "releaseInformationCode": "Y",
    "healthCareCodeInformation": [
      {
        "diagnosisTypeCode": "ABK",
        "diagnosisCode": "Z51.11"
      }
    ],
    "serviceLines": [
      {
        "assignedNumber": "1",
        "serviceDate": "20240602",
        "professionalService": {
          "procedureIdentifier": "HC",
          "procedureCode": "J1234",
          "lineItemChargeAmount": "1500.00",
          "measurementUnit": "UN",
          "serviceUnitCount": "100",
          "compositeDiagnosisCodePointers": {
            "diagnosisCodePointers": ["1"]
          }
        }
      }
    ]
  }
}
```

## Known Issues Resolved

1. **provider_accept_assignment_code** - This field does not exist in the database schema and is not used
2. **special_program_code** - Exists in schema and correctly mapped
3. **signature_indicator** - Exists in schema and correctly mapped to `signatureIndicator` in JSON

## Conclusion

The medical claims generation system successfully produces JSON output that matches the OpenAPI Claims_V3.json schema structure. The transformation from database records to JSON follows the required field mappings and data type conversions specified in the schema. 