-- Medical Claims Payer Settings and COB Processing Test
-- File: 0012-mm-payer-settings-cob-test.sql
-- Purpose: Test comprehensive payer settings application and COB processing for medical claims

-- Comprehensive Payer Settings Validation Function
CREATE OR REPLACE FUNCTION validate_mm_payer_settings(
  p_payer_id integer,
  p_site_id integer,
  p_insurance_id integer DEFAULT NULL
) RETURNS jsonb AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result jsonb;
  v_error_message text;
  v_params jsonb;
  v_payer_settings record;
  v_validation_errors text[] := '{}';
  v_validation_warnings text[] := '{}';
  v_missing_settings text[] := '{}';
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'payer_id', p_payer_id,
    'site_id', p_site_id,
    'insurance_id', p_insurance_id
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
      'validate_mm_payer_settings'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Validating MM payer settings for payer ID: %, site ID: %', p_payer_id, p_site_id;

    -- Get comprehensive payer and insurance settings
    SELECT * INTO v_payer_settings
    FROM get_insurance_claim_settings(p_insurance_id, p_site_id, p_payer_id);

    IF v_payer_settings IS NULL THEN
        v_validation_errors := array_append(v_validation_errors, 'Payer settings not found for payer ID: ' || p_payer_id);
    ELSE
        -- Validate required settings
        IF v_payer_settings.mm_claim_filing_indicator_code IS NULL THEN
            v_missing_settings := array_append(v_missing_settings, 'Missing Claim Filing Indicator Code');
        END IF;
        
        IF v_payer_settings.mm_default_service_place_id IS NULL THEN
            v_missing_settings := array_append(v_missing_settings, 'Missing Default Place of Service');
        END IF;
        
        -- Validate conditional settings
        IF COALESCE(v_payer_settings.mm_send_billing_prov_commercial_number, 'No') = 'Yes' 
           AND v_payer_settings.mm_billing_commercial_number IS NULL THEN
            v_validation_warnings := array_append(v_validation_warnings, 'Billing provider commercial number is enabled but not configured');
        END IF;
        
        IF COALESCE(v_payer_settings.mm_send_rendering_prov_commercial_number, 'No') = 'Yes' 
           AND v_payer_settings.mm_rendering_commercial_number IS NULL THEN
            v_validation_warnings := array_append(v_validation_warnings, 'Rendering provider commercial number is enabled but not configured');
        END IF;
        
        -- Validate rental settings
        IF COALESCE(v_payer_settings.daily_bill_rental, 'No') = 'Yes' 
           AND COALESCE(v_payer_settings.span_rental_dates, 'No') = 'Yes' THEN
            v_validation_warnings := array_append(v_validation_warnings, 'Both daily billing and rental date spanning are enabled - this may cause conflicts');
        END IF;

        -- Validate timely filing
        IF v_payer_settings.days_timely_filing IS NOT NULL 
           AND v_payer_settings.days_timely_filing::integer <= 0 THEN
            v_validation_errors := array_append(v_validation_errors, 'Days timely filing must be greater than 0');
        END IF;
        
        -- Validate max rental claims
        IF v_payer_settings.max_rental_claims IS NOT NULL 
           AND v_payer_settings.max_rental_claims::integer <= 0 THEN
            v_validation_errors := array_append(v_validation_errors, 'Max rental claims must be greater than 0');
        END IF;
    END IF;

    -- Build validation result
    v_result := jsonb_build_object(
        'payer_id', p_payer_id,
        'site_id', p_site_id,
        'insurance_id', p_insurance_id,
        'validation_status', CASE 
            WHEN array_length(v_validation_errors, 1) > 0 THEN 'ERROR'
            WHEN array_length(v_validation_warnings, 1) > 0 THEN 'WARNING'
            ELSE 'VALID'
        END,
        'validation_errors', array_to_json(v_validation_errors),
        'validation_warnings', array_to_json(v_validation_warnings),
        'missing_settings', array_to_json(v_missing_settings),
        'settings_found', (v_payer_settings IS NOT NULL),
        'validation_timestamp', EXTRACT(EPOCH FROM clock_timestamp())
    );

    RAISE LOG 'MM payer settings validation completed: %', v_result;

    -- Log success
    PERFORM log_billing_function(
      'validate_mm_payer_settings'::tracked_function,
      v_params,
      v_result,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in validate_mm_payer_settings',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'validate_mm_payer_settings',
        'payer_id', p_payer_id,
        'site_id', p_site_id,
        'insurance_id', p_insurance_id
      )
    );

    PERFORM log_billing_function(
      'validate_mm_payer_settings'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

-- Function to handle COB claim sequencing and validation
CREATE OR REPLACE FUNCTION validate_mm_cob_sequence(
  p_insurance_id integer,
  p_parent_claim_no text,
  p_current_payer_id integer
) RETURNS jsonb AS $BODY$
DECLARE
  v_start_time timestamp;
  v_result jsonb;
  v_error_message text;
  v_params jsonb;
  v_parent_claim_rec record;
  v_insurance_sequence integer;
  v_parent_sequence integer;
  v_validation_errors text[] := '{}';
  v_cob_eligible boolean := FALSE;
BEGIN
  -- Record start time
  v_start_time := clock_timestamp();

  -- Build parameters JSON for logging
  v_params := jsonb_build_object(
    'insurance_id', p_insurance_id,
    'parent_claim_no', p_parent_claim_no,
    'current_payer_id', p_current_payer_id
  );

  BEGIN
    -- Log function call
    PERFORM log_billing_function(
      'validate_mm_cob_sequence'::tracked_function,
      v_params,
      NULL::jsonb,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RAISE LOG 'Validating COB sequence for parent claim: %', p_parent_claim_no;

    -- Get parent claim information
    SELECT 
        mc.*,
        pi.sequence_no as parent_sequence
    INTO v_parent_claim_rec
    FROM form_med_claim mc
    INNER JOIN form_patient_insurance pi ON pi.payer_id = mc.payer_id 
        AND pi.patient_id = mc.patient_id
        AND pi.deleted IS NOT TRUE 
        AND pi.archived IS NOT TRUE
    WHERE mc.claim_no = p_parent_claim_no
      AND mc.deleted IS NOT TRUE 
      AND mc.archived IS NOT TRUE;

    IF v_parent_claim_rec IS NULL THEN
        v_validation_errors := array_append(v_validation_errors, 'Parent claim not found: ' || p_parent_claim_no);
    ELSE
        -- Get current insurance sequence
        SELECT sequence_no::integer INTO v_insurance_sequence
        FROM form_patient_insurance
        WHERE id = p_insurance_id
          AND deleted IS NOT TRUE 
          AND archived IS NOT TRUE;

        IF v_insurance_sequence IS NULL THEN
            v_validation_errors := array_append(v_validation_errors, 'Current insurance sequence not found');
        ELSE
            -- Validate COB sequence (current should be higher than parent)
            IF v_insurance_sequence <= v_parent_claim_rec.parent_sequence THEN
                v_validation_errors := array_append(v_validation_errors, 
                    'Invalid COB sequence: current (' || v_insurance_sequence || 
                    ') must be greater than parent (' || v_parent_claim_rec.parent_sequence || ')');
            ELSE
                v_cob_eligible := TRUE;
            END IF;
        END IF;

        -- Validate parent claim has response
        IF NOT EXISTS (
            SELECT 1 FROM form_med_claim_resp_835 r835 
            WHERE r835.claim_no = p_parent_claim_no 
            AND r835.deleted IS NOT TRUE
        ) AND NOT EXISTS (
            SELECT 1 FROM form_med_claim_resp_277 r277 
            WHERE r277.claim_no = p_parent_claim_no 
            AND r277.deleted IS NOT TRUE
        ) THEN
            v_validation_errors := array_append(v_validation_errors, 'Parent claim has no response - COB not eligible');
            v_cob_eligible := FALSE;
        END IF;
    END IF;

    -- Build validation result
    v_result := jsonb_build_object(
        'parent_claim_no', p_parent_claim_no,
        'current_payer_id', p_current_payer_id,
        'insurance_id', p_insurance_id,
        'parent_sequence', v_parent_claim_rec.parent_sequence,
        'current_sequence', v_insurance_sequence,
        'cob_eligible', v_cob_eligible,
        'validation_status', CASE 
            WHEN array_length(v_validation_errors, 1) > 0 THEN 'ERROR'
            ELSE 'VALID'
        END,
        'validation_errors', array_to_json(v_validation_errors),
        'validation_timestamp', EXTRACT(EPOCH FROM clock_timestamp())
    );

    RAISE LOG 'COB sequence validation completed: %', v_result;

    -- Log success
    PERFORM log_billing_function(
      'validate_mm_cob_sequence'::tracked_function,
      v_params,
      v_result,
      NULL::text,
      clock_timestamp() - v_start_time
    );

    RETURN v_result;

  EXCEPTION WHEN OTHERS THEN
    v_error_message := SQLERRM;

    INSERT INTO billing_error_log (
      error_message,
      error_context,
      error_type,
      schema_name,
      table_name,
      additional_details
    ) VALUES (
      v_error_message,
      'Exception in validate_mm_cob_sequence',
      'FUNCTION',
      current_schema(),
      'med_claim',
      jsonb_build_object(
        'function_name', 'validate_mm_cob_sequence',
        'insurance_id', p_insurance_id,
        'parent_claim_no', p_parent_claim_no,
        'current_payer_id', p_current_payer_id
      )
    );

    PERFORM log_billing_function(
      'validate_mm_cob_sequence'::tracked_function,
      v_params,
      NULL::jsonb,
      v_error_message::text,
      clock_timestamp() - v_start_time
    );
    RAISE;
  END;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

-- Comprehensive Test Function for Payer Settings and COB Processing
CREATE OR REPLACE FUNCTION test_mm_payer_settings_and_cob() RETURNS text AS $BODY$
DECLARE
  v_test_results text := '';
  v_test_insurance_id integer;
  v_test_payer_id integer;
  v_test_patient_id integer;
  v_test_site_id integer;
  v_test_charge_lines charge_line_with_split[];
  v_payer_settings_result jsonb;
  v_cob_result jsonb;
  v_parent_claim_no text := 'TEST_PARENT_CLAIM_001';
  v_test_count integer := 0;
  v_pass_count integer := 0;
BEGIN
  v_test_results := 'MM Payer Settings and COB Processing Test Results:' || E'\n';
  v_test_results := v_test_results || '================================================' || E'\n';

  -- Test 1: Basic Payer Settings Application
  BEGIN
    v_test_count := v_test_count + 1;
    
    -- Get test data
    SELECT id INTO v_test_payer_id FROM form_payer WHERE billing_method_id = 'mm' AND deleted IS NOT TRUE LIMIT 1;
    SELECT id INTO v_test_insurance_id FROM form_patient_insurance WHERE payer_id = v_test_payer_id AND deleted IS NOT TRUE LIMIT 1;
    SELECT patient_id INTO v_test_patient_id FROM form_patient_insurance WHERE id = v_test_insurance_id;
    SELECT id INTO v_test_site_id FROM form_site WHERE deleted IS NOT TRUE LIMIT 1;
    
    IF v_test_payer_id IS NULL OR v_test_insurance_id IS NULL OR v_test_patient_id IS NULL OR v_test_site_id IS NULL THEN
      v_test_results := v_test_results || 'Test 1 SKIPPED: Missing test data' || E'\n';
    ELSE
      -- Create test charge lines
      v_test_charge_lines := ARRAY[(
        1, -- charge_no
        v_test_patient_id, -- patient_id
        v_test_site_id, -- site_id
        v_test_payer_id, -- payer_id
        1, -- inventory_id
        100.00, -- expected
        0.00, -- paid
        1.0, -- charge_quantity
        1.0, -- bill_quantity
        CURRENT_DATE, -- service_date
        CURRENT_DATE + INTERVAL '1 day', -- service_date_end
        NULL, -- rx_no
        NULL, -- rental_id
        NULL, -- ticket_no
        NULL, -- modifier_1
        NULL, -- modifier_2
        NULL, -- modifier_3
        NULL -- modifier_4
      )::charge_line_with_split];

      -- Test payer settings application
      v_payer_settings_result := apply_mm_payer_settings(
        v_test_payer_id,
        NULL, -- no previous payer
        v_test_insurance_id,
        v_test_charge_lines
      );

      IF v_payer_settings_result IS NOT NULL THEN
        v_pass_count := v_pass_count + 1;
        v_test_results := v_test_results || 'Test 1 PASSED: Payer settings applied successfully' || E'\n';
        v_test_results := v_test_results || '  Settings: ' || v_payer_settings_result::text || E'\n';
      ELSE
        v_test_results := v_test_results || 'Test 1 FAILED: Payer settings returned NULL' || E'\n';
      END IF;
    END IF;
  EXCEPTION WHEN OTHERS THEN
    v_test_results := v_test_results || 'Test 1 FAILED: ' || SQLERRM || E'\n';
  END;

  -- Test 2: Hold Claims for Future Date of Service End
  BEGIN
    v_test_count := v_test_count + 1;
    
    IF v_test_payer_id IS NOT NULL THEN
      -- Update payer to hold claims for future DOS end
      UPDATE form_payer 
      SET mm_hold_claims_dos_end = 'Yes' 
      WHERE id = v_test_payer_id;

      -- Create charge lines with future service date end
      v_test_charge_lines := ARRAY[(
        1, -- charge_no
        v_test_patient_id, -- patient_id
        v_test_site_id, -- site_id
        v_test_payer_id, -- payer_id
        1, -- inventory_id
        100.00, -- expected
        0.00, -- paid
        1.0, -- charge_quantity
        1.0, -- bill_quantity
        CURRENT_DATE, -- service_date
        CURRENT_DATE + INTERVAL '30 days', -- service_date_end (future)
        NULL, -- rx_no
        NULL, -- rental_id
        NULL, -- ticket_no
        NULL, -- modifier_1
        NULL, -- modifier_2
        NULL, -- modifier_3
        NULL -- modifier_4
      )::charge_line_with_split];

      v_payer_settings_result := apply_mm_payer_settings(
        v_test_payer_id,
        NULL,
        v_test_insurance_id,
        v_test_charge_lines
      );

      IF (v_payer_settings_result->>'substatus_id') = '114' THEN
        v_pass_count := v_pass_count + 1;
        v_test_results := v_test_results || 'Test 2 PASSED: Hold claims setting works correctly' || E'\n';
      ELSE
        v_test_results := v_test_results || 'Test 2 FAILED: Hold claims setting not applied' || E'\n';
      END IF;

      -- Reset payer setting
      UPDATE form_payer 
      SET mm_hold_claims_dos_end = NULL 
      WHERE id = v_test_payer_id;
    ELSE
      v_test_results := v_test_results || 'Test 2 SKIPPED: No test payer available' || E'\n';
    END IF;
  EXCEPTION WHEN OTHERS THEN
    v_test_results := v_test_results || 'Test 2 FAILED: ' || SQLERRM || E'\n';
  END;

  -- Test 3: COB Processing with Mock Parent Claim
  BEGIN
    v_test_count := v_test_count + 1;
    
    IF v_test_payer_id IS NOT NULL AND v_test_insurance_id IS NOT NULL THEN
      -- Create a mock parent claim for testing
      INSERT INTO form_med_claim (
        claim_no,
        patient_id,
        insurance_id,
        payer_id,
        billed,
        status,
        created_on,
        deleted,
        archived
      ) VALUES (
        v_parent_claim_no,
        v_test_patient_id,
        v_test_insurance_id,
        v_test_payer_id,
        100.00,
        'Paid',
        NOW(),
        FALSE,
        FALSE
      );

      -- Create a mock response log
      INSERT INTO form_med_claim_resp_log (
        claim_no,
        response_id,
        created_on,
        deleted,
        archived
      ) VALUES (
        v_parent_claim_no,
        'TEST_RESPONSE_835_001',
        NOW(),
        FALSE,
        FALSE
      );

      -- Create a mock 835 response
      INSERT INTO form_med_claim_resp_835 (
        response_id,
        claim_payment_amount,
        total_charge_amount,
        total_pt_pay,
        check_issue_or_eft_effective_date,
        created_on,
        deleted,
        archived
      ) VALUES (
        'TEST_RESPONSE_835_001',
        80.00,
        100.00,
        20.00,
        TO_CHAR(CURRENT_DATE, 'MM/DD/YYYY'),
        NOW(),
        FALSE,
        FALSE
      );

      -- Test COB processing
      v_cob_result := build_mm_enhanced_cob_processing(
        v_test_insurance_id,
        v_parent_claim_no,
        v_test_payer_id
      );

      IF v_cob_result IS NOT NULL AND (v_cob_result->>'parent_response_type') = '835' THEN
        v_pass_count := v_pass_count + 1;
        v_test_results := v_test_results || 'Test 3 PASSED: COB processing works correctly' || E'\n';
        v_test_results := v_test_results || '  COB Result: ' || v_cob_result::text || E'\n';
      ELSE
        v_test_results := v_test_results || 'Test 3 FAILED: COB processing failed or returned incorrect data' || E'\n';
      END IF;

      -- Clean up test data
      DELETE FROM form_med_claim_resp_835 WHERE response_id = 'TEST_RESPONSE_835_001';
      DELETE FROM form_med_claim_resp_log WHERE response_id = 'TEST_RESPONSE_835_001';
      DELETE FROM form_med_claim WHERE claim_no = v_parent_claim_no;
    ELSE
      v_test_results := v_test_results || 'Test 3 SKIPPED: Missing test data' || E'\n';
    END IF;
  EXCEPTION WHEN OTHERS THEN
    v_test_results := v_test_results || 'Test 3 FAILED: ' || SQLERRM || E'\n';
    -- Clean up on error
    BEGIN
      DELETE FROM form_med_claim_resp_835 WHERE response_id = 'TEST_RESPONSE_835_001';
      DELETE FROM form_med_claim_resp_log WHERE response_id = 'TEST_RESPONSE_835_001';
      DELETE FROM form_med_claim WHERE claim_no = v_parent_claim_no;
    EXCEPTION WHEN OTHERS THEN
      NULL; -- Ignore cleanup errors
    END;
  END;

  -- Test 4: DME Rental Date Spanning
  BEGIN
    v_test_count := v_test_count + 1;
    
    IF v_test_payer_id IS NOT NULL THEN
      -- Update payer to span rental dates
      UPDATE form_payer 
      SET span_rental_dates = 'Yes' 
      WHERE id = v_test_payer_id;

      -- Test with service lines JSON
      DECLARE
        v_test_service_lines jsonb := '[{"service_date": "2025-01-31"}]'::jsonb;
        v_updated_service_lines jsonb;
      BEGIN
        v_payer_settings_result := apply_mm_payer_settings(
          v_test_payer_id,
          NULL,
          v_test_insurance_id,
          NULL, -- no charge lines needed for this test
          NULL, -- claim info
          v_test_service_lines -- service lines
        );

        -- Check if service date end was calculated correctly
        IF v_test_service_lines->0->>'service_date_end' IS NOT NULL THEN
          v_pass_count := v_pass_count + 1;
          v_test_results := v_test_results || 'Test 4 PASSED: DME rental date spanning works' || E'\n';
          v_test_results := v_test_results || '  Updated service lines: ' || v_test_service_lines::text || E'\n';
        ELSE
          v_test_results := v_test_results || 'Test 4 FAILED: DME rental date spanning not applied' || E'\n';
        END IF;
      END;

      -- Reset payer setting
      UPDATE form_payer 
      SET span_rental_dates = NULL 
      WHERE id = v_test_payer_id;
    ELSE
      v_test_results := v_test_results || 'Test 4 SKIPPED: No test payer available' || E'\n';
    END IF;
  EXCEPTION WHEN OTHERS THEN
    v_test_results := v_test_results || 'Test 4 FAILED: ' || SQLERRM || E'\n';
  END;

  -- Test 5: Insurance Settings Function Integration
  BEGIN
    v_test_count := v_test_count + 1;
    
    IF v_test_insurance_id IS NOT NULL AND v_test_site_id IS NOT NULL THEN
      DECLARE
        v_insurance_settings record;
      BEGIN
        -- Test the enhanced insurance settings function
        SELECT * INTO v_insurance_settings
        FROM get_insurance_claim_settings(v_test_insurance_id, v_test_site_id, NULL);

        IF v_insurance_settings.insurance_id IS NOT NULL THEN
          v_pass_count := v_pass_count + 1;
          v_test_results := v_test_results || 'Test 5 PASSED: Insurance settings function works' || E'\n';
          v_test_results := v_test_results || '  Insurance ID: ' || v_insurance_settings.insurance_id || E'\n';
          v_test_results := v_test_results || '  Payer ID: ' || v_insurance_settings.payer_id || E'\n';
          v_test_results := v_test_results || '  MM Claim Filing Code: ' || COALESCE(v_insurance_settings.mm_claim_filing_indicator_code, 'NULL') || E'\n';
          v_test_results := v_test_results || '  MM Default Service Place: ' || COALESCE(v_insurance_settings.mm_default_service_place_id, 'NULL') || E'\n';
        ELSE
          v_test_results := v_test_results || 'Test 5 FAILED: Insurance settings function returned no data' || E'\n';
        END IF;
      END;
    ELSE
      v_test_results := v_test_results || 'Test 5 SKIPPED: Missing test data' || E'\n';
    END IF;
  EXCEPTION WHEN OTHERS THEN
    v_test_results := v_test_results || 'Test 5 FAILED: ' || SQLERRM || E'\n';
  END;

  -- Test 6: Contract Information Processing
  BEGIN
    v_test_count := v_test_count + 1;
    
    IF v_test_payer_id IS NOT NULL THEN
      -- Create test contract data
      INSERT INTO form_payer_contract (
        payer_id,
        contract_type_code,
        contract_code,
        contract_version_identifier,
        contract_percentage,
        terms_discount_percentage,
        created_on,
        deleted,
        archived
      ) VALUES (
        v_test_payer_id,
        '01',
        'TEST_CONTRACT_001',
        '1.0',
        85.00,
        5.00,
        NOW(),
        FALSE,
        FALSE
      );

      v_payer_settings_result := apply_mm_payer_settings(
        v_test_payer_id,
        NULL,
        v_test_insurance_id,
        NULL
      );

      IF (v_payer_settings_result->>'contract_type_code') = '01' THEN
        v_pass_count := v_pass_count + 1;
        v_test_results := v_test_results || 'Test 6 PASSED: Contract information processed correctly' || E'\n';
        v_test_results := v_test_results || '  Contract Code: ' || (v_payer_settings_result->>'contract_code') || E'\n';
        v_test_results := v_test_results || '  Contract Percentage: ' || (v_payer_settings_result->>'contract_percentage') || E'\n';
      ELSE
        v_test_results := v_test_results || 'Test 6 FAILED: Contract information not processed' || E'\n';
      END IF;

      -- Clean up test contract data
      DELETE FROM form_payer_contract WHERE payer_id = v_test_payer_id AND contract_code = 'TEST_CONTRACT_001';
    ELSE
      v_test_results := v_test_results || 'Test 6 SKIPPED: No test payer available' || E'\n';
    END IF;
  EXCEPTION WHEN OTHERS THEN
    v_test_results := v_test_results || 'Test 6 FAILED: ' || SQLERRM || E'\n';
  END;

  -- Test 7: Commercial Number Settings
  BEGIN
    v_test_count := v_test_count + 1;
    
    IF v_test_payer_id IS NOT NULL THEN
      -- Update payer with commercial number settings
      UPDATE form_payer 
      SET 
        mm_send_billing_prov_commercial_number = 'Yes',
        mm_billing_commercial_number = 'TEST_BILLING_123',
        mm_send_rendering_prov_commercial_number = 'Yes',
        mm_rendering_commercial_number = 'TEST_RENDERING_456'
      WHERE id = v_test_payer_id;

      v_payer_settings_result := apply_mm_payer_settings(
        v_test_payer_id,
        NULL,
        v_test_insurance_id,
        NULL
      );

      IF (v_payer_settings_result->>'mm_billing_commercial_number') = 'TEST_BILLING_123' AND
         (v_payer_settings_result->>'mm_rendering_commercial_number') = 'TEST_RENDERING_456' THEN
        v_pass_count := v_pass_count + 1;
        v_test_results := v_test_results || 'Test 7 PASSED: Commercial number settings applied' || E'\n';
      ELSE
        v_test_results := v_test_results || 'Test 7 FAILED: Commercial number settings not applied' || E'\n';
      END IF;

      -- Reset payer settings
      UPDATE form_payer 
      SET 
        mm_send_billing_prov_commercial_number = NULL,
        mm_billing_commercial_number = NULL,
        mm_send_rendering_prov_commercial_number = NULL,
        mm_rendering_commercial_number = NULL
      WHERE id = v_test_payer_id;
    ELSE
      v_test_results := v_test_results || 'Test 7 SKIPPED: No test payer available' || E'\n';
    END IF;
  EXCEPTION WHEN OTHERS THEN
    v_test_results := v_test_results || 'Test 7 FAILED: ' || SQLERRM || E'\n';
  END;

  -- Test 8: Payer Settings Validation
  BEGIN
    v_test_count := v_test_count + 1;
    
    IF v_test_payer_id IS NOT NULL AND v_test_site_id IS NOT NULL THEN
      DECLARE
        v_validation_result jsonb;
      BEGIN
        v_validation_result := validate_mm_payer_settings(
          v_test_payer_id,
          v_test_site_id,
          v_test_insurance_id
        );

        IF v_validation_result IS NOT NULL AND 
           (v_validation_result->>'validation_status') IN ('VALID', 'WARNING') THEN
          v_pass_count := v_pass_count + 1;
          v_test_results := v_test_results || 'Test 8 PASSED: Payer settings validation works - Status: ' || 
                           (v_validation_result->>'validation_status') || E'\n';
          
          -- Show any warnings
          IF (v_validation_result->>'validation_status') = 'WARNING' THEN
            v_test_results := v_test_results || '  Warnings: ' || 
                             (v_validation_result->>'validation_warnings') || E'\n';
          END IF;
        ELSE
          v_test_results := v_test_results || 'Test 8 FAILED: Payer settings validation failed - Status: ' || 
                           COALESCE(v_validation_result->>'validation_status', 'NULL') || E'\n';
          v_test_results := v_test_results || '  Errors: ' || 
                           COALESCE(v_validation_result->>'validation_errors', 'NULL') || E'\n';
        END IF;
      END;
    ELSE
      v_test_results := v_test_results || 'Test 8 SKIPPED: Missing test data' || E'\n';
    END IF;
  EXCEPTION WHEN OTHERS THEN
    v_test_results := v_test_results || 'Test 8 FAILED: ' || SQLERRM || E'\n';
  END;

  -- Test 9: COB Sequence Validation
  BEGIN
    v_test_count := v_test_count + 1;
    
    IF v_test_insurance_id IS NOT NULL AND v_test_payer_id IS NOT NULL THEN
      DECLARE
        v_cob_validation_result jsonb;
        v_secondary_insurance_id integer;
        v_secondary_payer_id integer;
      BEGIN
        -- Create a secondary insurance for COB testing
        SELECT id INTO v_secondary_payer_id 
        FROM form_payer 
        WHERE billing_method_id = 'mm' 
          AND id != v_test_payer_id 
          AND deleted IS NOT TRUE 
        LIMIT 1;
        
        IF v_secondary_payer_id IS NOT NULL THEN
          INSERT INTO form_patient_insurance (
            patient_id,
            payer_id,
            sequence_no,
            created_on,
            deleted,
            archived
          ) VALUES (
            v_test_patient_id,
            v_secondary_payer_id,
            2, -- Secondary insurance
            NOW(),
            FALSE,
            FALSE
          ) RETURNING id INTO v_secondary_insurance_id;

          -- Test COB sequence validation
          v_cob_validation_result := validate_mm_cob_sequence(
            v_secondary_insurance_id,
            v_parent_claim_no,
            v_secondary_payer_id
          );

          IF v_cob_validation_result IS NOT NULL THEN
            v_pass_count := v_pass_count + 1;
            v_test_results := v_test_results || 'Test 9 PASSED: COB sequence validation works - Status: ' || 
                             (v_cob_validation_result->>'validation_status') || E'\n';
          ELSE
            v_test_results := v_test_results || 'Test 9 FAILED: COB sequence validation returned NULL' || E'\n';
          END IF;

          -- Clean up secondary insurance
          DELETE FROM form_patient_insurance WHERE id = v_secondary_insurance_id;
        ELSE
          v_test_results := v_test_results || 'Test 9 SKIPPED: No secondary payer available' || E'\n';
        END IF;
      END;
    ELSE
      v_test_results := v_test_results || 'Test 9 SKIPPED: Missing test data' || E'\n';
    END IF;
  EXCEPTION WHEN OTHERS THEN
    v_test_results := v_test_results || 'Test 9 FAILED: ' || SQLERRM || E'\n';
  END;

  -- Summary
  v_test_results := v_test_results || E'\n' || '================================================' || E'\n';
  v_test_results := v_test_results || 'Test Summary: ' || v_pass_count || '/' || v_test_count || ' tests passed' || E'\n';
  
  IF v_pass_count = v_test_count THEN
    v_test_results := v_test_results || 'ALL TESTS PASSED! ✓' || E'\n';
  ELSE
    v_test_results := v_test_results || 'SOME TESTS FAILED! ✗' || E'\n';
  END IF;

  RETURN v_test_results;
END;
$BODY$ LANGUAGE plpgsql VOLATILE;

-- Quick Test Function for Basic Functionality
CREATE OR REPLACE FUNCTION test_mm_payer_settings_quick() RETURNS text AS $BODY$
DECLARE
  v_result text := '';
  v_test_payer_id integer;
  v_test_insurance_id integer;
  v_settings_result jsonb;
BEGIN
  -- Get test data
  SELECT id INTO v_test_payer_id FROM form_payer WHERE billing_method_id = 'mm' AND deleted IS NOT TRUE LIMIT 1;
  SELECT id INTO v_test_insurance_id FROM form_patient_insurance WHERE payer_id = v_test_payer_id AND deleted IS NOT TRUE LIMIT 1;
  
  IF v_test_payer_id IS NULL OR v_test_insurance_id IS NULL THEN
    RETURN 'SKIPPED: No test data available for MM payer settings test';
  END IF;

  -- Test basic payer settings
  v_settings_result := apply_mm_payer_settings(v_test_payer_id, NULL, v_test_insurance_id, NULL);
  
  IF v_settings_result IS NOT NULL THEN
    v_result := 'PASSED: MM payer settings function works correctly' || E'\n';
    v_result := v_result || 'Claim Filing Code: ' || COALESCE(v_settings_result->>'claim_filing_indicator_code', 'NULL') || E'\n';
    v_result := v_result || 'Service Place Code: ' || COALESCE(v_settings_result->>'default_service_place_id', 'NULL') || E'\n';
  ELSE
    v_result := 'FAILED: MM payer settings function returned NULL';
  END IF;

  RETURN v_result;
END;
$BODY$ LANGUAGE plpgsql VOLATILE; 