-- Test SQL for DME Rental Billing Setup
-- This creates test data to verify DME rental billing functionality

BEGIN;

-- Create test patient
INSERT INTO form_patient (
    id, mrn, firstname, lastname, status_id, 
    created_by, updated_by, created_on, updated_on
) VALUES (
    9999, 'TEST-DME-001', 'John', 'Doe', '1',
    1, 1, NOW(), NOW()
) ON CONFLICT (id) DO UPDATE SET
    mrn = EXCLUDED.mrn,
    firstname = EXCLUDED.firstname,
    lastname = EXCLUDED.lastname,
    status_id = EXCLUDED.status_id,
    updated_by = EXCLUDED.updated_by,
    updated_on = EXCLUDED.updated_on;

-- Create test site
INSERT INTO form_site (
    id, name, created_by, updated_by, created_on, updated_on
) VALUES (
    999, 'Test DME Site', 1, 1, NOW(), NOW()
) ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    updated_by = EXCLUDED.updated_by,
    updated_on = EXCLUDED.updated_on;

-- Create test payer
INSERT INTO form_payer (
    id, organization, billing_method_id, type_id, cover_dme,
    created_by, updated_by, created_on, updated_on
) VALUES (
    999, 'Test DME Insurance', 'cms1500', 'COMM', 'Yes',
    1, 1, NOW(), NOW()
) ON CONFLICT (id) DO UPDATE SET
    organization = EXCLUDED.organization,
    billing_method_id = EXCLUDED.billing_method_id,
    type_id = EXCLUDED.type_id,
    cover_dme = EXCLUDED.cover_dme,
    updated_by = EXCLUDED.updated_by,
    updated_on = EXCLUDED.updated_on;

-- Create test patient insurance
INSERT INTO form_patient_insurance (
    id, patient_id, payer_id, active, billing_method_id,
    created_by, updated_by, created_on, updated_on
) VALUES (
    9999, 9999, 999, 'Yes', 'cms1500',
    1, 1, NOW(), NOW()
) ON CONFLICT (id) DO UPDATE SET
    patient_id = EXCLUDED.patient_id,
    payer_id = EXCLUDED.payer_id,
    active = EXCLUDED.active,
    billing_method_id = EXCLUDED.billing_method_id,
    updated_by = EXCLUDED.updated_by,
    updated_on = EXCLUDED.updated_on;

-- Create test DME equipment inventory
INSERT INTO form_inventory (
    id, name, type, billable_code_id, active,
    created_by, updated_by, created_on, updated_on
) VALUES (
    9999, 'Test CPAP Machine', 'Equipment Rental', 'E0601',
    'Yes', 1, 1, NOW(), NOW()
) ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    type = EXCLUDED.type,
    billable_code_id = EXCLUDED.billable_code_id,
    active = EXCLUDED.active,
    updated_by = EXCLUDED.updated_by,
    updated_on = EXCLUDED.updated_on;

-- Create test billable inventory for DME
INSERT INTO form_inventory (
    id, name, type, hcpc_code, active,
    created_by, updated_by, created_on, updated_on
) VALUES (
    9998, 'CPAP Rental Billing', 'Billable', 'E0601',
    'Yes', 1, 1, NOW(), NOW()
) ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    type = EXCLUDED.type,
    hcpc_code = EXCLUDED.hcpc_code,
    active = EXCLUDED.active,
    updated_by = EXCLUDED.updated_by,
    updated_on = EXCLUDED.updated_on;

-- Create test rental log entry
INSERT INTO form_inventory_rental_log (
    id, patient_id, inventory_id, serial_no, site_id,
    checked_out_date, ticket_no, ticket_item_no,
    created_by, updated_by, created_on, updated_on
) VALUES (
    9999, 9999, 9999, 'TEST-CPAP-001', 999,
    CURRENT_DATE - INTERVAL '5 days', 'TEST-TICKET-001', 'TEST-ITEM-001',
    1, 1, NOW(), NOW()
) ON CONFLICT (id) DO UPDATE SET
    patient_id = EXCLUDED.patient_id,
    inventory_id = EXCLUDED.inventory_id,
    serial_no = EXCLUDED.serial_no,
    site_id = EXCLUDED.site_id,
    checked_out_date = EXCLUDED.checked_out_date,
    ticket_no = EXCLUDED.ticket_no,
    ticket_item_no = EXCLUDED.ticket_item_no,
    updated_by = EXCLUDED.updated_by,
    updated_on = EXCLUDED.updated_on;

-- Create rental activity log entry
INSERT INTO form_ledger_rental_log_activity (
    id, localized_datetime, user_id, inventory_id, serial_no,
    patient_id, previous_patient_id, site_id, description,
    created_by, updated_by, created_on, updated_on
) VALUES (
    9999, CURRENT_DATE - INTERVAL '5 days', 1, 9999, 'TEST-CPAP-001',
    9999, NULL, 999, 'Equipment checked out to patient',
    1, 1, NOW(), NOW()
) ON CONFLICT (id) DO UPDATE SET
    localized_datetime = EXCLUDED.localized_datetime,
    user_id = EXCLUDED.user_id,
    inventory_id = EXCLUDED.inventory_id,
    serial_no = EXCLUDED.serial_no,
    patient_id = EXCLUDED.patient_id,
    previous_patient_id = EXCLUDED.previous_patient_id,
    site_id = EXCLUDED.site_id,
    description = EXCLUDED.description,
    updated_by = EXCLUDED.updated_by,
    updated_on = EXCLUDED.updated_on;

COMMIT;

-- Test the views
SELECT 'Active DME Rentals View Test' as test_name;
SELECT 
    rental_log_id,
    patient_id,
    inventory_id,
    billable_inventory_id,
    serial_no,
    billing_exclusion_reason,
    next_billing_date
FROM vw_active_dme_rentals 
WHERE patient_id = 9999;

SELECT 'DME Rentals Due for Billing View Test' as test_name;
SELECT 
    rental_log_id,
    patient_name,
    equipment_name,
    due_for_billing,
    service_from_date,
    service_to_date
FROM vw_dme_rentals_due_for_billing 
WHERE patient_id = 9999;

-- Test the charge line creation function
SELECT 'DME Rental Charge Line Creation Test' as test_name;
SELECT create_dme_rental_charge_line(
    9999,
    CURRENT_DATE,
    (CURRENT_DATE + INTERVAL '29 days')::date
) as charge_line_result; 