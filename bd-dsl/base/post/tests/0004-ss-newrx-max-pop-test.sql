-- 0001-ss-max-pop-test.sql
-- Comprehensive test for maximally populated NewRx message (QA-NEWRX-Max-Pop.json)
-- Asserts all main and subform fields are parsed and inserted correctly

DO $$
DECLARE
    message_json TEXT := '
    {"json_data": {"?xml": "","Message": {"Header": {"To": 111111,"From": 6301875277001,"MessageID": "0c9c3d8e48e74196b69c69320be2fb37","SentTime": "2018-05-01T13:42:39.7Z","SenderSoftware": {"SenderSoftwareDeveloper": "Surescripts and Maximally-Populated","SenderSoftwareProduct": "Surescripts and Maximally-Populated","SenderSoftwareVersionRelease": "This field is set to be fifty (50) characters long"},"PrescriberOrderNumber": "4987T6H43RKS8746RGSB3O86RS93S9879rq","PrescriberOrderGroup": {"OrderGroupNumber": "DNSDOIUEWY937Y49879274H3SHQ98Y33XX2","ItemCountInOrderGroup": 11,"TotalCountForOrderGroup": 12,"OrderGroupReason": "MultipleProductsPrescribed"}},"Body": {"NewRx": {"UrgencyIndicatorCode": "X","AllergyOrAdverseEvent": {"Allergies": {"SourceOfInformation": "P","EffectiveDate": {"Date": "1985-12-31"},"ExpirationDate": {"Date": "2018-01-01"},"AdverseEvent": {"Text": "This field is edited and the length is setup to now be a full 80 characters long","Code": 419511003},"DrugProductCoded": {"Code": 12345678911,"Qualifier": "ND","Text": "This field is edited and the length is setup to now be a full 80 characters long"},"ReactionCoded": {"Text": "This field is edited and the length is setup to now be a full 80 characters long","Code": 247472004},"SeverityCoded": {"Text": "This field is edited and the length is setup to now be a full 80 characters long","Code": 6736007}}},"BenefitsCoordination": [{"PayerIdentification": {"PayerID": "This field is edited and the length is setup to now be a full 80 characters long","ProcessorIdentificationNumber": 555555,"NAICCode": "This field is ~35~ characters long!","MutuallyDefined": "This field is ~35~ characters long!","StandardUniqueHealthPlanIdentifier": "This field is ~35~ characters long!","IINNumber": "This field is ~35~ characters long!"},"PayerName": "This field is edited and the length is setup to be 70 characters long!","CardholderID": "This field is ~35~ characters long!","CardHolderName": {"LastName": "This field is ~35~ characters long!","FirstName": "This field is ~35~ characters long!","MiddleName": "This field is ~35~ characters long!","Suffix": "THIS IS 10","Prefix": "THIS IS 10"},"GroupID": "This field is ~35~ characters long!","PayerResponsibilityCode": "PP","PatientRelationship": 4,"PersonCode": 1,"GroupName": "This field is edited and the length is setup to be 70 characters long!","Address": {"AddressLine1": "This field is ~40~ characters long here!","AddressLine2": "This field is ~40~ characters long here!","City": "This field is ~35~ characters long!","StateProvince": "CA","PostalCode": *********,"CountryCode": "US"},"CommunicationNumbers": {"PrimaryTelephone": {"Number": **********,"Extension": 45554745,"SupportsSMS": "Y"},"Beeper": {"Number": **********,"Extension": 32321242,"SupportsSMS": "N"},"ElectronicMail": "<EMAIL>","Fax": {"Number": **********,"Extension": 64646454,"SupportsSMS": "N"},"HomeTelephone": {"Number": **********,"Extension": 64457457,"SupportsSMS": "N"},"WorkTelephone": [{"Number": **********,"Extension": 32114514,"SupportsSMS": "N"},{"Number": **********,"Extension": 32114521,"SupportsSMS": "N"}],"OtherTelephone": [{"Number": **********,"Extension": 32566226,"SupportsSMS": "N"},{"Number": **********,"Extension": 32566986,"SupportsSMS": "N"}],"DirectAddress": "This field is established to test the length ability for a hundred (100) character long string test!----This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!"},"PBMMemberID": "This field is edited and the length is setup to now be a full 80 characters long","ResponsibleParty": {"LastName": "This field is ~35~ characters long!","FirstName": "This field is ~35~ characters long!","MiddleName": "This field is ~35~ characters long!","Suffix": "THIS IS 10","Prefix": "THIS IS 10"}},{"PayerIdentification": {"PayerID": "WENO","ProcessorIdentificationNumber": 555555,"IINNumber": 444444},"PayerName": "CASH CARD","GroupID": "BSURE11","PayerType": "L"},{"PayerIdentification": {"PayerID": 7417234,"ProcessorIdentificationNumber": 555555,"IINNumber": 444444},"PayerName": "Apply Patient Savings","GroupID": 2388,"PayerType": "M"}],"Patient": {"HumanPatient": {"Name": {"LastName": "This field is ~35~ characters long!","FirstName": "This field is ~35~ characters long!","MiddleName": "This field is ~35~ characters long!","Suffix": "THIS IS 10","Prefix": "THIS IS 10"},"Gender": "U","DateOfBirth": {"Date": "2002-04-01"},"Address": {"AddressLine1": "This field is ~40~ characters long here!","AddressLine2": "This field is ~40~ characters long here!","City": "This field is ~35~ characters long!","StateProvince": "CA","PostalCode": *********,"CountryCode": "US"},"CommunicationNumbers": {"PrimaryTelephone": {"Number": **********,"Extension": 45554745,"SupportsSMS": "Y"},"Beeper": {"Number": **********,"Extension": 32321242,"SupportsSMS": "N"},"ElectronicMail": "<EMAIL>","Fax": {"Number": **********,"Extension": 64646454,"SupportsSMS": "N"},"HomeTelephone": {"Number": **********,"Extension": 64457457,"SupportsSMS": "N"},"WorkTelephone": [{"Number": **********,"Extension": 32114514,"SupportsSMS": "N"},{"Number": **********,"Extension": 32114521,"SupportsSMS": "N"}],"OtherTelephone": [{"Number": **********,"Extension": 32566226,"SupportsSMS": "N"},{"Number": **********,"Extension": 32566986,"SupportsSMS": "N"}],"DirectAddress": "This field is established to test the length ability for a hundred (100) character long string test!----This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!"}}},"Pharmacy": {"Identification": {"NCPDPID": 2455142,"StateLicenseNumber": "796597%PH12%82R","MedicareNumber": 886112,"MedicaidNumber": 886112,"DEANumber": "*********","NPI": **********},"BusinessName": "Medi-Blue Rapid Clinic (000)","Address": {"AddressLine1": "2165-B1 Northpoint Parkway","City": "Santa Rosa","StateProvince": "CA","PostalCode": 95407,"CountryCode": "US"},"CommunicationNumbers": {"PrimaryTelephone": {"Number": **********},"Fax": {"Number": **********}}},"Prescriber": {"NonVeterinarian": {"Identification": {"StateLicenseNumber": "784577%1142%14","MedicaidNumber": 654745,"UPIN": 0,"DEANumber": "*********","HIN": 0,"NPI": 222222,"CertificateToPrescribe": "CTP.CA.1142%14"},"Specialty": "363L00000X","PracticeLocation": {"BusinessName": "MediStar of California"},"Name": {"LastName": "Thomas","FirstName": "Walden","MiddleName": "Macnair","Suffix": "NP"},"FormerName": {"LastName": "Thomas","FirstName": "Macnair","MiddleName": "Robert"},"Address": {"AddressLine1": "1425 Mendocino Ave","AddressLine2": "Suite 12-A","City": "Santa Rosa","StateProvince": "CA","PostalCode": 95401,"CountryCode": "US"},"CommunicationNumbers": {"PrimaryTelephone": {"Number": **********,"Extension": 4221},"ElectronicMail": "<EMAIL>","Fax": {"Number": **********},"HomeTelephone": {"Number": **********,"SupportsSMS": "Y"}}}},"Observation": {"Measurement": [{"VitalSign": "8302-2","LOINCVersion": 2.42,"Value": 62,"UnitOfMeasure": "[in_i]","UCUMVersion": 2.1,"ObservationDate": {"Date": "2018-01-20"}},{"VitalSign": "29463-7","LOINCVersion": 2.42,"Value": 140,"UnitOfMeasure": "[lb_av]","UCUMVersion": 2.1,"ObservationDate": {"Date": "2018-01-23"}}],"ObservationNotes": "This field is designed to test a long length of one-hundred-forty (140!) characters in order to better test the ability to support them all!"},"MedicationPrescribed": {"DrugDescription": "This field is established to test the length ability for a hundred-five (105) character long string test!","DrugCoded": {"ProductCode": {"Code": 12345678911,"Qualifier": "ND"},"Strength": {"StrengthValue": "This field is edited and the length is setup to be 70 characters long!","StrengthForm": {"Code": "C78747"},"StrengthUnitOfMeasure": {"Code": "C42576"}},"DrugDBCode": {"Code": 1649988,"Qualifier": "SCD"}},"Quantity": {"Value": 900,"CodeListQualifier": "CF","QuantityUnitOfMeasure": {"Code": "C28254"}},"DaysSupply": 101,"WrittenDate": {"DateTime": "2018-05-01T13:42:39.7Z"},"Substitutions": 1,"NumberOfRefills": 1,"Diagnosis": [{"ClinicalInformationQualifier": 1,"Primary": {"Code": "I10","Qualifier": "ABF","Description": "This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!","DateOfLastOfficeVisit": {"DateTime": "2018-01-01T13:42:39.7Z"}},"Secondary": {"Code": 59621000,"Qualifier": "LD","Description": "This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!","DateOfLastOfficeVisit": {"DateTime": "2018-01-01T13:42:39.7Z"}}},{"ClinicalInformationQualifier": 1,"Primary": {"Code": "E876","Qualifier": "ABF","Description": "This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!","DateOfLastOfficeVisit": {"DateTime": "2018-01-01T13:42:39.7Z"}},"Secondary": {"Code": 43339004,"Qualifier": "LD","Description": "This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!","DateOfLastOfficeVisit": {"DateTime": "2018-01-01T13:42:39.7Z"}}}],"PriorAuthorization": "This field is ~35~ characters long!","Note": "This field is established to test the length ability for two hundred ten (210) character long string test in order to test notes and other fields that are allowed to be that long per the schema and syntax check","DrugUseEvaluation": {"ServiceReasonCode": "PC","ProfessionalServiceCode": 0,"ServiceResultCode": 0,"CoAgent": {"CoAgentCode": {"Code": 12345678911,"Qualifier": 99,"Description": "This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!"}},"ClinicalSignificanceCode": 2,"AcknowledgementReason": "This field is established to test the length ability for a hundred (100) character long string test!"},"DrugCoverageStatusCode": "PA","PriorAuthorizationStatus": "A","Sig": {"SigText": "Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again. Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field test... All to get to 1000 characters in final length, ending here.","CodeSystem": {"SNOMEDVersion": 20170131,"FMTVersion": "16.03d"},"Instruction": [{"DoseAdministration": {"DoseDeliveryMethod": {"Text": "Take","Qualifier": "SNOMED","Code": 419652001},"Dosage": {"DoseQuantity": 0.5,"DoseUnitOfMeasure": {"Text": "tablet","Qualifier": "DoseUnitOfMeasure","Code": "C48542"}},"RouteOfAdministration": {"Text": "oral route","Qualifier": "SNOMED","Code": 26643006}},"TimingAndDuration": [{"Frequency": {"FrequencyNumericValue": 2,"FrequencyUnits": {"Text": "day","Qualifier": "SNOMED","Code": *********}}},{"Duration": {"DurationNumericValue": 1,"DurationText": {"Text": "week","Qualifier": "SNOMED","Code": *********}}}]},{"DoseAdministration": {"DoseDeliveryMethod": {"Text": "Take","Qualifier": "SNOMED","Code": 419652001},"Dosage": {"DoseQuantity": 1,"DoseUnitOfMeasure": {"Text": "tablet","Qualifier": "DoseUnitOfMeasure","Code": "C48542"}},"RouteOfAdministration": {"Text": "oral route","Qualifier": "SNOMED","Code": 26643006}},"TimingAndDuration": [{"Frequency": {"FrequencyNumericValue": 2,"FrequencyUnits": {"Text": "day","Qualifier": "SNOMED","Code": *********}}},{"Duration": {"DurationNumericValue": 1,"DurationText": {"Text": "week","Qualifier": "SNOMED","Code": *********}}}]},{"DoseAdministration": {"DoseDeliveryMethod": {"Text": "Take","Qualifier": "SNOMED","Code": 419652001},"Dosage": {"DoseQuantity": 1.5,"DoseUnitOfMeasure": {"Text": "tablet","Qualifier": "DoseUnitOfMeasure","Code": "C48542"}},"RouteOfAdministration": {"Text": "oral route","Qualifier": "SNOMED","Code": 26643006}},"TimingAndDuration": [{"Frequency": {"FrequencyNumericValue": 2,"FrequencyUnits": {"Text": "day","Qualifier": "SNOMED","Code": *********}}},{"Duration": {"DurationNumericValue": 1,"DurationText": {"Text": "week","Qualifier": "SNOMED","Code": *********}}}]},{"DoseAdministration": {"DoseDeliveryMethod": {"Text": "Take","Qualifier": "SNOMED","Code": 419652001},"Dosage": {"DoseQuantity": 2,"DoseUnitOfMeasure": {"Text": "tablet","Qualifier": "DoseUnitOfMeasure","Code": "C48542"}},"RouteOfAdministration": {"Text": "oral route","Qualifier": "SNOMED","Code": 26643006}},"TimingAndDuration": [{"Frequency": {"FrequencyNumericValue": 2,"FrequencyUnits": {"Text": "day","Qualifier": "SNOMED","Code": *********}}},{"Duration": {"DurationNumericValue": 2,"DurationText": {"Text": "week","Qualifier": "SNOMED","Code": *********}}}]}],"MultipleInstructionModifier": ["THEN","THEN","THEN"],"ClarifyingFreeText": "This field is established to test the length ability for two hundred fifty five (255) character long string test in order to test free text and misc random other fields...................... that are allowed to be that long per the schema and syntax check"},"DoNotFill": "Y","TimeZone": {"TimeZoneIdentifier": "UT","TimeZoneDifferenceQuantity": -5},"OrderCaptureMethod": "EP","ReasonForSubstitutionCodeUsed": "BRAND MEDICALLY NECESSARY","SplitScript": "B","RxFillIndicator": "All Fill Statuses","OfficeOfPharmacyAffairsID": "JJSHEYWH992-012","DeliveryRequest": "ALL FILLS DELIVERY","DeliveryLocation": "CONTACT PATIENT FOR DELIVERY","DiabeticSupply": {"TestingFrequency": 12,"TestingFrequencyNotes": "This field is established to test the length ability for two hundred ten (210) character long string test in order to test notes and other fields that are allowed to be that long per the schema and syntax check","SupplyIndicator": "N","InsulinDependent": "N","HasAutomatedInsulinDevice": "N"},"InjuryRelated": "OTHER","Service": {"AgencyOfService": {"BusinessName": "This field is edited and the length is setup to be 70 characters long!","Address": {"AddressLine1": "This field is ~40~ characters long here!","AddressLine2": "This field is ~40~ characters long here!","City": "This field is ~35~ characters long!","StateProvince": "CA","PostalCode": *********,"CountryCode": "US"},"CommunicationNumbers": {"PrimaryTelephone": {"Number": **********,"Extension": 45554745,"SupportsSMS": "Y"},"ElectronicMail": "<EMAIL>","Fax": {"Number": **********,"Extension": 64646454,"SupportsSMS": "N"},"OtherTelephone": [{"Number": **********,"Extension": 32566226,"SupportsSMS": "N"},{"Number": **********,"Extension": 32566986,"SupportsSMS": "N"}],"DirectAddress": "This field is established to test the length ability for a hundred (100) character long string test!----This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!"},"AgencyContactName": {"LastName": "This field is ~35~ characters long!","FirstName": "This field is ~35~ characters long!","MiddleName": "This field is ~35~ characters long!","Suffix": "THIS IS 10","Prefix": "THIS IS 10"}},"ServiceType": {"TypeOfServiceFreeText": "This field is edited and the length is setup to now be a full 80 characters long","TargetedTypeOfServiceFreeText": "This field is edited and the length is setup to now be a full 80 characters long","EffectiveDate": {"Date": "2018-01-01"},"ExpirationDate": {"Date": "2018-12-31"},"ReasonForMTMServiceFreeText": "This field is edited and the length is setup to now be a full 80 characters long","TypeOfServiceGroupSetting": "Y"}},"IVAdministration": {"NumberOfLumens": 12,"DiluentAmount": {"Value": 123456.1234,"CodeListQualifier": 38,"QuantityUnitOfMeasure": {"Code": "C28254"}},"SpecificAdministrationGauge": 18,"SpecificAdministrationBrand": "SUPER PICC LINE INC","SpecificAdministrationLength": 12,"SpecificAdministrationPump": "N","IVAccessType": {"Text": "This field is edited and the length is setup to now be a full 80 characters long","Code": "C99999"},"IVAccessDeviceType": {"IVAccessDeviceTypeDescription": "This field is edited and the length is setup to now be a full 80 characters long","IVAccessDeviceType": {"Text": "This field is edited and the length is setup to now be a full 80 characters long","Code": "C99999"}},"IVAccessCatheterTip": {"IVAccessCatheterTipDescription": "This field is edited and the length is setup to now be a full 80 characters long","IVAccessCatheterTipType": {"Text": "This field is edited and the length is setup to now be a full 80 characters long","Code": "C99999"}},"IVInfusion": {"IVInfusionDescription": "This field is edited and the length is setup to now be a full 80 characters long","IVInfusionType": {"Text": "This field is edited and the length is setup to now be a full 80 characters long","Code": "C99999"}}},"TreatmentIndicator": "CONTINUATION","ProphylacticOrEpisodic": "EPISODIC","CurrentTreatmentCycle": 3,"NumberOfCyclesPlanned": 35,"Wound": [{"Location": {"Text": "This field is edited and the length is setup to now be a full 80 characters long","Code": 310845006},"Laterality": {"Text": "This field is edited and the length is setup to now be a full 80 characters long","Code": *********},"Length": 12,"Width": 1,"Depth": 2},{"Location": {"Text": "This field is edited and the length is setup to now be a full 80 characters long","Code": 728205001},"Laterality": {"Text": "This field is edited and the length is setup to now be a full 80 characters long","Code": *********},"Length": 12,"Width": 3,"Depth": 2}],"PrescriberCheckedREMS": "N","REMSPatientRiskCategory": "This field is ~35~ characters long!","REMSAuthorizationNumber": "This field is designed to test a long length of one-hundred-forty (140!) characters in order to better test the ability to support them all!","Titration": {"PharmacyToTitrateDose": "Y","TitrationDose": {"Measurement": {"VitalSign": "29463-7","LOINCVersion": 2.42,"Value": 140,"UnitOfMeasure": "[lb_av]","UCUMVersion": 2.1,"MinimumMeasurementValue": 100,"MaximumMeasurementValue": 350,"MeasurementNotes": "This field is established to test the length ability for two hundred fifty five (255) character long string test in order to test free text and misc random other fields...................... that are allowed to be that long per the schema and syntax check"},"MeasurementTimingAndDuration": {"MeasurementAdministrationTiming": {"MeasurementTimingNumericValue": 123,"VariableMeasurementTimingModifier": "AND","AdministrationTimingNumericValue": 567,"MeasurementTimingUnits": {"Text": "This field is edited and the length is setup to now be a full 80 characters long","Qualifier": "SNOMED","Code": *********},"MeasurementTimingModifier": {"Text": "This field is edited and the length is setup to now be a full 80 characters long","Qualifier": "SNOMED","Code": *********},"MeasurementTimingEvent": {"Text": "This field is edited and the length is setup to now be a full 80 characters long","Qualifier": "SNOMED","Code": *********},"MeasurementTimingClarifyingFreeText": "This field is established to test the length ability for two hundred fifty five (255) character long string test in order to test free text and misc random other fields...................... that are allowed to be that long per the schema and syntax check"}}}},"FlavoringRequested": "Y","CompoundInformation": {"FinalCompoundPharmaceuticalDosageForm": "C28254","CompoundIngredientsLotNotUsed": [{"CompoundIngredient": {"CompoundIngredientItemDescription": "Diphenhydramine 12.5 mg/5 mL","Strength": {"StrengthValue": 12.5,"StrengthForm": {"Code": "C42986"},"StrengthUnitOfMeasure": {"Code": "C91131"}}},"Quantity": {"Value": 300,"CodeListQualifier": 38,"QuantityUnitOfMeasure": {"Code": "C28254"}}},{"CompoundIngredient": {"CompoundIngredientItemDescription": "Viscous lidocaine 2%","Strength": {"StrengthValue": 2,"StrengthForm": {"Code": "C42986"},"StrengthUnitOfMeasure": {"Code": "C25613"}}},"Quantity": {"Value": 300,"CodeListQualifier": 38,"QuantityUnitOfMeasure": {"Code": "C28254"}}},{"CompoundIngredient": {"CompoundIngredientItemDescription": "Maalox oral suspension","Strength": {"StrengthValue": "200/200/20","StrengthForm": {"Code": "C68992"},"StrengthUnitOfMeasure": {"Code": "C91131"}}},"Quantity": {"Value": 300,"CodeListQualifier": 38,"QuantityUnitOfMeasure": {"Code": "C28254"}}}]},"NumberOfPackagesToBeDispensed": 1,"PatientCodifiedNote": {"Qualifier": "AG","Value": 10},"OtherMedicationDate": [{"OtherMedicationDate": {"Date": "2018-05-08"},"OtherMedicationDateQualifier": "AnticipatedHealthCareFacilityDischargeDate"},{"OtherMedicationDate": {"Date": "2018-05-08"},"OtherMedicationDateQualifier": "DateValidated"},{"OtherMedicationDate": {"Date": "2018-05-08"},"OtherMedicationDateQualifier": "DeliveredOnDate"},{"OtherMedicationDate": {"Date": "2018-05-08"},"OtherMedicationDateQualifier": "EffectiveDate"},{"OtherMedicationDate": {"Date": "2018-05-08"},"OtherMedicationDateQualifier": "ExpirationDate"},{"OtherMedicationDate": {"Date": "2018-05-08"},"OtherMedicationDateQualifier": "OtherHealthCareFacilityDischargeDate"},{"OtherMedicationDate": {"Date": "2018-05-08"},"OtherMedicationDateQualifier": "PeriodEnd"},{"OtherMedicationDate": {"Date": "2018-05-08"},"OtherMedicationDateQualifier": "SoldDate"},{"OtherMedicationDate": {"Date": "2018-05-08"},"OtherMedicationDateQualifier": "StartDate"},{"OtherMedicationDate": {"Date": "2018-05-08"},"OtherMedicationDateQualifier": "TransplantDate"}],"NeedNoLaterThan": {"NeededNoLaterThanDate": "2018-12-01T13:42:39.7Z","NeededNoLaterThanReason": "This field is edited and the length is setup to be 70 characters long!"},"PlaceOfServiceNonSelfAdministeredProduct": 1,"ProviderExplicitAuthorizationToAdminister": "Y"},"Supervisor": {"NonVeterinarian": {"Identification": {"StateLicenseNumber": "784577%1142%14","MedicaidNumber": 654745,"UPIN": 0,"DEANumber": "*********","HIN": 0,"NPI": 222222,"CertificateToPrescribe": "CTP.CA.1142%14"},"Specialty": "363L00000X","PracticeLocation": {"BusinessName": "MediStar of California"},"Name": {"LastName": "Thomas","FirstName": "Walden","MiddleName": "Macnair","Suffix": "NP"},"FormerName": {"LastName": "Thomas","FirstName": "Macnair","MiddleName": "Robert"},"Address": {"AddressLine1": "1425 Mendocino Ave","AddressLine2": "Suite 12-A","City": "Santa Rosa","StateProvince": "CA","PostalCode": 95401,"CountryCode": "US"},"CommunicationNumbers": {"PrimaryTelephone": {"Number": **********,"Extension": 4221},"ElectronicMail": "<EMAIL>","Fax": {"Number": **********},"HomeTelephone": {"Number": **********,"SupportsSMS": "Y"}}}},"ProhibitRenewalRequest": false,"FollowUpPrescriber": {"NonVeterinarian": {"Identification": {"StateLicenseNumber": "784577%1142%14","MedicaidNumber": 654745,"UPIN": 0,"DEANumber": "*********","HIN": 0,"NPI": 222222,"CertificateToPrescribe": "CTP.CA.1142%14"},"Specialty": "363L00000X","PracticeLocation": {"BusinessName": "MediStar of California"},"Name": {"LastName": "Thomas","FirstName": "Walden","MiddleName": "Macnair","Suffix": "NP"},"FormerName": {"LastName": "Thomas","FirstName": "Macnair","MiddleName": "Robert"},"Address": {"AddressLine1": "1425 Mendocino Ave","AddressLine2": "Suite 12-A","City": "Santa Rosa","StateProvince": "CA","PostalCode": 95401,"CountryCode": "US"},"CommunicationNumbers": {"PrimaryTelephone": {"Number": **********,"Extension": 4221},"ElectronicMail": "<EMAIL>","Fax": {"Number": **********},"HomeTelephone": {"Number": **********,"SupportsSMS": "Y"}}}}}}}}}
    ';
    v_message_json JSONB;
    v_header_json JSONB;
    v_patient_json JSONB;
    v_prescriber_json JSONB;
    v_priority_flag TEXT := 'X';
    ss_message_id INTEGER;
    error_message TEXT;
    message_id_value TEXT := 'test-max-pop-' || floor(random()***********)::TEXT; -- generate unique message_id
BEGIN
    -- Parse the JSON string into JSONB
    v_message_json := (message_json::jsonb)->'json_data';

    -- Set the message_id in the JSON to the generated value
    v_message_json := jsonb_set(v_message_json, '{Message,Header,MessageID}', to_jsonb(message_id_value));

    -- Extract sub-objects
    v_header_json := v_message_json->'Message'->'Header';
    v_patient_json := v_message_json->'Message'->'Body'->'NewRx'->'Patient';
    v_prescriber_json := v_message_json->'Message'->'Body'->'NewRx'->'Prescriber';

    -- Call the parser function
    SELECT tp.ss_message_id, tp.error_message INTO ss_message_id, error_message
    FROM _parse_and_insert_newrx(
        p_ss_log_id := 99999999,
        p_message_json := v_message_json,
        p_surescripts_message_type := 'NewRx',
        p_header_data := v_header_json,
        p_patient_data := v_patient_json,
        p_prescriber_data := v_prescriber_json,
        p_priority_flag := v_priority_flag
    ) tp;
    IF error_message IS NOT NULL THEN
        RAISE EXCEPTION 'Parser error: %', error_message;
    END IF;
    -- Assert ALL form_ss_message fields (exhaustive)
    PERFORM assert_equals('false', (SELECT digital_signature_indicator FROM form_ss_message WHERE id = ss_message_id), 'digital_signature_indicator');
    PERFORM assert_equals('IN', (SELECT direction FROM form_ss_message WHERE id = ss_message_id), 'direction');
    PERFORM assert_equals('2018-05-01 13:42:39.7', (SELECT sent_dt::TEXT FROM form_ss_message WHERE id = ss_message_id), 'sent_dt');
    PERFORM assert_equals('X', (SELECT priority_flag FROM form_ss_message WHERE id = ss_message_id), 'priority_flag');
    PERFORM assert_equals('Yes', (SELECT processed FROM form_ss_message WHERE id = ss_message_id), 'processed');
    PERFORM assert_equals('Verified', (SELECT message_status FROM form_ss_message WHERE id = ss_message_id), 'message_status');
    PERFORM assert_equals('111111', (SELECT send_to FROM form_ss_message WHERE id = ss_message_id), 'send_to');
    PERFORM assert_equals('6301875277001', (SELECT sent_from FROM form_ss_message WHERE id = ss_message_id), 'sent_from');
    PERFORM assert_equals(message_id_value, (SELECT message_id FROM form_ss_message WHERE id = ss_message_id), 'message_id');
    PERFORM assert_equals(NULL, (SELECT related_message_id FROM form_ss_message WHERE id = ss_message_id), 'related_message_id');
    PERFORM assert_equals('4987T6H43RKS8746RGSB3O86RS93S9879rq', (SELECT physician_order_id FROM form_ss_message WHERE id = ss_message_id), 'physician_order_id');
    PERFORM assert_equals(NULL, (SELECT pharmacy_rx_no FROM form_ss_message WHERE id = ss_message_id), 'pharmacy_rx_no');
    PERFORM assert_equals('NewRx', (SELECT message_type FROM form_ss_message WHERE id = ss_message_id), 'message_type');
    PERFORM assert_equals('Surescripts and Maximally-Populated', (SELECT sender_software_developer FROM form_ss_message WHERE id = ss_message_id), 'sender_software_developer');
    PERFORM assert_equals('Surescripts and Maximally-Populated', (SELECT sender_software_product FROM form_ss_message WHERE id = ss_message_id), 'sender_software_product');
    PERFORM assert_equals('This field is set to be fifty (50) characters long', (SELECT sender_software_version FROM form_ss_message WHERE id = ss_message_id), 'sender_software_version');
    PERFORM assert_equals('DNSDOIUEWY937Y49879274H3SHQ98Y33XX2', (SELECT order_group_no FROM form_ss_message WHERE id = ss_message_id), 'order_group_no');
    PERFORM assert_equals(NULL, (SELECT rx_group_no FROM form_ss_message WHERE id = ss_message_id), 'rx_group_no');
    PERFORM assert_equals('11', (SELECT order_group_icnt::TEXT FROM form_ss_message WHERE id = ss_message_id), 'order_group_icnt');
    PERFORM assert_equals('12', (SELECT order_group_tcnt::TEXT FROM form_ss_message WHERE id = ss_message_id), 'order_group_tcnt');
    PERFORM assert_equals('MultipleProductsPrescribed', (SELECT order_group_reason FROM form_ss_message WHERE id = ss_message_id), 'order_group_reason');
    PERFORM assert_equals(NULL, (SELECT rx_group_reason FROM form_ss_message WHERE id = ss_message_id), 'rx_group_reason');
    -- patient_id, physician_id, pharmacy_order_id, site_id: skip (system resolved)
    PERFORM assert_equals('This field is ~35~ characters long!, This field is ~35~ characters long! This field is ~35~ characters long!', (SELECT patient_name_display FROM form_ss_message WHERE id = ss_message_id), 'patient_name_display');
    PERFORM assert_equals('This field is ~35~ characters long!', (SELECT patient_first_name FROM form_ss_message WHERE id = ss_message_id), 'patient_first_name');
    PERFORM assert_equals('This field is ~35~ characters long!', (SELECT patient_last_name FROM form_ss_message WHERE id = ss_message_id), 'patient_last_name');
    PERFORM assert_equals('This field is ~35~ characters long!', (SELECT patient_middle_name FROM form_ss_message WHERE id = ss_message_id), 'patient_middle_name');
    PERFORM assert_equals('2002-04-01', (SELECT patient_dob::TEXT FROM form_ss_message WHERE id = ss_message_id), 'patient_dob');
    PERFORM assert_equals('U', (SELECT patient_gender FROM form_ss_message WHERE id = ss_message_id), 'patient_gender');
    PERFORM assert_equals(NULL, (SELECT patient_ssn FROM form_ss_message WHERE id = ss_message_id), 'patient_ssn');
    PERFORM assert_equals('This field is ~40~ characters long here!', (SELECT patient_home_street_1 FROM form_ss_message WHERE id = ss_message_id), 'patient_home_street_1');
    PERFORM assert_equals('This field is ~40~ characters long here!', (SELECT patient_home_street_2 FROM form_ss_message WHERE id = ss_message_id), 'patient_home_street_2');
    PERFORM assert_equals('This field is ~35~ characters long!', (SELECT patient_home_city FROM form_ss_message WHERE id = ss_message_id), 'patient_home_city');
    PERFORM assert_equals('CA', (SELECT patient_home_state FROM form_ss_message WHERE id = ss_message_id), 'patient_home_state');
    PERFORM assert_equals('*********', (SELECT patient_home_zip FROM form_ss_message WHERE id = ss_message_id), 'patient_home_zip');
    PERFORM assert_equals('**********', (SELECT patient_phone FROM form_ss_message WHERE id = ss_message_id), 'patient_phone');
    PERFORM assert_equals(NULL, (SELECT patient_mrn FROM form_ss_message WHERE id = ss_message_id), 'patient_mrn');
    PERFORM assert_equals(NULL, (SELECT patient_medicare FROM form_ss_message WHERE id = ss_message_id), 'patient_medicare');
    PERFORM assert_equals(NULL, (SELECT patient_medicaid FROM form_ss_message WHERE id = ss_message_id), 'patient_medicaid');
    PERFORM assert_equals(NULL, (SELECT patient_rems FROM form_ss_message WHERE id = ss_message_id), 'patient_rems');
    PERFORM assert_equals(NULL, (SELECT nka FROM form_ss_message WHERE id = ss_message_id), 'nka');
    PERFORM assert_equals('Thomas, Walden', (SELECT prescriber_name_display FROM form_ss_message WHERE id = ss_message_id), 'prescriber_name_display');
    PERFORM assert_equals('222222', (SELECT prescriber_npi FROM form_ss_message WHERE id = ss_message_id), 'prescriber_npi');
    PERFORM assert_equals('*********', (SELECT prescriber_dea FROM form_ss_message WHERE id = ss_message_id), 'prescriber_dea');
    PERFORM assert_equals(NULL, (SELECT prescriber_rems FROM form_ss_message WHERE id = ss_message_id), 'prescriber_rems');
    PERFORM assert_equals(NULL, (SELECT prescriber_state_cs_lic FROM form_ss_message WHERE id = ss_message_id), 'prescriber_state_cs_lic');
    PERFORM assert_equals(NULL, (SELECT prescriber_medicare FROM form_ss_message WHERE id = ss_message_id), 'prescriber_medicare');
    PERFORM assert_equals('654745', (SELECT prescriber_medicaid FROM form_ss_message WHERE id = ss_message_id), 'prescriber_medicaid');
    PERFORM assert_equals('784577%1142%14', (SELECT prescriber_state_lic FROM form_ss_message WHERE id = ss_message_id), 'prescriber_state_lic');
    PERFORM assert_equals('CTP.CA.1142%14', (SELECT prescriber_certificate_to_prescribe FROM form_ss_message WHERE id = ss_message_id), 'prescriber_certificate_to_prescribe');
    PERFORM assert_equals(NULL, (SELECT prescriber_2000waiver_id FROM form_ss_message WHERE id = ss_message_id), 'prescriber_2000waiver_id');
    PERFORM assert_equals('363L00000X', (SELECT prescriber_spec_id FROM form_ss_message WHERE id = ss_message_id), 'prescriber_spec_id');
    PERFORM assert_equals('Thomas', (SELECT prescriber_last_name FROM form_ss_message WHERE id = ss_message_id), 'prescriber_last_name');
    PERFORM assert_equals('Walden', (SELECT prescriber_first_name FROM form_ss_message WHERE id = ss_message_id), 'prescriber_first_name');
    PERFORM assert_equals('1425 Mendocino Ave', (SELECT prescriber_address_1 FROM form_ss_message WHERE id = ss_message_id), 'prescriber_address_1');
    PERFORM assert_equals('Suite 12-A', (SELECT prescriber_address_2 FROM form_ss_message WHERE id = ss_message_id), 'prescriber_address_2');
    PERFORM assert_equals('Santa Rosa', (SELECT prescriber_city FROM form_ss_message WHERE id = ss_message_id), 'prescriber_city');
    PERFORM assert_equals('CA', (SELECT prescriber_state FROM form_ss_message WHERE id = ss_message_id), 'prescriber_state');
    PERFORM assert_equals('95401', (SELECT prescriber_zip FROM form_ss_message WHERE id = ss_message_id), 'prescriber_zip');
    PERFORM assert_equals('**********', (SELECT prescriber_phone FROM form_ss_message WHERE id = ss_message_id), 'prescriber_phone');
    PERFORM assert_equals('4221', (SELECT prescriber_extension FROM form_ss_message WHERE id = ss_message_id), 'prescriber_extension');
    PERFORM assert_equals('**********', (SELECT prescriber_fax FROM form_ss_message WHERE id = ss_message_id), 'prescriber_fax');
    PERFORM assert_equals('MediStar of California', (SELECT prescriber_loc_name FROM form_ss_message WHERE id = ss_message_id), 'prescriber_loc_name');

    -- Subform assertions (Allergies)
    DECLARE
        allergy_count INTEGER;
        benefit_count INTEGER;
        observation_count INTEGER;
        diagnosis_count INTEGER;
        compound_count INTEGER;
        due_count INTEGER;
        codified_note_count INTEGER;
    BEGIN
        -- Allergies
        SELECT COUNT(*) INTO allergy_count FROM form_ss_allergy a JOIN sf_form_ss_message_to_ss_allergy sf ON a.id = sf.form_ss_allergy_fk WHERE sf.form_ss_message_fk = ss_message_id;
        PERFORM assert_equals('1', allergy_count::TEXT, 'Allergy Count');
        PERFORM assert_equals('P', (SELECT source FROM form_ss_allergy a JOIN sf_form_ss_message_to_ss_allergy sf ON a.id = sf.form_ss_allergy_fk WHERE sf.form_ss_message_fk = ss_message_id LIMIT 1), 'Allergy Source');
        PERFORM assert_equals('1985-12-31', (SELECT effective_date::TEXT FROM form_ss_allergy a JOIN sf_form_ss_message_to_ss_allergy sf ON a.id = sf.form_ss_allergy_fk WHERE sf.form_ss_message_fk = ss_message_id LIMIT 1), 'Allergy Effective Date');
        PERFORM assert_equals('2018-01-01', (SELECT expiration_date::TEXT FROM form_ss_allergy a JOIN sf_form_ss_message_to_ss_allergy sf ON a.id = sf.form_ss_allergy_fk WHERE sf.form_ss_message_fk = ss_message_id LIMIT 1), 'Allergy Expiration Date');
        PERFORM assert_equals('419511003', (SELECT adverse_event_code_id FROM form_ss_allergy a JOIN sf_form_ss_message_to_ss_allergy sf ON a.id = sf.form_ss_allergy_fk WHERE sf.form_ss_message_fk = ss_message_id LIMIT 1), 'Allergy Adverse Event Code');
        PERFORM assert_equals('This field is edited and the length is setup to now be a full 80 characters long', (SELECT adverse_event_text FROM form_ss_allergy a JOIN sf_form_ss_message_to_ss_allergy sf ON a.id = sf.form_ss_allergy_fk WHERE sf.form_ss_message_fk = ss_message_id LIMIT 1), 'Allergy Adverse Event Text');
        PERFORM assert_equals('12345678911', (SELECT drug_product_code FROM form_ss_allergy a JOIN sf_form_ss_message_to_ss_allergy sf ON a.id = sf.form_ss_allergy_fk WHERE sf.form_ss_message_fk = ss_message_id LIMIT 1), 'Allergy Drug Product Code');
        PERFORM assert_equals('ND', (SELECT drug_product_qualifier_id FROM form_ss_allergy a JOIN sf_form_ss_message_to_ss_allergy sf ON a.id = sf.form_ss_allergy_fk WHERE sf.form_ss_message_fk = ss_message_id LIMIT 1), 'Allergy Drug Product Qualifier');
        PERFORM assert_equals('This field is edited and the length is setup to now be a full 80 characters long', (SELECT drug_product_text FROM form_ss_allergy a JOIN sf_form_ss_message_to_ss_allergy sf ON a.id = sf.form_ss_allergy_fk WHERE sf.form_ss_message_fk = ss_message_id LIMIT 1), 'Allergy Drug Product Text');
        PERFORM assert_equals('247472004', (SELECT reaction_code_id FROM form_ss_allergy a JOIN sf_form_ss_message_to_ss_allergy sf ON a.id = sf.form_ss_allergy_fk WHERE sf.form_ss_message_fk = ss_message_id LIMIT 1), 'Allergy Reaction Code');
        PERFORM assert_equals('This field is edited and the length is setup to now be a full 80 characters long', (SELECT reaction_text FROM form_ss_allergy a JOIN sf_form_ss_message_to_ss_allergy sf ON a.id = sf.form_ss_allergy_fk WHERE sf.form_ss_message_fk = ss_message_id LIMIT 1), 'Allergy Reaction Text');
        PERFORM assert_equals('6736007', (SELECT severity_code_id FROM form_ss_allergy a JOIN sf_form_ss_message_to_ss_allergy sf ON a.id = sf.form_ss_allergy_fk WHERE sf.form_ss_message_fk = ss_message_id LIMIT 1), 'Allergy Severity Code');
        PERFORM assert_equals('This field is edited and the length is setup to now be a full 80 characters long', (SELECT severity_text FROM form_ss_allergy a JOIN sf_form_ss_message_to_ss_allergy sf ON a.id = sf.form_ss_allergy_fk WHERE sf.form_ss_message_fk = ss_message_id LIMIT 1), 'Allergy Severity Text');

        -- Benefits
        SELECT COUNT(*) INTO benefit_count FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id;
        PERFORM assert_equals('3', benefit_count::TEXT, 'Benefit Count');
        -- Benefit 1
        PERFORM assert_equals('This field is edited and the length is setup to be 70 characters long!', (SELECT payer_name FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY b.id LIMIT 1 OFFSET 0), 'Benefit 1 Payer Name');
        PERFORM assert_equals('This field is ~35~ characters long!', (SELECT group_id FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY b.id LIMIT 1 OFFSET 0), 'Benefit 1 Group ID');
        PERFORM assert_equals('PP', (SELECT payer_level FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY b.id LIMIT 1 OFFSET 0), 'Benefit 1 Payer Responsibility Code');
        PERFORM assert_equals('This field is ~35~ characters long!', (SELECT cardholder_id FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY b.id LIMIT 1 OFFSET 0), 'Benefit 1 Cardholder ID');
        PERFORM assert_equals('This field is ~35~ characters long!', (SELECT cardholder_first_name FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY b.id LIMIT 1 OFFSET 0), 'Benefit 1 Cardholder First Name');
        PERFORM assert_equals('This field is ~35~ characters long!', (SELECT cardholder_last_name FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY b.id LIMIT 1 OFFSET 0), 'Benefit 1 Cardholder Last Name');
        PERFORM assert_equals('This field is ~35~ characters long!', (SELECT bin FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY b.id LIMIT 1 OFFSET 0), 'Benefit 1 BIN');
        PERFORM assert_equals('555555', (SELECT pcn FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY b.id LIMIT 1 OFFSET 0), 'Benefit 1 PCN');
        PERFORM assert_equals('This field is edited and the length is setup to now be a full 80 characters long', (SELECT pbm_member_id FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY b.id LIMIT 1 OFFSET 0), 'Benefit 1 PBM Member ID');
        -- Add all other benefit fields for all 3 rows
        -- Benefit 2
        PERFORM assert_equals('CASH CARD', (SELECT payer_name FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY b.id LIMIT 1 OFFSET 1), 'Benefit 2 Payer Name');
        PERFORM assert_equals('BSURE11', (SELECT group_id FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY b.id LIMIT 1 OFFSET 1), 'Benefit 2 Group ID');
        PERFORM assert_equals('L', (SELECT payer_type_id FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY b.id LIMIT 1 OFFSET 1), 'Benefit 2 Payer Type');
        -- Benefit 3
        PERFORM assert_equals('Apply Patient Savings', (SELECT payer_name FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY b.id LIMIT 1 OFFSET 2), 'Benefit 3 Payer Name');
        PERFORM assert_equals('2388', (SELECT group_id FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY b.id LIMIT 1 OFFSET 2), 'Benefit 3 Group ID');
        PERFORM assert_equals('M', (SELECT payer_type_id FROM form_ss_benefit b JOIN sf_form_ss_message_to_ss_benefit sf ON b.id = sf.form_ss_benefit_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY b.id LIMIT 1 OFFSET 2), 'Benefit 3 Payer Type');

        -- Observations
        SELECT COUNT(*) INTO observation_count FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = ss_message_id;
        PERFORM assert_equals('2', observation_count::TEXT, 'Observation Count');
        -- Observation 1
        PERFORM assert_equals('8302-2', (SELECT type_id FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY o.id LIMIT 1 OFFSET 0), 'Observation 1 Type');
        PERFORM assert_equals('2.42', (SELECT loinc_version FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY o.id LIMIT 1 OFFSET 0), 'Observation 1 LOINC Version');
        PERFORM assert_equals('62', (SELECT value::TEXT FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY o.id LIMIT 1 OFFSET 0), 'Observation 1 Value');
        PERFORM assert_equals('[in_i]', (SELECT unit_id FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY o.id LIMIT 1 OFFSET 0), 'Observation 1 Unit');
        PERFORM assert_equals('2.1', (SELECT ucum_version FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY o.id LIMIT 1 OFFSET 0), 'Observation 1 UCUM Version');
        PERFORM assert_equals('2018-01-20', (SELECT observation_date::TEXT FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY o.id LIMIT 1 OFFSET 0), 'Observation 1 Date');
        PERFORM assert_equals('This field is designed to test a long length of one-hundred-forty (140!) characters in order to better test the ability to support them all!', (SELECT notes FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY o.id LIMIT 1 OFFSET 0), 'Observation 1 Notes');
        -- Observation 2
        PERFORM assert_equals('29463-7', (SELECT type_id FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY o.id LIMIT 1 OFFSET 1), 'Observation 2 Type');
        PERFORM assert_equals('2.42', (SELECT loinc_version FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY o.id LIMIT 1 OFFSET 1), 'Observation 2 LOINC Version');
        PERFORM assert_equals('140', (SELECT value::TEXT FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY o.id LIMIT 1 OFFSET 1), 'Observation 2 Value');
        PERFORM assert_equals('[lb_av]', (SELECT unit_id FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY o.id LIMIT 1 OFFSET 1), 'Observation 2 Unit');
        PERFORM assert_equals('2.1', (SELECT ucum_version FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY o.id LIMIT 1 OFFSET 1), 'Observation 2 UCUM Version');
        PERFORM assert_equals('2018-01-23', (SELECT observation_date::TEXT FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY o.id LIMIT 1 OFFSET 1), 'Observation 2 Date');
        PERFORM assert_equals('This field is designed to test a long length of one-hundred-forty (140!) characters in order to better test the ability to support them all!', (SELECT notes FROM form_ss_observation o JOIN sf_form_ss_message_to_ss_observation sf ON o.id = sf.form_ss_observation_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY o.id LIMIT 1 OFFSET 1), 'Observation 2 Notes');

        -- Diagnoses
        SELECT COUNT(*) INTO diagnosis_count FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = ss_message_id;
        PERFORM assert_equals('4', diagnosis_count::TEXT, 'Diagnosis Count'); -- Max pop has 2 sets of Primary/Secondary
        -- Diagnosis 1 - Primary
        PERFORM assert_equals('I10', (SELECT dx_code FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = ss_message_id AND d.type = 'Primary' ORDER BY d.id LIMIT 1 OFFSET 0), 'Diagnosis 1 Primary Code');
        PERFORM assert_equals('ABF', (SELECT dx_code_qualifier_id FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = ss_message_id AND d.type = 'Primary' ORDER BY d.id LIMIT 1 OFFSET 0), 'Diagnosis 1 Primary Qualifier');
        PERFORM assert_equals('This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!', (SELECT dx_desc FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = ss_message_id AND d.type = 'Primary' ORDER BY d.id LIMIT 1 OFFSET 0), 'Diagnosis 1 Primary Description');
        -- Diagnosis 1 - Secondary
        PERFORM assert_equals('59621000', (SELECT dx_code FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = ss_message_id AND d.type = 'Secondary' ORDER BY d.id LIMIT 1 OFFSET 0), 'Diagnosis 1 Secondary Code');
        PERFORM assert_equals('LD', (SELECT dx_code_qualifier_id FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = ss_message_id AND d.type = 'Secondary' ORDER BY d.id LIMIT 1 OFFSET 0), 'Diagnosis 1 Secondary Qualifier');
        PERFORM assert_equals('This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!', (SELECT dx_desc FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = ss_message_id AND d.type = 'Secondary' ORDER BY d.id LIMIT 1 OFFSET 0), 'Diagnosis 1 Secondary Description');
        -- Diagnosis 2 - Primary
        PERFORM assert_equals('E876', (SELECT dx_code FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = ss_message_id AND d.type = 'Primary' ORDER BY d.id LIMIT 1 OFFSET 1), 'Diagnosis 2 Primary Code');
        PERFORM assert_equals('ABF', (SELECT dx_code_qualifier_id FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = ss_message_id AND d.type = 'Primary' ORDER BY d.id LIMIT 1 OFFSET 1), 'Diagnosis 2 Primary Qualifier');
        PERFORM assert_equals('This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!', (SELECT dx_desc FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = ss_message_id AND d.type = 'Primary' ORDER BY d.id LIMIT 1 OFFSET 1), 'Diagnosis 2 Primary Description');
        -- Diagnosis 2 - Secondary
        PERFORM assert_equals('43339004', (SELECT dx_code FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = ss_message_id AND d.type = 'Secondary' ORDER BY d.id LIMIT 1 OFFSET 1), 'Diagnosis 2 Secondary Code');
        PERFORM assert_equals('LD', (SELECT dx_code_qualifier_id FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = ss_message_id AND d.type = 'Secondary' ORDER BY d.id LIMIT 1 OFFSET 1), 'Diagnosis 2 Secondary Qualifier');
        PERFORM assert_equals('This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!', (SELECT dx_desc FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = ss_message_id AND d.type = 'Secondary' ORDER BY d.id LIMIT 1 OFFSET 1), 'Diagnosis 2 Secondary Description');

        -- Compounds
        SELECT COUNT(*) INTO compound_count FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id;
        PERFORM assert_equals('3', compound_count::TEXT, 'Compound Count');
        -- Compound 1
        PERFORM assert_equals('Diphenhydramine 12.5 mg/5 mL', (SELECT description FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY c.id LIMIT 1 OFFSET 0), 'Compound 1 Description');
        PERFORM assert_equals('12.5', (SELECT strength FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY c.id LIMIT 1 OFFSET 0), 'Compound 1 Strength');
        PERFORM assert_equals('C42986', (SELECT strength_form_id FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY c.id LIMIT 1 OFFSET 0), 'Compound 1 Strength Form');
        PERFORM assert_equals('C91131', (SELECT strength_uom_id FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY c.id LIMIT 1 OFFSET 0), 'Compound 1 Strength UOM');
        PERFORM assert_equals('300', (SELECT quantity::TEXT FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY c.id LIMIT 1 OFFSET 0), 'Compound 1 Quantity');
        -- Compound 2
        PERFORM assert_equals('Viscous lidocaine 2%', (SELECT description FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY c.id LIMIT 1 OFFSET 1), 'Compound 2 Description');
        PERFORM assert_equals('2', (SELECT strength FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY c.id LIMIT 1 OFFSET 1), 'Compound 2 Strength');
        PERFORM assert_equals('C42986', (SELECT strength_form_id FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY c.id LIMIT 1 OFFSET 1), 'Compound 2 Strength Form');
        PERFORM assert_equals('C25613', (SELECT strength_uom_id FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY c.id LIMIT 1 OFFSET 1), 'Compound 2 Strength UOM');
        PERFORM assert_equals('300', (SELECT quantity::TEXT FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY c.id LIMIT 1 OFFSET 1), 'Compound 2 Quantity');
        -- Compound 3
        PERFORM assert_equals('Maalox oral suspension', (SELECT description FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY c.id LIMIT 1 OFFSET 2), 'Compound 3 Description');
        PERFORM assert_equals('200/200/20', (SELECT strength FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY c.id LIMIT 1 OFFSET 2), 'Compound 3 Strength');
        PERFORM assert_equals('C68992', (SELECT strength_form_id FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY c.id LIMIT 1 OFFSET 2), 'Compound 3 Strength Form');
        PERFORM assert_equals('C91131', (SELECT strength_uom_id FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY c.id LIMIT 1 OFFSET 2), 'Compound 3 Strength UOM');
        PERFORM assert_equals('300', (SELECT quantity::TEXT FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY c.id LIMIT 1 OFFSET 2), 'Compound 3 Quantity');

        -- DUEs
        SELECT COUNT(*) INTO due_count FROM form_ss_due d JOIN sf_form_ss_message_to_ss_due sf ON d.id = sf.form_ss_due_fk WHERE sf.form_ss_message_fk = ss_message_id;
        PERFORM assert_equals('1', due_count::TEXT, 'DUE Count');
        -- DUE 1
        PERFORM assert_equals('PC', (SELECT service_reason_id FROM form_ss_due d JOIN sf_form_ss_message_to_ss_due sf ON d.id = sf.form_ss_due_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY d.id LIMIT 1), 'DUE 1 Service Reason Code');
        PERFORM assert_equals('0', (SELECT pservice_rsn_id FROM form_ss_due d JOIN sf_form_ss_message_to_ss_due sf ON d.id = sf.form_ss_due_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY d.id LIMIT 1), 'DUE 1 Professional Service Code'); -- JSON has 00, but parser might make it 0
        PERFORM assert_equals('0', (SELECT service_res_id[1] FROM form_ss_due d JOIN sf_form_ss_message_to_ss_due sf ON d.id = sf.form_ss_due_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY d.id LIMIT 1), 'DUE 1 Service Result Code'); -- JSON has 00, but parser might make it 0
        PERFORM assert_equals('12345678911', (SELECT coagent_code FROM form_ss_due d JOIN sf_form_ss_message_to_ss_due sf ON d.id = sf.form_ss_due_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY d.id LIMIT 1), 'DUE 1 CoAgent Code');
        PERFORM assert_equals('99', (SELECT coagent_qualifier_id FROM form_ss_due d JOIN sf_form_ss_message_to_ss_due sf ON d.id = sf.form_ss_due_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY d.id LIMIT 1), 'DUE 1 CoAgent Qualifier');
        PERFORM assert_equals('This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!', (SELECT coagent_description FROM form_ss_due d JOIN sf_form_ss_message_to_ss_due sf ON d.id = sf.form_ss_due_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY d.id LIMIT 1), 'DUE 1 CoAgent Description');
        PERFORM assert_equals('2', (SELECT clinical_significance FROM form_ss_due d JOIN sf_form_ss_message_to_ss_due sf ON d.id = sf.form_ss_due_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY d.id LIMIT 1), 'DUE 1 Clinical Significance Code');
        PERFORM assert_equals('This field is established to test the length ability for a hundred (100) character long string test!', (SELECT ack_reason FROM form_ss_due d JOIN sf_form_ss_message_to_ss_due sf ON d.id = sf.form_ss_due_fk WHERE sf.form_ss_message_fk = ss_message_id ORDER BY d.id LIMIT 1), 'DUE 1 Acknowledgement Reason');

        -- Codified Notes (one in this message)
        SELECT COUNT(*) INTO codified_note_count FROM form_ss_codified_note n JOIN sf_form_ss_message_to_ss_codified_note sf ON n.id = sf.form_ss_codified_note_fk WHERE sf.form_ss_message_fk = ss_message_id;
        PERFORM assert_equals('1', codified_note_count::TEXT, 'Codified Note Count');
        PERFORM assert_equals('AG', (SELECT qualifier_id FROM form_ss_codified_note n JOIN sf_form_ss_message_to_ss_codified_note sf ON n.id = sf.form_ss_codified_note_fk WHERE sf.form_ss_message_fk = ss_message_id LIMIT 1), 'Codified Note Qualifier');
        PERFORM assert_equals('10', (SELECT value::TEXT FROM form_ss_codified_note n JOIN sf_form_ss_message_to_ss_codified_note sf ON n.id = sf.form_ss_codified_note_fk WHERE sf.form_ss_message_fk = ss_message_id LIMIT 1), 'Codified Note Value');

        -- Drug/Medication assertions (exhaustive)
        PERFORM assert_equals('This field is established to test the length ability for a hundred-five (105) character long string test!', (SELECT description FROM form_ss_message WHERE id = ss_message_id), 'description');
        PERFORM assert_equals('ND', (SELECT product_code_qualifier_id FROM form_ss_message WHERE id = ss_message_id), 'product_code_qualifier_id');
        PERFORM assert_equals('12345678911', (SELECT product_code FROM form_ss_message WHERE id = ss_message_id), 'product_code');
        PERFORM assert_equals('SCD', (SELECT drug_db_qualifier_id FROM form_ss_message WHERE id = ss_message_id), 'drug_db_qualifier_id');
        PERFORM assert_equals('1649988', (SELECT drug_db_code FROM form_ss_message WHERE id = ss_message_id), 'drug_db_code');
        PERFORM assert_equals('This field is edited and the length is setup to be 70 characters long!', (SELECT strength FROM form_ss_message WHERE id = ss_message_id), 'strength');
        PERFORM assert_equals('C78747', (SELECT strength_form_id FROM form_ss_message WHERE id = ss_message_id), 'strength_form_id');
        PERFORM assert_equals('C42576', (SELECT strength_uom_id FROM form_ss_message WHERE id = ss_message_id), 'strength_uom_id');
        PERFORM assert_equals('900', (SELECT quantity::TEXT FROM form_ss_message WHERE id = ss_message_id), 'quantity');
        PERFORM assert_equals('CF', (SELECT quantity_qualifier_id FROM form_ss_message WHERE id = ss_message_id), 'quantity_qualifier_id');
        PERFORM assert_equals('C28254', (SELECT quantity_uom_id FROM form_ss_message WHERE id = ss_message_id), 'quantity_uom_id');
        PERFORM assert_equals('101', (SELECT days_supply::TEXT FROM form_ss_message WHERE id = ss_message_id), 'days_supply');
        PERFORM assert_equals('2018-05-01', (SELECT written_date::TEXT FROM form_ss_message WHERE id = ss_message_id), 'written_date');
        PERFORM assert_equals('1', (SELECT daw FROM form_ss_message WHERE id = ss_message_id), 'daw');
        PERFORM assert_equals('BRAND MEDICALLY NECESSARY', (SELECT daw_code_reason FROM form_ss_message WHERE id = ss_message_id), 'daw_code_reason');
        PERFORM assert_equals('1', (SELECT refills::TEXT FROM form_ss_message WHERE id = ss_message_id), 'refills');
        PERFORM assert_equals('This field is ~35~ characters long!', (SELECT pa_number FROM form_ss_message WHERE id = ss_message_id), 'pa_number');
        PERFORM assert_equals('A', (SELECT pa_status_id FROM form_ss_message WHERE id = ss_message_id), 'pa_status_id');
        PERFORM assert_equals('PA', (SELECT gr.form_list_ss_cvg_status_fk FROM gr_form_ss_message_drug_cvg_status_id_to_list_ss_cvg_status_id gr WHERE gr.form_ss_message_fk = ss_message_id AND gr.form_list_ss_cvg_status_fk = 'PA' LIMIT 1), 'drug_cvg_status_id');
        PERFORM assert_equals('Y', (SELECT do_not_fill FROM form_ss_message WHERE id = ss_message_id), 'do_not_fill');
        PERFORM assert_equals('This field is established to test the length ability for two hundred ten (210) character long string test in order to test notes and other fields that are allowed to be that long per the schema and syntax check', (SELECT note FROM form_ss_message WHERE id = ss_message_id), 'note');
        PERFORM assert_equals('Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again. Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers ********** and decimals *********.********* and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field test... All to get to 1000 characters in final length, ending here.', (SELECT sig FROM form_ss_message WHERE id = ss_message_id), 'sig');
        PERFORM assert_equals('ALL FILLS DELIVERY', (SELECT delivery_request FROM form_ss_message WHERE id = ss_message_id), 'delivery_request');
        PERFORM assert_equals('CONTACT PATIENT FOR DELIVERY', (SELECT delivery_location FROM form_ss_message WHERE id = ss_message_id), 'delivery_location');
        PERFORM assert_equals('Y', (SELECT flavoring_requested FROM form_ss_message WHERE id = ss_message_id), 'flavoring_requested');
        PERFORM assert_equals('N', (SELECT prescriber_checked_rems FROM form_ss_message WHERE id = ss_message_id), 'prescriber_checked_rems');
        PERFORM assert_equals('This field is ~35~ characters long!', (SELECT rems_risk_category FROM form_ss_message WHERE id = ss_message_id), 'rems_risk_category');
        PERFORM assert_equals('This field is designed to test a long length of one-hundred-forty (140!) characters in order to better test the ability to support them all!', (SELECT rems_authorization_number FROM form_ss_message WHERE id = ss_message_id), 'rems_authorization_number');
        PERFORM assert_equals('1', (SELECT clinical_info_qualifier FROM form_ss_message WHERE id = ss_message_id), 'clinical_info_qualifier');

        RAISE NOTICE 'All max-pop NewRx validations passed!';
    END;

END;
$$ LANGUAGE plpgsql; 