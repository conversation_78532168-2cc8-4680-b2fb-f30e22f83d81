-- Test Surescripts Prefill Views and Functions

-- Mocking necessary data for testing vw_ss_available_prefills and get_ss_response_prefill

DO $$
DECLARE
    v_test_patient_id INTEGER := 2896; -- Using patient ID obtained from the previous step
    v_test_physician_id INTEGER;
    v_test_site_id INTEGER;
    v_new_ss_message_id INTEGER;
    v_careplan_order_prefill JSONB;
    v_refill_response_prefill JSONB;
    v_main_params ss_message_main_params_type;
BEGIN
    RAISE NOTICE 'Starting Surescripts prefill tests...';

    -- Ensure a physician and site exist for linking (or use existing ones)
    SELECT id INTO v_test_physician_id FROM form_physician WHERE npi IS NOT NULL AND deleted IS NOT TRUE AND archived IS NOT TRUE LIMIT 1;
    IF v_test_physician_id IS NULL THEN
        RAISE WARNING 'No suitable physician found for testing, skipping some parts or using NULL.';
    END IF;

    SELECT id INTO v_test_site_id FROM form_site WHERE ncpdp_id IS NOT NULL AND deleted IS NOT TRUE AND archived IS NOT TRUE LIMIT 1;
    IF v_test_site_id IS NULL THEN
        RAISE WARNING 'No suitable site found for testing, skipping some parts or using NULL.';
    END IF;

    -- Prepare main params for a NewRx message
    v_main_params.ss_message_type := 'NewRx';
    v_main_params.ss_direction := 'IN';
    v_main_params.resolved_patient_id := v_test_patient_id;
    v_main_params.resolved_physician_id := v_test_physician_id;
    v_main_params.resolved_site_id := v_test_site_id;
    v_main_params.ss_to := (SELECT ncpdp_id FROM form_site WHERE id = v_test_site_id);
    v_main_params.ss_from := (SELECT npi FROM form_physician WHERE id = v_test_physician_id);
    v_main_params.ss_message_id_header := 'test-prefill-' || uuid_generate_v4()::TEXT;
    v_main_params.ss_patient_first_name := (SELECT firstname FROM form_patient WHERE id = v_test_patient_id);
    v_main_params.ss_patient_last_name := (SELECT lastname FROM form_patient WHERE id = v_test_patient_id);
    v_main_params.ss_patient_dob := (SELECT dob FROM form_patient WHERE id = v_test_patient_id);
    v_main_params.ss_prescriber_order_number := 'PON-' || substr(uuid_generate_v4()::TEXT, 1, 8);
    v_main_params.description := 'Test Drug Amoxicillin 250mg Tablet';
    v_main_params.product_code_qualifier_id := 'ND';
    v_main_params.product_code := '00093-3107-01'; -- Example NDC
    v_main_params.fdb_id := (SELECT code FROM form_list_fdb_ndc WHERE ndc = fn_format_ndc('00093-3107-01') LIMIT 1);
    v_main_params.quantity := 30;
    v_main_params.quantity_qualifier_id := '23'; -- Tablet
    v_main_params.days_supply := 10;
    v_main_params.sig := 'Take 1 tablet three times a day for 10 days';
    v_main_params.written_date := CURRENT_DATE;
    v_main_params.refills := 2;
    v_main_params.status_icons := ARRAY['new'];
    v_main_params.show_options := ARRAY['REMS'];
    v_main_params.prescriber_checked_rems := 'Y';


    RAISE NOTICE 'Attempting to insert a new form_ss_message for testing prefill generation. Patient ID: %', v_test_patient_id;

    v_new_ss_message_id := _insert_ss_message_and_subforms(
        p_main_params := v_main_params,
        p_allergies := ARRAY[]::ss_allergy_type[],
        p_benefits := ARRAY[]::ss_benefit_type[],
        p_observations := ARRAY[]::ss_observation_type[],
        p_dues := ARRAY[]::ss_due_type[],
        p_compounds := ARRAY[]::ss_compound_type[],
        p_diagnoses := ARRAY[(NULL, 'Primary', 'J03.90', 'ABF', 'Acute tonsillitis, unspecified')::ss_diagnosis_type], -- Example Diagnosis
        p_codified_notes := ARRAY[]::ss_codified_note_type[],
        p_ss_log_id := NULL -- Not linking to a specific log for this test insert
    );

    RAISE NOTICE 'Inserted test form_ss_message with ID: %', v_new_ss_message_id;

    -- Test vw_ss_available_prefills for 'careplan_order'
    RAISE NOTICE 'Testing vw_ss_available_prefills for careplan_order...';
    SELECT prefill_data INTO v_careplan_order_prefill
    FROM vw_ss_available_prefills
    WHERE ss_message_id = v_new_ss_message_id AND form_name = 'careplan_order';

    IF v_careplan_order_prefill IS NOT NULL THEN
        RAISE NOTICE 'Prefill data for careplan_order (ss_message_id: %): %', v_new_ss_message_id, v_careplan_order_prefill::TEXT;
        -- Check for potential double escaping concern specifically for subform_single_order
        IF jsonb_typeof(v_careplan_order_prefill->'subform_single_order') = 'array' AND jsonb_array_length(v_careplan_order_prefill->'subform_single_order') > 0 THEN
            IF jsonb_typeof((v_careplan_order_prefill->'subform_single_order')->0->'ss_description') = 'string' THEN
                 RAISE NOTICE 'SUCCESS: subform_single_order content appears correctly formatted (ss_description is a string).';
            ELSE
                 RAISE WARNING 'POTENTIAL ISSUE: subform_single_order content might be double-escaped or incorrectly typed. ss_description type: %', jsonb_typeof((v_careplan_order_prefill->'subform_single_order')->0->'ss_description');
            END IF;
        ELSIF jsonb_typeof(v_careplan_order_prefill->'subform_single_order') IS NOT NULL THEN
             RAISE WARNING 'POTENTIAL ISSUE: subform_single_order is not an array. Type: %', jsonb_typeof(v_careplan_order_prefill->'subform_single_order');
        ELSE
            RAISE NOTICE 'NOTE: No subform_single_order data generated by the view for this test case.';
        END IF;
    ELSE
        RAISE WARNING 'No prefill data found for careplan_order (ss_message_id: %) in vw_ss_available_prefills.', v_new_ss_message_id;
    END IF;

    -- Test get_ss_response_prefill for 'refill' action
    RAISE NOTICE 'Testing get_ss_response_prefill for refill action...';
    SELECT get_ss_response_prefill(v_new_ss_message_id, 'refill') INTO v_refill_response_prefill;

    IF v_refill_response_prefill IS NOT NULL THEN
        RAISE NOTICE 'Prefill data for refill response (original_ss_message_id: %): %', v_new_ss_message_id, v_refill_response_prefill::TEXT;
        IF v_refill_response_prefill->>'message_type' = 'RxRenewalRequest' THEN
            RAISE NOTICE 'SUCCESS: get_ss_response_prefill correctly set message_type to RxRenewalRequest for refill action.';
            IF v_refill_response_prefill->>'disp_description' IS NOT NULL THEN
                 RAISE NOTICE 'SUCCESS: get_ss_response_prefill included disp_description: %', v_refill_response_prefill->>'disp_description';
            ELSE
                 RAISE WARNING 'NOTE: get_ss_response_prefill did not populate disp_description for refill action.';
            END IF;
        ELSE
            RAISE WARNING 'POTENTIAL ISSUE: get_ss_response_prefill did not set message_type to RxRenewalRequest for refill. Value: %', v_refill_response_prefill->>'message_type';
        END IF;
    ELSE
        RAISE WARNING 'No prefill data returned by get_ss_response_prefill for refill action (original_ss_message_id: %).', v_new_ss_message_id;
    END IF;

    RAISE NOTICE 'Surescripts prefill tests completed.';

    -- IMPORTANT: Consider rolling back changes if this is purely for testing and data shouldn't persist.
    -- For now, data will persist. Add ROLLBACK; here if needed.
    -- ROLLBACK; 
END $$; 