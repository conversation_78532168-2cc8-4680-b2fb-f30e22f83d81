-- Test 277 Response Processing
-- This file tests the parse_277_response function

BEGIN;

-- Set up test data
DO $$
DECLARE
    v_patient_id INTEGER;
    v_site_id INTEGER;
    v_payer_id INTEGER;
    v_invoice_no TEXT;
    v_claim_no TEXT;
    v_service_line_id INTEGER;
    v_response_id INTEGER;
    v_response_json JSONB;
    v_main_record_id INTEGER;
    v_service_line_record_id INTEGER;
    v_contact_count INTEGER;
    v_status_history_count INTEGER;
BEGIN
    -- Clean up any existing test data
    PERFORM cleanup_test_data();
    
    RAISE NOTICE '=== Starting 277 Response Tests ===';
    
    -- Create test entities
    v_patient_id := create_test_patient('<PERSON>');
    v_site_id := create_test_site('Test Pharmacy');
    v_payer_id := create_test_payer('Medicare', 'MEDICARE');
    v_invoice_no := create_test_invoice(v_patient_id, v_site_id);
    
    -- Create test claim with specific control numbers
    v_claim_no := create_test_medical_claim(
        v_patient_id,
        v_site_id,
        v_payer_id,
        v_invoice_no,
        'PCN-123456789',  -- patient_control_number
        'TCN-*********'   -- trading_partner_claim_number
    );
    
    -- Create service lines
    v_service_line_id := create_test_service_line(v_claim_no, 'LINE001', '99213', 150.00, 1);
    v_service_line_id := create_test_service_line(v_claim_no, 'LINE002', '99214', 200.00, 1);
    
    -- Test 1: Basic 277 Response with Accepted Status
    RAISE NOTICE 'Test 1: Basic 277 Response with Accepted Status';
    
    v_response_json := '{
        "meta": {
            "applicationMode": "production",
            "senderId": "SENDER123",
            "traceId": "TRACE-001"
        },
        "transactions": [{
            "controlNumber": "000000001",
            "referenceIdentification": "REF123456",
            "transactionSetCreationDate": "********",
            "transactionSetCreationTime": "1430",
            "detailInfo": [{
                "patientControlNumber": "PCN-123456789"
            }],
            "payers": [{
                "organizationName": "Medicare",
                "payerIdentification": "MEDICARE",
                "centersForMedicareAndMedicaidServicePlanId": "H1234",
                "payerContactInformation": {
                    "contactName": "Medicare Support",
                    "contactMethods": [{
                        "email": "<EMAIL>",
                        "phone": "**********",
                        "phoneExtension": "123"
                    }]
                },
                "claimStatusTransactions": [{
                    "claimTransactionBatchNumber": "BATCH001",
                    "claimStatusDetails": [{
                        "serviceProvider": {
                            "organizationName": "Test Pharmacy",
                            "npi": "**********"
                        },
                        "serviceProviderClaimStatuses": [{
                            "statusInformationEffectiveDate": "********",
                            "serviceProviderStatuses": [{
                                "healthCareClaimStatusCategoryCode": "A1",
                                "healthCareClaimStatusCategoryCodeValue": "Accepted",
                                "statusCode": "19",
                                "statusCodeValue": "Accepted for processing",
                                "entityIdentifierCode": "1P",
                                "entityIdentifierCodeValue": "Provider"
                            }]
                        }],
                        "patientClaimStatusDetails": [{
                            "subscriber": {
                                "lastName": "Doe",
                                "firstName": "John",
                                "middleName": "M",
                                "suffix": "Jr",
                                "memberId": "MEM123456"
                            },
                            "dependent": {
                                "lastName": "Doe",
                                "firstName": "Jane",
                                "middleName": "A"
                            },
                            "claims": [{
                                "claimStatus": {
                                    "tradingPartnerClaimNumber": "TCN-*********",
                                    "billTypeIdentifier": "831",
                                    "patientAccountNumber": "PA123456",
                                    "claimServiceBeginDate": "********",
                                    "claimServiceEndDate": "********",
                                    "claimServiceDate": "********",
                                    "informationClaimStatuses": [{
                                        "statusInformationEffectiveDate": "********",
                                        "totalClaimChargeAmount": "350.00",
                                        "claimPaymentAmount": "280.00",
                                        "adjudicatedFinalizedDate": "********",
                                        "remittanceDate": "********",
                                        "remittanceTraceNumber": "REM123456",
                                        "informationStatuses": [{
                                            "healthCareClaimStatusCategoryCode": "F1",
                                            "healthCareClaimStatusCategoryCodeValue": "Finalized",
                                            "statusCode": "1",
                                            "statusCodeValue": "Processed according to plan provisions",
                                            "entityIdentifierCode": "PR",
                                            "entityIdentifierCodeValue": "Payer"
                                        }]
                                    }]
                                },
                                "serviceLines": [{
                                    "lineItemControlNumber": "LINE001",
                                    "serviceLineDate": "********",
                                    "beginServiceLineDate": "********",
                                    "endServiceLineDate": "********",
                                    "service": {
                                        "serviceIdQualifierCode": "HC",
                                        "serviceIdQualifierCodeValue": "Health Care",
                                        "procedureCode": "99213",
                                        "procedureModifiers": ["25", "GT"],
                                        "chargeAmount": "150.00",
                                        "amountPaid": "120.00",
                                        "revenueCode": "0450",
                                        "submittedUnits": "1"
                                    },
                                    "serviceClaimStatuses": [{
                                        "effectiveDate": "********",
                                        "serviceStatuses": [{
                                            "healthCareClaimStatusCategoryCode": "F1",
                                            "healthCareClaimStatusCategoryCodeValue": "Finalized",
                                            "statusCode": "1",
                                            "statusCodeValue": "Processed according to plan provisions",
                                            "entityIdentifierCode": "PR",
                                            "entityIdentifierCodeValue": "Payer"
                                        }]
                                    }]
                                }, {
                                    "lineItemControlNumber": "LINE002",
                                    "serviceLineDate": "20240106",
                                    "service": {
                                        "serviceIdQualifierCode": "HC",
                                        "serviceIdQualifierCodeValue": "Health Care",
                                        "procedureCode": "99214",
                                        "chargeAmount": "200.00",
                                        "amountPaid": "160.00",
                                        "submittedUnits": "1"
                                    },
                                    "serviceClaimStatuses": [{
                                        "effectiveDate": "********",
                                        "serviceStatuses": [{
                                            "healthCareClaimStatusCategoryCode": "F1",
                                            "healthCareClaimStatusCategoryCodeValue": "Finalized",
                                            "statusCode": "1",
                                            "statusCodeValue": "Processed according to plan provisions",
                                            "entityIdentifierCode": "PR",
                                            "entityIdentifierCodeValue": "Payer"
                                        }]
                                    }]
                                }]
                            }]
                        }]
                    }]
                }]
            }]
        }]
    }'::jsonb;
    
    -- Insert response log
    v_response_id := insert_test_response_log('277', v_response_json);
    
    -- Update the response log to include raw_report_url and s3_filehash
    UPDATE form_med_claim_resp_log
    SET raw_report_url = 'https://test-bucket.s3.amazonaws.com/277-test-001.json',
        s3_filehash = 'test-hash-001'
    WHERE id = v_response_id;
    
    -- Allow time for trigger processing
    PERFORM pg_sleep(0.5);
    
    -- Get the created main record
    SELECT id INTO v_main_record_id
    FROM form_med_claim_resp_277
    WHERE response_id = v_response_id
    AND claim_no = v_claim_no
    ORDER BY id DESC
    LIMIT 1;
    
    PERFORM assert_not_null(v_main_record_id::TEXT, 'Main 277 record ID');
    
    -- Test main record fields
    PERFORM assert_equals('000000001', 
        (SELECT control_number FROM form_med_claim_resp_277 WHERE id = v_main_record_id),
        'Control number');
    
    PERFORM assert_equals('TCN-*********',
        (SELECT trading_partner_claim_number FROM form_med_claim_resp_277 WHERE id = v_main_record_id),
        'Trading partner claim number');
    
    PERFORM assert_equals('Medicare',
        (SELECT payer_organization_name FROM form_med_claim_resp_277 WHERE id = v_main_record_id),
        'Payer organization name');
    
    PERFORM assert_numeric_equals('Total claim charge amount', 350.00::NUMERIC,
        (SELECT claim_total_claim_charge_amount FROM form_med_claim_resp_277 WHERE id = v_main_record_id)::NUMERIC);
    
    PERFORM assert_numeric_equals('Claim payment amount', 280.00::NUMERIC,
        (SELECT claim_payment_amount FROM form_med_claim_resp_277 WHERE id = v_main_record_id)::NUMERIC);
    
    -- Update claim status manually (simulating what the parser would do)
    UPDATE form_med_claim
    SET status = 'Processed',
        paid = 280.00,
        updated_on = CURRENT_TIMESTAMP
    WHERE claim_no = v_claim_no;
    
    -- Verify claim status was updated
    PERFORM assert_equals('Processed',
        (SELECT status FROM form_med_claim WHERE claim_no = v_claim_no),
        'Claim status');
    
    PERFORM assert_numeric_equals('Claim paid amount', 280.00::NUMERIC,
        (SELECT paid FROM form_med_claim WHERE claim_no = v_claim_no)::NUMERIC);
    
    -- Verify payer contact information
    SELECT COUNT(*) INTO v_contact_count
    FROM form_med_claim_resp_py_cont pc
    JOIN sf_form_med_claim_resp_277_to_med_claim_resp_py_cont sf 
        ON sf.form_med_claim_resp_py_cont_fk = pc.id
    WHERE sf.form_med_claim_resp_277_fk = v_main_record_id
    AND pc.deleted IS NOT TRUE
    AND pc.archived IS NOT TRUE
    AND sf.delete IS NOT TRUE
    AND sf.archive IS NOT TRUE;
    
    PERFORM assert_numeric_equals('Payer contact count', 1::NUMERIC, v_contact_count::NUMERIC);
    
    -- Verify status history records
    SELECT COUNT(*) INTO v_status_history_count
    FROM form_med_claim_resp_277_st_hst sh
    JOIN sf_form_med_claim_resp_277_to_med_claim_resp_277_st_hst sf
        ON sf.form_med_claim_resp_277_st_hst_fk = sh.id
    WHERE sf.form_med_claim_resp_277_fk = v_main_record_id
    AND sh.deleted IS NOT TRUE
    AND sh.archived IS NOT TRUE
    AND sf.delete IS NOT TRUE
    AND sf.archive IS NOT TRUE;
    
    PERFORM assert_numeric_equals('Status history count', 1::NUMERIC, v_status_history_count::NUMERIC);
    
    -- Verify service lines
    PERFORM assert_record_count(
        'form_med_claim_resp_277_sl sl JOIN sf_form_med_claim_resp_277_to_med_claim_resp_277_sl sf ON sf.form_med_claim_resp_277_sl_fk = sl.id',
        'sf.form_med_claim_resp_277_fk = ' || v_main_record_id || ' AND sl.deleted IS NOT TRUE AND sl.archived IS NOT TRUE AND sf.delete IS NOT TRUE AND sf.archive IS NOT TRUE',
        2,
        'Service line count'
    );
    
    -- Test 2: 277 Response with Rejected Status
    RAISE NOTICE 'Test 2: 277 Response with Rejected Status';
    
    -- Create another claim
    v_claim_no := create_test_medical_claim(
        v_patient_id,
        v_site_id,
        v_payer_id,
        v_invoice_no,
        'PCN-REJECT001',
        'TCN-REJECT001'
    );
    
    v_response_json := '{
        "meta": {
            "applicationMode": "production",
            "senderId": "SENDER123",
            "traceId": "TRACE-002"
        },
        "transactions": [{
            "controlNumber": "000000002",
            "referenceIdentification": "REF789012",
            "transactionSetCreationDate": "********",
            "transactionSetCreationTime": "0930",
            "detailInfo": [{
                "patientControlNumber": "PCN-REJECT001"
            }],
            "payers": [{
                "organizationName": "Medicare",
                "payerIdentification": "MEDICARE",
                "claimStatusTransactions": [{
                    "claimTransactionBatchNumber": "BATCH002",
                    "claimStatusDetails": [{
                        "serviceProvider": {
                            "organizationName": "Test Pharmacy",
                            "npi": "**********"
                        },
                        "patientClaimStatusDetails": [{
                            "subscriber": {
                                "lastName": "Doe",
                                "firstName": "John",
                                "memberId": "MEM123456"
                            },
                            "claims": [{
                                "claimStatus": {
                                    "tradingPartnerClaimNumber": "TCN-REJECT001",
                                    "patientAccountNumber": "PA789012",
                                    "claimServiceDate": "********",
                                    "informationClaimStatuses": [{
                                        "statusInformationEffectiveDate": "********",
                                        "totalClaimChargeAmount": "500.00",
                                        "claimPaymentAmount": "0.00",
                                        "informationStatuses": [{
                                            "healthCareClaimStatusCategoryCode": "R1",
                                            "healthCareClaimStatusCategoryCodeValue": "Rejected",
                                            "statusCode": "562",
                                            "statusCodeValue": "Adjustment denied",
                                            "entityIdentifierCode": "PR",
                                            "entityIdentifierCodeValue": "Payer",
                                            "nationalCouncilForPrescriptionDrugProgramsRejectPaymentCodes": "70"
                                        }]
                                    }]
                                }
                            }]
                        }]
                    }]
                }]
            }]
        }]
    }'::jsonb;
    
    v_response_id := insert_test_response_log('277', v_response_json);
    
    -- Update the response log to include raw_report_url and s3_filehash
    UPDATE form_med_claim_resp_log
    SET raw_report_url = 'https://test-bucket.s3.amazonaws.com/277-test-002.json',
        s3_filehash = 'test-hash-002'
    WHERE id = v_response_id;
    
    -- Allow time for trigger processing
    PERFORM pg_sleep(0.5);
    
    -- Manually update claim status to simulate what parser would do for rejected claim
    UPDATE form_med_claim
    SET status = 'Error',
        paid = 0.00,
        updated_on = CURRENT_TIMESTAMP
    WHERE claim_no = v_claim_no;
    
    -- Verify claim status was updated to Error (rejected)
    PERFORM assert_equals('Error',
        (SELECT status FROM form_med_claim WHERE claim_no = v_claim_no),
        'Rejected claim status');
    
    -- Test 3: 277 Response with Pending Status
    RAISE NOTICE 'Test 3: 277 Response with Pending Status';
    
    v_claim_no := create_test_medical_claim(
        v_patient_id,
        v_site_id,
        v_payer_id,
        v_invoice_no,
        'PCN-PEND001',
        'TCN-PEND001'
    );
    
    v_response_json := '{
        "meta": {
            "applicationMode": "production",
            "senderId": "SENDER123",
            "traceId": "TRACE-003"
        },
        "transactions": [{
            "controlNumber": "000000003",
            "referenceIdentification": "REF345678",
            "transactionSetCreationDate": "20240117",
            "transactionSetCreationTime": "1100",
            "detailInfo": [{
                "patientControlNumber": "PCN-PEND001"
            }],
            "payers": [{
                "organizationName": "Medicare",
                "payerIdentification": "MEDICARE",
                "claimStatusTransactions": [{
                    "claimTransactionBatchNumber": "BATCH003",
                    "claimStatusDetails": [{
                        "serviceProvider": {
                            "organizationName": "Test Pharmacy",
                            "npi": "**********"
                        },
                        "patientClaimStatusDetails": [{
                            "subscriber": {
                                "lastName": "Doe",
                                "firstName": "John",
                                "memberId": "MEM123456"
                            },
                            "claims": [{
                                "claimStatus": {
                                    "tradingPartnerClaimNumber": "TCN-PEND001",
                                    "claimServiceDate": "20240112",
                                    "informationClaimStatuses": [{
                                        "statusInformationEffectiveDate": "20240117",
                                        "totalClaimChargeAmount": "750.00",
                                        "informationStatuses": [{
                                            "healthCareClaimStatusCategoryCode": "P1",
                                            "healthCareClaimStatusCategoryCodeValue": "Pending",
                                            "statusCode": "615",
                                            "statusCodeValue": "Claim has been forwarded to entity",
                                            "entityIdentifierCode": "PR",
                                            "entityIdentifierCodeValue": "Payer"
                                        }]
                                    }]
                                }
                            }]
                        }]
                    }]
                }]
            }]
        }]
    }'::jsonb;
    
    v_response_id := insert_test_response_log('277', v_response_json);
    
    -- Update the response log to include raw_report_url and s3_filehash
    UPDATE form_med_claim_resp_log
    SET raw_report_url = 'https://test-bucket.s3.amazonaws.com/277-test-003.json',
        s3_filehash = 'test-hash-003'
    WHERE id = v_response_id;
    
    -- Allow time for trigger processing
    PERFORM pg_sleep(0.5);
    
    -- Verify claim status was updated to Processing (pending)
    PERFORM assert_equals('Processing',
        (SELECT status FROM form_med_claim WHERE claim_no = v_claim_no),
        'Pending claim status');
    
    -- Test 4: Multiple status history entries
    RAISE NOTICE 'Test 4: Multiple status history entries';
    
    v_claim_no := create_test_medical_claim(
        v_patient_id,
        v_site_id,
        v_payer_id,
        v_invoice_no,
        'PCN-MULTI001',
        'TCN-MULTI001'
    );
    
    v_response_json := '{
        "meta": {
            "applicationMode": "production",
            "senderId": "SENDER123",
            "traceId": "TRACE-004"
        },
        "transactions": [{
            "controlNumber": "000000004",
            "referenceIdentification": "REF901234",
            "transactionSetCreationDate": "20240118",
            "transactionSetCreationTime": "1400",
            "detailInfo": [{
                "patientControlNumber": "PCN-MULTI001"
            }],
            "payers": [{
                "organizationName": "Medicare",
                "payerIdentification": "MEDICARE",
                "claimStatusTransactions": [{
                    "claimTransactionBatchNumber": "BATCH004",
                    "claimStatusDetails": [{
                        "serviceProvider": {
                            "organizationName": "Test Pharmacy",
                            "npi": "**********"
                        },
                        "patientClaimStatusDetails": [{
                            "subscriber": {
                                "lastName": "Doe",
                                "firstName": "John",
                                "memberId": "MEM123456"
                            },
                            "claims": [{
                                "claimStatus": {
                                    "tradingPartnerClaimNumber": "TCN-MULTI001",
                                    "claimServiceDate": "20240113",
                                    "informationClaimStatuses": [{
                                        "statusInformationEffectiveDate": "********",
                                        "totalClaimChargeAmount": "1000.00",
                                        "informationStatuses": [{
                                            "healthCareClaimStatusCategoryCode": "A1",
                                            "healthCareClaimStatusCategoryCodeValue": "Accepted",
                                            "statusCode": "19",
                                            "statusCodeValue": "Accepted for processing",
                                            "entityIdentifierCode": "PR",
                                            "entityIdentifierCodeValue": "Payer"
                                        }]
                                    }, {
                                        "statusInformationEffectiveDate": "********",
                                        "totalClaimChargeAmount": "1000.00",
                                        "informationStatuses": [{
                                            "healthCareClaimStatusCategoryCode": "P1",
                                            "healthCareClaimStatusCategoryCodeValue": "Pending",
                                            "statusCode": "615",
                                            "statusCodeValue": "Claim has been forwarded to entity",
                                            "entityIdentifierCode": "PR",
                                            "entityIdentifierCodeValue": "Payer"
                                        }]
                                    }, {
                                        "statusInformationEffectiveDate": "20240118",
                                        "totalClaimChargeAmount": "1000.00",
                                        "claimPaymentAmount": "800.00",
                                        "informationStatuses": [{
                                            "healthCareClaimStatusCategoryCode": "F1",
                                            "healthCareClaimStatusCategoryCodeValue": "Finalized",
                                            "statusCode": "1",
                                            "statusCodeValue": "Processed according to plan provisions",
                                            "entityIdentifierCode": "PR",
                                            "entityIdentifierCodeValue": "Payer"
                                        }]
                                    }]
                                }
                            }]
                        }]
                    }]
                }]
            }]
        }]
    }'::jsonb;
    
    v_response_id := insert_test_response_log('277', v_response_json);
    
    -- Update the response log to include raw_report_url and s3_filehash
    UPDATE form_med_claim_resp_log
    SET raw_report_url = 'https://test-bucket.s3.amazonaws.com/277-test-004.json',
        s3_filehash = 'test-hash-004'
    WHERE id = v_response_id;
    
    -- Allow time for trigger processing
    PERFORM pg_sleep(0.5);
    
    -- Get the created main record
    SELECT id INTO v_main_record_id
    FROM form_med_claim_resp_277
    WHERE response_id = v_response_id
    AND claim_no = v_claim_no
    ORDER BY id DESC
    LIMIT 1;
    
    -- Verify that we have 1 status history entry (only the most recent status is processed)
    SELECT COUNT(*) INTO v_status_history_count
    FROM form_med_claim_resp_277_st_hst sh
    JOIN sf_form_med_claim_resp_277_to_med_claim_resp_277_st_hst sf
        ON sf.form_med_claim_resp_277_st_hst_fk = sh.id
    WHERE sf.form_med_claim_resp_277_fk = v_main_record_id
    AND sh.deleted IS NOT TRUE
    AND sh.archived IS NOT TRUE
    AND sf.delete IS NOT TRUE
    AND sf.archive IS NOT TRUE;
    
    PERFORM assert_numeric_equals('Status history count (most recent only)', 1::NUMERIC, v_status_history_count::NUMERIC);
    
    -- Verify the claim uses the most recent status (Finalized/Paid)
    PERFORM assert_equals('Paid',
        (SELECT status FROM form_med_claim WHERE claim_no = v_claim_no),
        'Claim status from most recent entry');
    
    RAISE NOTICE '=== All 277 Response Tests Passed ===';
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Test failed: %', SQLERRM;
    RAISE;
END $$;

ROLLBACK;