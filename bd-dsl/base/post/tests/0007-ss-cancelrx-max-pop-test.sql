DO $$
DECLARE
    -- Variable declarations
    v_message_json JSONB;
    v_header_json JSONB;
    v_patient_json JSONB;
    v_prescriber_json JSONB;
    v_pharmacy_json JSONB;
    v_medication_json JSONB;

    v_ss_message_id INTEGER;
    v_temp_text TEXT;
    v_temp_int INTEGER;
    v_temp_date DATE;
    v_temp_text_array TEXT[];
    error_message TEXT;
    message_id_value TEXT := 'test-cancelrx-max-pop-' || floor(random()***********)::TEXT;
    v_diag_record RECORD;
    v_compound_record RECORD;
    v_obs_record RECORD;
    v_due_record RECORD;
    v_allergy_record RECORD;
BEGIN
    
    -- Build the Header section
    v_header_json := jsonb_build_object(
        'To', 111111,
        'From', 6301875277001,
        'MessageID', message_id_value,
        'RelatesToMessageID', 'ECHO FROM REFRES',
        'SentTime', '2019-01-01T13:42:39.7Z',
        'SenderSoftware', jsonb_build_object(
            'SenderSoftwareDeveloper', 'Surescripts',
            'SenderSoftwareProduct', 'Certification Testing',
            'SenderSoftwareVersionRelease', 20170715
        )
    );
    
    -- Build the Patient section
    v_patient_json := jsonb_build_object(
        'HumanPatient', jsonb_build_object(
            'Name', jsonb_build_object(
                'LastName', 'Usumacintacoatzacoalcosniltepecvera',
                'FirstName', 'Juancarlosguadalupepaploapan',
                'MiddleName', 'Franciscolisandroculiacan',
                'Suffix', 'Junior'
            ),
            'Gender', 'M',
            'DateOfBirth', jsonb_build_object('Date', '2004-06-21'),
            'Address', jsonb_build_object(
                'AddressLine1', '27732 West Alameda Potholeladen Street',
                'AddressLine2', 'Apt 425-B',
                'City', 'Rancho Cucamonga',
                'StateProvince', 'CA',
                'PostalCode', 917011515,
                'CountryCode', 'US'
            ),
            'CommunicationNumbers', jsonb_build_object(
                'PrimaryTelephone', jsonb_build_object('Number', **********, 'SupportsSMS', 'Y'),
                'ElectronicMail', '<EMAIL>',
                'HomeTelephone', jsonb_build_object('Number', **********),
                'WorkTelephone', jsonb_build_object('Number', **********, 'Extension', 45422142, 'SupportsSMS', 'N'),
                'OtherTelephone', jsonb_build_object('Number', **********)
            )
        )
    );
    
    -- Build the Pharmacy section
    v_pharmacy_json := jsonb_build_object(
        'Identification', jsonb_build_object(
            'NCPDPID', 2455142,
            'StateLicenseNumber', '796597%PH12%82R',
            'MedicareNumber', 886112,
            'MedicaidNumber', 886112,
            'DEANumber', '*********',
            'NPI', **********
        ),
        'BusinessName', 'Medi-Blue Rapid Clinic (000)',
        'Address', jsonb_build_object(
            'AddressLine1', '2165-B1 Northpoint Parkway',
            'City', 'Santa Rosa',
            'StateProvince', 'CA',
            'PostalCode', 95407,
            'CountryCode', 'US'
        ),
        'CommunicationNumbers', jsonb_build_object(
            'PrimaryTelephone', jsonb_build_object('Number', **********),
            'Fax', jsonb_build_object('Number', **********)
        )
    );
    
    -- Build the Prescriber section
    v_prescriber_json := jsonb_build_object(
        'NonVeterinarian', jsonb_build_object(
            'Identification', jsonb_build_object(
                'StateLicenseNumber', '784577%1147%24',
                'MedicaidNumber', 658345,
                'DEANumber', '*********',
                'NPI', 222222,
                'CertificateToPrescribe', 'CTP.CA.1147%24'
            ),
            'Specialty', '363L00000X',
            'PracticeLocation', jsonb_build_object(
                'BusinessName', 'MediStar of California'
            ),
            'Name', jsonb_build_object(
                'LastName', 'Pimpernel',
                'FirstName', 'Marguerite',
                'MiddleName', 'Anne',
                'Suffix', 'MD',
                'Prefix', 'DR'
            ),
            'Address', jsonb_build_object(
                'AddressLine1', '1425 Mendocino Ave',
                'AddressLine2', 'Suite 12-A',
                'City', 'Santa Rosa',
                'StateProvince', 'CA',
                'PostalCode', 95401,
                'CountryCode', 'US'
            ),
            'CommunicationNumbers', jsonb_build_object(
                'PrimaryTelephone', jsonb_build_object('Number', **********, 'Extension', 4221),
                'ElectronicMail', '<EMAIL>',
                'Fax', jsonb_build_object('Number', 7079442121)
            )
        )
    );
    
    -- Build the compound ingredients
    DECLARE
        compound_ingredients JSONB := jsonb_build_array(
            jsonb_build_object(
                'CompoundIngredient', jsonb_build_object(
                    'CompoundIngredientItemDescription', 'Diphenhydramine 12.5 mg/5 mL',
                    'Strength', jsonb_build_object(
                        'StrengthValue', 12.5,
                        'StrengthForm', jsonb_build_object('Code', 'C42986'),
                        'StrengthUnitOfMeasure', jsonb_build_object('Code', 'C91131')
                    )
                ),
                'Quantity', jsonb_build_object(
                    'Value', 300,
                    'CodeListQualifier', 38,
                    'QuantityUnitOfMeasure', jsonb_build_object('Code', 'C28254')
                )
            ),
            jsonb_build_object(
                'CompoundIngredient', jsonb_build_object(
                    'CompoundIngredientItemDescription', 'Viscous lidocaine 2%',
                    'Strength', jsonb_build_object(
                        'StrengthValue', 2,
                        'StrengthForm', jsonb_build_object('Code', 'C42986'),
                        'StrengthUnitOfMeasure', jsonb_build_object('Code', 'C25613')
                    )
                ),
                'Quantity', jsonb_build_object(
                    'Value', 300,
                    'CodeListQualifier', 38,
                    'QuantityUnitOfMeasure', jsonb_build_object('Code', 'C28254')
                )
            ),
            jsonb_build_object(
                'CompoundIngredient', jsonb_build_object(
                    'CompoundIngredientItemDescription', 'Maalox oral suspension',
                    'Strength', jsonb_build_object(
                        'StrengthValue', '200/200/20',
                        'StrengthForm', jsonb_build_object('Code', 'C68992'),
                        'StrengthUnitOfMeasure', jsonb_build_object('Code', 'C91131')
                    )
                ),
                'Quantity', jsonb_build_object(
                    'Value', 300,
                    'CodeListQualifier', 38,
                    'QuantityUnitOfMeasure', jsonb_build_object('Code', 'C28254')
                )
            )
        );
    BEGIN
        -- Build the MedicationPrescribed section
        v_medication_json := jsonb_build_object(
            'DrugDescription', 'Magic Mouthwash Diphenhydramine 12.5 mg/5 mL, Viscous lidocaine 2%, Maalox 1 part',
            'Quantity', jsonb_build_object(
                'Value', 900,
                'CodeListQualifier', 'CF',
                'QuantityUnitOfMeasure', jsonb_build_object('Code', 'C28254')
            ),
            'DaysSupply', 30,
            'WrittenDate', jsonb_build_object('Date', '2019-01-01'),
            'Substitutions', 0,
            'NumberOfRefills', 1,
            'Diagnosis', jsonb_build_object(
                'ClinicalInformationQualifier', 2,
                'Primary', jsonb_build_object(
                    'Code', 'K1233',
                    'Qualifier', 'ABF',
                    'Description', 'Oral mucositis (ulcerative) due to radiation'
                ),
                'Secondary', jsonb_build_object(
                    'Code', 'Z510',
                    'Qualifier', 'ABF',
                    'Description', 'Encounter for antineoplastic radiation therapy'
                )
            ),
            'Note', 'Patient requested peppermint flavoring if possible. Please provide appropriate documentation to patient for how to use this product, including not swallowing solution. A child-resistant package also requested.',
            'Sig', jsonb_build_object(
                'SigText', 'Swish and spit 15 mL orally for 1 minute every 12 hours',
                'CodeSystem', jsonb_build_object(
                    'SNOMEDVersion', 20170131,
                    'FMTVersion', '16.03d'
                ),
                'Instruction', jsonb_build_object(
                    'DoseAdministration', jsonb_build_object(
                        'DoseDeliveryMethod', jsonb_build_object(
                            'Text', 'Swish',
                            'Qualifier', 'SNOMED',
                            'Code', 421805007
                        ),
                        'Dosage', jsonb_build_object(
                            'DoseQuantity', 15,
                            'DoseUnitOfMeasure', jsonb_build_object(
                                'Text', 'mL',
                                'Qualifier', 'DoseUnitOfMeasure',
                                'Code', 'C28254'
                            )
                        ),
                        'RouteOfAdministration', jsonb_build_object(
                            'Text', 'oral route',
                            'Qualifier', 'SNOMED',
                            'Code', 26643006
                        )
                    ),
                    'TimingAndDuration', jsonb_build_object(
                        'Interval', jsonb_build_object(
                            'IntervalNumericValue', 12,
                            'IntervalUnits', jsonb_build_object(
                                'Text', 'hours',
                                'Qualifier', 'SNOMED',
                                'Code', 307467005
                            )
                        ),
                        'TimingClarifyingFreeText', 'for 1 minute every 12 hours'
                    )
                )
            ),
            'CompoundInformation', jsonb_build_object(
                'FinalCompoundPharmaceuticalDosageForm', 'C68996',
                'CompoundIngredientsLotNotUsed', compound_ingredients
            )
        );
    END;
    
    -- Combine all sections into the full message
    v_message_json := jsonb_build_object(
            '?xml', '',
            'Message', jsonb_build_object(
                'Header', v_header_json,
                'Body', jsonb_build_object(
                    'CancelRx', jsonb_build_object(
                        'Patient', v_patient_json,
                        'Pharmacy', v_pharmacy_json,
                        'Prescriber', v_prescriber_json,
                        'MedicationPrescribed', v_medication_json
                    )
                )
            )
    );
    
    RAISE NOTICE 'Message JSON: %', v_message_json;
    -- Update the message ID with the generated value
    v_message_json := jsonb_set(v_message_json, '{Message,Header,MessageID}', to_jsonb(message_id_value));
    
    -- Extract the components for processing
    v_header_json := v_message_json->'Message'->'Header';
    v_patient_json := v_message_json->'Message'->'Body'->'CancelRx'->'Patient';
    v_prescriber_json := v_message_json->'Message'->'Body'->'CancelRx'->'Prescriber';
    v_pharmacy_json := v_message_json->'Message'->'Body'->'CancelRx'->'Pharmacy';

    -- Process the message
    SELECT tp.ss_message_id, tp.error_message INTO v_ss_message_id, error_message
    FROM _parse_and_insert_cancelrx(
        p_ss_log_id := 99999998, -- Fixed log ID for testing
        p_message_json := v_message_json::JSONB,
        p_surescripts_message_type := 'CancelRx'::TEXT,
        p_header_data := v_header_json::JSONB,
        p_patient_data := v_patient_json::JSONB,
        p_prescriber_data := v_prescriber_json::JSONB,
        p_priority_flag := NULL::TEXT 
    ) tp;

    IF error_message IS NOT NULL THEN
        RAISE EXCEPTION 'Parser error: %', error_message;
    END IF;

    RAISE NOTICE 'Processed CancelRx message, ss_message_id: % using Cert_CANCEL-2.json', v_ss_message_id;


    -- Assertions for form_ss_message
    PERFORM assert_not_null(v_ss_message_id::TEXT, 'ss_message_id');

    -- Header fields
    SELECT send_to INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('111111', v_temp_text, 'send_to');

    SELECT sent_from INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('6301875277001', v_temp_text, 'sent_from');

    SELECT message_id INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals(message_id_value, v_temp_text, 'message_id');

    SELECT related_message_id INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('ECHO FROM REFRES', v_temp_text, 'related_message_id');

    SELECT sent_dt::TEXT INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('2019-01-01 13:42:39.7', v_temp_text, 'sent_dt'); -- Assuming UTC storage

    SELECT sender_software_developer INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Surescripts', v_temp_text, 'sender_software_developer');

    SELECT sender_software_product INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Certification Testing', v_temp_text, 'sender_software_product');

    SELECT sender_software_version INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('20170715', v_temp_text, 'sender_software_version');

    SELECT physician_order_id INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_null(v_temp_text, 'physician_order_id (ss_prescriber_order_number)');

    -- Message Type & Direction
    SELECT message_type INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('CancelRx', v_temp_text, 'message_type (ss_message_type)');

    SELECT direction INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('IN', v_temp_text, 'direction (ss_direction)');

    -- Patient fields
    SELECT patient_name_display INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Usumacintacoatzacoalcosniltepecvera, Juancarlosguadalupepaploapan Franciscolisandroculiacan', v_temp_text, 'patient_name_display');

    SELECT patient_first_name INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Juancarlosguadalupepaploapan', v_temp_text, 'patient_first_name');

    SELECT patient_last_name INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Usumacintacoatzacoalcosniltepecvera', v_temp_text, 'patient_last_name');

    SELECT patient_middle_name INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Franciscolisandroculiacan', v_temp_text, 'patient_middle_name');

    SELECT patient_dob::TEXT INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('2004-06-21', v_temp_text, 'patient_dob');

    SELECT patient_gender INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('M', v_temp_text, 'patient_gender');

    SELECT patient_home_street_1 INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('27732 West Alameda Potholeladen Street', v_temp_text, 'patient_home_street_1');

    SELECT patient_home_street_2 INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Apt 425-B', v_temp_text, 'patient_home_street_2');

    SELECT patient_home_city INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Rancho Cucamonga', v_temp_text, 'patient_home_city');

    SELECT patient_home_state INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('CA', v_temp_text, 'patient_home_state');

    SELECT patient_home_zip INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('917011515', v_temp_text, 'patient_home_zip');

    SELECT patient_phone INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('**********', v_temp_text, 'patient_phone');

    -- Prescriber fields
    SELECT prescriber_name_display INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Pimpernel, Marguerite', v_temp_text, 'prescriber_name_display');

    SELECT prescriber_npi INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('222222', v_temp_text, 'prescriber_npi');

    SELECT prescriber_dea INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('*********', v_temp_text, 'prescriber_dea');

    SELECT prescriber_state_lic INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('784577%1147%24', v_temp_text, 'prescriber_state_lic');
    
    SELECT prescriber_medicaid INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('658345', v_temp_text, 'prescriber_medicaid');

    SELECT prescriber_certificate_to_prescribe INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('CTP.CA.1147%24', v_temp_text, 'prescriber_certificate_to_prescribe');

    SELECT prescriber_last_name INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Pimpernel', v_temp_text, 'prescriber_last_name');

    SELECT prescriber_first_name INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Marguerite', v_temp_text, 'prescriber_first_name');
    
    SELECT prescriber_address_1 INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('1425 Mendocino Ave', v_temp_text, 'prescriber_address_1');

    SELECT prescriber_address_2 INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Suite 12-A', v_temp_text, 'prescriber_address_2');
    
    SELECT prescriber_city INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Santa Rosa', v_temp_text, 'prescriber_city');

    SELECT prescriber_state INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('CA', v_temp_text, 'prescriber_state');

    SELECT prescriber_zip INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('95401', v_temp_text, 'prescriber_zip');

    SELECT prescriber_phone INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('**********', v_temp_text, 'prescriber_phone');

    SELECT prescriber_extension INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('4221', v_temp_text, 'prescriber_extension');

    SELECT prescriber_fax INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('7079442121', v_temp_text, 'prescriber_fax');

    SELECT prescriber_loc_name INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('MediStar of California', v_temp_text, 'prescriber_loc_name');

    SELECT prescriber_agent_first_name INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_null(v_temp_text, 'prescriber_agent_first_name');
    
    SELECT prescriber_agent_last_name INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_null(v_temp_text, 'prescriber_agent_last_name');

    -- Medication fields
    SELECT description INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Magic Mouthwash Diphenhydramine 12.5 mg/5 mL, Viscous lidocaine 2%, Maalox 1 part', v_temp_text, 'description');

    SELECT product_code_qualifier_id INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_null(v_temp_text, 'product_code_qualifier_id');
    
    SELECT product_code INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_null(v_temp_text, 'product_code');

    SELECT quantity::TEXT INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('900', v_temp_text, 'quantity');

    SELECT quantity_qualifier_id INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('CF', v_temp_text, 'quantity_qualifier_id');

    SELECT quantity_uom_id INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('C28254', v_temp_text, 'quantity_uom_id');

    SELECT days_supply::TEXT INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('30', v_temp_text, 'days_supply');

    SELECT written_date::TEXT INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('2019-01-01', v_temp_text, 'written_date');
    
    SELECT daw INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('0', v_temp_text, 'daw');

    SELECT refills::TEXT INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('1', v_temp_text, 'refills');

    SELECT note INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Patient requested peppermint flavoring if possible. Please provide appropriate documentation to patient for how to use this product, including not swallowing solution. A child-resistant package also requested.', v_temp_text, 'note');

    SELECT sig INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('Swish and spit 15 mL orally for 1 minute every 12 hours', v_temp_text, 'sig');
    
    SELECT compound_dosage_form_id INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('C68996', v_temp_text, 'compound_dosage_form_id');

    SELECT clinical_info_qualifier INTO v_temp_text FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_equals('2', v_temp_text, 'clinical_info_qualifier');

    -- Status Icons and Show Options
    SELECT status_icons INTO v_temp_text_array FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_text_array_equals(ARRAY['new', 'canceled'], v_temp_text_array, 'status_icons');
    
    SELECT show_options INTO v_temp_text_array FROM form_ss_message WHERE id = v_ss_message_id;
    PERFORM assert_text_array_equals(ARRAY['Practice Location'], v_temp_text_array, 'show_options');

    -- Subform Counts
    SELECT COUNT(*) INTO v_temp_int FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = v_ss_message_id;
    PERFORM assert_equals('2', v_temp_int::TEXT, 'form_ss_diagnosis count');

    SELECT COUNT(*) INTO v_temp_int FROM form_ss_compound c JOIN sf_form_ss_message_to_ss_compound sf ON c.id = sf.form_ss_compound_fk WHERE sf.form_ss_message_fk = v_ss_message_id;
    PERFORM assert_equals('3', v_temp_int::TEXT, 'form_ss_compound count');

    -- Detailed Diagnosis Checks
    FOR v_diag_record IN SELECT type, dx_code, dx_code_qualifier_id, dx_desc FROM form_ss_diagnosis d JOIN sf_form_ss_message_to_ss_diagnosis sf ON d.id = sf.form_ss_diagnosis_fk WHERE sf.form_ss_message_fk = v_ss_message_id ORDER BY type
    LOOP
        IF v_diag_record.type = 'Primary' THEN
            PERFORM assert_equals('K1233', v_diag_record.dx_code, 'Primary Diagnosis dx_code');
            PERFORM assert_equals('ABF', v_diag_record.dx_code_qualifier_id, 'Primary Diagnosis dx_code_qualifier_id');
            PERFORM assert_equals('Oral mucositis (ulcerative) due to radiation', v_diag_record.dx_desc, 'Primary Diagnosis dx_desc');
        ELSIF v_diag_record.type = 'Secondary' THEN
            PERFORM assert_equals('Z510', v_diag_record.dx_code, 'Secondary Diagnosis dx_code');
            PERFORM assert_equals('ABF', v_diag_record.dx_code_qualifier_id, 'Secondary Diagnosis dx_code_qualifier_id');
            PERFORM assert_equals('Encounter for antineoplastic radiation therapy', v_diag_record.dx_desc, 'Secondary Diagnosis dx_desc');
        END IF;
    END LOOP;

    -- Detailed Compound Checks (Order might not be guaranteed, so check by description)
    SELECT description, strength, strength_form_id, strength_uom_id, quantity::TEXT, quantity_uom_id, qual_id, code, fdb_id, dea_schedule_id, compound_no::TEXT
    INTO v_compound_record
    FROM form_ss_compound c
    JOIN sf_form_ss_message_to_ss_compound sfcmpd ON c.id = sfcmpd.form_ss_compound_fk
    WHERE sfcmpd.form_ss_message_fk = v_ss_message_id AND c.description = 'Diphenhydramine 12.5 mg/5 mL';
    PERFORM assert_equals('12.5', v_compound_record.strength, 'Compound 1 strength');
    PERFORM assert_equals('C42986', v_compound_record.strength_form_id, 'Compound 1 strength_form_id');
    PERFORM assert_equals('C91131', v_compound_record.strength_uom_id, 'Compound 1 strength_uom_id');
    PERFORM assert_equals('300', v_compound_record.quantity, 'Compound 1 quantity');
    PERFORM assert_equals('C28254', v_compound_record.quantity_uom_id, 'Compound 1 quantity_uom_id');
    PERFORM assert_null(v_compound_record.qual_id, 'Compound 1 qual_id');
    PERFORM assert_null(v_compound_record.code, 'Compound 1 code');
    PERFORM assert_null(v_compound_record.fdb_id, 'Compound 1 fdb_id');
    PERFORM assert_null(v_compound_record.dea_schedule_id, 'Compound 1 dea_schedule_id');
    PERFORM assert_null(v_compound_record.compound_no, 'Compound 1 compound_no');

    SELECT description, strength, strength_form_id, strength_uom_id, quantity::TEXT, quantity_uom_id
    INTO v_compound_record
    FROM form_ss_compound c
    JOIN sf_form_ss_message_to_ss_compound sfcmpd ON c.id = sfcmpd.form_ss_compound_fk
    WHERE sfcmpd.form_ss_message_fk = v_ss_message_id AND c.description = 'Viscous lidocaine 2%';
    PERFORM assert_equals('2', v_compound_record.strength, 'Compound 2 strength');
    PERFORM assert_equals('C42986', v_compound_record.strength_form_id, 'Compound 2 strength_form_id');
    PERFORM assert_equals('C25613', v_compound_record.strength_uom_id, 'Compound 2 strength_uom_id');
    PERFORM assert_equals('300', v_compound_record.quantity, 'Compound 2 quantity');
    PERFORM assert_equals('C28254', v_compound_record.quantity_uom_id, 'Compound 2 quantity_uom_id');

    SELECT description, strength, strength_form_id, strength_uom_id, quantity::TEXT, quantity_uom_id
    INTO v_compound_record
    FROM form_ss_compound c
    JOIN sf_form_ss_message_to_ss_compound sfcmpd ON c.id = sfcmpd.form_ss_compound_fk
    WHERE sfcmpd.form_ss_message_fk = v_ss_message_id AND c.description = 'Maalox oral suspension';
    PERFORM assert_equals('200/200/20', v_compound_record.strength, 'Compound 3 strength');
    PERFORM assert_equals('C68992', v_compound_record.strength_form_id, 'Compound 3 strength_form_id');
    PERFORM assert_equals('C91131', v_compound_record.strength_uom_id, 'Compound 3 strength_uom_id');
    PERFORM assert_equals('300', v_compound_record.quantity, 'Compound 3 quantity');
    PERFORM assert_equals('C28254', v_compound_record.quantity_uom_id, 'Compound 3 quantity_uom_id');

    RAISE NOTICE 'All CancelRx assertions passed for Cert_CANCEL-2.json!';

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Test failed: %', SQLERRM;
        RAISE;
END;
$$; 