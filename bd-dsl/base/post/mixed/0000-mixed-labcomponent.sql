BEGIN;
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,1,'WBC',NULL,'2015-11-20T18:37:19.000Z',1,NULL,'2015-11-20T18:37:19.000Z','2015-11-20T18:37:19.000Z',NULL,NULL,3.5,'WBC',10.5,NULL,'billion cells/L','White Blood Cell Count','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,2,'RBC',NULL,'2015-11-20T18:39:38.000Z',1,NULL,'2015-11-20T18:39:38.000Z','2015-11-20T18:39:38.000Z',NULL,NULL,3.9,'RBC',5.72,NULL,'trillion cells/L','Red Blood Cell Count','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,3,'Hgb',NULL,'2015-11-20T18:40:26.000Z',1,NULL,'2015-11-20T18:40:26.000Z','2015-11-20T22:09:42.000Z',NULL,NULL,12,'Hgb',17.5,NULL,'grams/dL','Hemoglobin','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,4,'Hct',NULL,'2015-11-20T18:40:59.000Z',1,NULL,'2015-11-20T18:40:59.000Z','2015-11-20T18:40:59.000Z',NULL,NULL,34.9,'Hct',50,NULL,'%','Hematocrit','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,5,'Platelet count',NULL,'2015-11-20T18:43:58.000Z',1,NULL,'2015-11-20T18:43:58.000Z','2015-11-20T18:43:58.000Z',NULL,NULL,150,'Platelet count',450,NULL,'billion/L','Platelet count','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,6,'MCV',NULL,'2015-11-20T18:47:08.000Z',1,NULL,'2015-11-20T18:47:08.000Z','2015-11-20T18:47:08.000Z',NULL,NULL,81.2,'MCV',98.3,NULL,'fL','Mean Corpuscular Volume','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,7,'Leukocytes',NULL,'2015-11-20T18:48:27.000Z',1,NULL,'2015-11-20T18:48:27.000Z','2015-11-20T18:48:27.000Z',NULL,NULL,4,'Leukocytes',11,NULL,'10^9/L','Total Leukocytes','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,8,'Neutrophils',NULL,'2015-11-20T18:48:49.000Z',1,NULL,'2015-11-20T18:48:49.000Z','2015-11-20T18:48:49.000Z',NULL,NULL,2.5,'Neutrophils',7.5,NULL,'10^9/L','Neutrophils','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,9,'Lymphocytes',NULL,'2015-11-20T18:49:03.000Z',1,NULL,'2015-11-20T18:49:03.000Z','2015-11-20T18:49:03.000Z',NULL,NULL,1.5,'Lymphocytes',3.5,NULL,'10^9/L','Lymphocytes','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,10,'Monocytes',NULL,'2015-11-20T18:49:26.000Z',1,NULL,'2015-11-20T18:49:26.000Z','2015-11-20T18:49:26.000Z',NULL,NULL,0.2,'Monocytes',0.8,NULL,'10^9/L','Monocytes','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,11,'Eosinophils',NULL,'2015-11-20T18:50:00.000Z',1,NULL,'2015-11-20T18:50:00.000Z','2015-11-20T18:50:00.000Z',NULL,NULL,0.04,'Eosinophils',0.4,NULL,'10^9/L','Eosinophils','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,12,'Basophils',NULL,'2015-11-20T18:50:16.000Z',1,NULL,'2015-11-20T18:50:16.000Z','2015-11-20T18:50:16.000Z',NULL,NULL,0.01,'Basophils',0.1,NULL,'10^9/L','Basophils','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,13,'MCH',NULL,'2015-11-20T18:52:05.000Z',1,NULL,'2015-11-20T18:52:05.000Z','2015-11-20T18:52:05.000Z',NULL,NULL,27,'MCH',33,NULL,'pg/cell','Mean Corpuscular Hemoglobin','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,14,'MCHC',NULL,'2015-11-20T18:52:22.000Z',1,NULL,'2015-11-20T18:52:22.000Z','2015-11-20T18:52:22.000Z',NULL,NULL,33,'MCHC',36,NULL,'g/dL','Mean Corpuscular Hemoglobin Concentration','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,15,'RDW',NULL,'2015-11-20T18:54:00.000Z',1,NULL,'2015-11-20T18:54:00.000Z','2015-11-20T18:54:00.000Z',NULL,NULL,10.2,'RDW',14.5,NULL,'%','Red Cell Distribution Width','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,16,'MPV',NULL,'2015-11-20T18:54:47.000Z',1,NULL,'2015-11-20T18:54:47.000Z','2015-11-20T18:54:47.000Z',NULL,NULL,7.5,'MPV',11.5,NULL,'fL','Mean Platelet Volume','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,17,'BUN',NULL,'2015-11-20T21:25:17.000Z',1,NULL,'2015-11-20T21:25:17.000Z','2015-11-20T21:25:17.000Z',NULL,NULL,7,'BUN',20,NULL,'mg/dL','Blood urea nitrogen','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,18,'Cr',NULL,'2015-11-20T21:29:04.000Z',1,NULL,'2015-11-20T21:29:04.000Z','2015-11-20T21:29:04.000Z',NULL,NULL,0.6,'Cr',1.2,NULL,'mg/dL','Creatinine','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,19,'Na',NULL,'2015-11-20T21:30:49.000Z',1,NULL,'2015-11-20T21:30:49.000Z','2015-11-20T21:30:49.000Z',NULL,NULL,135,'Na',145,NULL,'mEq/L','Sodium','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,20,'Potassium',NULL,'2015-11-20T21:33:23.000Z',1,NULL,'2015-11-20T21:33:23.000Z','2015-11-20T21:33:23.000Z',NULL,NULL,3.5,'Potassium',5,NULL,'mEq/L','Potassium','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,21,'Magnesium',NULL,'2015-11-20T21:35:12.000Z',1,NULL,'2015-11-20T21:35:12.000Z','2015-11-20T21:35:12.000Z',NULL,NULL,1.7,'Magnesium',2.2,NULL,'mg/dL','Magnesium','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,22,'Phosphorus',NULL,'2015-11-20T21:36:54.000Z',1,NULL,'2015-11-20T21:36:54.000Z','2015-11-20T21:36:54.000Z',NULL,NULL,2.4,'Phosphorus',4.1,NULL,'mg/dL','Phosphorus','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,23,'Calcium',NULL,'2015-11-20T21:39:26.000Z',1,NULL,'2015-11-20T21:39:26.000Z','2015-11-20T21:39:26.000Z',NULL,NULL,8.5,'Calcium',10.2,NULL,'mg/dL','Calcium','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,24,'Chloride',NULL,'2015-11-20T21:40:58.000Z',1,NULL,'2015-11-20T21:40:58.000Z','2015-11-20T21:40:58.000Z',NULL,NULL,96,'Chloride',106,NULL,'mEq/L','Chloride','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,25,'CO2',NULL,'2015-11-20T21:42:26.000Z',1,NULL,'2015-11-20T21:42:26.000Z','2015-11-20T21:42:26.000Z',NULL,NULL,23,'CO2',29,NULL,'mEq/L','Carbon Dioxide','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,26,'ALB',NULL,'2015-11-20T21:44:36.000Z',1,NULL,'2015-11-20T21:44:36.000Z','2015-11-20T21:44:36.000Z',NULL,NULL,3.5,'ALB',5.5,NULL,'g/dL','Albumin','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,27,'PreAlbumin',NULL,'2015-11-20T21:45:38.000Z',1,NULL,'2015-11-20T21:45:38.000Z','2015-11-20T21:45:38.000Z',NULL,NULL,19,'PreAlbumin',38,NULL,'mg/dL','PreAlbumin','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,28,'Glucose',NULL,'2015-11-20T21:48:21.000Z',1,NULL,'2015-11-20T21:48:21.000Z','2015-11-20T21:48:21.000Z',NULL,126,70,'Glucose',100,NULL,'mg/dL','Glucose','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,29,'Triglycerides',NULL,'2015-11-20T21:50:48.000Z',1,NULL,'2015-11-20T21:50:48.000Z','2015-11-20T21:50:48.000Z',NULL,500,0,'Triglycerides',200,NULL,'mg/dL','Triglycerides','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,30,'ALP',NULL,'2015-11-20T21:52:20.000Z',1,NULL,'2015-11-20T21:52:20.000Z','2015-11-20T21:52:20.000Z',NULL,NULL,44,'ALP',147,NULL,'IU/L','Alkaline phosphatase','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,31,'AST',NULL,'2015-11-20T21:54:38.000Z',1,NULL,'2015-11-20T21:54:38.000Z','2015-11-20T21:54:38.000Z',NULL,NULL,10,'AST',40,NULL,'units/L','Aspartate Aminotransferase','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,32,'ALT',NULL,'2015-11-20T21:55:35.000Z',1,NULL,'2015-11-20T21:55:35.000Z','2015-11-20T21:55:35.000Z',NULL,NULL,7,'ALT',56,NULL,'units/L','Alanine Aminotransferase','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,33,'TBIL',NULL,'2015-11-20T21:57:46.000Z',1,NULL,'2015-11-20T21:57:46.000Z','2015-11-20T21:57:46.000Z',NULL,NULL,0.1,'TBIL',0.4,NULL,'mg/dL','Bilirubin','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,34,'DBIL',NULL,'2015-11-20T21:59:56.000Z',1,NULL,'2015-11-20T21:59:56.000Z','2015-11-20T21:59:56.000Z',NULL,NULL,0.2,'DBIL',1.2,NULL,'mg/dL','Direct Bilirubin','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
INSERT INTO form_labcomponent ("archived", "change_type", "change_by", "reviewed_by", "created_by", "id", "auto_name", "deleted", "created_on", "updated_by", "reviewed_on", "change_on", "updated_on", "change_data", "severe_high", "norm_low", "short_name", "norm_high", "severe_low", "unit", "name", "allow_sync", "active", "legacy_data", "envoy_external_id") VALUES (NULL,NULL,NULL,NULL,1,35,'Total Protein',NULL,'2015-11-20T22:02:02.000Z',1,NULL,'2015-11-20T22:02:02.000Z','2015-11-20T22:02:02.000Z',NULL,NULL,6.4,'Total Protein',8.3,NULL,'g/dL','Total Protein','Yes','Yes',NULL,NULL) ON CONFLICT (id) DO UPDATE SET "archived" = EXCLUDED."archived", "change_type" = EXCLUDED."change_type", "change_by" = EXCLUDED."change_by", "reviewed_by" = EXCLUDED."reviewed_by", "created_by" = EXCLUDED."created_by", "auto_name" = EXCLUDED."auto_name", "deleted" = EXCLUDED."deleted", "created_on" = EXCLUDED."created_on", "updated_by" = EXCLUDED."updated_by", "reviewed_on" = EXCLUDED."reviewed_on", "change_on" = EXCLUDED."change_on", "updated_on" = EXCLUDED."updated_on", "change_data" = EXCLUDED."change_data", "severe_high" = EXCLUDED."severe_high", "norm_low" = EXCLUDED."norm_low", "short_name" = EXCLUDED."short_name", "norm_high" = EXCLUDED."norm_high", "severe_low" = EXCLUDED."severe_low", "unit" = EXCLUDED."unit", "name" = EXCLUDED."name", "legacy_data" = EXCLUDED."legacy_data", "envoy_external_id" = EXCLUDED."envoy_external_id" WHERE form_labcomponent.allow_sync = 'Yes';
COMMIT;
