BEGIN;
DELETE FROM form_list_billing_csstatus;
INSERT INTO form_list_billing_csstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'100 - Ready To Print',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'100','Ready To Print',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_csstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_csstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'101 - Ready To Bill',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'101','Ready To Bill',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_csstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_csstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'102 - Resubmit - Resubmit',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'102','Resubmit',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_csstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_csstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'103 - Sent',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'103', 'Sent',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_csstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_csstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'104 - Payor Special Project',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'104','Payor Special Project',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_csstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_csstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'105 - Ready To Transmit',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'105','Ready To Transmit',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_csstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_csstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'106 - Unconfirmed',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'106','Unconfirmed',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_csstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_csstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'107 - Write Off',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'107','Write Off',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_csstatus.allow_sync = 'Yes';

INSERT INTO form_list_billing_csstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'108 - Bill for Denial (Medicare)',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'108','Bill for Denial (Medicare)',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_csstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_csstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'109 - On-Hold for authorization',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'109','On-Hold for authorization',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_csstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_csstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'110 - On-Hold for PAP',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'110','On-Hold for PAP',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_csstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_csstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'111 - On-Hold for Copay Assistance',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'111','On-Hold for Copay Assistance',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_csstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_csstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'112 - On-Hold for CMN',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'112','On-Hold for CMN',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_csstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_csstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'113 - On-Hold for Nursing Visit',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'113','On-Hold for Nursing Visit',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_csstatus.allow_sync = 'Yes';
INSERT INTO form_list_billing_csstatus ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "short_name", "show_in_ar_report", "allow_sync", "active") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'114 - On-Hold for Service Date',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'114','On-Hold for Service Date',NULL,NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "short_name" = EXCLUDED."short_name", "show_in_ar_report" = EXCLUDED."show_in_ar_report" WHERE form_list_billing_csstatus.allow_sync = 'Yes';
COMMIT;
