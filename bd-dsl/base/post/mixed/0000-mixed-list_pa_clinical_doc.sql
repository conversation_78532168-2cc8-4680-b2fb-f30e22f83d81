BEGIN;
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'1 - Office notes',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'1','Office notes','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2 - Labs',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2','Labs','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'3 - Failed tx',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'3','Failed tx','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'4 - Failed systemic tx',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'4','Failed systemic tx','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'5 - Failed biologic tx',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'5','Failed biologic tx','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'6 - Failed topical tx',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'6','Failed topical tx','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'7 - Failed phototherapy',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'7','Failed phototherapy','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'8 - Failed bisphosphonate tx',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'8','Failed bisphosphonate tx','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'9 - Beta-blocker history',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'9','Beta-blocker history','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'10 - Statin history',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'10','Statin history','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'11 - Fracture history',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'11','Fracture history','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'12 - TB test',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'12','TB test','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'13 - ANA results',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'13','ANA results','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'14 - HCV-RNA results',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'14','HCV-RNA results','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'15 - Genotype results',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'15','Genotype results','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (16,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'16 - Hepatitis panel results',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'16','Hepatitis panel results','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (17,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'17 - Fibrosis results',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'17','Fibrosis results','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (18,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'18 - Drug screen results',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'18','Drug screen results','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (19,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'19 - HIV screen results',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'19','HIV screen results','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (20,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'20 - Viral load results',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'20','Viral load results','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (21,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'21 - CD4 count results',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'21','CD4 count results','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (22,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'22 - Baseline lipid results',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'22','Baseline lipid results','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (23,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'23 - Lipid panel results',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'23','Lipid panel results','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (24,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'24 - Direct-LDL results',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'24','Direct-LDL results','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (25,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'25 - Calcium level results',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'25','Calcium level results','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (26,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'26 - BMD T-score results',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'26','BMD T-score results','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (27,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'27 - Dutch Lipid score',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'27','Dutch Lipid score','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (28,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'28 - CDAI score',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'28','CDAI score','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (29,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'29 - NYHA class',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'29','NYHA class','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (30,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'30 - LVEF',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'30','LVEF','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (31,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'31 - Patient signed tx consent form',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'31','Patient signed tx consent form','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (32,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'32 - diagnosis code',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'32','diagnosis code','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (33,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'33 - Notes/labs showing improvement on tx',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'33','Notes/labs showing improvement on tx','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (34,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'34 - New Refill Prescription',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'34','New Refill Prescription','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (35,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'35 - Documentation of the severity of patient''s Atopic Dermatitis (e.g. SCORAD, POEM, EASI)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'35','Documentation of the severity of patient''s Atopic Dermatitis (e.g. SCORAD, POEM, EASI)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (36,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'36 - Hurley Stage',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'36','Hurley Stage','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (37,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'37 - Biopsy Results',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'37','Biopsy Results','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (38,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'38 - Hx/Episodes of Eosinophilic Esophagitis',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'38','Hx/Episodes of Eosinophilic Esophagitis','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
INSERT INTO form_list_pa_clinical_doc ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active") VALUES (39,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'39 - Tried and failed proton pump inhibitors (PPIs)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'39','Tried and failed proton pump inhibitors (PPIs)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name" WHERE form_list_pa_clinical_doc.allow_sync = 'Yes';
COMMIT;
