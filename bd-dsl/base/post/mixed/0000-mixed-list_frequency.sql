BEGIN;
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'1X One Time Only',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'1X',1,'One Time Only','Yes',NULL,'one time only') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'1XW1 Time a Week',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'1XW1',1,'Time a Week','Yes',NULL,'once a week') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2CD/15D2 cons days per 15d',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2CD/15D2',0.93,'cons days per 15d','Yes',NULL,'for 2 consecutive days every 15 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2CD/21D2 cons days per 21d',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2CD/21D2',0.67,'cons days per 21d','Yes',NULL,'for 2 consecutive days every 21 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2CD/28D2 cons days per 28 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2CD/28D2',0.5,'cons days per 28 days','Yes',NULL,'for 2 consecutive days every 28 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2CD/2W 2con days q 2weeks',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2CD/2W',1,'2con days q 2weeks','Yes',NULL,'for 2 consecutive days every 2 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2CD/3W 2cons days per 3w',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2CD/3W',0.67,'2cons days per 3w','Yes',NULL,'for 2 consecutive days every 3 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2D/14D 2 days per 14 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2D/14D',2,'2 days per 14 days','Yes',NULL,'for 2 days every 14 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2D/21D 2 days per 21 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2D/21D',0.67,'2 days per 21 days','Yes',NULL,'for 2 days every 21 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2D/28D 2 days per 28 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2D/28D',2,'2 days per 28 days','Yes',NULL,'for 2 days every 28 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2D/30D 2 days per 30 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2D/30D',0.47,'2 days per 30 days','Yes',NULL,'for 2 days every 30 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2D/6W 2 days per 6 weeks',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2D/6W',0.33,'2 days per 6 weeks','Yes',NULL,'for 2 days every 6 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2D/Q5W daily x2 days every 5wks',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2D/Q5W',2,'daily x2 days every 5wks','Yes',NULL,'for 2 days every 5 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2XM twice monthly',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2XM',0.5,'twice monthly','Yes',NULL,'twice monthly') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2XW 2 times a Week',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2XW',2,'2 times a Week','Yes',NULL,'twice a week') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (16,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'3CD/21D 3 consecutive/21 day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'3CD/21D',1,'3 consecutive/21 day','Yes',NULL,'for 3 consecutive days every 21 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (17,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'3CD/28D 3 cons.day/28 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'3CD/28D',0.75,'3 cons.day/28 days','Yes',NULL,'for 3 consecutive days every 28 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (18,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'3CD/42D 3 days every 6 weeks',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'3CD/42D',0.5,'3 days every 6 weeks','Yes',NULL,'for 3 days every 6 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (19,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'3D/21D 3 days per 21 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'3D/21D',3,'3 days per 21 days','Yes',NULL,'for 3 days every 21 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (20,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'3D/28D 3 days per 28 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'3D/28D',0.25,'3 days per 28 days','Yes',NULL,'for 3 days every 28 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (21,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'3XM 3 times a month',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'3XM',1,'3 times a month','Yes',NULL,'three times a month') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (22,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'3XW 3 Times a Week',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'3XW',3,'3 Times a Week','Yes',NULL,'three times a week') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (23,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'4CD/28D 4 cons days per 28 d',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'4CD/28D',1,'4 cons days per 28 d','Yes',NULL,'for 4 consecutive days every 28 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (24,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'4CD/8W 4 cons days per 8w',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'4CD/8W',0.125,'4 cons days per 8w','Yes',NULL,'for 4 consecutive days every 8 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (25,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'4CD/M 4 cons days per mon',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'4CD/M',0.25,'4 cons days per mon','Yes',NULL,'for 4 consecutive days every month') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (26,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'4D/28D 4 days per 28 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'4D/28D',1,'4 days per 28 days','Yes',NULL,'for 4 days every 28 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (27,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'BID Twice daily',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'BID',14,'Twice daily','Yes',NULL,'twice daily') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (28,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'HS nightly at bedtime',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'HS',7,'nightly at bedtime','Yes',NULL,'nightly at bedtime') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (29,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'over2D over 2 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'over2D',3.5,'over 2 days','Yes',NULL,'over 2 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (30,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'over3D over 3 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'over3D',2.33,'over 3 days','Yes',NULL,'over 3 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (31,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'over4D over 4 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'over4D',1.75,'over 4 days','Yes',NULL,'over 4 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (32,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'over5D over 5 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'over5D',1.4,'over 5 days','Yes',NULL,'over 5 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (33,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'over6D over 6 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'over6D',1.17,'over 6 days','Yes',NULL,'over 6 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (34,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'PRN as needed',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'PRN',1,'as needed','Yes',NULL,'as needed') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (35,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q10D every 10 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q10D',0.7,'every 10 days','Yes',NULL,'every 10 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (36,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q10W every 10 Weeks',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q10W',0.1,'every 10 Weeks','Yes',NULL,'every 10 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (37,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q12H every 12 Hours',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q12H',12,'every 12 Hours','Yes',NULL,'every 12 hours') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (38,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q12W every 12 weeks',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q12W',0.08,'every 12 weeks','Yes',NULL,'every 12 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (39,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q14-17D every 14 to 17 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q14-17D',0.45,'every 14 to 17 days','Yes',NULL,'every 14 to 17 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (40,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q14-19D every 14-19 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q14-19D',0.42,'every 14-19 days','Yes',NULL,'every 14-19 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (41,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q14D every 14 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q14D',0.5,'every 14 days','Yes',NULL,'every 14 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (42,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q15D every 15 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q15D',0.47,'every 15 days','Yes',NULL,'every 15 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (43,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q18D every 18 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q18D',0.39,'every 18 days','Yes',NULL,'every 18 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (44,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q1M every 1 month',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q1M',0.23,'every 1 month','Yes',NULL,'every 1 month') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (45,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q1W weekly',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q1W',1,'weekly','Yes',NULL,'weekly') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (46,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q24H every 24 hours',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q24H',7,'every 24 hours','Yes',NULL,'every 24 hours') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (47,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q28D every 28 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q28D',0.25,'every 28 days','Yes',NULL,'every 28 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (48,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q2M every 2 months',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q2M',0.12,'every 2 months','Yes',NULL,'every 2 months') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (49,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q2W every 2 weeks',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q2W',0.5,'every 2 weeks','Yes',NULL,'every 2 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (50,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q3-4W every 3 to 4 weeks',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q3-4W',0.25,'every 3 to 4 weeks','Yes',NULL,'every 3 to 4 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (51,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q30D every 30 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q30D',0.23,'every 30 days','Yes',NULL,'every 30 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (52,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q3D every 3 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q3D',2.33,'every 3 days','Yes',NULL,'every 3 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (53,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q3H every 3 hours',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q3H',56,'every 3 hours','Yes',NULL,'every 3 hours') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (54,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q3M every 3 months',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q3M',0.08,'every 3 months','Yes',NULL,'every 3 months') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (55,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q3W every 3 weeks',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q3W',0.333,'every 3 weeks','Yes',NULL,'every 3 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (56,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q4-5W every 4-5 weeks',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q4-5W',0.2,'every 4-5 weeks','Yes',NULL,'every 4-5 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (57,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q4H every 4 hours',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q4H',42,'every 4 hours','Yes',NULL,'every 4 hours') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (58,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q4W every 4 weeks',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q4W',0.25,'every 4 weeks','Yes',NULL,'every 4 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (59,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q5D every 5 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q5D',1.4,'every 5 days','Yes',NULL,'every 5 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (60,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q5W every 5 weeks',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q5W',0.2,'every 5 weeks','Yes',NULL,'every 5 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (61,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q6-8W every 6-8 weeks',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q6-8W',0.13,'every 6-8 weeks','Yes',NULL,'every 6-8 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (62,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q6H every 6 hours',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q6H',28,'every 6 hours','Yes',NULL,'every 6 hours') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (63,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q6M once every 6 months',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q6M',0.04,'once every 6 months','Yes',NULL,'once every 6 months') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (64,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q6W every 6 weeks',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q6W',0.167,'every 6 weeks','Yes',NULL,'every 6 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (65,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q7D every 7 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q7D',1,'every 7 days','Yes',NULL,'every 7 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (66,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q8H every 8 hours',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q8H',21,'every 8 hours','Yes',NULL,'every 8 hours') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (67,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q8W every 8 weeks',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q8W',0.13,'every 8 weeks','Yes',NULL,'every 8 weeks') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (68,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'QDAY daily',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'QDAY',7,'daily','Yes',NULL,'daily') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (69,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'QDAYx2D daily for 2 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'QDAYx2D',2,'daily for 2 days','Yes',NULL,'daily for 2 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (70,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'QDAYx3D once daily x 3 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'QDAYx3D',1,'once daily x 3 days','Yes',NULL,'once daily x 3 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (71,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'QDAYx4D daily for 4 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'QDAYx4D',1,'daily for 4 days','Yes',NULL,'daily for 4 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (72,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'QDAYx5D daily for 5 days',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'QDAYx5D',0.714,'daily for 5 days','Yes',NULL,'daily for 5 days') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (73,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'qdx2dq15 daily x2 days every 15 day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'qdx2dq15',2,'daily x2 days every 15 day','Yes',NULL,'daily x2 days every 15 day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (74,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'QHS at bedtime',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'QHS',7,'at bedtime','Yes',NULL,'at bedtime') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (75,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'QID four times daily',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'QID',28,'four times daily','Yes',NULL,'four times daily') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (76,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'QOD every other day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'QOD',4,'every other day','Yes',NULL,'every other day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (77,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'QOW every other week',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'QOW',1,'every other week','Yes',NULL,'every other week') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (78,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'TID 3 times a day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'TID',21,'3 times a day','Yes',NULL,'three times a day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (79,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'UAD use as directed',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'UAD',1,'use as directed','Yes',NULL,'use as directed') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
INSERT INTO form_list_frequency ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "multiplier", "name", "allow_sync", "active", "label_string") VALUES (80,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'UAD ANA use as directed ANA',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'UAD ANA',0.1,'use as directed ANA','Yes',NULL,'use as directed by nurse') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "multiplier" = EXCLUDED."multiplier", "name" = EXCLUDED."name", "label_string" = EXCLUDED."label_string" WHERE form_list_frequency.allow_sync = 'Yes';
COMMIT;
