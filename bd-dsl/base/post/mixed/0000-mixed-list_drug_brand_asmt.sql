BEGIN;
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Simponi Aria Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'simponiaria', 'Simponi Aria Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Simponi Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'simponi', 'Simponi Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Stelara Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'stelara', 'Stelara Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Soliris Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'soliris', 'Soliris Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Rituxan Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'rituxan', 'Rituxan Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Remicade Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'remicade', 'Remicade Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Radicava Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'radicava', 'Radicava Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Orencia Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'orencia', 'Orencia Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Ocrevus Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'ocrevus', 'Ocrevus Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Lemtrada Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'lemtrada', 'Lemtrada Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Krystexxa Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'krystexxa', 'Krystexxa Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Humira Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'humira', 'Humira Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Enbrel Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'enbrel', 'Enbrel Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Dupixent Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'dupixent', 'Dupixent Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Cimzia Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'cimzia', 'Cimzia Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';
INSERT INTO form_list_brand_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (16,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Actemra Initial Assessment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'actemra', 'Actemra Initial Assessment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "sys_period" = EXCLUDED."sys_period" WHERE form_list_brand_asmt.allow_sync = 'Yes';

COMMIT;
