BEGIN;
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Catheter / Access Device Compilation',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Catheter / Access Device Compilation','No','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Dispensing/Supply Error',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Dispensing/Supply Error','No','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'ER Visit',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ER Visit','Yes','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Experienced Side-Effect (Drug Related)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Experienced Side-Effect (Drug Related)','Yes','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Experienced Side-Effect (Not Drug Related)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Experienced Side-Effect (Not Drug Related)','Yes','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Hospitalization',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Hospitalization','Yes','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Medication Error',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Medication Error','No','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Medication/Supplies Issue',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Medication/Supplies Issue','No','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Missed Dose',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Missed Dose','Yes','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'No Response',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'No Response','Yes','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Pharmacy Concern',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Pharmacy Concern','No','Yes','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Pharmacy Personnel Issue',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Pharmacy Personnel Issue','No','Yes','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Potential Adherence Issue',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Potential Adherence Issue','No','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Response Time',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Response Time','No','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (16,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Unscheduled Physician Visit',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Unscheduled Physician Visit','No','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
INSERT INTO form_list_pharmacy_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (17,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Other',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Other','No','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_pharmacy_issue.allow_sync = 'Yes';
COMMIT;
