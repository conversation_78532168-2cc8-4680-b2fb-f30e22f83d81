BEGIN;

INSERT INTO form_list_rental_category ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'PMP - Pump',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'PMP','Pump','["2024-07-06 16:00:23.925985+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_rental_category.allow_sync = 'Yes';
INSERT INTO form_list_rental_category ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'IVP - IV Pole',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'IVP','IV Pole','["2024-07-06 16:00:23.925985+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_rental_category.allow_sync = 'Yes';

COMMIT;
