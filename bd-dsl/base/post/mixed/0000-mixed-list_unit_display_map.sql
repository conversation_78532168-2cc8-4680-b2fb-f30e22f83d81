BEGIN;
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','CK',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'CKs','Yes','Yes','CK') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pad',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pads','Yes','Yes','pad') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mole',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'moles','Yes','Yes','mole') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets/kg','Yes','Yes','tablet/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tab',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tabs','Yes','Yes','tab') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cap',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'caps','Yes','Yes','cap') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','x10e6 EIN/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'x10e6 EINs/day','Yes','Yes','x10e6 EIN/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','oz/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ozs/day','Yes','Yes','oz/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tspn/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tspns/day','Yes','Yes','tspn/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','x 10e9 viable cells',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'x 10e9 viable cells','Yes','Yes','x 10e9 viable cells') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'days','Yes','Yes','day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','wk',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'wks','Yes','Yes','wk') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tsp',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tsps','Yes','Yes','tsp') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','puff',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'puffs','Yes','Yes','puff') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mo',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mos','Yes','Yes','mo') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (16,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','yr',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'yrs','Yes','Yes','yr') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (17,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tbsp',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tbsps','Yes','Yes','tbsp') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (18,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','caplet',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'caplets','Yes','Yes','caplet') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (19,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','piece of gum',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pieces of gum','Yes','Yes','piece of gum') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (20,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','sachet',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sachets','Yes','Yes','sachet') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (21,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pastille',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pastilles','Yes','Yes','pastille') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (22,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pellet',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pellets','Yes','Yes','pellet') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (23,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vaginal insert',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vaginal inserts','Yes','Yes','vaginal insert') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (24,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ophthalmic insert',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ophthalmic inserts','Yes','Yes','ophthalmic insert') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (25,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','buccal tab',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'buccal tabs','Yes','Yes','buccal tab') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (26,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','dose',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'doses','Yes','Yes','dose') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (27,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','lb',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'lbs','Yes','Yes','lb') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (28,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','m2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'m2s','Yes','Yes','m2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (29,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','in2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'in2s','Yes','Yes','in2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (30,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','lesion',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'lesions','Yes','Yes','lesion') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (31,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/m2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/m2','Yes','Yes','gram/m2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (32,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'kgs','Yes','Yes','kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (33,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliunit/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'milliunits/kg','Yes','Yes','milliunit/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (34,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','L',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Ls','Yes','Yes','L') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (35,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/m2/hr',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/m2/hr','Yes','Yes','gram/m2/hr') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (36,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/m2/hr',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/m2/hr','Yes','Yes','unit/m2/hr') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (37,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','irrig',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'irrigs','Yes','Yes','irrig') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (38,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','lozenge',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'lozenges','Yes','Yes','lozenge') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (39,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cm2 of lesion',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cm2s of lesion','Yes','Yes','cm2 of lesion') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (40,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/kg','Yes','Yes','each/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (41,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/mL',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/mL','Yes','Yes','mg/mL') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (42,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','implant',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'implants','Yes','Yes','implant') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (43,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mmu cells/cm2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmu cells/cm2','Yes','Yes','mmu cells/cm2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (44,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL/min',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/min','Yes','Yes','mL/min') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (45,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mcg/m2/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/m2/hour','Yes','Yes','mcg/m2/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (46,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pump',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pumps','Yes','Yes','pump') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (47,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mCi',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mCis','Yes','Yes','mCi') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (48,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','nanogram/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'nanograms/minute','Yes','Yes','nanogram/minute') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (49,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','bar/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'bars/day','Yes','Yes','bar/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (50,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','bar',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'bars','Yes','Yes','bar') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (51,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','drop/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'drops/day','Yes','Yes','drop/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (52,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mcg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/day','Yes','Yes','mcg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (53,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mcg/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/minute','Yes','Yes','mcg/minute') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (54,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mmol/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmols/hour','Yes','Yes','mmol/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (55,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cm/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cms/day','Yes','Yes','cm/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (56,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/L',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/L','Yes','Yes','mg/L') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (57,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','application/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'applications/day','Yes','Yes','application/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (58,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inch/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inches/day','Yes','Yes','inch/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (59,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet or capsule/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets or capsules/day','Yes','Yes','tablet or capsule/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (60,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','applicatorful/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'applicatorfuls/day','Yes','Yes','applicatorful/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (61,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/hour','Yes','Yes','mg/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (62,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/hour','Yes','Yes','mL/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (63,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliunit',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'milliunits','Yes','Yes','milliunit') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (64,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/day','Yes','Yes','mg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (65,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inhalation/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inhalations/day','Yes','Yes','inhalation/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (66,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','spray/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sprays/day','Yes','Yes','spray/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (67,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/day','Yes','Yes','mL/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (68,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','softgel',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'softgels','Yes','Yes','softgel') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (69,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/hour','Yes','Yes','million units/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (70,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg of phenytoin equiv',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs of phenytoin equiv','Yes','Yes','mg of phenytoin equiv') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (71,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/minute','Yes','Yes','unit/minute') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (72,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','insert/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inserts/day','Yes','Yes','insert/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (73,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','insert',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inserts','Yes','Yes','insert') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (74,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs','Yes','Yes','mg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (75,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams','Yes','Yes','gram') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (76,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mmol',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmols','Yes','Yes','mmol') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (77,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','drop',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'drops','Yes','Yes','drop') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (78,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units','Yes','Yes','unit') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (79,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mcg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs','Yes','Yes','mcg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (80,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cm',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cms','Yes','Yes','cm') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (81,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','application',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'applications','Yes','Yes','application') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (82,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inch',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inches','Yes','Yes','inch') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (83,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet or capsule',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets or capsules','Yes','Yes','tablet or capsule') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (84,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','applicatorful',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'applicatorfuls','Yes','Yes','applicatorful') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (85,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inhalation',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inhalations','Yes','Yes','inhalation') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (86,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs','Yes','Yes','mL') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (87,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','packet/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'packets/day','Yes','Yes','packet/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (88,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','package/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'packages/day','Yes','Yes','package/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (89,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/square inch',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/square inch','Yes','Yes','mg/square inch') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (90,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/lesion',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/lesion','Yes','Yes','million units/lesion') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (91,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mmol/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmols/day','Yes','Yes','mmol/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (92,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','sprays',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sprays','Yes','Yes','sprays') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (93,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','package',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'packages','Yes','Yes','package') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (94,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mEq/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs/hour','Yes','Yes','mEq/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (95,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units','Yes','Yes','thousand units') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (96,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','packet',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'packets','Yes','Yes','packet') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (97,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/day','Yes','Yes','gram/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (98,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','suppository',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'suppositories','Yes','Yes','suppository') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (99,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','suppository/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'suppositories/day','Yes','Yes','suppository/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (100,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/day','Yes','Yes','thousand units/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (101,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vaginal ring/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vaginal rings/day','Yes','Yes','vaginal ring/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (102,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gummy',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gummies','Yes','Yes','gummy') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (103,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','patch/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'patches/day','Yes','Yes','patch/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (104,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/hour','Yes','Yes','thousand units/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (105,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/day','Yes','Yes','million units/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (106,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/minute','Yes','Yes','thousand units/minute') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (107,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vaginal ring',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vaginal rings','Yes','Yes','vaginal ring') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (108,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units','Yes','Yes','million units') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (109,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mEq/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs/day','Yes','Yes','mEq/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (110,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','patch',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'patches','Yes','Yes','patch') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (111,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/minute','Yes','Yes','million units/minute') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (112,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/minute','Yes','Yes','mg/minute') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (113,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'minutes','Yes','Yes','minute') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (114,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mEq',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs','Yes','Yes','mEq') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (115,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','spray',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sprays','Yes','Yes','spray') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (116,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliunit/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'milliunits/hour','Yes','Yes','milliunit/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (117,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/hour','Yes','Yes','unit/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (118,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'hours','Yes','Yes','hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (119,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','melt/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'melts/day','Yes','Yes','melt/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (120,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','second',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'seconds','Yes','Yes','second') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (121,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mcg/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/hour','Yes','Yes','mcg/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (122,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','melt',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'melts','Yes','Yes','melt') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (123,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliunit/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'milliunits/minute','Yes','Yes','milliunit/minute') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (124,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/day','Yes','Yes','unit/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (125,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pad/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pads/day','Yes','Yes','pad/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (126,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microunit',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'microunits','Yes','Yes','microunit') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (127,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vial/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vials/day','Yes','Yes','vial/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (128,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vial',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vials','Yes','Yes','vial') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (129,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','strip/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'strips/day','Yes','Yes','strip/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (130,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','nanogram',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'nanograms','Yes','Yes','nanogram') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (131,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','strip',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'strips','Yes','Yes','strip') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (132,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg of phenytoin equiv/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs of phenytoin equiv/kg','Yes','Yes','mg of phenytoin equiv/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (133,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mcg/kg/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/kg/minute','Yes','Yes','mcg/kg/minute') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (134,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/kg','Yes','Yes','unit/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (135,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/kg/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/kg/hour','Yes','Yes','mg/kg/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (136,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mcg/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/kg','Yes','Yes','mcg/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (137,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','milliunit/kg/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'milliunits/kg/hour','Yes','Yes','milliunit/kg/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (138,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/kg/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/kg/minute','Yes','Yes','unit/kg/minute') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (139,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/kg/day','Yes','Yes','unit/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (140,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/kg/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/kg/hour','Yes','Yes','unit/kg/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (141,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/kg','Yes','Yes','gram/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (142,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/kg','Yes','Yes','mL/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (143,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/kg/day','Yes','Yes','gram/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (144,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mcg/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/kg/day','Yes','Yes','mcg/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (145,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mEq/kg/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs/kg/hour','Yes','Yes','mEq/kg/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (146,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet or capsule/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets or capsules/kg','Yes','Yes','tablet or capsule/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (147,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet or capsule/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets or capsules/kg/day','Yes','Yes','tablet or capsule/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (148,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mmol/kg/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmols/kg/hour','Yes','Yes','mmol/kg/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (149,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','nanogram/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'nanograms/kg','Yes','Yes','nanogram/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (150,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mEq/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs/kg','Yes','Yes','mEq/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (151,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/kg','Yes','Yes','million units/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (152,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/kg/day','Yes','Yes','million units/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (153,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mEq/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs/kg/day','Yes','Yes','mEq/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (154,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','nanogram/kg/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'nanograms/kg/minute','Yes','Yes','nanogram/kg/minute') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (155,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/kg/day','Yes','Yes','mg/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (156,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/kg/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/kg/hour','Yes','Yes','thousand units/kg/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (157,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/kg','Yes','Yes','mg/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (158,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/kg/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/kg/hour','Yes','Yes','million units/kg/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (159,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/pound',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/pound','Yes','Yes','mg/pound') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (160,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/kg/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/kg/minute','Yes','Yes','mg/kg/minute') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (161,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','packet/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'packets/kg','Yes','Yes','packet/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (162,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/kg/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/kg/minute','Yes','Yes','million units/kg/minute') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (163,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/kg/day','Yes','Yes','mL/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (164,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/kg/day','Yes','Yes','thousand units/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (165,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/kg/minute',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/kg/minute','Yes','Yes','thousand units/kg/minute') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (166,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/kg','Yes','Yes','thousand units/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (167,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mmol/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmols/kg/day','Yes','Yes','mmol/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (168,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mmol/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmols/kg','Yes','Yes','mmol/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (169,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mcg/kg/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/kg/hour','Yes','Yes','mcg/kg/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (170,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg of phenytoin equiv/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs of phenytoin equiv/kg/day','Yes','Yes','mg of phenytoin equiv/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (171,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mcg/m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/m2/day','Yes','Yes','mcg/m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (172,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/m2/day','Yes','Yes','unit/m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (173,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/m2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/m2','Yes','Yes','mg/m2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (174,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/m2/day','Yes','Yes','gram/m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (175,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/m2/day','Yes','Yes','million units/m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (176,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','unit/m2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/m2','Yes','Yes','unit/m2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (177,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/m2/day','Yes','Yes','mg/m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (178,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/m2/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/m2/hour','Yes','Yes','mg/m2/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (179,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/m2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/m2','Yes','Yes','thousand units/m2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (180,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL/m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/m2/day','Yes','Yes','mL/m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (181,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microunit/m2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'microunits/m2','Yes','Yes','microunit/m2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (182,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL/m2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/m2','Yes','Yes','mL/m2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (183,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/m2/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/m2/hour','Yes','Yes','million units/m2/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (184,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million units/m2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million units/m2','Yes','Yes','million units/m2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (185,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','thousand units/m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'thousand units/m2/day','Yes','Yes','thousand units/m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (186,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mcg/m2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mcgs/m2','Yes','Yes','mcg/m2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (187,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs','Yes','Yes','each') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (188,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/day','Yes','Yes','each/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (189,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/dose',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/dose','Yes','Yes','each/dose') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (190,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/hour','Yes','Yes','each/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (191,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/kg/day','Yes','Yes','each/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (192,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/kg/dose',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/kg/dose','Yes','Yes','each/kg/dose') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (193,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/kg/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/kg/hour','Yes','Yes','each/kg/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (194,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','each/1.73 m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eachs/1.73 m2/day','Yes','Yes','each/1.73 m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (195,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/hour','Yes','Yes','gram/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (196,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gram/kg/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grams/kg/hour','Yes','Yes','gram/kg/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (197,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/dose',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/dose','Yes','Yes','mg/dose') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (198,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/kg/dose',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/kg/dose','Yes','Yes','mg/kg/dose') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (199,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/1.73 m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/1.73 m2/day','Yes','Yes','mg/1.73 m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (200,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL/dose',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/dose','Yes','Yes','mL/dose') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (201,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL/kg/dose',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/kg/dose','Yes','Yes','mL/kg/dose') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (202,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL/kg/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/kg/hour','Yes','Yes','mL/kg/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (203,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL/1.73 m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/1.73 m2/day','Yes','Yes','mL/1.73 m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (204,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','scoop',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'scoops','Yes','Yes','scoop') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (205,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','scoop/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'scoops/day','Yes','Yes','scoop/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (206,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','scoop/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'scoops/kg/day','Yes','Yes','scoop/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (207,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL/cm2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/cm2','Yes','Yes','mL/cm2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (208,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL/cm2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/cm2/day','Yes','Yes','mL/cm2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (209,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/cm2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/cm2','Yes','Yes','mg/cm2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (210,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/cm2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/cm2/day','Yes','Yes','mg/cm2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (211,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','neb',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'nebs','Yes','Yes','neb') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (212,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','towelette',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'towelettes','Yes','Yes','towelette') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (213,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','towelette/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'towelettes/day','Yes','Yes','towelette/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (214,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','wafer',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'wafers','Yes','Yes','wafer') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (215,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','wafer/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'wafers/day','Yes','Yes','wafer/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (216,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg of phenytoin equiv/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs of phenytoin equiv/day','Yes','Yes','mg of phenytoin equiv/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (217,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','scoop/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'scoops/kg','Yes','Yes','scoop/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (218,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vag insert/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vag inserts/day','Yes','Yes','vag insert/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (219,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','lozenge/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'lozenges/day','Yes','Yes','lozenge/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (220,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','device',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'devices','Yes','Yes','device') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (221,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mmu cells',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmu cells','Yes','Yes','mmu cells') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (222,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','m',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ms','Yes','Yes','m') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (223,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mEq/m2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs/m2','Yes','Yes','mEq/m2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (224,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mEq/m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mEqs/m2/day','Yes','Yes','mEq/m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (225,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mCi/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mCis/kg','Yes','Yes','mCi/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (226,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microL',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'microLs','Yes','Yes','microL') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (227,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microL/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'microLs/kg','Yes','Yes','microL/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (228,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL/m2/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/m2/hour','Yes','Yes','mL/m2/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (229,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','micromole',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'micromoles','Yes','Yes','micromole') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (230,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','piece of gum/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pieces of gum/day','Yes','Yes','piece of gum/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (231,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','amp',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'amps','Yes','Yes','amp') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (232,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','g/100 mL',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gs/100 mL','Yes','Yes','g/100 mL') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (233,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/100 mL',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/100 mL','Yes','Yes','mg/100 mL') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (234,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','g/100 g',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gs/100 g','Yes','Yes','g/100 g') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (235,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg/100 g',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs/100 g','Yes','Yes','mg/100 g') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (236,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','microCi',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'microCis','Yes','Yes','microCi') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (237,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ea/kg/min',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eas/kg/min','Yes','Yes','ea/kg/min') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (238,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ea/min',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eas/min','Yes','Yes','ea/min') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (239,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ea/m2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eas/m2','Yes','Yes','ea/m2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (240,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ea/m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eas/m2/day','Yes','Yes','ea/m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (241,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ea/m2/hr',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eas/m2/hr','Yes','Yes','ea/m2/hr') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (242,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inh/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inhs/kg','Yes','Yes','inh/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (243,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inh/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inhs/kg/day','Yes','Yes','inh/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (244,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','L/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Ls/day','Yes','Yes','L/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (245,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','L/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Ls/hour','Yes','Yes','L/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (246,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','L/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Ls/kg','Yes','Yes','L/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (247,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','L/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Ls/kg/day','Yes','Yes','L/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (248,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','L/kg/hour',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Ls/kg/hour','Yes','Yes','L/kg/hour') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (249,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mL/kg/min',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mLs/kg/min','Yes','Yes','mL/kg/min') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (250,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mmu cells/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmu cells/day','Yes','Yes','mmu cells/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (251,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','film',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'films','Yes','Yes','film') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (252,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','film/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'films/day','Yes','Yes','film/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (253,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','sachet/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sachets/day','Yes','Yes','sachet/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (254,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','patch/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'patches/kg/day','Yes','Yes','patch/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (255,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','patch/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'patches/kg','Yes','Yes','patch/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (256,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vial/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vials/kg','Yes','Yes','vial/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (257,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vial/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vials/kg/day','Yes','Yes','vial/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (258,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','packet/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'packets/kg/day','Yes','Yes','packet/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (259,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inch/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inches/kg','Yes','Yes','inch/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (260,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','inch/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'inches/kg/day','Yes','Yes','inch/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (261,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cell',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cells','Yes','Yes','cell') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (262,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','minim',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'minims','Yes','Yes','minim') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (263,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ft',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'fts','Yes','Yes','ft') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (264,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','bead',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'beads','Yes','Yes','bead') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (265,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','marker',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'markers','Yes','Yes','marker') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (266,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mm',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mms','Yes','Yes','mm') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (267,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gauge',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gauges','Yes','Yes','gauge') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (268,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','oz',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ozs','Yes','Yes','oz') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (269,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','yd',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'yds','Yes','Yes','yd') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (270,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cp',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cps','Yes','Yes','cp') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (271,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ng per day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ngs per day','Yes','Yes','ng per day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (272,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','nanogram/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'nanograms/kg/day','Yes','Yes','nanogram/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (273,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ng per hr',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ngs per hr','Yes','Yes','ng per hr') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (274,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vial/m2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vials/m2','Yes','Yes','vial/m2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (275,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vial/m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vials/m2/day','Yes','Yes','vial/m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (276,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets/day','Yes','Yes','tablet/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (277,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','capsule/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'capsules/day','Yes','Yes','capsule/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (278,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cell/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cells/day','Yes','Yes','cell/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (279,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pop',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pops','Yes','Yes','pop') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (280,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','amp/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'amps/day','Yes','Yes','amp/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (281,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vial/hr',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vials/hr','Yes','Yes','vial/hr') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (282,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','L/min',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Ls/min','Yes','Yes','L/min') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (283,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mm2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mm2s','Yes','Yes','mm2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (284,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','drp/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'drps/kg/day','Yes','Yes','drp/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (285,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','drp/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'drps/kg','Yes','Yes','drp/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (286,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cm2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cm2s','Yes','Yes','cm2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (287,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet or capsule/m2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets or capsules/m2','Yes','Yes','tablet or capsule/m2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (288,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet or capsule/m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets or capsules/m2/day','Yes','Yes','tablet or capsule/m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (289,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets/kg/day','Yes','Yes','tablet/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (290,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','capsule/kg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'capsules/kg/day','Yes','Yes','capsule/kg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (291,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet/m2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets/m2','Yes','Yes','tablet/m2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (292,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','capsule/m2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'capsules/m2','Yes','Yes','capsule/m2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (293,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tablet/m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tablets/m2/day','Yes','Yes','tablet/m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (294,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','capsule/m2/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'capsules/m2/day','Yes','Yes','capsule/m2/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (295,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','capsule/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'capsules/kg','Yes','Yes','capsule/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (296,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cells/kg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cells/kg','Yes','Yes','cells/kg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (297,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vg',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vgs','Yes','Yes','vg') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (298,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','vg/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'vgs/day','Yes','Yes','vg/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (299,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pump/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pumps/day','Yes','Yes','pump/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (300,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tube',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tubes','Yes','Yes','tube') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (301,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tube/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tubes/day','Yes','Yes','tube/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (302,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','implant/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'implants/day','Yes','Yes','implant/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (303,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','billion cells/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'billion cells/day','Yes','Yes','billion cells/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (304,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','billion cells',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'billion cells','Yes','Yes','billion cells') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (305,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','troche',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'troches','Yes','Yes','troche') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (306,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','blister',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'blisters','Yes','Yes','blister') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (307,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','kit',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'kits','Yes','Yes','kit') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (308,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','lancet',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'lancets','Yes','Yes','lancet') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (309,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pen needle',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pen needles','Yes','Yes','pen needle') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (310,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','sponge',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sponges','Yes','Yes','sponge') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (311,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','stick',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sticks','Yes','Yes','stick') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (312,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','swab',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'swabs','Yes','Yes','swab') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (313,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tspn',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tspns','Yes','Yes','tspn') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (314,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tbspn',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tbspns','Yes','Yes','tbspn') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (315,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','act',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'acts','Yes','Yes','act') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (316,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','squirt',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'squirts','Yes','Yes','squirt') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (317,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gel',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gels','Yes','Yes','gel') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (318,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gum',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gums','Yes','Yes','gum') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (319,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','piece',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pieces','Yes','Yes','piece') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (320,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','container',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'containers','Yes','Yes','container') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (321,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','respule',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'respules','Yes','Yes','respule') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (322,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','applicator',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'applicators','Yes','Yes','applicator') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (323,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pledget',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pledgets','Yes','Yes','pledget') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (324,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pill',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pills','Yes','Yes','pill') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (325,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pen',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pens','Yes','Yes','pen') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (326,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cartridge',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cartridges','Yes','Yes','cartridge') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (327,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','syringe',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'syringes','Yes','Yes','syringe') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (328,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg fish oil',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs fish oil','Yes','Yes','mg fish oil') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (329,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','mg fish oil/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgs fish oil/day','Yes','Yes','mg fish oil/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (330,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','puff/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'puffs/day','Yes','Yes','puff/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (331,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','actuation/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'actuations/day','Yes','Yes','actuation/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (332,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','applicator/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'applicators/day','Yes','Yes','applicator/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (333,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','cartridge/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cartridges/day','Yes','Yes','cartridge/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (334,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','container/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'containers/day','Yes','Yes','container/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (335,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','device/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'devices/day','Yes','Yes','device/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (336,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gel/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gels/day','Yes','Yes','gel/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (337,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gum/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gums/day','Yes','Yes','gum/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (338,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','gummy/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'gummies/day','Yes','Yes','gummy/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (339,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','lancet/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'lancets/day','Yes','Yes','lancet/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (340,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','lollipop/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'lollipops/day','Yes','Yes','lollipop/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (341,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','nebule/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'nebules/day','Yes','Yes','nebule/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (342,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pen/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pens/day','Yes','Yes','pen/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (343,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','piece/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pieces/day','Yes','Yes','piece/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (344,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pill/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pills/day','Yes','Yes','pill/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (345,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','pledget/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pledgets/day','Yes','Yes','pledget/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (346,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','respule/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'respules/day','Yes','Yes','respule/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (347,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','softgel/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'softgels/day','Yes','Yes','softgel/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (348,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','squirt/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'squirts/day','Yes','Yes','squirt/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (349,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','syringe/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'syringes/day','Yes','Yes','syringe/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (350,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','tbsp/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'tbsps/day','Yes','Yes','tbsp/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (351,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','kit/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'kits/day','Yes','Yes','kit/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (352,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','units/day',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units/day','Yes','Yes','units/day') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (353,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','million cells/cm2',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'million cells/cm2','Yes','Yes','million cells/cm2') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
INSERT INTO form_list_unit_display_map ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "plural_name", "allow_sync", "active", "code") VALUES (354,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','units',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'units','Yes','Yes','units') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "plural_name" = EXCLUDED."plural_name", "code" = EXCLUDED."code" WHERE form_list_unit_display_map.allow_sync = 'Yes';
COMMIT;
