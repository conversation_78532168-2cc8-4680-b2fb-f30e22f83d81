BEGIN;
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'41 - Submit Bill To Other Processor Or Primary Payer',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Submit Bill To Other Processor Or Primary Payer','41',1,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'572 - Medigap ID Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Medigap ID Not Covered','572',2,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'573 - Prescriber Alternate ID Associated State/Province Address Value Not Supported',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Prescriber Alternate ID Associated State/Province Address Value Not Supported','573',3,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'574 - Compound Ingredient Modifier Code Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Compound Ingredient Modifier Code Not Covered','574',4,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'583 - Provider ID Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Provider ID Not Covered','583',5,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'584 - Purchaser ID Associated State/Province Code Value Not Supported',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Purchaser ID Associated State/Province Code Value Not Supported','584',6,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'587 - Carrier ID Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Carrier ID Not Covered','587',7,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'589 - Patient ID Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Patient ID Not Covered','589',8,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'590 - Compound Dosage Form Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Compound Dosage Form Not Covered','590',9,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'591 - Plan ID Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Plan ID Not Covered','591',10,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'594 - Pay To ID Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Pay To ID Not Covered','594',11,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'595 - Associated Prescription/Service Provider ID Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Associated Prescription/Service Provider ID Not Covered','595',12,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'6G - Coordination Of Benefits/Other Payments Segment Required For Adjudication',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Coordination Of Benefits/Other Payments Segment Required For Adjudication','6G',13,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'60 - Product/Service Not Covered For Patient Age',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Product/Service Not Covered For Patient Age','60',14,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'61 - Product/Service Not Covered For Patient Gender',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Product/Service Not Covered For Patient Gender','61',15,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (16,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'63 - Product/Service ID Not Covered For Institutionalized Patient',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Product/Service ID Not Covered For Institutionalized Patient','63',16,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (17,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'65 - Patient Is Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Patient Is Not Covered','65',17,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (18,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'66 - Patient Age Exceeds Maximum Age',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Patient Age Exceeds Maximum Age','66',18,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (19,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'67 - Date Of Service Before Coverage Effective',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Date Of Service Before Coverage Effective','67',19,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (20,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'68 - Date Of Service After Coverage Expired',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Date Of Service After Coverage Expired','68',20,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (21,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'69 - Date Of Service After Coverage Terminated',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Date Of Service After Coverage Terminated','69',21,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (22,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'600 - Coverage Outside Submitted Date Of Service',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Coverage Outside Submitted Date Of Service','600',22,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (23,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'610 - Information Reporting Transaction (N1/N3) Matched To Reversed Or Rejected Claim Submitted Under Part D IIN PCN',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Information Reporting Transaction (N1/N3) Matched To Reversed Or Rejected Claim Submitted Under Part D IIN PCN','610',23,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (24,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'611 - Information Reporting Transaction (N1/N3) Was Matched To A Claim Submitted Under The Part D IIN/PCN Paid As Enhanced Or OTC Or By A Benefit Other Than Part D',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Information Reporting Transaction (N1/N3) Was Matched To A Claim Submitted Under The Part D IIN/PCN Paid As Enhanced Or OTC Or By A Benefit Other Than Part D','611',24,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (25,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'620 - This Product/Service May Be Covered Under Medicare Part D',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'This Product/Service May Be Covered Under Medicare Part D','620',25,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (26,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'621 - This Medicaid Patient Is Medicare Eligible',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'This Medicaid Patient Is Medicare Eligible','621',26,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (27,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'645 - Repackaged Product Is Not Covered By The Contract',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Repackaged Product Is Not Covered By The Contract','645',27,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (28,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'646 - Patient Not Eligible Due To Non Payment Of Premium. Patient To Contact Plan',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Patient Not Eligible Due To Non Payment Of Premium. Patient To Contact Plan','646',28,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (29,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'7X - Days Supply Exceeds Plan Limitation',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Days Supply Exceeds Plan Limitation','7X',29,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (30,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'7Y - Compounds Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Compounds Not Covered','7Y',30,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (31,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'70 - Product/Service Not Covered - Plan/Benefit Exclusion',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Product/Service Not Covered - Plan/Benefit Exclusion','70',31,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (32,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'73 - Additional Fills Are Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Additional Fills Are Not Covered','73',32,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (33,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'74 - Other Carrier Payment Meets Or Exceeds Payable',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Other Carrier Payment Meets Or Exceeds Payable','74',33,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (34,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'76 - Plan Limitations Exceeded',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Plan Limitations Exceeded','76',34,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (35,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'78 - Cost Exceeds Maximum',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Cost Exceeds Maximum','78',35,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (36,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'79 - Fill Too Soon',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Fill Too Soon','79',36,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (37,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'8H - Product/Service Only Covered On Compound Claim',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Product/Service Only Covered On Compound Claim','8H',37,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (38,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'8J - Incorrect Product/Service ID For Processor/Payer',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Incorrect Product/Service ID For Processor/Payer','8J',38,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (39,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'8Q - Excessive Refills Authorized',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Excessive Refills Authorized','8Q',39,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (40,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'8X - Collection From Cardholder Not Allowed',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Collection From Cardholder Not Allowed','8X',40,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (41,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'8Y - Excessive Amount Collected',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Excessive Amount Collected','8Y',41,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (42,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'80 - Diagnosis Code Submitted Does Not Meet Drug Coverage Criteria',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Diagnosis Code Submitted Does Not Meet Drug Coverage Criteria','80',42,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (43,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'81 - Claim Too Old',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Claim Too Old','81',43,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (44,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'82 - Claim Is Post-Dated',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Claim Is Post-Dated','82',44,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (45,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'85 - Claim Not Processed',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Claim Not Processed','85',45,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (46,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'9G - Quantity Dispensed Exceeds Maximum Allowed',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Quantity Dispensed Exceeds Maximum Allowed','9G',46,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (47,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'9N - Compound Ingredient Quantity Exceeds Maximum Allowed',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Compound Ingredient Quantity Exceeds Maximum Allowed','9N',47,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (48,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'9Q - Route Of Administration Submitted Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Route Of Administration Submitted Not Covered','9Q',48,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (49,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'9R - Prescription/Service Reference Number Qualifier Submitted Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Prescription/Service Reference Number Qualifier Submitted Not Covered','9R',49,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (50,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'9S - Future Associated Prescription/Service Date Not Allowed',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Future Associated Prescription/Service Date Not Allowed','9S',50,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (51,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'9U - Provider ID Qualifier Submitted Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Provider ID Qualifier Submitted Not Covered','9U',51,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (52,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'9X - Coupon Type Submitted Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Coupon Type Submitted Not Covered','9X',52,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (53,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'9Y - Compound Product ID Qualifier Submitted Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Compound Product ID Qualifier Submitted Not Covered','9Y',53,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (54,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'AC - Product Not Covered Non-Participating Manufacturer',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Product Not Covered Non-Participating Manufacturer','AC',54,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (55,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'AE - QMB (Qualified Medicare Beneficiary)-Bill Medicare',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'QMB (Qualified Medicare Beneficiary)-Bill Medicare','AE',55,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (56,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'AF - Patient Enrolled Under Managed Care',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Patient Enrolled Under Managed Care','AF',56,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (57,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'AG - Days Supply Limitation For Product/Service',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Days Supply Limitation For Product/Service','AG',57,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (58,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'AJ - Generic Drug Required',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Generic Drug Required','AJ',58,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (59,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'A5 - Not Covered Under Part D Law',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Not Covered Under Part D Law','A5',59,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (60,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'A6 - This Product/Service May Be Covered Under Medicare Part B',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'This Product/Service May Be Covered Under Medicare Part B','A6',60,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (61,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'BA - Compound Basis Of Cost Determination Submitted Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Compound Basis Of Cost Determination Submitted Not Covered','BA',61,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (62,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'BB - Diagnosis Code Qualifier Submitted Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Diagnosis Code Qualifier Submitted Not Covered','BB',62,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (63,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'M1 - Patient Not Covered In This Aid Category',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Patient Not Covered In This Aid Category','M1',63,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (64,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'M2 - Recipient Locked In',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Recipient Locked In','M2',64,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (65,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'MP - Other Payer Cardholder ID Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Other Payer Cardholder ID Not Covered','MP',65,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (66,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'MR - Product Not On Formulary',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Product Not On Formulary','MR',66,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (67,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'PA - PA Exhausted/Not Renewable',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'PA Exhausted/Not Renewable','PA',67,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (68,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'PW - Employer ID Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Employer ID Not Covered','PW',68,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (69,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'PX - Other Payer ID Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Other Payer ID Not Covered','PX',69,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (70,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'P5 - Coupon Expired',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Coupon Expired','P5',70,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (71,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'RL - Transitional Benefit/Resubmit Claim',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Transitional Benefit/Resubmit Claim','RL',71,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (72,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'RN - Plan Limits Exceeded On Intended Partial Fill Field Limitations',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Plan Limits Exceeded On Intended Partial Fill Field Limitations','RN',72,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (73,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'TQ - Dosage Exceeds Product Labeling Limit',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Dosage Exceeds Product Labeling Limit','TQ',73,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (74,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'UU - DAW 0 Cannot Be Submitted On A Multi-source Drug With Available Generics',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'DAW 0 Cannot Be Submitted On A Multi-source Drug With Available Generics','UU',74,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (75,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'A3 - This Product May Be Covered Under Hospice - Medicare A',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'This Product May Be Covered Under Hospice - Medicare A','A3',75,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (76,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'A4 - This Product May Be Covered Under The Medicare- B Bundled Payment To An ESRD Dialysis Facility',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'This Product May Be Covered Under The Medicare- B Bundled Payment To An ESRD Dialysis Facility','A4',76,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (77,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'665 - REMS - Excessive Days Supply',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'REMS - Excessive Days Supply','665',77,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (78,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'666 - REMS - Insufficient Days Supply',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'REMS - Insufficient Days Supply','666',78,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (79,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'667 - REMS - Excessive Dosage',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'REMS - Excessive Dosage','667',79,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (80,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'668 - REMS - Insufficient Dosage',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'REMS - Insufficient Dosage','668',80,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (81,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'669 - REMS - Additional Fills Not Permitted',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'REMS - Additional Fills Not Permitted','669',81,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (82,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'687 - REMS - Administrator Denied',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'REMS - Administrator Denied','687',82,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (83,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'688 - REMS - Service Billing Denied',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'REMS - Service Billing Denied','688',83,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (84,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'689 - PDMP - Administrator Denied',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'PDMP - Administrator Denied','689',84,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (85,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'741 - Exceeds Range Start Limitations',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Exceeds Range Start Limitations','741',85,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (86,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'742 - Exceeds Range End Limitations',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Exceeds Range End Limitations','742',86,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (87,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'766 - Discrepancy Amount In Excess Of Claimed Amount',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Discrepancy Amount In Excess Of Claimed Amount','766',87,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (88,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'774 - Prescriber Medicare Enrollment Period Is Outside Of Claim Date Of Service',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Prescriber Medicare Enrollment Period Is Outside Of Claim Date Of Service','774',88,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (89,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'822 - Drug Is Unrelated To The Terminal Illness And/Or Related Conditions. Not Covered Under Hospice.',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Drug Is Unrelated To The Terminal Illness And/Or Related Conditions. Not Covered Under Hospice.','822',89,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (90,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'823 - Drug Is Beneficiary''s Liability - Not Covered By Hospice Or Part D. Hospice Non-Formulary. Check Other Coverage.',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Drug Is Beneficiary''s Liability - Not Covered By Hospice Or Part D. Hospice Non-Formulary. Check Other Coverage.','823',90,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (91,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'816 - Pharmacy Benefit Exclusion May Be Covered Under Patient''s Medical Benefit',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Pharmacy Benefit Exclusion May Be Covered Under Patient''s Medical Benefit','816',91,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (92,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'817 - Pharmacy Benefit Exclusion Covered Under Patient''s Medical Benefit',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Pharmacy Benefit Exclusion Covered Under Patient''s Medical Benefit','817',92,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (93,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'818 - Medication Administration Not Covered Plan Benefit Exclusion',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Medication Administration Not Covered Plan Benefit Exclusion','818',93,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (94,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'819 - Plan Enrollment File Indicates Medicare As Primary Coverage',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Plan Enrollment File Indicates Medicare As Primary Coverage','819',94,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (95,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'794 - Deductible Over Accumulated',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Deductible Over Accumulated','794',95,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (96,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'795 - Out Of Pocket Over Accumulated',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Out Of Pocket Over Accumulated','795',96,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (97,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'796 - Maximum Benefit Amount (CAP) Over Accumulated',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Maximum Benefit Amount (CAP) Over Accumulated','796',97,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (98,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'798 - SA Over Accumulated',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'SA Over Accumulated','798',98,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (99,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'799 - LTC Over Accumulated',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'LTC Over Accumulated','799',99,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (100,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'800 - RXC Over Accumulated',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'RXC Over Accumulated','800',100,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (101,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'829 - Pharmacy Must Notify Beneficiary',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Pharmacy Must Notify Beneficiary','829',101,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (102,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'84 - Claim Has Not Been Paid/Captured',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Claim Has Not Been Paid/Captured','84',102,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (103,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'833 - Accumulator Year Is Not Within ATBT Timeframe',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Accumulator Year Is Not Within ATBT Timeframe','833',103,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (104,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'831 - Product Service ID Carve-Out Bill Medicaid Fee For Service',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Product Service ID Carve-Out Bill Medicaid Fee For Service','831',104,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (105,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'ZA - The Coordination Of Benefits/Other Payments Segment Is Mandatory To A Downstream Payer',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'The Coordination Of Benefits/Other Payments Segment Is Mandatory To A Downstream Payer','ZA',105,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (106,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'876 - Prescriptive Authority Restrictions Apply Criteria Not Met',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Prescriptive Authority Restrictions Apply Criteria Not Met','876',106,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (107,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'887 - A Previous Payer(s) Is An Excluded Federal Health Care Program Copay Assistance Is Not Allowed',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'A Previous Payer(s) Is An Excluded Federal Health Care Program Copay Assistance Is Not Allowed','887',107,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (108,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'888 - Beneficiary Is Enrolled In Excluded Federal Health Care Program',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Beneficiary Is Enrolled In Excluded Federal Health Care Program','888',108,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (109,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'891 - Days Supply Is Less Than Plan Minimum',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Days Supply Is Less Than Plan Minimum','891',109,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (110,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'895 - Allowed Number of Overrides Exhausted',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Allowed Number of Overrides Exhausted','895',110,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (111,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'896 - Other Adjudicated Program Type Of Unknown Is Not Covered',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'Other Adjudicated Program Type Of Unknown Is Not Covered','896',111,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (112,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'804 - M/I Amount Attributed To Product Selection/Brand',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'M/I Amount Attributed To Product Selection/Brand','804',112,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (113,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'805 - M/I Amount Attributed To Sales Tax',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'M/I Amount Attributed To Sales Tax','805',113,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
INSERT INTO form_list_ncpdp_resp_cob_trk ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "rank", "allow_sync", "active") VALUES (114,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'806 - M/I Amount Attributed To Process Fee',NULL,NULL,'2024-05-29T18:13:27.000Z',NULL,'M/I Amount Attributed To Process Fee','806',114,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "rank" = EXCLUDED."rank" WHERE form_list_ncpdp_resp_cob_trk.allow_sync = 'Yes';
COMMIT;
