BEGIN;
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Adult Growth Hormone Deficiency Assessment (AGHDA)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'aghda','Adult Growth Hormone Deficiency Assessment (AGHDA)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ALS C.A.R.E. (ALSFRS-R)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'alsfrs','ALS C.A.R.E. (ALSFRS-R)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Bath Ankylosing Spondylitis Disease Activity Index (BASDAI)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'basdai','Bath Ankylosing Spondylitis Disease Activity Index (BASDAI)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Braden Scale',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'braden','Braden Scale','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','CIDP Difficulties Survey',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'cidp','CIDP Difficulties Survey','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Dermatology Life Quality Index (DLQI)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'dlqi','Dermatology Life Quality Index (DLQI)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Expanded Disability Status Scale (EDSS)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'edss','Expanded Disability Status Scale (EDSS)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Epworth Sleepiness Scale (ESS)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'epworth','Epworth Sleepiness Scale (ESS)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Grip Strength Measurements',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'grip_strength','Grip Strength Measurements','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','HIV/AIDS-targeted Quality of Life (HAT-QOL)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'hat_qol','HIV/AIDS-targeted Quality of Life (HAT-QOL)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Harvey-Bradshaw Index',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'hbi','Harvey-Bradshaw Index','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Heart Failure Quality Of Life',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'hfqol','Heart Failure Quality Of Life','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Headache Management Questionnaire',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'hmq','Headache Management Questionnaire','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Health and Work Performance Questionnaire (HPQ)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'hpq','Health and Work Performance Questionnaire (HPQ)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','INCAT',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'incat','INCAT','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (16,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Modified Fatigue Impact Scale (MFIS-5)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mfis5','Modified Fatigue Impact Scale (MFIS-5)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (17,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Myasthenia Gravis Activities of Daily Living (MG-ADL)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mgadl','Myasthenia Gravis Activities of Daily Living (MG-ADL)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (18,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Modified Health Assessment Questionnaire (mHAQ)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mhaq','Modified Health Assessment Questionnaire (mHAQ)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (19,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Morisky Medication Adherence (MMAS-8)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmas8','Morisky Medication Adherence (MMAS-8)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (20,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Multifocal Motor Neuropathy',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mmn','Multifocal Motor Neuropathy','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (21,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Mini-Osteoporosis Quality of Life Questionnaire (mini-OQLQ)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'moqlq','Mini-Osteoporosis Quality of Life Questionnaire (mini-OQLQ)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (22,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Multiple Sclerosis Difficulties',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ms','Multiple Sclerosis Difficulties','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (23,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Myasthenia Gravis Difficulties',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'mygrav','Myasthenia Gravis Difficulties','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (24,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Myositis Difficulties',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'myositis','Myositis Difficulties','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (25,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','PID Quality Of Life (PADQOL-16)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'padqol','PID Quality Of Life (PADQOL-16)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (26,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Patient Health/Depression Questionnaire (PHQ-9)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'phq','Patient Health/Depression Questionnaire (PHQ-9)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (27,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Patient Activity Scale-II (PAS-II)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pas2','Patient Activity Scale-II (PAS-II)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (28,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','MOS Pain Effects Scale (PES)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pes','MOS Pain Effects Scale (PES)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (29,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Patient-Oriented Eczema Measure (POEM)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'poem','Patient-Oriented Eczema Measure (POEM)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (30,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Questions on Life Satisfaction – Hypopituitarism (QLS-H)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'qlsh','Questions on Life Satisfaction – Hypopituitarism (QLS-H)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (31,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Rheumatoid Arthritis Disease Activity Index (RADAI)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'radai','Rheumatoid Arthritis Disease Activity Index (RADAI)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (32,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','RODS',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'rods','RODS','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (33,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Quality of Life (SF12)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sf12v2','Quality of Life (SF12)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (34,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Short Inflammatory Bowel Disease Questionnaire (SIBDQ)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'sibdq','Short Inflammatory Bowel Disease Questionnaire (SIBDQ)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (35,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Stiff Person Syndrome',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'stiffps','Stiff Person Syndrome','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (36,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Withdrawal Assessment Tool',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'wat','Withdrawal Assessment Tool','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (37,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Patient Clinical Wellness (IV)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'wellness_iv','Patient Clinical Wellness (IV)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (38,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Clinical Wellness (Self Injectable)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'wellness_si','Clinical Wellness (Self Injectable)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (39,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Factor Quality of Life',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'qol_factor','Factor Quality of Life','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (40,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Pediatric Quality of Life',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'qol_peds','Pediatric Quality of Life','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (41,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','rarevoice_survey',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'rarevoice_survey','Rarevoice Survey','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "description", "allow_sync", "active") VALUES (42,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','outcomes',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'outcomes','Clinical Outcomes','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "description" = EXCLUDED."description" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
COMMIT;
