BEGIN;
INSERT INTO form_list_drug_category ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "sys_period", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Raw Drug',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'Raw Drug','["2024-10-02 14:30:52.322443+00",)','Yes',NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "sys_period" = EXCLUDED."sys_period" WHERE form_list_drug_category.allow_sync = 'Yes';
INSERT INTO form_list_drug_category ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "sys_period", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'IV Fluid',NULL,NULL,'2024-03-19T23:14:43.000Z',NULL,'IV Fluid','["2024-10-02 14:30:52.322443+00",)','Yes',NULL) ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "sys_period" = EXCLUDED."sys_period" WHERE form_list_drug_category.allow_sync = 'Yes';
INSERT INTO form_list_drug_category ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "sys_period", "allow_sync", "active") 
VALUES 
(3, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Adaptors', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Adaptors', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(4, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Alcohol / Betadine', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Alcohol / Betadine', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(5, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Caps', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Caps', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(6, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'CMPD Bags', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'CMPD Bags', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(7, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Containers', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Containers', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(8, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Disinfectant', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Disinfectant', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(9, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Document', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Document', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(10, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Dressing Supplies', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Dressing Supplies', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(11, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Elastomeric', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Elastomeric', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(12, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Extension Set', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Extension Set', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(13, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Flush', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Flush', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(14, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Free Drug', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Free Drug', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(15, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Gloves', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Gloves', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(16, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'IV Bag', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'IV Bag', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(17, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'IV Catheters', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'IV Catheters', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(18, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'IV Filters', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'IV Filters', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(19, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Miscellaneous', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Miscellaneous', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(20, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'NIOSH 1', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'NIOSH 1', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(21, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'NIOSH 2', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'NIOSH 2', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(22, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'NIOSH 3', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'NIOSH 3', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(23, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Pre-Medication', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Pre-Medication', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(24, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Pump Equipment', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Pump Equipment', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(25, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Pumps', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Pumps', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(26, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Rituxan IV - FREE D', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Rituxan IV - FREE D', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(27, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Spike', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Spike', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(28, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Supply', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Supply', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(29, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Syringes/Needles', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Syringes/Needles', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(30, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Tape', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Tape', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(31, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Tubing', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Tubing', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(32, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Vacutainer', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Vacutainer', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(33, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'IV Fluids', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'IV Fluids', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL),
(34, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 'Syringes / Needles', NULL, NULL, '2024-03-19T23:14:43.000Z', NULL, 'Syringes / Needles', '["2024-10-02 14:30:52.322443+00",)', 'Yes', NULL)
ON CONFLICT (id) DO UPDATE SET 
    "created_by" = EXCLUDED."created_by",
    "change_by" = EXCLUDED."change_by",
    "updated_by" = EXCLUDED."updated_by",
    "reviewed_by" = EXCLUDED."reviewed_by",
    "reviewed_on" = EXCLUDED."reviewed_on",
    "deleted" = EXCLUDED."deleted",
    "archived" = EXCLUDED."archived",
    "updated_on" = EXCLUDED."updated_on",
    "auto_name" = EXCLUDED."auto_name",
    "change_type" = EXCLUDED."change_type",
    "change_data" = EXCLUDED."change_data",
    "created_on" = EXCLUDED."created_on",
    "change_on" = EXCLUDED."change_on",
    "code" = EXCLUDED."code",
    "sys_period" = EXCLUDED."sys_period"
WHERE form_list_drug_category.allow_sync = 'Yes';
COMMIT;
