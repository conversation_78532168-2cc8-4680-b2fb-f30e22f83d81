BEGIN;
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (1,1,NULL,1,NULL,3,NULL,NULL,NULL,'2024-10-11T12:06:06.000Z','Acess to Patient navbar',NULL,NULL,'2024-02-05T13:40:37.000Z',NULL,'Rule','Acess to Patient navbar','Patient Navbar','/patient',NULL,NULL,'Yes',NULL,NULL,NULL,NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (2,1,NULL,1,NULL,2,NULL,NULL,NULL,'2024-02-05T17:14:51.000Z','Acess to Sales navbar',NULL,NULL,'2024-02-05T13:43:57.000Z',NULL,'Rule','Acess to Sales navbar','Sales Navbar','/sales',NULL,NULL,'Yes',NULL,NULL,NULL,NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (3,1,NULL,1,NULL,1,NULL,NULL,NULL,'2024-02-05T17:14:40.000Z','Access to Queue Navbar',NULL,NULL,'2024-02-05T13:45:01.000Z',NULL,'Rule','Access to Queue Navbar','Queue Navbar','/queue',NULL,NULL,'Yes',NULL,NULL,NULL,NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (4,1,NULL,1,NULL,4,NULL,NULL,NULL,NULL,'Access to Schedule Navbar',NULL,NULL,'2024-02-05T13:45:51.000Z',NULL,'Rule','Access to Schedule Navbar','Schedule Navbar','/schedule',NULL,NULL,'Yes',NULL,NULL,NULL,NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (5,1,NULL,1,NULL,9,NULL,NULL,NULL,'2024-02-05T14:23:37.000Z','Access To Analytics',NULL,NULL,'2024-02-05T13:46:32.000Z',NULL,'Rule','Access To Analytics','Analytics Navbar','/analytics',NULL,NULL,'Yes',NULL,NULL,NULL,NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (6,1,NULL,1,NULL,11,NULL,NULL,NULL,'2024-02-05T14:24:10.000Z','Access To Referral',NULL,NULL,'2024-02-05T14:23:57.000Z',NULL,'Rule','Access To Referral','Referral Navbar',NULL,NULL,NULL,'Yes',NULL,NULL,NULL,NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (7,1,NULL,1,NULL,3,NULL,NULL,NULL,'2024-02-05T17:20:48.000Z','Access snapshot initial',NULL,NULL,'2024-02-05T17:18:06.000Z',NULL,'Rule','Access snapshot initial','Snapshot initial','patient/*/snap/initial',NULL,NULL,'Yes',NULL,NULL,NULL,NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (8,1,NULL,1,NULL,3,NULL,NULL,NULL,NULL,'Access snapshot ongoing',NULL,NULL,'2024-02-05T17:20:54.000Z',NULL,'Rule','Access snapshot ongoing','Snapshot ongoing','patient/*/snap/ongoing',NULL,NULL,'Yes',NULL,NULL,NULL,NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (9,1,NULL,1,NULL,3,NULL,NULL,NULL,'2024-02-06T15:34:34.000Z','Access snapshot patient_external_order',NULL,NULL,'2024-02-05T17:21:58.000Z',NULL,'Rule','Access snapshot patient_external_order','Snapshot Patient External Order','patient/*/snap/patient_external_order',NULL,NULL,'Yes',NULL,NULL,NULL,NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (10,1,NULL,1,NULL,3,NULL,NULL,NULL,NULL,'Access snapshot careplan',NULL,NULL,'2024-02-05T17:22:48.000Z',NULL,'Rule','Access snapshot careplan','Snapshot careplan','/patient/*/snap/careplan',NULL,NULL,'Yes',NULL,NULL,NULL,NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (11,1,NULL,1,NULL,3,NULL,NULL,NULL,NULL,'Access complaint',NULL,NULL,'2024-02-05T17:23:23.000Z',NULL,'Rule','Access complaint','Snapshot complaint','/patient/*/snap/complaint',NULL,NULL,'Yes',NULL,NULL,NULL,NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (12,1,NULL,1,NULL,3,NULL,NULL,NULL,NULL,'Access snapshot encounter',NULL,NULL,'2024-02-05T17:25:18.000Z',NULL,'Rule','Access snapshot encounter','Snapshot encounter','/patient/*/snap/encounter',NULL,NULL,'Yes',NULL,NULL,NULL,NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (13,1,NULL,1,NULL,3,NULL,NULL,NULL,'2024-02-29T17:24:35.000Z','Access snapshot selfreport',NULL,NULL,'2024-02-05T17:26:03.000Z',NULL,'Rule','Access snapshot selfreport','Snapshot selfreport','/patient/*/snap/selfreport','',NULL,'Yes','',NULL,NULL,NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (14,1,NULL,1,NULL,3,NULL,NULL,NULL,'2024-10-14T15:59:52.000Z','filter_user_patients_by_physician',NULL,NULL,'2024-02-06T17:40:42.000Z',NULL,'RLS Access','filter_user_patients_by_physician','filter_user_patients_by_physician',NULL,'id','patient','No',NULL,'SELECT DISTINCT pn.id AS id
                FROM form_patient pn
                JOIN form_intake it ON it.patient_id = pn.id
                JOIN form_physician ph ON ph.id = it.referrer_name
                JOIN form_user fu ON fu.id = ph.user_id
                WHERE fu.role = ''physician'' AND fu.id = $1','No',NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (15,1,NULL,1,NULL,3,NULL,NULL,NULL,'2024-02-06T18:15:01.000Z','filter_user_patients_by_nurse',NULL,NULL,'2024-02-06T17:47:35.000Z',NULL,'RLS Access','filter_user_patients_by_nurse','filter_user_patients_by_nurse',NULL,'id','patient','Yes',NULL,'SELECT DISTINCT
  ps.patient_id AS id
FROM
  form_schedule_event AS ps
  INNER JOIN form_user AS fu ON fu.id = ps.user_id
WHERE
  (ps.archived IS NULL OR ps.archived = FALSE)
  AND (ps.deleted IS NULL OR ps.deleted = FALSE)
  AND (
        ( ps.effective_start_date >= (CURRENT_DATE - INTERVAL ''60 days'') AND ps.effective_start_date <= (CURRENT_DATE + INTERVAL ''45 days''))
    )
  AND fu.role = ''nurse''
  AND fu.id = $1
  AND (fu.archived IS NULL OR fu.archived = FALSE)
  AND (fu.deleted IS NULL OR fu.deleted = FALSE)
  AND (ps.archived IS NULL OR ps.archived = FALSE)
  AND (ps.deleted IS NULL OR ps.deleted = FALSE)','No',NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (16,1,NULL,1,NULL,3,NULL,NULL,NULL,'2024-02-07T12:54:38.000Z','Restrict Access for all to patients',NULL,NULL,'2024-02-06T18:31:55.000Z',NULL,'RLS Global','Restrict Access for all to patients',NULL,NULL,'id','patient','Yes','-1111',NULL,NULL,NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (17,1,NULL,1,NULL,3,NULL,NULL,NULL,NULL,'filter user patients by supervisor_group_nurse',NULL,NULL,'2024-02-06T18:58:47.000Z',NULL,'RLS Access','filter user patients by supervisor_group_nurse','filter user patients by supervisor_group_nurse',NULL,'id','patient','Yes',NULL,'SELECT DISTINCT
  ps.patient_id AS id
FROM
  form_schedule_event AS ps
  INNER JOIN form_user AS fu ON fu.id = ps.user_id
  INNER JOIN gr_form_supervisor_group_team_members_to_user_id AS ge ON fu.id = ge.form_user_fk
  INNER JOIN form_supervisor_group AS sg ON ge.form_supervisor_group_fk = sg.id AND sg.supervisor = $1
WHERE
  (ps.archived IS NULL OR ps.archived = FALSE)
  AND (ps.deleted IS NULL OR ps.deleted = FALSE)
  AND (
        ( ps.effective_start_date >= (CURRENT_DATE - INTERVAL ''60 days'') AND ps.effective_start_date <= (CURRENT_DATE + INTERVAL ''45 days''))
    )
  AND fu.role = ''nurse''
  AND (fu.archived IS NULL OR fu.archived = FALSE)
  AND (fu.deleted IS NULL OR fu.deleted = FALSE)
  AND (sg.archived IS NULL OR sg.archived = FALSE)
  AND (sg.deleted IS NULL OR sg.deleted = FALSE)','No',NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
INSERT INTO form_sec_rule ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "type", "description", "submodule", "path", "rule_key", "table_name", "active", "default_value", "query_value", "cache_result", "rule_link", "allow_sync") VALUES (18,1,NULL,1,NULL,5,NULL,NULL,NULL,'2024-10-10T16:31:45.000Z','Access to Inventory Module',NULL,NULL,'2024-10-10T16:30:35.000Z',NULL,'Rule','Access to Inventory Module','Inventory Navbar','/inventory',NULL,NULL,'Yes',NULL,NULL,NULL,NULL,'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "type" = EXCLUDED."type", "description" = EXCLUDED."description", "submodule" = EXCLUDED."submodule", "path" = EXCLUDED."path", "rule_key" = EXCLUDED."rule_key", "table_name" = EXCLUDED."table_name", "default_value" = EXCLUDED."default_value", "query_value" = EXCLUDED."query_value", "cache_result" = EXCLUDED."cache_result", "rule_link" = EXCLUDED."rule_link" WHERE form_sec_rule.allow_sync = 'Yes';
COMMIT;
