BEGIN;
INSERT INTO form_list_nursing_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Infusion Related Issue',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Infusion Related Issue','No','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_nursing_issue.allow_sync = 'Yes';
INSERT INTO form_list_nursing_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Late Arrival',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Late Arrival','No','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_nursing_issue.allow_sync = 'Yes';
INSERT INTO form_list_nursing_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Nursing Personnel Issue',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Nursing Personnel Issue','No','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_nursing_issue.allow_sync = 'Yes';
INSERT INTO form_list_nursing_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Product Wastage',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Product Wastage','No','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_nursing_issue.allow_sync = 'Yes';
INSERT INTO form_list_nursing_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Vascular Access Device (VAD)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Vascular Access Device (VAD)','Yes','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_nursing_issue.allow_sync = 'Yes';
INSERT INTO form_list_nursing_issue ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "ae_only", "complaint_only", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Other',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Other','No','No','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "ae_only" = EXCLUDED."ae_only", "complaint_only" = EXCLUDED."complaint_only" WHERE form_list_nursing_issue.allow_sync = 'Yes';
COMMIT;
