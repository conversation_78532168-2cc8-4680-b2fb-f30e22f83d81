BEGIN;
INSERT INTO form_template_email ("id", "created_by", "change_by", "updated_by", "reviewed_by", "module", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "name", "code", "subject", "template_html", "template_data", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,10,NULL,NULL,NULL,'2024-09-04T21:33:44.000Z','Reset Password Email - reset_pass_email',NULL,NULL,'2024-09-04T19:56:33.000Z',NULL,'Reset Password Email','reset_pass_email','{{subject}}','<!DOCTYPE html><html><body><div style="background-color:#F5F5F5;color:#262626;font-family:&quot;Helvetica Neue&quot;, &quot;<PERSON>l Nova&quot;, &quot;Nimbus Sans&quot;, <PERSON><PERSON>, sans-serif;font-size:16px;font-weight:400;letter-spacing:0.15008px;line-height:1.5;margin:0;padding:32px 0;min-height:100%;width:100%"><table align="center" width="100%" style="margin:0 auto;max-width:600px;background-color:#FFFFFF" role="presentation" cellSpacing="0" cellPadding="0" border="0"><tbody><tr style="width:100%"><td><div style="font-size:16px;padding:16px 24px 16px 24px"><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            
        }
        .header {
            background-color: #9974ce;
            padding: 20px;
            text-align: center;
            color: #ffffff;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .content {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      
        }
        .content p {
            font-size: 16px;
            line-height: 1.5;
        }
        .button {
            display: inline-block;
            padding: 15px 25px;
            font-size: 16px;
            color: #ffffff;
            background-color: #9974ce;
            border-radius: 5px;
            text-decoration: none;
            margin: 20px 0;
            text-align: center;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Reset Your Password</h1>
        </div>
        <div class="content">
            <p>Hello,</p>
            <p>We received a request to reset your password. Please click the button below to choose a new password:</p>
            <center>
            <a style=''color:white;'' href="{{reset_link}}" class="button">Reset Password</a>
            </center>
            <p>If you did not request a password reset, please ignore this email or contact support if you have questions.</p>
            <p>Thank you,<br>Clara</p>
        </div>
        <div class="footer">
            <p>If you''re having trouble clicking the "Reset Password" button, copy and paste the URL below into your web browser:</p>
            <p><a href="{{reset_link}}">{{reset_link}}</a></p>
        </div>
    </div>
</body>
</html>
</div></td></tr></tbody></table></div></body></html>',E'{"root":{"type":"EmailLayout","data":{"backdropColor":"#F5F5F5","canvasColor":"#FFFFFF","textColor":"#262626","fontFamily":"MODERN_SANS","childrenIds":["block-1725479826119"]}},"block-1713199011299":{"type":"Text","data":{"style":{"fontWeight":"normal","padding":{"top":16,"bottom":16,"right":24,"left":24}},"props":{"text":""}}},"block-0000000000001":{"type":"Html","data":{"style":{"fontSize":11,"textAlign":"center","padding":{"top":0,"bottom":0,"right":24,"left":24}},"props":{"contents":"Powered By <a class=''footer'' href=\\"//envoylabs.net\\" target=\\"_blank\\">Envoy</a>"}}},"block-1725479826119":{"type":"Html","data":{"style":{"fontSize":16,"textAlign":null,"padding":{"top":16,"bottom":16,"right":24,"left":24}},"props":{"contents":"<!DOCTYPE html>\\n<html lang=\\"en\\">\\n<head>\\n    <meta charset=\\"UTF-8\\">\\n    <meta name=\\"viewport\\" content=\\"width=device-width, initial-scale=1.0\\">\\n    <title>Reset Your Password</title>\\n    <style>\\n        body {\\n            font-family: Arial, sans-serif;\\n            background-color: #f4f4f4;\\n            margin: 0;\\n            padding: 0;\\n            color: #333;\\n        }\\n        .container {\\n            width: 100%;\\n            max-width: 600px;\\n            margin: 0 auto;\\n            padding: 20px;\\n            \\n        }\\n        .header {\\n            background-color: #9974ce;\\n            padding: 20px;\\n            text-align: center;\\n            color: #ffffff;\\n        }\\n        .header h1 {\\n            margin: 0;\\n            font-size: 24px;\\n        }\\n        .content {\\n            background-color: #ffffff;\\n            padding: 30px;\\n            border-radius: 5px;\\n            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n      \\n        }\\n        .content p {\\n            font-size: 16px;\\n            line-height: 1.5;\\n        }\\n        .button {\\n            display: inline-block;\\n            padding: 15px 25px;\\n            font-size: 16px;\\n            color: #ffffff;\\n            background-color: #9974ce;\\n            border-radius: 5px;\\n            text-decoration: none;\\n            margin: 20px 0;\\n            text-align: center;\\n        }\\n        .footer {\\n            text-align: center;\\n            margin-top: 20px;\\n            font-size: 14px;\\n            color: #777;\\n        }\\n    </style>\\n</head>\\n<body>\\n    <div class=\\"container\\">\\n        <div class=\\"header\\">\\n            <h1>Reset Your Password</h1>\\n        </div>\\n        <div class=\\"content\\">\\n            <p>Hello,</p>\\n            <p>We received a request to reset your password. Please click the button below to choose a new password:</p>\\n            <center>\\n            <a style=''color:white;'' href=\\"{{reset_link}}\\" class=\\"button\\">Reset Password</a>\\n            </center>\\n            <p>If you did not request a password reset, please ignore this email or contact support if you have questions.</p>\\n            <p>Thank you,<br>Clara</p>\\n        </div>\\n        <div class=\\"footer\\">\\n            <p>If you''re having trouble clicking the \\"Reset Password\\" button, copy and paste the URL below into your web browser:</p>\\n            <p><a href=\\"{{reset_link}}\\">{{reset_link}}</a></p>\\n        </div>\\n    </div>\\n</body>\\n</html>\\n"}}}}','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "module" = EXCLUDED."module", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "code" = EXCLUDED."code", "subject" = EXCLUDED."subject", "template_html" = EXCLUDED."template_html", "template_data" = EXCLUDED."template_data" WHERE form_template_email.allow_sync = 'Yes';
COMMIT;
