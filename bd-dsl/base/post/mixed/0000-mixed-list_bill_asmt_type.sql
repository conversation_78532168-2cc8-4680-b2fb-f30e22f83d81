BEGIN;
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ABN - Advanced Beneficiary Notice',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'abnnotice','ABN - Advanced Beneficiary Notice',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:31.000Z','Account Referred to Collections',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'collections','Account Referred to Collections',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:41.000Z','Admissions Patient Contact',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'admissionscontact','Admissions Patient Contact',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:55.000Z','Bankruptcy',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'bankruptcy','Bankruptcy',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:52:04.000Z','Billing Note',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'billnote','Billing Note',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Billing Audit',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'billaudit','Billing Audit',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Cash Posting Notes',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'postingnote','Cash Posting Notes',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Claim Status',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'claimstatus','Claim Status',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Credit Card Payment',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ccpayment','Credit Card Payment',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Copay Assistance',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'copayassist','Copay Assistance',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Delivery Signature Opt-Out',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'deliverysigoptout','Delivery Signature Opt-Out',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Denied Referral',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'deniedreferral','Denied Referral',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Document Sent to Patient',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'docsenttopt','Document Sent to Patient',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Durable Power of Attorney',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'dpoa','Durable Power of Attorney',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','SWO',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'swo','SWO',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (16,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Financial Hardship',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'hardship','Financial Hardship',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (17,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','HTC Authorization to Ship Request',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'htcauthtoship','HTC Authorization to Ship Request',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (18,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','HTC Authorization to Ship Approval',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'htcapproveship','HTC Authorization to Ship Approval',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (19,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Information',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'info','Information',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (20,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','INITIAL INSURANCE ELIGIBILITY/BENEFITS NOTE',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'initialeligibilitynote','INITIAL INSURANCE ELIGIBILITY/BENEFITS NOTE',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (21,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Medicare B DME Qualification',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'partbdmequal','Medicare B DME Qualification',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (22,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Medicare IVIG Demonstration',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ivigdemo','Medicare IVIG Demonstration',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (23,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Medical Record Request',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'medrecrequest','Medical Record Request',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (24,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','MONTHLY INSURANCE ELIGIBILITY/BENEFITS NOTE',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'monthlyinsureligibility','MONTHLY INSURANCE ELIGIBILITY/BENEFITS NOTE',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (25,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','New Admission',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'newadmin','New Admission',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (26,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Nursing Visit',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'nursevisit','Nursing Visit',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (27,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','On-Call Contact',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'oncallcontact','On-Call Contact',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (28,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Order Change',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'orderchange','Order Change',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (29,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Overpayment',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'overpayment','Overpayment',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (30,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Introduction Call',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'introcall','Introduction Call',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (31,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Patient Assistance',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'patientassistance','Patient Assistance',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (32,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Patient Call',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'patientcall','Patient Call',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (33,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Patient Discharge',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'patientdc','Patient Discharge',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (34,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Patient Estimate of Cost',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ptestimatecost','Patient Estimate of Cost',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (35,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Patient Transfer',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pttransfer','Patient Transfer',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (36,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Payer Correspondence',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ptcorrespondence','Payer Correspondence',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (37,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Payor Audit',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'payeraudit','Payor Audit',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (38,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Payment Plan',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'paymentplan','Payment Plan',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (39,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Prior Authorization',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pa','Prior Authorization',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (40,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Prior Authorization Approval',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'paapprove','Prior Authorization Approval',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (41,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Prior Auth / Re-Auth / GAP Requested-Pending',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pareauthgappend','Prior Auth / Re-Auth / GAP Requested-Pending',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (42,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Prior Auth / Re-Auth / GAP Status Check (Still Pending)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pareauthgapcheck','Prior Auth / Re-Auth / GAP Status Check (Still Pending)',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (43,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Prior Auth / Re-Auth / GAP Cancelation',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pareauthgapcancel','Prior Auth / Re-Auth / GAP Cancelation',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (44,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Prior Auth / Re-Auth / GAP Approved',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pareauthgapapprove','Prior Auth / Re-Auth / GAP Approved',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (45,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Prior Auth / Re-Auth / GAP Denied',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pareauthgapapdeny','Prior Auth / Re-Auth / GAP Denied',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (46,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Patient Demo Change Request',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ptdemochangereq','Patient Demo Change Request',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (47,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Referral Contact',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'refcontact','Referral Contact',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (48,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Patient Triage',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'pttriage','Patient Triage',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (49,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Refund',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'refund','Refund',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (50,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','SNF/LTAC Contact',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'snfltaccontact','SNF/LTAC Contact',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (51,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Insurance Eligibility/Benefits Note',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'eligibilitynote','Insurance Eligibility/Benefits Note',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "note_template", "allow_sync", "active") VALUES (52,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Provider Communication',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'provcomm','Provider Communication',NULL,'Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "note_template" = EXCLUDED."note_template" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
COMMIT;
