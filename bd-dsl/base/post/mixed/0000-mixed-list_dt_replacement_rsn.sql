BEGIN;
INSERT INTO form_list_dt_replacement_rsn ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active", "sys_period") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-24T18:50:46.000Z','GNC - Generic',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'GNC','Generic','Yes',NULL,'["2024-09-05 03:53:30.114999+00",)') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_dt_replacement_rsn.allow_sync = 'Yes';
INSERT INTO form_list_dt_replacement_rsn ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "allow_sync", "active", "sys_period") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-24T18:50:46.000Z','SPLY - In-stock supply',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'SPLY','In-stock supply','Yes','Yes','["2024-09-24 18:50:46.354681+00",)') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_dt_replacement_rsn.allow_sync = 'Yes';
COMMIT;
