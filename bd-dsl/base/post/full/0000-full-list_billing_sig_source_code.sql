BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_billing_sig_source_code;
INSERT INTO form_list_billing_sig_source_code ("code","name","id","created_on","created_by","auto_name") VALUES ('B','Signed signature authorization form or forms for both HCFA-1500 Claim Form block 12 and block 13 are on file',1,'2024-03-19 22:39:32',1,'B - Signed signature authorization form or forms for both HCFA-1500 Claim Form block 12 and block 13 are on file')
,('C','Signed HCFA-1500 Claim Form on file',2,'2024-03-19 22:39:32',1,'C - Signed HCFA-1500 Claim Form on file')
,('M','Signed signature authorization form for HCFA-1500 Claim Form block 13 on file',3,'2024-03-19 22:39:32',1,'M - Signed signature authorization form for HCFA-1500 Claim Form block 13 on file')
,('P','Signature generated by provider because the patient was not physically present for services',4,'2024-03-19 22:39:32',1,'P - Signature generated by provider because the patient was not physically present for services')
,('S','Signed signature authorization form for HCFA-1500 Claim Form block 12 on file',5,'2024-03-19 22:39:32',1,'S - Signed signature authorization form for HCFA-1500 Claim Form block 12 on file')
;
ALTER SEQUENCE "form_list_billing_sig_source_code_id_seq" RESTART WITH 6;
SET session_replication_role = DEFAULT;
 COMMIT;
