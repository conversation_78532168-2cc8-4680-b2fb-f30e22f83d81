BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_ss_drug_coded_qualifier;
INSERT INTO form_list_ss_drug_coded_qualifier ("id","created_on","created_by","auto_name","name","code") VALUES (1,'4/16/2024, 06:46:40',1,'National Drug Code (NDC)','National Drug Code (NDC)','ND'),
(2,'4/16/2024, 06:46:40',1,'Universal Product Code (UPC)','Universal Product Code (UPC)','UP'),
(3,'4/16/2024, 06:46:40',1,'National Drug File Reference (NDF-RT)','National Drug File Reference (NDF-RT)','RT'),
(4,'4/16/2024, 06:46:40',1,'Health Related Item (HRI)','Health Related Item (HRI)','NH'),
(5,'4/16/2024, 06:46:40',1,'Unique Ingredient Identifier (UNII)','Unique Ingredient Identifier (UNII)','UN'),
(6,'4/16/2024, 06:46:40',1,'RxNorm Semantic Clinical Drug (SCD)','RxNorm Semantic Clinical Drug (SCD)','SCD'),
(7,'4/16/2024, 06:46:40',1,'RxNorm Semantic Branded Drug (SBD)','RxNorm Semantic Branded Drug (SBD)','SBD'),
(8,'4/16/2024, 06:46:40',1,'RxNorm Generic Package (GPCK)','RxNorm Generic Package (GPCK)','GPK'),
(9,'4/16/2024, 06:46:40',1,'RxNorm Branded Package (BPCK)','RxNorm Branded Package (BPCK)','BPK'),
(10,'4/16/2024, 06:46:40',1,'Gold Standard Marketed Product Identifier (MPid)','Gold Standard Marketed Product Identifier (MPid)','GMP'),
(11,'4/16/2024, 06:46:40',1,'Gold Standard Product Identifier (ProdID)','Gold Standard Product Identifier (ProdID)','GPI'),
(12,'4/16/2024, 06:46:40',1,'Gold Standard Specific Product Identifier (SPID)','Gold Standard Specific Product Identifier (SPID)','GSP'),
(13,'4/16/2024, 06:46:40',1,'Device Identifier','Device Identifier','DI')
;
ALTER SEQUENCE "form_list_ss_drug_coded_qualifier_id_seq" RESTART WITH 14;
SET session_replication_role = DEFAULT;
COMMIT;